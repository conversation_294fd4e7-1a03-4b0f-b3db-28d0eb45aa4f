package com.kaolafm.kradio.flavor.impl;

import com.kaolafm.sdk.core.mediaplayer.OnHandleAudioFocusListener;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-07-12 11:53
 ******************************************/
public final class OnHandleAudioFocusImpl implements OnHandleAudioFocusListener {
    @Override
    public boolean onAudioFocusDuck(Object... objects) {
        return true;
    }

    @Override
    public boolean onAudioFocusGain(Object... objects) {
        return false;
    }

    @Override
    public boolean onAudioFocusLoss(Object... objects) {
        return false;
    }
}
