<?xml version="1.0" encoding="utf-8"?>
<com.kaolafm.kradio.lib.widget.ratio.RatioConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:background="@drawable/component_card_logo_bg"
    tools:background="@drawable/component_card_logo_bg"
    app:canScale="false"
    app:wh_ratio="0.861:1">

    <com.kaolafm.kradio.component.ui.base.view.UnInterceptDownChildRecyclerview
        android:id="@+id/card_view_page"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/indicator_rv"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/m10"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="@dimen/m20"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

</com.kaolafm.kradio.lib.widget.ratio.RatioConstraintLayout>