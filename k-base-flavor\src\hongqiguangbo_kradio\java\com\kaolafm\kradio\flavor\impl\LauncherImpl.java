package com.kaolafm.kradio.flavor.impl;

import android.text.TextUtils;
import android.util.Log;

import com.alibaba.fastjson.JSON;
import com.kaolafm.base.utils.SpUtil;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.SharedPreferenceUtil;
import com.kaolafm.kradio.player.bean.BroadcastRadioSimpleData;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;

import java.util.ArrayList;
import java.util.List;

public class LauncherImpl implements LauncherInter {
    public static String TAG = "LauncherImpl";

    public static final String BROADCAST_LIST = "BROADCAST_LIST";

    @Override
    public void onStart(Object... args) {
    }

    @Override
    public void onCreate(Object... args) {
        try {
            SpUtil.init(AppDelegate.getInstance().getContext());
            String json = SpUtil.getString(BROADCAST_LIST, "");
            if(TextUtils.isEmpty(json)) {
                Log.i(TAG,  "onCreate：empty");
                return;
            }
            SpUtil.putString(BROADCAST_LIST, "");
            Log.i(TAG,  "onCreate：" + json);
            ArrayList<BroadcastRadioSimpleData> arrayList = (ArrayList<BroadcastRadioSimpleData>) JSON.parseArray(json, BroadcastRadioSimpleData.class);
            Log.i(TAG,  "onCreate：" + arrayList.toString());
            PlayerManagerHelper.getInstance().addBroadcastRadioSimpleItems(arrayList);
        } catch (Exception e) {
            Log.i(TAG,  "onCreate"+e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public void onResume(Object... args) {

    }

    @Override
    public void onPause(Object... args) {

    }

    @Override
    public void onStop(Object... args) {
        ArrayList<BroadcastRadioSimpleData> arrayList = PlayerManagerHelper.getInstance().getBroadcastRadioSimpleItems();
        SpUtil.init(AppDelegate.getInstance().getContext());
        try {
            if (arrayList != null && arrayList.size() > 0) {
                String json = JSON.toJSONString(arrayList);
                Log.i(TAG,  "onStop：" + json);
                SpUtil.putString(BROADCAST_LIST, json);
            }
        } catch (Exception e) {
            Log.i(TAG,  "onStop：" + e.toString());
            e.printStackTrace();
        }
    }

    @Override
    public void onRestart(Object... args) {

    }

    @Override
    public void onDestory(Object... args) {

    }
}
