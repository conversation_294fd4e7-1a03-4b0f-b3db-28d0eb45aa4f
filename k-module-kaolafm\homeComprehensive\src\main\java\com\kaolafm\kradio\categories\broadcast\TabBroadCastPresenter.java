package com.kaolafm.kradio.categories.broadcast;

import android.util.Log;

import com.kaolafm.kradio.category.ErrorCode;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.kradio.lib.base.ui.BaseShowHideFragment;
import com.kaolafm.kradio.lib.bean.BroadcastTabContainerData;
import com.kaolafm.kradio.lib.bean.BroadcastTabData;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.opensdk.api.operation.model.category.Category;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.trello.rxlifecycle3.android.FragmentEvent;

import java.util.ArrayList;
import java.util.List;

public class TabBroadCastPresenter extends BasePresenter<TabBroadcastModel, IBroadcastTabView> {

    private static final String TAG = "TabBroadCastPresenter_" + BroadcastTabListFragment.TAG;

    public TabBroadCastPresenter(IBroadcastTabView view) {
        super(view);
    }

    @Override
    protected TabBroadcastModel createModel() {
        return new TabBroadcastModel();
    }

    public void requestData(int categoryId) {
        Log.d(TAG, "requestData() - mModel.requestBroadcastData()");
        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
            if (mView != null) {
                mView.showError(new ApiException(ErrorCode.NO_NET, "数据为空"));
            }
            return;
        }
        mModel.requestBroadcastData(categoryId, ((BaseShowHideFragment) mView).bindUntilEvent(FragmentEvent.DESTROY_VIEW), new HttpCallback<List<Category>>() {
            @Override
            public void onSuccess(List<Category> categories) {
                Log.d(TAG, "requestData() - mModel.requestBroadcastData() - success categories: " + categories);
                BroadcastTabContainerData containerData = parseApiResData(categories);
                if (mView != null) {
                    mView.showTab(containerData.tabsList);
                    mView.showTabContent(containerData.fragments);
                }
            }

            @Override
            public void onError(ApiException e) {
                Log.e(TAG, "requestData() - mModel.requestBroadcastData() - error:" + e.toString());
                if (mView != null) {
                    mView.showError(e);
                }
//                // 本地生成假数据以供调试
//                if (mView != null) {
//                    BroadcastTabContainerData containerData = mModel.generateTestData();
//                    mView.showTab(containerData.tabsList);
//                    mView.showTabContent(containerData.fragments);
//                }
            }
        });
    }

    private BroadcastTabContainerData parseApiResData(List<Category> categories) {
        Log.d(TAG, "parseApiResData() categories.size:" + (categories == null ? 0 : categories.size()) + ", categories:" + categories);
        BroadcastTabContainerData containerData = new BroadcastTabContainerData();
        containerData.tabsList = new ArrayList<>();
        containerData.fragments = new ArrayList<>();
        if (categories == null || categories.isEmpty()) {
            Log.d(TAG, "parseApiResData() categories.isEmpty -> containerData:" + containerData);
            return containerData;
        }
        for (int i = 0; i < categories.size(); i++) {
            Category categoryData = categories.get(i);
            BroadcastTabData tab = new BroadcastTabData();
            tab.code = categoryData.getCode();
            tab.name = categoryData.getName();
            tab.isSelected = i == 0;
            int categoryId;
            try {
                categoryId = Integer.parseInt(tab.code);
            } catch (Exception e) {
                Log.d(TAG, "parseApiResData() error e: " + e.toString());
                categoryId = 0;
            }
            containerData.tabsList.add(tab);
            containerData.fragments.add(BroadcastTabContentFragment.newInstance(categoryId, tab.name));
        }
        Log.d(TAG, "parseApiResData() categories.notEmpty -> containerData:" + containerData);
        return containerData;
    }


//    HashMap<Integer, Integer> mTabToHeadPos = new HashMap<>();
//
//    @RequiresApi(api = Build.VERSION_CODES.N)
//    private void transformData(@NonNull List<BroadcastTabData> tabList, @NonNull List<BroadcastTabContentData> contentList, HashMap<BroadcastTabData, List<BroadcastTabContentData>> map) {
//        tabList.addAll(map.keySet());
//        tabList.sort((o1, o2) -> o1.id - o2.id);
//        int count = 0;
//        for (int i = 0; i < tabList.size(); i++) {
//            BroadcastTabData tab = tabList.get(i);
//            mTabToHeadPos.put(tab.id, count);
//            count++;
//            BroadcastTabContentData content = new BroadcastTabContentData();
//            content.setName(tab.name);
//            content.setType(1);
//            contentList.add(content);
//            contentList.addAll(Objects.requireNonNull(map.get(tab)));
//            if (map.get(tab) != null) {
//                count += Objects.requireNonNull(map.get(tab)).size();
//            }
//        }
//        // 记录header的position
//    }
//
//    public HashMap<Integer, Integer> getMap() {
//        return mTabToHeadPos;
//    }
}
