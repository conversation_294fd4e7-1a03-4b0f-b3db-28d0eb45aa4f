package com.ecarx.sdk;

import android.util.Log;

import com.google.gson.Gson;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;

/**
 * <AUTHOR>
 **/
public class ECarXSdk {

    private static final String TAG = "novelot.ecarx";

    public static final String APP_ID = "58946fa69154a14398e63334b5d91bc3";
    public static final String PACKAGE_NAME = "com.kaolafm.kradio.k_radio_horizontal";
    //正式版
    public static final String SHA1 = "21:F1:E4:C2:CA:61:56:22:3D:DA:87:F2:B3:8D:80:A9:C6:BF:E7:B4";
    //调试版
    public static final String SHA1_DEBUG = "D8:7C:EA:52:6A:70:2A:39:EC:A9:97:AF:C1:1C:DC:98:95:FA:E5:1F";

    private OkHttpClient mClient;
    private Gson mGson;

    private ECarXSdk() {
        mClient = new OkHttpClient();
        mGson = new Gson();
    }

    private volatile static ECarXSdk sInstance;

    public static ECarXSdk getInstance() {
        if (sInstance == null) {
            synchronized (ECarXSdk.class) {
                if (sInstance == null) {
                    sInstance = new ECarXSdk();
                }
            }
        }
        return sInstance;
    }

//    private String getAppId() {
//        String appId = APP_ID;
//        try {
//            Application context = AppDelegate.getInstance().getContext();
//            ApplicationInfo applicationInfo = context.getPackageManager().getApplicationInfo(getPackageName(), PackageManager.GET_META_DATA);
//            Bundle metaData = applicationInfo.metaData;
//            appId = metaData.getString("eCarX_OpenAPI_AppId");
//        } catch (PackageManager.NameNotFoundException e) {
//            e.printStackTrace();
//        }
//        return appId;
//    }
//
//
//    private String getPackageName() {
//        Application context = AppDelegate.getInstance().getContext();
//        String packageName = context.getPackageName();
//        return packageName;
//    }

//    private String getSha1() {
//        return SHA1;
//    }

    /**
     * 亿咖通 拿到code后,通过code请求accessToken;
     *
     * @param code
     */
    public void getAccessToken(String appId, String packageName, String sha1, String code, final HttpCallback<AccessTokenResult> callback) {

        String url = String.format("http://api.xchanger.cn/auth/oauth2/access_token/%s", appId);

        Log.i(TAG, "getAccessToken: url=" + url);

        //packageName	Body参数	是	平台入驻时填写的包名
        //checkCode
//        RequestBody requestBody = new FormBody.Builder()
//                .add("packageName", packageName)
//                .add("checkCode", sha1)
//                .build();


        Map<String, String> map = new HashMap<>();
        map.put("packageName", packageName);
        map.put("checkCode", sha1);
        Gson gson = new Gson();
        String json = gson.toJson(map);
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), json);

        //AUTHORIZATION	请求头	是	前面获取的临时code
        Request q = new Request.Builder()
                .header("AUTHORIZATION", code)
                .url(url)
                .post(requestBody)
                .build();

        Call call = mClient.newCall(q);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.i(TAG, "getAccessToken onFailure: error=" + e);
                if (callback != null) {
                    callback.onError(new ApiException(e));
                }
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                Log.i(TAG, "getAccessToken onResponse: response=" + response);
                int responseCode = response.code();
                if (responseCode >= 200 && responseCode < 300) {
                    ResponseBody body = response.body();
                    if (body != null) {
                        String json = response.body().string();
                        body.close();
                        AccessTokenResult tokenResult = mGson.fromJson(json, AccessTokenResult.class);
                        if (tokenResult != null) {
                            //保存旧的token
                            TokenManager.getInstance().setToken(tokenResult);
                            if (callback != null) {
                                callback.onSuccess(tokenResult);
                            }
                        } else {
                            if (callback != null) {
                                callback.onError(new ApiException("Json解析时转换成了NULL."));
                            }
                        }
                    } else {
                        if (callback != null) {
                            callback.onError(new ApiException("ResponseBody is NULL."));
                        }
                    }
                } else {
                    if (callback != null) {
                        callback.onError(new ApiException("ResponseBody is NULL."));
                    }
                }
            }
        });
    }


    /**
     * 刷新token
     *
     * @param refreshToken
     * @param callback
     */
    public void refreshToken(String appId, String packageName, String sha1, String refreshToken, final HttpCallback<AccessTokenResult> callback) {
        //grantType	Body参数	是	填写为refresh_token
        //refreshToken	Body参数	是	获取accessToken时的refreshToken参数
        //packageName	Body参数	是	平台入驻时填写的包名
        //checkCode	Body参数	是	平台入驻时填写的sha1值
        //api.xchanger.cn/auth/oauth2/refresh_token/{appId}¬

        String url = String.format("http://api.xchanger.cn/auth/oauth2/refresh_token/%s", appId);

        Log.i(TAG, "refreshToken: url=" + url);

        OkHttpClient client = new OkHttpClient();
//        RequestBody requestBody = new FormBody.Builder()
//                .add("grantType", "refresh_token")
//                .add("refreshToken", refreshToken)
//                .add("packageName", packageName)
//                .add("checkCode", sha1)
//                .build();


        Map<String, String> map = new HashMap<>();
        map.put("grantType", "refresh_token");
        map.put("refreshToken", refreshToken);
        map.put("packageName", packageName);
        map.put("checkCode", sha1);
        Gson gson = new Gson();
        String json = gson.toJson(map);
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), json);

        Request q = new Request.Builder()
                .url(url)
                .put(requestBody)
                .build();

        Call call = client.newCall(q);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.i(TAG, "refreshToken onFailure: error=" + e);
                if (callback != null) {
                    callback.onError(new ApiException(e));
                }
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                Log.i(TAG, "refreshToken onResponse: response=" + response);
                int responseCode = response.code();
                if (responseCode >= 200 && responseCode < 300) {
                    ResponseBody body = response.body();
                    if (body != null) {
                        String json = response.body().string();
                        body.close();
                        AccessTokenResult tokenResult = mGson.fromJson(json, AccessTokenResult.class);
                        if (tokenResult != null) {
                            //保存旧的token
                            TokenManager.getInstance().setToken(tokenResult);
                            if (callback != null) {
                                callback.onSuccess(tokenResult);
                            }
                        } else {
                            if (callback != null) {
                                callback.onError(new ApiException("Json解析时转换成了NULL."));
                            }
                        }
                    } else {
                        if (callback != null) {
                            callback.onError(new ApiException("ResponseBody is NULL."));
                        }
                    }
                } else {
                    if (callback != null) {
                        callback.onError(new ApiException("ResponseBody is NULL."));
                    }
                }
            }
        });
    }

    /**
     * 获取用户数据
     *
     * @param accessToken
     * @param callback
     */
    public void getUserInfo(String openId, String accessToken, final HttpCallback<UserInfo> callback) {
        String url = String.format("http://api.xchanger.cn/open/userinfo/%s", openId);

        Log.i(TAG, "getUserInfo: url=" + url + "--token = " + accessToken);
        Request q = new Request.Builder()
                .header("AUTHORIZATION", accessToken)
                .url(url)
                .get()
                .build();

        Call call = mClient.newCall(q);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.i(TAG, "getUserInfo onFailure: error=" + e);
                if (callback != null) {
                    callback.onError(new ApiException(e));
                }
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                Log.i(TAG, "getUserInfo onResponse: response=" + response);
                int responseCode = response.code();
                if (responseCode >= 200 && responseCode < 300) {
                    ResponseBody body = response.body();
                    if (body != null) {
                        String json = response.body().string();
                        body.close();
                        UserInfo rst = mGson.fromJson(json, UserInfo.class);
                        if (rst != null) {
                            if (callback != null) {
                                callback.onSuccess(rst);
                            }
                        } else {
                            if (callback != null) {
                                callback.onError(new ApiException("Json解析时转换成了NULL."));
                            }
                        }
                    } else {
                        if (callback != null) {
                            callback.onError(new ApiException("ResponseBody is NULL."));
                        }
                    }
                } else {
                    if (callback != null) {
                        callback.onError(new ApiException("ResponseBody is NULL."));
                    }
                }
            }
        });
    }

}
