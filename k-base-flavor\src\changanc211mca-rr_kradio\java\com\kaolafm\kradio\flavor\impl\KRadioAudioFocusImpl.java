package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.media.AudioAttributes;
import android.media.AudioFocusRequest;
import android.media.AudioManager;
import android.os.Build;
import android.os.Handler;
import androidx.annotation.RequiresApi;


import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioFocusInter;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-06-21 10:54
 ******************************************/
public final class KRadioAudioFocusImpl implements KRadioAudioFocusInter {

    private AudioAttributes mAudioAttributes;

    private AudioFocusRequest mAudioFocusRequest;

    private Handler mHandler;

    private AudioManager mAudioManager;

    @RequiresApi(api = Build.VERSION_CODES.O)
    @Override
    public boolean requestAudioFocus(Object... args) {
        AudioManager.OnAudioFocusChangeListener onAudioFocusChangeListener = (AudioManager.OnAudioFocusChangeListener) args[0];
        if (mHandler == null) {
            mHandler = new Handler();
            mAudioManager = (AudioManager) AppDelegate.getInstance().getContext().getSystemService(Context.AUDIO_SERVICE);
            mAudioAttributes = new AudioAttributes.Builder()
                    .setUsage(AudioAttributes.USAGE_MEDIA)
                    .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                    .build();
            mAudioFocusRequest = new AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN)
                    .setAudioAttributes(mAudioAttributes)
                    .setAcceptsDelayedFocusGain(true)
                    .setOnAudioFocusChangeListener(onAudioFocusChangeListener, new Handler())
                    .build();
        }
        int rect = mAudioManager.requestAudioFocus(mAudioFocusRequest);
        return rect == AudioManager.AUDIOFOCUS_REQUEST_GRANTED;
    }

    @Override
    public boolean abandonAudioFocus(Object... args) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            mAudioManager.abandonAudioFocusRequest(mAudioFocusRequest);
        }
        PlayerManagerHelper.getInstance().play(false);
        return true;
    }
}
