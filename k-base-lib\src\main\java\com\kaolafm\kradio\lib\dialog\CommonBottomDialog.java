package com.kaolafm.kradio.lib.dialog;

import android.content.Context;
import androidx.annotation.StringRes;
import android.view.View;
import android.widget.TextView;

import com.kaolafm.kradio.lib.R;
import com.kaolafm.kradio.lib.dialog.DialogListener.OnNativeListener;
import com.kaolafm.kradio.lib.dialog.DialogListener.OnPositiveListener;
import com.kaolafm.kradio.lib.utils.AntiShake;


/**
 * <AUTHOR>
 * @date 2018/5/13
 */

public class CommonBottomDialog extends BaseBottomDialog {

    TextView mTvDialogBottomCancel;

    TextView mTvDialogBottomDefine;

    TextView mTvDialogBottomMessage;

    private OnNativeListener<CommonBottomDialog> mNativeListener;

    private OnPositiveListener<CommonBottomDialog> mPositiveListener;

    public CommonBottomDialog(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.dialog_common_bottom;
    }
    @Override
    public void initView(View view)
    {
        mTvDialogBottomCancel =view.findViewById(R.id.tv_dialog_bottom_cancel);
        mTvDialogBottomDefine =view.findViewById(R.id.tv_dialog_bottom_define);
        mTvDialogBottomMessage=view.findViewById(R.id.tv_dialog_bottom_message);

        mTvDialogBottomCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onViewClicked(v);
            }
        });
        mTvDialogBottomDefine.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onViewClicked(v);
            }
        });
    }
    public void onViewClicked(View view) {
        int id = view.getId();
        if (!AntiShake.check(id)) {
            if (id == R.id.tv_dialog_bottom_cancel) {
                if (mNativeListener != null) {
                    mNativeListener.onClick(this);
                } else {
                    dismiss();
                }
            } else if (id == R.id.tv_dialog_bottom_define) {
                if (mPositiveListener != null) {
                    mPositiveListener.onClick(this);
                }
            }
        }
    }

    public CommonBottomDialog setMessage(String message) {
        mTvDialogBottomMessage.setText(message);
        return this;
    }

    public CommonBottomDialog setMessage(@StringRes int resId) {
        mTvDialogBottomMessage.setText(resId);
        return this;
    }

    public CommonBottomDialog setLeftButton(CharSequence text) {
        mTvDialogBottomCancel.setText(text);
        return this;
    }

    public CommonBottomDialog setLeftButton(@StringRes int resId) {
        mTvDialogBottomCancel.setText(resId);
        return this;
    }

    public CommonBottomDialog setRightButton(CharSequence text) {
        mTvDialogBottomDefine.setText(text);
        return this;
    }

    public CommonBottomDialog setRightButton(@StringRes int resId) {
        mTvDialogBottomDefine.setText(resId);
        return this;
    }

    public CommonBottomDialog setOnNativeListener(OnNativeListener<CommonBottomDialog> nativeListener) {
        mNativeListener = nativeListener;
        return this;
    }

    public CommonBottomDialog setOnPositiveListener(OnPositiveListener<CommonBottomDialog> positiveListener) {
        mPositiveListener = positiveListener;
        return this;
    }
}
