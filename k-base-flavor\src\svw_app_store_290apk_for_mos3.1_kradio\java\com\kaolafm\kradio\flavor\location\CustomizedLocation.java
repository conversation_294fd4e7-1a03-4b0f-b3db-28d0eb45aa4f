package com.kaolafm.kradio.flavor.location;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Bundle;
import android.os.RemoteException;

import com.cns.android.location.Location;
import com.cns.android.location.POIManager;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.location.model.IKaoLaLocation;
import com.kaolafm.kradio.lib.location.model.KaoLaLocationListener;
import com.kaolafm.kradio.lib.location.model.LocationModel;
import com.kaolafm.opensdk.log.Logging;

import java.math.BigDecimal;
import com.kaolafm.kradio.flavor.impl.DebugImpl;

/**
 * 大众自定义位置信息获取
 */

public class CustomizedLocation implements IKaoLaLocation {

    private static final String TAG = "CustomizedLocation";

    private POIManager poiManager;

    private KaoLaLocationListener mLaLocationListener;

    private LocationModel mLocation = new LocationModel();

    public CustomizedLocation() {
        init();
    }

    private void notifyLocation() {
        mLaLocationListener.locationChange(mLocation);
    }

    private void init() {
        mLaLocationListener = new KaoLaLocationListener();
        start(AppDelegate.getInstance().getContext());
    }

    @Override
    public LocationModel getLocation() {
        return null;
    }

    @Override
    public void addLocationListener(IKaoLaLocationListener listener) {
        mLaLocationListener.addLocationListener(listener);
    }

    @Override
    public void removeLocationListener(IKaoLaLocationListener listener) {
        mLaLocationListener.removeLocationListener(listener);
    }

    POIManager.CurrentLocationChangeListener currentLocationChangeListener;

    @SuppressLint("MissingPermission")
    public void start(Context context) {
        if (DebugImpl.isDebug()){
            return;
        }

        try {
            poiManager = POIManager.getInstance(context);
//            if (!poiManager.isGpsUAuthorized()) {
//                //flase 不允许上传gps信息
//                return;
//            }
            currentLocationChangeListener = new POIManager.CurrentLocationChangeListener() {
                @Override
                public void onCurrentLocationChange(Bundle bundle) {
                    try {
                        Location location = poiManager.getVehicleLocation();
                        notifyLocation(location);
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }
            };
            poiManager.registerListenCurrentLocation(currentLocationChangeListener);

            Location location = poiManager.getVehicleLocation();
            if (null == location) {
                return;
            }

            notifyLocation(location);
        } catch (RemoteException e) {
            e.printStackTrace();
        }

    }

    private void notifyLocation(Location location) {
        Logging.i(TAG, "customized location = " + location);
        if (location == null) {
            return;
        }
        mLocation.setLongitude(convertScienceNumToNormal(location.getLongitude()));
        mLocation.setLatitude(convertScienceNumToNormal(location.getLatitude()));
        double d1 = convertScienceNumToNormal(location.getLatitude());
        double d2 = convertScienceNumToNormal(1.164074170517881E9);
        notifyLocation();
    }

    @Override
    public void destroy() {

    }

    private double convertScienceNumToNormal(Double sciNUm){
        double normal = sciNUm/10000000.f;
        return normal;
    }
}
