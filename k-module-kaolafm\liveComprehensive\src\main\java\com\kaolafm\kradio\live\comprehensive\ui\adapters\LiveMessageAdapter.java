package com.kaolafm.kradio.live.comprehensive.ui.adapters;

import android.graphics.Color;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.opensdk.api.live.model.MessageBean;
import com.kaolafm.opensdk.api.live.model.LiveRoomMessage;
import com.kaolafm.kradio.live.player.NimManager;

import java.util.List;

public class LiveMessageAdapter extends BaseAdapter<LiveRoomMessage> {
    private static final String TAG = "LiveMessageAdapter";
    private String myselfNickName;

//    private static final int ITEM_ENTER_ROOM_MESSAGE = 0;
//    private static final int ITEM_SEND_MESSAGE = 1;
//    private static final int ITEM_RECEIVE_MESSAGE = 2;
//    private static final int ITEM_SYSTEM_MESSAGE = 3;
//    private static final int ITEM_THEME_MESSAGE = 4;

    private static final int MESSAGE_ENTER_ROOM_TYPE = 1;
    private static final int MESSAGE_NORMAL_TYPE     = 2;
    private static final int MESSAGE_SYSTEM_TYPE     = 3;
    private static final int MESSAGE_THEME_TYPE      = 4;
    private static final int MESSAGE_SEND_TYPE       = 5;
    private static final int MESSAGE_RECEIVE_TYPE    = 6;


    public LiveMessageAdapter(List<LiveRoomMessage> dataList) {
        super(dataList);
        myselfNickName = NimManager.getInstance().getNickName();
        Log.d(TAG, "myselfNickName, myselfNickName is " + myselfNickName);
    }

    @Override
    public int getItemViewType(int position) {
        LiveRoomMessage itemData = getItemData(position);
        if (itemData.getMessageType() == LiveRoomMessage.MessageType.MESSAGE_NORMAL) {
            return MESSAGE_NORMAL_TYPE;
        } else if (itemData.getMessageType() == LiveRoomMessage.MessageType.MESSAGE_SYSTEM) {
            return MESSAGE_SYSTEM_TYPE;
        } else if (itemData.getMessageType() == LiveRoomMessage.MessageType.MESSAGE_THEME) {
            return MESSAGE_THEME_TYPE;
        } else if (itemData.getMessageType() == LiveRoomMessage.MessageType.MESSAGE_SEND) {
            return MESSAGE_SEND_TYPE;
        } else if (itemData.getMessageType() == LiveRoomMessage.MessageType.MESSAGE_RECEIVE) {
            return MESSAGE_RECEIVE_TYPE;
        } else if (itemData.getMessageType() == LiveRoomMessage.MessageType.MESSAGE_ENTER_ROOM) {
            return MESSAGE_ENTER_ROOM_TYPE;
        } else {
            return MESSAGE_NORMAL_TYPE;
        }
    }

    @Override
    protected BaseHolder<LiveRoomMessage> getViewHolder(ViewGroup parent, int viewType) {
        switch (viewType) {

            case MESSAGE_NORMAL_TYPE:
            case MESSAGE_SYSTEM_TYPE:
                return new NormalMessageHolder(inflate(parent, R.layout.comprehensive_live_message_item_normal, viewType));
            case MESSAGE_THEME_TYPE:
                return new ThemeMessageHolder(inflate(parent, R.layout.comprehensive_live_message_item_theme, viewType));
            case MESSAGE_SEND_TYPE:
            case MESSAGE_RECEIVE_TYPE:
            case MESSAGE_ENTER_ROOM_TYPE:
                return new ReceivedMessageHolder(inflate(parent, R.layout.comprehensive_live_message_item_normal, viewType));
            default:
                return new NormalMessageHolder(inflate(parent, R.layout.comprehensive_live_message_item_normal, viewType));
        }
    }

    private class NormalMessageHolder extends BaseHolder<LiveRoomMessage> {

        private TextView contentTv;

        public NormalMessageHolder(View itemView) {
            super(itemView);
            contentTv = itemView.findViewById(R.id.contentTv);
            itemView.setSelected(false);
        }

        @Override
        public void setupData(LiveRoomMessage messageBean, int position) {
            if (messageBean.getMessage() != null) {
                if (messageBean.getMessageType() == LiveRoomMessage.MessageType.MESSAGE_NORMAL) {
                    contentTv.setTextColor(ResUtil.getColor(R.color.comprehensive_live_message_normal_color));
                } else if (messageBean.getMessageType() == LiveRoomMessage.MessageType.MESSAGE_SYSTEM) {
                    contentTv.setTextColor(ResUtil.getColor(R.color.comprehensive_live_message_system_color));
                }
                contentTv.setText(messageBean.getMessage().contentString);
            }
        }
    }

    private class ReceivedMessageHolder extends BaseHolder<LiveRoomMessage> {

        private TextView contentTv;

        public ReceivedMessageHolder(View itemView) {
            super(itemView);
            contentTv = itemView.findViewById(R.id.contentTv);
            itemView.setSelected(false);
        }

        @Override
        public void setupData(LiveRoomMessage messageBean, int position) {
            // Log.i("111222333444", "11");
            String message = null;
            String hello = "欢迎  ";
            String nickName = null;

            if (messageBean.getUserInfo() != null) {
                nickName = messageBean.getUserInfo().getNickName();
                Log.d(TAG, "nickName, messageBean.getUserInfo().getNickName() is " + nickName);
            } else if (messageBean.getMessage() != null) {
                nickName = messageBean.getMessage().nickName;
                Log.d(TAG, "nickName, messageBean.getMessage().nickName is " + nickName);
            }

            if (nickName == null){
                // 解决 nickName 为null 时 nickName.length() nullPointer
                nickName = "";
            }

            boolean highlight = nickName != null && nickName.equals(myselfNickName);


            SpannableStringBuilder builder = null;
            ForegroundColorSpan helloSpan;
            ForegroundColorSpan nickSpan;
            ForegroundColorSpan contentSpan;
            if (messageBean.getMessageType() == LiveRoomMessage.MessageType.MESSAGE_ENTER_ROOM){
                helloSpan = new ForegroundColorSpan(itemView.getContext().getResources().getColor(R.color.comprehensive_live_message_welcome_text_color));
                nickSpan = new ForegroundColorSpan(itemView.getContext().getResources().getColor(R.color.comprehensive_live_message_welcome_name_color));
                contentSpan = new ForegroundColorSpan(itemView.getContext().getResources().getColor(R.color.comprehensive_live_message_welcome_text_color));
            } else {
                helloSpan = new ForegroundColorSpan(itemView.getContext().getResources().getColor(R.color.comprehensive_live_message_normal_color));
                nickSpan = new ForegroundColorSpan(itemView.getContext().getResources().getColor(R.color.comprehensive_live_message_system_color));
                contentSpan = new ForegroundColorSpan(itemView.getContext().getResources().getColor(R.color.comprehensive_live_message_normal_color));
            }

            if (messageBean.getMessageType() == LiveRoomMessage.MessageType.MESSAGE_SEND) {
                message = nickName + " 发送了新语音消息";
                builder = new SpannableStringBuilder(message);
                builder.setSpan(nickSpan, 0, nickName.length(), Spannable.SPAN_INCLUSIVE_INCLUSIVE);
                builder.setSpan(contentSpan, nickName.length(), message.length(), Spannable.SPAN_INCLUSIVE_INCLUSIVE);
            }else if (messageBean.getMessageType() == LiveRoomMessage.MessageType.MESSAGE_RECEIVE) {
                message = nickName + " "+messageBean.getMessage().contentString;
                builder = new SpannableStringBuilder(message);
                builder.setSpan(nickSpan, 0, nickName.length(), Spannable.SPAN_INCLUSIVE_INCLUSIVE);
                builder.setSpan(contentSpan, nickName.length(), message.length(), Spannable.SPAN_INCLUSIVE_INCLUSIVE);

            } else if (messageBean.getMessageType() == LiveRoomMessage.MessageType.MESSAGE_ENTER_ROOM){
                message = hello + nickName + "  进入直播间";

                String helloNick = hello + nickName;
                builder = new SpannableStringBuilder(message);
                builder.setSpan(helloSpan, 0, hello.length(), Spannable.SPAN_INCLUSIVE_INCLUSIVE);
                builder.setSpan(nickSpan, hello.length(), helloNick.length(), Spannable.SPAN_INCLUSIVE_INCLUSIVE);
                builder.setSpan(contentSpan, helloNick.length(), message.length(), Spannable.SPAN_INCLUSIVE_INCLUSIVE);
            }

            if (messageBean.getMessageType() == LiveRoomMessage.MessageType.MESSAGE_ENTER_ROOM){
                itemView.setBackgroundResource(R.drawable.comprehensive_live_message_item_welcome_bg);
            } else {
                itemView.setBackgroundResource(R.drawable.comprehensive_live_message_item_bg);
            }
            contentTv.setText(builder);
            itemView.setSelected(highlight);
        }
    }

    private class ThemeMessageHolder extends BaseHolder<LiveRoomMessage> {

        private TextView contentTv;


        public ThemeMessageHolder(View itemView) {
            super(itemView);
            contentTv = itemView.findViewById(R.id.contentTv);
        }

        @Override
        public void setupData(LiveRoomMessage messageBean, int position) {
            String message = messageBean.getMessage().contentString;
            contentTv.setText(message);
        }
    }


    private class LiveBarrageHolder extends BaseHolder<LiveRoomMessage> {

        private ImageView soundIv;
        private TextView durationTv, contentTv, cityTv;

        private SpannableStringBuilder stringBuilder;
        private ForegroundColorSpan colorSpan;

        public LiveBarrageHolder(View itemView) {
            super(itemView);
            soundIv = itemView.findViewById(R.id.soundIv);
            durationTv = itemView.findViewById(R.id.durationTv);
            contentTv = itemView.findViewById(R.id.contentTv);
            cityTv = itemView.findViewById(R.id.cityTv);

            colorSpan = new ForegroundColorSpan(Color.parseColor("#9AFFFFFF"));
            itemView.setSelected(false);
        }

        @Override
        public void setupData(LiveRoomMessage liveRoomMessage, int position) {
            if (liveRoomMessage.getMessage() == null) return;

            MessageBean message = liveRoomMessage.getMessage();
            if (message != null && message.sendChatMsgData != null)
                durationTv.setText(String.format(itemView.getContext().getString(R.string.live_second_symbol), Math.round(message.sendChatMsgData.duration / 1000f) + ""));

            stringBuilder = new SpannableStringBuilder();
            String nickStr = message.nickName + ":";
            stringBuilder.append(nickStr + message.contentString);
            stringBuilder.setSpan(colorSpan, 0, nickStr.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);

            contentTv.setText(stringBuilder);
            cityTv.setText("");
            itemView.setSelected(message.nickName != null && message.nickName.equals(myselfNickName));
        }
    }
}
