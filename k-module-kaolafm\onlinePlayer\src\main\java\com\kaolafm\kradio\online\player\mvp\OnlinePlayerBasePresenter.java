package com.kaolafm.kradio.online.player.mvp;

import android.content.Context;

import com.kaolafm.kradio.common.CP;
import com.kaolafm.kradio.common.ErrorInfo;
import com.kaolafm.kradio.common.ResultCallback;
import com.kaolafm.kradio.common.SubscribeData;
import com.kaolafm.kradio.component.ComponentClient;
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.subscribe.SubscribeHelper;

import java.lang.ref.WeakReference;

/**
 * <AUTHOR> <PERSON>
 */
public abstract class OnlinePlayerBasePresenter<V extends OnlinePlayerBaseView> extends BasePresenter<OnlinePlayerBaseModel, V> {

    protected OnlineSubscribeCallback mSubscribeCallback;
    protected OnlineSubscribeCallback mUnSubscribeCallback;

    public CheckSubscribeCallback mCheckSubscribeCallback;

    private ComponentClient mSubscribeClient;

    private ComponentClient mSubscribedClient;

    private ComponentClient mUnsubscribeClient;

    public OnlinePlayerBasePresenter(V view) {
        super(view);
        mCheckSubscribeCallback = new CheckSubscribeCallback(this);
        initSubscribeCallback();
    }

    @Override
    protected OnlinePlayerBaseModel createModel() {
        return new OnlinePlayerBaseModel();
    }

    /**
     * 订阅媒体
     *
     * @param context
     * @param subscribeData
     */
    public void subscribeMedia(Context context, SubscribeData subscribeData) {
        int cp = CP.KaoLaFM;
        mSubscribeClient = SubscribeHelper.subscribe(cp, subscribeData, mSubscribeCallback);
    }

    /**
     * 订阅媒体
     *
     * @param context
     * @param subscribeData
     */
    public void subscribeMedia(Context context, SubscribeData subscribeData, OnlineSubscribeCallback mOnlineSubscribeCallback) {
        int cp = CP.KaoLaFM;
        mSubscribeClient = SubscribeHelper.subscribe(cp, subscribeData, mOnlineSubscribeCallback);
    }

    /**
     * 取消订阅媒体
     *
     * @param context
     * @param subscribeData
     */
    public void unSubscribeMedia(Context context, SubscribeData subscribeData) {
        int cp = CP.KaoLaFM;
        mUnsubscribeClient = SubscribeHelper.unsubscribe(cp, subscribeData, mUnSubscribeCallback);
    }

    /**
     * 取消订阅媒体
     *
     * @param context
     * @param subscribeData
     */
    public void unSubscribeMedia(Context context, SubscribeData subscribeData, OnlineSubscribeCallback mOnlineSubscribeCallback) {
        int cp = CP.KaoLaFM;
        mUnsubscribeClient = SubscribeHelper.unsubscribe(cp, subscribeData, mOnlineSubscribeCallback);
    }

    /**
     *
     */
    protected abstract void initSubscribeCallback();

    /**
     * 检测媒体是否订阅aaa
     */
    public void checkSubscribeMedia() {
        checkSubscribeMedia(PlayerManagerHelper.getInstance().getSubscribeId(),mCheckSubscribeCallback);
    }
    /**
     * 检测媒体是否订阅aaa
     */
    public void checkSubscribeMedia(long id,CheckSubscribeCallback callback) {
        int cp = CP.KaoLaFM;
        mSubscribedClient = SubscribeHelper.isSubscribed(cp, String.valueOf(id), callback);
    }

    public static class CheckSubscribeCallback implements ResultCallback {
        private WeakReference<OnlinePlayerBasePresenter> weakReference;

        public CheckSubscribeCallback(OnlinePlayerBasePresenter playerPresenter) {
            weakReference = new WeakReference<>(playerPresenter);
        }

        @Override
        public void onResult(boolean result, int status) {
            OnlinePlayerBasePresenter playerPresenter = weakReference.get();
            if (playerPresenter == null) {
                return;
            }
            playerPresenter.updateSubscribe(result);
        }

        @Override
        public void onFailure(ErrorInfo errorInfo) {
            OnlinePlayerBasePresenter playerPresenter = weakReference.get();
            if (playerPresenter == null) {
                return;
            }
            playerPresenter.updateSubscribe(false);
        }
    }

    public void updateSubscribe(boolean isSubscribe) {
        if (mView == null) {
            return;
        }
        mView.updateSubscribe(isSubscribe);
    }

    @Override
    public void destroy() {
        super.destroy();
        SubscribeHelper.cancel(mSubscribeClient, mSubscribedClient, mUnsubscribeClient);
    }
}
