package com.kaolafm.gradle.plugin.component
/**
 * 已扫描到接口或者codeInsertToClassName jar的信息
 * <AUTHOR>
 * @since 2018/04/17
 */
class ScanJarHarvest {
    List<Harvest> harvestList = new ArrayList<>()
    class Harvest {
        String className
        String interfaceName
        boolean isInitClass

        @Override
        public String toString() {
            return "Harvest{" +
                    "className='" + className + '\'' +
                    ", interfaceName='" + interfaceName + '\'' +
                    ", isInitClass=" + isInitClass +
                    '}';
        }
    }
}