package com.kaolafm.kradio.user.setting;

import com.kaolafm.kradio.setting.SettingItem;
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-06-03
 */
public class HiddenFunctionPresenter extends BasePresenter<HiddenFunctionModel, IHiddenFunctionView> {

    public HiddenFunctionPresenter(IHiddenFunctionView view) {
        super(view);
    }

    public void showHiddenFunctions() {
        List<SettingItem> hiddenItems = mModel.getHiddenItems();
        if (mView != null) {
            mView.showHideItem(hiddenItems);
        }
    }

    @Override
    protected HiddenFunctionModel createModel() {
        return new HiddenFunctionModel();
    }
}
