<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/y180"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingLeft="@dimen/dialog_common_margin_left">

    <TextView
        android:id="@+id/tv_dialog_bottom_cancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/globle_round_bg"
        android:paddingStart="@dimen/x40"
        android:paddingTop="@dimen/y14"
        android:paddingEnd="@dimen/x40"
        android:paddingBottom="@dimen/y14"
        android:text="@string/cancel"
        android:textColor="@color/dialog_common_btn_cancel_text_color"
        android:textSize="@dimen/text_size3" />

    <TextView
        android:id="@+id/tv_dialog_bottom_define"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/x32"
        android:background="@drawable/globle_round_bg"
        android:paddingStart="@dimen/x40"
        android:paddingTop="@dimen/y14"
        android:paddingEnd="@dimen/x40"
        android:paddingBottom="@dimen/y14"
        android:text="@string/ok"
        android:textColor="@color/dialog_common_btn_sure_text_color"
        android:textSize="@dimen/text_size3" />

    <TextView
        android:id="@+id/tv_dialog_bottom_message"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="end"
        android:textColor="@color/dialog_common_btn_title_text_color"
        android:textSize="@dimen/dialog_common_bottom_content_size"
        tools:text="确定清空收听历史？" />
</LinearLayout>
