package com.kaolafm.kradio.live.player;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class NimUserInfoCache {

    private NimUserInfoCache() {
    }

    public static NimUserInfoCache getInstance() {
        return InstanceHolder.instance;
    }

    private Map<String, NimUserInfoCustomer> account2UserMap = new ConcurrentHashMap<>();

    public void clear() {
        clearUserCache();
    }

    public static class NimUserInfoCustomer {
        public String account;
        public String name;
        public String avatar;
    }

    private void clearUserCache() {
        account2UserMap.clear();
    }

    /**
     * ************************************ 用户资料变更监听(监听SDK) *****************************************
     */

    /**
     * *************************************** User缓存管理与变更通知 ********************************************
     */

    /**
     * ************************************ 单例 **********************************************
     */

    private static class InstanceHolder {
        private final static NimUserInfoCache instance = new NimUserInfoCache();
    }
}
