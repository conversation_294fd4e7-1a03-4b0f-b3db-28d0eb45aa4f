package com.kaolafm.kradio.flavor.impl;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.CustomIntent;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.os.Bundle;
import android.util.Log;

import com.kaolafm.kradio.lib.base.flavor.NetworkBrodcastInter;
import com.kaolafm.opensdk.log.Logging;

import static android.content.CustomIntent.ACTION_NETWORK_CHANGED;
import static android.content.CustomIntent.EXTRA_NETWORK_STATUS;

/**
 * author : wxb
 * date   : 2022/1/12
 * desc   :
 */
public class NetworkBrodcastImpl implements NetworkBrodcastInter {

    NetBradcastReceiver bradcastReceiver = new NetBradcastReceiver();


    @Override
    public void registerNetworkStatusChangeListener(Context context) {
        IntentFilter filter = new IntentFilter(CustomIntent.ACTION_NETWORK_CHANGED);
        context.registerReceiver(bradcastReceiver, filter);
        Log.d("NetworkBrodcastImpl", "registerNetworkStatusChangeListener==" + CustomIntent.ACTION_NETWORK_CHANGED);
    }

    @Override
    public void removeNetworkStatusChangeListener(Context context) {

        try {
//            java.lang.IllegalArgumentException: Receiver not registered: com.kaolafm.kradio.flavor.impl.NetBradcastReceiver@10d1ee5
            context.unregisterReceiver(bradcastReceiver);
        }catch (Exception e){
            e.printStackTrace();
        }


        Log.d("NetworkBrodcastImpl", "removeNetworkStatusChangeListener==" + CustomIntent.ACTION_NETWORK_CHANGED);

    }
}
