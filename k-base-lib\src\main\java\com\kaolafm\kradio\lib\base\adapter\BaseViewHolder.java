package com.kaolafm.kradio.lib.base.adapter;

import androidx.recyclerview.widget.RecyclerView;
import android.view.View;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-04-11 11:21
 ******************************************/

public abstract class BaseViewHolder<T> extends RecyclerView.ViewHolder implements BaseViewHolderUpdateListener<T>, View.OnClickListener {

    public BaseViewHolder(View itemView) {
        super(itemView);
    }

    @Override
    public void updateData(T t, int position) {
    }

    @Override
    public void onViewRecycled(RecyclerView.ViewHolder holder) {
    }

    @Override
    public void onClick(View v) {
    }
}
