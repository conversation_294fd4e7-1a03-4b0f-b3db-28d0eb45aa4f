package com.kaolafm.ad.comprehensive.utils;

/**
 * The type Ad utils.
 */
public class ADUtils {

    /**
     * Gets ad type str.防止硬编码广告类型，不好维护
     *
     * @param args the args
     *             {@link KradioAdSceneConstants#AD_TYPE_AUDIO}
     *             {@link KradioAdSceneConstants#AD_TYPE_IMAGE}
     *             {@link KradioAdSceneConstants#AD_TYPE_AUDIO_IMAGE}
     * @return the ad type str
     */
    public static String getADTypeStr(int... args) {
        String retStr = null;
        for (int arg : args) {
            if(retStr == null){
                retStr = String.valueOf(arg);
            }else {
                retStr += (','+String.valueOf(arg));
            }
        }
        return retStr;
    }
}
