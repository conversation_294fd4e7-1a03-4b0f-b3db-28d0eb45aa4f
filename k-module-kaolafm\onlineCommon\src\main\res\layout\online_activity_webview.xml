<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/online_page_bg_vague"
    android:fitsSystemWindows="true"
    android:paddingTop="@dimen/activity_title_top_padding">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_title"
        android:layout_width="match_parent"
        android:layout_height="@dimen/m116"
        android:background="@color/transparent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <include
            layout="@layout/online_bbf_title_center_textview"
            android:layout_width="match_parent"
            android:layout_height="@dimen/m116"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <ImageView
            style="@style/OnlineFragmentBackButton"
            android:layout_marginTop="@dimen/m18"
            tools:ignore="MissingConstraints" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/m1"
        android:background="@color/online_web_view_line_color"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cl_title" />

    <WebView

        android:id="@+id/webview"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginLeft="@dimen/m80"
        android:layout_marginRight="@dimen/m80"
        android:background="@android:color/transparent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line" />

</androidx.constraintlayout.widget.ConstraintLayout>