package com.kaolafm.kradio.common.utils;

import android.Manifest.permission;
import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.provider.Settings;
import androidx.core.content.ContextCompat;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.flavor.PermissionApplyInter;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;


public class PermissionUtils {
    public String[] perms;

    Context mContext;

    //需要弹出权限对话框
    public PermissionUtils(Activity ctx) {
        mContext = ctx;
        initPermissions();
    }

    //不需要弹出对话框，只校验权限，复用需要权限列表
    public PermissionUtils(Context ctx) {
        mContext = ctx;
        initPermissions();
    }

    private void initPermissions() {
        //     kradio 需要申请的危险权限如下
        PermissionApplyInter mSyncInstrumentInter = (PermissionApplyInter) ClazzImplUtil.getInter("PermissionApplyImpl");
        if (mSyncInstrumentInter != null) {
            perms = mSyncInstrumentInter.getPermission();
        } else {
            perms = new String[]{
                    permission.WRITE_EXTERNAL_STORAGE,
                    permission.READ_EXTERNAL_STORAGE,
                    permission.RECORD_AUDIO,
                    permission.ACCESS_FINE_LOCATION,
                    permission.ACCESS_COARSE_LOCATION,
            };
        }
    }

    public String[] getPerms() {
        return perms;
    }

    static int dialogWhich = 0;
    private boolean isShowDialog = false;

    public void getDialog() {
//        KRadioPermissionInter kRadioPermissionInter = ClazzImplUtil.getInter("KRadioPermissionImpl");
        String pacName = mContext.getApplicationInfo().packageName;

        AlertDialog.Builder builder = new AlertDialog.Builder(mContext);
        builder.setCancelable(false);
        builder.setMessage(mContext.getResources().getString(R.string.permissions_app_tip));
        builder.setTitle(mContext.getResources().getString(R.string.permissions_tip));
        builder.setNegativeButton(mContext.getResources().getString(R.string.cancel_setpermission), new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialogWhich = which;
            }
        });
        builder.setPositiveButton(mContext.getResources().getString(R.string.go_setpermission), new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialogWhich = which;
            }
        });

        builder.setOnDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                switch (dialogWhich) {
                    case DialogInterface.BUTTON_NEGATIVE: {

                        break;
                    }
                    case DialogInterface.BUTTON_POSITIVE: {
                        Uri packageURI = Uri.parse("package:" + pacName);
                        Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS, packageURI);
                        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        mContext.startActivity(intent);
                        break;
                    }
                    default:
                }
                isShowDialog = false;
//                    System.exit(0);
            }
        });
        builder.create().show();
        isShowDialog = true;
    }

    public boolean isShowDialog() {
        return isShowDialog;
    }

    public boolean isGrant() {
        if (mContext != null) {
            int grantsize = 0;
            for (String perm : perms) {
                boolean isgrant = checkPermission(mContext, perm);
                if (isgrant) {
                    grantsize = grantsize + 1;
                }
            }
            return grantsize == perms.length;
        } else {
            return true;
        }
    }

    public static boolean checkPermission(Context context, String... permissions) {
        for (String permission : permissions) {
            int granted = ContextCompat.checkSelfPermission(context, permission);
            if (granted != PackageManager.PERMISSION_GRANTED) {
                return false;
            }
        }
        return true;
    }

}
