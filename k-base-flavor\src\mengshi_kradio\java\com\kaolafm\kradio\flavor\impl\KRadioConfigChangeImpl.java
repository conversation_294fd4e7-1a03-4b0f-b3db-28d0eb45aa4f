package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.kradio.common.SkinStateManager;
import com.kaolafm.kradio.component.ui.base.skin.SkinHelper;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.base.flavor.ConfigChangeInter;
import com.kaolafm.kradio.lib.base.flavor.ThemeInter;
import com.kaolafm.kradio.lib.base.flavor.UserCenterInter;
import com.kaolafm.kradio.lib.base.ui.BaseActivity;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;

import org.greenrobot.eventbus.EventBus;

import skin.support.SkinCompatManager;
import skin.support.utils.SkinPreference;

/**
 * 基于Settings数据库监听的主题切换实现
 */
public class KRadioConfigChangeImpl implements ConfigChangeInter {

    private String TAG = "KRadioConfigChangeImpl";
    private String preTheme = "unknown";

    // 车厂提供的主题key
    public static final String THEME_NIGHT = "theme.night"; //黑夜---1
    public static final String THEME_NONIGHT = "theme.nonight"; //白天---2
    public static final String KEY_THEME_TYPE = "android.car.THEME_TYPE";

    @Override
    public void onConfigChanged(String theme) {
        if (isSameTheme(theme, true)) {
            return;
        }
        Log.i(TAG, "onConfigChanged skin: last is diff from new");

        switch (theme) {
            case THEME_NONIGHT:
                SkinCompatManager.getInstance().loadSkin(SkinHelper.DAY_SKIN, SkinCompatManager.SKIN_LOADER_STRATEGY_ASSETS);
                SkinHelper.setSkinName(AppDelegate.getInstance().getContext(), SkinHelper.DAY_SKIN);
                Log.i(TAG, "onConfigChanged...THEME: DAY");
                new Handler(Looper.getMainLooper()).postDelayed(
                        () -> {
                            SkinStateManager.getInstance().notifyLoadSkinSuccess();
                            postThemeChangeEvent(SkinHelper.DAY_SKIN);
                        },
                        100);
                resetStatusBar();
                break;
            case THEME_NIGHT:
                Log.i(TAG, "onConfigChanged...THEME: NIGHT");
                SkinCompatManager.getInstance().loadSkin("", new SkinCompatManager.SkinLoaderListener() {
                    @Override
                    public void onStart() {
                        Log.i(TAG, "onConfigChanged...THEME: NIGHT start");
                    }

                    @Override
                    public void onSuccess() {
                        Log.i(TAG, "onConfigChanged...THEME: NIGHT success");
                        SkinStateManager.getInstance().notifyLoadSkinSuccess();
                        postThemeChangeEvent(SkinHelper.NIGHT_SKIN);
                    }

                    @Override
                    public void onFailed(String errMsg) {
                        Log.e(TAG, "onConfigChanged...THEME: NIGHT failed: " + errMsg);
                    }
                }, SkinCompatManager.SKIN_LOADER_STRATEGY_NONE);
                resetStatusBar();
                break;
            default:
                postThemeChangeEvent(SkinHelper.DAY_SKIN);
                Log.i(TAG, "onConfigChanged...THEME: unknown theme = " + theme + ", use default DAY");
                break;
        }
    }

    @Override
    public void saveConfiguration(String theme) {
        if (TextUtils.isEmpty(theme)) {
            Log.w(TAG, "saveConfiguration: theme is empty, skip");
            return;
        }
        String skin = SkinPreference.getInstance().getSkinName();
        Log.i(TAG, "saveConfiguration: theme = " + theme + ", oldUI skin = " + skin + ", preTheme = " + preTheme);
        onConfigChanged(theme);
        Log.i(TAG, "saveConfiguration: old skin = " + skin);
        Log.i(TAG, "saveConfiguration: SAVE_THEME = " + preTheme);
    }

    private void postThemeChangeEvent(String theme) {
        UserCenterInter.ThemeChangeEvent themeChangeEvent = new UserCenterInter.ThemeChangeEvent();
        themeChangeEvent.setTheme(theme);
        EventBus.getDefault().post(themeChangeEvent);

        ThemeInter themeInter = ClazzImplUtil.getInter("KRadioThemeImpl");
        if (themeInter != null) {
            themeInter.onThemeChanged(theme);
        }
    }

    @Override
    public void saveConfiguration(String theme, boolean forceSave) {
        if (TextUtils.isEmpty(theme)) {
            Log.w(TAG, "saveConfiguration: theme is empty, skip");
            return;
        }
        // 解决第一次进来的时候资源覆盖问题
        String skin = SkinPreference.getInstance().getSkinName();
        Log.i(TAG, "saveConfiguration: forceSave = " + forceSave + ", theme = " + theme + ", oldUI skin = " + skin + ", preTheme = " + preTheme);

        if (forceSave) {
            preTheme = "unknown";
            if (isSameTheme(theme, false)) {
                postThemeChangeEvent(SkinHelper.IS_SAME_THEME);
            }
        }
        if ("unknown".equals(preTheme)) {
            onConfigChanged(theme);
            Log.i(TAG, "saveConfiguration: old skin = " + skin);
        }
        Log.i(TAG, "saveConfiguration: SAVE_THEME = " + preTheme);
    }

    private void resetStatusBar() {
        new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
            @Override
            public void run() {
                Activity activity = AppManager.getInstance().getCurrentActivity();
                if (activity instanceof BaseActivity) {
                    ((BaseActivity) activity).transStatusBar();
                }
            }
        }, 800);
    }

    /**
     * 判断要设置的新主题与当前主题是否相同
     */
    private boolean isSameTheme(String theme, boolean isFromOnConfigChanged) {
        if (isFromOnConfigChanged) {
            Log.i(TAG, "isSameTheme: preTheme = " + preTheme + ", newTheme = " + theme);
        }

        if (TextUtils.equals(preTheme, theme)) {
            Log.i(TAG, "isSameTheme: isSameTheme = true, isFromOnConfigChanged = " + isFromOnConfigChanged);
            return true;
        }

        if (isFromOnConfigChanged) {
            Log.i(TAG, "isSameTheme: theme changed from " + preTheme + " to " + theme);
            preTheme = theme;
        }

        String skin = SkinPreference.getInstance().getSkinName();
        if (isFromOnConfigChanged) {
            Log.i(TAG, "isSameTheme: last skin = " + skin);
        }

        // 检查主题和皮肤是否匹配
        if (THEME_NONIGHT.equals(theme) && TextUtils.equals(skin, SkinHelper.DAY_SKIN)) {
            Log.i(TAG, "isSameTheme: already is day theme and day skin");
            return true;
        }

        if (THEME_NIGHT.equals(theme) && (TextUtils.isEmpty(skin) || TextUtils.equals(skin, SkinHelper.NIGHT_SKIN))) {
            Log.i(TAG, "isSameTheme: already is night theme and night skin");
            return true;
        }

        Log.i(TAG, "isSameTheme: theme or skin changed, need update");
        return false;
    }
}
