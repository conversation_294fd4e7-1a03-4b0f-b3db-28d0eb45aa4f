<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/x48"
    android:background="@drawable/online_player_item_barrage_bg"
    android:minHeight="@dimen/m44">
    <!--    android:background="@drawable/online_player_barrage_bg">-->
    <!--    android:background="@drawable/online_player_item_barrage_bg">-->


    <TextView
        android:id="@+id/contentTv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/x43"
        android:layout_marginTop="@dimen/y10"
        android:layout_marginEnd="@dimen/x12"
        android:layout_marginBottom="@dimen/y10"
        android:textColor="@color/online_player_system_message_color"
        android:textSize="@dimen/m24"
        android:lineSpacingMultiplier="1.5"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0"
        tools:text="这里是内容这里是内容" />
</androidx.constraintlayout.widget.ConstraintLayout>