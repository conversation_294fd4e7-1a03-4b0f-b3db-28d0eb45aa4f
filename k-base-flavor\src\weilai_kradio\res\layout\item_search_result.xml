<?xml version="1.0" encoding="utf-8"?>
<com.kaolafm.kradio.common.widget.ScaleConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_search_result"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/search_guide_line_center"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.5" />

    <ImageView
        android:id="@+id/iv_search_cover"
        android:layout_width="@dimen/x200"
        android:layout_height="@dimen/x200"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_default="percent" />

    <View
        android:id="@+id/search_bg_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/bg_search_result"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/iv_search_cover"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/pi_search_result_playing_indicator"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginRight="@dimen/x21"
        android:background="@drawable/playing_bar_chart"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tv_search_title"
        app:layout_constraintDimensionRatio="W,1:1"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_search_title" />

    <com.kaolafm.kradio.k_kaolafm.search.KeywordTextView
        android:id="@+id/tv_search_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="@dimen/x20"
        android:layout_marginBottom="@dimen/y6"
        android:ellipsize="marquee"
        android:paddingLeft="@dimen/x24"
        android:singleLine="true"
        android:textColor="@color/search_result_title_text_color"
        android:textSize="@dimen/text_size9"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@id/search_guide_line_center"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintLeft_toRightOf="@id/iv_search_cover"
        app:layout_constraintRight_toLeftOf="@id/pi_search_result_playing_indicator"
        tools:text="标题标题标题标题" />

    <TextView
        android:id="@+id/tv_search_sub_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/x24"
        android:layout_marginTop="@dimen/y6"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/search_subtitle_color"
        android:textSize="@dimen/search_item_content_title_text_size"
        app:layout_constrainedWidth="true"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintLeft_toRightOf="@id/iv_search_cover"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/search_guide_line_center"
        tools:text="小标题小标题小标题小标题" />

    <ImageView
        android:id="@+id/search_result_vertical_line"
        android:layout_width="@dimen/x3"
        android:layout_height="0dp"
        android:layout_marginLeft="@dimen/x9"
        android:scaleType="center"
        android:src="@drawable/radio_list_item_vertical_line"
        app:layout_constraintBottom_toBottomOf="@id/tv_search_sub_title"
        app:layout_constraintLeft_toRightOf="@id/tv_search_sub_title"
        app:layout_constraintTop_toTopOf="@id/tv_search_sub_title" />

    <TextView
        android:id="@+id/tv_search_second_sub_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/x9"
        android:layout_marginRight="@dimen/x5"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/search_subtitle_color"
        android:textSize="@dimen/text_size1"
        app:layout_constrainedWidth="true"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintLeft_toRightOf="@id/search_result_vertical_line"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_search_sub_title"
        tools:text="小小标题" />

</com.kaolafm.kradio.common.widget.ScaleConstraintLayout>