package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.content.Intent;
import android.os.Handler;
import androidx.fragment.app.DialogFragment;
import androidx.appcompat.app.AppCompatActivity;
import android.view.Gravity;


import com.kaolafm.kradio.common.event.StopAudioEBData;
import com.kaolafm.kradio.flavor.R;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.base.flavor.KRadioBackKeyInter;
import com.kaolafm.kradio.lib.dialog.Dialogs;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.service.BYDWidgetService;
import com.kaolafm.opensdk.player.logic.PlayerManager;

import org.greenrobot.eventbus.EventBus;


/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-04-24 16:02
 ******************************************/
public final class KRadioBackKeyImpl implements KRadioBackKeyInter {
    @Override
    public boolean onBackPressed(Object... args) {
        final AppCompatActivity activity = (AppCompatActivity) args[0];
        DialogFragment dialogFragment = new Dialogs.Builder()
                .setType(Dialogs.TYPE_2BTN)
                .setGravity(Gravity.CENTER)
                .setMessage(ResUtil.getString(R.string.exit_msg_str))
                .setLeftBtnText(ResUtil.getString(R.string.ok))
                .setRightBtnText(ResUtil.getString(R.string.move_to_background_str))
                .setOnPositiveListener(dialog -> {
                    activity.moveTaskToBack(true);
                    dialog.dismiss();
                })
                .setOnNativeListener(dialog -> {
                    AppManager.getInstance().appExit();
                    dialog.dismiss();
                })
                .create();
        dialogFragment.show(activity.getSupportFragmentManager(), "clear_history");
        return true;
    }

    @Override
    public boolean appExit(Object... args) {
        Activity activity = AppManager.getInstance().getCurrentActivity();
        setWidgetState();
        if (activity == null) {
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    AppManager.getInstance().appExit();
                }
            }, 500);
        } else {
            StopAudioEBData stopAudioEBData = new StopAudioEBData();
            stopAudioEBData.canStopAudio = true;
            EventBus.getDefault().post(stopAudioEBData);
        }
        return true;
    }

    @Override
    public void dealKillYunTingReceiver(Object... args) {

    }

    private void setWidgetState() {
        PlayerManager.getInstance().pause(false);
        Intent intent = new Intent(AppDelegate.getInstance().getContext(), BYDWidgetService.class);
        intent.setAction(BYDWidgetService.WIDGET_ACTION_PAUSE);
        AppDelegate.getInstance().getContext().startService(intent);
    }
}
