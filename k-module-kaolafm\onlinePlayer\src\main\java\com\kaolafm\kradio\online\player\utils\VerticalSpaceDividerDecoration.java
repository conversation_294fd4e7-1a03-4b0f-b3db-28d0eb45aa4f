package com.kaolafm.kradio.live.comprehensive.ui.adapters;

import android.content.Context;
import android.graphics.Rect;
import android.support.v7.widget.RecyclerView;
import android.support.v7.widget.RecyclerView.Adapter;
import android.support.v7.widget.RecyclerView.ItemDecoration;
import android.support.v7.widget.RecyclerView.State;
import android.view.View;
import com.kaolafm.kradio.k_kaolafm.R.dimen;
import org.jetbrains.annotations.NotNull;

public final class VerticalSpaceDividerDecoration extends ItemDecoration {
   private int dividerHeight;
   private Context mContext;

   @Override
   public void getItemOffsets(@NotNull Rect outRect, @NotNull View view, @NotNull RecyclerView parent, @NotNull State state) {
      Adapter var10000 = parent.getAdapter();
      int count = var10000 != null ? var10000.getItemCount() : 0;
      super.getItemOffsets(outRect, view, parent, state);
      outRect.set(0, this.dividerHeight, 0, 0);
   }

   public VerticalSpaceDividerDecoration(@NotNull Context mContext) {
      super();
      this.dividerHeight = 1;
      this.mContext = mContext;
      this.dividerHeight = mContext.getResources().getDimensionPixelSize(dimen.m2);
   }

   public VerticalSpaceDividerDecoration(@NotNull Context mContext, int dividerHeight) {
      super();
      this.dividerHeight = 1;
      this.mContext = mContext;
      this.dividerHeight = dividerHeight;
   }
}
