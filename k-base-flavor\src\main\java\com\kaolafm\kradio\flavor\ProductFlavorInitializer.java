//package com.kaolafm.kradio.flavor;
//
//import android.app.Application;
//import android.content.Context;
//import android.os.Build;
//import android.util.Log;
//
//import KlSdkVehicle;
//import com.kaolafm.kradio.lib.base.flavor.DeviceInfoSetting;
//import com.kaolafm.kradio.lib.base.flavor.KRadioAuthInter;
//import com.kaolafm.kradio.lib.base.flavor.KRadioThirdPlatformInitInter;
//import com.kaolafm.kradio.lib.base.flavor.SyncInstrumentInter;
//import com.kaolafm.kradio.lib.init.AppInit;
//import com.kaolafm.kradio.lib.init.BaseAppInitializer;
//import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
//import com.kaolafm.kradio.lib.utils.ClazzUtil;
//import com.kaolafm.kradio.lib.utils.Constants;
//import com.kaolafm.kradio.lib.utils.InjectManager;
//import com.kaolafm.kradio.lib.utils.StringUtil;
//import com.kaolafm.opensdk.player.core.utils.PlayerCustomizeManager;
//import com.kaolafm.report.ReportHelper;
//
///**
// * 渠道初始化，这里的初始化会优先kaolafm的module里面所有。所以如果有其他必须在某个模块初始化之后才能做得操作需要添加监听或在其模块中初始化。
// *
// * <AUTHOR> Yan
// * @date 2019-09-18
// */
////@AppInit(priority = 60, description = "渠道适配初始化", isAsync = true, dependOn = "com.kaolafm.kradio.common.CommonInitializer")
//@AppInit(priority = 60, description = "渠道适配初始化", isAsync = true)
//public class ProductFlavorInitializer extends BaseAppInitializer {
//
//    @Override
//    public void onCreate(Application application) {
//        Log.i(Constants.START_TAG, "ProductFlavorInitializer: onCreate");
//        SyncInstrumentInter mSyncInstrumentInter = ClazzImplUtil.getInter("SyncInstrumentImpl");
//        if (mSyncInstrumentInter != null) {
//            mSyncInstrumentInter.initSyncInstrument();
//        }
//        initDeviceInfo(application);
//
//        KRadioThirdPlatformInitInter kRadioThirdPlatformInitInter = ClazzImplUtil.getInter("KRadioThirdPlatformInitImpl");
//        if (kRadioThirdPlatformInitInter != null) {
//            kRadioThirdPlatformInitInter.initThirdPlatform(application);
//        }
//        PlayerCustomizeManager.getInstance().setPlayStreamTypeListener(ClazzImplUtil.getInter("OnPlayerConfigImpl"));
//        PlayerCustomizeManager.getInstance()
//                .setOnHandleAudioFocusListener(ClazzImplUtil.getInter("OnHandleAudioFocusImpl"));
//        try {
//            KRadioAuthInter mKRadioAuthInter = (KRadioAuthInter) ClazzUtil
//                    .invoke(Class.forName(StringUtil.join(ClazzImplUtil.CLASS_FATHER_PACKAGE, "KRadioAuthImpl")),
//                            null,
//                            "getInstance",
//                            new Object[]{});
//            if (mKRadioAuthInter != null) {
//                mKRadioAuthInter.doInitCheckCanPlayInter();
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        InjectManager.getInstance().injectInit();
//    }
//
//    @Override
//    public void asyncCreate(Application application) {
//        super.asyncCreate(application);
//    }
//
//    /**
//     * 初始化设备信息
//     */
//    private void initDeviceInfo(Context context) {
//        DeviceInfoSetting deviceInfoSetting = ClazzImplUtil.getInter("DeviceInfoSettingImpl");
//        if (deviceInfoSetting != null) {
//            deviceInfoSetting.setInfoForSDK(context);
//        } else {
//            KlSdkVehicle.getInstance().setCarType(Build.DEVICE);
//        }
//        ReportHelper.getInstance().setCarType(KlSdkVehicle.getInstance().getCarType());
//    }
//}
