package com.kaolafm.kradio.user;

import android.content.res.Configuration;
import android.os.Bundle;
import androidx.annotation.CallSuper;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.kradio.common.widget.NotScrollViewPager;
import com.kaolafm.kradio.component.ComponentClient;
import com.kaolafm.kradio.component.DynamicComponent;
import com.kaolafm.kradio.component.MainThreadable;
import com.kaolafm.kradio.component.RealCaller;
import com.kaolafm.kradio.constant.LoginProcessorConst;
import com.kaolafm.kradio.constant.UserComponentConst;
import com.kaolafm.kradio.constant.UserStateObserverProcessorConst;
import com.kaolafm.kradio.history.comprehensive.ui.ButtonOperation;
import com.kaolafm.kradio.history.comprehensive.ui.HistoryFragment;
import com.kaolafm.kradio.home.comprehensive.ui.view.BaseBackFragment;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioMultiWindowInter;
import com.kaolafm.kradio.lib.base.mvp.IPresenter;
import com.kaolafm.kradio.lib.base.ui.BaseShowHideFragment;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.ComponentUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.MultiUtil;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.lib.widget.tab.OnTabSelectListener;
import com.kaolafm.kradio.lib.widget.tab.SlidingTabLayout;
import com.kaolafm.kradio.lib.widget.tab.Tab;
import com.kaolafm.kradio.purchase.comprehensive.purchased.PurchasedHomeFragment;
import com.kaolafm.kradio.subscribe.comprehensive.ui.SubscribeHomeFragment;
import com.kaolafm.kradio.user.comprehensive.ui.ComprehensiveUserInfoFragment;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.event.LoginReportEvent;
import com.kaolafm.report.util.ReportConstants;
import com.kaolafm.report.util.ReportParameterManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

//import com.kaolafm.kradio.coin.CoinFragment;

/**
 * 个人中心 页面（包括搜索、订阅收听历史、已购、账户中心等功能）
 */
@Route(path = RouterConstance.MINE_COMPREHENSIVE_URL)
public class BackUserFragment extends BaseBackFragment implements ButtonOperation {
    private static final String TAG = "BackUserFragment";

    private DynamicComponent mUserStateObserver;

    private boolean showClearHisotry;

    private SlidingTabLayout userTabTitle;

    private View viewDivider;

    private NotScrollViewPager userViewpager;

    //    private ImageView userRightBtn;
    private LinearLayout userSetting;
    private TextView settingBtn;

    private UserFragmentAdapter userFragmentAdapter;

    private int curPosition;

    private boolean subScriptionBtnShow = false;
    private boolean purchasedShow = false;

    public final static int SUBSCRIPTION = 0;
    public final static int HISTORY = 1;
    public final static int PURCHASED = 2;
    public final static int ACCOUNT = 3;
    public final static int POINTS = 3;
    public final static int SETTINGS = 5;

    private final static String PAGE_POSITION = "page_position";

    private HistoryFragment historyFragment;

    private boolean startSynchronize;

//    private SettingFragment settingFragment;

    private SubscribeHomeFragment mSubHomeFrag;
    //
    private PurchasedHomeFragment mPurchasedHomeFrag;

    /**
     * 是否登录成功后自动退出页面
     */
    String mBackType = LoginProcessorConst.BACKTYPE_NONE;
    int mBackIndex = -1;

    //遮罩
    private View zhezhao;

    public KRadioMultiWindowInter mKRadioMultiWindowInter;
    private int mOneClickButtonVisibility;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG,"onCreate");
        mKRadioMultiWindowInter = ClazzImplUtil.getInter("KradioMultiWindowImpl");
        mOneClickButtonVisibility = ResUtil.getInt(R.integer.one_click_listening_button_visibility);
    }


    @Override
    public void initView(View view) {
        super.initView(view);
        Log.d(TAG,"initView");
        TextView titleView = (TextView) View.inflate(getContext(), R.layout.bbf_title_center_textview, null);
        titleView.setText(R.string.user_title);
        this.addTitleCenterView(titleView);
        View contentView = View.inflate(getContext(), R.layout.fragment_user_content, null);
        zhezhao = contentView.findViewById(R.id.zhezhao);
        userTabTitle = contentView.findViewById(R.id.user_tab_title);
        viewDivider = contentView.findViewById(R.id.view_divider);
        userViewpager = contentView.findViewById(R.id.user_viewpager);
        userSetting = contentView.findViewById(R.id.user_setting);
        settingBtn = contentView.findViewById(R.id.settingBtn);
        userSetting.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
//                ComponentClient.obtainBuilder(SettingComponentConst.NAME)
//                        .setActionName(SettingComponentConst.START_ACTIVITY)
//                        .addParam("context", getActivity())
//                        .build().callAsync();
                RouterManager.getInstance().navigateToPage(getContext(), Constants.PAGE_ID_MINE_SETTING);
                ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_SETTING_BUTTON, settingBtn.getText().toString(), getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
            }
        });
        this.addContentView(contentView);
        initLoginOB();
        ButtonExposureOrClickReportEvent reportEventBean = new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_SETTING_BUTTON, settingBtn.getText().toString(), getPageId(), ReportConstants.CONTROL_TYPE_SCREEN);
        reportEventBean.setPage(getPageId());
        ReportHelper.getInstance().addEvent(reportEventBean);
    }


    /***************************************************************************************************************/

    public static BackUserFragment newInstance(int position) {
        BackUserFragment userFragment = new BackUserFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(PAGE_POSITION, position);
        userFragment.setArguments(bundle);
        return userFragment;
    }

    @Override
    public void showOrHide(boolean show) {
        if (userViewpager.getCurrentItem() == HISTORY) {
            showClearHisotry = show;
//            updateListenImmediatelyAndClearHistoryButtonState(HISTORY);
        }
    }

    @Override
    protected IPresenter createPresenter() {
        return null;
    }


    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        Log.d(TAG,"onViewCreated");
        userViewpager.setOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
            }

            @Override
            public void onPageSelected(int position) {
                curPosition = position;
//                updateListenImmediatelyAndClearHistoryButtonState(position);
//                Fragment fragment = userFragmentAdapter.getItem(position);
//                if (fragment != null) {
//                    fragment.onResume();
//                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {
            }
        });

        userViewpager.setOffscreenPageLimit(5);
    }

    @Override
    protected int getLayoutId_Tow() {
        return 0;
    }

    private List<Fragment> getChildFragments() {
        List<Fragment> list = new ArrayList<>();
        mSubHomeFrag = new SubscribeHomeFragment();
        mSubHomeFrag.setOnKeyListenListener(show -> {
            subScriptionBtnShow = show;
        });

        historyFragment = new HistoryFragment();
        mPurchasedHomeFrag = new PurchasedHomeFragment();

        mPurchasedHomeFrag.setOnKeyListenListener(show -> {
            purchasedShow = show;
        });
        list.add(mSubHomeFrag);
        list.add(historyFragment);
        //已购页面
        list.add(mPurchasedHomeFrag);
        list.add(new ComprehensiveUserInfoFragment());
        return list;
    }

    @Override
    public void onEnterAnimationEnd(Bundle savedInstanceState) {
        super.onEnterAnimationEnd(savedInstanceState);

        Log.d(TAG,"onEnterAnimationEnd");
        int pagePosition = 0;
        Bundle bundle = getArguments();
        if (bundle != null) {
            pagePosition = bundle.getInt(PAGE_POSITION, 0);
        }

        userFragmentAdapter = new UserFragmentAdapter(getChildFragmentManager(), getChildFragments());
        userViewpager.setAdapter(userFragmentAdapter);
//        userViewpager.setCurrentItem(pagePosition);
        //要在tabLayout.setAdapter之后再调用下边的方法,tabLayout才会与viewpager一致;
        userTabTitle.setCurrentTab(pagePosition);
        userViewpager.setCurrentItem(pagePosition);
        userTabTitle.setTabs(getTabList());
        userTabTitle.setOnTabSelectListener(new OnTabSelectListener() {
            @Override
            public void onTabSelect(int position) {
                userViewpager.setCurrentItem(position);
                String buttonid;
                switch (userTabTitle.getTab(position).title) {
                    case "我的订阅":
                        buttonid = ButtonExposureOrClickReportEvent.BUTTON_ID_MY_SUBSCRIBE_BUTTON;
                        break;
                    case "收听历史":
                        buttonid = ButtonExposureOrClickReportEvent.BUTTON_ID_MY_LISTEN_HISTORY;
                        break;
                    case "已购":
                        buttonid = ButtonExposureOrClickReportEvent.BUTTON_ID_MY_PURCHASED;
                        break;
                    default:
                        buttonid = ButtonExposureOrClickReportEvent.BUTTON_ID_PERSONAL_CENTER;
                }
                // fix ZMKQ-5542，切换tab时检查网络，增加toast提示
                NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), true);

                ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, buttonid, userTabTitle.getTab(position).title, ReportParameterManager.getInstance().getPage(), ReportConstants.CONTROL_TYPE_SCREEN));
            }

            @Override
            public void onTabReselect(int position) {

            }
        });
    }

    private List<Tab> getTabList() {
        String[] title = ResUtil.getStringArray(R.array.user_title);
        List<Tab> list = new ArrayList<>();
        for (int i = 0; i < title.length; i++) {
            Tab tab = new Tab();
            tab.title = title[i];
            tab.position = i;
            tab.code = i + "";
            list.add(tab);

            String buttonid;
            switch (tab.title) {
                case "我的订阅":
                    buttonid = ButtonExposureOrClickReportEvent.BUTTON_ID_MY_SUBSCRIBE_BUTTON;
                    break;
                case "收听历史":
                    buttonid = ButtonExposureOrClickReportEvent.BUTTON_ID_MY_LISTEN_HISTORY;
                    break;
                case "已购":
                    buttonid = ButtonExposureOrClickReportEvent.BUTTON_ID_MY_PURCHASED;
                    break;
                default:
                    buttonid = ButtonExposureOrClickReportEvent.BUTTON_ID_PERSONAL_CENTER;
            }
            ButtonExposureOrClickReportEvent reportEventBean = new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, buttonid, tab.title, Constants.PAGE_ID_ACCOUNT_MAIN, ReportConstants.CONTROL_TYPE_SCREEN);
            reportEventBean.setPage(Constants.PAGE_ID_ACCOUNT_MAIN);
            ReportHelper.getInstance().addEvent(reportEventBean);
        }
        return list;
    }


    private class UserStateObserver implements DynamicComponent, MainThreadable {
        @Override
        public String getName() {
            return "BackUserFragment-DynamicComponent";
        }

        @Override
        public boolean onCall(RealCaller caller) {
            switch (caller.actionName()) {
                case UserStateObserverProcessorConst.USER_LOGIN: {
                    if (!TextUtils.isEmpty(LoginManager.getInstance().getLoginInTo())){
                        reportLoginEvent();
                    }
                    if (mBackType.equals(LoginProcessorConst.BACKTYPE_POP)) {
                        if (mBackIndex == Constants.PLAYER_ITEM_CLICK
                                || mBackIndex == Constants.PLAYER_ITEM_CAROUSEL) {
//                            reportLoginEvent(mBackIndex);
                            mBackIndex = -1;
                        }
                        pop();
                    }
                    if (mBackType.equals(LoginProcessorConst.BACKTYPE_SWITCH)) {
                        updateCurrentTabPosition(mBackIndex);
//                        reportLoginEvent(mBackIndex);
                        //置空
                        setBackData(LoginProcessorConst.BACKTYPE_NONE, -1);
                    }
                    if (mBackIndex == BackUserFragment.SETTINGS) {
                        ComponentUtil.notifyObserver("SubscribeComponent", mBackType, null, true);
                    }
                    if (mBackIndex == Constants.HOME_TO_LOGIN) {
//                        reportLoginEvent(mBackIndex);
                        mBackIndex = -1;
                    }
                    break;
                }
//                case UserStateObserverProcessorConst.USER_LOGOUT: {
//                    if (userRightBtn != null
//                            && ACCOUNT == curPosition) {
//                        ViewUtil.setViewVisibility(userRightBtn, View.INVISIBLE);
//                    }
//                    break;
//                }
            }
            return false;
        }

        @Override
        public Boolean shouldActionRunOnMainThread(String actionName, ComponentClient caller) {
            return Boolean.TRUE;
        }
    }

    private void initLoginOB() {
        mUserStateObserver = new UserStateObserver();
        HashMap<String, Object> params = null;
        if (mBackType.equals(LoginProcessorConst.BACKTYPE_POP)) {
            //返回结果，退出登录页面。用户中心不需要显示loading
            params = new HashMap<>(1);
            params.put("isBack", true);
        }
        ComponentUtil.addObserver(UserComponentConst.NAME, mUserStateObserver, params);

    }

    private void reportLoginEvent() {
        String type = UserInfoManager.getInstance().getLoginType();
        if (TextUtils.isEmpty(type)) {
            return;
        }
        String remarks1 = LoginManager.getInstance().getLoginInTo();
        LoginManager.getInstance().setLoginInTo("");
        LoginReportEvent event = new LoginReportEvent();
        event.setType(type);
        event.setPage(Constants.PAGE_ID_LOGIN);
        event.setRemarks1(remarks1);
        ReportHelper.getInstance().addEvent(event);

    }
    private void reportLoginEvent(int index) {
        String type = UserInfoManager.getInstance().getLoginType();
        if (TextUtils.isEmpty(type)) {
            return;
        }
        String remarks1 = null;
        switch (index) {
            case Constants.SUBSCRIBE_INDEX:
                remarks1 = LoginReportEvent.REMARKS1_SUBSCRIBE_PAGE;
                break;
            case Constants.HISTORY_INDEX:
                remarks1 = LoginReportEvent.REMARKS1_HISTORY_PAGE;
                break;
            case Constants.PURCHASED_INDEX:
                remarks1 = LoginReportEvent.REMARKS1_PAYED_PAGE;
                break;
            case Constants.PLAYER_ITEM_CLICK:
                remarks1 = LoginReportEvent.REMARKS1_PLAY_ITEM;
                break;
            case Constants.PLAYER_ITEM_CAROUSEL:
                remarks1 = LoginReportEvent.REMARKS1_PLAY_CAROUSEL;
                break;
            case Constants.HOME_TO_LOGIN:
                remarks1 = LoginReportEvent.REMARKS1_USER_CENTER;
                break;
            default:
                break;
        }
        LoginReportEvent event = new LoginReportEvent();
        event.setType(type);
        event.setRemarks1(remarks1);
        ReportHelper.getInstance().addEvent(event);
    }

    public void updateCurrentTabPosition(int position) {
        if (userTabTitle != null) {
            userTabTitle.setCurrentTab(position);
        }
    }

//    /**
//     * 更新“一键收听”和"一键清空"按钮状态<br/>
//     * <p>
//     * "一键收听"与“一键清空”按钮本分别属于订阅与历史模块，但因UI设计缘故，需将其放在
//     * BackUserFragment中实现。
//     *
//     * @param position Tab页位置索引
//     */
//    private void updateListenImmediatelyAndClearHistoryButtonState(int position) {
//        KRadioOneKeyPlayInter inter = ClazzImplUtil.getInter("KRadioOneKeyPlayImpl");
//        boolean isCanShow = inter == null || inter.isShow();
//        if (position == SUBSCRIPTION) {
//            if (subScriptionBtnShow && UserInfoManager.getInstance().isUserLogin() && isCanShow) {
////                ViewUtil.setViewVisibility(userRightBtn, mOneClickButtonVisibility);
//                ViewUtil.setViewVisibility(userRightBtn, View.INVISIBLE);
////                Drawable drawable = ResUtil.getDrawable(R.drawable.ic_onkeyplay_normal);
////                int dimen = ResUtil.getDimen(R.dimen.m20);
////                drawable.setBounds(0, 0, dimen, dimen);
////                userRightBtn.setCompoundDrawablesRelative(drawable, null, null, null);
////                userRightBtn.setText(R.string.one_key_listen);
//                userRightBtn.setImageResource(R.drawable.ic_user_play_all);
//            } else {
//                ViewUtil.setViewVisibility(userRightBtn, View.INVISIBLE);
//            }
//
//        } else if (position == HISTORY) {
//            ViewUtil.setViewVisibility(userRightBtn, showClearHisotry ? View.VISIBLE : View.GONE);
//            if (showClearHisotry) {
//                ViewUtil.setViewVisibility(userRightBtn, View.VISIBLE);
////                Drawable drawable = ResUtil.getDrawable(R.drawable.user_history_delete);
////                int dimen = ResUtil.getDimen(R.dimen.m20);
////                drawable.setBounds(0, 0, dimen, dimen);
////                userRightBtn.setCompoundDrawablesRelative(drawable, null, null, null);
////                userRightBtn.setText(R.string.user_history_clear_str);
//                userRightBtn.setImageResource(R.drawable.sl_clear_his);
//            } else {
//                ViewUtil.setViewVisibility(userRightBtn, View.INVISIBLE);
//            }
//        } else if (position == ACCOUNT) {
//            if (UserInfoManager.getInstance().isUserLogin()) {
//                ViewUtil.setViewVisibility(userRightBtn, View.VISIBLE);
////                Drawable drawable = ResUtil.getDrawable(R.drawable.ic_order);
////                int dimen = ResUtil.getDimen(R.dimen.m20);
////                drawable.setBounds(0, 0, dimen, dimen);
////                userRightBtn.setCompoundDrawablesRelative(drawable, null, null, null);
////                userRightBtn.setText(R.string.my_order);
//                userRightBtn.setImageResource(R.drawable.ic_user_my_order_selector);
//            } else {
//                ViewUtil.setViewVisibility(userRightBtn, View.INVISIBLE);
//            }
//        } else if (position == PURCHASED) {
////          if (UserInfoManager.getInstance().isUserLogin() && isCanShow) {
////              ViewUtil.setViewVisibility(userRightBtn, View.VISIBLE);
////              Drawable drawable = ResUtil.getDrawable(R.drawable.ic_onkeyplay_normal);
////              drawable.setBounds(0, 0, dimen, dimen);
////              userRightBtn.setCompoundDrawablesRelative(drawable, null, null, null);
////              userRightBtn.setText(R.string.one_key_listen);
////              userRightBtn.setImageResource(R.drawable.ic_user_play_all);
////          } else {
//                ViewUtil.setViewVisibility(userRightBtn, View.INVISIBLE);
////          }
//        } else {
//            ViewUtil.setViewVisibility(userRightBtn, View.INVISIBLE);
//        }
//    }

    @Override
    public String getPageId() {
        BaseShowHideFragment fragment = getCurrentBaseFragment();
        if (fragment != null) {
            return fragment.getPageId();
        }
        return Constants.PAGE_ID_ACCOUNT_MAIN;
    }

    protected boolean isReportFragment() {
        BaseShowHideFragment fragment = getCurrentBaseFragment();
        if (fragment != null) {
            return fragment.getStartTime() > 0;
        }
        return false;
    }

    private BaseShowHideFragment getCurrentBaseFragment() {
        if (userFragmentAdapter != null) {
            Fragment fragment = userFragmentAdapter.getItem(curPosition);
            if (fragment instanceof BaseShowHideFragment) {
                return (BaseShowHideFragment) fragment;
            }
        }
        return null;
    }

    @Override
    @CallSuper
    protected void showAccordingToScreen(int orientation) {
        super.showAccordingToScreen(orientation);
        int paddingLeft = ScreenUtil.getGlobalPaddingLeft(orientation);
        int paddingRight;

        ViewGroup.LayoutParams layoutParamsZhe = zhezhao.getLayoutParams();
        layoutParamsZhe.width = paddingLeft;

        int dimenPadding;
        int textSize;
        if (orientation == Configuration.ORIENTATION_PORTRAIT) {
            dimenPadding = ResUtil.getDimen(R.dimen.x20);
            textSize = ResUtil.getDimen(R.dimen.user_sub_title_port_text_size);
            paddingRight = ResUtil.getDimen(R.dimen.right_btn_port_padding);
        } else {
            dimenPadding = ResUtil.getDimen(R.dimen.x30);
            textSize = ResUtil.getDimen(R.dimen.user_tab_text_size);
            paddingRight = ResUtil.getDimen(R.dimen.default_edge_end);
            if (MultiUtil.getMultiStatus()) {
                dimenPadding = ResUtil.getDimen(R.dimen.x20);
                textSize = ResUtil.getDimen(R.dimen.user_sub_title_port_text_size);
            }
        }
        Log.i(TAG, "userTabTitle textsize::" + textSize);

        userTabTitle.setTabPadding(ScreenUtil.px2dp(dimenPadding));

        int tempTextSize = textSize;
        userTabTitle.setTextSize(tempTextSize);
        //SlidingTabLayout的2.1.6统一使用sp
        int selectTextSize = tempTextSize;// ScreenUtil.sp2px(tempTextSize);
        userTabTitle.setTextSelectedSize(selectTextSize);

        //因为mTabLayout的item如果设置了item的padding,为了与页面的返回按钮对齐,做特殊处理;
//        float tabPadding = userTabTitle.getTabPadding();
//        paddingLeft -= tabPadding;

//        ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) userTabTitle.getLayoutParams();
//        layoutParams.leftMargin = paddingLeft;

        ViewGroup.MarginLayoutParams layoutParamsRight = (ViewGroup.MarginLayoutParams) userSetting.getLayoutParams();
        layoutParamsRight.rightMargin = paddingRight;

    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        // CPU优化：清理图片内存缓存
        if (getContext() != null) {
            ImageLoader.getInstance().clearMemoryCache(getContext());
        }
        ComponentUtil.removeObserver(UserComponentConst.NAME, mUserStateObserver);
    }

    public void setBackData(String type, int index) {
        mBackType = type;
        mBackIndex = index;
    }

    public boolean isUserLoginFragment() {
        boolean ret = false;
        if (userViewpager != null) {
            int currentItem = userViewpager.getCurrentItem();
            if (currentItem == 3) {
                ret = true;
            }
        }
        return ret;
    }

    @Override
    protected boolean autoSetBackViewMarginLeft() {
        return false; // 禁用自动边距，与设置页面保持一致
    }
}
