package com.kaolafm.kradio.player.online.views;

import android.animation.Animator;
import android.animation.Animator.AnimatorListener;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.PropertyValuesHolder;
import android.animation.TimeInterpolator;
import android.content.Context;
import android.support.constraint.ConstraintLayout;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.DecelerateInterpolator;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.kradio.common.widget.YunTingMusicPlayAnimationView;
import com.kaolafm.kradio.k_kaolafm.R.drawable;
import com.kaolafm.kradio.k_kaolafm.R.id;
import com.kaolafm.kradio.k_kaolafm.R.layout;
import com.kaolafm.kradio.k_kaolafm.R.string;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.live.player.HomeLiveManager;
import com.kaolafm.kradio.live.player.RecorderStatus;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;


public final class RecordButtonBoxLayout extends ConstraintLayout {
   private ImageButton mRecordButton;
   private TextView recordTipText;
   private ViewGroup mListenButton;
   private YunTingMusicPlayAnimationView mListenButtonAnim;
   
   private ViewGroup recordTextViewParent;
   private ImageView recordTextIv;
   private TextView recordTextView;
   private ViewGroup mCancelButton;
   
   private final List<View> visiableViews;
   private int mMeasureSpec;
   private final Runnable mChangeToIdleRunnable;

   public RecordButtonBoxLayout(@NotNull Context context) {
      this(context,null);
   }

   public RecordButtonBoxLayout(@NotNull Context context, @Nullable AttributeSet attrs) {
      this(context, attrs,0);

   }

   public RecordButtonBoxLayout(@NotNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
      super(context, attrs, defStyleAttr);
      boolean var4 = false;
      this.visiableViews = (List)(new ArrayList());
      LayoutInflater.from(this.getContext()).inflate(layout.online_player_record_button_popup_window, (ViewGroup)this);
      View var10001 = this.findViewById(id.recordIv);
      this.mRecordButton = (ImageButton)var10001;
      var10001 = this.findViewById(id.recordTipText);
      this.recordTipText = (TextView)var10001;
      var10001 = this.findViewById(id.live_listen_button_layout);
      this.mListenButton = (ViewGroup)var10001;
      var10001 = this.findViewById(id.live_listen_anim_image);
      this.mListenButtonAnim = (YunTingMusicPlayAnimationView)var10001;
      var10001 = this.findViewById(id.recordTextViewParent);
      this.recordTextViewParent = (ViewGroup)var10001;
      var10001 = this.findViewById(id.recordTextIv);
      this.recordTextIv = (ImageView)var10001;
      var10001 = this.findViewById(id.recordTextView);
      this.recordTextView = (TextView)var10001;
      var10001 = this.findViewById(id.live_cancel_button_layout);
      this.mCancelButton = (ViewGroup)var10001;
      this.mMeasureSpec = MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED);
      this.mChangeToIdleRunnable = (Runnable)(new Runnable() {
         public final void run() {
            RecordButtonBoxLayout.access$getMRecordButton$p(RecordButtonBoxLayout.this).setVisibility(View.VISIBLE);
            RecordButtonBoxLayout.access$getRecordTipText$p(RecordButtonBoxLayout.this).setText((CharSequence)ResUtil.getString(string.online_player_tip_record_idle));
            RecordButtonBoxLayout.access$getRecordTextViewParent$p(RecordButtonBoxLayout.this).setVisibility(View.INVISIBLE);
            HomeLiveManager.getInstance().setRecorderStatus(RecorderStatus.IDLE);
         }
      });
   }

   public final void setOnRecordButtonClickListener(@NotNull OnClickListener listener) {
      ImageButton var10000 = this.mRecordButton;
      var10000.setOnClickListener(listener);
   }

   public final void setOnListenButtonClickListener(@NotNull OnClickListener listener) {
      ViewGroup var10000 = this.mListenButton;
      var10000.setOnClickListener(listener);
   }

   public final void setOnCancelButtonClickListener(@NotNull OnClickListener listener) {
      ViewGroup var10000 = this.mCancelButton;
      var10000.setOnClickListener(listener);
   }

   public final void setOnSendButtonClickListener(@NotNull OnClickListener listener) {
      ViewGroup var10000 = this.recordTextViewParent;
      var10000.setOnClickListener(listener);
   }

   public final void notifyStartRecord() {
      ImageButton var10000 = this.mRecordButton;
      var10000.setVisibility(View.INVISIBLE);
      TextView var1 = this.recordTipText;
      var1.setText((CharSequence)ResUtil.getString(string.online_player_tip_recording));
      ImageView var2 = this.recordTextIv;
      var2.setImageResource(drawable.online_player_recording);
      ViewGroup var3 = this.recordTextViewParent;
      var3.setVisibility(View.VISIBLE);
   }

   public final void stopRecordingAnim(boolean isRecordFinish) {
      ImageButton var10000 = this.mRecordButton;
      var10000.setImageResource(drawable.online_player_record_mic);
      TextView var2 = this.recordTipText;
      byte var10001;
      if (isRecordFinish) {
         var10001 = 4;
      } else {
         TextView var3 = this.recordTipText;
         var3.setText((CharSequence)ResUtil.getString(string.online_player_tip_record_idle));
         var10001 = 0;
      }

      var2.setVisibility(var10001);
   }

   public final void updateRecordText(@NotNull String string) {
      TextView var10000 = this.recordTextView;
      var10000.setText((CharSequence)string);
      if (StringUtil.isEmpty(string)) {
         ImageView var2 = this.recordTextIv;
         var2.setImageResource(drawable.online_player_send);
      }

   }

   public final void notifyUserLoginStatus(boolean logged) {
      TextView var10000 = this.recordTipText;
      var10000.setText(logged ? (CharSequence)ResUtil.getString(string.online_player_tip_record_idle) : (CharSequence)ResUtil.getString(string.online_player_tip_record_unlogin));
   }

   public final void hideRecordButton() {
      this.resetVisibilityViews();
      ViewGroup var10000 = this.mListenButton;
      var10000.setVisibility(View.INVISIBLE);
      var10000 = this.mCancelButton;
      var10000.setVisibility(View.INVISIBLE);
      ImageButton var1 = this.mRecordButton;

      var1.setVisibility(View.INVISIBLE);
      TextView var2 = this.recordTipText;

      var2.setVisibility(View.INVISIBLE);
      var10000 = this.recordTextViewParent;
      var10000.setVisibility(View.INVISIBLE);
   }

   private final void resetVisibilityViews() {
      this.visiableViews.clear();
      if (mListenButton.getVisibility() == View.VISIBLE) {
         visiableViews.add(mListenButton);
      }
      if (mCancelButton.getVisibility() == View.VISIBLE) {
         visiableViews.add(mCancelButton);
      }
      if (mRecordButton.getVisibility() == View.VISIBLE) {
         visiableViews.add(mRecordButton);
      }
      if (recordTipText.getVisibility() == View.VISIBLE) {
         visiableViews.add(recordTipText);
      }
      if (recordTextViewParent.getVisibility() == View.VISIBLE) {
         visiableViews.add(recordTextViewParent);
      }
   }

   public final void startRecordFinishAnim() {
      ImageButton var10000 = this.mRecordButton;
      int recordWidth = var10000.getMeasuredWidth();
      var10000 = this.mRecordButton;
      int middle = var10000.getLeft() + recordWidth / 2;
      ViewGroup var14 = this.mCancelButton;
      int cancelLeft = var14.getLeft();
      var14 = this.mListenButton;
      int listenLeft = var14.getLeft();
      AnimatorSet animSet = new AnimatorSet();
      animSet.addListener((AnimatorListener)(new AnimatorListener() {
         public void onAnimationStart(@NotNull Animator animation) {
            RecordButtonBoxLayout.access$getMCancelButton$p(RecordButtonBoxLayout.this).setVisibility(View.VISIBLE);
            RecordButtonBoxLayout.access$getMListenButton$p(RecordButtonBoxLayout.this).setVisibility(View.VISIBLE);
         }

         public void onAnimationEnd(@NotNull Animator animation) {
         }

         public void onAnimationCancel(@NotNull Animator animation) {
         }

         public void onAnimationRepeat(@NotNull Animator animation) {
         }
      }));
      int animDuration = 300;
      PropertyValuesHolder pvhr = PropertyValuesHolder.ofFloat("x", new float[]{(float)middle, (float)cancelLeft});
      PropertyValuesHolder pvhl = PropertyValuesHolder.ofFloat("x", new float[]{(float)middle, (float)listenLeft});
      PropertyValuesHolder pvha = PropertyValuesHolder.ofFloat("alpha", new float[]{0.0F, 1.0F});
      var14 = this.mListenButton;
      ObjectAnimator var15 = ObjectAnimator.ofPropertyValuesHolder(var14, new PropertyValuesHolder[]{pvhl});
      ObjectAnimator oali = var15;
      oali.setDuration((long)animDuration);
      var14 = this.mListenButton;
      var15 = ObjectAnimator.ofPropertyValuesHolder(var14, new PropertyValuesHolder[]{pvha});
      ObjectAnimator oala = var15;
      oala.setDuration((long)animDuration);
      var14 = this.mCancelButton;
      var15 = ObjectAnimator.ofPropertyValuesHolder(var14, new PropertyValuesHolder[]{pvhr});
      ObjectAnimator oari = var15;
      oari.setDuration((long)animDuration);
      var14 = this.mCancelButton;
      var15 = ObjectAnimator.ofPropertyValuesHolder(var14, new PropertyValuesHolder[]{pvha});
      ObjectAnimator oara = var15;
      oala.setDuration((long)animDuration);
      animSet.playTogether(new Animator[]{(Animator)oali, (Animator)oari, (Animator)oala, (Animator)oara});
      animSet.setInterpolator((TimeInterpolator)(new DecelerateInterpolator()));
      animSet.start();
   }

   public final void notifyCancel() {
      ViewGroup var10000 = this.mCancelButton;
      var10000.setVisibility(View.INVISIBLE);
      TextView var1 = this.recordTipText;
      var1.setText((CharSequence)ResUtil.getString(string.online_player_tip_record_idle));
      var1 = this.recordTipText;
      var1.setVisibility(View.VISIBLE);
      var10000 = this.mListenButton;
      var10000.setVisibility(View.INVISIBLE);
   }

   public final void showRecordIdle() {
      ViewGroup var10000 = this.mCancelButton;
      var10000.setVisibility(View.INVISIBLE);
      var10000 = this.mListenButton;
      var10000.setVisibility(View.INVISIBLE);
      ImageButton var1 = this.mRecordButton;
      var1.setVisibility(View.VISIBLE);
      TextView var2 = this.recordTipText;
      var2.setText((CharSequence)ResUtil.getString(string.online_player_tip_record_idle));
      var2 = this.recordTipText;

      var2.setVisibility(View.VISIBLE);
      var10000 = this.recordTextViewParent;
      var10000.setVisibility(View.INVISIBLE);
   }

   public final void showRecordUploading() {
      ViewGroup var10000 = this.mCancelButton;
      var10000.setVisibility(View.INVISIBLE);
      var10000 = this.mListenButton;
     
      var10000.setVisibility(View.INVISIBLE);
      ImageView var1 = this.recordTextIv;
     
      var1.setImageResource(drawable.online_player_sending);
   }

   public final void showRecordUploaded() {
      ImageView var10000 = this.recordTextIv;
     
      var10000.setImageResource(drawable.online_player_send_finish);
      ImageButton var1 = this.mRecordButton;

      var1.removeCallbacks(this.mChangeToIdleRunnable);
      var1 = this.mRecordButton;
      var1.postDelayed(this.mChangeToIdleRunnable, 1000L);
   }

   public final void showRecordUploadAgainAsFailure() {
      ViewGroup var10000 = this.mCancelButton;
    
      var10000.setVisibility(View.VISIBLE);
      var10000 = this.mListenButton;
    
      var10000.setVisibility(View.VISIBLE);
      ImageView var1 = this.recordTextIv;
    
      var1.setImageResource(drawable.online_player_resend);
      ImageButton var2 = this.mRecordButton;
      var2.setVisibility(View.INVISIBLE);
      TextView var3 = this.recordTipText;
      var3.setVisibility(View.INVISIBLE);
      var10000 = this.recordTextViewParent;
      var10000.setVisibility(View.VISIBLE);
   }

   public final void startListenTimer() {
      YunTingMusicPlayAnimationView var10000 = this.mListenButtonAnim;

      var10000.startAnimation();
      var10000 = this.mListenButtonAnim;

      var10000.setVisibility(View.VISIBLE);
   }

   public final void stopListenTimer() {
      YunTingMusicPlayAnimationView var10000 = this.mListenButtonAnim;
      var10000.setVisibility(View.INVISIBLE);
      var10000 = this.mListenButtonAnim;
      var10000.stopAnimation();
   }


   // $FF: synthetic method
   public static final ViewGroup access$getMCancelButton$p(RecordButtonBoxLayout $this) {
      ViewGroup var10000 = $this.mCancelButton;
      return var10000;
   }

   // $FF: synthetic method
   public static final void access$setMCancelButton$p(RecordButtonBoxLayout $this, ViewGroup var1) {
      $this.mCancelButton = var1;
   }

   // $FF: synthetic method
   public static final ViewGroup access$getMListenButton$p(RecordButtonBoxLayout $this) {
      ViewGroup var10000 = $this.mListenButton;
      return var10000;
   }

   // $FF: synthetic method
   public static final void access$setMListenButton$p(RecordButtonBoxLayout $this, ViewGroup var1) {
      $this.mListenButton = var1;
   }

   // $FF: synthetic method
   public static final ImageButton access$getMRecordButton$p(RecordButtonBoxLayout $this) {
      ImageButton var10000 = $this.mRecordButton;
      return var10000;
   }

   // $FF: synthetic method
   public static final void access$setMRecordButton$p(RecordButtonBoxLayout $this, ImageButton var1) {
      $this.mRecordButton = var1;
   }

   // $FF: synthetic method
   public static final TextView access$getRecordTipText$p(RecordButtonBoxLayout $this) {
      TextView var10000 = $this.recordTipText;
      return var10000;
   }

   // $FF: synthetic method
   public static final void access$setRecordTipText$p(RecordButtonBoxLayout $this, TextView var1) {
      $this.recordTipText = var1;
   }

   // $FF: synthetic method
   public static final ViewGroup access$getRecordTextViewParent$p(RecordButtonBoxLayout $this) {
      ViewGroup var10000 = $this.recordTextViewParent;
      return var10000;
   }

   // $FF: synthetic method
   public static final void access$setRecordTextViewParent$p(RecordButtonBoxLayout $this, ViewGroup var1) {
      $this.recordTextViewParent = var1;
   }
}
