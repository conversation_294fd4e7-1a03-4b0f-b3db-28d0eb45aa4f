package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.util.Log;

import com.kaolafm.kradio.lib.base.flavor.ShutdownLogicListener;
import com.kaolafm.kradio.service.BYDWidgetService;

import cmgyunting.vehicleplayer.cnr.YunTingWidgetService;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/3/12.
 */

public class ShutdownLogic implements ShutdownLogicListener {
    @Override
    public boolean onShutdownLogic(Context context, Intent i) {
        Intent intent = new Intent(context, YunTingWidgetService.class);
        Log.i("ShutdownLogic","ShutdownLogic bydstartTest : ");
        if (i != null && i.getBooleanExtra("from_quickboot",false)) {
            SyncInstrumentImpl.isFormShutDown = true;
            intent.setAction(YunTingWidgetService.WIDGET_ACTION_PAUSE);
        } else {
            intent.setAction(YunTingWidgetService.WIDGET_ACTION_REFRESH);
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.startForegroundService(intent);
        } else {
            context.startService(intent);
        }
        return true;
    }
}
