package com.kaolafm.kradio.component.ui.base.cell;


import com.kaolafm.opensdk.api.operation.model.ImageFile;
import com.kaolafm.opensdk.api.operation.model.column.ColumnContent;

import java.util.List;
import java.util.Map;

/**
 * 基本数据类型
 *
 * <AUTHOR>
 * @date 2019-08-14
 */
public abstract class BaseCell implements Cell {

    /**
     * 所属栏目id
     */
    public String columnId;
    public String name;

    /**
     * 推荐理由
     */
    public String recommendReason;
    /**
     * 直播节目主播
     */
    public String anchor;

    public String code;

    public Card parent;

    public String imageUrl;

    public long playId;

    public long belongingId;//（所属专辑或者电台或者广播的Id）

    public int positionInParent;

    public int resType;

    public int contentType;//"id"  "name”(“6”  "交通台”;”7”  "经济台”;”8”  "新闻台”;”9”  "音乐台”;"10"  "校园台";”11"  "娱乐台";"12"  "方言台";"13"  "曲艺台";"14"  "外语台";"15"  "文艺台";"16"  "旅游台";"17"  "体育台;"18"  "生活台";"19"  "都市台";"20"  "综合台";"21"  "民族台”)

    public boolean selected;

    /**
     * 是否精品  1:是,0:否
     */
    public int fine;

    /**
     * 是否vip 1:是,0:否
     */
    public int vip;

    public String freq;
    /**
     * 组件类型
     * 1  上2下1组件
     * 2  轮播组件
     * 3  上2下3组件
     * 4  上1下1组件
     * 5  单内容大卡组件
     * 6  首页福利活动组件
     * 7  品牌入口组件
     * 10    圆形组件
     * 11    话题大卡组件
     * 12    话题小卡组件
     * 13    品牌主页大卡
     * 14    品牌主页 1+1
     * 15    活动类型组件
     */
    public int componentType;
    /**
     * 成员标签
     */
    public String memberTag;

    /**
     * 图片信息集合
     */
    public Map<String, ImageFile> imageFiles;
    /**
     * 组件内容区域
     */
        public List<ColumnContent> contentList;

    public String getMemberTag() {
        return memberTag;
    }

    public void setMemberTag(String memberTag) {
        this.memberTag = memberTag;
    }

    public int getComponentType() {
        return componentType;
    }

    public void setComponentType(int componentType) {
        this.componentType = componentType;
    }

    public List<ColumnContent> getContentList() {
        return contentList;
    }

    public String getColumnId() {
        return columnId;
    }

    public void setColumnId(String columnId) {
        this.columnId = columnId;
    }

    public void setContentList(List<ColumnContent> contentList) {
        this.contentList = contentList;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRecommendReason() {
        return recommendReason;
    }

    public void setRecommendReason(String recommendReason) {
        this.recommendReason = recommendReason;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Card getParent() {
        return parent;
    }

    public void setParent(Card parent) {
        this.parent = parent;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public long getPlayId() {
        return playId;
    }

    public void setPlayId(long playId) {
        this.playId = playId;
    }

    public long getBelongingId() {
        return belongingId;
    }

    public void setBelongingId(long belongingId) {
        this.belongingId = belongingId;
    }

    public int getPositionInParent() {
        return positionInParent;
    }

    public void setPositionInParent(int positionInParent) {
        this.positionInParent = positionInParent;
    }

    public int getResType() {
        return resType;
    }

    public void setResType(int resType) {
        this.resType = resType;
    }

    public int getContentType() {
        return contentType;
    }

    public void setContentType(int contentType) {
        this.contentType = contentType;
    }

    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }

    public int getFine() {
        return fine;
    }

    public void setFine(int fine) {
        this.fine = fine;
    }

    public int getVip() {
        return vip;
    }

    public void setVip(int vip) {
        this.vip = vip;
    }

    public String getFreq() {
        return freq;
    }

    public void setFreq(String freq) {
        this.freq = freq;
    }

    public String getAnchor() {
        return anchor;
    }

    public void setAnchor(String anchor) {
        this.anchor = anchor;
    }

    public boolean isVip() {
        return vip == 1;
    }

    public boolean isFine() {
        return fine == 1;
    }

    public Map<String, ImageFile> getImageFiles() {
        return imageFiles;
    }

    public void setImageFiles(Map<String, ImageFile> imageFiles) {
        this.imageFiles = imageFiles;
    }
}
