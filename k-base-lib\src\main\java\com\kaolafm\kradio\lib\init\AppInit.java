package com.kaolafm.kradio.lib.init;

import android.app.Application;
import androidx.annotation.IntRange;
import com.kaolafm.kradio.lib.init.Process.ProcessValue;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 初始化注解。可以设置各种初始化相关的属性<br>
 * {@link #priority()}——设置优先级， 1-100，越大优先级越高。默认50。<br>
 * {@link #process()}——执行的进程，主进程{@link Process#MAIN}、所有进程{@link Process#ALL}、其他非主进程{@link Process#OTHER}。默认主进程<br>
 * {@link #description()}—— 描述，可以是任意字符串。没有实际功能上的作用，只是用于说明初始化什么的，可用于调试。<br>
 * {@link #isAsync()}——是否使用异步初始化，默认不使用。只有true的时候才会回调{@link AppInitializable#asyncCreate(Application)}方法。<br>
 * {@link #dependOn()}——依赖项，需要依赖于某一个或多个初始化完成后才能初始化。字符串为所依赖的类的全路径名称。<br>
 * <AUTHOR> Yan
 * @date 2019-09-10
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface AppInit {

    /**
     * 优先级，1-100，越大优先级越高。默认50
     * @return
     */
    @IntRange(from = 0, to = 100)
    int priority() default 50;

    /**
     * 执行的进程，主进程{@link Process#MAIN}、所有进程{@link Process#ALL}、其他非主进程{@link Process#OTHER}。默认主进程
     * @return
     */
    @ProcessValue
    int process() default Process.MAIN;

    /**
     * 描述，可以是任意字符串。没有实际功能上的作用，只是用于说明初始化什么的，可用于调试。
     * @return
     */
    String description() default "";

    /**
     * 是否使用异步初始化，默认不使用。
     * @return
     */
    boolean isAsync() default false;

    /**
     * 依赖项，需要依赖于某一个或多个初始化完成后才能初始化。字符串为所依赖的类的全路径名称。
     * @return
     */
    String[] dependOn() default {};

}
