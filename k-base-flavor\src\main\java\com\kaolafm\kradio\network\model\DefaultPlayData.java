package com.kaolafm.kradio.network.model;

import java.util.List;

/**
 * <pre>
 *     <AUTHOR> Wenchl
 *     time   : 2018/09/04
 *     desc   :
 *     version: 1.0
 * </pre>
 */
public class DefaultPlayData {


    /**
     * id : 1200000000510
     * name : 启辰Radio
     * img : http://img.kaolafm.net/mz/images/201809/12178abb-4227-4afe-9834-3f7839317732/default.jpg
     * followedNum : 0
     * isOnline : 1
     * listenNum : 0
     * desc : 启辰Radio
     * commentNum : 0
     * isSubscribe : 0
     * type : 3
     * host : []
     * keyWords : []
     */

    private long id;
    private String name;
    private String img;
    private int followedNum;
    private int isOnline;
    private int listenNum;
    private String desc;
    private int commentNum;
    private int isSubscribe;
    private int type;
    private List<?> host;
    private List<?> keyWords;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public int getFollowedNum() {
        return followedNum;
    }

    public void setFollowedNum(int followedNum) {
        this.followedNum = followedNum;
    }

    public int getIsOnline() {
        return isOnline;
    }

    public void setIsOnline(int isOnline) {
        this.isOnline = isOnline;
    }

    public int getListenNum() {
        return listenNum;
    }

    public void setListenNum(int listenNum) {
        this.listenNum = listenNum;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public int getCommentNum() {
        return commentNum;
    }

    public void setCommentNum(int commentNum) {
        this.commentNum = commentNum;
    }

    public int getIsSubscribe() {
        return isSubscribe;
    }

    public void setIsSubscribe(int isSubscribe) {
        this.isSubscribe = isSubscribe;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public List<?> getHost() {
        return host;
    }

    public void setHost(List<?> host) {
        this.host = host;
    }

    public List<?> getKeyWords() {
        return keyWords;
    }

    public void setKeyWords(List<?> keyWords) {
        this.keyWords = keyWords;
    }

}
