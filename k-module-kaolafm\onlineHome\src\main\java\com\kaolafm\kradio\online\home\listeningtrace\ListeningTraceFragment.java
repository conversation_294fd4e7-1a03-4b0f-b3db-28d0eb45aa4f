package com.kaolafm.kradio.online.home.listeningtrace;

import android.graphics.Color;
import android.os.Bundle;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;
import android.util.Log;
import android.view.View;
import android.view.ViewStub;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.common.widget.NotScrollViewPager;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.flavor.AllCategoriesCustomTitleInter;
import com.kaolafm.kradio.lib.base.mvp.IPresenter;
import com.kaolafm.kradio.lib.base.ui.online.BaseShowHideLazyFragment;
import com.kaolafm.kradio.lib.base.ui.online.BaseViewPagerFragment;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.widget.tab.OnTabSelectListener;
import com.kaolafm.kradio.lib.widget.tab.SlidingTabLayout;
import com.kaolafm.kradio.lib.widget.tab.Tab;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.online.history.ui.OnlineHistoryFragment;
import com.kaolafm.kradio.online.subscriptions.ui.OnlineSubscriptionsFragment;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonClickReportEvent;
import com.kaolafm.kradio.online.categories.BaseBackFragment;
import com.trello.rxlifecycle3.LifecycleTransformer;

import java.util.ArrayList;
import java.util.List;

/**
 * 听迹
 *
 * <AUTHOR> shiqian
 * @date 2022-08-09
 */
@Route(path = RouterConstance.ONLINE_URL_LISTENING_TRACE)
public class ListeningTraceFragment extends BaseShowHideLazyFragment {
    private String TAG = "ListeningTraceFragment";

    private String[] titles;
    private List<Fragment> fragmentArray = new ArrayList<>();
    private ListeningTraceFragmentAdapter listeningTraceFragmentAdapter;

    View vRootCate;
    View loading;
    SlidingTabLayout mTabLayout;
    NotScrollViewPager mViewPager;
    OnlineSubscriptionsFragment onlineSubscriptionsFragment;
    OnlineHistoryFragment onlineHistoryFragment;
//    private int tabPosition = 0;

    private String mTargetChildFragmentPageId;//子Fragment中要求选中的子Fragment的PageId

    @Override
    protected int getLayoutId() {
        return R.layout.online_fragment_listening_trace_content;
    }

    @Override
    protected IPresenter createPresenter() {
        return null;
    }

    @Override
    protected boolean isReportFragment() {
        return true;
    }

    @Override
    protected void onVisibleChanged(boolean isVisible) {
        super.onVisibleChanged(isVisible);
        if (mTabLayout.getCurrentPosition() == 0) {
            onlineSubscriptionsFragment.setUserVisibleHint(isVisible);
        } else {
            onlineHistoryFragment.setUserVisibleHint(isVisible);
        }

    }

    @Override
    public void initView(View view) {
        Log.i(TAG, "initView: ");
        mTabLayout = view.findViewById(R.id.stb_all_category_title_name);
        mTabLayout.setTabWidth(ResUtil.getDimen(R.dimen.m176));
        changeTabTextSize(true);
        vRootCate = view.findViewById(R.id.vRootCate);
        loading = view.findViewById(R.id.loading);
        mViewPager = view.findViewById(R.id.vp_all_category_content);

        mViewPager.setOffscreenPageLimit(0);
        mViewPager.setScanScroll(false);
        mViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int i, float v, int i1) {

            }

            @Override
            public void onPageSelected(int i) {
                mTabLayout.setCurrentTab(i);
                changeTabState(i);
            }

            @Override
            public void onPageScrollStateChanged(int i) {

            }
        });
        mTabLayout.setOnTabSelectListener(new OnTabSelectListener() {
            @Override
            public void onTabSelect(int position) {
                NetworkUtil.isNetworkAvailable(getContext(), true);
                mViewPager.setCurrentItem(position, true);
                changeTabState(position);
                //数据上报
//                ButtonClickReportEvent event;
//                switch (mTabLayout.getTab(position).title) {
//                    case "我的订阅":
//                        event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_TJ_MY_ADD);
//                        ReportHelper.getInstance().addEvent(event);
//                        break;
//                    case "收听历史":
//                        event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_TJ_HISTORY);
//                        ReportHelper.getInstance().addEvent(event);
//                        break;
//                }
            }

            @Override
            public void onTabReselect(int position) {
                changeTabState(position);
            }
        });

        initData();
    }

    @Override
    protected void showAccordingToScreen(int orientation) {
        super.showAccordingToScreen(orientation);
    }

    private void initData() {
        titles = new String[]{"我的订阅", "收听历史"};
        Bundle bundle = new Bundle();
        onlineSubscriptionsFragment = new OnlineSubscriptionsFragment();
        bundle.putString(BaseViewPagerFragment.ARGUMENT_TAG, titles[0]);
        onlineSubscriptionsFragment.setArguments(bundle);
        fragmentArray.add(onlineSubscriptionsFragment);
        bundle = new Bundle();
        bundle.putString(BaseViewPagerFragment.ARGUMENT_TAG, titles[1]);
        onlineHistoryFragment = new OnlineHistoryFragment();
        onlineHistoryFragment.setArguments(bundle);
        fragmentArray.add(onlineHistoryFragment);
        listeningTraceFragmentAdapter = new ListeningTraceFragmentAdapter(getChildFragmentManager(), fragmentArray, titles);

        int selectTabIndex = 0;
        if (mTargetChildFragmentPageId != null) {
            switch (String.valueOf(mTargetChildFragmentPageId)) {
                case Constants.PAGE_ID_LISTENING_TRACE_SUBSCRIBE_ALBUM: //听迹--我的订阅--专辑/AI电台tab页面
                case Constants.PAGE_ID_LISTENING_TRACE_SUBSCRIBE_SONG:  //听迹--我的订阅--单曲tab页面
                case Constants.PAGE_ID_LISTENING_TRACE_SUBSCRIBE_TV:    //听迹--我的订阅--广播/电视tab页面
                    onlineSubscriptionsFragment.selectChildFragment(mTargetChildFragmentPageId);
                    mTargetChildFragmentPageId = null;
                    break;
                case Constants.PAGE_ID_LISTENING_TRACE_HISTORY: //听迹--收听历史页面
                    mTargetChildFragmentPageId = null;
                    selectTabIndex = 1;
                    break;
                default:
                    break;
            }
        }
        if (titles != null && titles.length > 0) {
            List<Tab> tabs = new ArrayList<>();
            AllCategoriesCustomTitleInter inter = ClazzImplUtil.getInter("AllCategoriesCustomTitleImpl");

            for (int i = 0; i < titles.length; i++) {
                Tab tab = new Tab();
                if (null != inter) {
                    tab.title = inter.TitleCustom(titles[i]);
                } else {
                    tab.title = titles[i];
                }
                tab.position = i;
                tab.select = i == selectTabIndex;
                tabs.add(tab);
            }
            mTabLayout.setTabs(tabs);
        }
        mViewPager.setAdapter(listeningTraceFragmentAdapter);

        mTabLayout.setCurrentTab(selectTabIndex);
        mViewPager.setCurrentItem(selectTabIndex);
        changeTabState(selectTabIndex);
    }


    public void changeTabState(int position) {
        for (int i = 0; i < titles.length; i++) {
            if (i == position) {
                mTabLayout.getTab(i).tabView.getPaint().setFakeBoldText(true);
                mTabLayout.getTab(i).tabView.setBackground(ResUtil.getDrawable(R.drawable.online_tab_class_all_indicator_bg));
            } else {
                mTabLayout.getTab(i).tabView.getPaint().setFakeBoldText(false);
                mTabLayout.getTab(i).tabView.setBackgroundColor(Color.TRANSPARENT);
            }
        }
    }

    /**
     * 修改标题字体大小
     *
     * @param needChange boolean值
     */
    public void changeTabTextSize(boolean needChange) {
        if (needChange) {
            int textSize = ResUtil.getDimen(R.dimen.online_nav_bar_tv_unselected);
            int textSizeSelected = ResUtil.getDimen(R.dimen.online_category_nav_bar_tv_selected);
            mTabLayout.setTextSize(textSize);
            mTabLayout.setTextSelectedSize(textSizeSelected);
            Log.i(TAG, "changeTabTextSize:" + textSize + " " + textSizeSelected);
        }
    }

    public void selectChildFragment(String pageId) {
        boolean isSelectSubscriptionFragment = false;
        switch (pageId) {
            case Constants.PAGE_ID_LISTENING_TRACE_SUBSCRIBE_ALBUM: //听迹--我的订阅--专辑/AI电台tab页面
            case Constants.PAGE_ID_LISTENING_TRACE_SUBSCRIBE_SONG:  //听迹--我的订阅--单曲tab页面
            case Constants.PAGE_ID_LISTENING_TRACE_SUBSCRIBE_TV:    //听迹--我的订阅--广播/电视tab页面
                mTargetChildFragmentPageId = pageId;
                isSelectSubscriptionFragment = true;
                break;
            case Constants.PAGE_ID_LISTENING_TRACE_HISTORY: //听迹--收听历史页面
                mTargetChildFragmentPageId = pageId;
                break;
            default:
                mTargetChildFragmentPageId = null;
                break;
        }
        if (ListUtil.isEmpty(fragmentArray) || mTargetChildFragmentPageId == null) {
            return;
        }
        if (isSelectSubscriptionFragment) {
            if (fragmentArray.size() > 0) {
                if (mTabLayout.getTabCount() > 0)
                    mTabLayout.getTab(0).tabView.performClick();
                ((OnlineSubscriptionsFragment) (fragmentArray.get(0))).selectChildFragment(mTargetChildFragmentPageId);
                mTargetChildFragmentPageId = null;
            }
        } else {
            if (mTabLayout.getTabCount() > 1) {
                mTabLayout.getTab(1).tabView.performClick();
                mTargetChildFragmentPageId = null;
            }
        }
    }

    @Override
    protected void lazyLoad() {

    }

}
