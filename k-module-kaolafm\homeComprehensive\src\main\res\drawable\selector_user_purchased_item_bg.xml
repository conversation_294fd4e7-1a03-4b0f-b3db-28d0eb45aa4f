<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_selected="true">
        <shape>
            <corners android:bottomRightRadius="@dimen/default_radius_img" android:topRightRadius="@dimen/default_radius_img" />
            <gradient android:angle="270" android:endColor="#ff2b4369" android:startColor="#ff4d5e99" android:type="linear" />
        </shape>
    </item>
    <item>
        <shape>
            <corners android:bottomRightRadius="@dimen/default_radius_img" android:topRightRadius="@dimen/default_radius_img" />
            <solid android:color="@color/bg_item_no_play" />
        </shape>
    </item>
</selector>