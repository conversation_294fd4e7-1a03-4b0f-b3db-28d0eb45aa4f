package com.ecarx.sdk;

import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 **/
public class AccessTokenResult {

    /**
     * accessToken : ACCESS_TOKEN
     * expiresIn : 120
     * openId : OPENID
     * refreshToken : REFRESH_TOKEN
     * scope : SCOPE
     */

    @SerializedName("accessToken")
    private String accessToken;
    @SerializedName("expiresIn")
    private int expiresIn;
    @SerializedName("openId")
    private String openId;
    @SerializedName("refreshToken")
    private String refreshToken;
    @SerializedName("scope")
    private String scope;

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public int getExpiresIn() {
        return expiresIn;
    }

    public void setExpiresIn(int expiresIn) {
        this.expiresIn = expiresIn;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public String getScope() {
        return scope;
    }

    public void setScope(String scope) {
        this.scope = scope;
    }

    @Override
    public String toString() {
        return "AccessTokenResult{" +
                "accessToken='" + accessToken + '\'' +
                ", expiresIn=" + expiresIn +
                ", openId='" + openId + '\'' +
                ", refreshToken='" + refreshToken + '\'' +
                ", scope='" + scope + '\'' +
                '}';
    }
}
