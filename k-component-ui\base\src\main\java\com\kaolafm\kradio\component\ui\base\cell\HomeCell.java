package com.kaolafm.kradio.component.ui.base.cell;

/**
 * 首页数据基本类型。
 * <AUTHOR>
 * @date 2019-08-16
 */
public class HomeCell extends BaseCell {
    public String firstCode = "0";

    public String secondCode = "0";
    public int itemType;

    public String outputMode;

    public String callBack;

    public boolean isLastInParent;

    public int iImageAd;

    public HomeCell(){
        iImageAd = -1;
    }

    public String getFirstCode() {
        return firstCode;
    }

    public void setFirstCode(String firstCode) {
        this.firstCode = firstCode;
    }

    public String getSecondCode() {
        return secondCode;
    }

    public void setSecondCode(String secondCode) {
        this.secondCode = secondCode;
    }

    @Override
    public int getItemType() {
        return itemType;
    }

    @Override
    public int spanSize() {
        return 1;
    }

    public void setItemType(int itemType) {
        this.itemType = itemType;
    }

    public String getOutputMode() {
        return outputMode;
    }

    public void setOutputMode(String outputMode) {
        this.outputMode = outputMode;
    }

    public String getCallBack() {
        return callBack;
    }

    public void setCallBack(String callBack) {
        this.callBack = callBack;
    }

    public boolean isLastInParent() {
        return isLastInParent;
    }

    public void setLastInParent(boolean lastInParent) {
        isLastInParent = lastInParent;
    }

    public int getiImageAd() {
        return iImageAd;
    }

    public void setiImageAd(int iImageAd) {
        this.iImageAd = iImageAd;
    }

    @Override
    public String toString() {
        return "HomeCell{" +
                "firstCode='" + firstCode + '\'' +
                ", secondCode='" + secondCode + '\'' +
                ", itemType=" + itemType +
                ", outputMode='" + outputMode + '\'' +
                ", callBack='" + callBack + '\'' +
                ", isLastInParent=" + isLastInParent +
                ", iImageAd=" + iImageAd +
                ", name='" + name + '\'' +
                ", recommendReason='" + recommendReason + '\'' +
                ", code='" + code + '\'' +
                ", parent=" + parent +
                ", imageUrl='" + imageUrl + '\'' +
                ", playId=" + playId +
                ", belongingId=" + belongingId +
                ", positionInParent=" + positionInParent +
                ", resType=" + resType +
                ", contentType=" + contentType +
                ", selected=" + selected +
                ", fine=" + fine +
                ", vip=" + vip +
                ", freq='" + freq + '\'' +
                '}';
    }

    /**
     * 释放资源，在ViewHolder回收时调用
     * 子类可以重写此方法来清理特定的资源
     */
    public void release() {
        // 基类默认实现为空，子类可以重写
    }
}
