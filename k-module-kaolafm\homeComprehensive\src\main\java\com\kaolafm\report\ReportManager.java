package com.kaolafm.report;


import android.os.SystemClock;
import android.text.TextUtils;

import com.kaolafm.base.utils.DeviceUtil;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.lib.report.Reporter;
import com.kaolafm.kradio.common.utils.AppInfoUtil;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.player.utils.PlayItemConstantsSon;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.PlayItemConstants;
import com.kaolafm.opensdk.player.logic.model.item.BroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.LivePlayItem;
import com.kaolafm.opensdk.player.logic.model.item.LiveStreamPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.TVPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.report.event.AppComponentShowAndClickEvent;
import com.kaolafm.report.event.BufferEndReportEvent;
import com.kaolafm.report.event.BufferStartReportEvent;
import com.kaolafm.report.event.ContentClickReportEvent;
import com.kaolafm.report.event.ContentShowReportEvent;
import com.kaolafm.report.event.KaoLaFmToKRadioEvent;
import com.kaolafm.report.event.LivingLeaveMessageReportEvent;
import com.kaolafm.report.event.LivingStartListenReportEvent;
import com.kaolafm.report.event.LoginReportEvent;
import com.kaolafm.report.event.MessageClickReportEvent;
import com.kaolafm.report.event.MessageShowReportEvent;
import com.kaolafm.report.event.MinusFeedbackReportEvent;
import com.kaolafm.report.event.PageShowReportEvent;
import com.kaolafm.report.event.PlayerUiControlReportEvent;
import com.kaolafm.report.event.PlusFeedbackReportEvent;
import com.kaolafm.report.event.RecommendSelectReportEvent;
import com.kaolafm.report.event.RecommendShowReportEvent;
import com.kaolafm.report.event.RequetErrorReportEvent;
import com.kaolafm.report.event.SearchResultReportEvent;
import com.kaolafm.report.event.SearchResultSelectReportEvent;
import com.kaolafm.report.event.SettingVoiceSwitchEvent;
import com.kaolafm.report.event.SubscibeReportEvent;
import com.kaolafm.report.event.ToneSelectReportEvent;
import com.kaolafm.report.event.UpdateReportEvent;
import com.kaolafm.report.inner.InnerPlayReportParameter;
import com.kaolafm.report.util.ReportConstants;

import org.jetbrains.annotations.NotNull;

import java.util.List;

import io.reactivex.Single;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.functions.Consumer;
import io.reactivex.schedulers.Schedulers;
import tv.danmaku.ijk.media.player.IjkMediaPlayer;

/**
 * <AUTHOR> on 2019-07-24.
 */

public class ReportManager implements Reporter {

    private static volatile ReportManager reportManager;

    /**
     * 暂停所有数据上报.为了测试提供, 不要轻易修改
     */
    private boolean isCloseAllReport = false;

    /**
     * 记录上次position的获取时间，用以检测卡顿
     */
    private long mBufferStartTime = 0;

    /**
     * 当前碎片播放总时长 单位ms
     */
    private long mAudioPlayedTime;

    /**
     * 播放器是否触发了seek true为是，false为否
     */
    private boolean isSeekEvent;

    private long mCurrentPosition;

    /**
     * 当前的播放对象
     */
    private PlayItem mCurrentPlayItem;

    private ReportManager() {
        if (isCloseAllReport) {
            ReportHelper.getInstance().release();
            return;
        }
        initListener();
    }

    public static ReportManager getInstance() {
        if (reportManager == null) {
            synchronized (ReportManager.class) {
                if (reportManager == null) {
                    reportManager = new ReportManager();
                }
            }
        }
        ReportHelper.getInstance().initByApk();
        return reportManager;
    }

    /**
     * 初始playlistener
     */
    @Override
    public void initListener() {
        if (isCloseAllReport) {
            return;
        }
        if (ReportHelper.getInstance().isInitSuccess) {
            config();
        } else {
            ReportHelper.getInstance().setIReportInitListener(this::config);
        }
    }

    private void config() {
        String versionName = AppInfoUtil.getVersionName(AppDelegate.getInstance().getContext());
        String versionCode = String.valueOf(AppInfoUtil.getVersionCode(AppDelegate.getInstance().getContext()));
        ReportHelper.getInstance().setRealVersion(versionName, versionCode);
        ReportHelper.getInstance().initUpdateEvent(UpdateReportEvent.TYPE_UPDATE_BY_SELF);
    }


    @Override
    public void initPlayerListener() {
        if (isCloseAllReport) {
            return;
        }
        PlayerManager.getInstance().addPlayControlStateCallback(playerStateListenerWrapper);
    }

    BasePlayStateListener playerStateListenerWrapper = new BasePlayStateListener() {

        @Override
        public void onPlayerPreparing(PlayItem playItem) {
            super.onPlayerPreparing(playItem);
            resetReportPlayedTime();
        }

        @Override
        public void onPlayerPlaying(PlayItem playItem) {
            super.onPlayerPlaying(playItem);
            reportStartPlay(playItem);
        }


        @Override
        public void onProgress(PlayItem playItem, long progress, long duration) {
            if (mCurrentPosition != 0) {
                long playTime = progress - mCurrentPosition;
                if (playTime > 0) {
                    mAudioPlayedTime += playTime;
                }
                ReportHelper.getInstance().setPlayPosition(mAudioPlayedTime, duration);
            }
            mCurrentPosition = progress;
        }

        @Override
        public void onPlayerFailed(PlayItem playItem, int i, int i1) {
            super.onPlayerFailed(playItem, i, i1);
            reportRequestError(playItem, i, i1);
        }

        @Override
        public void onPlayerEnd(PlayItem playItem) {
            super.onPlayerEnd(playItem);
            reportEndPlay(ReportConstants.PLAY_CHANGE_BY_AUTO);
            resetReportPlayedTime();
        }

        @Override
        public void onSeekStart(PlayItem playItem) {
            isSeekEvent = true;
            resetReportPlayedTime();
        }

        @Override
        public void onSeekComplete(PlayItem playItem) {
            super.onSeekComplete(playItem);
        }

        @Override
        public void onBufferingStart(PlayItem playItem) {
            super.onBufferingStart(playItem);
            mBufferStartTime = SystemClock.elapsedRealtime();
            reportBufferingStart(playItem, isSeekEvent);
        }

        @Override
        public void onBufferingEnd(PlayItem playItem) {
            super.onBufferingEnd(playItem);
            reportBufferingEnd(playItem, isSeekEvent, mBufferStartTime);
            isSeekEvent = false;
        }
    };

    /**
     * 重置播放时间
     */
    private void resetReportPlayedTime() {
        mAudioPlayedTime = 0;
        mCurrentPosition = 0;
    }

    @Override
    public void reportStartPlay(Object bean) {
        if (ReportHelper.getInstance().isUseBySDK) {
            return;
        }
        if (isInterceptReport()) {
            return;
        }
        if (!(bean instanceof PlayItem)) {
            return;
        }

        PlayItem playItem = (PlayItem) bean;

        if (PlayerManagerHelper.getInstance().isSameProgram(playItem, mCurrentPlayItem)) {
            return;
        }
        mCurrentPlayItem = playItem;
        int type = playItem.getType();
        if (type == PlayerConstants.RESOURCES_TYPE_LIVE_STREAM) {
            reportLivingStreamPlay(playItem);
        } else if (type == PlayerConstants.RESOURCES_TYPE_LIVING) {
            reportLivingPlay(playItem);
        } else {
            reportStartOtherPlay(playItem);
        }
    }

    @Override
    public void reportLivingPlay(Object bean) {
        if (!(bean instanceof PlayItem)) {
            return;
        }
        PlayItem playItem = (PlayItem) bean;
        if (StringUtil.isEmpty(playItem.getPlayUrl())) {
            return;
        }

        if (playItem instanceof LivePlayItem) {
            LivePlayItem livePlayItem = (LivePlayItem) playItem;
            InnerPlayReportParameter playReportParameter = new InnerPlayReportParameter();
            playReportParameter.setAudioid(String.valueOf(livePlayItem.getAudioId()));
            playReportParameter.setIsThirdParty(playItem.getIsThirdParty());
            playReportParameter.setLiveType_status(String.valueOf(livePlayItem.getStatus()));
            playReportParameter.setLiveType_live_id(String.valueOf(livePlayItem.getInfoData().getAlbumId()));
            playReportParameter.setLiveType_plan_id(String.valueOf(livePlayItem.getLiveId()));
            playReportParameter.setLiveType_position(LivingStartListenReportEvent.POSITION_RECOMMENT);
            playReportParameter.setSourceType(ReportConstants.SOURCE_TYPE_LIVING);
            playReportParameter.setTotalLength(livePlayItem.getDuration());
            playReportParameter.setLiveType_compereid(livePlayItem.getComperesId());
            playReportParameter.setInnerPlayer();
            ReportHelper.getInstance().addStartListenReport(playReportParameter);
        }
    }

    private void reportLivingStreamPlay(PlayItem playItem) {
        if (playItem instanceof LiveStreamPlayItem) {
            LiveStreamPlayItem liveStreamPlayItem = (LiveStreamPlayItem) playItem;
            InnerPlayReportParameter playReportParameter = new InnerPlayReportParameter();
            playReportParameter.setAudioid(String.valueOf(liveStreamPlayItem.getAudioId()));
            playReportParameter.setIsThirdParty(playItem.getIsThirdParty());
            playReportParameter.setLiveType_status(String.valueOf((liveStreamPlayItem).getStatus()));
            playReportParameter.setLiveType_live_id(String.valueOf((liveStreamPlayItem).getInfoData().getAlbumId()));
            playReportParameter.setLiveType_plan_id(String.valueOf((liveStreamPlayItem).getLiveId()));
            playReportParameter.setLiveType_position(playItem.getMapCacheData(PlayItemConstantsSon.KEY_LIVING_LOCATION));
            playReportParameter.setSourceType(ReportConstants.SOURCE_TYPE_LIVING);
            playReportParameter.setTotalLength(liveStreamPlayItem.getDuration());
            playReportParameter.setLiveType_compereid(String.valueOf((liveStreamPlayItem).getComperesId()));
            playReportParameter.setInnerPlayer();
            ReportHelper.getInstance().addStartListenReport(playReportParameter);
        }
    }

    @Override
    public void reportStartOtherPlay(Object bean) {
        if (!(bean instanceof PlayItem)) {
            return;
        }
        PlayItem playItem = (PlayItem) bean;
        if (StringUtil.isEmpty(playItem.getPlayUrl())) {
            return;
        }
//        Log.d("ReportManager", "reportStartOtherPlay class " + bean.getClass() + " , type" + playItem.getType() + " , vip " + playItem.getVip() + " , fine " + playItem.getFine() + " , buyType " + playItem.getBuyType());
//        Log.d("ReportManager", "playitem position " + playItem.getPosition());
        InnerPlayReportParameter playReportParameter = new InnerPlayReportParameter();
        playReportParameter.setIsThirdParty(playItem.getIsThirdParty());
        switch (playItem.getType()) {
            case PlayerConstants.RESOURCES_TYPE_BROADCAST:
            case PlayerConstants.RESOURCES_TYPE_TV:
                playReportParameter.setAlbumid("0");
                break;
            default:
                playReportParameter.setAlbumid(String.valueOf(playItem.getAlbumId()));
        }
        playReportParameter.setAudioid(playItem.getAudioId() != 0 ? String.valueOf(playItem.getAudioId()) : null);
        playReportParameter.setRadioid(playItem.getRadioId());
        if (playItem.getType() == PlayerConstants.RESOURCES_TYPE_RADIO) {
            playReportParameter.setRadioType(String.valueOf(playItem.getRadioSubTagType()));
        }
        playReportParameter.setAudioSource(playItem.getSource());
        playReportParameter
                .setRecommendResultCallback(playItem.getCallback());
        playReportParameter.setSourceType(getReportType());

        playReportParameter.setBroadcast_status(getBroadcastStatus(playItem));
        playReportParameter.setTotalLength(playItem.getDuration());
        if (playItem.getVip() == 1) {
            playReportParameter.setTag("VIP");
        } else if (playItem.getFine() == 1) {
            playReportParameter.setTag("精品");
        } else {
            playReportParameter.setTag("无");
        }
        if (playItem.getBuyType() == AudioDetails.BUY_TYPE_FREE) {
            playReportParameter.setAudioid_type(0);
        } else if (playItem.getBuyType() == AudioDetails.BUY_TYPE_AUDITION) {
            playReportParameter.setAudioid_type(2);
        } else {
            playReportParameter.setAudioid_type(1);
        }
        if (playItem.getType() == PlayerConstants.RESOURCES_TYPE_ONE_KEY_LISTENER_TYPE
                || playItem.getType() == PlayerConstants.RESOURCES_TYPE_PURCHASE_ONE_KEY_LISTENER_TYPE) {
            playReportParameter.setContentObtainType(ReportConstants.COTENT_BY_ONEKEY);
        } else if (playItem.getPosition() != 0) {
            playReportParameter.setContentObtainType(ReportConstants.COTENT_BY_BREAKPOINT);
        }
        playReportParameter.setInnerPlayer();
        ReportHelper.getInstance().addStartListenReport(playReportParameter);
    }

    /**
     * 1：直播中；2：回放中
     */
    private String getBroadcastStatus(PlayItem playItem) {
        if (playItem instanceof BroadcastPlayItem
                || playItem instanceof TVPlayItem) {
            if (PlayerConstants.BROADCAST_STATUS_LIVING == playItem.getStatus()) {
                return "1";
            } else if (PlayerConstants.BROADCAST_STATUS_PLAYBACK == playItem.getStatus()) {
                return "2";
            }
            return "1";
        }
        return null;
    }


    private int getReportType() {
        int radioType = PlayerManagerHelper.getInstance().getCurPlayItem().getType();
        switch (radioType) {
            case PlayerConstants.RESOURCES_TYPE_BROADCAST:
                return ReportConstants.SOURCE_TYPE_BROADCAST;
            case PlayerConstants.RESOURCES_TYPE_LIVING:
                return ReportConstants.SOURCE_TYPE_LIVING;
            default:
                return ReportConstants.SOURCE_TYPE_ALBUM;
        }
    }

    /**
     * 上报播放结束事件
     */
    @Override
    public void reportEndPlay(String reason) {
        if (ReportHelper.getInstance().isUseBySDK) {
            return;
        }
        if (isInterceptReport()) {
            return;
        }
        reportEndPlay(reason, false);
    }

    /**
     * 上报播放结束事件
     */
    @Override
    public void reportEndPlay(String reason, boolean isNeedReport) {
        if (isInterceptReport()) {
            return;
        }
        ReportHelper.getInstance().addEndListenReport(reason, isNeedReport);
    }

    /**
     * 上报卡顿开始
     */
    @Override
    public void reportBufferingStart(Object bean, boolean isSeek) {
        if (ReportHelper.getInstance().isUseBySDK) {
            return;
        }
        if (isInterceptReport()) {
            return;
        }
        if (!(bean instanceof PlayItem)) {
            return;
        }
        PlayItem playItem = (PlayItem) bean;

        BufferStartReportEvent bufferStartReportEvent = new BufferStartReportEvent();
        bufferStartReportEvent.setAlbumid(String.valueOf(playItem.getAlbumId()));
        bufferStartReportEvent.setAudioid(String.valueOf(playItem.getAudioId()));
        bufferStartReportEvent.setRadioid(String.valueOf(playItem.getRadioId()));
        if (isSeek) {
            bufferStartReportEvent.setType("1");
        }
        bufferStartReportEvent.setRemarks2(playItem.getPlayUrl());

        getDeviceDns(s -> {
            bufferStartReportEvent.setRemarks1(s);
            ReportHelper.getInstance().addEvent(bufferStartReportEvent, false);
        });
    }

    /**
     * 上报卡顿结束
     *
     * @param bufferStartTime 卡顿开始时间
     */
    @Override
    public void reportBufferingEnd(Object bean, boolean isSeek,
                                   long bufferStartTime) {
        if (ReportHelper.getInstance().isUseBySDK) {
            return;
        }
        if (isInterceptReport()) {
            return;
        }
        if (!(bean instanceof PlayItem)) {
            return;
        }
        PlayItem playItem = (PlayItem) bean;

        BufferEndReportEvent bufferEndReportEvent = new BufferEndReportEvent();
        bufferEndReportEvent.setAlbumid(String.valueOf(playItem.getAlbumId()));
        bufferEndReportEvent.setAudioid(String.valueOf(playItem.getAudioId()));
        bufferEndReportEvent.setRadioid(String.valueOf(playItem.getRadioId()));
        try {
            double time = (SystemClock.elapsedRealtime() - bufferStartTime) / 1000.0;
            bufferEndReportEvent.setPlaytime(String.format("%.2f", time));
        } catch (Exception e) {

        }
        if (isSeek) {
            bufferEndReportEvent.setType("1");
        }
        bufferEndReportEvent.setRemarks2(playItem.getPlayUrl());

        getDeviceDns(s -> {
            bufferEndReportEvent.setRemarks1(s);
            ReportHelper.getInstance().addEvent(bufferEndReportEvent, false);
        });
    }


    /**
     *
     */
    @Override
    public void reportRequestError(Object bean, int what, int extra) {
        if (isInterceptReport()) {
            return;
        }
        if (!(bean instanceof PlayItem)) {
            return;
        }
        PlayItem playItem = (PlayItem) bean;

        RequetErrorReportEvent errorReportEvent = new RequetErrorReportEvent();
        errorReportEvent.setMessage(what + "&" + extra);
        errorReportEvent.setAudioid(String.valueOf(playItem.getAudioId()));
        errorReportEvent.setRadioid(String.valueOf(playItem.getRadioId()));
        errorReportEvent.setUrl(playItem.getPlayUrl());

        String curDnsIpUsed = null;

        String dnsStr = playItem.getMapCacheData(PlayItemConstants.ITEM_KEY_DNS_ADDRESS);
        if (!TextUtils.isEmpty(dnsStr)) {
            if (dnsStr.contains(",")) {
                String[] addressArray = dnsStr.split(",");
                if (addressArray != null && addressArray.length > 0) {
                    curDnsIpUsed = addressArray[0];
                }
            } else {
                curDnsIpUsed = dnsStr;
            }
        }
        errorReportEvent.setRemarks1(curDnsIpUsed);
        if (what == IjkMediaPlayer.MEDIA_ERROR_IJK_PLAYER || what == IjkMediaPlayer.MEDIA_ERROR_IJK_PLAYER_ZERO) {
            errorReportEvent.setRemarks3("1");
        } else {
            errorReportEvent.setRemarks3("2");
        }
        errorReportEvent.setResult(String.valueOf(extra));
        boolean isHasNetwork = NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false);
        if (!isHasNetwork) {
            errorReportEvent.setSpeed("1");
        }
        getDeviceDns(s -> {
            errorReportEvent.setRemarks2(s);
            ReportHelper.getInstance().addEvent(errorReportEvent, false);
        });
    }

    @Override
    public void setPageId(String pageId) {
        if (isInterceptReport()) {
            return;
        }
        Logging.d(ReportConstants.REPORT_TAG, "设置pageid= " + pageId);
        ReportHelper.getInstance().setPage(pageId);
    }


    /**
     * 搜索结果 播放
     */
    @Override
    public void reportSearchToPlay(String playType, String callback) {
        if (isInterceptReport()) {
            return;
        }
        ReportHelper.getInstance().setSearchAudioPlayCallBack(playType, callback);
    }

    /**
     * @param way      选择方式	1.语音选择；2.手动选择；3.未知
     * @param radioId  radioId，碎片则为专辑id，ai电台为电台id，专辑为专辑id
     * @param id       内容id 对应选择的结果内容id：碎片id，专辑id、ai电台id、广播id（至少一个）
     * @param position 搜索结果索引号 被选择的结果索引号：1，2，3，4
     * @param result   搜索结果追踪号	搜索服务端透传的数据
     */
    @Override
    public void reportSearchSelectResult(String way, String radioId, String id, String position, String result) {
        if (isInterceptReport()) {
            return;
        }
        SearchResultSelectReportEvent searchResultSelectReportEvent = new SearchResultSelectReportEvent();
        searchResultSelectReportEvent.setWay(way);
        searchResultSelectReportEvent.setRadioid(radioId);
        searchResultSelectReportEvent.setRemarks2(id);
        searchResultSelectReportEvent.setRemarks3(position);
        searchResultSelectReportEvent.setRemarks9(result);
        ReportHelper.getInstance().addEvent(searchResultSelectReportEvent, false);
    }

    /**
     * @param requestAgent 用户原始文本，比如 ‘我要听刘德华的冰雨
     * @param type         1.手动输入；2.点击历史记录；3.点击联想词；4.语音搜索;5.点击热门搜索
     * @param result       0：超时；1：参数有误；2：无结果；3：正常
     * @param playType     0:否，1：是
     * @param keyword      语义理解的结果，比如‘刘德华，冰雨’
     * @param contentType  内容分类
     */
    @Override
    public void reportSearchResult(String requestAgent, String type, String result, String playType, String keyword, String contentType) {
        if (isInterceptReport()) {
            return;
        }
        SearchResultReportEvent searchResultReportEvent = new SearchResultReportEvent();
        searchResultReportEvent.setPlaytype(playType);
        searchResultReportEvent.setType(type);
        searchResultReportEvent.setResult(result);
        searchResultReportEvent.setRequest_agent(requestAgent);
        searchResultReportEvent.setRemarks1(keyword);
        searchResultReportEvent.setRemarks2(contentType);
        ReportHelper.getInstance().addEvent(searchResultReportEvent, false);
    }

    private void getDeviceDns(DeviceUtil.DnsCallback callback) {
        DeviceUtil.getDeviceDns(callback);
    }

    /**
     * 音质选择
     */
    @Override
    public void addToneSelectEvent(int type) {
        if (isInterceptReport()) {
            return;
        }
        ToneSelectReportEvent toneSelectReportEvent = new ToneSelectReportEvent();
        toneSelectReportEvent.setType(String.valueOf(type));
        ReportHelper.getInstance().addEvent(toneSelectReportEvent);
    }

    /**
     * 负反馈
     */
    @Override
    public void addMinusFeedbackEvent(String audioId, String radioId, String albumId, String callback,
                                      String position) {
        if (isInterceptReport()) {
            return;
        }
        MinusFeedbackReportEvent event = new MinusFeedbackReportEvent();
        event.setAudioid(audioId);
        event.setAlbumid(albumId);
        event.setPosition(position);
        event.setRemarks11(callback);
        event.setRadioid(radioId);
        ReportHelper.getInstance().addEvent(event);
    }

    /**
     * 正反馈
     */
    @Override
    public void addPlusFeedbackEvent(String audioId, String radioId, String albumId, String callback) {
        if (isInterceptReport()) {
            return;
        }
        PlusFeedbackReportEvent event = new PlusFeedbackReportEvent();
        event.setAudioid(audioId);
        event.setRadioid(radioId);
        event.setAlbumid(albumId);
        event.setRemarks11(callback);
        ReportHelper.getInstance().addEvent(event);
    }

    /**
     * 登录
     *
     * @param type     登录类型, 1 扫码  2 手机号登录
     * @param remarks1 122113.我的——个人中心——进入登录流程
     *                 122112.已购页面——点击立即登录——进入登录流程
     *                 122111.收听历史——点击立即登录——进入登录流程
     *                 122110.我的订阅——点击立即登录——进入登录流程
     *                 141200.试听碎片轮播至需付费碎片——判断登录
     *                 141201.播放详情页专辑封面下方点击“VIP会员 免费听”——判断登录
     *                 141202.播放详情页内点击需付费碎片——判断登录
     *                 <p>
     *                 232010听迹——收听历史——点击立即登录——进入登录流程
     *                 231010听迹——我的订阅——点击立即登录——进入登录流程
     *                 141202播放详情页内点击“购买专辑”——判断 登录
     *                 152000搜索结果页内点播付费单曲——判断登录（复用已有）
     *                 141204播放详情页内播放列表点击单曲订阅——判断登录
     *                 250021直播间详情页点击留言——判断登录
     */
    @Override
    public void addLoginEvent(String type, String remarks1) {
        if (isInterceptReport()) {
            return;
        }
        LoginReportEvent event = new LoginReportEvent();
        event.setType(type);
        event.setRemarks1(remarks1);
        ReportHelper.getInstance().addEvent(event);
    }

    /**
     * 直播留言
     */
    @Override
    public void addLivingLeaveMessageEvent(String id, String liveId, String compereId, String radioId) {
        if (isInterceptReport()) {
            return;
        }
        LivingLeaveMessageReportEvent event = new LivingLeaveMessageReportEvent();
        event.setLive_id(id);
        event.setPlan_id(liveId);
        event.setLive_manager_uid(compereId);
        event.setRadioid(radioId);
        ReportHelper.getInstance().addEvent(event);
    }

    /**
     * 订阅与取消订阅
     *
     * @param type          0：取消订阅；1：订阅
     * @param subscribeType 1：专辑；2：AI电台；3：广播；4：音乐电台（第三方内容源）；5：单曲；6：听电视
     * @param position      1：播放条（对应在线电台版底部播放器）；2：全屏播放器（对应在线电台版播放详情页）；3：widget，4:订阅列表页，5：播放列表
     * @param radioId       对应的专辑id，AI电台id，广播id，单曲id，听电视id
     */
    @Override
    public void addSubscribeEvent(String type, String subscribeType, String position, String radioId) {
        if (isInterceptReport()) {
            return;
        }
        SubscibeReportEvent event = new SubscibeReportEvent();
        event.setType(type);
        event.setSubscribetype(subscribeType);
        event.setPosition(position);
        event.setRadioid(radioId);
        ReportHelper.getInstance().addEvent(event);
    }

    /**
     * 转换订阅类型
     *
     * @param type 1：专辑；2：电台；3：广播；4：音乐电台（第三方内容源）
     */
    private String convertSubscribeType(String type) {
        int typeInt = 1;
        try {
            typeInt = Integer.parseInt(type);
        } catch (Exception e) {

        }
        String tempType;

        switch (typeInt) {
            case PlayerConstants.RESOURCES_TYPE_ALBUM:
                tempType = "1";
                break;
            case PlayerConstants.RESOURCES_TYPE_RADIO:
                tempType = "2";
                break;
            case PlayerConstants.RESOURCES_TYPE_BROADCAST:
                tempType = "3";
                break;
            case PlayerConstants.RESOURCES_TYPE_AUDIO:
                tempType = "5";
                break;
            case PlayerConstants.RESOURCES_TYPE_TV:
                tempType = "6";
                break;
            default:
                tempType = "4";
                break;
        }
        return tempType;
    }

    /**
     * 播放条控制
     *
     * @param type        1：播放后暂停；2：暂停后播放；3：上一首；4：下一首；5：列表,6：移动进度条
     * @param controlType 1：点击；2：语音；3：方控；4：其他;5：滑动
     * @param position    1：播放条；2：全屏播放器；3：widget
     */
    @Override
    public void addPlayerUiControlEvent(String type, String controlType, String position) {
        if (isInterceptReport()) {
            return;
        }
        PlayerUiControlReportEvent event = new PlayerUiControlReportEvent();
        event.setType(type);
        event.setControltype(controlType);
        event.setPosition(position);
        ReportHelper.getInstance().addEvent(event);
    }

    /**
     * 推荐展示
     */
    @Override
    public void addRecommendShowEvent(List<?> beanList) {
        if (isInterceptReport()) {
            return;
        }
        if (ListUtil.isEmpty(beanList)) {
            return;
        }
        int size = beanList.size();
        for (int i = 0; i < size; i++) {
            Object obj = beanList.get(i);

            if (!(obj instanceof HomeCell)) {
                continue;
            }
            HomeCell homeCell = (HomeCell) obj;
            if (StringUtil.isEmpty(homeCell.outputMode)) {
                continue;
            }
            if (StringUtil.isEmpty(homeCell.callBack)) {
                continue;
            }
            RecommendShowReportEvent event = new RecommendShowReportEvent();
            event.setType(homeCell.outputMode);
            event.setRemarks11(homeCell.callBack);
            ReportHelper.getInstance().addEvent(event);
        }
    }

    /**
     * 播放列表推荐展示
     */
    @Override
    public void addPlayListRecommendShowEvent(int type, String callBack) {
        if (isInterceptReport()) {
            return;
        }
        if (StringUtil.isEmpty(callBack)) {
            return;
        }
        if (type != Constants.AI_RADIO_CONTENT_TYPE_RECOMMEND) {
            return;
        }

        RecommendShowReportEvent event = new RecommendShowReportEvent();
        event.setType("0");
        event.setRemarks11(callBack);
        ReportHelper.getInstance().addEvent(event);
    }


    /**
     * 推荐点击
     *
     * @param callBack 推荐服务端透传的数据
     */
    @Override
    public void addRecommendSelectEvent(String type, String callBack) {
        if (isInterceptReport()) {
            return;
        }
        if (StringUtil.isEmpty(type) || StringUtil.isEmpty(callBack)) {
            return;
        }
        RecommendSelectReportEvent event = new RecommendSelectReportEvent();
        event.setType(type);
        event.setRemarks11(callBack);
        ReportHelper.getInstance().addEvent(event);
    }

    /**
     * 添加激活结果上报.
     */
    @Override
    public void tingBanToKRadioUpdate() {
        if (isInterceptReport()) {
            return;
        }
        KaoLaFmToKRadioEvent event = new KaoLaFmToKRadioEvent();
        ReportHelper.getInstance().addEvent(event);
    }

    /**
     * 是否拦截数据上报
     *
     * @return
     */
    public boolean isInterceptReport() {
        return isCloseAllReport || !ReportHelper.getInstance().isInitSuccess;
    }

    @Override
    public void addContentClickEvent(@NotNull String audioId, @NotNull String radioType,
                                     @NotNull String audioType, @NotNull String radioId,
                                     @NotNull String tag, @NotNull String pageId,
                                     @NotNull String remarks1, @NotNull String remarks2) {
        if (isInterceptReport()) {
            return;
        }

        ContentClickReportEvent event = new ContentClickReportEvent();
        event.setAudioId(audioId);
        event.setRadioType(radioType);
        event.setAudioIdType(audioType);
        event.setRadioId(radioId);
        event.setTag(tag);
        event.setPageId(pageId);
        event.setRemarks1(remarks1);
        event.setRemarks2(remarks2);
        ReportHelper.getInstance().addEvent(event);
    }

    @Override
    public void addPageShowEvent(long startTime, @NotNull String pageId) {
        long duration = System.currentTimeMillis() - startTime;
        if (isInterceptReport() || startTime < 0 || duration < 300) {
            return;
        }

        PageShowReportEvent event = new PageShowReportEvent();
        event.setPageId(pageId);
        event.setPageTime(String.valueOf(duration));
        ReportHelper.getInstance().addEvent(event);
    }

    @Override
    public void addContentShowEvent(@NotNull String audioId, @NotNull String contentType,
                                    @NotNull String audioType, @NotNull String radioId,
                                    @NotNull String tag, @NotNull String pageId,
                                    @NotNull String remarks1, @NotNull String remarks2) {
        if (isInterceptReport()) {
            return;
        }

        ContentShowReportEvent event = new ContentShowReportEvent();
        event.setAudioId(audioId);
        event.setRadioType(contentType);
        event.setAudioidType(audioType);
        event.setRadioId(radioId);
        event.setTag(tag);
        event.setPageId(pageId);
        event.setRemarks1(remarks1);
        event.setRemarks2(remarks2);
        ReportHelper.getInstance().addEvent(event);
    }

    @Override
    public void addSettingVoiceSwiych(int type) {
        if (isInterceptReport()) {
            return;
        }
        SettingVoiceSwitchEvent settingVoiceSwitchEvent = new SettingVoiceSwitchEvent();
        settingVoiceSwitchEvent.setType(String.valueOf(type));
        ReportHelper.getInstance().addEvent(settingVoiceSwitchEvent);
    }

    @Override
    public void addMessageShow(@NotNull String pageId, @NotNull String radiotype, @NotNull String remarks2) {
        if (isInterceptReport()) {
            return;
        }
        MessageShowReportEvent messageShowReportEvent = new MessageShowReportEvent();
        messageShowReportEvent.setRadiotype(radiotype);
        messageShowReportEvent.setRemarks2(remarks2);
        messageShowReportEvent.setPageid(pageId);
        ReportHelper.getInstance().addEvent(messageShowReportEvent);
    }

    @Override
    public void addMessageClike(@NotNull String pageId, @NotNull String radiotype, @NotNull String remarks2) {
        MessageClickReportEvent messageClickReportEvent = new MessageClickReportEvent();
        messageClickReportEvent.setRadiotype(radiotype);
        messageClickReportEvent.setRemarks2(remarks2);
        messageClickReportEvent.setPageid(pageId);
        messageClickReportEvent.setPage(pageId);
        ReportHelper.getInstance().addEvent(messageClickReportEvent);
    }

    @Override
    public void addComponentShowAndClickEvent(@NotNull String audioId, @NotNull boolean mold,
                                              @NotNull String cardid,  int action,
                                              @NotNull String column_code, @NotNull String memberorder,
                                              @NotNull String placeorder, @NotNull String radioId,
                                              @NotNull String tag, @NotNull String pageId,
                                              @NotNull String paytype) {
        if (isInterceptReport()) {
            return;
        }
        AppComponentShowAndClickEvent event = new AppComponentShowAndClickEvent();
        event.setCardid(cardid);
        event.setAudioid(audioId);
        event.setRadioid(radioId);
        event.setMemberorder(memberorder);
        event.setPlaceorder(placeorder);
        event.setPageid(pageId);
        event.setTag(tag);
        event.setPaytype(paytype);
        event.setColumn_code(column_code == null ? "0" : column_code);
        if (mold) {
            event.setMold("click");
            event.setAction(action + "");//1：跳转 2:播放 3:跳转+播放
        } else {
            event.setMold("exposure");//click:点击 exposure:曝光
        }

        ReportHelper.getInstance().addEvent(event);
    }
}
