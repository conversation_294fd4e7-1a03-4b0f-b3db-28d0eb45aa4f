package com.kaolafm.kradio.huawei.provider;


import com.huawei.carmediakit.bean.MediaElement;
import com.huawei.carmediakit.bean.MineCompilation;
import com.huawei.carmediakit.bean.MineDataType;
import com.huawei.carmediakit.bean.MineStatistics;
import com.huawei.carmediakit.bean.PageContent;
import com.huawei.carmediakit.bean.PageTab;
import com.huawei.carmediakit.bean.PagedMediaElements;
import com.huawei.carmediakit.bean.PagenationInfo;
import com.huawei.carmediakit.callback.BasicCallback;
import com.huawei.carmediakit.constant.ErrorCode;
import com.huawei.carmediakit.provider.IMediaDataProvider;
import com.kaolafm.kradio.common.CP;
import com.kaolafm.kradio.common.SubscribeData;
import com.kaolafm.kradio.common.bean.HistoryItem;
import com.kaolafm.kradio.common.helper.SubscribeHelper;
import com.kaolafm.kradio.huawei.convert.HistoryConvertUtil;
import com.kaolafm.kradio.huawei.convert.PlayDataConverterUtil;
import com.kaolafm.kradio.huawei.convert.SubscribeConvertUtil;
import com.kaolafm.kradio.huawei.utils.Constant;
import com.kaolafm.kradio.huawei.convert.DataConverterUtil;
import com.kaolafm.kradio.huawei.utils.MediaIdHelper;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.broadcast.BroadcastRequest;
import com.kaolafm.opensdk.api.broadcast.ProgramDetails;
import com.kaolafm.opensdk.api.media.AlbumRequest;
import com.kaolafm.opensdk.api.media.RadioRequest;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.api.operation.OperationRequest;
import com.kaolafm.opensdk.api.operation.model.category.Category;
import com.kaolafm.opensdk.api.operation.model.category.CategoryMember;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

import java.util.ArrayList;
import java.util.List;

public class MediaDataProvider implements IMediaDataProvider {
    public static final String TAG = Constant.TAG;

    @Override
    public List<PageTab> queryMainPageTabs() {
        Logger.i(TAG, "queryMainPageTabs");

        return Constant.pageTabList;
    }
    @Override
    public void asyncQueryPageContent(final String tabId, final
            BasicCallback<PageContent> resultCallback) {
        Logger.i(TAG, "asyncQueryPageContent");

        new OperationRequest().getCategoryList(tabId, true, 0, true, 0, 50, new HttpCallback<List<Category>>() {
            @Override
            public void onSuccess(List<Category> categoryList) {
                PageContent pageContent = DataConverterUtil.toPageContent(categoryList);
                resultCallback.callback(pageContent, ErrorCode.SUCCESS, "success");
            }

            @Override
            public void onError(ApiException e) {
                resultCallback.callback(null, ErrorCode.ERROR, "error");
            }
        });

    }

    @Override
    public void asyncQueryBanner(String s, String s1, int i, BasicCallback<PagedMediaElements> basicCallback) {
        Logger.i(TAG, "asyncQueryBanner");
    }

    @Override
    public void asyncQueryCompilation(String s, int i, BasicCallback<PagedMediaElements> basicCallback) {
        Logger.i(TAG, "asyncQueryCompilation s=" + s + ":i=" + i);
        if (s.contains("_")) {
            String id = MediaIdHelper.getPlayId(s);
            int type = MediaIdHelper.getContentType(s);
            if (type == PlayerConstants.RESOURCES_TYPE_ALBUM) {
                getAlbumList(id, i, basicCallback);
            } else if (type == PlayerConstants.RESOURCES_TYPE_RADIO) {
                getRadioList(id, i, basicCallback);
            } else if (type == PlayerConstants.RESOURCES_TYPE_BROADCAST) {
                getBroadcastList(id, i, basicCallback);
            }
            return;
        }
        if ("subscribe".equals(s)) {
            asyncQueryMinePageContent("all&我喜欢", "", i, basicCallback);
            return;
        }
        if ("history".equals(s)) {
            asyncQueryMinePageContent("all&最近播放", "", i, basicCallback);
            return;
        }
        new OperationRequest().getCategoryMemberList(s, i, 30, new HttpCallback<BasePageResult<List<CategoryMember>>>() {
            @Override
            public void onSuccess(BasePageResult<List<CategoryMember>> listBasePageResult) {
                PagedMediaElements pagedMediaElements = new PagedMediaElements();
                List<CategoryMember> memberList = listBasePageResult.getDataList();
                List<MediaElement> mediaElementList = new ArrayList<>();
                for (CategoryMember member: memberList) {
                    MediaElement element = DataConverterUtil.toMediaElement(member);
                    mediaElementList.add(element);
                }
                pagedMediaElements.setElements(mediaElementList);
                PagenationInfo pagenationInfo = new PagenationInfo();
                pagenationInfo.setTotalNum(listBasePageResult.getCount());
                pagenationInfo.setPageSize(listBasePageResult.getPageSize());
                pagenationInfo.setPageIndex(listBasePageResult.getCurrentPage());
                pagedMediaElements.setPagenationInfo(pagenationInfo);
                basicCallback.callback(pagedMediaElements, ErrorCode.SUCCESS, "Success");
            }

            @Override
            public void onError(ApiException e) {
                basicCallback.callback(null, ErrorCode.ERROR, "Error");
            }
        });
    }

    private void getAlbumList(String id, int pageNum, BasicCallback<PagedMediaElements> basicCallback) {
        new AlbumRequest().getPlaylist(Long.parseLong(id), AlbumRequest.SORT_ACS, 20, pageNum, new HttpCallback<BasePageResult<List<AudioDetails>>>() {
            @Override
            public void onSuccess(BasePageResult<List<AudioDetails>> result) {
                List<MediaElement> mediaElementList = PlayDataConverterUtil.toMediaElementListFromAudioDetails(result.getDataList());
                PagedMediaElements pagedMediaElements = buildPagedMediaElements(pageNum, 15, mediaElementList);
                basicCallback.callback(pagedMediaElements, ErrorCode.SUCCESS, "Success");
            }

            @Override
            public void onError(ApiException exception) {
                basicCallback.callback(null, ErrorCode.ERROR, "Error");
            }
        });
    }

    String clockId = "";
    private void getRadioList(String id, int pageNum, BasicCallback<PagedMediaElements> basicCallback) {
        new RadioRequest().getPlaylist(Long.parseLong(id), clockId, new HttpCallback<List<AudioDetails>>() {
            @Override
            public void onSuccess(List<AudioDetails> result) {
                Logger.i(TAG, "result=" + result);
                List<MediaElement> mediaElementList = PlayDataConverterUtil.toMediaElementListFromAudioDetails(result);
                PagedMediaElements pagedMediaElements = buildPagedMediaElements(pageNum, 15, mediaElementList);
                basicCallback.callback(pagedMediaElements, ErrorCode.SUCCESS, "Success");
            }

            @Override
            public void onError(ApiException e) {
                basicCallback.callback(null, ErrorCode.ERROR, "Error");
            }
        });
    }

    private void getBroadcastList(String id, int pageNum, BasicCallback<PagedMediaElements> basicCallback) {
        new BroadcastRequest().getBroadcastProgramList(Long.parseLong(id), null, new HttpCallback<List<ProgramDetails>>() {
            @Override
            public void onSuccess(List<ProgramDetails> programDetails) {
                Logger.i(TAG, "broadcast=" + programDetails);
                List<MediaElement> mediaElementList = PlayDataConverterUtil.toMediaElementListFromProgramDetails(programDetails);
                PagedMediaElements pagedMediaElements = buildPagedMediaElements(pageNum, 15, mediaElementList);
                basicCallback.callback(pagedMediaElements, ErrorCode.SUCCESS, "Success");
            }

            @Override
            public void onError(ApiException e) {
                basicCallback.callback(null, ErrorCode.ERROR, "Error");
            }
        });
    }

    @Override
    public void asyncQueryCollection(String s, String s1, int i, BasicCallback<PagedMediaElements> basicCallback) {
        Logger.i(TAG, "asyncQueryCollection");
    }

    @Override
    public void asyncQuerySingerSingles(String s, String s1, int i, BasicCallback<PagedMediaElements> basicCallback) {
        Logger.i(TAG, "asyncQuerySingerSingles");
    }

    @Override
    public void asyncQuerySingerAlbums(String s, String s1, int i, BasicCallback<PagedMediaElements> basicCallback) {
        Logger.i(TAG, "asyncQuerySingerAlbums");
    }

    @Override
    public void asyncQueryMineStatitics(BasicCallback<List<MineStatistics>> basicCallback) {
        Logger.i(TAG, "asyncQueryMineStatitics");
        HistoryManager.getInstance().getHistoryList(new HttpCallback<List<HistoryItem>>() {
            @Override
            public void onSuccess(List<HistoryItem> historyItems) {
                getSubscribeItemCount(historyItems.size(), basicCallback);
            }

            @Override
            public void onError(ApiException e) {
                basicCallback.callback(new ArrayList<>(), ErrorCode.ERROR, "error");
            }
        });
    }

    private void getSubscribeItemCount(int historyCount, BasicCallback<List<MineStatistics>> basicCallback) {
        SubscribeHelper.getMySubscriptions(CP.KaoLaFM, new HttpCallback<List<SubscribeData>>() {
            @Override
            public void onSuccess(List<SubscribeData> subscribes) {
                Logger.i(TAG, "success=" + subscribes);
                ArrayList<MineStatistics> arrayList = new ArrayList();
                MineStatistics mineStatistics = new MineStatistics();
                mineStatistics.setMediaId("latest_play_static");
                mineStatistics.setType(MineDataType.LATEST_PLAY);
                mineStatistics.setTotalElementsNum(historyCount);
                arrayList.add(mineStatistics);
                mineStatistics = new MineStatistics();
                mineStatistics.setMediaId("favorite_static");
                mineStatistics.setType(MineDataType.FAVOURITES);
                mineStatistics.setTotalElementsNum(subscribes.size());
                arrayList.add(mineStatistics);
                basicCallback.callback(arrayList, ErrorCode.SUCCESS, "Success");
            }

            @Override
            public void onError(ApiException e) {
                Logger.i(TAG, "e=" + e.toString());
                basicCallback.callback(new ArrayList<>(), ErrorCode.ERROR, "error");
            }
        });
    }

    @Override
    public void asyncQueryMinePageTabs(MineDataType mineDataType, BasicCallback<List<PageTab>> basicCallback) {
        Logger.i(TAG, "asyncQueryMinePageTabs");

        if (mineDataType == MineDataType.LATEST_PLAY) {
            basicCallback.callback(getPageTabs("最近播放", false),
                    ErrorCode.SUCCESS, "success");
            return;
        }
        if (mineDataType == MineDataType.FAVOURITES) {
            basicCallback.callback(getPageTabs("我喜欢", false),
                    ErrorCode.SUCCESS, "success");
            return;
        }
    }

    private List<PageTab> getPageTabs(String paramString, boolean paramBoolean) {
        ArrayList<PageTab> arrayList = new ArrayList();
        PageTab pageTab = new PageTab();
        pageTab.setTabName(paramString);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("all");
        stringBuilder.append("&");
        stringBuilder.append(paramString);
        pageTab.setMediaId(stringBuilder.toString());
        arrayList.add(pageTab);
        return arrayList;
    }

    @Override
    public void asyncQueryMinePageContent(String paramString1, String paramString2, int paramInt, BasicCallback<PagedMediaElements> basicCallback) {
        Logger.i(TAG, "asyncQueryMinePageContent paramString1=" + paramString1
                + ":paramString2=" + paramString2 + ":i=" + paramInt);
        String[] arrayOfString2 = new String[2];
        String[] arrayOfString1 = arrayOfString2;
        if (paramString1 != null) {
            arrayOfString1 = arrayOfString2;
            if (paramString1.contains("&"))
                arrayOfString1 = paramString1.split("&");
        }
        if ("最近播放".equals(arrayOfString1[1])) {
            HistoryManager.getInstance().getHistoryList(new HttpCallback<List<HistoryItem>>() {
                @Override
                public void onSuccess(List<HistoryItem> historyItems) {
                    Logger.i(TAG, "success=" + historyItems);
                    List<MediaElement> mediaElementList = HistoryConvertUtil.toMediaElementList(historyItems);
                    PagedMediaElements pagedMediaElements = buildPagedMediaElements(1,
                            mediaElementList.size(), mediaElementList);
                    basicCallback.callback(pagedMediaElements, ErrorCode.SUCCESS, "success");
                }

                @Override
                public void onError(ApiException e) {
                    Logger.i(TAG, "e=" + e.toString());
                    basicCallback.callback(new PagedMediaElements(), ErrorCode.ERROR, "error");
                }
            });
        } else {
            SubscribeHelper.getMySubscriptions(CP.KaoLaFM, new HttpCallback<List<SubscribeData>>() {
                @Override
                public void onSuccess(List<SubscribeData> subscribes) {
                    Logger.i(TAG, "success=" + subscribes);
                    List<MediaElement> mediaElementList = SubscribeConvertUtil.toMediaElementList(subscribes);
                    PagedMediaElements pagedMediaElements = buildPagedMediaElements(1,
                            mediaElementList.size(), mediaElementList);
                    basicCallback.callback(pagedMediaElements, ErrorCode.SUCCESS, "success");
                }

                @Override
                public void onError(ApiException e) {
                    Logger.i(TAG, "e=" + e.toString());
                    basicCallback.callback(new PagedMediaElements(), ErrorCode.ERROR, "error");
                }
            });
        }
    }

    private PagedMediaElements buildPagedMediaElements(int pageIndex, int pageSize, List<MediaElement> mediaElementList) {
        PagedMediaElements pagedMediaElements = new PagedMediaElements();
        PagenationInfo pagenationInfo = new PagenationInfo();
        pagenationInfo.setPageIndex(pageIndex);
        pagenationInfo.setPageSize(pageSize);
        pagenationInfo.setTotalNum(mediaElementList.size());
        pagedMediaElements.setPagenationInfo(pagenationInfo);
        pagedMediaElements.setElements(mediaElementList);

        return pagedMediaElements;
    }

    @Override
    public void asyncQueryMineCompilations(BasicCallback<List<MineCompilation>> basicCallback) {
        Logger.i(TAG, "asyncQueryMineCompilations");

        ArrayList<MineCompilation> arrayList = new ArrayList();

        HistoryManager.getInstance().getHistoryList(new HttpCallback<List<HistoryItem>>() {
            @Override
            public void onSuccess(List<HistoryItem> historyItems) {
                MineCompilation mineCompilation = new MineCompilation();
                List<MediaElement> elementList = HistoryConvertUtil.toMediaElementList(historyItems);
                mineCompilation.setTopElements(elementList);
                mineCompilation.setName("最近播放");
                mineCompilation.setMediaId("history");
                mineCompilation.setCompilationType(MineCompilation.MineCompilationType.MY_PLAYLIST);
                mineCompilation.setTotalElementsNum(elementList.size());
                mineCompilation.setElementsType(MediaElement.ElementType.PLAY_LIST);
                arrayList.add(mineCompilation);
                getSubscribeItem(arrayList, basicCallback);
            }

            @Override
            public void onError(ApiException e) {
                basicCallback.callback(new ArrayList<>(), ErrorCode.ERROR, "error");
            }
        });
    }

    private void getSubscribeItem(List arrayList, BasicCallback<List<MineCompilation>> basicCallback) {
        SubscribeHelper.getMySubscriptions(CP.KaoLaFM, new HttpCallback<List<SubscribeData>>() {
            @Override
            public void onSuccess(List<SubscribeData> subscribes) {
                Logger.i(TAG, "success=" + subscribes);
                MineCompilation compilation = new MineCompilation();
                List<MediaElement> elementList = SubscribeConvertUtil.toMediaElementList(subscribes);
                compilation.setMediaId("subscribe");
                compilation.setTopElements(elementList);
                compilation.setName("我喜欢");
                compilation.setCompilationType(MineCompilation.MineCompilationType.FAVOURITE_PLAYLIST);
                compilation.setElementsType(MediaElement.ElementType.PLAY_LIST);
                arrayList.add(compilation);
                basicCallback.callback(arrayList, ErrorCode.SUCCESS, "Success");
            }

            @Override
            public void onError(ApiException e) {
                Logger.i(TAG, "e=" + e.toString());
                basicCallback.callback(new ArrayList<>(), ErrorCode.ERROR, "error");
            }
        });
    }
}