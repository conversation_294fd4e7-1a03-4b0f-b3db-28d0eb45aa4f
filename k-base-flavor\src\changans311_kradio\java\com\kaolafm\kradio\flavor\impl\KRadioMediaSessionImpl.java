package com.kaolafm.kradio.flavor.impl;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.IntentFilter;
import android.media.AudioManager;
import android.util.Log;

import com.kaolafm.kradio.flavor.receiver.KRMediaButtonBroadcastReceiver;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioMediaSessionInter;

import com.kaolafm.sdk.core.mediaplayer.OnAudioFocusChangeInter;

import java.lang.ref.WeakReference;

import static com.incall.proxy.constant.SettingsConstantsDef.COAGENT_ACTION_KEY_CHANGED;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-05-22 20:59
 ******************************************/
public final class KRadioMediaSessionImpl implements KRadioMediaSessionInter {
    private static final String TAG = "KRadioMediaSessionImpl";
    private BroadcastReceiver mBroadcastReceiver;
    private MyOnAudioFocusChangeInter myOnAudioFocusChangeInter;

    public KRadioMediaSessionImpl() {
        // TODO 此逻辑暂时先做成标准逻辑（KRadio在失去音频焦点的情况下理论上是不应该响应多媒体按键事件的）
        myOnAudioFocusChangeInter = new MyOnAudioFocusChangeInter(this);
        PlayerManager.getInstance().addAudioFocusListener(myOnAudioFocusChangeInter);
    }

    @Override
    public void registerMediaSession(Object... args) {
        if (mBroadcastReceiver == null) {
            Context context = (Context) args[0];
            mBroadcastReceiver = new KRMediaButtonBroadcastReceiver();
            IntentFilter intentFilter = new IntentFilter(COAGENT_ACTION_KEY_CHANGED);
            context.registerReceiver(mBroadcastReceiver, intentFilter);
        }
    }

    @Override
    public void unregisterMediaSession(Object... args) {
        if (mBroadcastReceiver != null) {
            Context context = (Context) args[0];
            context.unregisterReceiver(mBroadcastReceiver);
            mBroadcastReceiver = null;
        }
    }

    @Override
    public void release(Object... args) {
        PlayerManager.getInstance().removeAudioFocusListener(myOnAudioFocusChangeInter);
    }

    private static class MyOnAudioFocusChangeInter implements OnAudioFocusChangeInter {
        private WeakReference<KRadioMediaSessionImpl> weakReference;

        public MyOnAudioFocusChangeInter(KRadioMediaSessionImpl kRadioMediaSessionImpl) {
            weakReference = new WeakReference<>(kRadioMediaSessionImpl);
        }

        @Override
        public void onAudioFocusChange(int i) {
            Log.i(TAG, "onAudioFocusChange--------> i = " + i);
            KRadioMediaSessionImpl kRadioMediaSessionImpl = weakReference.get();
            if (kRadioMediaSessionImpl == null) {
                return;
            }
            if (i == AudioManager.AUDIOFOCUS_GAIN) {
                kRadioMediaSessionImpl.registerMediaSession(AppDelegate.getInstance().getContext());
            } else if (i == AudioManager.AUDIOFOCUS_LOSS || i == AudioManager.AUDIOFOCUS_LOSS_TRANSIENT) {
                kRadioMediaSessionImpl.unregisterMediaSession(AppDelegate.getInstance().getContext());
            }
        }
    }
}