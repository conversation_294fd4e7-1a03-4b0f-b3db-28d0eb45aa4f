package com.kaolafm.kradio.flavor.impl;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioThirdPlatformInitInter;
import com.kaolafm.kradio.service.EmptyService;


public class KRadioThirdPlatformInitImpl implements KRadioThirdPlatformInitInter {

    @Override
    public boolean initThirdPlatform(Object... args) {
        EmptyService.start(AppDelegate.getInstance().getContext());
        return true;
    }

    @Override
    public boolean destroyThirdPlatform(Object... args) {
        return false;
    }
}
