// IMediaChangeInterFace.aidl
package com.iflytek.autofly.home.aidl;

import com.iflytek.autofly.home.aidl.IMediaBean;
import com.iflytek.autofly.home.aidl.IMediaChangeCallBack;
import com.iflytek.autofly.home.aidl.ILrcRow;
// Declare any non-default types here with import statements
interface IMediaChangeInterFace {

        //媒体准备完成  (电台需要传)
        void mediaPrepared(in IMediaBean bean);
        //播放进度
         //type:  1：音乐  2：有声读物   3：电台
        void mediaProgressChange(in int type,in int progress);
        //缓存进度
         //type:  1：音乐  2：有声读物   3：电台
        void mediaProgressBuffer(in int type,in int progress);
        //总长度
         //type:  1：音乐  2：有声读物   3：电台
        void mediaProgressPrepare(in int type,in int totalProgress);
        //错误信息  (电台需要传)
         //type:  1：音乐  2：有声读物   3：电台
        void error(in int type,in String error);
        //播放状态 1：单曲循环  2：列表循环  3：随机播放
         //type:  1：音乐  2：有声读物   3：电台
        void mediaPlayModel(in int type,in int model);
        //播放媒体列表变化  (电台需要传)
         //type:  1：音乐  2：有声读物   3：电台
        void mediaPlayListChange(in int type,in List<IMediaBean> changedList);

        //歌词变化
        void mediaPlayLrcChange(in int type,in List<ILrcRow> lrcList);

        void registerOnCountListener(in int type,in IMediaChangeCallBack listener);
        void unregisterOnCountListener(in int type,in IMediaChangeCallBack listener);
}
