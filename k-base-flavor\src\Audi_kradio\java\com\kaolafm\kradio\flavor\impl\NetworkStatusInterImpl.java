package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.util.Log;

import com.kaolafm.kradio.lib.base.flavor.NetworkStatusInter;

public class NetworkStatusInterImpl implements NetworkStatusInter {

    String TAG = "NetworkStatusInterImpl";

    @Override
    public boolean getNetStatus(Context context) {
        boolean result = false;
        ConnectivityManager manager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo networkInfo = manager.getActiveNetworkInfo();
        if (networkInfo != null) {
            result = networkInfo.isAvailable();
        }
        Log.d(TAG, "getNetStatus " + result);
        return result;
    }

    @Override
    public String[] netAction() {
        return new String[]{/*ConnectivityManager.ACTION_NETWORK_CHANGED*/};
    }
}