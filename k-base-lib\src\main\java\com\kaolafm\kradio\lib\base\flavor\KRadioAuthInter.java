package com.kaolafm.kradio.lib.base.flavor;

public interface KRadioAuthInter {
    String METHOD_TTS = "tts";
    String METHOD_NETCHANGE = "netChange";
    String METHOD_LIVING = "living";

    /**
     * 初始化
     *
     * @return
     */
    boolean doInitCheckCanPlayInter();

    /**
     * 释放一些内容
     *
     * @return
     */
    boolean unInitCheckCanPlayInter();


    /**
     * 主动触发check
     *
     * @param args
     * @return
     */
    boolean doCheckAuth(Object... args);


    /**
     * 流量鉴权是否通过的状态  true表示通过   false表示不通过
     * @param args
     * @return
     */
    boolean authStatus(Object... args);



    /**
     * 网络状态 是否需要鉴权
     * @param args
     * @return
     */
    boolean netWorkStatus(Object... args);

}
