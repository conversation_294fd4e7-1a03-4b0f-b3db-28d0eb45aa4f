//package com.kaolafm.kradio.lib.bean;
//
//import com.google.gson.annotations.SerializedName;
//
//import org.greenrobot.greendao.annotation.Entity;
//import org.greenrobot.greendao.annotation.Id;
//
///**
// *  配置了按钮肯定会有动作对应，不考虑按钮动作为null的情况；
// *  应急广播泡泡处的按钮是固定的查看详情和取消；
// */
//@Entity
//public class CrashMessageButtonActionDto {
//    @Id
//    private String msgId;
//
//    @SerializedName("actionType")
//    int actionType; //0：do nothing；1：根据<id,type>跳转；2：根据url跳H5页面
//    @SerializedName("id")
//    int id;
//    @SerializedName("type")
//    int type;
//    @SerializedName("url")
//    String url;
//
//    @Override
//    public String toString(){
//        return "CrashMessageButtonActionBean{" +
//                "msgId=" + msgId +
//                ", actionType=" + actionType +
//                ", id=" + id +
//                ", type=" + type +
//                ", url='" + url + '\'' +
//                '}';
//    }
//}