package com.kaolafm.kradio.flavor.impl;

import com.kaolafm.kradio.lib.base.flavor.KRadioAudioFocusListenerInter;
import com.kaolafm.opensdk.player.logic.PlayerManager;

import static android.media.AudioManager.AUDIOFOCUS_LOSS;
import static android.media.AudioManager.AUDIOFOCUS_LOSS_TRANSIENT;

public class KRadioAudioFocusListenerImpl implements KRadioAudioFocusListenerInter {
    @Override
    public void onFocusChanged(int status) {
        if (status == AUDIOFOCUS_LOSS || status == AUDIOFOCUS_LOSS_TRANSIENT) {
            PlayerManager.getInstance().pause(); //强制暂停下
        }
    }
}
