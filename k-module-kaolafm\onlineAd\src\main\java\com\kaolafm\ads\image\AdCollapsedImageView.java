package com.kaolafm.ads.image;

import android.content.Context;
import androidx.annotation.Nullable;
import android.util.AttributeSet;

import com.bumptech.glide.Glide;
import com.kaolafm.ads.image.base.BaseAdImageView;

public class AdCollapsedImageView extends BaseAdImageView {

    public AdCollapsedImageView(Context context) {
        super(context);
    }

    public AdCollapsedImageView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public AdCollapsedImageView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public void loadAdContent(AdContentInfo adContentInfo) {
        mTvSkip.setText("收起");
        mTvSkip.setOnClickListener((v) -> {
            mTvSkip.setOnClickListener(null);
            mAdImageListener.onAdImageCollapsed();
        });
        super.loadAdContent(adContentInfo);
    }

}
