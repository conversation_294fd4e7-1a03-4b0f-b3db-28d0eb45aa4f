package com.kaolafm.kradio.categories;

import androidx.fragment.app.Fragment;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.category.FragmentFactory;
import com.kaolafm.kradio.category.all.AllCategory;
import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.operation.model.category.Category;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/4/16
 */

class AllCategoriesModel extends SubcategoryModel {

    public AllCategoriesModel() {
        super(null);
    }

    public void getTopTabsAndFragments(long showId, HttpCallback<AllCategory> callback) {
        mOperationRequest.getCategoryRoot(ResType.TYPE_ALL, new HttpCallback<List<Category>>() {
            @Override
            public void onSuccess(List<Category> categories) {
                if (!ListUtil.isEmpty(categories)) {
                    AllCategory allCategory = new AllCategory();
                    ArrayList<Fragment> fragments = new ArrayList<>();
                    String[] tabNames = new String[categories.size()];
                    for (int i = 0, size = categories.size(); i < size; i++) {
                        Category category = categories.get(i);
                        String name = category.getName();
                        tabNames[i] = name;
                        long categoryId;
                        try {
                            categoryId = Long.parseLong(category.getCode());
                        } catch (Exception e) {
                            categoryId = -1;
                        }
                        if (categoryId > 0 && showId > 0) {
                            if (categoryId == showId)
                                allCategory.showIndex = i;
                        } else {
                            // 一级导航栏设置落地页赋值
                            if (category.getIsLandingPage() == 1) {
                                allCategory.showIndex = i;
                            }
                        }
                        if ("AI电台".equals(name)) {
//                            fragments.add(FragmentFactory.createSubcategoryFragment(categoryId));
                            fragments.add(FragmentFactory.createRadioTabFragment(categoryId, 0, CategoryConstant.MEDIA_TYPE_RADIO));
                        } else {
                            fragments.add(FragmentFactory.createRadioTabFragment(categoryId, 0));
                        }
                    }
                    allCategory.fragments = fragments;
                    allCategory.tabNames = tabNames;
                    if (callback != null) {
                        callback.onSuccess(allCategory);
                    }
                } else {
                    if (callback != null) {
                        callback.onError(new ApiException("没有数据"));
                    }
                }
            }

            @Override
            public void onError(ApiException e) {
                if (callback != null) {
                    callback.onError(e);
                }
            }
        });
    }
}
