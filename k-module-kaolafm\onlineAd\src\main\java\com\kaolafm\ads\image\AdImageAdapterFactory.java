package com.kaolafm.ads.image;


import com.kaolafm.ads.image.base.BaseAdImageAdapter;

import java.lang.reflect.Constructor;

public class AdImageAdapterFactory {
    public static BaseAdImageAdapter create(String className) throws Exception {
        Class<? extends BaseAdImageAdapter> bannerClass = Class.forName(className)
                .asSubclass(BaseAdImageAdapter.class);
        Constructor<?> bannerConstructor = bannerClass.getDeclaredConstructor((Class[]) null);
        bannerConstructor.setAccessible(true);
        return (BaseAdImageAdapter) bannerConstructor.newInstance();
    }
}
