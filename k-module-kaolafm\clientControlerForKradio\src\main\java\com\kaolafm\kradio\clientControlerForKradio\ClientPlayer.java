package com.kaolafm.kradio.clientControlerForKradio;


import com.kaolafm.sdk.core.Music;
import com.kaolafm.sdk.core.PlayListener;

import java.util.List;

/**
 * <AUTHOR>
 **/
public interface ClientPlayer {

    void resume();

    void play(PlayParam playParam);

    void play(List<PlayParam> playParams, int beginIndex);

    void pause();

    void playNext();

    void playPrev();

    int getPlayState();

    long getProgress();

    void forward(int second);

    void backward(int second);

    boolean hasPrev();

    boolean hasNext();

    PlayParam getPlayParam();

    void registerPlayListener(PlayListener playListener);
    void unregisterPlayListener(PlayListener playListener);

    Music getMusicInfo();

    void stop();

    void setNeedRequestAudioFocusOnPlayerInit(boolean needRequestAudioFocusOnPlayerInit);


    /**
     * 播放参数
     */
    public static class PlayParam {
        public long id;
        public int resType;
        public String title;
        public String img;
        public String fm;

        @Override
        public String toString() {
            return "PlayParam{" +
                    "id=" + id +
                    ", resType=" + resType +
                    ", title='" + title + '\'' +
                    ", img='" + img + '\'' +
                    ", fm='" + fm + '\'' +
                    '}';
        }
    }
}
