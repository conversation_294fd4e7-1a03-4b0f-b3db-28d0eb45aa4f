package com.kaolafm.kradio.flavor.broadcast;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.util.Log;

import com.alibaba.fastjson.JSONObject;
import com.bumptech.glide.Glide;
import com.bumptech.glide.request.FutureTarget;
import com.bumptech.glide.request.target.Target;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.utils.UrlUtil;
import com.kaolafm.opensdk.player.logic.model.item.InvalidPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import java.io.ByteArrayOutputStream;
import java.io.File;

import io.reactivex.Observable;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;

/**
 * 播放信息info中数据为json格式，定义如下
 * key	value类型	描述
 * type	string	指明发出消息的模块或接收消息的广播
 * data	json	各模块定义的json数据
 * detail	json	各模块定义的附件数据详细信息
 * <p>
 * 播放信息中的图片bitmap中的数据为图片的btye数组（大小务必控制在20k以下）
 * <p>
 * 说明：播放信息和图片的发送为同一广播，根据不同的ExtraName发送相应内容。可以同时发送播放信息和图片信息，也可以分开单独发送（图片信息的发送请控制频率，同样的图不要重复发送）
 * <p>
 * data:
 * type	int	音源类型
 * -1：默认值
 * 0: 在线音乐
 * 1: 本地音乐(废弃)
 * 2: 蓝牙音乐
 * 3: 喜马拉雅
 * 4: FM收音机
 * 5: U盘音乐
 * 6：酷狗1.0音乐(废弃)
 * 7：网易云音乐
 * 8：雷石
 * 9：酷狗MV
 * 10：在线广播（云听）
 * 11：公版云听
 * uniqueId	String	唯一值（可为空）
 * albumId	String	专辑Id（可为空）
 * name	string	歌曲名称
 * title	string	标题名称（在线音乐时为：“歌手 - 歌名”；其他音源时同name即可）
 * artist	string	艺术家名称（可为空）
 * imagUrl	String	图片资源id或者在线url
 * duration	int	时长单位秒
 * state	int	播放状态
 * -1：默认值
 * 0：暂停
 * 1：播放
 * 2：停止
 * <p>
 * detail:
 * length	int	图片数据的长度
 * offset	int	图片数据的偏移（默认偏移为length值）
 * format	string	图片格式如”jpeg”
 */
public class DashboardSendHelper {

    private static final String TAG = "DashboardSendHelper";

    private static DashboardSendHelper instance;

    private DashboardSendHelper() {
    }

    public static DashboardSendHelper getInstance() {
        if (instance == null) {
            synchronized (DashboardSendHelper.class) {
                if (instance == null) {
                    instance = new DashboardSendHelper();
                }
            }
        }
        return instance;
    }

    private String cacheTitle = "";


    public static final int STATUS_PAUSE = 0;
    public static final int STATUS_PLAYING = 1;
    public static final int STATUS_STOP = 2;
    public static final int STATUS_IDLE = -1;
    /**
     * 播放状态
     * -1：默认值
     * 0：暂停
     * 1：播放
     * 2：停止
     *
     * @param playItem
     * @param playStatus 见注释
     * @return
     */
    public void send(PlayItem playItem, int playStatus) {
        if (playItem == null || playItem instanceof InvalidPlayItem) {
            Log.i(TAG, "mediaInfo: playItem empty");
            return;
        }

        //data
        JSONObject data = new JSONObject();
        data.put("type", 11);
        data.put("name", playItem.getTitle());
        data.put("title", playItem.getTitle());
//        data.put("artist", playItem.getAlbumTitle() == null ? playItem.getRadioName() == null ? "" : playItem.getRadioName() : playItem.getAlbumTitle());
        data.put("duration", playItem.getDuration() / 1000);
        data.put("state", playStatus);

        //detail
        JSONObject detail = new JSONObject();

        //info
        JSONObject info = new JSONObject();
        info.put("type", "music");

        addJsonParams(playItem, data, detail, info);
    }

    @SuppressLint("CheckResult")
    private void addJsonParams(PlayItem playItem, JSONObject data, JSONObject detail, JSONObject info) {
        Observable.create((ObservableOnSubscribe<Bitmap>) emitter -> {
            Bitmap bitmap = null;
            try {
                String originPicUrl = playItem.getPicUrl();
                String imgUrl = UrlUtil.getCustomPicUrl(UrlUtil.PIC_100_100, originPicUrl);
                String extension = originPicUrl.substring(originPicUrl.lastIndexOf("."));
                detail.put("format", extension);
                data.put("imageUrl", imgUrl);
                Log.i(TAG, "format" + extension);
                Log.i(TAG, "url=" + imgUrl);
                FutureTarget<Bitmap> target = Glide.with(AppDelegate.getInstance().getContext())
                        .asBitmap()
                        .load(imgUrl)
                        .submit(Target.SIZE_ORIGINAL, Target.SIZE_ORIGINAL);
                bitmap = target.get();
            } catch (Exception e) {
                e.printStackTrace();
            }
            emitter.onNext(bitmap);
        }).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread()).subscribe(bitmap -> {
            byte[] bytes = bitmap2Bytes(bitmap);
            if (bytes.length != 0) {
                detail.put("length", bytes.length);
                detail.put("offset", bytes.length);
                info.put("data", data);
                info.put("detail", detail);

                String jsonInfo = info.toJSONString();
                sendMediaInfo(jsonInfo);

                if (cacheTitle != null && cacheTitle.equals(playItem.getTitle())) {
                    sendCoverImage(bytes);
                    cacheTitle = playItem.getTitle();
                }
            }
        });
    }

    private byte[] bitmap2Bytes(Bitmap bitmap) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        bitmap.compress(Bitmap.CompressFormat.JPEG, 100, baos);
        return baos.toByteArray();
    }


    private void sendMediaInfo(String infoJson) {
        Log.i(TAG, "sendMediaInfo:" + infoJson);
        Intent intent = new Intent();
        intent.setAction("com.leapmotor.transfer.MEDIA.INFO");
        intent.putExtra("info", infoJson);
        intent.addFlags(Intent.FLAG_ACTIVITY_PREVIOUS_IS_TOP);
        intent.addFlags(Intent.FLAG_RECEIVER_FOREGROUND);
        AppDelegate.getInstance().getContext().sendBroadcast(intent);
    }

    private void sendCoverImage(byte[] image) {
        Log.i(TAG, "sendCoverImage:" + image);
        Intent intent = new Intent();
        intent.setAction("com.leapmotor.transfer.MEDIA.INFO");
        intent.putExtra("bitmap", image);
        intent.addFlags(Intent.FLAG_ACTIVITY_PREVIOUS_IS_TOP);
        intent.addFlags(Intent.FLAG_RECEIVER_FOREGROUND);
        AppDelegate.getInstance().getContext().sendBroadcast(intent);
    }
}
