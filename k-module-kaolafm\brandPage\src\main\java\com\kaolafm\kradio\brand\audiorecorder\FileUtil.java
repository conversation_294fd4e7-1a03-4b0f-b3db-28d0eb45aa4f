package com.kaolafm.kradio.brand.audiorecorder;

import android.os.Environment;
import android.text.TextUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * 管理录音文件的类
 */
public class FileUtil {

    private static String rootPath = "kradio";
    //原始文件(不能播放)
    private static String AUDIO_PCM_BASEPATH = "/" + rootPath + "/pcm/";
    //可播放的高质量音频文件
    private static String AUDIO_WAV_BASEPATH = "/" + rootPath + "/wav/";

    private static void setRootPath(String rootPath) {
        FileUtil.rootPath = rootPath;
        if (rootPath == null || rootPath.equals("")) {
            AUDIO_PCM_BASEPATH = "/pcm/";
            AUDIO_WAV_BASEPATH = "/wav/";
        } else {
            AUDIO_PCM_BASEPATH = "/" + rootPath + "/pcm/";
            AUDIO_WAV_BASEPATH = "/" + rootPath + "/wav/";
        }
    }

    public static String getPcmFileAbsolutePath(String fileDir, String fileName) {
        if (TextUtils.isEmpty(fileName)) {
            throw new NullPointerException("fileName isEmpty");
        }

        String mAudioRawPath = "";

        if (!fileName.endsWith(".pcm")) {
            fileName = fileName + ".pcm";
        }
        String fileBasePath = fileDir + AUDIO_PCM_BASEPATH;
        File file = new File(fileBasePath);
        //创建目录
        if (!file.exists()) {
            file.mkdirs();
        }
        mAudioRawPath = fileBasePath + fileName;

        return mAudioRawPath;
    }

    public static String getWavFileAbsolutePath(String fileDir, String fileName) {
        if (fileName == null) {
            throw new NullPointerException("fileName can't be null");
        }


        String mAudioWavPath = "";

        if (!fileName.endsWith(".wav")) {
            fileName = fileName + ".wav";
        }
        String fileBasePath = fileDir + AUDIO_WAV_BASEPATH;
        File file = new File(fileBasePath);
        //创建目录
        if (!file.exists()) {
            file.mkdirs();
        }
        mAudioWavPath = fileBasePath + fileName;

        return mAudioWavPath;
    }

    /**
     * 判断是否有外部存储设备sdcard
     *
     * @return true | false
     */
    public static boolean isSdcardExit() {
        if (Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED))
            return true;
        else
            return false;
    }

    /**
     * 获取全部pcm文件列表
     *
     * @return
     */
    public static List<File> getPcmFiles() {
        List<File> list = new ArrayList<>();
        String fileBasePath = Environment.getExternalStorageDirectory().getAbsolutePath() + AUDIO_PCM_BASEPATH;

        File rootFile = new File(fileBasePath);
        if (!rootFile.exists()) {
        } else {

            File[] files = rootFile.listFiles();
            for (File file : files) {
                list.add(file);
            }

        }
        return list;

    }

    /**
     * 获取全部wav文件列表
     *
     * @return
     */
    public static List<File> getWavFiles() {
        List<File> list = new ArrayList<>();
        String fileBasePath = Environment.getExternalStorageDirectory().getAbsolutePath() + AUDIO_WAV_BASEPATH;

        File rootFile = new File(fileBasePath);
        if (!rootFile.exists()) {
        } else {
            File[] files = rootFile.listFiles();
            for (File file : files) {
                list.add(file);
            }

        }
        return list;
    }
}