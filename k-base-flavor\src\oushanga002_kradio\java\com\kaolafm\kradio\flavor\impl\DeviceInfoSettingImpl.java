package com.kaolafm.kradio.flavor.impl;

import android.provider.Settings;
import android.util.Log;

import com.kaolafm.kradio.lib.base.AppDelegate;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-05-22 20:50
 ******************************************/
public final class DeviceInfoSettingImpl implements DeviceInfoSetting {
    public static final String VEHICLE_VIN = "vehicle_vin";
    public static final String KEY_TUID = "navi_tuid";
    public static final String SYS_CAR_TYPE = "sys_car_type";
    @Override
    public void setInfoForSDK(Context context) {
        String vin = Settings.Global.getString(AppDelegate.getInstance().getContext().getContentResolver(),
                VEHICLE_VIN);
        Log.i("zsj", "setInfoForSDK: "+vin );
        String strCarType = Settings.Global.getString(AppDelegate.getInstance().getContext().getContentResolver(), SYS_CAR_TYPE);
        DeviceInfoUtil.setDeviceIdAndCarType(vin, strCarType);
    }
}
