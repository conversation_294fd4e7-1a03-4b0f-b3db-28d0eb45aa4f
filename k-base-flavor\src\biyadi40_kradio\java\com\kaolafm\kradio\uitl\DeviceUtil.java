package com.kaolafm.kradio.uitl;

import android.content.Context;
import android.content.pm.PackageManager;

import com.kaolafm.kradio.lib.base.AppDelegate;

public class DeviceUtil {

    /**
     * 获得当前应用版本号
     *
     * @param context
     * @return
     */
    public static int getVersionCode(Context context) {
        int versionCode = 0;
        try {
            context = context == null ? AppDelegate.getInstance().getContext() : context;
            versionCode = context.getPackageManager().getPackageInfo(
                    context.getPackageName(), 0).versionCode;
        } catch (PackageManager.NameNotFoundException e) {
        }
        return versionCode;
    }
}
