package com.kaolafm.kradio.home.comprehensive;


import android.util.Log;

import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.opensdk.player.logic.PlayerManager;

import java.lang.ref.WeakReference;

/**
 * <AUTHOR>
 * @date 2019-09-01
 */
public class HomeOnPlayerInitCompleteListener implements IPlayerInitCompleteListener {

    private WeakReference<HorizontalHomePlayerFragment> weakReference;

    private int type;

    /**
     * 初始化首页播放逻辑类型
     */
    public static final int INIT_PBP_TYPE = 2;

    public HomeOnPlayerInitCompleteListener(HorizontalHomePlayerFragment horizontalHomePlayerFragment) {
        weakReference = new WeakReference<>(horizontalHomePlayerFragment);
    }

    @Override
    public void onPlayerInitComplete(boolean b) {
        Log.i("kradio.home", "onPlayerInitComplete-------->isSuccess = " + b);
        PlayerManager.getInstance().removePlayerInitComplete(this);
        HorizontalHomePlayerFragment horizontalHomePlayerFragment = weakReference.get();
        if (horizontalHomePlayerFragment != null) {
            if (type == INIT_PBP_TYPE) {
                horizontalHomePlayerFragment.initPlayerContext();
            } else {
                horizontalHomePlayerFragment.onPlayerInitComplete(b);
            }
        }
    }

    public void setType(int type) {
        this.type = type;
    }
}
