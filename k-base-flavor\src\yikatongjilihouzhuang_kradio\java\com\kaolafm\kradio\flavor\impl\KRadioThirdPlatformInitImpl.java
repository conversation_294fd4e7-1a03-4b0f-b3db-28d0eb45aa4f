package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.content.Intent;
import android.util.Log;
import com.ecarx.sdk.ECarX;
import com.ecarx.sdk.ECarXAPIBase;
import com.ecarx.sdk.mediacenter.MediaCenterAPI;
import com.ecarx.sdk.oauth2.OAuth2API;
import com.ecarx.sdk.openapi.ECarXApiClient;
import com.kaolafm.kradio.common.event.LogoutBindEvent;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioThirdPlatformInitInter;
import com.kaolafm.kradio.lib.utils.ClazzUtil;
import com.kaolafm.kradio.widget.WidgetUpdateManager;
import com.kaolafm.kradio.widget.YiKaTongAppWidgetProvider;
import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-03-19 20:47
 ******************************************/
public final class KRadioThirdPlatformInitImpl implements KRadioThirdPlatformInitInter {
    private static final String TAG = "kradio.ecarx";

    @Override
    public boolean initThirdPlatform(Object... args) {
        EventBus.getDefault().register(this);
        Log.i(TAG, "initThirdPlatform:");
        Context context = (Context) args[0];
        initMediaCenterAPI(context);
//        initOAuth();
//        try {
//            ECarXApiClient.getInstance().init(context);
//        } catch (Exception e) {
//            e.printStackTrace();
//        } catch (Error error) {
//            error.printStackTrace();
//        }

        try {
            Log.i(TAG, "onAPIReady------>start");
            OAuth2API.getInstance().init(context, new ECarXApiClient.Callback() {
                @Override
                public void onAPIReady(boolean ready) {
                    Log.i(TAG, "onAPIReady------>ready = " + ready);
//                    if (ready) {
//                        Log.i(TAG, "账号互通 SDK初始化成功");
//                        initLogin(context);
//                    } else {
//                        Log.i(TAG, "账号互通 SDK初始化失败");
//                    }
                }
            });
        } catch (Exception e) {
        }

        //一卡通widget
//        WidgetUpdateManager.getInstance().init();
//        registerLogoutListener(context);
        return true;
    }

    private void registerLogoutListener(final Context context) {
        UserInfoManager.getInstance().addUserInfoStateListener(new UserInfoManager.IUserInfoStateListener() {
            @Override
            public void userLogin() {
                Log.i(ECarX.TAG, "UserInfoStateListener:userLogin:");
            }

            @Override
            public void userLogout() {
                Log.i(ECarX.TAG, "UserInfoStateListener:userLogout:");
                UserInfoManager.getInstance().removeUserInfoStateListener(this);
                ECarX.getInstance(context).logout();
            }

            @Override
            public void userCancel() {
                Log.i(ECarX.TAG, "UserInfoStateListener:userBound:");
            }

        });
    }

    @Override
    public boolean destroyThirdPlatform(Object... args) {
        Context context = (Context) args[0];
        WidgetUpdateManager.getInstance().destroy();
        WidgetUpdateManager.getInstance().updateWidgetInfo(null, null);
        if (context != null) {
            Intent intent = new Intent();
            intent.setAction(YiKaTongAppWidgetProvider.EXIT_ACTION);
            context.sendBroadcast(intent);
        }
        return true;
    }

//    /**
//     * 初始化账号sdk
//     */
//    private void initOAuth() {
//        Log.i(ECarX.TAG, "initOAuth:");
////        IMCApi imcApi = MCOAuthApiUtils.getMCApi();//初始化
////        imcApi.setLogEnable(true);//打开Log
////        imcApi.registerApp("58946fa69154a14398e63334b5d91bc3");//注册
////        Log.i(ECarX.TAG, "MCAppSupportAPI Version: " + imcApi.getMCAppSupportAPI());
//    }

    private void initMediaCenterAPI(Context context) {
        Log.i(TAG, "initMediaCenterAPI--------->API Version = " + ECarXAPIBase.VERSION_INT);
        try {
            MediaCenterAPI.get(context).init(context, new ECarXApiClient.Callback() {
                @Override
                public void onAPIReady(boolean b) {
                    Log.i(TAG, "onAPIReady--------->result = " + b);
                }
            });
        } catch (NoClassDefFoundError e) {
            Log.i(TAG, "initMediaCenterAPI--------->error = " + e.getLocalizedMessage());
        } catch (Throwable t) {
            Log.i(TAG, "initMediaCenterAPI--------->error = " + t.getLocalizedMessage());
        }
    }

//    private void initLogin(Context context) {
    // 解决https://app.huoban.com/tables/2100000007530121/items/2300001290467567?userId=1229522问题
//        boolean hasLogin = OAuth2API.getInstance().hasLogin();
//        if (!hasLogin) {
//            ECarX.getInstance(context).logout();
//        }
//        Log.i(TAG, "setLoginListener------>start hasLogin = " + hasLogin);

//        OAuth2API.getInstance().setLoginListener(new ILoginListener() {
//            @Override
//            public void onLogin() {
//                //账号登录
//                Log.i(TAG, "ecarx:onLogin: ");
//                ECarX.getInstance(context).login();
//            }
//
//            @Override
//            public void onLogout() {
//                //账号登出
//                Log.i(TAG, "ecarx:onLogout: ");
//                ECarX.getInstance(context).logout();
//            }
//        });
//    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onLogoutBindEvent(LogoutBindEvent event) {
        Log.i(TAG, "onLogoutBindEvent: event=" + event);
        if (event.status == LogoutBindEvent.LOGOUT) {
            ECarX.getInstance(AppDelegate.getInstance().getContext()).logout();
        }
    }
}
