package com.kaolafm.kradio.lib.base.ui;

/**
 * 用于ShowHideFragment和ViewPagerFragment简化代码的接口
 */
public interface IFragmentVisibility {

    /**
     * 当Fragment真正对用户可见时回调
     * ShowHideFragment:当显示切换时
     * ViewPagerFragment:当ViewPager定位到本Fragment时
     */
    public void onUserVisible();

    /**
     * 当Fragment对用户不可见时
     * ShowHideFragment:当隐藏切换时
     * ViewPagerFragment:当ViewPager定位到非本Fragment时
     */
    public void onUserInvisible();

    /**
     * 检查是否真正可见
     *
     * @return true:可见
     */
    public boolean isFinalUserVisible();
}
