<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="78px"
    android:background="@drawable/shape_hw_toast_layout_bg"
    android:gravity="center"
    android:layout_gravity="center"
    >

    <TextView
        android:id="@+id/toast_msg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="30px"
        android:gravity="center"
        android:textColor="@color/white"
        android:paddingLeft="40px"
        android:paddingRight="40px"
        android:paddingTop="20px"
        android:paddingBottom="20px"
        />

</LinearLayout>
