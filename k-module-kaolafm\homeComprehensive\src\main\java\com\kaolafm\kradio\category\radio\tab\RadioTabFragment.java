package com.kaolafm.kradio.category.radio.tab;

import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import android.util.Log;
import android.view.View;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.category.ErrorCode;
import com.kaolafm.kradio.category.base.BaseTabFragment;
import com.kaolafm.kradio.category.base.TabContract;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.categories.CategoryConstant;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.util.ReportConstants;

import java.util.List;

/**
 * 专栏节目、在线广播的二级页面
 *
 * <AUTHOR>
 **/
public class RadioTabFragment extends BaseTabFragment {
    private static final String TAG = "RadioTabFragment";
    private long mSonCode;
    private String[] titles;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public void initView(View view) {
        super.initView(view);
        mStbSubcategoryTabTitle.setSnapOnTabClick(true);
    }

    @Override
    public void initArgs() {
        super.initArgs();
        Bundle arg = getArguments();
        if (arg != null) {
            mSonCode = arg.getLong(CategoryConstant.SUBCATEGORY_ID);
        }
    }

    @Override
    protected int getLayoutId_Tow() {
        return 0;
    }

    @Override
    public void showError(Exception e) {
//        super.showError(e);
        Log.i(TAG, "showError: error = " + e.getMessage());
        String str = null;
        if (e instanceof ApiException) {
            switch (((ApiException) e).getCode()) {
                case ErrorCode.NO_NET:
                    isLoaded = false;
                    //str = ResUtil.getString(R.string.no_net_work_str);
                    showNoNetWorkView();
                    break;
                case ErrorCode.NO_SUBCATEGORY:
                case ErrorCode.TYPE_ERROR:
                    str = ResUtil.getString(R.string.error_subcategory_is_null);
                    break;
                default:
            }
        }
        if (str != null) {
            ToastUtil.showError(getContext(), str);
        }

    }


    @Override
    protected TabContract.IPresenter createPresenter() {
        return new RadioTabPresenter(this, mSonCode);
    }

    @Override
    protected void showAccordingToScreen(int orientation) {
        super.showAccordingToScreen(orientation);
        int textSize = ResUtil.getDimen(R.dimen.subtitle_tab_title_size);
//        changeTabTitleTextSize(textSize,textSize);
    }

    public void changeTabTitleTextSize(int noraml, int selected) {
        mStbSubcategoryTabTitle.setTextsize(ScreenUtil.px2dp(noraml));
      //  mStbSubcategoryTabTitle.setTextSelectSize(ScreenUtil.px2dp(selected));
    }

    @Override
    public void showData(String[] titles, List<Fragment> fs, int showIndex) {
        super.showData(titles, fs, showIndex);
        this.titles = titles;
        if (!ListUtil.isEmpty(titles)) {
            for (String title : titles) {
                ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_All_CLASSIFICATION_SECONDARY_NAVIGATION, title, getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
            }
        }
    }

    @Override
    protected void onTabSelected(int position) {
        super.onTabSelected(position);
        if (ListUtil.isEmpty(titles) || titles.length <= position) {
            return;
        }
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_All_CLASSIFICATION_SECONDARY_NAVIGATION, titles[position], getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
    }
}
