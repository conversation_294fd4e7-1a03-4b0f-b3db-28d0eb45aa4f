//package com.kaolafm.kradio.flavor.impl;
//
//import android.media.AudioManager;
//import android.util.Log;
//import android.view.KeyEvent;
//
//import com.aptiv.input.AptivInputInterface;
//import com.aptiv.input.AptivInputManager;
//import com.kaolafm.kradio.lib.base.AppDelegate;
//import com.kaolafm.kradio.lib.base.flavor.KRadioMediaSessionInter;
//import com.kaolafm.sdk.core.mediaplayer.AudioStatusManager;
//import com.kaolafm.sdk.core.mediaplayer.OnAudioFocusChangeInter;
//import com.kaolafm.utils.MediaButtonManagerUtil;
//
//import java.lang.ref.WeakReference;
//
///******************************************
// * 类描述:
// *
// * @version: V1.0
// * @author: yangshaoning
// * @time: 2019-05-22 20:59
// ******************************************/
//public final class KRadioMediaSessionImpl implements KRadioMediaSessionInter {
//    private static final String TAG = "KRadioMediaSessionImpl";
//    private MyOnAudioFocusChangeInter myOnAudioFocusChangeInter;
//
//    private AptivInputManager mAptivInputManager;
//    private AptivInputInterface mAptivInputInterface;
//    private ConnectCallback mConnectCallback;
//    private KeyEventCallback mAptivKeyEventCallback;
//    private MediaButtonManagerUtil mMediaButtonManagerUtil;
//
//    public KRadioMediaSessionImpl() {
//        mMediaButtonManagerUtil = new MediaButtonManagerUtil();
//        myOnAudioFocusChangeInter = new MyOnAudioFocusChangeInter(this);
//        AudioStatusManager.getInstance().registerOnAudioFocusChangeListener(myOnAudioFocusChangeInter);
//
//        mConnectCallback = new ConnectCallback();
//        mAptivKeyEventCallback = new KeyEventCallback();
//        mAptivInputInterface = new AptivInputInterface(AppDelegate.getInstance().getContext(), mConnectCallback);
//        mAptivInputInterface.connect();
//    }
//
//    @Override
//    public void registerMediaSession(Object... args) {
//        Log.i(TAG, "registerKeyInfo----------->" + args[0]);
//        if (mAptivInputManager != null) {
//            Log.i(TAG, "setActive ");
//            mAptivInputManager.setActive(true, AptivInputManager.SOURCE_TUNER);
//        }
//    }
//
//    @Override
//    public void unregisterMediaSession(Object... args) {
//        Log.i(TAG, "unRegisterKeyInfo----------->" + args[0]);
//        if (mAptivInputManager != null) {
//            Log.i(TAG, "setActive ");
//            mAptivInputManager.setActive(false, AptivInputManager.SOURCE_TUNER);
//        }
//    }
//
//    @Override
//    public void release(Object... args) {
//        AudioStatusManager.getInstance().unregisterOnAudioFocusChangeListener(myOnAudioFocusChangeInter);
//        mAptivInputInterface.disConnect();
//    }
//
//    /* KeyEventCallback */
//    private class KeyEventCallback implements AptivInputManager.AptivKeyEventCallback {
//        @Override
//        public boolean onKeyEvent(int keyCode, int keyAction) {
//            Log.i(TAG, "keyCode:" + keyCode + ";keyAction" + keyAction);
//            if (keyAction == KeyEvent.ACTION_UP) {
//                handleKeyEvent(keyCode, keyAction);
//            }
//            return true;
//        }
//    }
//
//    private static class MyOnAudioFocusChangeInter implements OnAudioFocusChangeInter {
//        private WeakReference<KRadioMediaSessionImpl> weakReference;
//
//        public MyOnAudioFocusChangeInter(KRadioMediaSessionImpl kRadioMediaSessionImpl) {
//            weakReference = new WeakReference<>(kRadioMediaSessionImpl);
//        }
//
//        @Override
//        public void onAudioFocusChange(int i) {
//            Log.i(TAG, "onAudioFocusChange--------> i = " + i);
//            KRadioMediaSessionImpl kRadioMediaSessionImpl = weakReference.get();
//            if (kRadioMediaSessionImpl == null) {
//                return;
//            }
//            if (i == AudioManager.AUDIOFOCUS_GAIN) {
//                kRadioMediaSessionImpl.registerMediaSession(AppDelegate.getInstance().getContext());
//            } else if (i == AudioManager.AUDIOFOCUS_LOSS || i == AudioManager.AUDIOFOCUS_LOSS_TRANSIENT) {
//                kRadioMediaSessionImpl.unregisterMediaSession(AppDelegate.getInstance().getContext());
//            }
//        }
//    }
//
//    private class ConnectCallback implements AptivInputInterface.ConnectionCallback {
//        @Override
//        public void onConncted() {
//            Log.i(TAG, "onConncted");
//            /* registerKeyEventCallback */
//            mAptivInputManager = mAptivInputInterface.getManager();
//            if (mAptivInputManager != null) {
//                Log.i(TAG, "register KeyEventCallback ");
//                mAptivInputManager.registerKeyEventCallback(mAptivKeyEventCallback, AptivInputManager.SOURCE_TUNER);
//                Log.i(TAG, "setActive ");
//                mAptivInputManager.setActive(true, AptivInputManager.SOURCE_TUNER);
//            }
//        }
//
//        @Override
//        public void onDisconnect() {
//            Log.i(TAG, "onDisconnect");
//            /* unregisterKeyEventCallback */
//            if (mAptivInputManager != null) {
//                Log.i(TAG, "unregister KeyEventCallback ");
//                mAptivInputManager.unregisterKeyEventCallback(mAptivKeyEventCallback, AptivInputManager.SOURCE_TUNER);
//            }
//        }
//    }
//
//    private void handleKeyEvent(int keyCode, int keyAction) {
//        mMediaButtonManagerUtil.manageMediaButtonClick(keyCode);
//    }
//}