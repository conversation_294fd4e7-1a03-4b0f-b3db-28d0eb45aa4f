package com.alibaba.android.arouter.base;

import java.util.TreeMap;

/**
 * TreeMap with unique key.
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2017/2/22 下午5:01
 */
public class UniqueKeyTreeMap<K, V> extends TreeMap<K, V> {
    private String tipText;

    public UniqueKeyTreeMap(String exceptionText) {
        super();

        tipText = exceptionText;
    }

    @Override
    public V put(K key, V value) {
        if (containsKey(key)) {
            throw new RuntimeException(String.format(tipText, key));
        } else {
            return super.put(key, value);
        }
    }
}
