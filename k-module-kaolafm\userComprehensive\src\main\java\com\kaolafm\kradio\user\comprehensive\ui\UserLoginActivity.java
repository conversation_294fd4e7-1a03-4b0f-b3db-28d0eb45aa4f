package com.kaolafm.kradio.user.comprehensive.ui;

import android.app.Activity;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;
import androidx.constraintlayout.widget.Group;
import androidx.constraintlayout.widget.Guideline;
import androidx.fragment.app.DialogFragment;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.kaolafm.kradio.common.base.BaseSkinAppCompatActivity;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.utils.CommonUtils;
import com.kaolafm.kradio.component.ComponentClient;
import com.kaolafm.kradio.component.ui.base.skin.SkinHelper;
import com.kaolafm.kradio.constant.MyOrderComponentConst;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.LoginIntercepter;
import com.kaolafm.kradio.lib.base.flavor.UserCenterInter;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.lib.dialog.Dialogs;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.purchase.PayManager;
import com.kaolafm.kradio.purchase.constant.PayConst;
import com.kaolafm.kradio.purchase.model.PayResult;
import com.kaolafm.kradio.purchase.observer.VipPayListener;
import com.kaolafm.kradio.user.UserInfoManager;
import com.kaolafm.kradio.user.ui.AccountLoginModel;
import com.kaolafm.kradio.user.ui.IUserLoginView;
import com.kaolafm.kradio.user.ui.UserLoginPresenter;
import com.kaolafm.opensdk.api.login.model.UserInfo;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonClickReportEvent;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.util.ReportConstants;

import java.util.ArrayList;
import java.util.concurrent.atomic.AtomicBoolean;
import static com.kaolafm.report.event.ButtonClickReportEvent.BUTTON_JOIN_NOW;
import static com.kaolafm.report.event.ButtonClickReportEvent.BUTTON_RENEW;
import static com.kaolafm.report.event.ButtonClickReportEvent.BUTTON_SIGN_OUT;

import org.greenrobot.eventbus.EventBus;

@Route(path = RouterConstance.LOGIN_COMPREHENSIVE_URL)
public class UserLoginActivity extends BaseSkinAppCompatActivity<UserLoginPresenter> implements IUserLoginView {
    private static final String TAG = "UserLoginActivity";
    private static final String TAG_THIRD = "THIRD_UserLoginActivity";

    Group mLoginView;
    View rootView;
    Group mLoginSuccessView;
    ImageView mUserAvatar;
    ImageView message_back_mine;
    TextView mUserName;
    View mLogoutButton;
    ConstraintLayout mRootLayout;
    FrameLayout mContentFrameLayout;
    FrameLayout mContentQrFrameLayou;
    ViewGroup mCustomerViewLayout;
    Guideline mLandGuideLine;
    Guideline mPortGuideLine;
    Guideline mScannedGuideLine;
    View mVipView;
    ImageView mMyOrder;
    TextView mVipTitle;
    TextView mVipSubTitle;
    TextView mToNext;
    ConstraintLayout mUserInfoRootView;
    ImageView mTitleHeader;
    ImageView mUserNameHeader;
    View mQrScannedLayout;
    ImageView mScannedUserAvatar;
    TextView mScannedUserName;

    private FrameLayout mGxyQrLayout;
    private static final String TAG_GXYQR_FM = "tag_gxyqr_fm";

    private ArrayList<BaseFragment> mFragments = new ArrayList<>();

    private ThirdLoginFragment mThirdLoginFragment;

    private LoginIntercepter mLoginIntercepter;
    private View mCustomerChildView;

    private AtomicBoolean showingQrScannedLayout = new AtomicBoolean(false);
    // 解决ZMKQ-5326
    private boolean mNeedShowQrScanLayout = true;

    private boolean mShowingGxyQrLayout = false;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        CommonUtils.getInstance().initGreyStyle(getWindow());
        mLoginIntercepter = ClazzImplUtil.getInter("LoginIntercepterImpl");
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_user_login;
    }

    @Override
    public int getLayoutId_Tow() {
        return R.layout.activity_user_login_two;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        addThirdQRView();

        mLoginView= findViewById(R.id.login_view);
        rootView= findViewById(R.id.rootView);
        mLoginSuccessView= findViewById(R.id.login_success_view);
        mUserAvatar= findViewById(R.id.user_avatar);
        message_back_mine= findViewById(R.id.message_back_mine);
        mUserName= findViewById(R.id.user_name);
        mLogoutButton= findViewById(R.id.btn_logout);
        mRootLayout= findViewById(R.id.user_login_main_layout);
        mContentFrameLayout= findViewById(R.id.fl_container);
        mContentQrFrameLayou= findViewById(R.id.fl_container_qr);
        mCustomerViewLayout= findViewById(R.id.user_login_customer_view_layout);
        mLandGuideLine= findViewById(R.id.land_guideline);
        mPortGuideLine= findViewById(R.id.port_guideline);
        mScannedGuideLine= findViewById(R.id.scanned_port_guideline);
        mVipView= findViewById(R.id.vip_view);
        mMyOrder= findViewById(R.id.user_my_order);
        mVipTitle= findViewById(R.id.vip_title);
        mVipSubTitle= findViewById(R.id.vip_subtitle);
        mToNext= findViewById(R.id.to_next);
        mUserInfoRootView= findViewById(R.id.user_info_root_layout);
        mTitleHeader= findViewById(R.id.title_header);
        mUserNameHeader= findViewById(R.id.user_name_header);
        mQrScannedLayout= findViewById(R.id.scanned_user_layout);
        mScannedUserAvatar= findViewById(R.id.scanned_user_avatar);
        mScannedUserName= findViewById(R.id.scanned_user_name);

        mLogoutButton.setOnClickListener(v -> {
            if (AntiShake.check(v.getId())) {
                return;
            }

            logout();
            //点击退出按钮事件上报
            ButtonClickReportEvent event = new ButtonClickReportEvent(BUTTON_SIGN_OUT);
            ReportHelper.getInstance().addEvent(event);
        });
        mMyOrder.setOnClickListener(v -> {
            if (AntiShake.check(v.getId())) {
                return;
            }
            //我的订单
            ComponentClient.obtainBuilder(MyOrderComponentConst.NAME)
                    .setActionName(MyOrderComponentConst.START_ACTIVITY)
                    .addParam("context", UserLoginActivity.this)
                    .build().callAsync();
        });
        mToNext.setOnClickListener(v -> toBuyVip(v));
        message_back_mine.setOnClickListener(v -> finish());
        mFragments.add(new LoginFragment());
        mFragments.add(AccountLoginFragment.newInstance(AccountLoginModel.TYPE_QR_YT));
        loadRootFragment(R.id.fl_container, mFragments.get(0), false, false);
        loadRootFragment(R.id.fl_container_qr, mFragments.get(1), false, false);
        initLoginView();
        changeViewLayoutForStatusBar(rootView);
    }

    /**
     * 添加第三方登录View
     */
    private void addThirdQRView(){
        Window window = getWindow();
        Log.i(TAG_THIRD, "addThirdQRView() --- window=" + window);
        if (window == null){
            return;
        }
        View decorContentView = window.getDecorView().findViewById(android.R.id.content);
        if (!(decorContentView instanceof ViewGroup)){
            return;
        }
        mGxyQrLayout = new FrameLayout(this);
        mGxyQrLayout.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        mGxyQrLayout.setBackgroundColor(ResUtil.getColor(R.color.mine_user_third_qr_bg_color));
        mGxyQrLayout.setClickable(true);
        mGxyQrLayout.setId(View.generateViewId());
        mGxyQrLayout.setVisibility(View.GONE);
        ((ViewGroup) decorContentView).addView(mGxyQrLayout);
    }

    @Override
    public void showGxyQrView(){
        Log.i(TAG_THIRD, "showGxyQrView() --- mGxyQrLayout=" + mGxyQrLayout);
        if (mGxyQrLayout != null) {
            mShowingGxyQrLayout = true;
            ViewUtil.setViewVisibility(mGxyQrLayout, View.VISIBLE);
            mThirdLoginFragment = ThirdLoginFragment.newInstance(AccountLoginModel.TYPE_QR_GXY);
            getSupportFragmentManager().beginTransaction().add(mGxyQrLayout.getId(), mThirdLoginFragment, TAG_GXYQR_FM).commit();
        }
    }

    @Override
    public void hideGxyQrView(){
        Log.i(TAG_THIRD, "hideGxyQrView() --- mGxyQrLayout=" + mGxyQrLayout);
        if (mGxyQrLayout != null) {
            mShowingGxyQrLayout = false;
            ViewUtil.setViewVisibility(mGxyQrLayout, View.GONE);
            getSupportFragmentManager().beginTransaction().remove(mThirdLoginFragment).commit();
            mThirdLoginFragment = null;
            if (mFragments.get(1) instanceof AccountLoginFragment){
                ((AccountLoginFragment) mFragments.get(1)).onGxyQrViewHided();
            }
        }
    }

    @Override
    public void initData() {

    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        saveConfiguration();
    }


    @Override
    protected UserLoginPresenter createPresenter() {
        return new UserLoginPresenter(this);
    }

    private void initLoginView() {
        Log.i(TAG, "initLoginView start");
        if (getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE) {
            changeLandView();
        } else {
            changePortraitView();
        }
        Activity activity = UserLoginActivity.this;
        if (mLoginIntercepter != null && mLoginIntercepter.intercept(activity)) {
            if (mCustomerChildView == null) {
                mCustomerChildView = mLoginIntercepter.getInterceptView(activity, mCustomerViewLayout);
                if (mCustomerViewLayout.getChildCount() == 0) {
                    mCustomerViewLayout.addView(mCustomerChildView);
                }
            }
            // 解决https://app.huoban.com/tables/2100000007530121/items/2300001898372256?userId=1229522问题
            ViewUtil.setViewVisibility(mCustomerChildView, View.VISIBLE);
            showCustomerLoginView();
        } else {
            if (UserInfoManager.getInstance().isUserBound()) {
                Log.i(TAG, "initLoginView isUserBound");
                mPresenter.getCacheUserInfo();
                mPresenter.getUserInfo();
            } else {
                showNormalLoginView();
            }
        }
    }

    private void showCustomerLoginView() {
        Log.i(TAG, "showCustomerLoginView start");
        setNeedShowQrScan(false);
        ViewUtil.setViewVisibility(mCustomerViewLayout, View.VISIBLE);
        ViewUtil.setViewVisibility(mLoginView, View.GONE);
        ViewUtil.setViewVisibility(mLoginSuccessView, View.GONE);
        ViewUtil.setViewVisibility(mQrScannedLayout, View.GONE);
        ViewUtil.setViewVisibility(mGxyQrLayout, mShowingGxyQrLayout ? View.VISIBLE : View.GONE);
        showingQrScannedLayout.set(false);
    }

    private void showNormalLoginView() {
        Log.i(TAG, "showNormalLoginView start");
        setNeedShowQrScan(false);
        ViewUtil.setViewVisibility(mLoginView, View.VISIBLE);
        ViewUtil.setViewVisibility(mCustomerViewLayout, View.GONE);
        ViewUtil.setViewVisibility(mLoginSuccessView, View.GONE);
        ViewUtil.setViewVisibility(mQrScannedLayout, View.GONE);
        ViewUtil.setViewVisibility(mGxyQrLayout, mShowingGxyQrLayout ? View.VISIBLE : View.GONE);
        showingQrScannedLayout.set(false);
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (!UserInfoManager.getInstance().isQrScanned()) {
            initLoginView();
        }
    }

    @Override
    public void onDestroy() {
        if (!UserInfoManager.getInstance().isUserBound()) {
            UserInfoManager.getInstance().notifyUserCancel();
        }
        UserInfoManager.getInstance().setQrCodeStatus(-1);
        super.onDestroy();
    }

    @Override
    protected boolean isReportPage() {
        return true;
    }

    @Override
    public String getPageId() {
        return Constants.PAGE_ID_LOGIN;
    }


    public void toBuyVip(View view) {
        if (AntiShake.check(view.getId())) {
            return;
        }

        PayManager.getInstance()
                .pay(PayConst.PAY_TYPE_VIP, null)
                .addPayListener(null, new VipPayListener() {

                    @Override
                    public void payResponse(PayResult payResult, PlayItem playItem, Long vipTime) {
                        if (mPresenter != null) {
                            mPresenter.getUserInfo();
                        }
                    }
                });

        //点击按钮事件上报
        if (view instanceof TextView) {
            String text = ((TextView) view).getText().toString();
            if (ResUtil.getString(R.string.user_buy_vip).equals(text)) {
                ButtonClickReportEvent event = new ButtonClickReportEvent(BUTTON_JOIN_NOW);
                ReportHelper.getInstance().addEvent(event);
                ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_JOIN_VIP, mToNext.getText().toString(), getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
            } else if (ResUtil.getString(R.string.user_vip_renew).equals(text)) {
                ButtonClickReportEvent event = new ButtonClickReportEvent(BUTTON_RENEW);
                ReportHelper.getInstance().addEvent(event);
                ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_VIP_RENEW, mToNext.getText().toString(), getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
            }
        }
    }

    public void setNeedShowQrScan(boolean flag) {
        Log.d(TAG, "setNeedShowQrScan, flag = " + flag);
        mNeedShowQrScanLayout = flag;
    }

    public void getUserInfo() {
        mPresenter.getUserInfo();
    }

    public void showLoginSuccessToast() {
        ToastUtil.showOnActivity(UserLoginActivity.this,
                ResUtil.getString(R.string.user_login_success));
    }

    public void userQrScanned(String avatar, String name) {
        setQrScannedLayoutVisible(avatar, name);
    }

    private void setQrScannedLayoutVisible(String avatar, String name) {
        if (!mNeedShowQrScanLayout) return;
        ImageLoader.getInstance().displayCircleImage(UserLoginActivity.this, avatar, mScannedUserAvatar, ResUtil.getDrawable(R.drawable.user_no_avatar));
        mScannedUserName.setText(name);
        if (showingQrScannedLayout.compareAndSet(false, true)) {
            ViewUtil.setViewVisibility(mQrScannedLayout, View.VISIBLE);
            ViewUtil.setViewVisibility(mCustomerViewLayout, View.GONE);
            ViewUtil.setViewVisibility(mLoginView, View.GONE);
            ViewUtil.setViewVisibility(mLoginSuccessView, View.GONE);
            ViewUtil.setViewVisibility(mGxyQrLayout, View.GONE);
            Button viewById = mQrScannedLayout.findViewById(R.id.scanned_btn_back);
            viewById.setOnClickListener(v -> {
                UserInfoManager.getInstance().setQrCodeStatus(-1);
                showNormalLoginView();
                showFragment();
                ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_RELOGIN, ((Button) v).getText().toString(), getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
            });
            ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_RELOGIN, viewById.getText().toString(), getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
        }
    }

    /**
     * 扫码中间状态的时候出现了二维码过期后，调用此方法回到登录页面
     */
    public void goToLoginView() {
        if (mQrScannedLayout != null && mQrScannedLayout.getVisibility() == View.VISIBLE) {
            showNormalLoginView();
            showFragment();
            setNeedShowQrScan(false);
        }
    }

    private void logout() {
        if (!NetworkUtil.isNetworkAvailable(UserLoginActivity.this, false)) {
            ToastUtil.showOnly(UserLoginActivity.this, R.string.no_net_work_str);
            return;
        }

        DialogFragment dialogFragment = new Dialogs.Builder()
                .setType(Dialogs.TYPE_2BTN)
                .setGravity(Gravity.CENTER)
                .setTitle(ResUtil.getString(R.string.user_logout))
                .setMessage(ResUtil.getString(R.string.user_logout_str))
                .setOnPositiveListener(dialog -> {
                    mPresenter.logout();
                    Intent intent = new Intent().setAction("com.yunting.login");
                    intent.putExtra("isLogin", false);
                    AppDelegate.getInstance().getContext().sendBroadcast(intent);
                    dialog.dismiss();
                })
                .create();
        dialogFragment.show(getSupportFragmentManager(), "clear_history");
    }

    @Override
    public void loginSuccess(UserInfo userInfo) {
        Log.i(TAG, "loginSuccess start");
        showOldUserTips(userInfo);
        showLoginSuccessToast();
        Intent intent = new Intent().setAction("com.yunting.login");
        intent.putExtra("isLogin", true);
        AppDelegate.getInstance().getContext().sendBroadcast(intent);
        finish();
//        ImageLoader.getInstance().displayCircleImage(UserLoginActivity.this, userInfo.getAvatar(), mUserAvatar, ResUtil.getDrawable(R.drawable.user_no_avatar));
//        mUserName.setText(userInfo.getNickName());
//        hideFragments();
//        ViewUtil.setViewVisibility(mLoginView, View.GONE);
//        ViewUtil.setViewVisibility(mLoginSuccessView, View.VISIBLE);
//        ViewUtil.setViewVisibility(mCustomerViewLayout, View.GONE);
//        ViewUtil.setViewVisibility(mQrScannedLayout, View.GONE);
//
//        if (userInfo.getVip() == 0) {
//            setNormalView(userInfo);
//        } else {
//            setVipView(userInfo);
//        }
    }

    //是否是老用户，如果是则给予toast提示(主要用于账号升级提示)，
    //本地不需要记录是否是第一次老用户升级新版本，状态由后台控制，第一次isOldUser返回1，第二次返回0
    public void showOldUserTips(UserInfo userInfo) {
        if (userInfo != null && (userInfo.getOldUser() == 1)) {
            ToastUtil.showOnActivity(UserLoginActivity.this, userInfo.getTips());
        }
    }

    /**
     * 非vip用户
     */
    private void setNormalView(UserInfo userInfoTemp) {
        mVipTitle.setText(R.string.user_no_vip_title);
        mVipSubTitle.setText(R.string.user_no_vip_subtitle);
        mVipView.setBackgroundResource(R.drawable.user_no_vip_bg);
        mToNext.setText(R.string.user_buy_vip);
        mToNext.setTextColor(ResUtil.getColor(R.color.colorWhite));
        mToNext.setBackgroundResource(R.drawable.comprehensive_user_no_vip_to_buy_bt);
        mUserNameHeader.setImageResource(R.drawable.user_name_header_normal);
        mTitleHeader.setImageResource(R.drawable.user_no_vip_header);
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_JOIN_VIP, mToNext.getText().toString(), getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
    }

    /**
     * vip用户
     */
    private void setVipView(UserInfo userInfoTemp) {
        mVipTitle.setText(R.string.user_vip_title);
        if (ScreenUtil.isPortrait()) {
            mVipSubTitle.setText(String.format(getString(R.string.user_vip_subtitle2),
                    userInfoTemp.getVipTime(), userInfoTemp.getVipRemainDays()));
        } else {
            mVipSubTitle.setText(String.format(getString(R.string.user_vip_subtitle),
                    userInfoTemp.getVipTime(), userInfoTemp.getVipRemainDays()));
        }
        mVipView.setBackgroundResource(R.drawable.user_vip_bg);
        mToNext.setText(R.string.user_vip_renew);
        mToNext.setTextColor(ResUtil.getColor(R.color.user_go_to_vip_color));
        mToNext.setBackgroundResource(R.drawable.comprehensive_user_vip_to_buy_bt);
        mUserNameHeader.setImageResource(R.drawable.user_name_header_vip);
        mTitleHeader.setImageResource(R.drawable.user_vip_header);
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_VIP_RENEW, mToNext.getText().toString(), getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
    }

    private void hideFragments() {
        for (BaseFragment fragment : mFragments) {
            fragment.onSupportInvisible();
        }
        if (mThirdLoginFragment != null){
            mThirdLoginFragment.onSupportInvisible();
        }
    }

    private void showFragment() {
        for (BaseFragment fragment : mFragments) {
            fragment.onSupportVisible();
        }
        if (mThirdLoginFragment != null){
            mThirdLoginFragment.onSupportVisible();
        }
    }

    @Override
    public void loginFailed() {
        ToastUtil.showOnly(UserLoginActivity.this, R.string.user_login_failed);
    }

    @Override
    public void logoutSuccess() {

    }

    @Override
    public void logoutFailed() {

    }

    @Override
    public void hideError() {
    }

    @Override
    public void showError() {
    }

    @Override
    public void showError(String errMsg, boolean clickToRetry) {
    }

//    @Override
//    public void logoutSuccess() {
//        Log.i(TAG, "logoutSuccess start");
//        showNormalLoginView();
//        showFragment();
//    }

//    @Override
//    public void logoutFailed() {
//        ToastUtil.showOnly(UserLoginActivity.this, R.string.user_logout_failed);
//    }

    @Override
    protected void showAccordingToScreen(int orientation) {
        super.showAccordingToScreen(orientation);
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            changeLandView();
        } else {
            changePortraitView();
        }
    }

    //横屏显示
    private void changeLandView() {
//        ViewGroup.MarginLayoutParams contentFrameLayoutLayoutParams =
//                (ViewGroup.MarginLayoutParams) mContentFrameLayout.getLayoutParams();
//        contentFrameLayoutLayoutParams.topMargin = 0;
//        contentFrameLayoutLayoutParams.leftMargin = 0;

//        ViewGroup.MarginLayoutParams contentQrFrameLayoutLayoutParams =
//                (ViewGroup.MarginLayoutParams) mContentQrFrameLayou.getLayoutParams();
//        contentQrFrameLayoutLayoutParams.topMargin = ResUtil.getDimen(R.dimen.y41);
//        contentQrFrameLayoutLayoutParams.rightMargin = 0;

//        UserInfoLandViewManager.setView(mUserInfoRootView);
//        ((ViewGroup.MarginLayoutParams)mMyOrder.getLayoutParams()).rightMargin = ResUtil.getDimen(R.dimen.right_btn_land_padding);
//        ConstraintSet set = new ConstraintSet();
//        set.clone(mRootLayout);
//        set.setGuidelinePercent(mLandGuideLine.getId(), 0.3353f);
//        set.setGuidelinePercent(mPortGuideLine.getId(), 0.0810f);
//        set.setGuidelinePercent(mScannedGuideLine.getId(), 0.1072f);
//        setLandPhoneLoginView(set);
//        setLandQrLoginView(set);
//        set.applyTo(mRootLayout);
    }

    //设置横屏手机号登录页面UI
    private void setLandPhoneLoginView(ConstraintSet set) {
        set.connect(mContentFrameLayout.getId(), ConstraintSet.TOP, mPortGuideLine.getId(), ConstraintSet.BOTTOM);
        set.connect(mContentFrameLayout.getId(), ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM);
        set.connect(mContentFrameLayout.getId(), ConstraintSet.LEFT, mLandGuideLine.getId(), ConstraintSet.RIGHT);
        set.connect(mContentFrameLayout.getId(), ConstraintSet.RIGHT, ConstraintSet.PARENT_ID, ConstraintSet.RIGHT);
    }

    //设置横屏二维码登录页面UI
    private void setLandQrLoginView(ConstraintSet set) {
        set.connect(mContentQrFrameLayou.getId(), ConstraintSet.RIGHT, mLandGuideLine.getId(), ConstraintSet.LEFT);
        set.connect(mContentQrFrameLayou.getId(), ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP);
        set.connect(mContentQrFrameLayou.getId(), ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM);
    }

    //竖屏显示
    private void changePortraitView() {
//        ViewGroup.MarginLayoutParams contentFrameLayoutLayoutParams =
//                (ViewGroup.MarginLayoutParams) mContentFrameLayout.getLayoutParams();
//        contentFrameLayoutLayoutParams.topMargin = ResUtil.getDimen(R.dimen.user_login_tab_layout_margin);
//
//        ViewGroup.MarginLayoutParams contentQrFrameLayoutLayoutParams =
//                (ViewGroup.MarginLayoutParams) mContentQrFrameLayou.getLayoutParams();
//        contentQrFrameLayoutLayoutParams.topMargin = 0;

//        UserInfoPortViewManager.setView(mUserInfoRootView);
//        ((ViewGroup.MarginLayoutParams)mMyOrder.getLayoutParams()).rightMargin = ResUtil.getDimen(R.dimen.right_btn_port_padding);
//        ConstraintSet set = new ConstraintSet();
//        set.clone(mRootLayout);
//        set.setGuidelinePercent(mPortGuideLine.getId(), 0.48f);
//        set.setGuidelinePercent(mScannedGuideLine.getId(), 0.0645f);
//        setPortPhoneLoginView(set);
//        setPortQrLoginView(set);
//        set.applyTo(mRootLayout);
    }

    //设置竖屏手机号登录部分UI
    private void setPortPhoneLoginView(ConstraintSet set) {
        set.connect(mContentFrameLayout.getId(), ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP);
        set.connect(mContentFrameLayout.getId(), ConstraintSet.BOTTOM, mPortGuideLine.getId(), ConstraintSet.TOP);
        set.connect(mContentFrameLayout.getId(), ConstraintSet.LEFT, ConstraintSet.PARENT_ID, ConstraintSet.LEFT);
    }

    //设置竖屏二维码登录部分UI
    private void setPortQrLoginView(ConstraintSet set) {
        set.connect(mContentQrFrameLayou.getId(), ConstraintSet.RIGHT, ConstraintSet.PARENT_ID, ConstraintSet.RIGHT);
        set.connect(mContentQrFrameLayou.getId(), ConstraintSet.TOP, mPortGuideLine.getId(), ConstraintSet.BOTTOM);
        set.connect(mContentQrFrameLayou.getId(), ConstraintSet.LEFT, ConstraintSet.PARENT_ID, ConstraintSet.LEFT);
    }

    public boolean isReportFragment() {
        return true;
    }

    @Override
    public void onBackPressedSupport() {
        if (mShowingGxyQrLayout){
            hideGxyQrView();
            mShowingGxyQrLayout = false;
            return;
        }
        super.onBackPressedSupport();
    }
}
