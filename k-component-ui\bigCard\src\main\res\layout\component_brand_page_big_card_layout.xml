<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/component_card_bg_7">

    <com.kaolafm.kradio.component.ui.base.view.OvalImageView
        android:id="@+id/card_bg_iv"
        android:layout_width="@dimen/m320"
        android:layout_height="@dimen/m432"
        app:oval_radius="@dimen/m16" />

    <RelativeLayout
        android:layout_width="@dimen/m320"
        android:layout_height="@dimen/m432"
        android:layout_centerInParent="true">

        <RelativeLayout
            android:id="@+id/card_pic_iv_rl"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/m20">

            <com.kaolafm.kradio.component.ui.base.view.OvalImageView
                android:id="@+id/card_pic_iv"
                android:layout_width="@dimen/m280"
                android:layout_height="@dimen/m280"
                app:oval_radius="@dimen/m12"
                tools:src="@drawable/splash_yunting" />

            <ImageView
                android:id="@+id/card_tag_iv"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/m42"
                android:layout_marginLeft="@dimen/m10"
                android:layout_marginTop="@dimen/m10"
                android:adjustViewBounds="true"
                android:scaleType="fitStart"
                tools:src="@drawable/icon_vip_home"
                android:visibility="gone" />

            <TextView
                android:id="@+id/card_tag_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/m10"
                android:layout_marginTop="@dimen/m10"
                android:adjustViewBounds="true"
                android:background="@drawable/brand_page_tab_bg"
                android:gravity="center"
                android:paddingLeft="@dimen/m20"
                android:paddingTop="@dimen/m10"
                android:paddingRight="@dimen/m20"
                android:paddingBottom="@dimen/m10"
                android:text="有奖互动"
                android:textColor="#FFFCFB"
                android:textSize="@dimen/m20" />

            <ImageView
                android:visibility="gone"
                android:id="@+id/card_play_iv"
                android:layout_width="@dimen/m38"
                android:layout_height="@dimen/m38"
                android:layout_alignEnd="@+id/card_pic_iv"
                android:layout_alignBottom="@+id/card_pic_iv"
                android:layout_marginRight="@dimen/m10"
                android:layout_marginBottom="@dimen/m10"
                android:src="@drawable/component_play_icon_2" />

            <com.kaolafm.kradio.component.ui.base.view.RateView
                android:id="@+id/card_layout_playing"
                android:visibility="gone"
                android:layout_width="@dimen/m38"
                android:layout_height="@dimen/m38"
                android:layout_alignEnd="@+id/card_pic_iv"
                android:layout_alignBottom="@+id/card_pic_iv"
                android:layout_marginRight="@dimen/m20"
                android:layout_marginBottom="@dimen/m20"
                app:lottie_autoPlay="true"
                app:lottie_fileName="lottie/rate.json"
                app:lottie_loop="true" />
        </RelativeLayout>


        <TextView
            android:id="@+id/card_title_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/card_pic_iv_rl"
            android:layout_alignStart="@+id/card_pic_iv_rl"
            android:layout_alignEnd="@+id/card_pic_iv_rl"
            android:layout_marginTop="@dimen/m16"
            android:ellipsize="end"
            android:lineSpacingExtra="@dimen/m7"
            android:maxWidth="@dimen/m260"
            android:maxLines="2"
            android:textColor="@color/component_brand_page_title_text_color"
            android:textSize="@dimen/m26"
            tools:text="我是标题啊我标啊" />

        <TextView
            android:id="@+id/card_des_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/card_title_tv"
            android:layout_alignStart="@+id/card_pic_iv_rl"
            android:layout_alignEnd="@+id/card_pic_iv_rl"
            android:layout_marginTop="@dimen/m1"
            android:ellipsize="end"
            android:lineSpacingExtra="@dimen/m7"
            android:maxWidth="@dimen/m260"
            android:maxLines="1"
            android:textColor="@color/component_brand_page_des_text_color"
            android:textSize="@dimen/m24"
            tools:text="我是标题啊我标啊" />

    </RelativeLayout>
</RelativeLayout>