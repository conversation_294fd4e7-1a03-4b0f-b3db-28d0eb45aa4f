package com.kaolafm.kradio.lib.utils;

import android.content.res.Configuration;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import android.os.Build.VERSION_CODES;
import androidx.annotation.ArrayRes;
import androidx.annotation.BoolRes;
import androidx.annotation.ColorRes;
import androidx.annotation.DimenRes;
import androidx.annotation.DrawableRes;
import androidx.annotation.IntegerRes;
import androidx.annotation.StringRes;
import android.util.TypedValue;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.AppManager;

/**
 * 根据资源id获取数据的工具类
 *
 * <AUTHOR>
 * @date 2018/4/27
 */

public class ResUtil {

    public static String getString(@StringRes int resId) {
        return AppDelegate.getInstance().getContext().getResources().getString(resId);
    }

    public static int getDimen(@DimenRes int resId) {
        return AppDelegate.getInstance().getContext().getResources().getDimensionPixelSize(resId);
    }

    public static float getDimension(@DimenRes int resId) {
        return AppDelegate.getInstance().getContext().getResources().getDimension(resId);
    }

    public static String[] getStringArray(@ArrayRes int resId) {
        return AppDelegate.getInstance().getContext().getResources().getStringArray(resId);
    }

    public static int[] getIntArray(@ArrayRes int resId) {
        return AppDelegate.getInstance().getContext().getResources().getIntArray(resId);
    }

    public static float getTextSize(@DimenRes int resId) {
        return AppDelegate.getInstance().getContext().getResources().getDimension(resId);
    }


    public static int getColor(@ColorRes int resId) {//换肤AOP的切面
        if (VERSION.SDK_INT >= VERSION_CODES.M) {
            return AppDelegate.getInstance().getContext().getResources().getColor(resId, null);
        } else {
            return AppDelegate.getInstance().getContext().getResources().getColor(resId);
        }
    }

    public static Drawable getDrawable(@DrawableRes int resId) {//换肤AOP的切面
        if (VERSION.SDK_INT >= VERSION_CODES.LOLLIPOP) {
            return AppDelegate.getInstance().getContext().getResources().getDrawable(resId, null);
        } else {
            return AppDelegate.getInstance().getContext().getResources().getDrawable(resId);
        }
    }

    public static boolean getBoolean(@BoolRes int boolResId) {
        return AppDelegate.getInstance().getContext().getResources().getBoolean(boolResId);
    }

    public static int getInt(@IntegerRes int intResId) {
        return AppDelegate.getInstance().getContext().getResources().getInteger(intResId);
    }

    public static int getOrientation() {
        if (AppManager.getInstance().getCurrentActivity() != null) {
            return AppManager.getInstance().getCurrentActivity().getResources().getConfiguration().orientation;
        } else {
            return Configuration.ORIENTATION_LANDSCAPE;
        }
//        return AppManager.getInstance().getCurrentActivity().getResources().getConfiguration().orientation;
    }

    public static float getFloat(@DimenRes int rId) {
        if (VERSION.SDK_INT >= 29) {
            return AppDelegate.getInstance().getContext().getResources().getFloat(rId);
        } else {
            TypedValue value = new TypedValue();
            AppDelegate.getInstance().getContext().getResources().getValue(rId, value, true);
            return value.getFloat();
        }
    }
}
