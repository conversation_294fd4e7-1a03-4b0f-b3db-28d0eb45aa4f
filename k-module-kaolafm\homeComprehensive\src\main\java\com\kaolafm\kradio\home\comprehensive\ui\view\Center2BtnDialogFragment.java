package com.kaolafm.kradio.home.comprehensive.ui.view;

import android.content.res.Configuration;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentActivity;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.kaolafm.kradio.component.ui.base.skin.SkinHelper;
import com.kaolafm.kradio.common.utils.CommonUtils;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.dialog.BaseDialogFragment;
import com.kaolafm.kradio.lib.dialog.DialogListener;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;

/**
 * <AUTHOR>
 **/
public class Center2BtnDialogFragment extends BaseDialogFragment {

    private View mTvDialogButtonMainLayout;
    private TextView mTvDialogBottomCancel;
    private TextView mTvDialogBottomDefine;
    private TextView mTvDialogBottomMessage;
    private View mRootView;
    private DialogListener.OnNativeListener<DialogFragment> mNativeListener;
    private DialogListener.OnPositiveListener<DialogFragment> mPositiveListener;
    private CharSequence message;
    private CharSequence leftBtnText;
    private CharSequence rightBtnText;
    private boolean canShowButton = true;

    public static Center2BtnDialogFragment create() {
        Center2BtnDialogFragment fragment = new Center2BtnDialogFragment();
        fragment.setGravity(Gravity.CENTER);
        fragment.setWidth(WindowManager.LayoutParams.WRAP_CONTENT);
//        fragment.setHeight((int) (ScreenUtil.getScreenHeight() * 0.14f));
        fragment.setHeight(WindowManager.LayoutParams.WRAP_CONTENT);
        return fragment;
    }

    @Override
    public void onStart() {
        super.onStart();
        Window window = getDialog().getWindow();
        WindowManager.LayoutParams params = window.getAttributes();
        if (SkinHelper.isNightMode()) {
            params.dimAmount = 0.9f;
        } else {
            params.dimAmount = 0.7f;
        }
        window.setAttributes(params);
        CommonUtils.getInstance().initGreyStyle(window);
    }

    @Override
    protected View getContentView() {
        FragmentActivity activity = getActivity();
        View inflate = View.inflate(activity, R.layout.dialog_fragment_center_2btn, null);
        mTvDialogButtonMainLayout = inflate.findViewById(R.id.tv_dialog_button_main_layout);
        mTvDialogBottomCancel = inflate.findViewById(R.id.tv_dialog_bottom_cancel);
        mTvDialogBottomDefine = inflate.findViewById(R.id.tv_dialog_bottom_define);
        mTvDialogBottomMessage = inflate.findViewById(R.id.tv_dialog_bottom_message);
        mRootView = inflate.findViewById(R.id.root_layout);

        mTvDialogBottomCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!AntiShake.check(v.getId())) {
                    if (mNativeListener != null) {
                        mNativeListener.onClick(Center2BtnDialogFragment.this);
                    } else {
                        dismiss();
                    }
                }
            }
        });

        mTvDialogBottomDefine.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!AntiShake.check(v.getId())) {
                    if (mPositiveListener != null) {
                        mPositiveListener.onClick(Center2BtnDialogFragment.this);
                    }
                }
            }
        });

        mTvDialogBottomMessage.setText(message);
        ViewUtil.setViewVisibility(mTvDialogButtonMainLayout, canShowButton ? View.VISIBLE : View.GONE);
        if (!TextUtils.isEmpty(leftBtnText)) {
            mTvDialogBottomCancel.setText(leftBtnText);
        }

        if (!TextUtils.isEmpty(rightBtnText)) {
            mTvDialogBottomDefine.setText(rightBtnText);
        }
        return inflate;
    }


    public void setMessage(CharSequence message) {
        this.message = message;
    }

    public void setLeftButton(CharSequence leftBtnText) {
        this.leftBtnText = leftBtnText;
    }

    public void setRightButton(CharSequence rightBtnText) {
        this.rightBtnText = rightBtnText;
    }

    public void setOnNativeListener(DialogListener.OnNativeListener<DialogFragment> nativeListener) {
        mNativeListener = nativeListener;
    }

    public void setOnPositiveListener(DialogListener.OnPositiveListener<DialogFragment> positiveListener) {
        mPositiveListener = positiveListener;
    }

    public void setCanShowButton(boolean canShowButton) {
        this.canShowButton = canShowButton;
    }

    @Override
    protected void showAccordingToScreen(int orientation) {
        super.showAccordingToScreen(orientation);

        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            changeLandView();
        } else {
            changePortraitView();
        }
    }

    //横屏显示
    private void changeLandView() {
        ViewGroup.MarginLayoutParams rootViewLayoutParams =
                (ViewGroup.MarginLayoutParams) mRootView.getLayoutParams();
//        rootViewLayoutParams.topMargin = ResUtil.getDimen(R.dimen.y198);
    }

    //竖屏显示
    private void changePortraitView() {
        ViewGroup.MarginLayoutParams rootViewLayoutParams =
                (ViewGroup.MarginLayoutParams) mRootView.getLayoutParams();
//        rootViewLayoutParams.topMargin = ResUtil.getDimen(R.dimen.y294);
    }
}
