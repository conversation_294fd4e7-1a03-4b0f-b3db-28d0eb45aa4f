<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <item
        android:id="@android:id/background" >
        <color android:color="@color/black_30_transparent_color"></color>
    </item>

    <item android:id="@android:id/secondaryProgress"
        >
        <!--<clip android:drawable="@drawable/shape_player_seekbar_secondary" />-->
        <color android:color="@color/color_white_50_transparent"></color>
    </item>

    <item android:id="@android:id/progress">
        <clip>
            <shape>
                <gradient
                    android:angle="0"
                    android:centerColor="@color/kaola_red"
                    android:endColor="@color/kaola_red"
                    android:startColor="@color/kaola_red" />
            </shape>
        </clip>
    </item>

</layer-list>