package com.kaolafm.ad.comprehensive.audioad;

import com.kaolafm.ad.AdConstant;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.TempTaskPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

/**
 * @ClassName TimerAudioAD
 * @Description 定时广告(定时广告)
 * <AUTHOR>
 * @Date 2020-03-11 17:41
 * @Version 1.0
 */
public class TimerAudioAD extends BaseAudioAD {
    @Override
    public void onGetData() {
        startPlay(makeAudioAdPlayItem(mAudioAdvert.getUrl(), AdConstant.TYPE_TIMED_ADVERT));
    }

    @Override
    protected TempTaskPlayItem makeAudioAdPlayItem(String url, int type) {
        TempTaskPlayItem adPlayItem = super.makeAudioAdPlayItem(url, type);
        adPlayItem.setNeedNextInnerAction(false);
        adPlayItem.setPlayStateListener(new BasePlayStateListener() {
            @Override
            public void onPlayerEnd(PlayItem playItem) {
                playAudioAdEnd();
            }
        });
        return adPlayItem;
    }

    @Override
    public void onError() {
        playAudioAdEnd();
    }

    @Override
    public void setCallback(Object o) {

    }
}
