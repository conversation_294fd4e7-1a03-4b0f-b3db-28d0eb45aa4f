package com.kaolafm.kradio.huawei.utils;

import android.graphics.Bitmap;

import com.huawei.carmediakit.bean.DialogInfo;
import com.huawei.carmediakit.bean.DialogQrCodeInfo;

import java.util.ArrayList;
import java.util.List;

public class DialogHelper {

    public static DialogInfo getNeedLoginDialog(Bitmap bitmap) {
        final DialogInfo dialogInfo = new DialogInfo();
        dialogInfo.setDialogId("LOGIN_NOTICE");
        /*
        dialogInfo.setTitle("登录");
        dialogInfo.setText("您还没有登录，确认要去登录吗？");
        List<DialogButtonInfo> buttons = new ArrayList<>();
        DialogButtonInfo button2 = new DialogButtonInfo();
        button2.setButtonId("LOGIN_BUTTON_OK");
        button2.setButtonText("确定");
        buttons.add(button2);

        dialogInfo.setButtons(buttons);
         */
        List<DialogQrCodeInfo> dialogQrCodeInfoList = new ArrayList<>();
        DialogQrCodeInfo dialogQrCodeInfo = new DialogQrCodeInfo("LOGIN_NOTICE");
        dialogQrCodeInfo.setCodeDatas(bitmap);
        dialogQrCodeInfo.setTips("请用手机云听app扫码登录");
        dialogQrCodeInfoList.add(dialogQrCodeInfo);
        dialogInfo.setQrCodes(dialogQrCodeInfoList);
        return dialogInfo;
    }
}
