<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:state_activated="true" android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#5C5E60" />
            <corners android:bottomLeftRadius="@dimen/big_corner_size" android:bottomRightRadius="@dimen/big_corner_size" android:radius="@dimen/big_corner_size" android:topLeftRadius="@dimen/big_corner_size" android:topRightRadius="@dimen/big_corner_size" />
        </shape>
    </item>

    <item android:state_activated="true" android:state_pressed="false">
        <shape android:shape="rectangle">
            <solid android:color="#8D8F92" />
            <corners android:bottomLeftRadius="@dimen/big_corner_size" android:bottomRightRadius="@dimen/big_corner_size" android:radius="@dimen/big_corner_size" android:topLeftRadius="@dimen/big_corner_size" android:topRightRadius="@dimen/big_corner_size" />
        </shape>
    </item>

    <item android:state_activated="false" android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#80C31B27" />
            <corners android:bottomLeftRadius="@dimen/big_corner_size" android:bottomRightRadius="@dimen/big_corner_size" android:radius="@dimen/big_corner_size" android:topLeftRadius="@dimen/big_corner_size" android:topRightRadius="@dimen/big_corner_size" />
        </shape>
    </item>

    <item android:state_activated="false" android:state_pressed="false">
        <shape android:shape="rectangle">
            <solid android:color="#C31B27" />
            <corners android:bottomLeftRadius="@dimen/big_corner_size" android:bottomRightRadius="@dimen/big_corner_size" android:radius="@dimen/big_corner_size" android:topLeftRadius="@dimen/big_corner_size" android:topRightRadius="@dimen/big_corner_size" />
        </shape>
    </item>


</selector>
