<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingTop="@dimen/y23"
    android:paddingBottom="@dimen/y19">

    <LinearLayout
        android:id="@+id/titleLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/msg_item_pic"
            android:layout_width="@dimen/m32"
            android:layout_height="@dimen/m32"
            android:scaleType="centerInside"
            tools:src="@drawable/online_message_yellow_icon" />

        <TextView
            android:id="@+id/msg_item_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/x10"
            android:textColor="@color/online_message_item_title_color2"
            android:textSize="@dimen/m24"
            tools:text="进突破ask" />

        <TextView
            android:id="@+id/msg_item_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/x32"
            android:textColor="@color/online_message_item_title_color2"
            android:textSize="@dimen/m24"
            tools:text="时间 1231321" />
    </LinearLayout>

    <TextView
        android:id="@+id/msg_item_content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y20"
        android:layout_marginEnd="@dimen/x32"
        android:textColor="@color/online_message_item_content_color"
        android:textSize="@dimen/m24"
        app:layout_constraintEnd_toStartOf="@id/arrowIcon"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titleLayout"
        tools:text="前方3公里正在进行道路施工车辆行驶缓慢，" />

    <ImageView
        android:id="@+id/arrowIcon"
        android:layout_width="@dimen/m36"
        android:layout_height="@dimen/m36"
        android:src="@drawable/online_message_arrow_right"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>