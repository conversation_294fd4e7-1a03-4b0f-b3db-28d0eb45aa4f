package com.kaolafm.kradio.category.radio;

import android.util.Log;

import com.kaolafm.kradio.category.radio.tab.RadioTabFragment;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;

@Aspect
public class RadioTabAspect{

    private String TAG = "RadioTabAspect";

//    @Around("execution(* com.kaolafm.kradio.category.radio.tab.RadioTabFragment.changeTabTitleTextSize(..))")
//    public void changeTabTitleSize(ProceedingJoinPoint point) throws Throwable {
//        Log.i(TAG,"changeTabTitleSize...");
//        RadioTabFragment fragment = (RadioTabFragment) point.getThis();
//        int normalSize = (int)point.getArgs()[0];
//        int selectedSize = (int)point.getArgs()[1];
//        Log.i(TAG,"ProceedingJoinPoint:"+fragment);
//        Log.i(TAG,"params::"+normalSize+" ::"+selectedSize);
//        int text_size_title2 = 40;
//        Integer [] params = {text_size_title2,text_size_title2};
//        Log.i(TAG,"changed params::"+params[0]+" ::"+params[1]);
//        point.proceed(params);
//    }

}
