package com.kaolafm.kradio.search;

import android.graphics.Rect;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;
import android.view.View;

public class SearchTypeGridItemDecoration extends RecyclerView.ItemDecoration {

    private float mHorizontalDivider;
    private float mVerticalDivider;

    public SearchTypeGridItemDecoration(float horizontalDivider, float verticalDivider) {
        mHorizontalDivider = horizontalDivider;
        mVerticalDivider = verticalDivider;
    }

    public float getmHorizontalDivider() {
        return mHorizontalDivider;
    }

    public void setmHorizontalDivider(int mHorizontalDivider) {
        this.mHorizontalDivider = mHorizontalDivider;
    }

    public float getmVerticalDivider() {
        return mVerticalDivider;
    }

    public void setmVerticalDivider(int mVerticalDivider) {
        this.mVerticalDivider = mVerticalDivider;
    }

    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {

        final int spanCount = getSpanCount(parent);
        final int childCount = parent.getAdapter().getItemCount();
        final int adapterPosition = parent.getChildAdapterPosition(view);
        final int disW = Math.round(mHorizontalDivider);
        final int indexSpace = adapterPosition % spanCount;
        outRect.set(disW * (indexSpace) / spanCount, 0, disW * (spanCount - indexSpace - 1) / spanCount, Math.round(mVerticalDivider));

//        if (isFirstColumn(adapterPosition, spanCount, childCount)) {
//            outRect.left = 0;
//        }

//        if (isLastColumn(adapterPosition, spanCount, childCount)) {
        //outRect.right = 0;
//        }

    }

    private boolean isFirstColumn(int position, int spanCount, int childCount) {
        return position % spanCount == 0;
    }

    private boolean isLastColumn(int position, int spanCount, int childCount) {
        return (position + 1) % spanCount == 0;
    }

    private boolean isLastRow(int position, int spanCount, int childCount) {
        int lastColumnCount = childCount % spanCount;
        lastColumnCount = lastColumnCount == 0 ? spanCount : lastColumnCount;
        return position >= childCount - lastColumnCount;
    }

    private int getSpanCount(RecyclerView parent) {
        RecyclerView.LayoutManager layoutManager = parent.getLayoutManager();

        if (layoutManager instanceof GridLayoutManager) {
            return ((GridLayoutManager) layoutManager).getSpanCount();
        } else if (layoutManager instanceof StaggeredGridLayoutManager) {
            return ((StaggeredGridLayoutManager) layoutManager).getSpanCount();
        } else {
            throw new UnsupportedOperationException("the GridDividerItemDecoration can only be used in " +
                    "the RecyclerView which use a GridLayoutManager or StaggeredGridLayoutManager");
        }
    }
}

