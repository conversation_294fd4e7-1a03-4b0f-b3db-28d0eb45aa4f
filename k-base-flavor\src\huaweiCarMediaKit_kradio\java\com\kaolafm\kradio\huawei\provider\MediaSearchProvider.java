package com.kaolafm.kradio.huawei.provider;


import com.huawei.carmediakit.bean.MediaElement;
import com.huawei.carmediakit.bean.PagedMediaElements;
import com.huawei.carmediakit.bean.SearchResult;
import com.huawei.carmediakit.bean.Single;
import com.huawei.carmediakit.callback.BasicCallback;
import com.huawei.carmediakit.provider.IMediaSearchProvider;
import com.iflytek.cloud.ErrorCode;
import com.kaolafm.kradio.huawei.convert.SearchDataConverterUtil;
import com.kaolafm.kradio.huawei.utils.Constant;
import com.kaolafm.kradio.k_kaolafm.search.SearchHistoryManager;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.opensdk.api.search.SearchRequest;
import com.kaolafm.opensdk.api.search.model.SearchProgramBean;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import java.util.ArrayList;
import java.util.List;

public class MediaSearchProvider implements IMediaSearchProvider {
    public static final String TAG = Constant.TAG;

    @Override
    public void asyncSearch(String s, BasicCallback<SearchResult> basicCallback) {
        Logger.i(TAG, "asyncSearch");
        SearchHistoryManager.getInstance().addRecentSearchTag(s);
        new SearchRequest().searchAll(s, new HttpCallback<List<SearchProgramBean>>() {
            @Override
            public void onSuccess(List<SearchProgramBean> searchProgramBeans) {
                SearchResult searchResult = SearchDataConverterUtil.toSearchResult(searchProgramBeans);
                basicCallback.callback(searchResult, ErrorCode.SUCCESS, "Success");
            }

            @Override
            public void onError(ApiException e) {
                basicCallback.callback(null, ErrorCode.ERROR_NO_MATCH, "Error");
            }
        });
    }

    @Override
    public void asyncSearchByType(String s, MediaElement.ElementType elementType, int i, BasicCallback<PagedMediaElements> basicCallback) {
        Logger.i(TAG, "asyncSearchByType");
    }

    @Override
    public List<String> querySearchHistory() {
        Logger.i(TAG, "querySearchHistory");
        return SearchHistoryManager.getInstance().getRecentSearchTags();
    }

    @Override
    public void asyncQueryHotSearch(BasicCallback<List<Single>> basicCallback) {
        Logger.i(TAG, "asyncQueryHotSearch");
        List<Single> singleList = new ArrayList<>();
        basicCallback.callback(singleList, ErrorCode.SUCCESS, "empty");
    }

    @Override
    public void asyncQueryHotSearchWords(BasicCallback<List<String>> basicCallback) {
        Logger.i(TAG, "asyncQueryHotSearchWords");
        new SearchRequest().getHotWords(new HttpCallback<List<String>>() {
            @Override
            public void onSuccess(List<String> strings) {
                if (strings != null && !strings.isEmpty()) {
                    basicCallback.callback(strings, ErrorCode.SUCCESS, "Success");
                    return;
                }
                basicCallback.callback(null, ErrorCode.ERROR_NO_MATCH, "Error");
            }

            @Override
            public void onError(ApiException e) {
                basicCallback.callback(null, ErrorCode.ERROR_NO_MATCH, "Error");
            }
        });
    }
}
