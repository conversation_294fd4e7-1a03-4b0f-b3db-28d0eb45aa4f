<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/comprehensive_live_message_item_bg"
    android:paddingVertical="@dimen/comprehensive_live_message_margin_vertical"
    android:paddingHorizontal="@dimen/comprehensive_live_message_margin_horizontal"
    android:minHeight="@dimen/comprehensive_live_message_min_height">

    <ImageView
        android:id="@+id/soundIv"
        android:layout_width="@dimen/x32"
        android:layout_height="@dimen/y32"
        android:layout_marginBottom="@dimen/y6"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/contentTv"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/contentTv" />

    <TextView
        android:id="@+id/durationTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/x4"
        android:gravity="center_vertical"
        android:textSize="@dimen/m20"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/contentTv"
        app:layout_constraintEnd_toStartOf="@id/contentTv"
        app:layout_constraintStart_toEndOf="@id/soundIv"
        app:layout_constraintTop_toTopOf="@id/contentTv"
        tools:text="199″" />

    <TextView
        android:id="@+id/contentTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/x32"
        android:layout_marginTop="@dimen/y10"
        android:layout_marginEnd="@dimen/x32"
        android:layout_marginBottom="@dimen/y10"
        android:lineSpacingMultiplier="1.5"
        android:textSize="@dimen/comprehensive_live_message_text_size"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/cityTv"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0"
        tools:text="这里是内容这里是内容" />

    <TextView
        android:id="@+id/cityTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxEms="6"
        android:paddingStart="@dimen/x12"
        android:paddingTop="@dimen/y4"
        android:paddingEnd="@dimen/x12"
        android:paddingBottom="@dimen/y4"
        android:singleLine="true"
        android:textColor="@color/text_color_white"
        android:textSize="@dimen/m20"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/soundIv"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/contentTv"
        app:layout_constraintTop_toTopOf="@id/soundIv"
        tools:text="石家庄市石家庄" />

</androidx.constraintlayout.widget.ConstraintLayout>