package com.kaolafm.kradio.flavor.impl;

import android.util.Log;

import com.kaolafm.kradio.lib.base.flavor.KRadioSetupPlayerInter;

import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.opensdk.player.logic.PlayerManager;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-10-29 17:23
 ******************************************/
public class KRadioSetupPlayerImpl implements KRadioSetupPlayerInter {
    private static final String TAG = "KRadioSetupPlayerImpl";

    @Override
    public boolean setupPlayer(Object... args) {
        PlayerManager playerManager = PlayerManager.getInstance();
        boolean isInitSuccess = playerManager.isPlayerInitSuccess();
        Log.i(TAG, "setupPlayer start");
        if (isInitSuccess) {
            PlayerManager.getInstance().requestAudioFocus();
        } else {
            playerManager.addPlayerInitComplete(new MyOnPlayerInitCompleteListener());
        }
        return true;
    }

    private static class MyOnPlayerInitCompleteListener implements IPlayerInitCompleteListener {

        @Override
        public void onPlayerInitComplete(boolean b) {
            Log.i(TAG, "onPlayerInitComplete start");
            PlayerManager.getInstance().removePlayerInitComplete(this);
            PlayerManager.getInstance().requestAudioFocus();
        }
    }
}
