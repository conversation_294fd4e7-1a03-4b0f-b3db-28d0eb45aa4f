package com.kaolafm.kradio.home.comprehensive.item;

import android.graphics.drawable.BitmapDrawable;
import androidx.annotation.NonNull;
import android.view.View;
import android.widget.TextView;

import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.component.ui.base.CellBinder;
import com.kaolafm.kradio.component.ui.base.ItemClickSupport;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.ui.BaseHolder.OnViewClickListener;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.lib.widget.ratio.RatioLinearLayout;

/**
 * 首页功能入口类型
 * <AUTHOR>
 * @date 2019-08-16
 */
public class FunctionPairCell extends HomeCell implements CellBinder<View, FunctionPairCell>, ItemClickSupport {

    public FunctionPairCell first;

    public FunctionPairCell second;

    public String firstCode = "0";

    public String secondCode = "0";

    TextView mTvItemHomeFunctionBottom;
    TextView mTvItemHomeFunctionTop;

    protected OnViewClickListener mListener;

    public FunctionPairCell() {
    }

    public FunctionPairCell(FunctionPairCell pair) {
        first = pair.first;
        second = pair.second;
    }

    public void reset() {
        first = null;
        second = null;
    }

    @Override
    public void mountView(@NonNull FunctionPairCell data, @NonNull View view, int position) {
        mTvItemHomeFunctionBottom=view.findViewById(R.id.tv_item_home_function_bottom);
        mTvItemHomeFunctionTop=view.findViewById(R.id.tv_item_home_function_top);


        if (view instanceof RatioLinearLayout) {
            ((RatioLinearLayout) view).setRatio(ResUtil.getString(R.string.home_function_cell_ration));
        }
        show(mTvItemHomeFunctionTop, data.first, position);
        show(mTvItemHomeFunctionBottom, data.second, position);
    }

    @Override
    public void setOnItemClickListener(OnViewClickListener listener) {
        mListener = listener;
    }

    @Override
    public int getItemType() {
        return R.layout.item_home_function;
    }

    private void show(TextView textView, HomeCell cell, int position) {

        if (cell != null) {
            textView.setText(cell.name);
            textView.setSelected(cell.selected);
            ImageLoader.getInstance().getBitmapFromCache(textView.getContext(), cell.imageUrl,
                    bitmap -> {
                        BitmapDrawable bitmapDrawable = new BitmapDrawable(null, bitmap);
                        int bound = ResUtil.getDimen(R.dimen.m36);
                        bitmapDrawable.setBounds(0, 0, bound, bound);
                        textView.setCompoundDrawables(null, bitmapDrawable, null, null);
                    });
            View parentView = (View) textView.getParent();
            parentView.setOnClickListener(v -> {
                if (mListener != null) {
                    parentView.setTag(cell);
                    mListener.onViewClick(parentView, position);
                }
            });
        }
    }

    @Override
    public int spanSize() {
        return ResUtil.getInt(R.integer.home_item_function_pair_spans);
    }
}
