package com.kaolafm.kradio.message.comprehensive;

import android.content.Context;
import android.util.Log;

import com.kaolafm.kradio.lib.utils.SharedPreferenceUtil;

public class MessageRateLimit {

    public static void updateRateLimit(Context context, int range, int limit){
        long now = System.currentTimeMillis();
        long before = getBucketFlushTime(context);


        if(getDifferentHours(before, now) > range){ //重置time和计数
            putBucketFlushTime(context, now);
            putLeftTokenCount(context, limit);
        } else { //消耗令牌
            int oldCount = getLeftTokenCount(context);
            Log.d("SocketClient", "oldCount: "+oldCount);
            putLeftTokenCount(context, oldCount - 1);
        }
    }

    private static final String MESSAGE_RATE_LIMIT_STORE = "message_rate_limit_store";
    private static final String MESSAGE_RATE_LIMIT_BUCKET_FLUSH_TIME_KEY = "message_rate_limit_bucket_flush_time_key";
    private static final String MESSAGE_RATE_LIMIT_LEFT_TOKEN_COUNT_KEY = "message_rate_limit_left_token_count_key";

    static long getBucketFlushTime(Context context){
        SharedPreferenceUtil sp = SharedPreferenceUtil.getInstance(context, MESSAGE_RATE_LIMIT_STORE);
        return sp.getLong(MESSAGE_RATE_LIMIT_BUCKET_FLUSH_TIME_KEY, 0);
    }
    static void putBucketFlushTime(Context context, long time){
        SharedPreferenceUtil sp = SharedPreferenceUtil.getInstance(context, MESSAGE_RATE_LIMIT_STORE);
        sp.putLong(MESSAGE_RATE_LIMIT_BUCKET_FLUSH_TIME_KEY, time);
    }

    public static int getLeftTokenCount(Context context){
        SharedPreferenceUtil sp = SharedPreferenceUtil.getInstance(context, MESSAGE_RATE_LIMIT_STORE);
        int count = sp.getInt(MESSAGE_RATE_LIMIT_LEFT_TOKEN_COUNT_KEY, 0);

        Log.d("SocketClient", "getLeftTokenCount: "+count);

        return count;
    }

    static void putLeftTokenCount(Context context, int count){
        SharedPreferenceUtil sp = SharedPreferenceUtil.getInstance(context, MESSAGE_RATE_LIMIT_STORE);
        sp.putInt(MESSAGE_RATE_LIMIT_LEFT_TOKEN_COUNT_KEY, count);
    }

    static long getDifferentHours(long before, long now){
        long dayM = 1000 * 24 * 60 * 60;
        long hourM = 1000 * 60 * 60;
        long differ = now - before;

        long hours = differ % dayM / hourM + 24 * (differ / dayM);

        return hours;
    }
}
