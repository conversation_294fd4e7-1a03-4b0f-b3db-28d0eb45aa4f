package com.kaolafm.kradio.lib.init;

import com.kaolafm.kradio.lib.common.ModelConstant;

public class ModelManager {

    private int MODEL = ModelConstant.MODEL_INVALID;

    private volatile static ModelManager mInstance = null;

    public static ModelManager getInstance() {
        if(mInstance == null){
            synchronized (ModelManager.class){
                if(mInstance == null){
                    mInstance = new ModelManager();
                }
            }
        }
        return mInstance;
    }

    public int getModel() {
        return MODEL;
    }

    public void setModel(int MODEL) {
        this.MODEL = MODEL;
        ModelInitManager.setModel(MODEL);
    }
}
