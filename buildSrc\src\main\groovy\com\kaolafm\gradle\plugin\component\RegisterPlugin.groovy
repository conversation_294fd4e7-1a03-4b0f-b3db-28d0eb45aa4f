package com.kaolafm.gradle.plugin.component

import com.android.build.gradle.AppExtension
import com.android.build.gradle.AppPlugin
import org.gradle.api.Plugin
import org.gradle.api.Project
/**
 * 自动注册插件入口
 * <AUTHOR>
 * @since 17/3/14 17:35
 */
public class RegisterPlugin implements Plugin<Project> {
    public static final String EXT_NAME = 'autoRegister'

    @Override
    public void apply(Project project) {
        /**
         * 注册transform接口
         */
        def isApp = project.plugins.hasPlugin(AppPlugin)
        def registerExtension = project.extensions.create(EXT_NAME, RegisterExtension, project) as RegisterExtension
        if (isApp) {
            println 'project(' + project.name + ') apply auto-register plugin'
            def android = project.extensions.getByType(AppExtension)
            def transformImpl = new RegisterTransform(project)
            android.registerTransform(transformImpl)
            project.afterEvaluate {
//                init(project, transformImpl)//此处要先于transformImpl.transform方法执行
                registerExtension.addRegisterInfo()
                DefaultRegistryHelper.addDefaultRegistry(registerExtension.list)
                transformImpl.extension = registerExtension

            }
        }
    }

    static void init(Project project, RegisterTransform transformImpl) {
        RegisterExtension extension = project.extensions.findByName(EXT_NAME) as RegisterExtension
        extension.project = project
        extension.convertConfig()
        DefaultRegistryHelper.addDefaultRegistry(extension.list)
        transformImpl.extension = extension
    }

}
