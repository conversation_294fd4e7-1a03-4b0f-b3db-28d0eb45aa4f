package com.kaolafm.kradio.live.comprehensive.goods;

import android.content.Context;
import androidx.viewpager.widget.ViewPager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.kaolafm.kradio.live.comprehensive.goods.adapter.GoodsGVAdapter;
import com.kaolafm.kradio.live.comprehensive.goods.adapter.GoodsVPAdapter;
import com.kaolafm.kradio.live.comprehensive.goods.utils.ShopExpressionUtil;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.opensdk.api.goods.model.Goods;

import java.util.ArrayList;
import java.util.List;

import static android.content.Context.LAYOUT_INFLATER_SERVICE;

/**
 * Created by Ren on 2023/2/13.
 */
public class GoodsPanelControl {

    private int columns = 6;
    private int rows = 1;
    //每页显示的表情view
    private List<View> views = new ArrayList<>();
    private RecyclerView mRecyclerView;
    private ShopExpressionUtil shopExpressionUtil;
    private LayoutInflater inflater;
    private List<Goods> mDatas = new ArrayList<>();
    private Context mContext;
    private LinearLayout mDotsLayout;
    private ViewPager mViewpager;

    public interface OnShopSelectedListener {
        void onSelected(Goods goods);
    }

    private OnShopSelectedListener onShopSelectedListener;

    public void setOnShopSelectedListener(OnShopSelectedListener listener) {
        onShopSelectedListener = listener;
    }

    public interface OnShopPurchaseListener {
        void onPurchase(Goods goods);
    }

    private OnShopPurchaseListener onShopPurchaseListener;

    public void setOnShopPurchaseListener(OnShopPurchaseListener listener) {
        onShopPurchaseListener = listener;
    }


    /**
     * @param context
     * @param viewPager    竖屏商品面板的ViewPager
     * @param recyclerView 横屏商品面板的RecycleView
     * @param dotsLayout   竖屏商品面板的小圆点父布局
     */
    public GoodsPanelControl(Context context, ViewPager viewPager, RecyclerView recyclerView, LinearLayout dotsLayout) {
        mContext = context;
        inflater = (LayoutInflater) context.getSystemService(LAYOUT_INFLATER_SERVICE);
        mViewpager = viewPager;
        mRecyclerView = recyclerView;
        mDotsLayout = dotsLayout;
    }

    /**
     * @param datas
     */
    public void setData(List<Goods> datas) {
        mDatas.clear();
        mDatas = datas;
        initPortraitShop();
        intitLandscapeShop();
    }

    /**
     * 初始化商品面板，横屏时显示
     */
    private void intitLandscapeShop() {
        if (mDatas == null || mDatas.size()<=0) {
            return;
        }
        if (shopExpressionUtil == null) {
            shopExpressionUtil = new ShopExpressionUtil();
        }
//        if (mDatas == null) {
//            mDatas = shopExpressionUtil.initStaticShops(mContext);
//        }

        shopExpressionUtil.shopView(mContext, mRecyclerView, mDatas);
    }

    /**
     * 初始化商品面板，竖屏时显示
     */
    private void initPortraitShop() {
        if (mDatas == null) {
            return;
        }
        if (shopExpressionUtil == null) {
            shopExpressionUtil = new ShopExpressionUtil();
        }
//        if (mDatas == null) {
//            mDatas = shopExpressionUtil.initStaticShops(mContext);
//        }
        int pagesize = shopExpressionUtil.getPagerCount(mDatas.size(), columns, rows);
        // 获取页数
        for (int i = 0; i < pagesize; i++) {
            views.add(shopExpressionUtil.viewPagerItem(mContext, i, mDatas, columns, rows, null));
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(16, 16);
            params.setMargins(10, 0, 10, 0);
            if (pagesize > 1) {
                mDotsLayout.addView(dotsItem(i), params);
            }
        }
        if (pagesize > 1) {
            mDotsLayout.setVisibility(View.VISIBLE);
        } else {
            mDotsLayout.setVisibility(View.GONE);
        }
        GoodsVPAdapter mVpAdapter = new GoodsVPAdapter(views);
        mViewpager.setAdapter(mVpAdapter);
        mViewpager.setOnPageChangeListener(new PageChangeListener());
        mViewpager.setCurrentItem(0);
        if (pagesize > 1) {
            mDotsLayout.getChildAt(0).setSelected(true);
        }

        shopExpressionUtil.setShopClickListener(new ShopExpressionUtil.ShopClickListener() {
            @Override
            public void onClick(int position, Goods goods) {
                if (onShopSelectedListener != null) {
                    onShopSelectedListener.onSelected(goods);
                }
            }
        });
        shopExpressionUtil.setShopPurchaseListener(new ShopExpressionUtil.ShopPurchaseListener() {
            @Override
            public void onPurchase(int position, Goods goods) {
                if (onShopPurchaseListener != null) {
                    onShopPurchaseListener.onPurchase(goods);
                }
            }
        });
    }

    /**
     * 表情页切换时，底部小圆点
     *
     * @param position
     * @return
     */
    private ImageView dotsItem(int position) {
        View layout = inflater.inflate(R.layout.comprehensive_goods_dot_image, null);
        ImageView iv = (ImageView) layout.findViewById(R.id.face_dot);
        iv.setId(position);
        return iv;
    }

    private boolean isScrolling = false;
    private boolean left = false;//从右向左，positionOffset值逐渐增大
    private boolean right = false;//从左向右，positionOffset值逐渐减小
    private int lastValue = -1;
    private boolean isClearStatus = true;//是否清除商品选中的状态在切换页面时

    /**
     * 是否清除商品选中的状态在切换页面时
     *
     * @param isClearStatus
     */
    public void isClearStatus(boolean isClearStatus) {
        this.isClearStatus = isClearStatus;
    }

    /**
     * 表情页改变时，dots效果也要跟着改变
     */
    class PageChangeListener implements ViewPager.OnPageChangeListener {

        @Override
        public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
            //这里的position是当前屏幕可见页面的第一个页面
            if (isScrolling) {
                if (lastValue > positionOffsetPixels) {
                    //递减，向右滑动
                    right = true;
                    left = false;
                } else if (lastValue < positionOffsetPixels) {
                    //递增，向左滑动
                    right = false;
                    left = true;
                } else if (lastValue == positionOffsetPixels) {
                    right = left = false;
                }
            }
//            Log.i("CustormViewPager", "onPageScrolled: positionOffset =>" + positionOffset + "; positionOffsetPixels =>" + positionOffsetPixels);
//            Log.i("CustormViewPager", "onPageScrolled: right =>" + right + "; left =>" + left);
            lastValue = positionOffsetPixels;
        }

        @Override
        public void onPageSelected(int position) {
            for (int i = 0; i < mDotsLayout.getChildCount(); i++) {
                mDotsLayout.getChildAt(i).setSelected(false);
            }
            mDotsLayout.getChildAt(position).setSelected(true);
            for (int i = 0; i < views.size(); i++) {//清除选中，当商品页面切换到另一个商品页面
                RecyclerView view = (RecyclerView) views.get(i);
                GoodsGVAdapter adapter = (GoodsGVAdapter) view.getAdapter();
                if (isClearStatus) {
                    adapter.clearSelection();
                    if (onShopSelectedListener != null) {
                        onShopSelectedListener.onSelected(mDatas.get(i));
                    }
                }

            }
        }

        @Override
        public void onPageScrollStateChanged(int state) {
            //ViewPager.SCROLL_STATE_IDLE 空闲状态 0；CustormViewPager.SCROLL_STATE_DRAGGING 正在滑动 1
            //ViewPager.SCROLL_STATE_SETTLING 滑动完毕 2；页面开始滑动时，状态变化（1,2,0）
            if (state == ViewPager.SCROLL_STATE_DRAGGING) {
                isScrolling = true;
            } else {
                isScrolling = false;
            }
            if (state == ViewPager.SCROLL_STATE_SETTLING) {
//                Log.i("CustormViewPager", "----------------right =>" + right + "; left =>" + left + "----------------------------");
                right = left = false;
//                lastValue = -1;
            }
        }
    }
}
