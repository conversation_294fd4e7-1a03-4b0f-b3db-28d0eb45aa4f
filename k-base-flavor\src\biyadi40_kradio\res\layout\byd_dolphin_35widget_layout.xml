<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/widget_playinfo_layout"
    android:layout_width="@dimen/byd_dolphin_35widget_layout_width"
    android:layout_height="@dimen/byd_dolphin_35widget_layout_height"
    android:orientation="vertical">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/widget_bg"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY"
            android:src="@drawable/byd_dolphin_35widget_bg" />


        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/byd_dolphin_35widget_icon_title_marginTop"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/widget_icon"
                android:layout_width="@dimen/byd_dolphin_35widget_icon_width"
                android:layout_height="@dimen/byd_dolphin_35widget_icon_height"
                android:src="@drawable/byd_35_app_icon" />

            <TextView
                android:id="@+id/widget_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/byd_dolphin_35widget_title_marginStart"
                android:text="@string/app_name"
                android:textColor="#607FA3"
                android:textSize="@dimen/byd_35widget_textSize" />
        </LinearLayout>

        <!-- 不能使用imageview的子类，限制只能使用ImageView
            详见：https://developer.android.com/guide/topics/appwidgets-->
        <ImageView
            android:id="@+id/widget_cover"
            android:layout_width="@dimen/byd_dolphin_35widget_thumb_width"
            android:layout_height="@dimen/byd_dolphin_35widget_thumb_height"
            android:layout_gravity="center"
            android:scaleType="fitXY"
            android:src="@drawable/byd_dolphin_35widget_thumb_yunting" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/widget_prev"
                android:layout_width="@dimen/byd_dolphin_35widget_button_size"
                android:layout_height="@dimen/byd_dolphin_35widget_button_size"
                android:scaleType="fitXY"
                android:src="@drawable/byd_dolphin_35widget_pre" />

            <ImageView
                android:id="@+id/widget_play_or_pause"
                android:layout_width="@dimen/byd_dolphin_35widget_button_size"
                android:layout_height="@dimen/byd_dolphin_35widget_button_size"
                android:layout_marginStart="@dimen/byd_dolphin_35widget_button_margin"
                android:layout_marginEnd="@dimen/byd_dolphin_35widget_button_margin"
                android:scaleType="fitXY"
                android:src="@drawable/byd_dolphin_35widget_stop" />

            <ImageView
                android:id="@+id/widget_next"
                android:layout_width="@dimen/byd_dolphin_35widget_button_size"
                android:layout_height="@dimen/byd_dolphin_35widget_button_size"
                android:scaleType="fitXY"
                android:src="@drawable/byd_dolphin_35widget_next" />

        </LinearLayout>

        <TextView
            android:id="@+id/widget_audio_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal|bottom"
            android:layout_marginBottom="@dimen/byd_35widget_text_margin_bottom"
            android:ellipsize="end"
            android:lines="1"
            android:maxEms="7"
            android:text="@string/default_artist"
            android:textColor="@color/byd_dolphin_35widget_title_color"
            android:textSize="@dimen/byd_35widget_textSize"
             />

    </FrameLayout>

</LinearLayout>
