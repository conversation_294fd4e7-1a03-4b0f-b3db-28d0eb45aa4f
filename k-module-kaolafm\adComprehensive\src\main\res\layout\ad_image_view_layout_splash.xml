<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_ad_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/transparent">

        <ImageView
            android:id="@+id/iv_ad"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clickable="true"
            android:scaleType="fitCenter"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_ad_image_label"
            android:layout_width="@dimen/m72"
            android:layout_height="@dimen/m40"
            android:layout_margin="@dimen/m32"
            android:background="@drawable/bg_ad_mark"
            android:gravity="center"
            android:text="@string/ad"
            android:textColor="@color/ad_sign_text_color"
            android:textSize="@dimen/m20"
            app:layout_constraintRight_toRightOf="@+id/iv_ad"
            app:layout_constraintTop_toTopOf="parent" />

        <!--        <ImageView-->
        <!--            android:id="@+id/iv_audio_sign"-->
        <!--            android:layout_width="@dimen/x39"-->
        <!--            android:layout_height="@dimen/y37"-->
        <!--            android:layout_gravity="center_vertical"-->
        <!--            android:background="@drawable/bg_ad_mark"-->
        <!--            android:paddingLeft="@dimen/x20"-->
        <!--            android:paddingTop="@dimen/y5"-->
        <!--            android:paddingBottom="@dimen/y5"-->
        <!--            android:visibility="gone"-->
        <!--            app:layout_constraintRight_toLeftOf="@id/tv_ad_image_label"-->
        <!--            app:layout_constraintTop_toTopOf="@id/tv_ad_image_label"-->
        <!--            tools:src="@drawable/kradio_aduio_ad_horn"-->
        <!--            tools:visibility="visible" />-->

        <TextView

            android:id="@+id/tv_ad_skip"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/m56"
            android:layout_marginStart="@dimen/m32"
            android:layout_marginTop="@dimen/m32"
            android:background="@drawable/bg_ad_image_skip"
            android:paddingLeft="@dimen/m24"
            android:paddingTop="@dimen/m10"
            android:gravity="center"
            android:paddingRight="@dimen/m24"
            android:paddingBottom="@dimen/m10"
            android:textColor="@color/text_color_7"
            android:textSize="@dimen/text_size3"
            app:layout_constraintLeft_toLeftOf="@+id/iv_ad"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</merge>
