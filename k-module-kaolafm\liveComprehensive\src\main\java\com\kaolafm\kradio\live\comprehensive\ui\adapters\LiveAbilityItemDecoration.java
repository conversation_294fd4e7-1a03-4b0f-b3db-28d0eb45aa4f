package com.kaolafm.kradio.live.comprehensive.ui.adapters;

import android.graphics.Rect;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.ResUtil;

/**
 * <AUTHOR>
 * @date 2023-03-08
 */
public class LiveAbilityItemDecoration extends RecyclerView.ItemDecoration {

    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {

        outRect.left = ResUtil.getDimen(R.dimen.m34);
        outRect.right = ResUtil.getDimen(R.dimen.m34);
//        int adapterPosition = parent.getChildAdapterPosition(view);
//        if (adapterPosition == 0) {
//            outRect.left = 0;
//        } else {
//            outRect.left = ResUtil.getDimen(R.dimen.m68);
//        }
    }
}
