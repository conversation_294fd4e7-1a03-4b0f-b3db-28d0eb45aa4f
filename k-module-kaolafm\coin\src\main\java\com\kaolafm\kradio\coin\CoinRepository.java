package com.kaolafm.kradio.coin;

import android.text.TextUtils;
import com.kaolafm.kradio.common.http.CommonRequestParamsUtil;
import com.kaolafm.opensdk.account.token.AccessTokenManager;
import com.kaolafm.opensdk.account.token.KaolaAccessToken;
import com.kaolafm.opensdk.api.coin.ShoppingMallQrRequest;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.socket.SocketApiConstants;
import com.kaolafm.opensdk.socket.SocketListener;
import com.kaolafm.opensdk.socket.SocketManager;
import java.util.Map;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;


public final class CoinRepository {
    private CoinSocketListener mSocketListener;

    public final void getCoinCount(@NotNull HttpCallback callback) {
        Intrinsics.checkParameterIsNotNull(callback, "callback");
        AccessTokenManager var10000 = AccessTokenManager.getInstance();
        Intrinsics.checkExpressionValueIsNotNull(var10000, "AccessTokenManager.getInstance()");
        KaolaAccessToken var3 = var10000.getKaolaAccessToken();
        Intrinsics.checkExpressionValueIsNotNull(var3, "AccessTokenManager.getInstance().kaolaAccessToken");
        String userId = var3.getUserId();
        if (TextUtils.isEmpty((CharSequence)userId)) {
            callback.onError(new ApiException("未登录"));
        } else {
            this.mSocketListener = new CoinSocketListener(callback);
            SocketManager var4 = SocketManager.getInstance().setMap((Map)CommonRequestParamsUtil.getCommonParams()).setSocketHost(SocketApiConstants.SOCKET_HOST);
            CoinSocketListener var10001 = this.mSocketListener;
            if (var10001 == null) {
                Intrinsics.throwUninitializedPropertyAccessException("mSocketListener");
            }

            var4.request((SocketListener)var10001);
        }

    }

    public final void getQrCode(@NotNull HttpCallback callback) {
        Intrinsics.checkParameterIsNotNull(callback, "callback");
        (new ShoppingMallQrRequest()).getQrOfShoppingMall(250, callback);
    }

    public final void release() {
        try {
            if (((CoinRepository)this).mSocketListener != null) {
                SocketManager var10000 = SocketManager.getInstance();
                CoinSocketListener var10001 = this.mSocketListener;
                if (var10001 == null) {
                    Intrinsics.throwUninitializedPropertyAccessException("mSocketListener");
                }

                var10000.removeListener((SocketListener)var10001);
            }
        } catch (Exception var2) {
            var2.printStackTrace();
        }

    }

    // $FF: synthetic method
    public static final CoinSocketListener access$getMSocketListener$p(CoinRepository $this) {
        CoinSocketListener var10000 = $this.mSocketListener;
        if (var10000 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("mSocketListener");
        }

        return var10000;
    }

    // $FF: synthetic method
    public static final void access$setMSocketListener$p(CoinRepository $this, CoinSocketListener var1) {
        $this.mSocketListener = var1;
    }
}


