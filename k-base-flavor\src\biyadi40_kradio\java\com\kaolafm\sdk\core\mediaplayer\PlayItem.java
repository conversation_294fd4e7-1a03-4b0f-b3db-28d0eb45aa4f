//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package com.kaolafm.sdk.core.mediaplayer;

import android.os.Parcel;
import android.os.Parcelable;
import android.os.Parcelable.Creator;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.player.logic.model.PlayItemType;

import java.util.HashMap;
import java.util.Map;

public class PlayItem implements Parcelable {
    public static final int CTG_TYPE_TX = 131;
    private long audioId;
    private String title;
    private String playUrl;
    private String defaultPlayUrl;
    private String offlineUrl;
    private boolean isOffline;
    private String offlinePlayUrl;
    private int position;
    private int duration;
    private int totalDuration;
    private String audioDes;
    private long albumId;
    private String albumPic;
    private String albumOfflinePic;
    private String albumName;
    private int orderNum;
    private String mp3PlayUrl;
    private String m3u8PlayUrl;
    private String shareUrl;
    private long categoryId;
    private String hosts;
    private long fileSize;
    private int isLiked;
    private String updateTime;
    private long createTime;
    private String clockId;
    private boolean isInterrupted;
    public static final int DATA_SRC_HISTORY = 1;
    private int dataSrc;
    private boolean isLivingUrl;
    private long startTime;
    private long finishTime;
    private String beginTime;
    private String endTime;
    private long liveId;
    private String audioPic;
    private PlayItemType type;
    private int status;
    private String dnsAddress;
    private String aacPlayUrl32;
    private String aacPlayUrl64;
    private String aacPlayUrl128;
    private String aacPlayUrl320;
    private String icon;
    private boolean canAutoDel;
    private String genre;
    private String mid;
    private String frequencyChannel;
    private String recommendReason;
    private int isThirdParty;
    private String radioSubTag;
    private int radioSubTagType;
    private String mainTitleName;
    private String subheadName;
    private boolean isTempTask;
    private boolean isTempTaskNeedStateCallBack;
    private Map mDataMap;
    public static final Creator<PlayItem> CREATOR = new Creator<PlayItem>() {
        public PlayItem createFromParcel(Parcel var1) {
            return new PlayItem(var1);
        }

        public PlayItem[] newArray(int var1) {
            return new PlayItem[var1];
        }
    };

    public PlayItem() {
        this.isInterrupted = false;
        this.type = PlayItemType.DEFAULT;
        this.isTempTask = false;
        this.isTempTaskNeedStateCallBack = false;
        this.mDataMap = new HashMap();
    }

    public PlayItemType getType() {
        return this.type;
    }

    public void setType(PlayItemType var1) {
        this.type = var1;
    }

    public long getLiveId() {
        return this.liveId;
    }

    public void setLiveId(long var1) {
        this.liveId = var1;
    }

    public void setAudioId(long var1) {
        this.audioId = var1;
    }

    public long getAudioId() {
        return this.audioId;
    }

    public String getAudioPic() {
        return this.audioPic;
    }

    public void setAudioPic(String var1) {
        this.audioPic = var1;
    }

    public void setTitle(String var1) {
        this.title = var1;
    }

    public String getTitle() {
        return this.title;
    }

    public void setPlayUrl(String var1) {
        this.playUrl = var1;
    }

    public String getPlayUrl() {
        return this.playUrl;
    }

    public void setIsOffline(boolean var1) {
        this.isOffline = var1;
    }

    public boolean getIsOffline() {
        return this.isOffline;
    }

    public void setOfflinePlayUrl(String var1) {
        this.offlinePlayUrl = var1;
    }

    public String getOfflinePlayUrl() {
        return this.offlinePlayUrl;
    }

    public void setPosition(int var1) {
        this.position = var1;
    }

    public int getPosition() {
        return this.position;
    }

    public void setDuration(int var1) {
        this.duration = var1;
    }

    public int getDuration() {
        return this.duration;
    }

    public void setAudioDes(String var1) {
        this.audioDes = var1;
    }

    public void setAlbumId(long var1) {
        this.albumId = var1;
    }

    public void setAlbumPic(String var1) {
        this.albumPic = var1;
    }

    public void setAlbumName(String var1) {
        this.albumName = var1;
    }

    public void setOrderNum(int var1) {
        this.orderNum = var1;
    }

    public void setMp3PlayUrl(String var1) {
        this.mp3PlayUrl = var1;
    }

    public void setShareUrl(String var1) {
        this.shareUrl = var1;
    }

    public void setCategoryId(long var1) {
        this.categoryId = var1;
    }

    public void setHosts(String var1) {
        this.hosts = var1;
    }

    public void setFileSize(long var1) {
        this.fileSize = var1;
    }

    public void setIsLiked(int var1) {
        this.isLiked = var1;
    }

    public void setUpdateTime(String var1) {
        this.updateTime = var1;
    }

    public void setClockId(String var1) {
        this.clockId = var1;
    }

    public String getAudioDes() {
        return this.audioDes;
    }

    public long getAlbumId() {
        return this.albumId;
    }

    public String getAlbumPic() {
        return this.albumPic;
    }

    public String getAlbumName() {
        return this.albumName;
    }

    public int getOrderNum() {
        return this.orderNum;
    }

    public String getMp3PlayUrl() {
        return this.mp3PlayUrl;
    }

    public String getShareUrl() {
        return this.shareUrl;
    }

    public long getCategoryId() {
        return this.categoryId;
    }

    public String getHosts() {
        return this.hosts;
    }

    public long getFileSize() {
        return this.fileSize;
    }

    public int getIsLiked() {
        return this.isLiked;
    }

    public String getUpdateTime() {
        return this.updateTime;
    }

    public String getClockId() {
        return this.clockId;
    }

    public long getStartTime() {
        return this.startTime;
    }

    public void setStartTime(long var1) {
        this.startTime = var1;
    }

    public long getFinishTime() {
        return this.finishTime;
    }

    public void setFinishTime(long var1) {
        this.finishTime = var1;
    }

    public String getBeginTime() {
        return this.beginTime;
    }

    public void setBeginTime(String var1) {
        this.beginTime = var1;
    }

    public String getEndTime() {
        return this.endTime;
    }

    public void setEndTime(String var1) {
        this.endTime = var1;
    }

    public void setIsInterrupted(boolean var1) {
        this.isInterrupted = var1;
    }

    public boolean getIsInterrupted() {
        return this.isInterrupted;
    }

    public long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(long var1) {
        this.createTime = var1;
    }

    public int getDataSrc() {
        return this.dataSrc;
    }

    public void setDataSrc(int var1) {
        this.dataSrc = var1;
    }

    public String getAlbumOfflinePic() {
        return this.albumOfflinePic;
    }

    public String getM3u8PlayUrl() {
        return this.m3u8PlayUrl;
    }

    public void setM3u8PlayUrl(String var1) {
        this.m3u8PlayUrl = var1;
    }

    public void setAlbumOfflinePic(String var1) {
        this.albumOfflinePic = var1;
    }

    public boolean isLivingUrl() {
        return this.isLivingUrl;
    }

    public void setIsLivingUrl(boolean var1) {
        this.isLivingUrl = var1;
    }

    public String getOfflineUrl() {
        return this.offlineUrl;
    }

    public void setOfflineUrl(String var1) {
        this.offlineUrl = var1;
    }

    public int getTotalDuration() {
        return this.totalDuration;
    }

    public void setTotalDuration(int var1) {
        this.totalDuration = var1;
    }

    public int getStatus() {
        return this.status;
    }

    public void setStatus(int var1) {
        this.status = var1;
    }

    public String getDnsAddress() {
        return this.dnsAddress;
    }

    public void setDnsAddress(String var1) {
        this.dnsAddress = var1;
    }

    public String getAacPlayUrl32() {
        return this.aacPlayUrl32;
    }

    public void setAacPlayUrl32(String var1) {
        this.aacPlayUrl32 = var1;
    }

    public String getAacPlayUrl64() {
        return this.aacPlayUrl64;
    }

    public void setAacPlayUrl64(String var1) {
        this.aacPlayUrl64 = var1;
    }

    public String getAacPlayUrl128() {
        return this.aacPlayUrl128;
    }

    public void setAacPlayUrl128(String var1) {
        this.aacPlayUrl128 = var1;
    }

    public void setIcon(String var1) {
        this.icon = var1;
    }

    public String getIcon() {
        return this.icon;
    }

    public boolean getCanAutoDel() {
        return this.canAutoDel;
    }

    public void setCanAutoDel(boolean var1) {
        this.canAutoDel = var1;
    }

    public String getGenre() {
        return this.genre;
    }

    public void setGenre(String var1) {
        this.genre = var1;
    }

    public String getMid() {
        return this.mid;
    }

    public void setMid(String var1) {
        this.mid = var1;
    }

    public String getFrequencyChannel() {
        return this.frequencyChannel;
    }

    public void setFrequencyChannel(String var1) {
        this.frequencyChannel = var1;
    }

    public String getPic() {
        return this.audioPic != null && this.audioPic.length() != 0 ? this.audioPic : this.albumPic;
    }

    public String getRecommendReason() {
        return this.recommendReason;
    }

    public void setRecommendReason(String var1) {
        this.recommendReason = var1;
    }

    public int getIsThirdParty() {
        return this.isThirdParty;
    }

    public void setIsThirdParty(int var1) {
        this.isThirdParty = var1;
    }

    public int describeContents() {
        return 0;
    }

    public void writeToParcel(Parcel var1, int var2) {
        var1.writeLong(this.audioId);
        var1.writeString(this.title);
        var1.writeString(this.playUrl);
        var1.writeString(this.offlineUrl);
        var1.writeByte((byte)(this.isOffline ? 1 : 0));
        var1.writeString(this.offlinePlayUrl);
        var1.writeInt(this.position);
        var1.writeInt(this.duration);
        var1.writeString(this.audioDes);
        var1.writeLong(this.albumId);
        var1.writeString(this.albumPic);
        var1.writeString(this.albumName);
        var1.writeInt(this.orderNum);
        var1.writeString(this.mp3PlayUrl);
        var1.writeString(this.shareUrl);
        var1.writeLong(this.categoryId);
        var1.writeString(this.hosts);
        var1.writeLong(this.fileSize);
        var1.writeInt(this.isLiked);
        var1.writeString(this.updateTime);
        var1.writeLong(this.createTime);
        var1.writeString(this.clockId);
        var1.writeByte((byte)(this.isInterrupted ? 1 : 0));
        var1.writeString(this.offlinePlayUrl);
        var1.writeLong(this.startTime);
        var1.writeLong(this.finishTime);
        var1.writeString(this.beginTime);
        var1.writeString(this.endTime);
        var1.writeInt(this.status);
        var1.writeString(this.dnsAddress);
        var1.writeString(this.aacPlayUrl32);
        var1.writeString(this.aacPlayUrl64);
        var1.writeString(this.aacPlayUrl128);
        var1.writeString(this.icon);
        var1.writeByte((byte)(this.canAutoDel ? 1 : 0));
        var1.writeString(this.genre);
        var1.writeString(this.mid);
        var1.writeString(this.frequencyChannel);
        var1.writeString(this.recommendReason);
        var1.writeInt(this.isThirdParty);
        var1.writeString(this.radioSubTag);
        var1.writeInt(this.radioSubTagType);
        var1.writeString(this.aacPlayUrl320);
        var1.writeString(this.mainTitleName);
        var1.writeString(this.subheadName);
        var1.writeString(this.defaultPlayUrl);
        var1.writeByte((byte)(this.isTempTask ? 1 : 0));
        var1.writeByte((byte)(this.isTempTaskNeedStateCallBack ? 1 : 0));
    }

    private PlayItem(Parcel var1) {
        this.isInterrupted = false;
        this.type = PlayItemType.DEFAULT;
        this.isTempTask = false;
        this.isTempTaskNeedStateCallBack = false;
        this.mDataMap = new HashMap();
        this.audioId = var1.readLong();
        this.title = var1.readString();
        this.playUrl = var1.readString();
        this.offlineUrl = var1.readString();
        this.isOffline = var1.readByte() != 0;
        this.offlinePlayUrl = var1.readString();
        this.position = var1.readInt();
        this.duration = var1.readInt();
        this.audioDes = var1.readString();
        this.albumId = var1.readLong();
        this.albumPic = var1.readString();
        this.albumName = var1.readString();
        this.orderNum = var1.readInt();
        this.mp3PlayUrl = var1.readString();
        this.shareUrl = var1.readString();
        this.categoryId = var1.readLong();
        this.hosts = var1.readString();
        this.fileSize = var1.readLong();
        this.isLiked = var1.readInt();
        this.updateTime = var1.readString();
        this.createTime = var1.readLong();
        this.clockId = var1.readString();
        this.isInterrupted = var1.readByte() != 0;
        this.offlinePlayUrl = var1.readString();
        this.startTime = var1.readLong();
        this.finishTime = var1.readLong();
        this.beginTime = var1.readString();
        this.endTime = var1.readString();
        this.status = var1.readInt();
        this.dnsAddress = var1.readString();
        this.aacPlayUrl32 = var1.readString();
        this.aacPlayUrl64 = var1.readString();
        this.aacPlayUrl128 = var1.readString();
        this.icon = var1.readString();
        this.canAutoDel = var1.readByte() != 0;
        this.genre = var1.readString();
        this.mid = var1.readString();
        this.frequencyChannel = var1.readString();
        this.recommendReason = var1.readString();
        this.isThirdParty = var1.readInt();
        this.radioSubTag = var1.readString();
        this.radioSubTagType = var1.readInt();
        this.aacPlayUrl320 = var1.readString();
        this.mainTitleName = var1.readString();
        this.subheadName = var1.readString();
        this.defaultPlayUrl = var1.readString();
        this.isTempTask = var1.readByte() != 0;
        this.isTempTaskNeedStateCallBack = var1.readByte() != 0;
    }

    public boolean equals(Object var1) {
        if (var1 instanceof PlayItem) {
            return ((PlayItem)var1).getAudioId() == this.getAudioId();
        } else {
            return false;
        }
    }

    public String getRadioSubTag() {
        return this.radioSubTag;
    }

    public void setRadioSubTag(String var1) {
        this.radioSubTag = var1;
    }

    public int getRadioSubTagType() {
        return this.radioSubTagType;
    }

    public void setRadioSubTagType(int var1) {
        this.radioSubTagType = var1;
    }

    public String getAacPlayUrl320() {
        return this.aacPlayUrl320;
    }

    public void setAacPlayUrl320(String var1) {
        this.aacPlayUrl320 = var1;
    }

    public String getMainTitleName() {
        return this.mainTitleName;
    }

    public void setMainTitleName(String var1) {
        this.mainTitleName = var1;
    }

    public String getSubheadName() {
        return this.subheadName;
    }

    public void setSubheadName(String var1) {
        this.subheadName = var1;
    }

    public String getDefaultPlayUrl() {
        return this.defaultPlayUrl;
    }

    public void setDefaultPlayUrl(String var1) {
        this.defaultPlayUrl = var1;
    }

    public boolean isTempTask() {
        return this.isTempTask;
    }

    public void setTempTask(boolean var1) {
        this.isTempTask = var1;
    }

    public boolean isTempTaskNeedStateCallBack() {
        return this.isTempTaskNeedStateCallBack;
    }

    public void setTempTaskNeedStateCallBack(boolean var1) {
        this.isTempTaskNeedStateCallBack = var1;
    }

    public boolean isTempTaskNotNeedCallback() {
        return this.isTempTask && !this.isTempTaskNeedStateCallBack;
    }

    public void addMapCacheData(String var1, String var2) {
        if (var1 != null && var2 != null) {
            this.mDataMap.put(var1, var2);
        }
    }

    public String getMapCacheData(String var1) {
        return var1 == null ? null : (String)this.mDataMap.get(var1);
    }

    public String toString() {
        return StringUtil.join(new Object[]{"current playItem = ", this.getTitle(), ", audioId = ", this.getAudioId(), ", position = ", this.getPosition(), ", duration = ", this.getDuration()});
    }
}
