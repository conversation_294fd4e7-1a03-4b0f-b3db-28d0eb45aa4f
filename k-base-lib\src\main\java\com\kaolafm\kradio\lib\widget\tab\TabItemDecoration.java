package com.kaolafm.kradio.lib.widget.tab;

import android.graphics.Rect;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.State;
import android.view.View;

/**
 * 首页的顶部tab的间距
 * <AUTHOR>
 * @date 2019-07-28
 */
public class TabItemDecoration extends RecyclerView.ItemDecoration {

    private int space;

    public TabItemDecoration(int space) {
        this.space = space;
    }

    @Override
    public void getItemOffsets(Rect outRect, View view,
            RecyclerView parent, State state) {
        //最后一条的postion
        int itemCount = state.getItemCount() - 1;
        //当前条目的position
        int pos = parent.getChildLayoutPosition(view);
        //最后一条
        if (pos != itemCount) {
            outRect.right = space;
        }
    }
}
