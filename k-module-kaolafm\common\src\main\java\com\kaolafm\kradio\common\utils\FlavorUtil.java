package com.kaolafm.kradio.common.utils;

import com.kaolafm.kradio.lib.base.flavor.RequestOptions;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;

/**
 *
 * <AUTHOR>
 */
public class FlavorUtil {

    public static String getHttp443Url(String url) {
        if(StringUtil.isEmpty(url)){
            return url;
        }
        RequestOptions mRequestOptions = ClazzImplUtil.getInter("RequestOptionsImpl");
        if(mRequestOptions!=null && mRequestOptions.getHttpsStrategy().isHttps("httpsPort")){
            if(url.startsWith("https")){
                url = url.substring("https://".length());
                url = "http://"+ url.replaceFirst(":[1-9]+[0-9]*/|/",":443/");
            }
        }
        return url;
    }
}
