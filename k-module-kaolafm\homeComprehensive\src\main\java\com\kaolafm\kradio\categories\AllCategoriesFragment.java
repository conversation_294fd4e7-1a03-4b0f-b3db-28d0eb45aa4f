package com.kaolafm.kradio.categories;

import android.content.res.Configuration;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewStub;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.CallSuper;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.kaolafm.kradio.categories.broadcast.BroadcastListFragment;
import com.kaolafm.kradio.categories.broadcast.BroadcastTabListFragment;
import com.kaolafm.kradio.category.ErrorCode;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.widget.NotScrollViewPager;
import com.kaolafm.kradio.component.ui.base.skin.SkinHelper;
import com.kaolafm.kradio.home.comprehensive.gallery.PageJumper;
import com.kaolafm.kradio.home.comprehensive.playerbar.ComprehensivePlayerBar;
import com.kaolafm.kradio.home.comprehensive.ui.view.BaseBackFragment;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.AllCategoriesCustomTitleInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioC211ViewSizeInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioClickRetryInter;
import com.kaolafm.kradio.lib.base.flavor.SkeletonInter;
import com.kaolafm.kradio.lib.base.flavor.UserCenterInter;
import com.kaolafm.kradio.lib.event.PagerJumpEvent;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkManager;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.widget.tab.OnTabSelectListener;
import com.kaolafm.kradio.lib.widget.tab.SlidingTabLayout;
import com.kaolafm.kradio.lib.widget.tab.Tab;
import com.kaolafm.launcher.LauncherActivity;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.util.ReportConstants;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

/**
 * 所有分类页面，包括音乐、电台、收音机等。
 *
 * <AUTHOR>
 * @date 2018/4/16
 */
@Route(path = RouterConstance.CATEGORIES_COMPREHENSIVE_URL)
public class AllCategoriesFragment extends BaseBackFragment<AllCategoriesPresenter> implements IAllCategoriesView {
    private final String TAG = AllCategoriesFragment.class.getSimpleName();
    public static int MAX_REQUEST_NUMBER = 28;

    private View vRootCate;
    private ViewStub mNoNetwork;
    private View mNoNetWorkRl;
    private View loading;
    private boolean isDataReceived;
    private boolean loadDataError;
    private SlidingTabLayout mTabLayout;
    private NotScrollViewPager mViewPager;
    private CategoriesFragmentAdapter mFragmentAdapter;
    private View mContentView;
    private float mFontWidth = 0.3f;//tab选中时的字重
    /**
     * 要显示的一级codeId
     */
    private long mFatherCode = 0;
    /**
     * 要显示的二级codeId
     */
    private long mSonCode = 0;
    private NetWorkListener mNetWorkListener;
    private int tabPosition;

    /**
     * 得到全局分类ui
     *
     * @param fatherCode 要显示的一级codeId
     * @param sonCode    要显示的二级codeId
     * @return AllCategoriesFragment
     */
//    public static AllCategoriesFragment getInstance(long fatherCode, long sonCode) {
//        AllCategoriesFragment allCategoriesFragment = new AllCategoriesFragment();
//        allCategoriesFragment.mFatherCode = fatherCode;
//        allCategoriesFragment.mSonCode = sonCode;
//        return allCategoriesFragment;
//    }
    public void setCode(long fatherCode, long sonCode) {
        mFatherCode = fatherCode;
        mSonCode = sonCode;
    }

    @Override
    protected void addFragmentRootViewPadding(View view) {
        super.addFragmentRootViewPadding(view);
    }

    @Override
    protected void setContentMaginTop() {
        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) root_layout_back.getLayoutParams();
        layoutParams.setMargins(0, ResUtil.getDimen(R.dimen.m42), 0, 0);
        root_layout_back.setLayoutParams(layoutParams);
    }

    @Override
    public void initView(View view) {
        Log.i("cate", "initView: ");
        super.initView(view);
        View titleView = View.inflate(getContext(), R.layout.bbf_title_center_tablayout, null);
        this.addTitleCenterView(titleView);

        mContentView = View.inflate(getContext(), R.layout.fragment_all_categories, null);
        this.addContentView(mContentView);

        mTabLayout = (SlidingTabLayout) titleView.findViewById(R.id.stb_all_category_title_name);
        KRadioC211ViewSizeInter inter = ClazzImplUtil.getInter("KRadioC211ViewSizeImpl");
        if (inter != null && inter.isNeedReset()) {
            mTabLayout.getLayoutParams().height = ResUtil.getDimen(R.dimen.y80);
        }
        // 初始化时设置背景，使用皮肤框架
        mTabLayout.setBackground(ResUtil.getDrawable(R.drawable.tab_base_bg));
        vRootCate = mContentView.findViewById(R.id.vRootCate);
        loading = mContentView.findViewById(R.id.loading);
        mViewPager = mContentView.findViewById(R.id.vp_all_category_content);

        //配合lazyfragment,避免预加载
        /**
         * 在全部页听ai电台下的电台，再听在线广播，两个都高亮显示
         */
//        mViewPager.setOffscreenPageLimit(0);
        mViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int i, float v, int i1) {

            }

            @Override
            public void onPageSelected(int i) {
                mTabLayout.setCurrentTab(i);
            }

            @Override
            public void onPageScrollStateChanged(int i) {

            }
        });
        mTabLayout.setOnTabSelectListener(new OnTabSelectListener() {
            @Override
            public void onTabSelect(int position) {
                mTabLayout.getTab(position).tabView.setBackground(ResUtil.getDrawable(R.drawable.tab_base_indicator));
                mTabLayout.getTab(position).tabView.setFontWidth(mFontWidth);

                mTabLayout.getTab(tabPosition).tabView.setFontWidth(0);
                mTabLayout.getTab(tabPosition).tabView.setBackground(new ColorDrawable());
                tabPosition = position;

                NetworkUtil.isNetworkAvailable(getContext(), true);
                mViewPager.setCurrentItem(position, true);

                ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_All_CLASSIFICATION_LEVEL_NAVIGATION, mTabLayout.getTab(tabPosition).title, getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
            }

            @Override
            public void onTabReselect(int position) {

            }
        });
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        showLoading();
        mPresenter.loadData();
        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
            mNetWorkListener = new NetWorkListener(this);
            NetworkManager.getInstance().addNetworkReadyListener(mNetWorkListener);
        }
    }

    @Override
    protected int getLayoutId_Tow() {
        return 0;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onThemeEvent(UserCenterInter.ThemeChangeEvent themeChangeEvent) {
        String theme = themeChangeEvent == null ? null : themeChangeEvent.getTheme();
        Log.i(TAG, "onThemeEvent() --- theme = " + theme);
        if (mTabLayout == null){
            return;
        }
        if (TextUtils.isEmpty(theme)){
            return;
        }

        // 处理isSameTheme事件：确认当前主题状态
        if (TextUtils.equals(SkinHelper.IS_SAME_THEME, theme)){
            Log.d(TAG, "收到isSameTheme事件，延迟更新TabLayout背景");
            updateTabLayoutDelayed();
            return;
        }

        // 根据皮肤框架的主题事件更新UI
        if (SkinHelper.NIGHT_SKIN.equals(theme) || SkinHelper.DAY_SKIN.equals(theme)) {
            Log.d(TAG, "主题切换为: " + theme + "，延迟更新TabLayout背景");
            updateTabLayoutDelayed();
        }
    }

    /**
     * 延迟更新TabLayout背景，确保在皮肤框架应用后执行
     */
    private void updateTabLayoutDelayed() {
        new Handler().postDelayed(() -> {
            if (mTabLayout != null) {
                mTabLayout.setBackground(ResUtil.getDrawable(R.drawable.tab_base_bg));
                // 添加空值检查，防止 IndexOutOfBoundsException
                Tab selectedTab = mTabLayout.getTab(tabPosition);
                if (selectedTab != null && selectedTab.tabView != null) {
                    selectedTab.tabView.setBackground(ResUtil.getDrawable(R.drawable.tab_base_indicator));
                }
            }
        }, 300);
    }

    @Override
    public void onEnterAnimationEnd(Bundle savedInstanceState) {
        Log.i("cate", "onEnterAnimationEnd:");
        super.onEnterAnimationEnd(savedInstanceState);
//        mPresenter.getData(mCategoryId, mSubcategoryId);
//        mPresenter.loadData();

//        int expand = (int) getResources().getDimension(R.dimen.m60);
//        ViewUtil.expandViewTouchDelegate(vClose, expand, expand, expand, expand);
    }


    @Override
    public String getPageId() {
        return Constants.PAGE_ID_CATEGORY;
    }

    @Override
    protected AllCategoriesPresenter createPresenter() {
        return new AllCategoriesPresenter(this, mFatherCode, mSonCode);
    }

    @Override
    public void showData(String[] titles, List<Fragment> fragments, int showIndex) {
        //下边的方法会导致,从广播进入,切到资讯,然后资讯下边的最后几个子tab的数据不能显示出来
//        for (int i = 0; i < titles.length; i++) {
//            Log.i(TAG, "showData: titles = " + titles[i]);
//        }
        //记录获取到data的状态
        isDataReceived = true;
        hideLoading();
        mFragmentAdapter = new CategoriesFragmentAdapter(getChildFragmentManager(), fragments, titles);
        if (titles != null && titles.length > 0) {
            List<Tab> tabs = new ArrayList<>();
            AllCategoriesCustomTitleInter inter = ClazzImplUtil.getInter("AllCategoriesCustomTitleImpl");

            for (int i = 0; i < titles.length; i++) {
                Tab tab = new Tab();
                if (null != inter) {
                    tab.title = inter.TitleCustom(titles[i]);
                } else {
                    tab.title = titles[i];
                }
                tab.position = i;
                tab.select = i == showIndex;
                tabs.add(tab);
                ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_All_CLASSIFICATION_LEVEL_NAVIGATION, tab.title, getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
            }
            mTabLayout.setTabs(tabs);
        }
        mViewPager.setAdapter(mFragmentAdapter);
        mFragmentAdapter.notifyDataSetChanged();
//        mTabLayout.setViewPager(mViewPager);
        // 一级导航栏切换到落地页
        mTabLayout.setCurrentTab(showIndex);
        mViewPager.setCurrentItem(showIndex);
        //刷新一下tab，不然断网再重连会出现字体高亮不随着位置显示。
        mTabLayout.notifyDataSetChanged();
        // 添加空值检查，防止 IndexOutOfBoundsException
        Tab selectedTab = mTabLayout.getTab(showIndex);
        if (selectedTab != null && selectedTab.tabView != null) {
            selectedTab.tabView.setBackground(ResUtil.getDrawable(R.drawable.tab_base_indicator));
            selectedTab.tabView.setFontWidth(mFontWidth);
        }
        tabPosition = showIndex;
        if (fragments != null) mViewPager.setOffscreenPageLimit(fragments.size());
    }

    @Override
    public void showError(Exception e) {
        Log.i(TAG, "showError: error = " + e.getMessage());
        //todo 网络异常时，记录当前状态
        loadDataError = true;
        hideLoading();
        String str = null;
        if (e instanceof ApiException) {
            switch (((ApiException) e).getCode()) {
                case ErrorCode.NO_NET:
                    //str = ResUtil.getString(R.string.no_net_work_str);
                    // 解决https://app.huoban.com/tables/2100000007530121/items/2300001944559937?userId=1229522问题
                    if (mFragmentAdapter == null || mFragmentAdapter.getCount() == 0) {
                        showNoNetWorkView(ResUtil.getString(R.string.network_nosigin), true);
                    }
                    break;
                case com.kaolafm.opensdk.http.error.ErrorCode.HTTPS_CERTIFICATE_ERROR:
                    showNoNetWorkView(ResUtil.getString(R.string.home_network_certificate_error), true);
                    break;
                case ErrorCode.NO_SUBCATEGORY:
                case ErrorCode.TYPE_ERROR:
                    str = ResUtil.getString(R.string.error_subcategory_is_null);
                    break;
                default:
            }
        }

        if (str != null) {
            ToastUtil.showError(getContext(), str);
        }
    }


    @Override
    public boolean useEventBus() {
        return true;
    }

    @Subscribe
    public void onEvent(PagerJumpEvent event) {
        if (event != null) {
            if (event.page == PagerJumpEvent.PAGE_BROADCAST_LIST && event.bundle != null) {
                int id = event.bundle.getInt("id");
                String name = event.bundle.getString("name");
                boolean isLeafCategory = event.bundle.getBoolean("isLeafCategory");
                Log.d(TAG, "TYPE_ITEM_BROADCAST_CATEGORY onClick id:" + id
                        + ", name:" + name + ", isLeafCategory:" + isLeafCategory);
                if (isLeafCategory) {
                    // 如果此节点是叶子节点，跳转到之前版本的三级页面
                    extraTransaction().start(BroadcastListFragment.newInstance(id, name));
                } else {
                    // 如果此节点不是叶子节点，跳转到四级分类页面
                    extraTransaction().start(BroadcastTabListFragment.newInstance(id, name));
                }
            } else if (event.page == PagerJumpEvent.PAGE_PLAYER_DETAIL){
                handleItemClickPlayingItem();
            }
        }
    }

    /***************************************************************************************************************/
//    private ViewSkeletonScreen mSkeleton;
    @Override
    protected void hideLoading() {
        super.hideLoading();
        ViewUtil.setViewVisibility(vRootCate, View.GONE);
//        if (mSkeleton != null) {
//            mSkeleton.hide();
//        }
        loading.setVisibility(View.GONE);
    }

    @Override
    protected void showLoading() {
        super.showLoading();
        SkeletonInter skeletonInter = ClazzImplUtil.getInter("SkeletonInterImpl");
        if (skeletonInter != null) {
            //延时显示skeleton框架，避免网络好的情况下出现一闪而过的加载页面
            //如果在此时间内数据已经加载出来，就取消显示
            if (skeletonInter.showSkeleton()) {
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        //如果在延迟时间后，数据仍没有显示出来，并且没有其他异常情况，则显示加载框架
                        if (!isDataReceived && !loadDataError) {
                            showSkeleton();
                        }
                    }
                }, skeletonInter.getDelayInterval());
            } else {
                Log.i(TAG, "dispose show skeleton");
            }
        } else {
            showSkeleton();
        }
    }

    private void showSkeleton() {
        loading.setVisibility(View.VISIBLE);
//        ViewUtil.setViewVisibility(vRootCate, View.VISIBLE);
//        if (mSkeleton == null) {
//            //骨架
//            mSkeleton = Skeleton.bind(vRootCate)
//                    .load(R.layout.layout_cate_skeleton)
//                    .shimmer(true)
//                    .show();
//        } else {
//            mSkeleton.show();
//        }
    }

    /***************************************************************************************************************/

    @Override
    @CallSuper
    protected void showAccordingToScreen(int orientation) {
        super.showAccordingToScreen(orientation);
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
//            mTabLayout.setTabPadding(ScreenUtil.px2dp(ResUtil.getDimen(R.dimen.n30)));
        } else {
            mTabLayout.setTabPadding(ScreenUtil.px2dp(ResUtil.getDimen(R.dimen.x20)));
        }
        changeTabTextSize(false);
    }

    /**
     * 修改"所有"页面中的一级标题字体大小
     *
     * @param needChange boolean值
     */
    public void changeTabTextSize(boolean needChange) {
//        if (needChange) {
//            int textSize = ScreenUtil.px2dp(ResUtil.getDimen(R.dimen.title_tab_title_normal_size));
//            int textSizeSelected = ScreenUtil.px2dp(ResUtil.getDimen(R.dimen.title_tab_title_selected_size));
//            mTabLayout.setTextSize(textSize);
//            mTabLayout.setTextSelectedSize(textSizeSelected);
//            Log.i(TAG, "changeTabTextSize:" + textSize + " " + textSizeSelected);
//        }
    }

    public void showNoNetWorkView(String error, boolean clickToRetry) {
        if (mContentView == null) {
            return;
        }
        if (mNoNetwork == null) {
            mNoNetwork = mContentView.findViewById(R.id.all_category_no_network);
            mNoNetWorkRl = mNoNetwork.inflate();
        }
        TextView tvNetworkNosign = mNoNetWorkRl.findViewById(R.id.tv_network_nosign);
        tvNetworkNosign.setText(error);
        // 支持点击重试
        KRadioClickRetryInter mKRadioClickRetryInter = ClazzImplUtil.getInter(
                "KRadioClickRetryInterImpl");
        if (clickToRetry || (mKRadioClickRetryInter != null
                && mKRadioClickRetryInter.canRetry())) {
            ImageView ivNetworkNoSign = mNoNetWorkRl.findViewById(R.id.network_nosigin);
            ivNetworkNoSign.setOnClickListener(v -> {
                hideErrorLayout();
                showLoading();
                new Handler().postDelayed(() -> mPresenter.loadData(), 100);
                String text = tvNetworkNosign.getText().toString();
                ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_NETWORK_RETRY, text, getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
            });
        }

        ViewUtil.setViewVisibility(mNoNetWorkRl, View.VISIBLE);

        String text = tvNetworkNosign.getText().toString();
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_NETWORK_RETRY, text, getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
    }

    private void hideErrorLayout() {
        ViewUtil.setViewVisibility(mNoNetWorkRl, View.GONE);
    }

    public static class NetWorkListener implements NetworkManager.INetworkReady {
        private WeakReference allCategoriesFragmentWeakReference;

        public NetWorkListener(AllCategoriesFragment allCategoriesFragment) {
            allCategoriesFragmentWeakReference = new WeakReference<>(allCategoriesFragment);
        }

        @Override
        public void networkChange(boolean hasNetwork) {
            AllCategoriesFragment accountLoginFragment = (AllCategoriesFragment) allCategoriesFragmentWeakReference.get();
            if (accountLoginFragment == null) {
                return;
            }
            if (!hasNetwork) {
                return;
            }

            if (accountLoginFragment.mPresenter != null) {
                accountLoginFragment.hideErrorLayout();
                accountLoginFragment.showLoading();
                accountLoginFragment.mPresenter.loadData();
            }
            NetworkManager.getInstance().removeNetworkReadyListener(accountLoginFragment.mNetWorkListener);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        isDataReceived = false;
        NetworkManager.getInstance().removeNetworkReadyListener(mNetWorkListener);
    }

    public boolean isReportFragment() {
        return true;
    }

    /**
     * 处理点击正在播放中的 item
     */
    private void handleItemClickPlayingItem(){
        int type = PlayerManager.getInstance().getCustomType();
        boolean isVideoType =
                type == PlayerConstants.RESOURCES_TYPE_VIDEO_AUDIO
                        || type == PlayerConstants.RESOURCES_TYPE_VIDEO_ALBUM
                        || type == PlayerConstants.RESOURCES_TYPE_TEMP_TASK;
        Log.i("handleClickCurPlayItem", "AllCategoriesFragment -> onEvent -> handleItemClickPlayingItem -> type=" + type + ", isVideoType=" + isVideoType);
        if (isVideoType) {
//            navigationToVideoPlayer();
        } else {
            ComprehensivePlayerBar playerBar = null;
            if (getActivity() instanceof LauncherActivity){
                playerBar = ((LauncherActivity) getActivity()).getPlayerBar();
            }
            Log.i("handleClickCurPlayItem", "AllCategoriesFragment -> onEvent -> handleItemClickPlayingItem -> playerBar=" + playerBar);
            PageJumper.getInstance().jumpToPlayerFragment(playerBar);
        }
    }

    @Override
    protected boolean autoSetBackViewMarginLeft() {
        return false; // 禁用自动边距，与设置页面保持一致
    }
}
