<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/car_owner_radio_card_back"
    android:layout_width="@dimen/m340"
    android:layout_height="@dimen/m340"
    android:layout_gravity="center"
    android:background="@drawable/car_owner_radio_center_bg">

    <com.kaolafm.kradio.component.ui.base.view.OvalImageView
        android:id="@+id/radio_back_cover"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@drawable/car_owner_radio_entrance_cover"
        app:circle="true"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/radio_mask"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@drawable/car_owner_radio_entrance_mask"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/radio_shade"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@drawable/car_owner_radio_entrance_shade"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/radio_sub_title"
        android:layout_width="@dimen/m239"
        android:layout_height="@dimen/m36"
        android:layout_marginBottom="@dimen/m52"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:text="6321参与 578.6w阅读量"
        android:textColor="#FFCFD6E6"
        android:textSize="@dimen/m18"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="@dimen/m47"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintBottom_toTopOf="@id/radio_sub_title"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <TextView
            android:id="@+id/radio_tag"
            android:layout_width="@dimen/m60"
            android:layout_height="@dimen/m36"
            android:background="@drawable/car_owner_radio_back_topic_btn_bg"
            android:gravity="center"
            android:maxLines="1"
            android:maxLength="2"
            android:text="话题"
            android:textColor="#FFFFFCFB"
            android:textSize="@dimen/m20" />

        <TextView
            android:id="@+id/radio_title"
            android:layout_width="@dimen/m211"
            android:layout_height="match_parent"
            android:layout_marginLeft="@dimen/m6"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="#我的保养日记"
            android:textColor="#FFFFFCFB"
            android:textSize="@dimen/m32"
            android:textStyle="bold" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>