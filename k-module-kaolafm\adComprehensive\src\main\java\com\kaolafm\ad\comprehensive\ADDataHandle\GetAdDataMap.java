package com.kaolafm.ad.comprehensive.ADDataHandle;

import android.util.Log;

import com.kaolafm.opensdk.api.ad.KradioAdRequest;
import com.kaolafm.opensdk.api.ad.model.AdZoneMapping;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

public class GetAdDataMap {

    static final String TAG = "GetAdDataMap";

    static public final int STATE_NONE = 1;
    static public final int STATE_SUCCESS = 2;
    static public final int STATE_ERROR = 3;
    static public final int STATE_GETADDATA_REQUEST = 4;

    public int mState = STATE_NONE;

    List<ADMapCallback> mADMapCallbackList;
    private volatile static GetAdDataMap INSTANCE;

    public static GetAdDataMap getInstance() {
        if(INSTANCE == null){
            synchronized (GetAdDataMap.class){
                if(INSTANCE == null){
                    INSTANCE = new GetAdDataMap();
                }
            }
        }
        return INSTANCE;
    }

    private GetAdDataMap() {
        mADMapCallbackList = new CopyOnWriteArrayList<>();
    }


    public void getAdData() {
        mState = STATE_GETADDATA_REQUEST;
        KradioAdRequest request = new KradioAdRequest();
        request.getZoneMappings(new HttpCallback<List<AdZoneMapping>>() {
            @Override
            public void onSuccess(List<AdZoneMapping> adZoneMappings) {
                Log.i(TAG, "get ad data onSuccess, mState: " + mState);
                if(mState == STATE_GETADDATA_REQUEST){
                    mState = STATE_SUCCESS;
                    //save ad data
                    SaveADDataMap.getInstance().saveAdDataMappingList(adZoneMappings);
                    updateListenerSuccess();
                }
            }

            @Override
            public void onError(ApiException e) {
                Log.i(TAG, "get ad data error , msg:" + e + ", mState:" + mState);
                if(mState == STATE_GETADDATA_REQUEST) {
                    mState = STATE_ERROR;
                    Log.i(TAG, "get ad data error , msg:" + e);
                    SaveADDataMap.getInstance().clearAll();
                    updateListenerFail();
                }
            }
        });

    }

    private void updateListenerSuccess() {
        for (ADMapCallback adCb : mADMapCallbackList) {
            adCb.updateADMapData();
        }
    }

    private void updateListenerFail() {
        for (ADMapCallback adCb : mADMapCallbackList) {
            adCb.handleFail();
        }
    }

    public void registerCallBack(ADMapCallback adCallback) {
        mADMapCallbackList.add(adCallback);
    }

    public void unregisterCallBack(ADMapCallback adCallback) {
        mADMapCallbackList.remove(adCallback);
    }

    public void clearAllCB(){
        mADMapCallbackList.clear();
    }

    static public int getADSpaceBySceneID(int sId) {
        List<AdZoneMapping> adZoneMappings = new SaveADDataMap().getAdDataMappingList();

        if (adZoneMappings != null) {
            for (AdZoneMapping adMapping : adZoneMappings) {
                if (adMapping.getAdExposureScene() == sId) {
                    int adZoneId = adMapping.getAdZoneId();
                    Log.i(TAG, "getADSpaceBySceneID: adZoneId="+adZoneId);
                    return adZoneId;
                }
            }
        }
        Log.e("GetAdDataMap", "getADSpaceBySceneID: 没有获取到广告位id");
        return -1;
    }

    /**
     * 返回Long类型, 广告id, 对于 null. 请求音频广告 有特殊意义
     *
     * @param sId
     * @return
     */
    static public Long getLongADSpaceBySceneID(int sId) {
        List<AdZoneMapping> adZoneMappings = new SaveADDataMap().getAdDataMappingList();

        if (adZoneMappings != null) {
            for (AdZoneMapping adMapping : adZoneMappings) {
                if (adMapping.getAdExposureScene() == sId) {
                    return (long) adMapping.getAdZoneId();
                }
            }
        }

        return null;
    }

    //for test only
    private void testSave() {
        //test data
        List<AdZoneMapping> testData = new ArrayList<>();
        AdZoneMapping ad1 = new AdZoneMapping();
        ad1.setAdExposureScene(1);
        ad1.setAdZoneId(28);
        testData.add(ad1);
        AdZoneMapping ad2 = new AdZoneMapping();
        ad2.setAdExposureScene(2);
        ad2.setAdZoneId(27);
        testData.add(ad2);
        SaveADDataMap.getInstance().saveAdDataMappingList(testData);
    }

    //for test only
    private void testRead() {
        //test data
        List<AdZoneMapping> testData;
        testData = SaveADDataMap.getInstance().getAdDataMappingList();

        for (AdZoneMapping adMap : testData) {
            Log.i(TAG, "adExposureScene = " + adMap.getAdExposureScene() + "  adZoneId=" + adMap.getAdZoneId());
        }
    }
}
