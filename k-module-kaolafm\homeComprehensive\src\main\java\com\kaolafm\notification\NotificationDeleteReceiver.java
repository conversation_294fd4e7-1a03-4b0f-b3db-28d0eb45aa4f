package com.kaolafm.notification;

import android.app.NotificationManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.kaolafm.kradio.lib.utils.CallTimerCollect;
import com.kaolafm.kradio.lib.utils.CallTimerManager;

/**
 * Created by kaolafm on 2018/5/10.
 */

public class NotificationDeleteReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
        int id = intent.getIntExtra("id", -1);
        Log.i("yls", "deletId:" + id);
        if (id != -1) {
            CallTimerManager callTimerManager = CallTimerCollect.get(id);
            if (callTimerManager == null) {
                return;
            }
            CallTimerCollect.get(id).cancel();
            NotificationManager notificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
            notificationManager.cancel(id);
        }
    }
}
