<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/intesting_cslayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/center_line"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.45" />

    <ImageView
        android:id="@+id/recommend_image_one"
        android:layout_width="@dimen/m155"
        android:layout_height="0dp"
        android:layout_marginRight="@dimen/x10"
        android:layout_marginBottom="@dimen/y20"
        android:src="@drawable/pc_bg_unselect"
        app:layout_constraintBottom_toTopOf="@id/center_line"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintRight_toLeftOf="@id/recommend_image_two" />

    <TextView
        android:id="@+id/recommend_text_one"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size3"
        app:layout_constraintBottom_toBottomOf="@id/recommend_image_one"
        app:layout_constraintLeft_toLeftOf="@id/recommend_image_one"
        app:layout_constraintRight_toRightOf="@id/recommend_image_one"
        app:layout_constraintTop_toTopOf="@id/recommend_image_one" />


    <ImageView
        android:id="@+id/recommend_image_two"
        android:layout_width="@dimen/m175"
        android:layout_height="0dp"
        android:layout_marginRight="@dimen/x20"
        android:layout_marginBottom="@dimen/m30"
        android:src="@drawable/pc_bg_unselect"
        app:layout_constraintBottom_toTopOf="@id/center_line"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintRight_toLeftOf="@id/recommend_image_three" />

    <TextView
        android:id="@+id/recommend_text_two"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size4"
        app:layout_constraintBottom_toBottomOf="@id/recommend_image_two"
        app:layout_constraintLeft_toLeftOf="@id/recommend_image_two"
        app:layout_constraintRight_toRightOf="@id/recommend_image_two"
        app:layout_constraintTop_toTopOf="@id/recommend_image_two" />


    <ImageView
        android:id="@+id/recommend_image_three"
        android:layout_width="@dimen/m155"
        android:layout_height="0dp"
        android:layout_marginLeft="@dimen/x90"
        android:src="@drawable/pc_bg_unselect"
        app:layout_constraintBottom_toTopOf="@id/center_line"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintHorizontal_bias="0.484"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:id="@+id/recommend_text_three"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size4"
        app:layout_constraintBottom_toBottomOf="@id/recommend_image_three"
        app:layout_constraintLeft_toLeftOf="@id/recommend_image_three"
        app:layout_constraintRight_toRightOf="@id/recommend_image_three"
        app:layout_constraintTop_toTopOf="@id/recommend_image_three" />


    <ImageView
        android:id="@+id/recommend_image_four"
        android:layout_width="@dimen/m175"
        android:layout_height="0dp"
        android:layout_marginBottom="@dimen/m25"
        android:src="@drawable/pc_bg_unselect"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintBottom_toTopOf="@id/center_line"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintLeft_toRightOf="@id/recommend_image_three" />

    <TextView
        android:id="@+id/recommend_text_four"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size4"
        app:layout_constraintBottom_toBottomOf="@id/recommend_image_four"
        app:layout_constraintLeft_toLeftOf="@id/recommend_image_four"
        app:layout_constraintRight_toRightOf="@id/recommend_image_four"
        app:layout_constraintTop_toTopOf="@id/recommend_image_four" />

    <ImageView
        android:id="@+id/recommend_image_five"
        android:layout_width="0dp"
        android:layout_height="@dimen/m155"
        android:layout_marginLeft="@dimen/m20"
        android:layout_marginBottom="32dp"
        android:src="@drawable/pc_bg_unselect"
        app:layout_constraintBottom_toTopOf="@id/center_line"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintLeft_toRightOf="@id/recommend_image_four" />

    <TextView
        android:id="@+id/recommend_text_five"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size4"
        app:layout_constraintBottom_toBottomOf="@id/recommend_image_five"
        app:layout_constraintLeft_toLeftOf="@id/recommend_image_five"
        app:layout_constraintRight_toRightOf="@id/recommend_image_five"
        app:layout_constraintTop_toTopOf="@id/recommend_image_five" />

    <!--第二层-->
    <ImageView
        android:id="@+id/recommend_image_six"
        android:layout_width="0dp"
        android:layout_height="@dimen/m155"
        android:layout_marginRight="@dimen/x30"
        android:src="@drawable/pc_bg_unselect"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintRight_toLeftOf="@id/recommend_image_seven"
        app:layout_constraintTop_toBottomOf="@id/center_line" />

    <TextView
        android:id="@+id/recommend_text_six"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size4"
        app:layout_constraintBottom_toBottomOf="@id/recommend_image_six"
        app:layout_constraintLeft_toLeftOf="@id/recommend_image_six"
        app:layout_constraintRight_toRightOf="@id/recommend_image_six"
        app:layout_constraintTop_toTopOf="@id/recommend_image_six" />


    <ImageView
        android:id="@+id/recommend_image_seven"
        android:layout_width="0dp"
        android:layout_height="@dimen/m190"
        android:layout_marginRight="@dimen/x30"
        android:src="@drawable/pc_bg_unselect"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintRight_toLeftOf="@id/recommend_image_eight"
        app:layout_constraintTop_toBottomOf="@id/center_line" />

    <TextView
        android:id="@+id/recommend_text_seven"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size4"
        app:layout_constraintBottom_toBottomOf="@id/recommend_image_seven"
        app:layout_constraintLeft_toLeftOf="@id/recommend_image_seven"
        app:layout_constraintRight_toRightOf="@id/recommend_image_seven"
        app:layout_constraintTop_toTopOf="@id/recommend_image_seven" />

    <ImageView
        android:id="@+id/recommend_image_eight"
        android:layout_width="0dp"
        android:layout_height="@dimen/m155"
        android:layout_marginLeft="@dimen/x30"
        android:layout_marginTop="@dimen/m30"
        android:src="@drawable/pc_bg_unselect"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/center_line" />

    <TextView
        android:id="@+id/recommend_text_eight"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size4"
        app:layout_constraintBottom_toBottomOf="@id/recommend_image_eight"
        app:layout_constraintLeft_toLeftOf="@id/recommend_image_eight"
        app:layout_constraintRight_toRightOf="@id/recommend_image_eight"
        app:layout_constraintTop_toTopOf="@id/recommend_image_eight" />

    <ImageView
        android:id="@+id/recommend_image_nice"
        android:layout_width="0dp"
        android:layout_height="@dimen/m155"
        android:layout_marginLeft="8dp"
        android:layout_marginTop="12dp"
        android:src="@drawable/pc_bg_unselect"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintLeft_toRightOf="@id/recommend_image_eight"
        app:layout_constraintTop_toBottomOf="@id/center_line" />

    <TextView
        android:id="@+id/recommend_text_nice"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size4"
        app:layout_constraintBottom_toBottomOf="@id/recommend_image_nice"
        app:layout_constraintLeft_toLeftOf="@id/recommend_image_nice"
        app:layout_constraintRight_toRightOf="@id/recommend_image_nice"
        app:layout_constraintTop_toTopOf="@id/recommend_image_nice" />

    <ImageView
        android:id="@+id/recommend_image_ten"
        android:layout_width="0dp"
        android:layout_height="@dimen/m190"
        android:layout_marginLeft="@dimen/m20"
        android:src="@drawable/pc_bg_unselect"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintLeft_toRightOf="@id/recommend_image_nice"
        app:layout_constraintTop_toTopOf="@id/center_line" />

    <TextView
        android:id="@+id/recommend_text_ten"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size4"
        app:layout_constraintBottom_toBottomOf="@id/recommend_image_ten"
        app:layout_constraintLeft_toLeftOf="@id/recommend_image_ten"
        app:layout_constraintRight_toRightOf="@id/recommend_image_ten"
        app:layout_constraintTop_toTopOf="@id/recommend_image_ten" />

    <TextView
        android:id="@+id/interstedSubmit"
        android:layout_width="@dimen/x360"
        android:layout_height="@dimen/y70"
        android:layout_marginBottom="@dimen/home_recommend_bottom_margin"
        android:background="@drawable/pc_next_bg"
        android:gravity="center"
        android:text="@string/personlityrecommendation_intest_submit"
        android:textColor="#FFFFFF"
        android:textSize="@dimen/text_size6"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>