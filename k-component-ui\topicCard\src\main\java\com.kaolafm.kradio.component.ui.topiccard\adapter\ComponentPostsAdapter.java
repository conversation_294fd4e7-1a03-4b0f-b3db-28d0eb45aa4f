package com.kaolafm.kradio.component.ui.topiccard.adapter;

import android.graphics.Bitmap;
import android.view.View;
import android.view.ViewGroup;
import android.view.View.OnClickListener;
import android.view.ViewGroup.LayoutParams;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.kradio.component.ui.R.dimen;
import com.kaolafm.kradio.component.ui.R.drawable;
import com.kaolafm.kradio.component.ui.R.id;
import com.kaolafm.kradio.component.ui.R.layout;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.date.TimeUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.lib.utils.imageloader.listener.OnGetBitmapListener;
import com.kaolafm.opensdk.api.topic.model.TopicPosts;

import java.util.Arrays;
import java.util.List;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public final class ComponentPostsAdapter extends BaseAdapter {
    private boolean isBigCard;
    @Nullable
    private ComponentPostsAdapter.OnItemLikeIconClickedListener listener;
    @Nullable
    private ComponentPostsAdapter.OnItemClickedListener itenListener;

    private final String formatNumber(long count, int decimalPlaces) {
        if (count < (long) 10000) {
            return String.valueOf(count);
        } else {
            String var5 = "%." + decimalPlaces + "fw";
            Object[] var6 = new Object[]{(float) count / 10000.0F};
            boolean var7 = false;
            String var10000 = String.format(var5, Arrays.copyOf(var6, var6.length));
            Intrinsics.checkExpressionValueIsNotNull(var10000, "java.lang.String.format(format, *args)");
            return var10000;
        }
    }

    @NotNull
    protected BaseHolder getViewHolder(@Nullable ViewGroup parent, int viewType) {
        View var10000 = this.inflate(parent, layout.component_rv_item_topic_posts, 0);
        Intrinsics.checkExpressionValueIsNotNull(var10000, "inflate(parent, R.layout…t_rv_item_topic_posts, 0)");
        View view = var10000;
        if (!this.isBigCard) {
            LayoutParams var5 = view.getLayoutParams();
            Intrinsics.checkExpressionValueIsNotNull(var5, "view.layoutParams");
            LayoutParams params = var5;
            params.width = ResUtil.getDimen(dimen.m380);
            view.setLayoutParams(params);
        }

        return (BaseHolder) (new ComponentPostsAdapter.RelatedContentViewHolder(view));
    }

    public final boolean isBigCard() {
        return this.isBigCard;
    }

    public final void setBigCard(boolean var1) {
        this.isBigCard = var1;
    }

    @Nullable
    public final ComponentPostsAdapter.OnItemLikeIconClickedListener getListener() {
        return this.listener;
    }

    public final void setListener(@Nullable ComponentPostsAdapter.OnItemLikeIconClickedListener var1) {
        this.listener = var1;
    }

    @Nullable
    public final ComponentPostsAdapter.OnItemClickedListener getItenListener() {
        return this.itenListener;
    }

    public final void setItenListener(@Nullable ComponentPostsAdapter.OnItemClickedListener var1) {
        this.itenListener = var1;
    }

    public ComponentPostsAdapter(@Nullable List dataList, boolean isBigCard, @Nullable ComponentPostsAdapter.OnItemLikeIconClickedListener listener, @Nullable ComponentPostsAdapter.OnItemClickedListener itenListener) {
        super(dataList);
        this.isBigCard = isBigCard;
        this.listener = listener;
        this.itenListener = itenListener;
    }

    public final class RelatedContentViewHolder extends BaseHolder {
        private final ImageView userImage;
        private final TextView userName;
        private final TextView updateTime;
        private final TextView likeCount;
        private final ImageView likeIcon;
        private final TextView postsContent;
        private final View likeParentView;

        public void setupData(@Nullable TopicPosts t, int position) {
            if (t != null) {
                TextView var10000 = this.userName;
                Intrinsics.checkExpressionValueIsNotNull(var10000, "userName");
                var10000.setText((CharSequence) t.getNickname());
                var10000 = this.likeCount;
                Intrinsics.checkExpressionValueIsNotNull(var10000, "likeCount");
                var10000.setText((CharSequence) ComponentPostsAdapter.this.formatNumber(t.getLikeCount(), 1));
                this.likeIcon.setImageResource(t.getLikeStatus() == 1 ? drawable.comprehensive_topic_posts_liked : drawable.comprehensive_topic_posts_unlike);
                var10000 = this.postsContent;
                Intrinsics.checkExpressionValueIsNotNull(var10000, "postsContent");
                var10000.setText((CharSequence) t.getContent());
                var10000 = this.updateTime;
                Intrinsics.checkExpressionValueIsNotNull(var10000, "updateTime");
                TextView var10001 = this.updateTime;
                Intrinsics.checkExpressionValueIsNotNull(var10001, "updateTime");
                var10000.setText((CharSequence) TimeUtil.getTopicPostsTime(var10001.getContext(), t.getPublishTime()));
                ImageLoader var3 = ImageLoader.getInstance();
                ImageView var4 = this.userImage;
                Intrinsics.checkExpressionValueIsNotNull(var4, "userImage");
                var3.getBitmapFromCache(var4.getContext(), t.getAvatar(), (OnGetBitmapListener) (new OnGetBitmapListener() {
                    public final void onBitmap(Bitmap it) {
                        if (it != null && !it.isRecycled()) {
                            RelatedContentViewHolder.this.userImage.setImageBitmap(it);
                        }

                    }
                }));
            }
        }

        // $FF: synthetic method
        // $FF: bridge method
        public void setupData(Object var1, int var2) {
            this.setupData((TopicPosts) var1, var2);
        }

        public RelatedContentViewHolder(@Nullable final View itemView) {
            super(itemView);
            if (itemView == null) {
                Intrinsics.throwNpe();
            }

            this.userImage = (ImageView) itemView.findViewById(id.userIcon);
            this.userName = (TextView) itemView.findViewById(id.userName);
            this.updateTime = (TextView) itemView.findViewById(id.updateTime);
            this.likeCount = (TextView) itemView.findViewById(id.likeCount);
            this.likeIcon = (ImageView) itemView.findViewById(id.likeIcon);
            this.postsContent = (TextView) itemView.findViewById(id.postsContent);
            this.likeParentView = itemView.findViewById(id.likeParentView);
            this.likeParentView.setOnClickListener((OnClickListener) (new OnClickListener() {
                public final void onClick(View it) {
                    ComponentPostsAdapter.OnItemLikeIconClickedListener var10000 = ComponentPostsAdapter.this.getListener();
                    if (var10000 != null) {
                        Object var10001 = ComponentPostsAdapter.this.getItemData(RelatedContentViewHolder.this.getAdapterPosition());
                        Intrinsics.checkExpressionValueIsNotNull(var10001, "getItemData(adapterPosition)");
                        var10000.onItemLikeClicked((TopicPosts) var10001, RelatedContentViewHolder.this.getAdapterPosition());
                    }

                }
            }));
            itemView.setOnClickListener((OnClickListener) (new OnClickListener() {
                public final void onClick(View it) {
                    ComponentPostsAdapter.OnItemClickedListener var10000 = ComponentPostsAdapter.this.getItenListener();
                    if (var10000 != null) {
                        var10000.onItemClicked(itemView, RelatedContentViewHolder.this.getAdapterPosition());
                    }

                }
            }));
        }
    }

    public interface OnItemLikeIconClickedListener {
        void onItemLikeClicked(@NotNull TopicPosts var1, int var2);
    }

    public interface OnItemClickedListener {
        void onItemClicked(@NotNull View var1, int var2);
    }
}
