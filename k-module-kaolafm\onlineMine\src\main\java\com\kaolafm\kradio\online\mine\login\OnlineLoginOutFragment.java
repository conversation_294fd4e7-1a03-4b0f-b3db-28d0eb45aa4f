package com.kaolafm.kradio.online.mine.login;

import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import com.kaolafm.kradio.k_kaolafm.R; 
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.lib.base.ui.online.BaseShowHideFragment;
import com.kaolafm.kradio.lib.base.ui.online.BaseViewPagerFragment;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.online.common.event.OnlineLoginEvent;
import com.kaolafm.kradio.onlineuser.ui.ILoginView;
import com.kaolafm.kradio.onlineuser.ui.LoginPresenter;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonClickReportEvent;

import org.greenrobot.eventbus.EventBus;
 

import static com.kaolafm.report.event.ButtonClickReportEvent.ONLINE_BUTTON_SIGN_OUT;


/**
 * 退出登录
 * 蔡佳彬
 */
public class OnlineLoginOutFragment extends BaseShowHideFragment<LoginPresenter> implements ILoginView {
 
    TextView login_out_ok; 
    TextView login_out_cancel;


    public OnlineLoginOutFragment() {
        // Required empty public constructor
    }


    public static OnlineLoginOutFragment newInstance(String param1, String param2) {
        OnlineLoginOutFragment fragment = new OnlineLoginOutFragment();
        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

    }

    @Override
    protected int getLayoutId() {
        return R.layout.online_fragment_login_out;
    }

    @Override
    protected LoginPresenter createPresenter() {
        return new LoginPresenter(this);
    }

    @Override
    public void initView(View view) {
        login_out_ok=view.findViewById(R.id.login_out_ok);
        login_out_cancel=view.findViewById(R.id.login_out_cancel);
       
        
        login_out_cancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getActivity().finish();
            }
        });
        login_out_ok.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!NetworkUtil.isNetworkAvailable(getActivity(), false)) {
                    ToastUtil.showOnly(getActivity(), R.string.no_net_work_str);
                    return;
                }
                mPresenter.logout();
            }
        });
    }

    @Override
    public void failedToGetCode(ApiException e) {

    }

    @Override
    public void startCountdown() {

    }

    @Override
    public void loginSuccess() {

    }

    @Override
    public void loginError() {

    }

    @Override
    public void logoutSuccess() {
//        EventBus.getDefault().post(new OnlineLoginEvent());
        getActivity().finish();
    }

    @Override
    public void logoutSError() {
        ToastUtil.showOnly(getActivity(), R.string.no_net_work_str);
    }

    @Override
    public void toast(String msg) {

    }
}