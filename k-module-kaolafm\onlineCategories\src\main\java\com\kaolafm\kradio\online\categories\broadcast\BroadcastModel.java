package com.kaolafm.kradio.online.categories.broadcast;

import android.text.TextUtils;

import com.kaolafm.kradio.common.bean.BroadcastRadioDetailData;
import com.kaolafm.kradio.lib.base.mvp.BaseModel;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.operation.OperationRequest;
import com.kaolafm.opensdk.api.operation.model.category.BroadcastCategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.CategoryMember;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.utils.operation.OperationAssister;
import com.trello.rxlifecycle3.LifecycleTransformer;

import java.util.ArrayList;
import java.util.List;


/**
 * Created by kaolafm on 2018/4/25.
 */

public class BroadcastModel extends BaseModel {

    /**
     * 默认每页条数
     */
    private static final int PAGE_SIZE = 20;

    private String mCategoryId;

    private int mNextPageNum = 1;

    private int mHaveNext;

    public BroadcastModel() {
    }

    public boolean haveNext() {
        return mHaveNext == Constants.HAVE_PAGE;
    }

    /**
     * 设置广播类型
     */
    public void setBroadcastCategoryType(int categoryId) {
        this.mCategoryId = String.valueOf(categoryId);
    }

    /**
     * 获取广播电台列表数据
     */
    public void requestBroadcastListData(LifecycleTransformer lifecycleTransformer, HttpCallback<List<BroadcastRadioDetailData>> callback) {
        OperationRequest operationRequest = new OperationRequest().bindLifecycle(lifecycleTransformer);
        operationRequest.getCategoryMemberList(mCategoryId, mNextPageNum, PAGE_SIZE,
                new HttpCallback<BasePageResult<List<CategoryMember>>>() {
                    @Override
                    public void onSuccess(BasePageResult<List<CategoryMember>> basePageResult) {
                        mHaveNext = basePageResult.getHaveNext();
                        mNextPageNum = basePageResult.getNextPage();
                        List<CategoryMember> dataList = basePageResult.getDataList();
                        List<BroadcastRadioDetailData> itemList = categoryMemberToBroadcastItem(dataList);
                        if (callback != null) {
                            callback.onSuccess(itemList);
                        }
                    }

                    @Override
                    public void onError(ApiException e) {
                        if (callback != null) {
                            callback.onError(e);
                        }
                    }
                });
    }

    public void setPageNum(int pageNum) {
        mNextPageNum = pageNum;
    }

    private List<BroadcastRadioDetailData> categoryMemberToBroadcastItem(List<CategoryMember> categoryMemberList) {
        ArrayList<BroadcastRadioDetailData> list = new ArrayList<>();
        if (categoryMemberList != null) {
            String playingId = PlayerManager.getInstance().getCurPlayItem().getRadioId();
            for (int i = 0, size = categoryMemberList.size(); i < size; i++) {
                CategoryMember categoryMember = categoryMemberList.get(i);
                if (categoryMember instanceof BroadcastCategoryMember) {
                    BroadcastCategoryMember broadcastMember = (BroadcastCategoryMember) categoryMember;
                    BroadcastRadioDetailData broadcastRadioDetailData = new BroadcastRadioDetailData();
                    Long broadcastId = broadcastMember.getBroadcastId();
                    broadcastRadioDetailData.setBroadcastId(broadcastId);
                    broadcastRadioDetailData.setName(broadcastMember.getTitle());
                    broadcastRadioDetailData.setPlayTimes(broadcastMember.getPlayTimes());
                    String freq = broadcastMember.getSubtitle();
                    broadcastRadioDetailData.setFreq(!TextUtils.isEmpty(freq) ? freq : "");
                    broadcastRadioDetailData.setIcon(OperationAssister.getImage(categoryMember));
                    broadcastRadioDetailData.setPlaying(TextUtils.equals(playingId, String.valueOf(broadcastId)));
                    list.add(broadcastRadioDetailData);
                }
            }
        }
        return list;
    }

    @Override
    public void destroy() {

    }
}
