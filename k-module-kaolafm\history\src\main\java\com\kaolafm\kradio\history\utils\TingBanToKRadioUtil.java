package com.kaolafm.kradio.history.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.kradio.lib.basedb.manager.HistoryDaoManager;
import com.kaolafm.kradio.lib.bean.HistoryItem;
import com.kaolafm.kradio.lib.report.ReportUtil;
import com.kaolafm.kradio.common.utils.ThreadUtil;
import com.kaolafm.kradio.constant.UserComponentConst;
import com.kaolafm.kradio.history.tingban.HistoryDbManager;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.ComponentUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.KaolaAppConfigData;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.opensdk.api.history.SaveHistoriesRequest;
import com.kaolafm.opensdk.api.history.model.SaveHistoryItem;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> on 2019-08-20.
 * 听伴升级到kradio udid 转换工具.
 */

public class TingBanToKRadioUtil {
    private static final String TINGBAN_PACKAGE_NAME = "com.edog.car";

    private static final String UDID_PREFERENCE_NAME = "udid_information_pf";

    private static final String UDID_VALUE = "udid_value";

    /**
     * 获取缓存的udid
     *
     * @param context
     * @return
     */
    private static String getCacheUdid(Context context) {
        return getSharedPreferences(context).getString(UDID_VALUE, Constants.BLANK_STR);
    }

    private static SharedPreferences getSharedPreferences(Context context) {
        return context.getSharedPreferences(UDID_PREFERENCE_NAME, Context.MODE_PRIVATE);
    }

    public static void init(Context context) {
        if (isTingBanToKRadio(context)) {
            getSharedPreferences(context).edit().clear().apply();
            ReportUtil.tingBanToKRadioUpdate();
        }
    }

    /**
     * 判断是否是 听伴升级到 KRadio
     *
     * @param context
     * @return
     */
    public static boolean isTingBanToKRadio(Context context) {
        String udid = getCacheUdid(context);
        if (!StringUtil.isEmpty(udid) && TINGBAN_PACKAGE_NAME.equals(context.getPackageName())) {
            return true;
        }
        return false;
    }


    /**
     * 从edog数据库拷贝收听历史数据到kradio数据库
     */
    public static void copyHistoryFromTingban(List<HistoryItem> hisHis) {

        ThreadUtil.runOnThread(() -> {
            Log.i("kraido.his", "拷贝听伴历史到kradio:");
            if (hisHis != null && !hisHis.isEmpty()) {
                Log.i("kraido.his", "           :size=" + hisHis.size());
                for (HistoryItem hi : hisHis) {
                    HistoryDaoManager.getInstance().insertSynch(hi);
                }

            } else {
                Log.i("kraido.his", "           :size=0");
            }
        });

    }


    /**
     * 获取听伴播放历史
     *
     * @return
     */
    public static List<HistoryItem> loadHistoryFromTingban() {
        return HistoryDbManager
                .getInstance(AppDelegate.getInstance().getContext()).getHistoryItemList();
    }

    /**
     * 清空听伴历史数据库数据
     */
    public static void clearHistroyOfTingban() {
        Log.i("kraido.his", "清空听伴历史数据库数据:");

        ThreadUtil.runOnThread(() -> {
            // 2019-08-22 清空原数据库数据
            HistoryDbManager.getInstance(AppDelegate.getInstance().getContext()).clearAllPlayHistory();
        });
    }

    /**
     * 上传听伴历史到服务器
     */
    public static void uploadHistoryOfTingbanToSever(List<HistoryItem> hisHis) {
        Log.i("kraido.his", "上传听伴历史到服务器:");

        if (hisHis != null && !hisHis.isEmpty()) {
            Log.i("kraido.his", "                :size=" + hisHis.size());

            List<SaveHistoryItem> shis = new ArrayList<>();

            for (HistoryItem hi : hisHis) {
                SaveHistoryItem item = toSaveHistoryItem(hi);
                if (item != null) {
                    shis.add(item);
                }
            }

            if (!shis.isEmpty()) {

                Log.i("kraido.his", "                :shis.size=" + shis.size());

                if (NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(),false)) {
                    new SaveHistoriesRequest().saveHistories(shis, new HttpCallback<Boolean>() {
                        @Override
                        public void onSuccess(Boolean aBoolean) {
                            Log.i("kraido.his", "                :onSuccess: aBoolean=" + aBoolean);
                        }

                        @Override
                        public void onError(ApiException e) {
                            Log.i("kraido.his", "                :onError: e=" + e);
                        }
                    });
                }
            }
        }
    }

    private static SaveHistoryItem toSaveHistoryItem(HistoryItem hi) {
        SaveHistoryItem rst = null;
        if (hi != null) {
            String appId = KaolaAppConfigData.getInstance().getAppId();
            String userId = ComponentUtil.getResultValue(UserComponentConst.NAME, UserComponentConst.GET_USER_ID);

            try {
                rst = new SaveHistoryItem();
                rst.setAppid(appId);
                if (!TextUtils.isEmpty(userId)) {
                    rst.setKradioUid(userId);
                }
                rst.setAudioId(hi.getAudioId());
                rst.setDuration(hi.getDuration());
                rst.setPlayedTime(hi.getPlayedTime());
                rst.setRadioId(hi.getRadioId());
                rst.setTimeStamp(hi.getTimeStamp());
                rst.setType(Integer.valueOf(hi.getType()));
            } catch (NumberFormatException e) {
                Log.i("kraido.his", "toSaveHistoryItem: e=" + e);
                e.printStackTrace();
                rst = null;
            }
        }

        Log.i("kraido.his", "toSaveHistoryItem: item=" + rst);
        return rst;
    }

}
