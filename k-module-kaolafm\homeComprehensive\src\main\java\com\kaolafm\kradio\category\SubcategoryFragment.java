package com.kaolafm.kradio.category;

import android.content.res.Configuration;
import android.os.Bundle;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.OnScrollListener;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.view.animation.AnimationUtils;
import android.view.animation.GridLayoutAnimationController;
import android.widget.HorizontalScrollView;
import com.flyco.tablayout.CommonTabLayout;
import com.flyco.tablayout.listener.CustomTabEntity;
import com.flyco.tablayout.listener.OnTabSelectListener;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.lib.report.ReportParamUtil;
import com.kaolafm.kradio.lib.report.ReportUtil;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.categories.CategoryConstant;
import com.kaolafm.kradio.categories.HorizontalCategoryItemSpace;
import com.kaolafm.kradio.categories.HorizontalSpanSizeLookup;
import com.kaolafm.kradio.categories.HorizontalSubcategoryAdapter;
import com.kaolafm.kradio.categories.ISubcategoryView;
import com.kaolafm.kradio.lib.bean.SubcategoryItemBean;
import com.kaolafm.kradio.lib.base.ui.BaseLazyFragment;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.RecyclerViewExposeUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.kradio.common.widget.GridRecyclerView;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import java.util.ArrayList;
import java.util.List;

/**
 * AI电台的二级页面
 * <AUTHOR> Yan
 * @date 2019-07-23
 */
public class SubcategoryFragment extends BaseLazyFragment<SubcategoryPresent>
        implements ISubcategoryView, RecyclerViewExposeUtil.OnItemExposeListener {

    private static final String TAG = "SubcategoryFragment";

    CommonTabLayout mCtbSubcategoryTabTitle;
    GridRecyclerView mGrvSubcategoryContent;
    HorizontalScrollView mHsvSubcategoryTab;
    ViewStub mVsLayoutErrorPage;

    private long mCategoryId;

    private GridLayoutAnimationController mGridController;

    private GridLayoutManager mGridLayoutManager;

    private int mLastPosition;

    private long mShowId = -1;

    private HorizontalSpanSizeLookup mSpanSizeLookup;

    private HorizontalSubcategoryAdapter mSubcategoryAdapter;

    private ArrayList<CustomTabEntity> mTabs;

    @Override
    public void initArgs() {
        super.initArgs();
        Bundle arg = getArguments();
        if (arg != null) {
            mCategoryId = arg.getLong(CategoryConstant.CATEGORY_ID);
            mShowId = arg.getLong(CategoryConstant.SUBCATEGORY_ID);
        }
    }

    @Override
    public void initView(View view) {
        mCtbSubcategoryTabTitle=view.findViewById(R.id.ctb_subcategory_tab_title);
        mGrvSubcategoryContent=view.findViewById(R.id.grv_subcategory_content);
        mHsvSubcategoryTab=view.findViewById(R.id.hsv_subcategory_tab);
        mVsLayoutErrorPage=view.findViewById(R.id.vs_layout_error_page);


        mSubcategoryAdapter = new HorizontalSubcategoryAdapter();
        mSubcategoryAdapter.setOnItemClickListener((itemView, viewType, subcategoryItemBean, position) -> {
            if (NetworkUtil.isNetworkAvailable(getContext(), true)) {
                if (mPresenter != null) {
                    mPresenter.onClick(subcategoryItemBean, position);
                }
            }
        });
        mGridLayoutManager = new GridLayoutManager(getContext(), CategoryConstant.GRID_TOTAL_SPAN_COUNT,
                GridLayoutManager.HORIZONTAL, false);
        mSpanSizeLookup = new HorizontalSpanSizeLookup(mSubcategoryAdapter);
        mGridLayoutManager.setSpanSizeLookup(mSpanSizeLookup);
        mGrvSubcategoryContent.setLayoutManager(mGridLayoutManager);
        //加载动画
        mGridController = (GridLayoutAnimationController) AnimationUtils
                .loadLayoutAnimation(getContext(), R.anim.layout_subcategory_item_load);

        mGrvSubcategoryContent.setAdapter(mSubcategoryAdapter);
        RecyclerViewExposeUtil exposeUtil = new RecyclerViewExposeUtil();
        exposeUtil.setRecyclerItemExposeListener(mGrvSubcategoryContent, this);

        //间隔
        mGrvSubcategoryContent.addItemDecoration(new HorizontalCategoryItemSpace());
        mGrvSubcategoryContent.addOnScrollListener(new OnScrollListener() {
            @Override
            public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
            }

            @Override
            public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                int position = mGridLayoutManager.findFirstCompletelyVisibleItemPosition();
                if (position > 0) {
                    SubcategoryItemBean itemData = mSubcategoryAdapter.getItemData(position);
                    String parentCode = itemData.getParentCode();
                    setTabIndex(parentCode);
                }
            }
        });

        mCtbSubcategoryTabTitle.setOnTabSelectListener(new OnTabSelectListener() {
            @Override
            public void onTabSelect(int position) {
                scrollToCurrentPosition(position);
            }

            @Override
            public void onTabReselect(int position) {

            }
        });

        PlayerManager.getInstance().addPlayControlStateCallback(basePlayStateListener);
    }


    private BasePlayStateListener basePlayStateListener = new BasePlayStateListener() {
        @Override
        public void onPlayerPreparing(PlayItem playItem) {
            if (mSubcategoryAdapter != null) {
                mSubcategoryAdapter.setSelected();
            }
        }
    };
    private void scrollToCurrentPosition(int position) {
        if (position < mTabs.size()) {
            TabEntity tabEntity = (TabEntity) mTabs.get(position);
            String code = tabEntity.getCode();
            int targetPosition = mPresenter.getPositionByTabCode(mSubcategoryAdapter.getDataList(), code);
            if (targetPosition >= 0) {
                // CPU优化：使用scrollToPosition替代smoothScrollToPosition
                mGrvSubcategoryContent.scrollToPosition(targetPosition);

            }
        }
    }

    private void setTabIndex(String parentCode) {
        if (!TextUtils.isEmpty(parentCode) && !ListUtil.isEmpty(mTabs)) {
            for (int i = 0, size = mTabs.size(); i < size; i++) {
                String code = ((TabEntity) mTabs.get(i)).getCode();
                if (TextUtils.equals(code, parentCode)) {
                    if (mCtbSubcategoryTabTitle != null) {
                        mCtbSubcategoryTabTitle.setCurrentTab(i);
                    }
                    scrollToCurrentTab(i);
                    return;
                }

            }
        }
    }

    private void scrollToCurrentTab(int position) {
        try {
            View tabView = ((ViewGroup) mCtbSubcategoryTabTitle.getChildAt(0)).getChildAt(position);
            int width1 = mCtbSubcategoryTabTitle.getWidth();
            mLastPosition = position;
            mHsvSubcategoryTab.scrollTo((int) tabView.getX(), 0);
        } catch (NullPointerException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void showError(ApiException e) {
        String str = null;
        if (e instanceof ApiException) {
            switch (((ApiException) e).getCode()) {
                case ErrorCode.NO_NET:
                    isLoaded = false;
                    str = ResUtil.getString(R.string.no_net_work_str);
                    break;
                case ErrorCode.NO_SUBCATEGORY:
                case ErrorCode.TYPE_ERROR:
                    str = ResUtil.getString(R.string.error_subcategory_is_null);
                    break;
                default:
            }
        }
        if (str != null) {
            ToastUtil.showError(getContext(), str);
        }
    }

    @Override
    public void showSubcontent(List<SubcategoryItemBean> itemBeans) {
        mSubcategoryAdapter.setDataList(itemBeans);
        mSubcategoryAdapter.setSelected();
        if (mShowId > 0) {
            setTabIndex(String.valueOf(mShowId));
        }
    }

    @Override
    public void showSubtitles(ArrayList<CustomTabEntity> subtabs) {
        mTabs = subtabs;
        mCtbSubcategoryTabTitle.setTabData(subtabs);
    }

    @Override
    public void toast(int stringId) {
        ToastUtil.showError(getContext(), stringId);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.fragment_subcategory;
    }

    @Override
    protected int getLayoutId_Tow() {
        return 0;
    }

    @Override
    protected SubcategoryPresent createPresenter() {
        return new SubcategoryPresent(this, mCategoryId, mShowId);
    }

    @Override
    protected void changeViewLayoutForStatusBar(View view) {
    }

    @Override
    protected void lazyLoad() {
        if (mPresenter != null) {
            mPresenter.start();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mGrvSubcategoryContent != null) {
            mGrvSubcategoryContent.clearOnScrollListeners();
        }
        PlayerManager.getInstance().removePlayControlStateCallback(basePlayStateListener);
    }

    @Override
    protected void showAccordingToScreen(int orientation) {
        super.showAccordingToScreen(orientation);
        int firstVisibleItemPosition = mGridLayoutManager.findFirstVisibleItemPosition();
        mSpanSizeLookup.setSpanCount(ResUtil.getInt(R.integer.subcategory_item_span_count));
        int left, top, right, bottom;
        //横屏
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            mGridLayoutManager.setOrientation(GridLayoutManager.HORIZONTAL);
            //竖屏
        } else {
            mGridLayoutManager.setOrientation(GridLayoutManager.VERTICAL);
        }
        left = ResUtil.getDimen(R.dimen.subcategory_content_left_padding);
        right = ResUtil.getDimen(R.dimen.subcategory_content_right_duration);
        top = ResUtil.getDimen(R.dimen.subcategory_content_top_duration);
        bottom = ResUtil.getDimen(R.dimen.subcategory_content_bottom_duration);

        //自动滚动到上一个位置。
        mGridLayoutManager.scrollToPositionWithOffset(firstVisibleItemPosition, -1);
        mGrvSubcategoryContent.setPadding(left, top, right, bottom);

        mSubcategoryAdapter.notifyDataSetChanged();
        int paddingLeft = (int) (ResUtil.getDimen(R.dimen.subcategory_tab_padding_left) - mCtbSubcategoryTabTitle.getTabPadding());
        mCtbSubcategoryTabTitle.setPadding(paddingLeft, 0, 0, 0);

        //fixme 字体适配(字号错误，待修正，修改AI电台的二级标题)
        int textSize = ScreenUtil.px2dp(ResUtil.getDimen(R.dimen.subtitle_tab_title_size));
        Log.i(TAG,"textsize:"+textSize);
//        mCtbSubcategoryTabTitle.setTextsize(textSize);
    }

    @Override
    public void onItemViewVisible(boolean visible, int position) {
        if (visible && mSubcategoryAdapter != null) {
            SubcategoryItemBean bean = mSubcategoryAdapter.getItemData(position);
            //过滤掉title，否则会把title也算进去
            if (bean.getCoverUrl() != null) {
                ReportUtil.addContentShowEvent("", ReportParamUtil.getRadioType(bean), "",
                        String.valueOf(bean.getId()), "",
                        Constants.PAGE_ID_CATEGORY, bean.getParentCode(), "");
            }
        }
    }
}
