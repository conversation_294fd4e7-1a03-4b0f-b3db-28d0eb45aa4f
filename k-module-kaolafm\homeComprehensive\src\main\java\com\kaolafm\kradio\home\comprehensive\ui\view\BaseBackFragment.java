package com.kaolafm.kradio.home.comprehensive.ui.view;

import android.os.Bundle;
import androidx.annotation.CallSuper;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.mvp.IPresenter;
import com.kaolafm.kradio.lib.base.ui.BaseShowHideFragment;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;


/**
 * <p>不可重写getLayoutId,将顶部箭头所在布局从原布局中删去,保留下边内容的布局</p>
 * <p>
 * <p>如果箭头布局包含标题,使用bbf_title_center_textview,并调用addTitleCenterView()添加</p>
 * <p>
 * <p>如果箭头布局包含Tab,使用bbf_title_center_tablayout,并调用addTitleCenterView()添加</p>
 * <p>
 * <p>通过addContentView(),添加内容布局</p>
 * <p>
 * <p>在initView中做view的初始化</p>
 * <p>
 * <p>目前不支持ButterKnife</p>
 * <p>
 * <p>示例:</p>
 *
 * <p>super.initView(view);
 *
 * <p>View titleView = View.inflate(getContext(), R.layout.bbf_title_center_tablayout, null);
 * <p>
 * <p>this.addTitleCenterView(titleView);
 * <p>
 * <p>mContentView = View.inflate(getContext(), R.layout.fragment_all_categories, null);
 * <p>this.addContentView(mContentView);
 * <p>
 * <p>mTabLayout = (SlidingTabLayout) titleView.findViewById(R.id.stb_all_category_title_name);
 * <p>vRootCate = mContentView.findViewById(R.id.vRootCate);
 * <p>mViewPager = mContentView.findViewById(R.id.vp_all_category_content);
 *
 * <AUTHOR>
 **/
public abstract class BaseBackFragment<P extends IPresenter> extends BaseShowHideFragment<P> {

    ViewGroup bbfTitleLayout;
    View bbfBack;
    LinearLayout bbfCenter;
    FrameLayout bbfRight;
    TextView text_line;
    protected FrameLayout bbfContent;
    protected LinearLayout root_layout_back;

    @Override
    protected int getLayoutId() {
        return R.layout.fragment_base_back;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        setContentMaginTop();
    }

    @Override
    @CallSuper
    public void initView(View view) {
        bbfTitleLayout=view.findViewById(R.id.bbf_title);
        bbfBack=view.findViewById(R.id.bbf_back);
        bbfCenter=view.findViewById(R.id.bbf_center);
        bbfRight=view.findViewById(R.id.bbf_right);
        text_line=view.findViewById(R.id.text_line);
        bbfContent=view.findViewById(R.id.bbf_content);
        root_layout_back=view.findViewById(R.id.root_layout_back);

        bbfBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                pop();
            }
        });
    }

    protected void addTitleCenterView(View view) {
        bbfCenter.addView(view);
    }

    protected void addTitleRightView(View view) {
        bbfRight.addView(view);
    }

    protected void addContentView(View view) {
        bbfContent.addView(view);
    }

    protected void setTextlineVisible() {
        text_line.setVisibility(View.VISIBLE);
    }

    protected void setContentMaginTop() {
        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) root_layout_back.getLayoutParams();
        layoutParams.setMargins(0, ResUtil.getDimen(R.dimen.m50), 0, 0);
        root_layout_back.setLayoutParams(layoutParams);
    }

    @Override
    @CallSuper
    protected void showAccordingToScreen(int orientation) {
        if (autoSetBackViewMarginLeft()) {
            ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) bbfBack.getLayoutParams();
            layoutParams.leftMargin = ScreenUtil.getGlobalBackMarginLeft(bbfBack, orientation);
        }
    }

    protected boolean autoSetBackViewMarginLeft() {
        return true;
    }


}
