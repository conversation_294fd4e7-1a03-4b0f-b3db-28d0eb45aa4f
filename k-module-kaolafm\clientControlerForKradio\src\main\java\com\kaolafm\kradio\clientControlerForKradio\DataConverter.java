package com.kaolafm.kradio.clientControlerForKradio;

import static com.kaolafm.kradio.lib.utils.UrlUtil.getLocalPicUri;

import android.text.TextUtils;

import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.lib.bean.Host;
import com.kaolafm.kradio.lib.common.ResType;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.api.operation.model.column.ActivityDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.AlbumDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.AudioDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.BroadcastDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.CategoryColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.ColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.FeatureDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.InfoFragmentDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.LiveProgramDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.PageRedirectionColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.RadioDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.RadioQQMusicDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.SearchResultColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.TVDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.TopicDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.VideoAlbumDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.VideoDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.WebViewColumnMember;
import com.kaolafm.opensdk.api.search.model.Compere;
import com.kaolafm.opensdk.api.search.model.SearchProgramBean;
import com.kaolafm.opensdk.api.search.model.VoiceSearchProgramBean;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.sdk.core.Music;
import com.kaolafm.sdk.core.SearchData;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by v on 2018/3/22.
 */

public class DataConverter {

    /**
     * 专辑
     */
    public final static int RESOURCES_TYPE_ALBUM = 0;
    /**
     * 碎片
     */
    public final static int RESOURCES_TYPE_AUDIO = 1;
    /**
     * 电台
     */
    public final static int RESOURCES_TYPE_PGC = 3;
    /**
     * 传统广播
     */
    public final static int RESOURCES_TYPE_BROADCAST = 11;

    private static String trimHtml(String text) {
        if (!TextUtils.isEmpty(text)) {
            text = text.replace("<em>", "").replace("</em>", "");
        }
        return text;
    }

    private static String getHostStr(ArrayList<Host> hostList) {
        String hosts = "佚名";
        if (hostList != null && !hostList.isEmpty()) {
            StringBuilder sb = new StringBuilder();
            for (Host item : hostList) {
                sb.append(item.getName()).append(",");
            }
            hosts = sb.toString();
            while (hosts.endsWith(",") || hosts.endsWith(" ")) {
                hosts = hosts.substring(0, hosts.length() - 1);
            }
            if (TextUtils.isEmpty(hosts)) {
                hosts = "佚名";
            }
        }
        return hosts;
    }

    /***************************************************************************************************************/

    public static ClientPlayer.PlayParam toPlayParam(Music music) {
        long id;
        int type;

        ClientPlayer.PlayParam playParam = new ClientPlayer.PlayParam();
        switch (music.type) {
            case RESOURCES_TYPE_ALBUM:
                id = music.albumId;
                type = ResType.ALBUM_TYPE;
                break;
            case RESOURCES_TYPE_PGC:
                id = music.albumId;
                type = ResType.RADIO_TYPE;
                break;
            case RESOURCES_TYPE_BROADCAST:
                id = music.albumId;
                type = ResType.BROADCAST_TYPE;
                break;
            default:
                id = music.audioId;
                type = ResType.AUDIO_TYPE;
                break;
        }


        playParam.id = id;
        playParam.resType = type;
        playParam.title = music.albumName;
        playParam.img = music.picUrl;
        return playParam;

    }

    public static ClientPlayer.PlayParam toPlayParam(SearchData searchData) {
        long id = searchData.getId();
        int type;

        ClientPlayer.PlayParam playParam = new ClientPlayer.PlayParam();
        switch (searchData.getType()) {
            case RESOURCES_TYPE_ALBUM:
                type = ResType.ALBUM_TYPE;
                break;
            case RESOURCES_TYPE_PGC:
                type = ResType.RADIO_TYPE;
                break;
            case RESOURCES_TYPE_BROADCAST:
                type = ResType.BROADCAST_TYPE;
                break;
            default:
                type = ResType.AUDIO_TYPE;
                break;
        }


        playParam.id = id;
        playParam.resType = type;
        playParam.title = searchData.getName();
        playParam.img = searchData.getImg();
        return playParam;
    }

    public static Music toMusic(VoiceSearchProgramBean dlb) {

        //Log.i("novelot", String.format("[%s,%s,%d]", dlb.getAlbumName(), dlb.getName(), dlb.getId()));
        if (dlb == null) {
            return null;
        }
        Music music = new Music();
        music.albumName = dlb.getAlbumName();

        music.audioName = dlb.getName();
        music.playUrl = dlb.getPlayUrl();
        music.picUrl = dlb.getImg();
        music.authorName = getHostStr(dlb.getComperes());
        music.duration = dlb.getDuration();
        //music.localPicUri = getLocalPicUri(music.picUrl);
        music.type = dlb.getType();
        music.searchCallBack = dlb.getCallback();


        switch (music.type) {
            case RESOURCES_TYPE_ALBUM:
            case RESOURCES_TYPE_PGC:
            case RESOURCES_TYPE_BROADCAST:
                music.albumId = dlb.getId();
                break;
            default:
                music.audioId = dlb.getId();
                break;
        }

        return music;
    }

    private static String getHostStr(List<Compere> hostList) {
        String hosts = "佚名";
        if (hostList != null && !hostList.isEmpty()) {
            StringBuilder sb = new StringBuilder();
            for (Compere item : hostList) {
                sb.append(item.getName()).append(",");
            }
            hosts = sb.toString();
            while (hosts.endsWith(",") || hosts.endsWith(" ")) {
                hosts = hosts.substring(0, hosts.length() - 1);
            }
            if (TextUtils.isEmpty(hosts)) {
                hosts = "佚名";
            }
        }
        return hosts;
    }

    public static SearchData toSearchData(SearchProgramBean voiceSearchData) {
        SearchData searchData = null;
        if (voiceSearchData != null) {
            searchData = new SearchData();
            searchData.setId(voiceSearchData.getId());
            searchData.setName(trimHtml(voiceSearchData.getName()));
            searchData.setType(voiceSearchData.getType());
            searchData.setImg(voiceSearchData.getImg());
            searchData.setHost(getHostStr(voiceSearchData.getComperes()));
        }
        return searchData;
    }
    public static SearchData cellToSearchData(HomeCell homecell) {
        SearchData searchData = null;
        if (homecell != null) {
            for (ColumnMember member : homecell.contentList){
                searchData = new SearchData();
                searchData.setId(homecell.getPlayId());
                if (member instanceof ActivityDetailColumnMember){
                    ActivityDetailColumnMember tmpMember = (ActivityDetailColumnMember)member;
                    searchData.setId(Long.valueOf(tmpMember.getId()));
                    searchData.setName(trimHtml(tmpMember.getTitle()));
                    searchData.setType(tmpMember.getResType());
                }else if (member instanceof AlbumDetailColumnMember){
                    AlbumDetailColumnMember tmpMember = (AlbumDetailColumnMember)member;
                    searchData.setId(Long.valueOf(tmpMember.getId()));
                    searchData.setName(trimHtml(tmpMember.getTitle()));
                    searchData.setType(tmpMember.getResType());
                }else if (member instanceof AudioDetailColumnMember){
                    AudioDetailColumnMember tmpMember = (AudioDetailColumnMember)member;
                    searchData.setId(Long.valueOf(tmpMember.getId()));
                    searchData.setName(trimHtml(tmpMember.getTitle()));
                    searchData.setType(tmpMember.getResType());
                }else if (member instanceof BroadcastDetailColumnMember   ){
                    BroadcastDetailColumnMember tmpMember = (BroadcastDetailColumnMember)member;
                    searchData.setId(Long.valueOf(tmpMember.getId()));
                    searchData.setName(trimHtml(tmpMember.getTitle()));
                    searchData.setType(tmpMember.getResType());
                }else if (member instanceof CategoryColumnMember){
                    CategoryColumnMember tmpMember = (CategoryColumnMember)member;
                    searchData.setId(Long.valueOf(tmpMember.getId()));
                    searchData.setName(trimHtml(tmpMember.getTitle()));
                    searchData.setType(tmpMember.getResType());
                }else if (member instanceof FeatureDetailColumnMember){
                    FeatureDetailColumnMember tmpMember = (FeatureDetailColumnMember)member;
                    searchData.setId(Long.valueOf(tmpMember.getId()));
                    searchData.setName(trimHtml(tmpMember.getTitle()));
                    searchData.setType(tmpMember.getResType());
                }else if (member instanceof InfoFragmentDetailColumnMember){
                    InfoFragmentDetailColumnMember tmpMember = (InfoFragmentDetailColumnMember)member;
                    searchData.setId(Long.valueOf(tmpMember.getId()));
                    searchData.setName(trimHtml(tmpMember.getTitle()));
                    searchData.setType(tmpMember.getResType());
                }else if (member instanceof LiveProgramDetailColumnMember){
                    LiveProgramDetailColumnMember tmpMember = (LiveProgramDetailColumnMember)member;
                    searchData.setId(Long.valueOf(tmpMember.getId()));
                    searchData.setName(trimHtml(tmpMember.getTitle()));
                    searchData.setType(tmpMember.getResType());
                }else if (member instanceof PageRedirectionColumnMember){
                    PageRedirectionColumnMember tmpMember = (PageRedirectionColumnMember)member;
                    searchData.setId(Long.valueOf(tmpMember.getId()));
                    searchData.setName(trimHtml(tmpMember.getTitle()));
                    searchData.setType(tmpMember.getResType());
                }else if (member instanceof RadioDetailColumnMember){
                    RadioDetailColumnMember tmpMember = (RadioDetailColumnMember)member;
                    searchData.setId(Long.valueOf(tmpMember.getId()));
                    searchData.setName(trimHtml(tmpMember.getTitle()));
                    searchData.setType(tmpMember.getResType());
                }else if (member instanceof RadioQQMusicDetailColumnMember){
                    RadioDetailColumnMember tmpMember = (RadioDetailColumnMember)member;
                    searchData.setId(Long.valueOf(tmpMember.getId()));
                    searchData.setName(trimHtml(tmpMember.getTitle()));
                    searchData.setType(tmpMember.getResType());
                }else if (member instanceof SearchResultColumnMember){
                    SearchResultColumnMember tmpMember = (SearchResultColumnMember)member;
                    searchData.setId(Long.valueOf(tmpMember.getId()));
                    searchData.setName(trimHtml(tmpMember.getTitle()));
                    searchData.setType(tmpMember.getResType());
                }else if (member instanceof TopicDetailColumnMember){
                    TopicDetailColumnMember tmpMember = (TopicDetailColumnMember)member;
                    searchData.setId(Long.valueOf(tmpMember.getId()));
                    searchData.setName(trimHtml(tmpMember.getTitle()));
                    searchData.setType(tmpMember.getResType());
                }else if (member instanceof TVDetailColumnMember){
                    TVDetailColumnMember tmpMember = (TVDetailColumnMember)member;
                    searchData.setId(Long.valueOf(tmpMember.getId()));
                    searchData.setName(trimHtml(tmpMember.getTitle()));
                    searchData.setType(tmpMember.getResType());
                }else if (member instanceof VideoAlbumDetailColumnMember){
                    VideoAlbumDetailColumnMember tmpMember = (VideoAlbumDetailColumnMember)member;
                    searchData.setId(Long.valueOf(tmpMember.getId()));
                    searchData.setName(trimHtml(tmpMember.getTitle()));
                    searchData.setType(tmpMember.getResType());
                }else if (member instanceof VideoDetailColumnMember){
                    VideoDetailColumnMember tmpMember = (VideoDetailColumnMember)member;
                    searchData.setId(Long.valueOf(tmpMember.getId()));
                    searchData.setName(trimHtml(tmpMember.getTitle()));
                    searchData.setType(tmpMember.getResType());
                }else if (member instanceof WebViewColumnMember) {
                    WebViewColumnMember tmpMember = (WebViewColumnMember)member;
                    searchData.setId(Long.valueOf(tmpMember.getId()));
                    searchData.setName(trimHtml(tmpMember.getTitle()));
                    searchData.setType(tmpMember.getResType());
                }
            }
        }
        return searchData;
    }

    public static ClientPlayer.PlayParam toPlayParam(VoiceSearchProgramBean dlb) {
        long id = dlb.getId();
        int type;

        ClientPlayer.PlayParam playParam = new ClientPlayer.PlayParam();

        switch (dlb.getType()) {
            case RESOURCES_TYPE_ALBUM:
                type = ResType.ALBUM_TYPE;
                break;
            case RESOURCES_TYPE_PGC:
                type = ResType.RADIO_TYPE;
                break;
            case RESOURCES_TYPE_BROADCAST:
                type = ResType.BROADCAST_TYPE;
                playParam.fm = "";
                break;
            default:
                type = ResType.AUDIO_TYPE;
                break;
        }


        playParam.id = id;
        playParam.resType = type;
        playParam.title = dlb.getAlbumName();
        playParam.img = dlb.getImg();

        return playParam;
    }

    public static Music toMusic(String type, PlayItem playItem) {
        if (playItem == null) {
            return null;
        }
        Music music = new Music();
        music.albumName = playItem.getAlbumTitle();
        music.isLivingUrl = playItem.isLiving();
        music.audioName =playItem.getTitle();
        music.playUrl = playItem.getPlayUrl();
        music.picUrl = PlayerManagerHelper.getInstance().getPlayItemPicUrl(playItem);
        music.authorName = playItem.getHost();
        music.duration = playItem.getDuration();
        music.progress = 0;
        music.localPicUri = getLocalPicUri(music.picUrl);
        music.progress = 0;
        // TODO: 2018/12/18 不能确定
        try {
            music.type = Integer.valueOf(type);
        } catch (NumberFormatException e) {
            music.type = -1;
        }


        switch (music.type) {
            case RESOURCES_TYPE_ALBUM:
            case RESOURCES_TYPE_PGC:
            case RESOURCES_TYPE_BROADCAST:
                music.albumId = Long.parseLong(playItem.getRadioId());
                music.albumName = playItem.getAlbumTitle();
                break;
            default:
                music.audioId = playItem.getAudioId();
                break;
        }

        return music;
    }

}
