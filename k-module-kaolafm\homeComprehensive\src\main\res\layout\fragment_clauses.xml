<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#0A0F29"
    android:orientation="vertical">

    <include layout="@layout/layout_title" />

    <TextView
        android:id="@+id/tv_clauses_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="vertical"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size3"
        android:lineSpacingExtra="@dimen/y11"
        android:paddingStart="@dimen/x40"
        android:paddingEnd="@dimen/x40"
        android:visibility="gone"
        />
    <WebView
        android:id="@+id/wv_clauses_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorTransparent"
        android:layerType="software"
        android:layout_marginStart="@dimen/x80"
        android:layout_marginEnd="@dimen/x80"
        />

</LinearLayout>
