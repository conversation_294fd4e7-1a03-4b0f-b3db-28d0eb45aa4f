<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_dialog">


    <TextView
        android:id="@+id/changan_title_z08"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/m62"
        android:text="@string/changan_title_z08"
        android:textColor="@color/colorWhite"
        android:textSize="@dimen/text_size3"
        android:gravity="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>


    <TextView
        android:id="@+id/changan_content_z08"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingLeft="@dimen/x90"
        android:paddingRight="@dimen/x90"
        android:gravity="center"
        android:text="@string/changan_content_z08"
        android:textColor="@color/colorWhite"
        android:textSize="@dimen/text_size3"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>


    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="1"/>

    <TextView
        android:id="@+id/changan_know_z08"
        android:layout_width="@dimen/x110"
        android:layout_height="@dimen/y48"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="@dimen/y62"
        android:background="@drawable/globle_round_bg"
        android:gravity="center"
        android:text="@string/changan_know"
        android:textColor="@color/colorWhite"
        android:textSize="@dimen/text_size3"
        app:layout_constraintBottom_toTopOf="@+id/guideline1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>


</androidx.constraintlayout.widget.ConstraintLayout>