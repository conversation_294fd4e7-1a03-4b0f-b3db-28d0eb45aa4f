<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/liveBarrageRootView"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <ImageView
        android:id="@+id/live_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"/>

    <View
        android:layout_width="0dp"
        android:layout_height="@dimen/y170"
        android:background="@drawable/comprehensive_shadow_top_bg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <View
        android:layout_width="0dp"
        android:layout_height="@dimen/y170"
        android:rotation="180"
        android:background="@drawable/comprehensive_shadow_top_bg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <ImageView
        android:theme="@style/FragmentBackButton"
        android:id="@+id/back"
        android:layout_width="@dimen/m48"
        android:layout_height="@dimen/m48"
        android:layout_marginStart="@dimen/x64"
        android:layout_marginTop="@dimen/y47"
        android:background="@color/transparent"
        android:clickable="true"
        android:focusable="true"
        android:padding="@dimen/m6"
        android:scaleType="centerInside"
        android:src="@drawable/live_ic_back"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!--  直播标题展示  -->
    <com.kaolafm.kradio.component.ui.base.view.KradioTextView
        android:id="@+id/live_title"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="@dimen/y43"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/comprehensive_live_title_height"
        android:layout_marginStart="@dimen/m142"
        android:gravity="center"
        android:text=""
        android:textColor="@color/comprehensive_live_title_color"
        android:textSize="@dimen/m30" />

    <!--  直播主播展示  -->
    <com.kaolafm.kradio.component.ui.base.view.KradioTextView
        android:id="@+id/live_anchor"
        app:layout_constraintTop_toBottomOf="@id/live_title"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/comprehensive_live_theme_height"
        android:layout_marginStart="@dimen/m142"
        android:gravity="center"
        android:text=""
        android:textColor="@color/comprehensive_live_anchor_color"
        android:textSize="@dimen/m24"/>

    <!--  直播主题展示  -->
    <LinearLayout
        android:visibility="gone"
        android:id="@+id/live_theme"
        app:layout_constraintTop_toBottomOf="@id/live_title"
        app:layout_constraintLeft_toRightOf="@id/live_anchor"
        android:layout_marginLeft="@dimen/x24"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/comprehensive_live_theme_height"
        android:paddingHorizontal="@dimen/x20"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:background="@drawable/comprehensive_live_theme_bg">

        <ImageView
            android:layout_width="@dimen/comprehensive_live_theme_icon_width"
            android:layout_height="@dimen/comprehensive_live_theme_icon_width"
            android:scaleType="fitXY"
            android:src="@drawable/comprehensive_live_theme_ic"/>
        <com.kaolafm.kradio.component.ui.base.view.KradioTextView
           android:layout_marginLeft="@dimen/m8"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="公告"
            android:id="@+id/noticeTip"
            android:textColor="@color/comprehensive_live_theme_color"
            android:textSize="@dimen/m24" />

    </LinearLayout>


    <!--  在线人数和排行榜展示  -->
    <LinearLayout
        android:id="@+id/live_cu_rank"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="@dimen/y43"
        android:layout_marginRight="@dimen/x64"
        android:paddingLeft="@dimen/x24"
        android:paddingRight="@dimen/x26"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/comprehensive_live_title_height"
        android:orientation="horizontal"
        android:gravity="center"
        android:background="@drawable/comprehensive_live_cu_rank_bg">

        <!--  在线人数展示  -->
        <com.kaolafm.kradio.component.ui.base.view.KradioTextView
            android:id="@+id/live_cu"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="@color/comprehensive_live_title_color"
            android:textSize="@dimen/m24" />

        <View
            android:visibility="gone"
            android:id="@+id/live_cu_rank_divider"
            android:layout_marginHorizontal="@dimen/x20"
            android:layout_width="@dimen/x1"
            android:layout_height="@dimen/y28"
            android:background="@color/comprehensive_live_cu_rank_divider_color"/>

        <include layout="@layout/comprehensive_live_rank" />
    </LinearLayout>

    <!--  底部功能区域  -->
    <LinearLayout
        android:id="@+id/live_bottom"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <include layout="@layout/comprehensive_live_bottom" />

    </LinearLayout>

    <!--  礼物消息区域  -->
    <LinearLayout
        android:id="@+id/live_gift_parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="@dimen/y145"
        android:layout_width="@dimen/x491"
        android:layout_height="wrap_content"
        android:orientation="vertical">
    </LinearLayout>


    <com.kaolafm.kradio.common.widget.KLRecyclerView
        android:id="@+id/recyclerView"
        app:layout_constraintTop_toBottomOf="@id/live_anchor"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toTopOf="@id/live_bottom"
        android:layout_width="@dimen/comprehensive_live_message_width_1920_720"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/y23"
        android:layout_marginLeft="@dimen/x70"
        android:layout_marginBottom="@dimen/y28"
        android:fadingEdgeLength="@dimen/y72"
        android:requiresFadingEdge="vertical"
        android:visibility="gone" />

    <androidx.constraintlayout.widget.ConstraintLayout
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toTopOf="@id/live_bottom"
        android:layout_marginLeft="@dimen/x70"
        android:layout_marginBottom="@dimen/y28"
        android:layout_width="@dimen/comprehensive_live_message_width_1920_720"
        android:layout_height="wrap_content">

        <include layout="@layout/comprehensive_live_message_item_hello"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <FrameLayout
        android:id="@+id/live_screen_bullet_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <!--  讲解中商品展示区域  -->
    <LinearLayout
        android:id="@+id/live_shop_card"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toTopOf="@id/live_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical">
    </LinearLayout>


    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/live_image_third_guideline"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.65" />

    <include layout="@layout/comprehensive_live_status_not_start"/>

    <include layout="@layout/comprehensive_live_status_finish"/>

    <include layout="@layout/comprehensive_live_status_pending"/>

<!--    <com.kaolafm.kradio.component.ui.base.view.KradioTextView-->
<!--        android:id="@+id/refresh_status_layout"-->
<!--        android:layout_width="@dimen/m260"-->
<!--        android:layout_height="@dimen/m72"-->
<!--        android:layout_marginStart="@dimen/m192"-->
<!--        android:gravity="center"-->
<!--        android:textSize="@dimen/m24"-->
<!--        android:visibility="gone"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:layout_constraintLeft_toLeftOf="parent"-->
<!--        app:layout_constraintTop_toBottomOf="@id/live_anchor">-->

<!--    </com.kaolafm.kradio.component.ui.base.view.KradioTextView>-->



    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/live_error_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/live_anchor">

        <TextView
            android:id="@+id/live_error_tip1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/m28"
            app:layout_constraintBottom_toTopOf="@id/live_error_tip2"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/live_error_tip2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/y40"
            android:textSize="@dimen/m20"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/live_error_tip1" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/live_coming_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/live_image_third_guideline">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:textSize="@dimen/m28"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.2789" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:textSize="@dimen/m20"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.4842" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <include
        android:id="@+id/loading"
        layout="@layout/refresh_center"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ViewStub
        android:id="@+id/live_no_network"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout="@layout/home_no_network_rl"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>