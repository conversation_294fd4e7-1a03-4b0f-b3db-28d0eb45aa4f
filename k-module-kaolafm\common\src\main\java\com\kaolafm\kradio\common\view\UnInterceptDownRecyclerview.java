package com.kaolafm.kradio.common.view;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import android.util.AttributeSet;
import android.view.MotionEvent;

public class UnInterceptDownRecyclerview extends RecyclerView {

    public UnInterceptDownRecyclerview(@NonNull Context context) {
        super(context);
    }

    public UnInterceptDownRecyclerview(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent e) {
        boolean b = super.onInterceptTouchEvent(e);
        if (e.getAction() == MotionEvent.ACTION_DOWN) {
            return false;
        }
        return b;
    }
}