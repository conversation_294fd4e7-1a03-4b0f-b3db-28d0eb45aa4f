<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/m156"
    android:layout_height="@dimen/m50"
    android:gravity="center"
    android:orientation="horizontal"
    tools:background="@drawable/message_details_btn_bg2"
   >

    <!--    android:layout_width="0dp"-->
    <!--    android:layout_weight="1"-->

    <ImageView
        android:id="@+id/messageBubbleIconIv"
        android:layout_width="@dimen/m26"
        android:layout_height="@dimen/m26" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="bottom">

        <TextView
            android:id="@+id/messageBubbleTextTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/bubble_btn_text_color"
            android:textSize="@dimen/m26"
            tools:text="重播" />

        <TextView
            android:id="@+id/messageBubbleTimerTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/x8"
            android:textColor="@color/bubble_timer_color"
            android:textSize="@dimen/m24"
            tools:text="(20s)" />
    </LinearLayout>
</LinearLayout>