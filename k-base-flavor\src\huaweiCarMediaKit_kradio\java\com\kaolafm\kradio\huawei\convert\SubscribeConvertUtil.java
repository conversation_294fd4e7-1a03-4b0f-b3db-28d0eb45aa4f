package com.kaolafm.kradio.huawei.convert;

import android.util.Log;

import com.huawei.carmediakit.bean.MediaElement;
import com.kaolafm.kradio.common.SubscribeData;
import com.kaolafm.kradio.huawei.utils.Constant;

import java.util.ArrayList;
import java.util.List;

import static com.kaolafm.kradio.huawei.utils.MediaIdHelper.getMediaId;

public class SubscribeConvertUtil {

    private static final String TAG = Constant.TAG;

    public static MediaElement toMediaElement(SubscribeData subscribeData) {
        MediaElement mediaElement = new MediaElement();
        mediaElement.setPlaying(false);
        if (subscribeData == null) {
            return mediaElement;
        }

        Log.i(TAG, "title=" + subscribeData.getType() + ":url="
                + subscribeData.getImg());
        mediaElement.setName(subscribeData.getName());
        mediaElement.setMediaId(getMediaId(subscribeData));
        mediaElement.setCoverUrl(subscribeData.getImg());
        mediaElement.setElementType(MediaElement.ElementType.ALBUM);
        mediaElement.setDesp(subscribeData.getDesc());

        return mediaElement;

    }

    public static List<MediaElement> toMediaElementList(List<SubscribeData> subscribeDataList) {
        List<MediaElement> mediaElementList = new ArrayList<>();
        MediaElement mediaElement;
        for (SubscribeData subscribeData: subscribeDataList) {
            mediaElement = toMediaElement(subscribeData);
            mediaElementList.add(mediaElement);
        }

        return mediaElementList;
    }
}
