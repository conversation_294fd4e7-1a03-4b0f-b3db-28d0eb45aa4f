package com.kaolafm.kradio.flavor.utils;


import com.kaolafm.kradio.lib.BuildConfig;
import com.kaolafm.opensdk.api.KaolaApiConstant;

import static android.app.Service.START_NOT_STICKY;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-08-09 10:18
 ******************************************/
public interface FlavorConstants {
    /**
     * 非粘性启动Service指令
     * <p>
     * 如果当前服务被终止了并且在此期间没有任何启动命令被传递到Service，
     * 那么系统将是使当前服务退出启动状态，
     * 并且除非重新调用Context.startService(Intent)，
     * 否则Service不会重新被创建（即不会重新调用onCreate方法）
     */
    int SERVICE_START_COMMAND = START_NOT_STICKY;

    String REQUEST_GET_DEFAULT_PLAYINFO = KaolaApiConstant.KAOLA_VERSION+"/operation/brandRadio";
}
