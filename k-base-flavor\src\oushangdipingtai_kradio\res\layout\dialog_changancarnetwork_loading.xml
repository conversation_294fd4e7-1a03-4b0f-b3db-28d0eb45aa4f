<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_dialog">


    <fr.castorflex.android.circularprogressbar.CircularProgressBar
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:id="@+id/changan_loading"
        style="@style/CustomerCircularProgressBar"
        android:layout_width="@dimen/m80"
        android:layout_height="@dimen/m80"
        android:visibility="visible"
        app:cpb_color="@color/circular_progress_color"
        app:cpb_stroke_width="@dimen/loading_progress_width"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="MissingConstraints"/>


    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="1"/>

    <TextView
        android:layout_width="@dimen/x110"
        android:layout_height="@dimen/y48"

        android:layout_marginBottom="@dimen/y62"
        android:gravity="center"
        android:text="@string/changan_loading"
        android:textColor="@color/colorWhite"
        android:textSize="@dimen/text_size3"
        app:layout_constraintBottom_toTopOf="@+id/guideline1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>