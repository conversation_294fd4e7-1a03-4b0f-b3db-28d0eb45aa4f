package com.kaolafm.kradio.history.comprehensive.ui;

import android.content.res.Configuration;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.SimpleItemAnimator;
import android.text.method.LinkMovementMethod;
import android.util.Log;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewStub;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.kaolafm.kradio.common.widget.CustomerRefreshHeader;
import com.kaolafm.kradio.lib.bean.HeadTitleItem;
import com.kaolafm.kradio.lib.bean.BroadcastRadioSimpleData;
import com.kaolafm.kradio.lib.bean.HistoryItem;
import com.kaolafm.kradio.lib.report.ReportUtil;
import com.kaolafm.kradio.component.ui.base.utils.ComponentUtils;
import com.kaolafm.kradio.home.utils.AppDateUtils;
import com.kaolafm.kradio.constant.UserComponentConst;
import com.kaolafm.kradio.history.mvp.IHistoryView;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioItemClickInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioMultiWindowInter;
import com.kaolafm.kradio.lib.base.flavor.UserCenterInter;
import com.kaolafm.kradio.lib.base.ui.BaseViewPagerLazyFragment;
import com.kaolafm.kradio.lib.dialog.Dialogs;
import com.kaolafm.kradio.lib.dialog.Dialogs.Builder;
import com.kaolafm.kradio.lib.toast.SuperToast;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.ComponentUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.RecyclerViewExposeUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.player.helper.intercept.OverhaulInterceptManager;
import com.kaolafm.kradio.user.UserInfoManager;
import com.kaolafm.opensdk.api.personalise.model.HotRecommend;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonClickReportEvent;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.util.ReportConstants;
import com.kaolafm.report.util.ReportParameterManager;
import com.lcodecore.tkrefreshlayout.RefreshListenerAdapter;
import com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout;
import com.lcodecore.tkrefreshlayout.utils.ScrollingUtil;

import java.util.ArrayList;
import java.util.List;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

/**
 * 电台的收听历史
 * Created by kaolafm on 2018/5/2.
 */

public class HistoryFragment extends BaseViewPagerLazyFragment<HistoryPresent>
        implements IHistoryView, RecyclerViewExposeUtil.OnItemExposeListener {
    private static final String TAG = "HistoryFragment";

    TwinklingRefreshLayout refreshLayout;
    RecyclerView mRvHistoryList;

    ConstraintLayout mRootLayout;

    View mHistoryLoading;

    ViewStub mHistoryNetworkError;

    LinearLayout no_content_root;
    ImageView iv_no_content;
    TextView tv_no_content2;
    TextView tv_no_content3;
    View no_content_layout;
    View historyCount;
    TextView tv_history_count;
    ImageView iv_user_clear_his;
    TextView upScroll, downScroll;

    private HistoryAdapter mHistoryAdapter;

    private volatile int lineCount = 2;

    GridLayoutManager layoutManager;

    public KRadioMultiWindowInter mKRadioMultiWindowInter;

    private BasePlayStateListener mPlayerStateListener;

    RelativeLayout mErrorLayout;
    private HotRecommend hotRecommend;
    private RecyclerViewExposeUtil exposeUtil;

    private static SuperToast mToast;

    @Override
    public void initView(View view) {

        mRootLayout=view.findViewById(R.id.history_main_layout);
        mHistoryLoading=view.findViewById(R.id.history_loading);
        mHistoryNetworkError=view.findViewById(R.id.vs_history_net_error);
        no_content_root=view.findViewById(R.id.no_content_root);
        iv_no_content=view.findViewById(R.id.iv_no_content);
        tv_no_content2=view.findViewById(R.id.tv_no_content2);
        tv_no_content3 = view.findViewById(R.id.tv_no_content3);
        no_content_layout = view.findViewById(R.id.tv_no_content_tip_layout);
        historyCount=view.findViewById(R.id.historyCount);
        tv_history_count=view.findViewById(R.id.tv_history_count);
        iv_user_clear_his=view.findViewById(R.id.user_clear_his);
        refreshLayout =view.findViewById(R.id.refreshLayout);
        initRefreshLayout();
        mRvHistoryList=view.findViewById(R.id.rv_history_list);
        iv_user_clear_his.setOnClickListener(v -> {
            if (AntiShake.check(iv_user_clear_his.getId())) {
                return;
            }
            clearHistory();
        });
        upScroll = view.findViewById(R.id.cd_up);
        downScroll = view.findViewById(R.id.cd_down);
        upScroll.setOnClickListener((v) -> {
            ScrollingUtil.scrollRrefreshListByVoice(mRvHistoryList, refreshLayout, -1);
        });
        downScroll.setOnClickListener((v) -> {
            ScrollingUtil.scrollRrefreshListByVoice(mRvHistoryList, refreshLayout, 1);
        });

        layoutManager = new GridLayoutManager(getContext(), lineCount) {
            @Override
            public void onLayoutChildren(RecyclerView.Recycler recycler, RecyclerView.State state) {
                try {
                    super.onLayoutChildren(recycler, state);
                } catch (Exception e) {
                    Log.d("HistoryFragment", "" + e);
                }
            }
        };
        //todo byd分屏适配需求
        layoutManager.setSpanSizeLookup(new GridLayoutManager.SpanSizeLookup() {
            @Override
            public int getSpanSize(int position) {
                int viewType = mHistoryAdapter.getItemViewType(position);
                if (viewType == HistoryAdapter.HEAD_UNLOGIN_TIP || viewType == HistoryAdapter.HISTORY_COUNT_TITLE) {
                    return lineCount;
                }
                return 1;
            }
        });
        mRvHistoryList.setLayoutManager(layoutManager);
        ((SimpleItemAnimator) mRvHistoryList.getItemAnimator()).setSupportsChangeAnimations(false);
        mRvHistoryList.getItemAnimator().setChangeDuration(0);
        mHistoryAdapter = new HistoryAdapter(this);
        mHistoryAdapter.setOnItemClickListener((itemView, viewType, historyItem, position) -> {
            if (historyItem == null) {
                return;
            }
            if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
                return;
            }
            //点击上报事件
            reportContentClickEvent(historyItem, position);
            if (historyItem.isOffline()) {
                ToastUtil.showOnActivity(getActivity(), getString(R.string.res_drop_off));
                return;
            }
            if (PlayerManagerHelper.getInstance().isPlayCurrentRadio(historyItem.getRadioId())) {
                KRadioItemClickInter kRadioItemClickInter = ClazzImplUtil
                        .getInter("KRadioItemClickImpl");
                if (kRadioItemClickInter != null) {
                    OverhaulInterceptManager.getInstance().setNeedOverhaulSwitch(false);
                    kRadioItemClickInter.doItemClick();
                }
                return;
            }
            if (StringUtil.equals(String.valueOf(PlayerConstants.RESOURCES_TYPE_BROADCAST), historyItem.getType())
                    || StringUtil.equals(String.valueOf(PlayerConstants.RESOURCES_TYPE_TV), historyItem.getType())) {
                BroadcastRadioSimpleData data = new BroadcastRadioSimpleData();
                data.setBroadcastId(Long.valueOf(historyItem.getRadioId()));
                data.setImg(historyItem.getPicUrl());
                data.setName(historyItem.getRadioTitle());
                if (StringUtil.equals(String.valueOf(PlayerConstants.RESOURCES_TYPE_TV), historyItem.getType())) {
                    data.setResType(PlayerConstants.RESOURCES_TYPE_TV);
                }
                PlayerManagerHelper.getInstance().addBroadcastRadioSimpleItem(data);
                if (StringUtil.equals(String.valueOf(PlayerConstants.RESOURCES_TYPE_BROADCAST), historyItem.getType())) {
                    OverhaulInterceptManager.getInstance().setNeedOverhaulSwitch(false);
                    PlayerManagerHelper.getInstance().startBroadcast(historyItem.getRadioId(), true);
                    return;
                }

            }
            OverhaulInterceptManager.getInstance().setNeedOverhaulSwitch(false);
            // do test 模拟生成数据 【（三体多人有声剧）】 start delete
//            historyItem.setType(String.valueOf(PlayerConstants.RESOURCES_TYPE_AUDIO));
//            historyItem.setRadioId("1100002161223");
//            historyItem.setAudioId("1000026798357");
//            historyItem.setPlayedTime(0L);
            // do test 模拟生成数据 【（三体多人有声剧）】 end delete
            PlayerManagerHelper.getInstance().startHistory(historyItem);// do
        });

        mPlayerStateListener = new BasePlayStateListener() {
            @Override
            public void onPlayerPreparing(PlayItem playItem) {
                if (mHistoryAdapter == null) {
                    return;
                }

                mHistoryAdapter.setPlaying(playItem.getRadioId());
            }

            @Override
            public void onPlayerPlaying(PlayItem playItem) {
                if (mHistoryAdapter == null) {
                    return;
                }
                mHistoryAdapter.setPlaying(playItem.getRadioId());
            }

            @Override
            public void onPlayerPaused(PlayItem playItem) {
                if (mHistoryAdapter == null) {
                    return;
                }
                mHistoryAdapter.setPlaying(playItem.getRadioId());
            }

            @Override
            public void onPlayerFailed(PlayItem playItem, int what, int extra) {
                super.onPlayerFailed(playItem, what, extra);
                if (mHistoryAdapter != null) {
                    mHistoryAdapter.setPlaying(playItem.getRadioId());
                }
            }
        };

        PlayerManager.getInstance().addPlayControlStateCallback(mPlayerStateListener);
        mRvHistoryList.setAdapter(mHistoryAdapter);
        mRvHistoryList.addItemDecoration(new HistoryItemDecoration(UserInfoManager.getInstance().isUserLogin()));

        exposeUtil = new RecyclerViewExposeUtil();
        exposeUtil.setRecyclerItemExposeListener(mRvHistoryList, this);

        mKRadioMultiWindowInter = ClazzImplUtil.getInter("KradioMultiWindowImpl");
    }

    private void initRefreshLayout() {
        refreshLayout.setHeaderView(new CustomerRefreshHeader(getContext()));
        refreshLayout.setEnableRefresh(true);
        refreshLayout.setEnableLoadmore(false);
        refreshLayout.setOnRefreshListener(new RefreshListenerAdapter() {
            @Override
            public void onRefresh(TwinklingRefreshLayout refreshLayout) {
                super.onRefresh(refreshLayout);
                if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(getContext())) {
                    refreshLayout.finishRefreshing();
                    return;
                }
                if (mPresenter != null) {
                    mPresenter.getHistoryList();
                }
            }
        });
    }

    /**
     * 点击item事件上报
     *
     * @param item
     */
    private void reportContentClickEvent(HistoryItem item, int position) {
        int typeInt = PlayerConstants.RESOURCES_TYPE_INVALID;
        try {
            typeInt = Integer.parseInt(item.getType());
        } catch (Exception e) {
            Log.i(TAG, "error=" + e.toString());
        }
//        ReportUtil.addContentClickEvent("", ReportParamUtil.getRadioType(typeInt),
//                "", String.valueOf(item.getRadioId()),
//                ReportParamUtil.getEventTag(item.isVIP(), item.isFine()),
//                getPageId(), String.valueOf(item.getCategoryId()), "");


        String tag = ComponentUtils.getInstance().getReportTag(item.getFreq(), item.getVip());
        ReportUtil.addComponentShowAndClickEvent("",
                true, "10"
                , 2, "", position + "",
                0 + "", String.valueOf(item.getRadioId())
                , tag, ReportParameterManager.getInstance().getPage(), tag);
    }

    @Override
    public String getPageId() {
        return Constants.PAGE_ID_MINE_HISTRORY;
    }

    @Override
    protected int getLayoutId() {
        return R.layout.fragment_history;
    }

    @Override
    protected int getLayoutId_Tow() {
        return 0;
    }

    @Override
    protected HistoryPresent createPresenter() {
        return new HistoryPresent(this);
    }

    @Override
    public void showButton(boolean show) {
        Fragment fragment = getParentFragment();
        if (fragment instanceof ButtonOperation) {
            ((ButtonOperation) fragment).showOrHide(show);
        }
    }

    @Override
    public void loginStatusChanged() {
        if (mRvHistoryList.getItemDecorationCount() > 0) {
            mRvHistoryList.removeItemDecorationAt(0);
        }
        mRvHistoryList.addItemDecoration(new HistoryItemDecoration(UserInfoManager.getInstance().isUserLogin()));
    }

    @Override
    public void showHistory(List<HistoryItem> historyItems) {
        showHistory(true);
        String format = ResUtil.getString(R.string.history_count);
//        mTvHistoryCount.setText(String.format(format, historyItems.size()));
        if (historyItems == null) {
            historyItems = new ArrayList<>();
        }

        if (historyItems.size() > 0) {
            HistoryItem historyItem0 = historyItems.get(0);
            historyCount.setVisibility(historyItem0 instanceof HeadTitleItem && historyItem0.getTypeId() == HistoryAdapter.HEAD_UNLOGIN_TIP ? View.GONE : View.VISIBLE);
            int count = 0;
            if (historyItem0 instanceof HeadTitleItem && historyItem0.getTypeId() == HistoryAdapter.HISTORY_COUNT_TITLE) {
                count = ((HeadTitleItem) historyItem0).getCount();
            } else if (historyItems.size() > 1) {
                HistoryItem historyItem1 = historyItems.get(1);
                if (historyItem1 instanceof HeadTitleItem && historyItem1.getTypeId() == HistoryAdapter.HISTORY_COUNT_TITLE) {
                    count = ((HeadTitleItem) historyItem1).getCount();
                }
            }
            tv_history_count.setText(String.format(format, count));
            // 更新主题样式
            updateHistoryCountTheme();
            if (historyItem0 instanceof HeadTitleItem && historyCount.getVisibility() == View.VISIBLE){
                // 删除 rv 的第一个[有x条收听历史]，否则会和固定的重复
                historyItems.remove(historyItem0);
            }
        }
        DiffUtil.DiffResult diffResult =
                DiffUtil.calculateDiff(new HistoryDiffCallback(historyItems, mHistoryAdapter.getDataList()), false);
        // DiffUtil无法处理item位置变化时的数据更新
        if (positionChanged(historyItems, mHistoryAdapter.getDataList())) {
            mHistoryAdapter.notifyDataSetChanged();
        } else {
            diffResult.dispatchUpdatesTo(mHistoryAdapter);
        }

        mHistoryAdapter.setDataList(historyItems, false);
        mRvHistoryList.postDelayed(new Runnable() {
            @Override
            public void run() {
                exposeUtil.handleCurrentVisibleItems();
            }
        },200);
    }

    private boolean positionChanged(List<HistoryItem> oldData, List<HistoryItem> newData) {
        if (oldData == null || newData == null) {
            return true;
        }
        if (oldData.size() != newData.size()) {
            return true;
        }
        try {
            for (int i = 0; i < oldData.size(); i++) {
                if (!oldData.get(i).getRadioId().equals(newData.get(i).getRadioId())) {
                    return true;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    @Override
    public void showEmpty() {
        showHistory(false);
    }

    @Override
    public void finishRefresh(boolean success) {
        refreshLayout.finishRefreshing();
    }

    @Override
    public void prepareFragmentStateForShowLoading() {
        mHistoryAdapter.setDataList(null);
        ViewUtil.setViewVisibility(no_content_root, View.GONE);
        ViewUtil.setViewVisibility(mErrorLayout, View.GONE);
    }

    @Override
    public void showLoading() {
        ViewUtil.setViewVisibility(mHistoryLoading, View.VISIBLE);
    }

    @Override
    public void hideLoading() {
        ViewUtil.setViewVisibility(mHistoryLoading, View.GONE);
    }

    private void showHistory(boolean haveHistory) {
        if (haveHistory) {
            ViewUtil.setViewVisibility(no_content_root, View.GONE);
        } else {
            setTextViewSpannable();
            ViewUtil.setViewVisibility(no_content_root, View.VISIBLE);
        }
        ViewUtil.setViewVisibility(mRvHistoryList, haveHistory ? View.VISIBLE : View.GONE);
        ViewUtil.setViewVisibility(mErrorLayout, View.GONE);
        historyCount.setVisibility(View.GONE);
//        ViewUtil.setViewVisibility(mTvHistoryCount, haveHistory ? View.VISIBLE : View.GONE);
    }

    private void setTextViewSpannable() {
        if (tv_no_content3 != null) {
            tv_no_content3.setMovementMethod(LinkMovementMethod.getInstance());
            if (hotRecommend != null) {
                ViewUtil.setViewVisibility(no_content_layout, View.VISIBLE);
                tv_no_content3.setHighlightColor(ResUtil.getColor(R.color.transparent));
                tv_no_content3.setText(AppDateUtils.getInstance().getTextViewSpannable2(hotRecommend));
            } else {
                ViewUtil.setViewVisibility(no_content_layout, View.GONE);
            }
        }
        tv_no_content2.setText(ResUtil.getString(R.string.no_listening_history2));
        iv_no_content.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_history_exception_pic));
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (mPresenter != null) {
            mPresenter.start();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        PlayerManager.getInstance().removePlayControlStateCallback(mPlayerStateListener);
    }

    @Override
    protected void lazyLoad() {

    }


    @Override
    public void onUserVisible() {
        super.onUserVisible();
        //因为需要每次显示历史都刷新数据，所以lazyLoad不适用
        NetworkUtil.isNetworkAvailableWidthDefaultToast(getContext());
        if (mPresenter != null) {
            mPresenter.getHistoryList();
        }
//        if (isVisibleToUser){
//        if (exposeUtil != null) {
//            mRvHistoryList.postDelayed(new Runnable() {
//                @Override
//                public void run() {
//                    exposeUtil.handleCurrentVisibleItems();
//                }
//            }, 200);
////            }
//        }
    }

    public void clickClear() {

        boolean isLogin = false;
        try {
            isLogin = ComponentUtil.getResultValue(UserComponentConst.NAME, UserComponentConst.IS_USER_BOUND);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (isLogin && !NetworkUtil.isNetworkAvailable(getContext(), false)) {
            showToast(R.string.no_net_work_str);
            return;
        }

        DialogFragment dialogFragment = new Builder()
                .setType(Dialogs.TYPE_2BTN)
                .setGravity(Gravity.CENTER)
                .setTitle(ResUtil.getString(R.string.user_history_clear_str))
                .setMessage(ResUtil.getString(R.string.are_you_sure_to_clear_your_listening_history))
                .setOnPositiveListener(dialog -> {
                    mPresenter.clearHistory();
                    dialog.dismiss();
                })
                .create();
        dialogFragment.show(getFragmentManager(), "clear_history");
    }


    private void updateView(boolean isLand) {
        ConstraintSet set = new ConstraintSet();
        set.clone(mRootLayout);
        if (isLand) {
            set.setVerticalBias(no_content_root.getId(), 0.5f);
        } else {
            set.setVerticalBias(no_content_root.getId(), 0.36f);
        }
        set.applyTo(mRootLayout);
    }

//    @Override
//    protected void showAccordingToScreen(int orientation) {
//        int paddingLeft = ScreenUtil.getGlobalPaddingLeft(orientation);
//        int paddingRight = ScreenUtil.getGlobalPaddingRight(orientation);
//
//        mRootLayout.setPadding(paddingLeft, 0, paddingRight, 0);
//
//        //
//        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
//            lineCount = 2;
//            updateView(true);
//        } else {
//            lineCount = 1;
//            updateView(false);
//        }
//
//        if (mKRadioMultiWindowInter == null || !mKRadioMultiWindowInter.setHistoryGridLayoutManager(layoutManager)) {
//            layoutManager.setSpanCount(lineCount);
//        }
//
//        mRvHistoryList.setLayoutManager(layoutManager);
//        mHistoryAdapter.notifyDataSetChanged();
//
//    }

    private void showErrorLayout(String error, boolean clickToRetry) {
        if (mErrorLayout == null) {
            mErrorLayout = (RelativeLayout) mHistoryNetworkError.inflate();
            TextView tvNetworkNosign = mErrorLayout.findViewById(R.id.tv_network_nosign);
            tvNetworkNosign.setText(error);
            // 支持点击重试
            if (clickToRetry) {
                ImageView ivNetworkNoSign = mErrorLayout.findViewById(R.id.network_nosigin);
                ivNetworkNoSign.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        hideErrorLayout();
                        mPresenter.getHistoryList();
                        TextView tvNetworkNosign = mErrorLayout.findViewById(R.id.tv_network_nosign);
                        String text = null;
                        if (tvNetworkNosign != null) {
                            text = tvNetworkNosign.getText().toString();
                        }
                        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK,
                                ButtonExposureOrClickReportEvent.BUTTON_ID_NETWORK_RETRY, text, getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
                    }
                });
            }
        }
        ViewUtil.setViewVisibility(no_content_root, View.GONE);
        ViewUtil.setViewVisibility(mErrorLayout, View.VISIBLE);
        if (mErrorLayout != null) {
            TextView tvNetworkNosign = mErrorLayout.findViewById(R.id.tv_network_nosign);
            String text = null;
            if (tvNetworkNosign != null) {
                text = tvNetworkNosign.getText().toString();
            }
            ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_NETWORK_RETRY, text, getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
        }
    }

    @Override
    public void hideErrorLayout() {
        if (mErrorLayout == null) {
            return;
        }
        ViewUtil.setViewVisibility(mErrorLayout, View.GONE);
    }

    @Override
    public void showError(String error, boolean clickToRetry) {
        ViewUtil.setViewVisibility(mRvHistoryList, View.GONE);
        ViewUtil.setViewVisibility(no_content_root, View.GONE);
        hideLoading();
        showErrorLayout(error, true);
    }

    @Override
    public void showToast(int resId) {
        ToastUtil.showError(getContext(), resId);
    }

    @Override
    public void showHotRecommend(HotRecommend hotRecommend) {
        this.hotRecommend = hotRecommend;
        setTextViewSpannable();
    }

    @Override
    protected void changeViewLayoutForStatusBar(View view) {
    }

    @Override
    protected void addFragmentRootViewPadding(View view) {
    }

    @Override
    protected void showAccordingToScreen(int orientation) {
        boolean isLand = ResUtil.getOrientation() == Configuration.ORIENTATION_LANDSCAPE;
        int paddingLeft = isLand ? ResUtil.getDimen(R.dimen.default_edge_start) : ResUtil.getDimen(R.dimen.x50);//ScreenUtil.getGlobalPaddingLeft(orientation);
        int paddingRight = isLand ? ResUtil.getDimen(R.dimen.default_edge_end) : ResUtil.getDimen(R.dimen.x50);//ScreenUtil.getGlobalPaddingRight(orientation);

        mRootLayout.setPadding(paddingLeft, 0, paddingRight, 0);

        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            lineCount = 2;
            updateView(true);
        } else {
            lineCount = 1;
            updateView(false);
        }

        Fragment fragment = getParentFragment();
        boolean multi = false;
//        if (fragment instanceof BackUserFragment) {
//            if (((BackUserFragment) fragment).mKRadioMultiWindowInter != null && ((BackUserFragment) fragment).mKRadioMultiWindowInter.setHistoryGridLayoutManager(layoutManager)) {
//                multi = true;
//            }
//        }

        KRadioMultiWindowInter multiWindowImpl = ClazzImplUtil.getInter("KradioMultiWindowImpl");
        if (multiWindowImpl != null) {
            boolean success = mKRadioMultiWindowInter.setHistoryGridLayoutManager(layoutManager);
            if (success) multi = true;
        }

        if (!multi) {
            layoutManager.setSpanCount(lineCount);
            // 解决横竖屏切换后adapter item位置错乱的问题
            mRvHistoryList.setAdapter(null);
            mRvHistoryList.setLayoutManager(null);
            mRvHistoryList.getRecycledViewPool().clear();
            mRvHistoryList.setAdapter(mHistoryAdapter);
        }
        mRvHistoryList.setLayoutManager(layoutManager);
        mHistoryAdapter.notifyDataSetChanged();

    }

    @Override
    public void onMultiWindowModeChanged(boolean isInMultiWindowMode) {
        super.onMultiWindowModeChanged(isInMultiWindowMode);
        if (mRvHistoryList != null) {
            RecyclerView.Adapter adapter = mRvHistoryList.getAdapter();
            RecyclerView.LayoutManager manager = mRvHistoryList.getLayoutManager();
            mRvHistoryList.setAdapter(null);
            mRvHistoryList.setLayoutManager(null);
            mRvHistoryList.getRecycledViewPool().clear();
            mRvHistoryList.setLayoutManager(manager);
            mRvHistoryList.setAdapter(adapter);
        }
    }

    protected boolean isReportFragment() {
        return true;
    }

    @Override
    public boolean useEventBus() {
        return true;
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onItemViewVisible(boolean visible, int position) {
        if (visible && mHistoryAdapter != null) {
            HistoryItem item = mHistoryAdapter.getItemData(position);
            if (item.getTypeId() == HistoryAdapter.HEAD_UNLOGIN_TIP
                    || item.getTypeId() == HistoryAdapter.HISTORY_COUNT_TITLE) {
                //非列表数据不上报
                return;
            }
            reportContentShowEvent(item, position);
        }
    }

    private void reportContentShowEvent(HistoryItem item, int position) {
        int typeInt = PlayerConstants.RESOURCES_TYPE_INVALID;
        try {
            typeInt = Integer.parseInt(item.getType());
        } catch (Exception e) {
            Log.i(TAG, "error=" + e.toString());
        }
//        ReportUtil.addContentShowEvent("", ReportParamUtil.getRadioType(typeInt),
//                "", String.valueOf(item.getRadioId()),
//                ReportParamUtil.getEventTag(item.isVIP(), item.isFine()),
//                getPageId(), String.valueOf(item.getCategoryId()), "");

        String tag = ComponentUtils.getInstance().getReportTag(item.getFreq(), item.getVip());
        ReportUtil.addComponentShowAndClickEvent("",
                false, "10"
                , 2, "", position + "",
                0 + "", String.valueOf(item.getRadioId())
                , tag, ReportParameterManager.getInstance().getPage(), tag);
    }

    void clearHistory() {
        clickClear();
        //点击一键清空事件上报
        ButtonClickReportEvent event = new ButtonClickReportEvent(ButtonClickReportEvent.BUTTON_CLICK_CLEAR);
        ReportHelper.getInstance().addEvent(event);
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_DELETE_BUTTON, "", ReportParameterManager.getInstance().getPage(), ReportConstants.CONTROL_TYPE_SCREEN));
    }

    private void updateHistoryCountTheme() {
        if (historyCount == null) return;

        boolean isDayMode = getCurrentThemeIsDayMode();
        historyCount.setBackground(ResUtil.getDrawable(isDayMode ? R.drawable.bg_home_day : R.drawable.bg_home));
    }

    /**
     * 从Settings读取当前是否为白天模式   true 白天模式  false 黑夜模式
     */
    private boolean getCurrentThemeIsDayMode() {
        try {
            String theme = android.provider.Settings.System.getString(
                getActivity().getContentResolver(), "android.car.THEME_TYPE");
            return !android.text.TextUtils.isEmpty(theme) && !"theme.night".equals(theme);
        } catch (Exception e) {
            android.util.Log.w(TAG, "读取主题失败，默认白天模式", e);
            return true;
        }
    }

    /**
     * 监听主题切换事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onThemeChangeEvent(UserCenterInter.ThemeChangeEvent event) {
        if (event == null) return;

        String theme = event.getTheme();
        Log.d(TAG, "收到主题变化事件: " + theme);

        // 只处理相关的主题事件
        if ("isSameTheme".equals(theme) || "night.skin".equals(theme) || "day.skin".equals(theme)) {
            refreshUIForThemeChange();
        }
    }

    /**
     * 刷新UI以适应主题变化
     */
    private void refreshUIForThemeChange() {
        if (historyCount == null) return;

        historyCount.postDelayed(() -> {
            updateHistoryCountTheme();
            if (mHistoryAdapter != null) {
                mHistoryAdapter.notifyDataSetChanged();
            }
            Log.d(TAG, "主题切换UI刷新完成");
        }, 300);
    }
}
