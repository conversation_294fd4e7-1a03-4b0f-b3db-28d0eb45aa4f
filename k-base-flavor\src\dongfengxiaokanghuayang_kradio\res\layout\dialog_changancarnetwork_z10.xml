<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_dialog">


    <TextView
        android:id="@+id/changan_title_z10"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/m62"
        android:text="@string/changan_title_z10"
        android:gravity="center"
        android:textColor="@color/colorWhite"
        android:textSize="@dimen/text_size3"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>


    <TextView
        android:id="@+id/changan_content_z10"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingLeft="@dimen/x30"
        android:paddingRight="@dimen/x30"
        android:text="@string/changan_content_z10"
        android:gravity="center"
        android:textColor="@color/colorWhite"
        android:textSize="@dimen/text_size3"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>


    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="1"/>

    <TextView
        android:id="@+id/changan_retry_z10"
        android:layout_width="@dimen/x110"
        android:layout_height="@dimen/y48"
        android:layout_marginRight="@dimen/x30"
        android:background="@drawable/globle_round_bg"
        android:gravity="center"
        android:text="@string/changan_retry"
        android:textColor="@color/colorWhite"
        android:textSize="@dimen/text_size3"
        android:layout_marginBottom="@dimen/y62"
        app:layout_constraintBottom_toTopOf="@+id/guideline1"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/changan_cancel_z10"/>

    <TextView
        android:id="@+id/changan_cancel_z10"
        android:layout_width="@dimen/x110"
        android:layout_height="@dimen/y48"
        android:layout_marginLeft="@dimen/x30"
        android:background="@drawable/globle_round_bg"
        android:gravity="center"
        android:text="@string/changan_cancel"
        android:textColor="@color/colorWhite"
        android:textSize="@dimen/text_size3"
        android:layout_marginBottom="@dimen/y62"
        app:layout_constraintBottom_toTopOf="@+id/guideline1"
        app:layout_constraintLeft_toRightOf="@id/changan_retry_z10"
        app:layout_constraintRight_toRightOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>