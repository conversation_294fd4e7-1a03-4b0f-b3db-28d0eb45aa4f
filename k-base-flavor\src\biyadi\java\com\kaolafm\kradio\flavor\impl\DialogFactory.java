package com.kaolafm.kradio.flavor.impl;

import androidx.fragment.app.DialogFragment;
import android.view.Gravity;

import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.dialog.Dialogs;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.view.Center2BtnDialogFragment;
import com.kaolafm.kradio.view.FullScreenDialogFragment;

public class DialogFactory implements com.kaolafm.kradio.lib.base.flavor.DialogCreater {

    @Override
    public DialogFragment create(Dialogs.Builder builder) {
        DialogFragment dialog = null;

        if (builder == null) {
            dialog = Center2BtnDialogFragment.create();
            return dialog;
        }

        if (builder.getType() == Dialogs.TYPE_2BTN && builder.getGravity() == Gravity.CENTER) {
            dialog = Center2BtnDialogFragment.create();
            ((Center2BtnDialogFragment) dialog).setWidth(ResUtil.getDimen(R.dimen.m605));
            ((Center2BtnDialogFragment) dialog).setHeight(ResUtil.getDimen(R.dimen.m309));
            ((Center2BtnDialogFragment) dialog).setMessage(builder.getMessage());
            ((Center2BtnDialogFragment) dialog).setLeftButton(builder.getLeftBtnText());
            ((Center2BtnDialogFragment) dialog).setRightButton(builder.getRightBtnText());
            ((Center2BtnDialogFragment) dialog).setOnNativeListener(builder.getOnNativeListener());
            ((Center2BtnDialogFragment) dialog).setOnPositiveListener(builder.getOnPositiveListener());
        } else if (builder.getType() == Dialogs.TYPE_LIST) {
            dialog = FullScreenDialogFragment.create();
            ((FullScreenDialogFragment) dialog).setTitle(builder.getTitle());
            ((FullScreenDialogFragment) dialog).setAdapter(builder.getAdapter());
        } else {
            //比亚迪默认居中
            dialog = Center2BtnDialogFragment.create();
            ((Center2BtnDialogFragment) dialog).setWidth(ResUtil.getDimen(R.dimen.m605));
            ((Center2BtnDialogFragment) dialog).setHeight(ResUtil.getDimen(R.dimen.m309));
            ((Center2BtnDialogFragment) dialog).setMessage(builder.getMessage());
            ((Center2BtnDialogFragment) dialog).setLeftButton(builder.getLeftBtnText());
            ((Center2BtnDialogFragment) dialog).setRightButton(builder.getRightBtnText());
            ((Center2BtnDialogFragment) dialog).setOnNativeListener(builder.getOnNativeListener());
            ((Center2BtnDialogFragment) dialog).setOnPositiveListener(builder.getOnPositiveListener());
        }

        return dialog;
    }
}