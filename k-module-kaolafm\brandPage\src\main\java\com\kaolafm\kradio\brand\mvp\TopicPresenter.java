package com.kaolafm.kradio.brand.mvp;

import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.kradio.lib.base.ui.BaseActivity;
import com.kaolafm.kradio.lib.base.ui.BaseDialogFragment;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.topic.TopicRequest;
import com.kaolafm.opensdk.api.topic.model.OperationResponse;
import com.kaolafm.opensdk.api.topic.model.TopicDetail;
import com.kaolafm.opensdk.api.topic.model.TopicPosts;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.trello.rxlifecycle3.LifecycleProvider;
import com.trello.rxlifecycle3.LifecycleTransformer;
import com.trello.rxlifecycle3.android.ActivityEvent;
import com.trello.rxlifecycle3.android.FragmentEvent;

import org.jetbrains.annotations.NotNull;

import java.util.List;

/**
 * <AUTHOR> shiqian
 * @date 2023-03-01
 */
public class TopicPresenter extends BasePresenter<TopicModel, ITopicDetailView> {

    public TopicPresenter(ITopicDetailView view) {
        super(view);
    }

    @Override
    protected TopicModel createModel() {
        LifecycleTransformer lifecycleTransformer = null;
        if (mView instanceof BaseActivity) {
            lifecycleTransformer = ((BaseActivity) mView).bindUntilEvent(ActivityEvent.DESTROY);
        } else if (mView instanceof BaseFragment) {
            lifecycleTransformer = ((BaseFragment) mView).bindUntilEvent(FragmentEvent.DESTROY_VIEW);
        } else if (mView instanceof BaseDialogFragment) {
            lifecycleTransformer = ((BaseDialogFragment) mView).bindUntilEvent(FragmentEvent.DESTROY_VIEW);
        }
        return new TopicModel(lifecycleTransformer);
    }

    public void getTopicDetail(long brandPageId) {
        mModel.getTopicDetail(brandPageId, new HttpCallback<TopicDetail>() {
            @Override
            public void onSuccess(TopicDetail topicDetails) {
                if (mView == null) return;
                if (topicDetails != null)
                    mView.onGetTopicDetailSuccess(topicDetails);
                else mView.onGetTopicDetailFailure();
            }

            @Override
            public void onError(ApiException e) {
                if (mView == null) return;
                mView.onGetTopicDetailFailure();
            }
        });
    }

    /**
     * 加载数据
     *
     * @param mTopicId
     * @param orderWay
     * @param mPageNum
     * @param mPageSize
     */
    public void loadPostsList(long mTopicId, int orderWay, int mPageNum, int mPageSize) {
        if (mModel == null) {
            if (mView != null) {
                mView.onGetPostsListFailure();
            }
            return;
        }
        mModel.getPostsList(mTopicId, orderWay, mPageNum, mPageSize, new HttpCallback<BasePageResult<List<TopicPosts>>>() {
            @Override
            public void onSuccess(BasePageResult<List<TopicPosts>> listBasePageResult) {
                if (mView == null) return;
                mView.onGetPostsListSuccess(listBasePageResult);
            }

            @Override
            public void onError(ApiException e) {
                if (mView == null) return;
                mView.onGetPostsListFailure();
            }
        });
    }

    /**
     * 点赞/取消点赞
     *
     * @param mTopicPosts
     */
    public void operatePosts(@NotNull TopicPosts mTopicPosts, int position, boolean isLike) {
        int type;
        if (!isLike)
            type = TopicRequest.POSTS_OPERATE_UNLIKE;
        else type = TopicRequest.POSTS_OPERATE_LIKE;
        mModel.operatePostsLike(mTopicPosts.getPostId(), type, new HttpCallback<OperationResponse>() {
            @Override
            public void onSuccess(OperationResponse likeOperation) {
                if (mView == null) return;
                if (likeOperation.getStatus() == 1) {
                    long count = mTopicPosts.getLikeCount();
                    if (type == TopicRequest.POSTS_OPERATE_UNLIKE) {
                        mTopicPosts.setLikeStatus(TopicPosts.STATUS_UNLIKE);
                        mTopicPosts.setLikeCount(--count);
                    } else {
                        mTopicPosts.setLikeStatus(TopicPosts.STATUS_LIKED);
                        mTopicPosts.setLikeCount(++count);
                    }
                    mView.onOperatePostsSuccess(mTopicPosts, position);
                } else {
                    mView.onOperatePostsFailure(type);
                }
            }

            @Override
            public void onError(ApiException e) {
                if (mView == null) return;
                mView.onOperatePostsFailure(type);
            }
        });
    }

    /**
     * 自动切换
     *
     * @param mTopicPosts
     */
    public void toggleLikePosts(@NotNull TopicPosts mTopicPosts, int position) {
        operatePosts(mTopicPosts, position, mTopicPosts.getLikeStatus() != TopicPosts.STATUS_LIKED);

    }

    /**
     * 发布帖子
     *
     * @param topicId
     * @param content
     */
    public void publishPosts(long topicId, String content) {
        mModel.publishPosts(topicId, content, new HttpCallback<OperationResponse>() {
            @Override
            public void onSuccess(OperationResponse operationResponse) {
                if (mView == null) return;
                if (operationResponse.getStatus() == 1) {
                    mView.onPublishPostsSuccess();
                } else {
                    mView.onPublishPostsFailure();
                }
            }

            @Override
            public void onError(ApiException e) {
                if (mView == null) return;
                mView.onPublishPostsFailure();
            }
        });
    }
}
