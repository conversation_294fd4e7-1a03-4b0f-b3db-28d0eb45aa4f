package com.kaolafm.kradio.user;

import android.content.Context;
import android.content.DialogInterface;
import androidx.annotation.StringRes;
import android.view.View;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.dialog.BaseCenterDialog;

/**
 * <AUTHOR>
 **/
public class SingleChoiceDialog extends BaseCenterDialog {


    private static class Params {
        public DialogInterface.OnClickListener onClickListener;
        AdapterView.OnItemSelectedListener onItemSelectedListener;
        BaseAdapter adatper;
        Context context;
        CharSequence title;

        public Params(Context context) {
            this.context = context;
        }
    }

    public static class Builder {
        private final Params p;

        public Builder(Context context) {
            p = new Params(context);
        }

        public Builder setTitle(CharSequence title) {
            p.title = title;
            return this;
        }

        public Builder setTitle(@StringRes int titleId) {
            p.title = p.context.getString(titleId);
            return this;
        }

        public Builder setAdapter(BaseAdapter adapter, DialogInterface.OnClickListener listener) {
            p.adatper = adapter;
            p.onClickListener = listener;
            return this;
        }

        public Builder setAdapter(BaseAdapter adapter) {
            p.adatper = adapter;
            return this;
        }

        public Builder setOnItemSelectedListener(AdapterView.OnItemSelectedListener listener) {
            p.onItemSelectedListener = listener;
            return this;
        }

        public SingleChoiceDialog build() {
            SingleChoiceDialog dialog = new SingleChoiceDialog(p.context);
            dialog.setTitle(p.title);
            dialog.setAdapter(p.adatper);
            dialog.setOnItemSelectedListener(p.onItemSelectedListener);
            dialog.setOnItemClickListener(p.onClickListener);
            return dialog;
        }


    }


    private ListAdapter mAdapter;
    private AdapterView.OnItemSelectedListener onItemSelectedListener;
    private DialogInterface.OnClickListener mOnClickListener;
    private CharSequence mTitle;


    private void setAdapter(ListAdapter adatper) {
        this.mAdapter = adatper;
    }

    private void setTitle(CharSequence title) {
        this.mTitle = title;
    }

    private void setOnItemSelectedListener(AdapterView.OnItemSelectedListener l) {
        this.onItemSelectedListener = l;
    }

    private void setOnItemClickListener(DialogInterface.OnClickListener l) {
        this.mOnClickListener = l;
    }

    TextView dialogTitle;
    ListView dialogList;


    public SingleChoiceDialog(Context context) {
        super(context);
    }

    @Override
    protected void initView(View view) {
        dialogTitle=view.findViewById(R.id.dialog_title);
        dialogList=view.findViewById(android.R.id.list);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.dialog_single_choice;
    }

    @Override
    protected void onCreateView() {
        super.onCreateView();
        dialogTitle.setText(mTitle);
        dialogList.setChoiceMode(ListView.CHOICE_MODE_SINGLE);
        dialogList.setAdapter(mAdapter);
        if (mOnClickListener != null) {
            dialogList.setOnItemClickListener(new AdapterView.OnItemClickListener() {
                @Override
                public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                    mOnClickListener.onClick(mDialog, position);
                }
            });
        }
    }

}
