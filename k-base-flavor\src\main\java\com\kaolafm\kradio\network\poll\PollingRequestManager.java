package com.kaolafm.kradio.network.poll;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.common.http.RequestInterceptManager;
import com.kaolafm.kradio.lib.bean.ListenHistoryDataBean;
import com.kaolafm.kradio.lib.base.flavor.LoginHttpUrl;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.opensdk.account.token.TingbanTokenObserver;
import com.kaolafm.opensdk.api.history.model.SyncHistoryStatus;
import com.kaolafm.opensdk.api.live.model.SocketLiveBean;
import com.kaolafm.opensdk.api.operation.OperationRequest;
import com.kaolafm.opensdk.api.operation.model.column.ColumnGrp;
import com.kaolafm.opensdk.api.poll.PollingRequest;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.opensdk.socket.SocketEvent;
import com.kaolafm.opensdk.socket.SocketListener;

import java.util.List;
import java.util.Map;

/**
 * 用于轮询的请求
 *
 * <AUTHOR> Yan
 * @date 2020/6/11
 */
public class PollingRequestManager {

    private static volatile PollingRequestManager mInstance;

    private LoginHttpUrl mLoginHttpUrlImpl;

    private OperationRequest mOperationRequest;

    private PollingRequest mPollingRequest;

    private TingbanTokenObserver mTokenObserver;

    public static PollingRequestManager getInstance() {
        if (mInstance == null) {
            synchronized (PollingRequestManager.class) {
                if (mInstance == null) {
                    mInstance = new PollingRequestManager();
                }
            }
        }
        return mInstance;
    }

    private PollingRequestManager() {
        mPollingRequest = new PollingRequest();
        mOperationRequest = new OperationRequest();
    }

    public <T> void request(String event, Map<String, Object> params, SocketListener<T> listener) {
        Logging.i("PollingRequestManager", "request: " + event);
        switch (event) {
            case SocketEvent.HOME_PAGE_REFRESH:
                mOperationRequest.getColumnTree(true, "mainPage", (HttpCallback<List<ColumnGrp>>) listener);
                break;
            case SocketEvent.RADIO_LIVE:
                mPollingRequest.getRadioLiveStream(params, (HttpCallback<List<SocketLiveBean>>) listener);
                break;
            case SocketEvent.COIN_REFRESH:
//                mPollingRequest.getIntegrals(params, (HttpCallback<CoinBean>) listener);
                break;
            case SocketEvent.SAVE_HISTORY:
                List<ListenHistoryDataBean> userHistoryList = (List<ListenHistoryDataBean>) params
                        .get("userHistoryList");
                if (!ListUtil.isEmpty(userHistoryList)) {
                    mPollingRequest.saveHistoryAccumulate(params, (HttpCallback<SyncHistoryStatus>) listener);
                }
                break;
            case SocketEvent.USER_LOGIN:
            case SocketEvent.USER_LOGOUT:
                //这里不再回调listener，因为是使用SDK内部的退出逻辑，即当前账号在其他设备登录时，服务器会判断当前设置的token和refreshToken都过期，
                // 这时SDK内部会执行退出逻辑，清空token，并回调AccessTokenObserver#onChange(T var1)，该回调已经在UserInfoManager中注册过了。
                if (mLoginHttpUrlImpl == null) {
                    mLoginHttpUrlImpl = ClazzImplUtil.getInter("LoginHttpUrlImpl");
                    if (mLoginHttpUrlImpl == null) {
                        mLoginHttpUrlImpl = new LoginHttpUrlImpl();
                    }
                    RequestInterceptManager.getInstance().setLoginHttpUrl(mLoginHttpUrlImpl);
                }
                break;
            default:
        }
    }
}
