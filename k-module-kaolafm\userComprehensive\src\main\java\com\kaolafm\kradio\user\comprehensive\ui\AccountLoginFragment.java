package com.kaolafm.kradio.user.comprehensive.ui;

import android.animation.ObjectAnimator;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.Build;
import androidx.annotation.RequiresApi;
import androidx.constraintlayout.widget.ConstraintLayout;

import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.AnimUtil;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.NetworkManager;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.lib.utils.imageloader.listener.OnImageLoaderListener;
import com.kaolafm.kradio.user.UserInfoManager;
import com.kaolafm.kradio.user.ui.AccountLoginModel;
import com.kaolafm.kradio.user.ui.AccountLoginPresenter;
import com.kaolafm.kradio.user.ui.IAccountLoginView;
import com.kaolafm.opensdk.api.login.model.UserInfo;

import java.lang.ref.WeakReference;

/**
 * <pre>
 *     <AUTHOR> Wenchl
 *     e-mail : <EMAIL>
 *     time   : 2019/03/06
 *     desc   :
 *     version: 1.0
 * </pre>
 */
public class AccountLoginFragment extends BaseFragment<AccountLoginPresenter> implements IAccountLoginView {

    private static final String TAG = "THIRD_AccountLoginFragment";

    private static final String KEY_TYPE_QR = "key_type_qr";

    private int mQrType = AccountLoginModel.TYPE_QR_YT;

    TextView tvLoginTitle;
    ImageView ivLoginQr;
    TextView tvLoginDownloadApp;

    LinearLayout userLoginLayout;
    View no_network_view;
    ConstraintLayout mRootLayout;

    LinearLayout userThirdPlatformView;
    TextView qrErrorMsg;
    ImageView loadingView;

    View occlusionView;
    TextView tvLoginGxy;

    NetWorkListener mNetWorkListener;
    ObjectAnimator animator;

    public static AccountLoginFragment newInstance(int qrType){
        AccountLoginFragment accountLoginFragment = new AccountLoginFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(KEY_TYPE_QR, qrType);
        accountLoginFragment.setArguments(bundle);
        return accountLoginFragment;
    }

    @Override
    public void initArgs() {
        super.initArgs();
        Bundle bundle = getArguments();
        if (bundle != null) {
            mQrType = bundle.getInt(KEY_TYPE_QR, AccountLoginModel.TYPE_QR_YT);
        } else {
            mQrType = AccountLoginModel.TYPE_QR_YT;
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        NetworkManager.getInstance().removeNetworkReadyListener(mNetWorkListener);
        if (animator != null){
            animator.cancel();
            animator = null;
        }
    }

    @Override
    protected int getLayoutId() {
        return R.layout.fragment_qr_login;
    }

    @Override
    protected int getLayoutId_Tow() {
        return 0;
    }

    @Override
    protected AccountLoginPresenter createPresenter() {
        return new AccountLoginPresenter(this, mQrType);
    }

    @Override
    public void initView(View view) {
        tvLoginTitle= view.findViewById(R.id.tv_login_title);
        ivLoginQr= view.findViewById(R.id.iv_login_qr);
        tvLoginDownloadApp= view.findViewById(R.id.tv_login_download_app);
        userLoginLayout= view.findViewById(R.id.user_qr_layout);
        no_network_view= view.findViewById(R.id.user_no_network_rel);
        mRootLayout= view.findViewById(R.id.user_account_main_layout);
        userThirdPlatformView= view.findViewById(R.id.user_third_platform_view);
        qrErrorMsg= view.findViewById(R.id.tv_error_msg);
        loadingView= view.findViewById(R.id.network_nosigin);
        occlusionView = view.findViewById(R.id.occlusionView);
        tvLoginGxy = view.findViewById(R.id.tv_login_gxy);
        tvLoginGxy.setOnClickListener(v -> {
            showGxyQrView();
        });

        initNetworkListener();
        no_network_view.setOnClickListener(v->{
            if (AntiShake.check(v.getId())) return;
            mPresenter.getData();
        });
    }

    private void showGxyQrView(){
        boolean isUserLoginActivity = getActivity() instanceof UserLoginActivity;
        Log.i(TAG, "showGxyQrView() --- isUserLoginActivity=" + isUserLoginActivity);
        if (isUserLoginActivity){
            ((UserLoginActivity) getActivity()).showGxyQrView();
            ViewUtil.setViewVisibility(occlusionView, View.VISIBLE);
        }
    }

    public void onGxyQrViewHided(){
        Log.i(TAG, "onGxyQrViewHided()");
        ViewUtil.setViewVisibility(occlusionView, View.GONE);
        if (mPresenter != null) {
            mPresenter.getData();
        }
    }

    @Override
    public void showQRCode(String url) {
        ImageLoader.getInstance().displayImage(getContext(), url, ivLoginQr,
                ResUtil.getDrawable(R.drawable.user_qr_error_bg), new OnImageLoaderListener() {
                    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
                    @Override
                    public void onLoadingFailed(String url, ImageView target, Exception exception) {
                        setQrCompleted(true);
                        showNoNetWork();
                    }

                    @Override
                    public void onLoadingComplete(String url, ImageView target) {
                        setQrCompleted(true);
                        ViewUtil.setViewVisibility(userLoginLayout, View.VISIBLE);
                        ViewUtil.setViewVisibility(userThirdPlatformView, View.GONE);
                        ViewUtil.setViewVisibility(no_network_view, View.GONE);
                        no_network_view.setContentDescription("");
                    }
                });


    }

    @Override
    public void showBindSuccess(UserInfo userInfo) {
        UserLoginActivity fragment = (UserLoginActivity)getActivity();
        if (fragment != null) {
            fragment.showLoginSuccessToast();
            fragment.loginSuccess(userInfo);
            Intent intent = new Intent().setAction("com.yunting.login");
            intent.putExtra("isLogin", true);
            AppDelegate.getInstance().getContext().sendBroadcast(intent);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (UserInfoManager.getInstance().isQrScanned() && mPresenter != null) {
            mPresenter.checkQRCodeStatusAndInfo();
            return;
        }
        if (mPresenter != null) {
            mPresenter.refreshData();
        }
    }

    @Override
    public void onSupportInvisible() {
        super.onSupportInvisible();
        if (mPresenter != null) {
            mPresenter.cancelCheck();
        }
    }

    @Override
    public void onSupportVisible() {
        super.onSupportVisible();
        if (UserInfoManager.getInstance().isQrScanned() && mPresenter != null) {
            mPresenter.checkQRCodeStatusAndInfo();
            return;
        }
        if (mPresenter != null) {
            mPresenter.getData();
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    @Override
    public void showNoNetWork() {
//        ViewUtil.setViewVisibility(userLoginLayout, View.INVISIBLE);
        ViewUtil.setViewVisibility(no_network_view, View.VISIBLE);
        no_network_view.setContentDescription(ResUtil.getString(R.string.content_desc_refresh));
        ToastUtil.showInfo(getContext(), ResUtil.getString(R.string.network_error_toast));
        qrErrorMsg.setText(ResUtil.getString(R.string.user_qr_error_no_network_msg));
        AnimUtil.stopAnimation(animator);
    }

    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    @Override
    public void showQrExpire() {
//        ViewUtil.setViewVisibility(userLoginLayout, View.INVISIBLE);
        ViewUtil.setViewVisibility(no_network_view, View.VISIBLE);
        no_network_view.setContentDescription(ResUtil.getString(R.string.content_desc_refresh));
        //ToastUtil.showOnActivity(getContext(), ResUtil.getString(R.string.user_qr_expire_toast));
        qrErrorMsg.setText(ResUtil.getString(R.string.user_qr_expire_msg));
        AnimUtil.stopAnimation(animator);
//        ((UserLoginFragment) getParentFragment()).goToLoginView();
        ((UserLoginActivity) getActivity()).goToLoginView();
    }

    @Override
    public void showQrScaned(String avatar, String name) {
//        ((UserLoginFragment) getParentFragment()).userQrScanned(avatar, name);
        ((UserLoginActivity) getActivity()).userQrScanned(avatar, name);
    }

    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    @Override
    public void showLoading() {
        ViewUtil.setViewVisibility(no_network_view, View.VISIBLE);
        no_network_view.setContentDescription("");
        qrErrorMsg.setText(ResUtil.getString(R.string.user_qr_loading));
        if (animator == null) {
            animator = AnimUtil.startRotation(loadingView, 500);
        } else {
            animator.resume();
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    @Override
    public void hideLoading() {
        AnimUtil.stopAnimation(animator);
    }

    @Override
    public void showIntercepterView(View view) {
        userThirdPlatformView.removeAllViews();
        userThirdPlatformView.addView(view);
        userThirdPlatformView.setVisibility(View.VISIBLE);
        userLoginLayout.setVisibility(View.INVISIBLE);
        ViewUtil.setViewVisibility(no_network_view, View.GONE);
        no_network_view.setContentDescription("");
    }

    private void uploadView(boolean isLand) {
//        ConstraintSet set = new ConstraintSet();
//        set.clone(mRootLayout);
//        if (isLand) {
//            set.setVerticalBias(userLoginLayout.getId(), 0.8f);
//            set.setHorizontalBias(userLoginLayout.getId(), 1);
//
//            set.setVerticalBias(no_network_view.getId(), 0.8f);
//            set.setHorizontalBias(no_network_view.getId(), 1);
//
//            set.setHorizontalBias(tvLoginDownloadApp.getId(), 1);
//        } else {
//            set.setHorizontalBias(userLoginLayout.getId(), 0.5f);
//            set.setHorizontalBias(no_network_view.getId(), 0.5f);
//            set.setHorizontalBias(tvLoginDownloadApp.getId(), 0.5f);
//        }
//        set.applyTo(mRootLayout);
    }


    @Override
    protected void showAccordingToScreen(int orientation) {
        super.showAccordingToScreen(orientation);
        int paddingLeft = ScreenUtil.getGlobalPaddingLeft(orientation);
        int paddingRight = ScreenUtil.getGlobalPaddingRight(orientation);

        //mRootLayout.setPadding(paddingLeft, 0, paddingRight, 0);
        //
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            uploadView(true);
        } else {
            uploadView(false);
        }
        if (UserInfoManager.getInstance().isQrScanned() && mPresenter != null) {
            mPresenter.checkQRCodeStatusAndInfo();
            return;
        }
        if (mPresenter != null) {
            mPresenter.refreshData();
        }
    }

    @Override
    protected void changeViewLayoutForStatusBar(View view) {
    }

    @Override
    protected void addFragmentRootViewPadding(View view) {
    }

    private void initNetworkListener() {
        if (UserInfoManager.getInstance().isUserBound()) {
            return;
        }

        mNetWorkListener = new NetWorkListener(this);
        NetworkManager.getInstance().addNetworkReadyListener(mNetWorkListener);

    }

    // 当二维码刷新成功时才允许qrscanlayout显示，用于解决ZMKQ-5326
    public void setQrCompleted(boolean flag) {
        if (getActivity() != null && getActivity() instanceof UserLoginActivity) {
            ((UserLoginActivity) getActivity()).setNeedShowQrScan(flag);
        }
    }

    public static class NetWorkListener implements NetworkManager.INetworkReady {
        private WeakReference accountLoginWeakReference;

        public NetWorkListener(AccountLoginFragment accountLoginFragment) {
            accountLoginWeakReference = new WeakReference<>(accountLoginFragment);
        }

        @Override
        public void networkChange(boolean hasNetwork) {
            if (!hasNetwork) {
                return;
            }
            AccountLoginFragment accountLoginFragment = (AccountLoginFragment) accountLoginWeakReference.get();
            if (accountLoginFragment == null) {
                return;
            }
            accountLoginFragment.mPresenter.getData();
        }
    }
}
