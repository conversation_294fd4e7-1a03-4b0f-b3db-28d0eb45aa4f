package com.kaolafm.kradio.clientControlerForKradio;

import android.os.RemoteException;
import android.util.Log;

import com.google.gson.Gson;
import com.kaolafm.kradio.user.UserInfoManager;
import com.kaolafm.opensdk.account.token.AccessTokenManager;
import com.kaolafm.opensdk.account.token.KaolaAccessToken;
import com.kaolafm.opensdk.account.token.TingbanTokenObserver;
import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.sdk.core.IExecuteResult;
import com.kaolafm.sdk.core.ex.ErrorCode;
import com.kaolafm.sdk.core.ex.bean.UserInfo;
import com.kaolafm.sdk.core.ex.cmd.BindListenerCmd;

import java.util.ArrayList;
import java.util.Iterator;

/**
 * <pre>
 *     <AUTHOR> Wenchl
 *     e-mail : <EMAIL>
 *     time   : 2019/07/16
 *     desc   :
 *     version: 1.0
 * </pre>
 */
public class ClientAccountLoginHelp {

    private String TAG = "client.ClientAccountLoginHelp";

    private ArrayList<IExecuteResult> mBindResults = new ArrayList<>();

    private Gson mGson;

    private String token = null;


    public ClientAccountLoginHelp(Gson gson) {
        mGson = gson;
    }

    public void init() {
        Log.i(TAG, "init start");
        UserInfoManager.getInstance().addUserInfoStateListener(iUserInfoStateListener);
        AccessTokenManager.getInstance().registerObserver(iTingbanTokenObserver);
    }

    public void setBindResult(IExecuteResult iExecuteResult) {
        if (!mBindResults.contains(iExecuteResult)) {
            mBindResults.add(iExecuteResult);
        }
        Log.i(TAG, "setBindResult:" + iExecuteResult);
    }

    public UserInfo getUserInfoFromNet() {
        UserInfo userInfo = new UserInfo();
        //
        UserInfoManager userInfoManager = UserInfoManager.getInstance();
        userInfo.setNickName(userInfoManager.getUserNickName());
        userInfo.setAvatar(userInfoManager.getUserFavicon());
        userInfo.setUserId(userInfoManager.getUserId());
        //
        KaolaAccessToken accessToken = AccessTokenManager.getInstance().getKaolaAccessToken();
        userInfo.setToken(accessToken.getAccessToken());
        userInfo.setRefreshToken(accessToken.getRefreshToken());
        Log.i(TAG, "getUserInfoFromNet: " + userInfo.toString());

        return userInfo;
    }

    private TingbanTokenObserver iTingbanTokenObserver = new TingbanTokenObserver() {
        @Override
        public void onChange(KaolaAccessToken kaolaAccessToken) {
            Logging.d("token发生改变, token=%s", kaolaAccessToken);
            if (mBindResults.size() == 0 || kaolaAccessToken == null) {
                return;
            }

            //第一次不通知，与bind 重复
            if (token == null) {
                token = kaolaAccessToken.getAccessToken();
                return;
            }

            //如果token相同就不通知cliensdk
            if (token.equals(kaolaAccessToken.getAccessToken())) {
                return;
            }

            token = kaolaAccessToken.getAccessToken();

            BindListenerCmd.Result bind = new BindListenerCmd.Result();
            bind.state = BindListenerCmd.Result.UPDATE_TOKEN;
            bind.setCode(ErrorCode.Success);
            bind.setUserInfo(getUserInfoFromNet());
//            try {
//                bindResult.onResult(mGson.toJson(bind));
//            } catch (RemoteException e) {
//                e.printStackTrace();
//            }
            notifyLoginStatus(mGson.toJson(bind));
        }
    };

    private UserInfoManager.IUserInfoStateListener iUserInfoStateListener = new UserInfoManager.IUserInfoStateListener() {

        @Override
        public void userLogin() {
            Log.i(TAG, "userLogin:" + mBindResults);
            if (mBindResults.size() == 0) {
                return;
            }
            BindListenerCmd.Result bind = new BindListenerCmd.Result();
            bind.state = BindListenerCmd.Result.BIND;
            bind.setCode(ErrorCode.Success);
            bind.setUserInfo(getUserInfoFromNet());
//            try {
//                bindResult.onResult(mGson.toJson(bind));
//            } catch (RemoteException e) {
//                e.printStackTrace();
//            }
            notifyLoginStatus(mGson.toJson(bind));
        }

        @Override
        public void userLogout() {
            Log.i(TAG, "userLogout:" + mBindResults);
            if (mBindResults.size() == 0) {
                return;
            }
            BindListenerCmd.Result bind = new BindListenerCmd.Result();
            bind.state = BindListenerCmd.Result.UNBIND;
            bind.setCode(ErrorCode.Success);
//            try {
//                bindResult.onResult(mGson.toJson(bind));
//            } catch (RemoteException e) {
//                e.printStackTrace();
//            }
            notifyLoginStatus(mGson.toJson(bind));
        }

        @Override
        public void userCancel() {
        }
    };

    private void notifyLoginStatus(String result) {
        for (Iterator<IExecuteResult> it = mBindResults.iterator(); it.hasNext(); ) {
            IExecuteResult bindResult = it.next();
            Log.i(TAG, "notifyLoginStatus = " + bindResult);
            try {
                bindResult.onResult(result);
            } catch (RemoteException e) {
                e.printStackTrace();
                it.remove();
            }
        }
    }
}
