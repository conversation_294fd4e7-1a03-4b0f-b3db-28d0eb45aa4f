package com.kaolafm.kradio.flavor.impl;

import android.annotation.SuppressLint;
import android.content.Context;
import android.media.AudioManager;
import android.util.Log;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioRequestAudioFocusInter;
import com.kaolafm.opensdk.player.logic.PlayerManager;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-04-13 15:27
 ******************************************/
public class KRadioRequestAudioFocusImpl implements KRadioRequestAudioFocusInter {
    private static final String TAG = "KRadioRequestAudioFocusImpl";

    @SuppressLint("LongLogTag")
    @Override
    public boolean requestAudioFocusBySelf(Object... args) {
//        PlayerManager playerManager = PlayerManager.getInstance();
//        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001415344389?userId=2169710问题
//        if (playerManager.isPlayerInitSuccess()) {
//            Log.i(TAG, "initThirdPlatform------>start isAppForeground");
//            requestAudioFocus();
//        } else {
//            playerManager.addPlayerInitCompleteListener(new OnPlayerInitCompleteClazz(this));
//            playerManager.setupPlayer();
//        }

        boolean flag = PlayerManager.getInstance().isPlayerInitSuccess();
        if (flag) {
            PlayerManager.getInstance().requestAudioFocus();
        } else {
            AudioManager audioManager = (AudioManager) AppDelegate.getInstance().getContext().getSystemService(Context.AUDIO_SERVICE);
            int result = audioManager.requestAudioFocus(mOnAudioFocusChangeListener, AudioManager.STREAM_MUSIC,
                    AudioManager.AUDIOFOCUS_GAIN);
            Log.i(TAG, "requestAudioFocusBySelf result = " + result);
            if (result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED) {
                PlayerManager.getInstance().notifyAudioFocus(AudioManager.AUDIOFOCUS_GAIN);
            }
        }
        return true;
    }

    private AudioManager.OnAudioFocusChangeListener mOnAudioFocusChangeListener = new AudioManager.OnAudioFocusChangeListener() {
        @SuppressLint("LongLogTag")
        @Override
        public void onAudioFocusChange(int focusChange) {
            Log.i(TAG, "onAudioFocusChange----focusChange = " + focusChange);
        }
    };

//    private static class OnPlayerInitCompleteClazz implements VLCMediaPlayClient.OnPlayerInitCompleteListener {
//        private WeakReference<KRadioRequestAudioFocusImpl> weakReference;
//
//        public OnPlayerInitCompleteClazz(KRadioRequestAudioFocusImpl kRadioRequestAudioFocusImpl) {
//            weakReference = new WeakReference<>(kRadioRequestAudioFocusImpl);
//        }
//
//        @Override
//        public void onPlayerInitComplete(boolean b) {
//            KRadioRequestAudioFocusImpl kRadioRequestAudioFocusImpl = weakReference.get();
//            if (kRadioRequestAudioFocusImpl != null) {
//                kRadioRequestAudioFocusImpl.requestAudioFocus();
//            }
//        }
//    }
//
//    private void requestAudioFocus() {
//        boolean isAppForeground = AppDelegate.getInstance().isAppForeground();
//        Log.i(TAG, "requestAudioFocus------>isAppForeground = " + isAppForeground);
//        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001495879559?userId=1545533问题
//        if (isAppForeground) {
//            AudioStatusManager.getInstance().requestAudioFocus();
//        }
//    }
}