package com.kaolafm.kradio.user.channel;

import com.kaolafm.kradio.lib.base.mvp.BaseModel;
import com.kaolafm.opensdk.http.core.HttpCallback;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/4/19
 */
class SwitchChannelModel extends BaseModel {

    @Override
    public void destroy() {

    }

    public void getChannels(HttpCallback<List<Channel>> callback) {
        new ChannelRequest().getChannelList(callback);
    }
}
