package com.kaolafm.kradio.lib.widget.ratio;

import android.content.Context;
import android.content.res.TypedArray;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import com.kaolafm.kradio.lib.R;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;

/**
 * <AUTHOR>
 * @date 2019-08-06
 */
public class RatioLinearLayout extends LinearLayout {

    private double mHeightRatio;

    private double mWidthRatio;

    public RatioLinearLayout(Context context) {
        this(context, null);
    }

    public RatioLinearLayout(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public RatioLinearLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    private void init(Context context, AttributeSet attrs) {
        TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.RatioLinearLayout);
        String ratio = ta.getString(R.styleable.RatioLinearLayout_wh_ratio);
        setRatio(ratio);
        ta.recycle();
    }

    @Override
    public ViewGroup.LayoutParams getLayoutParams() {
        ViewGroup.LayoutParams layoutParams = super.getLayoutParams();
        ViewUtil.switchWH(layoutParams, ResUtil.getOrientation());
        return layoutParams;
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        setMeasuredDimension(getDefaultSize(0, widthMeasureSpec), getDefaultSize(0, heightMeasureSpec));
        int[] specs = ViewUtil.measureByRatio(this, mWidthRatio, mHeightRatio, widthMeasureSpec, heightMeasureSpec);
        super.onMeasure(specs[0], specs[1]);
    }

    public void setRatio(String ratio) {
        if (!TextUtils.isEmpty(ratio)) {
            String[] wh = ratio.split("\\:");
            if (wh.length == 2) {
                mWidthRatio = Double.valueOf(wh[0]);
                mHeightRatio = Double.valueOf(wh[1]);
            }
        }
    }
}
