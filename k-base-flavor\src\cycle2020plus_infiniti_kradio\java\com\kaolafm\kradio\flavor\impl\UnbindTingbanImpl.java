package com.kaolafm.kradio.flavor.impl;


import android.util.Log;

import com.kaolafm.kradio.lib.base.flavor.UnbindTingban;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

/**
 * 使用二维码登录前拦截
 *
 * <AUTHOR>
 **/
public class UnbindTingbanImpl implements UnbindTingban {
    private static final String TAG = "UnbindTingbanImpl";

    @Override
    public void unbindTingban(Object... args) {
        Log.i(TAG, "unbindTingban start");
        new UserLoginModel().logout(new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                Log.i(TAG, "unbindTingban onSuccess aBoolean = " + aBoolean);
                if (aBoolean) {
                    UserInfoManager.getInstance().logout();
                    if (args != null && args.length > 0) {
                        ((AccountLoginModel.AccountCallBack) args[0]).onUnbindSuccess(true);
                    }
                }
            }

            @Override
            public void onError(ApiException e) {
                Log.i(TAG, "unbindTingban onError = " + e);
            }
        });
    }
}