package com.kaolafm.kradio.online.categories.broadcast;

import android.util.TypedValue;
import android.view.View;
import android.widget.TextView;

import com.kaolafm.kradio.common.SubcategoryItemBean;
import com.kaolafm.kradio.k_kaolafm.R; 
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.utils.ResUtil;
 

/**
 * 分类广播
 *
 * <AUTHOR>
 * @date 2018/5/9
 */

public class BroadcastCategoryViewHolder extends BaseHolder<SubcategoryItemBean> {
    
    TextView mTvBroadcastCategoryName;

    public BroadcastCategoryViewHolder(View itemView) {
        super(itemView);
        mTvBroadcastCategoryName=itemView.findViewById(R.id.tv_broadcast_category_name);
    }

    @Override
    public void setupData(SubcategoryItemBean subcategoryItemBean, int position) {
        mTvBroadcastCategoryName.setText(subcategoryItemBean.getName());
        mTvBroadcastCategoryName.setTextSize(TypedValue.COMPLEX_UNIT_PX, ResUtil.getDimen(R.dimen.online_subcategory_item_broadcast_category_text_size));
    }
}
