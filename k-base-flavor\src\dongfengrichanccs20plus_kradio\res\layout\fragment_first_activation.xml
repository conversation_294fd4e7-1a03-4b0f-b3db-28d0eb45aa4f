<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/activation_main_layout"
    android:layout_marginTop="@dimen/y100"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ViewStub
            android:id="@+id/avition_back_home_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout="@layout/layout_avition_left_navbar" />


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignLeft="@id/avition_back_home_layout"
            android:layout_marginBottom="@dimen/y155"
            android:orientation="vertical"
            android:paddingLeft="@dimen/x60"
            android:paddingTop="@dimen/y29"
            android:paddingRight="@dimen/x55">

            <TextView
                android:id="@+id/tv_activation_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:text="@string/activation_tingban"
                android:textColor="@color/colorWhite"
                android:textSize="@dimen/text_size12" />

            <androidx.core.widget.NestedScrollView
                android:id="@+id/myNestedScrollView"
                style="@style/scroll_view_bar_style"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/x14"
                android:scrollbars="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/tv_activation_content"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="@dimen/x5"
                        android:text="@string/activation_content"
                        android:textColor="@color/colorWhite"
                        android:textSize="@dimen/text_size7" />
                </LinearLayout>
            </androidx.core.widget.NestedScrollView>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginTop="@dimen/y5"
            android:orientation="vertical"
            android:paddingBottom="@dimen/y25">

            <LinearLayout
                android:id="@+id/activationUserProtocol"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal">

                <ImageView
                    android:id="@+id/activationUserProtocolChoose"
                    android:layout_width="@dimen/m40"
                    android:layout_height="@dimen/m40"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/activation_checkbox" />

                <RelativeLayout
                    android:id="@+id/activationUserProtocolPSLayout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:padding="@dimen/m10">

                    <TextView
                        android:id="@+id/activationUserProtocolPS"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:text="@string/activation_protocol_title_str"
                        android:textColor="@color/colorWhite"
                        android:textSize="@dimen/text_size5" />

                </RelativeLayout>

            </LinearLayout>


            <TextView
                android:id="@+id/tv_activation_activate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal|bottom"
                android:background="@drawable/activation_start_btn"
                android:enabled="true"
                android:focusable="@bool/is_focusable"
                android:paddingLeft="@dimen/x50"
                android:paddingTop="@dimen/y11"
                android:paddingRight="@dimen/x50"
                android:paddingBottom="@dimen/y11"
                android:text="@string/activation_start"
                android:textColor="@color/selector_activation_text_color"
                android:textSize="@dimen/text_size9" />


        </LinearLayout>

        <!--不使用的布局  这里设置为gone  保留是防止编译的时候找不到相应的资源报错-->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignLeft="@id/avition_back_home_layout"
            android:orientation="vertical"
            android:visibility="gone">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1">

                <ImageView
                    android:id="@+id/activationLogo"
                    android:layout_width="@dimen/m300"
                    android:layout_height="@dimen/activation_logo_height"
                    android:layout_centerInParent="true"
                    android:scaleType="fitCenter"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/activationLogoText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/activationLogo"
                    android:layout_centerInParent="true"
                    android:gravity="center_horizontal"
                    android:text="@string/activation_k_radio_normal_str"
                    android:textColor="@color/text_color_1"
                    android:textSize="@dimen/text_size12" />

                <TextView
                    android:id="@+id/activationTitle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/activationLogoText"
                    android:layout_margin="@dimen/y21"
                    android:gravity="center_horizontal"
                    android:text="@string/activation_k_radio_normal_title_str"
                    android:textColor="@color/text_anivation_color"
                    android:textSize="@dimen/text_size7" />

            </RelativeLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/y90"
                android:layout_weight="1">

                <LinearLayout
                    android:id="@+id/activationUserProtocol"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:paddingTop="@dimen/y10"
                    android:paddingBottom="@dimen/y30">

                    <ImageView
                        android:id="@+id/activationUserProtocolChoose"
                        android:layout_width="@dimen/m52"
                        android:layout_height="@dimen/m52"
                        android:layout_gravity="center_vertical"
                        android:layout_marginTop="@dimen/m3"
                        android:padding="@dimen/m10"
                        android:src="@drawable/activation_checkbox" />

                    <RelativeLayout
                        android:id="@+id/activationUserProtocolPSLayout"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:padding="@dimen/m10">

                        <TextView
                            android:id="@+id/activationUserProtocolPS"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:text="@string/activation_protocol_title_str"
                            android:textSize="@dimen/text_size5" />

                        <View
                            android:id="@+id/activationUserProtocolShadow"
                            android:layout_width="wrap_content"
                            android:layout_height="@dimen/y1"
                            android:layout_alignStart="@+id/activationUserProtocolPS"
                            android:layout_alignEnd="@+id/activationUserProtocolPS"
                            android:layout_alignBottom="@+id/activationUserProtocolPS" />
                    </RelativeLayout>

                </LinearLayout>

                <TextView
                    android:id="@+id/activationBtn"
                    android:layout_width="@dimen/x400"
                    android:layout_height="@dimen/y80"
                    android:layout_below="@id/activationUserProtocol"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/activation_btn_selector"
                    android:gravity="center"
                    android:text="@string/activation_str"
                    android:textColor="@color/text_color_1"
                    android:textSize="@dimen/text_size7" />

            </RelativeLayout>
        </LinearLayout>

        <ViewStub
            android:id="@+id/activation_home_button_viewStub"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout="@layout/activation_home_button" />

        <fr.castorflex.android.circularprogressbar.CircularProgressBar
            android:id="@+id/activation_loading_view"
            style="@style/CustomerCircularProgressBar"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_centerInParent="true"
            android:visibility="gone"
            app:cpb_color="@color/circular_progress_color" />
    </RelativeLayout>
</RelativeLayout>


