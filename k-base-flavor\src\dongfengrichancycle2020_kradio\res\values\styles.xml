<resources>

    <style name="scroll_view_bar_style">
        <item name="android:scrollbarThumbVertical">@drawable/shape_scroll_bar</item>
        <item name="android:scrollbars">vertical</item>
        <item name="android:scrollbarTrackVertical">@drawable/shape_scroll_bar_track_bg</item>
    </style>

<!--    https://app.huoban.com/tables/2100000007530121/items/2300001678199825?userId=1545533-->
    <style name="Animation">
        <item name="android:activityOpenEnterAnimation">@null</item>
        <item name="android:activityOpenExitAnimation">@null</item>
        <item name="android:activityCloseEnterAnimation">@null</item>
        <item name="android:activityCloseExitAnimation">@null</item>
        <item name="android:taskOpenEnterAnimation">@null</item>
        <item name="android:taskOpenExitAnimation">@null</item>
        <item name="android:taskCloseEnterAnimation">@null</item>
        <item name="android:taskCloseExitAnimation">@null</item>
        <item name="android:taskToFrontEnterAnimation">@null</item>
        <item name="android:taskToFrontExitAnimation">@null</item>
        <item name="android:taskToBackEnterAnimation">@null</item>
        <item name="android:taskToBackExitAnimation">@null</item>
    </style>

    <style name="AppTheme" parent="Theme.AppCompat.NoActionBar">
        <!-- 设置无标题-->
        <!--<item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>//无ActionBar
        <item name="android:windowFullscreen">@bool/isWindowFullScreen</item>//全屏即无通知栏
        <item name="android:windowContentOverlay">@null</item>//是否有遮盖
        <item name="android:windowDisablePreview">false</item>-->
        <item name="android:windowAnimationStyle">@style/Animation</item>
    </style>

    <style name="AppThemeCompat" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowNoTitle">true</item>//无标题
        <item name="android:windowActionBar">false</item>//无ActionBar
        <item name="android:windowFullscreen">@bool/isWindowFullScreen</item>//全屏即无通知栏
        <item name="android:windowContentOverlay">@null</item>//是否有遮盖
        <item name="android:windowBackground">@drawable/kradio_splash</item>
<!--        <item name="android:windowIsTranslucent">true</item>-->
    </style>

    <style name="AppThemeCompat.splash" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowNoTitle">true</item>//无标题
        <item name="android:windowActionBar">false</item>//无ActionBar
        <item name="android:windowFullscreen">@bool/isWindowFullScreen</item>//全屏即无通知栏
        <item name="android:windowContentOverlay">@null</item>//是否有遮盖
        <item name="android:windowBackground">@drawable/kradio_splash</item>
        <item name="android:windowAnimationStyle">@style/Animation</item>
    </style>

    <style name="AlertDialog" parent="@style/Theme.AppCompat.Light.Dialog">
        <!-- 这里设置背景为透明，为了隐藏边框 -->
        <item name="android:windowBackground">@color/colorBlack</item>
        <item name="android:windowNoTitle">true</item>
        <!-- 这里是修改顶部标题背景颜色，具体颜色自己定，可以是图片 -->
        <item name="android:topDark">@color/colorBlack</item>
        <!-- 这里是修改内容区域背景颜色 -->
        <item name="android:centerDark">@color/colorBlack</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="buttonBarPositiveButtonStyle">@style/positive</item>
        <item name="buttonBarNegativeButtonStyle">@style/negative</item>
    </style>

    <style name="positive">
        <item name="android:textColor">@color/colorAccent</item>
    </style>
    <style name="negative">
        <item name="android:textColor">@color/colorAccent</item>
    </style>
</resources>