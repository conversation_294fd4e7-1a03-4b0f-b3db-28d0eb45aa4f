<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/colorTransparent">

    <!--添加占位符，解决沉浸式状态栏的问题-->
    <View
        android:id="@+id/placeholder"
        android:layout_width="0dp"
        android:layout_height="@dimen/y60"
        android:background="@color/colorTransparent" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/placeholder"
        android:background="@color/color16"
        android:paddingLeft="@dimen/x40"
        android:paddingTop="@dimen/y40"
        android:paddingRight="@dimen/x40"
        android:paddingBottom="@dimen/y40">

        <ImageView
            android:id="@+id/flow_iv1"
            android:layout_width="@dimen/m56"
            android:layout_height="@dimen/m56"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:src="@drawable/scene_cover_default" />

        <LinearLayout
            android:id="@+id/btns"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btnCancle"
                android:layout_width="@dimen/x106"
                android:layout_height="@dimen/y56"
                android:layout_centerVertical="true"
                android:background="@drawable/notify_btn_bg"
                android:gravity="center"
                android:text="取消"
                android:textColor="@color/player_subscribe_text_selector"
                android:textSize="@dimen/text_size5" />

            <Button
                android:id="@+id/btnSure"
                android:layout_width="@dimen/x106"
                android:layout_height="@dimen/y56"
                android:layout_centerVertical="true"
                android:layout_marginLeft="@dimen/m10"
                android:layout_toRightOf="@+id/flow_iv2"
                android:background="@drawable/notify_btn_bg"
                android:gravity="center"
                android:text="确定"
                android:textColor="@color/player_subscribe_text_selector"
                android:textSize="@dimen/text_size5" />

        </LinearLayout>

        <com.kaolafm.kradio.lib.widget.AutoMarqueenTextView
            android:id="@+id/flow_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="@dimen/x30"
            android:layout_marginRight="@dimen/x30"
            android:layout_toLeftOf="@+id/btns"
            android:layout_toRightOf="@+id/flow_iv1"
            android:ellipsize="marquee"
            android:singleLine="true"
            android:textColor="#FFFFFF"
            android:textSize="@dimen/m28"
            tools:text="@string/app_name" />
    </RelativeLayout>
</RelativeLayout>

