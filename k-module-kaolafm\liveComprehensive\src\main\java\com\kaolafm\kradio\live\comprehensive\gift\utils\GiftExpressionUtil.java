package com.kaolafm.kradio.live.comprehensive.gift.utils;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.style.ImageSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.kradio.live.comprehensive.gift.adapter.GiftGVAdapter;
import com.kaolafm.kradio.live.comprehensive.gift.adapter.GiftGVDividerItemDecoration;
import com.kaolafm.kradio.live.comprehensive.gift.ui.AnimatedGifDrawable;
import com.kaolafm.kradio.live.comprehensive.gift.ui.AnimatedImageSpan;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.opensdk.api.live.model.Gift;
import com.kaolafm.opensdk.api.live.model.GiftsResult;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by Ren on 2023/2/9.
 */
public class GiftExpressionUtil {
    public static String ASSETS_ROOT = "p/";
    public static String ASSETS_GIF_ROOT = "g/";
    private boolean isNetData = false;//是否来自网络数据

    public GiftExpressionUtil(){

    }

//    public GiftExpressionUtil(boolean isNetData){
//        this.isNetData = isNetData;
//    }

    /**
     * 从静态图找到对应的动态图，为了方便此处我动态图和静态图名称保持一致
     *
     * @param mContext
     * @param gifTextView
     * @param content
     * @return
     */
    public SpannableStringBuilder prase(Context mContext, final TextView gifTextView, String content) {
        SpannableStringBuilder sb = new SpannableStringBuilder(content);
        String regex = "\\[[^\\]]+\\]";
        Pattern p = Pattern.compile(regex);
        Matcher m = p.matcher(content);
        while (m.find()) {
            String tempText = m.group();
            try {
                String num = tempText.substring(("[" + ASSETS_ROOT).length(), tempText.length() - ".png]".length());
                String gif = ASSETS_GIF_ROOT + num + ".gif";
                /**
                 * 如果open这里不抛异常说明存在gif，则显示对应的gif
                 * 否则说明gif找不到，则显示png\\[[^\\]]+\\]
                 * */
                InputStream is = mContext.getAssets().open(gif);
                sb.setSpan(
                        new AnimatedImageSpan(
                                new AnimatedGifDrawable(is, new AnimatedGifDrawable.UpdateListener() {
                                    // CPU优化：添加更新节流，避免过于频繁的UI刷新
                                    private long lastUpdateTime = 0;
                                    private static final long UPDATE_THROTTLE_MS = 50; // 50ms节流

                                    @Override
                                    public void update() {
                                        long currentTime = System.currentTimeMillis();
                                        if (currentTime - lastUpdateTime >= UPDATE_THROTTLE_MS) {
                                            lastUpdateTime = currentTime;
                                            gifTextView.postInvalidate();
                                        }
                                    }
                                })),
                        m.start(),
                        m.end(),
                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                is.close();
            } catch (Exception e) {//没找到对应的GIF，显示静态图
                String png = tempText.substring("[".length(), tempText.length() - "]".length());
                try {
                    sb.setSpan(
                            new ImageSpan(mContext, BitmapFactory.decodeStream(mContext.getAssets().open(png))),
                            m.start(),
                            m.end(),
                            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
                e.printStackTrace();
            }
        }
        return sb;
    }

    public SpannableStringBuilder getFace(Context mContext, String content) {
        SpannableStringBuilder sb = new SpannableStringBuilder();
        try {
            /**
             * 经过测试，虽然这里tempText被替换为png显示，但是但我单击发送按钮时，获取到輸入框的内容是tempText的值而不是png
             * 所以这里对这个tempText值做特殊处理
             * 格式：[face/png/f_static_000.png]，以方便判斷當前圖片是哪一個
             * */
            String tempText = "[" + content + "]";
            sb.append(tempText);
            sb.setSpan(
                    new ImageSpan(mContext, BitmapFactory.decodeStream(mContext.getAssets().open(content))),
                    sb.length() - tempText.length(),
                    sb.length(),
                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

        } catch (Exception e) {
            e.printStackTrace();
        }

        return sb;
    }

    public void setView(Context mContext, final View view, String content) {
        if (view != null && view instanceof ImageView) {//图片不显示GIF
            Bitmap bitmap = null;
            try {
                bitmap = BitmapFactory.decodeStream(mContext.getAssets().open(content));
            } catch (IOException e1) {
                e1.printStackTrace();
            }
            ((ImageView) view).setImageBitmap(bitmap);
        } else if (view != null && view instanceof TextView) {//文字可显示GIF
            TextView gifTextView = (TextView) view;
            String tempText = "[" + content + "]";
            SpannableStringBuilder sb = prase(mContext, gifTextView, tempText);
            gifTextView.setText(sb);
        }

    }

    /**
     * 横屏时显示
     * @param context
     * @param recyclerView
     *@param giftsResult  @return
     */
    public void giftView(final Context context, RecyclerView recyclerView, GiftsResult giftsResult){
        LinearLayoutManager layoutManager = new LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false);
        recyclerView.setLayoutManager(layoutManager);
        recyclerView.addItemDecoration(new GiftGVDividerItemDecoration(recyclerView.getContext(), layoutManager.getOrientation()));


        final GiftGVAdapter mGvAdapter = new GiftGVAdapter(recyclerView, giftsResult, context);
        recyclerView.setAdapter(mGvAdapter);

        // 单击表情执行的操作
        mGvAdapter.setOnItemClickListener(new GiftGVAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(View view, Gift gift, int position) {
                try {
//                    mGvAdapter.setSeclection(position);
//                    mGvAdapter.notifyDataSetChanged();
                    if (giftClickListener != null) {
                        giftClickListener.onClick(position, gift);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });

        mGvAdapter.setOnGiftGiveListener(new GiftGVAdapter.OnGiftGiveListener() {
            @Override
            public void onGiftGive(View view, Gift gift, int position) {
                try {
                    if (giftGiveListener != null) {
                        giftGiveListener.onGive(position, gift);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    /**
     * 竖屏时显示，每一页的数据
     *
     * @param context
     * @param position        第几页
     * @param giftsResult     表情集合
     * @param columns         列数
     * @param rows            行数
     * @param showView        View
     * @return
     */
    public View viewPagerItem(final Context context, int position, GiftsResult giftsResult, int columns, int rows, final View showView) {
        LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View layout = inflater.inflate(R.layout.comprehensive_goods_gridview, null);//表情布局

        RecyclerView recyclerView = (RecyclerView) layout.findViewById(R.id.chart_face_gv);
        GridLayoutManager girdLayoutManager = new GridLayoutManager(context, columns);
        recyclerView.setLayoutManager(girdLayoutManager);

        List<Gift> subList = new ArrayList<>();
        subList.addAll(giftsResult.getGiftList()
                .subList(position * (columns * rows - 0),
                        (columns * rows - 0) * (position + 1) > giftsResult.getGiftList()
                                .size() ? giftsResult.getGiftList().size() : (columns * rows - 0)
                                * (position + 1)));

        final GiftGVAdapter mGvAdapter = new GiftGVAdapter(recyclerView, giftsResult, context);
        recyclerView.setAdapter(mGvAdapter);
        // 单击表情执行的操作
        mGvAdapter.setOnItemClickListener(new GiftGVAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(View view, Gift gift, int position) {
                try {
                    setView(context, showView, gift.getGiftName());
//                    mGvAdapter.setSeclection(position);
//                    mGvAdapter.notifyDataSetChanged();
                    if (giftClickListener != null) {
                        giftClickListener.onClick(position, gift);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });

        mGvAdapter.setOnGiftGiveListener(new GiftGVAdapter.OnGiftGiveListener() {
            @Override
            public void onGiftGive(View view, Gift gift, int position) {
                try {
                    if (giftGiveListener != null) {
                        giftGiveListener.onGive(position, gift);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });

        return recyclerView;
    }

    public interface GiftClickListener {
        void onClick(int position, Gift gift);
    }

    private GiftClickListener giftClickListener;

    public void setGiftClickListener(GiftClickListener listener) {
        giftClickListener = listener;
    }

    public interface GiftGiveListener {
        void onGive(int position, Gift gift);
    }

    private GiftGiveListener giftGiveListener;

    public void setGiftGiveListener(GiftGiveListener listener) {
        giftGiveListener = listener;
    }

    /**
     * 根据表情数量以及GridView设置的行数和列数计算Pager数量
     *
     * @return
     */
    public int getPagerCount(int listSize, int columns, int rows) {
        return listSize % (columns * rows - 0) == 0 ? listSize / (columns * rows - 0) : listSize / (columns * rows - 0) + 1;
    }

    /**
     * 初始化表情列表staticGiftsList
     */
//    public List<Gift> initStaticGifts(Context context) {
//        List<Gift> giftsList = null;
//        try {
//            giftsList = new ArrayList<Gift>();
//            Gift giftModel;
//            String[] gifts = context.getAssets().list(ASSETS_ROOT.substring(0, ASSETS_ROOT.length() - 1));
//            //将Assets中的表情名称转为字符串一一添加进staticGiftsList
//            for (int i = 0; i < gifts.length; i++) {
//                giftModel = new Gift();
//                giftModel.setGiftName(ASSETS_ROOT+gifts[i]);
//                giftModel.setGiftImg("");
//                giftModel.setGiftCost(i+1+"");
//                giftsList.add(giftModel);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return giftsList;
//    }

}
