package com.kaolafm.kradio.search;

import com.kaolafm.kradio.lib.base.mvp.BaseModel;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.personalise.PersonalizedRequest;
import com.kaolafm.opensdk.api.personalise.model.HotRecommend;
import com.kaolafm.opensdk.api.search.AppSearchRequest;
import com.kaolafm.opensdk.api.search.SearchClassify;
import com.kaolafm.opensdk.api.search.SearchRequest;
import com.kaolafm.opensdk.api.search.model.SearchProgramBean;
import com.kaolafm.opensdk.http.core.HttpCallback;

import java.util.List;

public class SearchModel extends BaseModel {

    private SearchRequest mSearchRequest;
    private AppSearchRequest mAppSearchRequest;
    private PersonalizedRequest mPersonalizedRequest;


    public SearchModel() {
        mSearchRequest = new SearchRequest().setTag(this.toString());
        mAppSearchRequest = new AppSearchRequest().setTag(this.toString());
        mPersonalizedRequest = new PersonalizedRequest().setTag(this.toString());
    }

    @Override
    public void destroy() {

    }

    public void getAssociateWords(String keyword, HttpCallback<List<String>> callback) {
        mSearchRequest.getSuggestedWords(keyword, callback);
    }

    public void searchByKeyword(String keyword, HttpCallback<List<SearchProgramBean>> callback) {
        mSearchRequest.searchAll(keyword, callback);
    }

    public void getHotSearchWords(HttpCallback<List<String>> callback) {
        mSearchRequest.getHotWords(callback);
    }

    public void search(String keyword, int type, HttpCallback<BasePageResult<List<SearchProgramBean>>> callback) {
        mSearchRequest.searchByType(keyword, type, 20, 1, callback);
    }

    public void searchAllById(String keyword, String classifyId, int pagenum, int pagesize, HttpCallback<List<SearchProgramBean>> callback) {
        mAppSearchRequest.searchAllById(keyword, classifyId, pagenum, pagesize, callback);
    }

    public void searchClassifyAll(HttpCallback<List<SearchClassify>> callback) {
        mAppSearchRequest.searchClassifyAll(callback);
    }

    public void getHotRecommend(HttpCallback<HotRecommend> callback) {
        mPersonalizedRequest.getHotRecommend(callback);
    }


    public void cancelRequest() {
        mSearchRequest.cancel(this.toString());
        mAppSearchRequest.cancel(this.toString());
        mPersonalizedRequest.cancel(this.toString());
    }

}
