package com.kaolafm.kradio.flavor.impl;

import com.kaolafm.kradio.lib.base.flavor.KRadioShowSubscribeBtnInter;
import com.kaolafm.opensdk.player.logic.PlayerManager;

/**
 * author : wxb
 * date   : 2022/1/27
 * desc   :
 */
public class KRadioShowSubscribeBtnImpl implements KRadioShowSubscribeBtnInter {
    @Override
    public boolean canShowBtn() {
        try {
            //只有为1的时候不能订阅
            return PlayerManager.getInstance().getPlayListInfo().getNoSubscribe() != 1;

        }catch (Exception e){
            return true;
        }

    }
}
