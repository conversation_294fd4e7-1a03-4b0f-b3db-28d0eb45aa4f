package com.kaolafm.kradio.lib.base.flavor;

/******************************************
 * 类描述: 系统音频源切换监听
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-05-23 11:38
 ******************************************/
public interface KRadioSystemSourceChangeInter {

    /**
     * 注册音频源切换事件
     *
     * @param args
     * @return
     */
    boolean registerSourceChanged(Object... args);

    /**
     * 注销音频源切换事件
     *
     * @param args
     * @return
     */
    boolean unregisterSourceChanged(Object... args);
}
