<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/search_result_exception_layout_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/colorBlack">

    <ImageView
        android:id="@+id/iv_search_exception_pic"
        android:layout_width="@dimen/x360"
        android:layout_height="@dimen/y142"
        android:layout_marginBottom="@dimen/y32"
        android:src="@drawable/comprehensive_search_exception_pic"
        app:layout_constraintBottom_toTopOf="@id/tv_search_exception_message"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/tv_search_exception_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textColor="@color/comprehensive_search_exception_message"
        android:textSize="@dimen/text_size4"
        app:layout_constraintBottom_toBottomOf="@id/tv_search_exception_sub_message"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_search_exception_pic"
        tools:text="@string/search_no_result" />

    <skin.support.widget.SkinCompatTextView
        android:id="@+id/tv_search_exception_sub_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/comprehensive_hot_recommend_txt"
        android:textColor="@color/comprehensive_search_exception_message"
        android:textSize="@dimen/text_size4"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/hot_word_layout"
        app:layout_constraintTop_toBottomOf="@id/tv_search_exception_message"
        tools:visibility="visible" />

    <LinearLayout
        android:id="@+id/hot_word_layout"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@id/tv_search_exception_sub_message"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toRightOf="@id/tv_search_exception_sub_message"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_search_exception_sub_message">

        <TextView
            android:id="@+id/tv_hot_word"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center"
            android:textColor="@color/comprehensive_search_exception_message"
            android:textSize="@dimen/text_size4"
            tools:text="这里是热词" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>