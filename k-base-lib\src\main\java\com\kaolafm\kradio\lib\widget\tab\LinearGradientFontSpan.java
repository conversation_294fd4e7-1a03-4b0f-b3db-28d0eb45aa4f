package com.kaolafm.kradio.lib.widget.tab;

import android.graphics.Canvas;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.Shader;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.text.style.ReplacementSpan;
import android.text.style.UpdateAppearance;

public class LinearGradientFontSpan extends ReplacementSpan implements UpdateAppearance {
    private int startColor;
    private int endColor;
    private int mSize;
    public LinearGradientFontSpan(int startColor, int endColor) {
        this.startColor = startColor;
        this.endColor = endColor;
    }

    @Override
    public int getSize(@NonNull Paint paint, CharSequence text, int start, int end, @Nullable Paint.FontMetricsInt fm) {
        mSize = (int) paint.measureText(text , start , end);
        Paint.FontMetricsInt metrics = paint.getFontMetricsInt();
        if (fm != null) {
            fm.top = metrics.top;
            fm.ascent = metrics.ascent;
            fm.descent = metrics.descent;
            fm.bottom = metrics.bottom;
        }
        return mSize;
    }

    @Override
    public void draw(@NonNull Canvas canvas, CharSequence text, int start, int end, float x, int top, int y, int bottom, @NonNull Paint paint) {
        LinearGradient lg = new LinearGradient(0, 0, 0, paint.descent() - paint.ascent(),
                startColor, endColor, Shader.TileMode.REPEAT);
        paint.setShader(lg);        //这里注意这里画出来的渐变色会受TextView的字体色的透明度影响
        canvas.drawText(text, start, end, x, y, paint);//绘制文字
    }


}