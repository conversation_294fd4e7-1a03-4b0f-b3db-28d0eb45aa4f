package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.net.http.SslError;
import android.os.Build;
import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.Group;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.View;
import android.webkit.SslErrorHandler;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.auto.home.HubActivity;
import com.kaolafm.kradio.common.helper.SkinHelper;
import com.kaolafm.kradio.common.report.ReportUtil;
import com.kaolafm.kradio.common.utils.FlavorUtil;
import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.base.flavor.KRadioHubUserPromptInter;
import com.kaolafm.kradio.lib.base.flavor.UserCenterInter;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.KaolaAppConfigData;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.lib.utils.SharedPreferenceUtil;
import com.kaolafm.report.ReportHelper;

/**
 * @Package: com.kaolafm.kradio.flavor.impl
 * @Description:
 * @Author: Maclay
 * @Date: 14:40
 */
public class KRadioHubUserPromptImpl implements KRadioHubUserPromptInter {

    HubActivity activity;
    private NoticeHolder noticeHolder;
    boolean showNotice;

    @Override
    public boolean showUserPrompt(Context context) {
        return SharedPreferenceUtil.getInstance(context, "k_notice", Context.MODE_PRIVATE).getBoolean("show", true);
    }

    @Override
    public void showPrompt(@NonNull Activity activity) {
        this.activity = (HubActivity) activity;
        ScreenUtil.setStatusBar(activity.getWindow(),false);//沉浸
        showNotice();
    }


    private NoticeHolder initNoticeHolder() {
        if (noticeHolder == null) {
            noticeHolder = new NoticeHolder();
        }
        return noticeHolder;
    }

    private class NoticeHolder {
        boolean isChecked = false;
        int web = 0;
    }

    private void showNotice() {
        activity.setContentView(R.layout.launch_notice);
        if (noticeHolder == null) {
            ToastUtil.showInfo(activity, R.string.launcher_agreement_first_toast);
        }
        showNotice = true;
        TextView tvContent = activity.findViewById(R.id.tvContent);
        TextView tvTitle = activity.findViewById(R.id.tvTitle);
        String content = activity.getString(R.string.launch_notice_content);
        int index_start = content.indexOf("《", 0);
        int index_end = content.indexOf("》", index_start) + 1;
        int index_start1 = content.indexOf("《", index_end);
        int index_end1 = content.indexOf("》", index_start1) + 1;
        Group group = activity.findViewById(R.id.group);
        Group group1 = activity.findViewById(R.id.group1);
        ImageView ivBack = activity.findViewById(R.id.ivBack);
        ivBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                group1.setVisibility(View.GONE);
                group.setVisibility(View.VISIBLE);
                tvTitle.setText(R.string.launch_notice);
                initNoticeHolder().web = 0;
            }
        });
        WebView webView = activity.findViewById(R.id.webView);
        if (Build.VERSION.SDK_INT < 19 && webView != null) {
            webView.removeJavascriptInterface("searchBoxJavaBridge_");
            webView.removeJavascriptInterface("accessibility");
            webView.removeJavascriptInterface("accessibilityTraversal");
        }

        SpannableStringBuilder spannable = new SpannableStringBuilder(content);
        spannable.setSpan(new ForegroundColorSpan(activity.getResources().getColor(R.color.agreement_content_jump_txt)), index_start, index_end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannable.setSpan(new ClickableSpan() {
                              @Override
                              public void onClick(@NonNull View widget) {
                                  if (!NetworkUtil.isNetworkAvailable(activity, true)) {
                                      return;
                                  }
                                  group1.setVisibility(View.VISIBLE);
                                  group.setVisibility(View.GONE);
                                  tvTitle.setText(R.string.launcher_agreement0);
                                  String CUR_PAGE = "";
                                  if (SkinHelper.isNightMode()) {
                                      CUR_PAGE = ResUtil.getString(R.string.http_url_server_agreement_night);
                                  } else {
                                      CUR_PAGE = ResUtil.getString(R.string.http_url_server_agreement);
                                  }
                                  String web = FlavorUtil.getHttp443Url(CUR_PAGE);
                                  showWebView(webView, web);
                                  initNoticeHolder().web = 1;
                              }

                              @Override
                              public void updateDrawState(@NonNull TextPaint ds) {
                                  super.updateDrawState(ds);
                                  ds.setUnderlineText(false);
                              }
                          }, index_start, index_end
                , Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannable.setSpan(new ForegroundColorSpan(activity.getResources().getColor(R.color.agreement_content_jump_txt)), index_start1, index_end1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannable.setSpan(new ClickableSpan() {
                              @Override
                              public void onClick(@NonNull View widget) {
                                  if (!NetworkUtil.isNetworkAvailable(activity, true)) {
                                      return;
                                  }
                                  group1.setVisibility(View.VISIBLE);
                                  group.setVisibility(View.GONE);
                                  tvTitle.setText(R.string.launcher_agreement1);
                                  String CUR_PAGE = "";
                                  if (SkinHelper.isNightMode()) {
                                      CUR_PAGE = ResUtil.getString(com.kaolafm.kradio.k_kaolafm.R.string.http_url_policy_night);
                                  } else {
                                      CUR_PAGE = ResUtil.getString(com.kaolafm.kradio.k_kaolafm.R.string.http_url_policy);
                                  }
                                  showWebView(webView, FlavorUtil.getHttp443Url(CUR_PAGE));
                                  initNoticeHolder().web = 2;
                              }

                              @Override
                              public void updateDrawState(@NonNull TextPaint ds) {
                                  super.updateDrawState(ds);
                                  ds.setUnderlineText(false);
                              }
                          }, index_start1, index_end1
                , Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        tvContent.setText(spannable);
        tvContent.setMovementMethod(LinkMovementMethod.getInstance());
        CheckBox cb = activity.findViewById(R.id.cb);
        TextView tvCb = activity.findViewById(R.id.tvCb);
        tvCb.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                cb.performClick();
            }
        });
        cb.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                initNoticeHolder().isChecked = isChecked;
            }
        });
        activity.findViewById(R.id.tvStart).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                boolean agree = cb.isChecked();
                if (agree) {
                    showNotice = false;
                    SharedPreferenceUtil.getInstance(v.getContext(), "k_notice", Context.MODE_PRIVATE).putBoolean("show", false);
                    activity.normalOnCreate();
                    activity.onResume();
                } else {
                    ToastUtil.showInfo(v.getContext(), R.string.launcher_agreement_first_toast);
                }
            }
        });
        activity.findViewById(R.id.tvExit).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                activity.finish();
            }
        });
        if (noticeHolder != null) {
            cb.setChecked(noticeHolder.isChecked);
            switch (noticeHolder.web) {
                case 0:
                    //nothing
                    break;
                case 1:
                    group1.setVisibility(View.VISIBLE);
                    group.setVisibility(View.GONE);
                    tvTitle.setText(R.string.launcher_agreement0);
                    String CUR_PAGE = "";
                    if (SkinHelper.isNightMode()) {
                        CUR_PAGE = ResUtil.getString(R.string.http_url_server_agreement_night);
                    } else {
                        CUR_PAGE = ResUtil.getString(R.string.http_url_server_agreement);
                    }
                    String web = FlavorUtil.getHttp443Url(CUR_PAGE);
                    showWebView(webView, web);
                    break;
                case 2:
                    group1.setVisibility(View.VISIBLE);
                    group.setVisibility(View.GONE);
                    tvTitle.setText(R.string.launcher_agreement1);
                    String POLICY_PAGE = "";
                    if (SkinHelper.isNightMode()) {
                        POLICY_PAGE = ResUtil.getString(com.kaolafm.kradio.k_kaolafm.R.string.http_url_policy_night);
                    } else {
                        POLICY_PAGE = ResUtil.getString(com.kaolafm.kradio.k_kaolafm.R.string.http_url_policy);
                    }
                    showWebView(webView, FlavorUtil.getHttp443Url(POLICY_PAGE));
                    break;
            }
        }
    }

    private void showWebView(WebView webView, String url) {
        KaolaAppConfigData kaolaAppConfigData = KaolaAppConfigData.getInstance();
        StringBuilder sb = new StringBuilder(url);
//        String appId = kaolaAppConfigData.getAppId();
//        sb.append(appId);
        //支持黑夜模式和白天模式
        UserCenterInter userCenterInter = ClazzImplUtil.getInter("UserCenterInterImpl");
        if (userCenterInter != null && userCenterInter.supportDarkLightSwitch()) {
            sb.append("&theme=");
            sb.append(userCenterInter.getTheme(activity));
//            int skin = getContext().getResources().getConfiguration().uiMode;
//            if (TextUtils.equals(skin, "night")) {
//                sb.append("&theme=dark");
//            } else {
//                sb.append("&theme=light");
//            }
            webView = userCenterInter.getWebView(activity, sb.toString());
        } else {
            sb.append("mo1336&theme=");
            int uiMode = activity.getResources().getConfiguration().uiMode;
            if (uiMode == 17) {//默认
                sb.append("dark");
            } else if (uiMode == 33 || uiMode == 0x20) {
                sb.append("dark");
            } else {
                sb.append("light");
            }
        }
        setWebViewSetting(webView);
        webView.clearView();
        String httpUrl = sb.toString();
        webView.setVisibility(View.INVISIBLE);
        webView.loadUrl(httpUrl);
    }

    private void setWebViewSetting(WebView webViewContent) {
        WebSettings settings = webViewContent.getSettings();
        settings.setUseWideViewPort(true);
        settings.setJavaScriptEnabled(false);
        settings.setSavePassword(false);
        settings.setTextZoom(ResUtil.getInt(com.kaolafm.kradio.k_kaolafm.R.integer.web_view_zoom_size));
        //自动加载图片
        settings.setLoadsImagesAutomatically(true);
        settings.setAppCacheEnabled(false);
        settings.setCacheMode(WebSettings.LOAD_NO_CACHE);//不使用缓存，只从网络获取数据.
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            // 5.0以上允许加载http和https混合的页面(5.0以下默认允许，5.0+默认禁止)
            settings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        }
        webViewContent.setBackgroundColor(Color.TRANSPARENT);
        webViewContent.setLayerType(View.LAYER_TYPE_SOFTWARE, null);
        webViewContent.setDrawingCacheEnabled(false);
        webViewContent.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                //可处理loading开始
                super.onPageStarted(view, url, favicon);
            }

            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
//                return super.shouldOverrideUrlLoading(view, url);
                view.loadUrl(url);
                return true;
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                //可处理loading结束
                super.onPageFinished(view, url);
                webViewContent.setVisibility(View.VISIBLE);
                Log.d("web", "url = " + url);
            }

            @Override
            public void onReceivedSslError(WebView view, SslErrorHandler handler, SslError error) {
                handler.cancel();
            }
        });
    }

}
