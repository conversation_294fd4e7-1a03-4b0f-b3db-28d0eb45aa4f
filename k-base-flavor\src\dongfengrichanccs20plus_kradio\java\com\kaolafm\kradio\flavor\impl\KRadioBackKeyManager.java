package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentActivity;
import android.view.Gravity;

import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.dialog.Dialogs;
import com.kaolafm.kradio.lib.utils.IntentUtils;
import com.kaolafm.kradio.lib.utils.ResUtil;

public class KRadioBackKeyManager {

    private volatile static KRadioBackKeyManager mKRadioBackKeyManager;
    DialogFragment dialogFragment;

    private KRadioBackKeyManager(){}

    public static KRadioBackKeyManager getInstance(){
        if(mKRadioBackKeyManager == null){
            synchronized (KRadioBackKeyManager.class){
                if(mKRadioBackKeyManager == null){
                    mKRadioBackKeyManager = new KRadioBackKeyManager();
                }
            }
        }
        return mKRadioBackKeyManager;
    }

    public boolean onBackPressed(Object... args){
        final Activity activity = (Activity) args[0];
        if(dialogFragment!=null &&  dialogFragment.getDialog()!=null
                && dialogFragment.getDialog().isShowing()) {
            //dialog is showing so do something
            dialogFragment.getDialog().dismiss();
        } else {
            //dialog is not showing
            dialogFragment = new Dialogs.Builder()
                    .setType(Dialogs.TYPE_2BTN)
                    .setGravity(Gravity.CENTER)
                    .setLeftBtnText(ResUtil.getString(R.string.ok))
                    .setRightBtnText(ResUtil.getString(R.string.move_to_background_str))
                    .setMessage(ResUtil.getString(R.string.exit_msg_str))
                    .setOnPositiveListener(dialog -> {
                        dialog.dismiss();
                        IntentUtils.getInstance().startLauncher(activity);
                    }).setOnNativeListener(dialog -> {
                        dialog.dismiss();
                        IntentUtils.getInstance().startLauncher(activity);
                        activity.finish();
                    })
                    .create();
            dialogFragment.show(((FragmentActivity) activity).getSupportFragmentManager(), "exit_app");
        }
        return true;
    }
}
