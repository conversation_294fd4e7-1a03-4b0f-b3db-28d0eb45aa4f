package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.webkit.WebView;

import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.base.flavor.UserCenterInter;

import skin.support.utils.SkinPreference;

public class UserCenterInterImpl implements UserCenterInter {

    @Override
    public String getFlavorServiceAgreementUrl() {
        StringBuilder sb = new StringBuilder();
        sb.append("https//m.kaolafm.com/location/ytServerAgreement.html?appid=mo1336&theme=");
        String skin = SkinPreference.getInstance().getSkinName();
        if (TextUtils.equals(skin, "night")) {
            sb.append("dark");
        } else {
            sb.append("light");
        }
        return sb.toString();
    }

    @Override
    public String getFlavorHiddenPolicyUrl() {
        StringBuilder sb = new StringBuilder();
        sb.append("https://m.kaolafm.com/location/ytPolicy.html?appid=mo1336&theme=");
        String skin = SkinPreference.getInstance().getSkinName();
        if (TextUtils.equals(skin, "night")) {
            sb.append("dark");
        } else {
            sb.append("light");
        }
        return sb.toString();
    }

    @Override
    public boolean supportDarkLightSwitch() {
        return true;
    }

    @Override
    public String getTheme(Context context) {
        int uiMode = context.getResources().getConfiguration().uiMode;
        if (uiMode == 33 || uiMode == 0x20) {
            return "dark";
        }
        return "light";
    }

    @Override
    public boolean isShowThemeEvent(View view) {
        if (null == view || view.getVisibility() != View.VISIBLE) {
            return false;
        }
        return true;
    }

    @Override
    public WebView getWebView(Activity context, String url) {
        View v2 = context.findViewById(R.id.web_view_layout);
        WebView webView = context.findViewById(R.id.web_view_content);
        WebView webView2 = context.findViewById(R.id.web_view_content);
        if (webView2 != null) {
            if (url.endsWith("light")) {
                v2.setVisibility(View.GONE);
                webView.setVisibility(View.VISIBLE);
                webView2.setVisibility(View.GONE);
            } else {
                v2.setVisibility(View.VISIBLE);
                webView2.setVisibility(View.VISIBLE);
                webView.setVisibility(View.GONE);
                webView = webView2;
            }
        }
        return webView;
    }

    @Override
    public void setWebViewLongClick(WebView webView) {
        webView.setLongClickable(false);
        webView.setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                return true;
            }
        });
    }
}
