<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/phone_root_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:orientation="horizontal"
    tools:background="@color/color_black_50_transparent"
    android:padding="@dimen/m50">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="3"
        android:gravity="center">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical">

            <TextView
                android:id="@+id/phone_tip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/user_login_phone_tip"
                android:textColor="@color/user_info_value_color"
                android:textSize="@dimen/m24"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <EditText
                android:id="@+id/et_input_num"
                android:layout_width="@dimen/x352"
                android:layout_height="@dimen/y48"
                android:layout_marginTop="@dimen/y16"
                android:background="@drawable/online_user_login_input_bg"
                android:hint="@string/user_input_phone_num"
                android:imeOptions="actionNext"
                android:inputType="number"
                android:maxLength="11"
                android:maxLines="1"
                android:nextFocusForward="@+id/et_input_code"
                android:paddingStart="@dimen/x16"
                android:textColor="@color/user_info_value_color"
                android:textColorHint="@color/user_info_key_color"
                android:textCursorDrawable="@drawable/online_cursor_drawable"
                android:textSize="@dimen/m20"
                app:layout_constraintLeft_toLeftOf="@+id/phone_tip"
                app:layout_constraintTop_toBottomOf="@+id/phone_tip" />

            <EditText
                android:id="@+id/et_input_code"
                android:layout_width="@dimen/x166"
                android:layout_height="@dimen/y48"
                android:layout_marginTop="@dimen/online_login_button_margin_top"
                android:background="@drawable/online_user_login_input_bg"
                android:hint="@string/user_verification_code"
                android:inputType="number"
                android:maxLength="6"
                android:maxLines="1"
                android:paddingStart="@dimen/x16"
                android:textColor="@color/user_info_value_color"
                android:textColorHint="@color/user_info_key_color"
                android:textCursorDrawable="@drawable/online_cursor_drawable"
                android:textSize="@dimen/m20"
                app:layout_constraintLeft_toLeftOf="@id/et_input_num"
                app:layout_constraintTop_toBottomOf="@id/et_input_num" />

            <Button
                android:id="@+id/btn_get_code"
                android:layout_width="0dp"
                android:layout_height="@dimen/y48"
                android:layout_marginStart="@dimen/x20"
                android:layout_marginTop="@dimen/online_login_button_margin_top"
                android:background="@drawable/online_selector_code_btn_bg"
                android:gravity="center"
                android:text="@string/user_get_verification_code"
                android:textAllCaps="false"
                android:textColor="@drawable/online_selector_code_btn_text"
                android:textSize="@dimen/m20"
                app:layout_constraintBottom_toBottomOf="@id/et_input_code"
                app:layout_constraintLeft_toRightOf="@id/et_input_code"
                app:layout_constraintRight_toRightOf="@id/et_input_num" />

            <Button
                android:id="@+id/btn_login"
                android:layout_width="@dimen/x352"
                android:layout_height="@dimen/y64"
                android:layout_marginTop="@dimen/y50"
                android:background="@drawable/online_bg_login_btn"
                android:gravity="center"
                android:text="@string/user_login"
                android:textColor="@color/user_info_value_color"
                android:textSize="@dimen/m24"
                app:layout_constraintLeft_toLeftOf="@+id/phone_tip"
                app:layout_constraintTop_toBottomOf="@id/btn_get_code" />

            <TextView
                android:id="@+id/tv_no_login_ps"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/y20"
                android:text="@string/user_notice_string"
                android:textColor="@color/user_info_key_color"
                android:textSize="@dimen/m20"
                app:layout_constraintLeft_toLeftOf="@id/et_input_code"
                app:layout_constraintTop_toBottomOf="@id/btn_login" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>
</LinearLayout>