package com.kaolafm.kradio.flavor.impl;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.os.RemoteException;
import android.util.Log;

import com.dfl.api.usercenter.AccountInterflow;

/**
 * <AUTHOR>
 **/
public class RiChanHelper {

    private static final String TAG = "k.login.rch";
    private volatile static RiChanHelper mInstance;
    private Context mContext;
    private AccountInterflow mService;
    private ServiceConnection mConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            mService = AccountInterflow.Stub.asInterface(service);
            Log.i(TAG, "onServiceConnected:" + name + ",已连接.");
            try {
                service.linkToDeath(mDeathRecipient, 0);
                Log.i(TAG, "linkToDeath start");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            Log.i(TAG, "onServiceDisconnected:" + name + ",正在重连...");
            init();
        }
    };

    private IBinder.DeathRecipient mDeathRecipient = new IBinder.DeathRecipient() {
        @Override
        public void binderDied() {
            Log.i(TAG, "binderDied start");
            if (mService != null) {
                mService.asBinder().unlinkToDeath(mDeathRecipient, 0);
            }
            init();
        }
    };

    private RiChanHelper(Context context) {
        mContext = context;
    }

    public static RiChanHelper getInstance(Context context) {
        if (mInstance == null) {
            synchronized (RiChanHelper.class) {
                if (mInstance == null) {
                    mInstance = new RiChanHelper(context);
                }
            }
        }
        return mInstance;
    }


    public boolean init() {
        Intent intent = new Intent("com.szlanyou.usercenter.service.JiDouUserService");
        intent.setPackage("com.szlanyou.usercenter");
        intent.setAction("com.dfl.IBINDER_USERCENTER_SERVICE");
        return mContext.bindService(intent, mConnection, Context.BIND_AUTO_CREATE);
    }

    /**
     * 判断日产的[个人中心]是否登录
     *
     * @return
     */
    public boolean getLoginStatus() {
        boolean rst = false;
        try {
            rst = mService.getLoginStatus();
            Log.i(TAG, "onServiceConnected: loginStatus=" + rst);
//        } catch (DeadObjectException e) {
//            com.kaolafm.kradio.lib.utils.Logger.e(TAG, "getLoginStatus: 与个人中心(om.szlanyou.usercenter)的aidl连接中断了.");
        } catch (Exception e) {
            e.printStackTrace();
            init();
        }

        return rst;
    }

    /**
     * 显示日产[个人中心-登录]页面
     */
    public void showLoginPage() {
//        Intent intent = new Intent();
//        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//        intent.setClassName("com.szlanyou.usercenter", "com.szlanyou.usercenter.view.user.QRCodeActivity");
//        intent.putExtra("sourceName", "kaola");
//        mContext.startActivity(intent);
        if (mService == null) {
            Log.i(TAG, "showLoginPage mService is null");
            return;
        }
        try {
            mService.openIcarPages(2, 1);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }
}
