<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="@dimen/m309"
    android:layout_gravity="center"
    android:background="@drawable/bg_dialog"
    android:gravity="center"
    android:orientation="vertical"
    android:paddingLeft="@dimen/x40"
    android:paddingTop="@dimen/y40"
    android:paddingRight="@dimen/x40"
    android:paddingBottom="@dimen/y40">

    <TextView
        android:id="@+id/tv_dialog_bottom_message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textColor="@color/dialog_common_btn_title_text_color"
        android:textSize="@dimen/text_size5"
        tools:text="确定清空收听历史吗?" />

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/tv_dialog_button_main_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y80"
        android:gravity="center_horizontal"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_dialog_bottom_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/selector_dialog_costomize_btn_sure"
            android:paddingStart="@dimen/x8"
            android:paddingTop="@dimen/y14"
            android:paddingEnd="@dimen/x8"
            android:paddingBottom="@dimen/y14"
            android:text="@string/cancel"
            android:textColor="@color/dialog_common_btn_cancel_text_color"
            android:textSize="@dimen/text_size3" />

        <TextView
            android:id="@+id/tv_dialog_bottom_define"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/x60"
            android:background="@drawable/selector_dialog_costomize_btn_cancle"
            android:paddingStart="@dimen/x8"
            android:paddingTop="@dimen/y14"
            android:singleLine="true"
            android:paddingEnd="@dimen/x8"
            android:paddingBottom="@dimen/y14"
            android:text="@string/sure"
            android:textColor="@color/dialog_common_btn_sure_text_color"
            android:textSize="@dimen/text_size3" />
    </LinearLayout>
</LinearLayout>