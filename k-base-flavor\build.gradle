apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'product-flavor'
apply from: '../aop.gradle'

def and = rootProject.ext.android
def dependent = rootProject.ext.dependencies
def flavor = rootProject.ext.flavor

android {
    compileSdkVersion and.compileSdkVersion
    buildToolsVersion and.buildToolsVersion
    defaultConfig {
        minSdkVersion and.minSdkVersion
        targetSdkVersion and.targetSdkVersion
        versionCode and.versionCode
        versionName and.versionName

        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'
        multiDexEnabled true
        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [moduleName: project.getName()]
            }
        }
    }

    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
            // 此处用于读取SINGLE_CHANNEL的值适配黑夜白天模式
            res.srcDirs = ["src/${flavor.channel}/res", "src/${flavor.channel}/res-night"]
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        prelease.initWith(android.buildTypes.release)
    }

    lintOptions {
        quiet true
        abortOnError false
    }

    compileOptions {
        sourceCompatibility and.javaSourceVersion
        targetCompatibility and.javaTargetVersion
    }
    packagingOptions {
        exclude 'lib/arm64-v8a/liblocSDK8b.so'
    }
}

dependencies {
    implementation fileTree(include: ['*.jar', '*.aar'], dir: 'libs')
    testImplementation 'junit:junit:4.12'
    annotationProcessor project(":annotation")
    implementation project(':k-module-kaolafm')

}