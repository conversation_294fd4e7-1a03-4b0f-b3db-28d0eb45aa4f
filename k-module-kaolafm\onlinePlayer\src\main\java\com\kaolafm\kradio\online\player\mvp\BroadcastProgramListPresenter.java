package com.kaolafm.kradio.online.player.mvp;

import android.util.Log;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.kradio.lib.base.ui.online.BaseShowHideFragment;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.BroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.trello.rxlifecycle3.android.FragmentEvent;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-04-17 11:32
 ******************************************/

public final class BroadcastProgramListPresenter extends BasePresenter<BroadcastProgramListModel, BroadcastProgramListView> {

    public BroadcastProgramListPresenter(BroadcastProgramListView view) {
        super(view);
    }

    @Override
    protected BroadcastProgramListModel createModel() {
        return new BroadcastProgramListModel(((BaseShowHideFragment) mView).bindUntilEvent(FragmentEvent.DESTROY_VIEW));
    }

    /**
     * 获取广播播单数据
     *  @param bean
     * @param updateView
     * @param classifyId
     * @param listenNum
     */
    public void getBroadcastProgramList(BroadcastProgramRequestBean bean, boolean updateView, String channel, int classifyId, long listenNum) {
        //如果是正在播放的播单 ,就直接取播单数据
//        if (disposeCurrentPlay(bean)) {
//            return;
//        }
        Log.i("getBroadcastProgramList", "bean.getParentId() = " + bean.getParentId() + " bean.getDate() = " + bean.getDate());
        mModel.getBroadcastProgramList(bean.getParentId(), bean.getDate(), channel, classifyId, listenNum,new HttpCallback<ArrayList<PlayItem>>() {
            @Override
            public void onSuccess(ArrayList<PlayItem> playItemList) {
                onGetProgramListDataSuccess(playItemList, updateView);
            }

            @Override
            public void onError(ApiException e) {
                onGetProgramListDataError(e.getCode(), e.getMessage());
            }
        });
    }

    /**
     * 如果获取的是当前播放器播放的节目 , 直接通过播单获取
     *
     * @return
     */
    private boolean disposeCurrentPlay(BroadcastProgramRequestBean broadcastProgramRequestBean) {
        if (broadcastProgramRequestBean == null || broadcastProgramRequestBean.getDate() == null) {
            return false;
        }
        PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
        if (playItem instanceof BroadcastPlayItem) {
            if (broadcastProgramRequestBean.getDate().equals(time2Date(((BroadcastPlayItem) playItem).getTimeInfoData().getStartTime()))) {
                List<PlayItem> playItems = PlayerManager.getInstance().getPlayList();
                if (!ListUtil.isEmpty(playItems)) {
                    onGetProgramListDataSuccess(playItems, true);
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 将Unix时间戳转换成指定格式日期字符串
     */
    private String time2Date(Long timestamp) {
        String formats = "yyyy-MM-dd";
        return new SimpleDateFormat(formats, Locale.CHINA).format(new Date(timestamp));
    }

    private void onGetProgramListDataSuccess(List<PlayItem> playItemArrayList, boolean updateView) {
        if (mView == null) {
            return;
        }
        mView.onGetBroadcastProgramListDataSuccess(playItemArrayList, updateView);
    }

    private void onGetProgramListDataError(int code, String msg) {
        if (mView == null) {
            return;
        }
        mView.onGetBroadcastProgramListDataError(code, msg);
    }
}
