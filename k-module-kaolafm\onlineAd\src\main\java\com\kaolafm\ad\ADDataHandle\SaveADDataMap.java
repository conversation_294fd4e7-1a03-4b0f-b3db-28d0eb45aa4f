package com.kaolafm.ad.ADDataHandle;

import android.app.Application;
import android.content.Context;
import android.content.SharedPreferences;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.kaolafm.kradio.common.http.api.ad.AdZoneMapping;
import com.kaolafm.kradio.lib.base.AppDelegate;

import java.util.List;

public class SaveADDataMap {
    static final String AD_DATA_KEY = "ad_data_key";
    Gson gson = new Gson();

    Application context = AppDelegate.getInstance().getContext();
    SharedPreferences sp = context.getSharedPreferences("kradio.ad.data", Context.MODE_PRIVATE);

    volatile static SaveADDataMap mInstance;

    static SaveADDataMap getInstance(){
        if(mInstance == null){
            synchronized (SaveADDataMap.class){
                if(mInstance == null){
                    mInstance = new SaveADDataMap();
                }
            }
        }
        return mInstance;
    }

    public void saveAdDataMappingList(List<AdZoneMapping> adZoneMappings) {
        //因为条目始终只有一个，考虑不用db，用sp更简单，存json字符串，再实例化]
        String adDataString = gson.toJson(adZoneMappings);
        sp.edit().putString(AD_DATA_KEY, adDataString).apply();
    }

    public List<AdZoneMapping> getAdDataMappingList() {
        String jsonDataString = sp.getString(AD_DATA_KEY, null);
        if (jsonDataString != null) {
            return gson.fromJson(jsonDataString, new TypeToken<List<AdZoneMapping>>() {
            }.getType());
        } else {
            return null;
        }
    }

    public void clearAll() {
        sp.edit().remove(AD_DATA_KEY).apply();
    }
}
