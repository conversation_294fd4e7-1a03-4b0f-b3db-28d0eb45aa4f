package com.kaolafm.kradio.common;

import com.kaolafm.base.utils.ListUtil;

import java.util.ArrayList;

/**
 * <AUTHOR> on 2019-08-21.
 * 皮肤替换完成回调管理类。这里和换肤模块没有耦合，而是在渠道换肤弹窗里进行的回调。
 */

public class SkinStateManager {

    private ArrayList<ILoadSkinListener> mSkinListenerArrayList;

    private static final class S_INSTANCE {
        public static SkinStateManager SKIN_STATE_MANAGER = new SkinStateManager();
    }

    public static SkinStateManager getInstance() {
        return S_INSTANCE.SKIN_STATE_MANAGER;
    }

    private SkinStateManager() {
        mSkinListenerArrayList = new ArrayList<>();
    }

    public void notifyLoadSkinSuccess() {
        ArrayList<ILoadSkinListener> tempArrayList = (ArrayList<ILoadSkinListener>) mSkinListenerArrayList.clone();
        if (ListUtil.isEmpty(tempArrayList)) {
            return;
        }
        int size = tempArrayList.size();
        for (int i = 0; i < size; i++) {
            ILoadSkinListener iLoadSkinListener = tempArrayList.get(i);
            if (iLoadSkinListener == null) {
                continue;
            }
            iLoadSkinListener.loadSuccess();
        }

    }

    public void addLoadSkinListener(ILoadSkinListener loadSkinListener) {
        if (loadSkinListener == null) {
            return;
        }
        if (mSkinListenerArrayList.contains(loadSkinListener)) {
            return;
        }
        mSkinListenerArrayList.add(loadSkinListener);
    }

    public void removeLoadSkinListener(ILoadSkinListener loadSkinListener) {
        if (loadSkinListener == null) {
            return;
        }
        if (!mSkinListenerArrayList.contains(loadSkinListener)) {
            return;
        }
        mSkinListenerArrayList.remove(loadSkinListener);
    }

    /**
     * 换肤完成回调
     */
    public interface ILoadSkinListener {
        void loadSuccess();
    }
}
