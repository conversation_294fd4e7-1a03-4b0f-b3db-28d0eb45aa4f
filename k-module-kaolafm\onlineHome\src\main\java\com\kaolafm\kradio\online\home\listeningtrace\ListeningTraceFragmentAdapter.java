package com.kaolafm.kradio.online.home.listeningtrace;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.fragment.app.FragmentTransaction;
import android.view.ViewGroup;

import com.kaolafm.kradio.lib.base.flavor.AllCategoriesCustomTitleInter;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/4/23
 */

public class ListeningTraceFragmentAdapter extends FragmentPagerAdapter {

    private final FragmentManager mFragmentManager;

    private List<Fragment> mFragments;

    private String[] mTitles;

    public ListeningTraceFragmentAdapter(FragmentManager fm,
                                         List<Fragment> fragments, String[] titles) {
        super(fm);
        mFragmentManager = fm;
        mFragments = fragments;
        mTitles = titles;
    }

    @Override
    public int getCount() {
        return mFragments == null ? 0 : mFragments.size();
    }

    @Override
    public Fragment getItem(int position) {
        return mFragments.get(position);
    }

    @Nullable
    @Override
    public CharSequence getPageTitle(int position) {
        AllCategoriesCustomTitleInter inter = ClazzImplUtil.getInter("AllCategoriesCustomTitleImpl");
        if (null != inter) {
            return inter.TitleCustom(mTitles[position]);
        }
        return mTitles[position];
    }

    @Override
    public void destroyItem(ViewGroup container, int position, Object object) {
    }

    //以下两个方法是为了实现动态替换fragment。

    @Override
    public int getItemPosition(@NonNull Object object) {
        return POSITION_NONE;
    }

    @Override
    public long getItemId(int position) {
        return mFragments.get(position).hashCode();
    }

    /**
     * 替换指定位置的fragment
     *
     * @param position 要替换位置
     */
    public void replace(int position, Fragment fragment) {
        if (position >= 0 && position < mFragments.size()) {
            FragmentTransaction ft = mFragmentManager.beginTransaction();
            ft.remove(mFragments.get(position));
            ft.commit();
            mFragmentManager.executePendingTransactions();
            mFragments.set(position, fragment);
            notifyDataSetChanged();
        }
    }

    public void updateTitles(String[] titles) {
        mTitles = titles;
    }

    public void updateFragments(List<Fragment> fragments) {
        if (this.mFragments != null) {
            FragmentTransaction ft = mFragmentManager.beginTransaction();
            for (Fragment f : this.mFragments) {
                ft.remove(f);
            }
            ft.commit();
            ft = null;
            mFragmentManager.executePendingTransactions();
        }
        this.mFragments = fragments;
        notifyDataSetChanged();
    }
}
