package com.kaolafm.opensdk.api.ex;

import android.util.Log;

import com.kaolafm.opensdk.account.token.AccessTokenManager;
import com.kaolafm.opensdk.account.token.KaolaAccessToken;
import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.http.core.HttpCallback;

import java.util.HashMap;

import io.reactivex.functions.Function;
import okhttp3.MediaType;
import okhttp3.RequestBody;

/**
 * <AUTHOR>
 **/
public class BindAccountRequest extends BaseRequest {
    private static final String TAG = "BindAccountRequest";

    private BindAccountService mService;

    public BindAccountRequest() {
        mService = obtainRetrofitService(BindAccountService.class);
    }

    public void bindDeviceId(String phone, String nickName, String avatar, HttpCallback<DeviceInfo> callback) {
        HashMap<String, String> params = new HashMap<>();
        params.put("phone", phone);
        params.put("nickName", nickName);
        params.put("avatar", avatar);
        String body = mGsonLazy.get().toJson(params);
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), body);
        doHttpDeal(mService.bindDeviceId(requestBody), new Function<BaseResult<DeviceInfo>, DeviceInfo>() {
            @Override
            public DeviceInfo apply(BaseResult<DeviceInfo> baseResult) throws Exception {
                DeviceInfo result = baseResult.getResult();
                Log.i(TAG, "bind success result = " + result);
                saveToken(result);
                return result;
            }
        }, callback);
    }

    private boolean saveToken(DeviceInfo deviceInfo) {
        if (deviceInfo != null) {
            KaolaAccessToken accessToken = AccessTokenManager.getInstance().getKaolaAccessToken();
            accessToken.setUserId(deviceInfo.getUserId());
            accessToken.setAccessToken(deviceInfo.getAccessToken());
            accessToken.setRefreshToken(deviceInfo.getRefreshToken());
            accessToken.setRefreshTime(deviceInfo.getRefreshTime());
        }
        return true;
    }
}
