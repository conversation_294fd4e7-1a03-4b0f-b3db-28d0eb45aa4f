package com.kaolafm.kradio.lib.player.contant;

/******************************************
 * 类描述： 传统广播当前节目状态 类名称：BroadcastStatus
 *
 * @version: 1.0
 * @author: shaoning<PERSON>ang
 * @time: 2016-7-28 15:38
 ******************************************/
public interface BroadcastStatus {
    /**
     * 默认状态
     */
    int BROADCAST_STATUS_DEFAULT = 0;
    /**
     * 直播中
     */
    int BROADCAST_STATUS_LIVING = 1;
    /**
     * 可回放
     */
    int BROADCAST_STATUS_PLAYBACK = 2;
    /**
     * 未开播
     */
    int BROADCAST_STATUS_NOT_ON_AIR = 3;
}
