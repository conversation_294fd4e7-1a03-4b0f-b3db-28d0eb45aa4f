package com.kaolafm.kradio.common.widget;

import android.content.Context;
import android.os.Build;
import androidx.appcompat.widget.AppCompatImageButton;
import android.util.AttributeSet;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-04-14 10:12
 ******************************************/

public final class CImageButton extends AppCompatImageButton {
    public CImageButton(Context context) {
        super(context);
        initView();
    }

    public CImageButton(Context context, AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    public CImageButton(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
    }

    private void initView() {
        // 解决ImageButton在RelativeLayout/FrameLayout中层级总是在上层问题
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            setStateListAnimator(null);
        }
    }
}
