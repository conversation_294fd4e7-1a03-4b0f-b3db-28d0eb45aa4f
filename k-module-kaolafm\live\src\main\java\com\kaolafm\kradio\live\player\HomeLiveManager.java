package com.kaolafm.kradio.live.player;

import android.media.MediaPlayer;
import android.media.MediaRecorder;
import android.os.Environment;
import android.os.SystemClock;
import android.util.Log;

import com.kaolafm.kradio.lib.bean.BroadcastRadioSimpleData;
import com.kaolafm.kradio.lib.bean.HistoryItem;
import com.kaolafm.kradio.history.HistoryManager;
import com.kaolafm.kradio.lib.basedb.manager.MusicDaoManager;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioRecorderInter;
import com.kaolafm.kradio.lib.base.flavor.live.KradioRecorderInterface;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.live.KradioRecorder;
import com.kaolafm.kradio.live.LiveRecordhelper;
import com.kaolafm.kradio.live.mvp.HomeLivePresenter;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.player.utils.PlayItemConstantsSon;
import com.kaolafm.opensdk.api.live.model.LiveInfoDetail;
import com.kaolafm.opensdk.api.music.qq.model.Song;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.playlist.util.PlayListUtils;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.report.event.LivingStartListenReportEvent;

import java.io.File;
import java.io.IOException;
import java.util.Observable;
import java.util.Observer;

import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.functions.Action;
import io.reactivex.functions.Consumer;
import io.reactivex.schedulers.Schedulers;


/**
 * 管理直播信息的轮询，状态解析，录音管理，试听，默认播放，上传状态维护，是UI的一个辅助类
 *
 * <AUTHOR> Huangui
 */
public class HomeLiveManager {

    /**
     * KRadio开始录音广播消息
     *
     * @apiNote 此action针对KRadio客户端开始录音时发出的广播消息
     */
    public final static String START_RECORD_REQUEST_ACTION = "com.kaolafm.kradio.request.action.START_RECORD";
//    /**
//     * KRadio开始录音广播消息携带参数
//     *
//     * @apiNote 此extra针对KRadio客户端开始录音时发出的 START_RECORD_REQUEST_ACTION 消息额外携带的参数
//     */
//    public final static String START_RECORD_REQUEST_EXTRA = "com.kaolafm.kradio.request.extra.START_RECORD";

    /**
     * KRadio开始录音广播回复消息
     *
     * @apiNote 此action针对KRadio客户端开始录音时得到的回复广播消息（由非KRadio端发出）
     */
    public final static String START_RECORD_RESPONSSE_ACTION = "com.kaolafm.kradio.response.action.START_RECORD";
    /**
     * KRadio开始录音广播回复消息携带参数
     *
     * @apiNote 此extra针对KRadio客户端开始录音时收到的 START_RECORD_RESPONSE_ACTION 消息额外携带的参数（true 为同意KRadio的录音申请消息，false 为拒绝KRadio的录音申请消息）
     */
    public final static String START_RECORD_RESPONSE_BOOLEAN_EXTRA = "com.kaolafm.kradio.response.extra.START_RECORD";

    /**
     * KRadio停止录音广播消息
     *
     * @apiNote 此action针对KRadio客户端停止录音时发出的广播消息
     */
    public final static String STOP_RECORD_REQUEST_ACTION = "com.kaolafm.kradio.request.action.STOP_RECORD";
//    /**
//     * KRadio停止录音广播消息携带参数
//     *
//     * @apiNote 此extra针对KRadio客户端停止录音时发出的 STOP_RECORD_REQUEST_ACTION 消息额外携带的参数
//     */
//    public final static String STOP_RECORD_REQUEST_EXTRA = "com.kaolafm.kradio.request.extra.STOP_RECORD";

    /**
     * KRadio停止录音广播回复消息
     *
     * @apiNote 此action针对KRadio客户端停止录音时得到的回复广播消息（由非KRadio端发出）
     */
    public final static String STOP_RECORD_RESPONSSE_ACTION = "com.kaolafm.kradio.response.action.STOP_RECORD";
    /**
     * KRadio停止录音广播回复消息携带参数
     *
     * @apiNote 此extra针对KRadio客户端停止录音时收到的 STOP_RECORD_RESPONSSE_ACTION 消息额外携带的参数（true 为同意KRadio的停止录音申请消息，false 为拒绝KRadio的停止录音申请消息）
     */
    public final static String STOP_RECORD_RESPONSE_EXTRA = "com.kaolafm.kradio.response.extra.STOP_RECORD";


    private static final String TAG = "LiveManager";

    public static final long PROGRAM_ID_EMPTY = 0L;

    private static final int WHAT_LOOP_LIVE_STATUS = 1;

    private static final int LIVE_STATUS_LOOP_INTERVAL = 30 * 1000;

    public static final String RECORD_TIME_TOO_SHORT = "too_short";

    private static final int RECORD_MINIMUM_TIME = 1000;

    private LiveInfoDetail mShowingInfo;

    private LiveInfoDetail mPlayingInfo;

//    private MyHandler mHandler;
    private HomeLivePresenter mPresenter;

    private long mRecordStartTime;
    private int mRecordDuration;

    private LiveStatus mLiveStatus = LiveStatus.UNKNOWN;
    private ErrorStatus mErrorStatus = ErrorStatus.NO_ERROR;

    private KRadioAudioRecorderInter mKRadioAudioRecorderInter;
    private LiveRecordhelper mLiveRecordhelper;

    //录音机对象
    private KradioRecorderInterface mKradioRecorderInterface;

    public static final String EXTENSION = ".aac";
    public static final File RECORDINGS_DIR = new File(
            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_MUSIC),
            "K-Radio-Record");

    private MediaPlayer mMediaPlayer;
    private MediaRecorder mMediaRecorder;
    private String mFilePath;
    private RecorderStatusObservable mRecorderStatusObservable;

    private HomeLiveManager() {
//        mHandler = new MyHandler();
        mRecorderStatusObservable = new RecorderStatusObservable(RecorderStatus.IDLE);
        mKRadioAudioRecorderInter = ClazzImplUtil.getInter("KRadioAudioRecorderImpl");
        if (mKRadioAudioRecorderInter != null) {
            mLiveRecordhelper = new LiveRecordhelper();
            mKRadioAudioRecorderInter.initVR(AppDelegate.getInstance().getContext());
            mKRadioAudioRecorderInter.setVrStatusListener(new KRadioAudioRecorderInter.OnVRStatusListener() {
                @Override
                public void onSpeakBegin() {
                    Log.i(TAG, "onSpeakBegin--------->");
                }

                @Override
                public void onSpeakCompleted() {
                    Log.i(TAG, "onSpeakCompleted--------->");
                }
            });

            KradioRecorderInterface kradioRecorderInterface = mKRadioAudioRecorderInter.getRecorder();
            if (kradioRecorderInterface != null) {
                mKradioRecorderInterface = kradioRecorderInterface;
            } else {
                mKradioRecorderInterface = new KradioRecorder();
            }

        } else {
            //设置为考拉默认录音
            mKradioRecorderInterface = new KradioRecorder();
        }
    }

    private static final class INSTANCE_HOLDER {
        private static final HomeLiveManager INSTANCE = new HomeLiveManager();
    }

    public static HomeLiveManager getInstance() {
        return INSTANCE_HOLDER.INSTANCE;
    }

    public void setPresenter(HomeLivePresenter presenter) {
        mPresenter = presenter;
    }

    public LiveStatus getLiveStatus() {
        return mLiveStatus;
    }

    public void setLiveStatus(LiveStatus status) {
        this.mLiveStatus = status;
    }

    public LiveInfoDetail getShowingInfo() {
        return mShowingInfo;
    }

    public void setShowingInfo(LiveInfoDetail info) {
        this.mShowingInfo = info;
    }

    public LiveInfoDetail getPlayingInfo() {
        return mPlayingInfo;
    }

    public void setPlayingInfo(LiveInfoDetail mPlayingInfo) {
        this.mPlayingInfo = mPlayingInfo;
    }

    public ErrorStatus getErrorStatus() {
        return mErrorStatus;
    }

    public void setErrorStatus(ErrorStatus st) {
        this.mErrorStatus = st;
    }

//    private class MyHandler extends Handler {
//        @Override
//        public void handleMessage(Message msg) {
//            switch (msg.what) {
//                case WHAT_LOOP_LIVE_STATUS:
//                    if (mPresenter != null && mShowingInfo != null) {
//                        loopLiveStatus();
//                    }
//                    break;
//            }
//        }
//    }

//    public void loopLiveStatus() {
//        loopLiveStatus(PROGRAM_ID_EMPTY);
//    }

//    public void loopLiveStatus(long programId) {
//        if (mPresenter == null) return;
//        if (programId != PROGRAM_ID_EMPTY) {
//            mPresenter.getLiveInfo(programId);
//        } else if (mShowingInfo != null) {
//            mPresenter.getLiveInfo(mShowingInfo.getProgramId());
//        } else {
//            if (HomeLivePresenter.DEBUG_LIVE) {
//                Log.i(TAG, "loopLiveStatus: ERROR PROGRAM ID");
//            }
//        }
//        if (mShowingInfo != null) {
//            mPresenter.refreshChatRoomInfo();
//        }
//        if (mHandler.hasMessages(WHAT_LOOP_LIVE_STATUS)) {
//            mHandler.removeMessages(WHAT_LOOP_LIVE_STATUS);
//        }
//        mHandler.sendEmptyMessageDelayed(WHAT_LOOP_LIVE_STATUS, LIVE_STATUS_LOOP_INTERVAL);
//    }

//    public void stopLoopLiveStatus() {
//        mHandler.removeMessages(WHAT_LOOP_LIVE_STATUS);
//    }

    public void startRecordDeal() {
        if (mKRadioAudioRecorderInter != null) {
            //返回true 代表其他厂商使用sdk处理，直接获取通道成功，false代表考拉发送broadcast的形式
            boolean start = mKRadioAudioRecorderInter.onAudioRecordStart();
            if (start) {
                startRecord();
            }
        } else {
            startRecord();
        }
    }

    public String startRecord() {
        if (HomeLivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "startRecord");
        }

        if (mKradioRecorderInterface != null) {
            mKradioRecorderInterface.startRecord();
            mRecordStartTime = SystemClock.elapsedRealtime();
            mFilePath = mKradioRecorderInterface.getFilePath();
            mRecorderStatusObservable.setRecorderStatus(RecorderStatus.RECORDING);
            Log.i(TAG, "mKradioRecorderInterface startRecord");
        } else {
            mRecorderStatusObservable.setRecorderStatus(RecorderStatus.IDLE);
        }

        return null;

    }

    /**
     * @return 录音文件的路径，如果录音发生错误，返回null，如果录音时间过短，
     * 返回{@link LiveManager#RECORD_TIME_TOO_SHORT}
     */
    public String stopRecord() {
        return stopRecord(false);
    }

    /**
     * @param cancel 停止录音是否是要取消录音
     * @return 录音文件的路径，如果录音发生错误，返回null，如果录音时间过短，
     * 返回{@link LiveManager#RECORD_TIME_TOO_SHORT}
     */
    public String stopRecord(boolean cancel) {
        if (HomeLivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "stopRecord");
        }
        if (mKRadioAudioRecorderInter != null) {
            mKRadioAudioRecorderInter.onAudioRecordStop();
        }

        boolean stop = false;

        if (mKradioRecorderInterface != null) {
            stop = mKradioRecorderInterface.stopRecord();
            mRecordDuration = (int) (SystemClock.elapsedRealtime() - mRecordStartTime);
            Log.i(TAG, "mKradioRecorderInterface stopRecord");
        }
        Log.i(TAG, "mKradioRecorderInterface stopRecord status:" + stop);

        //兼容以前逻辑 stop failed 返回null
        if (!stop) {
            mRecorderStatusObservable.setRecorderStatus(RecorderStatus.IDLE);
            return null;
        }

        if (cancel) {
            cancel();
            return null;
        }
        if (mRecordDuration <= RECORD_MINIMUM_TIME) {
            mRecorderStatusObservable.setRecorderStatus(RecorderStatus.CANCEL);
            cancel();
            return RECORD_TIME_TOO_SHORT;
        }
        mRecorderStatusObservable.setRecorderStatus(RecorderStatus.FINISHED);
        return mFilePath;
    }

    private File createNewAudioFile() {
        if (!RECORDINGS_DIR.exists()) {
            RECORDINGS_DIR.mkdirs();
        }
        File file = new File(RECORDINGS_DIR, "Live-Leave-message" + EXTENSION);
        return file;
    }

    public int getRecordDuration() {
        return mRecordDuration;
    }

    /**
     * @return true 正在录音, false 不在录音
     */
    public boolean isRecording() {
        return mRecorderStatusObservable.getRecorderStatus() == RecorderStatus.RECORDING;
    }

    /**
     * @return true 正在试听, false 不在试听
     */
    public boolean isPlaying() {
        return mRecorderStatusObservable.getRecorderStatus() == RecorderStatus.LISTENING;
    }

    /**
     * @return true 录音刚刚完成，尚未进行其它操作
     */
    public boolean isFinished() {
        return mRecorderStatusObservable.getRecorderStatus() == RecorderStatus.FINISHED;
    }

    /**
     * @return true 试听过了录音，包括完整听完或者听了其中一部分，但尚未进行其它操作
     */
    public boolean isListened() {
        return mRecorderStatusObservable.getRecorderStatus() == RecorderStatus.LISTENED;
    }

    /**
     * @return true 空闲状态，可以录音，
     */
    public boolean isIdle() {
        return mRecorderStatusObservable.getRecorderStatus() == RecorderStatus.IDLE;
    }

    /**
     * @return true 正在上传录音文件
     */
    public boolean isUploading() {
        return mRecorderStatusObservable.getRecorderStatus() == RecorderStatus.UPLOADING;
    }

    /**
     * @return true 上传完成
     */
    public boolean isUploaded() {
        return mRecorderStatusObservable.getRecorderStatus() == RecorderStatus.UPLOADED;
    }

    /**
     * @return true 上传失败
     */
    public boolean isFailure() {
        return mRecorderStatusObservable.getRecorderStatus() == RecorderStatus.FAILURE;
    }

    public void startListen() {
        Log.w(TAG, "listen");
        mMediaPlayer = new MediaPlayer();
        mMediaPlayer.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
            @Override
            public void onCompletion(MediaPlayer mp) {
                mp.release();
                mMediaPlayer = null;
                mRecorderStatusObservable.setRecorderStatus(RecorderStatus.LISTENED);
                PlayerManagerHelper.getInstance().play(true);
            }
        });
        mMediaPlayer.setOnPreparedListener(new MediaPlayer.OnPreparedListener() {
            @Override
            public void onPrepared(MediaPlayer mp) {
                mRecorderStatusObservable.setRecorderStatus(RecorderStatus.LISTENING);
                mp.start();
            }
        });
        try {
            mMediaPlayer.setDataSource(mFilePath);
            mMediaPlayer.prepareAsync();
        } catch (IOException e) {
            if (HomeLivePresenter.DEBUG_LIVE) {
                Log.w(TAG, "listen", e);
            }
        }
        // 解决https://bugly.qq.com/v2/crash-reporting/crashes/bedf20cb76/40006/report?pid=1&search=&searchType=detail&bundleId=&channelId=dongfengqichen_chengdu&version=all&tagList=&start=0&date=all 问题
        catch (IllegalStateException e) {
            e.printStackTrace();
        }
    }

    public int getListenDuration() {
        if (mMediaPlayer != null) {
            return mMediaPlayer.getDuration();
        }
        return -1;
    }

    public String getFilePath() {
        return mFilePath;
    }

    public void stopListen() {
        if (HomeLivePresenter.DEBUG_LIVE) {
            Log.w(TAG, "stopListen");
        }
        if (mMediaPlayer != null && mMediaPlayer.isPlaying()) {
            mMediaPlayer.stop();
            mMediaPlayer.release();
            mMediaPlayer = null;
            mRecorderStatusObservable.setRecorderStatus(RecorderStatus.LISTENED);
            PlayerManagerHelper.getInstance().play(true);
        }
    }

    public void cancel() {
        stopListen();
        mRecorderStatusObservable.setRecorderStatus(RecorderStatus.IDLE);
        deleteFile();
    }

    public void deleteFile() {
        if (mFilePath == null) {
            return;
        }
        File file = new File(mFilePath);
        if (file.exists()) {
            file.delete();
        }
    }

    public void setRecorderStatus(RecorderStatus status) {
        mRecorderStatusObservable.setRecorderStatus(status);
    }

    public class RecorderStatusObservable extends Observable {

        RecorderStatusObservable(RecorderStatus status) {
            mRecordStatus = status;
        }

        private RecorderStatus mRecordStatus;

        public void setRecorderStatus(RecorderStatus status) {
            if (HomeLivePresenter.DEBUG_LIVE) {
                Log.i(TAG, "setRecorderStatus: " + status);
            }
            mRecordStatus = status;
            setChanged();
            notifyObservers(mRecordStatus);
        }

        public RecorderStatus getRecorderStatus() {
            return mRecordStatus;
        }
    }

    public void addRecorderStatusObserver(Observer o) {
        mRecorderStatusObservable.addObserver(o);
    }

    public void resetStatus() {
        setLiveStatus(LiveStatus.UNKNOWN);
        setErrorStatus(ErrorStatus.NO_ERROR);
        mRecorderStatusObservable.setRecorderStatus(RecorderStatus.IDLE);
    }

    public void removeRecorderStatusObserver(Observer o) {
        mRecorderStatusObservable.deleteObserver(o);
    }

    public void onLiveExit() {
        onLiveExit(false);
    }

    public void onLiveExit(boolean byUser) {
//        stopLoopLiveStatus();
        if (byUser) {
            playDefault();
        }
    }

    private void playDefault() {
        //todo 原来的逻辑是退出直播播放历史，正在修改是直播插播，不需要这个逻辑。
        io.reactivex.Observable.create(new ObservableOnSubscribe<HistoryItem>() {
            @Override
            public void subscribe(ObservableEmitter<HistoryItem> emitter) throws Exception {
                HistoryItem historyItemNewest = HistoryManager.getInstance().getLasted();
                Song song = MusicDaoManager.getInstance().queryLatest();
                if (historyItemNewest != null) {
                    if (song != null) {
                        if (song.getTimestamps() > historyItemNewest.getTimeStamp()) {
                            emitter.onComplete();
                            return;
                        }
                    }
                    emitter.onNext(historyItemNewest);
                } else if (song != null) {
                    emitter.onComplete();
                } else {
                    emitter.onError(new Exception("HistoryItem is null"));
                }
            }
        }).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<HistoryItem>() {
                    @Override
                    public void accept(HistoryItem historyItem) throws Exception {
                        if (StringUtil.equals(String.valueOf(PlayerConstants.RESOURCES_TYPE_BROADCAST), historyItem.getType())) {
                            BroadcastRadioSimpleData data = new BroadcastRadioSimpleData();
                            data.setBroadcastId(Long.valueOf(historyItem.getRadioId()));
                            data.setImg(historyItem.getPicUrl());
                            data.setName(historyItem.getRadioTitle());
                            data.setResType(PlayerConstants.RESOURCES_TYPE_BROADCAST);
                            PlayerManagerHelper.getInstance().addBroadcastRadioSimpleItem(data);
                        }else if (StringUtil.equals(String.valueOf(PlayerConstants.RESOURCES_TYPE_TV), historyItem.getType())){
                            BroadcastRadioSimpleData data = new BroadcastRadioSimpleData();
                            data.setBroadcastId(Long.valueOf(historyItem.getRadioId()));
                            data.setImg(historyItem.getPicUrl());
                            data.setName(historyItem.getRadioTitle());
                            data.setResType(PlayerConstants.RESOURCES_TYPE_TV);
                            PlayerManagerHelper.getInstance().addBroadcastRadioSimpleItem(data);
                        }

                        PlayerManagerHelper.getInstance().startHistory(historyItem);
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        //没有找到最后一次播放的记录
                    }
                }, new Action() {
                    @Override
                    public void run() throws Exception {
                    }
                });
    }

    public PlayItem toPlayItem(LiveInfoDetail detail) {
        PlayItem playItem = PlayListUtils.liveInfoToPlayItem(detail);
        playItem.addMapCacheData(PlayItemConstantsSon.KEY_LIVING_LOCATION, LivingStartListenReportEvent.POSITION_RECOMMENT);
        return playItem;
    }

    public void unRegisterResponseRecord() {
        if (mLiveRecordhelper != null) {
            mLiveRecordhelper.unRegisterResponseRecord();
        }
    }

    public void registerResponseRecord() {
        if (mLiveRecordhelper != null) {
            mLiveRecordhelper.registerResponseBroadcast();
        }
    }
}

