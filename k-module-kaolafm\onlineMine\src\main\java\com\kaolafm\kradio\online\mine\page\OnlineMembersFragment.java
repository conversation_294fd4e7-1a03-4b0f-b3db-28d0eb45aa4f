package com.kaolafm.kradio.online.mine.page;

import android.os.Bundle;
import androidx.constraintlayout.widget.ConstraintLayout;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewStub;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.kaolafm.base.utils.NetworkUtil;
import com.kaolafm.kradio.k_kaolafm.R; 
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.ui.online.BaseViewPagerFragment;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.kradio.onlineuser.ui.ILoginView;
import com.kaolafm.kradio.onlineuser.ui.LoginPresenter;
import com.kaolafm.kradio.purchase.PayManager;
import com.kaolafm.kradio.purchase.constant.PayConst;
import com.kaolafm.kradio.purchase.model.PayResult;
import com.kaolafm.kradio.purchase.observer.VipPayListener;
import com.kaolafm.kradio.user.UserInfoManager;
import com.kaolafm.opensdk.api.login.model.UserInfo;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonClickReportEvent;
  

import static com.kaolafm.report.event.ButtonClickReportEvent.ONLINE_BUTTON_JOIN_NOW;
import static com.kaolafm.report.event.ButtonClickReportEvent.ONLINE_BUTTON_RENEW;

/**
 * 会员中心
 */
public class OnlineMembersFragment extends BaseViewPagerFragment<LoginPresenter> implements ILoginView {
 
    ViewStub error_page; 
    ConstraintLayout unlogin; 
    ConstraintLayout login; 
    LinearLayout mineOrder; 
    ConstraintLayout bg; 
    TextView title; 
    TextView subtitle; 
    TextView btn; 
    ImageView icon;

    private View mErrorLayout;

    public OnlineMembersFragment() {
        // Required empty public constructor
    }

    @Override
    protected boolean isReportFragment() {
        return true;
    }

    public static OnlineMembersFragment newInstance() {
        OnlineMembersFragment fragment = new OnlineMembersFragment();
//        Bundle args = new Bundle();
//        args.putString(ARG_PARAM1, param1);
//        args.putString(ARG_PARAM2, param2);
//        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
//        if (getArguments() != null) {
//            mParam1 = getArguments().getString(ARG_PARAM1);
//            mParam2 = getArguments().getString(ARG_PARAM2);
//        }
    }

    @Override
    protected int getLayoutId() {
        return R.layout.online_fragment_members;
    }

    @Override
    protected LoginPresenter createPresenter() {
        return new LoginPresenter(this);
    }

    @Override
    public void initView(View view) {

        error_page=view.findViewById(R.id.error_page);
        unlogin=view.findViewById(R.id.online_fragment_unlogin);
        login=view.findViewById(R.id.online_fragment_login);
        mineOrder=view.findViewById(R.id.ll_mine_order);
        bg=view.findViewById(R.id.cl_bg);
        title=view.findViewById(R.id.tv_title);
        subtitle=view.findViewById(R.id.tv_subtitle);
        btn=view.findViewById(R.id.tv_btn);
        icon=view.findViewById(R.id.iv_icon);

        mineOrder.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onViewClicked(v);
            }
        });
        btn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onViewClicked(v);
            }
        });
    }

    @Override
    public void onResume() {
        super.onResume();
        showMembersUI();
        mPresenter.getUserInfo();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
//        UserInfoManager.getInstance().removeUserInfoStateListener(iUserInfoStateListener);
    }
 

    /**
     * 会员中心页面展示
     */
    public void showMembersUI() {
        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext())) {
            showErrorLayout("", true);
            return;
        }
        boolean isLogin = UserInfoManager.getInstance().isUserLogin();
        if (isLogin) {
            ViewUtil.setViewVisibility(login, View.VISIBLE);
            ViewUtil.setViewVisibility(mErrorLayout, View.GONE);
            ViewUtil.setViewVisibility(unlogin, View.GONE);
            int isVip = UserInfoManager.getInstance().getVip();
            if (isVip == 1) {
                showVipUI();
            } else {
                showLoginUI();
            }
        } else {
            showUnLoginUI();
        }
    }

    /**
     * 未登录页面
     */
    private void showUnLoginUI() {
        ViewUtil.setViewVisibility(unlogin, View.VISIBLE);
        ViewUtil.setViewVisibility(mErrorLayout, View.GONE);
        ViewUtil.setViewVisibility(login, View.GONE);
    }

    /**
     * 登录普通会员页面
     */
    private void showLoginUI() {
        bg.setBackground(ResUtil.getDrawable(R.drawable.online_members_unvip_bg));
        title.setTextColor(ResUtil.getColor(R.color.online_members_not_vip_text_color));
        title.setText(ResUtil.getString(R.string.mine_members_un_vip_title));
        subtitle.setTextColor(ResUtil.getColor(R.color.online_members_not_vip_text_color));
        subtitle.setText(ResUtil.getString(R.string.mine_members_un_vip_subtitle));
        btn.setText(ResUtil.getString(R.string.mine_members_un_vip_btn));
        btn.setBackground(ResUtil.getDrawable(R.drawable.online_members_unvip_btn_bg));
        icon.setBackground(ResUtil.getDrawable(R.drawable.online_members_unvip_icon));
    }

    /**
     * 登录Vip会员页面
     */
    private void showVipUI() {
        UserInfo userInfo = UserInfoManager.getInstance().getUserInfo();
        bg.setBackground(ResUtil.getDrawable(R.drawable.online_members_vip_bg));
        title.setTextColor(ResUtil.getColor(R.color.online_members_vip_text_color));
        title.setText(ResUtil.getString(R.string.mine_members_vip_title));
        subtitle.setTextColor(ResUtil.getColor(R.color.online_members_vip_text_color));
        subtitle.setText(String.format(getString(R.string.mine_members_vip_subtitle),
                userInfo.getVipTime(), userInfo.getVipRemainDays()));
        btn.setText(ResUtil.getString(R.string.mine_members_vip_btn));
        btn.setBackground(ResUtil.getDrawable(R.drawable.online_members_vip_btn_bg));
        icon.setBackground(ResUtil.getDrawable(R.drawable.online_members_vip_icon));
    }
 
    public void onViewClicked(View view) {
        int id = view.getId();
        if (!AntiShake.check(id)) {
            if (id == R.id.ll_mine_order) {
                showOrderFragment();
            } else if (id == R.id.tv_btn) {
                Log.e("rsq", "tv_btn");
                PayManager.getInstance()
                        .pay(PayConst.PAY_TYPE_VIP, null)
                        .addPayListener(null, new VipPayListener() {

                            @Override
                            public void payResponse(PayResult payResult, PlayItem playItem, Long vipTime) {
                                // 刷新会员信息
                                if (mPresenter != null) {
                                    mPresenter.getUserInfo();
                                }
                            }
                        });

                //点击按钮事件上报
                if (view instanceof TextView) {
                    String text = ((TextView) view).getText().toString();
                    if (ResUtil.getString(R.string.user_buy_vip).equals(text)) {
                        ButtonClickReportEvent event = new ButtonClickReportEvent(ONLINE_BUTTON_JOIN_NOW);
                        ReportHelper.getInstance().addEvent(event);
                    } else if (ResUtil.getString(R.string.user_vip_renew).equals(text)) {
                        ButtonClickReportEvent event = new ButtonClickReportEvent(ONLINE_BUTTON_RENEW);
                        ReportHelper.getInstance().addEvent(event);
                    }
                }
            }
        }
    }

    @Override
    public void failedToGetCode(ApiException e) {

    }

    @Override
    public void startCountdown() {

    }

    @Override
    public void loginSuccess() {
        showMembersUI();
    }

    @Override
    public void loginError() {
        showMembersUI();
    }

    @Override
    public void logoutSuccess() {
        showMembersUI();
    }

    @Override
    public void logoutSError() {
        showMembersUI();
    }

    @Override
    public void toast(String msg) {

    }

    private void showErrorLayout(String error, boolean clickToRetry) {
        if (mErrorLayout == null) {
            mErrorLayout = error_page.inflate();
            if (!TextUtils.isEmpty(error)) {
                TextView tvNetworkNosign = mErrorLayout.findViewById(R.id.tv_error);
                tvNetworkNosign.setText(error);
            }
            // 支持点击重试
            if (clickToRetry) {
                mErrorLayout.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        hideErrorLayout();
                        showMembersUI();
                    }
                });
            }
        }
        ViewUtil.setViewVisibility(mErrorLayout, View.VISIBLE);
        ViewUtil.setViewVisibility(unlogin, View.GONE);
        ViewUtil.setViewVisibility(login, View.GONE);
    }

    void hideErrorLayout() {
        ViewUtil.setViewVisibility(mErrorLayout, View.GONE);
    }

    @Override
    public String getPageId() {
        return Constants.PAGE_ID_MINE_MEMBERS;
    }

    public void showOrderFragment() {
        RouterManager.getInstance().jumpPage(RouterConstance.ACTIVITY_URL_MINE_ORDER);
    }
}