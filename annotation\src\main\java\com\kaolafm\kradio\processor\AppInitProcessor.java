package com.kaolafm.kradio.processor;

import com.kaolafm.kradio.lib.init.AppInit;
import com.squareup.javapoet.MethodSpec.Builder;
import com.squareup.javapoet.TypeName;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.lang.model.element.Element;
import javax.lang.model.element.TypeElement;

/**
 * 分组件初始化。将{@link AppInit}注解的类并实现{}
 * <AUTHOR>
 * @date 2019-09-12
 */

public class AppInitProcessor extends BaseProcessor {

    private static final String APP_INIT = "com.kaolafm.kradio.lib.init.AppInit";

    private static final String CLASS_NAME_SUFFIX = "AppInitItemContainer";

    public AppInitProcessor(CoreProcessor processor) {
        super(processor);
    }

    @Override
    public String getAnnotationName() {
        return APP_INIT;
    }

    @Override
    protected void insertStatementBefore(Set<TypeElement> elements, Builder constructorMethod) {
        List<TypeElement> typeElements = new ArrayList<>(elements);
        log("自然排序前="+elements);
        typeElements.sort((element1, element2) -> {
            AppInit appInit1 = element1.getAnnotation(AppInit.class);
            AppInit appInit2 = element2.getAnnotation(AppInit.class);
            return appInit2.priority() - appInit1.priority();
        });
        log("自然排序后，根据依赖排序前="+typeElements);
        elements.clear();
        Map<TypeElement, String[]> dependsMap = new HashMap<>();
        Map<String, TypeElement> elementMap = new HashMap<>();
        typeElements.forEach(typeElement -> {
            AppInit annotation = typeElement.getAnnotation(AppInit.class);
            String[] depends = annotation.dependOn();
            if (Util.isNotEmpty(depends)) {
                dependsMap.put(typeElement, depends);
            }
            elementMap.put(typeElement.getQualifiedName().toString(), typeElement);
        });

        if (!dependsMap.isEmpty()) {
            dependsMap.forEach((element, strings) -> {
                for (String dependName : strings) {
                    Element dependElement = elementMap.get(dependName);

                    if (typeElements.indexOf(dependElement) > typeElements.indexOf(element)) {
                        typeElements.remove(element);
                        typeElements.add(typeElements.indexOf(dependElement) + 1, element);
                    }
                }
            });
        }
        log("根据依赖排序后="+typeElements);
        elements.addAll(typeElements);

        constructorMethod.addStatement("super()");
    }

    @Override
    protected boolean validateAnnotateElement(Element element) {
        return true;
    }

    @Override
    public void processElement(TypeElement element, Builder constructorMethod) {
        AppInit appInit = element.getAnnotation(AppInit.class);
        constructorMethod.addStatement("add(new $T($L, $L, $S, Boolean.valueOf($S), new $T()))",
                getTypeName("com.kaolafm.kradio.lib.init.AppInitTask"),
                appInit.priority(),
                appInit.process(),
                appInit.description(),
                String.valueOf(appInit.isAsync()),
                getTypeName(element.getQualifiedName().toString()));
    }

    @Override
    protected String getClassName() {
        String moduleName = "_" + coreProcessor.getProcessingEnv().getOptions().get("moduleName")
                .replaceAll("-", "_");
        return Util.humpName(moduleName) + CLASS_NAME_SUFFIX;
    }

    @Override
    protected String getGeneratedPackageName() {
        return "com.kaolafm.kradio";
    }

    @Override
    boolean generateOneClass() {
        return true;
    }

    @Override
    protected TypeName getSuperclass() {
        return getTypeName("com.kaolafm.kradio.lib.init.AppInitTaskContainer");
    }
}
