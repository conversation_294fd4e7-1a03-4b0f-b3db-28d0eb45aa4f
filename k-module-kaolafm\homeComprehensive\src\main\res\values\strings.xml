<?xml version="1.0" encoding="utf-8"?>
<resources>

    <string name="comprehensive_main_activity_name">活动专区</string>
    <string name="comprehensive_main_activity_name2">活动\n专区</string>
    <string name="broadcast_program_info_time_joint_str">%1$s—%2$s</string>
    <string name="wrap_joint_str">%1$s\n%2$s</string>
    <string name="unknown_str">未知</string>
    <string name="suggest_str">推荐</string>
    <string name="similar_re_str">相似推荐</string>
    <string name="another_batch_str">换一批</string>
    <string name="media_hosts_str">主播&#12288;%1$s</string>
    <string name="media_detail_source_str">来源&#12288;%1$s</string>
    <string name="media_detail_singer_str">&#12288;&#12288;歌手&#12288;%1$s</string>
    <string name="media_detail_schools_str">&#12288;&#12288;流派&#12288;%1$s</string>
    <string name="media_detail_category_str">&#12288;&#12288;类别&#12288;%1$s</string>
    <string name="media_detail_album_str">专辑&#12288;%1$s</string>
    <string name="media_detail_brief_introduction_str">简介&#12288;%1$s</string>

    <string name="audio_duration_str">&#160;|&#160;时长&#160; </string>
    <string name="audio_duration_str_player">时长&#160; </string>

    <string name="audio_total_num_str">共%1$d个音频</string>
    <string name="album_audio_info_str">第%1$d期<!--&#8197;-->%1$s%2$s</string>
    <string name="today_str">今天</string>
    <string name="broadcast_playBack_str">回放</string>
    <string name="broadcast_living_str">直播中</string>
    <string name="broadcast_living_str2">直播</string>
    <string name="broadcast_not_beginning_str">未开始</string>


    <string name="exit_kradio_str">退出当前应用</string>
    <string name="backgroud_kradio_str">当前应用退到后台</string>


    <string name="single_mode_str">已切换到单曲循环模式</string>
    <string name="random_all_mode_str">已切换到随机播放模式</string>
    <string name="sequence_all_mode_str">已切换到顺序播放模式</string>


    <string name="account_have_bound">已绑定</string>
    <string name="my_subscription">我订阅的</string>
    <string name="one_key_listen">一键收听</string>
    <string name="my_like_format">我喜欢的（%d）</string>
    <string name="recently_listened">最近收听</string>
    <string name="listen_history">收听历史</string>
    <string name="collection_songmenu">收藏歌单</string>
    <string name="kaolafm">云听</string>
    <string name="scan_to_login_by_wechat">微信扫码登录</string>
    <string name="scan_to_login_by_qq">QQ扫码登录</string>
    <string name="read_and_agree_to_the_terms_of_service">QQ与微信账号的资产和权限不互通\n扫码登录即代表阅读并同意服务条款</string>
    <string name="read_and_agree_to_the_terms_of_service_single_line">QQ与微信账号的资产和权限不互通扫码登录即\n代表阅读并同意服务条款</string>
    <string name="music_wechat_login">音乐账号-微信登录</string>
    <string name="music_qq_login">音乐账号-QQ登录</string>
    <string name="no_subscription_and_listen_recommend2">暂无订阅内容</string>
    <string name="login_with_wechat_scan">用微信扫一扫进行扫码登录</string>
    <string name="login_with_qq_scan">用QQ扫一扫进行扫码登录</string>
    <string name="login_other">您的账号在别的设备上登录</string>


    <string name="pgc">智能电台</string>
    <string name="onlybroadcast">广播</string>
    <string name="choose_age">请选择年份</string>
    <string name="broadcast">广播电台</string>
    <string name="is_playing">正在播放：</string>
    <string name="last_play">上次播放：</string>
    <string name="unknown">未知</string>
    <string name="audio_num">%d期&#160;:&#160;</string>
    <string name="history_count">有%1$d条收听历史</string>
    <string name="subscription_count">有%1$d条订阅信息</string>


    <string name="live">直播</string>
    <string name="refresh_list">更新列表</string>
    <string name="login_by_scan_qr_with_kaolafm">使用云听手机端扫码登录</string>
    <string name="download_kaolafm_app">打开手机云听APP \n 扫描二维码登录</string>
    <string name="download_kaolafm_app_gxy">广西云APP \n 扫描二维码登录</string>
    <string name="download_kaolafm_app_qr_title_third">第三方账号登录</string>
    <string name="download_kaolafm_app_qr_name_gxy">广西云</string>
    <string name="mine">我的</string>
    <string name="cancel">取消</string>
    <string name="ok">确定</string>
    <string name="no_listening_history2">暂无收听历史</string>
    <string name="home_gallery_skeleton_item_text">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</string>
    <string name="qq_music">QQ音乐</string>
    <string name="offical_charts">官方榜</string>
    <string name="i_like">我喜欢</string>
    <string name="what_i_like">我喜欢的</string>
    <string name="qq_music_account">QQ音乐账号</string>
    <string name="kaolafm_account">考拉账号</string>
    <string name="unbinding">解除绑定</string>
    <string name="private_fm">私人FM</string>
    <string name="music_logout_title_str">退出登录</string>
    <string name="user_kickout">设备数量已达上限 \n 请重新登录!</string>

    <string name="xmly_logout_title_str">喜马拉雅账号</string>
    <string name="xmly_logout_btn_str">退出登录</string>
    <string name="xmly_login_title_str">使用喜马拉雅手机端扫码登录</string>
    <string name="xmly_error_need_login_str">喜马拉雅账号绑定失效,请重新绑定</string>

    <string name="xmly_sb_title_str">喜马拉雅</string>
    <string name="xmly_sb_all_number_str">共%1$d条订阅</string>
    <string name="player_failure">该资源暂不支持播放，试一试其他的吧</string>
    <string name="voice_earch">语音搜索</string>
    <string name="ximalaya">喜马拉雅</string>

    <string name="lave">剩余%s</string>
    <string name="no_history">没有播放记录</string>
    <string name="cancel_login">取消登录</string>
    <string name="login_success">登录成功</string>
    <string name="no_favorite_songmenu">暂无收藏歌单</string>
    <string name="qqmusic_clauses_of_service">QQ音乐服务许可协议</string>
    <string name="res_drop_off">该资源已经下架</string>
    <string name="please_login_qqmusic">请先登录QQ音乐</string>
    <string name="music">音乐</string>
    <string name="bind_success">绑定成功</string>
    <string name="bind_failed">绑定异常</string>

    <string name="dsv_ex_msg_dont_set_lm">You should not set LayoutManager on DiscreteScrollView.class instance. Library uses a special one. Just don\'t call the method.</string>
    <string name="dsv_ex_msg_adapter_wrong_recycler">InfiniteScrollAdapter is supposed to work only with DiscreteScrollView</string>
    <string name="is_not_online">该资源暂不支持播放，试一试其他的吧</string>
    <string name="ijkplayer_network_failed">当前网络不稳定，请稍后再试</string>
    <string name="no_my_like">暂无喜欢的单曲列表</string>
    <string name="no_broadcast_res_can_play">该时段暂无节目资源</string>
    <string name="no_copyright">因版权原因，暂时无法播放</string>

    <string name="user_title">我的</string>
    <string name="user_setting">设置</string>
    <string name="personlityrecommendation_title">完善资料，为你推荐更适合的内容</string>
    <string name="personlityrecommendation_sex">选择你的性别</string>
    <string name="personlityrecommendation_age">选择你的年龄</string>
    <string name="personlityrecommendation_man">男</string>
    <string name="personlityrecommendation_femail">女</string>
    <string name="personlityrecommendation_intest_submit">一键开启首页收听</string>
    <string name="user_person_center_aboutus_str">关&#8197;于&#8197;我&#8197;们</string>
    <string name="user_person_center_performance_setting">车机适配</string>
    <string name="user_person_center_count_suggest_str">意&#8197;见&#8197;反&#8197;馈</string>
    <string name="user_person_center_count_door">个性化推荐</string>

    <string name="user_history_clear_str">一键清空</string>
    <string name="my_order">我的订单</string>
    <string name="no_order_info">暂无订单信息</string>
    <string name="order_help">如有疑问，欢迎拨打客服热线：010-xxxxxxxx</string>

    <string name="user_synchronize_start">数据同步中……</string>
    <string name="user_synchronize_success">同步成功</string>
    <string name="user_synchronize_history_failed">历史同步失败</string>
    <string name="user_synchronize_subscription_failed">订阅同步失败</string>

    <string name="user_qr_error_no_network_msg">请您在网络良好时 \n 点击重新生码</string>
    <string name="user_qr_expire_msg">二维码已过期 \n 请点击刷新</string>
    <string name="network_error_toast">网络异常，请检查网络！</string>
    <string name="network_error_toast_no_exclamation_point">网络异常，请检查网络</string>
    <string name="user_qr_expire_toast">验证码已失效!</string>
    <string name="user_qr_loading">加载中...</string>

    <string name="person_center_suggest_title_str">使用微信扫描与听友交流</string>
    <string name="error_subcategory_is_null">子分类为空</string>


    <string name="age_90_0">1990</string>
    <string name="age_90_1">1991</string>
    <string name="age_90_2">1992</string>
    <string name="age_90_3">1993</string>
    <string name="age_90_4">1994</string>
    <string name="age_90_5">1995</string>
    <string name="age_90_6">1996</string>
    <string name="age_90_7">1997</string>
    <string name="age_90_8">1998</string>
    <string name="age_90_9">1999</string>

    <string name="age_80_0">1980</string>
    <string name="age_80_1">1981</string>
    <string name="age_80_2">1982</string>
    <string name="age_80_3">1983</string>
    <string name="age_80_4">1984</string>
    <string name="age_80_5">1985</string>
    <string name="age_80_6">1986</string>
    <string name="age_80_7">1987</string>
    <string name="age_80_8">1988</string>
    <string name="age_80_9">1989</string>

    <string name="age_70_0">1970</string>
    <string name="age_70_1">1971</string>
    <string name="age_70_2">1972</string>
    <string name="age_70_3">1973</string>
    <string name="age_70_4">1974</string>
    <string name="age_70_5">1975</string>
    <string name="age_70_6">1976</string>
    <string name="age_70_7">1977</string>
    <string name="age_70_8">1978</string>
    <string name="age_70_9">1979</string>

    <string name="age_60_0">1960</string>
    <string name="age_60_1">1961</string>
    <string name="age_60_2">1962</string>
    <string name="age_60_3">1963</string>
    <string name="age_60_4">1964</string>
    <string name="age_60_5">1965</string>
    <string name="age_60_6">1966</string>
    <string name="age_60_7">1967</string>
    <string name="age_60_8">1968</string>
    <string name="age_60_9">1969</string>

    <string name="age_50_0">1950</string>
    <string name="age_50_1">1951</string>
    <string name="age_50_2">1952</string>
    <string name="age_50_3">1953</string>
    <string name="age_50_4">1954</string>
    <string name="age_50_5">1955</string>
    <string name="age_50_6">1956</string>
    <string name="age_50_7">1957</string>
    <string name="age_50_8">1958</string>
    <string name="age_50_9">1959</string>

    <string name="age_00_0">2000</string>
    <string name="age_00_1">2001</string>
    <string name="age_00_2">2002</string>
    <string name="age_00_3">2003</string>
    <string name="age_00_4">2004</string>
    <string name="age_00_5">2005</string>
    <string name="age_00_6">2006</string>
    <string name="age_00_7">2007</string>
    <string name="age_00_8">2008</string>
    <string name="age_00_9">2009</string>


    <string name="search">搜索</string>
    <string name="search_hint">请输入电台、专辑、广播名称</string>
    <string name="comprehensive_home_search_hint">搜搜电台、专辑、广播</string>

    <string name="search_history">搜索历史</string>
    <string name="clear_search_history">清空记录</string>
    <string name="toast_plz_input_keyword">请输入关键词</string>
    <string name="search_no_result">暂无相关内容</string>
    <string name="are_you_sure_to_clear_your_search_history">确定清空搜索历史？</string>
    <string name="hot_search_words">热门搜索</string>
    <string name="comprehensive_hot_search_refreshing_text">正在刷新</string>
    <string name="comprehensive_hot_search_loading_text">正在加载</string>
    <string name="comprehensive_hot_recommend_txt">为您推荐</string>
    <string name="comprehensive_hot_recommend">为您推荐%s</string>
    <string name="comprehensive_search_input_more_toast">最多支持搜索100字</string>

    <string name="no_net_retry">加载超时,请点击重试</string>
    <string name="logout_str">退出绑定</string>
    <string name="network_has_linked">网络已连接</string>
    <string name="user_center_image_setting">图片设置</string>

    <!-- 激活 -->
    <string name="activation_k_radio_normal_str">欢迎激活K-Radio音频服务</string>
    <string name="activation_k_radio_normal_title_str">开启你的个性化娱乐旅程</string>
    <string name="activation_str">激活</string>
    <string name="activation_protocol_title_str">用户协议</string>
    <string name="person_center_aboutus_details_str">中央广播电视总台声音新媒体平台云听，以资讯、知识、文化为内容战略方向，集纳总台精品节目，聚合总台及全国各地广播频率直播流，持续生产文化类、知识类优质IP节目、有声资讯（云听资讯）和高品质有声书。
        云听已覆盖手机、车机、平板电脑、智能穿戴设备等多终端、全应用场景，致力于为用户提供全场景的声音产品和服务。</string>
    <string name="privacy_policy_content_str">1.云听（以下简称本APP）是央广新媒体文化传媒（北京）有限公司开发的一款车载音频客户端，旨在为用户提供优质的音频服务。所有车载客户端用户在下载并使用本APP软件时均被视为已经仔细阅读本声明并完全同意本声明之内容。凡以任何方式登陆本APP，或直接、间接使用本APP内容者，均被视为自愿接受本APP相关声明的约束。\n\n
2.本APP转载的音频内容并不代表本APP及本公司之意见及观点，也不代表本APP及本公司赞同其观点或证实其内容的真实性，其真实性、合法性由音频制作者负责，本APP及本公司不提供任何保证，并不承担任何法律责任。\n\n
3.用户明确并同意其使用本APP所存在的风险及因此而产生的后果将均由用户本人承担，本公司对此不承担任何法律责任。\n\n
4.因用户不当使用本APP而导致的任何意外、疏忽、车机（车身）损毁、合约毁坏、诽谤、版权或其他知识产权侵权纠纷等及其所造成的任何损失，本APP及本公司不承担任何法律责任。\n\n
5.对于因不可抗力或因黑客攻击、通讯线路中断等本APP及本公司不能控制的原因造成的网络服务中断或其他缺陷，导致用户不能正常使用本APP软件，本APP及本公司不承担任何责任，但将尽力减少因此给用户造成的损失或影响。\n\n
6.本声明未涉及的问题请参见国家有关法律法规，当本声明与国家有关法律法规冲突时，以国家法律法规为准。\n\n
7.本APP相关声明版权及其修改权、更新权和最终解释权均属本公司所有。\n\n </string>

    <string name="person_center_can_see_can_say_details_str">所见即可说可以帮助您在不方便双手触控的情况下操控应用，您所见的可点击按钮均可通过说出对应的指令来模拟点击。例如说出“全部”即可打开全部分类列表，如您想要收听当前页面的中国之声节目，可直接说出节目名称“中国之声”。\n\n如下所示图标按钮指令，帮助您快速使用所见即可说功能</string>

    <string name="person_center_title_str">我<!--&#8197;-->%1$s的</string>
    <string name="person_center_performance_setting">车<!--&#8197;-->%1$s机<!--&#8197;-->
        %2$s适<!--&#8197;-->%3$s配</string>
    <string name="person_center_aboutus_service_str">服务协议</string>
    <string name="person_center_aboutus_private_str">隐私政策</string>
    <string name="terms_of_use_content_str">
总则\n\n

请申请注册成为用户或者会员的您（以下简称"用户"）仔细阅读本服务协议（以下简称“本协议”）全部条款，并确认完全了解本协议之规定。\n\n

如果用户在阅读本协议后，勾选了本协议前选项框视作"同意"，将视为用户签署了本协议，表明自愿接受本协议全部条款的约束，本协议将构成用户与央广新媒体文化传媒（北京）有限公司及其主办的云听（包含云听移动客户端、在线开放平台及其他现在或未来开发或升级换代的自有平台、网站，以下合称："看吧宽频"）之间直接有约束力的法律文件。\n\n
用户直接或通过各类方式（如站外API引用等）间接使用云听服务和数据的行为，都将被视作已无条件接受本声明所涉全部内容；若用户对本声明的任何条款有异议，请停止使用云听所提供的全部服务。\n\n


第一条 充分授权\n\n

用户确认本协议之效力、解释、变更、执行与争议解决均适用中华人民共和国大陆地区法律，且用户应当是具备完全民事权利能力、民事行为能力的自然人、法人或其他组织。如果用户代表其雇主或单位，请在签署前，确认并保证已获得签署本协议的充分授权。\n\n


第二条 完全了解\n\n

云听是一家网络音频服务提供商，可针对不同的传播渠道为用户直接上载、管理、传播其内容提供服务，上述传播渠道包括但不限于：\n

   （1）用户自己拥有合法权利的网站；\n
   （2）通过云听的服务；\n
   （3）云听提供的可以使用的其他渠道。\n\n

云听可能对以上服务进行更改、更新或提高，用户应承诺当用户认为该等更改、更新或提供服务不符合用户需求或认为对用户会产生任何不利影响的，应停止使用云听提供的所有服务。\n\n


第三条 内容上传\n\n
用户在以各种方式使用云听服务和数据（包括但不限于发表、宣传介绍、转载、浏览及利用云听或云听用户发布内容）的过程中，不得以任何方式利用云听直接或间接从事违反中国法律、以及社会公德的行为，且不得发布含有以下内容的言论：\n
   （1）违反国家法律、危害国家安全统一、社会稳定、公序良俗、社会公德以及侮辱、诽谤、淫秽或含有任何性或性暗示的、暴力的内容；\n
   （2）侵害他人名誉权、肖像权、知识产权、商业秘密等合法权利的内容；\n
   （3）涉及他人隐私、个人信息或资料的；\n
   （4）骚扰、广告信息及垃圾信息；\n
   （5）其他违反法律法规、政策及公序良俗、社会公德或干扰视频正常运营和侵犯其他用户或第三方合法权益内容的信息。\n\n

云听有权对违反上述承诺的内容予以删除，并有权随时拒绝、限制用户使用该账号，或者注销该账号。因此产生的一切后果由用户自行承担。用户使用的云听记录，将可能被保存作证据使用。云听有权将用户违反法律或侵犯第三方权利或权益的记录报告给行政主管部门。\n\n

用户应充分了解并同意，保证其上传的内容符合前述规定。云听并无义务主动对用户上传的内容进行任何形式的检查、编辑与修改。云听将尽可能保存用户上传的合法内容，但不承诺将为用户保存其上传的内容，用户应自行就前述内容备份。\n\n

用户应充分了解并同意，云听提供的用户上传、分享、传播信息平台，仅用于用户上传、分享、传送及获取信息，该平台或通过该平台所传送的任何内容并不反映云听的观点、立场或政策，云听对此不承担任何责任。同时，用户应对该平台的其他用户提供的内容自行加以判断，并承担因使用该内容而引起的所有风险，包括因对内容的正确性、完整性或实用性的依赖而产生的风险，云听对此不承担任何法律责任。\n\n


第四条 注册账号\n\n
用户在云听注册账号需按照云听的要求设置密码。用户可按照云听的要求修改用户账号的密码，但不可修改账号。\n\n

用户注册账号时，应按照云听的提示及要求填写或提供资料、信息，并确保用户身份及信息的真实性、正确性及完整性；如果资料发生变化，应及时更改。\n\n

用户同意并承诺：\n
   （1） 不故意冒用他人信息注册账号；\n
   （2） 未经他人合法授权不以他人名义注册账号；\n
   （3） 不使用色情、暴力或侮辱、诽谤他人等违反公序良俗的词语注册账号。\n\n

如用户违反前述规定，云听有权随时拒绝、限制用户使用该账号，或者注销该账号。因此产生的一切后果由用户自行承担。\n\n

用户完成账号注册后，云听将向用户提供账号及初始密码信息，此后，用户应自行保管及维持密码及账号的安全。用户应自行使用该账号，并对任何人利用用户的账号及密码所进行的活动负完全的责任。\n\n

用户应了解，在账号和密码匹配时，云听无法对非法或未经用户授权使用其账号及密码的行为作出甄别，因此，云听对任何使用用户账号和密码登录并使用云听的行为不承担任何责任。用户应妥善保管自己的账号及密码，因用户自身原因丢失账号和密码及其他重要信息，由用户自行承担一切不利后果。\n\n

用户同意并承诺：\n\n
   （1） 当用户的账号或密码遭到未获授权的使用，或者发生其他任何安全问题时，用户会立即通过书面形式或云听官方服务热线（010-61934392）通知到云听；\n
   （2） 当用户每次上网或使用其他服务完毕后，会将有关账号等安全退出；\n\n

云听有权根据自己的判定，在怀疑账号被不当使用时，拒绝账号使用或限制账号使用或注销该账号。如用户连续180天未以账号登录云听，则云听有权根据自己的判定，注销该账号且无需事先向用户发送通知，因此产生的一切后果由用户承担。\n\n


第五条 个人信息\n\n
云听承诺，不以非法方式披露用户依照云听规定的方式标明应予保密的用户个人信息。用户了解并同意，云听可依照法律或司法、行政机关的强制性命令对第三方披露用户的个人信息且无需事先向用户发出通知。\n\n

用户了解并同意，云听在下述情形下可利用用户的个人信息：\n
   （1） 在紧急情况下，为维护用户及公众的权益。\n
   （2） 为维护云听的著作权、商标权、专利权及其他任何合法权利或权益。\n
   （3） 在进行促销或抽奖时，云听可能会与赞助商共享用户的个人信息，在这些情况下云听会在发送用户信息之前进行提示，并且用户可以通过明确表示不参与活动而终止传送过程。\n
   （4） 为获取第三方数据而将用户信息与第三方数据匹配。\n
   （5） 将用户数据用于统计，以便向未来的合作伙伴、广告商及其他第三方以及为了其他合法目的而描述云听的服务。\n\n


第六条 授权使用\n\n
用户同意：凡用户向云听上传内容，用户都在上传之同时授权云听以非专有的方式使用用户上传内容，云听有权自行或转授权他人对前述内容进行复制、编辑、修改、展示及网络传播；前述授权是免费的、无限期的。\n\n

用户了解并同意，云听可能存在大量由第三方享有权利的内容；用户不会违反法律而传播任何用户不明确享有合法权利的内容。\n\n

云听可以随时依其自身判断指定用户不应传播的第三方内容。\n\n

用户应自行对从云听获悉或获取的第三方内容进行分析、甄别、判断、依法使用并合理预防其风险。\n\n

云听不保证用户接受的任何第三方内容具有合法性或真实性或准确性或安全性，也不保证其构成任何可靠的或全面的知识系统，也不对因存储或传送第三方内容过程中产生的延误、错误、遗漏、不准确、或由此产生的任何直接或间接损害，向用户赔偿或补偿或承担法律责任。\n\n


第七条 第三方权利\n\n
如果第三方主张用户所上传的内容违反法律或侵犯第三方权利或利益时，云听有权不经实质性审查而直接删除用户所上传的内容、或断开用户所提供的链接。在此情形下，云听有权将用户的联系信息或注册信息提供给该第三方。如用户提供的联系信息有效，云听可能在第三方主张权利时，向用户转送第三方的主张或其资料或向用户发送通知。\n\n

如果第三方主张用户所上传的内容违反法律或侵犯第三方权利或利益时，云听有权不经实质性审查而直接删除用户所上传的内容、或断开用户所提供的链接。在此情形下，云听有权将用户的联系信息或注册信息提供给该第三方。如用户提供的联系信息有效，云听可能在第三方主张权利时，向用户转送第三方的主张或其资料或向用户发送通知。\n\n
如果用户接到云听向用户转送的第三方主张或其资料或向用户发送的通知，但认为用户的行为合法且不侵犯第三方权利或利益时，用户应向云听提供下述文件及信息，并保证其真实、合法、完整、有效：\n
   （1） 用户所要求恢复的内容或链接；\n
   （2） 可证明用户的身份或用户单位的身份的文件；\n
   （3） 用户的联系方式、地址；\n
   （4） 可证明用户就第三方所指的资料或信息享有合法权利、或不构成侵权的文件。\n\n

云听在接收到用户的上述文件及信息后，有权依其自身判断，自行决定是否恢复被删除的内容或恢复被断开的链接。\n\n


第八条 推送商业信息\n\n
用户了解并接受，云听为维护其运营所经营，可能进行下述商业活动，如用户拒绝接受上述信息，可向云听表明其拒绝接受的态度，云听将停止向用户发送相应信息。\n
   （1） 通过电子邮件、客户端、网页或其他合法方式向用户发送商品促销或其他相关商业信息。\n
   （2） 通过增值服务系统或其他方式向用户发送的相关服务信息或其他信息，其他信息包括但不限于通知信息、宣传信息、广告信息等。\n\n


第九条 善意使用\n\n
用户同意，仅以非商业目的使用云听；不对云听的任何部分或全部进行商业性质利用、复制、拷贝、出售、调查、广告，或将云听用于其他任何商业目的或商业性质的活动；但云听与用户另行签订有协议或云听另行指定可供用户使用的特定商业服务除外。\n\n


第十条 服务保障\n\n
云听尽可能保证其稳定运行。\n\n

用户应理解并同意，因法律、政策、技术、经济、管理的原因，除非用户和云听另有约定，云听不会因以下情形出现而对用户承担责任：\n
   （1） 云听无法使用或中断使用或无法完全适合用户的使用要求。\n
   （2） 云听受到干扰，无法及时、安全、可靠运行，或出现任何错误。\n
   （3） 经由云听取得的任何产品、服务或其他材料不符合用户的期望。\n
   （4） 用户资料遭到未经授权的使用或修改。\n\n


第十一条 服务中止或终止\n\n
本协议或本协议项下云听所提供的服务可在下述情形下部分或全部中止或终止：\n
   （1） 因法律规定，或云听服从行政命令或司法判令的要求。\n
   （2） 用户违反本协议。\n
   （3） 云听认为应予终止的其他情形。\n\n

在一般情形下，云听会提前按照用户提供的联系方式以电子邮件或短信或其他电子方式通知用户，服务将中止或终止。用户应了解并同意，在紧急情况或特殊情况下，云听可不经通知即中止或终止服务。\n\n

在本协议或本协议项下云听提供的服务中止或终止时，云听有权\n
   （1） 拒绝用户登录云听；\n
   （2） 删除用户信息；\n
   （3） 删除用户上传的内容。\n\n

用户同意，不因本协议或本协议项下云听所提供的服务中止或终止，而要求央广新媒体文化传媒（北京）有限公司或云听向用户作出赔偿或补偿或承担任何其他责任。\n\n


第十二条 法律适用\n\n
用户在使用云听时，应遵守中华人民共和国大陆地区法律。如无相关法律规定的，则应参照通用国际商业惯例和（或）行业惯例。\n\n


第十三条 用户责任\n\n
用户应就所上传的内容承担全部法律责任；无论前述责任是因侵犯第三方权利所引起的、或因用户违反与第三方或本协议引起的、或因用户违反法律引起的；前述责任包括对云听或第三方所承担的民事责任、或行政机关要求承担的行政责任或刑事责任。\n\n

用户同意，如因用户违反法律规定或本协议规定给云听造成损失，用户将充分赔偿云听所遭受的损失、包括其直接损失、间接损失、预期利益损失等一切损失。\n\n

用户承诺，如因第三方向用户主张权利而导致用户承担责任，或用户承担行政责任或刑事责任的，用户不以此为理由追究云听的责任。\n\n

如用户应向云听承担责任，前述责任不因本协议或本协议项下的服务被终止而免除。\n\n


第十四条 协议变更\n\n
云听可能不时发布针对用户的相关协议，并可能将该相关协议作为对本协议的补充或修改而将其内容作为本协议的一部分，并以网站公示的方式进行公告。请用户及时关注并阅读相关协议。若您在前述变更公告后继续使用云听服务的，即表示您已经阅读、理解并接受经修订的协议和规则。如您不同意相关变更，应当立即停止使用云听服务。\n\n


第十五条 纠纷解决\n\n
用户和云听均同意，因本协议解释或执行引起的任何争议，双方应首先友好协商解决。协商不成时，则任一方均可将争议提交北京仲裁委员会依据其届时有效的仲裁规则以仲裁方式解决。仲裁地点为北京，仲裁语言为中文。仲裁裁决为终局的，对各方均有法律约束力。\n\n

如用户点击“同意”，则本协议将立即生效，并构成用户和央广新媒体文化传媒（北京）有限公司及其主办的云听之间有约束力的法律文件。\n\n
请用户再次确认用户已全部阅读并充分理解上述协议。\n\n</string>


    <string name="person_center_aboutus_gongzhonghao_str"></string>
    <string name="person_center_aboutus_mail_str"></string>
    <string name="exit_app">再点击一次，退出APP</string>

    <string name="state_living">直播中</string>
    <string name="state_playback_generating">回放准备中</string>
    <string name="state_playback">回放中</string>

    <string name="comprehensive_playerbar_state_living">直播中</string>
    <string name="comprehensive_playerbar_state_playback">回听</string>
    <string name="comprehensive_playerbar_state_purchased">已购</string>
    <string name="comprehensive_playerbar_state_audition">试听</string>

    <string name="comprehensive_live_cannot_switch_program">直播节目不支持上下个切换</string>
    <string name="sure">确定</string>
    <string name="cancle">取消</string>

    <string name="all_ctg_str">全部</string>
    <string name="next_step_str">下一步</string>

    <string name="my_subscription_str">我的订阅</string>


    <string name="no_active_str">未激活</string>

    <string name="no_login_sub_str">登录后显示订阅内容</string>
    <string name="no_login_buttom_str">立即登录</string>

    <string name="no_login_purchased_str">登录后显示已购内容</string>
    <string name="purchased_count">有%1$d条已购内容</string>
    <string name="no_purchased">暂无已购信息</string>
    <string name="vip_btn">VIP免费听</string>
    <string name="vip_btn2">购买专辑</string>
    <string name="vip_purchased_text">已购</string>
    <string name="launch_notice">云听使用提示</string>
    <string name="launcher_agreement0">用户服务协议</string>
    <string name="launcher_agreement1">用户隐私政策</string>
    <string name="launch_notice_content">1.欢迎您使用云听，在使用本软件前，请您认真阅读并充分理解《用户服务协议》和《用户隐私政策》，开始使用后，即表示您已同意前述文件内说明的条款及政策。
        \n2.请不要在驾驶车辆的过程中，同时操作或注视本软件，务必在确保安全的前提下使用本软件，以防发生交通事故或其他危险情况。
        \n3.本软件的部分功能，需联网后方可使用。产生的流量费用具体请咨询当地的运营商。
        \n4.为了保证您的正常使用，在使用过程中，云听需要获取到以下的权限：访问网络、获取用户麦克风、储存空间、设备信息、获取位置等权限。这些权限云听并不会默认开启，只有使用者同意并授权之后才会生效。未经授权我们不会收集、处理或泄露您的个人信息。</string>
    <string name="start_using">开始使用</string>
    <string name="disagree_exit">不同意并退出</string>
    <string name="launcher_notice_tip">请勾选页面下方的“同意《用户服务协议》\n《云听个人信息隐私协议》”</string>
    <string name="launch_agreement_tip">阅读并同意以上条款</string>
    <string name="launcher_agreement_first_toast">阅读并勾选同意后，即可开始使用云听车载服务</string>

    <string name="order_vip_start_time">会员生效时间：</string>
    <string name="order_vip_end_time">会员到期时间：</string>
    <string name="order_pay_type">支付方式：</string>
    <string name="order_pay_num">订单编号：</string>
    <string name="order_pay_time">支付时间：</string>
    <string name="upload_pay_time">下单时间：</string>

    <string name="comprehensive_unsupport_change_program">暂不支持节目切换</string>
    <string name="no_next_program">下一时段节目，敬请期待</string>
    <string name="no_next_audio">当前播放音频为专辑最后一集</string>
    <string name="no_pre_audio">当前播放音频为专辑第一集</string>
    <string name="no_pre_program">更多精彩回放，可使用手机云听APP收听哦</string>
    <string name="broadcast_tomorrow">节目未开始，敬请期待</string>
    <string name="no_next_broadcast">当前播放电台为列表最后一个</string>
    <string name="no_pre_broadcast">当前播放电台为列表第一个</string>
    <string name="local_broadcast_no_permission">您未授予云听位置权限，因此无法使用本地广播功能。\n请为云听授予位置权限。</string>    <string name="broadcast_no_program">该电台暂无节目单</string>

    <string name="comprehensive_player_sort_positive">正序</string>
    <string name="comprehensive_player_sort_negative">倒序</string>
    <string name="comprehensive_player_playing_title">正在直播：%s</string>

    <string name="video_total_num_str">共%1$d个视频</string>
    <string name="video_no_more_pre">当前播放视频为专辑第一集</string>
    <string name="video_no_more_next">当前播放视频为专辑最后一集</string>
</resources>
