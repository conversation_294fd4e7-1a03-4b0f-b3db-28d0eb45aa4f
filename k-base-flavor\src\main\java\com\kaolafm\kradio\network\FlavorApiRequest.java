package com.kaolafm.kradio.network;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.network.model.DefaultPlayData;
import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.http.core.HttpCallback;
import java.util.List;

/**
 * 如果是多个渠道使用的网络请求可以添加至此处统一使用
 */
public class FlavorApiRequest extends BaseRequest {

    private final FlavorApiServices mFlavorApiServices;

    private static final class FlavorApiRequestHolder {
        private static final FlavorApiRequest INSTANCE = new FlavorApiRequest();
    }

    public static FlavorApiRequest getInstance() {
        return FlavorApiRequestHolder.INSTANCE;
    }

    private FlavorApiRequest() {
        mFlavorApiServices = obtainRetrofitService(FlavorApiServices.class);
    }

    public void getDefaultPlayInfo(HttpCallback<DefaultPlayData> callback) {
        doHttpDeal(mFlavorApiServices.getDefaultPlayInfo(),
                baseResult -> {
                    List<DefaultPlayData> playDatas = baseResult.getResult();
                    return ListUtil.isEmpty(playDatas) ? null : playDatas.get(0);
                }, callback);
    }

}

