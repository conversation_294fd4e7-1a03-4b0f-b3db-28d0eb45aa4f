package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.provider.Settings;

import com.kaolafm.kradio.lib.base.flavor.KRadioDeviceInfoInter;
import com.kaolafm.kradio.lib.utils.SystemPropertiesProxy;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-04-09 10:58
 ******************************************/
public final class KRadioDeviceInfoImpl implements KRadioDeviceInfoInter {
    @Override
    public String getDeviceId(Object... args) {
        Context context = (Context) args[0];
        return Settings.System.getString(context.getContentResolver(), "navi_tuid");
    }

    @Override
    public String getCarType(Object... args) {
        Context context = (Context) args[0];
        return SystemPropertiesProxy.getString(context, "ro.nwd.car_type");
    }
}
