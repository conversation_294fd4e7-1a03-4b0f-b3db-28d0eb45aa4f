package com.kaolafm.kradio.online.player.utils;

import android.os.Bundle;

import com.kaolafm.kradio.live1.comprehensive.utils.LiveUtil;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.kradio.online.player.pages.LiveBarrageFragment;
import com.kaolafm.report.event.LoginReportEvent;

public class OnlinePlayerLiveUtil {
    public static boolean showLoginIfNotLogin(LiveBarrageFragment liveBarrageFragment) {
        boolean userBound = LiveUtil.isUserBound();
        if (!userBound) {
//            RouterManager.getInstance().jumpPage(RouterConstance.ACTIVITY_URL_MINE);
            Bundle bundle=new Bundle();
            bundle.putString("type", LoginReportEvent.ONLINE_REMARKS1_LIVE_MSG);
            RouterManager.getInstance().jumpPage(RouterConstance.ACTIVITY_URL_LOGIN,bundle);
        }
        return userBound;
    }
}
