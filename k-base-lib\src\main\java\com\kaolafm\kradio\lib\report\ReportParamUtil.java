package com.kaolafm.kradio.lib.report;

import com.kaolafm.kradio.lib.bean.SubcategoryItemBean;
import com.kaolafm.kradio.lib.common.ResType;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

public class ReportParamUtil {

    public static String getRadioType(int resType) {
        String contentType = "-1";
        if (resType == ResType.ALBUM_TYPE) { //专辑
            contentType = "0";
        } else if (resType == ResType.BROADCAST_TYPE) { //广播
            contentType = "1";
        } else if (resType == ResType.RADIO_TYPE) { //电台
            contentType = "2";
        } else if (resType == ResType.LIVE_TYPE) { //直播
            contentType = "3";
        } else if (resType == ResType.FUNCTION_ENTER_SMALL) { //功能
            contentType = "4";
        }else if (resType == ResType.TV_TYPE){//听电视
            contentType = "5";
        }else if (resType == ResType.AUDIO_TYPE){//碎片
            contentType = "6";
        }

        return contentType;
    }

    public static String getRadioType(SubcategoryItemBean bean) {
        String radioType = "";
        if (bean.getItemType() == SubcategoryItemBean.TYPE_ITEM_BROADCAST_LOCAL) {
            radioType = "1";
        } else if (bean.getItemType() == SubcategoryItemBean.TYPE_ITEM_ALBUM) {
            radioType = "0";
        } else if (bean.getItemType() == SubcategoryItemBean.TYPE_ITEM_RADIO_CHANNEL) {
            radioType = "2";
        } else if (bean.getItemType() == SubcategoryItemBean.TYPE_ITEM_BROADCAST_CATEGORY) {
            radioType = "1";
        }else if (bean.getItemType() == SubcategoryItemBean.TYPE_ITEM_TV){
            radioType = "5";
        }

        return radioType;
    }

    public static String getAudioType(PlayItem playItem) {
        String result;
        if (playItem.getBuyType() == AudioDetails.BUY_TYPE_FREE) {
            result = "0";
        } else if (playItem.getBuyType() == AudioDetails.BUY_TYPE_AUDITION) {
            result = "2";
        } else {
            result = "1";
        }

        return result;
    }

    public static String getEventTag(boolean isVip, boolean isFine) {
        String result = "无";
        if (isVip) {
            result = "VIP";
        }
        if (isFine) {
            result = "精品";
        }

        return result;
    }
}
