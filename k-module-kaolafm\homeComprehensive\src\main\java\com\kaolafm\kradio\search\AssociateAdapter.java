package com.kaolafm.kradio.search;

import android.view.View;
import android.view.ViewGroup;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.util.ReportConstants;

import java.util.ArrayList;
import java.util.List;

public class AssociateAdapter extends BaseAdapter<AssociateInfo> {

    private String pageId = Constants.PAGE_ID_SEARCH;

    @Override
    protected BaseHolder<AssociateInfo> getViewHolder(ViewGroup parent, int viewType) {
        return new AssociateHolder(inflate(parent, R.layout.item_associate_list, viewType));
    }

    public void setPageId(String pageId) {
        this.pageId = pageId;
    }

    public class AssociateHolder extends BaseHolder<AssociateInfo> {

        KeywordTextView mTvAssociateWord;

        public AssociateHolder(View itemView) {
            super(itemView);
            mTvAssociateWord=itemView.findViewById(R.id.tv_associate_word);
        }

        @Override
        public void setupData(AssociateInfo associateInfo, int position) {
            List<String> keywords = new ArrayList<>();
            keywords.add(associateInfo.getKeyWord());
            mTvAssociateWord.setKeywordsAndColor(associateInfo.getAssociateWord(),
                    keywords, ResUtil.getColor(R.color.search_associate_text_color_normal));
        }
    }

    @Override
    public void onViewAttachedToWindow(BaseHolder<AssociateInfo> holder) {
        super.onViewAttachedToWindow(holder);
        if (holder instanceof AssociateHolder) {
            ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_SEARCH_ASSOCIATIONAL_WORD, ((AssociateHolder) holder).mTvAssociateWord.getText().toString(), pageId, ReportConstants.CONTROL_TYPE_SCREEN));
        }
    }

}
