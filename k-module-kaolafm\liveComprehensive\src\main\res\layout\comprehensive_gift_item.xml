<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/comprehensive_live_gift_item_width"
    android:layout_height="@dimen/comprehensive_live_gift_item_height"
    android:descendantFocusability="afterDescendants">

    <LinearLayout
        android:id="@+id/item_normal"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center_horizontal">

        <ImageView
            android:id="@+id/item_img_normal"
            android:layout_width="@dimen/comprehensive_live_gift_item_img_width"
            android:layout_height="@dimen/comprehensive_live_gift_item_img_width"
            android:scaleType="fitXY" />

        <TextView
            android:id="@+id/item_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:ellipsize="end"
            android:text=""
            android:textStyle="bold"
            android:textSize="@dimen/comprehensive_live_gift_item_name_text_size"
            android:textColor="@color/comprehensive_gift_item_name_color" />

        <TextView
            android:id="@+id/item_price_normal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:ellipsize="end"
            android:text=""
            android:textSize="@dimen/comprehensive_live_gift_item_price_normal_text_size"
            android:textColor="@color/comprehensive_gift_item_price_normal_color" />

        <TextView
            android:id="@+id/item_points"
            android:layout_centerHorizontal="true"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:ellipsize="end"
            android:text=""
            android:textSize="@dimen/comprehensive_live_gift_item_points_text_size"
            android:textColor="@color/comprehensive_gift_item_points_color"/>
    </LinearLayout>

    <LinearLayout
        android:visibility="gone"
        android:id="@+id/item_selected"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center_horizontal">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center_horizontal"
            android:background="@drawable/comprehensive_gift_item_selected_bg">

            <ImageView
                android:id="@+id/item_img_selected"
                android:layout_width="@dimen/comprehensive_live_gift_item_img_width"
                android:layout_height="@dimen/comprehensive_live_gift_item_img_width"
                android:scaleType="fitXY" />

            <TextView
                android:id="@+id/item_price_selected"
                android:layout_below="@id/item_img_selected"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:maxLines="1"
                android:ellipsize="end"
                android:text=""
                android:textSize="@dimen/comprehensive_live_gift_item_name_text_size"
                android:textColor="@color/comprehensive_gift_item_name_color" />
        </LinearLayout>

        <TextView
            android:id="@+id/item_btn"
            android:layout_width="match_parent"
            android:layout_height="@dimen/y56"
            android:gravity="center"
            android:text="赠送"
            android:textStyle="bold"
            android:textSize="@dimen/comprehensive_live_gift_item_price_selected_text_size"
            android:textColor="@color/comprehensive_gift_item_price_selected_color"
            android:background="@drawable/comprehensive_gift_item_btn_selected_bg"/>
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>