package com.kaolafm.kradio.flavor.carnetwork.dialog;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.util.Log;
import android.view.View;
import android.widget.TextView;

import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.flavor.carnetwork.carnetworkutils.OuShangAuthConstants;
import com.kaolafm.kradio.flavor.impl.CheckCanPlayImpl;

import com.kaolafm.kradio.lib.dialog.BaseCenterDelayDialog;
import com.kaolafm.sdk.core.mediaplayer.ICheckCanPlayInter;

import org.aspectj.lang.ProceedingJoinPoint;

import fr.castorflex.android.circularprogressbar.CircularProgressBar;

public class OuShangDialog extends BaseCenterDelayDialog {
    private Context mContext;
    private final static int CODE_DEFAULT = 99;

    private int mCode;//code值
    private int layoutType = 0;//布局表示
    private final static int layout_type_z01 = 1;
    private final static int layout_type_z02 = 2;
    private final static int layout_type_z08 = 3;
    private final static int layout_type_loading = 98;
    private final static int layout_type_timeout = 97;
    private final static int layout_type_error = 96;
    private final static int layout_type_connectfailed = 95;

    //z01布局控件
    private TextView changan_shop_z01;
    private TextView changan_cancel_z01;
    //z02至z07和z09布局控件
    private TextView changan_title_z02;
    private TextView changan_retry_z02;
    private TextView changan_cancel_z02;
    //z08布局控件
    private TextView changan_know_z08;
    //z10布局控件
    private TextView changan_retry_z10;
    private TextView changan_cancel_z10;

    //z10布局控件
    private TextView changan_retry_connectfailed;
    private TextView changan_cancel_connectfailed;

    //loading布局
    private CircularProgressBar changan_loading;

    private ProceedingJoinPoint mProceedingJoinPoint;

    public void setCode(Integer code) {
        mCode = code;
    }

    public void setProceedingJoinPoint(ProceedingJoinPoint proceedingJoinPoint) {
        mProceedingJoinPoint = proceedingJoinPoint;
    }

    public OuShangDialog(Context context) {
        super(context);
        mContext = context;
        mCode = CODE_DEFAULT;
    }

    @Override
    protected void returnContentView(View mContentView) {
        switch (layoutType) {
            case layout_type_z01:
                changan_shop_z01 = mContentView.findViewById(R.id.changan_shop_z01);
                changan_cancel_z01 = mContentView.findViewById(R.id.changan_cancel_z01);
                changan_shop_z01.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        //服务商城  包名   com.dc.webshop activity名 com.dc.webshop.MainActivity
                        Uri uri = Uri.parse("https://iov.changan.com.cn/huservice/#/user/login");
                        Intent intent = new Intent(Intent.ACTION_VIEW, uri);
                        mContext.startActivity(intent);
//                        Intent intent = new Intent();
//                        intent.setClassName("com.dc.webshop", "com.dc.webshop.MainActivity");
//                        mContext.startActivity(intent);
                    }
                });
                changan_cancel_z01.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        dismiss();
                    }
                });
                break;
            case layout_type_z02:
                changan_title_z02 = mContentView.findViewById(R.id.changan_title_z02);
                changan_retry_z02 = mContentView.findViewById(R.id.changan_retry_z02);
                changan_cancel_z02 = mContentView.findViewById(R.id.changan_cancel_z02);
                if (mCode <= 0 && mCode > -10) {
                    changan_title_z02.setText("(Z0" + Math.abs(mCode) + ")");
                } else {
                    changan_title_z02.setText(R.string.changan_title_z00);
                }
                changan_retry_z02.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        try {
                            CheckCanPlayImpl.getInstance().checkPlay(PlayerManager.getInstance().getCurPlayItem(), ICheckCanPlayInter.METHOD_START, false);
                        } catch (Throwable throwable) {
                            throwable.printStackTrace();
                        }
                    }
                });
                changan_cancel_z02.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        dismiss();
                    }
                });
                break;
            case layout_type_z08:
                changan_know_z08 = mContentView.findViewById(R.id.changan_know_z08);
                changan_know_z08.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        dismiss();
                    }
                });
                break;
            case layout_type_timeout:
                //超时布局
                changan_retry_z10 = mContentView.findViewById(R.id.changan_retry_z10);
                changan_cancel_z10 = mContentView.findViewById(R.id.changan_cancel_z10);
                changan_retry_z10.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        try {
                            CheckCanPlayImpl.getInstance().checkPlay(PlayerManager.getInstance().getCurPlayItem(), ICheckCanPlayInter.METHOD_START, false);
                        } catch (Throwable throwable) {
                            throwable.printStackTrace();
                        }
                    }
                });
                changan_cancel_z10.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        dismiss();
                    }
                });
                break;
            case layout_type_loading:
                //loading布局
                changan_loading = (CircularProgressBar) mContentView.findViewById(R.id.changan_loading);
                changan_loading.progressiveStart();
                break;

            default:
                return;
        }
    }

    @Override
    protected int delayTimeDimiss() {
        //loading状态要一直显示
        if (layoutType == layout_type_loading) {
            return 60000;
        }
        return 5000;
    }

    @Override
    protected int getLayoutId() {
        if (mCode != CODE_DEFAULT) {
            return getLayout();
        }
        return 0;
    }

    //获取相应布局
    private int getLayout() {
        Log.i("zsj", "getLayout: mCode = " + mCode);
        switch (mCode) {
            case OuShangAuthConstants.CODE_MINUS_ONE:
                layoutType = layout_type_z01;
                return R.layout.dialog_changancarnetwork_z01;
            case OuShangAuthConstants.CODE_MINUS_TWO:
            case OuShangAuthConstants.CODE_MINUS_THREE:
            case OuShangAuthConstants.CODE_MINUS_FOUR:
            case OuShangAuthConstants.CODE_MINUS_FIVE:
            case OuShangAuthConstants.CODE_MINUS_SIX:
            case OuShangAuthConstants.CODE_MINUS_SEVEN:
            case OuShangAuthConstants.CODE_MINUS_NINE:
            case OuShangAuthConstants.CODE_ERROR:
                layoutType = layout_type_z02;
                return R.layout.dialog_changancarnetwork_z02;
            case OuShangAuthConstants.CODE_MINUS_EIGHT:
                layoutType = layout_type_z08;
                return R.layout.dialog_changancarnetwork_z08;
            case OuShangAuthConstants.CODE_TIMEOUT:
                layoutType = layout_type_timeout;
                return R.layout.dialog_changancarnetwork_z10;
            case OuShangAuthConstants.CODE_LOADING:
                layoutType = layout_type_loading;
                return R.layout.dialog_changancarnetwork_loading;
            default:
                layoutType = layout_type_z02;
                Log.i("zsj", "getLayout: return 0");
                return R.layout.dialog_changancarnetwork_z02;
        }
    }
}
