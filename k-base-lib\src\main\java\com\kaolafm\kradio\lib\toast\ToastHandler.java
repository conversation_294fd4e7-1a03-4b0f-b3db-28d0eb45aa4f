package com.kaolafm.kradio.lib.toast;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.content.Context;
import android.os.Handler;
import android.os.Message;
import androidx.core.view.ViewCompat;
import android.util.Log;
import android.view.View;
import android.view.WindowManager;

import com.kaolafm.kradio.lib.R;
import com.kaolafm.kradio.lib.utils.ResUtil;

import java.util.Comparator;
import java.util.PriorityQueue;

/**
 * toast消息队列
 *
 * <AUTHOR>
 * @date 2018/4/11
 */
class ToastHandler extends Handler {

    private volatile static ToastHandler mToastHandler;

    private final PriorityQueue<SuperToast> mToastPriorityQueue;

    private ToastHandler() {
        mToastPriorityQueue = new PriorityQueue<>(10, new ToastComparator());
    }

    static ToastHandler createInstance() {
        return new ToastHandler();
    }

    static ToastHandler getInstance() {
        if (mToastHandler == null) {
            synchronized (ToastHandler.class) {
                if (mToastHandler == null) {
                    mToastHandler = new ToastHandler();
                }
            }
        }
        return mToastHandler;
    }

    /**
     * 添加一个toast对象到信息队列
     */
    void add(SuperToast superToast) {
        mToastPriorityQueue.add(superToast);
        showNextToast();
    }

    /**
     * 在当前队列显示下一条toast，如果有toast正在显示就不执行任何操作。当当前正在显示的toast消失后会调用该方法
     */
    private void showNextToast() {
        if (mToastPriorityQueue.isEmpty()) {
            return;
        }
        //从队列最顶部取toast
        final SuperToast superToast = mToastPriorityQueue.peek();
        if (!superToast.isShowing()) {
            displayToast(superToast);
        }
    }

    @Override
    public void handleMessage(Message msg) {
        final SuperToast superToast = (SuperToast) msg.obj;
        switch (msg.what) {
            case Messages.SHOW_NEXT:
                showNextToast();
                break;
            case Messages.DISPLAY_TOAST:
                displayToast(superToast);
                break;
            case Messages.REMOVE_TOAST:
                removeToast(superToast);
                break;
            default:
                super.handleMessage(msg);
                break;
        }
    }

    /**
     * 该方法是显示toast的具体方法。
     * <p></p>
     * 既可以使用WindowManager来显示，这样会显示在全局和系统toast一样；
     * 也可以使用外部提供的ViewGroup,这样只会显示在当前页面，和系统的snackbar一样，页面关闭toast也消失。
     */
    private void displayToast(SuperToast superToast) {
        if (superToast.isShowing()) {
            return;
        }
        long animDuration = 0;
        //如果toast显示层级是系统级别就使用WindowManager，层级和系统toast一样。
        if (superToast.getDisplayLevel() == ToastStyle.LEVEL_SYSTEM) {
            WindowManager wm = (WindowManager) superToast.getContext().getApplicationContext()
                    .getSystemService(Context.WINDOW_SERVICE);
            if (wm != null) {
                View view = superToast.getView();
                view.setMinimumWidth(ResUtil.getDimen(R.dimen.x342));
                // 解决 https://app.huoban.com/tables/2100000007530121/items/2300001170911657?userId=2169710 问题
                try {
                    wm.addView(view, superToast.getWindowParams());
                } catch (RuntimeException re) {
                    re.printStackTrace();
                }
            }
            //toast显示层级是activity级别，就使用外部提供的ViewGroup,层级和系统snackbar一样。
        } else {
            if (superToast.getViewGroup() == null) {
                Log.i("ToastHandler", "displayToast: ViewGroup不能为空");
                return;
            }
            try {
                superToast.getViewGroup().addView(superToast.getView());
                if (superToast.getShowAnimator() != null) {
                    Animator showAnimator = superToast.getShowAnimator();
                    animDuration = showAnimator.getDuration();
                    showAnimator.setTarget(superToast.getView());
                    showAnimator.start();
                }
            } catch (IllegalStateException e) {
                e.printStackTrace();
            }

        }
        //如果不是一直显示就延迟取消
        if (!superToast.isIndeterminate()) {
            sendDelayedMessage(superToast, Messages.REMOVE_TOAST, superToast.getDuration() + animDuration);
        }
    }

    /**
     * 延迟发送消息
     */
    private void sendDelayedMessage(SuperToast superToast, int what, long delay) {
        Message message = obtainMessage(what);
        message.obj = superToast;
        sendMessageDelayed(message, delay);
    }

    /**
     * 移除正在显示的toast。该方法会检索队列，如果队列中还有消息就会显示下一条。
     */
    void removeToast(final SuperToast superToast) {
        //移除队列第一条信息
        mToastPriorityQueue.poll();
        if (superToast.getDisplayLevel() == ToastStyle.LEVEL_SYSTEM) {
            WindowManager wm = (WindowManager) superToast.getContext().getApplicationContext()
                    .getSystemService(Context.WINDOW_SERVICE);
            if (wm != null) {
                View view = superToast.getView();
                if (view != null && ViewCompat.isAttachedToWindow(view)) {
                    wm.removeView(view);
                }
            }
            if (superToast.getDismissListener() != null) {
                superToast.getDismissListener().onDismiss(superToast.getView());
            }
            //彻底隐藏有个时间，所以等到彻底隐藏在显示下一条
            sendDelayedMessage(superToast, Messages.SHOW_NEXT, 250);
        } else {
            //如果改toast已经消失了，就不要再尝试让它消失。
            if (!superToast.isShowing()) {
                mToastPriorityQueue.remove(superToast);
                //不要信上边的话,如果App退到后台,这时mView.isShown()是false;
                dismiss(superToast);
                return;
            }
            if (superToast.getHideAnimator() != null) {
                Animator hideAnimator = superToast.getHideAnimator();
                hideAnimator.addListener(new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationEnd(Animator animation) {
                        dismiss(superToast);
                    }
                });
                hideAnimator.setTarget(superToast.getView());
                hideAnimator.start();
            } else {
                dismiss(superToast);
            }

        }

    }

    private void dismiss(SuperToast superToast) {
        if (superToast.getDismissListener() != null) {
            superToast.getDismissListener().onDismiss(superToast.getView());
        }
        superToast.getViewGroup().removeView(superToast.getView());
        showNextToast();
    }

    public void cancelAllToasts() {
        removeMessages(Messages.SHOW_NEXT);
        removeMessages(Messages.DISPLAY_TOAST);
        removeMessages(Messages.REMOVE_TOAST);
        for (SuperToast toast : mToastPriorityQueue) {
            if (toast.isShowing()) {
                if (toast.getDisplayLevel() == ToastStyle.LEVEL_ACTIVITY) {
                    try {
                        toast.getViewGroup().removeView(toast.getView());
                    } catch (NullPointerException | IllegalStateException e) {
                        e.printStackTrace();
                    }
                } else {
                    WindowManager wm = (WindowManager) toast.getContext().getApplicationContext()
                            .getSystemService(Context.WINDOW_SERVICE);
                    try {
                        wm.removeView(toast.getView());
                    } catch (NullPointerException | IllegalStateException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        mToastPriorityQueue.clear();
    }

    /**
     * 消息类型
     */
    private static final class Messages {

        /**
         * 显示当前toast
         */
        private static final int DISPLAY_TOAST = 101;

        /**
         * 显示下一条
         */
        private static final int SHOW_NEXT = 102;

        /**
         * 取消
         */
        private static final int REMOVE_TOAST = 103;
    }

    /**
     * toast消息队列比较器，根据优先级排序。优先级一样就更加时间，先来后到顺序。
     */
    private static class ToastComparator implements Comparator<SuperToast> {

        @Override
        public int compare(SuperToast o1, SuperToast o2) {
            if (o1.isShowing()) {
                return -1;
            }
            if (o1.getPriorityLevel() < o2.getPriorityLevel()) {
                return -1;
            } else if (o1.getPriorityLevel() > o2.getPriorityLevel()) {
                return 1;
            } else {
                return o1.getTimestamp() <= o2.getTimestamp() ? -1 : 1;
            }
        }
    }
}
