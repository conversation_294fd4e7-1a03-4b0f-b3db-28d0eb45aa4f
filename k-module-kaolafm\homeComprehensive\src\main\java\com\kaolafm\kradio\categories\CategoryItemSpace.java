package com.kaolafm.kradio.categories;

import android.graphics.Rect;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.GridLayoutManager.SpanSizeLookup;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.State;
import android.view.View;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.bean.SubcategoryItemBean;
import com.kaolafm.kradio.lib.utils.ResUtil;

/**
 * 分类页面各个item之间的间隔。根据类型显示不同的间距
 *
 * <AUTHOR>
 * @date 2018/4/29
 */

public class CategoryItemSpace extends RecyclerView.ItemDecoration {

    private int lastItemViewType = 0;

    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, State state) {
        int position = parent.getChildLayoutPosition(view);
        int itemViewType = parent.getAdapter().getItemViewType(position);
        int right = 0, bottom = 0;
        switch (itemViewType) {
            case SubcategoryItemBean.TYPE_ITEM_ALBUM:
            case SubcategoryItemBean.TYPE_ITEM_CHARTS:
                right = ResUtil.getDimen(R.dimen.m8);
                bottom = ResUtil.getDimen(R.dimen.m30);
                break;
            case SubcategoryItemBean.TYPE_ITEM_RADIO_CHANNEL:
            case SubcategoryItemBean.TYPE_ITEM_OFFICIAL_CHARTS:
            case SubcategoryItemBean.TYPE_ITEM_BROADCAST_LOCAL:
            case SubcategoryItemBean.TYPE_ITEM_BROADCAST_CATEGORY:
                right = ResUtil.getDimen(R.dimen.m8);
                bottom = ResUtil.getDimen(R.dimen.m8);
                break;
//            case SubcategoryItemBean.TYPE_ITEM_SUBSCRIPTION:
//                bottom = ResUtil.getDimen(R.dimen.m20);
//                break;
//            case SubcategoryItemBean.TYPE_ITEM_LISTEN:
//            case SubcategoryItemBean.TYPE_ITEM_ACCOUNT:
//                right = ResUtil.getDimen(R.dimen.m8);
//                break;
            default:
        }
        final GridLayoutManager gridLayoutManager = (GridLayoutManager) parent.getLayoutManager();
        final SpanSizeLookup spanSizeLookup = gridLayoutManager.getSpanSizeLookup();
        int itemCount = gridLayoutManager.getItemCount();
        if (itemCount == 0) {
            return;
        }
        int spanCount = gridLayoutManager.getSpanCount();
        int spanGroupIndex = spanSizeLookup.getSpanGroupIndex(position, spanCount);
//        int spanSize = spanSizeLookup.getSpanSize(position);
//        int spanIndex = spanSizeLookup.getSpanIndex(position, spanCount);

        //最后一行index
        int lastRow = spanSizeLookup.getSpanGroupIndex(itemCount - 1, spanCount);
        //竖屏:加到特殊处理,如果是最后一行就让间距显示30px
        if (spanGroupIndex == lastRow) {
            bottom = ResUtil.getDimen(R.dimen.y30);
        }
//        Log.i("CategoryItemSpace",
//                "getItemOffsets: position=" + position
//                        + ", spanSize=" + spanSize
//                        + ", spanCount=" + spanCount
//                        + ", spanGroupIndex=" + spanGroupIndex
//                        + ", spanIndex=" + spanIndex
//                        +", rowCount="+ lastRow);
        outRect.set(0, 0, Math.round(right), Math.round(bottom));

    }

    private void applyItemSpace(int hSpace, int vSpace) {

    }
}
