package com.kaolafm.kradio.flavor.impl;

import android.text.TextUtils;
import android.util.Log;

import com.byd.diLinkAccount.BindStateCallBack;
import com.google.gson.Gson;
import com.kaolafm.base.utils.DateUtil;
import com.kaolafm.kradio.common.event.LogoutBindEvent;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.opensdk.account.token.AccessTokenManager;
import com.kaolafm.opensdk.account.token.KaolaAccessToken;
import com.kaolafm.opensdk.api.login.LoginRequest;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.sdk.client.ex.bean.UserInfo;

import org.greenrobot.eventbus.EventBus;

public class AccountInterworkImpl implements AccountInterworkInter {
    private static final String TAG = "AccountInterworkImpl";

    @Override
    public boolean isOpenThirdPartyAccount() {
        boolean isOpenThirdPartyAccount = true;
        Log.i(TAG, "isOpenThirdPartyAccount="+isOpenThirdPartyAccount);
        return isOpenThirdPartyAccount;
    }

    @Override
    public void init() {
        // 初始化比亚迪sdk
        BydAccountHelper.getInstance(AppDelegate.getInstance().getContext().getApplicationContext())
                .init();
    }

    @Override
    public void bindAccount() {
        // 注册登录接口
        ThirdAccountLoginHelp.getInstance().init();
    }

    @Override
    public void getAccountBindState() {
        Log.i(TAG, "getAccountBindState");
        UserInfo userInfo = getUserInfoFromNet();
        String userId = userInfo.getUserId();
        KaolaAccessToken accessToken = AccessTokenManager.getInstance().getKaolaAccessToken();
        String token = new Gson().toJson(accessToken);
        String nickName = userInfo.getNickName();
        String avatar = userInfo.getAvatar();
        Log.i(TAG, "userId="+userId+";token="+token+";nickName="+nickName+";avatar="+avatar);
        BydAccountHelper.getInstance(AppDelegate.getInstance().getContext().getApplicationContext())
                .getAccountBindState(userId, token, nickName, avatar, new BindStateCallBack() {
                    @Override
                    public void onDoNothing() {
                        Log.i(TAG, "onDoNothing");
                    }

                    @Override
                    public void onChange(String userId, String token) {
                        Log.i(TAG, "onChange, 接收到车机端返回的userId="+userId+";token="+token);
                        // 云听需要切换账号
                        KaolaAccessToken accessToken = null;
                        try {
                            accessToken  = new Gson().fromJson(token, KaolaAccessToken.class);
                        }catch (Exception e){
                            Log.i(TAG, e.getMessage());
                        }
                        Log.i(TAG, "onChange, accessToken="+accessToken.toString());
                        ThirdAccountLoginHelp.getInstance().setOnChange(true);
                        ThirdAccountLoginHelp.getInstance().setOnLogin(true);
                        KaolaAccessToken tempKaolaAccessToken = AccessTokenManager.getInstance().getKaolaAccessToken();
                        if (tempKaolaAccessToken != null) {
                            accessToken.setOpenId(tempKaolaAccessToken.getOpenId());
//                            accessToken.setExpireTime(
//                                    DateUtil.getServerTime() + (tempKaolaAccessToken.getRefreshTime() - 10 * 1000));
                        }
                        ReportHelper.getInstance().initUid(accessToken.getUserId());
                        AccessTokenManager.getInstance().setCurrentAccessToken(accessToken);

                        // 登录获取信息
                        Log.i(TAG, "onChange, getUserInfo start");
                        LoginRequest request = new LoginRequest();
                        request.getUserInfo(new HttpCallback<com.kaolafm.opensdk.api.login.model.UserInfo>() {
                            @Override
                            public void onSuccess(com.kaolafm.opensdk.api.login.model.UserInfo userInfo) {
                                Log.i(TAG, "onChange, getUserInfo sucess");
                                UserInfoManager infoManager = UserInfoManager.getInstance();
                                infoManager.setUserNickName(userInfo.getNickName());
                                infoManager.setUserFavicon(userInfo.getAvatar());
                                infoManager.localLogin();
                                EventBus.getDefault().post(new LogoutBindEvent(LogoutBindEvent.LOGIN));

                                AccountInterworkInter mAccountInterworkInter = ClazzImplUtil.getInter("AccountInterworkImpl");
                                if(mAccountInterworkInter != null && mAccountInterworkInter.isOpenThirdPartyAccount()){
                                    Log.i(TAG,"onChange, setAccountBind,action=4");
                                    mAccountInterworkInter.setAccountBind(4);
                                }

                                Log.i(TAG, "onChange, UserInfoManager.userId="+infoManager.getUserId()+";UserInfoManager.token="+infoManager.getToken());
                                Log.i(TAG, "onChange, onSuccess,userInfo.nickName="+userInfo.getNickName()+";userInfo.avatar="+userInfo.getAvatar());


                            }

                            @Override
                            public void onError(ApiException e) {
                                Log.i(TAG, "getUserInfo failed");
                                Log.i(TAG, "onChange, onError,e="+e.getMessage());
                            }
                        });
                    }

                    @Override
                    public void onLogout() {
                        Log.i(TAG, "onLogout");
                        // 云听退出账号，保持退出登录状态
                        ThirdAccountLoginHelp.getInstance().setOnLogout(true);
                        UserInfoManager.getInstance().logout();
                        AccessTokenManager.getInstance().logoutKaola();
                        EventBus.getDefault().post(new LogoutBindEvent(LogoutBindEvent.LOGOUT));
                    }
                });
    }

    @Override
    public void setAccountBind(Object... args) {
        Log.i(TAG, "setAccountBind");
        int action = (int) args[0];
        UserInfo userInfo = getUserInfoFromNet();
        String userId = userInfo.getUserId();
        KaolaAccessToken accessToken = AccessTokenManager.getInstance().getKaolaAccessToken();
        String token = new Gson().toJson(accessToken);
        String nickName = userInfo.getNickName();
        String avatar = userInfo.getAvatar();
        Log.i(TAG, "action="+action+";userId="+userId+";token="+token+";nickName="+nickName+";avatar="+avatar);
        BydAccountHelper.getInstance(AppDelegate.getInstance().getContext().getApplicationContext())
                .setBYDAccountBind(action, userId, token, nickName, avatar);
    }

    public UserInfo getUserInfoFromNet() {
        UserInfo userInfo = new UserInfo();
        //
        UserInfoManager userInfoManager = UserInfoManager.getInstance();
        userInfo.setNickName(TextUtils.isEmpty(userInfoManager.getUserNickName())?"":userInfoManager.getUserNickName());
        userInfo.setAvatar(TextUtils.isEmpty(userInfoManager.getUserFavicon())?"":userInfoManager.getUserFavicon());
        userInfo.setUserId(TextUtils.isEmpty(userInfoManager.getUserId())?"":userInfoManager.getUserId());
        //
        KaolaAccessToken accessToken = AccessTokenManager.getInstance().getKaolaAccessToken();
        userInfo.setToken(TextUtils.isEmpty(accessToken.getAccessToken())?"":accessToken.getAccessToken());
        userInfo.setRefreshToken(TextUtils.isEmpty(accessToken.getRefreshToken())?"":accessToken.getRefreshToken());

        return userInfo;
    }
}
