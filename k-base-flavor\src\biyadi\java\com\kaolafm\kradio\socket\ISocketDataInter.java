package com.kaolafm.kradio.socket;

/**
 * 主要用于操作SocketManager给回来的数据
 */
public interface ISocketDataInter {
    /**
     * 初始化SocketData
     */
    void initSocket();

    /**
     * 释放资源，确认不在使用SocketData相关功能使用
     */
    void releaseSocket();

    /**
     * 仅通知当前监听数据返回
     * @param onlyThis boolean 是否只刷新当前的接口
     * @param getSocketDataCallBack
     */
    void notifySocketDataOnlyThis(boolean onlyThis, IGetSocketDataCallBack getSocketDataCallBack);
    /**
     *  添加监听
     * @param getSocketDataCallBack
     */
    void addIGetSocketDataCallBack(IGetSocketDataCallBack getSocketDataCallBack);

    /**
     * 移除监听
     * @param getSocketDataCallBack
     */
    void removeIGetSocketDataCallBack(IGetSocketDataCallBack getSocketDataCallBack);

    /**
     * 主动请求刷新数据，在添加的监听中接收数据
     */
    void requestRefresh();

    /**
     * 获取Socket的数据（有数据的话就不刷新），在添加的监听中接收数据
     */
    void getSocketData();
}