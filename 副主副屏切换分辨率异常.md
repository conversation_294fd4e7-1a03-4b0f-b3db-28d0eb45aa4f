# 副主副屏切换分辨率异常问题修复

## 问题描述
应用在副屏→主屏→副屏的切换过程中，第二次回到副屏时出现分辨率异常，UI显示不正确，就像分辨率没有适配一样。

## 问题分析

### 日志分析
从日志`副主副屏切换，分辨率异常.txt`中可以看到关键信息：

**正常时（初始在副屏）**：
```
16:16:18.744 I LauncherActivity: onConfigurationChanged...{...sw1080dp w1920dp h886dp...} s.7
```

**异常时（切换回副屏）**：
```
16:16:24.180 I LauncherActivity: onConfigurationChanged...{...sw720dp w1920dp h608dp...} s.3
```

### 根本原因
1. **Activity被销毁重建**：从主屏返回副屏时，LauncherActivity没有被恢复（Resume），而是被销毁后重建（Re-Created）
2. **配置变化处理不当**：在重建过程中，Activity收到了错误的屏幕配置信息
3. **AndroidManifest配置不完整**：`android:configChanges`属性缺少`smallestScreenSize`处理

### 问题机制
1. 当`smallestScreenSize`从`sw1080dp`变为`sw720dp`时
2. 由于AndroidManifest中没有声明处理`smallestScreenSize`变化
3. 系统认为这是一个"重大配置变化"，销毁并重建Activity
4. 在重建过程中，Activity获取到了错误的屏幕配置信息（sw720dp而不是sw1080dp）
5. UI基于错误的尺寸进行布局，导致分辨率异常

## 修复方案

### 1. AndroidManifest配置修复
在所有AndroidManifest.xml文件中，为Activity的`android:configChanges`属性添加完整的配置变化处理：

**修复前：**
```xml
android:configChanges="orientation|screenSize|keyboardHidden|locale|layoutDirection|keyboard|uiMode"
```

**修复后：**
```xml
android:configChanges="orientation|screenSize|smallestScreenSize|screenLayout|keyboardHidden|locale|layoutDirection|keyboard|uiMode|fontScale|density"
```

### 2. 强制UI刷新机制
**问题分析**：即使添加了`smallestScreenSize`配置，从主屏切换回副屏时，系统仍然可能传递错误的配置信息（sw720dp而不是sw1080dp），导致UI没有正确适配。

**解决方案**：在`onConfigurationChanged`中添加强制UI刷新机制。

#### 2.1 LauncherActivity强制刷新 (LauncherActivity.java)
```java
@Override
public void onConfigurationChanged(Configuration newConfig) {
    super.onConfigurationChanged(newConfig);
    // ... 其他代码

    // 修复：强制刷新UI以适配新的屏幕配置
    forceRefreshUIForConfigurationChange(newConfig);

    // ... 其他代码
}

private void forceRefreshUIForConfigurationChange(Configuration newConfig) {
    try {
        // 1. 强制刷新根视图
        View rootView = findViewById(android.R.id.content);
        if (rootView != null) {
            rootView.post(() -> {
                rootView.requestLayout();
                rootView.invalidate();
            });
        }

        // 2. 延迟执行，确保配置变化完全生效
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            // 强制重新获取屏幕尺寸相关的资源
            getResources().getConfiguration();
            // 通知EventBus重新适配
            EventBus.getDefault().post(new EventOrientationChangeData());
        }, 100);
    } catch (Exception e) {
        Log.e(TAG, "forceRefreshUIForConfigurationChange: error", e);
    }
}
```

#### 2.2 HorizontalHomePlayerFragment强制刷新 (HorizontalHomePlayerFragment.java)
```java
@Override
public void onConfigurationChanged(Configuration newConfig) {
    super.onConfigurationChanged(newConfig);
    // ... 其他代码

    // 修复：强制刷新UI以适配新的屏幕配置
    forceRefreshUIForScreenChange(newConfig);
}

private void forceRefreshUIForScreenChange(Configuration newConfig) {
    try {
        // 延迟执行，确保配置变化完全生效
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            // 1. 强制重新布局ViewPager和导航栏
            if (home_view_page != null) {
                home_view_page.requestLayout();
            }
            if (mHnlHomeNavigation != null) {
                mHnlHomeNavigation.requestLayout();
                mHnlHomeNavigation.post(() -> {
                    mHnlHomeNavigation.showAccordingToScreen(ResUtil.getOrientation());
                });
            }

            // 2. 强制刷新所有Fragment
            if (homeDateFragments != null) {
                for (ComperhensiveHomeDateFragment fragment : homeDateFragments) {
                    if (fragment != null && fragment.getView() != null) {
                        fragment.getView().requestLayout();
                    }
                }
            }

            // 3. 强制重新执行showAccordingToScreen
            showAccordingToScreen(ResUtil.getOrientation());
        }, 150);
    } catch (Exception e) {
        Log.e(TAG, "forceRefreshUIForScreenChange: error", e);
    }
}
```

### 修复的文件列表
**AndroidManifest配置文件：**
1. `k-base-flavor\src\lingke_kradio\AndroidManifest.xml`
2. `k-base-flavor\src\smart_kradio\AndroidManifest.xml`
3. `k-base-flavor\src\liantong_kradio\AndroidManifest.xml`
4. `k-base-flavor\src\yikatongjilikx11_kradio\AndroidManifest.xml`
5. `k-module-kaolafm\liveComprehensive\src\main\AndroidManifest.xml`
6. `k-module-kaolafm\activityComprehensive\src\main\AndroidManifest.xml`
7. `k-module-kaolafm\adComprehensive\src\main\AndroidManifest.xml`
8. `k-module-kaolafm\onlineHome\src\main\AndroidManifest.xml`

**Java代码文件：**
9. `k-module-kaolafm\homeComprehensive\src\main\java\com\kaolafm\launcher\LauncherActivity.java`
10. `k-module-kaolafm\homeComprehensive\src\main\java\com\kaolafm\kradio\home\comprehensive\HorizontalHomePlayerFragment.java`

### 关键配置项说明
- `smallestScreenSize`：处理最小屏幕尺寸变化（关键修复项）
- `screenLayout`：处理屏幕布局变化
- `fontScale`：处理字体缩放变化
- `density`：处理屏幕密度变化

## 修复效果
1. **避免Activity重建**：当屏幕配置发生变化时，Activity不会被销毁重建，而是调用`onConfigurationChanged`方法
2. **正确的配置信息**：Activity能够获取到正确的屏幕配置信息
3. **UI正确适配**：UI基于正确的屏幕尺寸进行布局和渲染
4. **用户体验提升**：副主副屏切换过程流畅，无分辨率异常

## 测试建议
1. 在副屏启动应用，确认显示正常
2. 切换到主屏，确认显示正常
3. 切换回副屏，确认显示仍然正常，无分辨率异常
4. 检查日志确认Activity不再被重建，而是调用`onConfigurationChanged`
5. 验证配置信息的一致性（sw1080dp应该保持不变）

## 技术原理
Android系统在检测到配置变化时，会根据AndroidManifest中的`android:configChanges`声明来决定处理方式：
- 如果配置项已声明：调用`onConfigurationChanged`方法，Activity继续运行
- 如果配置项未声明：销毁并重建Activity，可能导致状态丢失和配置错误

通过添加`smallestScreenSize`等配置项，确保多屏切换时Activity能够正确处理配置变化，避免重建导致的问题。
