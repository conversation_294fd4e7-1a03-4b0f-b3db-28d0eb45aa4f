package com.kaolafm.kradio.live;

import android.media.MediaRecorder;
import android.net.LocalServerSocket;
import android.net.LocalSocket;
import android.net.LocalSocketAddress;
import android.util.Log;

import com.kaolafm.kradio.lib.base.flavor.live.KradioRecorderInterface;
import com.kaolafm.kradio.live.mvp.LivePresenter;
import com.kaolafm.kradio.live.player.LiveManager;

import java.io.DataInputStream;
import java.io.File;
import java.io.IOException;
import java.net.DatagramSocket;

/**
 * <pre>
 *     <AUTHOR> Wenchl
 *     e-mail : <EMAIL>
 *     time   : 2019/04/24
 *     desc   :
 *     version: 1.0
 * </pre>
 */
public class KradioRecorder implements KradioRecorderInterface {

    private static final String TAG = "KradioRecorder";
    private RecordingStateCallback recordingStateCallback;  //接收数据

    private String mFilePath;

    private MediaRecorder mMediaRecorder;
    private boolean output2File = true; //是否输出到文件
    private boolean isAudioRecording;

    private LocalServerSocket lss;
    private LocalSocket sender, receiver;


    public KradioRecorder() {
        output2File = true;
    }

    public KradioRecorder(RecordingStateCallback callback) {
        output2File = false;
        this.recordingStateCallback = callback;
    }

    @Override
    public void startRecord() {
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "startRecord");
        }

        if (isAudioRecording) {
            Log.e(TAG, "正在录音，无需重复开始录音");
            return;
        }
        boolean succeed;
        if (output2File) {
            File file = createNewAudioFile();
            if (null == file) {
                if (LivePresenter.DEBUG_LIVE) {
                    Log.w(TAG, "startRecord, create file failed.");
                }
                return;
            }

            if (file.exists()) {
                file.delete();
            }

            mFilePath = file.getAbsolutePath();
            succeed = true;
        } else {
            succeed = initLocalSocket();
        }

        if (!succeed) {
            Log.e(TAG, "startRecord Failure");
            releaseAll();
            return;
        }

        mMediaRecorder = new MediaRecorder();
        mMediaRecorder.setAudioSource(MediaRecorder.AudioSource.MIC);
        mMediaRecorder.setOutputFormat(MediaRecorder.OutputFormat.AAC_ADTS);
        mMediaRecorder.setAudioEncoder(MediaRecorder.AudioEncoder.AAC);

        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "startRecord path: " + mFilePath);
        }

        if (output2File)
            mMediaRecorder.setOutputFile(mFilePath);
        else mMediaRecorder.setOutputFile(sender.getFileDescriptor());

        try {
            mMediaRecorder.prepare();
            mMediaRecorder.start();
        } catch (Exception e) {
            if (LivePresenter.DEBUG_LIVE) {
                Log.e(TAG, "startRecord start failed: ", e);
            }
            releaseAll();
            return;
        }
        this.isAudioRecording = true;
        if (!output2File) {
            if (recordingStateCallback != null) recordingStateCallback.onRecordingStarted();
            startAudioRecording();
        }
    }

    private void startAudioRecording() {
        new Thread(new AudioCaptureAndSendThread()).start();
    }

    private void releaseAll() {
        releaseMediaRecorder();
        releaseLocalSocket();
        mMediaRecorder = null;
    }

    private void releaseMediaRecorder() {
        try {
            if (mMediaRecorder == null) {
                return;
            }
            if (isAudioRecording) {
                mMediaRecorder.stop();
                isAudioRecording = false;
            }
            mMediaRecorder.reset();
            mMediaRecorder.release();
            mMediaRecorder = null;
        } catch (Exception err) {
            Log.d(TAG, err.toString());
        }
    }

    private boolean initLocalSocket() {
        boolean ret = true;
        try {
            releaseLocalSocket();

            String serverName = "armAudioServer";
            final int bufSize = 1024;

            lss = new LocalServerSocket(serverName);

            receiver = new LocalSocket();
            receiver.connect(new LocalSocketAddress(serverName));
            receiver.setReceiveBufferSize(bufSize);
            receiver.setSendBufferSize(bufSize);

            sender = lss.accept();
            sender.setReceiveBufferSize(bufSize);
            sender.setSendBufferSize(bufSize);
        } catch (IOException e) {
            e.printStackTrace();
            ret = false;
        }
        return ret;
    }

    private void releaseLocalSocket() {
        try {
            if (sender != null) {
                sender.close();
            }
            if (receiver != null) {
                receiver.close();
            }
            if (lss != null) {
                lss.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        sender = null;
        receiver = null;
        lss = null;
    }

    @Override
    public boolean stopRecord() {
        if (mMediaRecorder != null) {
            try {
                mMediaRecorder.stop();
                mMediaRecorder.release();
                mMediaRecorder = null;
                isAudioRecording = false;
                return true;
            } catch (RuntimeException stopException) {
                //部分手机上如果录音时间过短会返回stop failed的异常，原因是没有录到有效的数据，需要自己处理。所以这里返回true，上层接收到处理后续逻辑
                return true;
            } catch (Exception e) {
                if (LivePresenter.DEBUG_LIVE) {
                    Log.i(TAG, "stopRecord error: ", e);
                }
                return false;
            }
        }
        return false;
    }

    @Override
    public String getFilePath() {
        return mFilePath;
    }

    @Override
    public boolean isRecording() {
        return isAudioRecording;
    }

    private File createNewAudioFile() {
        if (!LiveManager.RECORDINGS_DIR.exists()) {
            LiveManager.RECORDINGS_DIR.mkdirs();
        }
        File file = new File(LiveManager.RECORDINGS_DIR, "Live-Leave-message" + LiveManager.EXTENSION);
        return file;
    }

    private class AudioCaptureAndSendThread implements Runnable {
        public void run() {
            try {
                sendAudio();
            } catch (Exception e) {
                Log.e(TAG, "sendAmrAudio() 出错");
            }
        }

        private void sendAudio() throws Exception {
            DatagramSocket udpSocket = new DatagramSocket();
            DataInputStream dataInput = new DataInputStream(receiver.getInputStream());

            byte[] sendBuffer = new byte[1024];
            while (isRecording()) {
                int numOfRead = dataInput.read(sendBuffer, 0, sendBuffer.length);
                if (numOfRead == -1) {
                    Log.d(TAG, "amr...no data get wait for data coming.....");
                    Thread.sleep(100);
                }
                if (recordingStateCallback != null)
                    recordingStateCallback.onDataReceived(sendBuffer, numOfRead);
            }
            udpSocket.close();
            dataInput.close();
            releaseAll();
            if (recordingStateCallback != null) recordingStateCallback.onRecordingStopped();
        }
    }

    public interface RecordingStateCallback {
        void onRecordingStarted();

        void onRecordingStopped();

        void onDataReceived(byte[] buffer, int length);
    }
}
