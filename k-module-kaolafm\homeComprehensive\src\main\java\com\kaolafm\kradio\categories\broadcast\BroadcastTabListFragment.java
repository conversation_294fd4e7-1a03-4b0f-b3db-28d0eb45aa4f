package com.kaolafm.kradio.categories.broadcast;

import android.content.res.Configuration;
import android.graphics.Rect;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.LinearSmoothScroller;
import androidx.recyclerview.widget.RecyclerView;

import com.kaolafm.kradio.categories.CategoriesFragmentAdapter;
import com.kaolafm.kradio.category.ErrorCode;
import com.kaolafm.kradio.common.widget.NotScrollViewPager;
import com.kaolafm.kradio.component.ui.base.skin.SkinHelper;
import com.kaolafm.kradio.home.comprehensive.ui.view.BaseBackFragment;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.bean.BroadcastTabData;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.RecyclerViewExposeUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import java.util.List;

/**
 * 全部分类，四级tab页
 */
public class BroadcastTabListFragment extends BaseBackFragment<TabBroadCastPresenter>
        implements IBroadcastTabView, RecyclerViewExposeUtil.OnItemExposeListener {

    public static final String TAG = BroadcastTabListFragment.class.getSimpleName();

    private static final String KEY_BROADCAST_CATEGORY_ID = "broadcastCategoryId";
    private static final String KEY_BROADCAST_CATEGORY_NAME = "categoryName";

    private int mCategoryId;
    private String mCategoryName;

    private RecyclerView mTabRecyclerView;
    private NotScrollViewPager mContentViewPager;
    private TabAdapter mTabAdapter;
    private CategoriesFragmentAdapter mContentVPAdapter;
    private TextView mScrollUp, mScrollDown;
    private View mLoadingView;
    private View mLayout;
    private ViewStub mVsLayoutErrorPage;
    private View mErrorView;

    private int mLastSelectTabPos = 0;

    private BasePlayStateListener mPlayerStateListener;

    public static BroadcastTabListFragment newInstance(int categoryId, String categoryName) {
        Log.d(TAG, "newInstance() create fragment -> categoryId:" + categoryId + ", categoryName:" + categoryName);
        Bundle args = new Bundle();
        args.putInt(KEY_BROADCAST_CATEGORY_ID, categoryId);
        args.putString(KEY_BROADCAST_CATEGORY_NAME, categoryName);
        BroadcastTabListFragment fragment = new BroadcastTabListFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void initArgs() {
        Bundle args = getArguments();
        if (args != null) {
            mCategoryId = args.getInt(KEY_BROADCAST_CATEGORY_ID);
            mCategoryName = args.getString(KEY_BROADCAST_CATEGORY_NAME);
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.Q)
    @Override
    public void initView(View view) {
        super.initView(view);
        Log.d(TAG, "initView");

        TextView titleView = (TextView) View.inflate(getContext(), R.layout.bbf_title_center_textview, null);
        titleView.setText(mCategoryName);
        addTitleCenterView(titleView);
        View contentView = View.inflate(getContext(), R.layout.fragment_tab_broadcast, null);
        addContentView(contentView);

        mTabRecyclerView = view.findViewById(R.id.rv_tab);
        handleTabRv();
        mContentViewPager = view.findViewById(R.id.vp_content);
        handleContentVP();
        handleOtherUi(view);

        mPlayerStateListener = new BasePlayStateListener() {
            @Override
            public void onPlayerPlaying(PlayItem playItem) {
                Log.d(TAG, "mPlayerStateListener --- onPlayerPlaying");
            }

            @Override
            public void onPlayerPaused(PlayItem playItem) {
                Log.d(TAG, "mPlayerStateListener --- onPlayerPaused");
            }
        };
        PlayerManager.getInstance().addPlayControlStateCallback(mPlayerStateListener);
    }

    @RequiresApi(api = Build.VERSION_CODES.Q)
    private void handleTabRv() {
        LinearLayoutManager linearLayout = new LinearLayoutManager(getContext());
        linearLayout.setOrientation(LinearLayoutManager.VERTICAL);
        mTabRecyclerView.setLayoutManager(linearLayout);
        mTabRecyclerView.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
                super.getItemOffsets(outRect, view, parent, state);
                outRect.bottom = ResUtil.getDimen(R.dimen.y24);
            }
        });
        mTabAdapter = new TabAdapter();
        mTabAdapter.setOnItemClickListener((view, viewType, broadcastTabData, position) -> {
            updateTabSelectStatus(position);
            mContentViewPager.setCurrentItem(position, false);
            Log.d(TAG, "tab click " + broadcastTabData.id + ", target position = " + position);
        });
        mTabRecyclerView.setAdapter(mTabAdapter);
        if (SkinHelper.isDayMode()) {
            mTabRecyclerView.setVerticalScrollbarThumbDrawable(ResUtil.getDrawable(R.drawable.broadcast_scroll_bar_day));
            mTabRecyclerView.setVerticalScrollbarTrackDrawable(ResUtil.getDrawable(R.drawable.broadcast_scroll_bar_track_day));
        } else {
            mTabRecyclerView.setVerticalScrollbarThumbDrawable(ResUtil.getDrawable(R.drawable.broadcast_scroll_bar));
            mTabRecyclerView.setVerticalScrollbarTrackDrawable(ResUtil.getDrawable(R.drawable.broadcast_scroll_bar_track));
        }
    }

    private void setLastSelectTabPosFromTabDataList(List<BroadcastTabData> tabData){
        if (tabData == null || tabData.isEmpty()){
            return;
        }
        for (int i = 0; i < tabData.size(); i++) {
            if (tabData.get(i).isSelected){
                mLastSelectTabPos = i;
                break;
            }
        }
        Log.d(TAG, "setLastSelectTabPosFromTabDataList mLastSelectTabPos=" + mLastSelectTabPos);
    }

    private void updateTabSelectStatus(int position){
        Log.d(TAG, "updateTabSelectStatus mLastSelectTabPos=" + mLastSelectTabPos + ", target position=" + position);
        List<BroadcastTabData> tabDataList = mTabAdapter.getDataList();
        if (tabDataList != null
                && !tabDataList.isEmpty()
                && mLastSelectTabPos >= 0
                && mLastSelectTabPos < tabDataList.size()
                && position >= 0
                && position < tabDataList.size()) {
            tabDataList.get(mLastSelectTabPos).isSelected = false;
            tabDataList.get(position).isSelected = true;
            mTabAdapter.notifyItemChanged(mLastSelectTabPos);
            mTabAdapter.notifyItemChanged(position);
            mLastSelectTabPos = position;
        }
    }

    private void scrollToPositionAndTop(RecyclerView recyclerView, LinearLayoutManager linearLayoutManager, int position) {
        LinearSmoothScroller linearSmoothScroller = new LinearSmoothScroller(recyclerView.getContext()){
            @Override
            protected int getVerticalSnapPreference() {
                return LinearSmoothScroller.SNAP_TO_START;
            }
        };
        linearSmoothScroller.setTargetPosition(position);
        linearLayoutManager.startSmoothScroll(linearSmoothScroller);
    }

    private void handleContentVP(){
         mContentVPAdapter = new CategoriesFragmentAdapter(getChildFragmentManager(), null, null);
         mContentViewPager.setAdapter(mContentVPAdapter);
    }

    private void handleOtherUi(View view) {
        mLoadingView = view.findViewById(R.id.loading_view);
        mLayout = view.findViewById(R.id.layout);
        mVsLayoutErrorPage=view.findViewById(R.id.vs_layout_error_page);
        hideLoading();
        // 所见即可说，语音操控滑动
        mScrollUp = view.findViewById(R.id.cd_up);
        mScrollDown = view.findViewById(R.id.cd_down);
        // BroadcastTabContentFragment中有此逻辑
//        mScrollUp.setOnClickListener((v -> ScrollingUtil.scrollListByVoice(mContentRecyclerView, -1)));
//        mScrollDown.setOnClickListener((v -> ScrollingUtil.scrollListByVoice(mContentRecyclerView, 1)));
    }

    @Override
    protected int getLayoutId_Tow() {
        return 0;
    }

    @Override
    protected TabBroadCastPresenter createPresenter() {
        return new TabBroadCastPresenter(this);
    }

    @Override
    public void onStart() {
        super.onStart();
        Log.d(TAG, "onStart");
        hideErrorLayout();
        showLoading();
        mPresenter.requestData(mCategoryId);
    }

    @Override
    public void showTab(List<BroadcastTabData> tabData) {
        Log.d(TAG, "showTab() tabData.size:" + (tabData == null ? 0 : tabData.size()) + ", tabData:" + tabData);
        hideErrorLayout();
        hideLoading();
        mTabAdapter.setDataList(tabData);
        setLastSelectTabPosFromTabDataList(tabData);
    }

    @Override
    public void showTabContent(List<Fragment> fragments) {
        Log.d(TAG, "showTabContent() fragments.size:" + (fragments == null ? 0 : fragments.size()));
        mContentVPAdapter.updateFragments(fragments);
        mContentViewPager.setCurrentItem(mLastSelectTabPos, false);
        if (fragments != null){
            mContentViewPager.setOffscreenPageLimit(fragments.size());
        }
    }

    @Override
    public void showError(Exception e) {
        Log.d(TAG, "showError() error:" + e.toString());
        hideLoading();
        ViewUtil.setViewVisibility(mLayout, View.GONE);
        if ((e instanceof ApiException) && ErrorCode.NO_NET == ((ApiException) e).getCode()){
            showNoNetWorkView();
        }
    }

    private void showNoNetWorkView() {
        if (mVsLayoutErrorPage == null) {
            return;
        }
        mErrorView = mVsLayoutErrorPage.inflate();
        TextView tvNetworkError = mErrorView.findViewById(R.id.tv_status_page_network_error);
        mVsLayoutErrorPage = null;
        if (tvNetworkError != null) {
            tvNetworkError.setOnClickListener(v -> {
                if (!AntiShake.check(v.getId())) {
                    if (!NetworkUtil.isNetworkAvailable(getContext(), true)) {
                        return;
                    }
                    if (mPresenter != null) {
                        hideErrorLayout();
                        showLoading();
                        mPresenter.requestData(mCategoryId);
                    }
                }
            });
        }
        ViewUtil.setViewVisibility(mErrorView, View.VISIBLE);
    }

    private void hideErrorLayout() {
        ViewUtil.setViewVisibility(mErrorView, View.GONE);
    }

    @Override
    protected void showLoading() {
        super.showLoading();
        ViewUtil.setViewVisibility(mLayout, View.GONE);
        ViewUtil.setViewVisibility(mLoadingView, View.VISIBLE);
    }

    @Override
    protected void hideLoading() {
        super.hideLoading();
        ViewUtil.setViewVisibility(mLayout, View.VISIBLE);
        ViewUtil.setViewVisibility(mLoadingView, View.GONE);
    }

    @Override
    public void onItemViewVisible(boolean visible, int position) {
        // 上报相关
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        PlayerManager.getInstance().removePlayControlStateCallback(mPlayerStateListener);
    }

    @RequiresApi(api = Build.VERSION_CODES.Q)
    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        mTabRecyclerView.setAdapter(null);
        mTabRecyclerView.setLayoutManager(null);
        mTabRecyclerView.getRecycledViewPool().clear();
        handleTabRv();
        mContentViewPager.setAdapter(null);
        handleContentVP();
        mPresenter.requestData(mCategoryId);
    }

    @RequiresApi(api = Build.VERSION_CODES.Q)
    @Override
    public void onMultiWindowModeChanged(boolean isInMultiWindowMode) {
        super.onMultiWindowModeChanged(isInMultiWindowMode);
        mTabRecyclerView.setAdapter(null);
        mTabRecyclerView.setLayoutManager(null);
        mTabRecyclerView.getRecycledViewPool().clear();
        handleTabRv();
        mContentViewPager.setAdapter(null);
        handleContentVP();
        mPresenter.requestData(mCategoryId);
    }
}

class TabAdapter extends BaseAdapter<BroadcastTabData> {

    @Override
    protected BaseHolder<BroadcastTabData> getViewHolder(ViewGroup parent, int viewType) {
        return new TabViewHolder(inflate(parent, R.layout.item_broadcast_tab_4, viewType));
    }

    static class TabViewHolder extends BaseHolder<BroadcastTabData> {
        TextView mTxt;

        public TabViewHolder(View itemView) {
            super(itemView);
            mTxt = itemView.findViewById(R.id.tab_txt);
        }

        @Override
        public void setupData(BroadcastTabData tab, int position) {
            mTxt.setText(tab.name);
            mTxt.setSelected(tab.isSelected);
        }
    }
}

//class ContentAdapter extends BaseAdapter<BroadcastTabContentData> {
//    public static final int TYPE_CONTENT = 0;
//    public static final int TYPE_TITLE = 1;
//
//    private long mCurrentPlayingId;
//    private int mCurrentPosition = -1;
//
//    @Override
//    protected BaseHolder<BroadcastTabContentData> getViewHolder(ViewGroup parent, int viewType) {
//        if (viewType == TYPE_TITLE) {
//            return new BroadcastHeadViewHolder(inflate(parent, R.layout.item_broadcast_content_head, viewType));
//        }
////        return new BroadcastContentViewHolder(inflate(parent, R.layout.item_broadcast_content_4, viewType));
//        return new BroadcastViewHolder(inflate(parent, R.layout.item_subcategory_broadcast_local, viewType));
//    }
//
//    @Override
//    public int getItemViewType(int position) {
//        return getItemData(position).getType();
//    }
//
//    @Override
//    public long getItemId(int position) {
//        return position;
//    }
//
//    public void setSelected(long playingId) {
//        if (mCurrentPlayingId != playingId) {
//            for (int i = 0, size = getItemCount(); i < size; i++) {
//                BroadcastTabContentData itemBean = mDataList.get(i);
//                //取消上一个播放状态
//                long broadcastId = itemBean.getBroadcastId();
//                if (broadcastId == mCurrentPlayingId) {
//                    itemBean.setPlaying(false);
//                    notifyItemChanged(i);
//                }
//                //ID一样就显示播放状态
//                if (broadcastId == playingId) {
//                    itemBean.setPlaying(true);
//                    notifyItemChanged(i);
//                    mCurrentPosition = i;
//                }
//            }
//            mCurrentPlayingId = playingId;
//        } else {
//            if (mCurrentPosition != -1) {
//                notifyItemChanged(mCurrentPosition);
//            }
//        }
//    }
//
//    static class BroadcastHeadViewHolder extends BaseHolder<BroadcastTabContentData> {
//        TextView mTxt;
//
//        public BroadcastHeadViewHolder(View itemView) {
//            super(itemView);
//            mTxt = itemView.findViewById(R.id.head_title);
//        }
//
//        @Override
//        public void setupData(BroadcastTabContentData content, int position) {
//            mTxt.setText(content.getName());
//        }
//    }
//
//    static class BroadcastContentViewHolder extends BaseHolder<BroadcastTabContentData> {
//        TextView mTxt;
//
//        public BroadcastContentViewHolder(View itemView) {
//            super(itemView);
//            mTxt = itemView.findViewById(R.id.content_title);
//        }
//
//        @Override
//        public void setupData(BroadcastTabContentData content, int position) {
//            mTxt.setText(content.getName());
//        }
//    }
//
//    static class BroadcastViewHolder extends BaseHolder<BroadcastTabContentData> {
//
//        ImageView mIvBroadcastCover;
//        TextView mTvBroadcastName;
//        ImageView live_icon;
//        View card_layout_playing;
//        TextView tvListenNum;
//        ImageView ivPlay;
//        View rootView;
//
//        BroadcastViewHolder(final View itemView) {
//            super(itemView);
//            mIvBroadcastCover=itemView.findViewById(R.id.iv_broadcast_cover);
//            mTvBroadcastName=itemView.findViewById(R.id.tv_broadcast_name);
//            live_icon=itemView.findViewById(R.id.live_icon);
//            card_layout_playing=itemView.findViewById(R.id.card_layout_playing);
//
//            tvListenNum = itemView.findViewById(R.id.tvListenNum);
//            ivPlay = itemView.findViewById(R.id.ivPlay);
//            rootView = itemView.findViewById(R.id.ll_broadcast);
//        }
//
//        @Override
//        public void setupData(BroadcastTabContentData broadcastTabContentData, int position) {
//
//            if (broadcastTabContentData.isPlaying()) {
//                card_layout_playing.setVisibility(View.VISIBLE);
//                mTvBroadcastName.setActivated(true);
//                itemView.setActivated(true);
//                tvListenNum.setSelected(true);
//                ivPlay.setSelected(true);
//                tvListenNum.setTextColor(ResUtil.getColor(R.color.text_color_7));
//            } else {
//                card_layout_playing.setVisibility(View.GONE);
//                mTvBroadcastName.setActivated(false);
//                itemView.setActivated(false);
//                tvListenNum.setSelected(false);
//                ivPlay.setSelected(false);
//                tvListenNum.setTextColor(ResUtil.getColor(R.color.text_color_8));
//            }
//            tvListenNum.setText(StringUtil.formatNum(broadcastTabContentData.getPlayTimes()));
//            mTvBroadcastName.setText(broadcastTabContentData.getName() + "  " + broadcastTabContentData.getFreq());
//            rootView.setContentDescription(broadcastTabContentData.getName());
//            Log.i("BroadcastListFragment", "url = " + broadcastTabContentData.getIcon());
//            ImageLoader.getInstance().displayImage(itemView.getContext(), UrlUtil.getCustomPicUrl(UrlUtil.PIC_100_100, broadcastTabContentData.getIcon()), mIvBroadcastCover,
//                    ResUtil.getDrawable(R.drawable.media_default_pic));
//        }
//    }
//}
