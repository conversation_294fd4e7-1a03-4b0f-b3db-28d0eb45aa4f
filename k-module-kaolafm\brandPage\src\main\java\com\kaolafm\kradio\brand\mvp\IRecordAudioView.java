package com.kaolafm.kradio.brand.mvp;

import com.kaolafm.kradio.lib.base.mvp.IView;
import com.kaolafm.opensdk.api.speech2text.model.SpeechToTextError;
import com.kaolafm.opensdk.api.speech2text.model.SpeechToTextResult;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public interface IRecordAudioView extends IView {
    void onRecordingStarted();

    void onRecordingStopped();

    void onRecordingError(@NotNull SpeechToTextError var1);

    void onRecordDurationTooShort();

    void onSpeechResultReceived(@NotNull SpeechToTextResult var1);

    void onNetWordError(@Nullable Object[] var1);
}
