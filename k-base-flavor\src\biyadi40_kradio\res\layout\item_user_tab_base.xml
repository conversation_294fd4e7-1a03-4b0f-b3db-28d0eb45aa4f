<?xml version="1.0" encoding="utf-8"?>
<com.kaolafm.kradio.common.widget.CScaleLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:sfl="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <com.kaolafm.kradio.lib.widget.square.SquareImageView
        android:id="@+id/iv_play_cover"
        android:layout_width="@dimen/m114"
        android:layout_height="@dimen/m114"
        android:scaleType="fitCenter"
        sfl:canScale="false" />

    <LinearLayout
        android:id="@+id/ll_item_user_tab_base"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/selector_user_list_item_bg"
        android:gravity="center_vertical"
        android:orientation="vertical"
        android:paddingLeft="@dimen/x30">

        <TextView
            android:layout_weight="30"
            android:id="@+id/user_tab_title"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_marginRight="@dimen/x50"
            android:gravity="center_vertical"
            android:layout_marginTop="@dimen/m2"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/user_item_title_text_color"
            android:textSize="@dimen/text_size_title4"
            tools:text="行动派" />

        <TextView
            android:layout_weight="26"
            android:id="@+id/user_tab_content"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:gravity="top"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/user_item_subtitle_text_color"
            android:textSize="@dimen/text_size_title5"
            tools:text="1天前更新" />

    </LinearLayout>

</com.kaolafm.kradio.common.widget.CScaleLinearLayout>
