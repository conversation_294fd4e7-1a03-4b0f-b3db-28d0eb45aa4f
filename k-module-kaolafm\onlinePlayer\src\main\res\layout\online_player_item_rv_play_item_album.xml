<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:background="@drawable/online_player_sound_selected">

    <ImageView
        android:id="@+id/ivPlaying"
        android:layout_width="@dimen/x18"
        android:layout_height="@dimen/y18"
        android:layout_marginStart="@dimen/m17"
        android:src="@drawable/online_player_animated_sound_dump"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.kaolafm.kradio.online.common.view.OnlineAutoMarqueenTextView
        android:id="@+id/tvItemTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/m48"
        android:layout_marginTop="@dimen/y14"
        android:layout_marginEnd="@dimen/x8"
        android:layout_marginBottom="0dp"
        android:ellipsize="end"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:gravity="center_vertical"
        android:marqueeRepeatLimit="marquee_forever"
        android:singleLine="true"
        android:textColor="@color/online_player_broadcast_title"
        android:textSize="@dimen/m24"
        android:textStyle="bold"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@id/subtitle"
        app:layout_constraintEnd_toStartOf="@id/ivBuyType"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginBottom="@dimen/y14"
        tools:text="乐海星哥乐海星哥乐海星哥乐海星哥乐海星哥乐海星哥乐海星哥乐海星哥乐海星哥乐海星哥乐海星哥乐海星哥乐海星哥乐海星哥" />

    <TextView
        android:id="@+id/audioListenFlagTv"
        android:layout_width="@dimen/x48"
        android:layout_height="@dimen/y24"
        android:layout_marginEnd="@dimen/x8"
        android:background="@drawable/online_player_reserve"
        android:gravity="center"
        android:textColor="@color/online_player_album_tag_tv"
        android:textSize="@dimen/m16"
        app:layout_constraintBottom_toBottomOf="@id/subtitle"
        app:layout_constraintEnd_toStartOf="@id/subtitle"
        app:layout_constraintStart_toStartOf="@id/tvItemTitle"
        app:layout_constraintTop_toTopOf="@id/subtitle"
        tools:text="@string/online_player_live_listen_message" />

    <TextView
        android:id="@+id/subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/x8"
        android:layout_marginTop="0px"
        android:layout_marginBottom="@dimen/y14"
        android:alpha="0.6"
        android:text="主播"
        android:textColor="@color/online_player_broadcast_subtitle"
        android:textSize="@dimen/m20"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/audioListenFlagTv"
        app:layout_constraintTop_toBottomOf="@id/tvItemTitle"
        app:layout_goneMarginStart="0dp" />

    <com.kaolafm.kradio.common.widget.CScaleImageView
        android:id="@+id/ivBuyType"
        android:layout_width="@dimen/m72"
        android:layout_height="@dimen/m72"
        android:layout_marginEnd="@dimen/x4"
        android:paddingStart="@dimen/m22"
        android:paddingEnd="@dimen/m22"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/ivLiked"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/online_player_album_clock" />

    <com.kaolafm.kradio.common.widget.CScaleImageView
        android:id="@+id/ivLiked"
        android:layout_width="@dimen/m72"
        android:layout_height="@dimen/m72"
        android:paddingStart="@dimen/m22"
        android:paddingEnd="@dimen/m20"
        android:src="@drawable/online_player_like"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>