package com.kaolafm.kradio.component.ui.base.utils;

import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.RectF;
import android.text.style.ReplacementSpan;

import com.kaolafm.kradio.component.ui.R;
import com.kaolafm.kradio.lib.utils.ResUtil;

public class RoundBackGroundColorSpan extends ReplacementSpan {
    private int bgColor;
    private int textColor;
    private boolean isLine;// 是否超过一行

    public RoundBackGroundColorSpan(int bgColor, int textColor,boolean isLine) {
        super();
        this.isLine=isLine;
        this.bgColor = bgColor;
        this.textColor = textColor;
    }

    @Override
    public int getSize(Paint paint, CharSequence text, int start, int end, Paint.FontMetricsInt fm) {
        return ((int) paint.measureText(text, start, end) + ResUtil.getDimen(R.dimen.m30));
    }

    @Override
    public void draw(Canvas canvas, CharSequence text, int start, int end, float x, int top, int y, int bottom, Paint paint) {
        int color1 = paint.getColor();
        paint.setColor(this.bgColor);
        paint.setStyle(Paint.Style.STROKE);
//        paint.setStrokeWidth(20);
        if (isLine){
            //超过一行
            canvas.drawRoundRect(new RectF(x, top + ResUtil.getDimen(R.dimen.m5),
                    x + ((int) paint.measureText(text, start, end) + ResUtil.getDimen(R.dimen.m20)),
                    bottom - ResUtil.getDimen(R.dimen.m7)), ResUtil.getDimen(R.dimen.m6), ResUtil.getDimen(R.dimen.m6), paint);
            paint.setColor(this.textColor);
            paint.setStyle(Paint.Style.FILL);
            canvas.drawText(text, start, end, x + ResUtil.getDimen(R.dimen.m10), y - ResUtil.getDimen(R.dimen.m1), paint);
        }else {
            canvas.drawRoundRect(new RectF(x, top + ResUtil.getDimen(R.dimen.m3),
                    x + ((int) paint.measureText(text, start, end) + ResUtil.getDimen(R.dimen.m20)),
                    bottom - ResUtil.getDimen(R.dimen.m2)), ResUtil.getDimen(R.dimen.m6), ResUtil.getDimen(R.dimen.m6), paint);
            paint.setColor(this.textColor);
            paint.setStyle(Paint.Style.FILL);
            canvas.drawText(text, start, end, x + ResUtil.getDimen(R.dimen.m10), y - ResUtil.getDimen(R.dimen.m3), paint);
        }
        paint.setColor(color1);
    }
}
