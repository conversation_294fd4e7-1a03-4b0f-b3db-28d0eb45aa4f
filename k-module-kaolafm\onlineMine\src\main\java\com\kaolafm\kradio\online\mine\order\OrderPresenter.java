package com.kaolafm.kradio.online.mine.order;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.kradio.lib.utils.NetworkManager;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.purchase.model.Order;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.http.error.ErrorCode;

import java.util.List;

/**
 * @Package: com.kaolafm.kradio.comprehensive.user.order
 * @Description:
 * @Author: Maclay
 * @Date: 17:32
 */
public class OrderPresenter extends BasePresenter<OrderModel, IMyOrderView> implements HttpCallback<BasePageResult<List<Order>>>, NetworkManager.INetworkReady {


    int pageNum = 1;
    boolean hasNext = false;
    boolean isLoading = false;

    public OrderPresenter(IMyOrderView view) {
        super(view);
    }

    @Override
    public void start() {
        super.start();
        NetworkManager.getInstance().addNetworkReadyListener(this);
    }

    @Override
    public void destroy() {
        super.destroy();
        NetworkManager.getInstance().removeNetworkReadyListener(this);
    }

    @Override
    protected OrderModel createModel() {
        OrderModel model = new OrderModel();
        model.setCallBack(this);
        return model;
    }

    void getList() {
        if (isLoading) {
            return;
        }
        if (mModel != null) {
            isLoading = true;
            pageNum = 1;
            mModel.getOrderList(pageNum);
        }
    }

    void loadingNext() {
        if (isLoading) {
            return;
        }
        if (mModel != null && hasMore()) {
            isLoading = true;
            pageNum++;
            mModel.getOrderList(pageNum);
        }
    }

    boolean hasMore() {
        return hasNext;
    }


    @Override
    public void onSuccess(BasePageResult<List<Order>> listBasePageResult) {
        if (mView != null && listBasePageResult != null) {
            hasNext = listBasePageResult.getHaveNext() == 1;
            mView.showList(listBasePageResult.getDataList(), listBasePageResult.getCurrentPage());
        }
        isLoading = false;
    }

    @Override
    public void onError(ApiException e) {
        if (e != null && e.getCode() == ErrorCode.HTTPS_CERTIFICATE_ERROR) {
            mView.showError(ResUtil.getString(R.string.home_network_certificate_error), true);
        } else {
            mView.showError();
        }
        isLoading = false;
    }

    @Override
    public void networkChange(boolean hasNetwork) {
        if (hasNetwork) {
            getList();
        }
    }
}
