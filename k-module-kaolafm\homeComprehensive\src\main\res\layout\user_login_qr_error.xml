<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/m188"
    android:layout_height="@dimen/m188"
    android:id="@+id/qr_error_layout"
    android:layout_gravity="center_horizontal"
    android:background="#B3000001">

    <ImageView
        android:id="@+id/network_nosigin"
        android:layout_width="@dimen/m32"
        android:layout_height="@dimen/m32"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/y53"
        android:src="@drawable/user_qr_error_refresh" />

    <TextView
        android:id="@+id/tv_network_nosign"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/network_nosigin"
        android:gravity="center_horizontal"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/m25"
        android:textSize="@dimen/m20"
        android:text="@string/user_qr_error_no_network_msg"
        android:textColor="@color/text_color_1" />

</RelativeLayout>