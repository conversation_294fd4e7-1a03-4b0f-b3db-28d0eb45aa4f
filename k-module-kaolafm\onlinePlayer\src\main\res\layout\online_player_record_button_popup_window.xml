<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    <!--试听-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/live_listen_button_layout"
        android:layout_width="@dimen/x108"
        android:layout_height="@dimen/y108"
        android:layout_marginEnd="@dimen/x26"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@id/recordIv"
        app:layout_constraintDimensionRatio="W,1:1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/recordIv"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/live_listen_message_image"
            android:layout_width="@dimen/x108"
            android:layout_height="@dimen/y108"
            android:scaleType="fitCenter"
            android:src="@drawable/online_player_listen"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.kaolafm.kradio.common.widget.YunTingMusicPlayAnimationView
            android:id="@+id/live_listen_anim_image"
            android:layout_width="@dimen/x108"
            android:layout_height="@dimen/y108"
            android:visibility="gone"
            app:fullEndColor="#8E91FF"
            app:fullStartColor="#FF5579"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="W,1:1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:lineCount="5"
            app:strokeCenterColor="#FFFFFFFF"
            app:strokeEndColor="#26FFFFFF"
            app:strokeStartColor="#26FFFFFF"
            app:strokeWidth="@dimen/x1" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!--取消-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/live_cancel_button_layout"
        android:layout_width="@dimen/x108"
        android:layout_height="@dimen/y108"
        android:layout_marginStart="@dimen/x26"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@id/recordIv"
        app:layout_constraintDimensionRatio="W,1:1"
        app:layout_constraintLeft_toRightOf="@id/recordIv"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/live_cancel_message_image"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleType="fitCenter"
            android:src="@drawable/online_player_cancel"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageButton
        android:id="@+id/recordIv"
        android:layout_width="@dimen/x144"
        android:layout_height="@dimen/y144"
        android:background="@color/transparent"
        android:scaleType="fitCenter"
        android:src="@drawable/online_player_record_mic"
        app:layout_constraintBottom_toTopOf="@id/recordTipText"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/recordTipText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/online_player_live_record_tip_color"
        android:textSize="@dimen/m20"
        app:layout_constraintEnd_toEndOf="@id/recordIv"
        app:layout_constraintStart_toStartOf="@id/recordIv"
        app:layout_constraintTop_toBottomOf="@id/recordIv"
        tools:text="点击说话" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/recordTextViewParent"
        android:layout_width="@dimen/x144"
        android:layout_height="@dimen/y144"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@id/recordIv"
        app:layout_constraintEnd_toEndOf="@id/recordIv"
        app:layout_constraintStart_toStartOf="@id/recordIv"
        app:layout_constraintTop_toTopOf="@id/recordIv">

        <ImageView
            android:id="@+id/recordTextIv"
            android:layout_width="@dimen/x144"
            android:layout_height="@dimen/y144"
            android:scaleType="fitCenter"
            android:src="@drawable/online_player_recording"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/recordTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/y30"
            android:text="@string/online_player_live_send_message"
            android:textColor="@color/online_player_live_recording_time_color"
            android:textSize="@dimen/m18"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>