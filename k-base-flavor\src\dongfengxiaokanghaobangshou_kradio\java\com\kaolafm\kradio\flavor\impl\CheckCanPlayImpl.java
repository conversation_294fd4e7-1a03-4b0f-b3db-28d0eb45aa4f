package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.os.RemoteException;
import android.util.Log;

import com.fce.thirdutil.FceThirdUtil;
import com.fce.thirdutil.NetworkStateResult;
import com.kaolafm.kradio.flavor.carnetwork.api.AuthApiRequest;
import com.kaolafm.kradio.flavor.carnetwork.carnetworkutils.AuthConstants;
import com.kaolafm.kradio.flavor.carnetwork.carnetworkutils.AuthListener;
import com.kaolafm.kradio.flavor.carnetwork.carnetworkutils.CarAuthUtil;
import com.kaolafm.kradio.flavor.carnetwork.dialog.AuthDialog;
import com.kaolafm.kradio.k_kaolafm.home.gallery.PageJumper;
import com.kaolafm.kradio.k_kaolafm.home.item.HomeCell;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.base.flavor.KRadioAuthInter;
import com.kaolafm.kradio.lib.utils.SharedPreferenceUtil;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import com.kaolafm.sdk.core.mediaplayer.CheckCanPlayInter;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import com.kaolafm.sdk.core.mediaplayer.PlayerService;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

import static com.kaolafm.kradio.flavor.carnetwork.carnetworkutils.AuthConstants.SECRETCODE;


public class CheckCanPlayImpl implements CheckCanPlayInter {
    private static final String TAG = "CheckCanPlayImpl";
    //    private Category.Item mCategoryItem;
    //https://app.huoban.com/tables/2100000007530121/items/2300001428094791
    private HomeCell homeCell;

    private PlayItem mPlayItem;
    private boolean fromUser;
    private String mType;
    private PlayerService.PlayerBinder playerBinder;

    private SharedPreferenceUtil mSPUtil;
    private AuthApiRequest mChangAnApiRequest;
    private AuthDialog mAuthDialog;

    private Activity preActivity;
    private Activity curActivity;
    private Handler mainHandler;

    private boolean isFromTTS = false;//防止语音播报通过后继续调用播放器导致流程走两次

    private static class InstanceHolder {
        //AudioStatusManager.injectKLCheckCanPlayListener("com.kaolafm.auto.flavor.impl.CheckCanPlayImpl")已经初始化了 ,为了获取同一个对象
        private final static CheckCanPlayInter CheckCanPlay = AudioStatusManager.getInstance().getCheckCanPlayInter();
    }

    public static CheckCanPlayInter getInstance() {
        return InstanceHolder.CheckCanPlay;
    }


    @Override
    public boolean checkPlay(Object... args) {
        Log.i(TAG, "CheckCanPlayImpl checkPlay");
        mType = (String) args[1];
        //如果是直播获取到的item为Category.Item类型
        if (mType.equals(KRadioAuthInter.METHOD_LIVING)) {
            homeCell = (HomeCell) args[0];
        } else {
            mPlayItem = (PlayItem) args[0];
        }
        fromUser = (boolean) args[2];

        if (playerBinder == null) {
            playerBinder = PlayerManager.getInstance().getPlayerBinder();
        }
        if (mainHandler == null) {
            mainHandler = new Handler(Looper.getMainLooper());
        }

        if (isFromTTS) {
            isFromTTS = false;
            doPlay(mType);
            return true;
        }

        CarAuthUtil.ischecking = true;
        checkAction(new AuthListener() {
            @Override
            public void onIsCanUse(boolean isCanUse) {
                Log.i(TAG, "onIsCanUse = " + isCanUse + ",mType = " + mType);
//                PlayerManager.getInstance().notifyIPlayChangedListener(mPlayItem);
                if (isCanUse) {
                    //可以播放
                    runUIdismiss();
                    doPlay(mType);
                } else {
                    //不可播放
                    doPause(mType);
                }
                CarAuthUtil.ischecking = false;
            }

            @Override
            public void Error(int errCode, String errMsg) {
                //不可播放
                doPause(mType);
                CarAuthUtil.ischecking = false;
            }
        });

        return true;
    }

    @Override
    public void addListener(Object... objects) {

    }

    @Override
    public void removeListener(Object... objects) {

    }


    //执行播放操作
    private void doPlay(String type) {
        if (mPlayItem != null) {
            mainHandler.post(new Runnable() {
                @Override
                public void run() {
                    //已在主线程中，可以更新UI
                    PlayerManager.getInstance().notifyIPlayChangedListener(mPlayItem);

                    switch (type) {
                        case CheckCanPlayInter.METHOD_PLAY:
                            try {
                                if (playerBinder != null) {
                                    playerBinder.playItem(fromUser);
                                }
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }
                            break;

                        case CheckCanPlayInter.METHOD_START:
                            //https://app.huoban.com/tables/2100000007530121/items/2300001427814367?userId=1874548
                            try {
                                if (playerBinder != null) {
                                    playerBinder.startItem(mPlayItem, fromUser);
                                }
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }
                            break;

                        case KRadioAuthInter.METHOD_TTS:
//                isFromTTS = true;
//                boolean isIntercept = ClockPlayer.getInstance(AppDelegate.getInstance().getContext()).intercept();
//                String radioType = KLAutoPlayerManager.getInstance().getRadioType();
//                if (isIntercept && String.valueOf(ResType.RADIO_TYPE).equals(radioType)) {
//                    PlayerManager.getInstance().reset();
//                    ClockPlayer.getInstance(AppDelegate.getInstance().getContext())
//                            .setClockPlayDoneListener(new ClockPlayer.ClockPlayDoneListener() {
//                                @Override
//                                public void onClockPlayDone() {
//                                    ClockPlayer.getInstance(AppDelegate.getInstance().getContext())
//                                            .setClockPlayDoneListener(null);
//                                    PlayerManager.getInstance().originStart(mPlayItem);
//                                }
//                            });
//                    ClockPlayer.getInstance(AppDelegate.getInstance().getContext()).play();
//                    PlayerManager.getInstance().notifyIPlayChangedListener(mPlayItem);
//                } else {
//                    PlayerManager.getInstance().originStart(mPlayItem);
//                }
                            break;
                        case KRadioAuthInter.METHOD_NETCHANGE:
                            //如果是网络切换不进行播放动作的执行，防止正在播放中的重复播放
                            break;
                        case KRadioAuthInter.METHOD_LIVING:
                            //跳转至直播界面
//                PageJumper.getInstance().jumpToLivePage(mCategoryItem);
                            PageJumper.getInstance().jumpToLivePage(homeCell.playId);
                            break;
                        default:
                    }
                }
            });
        }
    }

    //执行暂停操作
    private void doPause(String type) {
        //鉴权不通过通知卡片切换
        if (mainHandler != null) {
            mainHandler.post(new Runnable() {
                @Override
                public void run() {
                    //已在主线程中，可以更新UI
                    if (mPlayItem != null) {
                        PlayerManager.getInstance().notifyIPlayChangedListener(mPlayItem);
                    }


                    if (playerBinder != null && playerBinder.isPlaying()) {
                        try {
                            playerBinder.pause(false);
                        } catch (RemoteException e) {
                            e.printStackTrace();
                        }
                    }
                }
            });
        }

    }


    public void checkAction(final AuthListener mChangAnAuthListener) {

        if (!CarAuthUtil.checkAuthStatusAndDate()) {
            mChangAnAuthListener.onIsCanUse(true);
            return;
        }
        curActivity = AppManager.getInstance().getCurrentActivity();
        if (preActivity != curActivity && curActivity != null) {
            preActivity = curActivity;
            if (curActivity != null) mAuthDialog = new AuthDialog((Context) curActivity);
        } else {
            if (mAuthDialog == null && curActivity != null) {
                mAuthDialog = new AuthDialog((Context) curActivity);
            }
        }

        //显示loading状态
        runUIshow(AuthConstants.CODE_LOADING);
        ChangAnAuthRequest(mChangAnAuthListener);

    }


    /**
     * 判断当前网络是否需要鉴权，获取当前的Access token 并 请求联通结果获取流量鉴权结果
     */
    private void ChangAnAuthRequest(final AuthListener mChangAnAuthListener) {

        Log.i(TAG, "checkAction: needAuthByNetWork = " + CarAuthUtil.needAuthByNetWork());
        if (!CarAuthUtil.needAuthByNetWork()) {
            runUIshow(AuthConstants.CODE_NET_ERROR);
            mChangAnAuthListener.onIsCanUse(false);
            return;
        }

        //todo 暂时默认使用测试环境的联通接口
        AuthConstants.BASEURL = AuthConstants.BASEURL_TEST;

        String appid = AuthConstants.APPID;
        String vid = FceThirdUtil.getVin(AppDelegate.getInstance().getContext()).getVin();
        String vinstate = FceThirdUtil.getVin(AppDelegate.getInstance().getContext()).getState();
        if (vinstate == null || ("false").equals(vinstate) || ("").equals(vinstate)) {
            runUIshow(AuthConstants.CODE_VIN_FALSE);
            mChangAnAuthListener.onIsCanUse(false);
            Log.i(TAG, "vin state value is false ,state = " + vinstate + "    vid = " + vid);
            return;
        }

        String itemid = AuthConstants.ITEMID;
        long timestamp = System.currentTimeMillis() / 1000;
        if (timestamp == 0) {
            runUIshow(AuthConstants.CODE_TIMESTAMP_ERROR);
            mChangAnAuthListener.onIsCanUse(false);
            Log.i(TAG, "timestamp value is false ,timestamp = " + timestamp);
            return;
        }
//        String strUserCode = appid + "&" + timestamp + "&" + SECRETCODE;
//        String strUserCode = "UserId=" + appid + "&TimeStamp=" + timestamp + "&SecretCode=" + SECRETCODE;
        String strUserCode = "appid=" + appid + "&timeStamp=" + timestamp + "&secretCode=" + SECRETCODE;
        String usercode = encryptForMD5(strUserCode);
        NetworkStateResult mNetworkStateResult = FceThirdUtil.getNetWorkState(AppDelegate.getInstance().getContext());
        int netsource = mNetworkStateResult.getSource();
        int cartype = mNetworkStateResult.getType();

        Log.i(TAG, "ChangAnAuth  getChangAnAuth  param:  " + "appid = " + appid + ",vid = " + vid + ",vinstate = "+vinstate+",itemid = "
                + itemid + ",timestamp = " + timestamp + ",strUserCode = " + strUserCode + ",usercode = " + usercode
                + ",netsource = " + netsource + ",cartype = " + cartype);
        if (mChangAnApiRequest == null) {
            mChangAnApiRequest = new AuthApiRequest();
        }
        mChangAnApiRequest.requestAuth(appid, vid, itemid, usercode, timestamp
                , netsource, cartype, new HttpCallback<Integer>() {
                    @Override
                    public void onSuccess(Integer integer) {
                        if (integer <= 0) {
                            runUIshow(integer);
                            CarAuthUtil.saveAuthAndDate(String.valueOf(System.currentTimeMillis()), false);
                            mChangAnAuthListener.onIsCanUse(false);
                        } else if (integer == 1) {
                            runUIdismiss();
                            CarAuthUtil.saveAuthAndDate(String.valueOf(System.currentTimeMillis()), true);
                            mChangAnAuthListener.onIsCanUse(true);
                        }
                    }

                    @Override
                    public void onError(ApiException e) {
                        //由于对方接口返回数据与我们的不一致,导致一直走onError.可以通过e.getCode()判断是否成功
                        int code = e.getCode();
                        Log.i(TAG, "ChangAnAuth getChangAnAuth json :  = " + e.toString());
                        if (code <= 0) {
                            runUIshow(code);
                            CarAuthUtil.saveAuthAndDate(String.valueOf(System.currentTimeMillis()), false);
                            mChangAnAuthListener.onIsCanUse(false);
                        } else if (code == 1) {
                            runUIdismiss();
                            CarAuthUtil.saveAuthAndDate(String.valueOf(System.currentTimeMillis()), true);
                            mChangAnAuthListener.onIsCanUse(true);
                        } else if (code == 604 || code == 408) {
                            //超时和其他错误  sdkapi超时errorcode没有暴露  超时状态至为604和408
                            runUIshow(AuthConstants.CODE_TIMEOUT);
                            CarAuthUtil.saveAuthAndDate(String.valueOf(System.currentTimeMillis()), false);
                            mChangAnAuthListener.Error(code, e.getMessage());
                        } else {
                            runUIshow(AuthConstants.CODE_ERROR);
                            CarAuthUtil.saveAuthAndDate(String.valueOf(System.currentTimeMillis()), false);
                            mChangAnAuthListener.Error(code, e.getMessage());
                        }
                    }
                });
    }


    private void runUIshow(final Integer code) {
        try {
            if (mainHandler != null) {
                mainHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        //已在主线程中，可以更新UI
                        if (mAuthDialog != null) {
                            mAuthDialog.setCode(code);
                            mAuthDialog.show();
                        }
                    }
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
            runUIshow(AuthConstants.CODE_ERROR);
        }
    }

    private void runUIdismiss() {
        try {
            if (mainHandler != null) {
                mainHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        //已在主线程中，可以更新UI
                        if (mAuthDialog != null) {
                            mAuthDialog.dismiss();
                        }
                    }
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    //智网将提供secretcode参数用于计算UserCode；计算方法为：
    //usercode = MD5(“appid&timestamp&secretcode”)；
    //在上述计算过程中，各个参数均为其当时的取值。
    private boolean validateUserCode(String userId, String timeStamp, String userCodeReq, String secretCode) {
        String userCode = "UserId=" + userId + "&TimeStamp=" + timeStamp + "&SecretCode=" + secretCode;
        // MD5加密
        userCode = encryptForMD5(userCode);
        if (userCode.equalsIgnoreCase(userCodeReq)) {
            return true;
        } else {
            return false;
        }
    }

    private String encryptForMD5(String souce) {
        if (souce == null || souce.length() == 0) {
            return null;
        }
        StringBuffer hexString = new StringBuffer();
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(souce.getBytes());
            byte hash[] = md.digest();
            for (int i = 0; i < hash.length; i++) {
                if ((0xff & hash[i]) < 0x10) {
                    hexString.append("0" + Integer.toHexString((0xFF & hash[i])));
                } else {
                    hexString.append(Integer.toHexString(0xFF & hash[i]));
                }
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return null;
    }
}
