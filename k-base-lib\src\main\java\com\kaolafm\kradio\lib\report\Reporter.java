package com.kaolafm.kradio.lib.report;

import java.util.List;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/**
 * <AUTHOR>
 * @date 2019-11-01
 */
public interface Reporter {
   /**
    * 初始playlistener
    */
   void initListener();

   /**
    * 初始化播放listener
    */
   void initPlayerListener();

   /**
    * 上报开始播放
    */
   void reportStartPlay(@Nullable Object bean);

   /**
    * 上报直播播放
    */
   void reportLivingPlay(@Nullable Object bean);

   /**
    * 上报其他播放器播放
    */
   void reportStartOtherPlay(@Nullable Object bean);

   /**
    * 上报播放结束事件
    */
   void reportEndPlay(@NotNull String reason);

   /**
    * 上报播放结束事件
    */
   void reportEndPlay(@NotNull String reason, boolean isNeedReport);

   /**
    * 上报卡顿开始
    *
    * @param playItem
    * @param dns
    * @param type     卡顿原因
    */
   void reportBufferingStart(@Nullable Object bean, boolean isSeek);

   /**
    * 上报卡顿结束
    *
    * @param playItem
    * @param dns
    * @param type            卡顿原因
    * @param bufferStartTime 卡顿开始时间
    */
   void reportBufferingEnd(@Nullable Object bean, boolean isSeek, long bufferStartTime);

   /**
    * @param playItem
    * @param what
    * @param extra
    */
   void reportRequestError(@Nullable Object bean, int what, int extra);

   void setPageId(@NotNull String pageId);

   /**
    * 搜索结果 播放
    *
    * @param playType
    * @param callback
    */
   void reportSearchToPlay(@NotNull String playType, @NotNull String callback);

   /**
    * @param way      选择方式	1.语音选择；2.手动选择；3.未知
    * @param radioId  radioId，碎片则为专辑id，ai电台为电台id，专辑为专辑id
    * @param id       内容id 对应选择的结果内容id：碎片id，专辑id、ai电台id、广播id（至少一个）
    * @param position 搜索结果索引号 被选择的结果索引号：1，2，3，4
    * @param result   搜索结果追踪号	搜索服务端透传的数据
    */
   void reportSearchSelectResult(@NotNull String way, @NotNull String radioId, @NotNull String id, @NotNull String position, @NotNull String result);

   /**
    * @param requestAgent 用户原始文本，比如 ‘我要听刘德华的冰雨
    * @param type         1.手动输入；2.点击历史记录；3.点击联想词；4.语音搜索
    * @param result       0：超时；1：参数有误；2：无结果；3：正常
    * @param playType     0:否，1：是
    * @param keyword      语义理解的结果，比如‘刘德华，冰雨’
    * @param contentType  内容分类
    */
   void reportSearchResult(@NotNull String requestAgent, @NotNull String type, @NotNull String result, @NotNull String playType, @NotNull String keyword, @NotNull String contentType);

   /**
    * 音质选择
    *
    * @param type
    */
   void addToneSelectEvent(int type);

   /**
    * 消息语音播报开关
    *
    * @param type
    */
   void addSettingVoiceSwiych(int type);

   /**
    * 消息泡泡曝光
    *
    * @param type
    */
   void addMessageShow(@NotNull String pageId, @NotNull String radiotype, @NotNull String remarks2);

   /**
    * 消息泡泡点击曝光
    *
    * @param type
    */
   void addMessageClike(@NotNull String pageId, @NotNull String radiotype, @NotNull String remarks2);

   /**
    * 负反馈
    *
    * @param audioId
    * @param radioId
    * @param albumId
    * @param callback
    * @param position
    */
   void addMinusFeedbackEvent(@NotNull String audioId, @NotNull String radioId, @NotNull String albumId, @NotNull String callback, @NotNull String position);

   /**
    * 正反馈
    *
    * @param audioId
    * @param radioId
    * @param albumId
    * @param callback
    */
   void addPlusFeedbackEvent(@NotNull String audioId, @NotNull String radioId, @NotNull String albumId, @NotNull String callback);

   /**
    * 登录
    *
    * @param type     登录类型, 1 扫码  2 手机号登录
    * @param remarks1 122113.我的——个人中心——进入登录流程
    *                 122112.已购页面——点击立即登录——进入登录流程
    *                 122111.收听历史——点击立即登录——进入登录流程
    *                 122110.我的订阅——点击立即登录——进入登录流程
    *                 141200.试听碎片轮播至需付费碎片——判断登录
    *                 141201.播放详情页专辑封面下方点击“VIP会员 免费听”——判断登录
    *                 141202.播放详情页内点击需付费碎片——判断登录
    */
   void addLoginEvent(@NotNull String type, @NotNull String remarks1);

   /**
    * 直播留言
    *
    * @param id
    * @param liveId
    * @param compereId
    * @param radioId
    */
   void addLivingLeaveMessageEvent(@NotNull String id, @NotNull String liveId, @NotNull String compereId, @NotNull String radioId);

   /**
    * 订阅与取消订阅
    *
    * @param type          0：取消订阅；1：订阅
    * @param subscribeType 1：专辑；2：电台；3：广播；4：音乐电台（第三方内容源）
    * @param position      1：播放条；2：全屏播放器；3：widget
    * @param radioId       专辑取专辑id，电台取电台id，广播取广播id，音乐取音乐电台id
    */
   void addSubscribeEvent(@NotNull String type, @NotNull String subscribeType, @NotNull String position, @NotNull String radioId);

   /**
    * 播放条控制
    *
    * @param type        1：播放后暂停；2：暂停后播放；3：上一首；4：下一首；5：列表
    * @param controlType 1：点击；2：语音；3：方控；4：其他
    * @param position    1：播放条；2：全屏播放器；3：widget
    */
   void addPlayerUiControlEvent(@NotNull String type, @NotNull String controlType, @NotNull String position);

   /**
    * 推荐展示
    */
   void addRecommendShowEvent(@NotNull List<?> beanList);

   /**
    * 播放列表推荐展示
    */
   void addPlayListRecommendShowEvent(int type, @NotNull String callBack);

   /**
    * 推荐点击
    *
    * @param type
    * @param callBack 推荐服务端透传的数据
    */
   void addRecommendSelectEvent(@NotNull String type, @NotNull String callBack);

   /**
    * 添加激活结果上报.
    */
   void tingBanToKRadioUpdate();

   /**
    * 内容点击
    */
   void addContentClickEvent(@NotNull String audioId, @NotNull String contentType, @NotNull String audioType, @NotNull String radioId, @NotNull String tag, @NotNull String pageId, @NotNull String remarks1, @NotNull String remarks2);

   /**
    * 内容点击
    */
   void addComponentShowAndClickEvent(@NotNull String audioId, boolean mold, @NotNull String cardid, int action, @NotNull String column_code, @NotNull String memberorder, @NotNull String placeorder, @NotNull String radioId, @NotNull String tag, @NotNull String pageId, @NotNull String paytype);

   /**
    * 页面停留时长事件上报
    */
   void addPageShowEvent(long startTime, @NotNull String pageId);

   /**
    * 内容曝光
    */
   void addContentShowEvent(@NotNull String audioId, @NotNull String contentType, @NotNull String audioType, @NotNull String radioId, @NotNull String tag, @NotNull String pageId, @NotNull String remarks1, @NotNull String remarks2);
}
