package com.kaolafm;

import android.app.Activity;
import android.content.Context;
import android.util.Log;

import androidx.multidex.MultiDex;

import com.kaolafm.kradio.KModuleKaolafmAppInitItemContainer;
import com.kaolafm.kradio.KModuleKaolafmModelInitItemContainer;
import com.kaolafm.kradio.common.base.BaseApplication;
import com.kaolafm.kradio.common.http.vehicle.KlSdkVehicle;
import com.kaolafm.kradio.common.utils.CrashHandler;
import com.kaolafm.kradio.common.utils.DiskUtil;
import com.kaolafm.kradio.common.utils.SignUtil;
import com.kaolafm.kradio.k_kaolafm.BuildConfig;
import com.kaolafm.kradio.lib.base.flavor.KRadioLazyInitInter;
import com.kaolafm.kradio.lib.base.flavor.SignCheckInter;
import com.kaolafm.kradio.lib.exit.AppExitManager;
import com.kaolafm.kradio.lib.init.AppInitManager;
import com.kaolafm.kradio.lib.init.ModelInitManager;
import com.kaolafm.kradio.lib.utils.AppUtil;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.ClientConnectControl;
import com.kaolafm.kradio.lib.utils.ProcessUtil;
import com.kaolafm.kradio.lib.utils.YTDataCache;
import com.kaolafm.kradio.lib.utils.YTLogUtil;
import com.kaolafm.opensdk.OpenSDK;
import com.kaolafm.report.ReportHelper;

/**
 * <AUTHOR> Yan
 * @date 2019-07-10
 */
public class KradioApplication extends BaseApplication {

    private static final String TAG = "KradioApplication";

    @Override
    protected void attachBaseContext(Context base) {
        YTLogUtil.logStart(TAG, "attachBaseContext", "start");
        super.attachBaseContext(base);
        MultiDex.install(base);
    }

    @Override
    public void onCreate() {
        super.onCreate();
        YTLogUtil.logStart(TAG, "onCreate", "start");
        if (!ProcessUtil.isMainProcess(this)) {
            return;
        }
        YTDataCache.clearCache();
        OpenSDK.getInstance().preInit(this);
        if (AppInitManager.isTaskListEmpty()) {
            AppInitManager.registerInitializers(new KModuleKaolafmAppInitItemContainer());
        }
        if (ModelInitManager.isTaskListEmpty()) {
            ModelInitManager.registerInitializers(new KModuleKaolafmModelInitItemContainer());
        }

        //签名校验
        SignCheckInter signCheckInterImpl = ClazzImplUtil.getInter("SignCheckInterImpl");
        // 由于各渠道可能会有各自不同的签名，某些车厂会对应用包做重签名，此处由渠道控制是否要使用签名合法性校验并退出应用
        boolean needSignCheck = signCheckInterImpl != null && signCheckInterImpl.needCheck();
        if (needSignCheck && !SignUtil.isValidSignature(this)) {
            android.os.Process.killProcess(android.os.Process.myPid());
        }

        //空间不足判断
        boolean hasSpace = DiskUtil.checkFreeSpace(this);
        YTLogUtil.logStart(TAG, "onCreate", "hasSpace = " + hasSpace);
        Log.i(TAG, "onCreate hasSpace =: " + hasSpace);
        if (hasSpace) {
            AppInitManager.onCreate(this);
        } else {
            AppInitManager.onCreate(this);
        }
        ReportHelper.getInstance().setProductId(BuildConfig.PRODUCT_ID + "");
        ReportHelper.getInstance().setAppMode(BuildConfig.APP_MODE + "");
        ReportHelper.getInstance().setCarType(KlSdkVehicle.getInstance().getCarType());
        CrashHandler crashHandler = CrashHandler.getInstance();
        crashHandler.init(this);

        if (AppUtil.isMainProcess(this) && ClientConnectControl.instance.isProtocolReceived()) {
            KRadioLazyInitInter inter = ClazzImplUtil.getInter("KRadioLazyInitImpl");
            if (inter != null) {
                inter.afterPrivacyEnable();
            }
        }
        YTLogUtil.logStart(TAG, "onCreate", "end");
    }

    @Override
    public void onTerminate() {
        super.onTerminate();
        AppInitManager.onTerminate();
    }

    @Override
    public void onLowMemory() {
        super.onLowMemory();
    }

    @Override
    public void onTrimMemory(int level) {
        super.onTrimMemory(level);
    }


    @Override
    public void exitApplication() {
        super.exitApplication();
        AppExitManager.onExit(this);
    }

    @Override
    public void addActivity(Activity activity) {
        super.addActivity(activity);
    }

    @Override
    public void removeActivity(Activity activity) {
        super.removeActivity(activity);
    }
}
