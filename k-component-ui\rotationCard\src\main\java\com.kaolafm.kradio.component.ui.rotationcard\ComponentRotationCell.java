package com.kaolafm.kradio.component.ui.rotationcard;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Rect;
import android.os.Handler;
import android.os.Message;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.PagerSnapHelper;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.kradio.component.ui.R;
import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.component.ui.base.utils.ComponentUtils;
import com.kaolafm.kradio.component.ui.base.view.OvalImageView;
import com.kaolafm.kradio.component.ui.base.view.RateView;
import com.kaolafm.kradio.component.ui.base.CellBinder;
import com.kaolafm.kradio.component.ui.base.ItemClickSupport;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.utils.DateUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.UrlUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;

import java.lang.ref.WeakReference;

/**
 * 首页组件轮播
 */
public class ComponentRotationCell extends HomeCell implements CellBinder<View, ComponentRotationCell>, ItemClickSupport {

    RecyclerView card_view_page;
    RecyclerView indicator_rv;

    private ComponentRotationCell rotationCell;
    private int selectIndex;//当前选中的page
    // CPU优化：禁用轮播功能以降低CPU使用率
    private static final boolean CAROUSEL_ENABLED = false;
    private int delayMillis = 5 * 1000;//轮播时长
    private IndicatorAdapter indicatorAdapter;
    private ComponentRotationAdapter componentRotationAdapter;
    private int playIndex = -1;//当前播放的索引

    // CPU优化：使用WeakReference Handler避免内存泄漏
    private CarouselHandler mHandler = new CarouselHandler(this);

    /**
     * 使用弱引用Handler避免内存泄漏
     */
    private static class CarouselHandler extends Handler {
        private final WeakReference<ComponentRotationCell> mCellRef;

        CarouselHandler(ComponentRotationCell cell) {
            mCellRef = new WeakReference<>(cell);
        }

        @Override
        public void handleMessage(Message msg) {
            ComponentRotationCell cell = mCellRef.get();
            if (cell == null) return;
            cell.handleCarouselMessage(msg);
        }
    }

    /**
     * 处理轮播消息 - CPU优化：已禁用轮播功能
     */
    private void handleCarouselMessage(Message msg) {
        // CPU优化：轮播功能已禁用，直接返回
        if (!CAROUSEL_ENABLED) {
            return;
        }
        if (rotationCell != null && rotationCell.getContentList() != null) {
            selectIndex = (selectIndex + 1) % rotationCell.getContentList().size();
            // CPU优化：使用scrollToPosition替代smoothScrollToPosition
            if (card_view_page != null) {
                card_view_page.scrollToPosition(selectIndex);
            }
            Log.d("ComponentRotationCell", "--轮播切换--" + selectIndex);
            mHandler.sendEmptyMessageDelayed(1010, delayMillis);
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public void mountView(@NonNull ComponentRotationCell data, @NonNull View view, int position) {
        rotationCell = data;

        card_view_page = null;
        componentRotationAdapter = null;
        indicator_rv = null;
        indicatorAdapter = null;
        card_view_page = view.findViewById(R.id.card_view_page);
        indicator_rv = view.findViewById(R.id.indicator_rv);
        int mTouchSlop = ViewConfiguration.get(card_view_page.getContext()).getScaledTouchSlop();
        card_view_page.setOnTouchListener(new View.OnTouchListener() {
            float x = 0;
            float downX = 0;
            boolean isRequestDisallowInterceptTouchEvent = false;

            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (rotationCell.getContentList().size() > 0) {
                    switch (event.getAction()) {
                        case MotionEvent.ACTION_DOWN:
                            downX = event.getX();
                            break;
                        case MotionEvent.ACTION_UP://抬起
//                            if (!rotationCell.selected) {
                            if (Math.abs(downX - event.getX()) < mTouchSlop) {
                                if (onViewClickListener != null) {
                                    Log.d("ComponentRotationCell", "--点击--");
                                    v.setTag(selectIndex);
                                    onViewClickListener.onViewClick(v, position);
                                }
                            }
                            // CPU优化：轮播功能已禁用，不再启动自动轮播
                            if (CAROUSEL_ENABLED && rotationCell.getContentList().size() > 1 && !mHandler.hasMessages(1010)) {
                                mHandler.sendEmptyMessageDelayed(1010, delayMillis);
                                Log.d("ComponentRotationCell", "--抬起，恢复自动轮播--");
                            }
//                            }
                            break;
                        case MotionEvent.ACTION_MOVE://移动
                            if (x - event.getX() > mTouchSlop && !card_view_page.canScrollHorizontally(1) || event.getX() - x > mTouchSlop && !card_view_page.canScrollHorizontally(-1) && isRequestDisallowInterceptTouchEvent) {
                                isRequestDisallowInterceptTouchEvent = false;
                                v.getParent().requestDisallowInterceptTouchEvent(isRequestDisallowInterceptTouchEvent);
                            } else if (!isRequestDisallowInterceptTouchEvent) {
                                isRequestDisallowInterceptTouchEvent = true;
                                v.getParent().requestDisallowInterceptTouchEvent(isRequestDisallowInterceptTouchEvent);
                            }
                            x = event.getX();
                            if (mHandler.hasMessages(1010)) {
                                mHandler.removeMessages(1010);
                                Log.d("ComponentRotationCell", "--移动，停止自动轮播--");
                            }
                            break;
                    }
                } else {
                    v.getParent().requestDisallowInterceptTouchEvent(false);
                }
                return false;
            }
        });
        card_view_page.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Log.d("ComponentRotationCell", "%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%");
            }
        });
        initView();
    }

    @Override
    public int getItemType() {
        return R.layout.component_rotation_layout;
    }

    private BaseHolder.OnViewClickListener onViewClickListener;

    @Override
    public void setOnItemClickListener(BaseHolder.OnViewClickListener listener) {
        onViewClickListener = listener;
//        componentRotationAdapter.setOnItemClickListener(new IViewPageChildViewClick() {
//            @Override
//            public void onViewPageChildViewClick(View view, int position) {
//                if (onViewClickListener != null) {
//                    onViewClickListener.onViewClick(view, position);
//                }
//            }
//        });
    }

    /**
     * 轮播内的点击回调
     */
    public interface IViewPageChildViewClick {
        void onViewPageChildViewClick(View view, int position);
    }

    private void initView() {
        if (indicatorAdapter == null) {
            indicator_rv.setLayoutManager(new LinearLayoutManager(indicator_rv.getContext(), LinearLayoutManager.HORIZONTAL, false));
            if (indicator_rv.getItemDecorationCount() <= 0) {
                indicator_rv.addItemDecoration(new RecyclerView.ItemDecoration() {
                    @Override
                    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                        super.getItemOffsets(outRect, view, parent, state);
                        int childAdapterPosition = parent.getChildAdapterPosition(view);
                        outRect.left = ResUtil.getDimen(R.dimen.m10);
                        outRect.right = ResUtil.getDimen(R.dimen.m10);
                    }
                });
            }
            indicatorAdapter = new IndicatorAdapter(indicator_rv.getContext());
            indicator_rv.setAdapter(indicatorAdapter);
        }
        if (componentRotationAdapter == null) {
            card_view_page.setLayoutManager(new LinearLayoutManager(indicator_rv.getContext(), LinearLayoutManager.HORIZONTAL, false));
//            card_view_page.addItemDecoration(new RecyclerView.ItemDecoration() {
//                @Override
//                public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
//                    super.getItemOffsets(outRect, view, parent, state);
//                int childAdapterPosition = parent.getChildAdapterPosition(view);
//                outRect.left = ResUtil.getDimen(R.dimen.m10);
//                outRect.right = ResUtil.getDimen(R.dimen.m10);
//                }
//            });
            componentRotationAdapter = new ComponentRotationAdapter(card_view_page.getContext());
            componentRotationAdapter.setIndex(getPositionInParent());
            componentRotationAdapter.setDate(rotationCell);
            card_view_page.setAdapter(componentRotationAdapter);
            if (card_view_page.getOnFlingListener() == null) {
                PagerSnapHelper snapHelper = new PagerSnapHelper();
                snapHelper.attachToRecyclerView(card_view_page);
            }
            card_view_page.addOnScrollListener(new RecyclerView.OnScrollListener() {
                @Override
                public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
                    super.onScrollStateChanged(recyclerView, newState);
                    RecyclerView.LayoutManager layoutManager = recyclerView.getLayoutManager();
                    if (newState == RecyclerView.SCROLL_STATE_IDLE && layoutManager instanceof LinearLayoutManager) {
                        int firs = ((LinearLayoutManager) layoutManager).findFirstVisibleItemPosition();
                        Log.d("ComponentRotationCell", "=== firs=" + firs + ", selectIndex=" + selectIndex);
                        selectIndex = firs;
                        indicatorAdapter.notifyDataSetChanged();
//                        if (onpagerChageListener != null)
//                            onpagerChageListener.onPagerChage(position);
                    }
                }
            });
            if (selectIndex > 0)
                card_view_page.scrollToPosition(selectIndex);
        }

//        if (rotationCell.getContentList().size() > 1 && !mHandler.hasMessages(1010)) {
//            mHandler.sendEmptyMessageDelayed(1010, delayMillis);
//        }


//        if (rotationCell.selected) {
//            if (mHandler.hasMessages(1010)) {
//                mHandler.removeMessages(1010);
//            }
//            card_view_page.postDelayed(new Runnable() {
//                @Override
//                public void run() {
//                    if (playIndex >= 0) {
//                        card_view_page.smoothScrollToPosition(playIndex);
//                    }
//                }
//            }, 500);
//        } else {
//            playIndex = -1;
        if (rotationCell.getContentList().size() > 1 && !mHandler.hasMessages(1010)) {
            mHandler.sendEmptyMessageDelayed(1010, delayMillis);
        }
//        }

    }

    private class IndicatorAdapter extends RecyclerView.Adapter<IndicatorAdapter.ViewHolder> {
        Context context;

        public IndicatorAdapter(Context context) {
            this.context = context;
        }

        @Override
        public IndicatorAdapter.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(context).inflate(R.layout.home_indicator_item, parent, false);
            return new ViewHolder(view);
        }

        @Override
        public void onBindViewHolder(IndicatorAdapter.ViewHolder holder, int position) {
            if (position == selectIndex) {
                holder.imageView.setImageDrawable(context.getResources().getDrawable(R.drawable.indicator_select_shape));
            } else {
                holder.imageView.setImageDrawable(context.getResources().getDrawable(R.drawable.indicator_not_select_shape));
            }
        }

        @Override
        public int getItemCount() {
            return rotationCell.getContentList().size();
        }

        public class ViewHolder extends RecyclerView.ViewHolder {
            ImageView imageView;

            public ViewHolder(View itemView) {
                super(itemView);
                imageView = itemView.findViewById(R.id.iv);
            }

        }
    }

    public class ComponentRotationAdapter extends RecyclerView.Adapter<ComponentRotationAdapter.ViewHolder> {

        private Context context;
        private ComponentRotationCell cell;
        private int positionSuper;//父级的索引

        public ComponentRotationAdapter(Context context) {
            this.context = context;
        }

        /**
         * 当前page的索引
         *
         * @param position
         */
        public void setIndex(int position) {
            this.positionSuper = position;
        }

        /**
         * 成员数据
         *
         * @param componentRotationCell
         */
        public void setDate(ComponentRotationCell componentRotationCell) {
            this.cell = componentRotationCell;

        }

        @Override
        public ComponentRotationAdapter.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
            return new ComponentRotationAdapter.ViewHolder(LayoutInflater.from(context).inflate(R.layout.fragment_cell_rotation, parent, false));
        }

        @Override
        public void onBindViewHolder(ComponentRotationAdapter.ViewHolder holder, @SuppressLint("RecyclerView") int position) {

            holder.rotation_title_tv.setText(cell.getContentList().get(position).getTitle() + "");
            String bgUrl = UrlUtil.getCardBgUrl(cell.getContentList().get(position).getImageFiles());
            if (cell.getContentList().get(position).getCanPlay() == 1) {
                holder.card_play_iv.setVisibility(View.VISIBLE);
            } else {
                holder.card_play_iv.setVisibility(View.GONE);
            }
            if (!TextUtils.isEmpty(bgUrl)) {
                ImageLoader.getInstance().displayImage(context, bgUrl, holder.card_bg_iv);
            }else {
                holder.card_bg_iv.setBackground(ResUtil.getDrawable(R.drawable.component_card_bg));
            }
            holder.rotationAdapter = new ComponentRotationAdapter.RotationAdapter();
            holder.rotationAdapter.setPositionSuper(position);
            holder.rotation_rv.setClickable(false);
            holder.rotation_rv.setLayoutManager(new LinearLayoutManager(context));
            if (holder.rotation_rv.getItemDecorationCount() == 0) {
                holder.rotation_rv.addItemDecoration(new RecyclerView.ItemDecoration() {
                    @Override
                    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                        super.getItemOffsets(outRect, view, parent, state);
                        int childAdapterPosition = parent.getChildAdapterPosition(view);
                        if (childAdapterPosition == 0) {
                            outRect.top = ResUtil.getDimen(R.dimen.m16);
                        } else {
                            outRect.top = ResUtil.getDimen(R.dimen.m10);
                        }

                    }
                });
            }
            holder.rotation_rv.setAdapter(holder.rotationAdapter);
//            holder.item_click_viwe.setOnClickListener(new View.OnClickListener() {
//                @Override
//                public void onClick(View v) {
//                    if (onViewClickListener != null) {
//                        v.setTag(position);
//                        onViewClickListener.onViewPageChildViewClick(v, positionSuper);
//                        mHandler.removeMessages(1010);
//                        selectIndex = position;
////                        mHandler.sendEmptyMessageDelayed(1010, delayMillis);
////                        playIndex = position;
//                    }
//                }
//            });

            boolean select = cell.selected && ComponentUtils.getInstance()
                    .getCardHomePlayIdIsPlaying(cell.getContentList().get(position).getId()
                            , cell.getContentList().get(position).getCanPlay());
            holder.card_layout_playing.setVisibility(select ?
                    View.VISIBLE : View.GONE);
            holder.card_play_iv.setVisibility(!select ? View.VISIBLE : View.GONE);
            if (select) {
                playIndex = position;
            }
        }

        private ComponentRotationCell.IViewPageChildViewClick onViewClickListener;

        public void setOnItemClickListener(ComponentRotationCell.IViewPageChildViewClick listener) {
            onViewClickListener = listener;
        }

        @Override
        public int getItemCount() {
            return cell.getContentList().size();
        }

        public class ViewHolder extends RecyclerView.ViewHolder {

            TextView rotation_title_tv;
            RecyclerView rotation_rv;
            OvalImageView card_bg_iv;
            ImageView card_play_iv;
            RateView card_layout_playing;

            private ComponentRotationAdapter.RotationAdapter rotationAdapter;

            public ViewHolder(View itemView) {
                super(itemView);
                rotation_title_tv=itemView.findViewById(R.id.rotation_title_tv);
                rotation_rv=itemView.findViewById(R.id.rotation_rv);
                card_bg_iv=itemView.findViewById(R.id.card_bg_iv);
                card_play_iv=itemView.findViewById(R.id.card_play_iv);
                card_layout_playing=itemView.findViewById(R.id.card_layout_playing);

            }
        }


        public class RotationAdapter extends RecyclerView.Adapter<ComponentRotationAdapter.RotationAdapter.ViewHolder> {
            TextView item_title_tv;
            TextView item_time_tv;
            private int positionSuper;//父级的索引

            @Override
            public ComponentRotationAdapter.RotationAdapter.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
                return new ComponentRotationAdapter.RotationAdapter
                        .ViewHolder(LayoutInflater.from(context).inflate(R.layout.rotation_list_item, parent, false));
            }

            public void setPositionSuper(int positionSuper) {
                this.positionSuper = positionSuper;
            }

            @Override
            public void onBindViewHolder(ComponentRotationAdapter.RotationAdapter.ViewHolder holder, int position) {
                item_title_tv.setText(cell.getContentList().get(positionSuper).getColumnMemberChildContents().get(position).getTitle());
                long time = cell.getContentList().get(positionSuper).getColumnMemberChildContents().get(position).getUpdateDate();
                item_time_tv.setText(DateUtil.getDisTimeStr(time));
                if (position == 2) {
                    item_title_tv.setMaxLines(1);
                } else {
                    item_title_tv.setMaxLines(2);
                }
                holder.itemView.setContentDescription(item_title_tv.getText());
                holder.itemView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {

                    }
                });
            }

            @Override
            public int getItemCount() {
                return Math.min(cell.getContentList().get(positionSuper).getColumnMemberChildContents().size(), 3);
            }

            public class ViewHolder extends RecyclerView.ViewHolder {
                public ViewHolder(View itemView) {
                    super(itemView);
                    item_title_tv = itemView.findViewById(R.id.item_title_tv);
                    item_time_tv = itemView.findViewById(R.id.item_time_tv);
                }
            }
        }
    }

    @Override
    public void release() {
        super.release();
        // CPU优化：清理Handler消息，防止内存泄漏
        if (mHandler != null) {
            mHandler.removeCallbacksAndMessages(null);
        }
    }
}
