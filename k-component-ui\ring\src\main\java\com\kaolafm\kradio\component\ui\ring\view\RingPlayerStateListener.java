package com.kaolafm.kradio.component.ui.ring.view;

import android.annotation.SuppressLint;
import android.util.Log;

import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import java.lang.ref.WeakReference;

/**
 * RingView播单状态监听。
 *
 * <AUTHOR>
 * @date 2022-07-25
 */
public class RingPlayerStateListener extends BasePlayStateListener {

    private final String TAG = "Ring.StateListener";

    private WeakReference<Ring> weakReference;

    public RingPlayerStateListener(Ring ring) {
        weakReference = new WeakReference<>(ring);
    }

    @SuppressLint("LongLogTag")
    @Override
    public void onIdle(PlayItem playItem) {
        super.onIdle(playItem);
        Log.i(TAG, "onIdle start");
    }

    @SuppressLint("LongLogTag")
    @Override
    public void onPlayerPreparing(PlayItem playItem) {
        super.onPlayerPreparing(playItem);
        Log.i(TAG, "onPlayerPreparing start");
        Ring ring = weakReference.get();
        if (ring != null) {
            ring.notifyDataSetChanged();
        } else {
            Log.i(TAG, "onPlayerPreparing ringView is null");
        }
    }

    @SuppressLint("LongLogTag")
    @Override
    public void onPlayerPlaying(PlayItem playItem) {
        super.onPlayerPlaying(playItem);
        Log.i(TAG, "onPlayerPlaying start");
        Ring ring = weakReference.get();
        if (ring != null) {
            ring.playAnim();
        } else {
            Log.i(TAG, "onPlayerPlaying ringView is null");
        }
    }

    @SuppressLint("LongLogTag")
    @Override
    public void onPlayerPaused(PlayItem playItem) {
        Log.i(TAG, "onPlayerPaused start");
        Ring ring = weakReference.get();
        if (ring != null) {
            ring.pauseAnim();
        } else {
            Log.i(TAG, "onPlayerPaused ringView is null");
        }
    }

    @SuppressLint("LongLogTag")
    @Override
    public void onPlayerFailed(PlayItem playItem, int i, int i1) {
        super.onPlayerFailed(playItem, i, i1);
        Log.i(TAG, "onPlayerFailed: error = " + i + " , " + i1);
//        ToastUtil.showOnly(AppDelegate.getInstance().getContext(), R.string.player_failed);
        Ring ring = weakReference.get();
        if (ring != null) {
            ring.pauseAnim();
        } else {
            Log.i(TAG, "onPlayerFailed ringView is null");
        }
    }

    @SuppressLint("LongLogTag")
    @Override
    public void onPlayerEnd(PlayItem playItem) {
        super.onPlayerEnd(playItem);
        Log.i(TAG, "onPlayerEnd start");
        Ring ring = weakReference.get();
        if (ring != null) {
            ring.pauseAnim();
        } else {
            Log.i(TAG, "onPlayerEnd ringView is null");
        }
    }
}
