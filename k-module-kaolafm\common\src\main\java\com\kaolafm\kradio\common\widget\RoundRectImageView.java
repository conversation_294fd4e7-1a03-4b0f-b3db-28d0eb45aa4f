package com.kaolafm.kradio.common.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Path;
import android.graphics.Path.Direction;
import android.graphics.RectF;
import android.util.AttributeSet;

import androidx.appcompat.widget.AppCompatImageView;

import com.kaolafm.kradio.k_kaolafm.R.styleable;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.HashMap;


public final class RoundRectImageView extends AppCompatImageView {
   private float mRoundRectRadius;
   private Path mRoundRectPath;
   private RectF mRect;
   private float[] mRadiusRect;
   private HashMap _$_findViewCache;

   public RoundRectImageView(@NotNull Context context) {
      this(context, (AttributeSet)null);
   }

   public RoundRectImageView(@NotNull Context context, @Nullable AttributeSet attrs) {
      this(context, attrs, 0);
   }

   public RoundRectImageView(@NotNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
      super(context, attrs, defStyleAttr);
      TypedArray ta = context.obtainStyledAttributes(attrs, styleable.RoundRectImageView);
      this.mRoundRectRadius = (float)ta.getDimensionPixelSize(styleable.RoundRectImageView_rriv_radius, 200);
      ta.recycle();
      this.mRoundRectPath = new Path();
      this.mRect = new RectF();
      this.mRadiusRect = new float[8];
   }

   @Override
   protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
      super.onMeasure(widthMeasureSpec, heightMeasureSpec);
      RectF var10000 = this.mRect;
      var10000.set(0.0F, 0.0F, (float)this.getMeasuredWidth(), (float)this.getMeasuredHeight());
      this.mRadiusRect = new float[]{this.mRoundRectRadius, this.mRoundRectRadius, this.mRoundRectRadius, this.mRoundRectRadius, this.mRoundRectRadius, this.mRoundRectRadius, this.mRoundRectRadius, this.mRoundRectRadius};
      Path var3 = this.mRoundRectPath;
      RectF var10001 = this.mRect;
      float[] var10002 = this.mRadiusRect;
      var3.addRoundRect(var10001, var10002, Direction.CW);
   }

   @Override
   protected void onDraw(@Nullable Canvas canvas) {
      if (canvas != null) {
         Path var10001 = this.mRoundRectPath;
         canvas.clipPath(var10001);
      }

      super.onDraw(canvas);
   }
}
