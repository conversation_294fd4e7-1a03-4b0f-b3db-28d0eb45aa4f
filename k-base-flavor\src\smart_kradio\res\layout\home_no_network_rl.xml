<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/home_no_network_rel"
    android:background="@null">

    <ImageView
        android:id="@+id/network_nosigin"
        android:layout_width="@dimen/m500"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:scaleType="center"
        android:layout_centerInParent="true"
        android:src="@drawable/network_loss" />

    <TextView
        android:id="@+id/tv_network_nosign"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/network_nosigin"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/m25"
        android:textSize="@dimen/text_size_title4"
        android:text="@string/network_nosigin"
        android:textColor="@color/text_color_3" />

</RelativeLayout>