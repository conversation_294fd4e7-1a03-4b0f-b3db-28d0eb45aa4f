package com.kaolafm.kradio.common.comprehensive.web;

import android.animation.ValueAnimator;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.net.http.SslError;
import android.os.Build;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.SslErrorHandler;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ImageView;
import android.widget.TextView;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.kaolafm.kradio.common.base.BaseSkinAppCompatActivity;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.utils.CommonUtils;
import com.kaolafm.kradio.component.ui.base.skin.SkinHelper;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.flavor.UserCenterInter;
import com.kaolafm.kradio.lib.base.mvp.IPresenter;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.PageShowReportEvent;

import static com.kaolafm.kradio.lib.utils.Constants.ROUTER_PARAMS_KEY_WEB_PAGE_ID;
import static com.kaolafm.kradio.lib.utils.Constants.ROUTER_PARAMS_KEY_WEB_TITLE;
import static com.kaolafm.kradio.lib.utils.Constants.ROUTER_PARAMS_KEY_WEB_URL;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

@Route(path = RouterConstance.WEBVIEW_COMPREHENSIVE_URL)
public class WebViewActivity extends BaseSkinAppCompatActivity {

    private static final String TAG = "WebViewActivity";

    private WebView mWebView;
    private TextView bbf_center_title;
    private View line;
    //    private ImageView mCloseView;
    private ConstraintLayout rootLayout;
    private long startTime = -1;
    private String pageId = "";
    private ImageView back_webview;
    private View bottomView;
    private String url;
    private static final String THEME_DARK = "theme=dark";
    private static final String THEME_LIGHT = "theme=light";
    private boolean isFirstChange;

    public static void start(Context context, String url) {
        Intent intent = new Intent(context, WebViewActivity.class);
        intent.putExtra(ROUTER_PARAMS_KEY_WEB_URL, url);
        context.startActivity(intent);
    }

    public static void start(Context context, String url, String title, String pageId) {
        Intent intent = new Intent(context, WebViewActivity.class);
        intent.putExtra(ROUTER_PARAMS_KEY_WEB_URL, url);
        intent.putExtra(ROUTER_PARAMS_KEY_WEB_TITLE, title);
        intent.putExtra(ROUTER_PARAMS_KEY_WEB_PAGE_ID, pageId);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        overridePendingTransition(0, 0);
        super.onCreate(savedInstanceState);
        CommonUtils.getInstance().initGreyStyle(getWindow());
    }

    @Override
    public int getLayoutId() {
        return R.layout.comprehensive_activity_webview;
    }

    @Override
    public int getLayoutId_Tow() {
        return 0;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        url = getIntent().getStringExtra(ROUTER_PARAMS_KEY_WEB_URL);
        String title = getIntent().getStringExtra(ROUTER_PARAMS_KEY_WEB_TITLE);
        pageId = getIntent().getStringExtra(ROUTER_PARAMS_KEY_WEB_PAGE_ID);
        if (!TextUtils.isEmpty(pageId)) {
            ReportHelper.getInstance().setPage(pageId);
        }
        if (title == null) {
            title = "";
        }
        startTime = System.currentTimeMillis();

        Log.i("WebViewActivity", "url:" + url);

        mWebView = findViewById(R.id.webview);
        CommonUtils.getInstance().initGreyStyle(mWebView);
        bbf_center_title = findViewById(R.id.bbf_center_title);
        line = findViewById(R.id.line);
        rootLayout = findViewById(R.id.rootLayout);
        back_webview = findViewById(R.id.back_webview);
        bottomView = findViewById(R.id.view);
        bbf_center_title.setText(title);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            mWebView.setVerticalScrollbarThumbDrawable(ResUtil.getDrawable(R.drawable.sl_sh_sb_thumb));
            mWebView.setVerticalScrollbarTrackDrawable(ResUtil.getDrawable(R.drawable.sl_sh_sb_track));
        }
        if (!TextUtils.isEmpty(title)) {
            line.setVisibility(View.VISIBLE);
            ConstraintSet constraintSet = new ConstraintSet();
            constraintSet.clone(rootLayout);
            constraintSet.clear(mWebView.getId(), ConstraintSet.TOP);
            constraintSet.connect(mWebView.getId(), ConstraintSet.TOP, line.getId(), ConstraintSet.BOTTOM);
            constraintSet.applyTo(rootLayout);
        }
        mWebView.getSettings().setSavePassword(false);
        if (Build.VERSION.SDK_INT < 19 && mWebView != null) {
            mWebView.removeJavascriptInterface("searchBoxJavaBridge_");
            mWebView.removeJavascriptInterface("accessibility");
            mWebView.removeJavascriptInterface("accessibilityTraversal");
        }

//        mCloseView = findViewById(R.id.webview_close);
//        mCloseView.setOnClickListener(view1 -> {
//            finish();
//        });

        if (TextUtils.isEmpty(url)) {
            return;
        }

//        setCloseViewDrawable();
        if (back_webview instanceof ImageView) {
            ((ImageView) back_webview).setImageDrawable(ResUtil.getDrawable(R.drawable.player_ic_back));
        }
        back_webview.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (AntiShake.check(v.getId())) {
                    return;
                }
                if (bbf_center_title != null) {
                    bbf_center_title.setVisibility(View.GONE);
                }
                finish();
                overridePendingTransition(0, 0);
            }
        });

        // 第一次打开的时候适配下黑白模式判断 - 使用新版Settings方式
        updateThemeFromSettings();

        // 验证并修正URL中的主题参数
        url = validateAndFixUrlTheme(url);

        setWebViewSetting();

        mWebView.loadUrl(url);

        // 所见即可说
        TextView upScroll = findViewById(R.id.cd_up);
        TextView downScroll = findViewById(R.id.cd_down);
        upScroll.setOnClickListener((v) -> {
            scrollWebView(1);
        });
        downScroll.setOnClickListener((v) -> {
            scrollWebView(-1);
        });
    }


    private void setThemeByIsDay(boolean isDayModel) {
        // 背景
        rootLayout.setBackgroundResource(isDayModel ? R.drawable.bg_home_day : R.drawable.bg_home);
        // 分割线
        line.setBackgroundColor(ResUtil.getColor(isDayModel ? R.color.web_view_line_color_day : R.color.web_view_line_color));
        // 标题
        bbf_center_title.setTextColor(ResUtil.getColor(isDayModel ? R.color.global_title_text_color_day : R.color.global_title_text_color));
        // 返回键
        back_webview.setImageResource(isDayModel ? R.drawable.player_ic_back_day : R.drawable.player_ic_back);
        // 底部View
        bottomView.setBackgroundResource(isDayModel ? R.drawable.app_bottom_bg_day : R.drawable.app_bottom_bg);
    }

    /**
     * 验证并修正URL中的主题参数
     */
    private String validateAndFixUrlTheme(String originalUrl) {
        if (TextUtils.isEmpty(originalUrl)) {
            return originalUrl;
        }

        String settingsTheme = getCurrentThemeFromSettings();
        boolean isDayMode = !"theme.night".equals(settingsTheme);

        if (isDayMode && originalUrl.contains(THEME_DARK)) {
            return originalUrl.replace(THEME_DARK, THEME_LIGHT);
        } else if (!isDayMode && originalUrl.contains(THEME_LIGHT)) {
            return originalUrl.replace(THEME_LIGHT, THEME_DARK);
        }

        return originalUrl;
    }

    /**
     * 从Settings读取当前主题状态并更新UI
     */
    private void updateThemeFromSettings() {
        String settingsTheme = getCurrentThemeFromSettings();
        setThemeByIsDay(!"theme.night".equals(settingsTheme));
    }

    /**
     * 监听主题变化事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onThemeChangeEvent(UserCenterInter.ThemeChangeEvent event) {
        Log.d(TAG, "收到主题变化事件: " + event.getTheme());

        String theme = (event == null) ? "" : event.getTheme();
        if (TextUtils.isEmpty(theme)) {
            return;
        }

        // 处理isSameTheme事件：读取当前Settings确认主题状态
        if ("isSameTheme".equals(theme)) {
            Log.d(TAG, "收到isSameTheme事件，读取当前Settings主题状态");
            updateThemeFromSettings();
            return;
        }

        // 根据皮肤框架的主题事件更新UI
        if ("night.skin".equals(theme)) {
            Log.d(TAG, "EventBus: 变为黑夜模式");
            setThemeByIsDay(false);
        } else if ("day.skin".equals(theme)) {
            Log.d(TAG, "EventBus: 变为白天模式");
            setThemeByIsDay(true);
        }

        // 处理WebView URL切换（协议和隐私政策页面的主题切换）
        String curUrl = url;
        if (curUrl != null) {
            if (curUrl.contains(THEME_DARK) && "day.skin".equals(theme)) {
                String targetUrl = curUrl.replaceFirst(THEME_DARK, THEME_LIGHT);
                Log.d(TAG, "WebView URL切换: from " + curUrl + ", to " + targetUrl);
                url = targetUrl;
                setWebViewSetting();
                mWebView.loadUrl(url);
            } else if (curUrl.contains(THEME_LIGHT) && "night.skin".equals(theme)) {
                String targetUrl = curUrl.replaceFirst(THEME_LIGHT, THEME_DARK);
                Log.d(TAG, "WebView URL切换: from " + curUrl + ", to " + targetUrl);
                url = targetUrl;
                setWebViewSetting();
                mWebView.loadUrl(url);
            }
        }
    }



    // direction 1为下移，-1为上移，但产品希望向上滑动时屏幕向下，向下滑动时屏幕向上
    private void scrollWebView(int direction) {
        if (mWebView == null) return;
        if (!mWebView.canScrollVertically(direction)) {
            return;
        }

        int webViewHeight = mWebView.getMeasuredHeight();
        int startY = mWebView.getScrollY();
        int targetY = startY + direction * webViewHeight;
        if (targetY < 0) {
            targetY = 0;
        }
        ValueAnimator animator = ValueAnimator.ofInt(startY, targetY);
        animator.setDuration(300);
        animator.addUpdateListener(animation -> {
            int animatedValue = (int) animation.getAnimatedValue();
            mWebView.scrollTo(0, animatedValue);
        });
        animator.start();
    }

//    private void setCloseViewDrawable() {
//        if (backView instanceof ImageView) {
//            if (SkinHelper.isNightMode()) {
//                ((ImageView) backView).setImageTintList(ResUtil.getc(R.drawable.globle_arrow_normal_white));
//            } else {
//                ((ImageView) backView).setImageDrawable(ResUtil.getDrawable(R.drawable.globle_arrow_normal));
//            }
//        }
//    }

    @Override
    public void initData() {

    }

    @Override
    public void onResume() {
        if (mWebView != null) {
            mWebView.onResume();
        }
        super.onResume();
    }

    @Override
    public void onPause() {
        super.onPause();
        if (mWebView != null) {
            mWebView.onPause();
        }
    }

    @Override
    protected IPresenter createPresenter() {
        return null;
    }

    private void setWebViewSetting() {
        WebSettings settings = mWebView.getSettings();


        settings.setUseWideViewPort(true);

        settings.setJavaScriptEnabled(true);
        settings.setSavePassword(false);
        //自动加载图片
        settings.setLoadsImagesAutomatically(true);
        settings.setTextZoom(100);
        settings.setCacheMode(WebSettings.LOAD_NO_CACHE);//不使用缓存，只从网络获取数据.
        mWebView.setBackgroundColor(Color.TRANSPARENT);
//        mWebView.getBackground().setAlpha(1);

        int screenDensity = getResources().getDisplayMetrics().densityDpi;
        WebSettings.ZoomDensity zoomDensity = WebSettings.ZoomDensity.MEDIUM;
        switch (screenDensity) {
            case DisplayMetrics.DENSITY_LOW:
                zoomDensity = WebSettings.ZoomDensity.CLOSE;
                break;
            case DisplayMetrics.DENSITY_MEDIUM:
                zoomDensity = WebSettings.ZoomDensity.MEDIUM;
                break;
            case DisplayMetrics.DENSITY_HIGH:
                zoomDensity = WebSettings.ZoomDensity.FAR;
                break;
        }
        settings.setDefaultZoom(zoomDensity);


        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            // 5.0以上允许加载http和https混合的页面(5.0以下默认允许，5.0+默认禁止)
            settings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        }

        mWebView.setWebViewClient(new WebViewClient() {

            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                //可处理loading开始
                super.onPageStarted(view, url, favicon);
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                //可处理loading结束
                super.onPageFinished(view, url);
                settings.setJavaScriptEnabled(false);
            }

            @Override
            public void onReceivedSslError(WebView view, SslErrorHandler handler, SslError error) {
                handler.cancel();
            }
        });
    }


    @Override
    protected void onDestroy() {
        startTime = System.currentTimeMillis() - startTime;
//        ReportHelper.getInstance().setPage(pageId);
        PageShowReportEvent event = new PageShowReportEvent();
        event.setPage(pageId);
        event.setPageId(pageId);
        event.setPageTime(String.valueOf(startTime));
        ReportHelper.getInstance().addEvent(event);
        startTime = -1;
        if (mWebView != null) {
            ViewGroup parent = (ViewGroup) mWebView.getParent();
            if (parent != null) {
                parent.removeView(mWebView);
            }

            mWebView.stopLoading();
            // 退出时调用此方法，移除绑定的服务，否则某些特定系统会报错
            mWebView.getSettings().setJavaScriptEnabled(false);
            mWebView.clearHistory();
            mWebView.clearCache(true);
            mWebView.clearView();
            mWebView.removeAllViews();
            mWebView.destroy();

            mWebView = null;
        }
        clearConfiguration();
        super.onDestroy();
    }

//    @Override
//    public String consumeRoute(String pageId, Object extra) {
//        switch (pageId) {
//            case Constants.ONLINE_PAGE_ID_PAY_VIP: //VIP购买
//                return pageId;
//        }
//        return ROUTER_CONSUME_FULLY;
//    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        Log.i("WebViewActivity", "onNewIntent called");
        interceptApplicationJumpEvent(intent);
    }

    @Override
    public boolean useEventBus() {
        return true;
    }



   /* private void updateWebViewThumbDrawable(){
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                String skin = SkinPreference.getInstance().getSkinName();
                boolean isNight = TextUtils.isEmpty(skin) || TextUtils.equals(skin, SkinHelper.NIGHT_SKIN);
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    if (isNight) {
                        mWebView.setVerticalScrollbarThumbDrawable(ResUtil.getDrawable(R.drawable.sl_sh_sb_thumb));
                        mWebView.setVerticalScrollbarTrackDrawable(ResUtil.getDrawable(R.drawable.sl_sh_sb_track));
                    } *//*else {
                        mWebView.setVerticalScrollbarThumbDrawable(ResUtil.getDrawable(R.drawable.sl_sh_sb_thumb_day));
                        mWebView.setVerticalScrollbarTrackDrawable(ResUtil.getDrawable(R.drawable.sl_sh_sb_track_day));
                    }*//*
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }*/

    private void interceptApplicationJumpEvent(Intent intent) {
        String title, url;
        int pageId;
        Log.i("WebViewActivity", "interceptApplicationJumpEvent called");
        if (intent.hasExtra(Constants.ROUTER_PARAMS_KEY_WEB_URL)) {
            url = intent.getStringExtra(Constants.ROUTER_PARAMS_KEY_WEB_URL);
            Log.i("WebViewActivity", "onNewIntent - 接收到URL: " + url);
            if (StringUtil.isNotEmpty(url)) {
                this.url = url; // 更新实例变量，确保状态一致性
                mWebView.loadUrl(url);
                Log.i("WebViewActivity", "onNewIntent - WebView已加载新URL: " + url);
            }
        } else {
            Log.i("WebViewActivity", "onNewIntent - Intent中没有URL参数");
        }
        if (intent.hasExtra(Constants.ROUTER_PARAMS_KEY_WEB_TITLE)) {
            title = intent.getStringExtra(Constants.ROUTER_PARAMS_KEY_WEB_TITLE);
            bbf_center_title.setText(title);
            if (StringUtil.isNotEmpty(title)) {
                line.setVisibility(View.VISIBLE);
                ConstraintSet constraintSet = new ConstraintSet();
                constraintSet.clone(rootLayout);
                constraintSet.clear(mWebView.getId(), ConstraintSet.TOP);
                constraintSet.connect(mWebView.getId(), ConstraintSet.TOP, line.getId(), ConstraintSet.BOTTOM);
                constraintSet.applyTo(rootLayout);
            } else {
                line.setVisibility(View.GONE);
            }
        }

        if (intent.hasExtra(Constants.ROUTER_PARAMS_KEY_WEB_PAGE_ID)) {
            pageId = intent.getIntExtra(Constants.ROUTER_PARAMS_KEY_WEB_PAGE_ID, 0);
            if (pageId != 0) {
                this.pageId = String.valueOf(pageId);
                ReportHelper.getInstance().setPage(String.valueOf(this.pageId));
            } else {
                this.pageId = "";
                ReportHelper.getInstance().setPage(String.valueOf(this.pageId));
            }
        }
    }

    @Override
    public void overridePendingTransition(int enterAnim, int exitAnim) {
        super.overridePendingTransition(0, 0);
    }

    @Override
    public void finish() {
        super.finish();
        overridePendingTransition(0, 0);
    }

}
