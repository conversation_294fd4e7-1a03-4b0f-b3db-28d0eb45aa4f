package com.kaolafm.kradio.player.online.pages;

import android.animation.Animator;
import android.animation.Animator.AnimatorListener;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.PropertyValuesHolder;
import android.animation.TimeInterpolator;
import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.MeasureSpec;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.view.animation.DecelerateInterpolator;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.kradio.common.widget.PlayingIndicator;
import com.kaolafm.kradio.k_kaolafm.R.dimen;
import com.kaolafm.kradio.k_kaolafm.R.drawable;
import com.kaolafm.kradio.k_kaolafm.R.id;
import com.kaolafm.kradio.k_kaolafm.R.layout;
import com.kaolafm.kradio.k_kaolafm.R.string;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.live.player.HomeLiveManager;
import com.kaolafm.kradio.live.player.RecorderStatus;

import org.jetbrains.annotations.NotNull;

import kotlin.TypeCastException;

public final class RecordButtonPopupWindow extends PopupWindow {
   private LayoutInflater mLayoutInflater;
   private final ViewGroup mContentView;
   private final ImageButton mRecordButton;
   private ViewGroup mListenButton;
   private PlayingIndicator mListenButtonAnim;
   private ViewGroup recordTextViewParent;
   private ImageView recordTextIv;
   private TextView recordTextView;
   private ViewGroup mCancelButton;
   private final Runnable mChangeToIdleRunnable;
   @NotNull
   private final View anchor;

   public final void show() {
      this.showAtLocation(this.anchor, 8388691, (this.anchor.getMeasuredWidth() - this.mContentView.getMeasuredWidth()) / 2, ResUtil.getDimen(dimen.y64));
   }

   public final void setOnRecordButtonClickListener(@NotNull OnClickListener listener) {
      this.mRecordButton.setOnClickListener(listener);
   }

   public final void setOnListenButtonClickListener(@NotNull OnClickListener listener) {
      this.mListenButton.setOnClickListener(listener);
   }

   public final void setOnCancelButtonClickListener(@NotNull OnClickListener listener) {
      this.mCancelButton.setOnClickListener(listener);
   }

   public final void setOnSendButtonClickListener(@NotNull OnClickListener listener) {
      this.recordTextViewParent.setOnClickListener(listener);
   }

   public final void notifyStartRecord() {
      this.mRecordButton.setVisibility(View.INVISIBLE);
      this.recordTextIv.setImageResource(drawable.online_player_recording);
      this.recordTextViewParent.setVisibility(View.VISIBLE);
   }

   public final void stopRecordingAnim() {
      this.mRecordButton.setImageResource(drawable.online_player_record_mic);
   }

   public final void updateRecordText(@NotNull String string) {
      this.recordTextView.setText((CharSequence)string);
      if (StringUtil.isEmpty(string)) {
         this.recordTextIv.setImageResource(drawable.online_player_send);
      }

   }

   public final void cancelRecord() {
      this.mRecordButton.setVisibility(View.INVISIBLE);
      this.recordTextViewParent.setVisibility(View.INVISIBLE);
   }

   public final void hideRecordButton() {
      this.mListenButton.setVisibility(View.INVISIBLE);
      this.mCancelButton.setVisibility(View.INVISIBLE);
      this.mRecordButton.setVisibility(View.INVISIBLE);
      this.recordTextViewParent.setVisibility(View.INVISIBLE);
      this.mListenButton.setVisibility(View.INVISIBLE);
      this.mCancelButton.setVisibility(View.INVISIBLE);
   }

   public final void startRecordFinishAnim() {
      int recordWidth = this.mRecordButton.getMeasuredWidth();
      int middle = this.mRecordButton.getLeft() + recordWidth / 2;
      int cancelLeft = this.mCancelButton.getLeft();
      int listenLeft = this.mListenButton.getLeft();
      AnimatorSet animSet = new AnimatorSet();
      animSet.addListener((AnimatorListener)(new AnimatorListener() {
         public void onAnimationStart(@NotNull Animator animation) {
            RecordButtonPopupWindow.this.mCancelButton.setVisibility(View.VISIBLE);
            RecordButtonPopupWindow.this.mListenButton.setVisibility(View.VISIBLE);
         }

         public void onAnimationEnd(@NotNull Animator animation) {
         }

         public void onAnimationCancel(@NotNull Animator animation) {
         }

         public void onAnimationRepeat(@NotNull Animator animation) {
         }
      }));
      int animDuration = 300;
      PropertyValuesHolder pvhr = PropertyValuesHolder.ofFloat("x", new float[]{(float)middle, (float)cancelLeft});
      PropertyValuesHolder pvhl = PropertyValuesHolder.ofFloat("x", new float[]{(float)middle, (float)listenLeft});
      PropertyValuesHolder pvha = PropertyValuesHolder.ofFloat("alpha", new float[]{0.0F, 1.0F});
      ObjectAnimator var10000 = ObjectAnimator.ofPropertyValuesHolder(this.mListenButton, new PropertyValuesHolder[]{pvhl});
      ObjectAnimator oali = var10000;
      oali.setDuration((long)animDuration);
      var10000 = ObjectAnimator.ofPropertyValuesHolder(this.mListenButton, new PropertyValuesHolder[]{pvha});
      ObjectAnimator oala = var10000;
      oala.setDuration((long)animDuration);
      var10000 = ObjectAnimator.ofPropertyValuesHolder(this.mCancelButton, new PropertyValuesHolder[]{pvhr});
      ObjectAnimator oari = var10000;
      oari.setDuration((long)animDuration);
      var10000 = ObjectAnimator.ofPropertyValuesHolder(this.mCancelButton, new PropertyValuesHolder[]{pvha});
      ObjectAnimator oara = var10000;
      oala.setDuration((long)animDuration);
      animSet.playTogether(new Animator[]{(Animator)oali, (Animator)oari, (Animator)oala, (Animator)oara});
      animSet.setInterpolator((TimeInterpolator)(new DecelerateInterpolator()));
      animSet.start();
   }

   public final void updateListenText() {
   }

   public final void notifyCancel() {
      this.mCancelButton.setVisibility(View.INVISIBLE);
      this.mListenButton.setVisibility(View.INVISIBLE);
   }

   public final void showRecordIdle() {
      this.mCancelButton.setVisibility(View.INVISIBLE);
      this.mListenButton.setVisibility(View.INVISIBLE);
      this.mRecordButton.setVisibility(View.VISIBLE);
      this.recordTextViewParent.setVisibility(View.INVISIBLE);
   }

   public final void showRecordUploading() {
      this.mCancelButton.setVisibility(View.INVISIBLE);
      this.mListenButton.setVisibility(View.INVISIBLE);
      this.recordTextView.setText(string.online_player_live_uploading);
   }

   public final void showRecordUploaded() {
      this.mRecordButton.setVisibility(View.VISIBLE);
      this.recordTextViewParent.setVisibility(View.INVISIBLE);
      this.mRecordButton.removeCallbacks(this.mChangeToIdleRunnable);
      this.mRecordButton.postDelayed(this.mChangeToIdleRunnable, 500L);
   }

   public final void showRecordUploadAgainAsFailure() {
      this.mCancelButton.setVisibility(View.VISIBLE);
      this.mListenButton.setVisibility(View.VISIBLE);
      this.recordTextView.setText(string.online_player_live_upload_again);
      this.mRecordButton.setVisibility(View.INVISIBLE);
      this.recordTextViewParent.setVisibility(View.VISIBLE);
   }

   public final void startListenTimer() {
      this.mListenButtonAnim.start();
      this.mListenButtonAnim.setVisibility(View.VISIBLE);
   }

   public final void stopListenTimer() {
      this.mListenButtonAnim.setVisibility(View.INVISIBLE);
      this.mListenButtonAnim.stop();
   }

   @NotNull
   public final View getAnchor() {
      return this.anchor;
   }

   public RecordButtonPopupWindow(@NotNull Context context, @NotNull View anchor) {
      super(context);
      this.anchor = anchor;
      LayoutInflater var10001 = LayoutInflater.from(context);
      this.mLayoutInflater = var10001;
      View var4 = this.mLayoutInflater.inflate(layout.online_player_record_button_popup_window, (ViewGroup)null);
      if (var4 == null) {
         throw new TypeCastException("null cannot be cast to non-null type android.view.ViewGroup");
      } else {
         this.mContentView = (ViewGroup)var4;
         var4 = this.mContentView.findViewById(id.recordIv);
         this.mRecordButton = (ImageButton)var4;
         var4 = this.mContentView.findViewById(id.live_listen_button_layout);
         this.mListenButton = (ViewGroup)var4;
         var4 = this.mContentView.findViewById(id.live_listen_anim_image);
         this.mListenButtonAnim = (PlayingIndicator)var4;
         var4 = this.mContentView.findViewById(id.recordTextViewParent);
         this.recordTextViewParent = (ViewGroup)var4;
         var4 = this.mContentView.findViewById(id.recordTextIv);
         this.recordTextIv = (ImageView)var4;
         var4 = this.mContentView.findViewById(id.recordTextView);
         this.recordTextView = (TextView)var4;
         var4 = this.mContentView.findViewById(id.live_cancel_button_layout);
         this.mCancelButton = (ViewGroup)var4;
         this.setContentView((View)this.mContentView);
         this.setFocusable(false);
         this.setOutsideTouchable(false);
         this.setTouchable(true);
         this.setBackgroundDrawable((Drawable)(new ColorDrawable(0)));
         int makeMeasureSpec = MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED);
         this.mContentView.measure(makeMeasureSpec, makeMeasureSpec);
         this.mChangeToIdleRunnable = new Runnable() {
            @Override
            public void run() {
               HomeLiveManager.getInstance().setRecorderStatus(RecorderStatus.IDLE);
            }
         };
      }
   }
}
