package com.kaolafm.kradio.lib.widget.tab;

import android.animation.TypeEvaluator;
import android.animation.ValueAnimator;
import android.animation.ValueAnimator.AnimatorUpdateListener;
import android.content.Context;
import android.content.res.Configuration;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.os.Build;
import android.os.Bundle;
import android.os.Parcelable;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.animation.Interpolator;
import android.view.animation.LinearInterpolator;
import android.widget.HorizontalScrollView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.transition.Transition;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.lib.R;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.TimerUtil;
import com.kaolafm.kradio.lib.utils.imageloader.GlideApp;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Yan
 * @date 2019-08-20
 */
public class SlidingTabLayout extends HorizontalScrollView implements AnimatorUpdateListener {

    public static final int ANIMATION_DURATION = 1000;

    private OnScrollChangeListener mChangeListener;

    private Context mContext;

    private Drawable mCustomIndicatorDrawable;

    /**
     * 第一个item左侧不设置padding，true表示不设置，默认false设置
     */
    private boolean mFirstNoPadding;

    private int mIndicatorAnimDuration = -1;

    private boolean mIndicatorAnimEnable = false;

    private boolean isFirstDraw = true;

    private List<Tab> mTabList = new ArrayList<>();

    private LinearLayout mTabsContainer;

    private int mCurrentTab = -1;

    private int mLastTab = -1;

    private int mTabCount;

    /**
     * 用于绘制显示器
     */
    private Rect mIndicatorRect = new Rect();

    /**
     * 用于实现滚动居中
     */
    private Rect mTabRect = new Rect();

    private GradientDrawable mIndicatorDrawable = new GradientDrawable();

    private Path mTrianglePath = new Path();

    private static final int STYLE_NORMAL = 0;

    private static final int STYLE_TRIANGLE = 1;

    private static final int STYLE_BLOCK = 2;

    /**
     * 倒三角
     */
    private static final int STYLE_TRIANGLE_INVERTED = 3;

    private static final int STYLE_CUSTOM_DRAWABLE = 4;

    private int mIndicatorStyle = STYLE_CUSTOM_DRAWABLE;

    private float mTabPadding;

    private boolean mTabSpaceEqual;

    private float mTabWidth;

    /**
     * indicator
     */
    private int mIndicatorColor;

    private float mIndicatorHeight;

    private float mIndicatorWidth;

    private float mIndicatorCornerRadius;

    private float mIndicatorMarginLeft;

    private float mIndicatorMarginTop;

    private float mIndicatorMarginRight;

    private float mIndicatorMarginBottom;

    private int mIndicatorGravity;

    private boolean mIndicatorWidthEqualTitle;

    /**
     * title
     */
    private static final int TEXT_BOLD_NONE = 0;

    private static final int TEXT_BOLD_WHEN_SELECT = 1;

    private static final int TEXT_BOLD_BOTH = 2;

    private float mTextSize;

    private float mTextSelectSize;

    private int mTextSelectColor;

    private int mTextUnselectColor;

    private int mTextBold;

    private boolean mTextAllCaps;

    private int mLastScrollX;

    private ValueAnimator mValueAnimator;

    private Indicator mIndicator;

    private Interpolator mInterpolator = new LinearInterpolator();

    public SlidingTabLayout(Context context) {
        this(context, null, 0);
    }

    public SlidingTabLayout(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SlidingTabLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        //设置滚动视图是否可以伸缩其内容以填充视口
        setFillViewport(true);
        //重写onDraw方法,需要调用这个方法来清除flag
        setWillNotDraw(false);
        setClipChildren(false);
        setClipToPadding(false);

        this.mContext = context;
        mTabsContainer = new LinearLayout(context);
        addView(mTabsContainer);

        obtainAttributes(context, attrs);
        getIndicator();

        mValueAnimator = ValueAnimator.ofObject(new PointEvaluator(), mLastP, mCurrentP);
        mValueAnimator.addUpdateListener(this);
    }

    private void getIndicator() {
        switch (mIndicatorStyle) {
            case STYLE_CUSTOM_DRAWABLE:
                mIndicator = new DrawableIndicator();
                break;
            case STYLE_TRIANGLE:
                mIndicator = new TriangleIndicator(false);
                break;
            case STYLE_TRIANGLE_INVERTED:
                mIndicator = new TriangleIndicator(true);
                break;
            case STYLE_BLOCK:
                mIndicator = new BlockIndicator();
                break;
            default:
                mIndicator = new DefaultIndicator();
                break;
        }
    }

    public int getCurrentPosition() {
        return mCurrentTab;
    }

    public Tab getTab(int position) {
        // 添加边界检查，防止 IndexOutOfBoundsException
        if (mTabList == null || mTabList.isEmpty()) {
            Log.w("SlidingTabLayout", "getTab: mTabList is null or empty, position=" + position);
            return null;
        }
        if (position < 0 || position >= mTabList.size()) {
            Log.w("SlidingTabLayout", "getTab: position out of bounds, position=" + position + ", size=" + mTabList.size());
            return null;
        }
        return mTabList.get(position);
    }

    public boolean isLastItem() {
        if (mTabList == null || mTabList.isEmpty()) {
            return false;
        }
        return mCurrentTab == mTabList.size() - 1;
    }

    @Override
    public void onAnimationUpdate(ValueAnimator animation) {
        IndicatorPoint p = (IndicatorPoint) animation.getAnimatedValue();
        mIndicatorRect.left = (int) p.left;
        mIndicatorRect.right = (int) p.right;
        //滑动结束后再重新结束一下，防止指针偏移。用post是因为在部分车机上动画有问题，导致计算left出错，如果还是有问题，可以禁用动画mIndicatorAnimEnable
        if (p.offset == 1.0F) {
            post(() -> {
                calcIndicatorRect();
                scrollToCurrentTab();
                invalidate();
            });
        } else {
            scrollToCurrentTab();
            invalidate();
        }
    }

    private void obtainAttributes(Context context, AttributeSet attrs) {
        TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.SlidingTabLayout);
        mCustomIndicatorDrawable = ta.getDrawable(R.styleable.SlidingTabLayout_tl_indicator_drawable);
        mIndicatorStyle = ta.getInt(R.styleable.SlidingTabLayout_tl_indicator_style, STYLE_CUSTOM_DRAWABLE);
        mIndicatorAnimEnable = ta.getBoolean(R.styleable.SlidingTabLayout_tl_indicator_anim_enable, false);
        mIndicatorColor = ta.getColor(R.styleable.SlidingTabLayout_tl_indicator_color, Color
                .parseColor(mIndicatorStyle == STYLE_BLOCK ? "#4B6A87" : "#ffffff"));
        mIndicatorHeight = ta.getDimension(R.styleable.SlidingTabLayout_tl_indicator_height, getDefaultHeight());
        mIndicatorWidth = ta.getDimension(R.styleable.SlidingTabLayout_tl_indicator_width, getDefaultWidth());
        mIndicatorCornerRadius = ta.getDimension(R.styleable.SlidingTabLayout_tl_indicator_corner_radius,
                dp2px(mIndicatorStyle == STYLE_BLOCK ? -1 : 0));
        mIndicatorMarginLeft = ta.getDimension(R.styleable.SlidingTabLayout_tl_indicator_margin_left, dp2px(0));
        mIndicatorMarginTop = ta.getDimension(R.styleable.SlidingTabLayout_tl_indicator_margin_top,
                dp2px(mIndicatorStyle == STYLE_BLOCK ? 7 : 0));
        mIndicatorMarginRight = ta.getDimension(R.styleable.SlidingTabLayout_tl_indicator_margin_right, dp2px(0));
        mIndicatorMarginBottom = ta.getDimension(R.styleable.SlidingTabLayout_tl_indicator_margin_bottom,
                dp2px(mIndicatorStyle == STYLE_BLOCK ? 7 : 0));
        mIndicatorGravity = ta.getInt(R.styleable.SlidingTabLayout_tl_indicator_gravity, Gravity.BOTTOM);
        mIndicatorWidthEqualTitle = ta.getBoolean(R.styleable.SlidingTabLayout_tl_indicator_width_equal_title, false);

        mTextSize = ta.getDimension(R.styleable.SlidingTabLayout_kradio_tl_textSize, ResUtil.getDimen(R.dimen.text_size7));
        mTextSelectSize = ta
                .getDimension(R.styleable.SlidingTabLayout_kradio_tl_textSelectSize, ResUtil.getDimen(R.dimen.text_size9));

        mTextSelectColor = ta.getColor(R.styleable.SlidingTabLayout_tl_textSelectColor, Color.parseColor("#ffffff"));
        mTextUnselectColor = ta
                .getColor(R.styleable.SlidingTabLayout_tl_textUnselectColor, Color.parseColor("#AAffffff"));
        mTextBold = ta.getInt(R.styleable.SlidingTabLayout_tl_textBold, TEXT_BOLD_NONE);
        mTextAllCaps = ta.getBoolean(R.styleable.SlidingTabLayout_tl_textAllCaps, false);

        mTabSpaceEqual = ta.getBoolean(R.styleable.SlidingTabLayout_tl_tab_space_equal, false);
        mTabWidth = ta.getDimension(R.styleable.SlidingTabLayout_tl_tab_width, dp2px(-1));
        mTabPadding = ta.getDimension(R.styleable.SlidingTabLayout_tl_tab_padding,
                mTabSpaceEqual || mTabWidth > 0 ? 0 : ResUtil.getDimen(R.dimen.x20));
        mFirstNoPadding = ta.getBoolean(R.styleable.SlidingTabLayout_tl_first_no_padding, false);
        ta.recycle();
    }

    private int getDefaultHeight() {
        int defaultHeight = -1;
        if (mIndicatorStyle == STYLE_CUSTOM_DRAWABLE) {
            defaultHeight = mCustomIndicatorDrawable.getIntrinsicHeight();
        } else if (mIndicatorStyle == STYLE_TRIANGLE || mIndicatorStyle == STYLE_TRIANGLE_INVERTED) {
            defaultHeight = ResUtil.getDimen(R.dimen.x10);
        }
        return defaultHeight;
    }

    private int getDefaultWidth() {
        int defaultWidth = ResUtil.getDimen(R.dimen.y2);
        if (mIndicatorStyle == STYLE_CUSTOM_DRAWABLE) {
            defaultWidth = mCustomIndicatorDrawable.getIntrinsicWidth();
        } else if (mIndicatorStyle == STYLE_TRIANGLE || mIndicatorStyle == STYLE_TRIANGLE_INVERTED) {
            defaultWidth = ResUtil.getDimen(R.dimen.y4);
        } else if (mIndicatorStyle == STYLE_BLOCK) {
            defaultWidth = -1;
        }
        return defaultWidth;
    }

    /**
     * 更新数据
     */
    public void notifyDataSetChanged() {
        mTabsContainer.removeAllViews();
        this.mTabCount = mTabList.size();
        View tabView;
        for (int i = 0; i < mTabCount; i++) {
            tabView = LayoutInflater.from(mContext).inflate(R.layout.item_sliding_tab, null);
            Tab tab = mTabList.get(i);
            addTab(i, tab, tabView);
            mTabList.get(i).tabView = (TabView) tabView.findViewById(R.id.tv_tab_title);
        }
        updateTabStyles();
    }

    public void setTabs(List<Tab> tabs) {
        if (!ListUtil.isEmpty(tabs)) {
            mTabList = tabs;
            notifyDataSetChanged();
        }
    }

    /**
     * 生成渐变文字
     *
     * @param string
     * @return
     */
    public SpannableStringBuilder getRadiusGradientSpan(String string) {
        SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder(string);
        LinearGradientFontSpan span = new LinearGradientFontSpan(ResUtil.getColor(R.color.lib_tab_text_end)
                , ResUtil.getColor(R.color.lib_tab_text_start));
        spannableStringBuilder.setSpan(span, 0, spannableStringBuilder.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spannableStringBuilder;

    }

    /**
     * 创建并添加tab
     */
    private void addTab(final int position, Tab tab, View tabView) {
        TextView tvTabTitle = (TextView) tabView.findViewById(R.id.tv_tab_title);
        if (tvTabTitle != null) {
            if (tab != null) {
                if (!TextUtils.isEmpty(tab.icon)) {
                    //显示icon
                    tvTabTitle.setCompoundDrawablePadding(ResUtil.getDimen(R.dimen.m8));
                    GlideApp.with(getContext()).load(tab.icon).override(ResUtil.getDimen(R.dimen.m46)).into(new SimpleTarget<Drawable>() {
                        @Override
                        public void onResourceReady(@NonNull Drawable resource, @Nullable Transition<? super Drawable> transition) {
//                            Drawable dra = new BitmapDrawable(String.valueOf(resource));
                            resource.setBounds(0, 0, resource.getMinimumWidth(), resource.getMinimumHeight());
                            tvTabTitle.setCompoundDrawables(resource, null, null, null);
                        }
                    });
//                    tvTabTitle.setText(getRadiusGradientSpan(tab.title));
                    tvTabTitle.setText(tab.title);
                } else {
                    tvTabTitle.setText(tab.title);
                    tvTabTitle.setCompoundDrawablePadding(0);
                    tvTabTitle.setCompoundDrawables(null, null, null, null);
                }
                tabView.setContentDescription(tab.title);
            }
        }

        tabView.setOnClickListener(v -> {
            int index = mTabsContainer.indexOfChild(v);
            if (index != -1) {
                if (index != mCurrentTab) {
                    if (mListener != null) {
                        mListener.onTabSelect(index);
                    }
                    setCurrentTab(index);
                } else {
                    if (mListener != null) {
                        mListener.onTabReselect(index);
                    }
                }
            }
        });

        /** 每一个Tab的布局参数 */
        LinearLayout.LayoutParams lpTab = mTabSpaceEqual ?
                new LinearLayout.LayoutParams(0, LayoutParams.MATCH_PARENT, 1.0f) :
                new LinearLayout.LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.MATCH_PARENT);
        if (mTabWidth > 0) {
            lpTab = new LinearLayout.LayoutParams((int) mTabWidth, LayoutParams.MATCH_PARENT);
        }

        mTabsContainer.addView(tabView, position, lpTab);
    }

    private void updateTabStyles() {
        if (mTabCount <= 0) {
            return;
        }
        for (int i = 0; i < mTabCount; i++) {
            TabView tvTabTitle = (TabView) mTabsContainer.getChildAt(i).findViewById(R.id.tv_tab_title);
            if (tvTabTitle != null) {
                boolean selected = i == mCurrentTab;
                //第一次draw的时候不需要动画
                tvTabTitle.setCanAnimator(!isFirstDraw);
                tvTabTitle.setTextSize(TypedValue.COMPLEX_UNIT_PX, selected ? mTextSelectSize : mTextSize);
                tvTabTitle.setSelected(selected);
                tvTabTitle.setTextColor(selected ? mTextSelectColor : mTextUnselectColor);

                tvTabTitle.setPadding((int) mTabPadding, 0, (int) mTabPadding, 0);
                if (mTextAllCaps) {
                    tvTabTitle.setText(tvTabTitle.getText().toString().toUpperCase());
                }
                if (mTextBold == TEXT_BOLD_BOTH) {
                    tvTabTitle.getPaint().setFakeBoldText(true);
                } else if (mTextBold == TEXT_BOLD_NONE) {
                    tvTabTitle.getPaint().setFakeBoldText(false);
                } else if (mTextBold == TEXT_BOLD_WHEN_SELECT) {
                    tvTabTitle.getPaint().setFakeBoldText(selected);
                }
//                if (mCustomIndicatorDrawable != null) {
//                    if (selected)
//                        tvTabTitle.setBackground(mCustomIndicatorDrawable);
//                    else
//                        tvTabTitle.setBackground(new ColorDrawable());
//                }
            }
        }
        if (mFirstNoPadding) {
            View firstItem = mTabsContainer.getChildAt(0);
            if (firstItem != null) {
                firstItem.setPadding(0, 0, (int) mTabPadding, 0);
            }
        }
    }

//    private void updateTabDrawable() {
//        if (mTabCount <= 0) {
//            return;
//        }
//        for (int i = 0; i < mTabCount; i++) {
//            TabView tvTabTitle = (TabView) mTabsContainer.getChildAt(i).findViewById(R.id.tv_tab_title);
//            if (tvTabTitle != null) {
//                boolean selected = i == mCurrentTab;
//                if (mCustomIndicatorDrawable != null) {
//                    if (selected)
//                        tvTabTitle.setBackground(mCustomIndicatorDrawable);
//                    else
//                        tvTabTitle.setBackground(new ColorDrawable());
//                }
//            }
//        }
//    }

    private void calcOffset() {
        View view = mTabsContainer.getChildAt(this.mCurrentTab);

        if (view == null) {
            return;
        }
        final TabView currentTabView = (TabView) view.findViewById(R.id.tv_tab_title);
        int left = currentTabView.getLeft();
        int right = currentTabView.getRight();
        mCurrentP.left = left;
        mCurrentP.right = right;
        TabView lastTabView = null;
        int lastLeft = 0, lastRight = 0;
        if (this.mLastTab > -1) {
            lastTabView = (TabView) mTabsContainer.getChildAt(this.mLastTab).findViewById(R.id.tv_tab_title);

            if (lastTabView != null) {
                lastLeft = lastTabView.getLeft();
                lastRight = lastTabView.getRight();
                mLastP.left = lastLeft;
                mLastP.right = lastRight;
            }
        }
        if (mLastP.left == mCurrentP.left && mLastP.right == mCurrentP.right) {
            invalidate();
        } else {
            float reduced = 0, diff = 0;
            if (lastTabView != null) {
                int width = lastTabView.getWidth();
                mLastP.left = lastLeft + (width - mIndicatorWidth) / 2;
                mLastP.right = mLastP.left + mIndicatorWidth;
                float originWidth = getWidth(lastTabView, false);
                //缩小的距离
                reduced = width - originWidth;
            }
            if (mCurrentTab > mLastTab) {
                mCurrentP.left = left - reduced + (getWidth(currentTabView, true) - mIndicatorWidth) / 2;
                mCurrentP.right = mCurrentP.left + mIndicatorWidth;
            } else {
                mCurrentP.left = left + (getWidth(currentTabView, true) - mIndicatorWidth) / 2;
                mCurrentP.right = mCurrentP.left + mIndicatorWidth;
            }
            mValueAnimator.setObjectValues(mLastP, mCurrentP);
            mValueAnimator.setInterpolator(mInterpolator);

            if (mIndicatorAnimDuration < 0) {
                mIndicatorAnimDuration = ANIMATION_DURATION;
            }
            mValueAnimator.setDuration(mIndicatorAnimDuration);
            mValueAnimator.start();

        }
    }

    /**
     * HorizontalScrollView滚到当前tab,并且居中显示
     */
    public void scrollToCurrentTab() {
        if (mTabCount <= 0) {
            return;
        }

        /**当前Tab的left+当前Tab的Width乘以positionOffset*/
        int newScrollX = mTabsContainer.getChildAt(mCurrentTab).getLeft();

        if (mCurrentTab > 0) {
            /**HorizontalScrollView移动到当前tab,并居中*/
            newScrollX -= getWidth() / 2 - getPaddingLeft();
            View currentTabView = mTabsContainer.getChildAt(this.mCurrentTab);
            float left = currentTabView.getLeft();
            float right = currentTabView.getRight();

            newScrollX += ((right - left) / 2);
        }
        if (newScrollX != mLastScrollX) {
            mLastScrollX = newScrollX;
            /** scrollTo（int x,int y）:x,y代表的不是坐标点,而是偏移量
             *  x:表示离起始位置的x水平方向的偏移量
             *  y:表示离起始位置的y垂直方向的偏移量
             */

        }
        smoothScrollTo(newScrollX, 0);
    }

    private float margin;

    private void calcIndicatorRect() {
        TabView currentTabView = (TabView) mTabsContainer.getChildAt(this.mCurrentTab).findViewById(R.id.tv_tab_title);
        float left = currentTabView.getLeft();
        float right = currentTabView.getRight();

        //for mIndicatorWidthEqualTitle
        boolean isNormalEqualTitle = mIndicatorStyle == STYLE_NORMAL && mIndicatorWidthEqualTitle;
        float textWidth = 0;
        if (isNormalEqualTitle) {
            TextView tabTitle = currentTabView;
            mTextPaint.setTextSize(mTextSize);
            textWidth = mTextPaint.measureText(tabTitle.getText().toString());
            margin = (right - left - textWidth) / 2;

        }

        mIndicatorRect.left = (int) left;
        mIndicatorRect.right = (int) right;

        //for mIndicatorWidthEqualTitle
        if (isNormalEqualTitle) {
            if (mFirstNoPadding && this.mCurrentTab == 0) {
                mIndicatorRect.left = (int) left;
                mIndicatorRect.right = (int) (left + textWidth);
            } else {
                mIndicatorRect.left = (int) (left + margin);
                mIndicatorRect.right = (int) (right - margin);
            }
        }

        mTabRect.left = (int) left;
        mTabRect.right = (int) right;

        //indicatorWidth大于0时,圆角矩形以及三角形
        if (mIndicatorWidth >= 0 && !isNormalEqualTitle) {
            float indicatorLeft = left + (getWidth(currentTabView, true) - mIndicatorWidth) / 2;

            if (mFirstNoPadding && this.mCurrentTab == 0) {
                mIndicatorRect.left = (int) indicatorLeft - (int) mTabPadding / 2;
            } else {
                mIndicatorRect.left = (int) indicatorLeft;
            }
            mIndicatorRect.right = (int) (mIndicatorRect.left + mIndicatorWidth);
        }
    }

    private float getWidth(TabView tabView, boolean selected) {
        mTextPaint.setTextSize(selected ? mTextSelectSize : mTextSize);
        return mTextPaint.measureText(tabView.getText().toString()) + tabView.getPaddingLeft() + tabView.getPaddingRight();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        if (isInEditMode() || mTabCount <= 0) {
            return;
        }

        int height = getHeight();
        int paddingLeft = getPaddingLeft();
        //draw indicator line
        if (mIndicatorAnimEnable) {
            if (isFirstDraw) {
                calcIndicatorRect();
                isFirstDraw = false;
            }
        } else {
            calcIndicatorRect();
        }
        if (mIndicator != null) {
            mIndicator.draw(canvas, paddingLeft, height);
        }
    }

    public void setCurrentTab(int currentTab) {
        if (currentTab < 0) {
            return;
        }
        if (mCurrentTab == currentTab) {
            return;
        }
        setCurrentTabForce(currentTab);
    }

    /**
     * 强制设置当前选中tab
     */
    public void setCurrentTabForce(int currentTab) {
        mLastTab = mCurrentTab;
        this.mCurrentTab = currentTab;
        updateTabStyles();
        scrollToCurrentTab();
        if (mIndicatorAnimEnable) {
            calcOffset();
        } else {
            invalidate();
        }
    }

    public void setIndicatorStyle(int indicatorStyle) {
        this.mIndicatorStyle = indicatorStyle;
        invalidate();
    }

    public void setTabPadding(float tabPadding) {
        this.mTabPadding = dp2px(tabPadding);
        updateTabStyles();
    }

    public void setTabSpaceEqual(boolean tabSpaceEqual) {
        this.mTabSpaceEqual = tabSpaceEqual;
        updateTabStyles();
    }

    public void setTabWidth(float tabWidth) {
        this.mTabWidth = tabWidth;
        updateTabStyles();
    }

    public void setIndicatorColor(int indicatorColor) {
        this.mIndicatorColor = indicatorColor;
        invalidate();
    }

    public void setmCustomIndicatorDrawable(Drawable pic) {
        this.mCustomIndicatorDrawable = pic;
//        updateTabDrawable();
    }

    public void setIndicatorHeight(float indicatorHeight) {
        this.mIndicatorHeight = dp2px(indicatorHeight);
        invalidate();
    }

    public void setIndicatorWidth(float indicatorWidth) {
        this.mIndicatorWidth = dp2px(indicatorWidth);
        invalidate();
    }

    public void setIndicatorCornerRadius(float indicatorCornerRadius) {
        this.mIndicatorCornerRadius = dp2px(indicatorCornerRadius);
        invalidate();
    }

    public void setIndicatorGravity(int indicatorGravity) {
        this.mIndicatorGravity = indicatorGravity;
        invalidate();
    }

    public void setIndicatorMargin(float indicatorMarginLeft, float indicatorMarginTop,
                                   float indicatorMarginRight, float indicatorMarginBottom) {
        this.mIndicatorMarginLeft = dp2px(indicatorMarginLeft);
        this.mIndicatorMarginTop = dp2px(indicatorMarginTop);
        this.mIndicatorMarginRight = dp2px(indicatorMarginRight);
        this.mIndicatorMarginBottom = dp2px(indicatorMarginBottom);
        invalidate();
    }

    public void setIndicatorWidthEqualTitle(boolean indicatorWidthEqualTitle) {
        this.mIndicatorWidthEqualTitle = indicatorWidthEqualTitle;
        invalidate();
    }

    public void setTextSize(float textSize) {
        this.mTextSize = textSize;
        updateTabStyles();
    }

    public void setTextSelectedSize(float mTextSelectSize) {
        this.mTextSelectSize = mTextSelectSize;
        updateTabStyles();
    }

    public void setTextSize(float textSize, float textSelectSize) {
        boolean update = false;
        if (mTextSize != textSize) {
            this.mTextSize = textSize;
            update = true;
        }
        if (mTextSelectSize != textSelectSize) {
            this.mTextSelectSize = textSelectSize;
            update = true;
        }
        if (update) {
            //旋转屏幕重新计算
            isFirstDraw = true;
            updateTabStyles();
        }
    }

    public void setTextSelectColor(int textSelectColor) {
        this.mTextSelectColor = textSelectColor;
        updateTabStyles();
    }

    public void setTextUnselectColor(int textUnselectColor) {
        this.mTextUnselectColor = textUnselectColor;
        updateTabStyles();
    }

    public void setTextBold(int textBold) {
        this.mTextBold = textBold;
        updateTabStyles();
    }

    public void setTextAllCaps(boolean textAllCaps) {
        this.mTextAllCaps = textAllCaps;
        updateTabStyles();
    }

    public int getTabCount() {
        return mTabCount;
    }

    public Tab getCurrentTab() {
        if (mTabList.size() > mCurrentTab && mCurrentTab >= 0) {
            return mTabList.get(mCurrentTab);
        }
        return null;
    }

    public int getIndicatorStyle() {
        return mIndicatorStyle;
    }

    public float getTabPadding() {
        return mTabPadding;
    }

    public boolean isTabSpaceEqual() {
        return mTabSpaceEqual;
    }

    public float getTabWidth() {
        return mTabWidth;
    }

    public int getIndicatorColor() {
        return mIndicatorColor;
    }

    public float getIndicatorHeight() {
        return mIndicatorHeight;
    }

    public float getIndicatorWidth() {
        return mIndicatorWidth;
    }

    public float getIndicatorCornerRadius() {
        return mIndicatorCornerRadius;
    }

    public float getIndicatorMarginLeft() {
        return mIndicatorMarginLeft;
    }

    public float getIndicatorMarginTop() {
        return mIndicatorMarginTop;
    }

    public float getIndicatorMarginRight() {
        return mIndicatorMarginRight;
    }

    public float getIndicatorMarginBottom() {
        return mIndicatorMarginBottom;
    }

    public float getTextSize() {
        return mTextSize;
    }

    public float getTextSelectedSize() {
        return mTextSelectSize;
    }

    public int getTextSelectColor() {
        return mTextSelectColor;
    }

    public int getTextUnselectColor() {
        return mTextUnselectColor;
    }

    public int getTextBold() {
        return mTextBold;
    }

    public boolean isTextAllCaps() {
        return mTextAllCaps;
    }

    public TextView getTitleView(int tab) {
        return (TextView) mTabsContainer.getChildAt(tab);
    }

    //setter and getter

    // show MsgTipView
    private Paint mTextPaint = new Paint(Paint.ANTI_ALIAS_FLAG);

    private OnTabSelectListener mListener;

    public void setOnTabSelectListener(OnTabSelectListener listener) {
        this.mListener = listener;
    }

    @Override
    protected Parcelable onSaveInstanceState() {
        Bundle bundle = new Bundle();
        bundle.putParcelable("instanceState", super.onSaveInstanceState());
        bundle.putInt("mCurrentTab", mCurrentTab);
        return bundle;
    }

    @Override
    protected void onRestoreInstanceState(Parcelable state) {
        if (state instanceof Bundle) {
            Bundle bundle = (Bundle) state;
            mCurrentTab = bundle.getInt("mCurrentTab");
            state = bundle.getParcelable("instanceState");
            if (mCurrentTab != 0 && mTabsContainer.getChildCount() > 0) {
                updateTabStyles();
                scrollToCurrentTab();
            }
        }
        super.onRestoreInstanceState(state);
    }

    protected int dp2px(float dp) {
        final float scale = mContext.getResources().getDisplayMetrics().density;
        return (int) (dp * scale + 0.5f);
    }

    class IndicatorPoint {

        public float left = 0;

        public float right;

        public float offset;

        @Override
        public String toString() {
            return "IndicatorPoint{" +
                    "left=" + left +
                    ", right=" + right +
                    ", offset=" + offset +
                    '}';
        }
    }

    private IndicatorPoint mCurrentP = new IndicatorPoint();

    private IndicatorPoint mLastP = new IndicatorPoint();

    class PointEvaluator implements TypeEvaluator<IndicatorPoint> {

        @Override
        public IndicatorPoint evaluate(float fraction, IndicatorPoint startValue, IndicatorPoint endValue) {
            float left = startValue.left + fraction * (endValue.left - startValue.left);
            float right = startValue.right + fraction * (endValue.right - startValue.right);
            IndicatorPoint point = new IndicatorPoint();
            point.left = left;
            point.right = right;
            point.offset = fraction;
            return point;
        }
    }

    @Override
    protected void onScrollChanged(int l, int t, int oldl, int oldt) {
        super.onScrollChanged(l, t, oldl, oldt);
        if (mChangeListener != null) {
            int maxScrollX = getChildAt(0).getMeasuredWidth() - getMeasuredWidth();
            mChangeListener.onScrollChange(l == maxScrollX);
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (mValueAnimator != null) {
            mValueAnimator.removeAllListeners();
            mValueAnimator = null;
        }
        mChangeListener = null;
    }

    @Override
    protected void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        TimerUtil.newInstance().timer(100, num -> setCurrentTabForce(mCurrentTab));
    }

    public void setOnScrollChangeListener(OnScrollChangeListener changeListener) {
        mChangeListener = changeListener;
    }

    public interface OnScrollChangeListener {

        /**
         * 滑动监听
         *
         * @param isEnd 是否滑到最后
         */
        void onScrollChange(boolean isEnd);
    }

    /**
     * tab 的指针
     *
     * <AUTHOR> Yan
     * @date 2019-12-05
     */
    interface Indicator {

        /**
         * 绘制指针
         */
        void draw(Canvas canvas, int paddingLeft, int height);
    }

    private long startTime;

    /**
     * 图片指针
     */
    class DrawableIndicator implements Indicator {

        @RequiresApi(api = Build.VERSION_CODES.KITKAT)
        @Override
        public void draw(Canvas canvas, int paddingLeft, int height) {
            if (mCustomIndicatorDrawable != null) {
                if (getIndicatorHeight() > 0 && mIndicatorMarginBottom > 0) {//底部计算边距
                    mCustomIndicatorDrawable.setBounds(paddingLeft + mIndicatorRect.left,
                            (int) (height - getIndicatorHeight() - mIndicatorMarginBottom),
                            paddingLeft + mIndicatorRect.right,
                            (int) (height - mIndicatorMarginBottom));
                } else {
                    mCustomIndicatorDrawable.setBounds(paddingLeft + mIndicatorRect.left,
                            height - mCustomIndicatorDrawable.getIntrinsicHeight(),
                            paddingLeft + mIndicatorRect.right,
                            height);
                }
                if (mIndicatorAnimEnable) {
                    int maxAlpha = 255;//最大透明度
                    float count = Math.abs(mCurrentP.left - mLastP.left);//移动总距离
                    float toX = Math.abs(mIndicatorRect.left - mLastP.left);//当前移动距离
                    int in = 255;//变化的透明度
                    float toTime = 0;
                    if (count != 0 && toX != 0) {
                        //左右切换渐隐动画
                        float toXb = toX / (count / 2);

                        if (toXb > 1) {
                            in = (int) (maxAlpha * (toXb - 1));
                        } else {
                            in = (int) (maxAlpha - (maxAlpha * toXb));
                        }

                    } else {
                        //首次绘制在300毫秒内从无到有
                        if (startTime == 0) {
                            startTime = System.currentTimeMillis();
                            in = 0;
                        } else {
                            toTime = (System.currentTimeMillis() - startTime) / 300f;
                            in = (int) (maxAlpha * toTime);
                        }
                    }
                    if (in > maxAlpha) {
                        in = maxAlpha;
                    }
                    mCustomIndicatorDrawable.setAlpha(in);
                    Log.d("cai123", "----" + mIndicatorRect.left
                            + "--" + (System.currentTimeMillis() - startTime) + "--"
                            + toTime + "--" + in);
                }
                mCustomIndicatorDrawable.draw(canvas);
            }
        }
    }

    class TriangleIndicator implements Indicator {

        /**
         * 是不是倒三角，默认false-不是
         */
        private boolean mInverted = false;

        private Paint mTrianglePaint = new Paint(Paint.ANTI_ALIAS_FLAG);

        public TriangleIndicator(boolean inverted) {
            mInverted = inverted;
        }

        @Override
        public void draw(Canvas canvas, int paddingLeft, int height) {
            if (mIndicatorHeight > 0) {
                mTrianglePaint.setColor(mIndicatorColor);
                mTrianglePath.reset();
                float bottomCornerY = height;
                float apexY = height - mIndicatorHeight;
                if (mInverted) {
                    bottomCornerY = height - mIndicatorHeight;
                    apexY = height;
                }
                mTrianglePath.moveTo(paddingLeft + mIndicatorRect.left, bottomCornerY);
                mTrianglePath.lineTo(paddingLeft + mIndicatorRect.left / 2 + mIndicatorRect.right / 2, apexY);
                mTrianglePath.lineTo(paddingLeft + mIndicatorRect.right, bottomCornerY);
                mTrianglePath.close();
                canvas.drawPath(mTrianglePath, mTrianglePaint);
            }
        }
    }

    class BlockIndicator implements Indicator {

        @Override
        public void draw(Canvas canvas, int paddingLeft, int height) {
            if (mIndicatorHeight < 0) {
                mIndicatorHeight = height - mIndicatorMarginTop - mIndicatorMarginBottom;
            }
            if (mIndicatorHeight > 0) {
                if (mIndicatorCornerRadius < 0 || mIndicatorCornerRadius > mIndicatorHeight / 2) {
                    mIndicatorCornerRadius = mIndicatorHeight / 2;
                }

                mIndicatorDrawable.setColor(mIndicatorColor);
                mIndicatorDrawable.setBounds(paddingLeft + (int) mIndicatorMarginLeft + mIndicatorRect.left,
                        (int) mIndicatorMarginTop,
                        (int) (paddingLeft + mIndicatorRect.right - mIndicatorMarginRight),
                        (int) (mIndicatorMarginTop + mIndicatorHeight));
                mIndicatorDrawable.setCornerRadius(mIndicatorCornerRadius);
                mIndicatorDrawable.draw(canvas);
            }
        }
    }

    class DefaultIndicator implements Indicator {

        @Override
        public void draw(Canvas canvas, int paddingLeft, int height) {
            if (mIndicatorHeight > 0) {
                mIndicatorDrawable.setColor(mIndicatorColor);

                if (mIndicatorGravity == Gravity.BOTTOM) {
                    mIndicatorDrawable.setBounds(paddingLeft + (int) mIndicatorMarginLeft + mIndicatorRect.left,
                            height - (int) mIndicatorHeight - (int) mIndicatorMarginBottom,
                            paddingLeft + mIndicatorRect.right - (int) mIndicatorMarginRight,
                            height - (int) mIndicatorMarginBottom);
                } else {
                    mIndicatorDrawable.setBounds(paddingLeft + (int) mIndicatorMarginLeft + mIndicatorRect.left,
                            (int) mIndicatorMarginTop,
                            paddingLeft + mIndicatorRect.right - (int) mIndicatorMarginRight,
                            (int) mIndicatorHeight + (int) mIndicatorMarginTop);
                }
                mIndicatorDrawable.setCornerRadius(mIndicatorCornerRadius);
                mIndicatorDrawable.draw(canvas);
            }
        }
    }

    public void setIndicatorAnimEnable(boolean mIndicatorAnimEnable) {
        this.mIndicatorAnimEnable = mIndicatorAnimEnable;
    }
}
