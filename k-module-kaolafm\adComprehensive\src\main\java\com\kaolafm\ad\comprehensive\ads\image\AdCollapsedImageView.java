package com.kaolafm.ad.comprehensive.ads.image;

import android.content.Context;
import androidx.annotation.Nullable;
import android.util.AttributeSet;

import com.kaolafm.ad.comprehensive.ads.image.base.BaseAdImageView;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.util.ReportConstants;
import com.kaolafm.report.util.ReportParameterManager;

public class AdCollapsedImageView extends BaseAdImageView {

    public AdCollapsedImageView(Context context) {
        super(context);
    }

    public AdCollapsedImageView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public AdCollapsedImageView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public void loadAdContent(AdContentInfo adContentInfo) {
        mTvSkip.setText("收起");
        mTvSkip.setOnClickListener((v) -> {
            mTvSkip.setOnClickListener(null);
            mAdImageListener.onAdImageCollapsed();
            ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_AD_FOLD, mTvSkip.getText().toString(), ReportParameterManager.getInstance().getPage()
                    , ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_ADVERT, null, null, null, adContentInfo.getId()));
        });
        super.loadAdContent(adContentInfo);
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_AD_FOLD, mTvSkip.getText().toString(), ReportParameterManager.getInstance().getPage()
                , ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_ADVERT, null, null, null, adContentInfo.getId()));
    }

}
