<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:layout_gravity="center"
    android:paddingTop="@dimen/m20"
    android:gravity="center">
    <!--必须保留 LinearLayout,否则SlidingTabLayout的下三角与文字太靠近-->
    <com.kaolafm.kradio.lib.widget.tab.SlidingTabLayout
        android:id="@+id/stb_all_category_title_name"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/y44"
        android:background="@drawable/online_tab_class_all_bg"
        android:padding="0dp"
        app:tl_first_no_padding="false"
        app:tl_indicator_style="CUSTOM_DRAWABLE"
        app:tl_indicator_drawable="@drawable/online_tab_class_all_indicator_bg"
        app:tl_indicator_height="0dp"
        app:tl_indicator_width_equal_title="true"
        app:tl_indicator_width="0dp"
        app:tl_tab_width="@dimen/x128"
        app:tl_textSelectColor="@color/user_info_value_color"
        app:tl_textUnselectColor="@color/user_info_value_color"
        app:kradio_tl_textSize="@dimen/online_nav_bar_tv_unselected" />
</LinearLayout>
