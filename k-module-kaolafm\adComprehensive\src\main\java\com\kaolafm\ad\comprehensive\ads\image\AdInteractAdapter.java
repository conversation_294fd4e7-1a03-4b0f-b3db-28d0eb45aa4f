package com.kaolafm.ad.comprehensive.ads.image;

import android.content.Context;

import com.kaolafm.ad.api.model.InteractionAdvert;
import com.kaolafm.ad.comprehensive.ads.image.base.BaseAdContentView;
import com.kaolafm.ad.comprehensive.ads.image.base.BaseAdImageAdapter;

public class AdInteractAdapter extends BaseAdImageAdapter<InteractionAdvert> {
    @Override
    public BaseAdContentView<InteractionAdvert> onCreateAdView(Context context) {
        return new AdInteractContentView(context);
    }

    @Override
    public void onBindAdView(BaseAdContentView<InteractionAdvert> baseAdContentView, InteractionAdvert interactionAdvert) {
        baseAdContentView.loadAdContent(interactionAdvert);
    }
}
