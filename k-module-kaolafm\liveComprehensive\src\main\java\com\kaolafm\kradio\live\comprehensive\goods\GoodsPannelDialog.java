package com.kaolafm.kradio.live.comprehensive.goods;

import android.content.DialogInterface;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentManager;
import androidx.viewpager.widget.ViewPager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.LinearLayout;

import com.kaolafm.kradio.common.utils.CommonUtils;
import com.kaolafm.kradio.k_kaolafm.BuildConfig;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.report.ReportUtil;
import com.kaolafm.kradio.lib.utils.RecyclerViewExposeUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.opensdk.api.goods.model.Goods;
import com.kaolafm.opensdk.api.goods.model.GoodsResult;
import com.kaolafm.report.util.ReportParameterManager;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> shiqian
 * @date 2023-02-21
 */
public class GoodsPannelDialog extends DialogFragment implements RecyclerViewExposeUtil.OnItemExposeListener {


    private GoodsPanelControl goodsPanelControl;
    private LinearLayout shop_viewpager, shop_recyclerview;
    private RecyclerView shopRecyclerView;
    private ViewPager shopViewpager;
    private LinearLayout shopDotsLayout;

    List<Goods> mDatas = new ArrayList<>();

    @Override
    public void onStart() {
        super.onStart();
        CommonUtils.getInstance().initGreyStyle(getDialog().getWindow());

        getDialog().getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
//        getDialog().getWindow().setDimAmount(0f);
        WindowManager.LayoutParams layoutParams = getDialog().getWindow().getAttributes();
        layoutParams.gravity = Gravity.BOTTOM; // 位置
        layoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT;//宽度满屏
//        layoutParams.horizontalMargin = ResUtil.getDimen(R.dimen.x48);
        layoutParams.dimAmount = 0f;
        getDialog().getWindow().setAttributes(layoutParams);
        int padding = BuildConfig.LAYOUT_TYPE == 0 ? ResUtil.getDimen(R.dimen.m48) : ResUtil.getDimen(R.dimen.m40);
        getDialog().getWindow().getDecorView().setPadding(padding, 0, padding, 0);
//        getDialog().getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
    }

    RecyclerViewExposeUtil recyclerViewExposeUtil;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.comprehensive_goods_box, container, false);
        shop_viewpager = (LinearLayout) view.findViewById(R.id.shop_viewpager);
        shop_recyclerview = (LinearLayout) view.findViewById(R.id.shop_recyclerview);
        shopRecyclerView = (RecyclerView) view.findViewById(R.id.rv_shop);
        shopViewpager = (ViewPager) view.findViewById(R.id.shop_pagers);
        shopDotsLayout = (LinearLayout) view.findViewById(R.id.dots_container);
        recyclerViewExposeUtil = new RecyclerViewExposeUtil();
        recyclerViewExposeUtil.setRecyclerItemExposeListener(shopRecyclerView, this);
        goodsPanelControl = new GoodsPanelControl(getContext(), shopViewpager, shopRecyclerView, shopDotsLayout);
//        goodsPanelControl.setData(mDatas);
        view.findViewById(R.id.cd_close).setOnClickListener((v)->dismiss());
        goodsPanelControl.setOnShopSelectedListener(new GoodsPanelControl.OnShopSelectedListener() {
            @Override
            public void onSelected(Goods commodity) {

            }
        });
        goodsPanelControl.setOnShopPurchaseListener(new GoodsPanelControl.OnShopPurchaseListener() {
            @Override
            public void onPurchase(Goods goods) {
                if (mOnShopPurchaseListener != null) {
                    mOnShopPurchaseListener.onPurchase(goods);
                }
            }
        });
        shop_viewpager.setVisibility(View.GONE);
        shop_recyclerview.setVisibility(View.VISIBLE);
        return view;
    }

    @Override
    public void show(FragmentManager manager, String tag) {
        super.show(manager, tag);
        if (recyclerViewExposeUtil != null) {
            recyclerViewExposeUtil.handleCurrentVisibleItems();
        }
    }

    @Override
    public void onDismiss(DialogInterface dialog) {
        super.onDismiss(dialog);
        if (mOnDismissListener != null) {
            mOnDismissListener.onDismiss();
        }
    }

    public GoodsPannelDialog setData(GoodsResult goodsResult) {
        if (goodsResult != null) {
            mDatas.clear();
            mDatas = goodsResult.getGoodsList();
        }
        if (goodsPanelControl != null) {
            goodsPanelControl.setData(mDatas);
        }
        return this;
    }

    @Override
    public void onItemViewVisible(boolean visible, int position) {
        reportContentShowClickEvent(mDatas.get(position).getId()+"", position, false);
    }

    private void reportContentShowClickEvent(String id, int i, boolean b) {
        ReportUtil.addComponentShowAndClickEvent(id + "",
                b, "20"
                , 2, "0", i + "",
                0 + "", id + ""
                , "无", ReportParameterManager.getInstance().getPage(), "无");
    }

    public interface OnShopPurchaseListener {
        void onPurchase(Goods goods);
    }

    private GoodsPanelControl.OnShopPurchaseListener mOnShopPurchaseListener;

    public GoodsPannelDialog setOnShopPurchaseListener(GoodsPanelControl.OnShopPurchaseListener
                                                               listener) {
        mOnShopPurchaseListener = listener;
        return this;
    }


    public interface OnDismissListener {
        void onDismiss();
    }

    OnDismissListener mOnDismissListener;

    public GoodsPannelDialog setOnDismissListener(OnDismissListener onDismissListener) {
        mOnDismissListener = onDismissListener;
        return this;
    }
}
