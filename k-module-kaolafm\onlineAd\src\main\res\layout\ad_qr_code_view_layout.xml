<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <View
        android:id="@+id/qrcode_back"
        android:layout_width="@dimen/m180"
        android:layout_height="@dimen/m180"
        android:layout_marginStart="@dimen/m14"
        android:layout_marginTop="@dimen/m20"
        android:layout_marginEnd="@dimen/m20"
        android:layout_marginBottom="@dimen/m36"
        android:background="@drawable/online_ad_qrcode_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_ad_close" />

    <ImageView
        android:id="@+id/tv_ad_close"
        android:layout_width="@dimen/m48"
        android:layout_height="@dimen/m48"
        android:src="@drawable/online_ad_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_ad_qr"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="@dimen/m20"
        android:layout_marginTop="@dimen/y8"
        android:layout_marginBottom="@dimen/y8"
        app:layout_constraintBottom_toBottomOf="@id/qrcode_back"
        app:layout_constraintEnd_toEndOf="@id/qrcode_back"
        app:layout_constraintStart_toStartOf="@id/qrcode_back"
        app:layout_constraintTop_toTopOf="@id/qrcode_back" />

    <TextView
        android:id="@+id/tv_ad_msg"
        android:layout_width="@dimen/m208"
        android:layout_height="@dimen/m40"
        android:background="@drawable/online_ad_qrcode_tip_bg"
        android:gravity="center"
        android:maxEms="8"
        android:singleLine="true"
        android:textColor="@color/ad_text_color"
        android:textSize="@dimen/text_size2"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/qrcode_back"
        app:layout_constraintStart_toStartOf="@id/qrcode_back"
        tools:text="瑞星咖啡五折抢购" />


</androidx.constraintlayout.widget.ConstraintLayout>