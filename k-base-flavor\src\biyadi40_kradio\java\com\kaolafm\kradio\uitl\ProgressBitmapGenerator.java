package com.kaolafm.kradio.uitl;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.RectF;
import android.graphics.Shader;
import android.graphics.SweepGradient;
import android.graphics.drawable.Drawable;
import android.util.DisplayMetrics;
import android.util.Log;

import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.utils.ScreenUtil;


public class ProgressBitmapGenerator {

    private static String TAG = "ProgressBitmapGenerator";

    public static Bitmap generateMusicWidgetProgress(Context context, int duration, int progress) {
        int height = dpToPx(context, 80);
        float radius = dpToPx(context, 12);
        int progressHeight = dpToPx(context, 4);
        int width = context.getResources().getDimensionPixelSize(R.dimen.byd_35widget_progress_width);
        int startColor = context.getResources().getColor(R.color.byd_35widget_progress_start_color);
        int endColor = context.getResources().getColor(R.color.byd_35widget_progress_end_color);
        int lightColor = context.getResources().getColor(R.color.byd_35widget_progress_light_color);
        int lightStartColor = context.getResources().getColor(R.color.byd_35widget_progress_light_start_color);


        Bitmap bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        Paint paint = new Paint(Paint.ANTI_ALIAS_FLAG | Paint.FILTER_BITMAP_FLAG);

        Bitmap dest = generateMusicWidgetDestProgress(width, height, radius);
        Bitmap src = generateMusicWidgetSrcProgress(width, height, (int) (width * (progress / (duration * 1.0))), progressHeight, startColor, endColor, lightColor, lightStartColor);

        canvas.drawBitmap(dest, 0, 0, paint);
        RectF rectF = new RectF(0, 0, width, height);
        paint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.MULTIPLY));
        canvas.drawBitmap(src, null, rectF, paint);
        return bitmap;
    }

    private static Bitmap generateMusicWidgetDestProgress(int width, int height, float radius) {
        Bitmap dest = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(dest);
        Paint paint = new Paint(Paint.ANTI_ALIAS_FLAG | Paint.FILTER_BITMAP_FLAG);
        paint.setColor(Color.WHITE);
        canvas.drawRoundRect(0, 0, width, height, radius, radius, paint);
        RectF rectF = new RectF(0, 0, radius, radius);
        canvas.drawRect(rectF, paint);
        rectF.set(width - radius, 0, width, radius);
        canvas.drawRect(rectF, paint);
        return dest;
    }

    private static Bitmap generateMusicWidgetSrcProgress(int width, int height, int right, int progressHeight, int startColor, int endColor, int lightColor, int lightStartColor) {
        Bitmap src = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(src);
        Paint paint = new Paint(Paint.ANTI_ALIAS_FLAG | Paint.FILTER_BITMAP_FLAG);
        LinearGradient shader = new LinearGradient(0, 0, 0, height, startColor, endColor, Shader.TileMode.CLAMP);
        paint.setShader(shader);
        //绘制整体进度背景
        canvas.drawRect(0, 0, right, height, paint);

        paint.setShader(null);
        //修改绘制线条为渐变色（左-->右）
        LinearGradient shaderLine = new LinearGradient(0, 0, width, 0, lightStartColor, lightColor, Shader.TileMode.CLAMP);
        paint.setShader(shaderLine);
//        paint.setColor(lightColor);

        paint.setStrokeWidth(progressHeight);
        //绘制底部进度线条
        canvas.drawLine(0, height - progressHeight / 2f, right, height - progressHeight / 2f, paint);
        return src;
    }

    public static int dpToPx(Context context, int dp) {
//        int densityDpi = context.getResources().getDisplayMetrics().densityDpi;
//        int densityDefault = DisplayMetrics.DENSITY_DEFAULT;
//        return (int) Math.round(dp * (densityDpi * 1.0 / densityDefault));
        return ScreenUtil.dp2px(dp);
    }


    public static Bitmap generateDolphinMusicWidgetProgress(Context context, int duration, int progress, int backgroundId, int startColor, int endColor, int canvasWidth, int canvasHeight, int ringOutSize, int ringStrokeWidth) {
        Log.i(TAG, "progress:" + progress + "/" + duration);
        Canvas canvas = new Canvas();
        Paint paint = new Paint(Paint.ANTI_ALIAS_FLAG);
        paint.setDither(true);
        paint.setAntiAlias(true);
        paint.setStyle(Paint.Style.STROKE);
        paint.setStrokeWidth(ringStrokeWidth);


        Bitmap bitmap = Bitmap.createBitmap(canvasWidth, canvasHeight, Bitmap.Config.ARGB_8888);
        canvas.setBitmap(bitmap);

        //画底图
        Drawable background = context.getDrawable(backgroundId);
//        int width = background.getIntrinsicWidth();
//        int height = background.getIntrinsicHeight();
        background.setBounds(0, 0, canvasWidth, canvasHeight);
        background.draw(canvas);


        //画圆环
        //角度
        float angle = (float) 360 * progress / duration;
        //渐变器
        SweepGradient mSweepGradient = new SweepGradient(canvasWidth / 2,
                canvasHeight / 2,
                new int[]{startColor, endColor},
                new float[]{0f, (float) progress / duration});
        //旋转渐变
        Matrix matrix = new Matrix();
        matrix.setRotate(-90f, canvasWidth / 2, canvasHeight / 2);
        mSweepGradient.setLocalMatrix(matrix);
        paint.setShader(mSweepGradient);
        RectF rect = new RectF(canvasWidth / 2 - ringOutSize / 2 + ringStrokeWidth / 2,
                canvasHeight / 2 - ringOutSize / 2 + ringStrokeWidth / 2,
                canvasWidth - (canvasWidth / 2 - ringOutSize / 2 + ringStrokeWidth / 2),
                canvasHeight - (canvasHeight / 2 - ringOutSize / 2 + ringStrokeWidth / 2));

        canvas.drawArc(rect,
                -90, angle, false, paint);


        //画圆环结束的头部圆圈
        angle = 90f - angle; //抵消屏幕坐标系差异
        //转换为弧度
        float radian = (float) (Math.PI / 180f * angle);
        //定义头部画笔
        Paint minCirclePaint = new Paint();
        minCirclePaint.setAntiAlias(true);
        minCirclePaint.setColor(endColor);
        float a = rect.top,
                b = rect.left,
                c = rect.width(),
                d = rect.height();
        canvas.drawCircle((float) (b + c / 2f * (Math.cos(radian) + 1)),
                (float) (a + -1 * d / 2f * (Math.sin(radian) - 1)),
                ringStrokeWidth / 2f, minCirclePaint);
        return bitmap;
    }
}
