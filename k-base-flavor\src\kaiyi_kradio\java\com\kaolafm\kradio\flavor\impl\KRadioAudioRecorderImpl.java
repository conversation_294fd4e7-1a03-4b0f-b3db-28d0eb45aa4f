package com.kaolafm.kradio.flavor.impl;

import com.kaolafm.kradio.lib.base.flavor.KRadioAudioRecorderInter;
import com.kaolafm.kradio.lib.base.flavor.live.KradioRecorderInterface;



/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-07-05 12:16
 ******************************************/
public final class KRadioAudioRecorderImpl implements KRadioAudioRecorderInter {

    @Override
    public boolean initVR(Object... args) {
        return false;
    }

    @Override
    public boolean onAudioRecordStart(Object... args) {
        return true;
    }

    @Override
    public boolean onAudioRecordStop(Object... args) {
        return false;
    }

    @Override
    public void setVrStatusListener(OnVRStatusListener onVRStatusListener) {

    }

    @Override
    public KradioRecorderInterface getRecorder() {
        return new KaiyiAudioRecorderImpl();
    }

}
