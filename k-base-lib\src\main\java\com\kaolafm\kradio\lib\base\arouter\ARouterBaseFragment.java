package com.kaolafm.kradio.lib.base.arouter;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.kaolafm.kradio.lib.event.PopFragmentEBData;

import org.greenrobot.eventbus.EventBus;

import me.yokeyword.fragmentation.SupportFragment;

/**
 * created by ya<PERSON><PERSON><PERSON> on 2018/4/11 21:13
 * KaolaFM
 * //在 这里 初始化
 */
public abstract class ARouterBaseFragment extends SupportFragment {
    private static final String TAG = "ARouterBaseFragment";

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "-------------------onCreate: " + this.getClass().getSimpleName()+"---------------------------");
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        Log.d(TAG, "-------------------onCreateView: " + this.getClass().getSimpleName()+"---------------------------");
        return super.onCreateView(inflater, container, savedInstanceState);

    }

    @Override
    public void onStart() {
        super.onStart();
        Log.d(TAG, "-------------------onStart: " + this.getClass().getSimpleName()+"---------------------------");
    }

    @Override
    public void onResume() {
        super.onResume();
        Log.d(TAG, "-------------------onResume: " + this.getClass().getSimpleName()+"---------------------------");
    }

    @Override
    public void onStop() {
        super.onStop();
        Log.d(TAG, "-------------------onStop: " + this.getClass().getSimpleName()+"---------------------------");
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        Log.d(TAG, "-------------------onDestroyView: " + this.getClass().getSimpleName()+"---------------------------");
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "-------------------onDestroy: " + this.getClass().getSimpleName()+"---------------------------");
    }

    //跳转到其它frag
    public void addFrag(ARouterBaseFragment frag) {
        extraTransaction().start(frag);
    }

    public void addFrag(ARouterBaseFragment frag, int flag) {
        extraTransaction().start(frag, flag);
    }

    /**
     * 退出fragment,并且回到桌面跳转前的状态
     */
    public void removeFrag(ARouterBaseFragment frag) {
        //这里laucheractivity因为有回退恢复逻辑，需要特殊处理，普通activity直接pop
        ARouterBaseActivity act = (ARouterBaseActivity) getActivity();
        Log.i(TAG, "removeFrag start frag = " + frag);
        if (act != null && act.isLauncherActivity()) {
            Log.i(TAG, "removeFrag activity pop");
            EventBus.getDefault().post(new PopFragmentEBData());
        } else {
            Log.i(TAG, "removeFrag fragment pop");
            pop();
        }
    }

    //LauncherActivity destroy时候调用，负责释放资源
    public boolean onDestroyApp() {
        return true;
    }
}
