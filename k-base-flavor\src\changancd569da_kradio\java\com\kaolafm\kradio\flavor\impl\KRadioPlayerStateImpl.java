package com.kaolafm.kradio.flavor.impl;

import android.util.Log;

import com.kaolafm.kradio.lib.base.flavor.KRadioPlayerStateInter;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;

public class KRadioPlayerStateImpl implements KRadioPlayerStateInter {
    private static final String TAG = KRadioPlayerStateImpl.class.getSimpleName();
    @Override
    public boolean isPlaying() {
        boolean isPlaying = PlayerManagerHelper.getInstance().isPlaying();
        Log.i(TAG, "isPlaying = " + isPlaying);
        return isPlaying;
    }
}
