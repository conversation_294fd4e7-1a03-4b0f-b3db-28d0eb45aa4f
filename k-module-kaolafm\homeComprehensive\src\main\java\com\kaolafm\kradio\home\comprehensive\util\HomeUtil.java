package com.kaolafm.kradio.home.comprehensive.util;

import java.util.List;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: ListUtil.java
 *                                                                  *
 * Created in 2018/9/28 下午3:47                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
public class HomeUtil {

    /**
     * 剔除null元素
     *
     * @param list
     */
    public static void trimNull(List list) {
        if (list != null && !list.isEmpty()) {
            for (int i = list.size() - 1; i >= 0; i--) {
                if (list.get(i) == null) {
                    list.remove(i);
                }
            }
        }
    }


    /**
     * 类型转换异常处理,获取id
     *
     * @param str
     * @return
     */
    public static long getLongMaybeError(String str) {
        long id;
        try {
            id = Long.valueOf(str);
        } catch (NumberFormatException e) {
            id = -1L;
        }
        return id;
    }

    /**
     * 类型转换异常处理,获取id
     *
     * @param str
     * @return
     */
    public static long getLongMaybeError(String str, long defaultValue) {
        long id;
        try {
            id = Long.valueOf(str);
        } catch (NumberFormatException e) {
            id = defaultValue;
        }
        return id;
    }

    /**
     * 类型转换异常处理,获取int
     *
     * @param str
     * @return
     */
    public static int getIntMaybeError(String str) {
        int id;
        try {
            id = Integer.valueOf(str);
        } catch (NumberFormatException e) {
            id = -1;
        }
        return id;
    }

    /**
     * 类型转换异常处理,获取id
     *
     * @param str
     * @return
     */
    public static int getIntMaybeError(String str, int defaultValue) {
        int id;
        try {
            id = Integer.valueOf(str);
        } catch (NumberFormatException e) {
            id = defaultValue;
        }
        return id;
    }

    /**
     * 检查索引是否越界,并返回可用索引
     *
     * @param list
     * @param index
     * @return
     */
    public static int checkIndex(List<String> list, int index) {
        int rstIndex = index;
        if (list == null || list.isEmpty()) {
            rstIndex = 0;
        } else {
            int size = list.size();
            if (index < 0) {
                rstIndex = 0;
            }
            if (index >= size) {
                rstIndex = size - 1;
            }
        }
        return rstIndex;
    }

}
