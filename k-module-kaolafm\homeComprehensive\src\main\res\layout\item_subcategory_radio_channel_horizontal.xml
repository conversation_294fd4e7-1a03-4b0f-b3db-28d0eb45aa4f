<?xml version="1.0" encoding="utf-8"?>
<com.kaolafm.kradio.lib.widget.square.SquareHeightFrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:siv="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="match_parent">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.kaolafm.kradio.lib.widget.square.SquareImageView
            android:id="@+id/iv_cover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:contentDescription="@null"
            siv:canScale="false" />

        <View
            android:id="@+id/view_radio_mongolian"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/category_item_layer_meng_normal" />


        <ViewStub
            android:id="@+id/vs_layout_playing"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout="@layout/layout_playing_square_item" />

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_margin="@dimen/m12"
            android:ellipsize="end"
            android:gravity="center"
            android:maxEms="12"
            android:maxLines="@integer/sub_ctg_max_lines"
            android:textColor="@color/category_item_radio_title_text_color"
            android:textSize="@dimen/subcategory_item_radio_text_size"
            tool:text="箱底私藏!迷醉于电音质感女嗓" />

        <RelativeLayout
            android:id="@+id/layout_offline"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/offline_layer"
            android:visibility="gone">

            <ImageView
                android:layout_width="@dimen/x100"
                android:layout_height="@dimen/y56"
                android:layout_centerInParent="true"
                android:src="@drawable/offline" />
        </RelativeLayout>
    </RelativeLayout>


</com.kaolafm.kradio.lib.widget.square.SquareHeightFrameLayout>
