package com.kaolafm.kradio.flavor.impl;

import android.content.Context;

import com.kaolafm.kradio.lib.base.flavor.KRadioThirdPlatformInitInter;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-07-15 15:16
 ******************************************/
public class KRadioThirdPlatformInitImpl implements KRadioThirdPlatformInitInter {
    @Override
    public boolean initThirdPlatform(Object... args) {
        RiChanHelper.getInstance((Context) args[0]).init();
        return true;
    }

    @Override
    public boolean destroyThirdPlatform(Object... args) {
        return false;
    }
}
