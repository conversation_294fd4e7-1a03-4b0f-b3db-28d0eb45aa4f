package com.kaolafm.kradio.flavor.impl;

import com.kaolafm.kradio.lib.base.flavor.RequestOptions;
import com.kaolafm.opensdk.utils.BaseHttpsStrategy;

public class RequestOptionsImpl implements RequestOptions {

    class ChannelHttpsStrategy extends BaseHttpsStrategy{
        @Override
        public void updateChannelHttpsStrategy() {
            this.mHttpsMap.put("getHost", true);
            this.mHttpsMap.put("replaceUrl", true);
            this.mHttpsMap.put("adParam", true);
            this.mHttpsMap.put("media", false);
            this.mHttpsMap.put("httpsPort", false);
        }
    }
    @Override
    public BaseHttpsStrategy getHttpsStrategy() {
        return new ChannelHttpsStrategy();
    }
}
