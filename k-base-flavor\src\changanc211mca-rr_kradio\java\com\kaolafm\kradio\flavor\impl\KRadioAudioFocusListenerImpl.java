package com.kaolafm.kradio.flavor.impl;

import android.util.Log;

import com.incall.serversdk.source.SourceMngProxy;
import com.incall.serversdk.source.SourceType;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioFocusListenerInter;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import static android.media.AudioManager.AUDIOFOCUS_GAIN;
import static android.media.AudioManager.AUDIOFOCUS_LOSS;
import static android.media.AudioManager.AUDIOFOCUS_LOSS_TRANSIENT;

public class KRadioAudioFocusListenerImpl implements KRadioAudioFocusListenerInter {
    @Override
    public void onFocusChanged(int status) {
        Log.d("KRadioFocusListenerImpl", " onFocusChanged：" + status);
        if (status == AUDIOFOCUS_LOSS || status == AUDIOFOCUS_LOSS_TRANSIENT) {
            if (PlayerManager.getInstance().isPlaying()) {
                Log.d("KRadioFocusListenerImpl", " 播放中暂停");
                PlayerManager.getInstance().pause(); //强制暂停下
            } else {
                boolean isPauseFromUser = PlayerManager.getInstance().isPauseFromUser();
                Log.d("KRadioFocusListenerImpl", " 已暂停，再走一次，" + isPauseFromUser);
                PlayerManager.getInstance().pause(isPauseFromUser);
            }
        } else if (status == AUDIOFOCUS_GAIN) {
            if (!PlayerManager.getInstance().isPlaying() && !PlayerManager.getInstance().isPauseFromUser()) {
                Log.d("KRadioFocusListenerImpl", " 未播放且非用户暂停");
                try {
                    if (PlayerManager.getInstance().getCurPlayItem().isLiving()) {
                        Log.d("KRadioFocusListenerImpl", " 开始播放");
                        PlayerManager.getInstance().play();
                    }
                } catch (Exception e) {
                    Log.d("KRadioFocusListenerImpl", " 播放失败");
                }
            }
            SourceMngProxy.getInstance().requestSource(SourceType.ONLINE_AUDIO);
        }
    }
}
