package com.kaolafm.kradio.user.channel;

import android.text.TextUtils;
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.kradio.lib.utils.KaolaAppConfigData;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/4/19
 */
public class SwitchChannelPresent extends BasePresenter<SwitchChannelModel, ISwitchChannelView> {

    public SwitchChannelPresent(ISwitchChannelView view) {
        super(view);
    }

    @Override
    public void start() {
        super.start();
        mModel.getChannels(new HttpCallback<List<Channel>>() {
            @Override
            public void onSuccess(List<Channel> channels) {
                if (mView != null) {
                    mView.showChannels(channels);
                }
                showCurrentChannel(channels);
            }

            @Override
            public void onError(ApiException e) {

            }
        });
    }

    private void showCurrentChannel(List<Channel> channels) {
        String appId = KaolaAppConfigData.getInstance().getAppId();
        for (int i=0, size = channels.size(); i < size; i++) {
            Channel channel = channels.get(i);
            if (TextUtils.equals(appId, channel.getAppId())) {
                 if (mView != null) {
                     mView.showCurrentChannel(channel, i);
                 }
                 return;
             }
        }
    }

    @Override
    protected SwitchChannelModel createModel() {
        return new SwitchChannelModel();
    }
}
