<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/comprehensive_live_gift_box_height"
    android:gravity="center"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/m2"
    android:background="@drawable/comprehensive_live_bottom_bg">

    <!--分页显示显示-->
    <LinearLayout
        android:id="@+id/gift_viewpager"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <androidx.viewpager.widget.ViewPager
            android:id="@+id/gift_pagers"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center" />

        <LinearLayout
            android:id="@+id/dots_container"
            android:layout_width="match_parent"
            android:layout_height="20dp"
            android:layout_gravity="center"
            android:gravity="center"
            android:orientation="horizontal"></LinearLayout>
    </LinearLayout>

    <!--列表显示-->
    <LinearLayout
        android:visibility="gone"
        android:id="@+id/gift_recyclerview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_gift"
            android:layout_marginTop="@dimen/y32"
            android:layout_marginBottom="@dimen/y48"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </LinearLayout>

    <View
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_width="@dimen/m50"
        android:layout_height="match_parent"
        android:background="@drawable/live_pannel_dialog_container_mask"/>

</androidx.constraintlayout.widget.ConstraintLayout>