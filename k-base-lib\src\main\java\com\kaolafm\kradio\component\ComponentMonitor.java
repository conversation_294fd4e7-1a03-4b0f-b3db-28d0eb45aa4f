package com.kaolafm.kradio.component;

import android.app.Activity;
import androidx.lifecycle.Lifecycle.Event;
import androidx.lifecycle.LifecycleObserver;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.OnLifecycleEvent;
import android.os.SystemClock;
import androidx.fragment.app.Fragment;
import android.util.Log;
import java.lang.ref.WeakReference;
import java.util.Collection;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 组件监控类
 *
 * <AUTHOR>
 * @date 2019-07-04
 */
class ComponentMonitor {

    /**
     * RealCaller的容器。
     */
    private static final Map<String, RealCaller> CALLER_MAPS = new ConcurrentHashMap<>();
    private static volatile long minTimeoutAt = Long.MAX_VALUE;
    private static final byte[] LOCK = new byte[0];
    private static final AtomicBoolean STOPPED = new AtomicBoolean(true);
    /**
     * 添加监控
     *
     * @param caller 需要监控的caller
     */
    public static void addMonitor(RealCaller caller) {
        if (caller != null) {
            CALLER_MAPS.put(caller.getCallId(), caller);
            setTimeout(caller.timeoutAt());
            addLifecycleObserver(caller.client());
        }
    }

    /**
     * 使用rxlifecycle自动销毁
     * @param client
     */
    private static void addLifecycleObserver(ComponentClient client) {
        WeakReference<Activity> activityWeakReference = client.cancelOnDestroyActivity();
        if (activityWeakReference != null) {
            Activity activity = activityWeakReference.get();
            if (activity instanceof LifecycleOwner) {
                activity.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        ((LifecycleOwner) activity).getLifecycle().addObserver(new CancelLifecycleObserver());
                    }
                });
            }
        }
        WeakReference<Fragment> fragmentWeakReference = client.cancelOnDestroyFragment();
        if (fragmentWeakReference != null) {
            Fragment fragment = fragmentWeakReference.get();
            if (fragment != null) {
                fragment.getLifecycle().addObserver(new CancelLifecycleObserver());
            }
        }
    }

    /**
     * 设置超时时间。默认异步没有超时，同步2000ms。
     * @param timeoutAt
     */
    private static void setTimeout(long timeoutAt) {
        if (timeoutAt > 0) {
            if (minTimeoutAt > timeoutAt) {
                minTimeoutAt = timeoutAt;
                //如果最小timeout时间有变化，且监控线程在wait，则唤醒监控线程
                synchronized (LOCK) {
                    LOCK.notifyAll();
                }
            }
            if (STOPPED.compareAndSet(true, false)) {
                new TimeoutMonitorThread().start();
            }
        }
    }

    /**
     * 根据callId移除监控
     *
     * @param callId caller的id
     */
    public static void removeMonitor(String callId) {
        CALLER_MAPS.remove(callId);
    }

    /**
     * 根据id获取正在监控的caller
     *
     * @param callId caller的id
     * @return 正在监控的caller
     */
    static RealCaller getCallerById(String callId) {
        return CALLER_MAPS.get(callId);
    }

    /**
     * 超时监控线程
     */
    private static class TimeoutMonitorThread extends Thread {
        @Override
        public void run() {
            if (STOPPED.get()) {
                return;
            }
            while(CALLER_MAPS.size() > 0 || minTimeoutAt == Long.MAX_VALUE) {
                try {
                    long millis = minTimeoutAt - SystemClock.elapsedRealtime();
                    if (millis > 0) {
                        synchronized (LOCK) {
                            LOCK.wait(millis);
                        }
                    }
                    long min = Long.MAX_VALUE;
                    long now = SystemClock.elapsedRealtime();
                    for (RealCaller caller : CALLER_MAPS.values()) {
                        if (!caller.isFinished()) {
                            long timeoutAt = caller.timeoutAt();
                            if (timeoutAt > 0) {
                                if (timeoutAt < now) {
                                    caller.timeout();
                                } else if (timeoutAt < min) {
                                    min = timeoutAt;
                                }
                            }
                        }
                    }
                    minTimeoutAt = min;
                } catch (InterruptedException ignored) {

                }
            }
            STOPPED.set(true);
        }
    }

    /**
     * 在activity销毁时取消。在{@link com.kaolafm.kradio.lib.base.lifecycle.ActivityLifecycleForRxLifecycle}中自动销毁
     * @param activity
     */
    public static void cancelOnDestroy(Activity activity) {
        Collection<RealCaller> callers = CALLER_MAPS.values();
        for (RealCaller caller : callers) {
            WeakReference<Activity> destroyActivity = caller.client().cancelOnDestroyActivity();
            if (!caller.isFinished() && destroyActivity != null && caller.client().cancelOnDestroyActivity().get() == activity) {
                caller.cancelOnDestroy();
            }
        }
    }

    /**
     * 在fragment销毁时取消请求
     * @param fragment
     */
    public static void cancelOnDestroy(Fragment fragment) {
        Collection<RealCaller> callers = CALLER_MAPS.values();
        for (RealCaller caller : callers) {
            if (!caller.isFinished() && caller.client().cancelOnDestroyFragment() != null && caller.client().cancelOnDestroyFragment().get() == fragment) {
                caller.cancelOnDestroy();
            }
        }
    }

    /**
     * 用于自动取消组件调用的生命周期监听。会在Activity或Fragment销毁的时候取消。
     */
    private static class CancelLifecycleObserver implements LifecycleObserver {

        @OnLifecycleEvent(Event.ON_DESTROY)
        void onDestroy(LifecycleOwner owner) {
            if (owner instanceof Activity) {
                ComponentMonitor.cancelOnDestroy((Activity) owner);
            }else if (owner instanceof Fragment) {
                ComponentMonitor.cancelOnDestroy((Fragment) owner);
            }
            Log.i("CancelLifecycleObserver", "由于"+owner.getClass().getSimpleName()+"销毁而取消组件调用");
            owner.getLifecycle().removeObserver(this);
        }
    }


}
