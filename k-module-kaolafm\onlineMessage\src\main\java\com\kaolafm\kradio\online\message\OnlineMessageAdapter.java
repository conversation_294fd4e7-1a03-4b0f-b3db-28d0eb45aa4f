package com.kaolafm.kradio.online.message;

import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.kradio.k_kaolafm.R; 
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.utils.DateUtil;
import com.kaolafm.kradio.basedb.entity.meaasge.CrashMessageBean;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.online.player.utils.LogUtil;
 

/**
 * 消息盒子适配器 插播占位
 */
public class OnlineMessageAdapter extends BaseAdapter<CrashMessageBean> {
    @Override
    protected BaseHolder<CrashMessageBean> getViewHolder(ViewGroup parent, int viewType) {
        return new OnlineMessageViewHolder(inflate(parent, R.layout.online_msg_item_adapter, viewType));
    }

    public class OnlineMessageViewHolder extends BaseHolder<CrashMessageBean> {
    
        ImageView msgItemPic; 
        TextView msgItemTitle; 
        TextView msgItemTime; 
        TextView msgItemContent;

        public OnlineMessageViewHolder(View itemView) {
            super(itemView);
            msgItemPic=itemView.findViewById(R.id.msg_item_pic);
            msgItemTitle=itemView.findViewById(R.id.msg_item_title);
            msgItemTime=itemView.findViewById(R.id.msg_item_time);
            msgItemContent=itemView.findViewById(R.id.msg_item_content);
        }

        @Override
        public void setupData(CrashMessageBean onlineMessageBean, int position) {
            msgItemTitle.setText(onlineMessageBean.getTipsTitle());
            try {
                msgItemTime.setText(DateUtil.formatMillis("yyyy-MM-dd HH:mm", Long.parseLong(onlineMessageBean.getSendTime())));
            } catch (Exception e) {
                LogUtil.e(e.getMessage());
            }
            msgItemContent.setText(onlineMessageBean.getEventDescription());
            int resourceId = 0;
            switch (onlineMessageBean.getMsgLevel()) {
//            resourceId = R.drawable.online_message_bubble_warning;黄色预留，一期不做
                case "1"://情感化问候
                    resourceId = R.drawable.online_message_blue_icon;
                    break;
                case "2"://节目预约、社交 、礼物等
                    resourceId = R.drawable.online_message_green_icon;
                    break;
                case "3"://应急广播消息
//                    switch (onlineMessageBean.getEventLevel()) {
//                        case "1":
                            resourceId = R.drawable.online_message_red_iconr;
//                            break;
//                        case "2":
//                            resourceId = R.drawable.online_message_bubble_cheng;
//                            break;
//                        case "3":
//                            resourceId = R.drawable.online_message_bubble_huang;
//                            break;
//                        case "4":
//                            resourceId = R.drawable.online_message_bubble_lan;
//                            break;
//                    }
                    break;
            }

            if (resourceId != 0) {
                msgItemPic.setImageResource(resourceId);
            }
            if (onlineMessageBean.isLook()) {
                //已读消息
                msgItemTitle.setTextColor(ResUtil.getColor(R.color.online_message_item_title_color));
                msgItemTime.setTextColor(ResUtil.getColor(R.color.online_message_item_title_color));
                msgItemContent.setTextColor(ResUtil.getColor(R.color.online_message_item_title_color));
            } else {
                //未读消息
                msgItemTitle.setTextColor(ResUtil.getColor(R.color.online_message_item_title_color2));
                msgItemTime.setTextColor(ResUtil.getColor(R.color.online_message_item_title_color2));
                msgItemContent.setTextColor(ResUtil.getColor(R.color.online_message_item_content_color));
            }
        }
    }
}
