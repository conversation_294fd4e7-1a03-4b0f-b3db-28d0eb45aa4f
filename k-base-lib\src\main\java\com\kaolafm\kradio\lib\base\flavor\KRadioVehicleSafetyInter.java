package com.kaolafm.kradio.lib.base.flavor;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-01-17 20:11
 ******************************************/
public interface KRadioVehicleSafetyInter {
    /**
     * 启动检测
     *
     * @param args
     * @return true 启动成功，false 启动失败
     */
    boolean startCheck(Object... args);

    /**
     * 停止检测
     *
     * @param args
     * @return true 停止成功，false 停止失败
     */
    boolean stopCheck(Object... args);

    /**
     * 恢复检测
     *
     * @param args args[0] = KRadioVehicleSafetyCallback or args[0] = null!
     * @return true 恢复成功，false 恢复失败
     */
    boolean resumeCheck(Object... args);

    /**
     * 注册检测用户当前是否被允许回调
     *
     * @param kRadioVehicleSafetyCallback
     */
    void registerVehicleSafetyCheckCallback(KRadioVehicleSafetyCallback kRadioVehicleSafetyCallback);

    /**
     * 注销检测用户当前是否被允许回调
     *
     * @param kRadioVehicleSafetyCallback
     */
    void unregisterVehicleSafetyCheckCallback(KRadioVehicleSafetyCallback kRadioVehicleSafetyCallback);
}
