<?xml version="1.0" encoding="utf-8"?>
<com.kaolafm.kradio.lib.widget.ratio.RatioConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rcl_item_home_content"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clickable="false"
    android:focusable="false"
    app:wh_ratio="0.7:1">

    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/card_big_bottom" />

    <com.kaolafm.kradio.lib.widget.square.SquareLayout
        android:id="@+id/sv_item_home_place"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_item_home_cover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="1dp"
            android:scaleType="centerCrop" />

        <View
            android:id="@+id/view_item_home_cover_bg"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/card_big_fg" />
    </com.kaolafm.kradio.lib.widget.square.SquareLayout>

    <View
        android:id="@+id/view_item_home_text_bg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@drawable/card_big_bottom"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/sv_item_home_place" />

    <TextView
        android:id="@+id/tv_item_home_function_top"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:ellipsize="end"
        android:gravity="center"
        android:maxEms="6"
        android:maxLines="2"
        android:paddingStart="@dimen/x8"
        android:paddingEnd="@dimen/y8"
        android:paddingBottom="@dimen/y10"
        android:textColor="@color/card_big_title_text_color"
        android:textSize="@dimen/text_size4_vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="w,3:7"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        tools:text="二货一箩筐二货一箩筐二货一箩筐一箩二货一箩筐一箩筐" />

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_item_home_function_bottom"
        android:visibility="gone"
        android:text="占位用"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>
</com.kaolafm.kradio.lib.widget.ratio.RatioConstraintLayout>
