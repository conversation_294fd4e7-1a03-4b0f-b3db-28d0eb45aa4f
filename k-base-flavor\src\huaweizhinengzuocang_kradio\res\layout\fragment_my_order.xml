<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_home"
    android:paddingTop="@dimen/y80">

    <ImageView
        android:id="@+id/ivBack"
        style="@style/FragmentBackButton"
        tools:ignore="MissingConstraints" />

    <TextView
        android:id="@+id/my_order_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:text="@string/my_order"
        android:textColor="@color/global_title_text_color"
        android:textSize="@dimen/text_size6"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/ivBack"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/ivBack" />

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/color_common_line"
        app:layout_constraintTop_toBottomOf="@+id/my_order_tv"
        android:layout_marginTop="@dimen/y29"
        tools:ignore="MissingConstraints" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/my_order_item_start"
        android:layout_marginEnd="@dimen/my_order_item_end"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line"
        tools:listitem="@layout/item_order" />

    <TextView
        android:id="@+id/tvHelp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/y49"
        android:text="@string/order_help"
        android:textColor="#6C7190"
        android:textSize="@dimen/text_size3"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <ViewStub
        android:id="@+id/vsErrorLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout="@layout/home_no_network_rl" />
</androidx.constraintlayout.widget.ConstraintLayout>