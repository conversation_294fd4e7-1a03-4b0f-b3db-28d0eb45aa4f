package com.kaolafm.kradio.component.ui.base.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.PaintFlagsDrawFilter;
import android.graphics.Path;
import android.graphics.RectF;
import android.os.Build;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;

import com.kaolafm.kradio.component.ui.R;

import skin.support.widget.SkinCompatImageView;

public class OvalImageView extends SkinCompatImageView {
    public static final int TYPE_ALL = 0;
    public static final int TYPE_ONLY_TOP_LEFT = 1;
    public static final int TYPE_ONLY_BOTTOM = 2;
    public static final int TYPE_ONLY_TOP = 3;
    public static final int TYPE_ONLY_LEFT = 4;
    public static final int TYPE_ONLY_RIGHT = 5;
    private float[] rids;
    private Paint m_paint;
    private int ridType = TYPE_ALL;
    private float radius;
    private int boderColor = 0xffe6e6e6;
    private float edgeWidth = 2;
    private boolean hasBoder = true;
    private boolean isCircle;
    private int wRatio = -1;
    private int hRatio = -1;
    private Path cornerPath;
    private Path borderPath;

    private int lastW, lastH;

//    PaintFlagsDrawFilter filter = new PaintFlagsDrawFilter(0, Paint.ANTI_ALIAS_FLAG | Paint.FILTER_BITMAP_FLAG);

    public OvalImageView(Context context) {
        super(context);
        initView();
    }

    public OvalImageView(Context context, AttributeSet attrs) {
        super(context, attrs);
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.OvalImageView);
        ridType = typedArray.getInt(R.styleable.OvalImageView_rid_type, TYPE_ALL);
        isCircle = typedArray.getBoolean(R.styleable.OvalImageView_circle, false);
        hasBoder = typedArray.getBoolean(R.styleable.OvalImageView_has_boder, false);
        radius = typedArray.getDimension(R.styleable.OvalImageView_oval_radius, context.getResources().getDimension(R.dimen.default_radius_img));
        boderColor = typedArray.getColor(R.styleable.OvalImageView_color_edge, 0xffe6e6e6);
        edgeWidth = typedArray.getDimension(R.styleable.OvalImageView_width_edge, 0);
        wRatio = typedArray.getColor(R.styleable.OvalImageView_wRatio, -1);
        hRatio = typedArray.getColor(R.styleable.OvalImageView_hRatio, -1);
        typedArray.recycle();
        initView();
    }

    public void setIsCircle(boolean circle) {
        isCircle = circle;
        postInvalidate();
    }

    public void setBoderColor(int color) {
        boderColor = color;
        m_paint.setColor(boderColor);
        postInvalidate();
    }

    private void initView() {
//        setLayerType(LAYER_TYPE_SOFTWARE, null);
        setScaleType(ImageView.ScaleType.CENTER_CROP);
        m_paint = new Paint();
        m_paint.setAntiAlias(true);
        m_paint.setFlags(Paint.ANTI_ALIAS_FLAG);
        m_paint.setStyle(Paint.Style.STROKE);
        m_paint.setStrokeWidth(edgeWidth);
        m_paint.setColor(boderColor);
//        m_paint.setColor(0xffff0000);

        setRadius(radius);
    }

    public void setRadius(float radius) {
        this.radius = radius;
        switch (ridType) {
            case TYPE_ALL:
                rids = new float[]{radius, radius, radius, radius, radius, radius, radius, radius};
                break;
            case TYPE_ONLY_TOP_LEFT:
                rids = new float[]{radius, radius, 0, 0, 0, 0, 0, 0};
                break;
            case TYPE_ONLY_TOP:
                rids = new float[]{radius, radius, radius, radius, 0, 0, 0, 0};
                break;
            case TYPE_ONLY_BOTTOM:
                rids = new float[]{0, 0, 0, 0, radius, radius, radius, radius};
                break;
            case TYPE_ONLY_LEFT:
                rids = new float[]{radius, radius, 0, 0, 0, 0, radius, radius};
                break;
            case TYPE_ONLY_RIGHT:
                rids = new float[]{0, 0, radius, radius, radius, radius, 0, 0};
                break;
        }
    }

    //    @Override
//    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
//        int width = MeasureSpec.getSize(widthMeasureSpec);
//        int height = MeasureSpec.getSize(heightMeasureSpec);
//        setMeasuredDimension(width, height);
//    }

    /**
     * 画图
     *
     * @param canvas
     */
    protected void onDraw(Canvas canvas) {
        //禁用硬件加速
//        setLayerType(View.LAYER_TYPE_SOFTWARE, null);
        canvas.clipPath(getCornerPath());
//        canvas.setDrawFilter(filter);
        super.onDraw(canvas);
        if (hasBoder) {
//            if (TYPE_ALL == ridType || TYPE_ONLY_TOP == ridType) {
//            canvas.drawPath(path, m_paint);//小米 绘制gif有白边
            if (isCircle) {
                int w = this.getWidth();
                int h = this.getHeight();
                canvas.drawCircle(w / 2f, h / 2f, w / 2f, m_paint);
            } else {
                canvas.drawPath(getBorerPath(), m_paint);
            }
//            }
        }
    }

    private Path getCornerPath() {
        if (cornerPath == null) {
            int w = this.getWidth();
            int h = this.getHeight();
            cornerPath = new Path();
            if (isCircle) {
                cornerPath.addCircle(w / 2f, h / 2f, w / 2f, Path.Direction.CW);
            } else {
                cornerPath.addRoundRect(new RectF(0, 0, w, h), rids, Path.Direction.CW);
            }
            lastH = h;
            lastW = w;
        }
        return cornerPath;
    }

    private Path getBorerPath() {
        if (borderPath == null) {
            int w = this.getWidth();
            int h = this.getHeight();
            borderPath = new Path();
            borderPath.arcTo(new RectF(0, 0, 2 * radius, 2 * radius), 180, 90);
            borderPath.arcTo(new RectF(w - 2 * radius, 0, w, 2 * radius), 270, 90);
            borderPath.arcTo(new RectF(w - 2 * radius, h - 2 * radius, w, h), 0, 90);
            borderPath.arcTo(new RectF(0, h - 2 * radius, 2 * radius, h), 90, 90);
            borderPath.close();
        }
        return borderPath;
    }

    public void setWHRatio(int wRatio, int hRatio) {
        this.wRatio = wRatio;
        this.hRatio = hRatio;
    }

    private void checkSizeChange() {
        int w = getMeasuredWidth();
        int h = getMeasuredHeight();
        if (cornerPath != null) {//横竖屏切换尺寸会变化
            if (w != lastW || lastH != h) {
                cornerPath = null;
                borderPath = null;
                lastW = w;
                lastH = h;
            }
        }
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        checkSizeChange();
        if (wRatio != -1 && hRatio != -1) {
            final int widthSize = View.MeasureSpec.getSize(widthMeasureSpec);
//            final int heightSize = MeasureSpec.getSize(heightMeasureSpec);
            setMeasuredDimension(widthSize, Math.round(((float) widthSize) * hRatio / wRatio));
        }
    }
}