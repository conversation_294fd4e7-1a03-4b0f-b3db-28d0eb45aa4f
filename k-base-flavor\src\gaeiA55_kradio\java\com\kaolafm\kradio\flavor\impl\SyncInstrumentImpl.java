package com.kaolafm.kradio.flavor.impl;

import android.annotation.SuppressLint;
import android.cluster.ClusterManager;
import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Bundle;
import androidx.annotation.IntDef;
import android.util.Log;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.SyncInstrumentInter;
import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.IPlayerStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

@SuppressLint("WrongConstant")
public class SyncInstrumentImpl implements SyncInstrumentInter, IPlayerStateListener {
    private static final String TAG = "SyncInstrumentImpl";

    private ClusterManager mClusterManager;
    private Context mContext;
    private String appName;
    private IPlayerInitCompleteListener onPlayerInitCompleteListener = new IPlayerInitCompleteListener() {
        @Override
        public void onPlayerInitComplete(boolean b) {
            try {
                PlayerManager.getInstance().removePlayerInitComplete(onPlayerInitCompleteListener);
                PlayerManager.getInstance().addPlayControlStateCallback(SyncInstrumentImpl.this);
                mClusterManager = ClusterManager.getInstance(AppDelegate.getInstance().getContext());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    };


    private void initPlayer() {
        PlayerManager playerManager = PlayerManager.getInstance();
        boolean isInitSuccess = playerManager.isPlayerInitSuccess();
        if (isInitSuccess) {
            try {
                PlayerManager.getInstance().addPlayControlStateCallback(SyncInstrumentImpl.this);
                mClusterManager = ClusterManager.getInstance(AppDelegate.getInstance().getContext());
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            playerManager.addPlayerInitComplete(onPlayerInitCompleteListener);
        }
    }

    @Override
    public boolean initSyncInstrument() {
        //初始化注册监听接口
        Log.i(TAG, "initSyncInstrument: ");
        mContext = AppDelegate.getInstance().getContext();
        mClusterManager = ClusterManager.getInstance(AppDelegate.getInstance().getContext());
        initPlayer();
        return true;
    }

    @Override
    public boolean releaseSyncInstrument() {
        //反注册监听接口
//        Log.i(TAG, "releaseSyncInstrument: ");
//        KLAutoPlayerManager.getInstance().removeIPlayerStateListener(this);
//        BroadcastRadioListManager.getInstance().removeOnPlayingRadioChangedListener(this);
        return true;
    }

    @Override
    public void onIdle(PlayItem playItem) {

    }

    @Override
    public void onPlayerPreparing(PlayItem playItem) {
        Log.i(TAG, "onPlayerPreparing: " + playItem.getAlbumTitle() + "      " + playItem.getTitle());
        sendTitle(playItem.getTitle());
        sendImg(playItem.getPicUrl());
        sendAppName();
    }

    @Override
    public void onPlayerPlaying(PlayItem playItem) {
        Log.i(TAG, "onPlayerPlaying: MEDAI_STATE_START ");
        sendStatus(ClusterManager.State.MEDAI_STATE_START);
    }

    @Override
    public void onPlayerPaused(PlayItem playItem) {
        Log.i(TAG, "onPlayerPaused: MEDAI_STATE_PAUSE ");
        sendStatus(ClusterManager.State.MEDAI_STATE_PAUSE);
    }

    @Override
    public void onProgress(PlayItem playItem, long mProgress, long mDuration) {
        PlayItem item = PlayerManager.getInstance().getCurPlayItem();
        if (item != null) {
            long progressTalSeconds = (mProgress + 500) / 1000;
            long durationTalSeconds = (mDuration + 500) / 1000;

            int progressHours = (int) progressTalSeconds / 3600;
            int progressMinutes = (int) (progressTalSeconds / 60) % 60;
            int progressSeconds = (int) progressTalSeconds % 60;

            int durationHours = (int) durationTalSeconds / 3600;
            int durationMinutes = (int) (durationTalSeconds / 60) % 60;
            int durationSeconds = (int) durationTalSeconds % 60;

            sendTime(progressHours, progressMinutes, progressSeconds, durationHours, durationMinutes, durationSeconds);
        }
    }

    @Override
    public void onPlayerFailed(PlayItem playItem, int i, int i1) {

    }

    @Override
    public void onPlayerEnd(PlayItem playItem) {

    }

    @Override
    public void onSeekStart(PlayItem playItem) {

    }

    @Override
    public void onSeekComplete(PlayItem playItem) {

    }

    @Override
    public void onBufferingStart(PlayItem playItem) {

    }

    @Override
    public void onBufferingEnd(PlayItem playItem) {

    }

    @Override
    public void onDownloadProgress(PlayItem playItem, long l, long l1) {

    }

    private String getAppName(Context mContext) {
        String appName = null;
        try {
            PackageManager packageManager = mContext.getPackageManager();
            PackageInfo packageInfo = packageManager.getPackageInfo(
                    mContext.getPackageName(), 0);
            appName = mContext.getResources().getString(packageInfo.applicationInfo.labelRes);
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        return appName;
    }

    //设置歌曲名称
    private void sendTitle(String title) {
        if (mClusterManager == null) {
            Log.i(TAG, "sendTitle: mClusterManager = " + mClusterManager);
            return;
        }
        Log.i(TAG, "sendTitle: " + title);
        Bundle data = new Bundle();
        data.putString(ClusterManager.Key.MEDIA_NAME, title);
        mClusterManager.sendCommand(ClusterManager.Cmd.ACU_MEDIA_NAME, data);
    }

    //通知歌曲进度及时长
    private void sendTime(int curHour, int curMin, int curSec, int totalHour, int totalMin, int totalSec) {
        if (mClusterManager == null) {
            Log.i(TAG, "sendTime: mClusterManager = " + mClusterManager);
            return;
        }
        Bundle data = new Bundle();
        data.putInt(ClusterManager.Key.CLUSTER_CUR_HOUR, curHour);
        data.putInt(ClusterManager.Key.CLUSTER_CUR_MINUTE, curMin);
        data.putInt(ClusterManager.Key.CLUSTER_CUR_SECOND, curSec);
        data.putInt(ClusterManager.Key.CLUSTER_TOTAL_HOUR, totalHour);
        data.putInt(ClusterManager.Key.CLUSTER_TOTAL_MINUTE, totalMin);
        data.putInt(ClusterManager.Key.CLUSTER_TOTAL_SECOND, totalSec);
        mClusterManager.sendCommand(ClusterManager.Cmd.ACU_MEDIA_TIME, data);
    }

    //通知封面图片
    private void sendImg(String img) {
        if (mClusterManager == null) {
            Log.i(TAG, "sendImg: mClusterManager = " + mClusterManager);
            return;
        }
        Log.i(TAG, "sendImg: " + img);
        Bundle data = new Bundle();
        data.putString(ClusterManager.Key.MEDIA_ALBUM_PATH, img);
        mClusterManager.sendCommand(ClusterManager.Cmd.ACU_MEDIA_ALBUM, data);
    }

    //通知播放状态
    private void sendStatus(@GaeiPlayStauts int status) {
        if (mClusterManager == null) {
            Log.i(TAG, "sendStatus: mClusterManager = " + mClusterManager);
            return;
        }
        Log.i(TAG, "sendStatus: " + status);
        mClusterManager.sendCommand(ClusterManager.Cmd.ACU_MEDIA_STATE, status);
    }

    //通知应用名称
    private void sendAppName() {
        if (mClusterManager == null) {
            return;
        }
        if (appName == null) {
            appName = getAppName(mContext);
        }
        Log.i(TAG, "sendAppName: " + appName);
        Bundle data = new Bundle();
        data.putString(ClusterManager.Key.MEDIA_APP_NAME, appName);
        mClusterManager.sendCommand(ClusterManager.Cmd.ACU_MEDIA_APP_NAME, data);
    }


    @Retention(RetentionPolicy.SOURCE)
    @IntDef({ClusterManager.State.MEDAI_STATE_START, ClusterManager.State.MEDAI_STATE_PAUSE})
    public @interface GaeiPlayStauts {
    }

}