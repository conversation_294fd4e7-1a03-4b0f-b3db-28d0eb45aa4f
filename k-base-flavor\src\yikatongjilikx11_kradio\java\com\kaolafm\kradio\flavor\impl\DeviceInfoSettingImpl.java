package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.util.Log;

import com.ecarx.sdk.device.DeviceAPI;
import com.kaolafm.kradio.flavor.utils.DeviceInfoUtil;
import com.kaolafm.kradio.lib.base.flavor.DeviceInfoSetting;

/******************************************
 * 类描述:
 *
 ******************************************/
public final class DeviceInfoSettingImpl implements DeviceInfoSetting {
    private static final String TAG = DeviceInfoSettingImpl.class.getSimpleName();

    @Override
    public void setInfoForSDK(Context context) {
        String deviceId = null;
        String carType = null;
        String mIHUID=null;
        String mOperatorName=null;
        try {
            if (null==DeviceInfoSettingASyncImpl.mDeviceAPI){
                return;
            }
            /**
             * 获取车辆 VIN
             * @return 车辆VIN
             * 用于唯一记录用户数据，便于用户移植数据到其他车机，具体方法如下:
             */
            deviceId = DeviceInfoSettingASyncImpl.mDeviceAPI.getOpenVIN();
            /**i
             * 获取车型信息 Vehicle Type (类PCODE)
             * @return 车型信息，比如 NL-3A
             */
            carType = DeviceInfoSettingASyncImpl.mDeviceAPI.getVehicleType();
            /**
             * 获取获取 IHUID（车机 ID）
             * @return 车机 ID
             */
            mIHUID=  DeviceInfoSettingASyncImpl.mDeviceAPI.getOpenIHUID();
            /**
             * 获取运营商标识名称, 比如："GEELY"，"LYNKCO"等
             * 如果获取失败，则返回{@link #OPERATOR_NAME_UNKNOWN}
             * @return 运营商标识名称，比如："GEELY"，"LYNKCO"等。
             */
            mOperatorName = DeviceInfoSettingASyncImpl.mDeviceAPI.getOperatorName();
        } catch (Exception e) {
            e.printStackTrace();
        }

//        deviceId="lW1nyAsWR4uCbjySj4TrRgCvnQtUc1792rYIBbCHMmZJ0/0Yac7TPL2N6lB/XxoCS5H4to+JT66WBI+kQzqVNKs7bVz+K0TxFZ4GSctoDk5L3+1bIaAqsdAnrKhQmwa0FrmWOD7bc/p0lfMT3GtJ2o15hBd4tdNWcP1d8NcFozQ=";
//        carType="02605G1";
        Log.d(TAG, "setInfoForSDK: deviceId==" + deviceId + ":===type====" + carType+"===mIHUID==="+mIHUID+"===mOperatorName==="+mOperatorName);
//        DeviceInfoUtil.setDeviceIdAndCarType(deviceId, carType);
    }
}
