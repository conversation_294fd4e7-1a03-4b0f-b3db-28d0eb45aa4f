package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.content.Context;
import androidx.fragment.app.Fragment;

import com.kaolafm.kradio.component.ComponentClient;
import com.kaolafm.kradio.constant.LiveComponentConst;
import com.kaolafm.kradio.lib.base.flavor.KRadioPlayerBarJumpLiveInter;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;

import me.yokeyword.fragmentation.SupportActivity;

/**
 * author : xb
 * date   : 2022/6/22
 * desc   :
 */
public class KRadioPlayerBarJumpLiveImpl implements KRadioPlayerBarJumpLiveInter {
    @Override
    public void jumpLive(Object... args) {

        SupportActivity activity = (SupportActivity) args[0];
        Fragment fragment = (Fragment) activity.getTopFragment();
        ComponentClient.obtainBuilder(LiveComponentConst.NAME)
                .setActionName(LiveComponentConst.START_HOME_LIVE)
                .cancelOnDestroyWith(activity)
                .addParam("liveId", args[1])
                .addParam("context", fragment)
                .build().callAsync();
    }
}
