// IMediaChangeCallBack.aidl
package com.iflytek.autofly.home.aidl;

// Declare any non-default types here with import statements
import com.iflytek.autofly.home.aidl.IMediaBean;
import com.iflytek.autofly.home.aidl.ILrcRow;
interface IMediaChangeCallBack {
      //控制播放状态  (电台需要传)
      //type:  1：音乐  2：有声读物   3：电台
     void sendMediaSatus( in int type,in int status);
     //播放列表音乐(电台需要传)
      //type:  1：音乐  2：有声读物   3：电台
     void playMusicByPosition(in int type,in int position);
     //播放状态 1：单曲循环  2：列表循环  3：随机播放
      //type:  1：音乐  2：有声读物   3：电台
     void sendMediaPlayModel(in int type,in int model);

     //拖动播放进度
      //type:  1：音乐  2：有声读物   3：电台
     boolean sendProgress(in int type,in int progress);
     //获得正在播放得歌曲列表  (电台需要传)
      //type:  1：音乐  2：有声读物   3：电台
     List<IMediaBean> getPlayingMediaBeanList(in int type);
     //获取歌词
      //type:  1：音乐  2：有声读物   3：电台
     List<ILrcRow> getPlayingMediaLrcList(in int type);
}
