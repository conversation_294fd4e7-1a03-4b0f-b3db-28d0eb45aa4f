package com.kaolafm.kradio.flavor.impl;

import android.content.Context;

import com.kaolafm.kradio.lib.utils.SystemPropertiesProxy;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-07-04 14:14
 ******************************************/
public final class DeviceInfoSettingImpl implements DeviceInfoSetting {
    @Override
    public void setInfoForSDK(Context context) {
        String carType = SystemPropertiesProxy.getString((Context) args[0], "car_brand_number");
        DeviceInfoUtil.setDeviceIdAndCarType(null, carType);
    }

}
