package com.kaolafm.kradio.online.player.mvp;

import com.kaolafm.kradio.live1.model.MessageBean;
import com.kaolafm.kradio.live1.mvp.HomeLiveView;

import java.util.List;

public interface OnlineHomeLiveView extends HomeLiveView {
    void onHistoryMessageReceived(List<MessageBean> param, boolean isOld);

    void onHistoryMessageQueryFailed(int code, Throwable exception);

    void enterChatRoomSuccess();

    void enterChatRoomFailed();
}
