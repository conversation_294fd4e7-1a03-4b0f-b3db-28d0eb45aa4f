package com.kaolafm.kradio.upgrader.net.model;


/******************************************
 * 类描述： 升级对象 类名称：UpdateData
 *
 * @version: 1.0
 * @author: shao<PERSON><PERSON>ang
 * @time: 2016-8-2 12:00
 ******************************************/
public class UpdateData {
    /**
     * 升级类型 0：不用升级 1：推荐升级 2:强制升级
     */
    private int updateType;
    /**
     * 升级对应版本号
     */
    private String updateVersion;
    /**
     * 升级信息描述
     */
    private String updateInfo;
    /**
     * 升级APK URL
     */
    private String updateUrl;
    /**
     * 最新APK版本号
     */
    private String versionCode;

    public int getUpdateType() {
        return updateType;
    }

    public void setUpdateType(int updateType) {
        this.updateType = updateType;
    }

    public String getUpdateVersion() {
        return updateVersion;
    }

    public void setUpdateVersion(String updateVersion) {
        this.updateVersion = updateVersion;
    }

    public String getUpdateInfo() {
        return updateInfo;
    }

    public void setUpdateInfo(String updateInfo) {
        this.updateInfo = updateInfo;
    }

    public String getUpdateUrl() {
        return updateUrl;
    }

    public void setUpdateUrl(String updateUrl) {
        this.updateUrl = updateUrl;
    }

    public String getVersionCode() {
        return versionCode;
    }

    public void setVersionCode(String versionCode) {
        this.versionCode = versionCode;
    }

    /**
     * 是否需要升级
     *
     * @return true为是，false为否
     */
    public boolean canUpdate() {
        boolean flag = updateType != 0;
        return flag;
    }

    /**
     * 是否需要强制升级
     *
     * @return true为是，false为否
     */
    public boolean canForceUpdate() {
        boolean flag = updateType == 2;
        return flag;
    }

    @Override
    public String toString() {
        return "UpdateData{" +
                "updateType=" + updateType +
                ", updateVersion='" + updateVersion + '\'' +
                ", updateInfo='" + updateInfo + '\'' +
                ", updateUrl='" + updateUrl + '\'' +
                ", versionCode='" + versionCode + '\'' +
                '}';
    }
}
