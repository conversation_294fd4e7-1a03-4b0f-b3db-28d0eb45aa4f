package com.kaolafm.kradio.home.comprehensive.mvp;

import com.kaolafm.kradio.lib.base.mvp.IModel;
import com.kaolafm.opensdk.api.operation.model.column.ColumnGrp;
import com.kaolafm.opensdk.http.core.HttpCallback;

import java.util.List;

/**
 * <AUTHOR>
 **/
public interface ICategoryModel extends IModel {

    void getDatas(HttpCallback<List<ColumnGrp>> callback);

    void getDatas(String zone, HttpCallback<List<ColumnGrp>> callback);

    void refresh();
}
