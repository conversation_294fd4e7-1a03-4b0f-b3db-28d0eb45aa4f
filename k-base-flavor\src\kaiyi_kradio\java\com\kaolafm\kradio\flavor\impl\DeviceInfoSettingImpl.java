package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.os.Build;
import android.provider.Settings;


/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-03-22 11:46
 ******************************************/
public final class DeviceInfoSettingImpl implements DeviceInfoSetting {
    @Override
    public void setInfoForSDK(Context context) {
        Context context = (Context) args[0];
        String deviceId = Settings.Global.getString(context.getContentResolver(), "autoai_vin");
        DeviceInfoUtil.setDeviceIdAndCarType(deviceId, null);
    }

    @Override
    public String getCarType(Object... args) {
        Context context = (Context) args[0];
        String deviceId = Settings.Global.getString(context.getContentResolver(), "ivi.system.vehicle.daid");
        if (deviceId == null || deviceId.length() < 6) {
            return null;
        }

        return deviceId.substring(0, 6);
    }
}
