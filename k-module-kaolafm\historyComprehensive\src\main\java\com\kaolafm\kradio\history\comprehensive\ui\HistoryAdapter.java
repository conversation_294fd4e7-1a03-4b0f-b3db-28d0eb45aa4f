package com.kaolafm.kradio.history.comprehensive.ui;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewGroup.LayoutParams;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.kaolafm.kradio.lib.bean.HistoryItem;
import com.kaolafm.kradio.common.utils.CornerTransform;
import com.kaolafm.kradio.lib.bean.HeadTitleItem;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.DateUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.UrlUtil;
import com.kaolafm.kradio.component.ui.base.utils.VipCornerUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.user.LoginManager;
import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.AlbumPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.FeaturePlayItem;
import com.kaolafm.opensdk.player.logic.model.item.OneKeyPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonClickReportEvent;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.event.LoginReportEvent;
import com.kaolafm.report.util.ReportConstants;
import com.kaolafm.report.util.ReportParameterManager;

/**
 * <AUTHOR> Yan
 * @date 2020/8/19
 */
public class HistoryAdapter extends BaseAdapter<HistoryItem> {

    private Context mContext;

    public static final int HEAD_UNLOGIN_TIP = 151;

    public static final int HISTORY_COUNT_TITLE = 152;

    private String mCurrentPlayingId = PlayerManagerHelper.getInstance().getCurPlayItem().getRadioId();

    private HistoryFragment mFragment;

    public HistoryAdapter(HistoryFragment fragment) {
        setHasStableIds(true);
        mContext = fragment.getContext();
        mFragment = fragment;
    }

    @Override
    protected BaseHolder<HistoryItem> getViewHolder(ViewGroup parent, int viewType) {
        if (viewType == HEAD_UNLOGIN_TIP) {
            return new HeadHolder(inflate(parent, R.layout.item_history_head, viewType));
        } else if (viewType == HISTORY_COUNT_TITLE) {
            return new CountTitleHolder(inflate(parent, R.layout.item_history_count, viewType));
        }
        return new HistoryHolder(inflate(parent, R.layout.item_user_tab_base, viewType));
    }

    /**
     * 设置播放状态
     */
    public void setPlaying(String playingId) {
        for (int i = 0; i < getItemCount(); i++) {
            HistoryItem itemBean = mDataList.get(i);
            //ID一样就显示播放状态
            if (TextUtils.equals(String.valueOf(itemBean.getRadioId()), playingId)) {
                itemBean.setPlaying(true);
                notifyItemChanged(i);
            }
            if (TextUtils.equals(String.valueOf(itemBean.getRadioId()), mCurrentPlayingId)) {
                itemBean.setPlaying(false);
                notifyItemChanged(i);
            }
        }
        mCurrentPlayingId = playingId;
    }

    @Override
    public int getItemViewType(int position) {
        return getItemData(position).getTypeId();
    }

    class HistoryHolder extends BaseHolder<HistoryItem> {

        ImageView mIvPlayCover;
        TextView mTvHistoryTitle;
        TextView mTvHistoryContent;
        TextView user_item_tag_tv;
        View rootView;
        ImageView mVipIcon;
        ImageView mCoverOffline;
        View user_layout_playing;

        public HistoryHolder(View itemView) {
            super(itemView);
            mIvPlayCover=itemView.findViewById(R.id.iv_play_cover);
            mTvHistoryTitle=itemView.findViewById(R.id.user_tab_title);
            mTvHistoryContent=itemView.findViewById(R.id.user_tab_content);
            user_item_tag_tv=itemView.findViewById(R.id.user_item_tag_tv);
            rootView=itemView.findViewById(R.id.rootView);
            mVipIcon=itemView.findViewById(R.id.vip_icon);
            mCoverOffline=itemView.findViewById(R.id.cover_offline);
            user_layout_playing=itemView.findViewById(R.id.user_layout_playing);
        }

        @Override
        public void setupData(HistoryItem historyItem, int position) {
            PlayItem playItem = PlayerManagerHelper.getInstance().getCurPlayItem();
            String curPlayRadioId = "";
            if (playItem != null) {
                curPlayRadioId = playItem.getRadioId();
//                if (playItem instanceof OneKeyPlayItem) {
//                    curPlayRadioId = playItem.getAlbumId();
//                }
            }
            String tag = initTagText(historyItem);
            if (TextUtils.isEmpty(tag)) {
                user_item_tag_tv.setVisibility(View.GONE);
            } else {
                user_item_tag_tv.setVisibility(View.VISIBLE);
                user_item_tag_tv.setText(tag);
            }
            boolean isPlaying = String.valueOf(curPlayRadioId).equals(historyItem.getRadioId()) || historyItem.isPlaying();
            String subtitle = createSubTitle(historyItem, isPlaying);
            if (historyItem.getType().equals(PlayerConstants.RESOURCES_TYPE_BROADCAST + "") && !StringUtil.isEmpty(historyItem.getFreq())) {
                mTvHistoryTitle.setText(historyItem.getRadioTitle() + " " + historyItem.getFreq());
                rootView.setContentDescription(historyItem.getRadioTitle() + " " + historyItem.getFreq());
            } else {
                mTvHistoryTitle.setText(historyItem.getRadioTitle());
                rootView.setContentDescription(historyItem.getRadioTitle());
            }
            mTvHistoryContent.setText(subtitle);
//            ImageLoader.getInstance().displayImage(mContext,
//                    UrlUtil.getCustomPicUrl(UrlUtil.PIC_250_250, historyItem.getPicUrl()), mIvPlayCover);

/*            CornerTransform cornerTransform = new CornerTransform(mContext, ResUtil.getDimen(R.dimen.m8));
            cornerTransform.setNeedCorner(true, false, true, false);
            RequestOptions options = new RequestOptions()
                    .placeholder(ResUtil.getDrawable(R.drawable.media_default_pic_let_radius))
                    .error(ResUtil.getDrawable(R.drawable.media_default_pic_let_radius))
                    .transform(cornerTransform);
            Glide.with(mContext).asBitmap()
                    .load(UrlUtil.getCustomPicUrl(UrlUtil.PIC_250_250, historyItem.getPicUrl()))
                    .apply(options)
                    .into(mIvPlayCover);*/

            ImageLoader.getInstance().displayImage(mContext,
                    UrlUtil.getCustomPicUrl(UrlUtil.PIC_250_250, historyItem.getPicUrl()), mIvPlayCover);


            rootView.setActivated(isPlaying);
            mTvHistoryTitle.setSelected(isPlaying);
            mTvHistoryContent.setSelected(isPlaying);
            if (isPlaying) {
                user_layout_playing.setVisibility(View.VISIBLE);
            } else {
                user_layout_playing.setVisibility(View.GONE);
            }

            boolean offline = historyItem.isOffline();
            mCoverOffline.setVisibility(offline ? View.VISIBLE : View.GONE);

            historyItem.setPlaying(isPlaying);
//            OritationViewUtils.handleOrientationTextSize(mTvHistoryTitle, mTvHistoryTitle,
//                    R.dimen.text_size4,
//                    R.dimen.text_size4);
//            OritationViewUtils.handleOrientationTextSize(mTvHistoryContent, mTvHistoryContent,
//                    R.dimen.text_size2,
//                    R.dimen.text_size2);
            if (historyItem.getType().equals(ResType.TYPE_BROADCAST + "")
                    || historyItem.getType().equals(ResType.TYPE_LIVE + "")
                    || historyItem.getType().equals(ResType.TYPE_TV + "")) {
                mVipIcon.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_icon_live_class));
            } else {
                VipCornerUtil.setVipCorner(mVipIcon, historyItem.getVip(),
                        historyItem.getFine(), true);
            }
        }


        private String initTagText(HistoryItem item) {
            String result = null;
            int typeInt = Integer.parseInt(item.getType());
            switch (typeInt) {
                case PlayerConstants.RESOURCES_TYPE_BROADCAST:
                    result = "广播";
                    break;
                case PlayerConstants.RESOURCES_TYPE_TV:
                    result = "电视";
                    break;
                case PlayerConstants.RESOURCES_TYPE_FEATURE:
                    result = "专题";
                    break;
                case PlayerConstants.RESOURCES_TYPE_RADIO:
                case PlayerConstants.RESOURCES_TYPE_ALBUM:
                    result = "专辑";
                    break;
            }
            return result;
        }

        protected String createSubTitle(HistoryItem item, boolean isCurrentAudio) {
            String result = ResUtil.getString(R.string.unknown);
            if (item != null) {
                int typeInt = PlayerConstants.RESOURCES_TYPE_INVALID;
                try {
                    typeInt = Integer.parseInt(item.getType());
                } catch (Exception e) {

                }

                switch (typeInt) {
                    case PlayerConstants.RESOURCES_TYPE_RADIO:
                        result = DateUtil.getDisTimeStr(Long.valueOf(item.getRadioUpdateTime())) + "更新";
                        break;
                    case PlayerConstants.RESOURCES_TYPE_BROADCAST:
                    case PlayerConstants.RESOURCES_TYPE_TV:
                        if (TextUtils.isEmpty(item.getCurrentProgramName())) {
                            result = "暂无节目单";
                        } else {
                            if (item.getCurrentProgramName().equals("暂无节目单"))
                                result = item.getCurrentProgramName();
                            else
                                result = "正在直播：" + item.getCurrentProgramName();
                        }
                        break;
                    case PlayerConstants.RESOURCES_TYPE_ONE_KEY_LISTENER_TYPE:
                    case PlayerConstants.RESOURCES_TYPE_PURCHASE_ONE_KEY_LISTENER_TYPE: {
//                        String orderStr = Constants.BLANK_STR;
//                        if (item.getOrderNum() > 0) {
//                            orderStr = String.format(ResUtil.getString(R.string.audio_num), item.getOrderNum());
//                        }
                        if (isCurrentAudio) {
                            PlayItem playItem = PlayerManagerHelper.getInstance().getCurPlayItem();
                            if (playItem instanceof OneKeyPlayItem) {
                                OneKeyPlayItem oneKeyPlayItem = (OneKeyPlayItem) playItem;

//                                orderStr = String.format(ResUtil.getString(R.string.audio_num),
//                                        oneKeyPlayItem.getInfoData().getOrderNum());
                                result = StringUtil.join(ResUtil.getString(R.string.is_playing),
                                        oneKeyPlayItem.getInfoData().getTitle());
                            }
                        } else {
                            result = StringUtil.join(
                                    ResUtil.getString(R.string.last_play),
                                    item.getAudioTitle());
                        }
                    }
                    break;
                    case PlayerConstants.RESOURCES_TYPE_FEATURE:
                    case PlayerConstants.RESOURCES_TYPE_ALBUM:
//                        String orderStr = Constants.BLANK_STR;
//                        if (item.getOrderNum() > 0) {
//                            orderStr = String.format(ResUtil.getString(R.string.audio_num), item.getOrderNum());
//                        }
                        if (isCurrentAudio) {
                            PlayItem currPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
                            if (currPlayItem instanceof AlbumPlayItem) {
//                                orderStr = String.format(ResUtil.getString(R.string.audio_num),
//                                        ((AlbumPlayItem) currPlayItem).getInfoData().getOrderNum());
                                result = StringUtil.join(ResUtil.getString(R.string.is_playing),
                                        ((AlbumPlayItem) currPlayItem).getInfoData().getTitle());
                            } else if (currPlayItem instanceof FeaturePlayItem) {
//                                orderStr = String.format(ResUtil.getString(R.string.audio_num),
//                                        ((FeaturePlayItem) currPlayItem).getInfoData().getOrderNum());
                                result = StringUtil.join(ResUtil.getString(R.string.is_playing),
                                        ((FeaturePlayItem) currPlayItem).getInfoData().getTitle());
                            }

                        } else {
                            result = StringUtil.join(
                                    ResUtil.getString(R.string.last_play),
                                    item.getAudioTitle());
                        }
                        break;
                    default:
                        result = ResUtil.getString(R.string.unknown);
                        break;
                }
            }

            return result;
        }
    }

    class HeadHolder extends BaseHolder<HistoryItem> {

        TextView tvHistoryLogin;

        HeadHolder(View itemView) {
            super(itemView);
            itemView.setEnabled(false);
            tvHistoryLogin=itemView.findViewById(R.id.tv_history_login);
        }

        @Override
        public void setupData(HistoryItem historyItem, int position) {
            if (historyItem instanceof HeadTitleItem) {
                LayoutParams layoutParams = itemView.getLayoutParams();
                layoutParams.height = getDataList().size() > 0 ? LayoutParams.WRAP_CONTENT : LayoutParams.MATCH_PARENT;
                itemView.setLayoutParams(layoutParams);
            }
            tvHistoryLogin.setOnClickListener(v -> {
//                ComponentClient.obtainBuilder(LoginComponentConst.NAME)
//                        .setActionName(LoginProcessorConst.SWITH_TO_LOGIN_FRAG)
//                        .addParam(LoginProcessorConst.TOPFRAG, mFragment.getParentFragment())
//                        .addParam(LoginProcessorConst.BACKTYPE, LoginProcessorConst.BACKTYPE_SWITCH)
//                        .addParam(LoginProcessorConst.BACKFRAGINDEX, Constants.HISTORY_INDEX)
//                        .build().call();
                ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_LOGIN_BUTTON, tvHistoryLogin.getText().toString(), ReportParameterManager.getInstance().getPage(), ReportConstants.CONTROL_TYPE_SCREEN));
                boolean isLogin = LoginManager.getInstance().checkLogin();
                if (!isLogin){
                    LoginManager.getInstance().setLoginInTo(LoginReportEvent.REMARKS1_HISTORY_PAGE);
                }
            });

            ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_LOGIN_BUTTON, tvHistoryLogin.getText().toString(), ReportParameterManager.getInstance().getPage(), ReportConstants.CONTROL_TYPE_SCREEN));
        }
    }

    class CountTitleHolder extends BaseHolder<HistoryItem> {

        TextView tvHistoryCount;
        ImageView mIvclearHistory;

        CountTitleHolder(View itemView) {
            super(itemView);
            tvHistoryCount=itemView.findViewById(R.id.tv_history_count);
            mIvclearHistory=itemView.findViewById(R.id.user_clear_his);
            itemView.setEnabled(false);
            mIvclearHistory.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    if (AntiShake.check(mIvclearHistory.getId())) {
                        return;
                    }
                    if (mFragment != null && mFragment instanceof HistoryFragment) {
                        ((HistoryFragment) mFragment).clickClear();
                        //点击一键清空事件上报
                        ButtonClickReportEvent event = new ButtonClickReportEvent(ButtonClickReportEvent.BUTTON_CLICK_CLEAR);
                        ReportHelper.getInstance().addEvent(event);
                    }
                    ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_DELETE_BUTTON, "", ReportParameterManager.getInstance().getPage(), ReportConstants.CONTROL_TYPE_SCREEN));
                }
            });
        }

        @Override
        public void setupData(HistoryItem historyItem, int position) {
            if (historyItem instanceof HeadTitleItem) {
                String format = ResUtil.getString(R.string.history_count);
                tvHistoryCount.setText(String.format(format, ((HeadTitleItem) historyItem).getCount()));
            }
            ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_DELETE_BUTTON, "", ReportParameterManager.getInstance().getPage(), ReportConstants.CONTROL_TYPE_SCREEN));
        }
    }
    @Override
    public long getItemId(int position) {
        return position;
    }
}
