package com.kaolafm.kradio.lib.init;

import android.os.AsyncTask;
import android.util.Log;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.lib.utils.AppUtil;
import com.kaolafm.kradio.lib.utils.YTLogUtil;
import com.kaolafm.opensdk.log.Logging;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * 电台模式初始化管理类，用户处理各个初始化任务的初始化逻辑。该类只在切换模式的时候调用，其他地方不需要调用。
 * <AUTHOR>
 * @date 2022-06-30
 */
public class ModelInitManager {

    private static final String TAG = "ModelInitManager";

    static {

    }

    private ModelInitManager() {
    }

    private static List<ModelInitTask> mModelInitTasks = new ArrayList<>();

    private static BlockingQueue<ModelInitTask> mAsyncTaskQueue;

    private static volatile boolean isAsyncTaskFinished = false;

    private static boolean isMainProcess;


    static void registerInitializer(ModelInitTask modelInitTask) {
        Log.i(TAG,"Enter method registerInitializer.");
        if (modelInitTask != null) {
            mModelInitTasks.add(modelInitTask);
        }
        Log.i(TAG,"Exit method registerInitializer.");
    }

    public static void registerInitializers(ModelInitTaskContainer container) {
        YTLogUtil.logStart(TAG, "registerInitializers", "container size = " + container.modelInitTasks.size());
        if (container != null) {
            mModelInitTasks.addAll(container.modelInitTasks);
            if (!ListUtil.isEmpty(container.asyncInitTasks)) {
                //对于异步初始化的task第一次不在这里添加，只是初始化容器。
                if (mAsyncTaskQueue == null) {
                    mAsyncTaskQueue = new ArrayBlockingQueue<>(container.asyncInitTasks.size());
                } else {//如果已经有了其他初始化的task，就需要在扩容以后把原来的添加上，新增的同样不在这里添加。
                    ArrayBlockingQueue<ModelInitTask> modelInitTasks = new ArrayBlockingQueue<>(mModelInitTasks.size() + container.asyncInitTasks.size());
                    modelInitTasks.addAll(mModelInitTasks);
                    mAsyncTaskQueue = modelInitTasks;
                }
            }
        }
        YTLogUtil.logStart(TAG, "registerInitializers", "over");
    }

    static public boolean isTaskListEmpty(){
        if (ListUtil.isEmpty(mModelInitTasks)) {
            return true;
        }
        return false;
    }

    public static void setModel(int MODEL) {
        Logging.setDebug(true);
        Log.i(TAG,"Enter method setModel.");
        if (ListUtil.isEmpty(mModelInitTasks)) {
            Log.i(TAG,"mAppInitTasks is empty");
            return;
        }
        isMainProcess = AppUtil.isMainThread();

        doASyncInitTasks(MODEL);
        doSyncInitTasks(MODEL);

        isAsyncTaskFinished = true;
        Log.i(TAG,"Exit method setModel.");
    }

    private static void doASyncInitTasks(int MODEL) {
        AsyncTask.THREAD_POOL_EXECUTOR.execute(() -> asyncInit(MODEL));
    }

    private static void doSyncInitTasks(int MODEL) {
        for (ModelInitTask modelInitTask : mModelInitTasks) {
            if (isNotIgnore(modelInitTask)) {
                modelInitTask.initializer.onInit(MODEL);
                if (modelInitTask.isAsync && !mAsyncTaskQueue.contains(modelInitTask)) {
                    Log.i(TAG,"开始执行: dispatch 内部逻辑 ");
                    mAsyncTaskQueue.add(modelInitTask);
                }
            }
        }
    }

    private static void asyncInit(int MODEL) {
        if(mAsyncTaskQueue == null){
            return;
        }
        ModelInitTask modelInitTask;
        try {
            while (true) {
                modelInitTask = mAsyncTaskQueue.poll(100, TimeUnit.MILLISECONDS);
                if (modelInitTask == null) {
                    if (isAsyncTaskFinished) {
                        break;
                    } else {
                        continue;
                    }
                }
                if (isNotIgnore(modelInitTask)) {
                    final ModelInitTask finalModelInitTask = modelInitTask;
                    time(modelInitTask.initializer.getClass().getName() + " asyncCreate()",
                            () -> finalModelInitTask.initializer.asyncInit(MODEL));
                }
                if (isAsyncTaskFinished && mAsyncTaskQueue.isEmpty()) {
                    break;
                }
            }
        } catch (InterruptedException e) {
            Log.e(TAG, "Error occurred when start AppInitTask", e);
        }
    }

    private static void dispatch(Callback callback) {
        if (!ListUtil.isEmpty(mModelInitTasks)) {
            Log.i(TAG,"开始执行: dispatch size = " + mModelInitTasks.size());


        }
    }

    private static boolean isNotIgnore(ModelInitTask initItem) {
        Log.i(TAG,"isMainProcess =" + isMainProcess + ", initItem.inMainProcess() " + initItem.inMainProcess() + ", initItem.inOtherProcess() = " + initItem.inOtherProcess());
        return (isMainProcess && initItem.inMainProcess()) || (!isMainProcess && initItem.inOtherProcess());
    }

    /**
     * 统计执行时间
     */
    private static long time(Runnable runnable) {
        if (runnable == null) {
            return 0;
        }
        long startTime = System.currentTimeMillis();
        runnable.run();
        return System.currentTimeMillis() - startTime;
    }

    /**
     * 统计执行时间
     */
    private static long time(String desc, Runnable runnable) {
        long time = time(runnable);
        Logging.d("%s耗时:%sms\n\n", desc, time);
        return time;
    }

    /**
     * 统计执行时间
     */
    static String timeStr(String desc, Runnable runnable) {
        String msg = String.format("%s耗时:%sms\n\n", desc, time(runnable));
        Logging.d(msg);
        return msg;
    }

    private interface Callback {

        void dispatch(AppInitializable initializer);
    }

}
