package com.kaolafm.kradio.util;

import android.content.Context;
import android.media.AudioAttributes;
import android.media.AudioFocusRequest;
import android.media.AudioManager;
import android.os.Build;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.opensdk.player.core.model.AAudioFocus;
import com.kaolafm.opensdk.player.core.utils.PlayerCustomizeManager;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;

public class AudioSourceManager extends AAudioFocus {

    private static AudioSourceManager instance = new AudioSourceManager(AppDelegate.getInstance().getContext());

    private AudioManager mAudioManager;
    private AudioManager.OnAudioFocusChangeListener mOnAudioFocusChangeListener = (focusChange) -> {
        this.notifyAudioFocusChange(true, focusChange);
    };

    private AudioSourceManager(Context context) {
        this.mAudioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
    }

    public static AudioSourceManager getInstance() {
        return instance;
    }

    AudioFocusRequest audioFocusRequest;

    @Override
    public boolean requestAudioFocus() {
        if (mAudioManager == null) {
            return false;
        }
        //通知客户端
        //mKLAudioFocusOperationListener
        int result = 0;
        PlayerCustomizeManager.getInstance().beforeRequestAudioFocus(mAudioManager);

        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            AudioAttributes audioAttributes = new AudioAttributes.Builder()
                    .setUsage(AudioAttributes.USAGE_MEDIA)
                    .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                    .build();
            audioFocusRequest = new AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN)
                    .setAudioAttributes(audioAttributes)
                    .setOnAudioFocusChangeListener(mOnAudioFocusChangeListener)
                    .setAcceptsDelayedFocusGain(true)
                    .build();
            result = mAudioManager.requestAudioFocus(audioFocusRequest);
        } else {
            result = mAudioManager.requestAudioFocus(mOnAudioFocusChangeListener
                    , AudioManager.STREAM_MUSIC
                    , AudioManager.AUDIOFOCUS_GAIN);
        }

        PlayerLogUtil.log(getClass().getSimpleName(), "requestAudioFocus", "result = " + result);
        notifyAudioFocusChange(true, result);
        return result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED;
    }


    @Override
    public boolean abandonAudioFocus() {
        if (mAudioManager == null) {
            return false;
        }
        int rect;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O && audioFocusRequest != null) {
            rect = mAudioManager.abandonAudioFocusRequest(audioFocusRequest);
        } else {
            rect = mAudioManager.abandonAudioFocus(mOnAudioFocusChangeListener);
        }
        PlayerLogUtil.log(getClass().getSimpleName(), "abandonAudioFocus", "result = " + rect);

        notifyAudioFocusChange(false, -1);
        return rect == AudioManager.AUDIOFOCUS_REQUEST_GRANTED;
    }
}
