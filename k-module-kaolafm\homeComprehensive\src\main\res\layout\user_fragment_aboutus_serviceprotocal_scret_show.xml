<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_home"
    android:scrollbars="none">

    <ImageView
        android:id="@+id/login_close_btn"
        android:layout_width="@dimen/m36"
        android:layout_height="@dimen/m36"
        android:layout_marginStart="@dimen/m70"
        android:layout_marginTop="@dimen/m54"
        android:background="@color/transparent"
        android:scaleType="centerInside"
        android:src="@drawable/player_ic_back"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/login_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/m50"
        android:gravity="center"
        android:textColor="@color/text_color_7"
        android:textSize="@dimen/m30"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/deliver"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="@dimen/m34"
        android:background="@color/activity_line_bg"
        app:layout_constraintTop_toBottomOf="@+id/login_close_btn"
        tools:ignore="MissingConstraints" />

    <ScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/deliver">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingLeft="@dimen/m80"
            android:paddingTop="@dimen/m62"
            android:paddingRight="@dimen/m80"
            android:paddingBottom="@dimen/m50">

            <TextView
                android:id="@+id/person_center_aboutus_details_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:lineSpacingMultiplier="1.5"
                android:text="@string/privacy_policy_content_str"
                android:textColor="@color/person_center_aboutus_details_color"
                android:textSize="@dimen/text_size3" />

        </LinearLayout>

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>