package com.kaolafm.kradio.home.comprehensive.recyclerview;

import android.content.res.Configuration;
import android.util.Log;
import android.view.View;
import android.view.animation.AnimationUtils;
import android.view.animation.GridLayoutAnimationController;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.GridLayoutManager.SpanSizeLookup;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.ItemAnimator;
import androidx.recyclerview.widget.SimpleItemAnimator;

import com.kaolafm.kradio.common.widget.GridRecyclerView;
import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.home.comprehensive.adapter.HomeAdapter;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.ResUtil;

import java.util.List;

/**
 * 首页recycleview相关的工具类，用于绑定、数据填充、样式显示的定义等
 *
 * <AUTHOR> Yan
 * @date 2019-08-14
 */
public class HomeRecyclerViewHelper {

    private GridLayoutManager mGridLayoutManager;

    private HomeAdapter mHomeAdapter;

    private GridRecyclerView mRecyclerView;

    private GridLayoutAnimationController mGridController;

    public void bindRecycleView(@NonNull final GridRecyclerView recyclerView,
                                HomeAdapter homeAdapter) {
        if (mRecyclerView != null) {
            mRecyclerView.setAdapter(null);
            mRecyclerView.setLayoutManager(null);
        }
        mRecyclerView = recyclerView;
//      解决首页 RecycleView 在滑动时无法复用 viewholder 和 gc 问题。
        mRecyclerView.setItemViewCacheSize(ResUtil.getInt(R.integer.home_item_caches));
        // CPU优化：禁用绘制缓存以减少内存使用和CPU消耗
        mRecyclerView.setDrawingCacheEnabled(false);
        // CPU优化：禁用嵌套滚动以减少滚动计算
        mRecyclerView.setNestedScrollingEnabled(false);
        // CPU优化：设置更大的RecycledViewPool以减少ViewHolder创建
        RecyclerView.RecycledViewPool recycledViewPool = new RecyclerView.RecycledViewPool();
        recycledViewPool.setMaxRecycledViews(0, 20); // 增加缓存池大小
        mRecyclerView.setRecycledViewPool(recycledViewPool);

        // CPU优化：禁用硬件加速的某些功能以降低GPU使用率
        mRecyclerView.setLayerType(View.LAYER_TYPE_NONE, null);
        // CPU优化：设置预取项目数量，减少滚动时的布局计算
        if (mRecyclerView.getLayoutManager() instanceof LinearLayoutManager) {
            ((LinearLayoutManager) mRecyclerView.getLayoutManager()).setInitialPrefetchItemCount(4);
        }

        // CPU优化：设置RecyclerView的优化选项
        optimizeRecyclerViewPerformance();

        mHomeAdapter = homeAdapter;

        // CPU优化：完全禁用RecyclerView动画以提升性能
        closeDefaultAnimator(mRecyclerView);

        mGridLayoutManager = new GridLayoutManager(recyclerView.getContext(),
                ResUtil.getInt(R.integer.home_item_spans), GridLayoutManager.HORIZONTAL, false);
        SpanSizeLookup spanSizeLookup = new SpanSizeLookup() {
            @Override
            public int getSpanSize(int position) {
                HomeCell itemData = mHomeAdapter.getItemData(position);
                int spanSize = itemData != null ? itemData.spanSize() : 0;
                /*
                    这里是为了解决偶现的问题java.lang.IllegalArgumentException: Item at position xx requires 4 spans but GridLayoutManager has only 2 spans.
                    出现这个Bug的原因是可能是在执行GridLayoutManager#layoutChunk的时候已经转换屏幕了，
                    但是首页加锁导致mGridLayoutManager.setSpanCount()还未设置对应的值，这就会导致spanSize>mSpanCount的情况出现
                    如果showAccordingToScreen不加锁会导致快速转换的时候页面混乱。
                 */
                return Math.min(spanSize, mGridLayoutManager.getSpanCount());
            }
        };
        mGridLayoutManager.setSpanSizeLookup(spanSizeLookup);

        HomeItemDecoration homeItemDecoration = new HomeItemDecoration();
        mRecyclerView.addItemDecoration(homeItemDecoration);

        mRecyclerView.setLayoutManager(mGridLayoutManager);
        mRecyclerView.setAdapter(mHomeAdapter);
    }

    public void bindVerticalHomeRecycleView(@NonNull final GridRecyclerView recyclerView,
                                       HomeAdapter homeAdapter) {
        if (mRecyclerView != null) {
            mRecyclerView.setAdapter(null);
            mRecyclerView.setLayoutManager(null);
        }
        mRecyclerView = recyclerView;
//      解决首页 RecycleView 在滑动时无法复用 viewholder 和 gc 问题。
        mRecyclerView.setItemViewCacheSize(ResUtil.getInt(R.integer.home_item_caches));
        mRecyclerView.setDrawingCacheEnabled(true);
        mHomeAdapter = homeAdapter;

//        //关闭动画，解决刷新的闪烁问题
//        mRecyclerView.getItemAnimator().setAddDuration(0);
//        mRecyclerView.getItemAnimator().setChangeDuration(0);
//        mRecyclerView.getItemAnimator().setMoveDuration(0);
//        mRecyclerView.getItemAnimator().setRemoveDuration(0);
//        ((SimpleItemAnimator)recyclerView.getItemAnimator()).setSupportsChangeAnimations(false);
        //加载动画
        mGridController = (GridLayoutAnimationController) AnimationUtils.loadLayoutAnimation(recyclerView.getContext(), R.anim.layout_subcategory_item_load);
        mRecyclerView.setLayoutAnimation(null);

        final int spanCount = 5;
        mGridLayoutManager = new GridLayoutManager(recyclerView.getContext(),
                spanCount, GridLayoutManager.VERTICAL, false);
        SpanSizeLookup spanSizeLookup = new SpanSizeLookup() {
            @Override
            public int getSpanSize(int position) {
                if (true) return 1;
                HomeCell itemData = mHomeAdapter.getItemData(position);
                int spanSize = itemData != null ? itemData.spanSize() : 0;
                /*
                    这里是为了解决偶现的问题java.lang.IllegalArgumentException: Item at position xx requires 4 spans but GridLayoutManager has only 2 spans.
                    出现这个Bug的原因是可能是在执行GridLayoutManager#layoutChunk的时候已经转换屏幕了，
                    但是首页加锁导致mGridLayoutManager.setSpanCount()还未设置对应的值，这就会导致spanSize>mSpanCount的情况出现
                    如果showAccordingToScreen不加锁会导致快速转换的时候页面混乱。
                 */
                return Math.min(spanSize, mGridLayoutManager.getSpanCount());
            }
        };
        mGridLayoutManager.setSpanSizeLookup(spanSizeLookup);

        HomeItemDecoration homeItemDecoration = new HomeItemDecoration();
        mRecyclerView.addItemDecoration(homeItemDecoration);

        mRecyclerView.setLayoutManager(mGridLayoutManager);
        mRecyclerView.setAdapter(mHomeAdapter);
    }

    public void setDataList(List<HomeCell> cells) {
        Log.i("HomeRecyclerViewHelper", "setDataList. cells is "+cells.toString());
        mHomeAdapter.setDataList(cells);
        mHomeAdapter.changePlayingState();
    }

    public void showAccordingToScreen(int orientation) {
        int firstVisibleItemPosition = mGridLayoutManager.findFirstVisibleItemPosition();
        mGridLayoutManager.setSpanCount(ResUtil.getInt(R.integer.home_item_spans));
        mGridLayoutManager.setOrientation(orientation == Configuration.ORIENTATION_LANDSCAPE ? RecyclerView.HORIZONTAL : RecyclerView.VERTICAL);
        mGridLayoutManager.scrollToPositionWithOffset(firstVisibleItemPosition, 0);
        closeDefaultAnimator(mRecyclerView);
        mHomeAdapter.notifyDataSetChanged();
    }

    /**
     * 关闭默认局部刷新动画 - CPU优化：完全禁用动画以提升性能
     */
    public void closeDefaultAnimator(RecyclerView recyclerView) {
        if (recyclerView != null) {
            // CPU优化：直接设置为null以完全禁用动画
            recyclerView.setItemAnimator(null);

            // 备用方案：如果需要保留ItemAnimator但禁用动画
            // ItemAnimator itemAnimator = recyclerView.getItemAnimator();
            // if (itemAnimator != null) {
            //     itemAnimator.setAddDuration(0);
            //     itemAnimator.setChangeDuration(0);
            //     itemAnimator.setMoveDuration(0);
            //     itemAnimator.setRemoveDuration(0);
            //     if (itemAnimator instanceof SimpleItemAnimator) {
            //         ((SimpleItemAnimator) itemAnimator).setSupportsChangeAnimations(false);
            //     }
            // }
        }
    }

    public void notifyItemRangeChanged(int positionStart, int itemCount) {
        if (mHomeAdapter != null) {
            mHomeAdapter.notifyItemRangeChanged(positionStart, itemCount);
        }

    }

    /**
     * CPU优化：RecyclerView性能优化配置
     */
    private void optimizeRecyclerViewPerformance() {
        if (mRecyclerView == null) return;

        // CPU优化：禁用变更动画以减少重绘
        mRecyclerView.setHasFixedSize(true);

        // CPU优化：设置更大的ViewHolder缓存
        mRecyclerView.getRecycledViewPool().setMaxRecycledViews(0, 30);

        // CPU优化：减少过度绘制
        mRecyclerView.setClipToPadding(false);
        mRecyclerView.setClipChildren(false);

        // CPU优化：禁用焦点变化动画
        mRecyclerView.setFocusable(false);
        mRecyclerView.setFocusableInTouchMode(false);
    }

}
