package com.kaolafm.kradio.huawei.provider;

import android.graphics.Bitmap;
import android.util.Log;
import android.view.View;

import com.huawei.carmediakit.bean.LoginInfo;
import com.huawei.carmediakit.bean.QrCodeLoginInfo;
import com.huawei.carmediakit.bean.UserInfo;
import com.huawei.carmediakit.callback.BasicCallback;
import com.huawei.carmediakit.constant.ErrorCode;
import com.huawei.carmediakit.provider.IUserLoginProvider;
import com.huawei.carmediakit.reporter.UserSettingsReporter;
import com.kaolafm.kradio.huawei.utils.Constant;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;

import java.util.ArrayList;
import java.util.List;

public class UserLoginProvider implements IUserLoginProvider, IAccountLoginView {
    public static final String TAG = Constant.TAG;

    AccountLoginPresenter mPresenter;
    BasicCallback<LoginInfo> asyncQueryLoginInfoCallback;
    BasicCallback<QrCodeLoginInfo.QrCodeItem> refreshQrCallback;

    @Override
    public List<LoginInfo.LoginType> querySupportedLoginTypes() {
        Log.i(TAG, "querySupportedLoginTypes");
        ArrayList<LoginInfo.LoginType> arrayList = new ArrayList();
        arrayList.add(LoginInfo.LoginType.QR_CODE_SCAN);
        return arrayList;
    }

    @Override
    public void asyncQueryLoginInfo(LoginInfo.LoginType loginType, BasicCallback<LoginInfo> basicCallback) {
        Log.i(TAG, "queryLoginInfo");
        asyncQueryLoginInfoCallback = basicCallback;
        getPresenter().getData();
    }

    @Override
    public void asyncRefreshQrCodeItem(String s, BasicCallback<QrCodeLoginInfo.QrCodeItem> basicCallback) {
        Log.i(TAG, "asyncRefreshQrCodeItem=" + s);
        refreshQrCallback = basicCallback;
        getPresenter().getData();
    }

    private AccountLoginPresenter getPresenter() {
        if (mPresenter == null) {
            mPresenter = new AccountLoginPresenter(this);
        }
        return mPresenter;
    }

    @Override
    public void showQRCode(String url) {
        ImageLoader.getInstance().getBitmapFromCache(AppDelegate.getInstance().getContext(),
                url, this::showQrCallback);
    }

    private void showQrCallback(Bitmap bitmap) {
        Logger.i(TAG, "bitmap=" + bitmap);
        if (asyncQueryLoginInfoCallback != null) {
            ArrayList<QrCodeLoginInfo.QrCodeItem> arrayList = new ArrayList();
            QrCodeLoginInfo.QrCodeItem item = getQrCodeItem(bitmap);
            arrayList.add(item);
            asyncQueryLoginInfoCallback.callback((LoginInfo)new QrCodeLoginInfo(arrayList),
                    ErrorCode.SUCCESS, "success");
            asyncQueryLoginInfoCallback = null;
        }
        if (refreshQrCallback != null) {
            QrCodeLoginInfo.QrCodeItem item = getQrCodeItem(bitmap);
            refreshQrCallback.callback(item, ErrorCode.SUCCESS, "success");
            refreshQrCallback = null;
        }
    }

    private QrCodeLoginInfo.QrCodeItem getQrCodeItem(Bitmap bitmap) {
        QrCodeLoginInfo.QrCodeItem item = new QrCodeLoginInfo.QrCodeItem("qr",
                bitmap,
                null,
                "请用手机云听app扫码登录",
                11);

        return item;
    }

    @Override
    public void showBindSuccess(String name, String avatar) {
        Logger.i(TAG, "avatar=" + avatar);
        UserInfo userInfo = new UserInfo();
        userInfo.setNickName(name);
        userInfo.setProfilePicUrl(avatar);
        userInfo.setOnline(true);
        UserSettingsReporter.reportUserInfo(userInfo);
        mPresenter.cancelCheck();
    }

    @Override
    public void showNoNetWork() {

    }

    @Override
    public void showIntercepterView(View view) {

    }
}
