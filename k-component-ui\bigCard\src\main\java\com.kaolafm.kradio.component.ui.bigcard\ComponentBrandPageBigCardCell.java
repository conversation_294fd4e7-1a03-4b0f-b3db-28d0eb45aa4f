package com.kaolafm.kradio.component.ui.bigcard;

import androidx.annotation.NonNull;
import android.text.Html;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.util.Log;
import android.view.View;
import android.view.ViewTreeObserver;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.kradio.component.ui.R;
import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.component.ui.base.utils.ComponentUtils;
import com.kaolafm.kradio.component.ui.base.view.OvalImageView;
import com.kaolafm.kradio.component.ui.base.view.RateView;
import com.kaolafm.kradio.component.ui.base.CellBinder;
import com.kaolafm.kradio.component.ui.base.ItemClickSupport;
import com.kaolafm.kradio.component.ui.base.utils.RoundBackGroundColorSpan;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.common.ResType;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.UrlUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.opensdk.api.operation.model.column.ColumnContent;
import com.kaolafm.opensdk.api.operation.model.column.TopicDetailColumnMember;

import java.util.List;

/**
 * 品牌电台组件大卡片
 */
public class ComponentBrandPageBigCardCell extends HomeCell implements CellBinder<View, ComponentBrandPageBigCardCell>, ItemClickSupport {

    OvalImageView card_bg_iv;
    OvalImageView card_pic_iv;
    ImageView card_tag_iv;
    ImageView card_play_iv;
    TextView card_title_tv;
    TextView card_des_tv;
    RateView card_layout_playing;
    TextView card_tag_tv;

    @Override
    public void mountView(@NonNull ComponentBrandPageBigCardCell data, @NonNull View view, int position) {
        if (data.getContentList() == null || data.getContentList().size() == 0) {
            return;
        }
        card_bg_iv = view.findViewById(R.id.card_bg_iv);
        card_pic_iv= view.findViewById(R.id.card_pic_iv);
        card_tag_iv= view.findViewById(R.id.card_tag_iv);
        card_play_iv= view.findViewById(R.id.card_play_iv);
        card_title_tv= view.findViewById(R.id.card_title_tv);
        card_des_tv= view.findViewById(R.id.card_des_tv);
        card_layout_playing= view.findViewById(R.id.card_layout_playing);
        card_tag_tv= view.findViewById(R.id.card_tag_tv);


        if (data.getContentList().get(0) instanceof TopicDetailColumnMember) {
            card_title_tv.setMaxLines(1);
            String numText = ComponentUtils.getInstance().formatNumber(data.getContentList().get(0).getUserCount(),1)
                    + "<font size=" + ResUtil.getDimen(R.dimen.m18) + ">人参与</font> " +
                    ComponentUtils.getInstance().formatNumber(data.getContentList().get(0).getReadCount(),1) +
                    "<font size=" + ResUtil.getDimen(R.dimen.m18) + ">阅读量</font>";
            card_des_tv.setText(Html.fromHtml(numText));
        } else {
            if (!TextUtils.isEmpty(data.getContentList().get(0).getSubtitle())) {
                card_title_tv.setMaxLines(1);
                card_des_tv.setText(data.getContentList().get(0).getSubtitle());
            } else {
                card_title_tv.setMaxLines(2);
            }
        }
        card_title_tv.setText(data.getContentList().get(0).getTitle());
        if (TextUtils.isEmpty(data.getContentList().get(0).getTag())) {
            card_tag_tv.setVisibility(View.GONE);
        } else {
            card_tag_tv.setVisibility(View.VISIBLE);
            card_tag_tv.setText(data.getContentList().get(0).getTag());
            ComponentUtils.getInstance().setTagStyle(card_tag_tv, data.contentList.get(0).getTagColor());
        }

        hideCardDescIfNeed(data);

        String url = UrlUtil.getCardPicUrl(data.getContentList().get(0).getImageFiles());
        if (!TextUtils.isEmpty(url)) {
            card_pic_iv.setVisibility(View.VISIBLE);
            ImageLoader.getInstance().displayImage(view.getContext(),
                    url, card_pic_iv);
        } else {
            card_pic_iv.setVisibility(View.INVISIBLE);
        }
        boolean b = data.selected
                && ComponentUtils.getInstance().getCardHomePlayIdIsPlaying(data.contentList.get(0).getId(), data.contentList.get(0).getCanPlay());
        card_layout_playing.setVisibility(b ?
                View.VISIBLE : View.GONE);
        if (b) {
            card_play_iv.setVisibility(View.GONE);
        } else {
            if (data.contentList.get(0).getCanPlay() == 1) {
                card_play_iv.setVisibility(View.VISIBLE);
            }else {
                card_play_iv.setVisibility(View.GONE);
            }
        }

        view.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onViewClickListener != null) {
                    v.setTag(0);
                    onViewClickListener.onViewClick(v, getPositionInParent());
                }
            }
        });
    }

    private void hideCardDescIfNeed(ComponentBrandPageBigCardCell data){
        if(data == null){
            return;
        }
        List<ColumnContent> list = data.getContentList();
        if(list == null || list.size() == 0){
            return;
        }

        if(list.get(0).getResType() == ResType.ACTIVITY_TYPE){
            card_des_tv.setVisibility(View.INVISIBLE);
        }
    }
    private void initTitle(String title, int end) {
        ViewTreeObserver vto2 = card_title_tv.getViewTreeObserver();
        vto2.addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                TextPaint mTextPaint = card_title_tv.getPaint();
                mTextPaint.setTextSize(card_title_tv.getTextSize());
                int mTextViewWidth = (int) mTextPaint.measureText(title);
                SpannableString spannedString = new SpannableString(title);
                spannedString.setSpan(new RoundBackGroundColorSpan(ResUtil.getColor(R.color.home_card_big_tag_text_color)
                                , ResUtil.getColor(R.color.home_card_big_tag_text_color), mTextViewWidth > card_title_tv.getWidth()),
                        0, end, SpannableString.SPAN_COMPOSING);
                spannedString.setSpan(new AbsoluteSizeSpan(ResUtil.getDimen(R.dimen.m20)), 0, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                card_title_tv.setText(spannedString);
            }
        });
    }

    @Override
    public int getItemType() {
        return R.layout.component_brand_page_big_card_layout;
    }

    private BaseHolder.OnViewClickListener onViewClickListener;

    @Override
    public void setOnItemClickListener(BaseHolder.OnViewClickListener listener) {
        onViewClickListener = listener;
    }
}
