package com.kaolafm.kradio.lib.bean;

import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;
import com.kaolafm.opensdk.api.CrashMessageButtonActionBean;
import com.kaolafm.opensdk.api.CrashMessageTypeDataBean;

import org.greenrobot.greendao.annotation.Convert;
import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Generated;

@Entity
public class CrashMessageBean {
    @Id
    @SerializedName("msgId")
    private String msgId;
    @SerializedName("headline")
    private String headline;
    @SerializedName("eventDescription")
    private String eventDescription;
    @SerializedName("headlinePath")
    private String headlinePath;
    @SerializedName("eventDescriptionPath")
    private String eventDescriptionPath;
    /**
     * 一句话简介
     */
    @SerializedName("eventDescriptionExtract")
    private String eventDescriptionExtract;
    @SerializedName("sendTime")

     
    private String sendTime;
    /**
     * 插播类型 0-立即插播  1-延时插播 2-非插播消息
     */
    @SerializedName("playType")
    private String playType;
    /**
     * 消息紧急类型 一共三级
     * 1 灾害预警（红色）、重大紧急突发事件等
     * 2 云听应急广播（橙）
     * 3 云听应急广播（黄）
     */
    @SerializedName("msgLevel")
    private String msgLevel;
    /**
     * 消息内容类型
     */
    @SerializedName("msgContentType")
    private String msgContentType ;
    @SerializedName("tipsTitle")
    private String tipsTitle;
    @SerializedName("msgDetailsStartTime")
    private String msgDetailsStartTime;
    @SerializedName("msgDetailsEndTime")
    private String msgDetailsEndTime;
    @SerializedName("msgDetailsBgUrl")
    private String msgDetailsBgUrl;

    //用作了大图的配图文字
    @SerializedName("msgDetailsQrUrl")
    private String msgDetailsQrUrl;
    @SerializedName("msgDetailsBtbStyleLeft")
    private int msgDetailsBtbStyleLeft;
    @SerializedName("msgDetailsBtbStyleRight")
    private int msgDetailsBtbStyleRight;
    @SerializedName("msgDetailsBtnTextLeft")
    private String msgDetailsBtnTextLeft;
    @SerializedName("msgDetailsBtnTextRight")
    private String msgDetailsBtnTextRight;
    @SerializedName("msgDetailsBtnActionLeft")
    @Convert(converter = CrashMessageButtonActionBeanConverter.class, columnType = String.class)
    private CrashMessageButtonActionBean msgDetailsBtnActionLeft;
    @SerializedName("msgDetailsBtnActionRight")
    @Convert(converter = CrashMessageButtonActionBeanConverter.class, columnType = String.class)
    private CrashMessageButtonActionBean msgDetailsBtnActionRight;
    @SerializedName("emergencyId")
    private String emergencyId;
    @SerializedName("publishTime")
    private String publishTime;
    @SerializedName("sender")
    private String sender;
    @SerializedName("eventType")
    private String eventType;
    @SerializedName("msgStyleType")
    private int msgStyleType;//0-文 1-文+图 2-文+按钮 3-文+图+按钮
    /**
     * 灾害等级:1.红色预警 2.橙色预警 3.黄色预警 4.蓝色预警
     */
    @SerializedName("eventLevel")
    private String eventLevel;
    @SerializedName("isLook")
    private boolean isLook;
    @SerializedName("msgTipsPicUrl")
    private String msgTipsPicUrl;
    /**
     * 在线电台版消息小卡背景图
     */
    @SerializedName("cardBgUrl")
    private String cardBgUrl;
    /**
     * 综合版消息小卡背景图
     */
    @SerializedName("comprehensiveCardBgUrl ")
    private String comprehensiveCardBgUrl ;

    /**
     * 消息泡泡/卡片处共用的ICON
     */
    @SerializedName("msgIconUrl")
    private String msgIconUrl ;

    @SerializedName("msgBubbleBtnActionLeft")
    @Convert(converter = CrashMessageButtonActionBeanConverter.class, columnType = String.class)
    private CrashMessageButtonActionBean msgBubbleBtnActionLeft;
    @SerializedName("msgBubbleBtnActionRight")
    @Convert(converter = CrashMessageButtonActionBeanConverter.class, columnType = String.class)
    private CrashMessageButtonActionBean msgBubbleBtnActionRight;
    /**
     * 01:应急广播;02:活动推广;03:内容推荐;04:广告营销;05:直播推广；
     */
    @SerializedName("msgType")
    private String msgType;
    @SerializedName("startTime")
    private long startTime;
    @SerializedName("endTime")
    private long endTime;
    @SerializedName("msgPicUrl")
    private String msgPicUrl;
    @SerializedName("msgBubbleBtnTextLeft")
    private String msgBubbleBtnTextLeft;
    @SerializedName("msgBubbleBtnTextRight")
    private String msgBubbleBtnTextRight;
    @SerializedName("msgDetailsPicUrl")
    private String msgDetailsPicUrl;
    @SerializedName("msgDetailsPicTips")
    private String msgDetailsPicTips;

    @Convert(converter = CrashMessageTypeDataBeanConverter.class, columnType = String.class)
    @SerializedName("typeData")
    private CrashMessageTypeDataBean typeDataBean;

    @SerializedName("msgTime")
    private String msgTime;

    @SerializedName("msgQrUrl")
    private String msgQrUrl;

    public String getMsgQrUrl() {
        return this.msgQrUrl;
    }

    public void setMsgQrUrl(String msgQrUrl) {
        this.msgQrUrl = msgQrUrl;
    }

    public String getMsgTime() {
        return msgTime;
    }

    public void setMsgTime(String msgTime) {
        this.msgTime = msgTime;
    }

    public CrashMessageTypeDataBean getTypeDataBean() {
        return typeDataBean;
    }

    public void setTypeDataBean(CrashMessageTypeDataBean typeDataBean) {
        this.typeDataBean = typeDataBean;
    }

    public String getMsgDetailsPicTips(){
        return this.msgDetailsPicTips;
    }

    public void setMsgDetailsPicTips(String msgDetailsPicTips){
        this.msgDetailsPicTips = msgDetailsPicTips;
    }

    public String getMsgPicUrl() {
        return msgPicUrl;
    }

    public void setMsgPicUrl(String msgPicUrl) {
        this.msgPicUrl = msgPicUrl;
    }

    public String getMsgBubbleBtnTextLeft() {
        return msgBubbleBtnTextLeft;
    }

    public void setMsgBubbleBtnTextLeft(String msgBubbleBtnTextLeft) {
        this.msgBubbleBtnTextLeft = msgBubbleBtnTextLeft;
    }

    public String getMsgBubbleBtnTextRight() {
        return msgBubbleBtnTextRight;
    }

    public void setMsgBubbleBtnTextRight(String msgBubbleBtnTextRight) {
        this.msgBubbleBtnTextRight = msgBubbleBtnTextRight;
    }

    public String getMsgDetailsPicUrl() {
        return msgDetailsPicUrl;
    }

    public void setMsgDetailsPicUrl(String msgDetailsPicUrl) {
        this.msgDetailsPicUrl = msgDetailsPicUrl;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public String getMsgType(){
        return this.msgType;
    }

    public void setMsgType(String msgType){
        this.msgType = msgType;
    }

    public void setMsgBubbleBtnActionLeft(CrashMessageButtonActionBean bean){
        this.msgBubbleBtnActionLeft = bean;
    }
    public CrashMessageButtonActionBean getMsgBubbleBtnActionLeft(){
        return this.msgBubbleBtnActionLeft;
    }
    public void setMsgBubbleBtnActionRight(CrashMessageButtonActionBean bean){
        this.msgBubbleBtnActionRight = bean;
    }
    public CrashMessageButtonActionBean getMsgBubbleBtnActionRight(){
        return this.msgBubbleBtnActionRight;
    }

    public CrashMessageBean() {
    }

    @Generated(hash = 1350818934)
    public CrashMessageBean(String msgId, String headline, String eventDescription, String headlinePath, String eventDescriptionPath,
            String eventDescriptionExtract, String sendTime, String playType, String msgLevel, String msgContentType, String tipsTitle,
            String msgDetailsStartTime, String msgDetailsEndTime, String msgDetailsBgUrl, String msgDetailsQrUrl, int msgDetailsBtbStyleLeft,
            int msgDetailsBtbStyleRight, String msgDetailsBtnTextLeft, String msgDetailsBtnTextRight,
            CrashMessageButtonActionBean msgDetailsBtnActionLeft, CrashMessageButtonActionBean msgDetailsBtnActionRight, String emergencyId,
            String publishTime, String sender, String eventType, int msgStyleType, String eventLevel, boolean isLook, String msgTipsPicUrl,
            String cardBgUrl, String comprehensiveCardBgUrl, String msgIconUrl, CrashMessageButtonActionBean msgBubbleBtnActionLeft,
            CrashMessageButtonActionBean msgBubbleBtnActionRight, String msgType, long startTime, long endTime, String msgPicUrl,
            String msgBubbleBtnTextLeft, String msgBubbleBtnTextRight, String msgDetailsPicUrl, String msgDetailsPicTips,
            CrashMessageTypeDataBean typeDataBean, String msgTime, String msgQrUrl) {
        this.msgId = msgId;
        this.headline = headline;
        this.eventDescription = eventDescription;
        this.headlinePath = headlinePath;
        this.eventDescriptionPath = eventDescriptionPath;
        this.eventDescriptionExtract = eventDescriptionExtract;
        this.sendTime = sendTime;
        this.playType = playType;
        this.msgLevel = msgLevel;
        this.msgContentType = msgContentType;
        this.tipsTitle = tipsTitle;
        this.msgDetailsStartTime = msgDetailsStartTime;
        this.msgDetailsEndTime = msgDetailsEndTime;
        this.msgDetailsBgUrl = msgDetailsBgUrl;
        this.msgDetailsQrUrl = msgDetailsQrUrl;
        this.msgDetailsBtbStyleLeft = msgDetailsBtbStyleLeft;
        this.msgDetailsBtbStyleRight = msgDetailsBtbStyleRight;
        this.msgDetailsBtnTextLeft = msgDetailsBtnTextLeft;
        this.msgDetailsBtnTextRight = msgDetailsBtnTextRight;
        this.msgDetailsBtnActionLeft = msgDetailsBtnActionLeft;
        this.msgDetailsBtnActionRight = msgDetailsBtnActionRight;
        this.emergencyId = emergencyId;
        this.publishTime = publishTime;
        this.sender = sender;
        this.eventType = eventType;
        this.msgStyleType = msgStyleType;
        this.eventLevel = eventLevel;
        this.isLook = isLook;
        this.msgTipsPicUrl = msgTipsPicUrl;
        this.cardBgUrl = cardBgUrl;
        this.comprehensiveCardBgUrl = comprehensiveCardBgUrl;
        this.msgIconUrl = msgIconUrl;
        this.msgBubbleBtnActionLeft = msgBubbleBtnActionLeft;
        this.msgBubbleBtnActionRight = msgBubbleBtnActionRight;
        this.msgType = msgType;
        this.startTime = startTime;
        this.endTime = endTime;
        this.msgPicUrl = msgPicUrl;
        this.msgBubbleBtnTextLeft = msgBubbleBtnTextLeft;
        this.msgBubbleBtnTextRight = msgBubbleBtnTextRight;
        this.msgDetailsPicUrl = msgDetailsPicUrl;
        this.msgDetailsPicTips = msgDetailsPicTips;
        this.typeDataBean = typeDataBean;
        this.msgTime = msgTime;
        this.msgQrUrl = msgQrUrl;
    }

    public String getComprehensiveCardBgUrl() {
        return comprehensiveCardBgUrl;
    }

    public void setComprehensiveCardBgUrl(String comprehensiveCardBgUrl) {
        this.comprehensiveCardBgUrl = comprehensiveCardBgUrl;
    }

    public String getCardBgUrl() {
        return cardBgUrl;
    }

    public void setCardBgUrl(String cardBgUrl) {
        this.cardBgUrl = cardBgUrl;
    }

    public String getEventDescriptionExtract() {
        return eventDescriptionExtract;
    }

    public void setEventDescriptionExtract(String eventDescriptionExtract) {
        this.eventDescriptionExtract = eventDescriptionExtract;
    }

    public void setMsgTipsPicUrl(String msgTipsPicUrl) {
        this.msgTipsPicUrl = msgTipsPicUrl;
    }

    public String getMsgContentType() {
        return msgContentType;
    }

    public void setMsgContentType(String msgContentType) {
        this.msgContentType = msgContentType;
    }

    public int getMsgStyleType() {
        return msgStyleType;
    }

    public void setMsgStyleType(int msgStyleType) {
        this.msgStyleType = msgStyleType;
    }

    public boolean isLook() {
        return isLook;
    }

    public void setLook(boolean look) {
        isLook = look;
    }

    public String getMsgId() {
        return this.msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }

    public String getHeadline() {
        return this.headline;
    }

    public void setHeadline(String headline) {
        this.headline = headline;
    }

    public String getEventDescription() {
        return this.eventDescription;
    }

    public void setEventDescription(String eventDescription) {
        this.eventDescription = eventDescription;
    }

    public String getHeadlinePath() {
        return this.headlinePath;
    }

    public void setHeadlinePath(String headlinePath) {
        this.headlinePath = headlinePath;
    }

    public String getEventDescriptionPath() {
        return this.eventDescriptionPath;
    }

    public void setEventDescriptionPath(String eventDescriptionPath) {
        this.eventDescriptionPath = eventDescriptionPath;
    }

    public String getSendTime() {
        return this.sendTime;
    }

    public void setSendTime(String sendTime) {
        this.sendTime = sendTime;
    }

    public String getPlayType() {
        return this.playType;
    }

    public void setPlayType(String playType) {
        this.playType = playType;
    }

    public String getMsgLevel() {
        return this.msgLevel;
    }

    public void setMsgLevel(String msgLevel) {
        this.msgLevel = msgLevel;
    }

    public String getTipsTitle() {
        return this.tipsTitle;
    }

    public void setTipsTitle(String tipsTitle) {
        this.tipsTitle = tipsTitle;
    }

    public String getMsgDetailsStartTime() {
        return this.msgDetailsStartTime;
    }

    public void setMsgDetailsStartTime(String msgDetailsStartTime) {
        this.msgDetailsStartTime = msgDetailsStartTime;
    }

    public String getMsgDetailsEndTime() {
        return this.msgDetailsEndTime;
    }

    public void setMsgDetailsEndTime(String msgDetailsEndTime) {
        this.msgDetailsEndTime = msgDetailsEndTime;
    }

    public String getMsgDetailsBgUrl() {
        return this.msgDetailsBgUrl;
    }

    public void setMsgDetailsBgUrl(String msgDetailsBgUrl) {
        this.msgDetailsBgUrl = msgDetailsBgUrl;
    }

    public String getMsgDetailsQrUrl() {
        return this.msgDetailsQrUrl;
    }

    public void setMsgDetailsQrUrl(String msgDetailsQrUrl) {
        this.msgDetailsQrUrl = msgDetailsQrUrl;
    }

    public int getMsgDetailsBtbStyleLeft() {
        return this.msgDetailsBtbStyleLeft;
    }

    public void setMsgDetailsBtbStyleLeft(int msgDetailsBtbStyleLeft) {
        this.msgDetailsBtbStyleLeft = msgDetailsBtbStyleLeft;
    }

    public int getMsgDetailsBtbStyleRight() {
        return this.msgDetailsBtbStyleRight;
    }

    public void setMsgDetailsBtbStyleRight(int msgDetailsBtbStyleRight) {
        this.msgDetailsBtbStyleRight = msgDetailsBtbStyleRight;
    }

    public String getMsgDetailsBtnTextLeft() {
        return this.msgDetailsBtnTextLeft;
    }

    public void setMsgDetailsBtnTextLeft(String msgDetailsBtnTextLeft) {
        this.msgDetailsBtnTextLeft = msgDetailsBtnTextLeft;
    }

    public String getMsgDetailsBtnTextRight() {
        return this.msgDetailsBtnTextRight;
    }

    public void setMsgDetailsBtnTextRight(String msgDetailsBtnTextRight) {
        this.msgDetailsBtnTextRight = msgDetailsBtnTextRight;
    }

    public CrashMessageButtonActionBean getMsgDetailsBtnActionLeft() {
        return this.msgDetailsBtnActionLeft;
    }

    public void setMsgDetailsBtnActionLeft(CrashMessageButtonActionBean msgDetailsBtnActionLeft) {
        this.msgDetailsBtnActionLeft = msgDetailsBtnActionLeft;
    }

    public CrashMessageButtonActionBean getMsgDetailsBtnActionRight() {
        return this.msgDetailsBtnActionRight;
    }

    public void setMsgDetailsBtnActionRight(CrashMessageButtonActionBean msgDetailsBtnActionRight) {
        this.msgDetailsBtnActionRight = msgDetailsBtnActionRight;
    }

    public String getEmergencyId() {
        return this.emergencyId;
    }

    public void setEmergencyId(String emergencyId) {
        this.emergencyId = emergencyId;
    }

    public String getPublishTime() {
        return this.publishTime;
    }

    public void setPublishTime(String publishTime) {
        this.publishTime = publishTime;
    }

    public String getSender() {
        return this.sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public String getEventType() {
        return this.eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public String getEventLevel() {
        if (TextUtils.isEmpty(this.eventLevel)) {
            this.eventLevel = "0";
        }
        return this.eventLevel;
    }

    public void setEventLevel(String eventLevel) {
        this.eventLevel = eventLevel;
    }

    public boolean getIsLook() {
        return this.isLook;
    }

    public void setIsLook(boolean isLook) {
        this.isLook = isLook;
    }

    public String getMsgTipsPicUrl() {
        return this.msgTipsPicUrl;
    }

    public void setMsgIconUrl(String msgIconUrl){
        this.msgIconUrl = msgIconUrl;
    }
    public String getMsgIconUrl(){
        return this.msgIconUrl;
    }

    @Override
    public String toString() {
        return "CrashMessageBean{" +
                "msgId='" + msgId + '\'' +
                ", headline='" + headline + '\'' +
                ", eventDescription='" + eventDescription + '\'' +
                ", headlinePath='" + headlinePath + '\'' +
                ", eventDescriptionPath='" + eventDescriptionPath + '\'' +
                ", eventDescriptionExtract='" + eventDescriptionExtract + '\'' +
                ", sendTime='" + sendTime + '\'' +
                ", playType='" + playType + '\'' +
                ", msgLevel='" + msgLevel + '\'' +
                ", msgContentType='" + msgContentType + '\'' +
                ", tipsTitle='" + tipsTitle + '\'' +
                ", msgDetailsStartTime='" + msgDetailsStartTime + '\'' +
                ", msgDetailsEndTime='" + msgDetailsEndTime + '\'' +
                ", msgDetailsBgUrl='" + msgDetailsBgUrl + '\'' +
                ", msgDetailsQrUrl='" + msgDetailsQrUrl + '\'' +
                ", msgDetailsBtbStyleLeft=" + msgDetailsBtbStyleLeft +
                ", msgDetailsBtbStyleRight=" + msgDetailsBtbStyleRight +
                ", msgDetailsBtnTextLeft='" + msgDetailsBtnTextLeft + '\'' +
                ", msgDetailsBtnTextRight='" + msgDetailsBtnTextRight + '\'' +
                ", msgDetailsBtnActionLeft='" + msgDetailsBtnActionLeft + '\'' +
                ", msgDetailsBtnActionRight='" + msgDetailsBtnActionRight + '\'' +
                ", emergencyId='" + emergencyId + '\'' +
                ", publishTime='" + publishTime + '\'' +
                ", sender='" + sender + '\'' +
                ", eventType='" + eventType + '\'' +
                ", msgStyleType=" + msgStyleType +
                ", eventLevel='" + eventLevel + '\'' +
                ", isLook=" + isLook +
                ", msgTipsPicUrl='" + msgTipsPicUrl + '\'' +
                ", cardBgUrl='" + cardBgUrl + '\'' +
                ", msgQrUrl='" + msgQrUrl + '\'' +
                ", comprehensiveCardBgUrl='" + comprehensiveCardBgUrl + '\'' +
                ", typeDataBean='" + typeDataBean + '\'' +
                ", msgIconUrl='" + msgIconUrl + '\'' +
                '}';
    }
}
