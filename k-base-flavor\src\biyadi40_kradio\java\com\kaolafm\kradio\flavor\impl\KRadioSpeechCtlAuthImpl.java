package com.kaolafm.kradio.flavor.impl;

import android.app.Application;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioSpeechCtlAuthInter;
import com.kaolafm.kradio.lib.utils.SharedPreferenceUtil;

import static com.kaolafm.kradio.user.UserInfoDataMemory.USER_INFO;

public class KRadioSpeechCtlAuthImpl implements KRadioSpeechCtlAuthInter {

    private final String KEY_SPEECH_AUTH_BYD = "key_speech_auth_byd";
    private String TAG = "KRadioSpeechCtlAuthImpl";

    @Override
    public void checkAuth() {
        Application context = AppDelegate.getInstance().getContext();
        SharedPreferenceUtil sp = SharedPreferenceUtil.getInstance(context, USER_INFO, Context.MODE_PRIVATE);
        boolean isAuth = sp.getBoolean(KEY_SPEECH_AUTH_BYD, false);
        if (!isAuth) {
            String EXTRA_PACKAGE_NAME = context.getPackageName(); //应用包名
            String EXTRA_APPLICATION_TYPE = "MUSIC";//应用类型
            String ISCONTROL = "TRUE"; //应用是否支持智能语音控制，true 支持，

            Intent intent = new Intent();
            intent.putExtra("EXTRA_PACKAGE_NAME", EXTRA_PACKAGE_NAME);//类型为字符串
            intent.putExtra("EXTRA_APPLICATION_TYPE", EXTRA_APPLICATION_TYPE);//类型为字符串
            intent.putExtra("ISCONTROL", ISCONTROL); //类型为字符串

            context.sendBroadcast(intent);
            Log.i(TAG, "check BYD SpeechCtl Auth...");
        }
    }

}
