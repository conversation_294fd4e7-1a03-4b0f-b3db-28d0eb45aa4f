package com.kaolafm.kradio.online.common.web;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.net.http.SslError;
import android.os.Build;
import android.os.Bundle;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.SslErrorHandler;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ImageView;
import android.widget.TextView;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.kaolafm.kradio.common.base.BaseSkinAppCompatActivity;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.mvp.IPresenter;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.router.IRouterConsumer;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.PageShowReportEvent;

import static com.kaolafm.kradio.lib.utils.Constants.ROUTER_PARAMS_KEY_WEB_PAGE_ID;
import static com.kaolafm.kradio.lib.utils.Constants.ROUTER_PARAMS_KEY_WEB_TITLE;
import static com.kaolafm.kradio.lib.utils.Constants.ROUTER_PARAMS_KEY_WEB_URL;

/**
 * theme 主题 : dark 黑色 light 白色 默认白色
 * bgColor 自定义背景颜色 例：ffffff 透明 transparent 不传默认根据theme变化
 * fontColor 字体颜色 例：000000 不传默认根据theme变化
 * titleColor 标题颜色 例：000000 不传默认根据theme变化260020
 * titleBottomColor 标题底部线颜色 例：000000 不传默认根据theme变化
 * showTitle 是否显示标题 0 显示 1 不显示 默认为 0
 * titleSize 标题字体大小 例：18 默认 16px
 * contentSize 正文字体大小 例：16 默认 14px
 * marginL 正文左间距 例：20 默认 20px
 * marginR 正文右间距 例：20 默认 20px
 * marginT 正文上间距 例：110 默认 110px
 * 解释：所有参数根据需要要拼接即可
 */
@Route(path = RouterConstance.ONLINE_URL_AD_WEBVIEW)
public class OnlineWebViewActivity extends BaseSkinAppCompatActivity implements IRouterConsumer {

    private WebView mWebView;
    private TextView bbf_center_title;
    private View line;
    //    private ImageView mCloseView;
    private ConstraintLayout rootLayout;
    private long startTime = -1;
    private String pageId = "";

    public static void start(Context context, String url) {
        Intent intent = new Intent(context, OnlineWebViewActivity.class);
        intent.putExtra(ROUTER_PARAMS_KEY_WEB_URL, url);
        context.startActivity(intent);
    }

    public static void start(Context context, String url, String title, String pageId) {
        Intent intent = new Intent(context, OnlineWebViewActivity.class);
        intent.putExtra(ROUTER_PARAMS_KEY_WEB_URL, url);
        intent.putExtra(ROUTER_PARAMS_KEY_WEB_TITLE, title);
        intent.putExtra(ROUTER_PARAMS_KEY_WEB_PAGE_ID, pageId);
        context.startActivity(intent);
    }

    @Override
    public int getLayoutId() {
        return R.layout.online_activity_webview;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        String url = getIntent().getStringExtra(ROUTER_PARAMS_KEY_WEB_URL);
        String title = getIntent().getStringExtra(ROUTER_PARAMS_KEY_WEB_TITLE);
        pageId = getIntent().getStringExtra(ROUTER_PARAMS_KEY_WEB_PAGE_ID);
        if (!TextUtils.isEmpty(pageId)) {
            ReportHelper.getInstance().setPage(pageId);
        }
        if (title == null) {
            title = "";
        }
        startTime = System.currentTimeMillis();

        Log.i("OnlineWebViewActivity", "url:" + url);

        mWebView = findViewById(R.id.webview);
        bbf_center_title = findViewById(R.id.bbf_center_title);
        line = findViewById(R.id.line);
        rootLayout = findViewById(R.id.rootLayout);
        bbf_center_title.setText(title);
        if (!TextUtils.isEmpty(title)) {
            line.setVisibility(View.VISIBLE);
            ConstraintSet constraintSet = new ConstraintSet();
            constraintSet.clone(rootLayout);
            constraintSet.clear(mWebView.getId(), ConstraintSet.TOP);
            constraintSet.connect(mWebView.getId(), ConstraintSet.TOP, line.getId(), ConstraintSet.BOTTOM);
            constraintSet.applyTo(rootLayout);
        }
        mWebView.getSettings().setSavePassword(false);
        if (Build.VERSION.SDK_INT < 19 && mWebView != null) {
            mWebView.removeJavascriptInterface("searchBoxJavaBridge_");
            mWebView.removeJavascriptInterface("accessibility");
            mWebView.removeJavascriptInterface("accessibilityTraversal");
        }

//        mCloseView = findViewById(R.id.webview_close);
//        mCloseView.setOnClickListener(view1 -> {
//            finish();
//        });

        if (TextUtils.isEmpty(url)) {
            return;
        }

//        setCloseViewDrawable();
        if (backView instanceof ImageView) {
            ((ImageView) backView).setImageDrawable(ResUtil.getDrawable(R.drawable.online_player_ic_back));
        }
        line.setBackgroundColor(ResUtil.getColor(R.color.online_web_view_line_color));
        bbf_center_title.setTextColor(ResUtil.getColor(R.color.global_title_text_color));

        setWebViewSetting();

        mWebView.loadUrl(url);
    }

//    private void setCloseViewDrawable() {
//        if (backView instanceof ImageView) {
//            if (SkinHelper.isNightMode()) {
//                ((ImageView) backView).setImageTintList(ResUtil.getc(R.drawable.globle_arrow_normal_white));
//            } else {
//                ((ImageView) backView).setImageDrawable(ResUtil.getDrawable(R.drawable.globle_arrow_normal));
//            }
//        }
//    }

    @Override
    public void initData() {

    }

    @Override
    public void onResume() {
        if (mWebView != null) {
            mWebView.onResume();
        }
        super.onResume();
    }

    @Override
    public void onPause() {
        super.onPause();
        if (mWebView != null) {
            mWebView.onPause();
        }
    }

    @Override
    protected IPresenter createPresenter() {
        return null;
    }

    private void setWebViewSetting() {
        WebSettings settings = mWebView.getSettings();


        settings.setUseWideViewPort(true);

        settings.setJavaScriptEnabled(true);
        settings.setSavePassword(false);
        //自动加载图片
        settings.setLoadsImagesAutomatically(true);

        settings.setCacheMode(WebSettings.LOAD_NO_CACHE);//不使用缓存，只从网络获取数据.
        mWebView.setBackgroundColor(Color.TRANSPARENT);
//        mWebView.getBackground().setAlpha(1);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            // 5.0以上允许加载http和https混合的页面(5.0以下默认允许，5.0+默认禁止)
            settings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        }

        mWebView.setWebViewClient(new WebViewClient() {

            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                //可处理loading开始
                super.onPageStarted(view, url, favicon);
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                //可处理loading结束
                super.onPageFinished(view, url);
                settings.setJavaScriptEnabled(false);
            }

            @Override
            public void onReceivedSslError(WebView view, SslErrorHandler handler, SslError error) {
                handler.cancel();
            }
        });
    }


    @Override
    protected void onDestroy() {
        startTime = System.currentTimeMillis() - startTime;
//        ReportHelper.getInstance().setPage(pageId);
        PageShowReportEvent event = new PageShowReportEvent();
        event.setPage(pageId);
        event.setPageId(pageId);
        event.setPageTime(String.valueOf(startTime));
        ReportHelper.getInstance().addEvent(event);
        startTime = -1;
        if (mWebView != null) {
            ViewGroup parent = (ViewGroup) mWebView.getParent();
            if (parent != null) {
                parent.removeView(mWebView);
            }

            mWebView.stopLoading();
            // 退出时调用此方法，移除绑定的服务，否则某些特定系统会报错
            mWebView.getSettings().setJavaScriptEnabled(false);
            mWebView.clearHistory();
            mWebView.clearCache(true);
            mWebView.clearView();
            mWebView.removeAllViews();
            mWebView.destroy();

            mWebView = null;
        }
        super.onDestroy();
    }

    @Override
    public String consumeRoute(String pageId, Object extra) {
        switch (pageId) {
            case Constants.ONLINE_PAGE_ID_PAY_VIP: //VIP购买
                return pageId;
        }
        return ROUTER_CONSUME_FULLY;
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        interceptApplicationJumpEvent(intent);
    }

    private void interceptApplicationJumpEvent(Intent intent) {
        String title, url;
        int pageId;
        if (intent.hasExtra(Constants.ROUTER_PARAMS_KEY_WEB_URL)) {
            url = intent.getStringExtra(Constants.ROUTER_PARAMS_KEY_WEB_URL);
            if (StringUtil.isNotEmpty(url))
                mWebView.loadUrl(url);
        }
        if (intent.hasExtra(Constants.ROUTER_PARAMS_KEY_WEB_TITLE)) {
            title = intent.getStringExtra(Constants.ROUTER_PARAMS_KEY_WEB_TITLE);
            bbf_center_title.setText(title);
            if (StringUtil.isNotEmpty(title)) {
                line.setVisibility(View.VISIBLE);
                ConstraintSet constraintSet = new ConstraintSet();
                constraintSet.clone(rootLayout);
                constraintSet.clear(mWebView.getId(), ConstraintSet.TOP);
                constraintSet.connect(mWebView.getId(), ConstraintSet.TOP, line.getId(), ConstraintSet.BOTTOM);
                constraintSet.applyTo(rootLayout);
            } else {
                line.setVisibility(View.GONE);
            }
        }

        if (intent.hasExtra(Constants.ROUTER_PARAMS_KEY_WEB_PAGE_ID)) {
            pageId = intent.getIntExtra(Constants.ROUTER_PARAMS_KEY_WEB_PAGE_ID, 0);
            if (pageId != 0) {
                this.pageId = String.valueOf(pageId);
                ReportHelper.getInstance().setPage(String.valueOf(this.pageId));
            } else {
                this.pageId = "";
                ReportHelper.getInstance().setPage(String.valueOf(this.pageId));
            }
        }


    }
}