package com.kaolafm.kradio.category.radio.tab;

import androidx.fragment.app.Fragment;
import android.util.Log;

import com.kaolafm.kradio.category.ErrorCode;
import com.kaolafm.kradio.category.FragmentFactory;
import com.kaolafm.kradio.category.base.TabContract;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.opensdk.api.operation.model.category.Category;
import com.kaolafm.opensdk.api.operation.model.category.LeafCategory;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 **/
class RadioTabPresenter extends BasePresenter<RadioTabModel, TabContract.IView> implements TabContract.IPresenter {

    private long mItemId = 0;

    public RadioTabPresenter(TabContract.IView view, long sonCode) {
        super(view);
        mItemId = sonCode;
    }

    @Override
    protected RadioTabModel createModel() {
        return new RadioTabModel();
    }

    @Override
    public void loadAIData(long showTabId) {
        Log.i("RadioTabPresenter", "loadAIData: " + showTabId);
        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
            if (mView != null) {
                mView.showError(new ApiException(ErrorCode.NO_NET, "数据为空"));
            }
            return;
        }
        mModel.getSubcategoryList(String.valueOf(showTabId), new HttpCallback<List<Category>>() {
            @Override
            public void onSuccess(List<Category> categories) {
                if (categories != null && categories.size() > 0) {
//                    获取AI电台 常见电台的 次级分类
                    loadData(Long.parseLong(categories.get(0).getCode()));
                } else if (mView != null) {
                    mView.showError(new ApiException(ErrorCode.NO_NET, "数据为空"));
                }
            }

            @Override
            public void onError(ApiException e) {
                if (mView != null) {
                    mView.showError(new ApiException(ErrorCode.NO_NET, "数据为空"));
                }
            }
        });
    }


    @Override
    public void loadData(long parentId) {
        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
            if (mView != null) {
                mView.showError(new ApiException(ErrorCode.NO_NET, "数据为空"));
            }
            return;
        }
        mModel.getSubcategoryList(String.valueOf(parentId), new HttpCallback<List<Category>>() {
            @Override
            public void onSuccess(List<Category> categories) {
                showSubcategoryFragments(categories);
            }

            @Override
            public void onError(ApiException e) {
                if (mView != null) {
                    mView.showError(new ApiException(ErrorCode.NO_NET, "数据为空"));
                }
            }
        });
    }

    private void showSubcategoryFragments(List<Category> categories) {
        List<Fragment> fs = new ArrayList<>();
        String[] titles = new String[categories.size()];
        int mTempIndex = 0;
        //遍历接口
        if (categories.size() > 0) {
            for (int i = 0; i < categories.size(); i++) {
                Category category = categories.get(i);
                long codeLong;
                try {
                    codeLong = Long.parseLong(category.getCode());
                } catch (Exception e) {
                    codeLong = -1;
                }
                if (codeLong > 0 && mItemId > 0){
                    if (codeLong == mItemId) {
                        mTempIndex = i;
                    }
                } else {
                    // 二级导航栏设置落地页赋值
                    if (category.getIsLandingPage() == 1) {
                        mTempIndex = i;
                    }
                }
                String name = category.getName();
                titles[i] = name;
                if (category instanceof LeafCategory) {
                    fs.add(FragmentFactory.createTabItemFragment(Long.valueOf(category.getCode()), true));
                } else {
                    fs.add(FragmentFactory.createTabItemFragment(Long.valueOf(category.getCode()), false));
                }
            }
        }
        if (mView != null) {
            if (titles.length > 0) {
                mView.showData(titles, fs, mTempIndex);
            } else {
                mView.showError(new ApiException("数据为空"));
            }
        }
    }


    /**
     * 根据id,选择tab index
     */
    private int getIndexById(List<Category> tabItems, long showTabId) {
        int index = 0;
        for (int i = 0; i < tabItems.size(); i++) {
            if (Long.valueOf(tabItems.get(i).getCode()) == showTabId) {
                index = i;
                break;
            }
        }
        return index;
    }

    private boolean isNetworkAvailable() {
        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
            mView.showError(new ApiException(ErrorCode.NO_NET, ""));
            return false;
        } else {
            return true;
        }
    }

}
