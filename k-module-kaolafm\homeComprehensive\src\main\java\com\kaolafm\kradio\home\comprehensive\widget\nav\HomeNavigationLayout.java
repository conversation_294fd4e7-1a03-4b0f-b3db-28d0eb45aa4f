package com.kaolafm.kradio.home.comprehensive.widget.nav;

import android.content.Context;
import android.content.res.Configuration;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.GridLayoutManager.SpanSizeLookup;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.LayoutManager;
import androidx.recyclerview.widget.RecyclerView.OnScrollListener;

import android.graphics.drawable.GradientDrawable;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import com.kaolafm.kradio.home.comprehensive.adapter.HomeAdapter;
import com.kaolafm.kradio.k_kaolafm.BuildConfig;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.home.comprehensive.item.HomeCard;
import com.kaolafm.kradio.lib.base.flavor.KRadioLocationTabInter;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.widget.tab.OnTabSelectListener;
import com.kaolafm.kradio.lib.widget.tab.SlidingTabLayout;
import com.kaolafm.kradio.lib.widget.tab.Tab;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.util.ReportConstants;

import java.util.List;


/**
 * 首页导航栏。包括了导航tab和所有分类按钮。导航栏相关逻辑都在该类中完成。
 *
 * <AUTHOR> Yan
 * @date 2019-08-20
 */
public class HomeNavigationLayout extends ConstraintLayout {
    private static final String TAG = "HomeNavigationLayout";

    TextView mIvHomeNavAllcategrayMore;
    SlidingTabLayout mSrtlHomeNavigationTab;
    View view_zhe;

    private int mFirstVisibleItemPosition = -1;
    private float mFontWidth = 0.3f;//tab选中时的字重

    private RecyclerView mRecyclerView;
    private IHomeNavigationChangeListener iHomeNavigationChangeListener;
    private String mPageId = Constants.PAGE_ID_MAIN;    //所在页面的pageId，用于数据上报

    public HomeNavigationLayout(Context context) {
        this(context, null);
    }

    public HomeNavigationLayout(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public HomeNavigationLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        int layout = R.layout.view_home_navigation;
        if (BuildConfig.LAYOUT_TYPE == 1) {
            layout = R.layout.view_home_navigation_tow;
        }
        View view = LayoutInflater.from(getContext()).inflate(layout, this, false);
        ConstraintLayout.LayoutParams layoutParams = (LayoutParams) view.getLayoutParams();
        layoutParams.topToTop = ConstraintSet.PARENT_ID;
        layoutParams.bottomToBottom = ConstraintSet.PARENT_ID;
        view.setLayoutParams(layoutParams);
        addView(view);

        mIvHomeNavAllcategrayMore=view.findViewById(R.id.iv_home_nav_allcategray_more);
        mSrtlHomeNavigationTab=view.findViewById(R.id.srtl_home_navigation_tab);
        view_zhe=view.findViewById(R.id.view_zhe);


        setFocusable(false);
        mSrtlHomeNavigationTab.setOnTabSelectListener(new OnTabSelectListener() {
            @Override
            public void onTabSelect(int position) {
                mSrtlHomeNavigationTab.getTab(position).tabView.setFontWidth(mFontWidth);
                mSrtlHomeNavigationTab.getTab(currentPosition).tabView.setFontWidth(0f);
                currentPosition = position;
                scrollToTargetPosition(position);
            }

            @Override
            public void onTabReselect(int position) {
            }
        });
        mSrtlHomeNavigationTab.setOnScrollChangeListener(this::showMongolian);
        showAccordingToScreen(ResUtil.getOrientation());

        resetViewRadius();
    }

    public Tab getTabAt(int position) {
        if (position >= mSrtlHomeNavigationTab.getTabCount()) return null;
        return mSrtlHomeNavigationTab.getTab(position);
    }

    private void showMongolian(boolean isEnd) {
//        if (isEnd) {
//            ViewUtil.setViewVisibility(mIvHomeNavMongolianLayer, GONE);
//        } else {
//            ViewUtil.setViewVisibility(mIvHomeNavMongolianLayer, VISIBLE);
//        }
    }

    private void scrollToTargetPosition(int position) {
//        HomeAdapter adapter = (HomeAdapter) mRecyclerView.getAdapter();
//        for (int i = 0, itemCount = adapter.getItemCount(); i < itemCount; i++) {
//            HomeCell cell = adapter.getItemData(i);
//            if (cell != null) {
//                HomeCard parent = (HomeCard) cell.parent;
//                if (parent != null) {
//                    if (position == parent.parentPosition) {
//                        LayoutManager layoutManager = mRecyclerView.getLayoutManager();
//                        if (layoutManager instanceof LinearLayoutManager) {
//                            ((LinearLayoutManager) layoutManager).scrollToPositionWithOffset(i, 0);
//                        }
//                        break;
//                    }
//                }
//            }
//        }
        if (iHomeNavigationChangeListener != null) {
            iHomeNavigationChangeListener.onHomeNavigationChange(position);
        }

    }

    /**
     * 设置导航条选中监听
     *
     * @param iHomeNavigationChangeListener
     */
    public void setHomeNavigationChangeListener(IHomeNavigationChangeListener iHomeNavigationChangeListener) {
        this.iHomeNavigationChangeListener = iHomeNavigationChangeListener;
    }

    public void bindRecyclerView(RecyclerView recyclerView) {
        mRecyclerView = recyclerView;
        mRecyclerView.addOnScrollListener(new OnScrollListener() {
            @Override
            public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                if (recyclerView.getScrollState() != RecyclerView.SCROLL_STATE_IDLE) {
                    setCurrentTab();
                }
            }
        });
    }

    private void setCurrentTab() {
        int itemPosition = getFirstVisibleItemInFirstVisibleRow();
        Log.i(TAG, "setCurrentTab----->" + itemPosition);
        mSrtlHomeNavigationTab.setCurrentTab(itemPosition);
    }

    /**
     * 获取可见的第一行，然后在第一行中查找下一个tab的位置。
     */
    private int getFirstVisibleItemInFirstVisibleRow() {
        LayoutManager layoutManager = mRecyclerView.getLayoutManager();
        HomeAdapter adapter = (HomeAdapter) mRecyclerView.getAdapter();
        int currentPosition = mSrtlHomeNavigationTab.getCurrentPosition();
        if (layoutManager instanceof GridLayoutManager) {
            GridLayoutManager gridLayoutManager = (GridLayoutManager) layoutManager;
            int firstVisibleItemPosition = gridLayoutManager.findFirstVisibleItemPosition();
            int lastVisibleItemPosition = gridLayoutManager.findLastVisibleItemPosition();
            // 解决https://app.huoban.com/tables/2100000007530121/items/2300001548158831?userId=1881599问题
            if (adapter.getItemCount() - 1 == lastVisibleItemPosition) {
                return mSrtlHomeNavigationTab.getTabCount() - 1;
            }

            // 解决屏幕过大滑到最后定位不到广播的问题
            KRadioLocationTabInter locationTabInter = ClazzImplUtil.getInter("KRadioLocationTabImpl");
            Log.i(TAG, "locationTabInter ----->" + (locationTabInter != null));
            if (locationTabInter != null) {
                boolean isLast = locationTabInter.locationLastTab(adapter.getItemCount() - 1, gridLayoutManager.findLastCompletelyVisibleItemPosition());
                if (isLast) {
                    return mSrtlHomeNavigationTab.getTabCount() - 1;
                }
            }
            if (mFirstVisibleItemPosition == firstVisibleItemPosition) {
                return mSrtlHomeNavigationTab.getCurrentPosition();
            }
            mFirstVisibleItemPosition = firstVisibleItemPosition;
            int firstRow = getRowIndex(gridLayoutManager, mFirstVisibleItemPosition);
            int spanCount = gridLayoutManager.getSpanCount();
            for (int i = mFirstVisibleItemPosition; i < mFirstVisibleItemPosition + spanCount; i++) {
                int spanGroupIndex = getRowIndex(gridLayoutManager, i);
                //和第一个item是同一行才行
                if (spanGroupIndex == firstRow) {
                    //捕获一下空指针，判空太多了。
                    try {
                        HomeCard parent = (HomeCard) (adapter.getItemData(i).parent);
                        if (parent.parentPosition != currentPosition) {
                            currentPosition = parent.parentPosition;
                            break;
                        }
                    } catch (NullPointerException ignored) {
                    }
                }
            }
        }
        return currentPosition;
    }

    /**
     * 获取某个位置所在的行数
     *
     * @param position
     * @return
     */
    private int getRowIndex(GridLayoutManager gridLayoutManager, int position) {
        try { //捕获一下空指针，判空太多了。
            int spanCount = gridLayoutManager.getSpanCount();
            SpanSizeLookup spanSizeLookup = gridLayoutManager.getSpanSizeLookup();
            View viewByPosition = gridLayoutManager.findViewByPosition(position);
            int adapterPosition = mRecyclerView.getChildAdapterPosition(viewByPosition);
            return spanSizeLookup.getSpanGroupIndex(adapterPosition, spanCount);
        } catch (NullPointerException e) {
            return 0;
        }
    }

    int currentPosition = 0;

    public void setTabs(List<Tab> tabs) {
        mSrtlHomeNavigationTab.setTabs(tabs);
        //长连接会刷新，如果已经有数据再刷新要保持当前选中的tab不变

        for (int i = 0; i < tabs.size(); i++) {
            if (tabs.get(i).select) {
                currentPosition = i;
            }
        }
        mSrtlHomeNavigationTab.setCurrentTab(currentPosition);
        ViewUtil.setViewVisibility(mIvHomeNavAllcategrayMore, VISIBLE);
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(
                ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_CATEGORIES, mIvHomeNavAllcategrayMore.getText().toString(), getPageId(), ReportConstants.CONTROL_TYPE_SCREEN
        ));
        mSrtlHomeNavigationTab.getTab(currentPosition).tabView.setFontWidth(mFontWidth);
        mSrtlHomeNavigationTab.postDelayed(new Runnable() {
            @Override
            public void run() {
                mSrtlHomeNavigationTab.scrollToCurrentTab();
            }
        }, 200);
        // 始终隐藏遮罩
        view_zhe.setVisibility(GONE);
    }

    public void setOnMoreBtnClickListener(OnClickListener listener) {
        mIvHomeNavAllcategrayMore.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (listener != null) listener.onClick(v);
                ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(
                        ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_CATEGORIES, mIvHomeNavAllcategrayMore.getText().toString(), getPageId(), ReportConstants.CONTROL_TYPE_SCREEN
                ));
            }
        });
    }

    public void resetViewRadius(){
        try {
//            if (mIvHomeNavAllcategrayMore.getBackground() instanceof GradientDrawable){
//                GradientDrawable gradientDrawable = (GradientDrawable) mIvHomeNavAllcategrayMore.getBackground();
//                gradientDrawable.setCornerRadius(ResUtil.getDimen(R.dimen.m23));
//                mIvHomeNavAllcategrayMore.setBackground(gradientDrawable);
//            }
        } catch (Exception e) {
        }
    }

    @Override
    protected void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        showAccordingToScreen(ResUtil.getOrientation());
    }

    private void showAccordingToScreen(int orientation) {
        showMongolian(mSrtlHomeNavigationTab.isLastItem());
        changeHomeNavigationTabSize(ResUtil.getDimen(R.dimen.tab_text_size), ResUtil.getDimen(R.dimen.tab_text_select_size));
    }

    private void changeHomeNavigationTabSize(float textSize, float textSelectSize) {
        Log.i(TAG, "changeHomeNavigationTabSize::" + textSize + " " + textSelectSize);
        mSrtlHomeNavigationTab.setTextSize(textSize, textSelectSize);
    }

    public interface IHomeNavigationChangeListener {
        void onHomeNavigationChange(int position);
    }

    public Tab getCurrentTab() {
        return mSrtlHomeNavigationTab.getCurrentTab();
    }

    /**
     * 设置当前选中的tab
     * @param position tab位置
     */
    public void setCurrentTab(int position) {
        if (mSrtlHomeNavigationTab != null) {
            mSrtlHomeNavigationTab.setCurrentTab(position);
            currentPosition = position;
        }
    }

    public void destroy() {
//        mSrtlHomeNavigationTab.release();
//        mSrtlHomeNavigationTab.clearOnScrollListeners();
    }

    public void setPageId(String pageId) {
        this.mPageId = pageId;
    }

    public String getPageId() {
        return mPageId;
    }
}
