package com.kaolafm.kradio.lib.init;

import android.app.Application;
import android.os.AsyncTask;
import android.util.Log;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.lib.utils.AppUtil;
import com.kaolafm.kradio.lib.utils.YTLogUtil;
import com.kaolafm.opensdk.log.Logging;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * 初始化管理管理类，用户处理各个初始化任务的初始化逻辑。该类只在Application对应的生命周期中调用，其他地方不需要调用。
 * <AUTHOR>
 * @date 2019-09-10
 */
public class AppInitManager {

    private static final String TAG = "AppInitManager";

    static {

    }

    private AppInitManager() {
    }

    private static List<AppInitTask> mAppInitTasks = new ArrayList<>();

    private static BlockingQueue<AppInitTask> mAsyncTaskQueue;

    private static volatile boolean isAsyncTaskFinished = false;

    private static boolean isMainProcess;


    static void registerInitializer(AppInitTask appInitTask) {
        YTLogUtil.logStart(TAG, "registerInitializer", "start");
        if (appInitTask != null) {
            mAppInitTasks.add(appInitTask);
        }
        YTLogUtil.logStart(TAG, "registerInitializer", "end");
    }

    public static void registerInitializers(AppInitTaskContainer container) {
        YTLogUtil.logStart(TAG, "registerInitializers", "container size = " + container.appInitTasks.size());
        if (container != null) {
            mAppInitTasks.addAll(container.appInitTasks);
            if (!ListUtil.isEmpty(container.asyncInitTasks)) {
                //对于异步初始化的task第一次不在这里添加，只是初始化容器。
                if (mAsyncTaskQueue == null) {
                    mAsyncTaskQueue = new ArrayBlockingQueue<>(container.asyncInitTasks.size());
                } else {//如果已经有了其他初始化的task，就需要在扩容以后把原来的添加上，新增的同样不在这里添加。
                    ArrayBlockingQueue<AppInitTask> appInitTasks = new ArrayBlockingQueue<>(mAppInitTasks.size() + container.asyncInitTasks.size());
                    appInitTasks.addAll(mAppInitTasks);
                    mAsyncTaskQueue = appInitTasks;
                }
            }
        }
        YTLogUtil.logStart(TAG, "registerInitializers", "over");
    }

    static public boolean isTaskListEmpty(){
        if (ListUtil.isEmpty(mAppInitTasks)) {
            return true;
        }
        return false;
    }

    public static void onCreate(Application application) {
        YTLogUtil.logStart(TAG, "onCreate", "start");
        if (ListUtil.isEmpty(mAppInitTasks)) {
            return;
        }
        isMainProcess = AppUtil.isMainProcess(application);

        doASyncInitTasks(application);
        doSyncInitTasks(application);

        isAsyncTaskFinished = true;
        YTLogUtil.logStart(TAG, "onCreate", "end");
    }

    private static void doASyncInitTasks(Application application) {
        AsyncTask.THREAD_POOL_EXECUTOR.execute(() -> asyncCreate(application));
    }

    private static void doSyncInitTasks(Application application) {
        YTLogUtil.logStart(TAG, "doSyncInitTasks", "start");
        for (AppInitTask appInitTask : mAppInitTasks) {
            if (isNotIgnore(appInitTask)) {
                appInitTask.initializer.onCreate(application);
                if (appInitTask.isAsync && !mAsyncTaskQueue.contains(appInitTask)) {
                    mAsyncTaskQueue.add(appInitTask);
                }
            }
        }
        YTLogUtil.logStart(TAG, "doSyncInitTasks", "end");
    }

    private static void asyncCreate(Application application) {
        AppInitTask appInitTask;
        try {
            while (true) {
                appInitTask = mAsyncTaskQueue.poll(100, TimeUnit.MILLISECONDS);
                if (appInitTask == null) {
                    if (isAsyncTaskFinished) {
                        break;
                    } else {
                        continue;
                    }
                }
                if (isNotIgnore(appInitTask)) {
                    final AppInitTask finalAppInitTask = appInitTask;
                    time(appInitTask.initializer.getClass().getName() + " asyncCreate()",
                            () -> finalAppInitTask.initializer.asyncCreate(application));
                }
                if (isAsyncTaskFinished && mAsyncTaskQueue.isEmpty()) {
                    break;
                }
            }
        } catch (InterruptedException e) {
            YTLogUtil.logE(TAG, "Error occurred when start AppInitTask", e);
        }
    }

    public static void onTerminate() {
        YTLogUtil.logStart(TAG, "onTerminate", "start");
        time("onTerminate()总的", () -> dispatch(
                initializer -> time(initializer.getClass().getName() + " onTerminate()", initializer::onTerminate)));
    }

    public static void onLowMemory() {
        time("onLowMemory()总的", () -> dispatch(
                initializer -> time(initializer.getClass().getName() + " onLowMemory()", initializer::onLowMemory)));
    }


    public static void onTrimMemory(int level) {
        time("onTrimMemory()总的", () -> dispatch(initializer -> time(
                initializer.getClass().getName() + " onTrimMemory()", () -> initializer.onTrimMemory(level))));
    }

    private static void dispatch(Callback callback) {
        if (!ListUtil.isEmpty(mAppInitTasks)) {
            YTLogUtil.logStart(TAG, "dispatch", "size = " + mAppInitTasks.size());
        }
    }

    private static boolean isNotIgnore(AppInitTask initItem) {
        YTLogUtil.logI(TAG, "isMainProcess =" + isMainProcess + ", initItem.inMainProcess() " + initItem.inMainProcess() + ", initItem.inOtherProcess() = " + initItem.inOtherProcess());
        return (isMainProcess && initItem.inMainProcess()) || (!isMainProcess && initItem.inOtherProcess());
    }

    /**
     * 统计执行时间
     */
    private static long time(Runnable runnable) {
        if (runnable == null) {
            return 0;
        }
        long startTime = System.currentTimeMillis();
        runnable.run();
        return System.currentTimeMillis() - startTime;
    }

    /**
     * 统计执行时间
     */
    private static long time(String desc, Runnable runnable) {
        long time = time(runnable);
        YTLogUtil.logStart(TAG, "time", desc + "耗时:" + time + "ms");
        return time;
    }

    /**
     * 统计执行时间
     */
    static String timeStr(String desc, Runnable runnable) {
        String msg = String.format("%s耗时:%sms\n\n", desc, time(runnable));
        YTLogUtil.logStart(TAG, "timeStr", msg);
        return msg;
    }

    private interface Callback {
        void dispatch(AppInitializable initializer);
    }

}
