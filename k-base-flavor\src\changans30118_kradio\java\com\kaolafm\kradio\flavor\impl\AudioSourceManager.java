package com.kaolafm.kradio.flavor.impl;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.media.AudioManager;
import android.util.Log;

import com.incall.proxy.constant.SourceConstantsDef;
import com.incall.proxy.source.SourceManager;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.opensdk.player.core.model.AAudioFocus;

import static com.incall.proxy.constant.SourceConstantsDef.COAGENT_ACTION_SOURCE_CHANGED;

/**
 * 音源管理类 （替代AudioFocusManager功能）
 */
public class AudioSourceManager extends AAudioFocus {

    private final static String TAG = "AudioSourceManager";
    public static boolean mIsInSource = false;

    public void registerSourceChanged() {
        try {
            SourceManager.getInstance().addChangedListener(sourceInterruptListener);
            AppDelegate.getInstance().getContext().registerReceiver(mSourceReceiver, new IntentFilter(
                    SourceConstantsDef.COAGENT_ACTION_SOURCE_CHANGED));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void unregisterSourceChanged() {
        try {
            SourceManager.getInstance().removeChangedListener(sourceInterruptListener);
            AppDelegate.getInstance().getContext().unregisterReceiver(mSourceReceiver);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Override
    public boolean requestAudioFocus() {
        SourceConstantsDef.SourceID sourceID = SourceManager.getInstance().getCurrentSource();
        Log.i(TAG, "requestAudioSource: sourceID = " + sourceID);
        if (sourceID != null && SourceConstantsDef.SourceID.MULTI_MEDIA.name().equals(sourceID.name())) {
            return true;
        } else {
            return requestAudioFocus();
        }
    }

    @Override
    public boolean abandonAudioFocus() {
        return abandonAudioFocus();
    }


    /**
     * 音源中断监听器，当讯飞语音或tts等叠加源激活时回调此方法，当前音源不变
     * onResume时通过mSourceManager.addChangedListener(sourceInterruptListener)注册监听器
     * onPause时通过mSourceManager.removeChangedListener(sourceInterruptListener)注销监听器
     */
    private final SourceManager.SourceChangedListener sourceInterruptListener = new SourceManager.SourceChangedListener() {


        @Override
        public void onCurrnetInterruptChanged(boolean isInterrupted) {
            SourceConstantsDef.SourceID sourceId = SourceManager.getInstance().getCurrentSource();
            Log.i(TAG, "onCurrnetInterruptChanged:  sourceId = " + sourceId + ", isInterrupted =  " + isInterrupted);
            if (sourceId != SourceConstantsDef.SourceID.MULTI_MEDIA) {
                //不在APP源时，发生中断不做处理
                return;
            }
            if (isInterrupted) {
                //当前为APP源，发生叠加源打断，需停止内部音频播放
                mIsInSource = false;
                notifyAudioFocusChange(AudioManager.AUDIOFOCUS_LOSS);
//                innerKLAudioSourceChangeListener.onAudioSourceChange(AudioManager.AUDIOFOCUS_LOSS);
            } else {
                //当前为APP源，叠加源处理完毕释放时，可恢复内部音频播放
                mIsInSource = true;
                notifyAudioFocusChange(AudioManager.AUDIOFOCUS_GAIN);
//                innerKLAudioSourceChangeListener.onAudioSourceChange(AudioManager.AUDIOFOCUS_GAIN);
            }
        }
    };

    /**
     * 音源切换广播接收器，当音源切换时会发出此广播，需要使用音频场景的各应用监听广播SourceConstantsDef.COAGENT_ACTION_SOURCE_CHANGED，
     * 进入监听源时可进行相关播放逻辑，进入其他源时需停止当前播放逻辑
     */
    private final BroadcastReceiver mSourceReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (COAGENT_ACTION_SOURCE_CHANGED.equals(action)) {
                String sourceFrom = intent.getStringExtra(SourceConstantsDef.EXTRA_SOURCE_FROM);
                String sourceTo = intent.getStringExtra(SourceConstantsDef.EXTRA_SOURCE_TO);
                Log.i(TAG, "SOURCE Switch FROM: " + sourceFrom + ", TO: " + sourceTo);
                if (SourceConstantsDef.SourceID.valueOf(sourceFrom) == SourceConstantsDef.SourceID.MULTI_MEDIA) {
                    //pause all audio
                    mIsInSource = false;
                    notifyAudioFocusChange(AudioManager.AUDIOFOCUS_LOSS);
//                    innerKLAudioSourceChangeListener.onAudioSourceChange(AudioManager.AUDIOFOCUS_LOSS);
                } else if (SourceConstantsDef.SourceID.valueOf(sourceTo) == SourceConstantsDef.SourceID.MULTI_MEDIA) {
                    //resume audio
                    mIsInSource = true;
                    notifyAudioFocusChange(AudioManager.AUDIOFOCUS_GAIN);
//                    innerKLAudioSourceChangeListener.onAudioSourceChange(AudioManager.AUDIOFOCUS_GAIN);
                }
            }

        }
    };


    /**
     * 申请音频焦点-好帮手, 需要播放音频消息前调用此接口申请音源
     * 申请后需等待回调，申请成功方可播放音频
     *
     * @return
     */
    private static boolean requestAudioSource() {
        Log.i(TAG, "requestAudioSource: SourceID.MULTI_MEDIA");
        return SourceManager.getInstance().requestSource(SourceConstantsDef.SourceID.MULTI_MEDIA, false);
    }

    /**
     * 释放音频焦点-好帮手，音频消息播放结束后调用此接口释放源
     *
     * @return
     */
    private static boolean abandonAudioSource() {
        //mSourceManager.releaseSource(SourceID.CAR_CHAT);//拉起下一个音源界面时调用
        Log.i(TAG, "abandonAudioSource: SourceID.MULTI_MEDIA");
        return SourceManager.getInstance().releaseSource(SourceConstantsDef.SourceID.MULTI_MEDIA, false);//保持当前界面，不拉起下一个音源界面时调用
    }
}
