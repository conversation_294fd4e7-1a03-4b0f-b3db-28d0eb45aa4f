package com.kaolafm.kradio.lib.utils;

import android.app.Activity;
import android.os.Build;

import com.kaolafm.kradio.lib.base.AppManager;

/**
 * 分屏工具类
 */
public class MultiUtil {
    public static boolean isInMultiWindowMode = false;

    public static boolean getMultiStatus() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            try {
                Activity mainActivity = AppManager.getInstance().getMainActivity();
                if (mainActivity == null) return false;
                return mainActivity.isInMultiWindowMode();
            } catch (NoSuchFieldError error) {
                return false;
            }
        } else {
            return isInMultiWindowMode;
        }
    }

}
