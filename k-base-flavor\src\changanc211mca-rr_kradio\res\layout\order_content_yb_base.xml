<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/x960"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:background="@drawable/order_pay_bg">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:background="@drawable/order_info_bg"
        android:id="@+id/ll_order_top"
        android:layout_marginLeft="@dimen/order_pay_margin_left"
        android:layout_marginRight="@dimen/order_pay_margin_right"
        android:layout_width="match_parent"
        android:layout_height="@dimen/order_content_top_height"
        android:orientation="horizontal"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toTopOf="@id/ll_order_bottom">

        <RelativeLayout
            android:id="@+id/rl_order_content"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:paddingTop="@dimen/m51"
            android:paddingLeft="@dimen/m44"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/fl_order_qrcode"
            app:layout_constraintBottom_toBottomOf="parent">

            <FrameLayout
                android:id="@+id/fl_image"
                android:layout_width="@dimen/m200"
                android:layout_height="@dimen/m200">

                <com.kaolafm.kradio.view.OvalImageView
                    android:id="@+id/iv_order_info_image"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">
                </com.kaolafm.kradio.view.OvalImageView>

                <TextView
                    android:visibility="gone"
                    android:id="@+id/tv_tag"
                    android:layout_width="@dimen/m76"
                    android:layout_height="@dimen/m38"
                    android:gravity="center"
                    android:text=""
                    android:textSize="@dimen/text_size3"
                    android:shadowColor="#80000000"
                    android:shadowDx="0"
                    android:shadowDy="1"
                    android:shadowRadius="3"
                    android:background="@drawable/order_album_image_tag_bg">
                </TextView>
                <com.kaolafm.kradio.view.OvalImageView
                    android:visibility="gone"
                    android:id="@+id/iv_tag"
                    android:layout_width="@dimen/m76"
                    android:layout_height="@dimen/m38"
                    android:layout_alignStart="@+id/iv_item_home_cover"
                    android:layout_alignTop="@+id/iv_item_home_cover"
                    android:scaleType="centerCrop"
                    app:rid_type="1"
                    android:src="@drawable/icon_vip" />
            </FrameLayout>

            <RelativeLayout
                android:id="@+id/rl_info_yunbi"
                android:layout_toRightOf="@id/fl_image"
                android:layout_marginLeft="@dimen/m60"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <LinearLayout
                    android:id="@+id/ll_order_yunbi_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">
                    <TextView
                        android:id="@+id/tv_order_yunbi_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text=""
                        android:textStyle="bold"
                        android:maxLines="2"
                        android:ellipsize="end"
                        android:textColor="@color/text_color_1"
                        android:textSize="@dimen/text_size5"/>
                    <TextView
                        android:visibility="gone"
                        android:id="@+id/tv_order_yunbi_tag"
                        android:layout_marginLeft="@dimen/m10"
                        android:padding="@dimen/m5"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text=""
                        android:textColor="@color/text_color_1"
                        android:textSize="@dimen/text_size1"
                        android:background="@drawable/order_price_tag"/>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_order_yunbi_origin_price"
                    android:layout_below="@id/ll_order_yunbi_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="价格"
                        android:textColor="@color/text_color_1"
                        android:textSize="@dimen/text_size3"/>
                    <TextView
                        android:id="@+id/tv_order_yunbi_origin_price"
                        android:layout_marginLeft="@dimen/m10"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text=" "
                        android:textColor="@color/order_money_delete_color"
                        android:textSize="@dimen/text_size3"/>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_order_yunbi_current_price"
                    android:layout_below="@id/ll_order_yunbi_origin_price"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">
                    <TextView
                        android:id="@+id/tv_order_yunbi_current"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="折扣价"
                        android:textColor="@color/text_color_1"
                        android:textSize="@dimen/text_size3"/>
                    <TextView
                        android:id="@+id/tv_order_yunbi_current_price"
                        android:layout_marginLeft="@dimen/m10"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text=" "
                        android:textColor="@color/order_money_color"
                        android:textSize="@dimen/m50"
                        android:textStyle="bold"/>
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="云币"
                        android:textColor="@color/order_money_color"
                        android:textSize="@dimen/text_size1"/>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_balance"
                    android:layout_below="@id/ll_order_yunbi_current_price"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="当前账号余额"
                        android:textColor="@color/order_account_balance_color"
                        android:textSize="@dimen/text_size3"/>
                    <TextView
                        android:id="@+id/tv_order_balance"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text=" "
                        android:textColor="@color/order_account_balance_color"
                        android:textSize="@dimen/text_size3"/>
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="云币"
                        android:textColor="@color/order_account_balance_color"
                        android:textSize="@dimen/text_size3"/>
                </LinearLayout>

                <TextView
                    android:id="@+id/tv_order_yunbi_pay"
                    android:layout_alignParentBottom="true"
                    android:layout_width="@dimen/m140"
                    android:layout_height="@dimen/m50"
                    android:gravity="center"
                    android:enabled="false"
                    android:text="支付"
                    android:textColor="@color/text_color_1"
                    android:textSize="@dimen/text_size5"
                    android:background="@drawable/order_pay_btn_bg"/>
            </RelativeLayout>

        </RelativeLayout>

        <FrameLayout
            android:id="@+id/fl_order_qrcode"
            android:layout_width="@dimen/x196"
            android:layout_height="match_parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toRightOf="@id/rl_order_content"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <LinearLayout
                android:id="@+id/ll_order_yunbi"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center">

                <TextView
                    android:id="@+id/tv_yunbi_buy_notice"
                    android:visibility="gone"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/y30"
                    android:gravity="center"
                    android:text="购买须知"
                    android:textColor="@color/order_qrcode_title_color"
                    android:textSize="@dimen/text_size3" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingTop="@dimen/m17"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_marginLeft="@dimen/m18"
                        android:layout_marginTop="@dimen/m5"
                        android:id="@+id/iv_yunbi_notice"
                        android:layout_width="@dimen/m26"
                        android:layout_height="@dimen/m26"
                        android:background="@drawable/ic_pay_notice" />

                    <TextView
                        android:id="@+id/tv_yunbi_notice"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="请在手机云听APP进行云币充值"
                        android:textColor="@color/order_qrcode_sub_color"
                        android:textSize="@dimen/text_size3" />
                </LinearLayout>
            </LinearLayout>

        </FrameLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ll_order_bottom"
        android:layout_marginLeft="@dimen/order_pay_margin_left"
        android:layout_marginRight="@dimen/order_pay_margin_right"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/m12"
        android:orientation="vertical"
        app:layout_constraintTop_toBottomOf="@id/ll_order_top"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <LinearLayout
            android:id="@+id/ll_notice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent">

            <TextView
                android:id="@+id/tv_rmb_buy_notice"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/y30"
                android:gravity="center"
                android:text="购买须知"
                android:textColor="@color/order_qrcode_title_color"
                android:textSize="@dimen/text_size3"/>

            <TextView
                android:id="@+id/tv_buy_notice_1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/pay_notice_1"
                android:lineSpacingMultiplier="1.1"
                android:textColor="@color/order_desc_txt_color"
                android:textSize="@dimen/text_size0"/>

            <TextView
                android:id="@+id/tv_buy_notice_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/pay_notice_2"
                android:lineSpacingMultiplier="1.1"
                android:textColor="@color/order_desc_txt_color"
                android:textSize="@dimen/text_size0"/>

            <TextView
                android:id="@+id/tv_buy_notice_3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/pay_notice_3"
                android:lineSpacingMultiplier="1.1"
                android:textColor="@color/order_desc_txt_color"
                android:textSize="@dimen/text_size0"/>
            <TextView
                android:id="@+id/tv_rmb_buy_notice_4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text=" "
                android:textColor="@color/order_desc_txt_color"
                android:textSize="@dimen/text_size0"/>
        </LinearLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>