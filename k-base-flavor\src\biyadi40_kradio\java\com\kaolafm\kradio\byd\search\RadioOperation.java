package com.kaolafm.kradio.byd.search;

import android.content.Intent;

import com.kaolafm.kradio.clientControlerForKradio.ClientImpl;

/**
 * 电台节目搜索
 * 参数定义：
 * 除来源、期数、节目类型以外，其余参数字段可任意组合，三方应用可以根据应用功能选择对接字段，
 * 若不支持按照单独的参数进行搜索，可以将单独参数字段，按照关键词进行搜索，但是智能语音传过来的这些参数必须处理
 *
 * intent.putExtra("IS_BACKGROUND", "TRUE");//字符串类型，该参数非必须项，参数值为"TRUE"时为导航在前台，
 * 参数值为"FALSE"时，默认前台
 * intent.putExtra("EXTRA_KEYWORDS_SEARCH","西游释厄传");//字符串类型，节目
 * intent.putExtra("EXTRA_PROGRAM_TAGS","相声");//字符串类型，类型（包含相声、小品、世界史、热门、最新等）
 * intent.putExtra("EXTRA_ARTIST_SEARCH","郭德纲|于谦");//字符串类型，主播，多个主播时用"|"分隔
 * intent.putExtra("EXTRA_EPISODE_TAGS","1|ZERO");//字符串类型，期数，多个标签时用"|"分隔
 * intent.putExtra("EXTRA_EPISODE_TAGS","1|MAX");//字符串类型，最新一期，多个标签时用"|"分隔
 * intent.putExtra("EXTRA_MEDIASOURCE_SEARCH","喜欢");//字符串类型，来源（喜欢、收藏、订阅、推荐的）
 * intent.putExtra("EXTRA_SOURCE_SEARCH","读者");//字符串类型，节目专辑
 * intent.putExtra("EXTRA_TYPE_SEARCH","在线节目");//字符串类型，节目类型（新闻/在线节目）
 * 返回值定义：
 * 0：执行成功
 * 1：其他执行失败
 * 2：超出可设置的范围
 * 3：当前场景不支持该功能
 * 4：需手动操作用户协议后执行
 * 5：需登录后支持
 * 6：网络异常
 * 7：当前功能中暂无数据信息
 */
public class RadioOperation extends Operation{
    public String category = "140"; //传统广播

    public RadioOperation(ClientImpl client, Intent intent) {
        super(client, intent);
    }

    @Override
    public void exe() {
        String keywords = intent.getStringExtra("EXTRA_KEYWORDS_SEARCH");
        String programTags = intent.getStringExtra("EXTRA_PROGRAM_TAGS");
        String episode = intent.getStringExtra("EXTRA_EPISODE_TAGS");
        String source = intent.getStringExtra("EXTRA_SOURCE_SEARCH");

        doSearch(keywords, 1, 6, null, null,
                null, category);
    }
}
