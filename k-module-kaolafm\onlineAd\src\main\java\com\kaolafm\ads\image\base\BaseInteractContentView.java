package com.kaolafm.ads.image.base;

import android.content.Context;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.widget.ImageView;
import com.kaolafm.ad.api.model.InteractionAdvert;

import java.io.File;
import java.lang.ref.WeakReference;

/**
 * 二次互动广告基类。
 * <AUTHOR>
 * @date 2020-03-20
 */
public class BaseInteractContentView extends BaseDragAdContentView<InteractionAdvert> {

    protected String mDestUrl;
    protected String mLocalPath;

    protected ImageView mImageView;

    protected InteractionAdvert interactionAdvert;

    public BaseInteractContentView(Context context) {
        super(context);
    }

    public BaseInteractContentView(Context context,
            @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public BaseInteractContentView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public void loadAdContent(InteractionAdvert advert) {
        mDuration = advert.getExposeDuration();
        mDestUrl = advert.getDestUrl();
        mLocalPath = advert.getLocalImagePath();
        this.interactionAdvert = advert;
        String path = advert.getLocalPath();
        if (!TextUtils.isEmpty(path)) {
            File file = null;
            try {
                file = new File(path);
            } catch (Exception e) {

            }
            if (file == null || !file.exists()) {
                mAdImageListener.onAdImageLoadFailed();
                return;
            }
            displayLocalImage(mImageView, path);
            countdownToCloseAd();
            show();
            mAdImageListener.onAdImageLoaded(this);
            return;
        }
        displayImage(mImageView, advert.getUrl(), new AdInteractLoaderListener(this));
    }

    class AdInteractLoaderListener extends AdOnImageLoaderListener {
        private WeakReference<BaseInteractContentView> mView;

        AdInteractLoaderListener(BaseInteractContentView view){
            mView = new WeakReference<>(view);
        }

        @Override
        public void onLoadingComplete(String url, ImageView target) {
            BaseInteractContentView view = mView.get();
            if (view != null) {
                view.show();
                view.countdownToCloseAd();
            }
            super.onLoadingComplete(url, target);
        }
    }
}
