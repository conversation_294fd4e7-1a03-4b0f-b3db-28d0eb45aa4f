package com.kaolafm.kradio.flavor.impl;

import android.util.Log;

import com.kaolafm.kradio.flavor.util.AudioSourceManager;
import com.kaolafm.kradio.lib.base.flavor.KRadioSystemSourceChangeInter;

import com.kaolafm.opensdk.player.logic.playcontrol.PlayControl;

public class KRadioSystemSourceChangeImpl implements KRadioSystemSourceChangeInter {

    private AudioSourceManager mAudioSourceManager;

    @Override
    public boolean registerSourceChanged(Object... args) {
        if (mAudioSourceManager == null) {
            Log.i("SystemSourceChangeImpl", "开始设置AAudioFocus");
            mAudioSourceManager = AudioSourceManager.getInstance();
            PlayControl.getInstance().setCustomAudioFocus(mAudioSourceManager);
        }
        return true;
    }

    @Override
    public boolean unregisterSourceChanged(Object... args) {
        return true;
    }
}
