package com.kaolafm.kradio.lib.init;

import android.app.Application;

/**
 * 继承该类并添加注解{@link AppInit}的类会被自动添加到管理中{@link AppInitManager}根据其配置进行初始化相关接口的回调。
 * 重载了部分方法，用于简化继承类。
 * <AUTHOR>
 * @date 2019-09-10
 */
public abstract class BaseAppInitializer implements AppInitializable {

    @Override
    public void asyncCreate(Application application) {

    }

    @Override
    public void onTerminate() {

    }

    @Override
    public void onLowMemory() {

    }

    @Override
    public void onTrimMemory(int level) {

    }
}
