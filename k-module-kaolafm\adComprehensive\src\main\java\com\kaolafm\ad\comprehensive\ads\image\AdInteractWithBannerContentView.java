package com.kaolafm.ad.comprehensive.ads.image;

import android.content.Context;
import android.os.Build;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

public class AdInteractWithBannerContentView extends AdInteractContentView {

    private AdBannerView mInterView;


    public AdInteractWithBannerContentView(Context context) {
        super(context);
    }

    public AdInteractWithBannerContentView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public AdInteractWithBannerContentView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    @Override
    protected void onViewClick(View view) {
        super.onViewClick(view);
        ViewGroup parent = (ViewGroup) getParent();
        if (mInterView == null) {
            mInterView = new AdBannerView(getContext(), mDestUrl, mLocalPath,
                    interactionAdvert.getWidth(), interactionAdvert.getHeight());
            FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
            mInterView.setLayoutParams(layoutParams);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                mInterView.setElevation(3F);
            }
            mInterView.setOnViewHideListener(this::show);
            parent.addView(mInterView);
        }
        hideWithAnim();
        mInterView.show();
    }

    public void hideBannerView() {
        if (mInterView != null)
            mInterView.hide();
    }
}
