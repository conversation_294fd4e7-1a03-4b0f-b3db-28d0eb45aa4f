<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rl_selcet_sex"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/pc_slect_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/personlityrecommendation_sex"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/m28"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.30" />
    <!--选择性别-->
    <RelativeLayout
        android:layout_width="@dimen/m360"
        android:layout_height="@dimen/m320"
        android:layout_marginTop="@dimen/y100"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.60"
        app:layout_constraintWidth_default="percent"
        app:layout_constraintWidth_percent="0.35">

        <RelativeLayout
            android:id="@+id/pc_man_rl"
            android:layout_width="@dimen/m140"
            android:layout_height="@dimen/m140"

            android:background="@drawable/pc_bg_unselect">

            <ImageView
                android:id="@+id/pc_iv_man"
                android:layout_width="@dimen/m45"
                android:layout_height="@dimen/m45"
                android:layout_centerInParent="true"
                android:scaleType="fitXY"
                android:src="@drawable/pc_man" />
        </RelativeLayout>


        <TextView
            android:layout_width="@dimen/m210"
            android:layout_height="wrap_content"
            android:layout_below="@id/pc_man_rl"
            android:layout_alignLeft="@id/pc_man_rl"
            android:layout_alignRight="@id/pc_man_rl"
            android:layout_margin="@dimen/y20"
            android:gravity="center"
            android:text="@string/personlityrecommendation_man"
            android:textColor="@color/pc_sex_color"
            android:textSize="@dimen/m28" />

        <RelativeLayout
            android:id="@+id/pc_femail_rl"
            android:layout_width="@dimen/m140"
            android:layout_height="@dimen/m140"
            android:layout_alignParentRight="true"
            android:background="@drawable/pc_bg_unselect">

            <ImageView
                android:id="@+id/pc_iv_femail"
                android:layout_width="@dimen/m50"
                android:layout_height="@dimen/m50"
                android:layout_centerInParent="true"
                android:scaleType="fitXY"
                android:src="@drawable/pc_femail" />
        </RelativeLayout>

        <TextView
            android:layout_width="@dimen/m210"
            android:layout_height="wrap_content"
            android:layout_below="@id/pc_femail_rl"
            android:layout_alignLeft="@id/pc_femail_rl"
            android:layout_alignRight="@id/pc_femail_rl"
            android:layout_margin="@dimen/y20"
            android:gravity="center"
            android:text="@string/personlityrecommendation_femail"
            android:textColor="@color/pc_sex_color"
            android:textSize="@dimen/m28" />
    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>