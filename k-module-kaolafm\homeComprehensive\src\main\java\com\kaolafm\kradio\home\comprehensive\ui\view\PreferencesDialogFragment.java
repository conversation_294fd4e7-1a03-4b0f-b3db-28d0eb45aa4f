package com.kaolafm.kradio.home.comprehensive.ui.view;

import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.TextView;

import com.kaolafm.kradio.component.ui.base.skin.SkinHelper;
import com.kaolafm.kradio.common.utils.CommonUtils;
import com.kaolafm.kradio.common.widget.GridDividerItemDecoration;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.dialog.BaseDialogFragment;
import com.kaolafm.kradio.lib.dialog.DialogListener;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.TimerUtil;

/**
 * 订制偏好对话框
 *
 * <AUTHOR>
 **/
public class PreferencesDialogFragment extends BaseDialogFragment {
    private static final int GRID_TOTAL_SPAN_COUNT = 5;

    private RecyclerView mRecyclerView;
    private GridLayoutManager mLayoutManager;
    private RecyclerView.Adapter mAdapter;

    private CharSequence mSubtitle;

    private CharSequence mTitle;

    private TextView mTvSubtitle;

    private TextView mTvTitle;

    private DialogListener.OnNativeListener<DialogFragment> mNativeListener;
    private DialogListener.OnPositiveListener<DialogFragment> mPositiveListener;
    private CharSequence leftBtnText;

    private CharSequence rightBtnText;
    private Button btnLeft;
    private Button btnRight;


    public static PreferencesDialogFragment create() {
        PreferencesDialogFragment fragment = new PreferencesDialogFragment();
        fragment.setGravity(Gravity.CENTER);
        return fragment;
    }

    public void setAdapter(RecyclerView.Adapter adapter) {
        mAdapter = adapter;
    }


    public void setLeftButton(CharSequence leftBtnText) {
        this.leftBtnText = leftBtnText;
    }

    public void setRightButton(CharSequence rightBtnText) {
        this.rightBtnText = rightBtnText;
    }

    public void setOnNativeListener(DialogListener.OnNativeListener<DialogFragment> nativeListener) {
        mNativeListener = nativeListener;
    }

    public void setOnPositiveListener(DialogListener.OnPositiveListener<DialogFragment> positiveListener) {
        mPositiveListener = positiveListener;
    }

    @Override
    public void onStart() {
        super.onStart();
        Window window = getDialog().getWindow();
        WindowManager.LayoutParams params = window.getAttributes();
        if (SkinHelper.isNightMode()) {
            params.dimAmount = 0.9f;
        } else {
            params.dimAmount = 0.7f;
        }
        window.setAttributes(params);
        CommonUtils.getInstance().initGreyStyle(window);
    }

    @Override
    protected View getContentView() {
        FragmentActivity activity = getActivity();
        mLayoutManager = new GridLayoutManager(activity, GRID_TOTAL_SPAN_COUNT, GridLayoutManager.VERTICAL, false);
        View inflate = View.inflate(activity, R.layout.dialog_costomize, null);
        mTvTitle = inflate.findViewById(R.id.dialog_tv_title);
        mTvSubtitle = inflate.findViewById(R.id.dialog_tv_subtitle);
        btnLeft = inflate.findViewById(R.id.dialog_btn_left);
        btnRight = inflate.findViewById(R.id.dialog_btn_right);
        mRecyclerView = inflate.findViewById(R.id.dialog_rv);

        mRecyclerView.setLayoutManager(mLayoutManager);
        int space = ResUtil.getDimen(R.dimen.m40);
        GridDividerItemDecoration decor = new GridDividerItemDecoration(space, space);
        decor.isOffsetBottom(false);
        mRecyclerView.addItemDecoration(decor, 0);
        if (mAdapter != null) {
            mRecyclerView.setAdapter(mAdapter);
        }

        if (!TextUtils.isEmpty(mTitle)) {
            mTvTitle.setText(mTitle);
        }
        if (!TextUtils.isEmpty(mSubtitle)) {
            mTvSubtitle.setText(mSubtitle);
        }

        if (!TextUtils.isEmpty(leftBtnText)) {
            btnLeft.setText(leftBtnText);
        }

        if (!TextUtils.isEmpty(rightBtnText)) {
            btnRight.setText(rightBtnText);
        }

        btnLeft.setOnClickListener(v -> mNativeListener.onClick(PreferencesDialogFragment.this));

        btnRight.setOnClickListener(v -> mPositiveListener.onClick(PreferencesDialogFragment.this));

        return inflate;
    }

    @Override
    protected void showAccordingToScreen(int orientation) {
        setWidth(ResUtil.getDimen(R.dimen.preferences_dialog_width));
        setHeight(ResUtil.getDimen(R.dimen.preferences_dialog_height));
        super.showAccordingToScreen(orientation);
        mLayoutManager.setSpanCount(ResUtil.getInt(R.integer.preference_dialog_span_count));
        int paddingLeft = ResUtil.getDimen(R.dimen.preferences_dialog_padding_left);
        int paddingRight = ResUtil.getDimen(R.dimen.preferences_dialog_padding_right);
        mRecyclerView.setPadding(paddingLeft, 0, paddingRight, 0);
        mAdapter.notifyDataSetChanged();
    }

    @Override
    public void onMultiWindowModeChanged(boolean isInMultiWindowMode) {
        super.onMultiWindowModeChanged(isInMultiWindowMode);
        TimerUtil.newInstance().timer(400, num -> mAdapter.notifyDataSetChanged());
    }

    public void setTitle(CharSequence title) {
        mTitle = title;
    }

    public void setSubtitle(CharSequence subtitle) {
        mSubtitle = subtitle;
    }
}
