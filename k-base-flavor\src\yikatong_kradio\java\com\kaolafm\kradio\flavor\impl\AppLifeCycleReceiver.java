package com.kaolafm.kradio.flavor.impl;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.kaolafm.kradio.widget.WidgetUpdateManager;

/**
 * <pre>
 *     <AUTHOR> Wenchl
 *     e-mail : <EMAIL>
 *     time   : 2019/11/02
 *     desc   :
 *     version: 1.0
 * </pre>
 */
public class AppLifeCycleReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
        if ("com.kaolafm.client.ACTION_ONCREATE".equals(intent.getAction())){
            WidgetUpdateManager.getInstance().init();
            WidgetUpdateManager.getInstance().getDefaultPlayInfo(false);
        }
    }
}
