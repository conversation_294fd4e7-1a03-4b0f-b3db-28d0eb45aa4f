<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/widget_layout_three_main_layout"
    android:layout_width="1170px"
    android:layout_height="295px"
    android:background="@color/transparent_color"
    android:minWidth="1170px"
    android:minHeight="295px"
    tools:ignore="PxUsage">

    <RelativeLayout
        android:id="@+id/widget_playinfo_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">


        <ImageView
            android:id="@+id/widget_cover_bg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="34px"
            android:src="@drawable/widget_cover_bg_three" />

        <ImageView
            android:id="@+id/widget_cover"
            android:layout_width="167px"
            android:layout_height="167px"
            android:layout_marginLeft="111px"
            android:layout_marginTop="65px" />


        <LinearLayout
            android:id="@+id/ll_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="44px"
            android:layout_marginTop="62px"
            android:layout_toRightOf="@+id/widget_cover_bg"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/widget_audio_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/widget_audiocover_layout"
                android:ellipsize="end"
                android:lines="1"
                android:maxWidth="540px"
                android:maxLines="1"
                android:text=""
                android:textColor="@color/widget_audio_name_textcolor"
                android:textSize="@dimen/audio_name_text_size" />

            <TextView
                android:id="@+id/widget_album_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/widget_audio_name"
                android:layout_marginLeft="20px"
                android:ellipsize="end"
                android:gravity="left"
                android:lines="1"
                android:maxLines="1"
                android:text=""
                android:textColor="@color/widget_album_name_textcolor"
                android:textSize="@dimen/album_name_text_size" />
        </LinearLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/ll_title"
            android:layout_centerVertical="true"
            android:layout_marginLeft="44px"
            android:layout_marginTop="28px"
            android:layout_marginRight="89px"
            android:layout_toRightOf="@+id/widget_cover_bg"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/widget_cur_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:text="00:00"
                android:textColor="@color/widget_timecolor"
                android:textSize="@dimen/time_text_size" />


            <ImageView
                android:id="@+id/widget_progressBar"
                android:layout_width="@dimen/widget_progress_bar_three_width"
                android:layout_height="@dimen/widget_progress_bar_height"
                android:layout_centerInParent="true"
                android:background="@drawable/widget_progress_bar_bg" />

            <TextView
                android:id="@+id/widget_duration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:text="00:00"
                android:textColor="@color/widget_timecolor"
                android:textSize="@dimen/time_text_size" />

        </RelativeLayout>


        <LinearLayout
            android:id="@+id/widget_play_operation_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginRight="91px"
            android:layout_marginBottom="45px"
            android:layout_toRightOf="@+id/widget_cover_bg"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/widget_prev"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingTop="@dimen/widget_action_button_v_padding"
                android:paddingBottom="@dimen/widget_action_button_v_padding"
                android:layout_weight="1"
                android:src="@drawable/selector_widget_btn_play_prev" />

            <ImageView
                android:id="@+id/widget_play_or_pause"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:paddingTop="@dimen/widget_action_button_v_padding"
                android:paddingBottom="@dimen/widget_action_button_v_padding"
                android:src="@drawable/selector_widget_btn_pause" />

            <ImageView
                android:id="@+id/widget_next"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:paddingTop="@dimen/widget_action_button_v_padding"
                android:paddingBottom="@dimen/widget_action_button_v_padding"
                android:src="@drawable/selector_widget_btn_play_next" />

            <ImageView
                android:id="@+id/widget_collection"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="100px"
                android:paddingTop="@dimen/widget_action_button_v_padding"
                android:paddingBottom="@dimen/widget_action_button_v_padding"
                android:src="@drawable/selector_widget_btn_uncollection" />
        </LinearLayout>
    </RelativeLayout>
</RelativeLayout>