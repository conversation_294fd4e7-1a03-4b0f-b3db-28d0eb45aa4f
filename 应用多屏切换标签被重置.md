# 多屏切换时首页tab状态错乱问题修复

## 问题描述
在多屏切换（横竖屏切换）时，Activity会被重建，导致首页tab显示状态与实际内容不匹配：
- UI显示"推荐"tab高亮
- 但实际显示的是"音乐"栏目的内容
- 用户看到的是错误的数据

## 根本原因分析
1. **静态变量状态保持**: `YTDataCache.selectPage` 是静态变量，在Activity重建时保持了之前的值（音乐栏目的index）
2. **ViewPager默认状态**: ViewPager在重建时默认显示第0个Fragment（推荐）
3. **状态不同步**: 导致UI显示推荐，但YTDataCache指向音乐，数据来源错乱

## 修复方案

### 1. Activity状态保存和恢复 (LauncherActivity.java)
```java
// 添加常量
private static final String SAVED_CURRENT_TAB_INDEX = "current_tab_index";

// 在onSaveInstanceState中保存当前tab index
@Override
protected void onSaveInstanceState(Bundle outState) {
    // 保存当前选中的tab index，解决多屏切换时状态丢失问题
    int currentTabIndex = YTDataCache.getSelectPage();
    outState.putInt(SAVED_CURRENT_TAB_INDEX, currentTabIndex);
    Log.i(TAG, "onSaveInstanceState: saving currentTabIndex = " + currentTabIndex);
    // ... 其他代码
}

// 在onCreate中恢复tab index
if (savedInstanceState != null) {
    // 恢复当前选中的tab index，解决多屏切换时状态丢失问题
    int savedTabIndex = savedInstanceState.getInt(SAVED_CURRENT_TAB_INDEX, -1);
    if (savedTabIndex >= 0) {
        YTDataCache.setSelectPage(savedTabIndex);
        Log.i(TAG, "onCreate: restored currentTabIndex = " + savedTabIndex);
    }
    // ... 其他代码
}
```

### 2. ViewPager状态同步 (HorizontalHomePlayerFragment.java)
```java
// 在onTabDate方法中添加边界检查和状态同步
if (selectPage >= tabList.size()) {
    Log.w(TAG, "onTabDate: selectPage " + selectPage + " is out of bounds, resetting to 0");
    selectPage = 0;
    YTDataCache.setSelectPage(selectPage);
}

// 添加ViewPager页面变化监听器
home_view_page.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
    @Override
    public void onPageSelected(int position) {
        Log.i(TAG, "ViewPager onPageSelected: " + position);
        YTDataCache.setSelectPage(position);
        // 同步更新导航栏的选中状态
        if (mHnlHomeNavigation != null) {
            mHnlHomeNavigation.setCurrentTab(position);
        }
    }
    // ... 其他方法
});

// 确保ViewPager和导航栏状态一致
home_view_page.setCurrentItem(selectPage, false);
if (mHnlHomeNavigation != null) {
    mHnlHomeNavigation.setCurrentTab(selectPage);
}
```

### 2.1 HomeNavigationLayout增强 (HomeNavigationLayout.java)
```java
/**
 * 设置当前选中的tab
 * @param position tab位置
 */
public void setCurrentTab(int position) {
    if (mSrtlHomeNavigationTab != null) {
        mSrtlHomeNavigationTab.setCurrentTab(position);
        currentPosition = position;
    }
}
```

### 3. 数据清理机制 (ComperhensiveHomeDateFragment.java)
```java
@Override
protected void lazyLoad() {
    // 在Activity重建时，确保清理可能的脏数据缓存
    clearPreviousDataIfNeeded();
    mPresenter.initData(zone);
}

private void clearPreviousDataIfNeeded() {
    // 检查是否有旧数据且Fragment刚变为可见
    if (mHomeAdapterList != null && !mHomeAdapterList.isEmpty()) {
        boolean hasData = false;
        for (HomeAdapter adapter : mHomeAdapterList) {
            if (adapter.getItemCount() > 0) {
                hasData = true;
                break;
            }
        }
        
        if (hasData && !isLoaded) {
            Log.i(TAG, "clearPreviousDataIfNeeded: Found existing data in newly visible fragment, clearing to avoid stale data");
            clearDisplayData();
        }
    }
}

private void clearDisplayData() {
    if (mHomeAdapterList != null) {
        for (HomeAdapter adapter : mHomeAdapterList) {
            adapter.clear(); // 清理适配器数据
        }
    }
    // 刷新RecyclerView显示
    if (recyclerViewList != null) {
        for (RecyclerView recyclerView : recyclerViewList) {
            if (recyclerView.getAdapter() != null) {
                recyclerView.getAdapter().notifyDataSetChanged();
            }
        }
    }
}
```

## 修复效果
1. **状态一致性**: ViewPager当前页面与导航栏高亮状态完全一致
2. **数据正确性**: 显示的内容与选中的tab完全匹配
3. **用户体验**: 多屏切换后用户看到的是正确的页面和数据
4. **稳定性**: 避免了数据错乱和UI状态不一致的问题

## 测试建议
1. 在不同tab之间切换
2. 进行横竖屏切换
3. 验证切换后tab高亮状态与显示内容是否一致
4. 检查日志确认状态保存和恢复是否正常工作
