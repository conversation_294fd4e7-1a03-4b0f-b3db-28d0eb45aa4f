package com.kaolafm.kradio.live.mvp;

import android.content.Context;
import android.util.Log;

import com.alibaba.sdk.android.oss.ClientException;
import com.alibaba.sdk.android.oss.ServiceException;
import com.alibaba.sdk.android.oss.callback.OSSCompletedCallback;
import com.alibaba.sdk.android.oss.callback.OSSProgressCallback;
import com.alibaba.sdk.android.oss.model.PutObjectRequest;
import com.alibaba.sdk.android.oss.model.PutObjectResult;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.NetworkManager;
import com.kaolafm.kradio.live.player.ErrorStatus;
import com.kaolafm.kradio.live.player.HomeLiveManager;
import com.kaolafm.kradio.live.player.NimManager;
import com.kaolafm.kradio.live.player.RecordUploadHelper;
import com.kaolafm.kradio.live.player.RecorderStatus;
import com.kaolafm.kradio.live.utils.LiveBeanTransUtils;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.goods.model.GoodsResult;
import com.kaolafm.opensdk.api.live.LiveApiConstant;
import com.kaolafm.opensdk.api.live.LiveRequest;
import com.kaolafm.opensdk.api.live.model.ChatRoomMessageInfo;
import com.kaolafm.opensdk.api.live.model.ChatRoomMessageInfoResult;
import com.kaolafm.opensdk.api.live.model.ChatUserInfo;
import com.kaolafm.opensdk.api.live.model.Gift;
import com.kaolafm.opensdk.api.live.model.GiftGivingResult;
import com.kaolafm.opensdk.api.live.model.GiftsResult;
import com.kaolafm.opensdk.api.live.model.LiveChatRoomMemberInfoResult;
import com.kaolafm.opensdk.api.live.model.LiveInfoDetail;
import com.kaolafm.opensdk.api.live.model.MessageBean;
import com.kaolafm.opensdk.api.yunxin.model.GiftRankUser;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.log.Logging;
import com.trello.rxlifecycle3.LifecycleProvider;
import com.trello.rxlifecycle3.android.ActivityEvent;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.functions.Action;
import io.reactivex.functions.Consumer;
import io.reactivex.schedulers.Schedulers;

/**
 * 处理Live相关的数据获取，缓存，与聊天室的交互，通知UI进行更新
 *
 * <AUTHOR> Huangui
 */
public class HomeLivePresenter extends BasePresenter<LiveModel, HomeLiveView> {


    public static final String TAG = "HomeLivePresenter";

    public static final boolean DEBUG_LIVE = true;

    private NimManager.RoomMemberChangedObserver mMemberChangedObserver;

    private NimManager.OnChatMessageReceivedListener mMessageReceivedListener;

    private NimManager.EnterChatRoomListener mEnterChatRoomListener;

    public HomeLivePresenter(HomeLiveView view) {
        super(view);
    }

    @Override
    public void start() {
        super.start();
    }

    public void initNim(long liveId) {
        NimManager.getInstance().initNim();
        NimManager.getInstance().setLiveId(liveId);
        HomeLiveManager.getInstance().registerResponseRecord();
    }

    @Override
    protected LiveModel createModel() {
        return new LiveModel(((LifecycleProvider) mView).bindUntilEvent(ActivityEvent.DESTROY));
    }

    @Override
    public void destroy() {
        super.destroy();
        HomeLiveManager.getInstance().unRegisterResponseRecord();
    }

    public String getNickName() {
        return NimManager.getInstance().getNickName();
    }

    /**
     * 通过接口获取直播信息
     *
     * @param programid 直播ID
     */
    public void getLiveInfo(long programid) {
        // Log.i("************", "3");
        if (DEBUG_LIVE) {
            Logging.i(TAG, "getShowingInfo programid: " + programid);
        }
        if(mView==null){
            return;
        }
        mView.onLoading();
        if(NetworkManager.getInstance().isNotHasNetwork()){
            if(mView != null){
                mView.onLoadFinish();
                mView.showErrorInfo(ErrorStatus.ERROR_NET);
            }
            return;
        }
        if (mModel == null) {
            mView.onLoadFinish();
            return;
        }
        String roomId = NimManager.getInstance().getRoomId();
        if (DEBUG_LIVE) {
            Log.i(TAG, "getListenerNumber roomId: " + roomId);
        }

        mModel.getLiveInfo(String.valueOf(programid), new HttpCallback<LiveInfoDetail>() {
            @Override
            public void onSuccess(LiveInfoDetail liveInfoDetail) {
                if (DEBUG_LIVE) {
                    Log.i(TAG, "getShowingInfo onSuccess liveInfoDetail: " + liveInfoDetail.toString());
                }
                if(mView==null){
                    return;
                }
                mView.onLoadFinish();
                if (liveInfoDetail != null) {
                    mView.showLiveInfo(liveInfoDetail);
                } else {
                    mView.showErrorInfo(ErrorStatus.ERROR_REQUEST);
                }
            }

            @Override
            public void onError(ApiException exception) {
                if (DEBUG_LIVE) {
                    Log.i(TAG, "getShowingInfo onError throwable: " + exception.getMessage());
                }
                if(mView==null){
                    return;
                }
                mView.onLoadFinish();
                mView.showErrorInfo(ErrorStatus.ERROR_REQUEST);
            }
        });

    }

    /**
     * 获取礼物列表
     * @param liveId
     */
    public void getGifts(Integer liveId){
        if(NetworkManager.getInstance().isNotHasNetwork()){
            if(mView != null){
                mView.showErrorInfo(ErrorStatus.ERROR_REQUEST);
            }
            return;
        }
        if(mModel==null){
            return;
        }
        mModel.getGifts(liveId, new HttpCallback<GiftsResult>() {
            @Override
            public void onSuccess(GiftsResult giftsResult) {
                if(mView==null){
                    return;
                }
                if(giftsResult == null || giftsResult.getGiftList() == null){
                    mView.onGiftsFailed();
                    return;
                }
                mView.onGiftsSuccess(giftsResult);
            }

            @Override
            public void onError(ApiException e) {
                if(mView==null){
                    return;
                }
                mView.onGiftsFailed();
            }
        });
    }

    /**
     * 送礼物
     * @param gift
     * @param liveId
     * @param count
     */
    public void givingGifts(Gift gift,
                            Long liveId,
                            Integer count){
        Log.d(TAG, "live--- givingGifts() liveId=" + liveId + ", gift=" + gift + ", count=" + count);
        if(NetworkManager.getInstance().isNotHasNetwork()){
            if(mView != null){
                mView.showErrorInfo(ErrorStatus.ERROR_REQUEST);
            }
            return;
        }
        if(mView==null){
            return;
        }
        mView.onLoading();
        if(mModel==null){
            return;
        }
        mModel.givingGifts(gift.getGiftId(), liveId, count,
                new HttpCallback<GiftGivingResult>() {
                    @Override
                    public void onSuccess(GiftGivingResult giftGivingResult) {
                        if(mView==null){
                            return;
                        }
                        mView.onLoadFinish();
                        if(giftGivingResult==null){
                            mView.onGiveGiftFailed(new ApiException(0, "打赏失败,服务器异常"));
                            return;
                        }
                        if(giftGivingResult.getStatus()==0){
                            mView.onGiveGiftFailed(new ApiException(0, "打赏失败,服务器异常"));
                            return;
                        }
                        if(giftGivingResult.getStatus()==1){
                            mView.onGiveGiftSuccess(giftGivingResult, gift);
                            return;
                        }
                        if(giftGivingResult.getStatus()==2){
                            mView.onGiveGiftFailed(new ApiException(2, "打赏失败,错误的打赏次数"));
                            return;
                        }
                        if(giftGivingResult.getStatus()==3){
                            mView.onGiveGiftFailed(new ApiException(3, "打赏失败,礼物不存在"));
                            return;
                        }
                        if(giftGivingResult.getStatus()==4){
                            mView.onGiveGiftFailed(new ApiException(4, "打赏失败,直播不存在"));
                            return;
                        }
                        if(giftGivingResult.getStatus()==5){
                            mView.onGiveGiftFailed(new ApiException(5, "打赏失败,用户未登录"));
                            return;
                        }
                        if(giftGivingResult.getStatus()==6){
                            mView.onGiveGiftFailed(new ApiException(6, "打赏失败,用户不存在"));
                            return;
                        }
                        if(giftGivingResult.getStatus()==7){
                            mView.onGiveGiftFailed(new ApiException(7, "打赏失败,主播不在直播中"));
                            return;
                        }
                        if(giftGivingResult.getStatus()==8){
                            mView.onGiveGiftFailed(new ApiException(8, "打赏失败,账户余额不足"));
                            return;
                        }
                        if(giftGivingResult.getStatus()==9){
                            mView.onGiveGiftFailed(new ApiException(9, "打赏失败"));
                            return;
                        }
                    }

                    @Override
                    public void onError(ApiException e) {
                        if(mView==null){
                            return;
                        }
                        mView.onLoadFinish();
                        mView.onGiveGiftFailed(e);
                    }
                });
    }

    /**
     * 获取商品列表
     * @param liveId
     */
    public void getGoods(Integer liveId){
        if(NetworkManager.getInstance().isNotHasNetwork()){
            if(mView != null){
                mView.showErrorInfo(ErrorStatus.ERROR_REQUEST);
            }
            return;
        }
        if(mModel==null){
            return;
        }
        mModel.getGoods(liveId, new HttpCallback<GoodsResult>() {
            @Override
            public void onSuccess(GoodsResult goodsResult) {
                if(mView==null){
                    return;
                }
                if(goodsResult == null || goodsResult.getGoodsList() == null){
                    mView.onGoodsFailed();
                    return;
                }
                mView.onGoodsSuccess(goodsResult);
            }

            @Override
            public void onError(ApiException e) {
                if(mView==null){
                    return;
                }
                mView.onGoodsFailed();
            }
        });
    }

    /**
     * 通过接口获取聊天室成员信息
     */
    public void fetchRoomMembersByAPI(Long liveId){
        Log.d(TAG, "live--- fetchRoomMembersByAPI() liveId=" + liveId);
        if (mView == null){
            return;
        }
        mModel.fetchRoomMembers(liveId, new HttpCallback<LiveChatRoomMemberInfoResult>() {
            @Override
            public void onSuccess(LiveChatRoomMemberInfoResult memberInfoResult) {
                Log.d(TAG, "live--- fetchRoomMembers() onSuccess memberInfoResult=" + memberInfoResult);
                if (mView == null){
                    return;
                }
                List<ChatUserInfo> users = new ArrayList<>();
                if (memberInfoResult != null && !ListUtil.isEmpty(memberInfoResult.getRewardRanking())) {
                    for (GiftRankUser giftRankUser : memberInfoResult.getRewardRanking()) {
                        users.add(LiveBeanTransUtils.chatRoomMember2ChatUserInfo(giftRankUser));
                    }
                }
                mView.onChatRoomMemberReceived(users);
                try {
                    int count = Integer.parseInt(memberInfoResult.getOnlineUserCount());
                    mView.showListenerNumber(count);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onError(ApiException e) {
                Log.d(TAG, "live--- fetchRoomMembers() onError ApiException=" + e);
                if (mView == null){
                    return;
                }
                mView.onChatRoomMemberQueryFailed(e != null ? e.getCode() : -1, e);
            }
        });
    }

    /**
     * 通过接口获取聊天室在线人数
     */
    public void fetchRoomOnLineCountByAPI(Long liveId){
        Log.d(TAG, "live--- fetchRoomOnLineCountByAPI() liveId=" + liveId);
        if (mView == null){
            return;
        }
        mModel.fetchRoomMembers(liveId, new HttpCallback<LiveChatRoomMemberInfoResult>() {
            @Override
            public void onSuccess(LiveChatRoomMemberInfoResult memberInfoResult) {
                Log.d(TAG, "live--- fetchRoomOnLineCount() onSuccess memberInfoResult=" + memberInfoResult);
                if (mView == null || memberInfoResult == null){
                    return;
                }
                try {
                    int count = Integer.parseInt(memberInfoResult.getOnlineUserCount());
                    mView.showListenerNumber(count);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onError(ApiException e) {
                Log.d(TAG, "live--- fetchRoomOnLineCount() onError ApiException=" + e);
            }
        });
    }

    /**
     * 通过接口获取聊天室历史消息
     */
    public void getHistoryMessages(Long liveId, Long roomId, Long msgCount){
        Log.d(TAG, "live--- getHistoryMessages() roomId=" + roomId + ", msgCount=" + msgCount);
        if (mView == null){
            return;
        }
        mModel.getHistoryMessages(liveId, roomId, msgCount, new HttpCallback<ChatRoomMessageInfoResult>() {
            @Override
            public void onSuccess(ChatRoomMessageInfoResult messageInfoResult) {
                Log.d(TAG, "live--- getHistoryMessages() onSuccess messageInfoResult=" + messageInfoResult);
                if (mView == null || !(mView instanceof HomeLiveView)){
                    return;
                }
                List<MessageBean> mMessages = new ArrayList<>();
                if (messageInfoResult != null && !ListUtil.isEmpty(messageInfoResult.getMessageInfoList())) {
                    for (ChatRoomMessageInfo messageInfo : messageInfoResult.getMessageInfoList()){
                        mMessages.add(LiveBeanTransUtils.createMessageBean(messageInfo));
                    }
                }
                ((HomeLiveView) mView).onHistoryMessageReceived(mMessages, false);
            }

            @Override
            public void onError(ApiException e) {
                Log.d(TAG, "live--- getHistoryMessages() onError ApiException=" + e);
                if (mView == null || !(mView instanceof HomeLiveView)){
                    return;
                }
                ((HomeLiveView) mView).onHistoryMessageQueryFailed(e != null ? e.getCode() : -1, e);
            }
        });
    }

//    /**
//     * 注册云信IM账号,如果注册成功，直接进入直播对应的聊天室
//     *
//     * @param roomId 聊天室ID
//     */
//    public void registerToNim(String roomId, NimManager.EnterChatRoomListener listener) {
//        if (DEBUG_LIVE) {
//            Log.i(TAG, "registerToNim");
//        }
//        HttpCallback callback = new HttpCallback<ChatRoomTokenDetail>() {
//            @Override
//            public void onSuccess(ChatRoomTokenDetail bean) {
//                if (bean != null && bean.getToken() != null && bean.getAccid() != "0") {
//                    String accountId = String.valueOf(bean.getAccid());
//                    String token = bean.getToken();
//                    String nickName = bean.getNickName();
//                    if (DEBUG_LIVE) {
//                        Log.i(TAG, "registerToNim onSuccess accountId: " + accountId
//                                + ", token: " + token + ", nickName: " + nickName);
//                    }
////token = "446a92cb94e3eed08c5029cb90b0a2c3";
//                    NimManager.getInstance().setAccount(accountId);
//                    NimManager.getInstance().setNickName(nickName);
//                    LoginInfo loginInfo = new LoginInfo(accountId, token);
//                    NimManager.getInstance().startLoginToNim(loginInfo, roomId, listener);
//                } else {
//                    if (listener != null) {
//                        listener.loginFailed(-1);
//                    }
//                }
//            }
//
//            @Override
//            public void onError(ApiException t) {
//                if (DEBUG_LIVE) {
//                    Log.i(TAG, "registerToNim onError", t);
//                }
//                if (listener != null) {
//                    listener.getAccountFailed(0);
//                }
//            }
//        };
//        String uid = LiveUtil.getUserId();
//        String avatar = LiveUtil.getUserFavicon();
//        String nickName = LiveUtil.getUserNickname();
//        mModel.chatRoomToken(uid, avatar, nickName, callback);
//    }

    public void refreshChatRoomInfo(long liveId) {
        Log.i(TAG, "---live refreshChatRoomInfo");
//        getListenerNumber();
        fetchRoomMembers(liveId);
    }

    /**
     * 获取收听人数
     */
    public void getListenerNumber(Long liveId) {
        String roomId = NimManager.getInstance().getRoomId();
        if (DEBUG_LIVE) {
            Log.i(TAG, "getListenerNumber roomId: " + roomId);
        }
        // do live 直播新接口逻辑
        fetchRoomOnLineCountByAPI(liveId);
    }

    public void fetchRoomMembers(long liveId) {
        if (DEBUG_LIVE) {
            Log.i(TAG, "getListenerNumber liveId: " + liveId);
        }
        // do live 通过接口获取聊天室成员信息
        fetchRoomMembersByAPI(liveId);
    }

//    private class RecordUploadProgressCallback implements OSSProgressCallback<PutObjectRequest> {
//
//        private ObservableEmitter<Integer> mEmitter;
//
//        private RecordUploadProgressCallback(ObservableEmitter<Integer> mEmitter) {
//            this.mEmitter = mEmitter;
//        }
//
//        @Override
//        public void onProgress(PutObjectRequest request, long currentSize, long totalSize) {
//            float progress = (totalSize > 0) ? ((float) currentSize / totalSize) * 100 : -1;
//            if (DEBUG_LIVE) {
//                Log.i(TAG, String.format("onProgress Progress %d from %d (%f)",
//                        currentSize, totalSize, progress));
//            }
//            mEmitter.onNext((int) progress);
//        }
//    }

//    private class RecordUploadCompletedCallback implements OSSCompletedCallback<PutObjectRequest, PutObjectResult> {
//
//        private ObservableEmitter<Integer> mEmitter;
//
//        public RecordUploadCompletedCallback(ObservableEmitter<Integer> mEmitter) {
//            this.mEmitter = mEmitter;
//        }
//
//        @Override
//        public void onSuccess(PutObjectRequest request, PutObjectResult result) {
//            if (DEBUG_LIVE) {
//                Log.i(TAG, "onSuccess responseJson : " + result.toString());
//            }
//            mEmitter.onComplete();
//
//        }
//
//        @Override
//        public void onFailure(PutObjectRequest request, ClientException clientException, ServiceException serviceException) {
//            if (DEBUG_LIVE) {
//                Log.i(TAG, "onFailure client errorMessage : " + clientException.getMessage() + " ; onFailure server errorMessage :" + serviceException.getMessage());
//            }
//            mEmitter.tryOnError(new RuntimeException(clientException.getMessage()));
//            mEmitter.tryOnError(new RuntimeException(serviceException.getMessage()));
//        }
//    }


    /**
     * 进入聊天室
     *
     * @param roomId 聊天室id
     */
    public void enterChatRoom(Context context, String roomId) {
        enterChatRoom(false, context, roomId);
    }

    /**
     * 退出聊天室
     */
    public void exitChatRoom() {
        if (DEBUG_LIVE) {
            Log.i(TAG, "exitChatRoom");
        }
        NimManager.getInstance().exitChatRoom();
    }

    protected NimManager.RoomMemberChangedObserver createRoomMemberChangedObserver() {
        if (mMemberChangedObserver != null) {
            return mMemberChangedObserver;
        }
        mMemberChangedObserver = new NimManager.RoomMemberChangedObserver() {
            @Override
            public void onRoomMemberIn(ChatUserInfo chatUserInfo) {
                if (mView != null) {
                    mView.showRoomMemberEnter(chatUserInfo);
                }
            }

            @Override
            public void onRoomMemberExit(ChatUserInfo chatUserInfo) {
                Log.d(TAG, "直播间成员退出：" + chatUserInfo);
                if (mView != null) {
                    mView.showRoomMemberExit(chatUserInfo);
                }
            }
        };
        return mMemberChangedObserver;
    }

    protected NimManager.OnChatMessageReceivedListener createChatMessageReceivedListener() {
        if (mMessageReceivedListener != null) {
            return mMessageReceivedListener;
        }
        mMessageReceivedListener = new NimManager.OnChatMessageReceivedListener() {
            @Override
            public void onChatMessageReceived(ArrayList<MessageBean> messageData) {
                if (mView != null) {
                    mView.showChatMessageReceived(messageData);
                }
            }
        };
        return mMessageReceivedListener;
    }

//    private NimManager.EnterChatRoomListener createEnterChatRoomListener() {
//        if (mEnterChatRoomListener == null) {
//            mEnterChatRoomListener = new NimManager.EnterChatRoomListener() {
//                @Override
//                public void onException(Throwable throwable) {
//                    if (LivePresenter.DEBUG_LIVE) {
//                        Log.i(TAG, "enterChatRoom onException: ", throwable);
//                    }
//                    NimManager.getInstance().setChatRoomEntered(false);
//                }
//
//                @Override
//                public void loginFailed(int code) {
//                    if (LivePresenter.DEBUG_LIVE) {
//                        Log.i(TAG, "enterChatRoom loginFailed code: " + code);
//                    }
//                    NimManager.getInstance().setChatRoomEntered(false);
//                }
//
//                @Override
//                public void loginSuccess(String account) {
//                    if (LivePresenter.DEBUG_LIVE) {
//                        Log.i(TAG, "enterChatRoom loginSuccess account: " + account);
//                    }
//                    //此处登录成功，进入聊天室是否成功还需要进一步确认
//                }
//
//                @Override
//                public void enterChatRoomSuccess(EnterChatRoomResultData data) {
//                    if (LivePresenter.DEBUG_LIVE) {
//                        Log.i(TAG, "enterChatRoom enterChatRoomSuccess account: " + data);
//                    }
//                    NimManager.getInstance().setChatRoomEntered(true);
//                }
//
//                @Override
//                public void enterChatRoomFailed(int code) {
//                    if (LivePresenter.DEBUG_LIVE) {
//                        Log.i(TAG, "enterChatRoom enterChatRoomFailed code: " + code);
//                    }
//                }
//
//                @Override
//                public void getAccountFailed(int code) {
//                    if (LivePresenter.DEBUG_LIVE) {
//                        Log.i(TAG, "enterChatRoom getAccountFailed code: " + code);
//                    }
//                    NimManager.getInstance().setChatRoomEntered(false);
//                }
//            };
//        }
//        return mEnterChatRoomListener;
//    }

    public boolean isChatRoomEntered(){
        return NimManager.getInstance().isChatRoomEntered();
    }

    public void enterChatRoom(boolean ignoreLoginState, Context context, String roomId) {
        boolean entered = NimManager.getInstance().isChatRoomEntered();
        if (DEBUG_LIVE) {
            Log.i(TAG, "enterChatRoom roomId: " + roomId + ", entered: " + entered);
        }
        if (entered) {
            return;
        }
        // 回调聊天室用户的进入退出事件
        NimManager.getInstance().addRoomMemberChangedObserver(createRoomMemberChangedObserver());
        // 回调聊天室的消息接收事件
        NimManager.getInstance().addChatMessageReceivedListener(createChatMessageReceivedListener());
        // 进入云信聊天室，注册进入云信聊天室的结果回调, 进入成功后会调用 mPresenter.fetchRoomMembers() 拉取聊天室信息
        NimManager.getInstance().enterChatRoom(ignoreLoginState, context, roomId, createEnterChatRoomListener());
    }

    public void logoutIm(){
        NimManager.getInstance().logoutNim();

    }

    private NimManager.EnterChatRoomListener createEnterChatRoomListener() {
        if (mEnterChatRoomListener == null) {
            mEnterChatRoomListener = new NimManager.EnterChatRoomListener() {
                @Override
                public void onException(Throwable throwable) {
                    if (LivePresenter.DEBUG_LIVE) {
                        Log.i(TAG, "enterChatRoom onException: ", throwable);
                    }
                    NimManager.getInstance().setChatRoomEntered(false);
                    if (mView != null && mView instanceof HomeLiveView) {
                        ((HomeLiveView) mView).enterChatRoomFailed();
                    }
                }

                @Override
                public void loginFailed(int code) {
                    if (LivePresenter.DEBUG_LIVE) {
                        Log.i(TAG, "enterChatRoom loginFailed code: " + code);
                    }
                    NimManager.getInstance().setChatRoomEntered(false);
                    if (mView != null && mView instanceof HomeLiveView) {
                        ((HomeLiveView) mView).enterChatRoomFailed();
                    }
                }

                @Override
                public void loginSuccess(String account) {
                    // Log.i("************", "7");
                    if (LivePresenter.DEBUG_LIVE) {
                        Log.i(TAG, "enterChatRoom loginSuccess account: " + account);
                    }
                    //此处登录成功，进入聊天室是否成功还需要进一步确认
                }

                @Override
                public void enterChatRoomSuccess() {
                    // Log.i("************", "8");
                    if (LivePresenter.DEBUG_LIVE) {
                        Log.i(TAG, "enterChatRoom enterChatRoomSuccess account");
                    }
                    NimManager.getInstance().setChatRoomEntered(true);
                    if (mView != null && mView instanceof HomeLiveView) {
                        ((HomeLiveView) mView).enterChatRoomSuccess();
                    }
                }

                @Override
                public void enterChatRoomFailed(int code) {
                    if (LivePresenter.DEBUG_LIVE) {
                        Log.i(TAG, "enterChatRoom enterChatRoomFailed code: " + code);
                    }
                    NimManager.getInstance().setChatRoomEntered(false);
                    if (mView != null && mView instanceof HomeLiveView) {
                        ((HomeLiveView) mView).enterChatRoomFailed();
                    }
                }
            };
        }
        return mEnterChatRoomListener;
    }

    /**
     * 发送语音消息，同时向网宿和云信发送，以网宿的上传进度作为进度更新回调，但成功与否取决于两者的共同
     * 结果。
     *
     * @param context  上下文，网宿的SDK使用
     * @param program  上传到网宿时的附加信息
     * @param fileName 文件地址
     */
    public void sendAudioMessageToServer(Context context, RecordUploadHelper.UploadParam program,
                                         String fileName) {
        Log.e(TAG, "live---net--- sendAudioMessageToServer Thread:" + Thread.currentThread().getName());
        // 调用的此方法发送语音消息
        if(NetworkManager.getInstance().isNotHasNetwork()){
            ToastUtil.showInfo(context, R.string.no_net_work_str);
            return;
        }
        String path = HomeLiveManager.getInstance().getFilePath();
        if (path == null) {
            if (mView != null) {
                mView.showFileNotExist();
            }
            return;
        }
        File file = new File(path);
        if (!file.exists()) {
            if (mView != null) {
                mView.showFileNotExist();
            }
            return;
        }
        HomeLiveManager.getInstance().setRecorderStatus(RecorderStatus.UPLOADING);

        final String room = NimManager.getInstance().getRoomId();

        Observable wangsu = Observable.create(new ObservableOnSubscribe<Boolean>() {
            @Override
            public void subscribe(ObservableEmitter emitter) throws Exception {
                // path -> /storage/emulated/0/Android/data/com.edog.car/files/Music/K-Radio-Record/Live-Leave-message.aac
                Log.e(TAG, "live---net--- wangsu start Thread:" + Thread.currentThread().getName());
                RecordUploadHelper.uploadToAliyunOSS(context, path, program, fileName
                        , new RecordUploadCompletedCallback(emitter)
                        , new RecordUploadProgressCallback(emitter));
            }
        });

        Observable yunxin = Observable.create(new ObservableOnSubscribe<Boolean>() {
            @Override
            public void subscribe(ObservableEmitter<Boolean> emitter) {
                String path = LiveApiConstant.SEND_MESSAGE_BASE_URL + generateFileUploadedPath(fileName);
                Log.e(TAG, "live---net--- yunxin start Thread:" + Thread.currentThread().getName());
                Log.e(TAG, "live---net--- yunxin start path=" + path);
                // path -> https://iovimage.radio.cn/kradio_live_radio/rhTjbBXufN4-1696820752594.aac
                sendAudioMessageToServer(program, path, emitter);
            }
        });

        wangsu
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer() {
                    @Override
                    public void accept(Object o) throws Exception {
                        Log.e(TAG, "live---net--- wangsu showRecordUploadProgress: " + o);
                        if (mView != null) {
                            if (o instanceof Integer) {
                                Log.i(TAG, "sendAudioMessage showRecordUploadProgress: " + o);
                                mView.showRecordUploadProgress((int) o);
                            }
                        }
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        Log.e(TAG, "live---net--- wangsu accept throwable: " + throwable.getMessage());
                        if (mView != null) {
                            mView.showRecordUploadFailure();
                        }
                    }
                }, new Action() {
                    @Override
                    public void run() throws Exception {
                        Log.e(TAG, "live---net--- wangsu complete");
                        yunxin.subscribeOn(Schedulers.io())
                                .observeOn(AndroidSchedulers.mainThread())
                                .subscribe(new Consumer() {
                                    @Override
                                    public void accept(Object o) throws Exception {
                                        if (o instanceof Boolean && ((Boolean) o)) {

                                        }
                                    }
                                }, new Consumer<Throwable>() {
                                    @Override
                                    public void accept(Throwable throwable) throws Exception {
                                        Log.e(TAG, "live---net--- yunxin accept throwable: " + throwable.getMessage());
                                        if (mView != null) {
                                            mView.showRecordUploadFailure();
                                        }
                                    }
                                }, new Action() {
                                    @Override
                                    public void run() throws Exception {
                                        Log.e(TAG, "live---net--- yunxin complete");
                                        if (mView != null) {
                                            mView.showRecordUploadSuccess();
                                        }
                                    }
                                });
                    }
                });
    }

    private void sendAudioMessageToServer(RecordUploadHelper.UploadParam program, String path, ObservableEmitter<Boolean> emitter) {
        String encode = null;
        try {
            encode = URLEncoder.encode(path, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        new LiveRequest().sendMessageToServer(program.getUid(), program.getAppid(), encode, program.getLiveProgramId(), new HttpCallback<BaseResult<String>>() {
            @Override
            public void onSuccess(BaseResult<String> result) {
                if (result.getCode() == 10000) {
                    emitter.onNext(true);
                    emitter.onComplete();
                    return;
                }
                onError(new ApiException("发送失败"));
            }

            @Override
            public void onError(ApiException e) {
                emitter.tryOnError(new RuntimeException(e.getMessage()));
            }
        });
    }

    private class RecordUploadCompletedCallback implements OSSCompletedCallback<PutObjectRequest, PutObjectResult> {

        private ObservableEmitter<Integer> mEmitter;

        public RecordUploadCompletedCallback(ObservableEmitter<Integer> mEmitter) {
            this.mEmitter = mEmitter;
        }

        @Override
        public void onSuccess(PutObjectRequest request, PutObjectResult result) {
            if (DEBUG_LIVE) {
                Log.i(TAG, "onSuccess responseJson : " + result.toString());
            }
            mEmitter.onComplete();

        }

        @Override
        public void onFailure(PutObjectRequest request, ClientException clientException, ServiceException serviceException) {
            String clientMessage = "", serviceMessage = "";
            if (clientException != null) {
                clientMessage = clientException.toString();
            }
            if (serviceException != null) {
                serviceMessage = serviceException.toString();
            }
            if (DEBUG_LIVE) {
                Log.i(TAG, "onFailure client errorMessage : " + clientMessage + " ; onFailure server errorMessage :" + serviceMessage);
            }
            mEmitter.tryOnError(new RuntimeException(clientMessage));
            mEmitter.tryOnError(new RuntimeException(serviceMessage));
        }
    }

    private class RecordUploadProgressCallback implements OSSProgressCallback<PutObjectRequest> {

        private ObservableEmitter<Integer> mEmitter;

        private RecordUploadProgressCallback(ObservableEmitter<Integer> mEmitter) {
            this.mEmitter = mEmitter;
        }

        @Override
        public void onProgress(PutObjectRequest request, long currentSize, long totalSize) {
            float progress = (totalSize > 0) ? ((float) currentSize / totalSize) * 100 : -1;
            if (DEBUG_LIVE) {
                Log.i(TAG, String.format("onProgress Progress %d from %d (%f)",
                        currentSize, totalSize, progress));
            }
            mEmitter.onNext((int) progress);
        }
    }

    private static String generateFileUploadedPath(String fileName) {
        return "kradio_live_radio/" + fileName;
    }
}

