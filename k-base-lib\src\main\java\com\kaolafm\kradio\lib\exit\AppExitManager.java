package com.kaolafm.kradio.lib.exit;

import android.app.Application;
import android.os.AsyncTask;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.lib.utils.AppUtil;
import com.kaolafm.kradio.lib.utils.YTLogUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * 初始化管理管理类，用户处理各个初始化任务的初始化逻辑。该类只在Application对应的生命周期中调用，其他地方不需要调用。
 * <AUTHOR>
 * @date 2019-09-10
 */
public class AppExitManager {

    private static final String TAG = "AppInitManager";

    static {

    }

    private AppExitManager() {
    }

    private static List<AppExitTask> mAppExitTasks = new ArrayList<>();

    private static BlockingQueue<AppExitTask> mAsyncTaskQueue;

    private static volatile boolean isAsyncTaskFinished = false;

    private static boolean isMainProcess;


    static void registerExitreatment(AppExitTask appExitTask) {
        if (appExitTask != null) {
            mAppExitTasks.add(appExitTask);
        }
    }

    public static void registerInitializers(AppExitTaskContainer container) {
        if (container != null) {
            mAppExitTasks.addAll(container.appExitTasks);
            if (!ListUtil.isEmpty(container.asyncExitTasks)) {
                //对于异步初始化的task第一次不在这里添加，只是初始化容器。
                if (mAsyncTaskQueue == null) {
                    mAsyncTaskQueue = new ArrayBlockingQueue<>(container.asyncExitTasks.size());
                } else {//如果已经有了其他初始化的task，就需要在扩容以后把原来的添加上，新增的同样不在这里添加。
                    ArrayBlockingQueue<AppExitTask> appExitTasks = new ArrayBlockingQueue<>(mAppExitTasks.size() + container.asyncExitTasks.size());
                    appExitTasks.addAll(mAppExitTasks);
                    mAsyncTaskQueue = appExitTasks;
                }
            }
        }
    }

    static public boolean isTaskListEmpty(){
        if (ListUtil.isEmpty(mAppExitTasks)) {
            return true;
        }
        return false;
    }

    public static void onExit(Application application) {
        YTLogUtil.logStart(TAG, "onExit", "start");
        if (ListUtil.isEmpty(mAppExitTasks)) {
            return;
        }
        isMainProcess = AppUtil.isMainProcess(application);

        doASyncExitTasks(application);
        doSyncExitTasks(application);

        isAsyncTaskFinished = true;
        YTLogUtil.logStart(TAG, "onExit", "end");
    }

    private static void doASyncExitTasks(Application application) {
        AsyncTask.THREAD_POOL_EXECUTOR.execute(() -> asyncCreate(application));
    }

    private static void doSyncExitTasks(Application application) {
        YTLogUtil.logStart(TAG, "doSyncExitTasks", "start");
        for (AppExitTask appExitTask : mAppExitTasks) {
            if (isNotIgnore(appExitTask)) {
                appExitTask.exitreatment.onExit(application);
                if (appExitTask.isAsync && !mAsyncTaskQueue.contains(appExitTask)) {
                    mAsyncTaskQueue.add(appExitTask);
                }
            }
        }
        YTLogUtil.logStart(TAG, "doSyncExitTasks", "end");
    }

    private static void asyncCreate(Application application) {
        AppExitTask appExitTask;
        try {
            while (true) {
                appExitTask = mAsyncTaskQueue.poll(100, TimeUnit.MILLISECONDS);
                if (appExitTask == null) {
                    if (isAsyncTaskFinished) {
                        break;
                    } else {
                        continue;
                    }
                }
                if (isNotIgnore(appExitTask)) {
                    final AppExitTask finalAppExitTask = appExitTask;
                    time(appExitTask.exitreatment.getClass().getName() + " asyncCreate()",
                            () -> finalAppExitTask.exitreatment.asyncExit(application));
                }
                if (isAsyncTaskFinished && mAsyncTaskQueue.isEmpty()) {
                    break;
                }
            }
        } catch (InterruptedException e) {
            YTLogUtil.logE(TAG, "Error occurred when start AppExitTask", e);
        }
    }



//    private static void dispatch(Callback callback) {
//        if (!ListUtil.isEmpty(mAppExitTasks)) {
//        }
//    }

    private static boolean isNotIgnore(AppExitTask exitTask) {
        YTLogUtil.logStart(TAG, "isNotIgnore", "isMainProcess =" + isMainProcess + ", exitTask.inMainProcess() " + exitTask.inMainProcess() + ", exitTask.inOtherProcess() = " + exitTask.inOtherProcess());
        return (isMainProcess && exitTask.inMainProcess()) || (!isMainProcess && exitTask.inOtherProcess());
    }

    /**
     * 统计执行时间
     */
    private static long time(Runnable runnable) {
        if (runnable == null) {
            return 0;
        }
        long startTime = System.currentTimeMillis();
        runnable.run();
        return System.currentTimeMillis() - startTime;
    }

    /**
     * 统计执行时间
     */
    private static long time(String desc, Runnable runnable) {
        return time(runnable);
    }

    /**
     * 统计执行时间
     */
    static String timeStr(String desc, Runnable runnable) {
        return String.format("%s耗时:%sms\n\n", desc, time(runnable));
    }

    private interface Callback {
        void dispatch(AppExitreatment exitreatment);
    }

}
