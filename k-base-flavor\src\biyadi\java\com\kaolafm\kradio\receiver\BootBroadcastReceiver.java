package com.kaolafm.kradio.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.kaolafm.kradio.lib.base.flavor.BootCompletedLogicListener;
import com.kaolafm.kradio.lib.utils.InjectManager;

/**
 * Created by Wenchl on 2018/3/5.
 */

public class BootBroadcastReceiver extends BroadcastReceiver {

    @Override
    public void onReceive(Context context, Intent intent) {
        Log.i("BootBroadcastReceiver","BootBroadcastReceiver:"+intent.getAction());
        if (Intent.ACTION_BOOT_COMPLETED.equals(intent.getAction())) {
            BootCompletedLogicListener bootCompletedLogicListener = InjectManager.getInstance().getBootCompletedLogicListener();
            if (bootCompletedLogicListener != null) {
                bootCompletedLogicListener.onBootCompletedLogic(context,intent);
            }
        }

    }
}
