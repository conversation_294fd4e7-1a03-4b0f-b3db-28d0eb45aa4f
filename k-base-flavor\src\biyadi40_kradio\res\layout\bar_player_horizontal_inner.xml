<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_marginLeft="@dimen/x20"
    android:layout_marginRight="@dimen/x10">

    <TextView
        android:id="@+id/player_bar_title_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:ellipsize="end"
        android:singleLine="true"
        android:textColor="@color/player_radio_bar_title_text_color"
        app:layout_constraintBottom_toTopOf="@+id/player_bar_sub_layout"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="山东人说话没问题啊我觉得"
        app:layout_constraintVertical_chainStyle="packed" />

    <LinearLayout
        android:id="@+id/player_bar_sub_layout"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/player_bar_audio_content_height"
        android:layout_marginTop="@dimen/y4"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/player_bar_title_text">

        <TextView
            android:id="@+id/player_bar_sub_tag_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="@dimen/x12"
            android:background="@drawable/color_medium_line_radio_sub_title_tag_round_bg"
            android:includeFontPadding="false"
            android:paddingBottom="@dimen/y3"
            android:paddingLeft="@dimen/x8"
            android:paddingRight="@dimen/x8"
            android:paddingTop="@dimen/y3"
            android:singleLine="true"
            android:textColor="@color/player_radio_bar_subtitle_text_color"
            android:textSize="@dimen/play_bar_sub_title_text_size"
            tools:text="TopNews"
            tools:visibility="visible"
            android:visibility="gone" />

        <TextView
            android:id="@+id/player_bar_time_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="@dimen/x8"
            android:ellipsize="end"
            android:textColor="@color/player_radio_bar_subtitle_text_color"
            android:textSize="@dimen/play_bar_sub_title_text_size"
            tools:text="2020-07-16"
            tools:visibility="visible"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/player_bar_vertical_line"
            android:layout_width="@dimen/x1"
            android:layout_height="@dimen/y16"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="@dimen/x8"
            android:src="@drawable/radio_list_item_vertical_line"
            tools:visibility="visible"
            android:visibility="gone" />

        <TextView
            android:id="@+id/player_bar_sub_title_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/player_radio_bar_subtitle_text_color"
            tools:text="新石门客栈"
            android:textSize="@dimen/play_bar_sub_title_text_size" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>