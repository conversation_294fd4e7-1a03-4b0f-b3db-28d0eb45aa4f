package com.kaolafm.kradio.huawei.provider;

import android.graphics.Bitmap;
import android.view.View;

import com.huawei.carmediakit.bean.DialogInfo;
import com.huawei.carmediakit.bean.PayContentInfo;
import com.huawei.carmediakit.bean.UserInfo;
import com.huawei.carmediakit.callback.BasicCallback;
import com.huawei.carmediakit.constant.ErrorCode;
import com.huawei.carmediakit.provider.IDialogProvider;
import com.huawei.carmediakit.reporter.DialogReporter;
import com.huawei.carmediakit.reporter.UserSettingsReporter;
import com.kaolafm.kradio.huawei.utils.Constant;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;

import static com.kaolafm.kradio.huawei.utils.DialogHelper.getNeedLoginDialog;

public class DialogProvider implements IDialogProvider, IAccountLoginView {
    private static final String TAG = Constant.TAG;

    private AccountLoginPresenter loginPresenter;

    private BasicCallback<DialogInfo> mBasicCallback;

    private AccountLoginPresenter getLoginPresenter() {
        if (loginPresenter == null) {
            loginPresenter = new AccountLoginPresenter(this);
        }

        return loginPresenter;
    }

    @Override
    public void asyncRefreshQrCodeDialog(String s, BasicCallback<DialogInfo> basicCallback) {
        Logger.i(TAG, "asyncRefreshQrCodeDialog=" + s);
        mBasicCallback = basicCallback;
        getLoginPresenter().getData();
    }

    @Override
    public void asyncRefreshPayWindow(String s, BasicCallback<PayContentInfo> basicCallback) {

    }

    @Override
    public void showQRCode(String url) {
        ImageLoader.getInstance().getBitmapFromCache(AppDelegate.getInstance().getContext(),
                url, this::showQrCallback);
    }

    private void showQrCallback(Bitmap bitmap) {
        Logger.i(TAG, "bitmap=" + bitmap);
        if (mBasicCallback != null) {
            mBasicCallback.callback(getNeedLoginDialog(bitmap),
                    ErrorCode.SUCCESS, "success");
            mBasicCallback = null;
        }
    }

    @Override
    public void showBindSuccess(String name, String avatar) {
        Logger.i(TAG, "avatar=" + avatar);
        UserInfo userInfo = new UserInfo();
        userInfo.setNickName(name);
        userInfo.setProfilePicUrl(avatar);
        userInfo.setOnline(true);
        UserSettingsReporter.reportUserInfo(userInfo);
        getLoginPresenter().cancelCheck();
        DialogReporter.reportCancelDialog("LOGIN_NOTICE");
    }

    @Override
    public void showNoNetWork() {

    }

    @Override
    public void showIntercepterView(View view) {

    }
}
