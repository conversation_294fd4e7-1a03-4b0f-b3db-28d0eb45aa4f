package com.kaolafm.ad.comprehensive.processor;

import android.annotation.SuppressLint;
import android.os.Handler;
import android.os.Message;
import android.util.Log;

import com.kaolafm.ad.comprehensive.ADDataHandle.GetAdDataMap;
import com.kaolafm.ad.comprehensive.KradioAdAudioManager;
import com.kaolafm.ad.comprehensive.control.KradioAdSceneConstants;
import com.kaolafm.ad.expose.AdvertInterceptor;
import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.ad.comprehensive.implement.AdvertPlayerImpl;
import com.kaolafm.kradio.component.ActionProcessor;
import com.kaolafm.kradio.component.RealCaller;
import com.kaolafm.kradio.component.SharedConst;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.player.helper.intercept.AdPlayChainIntercept;
import com.kaolafm.opensdk.player.logic.model.PlayItemConstants;
import com.kaolafm.opensdk.player.logic.model.item.RadioPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.playlist.RadioPlayListControl;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 切换节目曝光
 * zoneId 和 adSceneId 相关wiki: http://wiki.kaolafm.com/pages/viewpage.action?pageId=11405204
 */

@SharedConst
public class AdExposeProcess implements ActionProcessor {
    private static final String TAG = "AdExposeProcess: " + PlayerConstants.LOG_TAG;
    private static final String ACTION_EXPOSE = "ad_expose_switchprogress";
    private static final String KEY_EXPOSE_AD_PLAYITEM = "key_expose_ad_playitem";
    private static final String KEY_EXPOSE_AD_CALLBACK = "key_expose_ad_callback";

    private TimeOutAdvertInterceptor mAdvertInterceptor;
    private Handler mHandler;
    private final AtomicBoolean ignore = new AtomicBoolean(false);


    public AdExposeProcess() {

    }

    @Override
    public String actionName() {
        return ACTION_EXPOSE;
    }

    @SuppressLint("LongLogTag")
    @Override
    public boolean onAction(RealCaller caller) {
//        UIThreadUtil.runUIThread(() -> KradioAdAudioManager.getInstance().cancel());
        Log.i("ChainIntercept", "===ChainIntercept ad onAction");
        PlayItem playItem = caller.getParamValue(KEY_EXPOSE_AD_PLAYITEM);
        AdPlayChainIntercept.IPlayAdEndCallback iPlayAdEndCallback = caller.getParamValue(KEY_EXPOSE_AD_CALLBACK);
        AdvertPlayerImpl advertPlayer = KradioAdAudioManager.getInstance().getAdvertPlayerimpl();
        if (advertPlayer == null) {
            notifyError(iPlayAdEndCallback);
            return false;
        }
        KradioAdAudioManager.getInstance().getAdvertPlayerimpl().
                setPlayAdEndCallback(iPlayAdEndCallback);
        int adSceneId = -1;
        Long zoneId = 0L;

        if (playItem.getType() == PlayerConstants.RESOURCES_TYPE_ALBUM) {
            adSceneId = KradioAdSceneConstants.ALBUM_SCENE;
            zoneId = GetAdDataMap.getLongADSpaceBySceneID(adSceneId);
        } else if (playItem.getType() == PlayerConstants.RESOURCES_TYPE_RADIO) {
            RadioPlayItem radioPlayItem = (RadioPlayItem) playItem;
            if (radioPlayItem.getRadioInfoData().getRadioType() == 6) {
                adSceneId = KradioAdSceneConstants.BRAND_SCENE;
            } else {
                adSceneId = KradioAdSceneConstants.RADIO_SCENE;
            }
            zoneId = GetAdDataMap.getLongADSpaceBySceneID(adSceneId);
            Log.i(TAG, "曝光广告: 碎片类型: type = 电台 zoneId = " + zoneId);
            if (zoneId == null) {
                notifyError(iPlayAdEndCallback);
                return false;
            }

            if (zoneId == -1) {
                int type = radioPlayItem.getRadioInfoData().getAdZoneChooseType();
                if (type == RadioPlayListControl.TYPE_ZONE_CHOOSE_DETAILS) {
                    zoneId = (long) radioPlayItem.getRadioInfoData().getAdZoneId();
                } else {
                    zoneId = getSwitchRadioAudioZoneId(radioPlayItem);
                    if (zoneId != null && zoneId != -1) {
                        exposure(KradioAdSceneConstants.AD_TYPE_SWITCH_RADIO_AUDIO, String.valueOf(zoneId), iPlayAdEndCallback);
                        return false;
                    }
                }
            }
        } else {
            notifyError(iPlayAdEndCallback);
            return false;
        }

        Log.i(TAG, "曝光广告: adSceneId = " + adSceneId + ", zoneId = " + zoneId);

        if (zoneId == null || zoneId == -1) {
            notifyError(iPlayAdEndCallback);
            return false;
        }

        exposure(KradioAdSceneConstants.SUB_TYPE_SWITCH_PROGROM, String.valueOf(zoneId), iPlayAdEndCallback);// 开始请求广告
        return false;
    }

    /**
     * 获取编排位广告id
     *
     * @return
     */
    private Long getSwitchRadioAudioZoneId(RadioPlayItem radioPlayItem) {
        Long zoneId = null;
        String tempType = radioPlayItem.getMapCacheData(PlayItemConstants.ITEM_KEY_AD_ZONE_TYPE);
        if (StringUtil.isEmpty(tempType)) {
            return zoneId;
        }
        if (tempType.equals(String.valueOf(RadioPlayListControl.TYPE_ZONE_CHOOSE_AUDIO))) {
            String zoneIdStr = radioPlayItem.getMapCacheData(PlayItemConstants.ITEM_KEY_AD_ZONE_ID);
            if (StringUtil.isNotEmpty(zoneIdStr)) {
                try {
                    zoneId = Long.parseLong(zoneIdStr);
                } catch (Exception e) {

                }
            }
        }
        return zoneId;
    }


    private void notifyError(AdPlayChainIntercept.IPlayAdEndCallback iPlayAdEndCallback) {
        if (iPlayAdEndCallback != null) {
            iPlayAdEndCallback.playEnd();
        }
    }

    /**
     * 曝光广告
     *
     * @param zoneId
     * @param iPlayAdEndCallback
     */
    @SuppressLint("LongLogTag")
    private void exposure(int type, String zoneId, AdPlayChainIntercept.IPlayAdEndCallback iPlayAdEndCallback) {
        Log.i(TAG, "开始曝光广告: id = " + zoneId);
        String acceptedAdTypes = "" + KradioAdSceneConstants.AD_TYPE_AUDIO + ","
                + KradioAdSceneConstants.AD_TYPE_IMAGE + ","
                + KradioAdSceneConstants.AD_TYPE_AUDIO_IMAGE + "";
        Log.i(TAG, "acceptedAdTypes:" + acceptedAdTypes);
        if (mHandler == null) {
            mHandler = new Handler();
        }
        //2秒接口没有返回直接当作失败处理
        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                Log.i(TAG, "广告曝光超时...");
                ignore.set(true);
                notifyError(iPlayAdEndCallback);
            }
        }, 2 * 1000);
        if (mAdvertInterceptor == null) {
            mAdvertInterceptor = new TimeOutAdvertInterceptor(mHandler, ignore);
        }
        AdvertisingManager.getInstance().addInterceptor(mAdvertInterceptor);
        ignore.set(false);
        AdvertisingManager.getInstance().expose(zoneId,
                type,
                String.valueOf(ScreenUtil.getScreenWidth()),
                String.valueOf(ScreenUtil.getScreenHeight()),
                acceptedAdTypes, ""
        );
    }

    private static class TimeOutAdvertInterceptor implements AdvertInterceptor {

        private final Handler mHandler;
        private final AtomicBoolean ignore;

        public TimeOutAdvertInterceptor(Handler mMyHandler, AtomicBoolean ignore) {
            this.mHandler = mMyHandler;
            this.ignore = ignore;
        }

        @SuppressLint("LongLogTag")
        @Override
        public void intercept(Chain chain) {
            String TAG = "AdExposeProcess: " + PlayerConstants.LOG_TAG;
            Log.i(TAG, "广告拦截器拦截，判断忽略场景");
            AdvertisingManager.getInstance().removeInterceptor(this);
            if (mHandler != null)
                mHandler.removeCallbacksAndMessages(null);
            if (ignore != null && ignore.compareAndSet(true, false)) {
                chain.process(null);
                Log.i(TAG, "广告拦截器拦截，忽略广告曝光");
            } else {
                chain.process(chain.advert());
                Log.i(TAG, "广告拦截器拦截，继续曝光");
            }
        }
    }
}
