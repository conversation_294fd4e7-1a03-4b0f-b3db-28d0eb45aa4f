package com.kaolafm.kradio.flavor.launcher;

import android.app.Activity;
import android.util.Log;

import com.kaolafm.utils.SystemHelper;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;

@Aspect
public class LauncherAspect {

    private String TAG = "LauncherAspect";

    @Around("execution(* com.kaolafm.launcher.LauncherActivity.onWindowFocusChanged(..))")
    public void dealWithFocusChanged(ProceedingJoinPoint joinPoint) throws Throwable {
        // 方式二：使用AOP切面方式处理底部导航栏显示隐藏需求
        Activity activity = (Activity) joinPoint.getThis();
        boolean hasFocus = (boolean) joinPoint.getArgs()[0];
        Log.i(TAG, "this_obj" + ":::" + activity);
        Log.i(TAG, "has_focus" + ":::" + hasFocus);
        if (hasFocus) {
            SystemHelper.hideSystemUI(activity);
        }
    }

}
