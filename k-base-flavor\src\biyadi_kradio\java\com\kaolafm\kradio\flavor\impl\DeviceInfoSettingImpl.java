package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.hardware.bydauto.bodywork.BYDAutoBodyworkDevice;

import com.kaolafm.kradio.flavor.utils.DeviceInfoUtil;
import com.kaolafm.kradio.lib.base.flavor.DeviceInfoSetting;


/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-03-22 11:46
 ******************************************/
public final class DeviceInfoSettingImpl implements DeviceInfoSetting {

    @Override
    public void setInfoForSDK(Context context) {
        try {
            String carType = String.valueOf(BYDAutoBodyworkDevice.getInstance(context).getAutoModelName());
            DeviceInfoUtil.setDeviceIdAndCarType(null, carType);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
