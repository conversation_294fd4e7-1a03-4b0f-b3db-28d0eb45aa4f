<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.kaolafm.kradio.flavor">

    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <application
        android:allowBackup="false"
        android:debuggable="false"
        tools:replace="android:allowBackup,android:debuggable">

        <activity
            android:name="com.kaolafm.kradio.flavor.ExitActivity"
            android:launchMode="singleTop"
            android:theme="@android:style/Theme.NoDisplay" />
        <activity
            android:name="com.kaolafm.kradio.flavor.OpenPrivacyActivity"
            android:launchMode="singleTop"
            android:theme="@android:style/Theme.NoDisplay" />

<!--        <service-->
<!--            android:name="com.kaolafm.launcher.InitService"-->
<!--            android:exported="false"-->
<!--            android:permission="android.permission.BIND_JOB_SERVICE" />-->
<!--        <service-->
<!--            android:name="com.kaolafm.launcher.scene.SceneService"-->
<!--            android:enabled="true"-->
<!--            android:exported="false">-->
<!--            <intent-filter>-->
<!--                <action android:name="com.kaolafm.kradio.action.scene" />-->
<!--            </intent-filter>-->
<!--        </service>-->

<!--        <activity-->
<!--            android:name="com.kaolafm.launcher.LauncherActivity"-->
<!--            android:configChanges="orientation|screenSize|locale|layoutDirection|keyboard|screenLayout|uiMode"-->
<!--            android:exported="false"-->
<!--            android:launchMode="singleTask"-->
<!--            android:screenOrientation="unspecified"-->
<!--            android:theme="@style/AppThemeCompat"-->
<!--            android:windowSoftInputMode="adjustPan">-->
<!--            <intent-filter>-->
<!--                <action android:name="android.intent.action.MAIN" />-->
<!--            </intent-filter>-->
<!--            <meta-data-->
<!--                android:name="distractionOptimized"-->
<!--                android:value="true" />-->
<!--        </activity>-->
<!--        <receiver-->
<!--            android:name="com.kaolafm.notification.NotificationDeleteReceiver"-->
<!--            android:exported="false">-->
<!--            <intent-filter>-->
<!--                <action android:name="com.kradio.delete.notification" />-->
<!--            </intent-filter>-->
<!--        </receiver>-->
    </application>
</manifest>