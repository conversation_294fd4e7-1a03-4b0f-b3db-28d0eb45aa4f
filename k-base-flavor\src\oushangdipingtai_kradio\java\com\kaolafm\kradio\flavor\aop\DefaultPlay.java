package com.kaolafm.kradio.flavor.aop;

import android.util.Log;


import com.kaolafm.kradio.network.FlavorApiRequest;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.sdk.core.model.DefaultPlayData;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;

import java.util.List;

@Aspect
public class DefaultPlay {
    private static final String TAG = "DefaultPlay";
    private static boolean isFirstTime = true;
    private FlavorApiRequest mFlavorApiRequest;

    @Around("execution(* com.kaolafm.opensdk.player.logic.PlayerManager.start(..))")
    public void defaultPlayerManager(ProceedingJoinPoint point) throws Throwable {
        Log.i(TAG, "defaultPlayerManager: isFirstTime = " + isFirstTime);
        if (isFirstTime) {
            mFlavorApiRequest = FlavorApiRequest.getInstance();
            mFlavorApiRequest.getDefaultPlayInfo(new HttpCallback<DefaultPlayData>() {
                @Override
                public void onSuccess(DefaultPlayData defaultPlayData) {
                    if (defaultPlayData == null) {
                        try {
                            point.proceed();
                        } catch (Throwable throwable) {
                            throwable.printStackTrace();
                        }
                        return;
                    }
                    Log.i(TAG, "getDefaultPlay onSuccess: " + defaultPlayData.getName() + "        " + defaultPlayData.getId() + "        " + defaultPlayData.getType());
                    PlayerManager.getInstance().play(defaultPlayData.getId(), String.valueOf(defaultPlayData.getType()));
                }

                @Override
                public void onError(ApiException e) {
                    Log.i(TAG, "getDefaultPlay onError: " + e.toString());
                    try {
                        point.proceed();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                    }
                }
            });
        } else {
            point.proceed();
        }
        isFirstTime = false;
    }


    @Around("execution(* com.kaolafm.kradio.client.ClientImpl.playMusic(..))")
    public void clientImplplayMusic(ProceedingJoinPoint point) throws Throwable {
        //https://app.huoban.com/tables/2100000007530121/items/2300001342733183?userId=1874548
        Log.i(TAG, "clientImplplayMusic: ");
        isFirstTime = false;
        point.proceed();
    }

}
