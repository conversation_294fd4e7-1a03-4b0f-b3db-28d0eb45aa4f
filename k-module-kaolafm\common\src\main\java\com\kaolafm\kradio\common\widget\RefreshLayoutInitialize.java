package com.kaolafm.kradio.common.widget;

import android.content.Context;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout;
import com.lcodecore.tkrefreshlayout.header.progresslayout.ProgressLayout;

/******************************************
 * 类描述: RefreshLayout 默认初始化配置
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2017-12-04 10:15
 ******************************************/

public final class RefreshLayoutInitialize {
    /**
     * 初始化下默认下拉刷新配置信息
     * 注意，这个地方不能写死像素，否则在有些屏幕上不能下拉刷新
     *
     * @param context
     * @param twinklingRefreshLayout
     */
    public static void initDefaultRefreshLayoutHeaderStyle(Context context, TwinklingRefreshLayout twinklingRefreshLayout) {
        ProgressLayout header = new ProgressLayout(context);
        twinklingRefreshLayout.setHeaderView(header);
        twinklingRefreshLayout.setFloatRefresh(true);
        twinklingRefreshLayout.setOverScrollRefreshShow(false);
        twinklingRefreshLayout.setHeaderHeight(context.getResources().getDimensionPixelOffset(R.dimen.y50));
        twinklingRefreshLayout.setMaxHeadHeight(context.getResources().getDimensionPixelOffset(R.dimen.y55));
        twinklingRefreshLayout.setOverScrollHeight(context.getResources().getDimensionPixelOffset(R.dimen.y50));
        header.showArrow(false);
        header.setColorSchemeColors(ResUtil.getColor(R.color.circular_progress_color));
    }
}
