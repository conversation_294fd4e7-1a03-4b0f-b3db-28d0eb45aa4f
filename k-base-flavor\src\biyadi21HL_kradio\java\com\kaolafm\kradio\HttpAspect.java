package com.kaolafm.kradio;

import okhttp3.HttpUrl.Builder;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;

/**
 * 用于切换sdk的http和https
 * <AUTHOR>
 * @date 2019-06-18
 */
@Aspect
public class HttpAspect {
    private boolean useHttp = false;

    @AfterReturning(value = "execution(* com.kaolafm.opensdk.http.core.RetrofitUrlManager.buildUrl(..))", returning = "builder")
    public void changeToHttp(Builder builder, JoinPoint point) throws Throwable {
        if (builder != null && useHttp) {
            builder.scheme("http");
        }
    }

    /*@After("execution(* com.kaolafm.kradio.k_kaolafm.user.UserFragment.initView(..))")
    public void onClick(JoinPoint point) throws Throwable {
        View view = (View) point.getArgs()[0];
        if (view != null) {
            view.findViewById(R.id.login_title).setOnClickListener(v -> {
                ToastUtil.showInfo(AppDelegate.getInstance().getContext(), "切换到HTTP");
                useHttp = true;
            });
        }
    }*/
}
