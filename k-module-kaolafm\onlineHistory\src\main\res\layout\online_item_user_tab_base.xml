<?xml version="1.0" encoding="utf-8"?>
<com.kaolafm.kradio.component.ui.base.view.ScaleConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootView"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <com.kaolafm.kradio.component.ui.base.view.OvalImageView
        android:id="@+id/iv_purchase_type"
        app:layout_constraintBottom_toTopOf="@id/user_tab_desc"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginBottom="@dimen/y16"
        android:layout_width="@dimen/m80"
        android:layout_height="@dimen/m36"
        app:rid_type="0" />

    <View
        android:id="@+id/backgroundIv"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/iv_purchase_type"
        android:layout_width="@dimen/m205"
        android:layout_height="@dimen/m205"
        android:background="@drawable/online_ring_unselected"
        app:layout_constraintDimensionRatio="1:1" />

    <com.kaolafm.kradio.component.ui.base.view.OvalImageView
        android:id="@+id/iv_play_cover"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="@dimen/y17"
        android:layout_width="@dimen/m76"
        android:layout_height="@dimen/m76"
        android:scaleType="centerCrop"
        app:oval_radius="@dimen/m38"
        app:rid_type="0"
        tools:src="@drawable/media_default_pic" />

    <TextView
        android:id="@+id/user_tab_content"
        app:layout_constraintBottom_toTopOf="@id/user_tab_title"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:alpha="0.6"
        android:textColor="@color/online_item_subscription_content_text_color"
        android:textSize="@dimen/text_size1"
        android:text=""/>

    <com.kaolafm.kradio.component.ui.base.view.KradioTextView
        android:id="@+id/user_tab_title"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toTopOf="@id/iv_purchase_type"
        android:layout_marginBottom="@dimen/y12"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        app:kt_font_weight="0.7"
        android:textColor="@color/online_user_item_title_text_color"
        android:textSize="@dimen/text_size3"/>



    <com.kaolafm.kradio.component.ui.base.view.KradioTextView
        android:id="@+id/user_tab_desc"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:singleLine="true"
        android:ellipsize="end"
        app:kt_font_weight="0.7"
        android:textColor="@color/online_user_item_title_text_color"
        android:textSize="@dimen/text_size1"
        android:text=""/>

</com.kaolafm.kradio.component.ui.base.view.ScaleConstraintLayout>