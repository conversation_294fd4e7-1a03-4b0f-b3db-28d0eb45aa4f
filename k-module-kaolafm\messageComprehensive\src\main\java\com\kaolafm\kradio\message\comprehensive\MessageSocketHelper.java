package com.kaolafm.kradio.message.comprehensive;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.google.gson.Gson;
import com.kaolafm.kradio.common.http.CommonRequestParamsUtil;
import com.kaolafm.kradio.home.utils.AppDateUtils;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.bean.CrashMessageBean;
import com.kaolafm.kradio.lib.utils.KaolaAppConfigData;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.api.CrashMessageBaseBean;
import com.kaolafm.opensdk.crash.CrashPlayerHelper;
import com.kaolafm.opensdk.crash.IcrashPlay;
import com.kaolafm.opensdk.emergencybroadcast.UploadLocationListener;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.socket.SocketApiConstants;
import com.kaolafm.opensdk.socket.SocketEvent;
import com.kaolafm.opensdk.socket.SocketListener;
import com.kaolafm.opensdk.socket.SocketManager;

import java.util.HashMap;
import java.util.Map;

public class MessageSocketHelper {

    private static boolean locationNotEmpty(){
        String lng = KaolaAppConfigData.getInstance().getLng();
        String lat = KaolaAppConfigData.getInstance().getLat();
        return !TextUtils.isEmpty(lat) && !TextUtils.isEmpty(lng);
    }

    private static boolean inited = false;
    public static void handleMessageSocket(Context context, CrashMessageArrivedListener arrivedListener, NextCrashMessagePlayedListener nextCrashMessagePlayedListener) {

        if (!inited && locationNotEmpty()) {
            sendMessageSocket(context, arrivedListener, nextCrashMessagePlayedListener);
            inited = true;
        }
    }

    private static void sendMessageSocket(Context context, CrashMessageArrivedListener arrivedListener, NextCrashMessagePlayedListener nextCrashMessagePlayedListener) {
        /**
         * 添加连续播放监听
         */
        CrashPlayerHelper.getInstance().setIcrashPlay(new IcrashPlay() {
            @Override
            public void onPlay(CrashMessageBaseBean crashMessageBaseBean) {
                if (crashMessageBaseBean == null) {
                    return;
                }
                String json = new Gson().toJson(crashMessageBaseBean);
                CrashMessageBean crashMessageBean = new Gson().fromJson(json, CrashMessageBean.class);
                String playType = crashMessageBean.getPlayType();

                if(playType != null && playType.equals("2") ){
                    if (!PlayerManagerHelper.getInstance().isPlaying()) {
                        CrashPlayerHelper.getInstance().removeImmediatelDate().playTips(context.getString(R.string.online_message_voice_url));
                    }
                } else {
                    Log.i("sendMessageSocket", "--------插播连续播放----" + json);
                    if (AppDateUtils.getInstance().isConfirmPlayMsg(crashMessageBean)) {
                        CrashPlayerHelper.getInstance().startPlay();
                    } else {
                        //语音播报开关被关闭需要播放提示音
                        CrashPlayerHelper.getInstance()
                                .removeImmediatelDate()//移除这一条
                                .playTips(context.getString(R.string.online_message_voice_url));
                    }
                }

                if(nextCrashMessagePlayedListener != null){
                    nextCrashMessagePlayedListener.onNextCrashMessagePlayed(crashMessageBean);
                }
//                showMessageDialog(crashMessageBean);
            }
        });
        SocketListener mSocketListener = new SocketListener<CrashMessageBaseBean>() {

            @Override
            public String getEvent() {
                return "SYSTEM_PUSH";
            }

            @Override
            public Map<String, Object> getParams(Map<String, Object> params) {
                return params;
            }

            @Override
            public boolean isNeedParams() {
                return true;
            }

            @Override
            public boolean isNeedRequest() {
                return true;
            }

            @Override
            public void onSuccess(CrashMessageBaseBean baseBeans) {
                String json = new Gson().toJson(baseBeans);
                Log.d("sendMessageSocket", "--------插播接口成功-----" + json);
                if(arrivedListener != null){
                    arrivedListener.onCrashMessageArrived(json);
                }
//                changeMessagePlay(json);
            }

            @Override
            public void onError(ApiException e) {
                Logger.e("sendMessageSocket", e.getMessage());
            }
        };

        Log.d("sendMessageSocket", "--------CommonRequestParamsUtil.getCommonParams()-----" + CommonRequestParamsUtil.getCommonParams());
        SocketManager.getInstance().setMap(CommonRequestParamsUtil.getCommonParams()).setSocketHost(SocketApiConstants.SOCKET_HOST).request(mSocketListener);

        SocketListener mEbPushSocketListener = new SocketListener<CrashMessageBaseBean>() {

            @Override
            public String getEvent() {
                return SocketEvent.API_MESSAGE;
            }

            @Override
            public Map<String, Object> getParams(Map<String, Object> params) {
                return params;
            }

            @Override
            public boolean isNeedParams() {
                return true;
            }

            @Override
            public boolean isNeedRequest() {
                return true;
            }

            @Override
            public void onSuccess(CrashMessageBaseBean baseBeans) {
                String json = new Gson().toJson(baseBeans);
                Log.d("sendMessageSocket", "--------插播接口成功-----" + json);
                if(arrivedListener != null){
                    arrivedListener.onCrashMessageArrived(json);
                }
//                changeMessagePlay(json);
            }

            @Override
            public void onError(ApiException e) {
                Logger.e("sendMessageSocket", e.getMessage());
            }
        };

        Log.d("sendMessageSocket", "--------CommonRequestParamsUtil.getCommonParams()-----" + CommonRequestParamsUtil.getCommonParams());
        SocketManager.getInstance().setMap(CommonRequestParamsUtil.getCommonParams()).setSocketHost(SocketApiConstants.SOCKET_HOST).request(mEbPushSocketListener);

    }

    public static void uploadLoc(){
        HashMap<String, String> commonParams = new HashMap<>();
        commonParams.put("EIO", "3");
        commonParams.put("transport", "websocket");

        String deviceId = commonParams.get("deviceid");
        if (deviceId != null) {
            commonParams.put("deviceId", deviceId);
        }
        String requestId = "time:" + System.currentTimeMillis();
        Log.i("uploadLoc", "requestId:" + requestId + " deviceId:" + deviceId);
        commonParams.put("requestId", requestId);

        commonParams.putAll(CommonRequestParamsUtil.getCommonParams());

        SocketManager.getInstance()
                .setMap(commonParams)
                .setSocketHost(SocketApiConstants.SOCKET_HOST)
                .request(UploadLocationListener.INSTANCE);
    }

    public interface NextCrashMessagePlayedListener{
        void onNextCrashMessagePlayed(CrashMessageBean crashMessageBean);
    }

    public interface CrashMessageArrivedListener{
        void onCrashMessageArrived(String json);
    }
}
