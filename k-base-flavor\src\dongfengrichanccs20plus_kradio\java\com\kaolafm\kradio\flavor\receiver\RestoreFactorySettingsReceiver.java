package com.kaolafm.kradio.flavor.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.SharedPreferenceUtil;

import java.io.File;

import static com.kaolafm.kradio.lib.utils.Constants.USER_PROMPT_FIRST_SP_FILE_NAME;
import static com.kaolafm.kradio.lib.utils.Constants.USER_PROMPT_FIRST_SP_ITEM_VALUE;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: guhb
 * @time: 2021-03-25 20:17
 ******************************************/
public final class RestoreFactorySettingsReceiver extends BroadcastReceiver {
    private static final String TAG = "RestoreFactoryReceiver";

    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        if ("com.hsae.action.restoreFactorySetting".equals(action)) {
            Log.i(TAG, "onReceive----------------->" + action);
            cleanSharedPreference(context);
        }
    }

    private static void cleanSharedPreference(Context context) {
        deleteFilesByDirectory(new File("/data/data/"
                + context.getPackageName() + "/shared_prefs"));
        SharedPreferenceUtil sharedPreferenceUtil = SharedPreferenceUtil
                .newInstance(AppDelegate.getInstance().getContext(), USER_PROMPT_FIRST_SP_FILE_NAME, Context.MODE_PRIVATE);
        sharedPreferenceUtil.putBoolean(USER_PROMPT_FIRST_SP_ITEM_VALUE, true);
    }


    private static void deleteFilesByDirectory(File directory) {
        if (directory != null && directory.exists() && directory.isDirectory()) {
            for (File item : directory.listFiles()) {
                item.delete();
            }
        }
    }
}
