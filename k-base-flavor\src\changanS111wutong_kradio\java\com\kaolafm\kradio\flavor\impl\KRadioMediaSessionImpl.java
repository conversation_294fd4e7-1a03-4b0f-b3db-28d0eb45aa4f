package com.kaolafm.kradio.flavor.impl;

import android.car.Car;
import android.car.CarNotConnectedException;
import android.car.input.CarInputManager;
import android.car.input.InputFilter;
import android.content.ComponentName;
import android.content.Context;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.util.Log;
import android.view.KeyEvent;

import com.kaolafm.kradio.lib.base.flavor.KRadioMediaSessionInter;
import com.kaolafm.utils.MediaButtonManagerUtil;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-10-16 16:48
 ******************************************/
public class KRadioMediaSessionImpl implements KRadioMediaSessionInter {
    private static final String TAG = "KRadioMediaSessionImpl";
    private Car mCar;
    private CarInputManager mCarInputManager;
    private MediaButtonManagerUtil mMediaButtonManagerUtil = new MediaButtonManagerUtil();

    @Override
    public void registerMediaSession(Object... args) {
        Context context = (Context) args[0];
        try {
            mCar = Car.createCar(context, new ServiceConnection() {
                @Override
                public void onServiceConnected(ComponentName name, IBinder service) {
                    try {
                        mCarInputManager = (CarInputManager) mCar.getCarManager(Car.INPUT_SERVICE);
                        registerCallBack();
                    } catch (CarNotConnectedException e) {
                        e.printStackTrace();
                    }
                }

                @Override
                public void onServiceDisconnected(ComponentName name) {
                }
            });
        } catch (NoSuchMethodError error) {
            error.printStackTrace();
        }
    }

    @Override
    public void unregisterMediaSession(Object... args) {
        unRegisterCallBack();
    }

    @Override
    public void release(Object... args) {

    }

    CarInputManager.CarInputEventCallback callback = new CarInputManager.CarInputEventCallback() {
        @Override
        public void onKeyEvent(KeyEvent keyEvent, int i) {
            int keyCode = keyEvent.getKeyCode();
            Log.i(TAG, "onKeyEvent keyCode = " + keyCode);
            mMediaButtonManagerUtil.manageMediaButtonClick(keyCode);
        }
    };

    private void registerCallBack() {
        int keyCodes[] = {
                KeyEvent.KEYCODE_MEDIA_PLAY,
                KeyEvent.KEYCODE_MEDIA_PAUSE,
                KeyEvent.KEYCODE_MEDIA_STOP,
                KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE,
                KeyEvent.KEYCODE_F3,
                KeyEvent.KEYCODE_MEDIA_NEXT,
                KeyEvent.KEYCODE_F5,
                KeyEvent.KEYCODE_MEDIA_PREVIOUS};
        int len = keyCodes.length;
        InputFilter[] inputFilters = new InputFilter[len];
        for (int i = 0; i < len; i++) {
            InputFilter inputFilter = new InputFilter(keyCodes[i], 0);
            inputFilters[i] = inputFilter;
        }
        try {
            mCarInputManager.registerCallback(callback, inputFilters);
        } catch (CarNotConnectedException e) {
            e.printStackTrace();
        }
    }

    private void unRegisterCallBack() {
        if (mCarInputManager != null) {
            mCarInputManager.unregisterCallback(callback);
        }
    }
}
