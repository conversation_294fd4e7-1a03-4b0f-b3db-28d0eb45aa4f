<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal">

    <!--    无用的布局-->
    <LinearLayout
        android:id="@+id/qrLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/qrCode"
            android:layout_width="@dimen/m200"
            android:layout_height="@dimen/m200"
            android:src="@drawable/about_us_btn_bg" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/y40"
            android:gravity="center_horizontal"
            android:text="@string/scan_qrcode_to_login_and_buy_from_misco_program"
            android:textColor="@color/text_color_2"
            android:textSize="@dimen/text_size3" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:drawableLeft="@drawable/ic_coin"
            android:drawablePadding="@dimen/x12"
            android:text="积分"
            android:textColor="@color/text_color_1"
            android:textSize="@dimen/text_size5" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/y18"
            android:gravity="bottom"
            android:orientation="horizontal">

            <com.kaolafm.view.ScrollNumberView
                android:id="@+id/scrollNumberView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:snv_number="2000"
                app:snv_textColor="@color/color_1"
                app:snv_textSize="@dimen/m98" />

            <TextView
                android:id="@+id/coinCount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/color_1"
                android:visibility="gone" />

            <top.wuhaojie.library.MultiScrollNumber
                android:id="@+id/scroll_number"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/color_1"
                android:visibility="gone"
                app:number_size="49"
                app:target_number="2000" />

            <com.kaolafm.kradio.coin.NumberFlipView
                android:id="@+id/nfv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/x12"
                android:text="分"
                android:textColor="@color/color_1"
                android:textSize="@dimen/text_size15" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>