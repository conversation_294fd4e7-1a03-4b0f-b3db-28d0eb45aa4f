package com.kaolafm.kradio.home.comprehensive.ui.view;

import android.content.res.Configuration;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentActivity;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.CheckBox;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.kaolafm.kradio.component.ui.base.skin.SkinHelper;
import com.kaolafm.kradio.common.utils.CommonUtils;
import com.kaolafm.kradio.common.utils.FlavorUtil;
import com.kaolafm.kradio.common.comprehensive.web.WebViewActivity;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.dialog.DialogListener;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;

/**
 * <AUTHOR>
 **/
public class PermissionCenterDialogFragment extends DialogFragment {

    private View mTvDialogButtonMainLayout;
    private TextView mTvDialogBottomCancel;
    private TextView mTvDialogBottomDefine;
    private TextView mTvDialogBottomMessage;
    private LinearLayout check_ll;
    private CheckBox check_box;
    private View mRootView;
    private DialogListener.OnNativeListener<DialogFragment> mNativeListener;
    private DialogListener.OnPositiveListener<DialogFragment> mPositiveListener;
    private CharSequence message;
    private CharSequence leftBtnText;
    private CharSequence rightBtnText;
    private boolean canShowButton = true;
    private boolean canShowCheckBox = false;//是否显示checkBox

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getContentView();
    }

    @Override
    public void onStart() {
        super.onStart();
        Window window = getDialog().getWindow();
        WindowManager.LayoutParams params = window.getAttributes();
        if (SkinHelper.isNightMode()) {
            params.dimAmount = 0.9f;
        } else {
            params.dimAmount = 0.7f;
        }
        window.setAttributes(params);
        CommonUtils.getInstance().initGreyStyle(window);
    }


    protected View getContentView() {
        FragmentActivity activity = getActivity();
        View inflate = View.inflate(activity, R.layout.dialog_fragment_permission_center_2btn, null);
        mTvDialogButtonMainLayout = inflate.findViewById(R.id.tv_dialog_button_main_layout);
        mTvDialogBottomCancel = inflate.findViewById(R.id.tv_dialog_bottom_cancel);
        mTvDialogBottomDefine = inflate.findViewById(R.id.tv_dialog_bottom_define);
        mTvDialogBottomMessage = inflate.findViewById(R.id.tv_dialog_bottom_message);
        check_ll = inflate.findViewById(R.id.check_ll);
        mRootView = inflate.findViewById(R.id.root_layout);
        mRootView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

            }
        });
        mTvDialogBottomCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!AntiShake.check(v.getId())) {
                    if (mNativeListener != null) {
                        mNativeListener.onClick(PermissionCenterDialogFragment.this);
                    } else {
                        dismiss();
                    }
                }
            }
        });

        mTvDialogBottomDefine.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!AntiShake.check(v.getId())) {
                    if (canShowCheckBox) {
                        if (!check_box.isSelected()) {
                            ToastUtil.showInfo(getContext(), R.string.launcher_agreement_first_toast);
                        } else {
                            if (mPositiveListener != null) {
                                mPositiveListener.onClick(PermissionCenterDialogFragment.this);
                            }
                            dismiss();
                        }
                    } else {
                        if (mPositiveListener != null) {
                            mPositiveListener.onClick(PermissionCenterDialogFragment.this);
                        }
                        dismiss();
                    }

                }
            }
        });
        mTvDialogBottomMessage.setText(CommonUtils.getInstance().initAgreementTextAndClick(message.toString()
                , new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!NetworkUtil.isNetworkAvailable(getContext(), true)) {
                    return;
                }
                String theme = "dark";
                if (SkinHelper.isDayMode()) {
                    theme = "light";
                }
                String web = ResUtil.getString(R.string.http_url_server_agreement)
                        + "?theme=" + theme + "&bgColor=transparent&contentSize="
                        + (int) ResUtil.getDimension(R.dimen.m22)
                        + "&showTitle=1"
                        + "&marginL=0"
                        + "&unit=1"
                        + "&marginR=" + ResUtil.getDimen(R.dimen.n33)
                        + "&textIndent=0";
                WebViewActivity.start(getActivity(),
                        FlavorUtil.getHttp443Url(web), getResources().getString(R.string.launcher_agreement0)
                        , Constants.PAGE_ID_ACCOUNT_PROTOCOL);
//                                  group1.setVisibility(View.VISIBLE);
//                                  group.setVisibility(View.GONE);
//                                  tvTitle.setText("");
//                                  String web = FlavorUtil.getHttp443Url(ResUtil.getString(R.string.http_url_server_agreement));
//                                  showWebView(webView, web);
//                                  initNoticeHolder().web = 1;
            }
        }, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!NetworkUtil.isNetworkAvailable(getContext(), true)) {
                    return;
                }
                String theme = "dark";
                if (SkinHelper.isDayMode()) {
                    theme = "light";
                }
                String CUR_PAGE = ResUtil.getString(R.string.http_url_policy)
                        + "?theme=" + theme + "&bgColor=transparent&contentSize="
                        + (int) ResUtil.getDimension(R.dimen.m22)
                        + "&showTitle=1"
                        + "&marginL=0"
                        + "&unit=1"
                        + "&marginR=" + ResUtil.getDimen(R.dimen.n33)
                        + "&textIndent=0";
                WebViewActivity.start(getContext(),
                        FlavorUtil.getHttp443Url(CUR_PAGE), getResources().getString(R.string.launcher_agreement1)
                        , Constants.PAGE_ID_ACCOUNT_POLICY);

//                                  group1.setVisibility(View.VISIBLE);
//                                  group.setVisibility(View.GONE);
//                                  tvTitle.setText("");
//                                  showWebView(webView, FlavorUtil.getHttp443Url(ResUtil.getString(R.string.http_url_policy)));
//                                  initNoticeHolder().web = 2;
            }
        }));
        mTvDialogBottomMessage.setHighlightColor(0);
        mTvDialogBottomMessage.setMovementMethod(LinkMovementMethod.getInstance());
        ViewUtil.setViewVisibility(mTvDialogButtonMainLayout, canShowButton ? View.VISIBLE : View.GONE);
        ViewUtil.setViewVisibility(check_ll, canShowCheckBox ? View.VISIBLE : View.GONE);
        if (!TextUtils.isEmpty(leftBtnText)) {
            mTvDialogBottomDefine.setText(leftBtnText);
        }

        if (!TextUtils.isEmpty(rightBtnText)) {
            mTvDialogBottomCancel.setText(rightBtnText);
        }
        return inflate;
    }

    public void setMessage(CharSequence message) {
        this.message = message;
    }

    public void setLeftBtnText(CharSequence leftBtnText) {
        this.leftBtnText = leftBtnText;
    }

    public void setRightBtnText(CharSequence rightBtnText) {
        this.rightBtnText = rightBtnText;
    }

    public void setOnNativeListener(DialogListener.OnNativeListener<DialogFragment> nativeListener) {
        mNativeListener = nativeListener;
    }

    public void setOnPositiveListener(DialogListener.OnPositiveListener<DialogFragment> positiveListener) {
        mPositiveListener = positiveListener;
    }

    public void setCanShowButton(boolean canShowButton) {
        this.canShowButton = canShowButton;
    }

    public void setCanShowCheckBox(boolean canShowCheckBox) {
        this.canShowCheckBox = canShowCheckBox;
    }


    protected void showAccordingToScreen(int orientation) {

        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            changeLandView();
        } else {
            changePortraitView();
        }
    }

    //横屏显示
    private void changeLandView() {
        ViewGroup.MarginLayoutParams rootViewLayoutParams =
                (ViewGroup.MarginLayoutParams) mRootView.getLayoutParams();
        rootViewLayoutParams.topMargin = ResUtil.getDimen(R.dimen.y198);
    }

    //竖屏显示
    private void changePortraitView() {
        ViewGroup.MarginLayoutParams rootViewLayoutParams =
                (ViewGroup.MarginLayoutParams) mRootView.getLayoutParams();
        rootViewLayoutParams.topMargin = ResUtil.getDimen(R.dimen.y294);
    }
}
