<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <HorizontalScrollView
        android:id="@+id/hsv_subcategory_tab"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scrollbars="none"
        app:layout_constraintBottom_toTopOf="@id/view_subcategory_divider"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.flyco.tablayout.CommonTabLayout
            android:id="@+id/ctb_subcategory_tab_title"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/y70"
            app:tl_iconVisible="false"
            app:tl_indicator_anim_enable="true"
            app:tl_indicator_color="@color/tab_indicator_underline_color"
            app:tl_indicator_height="@dimen/tab_indicator_height"
            app:tl_indicator_margin_top="@dimen/x20"
            app:tl_indicator_style="TRIANGLE_INVERTED"
            app:tl_indicator_width="@dimen/tab_indicator_width"
            app:tl_indicator_width_equal_title="true"
            app:tl_tab_padding="@dimen/x36"
            app:tl_textBold="SELECT"
            app:tl_textSelectColor="@color/tab_indicator_select_color"
            app:tl_textSelectSize="@dimen/all_ctg_sub_title_size"
            app:tl_textUnselectColor="@color/tab_indicator_unselect_color"
            app:tl_textsize="@dimen/all_ctg_sub_title_size" />
    </HorizontalScrollView>

    <View
        android:id="@+id/view_subcategory_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/y1"
        android:layout_alignParentTop="true"
        android:background="@color/subcategory_divider_color"
        app:layout_constraintTop_toBottomOf="@id/hsv_subcategory_tab" />

    <com.kaolafm.kradio.common.widget.GridTouchInterceptRecyclerView
        android:id="@+id/grv_subcategory_content"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/view_subcategory_divider" />

    <ViewStub
        android:id="@+id/vs_layout_error_page"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout="@layout/layout_each_status_page" />

</androidx.constraintlayout.widget.ConstraintLayout>