<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/m272"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guide_one"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.3" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guide_two"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.7" />


    <com.kaolafm.kradio.player.comprehensive.play.view.PlayerControlViewNext
        android:id="@+id/player_control_next_view"
        android:layout_width="@dimen/radio_player_pre_or_next_width_size"
        android:layout_height="@dimen/radio_player_pre_or_next_height_size"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/guide_two"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:next_location="2"
        app:next_icon="@drawable/comprehensive_playerbar_next"
        tools:ignore="MissingConstraints" />

    <com.kaolafm.kradio.player.comprehensive.play.view.PlayerControlViewPlay
        android:id="@+id/player_control_play_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/guide_one"
        app:layout_constraintRight_toLeftOf="@id/guide_two"
        app:layout_constraintTop_toTopOf="parent"
        app:play_location="2"
        tools:ignore="MissingConstraints" />

    <com.kaolafm.kradio.player.comprehensive.play.view.PlayerControlViewPrevious
        android:id="@+id/player_control_previous_view"
        android:layout_width="@dimen/radio_player_pre_or_next_width_size"
        android:layout_height="@dimen/radio_player_pre_or_next_height_size"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/guide_one"
        app:layout_constraintTop_toTopOf="parent"
        app:previous_location="2"
        app:previous_icon="@drawable/comprehensive_playerbar_prev"
        tools:ignore="MissingConstraints" />



</merge>