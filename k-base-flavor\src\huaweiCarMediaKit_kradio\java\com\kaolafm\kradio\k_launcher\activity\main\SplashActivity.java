package com.kaolafm.kradio.k_launcher.activity.main;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.util.Log;

import com.huawei.carmediakit.bean.PageTab;
import com.huawei.carmediakit.session.CarMediaUIStarter;
import com.kaolafm.auto.home.HubActivity;
import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.huawei.utils.Constant;
import com.kaolafm.kradio.huawei.convert.DataConverterUtil;
import com.kaolafm.kradio.huawei.listener.PlayerListener;
import com.kaolafm.opensdk.api.operation.OperationRequest;
import com.kaolafm.opensdk.api.operation.model.category.Category;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import java.util.List;

/******************************************
 * 类描述: 启动页面
 *
 ******************************************/
public class SplashActivity extends HubActivity {
    private static final String TAG = SplashActivity.class.getSimpleName();
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.i(TAG, "onCreate");
        setTheme(android.R.style.Theme_NoDisplay);
        setContentView(R.layout.splash_layout);
    }

    @Override
    protected void startToLauncher() {
        new PlayerListener().init();
        getTabData();
    }

    private void getTabData() {
        new OperationRequest().getCategoryList("0", true, 0, new HttpCallback<List<Category>>() {
            @Override
            public void onSuccess(List<Category> categoryList) {
                Constant.pageTabList = DataConverterUtil.toPageTagList(categoryList);
                PageTab pageTab = new PageTab();
                pageTab.setMediaId("-1");
                pageTab.setTabName("我的");
                Constant.pageTabList.add(pageTab);
                goToMainPage();
            }

            @Override
            public void onError(ApiException e) {
                Log.e(TAG, "error=" + e.toString());
                PageTab pageTab = new PageTab();
                pageTab.setMediaId("-1");
                pageTab.setTabName("我的");
                Constant.pageTabList.add(pageTab);
                goToMainPage();
            }
        });
    }

    private void goToMainPage() {
        CarMediaUIStarter.startMediaPage(this);
        finish();
    }

}
