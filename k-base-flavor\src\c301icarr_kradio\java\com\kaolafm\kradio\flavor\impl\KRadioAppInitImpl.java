package com.kaolafm.kradio.flavor.impl;

import android.content.IntentFilter;

import com.kaolafm.kradio.flavor.receiver.ExitAppReceiver;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioAppInitInter;

public class KRadioAppInitImpl implements KRadioAppInitInter {
    @Override
    public void onAppInit() {
        IntentFilter filter = new IntentFilter();
        filter.addAction("iflytek.intent.action.exitApp");
        AppDelegate.getInstance().getContext().registerReceiver(new ExitAppReceiver(), filter);
    }
}
