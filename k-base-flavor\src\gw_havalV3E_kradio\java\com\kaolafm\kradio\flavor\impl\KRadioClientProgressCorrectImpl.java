package com.kaolafm.kradio.flavor.impl;


import com.kaolafm.kradio.lib.base.flavor.KRadioClientProgressCorrectInter;
import com.kaolafm.opensdk.player.logic.PlayerManager;

public class KRadioClientProgressCorrectImpl implements KRadioClientProgressCorrectInter {
    @Override
    public long correctProgress(long nowProgress) {
        if (nowProgress <= 0) {
            nowProgress = PlayerManager.getInstance().getCurPlayItem().getPosition();
        }
        return nowProgress;
    }
}
