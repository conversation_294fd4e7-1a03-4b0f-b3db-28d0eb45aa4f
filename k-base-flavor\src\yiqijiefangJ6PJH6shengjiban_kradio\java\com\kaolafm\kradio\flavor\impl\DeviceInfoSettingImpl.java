package com.kaolafm.kradio.flavor.impl;

import android.content.ContentResolver;
import android.content.Context;
import android.provider.Settings;
import android.util.Log;

import com.kaolafm.kradio.flavor.utils.DeviceInfoUtil;
import com.kaolafm.kradio.lib.base.flavor.DeviceInfoSetting;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-10-20 18:18
 ******************************************/
public final class DeviceInfoSettingImpl implements DeviceInfoSetting {

    @Override
    public void setInfoForSDK(Context context) {
        ContentResolver contentResolver = context.getContentResolver();
        String carType = Settings.Global.getString(contentResolver, "faw_vist_vehicle_type");
        String deviceId = Settings.Global.getString(contentResolver, "faw_vist_vehicle_imsi");
        Log.i("DeviceInfoSettingImpl", "setInfoForSDK carType = " + carType + " deviceId = " + deviceId);
        DeviceInfoUtil.setDeviceIdAndCarType(deviceId, carType);
    }
}
