<resources>

<!--    <style name="AppThemeTrans" parent="@style/AppBaseTheme">-->
<!--        <item name="android:windowTranslucentStatus">true</item>-->
<!--        <item name="android:windowTranslucentNavigation">false</item>-->
<!--        &lt;!&ndash;Android 5.x开始需要把颜色设置透明，否则导航栏会呈现系统默认的浅灰色&ndash;&gt;-->
<!--        <item name="android:statusBarColor">@color/colorTransparent</item>-->
<!--    </style>-->
</resources>
