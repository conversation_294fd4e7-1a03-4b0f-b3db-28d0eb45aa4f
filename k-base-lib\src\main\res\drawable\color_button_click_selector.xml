<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:state_pressed="true">
        <shape android:shape="oval" >
            <size android:width="@dimen/m86" android:height="@dimen/m86" />
            <solid android:color="@color/transparent_color" />
        </shape>
    </item>

    <item >
        <shape android:shape="oval">
            <size android:width="@dimen/m86" android:height="@dimen/m86" />
            <solid android:color="@color/transparent_color" />
        </shape>
    </item>

    <item android:state_focused="true">
        <shape android:shape="oval">
            <size android:width="@dimen/m86" android:height="@dimen/m86" />
            <solid android:color="@color/transparent_color" />
        </shape>
    </item>
</selector>