package com.kaolafm.kradio.history.comprehensive.ui;

import androidx.recyclerview.widget.DiffUtil;

import com.kaolafm.kradio.lib.bean.HistoryItem;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import java.util.List;

public class HistoryDiffCallback extends DiffUtil.Callback {

    private List<HistoryItem> mOldDatas, mNewDatas;

    public HistoryDiffCallback(List<HistoryItem> oldDatas, List<HistoryItem> newDatas) {
        mOldDatas = oldDatas;
        mNewDatas = newDatas;
    }

    @Override
    public int getOldListSize() {
        return mOldDatas.size();
    }

    @Override
    public int getNewListSize() {
        return mNewDatas.size();
    }

    @Override
    public boolean areItemsTheSame(int oldItemPosition, int newItemPosition) {
        HistoryItem historyItem = mOldDatas.get(oldItemPosition);
        HistoryItem historyItem1 = mNewDatas.get(newItemPosition);
        if (historyItem1 == null && historyItem == null) return true;
        if (historyItem1 == null || historyItem == null) return false;
        return String.valueOf(historyItem.getType()).equals(String.valueOf(historyItem1.getType()))
                && String.valueOf(historyItem.getRadioId()).equals(String.valueOf(historyItem1.getRadioId()))
                ;
    }

    @Override
    public boolean areContentsTheSame(int oldItemPosition, int newItemPosition) {
        HistoryItem historyItem = mOldDatas.get(oldItemPosition);
        HistoryItem historyItem1 = mNewDatas.get(newItemPosition);
        if (historyItem1 == null && historyItem == null) return true;
        if (historyItem1 == null || historyItem == null) return false;
        PlayItem playItem = PlayerManagerHelper.getInstance().getCurPlayItem();
        return String.valueOf(historyItem.getType()).equals(String.valueOf(historyItem1.getType()))
                && String.valueOf(historyItem.getRadioId()).equals(String.valueOf(historyItem1.getRadioId()))
                && String.valueOf(historyItem.getRadioTitle()).equals(String.valueOf(historyItem1.getRadioTitle()))
                && String.valueOf(historyItem.getPicUrl()).equals(String.valueOf(historyItem1.getPicUrl()))
                && String.valueOf(historyItem.getCurrentProgramName()).equals(String.valueOf(historyItem1.getCurrentProgramName()))
                && String.valueOf(historyItem.getAudioTitle()).equals(String.valueOf(historyItem1.getAudioTitle()))
                && historyItem.isOffline() == historyItem1.isOffline()
                && historyItem.getOrderNum() == historyItem1.getOrderNum()
                && historyItem.getVip() == historyItem1.getVip()
                && historyItem.getFine() == historyItem1.getFine()
                ;
    }
}
