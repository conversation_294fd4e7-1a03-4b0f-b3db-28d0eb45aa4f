package com.kaolafm.gradle.plugin.component
/**
 * 用于生成常量的信息
 * <AUTHOR>
 * @date 2019-07-19
 */
class ConstantInfo {
    String name
    Object value
    String desc

    ConstantInfo(String name, Object value, String desc) {
        this.name = name
        this.value = value
        this.desc = desc
    }

    ConstantInfo() {
    }


    @Override
    public String toString() {
        return "ConstantInfo{" +
                "name='" + name + '\'' +
                ", value=" + value +
                ", desc='" + desc + '\'' +
                '}'
    }

    @Override
    boolean equals(Object o) {
        if (!o instanceof ConstantInfo) {
            return false
        }
        return this.toString().equals(o.toString())
    }
}