package com.kaolafm.kradio.user.channel;

import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.HostConstant;
import io.reactivex.Single;
import java.util.List;
import retrofit2.http.GET;
import retrofit2.http.Headers;

/**
 * <AUTHOR>
 * @date 2019/4/29
 */
interface ChannelService {
    @Headers(HostConstant.DOMAIN_HEADER_OPEN_KAOLA)
    @GET("/v2/app/kradioApk")
    Single<BaseResult<List<Channel>>> getChannelList();
}
