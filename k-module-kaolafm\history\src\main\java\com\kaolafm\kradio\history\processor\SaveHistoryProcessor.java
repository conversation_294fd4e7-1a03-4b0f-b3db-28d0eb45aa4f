package com.kaolafm.kradio.history.processor;

import android.util.Log;

import com.kaolafm.kradio.component.ActionProcessor;
import com.kaolafm.kradio.component.RealCaller;
import com.kaolafm.kradio.component.SharedConst;
import com.kaolafm.kradio.history.HistoryManager;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;


/**
 * 上报历史到服务端
 *
 * <AUTHOR>
 * @date 2019-10-23
 */
@SharedConst
public class SaveHistoryProcessor implements ActionProcessor {
    private static final String TAG = "SaveHistoryProcessor";

    private static final String ACTION_UPLOAD = "upload_history";

    private static final String KEY_PLAY_ITEM = "key_play_item_history";

    private static final String KEY_DO_NOT_UPDATE_UI = "key_do_not_update_ui";

    private static final String KEY_IS_SYNC_SAVE = "key_is_sync_save";

    private static final String KEY_ITEM_TYPE = "key_item_type";
    private static final String TYPE_QUIT_SAVE = "type_quit_save";//退出保存
    private static final String TYPE_PLAY_SAVE_PRE = "type_play_save_pre";//保存前一个
    private static final String TYPE_PLAY_SAVE = "TYPE_PLAY_SAVE";//保存当前

    @Override
    public String actionName() {
        return ACTION_UPLOAD;
    }

    @Override
    public boolean onAction(RealCaller caller) {
        PlayItem playItem = caller.getParamValue(KEY_PLAY_ITEM);
        boolean donotUpdateUI = caller.getParamValue(KEY_DO_NOT_UPDATE_UI);

        HistoryManager manager = HistoryManager.getInstance();

        String type = caller.getParamValue(KEY_ITEM_TYPE);
        switch (type) {
            case TYPE_QUIT_SAVE: {
                if (playItem == null) {
                    Log.i(TAG, "onAction TYPE_QUIT_SAVE saveHistory playItem is null");
                } else {
                    Log.i(TAG, "onAction TYPE_QUIT_SAVE saveHistory playItem position = " + playItem.getPosition());
                }
                manager.saveHistoryWhenDestroy(playItem);
                break;
            }
            case TYPE_PLAY_SAVE_PRE: {
                manager.saveHistory(playItem, true);
                break;
            }
            case TYPE_PLAY_SAVE: {
                manager.saveHistory(playItem, donotUpdateUI);
                break;
            }
            default:
                break;
        }
        return false;
    }
}
