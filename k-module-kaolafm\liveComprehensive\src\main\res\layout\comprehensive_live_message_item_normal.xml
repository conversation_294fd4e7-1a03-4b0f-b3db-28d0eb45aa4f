<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/comprehensive_live_message_item_bg"
    android:paddingVertical="@dimen/comprehensive_live_message_margin_vertical"
    android:paddingHorizontal="@dimen/comprehensive_live_message_margin_horizontal"
    android:minHeight="@dimen/comprehensive_live_message_min_height">


    <TextView
        android:id="@+id/contentTv"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constrainedWidth="true"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintVertical_bias="0"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/m24"
        tools:text="这里积分卡拉及时反馈将卡及时的反馈； 是内容积分卡的设计开发进度款九分裤蓝色的肌肤 净空法师酒店房间看了这里是内容" />
</androidx.constraintlayout.widget.ConstraintLayout>