package com.kaolafm.kradio.auto.appwidget.remoteviews;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.RectF;
import android.os.Build;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;
import android.widget.RemoteViews;

import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.utils.ScreenUtil;

/**
 * Created by chen.tuxi on 2020/2/11.
 * 进度控件
 */


public class ProgressView extends View {

    private static final String TAG = "kradio.ProgressView";

    private static final int STROKE_WIDTH = 1;
//    public static final int PROGRESS_WIDTH = 360;
//    public static final int PROGRESS_HRIGT = 20;
//    public static final int PROGRESS_LINE_HRIGT = 4;

    private Bitmap mBitmapPoint;
    private Paint mPaint;
    private float mProgress;

    int width;
    int height;
    int lineheight;

    //
    private Canvas canvas;
    private Bitmap progessBitmap;
    private Paint paint;
    private PorterDuffXfermode clear;
    private PorterDuffXfermode src;

    private Path mPath;

    private int paddingleft = 0;
    private int paddingright = 10;
    int mBitmapPointWidth;
    //


    public ProgressView(Context context) {
        this(context, null);
    }

    public ProgressView(Context context, int width, int height, int lineheight) {
        this(context, null);
        this.width = width;
        this.height = height;
        this.lineheight = lineheight;
    }

    public ProgressView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        mPaint = new Paint();
        mPaint.setColor(Color.parseColor("#2671FB"));
        mPaint.setStrokeWidth(ScreenUtil.dp2px(STROKE_WIDTH));
        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setAntiAlias(true);
        mBitmapPoint = BitmapFactory.decodeResource(context.getResources(), R.drawable.widget_progress_point);
//        mBitmapPoint = resizeImage(mBitmapPoint, 10, 10);
        mBitmapPointWidth = mBitmapPoint.getWidth();
        mPath = new Path();
        paint = new Paint();
        paint.setStrokeWidth(2);
        clear = new PorterDuffXfermode(PorterDuff.Mode.CLEAR);
        src = new PorterDuffXfermode(PorterDuff.Mode.SRC);
    }


    public Bitmap resizeImage(Bitmap bitmap, int width, int height) {
        int bmpWidth = bitmap.getWidth();
        int bmpHeight = bitmap.getHeight();

        float scaleWidth = ((float) width) / bmpWidth;
        float scaleHeight = ((float) height) / bmpHeight;

        Matrix matrix = new Matrix();
        matrix.postScale(scaleWidth, scaleHeight);

        return Bitmap.createBitmap(bitmap, 0, 0, bmpWidth, bmpHeight, matrix, true);
    }

    public void updateProgress(float progress) {
        this.mProgress = progress;
        //Bitmap.Config.ARGB_4444必须使用含有透明度的值，否则背景为黑色
        if (progessBitmap == null) {
            progessBitmap = Bitmap.createBitmap(this.width, this.height, Bitmap.Config.ARGB_4444);
        }
        if (canvas == null) {
            canvas = new Canvas(progessBitmap);
        }
        invalidate();
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);


        //裁剪区域值
        int left = 0;
        int top = 0;
        int right = width;
        int bottom = height;

        //绘制的进度值
        int drawleft = paddingleft;
        int drawtop = (height - lineheight) / 2;
        int drawright = paddingleft + (int) ((right) * mProgress);
        int drawbottom = (height + lineheight) / 2;

        canvas.save();
        mPath.addRect(new RectF(left, top, right, bottom), Path.Direction.CW);
        canvas.clipPath(mPath);//将Canvas按照上面的区域截取
        //fixme 测试完了去掉
        Log.d(TAG, "drawleft: " + drawleft + ", drawtop: " + drawtop + ", drawright: " + drawright + ", drawbottom: " + drawbottom);
        canvas.drawRect(drawleft, drawtop, drawright, drawbottom, mPaint);
        canvas.drawBitmap(mBitmapPoint, drawright, (height - mBitmapPoint.getWidth()) / 2, mPaint);
        canvas.restore();

    }


    public void drawToRemoteViews(RemoteViews views, int appwidget_iv, float progress) {
        //更新进度
        updateProgress(progress);

        paint.setXfermode(clear);
        canvas.drawPaint(paint);
        paint.setXfermode(src);
        //把进度View更新到Bitmap中
        draw(canvas);
        //更新Widget的进度位图
        views.setBitmap(appwidget_iv, "setImageBitmap", progessBitmap);

    }
}
