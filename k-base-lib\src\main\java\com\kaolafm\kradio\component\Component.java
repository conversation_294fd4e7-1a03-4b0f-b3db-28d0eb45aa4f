package com.kaolafm.kradio.component;

/**
 * <AUTHOR>
 * @date 2019-07-01
 */
@SharedConst
public interface Component {

    /**
     * 该方法接受其他模块发送的要执行的逻辑。该接口在{@link CallServiceInterceptor}中回调。
     * @param caller
     * @return true--表示使用异步将结果返回。即不立即将结果回调，而是在其他位置通过{@link ComponentClient#sendResult(String, ComponentResult)}发送结果给调用者。<br>
     *        false--表示使用同步将结果返回。将结果直接回调过去，即在该方法return之前调用{@link ComponentClient#sendResult(String, ComponentResult)}发送结果给调用者。
     *        如果没有数据返给调用者，可以不回调。默认返回{@link ComponentResult#defaultNullResult()}
     */
    boolean onCall(RealCaller caller);
}
