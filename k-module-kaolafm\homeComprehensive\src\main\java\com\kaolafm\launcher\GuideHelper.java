package com.kaolafm.launcher;

import android.content.Context;
import android.graphics.Rect;
import android.text.Spanned;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.lib.utils.SharedPreferenceUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;

import java.util.ArrayList;
import java.util.Deque;
import java.util.LinkedList;
import java.util.List;

/**
 * 可见即可说引导页面
 */
public class GuideHelper {

    private static final String CAN_SEE_CAN_SAY_GUIDE_STORE = "can_see_can_say_guide_store";
    private static final String CAN_SEE_CAN_SAY_GUIDE_SHOWED_KEY = "can_see_can_say_guide_showed_key";

    private static boolean isGuideShowed(LauncherActivity activity){
        SharedPreferenceUtil sp = SharedPreferenceUtil.getInstance(activity, CAN_SEE_CAN_SAY_GUIDE_STORE);
        return sp.getBoolean(CAN_SEE_CAN_SAY_GUIDE_SHOWED_KEY, false);
    }
    public static void setGuideShowed(LauncherActivity activity, boolean b){
        SharedPreferenceUtil sp = SharedPreferenceUtil.getInstance(activity, CAN_SEE_CAN_SAY_GUIDE_STORE);
        sp.putBoolean(CAN_SEE_CAN_SAY_GUIDE_SHOWED_KEY, b);
    }

    public static void handleGuide(LauncherActivity activity){
        if(isGuideShowed(activity)){
            return;
        }
        setGuideShowed(activity, true);

        activity.getWindow().getDecorView().postDelayed( () -> {
            View target = getFirstBigCardAsTargetView(activity);
            if(target != null){

                ViewStub vs = activity.findViewById(R.id.guide_view_stub);
                if(vs != null){
                    View inflatedView = vs.inflate();
                    
                    // 将引导层重新添加到DecorView以绕过Activity的padding限制，确保全屏覆盖
                    if (inflatedView != null) {
                        ViewGroup originalParent = (ViewGroup) inflatedView.getParent();
                        ViewGroup decorView = (ViewGroup) activity.getWindow().getDecorView();
                        
                        // 移除原来的父子关系
                        originalParent.removeView(inflatedView);
                        
                        // 添加到DecorView顶层，使用全屏布局参数
                        FrameLayout.LayoutParams fullScreenParams = new FrameLayout.LayoutParams(
                            ViewGroup.LayoutParams.MATCH_PARENT,
                            ViewGroup.LayoutParams.MATCH_PARENT
                        );
                        decorView.addView(inflatedView, fullScreenParams);
                    }
                }

                View root = initAndGetGuideRootView(activity);
                GuideBgView rootBg = root.findViewById(R.id.guide_root_bg);

                View step1_op_panel = root.findViewById(R.id.guide_step1_op_panel);

                TextView guide_step1_op_desc = step1_op_panel.findViewById(R.id.guide_step1_op_desc);

                TextView guide_step1_button = step1_op_panel.findViewById(R.id.guide_step1_button);

                rootBg.setTargetRectFocuesdListener(rect -> {
                    int screenWidth = ScreenUtil.getScreenWidth();
                    if(rectInScreen(screenWidth, rect)){
                        int centerX = rect.left + (rect.right - rect.left)/2;
                        if(centerX > screenWidth/2){

                            LinearLayout.LayoutParams descLp = (LinearLayout.LayoutParams)guide_step1_op_desc.getLayoutParams();
                            descLp.leftMargin = ResUtil.getDimen(R.dimen.m28);
                            guide_step1_op_desc.setLayoutParams(descLp);
//
                            LinearLayout.LayoutParams buttonLp = (LinearLayout.LayoutParams)guide_step1_button.getLayoutParams();
                            buttonLp.leftMargin = ResUtil.getDimen(R.dimen.m28);
                            guide_step1_button.setLayoutParams(buttonLp);

                            step1_op_panel.setBackgroundResource(R.drawable.guide_op_panel_bg_step1_flip_y);
//                        step1_op_panel.setLeft(rect.left);
//                        step1_op_panel.setBottom(rect.bottom - ResUtil.getDimen(R.dimen.m146));
                            step1_op_panel.setX(rect.left - ResUtil.getDimen(R.dimen.x712) + ResUtil.getDimen(R.dimen.m28));
                            step1_op_panel.setY(rect.bottom - ResUtil.getDimen(R.dimen.m146));

                        } else {
                            step1_op_panel.setBackgroundResource(R.drawable.guide_op_panel_bg_step1);
                            step1_op_panel.setX(rect.right - ResUtil.getDimen(R.dimen.m28));
                            step1_op_panel.setY(rect.bottom - ResUtil.getDimen(R.dimen.m146));
                        }
                    }
                });

                TextView card_title_tv = target.findViewById(R.id.card_title_tv);
                if(card_title_tv != null){
                    String titleRaw = "";
                    CharSequence textCS = card_title_tv.getText();
                    if(textCS != null){
                        if (textCS instanceof Spanned){
                            Spanned textSpanned = (Spanned) textCS;
                            Object[] spans = textSpanned.getSpans(0, textSpanned.length(), Object.class);
                            if (spans != null && spans.length > 0){
                                try {
                                    int spanStart = textSpanned.getSpanStart(spans[spans.length - 1]);
                                    int spanEnd = textSpanned.getSpanEnd(spans[spans.length - 1]);
                                    titleRaw = textCS.toString().substring(spanEnd);
                                } catch (Exception e) {
                                    titleRaw = textCS.toString();
                                }
                            } else {
                                titleRaw = textCS.toString();
                            }
                        } else {
                            titleRaw = textCS.toString();
                        }
                    }
                    String title = StringUtil.abbreviate(titleRaw, 0,  10);
                    guide_step1_op_desc.setText("您可说标题【"+title+"】来播放内容");
                }

                guide_step1_button.setOnClickListener( v -> {
                    step1_op_panel.setVisibility(View.GONE);
                    handleStep2(activity, root, rootBg);
                });

                rootBg.setHoleType(GuideBgView.HOLE_TYPE_RECT);
                rootBg.setTargetView(target);
                rootBg.invalidate();

                startFocusTargetView(rootBg, target, GuideBgView.HOLE_TYPE_RECT);
            }

        }, 1000);
    }

    private static boolean rectInScreen(int screenWidth, Rect rect){
        return rect.left > 0 && rect.right < screenWidth;
    }

    private static View initAndGetGuideRootView(LauncherActivity activity){
        View root = activity.findViewById(R.id.guild_root);
        root.setVisibility(View.VISIBLE);

        root.setClickable(true);

        // 注释：引导层现在通过重新定位到DecorView来解决状态栏覆盖问题

        root.findViewById(R.id.guide_close).setOnClickListener(v-> root.setVisibility(View.GONE));

        //reset state
        root.findViewById(R.id.guide_step1_op_panel).setVisibility(View.VISIBLE);
        root.findViewById(R.id.guide_step2_op_panel).setVisibility(View.GONE);
        root.findViewById(R.id.guide_step3_op_panel).setVisibility(View.GONE);
        root.findViewById(R.id.guide_root_bg).setVisibility(View.VISIBLE);
        root.findViewById(R.id.guide_root_bg_step3).setVisibility(View.GONE);

        return root;
    }

    interface Step1TargetViewFoundListener{
        void onStep1TargetViewFond(View target);
    }

    private static View getFirstBigCardAsTargetView(LauncherActivity activity){

        ViewGroup home_view_pager = activity.findViewById(R.id.home_view_page);
        List<View> bigCardViews = new ArrayList<>();
        travel(home_view_pager, bigCardViews);

        int[] locs = new int[2];

        for(int i = 0; i < bigCardViews.size(); i++ ){
            View tmp = bigCardViews.get(i);
            if(tmp.isShown()){
                tmp.getLocationOnScreen(locs);
                if(locs[0] > 0){
                    return tmp;
                }
            }
        }

        return null;
    }

    private static void travel(View view, List<View> bigCardViews){
        Rect rect = new Rect();
        Deque<View> stack = new LinkedList<>();
        stack.push(view);
        while ( !stack.isEmpty() ){
            View tmp = stack.pop();
            if(tmp.getId() == R.id.big_card_root_for_guide){
                if( !isCovered(tmp, rect) ){
                    bigCardViews.add(tmp);
                }
            }
            if(tmp instanceof ViewGroup){
                int cnt = ((ViewGroup) tmp).getChildCount();
                for(int i  = cnt-1; i >= 0; i--){
                    stack.push(((ViewGroup) tmp).getChildAt(i));
                }
            }
        }
    }

    private static boolean isCovered(View view, Rect rect) {
        boolean cover = false;
        cover = view.getGlobalVisibleRect(rect);
        if (cover) {
            if (rect.width() >= view.getMeasuredWidth() && rect.height() >= view.getMeasuredHeight()) {
                return false;
            }
        }
        return true;
    }
    private static void handleStep2(LauncherActivity activity, View root, GuideBgView rootBg){
        View step2_op_panel = root.findViewById(R.id.guide_step2_op_panel);
        step2_op_panel.setVisibility(View.VISIBLE);

        rootBg.setTargetRectFocuesdListener(rect -> {
            step2_op_panel.setX(rect.left -ResUtil.getDimen(R.dimen.m8));
            step2_op_panel.setY(rect.bottom);
        });

        step2_op_panel.findViewById(R.id.guide_step2_button).setOnClickListener(v -> {
            step2_op_panel.setVisibility(View.GONE);
            handleStep3(root, rootBg);
        });

        View userIcon = activity.findViewById(R.id.home_login_ll);
        startFocusTargetView(rootBg, userIcon, GuideBgView.HOLE_TYPE_CIRCLE);
    }

    private static void handleStep3(View root, GuideBgView guideRootBgView){
        guideRootBgView.setVisibility(View.GONE);
        View step3Bg = root.findViewById(R.id.guide_root_bg_step3);
        step3Bg.setVisibility(View.VISIBLE);

        View step3_op_panel = root.findViewById(R.id.guide_step3_op_panel);

        View guide_step3_op_desc = step3_op_panel.findViewById(R.id.guide_step3_op_desc);
        guide_step3_op_desc.setOnClickListener(v ->{
            RouterManager.getInstance().jumpPage(RouterConstance.CAN_SEE_CAN_SAY_COMPREHENSIVE_URL);
            root.setVisibility(View.GONE);
        });


        step3_op_panel.setVisibility(View.VISIBLE);
        step3_op_panel.findViewById(R.id.guide_step3_button).setOnClickListener( v -> {
            root.setVisibility(View.GONE);
        });
    }
    private static void startFocusTargetView(GuideBgView guideRootBgView, View target, int holeType){
        guideRootBgView.setVisibility(View.VISIBLE);
        guideRootBgView.setTargetView(target);
        guideRootBgView.setHoleType(holeType);
        guideRootBgView.invalidate();
    }
}