package com.iflytek.autofly.home.aidl;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @name AIUILauncher
 * @time 2020/1/8 10:29
 */
public class ILrcRow implements Parcelable {

    /**
     * 开始时间 为00:10:00
     ***/
    private String timeStr;
    /**
     * 开始时间 毫米数  00:10:00  为10000
     **/
    private int time;
    /**
     * 歌词内容
     **/
    private String content;
    /**
     * 该行歌词显示的总时间
     **/
    private int totalTime;

    public ILrcRow(String timeStr, int time, String content, int totalTime) {
        this.timeStr = timeStr;
        this.time = time;
        this.content = content;
        this.totalTime = totalTime;
    }

    public String getTimeStr() {
        return timeStr;
    }

    public void setTimeStr(String timeStr) {
        this.timeStr = timeStr;
    }

    public int getTime() {
        return time;
    }

    public void setTime(int time) {
        this.time = time;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getTotalTime() {
        return totalTime;
    }

    public void setTotalTime(int totalTime) {
        this.totalTime = totalTime;
    }

    protected ILrcRow(Parcel in) {
        timeStr = in.readString();
        time = in.readInt();
        content = in.readString();
        totalTime = in.readInt();
    }

    public static final Creator<ILrcRow> CREATOR = new Creator<ILrcRow>() {
        @Override
        public ILrcRow createFromParcel(Parcel in) {
            return new ILrcRow(in);
        }

        @Override
        public ILrcRow[] newArray(int size) {
            return new ILrcRow[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(timeStr);
        dest.writeInt(time);
        dest.writeString(content);
        dest.writeInt(totalTime);
    }
}
