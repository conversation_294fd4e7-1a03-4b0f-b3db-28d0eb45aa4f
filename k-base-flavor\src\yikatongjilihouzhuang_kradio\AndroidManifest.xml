<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.kaolafm.kradio.flavor">

    <application>
        <!--亿咖通账号授权回调:3.3.2.0版本-->
        <activity
            android:name="com.edog.car.stub.EntryActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@android:style/Theme.NoDisplay" />

        <receiver android:name=".impl.AppLifeCycleReceiver">
            <intent-filter>
                <action android:name="com.kaolafm.client.ACTION_ONCREATE"/>
            </intent-filter>
        </receiver>

        <!-- 一卡通widget注册 因需要设置resource="@xml/appwidget_provider" 所以无法在主目录注册会导致编译不过  -->
        <receiver android:name="com.kaolafm.kradio.widget.YiKaTongAppWidgetProvider">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
                <action android:name="com.kradio.yikatong.widget.exit" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/appwidget_provider" />
        </receiver>
        <!--亿咖通新增配置信息-->
        <meta-data
            android:name="eCarX_OpenAPI_AppId"
            android:value="58946fa69154a14398e63334b5d91bc3" />
        <meta-data
            android:name="eCarX_OpenAPI_AppKey"
            android:value="38ff2888cef557a8880f867ad019fc46" />

        <meta-data
            android:name="com.ecarx.membercenter.BuildInfo.APP_KEY"
            android:value="58946fa69154a14398e63334b5d91bc3" />
    </application>
</manifest>


