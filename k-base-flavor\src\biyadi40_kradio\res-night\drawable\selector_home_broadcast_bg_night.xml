<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_selected="false">
        <shape android:shape="rectangle">
            <gradient android:angle="90" android:endColor="#00252b35" android:startColor="#ff252b35" />
        </shape>
    </item>
    <item android:state_selected="true">
        <layer-list>
            <item>
                <shape android:shape="rectangle">
                    <gradient android:angle="90" android:endColor="#00252b35" android:startColor="#ff252b35" />
                </shape>
            </item>
            <item android:drawable="@drawable/card_small_fg_playing" />
        </layer-list>
    </item>
</selector>