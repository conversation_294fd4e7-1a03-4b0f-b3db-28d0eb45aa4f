package com.kaolafm.kradio.clientControlerForKradio.ex;

import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.google.gson.Gson;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.clientControlerForKradio.ClientAccountLoginHelp;
import com.kaolafm.kradio.clientControlerForKradio.ClientPlayer;
import com.kaolafm.kradio.clientControlerForKradio.PlayerImpl;
import com.kaolafm.kradio.common.ErrorInfo;
import com.kaolafm.kradio.lib.BuildConfig;
import com.kaolafm.kradio.lib.bean.HistoryItem;
import com.kaolafm.kradio.common.KradioSDKManager;
import com.kaolafm.kradio.common.ResultCallback;
import com.kaolafm.kradio.common.event.LogoutBindEvent;
import com.kaolafm.kradio.component.ComponentClient;
import com.kaolafm.kradio.constant.SettingPropertiesProcessorConst;
import com.kaolafm.kradio.constant.UserComponentConst;
import com.kaolafm.kradio.history.HistoryManager;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioAccountBindInter;
import com.kaolafm.kradio.lib.common.ResType;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.subscribe.SubscribeModel;
import com.kaolafm.kradio.user.UserInfoManager;
import com.kaolafm.kradio.user.ui.AccountLoginModel;
import com.kaolafm.opensdk.OpenSDK;
import com.kaolafm.opensdk.account.token.AccessTokenManager;
import com.kaolafm.opensdk.account.token.KaolaAccessToken;
import com.kaolafm.opensdk.api.broadcast.BroadcastDetails;
import com.kaolafm.opensdk.api.broadcast.BroadcastRequest;
import com.kaolafm.opensdk.api.broadcast.ProgramDetails;
import com.kaolafm.opensdk.api.login.LoginRequest;
import com.kaolafm.opensdk.api.login.model.QRCodeInfo;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.opensdk.player.core.listener.OnPlayLogicListener;
import com.kaolafm.opensdk.player.core.utils.PlayerCustomizeManager;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.sdk.core.IExecuteResult;
import com.kaolafm.sdk.core.cmd.Command;
import com.kaolafm.sdk.core.cmd.IsKaolRunningCmd;
import com.kaolafm.sdk.core.ex.ErrorCode;
import com.kaolafm.sdk.core.ex.bean.BroadcastRadioDetailData;
import com.kaolafm.sdk.core.ex.bean.BroadcastRadioPlayItem;
import com.kaolafm.sdk.core.ex.bean.Page;
import com.kaolafm.sdk.core.ex.cmd.BindListenerCmd;
import com.kaolafm.sdk.core.ex.cmd.BindTingbanCmd;
import com.kaolafm.sdk.core.ex.cmd.BroadcastCurrentProgramCmd;
import com.kaolafm.sdk.core.ex.cmd.BroadcastDateCmd;
import com.kaolafm.sdk.core.ex.cmd.BroadcastProgramDetailCmd;
import com.kaolafm.sdk.core.ex.cmd.BroadcastProgramListCmd;
import com.kaolafm.sdk.core.ex.cmd.BroadcastRadioDetailCmd;
import com.kaolafm.sdk.core.ex.cmd.CityBroadcastListCmd;
import com.kaolafm.sdk.core.ex.cmd.ContinuePlayCmd;
import com.kaolafm.sdk.core.ex.cmd.GetHistoryCmd;
import com.kaolafm.sdk.core.ex.cmd.GetUserInfoCmd;
import com.kaolafm.sdk.core.ex.cmd.GetVersionCodeCmd;
import com.kaolafm.sdk.core.ex.cmd.IsSubscribedCmd;
import com.kaolafm.sdk.core.ex.cmd.PageCmd;
import com.kaolafm.sdk.core.ex.cmd.RecommendPgcListCmd;
import com.kaolafm.sdk.core.ex.cmd.RequestAudioFocusCmd;
import com.kaolafm.sdk.core.ex.cmd.UnbindTingbanCmd;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;

import static com.kaolafm.kradio.lib.utils.Constants.START_PAGE;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: Executor231.java                                               
 *                                                                  *
 * Created in 2018/8/2 下午5:45                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
/*package*/ class Executor132 extends Executor {

    private final Gson mGson;
    private final ClientPlayer mPlayer;
    private ClientAccountLoginHelp clientAccountLoginHelp;
    private SubscribeModel subscribeModel;
    private long mRefreshTime;
    private Context context;
    private static final String TAG = "Executor132";
    private OnPlayLogicListener mOnPlayLogicListener;

    public Executor132(Context context) {
        this.context = context;
        Log.i(TAG, "Executor132");
        mGson = new Gson();
        subscribeModel = new SubscribeModel(/*context*/);
        mPlayer = new PlayerImpl(context);
        clientAccountLoginHelp = new ClientAccountLoginHelp(mGson);
        if (OpenSDK.getInstance().isActivate()) {
            Log.i(TAG, "Executor132 isActivate");
            clientAccountLoginHelp.init();
        } else {
//            EventBus.getDefault().register(this);
            KradioSDKManager.getInstance().addUsableObserver(() -> {
                if (clientAccountLoginHelp != null) {
                    clientAccountLoginHelp.init();
                }
            });
        }
    }

//    @Subscribe(threadMode = ThreadMode.MAIN)
//    public void initAfterActivate(ActivationEvent event) {
//        Log.i(TAG, "initAfterActivate");
//        if (clientAccountLoginHelp != null) {
//            clientAccountLoginHelp.init();
//        }
//        EventBus.getDefault().unregister(this);
//    }

    @Override
    public void execute(String method, String[] param, IExecuteResult executeResult) throws RemoteException {
        Log.i("Executor132", "execute:" + method);
        if (IsSubscribedCmd.METHOD_NAME.equals(method)) {
            isSubscribed(param, executeResult);
        } else if (GetHistoryCmd.METHOD_NAME.equals(method)) {
            getHistory(param, executeResult);
        } else if (RecommendPgcListCmd.METHOD_NAME.equals(method)) {
            //kradio无该接口
            getRecommendPGCList(param, executeResult);
        } else if (BroadcastRadioDetailCmd.METHOD_NAME.equals(method)) {
            getBroadcastDetail(param, executeResult);
        } else if (BroadcastDateCmd.METHOD_NAME.equals(method)) {
            //kradio无该接口
            //获取传统广播日期列表接口
            getBroadcastDate(executeResult);
        } else if (BroadcastCurrentProgramCmd.METHOD_NAME.equals(method)) {
            getBroadcastCurrentProgram(param, executeResult);
        } else if (BroadcastProgramDetailCmd.METHOD_NAME.equals(method)) {
            getBroadcastProgramDetail(param, executeResult);
        } else if (BroadcastProgramListCmd.METHOD_NAME.equals(method)) {
            getBroadcastProgramList(param, executeResult);
        } else if (CityBroadcastListCmd.METHOD_NAME.equals(method)) {
            //kradio无该接口
            getCityBroadcastList(param, executeResult);
        } else if (GetUserInfoCmd.METHOD_NAME.equals(method)) {
            getUserInfo(param, executeResult);
        } else if (BindTingbanCmd.METHOD_NAME.equals(method)) {
            bindTingban(param, executeResult);
        } else if (UnbindTingbanCmd.METHOD_NAME.equals(method)) {
            unbindTingban(param, executeResult);
        } else if (BindListenerCmd.METHOD_NAME.equals(method)) {
            setBindListener(param, executeResult);
        } else if (IsKaolRunningCmd.METHOD_NAME.equals(method)) {
            if (executeResult != null) {
                executeResult.onResult(String.valueOf(AppDelegate.getInstance().isKaolaRuning()));
            }
        } else if (ContinuePlayCmd.METHOD_NAME.equals(method)) {
            Log.i(TAG, "execute ContinuePlayCmd = " + param);
            boolean canContinuePlay = true;

            if (!ListUtil.isEmpty(param)) {
                canContinuePlay = Boolean.parseBoolean(param[0]);
            }
            boolean finalCanContinuePlay = canContinuePlay;
            if (mOnPlayLogicListener == null) {
                mOnPlayLogicListener = new OnPlayLogicListener() {
                    @Override
                    public boolean onPlayLogicDispose() {
                        return finalCanContinuePlay;
                    }
                };
                PlayerCustomizeManager.getInstance().setPlayLogicListener(mOnPlayLogicListener);
            }
            if (!canContinuePlay && PlayerManagerHelper.getInstance().isPlaying()) {
                PlayerManagerHelper.getInstance().switchPlayerStatus(true);
            }
        } else if (RequestAudioFocusCmd.METHOD.equals(method)) {
            boolean isInitSuccess = PlayerManager.getInstance().isPlayerInitSuccess();
            if (isInitSuccess) {
                PlayerManager.getInstance().requestAudioFocus();
            } else {
                mPlayer.setNeedRequestAudioFocusOnPlayerInit(true);
            }
        }else if (GetVersionCodeCmd.METHOD_NAME.equals(method)) {
            if (executeResult != null) {
                try {
                    PackageManager manager = context.getPackageManager();
                    PackageInfo info = manager.getPackageInfo(context.getPackageName(), 0);
                    String versionCode = info.versionCode+"";
                    executeResult.onResult(versionCode);
                }catch (PackageManager.NameNotFoundException e){
                    e.printStackTrace();
                }
            }
        } else {
            if (executeResult != null) {
                executeResult.onResult(null);
            }
        }
    }

    /**
     * 子类可重写该方法
     *
     * @param param
     * @param iExecuteResult
     */
    protected void isSubscribed(String[] param, final IExecuteResult iExecuteResult) {
        Log.i(TAG, "isSubscribed execute");
        ClientPlayer.PlayParam playParam = mPlayer.getPlayParam();

        if (playParam.resType == ResType.ALBUM_TYPE
                || playParam.resType == ResType.RADIO_TYPE
                || playParam.resType == ResType.BROADCAST_TYPE
                || playParam.resType == ResType.AUDIO_TYPE) {

            long id = playParam.id;

            subscribeModel.isSubscribed(String.valueOf(id), new ResultCallback() {
                @Override
                public void onResult(boolean result, int code) {
                    IsSubscribedCmd.Result rst = new IsSubscribedCmd.Result();
                    rst.setRst(result);
                    rst.setAlbumId(id);
                    String json = mGson.toJson(rst);
                    try {
                        Log.i(TAG, "onResult json: " + json);
                        iExecuteResult.onResult(json);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

                @Override
                public void onFailure(ErrorInfo errorInfo) {
                    IsSubscribedCmd.Result rst = new IsSubscribedCmd.Result();
                    rst.setRst(false);
                    rst.setAlbumId(id);
                    String json = mGson.toJson(rst);
                    try {
                        iExecuteResult.onResult(json);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            });
        }

    }


    /**
     * 子类可重写该方法
     *
     * @param param
     * @param iExecuteResult
     */
    protected void getHistory(String[] param, final IExecuteResult iExecuteResult) {
        HistoryManager.getInstance().getHistoryList(new HttpCallback<List<HistoryItem>>() {
            @Override
            public void onSuccess(List<HistoryItem> historyList) {
                List<com.kaolafm.sdk.core.ex.bean.HistoryItem> historyItemArrayList = new ArrayList<>();
                if (!ListUtil.isEmpty(historyList)) {
                    for (HistoryItem historyItem : historyList) {
                        com.kaolafm.sdk.core.ex.bean.HistoryItem hi = DataCorverter.toClientHistoryItem(historyItem);
                        if (hi != null) {
                            historyItemArrayList.add(hi);
                        }
                    }
                }
                GetHistoryCmd.Result getHisRst = new GetHistoryCmd.Result();
                getHisRst.setHistories(historyItemArrayList);
                getHisRst.setErrorCode(Command.CODE_SUCCESS);

                try {
                    iExecuteResult.onResult(mGson.toJson(getHisRst));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onError(ApiException e) {

            }
        });

    }


    protected void getBroadcastDetail(String[] param, final IExecuteResult iExecuteResult) {
        if (param == null || param.length < 1) {
            BroadcastRadioDetailCmd.Result result = new BroadcastRadioDetailCmd.Result();
            result.setErrorCode(ErrorCode.Failure);
            try {
                iExecuteResult.onResult(mGson.toJson(result));
            } catch (Exception e) {
                e.printStackTrace();
            }
            return;
        }
        long id = 0;
        if (!TextUtils.isEmpty(param[0])) {
            id = Long.valueOf(param[0]);
        }

        new BroadcastRequest().getBroadcastDetails(id, new HttpCallback<BroadcastDetails>() {
            @Override
            public void onSuccess(BroadcastDetails broadcastDetails) {
                try {
                    if (broadcastDetails != null) {
                        BroadcastRadioDetailCmd.Result result = new BroadcastRadioDetailCmd.Result();
                        result.setBroadcastRadioDetailData(toClientBroadcastRadioDetailData(broadcastDetails));
                        result.setErrorCode(Command.CODE_SUCCESS);
                        iExecuteResult.onResult(mGson.toJson(result));
                    } else {
                        BroadcastRadioDetailCmd.Result result = new BroadcastRadioDetailCmd.Result();
                        result.setErrorCode(-1);
                        iExecuteResult.onResult(mGson.toJson(result));
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onError(ApiException e) {
                BroadcastRadioDetailCmd.Result result = new BroadcastRadioDetailCmd.Result();
                result.setErrorCode(ErrorCode.Failure);
                try {
                    iExecuteResult.onResult(mGson.toJson(result));
                } catch (Exception ee) {
                    ee.printStackTrace();
                }
            }
        });

    }


    protected void getBroadcastCurrentProgram(String[] param, final IExecuteResult iExecuteResult) {
        if (param == null || param.length < 1) {
            BroadcastCurrentProgramCmd.Result result = new BroadcastCurrentProgramCmd.Result();
            result.setErrorCode(-1);
            try {
                iExecuteResult.onResult(mGson.toJson(result));
            } catch (Exception e) {
                e.printStackTrace();
            }
            return;
        }

        long id = 0;
        if (!TextUtils.isEmpty(param[0])) {
            id = Long.valueOf(param[0]);
        }

        new BroadcastRequest().getBroadcastCurrentProgramDetails(id, new HttpCallback<ProgramDetails>() {
            @Override
            public void onSuccess(ProgramDetails programDetails) {
                try {
                    if (programDetails != null) {
                        BroadcastCurrentProgramCmd.Result result = new BroadcastCurrentProgramCmd.Result();
                        result.setBroadcastRadioPlayItem(toClientBroadcastRadioPlayItem(programDetails));
                        result.setErrorCode(Command.CODE_SUCCESS);
                        iExecuteResult.onResult(mGson.toJson(result));
                    } else {
                        BroadcastCurrentProgramCmd.Result result = new BroadcastCurrentProgramCmd.Result();
                        result.setErrorCode(ErrorCode.Failure);
                        iExecuteResult.onResult(mGson.toJson(result));
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onError(ApiException e) {
                BroadcastCurrentProgramCmd.Result result = new BroadcastCurrentProgramCmd.Result();
                result.setErrorCode(-1);
                try {
                    iExecuteResult.onResult(mGson.toJson(result));
                } catch (Exception ee) {
                    ee.printStackTrace();
                }
            }
        });
    }


    protected void getBroadcastProgramDetail(String[] param, final IExecuteResult iExecuteResult) {
        if (param == null || param.length < 1) {
            BroadcastProgramDetailCmd.Result result = new BroadcastProgramDetailCmd.Result();
            result.setErrorCode(-1);
            try {
                iExecuteResult.onResult(mGson.toJson(result));
            } catch (Exception e) {
                e.printStackTrace();
            }
            return;
        }

        long id = 0;
        try {
            id = Long.valueOf(param[0]);
        } catch (NumberFormatException e) {
            BroadcastProgramDetailCmd.Result result = new BroadcastProgramDetailCmd.Result();
            result.setErrorCode(-1);
            try {
                iExecuteResult.onResult(mGson.toJson(result));
            } catch (Exception ee) {
                ee.printStackTrace();
            }
            return;
        }


        new BroadcastRequest().getBroadcastDetails(id, new HttpCallback<BroadcastDetails>() {
            @Override
            public void onSuccess(BroadcastDetails broadcastDetails) {
                try {
                    if (broadcastDetails != null) {
                        BroadcastProgramDetailCmd.Result result = new BroadcastProgramDetailCmd.Result();
                        result.setBroadcastRadioPlayItem(toClientBroadcastRadioPlayItem(broadcastDetails));
                        result.setErrorCode(Command.CODE_SUCCESS);
                        iExecuteResult.onResult(mGson.toJson(result));
                    } else {
                        BroadcastProgramDetailCmd.Result result = new BroadcastProgramDetailCmd.Result();
                        result.setErrorCode(ErrorCode.Failure);
                        iExecuteResult.onResult(mGson.toJson(result));
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onError(ApiException e) {
                BroadcastProgramDetailCmd.Result result = new BroadcastProgramDetailCmd.Result();
                result.setErrorCode(-1);
                try {
                    iExecuteResult.onResult(mGson.toJson(result));
                } catch (Exception ee) {
                    ee.printStackTrace();
                }
            }
        });

    }


    protected void getBroadcastProgramList(String[] param, final IExecuteResult iExecuteResult) {
        if (param == null || param.length < 2) {
            BroadcastProgramListCmd.Result result = new BroadcastProgramListCmd.Result();
            result.setErrorCode(-1);
            try {
                iExecuteResult.onResult(mGson.toJson(result));
            } catch (Exception e) {
                e.printStackTrace();
            }
            return;
        }

        long id = 0;
        if (!TextUtils.isEmpty(param[0])) {
            id = Long.valueOf(param[0]);
        }

        new BroadcastRequest().getBroadcastProgramList(id, param[1], new HttpCallback<List<ProgramDetails>>() {
            @Override
            public void onSuccess(List<ProgramDetails> programDetails) {
                try {
                    if (programDetails != null) {
                        BroadcastProgramListCmd.Result result = new BroadcastProgramListCmd.Result();
                        result.setBroadcastProgramListData(toClientBroadcastRadioPlayItemList((List<ProgramDetails>) programDetails));
                        result.setErrorCode(ErrorCode.Success);
                        iExecuteResult.onResult(mGson.toJson(result));
                    } else {
                        BroadcastProgramListCmd.Result result = new BroadcastProgramListCmd.Result();
                        result.setErrorCode(ErrorCode.Failure);
                        iExecuteResult.onResult(mGson.toJson(result));
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onError(ApiException e) {
                BroadcastProgramListCmd.Result result = new BroadcastProgramListCmd.Result();
                result.setErrorCode(-1);
                try {
                    iExecuteResult.onResult(mGson.toJson(result));
                } catch (Exception ee) {
                    ee.printStackTrace();
                }
            }
        });

    }


    /***************************************************************************************************************/


    /***************************************************************************************************************/

    private BroadcastRadioDetailData toClientBroadcastRadioDetailData(BroadcastDetails broadcastDetails) {
        BroadcastRadioDetailData brdd = new BroadcastRadioDetailData();
        brdd.setBroadcastId(broadcastDetails.getBroadcastId());
        brdd.setClassifyId(broadcastDetails.getClassifyId());
        brdd.setClassifyName(broadcastDetails.getClassifyName());
        brdd.setFreq(broadcastDetails.getFreq());
        brdd.setIcon(broadcastDetails.getIcon());
        brdd.setImg(broadcastDetails.getImg());
        brdd.setIsSubscribe(broadcastDetails.getIsSubscribe());
        brdd.setName(broadcastDetails.getName());
        brdd.setLikedNum(broadcastDetails.getLikedNum());
        brdd.setOnLineNum(broadcastDetails.getOnLineNum());
        brdd.setPlayUrl(broadcastDetails.getPlayUrl());
        brdd.setRoomId(broadcastDetails.getRoomId());
        brdd.setStatus(broadcastDetails.getStatus());
        return brdd;
    }


    private BroadcastRadioPlayItem toClientBroadcastRadioPlayItem(ProgramDetails programDetails) {
        BroadcastRadioPlayItem brpi = new BroadcastRadioPlayItem();
        brpi.setBackLiveUrl(programDetails.getBackLiveUrl());
        brpi.setBeginTime(programDetails.getBeginTime());
        brpi.setBroadcastDesc(programDetails.getBroadcastDesc());
        brpi.setBackLiveUrl(programDetails.getBackLiveUrl());
        brpi.setBroadcastId(programDetails.getBroadcastId());
        brpi.setBroadcastImg(programDetails.getBroadcastImg());
        brpi.setBroadcastRadioName(programDetails.getBroadcastName());
        brpi.setBroadcastName(programDetails.getBroadcastName());
        brpi.setComperes(programDetails.getComperes());
        brpi.setDesc(programDetails.getDesc());
        brpi.setEndTime(programDetails.getEndTime());
        brpi.setFinishTime(programDetails.getFinishTime());
        brpi.setIcon(programDetails.getIcon());
        brpi.setIsSubscribe(programDetails.getIsSubscribe());
        brpi.setNextProgramId(programDetails.getNextProgramId());
        brpi.setPlayUrl(programDetails.getPlayUrl());
        brpi.setPreProgramId(programDetails.getPreProgramId());
        brpi.setProgramId(programDetails.getProgramId());
        brpi.setStartTime(programDetails.getStartTime());
        brpi.setStatus(programDetails.getStatus());
        brpi.setTitle(programDetails.getTitle());
        return brpi;
    }


    private BroadcastRadioPlayItem toClientBroadcastRadioPlayItem(BroadcastDetails broadcastDetails) {
        BroadcastRadioPlayItem brpi = new BroadcastRadioPlayItem();
//        brpi.setBackLiveUrl(broadcastDetails.getBackLiveUrl());
//        brpi.setBeginTime(broadcastDetails.getBeginTime());
//        brpi.setBroadcastDesc(broadcastDetails.getb());
//        brpi.setBackLiveUrl(broadcastDetails.getBackLiveUrl());
        brpi.setBroadcastId(broadcastDetails.getBroadcastId());
        brpi.setBroadcastImg(broadcastDetails.getImg());
        brpi.setBroadcastRadioName(broadcastDetails.getName());
        brpi.setBroadcastName(broadcastDetails.getName());
//        brpi.setComperes(broadcastDetails.getc());
        brpi.setClassifyId(broadcastDetails.getClassifyId());
        brpi.setClassifyName(broadcastDetails.getClassifyName());
//        brpi.setDesc(broadcastDetails.get());
//        brpi.setEndTime(broadcastDetails.getEndTime());
//        brpi.setFinishTime(broadcastDetails.getFinishTime());
        brpi.setIcon(broadcastDetails.getIcon());
        brpi.setIsSubscribe(broadcastDetails.getIsSubscribe());
//        brpi.setNextProgramId(broadcastDetails.getNextProgramId());
        brpi.setPlayUrl(broadcastDetails.getPlayUrl());
//        brpi.setPreProgramId(broadcastDetails.get());
        //brpi.setProgramId(broadcastDetails.getBroadcastId());
        brpi.setStatus(broadcastDetails.getStatus());
        brpi.setTitle(broadcastDetails.getName());
        return brpi;
    }


    private List<BroadcastRadioPlayItem> toClientBroadcastRadioPlayItemList(List<ProgramDetails> list) {
        ArrayList<BroadcastRadioPlayItem> arrayList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            ProgramDetails broadcastRadioPlayItem = list.get(i);
            arrayList.add(toClientBroadcastRadioPlayItem(broadcastRadioPlayItem));
        }
        return arrayList;
    }

    private void getRecommendPGCList(String[] param, IExecuteResult executeResult) {
        //kradio无该接口
        RecommendPgcListCmd.Result rst = new RecommendPgcListCmd.Result();
        rst.setErrorCode(ErrorCode.ClientNotSupport);
        String rstJson = mGson.toJson(rst);
        try {
            executeResult.onResult(rstJson);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void getBroadcastDate(IExecuteResult executeResult) {
        //kradio无该接口
        BroadcastDateCmd.Result rst = new BroadcastDateCmd.Result();
        rst.setErrorCode(ErrorCode.ClientNotSupport);
        String rstJson = mGson.toJson(rst);
        try {
            executeResult.onResult(rstJson);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void getCityBroadcastList(String[] param, IExecuteResult executeResult) {
        //kradio无该接口
        CityBroadcastListCmd.Result rst = new CityBroadcastListCmd.Result();
        rst.setErrorCode(ErrorCode.ClientNotSupport);
        String rstJson = mGson.toJson(rst);
        try {
            executeResult.onResult(rstJson);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /***************************************************************************************************************/
    @Override
    public String execute(String method, String[] param) {
        if (PageCmd.METHOD.equals(method)) {
            return gotoPage(param);
        }
        return null;
    }

    private String gotoPage(String[] param) {
        if (param != null && param.length > 0) {
            String pageStr = param[0];
            if (Page.LOGIN.name().equals(pageStr)) {
                startActivity(Constants.START_PAGE_LOGIN);
            }

            return "success";
        }
        return "error:" + param;
    }

    private void startActivity(int page) {
        Intent intent = context.getPackageManager().getLaunchIntentForPackage(context.getPackageName());
        if (intent != null) {
            Log.i(TAG, "startActivity , page:" + page);
            intent.putExtra(START_PAGE, page);
            context.startActivity(intent);
        }
    }


    private void getUserInfo(String[] param, IExecuteResult result) {
        Log.i(TAG, "getUserInfo execute");
        GetUserInfoCmd.Result rst = new GetUserInfoCmd.Result();
        rst.setCode(ErrorCode.Success);
        rst.setUserInfo(clientAccountLoginHelp.getUserInfoFromNet());
        String s = mGson.toJson(rst);
        try {
            result.onResult(s);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void bindTingban(String[] param, IExecuteResult executeResult) {
        Logging.v("绑定听伴， param=%s", executeResult);
        BindTingbanCmd.Result rst = new BindTingbanCmd.Result();
        if (param != null && param.length < 3) {
            rst.setCode(ErrorCode.ParamError);
            String s = mGson.toJson(rst);
            try {
                executeResult.onResult(s);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            String userId = param[0];
            String token = param[1];
            String refreshToken = param[2];

            KRadioAccountBindInter kRadioAccountBindInter = ClazzImplUtil.getInter("KRadioAccountBindImpl");
            if (kRadioAccountBindInter != null) {
                Log.i(TAG, "bindTingban userId = " + userId + " token = " + token + " refreshToken = " + refreshToken);
                kRadioAccountBindInter.bindDeviceId(userId, token, refreshToken, new HttpCallback<Object>() {
                    @Override
                    public void onSuccess(Object obj) {
                        KaolaAccessToken kaolaAccessToken = AccessTokenManager.getInstance().getKaolaAccessToken();
                        getUserInfoFromNet(executeResult, rst, kaolaAccessToken.getUserId(), kaolaAccessToken.getAccessToken(), kaolaAccessToken.getRefreshToken());
                    }

                    @Override
                    public void onError(ApiException e) {
                        rst.setCode(ErrorCode.Failure);
                        String s = mGson.toJson(rst);
                        try {
                            executeResult.onResult(s);
                        } catch (Exception ee) {
                            e.printStackTrace();
                        }
                    }
                });
            } else {

//            getUserInfoFromNet(executeResult, rst, userId, token, refreshToken);

                //等到bind接口上线再打开
                //BindUserRequest request = new BindUserRequest();
//            BindUserRequestData bindUserRequestData = new BindUserRequestData();
//
//            bindUserRequestData.setAccess_token(token);
//            bindUserRequestData.setOpen_uid(userId);

//            String json = new Gson().toJson(bindUserRequestData);
//            request.bindDevice(new HttpCallback<Boolean>() {
//                @Override
//                public void onSuccess(Boolean aBoolean) {
//                    getUserInfoFromNet(executeResult, rst, userId, token, refreshToken);
//                }
//
//                @Override
//                public void onError(ApiException e) {
//                    rst.setCode(ErrorCode.Failure);
//                    String s = mGson.toJson(rst);
//                    try {
//                        executeResult.onResult(s);
//                    } catch (Exception ee) {
//                        e.printStackTrace();
//                    }
//                }
//            });
                getUserInfoFromNet(executeResult, rst, userId, token, refreshToken);
            }
        }
    }

    private void getUserInfoFromNet(IExecuteResult executeResult, BindTingbanCmd.Result rst, String userId, String token, String refreshToken) {
        KaolaAccessToken accessToken = AccessTokenManager.getInstance().getKaolaAccessToken();
        accessToken.setUserId(userId);
        accessToken.setAccessToken(token);
        accessToken.setRefreshToken(refreshToken);
        AccessTokenManager.getInstance().setCurrentAccessToken(accessToken);

        LoginRequest request = new LoginRequest();
        request.getUserInfo(new HttpCallback<com.kaolafm.opensdk.api.login.model.UserInfo>() {
            @Override
            public void onSuccess(com.kaolafm.opensdk.api.login.model.UserInfo ui) {
                Logger.i("k.login.exe132", "onSuccess: ");
                ComponentClient.obtainBuilder(UserComponentConst.NAME)
                        .setActionName(SettingPropertiesProcessorConst.SET_PROPERTIES)
                        .addParam(SettingPropertiesProcessorConst.USER_NICKNAME, ui.getNickName())
                        .addParam(SettingPropertiesProcessorConst.USER_FAVICON, ui.getAvatar())
                        .addParam(SettingPropertiesProcessorConst.LOCAL_LOGIN, true)
                        .build().call();

                EventBus.getDefault().post(new LogoutBindEvent(LogoutBindEvent.LOGIN));
                UserInfoManager.getInstance().localLogin();
                rst.setCode(ErrorCode.Success);
                rst.setUserInfo(clientAccountLoginHelp.getUserInfoFromNet());
                String s = mGson.toJson(rst);
                try {
                    executeResult.onResult(s);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onError(ApiException e) {
                rst.setCode(ErrorCode.Failure);
                String s = mGson.toJson(rst);
                try {
                    executeResult.onResult(s);
                } catch (Exception ee) {
                    e.printStackTrace();
                }
            }
        });
    }


    private void unbindTingban(String[] param, IExecuteResult executeResult) {
        UnbindTingbanCmd.Result rst = new UnbindTingbanCmd.Result();
        Logging.v("解绑听伴， param=%s, Result=%s", param, rst);
        AccountLoginModel accountLoginModel = new AccountLoginModel();
        accountLoginModel.addAccountCallBack(new AccountLoginModel.AccountCallBack() {
            @Override
            public void onQRCode(QRCodeInfo qrCodeInfo) {
                accountLoginModel.removeAccountCallBack(this);
            }

            @Override
            public void onBindSuccess(com.kaolafm.opensdk.api.login.model.UserInfo userInfo) {
                accountLoginModel.removeAccountCallBack(this);
            }

            @Override
            public void onUnbindSuccess(boolean success) {
                accountLoginModel.removeAccountCallBack(this);
                rst.setCode(success ? ErrorCode.Success : ErrorCode.Failure);
                String s = mGson.toJson(rst);
                try {
                    executeResult.onResult(s);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onQRCodeExpire() {
                accountLoginModel.removeAccountCallBack(this);
            }

            @Override
            public void onQRCodeScanned(String avatar, String name) {
                accountLoginModel.removeAccountCallBack(this);
            }
        });
        accountLoginModel.unbindKradio();
    }

    private void setBindListener(String[] param, IExecuteResult executeResult) {
        clientAccountLoginHelp.setBindResult(executeResult);
    }

}
