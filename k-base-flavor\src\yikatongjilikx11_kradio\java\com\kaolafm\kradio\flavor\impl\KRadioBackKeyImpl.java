package com.kaolafm.kradio.flavor.impl;

import androidx.appcompat.app.AppCompatActivity;
import android.view.KeyEvent;

import com.kaolafm.kradio.lib.base.flavor.KRadioBackKeyInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioMediaKeyEventInter;

/**
 * @Package: com.kaolafm.kradio.flavor.impl
 * @Description:
 * @Author: Maclay
 * @Date: 15:53
 */
public class KRadioBackKeyImpl implements KRadioBackKeyInter {
    @Override
    public boolean onBackPressed(Object... args) {
        AppCompatActivity activity = (AppCompatActivity) args[0];
        activity.moveTaskToBack(true);
        return true;
    }

    @Override
    public boolean appExit(Object... args) {
        return false;
    }

    @Override
    public void dealKillYunTingReceiver(Object... args) {

    }
}
