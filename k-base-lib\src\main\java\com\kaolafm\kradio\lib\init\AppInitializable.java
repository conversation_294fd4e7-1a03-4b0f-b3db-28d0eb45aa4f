package com.kaolafm.kradio.lib.init;

import android.app.Application;

/**
 * 实现该接口并添加注解{@link AppInit}的类会被自动添加到管理中{@link AppInitManager}根据其配置进行初始化相关接口的回调。
 * <AUTHOR> Yan
 * @date 2019-09-10
 */
public interface AppInitializable {

    /**
     * 会在Application的onCreate()方法中同步回调。
     * @param application
     */
    void onCreate(Application application);

    /**
     * 在Application的onCreate()方法中开启线程异步回调，该方法只有在{@link AppInit#isAsync()}返回true的时候才会执行。
     * @param application
     */
    void asyncCreate(Application application);

    /**
     *
     * 根据官方注释说明，该方法只会在模拟器中应用退出时才回调，在真机中不会回调。
     */
    void onTerminate();

    /**
     * 内存不足,调用时机大概等同于{@link #onTrimMemory(int)}中的TRIM_MEMORY_COMPLETE．
     */
    void onLowMemory();

    /**
     * 内存的使用情况，在这里可以处理内存释放
     *
     * @param level 当你的app在后台时：<br/>
     *              <p>
     *              TRIM_MEMORY_COMPLETE ：当前进程在LRU列表的尾部，如果没有足够的内存，它将很快被杀死。这时候你应该释放任何不影响app运行的资源。<br/>
     *              <p>
     *              TRIM_MEMORY_MODERATE ：当前进程在LRU列表的中部，如果系统进一步需要内存，你的进程可能会被杀死。<br/>
     *              <p>
     *              TRIM_MEMORY_BACKGROUND：当前进程在LRU列表的头部，虽然你的进程不会被高优杀死，但是系统已经开始准备杀死LRU列表中的其他进程了，
     *              <p>
     *              因此你应该尽量的释放能够快速回复的资源，以保证当用户返回你的app时可以快速恢复。<br/>
     *              <p>
     *              当你的app的可见性改变时：<br/>
     *              <p>
     *              TRIM_MEMORY_UI_HIDDEN：当前进程的界面已经不可见，这时是释放UI相关的资源的好时机。<br/>
     *              <p>
     *              当你的app正在运行时：<br/>
     *              <p>
     *              TRIM_MEMORY_RUNNING_CRITICAL：虽然你的进程不会被杀死，但是系统已经开始准备杀死其他的后台进程了，这时候你应该释放无用资源以防止性能下降。<br/>
     *              <p>
     *              下一个阶段就是调用"onLowMemory()"来报告开始杀死后台进程了，特别是状况已经开始影响到用户。<br/>
     *              <p>
     *              TRIM_MEMORY_RUNNING_LOW：虽然你的进程不会被杀死，但是系统已经开始准备杀死其他的后台进程了，你应该释放不必要的资源来提供系统性能，否则会
     *              <p>
     *              影响用户体验。<br/>
     *              <p>
     *              TRIM_MEMORY_RUNNING_MODERATE：系统已经进入了低内存的状态，你的进程正在运行但是不会被杀死。<br/>
     */
    void onTrimMemory(int level);
}
