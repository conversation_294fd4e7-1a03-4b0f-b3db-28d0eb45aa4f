package com.kaolafm.opensdk.api.ex;

import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.HostConstant;

import io.reactivex.Single;
import okhttp3.RequestBody;
import retrofit2.http.Body;
import retrofit2.http.Headers;
import retrofit2.http.POST;

/*package*/ interface EcarxService {

    @Headers({HostConstant.DOMAIN_HEADER_OPEN_KAOLA,HostConstant.DOMAIN_HEADER_HTTPS_PROTOCOL})
    @POST("/v2/security/account/bind/deviceId")
    Single<BaseResult<DeviceInfo>> bindDeviceId(@Body RequestBody params);



}