<?xml version="1.0" encoding="utf-8"?>
<com.kaolafm.kradio.lib.widget.ratio.RatioConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:canScale="false"
    app:wh_ratio="0.887:1"
    tools:background="@drawable/bg_home">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">


            <include
                android:layout_weight="1"
                android:id="@+id/layout_1_1_top_lift"
                layout="@layout/component_mini_card_layout"
                android:layout_width="match_parent"
                android:layout_height="0dp" />


            <include
                android:layout_marginTop="@dimen/m30"
                android:layout_weight="1"
                android:id="@+id/layout_1_1_top_right"
                layout="@layout/component_mini_card_layout"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_below="@+id/layout_1_1_top_lift"
               />
    </LinearLayout>
</com.kaolafm.kradio.lib.widget.ratio.RatioConstraintLayout>