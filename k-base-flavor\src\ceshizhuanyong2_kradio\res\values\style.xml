<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="recyclerview_bar_style">
        <!--滚动条宽度-->
        <item name="android:scrollbarSize">@dimen/m4</item>
        <!--滚动条方向-->
        <item name="android:scrollbars">vertical</item>
        <!--滚动条是否滑动-->
        <item name="android:fastScrollEnabled">true</item>
        <!--滚动条不消失-->
        <item name="android:fadeScrollbars">false</item>
        <!--        滚动条在recyclerview的padding外部显示-->
        <item name="android:scrollbarStyle">outsideOverlay</item>
        <item name="android:scrollbarAlwaysDrawVerticalTrack">true</item>
        <!--滚动条滑动块颜色-->
        <item name="android:scrollbarThumbVertical">@drawable/recycler_bar</item>
        <!--滚动条默认颜色-->
        <item name="android:scrollbarTrackVertical">@drawable/recycler_bar_bg</item>
    </style>
</resources>