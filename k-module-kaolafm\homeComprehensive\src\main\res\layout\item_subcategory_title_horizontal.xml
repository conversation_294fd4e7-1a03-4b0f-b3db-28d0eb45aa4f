<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="@dimen/x60"
    android:layout_height="match_parent"
    android:background="@drawable/category_item_title_bg"
    android:padding="@dimen/m15">

    <TextView
        android:id="@+id/item_subcategory_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:clickable="false"
        android:gravity="center"
        android:textColor="@color/category_item_title_text_color"
        android:textSize="@dimen/all_ctg_item_content_size"
        tool:text="热门" />
</FrameLayout>