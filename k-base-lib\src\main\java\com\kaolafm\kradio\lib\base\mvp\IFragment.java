package com.kaolafm.kradio.lib.base.mvp;

import android.view.View;

/**
 * Fragment的接口，只是为了规范方法。
 * <AUTHOR>
 * @date 2018/4/13
 */

public interface IFragment {

    /**
     * 是否使用EventBus,默认不使用
     * @return
     */
    boolean useEventBus();

    /**
     * 做一些view相关的操作
     * @param view
     */
    void initView(View view);

    /**
     * 初始化必要数据, 该方法执行早于view和present的初始化。
     * 一般用来获取Bundle数据。
     */
    void initArgs();
}
