package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import com.kaolafm.base.internal.BaseObtainDeviceId;
import com.kaolafm.kradio.lib.base.AppDelegate;

/**
 * @Description:
 * @Author: Maclay
 * @Date: 2021/12/30 11:11 上午
 */
public class KRadioCacheObtainDeviceId extends BaseObtainDeviceId {
    private static final String UDID_PREFERENCE_NAME = "udid_information_pf";

    private static final String UDID_VALUE = "udid_value";
    private static final String TAG = "BaseObtainDeviceId.KRadioCacheObtainDeviceId";


    @Override
    public String createUUID(Context context) {
        String uuid =  getSharedPreferences(context).getString(UDID_VALUE, "");
        Log.d(TAG,"createUUID uuid = "+uuid);
        return uuid;
    }

    /**
     * 获取SharedPreferences
     *
     * @param context
     * @return
     */
    private SharedPreferences getSharedPreferences(Context context) {
        SharedPreferences sharedPreferences = context.getSharedPreferences(UDID_PREFERENCE_NAME, Context.MODE_PRIVATE);
        return sharedPreferences;
    }

    @Override
    public void saveUUID(String uuid, int index) {
        super.saveUUID(uuid, index);
        Log.d(TAG,"saveUUID uuid = "+uuid);
        getSharedPreferences(AppDelegate.getInstance().getContext()).edit().putString(UDID_VALUE,uuid).commit();
    }
}