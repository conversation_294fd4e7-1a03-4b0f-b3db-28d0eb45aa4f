package com.kaolafm.kradio.online.mine.order;

import android.util.Log;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.mvp.BaseModel;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.purchase.PurchaseRequest;
import com.kaolafm.opensdk.api.purchase.model.Order;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.http.error.ErrorCode;

import java.util.ArrayList;
import java.util.List;

/**
 *
 */
public class OrderModel extends BaseModel {
    private static final String TAG = "k.order.model";

    HttpCallback<BasePageResult<List<Order>>> callback;

    PurchaseRequest purchaseRequest;

    public OrderModel() {
        purchaseRequest = new PurchaseRequest().setTag(TAG);
    }

    @Override
    public void destroy() {

    }

    public void setCallBack(HttpCallback<BasePageResult<List<Order>>> callback) {
        this.callback = callback;
    }

    public void getOrderList(int pageNum) {
        Log.d(TAG, "getOrderList " + pageNum);
        if (callback == null) {
            return;
        }
        if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
            callback.onError(new ApiException(ErrorCode.HTTP_NET_NOT_AVAILABLE, ""));
            return;
        }
        purchaseRequest.getOrderList(pageNum, 20, callback);
//        callback.onSuccess(getTest2());
    }

//    interface IGetOrderListCallBack {
//        void onSuccess(List<OrderItemBean> list,boolean hasNext);
//
//        void onFail();
//    }
//
//    private List<OrderItemBean> getTest() {
//        List<OrderItemBean> result = new ArrayList<>();
//        for (int i = 0; i < 19; i++) {
//            OrderItemBean orderItemBean = new OrderItemBean();
//            result.add(orderItemBean);
//            orderItemBean.setCreateTime("2020-06-30 12:12");
//            orderItemBean.setFinishTime("2020-06-30 12:12");
//            orderItemBean.setType(4);
//            orderItemBean.setPayWay(1);
//            orderItemBean.setPlayClient(1);
//            orderItemBean.setName("VIP会员");
//            orderItemBean.setStatus(1);
//        }
//        return result;
//    }


    private BasePageResult<List<Order>> getTest2() {
        BasePageResult<List<Order>> basePageResult = new BasePageResult();
        List<Order> result = new ArrayList<>();
        for (int i = 0; i < 19; i++) {
            Order orderItemBean = new Order();
            result.add(orderItemBean);
            orderItemBean.setCreateTime("2020-06-30 12:12");
            orderItemBean.setBillNo("11111111111111" + i);
            orderItemBean.setPayType(i % 3 + 1);
            orderItemBean.setPlantform(i % 2 + 1);
            orderItemBean.setProductType(i % 3 + 1);
            orderItemBean.setStatus(i % 3);
            orderItemBean.setTitle("VIP会员 " + orderItemBean.getProductType());
        }
        basePageResult.setDataList(result);
        basePageResult.setHaveNext(1);
        return basePageResult;
    }


}
