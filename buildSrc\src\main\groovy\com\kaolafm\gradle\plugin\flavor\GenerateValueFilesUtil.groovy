package com.kaolafm.gradle.plugin.flavor

class GenerateValueFilesUtil {
    private static String WTemplate = "<dimen name=\"x{0}\">{1}px</dimen>\n"

    private static String HTemplate = "<dimen name=\"y{0}\">{1}px</dimen>\n"

    private static String SizeTemplate = "<dimen name=\"n{0}\">{1}px</dimen>\n"

    private static String NearestSizeTemplate = "<dimen name=\"m{0}\">{1}px</dimen>\n"

    private static int baseW = 1920

    private static int baseH = 720

    private static int layoutType = 0

    /**
     * 更新基准
     * @param layoutType
     * @return
     */
    def static updateBaseWH(int layoutType) {
        if (this.layoutType != layoutType) {
            this.layoutType = layoutType
            switch (this.layoutType) {
                case 1:
                    baseW = 1280
                    baseH = 720
                    break
                default:
                    baseW = 1920
                    baseH = 720
                    break
            }
        }
    }

    def static generateDensity(String projectPath, List<String> densities) {
        int maxW = baseW
        int maxH = baseH
        if (densities != null) {
            densities.each {
                density ->
                    String[] wh = density.split("x")
                    int w = Integer.parseInt(wh[0])
                    int h = Integer.parseInt(wh[1])
                    //这里判断是为了竖屏适配
                    if (w > h) {
                        maxW = Math.max(maxW, w)
                        maxH = Math.max(maxH, h)
                    } else {
                        maxW = Math.max(maxW, h)
                        maxH = Math.max(maxH, w)
                    }
            }
        }

        if (projectPath != null && projectPath.trim().length() > 0) {
            String path = projectPath + "/src/main/res/values"
            File values = new File(path)
            if (!values.exists()) {
                values.mkdirs()
            }
            File layxFile = new File(path, "lay_x.xml")
            File layyFile = new File(path, "lay_y.xml")
            File laysizeFile = new File(path, "lay_size.xml")
            File layNearestFile = new File(path, "lay_size_nearest.xml")
            if (!layxFile.exists() || !layyFile.exists() || !laysizeFile.exists() || !layNearestFile.exists()) {
                //这里判断是为了竖屏适配
                if (maxW > maxH) {
                    generateXmlFile(path, maxW, maxH, maxW, maxH)
                } else {
                    generateXmlFile(path, maxH, maxW, maxH, maxW)
                }
            }
        }
    }

    def static generateDensity(String projectPath, String channel, List<String> densities) {
        if (densities != null) {
            densities.each {
                density ->
                    if (projectPath != null && projectPath.trim().length() > 0) {
                        String path = projectPath + "/src/" + channel + "/res/values-" + density
                        File values = new File(path)
                        if (!values.exists()) {
                            values.mkdirs()
                            String[] wh = density.split("x")
                            int w = Integer.parseInt(wh[0])
                            int h = Integer.parseInt(wh[1])
                            //这里判断是为了竖屏适配
                            if (w > h) {
                                generateXmlFile(path, w, h)
                            } else {
                                generateXmlFile(path, h, w)
                            }

                        }
                    }
            }
        }
    }

    private static void generateXmlFile(String path, Integer w, Integer h) {
        generateXmlFile(path, w, h, baseW, baseH)
    }

    private static void generateXmlFile(String path, Integer w, Integer h, Integer baseW, Integer baseH) {
        System.out.println("generateXmlFile>>>path=" + path + " , w=" + w + " ,h=" + h + " , baseW=" + baseW + " ,baseH=" + baseH)
        float cellw = w * 1.0f / baseW
        StringBuffer sbForWidth = new StringBuffer()
        sbForWidth.append("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n")
        sbForWidth.append("<resources>")
        System.out.println("width : " + w + "," + baseW + "," + cellw)
        for (int i = 1; i < baseW; i++) {
            sbForWidth.append(WTemplate.replace("{0}", i + "").replace("{1}", change(cellw * i) + ""))
        }
        sbForWidth.append(WTemplate.replace("{0}", baseW + "").replace("{1}", w + ""))
        sbForWidth.append("</resources>")

        float cellh = h * 1.0f / baseH
        StringBuffer sbForHeight = new StringBuffer()
        sbForHeight.append("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n")
        sbForHeight.append("<resources>")
        System.out.println("height : " + h + "," + baseH + "," + cellh)
        for (int i = 1; i < baseH; i++) {
            sbForHeight.append(HTemplate.replace("{0}", i + "").replace("{1}", change(cellh * i) + ""))
        }
        sbForHeight.append(HTemplate.replace("{0}", baseH + "").replace("{1}", h + ""))
        sbForHeight.append("</resources>")

        float minSize = (cellw < cellh) ? cellw : cellh
        StringBuffer sbMinSize = new StringBuffer()
        sbMinSize.append("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n")
        sbMinSize.append("<resources>")
        System.out.println("height : " + minSize + "," + cellw + "," + cellh)
        for (int i = 1; i <= baseW; i++) {
            sbMinSize.append(SizeTemplate.replace("{0}", i + "").replace("{1}", change(minSize * i) + ""))
        }
//        sbMinSize.append(SizeTemplate.replace("{0}", ((cellw < cellh) ? baseW : baseH) + "").
//                replace("{1}", ((cellw < cellh) ? w : h) + ""))
        sbMinSize.append("</resources>")

        float nearest = Math.abs(cellw - 1) < Math.abs(cellh - 1) ? cellw : cellh
        StringBuffer sbNearestSize = new StringBuffer()
        sbNearestSize.append("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n")
        sbNearestSize.append("<resources>")
        System.out.println("height : " + nearest + "," + cellw + "," + cellh)
        for (int i = 1; i <= baseW; i++) {
            sbNearestSize.append(NearestSizeTemplate.replace("{0}", i + "").replace("{1}", change(nearest * i) + ""))
        }
//        sbNearestSize.append(NearestSizeTemplate.replace("{0}", ((cellw < cellh) ? baseW : baseH) + "").
//                replace("{1}", ((cellw < cellh) ? w : h) + ""))
        sbNearestSize.append("</resources>")

        File fileDir = new File(path)
        if (!fileDir.exists())
            fileDir.mkdir()

        File layxFile = new File(fileDir.getAbsolutePath(), "lay_x.xml")
        File layyFile = new File(fileDir.getAbsolutePath(), "lay_y.xml")
        File laysizeFile = new File(fileDir.getAbsolutePath(), "lay_size.xml")
        File layNearestFile = new File(fileDir.getAbsolutePath(), "lay_size_nearest.xml")
        if (!layxFile.exists()) layxFile.createNewFile()
        if (!layyFile.exists()) layyFile.createNewFile()
        if (!laysizeFile.exists()) laysizeFile.createNewFile()
        if (!layNearestFile.exists()) layNearestFile.createNewFile()

        try {
            PrintWriter pw = new PrintWriter(new FileOutputStream(layxFile))
            pw.print(sbForWidth.toString())
            pw.close()
            pw = new PrintWriter(new FileOutputStream(layyFile))
            pw.print(sbForHeight.toString())
            pw.close()
            pw = new PrintWriter(new FileOutputStream(laysizeFile))
            pw.print(sbMinSize.toString())
            pw.close()
            pw = new PrintWriter(new FileOutputStream(layNearestFile))
            pw.print(sbNearestSize.toString())
            pw.close()
        } catch (FileNotFoundException e) {
            e.printStackTrace()
        }
    }

    private static float change(double a) {
        int temp = (int) (a * 100)
        return temp / 100f
    }
}