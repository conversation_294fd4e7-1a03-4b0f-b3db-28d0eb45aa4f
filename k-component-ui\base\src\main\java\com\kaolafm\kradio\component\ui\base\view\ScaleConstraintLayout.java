package com.kaolafm.kradio.component.ui.base.view;

import android.content.Context;
import androidx.constraintlayout.widget.ConstraintLayout;
import android.util.AttributeSet;
import android.view.MotionEvent;
import com.kaolafm.kradio.lib.utils.AnimUtil;

/**
 * <AUTHOR> on 2018/6/6.
 */

public class ScaleConstraintLayout extends ConstraintLayout{
    private boolean canScale = true;

    public ScaleConstraintLayout(Context context) {
        super(context);
    }

    public ScaleConstraintLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public ScaleConstraintLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (canScale) {
            AnimUtil.onTouchEvent(this, event);
        }
        return super.onTouchEvent(event);
    }

    public void setCanScale(boolean canScale) {
        this.canScale = canScale;
    }
}
