package com.kaolafm.kradio.flavor.impl;

import android.annotation.SuppressLint;
import android.os.Handler;
import androidx.fragment.app.Fragment;
import android.util.Log;


import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.k_kaolafm.home.HorizontalHomePlayerFragment;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioPlayLogicInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioOperateClickCellInter;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.player.ui.horizontal.RadioPlayerFragment;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.playcontrol.PlayControl;

import java.util.List;


/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-08-28 16:17
 ******************************************/
public final class KRadioAudioPlayLogicImpl implements KRadioAudioPlayLogicInter {
    private static final String TAG = "KRadioAudioPlayLogicImpl";

    @SuppressLint("LongLogTag")
    @Override
    public boolean autoPlayAudio(Object... args) {
        KRadioOperateClickCellInter operateModelInter = ClazzImplUtil.getInter("KRadioOperateClickImpl");
        boolean auto = false;
        Log.d(TAG, "autoPlayAudio operateModelInter=" + operateModelInter);
        if (operateModelInter != null && operateModelInter.getDataIntent() != null && operateModelInter.getIntentUri(operateModelInter.getDataIntent()) != null) {
            auto = true;
        }
        Log.d(TAG, "autoPlayAudio=" + auto);
        return auto;
    }



    @Override
    public boolean requestAudioFocus(Object... args) {

        return PlayerManager.getInstance().requestAudioFocus();

    }

    @SuppressLint("LongLogTag")
    @Override
    public boolean resumeAudioPlayLogic(Object... args) {
        PlayerManager playerManager = PlayerManager.getInstance();
        try {
            KRadioOperateClickCellInter operateModelInter = ClazzImplUtil.getInter("KRadioOperateClickImpl");
            if (operateModelInter != null && operateModelInter.getDataIntent() != null && operateModelInter.getIntentUri(operateModelInter.getDataIntent()) != null && operateModelInter.isOperate(operateModelInter.getDataIntent())) {
                Log.d(TAG, "recoverPlay  operateModelInter  getScheme=" + operateModelInter.getIntentUri(operateModelInter.getDataIntent()).getScheme());
                //因有的时候车机 在初次打开状态下不走new intent 及后续点击 从堆栈直接拉起的情况 在reusme恢复播放时进行一次判断
                if (playerManager.getCurrentAudioFocusStatus() < 0) {
                    requestAudioFocus();
                }
                operateModelInter.toPlay();
                try {
                    //跳转进入默认进入播放详情页。
                    if ((args[0]) instanceof LauncherActivity) {
                        List<Fragment> fragments = ((LauncherActivity) args[0]).getSupportFragmentManager().getFragments();
                        Fragment topFragment = fragments.get(0);
                        Log.d(TAG, "getSupportFragmentManager  fragments=" + fragments.toString());

                        if (fragments.size() < 3 && topFragment instanceof HorizontalHomePlayerFragment) {
                            List<PlayItem> playList = PlayerManager.getInstance().getPlayList();
                            if (ListUtil.isEmpty(playList)) {
                                //初始化刚进来  播放器没有初始化完整
                                Log.i(TAG, "jumpToPlayerFragment empty list");

                                ((HorizontalHomePlayerFragment) topFragment).extraTransaction().start(new RadioPlayerFragment());
//                            boolean isInitSuccess = playerManager.isPlayerInitSuccess();
//
                            } else {

                                ((HorizontalHomePlayerFragment) topFragment).jumpToPlayerFragment();

                            }
                        }

                    }

                } catch (Exception e) {
                    e.printStackTrace();
                }

                return true;
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

        recoverPlay();
        return true;
    }

    @Override
    public boolean restartAudioPlayLogic(Object... args) {
        return false;
    }

    @SuppressLint("LongLogTag")
    private void recoverPlay() {

        PlayerManager playerManager = PlayerManager.getInstance();

        Log.i(TAG, "recoverPlay---------->PlayerManager.isPausedFromUser() = " + playerManager.isPauseFromUser()
                + "          PlayerManager.getCurrentAudioFocusStatus() = " + playerManager.getCurrentAudioFocusStatus());

        if (playerManager.isPauseFromUser()) {
            if (playerManager.getCurrentAudioFocusStatus() < 0) {
                requestAudioFocus();
            }
            return;
        }
        if (playerManager.getCurrentAudioFocusStatus() < 0) {
            requestAudioFocus();
        }

        if (!playerManager.isPlaying()) {

            PlayerManagerHelper.getInstance().switchPlayerStatus(false);
        }
    }

    @Override
    public boolean doStartInPlay(Object... args) {
        return false;
    }
}
