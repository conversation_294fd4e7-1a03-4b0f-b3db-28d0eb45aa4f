package com.kaolafm.kradio.huawei.activity;

import android.app.Activity;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.Toast;

import com.kaolafm.kradio.common.http.api.feedback.FeedbackRequest;
import com.kaolafm.kradio.common.report.ReportUtil;
import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.base.flavor.KRadioDialogAttrInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioToastInter;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

public class FeedbackActivity extends Activity {
    private ImageView ivSuggestQr;
    FrameLayout flDialogCenterContent;
    ImageView ivDialogCenterClose;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ReportUtil.setPageId(Constants.PAGE_ID_ACCOUNT_OPINION);
        init();
        requestWXQrCode();
    }

    private void init() {
        LayoutInflater inflater = LayoutInflater.from(this);
        View view = inflater.inflate(R.layout.dialog_common_suggest_center, null);
        WindowManager.LayoutParams lp = this.getWindow().getAttributes();
        lp.alpha = 1f;
        lp.height = ResUtil.getDimen(R.dimen.y630);
        this.getWindow().setAttributes(lp);
        ivDialogCenterClose = view.findViewById(R.id.iv_dialog_center_close);
        ivDialogCenterClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                FeedbackActivity.this.finish();
            }
        });
        View view2 = inflater.inflate(R.layout.user_center_suggest_dailog, null);
        flDialogCenterContent = view.findViewById(R.id.fl_dialog_center_content);
        flDialogCenterContent.addView(view2);
        ivSuggestQr = view2.findViewById(R.id.iv_suggest_qr);
        setContentView(view);
    }

    @Override
    public void onDestroy() {
        ivSuggestQr.setImageResource(0);
        System.gc();
        super.onDestroy();
    }

    @Override
    protected void onStart() {
        super.onStart();
    }

    @Override
    protected void onStop() {
        super.onStop();
    }

    private void requestWXQrCode() {
        new FeedbackRequest().getWechatQRCodeForFeedback(new HttpCallback<String>() {
            @Override
            public void onSuccess(String s) {
                ImageLoader.getInstance().displayImage(FeedbackActivity.this, s, ivSuggestQr);
            }

            @Override
            public void onError(ApiException e) {
                KRadioToastInter kRadioToastInter = ClazzImplUtil.getInter("KRadioToastImpl");
                if (kRadioToastInter != null) {
                    kRadioToastInter.showToast(FeedbackActivity.this, "获取不到二维码", Toast.LENGTH_LONG);
                } else {
                    ToastUtil.showOnly(FeedbackActivity.this, "获取不到二维码");
                }
            }
        });

    }
}