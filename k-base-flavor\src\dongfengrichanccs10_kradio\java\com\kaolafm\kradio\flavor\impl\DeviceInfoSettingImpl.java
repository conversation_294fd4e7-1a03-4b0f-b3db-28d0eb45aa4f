package com.kaolafm.kradio.flavor.impl;

import android.content.Context;

import com.kaolafm.kradio.flavor.utils.DeviceInfoUtil;
import com.kaolafm.kradio.lib.base.flavor.DeviceInfoSetting;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-03-22 11:46
 ******************************************/
public final class DeviceInfoSettingImpl implements DeviceInfoSetting {
    @Override
    public void setInfoForSDK(Context context) {
        try {
            Class clazz = Class.forName("com.hsae.autosdk.settings.AutoSettings");
            Method methodInstance = clazz.getDeclaredMethod("getInstance");
            Object obj = methodInstance.invoke(clazz);
            Method methodDeviceEx = clazz.getDeclaredMethod("getDeviceIdEx");
            String deviceId = (String) methodDeviceEx.invoke(obj);
            DeviceInfoUtil.setDeviceIdAndCarType(deviceId, null);
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
