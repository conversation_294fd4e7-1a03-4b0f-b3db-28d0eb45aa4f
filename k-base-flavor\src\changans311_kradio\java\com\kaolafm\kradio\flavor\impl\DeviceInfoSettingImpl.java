package com.kaolafm.kradio.flavor.impl;

import android.util.Log;

import com.incall.proxy.constant.TboxConstantsDef;
import com.incall.proxy.setting.SettingManager;
import com.incall.proxy.tbox.TboxManager;
import com.kaolafm.kradio.lib.utils.StringUtil;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-05-22 20:50
 ******************************************/
public final class DeviceInfoSettingImpl implements DeviceInfoSetting {
    @Override
    public void setInfoForSDK(Context context) {
        String vin = TboxManager.getInstance().getId(TboxConstantsDef.ID_TYPE.VIN);
        Log.i("zsj", "setInfoForSDK: " + vin);
        String deviceId = StringUtil.makeAsciiOnly(vin);
        String carType = SettingManager.getInstance().getCurCarType();
        DeviceInfoUtil.setDeviceIdAndCarType(deviceId, carType);
//        if (StringUtil.isAsciiOnly(vin)) {
//            Log.i("zsj", "setInfoForSDK: allAscii ");
//            return vin;
//        } else {
//            Log.i("zsj", "setInfoForSDK: not allAscii ");
//            return null;
//        }
    }
}
