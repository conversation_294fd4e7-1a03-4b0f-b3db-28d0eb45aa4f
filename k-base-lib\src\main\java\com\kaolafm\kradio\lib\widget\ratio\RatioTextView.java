package com.kaolafm.kradio.lib.widget.ratio;

import android.content.Context;
import android.content.res.TypedArray;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatTextView;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.MotionEvent;
import com.kaolafm.kradio.lib.R;
import com.kaolafm.kradio.lib.utils.AnimUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;

/**
 * <AUTHOR>
 * @date 2019-08-17
 */
public class RatioTextView extends AppCompatTextView {

    private boolean canScale;

    private double mHeightRatio;

    private double mWidthRatio;

    public RatioTextView(Context context) {
        this(context, null);
    }

    public RatioTextView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public RatioTextView(Context context, @Nullable AttributeSet attrs,
            int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.RatioTextView);
        canScale = ta.getBoolean(R.styleable.RatioTextView_canScale, true);
        String ratio = ta.getString(R.styleable.RatioTextView_wh_ratio);
        parseRatio(ratio);
        ta.recycle();
    }

    private void parseRatio(String ratio) {
        if (!TextUtils.isEmpty(ratio)) {
            String[] wh = ratio.split("\\:");
            if (wh.length == 2) {
                mWidthRatio = Double.valueOf(wh[0]);
                mHeightRatio = Double.valueOf(wh[1]);
            }
        }
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        setMeasuredDimension(getDefaultSize(0, widthMeasureSpec), getDefaultSize(0, heightMeasureSpec));
        int[] specs = ViewUtil.measureByRatio(this, mWidthRatio, mHeightRatio, widthMeasureSpec, heightMeasureSpec);
        super.onMeasure(specs[0], specs[1]);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (canScale) {
            AnimUtil.onTouchEvent(this, event);
        }
        return super.onTouchEvent(event);
    }
    public void setRatio(String ratio) {
        parseRatio(ratio);
    }
}
