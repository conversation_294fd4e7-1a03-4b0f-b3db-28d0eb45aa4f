<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_dialog">


    <TextView
        android:id="@+id/tv_dialog_center_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingLeft="@dimen/x90"
        android:paddingRight="@dimen/x90"
        android:layout_marginBottom="@dimen/x100"
        android:gravity="center_horizontal"
        android:textColor="@color/text_color_5"
        android:textSize="@dimen/text_size5"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>


    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="1"/>

    <TextView
        android:id="@+id/tv_dialog_center_define"
        android:layout_width="@dimen/x146"
        android:layout_height="@dimen/y56"
        android:layout_marginRight="@dimen/x30"
        android:background="@drawable/globle_round_bg"
        android:gravity="center"
        android:textColor="@color/text_color_5"
        android:textSize="@dimen/text_size5"
        android:layout_marginBottom="@dimen/y62"
        app:layout_constraintBottom_toTopOf="@+id/guideline1"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/tv_dialog_center_cancel"/>

    <TextView
        android:id="@+id/tv_dialog_center_cancel"
        android:layout_width="@dimen/x146"
        android:layout_height="@dimen/y56"
        android:layout_marginLeft="@dimen/x30"
        android:background="@drawable/globle_round_bg"
        android:gravity="center"
        android:textColor="@color/text_color_5"
        android:textSize="@dimen/text_size5"
        android:layout_marginBottom="@dimen/y62"
        app:layout_constraintBottom_toTopOf="@+id/guideline1"
        app:layout_constraintLeft_toRightOf="@id/tv_dialog_center_define"
        app:layout_constraintRight_toRightOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>