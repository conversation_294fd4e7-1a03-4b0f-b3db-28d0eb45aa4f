package com.kaolafm.kradio.category.radio;

import androidx.fragment.app.Fragment;
import android.view.View;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.categories.CategoryConstant;
import com.kaolafm.kradio.lib.base.ui.BaseLazyFragment;

/**
 * <AUTHOR>
 **/
public class RadioDispatchFragment extends BaseLazyFragment<RadioDispatchContract.IPresenter> implements RadioDispatchContract.IView {

    @Override
    public void showData(Fragment fragment) {
        getChildFragmentManager().beginTransaction().replace(R.id.radio_content, fragment).commit();
        fragment.setUserVisibleHint(true);
    }

    @Override
    protected void lazyLoad() {
//        int type = getArguments().getInt(CategoryConstant.MEDIA_TYPE);
        long categoryId = getArguments().getLong(CategoryConstant.CATEGORY_ID);
        mPresenter.loadData(categoryId);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.fragment_radio;
    }

    @Override
    protected int getLayoutId_Tow() {
        return 0;
    }

    @Override
    protected RadioDispatchContract.IPresenter createPresenter() {
        return new RadioDispatchPresenter(this);
    }

    @Override
    public void initView(View view) {
        //因为使用getFragmentManager().beginTransaction().replace(R.id.qq_content, mFragmentUnload).commit();
        //所以,不需要解析view
    }
}
