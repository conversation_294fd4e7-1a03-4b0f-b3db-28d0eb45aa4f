package com.kaolafm.kradio.onlineactivity.ui;


import com.kaolafm.kradio.lib.base.mvp.IView;
import com.kaolafm.opensdk.api.activity.model.Activity;

import java.util.List;

public interface IActivityView extends IView {
    void onActivityInfo(List<Activity> activityList);

    void showEmpty();
    void hideEmpty();

    void showLoading();
    void hideLoading();

    //网络异常
    void showError(String error, boolean clickToRetry);
}
