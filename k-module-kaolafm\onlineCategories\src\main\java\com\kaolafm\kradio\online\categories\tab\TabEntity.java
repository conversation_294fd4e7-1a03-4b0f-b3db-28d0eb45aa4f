package com.kaolafm.kradio.online.categories.tab;

import com.flyco.tablayout.listener.CustomTabEntity;

/**
 * <AUTHOR>
 * @date 2019-07-23
 */
public class TabEntity implements CustomTabEntity {

    private String mTabTitle;

    private String code;

    public TabEntity(String tabTitle, String code) {
        mTabTitle = tabTitle;
        this.code = code;
    }

    @Override
    public String getTabTitle() {
        return mTabTitle;
    }

    @Override
    public int getTabSelectedIcon() {
        return 0;
    }

    @Override
    public int getTabUnselectedIcon() {
        return 0;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
