package com.kaolafm.kradio.component.ui.base;

import android.content.Context;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;

import com.kaolafm.kradio.component.ui.R;

/**
 * <AUTHOR>
 * @date 2023-03-03
 */
public class ComponentUI extends LinearLayout {




    public ComponentUI(Context context) {
        super(context);
    }

    public ComponentUI(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public ComponentUI(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        View view = LayoutInflater.from(context).inflate(R.layout.common_center_dialog, null);
    }
}
