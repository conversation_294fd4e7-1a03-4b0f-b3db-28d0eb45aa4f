package com.kaolafm.kradio.common.helper;

import android.util.Log;

import com.kaolafm.kradio.lib.bean.SubscribeData;
import com.kaolafm.kradio.lib.utils.AppExecutors;
import com.kaolafm.kradio.component.DynamicComponent;
import com.kaolafm.kradio.component.RealCaller;

import java.util.List;

/**
 * <AUTHOR>
 **/
public abstract class QqSubscribeChangeListenerComponent implements DynamicComponent {

    public static final String NAME = "QqSubscribeChangeListenerComponent";
    public static final String FUN_NAME = "onQQSubscribesChanged";
    private static final String TAG = "subscribe.cb";

    private long listenerId;

    public QqSubscribeChangeListenerComponent(long listenerId) {
        this.listenerId = listenerId;
    }

    @Override
    public String getName() {
        return String.valueOf(listenerId);
    }

    @Override
    public boolean onCall(RealCaller caller) {
        String actionName = caller.actionName();
        List<SubscribeData> subscribes = caller.getParamValue("resultOfOnQQSubscribesChanged");

        Log.i(TAG, "[QqSubscribeChangeListenerComponent]动态组件收到回调: actionName=" + actionName);

        if (FUN_NAME.equals(actionName)) {
            AppExecutors.getInstance().mainThread().execute(new Runnable() {
                @Override
                public void run() {
                    onSubscribesChanged(subscribes);
                }
            });
        }
        return true;
    }

    protected abstract void onSubscribesChanged(List<SubscribeData> subscribes);
}
