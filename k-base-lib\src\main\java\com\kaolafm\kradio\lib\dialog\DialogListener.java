package com.kaolafm.kradio.lib.dialog;

import android.app.Dialog;

/**
 * dialog回调
 * <AUTHOR>
 * @date 2018/5/11
 */

public interface DialogListener {
    interface OnClickListener{
        void onClick(Dialog dialog, int which);
    }
    interface OnPositiveListener<T> {
        void onClick(T dialog);
    }
    interface OnNativeListener<T>{
        void onClick(T dialog);
    }
    interface OnDismissListener{
        void onDismiss();
    }
}
