// MicroModule build file where you can declare MicroModule dependencies.

dependencies {
    implementation fileTree(dir: 'homeComprehensive/libs', include: ['*.jar'])
    implementation microModule(':common')
    implementation microModule(':home')
    implementation microModule(':playerComprehensive')
    implementation microModule(':userComprehensive')
    implementation microModule(':subscribeComprehensive')
    implementation microModule(':historyComprehensive')
    implementation microModule(':adComprehensive')
    implementation microModule(':subscribe')
    implementation microModule(':scene')
    implementation microModule(':coin')
    implementation microModule(':messageComprehensive')
    implementation microModule(':mainTab')
    implementation microModule(':activityComprehensive')
    implementation microModule(':liveComprehensive')
}
