package com.kaolafm.kradio.online.common.playbar;

import android.app.Activity;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleObserver;
import androidx.lifecycle.OnLifecycleEvent;
import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Color;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.os.Build;
import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.appcompat.app.AppCompatActivity;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.kradio.common.bean.BroadcastRadioSimpleData;
import com.kaolafm.kradio.common.event.BroadcastPlayerChangedData;
import com.kaolafm.kradio.common.report.ReportUtil;
import com.kaolafm.kradio.common.utils.TimeUtil;
import com.kaolafm.kradio.k_kaolafm.BuildConfig;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioNavBarInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioToastInter;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.utils.imageloader.GlideApp;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.lib.widget.AutoMarqueenTextView;
import com.kaolafm.kradio.online.common.event.JumpToOnlinePlayerEvent;
import com.kaolafm.kradio.online.common.event.ScrollToPlayingPlayItemPositionEvent;
import com.kaolafm.kradio.online.common.player.PlayerHelper;
import com.kaolafm.kradio.online.common.view.OnlineAutoMarqueenTextView;
import com.kaolafm.kradio.online.common.view.YunTingProgressBar;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.subscribe.SubscribeModel;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.AlbumPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.BroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.RadioPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.report.event.PlayerUiControlReportEvent;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import fr.castorflex.android.circularprogressbar.CircularProgressBar;
import kotlin.jvm.Synchronized;

import static com.kaolafm.kradio.common.utils.TimeUtil.getAudioUpdateTime;
import static com.kaolafm.kradio.lib.utils.ResUtil.getString;


/**
 * Created by v on 2018/4/12.
 */

public class OnlinePlayerBar extends RelativeLayout implements OnlinePlayerbarContract.IPlayerView {
    private static final String TAG = "PlayerBar";
    /**
     * 电台标签最长
     */
    private static final int MAX_SUB_TAG_LEN = 8;
    private static final String SUB_TAG_REPLACE_STRING = "...";

    private ImageView playerBarFun;

    private ImageView playerBarCover;

    private OnlineAutoMarqueenTextView playerBarSubTitleText;

    private CircularProgressBar playerBarLoading;

    private ImageView playerBarPlay;

    private ImageView playerBarCollect;

    private ImageView playerBarNext;
    private YunTingProgressBar ovalFlipSeekBar;
    private ImageView playerBarPrevious;
    private View playerBarPlayRl;
    private RelativeLayout playerBarConstrantlayoutRl;
    private OnlinePlayerBarPresenter mPresenter;
    private int mMargin;
    private boolean isLiveState;
    private Context mContext;
    private boolean isSeekByUser = false;

    private TextView playerBarPositionTv;
    private Runnable dismissPlayerbarPositionViewRunnable = new Runnable() {
        @Override
        public void run() {
//            ovalFlipSeekBar.setProgressCanInvalidate(true);
            updatePlayBarControlAndPlayTimeVisibility(true);
        }
    };

    public OnlinePlayerBar(Context context) {
        super(context);
        init(context);
    }

    public OnlinePlayerBar(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public OnlinePlayerBar(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public OnlinePlayerBar(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init(context);
    }

    @Synchronized
    private void updatePlayBarControlAndPlayTimeVisibility(boolean showPlayBarControl) {
        if (showPlayBarControl) {
            Log.e(TAG, "updatePlayBarControlAndPlayTimeVisibility设置显示");
            ViewUtil.setViewVisibility(playerBarPositionTv, View.GONE);
            ViewUtil.setViewVisibility(playerBarPlayRl, View.VISIBLE);
        } else {
            Log.e(TAG, "updatePlayBarControlAndPlayTimeVisibility设置隐藏");
            ViewUtil.setViewVisibility(playerBarPlayRl, View.GONE);
            ViewUtil.setViewVisibility(playerBarPositionTv, View.VISIBLE);
        }
    }

    private void init(Context ctx) {
        mContext = ctx;

        LayoutInflater.from(ctx).inflate(R.layout.online_bar_player_horiztal, this, true);
        playerBarPositionTv = findViewById(R.id.playerBarPositionTv);

        playerBarFun = (ImageView) findViewById(R.id.player_bar_fun);
        playerBarFun.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!AntiShake.check(v.getId())) {
                    jumpToPlayerActivity();
                }
            }
        });
        setPlayerBarFunVisibility(false);

        playerBarCover = (ImageView) findViewById(R.id.player_bar_cover);
        ovalFlipSeekBar = findViewById(R.id.online_main_seekbar);


        playerBarLoading = (CircularProgressBar) findViewById(R.id.player_bar_loading);
        playerBarPlay = (ImageView) findViewById(R.id.player_bar_play);
        playerBarCollect = (ImageView) findViewById(R.id.player_bar_collect);
        playerBarPrevious = (ImageView) findViewById(R.id.player_bar_previous);
        playerBarNext = (ImageView) findViewById(R.id.player_bar_next);
        playerBarPlayRl = findViewById(R.id.player_bar_play_rl);

        playerBarSubTitleText = findViewById(R.id.player_bar_sub_title_text);
        if (BuildConfig.IS_PLAY_ANIM) {
            playerBarSubTitleText.setFocusable(true);
        } else {
            playerBarSubTitleText.setFocusable(false);
        }

        playerBarConstrantlayoutRl = (RelativeLayout) findViewById(R.id.player_bar_constrantlayout_rl);
        playerBarConstrantlayoutRl.setOnClickListener(v -> {
            Log.i(TAG, "mPlayerBar onClick");
            if (!AntiShake.check(v.getId())) {
                Log.i(TAG, "mPlayerBar onClick be executed");
                if (PlayerManagerHelper.getInstance().isInProgramPlayerPage()) {
                    //在播放页，点击操作表示要定位到当前播放项
                    EventBus.getDefault().post(new ScrollToPlayingPlayItemPositionEvent());
                    return;
                }
                jumpToPlayerActivity();
            }
        });
//        mAiRadioMinusFeedbackView = findViewById(R.id.ai_radio_minus_feed_back_view);
        KRadioNavBarInter kRadioNavBarInter = ClazzImplUtil.getInter("KRadioNavBarImpl");
        if (kRadioNavBarInter != null) {
            kRadioNavBarInter.initNavBarHomeUI(this);
        }

        mPresenter = new OnlinePlayerBarPresenter(getContext(), this);
        ovalFlipSeekBar.setYunTingProgressBarCallback(new YunTingProgressBar.YunTingProgressBarCallback() {
            @Override
            public void onProgressChanged(YunTingProgressBar seekBar, float progress, boolean isUser) {
                int position = (int) progress;
                if (playerBarPositionTv.getVisibility() != View.VISIBLE) return;
                long duration = (long) seekBar.getMaxProgress();
                duration = Math.max(duration, 0);
                String totalTime = TimeUtil.getStringForTimeAutoShowHours(duration);
                String currentProgressTime;
                if (TimeUtil.isReachOneHour(duration)) {
                    currentProgressTime = TimeUtil.getStringForTimeShowHour(position);
                } else {
                    currentProgressTime = TimeUtil.getStringForTime(position);
                }
                String str = String.format(getString(R.string.online_playerbar_position_text), currentProgressTime, totalTime);

                SpannableString spannableString = new SpannableString(str);
                spannableString.setSpan(new ForegroundColorSpan(ResUtil.getColor(R.color.online_playerbar_position_highlight)), 0, currentProgressTime.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                playerBarPositionTv.setText(spannableString);
            }

            @Override
            public void onStartTrackingTouch(YunTingProgressBar seekBar) {
                isSeekByUser = true;
//                ovalFlipSeekBar.setProgressCanInvalidate(false);
                if (getHandler().hasCallbacks(dismissPlayerbarPositionViewRunnable)) {
                    getHandler().removeCallbacks(dismissPlayerbarPositionViewRunnable);
                }

                updatePlayBarControlAndPlayTimeVisibility(false);
            }

            @Override
            public void onStopTrackingTouch(YunTingProgressBar seekBar) {
                int position = (int) seekBar.getProgress();
                PlayerManagerHelper.getInstance().seek(position);
                isSeekByUser = false;
                getHandler().post(dismissPlayerbarPositionViewRunnable);
                String eventPosition;
                if (PlayerManagerHelper.getInstance().isInProgramPlayerPage()) {
                    eventPosition = PlayerUiControlReportEvent.POSITION_PLAY_FRAGEMNT;
                } else {
                    eventPosition = PlayerUiControlReportEvent.POSITION_PLAY_BAR;
                }
                ReportUtil.addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_CHANGE_PROGRESS, PlayerUiControlReportEvent.CONTROL_TYPE_SLIDE, eventPosition);
            }

            @Override
            public void onSingleClick(YunTingProgressBar seekBar) {
                int position = (int) seekBar.getProgress();
                PlayerManagerHelper.getInstance().seek(position);
                if (getHandler().hasCallbacks(dismissPlayerbarPositionViewRunnable)) {
                    getHandler().removeCallbacks(dismissPlayerbarPositionViewRunnable);
                }
                updatePlayBarControlAndPlayTimeVisibility(false);
//                ovalFlipSeekBar.setProgressCanInvalidate(false);
                getHandler().postDelayed(dismissPlayerbarPositionViewRunnable, 500);
                String eventPosition;
                if (PlayerManagerHelper.getInstance().isInProgramPlayerPage()) {
                    eventPosition = PlayerUiControlReportEvent.POSITION_PLAY_FRAGEMNT;
                } else {
                    eventPosition = PlayerUiControlReportEvent.POSITION_PLAY_BAR;
                }
                ReportUtil.addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_CHANGE_PROGRESS, PlayerUiControlReportEvent.CONTROL_TYPE_CLICK, eventPosition);
            }
        });

        EventBus.getDefault().register(this);
        showPauseState();
    }

    /**
     * 跳转到播放页
     */
    public void jumpToPlayerActivity() {
        PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
        if (playItem != null) {
            if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(getContext())) {
                return;
            }
        }
        EventBus.getDefault().post(new JumpToOnlinePlayerEvent(PlayerManagerHelper.getInstance().getPlayId(playItem)));
    }

    /**
     * 跳播放器
     */
//    public void jumpToPlayerFragment() {
//        //fixed 避免多次跳转同一fragment，创建多个fragment实例
//        if (PlayerHelper.isLiving()) {
//            jumpToLivePage(null);
//        } else {
//            SupportActivity activity = (SupportActivity) getContext();
//            int type = PlayerManagerHelper.getInstance().getCurrentPlayType();
//            if (type == PlayerConstants.RESOURCES_TYPE_BROADCAST) {
////                ComponentClient.obtainBuilder(BroadcastPlayerActivityComponentConst.NAME)
////                        .setActionName(BroadcastPlayerActivityComponentConst.START_ACTIVITY)
////                        .addParam("context", activity)
////                        .build().callAsync();
//                return;
//            }
//            List<PlayItem> playList = PlayerManager.getInstance().getPlayList();
//            if (ListUtil.isEmpty(playList)) {
//                Log.i(TAG, "jumpToPlayerFragment empty list");
//                return;
//            }
//
////            SubscribeHelper.isSubscribe = mPlayerBar.getSubscribeState();
//            if (!(activity.getTopFragment() instanceof RadioPlayerFragment)) {
//                Log.i(TAG, "jumpToPlayerFragment RadioPlayerFragment start");
//                FragmentManager fm = activity.getSupportFragmentManager();
//                int size = fm.getFragments().size();
//                Log.i("rsq", "size=" + size);
//                for (int i = fm.getFragments().size() - 1; i >= 0; i--) {
//                    Log.i("rsq", "i=" + i);
//                    if (fm.getFragments().get(i) instanceof RadioPlayerFragment) {
//                        ((BaseFragment) fm.getFragments().get(i)).pop();
//                    }
//                }
//                activity.extraTransaction().start(new RadioPlayerFragment());
//            } else {
//                Fragment fragment = (Fragment) activity.getTopFragment();
//                Lifecycle.State state = fragment.getLifecycle().getCurrentState();
//                if (state != Lifecycle.State.RESUMED) {
//                    activity.extraTransaction().startWithPop(activity.getTopFragment());
//                } else {
//                    RadioPlayerFragment radioPlayerFragment = (RadioPlayerFragment) activity.getTopFragment();
//                    radioPlayerFragment.mPlayListView.startCountDownTimer();
//                }
//            }
//        }
//    }

    /**
     * 跳到直播界面
     */
//    public void jumpToLivePage(Category.Item item) {
//        PlayerManagerHelper.getInstance().finishAudioAd();
//        Log.i(TAG, "jumpToLivePage");
//        long id = -1L;
//        if (item != null) {
//            id = item.id;
//        }
//        ComponentClient.obtainBuilder(LiveComponentConst.NAME)
//                .setActionName(LiveComponentConst.START_HOME_LIVE)
//                .cancelOnDestroyWith((Activity) getContext())
//                .addParam("liveId", id)
//                .addParam("context", this)
//                .build().callAsync();
//    }
    private void changeOrientationTextSize(int portrait, int landscap) {
        Log.i(TAG, "changeOrientationTextSize:" + ResUtil.getTextSize(portrait) + " " + ResUtil.getTextSize(landscap));
//        OritationViewUtils.handleOrientationTextSize(playerBarTitle, playerBarTitle, portrait, landscap);
    }

    public RelativeLayout getPlayerBarConstrantlayoutRl() {
        return playerBarConstrantlayoutRl;
    }

//    public View getPlayerBarActivityView() {
//        return playerBarActivity;
//    }

    public void setPlayerBarConstrantlayoutRl(RelativeLayout playerBarConstrantlayoutRl) {
        this.playerBarConstrantlayoutRl = playerBarConstrantlayoutRl;
    }

    public void attachPlayer() {
        mPresenter.attachPlayer();
    }

    public void detachPlayer() {
        //you cannot start a load for a destoryed activity
//        if (this.getRootActivity().isFinishing()) {
//            GlideApp.with(AppDelegate.getInstance().getContext()).clear(playerBarCover);
//        } else {
//            GlideApp.with(this).clear(playerBarCover);
//        }
        mPresenter.detachPlayer();
    }

    public void attachSubscribeModel(SubscribeModel subscribeModel) {
        mPresenter.attachSubscribeModel(subscribeModel);
    }

    public void detachSubscribeModel() {
        mPresenter.detachSubscribeModel();
    }

    @Override
    public void setCoverImageDrawable(Drawable drawable) {
        playerBarCover.setImageDrawable(drawable);
    }

    @Override
    public void setCoverImageUrl(String coverUrl) {
        ImageLoader.getInstance().displayImage(getContext(),
                coverUrl, playerBarCover, ResUtil.getDrawable(R.drawable.media_default_pic));
    }

    @Override
    public void setTitle(CharSequence title) {
//        Log.i("onekeylistener", title.toString());
        playerBarSubTitleText.setText(title);
    }

    @Override
    public void setSubtitle(CharSequence title) {
//         playerBarSubtitle.setText(title);
    }

    /**
     * @param progress 1~100
     */
    @Override
    public void updateProgress(int progress) {
//        修改 退出app ,播放器还在播放，再次打开app ，更新播放条状态，post 更新
//        if (isSeekByUser) {
//            return;
//        }
//        post(new Runnable() {
//            @Override
//            public void run() {
//                ovalFlipSeekBar.setProgress(progress);
//            }
//        });
    }

    @Override
    public void updateProgress(int position, int duration) {
        if (isSeekByUser) {
            return;
        }
        post(new Runnable() {
            @Override
            public void run() {
                ovalFlipSeekBar.setMaxProgress(duration);
//                playerBarProgress.setProgress(position);
                ovalFlipSeekBar.setProgress(position);
            }
        });
    }

    /**
     * 刚刚初始化后更新进度
     */
    public void updateProgressWhenInit() {
        PlayItem curPlayItem = PlayerManager.getInstance().getCurPlayItem();
        //如果正在播放，则会一直回调onProgress，无需重复设置进度，防止出现与真正进度不一致的情况
        if (curPlayItem == null) return;
        ovalFlipSeekBar.setMaxProgress(curPlayItem.getDuration());
        ovalFlipSeekBar.setProgress(curPlayItem.getPosition(), false);
    }

    public void setShortUnProgress(boolean shortUnProgress) {
        ovalFlipSeekBar.setShortUnProgress(shortUnProgress);
    }

    public boolean isShortUnProgress() {
        return ovalFlipSeekBar.isShortUnProgress();
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        showAccordingToScreen(ResUtil.getOrientation());
        attachPlayer();
        detachSubscribeModel();
        attachSubscribeModel(new SubscribeModel());
        addLifeObs();
    }

    @Override
    protected void onDetachedFromWindow() {
        detachSubscribeModel();
        super.onDetachedFromWindow();
        EventBus.getDefault().unregister(this);
//        if (mAiRadioMinusFeedbackView != null) {
//            mAiRadioMinusFeedbackView.release();
//        }
        detachPlayer();
        removeLifeObs();
    }

    private void addLifeObs() {
        getLifecycle().addObserver(lifecycleObserver = new LifecycleObserver() {
            @OnLifecycleEvent(Lifecycle.Event.ON_RESUME)
            public void onResume() {
                //只在启辰车机上出现这个问题，37534
                resetPlayerBarState();
                if (mPresenter != null)
                    mPresenter.updateBtnsState();
            }
        });
    }

    private void removeLifeObs() {
        if (lifecycleObserver != null) {
            getLifecycle().removeObserver(lifecycleObserver);
            lifecycleObserver = null;
        }
    }

    LifecycleObserver lifecycleObserver;


    @Override
    public void showLoading(boolean show) {
        if (isLiveState) {
            playerBarLoading.setVisibility(View.GONE);
            playerBarLoading.progressiveStop();
        } else {
//            ViewUtil.setViewVisibility(playerBarPlayRl, View.VISIBLE);
//            ViewUtil.setViewVisibility(playerBarLivePlayRl, View.GONE);
            ViewUtil.setViewVisibility(playerBarLoading, show ? View.VISIBLE : View.INVISIBLE);
            if (show) {
                playerBarLoading.progressiveStart();
            } else {
                playerBarLoading.progressiveStop();
            }
        }
    }

    @Override
    public void showPauseState() {
        Logging.i(TAG, "showPauseState start isLiveState = " + isLiveState);
        if (!isLiveState) {
            Log.e(TAG, "showPauseState设置显示");
            playerBarPlayRl.setVisibility(View.VISIBLE);
//            playerBarLivePlayRl.setVisibility(View.GONE);
            playerBarPlay.setImageResource(R.drawable.online_playerbar_play);
            Logging.i(TAG, "showPauseState drawable playerbar_play");
        } else {
//            Drawable drawable = ResUtil.getDrawable(R.drawable.playerbar_play);
//            playerBarLivePlayPause.setImageDrawable(drawable);
        }
//        playerBarTitle.setEllipsize(null);
    }

    @Override
    public void showPlayState() {
        Logging.i(TAG, "showPlayState start isLiveState = " + isLiveState);
        if (!isLiveState) {
            Drawable drawable = ResUtil.getDrawable(R.drawable.online_playerbar_pause);
            playerBarPlay.setImageDrawable(drawable);
            Logging.i(TAG, "showPlayState drawable ic_player_bar_pause_normal");
        }
//        playerBarTitle.setEllipsize(TextUtils.TruncateAt.MARQUEE);
    }

    /**
     * 收藏
     */
    @Override
    public void setCollectState(boolean isCollect) {
        playerBarCollect.setSelected(isCollect);
//
//        boolean isDisplay =  PlayerManager.getInstance().getPlayListInfo().getNoSubscribe() != 1;
//        ViewUtil.setViewVisibility(playerBarCollect, isDisplay ? VISIBLE : View.GONE);
    }

    @Override
    public boolean getCollectState() {
        if (playerBarCollect != null) {
            return playerBarCollect.isSelected();
        }
        return false;
    }

    @Override
    public void setCollectClickListener(OnClickListener listener) {
        playerBarCollect.setOnClickListener(listener);
    }

    @Override
    public void setNextClickListener(OnClickListener listener) {
        playerBarNext.setOnClickListener(listener);
    }

    @Override
    public void setPreClickListener(OnClickListener listener) {
        playerBarPrevious.setOnClickListener(listener);
    }

    @Override
    public void setPlayOrPauseClickListener(OnClickListener listener) {
        playerBarPlay.setOnClickListener(listener);
    }

    private String getAlbumName(PlayItem playItem) {
        if (playItem instanceof BroadcastPlayItem) {
            String frequencyChannel = ((BroadcastPlayItem) playItem).getFrequencyChannel();
            String albumName = ((BroadcastPlayItem) playItem).getInfoData().getAlbumName();
            if (!StringUtil.isEmpty(frequencyChannel)) {
                return String.format(AppDelegate.getInstance().getContext().getResources().getString(R.string.one_zh_cn_char_joint_str), albumName, getString(R.string.full_str), frequencyChannel);
            } else {
                return albumName;
            }
        }
        return playItem.getAlbumTitle();
    }


    @Override
    public void updateInfo(int cp, PlayItem playItem) {

        if (playItem == null) {
            return;
        }
        String title = playItem.getTitle();
        String subtitle = getAlbumName(playItem);
        String audioPic = PlayerManagerHelper.getInstance().getPlayItemPicUrl(playItem);

        boolean isShowTitle = !StringUtil.isEmpty(title);
        int type = playItem.getType();

        if (PlayerConstants.RESOURCES_TYPE_ALBUM == type) {
            String order = getResources().getString(R.string.album_audio_info_format_str);
            order = StringUtil.format(order, ((AlbumPlayItem) playItem).getInfoData().getOrderNum());
            title = order + "  " + title;
        }
        // 设置圆形图片
        ImageLoader.getInstance().displayImage(getContext(), audioPic, playerBarCover, ResUtil.getDrawable(R.drawable.media_default_pic));

        int whichPlayer = PlayerManager.getInstance().getCurPlayItem().getType();

        setPlayerUi(whichPlayer);

        int flagType = PlayerHelper.getPlayFlagType(playItem);
        setFlag(flagType);

        if (isShowTitle) {
            setTitle(title);
        }

//        if (type == PlayerConstants.RESOURCES_TYPE_RADIO) {
//            updateSubTitle(playItem);
//        } else if (type == PlayerConstants.RESOURCES_TYPE_LIVE_STREAM) {
//            updateLiveStreamSubTitle(playItem);
//        } else if (type == PlayerConstants.RESOURCES_TYPE_LIVING) {
//            updateLiveSubTitle(playItem);
//        } else {
////            ViewUtil.setViewVisibility(playerBarVerticalLine, View.GONE);
////            ViewUtil.setViewVisibility(playerBarTimeText, View.GONE);
////            ViewUtil.setViewVisibility(playerBarSubTagText, View.GONE);
////            ViewUtil.setViewVisibility(playerBarSubTitleText, View.VISIBLE);
////            playerBarTitle.setText(title);
//            setSubTitle(subtitle);
//        }

//        VipCornerUtil.setVipCorner(mVipIcon, playItem.getVip(), playItem.getFine());
    }

    private void setButtonIcons(@DrawableRes int collectIconRid, @DrawableRes int leftIconRid, @DrawableRes int rightIconRid) {
//        Log.e(TAG,"setButtonIcons设置显示");
//        playerBarPlayRl.setVisibility(View.VISIBLE);
//        playerBarLivePlayRl.setVisibility(View.GONE);

//        if (playerBarPrevious.getVisibility() == View.VISIBLE) {
        playerBarPrevious.setVisibility(View.VISIBLE);
//        }
        if (playerBarNext.getVisibility() != View.VISIBLE) {
            playerBarNext.setVisibility(View.VISIBLE);
        }

        showOrHideAiRadioMinusFeedback(false);
        showOrHideCollect(true);
        playerBarCollect.setImageDrawable(ResUtil.getDrawable(collectIconRid));
        playerBarPrevious.setImageDrawable(ResUtil.getDrawable(leftIconRid));
        playerBarNext.setImageDrawable(ResUtil.getDrawable(rightIconRid));
    }

//    private void setBroadcastIcons(@DrawableRes int leftIconRid, @DrawableRes int rightIconRid) {
//        playerBarPlayRl.setVisibility(View.VISIBLE);
////        playerBarLivePlayRl.setVisibility(View.GONE);
//
//        showOrHideAiRadioMinusFeedback(false);
//        showOrHideCollect(false);
//
//        if (playerBarPrevious.getVisibility() != View.VISIBLE) {
//            playerBarPrevious.setVisibility(View.VISIBLE);
//        }
//        if (playerBarNext.getVisibility() != View.VISIBLE) {
//            playerBarNext.setVisibility(View.VISIBLE);
//        }
//
//        Drawable drawable = ResUtil.getDrawable(leftIconRid);
//        playerBarPrevious.setImageDrawable(drawable);
//
//        Drawable drawable2 = ResUtil.getDrawable(rightIconRid);
//        playerBarNext.setImageDrawable(drawable2);
//    }

    @Override
    public void showBroadcastState() {
        isLiveState = false;

//        setBroadcastIcons(R.drawable.playerbar_broadcast_prev, R.drawable.playerbar_broadcast_next);

        if (playerBarPlay.getVisibility() != View.VISIBLE) {
            playerBarPlay.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void showNormalState() {
        isLiveState = false;

        setButtonIcons(R.drawable.playerbar_collect_album, R.drawable.online_playerbar_prev, R.drawable.online_playerbar_next);

        if (playerBarPlay.getVisibility() != View.VISIBLE) {
            playerBarPlay.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void showLiveState() {
        isLiveState = true;
//        playerBarLivePlayRl.setVisibility(View.VISIBLE);
//        if (PlayerManager.getInstance().isPlaying()) {
//            playerBarLivePlayPause.setImageResource(R.drawable.ic_player_bar_pause_normal);
//        } else {
//            Drawable drawable = ResUtil.getDrawable(R.drawable.playerbar_play);
//            playerBarLivePlayPause.setImageDrawable(drawable);
//        }
        Log.e(TAG, "showLiveState设置隐藏");
        playerBarPlayRl.setVisibility(View.GONE);
    }


    @Override
    public void setPrevState(boolean enable) {
        playerBarPrevious.setActivated(enable);
        playerBarCollect.setActivated(enable);
    }

    @Override
    public void setNextState(boolean hasNext) {
        playerBarNext.setActivated(hasNext);
    }

    public void setPlayerAndPauseBtnState(boolean enabled) {
        playerBarPlay.setEnabled(enabled);
    }

    @Override
    public void setEnabled(boolean enabled) {
        super.setEnabled(enabled);
        setPlayerAndPauseBtnState(enabled);
        setPrevState(enabled);
        setNextState(enabled);
        updateProgress(0);
//        playerBarTitle.setText("");
        setTitle("");
    }

    @Override
    public void showToast(String info) {
        ToastUtil.showOnly(getContext(), info);
    }

    @Override
    public void showError(int strRid) {
        if (NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), true)) {
            KRadioToastInter radioToastInter = ClazzImplUtil.getInter("KRadioToastImpl");
            if (radioToastInter != null) {
                radioToastInter.showToast(0, getContext(), getResources().getString(strRid));
            } else {
                ToastUtil.showNormal(getContext(), strRid);
            }
        }
    }


    /*****************************************************************************************************/
    /*****************************************************************************************************/


    private void setPlayerUi(int whichPlayer) {
        switch (whichPlayer) {
//            case PlayerConstants.RESOURCES_TYPE_LIVING: {
//                Log.i("PlayStatus", "PLAYER_TYPE_LIVE");
//                showLiveState();
//            }
//            break;
//            case PlayerConstants.RESOURCES_TYPE_BROADCAST: {
//                Log.i("PlayStatus", "PLAYER_TYPE_BROADCAST");
//                showBroadcastState();
//            }
//            break;
            default: {
                Log.i("PlayStatus", "showNormalState");
                showNormalState();
            }
            break;
        }
    }

    // TODO: 2022/9/23 设置vip、精品、直播标签
    private void setFlag(@PlayerHelper.FlagType int type) {
        switch (type) {
            case PlayerHelper.FLAG_TYPE_NON:
                break;
            case PlayerHelper.FLAG_TYPE_LIVE:
                break;
            case PlayerHelper.FLAG_TYPE_PLAYBACK:
                break;
            default:
                break;
        }
    }

    /**
     * 为了在断网情况下,统一首页与播放器页面的订阅状态.
     *
     * @return
     */
    public boolean getSubscribeState() {
        return playerBarCollect.isSelected();
    }

    @NonNull
    @Override
    public Lifecycle getLifecycle() {
        return ((AppCompatActivity) getContext()).getLifecycle();
    }


    protected void updateLiveStreamSubTitle(PlayItem playItem) {
//        playerBarTitle.setText(playItem.getTitle());
//        ViewUtil.setViewVisibility(playerBarVerticalLine, View.GONE);
//        ViewUtil.setViewVisibility(playerBarTimeText, View.GONE);
        ViewUtil.setViewVisibility(playerBarSubTitleText, View.GONE);
        switchLiveState(playItem.getStatus());
    }

    protected void updateLiveSubTitle(PlayItem playItem) {
        setTitle(playItem.getTitle());
    }

    protected void updateSubTitle(PlayItem playItem) {
        if (!(playItem instanceof RadioPlayItem)) {
            return;
        }
        RadioPlayItem radioPlayItem = (RadioPlayItem) playItem;

//        playerBarTitle.setText(formatTitle(playItem));

        String time = radioPlayItem.getInfoData().getUpdateTime();
        String name = radioPlayItem.getRadioInfoData().getSubheadName();
        String subTag = radioPlayItem.getRadioInfoData().getRadioSubTag();

        StringBuilder sb = new StringBuilder();
        if (!StringUtil.isEmpty(subTag)) {
            String subTagTemp = formatSubTag(subTag);
            if (!StringUtil.isEmpty(subTagTemp)) {
                sb.append(subTagTemp);
//                ViewUtil.setViewVisibility(playerBarSubTagText, View.VISIBLE);
//                playerBarSubTagText.setText(subTagTemp);
            } else {
//                ViewUtil.setViewVisibility(playerBarSubTagText, View.GONE);
            }
        } else {
//            ViewUtil.setViewVisibility(playerBarSubTagText, View.GONE);
        }
        /**
         * 如果是台宣, 只显示标签, 不显示其他信息.
         */
        if (PlayerManagerHelper.getInstance().isRadioTaiXuan(playItem)) {
            setTitle(sb);
            return;
        }
//        if (TextUtils.isEmpty(time)
//                || TextUtils.isEmpty(name)) {
//            playerBarVerticalLine.setVisibility(View.GONE);
//        }
        if ((!TextUtils.isEmpty(time))
                && (!TextUtils.isEmpty(name))) {
            if (sb.length() > 0) {
                sb.append(" ");
            }
            if (!PlayerManagerHelper.getInstance().isRadioTaiXuan(playItem)) {
                sb.append(getAudioUpdateTime(getContext(), Long.parseLong(time)));
                sb.append(" | ");
                sb.append(name);
            } else {
                sb.append(name);
            }
        }

        if (TextUtils.isEmpty(name) && !TextUtils.isEmpty(time)) {
            if (sb.length() > 0) {
                sb.append(" ");
            }
            if (!PlayerManagerHelper.getInstance().isRadioTaiXuan(playItem)) {
                sb.append(getAudioUpdateTime(getContext(), Long.parseLong(time)));
            } else {
            }
        }

        if (TextUtils.isEmpty(time) && !TextUtils.isEmpty(name)) {
            if (sb.length() > 0) {
                sb.append(" ");
                sb.append(" | ");
            }
            sb.append(name);
        }
        setTitle(sb);
    }

    private void switchLiveState(int status) {
//        switch (status) {
//            case LiveStreamPlayItem.Living: {
//                ViewUtil.setViewVisibility(playerBarSubTagText, View.VISIBLE);
//                playerBarSubTagText.setText(R.string.state_living);
//                playerBarSubTagText.setTextColor(ResUtil.getColor(R.color.live_tag_bg_color));
//                playerBarSubTagText.setBackgroundResource(R.drawable.live_tag_bg);
//                setLivingFlag();
//            }
//            break;
//            case LiveStreamPlayItem.PlaybackAvailable: {
//                ViewUtil.setViewVisibility(playerBarSubTagText, View.VISIBLE);
//                playerBarSubTagText.setText(R.string.state_playback);
//                playerBarSubTagText.setTextColor(ResUtil.getColor(R.color.playback_tag_bg_color));
//                playerBarSubTagText.setBackgroundResource(R.drawable.playback_tag_bg);
//                setPlaybackFlag();
//            }
//            break;
//        }
    }

    private String formatSubTag(String subTag) {
        /**
         * 去掉半角空格
         */
        String subTemp = subTag.replace(" ", "");
        /**
         * 去掉全角空格
         */
        String subTemp1 = subTemp.replace("　", "").trim();

        if (StringUtil.isEmpty(subTemp1)) {
            return null;
        }
        int len = subTemp1.length();
        if (len <= MAX_SUB_TAG_LEN) {
            return subTemp1;
        }
        String tag = subTemp1.substring(0, MAX_SUB_TAG_LEN);
        return StringUtil.join(tag, SUB_TAG_REPLACE_STRING);
    }

    private String formatTitle(PlayItem playItem) {
        String title = playItem.getTitle();

        if (playItem instanceof RadioPlayItem) {
            title = ((RadioPlayItem) playItem).getRadioInfoData().getMainTitleName();

            if (PlayerManagerHelper.getInstance().isRadioTaiXuan(playItem)) {
                title = ((RadioPlayItem) playItem).getRadioInfoData().getSubheadName();
            }
        }

        return title;
    }

    public void skinUpdateBtn() {
        //          判断当前播放器的状态刷新播放条数据，同时重置一下状态
        if (PlayerManager.getInstance().isPlayerInitSuccess()) {
            updateInfo(1, PlayerManager.getInstance().getCurPlayItem());
            if (PlayerManagerHelper.getInstance().isPlaying()) {
                showPlayState();
            } else {
                showPauseState();
            }
        }
    }

    /**
     * 显示和隐藏负反馈
     *
     * @param showMinus
     */
    private void showOrHideAiRadioMinusFeedback(boolean showMinus) {
//        if (showMinus) {
//            ViewUtil.setViewVisibility(mAiRadioMinusFeedbackView, View.VISIBLE);
//        } else {
//            ViewUtil.setViewVisibility(mAiRadioMinusFeedbackView, View.GONE);
//        }
    }

    /**
     * 显示和隐藏收藏
     *
     * @param showCollect
     */
    public void showOrHideCollect(boolean showCollect) {
        boolean isDisplay = true;
        if (PlayerManager.getInstance().getPlayListInfo() != null) {
            isDisplay = PlayerManager.getInstance().getPlayListInfo().getNoSubscribe() != 1;
        }
        if (PlayerManagerHelper.getInstance().getCurrentPlayType() == PlayerConstants.RESOURCES_TYPE_LIVING) {
            isDisplay = false;
        }
        if (showCollect && isDisplay) {
            ViewUtil.setViewVisibility(playerBarCollect, View.VISIBLE);
        } else {
            ViewUtil.setViewVisibility(playerBarCollect, View.INVISIBLE);
        }
    }

    @Override
    protected void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        showAccordingToScreen(ResUtil.getOrientation());
    }

    @Override
    public void setProgressEnabled(boolean isEnabled) {
        ovalFlipSeekBar.setEnabled(isEnabled);
//        if (isEnabled) {
//            ViewUtil.setViewVisibility(playerBarProgress, View.VISIBLE);
//        } else {
//            ViewUtil.setViewVisibility(playerBarProgress, View.GONE);
//        }
    }

    @Override
    public void setProgressVisibility(boolean visible) {
        int visibility = visible ? View.VISIBLE : View.INVISIBLE;
        ovalFlipSeekBar.setVisibility(visibility);
    }

    @Override
    public void updateBroadcastErrorInfo() {
        int position = PlayerManagerHelper.getInstance().getCurrentFrequencyPosition();
        BroadcastRadioSimpleData broadcastRadioSimpleData = PlayerManagerHelper.getInstance().getBroadcastRadioSimpleDataByIndex(position);
        if (broadcastRadioSimpleData == null) {
            return;
        }
        String title = broadcastRadioSimpleData.getName();
        String audioPic = broadcastRadioSimpleData.getImg();
//        playerBarTitle.setText(title);
        setTitle(title);

        // 设置圆形图片
        ImageLoader.getInstance().displayImage(getContext(), audioPic, playerBarCover, ResUtil.getDrawable(R.drawable.media_default_pic));

        setPlayerUi(PlayerConstants.RESOURCES_TYPE_BROADCAST);
//        ViewUtil.setViewVisibility(playerBarSubTagText, View.GONE);
//
        int flagType = PlayerHelper.FLAG_TYPE_LIVE;
        setFlag(flagType);
    }

    @Override
    public void showPlayBarType() {
        PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
        if (playItem == null) {
            return;
        }
        int whichPlayer = playItem.getType();
        setPlayerUi(whichPlayer);
    }

    @Override
    public Activity getRootActivity() {
        return (Activity) mContext;
    }

    protected synchronized void showAccordingToScreen(int orientation) {

        Log.i("playerbar", "showAccordingToScreen");
    }

    /**
     * 上汽大通渠道需求，需要添加按钮返回桌面
     *
     * @param listener
     */
    public void setPlayerBarHomeListener(OnClickListener listener) {
//        if (playerBarHome != null) {
//            playerBarHome.setOnClickListener(listener);
//        } else {
//            Log.i(TAG, "playerBarHome:" + playerBarHome);
//        }
    }

    //  广播暂无广播资源，接受广播，并在此刷新播放条信息
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void playNextIsNotLiving(BroadcastPlayerChangedData broadcastPlayerChangedData) {
        updateInfo(1, PlayerManager.getInstance().getCurPlayItem());
        resetPlayerBarState();
    }

    private void resetPlayerBarState() {
        PlayerManagerHelper playerManagerHelper = PlayerManagerHelper.getInstance();
        Log.i(TAG, "Player: isPlaying = " + playerManagerHelper.isPlaying());
        Log.i(TAG, "Player: isPlayingClock = " + playerManagerHelper.isPlayingClock());
        if (playerManagerHelper.isPlaying() && !playerManagerHelper.isPlayingClock()) {
            showPlayState();
        } else {
            showPauseState();
        }
    }

    /**
     * 设置小电视图标是否可点击
     *
     * @param isEnabled
     */
    public void setPlayerBarFunEnable(boolean isEnabled) {
        playerBarFun.setEnabled(isEnabled);
        if (isEnabled) {
            playerBarFun.setColorFilter(Color.WHITE, PorterDuff.Mode.SRC_IN);
        } else {
            playerBarFun.setColorFilter(Color.parseColor("#5A617D"), PorterDuff.Mode.SRC_IN);
        }
    }

    public void setPlayerBarFunVisibility(boolean visibilityB) {
        int visibility = visibilityB ? View.VISIBLE : View.INVISIBLE;
        playerBarFun.setVisibility(visibility);
    }
}