package com.kaolafm.kradio.online.home;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.ColorDrawable;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.Parcelable;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.core.view.GravityCompat;
import androidx.drawerlayout.widget.DrawerLayout;
import android.telephony.PhoneStateListener;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.google.gson.Gson;
import com.kaolafm.ad.AdConstant;
import com.kaolafm.ad.KradioAdAudioManager;
import com.kaolafm.ad.api.model.AudioAdvert;
import com.kaolafm.ad.control.KradioAdSceneConstants;
import com.kaolafm.ad.expose.AdvertisingImager;
import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.ads.image.AdvertisingImagerImpl;
import com.kaolafm.kradio.basedb.GreenDaoInterface;
import com.kaolafm.kradio.basedb.entity.meaasge.CrashMessageBean;
import com.kaolafm.kradio.basedb.manager.MessageDaoManager;
import com.kaolafm.kradio.common.base.BaseApplication;
import com.kaolafm.kradio.common.base.BaseSkinAppCompatActivity;
import com.kaolafm.kradio.common.http.CommonRequestParamsUtil;
import com.kaolafm.kradio.common.report.ReportUtil;
import com.kaolafm.kradio.common.widget.OvalImageView;
import com.kaolafm.kradio.common.widget.ProgressImageView;
import com.kaolafm.kradio.constant.UserComponentConst;
import com.kaolafm.kradio.history.HistoryManager;
import com.kaolafm.kradio.k_kaolafm.BuildConfig;
import com.kaolafm.kradio.k_kaolafm.R; 
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.base.flavor.KRadioStopAudioInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioThirdPlatformInitInter;
import com.kaolafm.kradio.lib.base.ui.online.BaseShowHideFragment;
import com.kaolafm.kradio.lib.common.ModelConstant;
import com.kaolafm.kradio.lib.init.ModelManager;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.ComponentUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.KaolaAppConfigData;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.lib.widget.tab.OnTabSelectListener;
import com.kaolafm.kradio.lib.widget.tab.SlidingTabLayout;
import com.kaolafm.kradio.lib.widget.tab.Tab;
import com.kaolafm.kradio.media.MediaSessionUtil;
import com.kaolafm.kradio.online.categories.AllCategoriesFragment;
import com.kaolafm.kradio.online.common.event.CancelJumpToOnlinePlayerEvent;
import com.kaolafm.kradio.online.common.event.CloseDrawerEvent;
import com.kaolafm.kradio.online.common.event.JumpToOnlinePlayerEvent;
import com.kaolafm.kradio.online.common.event.OlineChangeBadgeViewEvent;
import com.kaolafm.kradio.online.common.event.OnlinePlayerFragmentJumpActionEvent;
import com.kaolafm.kradio.online.common.event.OnlineShowCityTabEvent;
import com.kaolafm.kradio.online.common.location.OnlineLocationGetAderssBean;
import com.kaolafm.kradio.online.common.location.OnlineLocationRequest;
import com.kaolafm.kradio.online.common.playbar.OnlinePlayerBar;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.router.IRouterConsumer;
import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.kradio.online.common.utils.AppDateUtils;
import com.kaolafm.message.utils.OnlineMessageUtils;
import com.kaolafm.mian_tab.bean.MainBean;
import com.kaolafm.kradio.online.home.listeningtrace.ListeningTraceFragment;
import com.kaolafm.kradio.online.home.location.OnlineLocationSelectFragment;
import com.kaolafm.kradio.online.home.mvp.MainPresenter;
import com.kaolafm.kradio.online.message.MessageBubbleDialogFragment;
import com.kaolafm.kradio.online.message.MessageDetailsDialogFragment;
import com.kaolafm.kradio.online.mine.login.OnlineLoginActivity;
import com.kaolafm.kradio.online.player.pages.OnlinePlayerFragment;
import com.kaolafm.kradio.online.player.utils.LogUtil;
import com.kaolafm.kradio.online.search.bean.SearchInfo;
import com.kaolafm.kradio.online.search.pages.OnlineSearchFragment;
import com.kaolafm.kradio.onlineactivity.ui.ActivitysDetailsDialogFragment;
import com.kaolafm.kradio.player.event.PlayerListChangedEBData;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.player.helper.intercept.HintInterceptManager;
import com.kaolafm.kradio.player.helper.intercept.HintVoiceChainIntercept;
import com.kaolafm.kradio.player.radiolive.LiveStateManager;
import com.kaolafm.kradio.purchase.PayManager;
import com.kaolafm.kradio.purchase.constant.PayConst;
import com.kaolafm.kradio.purchase.model.PayResult;
import com.kaolafm.kradio.purchase.observer.AlbumPayListener;
import com.kaolafm.kradio.purchase.observer.AudiosPayListener;
import com.kaolafm.kradio.purchase.observer.VipPayListener;
import com.kaolafm.kradio.scene.launcher.event.LocationEvent;
import com.kaolafm.kradio.user.UserInfoManager;
import com.kaolafm.mian_tab.IMainTabView;
import com.kaolafm.mian_tab.MainTabPresenter;
import com.kaolafm.opensdk.api.CrashMessageBaseBean;
import com.kaolafm.opensdk.api.activity.model.Activity;
import com.kaolafm.opensdk.api.login.LoginRequest;
import com.kaolafm.opensdk.api.login.model.CumPlaytimeInfo;
import com.kaolafm.opensdk.api.purchase.model.PurchaseSucess;
import com.kaolafm.opensdk.crash.CrashPlayerHelper;
import com.kaolafm.opensdk.crash.IcrashPlay;
import com.kaolafm.opensdk.crash.Icrashstate;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.IPlayerStateListener;
import com.kaolafm.opensdk.player.logic.model.item.InvalidPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.socket.SocketEvent;
import com.kaolafm.opensdk.socket.SocketListener;
import com.kaolafm.opensdk.socket.SocketManager;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonClickReportEvent;
import com.kaolafm.report.event.LoginReportEvent;
import com.kaolafm.report.util.ReportConstants;
import com.kaolafm.report.util.ReportParameterManager;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

import kotlin.jvm.functions.Function0;

/**
 * 在线电台-首页
 */
@Route(path = RouterConstance.ACTIVITY_URL_MAIN)
public class MainActivity extends BaseSkinAppCompatActivity<MainPresenter> implements IMainTabView, IRouterConsumer {
    private static final String TAG = MainActivity.class.getSimpleName();
 
    FrameLayout searchFrameLayout; 
    FrameLayout mainFrameLayout; 
    SlidingTabLayout mSrtlHomeNavigationTab; 
    ImageView onlineMainBgIv; 
    TextView onlineMainCityTv; 
    TextView onlineMainUserName; 
    TextView onlineMainUserDurationTv; 
    OvalImageView onlineMainUserPicIv; 
    ImageView onlineMainUserPicBgIv; 
    ImageView online_main_user_vip_iv; 
    LinearLayout onlineMainLoginLl; 
    ImageView onlineMianSearchIv; 
    ImageView badgeView; 
    DrawerLayout onlineMianDl; 
    OnlinePlayerBar playBar; 
    ProgressImageView roadIv; 
    RelativeLayout root_layout; 
    LinearLayout onlineHomeLocationLl; 
    ConstraintLayout msgFloatRoot; 
    View sub_class_view;//分类选择市级光波底部遮罩

    private MainBean bean;
    private boolean isFront = false;
    private OnlineSearchFragment onlineSearchFragment;
    private OnlineLocationSelectFragment onlineLocationSelectFragment;
    private Fragment currentFragment;//当前显示片段
    private Fragment homeFragment;//猜你喜欢
    private Fragment categoriesFragment;//分类
    private Fragment listeningTraceFragment;//听迹
    private Fragment activitysFragment;//活动
    private MessageBubbleDialogFragment dialogFragment;

    //在打开搜索页或者播放详情页之前记录事件上报的PageId，用于返回到首页时重置pageId
    private String pageIdWhenJump;

    private PlayItem mLastPlayItem;
    private AudiosPayListener mAudiosPayListener;
    private boolean slided = false;
    private int currentTab = 0;
    /**
     * 是否在Activity销毁时停止音频播放 true 为是，false 为否
     */
    private boolean canStopAudio = true;

    private static final int HANDLER_JUMP_PLAYER_ACTIVITY = 1688;
    /**
     * 跳转到播放页的时间
     */
    private final long JUMP_PLAYER_ACTIVITY_DELAY = 10_000;

    private SearchInfo searchInfo;
    //电台搜索的点击事件
    private View.OnClickListener radioSearchClickListener = new View.OnClickListener() {

        @Override
        public void onClick(View v) {
            if (!AntiShake.check(v.getId())) {
                if (onlineLocationSelectFragment != null) onlineLocationSelectFragment = null;
                onlineSearchFragment = new OnlineSearchFragment();
                if (searchInfo != null) {
                    onlineSearchFragment.setSearchWord(searchInfo);
                    searchInfo = null;
                }
                getSupportFragmentManager().beginTransaction().replace(R.id.searchFrameLayout, onlineSearchFragment).commitAllowingStateLoss();
                onlineMianDl.openDrawer(GravityCompat.END);
                ButtonClickReportEvent event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_SEARCH_PAGE);
                ReportHelper.getInstance().addEvent(event);
            }
        }
    };

    private Handler mHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            if (msg.what == HANDLER_JUMP_PLAYER_ACTIVITY) {
                jumpToPlayerActivity((long) msg.obj);
            }
        }
    };
    private Fragment playerFragment;    //播放页Fragment
    private String mTargetChildFragmentPageId;//子Fragment中要求选中的子Fragment的PageId

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        UserInfoManager.getInstance().addUserInfoStateListener(iUserInfoStateListener);
    }

    @Override
    protected MainPresenter createPresenter() {
        return new MainPresenter();
    }

    @Override
    public boolean isLauncherActivity() {
        return true;
    }

    @Override
    public boolean useEventBus() {
        return true;
    }

    @Override
    public int getLayoutId() {
        return R.layout.online_layout_main;
    }

    @Override
    protected void onPostResume() {
        super.onPostResume();
    }

    boolean isShowmsg = false;
    String json2 = "{\"emergencyId\":\"2111111111111111122\",\"eventDescription\":\"04-消息---没有秒    文    正方形\"" +
            ",\"eventDescriptionExtract\":\"脑残你看马上来\"" +
            ",\"eventDescriptionPath\":\"https://image.kaolafm.net/mz/audios/202206/2ce8192b-f795-40e9-8e72-3d7778fe05de.mp3\"," +
            "\"eventLevel\":\"1\",\"eventType\":\"1\",\"headline\":\"立即立即123123\"" +
            ",\"headlinePath\":\"https://image.kaolafm.net/mz/audios/202206/987efb5c-9f7c-4289-b242-1a21d1b2cdc3.mp3\"" +
            ",\"msgContentType\":\"0\"" +
            ",\"msgDetailsBgUrl\":\"http://img.kaolafm.net/mz/images/202209/f811ccb9-c009-450d-80da-0b8c9f0b97b9/default.jpg\"" +
            ",\"msgDetailsBtbStyleLeft\":1,\"msgDetailsBtbStyleRight\":1,\"msgDetailsBtnActionLeft\":\"查看\"" +
            ",\"msgDetailsBtnActionRight\":\"关闭\",\"msgDetailsBtnTextLeft\":\"查看\",\"msgDetailsBtnTextRight\":\"关闭\"" +
            ",\"msgDetailsEndTime\":\"2022-10-26 14:58:35\"" +
            ",\"msgDetailsQrUrl\":\"http://img.kaolafm.net/mz/images/202209/61bfd9d7-4a93-4b6f-b24f-15dd52e84b65/default.png\"" +
            ",\"msgDetailsStartTime\":\"2022-08-26 14:58:35\",\"msgId\":\"2111111111111111224\",\"msgLevel\":\"3\"" +
            ",\"msgStyleType\":2,\"playType\":\"0\",\"publishTime\":\"2022-04-26 14:58:35\",\"sendTime\":\"1663745901000\"" +
            ",\"sender\":\"正安县气象台\",\"tipsTitle\":\"我是再次测试啊\"}";

    private void addMsg(String json) {
        Log.d("插播json：", json);
        changeMessagePlay(json);
//        if (!isShowmsg) {
//            isShowmsg = true;
//            onlineMianDl.postDelayed(new Runnable() {
//                @Override
//                public void run() {
//                    runOnUiThread(new Runnable() {
//                        @Override
//                        public void run() {
//                            addMsg(json2);
//                        }
//                    });
//                }
//            }, 3000);
//        }
    }

    @Override
    public void initView(Bundle savedInstanceState) {

        searchFrameLayout = findViewById(R.id.searchFrameLayout);
        mainFrameLayout = findViewById(R.id.online_main_fl);
        mSrtlHomeNavigationTab = findViewById(R.id.online_home_navigation_tab);
        onlineMainBgIv = findViewById(R.id.online_main_bg_iv);
        onlineMainCityTv = findViewById(R.id.online_main_city_tv);
        onlineMainUserName = findViewById(R.id.online_main_user_name);
        onlineMainUserDurationTv = findViewById(R.id.online_main_user_duration_tv);
        onlineMainUserPicIv = findViewById(R.id.online_main_user_pic_iv);
        onlineMainUserPicBgIv = findViewById(R.id.online_main_user_pic_bg_iv);
        online_main_user_vip_iv = findViewById(R.id.online_main_user_vip_iv);
        onlineMainLoginLl = findViewById(R.id.online_main_login_ll);
        onlineMianSearchIv = findViewById(R.id.online_mian_search_iv);
        badgeView = findViewById(R.id.badgeView);
        onlineMianDl = findViewById(R.id.online_mian_dl);
        playBar = findViewById(R.id.pb_home_play);
        roadIv = findViewById(R.id.online_iv_main_road);
        root_layout = findViewById(R.id.root_layout);
        onlineHomeLocationLl = findViewById(R.id.online_home_location_ll);
        msgFloatRoot = findViewById(R.id.msgFloatRoot);
        sub_class_view = findViewById(R.id.sub_class_view);

        msgFloatRoot.setOnClickListener(v -> showMessageBubble( v));
         
        
        RouterManager.getInstance().addRouterConsumer(this);
        mPresenter.reportAppStart(getIntent());
        KRadioStopAudioInter mKRadioStopAudioInter = ClazzImplUtil
                .getInter("KRadioStopAudioImpl");
        if (mKRadioStopAudioInter != null) {
            canStopAudio = mKRadioStopAudioInter.isStopAudio(this);
        }
        onlineMianDl.setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_CLOSED);
        onlineMianDl.setScrimColor(ResUtil.getColor(R.color.history_clear_dialog_bg));
        onlineMianDl.post(new Runnable() {
            @Override
            public void run() {
                DrawerLayout.LayoutParams layoutParams = (DrawerLayout.LayoutParams) searchFrameLayout.getLayoutParams();
                layoutParams.width = onlineMianDl.getWidth();
                searchFrameLayout.setLayoutParams(layoutParams);
            }
        });
        searchFrameLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!AntiShake.check(v.getId()))
                    if (onlineMianDl.isDrawerOpen(Gravity.RIGHT)) {
                        onlineMianDl.closeDrawer(Gravity.RIGHT);
                    }
            }
        });
        onlineMianDl.addDrawerListener(new DrawerLayout.DrawerListener() {
            private boolean isOpenDraw = false;
            private AtomicBoolean isAnimating = new AtomicBoolean(false);

            @Override
            public void onDrawerSlide(@NonNull View drawerView, float slideOffset) {

            }

            @Override
            public void onDrawerOpened(@NonNull View drawerView) {
                resetAdImageShowParam(false);
                onlineMianDl.setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_OPEN);
                isOpenDraw = true;
                if (onlineSearchFragment != null && onlineSearchFragment.isVisible()) {
                    resetPageId(onlineSearchFragment.getPageId());
                } else if (onlineLocationSelectFragment != null && onlineLocationSelectFragment.isVisible()) {
                    resetPageId(onlineLocationSelectFragment.getPageId());
                }
                hideAdImage();
                isAnimating.set(false);
            }

            @Override
            public void onDrawerClosed(@NonNull View drawerView) {
                resetAdImageShowParam(currentFragment instanceof HomeFragment);
                onlineMianDl.setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_CLOSED);
                if (onlineSearchFragment != null && onlineSearchFragment.isVisible()) {
                    onlineSearchFragment.notifyReset();
                    onlineSearchFragment.onPause();
                }
                if (onlineLocationSelectFragment != null && onlineLocationSelectFragment.isVisible()) {
                    onlineLocationSelectFragment.onPause();
                }
                isOpenDraw = false;
                resetPageId(pageIdWhenJump);
                hideAdImage();
                isAnimating.set(false);
            }

            @Override
            public void onDrawerStateChanged(int newState) {
                if (isAnimating.compareAndSet(false, true) && !isOpenDraw && newState != DrawerLayout.STATE_IDLE) {
                    pageIdWhenJump = ReportParameterManager.getInstance().getPage();
                }
                //当状态改变时，意味着有页面操作，则取消10秒自动跳转
                EventBus.getDefault().post(new CancelJumpToOnlinePlayerEvent());
            }
        });
        AppManager.getInstance().setMainActivity(this);
        ModelManager.getInstance().setModel(ModelConstant.MODEL_ONLINE);
        initHintVoiceIntercept();
        onLoginEvent();
        new MainTabPresenter(this).loadDate();
        onlineMainLoginLl.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!AntiShake.check(v.getId())) {
                    RouterManager.getInstance().jumpPage(RouterConstance.ACTIVITY_URL_MINE);
                    ButtonClickReportEvent event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_MINE_PAGE);
                    ReportHelper.getInstance().addEvent(event);
                }
            }
        });
        onlineHomeLocationLl.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //定位
                if (!AntiShake.check(v.getId())) {
                    if (onlineSearchFragment != null) onlineSearchFragment = null;
                    onlineLocationSelectFragment = new OnlineLocationSelectFragment();
                    getSupportFragmentManager().beginTransaction().replace(R.id.searchFrameLayout, onlineLocationSelectFragment).commitAllowingStateLoss();
                    onlineMianDl.openDrawer(GravityCompat.END);

                    ButtonClickReportEvent event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_LOACION_PAGE);
                    ReportHelper.getInstance().addEvent(event);
                }
                String json = "{\"emergencyId\":\"1111111111111111122\",\"eventDescription\":\"04-消息---没有秒    文    正方形\"" +
                        ",\"eventDescriptionExtract\":\"没有秒\"" +
                        ",\"eventDescriptionPath\":\"https://image.kaolafm.net/mz/audios/202206/2ce8192b-f795-40e9-8e72-3d7778fe05de.mp3\"," +
                        "\"eventLevel\":\"1\",\"eventType\":\"1\",\"headline\":\"立即立即\"" +
                        ",\"headlinePath\":\"https://image.kaolafm.net/mz/audios/202206/987efb5c-9f7c-4289-b242-1a21d1b2cdc3.mp3\"" +
                        ",\"msgContentType\":\"0\"" +
                        ",\"cardBgUrl\":\"http://img.kaolafm.net/mz/images/202210/a91309f0-e813-47d6-ac27-9bf48851aba6/default.png\"" +
                        ",\"msgTipsPicUrl\":\"http://img.kaolafm.net/mz/images/202210/1b60b1bb-d3d3-4c0c-ae54-8ce27c9db8b7/default.png\"" +
                        ",\"msgDetailsBgUrl\":\"http://img.kaolafm.net/mz/images/202209/f811ccb9-c009-450d-80da-0b8c9f0b97b9/default.jpg\"" +
                        ",\"msgDetailsBtbStyleLeft\":1,\"msgDetailsBtbStyleRight\":1,\"msgDetailsBtnActionLeft\":\"查看\"" +
                        ",\"msgDetailsBtnActionRight\":\"关闭\",\"msgDetailsBtnTextLeft\":\"查看\",\"msgDetailsBtnTextRight\":\"关闭\"" +
                        ",\"msgDetailsEndTime\":\"2022-10-26 14:58:35\"" +
                        ",\"msgDetailsQrUrl\":\"http://img.kaolafm.net/mz/images/202209/61bfd9d7-4a93-4b6f-b24f-15dd52e84b65/default.png\"" +
                        ",\"msgDetailsStartTime\":\"2022-08-26 14:58:35\",\"msgId\":\"1111111111111111224\",\"msgLevel\":\"3\"" +
                        ",\"msgStyleType\":2,\"playType\":\"0\",\"publishTime\":\"2022-04-26 14:58:35\",\"sendTime\":\"1663745901000\"" +
                        ",\"sender\":\"正安县气象台\",\"tipsTitle\":\"立即立即\"}";
//                addMsg(json);
//                addMsg(json2);
//                onlineMianDl.postDelayed(new Runnable() {
//                    @Override
//                    public void run() {
//                        runOnUiThread(new Runnable() {
//                            @Override
//                            public void run() {
//                                addMsg(json);
//                            }
//                        });
//                    }
//                }, 3000);
            }
        });
        onlineMianSearchIv.setOnClickListener(radioSearchClickListener);

        mSrtlHomeNavigationTab.setTextBold(1);
        mSrtlHomeNavigationTab.setOnTabSelectListener(new OnTabSelectListener() {
            @Override
            public void onTabSelect(int position) {
                //切换tab时取消自动跳转
                EventBus.getDefault().post(new CancelJumpToOnlinePlayerEvent());

                roadIv.setVisibility(View.GONE);
                ButtonClickReportEvent event;
                Bundle bundle = new Bundle();
                switch (position) {
                    case 0://猜你喜欢
                        onlineMainBgIv.setImageResource(R.drawable.online_page_bg_clear);
                        roadIv.setVisibility(View.VISIBLE);
                        if (homeFragment == null) {
                            bundle.putString(BaseShowHideFragment.ARGUMENT_TAG, "猜你喜欢");
                            homeFragment = RouterManager.getInstance().getRouterFragment(RouterConstance.FRAGMENT_URL_HOME_LIKE, bundle);
                        }
                        if (((HomeFragment) homeFragment).mErrorRl != null && ((HomeFragment) homeFragment).mErrorRl.getVisibility() == View.VISIBLE) {

                        } else {
                            roadIv.bringToFront();
                        }
                        changePage(homeFragment);
                        EventBus.getDefault().post(new OnlineShowCityTabEvent(false));
                        event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_HOME_PAGE);
                        ReportHelper.getInstance().addEvent(event);
                        break;
                    case 1://分类
                        onlineMainBgIv.setImageResource(R.drawable.online_page_bg_vague);
                        if (categoriesFragment == null) {
                            bundle.putString(BaseShowHideFragment.ARGUMENT_TAG, "分类");
                            categoriesFragment = RouterManager.getInstance().getRouterFragment(RouterConstance.FRAGMENT_URL_CATEGORIES, bundle);
                        }
                        changePage(categoriesFragment);
                        if (categoriesFragment instanceof AllCategoriesFragment && mTargetChildFragmentPageId != null) {
                            ((AllCategoriesFragment) categoriesFragment).selectChildFragment(mTargetChildFragmentPageId);
                            mTargetChildFragmentPageId = null;
                        }
                        EventBus.getDefault().post(new OnlineShowCityTabEvent(false));
                        event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_CLASS_PAGE);
                        ReportHelper.getInstance().addEvent(event);
                        break;
                    case 2://听迹  需要每次重新创建
                        onlineMainBgIv.setImageResource(R.drawable.online_page_bg_vague);
                        bundle.putString(BaseShowHideFragment.ARGUMENT_TAG, "听迹");
                        listeningTraceFragment = RouterManager.getInstance().getRouterFragment(RouterConstance.FRAGMENT_URL_LISTENING_TRACE, bundle);
                        changePage(listeningTraceFragment);
                        if (listeningTraceFragment instanceof ListeningTraceFragment && mTargetChildFragmentPageId != null) {
                            ((ListeningTraceFragment) listeningTraceFragment).selectChildFragment(mTargetChildFragmentPageId);
                            mTargetChildFragmentPageId = null;
                        }
                        EventBus.getDefault().post(new OnlineShowCityTabEvent(false));
                        event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_TJ_PAGE);
                        ReportHelper.getInstance().addEvent(event);
                        break;
                    case 3://活动
                        onlineMainBgIv.setImageResource(R.drawable.online_page_bg_vague);
                        onlineMainBgIv.setImageResource(R.drawable.online_page_bg_vague);
                        if (activitysFragment == null) {
                            bundle.putString(BaseShowHideFragment.ARGUMENT_TAG, "活动");
                            activitysFragment = RouterManager.getInstance().getRouterFragment(RouterConstance.FRAGMENT_URL_ACTIVITYS, bundle);
                        }
                        changePage(activitysFragment);
                        EventBus.getDefault().post(new OnlineShowCityTabEvent(false));
                        event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_ACTIVITY_PAGE);
                        ReportHelper.getInstance().addEvent(event);
                        break;
                }
                mSrtlHomeNavigationTab.getTab(position).tabView.setText(
                        AppDateUtils.getInstance().getRadiusGradientSpan(MainActivity.this
                                , mSrtlHomeNavigationTab.getTab(position).title));
                mSrtlHomeNavigationTab.getTab(position).tabView.getPaint().setFakeBoldText(true);

                mSrtlHomeNavigationTab.getTab(currentTab).tabView.setText(mSrtlHomeNavigationTab.getTab(currentTab).title);
                mSrtlHomeNavigationTab.getTab(currentTab).tabView.getPaint().setFakeBoldText(false);
                mSrtlHomeNavigationTab.getTab(currentTab).tabView.setTextColor(ResUtil.getColor(R.color.online_main_tab_text_color));
                playBar.bringToFront();
                msgFloatRoot.bringToFront();
                currentTab = position;
            }

            @Override
            public void onTabReselect(int position) {
                switch (position) {
                    case 1:
                        if (categoriesFragment instanceof AllCategoriesFragment && mTargetChildFragmentPageId != null) {
                            ((AllCategoriesFragment) categoriesFragment).selectChildFragment(mTargetChildFragmentPageId);
                            mTargetChildFragmentPageId = null;
                        }
                        break;
                    case 2:
                        if (listeningTraceFragment instanceof ListeningTraceFragment && mTargetChildFragmentPageId != null) {
                            ((ListeningTraceFragment) listeningTraceFragment).selectChildFragment(mTargetChildFragmentPageId);
                            mTargetChildFragmentPageId = null;
                        }
                        break;
                }
            }
        });
        sub_class_view.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                EventBus.getDefault().post(new OnlineShowCityTabEvent(false));
            }
        });
        sendMessageSocket();
        startAnim();

        //应用内跳转处理
        interceptApplicationJumpEvent(null);
    }


    @Subscribe
    public void showClassView(OnlineShowCityTabEvent showCityTabEvent) {
        if (sub_class_view == null) {
            return;
        }
        if (currentTab == 1 && showCityTabEvent.isShow()) {
            sub_class_view.setVisibility(View.VISIBLE);
        } else {
            sub_class_view.setVisibility(View.GONE);
        }
    }

    /**
     * 付费
     */
    private void initHintVoiceIntercept() {
        HintInterceptManager hintInterceptManager = HintInterceptManager.getInstance();
        hintInterceptManager.addOnCurrentPlayItemIntercept(new HintInterceptManager.OnCurrentPlayItemIntercept() {
            @Override
            public void getHintInterceptState(PlayItem playItem, int hintType, int buyType, int buyStatus, boolean isUserLogin) {
                Log.d("123456", "getHintInterceptState");
                Log.i(TAG, "getHintInterceptState :" + playItem.getBuyStatus());
                HistoryManager.getInstance().saveHistory(playItem, true);
                if (!isUserLogin && topIsLoginPage()) {
                    return;
                }

                //每次都添加新的listener但旧的listener没有移除，会导致重复回调
                final boolean[] disposePayResult = {false};
                jumpToPlayerFragmentAndPay(playItem, hintType, buyType, buyStatus, isUserLogin, new AlbumPayListener() {
                    @Override
                    public void payResponse(PayResult payResult, PlayItem resultItem) {
                        hintInterceptManager.notifyPaySuccess();
                        if (playItem != resultItem) {
                            PayManager.getInstance().removeItemListeners(playItem);
                        }
                        //专辑购买需要刷新专辑列表，并播放当前条目
                        PlayerManagerHelper.getInstance().restart(playItem.getAlbumId(), playItem.getType());
                    }
                }, new AudiosPayListener() {
                    @Override
                    public void payResponse(PayResult payResult, PlayItem resultItem, String audioIds) {
                        if (!disposePayResult[0]) {
                            hintInterceptManager.notifyPaySuccess();
                            Integer status = payResult.getPurchaseSucess().getStatus();
                            if (status == PurchaseSucess.STATUS_SUCCESS) {
                                onAudioPayed(audioIds);
                            }
                            disposePayResult[0] = true; //更新为已执行的状态
                            PayManager.getInstance().removeItemSingleListener(playItem, this); //回调完成移除回调
                        }
                    }
                }, new VipPayListener() {
                    @Override
                    public void payResponse(PayResult payResult, PlayItem resultItem, Long vipTime) {
                        Log.i(TAG, "VIP PAY SUCCESS !");
                        hintInterceptManager.notifyPaySuccess();
                        PlayerManagerHelper.getInstance().refreshPlayList();
                    }
                });
            }
        });
    }

    private void jumpToPlayerFragmentAndPay(PlayItem playItem, int hintType, int buyType, int buyStatus, boolean isUserLogin, AlbumPayListener albumPayListener, AudiosPayListener audiosPayListener, VipPayListener vipPayListener) {
        if (!isUserLogin) {
            if (playBar != null) {
                if (((HomeFragment) homeFragment).mRing != null) {
                    ((HomeFragment) homeFragment).mRing.setTouchEnable(false);
                }
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        jumpToPlayerActivity();
                    }
                }, 1000);
            }
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    Bundle bundle = new Bundle();
                    if (onlineSearchFragment != null && onlineSearchFragment.isVisible()) {
                        //点击搜索触发
                        bundle.putString("type", LoginReportEvent.ONLINE_REMARKS1_SEARCH_LOGIN);
                    } else {
                        bundle.putString("type", LoginReportEvent.ONLINE_REMARKS1_PLAY_CAROUSEL);
                    }
                    RouterManager.getInstance().jumpPage(RouterConstance.ACTIVITY_URL_LOGIN, bundle);
                }
            }, 1500);
        } else {
            if (playBar != null) {
                if (((HomeFragment) homeFragment).mRing != null) {
                    ((HomeFragment) homeFragment).mRing.setTouchEnable(false);
                }
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        jumpToPlayerActivity();
                    }
                }, 1000);
            }
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    // 调用支付相关弹窗逻辑，并监听支付回调
                    switch (buyType) {
                        case HintVoiceChainIntercept.TYPE_ALBUM: //专辑购买
                            final boolean[] disposeAlbumPayResult = {false};
                            PayManager.getInstance()
                                    .pay(PayConst.PAY_TYPE_ALBUM, playItem)
                                    .addPayListener(playItem, albumPayListener);
                            break;
                        case HintVoiceChainIntercept.TYPE_AUDIO: //碎片购买
                            if (mLastPlayItem != null && mAudiosPayListener != null) {
                                PayManager.getInstance().removeItemSingleListener(mLastPlayItem, mAudiosPayListener);
                            }
                            mLastPlayItem = playItem;
                            PayManager.getInstance()
                                    .pay(PayConst.PAY_TYPE_AUDIOS, playItem)
                                    .addPayListener(playItem, mAudiosPayListener = audiosPayListener);
                            break;
                        case HintVoiceChainIntercept.TYPE_VIP: //vip购买
                            PayManager.getInstance()
                                    .pay(PayConst.PAY_TYPE_VIP, playItem)
                                    .addPayListener(null, vipPayListener);
                            break;
                        default:
                            break;
                    }
                }
            }, 1500);
        }
    }

    private boolean topIsLoginPage() {
        return AppManager.getInstance().getCurrentActivity() instanceof OnlineLoginActivity;
    }

    /**
     * 碎片支付成功后的播放处理
     *
     * @param audioIds
     */
    private void onAudioPayed(String audioIds) {
        Log.i(TAG, "onAudioPayed audioIds:" + audioIds);
        List<Long> idList = PlayerManagerHelper.getInstance().convertAudioIdsToItemIdsList(audioIds);
        if (idList != null && idList.size() > 0) {
            long firstPayedAudioId = idList.get(0);
            PlayerManager.getInstance().getPlayItemFromAudioId(firstPayedAudioId, new PlayerManager.GetPlayItemListener() {
                @Override
                public void success(PlayItem playItem) {
                    Log.i(TAG, "onAudioPayed save history for next play:" + playItem.getRadioName());
                    HistoryManager.getInstance().saveHistory(playItem, true);
                    PlayerManagerHelper.getInstance().restart(playItem.getAlbumId(), playItem.getType());
                }

                @Override
                public void error(ApiException e) {
                    e.printStackTrace();
                }
            });
        }
    }

    private void sendMessageSocket() {
        /**
         * 添加连续播放监听
         */
        CrashPlayerHelper.getInstance().setIcrashPlay(new IcrashPlay() {
            @Override
            public void onPlay(CrashMessageBaseBean crashMessageBaseBean) {
                if (crashMessageBaseBean == null) {
                    return;
                }
                String json = new Gson().toJson(crashMessageBaseBean);
                CrashMessageBean crashMessageBean = new Gson().fromJson(json, CrashMessageBean.class);
                Log.i("sendMessageSocket", "--------插播连续播放----" + json);
                if (AppDateUtils.getInstance().isConfirmPlayMsg(crashMessageBean)) {
                    CrashPlayerHelper.getInstance().startPlay();
                } else {
                    //语音播报开关被关闭需要播放提示音
                    CrashPlayerHelper.getInstance()
                            .removeImmediatelDate()//移除这一条
                            .playTips(getString(R.string.online_message_voice_url));
                }
                showMessageDialog(crashMessageBean);
            }
        });
        SocketListener mSocketListener = new SocketListener<CrashMessageBaseBean>() {

            @Override
            public String getEvent() {
                return SocketEvent.APP_MESSAGE;
            }

            @Override
            public Map<String, Object> getParams(Map<String, Object> params) {
                return params;
            }

            @Override
            public boolean isNeedParams() {
                return true;
            }

            @Override
            public boolean isNeedRequest() {
                return true;
            }

            @Override
            public void onSuccess(CrashMessageBaseBean baseBeans) {
                String json = new Gson().toJson(baseBeans);
                Log.d("sendMessageSocket", "--------插播接口成功-----" + json);
                changeMessagePlay(json);
            }

            @Override
            public void onError(ApiException e) {
                LogUtil.e("sendMessageSocket", e.getMessage());
            }
        };

        SocketManager.getInstance().setMap(CommonRequestParamsUtil.getCommonParams()).setSocketHost(SocketApiConstants.SOCKET_HOST).request(mSocketListener);
    }

    private PhoneStateListener phoneStateListener;

    private boolean isCalling() {
        boolean b = false;
        TelephonyManager telephonyManager = (TelephonyManager) getSystemService(Context.TELEPHONY_SERVICE);
        if (TelephonyManager.CALL_STATE_OFFHOOK == telephonyManager.getCallState()
                || TelephonyManager.CALL_STATE_RINGING == telephonyManager.getCallState()) {
            //正在通话
            b = true;
            callingInMessage(telephonyManager);
        }
        return b;
    }

    /**
     * 处理接受插播消息的逻辑
     */
    private synchronized void changeMessagePlay(String json) {

        CrashMessageBean crashMessageBean = new Gson().fromJson(json, CrashMessageBean.class);
        MessageDaoManager.getInstance().save(crashMessageBean);

        //插播进入，如果在播放节目切换广告，则关闭
        AudioAdvert audioAdvert = KradioAdAudioManager.getInstance().getAudioAdvert();
        if (audioAdvert != null &&
                (audioAdvert.getSubtype() == KradioAdSceneConstants.SUB_TYPE_SWITCH_PROGROM ||
                        audioAdvert.getSubtype() == AdConstant.TYPE_TIMED_ADVERT)) {
            AdvertisingManager.getInstance().close();
        }

        switch (crashMessageBean.getPlayType()) {
            case "0"://立即插播
                if (AppDateUtils.getInstance().isConfirmPlayMsg(crashMessageBean)) {
                    if (isCalling()) {
                        //正在通话
                        CrashPlayerHelper.getInstance()
                                .addImmediatelyplayDate(new Gson().fromJson(new Gson().toJson(crashMessageBean), CrashMessageBaseBean.class));
                    } else {
                        if (CrashPlayerHelper.getInstance().isPlayCrash) {
                            CrashPlayerHelper.getInstance()
                                    .addImmediatelyplayDate(new Gson().fromJson(new Gson().toJson(crashMessageBean), CrashMessageBaseBean.class));
                        } else {
                            CrashPlayerHelper.getInstance()
                                    .addImmediatelyplayDate(new Gson().fromJson(new Gson().toJson(crashMessageBean), CrashMessageBaseBean.class))
                                    .setIcrashstate(new Icrashstate() {
                                        @Override
                                        public void onCrashstate(int i) {

                                        }

                                        @Override
                                        public void onBufferingUpdate(CrashMessageBaseBean crashMessageBaseBean) {
                                            if (OnlineMessageUtils.getInstance().getShowMsgDetails() || crashMessageBaseBean == null) {
                                                //如果是从消息详情点击的播放就不展示弹窗
                                                return;
                                            }
                                            String json = new Gson().toJson(crashMessageBaseBean);
                                            CrashMessageBean crashMessageBean = new Gson().fromJson(json, CrashMessageBean.class);
                                            showMessageDialog(crashMessageBean);
                                        }
                                    })
                                    .startPlay();
                        }
                    }
                } else {
                    //语音播报开关被关闭需要播放提示音
                    CrashPlayerHelper.getInstance()
                            .setIcrashstate(new Icrashstate() {
                                @Override
                                public void onCrashstate(int i) {

                                }

                                @Override
                                public void onBufferingUpdate(CrashMessageBaseBean crashMessageBaseBean) {
//                                    if (crashMessageBaseBean == null) {
//                                        return;
//                                    }
//                                    String json = new Gson().toJson(crashMessageBaseBean);
//                                    CrashMessageBean crashMessageBean = new Gson().fromJson(json, CrashMessageBean.class);
//                                    showMessageDialog(crashMessageBean);
                                }
                            })
                            .playTips(getString(R.string.online_message_voice_url));
                    showMessageDialog(crashMessageBean);
                }
                break;
            case "1"://延时插播
                CrashPlayerHelper.getInstance()
                        .addPlayDate(new Gson().fromJson(new Gson().toJson(crashMessageBean), CrashMessageBaseBean.class));
                if (AppDateUtils.getInstance().isConfirmPlayMsg(crashMessageBean)) {
                    if (!isCalling()) {
                        //未在通话
                        if (!PlayerManager.getInstance().isPlaying()) {
                            //如果没有在播放中，并且插播未在播放中，就直接播放延时插播
                            if (!CrashPlayerHelper.getInstance().isPlayCrash) {
                                CrashPlayerHelper.getInstance().startPlay();
                                showMessageDialog(crashMessageBean);
                            }
                        }
                    }
                } else {
                    //语音播报开关被关闭需要播放提示音
                    if (!PlayerManager.getInstance().isPlaying()) {
                        CrashPlayerHelper.getInstance().playTips(getString(R.string.online_message_voice_url));
                        showMessageDialog(crashMessageBean);
                    }
                }

                break;
            case "2"://普通消息
                showMessageDialog(crashMessageBean);
                break;
        }
        changeBadgeView();
    }

    /**
     * 电话状态监听
     */
    private void callingInMessage(TelephonyManager telephonyManager) {
        if (phoneStateListener == null) {
            phoneStateListener = new PhoneStateListener() {
                @Override
                public void onCallStateChanged(int state, String phoneNumber) {
                    super.onCallStateChanged(state, phoneNumber);
                    switch (state) {
                        case TelephonyManager.CALL_STATE_IDLE: {
                            Log.e(TAG, "CALL_STATE_IDLE");
                            if (CrashPlayerHelper.getInstance().isImmediatePlayDate()) {
                                CrashPlayerHelper.getInstance()
                                        .setIcrashstate(new Icrashstate() {
                                            @Override
                                            public void onCrashstate(int i) {

                                            }

                                            @Override
                                            public void onBufferingUpdate(CrashMessageBaseBean crashMessageBaseBean) {
                                                if (crashMessageBaseBean == null) {
                                                    return;
                                                }
                                                String json = new Gson().toJson(CrashPlayerHelper.getInstance().getCrashMessageBaseBean());
                                                Log.i("sendMessageSocket", "--------通话结束，插播播放-----" + json);
                                                CrashMessageBean crashMessageBean = new Gson().fromJson(json, CrashMessageBean.class);
                                                showMessageDialog(crashMessageBean);
                                            }

                                        })
                                        .startPlay();

                            }
                            break;
                        }
                        case TelephonyManager.CALL_STATE_OFFHOOK: {
                            Log.e(TAG, "CALL_STATE_OFFHOOK");

                            break;
                        }
                        case TelephonyManager.CALL_STATE_RINGING: {
                            Log.e(TAG, "CALL_STATE_RINGING");
                            break;
                        }
                        default:
                            break;
                    }
                }
            };
            telephonyManager.listen(phoneStateListener, PhoneStateListener.LISTEN_CALL_STATE);
        }
    }

    @Subscribe
    public void changeBadgeView(OlineChangeBadgeViewEvent event) {
        changeBadgeView();
    }

    /**
     * 更新小红点
     */
    private void changeBadgeView() {
        badgeView.postDelayed(new Runnable() {
            @Override
            public void run() {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        MessageDaoManager.getInstance().queryMsgLook(new GreenDaoInterface.OnQueryListener<List<CrashMessageBean>>() {
                            @Override
                            public void onQuery(List<CrashMessageBean> crashMessageBeans) {
                                if (crashMessageBeans != null && crashMessageBeans.size() > 0) {
                                    badgeView.setVisibility(View.VISIBLE);
                                } else {
                                    badgeView.setVisibility(View.GONE);
                                }
                            }
                        });
                    }
                });
            }
        }, 300);
    }

    private void startAnim() {
        if (BuildConfig.IS_PLAY_ANIM) {
            roadIv.bringToFront();
            roadIv.setImgRes(R.drawable.online_road_clip)
                    .setAnimFinishCallback(new ProgressImageView.AnimFinishCallback() {
                        @Override
                        public void onFinish() {
                            loadHome();
                        }
                    })
                    .startAnim();
        } else {
            roadIv.bringToFront();
            roadIv.setImgRes(R.drawable.online_road_clip)
                    .setAnimFinishCallback(new ProgressImageView.AnimFinishCallback() {
                        @Override
                        public void onFinish() {
                            loadHome();
                        }
                    })
                    .startAnim(0);
        }
    }

    private void loadHome() {
        if (currentTab == 0) {
            playBar.bringToFront();
            playBar.setVisibility(View.VISIBLE);
            //加载首页
            if (homeFragment == null) {
                Bundle bundle = new Bundle();
                bundle.putString(BaseShowHideFragment.ARGUMENT_TAG, "猜你喜欢");
                homeFragment = RouterManager.getInstance().getRouterFragment(RouterConstance.FRAGMENT_URL_HOME_LIKE, bundle);
            }
            changePage(homeFragment);
        }
    }

    /**
     * 切换tab
     *
     * @param fragment
     */
    @Subscribe
    public void changePage(Fragment fragment) {
        if (currentFragment == fragment && currentFragment != null && currentFragment.isVisible())
            return;
        hideAdImage();
        //重置参数
        resetAdImageShowParam(fragment instanceof HomeFragment);

        if (fragment instanceof AllCategoriesFragment) {
            mSrtlHomeNavigationTab.setCurrentTab(1);
        }
//        loadRootFragment(R.id.online_main_fl,
//                iSupportFragment,
//                false, false);
        FragmentTransaction fragmentTransaction = getSupportFragmentManager().beginTransaction();
        if (!fragment.isAdded()) {
            fragmentTransaction.add(R.id.online_main_fl, fragment);
        }
        if (currentFragment != null) {
            fragmentTransaction.hide(currentFragment).show(fragment).commit();
        } else {
            fragmentTransaction.show(fragment).commit();
        }
        currentFragment = fragment;
    }

    private void hideCurrentFragment() {
        if (currentFragment == null) return;
        FragmentTransaction fragmentTransaction = getSupportFragmentManager().beginTransaction();
        fragmentTransaction.hide(currentFragment).commit();
    }

    /**
     * 重置参数：能否显示广告主图
     * 需求来自：在线电台，只在猜你喜欢、专辑/AI电台播放页、广播/听电视播放页显示广告主图，且切换页面时隐藏广告主图
     *
     * @param isCanShowAdImage
     */
    private void resetAdImageShowParam(boolean isCanShowAdImage) {
        AdvertisingImager advertisingImager = AdvertisingManager.getInstance().getImager();
        if (advertisingImager != null && advertisingImager instanceof AdvertisingImagerImpl) {
            ((AdvertisingImagerImpl) advertisingImager).setCanShowAdImage(isCanShowAdImage);
        }
    }

    @Override
    public void initData() {
//        InitService.getLocation();//速度很慢
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public synchronized void locationEvent(LocationEvent locationEvent) {
        if (!TextUtils.isEmpty(KaolaAppConfigData.getInstance().getCityName())) {
            return;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("lat", KaolaAppConfigData.getInstance().getLat());
        map.put("lng", KaolaAppConfigData.getInstance().getLng());
        new OnlineLocationRequest().getAdress(map, new HttpCallback<OnlineLocationGetAderssBean>() {
            @Override
            public void onSuccess(OnlineLocationGetAderssBean onlineLocationGetAderssBean) {
                KaolaAppConfigData.getInstance().setCityName(onlineLocationGetAderssBean.city);
                if (!TextUtils.isEmpty(KaolaAppConfigData.getInstance().getCityName()) && onlineMainCityTv != null) {
                    onlineMainCityTv.setText(onlineLocationGetAderssBean.city);
                }
            }

            @Override
            public void onError(ApiException e) {

            }
        });

    }


    UserInfoManager.IUserInfoStateListener iUserInfoStateListener = new UserInfoManager.IUserInfoStateListener() {
        @Override
        public void userLogin() {
            onLoginEvent();
        }

        @Override
        public void userLogout() {
            onLoginEvent();
        }

        @Override
        public void userCancel() {

        }
    };

    public void onLoginEvent() {
        if (UserInfoManager.getInstance().isUserLogin()) {
            onlineMainUserName.setText(UserInfoManager.getInstance().getUserNickName());
            onlineMainUserPicIv.setVisibility(View.VISIBLE);
            ImageLoader.getInstance().displayImage(this, UserInfoManager.getInstance().getUserFavicon(), onlineMainUserPicIv);
            onlineMainUserDurationTv.setVisibility(View.VISIBLE);
            onlineMainUserPicBgIv.setImageResource(R.drawable.online_user_pic_bg);
            if (UserInfoManager.getInstance().getVip() == 1) {
                online_main_user_vip_iv.setVisibility(View.VISIBLE);
            } else {
                online_main_user_vip_iv.setVisibility(View.INVISIBLE);
            }

        } else {
            onlineMainUserName.setText("登录");
            onlineMainUserPicIv.setImageResource(0);
            onlineMainUserPicIv.setVisibility(View.GONE);
            onlineMainUserDurationTv.setVisibility(View.GONE);
            onlineMainUserPicBgIv.setImageDrawable(ResUtil.getDrawable(R.drawable.online_user_no_login_icon));
            online_main_user_vip_iv.setVisibility(View.INVISIBLE);
        }
        setCumPlaytime(UserInfoManager.getInstance().isUserLogin());
    }

    @Override
    protected void onStart() {
        super.onStart();
        isFront = true;
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (onlineMianDl != null)
            onlineMianDl.setScrimColor(ResUtil.getColor(R.color.history_clear_dialog_bg));
        List<CrashMessageBean> messageBeanList = MessageDaoManager.getInstance().queryAllSync();
        if (messageBeanList != null && messageBeanList.size() > 0) {
            try {
                for (CrashMessageBean messageBean : messageBeanList) {
                    if ((System.currentTimeMillis() - Long.parseLong(messageBean.getSendTime())) >= (1000 * 60 * 60 * 24)) {
                        //如果消息超过24小时就要删除0
                        MessageDaoManager.getInstance().delete(messageBean.getMsgId());
                        Log.d(TAG, "-----插播消息超过24小时删除----" + messageBean.getHeadline());
                    }
                }
            } catch (Exception e) {
                LogUtil.e(e.getMessage());
            }
        }
        changeBadgeView();
        if (UserInfoManager.getInstance().isUserLogin()) {
            ImageLoader.getInstance().displayCircleImage(this, UserInfoManager.getInstance().getUserInfo().getAvatar()
                    , onlineMainUserPicIv, ResUtil.getDrawable(R.drawable.online_user_no_login_icon));
            String name = UserInfoManager.getInstance().getUserInfo().getNickName();
            onlineMainUserName.setText(TextUtils.isEmpty(name) ? "已登录" : name);
            setCumPlaytime(true);
        } else {
            onlineMainUserName.setText("登录");
            onlineMainUserPicIv.setImageDrawable(new ColorDrawable());
            onlineMainUserPicIv.setVisibility(View.GONE);
            onlineMainUserDurationTv.setVisibility(View.GONE);
            onlineMainUserPicBgIv.setImageDrawable(ResUtil.getDrawable(R.drawable.online_user_no_login_icon));
            online_main_user_vip_iv.setVisibility(View.INVISIBLE);
        }

        mSrtlHomeNavigationTab.getTab(currentTab).tabView.setText(
                AppDateUtils.getInstance().getRadiusGradientSpan(MainActivity.this
                        , mSrtlHomeNavigationTab.getTab(currentTab).title));
        mSrtlHomeNavigationTab.getTab(currentTab).tabView.getPaint().setFakeBoldText(true);

        if (!TextUtils.isEmpty(KaolaAppConfigData.getInstance().getCityName())) {
            if (onlineMainCityTv != null) {
                onlineMainCityTv.setText(KaolaAppConfigData.getInstance().getCityName());
            }
        } else {
//            List<String> tags = OnlineLocationSearchHistoryManager.getInstance().getRecentSearchTags();
//            List<OnlineRecomandCityBean> beanList = new ArrayList<>();
//            OnlineRecomandCityBean bean;
//            Gson gson = new Gson();
//            for (String tag : tags) {
//                bean = gson.fromJson(tag, OnlineRecomandCityBean.class);
//                beanList.add(bean);
//            }
//            if (beanList.size() > 0) {
//                KaolaAppConfigData.getInstance().setCityName(beanList.get(0).city);
//                KaolaAppConfigData.getInstance().setLat(beanList.get(0).lat);
//                KaolaAppConfigData.getInstance().setLng(beanList.get(0).lng);
//                if (onlineMainCityTv != null)
//                    onlineMainCityTv.setText(KaolaAppConfigData.getInstance().getCityName());
//            }
        }
    }

    /**
     * 设置用户收听时长
     *
     * @param isLogin
     */
    private void setCumPlaytime(boolean isLogin) {
        if (isLogin) {
            new LoginRequest().getCumPlaytime(UserInfoManager.getInstance().getUserId()
                    , KaolaAppConfigData.getInstance().getAppId(), new HttpCallback<CumPlaytimeInfo>() {
                        @SuppressLint("SetTextI18n")
                        @Override
                        public void onSuccess(CumPlaytimeInfo cumPlaytimeInfo) {
                            if (cumPlaytimeInfo.getUid() != null)
                                onlineMainUserDurationTv.setText("收听时长 "
                                        + AppDateUtils.getInstance().userDurationFormat(cumPlaytimeInfo.getCumulativeDuration()) + "h");
                        }

                        @Override
                        public void onError(ApiException e) {
                            Log.e(TAG, e.getMessage());
                        }
                    });
        } else {
            onlineMainUserDurationTv.setText("收听时长 0h");
        }
    }

    @Override
    protected void onPause() {
        hideAdImage();
        super.onPause();
    }

    /**
     * 隐藏图片广告
     */
    private void hideAdImage() {
        //页面操作时，隐藏图片广告
        AdvertisingImagerImpl imager = (AdvertisingImagerImpl) AdvertisingManager.getInstance().getImager();
        if (imager != null && imager.isAdViewShowing()) {
            imager.hide(null);
        }
    }

    @Override
    protected void onDestroy() {
        RouterManager.getInstance().removeRouterConsumer(this);
        Log.i(TAG, "onDestroy start");
        UserInfoManager.getInstance().removeUserInfoStateListener(iUserInfoStateListener);
        HistoryManager.getInstance().saveHistory(PlayerManager.getInstance().getCurPlayItem(), true);
        if (playBar != null) {
            playBar.detachPlayer();
            playBar.detachSubscribeModel();
        }
        //如果不加canStopAudio,会导致通过ClientSDK发送退出App但不停止播放命令,再次回到App中,点击电台不能播放
        if (canStopAudio) {
            SocketManager.getInstance().close();
            LiveStateManager.getInstance().exitRadio();
        }

        destroyPlayer();
        AdvertisingManager.getInstance().close();
        AdvertisingImager advertisingImager = AdvertisingManager.getInstance().getImager();
        if (advertisingImager != null && advertisingImager instanceof AdvertisingImagerImpl) {
            ((AdvertisingImagerImpl) advertisingImager).destroyAdView();
        }
        BaseApplication.getInstance().removeActivity(this);


        KRadioThirdPlatformInitInter kRadioThirdPlatformInitInter = ClazzImplUtil
                .getInter("KRadioThirdPlatformInitImpl");
        if (kRadioThirdPlatformInitInter != null) {
            kRadioThirdPlatformInitInter.destroyThirdPlatform(getApplicationContext());
        }
        AppManager.getInstance().setMainActivity(null);
        if (canStopAudio) {
            PlayerManager.getInstance().pause(false);
            PlayerManagerHelper.getInstance().destroy();
        }
        super.onDestroy();
    }

    /**
     * 获取数据
     */
    private void initDataBean() {
        List<Tab> tabs = new ArrayList<>();
        if (bean != null) {
            Tab tab;
            for (int i = 0; i < bean.run.navigation_bar.size(); i++) {
                tab = new Tab();
                tab.title = bean.run.navigation_bar.get(i).name;
                tab.position = i;
                tab.code = bean.run.navigation_bar.get(i).id;
                tabs.add(tab);
            }
        }
        mSrtlHomeNavigationTab.setTabs(tabs);
        mSrtlHomeNavigationTab.setCurrentTab(0);
        mSrtlHomeNavigationTab.getCurrentTab().tabView.setText(
                AppDateUtils.getInstance().getRadiusGradientSpan(this, mSrtlHomeNavigationTab.getCurrentTab()
                        .title));
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void cancelJumpToOnlinePlayerEvent(CancelJumpToOnlinePlayerEvent event) {
        mHandler.removeMessages(HANDLER_JUMP_PLAYER_ACTIVITY);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void jumpToPlayerActivity(JumpToOnlinePlayerEvent event) {
        if (event.isDelay()) {
            mHandler.removeMessages(HANDLER_JUMP_PLAYER_ACTIVITY);
            mHandler.sendMessageDelayed(mHandler.obtainMessage(HANDLER_JUMP_PLAYER_ACTIVITY, event.getLivingProgramId()), JUMP_PLAYER_ACTIVITY_DELAY);
        } else {
            mHandler.removeMessages(HANDLER_JUMP_PLAYER_ACTIVITY);
            mHandler.obtainMessage(HANDLER_JUMP_PLAYER_ACTIVITY, event.getLivingProgramId()).sendToTarget();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void closeDrawer(CloseDrawerEvent event) {
        onlineMianDl.closeDrawer(Gravity.END);
        if (event.isRefresh) {
            if (!TextUtils.isEmpty(KaolaAppConfigData.getInstance().getCityName())) {
                if (onlineMainCityTv != null)
                    onlineMainCityTv.setText(KaolaAppConfigData.getInstance().getCityName());
            }
            if (homeFragment != null && homeFragment instanceof HomeFragment) {
                ((HomeFragment) homeFragment).initData();
            }
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && onlineMianDl.isDrawerOpen(Gravity.END)) {
            onlineMianDl.closeDrawer(Gravity.END);
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    public void jumpToPlayerActivity() {
        PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
        if (playItem != null) {
            if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(this)) {
                return;
            }
            long id = PlayerManagerHelper.getInstance().getPlayId(playItem);
            jumpToPlayerActivity(id);
        }
    }

    /**
     * 跳转到播放页
     *
     * @param liveProgramId 如果是传统直播，则为直播id。如果不是直播，则为0
     */
    public void jumpToPlayerActivity(long liveProgramId) {
        mHandler.removeMessages(HANDLER_JUMP_PLAYER_ACTIVITY);
        if (!isFront || isShowingPlayerFragment())
            return;

        if (checkCannotJumpToPlayerPage(liveProgramId)) {
            return;
        }

        //如果发起跳转的是直播或广播电台，而且传送来的id与正在播放的直播或广播电台id不匹配，则不跳转。
        //防止在跳转命令发送期间切换了节目导致仍然跳转的问题出现
        if (liveProgramId != JumpToOnlinePlayerEvent.PROGRAM_NOT_SET) {
            PlayItem curPlayItem = PlayerManager.getInstance().getCurPlayItem();
            if (curPlayItem instanceof InvalidPlayItem) return;
            long currPlayId = PlayerManagerHelper.getInstance().getPlayId(curPlayItem);
            if (currPlayId != liveProgramId)
                return;
        }
        isFront = false;
        EventBus.getDefault().post(new OnlinePlayerFragmentJumpActionEvent(OnlinePlayerFragmentJumpActionEvent.ACTION_START));

        showPlayerFragment();
        hideAdImage();
    }

    /**
     * 检查不能跳转到播放页
     *
     * @param liveProgramId
     * @return
     */
    private boolean checkCannotJumpToPlayerPage(long liveProgramId) {
        //正在播放插播
        if (CrashPlayerHelper.getInstance().isPlayCrash) {
            return true;
        }
        //正在显示音频广告
        boolean isShowingAd = KradioAdAudioManager.getInstance().isShowing();
        if (isShowingAd) {
            return true;
        }
        //正在显示播放页
        if (isShowingPlayerFragment()) {
            return true;
        }
        return false;
    }

    private void showPlayerFragment() {
        //获取跳转前的事件上报PageId
        pageIdWhenJump = ReportParameterManager.getInstance().getPage();

        Bundle bundle = new Bundle();
        bundle.putString(BaseShowHideFragment.ARGUMENT_TAG, "播放详情页");
        bundle.putInt(OnlinePlayerFragment.PARAM_CENTER_X, (int) (playBar.getX() + playBar.getMeasuredWidth() / 2f));
        bundle.putInt(OnlinePlayerFragment.PARAM_PLAYERBAR_WIDTH, playBar.getMeasuredWidth());
        bundle.putInt(OnlinePlayerFragment.PARAM_RING_WIDTH, ((HomeFragment) homeFragment).getRingWidth());
        bundle.putInt(OnlinePlayerFragment.PARAM_RING_HEIGHT, ((HomeFragment) homeFragment).getRingHeight());
        bundle.putInt(OnlinePlayerFragment.PARAM_RING_MARGIN_BOTTOM, ((HomeFragment) homeFragment).getRingMarginBottom());
        bundle.putBoolean(OnlinePlayerFragment.PARAM_SHOW_ANIMATION, homeFragment.isVisible() && Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP);
        playerFragment = RouterManager.getInstance().getRouterFragment(RouterConstance.FRAGMENT_URL_PLAYER_FRAGMENT, bundle);
        //只有猜你喜欢需要共享组件，共享组件的也买你跳转时会一直显示当前页面之道光圈移动到播放页面的位置后再替换成播放页的布局
        //这时如果在跳转过程中滑动或者点击切换播单，会导致播放页显示内容跟实际播放不一致，因此需要禁用切换播单
        if (homeFragment instanceof HomeFragment) {
            ((HomeFragment) homeFragment).setEnablePlayItemChange(false);
        }
        getSupportFragmentManager().beginTransaction().add(R.id.playerFragmentParent, playerFragment, OnlinePlayerFragment.class.getSimpleName()).commitNowAllowingStateLoss();

        hideCurrentFragment();
    }

    @Override
    protected void onStop() {
        HistoryManager.getInstance().saveBroadcastList(PlayerManagerHelper.getInstance().getBroadcastRadioSimpleItems());
        isFront = false;
        super.onStop();
    }

    private void hidePlayerFragment(boolean showAnimation) {
        if (!isShowingPlayerFragment()) {
            return;
        }
        changePage(currentFragment);
        PlayerManagerHelper.getInstance().setInProgramPlayerPage(false);
        if (homeFragment instanceof HomeFragment) {
            isFront = true;
            ((HomeFragment) homeFragment).setEnablePlayItemChange(true);
        }
        if (!showAnimation) {
            getSupportFragmentManager().beginTransaction().remove(playerFragment).commitNowAllowingStateLoss();
            playerFragment = null;
            //重置PageId
            resetPageId(pageIdWhenJump);
            resetAdImageShowParam(currentFragment instanceof HomeFragment);
            EventBus.getDefault().post(new PlayerListChangedEBData());
            return;
        }
        if (playerFragment instanceof OnlinePlayerFragment) {
            ((OnlinePlayerFragment) playerFragment).onBackPressed(new Function0() {
                /**
                 * 播放详情页的返回动作完成
                 * @return
                 */
                @Override
                public Object invoke() {
                    getSupportFragmentManager().beginTransaction().remove(playerFragment).commitNowAllowingStateLoss();
                    playerFragment = null;
                    //重置PageId
                    resetPageId(pageIdWhenJump);
                    resetAdImageShowParam(currentFragment instanceof HomeFragment);
                    EventBus.getDefault().post(new PlayerListChangedEBData());
                    return null;
                }
            });
        }
    }

    private boolean isShowingPlayerFragment() {
        return playerFragment != null && getSupportFragmentManager().findFragmentByTag(OnlinePlayerFragment.class.getSimpleName()) != null;
    }

    @Override
    public void onBackPressedSupport() {
        if (isShowingPlayerFragment()) {
            hidePlayerFragment(true);
            return;
        }
        super.onBackPressedSupport();
    }

    @Override
    public void onDate(MainBean bean) {
        this.bean = bean;
        initDataBean();
    }

    @Override
    protected void onPostCreate(@Nullable Bundle savedInstanceState) {
        super.onPostCreate(savedInstanceState);
    }
 
    public void showMessageBubble(View v) {
        if (!AntiShake.check(v.getId())) {
            RouterManager.getInstance().jumpPage(RouterConstance.ACTIVITY_URL_MESSAGE);
            ButtonClickReportEvent event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_MESSAGE_PAGE);
            ReportHelper.getInstance().addEvent(event);
        }
    }

    /**
     * 判断是否显示消息泡泡为透明背景
     *
     * @return
     */
    private boolean isShowDialogBg() {
        boolean b = false;
        if (isFront) {
            //在首页，只有猜你喜欢是透明
            if (currentTab == 0) {
                if (onlineMianDl.isDrawerOpen(Gravity.RIGHT)) {
                    return true;
                }
            } else {
                return true;
            }
        } else {
            return true;
        }
        return b;
    }

    private CrashMessageBean crashMessageBean;

    /**
     * 显示消息弹窗
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void showMessageDialog(CrashMessageBean crashMessageBean) {
        Log.d("messageUI", "showMessageDialog");
//        if (AppManager.getInstance().getCurrentActivity() instanceof OnlineMessageActivity) {
//            //消息盒子页面不显示
//            return;
//        }
        if (dialogFragment != null && dialogFragment.isShowing()) {
            Log.d("messageUI", "dialogFragment is not null");
            if (dialogFragment.getCrashMessageBean() != null
                    && dialogFragment.getCrashMessageBean().getMsgId().equals(crashMessageBean.getMsgId())) {
                Log.d("showMessageDialog", "-------------插播-----已经在展示当前的消息");
                return;
            } else {
                dialogFragment.dismiss();
            }
        }
        if (AppManager.getInstance().getCurrentActivity() == null || crashMessageBean == null) {
            return;
        }

        createMessageBubbleDialogFragment(crashMessageBean);

        ((HomeFragment) homeFragment).mRing.setTouchEnable(false);
        long delayChange = ((HomeFragment) homeFragment).mRing.isAnimPlaying() ? 1000 : 0;
        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                dialogFragment.show();
            }
        }, delayChange);
        Log.d("messageUI", "dialogFragment is null");
    }

    /**
     * 创建消息泡泡弹窗Fragment
     */
    private void createMessageBubbleDialogFragment(CrashMessageBean crashMessageBean) {
        dialogFragment = new MessageBubbleDialogFragment(AppManager.getInstance().getCurrentActivity())
                .setMDimAmount(0.5f)
                .setCrashMessageBean(crashMessageBean)
                .showDialogBg(isShowDialogBg())
                .setTitle(crashMessageBean.getTipsTitle())
                .setSubTitle(crashMessageBean.getEventDescriptionExtract())
                .setButtons(new ArrayList<MessageBubbleDialogFragment.MessageBubbleButton>() {{
                    add(new MessageBubbleDialogFragment.MessageBubbleButton(crashMessageBean.getMsgDetailsBtnTextLeft(), R.id.online_message_bubble_button_1_id));
                    add(new MessageBubbleDialogFragment.MessageBubbleButton(crashMessageBean.getMsgDetailsBtnTextRight(), 8L, R.id.online_message_bubble_button_2_id));
//                    add(new MessageBubbleDialogFragment.MessageBubbleButton("删除", R.drawable.online_search_icon_delete, R.id.online_message_bubble_button_3_id));
//                    add(new MessageBubbleDialogFragment.MessageBubbleButton("确认", R.drawable.online_user_no_login_icon, 15, R.id.online_message_bubble_button_4_id));
                }}, new MessageBubbleDialogFragment.OnButtonClickListener() {
                    @Override
                    public void onButtonClick(MessageBubbleDialogFragment fragment, View v) {
                        int id = v.getId();
                        if (id == R.id.online_message_bubble_button_1_id) {
                            //查看详情
                            //数据上报
                            ButtonClickReportEvent event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_MSG_DETAILS_SHOW);
                            event.setPage(Constants.PAGE_ID_MESSAGE_CARD);
                            ReportHelper.getInstance().addEvent(event);
                            showMessageBubbleDetailFragment(crashMessageBean);
                            dialogFragment.dismiss();

                        } else if (id == R.id.online_message_bubble_button_2_id) {
                            //关闭
                            //数据上报
                            ButtonClickReportEvent event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_MSG_CLOSE);
                            event.setPage(Constants.PAGE_ID_MESSAGE_CARD);
                            ReportHelper.getInstance().addEvent(event);
                            if (CrashPlayerHelper.getInstance().isPlay()) {
                                CrashPlayerHelper.getInstance().playEnd();
                            }
                            dialogFragment.dismiss();
                        } else if (id == R.id.online_message_bubble_button_3_id) {
                            Log.e(TAG, "点击按钮：3");
                        } else if (id == R.id.online_message_bubble_button_4_id) {
                            Log.e(TAG, "点击按钮：4");
                        }
                        //上报点击
                        ReportUtil.addMessageClike(Constants.PAGE_ID_MESSAGE_CARD, crashMessageBean.getMsgContentType(), crashMessageBean.getMsgId());
                    }
                }).setOnShowListener(new MessageBubbleDialogFragment.OnShowListener() {
                    @Override
                    public void onShow() {
                        Log.d("messageUI", "dialogFragment onShow");
                        changeHomeUI(true);
                    }
                }).setOnDisMissListener(new MessageBubbleDialogFragment.OnDismissListener() {
                    @Override
                    public void onDisMiss() {
                        Log.d("messageUI", "dialogFragment onDisMiss");
                        changeHomeUI(false);
                    }
                });
    }

    /**
     * 展示消息泡泡详情
     */
    private void showMessageBubbleDetailFragment(CrashMessageBean crashMessageBean) {
        MessageDetailsDialogFragment detailsDialogFragment
                = new MessageDetailsDialogFragment(AppManager.getInstance().getCurrentActivity());
        detailsDialogFragment.setCrashMessageBean(crashMessageBean)
                .show();
        MessageDaoManager.getInstance().updateLook(crashMessageBean.getMsgId());
    }

    void changeHomeUI(boolean slide) {
        Log.d("messageUI", "changeHomeUI, slide is " + slide + ", slided is " + slided);
        if (slide) {
            if (!(currentFragment instanceof HomeFragment) || slided) {
                return;
            }
            Log.d("messageUI", "changeHomeUI, currentFragment is HomeFragment");
            int screenWidth = ScreenUtil.getScreenWidth();
            int playerCenterX = screenWidth - (screenWidth - getResources().getDimensionPixelSize(R.dimen.x507)) / 2;
            int playerWidth = getResources().getDimensionPixelSize(R.dimen.x873);
            ViewGroup.LayoutParams layoutParams = playBar.getLayoutParams();
            ViewGroup.LayoutParams layoutParams1 = ((HomeFragment) homeFragment).mRing.getLayoutParams();
            ViewGroup.LayoutParams layoutParams2 = roadIv.getLayoutParams();
            ((RelativeLayout.LayoutParams) layoutParams).setMarginStart(playerCenterX - playerWidth / 2);
            ((FrameLayout.LayoutParams) layoutParams1).setMarginStart(playerCenterX - playerWidth / 2);
            ((RelativeLayout.LayoutParams) layoutParams2).setMarginStart(playerCenterX - playerWidth / 2);
            playBar.setLayoutParams(layoutParams);
            ((HomeFragment) homeFragment).mRing.setLayoutParams(layoutParams1);
            roadIv.setLayoutParams(layoutParams2);
            ((HomeFragment) homeFragment).mRing.hideUnselectItem();
            slided = true;
            Log.d("messageUI", "changeHomeUI, startAnim");
        } else {
            if (!slided) {
                return;
            }
            int playerMargain = getResources().getDimensionPixelSize(R.dimen.x90);
            ViewGroup.LayoutParams layoutParams = playBar.getLayoutParams();
            ViewGroup.LayoutParams layoutParams1 = ((HomeFragment) homeFragment).mRing.getLayoutParams();
            ViewGroup.LayoutParams layoutParams2 = roadIv.getLayoutParams();
            ((RelativeLayout.LayoutParams) layoutParams).setMarginStart(playerMargain);
            ((RelativeLayout.LayoutParams) layoutParams).setMarginEnd(playerMargain);
            ((FrameLayout.LayoutParams) layoutParams1).setMarginStart(0);
            ((RelativeLayout.LayoutParams) layoutParams2).setMarginStart(0);
            playBar.setLayoutParams(layoutParams);
            ((HomeFragment) homeFragment).mRing.setLayoutParams(layoutParams1);
            roadIv.setLayoutParams(layoutParams2);
            ((HomeFragment) homeFragment).mRing.showUnselectItem();
            ((HomeFragment) homeFragment).mRing.setTouchEnable(true);
            slided = false;
            Log.d("messageUI", "changeHomeUI, endAnim");
        }
    }

    /**
     * 销毁播放器
     */
    private void destroyPlayer() {
        Log.i(TAG, "destroyPlayer: canStopAudio = " + canStopAudio);
        if (canStopAudio) {
            MediaSessionUtil.getInstance().unregisterMediaSession();
            MediaSessionUtil.getInstance().release();
        } else {
            PlayerManagerHelper.getInstance().resetVariable();
        }
    }

    /**
     * 用于侧滑搜索页打开和关闭、播放详情页隐藏后重置PageId
     *
     * @param pageId
     */
    private void resetPageId(String pageId) {
        if (!StringUtil.isEmpty(pageId)) {
            Log.i(ReportConstants.REPORT_TAG, "设置pageid= " + pageId);
            ReportHelper.getInstance().setPage(pageId);
        }
    }

    @Override
    public String consumeRoute(String pageId, Object extra) {
//        if (!getLifecycle().getCurrentState().isAtLeast(Lifecycle.State.RESUMED)) return pageId;

        if (!Constants.ONLINE_PAGE_ID_SEARCH.equals(pageId)
                && !Constants.ONLINE_PAGE_ID_SEARCH_RESULT.equals(pageId)
                && !Constants.PAGE_ID_LOCATION.equals(pageId)) {
            //如果不是跳转搜索页，则应隐藏搜索页
            if (onlineMianDl.isDrawerOpen(Gravity.END))
                onlineMianDl.closeDrawer(Gravity.END);
        }
        int tabCount;
        switch (pageId) {
            case Constants.PAGE_ID_HOME_LIKE:   //猜你喜欢
                tabCount = mSrtlHomeNavigationTab.getTabCount();
                if (tabCount > 0) mSrtlHomeNavigationTab.getTab(0).tabView.performClick();
                break;
            case Constants.PAGE_ID_CATEGORIES_BROADCAST:    //分类--广播tab页面
            case Constants.PAGE_ID_CATEGORIES_TV:   //分类--电视tab页面
            case Constants.PAGE_ID_CATEGORIES_AI:   //分类--AI电台tab页面
            case Constants.PAGE_ID_CATEGORIES_ALBUM:    //分类--专辑tab页面
                mTargetChildFragmentPageId = pageId;
                tabCount = mSrtlHomeNavigationTab.getTabCount();
                if (tabCount > 1) mSrtlHomeNavigationTab.getTab(1).tabView.performClick();
                break;
            case Constants.PAGE_ID_LISTENING_TRACE_SUBSCRIBE_ALBUM: //听迹--我的订阅--专辑/AI电台tab页面
            case Constants.PAGE_ID_LISTENING_TRACE_SUBSCRIBE_SONG:  //听迹--我的订阅--单曲tab页面
            case Constants.PAGE_ID_LISTENING_TRACE_SUBSCRIBE_TV:    //听迹--我的订阅--广播/电视tab页面
            case Constants.PAGE_ID_LISTENING_TRACE_HISTORY: //听迹--收听历史页面
                mTargetChildFragmentPageId = pageId;
                tabCount = mSrtlHomeNavigationTab.getTabCount();
                if (tabCount > 2) mSrtlHomeNavigationTab.getTab(2).tabView.performClick();
                break;
            case Constants.ONLINE_PAGE_ID_ACTIVITY: //活动-列表
                tabCount = mSrtlHomeNavigationTab.getTabCount();
                if (tabCount > 3) mSrtlHomeNavigationTab.getTab(3).tabView.performClick();
                break;
            case Constants.ONLINE_PAGE_ID_ACTIVITY_DATEILS: //活动-详情，需要切换到活动-列表页面
                tabCount = mSrtlHomeNavigationTab.getTabCount();
                if (tabCount > 3) mSrtlHomeNavigationTab.getTab(3).tabView.performClick();
                if (extra instanceof Activity) {
                    ActivitysDetailsDialogFragment dialogFragment
                            = (ActivitysDetailsDialogFragment) new ActivitysDetailsDialogFragment(this);
                    dialogFragment.setActivityBean((Activity) extra);
                    dialogFragment.show();
                }
                break;
            case Constants.ONLINE_PAGE_ID_SEARCH:   //搜索页
                onlineMianSearchIv.performClick();
                break;
            case Constants.ONLINE_PAGE_ID_SEARCH_RESULT:  //搜索结果页
                if (extra instanceof SearchInfo) {
                    searchInfo = (SearchInfo) extra;
                }
                onlineMianSearchIv.performClick();
                break;
            case Constants.PAGE_ID_LOCATION:    //城市选择页
                onlineHomeLocationLl.performClick();
                break;
            case Constants.ONLINE_PAGE_ID_PLAYER_ALBUM: //专辑详情页
                if (PlayerManagerHelper.getInstance().getCurrentPlayType() != PlayerConstants.RESOURCES_TYPE_ALBUM
                        && PlayerManagerHelper.getInstance().getCurrentPlayType() != PlayerConstants.RESOURCES_TYPE_RADIO) {
                    ToastUtil.showNormal(this, "无法跳转到详情页，当前播放非专栏节目/智能电台");
                } else {
                    if (isShowingPlayerFragment()) {
                        if (extra instanceof Boolean && (Boolean) extra) {
                            //相同节目，不刷新了
                            return IRouterConsumer.ROUTER_CONSUME_FULLY;
                        }
                        if (playerFragment instanceof IRouterConsumer) {
                            return ((IRouterConsumer) playerFragment).consumeRoute(pageId, extra);
                        }
                    }
                    jumpToPlayerActivity();
                }
                break;
            case Constants.PAGE_ID_PLAY_TV: //广播详情页
                if (PlayerManagerHelper.getInstance().getCurrentPlayType() != PlayerConstants.RESOURCES_TYPE_BROADCAST
                        && PlayerManagerHelper.getInstance().getCurrentPlayType() != PlayerConstants.RESOURCES_TYPE_TV) {
                    ToastUtil.showNormal(this, "无法跳转到详情页，当前播放非广播/听电视");
                } else {
                    if (isShowingPlayerFragment()) {
                        if (extra instanceof Boolean && (Boolean) extra) {
                            //相同节目，不刷新了
                            return IRouterConsumer.ROUTER_CONSUME_FULLY;
                        }
                        if (playerFragment instanceof IRouterConsumer) {
                            return ((IRouterConsumer) playerFragment).consumeRoute(pageId, extra);
                        }
                    }
                    jumpToPlayerActivity();
                }
                break;
            case Constants.PAGE_ID_LIVE_ROOM: //直播详情页
                if (PlayerManagerHelper.getInstance().getCurrentPlayType() != PlayerConstants.RESOURCES_TYPE_LIVING) {
                    ToastUtil.showNormal(this, "无法跳转到详情页，当前播放非直播");
                } else {
                    if (isShowingPlayerFragment()) {
                        if (extra instanceof Boolean && (Boolean) extra) {
                            //相同节目，不刷新了
                            return IRouterConsumer.ROUTER_CONSUME_FULLY;
                        }
                        if (playerFragment instanceof IRouterConsumer) {
                            return ((IRouterConsumer) playerFragment).consumeRoute(pageId, extra);
                        }
                    }
                    jumpToPlayerActivity();
                }
                break;
            case Constants.ONLINE_PAGE_ID_PAY_VIP: //VIP购买
                if (ComponentUtil.getResultValue(UserComponentConst.NAME, UserComponentConst.IS_USER_BOUND)) {
                    PayManager.getInstance()
                            .pay(PayConst.PAY_TYPE_VIP, PlayerManager.getInstance().getCurPlayItem());
                } else {
                    Bundle bundle = new Bundle();
                    bundle.putString("type", ReportParameterManager.getInstance().getPage());
                    RouterManager.getInstance().jumpPage(RouterConstance.ACTIVITY_URL_LOGIN, bundle);
                }
                break;
            case Constants.ONLINE_PAGE_ID_PAY_ALBUM: //专辑购买
            case Constants.ONLINE_PAGE_ID_PAY_AUDIO: //碎片购买页面-单集购买
            case Constants.ONLINE_PAGE_ID_PAY_AUDIO_LIST: //碎片购买页面-多集购买
                if (PlayerManagerHelper.getInstance().getCurrentPlayType() != PlayerConstants.RESOURCES_TYPE_ALBUM) {
                    ToastUtil.showNormal(getApplication(), "无法跳转到支付页，当前播放非专栏节目/智能电台");
                } else {
                    PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
                    int buyStatus = playItem.getBuyStatus();
                    int buyType = playItem.getBuyType();
                    boolean canJump = true;
                    // FIXME: 2022/11/10 当购买专辑、购买单曲的时候，需要后期用到具体场景时再做修改
                    if (pageId.equals(Constants.ONLINE_PAGE_ID_PAY_ALBUM) && buyType != HintVoiceChainIntercept.TYPE_ALBUM) {
                        ToastUtil.showNormal(getApplication(), "当前播放内容不可购买专辑");
                        canJump = false;
                    } else if (pageId.equals(Constants.ONLINE_PAGE_ID_PAY_VIP) && (playItem.getVip() != 1)) {
                        ToastUtil.showNormal(getApplication(), "当前播放内容不可购买VIP");
                        canJump = false;
                    } else if ((pageId.equals(Constants.ONLINE_PAGE_ID_PAY_AUDIO) || pageId.equals(Constants.ONLINE_PAGE_ID_PAY_AUDIO_LIST)) &&
                            buyType != HintVoiceChainIntercept.TYPE_AUDIO) {
                        ToastUtil.showNormal(getApplication(), "当前播放内容不可购买碎片");
                        canJump = false;
                    }
                    if (canJump) {
                        jumpToPlayerFragmentAndPay(playItem, 0, buyType, buyStatus, ComponentUtil.getResultValue(UserComponentConst.NAME, UserComponentConst.IS_USER_BOUND), null, null, null);
                    }
                }
                break;
            case Constants.PAGE_ID_MESSAGE_CARD:    //消息泡泡插播
                if (extra instanceof CrashMessageBean) {
                    CrashMessageBean crashMessageBean = (CrashMessageBean) extra;
                    createMessageBubbleDialogFragment(crashMessageBean);
                    long delayChange = ((HomeFragment) homeFragment).mRing.isAnimPlaying() ? 1000 : 0;
                    mHandler.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            dialogFragment.show();
                        }
                    }, delayChange);
                } else {
                    ToastUtil.showNormal(getApplication(), "插播消息错误，跳转插播页失败");
                }
                break;
            case Constants.PAGE_ID_MESSAGE_DETAILS:    //消息泡泡插播详情
                if (extra instanceof CrashMessageBean) {
                    CrashMessageBean crashMessageBean = (CrashMessageBean) extra;
                    showMessageBubbleDetailFragment(crashMessageBean);
                } else {
                    ToastUtil.showNormal(getApplication(), "插播消息错误，跳转消息详情页失败");
                }
                break;
        }
        return IRouterConsumer.ROUTER_CONSUME_FULLY;
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        Log.e(TAG, "MainActivity----->onNewIntent");
        interceptApplicationJumpEvent(intent);
    }

    private void interceptApplicationJumpEvent(Intent intent) {
        if (intent == null) intent = getIntent();
        String pageId = intent.getStringExtra(Constants.ROUTER_PARAMS_KEY_PAGE_ID);
        if (StringUtil.isEmpty(pageId)) return;
        if (PlayerManagerHelper.getInstance().isAudioAdPlayLockOver()){
            //如果在广告的锁定期就不响应跳转
            return;
        }
        Parcelable extra;
        switch (pageId) {
            case Constants.ONLINE_PAGE_ID_PLAYER_ALBUM: //专辑详情页
            case Constants.PAGE_ID_PLAY_TV: //广播详情页
            case Constants.PAGE_ID_LIVE_ROOM: //直播详情页
            case Constants.ONLINE_PAGE_ID_PAY_ALBUM: //专辑购买
            case Constants.ONLINE_PAGE_ID_PAY_AUDIO: //碎片购买页面-单集购买
            case Constants.ONLINE_PAGE_ID_PAY_AUDIO_LIST: //碎片购买页面-多集购买
                String resourceId = intent.getStringExtra(Constants.ROUTER_PARAMS_KEY_RESOURCE_ID);
                int resourceType = intent.getIntExtra(Constants.ROUTER_PARAMS_KEY_RESOURCE_TYPE, 0);
                boolean isSameProgram = String.valueOf(PlayerManagerHelper.getInstance().getPlayId(PlayerManager.getInstance().getCurPlayItem())).equals(resourceId);
                if (!isSameProgram) {
                    BeginNewPlayListener mBeginNewPlayListener = new BeginNewPlayListener() {
                        @Override
                        public void onPlayerPreparing(PlayItem playItem) {
                            PlayerManagerHelper.getInstance().play(false);
                            if (!String.valueOf(PlayerManagerHelper.getInstance().getPlayId(PlayerManager.getInstance().getCurPlayItem())).equals(resourceId))
                                return;
                            PlayerManager.getInstance().removePlayControlStateCallback(this);
                            consumeRoute(pageId, false);
                        }
                    };
                    PlayerManagerHelper.getInstance().pause(false);
                    PlayerManager.getInstance().addPlayControlStateCallback(mBeginNewPlayListener);
                    PlayerManagerHelper.getInstance().start(resourceId, resourceType);
                } else {
                    consumeRoute(pageId, true);
                }
                return;
            case Constants.ONLINE_PAGE_ID_SEARCH_RESULT:
                String keyword = intent.getStringExtra(Constants.ROUTER_PARAMS_KEY_SEARCH_KEYWORD);
                if (StringUtil.isEmpty(keyword)) {
                    Logger.e(TAG, "应用内跳转--->搜索关键词为空");
                    return;
                }
                extra = new SearchInfo(keyword, intent.getIntExtra(Constants.ROUTER_PARAMS_KEY_SEARCH_WAY, 1));
                break;
            default:
                extra = intent.getParcelableExtra(Constants.ROUTER_PARAMS_KEY_EXTRA);
                break;
        }
        if (StringUtil.isNotEmpty(pageId)) {
            consumeRoute(pageId, extra);
        }
    }


    /**
     * 用于应用内跳转前播放新资源时的监听器
     */
    private abstract static class BeginNewPlayListener implements IPlayerStateListener {

        @Override
        public void onIdle(PlayItem playItem) {

        }

        @Override
        public void onPlayerPreparing(PlayItem playItem) {

        }

        @Override
        public void onPlayerPlaying(PlayItem playItem) {

        }

        @Override
        public void onPlayerPaused(PlayItem playItem) {

        }

        @Override
        public void onProgress(PlayItem playItem, long l, long l1) {

        }

        @Override
        public void onPlayerFailed(PlayItem playItem, int i, int i1) {

        }

        @Override
        public void onPlayerEnd(PlayItem playItem) {

        }

        @Override
        public void onSeekStart(PlayItem playItem) {

        }

        @Override
        public void onSeekComplete(PlayItem playItem) {

        }

        @Override
        public void onBufferingStart(PlayItem playItem) {

        }

        @Override
        public void onBufferingEnd(PlayItem playItem) {

        }

        @Override
        public void onDownloadProgress(PlayItem playItem, long l, long l1) {

        }
    }
}
