<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/root_layout"
        android:layout_width="@dimen/m666"
        android:layout_height="@dimen/m388"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/y186"
        android:background="@drawable/bg_dialog"
        android:gravity="center"
        android:maxWidth="@dimen/m666"
        android:orientation="vertical"
        android:paddingLeft="@dimen/x60"
        android:paddingTop="@dimen/y50"
        android:paddingRight="@dimen/x60"
        android:paddingBottom="@dimen/y50">

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <TextView
                android:id="@+id/tv_dialog_bottom_message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="start"
                android:lineSpacingExtra="@dimen/m7"
                android:textColor="@color/dialog_common_btn_title_text_color"
                android:textSize="@dimen/text_size4"
                tools:text="@string/person_center_permission_dialog_str" />
        </ScrollView>


        <LinearLayout
            android:id="@+id/check_ll"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/y14"
            android:orientation="horizontal"
            android:visibility="gone"
            tools:visibility="visible">

            <CheckBox
                android:id="@+id/check_box"
                android:layout_width="@dimen/m32"
                android:layout_height="@dimen/m32"
                android:background="@drawable/sl_cb"
                android:button="@null"
                android:checked="false"
                android:paddingStart="@dimen/x10"
                android:textColor="#ffffffff"
                android:textSize="@dimen/text_size3" />

            <TextView
                android:id="@+id/tvCb"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:paddingStart="@dimen/x22"
                android:text="@string/launch_agreement_tip"
                android:textColor="@color/global_title_text_color"
                android:textSize="@dimen/text_size4" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/tv_dialog_button_main_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/y30"
            android:gravity="center_horizontal"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_dialog_bottom_define"
                android:layout_width="@dimen/m180"
                android:layout_height="@dimen/m50"
                android:background="@drawable/selector_dialog_costomize_btn_sure"
                android:gravity="center"
                android:text="@string/ok"
                android:textColor="@color/dialog_common_btn_cancel_text_color"
                android:textSize="@dimen/text_size4" />

            <TextView
                android:id="@+id/tv_dialog_bottom_cancel"
                android:layout_width="@dimen/m180"
                android:layout_height="@dimen/m50"
                android:layout_marginStart="@dimen/m110"
                android:background="@drawable/selector_dialog_costomize_btn_cancle"
                android:gravity="center"
                android:minWidth="@dimen/m156"
                android:text="@string/cancel"
                android:textColor="@color/dialog_common_btn_sure_text_color"
                android:textSize="@dimen/text_size4" />
        </LinearLayout>
    </LinearLayout>
</FrameLayout>
