package com.kaolafm.kradio.component.ui.ring.view;

import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.lib.common.ResType;
import com.kaolafm.kradio.lib.utils.UrlUtil;
import com.kaolafm.opensdk.api.operation.model.column.AlbumDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.AudioDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.BroadcastDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.CategoryColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.Column;
import com.kaolafm.opensdk.api.operation.model.column.ColumnGrp;
import com.kaolafm.opensdk.api.operation.model.column.ColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.LiveProgramDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.RadioDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.RadioQQMusicDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.SearchResultColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.TVDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.WebViewColumnMember;
import com.kaolafm.opensdk.player.logic.model.item.AlbumPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.BroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.LivePlayItem;
import com.kaolafm.opensdk.player.logic.model.item.RadioPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.TVPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.utils.operation.OperationAssister;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> shiqian
 * @date 2022-07-19
 */
public class RingManager {

    private List<HomeCell> mList = new ArrayList<>();
    private HomeCell mTempCell = null;
    private HomeCell mCurCell = null;
    private int mIndex = 0;

    private static final String TAG = "RingManager";

    private volatile static RingManager mInstance = null;

    public static RingManager getInstance() {
        if (mInstance == null) {
            synchronized (RingManager.class) {
                if (mInstance == null) {
                    Log.i(TAG, "new RingManager");
                    mInstance = new RingManager();
                }
            }
        }
        return mInstance;
    }

    /**
     * 设置数据
     *
     * @param list
     */
    public void setDatas(List<HomeCell> list) {
        mList.clear();

        //根据playId去重
//        list = list.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(HomeCell :: getPlayId))), ArrayList::new));
        for (int i = 0; i < list.size(); i++) {
            boolean shouldAdd = true;
            for (int j = 0; j < mList.size(); j++) {
                if (mList.get(j).playId == list.get(i).playId) {
                    shouldAdd = false;
                    break;
                }
            }
            if (shouldAdd) {
                mList.add(list.get(i));
            }
        }
    }

    public List<HomeCell> getDatas() {
        return mList;
    }

    public HomeCell getTempData() {
        return mTempCell;
    }

    /**
     * 添加临时数据（光圈列表以外的数据）
     *
     * @param position
     * @param homeCell
     */
    public void addTempData(int position, HomeCell homeCell) {
        if (mTempCell != null) {
            mList.add(mList.indexOf(mTempCell), homeCell);
            mList.remove(mTempCell);
            mTempCell = homeCell;
        } else {
            mList.add(position, homeCell);
            mTempCell = homeCell;
        }
    }

    public void deleteTempData() {
        if (mTempCell != null) {
            mList.remove(mTempCell);
            mTempCell = null;
        }
    }

    public HomeCell getCurItem() {
        return mCurCell;
    }


    public void setCurIndex(int index) {
        mIndex = index;
        mCurCell = mList.get(mIndex);
    }

    public int getCurIndex() {
        return mIndex;
    }

    public void deleteData(PlayItem playItem) {
        for (int i = mList.size() - 1; i >= 0; i--) {
            HomeCell homeCell = mList.get(i);
            if (homeCell.belongingId == Long.valueOf(playItem.getAlbumId())) {
                mList.remove(homeCell);
                setCurIndex(i);
            }
        }
    }

    /**
     * PlayItem转化为HomeCell
     *
     * @param playItem
     * @return
     */
    public static HomeCell transPlayItemToHomeCell(PlayItem playItem) {
        if (playItem != null) {
            HomeCell cell = new HomeCell();
            cell.resType = playItem.getType();
            cell.imageUrl = playItem.getPicUrl();
            cell.vip = playItem.getVip();
            cell.fine = playItem.getFine();
            if (playItem instanceof AlbumPlayItem) {
                cell.code = playItem.getAudioId() + "";
                cell.playId = playItem.getAudioId();
                cell.belongingId = Long.parseLong(playItem.getAlbumId());
                if (TextUtils.isEmpty(playItem.getAlbumTitle())) {
                    cell.name = playItem.getTitle();
                } else {
                    cell.name = playItem.getAlbumTitle();
                }
                cell.imageUrl = playItem.getPicUrl();
            } else if (playItem instanceof BroadcastPlayItem) {
                cell.code = playItem.getRadioId();
                cell.playId = Long.parseLong(playItem.getRadioId());
                cell.belongingId = Long.parseLong(playItem.getAlbumId());
                cell.name = playItem.getAlbumTitle();
                cell.imageUrl = playItem.getPicUrl();
                cell.freq = ((BroadcastPlayItem) playItem).getFrequencyChannel();
                cell.contentType = ((BroadcastPlayItem) playItem).getBroadcastSort();
            } else if (playItem instanceof TVPlayItem) {
                cell.code = playItem.getRadioId();
                cell.playId = Long.parseLong(playItem.getRadioId());
                cell.belongingId = Long.parseLong(playItem.getAlbumId());
                cell.name = playItem.getAlbumTitle();
                cell.imageUrl = playItem.getPicUrl();
                cell.freq = ((TVPlayItem) playItem).getFrequencyChannel();
            } else if (playItem instanceof RadioPlayItem) {
                cell.code = playItem.getRadioId();
                cell.playId = Long.parseLong(playItem.getRadioId());
                cell.belongingId = Long.parseLong(playItem.getRadioId());
                cell.name = playItem.getRadioName();
                cell.imageUrl = playItem.getPicUrl();
            } else if (playItem instanceof LivePlayItem) {
                cell.code = playItem.getRadioId();
                cell.playId = Long.parseLong(playItem.getRadioId());
                cell.belongingId = playItem.getAudioId();
                cell.name = playItem.getRadioName();
                cell.imageUrl = playItem.getPicUrl();
            }
            return cell;
        }
        return null;
    }


    /**
     * 栏目数组转化为HomeCell数组
     *
     * @param grps
     * @return
     */
    public static List<HomeCell> transColumnGrpsToHomeCells(List<ColumnGrp> grps) {
        if (ListUtil.isEmpty(grps)) {
            return null;
        }
        ArrayList<HomeCell> homeCells = new ArrayList<>();
        Iterator<ColumnGrp> iterator = grps.iterator();
        while (iterator.hasNext()) {
            ColumnGrp columnGrp = iterator.next();
            //转化为Card。tab不为空说明有子栏目
            if (columnGrp instanceof Column) {
                getHomeCellList(columnGrp, homeCells);
            } else {
                List<? extends ColumnGrp> childColumnGrps = columnGrp.getChildColumns();
                if (childColumnGrps != null) {
                    for (int j = 0, childSize = childColumnGrps.size(); j < childSize; j++) {
                        ColumnGrp childGroup = childColumnGrps.get(j);
                        if (childGroup instanceof Column) {
                            getHomeCellList(childGroup, homeCells);
                        }
                    }
                }
            }
        }
        return homeCells;
    }

    private static void getHomeCellList(ColumnGrp columnGrp, ArrayList<HomeCell> homeCells) {
        List<? extends ColumnMember> columnMembers = ((Column) columnGrp).getColumnMembers();
        Iterator<? extends ColumnMember> iterator2 = columnMembers.iterator();

        while (iterator2.hasNext()) {
            ColumnMember columnMember = iterator2.next();
            HomeCell homeCell = new HomeCell();
            if (columnMember instanceof CategoryColumnMember) {
                Map<String, String> extInfo = columnMember.getExtInfo();
                //功能入口，小卡片
                if (extInfo != null && ("SMALL".equals(extInfo.get("displayStyle"))
                        || "BIG".equals(extInfo.get("displayStyle")))) {

                    String[] paths = getPaths(extInfo);
                    homeCell.firstCode = paths[0];
                    homeCell.secondCode = paths[1];
                }
            }
            String image = OperationAssister.getImage(columnMember);
            homeCell.imageUrl = UrlUtil.getDefaultConfigPicUrl(image);
            homeCell.code = columnMember.getCode();
            homeCell.playId = OperationAssister.getId(columnMember);
            if (columnMember instanceof AlbumDetailColumnMember) {
                homeCell.belongingId = ((AlbumDetailColumnMember) columnMember).getAlbumId();
            } else if (columnMember instanceof RadioDetailColumnMember) {
                homeCell.belongingId = ((RadioDetailColumnMember) columnMember).getRadioId();
            } else if (columnMember instanceof BroadcastDetailColumnMember) {
                homeCell.belongingId = ((BroadcastDetailColumnMember) columnMember).getBroadcastId();
                homeCell.contentType = ((BroadcastDetailColumnMember) columnMember).getBroadcastSort();
                homeCell.freq = ((BroadcastDetailColumnMember) columnMember).getFreq();
            } else if (columnMember instanceof TVDetailColumnMember) {
                homeCell.belongingId = ((TVDetailColumnMember) columnMember).getListenTVid();
            } else if (columnMember instanceof LiveProgramDetailColumnMember) {
                LiveProgramDetailColumnMember liveColumnMember = (LiveProgramDetailColumnMember) columnMember;
                homeCell.belongingId = liveColumnMember.getLiveProgramId();
                homeCell.anchor = liveColumnMember.getAnchor();
            }

            homeCell.name = columnMember.getTitle();
            homeCell.resType = getResType(columnMember);
            homeCell.recommendReason = columnMember.getRecommendReason();
            homeCell.outputMode = columnMember.getOutputMode();
            homeCell.callBack = columnMember.getCallBack();
            homeCell.vip = getVip(columnMember);
            homeCell.fine = getFine(columnMember);
            homeCells.add(homeCell);
        }

    }

    private static String[] getPaths(Map<String, String> extInfo) {
        String[] codes = new String[]{"0", "0"};
        String categoryPath = extInfo.get("categoryPath");
        if (!TextUtils.isEmpty(categoryPath)) {
            String[] paths = categoryPath.split("\\/");
            if (paths.length > 0) {
                codes[0] = paths[0];
            }
            if (paths.length > 1) {
                codes[1] = paths[1];
            }
        }
        return codes;
    }

    /**
     * 获取播放类型
     */
    private static int getResType(ColumnMember cm) {
        int restype = ResType.ALBUM_TYPE;
        if (cm instanceof LiveProgramDetailColumnMember) {
            restype = ResType.LIVE_TYPE;
        } else if (cm instanceof SearchResultColumnMember) {

        } else if (cm instanceof BroadcastDetailColumnMember) {
            restype = ResType.BROADCAST_TYPE;
        } else if (cm instanceof TVDetailColumnMember) {
            restype = ResType.TV_TYPE;
        } else if (cm instanceof AlbumDetailColumnMember) {
            restype = ResType.ALBUM_TYPE;
        } else if (cm instanceof AudioDetailColumnMember) {
            restype = ResType.AUDIO_TYPE;
        } else if (cm instanceof RadioDetailColumnMember) {
            restype = ResType.RADIO_TYPE;
        } else if (cm instanceof RadioQQMusicDetailColumnMember) {
            RadioQQMusicDetailColumnMember qqcm = (RadioQQMusicDetailColumnMember) cm;
//            0,场景电台 ;1,标签电台
            switch (qqcm.getRadioQQMusicType()) {
                case 1:
                    restype = ResType.RESOURCES_TYPE_MUSIC_RADIO_LABEL;
                    break;
                case 0:
                    restype = ResType.RESOURCES_TYPE_MUSIC_RADIO_SCENE;
                    break;
                default:
                    restype = ResType.RESOURCES_TYPE_QQ_MUSIC;
            }
        } else if (cm instanceof WebViewColumnMember) {
            //error
        } else if (cm instanceof CategoryColumnMember) {
//            1:专辑；2:广播；3:直播；4:智能电台；5:QQ音乐电台
            //功能入口，跳转，需要额外信息
            restype = ResType.FUNCTION_ENTER_SMALL;

        }
        Map<String, String> extInfo = cm.getExtInfo();
        return restype;
    }

    /**
     * 是否vip 1:是,0:否
     */
    private static int getVip(ColumnMember cm) {
        int vipType = 0;
        if (cm instanceof AlbumDetailColumnMember) {
            vipType = ((AlbumDetailColumnMember) cm).getVip();
        }
        return vipType;
    }

    /**
     * 是否精品  1:是,0:否
     */
    private static int getFine(ColumnMember cm) {
        int fine = 0;
        if (cm instanceof AlbumDetailColumnMember) {
            fine = ((AlbumDetailColumnMember) cm).getFine();
        }
        return fine;
    }
}
