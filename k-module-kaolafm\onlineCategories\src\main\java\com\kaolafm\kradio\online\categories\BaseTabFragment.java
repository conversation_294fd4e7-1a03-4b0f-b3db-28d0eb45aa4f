package com.kaolafm.kradio.online.categories;

import androidx.lifecycle.Lifecycle.State;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.Fragment;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup.MarginLayoutParams;
import android.view.ViewStub;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import com.flyco.tablayout.SlidingTabLayout;
import com.flyco.tablayout.listener.OnTabSelectListener;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.common.SubcategoryItemBean;
import com.kaolafm.kradio.common.widget.NotScrollViewPager;
import com.kaolafm.kradio.k_kaolafm.R; 
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.ui.BaseLazyFragment;
import com.kaolafm.kradio.lib.base.ui.online.BaseShowHideLazyFragment;
import com.kaolafm.kradio.lib.base.ui.online.BaseViewPagerLazyFragment;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.lib.widget.tab.Tab;
import com.kaolafm.kradio.online.common.event.OnlineShowCityTabEvent;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.kradio.online.common.utils.AppDateUtils;
import com.kaolafm.kradio.online.categories.adapter.CategoriesFragmentAdapter;
import com.kaolafm.kradio.online.categories.event.SubcategoryItemEvent;
import com.kaolafm.kradio.online.categories.tab.TabContract;
import com.kaolafm.kradio.online.categories.tab.TabItemFragment;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonClickReportEvent;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.List; 

/**
 * 所有分类页面tab页面的基类
 *
 * <AUTHOR>
 **/
public abstract class BaseTabFragment extends BaseViewPagerLazyFragment<TabContract.IPresenter> implements TabContract.IView {

    private static final String TAG = "BaseTabFragment";

    private Runnable runnable;
 
    LinearLayout mLlSubcategoryRoot;
 
    ConstraintLayout tabTitleLl; 
    View tab_title_city_ll; 
    protected SlidingTabLayout mStbSubcategoryTabTitle;
 
    protected com.kaolafm.kradio.lib.widget.tab.SlidingTabLayout mStbSubcategoryTabTitleTwo;
 
    View slidingTwoGradientLeft; 
    View slidingTwoGradientRight;
 
    protected com.kaolafm.kradio.lib.widget.tab.SlidingTabLayout stb_subcategory_tab_title_city;
 
    View slidingCityGradientLeft; 
    View slidingCityGradientRight;
 
    RelativeLayout mTabLayoutContent;
 
    ImageView mTvRefreshBtn;
 
    View mViewDivider;
 
    NotScrollViewPager mVpSubcategoryContent; 
    NotScrollViewPager mVpSubcategoryContent2;
 
    ViewStub mVsLayoutErrorPage;

    protected int currentTabIndex = 0;
    protected boolean isBroadcast = false;//是否是在线广播
    private int orientation;
    private long[] subTitleIds, subTitleCityIds;
    private SubcategoryItemEvent subcategoryItem, subcategoryCityItem;//省数据,市数据


    @Override
    protected void changeViewLayoutForStatusBar(View view) {
        // Do-Nothing
        //因为是子fragment,所以不需要设置pading.
    }

    private CategoriesFragmentAdapter mAdapter;
    String[] ids;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        EventBus.getDefault().register(this);
        super.onCreate(savedInstanceState);
    }

    @Override
    public void onResume() {
        super.onResume();
        //皮肤切换需要重新刷新UI
        if (mStbSubcategoryTabTitle != null) {
            mStbSubcategoryTabTitle.setBackground(ResUtil.getDrawable(R.drawable.online_tab_category_two_bg));
            try {
                mStbSubcategoryTabTitle.getTitleView(currentTabIndex).setText(AppDateUtils.getInstance()
                        .getRadiusGradientSpan(getContext(), mStbSubcategoryTabTitle.getTitleView(currentTabIndex).getText().toString()));
                mStbSubcategoryTabTitle.getTitleView(currentTabIndex).getPaint().setFakeBoldText(true);
            } catch (Exception e) {
                Log.e(TAG, e.getMessage());
            }
        }

        if (mAdapter != null && mAdapter.getItem(currentTabIndex) != null
                && mAdapter.getItem(currentTabIndex) instanceof TabItemFragment) {
            ((TabItemFragment) mAdapter.getItem(currentTabIndex)).update();
        }
    }

    @Override
    public void showData(String[] titles, String[] ids, List<Fragment> fs, int showIndex) {
        if (titles == null || titles.length == 0) {
            return;
        }
        mStbSubcategoryTabTitle.setVisibility(View.VISIBLE);
        mStbSubcategoryTabTitle.setBackground(ResUtil.getDrawable(R.drawable.online_tab_category_two_bg));
        mStbSubcategoryTabTitleTwo.setBackground(ResUtil.getDrawable(R.drawable.online_tab_category_two_bg));
        stb_subcategory_tab_title_city.setBackground(ResUtil.getDrawable(R.drawable.online_tab_category_two_bg));
        this.ids = ids;
        mAdapter.updateFragments(fs);
        mVpSubcategoryContent.setOffscreenPageLimit(fs.size()-1);
        mStbSubcategoryTabTitle.setViewPager(mVpSubcategoryContent, titles);
        mStbSubcategoryTabTitle.setCurrentTab(showIndex);
        mStbSubcategoryTabTitle.getTitleView(showIndex).setHeight((int) getResources().getDimension(R.dimen.y56));
        mStbSubcategoryTabTitle.getTitleView(showIndex).setText(AppDateUtils.getInstance()
                .getRadiusGradientSpan(getContext(), mStbSubcategoryTabTitle.getTitleView(showIndex).getText().toString()));
        mStbSubcategoryTabTitle.getTitleView(showIndex).getPaint().setFakeBoldText(true);
        if (CategoryConstant.MEDIA_TYPE_BROADCAST == mediaType) {
            Drawable drawable = ResUtil.getDrawable(R.drawable.online_class_tab_div);
            //注意查看方法TextView.setCompoundDrawables(Drawable, Drawable, Drawable, Drawable)
            //的注释，要求设置的drawable必须已经通过Drawable.setBounds方法设置过边界参数
            //所以，此种方式下该行必不可少
            drawable.setBounds(0, 0, ResUtil.getDimen(R.dimen.m1), ResUtil.getDimen(R.dimen.m32));
            for (int i = 0; i < titles.length - 1; i++) {
                Log.i(TAG, "showData: titles = " + titles[i]);
                mStbSubcategoryTabTitle.getTitleView(i)
                        .setCompoundDrawables(null, null
                                , drawable, null);
                mStbSubcategoryTabTitle.getTitleView(i).setCompoundDrawablePadding(ResUtil.getDimen(R.dimen.m25));

            }
            mStbSubcategoryTabTitle.getTitleView(showIndex).setBackgroundResource(R.drawable.online_tab_indicator_pic_my);
            mStbSubcategoryTabTitle.setTabPadding(ResUtil.getDimen(R.dimen.m5));
        } else {
            mStbSubcategoryTabTitle.getTitleView(showIndex).setBackgroundResource(R.drawable.online_tab_indicator_pic);
            mStbSubcategoryTabTitle.setTabPadding(ResUtil.getDimen(R.dimen.m8));
        }
        currentTabIndex = showIndex;
        if (isBroadcast) {
            if (!mStbSubcategoryTabTitle.getTitleView(showIndex).getText().toString().equals("国家台"))
                tabTitleLl.setVisibility(View.VISIBLE);
            mStbSubcategoryTabTitleTwo.setTextSize(getResources().getDimension(R.dimen.online_all_ctg_sub_title_size));
            mStbSubcategoryTabTitleTwo.setTextSelectedSize(getResources().getDimension(R.dimen.online_all_ctg_sub_title_size));
            stb_subcategory_tab_title_city.setTextSize(getResources().getDimension(R.dimen.online_all_ctg_sub_title_size));
            stb_subcategory_tab_title_city.setTextSelectedSize(getResources().getDimension(R.dimen.online_all_ctg_sub_title_size));
        } else {
            tabTitleLl.setVisibility(View.GONE);
        }

    }

    @Subscribe
    public synchronized void subcategoryItemEvent(SubcategoryItemEvent subcategoryItem) {
        if (subcategoryItem == null || subcategoryItem.getData() == null
                || subcategoryItem.getData().get(0).getItemType() != SubcategoryItemBean.TYPE_ITEM_BROADCAST_CATEGORY) {
            return;
        }
        if (!mStbSubcategoryTabTitle.getTitleView(currentTabIndex).getText().toString().equals("省市台")
                && !mStbSubcategoryTabTitle.getTitleView(currentTabIndex).getText().toString().equals("按分类")) {
            return;
        }
        if (mStbSubcategoryTabTitle.getTitleView(currentTabIndex).getText().toString().equals("按分类")) {
            EventBus.getDefault().post(new OnlineShowCityTabEvent(false));
        }
        String[] subTitles = new String[subcategoryItem.getData().size()];


        if (subcategoryItem.getType() == 0) {
            subTitleIds = new long[subcategoryItem.getData().size()];
            for (int i = 0; i < subcategoryItem.getData().size(); i++) {
                subTitles[i] = subcategoryItem.getData().get(i).getName();
                subTitleIds[i] = subcategoryItem.getData().get(i).getId();
            }
            this.subcategoryItem = subcategoryItem;
            List<Tab> tabs = initTab(subTitles);
            mStbSubcategoryTabTitleTwo.setTabs(tabs);
            mStbSubcategoryTabTitleTwo.setCurrentTab(0);
            mStbSubcategoryTabTitleTwo.getTitleView(0).setText(AppDateUtils.getInstance()
                    .getRadiusGradientSpan(getContext(), mStbSubcategoryTabTitleTwo.getTitleView(0).getText().toString()));
            mStbSubcategoryTabTitleTwo.scrollToCurrentTab();
            mStbSubcategoryTabTitleTwo.getTitleView(0).getPaint().setFakeBoldText(true);
            if (!mStbSubcategoryTabTitleTwo.canScrollHorizontally(1)) {
                mStbSubcategoryTabTitleTwo.setPadding(0,0,0,0);
                slidingTwoGradientLeft.setVisibility(View.GONE);
                slidingTwoGradientRight.setVisibility(View.GONE);
            } else {
                mStbSubcategoryTabTitleTwo.setPadding(ResUtil.getDimen(R.dimen.m10),0,0,0);
                slidingTwoGradientLeft.setVisibility(View.VISIBLE);
                slidingTwoGradientRight.setVisibility(View.VISIBLE);
            }
            //加载市级数据
            mPresenter.loaCiData(subcategoryItem.getData().get(0).getId());
        } else {
            subTitleCityIds = new long[subcategoryItem.getData().size()];
            for (int i = 0; i < subcategoryItem.getData().size(); i++) {
                subTitles[i] = subcategoryItem.getData().get(i).getName();
                subTitleCityIds[i] = subcategoryItem.getData().get(i).getId();
            }
            this.subcategoryCityItem = subcategoryItem;
            List<Tab> tabs = initTab(subTitles);
            stb_subcategory_tab_title_city.setTabs(tabs);
            if (ListUtil.isEmpty(tabs)) {
                slidingCityGradientLeft.setVisibility(View.GONE);
                slidingCityGradientRight.setVisibility(View.GONE);
            } else {
                slidingCityGradientLeft.setVisibility(View.VISIBLE);
                slidingCityGradientRight.setVisibility(View.VISIBLE);
            }
            stb_subcategory_tab_title_city.setCurrentTab(0);
            stb_subcategory_tab_title_city.getTitleView(0).getPaint().setFakeBoldText(true);
            stb_subcategory_tab_title_city.getTitleView(0).setText(AppDateUtils.getInstance()
                    .getRadiusGradientSpan(getContext(), stb_subcategory_tab_title_city.getTitleView(0).getText().toString()));
            EventBus.getDefault().post(new OnlineShowCityTabEvent(true));

            //默认加载第一个城市
            if (subTitleIds != null && subTitleIds.length > 0 && mAdapter.getItem(currentTabIndex) instanceof TabItemFragment) {
                ((TabItemFragment) mAdapter.getItem(currentTabIndex)).changeTabItemDate((int) subTitleCityIds[0]);
            }
        }
    }

    private List<Tab> initTab(String[] titles) {
        List<Tab> tabList = new ArrayList<>();
        Tab tab;
        if (!mStbSubcategoryTabTitle.getTitleView(currentTabIndex).getText().toString().equals("国家台")) {
            for (int i = 0; i < titles.length; i++) {
                tab = new Tab();
                tab.title = titles[i];
                tabList.add(tab);
            }
        }
        return tabList;
    }

    @Override
    public void showContent(String[] titles, List<SubcategoryItemBean> itemBeans, int showIndex) {

    }

    @Override
    public void showError(Exception e) {
        // TODO: 2019/3/1 novelot 具体改
        Log.i(TAG, "showError: error = " + e.getMessage());
        String str = null;
        if (e instanceof ApiException) {
            switch (((ApiException) e).getCode()) {
                case ErrorCode.NO_NET:
                    str = ResUtil.getString(R.string.no_net_work_str);
                    break;
                case ErrorCode.NO_SUBCATEGORY:
                case ErrorCode.TYPE_ERROR:
                    str = ResUtil.getString(R.string.online_error_subcategory_is_null);
                    break;
                default:
            }
        }
        if (str != null) {
            ToastUtil.showError(getContext(), str);
        }

    }

    long showTabId;
    long mediaType;

    @Override
    protected void lazyLoad() {

        State currentState = this.getLifecycle().getCurrentState();
        if (currentState.isAtLeast(State.CREATED)) {
            Bundle arg = getArguments();
            if (arg != null) {
                showTabId = arg.getLong(CategoryConstant.CATEGORY_ID);
                mediaType = arg.getLong(CategoryConstant.MEDIA_TYPE, -1);

                switch ((int) mediaType) {
                    case CategoryConstant.MEDIA_TYPE_RADIO:
                        isBroadcast = false;
                        break;
                    case CategoryConstant.MEDIA_TYPE_BROADCAST:
                        isBroadcast = true;
                        showAccordingToScreen(orientation);
                        break;
                    default:
                        isBroadcast = false;
                        break;
                }
                mPresenter.loadData(showTabId);
            }
        }

    }

    @Override
    protected int getLayoutId() {
        return R.layout.online_fragment_purchased_horizontal;
    }


    @Subscribe
    public void showClassView(OnlineShowCityTabEvent showCityTabEvent) {
        changeCityTabShow(showCityTabEvent.isShow());
    }

    @Override
    public void initView(View view) {

        mLlSubcategoryRoot=view.findViewById(R.id.ll_subcategory_root);
        tabTitleLl=view.findViewById(R.id.tab_title_ll);
        tab_title_city_ll=view.findViewById(R.id.tab_title_city_ll);
        mStbSubcategoryTabTitle=view.findViewById(R.id.stb_subcategory_tab_title);
        mStbSubcategoryTabTitleTwo=view.findViewById(R.id.stb_subcategory_tab_title_two);
        slidingTwoGradientLeft=view.findViewById(R.id.slidingTwoGradientLeft);
        slidingTwoGradientRight=view.findViewById(R.id.slidingTwoGradientRight);
        stb_subcategory_tab_title_city=view.findViewById(R.id.stb_subcategory_tab_title_city);
        slidingCityGradientLeft=view.findViewById(R.id.slidingCityGradientLeft);
        slidingCityGradientRight=view.findViewById(R.id.slidingCityGradientRight);
        mTabLayoutContent=view.findViewById(R.id.tabLayoutContent);
        mTvRefreshBtn=view.findViewById(R.id.tv_refresh_btn);
        mViewDivider=view.findViewById(R.id.view_divider);
        mVpSubcategoryContent=view.findViewById(R.id.vp_subcategory_content);
        mVpSubcategoryContent2=view.findViewById(R.id.vp_subcategory_content2);
        mVsLayoutErrorPage=view.findViewById(R.id.vs_layout_error_page);
 
        
        setupViews();
        // 调试用,使用该句,可以显示界面;去掉后不显示
        //mPresenter.loadData();
    }

    private void setupViews() {
        if (mAdapter == null) {
            mAdapter = new CategoriesFragmentAdapter(getChildFragmentManager(), null, null);
        }
        mVpSubcategoryContent.setAdapter(mAdapter);
        //配合lazyfragment,避免预加载
        mVpSubcategoryContent.setOffscreenPageLimit(0);
        mStbSubcategoryTabTitle.setViewPager(mVpSubcategoryContent);
        mStbSubcategoryTabTitle.setOnTabSelectListener(new OnTabSelectListener() {
            @Override
            public synchronized void onTabSelect(int position) {
                if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), true)) {
                    mStbSubcategoryTabTitle.setCurrentTab(currentTabIndex);
                    return;
                }
                if (position == mStbSubcategoryTabTitle.getTabCount() - 1 || !isBroadcast) {
                    mStbSubcategoryTabTitle.getTitleView(position).setBackgroundResource(R.drawable.online_tab_indicator_pic);
                } else {
                    mStbSubcategoryTabTitle.getTitleView(position).setBackgroundResource(R.drawable.online_tab_indicator_pic_my);
                }
                mStbSubcategoryTabTitle.getTitleView(position).setHeight((int) getResources().getDimension(R.dimen.y56));
                mStbSubcategoryTabTitle.getTitleView(position).setText(AppDateUtils.getInstance()
                        .getRadiusGradientSpan(getContext(), mStbSubcategoryTabTitle.getTitleView(position).getText().toString()));
                mStbSubcategoryTabTitle.getTitleView(position).getPaint().setFakeBoldText(true);

                mStbSubcategoryTabTitle.getTitleView(currentTabIndex).getPaint().setFakeBoldText(false);
                mStbSubcategoryTabTitle.getTitleView(currentTabIndex).setBackgroundResource(0);
                mStbSubcategoryTabTitle.getTitleView(currentTabIndex).setTextColor(ResUtil.getColor(R.color.user_info_key_color));
                mStbSubcategoryTabTitle.getTitleView(currentTabIndex).setText(mStbSubcategoryTabTitle
                        .getTitleView(currentTabIndex).getText().toString());

                if (isBroadcast) {
                    ButtonClickReportEvent event = null;
                    if (mStbSubcategoryTabTitle.getTitleView(position).getText().toString().equals("国家台")) {
                        tabTitleLl.setVisibility(View.GONE);
                        EventBus.getDefault().post(new OnlineShowCityTabEvent(false));

                        event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_CLASS_GB_COUNTRIES);
                        ReportHelper.getInstance().addEvent(event);
                    } else if (mStbSubcategoryTabTitle.getTitleView(position).getText().toString().equals("本地广播")) {
                        tabTitleLl.setVisibility(View.GONE);
                        EventBus.getDefault().post(new OnlineShowCityTabEvent(false));
//                        event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_CLASS_GB_COUNTRIES);
//                        ReportHelper.getInstance().addEvent(event);
                    } else if (mStbSubcategoryTabTitle.getTitleView(position).getText().toString().equals("省市台")) {
                        tabTitleLl.setVisibility(View.VISIBLE);
                        if (mAdapter.getItem(position) instanceof TabItemFragment) {
                            ((TabItemFragment) mAdapter.getItem(position)).update();
                        }
                        event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_CLASS_GB_CITY);
                        ReportHelper.getInstance().addEvent(event);
                    } else if (mStbSubcategoryTabTitle.getTitleView(position).getText().toString().equals("按分类")) {
                        tabTitleLl.setVisibility(View.VISIBLE);
                        if (mAdapter.getItem(position) instanceof TabItemFragment) {
                            ((TabItemFragment) mAdapter.getItem(position)).update();
                        }
                        EventBus.getDefault().post(new OnlineShowCityTabEvent(false));
                        event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_CLASS_GB_CLASS);
                        ReportHelper.getInstance().addEvent(event);
                    }
                } else {
                    tabTitleLl.setVisibility(View.GONE);
                    EventBus.getDefault().post(new OnlineShowCityTabEvent(false));
                }

                currentTabIndex = position;
            }

            @Override
            public void onTabReselect(int position) {

            }
        });
        mStbSubcategoryTabTitleTwo.setOnTabSelectListener(new com.kaolafm.kradio.lib.widget.tab.OnTabSelectListener() {
            int index = 0;

            @Override
            public void onTabSelect(int position) {

                if (mStbSubcategoryTabTitle.getTitleView(currentTabIndex).getText().toString().equals("省市台")
                        && (subcategoryItem.getData().size() > position && subcategoryItem.getData().get(position).getType().equals(CategoryConstant.TYPE_CATEGORY))) {
                    mPresenter.loaCiData(subcategoryItem.getData().get(position).getId());
                    EventBus.getDefault().post(new OnlineShowCityTabEvent(true));
                } else {
                    EventBus.getDefault().post(new OnlineShowCityTabEvent(false));
                    if (subTitleIds != null && subTitleIds.length > 0 && mAdapter.getItem(currentTabIndex) instanceof TabItemFragment) {
                        ((TabItemFragment) mAdapter.getItem(currentTabIndex)).changeTabItemDate((int) subTitleIds[position]);
                    }
                }
                mStbSubcategoryTabTitleTwo.getTitleView(position).getPaint().setFakeBoldText(true);
                mStbSubcategoryTabTitleTwo.getTitleView(position).setText(AppDateUtils.getInstance()
                        .getRadiusGradientSpan(getContext(), mStbSubcategoryTabTitleTwo.getTitleView(position).getText().toString()));
                if (index < mStbSubcategoryTabTitleTwo.getTabCount()) {
                    mStbSubcategoryTabTitleTwo.getTitleView(index).getPaint().setFakeBoldText(false);
                    mStbSubcategoryTabTitleTwo.getTitleView(index).setTextColor(ResUtil.getColor(R.color.user_info_key_color));
                    mStbSubcategoryTabTitleTwo.getTitleView(index).setText(mStbSubcategoryTabTitleTwo
                            .getTitleView(index).getText().toString());
                }
                index = position;
            }

            @Override
            public void onTabReselect(int position) {
                if (mStbSubcategoryTabTitle.getTitleView(currentTabIndex).getText().toString().equals("省市台")
                        && subcategoryItem.getData().get(position).getType().equals(CategoryConstant.TYPE_CATEGORY)) {
//                    changeCityTabShow(true);
                    EventBus.getDefault().post(new OnlineShowCityTabEvent(true));
                }
            }
        });
        stb_subcategory_tab_title_city.setOnTabSelectListener(new com.kaolafm.kradio.lib.widget.tab.OnTabSelectListener() {
            int index = 0;

            @Override
            public void onTabSelect(int position) {
//                if (subTitleIds != null && subTitleIds.length > 0 && mAdapter.getItem(currentTabIndex) instanceof TabItemFragment) {
//                    ((TabItemFragment) mAdapter.getItem(currentTabIndex)).changeTabItemDate((int) subTitleIds[position]);
//                }
                //加载城市数据
                if (subTitleIds != null && subTitleIds.length > 0 && mAdapter.getItem(currentTabIndex) instanceof TabItemFragment) {
                    ((TabItemFragment) mAdapter.getItem(currentTabIndex)).changeTabItemDate((int) subTitleCityIds[position]);
                }
                EventBus.getDefault().post(new OnlineShowCityTabEvent(false));

                stb_subcategory_tab_title_city.getTitleView(position).setText(AppDateUtils.getInstance()
                        .getRadiusGradientSpan(getContext(), stb_subcategory_tab_title_city.getTitleView(position).getText().toString()));
                stb_subcategory_tab_title_city.getTitleView(position).getPaint().setFakeBoldText(true);
                if (index < stb_subcategory_tab_title_city.getTabCount()) {
                    stb_subcategory_tab_title_city.getTitleView(index).getPaint().setFakeBoldText(false);
                    stb_subcategory_tab_title_city.getTitleView(index).setTextColor(ResUtil.getColor(R.color.user_info_key_color));
                    stb_subcategory_tab_title_city.getTitleView(index).setText(stb_subcategory_tab_title_city
                            .getTitleView(index).getText().toString());
                }
                index = position;
            }

            @Override
            public void onTabReselect(int position) {

            }
        });
        tab_title_city_ll.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                changeCityTabShow(false);
                EventBus.getDefault().post(new OnlineShowCityTabEvent(false));
            }
        });
    }

    /**
     * 控制市选择tab的隐藏显示
     *
     * @param b
     */
    private void changeCityTabShow(boolean b) {
        if (b) {
            tab_title_city_ll.setVisibility(View.VISIBLE);
            stb_subcategory_tab_title_city.setVisibility(View.VISIBLE);
            slidingCityGradientLeft.setVisibility(View.VISIBLE);
            slidingCityGradientRight.setVisibility(View.VISIBLE);
        } else {
            tab_title_city_ll.setVisibility(View.GONE);
            stb_subcategory_tab_title_city.setVisibility(View.GONE);
            slidingCityGradientLeft.setVisibility(View.GONE);
            slidingCityGradientRight.setVisibility(View.GONE);

        }
    }

    @Override
    protected void showAccordingToScreen(int orientation) {
        this.orientation = orientation;
        int paddingRight = ScreenUtil.getGlobalPaddingRight(orientation);

        //因为mTabLayout的item如果设置了item的padding,为了与页面的返回按钮对齐,做特殊处理;
        int paddingLeft = (int) (ResUtil.getDimen(R.dimen.online_subcategory_tab_padding_left) - mStbSubcategoryTabTitle.getTabPadding());

        MarginLayoutParams layoutParams = (MarginLayoutParams) mStbSubcategoryTabTitle.getLayoutParams();
        layoutParams.leftMargin = paddingLeft;
        layoutParams.rightMargin = paddingLeft;
        if (isBroadcast) {
            layoutParams.rightMargin = paddingLeft / 2;
            MarginLayoutParams layoutParams2 = (MarginLayoutParams) mStbSubcategoryTabTitleTwo.getLayoutParams();
            layoutParams2.rightMargin = paddingLeft;
            MarginLayoutParams layoutParams3 = (MarginLayoutParams) stb_subcategory_tab_title_city.getLayoutParams();
            layoutParams3.rightMargin = paddingLeft;

        }


        MarginLayoutParams layoutParamsRefresh = (MarginLayoutParams) mTvRefreshBtn.getLayoutParams();
        layoutParamsRefresh.rightMargin = paddingRight;

//        mAdapter.notifyDataSetChanged();
        mStbSubcategoryTabTitle.postDelayed(runnable = new Runnable() {
            @Override
            public void run() {
                if (mStbSubcategoryTabTitle != null) { //fixed 快速点击全部，返回会因为mStbSubcategoryTabTitle为null导致崩溃，因此需要判空处理
                    Log.i(TAG, "delayScroll:" + mStbSubcategoryTabTitle);
                    mStbSubcategoryTabTitle.onPageScrolled(mStbSubcategoryTabTitle.getCurrentTab(),
                            0, 0);
                }
            }
        }, 200);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        //fixed 快速点击全部，返回会因为mStbSubcategoryTabTitle为null导致崩溃，由于post处理时，view已被销毁，因此需要在此处移除post处理
        if (runnable != null && mStbSubcategoryTabTitle != null) {
            Log.i(TAG, "removeCallbacks:" + runnable);
            mStbSubcategoryTabTitle.removeCallbacks(runnable);
        }
        if (runnable != null && isBroadcast && mStbSubcategoryTabTitleTwo != null) {
            Log.i(TAG, "removeCallbacks:" + runnable);
            mStbSubcategoryTabTitleTwo.removeCallbacks(runnable);
        }
    }
}
