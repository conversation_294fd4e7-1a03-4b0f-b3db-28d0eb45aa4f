<?xml version="1.0" encoding="utf-8"?>
<com.kaolafm.kradio.common.widget.CScaleLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/similar_re_main_layout"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/similar_re_image_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/similar_re_imageView"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <View
            android:id="@+id/similar_re_cover_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/card_big_fg_playing"
            android:visibility="gone" />

        <com.kaolafm.kradio.common.widget.CTextView
            android:id="@+id/similar_re_listen_num_textView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:background="@drawable/trans_to_30_black_gradient_bg"
            android:ellipsize="end"
            android:gravity="center_vertical|right"
            android:maxLines="1"
            android:padding="@dimen/similar_re_num_padding"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/text_size1" />

        <com.kaolafm.kradio.common.widget.PlayingIndicator xmlns:app="http://schemas.android.com/apk/res-auto"
            android:id="@+id/similar_re_playingIndicator"
            android:layout_width="@dimen/player_list_playing_bar_size"
            android:layout_height="@dimen/player_list_playing_bar_size"
            android:layout_alignParentBottom="true"
            android:layout_margin="@dimen/similar_re_num_padding"
            app:bar_color="@color/play_indicator_color"
            app:bar_num="@integer/playing_bar_num"
            app:duration="@integer/playing_bar_duration"
            app:step_num="@integer/playing_bar_step_num" />
    </RelativeLayout>

    <com.kaolafm.kradio.common.widget.CTextView
        android:id="@+id/similar_re_name_textView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="2"
        android:paddingBottom="@dimen/y10"
        android:paddingTop="@dimen/y10"
        android:textColor="@color/colorWhite"
        android:textSize="@dimen/text_size3" />
</com.kaolafm.kradio.common.widget.CScaleLinearLayout>