package com.kaolafm.kradio.history.comprehensive.ui;

import android.content.res.Configuration;
import android.graphics.Rect;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;
import android.view.View;

import com.kaolafm.kradio.lib.bean.HistoryItem;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.utils.ResUtil;

/**
 * 历史列表间隔装饰。没有复用其他的是因为需要判断去登陆item和标题item
 *
 * <AUTHOR>
 * @date 2020/8/21
 */
public class HistoryItemDecoration extends RecyclerView.ItemDecoration {
    private boolean isUserLogin = false;

    public HistoryItemDecoration(boolean isUserLogin) {
        this.isUserLogin = isUserLogin;
    }

    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
        int adapterPosition = parent.getChildAdapterPosition(view);
        int type;
        try {
            type = parent.getLayoutManager().getItemViewType(view);
        } catch (Exception e) {
            type = -1;
        }
        boolean isSpecItem = type == HistoryAdapter.HISTORY_COUNT_TITLE || type == HistoryAdapter.HEAD_UNLOGIN_TIP;
        if (isUserLogin) {
            if (adapterPosition == 0 && isSpecItem) {
                outRect.bottom = ResUtil.getDimen(R.dimen.m10);
            } else {
                outRect.bottom = ResUtil.getDimen(R.dimen.m24);
            }
        } else {
            if (adapterPosition == 0) {
                outRect.bottom = 0;
            } else if (adapterPosition == 1 && isSpecItem) {
                outRect.bottom = ResUtil.getDimen(R.dimen.m10);
            } else {
                outRect.bottom = ResUtil.getDimen(R.dimen.m24);
            }
        }
        if (type == HistoryAdapter.HISTORY_COUNT_TITLE || type == HistoryAdapter.HEAD_UNLOGIN_TIP) {
            return;
        }
        if (ResUtil.getOrientation() == Configuration.ORIENTATION_PORTRAIT) {
            return;
        }
        if (isLastColumn(view, parent)) {
            outRect.right = 0;
            outRect.left = ResUtil.getDimen(R.dimen.m25);
        } else {
            outRect.left = 0;
            outRect.right = ResUtil.getDimen(R.dimen.m25);
        }

    }

    private boolean isLastColumn(View view, RecyclerView parent) {
        final int spanCount = getSpanCount(parent);
        final int position = parent.getChildAdapterPosition(view);
        BaseAdapter<HistoryItem> adapter = (BaseAdapter<HistoryItem>) parent.getAdapter();
        HistoryItem itemData = adapter.getItemData(0);
        if (itemData.getTypeId() == HistoryAdapter.HISTORY_COUNT_TITLE) {
            return position % spanCount == 0;
        } else {
            return (position + 1) % spanCount == 0;
        }
    }

    private int getSpanCount(RecyclerView parent) {
        RecyclerView.LayoutManager layoutManager = parent.getLayoutManager();

        if (layoutManager instanceof GridLayoutManager) {
            return ((GridLayoutManager) layoutManager).getSpanCount();
        } else if (layoutManager instanceof StaggeredGridLayoutManager) {
            return ((StaggeredGridLayoutManager) layoutManager).getSpanCount();
        } else {
            throw new UnsupportedOperationException("the GridDividerItemDecoration can only be used in " +
                    "the RecyclerView which use a GridLayoutManager or StaggeredGridLayoutManager");
        }
    }
}
