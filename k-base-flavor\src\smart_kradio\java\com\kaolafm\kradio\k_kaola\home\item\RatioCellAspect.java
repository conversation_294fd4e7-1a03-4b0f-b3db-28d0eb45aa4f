package com.kaolafm.kradio.k_kaola.home.item;

import android.util.Log;

import com.kaolafm.kradio.category.radio.tab.RadioTabFragment;
import com.kaolafm.kradio.k_kaolafm.home.item.GoldenRatioCell;
import com.kaolafm.kradio.lib.utils.ResUtil;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;

@Aspect
public class RatioCellAspect {

    private String TAG = "RatioCellAspect";

    @Around("execution(* GoldenRatioCell.changeHomeTitleSize(..))")
    public void chageHomeTitleSize(ProceedingJoinPoint point) throws Throwable {
        Log.i(TAG,"chageHomeTitleSize...");
        GoldenRatioCell goldenRatioCell = (GoldenRatioCell) point.getThis();
        Log.i(TAG,"ProceedingJoinPoint:"+goldenRatioCell);
        Log.i(TAG,"origin params ::"+(int)point.getArgs()[0]);
        int size = ResUtil.getDimen(com.kaolafm.kradio.k_kaolafm.R.dimen.text_size_title5);
        Integer [] params = {size};
        Log.i(TAG,"changed params::"+params[0]);
        point.proceed(params);
    }
}
