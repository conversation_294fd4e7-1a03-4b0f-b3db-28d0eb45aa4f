package com.kaolafm.kradio.flavor.impl;

import android.util.Log;


import com.kaolafm.sdk.core.mediaplayer.OnHandleAudioFocusListener;

import static android.media.AudioManager.AUDIOFOCUS_LOSS_TRANSIENT;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-07-12 11:53
 ******************************************/
public final class OnHandleAudioFocusImpl implements OnHandleAudioFocusListener {
    private static final String TAG = "OnHandleAudioFocusImpl";

    @Override
    public boolean onAudioFocusDuck(Object... objects) {
        return false;
    }

    @Override
    public boolean onAudioFocusGain(Object... objects) {
        return false;
    }

    @Override
    public boolean onAudioFocusLoss(Object... objects) {
        int focusChange = (int) objects[0];
        Log.i(TAG, "onAudioFocusLoss: focusChange = " + focusChange);
        if (focusChange == AUDIOFOCUS_LOSS_TRANSIENT) {
            KLAutoPlayerManager.getInstance().setVolume(0.3F, 0.3F);
            return true;
        } else {
            return false;
        }
    }
}
