package com.kaolafm.kradio.widget;

import android.app.PendingIntent;
import android.appwidget.AppWidgetManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.RemoteViews;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.common.CP;
import com.kaolafm.kradio.common.ErrorInfo;
import com.kaolafm.kradio.common.ResultCallback;
import com.kaolafm.kradio.common.SubscribeData;
import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.k_kaolafm.home.player.PlayerHelper;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.IntentUtils;
import com.kaolafm.kradio.lib.utils.UrlUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.lib.utils.imageloader.listener.OnGetBitmapListener;
import com.kaolafm.kradio.network.FlavorApiRequest;
import com.kaolafm.kradio.network.model.DefaultPlayData;
import com.kaolafm.kradio.player.bean.PlayerRadioListItem;


import com.kaolafm.kradio.player.manager.LiveBroadcastPlayerManager;
import com.kaolafm.kradio.player.manager.PlayerRadioListManager;
import com.kaolafm.kradio.subscribe.SubscribeChangeListener;
import com.kaolafm.kradio.subscribe.SubscribeManager;
import com.kaolafm.kradio.subscribe.SubscribeModel;
import com.kaolafm.kradio.subscribe.SubscribeRepository;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.report.event.SubscibeReportEvent;
import com.kaolafm.sdk.core.mediaplayer.IPlayerStateListener;

import com.kaolafm.utils.MediaSessionUtil;
import java.util.List;

/**
 * <pre>
 *     <AUTHOR> Wenchl
 *     e-mail : <EMAIL>
 *     time   : 2019/04/04
 *     desc   :
 *     version: 1.0
 * </pre>
 */
public class WidgetUpdateManager implements IPlayerStateListener, SubscribeChangeListener, IPlayChangedListener {

    private static final String TAG = "WidgetUpdateManager";

    private Context context = AppDelegate.getInstance().getContext();

    private SubscribeModel subscribeModel;

    private Bitmap curBitmap;

    private Bitmap curBrandRadioBitmap;

    private boolean subscription;

    public static final String KEY_SUBSCRIBE_ID = "key_subscribe_id";

    private DefaultPlayData mDefaultPlayData;

    private boolean setNeedInit = false;

    private static class WidgetUpdateManagerInstance{
        private static final WidgetUpdateManager INSTANCE = new WidgetUpdateManager();
    }

    public static WidgetUpdateManager getInstance() {
        return WidgetUpdateManagerInstance.INSTANCE;
    }

    public void init(){
        Log.i(TAG,"init:"+setNeedInit);
        if (setNeedInit) {
            return;
        }
        PlayerManager.getInstance().addPlayControlStateCallback(this);
        KLAutoPlayerManager.getInstance().addIPlayChangedListener(this);
        ((SubscribeRepository) SubscribeManager.getInstance(context, CP.KaoLaFM)).addSubscribeChangeListener(this);
        subscribeModel = new SubscribeModel();
        MediaSessionUtil.getInstance().registerMediaSession();
        setNeedInit = true;
    }

    public void destroy() {
        Log.i(TAG, "destroy");
        PlayerManager.getInstance().removePlayControlStateCallback(this);
        PlayerManager.getInstance().removeIPlayChangedListener(this);
        ((SubscribeRepository) SubscribeManager.getInstance(context, CP.KaoLaFM)).removeSubscribeChangeListener(this);
    }

    public void updateWidgetInfo(PlayItem playItem, Bitmap bitmap) {
        boolean isShowRadio = isShowRadio();
        Log.i(TAG, "updateWidgetInfo playItem:" + playItem + " isBrandsRadio:" + isShowRadio + " mDefaultPlayData:" + mDefaultPlayData);
        if (playItem != null && isShowRadio) {
            AppWidgetManager appWidgetManager = AppWidgetManager.getInstance(context);
            ComponentName componentName = new ComponentName(context, YiKaTongAppWidgetProvider.class);
            RemoteViews views = new RemoteViews(context.getPackageName(), R.layout.widget_layout);

            //设置图片
            if (bitmap != null) {
                views.setImageViewBitmap(R.id.widget_radio_cover, bitmap);
            }

            //设置广播flag
            int flagType = PlayerHelper.getPlayFlagType(playItem);
            setFlag(views, flagType);

            //设置点击进入kradio 事件
            Intent intent = IntentUtils.getInstance().getLauncherIntentUseWidget(context);
            views.setOnClickPendingIntent(R.id.widget_radio_cover, PendingIntent.getActivity(context, 0, intent, 0));
            views.setOnClickPendingIntent(R.id.widget_layout, PendingIntent.getActivity(context, 0, intent, 0));

            //设置album name
            views.setTextViewText(R.id.widget_radio_name, KLAutoPlayerManager.getInstance().getRadioName());

            //设置audio name
            views.setTextViewText(R.id.widget_audio_name, PlayItemUtils.getPlayItemAudioName(playItem));

            int whichPlayer = PlayerHelper.whichPlayer();

            if (PlayerHelper.PLAYER_TYPE_LIVE == whichPlayer) {
                //直播中按钮要置灰
                views.setImageViewResource(R.id.widget_play_pause, R.drawable.widget_pause_unselected);
                views.setImageViewResource(R.id.widget_left_btn, R.drawable.widget_prev_unselected);
                views.setImageViewResource(R.id.widget_right_btn, R.drawable.widget_next_unselected);
            } else {
                //设置非直播情况下 widget 上/下一首，以及点击事件

                //设置播放暂停事件、UI
                if (PlayerManager.getInstance().isPlaying()) {
                    views.setImageViewResource(R.id.widget_play_pause, R.drawable.selector_widget_pause);
                    views.setOnClickPendingIntent(R.id.widget_play_pause, getPendingIntent(context, YiKaTongAppWidgetProvider.PAUSE_ACTION));
                } else {
                    views.setImageViewResource(R.id.widget_play_pause, R.drawable.selector_widget_play);
                    views.setOnClickPendingIntent(R.id.widget_play_pause, getPendingIntent(context, YiKaTongAppWidgetProvider.PLAY_ACTION));
                }

                boolean hasNext = KLAutoPlayerManager.getInstance().hasNext();
                boolean hasPrev = KLAutoPlayerManager.getInstance().hasPre();

                //广播类型有上一首，pgc 专辑 改为订阅按钮
                if (whichPlayer == PlayerHelper.PLAYER_TYPE_BROADCAST) {

                    if (hasPrev) {
                        views.setOnClickPendingIntent(R.id.widget_left_btn, getPendingIntent(context, YiKaTongAppWidgetProvider.PREV_ACTION));
                        views.setImageViewResource(R.id.widget_left_btn, R.drawable.selector_widget_prev);
                    } else {
                        views.setOnClickPendingIntent(R.id.widget_left_btn, null);
                        views.setImageViewResource(R.id.widget_left_btn, R.drawable.widget_prev_unselected);
                    }

                } else {
//                    Bundle bundle = new Bundle();
//                    bundle.putLong(KEY_SUBSCRIBE_ID,playItem.getAlbumId());
                    views.setOnClickPendingIntent(R.id.widget_left_btn, getPendingIntent(context, YiKaTongAppWidgetProvider.SUBSCRIPTION_ACTION));

                    if (subscription) {
                        views.setImageViewResource(R.id.widget_left_btn, R.drawable.widget_subscription);
                    } else {
                        views.setImageViewResource(R.id.widget_left_btn, R.drawable.widget_unsubscription);
                    }
                }

                if (hasNext) {
                    views.setOnClickPendingIntent(R.id.widget_right_btn, getPendingIntent(context, YiKaTongAppWidgetProvider.NEXT_ACTION));
                    views.setImageViewResource(R.id.widget_right_btn, R.drawable.selector_widget_next);
                } else {
                    views.setOnClickPendingIntent(R.id.widget_right_btn, null);
                    views.setImageViewResource(R.id.widget_right_btn, R.drawable.widget_next_unselected);
                }

            }

            appWidgetManager.updateAppWidget(componentName, views);

        } else {

            //设置播其他节目时，品牌电台的展示情况
            if (mDefaultPlayData != null) {
                AppWidgetManager appWidgetManager = AppWidgetManager.getInstance(context);
                ComponentName componentName = new ComponentName(context, YiKaTongAppWidgetProvider.class);
                RemoteViews views = new RemoteViews(context.getPackageName(), R.layout.widget_layout);

                //设置点击进入kradio 事件
                Intent intent = IntentUtils.getInstance().getLauncherIntentUseWidget(context);
                views.setOnClickPendingIntent(R.id.widget_radio_cover, PendingIntent.getActivity(context, 0, intent, 0));
                views.setOnClickPendingIntent(R.id.widget_layout, PendingIntent.getActivity(context, 0, intent, 0));


                //设置播放事件
                views.setImageViewResource(R.id.widget_play_pause, R.drawable.selector_widget_play);
                views.setOnClickPendingIntent(R.id.widget_play_pause, getPendingIntent(context, YiKaTongAppWidgetProvider.PLAY_ACTION));

                //设置图片
                if (curBrandRadioBitmap != null) {
                    views.setImageViewBitmap(R.id.widget_radio_cover, curBrandRadioBitmap);
                }

                views.setOnClickPendingIntent(R.id.widget_right_btn, null);
                views.setImageViewResource(R.id.widget_right_btn, R.drawable.widget_next_unselected);

                views.setOnClickPendingIntent(R.id.widget_left_btn, null);
                views.setImageViewResource(R.id.widget_left_btn, R.drawable.widget_subscription_unselected);

                //设置album name
                views.setTextViewText(R.id.widget_radio_name, "");

                //设置audio name
                views.setTextViewText(R.id.widget_audio_name, mDefaultPlayData.getName());

                appWidgetManager.updateAppWidget(componentName, views);

            } else {
                AppWidgetManager appWidgetManager = AppWidgetManager.getInstance(context);
                ComponentName componentName = new ComponentName(context, YiKaTongAppWidgetProvider.class);
                RemoteViews views = new RemoteViews(context.getPackageName(), R.layout.widget_layout);
                Intent intent = IntentUtils.getInstance().getLauncherIntentUseWidget(context);
                //设置点击进入kradio 事件
                views.setOnClickPendingIntent(R.id.widget_radio_cover, PendingIntent.getActivity(context, 0, intent, 0));
                views.setOnClickPendingIntent(R.id.widget_layout, PendingIntent.getActivity(context, 0, intent, 0));

                views.setOnClickPendingIntent(R.id.widget_play_pause, getPendingIntent(context, YiKaTongAppWidgetProvider.PLAY_ACTION));

                views.setImageViewResource(R.id.widget_right_btn, R.drawable.widget_next_unselected);

                views.setImageViewResource(R.id.widget_left_btn, R.drawable.widget_subscription_unselected);

                appWidgetManager.updateAppWidget(componentName, views);

            }
        }

    }

    private void setFlag(RemoteViews remoteViews, int flag) {
        switch (flag) {
            case PlayerHelper.FLAG_TYPE_NON:
                //隐藏直播角标
                remoteViews.setViewVisibility(R.id.widget_radio_flag, View.GONE);
                break;
            case PlayerHelper.FLAG_TYPE_LIVE:
                //显示直播角标
                remoteViews.setViewVisibility(R.id.widget_radio_flag, View.VISIBLE);
                remoteViews.setImageViewResource(R.id.widget_radio_flag, R.drawable.widget_live_flag);
                break;
            case PlayerHelper.FLAG_TYPE_PLAYBACK:
                //显示回放角标
                remoteViews.setViewVisibility(R.id.widget_radio_flag, View.VISIBLE);
                remoteViews.setImageViewResource(R.id.widget_radio_flag, R.drawable.widget_playback_flag);
                break;
            default:
                remoteViews.setViewVisibility(R.id.widget_radio_flag, View.GONE);
                break;
        }
    }

    public void getBitmap(final PlayItem playItem) {
        if (playItem == null) {
            return;
        }
        String url = playItem.getPic();

        if (TextUtils.isEmpty(url)) {
            PlayerRadioListItem curPlayerRadioListItem = PlayerRadioListManager.getInstance().getCurRadioItem();
            if (curPlayerRadioListItem != null && !TextUtils.isEmpty(curPlayerRadioListItem.getPicUrl())) {
                url = curPlayerRadioListItem.getPicUrl();
            }
        }

        String picUrl = UrlUtil.getCustomPicUrl(UrlUtil.PIC_250_250, url);

        final int whichPlayer = PlayerHelper.whichPlayer();

        ImageLoader.getInstance().getBitmapFromCache(context, picUrl, new OnGetBitmapListener() {
            @Override
            public void onBitmap(Bitmap bitmap) {
                PlayItem curPlayItem;
                if (whichPlayer == PlayerHelper.PLAYER_TYPE_LIVE) {
                    curPlayItem = LiveBroadcastPlayerManager.getInstance().getPlayItem();
                } else {
                    curPlayItem = PlayerManager.getInstance().getCurPlayItem();
                }
                if (curPlayItem != null && curPlayItem.getAudioId() == playItem.getAudioId()) {
                    curBitmap = bitmap;
                    updateWidgetInfo(playItem, bitmap);
                }
            }
        });
    }

    private void getBitmapForBrand(String url) {
        String picUrl = UrlUtil.getCustomPicUrl(UrlUtil.PIC_250_250, url);
        Log.i(TAG,"getBitmapForBrand url:"+picUrl);
        ImageLoader.getInstance().getBitmapFromCache(context, picUrl, bitmap -> {
            curBrandRadioBitmap = bitmap;
//            updateWidgetInfo(PlayerManager.getInstance().getCurPlayItem(),curBrandRadioBitmap);
        });
    }

    private PendingIntent getPendingIntent(Context context, String action) {
        return getPendingIntent(context, action, null);
    }

    private PendingIntent getPendingIntent(Context context, String action, Bundle bundle) {
        Intent intent = new Intent();
        intent.setClass(context, YiKaTongAppWidgetProvider.class);
        intent.setAction(action);
        if (bundle != null) {
            intent.putExtras(bundle);
        }
        PendingIntent pendingIntent = PendingIntent.getBroadcast(context, 0, intent, 0);
        return pendingIntent;
    }

    public void widgetSubscribe(final long id) {
        if (subscribeModel == null) {
            Log.i(TAG, "subscribeModel null");
            return;
        }
        subscribeModel.isSubscribed(String.valueOf(id), new ResultCallback() {
            @Override
            public void onResult(boolean result, int code) {
                if (result) {
                    unSubscribe(id);
                } else {
                    subscribe(id);
                }
            }

            @Override
            public void onFailure(ErrorInfo errorInfo) {

            }
        });
    }

    private void subscribe(long id) {
        if (subscribeModel == null) {
            Log.i(TAG, "subscribeModel null");
            return;
        }
        SubscribeData subscribeData = new SubscribeData();
        subscribeData.setId(id);
        subscribeModel.subscribe(subscribeData, new ResultCallback() {
            @Override
            public void onResult(boolean result, int code) {
                subscription = result;
                updateWidgetInfo(PlayerManager.getInstance().getCurPlayItem(), curBitmap);
            }

            @Override
            public void onFailure(ErrorInfo errorInfo) {

            }
        });
    }

    private void unSubscribe(long id) {
        if (subscribeModel == null) {
            return;
        }
        SubscribeData subscribeData = new SubscribeData();
        subscribeData.setId(id);
        subscribeData.setLocation(SubscibeReportEvent.POSITION_WIDGET);
        subscribeModel.unsubscribe(subscribeData, new ResultCallback() {
            @Override
            public void onResult(boolean result, int code) {
                if (result) {
                    subscription = false;
                }
                updateWidgetInfo(PlayerManager.getInstance().getCurPlayItem(), curBitmap);

            }

            @Override
            public void onFailure(ErrorInfo errorInfo) {

            }
        });
    }

    private void setSubscribeState() {
        if (subscribeModel == null) {
            Log.i(TAG, "subscribeModel null");
            return;
        }
        subscribeModel.isSubscribed(String.valueOf(PlayerHelper.getSubscribeId()), new ResultCallback() {
            @Override
            public void onResult(boolean result, int code) {
                subscription = result;
                updateWidgetInfo(PlayerManager.getInstance().getCurPlayItem(), curBitmap);
            }

            @Override
            public void onFailure(ErrorInfo errorInfo) {

            }
        });
    }

    @Override
    public void onIdle(PlayItem playItem) {

    }

    @Override
    public void onPlayerPreparing(PlayItem playItem) {
    }

    @Override
    public void onPlayerPlaying(PlayItem playItem) {
        Log.i(TAG, "onPlayerPlaying");
        updateWidgetInfo(playItem, curBitmap);
    }

    @Override
    public void onPlayerPaused(PlayItem playItem) {
        updateWidgetInfo(playItem, curBitmap);
    }

    @Override
    public void onProgress(String s, int i, int i1, boolean b) {

    }

    @Override
    public void onPlayerFailed(PlayItem playItem, int i, int i1) {
        updateWidgetInfo(playItem, curBitmap);
    }

    @Override
    public void onPlayerEnd(PlayItem playItem) {
        updateWidgetInfo(playItem, curBitmap);
    }

    @Override
    public void onSeekStart(String s) {

    }

    @Override
    public void onSeekComplete(String s) {

    }

    @Override
    public void onBufferingStart(PlayItem playItem) {

    }

    @Override
    public void onBufferingEnd(PlayItem playItem) {

    }

    @Override
    public void onSubscribesChanged(List<SubscribeData> subscribes) {
        subscription = false;
        if (!ListUtil.isEmpty(subscribes)) {
            for (SubscribeData s : subscribes) {
                if (s.getId() == PlayerHelper.getSubscribeId()) {
                    subscription = true;
                    break;
                }
            }
        }
        updateWidgetInfo(PlayerManager.getInstance().getCurPlayItem(), curBitmap);
    }

    @Override
    public void onPlayChangeChanged(PlayItem playItem) {
        Log.i(TAG, "onPlayChangeChanged");
        updateWidgetInfo(playItem, null);
        getBitmap(playItem);
        setSubscribeState();
    }


    /**
     * widget是否可以显示电台的信息
     * （1.如果有品牌电台的情况下，只在播放品牌电台的时候展示，其他情况显示默认封面
     * 2.如果没有品牌电台，后台没有配置，所有的播放信息都展 此项主要是针对后期去掉配置项的情况）
     *
     * @return
     */
    public boolean isShowRadio() {
        if (mDefaultPlayData != null) {
            String radioId = PlayItemUtils.getPlayItemRadioId();
            long brandsRadioId = mDefaultPlayData.getId();
            Log.i(TAG, "isBrandsRadio radioId:" + radioId + " brandsRadioId：" + brandsRadioId);
            String strBrandsId = String.valueOf(brandsRadioId);
            if (strBrandsId.equals(radioId)) {
                return true;
            } else {
                return false;
            }
        } else {
            return true;
        }
    }

    public void playBrandRadio(){
        /*boolean init = PlayerManager.getInstance().isPlayerInitSuccess();
        Log.i(TAG,"player init:"+init);
        if (!init) {
            PlayerManager.getInstance().addPlayerInitComplete(new IPlayerInitCompleteListener() {
                @Override
                public void onPlayerInitComplete(boolean b) {
                    init();
                    playJiXunRadio();
                }
            });
            PlayerManager.getInstance().setupPlayer();
        } else {
            init();
            playJiXunRadio();
        }*/
    }

    private void playJiXunRadio(){
        if (mDefaultPlayData != null) {
            PlayerManager.getInstance().play(mDefaultPlayData.getId(), String.valueOf(mDefaultPlayData.getType()));
        } else {
            getDefaultPlayInfo(true);
        }
    }

    public void getDefaultPlayInfo(boolean playRadio) {
        FlavorApiRequest.getInstance().getDefaultPlayInfo(new HttpCallback<DefaultPlayData>() {
            @Override
            public void onSuccess(DefaultPlayData defaultPlayData) {
                mDefaultPlayData = defaultPlayData;
                Log.i(TAG,"getDefaultPlayInfo success:"+mDefaultPlayData);
                if (mDefaultPlayData != null) {
                    getBitmapForBrand(mDefaultPlayData.getImg());
                    if (playRadio) {
                        PlayerManager.getInstance().play(defaultPlayData.getId(), String.valueOf(defaultPlayData.getType()));
                    }
                }
            }

            @Override
            public void onError(ApiException exception) {
                Log.i(TAG, "get default playinfo error , msg:" + exception);
            }
        });
    }

}
