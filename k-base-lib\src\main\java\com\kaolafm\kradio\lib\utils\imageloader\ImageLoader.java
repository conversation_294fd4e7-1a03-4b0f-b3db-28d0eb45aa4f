package com.kaolafm.kradio.lib.utils.imageloader;

import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import android.widget.ImageView;

import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.resource.bitmap.BitmapTransformation;
import com.kaolafm.kradio.lib.R;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageConfigImpl.Builder;
import com.kaolafm.kradio.lib.utils.imageloader.listener.OnGetBitmapListener;
import com.kaolafm.kradio.lib.utils.imageloader.listener.OnGetDrawableListener;
import com.kaolafm.kradio.lib.utils.imageloader.listener.OnImageLoaderListener;
import com.kaolafm.kradio.lib.utils.imageloader.transformation.CircleTransformation;

import java.io.File;

/**
 * 图片加载工具，采用策略模式，默认的策略是使用Glide加载框架{@link GlideImageLoaderStrategy}。
 *
 * <AUTHOR>
 * @date 2018/4/15
 */

public class ImageLoader implements BaseImageLoader, BaseInterface {

    /**
     * 默认模糊度
     */
    public static final int DEFAULT_BLUR_RADIUS = 15;

    private static volatile ImageLoader mImageLoader;

    private Drawable mDefaultErrorPic;

    private Drawable mDefaultPlaceholder;


    @NonNull
    private BaseImageLoaderStrategy mImageStrategy;

    private ImageLoader() {
        mImageStrategy = new GlideImageLoaderStrategy();
    }

    public static ImageLoader getInstance() {
        if (mImageLoader == null) {
            synchronized (ImageLoader.class) {
                if (mImageLoader == null) {
                    mImageLoader = new ImageLoader();
                }
            }
        }
        return mImageLoader;
    }

    /**
     * 初始化默认参数
     */
    public void initDefault(Drawable placeholder, Drawable errorPic) {
        mDefaultPlaceholder = placeholder;
        mDefaultErrorPic = errorPic;
    }

    @Override
    public File getCacheDir(Context context) {
        return mImageStrategy.getCacheDir(context);
    }

    @Override
    public void clearMemoryCache(Context context) {
        mImageStrategy.clearMemoryCache(context);
    }

    @Override
    public void clearDiskCache(Context context) {
        mImageStrategy.clearDiskCache(context);
    }

    @Override
    public Bitmap getBitmapFromCache(Context context, String url) {
        return mImageStrategy.getBitmapFromCache(context, url);
    }

    @Override
    public Bitmap getBitmapFromCache(Context context, String url, int size) {
        return mImageStrategy.getBitmapFromCache(context, url, size);
    }

    @Override
    public void getBitmapFromCache(Context context, String url, OnGetBitmapListener listener) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imgType(ImageConfigImpl.TYPE_BITMAP)
                .onGetBitmapListener(listener)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void getBitmapFromCache(Context context, String url, int radius, OnGetBitmapListener listener) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imgType(ImageConfigImpl.TYPE_BITMAP)
                .onGetBitmapListener(listener)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .imageRadius(radius)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void getBitmapFromCache(Context context, int resId, int radius, OnGetBitmapListener listener) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .resId(resId)
                .imgType(ImageConfigImpl.TYPE_BITMAP)
                .onGetBitmapListener(listener)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .imageRadius(radius)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayImage(Context context, Builder builder) {
        ImageConfigImpl config = builder.placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .fallback(mDefaultErrorPic).build();
        mImageStrategy.loadImage(context, config);
    }

    @Override
    public void displayImage(Context context, int resId, ImageView imageView) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .resId(resId)
                .imageView(imageView)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .fallback(mDefaultErrorPic)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayImage(Context context, int resId, ImageView imageView, int size) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .resId(resId)
                .imageView(imageView)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .fallback(mDefaultErrorPic)
                .size(size)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayImageAsBitmap(Context context, int resId, ImageView imageView) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .resId(resId)
                .imageView(imageView)
                .imgType(ImageConfigImpl.TYPE_BITMAP)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .fallback(mDefaultErrorPic)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayImageAsGif(Context context, int resId, ImageView imageView) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .resId(resId)
                .imageView(imageView)
                .imgType(ImageConfigImpl.TYPE_GIF)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .fallback(mDefaultErrorPic)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayImageFixedSize(Context context, int resId, ImageView imageView, int size) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .resId(resId)
                .imageView(imageView)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .fallback(mDefaultErrorPic)
                .size(size)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayImage(Context context, String url, ImageView imageView) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .fallback(mDefaultErrorPic)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayImageNotCenterCrop(Context context, String url, ImageView imageView) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .isCenterCrop(false)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .fallback(mDefaultErrorPic)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }


    @Override
    public void displayImage(Context context, String url, ImageView imageView, boolean isCache) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .fallback(mDefaultErrorPic)
                .cacheStrategy(isCache ? DiskCacheStrategy.ALL : DiskCacheStrategy.NONE)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayImage(Fragment fragment, String url, ImageView imageView) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .fallback(mDefaultErrorPic)
                .build();
        mImageStrategy.loadImage(fragment, imageConfig);
    }

    @Override
    public void displayImage(Context context, String url, ImageView imageView, Drawable defRes) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .placeholder(defRes)
                .errorPic(defRes)
                .fallback(defRes)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayImage(Fragment fragment, String url, ImageView imageView, Drawable defRes) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .placeholder(defRes)
                .errorPic(defRes)
                .fallback(defRes)
                .build();
        mImageStrategy.loadImage(fragment, imageConfig);
    }

    @Override
    public void displayImage(Context context, String url, ImageView imageView, BitmapTransformation... transformations) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .fallback(mDefaultErrorPic)
                .transformations(transformations)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayImage(Context context, String url, ImageView imageView, OnImageLoaderListener listener,
                             BitmapTransformation... transformations) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .imgType(ImageConfigImpl.TYPE_BITMAP)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .fallback(mDefaultErrorPic)
                .onImageLoaderListener(listener)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayImage(Fragment fragment, String url, ImageView imageView, BitmapTransformation... transformations) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .fallback(mDefaultErrorPic)
                .build();
        mImageStrategy.loadImage(fragment, imageConfig);
    }

    @Override
    public void displayImage(Context context, String url, ImageView imageView, Drawable defRes,
                             BitmapTransformation... transformations) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .placeholder(defRes)
                .errorPic(defRes)
                .fallback(defRes)
                .transformations(transformations)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayImage(Fragment fragment, String url, ImageView imageView, Drawable defRes,
                             BitmapTransformation... transformations) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .placeholder(defRes)
                .errorPic(defRes)
                .fallback(defRes)
                .transformations(transformations)
                .build();
        mImageStrategy.loadImage(fragment, imageConfig);
    }

    @Override
    public void displayImage(Context context, String url, ImageView imageView, Drawable defRes, int size) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .placeholder(defRes)
                .errorPic(defRes)
                .fallback(defRes)
                .size(size)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayImage(Fragment fragment, String url, ImageView imageView, Drawable defRes, int size) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .placeholder(defRes)
                .errorPic(defRes)
                .fallback(defRes)
                .size(size)
                .build();
        mImageStrategy.loadImage(fragment, imageConfig);
    }


    /**
     * @param context
     * @param url
     * @param imageView
     * @param defRes
     * @param cacheInMemory 是否缓存到内存，false不缓存。默认是true缓存
     */
    @Override
    public void displayImage(Context context, String url, ImageView imageView, Drawable defRes, boolean cacheInMemory) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .placeholder(defRes)
                .errorPic(defRes)
                .fallback(defRes)
                .cacheInMemory(cacheInMemory)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayImage(Fragment fragment, String url, ImageView imageView, Drawable defRes, boolean cacheInMemory) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .placeholder(defRes)
                .errorPic(defRes)
                .fallback(defRes)
                .cacheInMemory(cacheInMemory)
                .build();
        mImageStrategy.loadImage(fragment, imageConfig);
    }

    @Override
    public void displayImage(Context context, String url, ImageView imageView, OnImageLoaderListener listener) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .fallback(mDefaultErrorPic)
                .onImageLoaderListener(listener)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayImage(Context context, String url, ImageView imageView, OnImageLoaderListener listener, boolean isCenterCrop) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .fallback(mDefaultErrorPic)
                .onImageLoaderListener(listener)
                .isCenterCrop(isCenterCrop)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayImage(Fragment fragment, String url, ImageView imageView, OnImageLoaderListener listener) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .fallback(mDefaultErrorPic)
                .onImageLoaderListener(listener)
                .build();
        mImageStrategy.loadImage(fragment, imageConfig);
    }

    @Override
    public void displayImage(Context context, String url, ImageView imageView, Drawable defRes,
                             OnImageLoaderListener listener) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .placeholder(defRes)
                .errorPic(defRes)
                .fallback(defRes)
                .onImageLoaderListener(listener)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayImage(Fragment fragment, String url, ImageView imageView, Drawable defRes,
                             OnImageLoaderListener listener) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .placeholder(defRes)
                .errorPic(defRes)
                .fallback(defRes)
                .onImageLoaderListener(listener)
                .build();
        mImageStrategy.loadImage(fragment, imageConfig);
    }

    @Override
    public void getBlurBitmapFromCache(Context context, String url, int blurRadius, OnGetBitmapListener listener) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imgType(ImageConfigImpl.TYPE_BITMAP)
                .onGetBitmapListener(listener)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .blurValue(blurRadius)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayImageFixedSize(Context context, String url, ImageView imageView, int size) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .size(size)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayImageFixedSize(Fragment fragment, String url, ImageView imageView, int size) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .size(size)
                .build();
        mImageStrategy.loadImage(fragment, imageConfig);
    }

    @Override
    public void displayCircleImage(Context context, String url, ImageView imageView) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .fallback(mDefaultErrorPic)
                .isCircle(true)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayCircleImage(Context context, int resId, ImageView imageView) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .resId(resId)
                .imageView(imageView)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .fallback(mDefaultErrorPic)
                .isCircle(true)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayCircleImage(Context context, String url, ImageView imageView, OnImageLoaderListener onImageLoaderListener) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .fallback(mDefaultErrorPic)
                .isCircle(true)
                .onImageLoaderListener(onImageLoaderListener)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayCircleImageAsGif(Context context, String url, ImageView imageView, OnImageLoaderListener onImageLoaderListener) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .fallback(mDefaultErrorPic)
                .imgType(ImageConfigImpl.TYPE_GIF)
                .isCircle(true)
                .onImageLoaderListener(onImageLoaderListener)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayCircleImage(Context context, String url, ImageView imageView, int borderWidth,
                                   int borderColor) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .fallback(mDefaultErrorPic)
                .transformations(new CircleTransformation(borderWidth, borderColor))
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayCircleImage(Context context, String url, ImageView imageView, int borderWidth, int borderColor,
                                   int size) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .fallback(mDefaultErrorPic)
                .size(size)
                .transformations(new CircleTransformation(borderWidth, borderColor))
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayCircleImage(Context context, String url, ImageView imageView, int borderWidth, int borderColor,
                                   int size, Drawable def) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .placeholder(def)
                .errorPic(def)
                .fallback(def)
                .size(size)
                .transformations(new CircleTransformation(borderWidth, borderColor))
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayCircleImage(Context context, String url, ImageView imageView, Drawable defRes) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .placeholder(defRes)
                .errorPic(defRes)
                .isCircle(true)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayCircleImage(Fragment fragment, String url, ImageView imageView, Drawable defRes) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .placeholder(defRes)
                .errorPic(defRes)
                .transformations(new CircleTransformation(ResUtil.getDimen(R.dimen.m2), ResUtil.getColor(R.color.avatar_border_color)))
                .build();
        mImageStrategy.loadImage(fragment, imageConfig);
    }

    @Override
    public void displayRoundImage(Context context, String url, ImageView imageView, int radius) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultPlaceholder)
                .imageRadius(radius)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayRoundImage(Context context, String url, ImageView imageView, Drawable defRes, int radius) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .placeholder(defRes)
                .errorPic(defRes)
                .imageRadius(radius)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayRoundImage(Fragment fragment, String url, ImageView imageView, Drawable defRes, int radius) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .placeholder(defRes)
                .errorPic(defRes)
                .imageRadius(radius)
                .build();
        mImageStrategy.loadImage(fragment, imageConfig);
    }

    @Override
    public void displayBlurImage(Context context, String url, int blurRadius, OnGetDrawableListener listener) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imgType(ImageConfigImpl.TYPE_DRAWABLE)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .onGetDrawableListener(listener)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayBlurImage(Context context, String url, ImageView imageView, Drawable defRes, int blurRadius) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .placeholder(defRes)
                .errorPic(defRes)
                .blurValue(blurRadius)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayBlurImage(Context context, int resId, ImageView imageView, int blurRadius) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .resId(resId)
                .imageView(imageView)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .blurValue(blurRadius)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayBlurImage(Fragment fragment, String url, ImageView imageView, Drawable defRes, int blurRadius) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .placeholder(defRes)
                .errorPic(defRes)
                .blurValue(blurRadius)
                .build();
        mImageStrategy.loadImage(fragment, imageConfig);
    }

    @Override
    public void displayBlurImage(Context context, String url, ImageView imageView) {
        displayBlurImage(context, url, imageView, null, DEFAULT_BLUR_RADIUS);
    }

    @Override
    public void displayImageInResource(Context context, int resId, ImageView imageView) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .resId(resId)
                .imageView(imageView)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayImageInResource(Fragment fragment, int resId, ImageView imageView) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .resId(resId)
                .imageView(imageView)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .build();
        mImageStrategy.loadImage(fragment, imageConfig);
    }

    @Override
    public void displayImageInResource(Context context, int resId, ImageView imageView,
                                       BitmapTransformation... transformations) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .resId(resId)
                .imageView(imageView)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayImageInResource(Fragment fragment, int resId, ImageView imageView,
                                       BitmapTransformation... transformations) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .resId(resId)
                .imageView(imageView)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .build();
        mImageStrategy.loadImage(fragment, imageConfig);
    }

    @Override
    public void displayImageInResource(Context context, int resId, ImageView imageView, Drawable defRes) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .resId(resId)
                .imageView(imageView)
                .placeholder(defRes)
                .errorPic(defRes)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayImageInResource(Fragment fragment, int resId, ImageView imageView, Drawable defRes) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .resId(resId)
                .imageView(imageView)
                .placeholder(defRes)
                .errorPic(defRes)
                .build();
        mImageStrategy.loadImage(fragment, imageConfig);
    }

    @Override
    public void displayImageInResource(Context context, int resId, ImageView imageView, Drawable defRes,
                                       BitmapTransformation... transformations) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .resId(resId)
                .imageView(imageView)
                .placeholder(defRes)
                .errorPic(defRes)
                .transformations(transformations)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayImageInResource(Fragment fragment, int resId, ImageView imageView, Drawable defRes,
                                       BitmapTransformation... transformations) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .resId(resId)
                .imageView(imageView)
                .placeholder(defRes)
                .errorPic(defRes)
                .transformations(transformations)
                .build();
        mImageStrategy.loadImage(fragment, imageConfig);

    }

    @Override
    public void displayImage(Context context, String url, ImageView imageView, boolean isCache, boolean isCenterCrop) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .url(url)
                .imageView(imageView)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .fallback(mDefaultErrorPic)
                .isCenterCrop(isCenterCrop)
                .cacheStrategy(isCache ? DiskCacheStrategy.ALL : DiskCacheStrategy.NONE)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }

    @Override
    public void displayImage(Context context, int resId, ImageView imageView, boolean isCenterCrop) {
        ImageConfigImpl imageConfig = ImageConfigImpl.builder()
                .resId(resId)
                .imageView(imageView)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .fallback(mDefaultErrorPic)
                .isCenterCrop(isCenterCrop)
                .build();
        mImageStrategy.loadImage(context, imageConfig);
    }


    public void displayLocalImage(Context context, String url, ImageView imageView) {
        ImageConfigImpl config = ImageConfigImpl.builder()
                .url(url)
                .imgType(ImageConfigImpl.TYPE_UNKNOWN)
                .isLocal(true)
                .isCenterCrop(false)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .fallback(mDefaultErrorPic)
                .imageView(imageView)
                .build();
        mImageStrategy.loadImage(context, config);
    }

    public void displayLocalIRoundmage(Context context, String url, ImageView imageView, int imageRadius) {
        ImageConfigImpl config = ImageConfigImpl.builder()
                .url(url)
                .imgType(ImageConfigImpl.TYPE_UNKNOWN)
                .isLocal(true)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .fallback(mDefaultErrorPic)
                .imageRadius(imageRadius)
                .imageView(imageView)
                .build();
        mImageStrategy.loadImage(context, config);
    }

    public void displayLocalCircleImage(Context context, String url, ImageView imageView) {
        ImageConfigImpl config = ImageConfigImpl.builder()
                .url(url)
                .imgType(ImageConfigImpl.TYPE_UNKNOWN)
                .isLocal(true)
                .isCircle(true)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .fallback(mDefaultErrorPic)
                .imageView(imageView)
                .build();
        mImageStrategy.loadImage(context, config);
    }

    public void displayLocalImage(Context context, String url, ImageView imageView, OnImageLoaderListener onImageLoaderListener) {
        ImageConfigImpl config = ImageConfigImpl.builder()
                .url(url)
                .imgType(ImageConfigImpl.TYPE_UNKNOWN)
                .isLocal(true)
                .isCenterCrop(false)
                .onImageLoaderListener(onImageLoaderListener)
                .placeholder(mDefaultPlaceholder)
                .errorPic(mDefaultErrorPic)
                .fallback(mDefaultErrorPic)
                .imageView(imageView)
                .build();
        mImageStrategy.loadImage(context, config);
    }


    /**
     * 获取当前图片加载策略
     */
    public <T extends ImageConfig> BaseImageLoaderStrategy<T> getLoadImageStrategy() {
        return mImageStrategy;
    }

    /**
     * 设置图片加载策略
     */
    public <T extends ImageConfig> void setLoadImageStrategy(BaseImageLoaderStrategy<T> imageStrategy) {
        mImageStrategy = imageStrategy;
    }

    /**
     * 取消加载并清理磁盘和内存缓存
     */
    public <T extends ImageConfig> void clear(Context context, T config) {
        mImageStrategy.clear(context, config);
    }

    @Override
    public void pauseRequests(Context context) {
        mImageStrategy.pauseRequests(context);
    }

    @Override
    public void resumeRequests(Context context) {
        mImageStrategy.resumeRequests(context);
    }

    @Override
    public void pauseRequests(Activity activity) {
        mImageStrategy.pauseRequests(activity);
    }

    @Override
    public void resumeRequests(Activity activity) {
        mImageStrategy.resumeRequests(activity);
    }

    @Override
    public void pauseRequests(FragmentActivity fragmentActivity) {
        mImageStrategy.pauseRequests(fragmentActivity);
    }

    @Override
    public void resumeRequests(FragmentActivity fragmentActivity) {
        mImageStrategy.resumeRequests(fragmentActivity);
    }

    @Override
    public void pauseRequests(android.app.Fragment fragment) {
        mImageStrategy.pauseRequests(fragment);
    }

    @Override
    public void resumeRequests(android.app.Fragment fragment) {
        mImageStrategy.resumeRequests(fragment);
    }

    @Override
    public void pauseRequests(Fragment fragment) {
        mImageStrategy.pauseRequests(fragment);
    }

    @Override
    public void resumeRequests(Fragment fragment) {
        mImageStrategy.resumeRequests(fragment);
    }
}
