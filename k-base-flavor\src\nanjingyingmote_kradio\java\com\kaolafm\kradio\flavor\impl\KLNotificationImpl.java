package com.kaolafm.kradio.flavor.impl;

import android.app.Notification;
import android.content.Context;
import androidx.core.app.NotificationCompat;
import android.util.Log;

import com.kaolafm.kradio.flavor.R;
import com.kaolafm.sdk.core.mediaplayer.KLNotificationListener;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-11-18 14:38
 ******************************************/
public class KLNotificationImpl implements KLNotificationListener {
    private static final String TAG = "KLNotificationImpl";

    @Override
    public Notification createNotification(Object... objects) {
        Context context = (Context) objects[0];
        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, null);
        builder.setSmallIcon(R.drawable.small_notification_icon);
        Notification notification = builder.build();
        notification.flags = Notification.FLAG_ONGOING_EVENT;
        notification.flags |= Notification.FLAG_NO_CLEAR;
        notification.flags |= Notification.FLAG_FOREGROUND_SERVICE;
        Log.i(TAG, "createNotification----->" + notification);
        return notification;
    }
}
