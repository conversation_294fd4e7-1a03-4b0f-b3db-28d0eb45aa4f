<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/comprehensive_theme_bg">
    <!-- 隐藏控件，用于所见即可说语音执行关闭操作 -->
    <TextView
        android:id="@+id/cd_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@color/transparent"
        android:contentDescription="@string/content_desc_close_dialog"
        android:text="@string/content_desc_close_dialog"
        android:textColor="@color/transparent"
        android:textSize="1sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
         />

    <RelativeLayout
        android:layout_width="@dimen/x652"
        android:layout_height="@dimen/y56"
        android:gravity="center_vertical"
        android:paddingLeft="@dimen/x32"
        android:background="@drawable/comprehensive_theme_header_bg">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="直播公告"
            android:textColor="#FFEEEEEE"
            android:textSize="@dimen/m26"/>

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="@dimen/x652"
        android:layout_height="@dimen/y280"
        android:gravity="left|top"
        android:paddingLeft="@dimen/x32"
        android:paddingRight="@dimen/x12"
        android:paddingTop="@dimen/y24"
        android:paddingBottom="@dimen/y40"
        android:background="@drawable/comprehensive_theme_content_bg">

        <TextView
            android:id="@+id/live_theme_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingRight="@dimen/x16"
            android:scrollbars="vertical"
            android:scrollbarStyle="outsideInset"
            android:scrollbarThumbVertical="@drawable/comprehensive_theme_scroll_thumb_bg"
            android:scrollbarTrackVertical="@drawable/comprehensive_theme_scroll_track_bg"
            android:gravity="start"
            android:text=""
            android:textColor="#FFEEEEEE"
            android:textSize="@dimen/m24"/>

    </RelativeLayout>

</LinearLayout>