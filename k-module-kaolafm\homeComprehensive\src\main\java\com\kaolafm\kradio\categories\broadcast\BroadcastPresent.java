package com.kaolafm.kradio.categories.broadcast;

import android.os.Handler;
import android.text.TextUtils;

import com.kaolafm.kradio.common.utils.TimeUtil;
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.kradio.lib.base.ui.BaseShowHideFragment;
import com.kaolafm.opensdk.api.broadcast.ProgramDetails;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.kradio.lib.bean.BroadcastRadioDetailData;
import com.kaolafm.kradio.lib.bean.BroadcastRadioSimpleData;
import com.trello.rxlifecycle3.android.FragmentEvent;
import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

/**
 * Created by kaolafm on 2018/4/25.
 */

public class BroadcastPresent extends BasePresenter<BroadcastModel, IBroadcastView> {

    private boolean isLoadMore;

    public BroadcastPresent(IBroadcastView view) {
        super(view);
    }

    public boolean enableLoadmore() {
        return mModel.haveNext();
    }

    public ArrayList<BroadcastRadioSimpleData> itemBean2Simple(List<BroadcastRadioDetailData> dataList) {
        if (dataList == null) {
            return null;
        }
        ArrayList<BroadcastRadioSimpleData> broadcastRadioSimpleDataList = new ArrayList<>();
        for (int i = 0, size = dataList.size(); i < size; i++) {
            BroadcastRadioDetailData broadcastRadioDetailData = dataList.get(i);
            BroadcastRadioSimpleData simpleData = new BroadcastRadioSimpleData();
            simpleData.setBroadcastId(broadcastRadioDetailData.getBroadcastId());
            String freq = broadcastRadioDetailData.getFreq();
            freq = TextUtils.isEmpty(freq) ? "" : freq;
            simpleData.setName(broadcastRadioDetailData.getName() + "  " + freq);
            simpleData.setImg(broadcastRadioDetailData.getIcon());
            broadcastRadioSimpleDataList.add(simpleData);
        }
        return broadcastRadioSimpleDataList;
    }

    @Override
    protected BroadcastModel createModel() {
        return new BroadcastModel();
    }

    public void loadFirstData(int categoryId) {
        setBroadcastCategoryType(categoryId);
        mModel.setPageNum(1);
        requestBroadcastListData();
        isLoadMore = false;
    }

    public void loadMore() {
        if (!mModel.haveNext()) {
            mView.showError();
            return;
        }
        isLoadMore = true;
        requestBroadcastListData();
    }

    /**
     * 设置广播类型
     */
    private void setBroadcastCategoryType(int classifyId) {
        mModel.setBroadcastCategoryType(classifyId);
    }

    public void requestBroadcastListData() {
        mModel.requestBroadcastListData(((BaseShowHideFragment)mView).bindUntilEvent(FragmentEvent.DESTROY_VIEW), new HttpCallback<List<BroadcastRadioDetailData>>() {
            @Override
            public void onSuccess(List<BroadcastRadioDetailData> basePageResult) {
                if (mView != null) {
                    mView.notifyDataChange(basePageResult, isLoadMore);
                }
                schedulProgramListRequest(1);
            }

            @Override
            public void onError(ApiException e) {
                if (mView != null) {
                    mView.showError();
                }
            }
        });
    }
    public void schedulProgramListRequest(int delay){
        mModel.schedulProgramListRequest(1,new HttpCallback<List<ProgramDetails>>() {
            @Override
            public void onSuccess(List<ProgramDetails> list) {
                if (mView != null) {
                    for (int i = 0; i < list.size(); i++) {
                        if(TimeUtil.isWithinTime(list.get(i).getBeginTime(),list.get(i).getEndTime())){
                            mView.showImage(list.get(i).getBroadcastId(),list.get(i).getImage(),list.get(i).getDesc());
                        }
                        if(!TimeUtil.isAfterTime(list.get(i).getBeginTime())) {
                            schedulProgramList(list.get(i).getBeginTime(), list.get(i).getEndTime(), list.get(i).getBroadcastId(), list.get(i).getImage(), list.get(i).getDesc());
                        }
                    }
                }
            }

            @Override
            public void onError(ApiException e) {

            }
        });
    }
    public void schedulProgramList(String beginTime,String endTime,long broadcastId,String img,String desc){
        int delay = TimeUtil.getDelay(beginTime);
        new Handler().postDelayed(()->{
            if(mView!=null){
                mView.showImage(broadcastId,img,desc);
            }
        },delay * 1000);
    }
    public void cancelSchedule(){
        mModel.cancelSchedule();
    }
}
