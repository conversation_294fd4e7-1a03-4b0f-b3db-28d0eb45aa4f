//package com.kaolafm.kradio.flavor.impl;
//
//import android.util.Log;
//import android.view.KeyEvent;
//
//
//import com.kaolafm.kradio.lib.base.flavor.KRadioMediaKeyEventInter;
//
///******************************************
// * 类描述:
// *
// * @version: V1.0
// * @author: yangshaoning
// * @time: 2019-12-11 20:40
// ******************************************/
//public class KRadioMediaKeyEventImpl implements KRadioMediaKeyEventInter {
//    private static final String TAG = "KRadioMediaKeyEventImpl";
//
//    @Override
//    public boolean onKeyUp(int keyCode, KeyEvent event) {
//        Log.i(TAG, "onKeyUp----->keyCode = " + keyCode);
//        keyCodeOptions(keyCode);
//        return true;
//    }
//
//    private void keyCodeOptions(int keyCode) {
//        if (keyCode == KeyEvent.KEYCODE_MEDIA_NEXT
//                || keyCode == KeyEvent.KEYCODE_MEDIA_PREVIOUS
//                || keyCode == KeyEvent.KEYCODE_ZOOM_IN
//                || keyCode == KeyEvent.KEYCODE_ZOOM_OUT
//                || keyCode == KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE) {
//            if (keyCode == KeyEvent.KEYCODE_MEDIA_NEXT
//                    || keyCode == KeyEvent.KEYCODE_ZOOM_IN) {
//                //下一首
//                KLAutoPlayerManager.getInstance().playNext();
//            } else if (keyCode == KeyEvent.KEYCODE_MEDIA_PREVIOUS
//                    || keyCode == KeyEvent.KEYCODE_ZOOM_OUT) {
//                //上一首
//                KLAutoPlayerManager.getInstance().playPre();
//            } else if (keyCode == KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE) {
//                //暂停、播放
//                PlayerManagerHelper.getInstance().switchPlayerStatus(true);
//            }
//        }
//    }
//}