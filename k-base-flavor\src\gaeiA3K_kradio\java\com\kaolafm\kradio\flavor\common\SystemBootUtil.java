package com.kaolafm.kradio.flavor.common;

import android.content.Context;

import com.kaolafm.kradio.lib.utils.SharedPreferenceUtil;


/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-12-17 20:28
 ******************************************/
public final class SystemBootUtil {

    /**
     * 记录当前系统首次启动状态文件名
     */
    private static final String BOOT_FIRST_NAME = "boot_first";

    /**
     * 记录当前系统启动状态关键字
     */
    private static final String BOOT_FIRST_VALUE = "boot_first_status";

    /**
     * 自系统启动后，是否第一次启动考拉APP
     *
     * @param context
     * @return true为首次启动APP，false为否
     */
    public boolean isFirstBoot(Context context) {
//        SharedPreferenceUtil sharedPreferenceUtil = SharedPreferenceUtil.getInstance(context, BOOT_FIRST_NAME, Context.MODE_PRIVATE);
//        boolean flag = sharedPreferenceUtil.getBoolean(BOOT_FIRST_VALUE, false);
        return true;
    }

    /**
     * 自系统启动后，更新第一次启动考拉APP状态
     *
     * @param context
     * @return true为首次启动APP，false为否
     */
    public void updateFirstBoot(Context context, boolean flag) {
        SharedPreferenceUtil sharedPreferenceUtil = SharedPreferenceUtil.getInstance(context, BOOT_FIRST_NAME, Context.MODE_PRIVATE);
        sharedPreferenceUtil.putBoolean(BOOT_FIRST_VALUE, flag);
    }
}
