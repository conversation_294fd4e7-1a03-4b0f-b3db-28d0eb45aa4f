package com.kaolafm.kradio.flavor.impl;

import android.util.Log;
import android.view.KeyEvent;

import com.kaolafm.kradio.lib.base.flavor.KRadioMediaKeyEventInter;
import com.kaolafm.utils.MediaButtonManagerUtil;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-04-14 11:13
 ******************************************/
public class KRadioMediaKeyEventImpl implements KRadioMediaKeyEventInter {
    private MediaButtonManagerUtil mMediaButtonManagerUtil = new MediaButtonManagerUtil();

    @Override
    public boolean onKeyUp(int keyCode, KeyEvent event) {
        Log.d("KRadioMediaKeyEventImpl", "keyCode: " + keyCode);
        mMediaButtonManagerUtil.manageMediaButtonClick(keyCode);
        return true;
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        return false;
    }
}
