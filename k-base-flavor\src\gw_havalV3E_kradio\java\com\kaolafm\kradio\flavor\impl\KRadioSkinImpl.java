package com.kaolafm.kradio.flavor.impl;

import android.text.TextUtils;

import com.kaolafm.kradio.lib.base.flavor.KRadioSkinInter;
import com.kaolafm.kradio.skin.SkinInitializer;
import com.kaolafm.kradio.skin.view.SkinablePlayerBar;

/**
 * @Description:
 * @Author: Maclay
 * @Date: 2021/4/28 10:06
 */
public class KRadioSkinImpl implements KRadioSkinInter {
    @Override
    public boolean isSupport(Object... o) {
        if (o != null && o.length > 0) {
            String clazzName = o[0].toString();
            if (TextUtils.equals(SkinablePlayerBar.class.getName(), clazzName)) {
                return false;
            } else if (TextUtils.equals(SkinInitializer.class.getName(), clazzName)) {//旧框架开关，关闭后很多地方还不适配，先不关闭4-28
                return true;
            }
        }
        return true;
    }
}