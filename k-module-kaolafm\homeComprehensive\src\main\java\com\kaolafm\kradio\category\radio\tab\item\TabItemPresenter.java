package com.kaolafm.kradio.category.radio.tab.item;

import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.OnLifecycleEvent;

import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.common.utils.TimeUtil;
import com.kaolafm.kradio.category.ClickHelper;
import com.kaolafm.kradio.category.radio.tab.item.TabItemContract.IView;
import com.kaolafm.kradio.lib.bean.SubcategoryItemBean;
import com.kaolafm.kradio.categories.HorizontalSubcategoryAdapter;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.bean.BroadcastRadioSimpleData;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.api.broadcast.ProgramDetails;
import com.kaolafm.opensdk.api.operation.model.category.Category;
import com.kaolafm.opensdk.api.operation.model.category.LeafCategory;
import com.kaolafm.opensdk.api.personalise.PersonalizedRequest;
import com.kaolafm.opensdk.api.personalise.model.HotRecommend;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

/**
 * <AUTHOR>
 **/
public class TabItemPresenter extends BasePresenter<TabItemModel, TabItemContract.IView> implements TabItemContract.IPresenter {

    private boolean mHaveMembers;

    private long cid;

    TabItemPresenter(IView view, long id, boolean haveMembers) {
        super(view);
        this.cid = id;
        mHaveMembers = haveMembers;
        registerListener();
    }

    @Override
    protected TabItemModel createModel() {
        return new TabItemModel();
    }

    @Override
    public void loadData() {
        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
            if (mView != null) {
                mView.showError(new ApiException("网络错误!"));
            }
            return;
        }
        getHotDate();
        String parentCode = String.valueOf(cid);
        if (mHaveMembers) {
            mModel.getCategoryMemberList(parentCode, new HttpCallback<List<SubcategoryItemBean>>() {
                @Override
                public void onSuccess(List<SubcategoryItemBean> itemBeans) {
                    if (mView != null) {
                        mView.showData(itemBeans);
                    }
                    schedulProgramListRequest(1);
                }

                @Override
                public void onError(ApiException e) {
                    if (mView != null) {
                        mView.showError(e);
                    }
                }
            });
        } else {
            mModel.getSubcategoryList(parentCode, new HttpCallback<List<Category>>() {
                @Override
                public void onSuccess(List<Category> categories) {
                    ArrayList<SubcategoryItemBean> itemBeans = new ArrayList<>();
                    if (!ListUtil.isEmpty(categories)) {
                        for (Category category : categories) {
                            SubcategoryItemBean subcategoryItemBean = new SubcategoryItemBean();
                            String code = category.getCode();
                            if (TextUtils.isEmpty(code)) {
                                code = "0";
                            }
                            subcategoryItemBean.setId(Long.parseLong(code));
                            subcategoryItemBean.setName(category.getName());
                            subcategoryItemBean.setItemType(SubcategoryItemBean.TYPE_ITEM_BROADCAST_CATEGORY);
                            subcategoryItemBean.setLeafCategory(category instanceof LeafCategory);
                            itemBeans.add(subcategoryItemBean);
                        }
                    }
                    if (mView != null) {
                        mView.showData(itemBeans);
                    }
                }

                @Override
                public void onError(ApiException e) {
                    if (mView != null) {
                        mView.showError(e);
                    }
                }
            });
        }

    }

    public void schedulProgramListRequest(int delay){
        mModel.schedulProgramListRequest(1,new HttpCallback<List<ProgramDetails>>() {
            @Override
            public void onSuccess(List<ProgramDetails> list) {
                if (mView != null) {
                    for (int i = 0; i < list.size(); i++) {
                        if(TimeUtil.isWithinTime(list.get(i).getBeginTime(),list.get(i).getEndTime())){
                            mView.showImage(list.get(i).getBroadcastId(),list.get(i).getImage(),list.get(i).getDesc());
                        }
                        if(!TimeUtil.isAfterTime(list.get(i).getBeginTime())) {
                            schedulProgramList(list.get(i).getBeginTime(), list.get(i).getEndTime(), list.get(i).getBroadcastId(), list.get(i).getImage(), list.get(i).getDesc());
                        }
                    }
                }
            }

            @Override
            public void onError(ApiException e) {

            }
        });
    }
    // CPU优化：使用静态Handler避免频繁创建Handler对象
    private static final Handler sScheduleHandler = new Handler(Looper.getMainLooper());

    public void schedulProgramList(String beginTime,String endTime,long broadcastId,String img,String desc){
        int delay = TimeUtil.getDelay(beginTime);
        // CPU优化：复用Handler，避免频繁创建新对象
        sScheduleHandler.postDelayed(()->{
            if(mView != null){
                mView.showImage(broadcastId,img,desc);
            }
        },delay * 1000);
    }
    public void cancelSchedule(){
        mModel.cancelSchedule();
    }
    /**
     * 获取推荐
     */
    public void getHotDate() {
        new PersonalizedRequest().getHotRecommend(new HttpCallback<HotRecommend>() {
            @Override
            public void onSuccess(HotRecommend hotRecommend) {
                if (mView != null) {
                    mView.showHotRecommend(hotRecommend);
                }
            }

            @Override
            public void onError(ApiException e) {

            }
        });
    }

    private boolean isLoadingMore = false;
    @Override
    public void loadMore() {
        if (mModel == null) {
            return;
        }
        if (isHasNextPage() && !isLoadingMore) {
            isLoadingMore = true;
            mModel.loadMoreCategoryMember(new HttpCallback<List<SubcategoryItemBean>>() {
                @Override
                public void onSuccess(List<SubcategoryItemBean> itemBeanList) {
                    isLoadingMore = false;
                    if (mView != null) {
                        mView.showMoreData(itemBeanList);
                    }
                }

                @Override
                public void onError(ApiException e) {
                    isLoadingMore = false;
                    if (mView != null) {
                        mView.showMoreDataError(e);
                    }
                }
            });
        }
    }

    @Override
    public boolean isHasNextPage() {
        return mModel.isHaveNext();
    }


    @Override
    public void onClick(SubcategoryItemBean subcategoryItemBean, int position) {
        ClickHelper.onClick(subcategoryItemBean, position);
    }

    @Override
    public void onClick(SubcategoryItemBean subcategoryItemBean, HorizontalSubcategoryAdapter adapter, int position) {
        //即点即播的广播需要转换成广播的播单数据列表。
        if (subcategoryItemBean.getItemType() == SubcategoryItemBean.TYPE_ITEM_BROADCAST_LOCAL
                || subcategoryItemBean.getItemType() == SubcategoryItemBean.TYPE_ITEM_TV) {
            if (adapter != null) {
                List<SubcategoryItemBean> dataList = adapter.getDataList();
                ArrayList<BroadcastRadioSimpleData> list = mModel.itemBean2Simple(dataList);
                PlayerManagerHelper.getInstance().addBroadcastRadioSimpleItems(list);
            }
        }
        //position 没有用到，所以随便填了一个0
        ClickHelper.onClick(subcategoryItemBean, position, mHaveMembers);
    }

    private BasePlayStateListener basePlayStateListener = new BasePlayStateListener() {
        @Override
        public void onPlayerPreparing(PlayItem playItem) {
            if (mView != null) {
                mView.setSelected();
            }
        }

        @Override
        public void onPlayerPlaying(PlayItem playItem) {
            if (mView != null) {
                mView.setSelected();
            }
        }

        @Override
        public void onPlayerPaused(PlayItem playItem) {
            if (mView != null) {
                mView.setSelected();
            }
        }

    };

    //@OnLifecycleEvent(Lifecycle.Event.ON_RESUME)
    @Override
    public void registerListener() {
        PlayerManager.getInstance().addPlayControlStateCallback(basePlayStateListener);
    }

    //解决https://app.huoban.com/tables/2100000007530121/items/2300001555864138?userId=1229522问题
    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    public void unregisterListener() {
        PlayerManager.getInstance().removePlayControlStateCallback(basePlayStateListener);
    }
}
