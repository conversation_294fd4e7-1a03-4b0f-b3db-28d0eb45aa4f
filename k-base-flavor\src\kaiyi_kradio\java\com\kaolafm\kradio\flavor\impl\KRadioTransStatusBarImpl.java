package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import com.kaolafm.kradio.lib.base.flavor.KRadioTransStatusBarInter;
import com.kaolafm.kradio.lib.utils.ScreenUtil;

import static com.kaolafm.kradio.lib.utils.ScreenUtil.setStatusBar;
import static com.kaolafm.kradio.lib.utils.ViewUtil.addPaddingForView;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-07-18 11:19
 ******************************************/
public final class KRadioTransStatusBarImpl implements KRadioTransStatusBarInter {
    private int mStatusBarHeight;

    public KRadioTransStatusBarImpl() {
        mStatusBarHeight = ScreenUtil.getStatusBarHeight() - 14;
    }

    @Override
    public boolean changeStatusBarColor(Activity activity, int colorRes) {
        Window window = activity.getWindow();
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001282531704?userId=1881599问题
        window.setFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
                WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS);
        setStatusBar(window, false);
        return true;
    }

    @Override
    public boolean changeViewLayoutForStatusBar(View view, int id) {
        addPaddingForView(view, 0, mStatusBarHeight, 0, 0);
        return true;
    }

    @Override
    public boolean canChangeViewLayoutForStatusBar(Object... args) {
        return true;
    }

    @Override
    public int getStatusBarHeight(Object... args) {
        return mStatusBarHeight;
    }
}
