package com.kaolafm.kradio.lib.bean;

/**
 * <pre>
 *     <AUTHOR> Wenchl
 *     e-mail : <EMAIL>
 *     time   : 2019/03/07
 *     desc   :
 *     version: 1.0
 * </pre>
 */
public class PurchasedItemBean {

    private int viewType;

    /**
     * 专辑，PGC，传统广播电台ID
     */
    private long id;
    /**
     * 专辑，PGC，传统广播电台名称
     */
    private String name;
    /**
     * 专辑，PGC，传统广播电台 类型
     */
    private int type;
    /**
     * 专辑，PGC，传统广播电台图片URL
     */
    private String img;
    /**
     * 更新时间 时间戳形式
     */
    private String updateTime;
    /**
     * 是否已经上线 （1是，0否）
     */
    private boolean isOnLine = true;
    /**
     * 是否有版权 （1是，0否）
     */
    private int hasCopyright;

    private boolean isPlaying = false;

    /**
     * 是否订阅。默认已订阅
     */
    private boolean subscribed = true;

    private int vip;

    private int fine;

    /**
     *播放量
     */
    private long listenNum;

    public long getListenNum() {
        return listenNum;
    }

    public void setListenNum(long listenNum) {
        this.listenNum = listenNum;
    }

    public int getVip() {
        return vip;
    }

    public void setVip(int vip) {
        this.vip = vip;
    }

    public int getFine() {
        return fine;
    }

    public void setFine(int fine) {
        this.fine = fine;
    }

    public void setPlaying(boolean playing) {
        isPlaying = playing;
    }

    public boolean isPlaying() {
        return isPlaying;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }

    public int getViewType() {
        return viewType;
    }

    public long getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getImg() {
        return img;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public int getHasCopyright() {
        return hasCopyright;
    }

    public void setViewType(int viewType) {
        this.viewType = viewType;
    }

    public void setId(long id) {
        this.id = id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public void setHasCopyright(int hasCopyright) {
        this.hasCopyright = hasCopyright;
    }

    public boolean isOnLine() {
        return isOnLine;
    }

    public void setOnLine(boolean onLine) {
        isOnLine = onLine;
    }

    public boolean isSubscribed() {
        return subscribed;
    }

    public void setSubscribed(boolean subscribed) {
        this.subscribed = subscribed;
    }
}
