package com.kaolafm.kradio.lib.widget;

import android.content.Context;
import androidx.appcompat.widget.AppCompatTextView;
import android.util.AttributeSet;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-07-12 17:22
 ******************************************/
public class AutoMarqueenTextView extends AppCompatTextView {
    private boolean isFocusable = true;

    public AutoMarqueenTextView(Context context) {
        super(context);
    }

    public AutoMarqueenTextView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public AutoMarqueenTextView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public boolean isFocused() {
        return isFocusable;
    }

    public void setFocusable(boolean isFocusable) {
        this.isFocusable = isFocusable;
    }
}
