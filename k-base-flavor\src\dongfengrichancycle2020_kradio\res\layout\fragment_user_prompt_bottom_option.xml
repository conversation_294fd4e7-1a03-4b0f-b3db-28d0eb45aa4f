<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@id/user_prompt_agree_image"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y20"
        android:paddingTop="@dimen/y20"
        android:paddingBottom="@dimen/y20"
        android:src="@drawable/ic_user_prompt_agree"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@id/user_prompt_agree_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/x10"
        android:paddingTop="@dimen/y20"
        android:paddingBottom="@dimen/y20"
        android:text="@string/user_prompt_agree_ps_str"
        android:textColor="@color/text_color_5"
        android:textSize="@dimen/user_prompt_content_text_size"
        app:layout_constraintBottom_toBottomOf="@id/user_prompt_agree_image"
        app:layout_constraintLeft_toRightOf="@id/user_prompt_agree_image"
        app:layout_constraintTop_toTopOf="@id/user_prompt_agree_image" />

    <TextView
        android:id="@id/user_prompt_agree_use"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/user_prompt_agree_use_selector"
        android:paddingLeft="@dimen/x50"
        android:paddingTop="@dimen/x10"
        android:paddingRight="@dimen/x50"
        android:paddingBottom="@dimen/x10"
        android:text="@string/user_prompt_agree_use_str"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size6"
        android:layout_marginEnd="@dimen/x104"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/user_prompt_disagree_use"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/user_prompt_agree_image"
        app:layout_constraintVertical_bias="0.3" />

    <TextView
        android:id="@id/user_prompt_disagree_use"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/disagree_btn"
        android:paddingLeft="@dimen/x50"
        android:paddingTop="@dimen/x10"
        android:paddingRight="@dimen/x50"
        android:paddingBottom="@dimen/x10"
        android:text="@string/disagree_str"
        android:textColor="@color/disagree_text_selector"
        android:textSize="@dimen/text_size6"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/user_prompt_agree_use"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/user_prompt_agree_image"
        app:layout_constraintVertical_bias="0.3" />
</androidx.constraintlayout.widget.ConstraintLayout>