package com.kaolafm.kradio.lib.utils.imageloader;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.os.Build;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import android.text.TextUtils;
import android.util.Log;
import android.widget.ImageView;
import com.bumptech.glide.Glide;
import com.bumptech.glide.integration.webp.decoder.WebpDrawable;
import com.bumptech.glide.integration.webp.decoder.WebpDrawableTransformation;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.Transformation;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.load.DecodeFormat;
import com.bumptech.glide.load.resource.bitmap.BitmapTransitionOptions;
import com.bumptech.glide.load.resource.bitmap.CenterCrop;
import com.bumptech.glide.load.resource.bitmap.CircleCrop;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions;
import com.bumptech.glide.load.resource.gif.GifDrawable;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.target.Target;
import com.bumptech.glide.request.transition.Transition;
import com.kaolafm.kradio.lib.BuildConfig;
import com.kaolafm.kradio.lib.base.flavor.KRadioImgLoaderLifecycleInterceptInter;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.imageloader.listener.OnGetBitmapListener;
import com.kaolafm.kradio.lib.utils.imageloader.transformation.BlurTransformation;
import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import java.io.File;
import java.util.concurrent.ExecutionException;

/**
 * Glide图片加载框架策略。
 *
 * <AUTHOR>
 * @date 2018/4/15
 */

public class GlideImageLoaderStrategy implements BaseImageLoaderStrategy<ImageConfigImpl>, BaseInterface {

    private KRadioImgLoaderLifecycleInterceptInter mKRadioImgLoaderLifecycleInterceptInter;

    public GlideImageLoaderStrategy() {
        mKRadioImgLoaderLifecycleInterceptInter = ClazzImplUtil.getInter("KRadioImgLoaderLifecycleInterceptImpl");
    }

    @SuppressLint("CheckResult")
    @Override
    public void loadImage(Context ctx, ImageConfigImpl config) {
        if (ctx == null || config == null) {
            Log.i("GlideImageLoaderS", "loadImage: Context=" + ctx + "或ImageConfigImpl=" + config + "为空");
            return;
        }
        if (mKRadioImgLoaderLifecycleInterceptInter != null && mKRadioImgLoaderLifecycleInterceptInter
                .canInterceptImgLoader()) {
            ctx = ctx instanceof Activity ? ctx.getApplicationContext() : ctx;
        }
        // 解决https://bugly.qq.com/v2/crash-reporting/crashes/bedf20cb76/16106/report?pid=1&search=&searchType=detail&bundleId=&channelId=dongfengqichen_chengdu&version=all&tagList=&start=0&date=allhttps://bugly.qq.com/v2/crash-reporting/crashes/bedf20cb76/16106/report?pid=1&search=&searchType=detail&bundleId=&channelId=dongfengqichen_chengdu&version=all&tagList=&start=0&date=all问题
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1 && ctx instanceof Activity) {
            if (((Activity) ctx).isDestroyed()) {
                return;
            }
        }
        //如果context是activity则自动使用Activity的生命周期
        switch (config.getImgType()) {
            case ImageConfigImpl.TYPE_DRAWABLE:
                GlideRequest<Drawable> drawableRequest = GlideApp.with(ctx).asDrawable();
                if (config.isCrossFade()) {
                    drawableRequest.transition(DrawableTransitionOptions.withCrossFade());
                }
                load(config, drawableRequest);
                break;
            case ImageConfigImpl.TYPE_BITMAP:
                GlideRequest<Bitmap> bitmapRequest = GlideApp.with(ctx).asBitmap()
                        .placeholder(config.placeHolderDrawale);
                if (config.isCrossFade()) {
                    bitmapRequest.transition(BitmapTransitionOptions.withCrossFade());
                }
                load(config, bitmapRequest);
                break;
            case ImageConfigImpl.TYPE_GIF:
                GlideRequest<GifDrawable> gifRequest = GlideApp.with(ctx).asGif();
                load(config, gifRequest);
                break;
            default:
                GlideRequests requests = GlideApp.with(ctx);
                GlideRequest request;
                if (!TextUtils.isEmpty(config.getUrl())) {
                    if (config.isLocal()) {
                        request = requests.load(new File(config.getUrl()));
                    }else {
                        request = requests.load(config.getUrl());
                    }
                } else {
                    request = requests.load(config.getResId());
                }
                config(config, request);
                listener(config, request);
                into(config, request);
        }
    }

    @SuppressLint("CheckResult")
    public <T> void load(ImageConfigImpl config, GlideRequest<T> requests) {
        GlideRequest<T> glideRequest;
        if (!TextUtils.isEmpty(config.getUrl())) {
            if (config.isLocal()) {
                glideRequest = requests.load(new File(config.getUrl()));
            }else {
                glideRequest = requests.load(config.getUrl());
            }
        } else {
            glideRequest = requests.load(config.getResId());
        }
        config(config, glideRequest);
        listener(config, glideRequest);
        into(config, glideRequest);

    }

    private <T> void into(ImageConfigImpl config, GlideRequest<T> glideRequest) {
        if (config.getImageView() != null) {
            glideRequest.into(config.getImageView());
        } else {
            glideRequest.into(new SimpleTarget<T>() {
                @Override
                public void onResourceReady(@NonNull T resource, @Nullable Transition<? super T> transition) {
                    if (config.getOnBitmapListener() != null && resource instanceof Bitmap) {
                        config.getOnBitmapListener().onBitmap((Bitmap) resource);
                    } else if (config.getOnDrawableListener() != null && resource instanceof Drawable) {
                        config.getOnDrawableListener().onDrawable((Drawable) resource);
                    }
                }
            });
        }
    }

    @SuppressLint("CheckResult")
    private <T> void listener(ImageConfigImpl config, GlideRequest<T> glideRequest) {
        if (config.getOnImageLoaderListener() != null) {
            glideRequest.listener(new RequestListener<T>() {
                @Override
                public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<T> target,
                        boolean isFirstResource) {
                    config.getOnImageLoaderListener().onLoadingFailed(config.url, config.imageView, e);
                    return false;
                }

                @Override
                public boolean onResourceReady(T resource, Object model, Target<T> target, DataSource dataSource,
                        boolean isFirstResource) {
                    config.getOnImageLoaderListener().onLoadingComplete(config.url, config.imageView);
                    return false;
                }
            });
        }
    }

    @SuppressLint("CheckResult")
    private <T> void config(ImageConfigImpl config, GlideRequest<T> glideRequest) {
        //缓存策略
        DiskCacheStrategy cacheStrategy = config.getCacheStrategy();
        if (cacheStrategy != null) {
            glideRequest.diskCacheStrategy(cacheStrategy);
        }

        transform(config, glideRequest);

        //设置占位符
        if (config.getPlaceHolder() != null) {
            glideRequest.placeholder(config.getPlaceHolder());
        }

        //设置错误的图片
        if (config.getErrorPic() != null) {
            glideRequest.error(config.getErrorPic());
        }

        //设置请求 url 为空图片
        if (config.getFallback() != null) {
            glideRequest.fallback(config.getFallback());
        }
        //设置图片大小
        if (config.getSize() > 0) {
            glideRequest.override(config.getSize());
        }
        //是否缓存到内存
        glideRequest.skipMemoryCache(!config.isCacheInMemory());

        // CPU优化：设置图片解码格式为RGB_565以减少内存使用
        glideRequest.format(DecodeFormat.PREFER_RGB_565);
    }

    @SuppressLint("CheckResult")
    private <T> void transform(ImageConfigImpl config, GlideRequest<T> glideRequest) {
        if (config.isCenterCrop()) {
            applyTransform(glideRequest, new CenterCrop());
        }

        if (config.isCircle()) {
            applyTransform(glideRequest, new CircleCrop());
        }

        if (config.isImageRadius()) {
            applyTransform(glideRequest, new RoundedCorners(config.getImageRadius()));
        }

        if (config.isBlurImage()) {
            applyTransform(glideRequest, new BlurTransformation(config.getBlurValue()));
        }

        //glide用它来改变图形的形状
        if (config.getTransformations() != null) {
            applyTransform(glideRequest, config.getTransformations());
        }
    }

    @SuppressLint("CheckResult")
    private <T> void applyTransform(GlideRequest<T> glideRequest, Transformation... transformations) {
        if (BuildConfig.SUPPORT_WEBP_ANIMATION) {
            for (Transformation transformation : transformations) {
                glideRequest.optionalTransform(transformation)
                        .optionalTransform(WebpDrawable.class, new WebpDrawableTransformation(transformation));
            }
        } else {
            glideRequest.transforms(transformations);
        }
    }


    @Override
    public void loadImage(Fragment fragment, ImageConfigImpl config) {
        if (fragment != null && fragment.getActivity() != null) {
            load(config, GlideApp.with(fragment).asDrawable());
        } else {
            Log.i("GlideImageLoaderS", String.format("loadImage: %s为空或者还没attached或已经destroy", fragment));
        }
    }

    @Override
    public void clear(Context ctx, ImageConfigImpl config) {
        if (ctx == null && config == null) {
            Log.i("GlideImageLoaderS", "clear: Context=" + ctx + "或ImageConfigImpl=" + config + "为空");
            return;
        }
        //如果还有图片加载就停止加载。
        cancelRequest(ctx, config);
        //清除本地缓存
        clearDiskCache(ctx, config);
        //清除内存缓存
        clearMemory(ctx, config);
    }

    @Override
    public void destroy(Context context) {
    }

    @Override
    public File getCacheDir(Context context) {
        if (context != null) {
            return GlideApp.getPhotoCacheDir(context, GlideModuleConfiguration.IMAGE_DISK_CACHE_NAME);
        } else {
            return null;
        }
    }

    @Override
    public Bitmap getBitmapFromCache(Context context, String url) {
        return getBitmapFromCache(context, url, Target.SIZE_ORIGINAL);
    }

    @Override
    public Bitmap getBitmapFromCache(Context context, String url, int size) {
        if (context == null && TextUtils.isEmpty(url)) {
            return null;
        }
        Bitmap bitmap = null;
        try {
            bitmap = Glide.with(context).asBitmap().load(url).submit(size, size).get();
        } catch (InterruptedException e) {
            e.printStackTrace();
        } catch (ExecutionException e) {
            e.printStackTrace();
        }
        return bitmap;
    }

    @Override
    public void getBitmapFromCache(Context context, String url, OnGetBitmapListener listener) {
        if (context != null) {
            GlideApp.with(context).asBitmap().load(url).into(new SimpleTarget<Bitmap>() {
                @Override
                public void onResourceReady(@NonNull Bitmap resource,
                        @Nullable Transition<? super Bitmap> transition) {
                    if (listener != null) {
                        listener.onBitmap(resource);
                    }
                }
            });
        }
    }

    @Override
    public void getBitmapFromCache(Context context, String url, int radius, OnGetBitmapListener listener) {
        if (context != null) {
            GlideApp.with(context).asBitmap().load(url).transform(new RoundedCorners(radius)).into(new SimpleTarget<Bitmap>() {
                @Override
                public void onResourceReady(@NonNull Bitmap resource,
                                            @Nullable Transition<? super Bitmap> transition) {
                    if (listener != null) {
                        listener.onBitmap(resource);
                    }
                }
            });
        }
    }

    @Override
    public void getBitmapFromCache(Context context, int resId, int radius, OnGetBitmapListener listener) {
        if (context != null) {
            GlideApp.with(context).asBitmap().load(resId).transform(new RoundedCorners(radius)).into(new SimpleTarget<Bitmap>() {
                @Override
                public void onResourceReady(@NonNull Bitmap resource,
                                            @Nullable Transition<? super Bitmap> transition) {
                    if (listener != null) {
                        listener.onBitmap(resource);
                    }
                }
            });
        }
    }

    /**
     * 清理内存缓存
     */
    public void clearMemory(Context ctx, ImageConfigImpl config) {
        if (ctx != null && config.isClearMemory()) {
            Observable.just(0)
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(integer -> GlideApp.get(ctx).clearMemory());
        }
    }

    /**
     * 清理内存缓存
     */
    @Override
    public void clearMemoryCache(Context context) {
        if (context != null) {
            Observable.just(0)
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(integer -> GlideApp.get(context).clearMemory());
        }
    }

    /**
     * 清理磁盘缓存
     */
    public void clearDiskCache(Context ctx, ImageConfigImpl config) {
        if (ctx != null && config.isClearDiskCache()) {
            Observable.just(0)
                    .observeOn(Schedulers.io())
                    .subscribe(integer -> GlideApp.get(ctx).clearDiskCache());
        }
    }

    /**
     * 清理磁盘缓存
     */
    @Override
    public void clearDiskCache(Context context) {
        Observable.just(0)
                .observeOn(Schedulers.io())
                .subscribe(integer -> GlideApp.get(context).clearDiskCache());
    }

    /**
     * 取消正在加载中的图片加载
     */
    public void cancelRequest(Context ctx, ImageConfigImpl config) {
        if (config.getImageViews() != null && config.getImageViews().length > 0) {
            for (ImageView imageView : config.getImageViews()) {
                GlideApp.get(ctx).getRequestManagerRetriever().get(ctx).clear(imageView);
            }
        }
    }

    @Override
    public void pauseRequests(Context context) {
        GlideRequests request = request(context);
        if (request != null) {
            request.pauseRequests();
        }
    }

    @Override
    public void resumeRequests(Context context) {
        GlideRequests request = request(context);
        if (request != null) {
            request.resumeRequests();
        }
    }

    @Override
    public void pauseRequests(Activity activity) {
        GlideRequests request = request(activity);
        if (request != null) {
            request.pauseRequests();
        }
    }

    @Override
    public void resumeRequests(Activity activity) {
        GlideRequests request = request(activity);
        if (request != null) {
            request.resumeRequests();
        }
    }

    @Override
    public void pauseRequests(FragmentActivity fragmentActivity) {
        GlideRequests request = request(fragmentActivity);
        if (request != null) {
            request.pauseRequests();
        }
    }

    @Override
    public void resumeRequests(FragmentActivity fragmentActivity) {
        GlideRequests request = request(fragmentActivity);
        if (request != null) {
            request.resumeRequests();
        }
    }

    @Override
    public void pauseRequests(android.app.Fragment fragment) {
        GlideRequests request = request(fragment);
        if (request != null) {
            request.pauseRequests();
        }
    }

    @Override
    public void resumeRequests(android.app.Fragment fragment) {
        GlideRequests request = request(fragment);
        if (request != null) {
            request.resumeRequests();
        }
    }

    @Override
    public void pauseRequests(Fragment fragment) {
        GlideRequests request = request(fragment);
        if (request != null) {
            request.pauseRequests();
        }
    }

    @Override
    public void resumeRequests(Fragment fragment) {
        GlideRequests request = request(fragment);
        if (request != null) {
            request.resumeRequests();
        }
    }

    private GlideRequests request(Context context) {
        if (context != null) {
            return GlideApp.with(context);
        } else {
            Log.i("GlideImageLoaderS", "request: context为空");
            return null;
        }
    }

    private GlideRequests request(Fragment fragment) {
        if (fragment != null && fragment.getActivity() != null) {
            return GlideApp.with(fragment);
        } else {
            Log.i("GlideImageLoaderS", String.format("request: fragment为空或者%s还没attached或已经destroy", fragment));
            return null;
        }
    }

    private GlideRequests request(android.app.Fragment fragment) {
        if (fragment != null && fragment.getActivity() != null) {
            return GlideApp.with(fragment);
        } else {
            Log.i("GlideImageLoaderS", String.format("request: fragment为空或者%s还没attached或已经destroy", fragment));
            return null;
        }
    }

    private GlideRequests request(FragmentActivity activity) {
        if (activity != null && !activity.isDestroyed() && !activity.isFinishing()) {
            return GlideApp.with(activity);
        } else {
            Log.i("GlideImageLoaderS", String.format("request: activity为空或者%s已经finish或destroy", activity));
            return null;
        }
    }

    private GlideRequests request(Activity activity) {
        if (activity != null && !activity.isFinishing() && !activity.isDestroyed()) {
            return GlideApp.with(activity);
        } else {
            Log.i("GlideImageLoaderS", String.format("request: activity为空或者%s已经finish或destroy", activity));
            return null;
        }
    }

}
