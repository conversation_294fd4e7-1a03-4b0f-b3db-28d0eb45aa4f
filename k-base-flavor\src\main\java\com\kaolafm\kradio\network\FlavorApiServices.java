package com.kaolafm.kradio.network;

import com.kaolafm.kradio.network.model.DefaultPlayData;
import com.kaolafm.kradio.flavor.utils.FlavorConstants;
import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BaseResult;
import io.reactivex.Single;
import java.util.List;
import retrofit2.http.GET;
import retrofit2.http.Headers;

interface FlavorApiServices {
    /**
     * 默认播放信息
     */
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(FlavorConstants.REQUEST_GET_DEFAULT_PLAYINFO)
    Single<BaseResult<List<DefaultPlayData>>> getDefaultPlayInfo();

}
