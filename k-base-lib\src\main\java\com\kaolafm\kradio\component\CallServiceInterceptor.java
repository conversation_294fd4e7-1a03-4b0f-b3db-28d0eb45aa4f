package com.kaolafm.kradio.component;

import android.os.Looper;
import com.kaolafm.kradio.component.ComponentResult.Code;
import com.kaolafm.opensdk.log.Logging;

/**
 * 最后执行的拦截器，并将结果返回
 *
 * <AUTHOR>
 * @date 2019-07-01
 */
class CallServiceInterceptor implements ComponentInterceptor {

    private CallServiceInterceptor() {
    }

    private static class CallServiceInterceptorHolder {

        private static final CallServiceInterceptor INSTANCE = new CallServiceInterceptor();
    }

    public static CallServiceInterceptor getInstance() {
        return CallServiceInterceptorHolder.INSTANCE;
    }

    @Override
    public ComponentResult intercept(ComponentChain chain) {
        ComponentClient client = chain.client();
        RealCaller caller = chain.caller();
        String componentName = client.componentName();
        Component component = ComponentManager.getComponentByName(componentName);
        if (component == null) {
            return ComponentResult.error(Code.ERROR_NO_COMPONENT_FOUND);
        }
        try {
            boolean shouldSwitchThread = false;
            CallServiceRunnable callServiceRunnable = new CallServiceRunnable(caller, component);
            if (component instanceof MainThreadable) {
                Logging.d("intercept: 控制运行所在线程%s", component);
                boolean currentThreadIsMain = Looper.myLooper() == Looper.getMainLooper();
                Boolean runOnMainThread = ((MainThreadable) component).shouldActionRunOnMainThread(componentName, client);
                shouldSwitchThread = runOnMainThread != null && runOnMainThread ^ currentThreadIsMain;
                //是否需要切换线程。
                if (shouldSwitchThread) {
                    callServiceRunnable.setShouldSwitchThread(true);
                    Logging.d("intercept: %s在%s线程中运行", component, runOnMainThread? "主":"子");
                    if (runOnMainThread) {
                        //回调需要运行在主线程，当前线程是子线程
                        client.dispatcher().mainThread(callServiceRunnable);
                    } else {
                        //回调需要运行在子线程，当前是主线程。
                        client.dispatcher().threadPool(callServiceRunnable);
                    }
                }
            }
            //不需要切换线程，直接运行。
            if (!shouldSwitchThread) {
                Logging.d("intercept: %s不需要切换线程，直接运行。", callServiceRunnable);
                callServiceRunnable.run();
            }
            if (!caller.isFinished()) {
                chain.proceed();
            }
        } catch (Exception e) {
            return ComponentResult.defaultExceptionResult(e);
        }

        return chain.caller().result();
    }

    static class CallServiceRunnable implements Runnable {

        private RealCaller mCaller;

        private Component component;

        private boolean switchThread = false;

        public CallServiceRunnable(RealCaller caller, Component component) {
            this.mCaller = caller;
            this.component = component;
            Thread.currentThread().setName("CallServiceRunnable-"+component.getClass().getName()+"-"+caller.getCallId());
        }

        void setShouldSwitchThread(boolean switchThread) {
            this.switchThread = switchThread;
        }

        @Override
        public void run() {

            try {
                //被取消的时候就不在执行组件的逻辑。
                if (mCaller.isFinished()) {
                    return;
                }
                boolean callbackDelay = component.onCall(mCaller);
                //如果是同步回调接口，并且没有主动发送结果。
                if (!callbackDelay && !mCaller.isFinished()) {
                    Logging.d("run: %s不是异步获取结果，且没有收到结果，发送ERROR_NULL_RESULT", component);
                    setResult(ComponentResult.defaultNullResult());
                }
                if (callbackDelay) {
                    Logging.d("run: %s异步获取结果，等待结果", component);
                }
            } catch (Exception e) {
                Logging.e("处理action时发生%s的异常", e);
                setResult(ComponentResult.defaultExceptionResult(e));
            }
        }

        private void setResult(ComponentResult result) {
            if (switchThread) {
                mCaller.setResultForWaiting(result);
            } else {
                mCaller.setResult(result);
            }
        }
    }
}
