package com.kaolafm.kradio.lib.location.model;

/**
 * @ClassName IKaoLaLocation
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/5/26 14:43
 * @Version 1.0
 */
public interface IKaoLaLocation {
    int TIME_INTERVAL = 60 * 5 * 1000;
    LocationModel getLocation();


    void addLocationListener(IKaoLaLocationListener listener);

    void removeLocationListener(IKaoLaLocationListener listener);

    interface IKaoLaLocationListener {
        void locationChange(LocationModel location);
    }

    void destroy();

    default boolean initSuccess() {
        return true;
    }

    default void reInit(){

    }
}
