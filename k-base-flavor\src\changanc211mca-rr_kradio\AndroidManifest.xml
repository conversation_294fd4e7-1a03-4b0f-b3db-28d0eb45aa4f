<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.kaolafm.kradio.flavor"
    android:sharedUserId="android.uid.system">

    <uses-sdk tools:overrideLibrary="com.incall.apps.commoninterface"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>

    <application>

        <receiver
            android:name="com.kaolafm.kradio.util.SpeechManager$ReceiveFromSpeechReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com.iflytek.autofly.handMessage" />
            </intent-filter>
        </receiver>

        <service
            android:name="com.kaolafm.kradio.service.KRadioInitService"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com.kaolafm.kradio.service.AUTO_PLAY"/>
            </intent-filter>
        </service>

        <service android:name="com.kaolafm.kradio.service.EmptyService"/>

    </application>
</manifest>


