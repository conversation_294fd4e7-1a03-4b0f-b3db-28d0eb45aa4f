package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.app.ActivityManager;
import android.content.Context;
import android.os.Build;
import androidx.annotation.RequiresApi;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentActivity;
import android.view.Gravity;
import android.view.View;

import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioHomeExitInter;
import com.kaolafm.kradio.lib.dialog.Dialogs;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;

import java.util.List;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: lidan
 * @time: 2021-05-27 15:54
 ******************************************/
public class KRadioHomeExitImpl implements KRadioHomeExitInter {

    private static final String TAG = "KRadioHomeExitImpl";

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)

    @Override
    public boolean isShowHomeExitBtn(View view, boolean show, Object... args) {
        final Activity activity = (Activity) args[0];
        view.setVisibility(show ? View.VISIBLE : View.GONE);
        int expand = (int) view.getResources().getDimension(R.dimen.m12);
        ViewUtil.expandViewTouchDelegate(view, expand, expand, expand, expand);
        view.setOnClickListener(v -> {

            DialogFragment dialogFragment = new Dialogs.Builder()
                    .setType(Dialogs.TYPE_2BTN)
                    .setGravity(Gravity.CENTER)
                    .setLeftBtnText(ResUtil.getString(R.string.move_to_background_str))
                    .setRightBtnText(ResUtil.getString(R.string.ok))
                    .setMessage(ResUtil.getString(R.string.exit_msg_str))
                    .setOnPositiveListener(dialog -> {
                        dialog.dismiss();
                        // 销毁任务栈
                        ActivityManager activityManager = (ActivityManager) AppDelegate.getInstance().getContext().getSystemService(Context.ACTIVITY_SERVICE);
                        List<ActivityManager.AppTask> appTaskList = activityManager.getAppTasks();
                        for (ActivityManager.AppTask appTask : appTaskList) {
                            appTask.finishAndRemoveTask();
                        }
                    }).setOnNativeListener(dialog -> {
                        dialog.dismiss();
                        activity.moveTaskToBack(true);
                    })
                    .create();
            dialogFragment.show(((FragmentActivity) activity).getSupportFragmentManager(), "exit_app");
        });

        return false;
    }
}
