package com.kaolafm.notify;

import android.app.Activity;

/**
 * <AUTHOR>
 **/
public class NotifyParam {

    public Activity activity;
    public int type;
    public String iconUrl;
    public String msg;
    public NotifyCallback callback;

    public String json;

    public static class Builder {

        private Activity activity;
        private int type;
        private String iconUrl;
        private String msg;
        private NotifyCallback callback;

        private String json;

        public Builder(int type) {
            this.type = type;
        }

        public Builder activity(Activity activity) {
            this.activity = activity;
            return this;
        }

        public Builder icon(String iconUrl) {
            this.iconUrl = iconUrl;
            return this;
        }

        public Builder msg(String msg) {
            this.msg = msg;
            return this;
        }

        public Builder callback(NotifyCallback callback) {
            this.callback = callback;
            return this;
        }


        public Builder json(String json) {
            this.json = json;
            return this;
        }


        public NotifyParam build() {
            NotifyParam np = new NotifyParam();
            np.activity = this.activity;
            np.type = this.type;
            np.iconUrl = this.iconUrl;
            np.msg = this.msg;
            np.callback = this.callback;
            np.json = this.json;
            return np;
        }
    }

}
