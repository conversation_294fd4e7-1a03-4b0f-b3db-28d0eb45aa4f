package com.kaolafm.kradio.lib.init;

/**
 * 实现该接口并添加注解{@link ModelInit}的类会被自动添加到管理中{@link ModelInitManager}根据其配置进行初始化相关接口的回调。
 * <AUTHOR>
 * @date 2022-06-30
 */
public interface ModelInitializable {

    /**
     * 会在切换模式的onInit()方法中同步回调。
     * @param MODEL
     */
    void onInit(int MODEL);

    /**
     * 会在切换模式的onInit()方法中开启线程异步回调，该方法只有在{@link ModelInit#isAsync()}返回true的时候才会执行。
     * @param MODEL
     */
    void asyncInit(int MODEL);
}
