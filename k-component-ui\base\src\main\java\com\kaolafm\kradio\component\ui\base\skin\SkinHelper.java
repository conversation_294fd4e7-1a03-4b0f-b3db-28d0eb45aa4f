package com.kaolafm.kradio.component.ui.base.skin;

import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;

import com.kaolafm.kradio.component.SharedConst;
import com.kaolafm.kradio.lib.BuildConfig;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import skin.support.SkinCompatManager;

/**
 * <AUTHOR>
 **/
@SharedConst
public class SkinHelper {

    public static final String DAY_SKIN = "day.skin";
    public static final String NIGHT_SKIN = "night.skin";
    public static final String PRE_SKIN_NAME = "pre_skin_name";
    public static final String IS_FIRST_LAUNCH = "is_first_launch";
    public static final String IS_SAME_THEME = "isSameTheme";
    public static final String AUTO_SKIN = "autoSkin";
    public static final String IS_AUTO_SKIN = "isAutoSkin";


    /**
     * 获取当前皮肤
     *
     * @return
     */
    public static String getCurSkinName() {
        return SkinCompatManager.getInstance().getCurSkinName();
    }

    /**
     * 获取皮肤对应的显示名
     *
     * @param skinFileName
     * @return
     */
    public static String getSkinDisplayName(String skinFileName) {
        String rst;
        switch (skinFileName) {
            case "gray.skin":
                rst = "烟灰色版";
                break;
            case "yellow.skin":
                rst = "金色版";
                break;
            case "blue.skin":
                rst = "青色版";
                break;
            case "night.skin":
                rst = "夜间模式";
                break;
            case "day.skin":
                rst = "白天模式";
                break;
            default:
                rst = "蓝色版（默认）";
                break;
        }
        return rst;
    }

    /**
     * BaseActivity#isAutoSkin 中通过反射调用此方法, 所以需要添加混淆规则来保证此类的类名和方法名不能被混淆
     * 混淆规则: -keep class com.kaolafm.kradio.component.ui.base.skin.SkinHelper{*;}
     */
    public static boolean getIsAutoSkin(Context context) {
        if (!BuildConfig.CAN_AUTO_THEME){
            return false;
        }
        SharedPreferences sp = context.getSharedPreferences("kradio.skin", Context.MODE_PRIVATE);
        boolean isAutoSkin = sp.getBoolean(IS_AUTO_SKIN, false);
        return isAutoSkin;
    }

    public static void setIsAutoSkin(Context context, boolean isAutoSkin) {
        SharedPreferences sp = context.getSharedPreferences("kradio.skin", Context.MODE_PRIVATE);
        sp.edit().putBoolean(IS_AUTO_SKIN, isAutoSkin).apply();
    }


    /**
     * 遍历Assets下的所有皮肤
     *
     * @param context
     * @return
     */
    public static List<String> getAllSkinsFromAssets(Context context) {
        List<String> skins = new ArrayList<>();
        skins.add("");
        try {
            String[] skinF = context.getAssets().list("skins");
            if (skinF != null && skinF.length > 0) {
                for (String skin : skinF) {
                    if (!TextUtils.isEmpty(skin) && !DAY_SKIN.equals(skin)) {
                        skins.add(skin);
                    }
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return skins;
    }

    /**
     * 是否夜间模式
     *
     * @return 默认皮肤返回""
     */
    public static boolean isDayMode() {
        String curSkinName = SkinCompatManager.getInstance().getCurSkinName();
        return DAY_SKIN.equals(curSkinName);
    }

    /**
     * 是否是夜间模式
     * @return true 是， false 否
     */
    public static boolean isNightMode() {
        String curSkinName = SkinCompatManager.getInstance().getCurSkinName();
        return NIGHT_SKIN.equals(curSkinName);
    }

    public static List<String> getDayNightSkins(Context context) {
        List<String> rst = new ArrayList<>();
        rst.add(DAY_SKIN);
        rst.add(getPreSkinName(context));
        return rst;
    }

    public static String getPreSkinName(Context context) {
        SharedPreferences sp = context.getSharedPreferences("kradio.skin", Context.MODE_PRIVATE);
        String pre_skin_name = sp.getString(PRE_SKIN_NAME, "");
        return pre_skin_name;
    }

    public static void setSkinName(Context context, String skinName) {
        if (DAY_SKIN.equals(skinName)) {
            return;
        }
        SharedPreferences sp = context.getSharedPreferences("kradio.skin", Context.MODE_PRIVATE);
        sp.edit().putString(PRE_SKIN_NAME, skinName).commit();
    }

    /**
     * 是否第一次启动
     *
     * @param context
     * @return
     */
    public static boolean isFirstLaunch(Context context) {
        SharedPreferences sp = context.getSharedPreferences("kradio.skin", Context.MODE_PRIVATE);
        boolean isFirstLaunch = sp.getBoolean(IS_FIRST_LAUNCH, true);
        if (isFirstLaunch) {
            sp.edit().putBoolean(IS_FIRST_LAUNCH, false).commit();
        }
        return isFirstLaunch;
    }
}
