package com.kaolafm.kradio.category.base;

import androidx.lifecycle.Lifecycle.State;
import android.os.Bundle;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup.MarginLayoutParams;
import android.view.ViewStub;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.flyco.tablayout.SlidingTabLayout;
import com.flyco.tablayout.listener.OnTabSelectListener;
import com.kaolafm.kradio.category.ErrorCode;
import com.kaolafm.kradio.category.base.TabContract.IPresenter;
import com.kaolafm.kradio.category.base.TabContract.IView;
import com.kaolafm.kradio.lib.bean.SubcategoryItemBean;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.categories.CategoriesFragmentAdapter;
import com.kaolafm.kradio.categories.CategoryConstant;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.ui.BaseViewPagerLazyFragment;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.opensdk.http.error.ApiException;

import java.util.List;


/**
 * 所有分类页面tab页面的基类
 *
 * <AUTHOR>
 **/
public abstract class BaseTabFragment extends BaseViewPagerLazyFragment<IPresenter> implements IView {

    private static final String TAG = "BaseTabFragment";

    private Runnable runnable;

    LinearLayout mLlSubcategoryRoot;
    protected SlidingTabLayout mStbSubcategoryTabTitle;
    LinearLayout mTabLayoutContent;
    ImageView mTvRefreshBtn;
    View mViewDivider;
    ViewPager mVpSubcategoryContent;
    ViewStub mVsLayoutErrorPage;


    @Override
    protected void changeViewLayoutForStatusBar(View view) {
        // Do-Nothing
        //因为是子fragment,所以不需要设置pading.
    }

    private CategoriesFragmentAdapter mAdapter;

    @Override
    public void showData(String[] titles, List<Fragment> fs, int showIndex) {
        hideErrorLayout();
        for (int i = 0; i < titles.length; i++) {
            Log.i(TAG, "showData: titles = " + titles[i]);
        }
        mAdapter.updateFragments(fs);
        mStbSubcategoryTabTitle.setViewPager(mVpSubcategoryContent, titles);
        // 二级导航栏切换到落地页
        mStbSubcategoryTabTitle.setCurrentTab(showIndex);
        //配合lazyfragment,避免预加载
        if (fs != null)
            mVpSubcategoryContent.setOffscreenPageLimit(fs.size());
    }

    @Override
    public void showContent(String[] titles, List<SubcategoryItemBean> itemBeans, int showIndex) {

    }

    @Override
    public void showError(Exception e) {
        // TODO: 2019/3/1 novelot 具体改
        Log.i(TAG, "showError: error = " + e.getMessage());
        String str = null;
        if (e instanceof ApiException) {
            switch (((ApiException) e).getCode()) {
                case ErrorCode.NO_NET:
                    str = ResUtil.getString(R.string.no_net_work_str);
                    break;
                case ErrorCode.NO_SUBCATEGORY:
                case ErrorCode.TYPE_ERROR:
                    str = ResUtil.getString(R.string.error_subcategory_is_null);
                    break;
                default:
            }
        }
        if (str != null) {
            ToastUtil.showError(getContext(), str);
        }

    }

    private View mErrorView;

    public void showNoNetWorkView() {
        if (mVsLayoutErrorPage == null) {
            return;
        }
        if (mVsLayoutErrorPage != null) {
            mErrorView = mVsLayoutErrorPage.inflate();
            TextView tvNetworkError = mErrorView.findViewById(R.id.tv_status_page_network_error);
            mVsLayoutErrorPage = null;
            if (tvNetworkError != null) {
                tvNetworkError.setOnClickListener(v -> {
                    if (!AntiShake.check(v.getId())) {
                        if (!NetworkUtil.isNetworkAvailable(getContext(), true)) {
                            return;
                        }
                        if (mPresenter != null) {
                            showLoading();
                            lazyLoad();
                        }
                    }
                });
            }
        }
        isLoaded = false;
        ViewUtil.setViewVisibility(mErrorView, View.VISIBLE);
        hideLoading();
    }

    private void hideErrorLayout() {
        ViewUtil.setViewVisibility(mErrorView, View.GONE);
    }

    @Override
    protected void lazyLoad() {
        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
            showNoNetWorkView();
            return;
        }
        hideErrorLayout();
        State currentState = this.getLifecycle().getCurrentState();
        if (currentState.isAtLeast(State.CREATED)) {
            Bundle arg = getArguments();
            if (arg != null) {
                long showTabId = arg.getLong(CategoryConstant.CATEGORY_ID);
                long mediaType = arg.getLong(CategoryConstant.MEDIA_TYPE, -1);
//                if (mediaType >= 0) {
//                    mPresenter.loadAIData(showTabId);
//                } else {
                mPresenter.loadData(showTabId);
//                }
            }
        }

    }

    @Override
    protected int getLayoutId() {
        return R.layout.fragment_base_tab;
    }


    @Override
    public void initView(View view) {

        mLlSubcategoryRoot=view.findViewById(R.id.ll_subcategory_root);
        mStbSubcategoryTabTitle=view.findViewById(R.id.stb_subcategory_tab_title);
        mTabLayoutContent=view.findViewById(R.id.tabLayoutContent);
        mTvRefreshBtn=view.findViewById(R.id.tv_refresh_btn);
        mViewDivider=view.findViewById(R.id.view_divider);
        mVpSubcategoryContent=view.findViewById(R.id.vp_subcategory_content);
        mVsLayoutErrorPage=view.findViewById(R.id.vs_layout_error_page);


        setupViews();
        // 调试用,使用该句,可以显示界面;去掉后不显示
        //mPresenter.loadData();
    }

    private void setupViews() {
        if (mAdapter == null) {
            mAdapter = new CategoriesFragmentAdapter(getChildFragmentManager(), null, null);
        }
        mVpSubcategoryContent.setAdapter(mAdapter);
        //配合lazyfragment,避免预加载
//        mVpSubcategoryContent.setOffscreenPageLimit(0);
        mStbSubcategoryTabTitle.setViewPager(mVpSubcategoryContent);
        mStbSubcategoryTabTitle.setOnTabSelectListener(new OnTabSelectListener() {
            @Override
            public void onTabSelect(int position) {
                NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), true);
                onTabSelected(position);
            }

            @Override
            public void onTabReselect(int position) {

            }
        });
    }

    protected void onTabSelected(int position) {

    }

    @Override
    protected void showAccordingToScreen(int orientation) {
        int paddingRight = ScreenUtil.getGlobalPaddingRight(orientation);

        //因为mTabLayout的item如果设置了item的padding,为了与页面的返回按钮对齐,做特殊处理;
        int paddingLeft = (int) (ResUtil.getDimen(R.dimen.default_edge_start) - mStbSubcategoryTabTitle.getTabPadding());

        MarginLayoutParams layoutParams = (MarginLayoutParams) mStbSubcategoryTabTitle.getLayoutParams();
        layoutParams.leftMargin = paddingLeft;

        MarginLayoutParams layoutParamsRefresh = (MarginLayoutParams) mTvRefreshBtn.getLayoutParams();
        layoutParamsRefresh.rightMargin = paddingRight;

//        mAdapter.notifyDataSetChanged();
        mStbSubcategoryTabTitle.postDelayed(runnable = new Runnable() {
            @Override
            public void run() {
                if (mStbSubcategoryTabTitle != null) { //fixed 快速点击全部，返回会因为mStbSubcategoryTabTitle为null导致崩溃，因此需要判空处理
                    Log.i(TAG, "delayScroll:" + mStbSubcategoryTabTitle);
                    mStbSubcategoryTabTitle.onPageScrolled(mStbSubcategoryTabTitle.getCurrentTab(),
                            0, 0);
                }
            }
        }, 200);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        //fixed 快速点击全部，返回会因为mStbSubcategoryTabTitle为null导致崩溃，由于post处理时，view已被销毁，因此需要在此处移除post处理
        if (runnable != null && mStbSubcategoryTabTitle != null) {
            Log.i(TAG, "removeCallbacks:" + runnable);
            mStbSubcategoryTabTitle.removeCallbacks(runnable);
        }
    }

    @Override
    public String getPageId() {
        return Constants.COMPREHENSIVE_PAGE_ID_ALL_CATEGORIES;
    }
}
