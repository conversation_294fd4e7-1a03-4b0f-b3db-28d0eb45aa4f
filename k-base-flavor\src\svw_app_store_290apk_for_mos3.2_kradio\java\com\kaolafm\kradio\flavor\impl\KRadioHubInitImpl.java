//package com.kaolafm.kradio.flavor.impl;
//
//import android.app.Activity;
//import android.app.NotificationManager;
//import android.app.PendingIntent;
//import android.app.Popup;
//import android.content.Context;
//import android.content.Intent;
//import android.os.Bundle;
//import android.util.Log;
//
//import com.cns.android.location.POIManager;
//import com.kaolafm.kradio.flavor.ExitActivity;
//import com.kaolafm.kradio.flavor.OpenPrivacyActivity;
//import com.kaolafm.kradio.flavor.R;
//import com.kaolafm.kradio.lib.base.flavor.KRadioHubInitInter;
//
///**
// * author : wxb
// * date   : 2021/10/28
// * desc   :当“隐私模式”处于开启状态下，用户打开“云听”App，“云听”App将处于无网络、不可用状态，且在前端弹出popup，提示“抱歉，位置/语音服务开关已关闭，如需使用请前往设置中打开”
// */
//public class KRadioHubInitImpl implements KRadioHubInitInter {
//    @Override
//    public boolean hasPrivacyMode(Object... args) {
//        Log.d("KRadioHubInitImpl", "hasPrivacyMode in");
//
//        Activity activity = (Activity) args[0];
//        try {
//            if (!POIManager.getInstance(activity).isGpsUAuthorized()) {
//                //flase 不允许上传gps信息
//                showPopup(activity);
//                return true;
//            }
//            POIManager.getInstance(activity).registerListenGpsUAuthorized(new POIManager.GpsUAuthorizedChangeListener() {
//                @Override
//                public void onGpsUAuthorizedChange(Bundle bundle) {
//                    if (!bundle.getBoolean(POIManager.POIConstants.GPS_UPLOAD_AUTHORIZED)) {
//                        //flase 不允许上传gps信息
//                        showPopup(activity);
//                    }
//
//                }
//            });
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//
//        return false;
//    }
//
//    private void showPopup(Activity activity) {
//        Popup popup = new Popup(activity.getString(R.string.popu_msg), activity.getString(R.string.popu_locmsg));
//        //点击取消后退出应用
//        Intent intent = new Intent(activity, ExitActivity.class);
//        PendingIntent pendingIntent = PendingIntent.getActivity(activity, 9, intent, PendingIntent.FLAG_UPDATE_CURRENT);
//        popup.setActionOne(activity.getString(R.string.popu_cancle), pendingIntent);
//        //  跳转至账号主页页面:
//        intent = new Intent(activity, OpenPrivacyActivity.class);
//        pendingIntent = PendingIntent.getActivity(activity, 9, intent, PendingIntent.FLAG_UPDATE_CURRENT);
//        popup.setActionCancel(activity.getString(R.string.popu_setting), pendingIntent);
//        NotificationManager notificationManager = (NotificationManager) activity.getSystemService(Context.NOTIFICATION_SERVICE);
//        notificationManager.addPopup(9, popup);
//    }
//}
