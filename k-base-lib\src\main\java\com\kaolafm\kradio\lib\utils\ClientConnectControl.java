package com.kaolafm.kradio.lib.utils;

import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.kaolafm.kradio.lib.base.AppDelegate;

public class ClientConnectControl {
        public static ClientConnectControl instance = new ClientConnectControl();
        /**
         * @param context
         */
        public void notifyClient(Context context, int status) {
            Log.i("client.ClientConnect","ClientConnectControl");
            Intent intent = new Intent();
            intent.setAction("com.kaolafm.connection.action");
            intent.putExtra("status",status);
            context.sendBroadcast(intent);
        }
        /**
         * 开启APP时的广播
         */
        public void notifyAppOpen() {
            notifyClient(AppDelegate.getInstance().getContext(),0);
        }
    
        /**
         * 权限接受时的广播
         */
        public void notifyPermissionReceived() {
            notifyClient(AppDelegate.getInstance().getContext(),1);
        }
    
        /**
         * 权限拒绝时的广播
         */
        public void notifyPermissionRejected() {
            notifyClient(AppDelegate.getInstance().getContext(),-1);
        }
        /**
         * 协议同意时的广播
         */
        public void notifyProtocolReceived() {
            SharedPreferenceUtil.getInstance(AppDelegate.getInstance().getContext(), "k_notice").putBoolean("show", false);
            notifyClient(AppDelegate.getInstance().getContext(),2);
        }
        /**
         * 协议拒绝时的广播
         */
        public void notifyProtocolRejected() {
            SharedPreferenceUtil.getInstance(AppDelegate.getInstance().getContext(), "k_notice").putBoolean("show", true);
            notifyClient(AppDelegate.getInstance().getContext(),-2);
        }
        public boolean isProtocolReceived(){
            boolean userServiceAgreed = !SharedPreferenceUtil.getInstance(AppDelegate.getInstance().getContext(), "k_notice").getBoolean("show", true);
            Log.i("client.ClientConnect", "userServiceAgreed:" + userServiceAgreed);
            return userServiceAgreed;
        }
    }