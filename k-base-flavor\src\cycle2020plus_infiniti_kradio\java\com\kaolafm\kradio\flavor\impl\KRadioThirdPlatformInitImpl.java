package com.kaolafm.kradio.flavor.impl;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.Log;

import com.kaolafm.kradio.lib.base.flavor.KRadioThirdPlatformInitInter;
import com.kaolafm.opensdk.player.core.utils.PlayerCustomizeManager;
import com.kaolafm.opensdk.player.logic.PlayerManager;

/**
 * <AUTHOR>
 **/
public class KRadioThirdPlatformInitImpl implements KRadioThirdPlatformInitInter {
    private static final String TAG = "KRadioThirdPlatformInitImpl";

    @SuppressLint("LongLogTag")
    @Override
    public boolean initThirdPlatform(Object... args) {
        Context context = (Context) args[0];
        RiChanHelper.getInstance(context).init();
        PlayerCustomizeManager.getInstance().setNeedRequestAudioFocus(false);
        PlayerManager playerManager = PlayerManager.getInstance();
        if (!playerManager.isPlayerInitSuccess()) {
            Log.i(TAG, "initThirdPlatform------>setupPlayer start");
            PlayerManager.getInstance().init(context);
        }
        return true;
    }

    @Override
    public boolean destroyThirdPlatform(Object... args) {
        return false;
    }
}
