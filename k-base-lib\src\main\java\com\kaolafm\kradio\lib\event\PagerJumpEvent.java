package com.kaolafm.kradio.lib.event;

import android.os.Bundle;
import androidx.annotation.IntDef;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: PagerJumpEvent.java                                               
 *                                                                  *
 * Created in 2018/6/14 下午4:37                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
public class PagerJumpEvent {

    public static final int PAGE_CATEGORY = 0;//全部分类

    public static final int PAGE_ALL_FUNCTIONS = 1;

    public static final int PAGE_BROADCAST_LIST = 2;

    public static final int PAGE_PLAYER_DETAIL = 3;

    @IntDef({PAGE_CATEGORY, PAGE_ALL_FUNCTIONS,PAGE_BROADCAST_LIST, PAGE_PLAYER_DETAIL})
    public @interface Page {
    }


    public int page;
    public Bundle bundle;

    public PagerJumpEvent(@Page int page, Bundle bundle) {
        this.page = page;
        this.bundle = bundle;
    }


    @Override
    public String toString() {
        return "PagerJumpEvent{" +
                "page=" + page +
                ", bundle=" + bundle +
                '}';
    }
}
