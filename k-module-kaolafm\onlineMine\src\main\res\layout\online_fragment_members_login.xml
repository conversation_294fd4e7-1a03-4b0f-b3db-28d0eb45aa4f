<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:gravity="center"
    android:clipChildren="false">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_bg"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_width="@dimen/x672"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tv_title"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            android:layout_marginTop="@dimen/y60"
            android:layout_marginLeft="@dimen/x36"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text=""
            android:textStyle="bold"
            android:textSize="@dimen/text_size9" />

        <TextView
            android:id="@+id/tv_subtitle"
            app:layout_constraintTop_toBottomOf="@id/tv_title"
            app:layout_constraintLeft_toLeftOf="parent"
            android:layout_marginTop="@dimen/y12"
            android:layout_marginLeft="@dimen/x36"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text=""
            android:textSize="@dimen/text_size2" />

        <TextView
            android:id="@+id/tv_btn"
            app:layout_constraintTop_toBottomOf="@id/tv_subtitle"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginTop="@dimen/y52"
            android:layout_marginLeft="@dimen/x36"
            android:layout_marginBottom="@dimen/y24"
            android:layout_width="@dimen/m150"
            android:layout_height="@dimen/m50"
            android:gravity="center"
            android:text=""
            android:textColor="@color/online_members_vip_text_color"
            android:textSize="@dimen/text_size1" />

        <View
            android:id="@+id/line"
            android:layout_width="1px"
            android:layout_height="1px"
            android:layout_marginTop="@dimen/m170"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent"/>

        <ImageView
            android:id="@+id/iv_icon"
            app:layout_constraintBottom_toBottomOf="@id/line"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_marginRight="@dimen/x32"
            android:layout_width="@dimen/m134"
            android:layout_height="@dimen/m190"
            android:background="@drawable/online_members_unvip_icon"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/ll_mine_order"
        app:layout_constraintBottom_toTopOf="@id/cl_bg"
        app:layout_constraintRight_toRightOf="@id/cl_bg"
        android:layout_marginBottom="@dimen/y47"
        android:layout_width="@dimen/m146"
        android:layout_height="@dimen/m48"
        android:gravity="center"
        android:orientation="horizontal"
        android:background="@drawable/online_members_order_bg">

        <ImageView
            android:layout_width="@dimen/m22"
            android:layout_height="@dimen/m22"
            android:background="@drawable/online_members_order_icon"/>

        <TextView
            android:layout_marginLeft="@dimen/x5"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/mine_order"
            android:textColor="@color/online_members_order_text_color"
            android:textSize="@dimen/text_size1" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>