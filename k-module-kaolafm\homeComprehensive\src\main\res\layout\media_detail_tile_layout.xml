<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@id/media_detail_title"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/media_detail_fist_info_main_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.kaolafm.kradio.common.widget.CTextView
            android:id="@+id/media_detail_first_info_textView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/text_size3" />

        <com.kaolafm.kradio.common.widget.CTextView
            android:id="@+id/media_detail_second_info_textView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/text_size3" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/media_detail_second_info_main_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y8"
        android:orientation="horizontal">

        <com.kaolafm.kradio.common.widget.CTextView
            android:id="@+id/media_detail_third_info_textView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/text_size3" />

        <com.kaolafm.kradio.common.widget.CTextView
            android:id="@+id/media_detail_fourth_info_textView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/text_size3" />
    </LinearLayout>


    <View
        android:id="@+id/media_detail_line_view"
        android:layout_width="match_parent"
        android:layout_height="@dimen/common_line_height"
        android:layout_marginTop="@dimen/y34"
        android:background="@color/color_common_line" />
</LinearLayout>