package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.util.Log;

import com.hsae.autosdk.settings.AutoSettings;
import com.kaolafm.kradio.flavor.utils.DeviceInfoUtil;
import com.kaolafm.kradio.lib.base.flavor.DeviceInfoSetting;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;


/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-09-07 15:11
 ******************************************/
public final class DeviceInfoSettingImpl implements DeviceInfoSetting {
    private static final String TAG = "DeviceInfoSettingImpl";

    @Override
    public void setInfoForSDK(Context context) {
        try {
            DeviceInfoUtil.setDeviceIdAndCarType(getDeviceId(), getCarType());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public String getDeviceId(Object... args) {
        try {
            Class clazz = Class.forName("com.hsae.autosdk.settings.AutoSettings");
            Method methodInstance = clazz.getDeclaredMethod("getInstance");
            Object obj = methodInstance.invoke(clazz);
            Method methodDeviceEx = clazz.getDeclaredMethod("getDeviceIdEx");
            String deviceId = (String) methodDeviceEx.invoke(obj);
            return deviceId;
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public String getCarType(Object... args) {
        String productType = "";
        int type = -1;
        try {
            type = AutoSettings.getInstance().getProductType();
        } catch (Throwable t) {
            t.printStackTrace();
        }
        Log.i(TAG, "getProductType productType: " + type);
        switch (type) {
            case 0x00:
                productType = "VHD60A";
                break;
            case 0x01:
                productType = "VHD60B";
                break;
            case 0x02:
                productType = "VHD60C";
                break;
            default:
                Log.i(TAG, "unknow product type");
                break;
        }
        return productType;
    }
}
