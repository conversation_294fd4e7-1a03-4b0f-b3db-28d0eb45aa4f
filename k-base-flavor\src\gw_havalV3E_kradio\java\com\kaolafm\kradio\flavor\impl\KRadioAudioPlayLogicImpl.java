package com.kaolafm.kradio.flavor.impl;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.kaolafm.kradio.common.SystemBootUtil;
import com.kaolafm.kradio.flavor.customer.utils.TbAptivSrcManager;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioPlayLogicInter;
import com.kaolafm.kradio.lib.utils.IntentUtils;
import com.kaolafm.opensdk.player.logic.PlayerManager;

import static com.kaolafm.kradio.lib.utils.Constants.INVALID_NUM;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-07-03 16:38
 ******************************************/
@SuppressLint("LongLogTag")
public class KRadioAudioPlayLogicImpl implements KRadioAudioPlayLogicInter {
    private static final String TAG = "KRadioAudioPlayLogicImpl";

    public KRadioAudioPlayLogicImpl() {
//        PlayerManager.getInstance().setOnPlayLogicListener(() -> {
//            int currentFocus = PlayerManager.getInstance().getCurrentAudioFocusStatus();
//            Log.i(TAG, "isAppOnForeground--------->currentFocus = " + currentFocus);
//            if ((currentFocus < 0)) {
//                boolean isAppOnForeground = IntentUtils.isAppOnForeground();
//                Log.i(TAG, "isAppOnForeground--------->isAppOnForeground = " + isAppOnForeground);
//                return !isAppOnForeground;
//            }
//            return false;
//        });
    }

    @Override
    public boolean autoPlayAudio(Object... args) {
        Context context = (Context) args[0];
        SystemBootUtil systemBootUtil = new SystemBootUtil();
        boolean flag = systemBootUtil.isFirstBoot(context);
        Log.i(TAG, "autoPlayAudio:   flag = " + flag);
        if (flag) {
            PlayerManager.getInstance().play();
            systemBootUtil.updateFirstBoot(context, false);
            return true;
        }
        return false;
    }

    @Override
    public boolean requestAudioFocus(Object... args) {
        PlayerManager audioStatusManager = PlayerManager.getInstance();
        return audioStatusManager.requestAudioFocus();
    }

    @Override
    public boolean resumeAudioPlayLogic(Object... args) {
        Activity activity = (Activity) args[0];
        boolean flag = true;
        int from = -1;
        Intent intent = activity.getIntent();
        Log.i(TAG, "resumeAudioPlayLogic intent = " + intent);
        if (intent != null) {
            // from = 1 来自mode切源
            from = intent.getIntExtra("from", INVALID_NUM);
            Log.i(TAG, "resumeAudioPlayLogic from = " + from);
            flag = from != 1;
        }
        recoverPlay(flag);
        return true;
    }

    private void recoverPlay(boolean canCheckPausedFromUser) {
        PlayerManager klAutoPlayerManager = PlayerManager.getInstance();
        Log.i(TAG, "recoverPlay---------->audioStatusManager.isPausedFromUser() = " + klAutoPlayerManager.isPauseFromUser()
                + " audioStatusManager.getCurrentFocusChange() = " + klAutoPlayerManager.getCurrentAudioFocusStatus());
        if (canCheckPausedFromUser && klAutoPlayerManager.isPauseFromUser()) {
            requestAudioFocus();
            return;
        }
        requestAudioFocus();
        if (!klAutoPlayerManager.isPlaying()) {
            klAutoPlayerManager.switchPlayerStatus(false);
        }
    }

    @Override
    public boolean restartAudioPlayLogic(Object... args) {
        int currentFocus = PlayerManager.getInstance().getCurrentAudioFocusStatus();
        if (currentFocus > 0) {
            TbAptivSrcManager.getInstance().setSrcEnable();
        } else {
            TbAptivSrcManager.getInstance().setSrcConnected();
        }
        Log.i(TAG, "restartAudioPlayLogic start currentFocus = " + currentFocus);
        return true;
    }

//    @Override
//    public boolean stopAudioPlayLogic(Object... args) {
//        int currentFocus = PlayerManager.getInstance().getCurrentAudioFocusStatus();
//        if (currentFocus > 0) {
//            TbAptivSrcManager.getInstance().setSrcEnable();
//        } else {
//            TbAptivSrcManager.getInstance().setSrcConnected();
//        }
//        Log.i(TAG, "stopAudioPlayLogic start currentFocus = " + currentFocus);
//        return true;
//    }

    @Override
    public boolean doStartInPlay(Object... args) {
        return false;
    }
}
