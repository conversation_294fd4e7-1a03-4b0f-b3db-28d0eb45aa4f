package com.kaolafm.kradio.lib.bean;

import androidx.annotation.NonNull;

import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

/**
 * @ClassName UploadHistoryResultBean
 * @Description 历史上传 - 数据bean
 * <AUTHOR>
 * @Date 2020-02-10
 * @Version 1.0
 */

public class ListenHistoryDataBean {

    /**
     * 专辑id，电台id，直播id统称
     */
    private long pareContentId;
    /**
     * 碎片id，直播节目id统称
     */
    private long contentId;
    /**
     * 0专辑 1碎片 3电台 4一键收听 5直播 11广播 12电视
     */
    private int type;
    /**
     * 上次播放位置
     */
    private long playedTime;
    /**
     * 播放时长
     */
    private long duration;
    /**
     * 产生历史记录的时间戳
     */
    private long timeStamp;

    public long getPareContentId() {
        return pareContentId;
    }

    public void setPareContentId(long pareContentId) {
        this.pareContentId = pareContentId;
    }

    public long getContentId() {
        return contentId;
    }

    public void setContentId(long contentId) {
        this.contentId = contentId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public long getPlayedTime() {
        return playedTime;
    }

    public void setPlayedTime(long playedTime) {
        this.playedTime = playedTime;
    }

    public long getDuration() {
        return duration;
    }

    public void setDuration(long duration) {
        this.duration = duration;
    }

    public long getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(long timeStamp) {
        this.timeStamp = timeStamp;
    }

    @NonNull
    @Override
    public String toString() {
        return "所属 id = " + pareContentId +
                ", 碎片 id = " + contentId +
                ", 类型 = " + getType(type) +
                ", 播放位置 = " + playedTime +
                ", 播放时长 = " + duration +
                ", 时间戳 = " + timeStamp;
    }

    private String getType(int type) {
        String typeStr = Constants.BLANK_STR;
        switch (type) {
            case PlayerConstants.RESOURCES_TYPE_ALBUM:
                typeStr = "专辑类型";
                break;
            case PlayerConstants.RESOURCES_TYPE_AUDIO:
                typeStr = "碎片类型";
                break;
            case PlayerConstants.RESOURCES_TYPE_RADIO:
                typeStr = "电台类型";
                break;
            case PlayerConstants.RESOURCES_TYPE_LIVING:
                typeStr = "直播类型";
                break;
            case PlayerConstants.RESOURCES_TYPE_ONE_KEY_LISTENER_TYPE:
                typeStr = "一键收听类型";
                break;
            case PlayerConstants.RESOURCES_TYPE_BROADCAST:
                typeStr = "广播类型";
                break;
            case PlayerConstants.RESOURCES_TYPE_TEMP_TASK:
                typeStr = "临时任务类型";
                break;
            case PlayerConstants.RESOURCES_TYPE_LIVE_STREAM:
                typeStr = "直播入流类型";
                break;
            default:
                typeStr = "其他类型";
                break;
        }
        return typeStr;
    }


}
