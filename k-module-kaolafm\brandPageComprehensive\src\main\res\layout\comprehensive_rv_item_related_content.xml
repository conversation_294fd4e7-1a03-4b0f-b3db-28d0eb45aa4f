<?xml version="1.0" encoding="utf-8"?>
<com.kaolafm.kradio.component.ui.base.view.ScaleConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/m160"
    android:background="@drawable/comprehensive_item_related_content"
    android:clickable="true"
    android:focusable="true">

    <com.kaolafm.kradio.component.ui.base.view.RoundCircleImageView
        android:id="@+id/coverPic"
        android:layout_width="@dimen/m120"
        android:layout_height="@dimen/m120"
        android:layout_marginEnd="@dimen/m20"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:round_radius="@dimen/m9" />

    <com.kaolafm.kradio.component.ui.base.view.KradioTextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/m20"
        android:layout_marginTop="@dimen/m58"
        android:layout_marginEnd="@dimen/m34"
        android:layout_marginBottom="@dimen/m30"
        android:ellipsize="end"
        android:lineSpacingExtra="@dimen/m6"
        android:maxLines="2"
        android:text="TextView"
        android:textColor="@color/text_color_white"
        android:textSize="@dimen/m26"
        app:kt_font_weight="0.3"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toStartOf="@+id/coverPic"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="标题标题标题标题标题标标题标题题标题标题标题标题标题标题标题标题标题标题标题标题标题标题标题标题标题标题标题标题标题标题标题标题标题标题标题" />

    <TextView
        android:id="@+id/tagView"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/m42"
        android:background="@drawable/comprehensive_topic_related_content_tag_bg"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/m20"
        android:paddingEnd="@dimen/m20"
        android:textColor="@color/comprehensive_topic_related_content_tag_text_color"
        android:textSize="@dimen/m20"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="有奖互动" />
</com.kaolafm.kradio.component.ui.base.view.ScaleConstraintLayout>