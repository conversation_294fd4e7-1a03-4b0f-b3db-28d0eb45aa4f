package com.kaolafm.kradio.lib.bean;


import com.kaolafm.opensdk.player.logic.model.item.BroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.InfoData;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

/******************************************
 * 类描述: 此类主要目的是为满足产品点击某一个广播要关联若干个广播对象播放需求
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-05-20 09:57
 ******************************************/
public final class BroadcastRadioSimpleData {
    /**
     * 电台Id
     */
    private long broadcastId;
    /**
     * 电台名称
     */
    private String name;
    /**
     * 封面url
     */
    private String img;
    /**
     * 资源类型 默认是广播，还会有听电视
     */
    private int resType = 11;

    public int getResType() {
        return resType;
    }

    public void setResType(int resType) {
        this.resType = resType;
    }

    public long getBroadcastId() {
        return broadcastId;
    }

    public void setBroadcastId(long broadcastId) {
        this.broadcastId = broadcastId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public PlayItem transToPlayItem() {
        BroadcastPlayItem playItem = new BroadcastPlayItem();
        InfoData infoData = playItem.getInfoData();
        infoData.setAlbumId(broadcastId);
        playItem.setAudioId(broadcastId);
        infoData.setTitle(name);
        infoData.setAlbumName(name);
        infoData.setAlbumPic(img);
        playItem.setStatus(PlayerConstants.BROADCAST_STATUS_LIVING);
        return playItem;
    }

}
