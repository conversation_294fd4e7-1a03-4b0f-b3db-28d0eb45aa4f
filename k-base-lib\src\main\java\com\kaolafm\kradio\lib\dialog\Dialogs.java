package com.kaolafm.kradio.lib.dialog;

import androidx.annotation.IntDef;
import androidx.fragment.app.DialogFragment;
import androidx.recyclerview.widget.RecyclerView;
import android.view.Gravity;

import com.kaolafm.kradio.lib.base.flavor.DialogCreator;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.ClazzUtil;

/**
 * <AUTHOR>
 **/
public final class Dialogs {

    public static final int TYPE_2BTN = 0;
    public static final int TYPE_LIST = 1;
    public static final int TYPE_QR = 2;
    public static final int TYPE_TITLE_LIST_2BTN = 3;
    public static final int TYPE_2BTN_WITH_TITLE = 4;
    public static final int TYPE_2BTN_PERMISSION = 5;

    @IntDef({TYPE_2BTN, TYPE_LIST, TYPE_QR, TYPE_TITLE_LIST_2BTN, TYPE_2BTN_WITH_TITLE,TYPE_2BTN_PERMISSION})
    public @interface Type {
    }


    public static class Builder {
        private static DialogCreator sMDialogCreator;
        private int gravity = Gravity.BOTTOM;
        private @Type
        int type = TYPE_2BTN;
        int background = -1;
        private boolean outCancel = true;
        private CharSequence title;
        private CharSequence message;
        private RecyclerView.Adapter adapter;
        private CharSequence leftBtnText;
        private CharSequence rightBtnText;
        private DialogListener.OnNativeListener<DialogFragment> onNativeListener;
        private DialogListener.OnPositiveListener<DialogFragment> onPositiveListener;
        private DialogListener.OnClickListener onOutsideClickListener;
        private boolean canShowButton = true;
        private boolean canShowCheckBox = false;

        public boolean isOutCancel() {
            return outCancel;
        }

        public boolean isCanShowCheckBox() {
            return canShowCheckBox;
        }

        public Builder setCanShowCheckBox(boolean canShowCheckBox) {
            this.canShowCheckBox = canShowCheckBox;
            return this;
        }

        public Builder setOutCancel(boolean outCancel) {
            this.outCancel = outCancel;
            return this;
        }

        public Builder setBackground(@Type int background) {
            this.background = background;
            return this;
        }

        public Builder setType(@Type int type) {
            this.type = type;
            return this;
        }

        public Builder setGravity(int gravity) {
            this.gravity = gravity;
            return this;
        }

        public Builder setTitle(CharSequence title) {
            this.title = title;
            return this;
        }

        public Builder setMessage(CharSequence message) {
            this.message = message;
            return this;
        }

        public Builder setLeftBtnText(CharSequence leftBtnText) {
            this.leftBtnText = leftBtnText;
            return this;
        }

        public Builder setRightBtnText(CharSequence rightBtnText) {
            this.rightBtnText = rightBtnText;
            return this;
        }

        public Builder setOnNativeListener(DialogListener.OnNativeListener<DialogFragment> onNativeListener) {
            this.onNativeListener = onNativeListener;
            return this;
        }

        public Builder setOnPositiveListener(DialogListener.OnPositiveListener<DialogFragment> onPositiveListener) {
            this.onPositiveListener = onPositiveListener;
            return this;
        }


        public Builder setOnOutsideClickListener(DialogListener.OnClickListener l) {
            this.onOutsideClickListener = l;
            return this;
        }

        public Builder setAdapter(RecyclerView.Adapter adapter) {
            this.adapter = adapter;
            return this;
        }

        public int getGravity() {
            return gravity;
        }

        public int getType() {
            return type;
        }

        public int getBackground() {
            return background;
        }

        public CharSequence getTitle() {
            return title;
        }

        public CharSequence getMessage() {
            return message;
        }

        public RecyclerView.Adapter getAdapter() {
            return adapter;
        }

        public CharSequence getLeftBtnText() {
            return leftBtnText;
        }

        public CharSequence getRightBtnText() {
            return rightBtnText;
        }

        public boolean isCanShowButton() {
            return canShowButton;
        }

        public Builder setCanShowButton(boolean canShowButton) {
            this.canShowButton = canShowButton;
            return this;
        }

        public DialogListener.OnNativeListener<DialogFragment> getOnNativeListener() {
            return onNativeListener;
        }

        public DialogListener.OnPositiveListener<DialogFragment> getOnPositiveListener() {
            return onPositiveListener;
        }

        public DialogListener.OnClickListener getOnOutsideClickListener() {
            return onOutsideClickListener;
        }

        public DialogFragment create() {
            if (sMDialogCreator == null) {
                sMDialogCreator = ClazzImplUtil.getInter("DialogFactory");
                if (sMDialogCreator == null) {
                    sMDialogCreator = ClazzUtil.getClazzInstance("com.kaolafm.kradio.home.comprehensive.ui.view.DialogFactory");
                    if (sMDialogCreator == null) {
                        sMDialogCreator = ClazzUtil.getClazzInstance("com.kaolafm.kradio.online.common.view.DialogFactory");
                    }
                }
            }
            return sMDialogCreator.create(this);
        }
    }


}
