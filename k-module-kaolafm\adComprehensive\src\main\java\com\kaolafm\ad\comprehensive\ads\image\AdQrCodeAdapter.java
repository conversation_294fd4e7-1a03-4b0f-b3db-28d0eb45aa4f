package com.kaolafm.ad.comprehensive.ads.image;

import android.content.Context;

import com.kaolafm.ad.api.model.InteractionAdvert;
import com.kaolafm.ad.comprehensive.ads.image.base.BaseAdImageAdapter;
import com.kaolafm.ad.comprehensive.ads.image.base.BaseAdContentView;

public class AdQrCodeAdapter extends BaseAdImageAdapter<InteractionAdvert> {
    @Override
    public BaseAdContentView<InteractionAdvert> onCreateAdView(Context context) {
        return new AdQrCodeView(context);
    }

    @Override
    public void onBindAdView(BaseAdContentView<InteractionAdvert> baseAdContentView, InteractionAdvert interactionAdvert) {
        baseAdContentView.loadAdContent(interactionAdvert);
    }
}
