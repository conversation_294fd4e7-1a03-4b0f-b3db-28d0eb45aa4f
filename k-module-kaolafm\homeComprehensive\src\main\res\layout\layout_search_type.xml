<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center_horizontal"
    android:background="@drawable/bg_search_popwindow">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_search_type"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/search_type_spinner_recycleview_top"
        android:layout_marginBottom="@dimen/search_type_spinner_recycleview_bottom"
        android:paddingStart="@dimen/search_type_spinner_recycleview_left"
        android:paddingEnd="@dimen/search_type_spinner_recycleview_right"
        app:layout_constrainedWidth="true"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        android:id="@+id/search_type_loading"
        layout="@layout/refresh_bottom"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ViewStub
        android:id="@+id/vs_search_type_network_error"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout="@layout/home_no_network_rl" />

</androidx.constraintlayout.widget.ConstraintLayout>