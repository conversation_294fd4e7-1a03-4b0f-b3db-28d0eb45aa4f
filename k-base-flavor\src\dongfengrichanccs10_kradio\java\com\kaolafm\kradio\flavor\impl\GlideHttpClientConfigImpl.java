package com.kaolafm.kradio.flavor.impl;

import com.kaolafm.kradio.lib.base.flavor.GlideHttpClientConfig;
import okhttp3.Dispatcher;
import okhttp3.OkHttpClient.Builder;

/**
 * <AUTHOR>
 * @date 2019-11-14
 */
public class GlideHttpClientConfigImpl implements GlideHttpClientConfig {

    @Override
    public Builder build(Builder builder) {
        Dispatcher dispatcher = new Dispatcher();
        dispatcher.setMaxRequests(1);
        builder.dispatcher(dispatcher);
        return builder;
    }
}
