package com.kaolafm.ad.comprehensive.ads.image.base;

import android.content.Context;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.ad.comprehensive.ads.image.AdContentInfo;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.AnimUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;

import java.io.File;
import java.lang.ref.WeakReference;

/**
 * 顶部显示的图片广告基类
 *
 * <AUTHOR>
 * @date 2020-03-20
 */
public abstract class BaseAdImageView extends BaseAdWithDurationContentView<AdContentInfo> {

    protected ImageView mIvAd;

    protected TextView mTvSkip;

    public BaseAdImageView(Context context) {
        this(context, null);
    }

    public BaseAdImageView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public BaseAdImageView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        LayoutInflater.from(context).inflate(R.layout.ad_image_view_layout, this, true);
        mIvAd = findViewById(R.id.iv_ad);
        mTvSkip = findViewById(R.id.tv_ad_skip);
        this.setVisibility(INVISIBLE);
    }

    @Override
    public void loadAdContent(AdContentInfo adContentInfo) {
        mDuration = adContentInfo.getImageDuration();
        String path = adContentInfo.getLocalPath();
        if (adContentInfo.isPreload()) {
            File file = null;
            try {
                file = new File(path);
            } catch (Exception e) {

            }
            if (file == null || !file.exists()) {
                mAdImageListener.onAdImageLoadFailed();
                return;
            }
            displayLocalImage(mIvAd, adContentInfo.getLocalPath(), ResUtil.getDimen(R.dimen.m20));
            countdownToCloseAd();
            show();
            mAdImageListener.onAdImageLoaded(this);
            return;
        }
        displayImage(mIvAd, adContentInfo.getImageUrl(), ResUtil.getDimen(R.dimen.m20), getListener());
    }

    protected AdImageLoaderListener getListener() {
        return new AdImageLoaderListener(this);
    }

    @Override
    public void show() {
        super.show();
        AnimUtil.startTranslateY(this, -getLayoutParams().height, 0);
    }

    public class AdImageLoaderListener extends AdOnImageLoaderListener {

        private WeakReference<BaseAdImageView> mView;

        public AdImageLoaderListener(BaseAdImageView view) {
            mView = new WeakReference<>(view);
        }

        @Override
        public void onLoadingComplete(String url, ImageView target) {
            BaseAdImageView view = mView.get();
            if (view != null) {
                view.countdownToCloseAd();
                view.show();
            }
            super.onLoadingComplete(url, target);
        }
    }
}
