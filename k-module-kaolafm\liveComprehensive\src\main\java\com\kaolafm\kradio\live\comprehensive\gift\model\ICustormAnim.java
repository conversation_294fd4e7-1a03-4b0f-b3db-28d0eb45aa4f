package com.kaolafm.kradio.live.comprehensive.gift.model;

import android.animation.AnimatorSet;
import android.view.View;

import com.kaolafm.kradio.live.comprehensive.gift.ui.GiftFrameLayout;

/**
 * Created by Ren on 2023/2/9.
 */
public interface ICustormAnim {
    AnimatorSet startAnim(GiftFrameLayout giftFrameLayout, View rootView);
    AnimatorSet comboAnim(GiftFrameLayout giftFrameLayout, View rootView, boolean isFirst);
    AnimatorSet endAnim(GiftFrameLayout giftFrameLayout, View rootView);
}
