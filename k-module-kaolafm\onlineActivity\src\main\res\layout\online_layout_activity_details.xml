<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/message_details_bg">

    <ImageView
        android:id="@+id/bubbleBg"
        android:layout_width="@dimen/m1000"
        android:layout_height="@dimen/m365"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/y124"
        android:layout_marginBottom="@dimen/y50"
        android:scaleType="centerCrop"
        android:src="@drawable/online_activity_test_bg" />

    <LinearLayout
        android:id="@+id/content_ll"
        android:layout_width="@dimen/m1000"
        android:layout_height="@dimen/m365"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/y114"
        android:orientation="horizontal">

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/bubbleIcon"
                android:layout_width="@dimen/m134"
                android:layout_height="@dimen/m134"
                android:layout_marginStart="@dimen/x152"
                android:layout_marginTop="@dimen/m22"
                android:scaleType="centerInside"
                tools:src="@drawable/online_message_yellow_icon" />

            <RelativeLayout
                android:id="@+id/activity_video_rl"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/x90"
                android:layout_marginTop="@dimen/y22"
                android:background="@drawable/video_pic_bg"
                android:visibility="gone"
                tools:visibility="visible">

                <ImageView
                    android:id="@+id/viedo_iv"
                    android:layout_width="@dimen/m214"
                    android:layout_height="@dimen/m314"
                    android:padding="@dimen/m80"
                    android:scaleType="centerInside" />

                <ImageView
                    android:id="@+id/viedo_iv2"
                    android:layout_width="@dimen/m238"
                    android:layout_height="@dimen/m318"
                    android:padding="@dimen/m80"
                    android:scaleType="centerInside"
                    android:src="@drawable/video_icon" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/bubble_pic_rl"
                android:layout_width="@dimen/m285"
                android:layout_height="@dimen/m160"
                android:layout_alignParentBottom="true"
                android:layout_marginLeft="@dimen/x22"
                android:layout_marginBottom="@dimen/y37"
                android:background="@drawable/message_qr_bg"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="visible">

                <com.kaolafm.kradio.component.ui.base.view.OvalImageView
                    android:id="@+id/bubble_pic_iv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:layout_margin="@dimen/m4"
                    app:oval_radius="@dimen/m18"
                    tools:src="@drawable/online_message_yellow_icon" />

                <ImageView
                    android:id="@+id/bubble_pic_detils_iv"
                    android:layout_width="@dimen/x48"
                    android:layout_height="@dimen/y48"
                    android:layout_alignParentRight="true"
                    android:layout_alignParentBottom="true"
                    android:layout_marginRight="@dimen/m8"
                    android:layout_marginBottom="@dimen/m8"
                    android:scaleType="centerInside"
                    android:src="@drawable/online_msg_pic_icon" />
            </RelativeLayout>
        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical">


            <TextView
                android:maxLines="1"
                android:ellipsize="end"
                android:id="@+id/bubbleTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/x30"
                android:layout_marginTop="@dimen/y51"
                android:textColor="@color/online_activity_dateils_title_color"
                android:textSize="@dimen/m36"
                android:textStyle="bold"
                tools:text="路况交通" />

            <TextView
                android:id="@+id/bubbleSubTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/x30"
                android:layout_marginTop="@dimen/y8"
                android:layout_marginEnd="@dimen/x63"
                android:lineSpacingMultiplier="1.3"
                android:textColor="@color/colorWhite"
                android:textSize="@dimen/m24"
                android:textStyle="bold"
                app:layout_constraintStart_toEndOf="@id/bubbleIcon"
                app:layout_constraintTop_toBottomOf="@id/bubbleTitle"
                tools:text="2022.05.31 14:08" />

            <TextView
                android:id="@+id/bubbleContent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/x30"
                android:layout_marginTop="@dimen/y12"
                android:maxLines="3"
                android:paddingRight="@dimen/m76"
                android:textColor="@color/online_activity_dateils_content_color"
                android:textSize="@dimen/m20"
                app:layout_constraintEnd_toEndOf="@+id/bubbleBg"
                app:layout_constraintStart_toStartOf="@+id/bubbleSubTitle"
                app:layout_constraintTop_toBottomOf="@id/bubbleSubTitle"
                tools:text="前方3公里正在进行道路施工车辆行驶缓慢，请谨慎驾驶谨慎驾驶谨慎驾驶重要事情说请谨慎驾驶谨慎驾驶谨慎驾驶重要事情说" />

            <LinearLayout
                android:id="@+id/bubbleButtonParent"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/x17"
                android:layout_marginEnd="@dimen/x80"
                android:layout_marginBottom="@dimen/y26"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                app:layout_constraintBottom_toBottomOf="@id/bubbleBg"
                app:layout_constraintEnd_toEndOf="@id/bubbleBg"
                app:layout_constraintStart_toStartOf="@id/bubbleBg">

            </LinearLayout>
        </LinearLayout>

    </LinearLayout>

    <ImageView
        android:id="@+id/play_iv"
        android:layout_width="@dimen/m40"
        android:layout_height="@dimen/m40"
        android:layout_alignLeft="@+id/content_ll"
        android:layout_alignTop="@+id/content_ll"
        android:layout_marginTop="@dimen/y40"
        android:layout_marginLeft="@dimen/x30"
        android:fitsSystemWindows="true"
        android:scaleType="centerInside"
        android:src="@drawable/online_msg_play_icon" />

    <RelativeLayout
        android:id="@+id/video_view_rl"
        android:layout_width="@dimen/x910"
        android:layout_height="@dimen/y512"
        android:layout_centerInParent="true"
        android:background="@color/colorBlack"
        android:visibility="gone">

        <VideoView

            android:id="@+id/video_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignParentLeft="true"
            android:layout_alignParentTop="true"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true" />

        <ImageView
            android:id="@+id/viedo_replay"
            android:layout_width="@dimen/m100"
            android:layout_height="@dimen/m100"
            android:layout_centerInParent="true"
            android:scaleType="centerInside"
            android:src="@drawable/video_icon"
            android:visibility="gone" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/pic_big_rl"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/message_details_bg"
        android:visibility="gone">

        <com.davemorrissey.labs.subscaleview.SubsamplingScaleImageView
            android:id="@+id/pic_big_iv"
            android:layout_width="@dimen/m910"
            android:layout_height="@dimen/m512"
            android:layout_centerInParent="true" />
    </RelativeLayout>
    <include
        android:id="@+id/loading"
        layout="@layout/online_refresh_center"
        android:layout_width="@dimen/m1000"
        android:layout_height="@dimen/m365"
        android:visibility="gone"
        android:layout_centerInParent="true"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</RelativeLayout>