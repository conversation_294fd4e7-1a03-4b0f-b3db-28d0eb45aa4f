package com.kaolafm.kradio.lib.utils;

import android.app.Activity;
import android.content.Context;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.graphics.Color;
import android.os.Build;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Display;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import com.kaolafm.kradio.lib.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.AppManager;

import java.lang.reflect.Method;

/**
 * 获取屏幕相关工具类
 * Created by donald on 2017/10/17.
 */

public class ScreenUtil {



    private ScreenUtil() {
    }

    /**
     * 获取当前Activity的Context，如果没有则使用Application Context
     */
    private static Context getCurrentContext() {
        Context activityContext = AppManager.getInstance().getCurrentActivity();
        if (activityContext != null) {
            return activityContext;
        }
        return AppDelegate.getInstance().getContext().getApplicationContext();
    }

    /**
     * 获取WindowManager，支持多屏适配
     * 优先使用Activity的WindowManager确保获取当前屏幕信息
     */
    private static WindowManager getWindowManagerForMultiDisplay(Context context) {
        try {
            if (context instanceof Activity) {
                // Activity.getWindowManager()返回当前Activity所在屏幕的WindowManager
                WindowManager wm = ((Activity) context).getWindowManager();
                Log.d("ScreenUtil", "使用Activity.getWindowManager(), activity=" + context.getClass().getSimpleName());
                return wm;
            } else {
                // 非Activity Context使用系统服务
                WindowManager wm = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
                Log.d("ScreenUtil", "使用Context.getSystemService(), context=" + context.getClass().getSimpleName());
                return wm;
            }
        } catch (Exception e) {
            Log.w("ScreenUtil", "获取WindowManager失败，使用fallback方案", e);
            return (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        }
    }



    /**
     * 返回当前屏幕是否为竖屏。
     *
     * @return 当且仅当当前屏幕为竖屏时返回true, 否则返回false。
     */
    public static boolean isPortrait() {
        return getCurrentContext().getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT;
    }

    /**
     * 获取状态栏高度
     */
    public static int getStatusBarHeight() {
        Context context = getCurrentContext();
        int result = 0;
        int resourceId = context.getResources().getIdentifier("status_bar_height", "dimen", "android");
        if (resourceId > 0) {
            result = context.getResources().getDimensionPixelSize(resourceId);
        }
        return result;
    }

    /**
     * 获取屏幕宽度 px
     * 支持多屏适配：优先使用Activity的WindowManager获取当前屏幕信息
     */
    public static int getScreenWidth() {
        Context context = getCurrentContext();
        WindowManager wm = getWindowManagerForMultiDisplay(context);
        DisplayMetrics displayMetrics = new DisplayMetrics();
        wm.getDefaultDisplay().getRealMetrics(displayMetrics);

        // 添加调试日志
        try {
            Display display = wm.getDefaultDisplay();
            Log.d("ScreenUtil", String.format("getScreenWidth: %d, displayId=%d, context=%s",
                  displayMetrics.widthPixels, display.getDisplayId(), context.getClass().getSimpleName()));
        } catch (Exception e) {
            Log.w("ScreenUtil", "记录屏幕宽度日志失败", e);
        }

        return displayMetrics.widthPixels;
    }

    /**
     * 获取屏幕高度
     * 支持多屏适配：优先使用Activity的WindowManager获取当前屏幕信息
     */
    public static int getScreenHeight() {
        // 解决横竖屏切换时，偶现的 获取屏幕高度 错乱问题,在横版情况下，高度低于宽度,返回 宽高度中最小值
        Context context = getCurrentContext();
        WindowManager wm = getWindowManagerForMultiDisplay(context);
        DisplayMetrics displayMetrics = new DisplayMetrics();
        wm.getDefaultDisplay().getRealMetrics(displayMetrics);

        // 添加调试日志
        try {
            Display display = wm.getDefaultDisplay();
            Log.d("ScreenUtil", String.format("getScreenHeight: %d, displayId=%d, context=%s",
                  displayMetrics.heightPixels, display.getDisplayId(), context.getClass().getSimpleName()));
        } catch (Exception e) {
            Log.w("ScreenUtil", "记录屏幕高度日志失败", e);
        }

        // 添加log,查看一个 偶现问题。
        // Log.i("heightPixels", " " + displayMetrics.heightPixels);
//        if (displayMetrics.heightPixels >= displayMetrics.widthPixels) {
//            return displayMetrics.widthPixels;
//        } else {
//            return displayMetrics.heightPixels;
//        }
        return displayMetrics.heightPixels;
    }

    public static String getScreeSize() {
        return getScreenWidth() + "*" + getScreenHeight();
    }

    /**
     * 获取虚拟按键的高度
     */
    public static int getNavigationBarHeight() {
        if (!checkDeviceHasNavigationBar()) {
            return 0;
        }
        int navigationBarHeight = 0;
        Context context = getCurrentContext();
        Resources resources = context.getResources();
        int resourceId = resources.getIdentifier("navigation_bar_height", "dimen", "android");
        if (resourceId > 0) {
            navigationBarHeight = resources.getDimensionPixelSize(resourceId);
        }
        return navigationBarHeight;
    }

    /**
     * 检查是否有虚拟按键
     */
    public static boolean checkDeviceHasNavigationBar() {
        if (Build.VERSION.SDK_INT < 21) {
            return false;
        }
        boolean hasNavigationBar = false;
        Context context = getCurrentContext();
        Resources resources = context.getResources();
        int id = resources.getIdentifier("config_showNavigationBar", "bool", "android");
        if (id > 0) {
            hasNavigationBar = resources.getBoolean(id);
        }
        //检查虚拟按键是否被重写
        try {
            Class systemProperties = Class.forName("android.os.SystemProperties");
            Method getMethod = systemProperties.getMethod("get", String.class);
            String navBarOverride = (String) getMethod.invoke(systemProperties, "qemu.hw.mainkeys");
            if (TextUtils.equals("1", navBarOverride)) {
                hasNavigationBar = false;
            } else if (TextUtils.equals("0", navBarOverride)) {
                hasNavigationBar = true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return hasNavigationBar;
    }

    /**
     * 除去status高度的屏幕高度
     */
    public static int getScreenHeightWithoutStatus() {
        return getScreenHeight() - getStatusBarHeight();
    }


    /**
     * dp 转px
     */
    public static int dp2px(int dp) {
        Context context = getCurrentContext();
        final float scale = context.getResources().getDisplayMetrics().density;
        return (int) (dp * scale + 0.5f);
    }

    public static int px2dp(int px) {
        Context context = getCurrentContext();
        final float scale = context.getResources().getDisplayMetrics().density;
        return (int) (px / scale + 0.5f);
    }

    public static int sp2px(float sp) {
        Context context = getCurrentContext();
        final float scale = context.getResources().getDisplayMetrics().scaledDensity;
        return (int) (sp * scale + 0.5f);
    }

    public static int px2sp(float pxValue) {
        Context context = getCurrentContext();
        final float fontScale = context.getResources().getDisplayMetrics().scaledDensity;
        return (int) (pxValue / fontScale + 0.5f);
    }

    public static void setStatusBar(Window window, boolean navi) {
        setStatusBar(window, navi, true);
    }

    public static void setStatusBar(Window window, boolean navi, boolean isStatusBarTransparent) {
        //api>21,全透明状态栏和导航栏;api>19,半透明状态栏和导航栏
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS
                    | WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION);
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            if (isStatusBarTransparent) {
                window.setStatusBarColor(Color.TRANSPARENT);
            }
            if (navi) {
                window.getDecorView().setSystemUiVisibility(
                        //状态栏不会被隐藏但activity布局会扩展到状态栏所在位置
                        View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                                //导航栏不会被隐藏但activity布局会扩展到导航栏所在位置
                                | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                                | View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
                window.setNavigationBarColor(Color.TRANSPARENT);
            } else {
                window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                        | View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
            }
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            if (navi) {
                //半透明导航栏
                window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION);
            }
            //半透明状态栏
            window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        }
    }


    /**
     * 获取全局页面左边距
     *
     * @param orientation Configuration.ORIENTATION_LANDSCAPE or Configuration.ORIENTATION_PORTRAIT
     * @return
     */
    public static int getGlobalPaddingLeft(int orientation) {
        int padding;
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
//            padding = ResUtil.getDimen(R.dimen.m50) + ResUtil.getDimen(R.dimen.m22);
            padding = ResUtil.getDimen(R.dimen.m70);
        } else {
//            padding = ResUtil.getDimen(R.dimen.m25) + ResUtil.getDimen(R.dimen.m22);
            padding = ResUtil.getDimen(R.dimen.x50);
        }
        return padding;
    }

    /**
     * 获取全局页面右边距
     *
     * @param orientation Configuration.ORIENTATION_LANDSCAPE or Configuration.ORIENTATION_PORTRAIT
     * @return
     */
    public static int getGlobalPaddingRight(int orientation) {
        int padding;
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
//            padding = ResUtil.getDimen(R.dimen.m50) + ResUtil.getDimen(R.dimen.m22);
            padding = ResUtil.getDimen(R.dimen.m80);
        } else {
//            padding = ResUtil.getDimen(R.dimen.m25) + ResUtil.getDimen(R.dimen.m22);
            padding = ResUtil.getDimen(R.dimen.x50);
        }
        return padding;
    }

    /**
     * 获取内容区域相对于全屏的比例
     * <p>
     * <p>或使用常量:com.kaolafm.view.ViewConstants.TITLE_LAND_PERCENT 或 com.kaolafm.view.ViewConstants.TITLE_PORT_PERCENT
     *
     * @param orientation 屏幕方向
     * @return
     */
    public static float getGlobalConstrainPercentWidth(int orientation) {
        float bias = ((float) getScreenWidth() - getGlobalPaddingRight(orientation) - getGlobalPaddingLeft(orientation)) / getScreenWidth();
        return bias;
    }

    /**
     * 计算返回键控件的左边距.箭头中线与内容左边距对齐
     *
     * @param backView    返回键控件
     * @param orientation 屏幕方向
     * @return
     */
    public static int getGlobalBackMarginLeft(View backView, int orientation) {
        //计算箭头的尖儿,距离bbfBack左边的距离
        int width = backView.getWidth();
        if (width == 0) {
            width = ResUtil.getDimen(R.dimen.m80);
        }
        int srcWidht = width - backView.getPaddingLeft() - backView.getPaddingRight();
        double srcRectW = srcWidht / 2 / 1.414;
        double arrawLeft = width / 2;// width / 2 - srcRectW;

        int leftMargin = (int) (getGlobalPaddingLeft(orientation));
//        int paddingRight = ScreenUtil.getGlobalPaddingRight(orientation);
        return leftMargin;
    }

    /**
     * 全局通知栏宽度
     *
     * @return
     */
    public static int getGlobalNotifyWindowWidth(int orientation) {
        return getScreenWidth() - getGlobalPaddingRight(orientation) - getGlobalPaddingLeft(orientation);
    }
}
