package com.kaolafm.kradio.online.common.view;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.Bitmap.Config;
import android.renderscript.Allocation;
import android.renderscript.Element;
import android.renderscript.RenderScript;
import android.renderscript.ScriptIntrinsicBlur;
import androidx.annotation.NonNull;
import android.util.AttributeSet;
import android.view.View;
import java.util.HashMap;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public  class DoubleLayerView extends View {
    private Bitmap mSrcBitmap;
    private Bitmap mDestBitmap;
    private Paint mPaint;
    private Paint mBitmapPaint;
    private int mDestOffsetX;
    private int mDestOffsetY;
    private int mSrcOffsetX;
    private int mSrcOffsetY;
    private float mDestAlpha;
    private float mSrcAlpha;
    public static final float ALPHA_NO_SET = -1.0F;
    @NotNull
    public static final DoubleLayerView.Companion Companion = new DoubleLayerView.Companion();
    private HashMap _$_findViewCache;

    protected void onDraw(@NotNull Canvas canvas) {
        Intrinsics.checkParameterIsNotNull(canvas, "canvas");
        super.onDraw(canvas);
        if (this.mDestBitmap != null) {
            Bitmap var10001 = this.mDestBitmap;
            if (var10001 == null) {
                Intrinsics.throwNpe();
            }

            Paint var10004 = this.mPaint;
            if (var10004 == null) {
                Intrinsics.throwUninitializedPropertyAccessException("mPaint");
            }

            canvas.drawBitmap(var10001, 0.0F, 0.0F, var10004);
            if (this.mSrcBitmap != null) {
                var10001 = this.mSrcBitmap;
                if (var10001 == null) {
                    Intrinsics.throwNpe();
                }

                var10004 = this.mPaint;
                if (var10004 == null) {
                    Intrinsics.throwUninitializedPropertyAccessException("mPaint");
                }

                canvas.drawBitmap(var10001, 0.0F, 0.0F, var10004);
            }

        }
    }

    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        if (w != oldw || h != oldh) {
            if (this.mDestBitmap != null) {
                this.setDestBitmap(this.mDestBitmap, this.mDestOffsetX, this.mDestOffsetY, this.mDestAlpha);
            }

            if (this.mSrcBitmap != null) {
                try {
                    this.setSrcBitmap(this.mSrcBitmap, this.mSrcOffsetX, this.mSrcOffsetY, this.mSrcAlpha);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                }
            }

        }
    }

    public void setDestBitmap(@NonNull @Nullable Bitmap destBitmap, int offsetX, int offsetY, float alpha) {
        this.mDestOffsetX = offsetX;
        this.mDestOffsetY = offsetY;
        this.mDestAlpha = alpha;
        if (this.getMeasuredWidth() != 0 && this.getMeasuredHeight() != 0 && destBitmap != null) {
            if (destBitmap.getWidth() == this.getMeasuredWidth() && destBitmap.getHeight() == this.getMeasuredHeight() && offsetX == 0 && offsetY == 0 && alpha == -1.0F) {
                this.mDestBitmap = destBitmap;
                this.invalidate();
            } else {
                int[] size = this.computeNewSize(destBitmap, this.getMeasuredWidth(), this.getMeasuredHeight());
                this.mDestBitmap = this.reSizeBitmap(destBitmap, size[0], size[1], offsetX, offsetY, alpha);
                this.invalidate();
            }
        } else {
            this.mDestBitmap = destBitmap;
            this.invalidate();
        }
    }

    // $FF: synthetic method
    public static void setDestBitmap$default(DoubleLayerView var0, Bitmap var1, int var2, int var3, float var4, int var5, Object var6) {
        if ((var5 & 2) != 0) {
            var2 = 0;
        }

        if ((var5 & 4) != 0) {
            var3 = 0;
        }

        if ((var5 & 8) != 0) {
            var4 = -1.0F;
        }

        var0.setDestBitmap(var1, var2, var3, var4);
    }

    public void setSrcBitmap(@Nullable Bitmap srcBitmap, int offsetX, int offsetY, float alpha) throws Throwable {
        if (!this.checkDestBitmap()) {
            throw (Throwable)(new RuntimeException("The destBitmap parameter is empty, please set it"));
        } else {
            this.mSrcOffsetX = offsetX;
            this.mSrcOffsetY = offsetY;
            this.mSrcAlpha = alpha;
            if (this.getMeasuredWidth() != 0 && this.getMeasuredHeight() != 0 && srcBitmap != null) {
                Bitmap var10000 = this.mDestBitmap;
                if (var10000 == null) {
                    Intrinsics.throwNpe();
                }

                if (var10000.getWidth() == srcBitmap.getWidth()) {
                    var10000 = this.mDestBitmap;
                    if (var10000 == null) {
                        Intrinsics.throwNpe();
                    }

                    if (var10000.getHeight() == srcBitmap.getHeight() && offsetX == 0 && offsetY == 0 && alpha == -1.0F) {
                        this.mSrcBitmap = srcBitmap;
                        this.invalidate();
                        return;
                    }
                }

                Bitmap var10002 = this.mDestBitmap;
                if (var10002 == null) {
                    Intrinsics.throwNpe();
                }

                int[] size = this.computeSrcSize(srcBitmap, var10002);
                this.mSrcBitmap = this.reSizeBitmap(srcBitmap, size[0], size[1], offsetX, offsetY, alpha);
                this.invalidate();
            } else {
                this.mSrcBitmap = srcBitmap;
                this.invalidate();
            }
        }
    }

    // $FF: synthetic method
    public static void setSrcBitmap$default(DoubleLayerView var0, Bitmap var1, int var2, int var3, float var4, int var5, Object var6) throws Throwable {
        if ((var5 & 2) != 0) {
            var2 = 0;
        }

        if ((var5 & 4) != 0) {
            var3 = 0;
        }

        if ((var5 & 8) != 0) {
            var4 = -1.0F;
        }

        var0.setSrcBitmap(var1, var2, var3, var4);
    }

    private final Bitmap reSizeBitmap(Bitmap srcBitmap, int newWidth, int newHeight, int offsetX, int offsetY, float alpha) {
        if (this.mBitmapPaint == null) {
            this.mBitmapPaint = new Paint(1);
        }

        Paint var10000 = this.mBitmapPaint;
        if (var10000 == null) {
            Intrinsics.throwNpe();
        }

        var10000.setAlpha((int)(alpha * (float)255));
        Bitmap bitmap = Bitmap.createBitmap(this.getMeasuredWidth(), this.getMeasuredHeight(), Config.ARGB_8888);
        int top = this.getMeasuredHeight() - newHeight;
        int left = offsetX;
        top += offsetY;
        Canvas canvas = new Canvas(bitmap);
        canvas.drawBitmap(srcBitmap, (Rect)null, new Rect(left, top, left + newWidth, top + newHeight), this.mBitmapPaint);
        srcBitmap.recycle();
        Intrinsics.checkExpressionValueIsNotNull(bitmap, "bitmap");
        return bitmap;
    }

    // $FF: synthetic method
    static Bitmap reSizeBitmap$default(DoubleLayerView var0, Bitmap var1, int var2, int var3, int var4, int var5, float var6, int var7, Object var8) {
        if ((var7 & 8) != 0) {
            var4 = 0;
        }

        if ((var7 & 16) != 0) {
            var5 = 0;
        }

        if ((var7 & 32) != 0) {
            var6 = -1.0F;
        }

        return var0.reSizeBitmap(var1, var2, var3, var4, var5, var6);
    }

    private final int[] computeNewSize(Bitmap bitmap, int width, int height) {
        int[] size = new int[]{0, 0};
        float ratioSrc = (float)bitmap.getHeight() / (float)bitmap.getWidth();
        float ratioDest = (float)height / (float)width;
        if (ratioDest > ratioSrc) {
            size[0] = width;
            size[1] = (int)(ratioSrc * (float)size[0]);
        } else if (ratioSrc > ratioDest) {
            size[1] = height;
            size[0] = (int)((float)size[1] / ratioSrc);
        } else {
            size[0] = width;
            size[1] = height;
        }

        return size;
    }

    private final int[] computeSrcSize(Bitmap srcBitmap, Bitmap destBitmap) {
        return this.computeNewSize(srcBitmap, destBitmap.getWidth(), destBitmap.getHeight());
    }

    private final boolean checkDestBitmap() {
        return this.mDestBitmap != null;
    }

    private final Bitmap getBlurBitmap(int radius, Bitmap bitmap) {
        RenderScript renderScript = RenderScript.create(this.getContext());
        Allocation var10000 = Allocation.createFromBitmap(renderScript, bitmap);
        Intrinsics.checkExpressionValueIsNotNull(var10000, "Allocation.createFromBitmap(renderScript, bitmap)");
        Allocation input = var10000;
        var10000 = Allocation.createTyped(renderScript, input.getType());
        Intrinsics.checkExpressionValueIsNotNull(var10000, "Allocation.createTyped(renderScript, input.type)");
        Allocation output = var10000;
        ScriptIntrinsicBlur var7 = ScriptIntrinsicBlur.create(renderScript, Element.U8_4(renderScript));
        Intrinsics.checkExpressionValueIsNotNull(var7, "ScriptIntrinsicBlur.crea…ement.U8_4(renderScript))");
        ScriptIntrinsicBlur scriptIntrinsicBlur = var7;
        scriptIntrinsicBlur.setRadius((float)radius);
        scriptIntrinsicBlur.setInput(input);
        scriptIntrinsicBlur.forEach(output);
        output.copyTo(bitmap);
        return bitmap;
    }

    public DoubleLayerView(@Nullable Context context) {
        this(context, (AttributeSet)null);
    }

    public DoubleLayerView(@Nullable Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public DoubleLayerView(@Nullable Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.mDestAlpha = -1.0F;
        this.mSrcAlpha = -1.0F;
        this.mPaint = new Paint(1);
    }

    public View _$_findCachedViewById(int var1) {
        if (this._$_findViewCache == null) {
            this._$_findViewCache = new HashMap();
        }

        View var2 = (View)this._$_findViewCache.get(var1);
        if (var2 == null) {
            var2 = this.findViewById(var1);
            this._$_findViewCache.put(var1, var2);
        }

        return var2;
    }

    public void _$_clearFindViewByIdCache() {
        if (this._$_findViewCache != null) {
            this._$_findViewCache.clear();
        }

    }

    public static final class Companion {
        private Companion() {
        }
    }
}
