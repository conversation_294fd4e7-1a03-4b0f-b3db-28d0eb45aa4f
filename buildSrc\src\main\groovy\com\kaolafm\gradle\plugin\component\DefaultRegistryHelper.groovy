package com.kaolafm.gradle.plugin.component
/**
 * 框架自身默认的注册配置辅助类
 */
class DefaultRegistryHelper {

    static void addDefaultRegistry(ArrayList<RegisterInfo> list) {
        def exclude = ['com/kaolafm/kradio/component/.*']
        addDefaultRegistryFor(list,
                'com.kaolafm.kradio.component.Component',
                ['com.kaolafm.kradio.component.BaseComponent'],
                'com.kaolafm.kradio.component.ComponentManager',
                null,
                'registerComponent',
                exclude)
        addDefaultRegistryFor(list,
                'com.kaolafm.kradio.lib.init.AppInitTaskContainer',
                ['com.kaolafm.kradio.lib.init.AppInitTaskContainer'],
                'com.kaolafm.kradio.lib.init.AppInitManager',
                null,
                'registerInitializers',
                ['com/kaolafm/kradio/lib.init/.*'])
    }

    static void addDefaultRegistryFor(ArrayList<RegisterInfo> list, String interfaceName,
            ArrayList<String> superClassNames,
            String codeInsertToClassName,
            String initMethodName,
            String registerMethodName,
            List<String> exclude) {
        if (!list.find { it.interfaceName == RegisterInfo.convertDotToSlash(interfaceName) }) {
            RegisterInfo info = new RegisterInfo()
            info.interfaceName = interfaceName
            info.superClassNames = superClassNames
            info.initClassName = codeInsertToClassName //代码注入的类
            info.initMethodName = initMethodName
            info.registerMethodName = registerMethodName //生成的代码所调用的方法
//            info.paramType = paramType //注册方法的类型
            info.isShareConst = true
            info.exclude = exclude
            info.init()
            list.add(info)
        }
    }
}