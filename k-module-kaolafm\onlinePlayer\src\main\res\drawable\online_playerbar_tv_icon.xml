<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <selector>
            <item android:drawable="@drawable/online_playbar_fun_icon_normal" android:state_activated="true" android:state_enabled="true" />
            <item android:drawable="@drawable/online_playbar_fun_icon_pressed" android:state_enabled="false" />
            <item android:drawable="@drawable/online_playbar_fun_icon_pressed" android:state_activated="false" android:state_enabled="true" />
            <item android:drawable="@drawable/online_playbar_fun_icon_normal" />
        </selector>
    </item>
    <item android:state_pressed="false">
        <selector>
            <item android:drawable="@drawable/online_playbar_fun_icon_normal" android:state_activated="true" android:state_enabled="true" />
            <item android:drawable="@drawable/online_playbar_fun_icon_pressed" android:state_enabled="false" />
            <item android:drawable="@drawable/online_playbar_fun_icon_pressed" android:state_activated="false" android:state_enabled="true" />
            <item android:drawable="@drawable/online_playbar_fun_icon_normal" />
        </selector>
    </item>

</selector>