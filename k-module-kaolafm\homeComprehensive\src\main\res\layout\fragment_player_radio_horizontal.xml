<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/player_radio_root_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_home"
    android:clickable="false">

    <View
        android:id="@+id/player_title_line"
        android:layout_width="0dp"
        android:layout_height="@dimen/y1"
        android:layout_marginTop="@dimen/y125"
        android:background="@color/comprehensive_player_broadcast_title_divider"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        style="@style/ComprehensiveFragmentBackButton"
        android:layout_marginTop="0dp"
        app:layout_constraintBottom_toBottomOf="@id/player_radio_title_text"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/player_radio_title_text" />


    <com.kaolafm.kradio.component.ui.base.view.KradioTextView
        android:id="@+id/player_radio_title_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y50"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:textColor="@color/global_title_text_color"
        android:textSize="@dimen/text_size6"
        app:kt_font_weight="0.3"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="中国交通广播中国交通广播中国交通广播中国交通广播" />

    <com.kaolafm.kradio.player.comprehensive.audio.RadioPlaySourceFoundView
        android:id="@+id/radio_play_source_found_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_weight="0.1"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/player_radio_title_text"
        app:layout_constraintStart_toEndOf="@id/player_radio_title_text" />

    <TextView
        android:id="@+id/player_radio_title_sub_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginStart="@dimen/x120"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:textColor="@color/comprehensive_player_radio_audio_count_tv_color"
        android:textSize="@dimen/comprehensive_player_radio_audio_count_text_size"
        app:layout_constraintBottom_toBottomOf="@id/sortTypeLayout"
        app:layout_constraintStart_toEndOf="@id/player_radio_image"
        app:layout_constraintTop_toTopOf="@id/sortTypeLayout"
        tools:text="共500个音频" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/sortTypeLayout"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/y61"
        android:layout_marginTop="@dimen/y20"
        android:layout_marginEnd="@dimen/x140"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingStart="@dimen/x10"
        android:paddingTop="@dimen/y10"
        android:paddingEnd="@dimen/x10"
        android:paddingBottom="@dimen/y10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/player_title_line">

        <ImageView
            android:id="@+id/sortIcon"
            android:layout_width="@dimen/m36"
            android:layout_height="@dimen/m36"
            android:layout_marginEnd="@dimen/x10"
            android:src="@drawable/comprehensive_search_sort_up"
            app:layout_constraintBottom_toBottomOf="@id/sortTipTv"
            app:layout_constraintEnd_toStartOf="@id/sortTipTv"
            app:layout_constraintTop_toTopOf="@id/sortTipTv" />

        <TextView
            android:id="@+id/sortTipTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/y1"
            android:gravity="center_vertical"
            android:text="@string/comprehensive_player_sort_positive"
            android:textColor="@color/comprehensive_player_album_sort_tip"
            android:textSize="@dimen/m28"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/sortDividerView"
        android:layout_width="0dp"
        android:layout_height="@dimen/y1"
        android:layout_marginTop="@dimen/y91"
        android:layout_marginEnd="@dimen/x10"
        android:background="@color/comprehensive_player_radio_sort_divider"
        app:layout_constraintEnd_toEndOf="@id/sortTypeLayout"
        app:layout_constraintStart_toStartOf="@id/player_radio_title_sub_text"
        app:layout_constraintTop_toBottomOf="@id/player_title_line" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/sortTypeGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="sortDividerView,sortTypeLayout,player_radio_title_sub_text"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/player_radio_image"
        android:layout_width="@dimen/m260"
        android:layout_height="@dimen/m294"
        android:layout_marginStart="@dimen/x120"
        android:layout_marginTop="@dimen/y206"
        android:scaleType="fitXY"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/vip_icon"
        android:layout_width="@dimen/m60"
        android:layout_height="@dimen/m30"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@id/player_radio_image"
        app:layout_constraintTop_toTopOf="@id/player_radio_image"
        tools:src="@drawable/comprehensive_icon_live_class"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_live_flag"
        android:layout_width="@dimen/m60"
        android:layout_height="@dimen/m30"
        android:background="@drawable/flag_live_bg"
        android:gravity="center"
        android:text="@string/live"
        android:textColor="@color/colorWhite"
        android:textSize="@dimen/m18"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="@id/player_radio_image"
        app:layout_constraintTop_toTopOf="@id/player_radio_image"
        tools:visibility="gone" />

    <TextView
        android:id="@+id/tvBuy"
        android:layout_width="@dimen/m260"
        android:layout_height="@dimen/m50"
        android:layout_marginTop="@dimen/y6"
        android:background="@drawable/comprehensive_player_radio_vip_btn_bg"
        android:gravity="center"
        android:textColor="@color/text_color_white"
        android:textSize="@dimen/m26"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/player_radio_image"
        app:layout_constraintStart_toStartOf="@id/player_radio_image"
        app:layout_constraintTop_toBottomOf="@id/player_radio_image"
        tools:text="@string/vip_btn2"
        tools:visibility="visible" />

    <com.kaolafm.kradio.player.comprehensive.audio.RadioLiveItemView
        android:id="@+id/cl_live"
        android:layout_width="0dp"
        android:layout_height="@dimen/y104"
        android:layout_marginTop="@dimen/y51"
        android:layout_marginEnd="@dimen/all_view_left_right_distance"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="@id/player_radio_play_list"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/sortDividerView"
        tools:visibility="gone" />


    <com.kaolafm.kradio.player.comprehensive.play.view.AIRadioPlusFeedbackView
        android:id="@+id/ai_radio_plus_feed_back_view"
        android:layout_width="@dimen/m85"
        android:layout_height="@dimen/m85"
        android:layout_marginBottom="@dimen/m30"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/player_radio_image"
        app:layout_constraintRight_toRightOf="@id/player_radio_image"
        tools:visibility="visible" />

    <com.kaolafm.kradio.player.comprehensive.play.widget.RadioPlayListContent
        android:id="@+id/player_radio_play_list"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/x90"
        android:layout_marginEnd="@dimen/x120"
        app:layout_constraintBottom_toTopOf="@id/playerbarContentView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/player_radio_image"
        app:layout_constraintTop_toBottomOf="@id/cl_live"
        tools:visibility="gone" />

    <com.kaolafm.kradio.common.widget.RadioPlayerImageAnimLayout
        android:id="@+id/player_radio_image_anim_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/player_radio_image"
        app:layout_constraintLeft_toLeftOf="@id/player_radio_image"
        app:layout_constraintRight_toRightOf="@id/player_radio_image"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/player_radio_top_icon"
        android:layout_width="@dimen/m80"
        android:layout_height="@dimen/m80"
        android:layout_marginEnd="@dimen/m60"
        android:layout_marginBottom="@dimen/m20"
        android:contentDescription="@string/content_desc_top"
        android:scaleType="fitXY"
        android:src="@drawable/player_top_selector"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/playerbarContentView"
        app:layout_constraintRight_toRightOf="parent"
        tools:visibility="visible" />

    <FrameLayout
        android:id="@+id/playerbarContentView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <ViewStub
            android:id="@+id/playerBarViewStub"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/y20"
            android:layout="@layout/viewstub_playerbar"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </FrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
