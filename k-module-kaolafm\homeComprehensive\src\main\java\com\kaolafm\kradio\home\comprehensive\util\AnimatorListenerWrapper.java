package com.kaolafm.kradio.home.comprehensive.util;

import android.animation.Animator;

/**
 * 只需要重载onAnimationEnd这个接口，简化代码
 */
public class AnimatorListenerWrapper implements Animator.AnimatorListener {
    @Override
    public void onAnimationStart(Animator animation) {

    }

    @Override
    public void onAnimationEnd(Animator animation) {

    }

    @Override
    public void onAnimationCancel(Animator animation) {

    }

    @Override
    public void onAnimationRepeat(Animator animation) {

    }
}
