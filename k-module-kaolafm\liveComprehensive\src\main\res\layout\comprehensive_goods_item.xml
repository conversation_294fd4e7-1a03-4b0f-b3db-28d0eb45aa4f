<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:descendantFocusability="afterDescendants"
    android:background="@drawable/comprehensive_goods_item_bg">

    <com.kaolafm.kradio.component.ui.base.view.OvalImageView
        android:id="@+id/item_img"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginVertical="@dimen/m8"
        android:layout_marginLeft="@dimen/m8"
        android:layout_width="@dimen/comprehensive_live_shop_item_img_width"
        android:layout_height="@dimen/comprehensive_live_shop_item_img_width"
        android:scaleType="fitXY"
        app:rid_type="4"/>

    <TextView
        android:id="@+id/item_position"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="@id/item_img"
        app:layout_constraintLeft_toLeftOf="@id/item_img"
        android:layout_width="@dimen/comprehensive_live_shop_item_pos_width"
        android:layout_height="@dimen/comprehensive_live_shop_item_pos_height"
        android:gravity="center"
        android:text=""
        android:textColor="#FFFFFFFF"
        android:textSize="@dimen/m24"
        android:background="@drawable/comprehensive_goods_item_pos_bg"/>

    <RelativeLayout
        android:id="@+id/item_explain"
        app:layout_constraintLeft_toLeftOf="@id/item_img"
        app:layout_constraintBottom_toBottomOf="@id/item_img"
        android:layout_width="@dimen/comprehensive_live_shop_item_img_width"
        android:layout_height="@dimen/comprehensive_live_shop_item_explain_height"
        android:background="@drawable/comprehensive_goods_item_explain_bg"
        android:gravity="center">

        <com.kaolafm.kradio.component.ui.base.view.RateView
            android:id="@+id/item_explain_sign"
            android:layout_width="@dimen/m24"
            android:layout_height="@dimen/m24"
            app:lottie_autoPlay="true"
            app:lottie_fileName="lottie/rate.json"
            app:lottie_loop="true" />


        <TextView
            android:layout_toRightOf="@id/item_explain_sign"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="讲解中"
            android:textSize="@dimen/m20"
            android:textColor="#FFEEEEEE"/>

    </RelativeLayout>

    <TextView
        android:id="@+id/item_title"
        app:layout_constraintTop_toTopOf="@id/item_img"
        app:layout_constraintLeft_toRightOf="@id/item_img"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginHorizontal="@dimen/m20"
        android:layout_width="@dimen/m364"
        android:layout_height="wrap_content"
        android:minLines="2"
        android:maxLines="2"
        android:ellipsize="end"
        android:text=""
        android:textSize="@dimen/m26"
        android:lineSpacingExtra="@dimen/m5"
        android:textColor="#FF333333"/>

    <TextView
        android:id="@+id/item_desc"
        app:layout_constraintTop_toBottomOf="@id/item_title"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toRightOf="@id/item_img"
        android:layout_marginHorizontal="@dimen/m20"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:maxLines="1"
        android:ellipsize="end"
        android:text=""
        android:textSize="@dimen/m24"
        android:textColor="#FF4B4B"/>

    <TextView
        android:id="@+id/item_origin_price"
        app:layout_constraintTop_toBottomOf="@id/item_desc"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toRightOf="@id/item_img"
        android:layout_marginHorizontal="@dimen/m20"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="￥"
        android:textSize="@dimen/m24"
        android:textColor="#FF9497A8"/>

    <TextView
        android:id="@+id/item_discount"
        app:layout_constraintTop_toBottomOf="@id/item_origin_price"
        app:layout_constraintLeft_toRightOf="@id/item_img"
        app:layout_constraintBaseline_toBaselineOf="@id/item_discount_price"
        android:layout_marginLeft="@dimen/m20"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="￥"
        android:textSize="@dimen/m24"
        android:textColor="#FF4B4B"/>

    <TextView
        android:id="@+id/item_discount_price"
        app:layout_constraintTop_toBottomOf="@id/item_origin_price"
        app:layout_constraintLeft_toRightOf="@id/item_discount"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text=""
        android:textSize="@dimen/m36"
        android:textColor="#FF4B4B"/>

    <TextView
        android:id="@+id/item_btn"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginRight="@dimen/m20"
        android:layout_marginBottom="@dimen/m12"
        android:layout_width="@dimen/comprehensive_live_shop_item_btn_width"
        android:layout_height="@dimen/comprehensive_live_shop_item_btn_height"
        android:gravity="center"
        android:text="去抢购"
        android:textSize="@dimen/comprehensive_live_gift_item_price_selected_text_size"
        android:textColor="@color/comprehensive_gift_item_price_selected_color"
        android:background="@drawable/comprehensive_goods_item_btn_normal_bg"/>

    <TextView
        android:id="@+id/item_off_shelf"
        app:layout_constraintTop_toTopOf="@id/item_img"
        app:layout_constraintLeft_toLeftOf="@id/item_img"
        app:layout_constraintBottom_toBottomOf="@id/item_img"
        android:layout_width="@dimen/comprehensive_live_shop_item_img_width"
        android:layout_height="@dimen/comprehensive_live_shop_item_img_width"
        android:gravity="center"
        android:text="- 已下架 -"
        android:textSize="@dimen/comprehensive_live_gift_item_price_selected_text_size"
        android:textColor="@color/comprehensive_gift_item_price_selected_color"
        android:background="@drawable/comprehensive_goods_item_off_shelf_bg"/>

</androidx.constraintlayout.widget.ConstraintLayout>