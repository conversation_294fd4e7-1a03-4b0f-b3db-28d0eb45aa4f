package com.kaolafm.kradio.lib.utils;

import android.os.Process;
import android.util.Log;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.FakeNetworkCheckInter;

import java.net.InetSocketAddress;
import java.net.Socket;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

import io.reactivex.Single;
import io.reactivex.android.schedulers.AndroidSchedulers;

/**
 * @ClassName NetworkManager
 * <AUTHOR>
 * @Date 2020/6/16 18:28
 * @Version 1.0
 */
public class NetworkManager {
    private static final int MAX_RETRY_TIME = 10000;
    private static final int MIN_RETRY_TIME = 5000;

    public static final int NETWORK_DEFAULT = -1;
    public static final int NETWORK_NO = 0;
    public static final int NETWORK_CONNECT = 1;

    private static final String TAG = "NetworkManager";

    private volatile int mNoNetworkCount = 0;
    private static volatile NetworkManager manager;

    private static volatile int mNetWorkState = NETWORK_DEFAULT;
    private volatile int mRetryTime = MIN_RETRY_TIME;
    private Thread mThread;

    private List<INetworkReady> networkReadyArrayList = new CopyOnWriteArrayList<>();
    private volatile boolean isExitThread = false;
    private FakeNetworkCheckInter fakeNetworkCheckInter;

    private NetworkManager() {
    }

    public void init() {
        if (mThread != null) {
            return;
        }
        fakeNetworkCheckInter = ClazzImplUtil.getInter("FakeNetworkCheckImpl");
        start();
    }

    public static NetworkManager getInstance() {
        if (manager == null) {
            synchronized (NetworkManager.class) {
                if (manager == null) {
                    manager = new NetworkManager();
                }
            }
        }
        return manager;
    }

    public void start() {
        Log.i(TAG, "Socket test thread init!");
        initNetworkListener();
        isExitThread = false;
        mThread = new Thread(getRunnable());
        mThread.start();
    }

    private Runnable getRunnable() {
        return () -> {
            Process.setThreadPriority(Process.THREAD_PRIORITY_BACKGROUND);
            while (!isExitThread) {
                Log.i(TAG, "Socket test thread is running!");
                if (null != fakeNetworkCheckInter) {
                    checkFakeNetworkUseCustomizedMethod();
                } else {
                    testSocket(0);
                }
                try {
                    Thread.sleep(mRetryTime);
                } catch (InterruptedException e) {
                    Log.e(TAG, "Socket test thread sleep is interrupted.", e);
                }
            }
        };
    }

    public void checkFakeNetworkUseCustomizedMethod() {
        boolean networkUsable = fakeNetworkCheckInter.isInternetAvailable(AppDelegate.getInstance().getContext());
        Log.i(TAG, "checkFakeNetworkUseCustomizedMethod networkUsable: " + networkUsable);
        if (networkUsable) {
            mNoNetworkCount = 0;
            if (mNetWorkState != NETWORK_CONNECT) {
                Log.i(TAG, "NETWORK_CONNECT");
                mNetWorkState = NETWORK_CONNECT;
                mRetryTime = MAX_RETRY_TIME;
                notifyNetworkChange();
            }
        } else {
            if (mNoNetworkCount == 0) {
                Log.i(TAG, "Socket test failed, fault tolerance!");
                mNoNetworkCount++;
                return;
            }
            if (mNetWorkState != NETWORK_NO) {
                Log.i(TAG, "NETWORK_NO");
                mNetWorkState = NETWORK_NO;
                mRetryTime = MIN_RETRY_TIME;
                notifyNetworkChange();
            }
        }
    }

    private void initNetworkListener() {
        NetworkMonitor.getInstance(AppDelegate.getInstance().getContext()).registerNetworkStatusChangeListener(onNetworkStatusChangedListener);
    }

    private NetworkMonitor.OnNetworkStatusChangedListener onNetworkStatusChangedListener = (newStatus, oldStatus) -> {
        if (newStatus != NetworkMonitor.STATUS_NO_NETWORK) {
            Log.i(TAG, "Network status changed, new status is: " + newStatus);
            mThread.interrupt();
            start();
        } else {
            mNetWorkState = NETWORK_NO;
        }
    };

    private static final List<Address> addressList = new ArrayList<>();

    static {
        /*
         * 网络校验规则优化。
         * 现状：每个地区就近选择网络DNS服务器，如服务器不同，则判定网络不通。
         * 优化：对网络校验的网络DNS服务器主线不通的情况下，通过其他线路验证，所有线路均不通过，才判定网络不通。
         * 该优化已在比亚迪和福特项目处理。本次覆盖3.0.1新版本和2.10.5版本
         * DNS服务器地址：
         * <p/>
         * *********  https://www.alidns.com/
         * *********  https://www.alidns.com/
         * iovopen.radio.cn:443
         * ytlive.radio.cn
         * open.kaolafm.com:443
         * <p/>
         * 注：上述没有端口的都是53
         */
        addressList.add(new Address("iovopen.radio.cn", 443));
        addressList.add(new Address("ytlive.radio.cn", 443));
        addressList.add(new Address("ytcast.radio.cn", 443));
        addressList.add(new Address("open.kaolafm.com", 443));
        addressList.add(new Address("***************", 53));
    }

    private void testSocket(int ipIndex) {
        try {
            Log.i(TAG, "Socket test start");
            Socket sock = new Socket();
            Address address = addressList.get(ipIndex);
            Log.i(TAG, "ip: " + address.host);
            sock.connect(new InetSocketAddress(address.host, address.port), 3000);
            sock.close();

            Log.i(TAG, "Socket test success finish");
            if (mNetWorkState != NETWORK_CONNECT) {
                Log.i(TAG, "NETWORK_CONNECT");
                mNetWorkState = NETWORK_CONNECT;
                mRetryTime = MAX_RETRY_TIME;
                notifyNetworkChange();
            }
        } catch (Exception e) {
            Log.i(TAG, "check ip failed:" + e.toString());
            if (ipIndex + 1 < addressList.size()) { //判断下一个ip
                testSocket(ipIndex + 1);
                return;
            }
            if (mNetWorkState != NETWORK_NO) {
                Log.i(TAG, "NETWORK_NO");
                mNetWorkState = NETWORK_NO;
                mRetryTime = MIN_RETRY_TIME;
                notifyNetworkChange();
            }
            Log.i(TAG, "Socket test failed." + e.getMessage());
        }
    }

    public void addNetworkReadyListener(INetworkReady iNetworkReady) {
        if (iNetworkReady == null) {
            return;
        }
        if (!networkReadyArrayList.contains(iNetworkReady)) {
            networkReadyArrayList.add(iNetworkReady);
        }
    }

    public void removeNetworkReadyListener(INetworkReady iNetworkReady) {
        if (iNetworkReady == null) {
            return;
        }
        networkReadyArrayList.remove(iNetworkReady);
    }

    public void notifyNetworkChange() {
        Single.fromCallable(() -> true).observeOn(AndroidSchedulers.mainThread()).subscribe(aBoolean -> notifyChange(), throwable -> {
        });
    }

    private void notifyChange() {
        // 有些场景下，在networkChange调用时会从NetworkManager将自身监听移除掉，这样会导致后续Listener不执行，所以复制一份出来
        for (INetworkReady iNetworkReady : networkReadyArrayList) {
            if (iNetworkReady == null) {
                continue;
            }
            iNetworkReady.networkChange(mNetWorkState == NETWORK_CONNECT);
        }
    }

    public interface INetworkReady {
        void networkChange(boolean hasNetwork);
    }

    public boolean isNotHasNetwork() {
        return mNetWorkState != NETWORK_CONNECT;
    }

    public int getNetworkState() {
        Log.i(TAG, "getNetworkState:" + mNetWorkState);
        return mNetWorkState;
    }

    public void destroy() {
        NetworkMonitor.getInstance(AppDelegate.getInstance().getContext()).removeNetworkStatusChangeListener(onNetworkStatusChangedListener);
        if (mThread != null) {
            isExitThread = true;
            mThread.interrupt();
        }
        mThread = null;
    }

    static class Address {
        String host;
        int port;

        public Address(String host, int port) {
            this.host = host;
            this.port = port;
        }
    }
}
