package com.kaolafm.kradio.huawei.controller;

import com.huawei.carmediakit.bean.DialogAction;
import com.huawei.carmediakit.bean.OperResult;
import com.huawei.carmediakit.controller.IDialogController;
import com.iflytek.cloud.ErrorCode;
import com.kaolafm.kradio.huawei.utils.Constant;
import com.kaolafm.kradio.lib.utils.Logger;

public class DialogController implements IDialogController {
    public static final String TAG = Constant.TAG;

    @Override
    public OperResult dialogCallback(DialogAction dialogAction) {
        Logger.i(TAG, "dialogCallback");
        if ("LOGIN_NOTICE".equals(dialogAction.getDialogId())
                && "LOGIN_BUTTON_OK".equals(dialogAction.getClickedBtnId())) {
            // 打开会员中心页面。startMemberCenterActivity 待实现
            //startMemberCenterActivity();
        }
        return new OperResult(ErrorCode.SUCCESS, "Success");
    }

    @Override
    public OperResult onDialogCancel(String s) {
        return null;
    }

    @Override
    public OperResult onClosePayWindow() {
        return null;
    }
}
