<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tl="http://schemas.android.com/apk/res-auto"
    android:id="@+id/ll_subcategory_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/tabLayoutContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginTop="@dimen/m15"
        android:orientation="horizontal">

        <!--二级tab-->
        <!--注意:为了对齐fragment,必须满足:tl_tab_padding + layout_marginLeft == x90-->
        <com.flyco.tablayout.SlidingTabLayout
            android:id="@+id/stb_subcategory_tab_title"
            android:layout_width="0dp"
            android:layout_height="@dimen/y70"
            android:layout_weight="1"
            tl:tl_indicator_anim_enable="true"
            tl:tl_indicator_color="@color/tab_indicator_underline_color"
            tl:tl_indicator_height="@dimen/tab_indicator_height"
            tl:tl_indicator_width="@dimen/tab_indicator_width"
            tl:tl_indicator_width_equal_title="true"
            tl:tl_tab_padding="@dimen/n30"
            tl:tl_textBold="NONE"
            tl:tl_textSelectColor="@color/text_color_7"
            tl:tl_textSelectSize="@dimen/all_ctg_sub_title_size"
            tl:tl_textUnselectColor="@color/text_color_8"
            tl:tl_textsize="@dimen/all_ctg_sub_title_size" />


        <ImageView
            android:id="@+id/tv_refresh_btn"
            android:layout_width="@dimen/x138"
            android:layout_height="@dimen/y50"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="@dimen/y30"
            android:src="@drawable/selector_update_list"
            android:visibility="gone" />
    </LinearLayout>

    <View
        android:id="@+id/view_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/y1"
        android:background="@color/color_common_line" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.kaolafm.kradio.common.widget.NotScrollViewPager
            android:id="@+id/vp_subcategory_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never" />

        <ViewStub
            android:id="@+id/vs_layout_error_page"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout="@layout/layout_each_status_page" />

    </RelativeLayout>
</LinearLayout>
