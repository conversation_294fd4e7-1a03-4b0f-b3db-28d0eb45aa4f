package com.kaolafm.kradio.flavor.impl;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.util.Log;

import com.szlanyou.usercenter.IUserInterface;

/**
 * <AUTHOR>
 **/
public class RiChanHelper {

    private static final String TAG = "k.login.rch";
    private volatile static RiChanHelper mInstance;
    private Context mContext;
    private IUserInterface mService;
    private ServiceConnection mConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            mService = IUserInterface.Stub.asInterface(service);
            Log.i(TAG, "onServiceConnected:" + name + ",已连接.");
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            Log.i(TAG, "onServiceDisconnected:" + name + ",正在重连...");
            init();
        }
    };


    private RiChanHelper(Context context) {
        mContext = context;
    }

    public static RiChanHelper getInstance(Context context) {
        if (mInstance == null) {
            synchronized (RiChanHelper.class) {
                if (mInstance == null) {
                    mInstance = new RiChanHelper(context);
                }
            }
        }
        return mInstance;
    }


    public boolean init() {
        Intent intent = new Intent("com.szlanyou.usercenter.service.JiDouUserService");
        intent.setPackage("com.szlanyou.usercenter");
        intent.setAction("com.szlanyou.usercenter.AIDL_USERINFO_ACTION");
        return mContext.bindService(intent, mConnection, Context.BIND_AUTO_CREATE);
    }

    /**
     * 判断日产的[个人中心]是否登录
     *
     * @return
     */
    public boolean getLoginStatus() {
        boolean rst = false;
        try {
            String loginStatus = mService.getLoginStatus();
            Log.i(TAG, "onServiceConnected: loginStatus=" + loginStatus);
            //返回结果只是一个String类型的字符串，1代表已登录，0代表未登录
            rst = "1".equals(loginStatus);
//        } catch (DeadObjectException e) {
//            com.kaolafm.kradio.lib.utils.Logger.e(TAG, "getLoginStatus: 与个人中心(om.szlanyou.usercenter)的aidl连接中断了.");
        } catch (Exception e) {
            e.printStackTrace();
        }

        return rst;
    }

    /**
     * 显示日产[个人中心-登录]页面
     */
    public void showLoginPage() {
        Intent intent = new Intent();
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.setClassName("com.szlanyou.usercenter", "com.szlanyou.usercenter.view.user.QRCodeActivity");
        intent.putExtra("sourceName", "kaola");
        mContext.startActivity(intent);
    }
}
