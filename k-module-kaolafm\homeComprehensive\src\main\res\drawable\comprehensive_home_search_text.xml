<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!--  UI要求暂时禁用按压状态  -->
    <!--    <item android:state_pressed="true">-->
    <!--        <selector>-->
    <!--            <item android:drawable="@drawable/comprehensive_home_search_pressed" android:state_enabled="true" />-->
    <!--            <item android:drawable="@drawable/comprehensive_home_search_invalid" android:state_enabled="false" />-->
    <!--        </selector>-->
    <!--    </item>-->
    <!--    <item android:state_pressed="false">-->
    <!--        <selector>-->
    <!--            <item android:drawable="@drawable/comprehensive_home_search_normal" android:state_enabled="true" />-->
    <!--            <item android:drawable="@drawable/comprehensive_home_search_invalid" android:state_enabled="false" />-->
    <!--        </selector>-->
    <!--    </item>-->


    <item android:drawable="@drawable/comprehensive_home_search_normal" android:state_enabled="true" />
    <item android:drawable="@drawable/comprehensive_home_search_invalid" android:state_enabled="false" />

</selector>