package com.kaolafm.kradio.common.utils;

import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.kradio.lib.utils.Constants;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

public class DateFormatUtil {

    /**
     * 冒号分隔符
     */
    private static final String SEPERATOR_COLON = ":";

    private static SimpleDateFormat sdf = new SimpleDateFormat();
    /**
     * 任富 改 经过memory allocation tracker 观察 这个对象可以声明成类变量，同时“：” 用append拼接，内存使用减少17倍。
     */
    private static StringBuffer sb = new StringBuffer();

    /**
     * 时间单位
     */
    private static final int TIME_UNIT = 60;

    private static final String DATE_FORMAT_PATTERN = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_FORMAT_PATTERN_1 = "MM/dd HH:mm";
    public static final String DATE_FORMAT_PATTERN_2 = "mm时ss分";
    public static final String DATE_FORMAT_PATTERN_3 = "mm:ss";
    public static final String DATE_FORMAT_PATTERN_4 = "mm:ss";
    public static final String DATE_FORMAT_PATTERN_5 = "yyyy/MM/dd";
    public static final String DATE_FORMAT_PATTERN_6 = "yyyy-MM-dd  HH:mm:ss";
    public static final String DATE_FORMAT_PATTERN_7 = "yyyyMMdd";
    public static final String DATE_FORMAT_HOUR = "HH:mm:ss";

    public static String getCurrDate() {
        Calendar c = Calendar.getInstance();
        Date date = c.getTime();
        sdf.applyPattern(DATE_FORMAT_PATTERN);
//        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_PATTERN);
        String dStr = sdf.format(date);
        return dStr;
    }

    public static String getDateByTimeStamp(String pattern, long timeStamp) {
        Calendar c = Calendar.getInstance();
        c.setTimeInMillis(timeStamp);
        sdf.applyPattern(pattern);
//        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_PATTERN);
        String dStr = sdf.format(c.getTime());
        return dStr;
    }

    public static String getCurrDateByPattern(String pattern) {
        Calendar c = Calendar.getInstance();
        Date date = c.getTime();
        sdf.applyPattern(pattern);
//        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        String dStr = sdf.format(date);
        return dStr;
    }

    public static String getCurrDate(long time) {

        sdf.applyPattern(DATE_FORMAT_HOUR);
//        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_HOUR);
        String dStr = sdf.format(new Date(time));
        return dStr;
    }


    public static String getFormateDateString(long time, String pattern) {
        if (time > 3600000) {
            return getFormateDateHourString(time, DATE_FORMAT_HOUR);
        }
        /**
         * 转换时区，否则时间会出现问题  东八区
         */
        String timeZone = "GMT+8";
        sdf.applyPattern(pattern);
//        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        sdf.setTimeZone(TimeZone.getTimeZone(timeZone));
        String dStr = sdf.format(new Date(time));
        return dStr;
    }

    /**
     * 友好时间  HH:mm:ss
     *
     * @param time
     * @param pattern
     * @return
     */
    public static String getFormateDateHourString(long time, String pattern) {

        SimpleDateFormat formatter = new SimpleDateFormat(pattern);//初始化Formatter的转换格式。

        String hms = formatter.format(time - TimeZone.getDefault().getRawOffset());

        return hms;
    }

    public static String getFormateDateString(String getmPlayPosition, String pattern) {
        long time = 0;
        try {
            time = Long.parseLong(getmPlayPosition);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return getFormateDateString(time, pattern);
    }

    /**
     * translate time from origPatten to newPatten
     *
     * @param origTime
     * @param origPatten
     * @param newPatten
     * @return
     */
    public static String translateTime(String origTime, String origPatten, String newPatten) {
        try {
            SimpleDateFormat orgSdf = new SimpleDateFormat(origPatten, Locale.US);
            SimpleDateFormat newSdf = new SimpleDateFormat(newPatten, Locale.US);
            Date date = orgSdf.parse(origTime);
            return newSdf.format(date);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return origTime;
    }

    /**
     * format time.[HH:]mm:ss
     *
     * @param t
     * @return
     * <AUTHOR>
     */
    public static String formatTime(long t) {

        int hour = 0;
        int min = 0;
        int sec = 0;
        String formateTime = Constants.BLANK_STR;

        if (t > 60 * 60 * 1000) {
            hour = (int) (t / (60 * 60 * 1000));
            t -= hour * 60 * 60 * 1000;
        }
        if (t > 60 * 1000) {
            min = (int) (t / (60 * 1000));
            t -= min * 60 * 1000;
        }
        if (t > 1000) {
            sec = (int) (t / 1000);
            t -= sec * 1000;
        }

        if (hour >= 10) {
            formateTime += hour + ":";
        } else if (hour > 0 && hour < 10) {
            formateTime += "0" + hour + ":";
        }

        if (min >= 10 && min < 60) {
            formateTime += min + ":";
        } else if (min > 0 && min < 10) {
            formateTime += "0" + min + ":";
        } else {
            formateTime += "00" + ":";
        }

        if (sec >= 10 && sec < 60) {
            formateTime += sec + Constants.BLANK_STR;
        } else if (sec > 0 && sec < 10) {
            formateTime += "0" + sec;
        } else {
            formateTime += "00";
        }
        return formateTime;
    }

    public static int getDayByYear(long time) {
        Calendar currentCalendar = Calendar.getInstance();
        currentCalendar.setTimeInMillis(time);
        int day = currentCalendar.get(Calendar.YEAR) * 1000 + currentCalendar.get(Calendar.DAY_OF_YEAR);
        return day;
    }

    public static String getDescriptiveTime(int ms) {
        if (ms <= 0) {
            return "00:00";
        }
        int s = (int) Math.floor(ms / 1000F);
        int i = s / 60;
        int j = s % 60;
        String min = i < 10 ? "0" + i : String.valueOf(i);
        String sec = j < 10 ? "0" + j : String.valueOf(j);
        return min + ":" + sec;
    }

    /**
     * 得到时间间隔
     *
     * @param start  开始时间
     * @param end    结束时间
     * @param format 开始和结束时间的format
     * @return
     */
    public static String getDuration(String start, String end, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format, Locale.US);
        long between = 0;
        try {
            Date beginDate = sdf.parse(start);
            Date endDate = sdf.parse(end);
            between = (endDate.getTime() - beginDate.getTime());// 得到两者的毫秒数
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return formatTime(between);
    }

    /**
     * 把时间转换为毫秒数
     *
     * @param time, format is hh:mm:ss
     * @return
     */

    public static long transletToSecond(String time) {
        long seconds = 0;
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_HOUR, Locale.CHINA);
        try {
            Date timeDate = sdf.parse(time);
            seconds = timeDate.getTime();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return seconds;
    }

    /**
     * 得到时间间隔
     *
     * @param start 开始时间
     * @param end   结束时间
     * @param
     * @return
     */
    public static String getDuration(String start, String end) {
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_PATTERN, Locale.US);
        long between = 0;
        try {
            Date beginDate = sdf.parse(start);
            Date endDate = sdf.parse(end);
            between = (endDate.getTime() - beginDate.getTime());// 得到两者的毫秒数
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return formatTime(between);
    }
    public static int strToSecond(String time) {
        if (TextUtils.isEmpty(time)) {
            return 0;
        }
        String[] times = time.split(":");
        for (int i = 0; i < times.length; i++) {
            Log.i("DateFormatUtil", "strToSecond: time=" + Integer.valueOf(times[i]));
        }
        int length = times.length;
        if (length == 3){
            return (Integer.valueOf(times[0]) * 60 * 60 + Integer.valueOf(times[1]) * 60 + Integer.valueOf(times[2])) * 1000;
        }else if (length == 2){
            return (Integer.valueOf(times[0]) * 60 + Integer.valueOf(times[1])) * 1000;
        }
        return 0;
    }

    /**
     * 从指定时间字符串中解析年月日十分秒
     *
     * @param time 时间字符串
     * @return
     */
    public static Calendar getDateFormat(String time) {
        Calendar calendar = null;
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_PATTERN, Locale.CHINA);
        try {
            Date date = sdf.parse(time);
            calendar = Calendar.getInstance();
            calendar.setTime(date);

        } catch (Exception ex) {
            ex.printStackTrace();
        }

        return calendar;
    }

    /**
     * 将时间转成 时间戳
     *
     * @param time 2015-09-24 10:44:55
     * @return
     */
    public static long getDateToTimeStamp(String time) {
        long formatTime = 0;

        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.CHINA);
            Date date = format.parse(time);
            formatTime = date.getTime();
        } catch (ParseException e) {
            e.printStackTrace();
        }

        return formatTime;
    }

    /**
     * 处理时间格式，例如：3:5:4 -> 03:05:04
     *
     * @param time
     * @return
     */
    public static String getFormatTime(int time) {

        if (sb != null) {
            // 清除sb里的数据
            sb.delete(0, sb.length());
        }

        //将负转正
        if (time < 0) {
            time = Math.abs(time);
        }

//        StringBuffer sb = new StringBuffer();移到类变量

        int remainingMinutes = time % (TIME_UNIT * TIME_UNIT);

        int hours = time / (TIME_UNIT * TIME_UNIT);
        int minutes = remainingMinutes / TIME_UNIT;
        int seconds = remainingMinutes % TIME_UNIT;

//        sb.append(changeToDouble(hours) + ":");
//        sb.append(changeToDouble(minutes) + ":");
//        sb.append(changeToDouble(seconds));

        sb.append(changeToDouble(hours)).append(SEPERATOR_COLON);
        sb.append(changeToDouble(minutes)).append(SEPERATOR_COLON);
        sb.append(changeToDouble(seconds));

        return sb.toString();
    }

    /**
     * 如果当前的数值小于10，将其转换为2位的时间表示，例如：3:5:4 -> 03:05:04
     *
     * @param time
     * @return
     */
    public static String changeToDouble(int time) {
        if (time < 10)
            return "0" + time;
        return String.valueOf(time);
    }

    public static String getCurrTime() {
        sdf.applyPattern(DATE_FORMAT_HOUR);
        return sdf.format(new Date());
    }
}
