apply plugin: "java-library"
apply plugin: 'groovy'
apply plugin: 'java'

repositories {
    maven { url 'https://maven.aliyun.com/repository/public' }
    google()
}
dependencies {
    implementation gradleApi()//gradle sdk
    implementation localGroovy()//groovy sdk
    implementation 'com.android.tools.build:gradle:4.0.2'
    implementation 'com.google.auto.service:auto-service-annotations:1.0.1'
    annotationProcessor 'com.google.auto.service:auto-service:1.0.1'

    compileOnly 'androidx.annotation:annotation:1.6.0'
}

tasks.withType(JavaCompile) {
    options.encoding = "UTF-8"
}
java {
    sourceCompatibility = JavaVersion.VERSION_1_8
    targetCompatibility = JavaVersion.VERSION_1_8
}