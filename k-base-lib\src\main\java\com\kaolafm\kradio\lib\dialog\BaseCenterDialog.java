package com.kaolafm.kradio.lib.dialog;

import android.content.Context;
import android.content.DialogInterface;
import android.content.res.Resources.NotFoundException;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import com.kaolafm.kradio.lib.R;
import com.kaolafm.kradio.lib.dialog.BaseDialog.Build;
import com.kaolafm.kradio.lib.dialog.DialogListener.OnDismissListener;
import com.kaolafm.kradio.lib.dialog.DialogListener.OnNativeListener;
import com.kaolafm.kradio.lib.dialog.DialogListener.OnPositiveListener;
import com.kaolafm.kradio.lib.utils.ResUtil;

/**
 * 普通中间显示的基类dialog
 *
 * <AUTHOR>
 * @date 2018/5/11
 */

public abstract class BaseCenterDialog {

    protected final Context mContext;

    protected final BaseDialog mDialog;

    protected OnNativeListener mNativeListener;

    protected OnPositiveListener mPositiveListener;

    protected OnDismissListener mDismissListener;

    private final DialogInterface.OnDismissListener mOnDismissListener;

    public BaseCenterDialog(Context context) {
        mContext = context;
        mDialog = new Build(context)
                .setDialogView(R.layout.dialog_common_center)
                .setWidthScale(0.7F)
                .setHeight(ResUtil.getDimen(R.dimen.y590))
                .build();
        mOnDismissListener = dialog -> {
            if (mDismissListener != null) {
                mDismissListener.onDismiss();
                mDismissListener = null;
            }
            release();
        };
        mDialog.setOnDismissListener(mOnDismissListener);
        FrameLayout flDialogCommonContent = mDialog.getDialogView().findViewById(R.id.fl_dialog_center_content);
        View contentView = null;
        try {
            contentView = LayoutInflater.from(context).inflate(getLayoutId(), null);
            initView(contentView);
        } catch (NotFoundException e) {
            contentView = getContentView();
        }
        flDialogCommonContent.addView(contentView);
    }

    public void show(){
        onCreateView();
        mDialog.show();
    }
    public void dismiss(){
        if (mDialog != null){
            mDialog.dismiss();
        }
    }

    protected void release() {
        if (mDialog != null) {
            mDialog.setOnDismissListener(null);
        }
    }

    public <T extends BaseCenterDialog> T setOnDismissListener(OnDismissListener dismissListener){
        mDismissListener = dismissListener;
        return (T) this;
    }
    protected abstract void initView(View view);
    protected void onCreateView() {

    }

    protected View getContentView() {
        return null;
    }

    /**
     * 获取dialog显示内容的布局id
     */
    protected abstract int getLayoutId();

    public <T extends BaseCenterDialog> T setOnPositiveListener(OnPositiveListener<T> positiveListener){
        mPositiveListener = positiveListener;
        return (T) this;
    }
    public <T extends BaseCenterDialog> T setOnNativeListener(OnNativeListener nativeListener){
        mNativeListener = nativeListener;
        return (T) this;
    }

}
