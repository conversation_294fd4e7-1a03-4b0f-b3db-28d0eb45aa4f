package com.kaolafm.kradio.common.widget.listpopup;

import android.content.Context;
import android.graphics.Rect;
import android.os.Build;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.PopupWindow;

import com.kaolafm.kradio.k_kaolafm.R;

import java.util.List;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

public class ListPopupWindow extends PopupWindow {

    //上下文
    private Context context;
    //父视图
//    private View parentView;
    //item数据源
    private List<PopupItemBean> dataList;

    //适配器
    private ListPopupWindowAdpater adapter;

    //声明接口对象
    private OnPopubItemClickListener popupItemListener;
    private OnBottomTextViewClickListener bottomTextViewListener;


    /**
     * 定义接口用于PopupItem回调点击事件处理
     */
    public interface OnPopubItemClickListener {
        void onPopupItemClick(View view, int postion);
    }

    /**
     * 定义接口用于底部TextView回调点击事件处理
     */
    public interface OnBottomTextViewClickListener {
        void onBottomClick();
    }

    /**
     * 构造函数
     */
    public ListPopupWindow(Context context,
                           List<PopupItemBean> dataList,
                           OnPopubItemClickListener popupItemListener) {

        this.context = context;
        this.dataList = dataList;
//        this.parentView = parentView;

        this.popupItemListener = popupItemListener;
//        this.bottomTextViewListener = bottomTextViewListener;

        initCustomPopupWindow();
    }


    /**
     * 初始化自定义的PopupWindow
     */
    private void initCustomPopupWindow() {
        // 加载自定义布局文件，转化为组件
        View parentView = LayoutInflater.from(context).inflate(R.layout.list_popup, null);
        // 设置显示的view
        setContentView(parentView);

        // 初始化控件
        RecyclerView popWindowListView = (RecyclerView) parentView.findViewById(R.id.rv_popupWindowList);

//        TextView tv = (TextView) parentView.findViewById(R.id.id_tv_bottom);

        // 设置弹出窗体的高
        this.setWidth(ViewGroup.LayoutParams.WRAP_CONTENT);
        this.setHeight(ViewGroup.LayoutParams.WRAP_CONTENT);
        // 设置弹出窗体可点击
        this.setFocusable(true);
        // 设置SelectPicPopupWindow弹出窗体的背景
        this.setBackgroundDrawable(null);


        // view添加OnTouchListener监听判断获取触屏位置如果在布局外面则销毁弹出框
        parentView.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                // getTop View自身的顶边到其父布局顶边的距离,因为是根目录所以为0
                int height = parentView.findViewById(R.id.id_rl_relativeLayout).getTop();
                // getY 点击事件距离控件顶边的举例
                int y = (int) event.getY();
                // 当抬起 并且 y>height时（也就是 只要不点击ListView范围内）,dismiss popupWindow
                if (event.getAction() == MotionEvent.ACTION_UP) {
                    if (y > height) {
                        dismiss();
                    }
                }
                return true;
            }
        });
        // 更新位置和大小(不加这行代码也行)
//        update();
        // 实例化适配器
        adapter = new ListPopupWindowAdpater(context, dataList);

        // ListView设置点击事件
        adapter.setOnItemClickListener(new ListPopupWindowAdpater.OnItemClickListener() {
            @Override
            public void onItemClick(View view, int position) {
                popupItemListener.onPopupItemClick(view, position);
            }
        });

        popWindowListView.setLayoutManager(new LinearLayoutManager(popWindowListView.getContext()));
        // 设置适配器
        popWindowListView.setAdapter(adapter);

        // TextView设置点击事件
//        tv.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                // 回调onBottomClick
//                bottomTextViewListener.onBottomClick();
//            }
//        });

    }

    public void showAsDropDown(View anchor) {
        if (Build.VERSION.SDK_INT == 24) {
            Rect rect = new Rect();
            anchor.getGlobalVisibleRect(rect);
            int h = anchor.getResources().getDisplayMetrics().heightPixels - rect.bottom;
            setHeight(h);
        }
        super.showAsDropDown(anchor);
    }
}
