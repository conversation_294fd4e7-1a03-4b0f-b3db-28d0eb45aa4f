package com.kaolafm.ads.image;

import android.animation.Animator;
import android.content.Context;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import com.kaolafm.ad.api.model.InteractionAdvert;
import com.kaolafm.ads.image.base.BaseInteractContentView;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;

public class AdQrCodeView extends BaseInteractContentView {
    private ImageView mTvAdClose;
    private TextView mTvAdMsg;

    public AdQrCodeView(Context context) {
        super(context);
        init(context);
    }

    public AdQrCodeView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public AdQrCodeView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    @Override
    public void loadAdContent(InteractionAdvert advert) {
        super.loadAdContent(advert);
        if (StringUtil.isEmpty(advert.getDescription())){
            ViewUtil.setViewVisibility(mTvAdMsg, View.INVISIBLE);
        }else{
            ViewUtil.setViewVisibility(mTvAdMsg, View.VISIBLE);
            mTvAdMsg.setText(StringUtil.getMaxSubstring(advert.getDescription(),8));
        }
    }

    @Override
    public void countdownToCloseAd() {
        if(mDuration != 0) {
            super.countdownToCloseAd();
        }
    }

    private void init(Context context){
        LayoutInflater.from(context).inflate(R.layout.ad_qr_code_view_layout, this, true);
        mImageView = findViewById(R.id.iv_ad_qr);
        mTvAdMsg = findViewById(R.id.tv_ad_msg);
        mTvAdClose = findViewById(R.id.tv_ad_close);
        mTvAdClose.setOnClickListener((v) -> {
            cancelClose();
            hide();
        });
        this.setVisibility(INVISIBLE);
    }

    @Override
    public void hide() {
        super.hide(new HideAnimationListener(this) {
            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
                mAdImageListener.onAdSkip();
            }
        });
    }
}
