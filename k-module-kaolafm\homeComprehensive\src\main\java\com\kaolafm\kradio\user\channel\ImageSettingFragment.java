package com.kaolafm.kradio.user.channel;

import androidx.appcompat.widget.SwitchCompat;
import android.view.View;
import android.widget.Button;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.RadioButton;
import android.widget.RadioGroup;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.mvp.IPresenter;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.lib.sdk.utils.PerformanceSettingMananger;
import com.kaolafm.kradio.lib.utils.StringUtil;


/**
 * <pre>
 *     <AUTHOR> Wenchl
 *     e-mail : <EMAIL>
 *     time   : 2019/02/18
 *     desc   :
 *     version: 1.0
 * </pre>
 */

public class ImageSettingFragment extends BaseFragment implements CompoundButton.OnCheckedChangeListener {

    RadioButton imageHigh;
    RadioButton imageMedium;
    RadioButton imageLow;
    RadioGroup imageRadiogroup;
    SwitchCompat playerImageSwitch;
    SwitchCompat broadcastPlaylistSwitch;
    SwitchCompat homeImageSwitch;
    SwitchCompat jpgOrWebpSwitch;
    SwitchCompat splashSwitch;
    EditText ImageSwitchCustomizeEt;
    Button ImageSizeComitButton;

    @Override
    protected int getLayoutId() {
        return R.layout.user_fragment_image_setting;
    }

    @Override
    protected int getLayoutId_Tow() {
        return 0;
    }

    @Override
    protected IPresenter createPresenter() {
        return null;
    }

    @Override
    public void initView(View view) {
        imageHigh=view.findViewById(R.id.image_high);
        imageMedium=view.findViewById(R.id.image_medium);
        imageLow=view.findViewById(R.id.image_low);
        imageRadiogroup=view.findViewById(R.id.image_radiogroup);
        playerImageSwitch=view.findViewById(R.id.player_blur_switch);
        broadcastPlaylistSwitch=view.findViewById(R.id.broadcast_playlist_switch);
        homeImageSwitch=view.findViewById(R.id.home_image_switch);
        jpgOrWebpSwitch=view.findViewById(R.id.jpgorwebp_switch);
        splashSwitch=view.findViewById(R.id.splash_switch);
        ImageSwitchCustomizeEt=view.findViewById(R.id.image_customize);
        ImageSizeComitButton=view.findViewById(R.id.image_size_commit);


        imageRadiogroup.setOnCheckedChangeListener((group, checkedId) -> {
            int size = PerformanceSettingMananger.IMAGE_SIZE_DEFAULT;
            if (checkedId == R.id.image_high) {
                size = PerformanceSettingMananger.HIGH_SIZE;
            } else if (checkedId == R.id.image_medium) {
                size = PerformanceSettingMananger.MEDIUM_SIZE;
            } else if (checkedId == R.id.image_low) {
                size = PerformanceSettingMananger.LOW_SIZE;
            }
            PerformanceSettingMananger.getInstance().setHomeImageSize(size);
            PerformanceSettingMananger.getInstance().setCategoriesImageSize(size);
        });

        imageRadiogroup.check(getCheckId());

        broadcastPlaylistSwitch.setOnCheckedChangeListener(this);
        playerImageSwitch.setOnCheckedChangeListener(this);
        homeImageSwitch.setOnCheckedChangeListener(this);
        jpgOrWebpSwitch.setOnCheckedChangeListener(this);
        splashSwitch.setOnCheckedChangeListener(this);
        playerImageSwitch.setChecked(PerformanceSettingMananger.getInstance().getIsNeedBlur());
        broadcastPlaylistSwitch.setChecked(PerformanceSettingMananger.getInstance().getPlayerIsNeedMongolia());
        homeImageSwitch.setChecked(PerformanceSettingMananger.getInstance().getHomeIsNeedMongolia());
        jpgOrWebpSwitch.setChecked(PerformanceSettingMananger.getInstance().getHomeIsJpgOrWebp());
        splashSwitch.setChecked(PerformanceSettingMananger.getInstance().getHomeIsStartSplash());
        ImageSizeComitButton.setOnClickListener(v -> {
            String sizeStr = ImageSwitchCustomizeEt.getText().toString();
            if (StringUtil.isEmpty(sizeStr)) {
                return;
            }
            try {
                int size = Integer.parseInt(sizeStr);
                PerformanceSettingMananger.getInstance().setHomeImageSize(size);
                PerformanceSettingMananger.getInstance().setCategoriesImageSize(size);
            } catch (Exception e) {

            }
        });
    }

    private int getCheckId() {
        int size = PerformanceSettingMananger.getInstance().getCategoriesImageSize();
        switch (size) {
            case PerformanceSettingMananger.HIGH_SIZE:
                return R.id.image_high;

            case PerformanceSettingMananger.MEDIUM_SIZE:
                return R.id.image_medium;

            case PerformanceSettingMananger.LOW_SIZE:
                return R.id.image_low;

            default:
                return R.id.image_low;
        }
    }

    @Override
    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
        int id = buttonView.getId();
        if (id == R.id.player_blur_switch) {
            PerformanceSettingMananger.getInstance().setIsNeedBlur(isChecked);
        } else if (id == R.id.broadcast_playlist_switch) {
            PerformanceSettingMananger.getInstance().setPlayerIsNeedMongolia(isChecked);
        } else if (id == R.id.home_image_switch) {
            PerformanceSettingMananger.getInstance().setHomeIsNeedMongolia(isChecked);
        } else if (id == R.id.jpgorwebp_switch) {
            PerformanceSettingMananger.getInstance().setHomeIsJpgOrWebp(isChecked);
        } else if (id == R.id.splash_switch) {
            PerformanceSettingMananger.getInstance().setHomeIsStartSplash(isChecked);
        }
    }

}
