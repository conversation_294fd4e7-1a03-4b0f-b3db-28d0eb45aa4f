package com.kaolafm.kradio.common.utils;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.Signature;
import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.kradio.lib.utils.MD5;

public class SignUtil {

    private static final String TAG = "SignUtil";

    public static boolean isValidSignature(Context context) {
        //todo 此处可以放在native层处理增加反编译的难度
        boolean isValid = false;
        int signature = getSignature(context);
        String runTimeSign = MD5.getMD5Str(String.valueOf(signature));
        if (TextUtils.isEmpty(runTimeSign)) {
            Log.i(TAG,"isValidSignature:"+isValid);
            return isValid;
        }
        String validSign = "030b90b549bf42a8bc2e420a66efaefc";
        if (!validSign.equals(runTimeSign)) {
            Log.i(TAG,"isValidSignature:"+isValid);
            return isValid;
        }
        isValid = true;
        Log.i(TAG,"isValidSignature:"+isValid);
        return isValid;
    }

    private static int getSignature(Context context) {
        PackageManager pm = context.getPackageManager();
        PackageInfo pi;
        StringBuilder sb = new StringBuilder();
        // 获取签名信息
        try {
            pi = pm.getPackageInfo(context.getPackageName(), PackageManager.GET_SIGNATURES);
            Signature[] signatures = pi.signatures;
            for (Signature signature : signatures) {
                sb.append(signature.toCharsString());
            }
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        return sb.toString().hashCode();
    }

}
