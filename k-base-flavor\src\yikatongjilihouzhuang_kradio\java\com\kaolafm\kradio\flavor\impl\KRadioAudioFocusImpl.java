package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.media.AudioManager;
import android.util.Log;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioFocusInter;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-09-16 15:03
 ******************************************/
public final class KRadioAudioFocusImpl implements KRadioAudioFocusInter {
    private static final String TAG = "KRadioAudioFocusImpl";
    public static final int ECARX_AUDIO_STREAM_TYPE = 15;

    @Override
    public boolean requestAudioFocus(Object... args) {
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001276965757?userId=1229522问题
        AudioManager am = (AudioManager) AppDelegate.getInstance().getContext().getSystemService(Context.AUDIO_SERVICE);
        boolean status = AudioManager.AUDIOFOCUS_REQUEST_GRANTED ==
                am.requestAudioFocus((AudioManager.OnAudioFocusChangeListener) args[0], ECARX_AUDIO_STREAM_TYPE,
                        AudioManager.AUDIOFOCUS_GAIN);
        Log.i(TAG, "live requestAudioFocus status:" + status);
        return status;
    }

    @Override
    public boolean abandonAudioFocus(Object... args) {
        AudioManager am = (AudioManager) AppDelegate.getInstance().getContext().getSystemService(Context.AUDIO_SERVICE);
        boolean status = AudioManager.AUDIOFOCUS_REQUEST_GRANTED ==
                am.abandonAudioFocus((AudioManager.OnAudioFocusChangeListener) args[0]);
        Log.i(TAG, "live abandonAudioFocus status:" + status);
        return status;
    }
}
