package com.kaolafm.kradio.home.comprehensive.item;

import android.content.res.Configuration;
import androidx.annotation.NonNull;
import android.util.Log;
import android.util.TypedValue;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.ad.api.model.ImageAdvert;
import com.kaolafm.ad.api.model.InteractionAdvert;
import com.kaolafm.ad.comprehensive.conflict.AdConflict;
import com.kaolafm.ad.comprehensive.control.KradioAdSceneConstants;
import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.component.ui.base.CellBinder;
import com.kaolafm.kradio.lib.widget.square.SquareLayout;
import com.kaolafm.kradio.home.comprehensive.ad.KradioAdColumnManager;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.flavor.KRadioHomeItemFontSizeInter;
import com.kaolafm.kradio.lib.common.ResType;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.lib.utils.imageloader.listener.OnImageLoaderListener;
import com.kaolafm.kradio.lib.widget.ratio.RatioConstraintLayout;
import com.kaolafm.kradio.component.ui.base.view.RateView;
import com.kaolafm.kradio.component.ui.base.utils.VipCornerUtil;

/**
 * 首页普通黄金比例类型，目前包括专辑、电台、直播类型，其他未定义的类型也默认显示该类型。
 *
 * <AUTHOR> Yan
 * @date 2019-08-16
 */
public class GoldenRatioCell extends HomeCell implements CellBinder<View, HomeCell> {
    private static final int TITILE_LINE_MAX = 8;

    ImageView mIvItemHomeCover;
    SquareLayout mSquareLayout;
    RatioConstraintLayout mRclItemHomeContent;
    TextView mTvItemHomeTitle;
    View mViewItemHomeCoverBg;
    View mViewItemHomeTextBg;
    TextView mTvItemHomeAdLabel;
    ImageView mVipView;
    RateView mPlayingIcon;

    @Override
    public void mountView(@NonNull HomeCell data, @NonNull View view, int position) {
        mIvItemHomeCover=view.findViewById(R.id.iv_item_home_cover);
        mSquareLayout=view.findViewById(R.id.sv_item_home_place);
        mRclItemHomeContent=view.findViewById(R.id.rcl_item_home_content);
        mTvItemHomeTitle=view.findViewById(R.id.tv_item_home_title);
        mViewItemHomeCoverBg=view.findViewById(R.id.view_item_home_cover_bg);
        mViewItemHomeTextBg=view.findViewById(R.id.view_item_home_text_bg);
        mTvItemHomeAdLabel=view.findViewById(R.id.tv_item_home_ad_label);
        mVipView=view.findViewById(R.id.vip_icon);
        mPlayingIcon=view.findViewById(R.id.vs_layout_playing);


        Log.i("GoldenRatioCell", "mountView dataad:" + data.iImageAd + " , position:" + position + ",url:" + data.imageUrl);
        //因连图的上报属性和二次互动后台配置了全部的图片，所以这里只需要标记一个连图图片，曝光一次即可，对应 首页的GoldenRatioCell 类
        if (data.iImageAd == 0) {
            ImageLoader.getInstance().displayImage(view.getContext(), data.imageUrl, mIvItemHomeCover, new OnImageLoaderListener() {
                @Override
                public void onLoadingFailed(String url, ImageView target, Exception exception) {

                }

                @Override
                public void onLoadingComplete(String url, ImageView target) {
                    ImageAdvert imageAdvert = KradioAdColumnManager.getInstance().getAdvert(position);
                    if (imageAdvert != null) {
                        AdConflict.closeAdInteraction();
                        //上报连图展示
                        AdvertisingManager.getInstance().getReporter().display(imageAdvert);
                        //处理连图二次互动
                        InteractionAdvert interactionAdvert = imageAdvert.getInteractionAdvert();
                        if (interactionAdvert != null && interactionAdvert.getOpportunity() == 1) {
                            interactionAdvert.setSubtype(KradioAdSceneConstants.SUB_TYPE_COLOUM);
                            AdvertisingManager.getInstance().expose(interactionAdvert);
                        }
                        KradioAdColumnManager.getInstance().removeAdvert(position);
                    }
                }
            });
        } else {
            ImageLoader.getInstance().displayImage(view.getContext(), data.imageUrl, mIvItemHomeCover);
        }
        String tmpValue = data.name;
        if (tmpValue.length() <= TITILE_LINE_MAX) {
            tmpValue += "\n";
        }
        mTvItemHomeTitle.setText(tmpValue);
        Log.i("AdTag", data.toString());
        if (data.iImageAd >= 0) {
            ViewUtil.setViewVisibility(mTvItemHomeAdLabel, View.VISIBLE);
        } else {
            ViewUtil.setViewVisibility(mTvItemHomeAdLabel, View.GONE);
        }
        if (data.resType == ResType.LIVE_TYPE) {
            mVipView.setImageDrawable(ResUtil.getDrawable(R.drawable.ic_live));
        } else if (data.resType == com.kaolafm.opensdk.ResType.TYPE_VIDEO_AUDIO ||
                data.resType == com.kaolafm.opensdk.ResType.TYPE_VIDEO_ALBUM) {
            mVipView.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_icon_video));
        } else {
            //vip or 精品
            VipCornerUtil.setVipCorner(mVipView, data.vip, data.fine);
        }

        onOrientationChanged();
//        mSquareLayout.setPlayState(data.selected);
//        mIvItemHomeCover.setPlayState(data.selected);
        mPlayingIcon.setVisibility(data.selected? View.VISIBLE: View.GONE);
        mViewItemHomeCoverBg.setSelected(data.selected);
        mTvItemHomeTitle.setSelected(data.selected);

        KRadioHomeItemFontSizeInter homeItemFontSizeInter = ClazzImplUtil.getInter("KRadioHomeItemFontSizeImpl");
        if (homeItemFontSizeInter != null) {
            changeHomeTitleSize(ResUtil.getDimen(homeItemFontSizeInter.getSizeId()));
        } else {
            changeHomeTitleSize(ResUtil.getDimen(R.dimen.home_item_golden_text_size));
        }

        ViewUtil.setViewVisibilityAccordingToSetting(mViewItemHomeCoverBg);
    }

    private void changeHomeTitleSize(int size) {
        mTvItemHomeTitle.setTextSize(TypedValue.COMPLEX_UNIT_PX, size);
    }

    @Override
    public int getItemType() {
        return R.layout.item_home_golden_ration;
    }

    @Override
    public int spanSize() {
        return ResUtil.getInt(R.integer.home_item_radio_spans);
    }

    private void onOrientationChanged() {
        int orientation = ResUtil.getOrientation();
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            mRclItemHomeContent.setRatio("0.719:1");
        } else {
            mRclItemHomeContent.setRatio("0.66:1");
        }
    }
}
