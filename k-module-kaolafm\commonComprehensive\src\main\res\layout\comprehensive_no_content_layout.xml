<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/no_content_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:gravity="center"
    tools:background="@drawable/bg_home"
    android:orientation="vertical"
    tools:visibility="visible"
    android:visibility="gone">

    <ImageView
        android:id="@+id/iv_no_content"
        android:layout_width="@dimen/m360"
        android:layout_height="@dimen/m142"
        android:layout_marginBottom="@dimen/y32" />

    <TextView
        android:id="@+id/tv_no_content2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textColor="@color/text_color_7"
        android:textSize="@dimen/text_size4"
        tools:text="说你的见到你咖啡胶囊" />

    <LinearLayout
        android:id="@+id/tv_no_content_tip_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_no_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/m5"
            android:gravity="center"
            android:text="@string/comprehensive_hot_recommend_txt"
            android:textColor="@color/text_color_7"
            android:textSize="@dimen/text_size4" />

        <TextView
            android:id="@+id/tv_no_content3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/m5"
            android:gravity="center"
            android:textColor="@color/text_color_7"
            android:textSize="@dimen/text_size4"
            tools:text="说你的见到你咖啡胶囊" />
    </LinearLayout>
</LinearLayout>
