package com.kaolafm.kradio.common.http;

import android.text.TextUtils;

import com.kaolafm.kradio.lib.utils.KaolaAppConfigData;
import com.kaolafm.kradio.lib.utils.MD5;
import com.kaolafm.opensdk.account.token.AccessTokenManager;
import com.kaolafm.opensdk.account.token.KaolaAccessToken;
import com.kaolafm.opensdk.utils.BaseHttpsStrategy;

import java.util.Arrays;
import java.util.HashMap;

import static com.kaolafm.kradio.lib.utils.Constants.BLANK_STR;
import static com.kaolafm.kradio.lib.utils.Constants.KEY_APP_ID;
import static com.kaolafm.kradio.lib.utils.Constants.KEY_APP_TYPE;
import static com.kaolafm.kradio.lib.utils.Constants.KEY_CHANNEL;
import static com.kaolafm.kradio.lib.utils.Constants.KEY_DEVICE_ID;
import static com.kaolafm.kradio.lib.utils.Constants.KEY_KRADIO_USER_ID;
import static com.kaolafm.kradio.lib.utils.Constants.KEY_OPEN_ID;
import static com.kaolafm.kradio.lib.utils.Constants.KEY_OS;
import static com.kaolafm.kradio.lib.utils.Constants.KEY_PACKAGE;
import static com.kaolafm.kradio.lib.utils.Constants.KEY_SIGN;
import static com.kaolafm.kradio.lib.utils.Constants.KEY_TOKEN;
import static com.kaolafm.kradio.lib.utils.Constants.KEY_USER_ID;
import static com.kaolafm.kradio.lib.utils.Constants.KEY_VERSION;
import static com.kaolafm.kradio.lib.utils.Constants.LAT;
import static com.kaolafm.kradio.lib.utils.Constants.LNG;
import static com.kaolafm.kradio.lib.utils.Constants.OS_NAME;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-05-10 15:59
 *
 *
 * 此类只 对应 kaola 请求的旧接口
 * 调用此类前 需要和服务器人员确实 ,是否使用旧的签名验证方式
 * kradio 的新接口(新的验证方式 请使用) KRadioCommonRequestParamsUtil
 ******************************************/
public final class CommonRequestParamsUtil {

    private CommonRequestParamsUtil() {
    }

//    private static final String EQUAL_SIGN_STR = "=";

    /**
     * 获取公共参数
     *
     * @return
     */
    public static HashMap<String, String> getCommonParams() {
        HashMap<String, String> data = new HashMap<>();
        KaolaAppConfigData kaolaAppConfigData = KaolaAppConfigData.getInstance();
        KaolaAccessToken kaolaAccessToken = AccessTokenManager.getInstance().getKaolaAccessToken();
        String appId = kaolaAppConfigData.getAppId();
        String pkgName = kaolaAppConfigData.getPackageName();
        String openId = kaolaAccessToken.getOpenId();
        String deviceId = kaolaAppConfigData.getUdid();
        data.put(KEY_APP_ID, appId);
        data.put(KEY_PACKAGE, pkgName);
        if (!TextUtils.isEmpty(openId)) {
            data.put(KEY_OPEN_ID, openId);
        }
        data.put(KEY_DEVICE_ID, deviceId);
        data.put(KEY_OS, OS_NAME);

        String contentSub = data.toString().replace("{", BLANK_STR).replace("}", BLANK_STR).replace(" ", BLANK_STR);
        String[] totalValues = contentSub.split(",");

        data.put(KEY_SIGN, MD5.getMD5Str(madeUrlSign(totalValues, kaolaAppConfigData.getAppKey())));

        // 不需要加签参数
        if (!TextUtils.isEmpty(kaolaAccessToken.getUserId())) {
            data.put(KEY_USER_ID, kaolaAccessToken.getUserId());
        }

        data.put(KEY_CHANNEL, kaolaAppConfigData.getChannel());
        data.put(KEY_APP_TYPE, kaolaAppConfigData.getAppType());
        data.put(KEY_VERSION, kaolaAppConfigData.getVersionName());

        if (!TextUtils.isEmpty(kaolaAccessToken.getUserId())) {
            data.put(KEY_KRADIO_USER_ID, kaolaAccessToken.getUserId());
        }

        if (!TextUtils.isEmpty(kaolaAccessToken.getAccessToken())) {
            data.put(KEY_TOKEN, kaolaAccessToken.getAccessToken());
        }

        String lat = kaolaAppConfigData.getLat();
        data.put(LAT, lat == null ? "" : lat);
        String lng = kaolaAppConfigData.getLng();
        data.put(LNG, lng == null ? "" : lng);
        String capabilities = "PAY_CONTENT_SUPPORTTED";
        BaseHttpsStrategy baseHttpsStrategy = RequestInterceptManager.getInstance().getHttpsStrategy();
        if (baseHttpsStrategy != null && baseHttpsStrategy.isHttps(BaseHttpsStrategy.MEDIA)) {
            capabilities += ",MEDIA_URL_MUST_BE_HTTPS";
        }
        data.put("capabilities", capabilities);
        return data;
    }

    /**
     * 生成url加密sign数组
     *
     * @param params
     * @return
     */
    private static String madeUrlSign(String[] params, String appKey) {
        Arrays.sort(params);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(appKey);
        int index = 0;
        String preValue = null;
        for (String value : params) {
            if (TextUtils.isEmpty(value) || value.equals(preValue)) {
                continue;
            }
            String tempValue = value.trim();
            preValue = tempValue;
            stringBuilder.append(index++);
            stringBuilder.append(tempValue);
        }
        stringBuilder.append(appKey);
        return stringBuilder.toString();
    }
}
