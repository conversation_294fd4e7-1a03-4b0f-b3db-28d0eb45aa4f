package com.kaolafm.kradio.lib.base.adapter;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import java.util.ArrayList;

/******************************************
 * 类描述: 基础RecyclerView 适配器
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-04-11 11:15
 ******************************************/

public abstract class BaseRecyclerAdapter<T, V extends RecyclerView.ViewHolder> extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    private ArrayList<T> mData;

    private LayoutInflater mLayoutInflater;

    public BaseRecyclerAdapter(Context context) {
        mLayoutInflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
    }

    public BaseRecyclerAdapter(Context context, boolean hasStableIds) {
        setHasStableIds(hasStableIds);
        mLayoutInflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
    }

    public void setData(ArrayList<T> data){
        mData = data;
    }

    public ArrayList<T> getData() {
        return mData;
    }

    // CPU优化：减少刷新频率，使用防抖机制
    private static final long REFRESH_DEBOUNCE_TIME = 100; // 100ms防抖
    private long mLastRefreshTime = 0;

    public void updateData(ArrayList<T> data) {
        if (data == null || data.isEmpty()) {
            return;
        }
        if (mData != null && !mData.isEmpty()) {
            mData.clear();
        }
        appendData(data);
        // CPU优化：防抖刷新，避免频繁重绘
        debounceNotifyDataSetChanged();
    }

    private void debounceNotifyDataSetChanged() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - mLastRefreshTime > REFRESH_DEBOUNCE_TIME) {
            notifyDataSetChanged();
            mLastRefreshTime = currentTime;
        }
    }

    public void addData(ArrayList<T> data) {
        if (data == null || data.isEmpty()) {
            return;
        }
        int position = 0;
        if (mData != null && mData.size() != 0) {
            position = mData.size();
        }
        appendData(data);
        notifyItemRangeInserted(position, data.size());
    }

    public void addDataToHeader(ArrayList<T> data) {
        if (data == null || data.isEmpty()) {
            return;
        }
        appendDataToHeader(data);
        notifyItemRangeInserted(0, data.size());
    }

    public void addData(T data) {
        if (data == null) {
            return;
        }
        appendData(data);
        notifyItemInserted(mData.size() - 1);
    }

    public void addData(T data, int index) {
        if (data == null) {
            return;
        }
        appendDataAtIndex(data, index);
        notifyItemInserted(index);
    }

    public void updateData(T t) {
        if (t == null) {
            return;
        }
        if (mData != null && !mData.isEmpty()) { // 去重操作
            boolean isContains = mData.contains(t);
            if (isContains) {
                return;
            }
        }
        appendData(t);
        // CPU优化：使用防抖刷新
        debounceNotifyDataSetChanged();
    }

    public void clearData() {
        if (mData != null && !mData.isEmpty()) {
            mData.clear();
        }
        notifyDataSetChanged();
    }

    /**
     * 替换特定位置数据
     *
     * @param t
     * @param index
     */
    public void replacePositionData(T t, int index) {
        if (mData == null || mData.isEmpty()) {
            return;
        }
        if (mData.size() <= index) {
            return;
        }
        T tempData = mData.get(index);
        mData.remove(tempData);
        mData.add(index, t);
        notifyItemChanged(index);
//        notifyDataSetChanged();
    }

    public T getItem(int position) {
        return mData.get(position);
    }

    protected abstract int getContentLayoutId(int viewType);

    protected abstract V createViewHolder(View view, int viewType);


    @Override
    public V onCreateViewHolder(ViewGroup parent, int viewType) {
        Log.i("BaseAdapter", " " + getClass().getSimpleName());
        View view = mLayoutInflater.inflate(getContentLayoutId(viewType), parent, false);
        return createViewHolder(view, viewType);
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        T t = mData.get(position);
        (((BaseViewHolderUpdateListener) holder)).updateData(t, position);
    }

    @Override
    public void onViewRecycled(RecyclerView.ViewHolder holder) {
        (((BaseViewHolderUpdateListener) holder)).onViewRecycled(holder);
        super.onViewRecycled(holder);
    }

    @Override
    public int getItemCount() {
        if (mData == null) {
            return 0;
        }
        return mData.size();
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    private void appendData(ArrayList<T> data) {
        int size = data.size();
        if (mData == null) {
            mData = new ArrayList<>(size);
        }
        mData.addAll(data);
    }

    private void appendDataToHeader(ArrayList<T> data) {
        int size = data.size();
        if (mData == null) {
            mData = new ArrayList<>(size);
        }
        mData.addAll(0, data);
    }

    private void appendDataAtIndex(T t, int index) {
        if (mData == null) {
            mData = new ArrayList<>(1);
        }
        mData.add(index, t);
    }

    private void appendData(T t) {
        if (mData == null) {
            mData = new ArrayList<>(1);
        }
        mData.add(t);
    }
}
