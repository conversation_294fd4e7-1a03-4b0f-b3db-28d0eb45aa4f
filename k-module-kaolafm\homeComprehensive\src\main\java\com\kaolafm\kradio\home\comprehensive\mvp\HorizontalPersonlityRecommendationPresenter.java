package com.kaolafm.kradio.home.comprehensive.mvp;

import android.content.Context;
import android.util.Log;

import com.kaolafm.base.utils.NetworkMonitor;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.kradio.common.KradioSDKManager;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.mvp.IPresenter;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.opensdk.OpenSDK;
import com.kaolafm.opensdk.api.personalise.PersonalizedRequest;
import com.kaolafm.opensdk.api.personalise.model.InterestTag;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.log.Logging;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

public class HorizontalPersonlityRecommendationPresenter implements NetworkMonitor.OnNetworkStatusChangedListener, HorizontalPersonlityRecommendationPresenterContact.IPresenter, IPresenter {
    private HorizontalPersonlityRecommendationPresenterContact.View iView;
    PersonalizedRequest personalizedRequest = new PersonalizedRequest();
    //    List<InterestTag> mInterestTags;
    public static int mCheChangStaus = 0;
//    Context mcontext;

    private static final String TAG = "Test_Active";
    private MyOnUsableObserver myOnUsableObserver;

    @Override
    public void onStatusChanged(int newStatus, int oldStatus) {
        if (newStatus == NetworkMonitor.STATUS_MOBILE || newStatus == NetworkMonitor.STATUS_WIFI) {
            getInterestTagList();
        }
    }

    public HorizontalPersonlityRecommendationPresenter(Context context, HorizontalPersonlityRecommendationPresenterContact.View iView) {
        this.iView = iView;
        Log.i(TAG, "HorizontalPersonlityRecommendationPresenter---->" + OpenSDK.getInstance().isActivate());
        if (!OpenSDK.getInstance().isActivate()) {
            myOnUsableObserver = new MyOnUsableObserver(this);
            KradioSDKManager.getInstance().addUsableObserver(myOnUsableObserver);
//            EventBus.getDefault().register(this);
            showError(ResUtil.getString(R.string.no_active_str));
        } else {
            boolean isNetworkAvailable = NetworkUtil.isNetworkAvailable(context, false);
            Log.i(TAG, "HorizontalPersonlityRecommendationPresenter---->isNetworkAvailable = " + isNetworkAvailable);
            if (isNetworkAvailable) {
                getInterestTagList();
            } else {
                // 解决https://app.huoban.com/tables/2100000007530121/items/2300001480041382?userId=2169710问题
                NetworkMonitor.getInstance(context).registerNetworkStatusChangeListener(this);
                showError(ResUtil.getString(R.string.network_nosigin));
            }
        }
    }

    private static class MyOnUsableObserver implements KradioSDKManager.OnUsableObserver {
        private WeakReference<HorizontalPersonlityRecommendationPresenter> weakReference;

        public MyOnUsableObserver(HorizontalPersonlityRecommendationPresenter horizontalPersonlityRecommendationPresenter) {
            weakReference = new WeakReference<>(horizontalPersonlityRecommendationPresenter);
        }

        @Override
        public void activate() {
            HorizontalPersonlityRecommendationPresenter horizontalPersonlityRecommendationPresenter = weakReference.get();
            if (horizontalPersonlityRecommendationPresenter != null) {
                horizontalPersonlityRecommendationPresenter.getInterestTagList();
            }
        }
    }

    private void showError(String msg) {
        dealRequestExcepyion(new ApiException(-1, msg));
    }

    @Override
    public void saveInterestTags(ArrayList<String> tagList) {
        new PersonalizedRequest().saveInterestTags(StringUtil.collection2String(tagList, ","), new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                if (iView != null) {
                    iView.saveSuccess("保存成功");
                }
            }

            @Override
            public void onError(ApiException exception) {
                if (iView != null) {
                    iView.saveError(exception);
                }
            }
        });
    }

    @Override
    public void start() {
        Logging.i("获取兴趣标签");
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001424720623?userId=2169710问题
//        getInterestTagList();
    }

//    @Subscribe(threadMode = ThreadMode.MAIN)
//    public void getInterestTagListAfterActivate(ActivationEvent event) {
//        Log.i("RecommendationPresenter", "receive: " + event);
//        getInterestTagList();
//        EventBus.getDefault().unregister(this);
//    }

    public void getInterestTagList() {
        Log.i(TAG, "getInterestTagList------>start isActive = " + OpenSDK.getInstance().isActivate() + "--mCheChangStaus = " + mCheChangStaus);
        //首次进入获取 用户属性接口，如果列表为空，表示 没有属性，需要展示用户选择属性界面，如果有用户属性，进入展示用户兴趣标签界面
        if (mCheChangStaus == 0) {
//            ToastUtil.showOnly(mcontext,"未登录状态，直接走个性推荐");
            personalizedRequest.getInterestTagList(new HttpCallback<List<InterestTag>>() {
                @Override
                public void onSuccess(List<InterestTag> interestTags) {
                    Log.i(TAG, "getInterestTagList------>onSuccess");
                    iView.showFirst(interestTags);
                }

                @Override
                public void onError(ApiException exception) {
                    Log.i(TAG, "getInterestTagList------>onError");
                    dealRequestExcepyion(exception);
                }
            });
        } else if (mCheChangStaus == 1) {
//            ToastUtil.showOnly(mcontext,"听伴账号绑定，未保存车厂信息");
            personalizedRequest.getInterestTagList(new HttpCallback<List<InterestTag>>() {
                @Override
                public void onSuccess(List<InterestTag> interestTags) {
                    Log.i(TAG, "getInterestTagList------>onSuccess");
                    iView.showFirst(interestTags);
                }

                @Override
                public void onError(ApiException exception) {
                    Log.i(TAG, "getInterestTagList------>onError");
                    dealRequestExcepyion(exception);
                }
            });
        } else if (mCheChangStaus == 2) {
//            ToastUtil.showOnly(mcontext, "听伴账号绑定，保存车厂信息");

            personalizedRequest.getInterestTagList(new HttpCallback<List<InterestTag>>() {
                @Override
                public void onSuccess(List<InterestTag> interestTags) {
                    Log.i(TAG, "getInterestTagList------>onSuccess");
                    iView.showFirst(interestTags);
                }

                @Override
                public void onError(ApiException exception) {
                    Log.i(TAG, "getInterestTagList------>onError");
                    dealRequestExcepyion(exception);
                }
            });
        }
    }

    //    超时 和报错 统一处理
    public void dealRequestExcepyion(ApiException exception) {
        Logging.i("获取兴趣标签失败， exception=%s", exception);
        iView.showError(exception.getCode() + "");
    }

    @Override
    public void destroy() {
        KradioSDKManager.getInstance().removeUsableObserver(myOnUsableObserver);
    }
}

