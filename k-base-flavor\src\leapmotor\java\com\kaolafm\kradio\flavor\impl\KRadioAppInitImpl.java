package com.kaolafm.kradio.flavor.impl;

import com.kaolafm.kradio.flavor.broadcast.DashboardSendHelper;
import com.kaolafm.kradio.lib.base.flavor.KRadioAppInitInter;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.IPlayerStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

public class KRadioAppInitImpl implements KRadioAppInitInter {
    @Override
    public void onAppInit() {
        PlayerManager.getInstance().addPlayControlStateCallback(new IPlayerStateListener() {
            @Override
            public void onIdle(PlayItem playItem) {
                DashboardSendHelper.getInstance().send(playItem, DashboardSendHelper.STATUS_IDLE);
            }

            @Override
            public void onPlayerPreparing(PlayItem playItem) {

            }

            @Override
            public void onPlayerPlaying(PlayItem playItem) {
                DashboardSendHelper.getInstance().send(playItem, DashboardSendHelper.STATUS_PLAYING);
            }

            @Override
            public void onPlayerPaused(PlayItem playItem) {
                DashboardSendHelper.getInstance().send(playItem, DashboardSendHelper.STATUS_PAUSE);
            }

            @Override
            public void onProgress(PlayItem playItem, long l, long l1) {

            }

            @Override
            public void onPlayerFailed(PlayItem playItem, int i, int i1) {
                DashboardSendHelper.getInstance().send(playItem, DashboardSendHelper.STATUS_IDLE);
            }

            @Override
            public void onPlayerEnd(PlayItem playItem) {
                DashboardSendHelper.getInstance().send(playItem, DashboardSendHelper.STATUS_STOP);
            }

            @Override
            public void onSeekStart(PlayItem playItem) {

            }

            @Override
            public void onSeekComplete(PlayItem playItem) {

            }

            @Override
            public void onBufferingStart(PlayItem playItem) {

            }

            @Override
            public void onBufferingEnd(PlayItem playItem) {

            }

            @Override
            public void onDownloadProgress(PlayItem playItem, long l, long l1) {

            }
        });
    }
}
