package com.kaolafm.kradio.lib.basedb.manager;

import androidx.annotation.Nullable;

import com.kaolafm.kradio.lib.bean.BroadcastRadioSimpleData;

import java.util.List;

import io.reactivex.Single;

public abstract class BaseManager implements Manager {

    public void saveBroadcastList(@Nullable List<BroadcastRadioSimpleData> arrayList) {

    }

    public Single<List<BroadcastRadioSimpleData>> getLocalBroadcastList() {
        return null;
    }

    public List<BroadcastRadioSimpleData> getLocalBroadcastListDirectly() {
        return null;
    }
}
