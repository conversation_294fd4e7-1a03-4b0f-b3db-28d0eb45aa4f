package com.kaolafm.kradio.lib.bean;

import com.google.gson.annotations.SerializedName;
import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Generated;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Unique;

/**
 * 歌曲
 *
 * <AUTHOR>
 * @date 2018/4/19
 */
@Entity
public class SongHistory {

    /**
     * user_own_rule:1
     * album_id : 32528
     * album_mid : 004cs7z20yLcpB
     * album_name : 少年故事
     * album_pic : http://y.gtimg.cn/music/photo_new/T002R120x120M000004cs7z20yLcpB.jpg
     * genre : Rock  摇滚
     * hot : 0 1：表示我喜欢，0：表示非我喜欢
     * isOnly : 0 1：表示独家，0：表示非独家
     * k_song_id : 0
     * k_song_mid : 000g8Ykk3X4wxk
     * language : 国语
     * pingpong : 68_7077680_
     * playable : 1
     * public_time : 2007-07-28
     * recommend_reason : 根据你收藏的「封锁线」推荐
     * singer_id : 11566
     * singer_mid : 0003UsC034tJbh
     * singer_name : 彭坦
     * singer_pic : http://y.gtimg.cn/music/photo_new/T001R120x120M0000003UsC034tJbh.jpg
     * size_try : 0
     * song_h5_url : http://c.y.qq.com/v8/playsong.html?songmid=004AV0kp2CBJRe&ADTAG=opi12345671
     * song_id : 402719
     * song_mid : 004AV0kp2CBJRe
     * song_name : 孔雀
     * song_play_time : 321
     * song_play_url : http://isure.stream.qqmusic.qq.com/C200004AV0kp2CBJRe.m4a?vkey=133089D32DDD7B7D83966A8E4BD788A4E4C90AF885273A50ED22949F147300EE05D51A1EE79D1A016799423943B50DF3DD1877CA30C478F9&guid=1234713449&fromtag=50&uin=286729788
     * song_play_url_hq : http://isure.stream.qqmusic.qq.com/C600004AV0kp2CBJRe.m4a?vkey=4871CC643F95D6B9D93E43DDB67B2E33640CF3F373817FFBDF5CDEE7A15AE92A8EB491289DE5E0E68D90D7FBD4AB4F84B6C71C5BFDBE731F&guid=1234713449&fromtag=50&uin=286729788
     * song_play_url_sq : http://isure.stream.qqmusic.qq.com/F000004AV0kp2CBJRe.flac?vkey=F0B3BD4A4AB7891A15E3C3223F65A9B5E492E2ABBA62585DFC542D13C5728FBA2B00843A908C13347FF96715CCCDFBFD6418DEA24FBEFE9A&guid=1234713449&fromtag=50&uin=286729788
     * song_play_url_standard : http://isure.stream.qqmusic.qq.com/C400004AV0kp2CBJRe.m4a?vkey=210F4BF5A785241D220E40FBDAC607704971C848D0347D067C947BEB93EA1591D9DAAD85E7C89B72A7BA73C4F55CDF09BF1B0EA0F0456D8B&guid=1234713449&fromtag=50&uin=286729788
     * song_size : 1945654
     * song_size_hq : 7221597
     * song_size_sq : 28932270
     * song_size_standard : 3692087
     * try_begin : 0
     * try_end : 0
     * unplayable_code:该字段标识不能播放的原因：
     *                  1：因海外地区不能播放
     *                  2：因歌曲没有版权不能播放
     *                  3：该歌曲为付费歌曲，因用户非会员不能播放
     *                  4：因歌曲为数字专辑歌曲不能播放
     * unplayable_msg:不能播放原因描述语
     *
     */

    @Id(autoincrement = true)
    private Long id;
    /**
     * 表示用户拥有接口的权限。
     * 0：只浏览
     * 1：可播放
     */
    @SerializedName("user_own_rule")
    private int userOwnRule;

    @SerializedName("album_id")
    private int albumId;

    @SerializedName("album_mid")
    private String albumMid;

    @SerializedName("album_name")
    private String albumName;

    @SerializedName("album_pic")
    private String albumPic;

    @SerializedName("genre")
    private String genre;

    @SerializedName("hot")
    private int hot;

    @SerializedName("isOnly")
    private int isOnly;

    @SerializedName("k_song_id")
    private int kSongId;

    @SerializedName("k_song_mid")
    private String kSongMid;

    @SerializedName("language")
    private String language;

    @SerializedName("pingpong")
    private String pingpong;

    @SerializedName("playable")
    private int playable;

    @SerializedName("public_time")
    private String publicTime;

    @SerializedName("recommend_reason")
    private String recommendReason;

    @SerializedName("singer_id")
    private int singerId;

    @SerializedName("singer_mid")
    private String singerMid;

    @SerializedName("singer_name")
    private String singerName;

    @SerializedName("singer_pic")
    private String singerPic;

    @SerializedName("size_try")
    private int sizeTry;

    @SerializedName("song_h5_url")
    private String songH5Url;

    @Unique
    @SerializedName("song_id")
    private Long songId;

    @SerializedName("song_mid")
    private String songMid;

    @SerializedName("song_name")
    private String songName;

    @SerializedName("song_play_time")
    private int songPlayTime;

    @SerializedName("song_play_url")
    private String songPlayUrl;

    @SerializedName("song_play_url_hq")
    private String songPlayUrlHq;

    @SerializedName("song_play_url_sq")
    private String songPlayUrlSq;

    @SerializedName("song_play_url_standard")
    private String songPlayUrlStandard;

    @SerializedName("song_size")
    private int songSize;

    @SerializedName("song_size_hq")
    private int songSizeHq;

    @SerializedName("song_size_sq")
    private int songSizeSq;

    @SerializedName("song_size_standard")
    private int songSizeStandard;

    @SerializedName("try_begin")
    private int tryBegin;

    @SerializedName("try_end")
    private int tryEnd;

    /**
     * 该字段标识不能播放的原因：
     * 1：因海外地区不能播放
     * 2：因歌曲没有版权不能播放
     * 3：该歌曲为付费歌曲，因用户非会员不能播放
     * 4：因歌曲为数字专辑歌曲不能播放
     */
    @SerializedName("unplayable_code")
    private int unplayableCode;

    /**
     * 不能播放原因描述语
     */
    @SerializedName("unplayable_msg")
    private String unplayableMsg;

    private long timestamps;

    @Generated(hash = 1489366146)
    public SongHistory(Long id, int userOwnRule, int albumId, String albumMid, String albumName, String albumPic, String genre, int hot, int isOnly, int kSongId, String kSongMid, String language, String pingpong, int playable, String publicTime,
            String recommendReason, int singerId, String singerMid, String singerName, String singerPic, int sizeTry, String songH5Url, Long songId, String songMid, String songName, int songPlayTime, String songPlayUrl, String songPlayUrlHq,
            String songPlayUrlSq, String songPlayUrlStandard, int songSize, int songSizeHq, int songSizeSq, int songSizeStandard, int tryBegin, int tryEnd, int unplayableCode, String unplayableMsg, long timestamps) {
        this.id = id;
        this.userOwnRule = userOwnRule;
        this.albumId = albumId;
        this.albumMid = albumMid;
        this.albumName = albumName;
        this.albumPic = albumPic;
        this.genre = genre;
        this.hot = hot;
        this.isOnly = isOnly;
        this.kSongId = kSongId;
        this.kSongMid = kSongMid;
        this.language = language;
        this.pingpong = pingpong;
        this.playable = playable;
        this.publicTime = publicTime;
        this.recommendReason = recommendReason;
        this.singerId = singerId;
        this.singerMid = singerMid;
        this.singerName = singerName;
        this.singerPic = singerPic;
        this.sizeTry = sizeTry;
        this.songH5Url = songH5Url;
        this.songId = songId;
        this.songMid = songMid;
        this.songName = songName;
        this.songPlayTime = songPlayTime;
        this.songPlayUrl = songPlayUrl;
        this.songPlayUrlHq = songPlayUrlHq;
        this.songPlayUrlSq = songPlayUrlSq;
        this.songPlayUrlStandard = songPlayUrlStandard;
        this.songSize = songSize;
        this.songSizeHq = songSizeHq;
        this.songSizeSq = songSizeSq;
        this.songSizeStandard = songSizeStandard;
        this.tryBegin = tryBegin;
        this.tryEnd = tryEnd;
        this.unplayableCode = unplayableCode;
        this.unplayableMsg = unplayableMsg;
        this.timestamps = timestamps;
    }

    @Generated(hash = 813352260)
    public SongHistory() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public int getAlbumId() {
        return albumId;
    }

    public String getAlbumMid() {
        return albumMid;
    }

    public String getAlbumName() {
        return albumName;
    }

    public String getAlbumPic() {
        return albumPic;
    }

    public String getGenre() {
        return genre;
    }

    public int getHot() {
        return hot;
    }

    public int getIsOnly() {
        return isOnly;
    }

    public int getKSongId() {
        return kSongId;
    }

    public String getKSongMid() {
        return kSongMid;
    }

    public String getLanguage() {
        return language;
    }

    public String getPingpong() {
        return pingpong;
    }

    public int getPlayable() {
        return playable;
    }

    public String getPublicTime() {
        return publicTime;
    }

    public String getRecommendReason() {
        return recommendReason;
    }

    public int getSingerId() {
        return singerId;
    }

    public String getSingerMid() {
        return singerMid;
    }

    public String getSingerName() {
        return singerName;
    }

    public String getSingerPic() {
        return singerPic;
    }

    public int getSizeTry() {
        return sizeTry;
    }

    public String getSongH5Url() {
        return songH5Url;
    }

    public Long getSongId() {
        return songId;
    }

    public String getSongMid() {
        return songMid;
    }

    public String getSongName() {
        return songName;
    }

    public int getSongPlayTime() {
        return songPlayTime;
    }

    public String getSongPlayUrl() {
        return songPlayUrl;
    }

    public String getSongPlayUrlHq() {
        return songPlayUrlHq;
    }

    public String getSongPlayUrlSq() {
        return songPlayUrlSq;
    }

    public String getSongPlayUrlStandard() {
        return songPlayUrlStandard;
    }

    public int getSongSize() {
        return songSize;
    }

    public int getSongSizeHq() {
        return songSizeHq;
    }

    public int getSongSizeSq() {
        return songSizeSq;
    }

    public int getSongSizeStandard() {
        return songSizeStandard;
    }

    public int getTryBegin() {
        return tryBegin;
    }

    public int getTryEnd() {
        return tryEnd;
    }

    public void setAlbumId(int albumId) {
        this.albumId = albumId;
    }

    public void setAlbumMid(String albumMid) {
        this.albumMid = albumMid;
    }

    public void setAlbumName(String albumName) {
        this.albumName = albumName;
    }

    public void setAlbumPic(String albumPic) {
        this.albumPic = albumPic;
    }

    public void setGenre(String genre) {
        this.genre = genre;
    }

    public void setHot(int hot) {
        this.hot = hot;
    }

    public void setIsOnly(int isOnly) {
        this.isOnly = isOnly;
    }

    public void setKSongId(int kSongId) {
        this.kSongId = kSongId;
    }

    public void setKSongMid(String kSongMid) {
        this.kSongMid = kSongMid;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public void setPingpong(String pingpong) {
        this.pingpong = pingpong;
    }

    public void setPlayable(int playable) {
        this.playable = playable;
    }

    public void setPublicTime(String publicTime) {
        this.publicTime = publicTime;
    }

    public void setRecommendReason(String recommendReason) {
        this.recommendReason = recommendReason;
    }

    public void setSingerId(int singerId) {
        this.singerId = singerId;
    }

    public void setSingerMid(String singerMid) {
        this.singerMid = singerMid;
    }

    public void setSingerName(String singerName) {
        this.singerName = singerName;
    }

    public void setSingerPic(String singerPic) {
        this.singerPic = singerPic;
    }

    public void setSizeTry(int sizeTry) {
        this.sizeTry = sizeTry;
    }

    public void setSongH5Url(String songH5Url) {
        this.songH5Url = songH5Url;
    }

    public void setSongId(Long songId) {
        this.songId = songId;
    }

    public void setSongMid(String songMid) {
        this.songMid = songMid;
    }

    public void setSongName(String songName) {
        this.songName = songName;
    }

    public void setSongPlayTime(int songPlayTime) {
        this.songPlayTime = songPlayTime;
    }

    public void setSongPlayUrl(String songPlayUrl) {
        this.songPlayUrl = songPlayUrl;
    }

    public void setSongPlayUrlHq(String songPlayUrlHq) {
        this.songPlayUrlHq = songPlayUrlHq;
    }

    public void setSongPlayUrlSq(String songPlayUrlSq) {
        this.songPlayUrlSq = songPlayUrlSq;
    }

    public void setSongPlayUrlStandard(String songPlayUrlStandard) {
        this.songPlayUrlStandard = songPlayUrlStandard;
    }

    public void setSongSize(int songSize) {
        this.songSize = songSize;
    }

    public void setSongSizeHq(int songSizeHq) {
        this.songSizeHq = songSizeHq;
    }

    public void setSongSizeSq(int songSizeSq) {
        this.songSizeSq = songSizeSq;
    }

    public void setSongSizeStandard(int songSizeStandard) {
        this.songSizeStandard = songSizeStandard;
    }

    public void setTryBegin(int tryBegin) {
        this.tryBegin = tryBegin;
    }

    public void setTryEnd(int tryEnd) {
        this.tryEnd = tryEnd;
    }

    public int getUserOwnRule() {
        return userOwnRule;
    }

    public void setUserOwnRule(int userOwnRule) {
        this.userOwnRule = userOwnRule;
    }

    public int getkSongId() {
        return kSongId;
    }

    public void setkSongId(int kSongId) {
        this.kSongId = kSongId;
    }

    public String getkSongMid() {
        return kSongMid;
    }

    public void setkSongMid(String kSongMid) {
        this.kSongMid = kSongMid;
    }

    public int getUnplayableCode() {
        return unplayableCode;
    }

    public void setUnplayableCode(int unplayableCode) {
        this.unplayableCode = unplayableCode;
    }

    public String getUnplayableMsg() {
        return unplayableMsg;
    }

    public void setUnplayableMsg(String unplayableMsg) {
        this.unplayableMsg = unplayableMsg;
    }

    public long getTimestamps() {
        return timestamps;
    }

    public void setTimestamps(long timestamps) {
        this.timestamps = timestamps;
    }

}
