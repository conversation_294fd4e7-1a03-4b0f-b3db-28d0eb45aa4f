package com.kaolafm.kradio.service;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.os.Build;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import android.util.AttributeSet;
import android.view.View;
import android.widget.RemoteViews;

import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.utils.ScreenUtil;

/**
 * Created by chen.tuxi on 2020/2/11.
 * 进度控件
 */

public class ProgressView extends View {
    private static final int STROKE_WIDTH = 1;
    public static final int PROGRESS_WIDTH = 118;
    private static final int POINT_WIDTH = ScreenUtil.dp2px(2);
    private Bitmap mBitmapPoint;
    private Paint mPaint;
    private float mProgress;
    private boolean isSportTheme = false;


    //
    private Canvas canvas;
    private Bitmap progessBitmap;
    private Paint paint;
    private PorterDuffXfermode clear;
    private PorterDuffXfermode src;


    //

    public ProgressView(Context context) {
        this(context, null);
    }

    public ProgressView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        mPaint = new Paint();
//        mPaint.setColor(Color.parseColor("#00DCFF"));
        mPaint.setColor(Color.parseColor("#FF0000"));
        mPaint.setStrokeWidth(ScreenUtil.dp2px(STROKE_WIDTH));
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setAntiAlias(true);
        mBitmapPoint = BitmapFactory.decodeResource(context.getResources(), R.drawable.byd_widget_point_sport);


        //

        paint = new Paint();
//        paint.setColor(Color.RED);
//        paint.setColor(Color.parseColor("#00DCFF"));
        paint.setStrokeWidth(2);
        clear = new PorterDuffXfermode(PorterDuff.Mode.CLEAR);
        src = new PorterDuffXfermode(PorterDuff.Mode.SRC);

        int width = ScreenUtil.dp2px(ProgressView.PROGRESS_WIDTH); // Constants.PROGRESS_WIDTH = 92
        int height = ScreenUtil.dp2px(ProgressView.PROGRESS_WIDTH);
        //Bitmap.Config.ARGB_4444必须使用含有透明度的值，否则背景为黑色
        progessBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_4444);
        canvas = new Canvas(progessBitmap);
    }

    public void updateProgress(float progress) {
        this.mProgress = progress;
        invalidate();
    }

    public void setTheme(Context context, boolean sportTheme) {
        isSportTheme = sportTheme;
        if (isSportTheme) {
            mBitmapPoint = BitmapFactory.decodeResource(context.getResources(), R.drawable.byd_widget_point_sport);
            mPaint.setColor(Color.parseColor("#FF0000"));
        } else {
            mBitmapPoint = BitmapFactory.decodeResource(context.getResources(), R.drawable.byd_widget_point_eco);
            mPaint.setColor(Color.parseColor("#00DCFF"));
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        float degree = mProgress * 360;
        int left = POINT_WIDTH;
        int top = POINT_WIDTH;
        int right = ScreenUtil.dp2px(PROGRESS_WIDTH) - left;
        int bottom = ScreenUtil.dp2px(PROGRESS_WIDTH) - top;
        float px = ScreenUtil.dp2px(PROGRESS_WIDTH) / 2;
        float py = ScreenUtil.dp2px(PROGRESS_WIDTH) / 2;

        canvas.drawArc(left, top, right, bottom, 270, degree, false, mPaint);
        canvas.save();
        canvas.rotate(degree, px, py);
        canvas.drawBitmap(mBitmapPoint, px - POINT_WIDTH, 0 - POINT_WIDTH, mPaint);
        canvas.restore();

    }


    public void drawToRemoteViews(RemoteViews views, int appwidget_iv, float progress) {
        //更新进度
        updateProgress(progress);

        paint.setXfermode(clear);
        canvas.drawPaint(paint);
        paint.setXfermode(src);
        //把进度View更新到Bitmap中
        draw(canvas);
        //更新Widget的进度位图
        views.setBitmap(appwidget_iv, "setImageBitmap", progessBitmap);

    }
}
