package com.kaolafm.kradio.common.utils;

import android.util.Base64;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.ResUtil;

import java.io.UnsupportedEncodingException;
import java.util.Arrays;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

public class AESUtil {
    /**
     * AES加密密钥
     */
    public static final String AES_KEY = ResUtil.getString(R.string.AES_KEY);

    /**
     * 空字符串
     */
    public static final String BLANK_STR = "";

    /**
     * AES解密
     *
     * @param stringToDecode
     * @param key
     * @return
     * @throws NullPointerException
     */
    public static String decode(String stringToDecode, String key) throws Exception {
        String decodeStr = BLANK_STR;
        try {
            SecretKeySpec skeySpec = getKey(key);
            byte[] clearText = Base64.decode(stringToDecode.getBytes("UTF8"), Base64.DEFAULT);
            final byte[] iv = new byte[16];
            Arrays.fill(iv, (byte) 0x00);
            IvParameterSpec ivParameterSpec = new IvParameterSpec(iv);
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding");
            cipher.init(Cipher.DECRYPT_MODE, skeySpec, ivParameterSpec);
            decodeStr = new String(cipher.doFinal(clearText));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return decodeStr;
    }

    /**
     * AES加密
     *
     * @param stringToEncode
     * @param key
     * @return
     * @throws NullPointerException
     */
    public static String encode(String stringToEncode, String key) throws Exception {
        String encodeStr = BLANK_STR;
        try {
            SecretKeySpec skeySpec = getKey(key);
            byte[] clearText = stringToEncode.getBytes("UTF8");
            final byte[] iv = new byte[16];
            Arrays.fill(iv, (byte) 0x00);
            IvParameterSpec ivParameterSpec = new IvParameterSpec(iv);
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding");
            cipher.init(Cipher.ENCRYPT_MODE, skeySpec, ivParameterSpec);
            encodeStr = Base64.encodeToString(cipher.doFinal(clearText), Base64.DEFAULT);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return encodeStr;
    }

    private static SecretKeySpec getKey(String password) throws UnsupportedEncodingException {
        int keyLength = 128;
        byte[] keyBytes = new byte[keyLength / 8];
        Arrays.fill(keyBytes, (byte) 0x0);
        byte[] passwordBytes = password.getBytes("UTF-8");
        int length = passwordBytes.length < keyBytes.length ? passwordBytes.length : keyBytes.length;
        System.arraycopy(passwordBytes, 0, keyBytes, 0, length);
        SecretKeySpec key = new SecretKeySpec(keyBytes, "AES");
        return key;
    }
}