package com.kaolafm.kradio.user.setting;

import android.graphics.Color;
import android.os.SystemClock;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintLayout.LayoutParams;
import androidx.constraintlayout.widget.ConstraintSet;
import android.view.View;

import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.setting.SettingItem;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.sdk.utils.PerformanceSettingMananger;
import com.kaolafm.kradio.lib.utils.ResUtil;

import java.util.List;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;

/**
 * 后门相关功能
 *
 * <AUTHOR>
 * @date 2019-06-03
 */
@Aspect
public class HiddenFunctionAspect implements IHiddenFunctionView {

    //点击次数
    private final static int CLICK_COUNTS = 6;

    //规定有效时间
    private final static long DURATION = 2000;

    private BaseAdapter<SettingItem> mAdapter;

    private long[] mHits = new long[CLICK_COUNTS];

    private HiddenFunctionPresenter mPresenter;
    boolean isShow = false;//是否已经显示

    /**
     * 添加隐藏按钮
     * 2.10.1将SettingFragment换成了SettingActivity
     */
    @After("execution(* com.kaolafm.kradio.setting.comprehensive.ui.SettingActivity.onInitView(..))")
    public void addButton(JoinPoint point) throws Throwable {
        isShow = false;
        ConstraintLayout rootView = (ConstraintLayout) point.getArgs()[0];
        if (rootView != null) {
            View btn = new View(rootView.getContext());
            btn.setId(R.id.view_setting_hidden_btn);
            int dimen = ResUtil.getDimen(R.dimen.x150);
            btn.setLayoutParams(new LayoutParams(dimen, dimen));
            rootView.addView(btn);
            ConstraintSet constraintSet = new ConstraintSet();
            constraintSet.clone(rootView);
            constraintSet
                    .connect(btn.getId(), ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, 0);
            constraintSet.connect(btn.getId(), ConstraintSet.RIGHT, ConstraintSet.PARENT_ID, ConstraintSet.RIGHT, 0);
            constraintSet.setDimensionRatio(btn.getId(), "1:1");
            constraintSet.applyTo(rootView);

            mPresenter = new HiddenFunctionPresenter(this);

            if (!PerformanceSettingMananger.getInstance().isShowConfig()) {
                btn.setOnClickListener(this::onBtnClick);
            }
        }
    }


    @After("execution(* com.kaolafm.kradio.setting.comprehensive.ui.SettingActivity.showItems(..))")
    public void addHiddenItems(JoinPoint point) {
        if (PerformanceSettingMananger.getInstance().isShowConfig()) {
            //是否显示后门
            showBackDoor();
        }
    }

    /**
     * 获取adapter对象。有内存泄露
     * 2.10.1将SettingFragment换成了SettingActivity
     */
    //@AfterReturning(value = "execution(* com.kaolafm.kradio.setting.SettingFragment.getSettingAdapter(..))", returning = "adapter")
    @AfterReturning(value = "execution(* com.kaolafm.kradio.setting.comprehensive.ui.SettingActivity.getSettingAdapter(..))", returning = "adapter")
    public void getAdapter(BaseAdapter<SettingItem> adapter, JoinPoint point) throws Throwable {
        mAdapter = adapter;
    }

    @After("execution(* com.kaolafm.kradio.setting.comprehensive.ui.SettingActivity.onDestroy(..))")
    public void destroy(JoinPoint point) throws Throwable {
        mAdapter = null;
        if (mPresenter != null) {
            mPresenter.destroy();
            mPresenter = null;
        }
    }

    @Override
    public void showHideItem(List<SettingItem> hiddenItems) {
        if (mAdapter != null) {
            mAdapter.addDataList(hiddenItems);
        }
    }


    private void onBtnClick(View view) {
        //显示后门
        System.arraycopy(mHits, 1, mHits, 0, mHits.length - 1);
        //实现左移，然后最后一个位置更新距离开机的时间，如果最后一个时间和最开始时间小于DURATION，即连续5次点击
        mHits[mHits.length - 1] = SystemClock.uptimeMillis();
        if (mHits[0] >= (SystemClock.uptimeMillis() - DURATION) && !isShow) {
            isShow = true;
            if (!PerformanceSettingMananger.getInstance().isShowConfig()) {
                PerformanceSettingMananger.getInstance().showConfig();
                showBackDoor();
            }
        }
    }

    /**
     * 是否显示后门
     */
    private void showBackDoor() {
        if (mPresenter != null) {
            mPresenter.showHiddenFunctions();
        }
    }

}
