<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/user_main_bg"
    tools:context=".PerformanceSettingFragment">

    <RelativeLayout
        android:id="@+id/user_center_aboutus_title_rv"
        android:layout_width="match_parent"
        android:layout_height="@dimen/y120"
        >

        <ImageView
            android:id="@+id/login_close_btn"
            android:layout_width="@dimen/m80"
            android:layout_height="@dimen/m80"
            android:layout_centerVertical="true"
            android:layout_marginLeft="@dimen/x65"
            android:scaleType="centerCrop"
            android:padding="@dimen/m25"
            android:background="@drawable/color_main_button_click_selector"
            android:src="@drawable/user_ic_left_back" />

        <TextView
            android:id="@+id/login_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:text="@string/person_center_performance_setting"
            android:textColor="@color/text_color_1"
            android:textSize="@dimen/m32" />

        <View
            android:id="@+id/view_divider"
            android:layout_width="match_parent"
            android:layout_height="@dimen/y1"
            android:layout_alignParentBottom="true"
            android:background="#19FFFFFF" />
    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:weightSum="100"
        >

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/setting_recyclerview"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="30"
            ></androidx.recyclerview.widget.RecyclerView>

        <FrameLayout
            android:id="@+id/setting_fragment_layout"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="70"
            >
        </FrameLayout>

    </LinearLayout>


</LinearLayout>