<resources>

    <style name="home_all_ctg_style">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@drawable/ic_home_category</item>
    </style>
    <style name="AppThemeCompat" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowNoTitle">false</item>//无标题
        <item name="android:windowActionBar">false</item>//无ActionBar
        <item name="android:windowFullscreen">@bool/isWindowFullScreen</item>//全屏即无通知栏
        <item name="android:windowContentOverlay">@null</item>//是否有遮盖
        <item name="android:windowBackground">@null</item>
        <item name="android:windowEnableSplitTouch">false</item>
        <item name="android:splitMotionEvents">false</item>
    </style>
    <style name="AppThemeCompat.splash" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowNoTitle">false</item>//无标题
        <item name="android:windowActionBar">false</item>//无ActionBar
        <item name="android:windowFullscreen">@bool/isWindowFullScreen</item>//全屏即无通知栏
        <item name="android:windowContentOverlay">@null</item>//是否有遮盖
        <!--<item name="android:windowIsTranslucent">true</item>-->
        <!--        <item name="android:windowBackground">@drawable/splash_yunting</item>-->
        <item name="android:windowBackground">@drawable/background_splash_comprehensive</item>
        <!--        <item name="android:windowBackground">@drawable/ic_launcher_yt</item>-->
        <!--<item name="android:windowAnimationStyle">@style/activityDefaultAnimation</item>-->
        <!--<item name="android:windowAnimationStyle">@style/activityAnim</item>-->
    </style>
</resources>
