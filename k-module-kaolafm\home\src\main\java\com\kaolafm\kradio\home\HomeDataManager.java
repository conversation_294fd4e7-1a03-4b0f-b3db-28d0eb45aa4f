package com.kaolafm.kradio.home;

import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.common.http.CommonRequestParamsUtil;
import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.history.HistoryManager;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioPlayLogicInter;
import com.kaolafm.kradio.lib.base.flavor.KradioZoneInter;
import com.kaolafm.kradio.lib.bean.BroadcastRadioSimpleData;
import com.kaolafm.kradio.lib.bean.HistoryItem;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.KaolaAppConfigData;
import com.kaolafm.kradio.lib.utils.NetworkManager;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.YTDataCache;
import com.kaolafm.kradio.lib.utils.YTLogUtil;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.broadcast.BroadcastDetails;
import com.kaolafm.opensdk.api.broadcast.BroadcastRequest;
import com.kaolafm.opensdk.api.broadcast.ProgramDetails;
import com.kaolafm.opensdk.api.operation.OperationRequest;
import com.kaolafm.opensdk.api.operation.model.column.ActivityDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.BroadcastDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.ColumnGrp;
import com.kaolafm.opensdk.api.operation.model.column.PageRedirectionColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.WebViewColumnMember;
import com.kaolafm.opensdk.api.tv.TVRequest;
import com.kaolafm.opensdk.api.tv.model.TVDetails;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;
import com.kaolafm.opensdk.socket.SocketApiConstants;
import com.kaolafm.opensdk.socket.SocketEvent;
import com.kaolafm.opensdk.socket.SocketListener;
import com.kaolafm.opensdk.socket.SocketManager;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.CompletableFuture;

import io.reactivex.disposables.Disposable;

/**
 * 首页数据相关逻辑管理类。<br>
 * 将首页的逻辑抽离到这里，是因为有些车厂在不显示首页的时候就要获取到数据，但是逻辑执行都是一样的。<br>
 * 为了统一维护首页的相关逻辑，首页和渠道都只调用这里。
 *
 * <AUTHOR> Yan
 * @date 2019-11-06
 */
public class HomeDataManager {
    private final String TAG = "HomeDataManager";

    private volatile static HomeDataManager ourInstance;
    private Timer mTime;

    private TaskChain mTaskChain;
    private final static String KEY_HISTORY = "history";
    private final static String KEY_DATA = "data";

    private SocketListener<List<ColumnGrp>> mSocketListener;
    private final Queue<Long> broadcastDataList = new LinkedList<>();

    private List<ColumnGrp> mColumnGrpList;
    private HomeCell mFirstHomeCell;
    private Disposable localBroadcastListDisposable;

    private BroadcastRequest mBroadcastRequest;

    public static HomeDataManager getInstance() {
        if (ourInstance == null) {
            synchronized (HomeDataManager.class) {
                if (ourInstance == null) {
                    ourInstance = new HomeDataManager();
                }
            }
        }
        return ourInstance;
    }

    private HomeDataManager() {
        initConditions();
        mTime = new Timer();
    }

    private void initConditions() {
        mTaskChain = new TaskChain();
        mTaskChain.addTask(KEY_HISTORY, false);
        mTaskChain.addTask(KEY_DATA, false);
        mTaskChain.registerCallBack(() -> {
            mTaskChain = null;
            playFirstItem();
        });
        mBroadcastRequest = new BroadcastRequest();
    }

    private IPlayerInitCompleteListener initCompleteListener;
    private NetworkManager.INetworkReady networkReadyListener;

    public void autoPlay() {
        YTLogUtil.logStart(TAG, "autoPlay", "");
        CompletableFuture<Boolean> playerFuture = CompletableFuture.supplyAsync(this::waitForPlayerInitComplete);
        CompletableFuture<Boolean> interFuture = CompletableFuture.supplyAsync(this::checkHasAudioPlayerLogicInter);
        CompletableFuture<Boolean> networkFuture = CompletableFuture.supplyAsync(this::waitFromNetwork);

        CompletableFuture<Void> allOf = CompletableFuture.allOf(playerFuture, interFuture, networkFuture);
        allOf.thenRun(() -> {
            if (playerFuture.join() && interFuture.join() && networkFuture.join()) {
                YTLogUtil.logStart(TAG, "autoPlay", "allOf");
                YTDataCache.obtainHistoryList(HistoryManager.getInstance()).thenAccept(data -> {
                    if (!ListUtil.isEmpty(data)) {
                        hasDataPlay(data);
                    } else {
                        noDataPlay();
                    }
                });
            }
        });

//        CompletableFuture.supplyAsync(() -> waitForPlayerInitComplete().join())
//                .thenCompose(complete -> {
//                    YTLogUtil.logStart(TAG, "waitForPlayerInitComplete", "complete:" + complete);
//                    if (complete) {
//                        return checkHasAudioPlayerLogicInter();
//                    }
//                }).thenCompose(hasInter -> {
//                    YTLogUtil.logStart(TAG, "checkHasAudioPlayerLogicInter", "hasInter:" + hasInter);
//                    return waitFromNetwork();
//                }).thenCompose(success03 -> {
//                    YTLogUtil.logStart(TAG, "waitFromNetwork", "success03:" + success03);
//                    if (success03) {
////                        return waitFromHistory();
//                        return YTDataCache.obtainHistoryList();
//                    } else {
//                        return CompletableFuture.completedFuture(null);
//                    }
//                }).thenCompose(data -> {
//                    YTLogUtil.logStart(TAG, "waitFromHistory", "");
//                    if (data != null) {
//                        hasDataPlay(data);
//                    } else {
//                        noDataPlay();
//                    }
//                    return CompletableFuture.completedFuture(null);
//                }).exceptionally(e -> {
//                    // 错误处理
//                    YTLogUtil.logStart(TAG, "autoPlay", "Error: " + e.getMessage());
//                    return null;
//                });
    }

    private Boolean waitForPlayerInitComplete() {
        CompletableFuture<Boolean> future = new CompletableFuture<>();
        if (PlayerManagerHelper.getInstance().isPlayerInitComplete()) {
            future.complete(true);
        } else {
            initCompleteListener = success -> {
                PlayerManagerHelper.getInstance().removePlayerInitCompleteListener(initCompleteListener);
                initCompleteListener = null;
                future.complete(success);
            };
            PlayerManagerHelper.getInstance().addPlayerInitCompleteListener(initCompleteListener);
        }
        return future.join();
    }

    private Boolean waitFromNetwork() {
        CompletableFuture<Boolean> future = new CompletableFuture<>();
        if (NetworkManager.getInstance().isNotHasNetwork()) {
            networkReadyListener = hasNetwork -> {
                if (hasNetwork) {
                    NetworkManager.getInstance().removeNetworkReadyListener(networkReadyListener);
                    networkReadyListener = null;
                    future.complete(true);
                }
            };
            NetworkManager.getInstance().addNetworkReadyListener(networkReadyListener);
        } else {
            future.complete(true);
        }

        return future.join();
    }

    private Boolean checkHasAudioPlayerLogicInter() {
        CompletableFuture<Boolean> future = new CompletableFuture<>();
        new Handler(Looper.getMainLooper()).post(() -> {
            KRadioAudioPlayLogicInter mKRadioAudioPlayLogicInter = ClazzImplUtil.getInter("KRadioAudioPlayLogicImpl");
            if (mKRadioAudioPlayLogicInter != null) {
                boolean status = mKRadioAudioPlayLogicInter.autoPlayAudio(AppDelegate.getInstance().getContext());
                future.complete(!status);
            } else {
                future.complete(true);
            }
        });
        return future.join();
    }

    private CompletableFuture<List<HistoryItem>> waitFromHistory() {
        CompletableFuture<List<HistoryItem>> future = new CompletableFuture<>();
        HistoryManager.getInstance().getHistoryList(new HttpCallback<List<HistoryItem>>() {
            @Override
            public void onSuccess(List<HistoryItem> historyList) {
                if (!ListUtil.isEmpty(historyList)) {
                    future.complete(historyList);
                } else {
                    future.complete(null);
                }
            }

            @Override
            public void onError(ApiException e) {
                future.complete(null);
            }
        });
        return future;
    }


    private void hasDataPlay(List<HistoryItem> historyList) {
        String videoAlbumType = String.valueOf(PlayerConstants.RESOURCES_TYPE_VIDEO_ALBUM);
        String videoAudioType = String.valueOf(PlayerConstants.RESOURCES_TYPE_VIDEO_AUDIO);
        for (HistoryItem historyItem : historyList) {
            if (historyItem.getType().equals(videoAlbumType)) {
                continue;
            }

            if (historyItem.getType().equals(videoAudioType)) {
                continue;
            }
//            playHistoryTest(historyItem);
            playHistory(historyItem);
            break;
        }
    }

    private void noDataPlay() {
        if (mTaskChain != null) {
            new Handler(Looper.getMainLooper()).post(() -> {
                mTaskChain.updateConditions(KEY_HISTORY, true);
            });
        }
    }


    private void playHistoryTest(HistoryItem historyItem) {
        YTLogUtil.logStart(TAG, "playHistory", "");
        PlayerManagerHelper.getInstance().startHistory(historyItem);
    }

    private void playHistory(HistoryItem historyItem) {
        YTLogUtil.logStart(TAG, "playHistory", "");
        if (StringUtil.equals(String.valueOf(PlayerConstants.RESOURCES_TYPE_BROADCAST), historyItem.getType())
                || StringUtil.equals(String.valueOf(PlayerConstants.RESOURCES_TYPE_TV), historyItem.getType())) {
            long currentId = Long.parseLong(historyItem.getRadioId());
            boolean isLocal = false;
            //先看是否本地有缓存
            YTLogUtil.logStart(TAG, "playHistory", "broadcastId:" + currentId);
            YTDataCache.obtainHistoryLocalBroadcastList(HistoryManager.getInstance()).thenAccept(localBroadcastList -> {
//                new Handler(Looper.getMainLooper()).post(() -> {
                    YTLogUtil.logStart(TAG, "playHistory", "localBroadcastList:" + localBroadcastList);
                    if (localBroadcastList != null && !localBroadcastList.isEmpty()) {
                        // 处理获取到的广播列表
                        boolean result = doNextAfterBroadList(localBroadcastList, currentId, isLocal, historyItem);
                        if (!result) {
                            PlayerManagerHelper.getInstance().startHistory(historyItem);
                        }
                    } else {
                        // 处理返回 null 的情况
                        PlayerManagerHelper.getInstance().startHistory(historyItem);
                    }
//                });
            });
//            localBroadcastListDisposable = HistoryManager.getInstance().getLocalBroadcastList()
//                    .subscribe(broadcastList -> {
//                        YTLogUtil.logStart(TAG, "playHistory", "broadcastList:");
//                        if (broadcastList != null) {
//                            // 处理获取到的广播列表
//                            boolean result = doNextAfterBroadList(broadcastList, currentId, isLocal, historyItem);
//                            if (!result) {
//                                PlayerManagerHelper.getInstance().startHistory(historyItem);
//                            }
//                        } else {
//                            // 处理返回 null 的情况
//                            PlayerManagerHelper.getInstance().startHistory(historyItem);
//                        }
//                    }, throwable -> {
//                        // 处理异常
//                        YTLogUtil.logStart(TAG, "playHistory", "Error:" + throwable.toString());
//                        PlayerManagerHelper.getInstance().startHistory(historyItem);
//                    });
        } else {
            PlayerManagerHelper.getInstance().startHistory(historyItem);
        }
    }

    private boolean doNextAfterBroadList(List<BroadcastRadioSimpleData> list, long currentId, boolean isLocal, HistoryItem historyItem) {
        boolean result = false;
        if (list != null && !list.isEmpty() && list.size() > 1) {
            for (int i = 0; i < list.size(); i++) {
                if (list.get(i).getBroadcastId() == currentId) {
                    PlayerManagerHelper.getInstance().addBroadcastRadioSimpleItems((ArrayList<BroadcastRadioSimpleData>) list);
                    isLocal = true;
                    break;
                }
            }
        }
        if (isLocal) {
            PlayerManagerHelper.getInstance().calculateCurrentFrequencyPosition(currentId);
        } else if (String.valueOf(PlayerConstants.RESOURCES_TYPE_BROADCAST).equals(historyItem.getType())) {
            //获取地区列表进行播放,广播
            getBroadcastNeighborListToPlay(historyItem);
            result = true;
        } else {
            //听电视
            getTvNeighborListToPlay(historyItem);
            result = true;
        }
        return result;
    }

    private void getBroadcastNeighborListToPlay(HistoryItem historyItem) {
        new BroadcastRequest().getBroadcastNeighborList(Long.parseLong(historyItem.getRadioId()),
                1, 100, new HttpCallback<BasePageResult<List<BroadcastDetails>>>() {
                    @Override
                    public void onSuccess(BasePageResult<List<BroadcastDetails>> listBasePageResult) {
                        ArrayList<BroadcastRadioSimpleData> list = itemBroadcastBean2Simple(listBasePageResult.getDataList());
                        int index = -1;
                        for (int i = 0; i < list.size(); i++) {
                            if (TextUtils.equals(historyItem.getRadioId(), String.valueOf(list.get(i).getBroadcastId()))) {
                                index = i;
                                break;
                            }
                        }
                        if (index >= 0) {
                            PlayerManagerHelper.getInstance().addBroadcastRadioSimpleItems(list);
                            PlayerManagerHelper.getInstance().setCurrentFrequencyPosition(index);
                        }
                        PlayerManagerHelper.getInstance().startHistory(historyItem);
                    }

                    @Override
                    public void onError(ApiException e) {
                        PlayerLogUtil.log(getClass().getSimpleName(), "getBroadcastNeighborListToPlay err " + e.toString());
                        PlayerManagerHelper.getInstance().startHistory(historyItem);
                    }
                });
    }

    private void getTvNeighborListToPlay(HistoryItem historyItem) {
        new TVRequest().getTVNeighborList(new HttpCallback<List<TVDetails>>() {
            @Override
            public void onSuccess(List<TVDetails> listBasePageResult) {
                ArrayList<BroadcastRadioSimpleData> list = itemTvBean2Simple(listBasePageResult);
                int index = -1;
                for (int i = 0; i < list.size(); i++) {
                    if (TextUtils.equals(historyItem.getRadioId(), String.valueOf(list.get(i).getBroadcastId()))) {
                        index = i;
                        break;
                    }
                }
                if (index >= 0) {
                    PlayerManagerHelper.getInstance().addBroadcastRadioSimpleItems(list);
                    PlayerManagerHelper.getInstance().setCurrentFrequencyPosition(index);
                }
                PlayerManagerHelper.getInstance().startHistory(historyItem);
            }

            @Override
            public void onError(ApiException e) {
                PlayerLogUtil.log(getClass().getSimpleName(), "getBroadcastNeighborListToPlay err " + e.toString());
                PlayerManagerHelper.getInstance().startHistory(historyItem);
            }
        });
    }

    private ArrayList<BroadcastRadioSimpleData> itemBroadcastBean2Simple(List<BroadcastDetails> dataList) {
        if (dataList == null) {
            return null;
        }
        ArrayList<BroadcastRadioSimpleData> broadcastRadioSimpleDataList = new ArrayList<>();
        for (int i = 0, size = dataList.size(); i < size; i++) {
            BroadcastDetails broadcastRadioDetailData = dataList.get(i);
            BroadcastRadioSimpleData simpleData = new BroadcastRadioSimpleData();
            simpleData.setBroadcastId(broadcastRadioDetailData.getBroadcastId());
            String freq = broadcastRadioDetailData.getFreq();
            freq = TextUtils.isEmpty(freq) ? "" : freq;
            simpleData.setName(broadcastRadioDetailData.getName() + "  " + freq);
            simpleData.setImg(broadcastRadioDetailData.getImg());
            broadcastRadioSimpleDataList.add(simpleData);
        }
        return broadcastRadioSimpleDataList;
    }

    private ArrayList<BroadcastRadioSimpleData> itemTvBean2Simple(List<TVDetails> dataList) {
        if (dataList == null) {
            return null;
        }
        ArrayList<BroadcastRadioSimpleData> broadcastRadioSimpleDataList = new ArrayList<>();
        for (int i = 0, size = dataList.size(); i < size; i++) {
            TVDetails tvDetailData = dataList.get(i);
            BroadcastRadioSimpleData simpleData = new BroadcastRadioSimpleData();
            simpleData.setBroadcastId(tvDetailData.getListenTVid());
            simpleData.setName(tvDetailData.getName());
            simpleData.setImg(tvDetailData.getImg());
            simpleData.setResType(ResType.TYPE_TV);
            broadcastRadioSimpleDataList.add(simpleData);
        }
        return broadcastRadioSimpleDataList;
    }

    /**
     * 播放首页的第一个节目
     */
    private void playFirstItem() {
        if (mFirstHomeCell == null) {
            return;
        }
        for (int i = 0; i < mFirstHomeCell.getContentList().size(); i++) {
            if (!(mFirstHomeCell.getContentList().get(i) instanceof WebViewColumnMember)
                    && !(mFirstHomeCell.getContentList().get(i) instanceof ActivityDetailColumnMember)
                    && !(mFirstHomeCell.getContentList().get(i) instanceof PageRedirectionColumnMember)) {
                PlayerManagerHelper.getInstance()
                        .start(String.valueOf(mFirstHomeCell.getContentList().get(i).getId())
                                , mFirstHomeCell.getContentList().get(i).getResType());
                break;
            }
        }
    }

    /**
     * 语音控制--播放首页的第一个条目
     *
     * @param callback
     */
    public void playFirstItemWithCallBack(PlayFirstItemCallback callback) {
        if (mFirstHomeCell == null) {
            callback.failed();
        } else {
            PlayerManagerHelper.getInstance().start(String.valueOf(mFirstHomeCell.playId), mFirstHomeCell.resType);
            callback.success();
        }
    }

    public interface PlayFirstItemCallback {

        void success();

        void failed();

    }

    public void updateFirstHomeCell(List<ColumnGrp> columnGrps) {
        mFirstHomeCell = HomeDataUtil.getFirstCell(columnGrps);
        if (mTaskChain != null && mTaskChain.mTasks.get(KEY_DATA) != null
                && Boolean.FALSE.equals(mTaskChain.mTasks.get(KEY_DATA))) {
            mTaskChain.updateConditions(KEY_DATA, true);
        }
    }


    public void request(String zone, HttpCallback<List<ColumnGrp>> httpCallback) {
        new OperationRequest().getColumnTree(true, zone, new HttpCallback<List<ColumnGrp>>() {
            @Override
            public void onSuccess(List<ColumnGrp> columnGrps) {
                updateFirstHomeCell(columnGrps);
                if (httpCallback != null) httpCallback.onSuccess(columnGrps);
            }

            @Override
            public void onError(ApiException e) {
                if (httpCallback != null) httpCallback.onError(e);
            }
        });
    }

    public void request(HttpCallback<List<ColumnGrp>> httpCallback) {
        if (mColumnGrpList != null) {
            if (httpCallback != null) {
                httpCallback.onSuccess(mColumnGrpList);
            }
        } else {
            if (mSocketListener == null) {
                mSocketListener = new SocketListener<List<ColumnGrp>>() {

                    @Override
                    public String getEvent() {
                        return SocketEvent.HOME_PAGE_REFRESH;
                    }

                    @Override
                    public Map<String, Object> getParams(Map<String, Object> params) {
                        // zone区域只有基线版本需要传此参数，各渠道可以传递自己的zone参数,
                        // 若首页无内容显示，可将"needDefaultZonePage"返回false
                        KradioZoneInter zoneInter = ClazzImplUtil.getInter("KradioZoneInterImpl");
                        boolean needDefaultZonePage = true;
                        if (zoneInter != null) {
                            needDefaultZonePage = zoneInter.needDefaultZonePage();
                            zoneInter.setParams(params);
                        }

                        //zone
                        if (needDefaultZonePage) {
                            String zone = "mainPage";
                            params.put("zone", zone);
                        }

                        //经纬度:默认北京
                        String lng = KaolaAppConfigData.getInstance().getLng();
                        String lat = KaolaAppConfigData.getInstance().getLat();
                        if (TextUtils.isEmpty(lat) || TextUtils.isEmpty(lng)) {
                            lng = "";
                            lat = "";
                        }

                        params.put("lng", lng);
                        params.put("lat", lat);
                        params.put("parentCode", "0");
                        params.put("isRecursive", "1");
                        return params;
                    }

                    @Override
                    public boolean isNeedParams() {
                        return true;
                    }

                    @Override
                    public boolean isNeedRequest() {
                        return true;
                    }

                    @Override
                    public void onSuccess(List<ColumnGrp> columnGrps) {
                        Log.i("RingViewDataManager", "trouble shoot, enter method mModel.SocketListener.onSuccess, begin convert Json");
                        mColumnGrpList = columnGrps;
                        mFirstHomeCell = HomeDataUtil.getFirstCell(columnGrps);
                        Log.i("RingViewDataManager", "trouble shoot, enter method mModel.SocketListener.onSuccess, end convert Json");
                        if (httpCallback != null) {
                            if (mColumnGrpList != null) {
                                httpCallback.onSuccess(mColumnGrpList);
                            } else {
                                httpCallback.onError(new ApiException("数据为空"));
                            }
                        }
                        if (mTaskChain != null) {
                            mTaskChain.updateConditions(KEY_DATA, true);
                        }
                    }

                    @Override
                    public void onError(ApiException e) {
                        if (httpCallback != null) {
                            httpCallback.onError(e);
                        }
                    }
                };
            }
            SocketManager.getInstance().setMap(CommonRequestParamsUtil.getCommonParams()).setSocketHost(SocketApiConstants.SOCKET_HOST).request(mSocketListener);
        }
    }

    public boolean isEmptyHistoryAndEmptyData() {
        if (mTaskChain == null || mTaskChain.mTasks.isEmpty()) {
            return true;
        }
        boolean isNoHistory = Boolean.TRUE.equals(mTaskChain.mTasks.get(KEY_HISTORY));
        boolean isNoData = Boolean.FALSE.equals(mTaskChain.mTasks.get(KEY_DATA));
        Log.i("HomeDataManager", "isAllEmpty: " + isNoHistory + " " + isNoData);
        return isNoHistory && isNoData;
    }

    public void getBroadcastProgramList(long broadcastId, String data, HttpCallback<List<ProgramDetails>> callback) {
        mBroadcastRequest.getBroadcastProgramList(broadcastId, data, new HttpCallback<List<ProgramDetails>>() {
            @Override
            public void onSuccess(List<ProgramDetails> list) {
                callback.onSuccess(list);
            }

            @Override
            public void onError(ApiException e) {
                callback.onError(e);
            }
        });
    }

    public void schedulProgramListRequest(int delay, HttpCallback<List<ProgramDetails>> callback) {
        // 添加null检查，避免空指针异常，但保持原始逻辑
        if (mTime == null) {
            Log.w(TAG, "Timer is null, cannot schedule program list request. Timer may have been cancelled.");
            return;
        }

        TimerTask mTimeTask = new TimerTask() {
            @Override
            public void run() {
                Log.i("broadcastDataList:", broadcastDataList.size() + "");
                if (!broadcastDataList.isEmpty()) {
                    Long broadcastId = broadcastDataList.poll();
                    if (broadcastId != null) {
                        getBroadcastProgramList(broadcastId, null, callback);
                    }
                }
            }
        };

        try {
            mTime.schedule(mTimeTask, delay * 1000L, 1000);
        } catch (IllegalStateException e) {
            // Timer已被取消，记录日志但不重新创建，保持原始逻辑
            Log.w(TAG, "Timer was cancelled, cannot schedule task", e);
        } catch (Exception e) {
            Log.e(TAG, "Error scheduling timer task", e);
        }
    }

    public void addBroadcastList(List<HomeCell> cells) {
        if (cells != null && !cells.isEmpty()) {
            for (HomeCell cell : cells) {
                if (cell.contentList != null) {
                    for (int i = 0; i < cell.contentList.size(); i++) {
                        if (cell.contentList.get(i) instanceof BroadcastDetailColumnMember) {
                            broadcastDataList.add(Long.parseLong(cell.contentList.get(i).getId()));
                        }
                    }
                }

            }
        }
    }

    public void cancelSchedule() {
        // 保持原始逻辑，但添加异常处理
        if (mTime != null) {
            try {
                mTime.cancel();
                mTime = null;
                Log.i(TAG, "Timer cancelled successfully");
            } catch (Exception e) {
                Log.w(TAG, "Error cancelling timer", e);
                mTime = null; // 即使出错也设置为null，保持原始逻辑
            }
        }

        if (networkReadyListener != null) {
            NetworkManager.getInstance().removeNetworkReadyListener(networkReadyListener);
            networkReadyListener = null;
        }
        if (initCompleteListener != null) {
            PlayerManager.getInstance().removePlayerInitComplete(initCompleteListener);
            initCompleteListener = null;
        }
        if (localBroadcastListDisposable != null && !localBroadcastListDisposable.isDisposed()) {
            localBroadcastListDisposable.dispose();
        }
    }
}
