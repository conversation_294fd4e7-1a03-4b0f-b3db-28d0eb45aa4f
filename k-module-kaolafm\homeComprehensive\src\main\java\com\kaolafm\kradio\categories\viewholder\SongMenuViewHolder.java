package com.kaolafm.kradio.categories.viewholder;

import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.bean.SubcategoryItemBean;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;

/**
 * 歌单，专辑等带有播放量的item
 * <AUTHOR>
 * @date 2018/4/25
 */

public class SongMenuViewHolder extends BaseSubcategoryViewHolder {


    ImageView mIvSubcategorySongmenuCover;
    TextView mTvSubcategorySongmenuListenNum;
    TextView mTvSubcategorySongmenuName;
    LinearLayout mLinearLayout;

    public SongMenuViewHolder(View itemView) {
        super(itemView);
        mIvSubcategorySongmenuCover=itemView.findViewById(R.id.iv_subcategory_songmenu_cover);
        mTvSubcategorySongmenuListenNum=itemView.findViewById(R.id.tv_subcategory_songmenu_listen_num);
        mTvSubcategorySongmenuName=itemView.findViewById(R.id.tv_subcategory_songmenu_name);
        mLinearLayout=itemView.findViewById(R.id.ll_songmenu_root);
    }

    @Override
    public void setupData(SubcategoryItemBean subcategoryItemBean, int position) {
        super.setupData(subcategoryItemBean, position);
        ImageLoader.getInstance()
                .displayImage(itemView.getContext(), subcategoryItemBean.getCoverUrl(), mIvSubcategorySongmenuCover);
        mTvSubcategorySongmenuListenNum.setText(StringUtil.formatNum(subcategoryItemBean.getListenNum()));
        mTvSubcategorySongmenuName.setText(subcategoryItemBean.getName());
    }

}
