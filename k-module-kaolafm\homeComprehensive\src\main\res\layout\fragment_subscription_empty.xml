<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_centerInParent="true"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:paddingTop="@dimen/sub_padding_top"
    tools:background="@drawable/bg_home">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:text="@string/no_login_sub_str"
        android:textColor="@color/text_color_7"
        android:textSize="@dimen/text_size4" />

    <Button
        android:id="@+id/btn_login"
        style="?android:attr/borderlessButtonStyle"
        android:layout_width="@dimen/x370"
        android:layout_height="@dimen/y64"
        android:layout_marginTop="@dimen/y22"
        android:background="@drawable/bg_login_btn"
        android:gravity="center"
        android:text="@string/no_login_buttom_str"
        android:textColor="@color/text_color_7"
        android:textSize="@dimen/text_size5" />

</LinearLayout>