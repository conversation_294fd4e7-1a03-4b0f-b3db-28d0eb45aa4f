package com.kaolafm.kradio.user.channel;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;
import android.view.View;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.mvp.IPresenter;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.lib.sdk.utils.PerformanceSettingMananger;


/**
 * <pre>
 *     <AUTHOR> Wenchl
 *     e-mail : <EMAIL>
 *     time   : 2019/02/18
 *     desc   :
 *     version: 1.0
 * </pre>
 */

public class AnimationSettingFragment extends BaseFragment {
    SwitchCompat animationSwitch;

    @Override
    protected int getLayoutId() {
        return R.layout.user_fragment_animation_setting;
    }

    @Override
    protected int getLayoutId_Tow() {
        return 0;
    }

    @Override
    protected IPresenter createPresenter() {
        return null;
    }

    @Override
    public void initView(View view) {
        animationSwitch=view.findViewById(R.id.animation_switch);
        boolean needAnimation = PerformanceSettingMananger.getInstance().getIsNeedAnimation();
        animationSwitch.setChecked(needAnimation);
        animationSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> PerformanceSettingMananger.getInstance().setIsNeedAnimation(isChecked));
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
    }
}
