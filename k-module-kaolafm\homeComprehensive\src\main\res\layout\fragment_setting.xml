<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/setting_root_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_home"
    android:scrollbars="none">

    <ImageView
        android:id="@+id/setting_back"
        android:layout_width="@dimen/m36"
        android:layout_height="@dimen/m36"
        android:layout_marginTop="0dp"
        android:layout_marginStart="@dimen/m70"
        android:background="@color/transparent"
        android:scaleType="centerInside"
        android:src="@drawable/player_ic_back"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_activity_title"
        app:layout_constraintBottom_toBottomOf="@+id/tv_activity_title"/>

    <com.kaolafm.kradio.component.ui.base.view.KradioTextView
        android:id="@+id/tv_activity_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y50"
        android:gravity="center"
        android:text="@string/user_setting"
        android:textColor="@color/text_color_7"
        android:textSize="@dimen/m30"
        app:kt_font_weight="0.3"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/deliver"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="@dimen/m34"
        android:background="@color/activity_line_bg"
        app:layout_constraintTop_toBottomOf="@+id/setting_back"
        tools:ignore="MissingConstraints" />

    <ScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/deliver">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingLeft="@dimen/m80"
            android:paddingTop="@dimen/m62"
            android:paddingRight="@dimen/m80"
            android:paddingBottom="@dimen/m50">

            <androidx.viewpager.widget.ViewPager
                android:id="@+id/view_page"
                android:layout_width="0dp"
                android:layout_height="0dp" />

            <androidx.viewpager.widget.ViewPager
                android:id="@+id/view_page2"
                android:layout_width="0dp"
                android:layout_height="0dp" />


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:visibility="gone">

                <LinearLayout
                    android:id="@+id/skinTitleView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="top"
                    android:layout_weight="1"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="@dimen/m28"
                        android:layout_height="@dimen/m28"
                        android:layout_gravity="center_vertical"
                        android:src="@drawable/setting_theme_icon" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/m10"
                        android:text="@string/setting_theme_text"
                        android:textColor="@color/setting_title_text_color"
                        android:textSize="@dimen/m26" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/setting_theme_ll"
                    android:layout_width="@dimen/m430"
                    android:layout_height="@dimen/m60"
                    android:layout_gravity="end"
                    android:background="@drawable/tab_setting_bg"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:id="@+id/setting_theme_a_ll"
                        android:layout_width="@dimen/m208"
                        android:layout_height="@dimen/m56"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:contentDescription="@string/setting_theme_text3"
                        tools:background="@drawable/tab_setting_indicator_bg">

                        <TextView
                            android:id="@+id/setting_theme_a_tv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/setting_theme_text3"
                            android:textColor="@color/setting_tab_text_color"
                            android:textSize="@dimen/m24" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/setting_theme_b_ll"
                        android:layout_width="@dimen/m208"
                        android:layout_height="@dimen/m56"
                        android:layout_marginStart="@dimen/m6"
                        android:contentDescription="@string/setting_theme_text4"
                        android:gravity="center"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/setting_theme_b_tv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/setting_theme_text4"
                            android:textColor="@color/setting_tab_text_color_not"
                            android:textSize="@dimen/m24" />
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/m40"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="@dimen/m28"
                    android:layout_height="@dimen/m28"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/setting_message_icon" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/m10"
                    android:text="@string/setting_message_text"
                    android:textColor="@color/setting_title_text_color"
                    android:textSize="@dimen/m26" />
            </LinearLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/m20"
                android:paddingLeft="@dimen/m40">

                <TextView
                    android:id="@+id/rl_a_tv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:text="@string/setting_message_play_text"
                    android:textColor="@color/setting_msg_text_color"
                    android:textSize="@dimen/m24" />


                <Switch
                    android:id="@+id/setting_msg_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_alignParentRight="true"
                    android:layout_marginRight="6dp"
                    android:contentDescription="@string/setting_message_play_text"
                    android:focusable="true"
                    android:thumb="@drawable/setting_switch_thumb" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/m22"
                android:layout_marginRight="@dimen/m170"
                android:visibility="gone">

                <TextView
                    android:id="@+id/rl_b_tv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/setting_message_helper_text"
                    android:textColor="@color/setting_msg_text_color"
                    android:textSize="@dimen/m24" />

                <TextView
                    android:id="@+id/rl_b_tips_tv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/rl_b_tv"
                    android:text="@string/setting_message_helper_tips_text"
                    android:textColor="@color/setting_msg_tips_text_color"
                    android:textSize="@dimen/m20" />

                <Switch
                    android:id="@+id/setting_msg_helper_switch"
                    android:layout_width="@dimen/m80"
                    android:layout_height="@dimen/m40"
                    android:layout_alignParentRight="true"
                    android:thumb="@drawable/setting_switch_thumb"
                    android:track="@drawable/setting_switch_selector" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/m22"
                android:layout_marginRight="@dimen/m170"
                android:visibility="gone">

                <TextView
                    android:id="@+id/rl_c_tv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/setting_message_travel_text"
                    android:textColor="@color/setting_msg_text_color"
                    android:textSize="@dimen/m24" />

                <TextView
                    android:id="@+id/rl_c_tips_tv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/rl_c_tv"
                    android:text="@string/setting_message_travel_tips_text"
                    android:textColor="@color/setting_msg_tips_text_color"
                    android:textSize="@dimen/m20" />

                <Switch
                    android:id="@+id/setting_msg_travel_switch"
                    android:layout_width="@dimen/m80"
                    android:layout_height="@dimen/m40"
                    android:layout_alignParentRight="true"
                    android:thumb="@drawable/setting_switch_thumb"
                    android:track="@drawable/setting_switch_selector" />
            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/m40"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/toneQualityTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"

                    android:orientation="horizontal"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <ImageView
                        android:layout_width="@dimen/m28"
                        android:layout_height="@dimen/m28"
                        android:layout_gravity="center_vertical"
                        android:src="@drawable/setting_acoustics_icon" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/m10"
                        android:text="@string/setting_acoustics_text"
                        android:textColor="@color/setting_title_text_color"
                        android:textSize="@dimen/m26" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/soundQualitiesParentView"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/m74"
                    android:background="@drawable/tab_setting_bg"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:visibility="visible">

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/canSeeCanSayPanel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/m40"
                android:contentDescription="@string/person_center_aboutus_str"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <ImageView
                    android:layout_width="@dimen/m28"
                    android:layout_height="@dimen/m28"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/settings_can_see_can_say" />

                <TextView
                    android:id="@+id/canSeeCanSayTv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/m10"
                    android:layout_weight="1"
                    android:text="@string/person_center_can_see_can_say_str"
                    android:textColor="@color/setting_title_text_color"
                    android:textSize="@dimen/m26" />

                <ImageView
                    android:layout_width="@dimen/m28"
                    android:layout_height="@dimen/m28"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/message_arrow_right" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/aboutusTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/m40"
                android:contentDescription="@string/person_center_aboutus_str"
                android:orientation="horizontal"
                android:visibility="visible">

                <ImageView
                    android:layout_width="@dimen/m28"
                    android:layout_height="@dimen/m28"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/settings_aboutus" />

                <TextView
                    android:id="@+id/aboutusTv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/m10"
                    android:layout_weight="1"
                    android:text="@string/person_center_aboutus_str"
                    android:textColor="@color/setting_title_text_color"
                    android:textSize="@dimen/m26" />

                <ImageView
                    android:layout_width="@dimen/m28"
                    android:layout_height="@dimen/m28"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/message_arrow_right" />
            </LinearLayout>
            <LinearLayout
                android:id="@+id/permissionTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/m50"
                android:contentDescription="@string/person_center_permission_str"
                android:orientation="horizontal"
                android:visibility="visible">

                <ImageView
                    android:layout_width="@dimen/m28"
                    android:layout_height="@dimen/m28"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/settings_permission" />

                <TextView
                    android:id="@+id/userClauseTv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/m10"
                    android:layout_weight="1"
                    android:text="@string/person_center_permission_str"
                    android:textColor="@color/setting_title_text_color"
                    android:textSize="@dimen/m26" />

                <ImageView
                    android:layout_width="@dimen/m28"
                    android:layout_height="@dimen/m28"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/message_arrow_right" />
            </LinearLayout>


            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/m49"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="@dimen/m28"
                    android:layout_height="@dimen/m28"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/setting_back_msg_icon" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/m10"
                    android:text="@string/setting_back_msg_text"
                    android:textColor="@color/setting_title_text_color"
                    android:textSize="@dimen/m26" />
            </LinearLayout>


            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/m25"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/qcode_iv"
                    android:layout_width="@dimen/m120"
                    android:layout_height="@dimen/m120"
                    android:src="@drawable/settong_msg_code_pic" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:layout_marginLeft="@dimen/m23"
                    android:lineSpacingExtra="@dimen/m7"
                    android:text="@string/setting_back_msg_text2"
                    android:textColor="@color/setting_title_text_color"
                    android:textSize="@dimen/m22" />
            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/aop_rv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
        </LinearLayout>

    </ScrollView>

    <FrameLayout
        android:id="@+id/fragment_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <!-- 隐藏控件，用于所见即可说语音执行滑动操作 -->
    <TextView
        style="@style/ContentDescriptionScrollUp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/scrollView" />

    <TextView
        style="@style/ContentDescriptionScrollDown"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/scrollView" />
</androidx.constraintlayout.widget.ConstraintLayout>