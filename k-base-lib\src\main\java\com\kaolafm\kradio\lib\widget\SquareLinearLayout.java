package com.kaolafm.kradio.lib.widget;

import android.content.Context;
import android.content.res.TypedArray;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.LinearLayout;
import com.kaolafm.kradio.lib.R;
import com.kaolafm.kradio.lib.utils.AnimUtil;

/**
 * 正方形的LinearLayout。根据宽度设置高度的方式呈现正方形
 * <AUTHOR>
 * @date 2018/5/13
 */

public class SquareLinearLayout extends LinearLayout {
    /**
     * 是否有点击播放效果，默认true有。
     */
    private boolean canScale;

    public SquareLinearLayout(@NonNull Context context) {
        this(context, null);
    }

    public SquareLinearLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SquareLinearLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    private void init(Context context, AttributeSet attrs) {
        TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.SquareLinearLayout);
        canScale = ta.getBoolean(R.styleable.SquareLinearLayout_canScale, true);
        ta.recycle();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        setMeasuredDimension(getDefaultSize(0, widthMeasureSpec), getDefaultSize(0, heightMeasureSpec));
        int measuredWidth = getMeasuredWidth();
        //高度和宽度一样
        heightMeasureSpec = widthMeasureSpec = MeasureSpec.makeMeasureSpec(measuredWidth, MeasureSpec.EXACTLY);
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (canScale) {
            int action = event.getAction();
            switch (action) {
                case MotionEvent.ACTION_DOWN:
                    AnimUtil.startScalePress(this);
                    break;
                case MotionEvent.ACTION_OUTSIDE:
                case MotionEvent.ACTION_CANCEL:
                case MotionEvent.ACTION_UP:
                    AnimUtil.startScaleRelease(this);
                    break;
                default:
                    break;
            }
        }
        return super.onTouchEvent(event);
    }
}
