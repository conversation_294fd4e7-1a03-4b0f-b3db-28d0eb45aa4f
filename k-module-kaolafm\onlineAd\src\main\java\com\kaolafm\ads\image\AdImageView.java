package com.kaolafm.ads.image;

import android.content.Context;
import androidx.annotation.Nullable;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.style.ForegroundColorSpan;
import android.util.AttributeSet;
import android.widget.ImageView;

import com.kaolafm.ads.image.base.BaseAdImageView;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.ResUtil;

import io.reactivex.Observable;
import io.reactivex.Observer;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Function;

import java.lang.ref.WeakReference;
import java.util.concurrent.TimeUnit;

public class AdImageView extends BaseAdImageView {

    private int mSkipTime;
    private boolean mIsJump;

    private Disposable mSkipDisposable;

    public AdImageView(Context context) {
        super(context);
    }

    public AdImageView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public AdImageView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        cancelSkip();
        cancelClose();
    }

    @Override
    public void loadAdContent(AdContentInfo adContentInfo) {
        super.loadAdContent(adContentInfo);
        mSkipTime = adContentInfo.getSkipTime();
        mIsJump = adContentInfo.isJump();
        if (adContentInfo.isPreload()) {
            setSkipTimeView();
        }
    }

    @Override
    protected AdImageLoaderListener getListener() {
        return new AdOnImageLoaderListener(this);
    }

    public void setSkipTimeView() {
        if (!mIsJump) {
            mTvSkip.setVisibility(GONE);
            return;
        }
        if (mSkipTime <= 0) {
            mTvSkip.setText("跳过广告");
            mTvSkip.setOnClickListener((v) -> {
                mTvSkip.setOnClickListener(null);
                cancelClose();
                mAdImageListener.onAdSkip();
            });
            return;
        }
        Observable.interval(0, 1, TimeUnit.SECONDS)
                .take(mSkipTime + 1)
                .onTerminateDetach()
                .map(new Function<Long, Long>() {
                    @Override
                    public Long apply(Long aLong) throws Exception {
                        return mSkipTime - aLong;
                    }
                })
                .observeOn(AndroidSchedulers.mainThread())
                .onTerminateDetach()
                .subscribe(new Observer<Long>() {
                    @Override
                    public void onSubscribe(Disposable d) {
                        mSkipDisposable = d;
                    }

                    @Override
                    public void onNext(Long aLong) {
                        String text = aLong + " s后可跳过广告";
                        SpannableString ssb = new SpannableString(text);
                        ForegroundColorSpan span = new ForegroundColorSpan(ResUtil.getColor(R.color.online_ad_jump_color));
                        ssb.setSpan(span, 0, text.indexOf("s"), SpannableStringBuilder.SPAN_EXCLUSIVE_EXCLUSIVE);
                        mTvSkip.setText(ssb);
                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onComplete() {
                        mTvSkip.setText("跳过广告");
                        mTvSkip.setOnClickListener((v) -> {
                            mTvSkip.setOnClickListener(null);
                            cancelClose();
                            mAdImageListener.onAdSkip();
                        });
                        cancelSkip();
                    }
                });
    }

    private void cancelSkip() {
        if (mSkipDisposable != null && !mSkipDisposable.isDisposed()) {
            mSkipDisposable.dispose();
            mSkipDisposable = null;
        }
    }

    class AdOnImageLoaderListener extends AdImageLoaderListener {
        private WeakReference<AdImageView> mView;

        public AdOnImageLoaderListener(AdImageView view) {
            super(view);
            mView = new WeakReference<>(view);
        }

        @Override
        public void onLoadingFailed(String url, ImageView target, Exception exception) {
            mAdImageListener.onAdImageLoadFailed();
        }

        @Override
        public void onLoadingComplete(String url, ImageView target) {
            AdImageView view = mView.get();
            if (view != null) {
                view.setSkipTimeView();
            }
            super.onLoadingComplete(url, target);
        }
    }
}
