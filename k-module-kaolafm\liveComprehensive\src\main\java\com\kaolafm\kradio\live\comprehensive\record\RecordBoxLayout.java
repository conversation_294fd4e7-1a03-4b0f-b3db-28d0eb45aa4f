package com.kaolafm.kradio.live.comprehensive.record;

import android.animation.Animator;
import android.animation.Animator.AnimatorListener;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.PropertyValuesHolder;
import android.animation.TimeInterpolator;
import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.DecelerateInterpolator;
import android.view.animation.Interpolator;
import android.view.animation.LinearInterpolator;
import android.view.animation.RotateAnimation;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.kradio.common.widget.PlayAnimView;
import com.kaolafm.kradio.k_kaolafm.R.drawable;
import com.kaolafm.kradio.k_kaolafm.R.id;
import com.kaolafm.kradio.k_kaolafm.R.layout;
import com.kaolafm.kradio.k_kaolafm.R.string;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.live.player.HomeLiveManager;
import com.kaolafm.kradio.live.player.RecorderStatus;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.List;


public  class RecordBoxLayout extends ConstraintLayout {

    public RecordBoxLayout(@NotNull Context context) {
        this(context, null);
    }

    public RecordBoxLayout(@NotNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public RecordBoxLayout(@NotNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.loadingAnimation = new RotateAnimation(0.0F, 360.0F, 1, 0.5F, 1, 0.5F);
        this.loadingAnimation.setRepeatCount(Animation.INFINITE);
        this.loadingAnimation.setRepeatMode(Animation.RESTART);
        this.loadingAnimation.setInterpolator((Interpolator) (new LinearInterpolator()));
        this.loadingAnimation.setDuration(2000L);
        this.visiableViews = (List) (new ArrayList());
        String var10001 = ResUtil.getString(string.voice_message_click_finish);
        this.liveTipReordStr = var10001;
        var10001 = ResUtil.getString(string.voice_message_click_to_start);
        this.liveTipReordIdleStr = var10001;
        var10001 = ResUtil.getString(string.voice_message_send);
        this.liveTipReordSendStr = var10001;
        var10001 = ResUtil.getString(string.voice_message_sending);
        this.liveTipReordSendingStr = var10001;
        var10001 = ResUtil.getString(string.voice_message_send_success);
        this.liveTipReordSendSuccessStr = var10001;
        var10001 = ResUtil.getString(string.voice_message_resend);
        this.liveTipReordReSendStr = var10001;
        var10001 = ResUtil.getString(string.voice_message_please_login);
        this.liveTipReordUnloginStr = var10001;
        this.liveListenImg = drawable.voice_listen_comprehensive;
        this.liveListenIngImg = drawable.voice_button_empty_bg_comprehensive;
        this.liveRecordIngImg = drawable.voice_button_empty_bg_comprehensive;
        this.liveRecordMicImg = drawable.voice_record_mic_comprehensive;
        this.liveSendImg = drawable.voice_send_comprehensive;
        this.liveSendIngImg = drawable.voice_send_comprehensive;
        this.liveSendFinishImg = drawable.voice_send_finish_comprehensive;
        this.liveResendImg = drawable.voice_resend_comprehensive;
        this.recordButtonBoxBg = drawable.comprehensive_record_bg;
        LayoutInflater.from(this.getContext()).inflate(layout.record_button_popup_window_comprehensive, (ViewGroup) this);
        View var10 = this.findViewById(id.record_box);
        this.mRecordBox = (ConstraintLayout) var10;
        var10 = this.findViewById(id.micIv);
        this.mMicButton = (ImageButton) var10;
        var10 = this.findViewById(id.recordTipText);
        this.recordTipText = (TextView) var10;
        var10 = this.findViewById(id.live_listen_button_layout);
        this.mListenButton = (ViewGroup) var10;
        var10 = this.findViewById(id.live_record_anim_image);
        this.mRecordButtonAnim = (PlayAnimView) var10;
        var10 = this.findViewById(id.live_listen_anim_image);
        this.mListenButtonAnim = (PlayAnimView) var10;
        var10 = this.findViewById(id.live_send_anim);
        this.mSendButtonAnim = (ImageView) var10;
        var10 = this.findViewById(id.live_listen_message_image);
        this.mListenMessageImg = (ImageView) var10;
        var10 = this.findViewById(id.live_listen_message_text);
        this.mListenMessageText = (TextView) var10;
        var10 = this.findViewById(id.recordLayout);
        this.recordLayout = (ViewGroup) var10;
        var10 = this.findViewById(id.recordIv);
        this.recordIv = (ImageView) var10;
        var10 = this.findViewById(id.recordTextView);
        this.recordTextView = (TextView) var10;
        var10 = this.findViewById(id.live_cancel_button_layout);
        this.mCancelButton = (ViewGroup) var10;
        var10 = this.findViewById(id.live_cancel_message_text);
        this.mCancelTextView = (TextView) var10;
        this.mMeasureSpec = MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED);
        this.mChangeToIdleRunnable = (Runnable) (new Runnable() {
            @Override
            public void run() {
                mMicButton .setImageResource(RecordBoxLayout.this.liveRecordMicImg);
                mMicButton .setVisibility(View.VISIBLE);
                recordTipText.setText((CharSequence) RecordBoxLayout.this.liveTipReordIdleStr);
                recordLayout.setVisibility(View.INVISIBLE);
                HomeLiveManager.getInstance().setRecorderStatus(RecorderStatus.IDLE);
                if (mOnButtonExposedListener != null) {
                    mOnButtonExposedListener.onRecordButtonExposed(RecordBoxLayout.this.liveTipReordIdleStr);
                }

            }
        });
    }

    private final RotateAnimation loadingAnimation;

    private ConstraintLayout mRecordBox;
    private ImageButton mMicButton;
    private TextView recordTipText;
    private ViewGroup mListenButton;
    private PlayAnimView mRecordButtonAnim;
    private PlayAnimView mListenButtonAnim;
    private ImageView mSendButtonAnim;
    private ImageView mListenMessageImg;
    private TextView mListenMessageText;

    private ViewGroup recordLayout;
    private ImageView recordIv;
    private TextView recordTextView;
    private ViewGroup mCancelButton;
    private TextView mCancelTextView;

    private final List<View> visiableViews;
    private int mMeasureSpec;

    private String liveTipReordStr;
    private String liveTipReordIdleStr;
    private String liveTipReordSendStr;
    private String liveTipReordSendingStr;
    private String liveTipReordSendSuccessStr;
    private String liveTipReordReSendStr;
    private String liveTipReordUnloginStr;
    private int liveListenImg;
    private int liveListenIngImg;
    private int liveRecordIngImg;
    private int liveRecordMicImg;
    private int liveSendImg;
    private int liveSendIngImg;
    private int liveSendFinishImg;
    private int liveResendImg;
    private int recordButtonBoxBg;

    private final Runnable mChangeToIdleRunnable;
    private RecordBoxLayout.OnButtonExposedListener mOnButtonExposedListener;

    public final void setOnRecordButtonClickListener(@NotNull OnClickListener listener) {
        ImageButton var10000 = this.mMicButton;
        var10000.setOnClickListener(listener);
    }

    public final void setOnListenButtonClickListener(@NotNull OnClickListener listener) {
        ViewGroup var10000 = this.mListenButton;
        var10000.setOnClickListener(listener);
    }

    public final void setOnCancelButtonClickListener(@NotNull OnClickListener listener) {
        ViewGroup var10000 = this.mCancelButton;
        var10000.setOnClickListener(listener);
    }

    public final void setOnSendButtonClickListener(@NotNull OnClickListener listener) {
        ViewGroup var10000 = this.recordLayout;
        var10000.setOnClickListener(listener);
    }

    // 开始录音
    public final void notifyStartRecord() {
        this.mRecordButtonAnim.startAnimation();
        this.mRecordButtonAnim.setVisibility(View.VISIBLE);
        this.mMicButton.setVisibility(View.INVISIBLE);
        this.recordTipText.setText((CharSequence) this.liveTipReordStr);
        this.recordIv.setImageResource(this.liveRecordIngImg);
        this.recordLayout.setVisibility(View.VISIBLE);
        if (this.mOnButtonExposedListener != null) {
            this.mOnButtonExposedListener.onRecordingButtonExposed(this.liveTipReordStr);
        }
    }

    // 停止录音中动画
    public final void stopRecordingAnim(boolean isRecordFinish) {
        this.mRecordButtonAnim.setVisibility(View.INVISIBLE);
        this.mRecordButtonAnim.stopAnimation();
        this.mMicButton.setImageResource(this.liveRecordMicImg);
        if (isRecordFinish) {
            this.recordTipText.setText((CharSequence) this.liveTipReordSendStr);
            if (this.mOnButtonExposedListener != null) {
                this.mOnButtonExposedListener.onSendButtonExposed(this.liveTipReordSendStr);
            }
        } else {
            this.recordTipText.setText((CharSequence) this.liveTipReordIdleStr);
            if (this.mOnButtonExposedListener != null) {
                this.mOnButtonExposedListener.onRecordButtonExposed(this.liveTipReordIdleStr);
            }
        }

    }

    // 开始播放录制好的音频
    public final void startListenTimer() {
        this.mListenButtonAnim.startAnimation();
        this.mListenButtonAnim.setVisibility(View.VISIBLE);
        this.mListenMessageImg.setImageResource(this.liveListenIngImg);
        this.mListenMessageText.setText(string.voice_message_playing);
        RecordBoxLayout.OnButtonExposedListener var3 = this.mOnButtonExposedListener;
        if (this.mOnButtonExposedListener != null) {
            this.mOnButtonExposedListener.onListeningButtonExposed(this.mListenMessageText.getText().toString());
        }

    }

    // 结束播放录制好的音频
    public final void stopListenTimer() {
        mListenButtonAnim.setVisibility(View.INVISIBLE);
        mListenButtonAnim.stopAnimation();
        mListenMessageImg.setImageResource(this.liveListenImg);
        mListenMessageText.setText(string.voice_message_audition);
        RecordBoxLayout.OnButtonExposedListener var3 = this.mOnButtonExposedListener;
        if (var3 != null) {
            var3.onListenButtonExposed(this.mListenMessageText.getText().toString());
        }
    }

    public final void updateRecordText(@NotNull String string) {
        recordTextView.setText((CharSequence) string);
        if (StringUtil.isEmpty(string)) {
            recordIv.setImageResource(this.liveSendImg);
        }
    }

    public final void notifyUserLoginStatus(boolean isLogin) {
        recordTipText.setText(isLogin ? (CharSequence) this.liveTipReordIdleStr : (CharSequence) this.liveTipReordUnloginStr);
    }

    public final void hideRecordButton() {
        this.resetVisibilityViews();
        mListenButton.setVisibility(View.INVISIBLE);
        mCancelButton.setVisibility(View.INVISIBLE);
        mMicButton.setVisibility(View.INVISIBLE);
        recordTipText.setVisibility(View.INVISIBLE);
        recordLayout.setVisibility(View.INVISIBLE);
    }

    private void resetVisibilityViews() {
        this.visiableViews.clear();
        if (mListenButton.getVisibility() == View.VISIBLE) {
            visiableViews.add(mListenButton);
        }
        if (mCancelButton.getVisibility() == View.VISIBLE) {
            visiableViews.add(mCancelButton);
        }
        if (mMicButton.getVisibility() == View.VISIBLE) {
            visiableViews.add(mMicButton);
        }
        if (recordTipText.getVisibility() == View.VISIBLE) {
            visiableViews.add(recordTipText);
        }
        if (recordLayout.getVisibility() == View.VISIBLE) {
            visiableViews.add(recordLayout);
        }

    }

    public final void startRecordFinishAnim() {
        int recordWidth = mMicButton.getMeasuredWidth();
        int middle = mMicButton.getLeft() + recordWidth / 2;
        int cancelLeft = mCancelButton.getLeft();
        int listenLeft = mListenButton.getLeft();
        AnimatorSet animSet = new AnimatorSet();
        animSet.addListener((AnimatorListener) (new AnimatorListener() {
            @Override
            public void onAnimationStart(@NotNull Animator animation) {
                mCancelButton.setVisibility(View.VISIBLE);
                mListenButton.setVisibility(View.VISIBLE);
                if (mOnButtonExposedListener != null) {
                    mOnButtonExposedListener.onCancelButtonExposed(mCancelTextView.getText().toString());
                    mOnButtonExposedListener.onListenButtonExposed(mListenMessageText.getText().toString());
                }
            }

            @Override
            public void onAnimationEnd(@NotNull Animator animation) {
            }

            @Override
            public void onAnimationCancel(@NotNull Animator animation) {
            }

            @Override
            public void onAnimationRepeat(@NotNull Animator animation) {
            }
        }));
        int animDuration = 300;
        PropertyValuesHolder pvhr = PropertyValuesHolder.ofFloat("x", new float[]{(float) middle, (float) cancelLeft});
        PropertyValuesHolder pvhl = PropertyValuesHolder.ofFloat("x", new float[]{(float) middle, (float) listenLeft});
        PropertyValuesHolder pvha = PropertyValuesHolder.ofFloat("alpha", new float[]{0.0F, 1.0F});
        ObjectAnimator oali = ObjectAnimator.ofPropertyValuesHolder(mListenButton, new PropertyValuesHolder[]{pvhl});
        oali.setDuration((long) animDuration);
        ObjectAnimator oala = ObjectAnimator.ofPropertyValuesHolder(mListenButton, new PropertyValuesHolder[]{pvha});
        oala.setDuration((long) animDuration);
        ObjectAnimator oari = ObjectAnimator.ofPropertyValuesHolder(mCancelButton, new PropertyValuesHolder[]{pvhr});
        oari.setDuration((long) animDuration);
        ObjectAnimator  oara = ObjectAnimator.ofPropertyValuesHolder(mCancelButton, new PropertyValuesHolder[]{pvha});
        oara.setDuration((long) animDuration);
        animSet.playTogether(new Animator[]{(Animator) oali, (Animator) oari, (Animator) oala, (Animator) oara});
        animSet.setInterpolator((TimeInterpolator) (new DecelerateInterpolator()));
        animSet.start();
    }
    // 取消
    public final void notifyCancel() {
        mCancelButton.setVisibility(View.INVISIBLE);
        mMicButton.setImageResource(this.liveRecordMicImg);
        recordTipText.setText((CharSequence) this.liveTipReordIdleStr);
        recordTipText.setVisibility(View.VISIBLE);
        mListenButton.setVisibility(View.INVISIBLE);
        if (mOnButtonExposedListener != null) {
            mOnButtonExposedListener.onRecordButtonExposed(this.liveTipReordIdleStr);
        }
    }
    // 复位
    public final void showRecordIdle() {
        mCancelButton.setVisibility(View.INVISIBLE);
        mListenButton.setVisibility(View.INVISIBLE);
        mMicButton.setImageResource(this.liveRecordMicImg);
        mMicButton.setVisibility(View.VISIBLE);
        recordTipText.setText((CharSequence) this.liveTipReordIdleStr);
        recordTipText.setVisibility(View.VISIBLE);
        recordLayout.setVisibility(View.INVISIBLE);
        if (mOnButtonExposedListener != null) {
            mOnButtonExposedListener.onRecordButtonExposed(this.liveTipReordIdleStr);
        }

    }
    // 发送中
    public final void showRecordUploading() {
        mSendButtonAnim.setAnimation((Animation) this.loadingAnimation);
        mSendButtonAnim.setVisibility(View.VISIBLE);
        mCancelButton.setVisibility(View.INVISIBLE);
        mListenButton.setVisibility(View.INVISIBLE);
        recordTipText.setText((CharSequence) this.liveTipReordSendingStr);
        recordIv.setImageResource(this.liveSendIngImg);
        if (mOnButtonExposedListener != null) {
            mOnButtonExposedListener.onSendingButtonExposed(this.liveTipReordSendingStr);
        }

    }
    // 发送成功
    public final void showRecordUploaded() {
        if (mSendButtonAnim.getAnimation() != null) {
            mSendButtonAnim.getAnimation().cancel();
        }
        mSendButtonAnim.setVisibility(View.GONE);
        recordIv.setImageResource(this.liveSendFinishImg);
        recordTipText.setText((CharSequence) this.liveTipReordSendSuccessStr);
        mMicButton.removeCallbacks(this.mChangeToIdleRunnable);
        mMicButton.postDelayed(this.mChangeToIdleRunnable, 1000L);
        if (mOnButtonExposedListener != null) {
            mOnButtonExposedListener.onSendSucceedButtonExposed(this.liveTipReordSendSuccessStr);
        }

    }
    // 发送失败
    public final void showRecordUploadAgainAsFailure() {
        if (mSendButtonAnim.getAnimation() != null) {
            mSendButtonAnim.getAnimation().cancel();
        }
        mSendButtonAnim.setVisibility(View.GONE);
        mCancelButton.setVisibility(View.VISIBLE);
        mListenButton.setVisibility(View.VISIBLE);
        recordIv.setImageResource(this.liveResendImg);
        mMicButton.setVisibility(View.INVISIBLE);
        recordTipText.setText((CharSequence) this.liveTipReordReSendStr);
        recordLayout.setVisibility(View.VISIBLE);
        if (mOnButtonExposedListener != null) {
            mOnButtonExposedListener.onCancelButtonExposed(mCancelTextView.getText().toString());
            mOnButtonExposedListener.onListenButtonExposed(mListenMessageText.getText().toString());
            mOnButtonExposedListener.onSendButtonExposed(this.liveTipReordReSendStr);
        }
    }

    public final void setOnButtonExposedListener(@NotNull RecordBoxLayout.OnButtonExposedListener listener) {
        this.mOnButtonExposedListener = listener;
    }

    public interface OnButtonExposedListener {
        void onRecordButtonExposed(@NotNull String var1);

        void onCancelButtonExposed(@NotNull String var1);

        void onListenButtonExposed(@NotNull String var1);

        void onSendButtonExposed(@NotNull String var1);

        void onRecordingButtonExposed(@NotNull String var1);

        void onListeningButtonExposed(@NotNull String var1);

        void onSendingButtonExposed(@NotNull String var1);

        void onSendSucceedButtonExposed(@NotNull String var1);
    }
}
