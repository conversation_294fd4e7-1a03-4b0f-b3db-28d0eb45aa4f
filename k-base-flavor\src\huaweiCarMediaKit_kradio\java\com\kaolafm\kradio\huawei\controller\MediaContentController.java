package com.kaolafm.kradio.huawei.controller;


import android.graphics.Bitmap;
import android.view.View;

import com.huawei.carmediakit.bean.MediaBaseLocator;
import com.huawei.carmediakit.bean.OperResult;
import com.huawei.carmediakit.bean.UserInfo;
import com.huawei.carmediakit.callback.BasicCallback;
import com.huawei.carmediakit.constant.ErrorCode;
import com.huawei.carmediakit.controller.IMediaDataController;
import com.huawei.carmediakit.reporter.DialogReporter;
import com.huawei.carmediakit.reporter.UserSettingsReporter;
import com.kaolafm.kradio.huawei.utils.Constant;
import com.kaolafm.kradio.huawei.utils.DialogHelper;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;


public class MediaContentController implements IMediaDataController, IAccountLoginView {
    public static final String TAG = Constant.TAG;

    private AccountLoginPresenter loginPresenter;
    BasicCallback<OperResult> mBasicCallback;

    private AccountLoginPresenter getLoginPresenter() {
        if (loginPresenter == null) {
            loginPresenter = new AccountLoginPresenter(this);
        }

        return loginPresenter;
    }

    public void showNeedLoginDialog(Bitmap bitmap) {
        // 通知 CarMediaUI 弹框提示用户
        DialogReporter.reportShowDialog(DialogHelper.getNeedLoginDialog(bitmap));
    }

    @Override
    public void showQRCode(String url) {
        ImageLoader.getInstance().getBitmapFromCache(AppDelegate.getInstance().getContext(),
                url, this::showQrCallback);
    }

    private void showQrCallback(Bitmap bitmap) {
        Logger.i(TAG, "bitmap=" + bitmap);
        if (mBasicCallback != null) {
            showNeedLoginDialog(bitmap);
            mBasicCallback.callback(new OperResult(ErrorCode.SUCCESS, "success"),
                    ErrorCode.SUCCESS, "success");
            mBasicCallback = null;
        }
    }

    @Override
    public void showBindSuccess(String name, String avatar) {
        Logger.i(TAG, "avatar=" + avatar);
        UserInfo userInfo = new UserInfo();
        userInfo.setNickName(name);
        userInfo.setProfilePicUrl(avatar);
        userInfo.setOnline(true);
        UserSettingsReporter.reportUserInfo(userInfo);
        getLoginPresenter().cancelCheck();
        DialogReporter.reportCancelDialog("LOGIN_NOTICE");
    }

    @Override
    public void showNoNetWork() {

    }

    @Override
    public void showIntercepterView(View view) {

    }

    @Override
    public void asyncAddFavourite(MediaBaseLocator mediaBaseLocator, BasicCallback<OperResult> basicCallback) {
        Logger.i(TAG, "asyncAddFavourite=" + mediaBaseLocator);
        if (!UserInfoManager.getInstance().isUserBound()) {
            mBasicCallback = basicCallback;
            getLoginPresenter().getData();
            basicCallback.callback(new OperResult(ErrorCode.LOGIN_REQUIRED, ""), ErrorCode.LOGIN_REQUIRED, "");
            return;
        }
        long subscribeId = PlayerManagerHelper.getInstance().getSubscribeId();
        ClientImplController.getClientImpl().subscribe(subscribeId, basicCallback);
    }

    @Override
    public void asyncRemoveFavourite(MediaBaseLocator mediaBaseLocator, BasicCallback<OperResult> basicCallback) {
        Logger.i(TAG, "asyncRemoveFavourite=" + mediaBaseLocator);

        long subscribeId = PlayerManagerHelper.getInstance().getSubscribeId();
        ClientImplController.getClientImpl().unSubscribe(subscribeId, basicCallback);
    }
}
