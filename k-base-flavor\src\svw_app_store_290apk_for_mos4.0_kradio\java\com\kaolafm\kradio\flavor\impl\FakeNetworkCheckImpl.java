package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.util.Log;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

import com.kaolafm.kradio.common.utils.FlavorUtil;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.FakeNetworkCheckInter;
import com.kaolafm.launcher.scene.SceneManager;
import com.kaolafm.launcher.scene.SceneReceiver;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

public class FakeNetworkCheckImpl implements FakeNetworkCheckInter {
    @Override
    public boolean isInternetAvailable(Context context) {
        Log.d("FakeNetworkCheckImpl", "return=" + netStates);
        return newHttpPoll();
    }

    public FakeNetworkCheckImpl() {
    }

    public static boolean netStates = true;
    Call mcall;
    long starttime;

    public boolean newHttpPoll() {
//        大众渠道，如果需要咱自己实现网络监测，先试试原有的tcp连接阿里云dns。如果不行就得使用http://iovopen.radio.cn:443/generate（https://iovopen.radio.cn/generate）这个链接返回204
        Request request = new Request.Builder().url(FlavorUtil.getHttp443Url("https://iovopen.radio.cn/generate")).get().build();
        OkHttpClient client = new OkHttpClient.Builder().connectTimeout(5, TimeUnit.SECONDS).readTimeout(5, TimeUnit.SECONDS).build();
        if (mcall != null && System.currentTimeMillis() - starttime > 5000) {
            mcall.cancel();
            netStates = false;
            Log.d("FakeNetworkCheckImpl", "cancel=");
        } else if (mcall != null) {
            Log.d("FakeNetworkCheckImpl", "System.currentTimeMillis() - starttime < 5000");
            return netStates;
        }
        mcall = client.newCall(request);
        Log.d("FakeNetworkCheckImpl", "newCall=" + netStates);
        starttime = System.currentTimeMillis();
        try {
            //同步会出现  NetworkOnMainThreadException
//            Response response = call.execute();
//            Log.d("FakeNetworkCheckImpl", response.toString());
//            b=response.code() == 204;
            mcall.enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    Log.d("FakeNetworkCheckImpl", "onFailure=" + e.toString());
                    netStates = false;
                    mcall = null;
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try {
                        Log.d("FakeNetworkCheckImpl", response.toString());
                        netStates = response.code() == 204;
                        Log.d("FakeNetworkCheckImpl","onResponse="+netStates);

                    } catch (Exception e) {
                        e.printStackTrace();
                        Log.d("FakeNetworkCheckImpl", "onResponse=" + e.toString());
                        netStates = false;
                    }
                    mcall = null;
                }
            });
            return netStates;
        } catch (Exception e) {
            e.printStackTrace();
            Log.d("FakeNetworkCheckImpl", "Exception=" + netStates);
            netStates = false;
        }
        Log.d("FakeNetworkCheckImpl", "return==" + netStates);
        return netStates;
    }
}
