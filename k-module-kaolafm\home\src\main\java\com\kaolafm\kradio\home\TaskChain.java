package com.kaolafm.kradio.home;

import android.util.Log;

import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.YTLogUtil;

import java.util.HashMap;
import java.util.Map;

public class TaskChain {
    public interface TaskHandlerCallBack {
        void doEvent();
    }

    public final Map<String, Boolean> mTasks = new HashMap<>();
    private TaskHandlerCallBack mTaskHandlerCallBack;

    public void addTask(String key, Boolean initValue) {
        mTasks.put(key, initValue);
    }

    public void registerCallBack(TaskHandlerCallBack cb) {
        mTaskHandlerCallBack = cb;
    }

    public void updateConditions(String key, boolean value) {
        YTLogUtil.logStart("TaskChain", "updateConditions", "key = " + key + " , value = " + value);
        //更新条件
        mTasks.put(key, value);

        boolean isAllOk = true;
        for (String tkey : mTasks.keySet()) {
            if (!mTasks.get(tkey)) {
                isAllOk = false;
            }
        }
        YTLogUtil.logStart("TaskChain", "updateConditions", "isAllOk = " + isAllOk);

        if (isAllOk && mTaskHandlerCallBack != null) {
            mTaskHandlerCallBack.doEvent();
        }
        YTLogUtil.logStart("TaskChain", "updateConditions", "end");
    }

}
