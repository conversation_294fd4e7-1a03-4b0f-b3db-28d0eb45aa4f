package com.kaolafm.kradio.category.radio.tab.item;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.location.LocationManager;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.View;
import android.view.ViewStub;
import android.view.animation.AnimationUtils;
import android.view.animation.GridLayoutAnimationController;
import android.view.animation.LayoutAnimationController;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.categories.CategoryConstant;
import com.kaolafm.kradio.categories.HorizontalCategoryItemSpace;
import com.kaolafm.kradio.categories.HorizontalSpanSizeLookup;
import com.kaolafm.kradio.categories.HorizontalSubcategoryAdapter;
import com.kaolafm.kradio.category.radio.tab.item.TabItemContract.IPresenter;
import com.kaolafm.kradio.common.utils.PermissionUtils;
import com.kaolafm.kradio.common.widget.GridRecyclerView;
import com.kaolafm.kradio.component.ui.base.utils.ComponentUtils;
import com.kaolafm.kradio.flavor.location.BaiduLocation;
import com.kaolafm.kradio.home.utils.AppDateUtils;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.ui.BaseLazyFragment;
import com.kaolafm.kradio.lib.bean.SubcategoryItemBean;
import com.kaolafm.kradio.lib.report.ReportParamUtil;
import com.kaolafm.kradio.lib.report.ReportUtil;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.KaolaAppConfigData;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.RecyclerViewExposeUtil;
import com.kaolafm.kradio.lib.utils.RecyclerViewUtils;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.SharedPreferenceUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.scene.launcher.InitService;
import com.kaolafm.kradio.scene.launcher.event.LocationEvent;
import com.kaolafm.opensdk.api.personalise.model.HotRecommend;
import com.kaolafm.report.util.ReportParameterManager;
import com.lcodecore.tkrefreshlayout.utils.ScrollingUtil;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.lang.ref.WeakReference;
import java.util.List;
import java.util.Objects;

/**
 * 具体每一页二级分类的Fragment页面
 *
 * <AUTHOR>
 */
public class TabItemFragment extends BaseLazyFragment<IPresenter>
        implements TabItemContract.IView, RecyclerViewExposeUtil.OnItemExposeListener {
    public static final String TAG = "TabItemFragment";

    private GridLayoutManager mGridLayoutManager;

    private GridRecyclerView mRvSubcategoryContent;
    private View mErrorView;

    private HorizontalSpanSizeLookup mSpanSizeLookup;

    private ViewStub mVsLayoutErrorPage;
    private View mLoadingView;
    private View clPermissionTip;
    private TextView permissionTipText;
    private SpannableString spannableString;
    LinearLayout no_content_root;
    ImageView iv_no_content;
    TextView tv_no_content;
    TextView tv_no_content2;
    TextView scrollLeft, scrollRight;

    private HorizontalSubcategoryAdapter mSubcategoryAdapter;
    private GridLayoutAnimationController mGridController;
    private RecyclerViewScrollListener mRecyclerViewScrollListener;
    private int spaceResId = R.integer.subcategory_item_span_count;

    private long categoryId;

    private volatile boolean loaded;
    private boolean isLocalBroadcast = false;
    private HorizontalCategoryItemSpace mHorizontalCategoryItemSpace = new HorizontalCategoryItemSpace();

    PermissionUtils mPermissionUtils;
    private static final int PERMISSION_REQUEST_CODE = 10000;

    @Override
    protected void changeViewLayoutForStatusBar(View view) {
        // Do-Nothing
        //因为是子fragment,所以不需要设置pading.
    }

    @Override
    protected void lazyLoad() {
        if (isLocalBroadcast) {
            if (!hasGpsPermission() || !hasLocationInfo()) {
                showGpsPermissionTip();
                return;
            }
        }
        if (mPresenter != null) {
            showLoading();
            mPresenter.loadData();
        }
    }

    private void showGpsPermissionTip() {
        clPermissionTip.setVisibility(View.VISIBLE);
        if (spannableString == null) {
            String tip = getString(R.string.local_broadcast_no_permission);
            SpannableString spannableString = new SpannableString(tip);
            spannableString.setSpan(new ClickableSpan() {
                @Override
                public void onClick(@NonNull View widget) {
                    try {
                        mPermissionUtils = new PermissionUtils(getContext());
                        requestPermission();
                    } catch (Exception e) {
                        e.printStackTrace();
                        Log.e(TAG, "申请权限失败" + e.getMessage());
                    }
                }

                @Override
                public void updateDrawState(@NonNull TextPaint ds) {
                    ds.setUnderlineText(false);
                }
            }, tip.length() - 5, tip.length() - 1, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
            spannableString.setSpan(new ForegroundColorSpan(ResUtil.getColor(R.color.player_radio_bar_subtitle_text_color)),
                    tip.length() - 5, tip.length() - 1, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
            permissionTipText.setText(spannableString);
            permissionTipText.setMovementMethod(LinkMovementMethod.getInstance());
        }
    }

    private void requestPermission() {
        String[] permissionArray = new String[]{
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION,
        };
        ActivityCompat.requestPermissions(getActivity(), permissionArray, PERMISSION_REQUEST_CODE);
        Objects.requireNonNull(SharedPreferenceUtil.getInstance(getContext())).putBoolean(SharedPreferenceUtil.PERMISSION_REQUEST, true);

    }

    private void hideGpsPermissionTip() {
        clPermissionTip.setVisibility(View.GONE);
    }

    private boolean hasGpsPermission() {
        String per[] = new String[]{
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION
        };
        return PermissionUtils.checkPermission(getContext(), per);
    }

    private boolean isGpsOpen() {
        LocationManager locationManager
                = (LocationManager) getContext().getApplicationContext().getSystemService(Context.LOCATION_SERVICE);
        // 通过GPS卫星定位，定位级别可以精确到街（通过24颗卫星定位，在室外和空旷的地方定位准确、速度快）
        boolean gps = locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER);
        // 通过WLAN或移动网络(3G/2G)确定的位置（也称作AGPS，辅助GPS定位。主要用于在室内或遮盖物（建筑群或茂密的深林等）密集的地方定位）
        boolean network = locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER);
        boolean passive = locationManager.isProviderEnabled(LocationManager.PASSIVE_PROVIDER);
        if (gps || network || passive) {
            return true;
        }
        return false;
    }

    private boolean hasLocationInfo() {
        return !TextUtils.isEmpty(KaolaAppConfigData.getInstance().getLat())
                && !TextUtils.isEmpty(KaolaAppConfigData.getInstance().getLng());
    }

    @Override
    public void onResume() {
        super.onResume();
        checkLoadData();
        // CPU优化：Fragment可见时恢复图片请求
        Glide.with(this).resumeRequests();
    }

    @Override
    public void onStop() {
        super.onStop();
        // CPU优化：Fragment不可见时暂停图片请求
        Glide.with(this).pauseRequests();
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser) {
            checkLoadData();
        }
    }

    private void checkLoadData() {
        if (!isLocalBroadcast) {
            return;
        }
        if (clPermissionTip.getVisibility() == View.GONE) {
            return;
        }
        if (!hasGpsPermission()) {
            showGpsPermissionTip();
        } else {
            hideGpsPermissionTip();
            if (isGpsOpen() && !hasLocationInfo()) {
                if (!BaiduLocation.getInstance().initSuccess()) {
                    BaiduLocation.getInstance().reInit();
                }
//                InitService.startLocation();//速度很慢
            }
            if (hasLocationInfo()) {
                hideGpsPermissionTip();
                if (mPresenter != null) {
                    showLoading();
                    mPresenter.loadData();
                }
            }
        }
    }

    @Override
    public boolean useEventBus() {
        if (isLocalBroadcast) {
            return true;
        } else {
            return false;
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onLocationEvent(LocationEvent event) {
        if (!loaded) {
            loaded = true;
            hideGpsPermissionTip();
            if (mPresenter != null) {
                showLoading();
                mPresenter.loadData();
            }
        }
    }
    @Override
    public void setSelected() {
        if (mSubcategoryAdapter != null) {
            mSubcategoryAdapter.setSelected();
        }
    }

    @Override
    public void showImage(long id, String imgUrl, String desc) {
        SubcategoryItemBean bean = new SubcategoryItemBean();
        bean.setId(id);
        bean.setCoverUrl(imgUrl);
        bean.setTitle(desc);
        mSubcategoryAdapter.updateItem(bean);
    }

    @Override
    public void showData(List<SubcategoryItemBean> data) {
        loaded = true;
        setData(data);
        setSelected();
    }

    @Override
    public void showMoreData(List<SubcategoryItemBean> data) {
        hideLoading();
        if (ListUtil.isEmpty(data)) {
            return;
        }
        mSubcategoryAdapter.addDataList(data);
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001697153594?userId=1229522问题
        setSelected();
    }

    @Override
    public void showHotRecommend(HotRecommend hotRecommend) {
        this.hotRecommend = hotRecommend;
        setTextViewSpannable();
    }

    private HotRecommend hotRecommend;

    private void setTextViewSpannable() {
        tv_no_content.setMovementMethod(LinkMovementMethod.getInstance());
        if (hotRecommend != null) {
            tv_no_content.setText(AppDateUtils.getInstance().getTextViewSpannable(hotRecommend));
        }
        tv_no_content2.setText(ResUtil.getString(R.string.search_no_result));
        iv_no_content.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_subscription_exception_pic));
    }

    @Override
    public void showError(Exception exception) {

        Log.i(TAG, "showError: error = " + exception.getMessage());
        if (mVsLayoutErrorPage != null) {
            mErrorView = mVsLayoutErrorPage.inflate();
            TextView tvNetworkError = mErrorView.findViewById(R.id.tv_status_page_network_error);
            mVsLayoutErrorPage = null;
            if (tvNetworkError != null) {
                tvNetworkError.setOnClickListener(v -> {
                    if (!AntiShake.check(v.getId())) {
                        if (!NetworkUtil.isNetworkAvailable(getContext(), true)) {
                            return;
                        }
                        mSubcategoryAdapter.clear();
                        if (mPresenter != null) {
                            showLoading();
                            mPresenter.loadData();
                        }
                    }
                });
            }
        }
        isLoaded = false;
        ViewUtil.setViewVisibility(mErrorView, View.VISIBLE);
        hideLoading();
    }

    @Override
    public void showMoreDataError(Exception exception) {
        hideLoading();
    }

    @Override
    protected int getLayoutId() {
        return R.layout.fragment_subcategory_item;
    }

    @Override
    protected int getLayoutId_Tow() {
        return 0;
    }

    @Override
    protected TabItemContract.IPresenter createPresenter() {
        Bundle arg = getArguments();
        boolean haveMembers = true;
        if (arg != null) {
            haveMembers = arg.getBoolean(CategoryConstant.HAVE_MEMBERS, true);
        }
        categoryId = getArguments().getLong(CategoryConstant.CATEGORY_ID);
        if (CategoryConstant.LOCAL_BROADCAST_CATEGORY_CODE == categoryId) {
            isLocalBroadcast = true;
        }
        return new TabItemPresenter(this, categoryId, haveMembers);
    }

    @Override
    public void initView(View view) {
        assignViews(view);
        setup();
    }


    private void assignViews(View view) {
        mRvSubcategoryContent = view.findViewById(R.id.rv_subcategory_content);
        mVsLayoutErrorPage = view.findViewById(R.id.vs_layout_error_page);
        mLoadingView = view.findViewById(R.id.category_loading_view);
        clPermissionTip = view.findViewById(R.id.clPermissionTip);

        no_content_root = view.findViewById(R.id.no_content_root);
        iv_no_content = view.findViewById(R.id.iv_no_content);
        tv_no_content = view.findViewById(R.id.tv_no_content);
        tv_no_content2 = view.findViewById(R.id.tv_no_content2);

        scrollLeft = view.findViewById(R.id.cd_left);
        scrollRight = view.findViewById(R.id.cd_right);
        scrollLeft.setOnClickListener((v) -> ScrollingUtil.scrollListHorizontalByVoice(mRvSubcategoryContent, -1));
        scrollRight.setOnClickListener((v) -> ScrollingUtil.scrollListHorizontalByVoice(mRvSubcategoryContent, 1));
        permissionTipText = view.findViewById(R.id.tv_permission_tip);

    }

    private void setup() {
        mSubcategoryAdapter = new HorizontalSubcategoryAdapter();
        mSubcategoryAdapter.setOnItemClickListener((itemView, viewType, subcategoryItemBean, position) -> {
            if (NetworkUtil.isNetworkAvailable(getContext(), true)) {
                if (mPresenter != null) {
                    // TODO: 2019/3/2 后续修改
                    mPresenter.onClick(subcategoryItemBean, mSubcategoryAdapter, position);
                }
            }
        });
        mGridLayoutManager = new GridLayoutManager(getContext(), CategoryConstant.GRID_TOTAL_SPAN_COUNT
                , GridLayoutManager.HORIZONTAL, false);
        mSpanSizeLookup = new HorizontalSpanSizeLookup(mSubcategoryAdapter);
        mGridLayoutManager.setSpanSizeLookup(mSpanSizeLookup);

        mRvSubcategoryContent.setLayoutManager(mGridLayoutManager);
        //加载动画
        mGridController = (GridLayoutAnimationController) AnimationUtils.loadLayoutAnimation(getContext(), R.anim.layout_subcategory_item_load);

        mRvSubcategoryContent.setAdapter(mSubcategoryAdapter);

        //间隔
        mHorizontalCategoryItemSpace.setPorFirstRowTop(ResUtil.getDimen(R.dimen.subcategory_content_top_duration));
        mRvSubcategoryContent.addItemDecoration(mHorizontalCategoryItemSpace);

        RecyclerViewExposeUtil exposeUtil = new RecyclerViewExposeUtil();
        exposeUtil.setRecyclerItemExposeListener(mRvSubcategoryContent, this);
    }


    private void setData(List<SubcategoryItemBean> data) {
        hideLoading();
        ViewUtil.setViewVisibility(mErrorView, View.GONE);

        if (data != null && !data.isEmpty()) {
            no_content_root.setVisibility(View.GONE);
            if (data.get(0).getItemType() == SubcategoryItemBean.TYPE_ITEM_BROADCAST_CATEGORY) {
                changeRvGridManagerCategory();
            }
            mRvSubcategoryContent.scrollToPosition(0);
            mSubcategoryAdapter.setDataList(data);
            mRvSubcategoryContent.scheduleLayoutAnimation();
            initLayoutAnimationAndHead(data.get(0).getItemType());
            if (mPresenter.isHasNextPage()) {
                mRecyclerViewScrollListener = new RecyclerViewScrollListener(TabItemFragment.this);
                mRvSubcategoryContent.addOnScrollListener(mRecyclerViewScrollListener);
            }

        } else {
            setTextViewSpannable();
            no_content_root.setVisibility(View.VISIBLE);
        }

    }

    private void changeRvGridManagerCategory() {
        if (spaceResId == R.integer.subcategory_categroy_item_span_count) {
            return;
        }
        spaceResId = R.integer.subcategory_categroy_item_span_count;
        int spanCount = ResUtil.getInt(spaceResId);
        mSpanSizeLookup.setSpanCount(spanCount);
        mGridLayoutManager.setSpanCount(spanCount);
        mSubcategoryAdapter.notifyDataSetChanged();
    }


    /**
     * 设置加载动画和更新列表头布局。
     * 垂直布局用mLayoutController， gride布局用mGridController。
     * 切换的时候，判断是否需要更换。
     */
    private void initLayoutAnimationAndHead(int itemType) {
        LayoutAnimationController layoutAnimation = mRvSubcategoryContent.getLayoutAnimation();
        if (!(layoutAnimation instanceof GridLayoutAnimationController)) {
            mRvSubcategoryContent.setLayoutAnimation(null);
        }
    }

    @Override
    public void onItemViewVisible(boolean visible, int position) {
        if (visible && mSubcategoryAdapter != null) {
            SubcategoryItemBean bean = mSubcategoryAdapter.getItemData(position);
            ReportUtil.addContentShowEvent("", ReportParamUtil.getRadioType(bean), "",
                    String.valueOf(bean.getId()), "",
                    Constants.PAGE_ID_CATEGORY, bean.getParentCode(), "");

            String tag = ComponentUtils.getInstance().getReportTag(bean.getFreq(), bean.getVip());
            ReportUtil.addComponentShowAndClickEvent("",
                    false, "9"
                    , 2, bean.getParentCode(), position + "",
                    0 + "", String.valueOf(bean.getId())
                    , tag, ReportParameterManager.getInstance().getPage(), tag);
        }
    }

    private static class RecyclerViewScrollListener extends RecyclerView.OnScrollListener {
        WeakReference<TabItemFragment> tabItemFragmentWeakReference;

        public RecyclerViewScrollListener(TabItemFragment tabItemFragment) {
            tabItemFragmentWeakReference = new WeakReference<>(tabItemFragment);
        }

        @Override
        public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
            super.onScrollStateChanged(recyclerView, newState);
            //防止重复加载，只有松开手后再加载
            if (newState != RecyclerView.SCROLL_STATE_IDLE) {
                return;
            }
            if (tabItemFragmentWeakReference == null) {
                return;
            }
            TabItemFragment tabItemFragment = tabItemFragmentWeakReference.get();
            if (tabItemFragment == null) {
                return;
            }
            if (tabItemFragment.mRvSubcategoryContent == null) {
                return;
            }
            GridLayoutManager gridLayoutManager = (GridLayoutManager) tabItemFragment.mRvSubcategoryContent.getLayoutManager();
            if (gridLayoutManager == null) {
                return;
            }
            int adapterSize = tabItemFragment.mSubcategoryAdapter.getItemCount();

            if (gridLayoutManager.findLastVisibleItemPosition() + 1 < adapterSize) {
                return;
            }
            Log.i(TAG, "滑动到了加载下一页数据");

            if (!tabItemFragment.isHasNextPage()) {
                return;
            }
            Log.i(TAG, "有下一页数据.加载数据");
            tabItemFragment.loadMore();
        }

        @Override
        public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
            super.onScrolled(recyclerView, dx, dy);
        }
    }

    private boolean isHasNextPage() {
        if (mPresenter == null) {
            return false;
        }
        return mPresenter.isHasNextPage();
    }

    private void loadMore() {
        if (mPresenter == null) {
            return;
        }
        if (!NetworkUtil.isNetworkAvailable(getContext(), true)) {
            return;
        }
        showLoading();
        mPresenter.loadMore();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        loaded = false;
        if (mRvSubcategoryContent != null && mRecyclerViewScrollListener != null) {
            mRvSubcategoryContent.removeOnScrollListener(mRecyclerViewScrollListener);
        }
        if(mPresenter != null){
            mPresenter.cancelSchedule();
        }
    }

    @Override
    protected void showLoading() {
        super.showLoading();
        loaded = true;
        ViewUtil.setViewVisibility(mLoadingView, View.VISIBLE);

    }

    @Override
    protected void hideLoading() {
        super.hideLoading();
        ViewUtil.setViewVisibility(mLoadingView, View.GONE);
    }

    @Override
    protected void showAccordingToScreen(int orientation) {
        int firstVisibleItemPosition = mGridLayoutManager.findFirstVisibleItemPosition();
        int spanCount = ResUtil.getInt(spaceResId);
        mSpanSizeLookup.setSpanCount(spanCount);
        int left, top, right, bottom;
        mGridLayoutManager.setSpanCount(spanCount);
        //横屏
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            mGridLayoutManager.setOrientation(GridLayoutManager.HORIZONTAL);
            //竖屏
        } else {
            mGridLayoutManager.setOrientation(GridLayoutManager.VERTICAL);
        }

        left = ResUtil.getDimen(R.dimen.default_edge_start);
        top = 0;
        right = ResUtil.getDimen(R.dimen.subcategory_content_right_duration);
        bottom = ResUtil.getDimen(R.dimen.subcategory_content_bottom_duration);
        mRvSubcategoryContent.setPadding(left, top, right, bottom);

        mSubcategoryAdapter.notifyDataSetChanged();

        //自动滚动到上一个位置。
        //offset至少要有一个像素的便宜，否则某些手机和车机会不滚动
        mGridLayoutManager.scrollToPositionWithOffset(firstVisibleItemPosition, -1);
        RecyclerViewUtils.resetRecyclerView(mRvSubcategoryContent);

//        ConstraintLayout.LayoutParams lp = (ConstraintLayout.LayoutParams) mLoadingView.findViewById(R.id.llRefresh).getLayoutParams();
//        lp.verticalBias = ResUtil.getFloat(R.dimen.loading_vertical_bias);
//        mLoadingView.findViewById(R.id.llRefresh).setLayoutParams(lp);
    }

    @Override
    public void onMultiWindowModeChanged(boolean isInMultiWindowMode) {
        super.onMultiWindowModeChanged(isInMultiWindowMode);
        Log.i(TAG, "onMultiWindowModeChanged " + isInMultiWindowMode);
//        if (mRvSubcategoryContent != null) {
//            RecyclerView.Adapter adapter = mRvSubcategoryContent.getAdapter();
//            RecyclerView.LayoutManager manager = mRvSubcategoryContent.getLayoutManager();
//            mRvSubcategoryContent.setAdapter(null);
//            mRvSubcategoryContent.setLayoutManager(null);
//            mRvSubcategoryContent.getRecycledViewPool().clear();
//            mRvSubcategoryContent.setLayoutManager(manager);
//            mRvSubcategoryContent.setAdapter(adapter);
//        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        // CPU优化：清理图片内存缓存
        if (getContext() != null) {
            ImageLoader.getInstance().clearMemoryCache(getContext());
        }
    }
}
