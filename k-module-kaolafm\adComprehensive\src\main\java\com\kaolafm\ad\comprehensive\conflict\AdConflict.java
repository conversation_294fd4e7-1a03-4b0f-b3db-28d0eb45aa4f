package com.kaolafm.ad.comprehensive.conflict;

import android.util.Log;

import com.kaolafm.ad.api.model.Advert;

public class AdConflict {

    private static AdConflictHelper mAdConflictHelper = new AdConflictHelper();

    /**
     * 是否已经进入到首页
     */
    public static boolean isShowHome = false;

    public static void init(){
        mAdConflictHelper.init();
    }

    /**
     * 首屏连图冲突特殊需求
     * 首屏连图出现时需要关闭开屏的二次互动
     */
    public static void closeAdInteraction(){
        mAdConflictHelper.columnAdCloseInteraction();
    }

    public static void removeAdvert(Advert advert){
        Log.i("AdConflict","外部调用remove："+advert);
        mAdConflictHelper.remove(advert);
    }
}
