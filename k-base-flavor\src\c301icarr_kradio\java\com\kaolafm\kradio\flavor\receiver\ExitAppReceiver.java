package com.kaolafm.kradio.flavor.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.opensdk.player.logic.PlayerManager;

public class ExitAppReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
        PlayerManager.getInstance().abandonAudioFocus();
        AppManager.getInstance().killAll();
    }
}
