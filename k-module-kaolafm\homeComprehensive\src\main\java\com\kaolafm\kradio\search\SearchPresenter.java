package com.kaolafm.kradio.search;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.lib.report.ReportParamUtil;
import com.kaolafm.kradio.lib.report.ReportUtil;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.opensdk.api.personalise.model.HotRecommend;
import com.kaolafm.opensdk.api.search.SearchClassify;
import com.kaolafm.opensdk.api.search.model.SearchProgramBean;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.http.error.ErrorCode;

import java.util.ArrayList;
import java.util.List;

public class SearchPresenter extends BasePresenter<SearchModel, ISearchView> {

    public SearchPresenter(ISearchView view) {
        super(view);
    }

    @Override
    protected SearchModel createModel() {
        return new SearchModel();
    }

    @Override
    public void destroy() {
        cancelRequest();
        super.destroy();
    }

    /**
     * 获取联想词
     *
     * @param keyword 关键词
     */
    public void getAssociateWords(String keyword) {
        mModel.getAssociateWords(keyword, new HttpCallback<List<String>>() {
            @Override
            public void onSuccess(List<String> strings) {
                List<AssociateInfo> associateInfos = new ArrayList<>();
                for (String word : strings) {
                    AssociateInfo associateInfo = new AssociateInfo();
                    associateInfo.setAssociateWord(word);
                    associateInfo.setKeyWord(keyword);
                    associateInfos.add(associateInfo);
                }
                mView.showAssociateWordsView(associateInfos);
            }

            @Override
            public void onError(ApiException e) {
                mView.showAssociateWordsView(new ArrayList<AssociateInfo>());
            }
        });
    }

    /**
     * 获取热词
     */
    public void getHotSearchWords() {
        mModel.getHotSearchWords(new HttpCallback<List<String>>() {
            @Override
            public void onSuccess(List<String> hotWords) {
                if (ListUtil.isEmpty(hotWords)) {
                    return;
                }
                int size = hotWords.size();
                if (size > 10) {
                    hotWords = hotWords.subList(size - 10, size);
                }
                mView.showHotSearchWordsView(hotWords);
            }

            @Override
            public void onError(ApiException e) {
                mView.showHotSearchWordsView(null);
            }
        });
    }

    public void searchByKeyword(String searchType, String searchWay, String keyword) {
        searchByKeyword(searchType, searchWay, keyword, 1, 10, false);
    }

    /**
     * 通过关键词搜索
     *
     * @param searchType 内容类型
     * @param searchWay  搜索方式(用于上报数据)
     * @param keyword    关键词
     */
    public void searchByKeyword(String searchType, String searchWay, String keyword, int pagenum, int pagesize, boolean fromRefresh) {
        SearchHistoryManager.getInstance().addRecentSearchTag(keyword);
        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
            mView.showNoNetView();
            return;
        }
        if (!fromRefresh) {
            mView.showLoadingView();
        }
        mModel.searchAllById(keyword, searchType, pagenum, pagesize, new HttpCallback<List<SearchProgramBean>>() {
            @Override
            public void onSuccess(List<SearchProgramBean> searchProgramBeans) {
                if (ListUtil.isEmpty(searchProgramBeans)) {
                    reportSearchResultEvent(searchWay, "2", keyword, searchType);
                    mView.showNoResultView();
                    return;
                }
                mView.showSearchResultView(searchProgramBeans);
                reportSearchResultEvent(searchWay, "3", keyword, searchType);
            }

            @Override
            public void onError(ApiException e) {
                int errorCode = e.getCode();
                String result;
                if (errorCode == 40000) {
                    result = "1";
                } else {
                    result = "0";
                }
                reportSearchResultEvent(searchWay, result, keyword, searchType);
                if (errorCode == ErrorCode.HTTPS_CERTIFICATE_ERROR) {
                    mView.showNetworkError(ResUtil.getString(R.string.home_network_certificate_error), true);
                } else {
                    mView.showErrorView(errorCode);
                }
            }
        });
    }

    /**
     * 获取内容分类名及其ID
     */
    public void searchClassifyAll() {
        mModel.searchClassifyAll(new HttpCallback<List<SearchClassify>>() {
            @Override
            public void onSuccess(List<SearchClassify> searchClassifies) {
                List<SearchType> searchTypeList = new ArrayList<>();
                for (int i = 0; i < searchClassifies.size(); i++) {
                    SearchType searchType = new SearchType();
                    searchType.setTypeName(searchClassifies.get(i).getName());
                    searchType.setType(searchClassifies.get(i).getId());
                    if (i == 0) {
                        searchType.setSelect(true);
                    } else {
                        searchType.setSelect(false);
                    }
                    searchTypeList.add(searchType);
                }
                mView.setTypeData(searchTypeList);
            }

            @Override
            public void onError(ApiException e) {
                mView.setTypeData(null);
                if (e.getCode() == ErrorCode.HTTPS_CERTIFICATE_ERROR) {
                    mView.showSearchTypeNetworkError(ResUtil.getString(R.string.search_type_network_certificate_error));
                }
            }
        });
    }

    /**
     * 请求热门推荐内容
     */
    public void getHotRecommend() {
        mModel.getHotRecommend(new HttpCallback<HotRecommend>() {
            @Override
            public void onSuccess(HotRecommend hotRecommend) {
                if (mView != null) {
                    mView.showHotRecommend(hotRecommend);
                }
            }

            @Override
            public void onError(ApiException e) {

            }
        });
    }

    /**
     * 上报搜索播放事件
     *
     * @param searchProgramBean 搜索条目
     * @param postion           位置
     */
    public void reportSearchResultPlayEvent(SearchProgramBean searchProgramBean, int postion) {
        ReportUtil.reportSearchSelectResult("2", null, String.valueOf(searchProgramBean.getId()),
                String.valueOf(postion + 1), searchProgramBean.getCallback());
        ReportUtil.reportSearchToPlay("2", searchProgramBean.getCallback());
    }

    /**
     * 上报内容被点击事件
     *
     * @param bean
     * @param position
     */
    public void reportContentClickEvent(SearchProgramBean bean, int position) {
        ReportUtil.addContentClickEvent("", ReportParamUtil.getRadioType(bean.getType()),
                "", String.valueOf(bean.getId()),
                ReportParamUtil.getEventTag(bean.getVip() == 1, bean.getFine() == 1),
                Constants.PAGE_ID_SEARCH_RESULT, "", String.valueOf(position));
    }

    public void reportContentShowEvent(SearchProgramBean bean, int position) {
        ReportUtil.addContentShowEvent("", ReportParamUtil.getRadioType(bean.getType()),
                "", String.valueOf(bean.getId()),
                ReportParamUtil.getEventTag(bean.getVip() == 1, bean.getFine() == 1),
                Constants.PAGE_ID_SEARCH_RESULT, "", String.valueOf(position));
    }

    /**
     * 上报搜索结果事件
     *
     * @param searchType  搜索方式
     * @param result      搜索结果
     * @param keyword     关键词
     * @param contentType 内容分类
     */
    public void reportSearchResultEvent(String searchType, String result, String keyword, String contentType) {
        ReportUtil.reportSearchResult(keyword, searchType, result, "0", null, contentType);
    }

    public void cancelRequest() {
        mModel.cancelRequest();
    }
}
