package com.kaolafm.kradio.lib.init;

import java.util.ArrayList;
import java.util.List;

/**
 * 初始化任务{@link AppInitTask}集合。内部逻辑处理使用，外部不需要关心
 * <AUTHOR>
 * @date 2019-09-16
 */
public class AppInitTaskContainer {

    public List<AppInitTask> appInitTasks;

    public List<AppInitTask> asyncInitTasks;

    public AppInitTaskContainer() {
        appInitTasks = new ArrayList<>();
        asyncInitTasks = new ArrayList<>();
    }

    public void add(AppInitTask task) {
        appInitTasks.add(task);
        if (task.isAsync) {
            asyncInitTasks.add(task);
        }
    }
}
