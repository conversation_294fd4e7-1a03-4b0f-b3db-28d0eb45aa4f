package com.kaolafm.kradio.flavor.impl;

import androidx.fragment.app.DialogFragment;
import androidx.appcompat.app.AppCompatActivity;
import android.view.Gravity;

import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.base.flavor.KRadioBackKeyInter;
import com.kaolafm.kradio.lib.dialog.Dialogs;
import com.kaolafm.kradio.lib.utils.ResUtil;


/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-04-24 16:02
 ******************************************/
public final class KRadioBackKeyImpl implements KRadioBackKeyInter {
    @Override
    public boolean onBackPressed(Object... args) {
        final AppCompatActivity activity = (AppCompatActivity) args[0];
        DialogFragment dialogFragment = new Dialogs.Builder()
                .setType(Dialogs.TYPE_2BTN)
                .setGravity(Gravity.CENTER)
                .setMessage(ResUtil.getString(R.string.exit_msg_str))
                .setLeftBtnText(ResUtil.getString(R.string.ok))
                .setRightBtnText(ResUtil.getString(R.string.move_to_background_str))
                .setOnPositiveListener(dialog -> {
                    activity.moveTaskToBack(true);
                    dialog.dismiss();
                })
                .setOnNativeListener(dialog -> {
                    activity.finish();
                    dialog.dismiss();
                })
                .create();
        dialogFragment.show(activity.getSupportFragmentManager(), "clear_history");
        return true;
    }

}
