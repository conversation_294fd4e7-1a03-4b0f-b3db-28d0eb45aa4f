package com.kaolafm.kradio.lib.base.flavor;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-01-18 10:25
 ******************************************/
public interface KRadioVehicleSafetyCallback {
    /**
     * 检测长文本
     *
     * @param result           检测结果 true为允许，false为不允许
     * @param canShowCheckInfo 是否需要提示检测结果 true为是，false为否
     */
    void checkLongText(boolean result, boolean canShowCheckInfo);

    /**
     * 检测特殊图片信息展示
     *
     * @param result           检测结果 true为允许，false为不允许
     * @param canShowCheckInfo 是否需要提示检测结果 true为是，false为否
     */
    void checkImage(boolean result, boolean canShowCheckInfo);
}
