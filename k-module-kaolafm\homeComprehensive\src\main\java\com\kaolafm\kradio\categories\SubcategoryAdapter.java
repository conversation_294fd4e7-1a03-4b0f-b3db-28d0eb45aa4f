package com.kaolafm.kradio.categories;

import android.text.TextUtils;
import android.util.Log;
import android.view.ViewGroup;

import com.kaolafm.kradio.lib.bean.SubcategoryItemBean;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.categories.broadcast.BroadcastCategoryViewHolder;
import com.kaolafm.kradio.categories.broadcast.BroadcastLocalViewHolder;
import com.kaolafm.kradio.categories.viewholder.BaseSubcategoryViewHolder;
import com.kaolafm.kradio.categories.viewholder.RadioChannelViewHolder;
import com.kaolafm.kradio.categories.viewholder.SongMenuViewHolder;
import com.kaolafm.kradio.categories.viewholder.TitleViewHolder;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.common.ResType;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

/**
 * 具体每一页二级分类的adapter
 *
 * <AUTHOR>
 * @date 2018/4/25
 */

public class SubcategoryAdapter extends BaseAdapter<SubcategoryItemBean> {

    @Override
    public int getItemViewType(int position) {
        return position < getItemCount() ? mDataList.get(position).getItemType() : 0;
    }
    public void updateItem(SubcategoryItemBean data){
        for (int i = 0; i < mDataList.size(); i++) {
            SubcategoryItemBean bean = mDataList.get(i);
            if(data.getId() == bean.getId()){
                // To Fix SNTE-8
//                if(!TextUtils.isEmpty(data.getCoverUrl())){
//                    bean.setCoverUrl(data.getCoverUrl());
//                }
                if(!TextUtils.isEmpty(data.getDesc())){
                    bean.setTitle(data.getDesc());
                }
                notifyItemChanged(i);
                break;
            }
        }
    }

    public void setSelected() {
        PlayItem playItem = PlayerManagerHelper.getInstance().getCurPlayItem();
        if (playItem == null) {
            return;
        }
        String playingId = playItem.getRadioId();
        int radioType = playItem.getType();
        if (radioType == PlayerConstants.RESOURCES_TYPE_ONE_KEY_LISTENER_TYPE) {
            radioType = ResType.ALBUM_TYPE;
        }

        for (int i = 0, size = getItemCount(); i < size; i++) {
            SubcategoryItemBean itemBean = mDataList.get(i);
            if (itemBean.isSelected()) {
                itemBean.setSelected(false);
                notifyItemChanged(i);
            }
            //ID和类型一样就显示播放状态
            if (TextUtils.equals(String.valueOf(itemBean.getId()), playingId) && radioType == itemBean.getResType()) {
                Log.i("kradio.cate", "  setSelected: title=" + itemBean.getName());
                itemBean.setSelected(true);
                notifyItemChanged(i);
            }
        }

    }


    @Override
    protected BaseHolder<SubcategoryItemBean> getViewHolder(ViewGroup parent, int viewType) {
        switch (viewType) {
            case SubcategoryItemBean.TYPE_ITEM_TITLE:
                return new TitleViewHolder(inflate(parent, R.layout.item_subcategory_title, viewType));
            case SubcategoryItemBean.TYPE_ITEM_ALBUM:
                return new SongMenuViewHolder(inflate(parent, R.layout.item_subcategory_songmenu, viewType));
            case SubcategoryItemBean.TYPE_ITEM_RADIO_CHANNEL:
                return new RadioChannelViewHolder(inflate(parent, R.layout.item_subcategory_radio_channel, viewType), null);
            case SubcategoryItemBean.TYPE_ITEM_BROADCAST_CATEGORY:
                return new BroadcastCategoryViewHolder(inflate(parent, R.layout.item_subcategory_broadcast_category, viewType));
            case SubcategoryItemBean.TYPE_ITEM_BROADCAST_LOCAL:
                return new BroadcastLocalViewHolder(inflate(parent, R.layout.item_subcategory_broadcast_local, viewType));
            default:
                return new BaseSubcategoryViewHolder(inflate(parent, R.layout.item_subcategory_title, viewType));
        }
    }
}
