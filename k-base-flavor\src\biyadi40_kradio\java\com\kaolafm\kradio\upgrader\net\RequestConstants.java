package com.kaolafm.kradio.upgrader.net;

/**
 * <AUTHOR> on 2019/4/1.
 */

public class RequestConstants {
    public final static int DEFAULT_NETWORK_TIMEOUT = 30;

    //测试接口路径
    public final static String BASE_URL_TEST_HOST = "http://testupgrade.byd.com:8055/ota-api/";
    public final static String BASE_URL_TEST_IP = "http://*************:8055/ota-api/";
    // 正式接口路径
    public final static String BASE_URL_HOST = "http://productupgrade.byd.com:8055/ota-api/";

    public final static String SignHeader = "7c8c2f1fe02e7aa404e6d087940d946a7f517d";
    public final static String SignEnder = "4d0d34315d8c1e041ee2616b84c35839ca192320941";
    public final static String salt = "5273497BA5CE795B433F66D3E180F7C0";


    public final static String CODE_SUCCESS = "01";//请求成功
    public final static String CODE_REQUEST_ERROR = "02";//参数错误
    public final static String CODE_REQUEST_OFTEN = "03";//请求过于频繁,请稍后再试
    public final static String CODE_NOMES = "04";//未查询到信息
    public final static String CODE_SIGN_ERROR = "05";//签名信息不对
    public final static String CODE_SERVER_BUSY = "99";//系统繁忙，请稍后再试


}
