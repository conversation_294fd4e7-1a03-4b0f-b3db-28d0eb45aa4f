<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:gravity="center">

    <include
        android:visibility="gone"
        android:id="@+id/online_fragment_unlogin"
        layout="@layout/online_fragment_unlogin" />

    <include
        android:visibility="gone"
        android:id="@+id/online_fragment_login"
        layout="@layout/online_fragment_members_login" />

    <ViewStub
        android:visibility="gone"
        android:id="@+id/error_page"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout="@layout/online_error_layout" />

</androidx.constraintlayout.widget.ConstraintLayout>