package com.kaolafm.kradio.component;

import java.util.concurrent.ConcurrentHashMap;

/**
 * 组件管理类
 * <AUTHOR>
 * @date 2019-07-01
 */
class ComponentManager {

    static {
        //默认初始化的地方。由插件在该代码块里自动进行默认初始化。
    }

    /** 当前进程中的组件集合 */
    private static final ConcurrentHashMap<String, Component> COMPONENTS = new ConcurrentHashMap<>();

    /**
     * 组件名称对应的进程名称集合
     * 当前进程为主进程：包含当前app内的所有静态组件和动态组件的（名称 - 进程名）的映射表
     * 当前进程为子进程：包含当前app内的所有静态组件和当前进程内注册的动态组件的（名称 - 进程名）的映射表
     */
//    private static final ConcurrentHashMap<String, String> COMPONENT_PROCESS_NAMES = new ConcurrentHashMap<>();

    public static Component getComponentByName(String componentName) {

        return COMPONENTS.get(componentName);
    }

    public static boolean hasComponent(String componentName) {
        return COMPONENTS.get(componentName) != null;
    }

    /**
     * 注册组件
     */
    static void registerComponent(Component component) {
        if (component != null) {
            String componentName = component.getClass().getSimpleName();
            if (component instanceof DynamicComponent) {
                componentName = ((DynamicComponent) component).getName();
            }
            try{
                COMPONENTS.put(componentName, component);
            } catch(Exception e) {
                e.printStackTrace();
            }
        }
    }

    static void unregisterComponent(Component component) {
        if (component != null) {
            String componentName = component.getClass().getSimpleName();
            if (component instanceof DynamicComponent) {
                componentName = ((DynamicComponent) component).getName();
            }
            COMPONENTS.remove(componentName);
        }
    }

}
