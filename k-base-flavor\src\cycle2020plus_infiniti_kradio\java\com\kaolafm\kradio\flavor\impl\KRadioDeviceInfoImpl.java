//package com.kaolafm.kradio.flavor.impl;
//
//import android.content.Context;
//import android.provider.Settings;
//
//import com.kaolafm.kradio.lib.base.flavor.KRadioDeviceInfoInter;
//
///******************************************
// * 类描述:
// *
// * @version: V1.0
// * @author: yangshaoning
// * @time: 2019-03-22 11:46
// ******************************************/
//public final class KRadioDeviceInfoImpl implements KRadioDeviceInfoInter {
//    @Override
//    public String getDeviceId(Object... args) {
//        Context context = (Context) args[0];
//        String deviceId = Settings.Global.getString(context.getContentResolver(), "ivi.system.vehicle.daid");
//        return deviceId;
//    }
//
//    @Override
//    public String getCarType(Object... args) {
//        Context context = (Context) args[0];
//        String deviceId = Settings.Global.getString(context.getContentResolver(), "ivi.system.vehicle.daid");
//        if (deviceId == null || deviceId.length() < 6) {
//            return null;
//        }
//
//        return deviceId.substring(0, 6);
//    }
//}
