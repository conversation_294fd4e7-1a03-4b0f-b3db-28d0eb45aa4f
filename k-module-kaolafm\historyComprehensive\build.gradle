// MicroModule build file where you can declare MicroModule dependencies.
dependencies {
    def dependent = rootProject.ext.dependencies
    implementation fileTree(dir: 'historyComprehensive/libs', include: ['*.jar'])
    implementation microModule(':common')
    implementation microModule(':history')
    implementation microModule(':commonComprehensive')
    implementation microModule(':purchaseComprehensive')
}
