<resources>

    <!-- Online Radio Splash Theme. -->
    <style name="OnlineAppThemeCompat.Splash" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowBackground">@drawable/online_background_splash</item>
    </style>

    <style name="BaseDialogTheme" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:backgroundDimAmount">0.7</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowFullscreen">true</item>//全屏即无通知栏
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowIsFloating">true</item>
    </style>
    <!--    背景透明-->
    <style name="TranslucentTheme" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@color/login_bg</item>
        <item name="android:background">@color/transparent</item>
        <item name="android:windowFullscreen">true</item>//全屏即无通知栏
        <item name="android:colorBackground">@color/transparent</item>
    </style>

    <style name="OnlineMessageBubbleAnimation">
        <item name="android:windowEnterAnimation">@anim/online_message_bubble_show</item>
        <item name="android:windowExitAnimation">@anim/online_message_bubble_dismiss</item>
    </style>
    <style name="FullScreenDialogTheme" parent="BaseDialogTheme">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:background">@color/transparent</item>
    </style>
    <style name="OnlineFragmentBackButton">
        <item name="android:id">@id/backView</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:layout_width">@dimen/m80</item>
        <item name="android:layout_height">@dimen/m80</item>
        <item name="android:background">@drawable/color_main_button_click_selector</item>
        <item name="android:padding">@dimen/m22</item>
        <item name="android:scaleType">centerInside</item>
        <item name="android:src">@drawable/online_player_ic_back</item>
        <item name="layout_constraintLeft_toLeftOf">parent</item>
        <item name="layout_constraintTop_toTopOf">parent</item>
    </style>
    <style name="Online_FragmentBackButton_white" parent="FragmentBackButton_white">
        <item name="android:src">@drawable/online_player_ic_back</item>
    </style>
</resources>
