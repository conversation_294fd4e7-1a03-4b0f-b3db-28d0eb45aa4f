package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.os.Handler;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.base.flavor.KRadioDialogActivityInter;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;

public class KRadioDialogActivityImpl implements KRadioDialogActivityInter {
    @Override
    public void handleHubActivity(Activity activity) {

    }

    @Override
    public void handleBaseActivity(Activity activity) {
        activity.getWindow().getDecorView().setPadding(0, 0, 0, 80); //让80px底部给导航栏
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                Log.e("KRadioDialogActivityImp", ScreenUtil.getScreenWidth() + "  " + ScreenUtil.getScreenHeight()
                        + "  " + ResUtil.getDimen(R.dimen.x1280) + "  " + ResUtil.getDimen(R.dimen.y720) + "  " + ResUtil.getDimen(R.dimen.m720));
            }
        }, 4000);

    }
}
