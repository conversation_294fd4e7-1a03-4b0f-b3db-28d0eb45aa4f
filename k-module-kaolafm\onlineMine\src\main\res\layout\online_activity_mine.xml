<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/online_page_bg_vague"
    tools:context="com.kaolafm.kradio.online.mine.MineActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/y76"
            android:layout_marginTop="@dimen/y20">

            <ImageView
                android:id="@+id/back_mine"
                android:layout_width="@dimen/x84"
                android:layout_height="match_parent"
                android:layout_marginTop="@dimen/y6"
                android:background="@color/transparent"
                android:clickable="true"
                android:focusable="true"
                android:paddingStart="@dimen/x28"
                android:paddingEnd="@dimen/x28"
                android:scaleType="fitCenter"
                android:src="@drawable/online_player_ic_back"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.kaolafm.kradio.lib.widget.tab.SlidingTabLayout
                android:id="@+id/mine_tablayout"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_centerVertical="true"
                android:layout_marginLeft="@dimen/x521"
                android:layout_marginRight="@dimen/m100"
                android:layout_marginBottom="@dimen/m5"
                app:tl_first_no_padding="true"
                app:tl_indicator_color="@color/mine_tab_indicator_color"
                app:tl_indicator_height="@dimen/m2"
                app:tl_tab_padding="@dimen/m20"
                app:tl_indicator_style="NORMAL"
                app:tl_indicator_width="@dimen/m40"
                app:tl_textSelectColor="@color/online_main_tab_select_text_color"
                app:kradio_tl_textSelectSize="@dimen/m28"
                app:kradio_tl_textSize="@dimen/m24"
                app:tl_textSelectSize="@dimen/m28"
                app:tl_textSize="@dimen/m24"
                app:tl_textUnselectColor="@color/online_main_tab_text_color" />
        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal">

            <FrameLayout
                android:id="@+id/mine_user_fl"
                android:layout_width="@dimen/x386"
                android:layout_height="match_parent" />

            <View
                android:layout_width="@dimen/m1"
                android:layout_height="@dimen/y380"
                android:layout_gravity="center_vertical"
                android:background="@color/mine_page_line_color" />

            <com.kaolafm.kradio.common.widget.NotScrollViewPager
                android:id="@+id/mine_viewpage"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1" />
        </LinearLayout>

    </LinearLayout>

    <include
        layout="@layout/online_layout_msg_float_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true" />
</RelativeLayout>