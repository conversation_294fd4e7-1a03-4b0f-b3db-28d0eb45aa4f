package com.kaolafm.kradio.flavor.impl;

import android.app.Application;

import com.kaolafm.kradio.flavor.carnetwork.carnetworkutils.CarAuthUtil;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioAuthInter;

import com.kaolafm.sdk.core.mediaplayer.OnAudioFocusChangeInter;


public class KRadioAuthImpl implements KRadioAuthInter, OnAudioFocusChangeInter {
    private Application context;

    @Override
    public void onAudioFocusChange(int i) {
        if (i < 0) {
            PlayerManager.getInstance().abandonAudioFocus();
        }
    }

    private static class InstanceHolder {
        private final static KRadioAuthImpl sInstance = new KRadioAuthImpl();
    }

    public static KRadioAuthImpl getInstance() {
        return InstanceHolder.sInstance;
    }

    @Override
    public boolean doInitCheckCanPlayInter() {
        PlayerCustomizeManager.getInstance().injectKLCheckCanPlayListener("com.kaolafm.kradio.flavor.impl.CheckCanPlayImpl");
        context = AppDelegate.getInstance().getContext();
        if (PlayerCustomizeManager.getInstance().getCheckCanPlayInter() != null) {
            PlayerCustomizeManager.getInstance().getCheckCanPlayInter().addListener();
        }
        return true;
    }

    @Override
    public boolean unInitCheckCanPlayInter() {
        if (PlayerCustomizeManager.getInstance().getCheckCanPlayInter() != null) {
            PlayerCustomizeManager.getInstance().getCheckCanPlayInter().removeListener();
        }
        return true;
    }

    @Override
    public boolean doCheckAuth(Object... args) {
        if (PlayerCustomizeManager.getInstance().getCheckCanPlayInter() != null) {
            PlayerCustomizeManager.getInstance().getCheckCanPlayInter().checkPlay(args);
        }
        return true;
    }

    @Override
    public boolean authStatus(Object... args) {
        //一个本地保存的鉴权状态  一个是网络是否需要去鉴权
//        !CarAuthUtil.needAuthByNetWork() || CarAuthUtil.AUTH_THROUGH;
        return CarAuthUtil.AUTH_THROUGH;
    }

    @Override
    public boolean netWorkStatus(Object... args) {
        return true;
    }

}
