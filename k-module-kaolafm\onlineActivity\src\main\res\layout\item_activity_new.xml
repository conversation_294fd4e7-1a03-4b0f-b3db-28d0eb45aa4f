<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/item_root"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/item_activitys_bg_iv"
        android:layout_width="@dimen/m360"
        android:layout_height="@dimen/m360"
        android:src="@drawable/item_activitys_bg_pic"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="MissingConstraints" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="@dimen/m360"
        android:layout_height="@dimen/m360"
        android:paddingTop="@dimen/y26"
        android:layout_centerInParent="true">

        <RelativeLayout
            android:id="@+id/pic_rl"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/pic_image"
                android:layout_width="@dimen/m100"
                android:layout_height="@dimen/m100"
                android:layout_centerInParent="true"
                android:background="@drawable/activity_item_img_bg"
                tools:background="@color/red01"
                tools:ignore="MissingConstraints" />

        </RelativeLayout>

        <com.kaolafm.kradio.component.ui.base.view.KradioTextView
            android:id="@+id/title_activity"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/y15"
            android:ellipsize="end"
            android:singleLine="true"
            android:gravity="center"
            android:paddingLeft="@dimen/x5"
            android:paddingRight="@dimen/x5"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/m38"
            app:kt_font_weight="0.5"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/pic_rl"
            tools:ignore="MissingConstraints"
            android:text="活动标题活动标题活题活动题" />

        <TextView
            android:id="@+id/des_activity"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/m5"
            android:maxWidth="@dimen/x300"
            android:ellipsize="end"
            android:gravity="center"
            android:singleLine="true"
            android:textColor="#D3EDFF"
            android:textSize="@dimen/m20"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title_activity"
            tools:text="新年活动新年活动新年活动新年活动新年活动新年活动新年活动新年活动新年活动新年活动新年活动" />

        <com.kaolafm.kradio.component.ui.base.view.KradioTextView
            android:id="@+id/date_activity"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/m5"
            android:ellipsize="end"
            android:gravity="center"
            android:maxEms="12"
            android:maxLines="1"
            android:textStyle="bold"
            android:textColor="@color/color_2"
            android:textSize="@dimen/m24"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/des_activity"
            tools:ignore="MissingConstraints"
            tools:text="2022.03.12" />

        <TextView
            android:id="@+id/activity_details_tv"
            android:layout_width="@dimen/x160"
            android:layout_height="@dimen/y48"
            android:layout_marginTop="@dimen/m18"
            android:gravity="center"
            android:text="查看详情"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/m22"
            tools:background="@drawable/online_activity_item_btn_bg_green"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/date_activity" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/qr_view_expire"
        android:layout_width="@dimen/m360"
        android:layout_height="@dimen/m360"
        android:background="@drawable/item_expire_bg"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="MissingConstraints" />

    <TextView
        android:id="@+id/qr_expire_icon"
        android:layout_width="@dimen/m240"
        android:layout_height="@dimen/m72"
        android:layout_centerHorizontal="true"
        android:layout_alignBottom="@+id/item_activitys_bg_iv"
        android:background="@drawable/component_activity_end_text_bg"
        android:gravity="center"
       android:layout_marginBottom="@dimen/m94"
        android:text="活动已结束"
        android:textColor="@color/colorWhite"
        android:textSize="@dimen/m28"
        android:visibility="gone"
        />
</RelativeLayout>