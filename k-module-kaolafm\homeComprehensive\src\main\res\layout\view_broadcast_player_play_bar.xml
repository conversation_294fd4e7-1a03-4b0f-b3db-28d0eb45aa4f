<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:showIn="@layout/fragment_broadcast_player_horizontal">

    <ImageView
        android:id="@+id/broadcast_invisible"
        android:layout_width="@dimen/broadcast_player_pre_or_next_size"
        android:layout_height="@dimen/broadcast_player_pre_or_next_size"
        android:background="@drawable/color_main_button_click_selector"
        android:scaleType="centerInside"
        android:src="@drawable/player_listening_by_music_selector"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="W,1:1"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.7"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/broadcast_play_pre_audio"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/broadcast_playlist"
        android:layout_width="@dimen/broadcast_player_pre_or_next_size"
        android:layout_height="@dimen/broadcast_player_pre_or_next_size"
        android:layout_marginEnd="@dimen/x10"
        android:background="@drawable/color_main_button_click_selector"
        android:padding="@dimen/m15"
        android:scaleType="fitXY"
        android:src="@drawable/player_playlist_selector"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/broadcast_play_next_audio"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.kaolafm.kradio.player.comprehensive.play.view.PlayerControlViewNext
        android:id="@+id/broadcast_play_next_audio"
        android:layout_width="@dimen/broadcast_player_pre_or_next_size"
        android:layout_height="@dimen/broadcast_player_pre_or_next_size"
        android:padding="@dimen/m20"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/broadcast_switch_playState_mode"
        app:layout_constraintRight_toLeftOf="@id/broadcast_playlist"
        app:layout_constraintTop_toTopOf="parent"
        app:next_icon="@drawable/comprehensive_playerbar_next"
        app:next_location="2" />

    <com.kaolafm.kradio.player.comprehensive.play.view.PlayerControlViewPlay
        android:id="@+id/broadcast_switch_playState_mode"
        android:layout_width="@dimen/broadcast_player_play_size"
        android:layout_height="@dimen/broadcast_player_play_size"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/broadcast_play_pre_audio"
        app:layout_constraintRight_toLeftOf="@id/broadcast_play_next_audio"
        app:layout_constraintTop_toTopOf="parent"
        app:play_location="2" />


    <com.kaolafm.kradio.player.comprehensive.play.view.PlayerControlViewPrevious
        android:id="@+id/broadcast_play_pre_audio"
        android:layout_width="@dimen/broadcast_player_pre_or_next_size"
        android:layout_height="@dimen/broadcast_player_pre_or_next_size"
        android:padding="@dimen/m20"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintLeft_toRightOf="@id/broadcast_invisible"
        app:layout_constraintRight_toLeftOf="@id/broadcast_switch_playState_mode"
        app:layout_constraintTop_toTopOf="parent"
        app:previous_icon="@drawable/comprehensive_playerbar_prev"
        app:previous_location="2" />

</merge>