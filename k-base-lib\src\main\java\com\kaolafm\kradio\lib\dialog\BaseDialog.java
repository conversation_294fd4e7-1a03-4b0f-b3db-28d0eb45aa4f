package com.kaolafm.kradio.lib.dialog;

import android.animation.Animator;
import android.animation.AnimatorInflater;
import android.animation.AnimatorListenerAdapter;
import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import androidx.annotation.AnimatorRes;
import androidx.annotation.FloatRange;
import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.StyleRes;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager.LayoutParams;

import com.kaolafm.kradio.lib.R;
import com.kaolafm.kradio.lib.base.flavor.KRadioDialogPropInter;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;

import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;

/**
 * Dialog基类，但是使用时，并不是一定要继承该类，而是在具体的类中直接new，并设置参数。具体的逻辑处理在具体的dialog类中。
 * 该类只是给dialog进行设置参数。
 *
 * <AUTHOR>
 * @date 2018/4/10
 */

public class BaseDialog extends Dialog {

    protected Build mBuild;

    protected Context mContext;

    /**
     * 动画是否正在执行， true正在执行
     */
    private boolean isAnimShowing = false;

    private View mDialogView;

    private KRadioDialogPropInter mKRadioDialogPropInter;

    protected BaseDialog(Build build) {
        super(build.context, build.themeResId);
        mBuild = build;
        mContext = build.context;
        mKRadioDialogPropInter = ClazzImplUtil.getInter("KRadioDialogPropImpl");
        if (mKRadioDialogPropInter != null) {
            mKRadioDialogPropInter.hideNavigationBar(BaseDialog.this.getWindow());
        }
        init();
    }

    protected BaseDialog(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected BaseDialog(@NonNull Context context) {
        super(context);
    }

    protected BaseDialog(@NonNull Context context, boolean cancelable,
                         @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    private void init() {
        mDialogView = mBuild.dialogView;
        setContentView(mDialogView);
        setCanceledOnTouchOutside(mBuild.cancelOutside);
        setCancelable(mBuild.cancelable);
    }

    @Override
    public void onAttachedToWindow() {
        super.onAttachedToWindow();
//        DisplayMetrics displayMetrics = mContext.getResources().getDisplayMetrics();
        Window window = getWindow();
        LayoutParams layoutParams = window.getAttributes();
        if (mKRadioDialogPropInter != null) {
            mKRadioDialogPropInter.addDialogWindowFlags(window);
        }
        int width = ScreenUtil.getScreenWidth();
        if (mBuild.isFullScreen) {
            window.getDecorView().setPadding(0, 0, 0, 0);
            layoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT;
            layoutParams.height = ViewGroup.LayoutParams.MATCH_PARENT;
        } else {
            if (mBuild.widthScale == 0) {
                if (mBuild.width != 0) {
                    layoutParams.width = mBuild.width;
                } else {
                    layoutParams.width = ViewGroup.LayoutParams.WRAP_CONTENT;
                }
            } else if (mBuild.widthScale == 1.0F) {
                layoutParams.width = LayoutParams.MATCH_PARENT;
            } else {
                layoutParams.width = (int) (width /*displayMetrics.widthPixels*/ * mBuild.widthScale);
            }
            if (mBuild.heightScale == 0) {
                if (mBuild.height == 0) {
                    layoutParams.height = mBuild.height;
                } else {
                    layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT;
                }
            } else {
                int height = ScreenUtil.getScreenHeight();
                layoutParams.height = (int) (height /*displayMetrics.heightPixels*/ * mBuild.heightScale);
            }
            layoutParams.x = mBuild.xOffset;
            layoutParams.y = mBuild.yOffset;
            Log.i("BaseDialog", "------------>widthPixels = " + width + "---->scale = " + mBuild.widthScale + "---->xOffset = " + mBuild.xOffset);
        }
        layoutParams.gravity = mBuild.gravity;
        window.setAttributes(layoutParams);

        if (mBuild.showAnim != null) {
            mBuild.showAnim.addListener(new AnimatorListenerAdapter() {
                @Override
                public void onAnimationCancel(Animator animation) {
                    isAnimShowing = false;
                }

                @Override
                public void onAnimationEnd(Animator animation) {
                    isAnimShowing = false;
                    delayDismiss();
                }

                @Override
                public void onAnimationStart(Animator animation) {
                    isAnimShowing = true;
                }
            });
            mBuild.showAnim.setTarget(mDialogView);
            mBuild.showAnim.start();
        }
    }

    @Override
    public boolean dispatchTouchEvent(@NonNull MotionEvent ev) {
        if (isAnimShowing || mBuild.autoDismissDelay > 0) {
            return true;
        }
        return super.dispatchTouchEvent(ev);
    }

    @Override
    public void onBackPressed() {
        if (isAnimShowing || mBuild.autoDismissDelay > 0) {
            return;
        }
        super.onBackPressed();
    }

    /**
     * 获取dialog的View
     */
    public View getDialogView() {
        return mDialogView;
    }

    @Override
    public void dismiss() {
        if (mBuild.dismissAnim != null) {
            mBuild.dismissAnim.addListener(new AnimatorListenerAdapter() {
                @Override
                public void onAnimationCancel(Animator animation) {
                    isAnimShowing = false;
                }

                @Override
                public void onAnimationEnd(Animator animation) {
                    isAnimShowing = false;
                }

                @Override
                public void onAnimationStart(Animator animation) {
                    isAnimShowing = true;
                }
            });
            mBuild.dismissAnim.setTarget(mDialogView);
            mBuild.dismissAnim.start();
        } else {
            superDismiss();
        }
    }

    private void superDismiss() {
        super.dismiss();
    }

    private void delayDismiss() {
        if (mBuild.autoDismissDelay > 0) {
            Observable.timer(mBuild.autoDismissDelay, TimeUnit.MILLISECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(aLong -> dismiss());
        }
    }

    @Override
    public void show() {
        super.show();
    }


    public static class Build {

        private boolean cancelable = true;

        private boolean cancelOutside = true;

        private View dialogView;

        private Animator showAnim;

        private Animator dismissAnim;

        private int gravity = Gravity.CENTER;

        private boolean isFullScreen = false;

        /**
         * 宽度占屏幕的宽的比例(0-1)，0表示不设置比例，默认0
         */
        private float widthScale = 0;

        /**
         * 高度占屏幕的高的比例(0-1)，0表示不设置比例，默认0
         */
        private float heightScale = 0;

        /**
         * 水平方向偏移量
         */
        private int xOffset = 0;

        /**
         * 垂直方向偏移量
         */
        private int yOffset = 0;

        private int width = 0;

        private int height = 0;

        private int themeResId = R.style.BaseDialogTheme;

        /**
         * 自动延迟消失时间，默认为0，为0时不自动消失。
         */
        private long autoDismissDelay = 0;

        private DialogListener.OnDismissListener dismissListener;

        private Context context;

        public Build(Context context) {
            this.context = context;
        }

        /**
         * 设置点击返回按钮时dialog是否消失，默认true。
         *
         * @param cancelable true消失
         */
        public Build setCancelable(boolean cancelable) {
            this.cancelable = cancelable;
            return this;
        }

        /**
         * 设置dialog在点击空白区域是否消失，默认true。
         *
         * @param cancelOutside true 消失
         */
        public Build setCancelOutside(boolean cancelOutside) {
            this.cancelOutside = cancelOutside;
            return this;
        }

        /**
         * 设置自定义dialog的view的布局id
         */
        public Build setDialogView(@LayoutRes int layoutId) {
            dialogView = LayoutInflater.from(context).inflate(layoutId, null);
            return this;
        }

        public Build setDialogView(View dialogView) {
            this.dialogView = dialogView;
            return this;
        }

        /**
         * 设置dialog显示动画
         */
        public Build setShowAnim(Animator showAnim) {
            this.showAnim = showAnim;
            return this;
        }

        public Build setShowAnim(@AnimatorRes int animId) {
            this.showAnim = AnimatorInflater.loadAnimator(context, animId);
            return this;
        }

        /**
         * 设置dialog消失动画
         */
        public Build setDismissAnim(Animator dismissAnim) {
            this.dismissAnim = dismissAnim;
            return this;
        }

        public Build setDismissAnim(@AnimatorRes int animId) {
            this.dismissAnim = AnimatorInflater.loadAnimator(context, animId);
            return this;
        }

        /**
         * dialog的显示位置，默认是屏幕中间
         */
        public Build setGravity(int gravity) {
            this.gravity = gravity;
            return this;
        }

        /**
         * 是否全屏显示，
         *
         * @param fullScreen true 全屏显示，默认不全屏
         */
        public Build setFullScreen(boolean fullScreen) {
            isFullScreen = fullScreen;
            return this;
        }

        /**
         * 宽度占屏幕的宽的比例(0-1)，0表示不设置比例，默认0
         */
        public Build setWidthScale(@FloatRange(from = 0, to = 1) float widthScale) {
            this.widthScale = widthScale;
            return this;
        }

        /**
         * 高度占屏幕的高的比例(0-1)，0表示不设置比例，默认0
         */
        public Build setHeightScale(@FloatRange(from = 0, to = 1) float heightScale) {
            this.heightScale = heightScale;
            return this;
        }

        /**
         * 设置dialog的style，默认{@link R.style#BaseDialogTheme}
         *
         * @param themeResId style id
         */
        public Build setThemeResId(@StyleRes int themeResId) {
            this.themeResId = themeResId;
            return this;
        }

        /**
         * 自动延迟消失时间，默认为0，为0时不自动消失。毫秒值
         */
        public Build setAutoDismissDelay(long autoDismissDelay) {
            this.autoDismissDelay = autoDismissDelay;
            return this;
        }

        public Build setDismissListener(DialogListener.OnDismissListener dismissListener) {
            this.dismissListener = dismissListener;
            return this;
        }

        /**
         * 设置水平方法偏移量
         *
         * @param xOffset
         */
        public Build setXOffset(int xOffset) {
            this.xOffset = xOffset;
            return this;
        }

        /**
         * 设置垂直方法偏移量
         *
         * @param yOffset
         */
        public Build setYOffset(int yOffset) {
            this.yOffset = yOffset;
            return this;
        }

        /**
         * 设置固定宽带，widthScale = 0 时有效
         *
         * @param width
         * @return
         */
        public Build setWidth(int width) {
            this.width = width;
            return this;
        }

        /**
         * 设置固定高度 heightScale  = 0 时有效
         *
         * @param height
         * @return
         */
        public Build setHeight(int height) {
            this.height = height;
            return this;
        }

        public BaseDialog build() {
            return new BaseDialog(this);
        }
    }

}
