package com.kaolafm.kradio.flavor.impl;

import com.kaolafm.kradio.lib.base.flavor.KRadioThirdPlatformInitInter;


/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-02-13 15:27
 ******************************************/
public class KRadioThirdPlatformInitImpl implements KRadioThirdPlatformInitInter {


    @Override
    public boolean initThirdPlatform(Object... args) {
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001531080035?userId=1229522问题
        AudioStatusManager.getInstance().setNeedPlayServiceForeground(false);
        return true;
    }

    @Override
    public boolean destroyThirdPlatform(Object... args) {
        return false;
    }
}
