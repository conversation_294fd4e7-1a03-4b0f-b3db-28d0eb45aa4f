package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.app.ActivityManager;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Handler;
import androidx.annotation.RequiresApi;
import androidx.fragment.app.DialogFragment;
import androidx.appcompat.app.AppCompatActivity;
import android.util.Log;
import android.view.Gravity;

import com.kaolafm.kradio.common.event.StopAudioEBData;
import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.base.flavor.KRadioBackKeyInter;
import com.kaolafm.kradio.lib.base.flavor.SyncInstrumentInter;
import com.kaolafm.kradio.lib.dialog.Dialogs;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.player.logic.PlayerManager;

import org.greenrobot.eventbus.EventBus;

import java.util.List;

import cmgyunting.vehicleplayer.cnr.YunTingWidgetService;
import cmgyunting.vehicleplayer.cnr.widget.YunTingDophoinWidgetService;


/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-04-24 16:02
 ******************************************/
public final class KRadioBackKeyImpl implements KRadioBackKeyInter {

    private String TAG = "KRadioBackKeyImpl";

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    @Override
    public boolean onBackPressed(Object... args) {
        final AppCompatActivity activity = (AppCompatActivity) args[0];
        DialogFragment dialogFragment = new Dialogs.Builder()
                .setType(Dialogs.TYPE_2BTN)
                .setGravity(Gravity.CENTER)
                .setMessage(ResUtil.getString(R.string.exit_msg_str))
                .setLeftBtnText(ResUtil.getString(R.string.move_to_background_str))
                .setRightBtnText("\u3000" + ResUtil.getString(R.string.ok) + "\u3000")
                .setOnPositiveListener(dialog -> {
                    StopAudioEBData stopAudioEBData = new StopAudioEBData();
                    stopAudioEBData.canStopAudio = false;
                    EventBus.getDefault().post(stopAudioEBData);
                    setWidgetState();
//                    activity.finish();
                    dialog.dismiss();

//                    // activity的onDestroy没有执行，先手动执行destroy方法
//                    PlayerManagerHelper.getInstance().destroy();
                    //T226861 [Di4.0域控_3rd][功能缺陷][规划院外包-蓝立洪][HCE-汉7413_实车][量产][dilink4.0_canfd_e_dev][必现][自测][酷我音乐]后台播放酷我音乐，打开云听进入播放后，退出云听，酷我音乐不会自动恢复播放
                    PlayerManager.getInstance().abandonAudioFocus();
                    SyncInstrumentInter mSyncInstrumentInter = ClazzImplUtil.getInterSingleInstance("SyncInstrumentImpl");
                    Log.e(TAG, "mSyncInstrumentInter = " + mSyncInstrumentInter);
                    if (mSyncInstrumentInter != null) {
                        mSyncInstrumentInter.releaseSyncInstrument();
                    }
                    AppManager.getInstance().appExit();
                })
                .setOnNativeListener(dialog -> {
                    dialog.dismiss();
                    new Handler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            activity.moveTaskToBack(true);
                        }
                    }, 200);

                })
                .create();
        dialogFragment.show(activity.getSupportFragmentManager(), "clear_history");
        return true;
    }

    @Override
    public boolean appExit(Object... args) {
        Activity activity = AppManager.getInstance().getCurrentActivity();
        setWidgetState();
        Log.i(TAG, "activity:" + activity);
        if (activity == null) {
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    PlayerManager.getInstance().abandonAudioFocus();
                    AppManager.getInstance().appExitNoKill();
                }
            }, 500);
        } else {
            StopAudioEBData stopAudioEBData = new StopAudioEBData();
            stopAudioEBData.canStopAudio = true;
            EventBus.getDefault().post(stopAudioEBData);
        }
        return true;
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    @Override
    public void dealKillYunTingReceiver(Object... args) {
        // byd 收到kill广播 只处理状态
        setWidgetState();
        StopAudioEBData stopAudioEBData = new StopAudioEBData();
        stopAudioEBData.canStopAudio = true;
        EventBus.getDefault().post(stopAudioEBData);

        if (args.length > 1 && ((Boolean) args[1])) {
            // 销毁任务栈
            ActivityManager activityManager = (ActivityManager) AppDelegate.getInstance().getContext().getSystemService(Context.ACTIVITY_SERVICE);
            List<ActivityManager.AppTask> appTaskList = activityManager.getAppTasks();
            for (ActivityManager.AppTask appTask : appTaskList) {
//            appTask.setExcludeFromRecents(true);
                appTask.finishAndRemoveTask();
            }
        }
//        if (activity != null) {
//            activity.finish();
//            Log.i(TAG,"finish the activity:"+activity);
//        }else {
//            new Handler().postDelayed(new Runnable() {
//                @Override
//                public void run() {
//                    AppManager.getInstance().appExit();
//                    Log.i(TAG,"exit yunting app...");
//                }
//            }, 500);
//        }
    }

    private void setWidgetState() {
        //todo 海豚和普通widget合并为一个service进行处理（暂时为了规避新的bug，需修改后充分测试后再合并）
        Log.i(TAG, "setWidgetState");
        PlayerManagerHelper.getInstance().pause(false);

        //4.0普通widget服务
        Intent intent = new Intent(AppDelegate.getInstance().getContext(), YunTingWidgetService.class);
        intent.setAction(YunTingWidgetService.WIDGET_ACTION_EXIT);
//        AppDelegate.getInstance().getContext().startService(intent);
        startServiceCompat(AppDelegate.getInstance().getContext(), intent);

        //海豚widget服务
        Intent dophinIntent = new Intent(AppDelegate.getInstance().getContext(), YunTingDophoinWidgetService.class);
        intent.setAction(YunTingDophoinWidgetService.WIDGET_ACTION_EXIT);
//        AppDelegate.getInstance().getContext().startService(intent);
        startServiceCompat(AppDelegate.getInstance().getContext(), dophinIntent);
    }

    private void startServiceCompat(Context context, Intent intent) {
        Log.i(TAG, "startServiceCompat:SDK_VERSION" + Build.VERSION.SDK_INT);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.startForegroundService(intent);
        } else {
            context.startService(intent);
        }
    }


}
