package com.kaolafm.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Color;
import android.os.Build;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import android.util.AttributeSet;
import android.util.Log;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.ViewFlipper;

import com.kaolafm.kradio.k_kaolafm.R;


import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 **/
public class ScrollNumberView extends LinearLayout {

    private float textSize;
    private int textColor;
    private List<Integer> mNumbers;

    @RequiresApi(api = Build.VERSION_CODES.HONEYCOMB)
    public ScrollNumberView(Context context) {
        this(context, null);
    }

    @RequiresApi(api = Build.VERSION_CODES.HONEYCOMB)
    public ScrollNumberView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    @RequiresApi(api = Build.VERSION_CODES.HONEYCOMB)
    public ScrollNumberView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.ScrollNumberView);

        textSize = ta.getDimension(R.styleable.ScrollNumberView_snv_textSize, 0);
        textColor = ta.getColor(R.styleable.ScrollNumberView_snv_textColor, Color.parseColor("#ffffff"));
        int number = ta.getInteger(R.styleable.ScrollNumberView_snv_number, 0);
        mNumbers = splitNumber(number);
        initView();
    }

    private List<Integer> splitNumber(int num) {
        List<Integer> numbers = new ArrayList<>();

        String str = String.valueOf(num);
        char[] charArray = str.toCharArray();
        for (int i = 0; i < charArray.length; i++) {
            if (Character.isDigit(charArray[i])) {
                numbers.add(Integer.valueOf(charArray[i] - '0'));
            } else {
                //不参加动画
                numbers.add(Integer.valueOf(charArray[i]));
            }
        }

        return numbers;
    }

    private void initView() {
        for (int i = 0; i < mNumbers.size(); i++) {
            View textView = createView(mNumbers.get(i));
            addView(textView);
        }
    }

    private View createView(int num) {
        TextView tv = new TextView(getContext());
        tv.setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize);
        tv.setTextColor(textColor);
        tv.setText(String.valueOf(num));
        return tv;
    }


    public void update(int num) {
        List<Integer> numTo = splitNumber(num);
        List<Integer> numFrom = mNumbers;

        if (numTo.size() < numFrom.size()) {
            int size = numFrom.size() - numTo.size();

            for (int i = 0; i < size; i++) {
                numFrom.remove(0);
                removeViewAt(0);
            }
        }

        int ch = numTo.size() - numFrom.size();
        if (ch > 0) {
            for (int i = ch - 1; i >= 0; i--) {
                addView(createView(numTo.get(i)), 0);
                numFrom.add(0, numTo.get(i));
            }
        }


        for (int i = 0; i < numTo.size(); i++) {
            View childAt = getChildAt(i);
            int index = i;
            int from = numFrom.get(i);
            int to = numTo.get(i);

            if (from != to) {
                if (childAt != null && childAt instanceof TextView) {
                    ((ViewGroup) childAt.getParent()).removeView(childAt);
                    ((TextView) childAt).setText(String.valueOf(to));
                    from++;
                    if (from == 10) {
                        from = 0;
                    }
                    flipper(from, to, index, childAt);
                }
            }
        }

        mNumbers = numTo;
    }


    private void flipper(int from, int to, int index, View tv) {

        ViewFlipper mFlipper = new ViewFlipper(getContext());
        mFlipper.setInAnimation(AnimationUtils.loadAnimation(getContext(), R.anim.push_up_in));
        mFlipper.setOutAnimation(AnimationUtils.loadAnimation(getContext(), R.anim.push_up_out));

        LinearLayout.LayoutParams layoutParams = (LayoutParams) tv.getLayoutParams();
        this.addView(mFlipper, index, layoutParams);

        int tmp = to - from;
        final int cha = tmp < 0 ? tmp + 10 : tmp;


        for (int i = 0; i <= cha; i++) {
            int n = i + from;
            if (n >= 10) {
                n -= 10;
            }
            Log.i("novelot", "flipper: n=" + n);
            mFlipper.addView(createView(n));
        }

        //mFlipper.setFlipInterval(0);
        mFlipper.startFlipping();
        mFlipper.getInAnimation().setAnimationListener(new Animation.AnimationListener() {
            int chhh = cha;

            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {
                chhh--;
                Log.i("novelot", "onAnimationEnd:" + chhh);
                if (chhh < 0) {
                    mFlipper.stopFlipping();
                    int childCount = (ScrollNumberView.this).getChildCount();
                    if (childCount < index) {
                        return;
                    }
                    (ScrollNumberView.this).removeView(mFlipper);
                    (ScrollNumberView.this).addView(tv, index);
                } else {
                    mFlipper.showNext();
                }

            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });

    }

    public void reset(){
        mNumbers = splitNumber(0);
        removeAllViews();
        initView();
    }

    public void setTextColor(int textColor) {
        this.textColor = textColor;
        removeAllViews();
        initView();
    }
}
