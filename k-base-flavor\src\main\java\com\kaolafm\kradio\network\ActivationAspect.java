package com.kaolafm.kradio.network;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.lib.base.AppDelegate;
import java.util.Map;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;

/**
 * 该类是针对激活需要添加一个字段用于判断是新装用户还是由原来的车载基线听伴升级到K-radio的用户
 *
 * <AUTHOR>
 * @date 2019-08-28
 */
@Aspect
public class ActivationAspect {

//    @Around("execution(* com.kaolafm.opensdk.api.init.InitRequest.getInitParams(..))")
//    public Object addIsUpdateParam(ProceedingJoinPoint point) throws Throwable {
//        Map params = (Map) point.proceed();
//        if (!ListUtil.isEmpty(params)) {
//            boolean tingBanToKRadio = TingBanToKRadioUtil.isTingBanToKRadio(AppDelegate.getInstance().getContext());
//            params.put("isUpgrade", tingBanToKRadio ? "1" : "0");
//        }
//        return params;
//    }
}
