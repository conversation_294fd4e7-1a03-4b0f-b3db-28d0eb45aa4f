<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/all_category_top_guideline_skeletion"
        style="@style/secondary_page_top_guideline" />

    <!--留出来一个洞,以便能够透过该布局,点击到下边的返回按钮-->
    <FrameLayout
        android:id="@+id/iv_all_category_close_skeletion"
        style="@style/SkeletonAllCategory"
        app:layout_constraintBottom_toTopOf="@id/all_category_top_guideline_skeletion"
        app:layout_constraintLeft_toLeftOf="parent" />

    <TextView
        android:id="@+id/tab_item_0"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@drawable/skeleton_bg"
        android:textSize="@dimen/nav_bar_tv_unselected"
        app:layout_constraintBottom_toTopOf="@id/all_category_top_guideline_skeletion"
        app:layout_constraintLeft_toRightOf="@id/iv_all_category_close_skeletion"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_default="percent"
        app:layout_constraintWidth_percent="0.5"
        tools:layout_editor_absoluteX="8dp"
        tools:layout_editor_absoluteY="0dp" />

    <TextView
        android:id="@+id/tab_sub_skeletion"
        android:layout_width="100dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/x90"
        android:layout_marginTop="@dimen/y46"
        android:background="@drawable/skeleton_bg"
        android:textSize="@dimen/text_size4"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/all_category_top_guideline_skeletion" />

    <TextView
        android:id="@+id/tab_sub_1_skeletion"
        android:layout_width="100dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/x36"
        android:layout_marginTop="@dimen/y46"
        android:background="@drawable/skeleton_bg"
        android:textSize="@dimen/text_size4"
        app:layout_constraintLeft_toRightOf="@id/tab_sub_skeletion"
        app:layout_constraintTop_toBottomOf="@id/all_category_top_guideline_skeletion" />

    <View
        android:id="@+id/view_divider_skeletion"
        android:layout_width="match_parent"
        android:layout_height="@dimen/y1"

        android:background="@color/color_white_10_transparent"
        app:layout_constraintTop_toBottomOf="@id/tab_sub_skeletion" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginLeft="@dimen/x90"
        android:layout_marginTop="@dimen/y70"
        android:layout_marginRight="@dimen/x90"
        android:layout_marginBottom="@dimen/y80"

        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/view_divider_skeletion">

        <View
            android:layout_width="@dimen/x60"
            android:layout_height="match_parent"
            android:layout_marginRight="@dimen/m6"
            android:background="@drawable/skeleton_bg" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginRight="@dimen/m6"
            android:orientation="vertical">

            <com.kaolafm.kradio.lib.widget.square.SquareHeightFrameLayout
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_marginBottom="@dimen/m6"
                android:layout_weight="1"
                android:background="@drawable/skeleton_bg" />

            <com.kaolafm.kradio.lib.widget.square.SquareHeightFrameLayout
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:background="@drawable/skeleton_bg" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <com.kaolafm.kradio.lib.widget.square.SquareHeightFrameLayout
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_marginRight="@dimen/m6"
                android:layout_marginBottom="@dimen/m6"
                android:layout_weight="1"
                android:background="@drawable/skeleton_bg" />

            <com.kaolafm.kradio.lib.widget.square.SquareHeightFrameLayout
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:background="@drawable/skeleton_bg" />
        </LinearLayout>

        <View
            android:layout_width="@dimen/x60"
            android:layout_height="match_parent"
            android:layout_marginLeft="@dimen/m30"
            android:layout_marginRight="@dimen/m6"
            android:background="@drawable/skeleton_bg"
            android:visibility="gone" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginRight="@dimen/m6"
            android:orientation="vertical">

            <com.kaolafm.kradio.lib.widget.square.SquareHeightFrameLayout
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_marginBottom="@dimen/m6"
                android:layout_weight="1"
                android:background="@drawable/skeleton_bg" />

            <com.kaolafm.kradio.lib.widget.square.SquareHeightFrameLayout
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:background="@drawable/skeleton_bg" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <com.kaolafm.kradio.lib.widget.square.SquareHeightFrameLayout
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_marginRight="@dimen/m6"
                android:layout_marginBottom="@dimen/m6"
                android:layout_weight="1"
                android:background="@drawable/skeleton_bg" />

            <com.kaolafm.kradio.lib.widget.square.SquareHeightFrameLayout
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:background="@drawable/skeleton_bg" />
        </LinearLayout>
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
