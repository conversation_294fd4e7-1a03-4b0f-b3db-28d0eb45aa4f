<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="78px"
    android:layout_gravity="center"
    android:background="@drawable/shape_hw_toast_layout_bg"
    android:gravity="center">

    <TextView
        android:id="@+id/toast_msg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingLeft="40px"
        android:paddingTop="20px"
        android:paddingRight="40px"
        android:paddingBottom="20px"
        android:textColor="#ffffff"
        android:textSize="30px" />
</LinearLayout>
