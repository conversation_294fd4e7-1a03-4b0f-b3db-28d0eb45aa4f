<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/launcher_main_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_home"
        android:paddingLeft="@dimen/activity_root_view_padding_left">

        <FrameLayout
            android:id="@+id/fl_launcher_root_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginLeft="@dimen/activity_root_view_margin_left"
            android:layout_marginBottom="@dimen/activity_root_view_margin_bottom" />

        <ViewStub
            android:id="@+id/water_mark_viewStub"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout="@layout/water_mark_layout" />

        <ViewStub
            android:id="@+id/app_title_viewStub"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout="@layout/common_text" />
    </FrameLayout>
</androidx.cardview.widget.CardView>

