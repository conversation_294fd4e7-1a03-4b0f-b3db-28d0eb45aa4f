package com.kaolafm.kradio.common.http.api.feedback;

import com.kaolafm.opensdk.api.KaolaApiConstant;

/**
 * <AUTHOR> on 2019-07-18.
 */

public class FeedbackRequestConstant {

    /**
     * 负反馈
     */
    public static final String DOMAIN_NAME_MINUS_FEED_BACK = "MINUS_FEED_BACK";
    public static final String DOMAIN_HEADER_MINUS_FEED_BACK = "Domain-Name:" + DOMAIN_NAME_MINUS_FEED_BACK;

    private static final String VERSION = KaolaApiConstant.KAOLA_VERSION;
    /**
     * 负反馈
     */
    public static final String REQUEST_GET_MINUS_FEED_BACK = "/radioStreamFeedBack";

    /**
     * AI 电台反馈地址
     */
    public static final String AI_RADIO_FEED_BACK = "https://rec.kaolafm.com/";

    /**
     * 意见反馈更新微信二维码接口
     */
    public static final String GET_FEEDBACK_WX_QRCODE = VERSION + "/group/kradioQRCode";


    public static final String KEY_DEVICE_ID = "deviceId";
    public static final String KEY_UID = "uid";
    public static final String KEY_ALBUM_ID = "albumId";
}
