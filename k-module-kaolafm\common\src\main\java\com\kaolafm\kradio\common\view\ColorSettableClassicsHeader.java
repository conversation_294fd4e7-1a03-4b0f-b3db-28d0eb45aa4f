package com.kaolafm.kradio.common.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import androidx.annotation.ColorInt;
import android.util.AttributeSet;
import android.view.View;

import com.kaolafm.kradio.lib.R;
import com.scwang.smartrefresh.layout.header.ClassicsHeader;

import java.util.HashMap;

import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.Nullable;


public class ColorSettableClassicsHeader extends ClassicsHeader {
    @ColorInt
    private int mTextColorTitle;
    @ColorInt
    private int mTextColorTime;
    @ColorInt
    private int mProgressColor;
    @ColorInt
    private int mArrowColor;
    private HashMap _$_findViewCache;

    public final void setTextColorTitle(@ColorInt int color) {
        this.mTitleText.setTextColor(color);
    }

    public final void setTextColorTime(@ColorInt int color) {
        this.mLastUpdateText.setTextColor(color);
    }

    public final void setProgressColor(@ColorInt int color) {
        if (this.mProgressDrawable != null) {
            this.mProgressDrawable.setColor(color);
            this.mProgressView.invalidateDrawable((Drawable)this.mProgressDrawable);
        }

    }

    public final void setArrowColor(@ColorInt int color) {
        if (this.mArrowDrawable != null) {
            this.mArrowDrawable.setColor(color);
            this.mArrowView.invalidateDrawable((Drawable)this.mArrowDrawable);
        }

    }

    public ColorSettableClassicsHeader(@Nullable Context context) {
        this(context, (AttributeSet)null);
    }

    public ColorSettableClassicsHeader(@Nullable Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        this.mTextColorTitle = -1;
        this.mTextColorTime = -1;
        this.mProgressColor = -1;
        this.mArrowColor = -1;
        if (context != null) {
            TypedArray var10000 = context.obtainStyledAttributes(attrs, R.styleable.ColorSettableClassicsHeader);
            if (var10000 != null) {
                TypedArray ta = var10000;
                if (ta == null) {
                    Intrinsics.throwNpe();
                }

                this.mTextColorTitle = ta.getColor(R.styleable.ColorSettableClassicsHeader_srlTextColorTitle, -1);
                this.mTextColorTime = ta.getColor(R.styleable.ColorSettableClassicsHeader_srlTextColorTime, -1);
                this.mProgressColor = ta.getColor(R.styleable.ColorSettableClassicsHeader_srlProgressColor, -1);
                this.mArrowColor = ta.getColor(R.styleable.ColorSettableClassicsHeader_srlArrowColor, -1);
                ta.recycle();
                if (this.mTextColorTitle != -1) {
                    this.setTextColorTitle(this.mTextColorTitle);
                }

                if (this.mTextColorTime != -1) {
                    this.setTextColorTime(this.mTextColorTime);
                }

                if (this.mProgressColor != -1) {
                    this.setProgressColor(this.mProgressColor);
                }

                if (this.mArrowColor != -1) {
                    this.setArrowColor(this.mArrowColor);
                }

                return;
            }
        }

    }

    public View _$_findCachedViewById(int var1) {
        if (this._$_findViewCache == null) {
            this._$_findViewCache = new HashMap();
        }

        View var2 = (View)this._$_findViewCache.get(var1);
        if (var2 == null) {
            var2 = this.findViewById(var1);
            this._$_findViewCache.put(var1, var2);
        }

        return var2;
    }

    public void _$_clearFindViewByIdCache() {
        if (this._$_findViewCache != null) {
            this._$_findViewCache.clear();
        }

    }
}
