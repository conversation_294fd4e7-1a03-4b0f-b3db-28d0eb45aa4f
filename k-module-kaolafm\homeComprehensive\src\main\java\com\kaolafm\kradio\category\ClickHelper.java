package com.kaolafm.kradio.category;

import android.os.Bundle;
import android.util.Log;

import com.kaolafm.kradio.lib.bean.SubcategoryItemBean;
import com.kaolafm.kradio.lib.report.ReportParamUtil;
import com.kaolafm.kradio.lib.report.ReportUtil;
import com.kaolafm.kradio.component.ui.base.utils.ComponentUtils;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.event.PagerJumpEvent;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.report.util.ReportParameterManager;

import org.greenrobot.eventbus.EventBus;

/**
 * <AUTHOR>
 **/
public class ClickHelper {

    public static void onClick(SubcategoryItemBean subcategoryItemBean, int position) {
        onClick(subcategoryItemBean, position, false);
    }

    public static void onClick(SubcategoryItemBean subcategoryItemBean, int position, boolean needHandlePlaying) {
        onClickBroadcast(subcategoryItemBean, position, needHandlePlaying);
    }

    private static void onClickBroadcast(SubcategoryItemBean subcategoryItemBean, int position, boolean needHandlePlaying) {
        Log.i("handleClickCurPlayItem", "ClickHelper -> onClickBroadcast -> needHandlePlaying=" + needHandlePlaying);
        if (PlayerManagerHelper.getInstance().isPlayCurrentRadio(String.valueOf(subcategoryItemBean.getId()))) {
            Log.i("handleClickCurPlayItem", "ClickHelper -> onClickBroadcast -> isPlayCurrentRadio=true");
            // 只针对正方形的卡片做处理
            if (needHandlePlaying){
                boolean isPlaying = PlayerManagerHelper.getInstance().isPlaying();
                Log.i("handleClickCurPlayItem", "ClickHelper -> onClickBroadcast -> isPlaying=" + isPlaying);
                if (isPlaying) {
                    EventBus.getDefault().post(new PagerJumpEvent(PagerJumpEvent.PAGE_PLAYER_DETAIL, null));
                    return;
                }
            } else {
                return;
            }
        }
        switch (subcategoryItemBean.getItemType()) {
            case SubcategoryItemBean.TYPE_ITEM_BROADCAST_CATEGORY:
                reportContentClickEvent(subcategoryItemBean, position);
                Bundle bundle = new Bundle();
                bundle.putInt("id", (int) subcategoryItemBean.getId());
                bundle.putString("name", subcategoryItemBean.getName());
                bundle.putBoolean("isLeafCategory", subcategoryItemBean.isLeafCategory());
                EventBus.getDefault().post(new PagerJumpEvent(PagerJumpEvent.PAGE_BROADCAST_LIST, bundle));
                break;
            case SubcategoryItemBean.TYPE_ITEM_BROADCAST_LOCAL:
                reportContentClickEvent(subcategoryItemBean, position);
                PlayerManagerHelper.getInstance().start(String.valueOf(subcategoryItemBean.getId()), PlayerConstants.RESOURCES_TYPE_BROADCAST);
                break;
            case SubcategoryItemBean.TYPE_ITEM_TV:
                reportContentClickEvent(subcategoryItemBean, position);
                PlayerManagerHelper.getInstance().start(String.valueOf(subcategoryItemBean.getId()), PlayerConstants.RESOURCES_TYPE_TV);
                break;
            case SubcategoryItemBean.TYPE_ITEM_ALBUM: {
                //订阅的item,点击会走到这里.
                //判断是否下线,并提示.
                reportContentClickEvent(subcategoryItemBean, position);
                if (subcategoryItemBean.isOnline()) {
                    PlayerManagerHelper.getInstance().start(String.valueOf(subcategoryItemBean.getId()), PlayerConstants.RESOURCES_TYPE_ALBUM);
                } else {
                    ToastUtil.showNormal(AppDelegate.getInstance().getContext(), ResUtil.getString(R.string.is_not_online));
                }
            }
            break;
            case SubcategoryItemBean.TYPE_ITEM_RADIO_CHANNEL:
                reportContentClickEvent(subcategoryItemBean, position);
                PlayerManagerHelper.getInstance().start(String.valueOf(subcategoryItemBean.getId()), PlayerConstants.RESOURCES_TYPE_RADIO);
                break;
            case SubcategoryItemBean.TYPE_ITEM_TITLE:
            case SubcategoryItemBean.TYPE_ITEM_BUTTON:
                // Do-Nothing
                break;
            default:
        }
    }

    private static void reportContentClickEvent(SubcategoryItemBean bean, int position) {
        ReportUtil.addContentClickEvent("", ReportParamUtil.getRadioType(bean), "",
                String.valueOf(bean.getId()), "",
                Constants.PAGE_ID_CATEGORY, bean.getParentCode(), "");

        String tag = ComponentUtils.getInstance().getReportTag(bean.getFreq(), bean.getVip());
        ReportUtil.addComponentShowAndClickEvent("",
                true, "9"
                , 2, bean.getParentCode(), position + "",
                0 + "", String.valueOf(bean.getId())
                , tag, ReportParameterManager.getInstance().getPage(), tag);
    }


}
