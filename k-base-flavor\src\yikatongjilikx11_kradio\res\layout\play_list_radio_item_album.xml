<?xml version="1.0" encoding="utf-8"?>

<com.kaolafm.kradio.component.ui.base.view.ScaleConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/radio_item_main_layout"
    android:layout_width="match_parent"
    android:layout_height="@dimen/y120"
    android:layout_marginBottom="@dimen/y15"
    android:background="@drawable/player_radio_item_root_layout_bg">

    <com.kaolafm.kradio.common.widget.GradientProgressBar
        android:id="@+id/radio_list_item_progress_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/player_radio_item_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:progressDrawable="@drawable/player_bar_progress_bg" />


    <LinearLayout
        android:id="@+id/radio_list_item_title_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingLeft="@dimen/x30"
        app:layout_constraintBottom_toTopOf="@+id/radio_list_item_time_text"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed">

        <ImageView
            android:id="@+id/radio_list_item_playing_icon"
            android:layout_width="@dimen/player_list_playing_bar_size"
            android:layout_height="@dimen/player_list_playing_bar_size"
            android:layout_gravity="bottom"
            android:layout_marginRight="@dimen/x10"
            android:layout_marginBottom="@dimen/y5"
            android:background="@drawable/playing_bar_chart" />

        <TextView
            android:id="@+id/radio_list_item_title_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:textSize="@dimen/text_size_title4"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="@color/player_radio_item_title_text_color" />
    </LinearLayout>

    <TextView
        android:id="@+id/radio_list_item_time_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:maxLines="1"
        android:paddingLeft="@dimen/x30"
        android:textColor="@color/player_radio_item_subtitle_text_color"
        android:textSize="@dimen/text_size_title5"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintTop_toBottomOf="@+id/radio_list_item_title_layout" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/progressBlock"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/progressBlockHeight"
            android:layout_alignParentTop="true"
            android:background="@drawable/block"
            android:gravity="center"
            android:paddingLeft="@dimen/x6"
            android:paddingRight="@dimen/x6"
            android:singleLine="true"
            android:textColor="@color/text_color_1"
            android:textSize="@dimen/text_size1"
            android:visibility="gone" />
    </LinearLayout>
</com.kaolafm.kradio.component.ui.base.view.ScaleConstraintLayout>

