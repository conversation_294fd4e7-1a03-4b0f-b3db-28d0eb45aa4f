package com.kaolafm.kradio.util;

import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Matrix;
import android.os.Handler;
import android.util.Base64;
import android.util.Log;

import com.alibaba.fastjson.JSONObject;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;

import mega.car.MegaCarPropHelper;
import mega.car.config.Media;
import mega.car.hardware.CarPropertyValue;

public class DashboardUtil {

    private static final String TAG = "DashboardUtil";

    private static DashboardUtil instance;

    private MegaCarPropHelper helper;

    private DashboardUtil() {
    }

    public void init(Activity activity) {
        try {
            Log.i(TAG, "begin init");
            helper = MegaCarPropHelper.getInstance(activity, null);
        } catch (Exception | Error e) {
            Log.e(TAG, "init:" + e.toString());
        }
    }

    public static DashboardUtil getInstance() {
        if (instance == null) {
            synchronized (DashboardUtil.class) {
                if (instance == null) {
                    instance = new DashboardUtil();
                }
            }
        }
        return instance;
    }

    /**
     * 歌曲信息
     * ID Media.ID_MEDIA_DATA
     * access WRITE
     * type JSON String
     * params {
     * "title":"That girl", //􏰗曲名
     * "author":"zhangsan", //􏰗手名
     * "stream_type":0-7, //媒体􏰘􏰇型: 0 􏰖􏰙异常媒体􏰇型; 1 􏰖􏰙 QQ 􏰚乐; 2 􏰖􏰙喜􏰛拉􏰜; 3 􏰖􏰙 AM􏰝4 􏰖􏰙 FM; 5 􏰖􏰙􏰞􏰟􏰚乐; 6 􏰖􏰙 USB 􏰚乐,7 􏰠􏰡􏰚乐
     * "duration":236//􏰗曲总时􏰢􏰝单位s
     * }
     */
    public void sendMediaData(String title, String author, int duration) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("title", title);
            jsonObject.put("author", author);
            jsonObject.put("stream_type", 9);
            jsonObject.put("duration", duration);
            String jsonString = jsonObject.toJSONString();
            sendDataToService(Media.ID_MEDIA_DATA, jsonString);
        } catch (Exception e) {
            Log.i(TAG, "mediaData:" + e.toString());
        }
    }

    /**
     * ID Media.ID_MEDIA_PICTURE
     * access WRITE
     * type String
     * params
     * 140*140 大小􏰫 png 格式图􏰪􏰝􏰬 base64 􏰭􏰮后􏰫字􏰯串
     */
    public void sendMediaPic(String imageUrl) {
        boolean isPng = imageUrl.contains("png");
        new Thread(() -> {
            URL imageurl = null;
            try {
                //转bitmap
                imageurl = new URL(imageUrl);
                HttpURLConnection conn = (HttpURLConnection) imageurl.openConnection();
                conn.setDoInput(true);
                conn.connect();
                InputStream is = conn.getInputStream();
                Bitmap bitmap = BitmapFactory.decodeStream(is);
                is.close();

                //压缩140x140
                int width = bitmap.getWidth();
                int height = bitmap.getHeight();
                Log.i(TAG, "oldWidth:" + width);
                Log.i(TAG, "oldHeight:" + height);
                int newWidth = 140;
                int newHeight = 140;
                float scaleWidth = ((float) newWidth) / width;
                float scaleHeight = ((float) newHeight) / height;
                Matrix matrix = new Matrix();
                matrix.postScale(scaleWidth, scaleHeight);
                bitmap = Bitmap.createBitmap(bitmap, 0, 0, width, height, matrix, true);
                Log.i(TAG, "newWidth" + bitmap.getWidth());
                Log.i(TAG, "newHeight" + bitmap.getHeight());

                //转base64String
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                bitmap.compress(isPng ? Bitmap.CompressFormat.PNG : Bitmap.CompressFormat.JPEG, 100, baos);
                baos.flush();
                baos.close();
                byte[] bitmapBytes = baos.toByteArray();
                String result = Base64.encodeToString(bitmapBytes, Base64.DEFAULT);
                Log.i(TAG, "base64:" + result.substring(0, 100));

                //发送到服务
                sendDataToService(Media.ID_MEDIA_PICTURE, result);
            } catch (Exception e) {
                Log.e(TAG, "sendMediaPic:" + e.toString());
            }
        }).start();

    }

    /**
     * ID Media.ID_MEDIA_PLAYBACK_STATE
     * access WRITE
     * type JSON String
     * params {
     * "player_status":-1|0|1|2, //播放􏰊态:-1 􏰖􏰙 error; 0 􏰖􏰙 paused; 1 􏰖􏰙 playing; 2 􏰖􏰙􏰤冲
     * " progress":0~1 //播放􏰣度􏰥分􏰦􏰝􏰧􏰨型。
     * }
     */
    public static int BUFFER = 2;
    public static int ERROR = -1;
    public static int PAUSED = 0;
    public static int PLAYING = 1;

    public void sendPlayState(int status) {
        sendPlayState(status, -1);
    }

    public void sendPlayState(int status, double percent) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("player_status", status);
            if (percent >= 0 && percent <= -1)
                jsonObject.put("progress", percent);
            String jsonString = jsonObject.toJSONString();
            sendDataToService(Media.ID_MEDIA_PLAYBACK_STATE, jsonString);
        } catch (Exception e) {
            Log.i(TAG, "sendPlayState:" + e.toString());
        }
    }

    private void sendDataToService(int id, String data) {
        if (helper == null) {
            init(AppManager.getInstance().getCurrentActivity());
        }
        if (helper == null) {
            return;
        }
        CarPropertyValue<String> carPropertyValue = new CarPropertyValue<>(id, data);
        helper.setRawProp(carPropertyValue);
    }
}
