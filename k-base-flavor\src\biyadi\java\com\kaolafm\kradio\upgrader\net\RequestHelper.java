package com.kaolafm.kradio.upgrader.net;

import android.util.Log;


import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.upgrader.BYDUpgrader;
import com.kaolafm.kradio.upgrader.net.api.RequestService;
import com.kaolafm.kradio.upgrader.net.model.QueryRequestData;
import com.kaolafm.kradio.upgrader.net.model.QueryVersionData;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import okhttp3.Request;

import okhttp3.RequestBody;
import okhttp3.ResponseBody;
import okhttp3.logging.HttpLoggingInterceptor;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

/**
 * <AUTHOR> on 2019/4/1.
 */

public class RequestHelper {
    private final static String TAG = "RequestHelper";
    private volatile static RequestHelper requestHelper;
    private Retrofit mRetrofit;
    private RequestService requestService;
    public static boolean isWork = false;

    private RequestHelper() {

    }

    public static RequestHelper getInstance() {
        if (requestHelper == null) {
            synchronized (RequestHelper.class) {
                if (requestHelper == null) {
                    requestHelper = new RequestHelper();
                }
            }
        }
        return requestHelper;
    }

    public void init() {
        HttpLoggingInterceptor loggingInterceptor = new HttpLoggingInterceptor(new HttpLoggingInterceptor.Logger() {
            @Override
            public void log(String message) {
                Log.i(TAG, message);
            }
        }).setLevel(HttpLoggingInterceptor.Level.BODY);

        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(RequestConstants.DEFAULT_NETWORK_TIMEOUT, TimeUnit.SECONDS)
                .readTimeout(RequestConstants.DEFAULT_NETWORK_TIMEOUT, TimeUnit.SECONDS)
                .addInterceptor(loggingInterceptor)
                .addInterceptor(new Interceptor() {
                    @Override
                    public okhttp3.Response intercept(Chain chain) throws IOException {
                        Request original = chain.request();
//                        Log.i(TAG, "intercept: " + original.toString());
//                        Log.i(TAG, "intercept: " + original.body().toString());
//                        Log.i(TAG, "intercept: " + original.headers().toString());
                        return chain.proceed(original);
                    }
                })
                .build();


        mRetrofit = new Retrofit.Builder()
                .baseUrl(RequestConstants.BASE_URL_TEST_HOST)
                .client(client)
                .addConverterFactory(GsonConverterFactory.create())
                .build();
        requestService = mRetrofit.create(RequestService.class);
    }


    //版本升级请求
    public void addQueryVersionRequest(Map<String, String> headers, QueryRequestData queryRequestData, boolean isAccord) {
        isWork = true;
        RequestBody requestBody = new BYDRequest().getQuery(queryRequestData);
        Call<QueryVersionData> call = requestService.requestQueryVersion(headers, requestBody);
        call.enqueue(new Callback<QueryVersionData>() {
            @Override
            public void onResponse(Call<QueryVersionData> call, Response<QueryVersionData> response) {
                isWork = false;
                Log.i(TAG, " 请求返回成功: " + response.body().getMsg());
                QueryVersionData mQueryVersionData = response.body();
                if (response.isSuccessful()) {
                    if (mQueryVersionData != null) {
                        BYDUpgrader.checkUp(mQueryVersionData);
                    }
                }
            }

            @Override
            public void onFailure(Call<QueryVersionData> call, Throwable t) {
                isWork = false;
                if (isAccord) {
                    ToastUtil.showOnly(AppDelegate.getInstance().getContext(), AppDelegate.getInstance().getContext().getResources().getString(R.string.allready_lastest_version));
                }
                Log.i(TAG, " 请求返回失败:" + t.getMessage());
            }
        });

    }






}
