package com.kaolafm.kradio.flavor.impl;

import android.content.pm.PackageManager;
import android.hardware.bydauto.audio.BYDAutoAudioDevice;
import android.hardware.bydauto.instrument.BYDAutoInstrumentDevice;
import android.media.AudioManager;
import androidx.core.content.ContextCompat;
import android.text.TextUtils;
import android.util.Log;


import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.SyncInstrumentInter;
import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.IPlayerStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;


public class SyncInstrumentImpl implements SyncInstrumentInter, IPlayerStateListener, OnAudioFocusChangeInter {
    private static final String TAG = "k.byd.sii";
    private BYDAutoInstrumentDevice mBYDAutoInstrumentDevice;
    public static boolean isFormRelease = false;
    public static boolean isFormShutDown = false;
    private PlayerManager mPlayerManager;
    private IPlayerInitCompleteListener onPlayerInitCompleteListener = new IPlayerInitCompleteListener() {
        @Override
        public void onPlayerInitComplete(boolean b) {
            try {
                PlayerManager.getInstance().removePlayerInitComplete(onPlayerInitCompleteListener);
                PlayerManager.getInstance().addPlayControlStateCallback(SyncInstrumentImpl.this);
                Log.i(TAG, "onPlayerInitComplete init mBYDAutoInstrumentDevice");
                mBYDAutoInstrumentDevice = BYDAutoInstrumentDevice.getInstance(AppDelegate.getInstance().getContext());
                Log.i(TAG, "onPlayerInitComplete init mBYDAutoInstrumentDevice result " + (mBYDAutoInstrumentDevice == null));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    };

    private void initPlayer() {
        PlayerManager playerManager = PlayerManager.getInstance();
        boolean isInitSuccess = playerManager.isPlayerInitSuccess();
        if (isInitSuccess) {
            try {
                mPlayerManager.addPlayControlStateCallback(this);
                mBYDAutoInstrumentDevice = BYDAutoInstrumentDevice.getInstance(AppDelegate.getInstance().getContext());
                Log.i(TAG, "initPlayer mBYDAutoInstrumentDevice is null " + (mBYDAutoInstrumentDevice == null) + " ,mBYDAutoAudioDevice is null " + (mBYDAutoInstrumentDevice == null));

            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            playerManager.addPlayerInitComplete(onPlayerInitCompleteListener);
//            playerManager.setupPlayer();
        }
    }

    @Override
    public boolean initSyncInstrument() {
        //初始化注册监听接口
        isFormRelease = false;
        mPlayerManager = PlayerManager.getInstance();
        initPlayer();
        PlayerManager.getInstance().addAudioFocusListener(this);
        Log.i(TAG, "requestAudioFocus------------->flag initSyncInstrument");
        return true;
    }

    @Override
    public boolean releaseSyncInstrument() {
        //反注册监听接口
        if (mBYDAutoInstrumentDevice == null) {
            mBYDAutoInstrumentDevice = BYDAutoInstrumentDevice.getInstance(AppDelegate.getInstance().getContext());
            if (mBYDAutoInstrumentDevice != null) {
                Log.v(TAG, "releaseSyncInstrument: BYDAutoInstrumentDevice.MUSIC_STOP");
                isFormRelease = true;
                int rst = ContextCompat.checkSelfPermission(AppDelegate.getInstance().getContext(), "android.permission.BYDAUTO_INSTRUMENT_SET");
                Log.v(TAG, "授权rst=" + rst);
                if (rst == PackageManager.PERMISSION_GRANTED) {
                    mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_STOP);
                } else {
                    Log.v(TAG, "未授权BYDAUTO_INSTRUMENT_SET.");
                }
            }
        } else {
            Log.v(TAG, "releaseSyncInstrument: BYDAutoInstrumentDevice.MUSIC_STOP");
            isFormRelease = true;
            mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_STOP);
        }
        Log.v(TAG, "releaseSyncInstrument: isFormRelease = " + isFormRelease);

        PlayerManager.getInstance().removePlayControlStateCallback(this);
        PlayerManager.getInstance().removeAudioFocusListener(this);

        //注销AutoPlayService
//        Intent intent = new Intent(AppDelegate.getInstance().getContext(), AutoPlayService.class);
//        AppDelegate.getInstance().getContext().stopService(intent);
        return true;
    }

    @Override
    public void onIdle(PlayItem playItem) {
//        try {
//            Log.v(TAG, "onIdle: BYDAutoInstrumentDevice.MUSIC_PAUSE");
//            isFormRelease = false;
//            mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_PAUSE);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
    }

    @Override
    public void onPlayerPreparing(PlayItem playItem) {
        try {
            Log.v(TAG, "onPlayerPreparing: playItem.getTitle() = " + playItem.getTitle());
            mBYDAutoInstrumentDevice.sendMusicName(playItem.getTitle());
            mBYDAutoInstrumentDevice.sendMusicPlaybackProgress(0);
        } catch (Exception e) {
        }
    }

    @Override
    public void onPlayerPlaying(PlayItem playItem) {
        try {
            Log.v(TAG, "onPlayerPlaying: playItem.getTitle() = " + playItem.getTitle());
            Log.v(TAG, "onPlayerPlaying: BYDAutoInstrumentDevice.MUSIC_PLAY ");
            mBYDAutoInstrumentDevice.sendMusicName(playItem.getTitle());
            mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_PLAY);
        } catch (Exception e) {
        }
    }

    @Override
    public void onPlayerPaused(PlayItem playItem) {
        try {
            Log.v(TAG, "onPlayerPaused: isFormRelease = " + isFormRelease);
            if (PlayerManager.getInstance().getCurrentAudioFocusStatus() < 0) {
                if (!isFormRelease) {
                    Log.v(TAG, "onPlayerPaused: BYDAutoInstrumentDevice.MUSIC_STOP ");
                    mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_STOP);
                    return;
                }
                isFormRelease = false;
            } else {
                if (isFormRelease) {
                    Log.v(TAG, "onPlayerPaused: BYDAutoInstrumentDevice.MUSIC_STOP ");
                    mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_STOP);
                    isFormRelease = false;
                } else {
                    Log.v(TAG, "onPlayerPaused: BYDAutoInstrumentDevice.MUSIC_PAUSE ");
                    mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_PAUSE);
                }
            }
        } catch (Exception e) {
        }
    }

    @Override
    public void onProgress(PlayItem playItem, long progress, long l1) {
        try {
            Log.i(TAG, "onProgress: " + progress / 1000);
            sendPlayingAndAllTime((int) progress, (int) l1);
//            mBYDAutoInstrumentDevice.sendMusicPlaybackProgress(progress / 1000);
            if (mBYDAutoInstrumentDevice != null) {
                mBYDAutoInstrumentDevice.sendMusicPlaybackProgress((int) progress / 1000);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }



    @Override
    public void onPlayerFailed(PlayItem playItem, int i, int i1) {
        try {
            Log.v(TAG, "onPlayerFailed: BYDAutoInstrumentDevice.MUSIC_PAUSE");
            mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_PAUSE);
        } catch (Exception e) {
        }
    }

    @Override
    public void onPlayerEnd(PlayItem playItem) {
        try {
            Log.v(TAG, "onPlayerEnd: BYDAutoInstrumentDevice.MUSIC_PAUSE");
            mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_PAUSE);
        } catch (Exception e) {
        }
    }

    @Override
    public void onSeekStart(PlayItem playItem) {

    }

    @Override
    public void onSeekComplete(PlayItem playItem) {

    }



    @Override
    public void onBufferingStart(PlayItem playItem) {

    }

    @Override
    public void onBufferingEnd(PlayItem playItem) {

    }

    @Override
    public void onDownloadProgress(PlayItem playItem, long l, long l1) {

    }

    @Override
    public void onAudioFocusChange(int i) {
        Log.v(TAG, "onAudioFocusChange: AudioFocus = " + i);
        if (i < 0) {
            try {
                Log.v(TAG, "onAudioFocusChange: BYDAutoInstrumentDevice.MUSIC_STOP");
                mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_STOP);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            try {
                if (i == AudioManager.AUDIOFOCUS_GAIN) {
                    //focus gain 的时候发一遍所有信息。
                    mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_PAUSE);
                    PlayItem currentPlayItem = PlayerManager.getInstance().getCurPlayItem();
                    //  解决https://app.huoban.com/tables/2100000007530121/items/2300001793949439?userId=1229522问题
                    if (currentPlayItem != null) {
                        String title = currentPlayItem.getTitle();
                        if (TextUtils.isEmpty(title)) {
                            title = "";
                        }
                        mBYDAutoInstrumentDevice.sendMusicName(title);

                        int position = currentPlayItem.getPosition();
                        mBYDAutoInstrumentDevice.sendMusicPlaybackProgress(position);
                        int duration = currentPlayItem.getDuration();
                        sendPlayingAndAllTime(position, duration);
                    }

                    //source: MUSIC_SOURCE_VIDE O = 8; MUSIC_SOURCE_KUW O =9; MUSIC_SOURCE_KOAL A = 10; MUSIC_SOURCE_OTHE RS= 11;
                    Log.i(TAG, "onAudioFocusChange sendMusicSource start");
                    mBYDAutoInstrumentDevice.sendMusicSource(10);
                } else {
                    Log.i(TAG, "onAudioFocusChange: BYDAutoInstrumentDevice.MUSIC_PAUSE");
                    mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_PAUSE);
                }
            } catch (Exception e) {
                Log.i(TAG, "onAudioFocusChange error " + e.getLocalizedMessage());
            }
        }
    }

    /**
     * 发送总时长及进度时长
     *
     * @param position
     * @param duration
     */
    private void sendPlayingAndAllTime(int position, int duration) {
        //未提供
        long totalSeconds = (position + 500) / 1000;
        int seconds = (int) (totalSeconds % 60);
        int minutes = (int) ((totalSeconds / 60) % 60);
        int hours = (int) (totalSeconds / 3600);

        Log.v(TAG, "sendPlayingAndAllTime: position=" + position);
        Log.v(TAG, "                  : hours=" + hours);
        Log.v(TAG, "                  : minutes=" + minutes);
        Log.v(TAG, "                  : seconds=" + seconds);
        try {
            BYDAutoAudioDevice.getInstance(AppDelegate.getInstance().getContext()).sendAudioPlayingTime(hours, minutes, seconds);
        } catch (NoSuchMethodError error) {
        }

        totalSeconds = /*(duration + 500)*/ duration / 1000;
        seconds = (int) (totalSeconds % 60);
        minutes = (int) ((totalSeconds / 60) % 60);
        hours = (int) (totalSeconds / 3600);

        Log.v(TAG, "sendPlayingAndAllTime: duration=" + duration);
        Log.v(TAG, "                  : hours=" + hours);
        Log.v(TAG, "                  : minutes=" + minutes);
        Log.v(TAG, "                  : seconds=" + seconds);
        try {
            BYDAutoAudioDevice.getInstance(AppDelegate.getInstance().getContext()).sendAudioTotalTime(hours, minutes, seconds);
        } catch (NoSuchMethodError error) {
        }
    }


}