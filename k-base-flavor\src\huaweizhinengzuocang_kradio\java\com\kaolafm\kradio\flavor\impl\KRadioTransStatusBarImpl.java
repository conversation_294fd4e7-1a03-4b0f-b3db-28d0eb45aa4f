package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.graphics.Color;
import android.os.Build;
import androidx.annotation.RequiresApi;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import com.kaolafm.kradio.common.helper.SkinHelper;
import com.kaolafm.kradio.lib.base.flavor.KRadioTransStatusBarInter;
import com.kaolafm.kradio.lib.utils.ScreenUtil;

import static com.kaolafm.kradio.lib.utils.ScreenUtil.setStatusBar;
import static com.kaolafm.kradio.lib.utils.ViewUtil.addPaddingForView;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-09-05 14:55
 ******************************************/
public final class KRadioTransStatusBarImpl implements KRadioTransStatusBarInter {

    private static final String TAG = "KRadioStatusBarImpl";

    private int mStatusBarHeight;

    public KRadioTransStatusBarImpl() {
        mStatusBarHeight = ScreenUtil.getStatusBarHeight();
    }

    @Override
    public boolean changeStatusBarColor(Activity activity, int colorRes) {
        Window window = activity.getWindow();
        setStatusBar(window, false);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.setNavigationBarColor(Color.TRANSPARENT);
        }
        //修改statusbar字体颜色
        Log.i(TAG, "beginChange");
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (SkinHelper.isNightMode()) {
                Log.i(TAG, "changeNight");
                window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                        | View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
            } else {
                Log.i(TAG, "changeLight");
                window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                        | View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
            }
        }
        return true;
    }

    @Override
    public boolean changeViewLayoutForStatusBar(View view, int id) {
        addPaddingForView(view, 0, mStatusBarHeight, 0, 0);
        return true;
    }

    @Override
    public boolean canChangeViewLayoutForStatusBar(Object... args) {
        return false;
    }

    @Override
    public int getStatusBarHeight(Object... args) {
        return mStatusBarHeight;
    }
}
