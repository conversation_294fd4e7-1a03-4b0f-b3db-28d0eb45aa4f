<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape xmlns:android="http://schemas.android.com/apk/res/android" android:shape="rectangle">
            <solid android:color="@color/color_black_50_transparent" />
            <corners android:radius="180dp" />
        </shape>
    </item>
    <item>
        <shape xmlns:android="http://schemas.android.com/apk/res/android" android:shape="rectangle">
            <stroke android:width="@dimen/m2" android:color="@color/globle_round_bgun_pressed" />
            <corners android:radius="180dp" />
        </shape>
    </item>
</selector>