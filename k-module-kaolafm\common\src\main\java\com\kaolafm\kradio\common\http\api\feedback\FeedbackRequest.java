package com.kaolafm.kradio.common.http.api.feedback;


import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.kradio.lib.bean.WxQrcode;
import com.kaolafm.kradio.lib.utils.KaolaAppConfigData;
import com.kaolafm.opensdk.account.token.AccessTokenManager;
import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.http.core.HttpCallback;
import java.util.HashMap;

/**
 * <AUTHOR> on 2019-07-18.
 * 负反馈请求, 使用opensdk网络请求.
 */

public class FeedbackRequest extends BaseRequest {
    FeedbackApiService mFeedbackApiService;

    public FeedbackRequest() {
        mUrlManager.putDomain(FeedbackRequestConstant.DOMAIN_NAME_MINUS_FEED_BACK, FeedbackRequestConstant.AI_RADIO_FEED_BACK);
        mFeedbackApiService = obtainRetrofitService(FeedbackApiService.class);
    }

    /**
     * 负反馈
     *
     * @param albumId
     * @param callback
     */
    public void getMinusFeedback(String albumId, HttpCallback<BaseResult> callback) {
        HashMap<String, String> tempMap = new HashMap<>();
        tempMap.put(FeedbackRequestConstant.KEY_ALBUM_ID, albumId);
        tempMap.put(FeedbackRequestConstant.KEY_DEVICE_ID, KaolaAppConfigData.getInstance().getUdid());
        String uid = AccessTokenManager.getInstance().getKaolaAccessToken().getUserId();
        if (!StringUtil.isEmpty(uid)) {
            tempMap.put(FeedbackRequestConstant.KEY_UID, uid);
        }
        doHttpDeal(mFeedbackApiService.getMinusFeedback(tempMap), callback);
    }

    /**
     * 获取意见反馈的微信二维码
     * @param callback
     */
    public void getWechatQRCodeForFeedback(HttpCallback<String> callback) {
        doHttpDeal(mFeedbackApiService.getWechatQRCodeForFeedback(), baseResult -> {
            WxQrcode result = baseResult.getResult();
            return result != null ? result.getUrl() : null;
        }, callback);
    }
}
