<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools" android:layout_width="match_parent"
    android:layout_height="match_parent">
    <TextView
        android:id="@+id/tvDes0"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y10"
        android:text="@string/mine_order_vip_start_time"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size4"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvContent0"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y10"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size4"
        app:layout_constraintBaseline_toBaselineOf="@id/tvDes0"
        app:layout_constraintStart_toEndOf="@id/tvDes0"
        tools:text="2021.05.07 22:30" />

</androidx.constraintlayout.widget.ConstraintLayout>