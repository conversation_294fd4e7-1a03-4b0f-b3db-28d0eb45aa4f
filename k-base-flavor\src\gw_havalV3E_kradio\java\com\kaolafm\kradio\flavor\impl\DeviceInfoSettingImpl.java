package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.os.Build;
import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.base.internal.DeviceId;
import com.kaolafm.base.utils.SpUtil;
import com.kaolafm.kradio.common.KradioSDKManager;
import com.kaolafm.kradio.common.http.vehicle.KlSdkVehicle;
import com.kaolafm.kradio.flavor.utils.DeviceInfoUtil;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.DeviceInfoSetting;
import com.kaolafm.kradio.lib.utils.SharedPreferenceUtil;
import com.kaolafm.kradio.lib.utils.SystemPropertiesProxy;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-02-12 17:52
 ******************************************/
public final class DeviceInfoSettingImpl implements DeviceInfoSetting {

    public static final String TAG = "DeviceInfoSettingImpl";

    @Override
    public void setInfoForSDK(Context context) {
        String deviceId = getDeviceId();
        String carType = getCarType(context);
        setDeviceIdAndCarType(context, deviceId, carType);
    }

    //todo：暂时简单处理，后续还需详细盘 CacheObtainDeviceId.saveUUID.mTask有逻辑问题
    private static void setDeviceIdAndCarType(Context context, String deviceId, String carType) {
        if (TextUtils.isEmpty(carType)) {
            KlSdkVehicle.getInstance().setCarType(Build.DEVICE);
        } else {
            KlSdkVehicle.getInstance().setCarType(carType);
        }
        if (!TextUtils.isEmpty(deviceId)) {
            try {
                SharedPreferenceUtil sp = SharedPreferenceUtil.getInstance(context, "old_device", Context.MODE_PRIVATE);
                String oldDeviceId = sp.getString("old_device", "");
                Log.i(TAG, "setDeviceIdAndCarType: oldDeviceId = " + oldDeviceId + " nowDeviceId" + deviceId);
                if (!oldDeviceId.equals(deviceId)) {
                    KradioSDKManager.getInstance().setNeedClearToken(true);
                }
                DeviceId.setDeviceId(deviceId);
                sp.putString("old_device", deviceId);
            } catch (Exception e) {
                Log.e(TAG, " " + e.toString());
            }
        }
    }


    private String getDeviceId(Object... args) {
        return Build.SERIAL;
    }

    private String getCarType(Object... args) {
        Context context = (Context) args[0];
        String cayType = SystemPropertiesProxy.getString(context, "persist.beantechs.vehicle.cartype");
        return cayType;
    }
}