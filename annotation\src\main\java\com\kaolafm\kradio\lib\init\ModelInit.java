package com.kaolafm.kradio.lib.init;

import androidx.annotation.IntRange;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 初始化注解。 这个注解是为了能编译通过，真正使用的是lib里面的ModelInit
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface ModelInit {

    /**
     * 优先级，1-100，越大优先级越高。默认50
     * @return
     */
    @IntRange(from = 0, to = 100)
    int priority() default 50;

    /**
     * 执行的进程，主进程{@link Process#MAIN}、所有进程{@link Process#ALL}、其他非主进程{@link Process#OTHER}。默认主进程
     * @return
     */
    @Process.ProcessValue
    int process() default Process.MAIN;

    /**
     * 描述，可以是任意字符串。没有实际功能上的作用，只是用于说明初始化什么的，可用于调试。
     * @return
     */
    String description() default "";

    /**
     * 是否使用异步初始化，默认不使用。
     * @return
     */
    boolean isAsync() default false;

    /**
     * 依赖项，需要依赖于某一个或多个初始化完成后才能初始化。字符串为所依赖的类的全路径名称。
     * @return
     */
    String[] dependOn() default {};

}
