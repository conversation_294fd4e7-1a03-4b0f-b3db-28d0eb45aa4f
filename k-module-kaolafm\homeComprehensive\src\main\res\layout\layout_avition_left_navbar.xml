<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/activation_main_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:layout_width="1dp"
        android:layout_height="match_parent"
        android:layout_alignParentLeft="true"
        android:layout_marginLeft="@dimen/x100"
        android:background="@color/activation_color_line" />
    <!--<include layout="@layout/user_head_view" />-->
    <RelativeLayout
        android:id="@+id/navigation_left"
        android:layout_width="@dimen/x100"
        android:layout_height="match_parent"
        android:paddingTop="@dimen/y120"
        android:paddingBottom="@dimen/y120">

        <ImageView
            android:id="@+id/kradio_nav_back"
            android:layout_width="@dimen/m80"
            android:layout_height="@dimen/m80"
            android:layout_alignParentTop="true"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/y80"
            android:background="@drawable/color_main_button_click_selector"
            android:padding="@dimen/m23"
            android:src="@drawable/kradio_back" />

        <ImageView
            android:id="@+id/kradio_nav_home"
            android:layout_width="@dimen/m80"
            android:layout_height="@dimen/m80"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="@dimen/y80"
            android:background="@drawable/color_main_button_click_selector"
            android:padding="@dimen/m23"
            android:src="@drawable/kradio_home" />
    </RelativeLayout>
</RelativeLayout>