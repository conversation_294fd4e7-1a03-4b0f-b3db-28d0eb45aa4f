package com.kaolafm.kradio.lib.widget;

import android.graphics.Rect;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.State;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;
import android.view.View;

/**
 * 自动计算左右间距,瀑布流不支持
 */
public class BaseItemSpace extends RecyclerView.ItemDecoration {

    private int dividerWidth;
    private int topMargin;
    private int bottomMargin;

    public BaseItemSpace(int dividerWidth) {
        this.dividerWidth = dividerWidth;
    }

    public BaseItemSpace setTop(int topMargin) {
        this.topMargin = topMargin;
        return this;
    }

    public BaseItemSpace setBottom(int bottomMargin) {
        this.bottomMargin = bottomMargin;
        return this;
    }

    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, State state) {
        int itemCount = parent.getLayoutManager().getItemCount();
        if (itemCount == 0) {
            return;
        }
        int spanCount = getSpanCount(parent);// 每行的列数
        int position = parent.getChildLayoutPosition(view);
        int column = position % spanCount;//列 从0开始
        int right = 0, bottom = bottomMargin, left = 0, top = topMargin;
        left = column * dividerWidth / spanCount;
        right = (spanCount - column - 1) * dividerWidth / spanCount;
        outRect.set(Math.round(left), top, Math.round(right), Math.round(bottom));
    }

    private int getSpanCount(RecyclerView parent) {
        RecyclerView.LayoutManager layoutManager = parent.getLayoutManager();

        if (layoutManager instanceof GridLayoutManager) {
            return ((GridLayoutManager) layoutManager).getSpanCount();
        } else if (layoutManager instanceof StaggeredGridLayoutManager) {
            return ((StaggeredGridLayoutManager) layoutManager).getSpanCount();
        } else {
            throw new UnsupportedOperationException("the GridDividerItemDecoration can only be used in " +
                    "the RecyclerView which use a GridLayoutManager or StaggeredGridLayoutManager");
        }
    }
}
