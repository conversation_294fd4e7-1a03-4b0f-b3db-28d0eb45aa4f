//package com.kaolafm.kradio.flavor.impl;
//
//import android.content.Context;
//import android.content.Intent;
//import android.text.TextUtils;
//import android.util.Log;
//
//import com.kaolafm.kradio.flavor.utils.PlayerUtil;
//
//import com.kaolafm.kradio.lib.base.AppDelegate;
//import com.kaolafm.kradio.lib.base.flavor.KRadioScenePlayInter;
//import com.kaolafm.kradio.lib.utils.SharedPreferenceUtil;
//import com.kaolafm.kradio.report.data.SceneBaseReportData;
//import com.kaolafm.kradio.report.data.SceneBroadcastEndListenReportEvent;
//import com.kaolafm.kradio.report.data.SceneBroadcastStartListenReportEvent;
//import com.kaolafm.kradio.report.data.SceneEndListenReportData;
//import com.kaolafm.kradio.report.data.SceneStartListenReportData;
//import com.kaolafm.kradio.scene.ScenePlayListHelp;
//import com.kaolafm.kradio.service.BYDWidgetService;
//import com.kaolafm.report.ReportHelper;
//import com.kaolafm.report.event.BaseReportEventBean;
//import com.kaolafm.report.event.BroadcastEndListenReportEvent;
//import com.kaolafm.report.event.BroadcastStartListenReportEvent;
//import com.kaolafm.report.event.EndListenReportEvent;
//import com.kaolafm.report.event.StartListenReportEvent;
//import com.kaolafm.report.listener.IReportEventIntercept;
//
//
//import static com.kaolafm.kradio.uitl.Constants.SCENE_START_EVENT_CODE;
//
//public class KRadioScenePlayImpl implements KRadioScenePlayInter {
//    private static final String TAG = "KRadioScenePlayImpl";
//
//    private static final String FIRST_PLAY_SCENE_XML_NAME = "firstPlayScene";
//
//    private static final String FIRST_PLAY_SCENE_VALUE = "firstPlaySceneValue";
//
//    private SharedPreferenceUtil mSharedPreferenceUtil;
//
//    public KRadioScenePlayImpl() {
//        ReportHelper.getInstance().setReportEventIntercept(new IReportEventIntercept() {
//            @Override
//            public void report(String s, BaseReportEventBean baseReportEventBean) {
//                SceneMultiData sceneMultiData = KLAutoPlayerManager.getInstance().getSceneRadioData();
//                Log.i(TAG, "start report Scene Data " + sceneMultiData);
//                if (sceneMultiData == null || !sceneMultiData.isFromScene()) {
//                    return;
//                }
//                // 点播开始数据上报对象
//                if (baseReportEventBean instanceof StartListenReportEvent) {
//                    StartListenReportEvent startListenReportEvent = (StartListenReportEvent) baseReportEventBean;
//
//                    SceneStartListenReportData sceneStartListenReportData = new SceneStartListenReportData(startListenReportEvent);
//                    sceneStartListenReportData.setCjcode(sceneMultiData.getCode());
//                    sceneStartListenReportData.setCjtype(sceneMultiData.getType());
//
//                    ReportHelper.getInstance().addEvent(sceneStartListenReportData);
//                }
//                // 点播结束数据上报对象
//                else if (baseReportEventBean instanceof EndListenReportEvent) {
//                    EndListenReportEvent endListenReportEvent = (EndListenReportEvent) baseReportEventBean;
//
//                    SceneEndListenReportData sceneEndListenReportData = new SceneEndListenReportData(endListenReportEvent);
//                    sceneEndListenReportData.setCjcode(sceneMultiData.getCode());
//                    sceneEndListenReportData.setCjtype(sceneMultiData.getType());
//
//                    ReportHelper.getInstance().addEvent(sceneEndListenReportData);
//                }
//                // 广播开始数据上报对象
//                else if (baseReportEventBean instanceof BroadcastStartListenReportEvent) {
//                    BroadcastStartListenReportEvent broadcastStartListenReportEvent = (BroadcastStartListenReportEvent) baseReportEventBean;
//
//                    SceneBroadcastStartListenReportEvent sceneBroadcastStartListenReportEvent = new SceneBroadcastStartListenReportEvent(broadcastStartListenReportEvent);
//                    sceneBroadcastStartListenReportEvent.setCjcode(sceneMultiData.getCode());
//                    sceneBroadcastStartListenReportEvent.setCjtype(sceneMultiData.getType());
//
//                    ReportHelper.getInstance().addEvent(sceneBroadcastStartListenReportEvent);
//                }
//                // 广播结束数据上报对象
//                else if (baseReportEventBean instanceof BroadcastEndListenReportEvent) {
//                    BroadcastEndListenReportEvent broadcastEndListenReportEvent = (BroadcastEndListenReportEvent) baseReportEventBean;
//                    SceneBroadcastEndListenReportEvent sceneBroadcastEndListenReportEvent = new SceneBroadcastEndListenReportEvent(broadcastEndListenReportEvent);
//                    sceneBroadcastEndListenReportEvent.setCjcode(sceneMultiData.getCode());
//                    sceneBroadcastEndListenReportEvent.setCjtype(sceneMultiData.getType());
//
//                    ReportHelper.getInstance().addEvent(sceneBroadcastEndListenReportEvent);
//                }
//            }
//        });
//    }
//
//    @Override
//    public void playSceneRadio(Object... args) {
//        //播放场景推荐拉起比亚迪widget 解决http://redmine.itings.cn/issues/38644
//        Context context = AppDelegate.getInstance().getContext();
//        Intent intent = new Intent(context, BYDWidgetService.class);
//        intent.setAction(BYDWidgetService.WIDGET_ACTION_REFRESH);
//        context.startService(intent);
//
//        Object obj = args[0];
//        SceneMultiData sceneMultiData = null;
//        if (obj instanceof SceneMultiData) {
//            sceneMultiData = (SceneMultiData) obj;
//        }
//
//        if (sceneMultiData == null) {
//            Log.w(TAG, "SceneMultiData is null");
//            return;
//        }
//
//        sceneMultiData.setStartScenePlayCallBack(mStartScenePlayCallBack);
//        boolean isAdd = sceneMultiData.isAdd();
//        String code = sceneMultiData.getCode();
//        String type = sceneMultiData.getType();
//
//        Log.i(TAG, "playSceneRadio code = " + code + " type = " + type + " isAdd = " + isAdd);
//
//        // type = "1" 则播放历史场景（即播放app当前存储历史）
//        if ("1".equals(type)) {
////            PlayerUtil.playNetOrLocal(sceneMultiData);
//            PlayerUtil.playNetOrLocal();
//        } else {
//            ScenePlayListHelp.getInstance().playSceneRadio(sceneMultiData);
//        }
//
//        if (mSharedPreferenceUtil == null) {
//            mSharedPreferenceUtil = SharedPreferenceUtil.newInstance(context, FIRST_PLAY_SCENE_XML_NAME, Context.MODE_PRIVATE);
//        }
//        SceneBaseReportData sceneBaseReportData = new SceneBaseReportData();
//        sceneBaseReportData.setEventcode(SCENE_START_EVENT_CODE);
//        sceneBaseReportData.setCjcode(code);
//        sceneBaseReportData.setCjtype(type);
//
//        boolean flag = mSharedPreferenceUtil.getBoolean(FIRST_PLAY_SCENE_VALUE, true);
//        String flow;
//        if (flag) {
//            flow = "1";
//            mSharedPreferenceUtil.putBoolean(FIRST_PLAY_SCENE_VALUE, false);
//        } else {
//            flow = "0";
//        }
//        sceneBaseReportData.setFlow(flow);
//
//        ReportHelper.getInstance().addEvent(sceneBaseReportData, true);
//    }
//
//    @Override
//    public boolean isPlayNextForSceneRadio(Object... args) {
//        PlayItem playItem = (PlayItem) args[0];
//        if (playItem == null) {
//            return true;
//        }
//
//        if (playItem.getAudioId() == ScenePlayListHelp.getInstance().getAlbumAudioId()) {
//            //暂停播放之后清空场景电台id
//            ScenePlayListHelp.getInstance().setAlbumAudioId(-1);
//            return false;
//        }
//        return true;
//    }
//
//    @Override
//    public int getBanAdIndex() {
//        String radioId = KLAutoPlayerManager.getInstance().getRadioId();
//        long sceneRadioId = ScenePlayListHelp.getInstance().getSceneRadioId();
//        if (!TextUtils.isEmpty(radioId) && Long.valueOf(radioId) == sceneRadioId) {
//            return 3;
//        }
//        return -1;
//    }
//
//    private SceneMultiData.StartScenePlayCallBack mStartScenePlayCallBack = new SceneMultiData.StartScenePlayCallBack() {
//        @Override
//        public void onStartScenePlay(String radioId, String audioId) {
//            long id = 0;
//            if (!TextUtils.isEmpty(audioId)) {
//                try {
//                    id = Long.parseLong(audioId);
//                } catch (NumberFormatException nfe) {
//                    nfe.printStackTrace();
//                }
//            }
//            SceneMultiData sceneMultiData = KLAutoPlayerManager.getInstance().getSceneRadioData();
//            if (sceneMultiData == null) {
//                Log.w(TAG, "onStartScenePlay SceneMultiData is null");
//                return;
//            }
//            if (id != 0) {
//                ScenePlayListHelp.getInstance().sendSceneRadioCmd(sceneMultiData.getExecuteResult(), id);
//            } else {
//                if (!TextUtils.isEmpty(radioId)) {
//                    try {
//                        id = Long.parseLong(radioId);
//                    } catch (NumberFormatException nfe) {
//                        nfe.printStackTrace();
//                    }
//                }
//                ScenePlayListHelp.getInstance().sendSceneRadioCmd(sceneMultiData.getExecuteResult(), id);
//            }
//        }
//    };
//}
