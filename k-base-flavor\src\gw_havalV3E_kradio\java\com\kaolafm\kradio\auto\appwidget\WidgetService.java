package com.kaolafm.kradio.auto.appwidget;

import android.annotation.SuppressLint;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.appwidget.AppWidgetManager;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.os.Build;
import android.os.IBinder;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.core.app.NotificationCompat;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.auto.appwidget.remoteviews.BitmapUtil;
import com.kaolafm.kradio.auto.appwidget.remoteviews.IRemoteViews;
import com.kaolafm.kradio.auto.appwidget.remoteviews.RemoteData;
import com.kaolafm.kradio.auto.appwidget.remoteviews.RemoteViewFactory;
import com.kaolafm.kradio.common.CP;
import com.kaolafm.kradio.common.ErrorInfo;
import com.kaolafm.kradio.common.ResultCallback;
import com.kaolafm.kradio.common.SubscribeData;
import com.kaolafm.kradio.flavor.R;

import com.kaolafm.kradio.flavor.utils.PlayerUtil;
import com.kaolafm.kradio.k_kaolafm.home.player.PlayerHelper;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.IntentUtils;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.lib.utils.UrlUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.report.ReportManager;
import com.kaolafm.kradio.subscribe.SubscribeChangeListener;
import com.kaolafm.kradio.subscribe.SubscribeManager;
import com.kaolafm.kradio.subscribe.SubscribeModel;
import com.kaolafm.kradio.subscribe.SubscribeRepository;
import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.IPlayListControl;
import com.kaolafm.opensdk.player.logic.listener.IPlayerStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.report.event.PlayerUiControlReportEvent;
import com.kaolafm.utils.BitmapUtils;


import java.util.List;

import io.reactivex.Observable;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;

import static android.graphics.Color.*;


/**
 * <AUTHOR>
 */
public class WidgetService extends Service implements IPlayerStateListener, SubscribeChangeListener {

    public static final String WIDGET_ACTION_NEXT = "kl.action.next";
    public static final String WIDGET_ACTION_PREV = "kl.action.prev";
    public static final String WIDGET_ACTION_PAUSE = "kl.action.pause";
    public static final String WIDGET_ACTION_PLAY = "kl.action.play";
    public static final String WIDGET_ACTION_REFRESH = "kl.action.refresh";
    public static final String WIDGET_ACTION_EXIT = "kl.action.exit";
    public static final String WIDGET_ACTION_COLLECTION = "kl.action.collection";

    private Context context = AppDelegate.getInstance().getContext();

    private static final String TAG = "WidgetService";
    //专辑图片
    private Bitmap mCurrentBitmap;

    private Bitmap mBlurBitmap;

    private Bitmap mBlurBitmapByLand;

    private Bitmap mBlurBitmapByPort;

    private boolean subscription = false;

    private String livingTime = "";

    private int landW;
    private int landH;
    private int portW;
    private int portH;

    private SubscribeModel subscribeModel;

    private boolean isPortrait = true;

    @Override
    public void onCreate() {
        super.onCreate();
        startForeground();
        boolean init = PlayerManager.getInstance().isPlayerInitSuccess();
        Log.i(TAG, "onCreate init:" + init);
        if (!init) {
            PlayerManager.getInstance().addPlayerInitComplete(new IPlayerInitCompleteListener() {
                @Override
                public void onPlayerInitComplete(boolean b) {
                    if (b) {
                        PlayerManager.getInstance().removePlayerInitComplete(this);
                        init();
                    }
                }
            });
        } else {
            init();
        }

    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.i(TAG, "onStartCommand intent = " + intent);
        if (intent == null) {
            return START_NOT_STICKY;
        }
        String action = intent.getAction();
        Log.i(TAG, "onStartCommand action = " + action);

        int appWidgetId = intent.getIntExtra(AppWidgetManager.EXTRA_APPWIDGET_ID,
                AppWidgetManager.INVALID_APPWIDGET_ID);

        executeActionCommand(action, appWidgetId);
        return START_NOT_STICKY;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mCurrentBitmap = null;
        PlayerManager.getInstance().removePlayControlStateCallback(this);
        //BroadcastRadioListManager.getInstance().removeOnBroadcastRadioLivingListener(this);
        //KLAutoPlayerManager.getInstance().removeIPlayerStateListener(this);
        ((SubscribeRepository) SubscribeManager.getInstance(context, CP.KaoLaFM)).removeSubscribeChangeListener(this);
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            mBlurBitmap = mBlurBitmapByLand;
            isPortrait = false;
        } else {
            mBlurBitmap = mBlurBitmapByPort;
            isPortrait = true;
        }
        setRemoteViews(PlayerManager.getInstance().getCurPlayItem(), AppWidgetManager.INVALID_APPWIDGET_ID);
        super.onConfigurationChanged(newConfig);
    }

    private void init() {
        subscribeModel = new SubscribeModel();
        PlayerManager.getInstance().init(AppDelegate.getInstance().getContext());
        PlayerManager.getInstance().addPlayControlStateCallback(this);
//        PlayerManager.getInstance().addIPlayChangedListener(this);
        ((SubscribeRepository) SubscribeManager.getInstance(context, CP.KaoLaFM)).addSubscribeChangeListener(this);
//        PlayerManager.getInstance().addOnBroadcastRadioLivingListener(this);
        initWH();
        initPlayInfo();
        if (ResUtil.getOrientation() == Configuration.ORIENTATION_LANDSCAPE) {
            isPortrait = false;
        }
    }

    private void initWH() {
        landW = ScreenUtil.dp2px(239);
        landH = ScreenUtil.dp2px(268 - 50);
        portW = ScreenUtil.dp2px(390);
        portH = ScreenUtil.dp2px(177 - 37);
    }

    private void initPlayInfo() {
        PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
        if(playItem != null) {
            updateBitmap(playItem);
        }
//        if (playItem == null) {
//            HistoryDaoManager.getInstance().queryHistoryListOrderByTime(1, historyItems -> {
//                if (!ListUtil.isEmpty(historyItems)) {
//                    HistoryItem historyItem = historyItems.get(0);
//                    if (historyItem != null) {
//                        PlayItem historyPlayItem = HistoryUtils.turnPlayItem(historyItem);
//                        updateBitmap(historyPlayItem);
//                    }
//                }
//            });
//        } else {
//            x
//        }
    }

    @Override
    public void onIdle(PlayItem playItem) {
        Log.i(TAG, "onIdle");
    }

    @Override
    public void onPlayerPreparing(final PlayItem playItem) {
        Log.i(TAG, "onPlayerPreparing");
        if (playItem == null) {
            Log.i(TAG, "playitem null");
            return;
        }
        updateBitmap(playItem);
        setRemoteViews(playItem, AppWidgetManager.INVALID_APPWIDGET_ID);
        setSubscribeState();
    }

    @Override
    public void onPlayerPlaying(final PlayItem playItem) {
        Log.i(TAG, "onPlayerPlaying");
        setRemoteViews(playItem, AppWidgetManager.INVALID_APPWIDGET_ID);
    }

    @Override
    public void onPlayerPaused(PlayItem playItem) {
        Log.i(TAG, "onPlayerPaused");
        setRemoteViews(playItem, AppWidgetManager.INVALID_APPWIDGET_ID);
    }

    @Override
    public void onProgress(PlayItem playItem, long l, long l1) {
        if (playItem != null && !playItem.isLiving()) {
            setRemoteViews(playItem, AppWidgetManager.INVALID_APPWIDGET_ID);
        }
    }

    @Override
    public void onPlayerFailed(PlayItem playItem, int what, int extra) {
        Log.i(TAG, "onPlayerFailed");
    }

    @Override
    public void onPlayerEnd(PlayItem playItem) {
        Log.i(TAG, "onPlayerEnd");
    }

    @Override
    public void onSeekStart(PlayItem playItem) {
        Log.i(TAG, "onSeekStart");
    }

    @Override
    public void onSeekComplete(PlayItem playItem) {
        Log.i(TAG, "onSeekComplete");
    }

    @Override
    public void onBufferingStart(PlayItem playItem) {
        Log.i(TAG, "onBufferingStart");
        if (playItem != null && !playItem.isLiving()) {
            setRemoteViews(playItem, AppWidgetManager.INVALID_APPWIDGET_ID);
        }
    }

    @Override
    public void onBufferingEnd(PlayItem playItem) {
        Log.i(TAG, "onBufferingEnd");
    }

    @Override
    public void onDownloadProgress(PlayItem playItem, long l, long l1) {

    }

    private String getPic(PlayItem playItem, String type) {
        String url = playItem.getPicUrl();

//        if (TextUtils.isEmpty(url)) {
//            PlayerRadioListItem curPlayerRadioListItem = PlayerRadioListManager.getInstance().getCurRadioItem();
//            if (curPlayerRadioListItem != null && !TextUtils.isEmpty(curPlayerRadioListItem.getPicUrl())) {
//                url = curPlayerRadioListItem.getPicUrl();
//            }
//        }

        String picUrl = UrlUtil.getCustomPicUrl(type, url);

        return picUrl;
    }

    private void executeActionCommand(String action, int appWidgetId) {
        if (TextUtils.isEmpty(action)) {
            return;
        }

        IPlayListControl playListControl = PlayerManager.getInstance().getPlayListControl();
        Log.i(TAG, "playListControl:" + playListControl);
        Log.i(TAG, "executeActionCommand:" + action);

        switch (action) {
            case WIDGET_ACTION_NEXT:
                if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
                    return;
                }

                if (playListControl == null) {
                    go2Launcher();
                    return;
                }
                if (PlayerManager.getInstance().getCurPlayItem() == null) {
                    PlayerUtil.playNetOrLocal();
                } else {
                    if(PlayerManager.getInstance().hasNext()) {
                        PlayerManager.getInstance().playNext();
                    } else {
                        Toast.makeText(context, "已经是最后一首了", Toast.LENGTH_LONG).show();
                    }
                }
                ReportManager.getInstance().addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_NEXT, PlayerUiControlReportEvent.CONTROL_TYPE_CLICK, PlayerUiControlReportEvent.POSITION_WIDGET);
                break;
            case WIDGET_ACTION_PREV:
                if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
                    return;
                }

                if (playListControl == null) {
                    go2Launcher();
                    return;
                }
                if (PlayerManager.getInstance().getCurPlayItem() == null) {
                    PlayerUtil.playNetOrLocal();
                } else {
                    if(PlayerManager.getInstance().hasPre()) {
                        PlayerManager.getInstance().playPre();
                    } else {
                        Toast.makeText(context, "已经是第一首了", Toast.LENGTH_LONG).show();
                    }
                }
                ReportManager.getInstance().addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_PREVIOUS, PlayerUiControlReportEvent.CONTROL_TYPE_CLICK, PlayerUiControlReportEvent.POSITION_WIDGET);

                break;
            case WIDGET_ACTION_PLAY:
                if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
                    return;
                }

                if (playListControl == null) {
                    go2Launcher();
                    return;
                }
                //fix bug: BUX1.X-APTIVBUX1XAP-446
                int currentFocus = PlayerManager.getInstance().getCurrentAudioFocusStatus();
                if (currentFocus < 0) {
                    PlayerManager.getInstance().requestAudioFocus();
                }
                // end fix

                if (PlayerManager.getInstance().getCurPlayItem() == null) {
                    PlayerUtil.playNetOrLocal();
                } else {
                    PlayerManagerHelper.getInstance().switchPlayerStatus(true);
                }
                ReportManager.getInstance().addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_PLAY, PlayerUiControlReportEvent.CONTROL_TYPE_CLICK, PlayerUiControlReportEvent.POSITION_WIDGET);
                break;
            case WIDGET_ACTION_PAUSE:
                if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
                    return;
                }
                PlayerManager.getInstance().pause(true);
                ReportManager.getInstance().addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_PAUSE, PlayerUiControlReportEvent.CONTROL_TYPE_CLICK, PlayerUiControlReportEvent.POSITION_WIDGET);
                break;
            case WIDGET_ACTION_COLLECTION:
                widgetSubscribe(PlayerHelper.getSubscribeId());
                break;
            case WIDGET_ACTION_REFRESH:
                updateBitmap(PlayerManager.getInstance().getCurPlayItem());
                setRemoteViews(PlayerManager.getInstance().getCurPlayItem(), appWidgetId);
                break;
            case WIDGET_ACTION_EXIT:
                setRemoteViews(PlayerManager.getInstance().getCurPlayItem(), appWidgetId);
                break;
        }
    }

    @Override
    public void onSubscribesChanged(List<SubscribeData> subscribes) {
        subscription = false;
        if (!ListUtil.isEmpty(subscribes)) {
            for (SubscribeData s : subscribes) {
                if (s.getId() == PlayerHelper.getSubscribeId()) {
                    subscription = true;
                    break;
                }
            }
        }
        setRemoteViews(PlayerManager.getInstance().getCurPlayItem(), AppWidgetManager.INVALID_APPWIDGET_ID);
    }

//    @Override
//    public void onPlayChangeChanged(PlayItem playItem) {
//        setRemoteViews(playItem, AppWidgetManager.INVALID_APPWIDGET_ID);
//        setSubscribeState();
//    }

//    private static class MyGeneralCallback implements GeneralCallback {
//        private String actionType;
//        private Context context;
//
//        public MyGeneralCallback(Context context, String actionType) {
//            this.context = context;
//            this.actionType = actionType;
//        }
//
//        @Override
//        public void onResult(Object o) {
////            PlayerManager.getInstance().removeGetContentListener(this);
////            if (WIDGET_ACTION_PREV.equals(actionType)) {
////                KLAutoPlayerManager.getInstance(context).playPre();
////            } else {
////                KLAutoPlayerManager.getInstance(context).playNext();
////            }
//        }
//
//        @Override
//        public void onError(int i) {
//            PlayerManager.getInstance().removeGetContentListener(this);
//        }
//
//        @Override
//        public void onException(Throwable throwable) {
//            PlayerManager.getInstance().removeGetContentListener(this);
//        }
//    }


    private void playHistoryOrDefault(final String actionType) {
//        HistoryItem history = getRecentlyItem();
//        boolean isPlayNextOrPre = WIDGET_ACTION_NEXT.equals(actionType) || WIDGET_ACTION_PREV.equals(actionType);
//        if (WIDGET_ACTION_PLAY.equals(actionType)) {
//
//        } else {
//            if (history != null && !Constants.RESOURCES_TYPE_BROADCAST.equals(history.getType())) {
//                if (isPlayNextOrPre) {
//                    PlayerManager.getInstance().addGetContentListener(new MyGeneralCallback(getApplicationContext(), actionType));
//                }
//            }
//        }
//        if (history != null) {
//            KLAutoPlayerManager.getInstance(getApplicationContext()).playHistory(history, !isPlayNextOrPre);
//        } else {
//            KLAutoPlayerManager.getInstance(getApplicationContext()).playPgc(Constants.DEFAULT_PGC, !isPlayNextOrPre);
//        }
    }

//    @Override
//    public void onLivingCountDown(String s) {
//        PlayItem playItem = KLAutoPlayerManager.getInstance().getCurrentPlayItem();
//        livingTime = s;
//        if (playItem != null && playItem.isLivingUrl()) {
//            setRemoteViews(playItem, AppWidgetManager.INVALID_APPWIDGET_ID);
//        }
//    }


    private void updateBitmap(PlayItem playItem) {
        if (playItem == null) {
            mCurrentBitmap = BitmapUtil.makeRoundCycle(BitmapUtil.getBitmapFromResource(AppDelegate.getInstance().getContext().getResources(), R.drawable.widget_cover_default));
            return;
        }
        String pic = getPic(playItem, UrlUtil.PIC_250_250);
        Log.i(TAG, "updateBitmap:" + pic);
        if (TextUtils.isEmpty(pic)) {
            mCurrentBitmap = BitmapUtil.makeRoundCycle(BitmapUtil.getBitmapFromResource(AppDelegate.getInstance().getContext().getResources(), R.drawable.widget_cover_default));
            setRemoteViews(playItem, AppWidgetManager.INVALID_APPWIDGET_ID);
        } else {
            ImageLoader.getInstance().getBitmapFromCache(context, pic, bitmap -> {
                PlayItem curPlayItem = PlayerManager.getInstance().getCurPlayItem();
                if (curPlayItem != null && curPlayItem.getAudioId() == playItem.getAudioId()) {
                    mCurrentBitmap = bitmap;
                    setRemoteViews(playItem, AppWidgetManager.INVALID_APPWIDGET_ID);
                } else if (curPlayItem == null) {
                    mCurrentBitmap = BitmapUtil.makeRoundCycle(BitmapUtil.getBitmapFromResource(AppDelegate.getInstance().getContext().getResources(), R.drawable.widget_cover_default));
                    setRemoteViews(playItem, AppWidgetManager.INVALID_APPWIDGET_ID);
                }

            });
        }

        updateblurBitmap(playItem);
    }


    private void updateblurBitmap(PlayItem playItem) {
        String pic = getPic(playItem, UrlUtil.PIC_100_100);
        ImageLoader.getInstance().getBlurBitmapFromCache(context, pic, 20, bitmap -> {
            Observable.create((ObservableOnSubscribe<Boolean>) emitter -> {

                Bitmap portB = BitmapUtils.createScaledBitmapRGB565(bitmap, portW, portH, BitmapUtils.ScalingLogic.CROP);
                Bitmap landB = BitmapUtils.createScaledBitmapRGB565(bitmap, landW, landH, BitmapUtils.ScalingLogic.CROP);

                mBlurBitmapByPort = BitmapUtils.setBitmapRoundSize(portB, portW, portH, ScreenUtil.dp2px(10));
                mBlurBitmapByLand = BitmapUtils.setBitmapRoundSize(landB, landW, landH, ScreenUtil.dp2px(10));

                if (ResUtil.getOrientation() == Configuration.ORIENTATION_LANDSCAPE) {
                    mBlurBitmap = mBlurBitmapByLand;
                } else {
                    mBlurBitmap = mBlurBitmapByPort;
                }

                emitter.onNext(true);
            }).subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(b -> setRemoteViews(playItem, AppWidgetManager.INVALID_APPWIDGET_ID));
        });
    }

    public void setRemoteViews(PlayItem playItem, int appWidgetId) {
        RemoteData mRemoteData = new RemoteData.Builder().mPlayItem(playItem).mCurrentBitmap(mCurrentBitmap).mBlurBitmap(mBlurBitmap).livingTime(livingTime).subscription(subscription).build();
        mRemoteData.setAppWidgetId(appWidgetId);
        IRemoteViews iRemoteViews = RemoteViewFactory.getInstance().getIRemoteViewsByDefault();
        Log.i(TAG, "setRemoteViews iRemoteViews--->" + iRemoteViews);
        iRemoteViews.setRemoteViews(mRemoteData);
    }


    public void widgetSubscribe(final long id) {
        if (subscribeModel == null) {
            return;
        }
        subscribeModel.isSubscribed(String.valueOf(id), new ResultCallback() {
            @Override
            public void onResult(boolean result, int code) {
                if (result) {
                    unSubscribe(id);
                } else {
                    subscribe(id);
                }
            }

            @Override
            public void onFailure(ErrorInfo errorInfo) {

            }
        });
    }

    private void subscribe(long id) {
        if (subscribeModel == null) {
            return;
        }
        SubscribeData subscribeData = new SubscribeData();
        subscribeData.setId(id);
        subscribeModel.subscribe(subscribeData, new ResultCallback() {
            @Override
            public void onResult(boolean result, int code) {
                subscription = result;
                setRemoteViews(PlayerManager.getInstance().getCurPlayItem(), AppWidgetManager.INVALID_APPWIDGET_ID);
            }

            @Override
            public void onFailure(ErrorInfo errorInfo) {

            }
        });
    }

    private void unSubscribe(long id) {
        if (subscribeModel == null) {
            return;
        }
        SubscribeData sd = new SubscribeData();
        sd.setId(id);
        subscribeModel.unsubscribe(sd, new ResultCallback() {
            @Override
            public void onResult(boolean result, int code) {
                if (result) {
                    subscription = false;
                }
                setRemoteViews(PlayerManager.getInstance().getCurPlayItem(), AppWidgetManager.INVALID_APPWIDGET_ID);
            }

            @Override
            public void onFailure(ErrorInfo errorInfo) {

            }
        });
    }

    private void setSubscribeState() {
        if (subscribeModel == null) {
            return;
        }
        subscribeModel.isSubscribed(String.valueOf(PlayerHelper.getSubscribeId()), new ResultCallback() {
            @Override
            public void onResult(boolean result, int code) {
                subscription = result;
                setRemoteViews(PlayerManager.getInstance().getCurPlayItem(), AppWidgetManager.INVALID_APPWIDGET_ID);
            }

            @Override
            public void onFailure(ErrorInfo errorInfo) {

            }
        });
    }

    @SuppressLint("WrongConstant")
    private void startForeground() {
        String CHANNEL_ONE_ID = "cmgyunting.vehicleplayer.cnr";
        String CHANNEL_ONE_NAME = "Widget One";
        NotificationChannel notificationChannel = null;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            notificationChannel = new NotificationChannel(CHANNEL_ONE_ID,
                    CHANNEL_ONE_NAME, NotificationManager.IMPORTANCE_DEFAULT);
            NotificationManager manager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
            assert manager != null;
            manager.createNotificationChannel(notificationChannel);
            startForeground(1, new NotificationCompat.Builder(this, CHANNEL_ONE_ID).build());
        }
    }

    @RequiresApi(Build.VERSION_CODES.O)
    private String createNotificationChannel(String channelId, String channelName) {
        @SuppressLint("WrongConstant") NotificationChannel chan = new NotificationChannel(channelId, channelName, NotificationManager.IMPORTANCE_NONE);
        chan.setLightColor(BLUE);
        chan.setLockscreenVisibility(Notification.VISIBILITY_PRIVATE);
        NotificationManager service = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
        service.createNotificationChannel(chan);
        return channelId;
    }

    private void go2Launcher() {
        //fixed 修复应用未启动添加widget点击按钮没有响应的问题，直接进入应用
        Intent launchAppIntent = IntentUtils.getInstance().getLauncherIntentUseWidget(context);
        context.startActivity(launchAppIntent);
    }


}
