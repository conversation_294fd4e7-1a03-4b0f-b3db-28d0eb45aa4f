package com.kaolafm.kradio.online.common.view;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.view.GravityCompat;
import androidx.core.view.ViewCompat;
import androidx.drawerlayout.widget.DrawerLayout;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
 


/**
 * 不拦截Down事件的DrawerLayout
 * 与DrawerLayoutRecyclerView配合使用解决嵌套时的滑动冲突
 */
public class SmartRefreshHorizontalDrawerLayout extends DrawerLayout {
    private SmartRefreshLayout mSmartRefreshLayout;
    private boolean hasIntercept = true;

    public SmartRefreshHorizontalDrawerLayout(@NonNull Context context) {
        super(context);
    }

    public SmartRefreshHorizontalDrawerLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public SmartRefreshHorizontalDrawerLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    public void setSmartRefreshLayout(SmartRefreshLayout mSmartRefreshLayout) {
        this.mSmartRefreshLayout = mSmartRefreshLayout;
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        boolean b = super.onInterceptTouchEvent(ev);
        if (mSmartRefreshLayout == null || !isDrawerOpen()) return b;
        if (ev.getActionMasked() == MotionEvent.ACTION_DOWN && touchOnDrawer(ev.getRawX(), ev.getRawY())) {
            hasIntercept = false;
            return false;
        }
        if (!hasIntercept) {
            if (ev.getActionMasked() == MotionEvent.ACTION_UP || ev.getActionMasked() == MotionEvent.ACTION_CANCEL) {
                hasIntercept = true;
            }
            return false;
        }
        return b;
    }

    private boolean isDrawerOpen() {
        return isDrawerOpen(Gravity.START) || isDrawerOpen(Gravity.END);
    }

    /**
     * 是否点击到了抽屉布局上
     *
     * @param x
     * @param y
     * @return
     */
    private boolean touchOnDrawer(float x, float y) {
        //没有打开抽屉，返回false
        if (!isDrawerOpen()) {
            return false;
        }

        View child;
        int[] location = new int[2];
        for (int index = 0; index < getChildCount(); index++) {
            child = getChildAt(index);
            //该child不是抽屉，不判断
            if (!isDrawerView(child)) {
                continue;
            }
            //判断是否点击在抽屉child上
            child.getLocationOnScreen(location);
            if (x >= location[0] && x <= location[0] + child.getWidth() &&
                    y >= location[1] && y <= location[1] + child.getHeight()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 是否是抽屉布局
     *
     * @param child
     * @return
     */
    boolean isDrawerView(View child) {
        final int gravity = ((LayoutParams) child.getLayoutParams()).gravity;
        final int absGravity = GravityCompat.getAbsoluteGravity(gravity,
                ViewCompat.getLayoutDirection(child));
        if ((absGravity & Gravity.LEFT) != 0) {
            // This child is a left-edge drawer
            return true;
        }
        if ((absGravity & Gravity.RIGHT) != 0) {
            // This child is a right-edge drawer
            return true;
        }
        return false;
    }
}
