<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/widget_playinfo_layout"
    android:layout_width="@dimen/byd_35widget_layout_width"
    android:layout_height="@dimen/byd_35widget_layout_height">


    <!--    <ImageView-->
    <!--        android:id="@+id/widget_bg_shape"-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="50dp"-->
    <!--        android:orientation="horizontal"-->
    <!--        android:scaleType="fitXY"-->
    <!--        android:src="@drawable/byd_35widget_bg_shadow"/>-->


    <!--    android:layout_marginTop="@dimen/byd_35widget_bg_marginTop"-->
    <!--    android:layout_marginBottom="@dimen/byd_35widget_bg_marginBottom"-->
    <!--    android:layout_marginStart="@dimen/byd_35widget_bg_marginStart"-->
    <!--    android:layout_marginEnd="@dimen/byd_35widget_bg_marginEnd"-->

    <ImageView
        android:id="@+id/widget_bg_shadow"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitXY"
        android:src="@drawable/byd_35widget_bg_shadow" />

    <FrameLayout
        android:layout_width="@dimen/byd_35widget_layout_width_inner"
        android:layout_height="@dimen/byd_35widget_layout_height_inner"
        android:layout_centerInParent="true">


        <ImageView
            android:id="@+id/widget_bg"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY"
            android:src="@drawable/byd_35widget_bg" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="@dimen/byd_35widget_icon_title_layout_height"
            android:layout_marginStart="@dimen/byd_35widget_icon_title_layout_marginStart"
            android:layout_marginTop="@dimen/byd_35widget_icon_title_layout_marginTop"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/widget_icon"
                android:layout_width="@dimen/byd_35widget_icon_width"
                android:layout_height="@dimen/byd_35widget_icon_height"
                android:src="@drawable/byd_35_app_icon" />

            <TextView
                android:id="@+id/widget_title"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginStart="@dimen/byd_35widget_title_marginStart"
                android:gravity="center_vertical"
                android:text="@string/app_name"
                android:textColor="@color/byd_35widget_title_color"
                android:textSize="@dimen/byd_35widget_textSizeSecondLevel" />
        </LinearLayout>

        <ImageView
            android:id="@+id/widget_cover"
            android:layout_width="@dimen/byd_35widget_thumb_width"
            android:layout_height="@dimen/byd_35widget_thumb_height"
            android:layout_alignParentStart="true"
            android:layout_gravity="end"
            android:layout_marginTop="@dimen/byd_35widget_thumb_mar_top"
            android:layout_marginEnd="@dimen/byd_35widget_thumb_mar_end"
            android:src="@drawable/byd_35widget_thumb_default_new" />


        <LinearLayout
            android:layout_width="@dimen/byd_35widget_name_artist_width"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/byd_35widget_name_artist_marginStart"
            android:gravity="center_vertical"
            android:orientation="vertical">

            <TextView
                android:id="@+id/widget_audio_name"
                android:layout_width="match_parent"
                android:layout_height="@dimen/byd_35widget_name_height"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:singleLine="true"
                android:text="@string/app_name"
                android:textColor="@color/byd_35widget_content_color"
                android:textSize="@dimen/byd_35widget_textSizeFirstLevel"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/widget_album_name"
                android:layout_width="match_parent"
                android:layout_height="@dimen/byd_35widget_name_height"
                android:layout_marginTop="@dimen/byd_35widget_des_mar_top"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:singleLine="true"
                android:text="@string/default_artist"
                android:textColor="@color/byd_35widget_content_color1"
                android:textSize="@dimen/byd_35widget_textSizeSecondLevel" />
        </LinearLayout>

        <ImageView
            android:id="@+id/widget_progress"
            android:layout_width="@dimen/byd_35widget_progress_width"
            android:layout_height="@dimen/byd_35widget_progress_height"
            android:layout_gravity="bottom|center_horizontal"
            android:layout_marginBottom="@dimen/byd_35widget_progress_marginBottom" />

        <LinearLayout
            android:layout_width="@dimen/byd_35widget_button_layout_width"
            android:layout_height="@dimen/byd_35widget_button_layout_height"
            android:layout_gravity="bottom"
            android:layout_marginStart="@dimen/byd_35widget_button_layout_marginStart"
            android:layout_marginBottom="@dimen/byd_35widget_button_layout_marginBottom"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/widget_prev"
                android:layout_width="@dimen/byd_35widget_button_width"
                android:layout_height="@dimen/byd_35widget_button_height"
                android:paddingStart="@dimen/byd_35widget_button_marginStart2"
                android:paddingTop="@dimen/byd_35widget_button_paddingTop"
                android:paddingEnd="@dimen/byd_35widget_button_paddingEnd"
                android:paddingBottom="@dimen/byd_35widget_button_paddingBottom"
                android:scaleType="centerInside"
                android:src="@drawable/byd_35widget_pre" />

            <ImageView
                android:id="@+id/widget_play_or_pause"
                android:layout_width="@dimen/byd_35widget_button_width"
                android:layout_height="@dimen/byd_35widget_button_height"
                android:layout_marginStart="@dimen/byd_35widget_button_marginStart2"
                android:paddingStart="@dimen/byd_35widget_button_paddingStart"
                android:paddingTop="@dimen/byd_35widget_button_paddingTop"
                android:paddingEnd="@dimen/byd_35widget_button_paddingEnd"
                android:paddingBottom="@dimen/byd_35widget_button_paddingBottom"
                android:scaleType="centerInside"
                android:src="@drawable/byd_35widget_stop" />

            <ImageView
                android:id="@+id/widget_next"
                android:layout_width="@dimen/byd_35widget_button_width"
                android:layout_height="@dimen/byd_35widget_button_height"
                android:layout_marginStart="@dimen/byd_35widget_button_marginStart"
                android:paddingStart="@dimen/byd_35widget_button_paddingStart"
                android:paddingTop="@dimen/byd_35widget_button_paddingTop"
                android:paddingEnd="@dimen/byd_35widget_button_paddingEnd"
                android:paddingBottom="@dimen/byd_35widget_button_paddingBottom"
                android:scaleType="centerInside"
                android:src="@drawable/byd_35widget_next" />
        </LinearLayout>


        <!--    <ProgressBar-->
        <!--        android:id="@+id/widget_progressBar"-->
        <!--        style="@style/Widget.AppCompat.ProgressBar.Horizontal"-->
        <!--        android:layout_width="@dimen/byd_35widget_progress_width"-->
        <!--        android:layout_height="@dimen/byd_35widget_progress_height"-->
        <!--        android:layout_alignParentBottom="true"-->
        <!--        android:layout_gravity="bottom|center_horizontal"-->
        <!--        android:progress="0"-->
        <!--        android:progressDrawable="@drawable/widget_seekbar_bar"-->
        <!--        android:scrollbarStyle="insideInset" />-->


    </FrameLayout>


</RelativeLayout>
