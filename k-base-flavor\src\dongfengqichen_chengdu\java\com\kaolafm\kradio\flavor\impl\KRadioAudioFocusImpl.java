package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.media.AudioAttributes;
import android.media.AudioFocusRequest;
import android.media.AudioManager;
import android.os.Handler;


import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioFocusInter;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-06-21 10:54
 ******************************************/
public final class KRadioAudioFocusImpl implements KRadioAudioFocusInter {

    private AudioAttributes mAudioAttributes;

    private AudioFocusRequest mAudioFocusRequest;

    private Handler mHandler;

    private AudioManager mAudioManager;

    @Override
    public boolean requestAudioFocus(Object... args) {
        AudioManager.OnAudioFocusChangeListener onAudioFocusChangeListener = (AudioManager.OnAudioFocusChangeListener) args[0];
        if (mHandler == null) {
            mHandler = new Handler();
            mAudioManager = (AudioManager) AppDelegate.getInstance().getContext().getSystemService(Context.AUDIO_SERVICE);
            mAudioAttributes = new AudioAttributes.Builder()
                    .setUsage(AudioAttributes.USAGE_MEDIA)
                    .build();
            mAudioFocusRequest = new AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN)
                    .setAudioAttributes(mAudioAttributes)
                    .setAcceptsDelayedFocusGain(false)
                    .setOnAudioFocusChangeListener(onAudioFocusChangeListener, new Handler())
                    .build();
        }
        int rect = mAudioManager.requestAudioFocus(mAudioFocusRequest);
        return rect == AudioManager.AUDIOFOCUS_REQUEST_GRANTED;
    }

    @Override
    public boolean abandonAudioFocus(Object... args) {
        PlayerManagerHelper.getInstance().play(false);
        return true;
    }
}
