package com.kaolafm.ad.comprehensive.ads.image;

import android.content.Context;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.ad.api.model.InteractionAdvert;
import com.kaolafm.ad.comprehensive.ads.image.base.BaseInteractContentView;
import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.lib.utils.imageloader.listener.OnImageLoaderListener;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.util.ReportConstants;
import com.kaolafm.report.util.ReportParameterManager;

public class AdInteractContentView extends BaseInteractContentView {
    private TextView mIvAdMsg;
    private ImageView mIvAdClose;

    public AdInteractContentView(Context context) {
        super(context);
        init(context);
    }

    public AdInteractContentView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public AdInteractContentView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        LayoutInflater.from(context).inflate(R.layout.ad_interact_view_layout, this, true);//do
        mImageView = findViewById(R.id.iv_ad_interact);
        mIvAdMsg = findViewById(R.id.tv_ad_msg);
        mIvAdClose = findViewById(R.id.iv_ad_close);
        mIvAdClose.setOnClickListener((v) -> {
            mAdImageListener.onAdSkip();
            hideWithAnim();
            ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_AD_INTERACTION_CLOSE, "", ReportParameterManager.getInstance().getPage()
                    , ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_ADVERT, null, null, null, String.valueOf(interactionAdvert.getId())));
        });
        this.setOnClickListener(this::onViewClick);
        this.setVisibility(INVISIBLE);
    }

    @Override
    public void show() {
        super.show();
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_AD_INTERACTION_CLOSE, "", ReportParameterManager.getInstance().getPage()
                , ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_ADVERT, null, null, null, String.valueOf(interactionAdvert.getId())));
    }

    @Override
    protected void displayImage(ImageView view, String url, OnImageLoaderListener listener) {
        ImageLoader.getInstance().displayCircleImage(getContext(), url, view, listener);
    }

    @Override
    protected void displayLocalImage(ImageView view, String path) {
        ImageLoader.getInstance().displayLocalCircleImage(getContext(), path, view);
    }

    @Override
    public void loadAdContent(InteractionAdvert interactionAdvert) {
        super.loadAdContent(interactionAdvert);
        mIvAdMsg.setText(interactionAdvert.getDescription());
    }

    @Override
    public void countdownToCloseAd() {
        if (mDuration != 0) {
            super.countdownToCloseAd();
        }
    }

    protected void onViewClick(View view) {
        AdvertisingManager.getInstance().getReporter().click(interactionAdvert);
    }
}
