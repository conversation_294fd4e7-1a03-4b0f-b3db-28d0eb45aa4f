package com.kaolafm.kradio.lib.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;
import android.text.TextUtils;

import com.tencent.mmkv.MMKV;

public class SharedPreferenceUtil {
    private MMKV mSharedPreferences;

    /**
     * 是否是异步提交，true 是异步，使用apply，效率更高，false是同步，使用commit
     */
    private static boolean isAsynch = true;

    private static final String DEFAULT_FILE = "sp_default";
    public static final String PERMISSION_REQUEST = "permission_request";



    private SharedPreferenceUtil() {
    }

    private static class InstanceHolder {

        private final static SharedPreferenceUtil SHARED_PREFERENCE = new SharedPreferenceUtil();
    }

    public static SharedPreferenceUtil getInstance(Context context) {
        return getInstance(context, DEFAULT_FILE);
    }

    public static SharedPreferenceUtil getInstance(Context context, String sharedFileName) {
        return getInstance(context, sharedFileName, Context.MODE_PRIVATE);
    }

    /***
     * 获取Sharedpreference_U 单一实例
     *
     * @param context        应用程序上下文
     * @param sharedFileName sharedPreference文件名称
     * @param mode           sharedPreference 读取模式
     * @return Sharedpreference_U 单例
     */
    public static SharedPreferenceUtil getInstance(Context context,
                                                   String sharedFileName, int mode) {
        if (context == null) {
            return null;
        }

        SharedPreferences sharedPreferences = context.getSharedPreferences(sharedFileName, mode);
        InstanceHolder.SHARED_PREFERENCE.mSharedPreferences = MMKV.mmkvWithID(sharedFileName);
        InstanceHolder.SHARED_PREFERENCE.mSharedPreferences.importFromSharedPreferences(sharedPreferences);
        sharedPreferences.edit().clear().apply();
        return InstanceHolder.SHARED_PREFERENCE;
    }

    /**
     * 是否是异步提交，true 是异步，使用apply，效率更高，false是同步，使用commit
     */
    public static SharedPreferenceUtil getInstance(Context context,
                                                   String sharedFileName, boolean isAsynch, int mode) {
        SharedPreferenceUtil.isAsynch = isAsynch;
        return getInstance(context, sharedFileName, mode);
    }

    /***
     * 获取Sharedpreference_U 单一实例
     *
     * @param context        应用程序上下文
     * @param sharedFileName sharedPreference文件名称
     * @param mode           sharedPreference 读取模式
     * @return Sharedpreference_U 新对象（非单例）
     */
    public static SharedPreferenceUtil newInstance(Context context,
                                                   String sharedFileName, int mode) {
        if (context == null) {
            return null;
        }
        SharedPreferenceUtil spUtil = new SharedPreferenceUtil();
        spUtil.mSharedPreferences = MMKV.mmkvWithID(sharedFileName);
        SharedPreferences sharedPreferences = context.getSharedPreferences(sharedFileName, mode);
        spUtil.mSharedPreferences.importFromSharedPreferences(sharedPreferences);
        sharedPreferences.edit().clear().apply();
        return spUtil;
    }

    public void clear() {
        Editor editor = mSharedPreferences.edit();
        editor.clear();
        apply(editor);
    }

    public void remove(String... keys) {
        Editor edit = mSharedPreferences.edit();
        for (String key : keys) {
            edit.remove(key);
        }
        apply(edit);
    }

    public void putBoolean(String key, boolean value) {
        Editor editor = mSharedPreferences.edit();
        editor.putBoolean(key, value);
        apply(editor);
    }

    public boolean getBoolean(String key, boolean defValue) {
        return TextUtils.isEmpty(key) ? defValue : mSharedPreferences.getBoolean(key, defValue);
    }

    public void putInt(String key, int value) {
        if (!TextUtils.isEmpty(key)) {
            Editor editor = mSharedPreferences.edit();
            editor.putInt(key, value);
            apply(editor);
        }
    }

    public void putIntAsyn(String key, int value) {
        Editor editor = mSharedPreferences.edit();
        editor.putInt(key, value);
        apply(editor);
    }

    public int getInt(String key, int defValue) {
        return TextUtils.isEmpty(key) ? defValue : mSharedPreferences.getInt(key, defValue);
    }

    public void putFloat(String key, float value) {
        if (!TextUtils.isEmpty(key)) {
            Editor editor = mSharedPreferences.edit();
            editor.putFloat(key, value);
            apply(editor);
        }
    }

    public float getFloat(String key, float defValue) {
        return TextUtils.isEmpty(key) ? defValue : mSharedPreferences.getFloat(key, defValue);
    }

    public void putLong(String key, long value) {
        if (!TextUtils.isEmpty(key)) {
            Editor editor = mSharedPreferences.edit();
            editor.putLong(key, value);
            apply(editor);
        }
    }

    public long getLong(String key, long defValue) {
        return TextUtils.isEmpty(key) ? defValue : mSharedPreferences.getLong(key, defValue);
    }

    public void putString(String key, String value) {
        if (!TextUtils.isEmpty(key)) {
            Editor editor = mSharedPreferences.edit();
            editor.putString(key, value);
            apply(editor);
        }
    }

    public String getString(String key, String defValue) {
        return TextUtils.isEmpty(key) ? defValue : mSharedPreferences.getString(key, defValue);
    }

    private void apply(Editor editor) {
        if (isAsynch) {
            editor.apply();
        } else {
            editor.commit();
        }
    }
}
