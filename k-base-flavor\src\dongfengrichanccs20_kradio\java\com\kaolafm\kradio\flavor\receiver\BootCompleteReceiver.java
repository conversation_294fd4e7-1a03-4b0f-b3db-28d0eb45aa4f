package com.kaolafm.kradio.flavor.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.kaolafm.base.utils.KaolaTask;
import com.kaolafm.kradio.flavor.common.SystemBootUtil;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-07-05 11:54
 ******************************************/
public final class BootCompleteReceiver extends BroadcastReceiver {
    private static final String TAG = "BootCompleteReceiver";

    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        Log.i(TAG, "onReceive----------------->" + action);
        if ("com.hsae.auto.ACTION_AUTO_CORE".equals(action)) {
            // 解决https://app.huoban.com/tables/2100000007530121/items/2300001671433707?userId=1229522问题
            new KaolaTask() {
                @Override
                protected Object doInBackground(Object[] objects) {
                    SystemBootUtil systemBootUtil = new SystemBootUtil();
                    systemBootUtil.updateFirstBoot(context, true);
                    return null;
                }
            }.execute();
        }
    }
}
