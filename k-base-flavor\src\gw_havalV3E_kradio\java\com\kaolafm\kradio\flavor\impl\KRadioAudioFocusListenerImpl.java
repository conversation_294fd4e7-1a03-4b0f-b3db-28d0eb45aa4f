package com.kaolafm.kradio.flavor.impl;

import android.util.Log;

import com.kaolafm.kradio.flavor.utils.PlayerUtil;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioFocusListenerInter;
import com.kaolafm.opensdk.player.logic.PlayerManager;

import static android.media.AudioManager.AUDIOFOCUS_GAIN;
import static android.media.AudioManager.AUDIOFOCUS_LOSS;
import static android.media.AudioManager.AUDIOFOCUS_LOSS_TRANSIENT;

public class KRadioAudioFocusListenerImpl implements KRadioAudioFocusListenerInter {
    @Override
    public void onFocusChanged(int status) {
        Log.d("KRadioFocusListenerImpl", " onFocusChanged：" + status);
        if (status == AUDIOFOCUS_LOSS || status == AUDIOFOCUS_LOSS_TRANSIENT) {
            if (PlayerManager.getInstance().isPlaying()) {
                PlayerManager.getInstance().pause(); //强制暂停下
            }
        }
    }
}
