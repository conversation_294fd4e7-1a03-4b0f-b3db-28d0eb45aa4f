<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/x604"
    android:layout_height="@dimen/y325"
    android:gravity="center"
    android:orientation="vertical"
    tools:context="com.kaolafm.kradio.online.mine.login.OnlineLoginOutFragment">

    <TextView
        android:id="@+id/login_out_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/user_logout"
        android:visibility="gone"
        android:textColor="@color/colorWhite"
        android:textSize="@dimen/m28" />

    <TextView

        android:id="@+id/login_out_content"
        android:layout_width="@dimen/x430"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/m36"
        android:gravity="center"
        android:text="@string/online_user_logout_str"
        android:textColor="@color/online_login_out_text_color"
        android:textSize="@dimen/m28" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/m56"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/login_out_ok"
            android:layout_width="@dimen/x234"
            android:layout_height="@dimen/y64"
            android:background="@drawable/online_bg_login_btn_32dp"
            android:gravity="center"
            android:text="@string/online_user_logout_ok"
            android:textColor="@color/user_info_value_color"
            android:textSize="@dimen/m22" />

        <TextView
            android:id="@+id/login_out_cancel"
            android:layout_width="@dimen/x234"
            android:layout_height="@dimen/y64"
            android:layout_marginLeft="@dimen/m40"
            android:background="@drawable/user_login_out_tv_bg"
            android:gravity="center"
            android:text="@string/online_user_logout_cancel"
            android:textColor="@color/user_info_value_color"
            android:textSize="@dimen/m22" />
    </LinearLayout>
</LinearLayout>