package com.kaolafm.kradio.category;

import android.os.Bundle;
import androidx.fragment.app.Fragment;

import com.kaolafm.kradio.category.all.AllTabFragment;
import com.kaolafm.kradio.category.qq.tab.QQTabFragment;
import com.kaolafm.kradio.category.radio.RadioDispatchFragment;
import com.kaolafm.kradio.category.radio.tab.RadioTabFragment;
import com.kaolafm.kradio.category.radio.tab.item.TabItemFragment;
import com.kaolafm.kradio.coin.CoinFragment;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.kradio.categories.AllCategoriesFragment;
import com.kaolafm.kradio.categories.CategoryConstant;
import com.kaolafm.kradio.categories.login.QRCodeFragment;

/**
 * <AUTHOR>
 **/
public class FragmentFactory {

    /**
     * 分类页面
     *
     * @param type
     * @param categoryId
     * @param subcategoryId
     * @return
     */
    public static Fragment createAllCategoriesFragment(@CategoryConstant.MediaType int type, long categoryId, long subcategoryId) {
        AllCategoriesFragment fragment ;
        fragment = (AllCategoriesFragment) RouterManager.getInstance().getRouterFragment(RouterConstance.CATEGORIES_COMPREHENSIVE_URL);
        fragment.setCode(categoryId,subcategoryId);
        return fragment;
    }
//    /**
//     * 分类页面
//     *
//     * @param categoryId
//     * @param subcategoryId
//     * @return
//     */
//    public static Fragment createAllCategoriesFragment(long fatherCode, long sonCode) {
//        return AllCategoriesFragment.getInstance(fatherCode, sonCode);
//    }
    /**
     * 登录页面
     *
     * @return
     */
    public static Fragment comeHereLogin() {
        AllCategoriesFragment fragment = new AllCategoriesFragment();
        Bundle arg = new Bundle();
        arg.putBoolean(CategoryConstant.COME_TO_LOGIN, true);
        fragment.setArguments(arg);
        return fragment;
    }

    /**
     * 积分页面
     *
     * @return
     */
    public static Fragment createCoinFragment() {
        CoinFragment fragment = new CoinFragment();
        return fragment;
    }

    /**
     * All
     *
     * @param type
     * @param categoryId
     * @param subcategoryId
     * @return
     */
    public static Fragment createAllTabFragment(@CategoryConstant.MediaType int type, long categoryId, long subcategoryId) {
        AllTabFragment fragment = new AllTabFragment();
        Bundle arg = new Bundle();
        arg.putInt(CategoryConstant.MEDIA_TYPE, type);
        arg.putLong(CategoryConstant.CATEGORY_ID, categoryId);
        arg.putLong(CategoryConstant.SUBCATEGORY_ID, subcategoryId);
        fragment.setArguments(arg);
        return fragment;
    }

    public static Fragment createRadioDispatchFragment(long categoryId) {
        Bundle bundle = new Bundle();
//        bundle.putInt(CategoryConstant.MEDIA_TYPE, type);
        bundle.putLong(CategoryConstant.CATEGORY_ID, categoryId);
        RadioDispatchFragment subcategoryFragment = new RadioDispatchFragment();
        subcategoryFragment.setArguments(bundle);
        return subcategoryFragment;
    }

    public static Fragment createQRCodeFragment(int loginType) {
        QRCodeFragment qrCodeFragment = new QRCodeFragment();
        Bundle args = new Bundle();
        args.putInt(CategoryConstant.LOGIN_TYPE, loginType);
        qrCodeFragment.setArguments(args);
        return qrCodeFragment;
    }

    public static Fragment createQqTabFragment(long showTabId) {
        QQTabFragment fragment = new QQTabFragment();
        Bundle args = new Bundle();
        args.putLong(CategoryConstant.CATEGORY_ID, showTabId);
        fragment.setArguments(args);
        return fragment;
    }


    public static Fragment createRadioTabFragment(long showTabId, long sonId) {
        RadioTabFragment fragment = new RadioTabFragment();
        Bundle args = new Bundle();
        args.putLong(CategoryConstant.CATEGORY_ID, showTabId);
        args.putLong(CategoryConstant.SUBCATEGORY_ID, sonId);
        fragment.setArguments(args);
        return fragment;
    }
    public static Fragment createRadioTabFragment(long showTabId, long sonId,@CategoryConstant.MediaType int type) {
        RadioTabFragment fragment = new RadioTabFragment();
        Bundle args = new Bundle();
        args.putLong(CategoryConstant.CATEGORY_ID, showTabId);
        args.putLong(CategoryConstant.SUBCATEGORY_ID, sonId);
        args.putLong(CategoryConstant.MEDIA_TYPE, type);
        fragment.setArguments(args);
        return fragment;
    }

    public static Fragment createSubcategoryFragment(long categoryId) {
        SubcategoryFragment fragment = new SubcategoryFragment();
        Bundle args = new Bundle();
        args.putLong(CategoryConstant.CATEGORY_ID, categoryId);
        fragment.setArguments(args);
        return fragment;
    }


    public static Fragment createTabItemFragment(long categoryId) {
        return createTabItemFragment(categoryId, true);
    }

    public static Fragment createTabItemFragment(long categoryId, boolean haveMembers) {
        TabItemFragment fragment = new TabItemFragment();
        Bundle args = new Bundle();
        args.putLong(CategoryConstant.CATEGORY_ID, categoryId);
        args.putLong(CategoryConstant.MEDIA_TYPE, CategoryConstant.MEDIA_TYPE_RADIO);
        args.putBoolean(CategoryConstant.HAVE_MEMBERS, haveMembers);
        fragment.setArguments(args);
        return fragment;
    }

    public static Fragment createTabItemFragment(long categoryId, boolean haveMembers, boolean isLocalRadio) {
        TabItemFragment fragment = new TabItemFragment();
        Bundle args = new Bundle();
        args.putLong(CategoryConstant.CATEGORY_ID, categoryId);
        args.putLong(CategoryConstant.MEDIA_TYPE, CategoryConstant.MEDIA_TYPE_RADIO);
        args.putBoolean(CategoryConstant.HAVE_MEMBERS, haveMembers);
        args.putBoolean(CategoryConstant.IS_LOCAL_RADIO, isLocalRadio);
        fragment.setArguments(args);
        return fragment;
    }
}
