<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/item_sound_quality_main_layout"
    android:layout_width="match_parent"
    android:layout_height="@dimen/y90">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guide_line_center"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.5" />

    <TextView
        android:id="@+id/item_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/text_size3"
        android:textColor="@color/sound_quality_title_text_selector"
        app:layout_constraintBottom_toTopOf="@id/guide_line_center"
        app:layout_constraintHorizontal_bias="0.05"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/item_title_explain"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textColor="@color/sound_quality_sub_text_selector"
        android:textSize="@dimen/text_size1"
        app:layout_constraintLeft_toLeftOf="@id/item_title"
        app:layout_constraintTop_toBottomOf="@id/guide_line_center" />

    <ImageView
        android:id="@+id/item_select"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/live_message_send_success"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_bias="0.9"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/high_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/y1"
        android:background="@color/setting_sound_quality_line"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>