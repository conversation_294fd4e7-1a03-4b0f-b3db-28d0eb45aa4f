package com.kaolafm.kradio.aop;

import com.kaolafm.kradio.setting.SettingItem;
import java.util.List;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;

/**
 * <AUTHOR>
 * @date 2019-06-03
 */
@Aspect
public class ChangSkinAspect {

    /**
     * 长安渠道不需要一键换肤。所以在这里拦截移除一键换肤item
     * @param itemList
     * @param point
     * @throws Throwable
     */
    @AfterReturning(value = "execution(* com.kaolafm.kradio.setting.SettingModel.getItemList(..))", returning = "itemList")
    public void excludeChangeSkin(List<SettingItem> itemList, JoinPoint point) throws Throwable {
        if (itemList != null) {
            //遍历，虽然有性能开销，但是不会因为位置发生改变，再修改这里，如果根据索引移除，位置发生变化需要在这里进行修改
            for (int i = 0, size = itemList.size(); i < size; i++) {
                SettingItem settingItem = itemList.get(i);
                if (settingItem.getFunctionId() == SettingItem.ITEM_FUNCTION_CHANGE_SKIN) {
                    itemList.remove(i);
                    return;
                }
            }
        }
    }
}
