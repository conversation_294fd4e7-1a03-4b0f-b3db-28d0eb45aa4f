package com.kaolafm.kradio.flavor.impl;


import android.util.Log;

import com.fce.thirdutil.FceThirdUtil;
import com.fce.thirdutil.VinResult;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.StringUtil;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-05-22 20:50
 ******************************************/
public final class KRadioDeviceInfoImpl implements KRadioDeviceInfoInter {
    private static final String TAG = "KRadioDeviceInfoImpl";
    @Override
    public String getDeviceId(Object... args) {
        String vin = "";
        VinResult vinResult = FceThirdUtil.getVin(AppDelegate.getInstance().getContext());
        if (vinResult != null && ("success").equals(vinResult.getState())) {
            vin = vinResult.getVin();
        }
        Log.i(TAG, "getDeviceId: " + vin);
        return StringUtil.makeAsciiOnly(vin);
    }

    @Override
    public String getCarType(Object... args) {
        return null;
    }
}
