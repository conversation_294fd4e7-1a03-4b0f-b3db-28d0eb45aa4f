package com.ecarx.sdk.step;

import android.util.Log;

import com.ecarx.sdk.ECarX;
import com.ecarx.sdk.UserInfo;
//import com.kaolafm.opensdk.api.ex.DeviceInfo;
//import com.kaolafm.opensdk.api.ex.EcarxRequest;
import com.kaolafm.opensdk.api.ex.DeviceInfo;
import com.kaolafm.opensdk.api.ex.EcarxRequest;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;


/**
 * <AUTHOR>
 **/
public class GetOpenUidStep extends Step {

    private ECarX ecarx;

    public GetOpenUidStep(ECarX ecarx) {
        this.ecarx = ecarx;
    }

    @Override
    public void exe() {
        Log.i(ECarX.TAG, "exe:"+getClass().getSimpleName());
        UserInfo userInfo = ecarx.getEcarxUserInfo();
        new EcarxRequest().bindDeviceId(userInfo.getMobile(), userInfo.getNickName(), userInfo.getUserIco(), new HttpCallback<DeviceInfo>() {
            @Override
            public void onSuccess(DeviceInfo deviceInfo) {
                // TODO: 2019/4/12 在OPenSdk中更新用户信息
                ecarx.setOpenUid(deviceInfo.getUserId());
                ecarx.updateStep();
                ecarx.nextStep();
            }

            @Override
            public void onError(ApiException e) {
                Log.i(ECarX.TAG, "   onError: error=" + e);
                ecarx.error(e);
            }
        });
    }
}
