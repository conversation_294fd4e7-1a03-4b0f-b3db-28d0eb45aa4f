package com.kaolafm.kradio.lib.init;

import androidx.annotation.IntDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * <AUTHOR>
 * @date 2019-09-10
 */
public final class Process {

    /**
     * 所有进程
     */
    public static final int ALL = 100;

    /**
     * 主进程
     */
    public static final int MAIN = 101;

    /**
     * 其他非主进程
     */
    public static final int OTHER = 102;

    /**
     * 线程的限定注解
     */
    @IntDef({ALL, MAIN, OTHER})
    @Retention(RetentionPolicy.RUNTIME)
    @interface ProcessValue{}
}
