package com.kaolafm.kradio.online.player.adapters;

import android.graphics.Color;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.live1.model.MessageBean;
import com.kaolafm.kradio.live1.player.NimManager;
import com.kaolafm.kradio.online.player.models.SystemMessage;

import java.util.List;

public class LiveMessageAdapter extends BaseAdapter<SystemMessage> {
    private String myselfNickName;

    private static final int ITEM_ENTER_ROOM = 0;
    private static final int ITEM_SEND_MESSAGE = 1;
    private static final int ITEM_RECEIVE_MESSAGE = 2;
    private static final int ITEM_SYSTEM_MESSAGE = 3;

    public LiveMessageAdapter(List<SystemMessage> dataList) {
        super(dataList);
        myselfNickName = NimManager.getInstance().getNickName();
    }

    @Override
    public int getItemViewType(int position) {
        SystemMessage itemData = getItemData(position);
        if (itemData.getMessageType() == SystemMessage.MessageType.ENTER_ROOM) {
            return ITEM_ENTER_ROOM;
        } else if (itemData.getMessageType() == SystemMessage.MessageType.SEND) {
            return ITEM_SEND_MESSAGE;
        } else if (itemData.getMessageType() == SystemMessage.MessageType.RECEIVE) {
            return ITEM_RECEIVE_MESSAGE;
        } else {
            return ITEM_SYSTEM_MESSAGE;
        }
    }

    @Override
    protected BaseHolder<SystemMessage> getViewHolder(ViewGroup parent, int viewType) {
        switch (viewType) {
            case ITEM_ENTER_ROOM:
            case ITEM_SEND_MESSAGE:
            case ITEM_SYSTEM_MESSAGE:
                return new SystemMessageHolder(inflate(parent, R.layout.online_player_item_rv_system_message, viewType));
            default:
                return new LiveBarrageHolder(inflate(parent, R.layout.online_player_item_rv_barrage, viewType));
        }
    }

    private class SystemMessageHolder extends BaseHolder<SystemMessage> {

        private TextView contentTv;


        public SystemMessageHolder(View itemView) {
            super(itemView);
            contentTv = itemView.findViewById(R.id.contentTv);
            itemView.setSelected(false);
        }

        @Override
        public void setupData(SystemMessage messageBean, int position) {
            if (StringUtil.isEmpty(myselfNickName)) {
                myselfNickName = NimManager.getInstance().getNickName();
            }
            String message = null;
            String nickName = null;

            if (messageBean.getUserInfo() != null)
                nickName = messageBean.getUserInfo().getNickName();
            else if (messageBean.getMessage() != null)
                nickName = messageBean.getMessage().nickName;

            boolean highlight = nickName != null && nickName.equals(myselfNickName);

            if (messageBean.getMessageType() == SystemMessage.MessageType.ENTER_ROOM) {
                contentTv.setTextColor(ResUtil.getColor(R.color.online_player_user_message_color));
                message = nickName + " 进入直播间";
            } else if (messageBean.getMessageType() == SystemMessage.MessageType.SEND) {
                contentTv.setTextColor(ResUtil.getColor(R.color.online_player_user_message_color));
                message = nickName + " 发送了新语音消息";
            } else {
                contentTv.setTextColor(ResUtil.getColor(R.color.online_player_system_message_color));
                if (messageBean.getMessage() != null) {
                    message = messageBean.getMessage().contentString;
                    highlight = true;
                }
            }
            contentTv.setText(message);
            itemView.setSelected(highlight);
        }
    }


    private class LiveBarrageHolder extends BaseHolder<SystemMessage> {

        private ImageView soundIv;
        private TextView durationTv, contentTv, cityTv;

        private SpannableStringBuilder stringBuilder;
        private ForegroundColorSpan colorSpan;

        public LiveBarrageHolder(View itemView) {
            super(itemView);
            soundIv = itemView.findViewById(R.id.soundIv);
            durationTv = itemView.findViewById(R.id.durationTv);
            contentTv = itemView.findViewById(R.id.contentTv);
            cityTv = itemView.findViewById(R.id.cityTv);

            colorSpan = new ForegroundColorSpan(Color.parseColor("#9AFFFFFF"));
            itemView.setSelected(false);
        }

        @Override
        public void setupData(SystemMessage systemMessage, int position) {
            if (StringUtil.isEmpty(myselfNickName)) {
                myselfNickName = NimManager.getInstance().getNickName();
            }
            if (systemMessage.getMessage() == null) return;

            MessageBean message = systemMessage.getMessage();
            if (message != null && message.sendChatMsgData != null)
                durationTv.setText(String.format(itemView.getContext().getString(R.string.online_player_live_second_symbol), Math.round(message.sendChatMsgData.duration / 1000f) + ""));

            stringBuilder = new SpannableStringBuilder();
            String nickStr = message.nickName + ":";
            stringBuilder.append(nickStr + message.contentString);
            stringBuilder.setSpan(colorSpan, 0, nickStr.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);

            contentTv.setText(stringBuilder);
            cityTv.setText("");
            itemView.setSelected(message.nickName != null && message.nickName.equals(myselfNickName));
        }
    }
}
