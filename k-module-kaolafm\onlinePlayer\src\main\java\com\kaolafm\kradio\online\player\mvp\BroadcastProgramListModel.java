package com.kaolafm.kradio.online.player.mvp;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.lib.base.mvp.BaseModel;
import com.kaolafm.opensdk.api.broadcast.BroadcastRequest;
import com.kaolafm.opensdk.api.broadcast.ProgramDetails;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.playlist.util.PlayListUtils;
import com.trello.rxlifecycle3.LifecycleTransformer;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/3/6
 */
public class BroadcastProgramListModel extends BaseModel {

    private final LifecycleTransformer mLifecycleTransformer;

    BroadcastProgramListModel(LifecycleTransformer lifecycleTransformer) {
        mLifecycleTransformer = lifecycleTransformer;
    }

    void getBroadcastProgramList(long id, String date, String channel, int classifyId, long listenNum, HttpCallback<ArrayList<PlayItem>> httpCallback) {
        BroadcastRequest broadcastRequest = new BroadcastRequest().bindLifecycle(mLifecycleTransformer);
        HttpCallback callback = new HttpCallback<List<ProgramDetails>>() {
            @Override
            public void onSuccess(List<ProgramDetails> programDetailsList) {
                if (httpCallback == null) {
                    return;
                }
                if (ListUtil.isEmpty(programDetailsList)) {
                    httpCallback.onError(new ApiException(-1, "数据为空"));
                } else {
                    ArrayList<PlayItem> playItemArrayList = PlayListUtils.programDetailsToPlayItem(programDetailsList, channel, 0, classifyId, listenNum);
                    httpCallback.onSuccess(playItemArrayList);
                }
            }

            @Override
            public void onError(ApiException e) {

            }
        };
        broadcastRequest.getBroadcastProgramList(id, date, callback);

    }

    @Override
    public void destroy() {

    }
}
