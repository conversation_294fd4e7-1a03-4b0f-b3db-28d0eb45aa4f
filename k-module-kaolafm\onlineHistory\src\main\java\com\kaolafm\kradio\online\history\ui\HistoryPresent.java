package com.kaolafm.kradio.online.history.ui;


import android.util.Log;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.common.bean.HistoryItem;
import com.kaolafm.kradio.component.ComponentClient;
import com.kaolafm.kradio.component.DynamicComponent;
import com.kaolafm.kradio.component.MainThreadable;
import com.kaolafm.kradio.component.RealCaller;
import com.kaolafm.kradio.constant.UserComponentConst;
import com.kaolafm.kradio.constant.UserStateObserverProcessorConst;
import com.kaolafm.kradio.basedb.bean.HeadTitleItem;
import com.kaolafm.kradio.history.mvp.HistoryModel;
import com.kaolafm.kradio.history.mvp.IHistoryView;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioHistoryInter;
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.ComponentUtil;
import com.kaolafm.kradio.lib.utils.NetworkManager;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.user.UserInfoManager;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by kaolafm on 2018/5/2.
 */

public class HistoryPresent extends BasePresenter<HistoryModel, IHistoryView> {

    private DynamicComponent mHistoryUserObserver;

    private Object mLogin;

    NetworkManager.INetworkReady mNetworkReady = hasNetwork -> {
        if (hasNetwork) {
            getHistoryList();
        }
    };

    public HistoryPresent(IHistoryView view) {
        super(view);
        mHistoryUserObserver = new HistoryUserObserver();
        ComponentUtil.addObserver(UserComponentConst.NAME, mHistoryUserObserver);
    }

    @Override
    public void start() {
        super.start();
        mLogin = ComponentUtil.getResultValue(UserComponentConst.NAME, UserComponentConst.IS_USER_BOUND);
        if (mLogin == null) {
            mLogin = UserInfoManager.getInstance().isUserBound();
        }
        // 注册网络连接监听
        NetworkManager.getInstance().addNetworkReadyListener(mNetworkReady);
        mModel.setListener(aBoolean -> updateList());
    }

    @Override
    public void destroy() {
        ComponentUtil.removeObserver(UserComponentConst.NAME, mHistoryUserObserver);
        mModel.setListener(null);
        super.destroy();
        NetworkManager.getInstance().removeNetworkReadyListener(mNetworkReady);
    }

    @Override
    protected HistoryModel createModel() {
        return new HistoryModel();
    }

    /**
     * 在无网或者未登录的情况下，加载本地历史。
     * 否则加载用户历史。
     */
    public void getHistoryList() {
        //逻辑移动到下面 统一处理目前有两次调用但是逻辑不一致
//        mLogin = ComponentUtil.getResultValue(UserComponentConst.NAME, UserComponentConst.IS_USER_BOUND);
//        if (mLogin && !NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
//            if (mView != null) {
//                mView.showButton(false);
//                mView.showError(ResUtil.getString(R.string.home_network_nosigin), false);
//            }
//            return;
//        }
//        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
//            if (mView != null) {
//                mView.showError(ResUtil.getString(R.string.home_network_nosigin), false);
//            }
//            return;
//        }
        if (mView != null) {
            mView.prepareFragmentStateForShowLoading();
            mView.showLoading();
        }
        updateList();
    }

    /**
     * 从数据库按照时间戳重新拉取数据
     */
    private void updateList() {
        if (null == mModel) {
            return;
        }

        mLogin = ComponentUtil.getResultValue(UserComponentConst.NAME, UserComponentConst.IS_USER_BOUND);
        if (mLogin == null) {
            mLogin = UserInfoManager.getInstance().isUserBound();
        }
        if ((boolean)mLogin && !NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
            if (mView != null) {
                mView.hideLoading();
                mView.showButton(false);
                mView.showError(ResUtil.getString(R.string.network_nosigin), true);
            }
            return;
        }

        mModel.getHistoryList((boolean)mLogin, new HttpCallback<List<HistoryItem>>() {
            @Override
            public void onSuccess(List<HistoryItem> historyList) {
                if (mView != null) {
                    mView.hideLoading();
                    showHistory(historyList);
                }
            }

            @Override
            public void onError(ApiException e) {
                Log.e("HistoryPresent", "onError: " + e);
                if (mView != null) {
                    mView.hideLoading();
                    if ((boolean) mLogin && !NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
                        mView.hideLoading();
                        mView.showButton(false);
                        mView.showError(ResUtil.getString(R.string.network_nosigin), true);
                    } else if ((boolean) mLogin) {
                        mView.showEmpty();
                    }
                }
            }
        });
    }


    private void showHistory(List<HistoryItem> historyList) {
        if (historyList == null) {
            historyList = new ArrayList<>();
        }
        int size = historyList.size();
        Log.e("HistoryPresent", "historyList.size: " + size);
        boolean nonEmpty = size > 0;
        if (mLogin == null) {
            mLogin = UserInfoManager.getInstance().isUserBound();
        }
        if (!(boolean) mLogin) {
            //todo 迁移到byd渠道代码
            HeadTitleItem head = new HeadTitleItem();
            head.setTypeId(OnlineHistoryAdapter.HEAD_UNLOGIN_TIP);
//            head.setItemHeight(nonEmpty ? LayoutParams.WRAP_CONTENT : LayoutParams.MATCH_PARENT);
            historyList.add(0, head);
        }
        if (nonEmpty) {
            //todo 迁移到byd渠道代码
//            HeadTitleItem title = new HeadTitleItem();
//            title.setCount(size);
//            title.setTypeId(OnlineHistoryAdapter.HISTORY_COUNT_TITLE);
//            historyList.add(mLogin ? 0 : 1, title);
            mView.showButton(true);
        } else {
            mView.showButton(false);
        }

        if (!ListUtil.isEmpty(historyList)) {
            mView.showHistory(historyList);
        } else {
            //登录情况下且没有历史才显示空页面
            if ((boolean) mLogin) {
                mView.showEmpty();
            } else {
                KRadioHistoryInter kRadioHistoryInter = ClazzImplUtil
                        .getInter("KRadioHistoryImpl");
                if (kRadioHistoryInter != null) {
                    kRadioHistoryInter.doNoHistoryData(mView);
                } else {
                    mView.showEmpty();
                }
            }
        }
    }

    /**
     * 清空历史
     */
    public void clearHistory() {
        mModel.clearHistory(new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                if (mView != null) {
                    showHistory(null);
                }
            }

            @Override
            public void onError(ApiException e) {

            }
        });
    }

    private class HistoryUserObserver implements DynamicComponent, MainThreadable {

        @Override
        public String getName() {
            return "HistoryPresent-UserObserver";
        }

        @Override
        public boolean onCall(RealCaller caller) {
            String actionName = caller.actionName();
            switch (actionName) {
                case UserStateObserverProcessorConst.USER_LOGIN:
                case UserStateObserverProcessorConst.USER_LOGOUT:
                    getHistoryList();
                    break;
//                case LoginProcessorConst.BACKTYPE_SWITCH:
//                    Log.e("HistoryUserObserver", "onCall: BACKTYPE_SWITCH");
//                    break;
                default:
                    break;
            }
            return false;
        }

        @Override
        public Boolean shouldActionRunOnMainThread(String actionName, ComponentClient caller) {
            return true;
        }
    }
}
