apply plugin: 'maven'

uploadArchives {
    repositories {
        mavenDeployer {
            repository(url: LOCAL_REPO_URL) {  // 设置生成文件保存路径
//                authentication(userName: 'tingban', password: 'vaubMZBfkLoIfyNp')
            }
//            repository(url: uri('../repo'))
            pom.project {
                version LIBRARY_VERSION  // 设置版本，标识版本，并为以后升级做区分
                artifactId ARTIFACTIDKAOLAFM // Maven构建的项目名，比如你的项目中有子项目，就可以使用"项目名-子项目名"的命名方式
                groupId GROUP_ID // 公司名或是组织名。一般来说groupId是由三个部分组成，每个部分之间以"."分隔，第一部分是项目用途，比如用于商业的就是"com"，用于非营利性组织的就　　是"org"；第二部分是公司名，比如"tengxun"、"baidu"、"alibaba"；第三部分是你的项目名
                packaging LIB_TYPE // 项目打包的类型，可以使jar、war、rar、ear、pom，默认是jar
                description LIB_DESCRIPTION
            }
        }
    }
}