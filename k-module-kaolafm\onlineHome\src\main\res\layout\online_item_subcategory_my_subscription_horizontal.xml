<?xml version="1.0" encoding="utf-8"?>
<com.kaolafm.kradio.lib.widget.square.SquareHeightFrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/m228"
    android:layout_height="@dimen/m228">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.kaolafm.kradio.component.ui.base.view.OvalImageView
            android:id="@+id/iv_cover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:contentDescription="@null"
            app:circle="true"
            tool:src="@drawable/media_default_pic" />

        <View
            android:id="@+id/view_item_subscription_mongolian_bg"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/online_class_item_unselect_bg"
             />

        <ImageView
            android:id="@+id/vip_icon"
            android:layout_width="@dimen/m80"
            android:layout_height="@dimen/m36"
            android:layout_alignStart="@+id/iv_item_home_cover"
            android:layout_alignTop="@+id/iv_item_home_cover"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:scaleType="centerInside"
            tool:src="@drawable/icon_vip_class" />

        <View
            android:id="@+id/view_item_subscription_mongolian"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone"
            android:background="@drawable/online_class_item_select_bg" />


        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/llFreq"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/m26"
                android:layout_centerInParent="true"
                android:layout_marginTop="@dimen/m12"
                android:background="@drawable/online_sh_bg_freq"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="FM "
                    android:textColor="@color/online_item_subcategory_freq"
                    android:textSize="@dimen/m16" />

                <TextView
                    android:id="@+id/tvFreq"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/m24"
                    android:textColor="@color/online_item_subcategory_freq"
                    android:textSize="@dimen/text_size1"
                    tools:text="103.9" />
            </LinearLayout>

            <com.kaolafm.kradio.component.ui.base.view.KradioTextView
                android:id="@+id/tv_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textColor="@color/online_category_item_radio_title_text_color"
                android:textSize="@dimen/m24"
                tool:text="箱底私藏!迷醉于电音质感女嗓" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_number"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_alignParentRight="true"
            android:layout_marginTop="@dimen/y8"
            android:layout_marginRight="@dimen/x8"
            android:ellipsize="end"
            android:gravity="center"
            android:maxEms="5"
            android:maxLines="2"
            android:textColor="@color/online_category_item_radio_subtitle_text_color"
            android:textSize="@dimen/online_text_size_ext_2"
            tool:text="20万" />

        <!--        android:background="@drawable/online_offline_layer"-->
        <RelativeLayout
            android:id="@+id/layout_offline"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/online_offline_layer"
            android:visibility="gone">

            <ImageView
                android:id="@+id/layout_offline_iv"
                android:layout_width="@dimen/m100"
                android:layout_height="@dimen/m56"
                android:layout_centerInParent="true"
                android:src="@drawable/online_offline"
                android:visibility="gone"
                tools:visibility="visible" />

        </RelativeLayout>

    </RelativeLayout>


</com.kaolafm.kradio.lib.widget.square.SquareHeightFrameLayout>
