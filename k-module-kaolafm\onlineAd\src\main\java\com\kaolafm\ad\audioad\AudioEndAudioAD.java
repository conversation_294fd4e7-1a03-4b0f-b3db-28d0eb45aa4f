package com.kaolafm.ad.audioad;

import com.kaolafm.ad.control.KradioAdSceneConstants;
import com.kaolafm.opensdk.player.core.listener.IPlayIntercept;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.TempTaskPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

/**
 * @ClassName AudioEndAudioAD
 * @Description 节目关闭广告(编排位广告)
 * <AUTHOR>
 * @Date 2020-03-11 17:41
 * @Version 1.0
 */
public class AudioEndAudioAD extends BaseAudioAD {

    private IPlayIntercept mPlayEndIntercept;

    @Override
    public void onGetData() {
        startPlay(makeAudioAdPlayItem(mAudioAdvert.getUrl(), KradioAdSceneConstants.AD_TYPE_SWITCH_RADIO_AUDIO));
    }

    @Override
    protected TempTaskPlayItem makeAudioAdPlayItem(String url, int type) {
        TempTaskPlayItem adPlayItem = super.makeAudioAdPlayItem(url, type);
        adPlayItem.setNeedNextInnerAction(false);
        adPlayItem.setPlayStateListener(new BasePlayStateListener() {
            @Override
            public void onPlayerEnd(PlayItem playItem) {
                notifyPlayEnd();
                playAudioAdEnd();
            }
        });
        return adPlayItem;
    }

    @Override
    public void onError() {
        notifyPlayEnd();
    }

    private void notifyPlayEnd() {
        if (mPlayEndIntercept != null) {
            mPlayEndIntercept.interceptDone();
        }
    }


    @Override
    public void setCallback(Object o) {
        if (o instanceof IPlayIntercept) {
            mPlayEndIntercept = (IPlayIntercept) o;
        }
    }
}
