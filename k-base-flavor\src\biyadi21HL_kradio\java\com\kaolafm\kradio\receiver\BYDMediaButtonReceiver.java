/*
 * Copyright (C) 2015 AutoRadio
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.kaolafm.kradio.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;
import android.view.KeyEvent;

import com.kaolafm.utils.MediaButtonManagerUtil;


/******************************************
 * 类描述： 比亚迪音频远端控制接收者
 *
 * @version: 1.0
 * @author: shaoningYang
 * @time: 2015-6-17 下午8:51:10
 ******************************************/
public class BYDMediaButtonReceiver extends BroadcastReceiver {
    private MediaButtonManagerUtil mMediaButtonManagerUtil = new MediaButtonManagerUtil();

    private static final String MEDIA_BUTTON_ACTION = "byd.intent.action.MEDIA_BUTTON";

    @Override
    public void onReceive(Context context, Intent intent) {
        if (MEDIA_BUTTON_ACTION.equals(intent.getAction())) {
            String pkgName = intent.getStringExtra("pkgName");
            Log.d("BYDMediaButtonReceiver","BYDMediaButtonReceiver bydstartTest : ");
            Log.d("BYDMediaButtonReceiver","pkgName:"+pkgName);
            if (context.getPackageName().equals(pkgName)) {
                KeyEvent key = intent.getParcelableExtra(Intent.EXTRA_KEY_EVENT);
                if (key == null) {
                    return;
                }
                int keyAction = key.getAction();
                Log.d("BYDMediaButtonReceiver","keyAction:"+keyAction);
                if (keyAction == KeyEvent.ACTION_UP) {
                    return;
                }
                int keyCode = key.getKeyCode();
                mMediaButtonManagerUtil.manageMediaButtonClick(keyCode);
            }
        }
    }
}
