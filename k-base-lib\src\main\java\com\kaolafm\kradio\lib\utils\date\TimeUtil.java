package com.kaolafm.kradio.lib.utils.date;

import android.content.Context;
import android.content.res.Resources;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.kradio.lib.R;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.opensdk.api.media.model.AIAudioDetails;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Formatter;
import java.util.Locale;


/******************************************
 * 类描述： 时间处理工具类 类名称：TimeUtil
 *
 * @version: 1.0
 * @author: shaoningYang
 * @time: 2016-7-20 16:54
 ******************************************/
public class TimeUtil {
    private static final String TAG = "TimeUtil";
    private static SimpleDateFormat sdf = new SimpleDateFormat();
    private static final String DATE_FORMAT_HOUR = "HH:mm:ss";

    private static StringBuffer sb = new StringBuffer();
    /**
     * 时间单位
     */
    private static final int TIME_UNIT = 60;

    /**
     * 处理时间格式，例如：3:5:4 -> 03:05:04
     *
     * @param time
     * @return
     */
    public static String getFormatTime(int time) {
        if (sb != null) {
            // 清除sb里的数据
            sb.delete(0, sb.length());
        }
        //将负转正
        if (time < 0) {
            time = Math.abs(time);
        }

        int remainingMinutes = time % (TIME_UNIT * TIME_UNIT);

        int hours = time / (TIME_UNIT * TIME_UNIT);
        int minutes = remainingMinutes / TIME_UNIT;
        int seconds = remainingMinutes % TIME_UNIT;
        sb.append(changeToDouble(hours)).append(Constants.COLON_STR);
        sb.append(changeToDouble(minutes)).append(Constants.COLON_STR);
        sb.append(changeToDouble(seconds));

        return sb.toString();
    }

    /**
     * 如果当前的数值小于10，将其转换为2位的时间表示，例如：3:5:4 -> 03:05:04
     *
     * @param time
     * @return
     */
    public static String changeToDouble(int time) {
        if (time < 10) {
            return StringUtil.join("0", time);
        }
        return String.valueOf(time);
    }

    public static String getCurrDate(long time) {
        sdf.applyPattern(DATE_FORMAT_HOUR);
        String dStr = sdf.format(new Date(time));
        return dStr;
    }

    /**
     * 将音频时长转换成HH:MM:SS类型
     *
     * @param builder
     * @param formatter
     * @param timeMs
     * @return
     */
    public static String getStringForTime(StringBuilder builder, Formatter formatter, long timeMs) {
        if (timeMs == -1) {
            timeMs = 0;
        }
        long totalSeconds = (timeMs + 500) / 1000;
        long seconds = totalSeconds % 60;
        long minutes = (totalSeconds / 60) % 60;
        long hours = totalSeconds / 3600;
        builder.setLength(0);
        return hours > 0 ? formatter.format("%d:%02d:%02d", hours, minutes, seconds).toString()
                : formatter.format("%02d:%02d", minutes, seconds).toString();
    }

    /**
     * 将音频时长转换成HH:MM:SS类型
     *
     * @param timeMs
     * @return
     */
    public static String getStringForTime(long timeMs) {
        return getStringForTime(timeMs, false);
    }

    /**
     * 将音频时长转换成HH:MM:SS类型
     *
     * @param timeMs
     * @param isChinese 小时、分、秒使用中文汉字，否则使用冒号
     * @return
     */
    public static String getStringForTime(long timeMs, boolean isChinese) {
        StringBuilder builder = new StringBuilder();
        Formatter formatter = new Formatter(builder, Locale.getDefault());
        if (timeMs == -1) {
            timeMs = 0;
        }
        long totalSeconds = (timeMs + 500) / 1000;
        long seconds = totalSeconds % 60;
        long minutes = (totalSeconds / 60) % 60;
        long hours = totalSeconds / 3600;
        builder.setLength(0);
        if (isChinese) {
            return hours > 0 ? formatter.format("%d小时%02d分%02d秒", hours, minutes, seconds).toString()
                    : formatter.format("%02d分%02d", minutes, seconds).toString();
        } else {
            return hours > 0 ? formatter.format("%d:%02d:%02d", hours, minutes, seconds).toString()
                    : formatter.format("%02d:%02d", minutes, seconds).toString();
        }
    }

    /**
     * 将音频时长转换成HH:MM:SS类型,即使不足1小时也显示小时数
     *
     * @param timeMs
     * @return
     */
    public static String getStringForTimeShowHour(long timeMs) {
        StringBuilder builder = new StringBuilder();
        Formatter formatter = new Formatter(builder, Locale.getDefault());
        if (timeMs == -1) {
            timeMs = 0;
        }
        long totalSeconds = (timeMs + 500) / 1000;
        long seconds = totalSeconds % 60;
        long minutes = (totalSeconds / 60) % 60;
        long hours = totalSeconds / 3600;
        builder.setLength(0);
        return formatter.format("%02d:%02d:%02d", hours, minutes, seconds).toString();

    }

    /**
     * 将音频时长转换成HH:MM:SS类型
     * 如果不足1小时，则不显示小时数，如果达到1小时就显示小时数,小时数要保留两位
     *
     * @param timeMs
     * @return
     */
    public static String getStringForTimeAutoShowHours(long timeMs) {
        StringBuilder builder = new StringBuilder();
        Formatter formatter = new Formatter(builder, Locale.getDefault());
        if (timeMs == -1) {
            timeMs = 0;
        }
        long totalSeconds = (timeMs + 500) / 1000;
        long seconds = totalSeconds % 60;
        long minutes = (totalSeconds / 60) % 60;
        long hours = totalSeconds / 3600;
        builder.setLength(0);
        return hours > 0 ? formatter.format("%02d:%02d:%02d", hours, minutes, seconds).toString()
                : formatter.format("%02d:%02d", minutes, seconds).toString();
    }

    /**
     * 是否满1小时
     *
     * @param timeMs
     * @return
     */
    public static boolean isReachOneHour(long timeMs) {
        return timeMs >= 60 * 60 * 1000;
    }


    private static final int SECONDS_OF_1_MINUTE = TIME_UNIT;
    private static final int SECONDS_OF_10_MINUTE = 10 * TIME_UNIT;


//    private static final int seconds_of_30minutes = 30 * TIME_UNIT;

    private static final int SECONDS_OF_1_HOUR = TIME_UNIT * TIME_UNIT;

    private static final int SECONDS_OF_1_DAY = 24 * TIME_UNIT * TIME_UNIT;

    private static final int SECONDS_OF_15_DAYS = SECONDS_OF_1_DAY * 15;

//    private static final int seconds_of_30days = SECONDS_OF_1_DAY * 30;

//    private static final int seconds_of_6months = seconds_of_30days * 6;
//
//    private static final int seconds_of_1year = seconds_of_30days * 12;

    /**
     * 判断是否今年
     *
     * @param timeStamp
     * @return
     */
    private static boolean isThisYear(long timeStamp) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.MONTH, 0);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return timeStamp >= calendar.getTimeInMillis();
    }

    /**
     * 判断是否今天
     *
     * @param timeStamp
     * @return
     */
    private static boolean isToday(long timeStamp) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return timeStamp >= calendar.getTimeInMillis();
    }

    /**
     * 判断时间戳到当天00：00:00有几个自然日
     *
     * @param timeStamp
     * @return 昨天：1 前天：2 3天前：3
     */
    private static int getDaysToToday(long timeStamp) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        long today = calendar.getTimeInMillis();

        if (today >= timeStamp) {
            return (int) ((today - timeStamp) / (SECONDS_OF_1_DAY * 1000)) + 1;
        }
        return -1;
    }

    private static String parseTime(Resources res, long time) {
        SimpleDateFormat sdf = new SimpleDateFormat(res.getString(R.string.simple_time_format_str), Locale.getDefault());
        return sdf.format(new Date(time));
    }

    private static String parseDate(Resources res, long time) {
        SimpleDateFormat sdf = new SimpleDateFormat(res.getString(R.string.simple_date_line_format_str), Locale.getDefault());
        return sdf.format(new Date(time));
    }

    private static String parseDateExcludeYear(Resources res, long time) {
        SimpleDateFormat sdf = new SimpleDateFormat(res.getString(R.string.simple_date_line_exclude_year_format_str), Locale.getDefault());
        return sdf.format(new Date(time));
    }

    /**
     * 获取Ai电台碎片更新时间
     *
     * @param context
     * @param timeStamp
     * @return
     */
    public static String getAIAudioUpdateTime(Context context, long timeStamp, int timeType) {
        String tempTimeStampStr = String.valueOf(timeStamp);
        long tempTimeStamp = timeStamp;
        // 解决31910,31824问题
        if (tempTimeStampStr.length() >= 13) {
            tempTimeStamp /= 1000;
        } else {
            timeStamp *= 1000;
        }
        long elapsedTime = (System.currentTimeMillis() / 1000 - tempTimeStamp);
        Resources res = context.getResources();

        switch (timeType) {
            case AIAudioDetails.TIME_TYPE_SIMPLICITY:
                //不显示具体时间
                if (isToday(tempTimeStamp * 1000)) {
                    if (elapsedTime <= SECONDS_OF_10_MINUTE) {
                        return res.getString(R.string.audio_update1_str);
                    }
                    if (elapsedTime <= SECONDS_OF_1_HOUR) {
                        return StringUtil.format(res.getString(R.string.audio_update2_str), elapsedTime / SECONDS_OF_1_MINUTE);
                    }
                    return StringUtil.format(res.getString(R.string.audio_update3_str), elapsedTime / SECONDS_OF_1_HOUR);
                }
                int daysToToday = getDaysToToday(tempTimeStamp * 1000);
                if (daysToToday == 1) {
                    return StringUtil.format(res.getString(R.string.audio_update6_str), parseTime(res, tempTimeStamp * 1000));
                } else if (daysToToday == 2) {
                    return StringUtil.format(res.getString(R.string.audio_update7_str), parseTime(res, tempTimeStamp * 1000));
                } else if (daysToToday <= 7) {
                    return StringUtil.format(res.getString(R.string.audio_update8_str), daysToToday, parseTime(res, tempTimeStamp * 1000));
                } else {
                    return res.getString(R.string.audio_update9_str);
                }
            case AIAudioDetails.TIME_TYPE_DEFAULT:
                //默认
                if (isThisYear(tempTimeStamp * 1000)) {
                    if (isToday(tempTimeStamp * 1000)) {
                        if (elapsedTime <= SECONDS_OF_10_MINUTE) {
                            return res.getString(R.string.audio_update1_str);
                        }
                        if (elapsedTime <= SECONDS_OF_1_HOUR) {
                            return StringUtil.format(res.getString(R.string.audio_update2_str), elapsedTime / SECONDS_OF_1_MINUTE);
                        }
                        return StringUtil.format(res.getString(R.string.audio_update3_str), elapsedTime / SECONDS_OF_1_HOUR);
                    }
                    daysToToday = getDaysToToday(tempTimeStamp * 1000);
                    if (daysToToday == 1) {
                        return StringUtil.format(res.getString(R.string.audio_update6_str), parseTime(res, tempTimeStamp * 1000));
                    } else if (daysToToday == 2) {
                        return StringUtil.format(res.getString(R.string.audio_update7_str), parseTime(res, tempTimeStamp * 1000));
                    } else if (daysToToday <= 7) {
                        return StringUtil.format(res.getString(R.string.audio_update8_str), daysToToday, parseTime(res, tempTimeStamp * 1000));
                    } else {
                        return parseDateExcludeYear(res, tempTimeStamp * 1000);
                    }
                }
                return parseDate(res, tempTimeStamp * 1000);
            default:
                return null;
        }
    }

    /**
     * 获取Ai电台碎片更新时间
     *
     * @param context
     * @param timeStamp
     * @return
     */
    public static String getTopicPostsTime(Context context, long timeStamp) {
        String tempTimeStampStr = String.valueOf(timeStamp);
        long tempTimeStamp = timeStamp;
        // 解决31910,31824问题
        if (tempTimeStampStr.length() >= 13) {
            tempTimeStamp /= 1000;
        } else {
            timeStamp *= 1000;
        }
        long elapsedTime = (System.currentTimeMillis() / 1000 - tempTimeStamp);
        Resources res = context.getResources();

        SimpleDateFormat sdf = new SimpleDateFormat(res.getString(R.string.simple_date_line_exclude_year_format_str), Locale.getDefault());
        if (isThisYear(tempTimeStamp * 1000)) {
            if (isToday(tempTimeStamp * 1000)) {
                if (elapsedTime <= SECONDS_OF_10_MINUTE) {
                    return res.getString(R.string.posts_update1_str);
                }
                if (elapsedTime <= SECONDS_OF_1_HOUR) {
                    return StringUtil.format(res.getString(R.string.posts_update2_str), elapsedTime / SECONDS_OF_1_MINUTE);
                }
                return StringUtil.format(res.getString(R.string.posts_update3_str), parseTime(res, tempTimeStamp * 1000));
            }
            int daysToToday = getDaysToToday(tempTimeStamp * 1000);
            if (daysToToday == 1) {
                return StringUtil.format(res.getString(R.string.posts_update4_str), parseTime(res, tempTimeStamp * 1000));
            } else if (daysToToday == 2) {
                return StringUtil.format(res.getString(R.string.posts_update5_str), parseTime(res, tempTimeStamp * 1000));
            } else {
                sdf.applyPattern("MM-dd HH:mm");
                return sdf.format(new Date(tempTimeStamp * 1000));
            }
        }
        sdf.applyPattern("yyyy-MM-dd HH:mm");
        return sdf.format(new Date(tempTimeStamp * 1000));
    }

    /**
     * 获取碎片更新时间
     *
     * @param context
     * @param timeStamp
     * @return
     */
    public static String getAudioUpdateTime(Context context, long timeStamp) {
        String tempTimeStampStr = String.valueOf(timeStamp);
        long tempTimeStamp = timeStamp;
        // 解决31910,31824问题
        if (tempTimeStampStr.length() >= 13) {
            tempTimeStamp /= 1000;
        } else {
            timeStamp *= 1000;
        }
        long elapsedTime = (System.currentTimeMillis() / 1000 - tempTimeStamp);
        Resources res = context.getResources();
        if (elapsedTime <= SECONDS_OF_1_MINUTE) {
            return res.getString(R.string.audio_update1_str);
        }
        if (elapsedTime <= SECONDS_OF_1_HOUR) {
            return StringUtil.format(res.getString(R.string.audio_update2_str), elapsedTime / SECONDS_OF_1_MINUTE);
        }
        if (elapsedTime <= SECONDS_OF_1_DAY) {
            return StringUtil.format(res.getString(R.string.audio_update3_str), elapsedTime / SECONDS_OF_1_HOUR);
        }
        if (elapsedTime <= SECONDS_OF_15_DAYS) {
            return StringUtil.format(res.getString(R.string.audio_update4_str), elapsedTime / SECONDS_OF_1_DAY);
        }
        SimpleDateFormat sdf = new SimpleDateFormat(res.getString(R.string.simple_date_format_str));
        Date curDate = new Date(timeStamp);
        String dataStrNew = sdf.format(curDate);
        return StringUtil.format(res.getString(R.string.audio_update5_str), dataStrNew);
    }

    public static int getHours(int millis) {
        int second = (millis + 500) / 1000;
        int hours = second / 3600;
        return hours;
    }

    public static int getMinutes(int millis) {
        int second = (millis + 500) / 1000;
        int minutes = (second / 60) % 60;
        return minutes;
    }

    public static int getSeconds(int millis) {
        int totalSeconds = (millis + 500) / 1000;
        int seconds = totalSeconds % 60;
        return seconds;
    }
}
