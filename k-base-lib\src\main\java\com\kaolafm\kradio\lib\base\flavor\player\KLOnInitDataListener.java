package com.kaolafm.kradio.lib.base.flavor.player;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-02-09 10:02
 ******************************************/

public interface KLOnInitDataListener {
    /**
     * 获取设备序列号
     *
     * @return
     */
    String onObtainDeviceSerialNo();

    /**
     * 是否启用本地读存openId功能
     *
     * @return
     */
    boolean nonUseLocalOpenId();

    /**
     * 初始化udid
     *
     * @return
     */
    String initUdid();
}
