package com.kaolafm.kradio.flavor.impl;

import android.annotation.SuppressLint;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.os.RemoteException;
import android.util.Log;

import com.iflytek.autofly.home.aidl.IMediaBean;
import com.iflytek.autofly.home.aidl.IMediaChangeCallBack;
import com.iflytek.autofly.home.aidl.IMediaChangeInterFace;
import com.kaolafm.kradio.lib.base.flavor.KRadioThirdPlatformInitInter;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayListStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import java.util.ArrayList;
import java.util.List;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-07-13 20:19
 ******************************************/
public class KRadioThirdPlatformInitImpl implements KRadioThirdPlatformInitInter {
    private static final String TAG = "KRadioThirdPlatformInitImpl";
    private static final int MEDIA_TYPE = 4;

    @Override
    public boolean initThirdPlatform(Object... args) {
        Context context = (Context) args[0];
        Intent intent = new Intent();
        intent.setClassName("com.incall.apps.flexiblelauncher", "com.iflytek.autofly.home.service.MediaService");
        context.bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE);
        PlayerManager playerManager = PlayerManager.getInstance();
        playerManager.addPlayControlStateCallback(mBasePlayStateListener);
        playerManager.addPlayListControlStateCallback(mPlayListIPlayerListChangedListener);
        return true;
    }

    @Override
    public boolean destroyThirdPlatform(Object... args) {
        PlayerManager playerManager = PlayerManager.getInstance();
        playerManager.removePlayListControlStateCallback(mPlayListIPlayerListChangedListener);
        playerManager.removePlayControlStateCallback(mBasePlayStateListener);
        unregisterOnCountListener();
        return true;
    }

    private IMediaChangeInterFace mediaChangeInterFace;
    private ServiceConnection serviceConnection = new ServiceConnection() {
        @SuppressLint("LongLogTag")
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            Log.i(TAG, "--onServiceConnected--" + service);
            mediaChangeInterFace = IMediaChangeInterFace.Stub.asInterface(service);
            try {
                mediaChangeInterFace.registerOnCountListener(MEDIA_TYPE, iMediaChangeCallBack);
                mediaChangeInterFace.mediaPlayLrcChange(MEDIA_TYPE, null);
                if (PlayerManagerHelper.getInstance().isPlaying()) {
                    PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
                    onPlaying(playItem);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @SuppressLint("LongLogTag")
        @Override
        public void onServiceDisconnected(ComponentName name) {
            Log.i(TAG, "---onServiceDisconnected---");
            unregisterOnCountListener();
            mediaChangeInterFace = null;
        }
    };

    private IMediaChangeCallBack iMediaChangeCallBack = new IMediaChangeCallBack() {
        @SuppressLint("LongLogTag")
        @Override
        public void sendMediaSatus(int type, int status) throws RemoteException {
            Log.i(TAG, "sendMediaSatus start " + status);
        }

        @SuppressLint("LongLogTag")
        @Override
        public void playMusicByPosition(int type, int position) throws RemoteException {
            Log.i(TAG, "playMusicByPosition start " + position);
        }

        @SuppressLint("LongLogTag")
        @Override
        public void sendMediaPlayModel(int type, int model) throws RemoteException {
            Log.i(TAG, "sendMediaPlayModel start " + model);
        }

        @Override
        public boolean sendProgress(int type, int progress) throws RemoteException {
            return false;
        }

        @SuppressLint("LongLogTag")
        @Override
        public List<IMediaBean> getPlayingMediaBeanList(int type) throws RemoteException {
            Log.i(TAG, "getPlayingMediaBeanList start ");
            return null;
        }

        @SuppressLint("LongLogTag")
        @Override
        public List<com.iflytek.autofly.home.aidl.ILrcRow> getPlayingMediaLrcList(int type) throws RemoteException {
            Log.i(TAG, "getPlayingMediaLrcList start ");
            return null;
        }

        @Override
        public IBinder asBinder() {
            return null;
        }
    };

    private BasePlayStateListener mBasePlayStateListener = new BasePlayStateListener() {

        @Override
        public void onPlayerPlaying(PlayItem playItem) {
            super.onPlayerPlaying(playItem);
            onPlaying(playItem);
        }

        @Override
        public void onProgress(PlayItem playItem, long progress, long l) {
            super.onProgress(playItem, progress, l);
            if (isMediaInterIsAvailable()) {
                try {
                    mediaChangeInterFace.mediaProgressChange(MEDIA_TYPE, (int) progress);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
        }
    };

    private void unregisterOnCountListener() {
        if (isMediaInterIsAvailable()) {
            try {
                mediaChangeInterFace.unregisterOnCountListener(MEDIA_TYPE, iMediaChangeCallBack);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    private boolean isMediaInterIsAvailable() {
        return mediaChangeInterFace != null;
    }

    private IMediaBean transPlayItemToIMediaBean(PlayItem playItem) {
        IMediaBean iMediaBean = new IMediaBean(null,
                MEDIA_TYPE,
                0,
                0,
                1,
                playItem.getTitle(),
                playItem.getAlbumTitle(),
                PlayerManagerHelper.getInstance().isPlaying() ? 1 : 0,
                PlayerManagerHelper.getInstance().getPlayItemPicUrl(playItem),
                playItem.getDuration(),
                playItem.getPosition());
        return iMediaBean;
    }

    private void notifyPlayerListUpdate() {
        List<PlayItem> playItemArrayList = PlayerManagerHelper.getInstance().getPlayList(true);
        ArrayList<IMediaBean> iMediaBeanArrayList = new ArrayList<>();
        if (playItemArrayList == null) {
            playerListUpdate(iMediaBeanArrayList);
        } else {
            for (int i = 0, size = playItemArrayList.size(); i < size; i++) {
                IMediaBean iMediaBean = transPlayItemToIMediaBean(playItemArrayList.get(i));
                iMediaBeanArrayList.add(iMediaBean);
            }
            playerListUpdate(iMediaBeanArrayList);
        }
    }

    private void playerListUpdate(ArrayList<IMediaBean> iMediaBeans) {
        if (isMediaInterIsAvailable()) {
            try {
                mediaChangeInterFace.mediaPlayListChange(MEDIA_TYPE, iMediaBeans);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    private void onPlaying(PlayItem playItem) {
        if (isMediaInterIsAvailable()) {
            IMediaBean iMediaBean = transPlayItemToIMediaBean(playItem);
            try {
                mediaChangeInterFace.mediaPrepared(iMediaBean);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    private IPlayListStateListener mPlayListIPlayerListChangedListener = new IPlayListStateListener() {

        @Override
        public void onPlayListChange(List<PlayItem> list) {
            notifyPlayerListUpdate();
        }

        @Override
        public void onPlayListChangeError(int i) {

        }
    };
}
