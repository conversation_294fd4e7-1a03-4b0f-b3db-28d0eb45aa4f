package com.kaolafm.kradio.online.categories.radio;

import android.os.Bundle;
import androidx.fragment.app.Fragment;
import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.common.SubcategoryItemBean;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.kradio.lib.base.ui.online.BaseShowHideFragment;
import com.kaolafm.kradio.lib.base.ui.online.BaseViewPagerFragment;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.online.categories.CategoryConstant;
import com.kaolafm.kradio.online.categories.ErrorCode;
import com.kaolafm.kradio.online.categories.event.SubcategoryItemEvent;
import com.kaolafm.kradio.online.categories.tab.TabContract;
import com.kaolafm.kradio.online.categories.tab.TabItemFragment;
import com.kaolafm.opensdk.api.operation.model.category.Category;
import com.kaolafm.opensdk.api.operation.model.category.LeafCategory;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 **/
class RadioTabPresenter extends BasePresenter<RadioTabModel, TabContract.IView> implements TabContract.IPresenter {

    private long mItemId = 0;
    private String pageId = "";

    public RadioTabPresenter(TabContract.IView view, long sonCode, String pageId) {
        super(view);
        mItemId = sonCode;
        this.pageId = pageId;
    }

    @Override
    protected RadioTabModel createModel() {
        return new RadioTabModel();
    }

    @Override
    public void loadAIData(long showTabId) {
        Log.i("RadioTabPresenter", "loadAIData: " + showTabId);
        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
            if (mView != null) {
                mView.showError(new ApiException(ErrorCode.NO_NET, "数据为空"));
            }
            return;
        }
        mModel.getSubcategoryList(String.valueOf(showTabId), new HttpCallback<List<Category>>() {
            @Override
            public void onSuccess(List<Category> categories) {
                if (categories != null && categories.size() > 0) {
//                    获取AI电台 常见电台的 次级分类
                    loadData(Long.parseLong(categories.get(0).getCode()));
                } else if (mView != null) {
                    mView.showError(new ApiException(ErrorCode.NO_NET, "数据为空"));
                }
            }

            @Override
            public void onError(ApiException e) {
                if (mView != null) {
                    mView.showError(new ApiException(ErrorCode.NO_NET, "数据为空"));
                }
            }
        });
    }

    @Override
    public void loaCiData(long showTabId) {
        mModel.getSubcategoryList(showTabId + "", new HttpCallback<List<Category>>() {
            @Override
            public void onSuccess(List<Category> categories) {
                ArrayList<SubcategoryItemBean> itemBeans = new ArrayList<>();
                if (!ListUtil.isEmpty(categories)) {
                    for (Category category : categories) {
                        SubcategoryItemBean subcategoryItemBean = new SubcategoryItemBean();
                        String code = category.getCode();
                        if (TextUtils.isEmpty(code)) {
                            code = "0";
                        }
                        subcategoryItemBean.setId(Long.parseLong(code));
                        subcategoryItemBean.setName(category.getName());
                        subcategoryItemBean.setType(category.getType());
                        subcategoryItemBean.setItemType(SubcategoryItemBean.TYPE_ITEM_BROADCAST_CATEGORY);
                        itemBeans.add(subcategoryItemBean);
                    }
                }
                EventBus.getDefault().post(new SubcategoryItemEvent(1, itemBeans));
            }

            @Override
            public void onError(ApiException e) {
                if (mView != null) {
                    mView.showError(e);
                }
            }
        });
    }


    @Override
    public void loadData(long parentId) {
        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
            if (mView != null) {
                mView.showError(new ApiException(ErrorCode.NO_NET, "数据为空"));
            }
            return;
        }
        mModel.getSubcategoryList(String.valueOf(parentId), new HttpCallback<List<Category>>() {
            @Override
            public void onSuccess(List<Category> categories) {
                showSubcategoryFragments(categories);
            }

            @Override
            public void onError(ApiException e) {
                if (mView != null) {
                    mView.showError(new ApiException(ErrorCode.NO_NET, "数据为空"));
                }
            }
        });
    }

    private void showSubcategoryFragments(List<Category> categories) {
        List<Fragment> fs = new ArrayList<>();
        String[] titles = new String[categories.size()];
        String[] ids = new String[categories.size()];
        int mTempIndex = 0;
        //遍历接口
        if (categories.size() > 0) {
            for (int i = 0; i < categories.size(); i++) {
                Category category = categories.get(i);
                if (Long.valueOf(category.getCode()) == mItemId) {
                    mTempIndex = i;
                }
                String name = category.getName();
                titles[i] = name;
                ids[i] = category.getCode();
                if (category instanceof LeafCategory) {
                    fs.add(createTabItemFragment(name, pageId, Long.valueOf(category.getCode()), true));
                } else {
                    fs.add(createTabItemFragment(name, pageId, Long.valueOf(category.getCode()), false));
                }
            }
        }
        if (mView != null) {
            if (titles.length > 0) {
                mView.showData(titles, ids, fs, mTempIndex);
            } else {
                mView.showError(new ApiException("数据为空"));
            }
        }
    }

    public Fragment createTabItemFragment(String name, String pageId, long categoryId, boolean haveMembers, boolean isLocalRadio) {
        TabItemFragment fragment = new TabItemFragment();
        Bundle args = new Bundle();
        args.putString(CategoryConstant.PAGE_ID, pageId);
        args.putString(BaseViewPagerFragment.ARGUMENT_TAG, name);
        args.putLong(CategoryConstant.CATEGORY_ID, categoryId);
        args.putLong(CategoryConstant.MEDIA_TYPE, CategoryConstant.MEDIA_TYPE_RADIO);
        args.putBoolean(CategoryConstant.HAVE_MEMBERS, haveMembers);
        args.putBoolean(CategoryConstant.IS_LOCAL_RADIO, isLocalRadio);
        fragment.setArguments(args);
        return fragment;
    }

    public Fragment createTabItemFragment(String name, String pageId, long categoryId, boolean haveMembers) {
        TabItemFragment fragment = new TabItemFragment();
        Bundle args = new Bundle();
        args.putString(CategoryConstant.PAGE_ID, pageId);
        args.putString(BaseViewPagerFragment.ARGUMENT_TAG, name);
        args.putLong(CategoryConstant.CATEGORY_ID, categoryId);
        args.putLong(CategoryConstant.MEDIA_TYPE, CategoryConstant.MEDIA_TYPE_RADIO);
        args.putBoolean(CategoryConstant.HAVE_MEMBERS, haveMembers);
        fragment.setArguments(args);
        return fragment;
    }

    /**
     * 根据id,选择tab index
     */
    private int getIndexById(List<Category> tabItems, long showTabId) {
        int index = 0;
        for (int i = 0; i < tabItems.size(); i++) {
            if (Long.valueOf(tabItems.get(i).getCode()) == showTabId) {
                index = i;
                break;
            }
        }
        return index;
    }

    private boolean isNetworkAvailable() {
        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
            mView.showError(new ApiException(ErrorCode.NO_NET, ""));
            return false;
        } else {
            return true;
        }
    }

}
