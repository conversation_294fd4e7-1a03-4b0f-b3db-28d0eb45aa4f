<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/music_player_controller_bar_left"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.13" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/music_player_controller_bar_right"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.87" />

    <ImageView
        android:id="@id/pcb_switch_playBack_mode"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/color_main_button_click_selector"
        android:scaleType="centerInside"
        android:src="@drawable/player_all_mode_selector"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="W,1:1"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.7"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintLeft_toRightOf="@id/music_player_controller_bar_left"
        app:layout_constraintRight_toLeftOf="@id/pcb_like"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@id/pcb_like"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/color_main_button_click_selector"
        android:scaleType="centerInside"
        android:src="@drawable/player_switch_like_selector"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="W,1:1"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.7"
        app:layout_constraintLeft_toRightOf="@id/pcb_switch_playBack_mode"
        app:layout_constraintRight_toLeftOf="@id/pcb_play_pre_audio"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@id/pcb_play_pre_audio"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/color_main_button_click_selector"
        android:scaleType="centerInside"
        android:src="@drawable/player_pre_selector"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="W,1:1"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.7"
        app:layout_constraintLeft_toRightOf="@id/pcb_like"
        app:layout_constraintRight_toLeftOf="@id/pcb_switch_playState_mode"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/pcb_switch_playState_mode"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@null"
        android:scaleType="centerInside"
        android:src="@drawable/player_switch_play_selector"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="W,1:1"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.9"
        app:layout_constraintLeft_toRightOf="@id/pcb_play_pre_audio"
        app:layout_constraintRight_toLeftOf="@id/pcb_play_next_audio"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@id/pcb_play_next_audio"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/color_main_button_click_selector"
        android:scaleType="centerInside"
        android:src="@drawable/player_next_selector"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="W,1:1"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.7"
        app:layout_constraintLeft_toRightOf="@id/pcb_switch_playState_mode"
        app:layout_constraintRight_toLeftOf="@id/pcb_lrc"
        app:layout_constraintTop_toTopOf="parent" />


    <ImageView
        android:id="@id/pcb_lrc"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/color_main_button_click_selector"
        android:scaleType="centerInside"
        android:src="@drawable/player_switch_lrc_selector"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="W,1:1"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.7"
        app:layout_constraintLeft_toRightOf="@id/pcb_play_next_audio"
        app:layout_constraintRight_toLeftOf="@id/pcb_playlist"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@id/pcb_playlist"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/color_main_button_click_selector"
        android:scaleType="centerInside"
        android:src="@drawable/player_playlist_selector"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="W,1:1"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.7"
        app:layout_constraintLeft_toRightOf="@id/pcb_lrc"
        app:layout_constraintRight_toLeftOf="@id/music_player_controller_bar_right"
        app:layout_constraintTop_toTopOf="parent" />

    <fr.castorflex.android.circularprogressbar.CircularProgressBar
        android:id="@+id/pcb_play_loading"
        style="@style/CustomerCircularProgressBar"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:cpb_color="@color/circular_progress_color"
        app:cpb_stroke_width="@dimen/loading_progress_width"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="W,1:1"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.92"
        app:layout_constraintLeft_toRightOf="@id/pcb_play_pre_audio"
        app:layout_constraintRight_toLeftOf="@id/pcb_play_next_audio"
        app:layout_constraintTop_toTopOf="parent" />
</merge>
