package com.kaolafm.kradio.category.qq;

import androidx.fragment.app.Fragment;
import android.view.View;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.categories.CategoryConstant;
import com.kaolafm.kradio.lib.base.ui.BaseLazyFragment;

/**
 * <AUTHOR>
 **/
public class QqDispatchFragment extends BaseLazyFragment<QqDispatchContract.IPresenter> implements QqDispatchContract.IView {

    @Override
    protected int getLayoutId() {
        return R.layout.fragment_qq;
    }

    @Override
    protected int getLayoutId_Tow() {
        return 0;
    }

    @Override
    protected QqDispatchContract.IPresenter createPresenter() {
        return new QqDispatchPresenter(this);
    }

    @Override
    public void initView(View view) {
        //因为使用getFragmentManager().beginTransaction().replace(R.id.qq_content, mFragmentUnload).commit();
        //所以,不需要解析view
    }

    @Override
    public void showData(Fragment fragment) {
        getChildFragmentManager().beginTransaction().replace(R.id.qq_content, fragment).commit();
        fragment.setUserVisibleHint(true);
    }

    @Override
    protected void lazyLoad() {
        long categoryId = getArguments().getLong(CategoryConstant.CATEGORY_ID);
        mPresenter.loadData(categoryId);
    }

}
