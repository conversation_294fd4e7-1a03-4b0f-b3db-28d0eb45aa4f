package com.kaolafm.ad.processor;

import android.annotation.SuppressLint;
import android.util.Log;

import com.kaolafm.ad.ADDataHandle.GetAdDataMap;
import com.kaolafm.ad.KradioAdAudioManager;
import com.kaolafm.ad.control.KradioAdSceneConstants;
import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.ad.implement.AdvertPlayerImpl;
import com.kaolafm.kradio.component.ActionProcessor;
import com.kaolafm.kradio.component.RealCaller;
import com.kaolafm.kradio.component.SharedConst;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.player.helper.intercept.AdPlayChainIntercept;
import com.kaolafm.opensdk.player.logic.model.PlayItemConstants;
import com.kaolafm.opensdk.player.logic.model.item.RadioPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.playlist.RadioPlayListControl;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

/**
 * 切换节目曝光
 * zoneId 和 adSceneId 相关wiki: http://wiki.kaolafm.com/pages/viewpage.action?pageId=11405204
 */

@SharedConst
public class AdExposeProcess implements ActionProcessor {
    private static final String TAG = "AdExposeProcess: " + PlayerConstants.LOG_TAG;
    private static final String ACTION_EXPOSE = "ad_expose_switchprogress";
    private static final String KEY_EXPOSE_AD_PLAYITEM = "key_expose_ad_playitem";
    private static final String KEY_EXPOSE_AD_CALLBACK = "key_expose_ad_callback";

    public AdExposeProcess() {

    }

    @Override
    public String actionName() {
        return ACTION_EXPOSE;
    }

    @SuppressLint("LongLogTag")
    @Override
    public boolean onAction(RealCaller caller) {
//        UIThreadUtil.runUIThread(() -> KradioAdAudioManager.getInstance().cancel());
        PlayItem playItem = caller.getParamValue(KEY_EXPOSE_AD_PLAYITEM);
        AdPlayChainIntercept.IPlayAdEndCallback iPlayAdEndCallback = caller.getParamValue(KEY_EXPOSE_AD_CALLBACK);
        AdvertPlayerImpl advertPlayer = KradioAdAudioManager.getInstance().getAdvertPlayerimpl();
        if (advertPlayer == null) {
            notifyError(iPlayAdEndCallback);
            return false;
        }
        KradioAdAudioManager.getInstance().getAdvertPlayerimpl().
                setPlayAdEndCallback(iPlayAdEndCallback);
        int adSceneId = -1;
        Long zoneId = 0L;

        if (playItem.getType() == PlayerConstants.RESOURCES_TYPE_ALBUM) {
            adSceneId = KradioAdSceneConstants.ALBUM_SCENE;
            zoneId = GetAdDataMap.getLongADSpaceBySceneID(adSceneId);
        } else if (playItem.getType() == PlayerConstants.RESOURCES_TYPE_RADIO) {
            RadioPlayItem radioPlayItem = (RadioPlayItem) playItem;
            if (radioPlayItem.getRadioInfoData().getRadioType() == 6) {
                adSceneId = KradioAdSceneConstants.BRAND_SCENE;
            } else {
                adSceneId = KradioAdSceneConstants.RADIO_SCENE;
            }
            zoneId = GetAdDataMap.getLongADSpaceBySceneID(adSceneId);
            Log.i(TAG, "曝光广告: 碎片类型: type = 电台 zoneId = " + zoneId);
            if (zoneId == null) {
                notifyError(iPlayAdEndCallback);
                return false;
            }

            if (zoneId == -1) {
                int type = radioPlayItem.getRadioInfoData().getAdZoneChooseType();
                if (type == RadioPlayListControl.TYPE_ZONE_CHOOSE_DETAILS) {
                    zoneId = (long) radioPlayItem.getRadioInfoData().getAdZoneId();
                } else {
                    zoneId = getSwitchRadioAudioZoneId(radioPlayItem);
                    if (zoneId != null && zoneId != -1) {
                        exposure(KradioAdSceneConstants.AD_TYPE_SWITCH_RADIO_AUDIO, String.valueOf(zoneId));
                        return false;
                    }
                }
            }
        } else {
            notifyError(iPlayAdEndCallback);
            return false;
        }

        Log.i(TAG, "曝光广告: adSceneId = " + adSceneId + ", zoneId = " + zoneId);

        if (zoneId == null || zoneId == -1) {
            notifyError(iPlayAdEndCallback);
            return false;
        }

        exposure(KradioAdSceneConstants.SUB_TYPE_SWITCH_PROGROM, String.valueOf(zoneId));
        return false;
    }

    /**
     * 获取编排位广告id
     *
     * @return
     */
    private Long getSwitchRadioAudioZoneId(RadioPlayItem radioPlayItem) {
        Long zoneId = null;
        String tempType = radioPlayItem.getMapCacheData(PlayItemConstants.ITEM_KEY_AD_ZONE_TYPE);
        if (StringUtil.isEmpty(tempType)) {
            return zoneId;
        }
        if (tempType.equals(String.valueOf(RadioPlayListControl.TYPE_ZONE_CHOOSE_AUDIO))) {
            String zoneIdStr = radioPlayItem.getMapCacheData(PlayItemConstants.ITEM_KEY_AD_ZONE_ID);
            if (StringUtil.isNotEmpty(zoneIdStr)) {
                try {
                    zoneId = Long.parseLong(zoneIdStr);
                } catch (Exception e) {

                }
            }
        }
        return zoneId;
    }


    private void notifyError(AdPlayChainIntercept.IPlayAdEndCallback iPlayAdEndCallback) {
        if (iPlayAdEndCallback != null) {
            iPlayAdEndCallback.playEnd();
        }
    }

    /**
     * 曝光广告
     *
     * @param zoneId
     */
    @SuppressLint("LongLogTag")
    private void exposure(int type, String zoneId) {
        Log.i(TAG, "开始曝光广告: id = " + zoneId);
        String acceptedAdTypes = "" + KradioAdSceneConstants.AD_TYPE_AUDIO + ","
                + KradioAdSceneConstants.AD_TYPE_IMAGE + ","
                + KradioAdSceneConstants.AD_TYPE_AUDIO_IMAGE + "";
        Log.i(TAG, "acceptedAdTypes:" + acceptedAdTypes);
        AdvertisingManager.getInstance().expose(zoneId,
                type,
                String.valueOf(ScreenUtil.getScreenWidth()),
                String.valueOf(ScreenUtil.getScreenHeight()),
                acceptedAdTypes, ""
        );
    }
}
