package com.kaolafm.kradio.categories.viewholder;

import android.text.TextUtils;
import android.util.Log;
import android.util.TypedValue;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.kradio.lib.bean.SubcategoryItemBean;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.flavor.KRadioPicSettingInter;
import com.kaolafm.kradio.lib.utils.DateUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.component.ui.base.utils.VipCornerUtil;
import com.kaolafm.kradio.component.ui.base.view.OvalImageView;


/**
 * <AUTHOR>
 **/
public class HorizontalSubscriptionViewHolder extends BaseSubcategoryViewHolder {

    private static final int WAN = 10000;
    private static final String DONGFENGRICHAN = "KRadioPicSettingImpl";

    OvalImageView ivCover;
    TextView tvName;
    TextView tvNumber;
    View layout_offline;
    View viewItemSubscriptionMongolian;
    ImageView vipIcon;
    View llFreq;
    TextView tvFreq;
    View rootView;

    private KRadioPicSettingInter mPicSetting;


    public HorizontalSubscriptionViewHolder(View itemView, KRadioPicSettingInter picSetting) {
        super(itemView);
        mPicSetting = picSetting;

        ivCover=itemView.findViewById(R.id.iv_cover);
        tvName=itemView.findViewById(R.id.tv_name);
        tvNumber=itemView.findViewById(R.id.tv_number);
        layout_offline=itemView.findViewById(R.id.layout_offline);
        viewItemSubscriptionMongolian=itemView.findViewById(R.id.view_item_subscription_mongolian);
        vipIcon=itemView.findViewById(R.id.vip_icon);
        llFreq=itemView.findViewById(R.id.llFreq);
        tvFreq=itemView.findViewById(R.id.tvFreq);

        rootView=itemView.findViewById(R.id.root_view);
    }

    @Override
    public void setupData(SubcategoryItemBean subcategoryItemBean, int position) {
        super.setupData(subcategoryItemBean, position);

        boolean hasFreq = false;
        if (subcategoryItemBean.getItemType() == SubcategoryItemBean.TYPE_ITEM_BROADCAST_LOCAL
                || subcategoryItemBean.getItemType() == SubcategoryItemBean.TYPE_ITEM_BROADCAST_CATEGORY
                || subcategoryItemBean.getItemType() == SubcategoryItemBean.TYPE_ITEM_TV) {
            String freq = subcategoryItemBean.getFreq();
            if (StringUtil.isEmpty(freq) && subcategoryItemBean.getName().contains("FM")) {
                freq = subcategoryItemBean.getName();
            }
            if (!StringUtil.isEmpty(freq)) {
                Log.i("HorizontalSubscription",freq);
                if (freq.startsWith("FM")) {
                    freq = freq.replace("FM", "");
                }
                if (freq.contains("/")) {
                    freq = freq.substring(0, freq.indexOf("/"));
                }
                if (freq.contains(",AM")) {
                    freq = freq.substring(0, freq.indexOf(",AM"));
                }
                if (freq.contains(" AM")) {
                    freq = freq.substring(0, freq.indexOf(" AM"));
                }
                if(!freq.isEmpty()){
                    tvFreq.setText(StringUtil.getMaxString(freq, 9));
                    hasFreq = true;
                }
            }
            llFreq.setVisibility(hasFreq ? View.VISIBLE : View.INVISIBLE);
        } else {
            llFreq.setVisibility(hasFreq ? View.VISIBLE : View.GONE);
        }
        String picUrl;
        picUrl = HolderUtils.getUrlString(subcategoryItemBean, mPicSetting);

        ImageLoader.getInstance().displayImage(mContext, picUrl, ivCover);

        String name = "";
        if (subcategoryItemBean.getItemType() == SubcategoryItemBean.TYPE_ITEM_BROADCAST_LOCAL
                || subcategoryItemBean.getItemType() == SubcategoryItemBean.TYPE_ITEM_TV) {
            if (TextUtils.isEmpty(subcategoryItemBean.getTitle())) {
                name = subcategoryItemBean.getName();
            } else {
                name = subcategoryItemBean.getTitle();
            }
        } else {
            name = subcategoryItemBean.getName();
        }
        tvName.setText(StringUtil.getMaxString(name, 5));
        rootView.setContentDescription(StringUtil.getMaxString(name, 5));
        //QQ
        long listenNum = subcategoryItemBean.getListenNum();
        if (listenNum > 0) {
            String strListenNum = toStr(listenNum);
            tvNumber.setText(strListenNum);
            ViewUtil.setViewVisibility(tvNumber, View.VISIBLE);
        } else {
            //听伴:听伴没有listenNum字段
            String updateTime = subcategoryItemBean.getUpdateTime();
            if (!TextUtils.isEmpty(updateTime)) {
                tvNumber.setText(DateUtil.getDisTimeStr(Long.valueOf(updateTime)));
                ViewUtil.setViewVisibility(tvNumber, View.VISIBLE);
            } else {
                ViewUtil.setViewVisibility(tvNumber, View.INVISIBLE);
            }
        }
        if (subcategoryItemBean.getItemType() == SubcategoryItemBean.TYPE_ITEM_BROADCAST_CATEGORY
                || subcategoryItemBean.getItemType() == SubcategoryItemBean.TYPE_ITEM_BROADCAST_LOCAL
                || subcategoryItemBean.getItemType() == SubcategoryItemBean.TYPE_ITEM_TV) {
            vipIcon.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_icon_live_class));
        } else {
            vipIcon.setImageResource(0);
            VipCornerUtil.setVipCorner(vipIcon, subcategoryItemBean.getVip(), subcategoryItemBean.getFine(), false);
            Log.d("cai66", "--------vip" + subcategoryItemBean.getVip() + "--精品--" + subcategoryItemBean.getFine());
//            if (subcategoryItemBean.isSelected()) {
//                tvName.setTextSize(TypedValue.COMPLEX_UNIT_PX, ResUtil.getDimen(R.dimen.m24));//AI电台，全部
//            } else {
//                tvName.setTextSize(TypedValue.COMPLEX_UNIT_PX, ResUtil.getDimen(R.dimen.m24));//AI电台，全部
//            }
        }

        //是否下线
        boolean online = subcategoryItemBean.isOnline();
        layout_offline.setVisibility(online ? View.GONE : View.VISIBLE);

//        tvName.setTextSize(TypedValue.COMPLEX_UNIT_PX, ResUtil.getDimen(R.dimen.subcategory_item_radio_text_size));
        //todo flavor代码迁移（yikatongkx11）
        tvName.setTextSize(TypedValue.COMPLEX_UNIT_PX, ResUtil.getDimen(R.dimen.text_size_title7));//AI电台，全部
        tvName.setSelected(subcategoryItemBean.isSelected());


        ViewUtil.setViewVisibilityAccordingToSetting(viewItemSubscriptionMongolian);

    }

    /**
     * 将收听量,转为文字
     *
     * @param listenNum
     * @return
     */
    private static String toStr(long listenNum) {
        StringBuilder str = new StringBuilder();

        long l = listenNum / (WAN * WAN);
        if (l > 0) {
            //上亿
            str.append(l).append("亿");
            listenNum = listenNum - l * WAN * WAN;
            l = listenNum / WAN;
            if (l > 0) {
                str.append(l).append("万");
            }
        } else {
            //上十万
            l = listenNum / WAN;
            if (l >= 10) {
                str.append(l).append("万");
            } else {
                str.append(listenNum);
            }
        }


        return str.toString();
    }
}
