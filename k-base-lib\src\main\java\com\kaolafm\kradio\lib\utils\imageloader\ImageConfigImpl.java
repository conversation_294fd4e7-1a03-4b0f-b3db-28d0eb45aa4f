/*
 * Copyright 2017 JessYan
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.kaolafm.kradio.lib.utils.imageloader;

import android.graphics.drawable.Drawable;
import androidx.annotation.IntDef;
import android.widget.ImageView;

import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.resource.bitmap.BitmapTransformation;
import com.bumptech.glide.request.target.Target;
import com.kaolafm.kradio.lib.utils.imageloader.listener.OnGetBitmapListener;
import com.kaolafm.kradio.lib.utils.imageloader.listener.OnGetDrawableListener;
import com.kaolafm.kradio.lib.utils.imageloader.listener.OnImageLoaderListener;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * ================================================
 * 这里存放图片请求的配置信息,可以一直扩展字段,如果外部调用时想让图片加载框架
 * 做一些操作,比如清除缓存或者切换缓存策略,则可以定义一个 int 类型的变量,内部根据 switch(int) 做不同的操作
 * 其他操作同理
 * <p>
 * ================================================
 */
public class ImageConfigImpl extends ImageConfig {

    @Retention(RetentionPolicy.SOURCE)
    @IntDef(flag = true, value = {TYPE_BITMAP, TYPE_DRAWABLE, TYPE_GIF})
    public @interface ImageType {
    }

    /**
     * bitmap图片类型。
     */
    public static final int TYPE_BITMAP = 0x00001;

    /**
     * drawable图片类型。
     */
    public static final int TYPE_DRAWABLE = 0x00002;

    /**
     * gif图片类型。
     */
    public  static final int TYPE_GIF = 0x00003;

    /**
     * 不确定的类型
     */
    public static final int TYPE_UNKNOWN = 0x00000;

    /**
     * 缓存策略，默认all
     * DiskCacheStrategy.all<br/>
     * DiskCacheStrategy.NONE<br/>
     * DiskCacheStrategy.SOURCE<br/>
     * DiskCacheStrategy.RESULT
     */
    private DiskCacheStrategy cacheStrategy;
    /**
     * 请求 url 为空,则使用此图片作为占位符
     */
    private Drawable fallback;
    /**
     * 图片每个圆角的大小
     */
    private int imageRadius;
    /**
     * 高斯模糊值, 值越大模糊效果越大
     */
    private int blurValue;

    /**
     * url是否是本地地址，true表示是本地地址
     */
    private boolean local;

    /**
     * 资源id
     */
    private final int resId;

    /**
     * glide用它来改变图形的形状
     *
     * @see {@link Builder#transformations(BitmapTransformation...)}
     */
    private BitmapTransformation[] transformations;
    private ImageView[] imageViews;
    /**
     * 是否使用淡入淡出过渡动画
     */
    private boolean isCrossFade;
    /**
     * 是否将图片剪切为 CenterCrop
     */
    private boolean isCenterCrop;
    /**
     * 是否将图片剪切为圆形
     */
    private boolean isCircle;
    /**
     * 清理内存缓存
     */
    private boolean isClearMemory;
    /**
     * 清理本地缓存
     */
    private boolean isClearDiskCache;
    /**
     * 图片大小，正方形
     */
    private int size;
    /**
     * 是否缓存到内存
     */
    private boolean cacheInMemory;
    /**
     * 获取Bitmap监听
     */
    private OnGetBitmapListener bitmapListener;
    /**
     * 获取drawable监听
     */
    private OnGetDrawableListener drawableListener;
    /**
     * 图片加载回调
     */
    private OnImageLoaderListener imageLoaderListener;

    /**
     * 不用imageview时必须设置 Target
     */
    private Target target;

    /**
     * 图片类型，Bitmap,Drawable, Gif。默认Drawable
     */
    private int imgType;


    private ImageConfigImpl(Builder builder) {
        this.url = builder.url;
        this.imageView = builder.imageView;
        this.placeHolderDrawale = builder.placeholder;
        this.errorPicDrawable = builder.errorPic;
        this.fallback = builder.fallback;
        this.cacheStrategy = builder.cacheStrategy;
        this.imageRadius = builder.imageRadius;
        this.blurValue = builder.blurValue;
        this.transformations = builder.transformations;
        this.imageViews = builder.imageViews;
        this.isCrossFade = builder.isCrossFade;
        this.isCenterCrop = builder.isCenterCrop;
        this.isCircle = builder.isCircle;
        this.isClearMemory = builder.isClearMemory;
        this.isClearDiskCache = builder.isClearDiskCache;
        this.resId = builder.resId;
        this.size = builder.size;
        this.cacheInMemory = builder.cacheInMemory;
        this.bitmapListener = builder.bitmapListener;
        this.drawableListener = builder.drawableListener;
        this.imageLoaderListener = builder.imageLoaderListener;
        this.target = builder.target;
        this.imgType = builder.imgType;
        this.local = builder.local;
    }

    public void setCacheStrategy(DiskCacheStrategy cacheStrategy) {
        this.cacheStrategy = cacheStrategy;
    }

    public DiskCacheStrategy getCacheStrategy() {
        return cacheStrategy;
    }

    public BitmapTransformation[] getTransformations() {
        return transformations;
    }

    public ImageView[] getImageViews() {
        return imageViews;
    }

    public boolean isClearMemory() {
        return isClearMemory;
    }

    public boolean isClearDiskCache() {
        return isClearDiskCache;
    }

    public Drawable getFallback() {
        return fallback;
    }

    public int getBlurValue() {
        return blurValue;
    }

    public boolean isBlurImage() {
        return blurValue > 0;
    }

    public int getImageRadius() {
        return imageRadius;
    }

    public boolean isImageRadius() {
        return imageRadius > 0;
    }

    public boolean isCrossFade() {
        return isCrossFade;
    }

    public boolean isCenterCrop() {
        return isCenterCrop;
    }

    public boolean isCircle() {
        return isCircle;
    }

    public int getResId() {
        return resId;
    }

    public int getSize() {
        return size;
    }

    public boolean isCacheInMemory() {
        return cacheInMemory;
    }

    public OnGetBitmapListener getOnBitmapListener() {
        return bitmapListener;
    }

    public OnGetDrawableListener getOnDrawableListener() {
        return drawableListener;
    }

    public OnImageLoaderListener getOnImageLoaderListener() {
        return imageLoaderListener;
    }

    public Target getTarget() {
        return target;
    }

    public int getImgType() {
        return imgType;
    }

    public boolean isLocal() {
        return local;
    }

    public static Builder builder() {
        return new Builder();
    }


    public static final class Builder {

        /**
         * 图片大小，正方形
         */
        public int size = 0;

        private boolean local;

        private String url;
        private ImageView imageView;
        private Drawable placeholder = null;
        private Drawable errorPic = null;
        /**
         * 请求 url 为空,则使用此图片作为占位符
         */
        private Drawable fallback;
        /**
         * 缓存策略，默认all
         * DiskCacheStrategy.all<br/>
         * DiskCacheStrategy.NONE<br/>
         * DiskCacheStrategy.SOURCE<br/>
         * DiskCacheStrategy.RESULT
         */
        private DiskCacheStrategy cacheStrategy = DiskCacheStrategy.ALL;
        /**
         * 图片每个圆角的大小,只要大于零就会设置圆角
         */
        private int imageRadius = 0;
        /**
         * 高斯模糊值, 值越大模糊效果越大
         */
        private int blurValue = 0;
        /**
         * glide用它来改变图形的形状
         *
         * @see {@link Builder#transformations(BitmapTransformation...)}
         */
        private BitmapTransformation[] transformations;
        private ImageView[] imageViews;
        /**
         * 是否使用淡入淡出过渡动画
         */
        private boolean isCrossFade;
        /**
         * 是否将图片剪切为 CenterCrop
         */
        private boolean isCenterCrop = true;
        /**
         * 是否将图片剪切为圆形
         */
        private boolean isCircle;
        /**
         * 清理内存缓存
         */
        private boolean isClearMemory;
        /**
         * 清理本地缓存
         */
        private boolean isClearDiskCache;
        /**
         * 资源id
         */
        private int resId = 0;
        /**
         * 是否缓存到内存
         */
        private boolean cacheInMemory = true;

        /**
         * 获取Bitmap监听
         */
        private OnGetBitmapListener bitmapListener;
        /**
         * 获取drawable监听
         */
        private OnGetDrawableListener drawableListener;
        /**
         * 图片加载回调
         */
        private OnImageLoaderListener imageLoaderListener;
        /**
         * 不用imageview时必须设置 Target
         */
        private Target target;
        /**
         * 图片类型，Bitmap,Drawable, Gif。默认Drawable
         */
        private int imgType = ImageConfigImpl.TYPE_DRAWABLE;

        private Builder() {
        }

        public Builder url(String url) {
            this.url = url;
            return this;
        }

        public Builder placeholder(Drawable placeholder) {
            this.placeholder = placeholder;
            return this;
        }

        public Builder errorPic(Drawable errorPic) {
            this.errorPic = errorPic;
            return this;
        }

        public Builder fallback(Drawable fallback) {
            this.fallback = fallback;
            return this;
        }

        public Builder imageView(ImageView imageView) {
            this.imageView = imageView;
            return this;
        }

        public Builder cacheStrategy(DiskCacheStrategy cacheStrategy) {
            this.cacheStrategy = cacheStrategy;
            return this;
        }

        /**
         * 设置圆角值
         *
         * @param imageRadius 只要圆角值大于0就会自动设置圆角
         * @return
         */
        public Builder imageRadius(int imageRadius) {
            this.imageRadius = imageRadius;
            return this;
        }

        /**
         * 设置高斯模糊值，建议设置为 15
         *
         * @param blurValue 只要该值大于0 就自动设置高斯模糊
         * @return
         */
        public Builder blurValue(int blurValue) {
            this.blurValue = blurValue;
            return this;
        }

        /**
         * 给图片添加 Glide 独有的 BitmapTransformation
         * <p>
         * 因为 BitmapTransformation 是 Glide 独有的类,
         * 如果不是使用glide，请使用 {@link #isCircle()}, {@link #isCenterCrop()}, {@link #isImageRadius()} 替代，并自己实现
         *
         * @param transformations {@link BitmapTransformation}
         */
        public Builder transformations(BitmapTransformation... transformations) {
            this.transformations = transformations;
            return this;
        }

        public Builder imageViews(ImageView... imageViews) {
            this.imageViews = imageViews;
            return this;
        }

        public Builder isCrossFade(boolean isCrossFade) {
            this.isCrossFade = isCrossFade;
            return this;
        }

        public Builder isCenterCrop(boolean isCenterCrop) {
            this.isCenterCrop = isCenterCrop;
            return this;
        }

        public Builder isCircle(boolean isCircle) {
            this.isCircle = isCircle;
            return this;
        }

        public Builder isClearMemory(boolean isClearMemory) {
            this.isClearMemory = isClearMemory;
            return this;
        }

        public Builder isClearDiskCache(boolean isClearDiskCache) {
            this.isClearDiskCache = isClearDiskCache;
            return this;
        }

        public Builder resId(int resId) {
            this.resId = resId;
            return this;
        }

        public Builder onGetBitmapListener(OnGetBitmapListener bitmapListener) {
            this.bitmapListener = bitmapListener;
            return this;
        }

        public Builder onGetDrawableListener(OnGetDrawableListener drawableListener) {
            this.drawableListener = drawableListener;
            return this;
        }

        public Builder onImageLoaderListener(OnImageLoaderListener imageLoaderListener) {
            this.imageLoaderListener = imageLoaderListener;
            return this;
        }

        public Builder size(int size) {
            this.size = size;
            return this;
        }

        public Builder cacheInMemory(boolean cacheInMemory) {
            this.cacheInMemory = cacheInMemory;
            return this;
        }

        /**
         * 不使用imageview时，必须设置Target
         *
         * @param target
         * @return
         */
        public <Y extends Target> Builder target(Y target) {
            this.target = target;
            return this;
        }

        public Builder imgType(@ImageType int imgType) {
            this.imgType = imgType;
            return this;
        }

        /**
         * url是否是本地图片地址
         * @param local
         * @return
         */
        public Builder isLocal(boolean local) {
            this.local = local;
            return this;
        }


        public ImageConfigImpl build() {
            return new ImageConfigImpl(this);
        }
    }
}
