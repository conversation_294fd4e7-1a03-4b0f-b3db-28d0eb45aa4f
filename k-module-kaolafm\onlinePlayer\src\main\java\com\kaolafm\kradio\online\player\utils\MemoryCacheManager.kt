package com.kaolafm.kradio.online.player.utils

import androidx.core.util.LruCache
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException

class MemoryCacheManager private constructor() {
    private var mMediaFileBitmapMemoryCache: LruCache<String, List<*>>

    // 获取该应用虚拟机允许的最大内存，超过该内存量，会OOM。用kb单位保存。
    val maxMemory = (Runtime.getRuntime().maxMemory() / 1024).toInt()

    // 获取最大内存的1/8.
    val cacheSize = maxMemory / 8

    companion object {
        private var instance: MemoryCacheManager? = null
        fun newInstance(): MemoryCacheManager {
            if (instance == null) {
                synchronized(MemoryCacheManager::class.java) {
                    if (instance == null) {
                        instance = MemoryCacheManager()
                    }
                }
            }
            return instance!!
        }
    }

    init {
        mMediaFileBitmapMemoryCache = object : LruCache<String, List<*>>(cacheSize) {

        }
    }

    fun addListDataToMemoryCache(key: String, listData: List<*>) {
        if (getListDataFromMemCache(key) == null) {
            mMediaFileBitmapMemoryCache.put(hashKeyForDisk(key), listData)
        }
    }

    fun getListDataFromMemCache(key: String): List<*> {
        return mMediaFileBitmapMemoryCache.get(hashKeyForDisk(key))
    }

    fun clearAllFromMemCache() {
        mMediaFileBitmapMemoryCache.evictAll()
    }

    /**
     * String MD5加密
     *
     * @param key
     * @return
     */
    private fun hashKeyForDisk(key: String): String {
        val cacheKey: String
        cacheKey = try {
            val mDigest = MessageDigest.getInstance("MD5")
            mDigest.update(key.toByteArray())
            bytesToHexString(mDigest.digest())
        } catch (e: NoSuchAlgorithmException) {
            key.hashCode().toString()
        }
        return cacheKey.toLowerCase()
    }

    private fun bytesToHexString(var0: ByteArray): String {
        val var1 = StringBuffer(var0.size)
        for (var3 in var0.indices) {
            val var2 = Integer.toHexString(255 and var0[var3].toInt())
            if (var2.length < 2) {
                var1.append(0)
            }
            var1.append(var2.toUpperCase())
        }
        return var1.toString()
    }
}