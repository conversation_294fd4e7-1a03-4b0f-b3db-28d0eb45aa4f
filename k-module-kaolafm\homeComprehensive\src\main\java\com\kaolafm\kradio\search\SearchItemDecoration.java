package com.kaolafm.kradio.search;

import android.graphics.Rect;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;
import android.view.View;

public class SearchItemDecoration extends RecyclerView.ItemDecoration {

    private int mLeft, mRight, mTop, mBottom;
    private boolean firstRowShowDecoration, lastRowShowDecoration, firstColumnShowDecoration, lastColumnShowDecoration;
    private static final int UNSPORTED = -1;

    public SearchItemDecoration(int left, int right, int top, int bottom) {
        this(left, right, top, bottom, true, true, true, true);
    }

    public SearchItemDecoration(int left, int right, int top, int bottom, boolean firstRowShowDecoration, boolean lastRowShowDecoration, boolean firstColumnShowDecoration, boolean lastColumnShowDecoration) {
        mLeft = left;
        mRight = right;
        mTop = top;
        mBottom = bottom;
        this.firstRowShowDecoration = firstRowShowDecoration;
        this.lastRowShowDecoration = lastRowShowDecoration;
        this.firstColumnShowDecoration = firstColumnShowDecoration;
        this.lastColumnShowDecoration = lastColumnShowDecoration;
    }

    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {


        outRect.left = mLeft;
        outRect.right = mRight;
        outRect.top = mTop;
        outRect.bottom = mBottom;

        final int spanCount = getSpanCount(parent);
        if (spanCount == UNSPORTED) return;
        final int childCount = parent.getAdapter().getItemCount();
        final int adapterPosition = parent.getChildAdapterPosition(view);

        if (isFirstRow(adapterPosition, spanCount, childCount) && !firstRowShowDecoration) {
            outRect.top = 0;
        }
        if (isLastRow(adapterPosition, spanCount, childCount) && !lastRowShowDecoration) {
            outRect.bottom = 0;
        }
        if (isFirstColumn(adapterPosition, spanCount, childCount) && !firstColumnShowDecoration) {
            outRect.left = 0;
        }
        if (isLastColumn(adapterPosition, spanCount, childCount) && !lastColumnShowDecoration) {
            outRect.right = 0;
        }
    }

    private int getSpanCount(RecyclerView parent) {
        RecyclerView.LayoutManager layoutManager = parent.getLayoutManager();

        if (layoutManager instanceof GridLayoutManager) {
            return ((GridLayoutManager) layoutManager).getSpanCount();
        } else if (layoutManager instanceof StaggeredGridLayoutManager) {
            return ((StaggeredGridLayoutManager) layoutManager).getSpanCount();
        } else if (layoutManager instanceof LinearLayoutManager) {
            return 1;
        } else {
            return UNSPORTED;
        }
    }

    private boolean isFirstRow(int position, int spanCount, int childCount) {
        return position < spanCount;
    }

    private boolean isLastRow(int position, int spanCount, int childCount) {
        return position >= childCount - spanCount;
    }

    private boolean isFirstColumn(int position, int spanCount, int childCount) {
        return position % spanCount == 0;
    }

    private boolean isLastColumn(int position, int spanCount, int childCount) {
        return (position + 1) % spanCount == 0;
    }

}
