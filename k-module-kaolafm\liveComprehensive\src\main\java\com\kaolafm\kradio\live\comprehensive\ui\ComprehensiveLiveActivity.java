package com.kaolafm.kradio.live.comprehensive.ui;

import static com.kaolafm.kradio.live.player.NimManager.LIVE_CUSTOM_MSG_RANK_TYPE;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Color;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.TextUtils;
import android.util.Log;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewStub;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.DecelerateInterpolator;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.google.gson.Gson;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.common.base.BaseSkinAppCompatActivity;
import com.kaolafm.kradio.common.comprehensive.event.ChangeBadgeViewEvent;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.kradio.common.utils.CommonUtils;
import com.kaolafm.kradio.common.utils.PermissionUtils;
import com.kaolafm.kradio.component.ui.base.view.KradioTextView;
import com.kaolafm.kradio.k_kaolafm.BuildConfig;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.flavor.KRadioFragmentTransStatusBarAdapterInter;
import com.kaolafm.kradio.lib.basedb.GreenDaoInterface;
import com.kaolafm.kradio.lib.basedb.manager.MessageDaoManager;
import com.kaolafm.kradio.lib.bean.CrashMessageBean;
import com.kaolafm.kradio.lib.common.ResType;
import com.kaolafm.kradio.lib.report.ReportParamUtil;
import com.kaolafm.kradio.lib.report.ReportUtil;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkManager;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.TimerUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.lib.utils.imageloader.listener.OnImageLoaderListener;
import com.kaolafm.kradio.live.comprehensive.gift.GiftMsgControl;
import com.kaolafm.kradio.live.comprehensive.gift.GiftPannelDialog;
import com.kaolafm.kradio.live.comprehensive.gift.ui.CustormAnim;
import com.kaolafm.kradio.live.comprehensive.goods.GoodsCardControl;
import com.kaolafm.kradio.live.comprehensive.goods.GoodsPanelControl;
import com.kaolafm.kradio.live.comprehensive.goods.GoodsPannelDialog;
import com.kaolafm.kradio.live.comprehensive.goods.ui.GoodsCardLayout;
import com.kaolafm.kradio.live.comprehensive.record.RecordPannelDialog;
import com.kaolafm.kradio.live.comprehensive.theme.ThemeDialog;
import com.kaolafm.kradio.live.comprehensive.ui.adapters.LiveAbilityAdapter;
import com.kaolafm.kradio.live.comprehensive.ui.adapters.LiveMessageAdapter;
import com.kaolafm.kradio.live.comprehensive.ui.adapters.LowSpeedLayoutManager;
import com.kaolafm.kradio.live.comprehensive.ui.adapters.VerticalSpaceDividerDecoration;
import com.kaolafm.kradio.live.model.BusEventPlayingProgramIdChanged;
import com.kaolafm.kradio.live.mvp.HomeLivePresenter;
import com.kaolafm.kradio.live.mvp.HomeLiveView;
import com.kaolafm.kradio.live.mvp.LivePresenter;
import com.kaolafm.kradio.live.player.ErrorStatus;
import com.kaolafm.kradio.live.player.HomeLiveManager;
import com.kaolafm.kradio.live.player.LiveStatus;
import com.kaolafm.kradio.live.player.NimManager;
import com.kaolafm.kradio.live.player.RecordUploadHelper;
import com.kaolafm.kradio.live.player.RecorderStatus;
import com.kaolafm.kradio.live.utils.LiveBeanTransUtils;
import com.kaolafm.kradio.live.utils.LiveUtil;
import com.kaolafm.kradio.player.event.PlayerChangedEBData;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.purchase.PayManager;
import com.kaolafm.kradio.purchase.constant.PayConst;
import com.kaolafm.kradio.user.LoginManager;
import com.kaolafm.kradio.user.UserInfoManager;
import com.kaolafm.opensdk.api.goods.model.Goods;
import com.kaolafm.opensdk.api.goods.model.GoodsResult;
import com.kaolafm.opensdk.api.live.model.ChatUserInfo;
import com.kaolafm.opensdk.api.live.model.Gift;
import com.kaolafm.opensdk.api.live.model.GiftGivingResult;
import com.kaolafm.opensdk.api.live.model.GiftsResult;
import com.kaolafm.opensdk.api.live.model.LiveAbility;
import com.kaolafm.opensdk.api.live.model.LiveInfoDetail;
import com.kaolafm.opensdk.api.live.model.LiveRoomMessage;
import com.kaolafm.opensdk.api.live.model.MessageBean;
import com.kaolafm.opensdk.api.yunxin.model.GiftMsg;
import com.kaolafm.opensdk.api.yunxin.model.GiftRankMsg;
import com.kaolafm.opensdk.api.yunxin.model.GiftRankUser;
import com.kaolafm.opensdk.api.yunxin.model.GoodsCardMsg;
import com.kaolafm.opensdk.api.yunxin.model.LiveCommentStatusMsg;
import com.kaolafm.opensdk.api.yunxin.model.LiveStatusMsg;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.LivePlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.event.LoginReportEvent;
import com.kaolafm.report.event.PlayerUiControlReportEvent;
import com.kaolafm.report.util.ReportConstants;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

import skin.support.content.res.SkinCompatResources;

/**
 * 直播间页面
 */
@Route(path = RouterConstance.PLAY_LIVE_COMPREHENSIVE_URL)
public class ComprehensiveLiveActivity extends BaseSkinAppCompatActivity<HomeLivePresenter> implements HomeLiveView, NetworkManager.INetworkReady, LoginManager.LoginListener {

    private static final String TAG = ComprehensiveLiveActivity.class.getSimpleName();

    /**
     * 测试按钮开关，记得关闭
     */
    private static final boolean SWITCH_TEST_BUTTON = false;

    private static final int PLAY_ERROR_TOLERANCE_TIME = 60 * 1000;


    private CountDownTimer mForecastTimer;

    ImageView liveBg;
    View loading;
    ImageView backView;
    KradioTextView liveTitle;
    KradioTextView liveAnchor;
    LinearLayout liveTheme;
    KradioTextView liveCu;
    LinearLayout liveCuRankLayout;
    View liveCuRankDivider;
    LinearLayout liveRank;
    RelativeLayout liveRank1Bg;
    RelativeLayout liveRank2Bg;
    RelativeLayout liveRank3Bg;
    ImageView liveRank1;
    ImageView liveRank2;
    ImageView liveRank3;
    RecyclerView liveBottomAbility;
    FrameLayout mScreenBulletContainer;
    ConstraintLayout mLiveNotStartLayout;
    KradioTextView mForecastTextHour;
    KradioTextView mForecastTextMinute;
    KradioTextView mForecastTextSecond;
    ConstraintLayout mLiveFinishLayout;
    KradioTextView mLiveFinishNumText;
    TextView mLiveFinishNumTenThoundText;
    ConstraintLayout mErrorLayout;
    ConstraintLayout mComingLayout;
    RecyclerView recyclerView;
    ConstraintLayout liveMessageItemHello;
    TextView liveMessageItemHelloName;
    View liveBarrageRootView;
    ConstraintLayout livePendingLayout;
    ViewStub mErrorPage;
    TextView noticeTip;
    RelativeLayout mNetLayout;

    Animation mBottomInAnimation, mBottomOutAnimation;


    private boolean isFront = false;
    private boolean gotoLogin = false;  //是否打开过登录页面

    private TextView mLastMessageText;
    private boolean isLoadingHistory = false;  //是否正在加载历史数据

    private long mProgramId; //节目id


    private String mForecastString;

    private RecyclerView.OnScrollListener mScrollListener;
    private AtomicBoolean isFirstLoadHistory = new AtomicBoolean(true); //第一次加载
    private AtomicBoolean checkLiveInfoByUser = new AtomicBoolean(false);   //用户点击“立即收听”按钮查询状态

//    private KRadioSpeakImageInter mKRadioSpeakImageInter;

    private KRadioFragmentTransStatusBarAdapterInter mKRadioFragmentTransStatusBarAdapterInter;
//    private UserLoginComponent mUserLoginComponent;

    private boolean bPlayerEnabled;
    private PlayItem mPlayItem;

    private BasePlayStateListener mPlayerStateListener;

    private LinkedList<ChatUserInfo> mMemberEnterQueue = new LinkedList<>();
    private LinkedList<MessageBean> mMessageReceiveQueue = new LinkedList<>();

    //在线成员列表，仅保存用于展示头像的几个成员
    private List<ChatUserInfo> mOnlineMembers = new ArrayList<>();

    private boolean bDoMemberEnterAnim;
    private boolean bDoMessageReceiveAnim;

    //系统消息列表，临时将系统消息放置到列表中展示
    private List<LiveRoomMessage> mMessages = new ArrayList() {{
        MessageBean messageBean = new MessageBean();
        messageBean.contentString = ResUtil.getString(R.string.live_system_tip);
        messageBean.chatTime = String.valueOf(System.currentTimeMillis());
        add(new LiveRoomMessage(LiveRoomMessage.MessageType.MESSAGE_SYSTEM, null, messageBean));
    }};
    private LiveMessageAdapter mMessageAdapter;
    private int mineHeaderViewPosition = -1;    //未登录时自己的头像在头像父级View中的位置
    private int listenerCount = 1;//当前收听总人数，会被定时更新
    private List<GiftRankUser> rankUserList = new ArrayList<>();//当前收听总人数，会被定时更新
    private boolean isShow = false;

    private ImageView messageBadgeView;
    private RelativeLayout bottomGift, bottomRecord, bottomGoods;
    private GiftPannelDialog mGiftPannelDialog;
    private GoodsPannelDialog mGoodsPannelDialog = new GoodsPannelDialog();
    private RecordPannelDialog mRecordPannelDialog;
    private GoodsCardControl goodsCardControl;
    private GiftMsgControl giftMsgControl;
    private LinearLayout shopCardLayout;
    private String liveThemeContent;
    private GoodsResult mGoodsResult;
    private GiftsResult mGiftsResult;
    private boolean currentStart;
    private LiveAbilityAdapter mLiveAbilityAdapter;
    private List<LiveAbility> mLiveAbilityList = new ArrayList<>();
    private Integer isShowOnlineNum = 0;
    private LiveInfoDetail mLiveInfoDetail;
    private boolean isFirstEnter = true;
    private String liveBgUrl = "";
    private PermissionUtils permissionUtils;
    @Override
    public int getLayoutId() {
        return R.layout.comprehensive_live_ui_1920_720;
    }

    @Override
    public int getLayoutId_Tow() {
        return R.layout.comprehensive_live_ui_1280_720;
    }

    @Override
    protected boolean isReportPage() {
        return true;
    }

    @Override
    public void initView(Bundle savedInstanceState) {

        liveBg = findViewById(R.id.live_bg);
        loading = findViewById(R.id.loading);
        backView = findViewById(R.id.back);
        liveTitle = findViewById(R.id.live_title);
        liveAnchor = findViewById(R.id.live_anchor);
        liveTheme = findViewById(R.id.live_theme);
        liveCu = findViewById(R.id.live_cu);
        liveCuRankLayout = findViewById(R.id.live_cu_rank);
        liveCuRankDivider = findViewById(R.id.live_cu_rank_divider);
        liveRank = findViewById(R.id.live_rank);
        liveRank1Bg = findViewById(R.id.live_rank_1_bg);
        liveRank2Bg = findViewById(R.id.live_rank_2_bg);
        liveRank3Bg = findViewById(R.id.live_rank_3_bg);
        liveRank1 = findViewById(R.id.live_rank_1);
        liveRank2 = findViewById(R.id.live_rank_2);
        liveRank3 = findViewById(R.id.live_rank_3);
        liveBottomAbility = findViewById(R.id.live_bottom_ability);
        mScreenBulletContainer = findViewById(R.id.live_screen_bullet_layout);
        mLiveNotStartLayout = findViewById(R.id.live_not_start_layout);
        mForecastTextHour = findViewById(R.id.live_not_start_time_hour);
        mForecastTextMinute = findViewById(R.id.live_not_start_time_minute);
        mForecastTextSecond = findViewById(R.id.live_not_start_time_second);
        mLiveFinishLayout = findViewById(R.id.live_finish_layout);
        mLiveFinishNumText = findViewById(R.id.live_finish_num);
        mLiveFinishNumTenThoundText = findViewById(R.id.live_finish_num_ten_thousand);
        mErrorLayout = findViewById(R.id.live_error_layout);
        mComingLayout = findViewById(R.id.live_coming_layout);
        recyclerView = findViewById(R.id.recyclerView);
        liveMessageItemHello = findViewById(R.id.live_message_item_hello);
        liveMessageItemHelloName = findViewById(R.id.nameTv);
        liveBarrageRootView = findViewById(R.id.liveBarrageRootView);
        livePendingLayout = findViewById(R.id.live_pending_layout);
        mErrorPage = findViewById(R.id.live_no_network);
        noticeTip = findViewById(R.id.noticeTip);

        NetworkManager.getInstance().addNetworkReadyListener(this);
        LoginManager.getInstance().registerLoginListener(this);

        Bundle bundle = getIntent().getExtras();
        mProgramId = bundle.getLong("liveId");
//        mProgramId = 1588936310;
        ImageLoader.getInstance().displayImage(ComprehensiveLiveActivity.this, R.drawable.comprehensive_live_bg, liveBg);
        backView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        liveTheme.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ThemeDialog.getInstance(getPageId()).setContent(liveThemeContent).show(getSupportFragmentManager(), "dialog_fragment");
                PlayItem curPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
                String radioId = null, audioId = null;
                if (curPlayItem != null) {
                    radioId = curPlayItem.getRadioId();
                    audioId = String.valueOf(curPlayItem.getAudioId());
                }
                ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_LIVE_ROOM_NOTICE, noticeTip.getText().toString(), getPageId(), ReportConstants.CONTROL_TYPE_SCREEN, null, radioId, audioId, audioId, null));
            }
        });
        LinearLayoutManager layout = new LowSpeedLayoutManager(this, LinearLayoutManager.VERTICAL, false);
        layout.setStackFromEnd(true);
        recyclerView.setLayoutManager(layout);
        mMessageAdapter = new LiveMessageAdapter(mMessages);
        recyclerView.setAdapter(mMessageAdapter);

        // 关闭 RecyclerView 的 item 动画
        RecyclerView.ItemAnimator itemAnimator = recyclerView.getItemAnimator();
        if (itemAnimator instanceof DefaultItemAnimator) {
            ((DefaultItemAnimator) itemAnimator).setSupportsChangeAnimations(false);
        }
        recyclerView.addItemDecoration(new VerticalSpaceDividerDecoration(recyclerView.getContext(), ResUtil.getDimen(R.dimen.y20)));
        initViewInner();

//        initHello();
        initBottom();
        initGift();


        reportEvents();
        permissionUtils = new PermissionUtils(this);
    }

    private void reportEvents() {
        PlayItem curPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
        String radioId = null, audioId = null;
        if (curPlayItem != null) {
            radioId = curPlayItem.getRadioId();
            audioId = String.valueOf(curPlayItem.getAudioId());
        }
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_LIVE_ROOM_NOTICE, noticeTip.getText().toString(), getPageId(), ReportConstants.CONTROL_TYPE_SCREEN, null, radioId, audioId, audioId, null));
    }

    @Override
    public void initData() {

    }

    @Override
    protected HomeLivePresenter createPresenter() {
        mPresenter = new HomeLivePresenter(this);
        HomeLiveManager.getInstance().setPresenter(mPresenter);
        return mPresenter;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
//        Log.e(TAG, "chuan===UserInfoManager===" + UserInfoManager.getInstance().getUserInfo());
//        Log.e(TAG, "chuan===UserInfoManager===" + UserInfoManager.getInstance().getUserNickName());
//        Log.e(TAG, "chuan===UserInfoManager===" + UserInfoManager.getInstance().getUserArea());
//        Log.e(TAG, "chuan===UserInfoManager===" + UserInfoManager.getInstance().getOpenId());
//        Log.e(TAG, "chuan===UserInfoManager===" + UserInfoManager.getInstance().getUserId());
//        Log.e(TAG, "chuan===UserInfoManager===" + UserInfoManager.getInstance().getToken());
        CommonUtils.getInstance().initGreyStyle(getWindow());
        // Log.i("111222333444", "1");
        mForecastString = getString(R.string.live_hour_minute_second);
        mKRadioFragmentTransStatusBarAdapterInter = ClazzImplUtil.getInter("KRadioFragmentTransStatusBarAdapterImpl");

//        mUserLoginComponent = new UserLoginComponent();
//        ComponentUtil.addObserver(UserComponentConst.NAME, mUserLoginComponent);

        if (mPlayerStateListener == null) mPlayerStateListener = new BasePlayStateListener() {
            @Override
            public void onPlayerFailed(PlayItem playItem, int i1, int i) {
                showErrorStatus(ErrorStatus.ERROR_PLAY);
            }
        };
        PlayerManager.getInstance().addPlayControlStateCallback(mPlayerStateListener);
        // 本地调试用, 添加 [生成测试消息] 与 [删除测试消息] 按钮
        generateTestButton();
    }

    @Override
    public void onResume() {
        super.onResume();
        isFront = true;
        changeBadgeView(null);

        TimerUtil.newInstance().timer(250, num -> {
            //36568 【monkey】FATAL EXCEPTION: main，io.reactivex.exceptions.OnErrorNotImplementedException
            if (mPresenter != null) {
                // Log.i("111222333444", "2-1");
                initHomeLiveManager();
            }
        });
    }

    @Override
    public void onPause() {
        isFront = false;
        isShow = false;
        super.onPause();
    }

    /**
     * 用户每次进入直播间，需要弹出固定欢迎语，展示形式：欢迎 xxx（用户名）进入直播间，
     * 用户名需突出展示，若用户未登录账号，则不展示欢迎语；
     * -欢迎语在留言区最底部弹出即可（在留言区上层弹出），展示2s自动单条消失,欢迎语不进入留言信息流
     */
    private void initHello() {
        if (LoginManager.getInstance().isLogin()) {
            liveMessageItemHelloName.setText(UserInfoManager.getInstance().getUserInfo().getNickName());
            liveMessageItemHello.setVisibility(View.VISIBLE);
            liveMessageItemHello.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (liveMessageItemHello != null) {
                        liveMessageItemHello.setVisibility(View.GONE);
                    }
                }
            }, 2000);

        } else {
            liveMessageItemHello.setVisibility(View.GONE);
        }
    }

    private void initBottom() {
        mBottomInAnimation = AnimationUtils.loadAnimation(this, R.anim.comprehensive_bottom_btn_in_anim);
        mBottomOutAnimation = AnimationUtils.loadAnimation(this, R.anim.comprehensive_bottom_btn_out_anim);

        liveBottomAbility.setLayoutManager(new LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false));
        mLiveAbilityAdapter = new LiveAbilityAdapter(mLiveAbilityList);
        mLiveAbilityAdapter.setOnAbilityStatListener(new LiveAbilityAdapter.OnAbilityStatListener() {
            @Override
            public void onAbilityStart(View view, Integer type) {
                if (AntiShake.check(view.getId())) return;
                String buttonid = null;
                if (type == LiveAbility.LIVE_ABILITY_SEND_MESSAGE) {
                    buttonid = ButtonExposureOrClickReportEvent.BUTTON_ID_LIVE_ROOM_START_RECORD_MESSAGE;
                    openBottomRecord(view);
//                    GoodsCardMsg goodsCardMsg = new GoodsCardMsg();
//                    goodsCardMsg.setId(1l);
//                    goodsCardMsg.setPushType(0);
//                    EventBus.getDefault().post(goodsCardMsg);
                } else if (type == LiveAbility.LIVE_ABILITY_REWARD) {
                    buttonid = ButtonExposureOrClickReportEvent.BUTTON_ID_LIVE_ROOM_GIFT_SPAN_CONTROLLER;
                    openBottomGift(view);
//                    String s = "abcdefghijklmnopqrstuvwxyz";
//                    GoodsCardMsg goodsCardMsg = new GoodsCardMsg();
//                    goodsCardMsg.setId(1l);
//                    goodsCardMsg.setName(s.charAt((int)(Math.random() * 26))+"");
//                    goodsCardMsg.setMarketPrice((long)(Math.random() * 1000));
//                    goodsCardMsg.setPicUrl("http://up.deskcity.org/pic_source/2f/f4/42/2ff442798331f6cc6005098766304e39.jpg");
//                    goodsCardMsg.setPushType(1);
//                    goodsCardMsg.setShelf(1);
//                    goodsCardMsg.setStock(15);
//                    EventBus.getDefault().post(goodsCardMsg);

                } else if (type == LiveAbility.LIVE_ABILITY_SHOPPING) {
                    buttonid = ButtonExposureOrClickReportEvent.BUTTON_ID_LIVE_ROOM_SHOPPING_CART_SPAN_CONTROLLER;
                    openBottomGoods(view);
                }

                PlayItem curPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
                String radioId = null, audioId = null;
                if (curPlayItem != null) {
                    radioId = curPlayItem.getRadioId();
                    audioId = String.valueOf(curPlayItem.getAudioId());
                }
                ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, buttonid, null, getPageId(), PlayerUiControlReportEvent.CONTROL_TYPE_CLICK, null, radioId, audioId, audioId, null));
            }

            @Override
            public void onGoodsInit(View view) {
                initGoods(view);
            }
        });
        liveBottomAbility.setAdapter(mLiveAbilityAdapter);
//        liveBottomAbility.addItemDecoration(new LiveAbilityItemDecoration());

        messageBadgeView = findViewById(R.id.message_badge_view);
        messageBadgeView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                RouterManager.getInstance().jumpPage(RouterConstance.MESSAGE_COMPREHENSIVE_URL);
            }
        });
    }

    // 加载礼物相关UI
    private void initGift() {

        final LinearLayout giftParent = (LinearLayout) findViewById(R.id.live_gift_parent);
        giftMsgControl = new GiftMsgControl(this);
        giftMsgControl.setGiftLayout(giftParent, 3).setHideMode(false).setCustormAnim(new CustormAnim());//这里可以自定义礼物动画
        giftMsgControl.setDisplayMode(GiftMsgControl.FROM_BOTTOM_TO_TOP);
    }

    // 加载商品相关UI
    private void initGoods(View view) {
        shopCardLayout = (LinearLayout) findViewById(R.id.live_shop_card);
        goodsCardControl = new GoodsCardControl(ComprehensiveLiveActivity.this, shopCardLayout, view, getPageId());
        goodsCardControl.setOnShopPurchaseListener(new GoodsCardLayout.OnShopPurchaseListener() {
            @Override
            public void onPurchase(GoodsCardMsg goodsCardMsg, int position) {
                reportContentShowClickEvent(goodsCardMsg, position, true);
                if (LoginManager.getInstance().checkLogin()) {
                    PayManager.getInstance().pay(PayConst.PAY_TYPE_GOODS, goodsCardMsg.getId());
                }
            }
        });
    }

    private void reportContentShowClickEvent(GoodsCardMsg goodsCardMsg, int i, boolean b) {
        ReportUtil.addComponentShowAndClickEvent(goodsCardMsg.getId() + "", b, "20", 2, "", i + "", 0 + "", goodsCardMsg.getId() + "", "无", getPageId(), "无");
    }

    /**
     * 调用打赏礼物功能
     *
     * @param view
     */
    private void openBottomGift(View view) {
        mPresenter.getGifts((int) mProgramId);
        view.startAnimation(mBottomInAnimation);
        mGiftPannelDialog = GiftPannelDialog.getInstance(mGiftsResult, getPageId());
        mGiftPannelDialog.setOnGivingGiftListener(new GiftPannelDialog.OnGivingGiftListener() {
            @Override
            public void onGiving(Gift gift) {
                if (LoginManager.getInstance().checkLogin()) {
                    if (gift == null || gift.getGiftCost() == null) {
                        ToastUtil.showInfo(ComprehensiveLiveActivity.this, R.string.live_gift_cost_error_tip);
                        return;
                    }
                    if (gift.getGiftCost() == 0) {
                        mPresenter.givingGifts(gift, mProgramId, 1);
                        cancelGiftPannelDialog();
                        return;
                    }
                    if (mGiftsResult == null || mGiftsResult.getBalance() == null) {
                        ToastUtil.showInfo(ComprehensiveLiveActivity.this, R.string.live_balance_error_tip);
                        return;
                    }
                    if (gift.getGiftCost() > mGiftsResult.getBalance()) {
                        ToastUtil.showInfo(ComprehensiveLiveActivity.this, R.string.live_balance_low_tip);
                        return;
                    }
                    mPresenter.givingGifts(gift, mProgramId, 1);
                    cancelGiftPannelDialog();
                }
            }
        }).setOnDismissListener(new GiftPannelDialog.OnDismissListener() {
            @Override
            public void onDismiss() {
                view.startAnimation(mBottomOutAnimation);
                mGiftPannelDialog = null;
            }
        }).show(getSupportFragmentManager(), "gifts_pannel");
    }

    /**
     * 调用购物功能
     *
     * @param view
     */
    private void openBottomGoods(View view) {
        if (mGoodsPannelDialog != null && mGoodsPannelDialog.isVisible()) return;
        mPresenter.getGoods((int) mProgramId);
        view.startAnimation(mBottomInAnimation);
        mGoodsPannelDialog.setOnShopPurchaseListener(new GoodsPanelControl.OnShopPurchaseListener() {
            @Override
            public void onPurchase(Goods goods) {
                if (LoginManager.getInstance().checkLogin()) {
                    PayManager.getInstance().pay(PayConst.PAY_TYPE_GOODS, goods.getId());
                    cancelGiftPannelDialog();
                }
            }
        }).setOnDismissListener(new GoodsPannelDialog.OnDismissListener() {
            @Override
            public void onDismiss() {
                view.startAnimation(mBottomOutAnimation);
                mGoodsPannelDialog = null;
            }
        }).show(getSupportFragmentManager(), "goods_pannel");
    }

    /**
     * 取消录音
     */
    public void closeRecord() {
        if (mRecordPannelDialog != null && mRecordPannelDialog.getShowsDialog() && HomeLiveManager.getInstance().isRecording()) {
            mRecordPannelDialog.cancel();
        }
    }

    /**
     * 调用语音留言功能
     *
     * @param view
     */
    private void openBottomRecord(View view) {
        view.startAnimation(mBottomInAnimation);
        mRecordPannelDialog = RecordPannelDialog.getInstance(getPageId());
        mRecordPannelDialog.setData(mProgramId).setOnSendAudioMsgToServerListener(new RecordPannelDialog.OnSendAudioMsgToServerListener() {
            @Override
            public void onSendAudioMsgToServer(RecordUploadHelper.UploadParam program, String fileName) {
                mPresenter.sendAudioMessageToServer(ComprehensiveLiveActivity.this, program, fileName);
            }
        }).setOnSendAudioMsgSuccessListener(new RecordPannelDialog.OnSendAudioMsgSuccessListener() {
            @Override
            public void OnSendAudioMsgSuccess() {
                cancelGiftPannelDialog();
            }
        }).setOnDismissListener(new RecordPannelDialog.OnDismissListener() {
            @Override
            public void onDismiss() {
                view.startAnimation(mBottomOutAnimation);
                mRecordPannelDialog = null;
            }
        }).show(getSupportFragmentManager(), "record_panel");
    }


    private void initHomeLiveManager() {
        // Log.i("111222333444", "2");
        mPresenter.initNim(mProgramId);
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "initHomeLiveManager");
        }

        if (mProgramId > 0) {
//            HomeLiveManager.getInstance().loopLiveStatus(mProgramId);
            // Log.i("111222333444", "3-1");
            mPresenter.getLiveInfo(mProgramId);
        } else {
            Log.i(TAG, "initHomeLiveManager, null programid, no old data, show error");
            showErrorInfo(ErrorStatus.ERROR_ARGUMENT);
        }
    }

    @Override
    public String getPageId() {
        return Constants.PAGE_ID_LIVE_ROOM;
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPlayerChanged(PlayerChangedEBData playerChangedEBData) {
        if (bPlayerEnabled) {
            HomeLiveManager.getInstance().onLiveExit();
            this.pop();
//            exitChatRoom();
        }
    }

    @Override
    public void showFileNotExist() {
        String msg = getResources().getString(R.string.live_upload_failed) + ": " + getResources().getString(R.string.live_file_not_exist);
//        showErrorToast(msg);
        HomeLiveManager.getInstance().setRecorderStatus(RecorderStatus.IDLE);
    }

    @Override
    public void showRecordUploadProgress(int progress) {
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "showRecordUploadProgress: " + progress);
        }
//        mRecordButtonImage.setProgress(progress);
    }

    @Override
    public void showRecordUploadSuccess() {
        Log.e(TAG, "live---net--- showRecordUploadSuccess Thread:" + Thread.currentThread().getName());
        //语音消息发送成功后才上报发送事件
        LiveInfoDetail info = HomeLiveManager.getInstance().getPlayingInfo();
        ReportUtil.addLivingLeaveMessageEvent(String.valueOf(info.getLiveId()), String.valueOf(info.getProgramId()), info.getComperes(), null);
        HomeLiveManager.getInstance().setRecorderStatus(RecorderStatus.UPLOADED);
        HomeLiveManager.getInstance().deleteFile();
    }

    @Override
    public void showRecordUploadFailure() {
        Log.e(TAG, "live---net--- showRecordUploadFailure 01 Thread:" + Thread.currentThread().getName());
        //由于文件上传的回调是在子线程进行的，所以用post的方式让它在UI线程更新UI
        post(new Runnable() {
            @Override
            public void run() {
                Log.e(TAG, "live---net--- showRecordUploadFailure 02 Thread:" + Thread.currentThread().getName());
                ToastUtil.showError(ComprehensiveLiveActivity.this, R.string.live_upload_failed);
                HomeLiveManager.getInstance().setRecorderStatus(RecorderStatus.FAILURE);
            }
        });
    }

    @Override
    public void showLiveInfo(LiveInfoDetail info) {
        // Log.i("111222333444", "4");
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "showLiveInfo");
        }
        if (info == null) {
            showErrorInfo(ErrorStatus.ERROR_REQUEST);
        } else {
            mLiveInfoDetail = info;
            if (info.getLiveAbilityList() != null) {
                mLiveAbilityList.clear();
                mLiveAbilityList.addAll(info.getLiveAbilityList());
                mLiveAbilityAdapter.notifyDataSetChanged();
            }

            String roomId = info.getRoomId();
            // FIXME: 2022/7/5 1721206561这个roomId是测试专用的新直播间的id
//            String roomId = "373127209";
            int status = info.getStatus();
            if (LivePresenter.DEBUG_LIVE) {
                Log.i(TAG, "showLiveInfo roomId: " + roomId + ", STATUS: " + status);
            }
            // Log.i("111222333444", "5");
            setLiveRoomContent(info);
            enterLiveRoom(roomId);
        }
    }

    private void enterLiveRoom(String roomId) {
        if (!TextUtils.isEmpty(roomId)) {
            if (LiveUtil.isUserBound()) {
                // Log.i("111222333444", "6");
                mPresenter.enterChatRoom(this, roomId);
//                    recordButtonBoxLayout.notifyUserLoginStatus(true);
            } else {
                // Log.i("111222333444", "-6");
                mPresenter.enterChatRoom(true, this, roomId);
//                    recordButtonBoxLayout.notifyUserLoginStatus(false);
                if (isFront && !gotoLogin) {
                    gotoLogin = true;
                }
            }
        }
    }

    @Override
    public void showErrorInfo(ErrorStatus status) {
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "getShowingInfo onError msg: " + status);
        }
        showErrorStatus(status);
    }

    /**
     * 设置直播间详情信息
     *
     * @param info
     */
    private void setLiveRoomContent(LiveInfoDetail info) {
        HomeLiveManager.getInstance().setShowingInfo(info);
        Log.i(TAG, "---live   setLiveRoomContent: " + info.getStatus());

        showCommonInfo(info);

        int status = info.getStatus();

        if (info.getShowOnlineNum() != null) {
            if (status == LiveInfoDetail.STATUS_LIVING) {
                // 只有【直播中】的状态才需要判断是否显示在线人数，其它状态一律不显示
                isShowOnlineNum = info.getShowOnlineNum();
            } else {
                isShowOnlineNum = 0;
            }
        } else {
            isShowOnlineNum = 0;
        }

//        mPresenter.refreshChatRoomInfo(mProgramId);

        PlayItem item = PlayerManagerHelper.getInstance().getCurPlayItem();
        if (item instanceof LivePlayItem) {
            if (info.getProgramId() == ((LivePlayItem) item).getLiveId()) {
                ((LivePlayItem) item).setStatus(status);
            }
        }

        if (status == LiveInfoDetail.STATUS_NOT_START) {
            if (HomeLiveManager.getInstance().getLiveStatus() != LiveStatus.NOT_START && !mLiveNotStartLayout.isShown()) {
                showForecast(info);
            }
            // 用户手动点击了“立即收听”按钮，这个时候需要提示
            if (checkLiveInfoByUser.compareAndSet(true, false)) {
                ToastUtil.showNormal(this, R.string.live_coming_1);
            }
            stopLive();
            //iconLiving.setVisibility(View.GONE);
        } else if (status == LiveInfoDetail.STATUS_FINISHED) {
            if (HomeLiveManager.getInstance().getLiveStatus() != LiveStatus.FINISHED && !mLiveFinishLayout.isShown()) {
                showFinish();
            }
            stopLive();
            //iconLiving.setVisibility(View.GONE);
        } else if (status == LiveInfoDetail.STATUS_LIVING) {
            //iconLiving.setVisibility(View.VISIBLE);
            showLive(info);
        } else if (status == LiveInfoDetail.STATUS_COMING) {
            if (HomeLiveManager.getInstance().getLiveStatus() != LiveStatus.COMING && !mComingLayout.isShown()) {
                showComing();
            }
            stopLive();
            //iconLiving.setVisibility(View.GONE);
        } else if (status == LiveInfoDetail.STATUS_DELAYED) {
            if (HomeLiveManager.getInstance().getLiveStatus() != LiveStatus.NOT_START && !mLiveNotStartLayout.isShown()) {
                showForecast(info);
            }
            stopLive();
            //iconLiving.setVisibility(View.GONE);
        } else {
            //未知状态
//            hideRecordButton();
            stopLive();
        }
        //report
        if (!isShow) reportContentShowEvent(info);
    }

    private void reportContentShowEvent(LiveInfoDetail info) {
        isShow = true;
        ReportUtil.addContentShowEvent(String.valueOf(info.getProgramId()), ReportParamUtil.getRadioType(ResType.LIVE_TYPE), "0", String.valueOf(info.getLiveId()), "无", getPageId(), getPageId(), "0");

    }

    private void showCommonInfo(LiveInfoDetail result) {
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "showCommonInfo");
        }
        if (!TextUtils.isEmpty(result.getLiveBgPic()) && !liveBgUrl.equals(result.getLiveBgPic())) {
            ImageLoader.getInstance().displayImage(ComprehensiveLiveActivity.this, result.getLiveBgPic(), liveBg, ResUtil.getDrawable(R.drawable.comprehensive_live_bg), new OnImageLoaderListener() {
                @Override
                public void onLoadingFailed(String url, ImageView target, Exception exception) {

                }

                @Override
                public void onLoadingComplete(String url, ImageView target) {
                    liveBgUrl = result.getLiveBgPic();
                }
            });
        }
        liveTitle.setText(StringUtil.getMaxSubstring(result.getLiveName(), 20));
        liveAnchor.setText(TextUtils.isEmpty(result.getComperes()) ? "" : "主播    " + result.getComperes());
        if (liveThemeContent != null && result.getAnnouncement() != null && liveThemeContent.equals(result.getAnnouncement())) {
            return;
        }
        liveThemeContent = result.getAnnouncement();
        if (TextUtils.isEmpty(liveThemeContent)) {
            liveTheme.setVisibility(View.GONE);
        } else {
            liveTheme.setVisibility(View.VISIBLE);
            MessageBean messageBean = new MessageBean();
            messageBean.contentString = liveThemeContent;
            messageBean.chatTime = String.valueOf(System.currentTimeMillis());
            mMessages.add(new LiveRoomMessage(LiveRoomMessage.MessageType.MESSAGE_THEME, null, messageBean));
        }
    }

    private void stopForecastTimer() {
        if (mForecastTimer != null) {
            mForecastTimer.cancel();
            mForecastTimer = null;
        }
    }

    /**
     * 收到消息动画，在左下部向上弹起
     *
     * @param message
     */
    private void showMessageReceived(MessageBean message) {
        final TextView textView = (TextView) LayoutInflater.from(this).inflate(R.layout.comprehensive_live_screen_bullet_text, null);
        textView.setText(String.format(getString(R.string.live_message_received), message.nickName));
//        textView.setBackgroundResource(R.drawable.live_message_received_bg);
        textView.setBackgroundDrawable(SkinCompatResources.getDrawable(this, R.drawable.comprehensive_live_message_received_bg));
        final FrameLayout.LayoutParams lp = new FrameLayout.LayoutParams(FrameLayout.LayoutParams.WRAP_CONTENT, FrameLayout.LayoutParams.WRAP_CONTENT);
        lp.leftMargin = liveAnchor.getLeft();
        mScreenBulletContainer.addView(textView, lp);

        int bottom = mScreenBulletContainer.getBottom() - mScreenBulletContainer.getTop();

        if (mLastMessageText != null) {
            int s = (int) mLastMessageText.getY();
            int e = (int) (bottom * 0.60);

            ObjectAnimator oan = ObjectAnimator.ofFloat(mLastMessageText, "y", s, e);
            oan.setDuration(300);

            ObjectAnimator oaa = ObjectAnimator.ofFloat(mLastMessageText, "alpha", 1, 0);
            oaa.setDuration(300);

            AnimatorSet as = new AnimatorSet();
            as.playTogether(oan, oaa);
            as.start();
        }

        int width = View.MeasureSpec.makeMeasureSpec((1 << 30) - 1, View.MeasureSpec.AT_MOST);
        int height = View.MeasureSpec.makeMeasureSpec((1 << 30) - 1, View.MeasureSpec.AT_MOST);
        textView.measure(width, height);

        int middle = bottom - (textView.getMeasuredHeight() * 2);
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001576707636?userId=2169710问题
        ObjectAnimator oan = ObjectAnimator.ofFloat(textView, "y", bottom, middle - ResUtil.getInt(R.integer.live_msg_y_d_value));
        oan.setDuration(500);
        oan.setInterpolator(new DecelerateInterpolator());
        AnimatorSet animSet = new AnimatorSet();
        animSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
                bDoMessageReceiveAnim = true;
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                bDoMessageReceiveAnim = false;
                if (mScreenBulletContainer != null && mLastMessageText != null) {
                    mScreenBulletContainer.removeView(mLastMessageText);
                }
                mLastMessageText = textView;
                if (mMessageReceiveQueue.size() > 0) {
                    MessageBean mb = mMessageReceiveQueue.removeFirst();
                    showMessageReceived(mb);
                }
                mLastMessageText.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (mLastMessageText != null && mScreenBulletContainer != null) {
                            mScreenBulletContainer.removeView(mLastMessageText);
                        }
                    }
                }, 4000);
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                bDoMessageReceiveAnim = false;
            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        animSet.play(oan);
        animSet.start();
    }

    // 直播开始前
    private void showForecast(LiveInfoDetail result) {
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "showForecast");
        }
        HomeLiveManager.getInstance().setLiveStatus(LiveStatus.NOT_START);
        recyclerView.setVisibility(View.GONE);
        liveCuRankLayout.setVisibility(View.GONE);
        liveBottomAbility.setVisibility(View.GONE);
//        userHeaderParentView.setVisibility(View.GONE);
//        mListenerNumberText.setVisibility(View.GONE);
        mLiveFinishLayout.setVisibility(View.GONE);
        mErrorLayout.setVisibility(View.GONE);
        mComingLayout.setVisibility(View.GONE);
        startForecastTimer(result.getStartTime(), result.getServerTime());
    }

    private void showFinish() {
        //轮询到结束并且播放器也没有播放，就不在轮询了。
//        HomeLiveManager.getInstance().stopLoopLiveStatus();
        HomeLiveManager.getInstance().setLiveStatus(LiveStatus.FINISHED);
        liveCuRankLayout.setVisibility(View.VISIBLE);
        liveBottomAbility.setVisibility(View.GONE);
        livePendingLayout.setVisibility(View.GONE);
        recyclerView.setVisibility(View.GONE);
        if (mLiveInfoDetail != null && mLiveInfoDetail.getTotalOnlineNum() != null) {
            mLiveFinishNumText.setText(formatNumberWithoutTenThoundText(mLiveFinishNumTenThoundText, mLiveInfoDetail.getTotalOnlineNum()));
        }
        mLiveFinishLayout.setVisibility(View.VISIBLE);
//        userHeaderParentView.setVisibility(View.GONE);
        mLiveNotStartLayout.setVisibility(View.GONE);
        mComingLayout.setVisibility(View.GONE);
        mErrorLayout.setVisibility(View.GONE);
//        hideRecordButton();
        if (mPlayItem != null && bPlayerEnabled && HomeLiveManager.getInstance().getLiveStatus() == LiveStatus.LIVING) {
            bPlayerEnabled = false;
            PlayerManager.getInstance().reset();
        }
    }

    private void showLive(LiveInfoDetail info) {
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "showLive result: " + info.getProgramName());
        }
        boolean isNeedRestart = HomeLiveManager.getInstance().getLiveStatus() != LiveStatus.LIVING;
        HomeLiveManager.getInstance().setLiveStatus(LiveStatus.LIVING);
//        userHeaderParentView.setVisibility(View.VISIBLE);
        recyclerView.setVisibility(View.VISIBLE);
        liveCuRankLayout.setVisibility(View.VISIBLE);
        liveBottomAbility.setVisibility(View.VISIBLE);
        livePendingLayout.setVisibility(View.GONE);
        mLiveNotStartLayout.setVisibility(View.GONE);
        mLiveFinishLayout.setVisibility(View.GONE);
        mErrorLayout.setVisibility(View.GONE);
        mComingLayout.setVisibility(View.GONE);
//        showImageButton();
        mPlayItem = HomeLiveManager.getInstance().toPlayItem(info);
        String liveUrl = mPlayItem.getPlayUrl();
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "live--- showLive play liveUrl: " + liveUrl);
        }
        setLiveCuRankUI(listenerCount, rankUserList);
        if (liveUrl == null) {
            showComing();
        } else {
            boolean isPlaying = PlayerManagerHelper.getInstance().isPlaying();

            bPlayerEnabled = true;

            HomeLiveManager.getInstance().setPlayingInfo(info);
            PlayItem playItem = PlayerManagerHelper.getInstance().getCurPlayItem();
            boolean isSameProgram = mPlayItem instanceof LivePlayItem && PlayerManagerHelper.getInstance().isSameProgram(playItem, String.valueOf(((LivePlayItem) mPlayItem).getLiveId()));
            if (isPlaying && isSameProgram) {
                if (LivePresenter.DEBUG_LIVE) {
                    Log.i(TAG, "live--- showLive play but is playing and have same id, return");
                }
                return;
            }
            if (LivePresenter.DEBUG_LIVE) {
                Log.i(TAG, "live--- showLive play try start isNeedRestart:" + isNeedRestart);
            }
            //原本的逻辑是进入直播间后自动播放，现在要求不自动播放
            if (isNeedRestart || !isPlaying) {
                // 用 restart() 替换 start(), 解决调用 stopLive() 暂停播放后, 再次调用 start() 无法再次起播或者起播时间过长的问题
                PlayerManagerHelper.getInstance().restart(((LivePlayItem) mPlayItem).getLiveId() + "", PlayerConstants.RESOURCES_TYPE_LIVING);
                // Log.i("111222333444", "2-2");
//                initHomeLiveManager();
            }
        }

    }

    private void showComing() {
        HomeLiveManager.getInstance().setLiveStatus(LiveStatus.COMING);
        mLiveNotStartLayout.setVisibility(View.GONE);
        mLiveFinishLayout.setVisibility(View.GONE);
        livePendingLayout.setVisibility(View.GONE);
        recyclerView.setVisibility(View.GONE);
        liveCuRankLayout.setVisibility(View.GONE);
        liveBottomAbility.setVisibility(View.GONE);
        mErrorLayout.setVisibility(View.GONE);
//        userHeaderParentView.setVisibility(View.GONE);
        mComingLayout.setVisibility(View.VISIBLE);
//        hideRecordButton();
    }

    /**
     * 根据错误码展示UI
     *
     * @param status
     */
    private void showErrorStatus(ErrorStatus status) {
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "showErrorStatus reason: " + status);
        }
        if (status == ErrorStatus.ERROR_NET) {
            if (mLiveInfoDetail == null) {
                showErrorLayout(ResUtil.getString(R.string.network_nosigin_try), true);
                mComingLayout.setVisibility(View.GONE);
                mLiveNotStartLayout.setVisibility(View.GONE);
                mLiveFinishLayout.setVisibility(View.GONE);
                recyclerView.setVisibility(View.GONE);
                liveCuRankLayout.setVisibility(View.GONE);
                liveBottomAbility.setVisibility(View.GONE);
                livePendingLayout.setVisibility(View.GONE);
                mErrorLayout.setVisibility(View.GONE);
            } else {
                onLoading();
                loading.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (loading != null && loading.getVisibility() == View.VISIBLE) {
                            onLoadFinish();
                            ToastUtil.showInfo(ComprehensiveLiveActivity.this, "当前无网络，稍后再试吧");
                        }
                    }
                }, 10000);
            }
        } else if (status == ErrorStatus.ERROR_REQUEST) {
//            ToastUtil.showInfo(this, "数据有误");
        } else if (status == ErrorStatus.ERROR_PLAY) {
            //播放错误，如果状态是结束，则显示结束
            LiveInfoDetail info = HomeLiveManager.getInstance().getPlayingInfo();
            if (info == null) {
                HomeLiveManager.getInstance().setErrorStatus(status);
                showCannotPlayError();
                return;
            }
            if (info.getStatus() == LiveInfoDetail.STATUS_FINISHED) {
                showFinish();
                return;
            } else if (info.getStatus() == LiveInfoDetail.STATUS_LIVING) {
                //直播倒计时结束，开始播放，由于流的延迟性，很可能播放失败，所以在直播开始后的1分钟内的
                //播放失败，当做预告处理
                long serverTime = info.getServerTime();
                long startTime = info.getStartTime();
                if (LivePresenter.DEBUG_LIVE) {
                    Log.i(TAG, "showErrorStatus living serverTime: " + serverTime + ", startTime: " + startTime);
                }
                if (serverTime - startTime <= PLAY_ERROR_TOLERANCE_TIME) {
                    showComing();
                    return;
                }
            }
        } else if (status == ErrorStatus.ERROR_ARGUMENT) {
            ToastUtil.showInfo(this, "参数不合法");
        }
        HomeLiveManager.getInstance().setErrorStatus(status);
        HomeLiveManager.getInstance().setLiveStatus(LiveStatus.ERROR);
        bPlayerEnabled = false;
    }

    private void showCannotPlayError() {
        mComingLayout.setVisibility(View.GONE);
        mLiveNotStartLayout.setVisibility(View.GONE);
        mLiveFinishLayout.setVisibility(View.GONE);
        recyclerView.setVisibility(View.GONE);
        liveCuRankLayout.setVisibility(View.GONE);
        liveBottomAbility.setVisibility(View.GONE);
        livePendingLayout.setVisibility(View.GONE);
        mErrorLayout.setVisibility(View.VISIBLE);
    }


    private void showErrorLayout(String error, boolean clickToRetry) {
        if (mNetLayout == null) {
            mNetLayout = (RelativeLayout) mErrorPage.inflate();
            TextView tvNetworkNosignTips = mNetLayout.findViewById(R.id.tv_network_nosign_tips);
            tvNetworkNosignTips.setVisibility(View.VISIBLE);
            TextView tvNetworkNosign = mNetLayout.findViewById(R.id.tv_network_nosign);
            tvNetworkNosign.setText(error);
            // 支持点击重试
            if (clickToRetry) {
                ImageView ivNetworkNoSign = mNetLayout.findViewById(R.id.network_nosigin);
                ivNetworkNoSign.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        ViewUtil.setViewVisibility(mNetLayout, View.GONE);
                        // Log.i("111222333444", "2-3");
                        initHomeLiveManager();
                        TextView tvNetworkNosign = mNetLayout.findViewById(R.id.tv_network_nosign);
                        String text = null;
                        if (tvNetworkNosign != null) {
                            text = tvNetworkNosign.getText().toString();
                        }
                        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_NETWORK_RETRY, text, getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
                    }
                });
            }
        }
        ViewUtil.setViewVisibility(mNetLayout, View.VISIBLE);
        TextView tvNetworkNosign = mNetLayout.findViewById(R.id.tv_network_nosign);
        String text = null;
        if (tvNetworkNosign != null) {
            text = tvNetworkNosign.getText().toString();
        }
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_NETWORK_RETRY, text, getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
    }

    /**
     * 显示收听人数
     *
     * @param number
     */
    @Override
    public void showListenerNumber(int number) {
        this.listenerCount = number;
//        SecureRandom secureRandom = new SecureRandom();
//        this.listenerCount = secureRandom.nextInt(1000000);
        setLiveCuRankUI(this.listenerCount, this.rankUserList);
    }

    private String formatNumberWithTenThoundText(int number) {
        if (number < 10000) {
            return String.format(getString(R.string.live_listening_number_low), number);
        }
        float result = number / 10000f;
        return String.format(getString(R.string.live_listening_number_high), result);
    }

    private String formatNumberWithoutTenThoundText(View view, Long number) {
        if (number < 10000) {
            if (view != null) {
                view.setVisibility(View.GONE);
            }
            return String.format(getString(R.string.live_listening_number_low), number);
        }
        if (view != null) {
            view.setVisibility(View.VISIBLE);
        }
        float result = number / 10000f;
        return String.format(getString(R.string.live_listening_number_high_without_text), result);
    }

    /**
     * 显示成员进入，如果当前正在执行{@link # showMemberEnter(ChatUserInfo)}中的动画,则这个消息进入队列，
     * 等动画完成后再进行
     *
     * @param member
     */
    @Override
    public void showRoomMemberEnter(ChatUserInfo member) {
        mPresenter.getListenerNumber(mProgramId);
        if (member != null) {
            // TODO: 2022/8/24
            if (LivePresenter.DEBUG_LIVE) {
                Log.i(TAG, "showRoomMemberEnter: " + member.getNickName());
            }
//            if (userHeaderParentView.getChildCount() > 5) return;
//            if (userHeaderParentView.getChildCount() == 5) {
////                addMoreHeaderTpParent();
//            }
//            if (!containsMember(member)) {
//                mOnlineMembers.add(member);
//                addHeaderToParent(member, userHeaderParentView.getChildCount() * ResUtil.getDimen(R.dimen.x24));
//            }
//            if (mOnlineMembers.size() >= 5) {
//                addMoreHeaderTpParent();
//            }
            //如果自己没登陆而且没有获取到昵称
//            if (!LiveUtil.isUserBound() && StringUtil.isEmpty(member.getNickName())) return;

            /*
            //当前版本不再显示成员进入的漂浮提示
            //如果正在做成员进入动画，或者成员进入队列中有元素在排队，添加到队尾，否则展示此条进入提示
            if (mMemberEnterQueue.size() > 0 || bDoMemberEnterAnim) {
                mMemberEnterQueue.addLast(member);
            } else {
                showMemberEnter(member);
            }
             */

        }

    }

    @Override
    public void showRoomMemberExit(ChatUserInfo member) {
        Log.i(TAG, "---live showRoomMemberExit");
        mPresenter.getListenerNumber(mProgramId);
        if (!containsMember(member)) return;
        if (mPresenter.isChatRoomEntered()) mPresenter.fetchRoomMembers(mProgramId);
    }

//    /**
//     * 成员进入动画，在顶部自右往左飞过
//     *
//     * @param member
//     */
//    private void showMemberEnter(ChatUserInfo member) {
//        // TODO: 2022/6/30
//        Context context = this;
//        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001107580293问题
//        if (context == null) {
//            return;
//        }
//
//        // 将进入直播间的系统消息添加到列表
//        if (mMessageAdapter != null) {
//            mMessages.add(new LiveRoomMessage(LiveRoomMessage.MessageType.MESSAGE_ENTER_ROOM, member, null));
//            mMessageAdapter.notifyItemInserted(mMessages.size() - 1);
//            if (!recyclerView.canScrollVertically(1)) {
//                recyclerView.smoothScrollToPosition(mMessages.size() - 1);
//            }
//        }
//
//
//        final TextView textView = (TextView)
//                LayoutInflater.from(context).inflate(R.layout.comprehensive_live_screen_bullet_text, null);
//        textView.setText(String.format(getString(R.string.live_member_enter), member.getNickName()));
//
//        textView.setBackgroundDrawable(SkinCompatResources.getDrawableCompat(this, R.drawable.comprehensive_live_screen_bullet_bg));
//        textView.setGravity(Gravity.CENTER_VERTICAL);
//        FrameLayout.LayoutParams lp = new FrameLayout.LayoutParams(
//                FrameLayout.LayoutParams.WRAP_CONTENT,
//                FrameLayout.LayoutParams.WRAP_CONTENT);
//        lp.topMargin = (int) (mScreenBulletContainer.getHeight() * 0.2);
//        mScreenBulletContainer.addView(textView, lp);
//
//        int start = mScreenBulletContainer.getRight();
//        //直播中的图标右边50px慢下来
//        int middle = liveAnchor.getRight() + 50;
//        int left = liveAnchor.getLeft();
//
//        ObjectAnimator oar = ObjectAnimator.ofFloat(textView, "x", start, middle);
//        oar.setDuration(500);
//        oar.setInterpolator(new DecelerateInterpolator());
//
//        ObjectAnimator oam = ObjectAnimator.ofFloat(textView, "x", middle, left);
//        oam.setDuration(3000);
//
//        //滑出屏幕，需要一个负值,由于View刚Add，还没有宽度，所以写一个参考值
//        ObjectAnimator oal = ObjectAnimator.ofFloat(textView, "x", left, -300);
//        oal.setDuration(300);
//        oal.setInterpolator(new AccelerateInterpolator());
//
//        AnimatorSet animSet = new AnimatorSet();
//        animSet.addListener(new Animator.AnimatorListener() {
//            @Override
//            public void onAnimationStart(Animator animation) {
//                bDoMemberEnterAnim = true;
//            }
//
//            @Override
//            public void onAnimationEnd(Animator animation) {
//                bDoMemberEnterAnim = false;
//                if (mScreenBulletContainer != null && textView != null) {
//                    mScreenBulletContainer.removeView(textView);
//                }
//                if (mMemberEnterQueue.size() > 0) {
//                    ChatUserInfo member = mMemberEnterQueue.removeFirst();
//                    showMemberEnter(member);
//                }
//            }
//
//            @Override
//            public void onAnimationCancel(Animator animation) {
//                bDoMemberEnterAnim = false;
//            }
//
//            @Override
//            public void onAnimationRepeat(Animator animation) {
//
//            }
//        });
//        animSet.play(oal).after(oam).after(oar);
//        animSet.start();
//    }


    /**
     * 显示收到消息，如果当前正在执行{@link #showMessageReceived(MessageBean)}中的动画,
     * 则这个消息进入队列，等动画完成后再进行。如果是多条消息，则逐条进入队列
     *
     * @param messageList
     */
    @Override
    public void showChatMessageReceived(ArrayList<MessageBean> messageList) {
        if (messageList == null || messageList.size() <= 0) {
            if (LivePresenter.DEBUG_LIVE) {
                Log.i(TAG, "showChatMessageReceived empty message list");
            }
            return;
        }
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "showChatMessageReceived message count: " + messageList.size());
        }

        int startPosition = mMessages.size();
        for (MessageBean messageBean : messageList) {
            mMessages.add(new LiveRoomMessage(LiveRoomMessage.MessageType.MESSAGE_RECEIVE, null, messageBean));
        }
        mMessageAdapter.notifyItemRangeInserted(startPosition, messageList.size());
        if (!recyclerView.canScrollVertically(1)) {
            recyclerView.smoothScrollToPosition(mMessages.size() - 1);
        }
//        showMessageReceivedAnimation(messageList);
    }

    @Override
    public void onChatMessageReceivedToRemove(LiveCommentStatusMsg liveCommentStatusMsg) {
        if (liveCommentStatusMsg == null) {
            if (LivePresenter.DEBUG_LIVE) {
                Log.i(TAG, "onChatMessageReceivedToRemove --- liveCommentStatusMsg is null");
            }
            return;
        }
        if (liveCommentStatusMsg.getStatus() != LiveCommentStatusMsg.STATUS_REMOVE) {
            if (LivePresenter.DEBUG_LIVE) {
                Log.i(TAG, "onChatMessageReceivedToRemove --- liveCommentStatusMsg.getStatus() is not STATUS_REMOVE, not need remove");
            }
            return;
        }
        if (mMessages == null || mMessages.isEmpty()) {
            if (LivePresenter.DEBUG_LIVE) {
                Log.i(TAG, "onChatMessageReceivedToRemove --- mMessages is empty");
            }
            return;
        }
        int removedPosition = -1;
        for (int index = 0; index < mMessages.size(); index++) {
            boolean isSameMsgData = LiveBeanTransUtils.isSameMsgData(mMessages.get(index).getMessage(), liveCommentStatusMsg);
            if (isSameMsgData) {
                removedPosition = index;
                break;
            }
        }
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "onChatMessageReceivedToRemove --- removedPosition=" + removedPosition);
        }
        if (removedPosition < 0 || removedPosition >= mMessages.size()) {
            if (LivePresenter.DEBUG_LIVE) {
                Log.i(TAG, "onChatMessageReceivedToRemove --- can't find removedPosition");
            }
            if (SWITCH_TEST_BUTTON) {
                ToastUtil.showError(null, "指定删除的消息 [" + liveCommentStatusMsg.getContent() + "] 不存在");
            }
            return;
        }
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "onChatMessageReceivedToRemove --- removedPosition is exist, start to remove");
        }
        if (SWITCH_TEST_BUTTON) {
            ToastUtil.showError(null, "删除 [" + liveCommentStatusMsg.getContent() + "] 成功");
        }
        mMessages.remove(removedPosition);
        mMessageAdapter.notifyItemRemoved(removedPosition);
        mMessageAdapter.notifyItemRangeChanged(removedPosition, mMessages.size() - removedPosition);
    }

    public void showChatMessageSent(ArrayList<MessageBean> messageList) {
        if (messageList == null || messageList.isEmpty()) {
            if (LivePresenter.DEBUG_LIVE) {
                Log.i(TAG, "showChatMessageReceived empty message list");
            }
            return;
        }
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "showChatMessageReceived message count: " + messageList.size());
        }
        int startPosition = mMessages.size();
        for (MessageBean messageBean : messageList) {
            mMessages.add(new LiveRoomMessage(LiveRoomMessage.MessageType.MESSAGE_SEND, null, messageBean));
        }
        mMessageAdapter.notifyItemRangeInserted(startPosition, 1);
        if (!recyclerView.canScrollVertically(1)) {
            recyclerView.smoothScrollToPosition(mMessages.size() - 1);
        }

//        showMessageReceivedAnimation(messageList);
    }

    private void showMessageReceivedAnimation(ArrayList<MessageBean> messageList) {
        //消息以列表的方式组织，所以如果消息队列中有消息或者正在进行消息提示的动画，把所有
        //新来的消息添加到队列，
        if (mMessageReceiveQueue.size() > 0 || bDoMessageReceiveAnim) {
            mMessageReceiveQueue.addAll(messageList);
        } else {
            //否则，取列表中的第一条展示，如果还有剩下的，进入消息队列
            MessageBean message = messageList.remove(0);
            showMessageReceived(message);
            if (messageList.size() > 0) {
                mMessageReceiveQueue.addAll(messageList);
            }
        }
    }

    public void releaseOnDestroyView() {
        if (mScrollListener != null) recyclerView.removeOnScrollListener(mScrollListener);
        if (goodsCardControl != null) goodsCardControl.destory();
        PlayerManager.getInstance().removePlayControlStateCallback(mPlayerStateListener);
        exitLiveRoom();
    }

    /**
     * 退出云信
     */
    private void clearNimStatus() {
        mPresenter.exitChatRoom();
        mPresenter.logoutIm();
    }

    @Override
    public void onDestroy() {
        // Log.i("111222333444", "13");

        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "onDestroy");
        }
        LoginManager.getInstance().unregisterLoginListener(this);
        NetworkManager.getInstance().removeNetworkReadyListener(this);
        releaseOnDestroyView();
        releaseOnDestroy();
        if (goodsCardControl != null) goodsCardControl.dismiss();
        mBottomInAnimation = null;
        mBottomOutAnimation = null;
        super.onDestroy();
    }

    public void releaseOnDestroy() {
        if (HomeLiveManager.getInstance().isPlaying()) {
            HomeLiveManager.getInstance().stopListen();
        }
        if (HomeLiveManager.getInstance().isRecording()) {
            HomeLiveManager.getInstance().stopRecord(true);
        }
//        if (NetworkUtil.isNetworkAvailableWidthDefaultToast(this)) {
//            stopLoopLiveStatus();
//        }
//        HomeLiveManager.getInstance().removeRecorderStatusObserver(mRecorderStatusObserver);
//
//        stopRecordTimer();
        stopForecastTimer();
        HomeLiveManager.getInstance().resetStatus();
        HomeLiveManager.getInstance().setPresenter(null);
//        ComponentUtil.removeObserver(UserComponentConst.NAME, mUserLoginComponent);
    }


    private void exitLiveRoom() {
        clearNimStatus();
        clearChatRoomQueue();
    }

    private void clearChatRoomQueue() {
        mMessages.clear();
        mMemberEnterQueue.clear();
        mMessageReceiveQueue.clear();
    }


    //直播未开始显示倒计时，倒计时结束仍然未开始显示直播准备中
    private void startForecastTimer(long startTime, long serverTime) {
        if (mForecastTimer != null) {
            mForecastTimer.cancel();
            mForecastTimer = null;
        }
        long millisInFuture = startTime - serverTime;
        if (millisInFuture > 0) {
            livePendingLayout.setVisibility(View.GONE);
            mLiveNotStartLayout.setVisibility(View.VISIBLE);
            mForecastTimer = new CountDownTimer(millisInFuture, 1000) {
                @Override
                public void onTick(long millisUntilFinished) {
                    mForecastTextSecond.setText(generateForestStringSecond(millisUntilFinished));
                    mForecastTextMinute.setText(generateForestStringMinute(millisUntilFinished));
                    mForecastTextHour.setText(generateForestStringHour(millisUntilFinished));
                }

                @Override
                public void onFinish() {
                    mLiveNotStartLayout.setVisibility(View.GONE);
                    livePendingLayout.setVisibility(View.VISIBLE);
                    // Log.i("111222333444", "3-2");
                    mPresenter.getLiveInfo(mProgramId);
                }
            };
            mForecastTimer.start();
        } else {
            //此状态下主站正在对直播进行审核，所以status为直播中但是服务器时间>直播开始时间
            mLiveNotStartLayout.setVisibility(View.GONE);
            livePendingLayout.setVisibility(View.VISIBLE);
        }
    }

    private String generateForestStringHour(long period) {
        int seconds = (int) (period / 1000);
        int hour = seconds / (60 * 60);
        return String.valueOf(hour);
    }

    private String generateForestStringMinute(long period) {
        int seconds = (int) (period / 1000);
        int left = seconds % (60 * 60);
        int minute = left / 60;
        String minuteStr = null;
        if (minute < 10) {
            minuteStr = "0" + minute;
        } else {
            minuteStr = String.valueOf(minute);
        }
        return minuteStr;
    }

    private String generateForestStringSecond(long period) {
        int seconds = (int) (period / 1000);
        int left = seconds % (60 * 60);
        int second = left % 60;
        String secondStr = null;
        if (second < 10) {
            secondStr = "0" + second;
        } else {
            secondStr = String.valueOf(second);
        }
        return secondStr;
    }

//    private String generateForestString(long period) {
//        int seconds = (int) (period / 1000);
//        int hour = seconds / (60 * 60);
//        int left = seconds % (60 * 60);
//        int minute = left / 60;
//        int second = left % 60;
//        String minuteStr = null;
//        if (minute < 10) {
//            minuteStr = "0" + minute;
//        } else {
//            minuteStr = String.valueOf(minute);
//        }
//        String secondStr = null;
//        if (second < 10) {
//            secondStr = "0" + second;
//        } else {
//            secondStr = String.valueOf(second);
//        }
//        String ret = String.format(mForecastString, hour, minuteStr, secondStr);
//        return ret;
//    }


//    @Override
//    public void onClick(View v) {
//        int id = v.getId();
//        if (id == R.id.live_cancel_button_layout) {
//            cancel();
//        } else if (id == R.id.live_listen_button_layout) {
//            if (HomeLiveManager.getInstance().isPlaying()) {
//                stopListen();
//            } else if (HomeLiveManager.getInstance().isFinished()) {
//                startListen();
//            } else if (HomeLiveManager.getInstance().isListened() || HomeLiveManager.getInstance().isFailure()) {
//                startListen();
//            }
//        } else if (id == R.id.recordIv || id == R.id.recordTextViewParent) {
//            boolean isUserLogin = ComprehensiveLiveUtil.showLoginIfNotLogin();
//            if (isUserLogin) {
//                if (HomeLiveManager.getInstance().isRecording()) {
//                    stopRecord();
//                } else if (HomeLiveManager.getInstance().isIdle()) {
//                    startRecordWithPermissionCheck();
//                } else if (HomeLiveManager.getInstance().isUploading()) {
//                    //
//                } else if (HomeLiveManager.getInstance().isUploaded()) {
//                    HomeLiveManager.getInstance().setRecorderStatus(RecorderStatus.IDLE);
//                } else {
//                    sendMessage();
//                }
//            }
//        }
//    }

    @Override
    public void onHistoryMessageReceived(List<MessageBean> param, boolean isOld) {
        isLoadingHistory = false;
        if (mMessageAdapter != null) {
            int startPosition = 0;
            if (!isOld) {
                startPosition = mMessages.size();
            }
            MessageBean messageBean;
            for (int i = 0; i < param.size(); i++) {
                messageBean = param.get(i);
                mMessages.add(startPosition + i, new LiveRoomMessage(LiveRoomMessage.MessageType.MESSAGE_RECEIVE, null, messageBean));
            }
            mMessageAdapter.notifyItemRangeInserted(startPosition, param.size());
            if (isOld && isFirstLoadHistory.compareAndSet(true, false)) {
                recyclerView.scrollToPosition(mMessages.size() - 1);
                initScrollListener();
                recyclerView.addOnScrollListener(mScrollListener);
            }
        }
    }

    private void initScrollListener() {
        if (mScrollListener == null) {
            mScrollListener = new RecyclerView.OnScrollListener() {
                @Override
                public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
                    super.onScrollStateChanged(recyclerView, newState);
                }

                @Override
                public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                    super.onScrolled(recyclerView, dx, dy);
                    if (!recyclerView.canScrollVertically(-1) && !ListUtil.isEmpty(mMessages)) {
                        for (LiveRoomMessage mMessage : mMessages) {
                            MessageBean message = mMessage.getMessage();
                            if (message == null) {
                                continue;
                            }
                            if (isLoadingHistory) return;
                            isLoadingHistory = true;
//                            mPresenter.getHistoryMessageList(NimManager.getInstance().getRoomId(), Long.parseLong(message.chatTime), 100, QueryDirectionEnum.QUERY_OLD);
                            return;
                        }
                    }
                }
            };
        }
    }

    @Override
    public void onHistoryMessageQueryFailed(int code, Throwable exception) {
        Log.e(TAG, "查询历史消息失败：code=" + code + ",exception=" + exception);
        isLoadingHistory = false;
    }

    @Override
    public void onChatRoomMemberReceived(List<ChatUserInfo> users) {
//        userHeaderParentView.removeAllViews();
        mOnlineMembers.clear();
        ChatUserInfo userInfo;
        if (!ListUtil.isEmpty(users)) {
            for (int i = 0; i < users.size(); i++) {
                if (mOnlineMembers.size() >= 5) break;
                userInfo = users.get(i);
                if (!containsMember(userInfo)) {
//                    addHeaderToParent(userInfo, i * ResUtil.getDimen(R.dimen.x24));
                    mOnlineMembers.add(userInfo);
                }
            }
            if (mOnlineMembers.size() >= 5) {
//                addMoreHeaderTpParent();
            }
        }

        // do live 直播新接口逻辑
        // 更新排行榜信息
        GiftRankMsg giftRankMsg = new GiftRankMsg();
        List<GiftRankUser> rewardRanking = new ArrayList<>();
        for (int i = 0; i < users.size(); i++) {
            ChatUserInfo info = users.get(i);
            GiftRankUser giftRankUser = new GiftRankUser();
            giftRankUser.setAccount(info.getUserName());
            giftRankUser.setNickname(info.getNickName());
            giftRankUser.setAvatar(info.getAvatar());
            rewardRanking.add(giftRankUser);
        }
        giftRankMsg.setRewardRanking(rewardRanking);
        String rankMsg = new Gson().toJson(giftRankMsg);
        Log.d(TAG, "live--- onChatRoomMemberReceived rankMsg=" + rankMsg);
        NimManager.getInstance().parseCustomMsg(LIVE_CUSTOM_MSG_RANK_TYPE, rankMsg);

        // 显示 欢迎 xxx 进入直播间
        if (LoginManager.getInstance().isLogin() && isFirstEnter) {
            isFirstEnter = false;
            MessageBean messageBean = new MessageBean();
            messageBean.chatTime = String.valueOf(System.currentTimeMillis());
            ChatUserInfo myUserInfo = new ChatUserInfo();
            myUserInfo.setNickName(NimManager.getInstance().getUserNickName());
            LiveRoomMessage liveRoomMessage = new LiveRoomMessage(LiveRoomMessage.MessageType.MESSAGE_ENTER_ROOM, myUserInfo, messageBean);
            mMessages.add(liveRoomMessage);
            int insertStartPosition = mMessages.size();
            mMessageAdapter.notifyItemRangeInserted(insertStartPosition, 1);
            mMessageAdapter.notifyItemRangeChanged(insertStartPosition, mMessages.size() - insertStartPosition);
            // Log.i("111222333444", "10");
            if (!recyclerView.canScrollVertically(1)) {
                recyclerView.smoothScrollToPosition(mMessages.size() - 1);
            }
            recyclerView.postDelayed(new Runnable() {
                @Override
                public void run() {
                    // Log.i("111222333444", "12");
                    int deleteStartPosition = mMessages.indexOf(liveRoomMessage);
                    mMessages.remove(liveRoomMessage);
                    mMessageAdapter.notifyItemRangeRemoved(deleteStartPosition, 1);
                    mMessageAdapter.notifyItemRangeChanged(deleteStartPosition, mMessages.size() - deleteStartPosition);
//                    if (!recyclerView.canScrollVertically(1)) {
//                        recyclerView.smoothScrollToPosition(mMessages.size() - 1);
//                    }
                }
            }, 2000);
        }
    }

    private boolean containsMember(ChatUserInfo userInfo) {
        for (ChatUserInfo mOnlineMember : mOnlineMembers) {
            if (mOnlineMember.getUid() != null && mOnlineMember.getUid().equals(userInfo.getUid()))
                return true;
        }
        return false;
    }

//    /**
//     * 添加用户头像
//     *
//     * @param userInfo
//     * @param marginStart
//     */
//    private void addHeaderToParent(ChatUserInfo userInfo, int marginStart) {
//        StrokeRoundImageView circleImageView = new StrokeRoundImageView(this);
//        RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(ResUtil.getDimen(R.dimen.m40), ResUtil.getDimen(R.dimen.m40));
//        params.setMarginStart(marginStart);
//        userHeaderParentView.addView(circleImageView, params);
//        RequestOptions options = new RequestOptions()
//                .placeholder(R.drawable.live_user_head_default)                //加载成功之前占位图
//                .error(R.drawable.live_user_head_default);                //加载错误之后的错误图
//        Glide.with(this).load(userInfo.getAvatar()).apply(options).into(circleImageView);
//        if (!LiveUtil.isUserBound() && StringUtil.isEmpty(userInfo.getUserName())) {
//            //如果没有登录，则应记录游客头像的位置，登录的时候根据位置删除该头像
//            this.mineHeaderViewPosition = userHeaderParentView.getChildCount() - 1;
//        }
//    }

//    private void addMoreHeaderTpParent() {
//        RoundImageView circleImageView = new RoundImageView(this);
//        RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(ResUtil.getDimen(R.dimen.x40), ResUtil.getDimen(R.dimen.y40));
//        params.setMarginStart(5 * ResUtil.getDimen(R.dimen.x24));
//        userHeaderParentView.addView(circleImageView, params);
//        Glide.with(this).load(R.drawable.online_player_more_member).into(circleImageView);
//    }


//    private void removeMineHeaderView() {
//        if (this.mineHeaderViewPosition >= 0 && userHeaderParentView.getChildCount() > this.mineHeaderViewPosition) {
//            userHeaderParentView.removeViewAt(this.mineHeaderViewPosition);
//            this.mineHeaderViewPosition = -1;
//            if (mPresenter.isChatRoomEntered()) {
//                mPresenter.fetchRoomMembers();
//            }
//        }
//    }

    @Override
    public void onChatRoomMemberQueryFailed(int code, Throwable exception) {

    }

    @Override
    public void enterChatRoomSuccess() {
        Log.d(TAG, "live--- enterChatRoomSuccess");
        mPresenter.fetchRoomMembers(mProgramId);
    }

    @Override
    public void enterChatRoomFailed() {
//        ToastUtil.showInfo(this, R.string.live_disconnect_error_tip);
        showErrorLayout(ResUtil.getString(R.string.live_disconnect_error_tip), true);
        mComingLayout.setVisibility(View.GONE);
        mLiveNotStartLayout.setVisibility(View.GONE);
        mLiveFinishLayout.setVisibility(View.GONE);
        recyclerView.setVisibility(View.GONE);
        liveCuRankLayout.setVisibility(View.GONE);
        liveBottomAbility.setVisibility(View.GONE);
        livePendingLayout.setVisibility(View.GONE);
        mErrorLayout.setVisibility(View.GONE);
    }

    @Override
    public void onGiftsSuccess(GiftsResult giftsResult) {
//        giftPanelControl.setData(giftsResult.getGiftList());
        mGiftsResult = giftsResult;
        if (mGiftPannelDialog != null && mGiftPannelDialog.isVisible()) {
            mGiftPannelDialog.setData(giftsResult);
        }
    }

    private void cancelGiftPannelDialog() {
        if (mGiftPannelDialog == null) return;
        mGiftPannelDialog.dismissAllowingStateLoss();
        mGiftPannelDialog = null;
    }

    @Override
    public void onGiftsFailed() {

    }

    @Override
    public void onLoading() {
        if (loading != null) {
            loading.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void onLoadFinish() {
        if (loading != null) {
            loading.setVisibility(View.GONE);
        }
    }

    @Override
    public void onGiveGiftSuccess(GiftGivingResult giftGivingResult, Gift gift) {
//        ToastUtil.showInfo(this, giftGivingResult.getMsg());
        cancelGiftPannelDialog();
        ToastUtil.showInfo(this, "打赏成功");
//        showGiftToast(this, Toast.LENGTH_LONG, gift.getGiftImg(), "打赏成功");
    }

    private void showGiftToast(Context context, int showTime, String img, String msg) {
        Toast toast = Toast.makeText(context, msg, showTime);
        toast.setGravity(Gravity.CENTER, 0, 0);  //设置显示位置
        LinearLayout layout = (LinearLayout) toast.getView();
        layout.setBackground(getResources().getDrawable(R.drawable.common_toast_bg));
        ImageView image = new ImageView(this);
        int dimenW = ResUtil.getDimen(R.dimen.m100);
        int dimenTM = ResUtil.getDimen(R.dimen.m10);
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(dimenW, dimenW);
        params.gravity = Gravity.CENTER;
        params.topMargin = dimenTM;
        image.setLayoutParams(params);
        ImageLoader.getInstance().displayImage(context, img, image);
        layout.addView(image, 0);
        TextView v = (TextView) toast.getView().findViewById(android.R.id.message);
        v.setTextColor(Color.WHITE);     //设置字体颜色
        toast.show();
    }


    @Override
    public void onGiveGiftFailed(ApiException e) {
        cancelGiftPannelDialog();
        ToastUtil.showInfo(this, e.getMessage());
        Log.e(TAG, "onGiveGiftFailed: error code is " + e.getCode() + "; error msg is " + e.getMessage());
    }

    @Override
    public void onGoodsSuccess(GoodsResult goodsResult) {
//        goodsPanelControl.setData(goodsResult.getGoodsList());
//        goodsPannel.setData(goodsResult.getGoodsList());
        mGoodsResult = goodsResult;
        if (mGoodsPannelDialog != null) {
            mGoodsPannelDialog.setData(mGoodsResult);
        }
    }

    @Override
    public void onGoodsFailed() {

    }

    @Override
    public void networkChange(boolean hasNetwork) {
        if (hasNetwork) {
            onLoadFinish();
            // Log.i("111222333444", "2-4");
            initHomeLiveManager();
            if (mNetLayout != null) {
                mNetLayout.setVisibility(View.GONE);
            }
        } else {
            showErrorInfo(ErrorStatus.ERROR_NET);
        }
    }

    @Override
    public void onLoginStateChange(int cp, boolean isLogin) {
//        if (isLogin) {
//            if (mPresenter.isChatRoomEntered()) {
//                clearNimStatus();
////                    removeMineHeaderView();
//            }
//        } else {
//            initHomeLiveManager();
//        }
        clearNimStatus();
        // Log.i("111222333444", "2-5");
        initHomeLiveManager();
        if (isLogin) {
            reportLoginEvent();
        }
    }

    private void reportLoginEvent() {
        String type = UserInfoManager.getInstance().getLoginType();
        if (TextUtils.isEmpty(type)) {
            return;
        }
        LoginReportEvent event = new LoginReportEvent();
        event.setType(type);
        event.setRemarks1(LoginReportEvent.REMARKS1_LIVE_ROOM_MSG);
        ReportHelper.getInstance().addEvent(event);
    }

    @Override
    public void onCancel() {

    }

//    private class UserLoginComponent implements DynamicComponent {
//
//        @Override
//        public String getName() {
//            return "UserLoginComponent$ComprehensiveLiveActivity";
//        }
//
//        @Override
//        public boolean onCall(RealCaller caller) {
//            String actionName = caller.actionName();
//            if (UserStateObserverProcessorConst.USER_LOGIN.equals(actionName)) {
//                if (mPresenter.isChatRoomEntered()) {
//                    clearNimStatus();
////                    removeMineHeaderView();
//                }
//            }
//            if (UserStateObserverProcessorConst.USER_LOGOUT.equals(actionName) || UserStateObserverProcessorConst.USER_LOGIN.equals(actionName)) {
//                initHomeLiveManager();
//            }
//            return false;
//        }
//    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (ResUtil.getOrientation() == Configuration.ORIENTATION_LANDSCAPE) {
//            uploadView(true);
        } else {
//            uploadView(false);
        }
    }

    private void initViewInner() {
        int mCurrentOrientation = ResUtil.getOrientation();
        if (mCurrentOrientation == Configuration.ORIENTATION_PORTRAIT) {
//            uploadView(false);
        } else if (mCurrentOrientation == Configuration.ORIENTATION_LANDSCAPE) {
//            uploadView(true);
        }
    }

    private void stopLive() {
        if (PlayerManagerHelper.getInstance().isLivingPlayer()) {
            PlayerManagerHelper.getInstance().pause(false);
            PlayerManager.getInstance().stop(false);
            PlayerManager.getInstance().reset();
            HomeLiveManager.getInstance().onLiveExit(false);
//            exitChatRoom();
            bPlayerEnabled = false;
        }
    }

//    private void stopLoopLiveStatus() {
//        if (PlayerManagerHelper.getInstance().isLivingPlayer()) {
////            PlayerManager.getInstance().reset();
////            HomeLiveManager.getInstance().onLiveExit(true);
////            HomeLiveManager.getInstance().stopLoopLiveStatus();
//            exitChatRoom();
//            bPlayerEnabled = false;
//        }
//    }

//    @Override
//    public void onHiddenChanged(boolean hidden) {
//        super.onHiddenChanged(hidden);
//        if (!hidden) {
//            PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
//            if (playItem.getType() != PlayerConstants.RESOURCES_TYPE_LIVING ||
//                    playItem.getType() != PlayerConstants.RESOURCES_TYPE_BROADCAST) {
//                pop();
//            }
//        }
//    }


    @Override
    public boolean useEventBus() {
        return true;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void updateLivingProgramId(BusEventPlayingProgramIdChanged playingProgramIdChanged) {
//        this.mProgramId = playingProgramIdChanged.getProgramId();
    }


    /**
     * 更新消息列表
     *
     * @param list
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMessageEvent(ArrayList<MessageBean> list) {
        showChatMessageSent(list);
    }

    /**
     * 消息评论状态变更 - 例如：删除消息
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMessageEvent(LiveCommentStatusMsg liveCommentStatusMsg) {
        onChatMessageReceivedToRemove(liveCommentStatusMsg);
    }

    /**
     * 更新直播播放状态
     *
     * @param liveStatusMsg
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMessageEvent(LiveStatusMsg liveStatusMsg) {
        Log.e(TAG, "live--- onMessageEvent liveStatusMsg:" + liveStatusMsg);
        if (mProgramId <= 0) {
            return;
        }
        // Log.i("111222333444", "3-3");
        mPresenter.getLiveInfo(mProgramId);
    }

    /**
     * 更新排行榜
     *
     * @param giftRankMsg
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMessageEvent(GiftRankMsg giftRankMsg) {
        // NimManager#parseCustomMsg
        if (giftRankMsg == null || giftRankMsg.getRewardRanking() == null) {
            return;
        }
        this.rankUserList.clear();
        this.rankUserList = giftRankMsg.getRewardRanking();
        setLiveCuRankUI(this.listenerCount, this.rankUserList);
    }

    /**
     * 收到礼物打赏的消息
     *
     * @param giftMsg
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMessageEvent(GiftMsg giftMsg) {
        if (currentStart) {
            giftMsg.setHitCombo(1);
        }
        giftMsgControl.loadGift(giftMsg);
        Log.d(TAG, "onClick: " + giftMsgControl.getShowingGiftLayoutCount());
    }

    /**
     * 正在讲解的商品上下链接
     *
     * @param goodsCardMsg
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMessageEvent(GoodsCardMsg goodsCardMsg) {
        if (goodsCardMsg == null || goodsCardControl == null) {
            return;
        }
        if (BuildConfig.DEBUG) {
            Log.d(TAG, "goodsCardMsg.getPushType() is " + goodsCardMsg.getPushType());
        }
        if (goodsCardMsg.getPushType() == null) {
            return;
        }
        int position = -1;
        if (mGoodsResult != null && mGoodsResult.getGoodsList() != null) {
            for (int i = 0; i < mGoodsResult.getGoodsList().size(); i++) {
                if (goodsCardMsg.getId().equals(mGoodsResult.getGoodsList().get(i).getId())) {
                    position = i;
                }
            }

            if (position != -1) {
                mGoodsResult.getGoodsList().get(position).setPushType(goodsCardMsg.getPushType());
                if (mGoodsPannelDialog != null) {
                    mGoodsPannelDialog.setData(mGoodsResult);
                }
            }
        }

        if (goodsCardMsg.getPushType() == 1) {
            goodsCardControl.show(goodsCardMsg, position);
        } else {
            goodsCardControl.dismiss();
        }

    }

    /**
     * 显示在线人数和排行榜
     *
     * @param cu
     * @param list
     */
    private void setLiveCuRankUI(int cu, List<GiftRankUser> list) {
        if (liveCuRankLayout == null) {
            return;
        }
        if (isShowOnlineNum == 1 && (list != null && list.size() > 0)) {
            // 在线人数和排行榜同时显示
            liveCuRankLayout.setVisibility(View.VISIBLE);
            setCu(cu);
            setRank(list);
            liveCuRankDivider.setVisibility(View.VISIBLE);
        } else if (isShowOnlineNum == 1 && (list == null || list.size() < 1)) {
            // 只显示在线人数
            liveCuRankLayout.setVisibility(View.VISIBLE);
            setCu(cu);
            liveCuRankDivider.setVisibility(View.GONE);
        } else if (isShowOnlineNum != 1 && (list != null && list.size() > 0)) {
            // 只显示排行榜
            liveCuRankLayout.setVisibility(View.VISIBLE);
            setRank(list);
            liveCuRankDivider.setVisibility(View.GONE);
        } else {
            // 在线人数和排行榜都不显示
            liveCuRankLayout.setVisibility(View.GONE);
        }

    }

    // 显示在线人数
    private void setCu(int cu) {
        liveCu.setText("在线人数：" + formatNumberWithTenThoundText(cu));
        liveCu.setVisibility(View.VISIBLE);
    }

    // 显示排行榜
    private void setRank(List<GiftRankUser> list) {
        if (list.size() > 0) {
            liveRank.setVisibility(View.VISIBLE);
            ImageLoader.getInstance().displayCircleImage(this, list.get(0).getAvatar(), liveRank1);
            liveRank1Bg.setVisibility(View.VISIBLE);
        } else {
            liveRank.setVisibility(View.GONE);
            liveRank1Bg.setVisibility(View.GONE);
        }
        if (list.size() > 1) {
            ImageLoader.getInstance().displayCircleImage(this, list.get(1).getAvatar(), liveRank2);
            liveRank2Bg.setVisibility(View.VISIBLE);
        } else {
            liveRank2Bg.setVisibility(View.GONE);
        }
        if (list.size() > 2) {
            ImageLoader.getInstance().displayCircleImage(this, list.get(2).getAvatar(), liveRank3);
            liveRank3Bg.setVisibility(View.VISIBLE);
        } else {
            liveRank3Bg.setVisibility(View.GONE);
        }
    }

    /**
     * 更新消息小红点
     *
     * @param event
     */
    @Subscribe
    public void changeBadgeView(ChangeBadgeViewEvent event) {
        messageBadgeView.postDelayed(new Runnable() {
            @Override
            public void run() {
                MessageDaoManager.getInstance().queryMsgLook(new GreenDaoInterface.OnQueryListener<List<CrashMessageBean>>() {
                    @Override
                    public void onQuery(List<CrashMessageBean> crashMessageBeans) {
                        if (crashMessageBeans != null && crashMessageBeans.size() > 0) {
                            messageBadgeView.setImageResource(R.drawable.comprehensive_message_badge_view_red_point);
                        } else {
                            messageBadgeView.setImageResource(R.drawable.comprehensive_message_badge_view);
                        }
                    }
                });
            }
        }, 300);

    }

    /**
     * 本地调试用, 添加 [生成测试消息] 与 [删除测试消息] 按钮
     */
    private void generateTestButton() {
        if (!SWITCH_TEST_BUTTON) return;

        FrameLayout mContentView;
        try {
            mContentView = getWindow().getDecorView().findViewById(android.R.id.content);
        } catch (Exception e) {
            mContentView = null;
        }
        if (mContentView == null) {
            return;
        }
        LinearLayout ll = new LinearLayout(this);
        FrameLayout.LayoutParams llParams = new FrameLayout.LayoutParams(FrameLayout.LayoutParams.WRAP_CONTENT, FrameLayout.LayoutParams.WRAP_CONTENT);
        llParams.gravity = Gravity.BOTTOM | Gravity.END;
        ll.setLayoutParams(llParams);
        ll.setHorizontalGravity(LinearLayout.HORIZONTAL);

        for (int i = 0; i < 2; i++) {
            TextView tv = new TextView(this);
            LinearLayout.LayoutParams tvParams = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
            tvParams.bottomMargin = ResUtil.getDimen(R.dimen.m20);
            tvParams.rightMargin = ResUtil.getDimen(R.dimen.m20);
            tv.setLayoutParams(tvParams);
            tv.setTextSize(TypedValue.COMPLEX_UNIT_SP, 20);
            tv.setBackgroundColor(Color.GRAY);
            tv.setTextColor(Color.WHITE);
            if (i == 0) {
                tv.setText("生成测试消息");
                tv.setOnClickListener(v -> {
                    final int count = 30;
                    ArrayList<MessageBean> beanList = new ArrayList<>();
                    for (int index = 1; index <= count; index++) {
                        MessageBean messageBean = new MessageBean();
                        messageBean.id = String.valueOf(index);
                        messageBean.type = NimManager.MSG_TYPE_RECEIVE;
                        messageBean.account = "测试消息-" + index;
                        messageBean.contentString = "测试消息-" + index;
                        messageBean.chatTime = "测试消息-" + index;
                        messageBean.userIconUrl = "测试消息-" + index;
                        messageBean.nickName = "测试消息-" + index;
                        beanList.add(messageBean);
                    }
                    showChatMessageReceived(beanList);
                    if (SWITCH_TEST_BUTTON) {
                        ToastUtil.showError(null, "添加测试消息成功");
                    }
                });
            } else {
                tv.setText("删除测试消息");
                tv.setOnClickListener(v -> {
                    final int index = 15;
                    LiveCommentStatusMsg liveCommentStatusMsg = new LiveCommentStatusMsg();
                    liveCommentStatusMsg.setId(String.valueOf(index));
                    liveCommentStatusMsg.setContent("测试消息-" + index);
                    liveCommentStatusMsg.setStatus(LiveCommentStatusMsg.STATUS_REMOVE);
                    onChatMessageReceivedToRemove(liveCommentStatusMsg);
                });
            }
            ll.addView(tv);
        }

        mContentView.addView(ll);
    }

    //    @Override
//    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
//        if (requestCode == RecordPannelDialog.CODE_PERMISSION_REQUEST) {
//            if (permissions.length > 0) {
//                for (int i = 0; i < permissions.length; i++) {
//                    if (grantResults.length > 0 && grantResults[i] != PackageManager.PERMISSION_GRANTED) {
//                        if (ActivityCompat.shouldShowRequestPermissionRationale(ComprehensiveLiveActivity.this, permissions[i])) {
//                            ToastUtil.showInfo(ComprehensiveLiveActivity.this, "您未授予云听权限，因此无法使用录音功能");
//                        } else {
//                            permissionUtils.getDialog();
//                        }
//                        return;
//                    }
//                }
//            }
//        } else {
//            super.onRequestPermissionsResult(requestCode, permissions, grantResults);
//        }
//    }
}