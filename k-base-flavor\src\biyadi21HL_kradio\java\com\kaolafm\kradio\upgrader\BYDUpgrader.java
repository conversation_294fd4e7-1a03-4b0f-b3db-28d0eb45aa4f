package com.kaolafm.kradio.upgrader;

import android.app.Activity;
import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.dialog.DialogListener;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.uitl.DeviceUtil;
import com.kaolafm.kradio.upgrader.dialog.UpGraderDialog;
import com.kaolafm.kradio.upgrader.net.RequestConstants;
import com.kaolafm.kradio.upgrader.net.model.QueryVersionData;
import com.kaolafm.kradio.upgrader.net.model.UpdateData;

import io.reactivex.Observable;
import io.reactivex.Observer;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;


/**
 * <AUTHOR> on 2019/4/1.
 */

public class BYDUpgrader {
    private static final String TAG = "BYDUpgrader";
    public static int UPGRADESTYLE_ALERT = 1;//1：提示升级 2：强制升级 3:静默升级
    public static int UPGRADESTYLE_FORCE = 2;//1：提示升级 2：强制升级 3:静默升级
    public static int UPGRADESTYLE_SILEN = 3;//1：提示升级 2：强制升级 3:静默升级

    //检查升级
    public static void checkUp(QueryVersionData mQueryVersionData) {

        //判断code
//        Log.e("logx", "xxxx RequestCode =  " + mQueryVersionData.getCode());
        switch (mQueryVersionData.getCode()) {
            case RequestConstants.CODE_SUCCESS:
                break;
            case RequestConstants.CODE_REQUEST_ERROR:
                ToastUtil.showOnly(AppDelegate.getInstance().getContext(), AppDelegate.getInstance().getContext().getResources().getString(R.string.allready_lastest_version));
                return;
            case RequestConstants.CODE_REQUEST_OFTEN:
                ToastUtil.showOnly(AppDelegate.getInstance().getContext(), AppDelegate.getInstance().getContext().getResources().getString(R.string.allready_lastest_version));
                return;
            case RequestConstants.CODE_NOMES:
                ToastUtil.showOnly(AppDelegate.getInstance().getContext(), AppDelegate.getInstance().getContext().getResources().getString(R.string.allready_lastest_version));
                return;
            case RequestConstants.CODE_SIGN_ERROR:
                ToastUtil.showOnly(AppDelegate.getInstance().getContext(), AppDelegate.getInstance().getContext().getResources().getString(R.string.allready_lastest_version));
                return;
            case RequestConstants.CODE_SERVER_BUSY:
                ToastUtil.showOnly(AppDelegate.getInstance().getContext(), AppDelegate.getInstance().getContext().getResources().getString(R.string.allready_lastest_version));
                return;

        }
        //判断是否升级
        QueryVersionData.DataBean data = mQueryVersionData.getData();
//        Log.i(TAG, "xxxx 开始checkUp       data.getVersionCode() = " + data.getVersionCode() + "       DeviceUtil.getVersionCode(AppDelegate.getInstance().getContext()) = " + DeviceUtil.getVersionCode(AppDelegate.getInstance().getContext()));
//        Log.e("logx", "data = " + data);
        if (data != null && data.getVersionCode() > DeviceUtil.getVersionCode(AppDelegate.getInstance().getContext())) {
            Activity activity = AppManager.getInstance().getCurrentActivity();
            Log.i(TAG, "checkUp------>activity = " + activity);
            Log.i(TAG, "checkUp------>activityList = " + AppManager.getInstance().getActivityList());
            if (activity != null) {
                showVersionDialog(activity, mQueryVersionData);
            }
        } else {
            ToastUtil.showOnly(AppDelegate.getInstance().getContext(), AppDelegate.getInstance().getContext().getResources().getString(R.string.allready_lastest_version));
        }
    }


    /**
     * 显示新版信息对话框
     *
     * @param activity
     * @param queryVersionData
     */
    private static void showVersionDialog(final Activity activity, final QueryVersionData queryVersionData) {
        final boolean canclable = queryVersionData.getData().getUpgradeStyle() != BYDUpgrader.UPGRADESTYLE_FORCE;
        Log.i(TAG, "showVersionDialog------->canclable = " + canclable);
        String title;
        if (canclable) {
            String versionCode = String.valueOf(queryVersionData.getData().getVersionCode());
            String littleVersion = "." + versionCode.substring(versionCode.length() - 4, versionCode.length());
            title = String.format(activity.getString(R.string.check_lastest_version), queryVersionData.getData().getVersionName() + littleVersion);
        } else {
            if (TextUtils.isEmpty(queryVersionData.getData().getUpgradeContent())) {
                title = activity.getResources().getString(R.string.msg_default_force_upgrade);
            } else {
                title = queryVersionData.getData().getUpgradeContent();
            }
        }
        if (activity != null) {
            UpGraderDialog upGraderDialog = new UpGraderDialog(activity);
            upGraderDialog.setmCode(UpGraderDialog.UpGraderDialogAlert);
            upGraderDialog.setOnPositiveListener(dialog -> {
                upGraderDialog.setmCode(UpGraderDialog.UpGraderDialogProgress);
                upGraderDialog.show();
                UpdateData info = new UpdateData();
                info.setUpdateInfo(queryVersionData.getData().getUpgradeContent());
                info.setUpdateType(queryVersionData.getData().getUpgradeStyle());
                info.setUpdateUrl(queryVersionData.getData().getDownloadUrl());
                info.setUpdateVersion(queryVersionData.getData().getVersionName());
                info.setVersionCode(String.valueOf(queryVersionData.getData().getVersionCode()));
                UpgraderManager.getInstance().downApkFile(info, new UpgraderManager.DownloadApkListener() {
                    @Override
                    public void onStart() {
                        runUIThread(() -> {
                            upGraderDialog.setInfo(info);
                        });
                    }

                    @Override
                    public void onProgress(int p) {
//                        Log.d(TAG, "onProgress: p = " + p);
                        runUIThread(() -> upGraderDialog.upProgress(p));
                    }

                    @Override
                    public void onFinish(String path) {
                        Log.d(TAG, "onFinish: path = " + path);
                        runUIThread(() -> {
                            UpgraderManager.getInstance().installApk(path);
                            upGraderDialog.dismiss();
                        });

                    }

                    @Override
                    public void onError(String msg) {
                        Log.d(TAG, "onError: msg = " + msg);
                        runUIThread(() -> {
                            upGraderDialog.dismiss();
                            ToastUtil.showOnly(AppDelegate.getInstance().getContext(), AppDelegate.getInstance().getContext().getResources().getString(R.string.downloadapk_network_error));
                        });
                    }
                });
            }).setOnNativeListener(new DialogListener.OnNativeListener() {
                @Override
                public void onClick(Object dialog) {
                    if (canclable) {
                        upGraderDialog.dismiss();
                    } else {
                        upGraderDialog.dismiss();
                        /*退出应用*/
                        AppManager.getInstance().appExit();
                    }
                }
            }).show();
        }
    }


    /**
     * 线程切换
     *
     * @param mUiThread
     */
    public static void runUIThread(UiThread mUiThread) {
        Observable.fromArray(new String[]{})
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<String>() {
                    @Override
                    public void onSubscribe(Disposable d) {

                    }

                    @Override
                    public void onNext(String o) {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onComplete() {
                        mUiThread.onSuccess();
                    }
                });
    }

    public interface UiThread {
        void onSuccess();
    }
}
