package com.kaolafm.kradio.common.utils;

import android.annotation.TargetApi;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.BitmapShader;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.ColorFilter;
import android.graphics.LightingColorFilter;
import android.graphics.LinearGradient;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.Shader;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.graphics.drawable.LayerDrawable;
import android.graphics.drawable.ShapeDrawable;
import androidx.annotation.ColorInt;
import androidx.annotation.DrawableRes;
import androidx.annotation.FloatRange;
import androidx.annotation.Nullable;
import androidx.appcompat.content.res.AppCompatResources;
import android.view.View;
import android.widget.ImageView;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.ResUtil;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-04-13 15:29
 ******************************************/

public final class BitmapUtils {
    private BitmapUtils() {
    }

    /**
     * 创建倒影bitmap
     *
     * @param originalImage 原始Bitmap
     * @param reflect       倒影占原始Bitmap的高度比列
     * @return
     */
    public static Bitmap createReflectionBitmap(Bitmap originalImage, float reflect, int startReflectColor, int endReflectColor) {
        int mWidth = originalImage.getWidth();
        int mHeight = originalImage.getHeight();
        int fHeight = (int) (mHeight * reflect);
        //原图和倒影图之间的缝隙
        int gap = 0;
        Matrix matrix = new Matrix();
        matrix.preScale(1, -1);
        Bitmap reflection;
        Bitmap background;
        try {
            reflection = Bitmap.createBitmap(originalImage, 0, mHeight - fHeight,
                    mWidth, fHeight, matrix, false);
            background = Bitmap.createBitmap(mWidth, mHeight + gap + fHeight, Bitmap.Config.ARGB_8888);
        } catch (OutOfMemoryError oom) {
            oom.printStackTrace();
            return originalImage;
        }
        //画出渐变颜色
        int bHeight = background.getHeight();

        Canvas canvas = new Canvas(background);
        Paint p1 = new Paint();
        p1.setAntiAlias(true);
        //画出原图
        canvas.drawBitmap(originalImage, 0, 0, p1);
        //画出间隙
        Paint gapPaint = new Paint();
        Shader gapShader = new LinearGradient(0, mWidth, mHeight, mHeight + gap,
                new int[]{Color.parseColor("#4D000000"),
                        Color.parseColor("#00000000")}, null, Shader.TileMode.CLAMP);
        gapPaint.setAntiAlias(true);
        gapPaint.setShader(gapShader);
        canvas.drawRect(0, mHeight, mWidth, mHeight + gap, gapPaint);
        //画出倒影图
        canvas.drawBitmap(reflection, 0, mHeight + gap, p1);
        Paint shaderPaint = new Paint();
        shaderPaint.setAntiAlias(true);
        if (startReflectColor != 0 || endReflectColor != 0) {
//            LinearGradient shader = new LinearGradient(0, mHeight, 0,
//                    reflection.getHeight(), startReflectColor, endReflectColor, Shader.TileMode.MIRROR);
            Shader shader = new LinearGradient(0, bHeight - fHeight, 0, bHeight, new int[]{startReflectColor,
                    endReflectColor}, new float[]{0.0F, 0.8F}, Shader.TileMode.REPEAT);
            shaderPaint.setShader(shader);
            shaderPaint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.DST_IN));
        }
        canvas.drawRect(0, bHeight - fHeight, mWidth, bHeight, shaderPaint);
        return background;
    }

    /**
     * 创建倒影bitmap不带原图
     *
     * @param originalImage 原始Bitmap
     * @return
     */
    public static Bitmap createReflectionBitmapBrand(Bitmap originalImage) {
        int mWidth = originalImage.getWidth();
        int mHeight = originalImage.getHeight();
        int fHeight = (int) (mHeight * 0.130F);
        //原图和倒影图之间的缝隙
        int gap = 0;
        Matrix matrix = new Matrix();
        matrix.preScale(1, -1);
        Bitmap reflection;
//        Bitmap background;
        try {
            reflection = Bitmap.createBitmap(originalImage, 0, mHeight - fHeight,
                    mWidth, fHeight, matrix, false);
//            background = Bitmap.createBitmap(mWidth, mHeight + gap + fHeight, Bitmap.Config.ARGB_8888);
        } catch (OutOfMemoryError oom) {
            oom.printStackTrace();
            return originalImage;
        }
        //画出渐变颜色
        int bHeight = reflection.getHeight();

        Canvas canvas = new Canvas(reflection);
        Paint p1 = new Paint();
        p1.setAntiAlias(true);
        //画出原图
//        canvas.drawBitmap(originalImage, 0, 0, p1);
        //画出间隙
//        Paint gapPaint = new Paint();
//        Shader gapShader = new LinearGradient(0, mWidth, mHeight, mHeight + gap,
//                new int[]{Color.parseColor("#4D000000"),
//                        Color.parseColor("#00000000")}, null, Shader.TileMode.CLAMP);
//        gapPaint.setAntiAlias(true);
//        gapPaint.setShader(gapShader);
//        canvas.drawRect(0, mHeight, mWidth, mHeight + gap, gapPaint);
        //画出倒影图
        canvas.drawBitmap(reflection, 0, mHeight + gap, p1);
        Paint shaderPaint = new Paint();
        shaderPaint.setAntiAlias(true);
        if (ResUtil.getColor(R.color.color_cover_reflection_start) != 0
                || ResUtil.getColor(R.color.color_cover_reflection_end) != 0) {
//            LinearGradient shader = new LinearGradient(0, mHeight, 0,
//                    reflection.getHeight(), startReflectColor, endReflectColor, Shader.TileMode.MIRROR);
            Shader shader = new LinearGradient(0, bHeight - fHeight, 0, bHeight
                    , new int[]{ResUtil.getColor(R.color.color_cover_reflection_start),
                    ResUtil.getColor(R.color.color_cover_reflection_end)}, new float[]{0.0F, 0.8F}, Shader.TileMode.REPEAT);
            shaderPaint.setShader(shader);
            shaderPaint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.DST_IN));
        }
        canvas.drawRect(0, bHeight - fHeight, mWidth, bHeight, shaderPaint);
        return reflection;
    }


    /**
     * ScalingLogic defines how scaling should be carried out if source and
     * destination image has different aspect ratio.
     * <p/>
     * CROP: Scales the image the minimum amount while making sure that at least
     * one of the two dimensions fit inside the requested destination area.
     * Parts of the source image will be cropped to realize this.
     * <p/>
     * FIT: Scales the image the minimum amount while making sure both
     * dimensions fit inside the requested destination area. The resulting
     * destination dimensions might be adjusted to a smaller size than
     * requested.
     */
    public enum ScalingLogic {
        CROP, FIT, TOP
    }

    /**
     * Utility function for creating a scaled version of an existing bitmap
     *
     * @param unscaledBitmap Bitmap to scale
     * @param dstWidth       Wanted width of destination bitmap
     * @param dstHeight      Wanted height of destination bitmap
     * @param scalingLogic   Logic to use to avoid image stretching
     * @return New scaled bitmap object
     */
    public static Bitmap createScaledBitmapRGB565(Bitmap unscaledBitmap, int dstWidth, int dstHeight,
                                                  ScalingLogic scalingLogic) {
        Bitmap bitmap = createScaledBitmapByConfig(unscaledBitmap, dstWidth, dstHeight, scalingLogic, Bitmap.Config.RGB_565);
        return bitmap;
    }


    private static Bitmap createScaledBitmapByConfig(Bitmap unscaledBitmap, int dstWidth, int dstHeight,
                                                     ScalingLogic scalingLogic, Bitmap.Config config) {
        if (unscaledBitmap == null || unscaledBitmap.isRecycled()) {
            return null;
        }
        int width = unscaledBitmap.getWidth();
        int height = unscaledBitmap.getHeight();
        if (width == 0 || height == 0 || dstHeight == 0 || dstWidth == 0) {
            return unscaledBitmap;
        }
        Rect srcRect = calculateSrcRect(width, height,
                dstWidth, dstHeight, scalingLogic);
        Rect dstRect = calculateDstRect(width, height,
                dstWidth, dstHeight, scalingLogic);
        Bitmap scaledBitmap;
        int dstW = dstRect.width();
        int dstH = dstRect.height();
        if (dstH == 0 || dstW == 0) {
            return unscaledBitmap;
        }
        try {
            // 如果需要支持ARGB_8888可以提供另外一个函数实现或自行加参(RGB_565相交于ARGB_8888会节省1倍内存空间)
            scaledBitmap = Bitmap.createBitmap(dstW, dstH, config);
            Canvas canvas = new Canvas(scaledBitmap);
            canvas.drawBitmap(unscaledBitmap, srcRect, dstRect, new Paint(Paint.FILTER_BITMAP_FLAG));
        } catch (OutOfMemoryError oom) {
            oom.printStackTrace();
            return unscaledBitmap;
        } catch (Throwable t) {
            t.printStackTrace();
            return unscaledBitmap;
        }

        return scaledBitmap;
    }

    /**
     * Calculates source rectangle for scaling bitmap
     *
     * @param srcWidth     Width of source image
     * @param srcHeight    Height of source image
     * @param dstWidth     Width of destination area
     * @param dstHeight    Height of destination area
     * @param scalingLogic Logic to use to avoid image stretching
     * @return Optimal source rectangle
     */
    public static Rect calculateSrcRect(int srcWidth, int srcHeight, int dstWidth, int dstHeight,
                                        ScalingLogic scalingLogic) {
        if (scalingLogic == ScalingLogic.CROP) {
            final float srcAspect = (float) srcWidth / (float) srcHeight;
            final float dstAspect = (float) dstWidth / (float) dstHeight;

            if (srcAspect > dstAspect) {
                final int srcRectWidth = (int) (srcHeight * dstAspect);
                final int srcRectLeft = (srcWidth - srcRectWidth) / 2;
                return new Rect(srcRectLeft, 0, srcRectLeft + srcRectWidth, srcHeight);
            } else {
                final int srcRectHeight = (int) (srcWidth / dstAspect);
                final int scrRectTop = (int) (srcHeight - srcRectHeight) / 2;
                return new Rect(0, scrRectTop, srcWidth, scrRectTop + srcRectHeight);
            }
        } else if (scalingLogic == ScalingLogic.TOP) {
            final float srcAspect = (float) srcWidth / (float) srcHeight;
            final float dstAspect = (float) dstWidth / (float) dstHeight;

            if (srcAspect > dstAspect) {
                final int srcRectWidth = (int) (srcHeight * dstAspect);
                final int srcRectLeft = (srcWidth - srcRectWidth) / 2;
                return new Rect(srcRectLeft, 0, srcRectLeft + srcRectWidth, srcHeight);
            } else {
                final int srcRectHeight = (int) (srcWidth / dstAspect);
                final int scrRectTop = (int) (srcHeight - srcRectHeight);
                return new Rect(0, scrRectTop, srcWidth, scrRectTop + srcRectHeight);
            }
        } else {
            return new Rect(0, 0, srcWidth, srcHeight);
        }
    }

    /**
     * Calculates destination rectangle for scaling bitmap
     *
     * @param srcWidth     Width of source image
     * @param srcHeight    Height of source image
     * @param dstWidth     Width of destination area
     * @param dstHeight    Height of destination area
     * @param scalingLogic Logic to use to avoid image stretching
     * @return Optimal destination rectangle
     */
    public static Rect calculateDstRect(int srcWidth, int srcHeight, int dstWidth, int dstHeight,
                                        ScalingLogic scalingLogic) {
        if (scalingLogic == ScalingLogic.FIT) {
            final float srcAspect = (float) srcWidth / (float) srcHeight;
            final float dstAspect = (float) dstWidth / (float) dstHeight;

            if (srcAspect > dstAspect) {
                return new Rect(0, 0, dstWidth, (int) (dstWidth / srcAspect));
            } else {
                return new Rect(0, 0, (int) (dstHeight * srcAspect), dstHeight);
            }
        } else if (scalingLogic == ScalingLogic.TOP) {
            final float srcAspect = (float) srcWidth / (float) srcHeight;
            final float dstAspect = (float) dstWidth / (float) dstHeight;

            if (srcAspect > dstAspect) {
                return new Rect(0, 0, dstWidth, (int) (dstWidth / srcAspect));
            } else {
                return new Rect(0, 0, (int) (dstHeight * srcAspect), dstHeight);
            }
        } else {
            return new Rect(0, 0, dstWidth, dstHeight);
        }
    }

    public static Bitmap setBitmapRoundSize(Bitmap bm, int newWidth, int newHeight, int r) {
        if (bm == null) {
            return null;
        }

        // 获得图片的宽高.
        int width = bm.getWidth();
        int height = bm.getHeight();
        // 计算缩放比例.
        float scaleWidth = ((float) newWidth) / width;
        float scaleHeight = ((float) newHeight) / height;
        // 取得想要缩放的matrix参数.
        Matrix matrix = new Matrix();
        matrix.postScale(scaleWidth, scaleHeight);
        // 初始化绘制纹理图
        BitmapShader bitmapShader = new BitmapShader(bm, Shader.TileMode.CLAMP, Shader.TileMode.CLAMP);
        // 根据控件大小对纹理图进行拉伸缩放处理
        bitmapShader.setLocalMatrix(matrix);

        // 初始化目标bitmap
        Bitmap targetBitmap = Bitmap.createBitmap(newWidth, newHeight, Bitmap.Config.ARGB_8888);

        // 初始化目标画布
        Canvas targetCanvas = new Canvas(targetBitmap);

        // 初始化画笔
        Paint paint = new Paint();
        paint.setAntiAlias(true);
        paint.setShader(bitmapShader);
        RectF rectF = new RectF(0, 0, newWidth, newHeight);
        // 利用画笔将纹理图绘制到画布上面
        targetCanvas.drawRoundRect(rectF, r, r, paint);

        targetCanvas.drawRect(0, rectF.bottom - r, r, rectF.bottom, paint);

        targetCanvas.drawRect(rectF.right - r, rectF.bottom - r, rectF.right, rectF.bottom, paint);

        return targetBitmap;
    }

    //节省每次创建时产生的开销，但要注意多线程操作synchronized
    private static final Canvas sCanvas = new Canvas();

    /**
     * 从一个view创建Bitmap。
     * 注意点：绘制之前要清掉 View 的焦点，因为焦点可能会改变一个 View 的 UI 状态。
     * 来源：https://github.com/tyrantgit/ExplosionField
     *
     * @param view  传入一个 View，会获取这个 View 的内容创建 Bitmap。
     * @param scale 缩放比例，对创建的 Bitmap 进行缩放，数值支持从 0 到 1。
     */
    public static Bitmap createBitmapFromView(View view, @FloatRange(from = 0.0f, to = 1.0f) float scale) {
        if (view instanceof ImageView) {
            Drawable drawable = ((ImageView) view).getDrawable();
            if (drawable != null && drawable instanceof BitmapDrawable) {
                return ((BitmapDrawable) drawable).getBitmap();
            }
        }
        view.clearFocus();
        Bitmap bitmap = createBitmapSafely((int) (view.getWidth() * scale),
                (int) (view.getHeight() * scale), Bitmap.Config.ARGB_8888, 1);
        if (bitmap != null) {
            synchronized (sCanvas) {
                Canvas canvas = sCanvas;
                canvas.setBitmap(bitmap);
                canvas.save();
                canvas.drawColor(Color.WHITE); // 防止 View 上面有些区域空白导致最终 Bitmap 上有些区域变黑
                canvas.scale(scale, scale);
                view.draw(canvas);
                canvas.restore();
                canvas.setBitmap(null);
            }
        }
        return bitmap;
    }

    /**
     * 从一个view创建Bitmap 透明背景。
     * 注意点：绘制之前要清掉 View 的焦点，因为焦点可能会改变一个 View 的 UI 状态。
     * 来源：https://github.com/tyrantgit/ExplosionField
     *
     * @param view  传入一个 View，会获取这个 View 的内容创建 Bitmap。
     * @param scale 缩放比例，对创建的 Bitmap 进行缩放，数值支持从 0 到 1。
     */
    public static Bitmap createBitmapFromViewNotBg(View view, @FloatRange(from = 0.0f, to = 1.0f) float scale) {
        if (view instanceof ImageView) {
            Drawable drawable = ((ImageView) view).getDrawable();
            if (drawable != null && drawable instanceof BitmapDrawable) {
                return ((BitmapDrawable) drawable).getBitmap();
            }
        }
        view.clearFocus();
        Bitmap bitmap = createBitmapSafely((int) (view.getWidth() * scale),
                (int) (view.getHeight() * scale), Bitmap.Config.ARGB_8888, 1);
        if (bitmap != null) {
            synchronized (sCanvas) {
                Canvas canvas = sCanvas;
                canvas.setBitmap(bitmap);
                canvas.save();
                canvas.drawColor(Color.TRANSPARENT);
                canvas.scale(scale, scale);
                view.draw(canvas);
                canvas.restore();
                canvas.setBitmap(null);
            }
        }
        return bitmap;
    }

    public static Bitmap createBitmapFromView(View view) {
        return createBitmapFromView(view, 1f);
    }

    /**
     * 从一个view创建Bitmap。把view的区域截掉leftCrop/topCrop/rightCrop/bottomCrop
     */
    public static Bitmap createBitmapFromView(View view, int leftCrop, int topCrop, int rightCrop, int bottomCrop) {
        Bitmap originBitmap = createBitmapFromView(view);
        if (originBitmap == null) {
            return null;
        }
        Bitmap cutBitmap = createBitmapSafely(view.getWidth() - rightCrop - leftCrop, view.getHeight() - topCrop - bottomCrop, Bitmap.Config.ARGB_8888, 1);
        if (cutBitmap == null) {
            return null;
        }
        Canvas canvas = new Canvas(cutBitmap);
        Rect src = new Rect(leftCrop, topCrop, view.getWidth() - rightCrop, view.getHeight() - bottomCrop);
        Rect dest = new Rect(0, 0, view.getWidth() - rightCrop - leftCrop, view.getHeight() - topCrop - bottomCrop);
        canvas.drawColor(Color.WHITE); // 防止 View 上面有些区域空白导致最终 Bitmap 上有些区域变黑
        canvas.drawBitmap(originBitmap, src, dest, null);
        originBitmap.recycle();
        return cutBitmap;
    }

    /**
     * 安全的创建bitmap。
     * 如果新建 Bitmap 时产生了 OOM，可以主动进行一次 GC - System.gc()，然后再次尝试创建。
     *
     * @param width      Bitmap 宽度。
     * @param height     Bitmap 高度。
     * @param config     传入一个 Bitmap.Config。
     * @param retryCount 创建 Bitmap 时产生 OOM 后，主动重试的次数。
     * @return 返回创建的 Bitmap。
     */
    public static Bitmap createBitmapSafely(int width, int height, Bitmap.Config config, int retryCount) {
        if (width == 0 || height == 0) {
            return null;
        }
        try {
            return Bitmap.createBitmap(width, height, config);
        } catch (OutOfMemoryError e) {
            e.printStackTrace();
            if (retryCount > 0) {
                System.gc();
                return createBitmapSafely(width, height, config, retryCount - 1);
            }
            return null;
        }
    }

    /**
     * 创建一张指定大小的纯色图片，支持圆角
     *
     * @param resources    Resources对象，用于创建BitmapDrawable
     * @param width        图片的宽度
     * @param height       图片的高度
     * @param cornerRadius 图片的圆角，不需要则传0
     * @param filledColor  图片的填充色
     * @return 指定大小的纯色图片
     */
    public static BitmapDrawable createDrawableWithSize(Resources resources, int width, int height, int cornerRadius, @ColorInt int filledColor) {
        Bitmap output = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(output);

        if (filledColor == 0) {
            filledColor = Color.TRANSPARENT;
        }

        if (cornerRadius > 0) {
            Paint paint = new Paint();
            paint.setAntiAlias(true);
            paint.setStyle(Paint.Style.FILL);
            paint.setColor(filledColor);
            canvas.drawRoundRect(new RectF(0, 0, width, height), cornerRadius, cornerRadius, paint);
        } else {
            canvas.drawColor(filledColor);
        }
        return new BitmapDrawable(resources, output);
    }

    /**
     * 设置Drawable的颜色
     * <b>这里不对Drawable进行mutate()，会影响到所有用到这个Drawable的地方，如果要避免，请先自行mutate()</b>
     */
    public static ColorFilter setDrawableTintColor(Drawable drawable, @ColorInt int tintColor) {
        LightingColorFilter colorFilter = new LightingColorFilter(Color.argb(255, 0, 0, 0), tintColor);
        drawable.setColorFilter(colorFilter);
        return colorFilter;
    }

    /**
     * 由一个drawable生成bitmap
     */
    public static Bitmap drawableToBitmap(Drawable drawable) {
        if (drawable == null)
            return null;
        else if (drawable instanceof BitmapDrawable) {
            return ((BitmapDrawable) drawable).getBitmap();
        }

        int intrinsicWidth = drawable.getIntrinsicWidth();
        int intrinsicHeight = drawable.getIntrinsicHeight();

        if (!(intrinsicWidth > 0 && intrinsicHeight > 0))
            return null;

        try {
            Bitmap bitmap = Bitmap.createBitmap(intrinsicWidth, intrinsicHeight, Bitmap.Config.ARGB_8888);
            Canvas canvas = new Canvas(bitmap);
            drawable.setBounds(0, 0, canvas.getWidth(), canvas.getHeight());
            drawable.draw(canvas);
            return bitmap;
        } catch (OutOfMemoryError e) {
            return null;
        }
    }

    /**
     * 创建一张渐变图片，支持韵脚。
     *
     * @param startColor 渐变开始色
     * @param endColor   渐变结束色
     * @param radius     圆角大小
     * @param centerX    渐变中心点 X 轴坐标
     * @param centerY    渐变中心点 Y 轴坐标
     * @return 返回所创建的渐变图片。
     */
    @TargetApi(16)
    public static GradientDrawable createCircleGradientDrawable(@ColorInt int startColor,
                                                                @ColorInt int endColor, int radius,
                                                                @FloatRange(from = 0f, to = 1f) float centerX,
                                                                @FloatRange(from = 0f, to = 1f) float centerY) {
        GradientDrawable gradientDrawable = new GradientDrawable();
        gradientDrawable.setColors(new int[]{
                startColor,
                endColor
        });
        gradientDrawable.setGradientType(GradientDrawable.RADIAL_GRADIENT);
        gradientDrawable.setGradientRadius(radius);
        gradientDrawable.setGradientCenter(centerX, centerY);
        return gradientDrawable;
    }


    /**
     * 动态创建带上分隔线或下分隔线的Drawable。
     *
     * @param separatorColor 分割线颜色。
     * @param bgColor        Drawable 的背景色。
     * @param top            true 则分割线为上分割线，false 则为下分割线。
     * @return 返回所创建的 Drawable。
     */
    public static LayerDrawable createItemSeparatorBg(@ColorInt int separatorColor, @ColorInt int bgColor, int separatorHeight, boolean top) {

        ShapeDrawable separator = new ShapeDrawable();
        separator.getPaint().setStyle(Paint.Style.FILL);
        separator.getPaint().setColor(separatorColor);

        ShapeDrawable bg = new ShapeDrawable();
        bg.getPaint().setStyle(Paint.Style.FILL);
        bg.getPaint().setColor(bgColor);

        Drawable[] layers = {separator, bg};
        LayerDrawable layerDrawable = new LayerDrawable(layers);

        layerDrawable.setLayerInset(1, 0, top ? separatorHeight : 0, 0, top ? 0 : separatorHeight);
        return layerDrawable;
    }


    /////////////// VectorDrawable /////////////////////

    public static
    @Nullable
    Drawable getVectorDrawable(Context context, @DrawableRes int resVector) {
        try {
            return AppCompatResources.getDrawable(context, resVector);
        } catch (Exception e) {
            return null;
        }
    }

    public static Bitmap vectorDrawableToBitmap(Context context, @DrawableRes int resVector) {
        Drawable drawable = getVectorDrawable(context, resVector);
        if (drawable != null) {
            Bitmap b = Bitmap.createBitmap(drawable.getIntrinsicWidth(), drawable.getIntrinsicHeight(), Bitmap.Config.ARGB_8888);
            Canvas c = new Canvas(b);
            drawable.setBounds(0, 0, c.getWidth(), c.getHeight());
            drawable.draw(c);
            return b;
        }
        return null;
    }

    /////////////// VectorDrawable /////////////////////

    //将bitmap保存为本地文件
    public static String saveBitmap(Context context, Bitmap bitmap, String bitmapFileName) {
        File avaterFile = new File(context.getDir("image", Context.MODE_PRIVATE), bitmapFileName);//设置文件名称
        if (avaterFile.exists()) {
            avaterFile.delete();
        }

        try {
            avaterFile.createNewFile();
            FileOutputStream fos = new FileOutputStream(avaterFile);
            bitmap.compress(Bitmap.CompressFormat.JPEG, 10, fos);
            fos.flush();
            fos.close();
        } catch (IOException e) {
            e.printStackTrace();
        }

        return avaterFile.getAbsolutePath();
    }

}
