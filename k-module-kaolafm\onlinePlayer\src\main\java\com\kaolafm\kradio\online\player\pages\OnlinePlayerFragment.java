package com.kaolafm.kradio.online.player.pages;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.os.Bundle;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager.widget.ViewPager;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.bumptech.glide.Glide;
import com.kaolafm.ad.expose.AdvertisingImager;
import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.ads.image.AdvertisingImagerImpl;
import com.kaolafm.kradio.common.bean.BroadcastRadioDetailData;
import com.kaolafm.kradio.common.widget.ring.Ring;
import com.kaolafm.kradio.k_kaolafm.R; 
import com.kaolafm.kradio.lib.base.mvp.IPresenter;
import com.kaolafm.kradio.lib.sdk.utils.PerformanceSettingMananger;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.online.common.base.MBaseShowHideFragment;
import com.kaolafm.kradio.online.common.event.OnlinePlayerFragmentJumpActionEvent;
import com.kaolafm.kradio.online.common.playbar.OnlinePlayerBar;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.router.IRouterConsumer;
import com.kaolafm.kradio.online.player.adapters.PlayListFragmentPagerAdapter;
import com.kaolafm.kradio.online.player.models.BusEventPlayingLiving;
import com.kaolafm.kradio.online.player.mvp.BroadcastDetailPresenter;
import com.kaolafm.kradio.online.player.mvp.BroadcastDetailView;
import com.kaolafm.kradio.online.player.utils.NGGuidePageTransformer;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayerStateListener;
import com.kaolafm.opensdk.player.logic.model.item.LivePlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
 
import kotlin.jvm.functions.Function0;

@Route(path = RouterConstance.ONLINE_URL_PLAY)
public class OnlinePlayerFragment extends MBaseShowHideFragment<IPresenter> implements ViewPager.OnPageChangeListener, BroadcastDetailView, IRouterConsumer {
    public static final String PARAM_CENTER_X = "centerX";
    public static final String PARAM_PLAYERBAR_WIDTH = "playerbarWidth";
    public static final String PARAM_RING_WIDTH = "ringWidth";
    public static final String PARAM_RING_HEIGHT = "ringHeight";
    public static final String PARAM_RING_MARGIN_BOTTOM = "ringMarginBottom";
    public static final String PARAM_SHOW_ANIMATION = "showAnimation";
    private static final String TAG = OnlinePlayerFragment.class.getSimpleName();

    private int centerX, playerBarWidth;
    private int ringWidth, ringHeight, ringMarginBottom;
    private boolean isShowAnimation;

    public static final String SHARED_VIEW_FLAG_PLAYER = "SHARED_VIEW_PLAYER";
    public static final String SHARED_VIEW_FLAG_RING = "SHARED_VIEW_RING";
    public static final String SHARED_VIEW_FLAG_ROAD = "SHARED_VIEW_ROAD";
    /**
     * 直播的节目Id
     */
    public static final String BUNDLE_LIVE_PROGRAM_ID = "BUNDLE_LIVE_PROGRAM_ID";
    public static final String BUNDLE_NEED_SET_POSITION = "BUNDLE_NEED_SET_POSITION";

 
    View backBt; 
    ConstraintLayout rootView; 
    ViewPager mViewPager; 

    private boolean viewStubIsPlayerBar = true;

    OnlinePlayerBar playerRoot;
    ImageView roadIv;
    Ring mRing;

    ImageView hostPic;
    TextView hostNameTv;
    TextView liveTitleTv;

    private List<View> mRightViews = new ArrayList<View>();

    private List<Fragment> mFragments = new ArrayList<Fragment>();
    private NGGuidePageTransformer ngGuidePageTransformer;

    private PlayListFragmentPagerAdapter adapter;
    private long mLiveProgramId;    //直播节目的节目id，只有playItem是LivePlayItem时有效
    private boolean isRunningAnimation;//正在执行动画
    private IPlayerStateListener mPlayerListener = new BasePlayStateListener() {
        @Override
        public void onPlayerPreparing(PlayItem playItem) {
            super.onPlayerPreparing(playItem);
            if (mRing != null) mRing.refreshData();
        }
    };


    public OnlinePlayerFragment() {
        // Required empty public constructor
    }


    public static OnlinePlayerFragment newInstance(boolean isShowAnimation, int playerBarWidth, int ringWidth, int ringHeight, int ringMArginBottom, int centerX) {
        OnlinePlayerFragment fragment = new OnlinePlayerFragment();
        Bundle args = new Bundle();
        args.putInt(PARAM_CENTER_X, centerX);
        args.putInt(PARAM_PLAYERBAR_WIDTH, playerBarWidth);
        args.putInt(PARAM_RING_HEIGHT, ringHeight);
        args.putInt(PARAM_RING_WIDTH, ringWidth);
        args.putInt(PARAM_RING_MARGIN_BOTTOM, ringMArginBottom);
        args.putBoolean(PARAM_SHOW_ANIMATION, isShowAnimation);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void initArgs() {
        super.initArgs();
        if (getArguments() != null) {
            Bundle arguments = getArguments();
            isShowAnimation = arguments.getBoolean(PARAM_SHOW_ANIMATION);
            centerX = arguments.getInt(PARAM_CENTER_X);
            playerBarWidth = arguments.getInt(PARAM_PLAYERBAR_WIDTH);
            ringHeight = arguments.getInt(PARAM_RING_HEIGHT);
            ringWidth = arguments.getInt(PARAM_RING_WIDTH);
            ringMarginBottom = arguments.getInt(PARAM_RING_MARGIN_BOTTOM);
            if (arguments.containsKey(BUNDLE_LIVE_PROGRAM_ID))
                mLiveProgramId = arguments.getLong(BUNDLE_LIVE_PROGRAM_ID, 0);
        }
    }

    @Override
    protected int getLayoutId() {
        return R.layout.online_player_activity_play;
    }

    @Override
    protected IPresenter createPresenter() {
        return new BroadcastDetailPresenter(this);
    }

    @Override
    public void initView(View view) {

        backBt=view.findViewById(R.id.backBt);
        rootView=view.findViewById(R.id.rootView);
        mViewPager=view.findViewById(R.id.viewPager);
       
        PlayerManagerHelper.getInstance().setInProgramPlayerPage(true);

        PerformanceSettingMananger.getInstance().setIsNeedAnimation(true);

        refreshViews(isShowAnimation);

        backBt.setOnClickListener(this::backPressed);

        PlayerManager.getInstance().addPlayControlStateCallback(mPlayerListener);
    }

    /**
     * 刷新右侧播放器或者直播信息组件、左侧fragment
     */
    private void refreshViews(boolean isShowAnimation) {
        int layoutId = 0;

        Fragment childFragment = null;
        switch (PlayerManagerHelper.getInstance().getCurrentPlayType()) {
            case PlayerConstants.RESOURCES_TYPE_BROADCAST:
            case PlayerConstants.RESOURCES_TYPE_TV:
                childFragment = OnlineBroadcastPlayListFragment.newInstance("广播/听电视详情页");
                if (mFragments.size() > 0) {
                    mFragments.set(0, OnlineBroadcastPlayListFragment.newInstance("广播/听电视详情页"));
                } else {
                    mFragments.add(OnlineBroadcastPlayListFragment.newInstance("广播/听电视详情页"));
                }
                layoutId = R.layout.online_player_viewstub_playerbar;
                viewStubIsPlayerBar = true;
                resetAdImageShowParam(true);
                break;
            case PlayerConstants.RESOURCES_TYPE_ALBUM:
            case PlayerConstants.RESOURCES_TYPE_RADIO:
                layoutId = R.layout.online_player_viewstub_playerbar;
                viewStubIsPlayerBar = true;
                childFragment = OnlineAlbumPlayListRebuildFragment.newInstance("专辑/AI电台详情页");
                if (mFragments.size() > 0) {
                    mFragments.set(0, OnlineAlbumPlayListRebuildFragment.newInstance("专辑/AI电台详情页"));
                } else {
                    mFragments.add(OnlineAlbumPlayListRebuildFragment.newInstance("专辑/AI电台详情页"));
                }
                resetAdImageShowParam(true);
                break;
            case PlayerConstants.RESOURCES_TYPE_LIVING:
                layoutId = R.layout.online_player_viewstub_host_info;
                rootView.setBackgroundResource(R.drawable.online_page_bg_vague);
                viewStubIsPlayerBar = false;
                PlayItem curPlayItem = PlayerManager.getInstance().getCurPlayItem();
                childFragment = LiveBarrageFragment.newInstance(((LivePlayItem) curPlayItem).getLiveId(), "直播详情页");
                resetAdImageShowParam(false);
                break;
        }
        if (layoutId != 0) {
            LayoutInflater.from(getContext()).inflate(layoutId, rootView);
        }

        if (mFragments.size() == 0) {
            mFragments.add(childFragment);
        } else {
            mFragments.set(0, childFragment);
        }

        if (adapter == null) {
            ngGuidePageTransformer = new NGGuidePageTransformer();
            ngGuidePageTransformer.setCurrentItem(getContext(), 0, mFragments);
            mViewPager.setPageTransformer(true, ngGuidePageTransformer);
            adapter = new PlayListFragmentPagerAdapter(getChildFragmentManager(), mFragments);
            mViewPager.setAdapter(adapter);
            mViewPager.setOnPageChangeListener(this);
        } else {
            ngGuidePageTransformer.setCurrentItem(getContext(), 0, mFragments);
            mViewPager.setPageTransformer(true, ngGuidePageTransformer);
            adapter.notifyDataSetChanged();
        }

        PlayItem curPlayItem = PlayerManager.getInstance().getCurPlayItem();
        updateViewPagerEnable(new BusEventPlayingLiving(curPlayItem != null && (PlayerManagerHelper.getInstance().isBroadcastPlayer() || PlayerManagerHelper.getInstance().isTvPlayer()) && curPlayItem.isLiving()));

        if (viewStubIsPlayerBar) {
            initPlayerBar(rootView, isShowAnimation);
        } else {
            initHostInt(rootView);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (mRing != null && PlayerManager.getInstance().isPlaying()) {
            mRing.playAnim();
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        if (mRing != null) {
            mRing.pauseAnim();
        }
    }

    @Override
    public void onDestroyView() {
        mHandler.removeCallbacksAndMessages(null);
        PlayerManager.getInstance().removePlayControlStateCallback(mPlayerListener);
        super.onDestroyView();
    }

    private void initHostInt(View inflated) {
        mRightViews.clear();
        //通知跳转动作完成
        EventBus.getDefault().post(new OnlinePlayerFragmentJumpActionEvent(OnlinePlayerFragmentJumpActionEvent.ACTION_STOP));

        hostPic = inflated.findViewById(R.id.hostPic);
        hostNameTv = inflated.findViewById(R.id.hostNameTv);
        liveTitleTv = inflated.findViewById(R.id.liveTitleTv);

        mRightViews.add(hostPic);
        mRightViews.add(hostNameTv);
        mRightViews.add(liveTitleTv);

        PlayItem curPlayItem = PlayerManager.getInstance().getCurPlayItem();
        if (!(curPlayItem instanceof LivePlayItem)) return;
        String title = curPlayItem.getTitle();
        if (title == null) {
            title = "";
        }
        if (title.contains("\n")) {
            title = title.replace("\n", "");
        }
        liveTitleTv.setText(StringUtil.getMaxString(title, 18));
        String hosts;
        if (curPlayItem.getHost() == null)
            hosts = "";
        else hosts = curPlayItem.getHost();
        hostNameTv.setText(StringUtil.getMaxString("主播：" + hosts, 16));
        Glide.with(this).load(((LivePlayItem) curPlayItem).getProgramPic()).into(hostPic);
    }

    private void initPlayerBar(View inflated, boolean showAnimation) {
        mRightViews.clear();
        playerRoot = inflated.findViewById(R.id.playerRoot);
        mRing = inflated.findViewById(R.id.ring);
        roadIv = inflated.findViewById(R.id.online_main_lu_iv);
        playerRoot.setPlayerBarFunVisibility(false);
        playerRoot.setShortUnProgress(true);

        mRightViews.add(playerRoot);
        mRightViews.add(mRing);
        mRightViews.add(roadIv);

        switch (PlayerManagerHelper.getInstance().getCurrentPlayType()) {
            case PlayerConstants.RESOURCES_TYPE_BROADCAST:
            case PlayerConstants.RESOURCES_TYPE_TV://广播/听电视详情页
                mRing.setPageId(Constants.PAGE_ID_PLAY_TV);
                break;
            case PlayerConstants.RESOURCES_TYPE_ALBUM:
            case PlayerConstants.RESOURCES_TYPE_RADIO://专辑/AI电台详情页
                mRing.setPageId(Constants.ONLINE_PAGE_ID_PLAYER_ALBUM);
                break;
            case PlayerConstants.RESOURCES_TYPE_LIVING://直播详情页
                break;
        }
        mRing.setTouchEnable(false);
        mRing.refreshData();
        mRing.hideUnselectItem();

        setViewPositionWhenEnter(showAnimation);
        playerRoot.updateProgressWhenInit();
    }

    private void showExitAnimator(Function0 callback) {
        if (isRunningAnimation) return;
        if (!viewStubIsPlayerBar) {
            if (callback != null)
                callback.invoke();
            return;
        }
        mViewPager.animate().alpha(0f).setDuration(300).start();

        ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) mRing.getLayoutParams();
        int ringBottomMargin = layoutParams.bottomMargin;
        int currRingHeight = mRing.getMeasuredHeight();
        int playerCenterX = centerX + getResources().getDimensionPixelSize(R.dimen.x262);
        int playerWidth = getResources().getDimensionPixelSize(R.dimen.x873);
        ValueAnimator valueAnimator = ValueAnimator.ofFloat(1f, 0f).setDuration(300);
        valueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            ConstraintLayout.LayoutParams playerBarParams, ringParams;

            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                float value = (float) animation.getAnimatedValue();
                float newCenter = centerX + (playerCenterX - centerX) * value;
                if (playerBarParams == null)
                    playerBarParams = (ConstraintLayout.LayoutParams) playerRoot.getLayoutParams();
                int width = (int) (playerBarWidth + (playerWidth - playerBarWidth) * value);
                playerBarParams.width = width;
                playerBarParams.setMarginStart((int) (newCenter - width / 2f));
                playerRoot.setLayoutParams(playerBarParams);
                if (ringParams == null)
                    ringParams = (ConstraintLayout.LayoutParams) mRing.getLayoutParams();
                ringParams.width = (int) (ringWidth + (playerWidth - ringWidth) * value);
                ringParams.setMargins((int) (newCenter - ringParams.width / 2f), ringParams.topMargin, ringParams.getMarginEnd(), (int) (ringBottomMargin + (ringMarginBottom - ringBottomMargin) * value));
                ringParams.height = (int) (ringHeight + (currRingHeight - ringHeight) * value);
                mRing.setLayoutParams(ringParams);
            }
        });
        valueAnimator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
                mHandler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        isRunningAnimation = false;
                        if (callback != null)
                            callback.invoke();
                    }
                }, 50);
            }

            @Override
            public void onAnimationStart(Animator animation) {
                super.onAnimationStart(animation);
                isRunningAnimation = true;
            }
        });
        valueAnimator.start();
    }

    private void setViewPositionWhenEnter(boolean showAnimation) {
        ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) mRing.getLayoutParams();
        int ringBottomMargin = layoutParams.bottomMargin;
        int targetRingHeight = ScreenUtil.getScreenHeight() - layoutParams.topMargin - ringBottomMargin;
        int playerCenterX = centerX + getResources().getDimensionPixelSize(R.dimen.x262);
        int playerWidth = getResources().getDimensionPixelSize(R.dimen.x873);
        if (!showAnimation) {
            ConstraintLayout.LayoutParams playerBarParams = (ConstraintLayout.LayoutParams) playerRoot.getLayoutParams();
            playerBarParams.width = playerWidth;
            playerBarParams.setMarginStart(playerCenterX - playerWidth / 2);
            playerRoot.setLayoutParams(playerBarParams);
            ConstraintLayout.LayoutParams ringParams = (ConstraintLayout.LayoutParams) mRing.getLayoutParams();
            ringParams.width = playerWidth;
            ringParams.setMargins((int) (playerCenterX - ringParams.width / 2f), ringParams.topMargin, ringParams.getMarginEnd(), ringMarginBottom);
            ringParams.height = targetRingHeight;
            mRing.setLayoutParams(ringParams);

            //通知跳转动作完成
            EventBus.getDefault().post(new OnlinePlayerFragmentJumpActionEvent(OnlinePlayerFragmentJumpActionEvent.ACTION_STOP));
            mRing.refreshData();
            return;
        }
        if (isRunningAnimation) return;
        ValueAnimator valueAnimator = ValueAnimator.ofFloat(0, 1f).setDuration(300);
        valueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            ConstraintLayout.LayoutParams playerBarParams, ringParams;

            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                float value = (float) animation.getAnimatedValue();
                float newCenter = centerX + (playerCenterX - centerX) * value;
                if (playerBarParams == null)
                    playerBarParams = (ConstraintLayout.LayoutParams) playerRoot.getLayoutParams();
                int width = (int) (playerBarWidth + (playerWidth - playerBarWidth) * value);
                playerBarParams.width = width;
                playerBarParams.setMarginStart((int) (newCenter - width / 2f));
                playerRoot.setLayoutParams(playerBarParams);
                if (ringParams == null)
                    ringParams = (ConstraintLayout.LayoutParams) mRing.getLayoutParams();
                ringParams.width = (int) (ringWidth + (playerWidth - ringWidth) * value);
                ringParams.setMargins((int) (newCenter - ringParams.width / 2f), ringParams.topMargin, ringParams.getMarginEnd(), (int) (ringBottomMargin + (ringMarginBottom - ringBottomMargin) * value));
                ringParams.height = (int) (ringHeight + (targetRingHeight - ringHeight) * value);
                mRing.setLayoutParams(ringParams);
            }
        });
        valueAnimator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
                isRunningAnimation = false;
                //通知跳转动作完成
                EventBus.getDefault().post(new OnlinePlayerFragmentJumpActionEvent(OnlinePlayerFragmentJumpActionEvent.ACTION_STOP));
                mRing.refreshData();
            }

            @Override
            public void onAnimationStart(Animator animation) {
                super.onAnimationStart(animation);
                isRunningAnimation = true;
            }
        });
        valueAnimator.start();
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        if (hidden) {
            if (mRing != null)
                mRing.pauseAnim();
        } else {
            if (PlayerManager.getInstance().isPlaying()) {
                if (mRing != null)
                    mRing.playAnim();
            }
        }
    }

    void backPressed(View view) {
        Log.e(TAG, "点击事件触发");
        FragmentActivity activity = getActivity();
        if (activity != null) activity.onBackPressed();
    }

    /**
     * 刷新标签
     */
    public void upDateRingItemView() {
        if (mRing != null)
            mRing.notifyDataSetChanged();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void updateViewPagerEnable(BusEventPlayingLiving busEventPlayingLiving) {
        if (busEventPlayingLiving == null) return;
        if (busEventPlayingLiving.isPlayingBroadcastLiving()) {
            // FIXME: 2022/7/4 这个版本不支持广播进入弹幕页，所以注释掉下面一行代码。当需求修改后解注释
//            playerRoot.setPlayerBarFunEnable(true);
            mViewPager.setOnTouchListener(null);
        } else {
            mViewPager.setCurrentItem(0, true);
            if (playerRoot != null)
                playerRoot.setPlayerBarFunEnable(false);
            mViewPager.setOnTouchListener(new View.OnTouchListener() {
                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    return false;
                }
            });
        }
    }

    /**
     * 是否在直播间（录音按钮是否可用）
     *
     * @return
     */
    private boolean inLivingRoom() {
        if (PlayerManagerHelper.getInstance().getCurrentPlayType() == PlayerConstants.RESOURCES_TYPE_LIVING)
            return true;
        if (mViewPager != null) {
            int currentItem = mViewPager.getCurrentItem();
            if (mFragments.size() > currentItem) {
                Fragment fragment = mFragments.get(currentItem);
                if (fragment instanceof LiveBarrageFragment) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public boolean useEventBus() {
        return PlayerManagerHelper.getInstance().getCurrentPlayType() == PlayerConstants.RESOURCES_TYPE_BROADCAST ||
                PlayerManagerHelper.getInstance().getCurrentPlayType() == PlayerConstants.RESOURCES_TYPE_LIVING;
    }

    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
    }

    @Override
    public void onPageSelected(int position) {
        if (ngGuidePageTransformer != null)
            ngGuidePageTransformer.setCurrentItem(position);
        if (mFragments.size() > position) {
            Fragment fragment = mFragments.get(position);
            if (fragment instanceof LiveBarrageFragment) {
                if (playerRoot != null)
                    playerRoot.setPlayerBarFunVisibility(false);
                return;
            }
        }
        if (playerRoot != null)
            playerRoot.setPlayerBarFunVisibility(false);
//        playerRoot.setPlayerBarFunVisibility(true);
    }

    @Override
    public void onPageScrollStateChanged(int state) {

    }

    @Override
    public void onGetBroadcastDetailSuccess(BroadcastRadioDetailData broadcastDateData) {
        mFragments.add(LiveBarrageFragment.newInstance(broadcastDateData.getRoomId(), "直播详情页"));
        if (adapter != null) {
            adapter.notifyDataSetChanged();
        }
        if (ngGuidePageTransformer != null) {
            int position = 0;
            if (mViewPager != null) {
                position = mViewPager.getCurrentItem();
            }
            ngGuidePageTransformer.setCurrentItem(getContext(), position, mFragments);
        }
    }

    @Override
    public void onGetBroadcastDetailError(int code, String msg) {

    }


    public void setComperes(String comperes) {
        hostNameTv.setText(StringUtil.getMaxString("主播：" + comperes, 16));
    }

    public void onBackPressed(Function0 animationFinishedCallback) {
        if (isShowAnimation && viewStubIsPlayerBar) {
            showExitAnimator(animationFinishedCallback);
        } else {
            animationFinishedCallback.invoke();
        }
    }

    /**
     * 重置参数：能否显示广告主图
     * 需求来自：在线电台，只在猜你喜欢、专辑/AI电台播放页、广播/听电视播放页显示广告主图，且切换页面时隐藏广告主图
     *
     * @param isCanShowAdImage
     */
    private void resetAdImageShowParam(boolean isCanShowAdImage) {
        AdvertisingImager advertisingImager = AdvertisingManager.getInstance().getImager();
        if (advertisingImager != null && advertisingImager instanceof AdvertisingImagerImpl) {
            ((AdvertisingImagerImpl) advertisingImager).setCanShowAdImage(isCanShowAdImage);
        }
    }

    public void releaseLiving() {
        if (mFragments == null) return;
        Fragment fragment;
        for (int i = 0; i < mFragments.size(); i++) {
            fragment = mFragments.get(i);
            if (fragment instanceof LiveBarrageFragment) {
                ((LiveBarrageFragment) fragment).releaseOnDestroyView();
                ((LiveBarrageFragment) fragment).releaseOnDestroy();
            }
        }

    }

    @Override
    public String consumeRoute(String pageId, Object extra) {
        //来到这就说明一定不是相同节目了。
        if (!viewStubIsPlayerBar) {
            releaseLiving();
        }
        Iterator<View> iterator = mRightViews.iterator();
        while (iterator.hasNext()) {
            View next = iterator.next();
            rootView.removeView(next);
            iterator.remove();
        }
        refreshViews(false);

        return IRouterConsumer.ROUTER_CONSUME_FULLY;
    }
}