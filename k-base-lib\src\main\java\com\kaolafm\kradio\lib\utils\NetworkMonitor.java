package com.kaolafm.kradio.lib.utils;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.util.Log;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.lib.base.flavor.NetworkBrodcastInter;
import com.kaolafm.kradio.lib.base.flavor.NetworkStatusInter;

import java.lang.ref.WeakReference;
import java.util.concurrent.CopyOnWriteArrayList;

public class NetworkMonitor {
    private static final String TAG = "NetworkMonitor";

    public interface OnNetworkStatusChangedListener {
        void onStatusChanged(int newStatus, int oldStatus);
    }

    private WeakReference<Context> mContextWeakReference;

    private NetworkStatusInter mNetworkStatus;

    public static final int STATUS_NO_NETWORK = 0;
    public static final int STATUS_WIFI = 1;
    public static final int STATUS_MOBILE = 2;
    private NetworkBrodcastInter networkBrodcastInter;

    private volatile int mNetStatus = STATUS_NO_NETWORK;

    private static class NETWORK_MONITOR_CLASS {
        private static final NetworkMonitor NETWORK_MONITOR_INSTANCE = new NetworkMonitor();
    }

    public static NetworkMonitor getInstance(Context context) {
        if (NETWORK_MONITOR_CLASS.NETWORK_MONITOR_INSTANCE.mContextWeakReference == null) {
            NETWORK_MONITOR_CLASS.NETWORK_MONITOR_INSTANCE.mContextWeakReference = new WeakReference<>(context instanceof Activity ?
                    context.getApplicationContext() : context);
            NETWORK_MONITOR_CLASS.NETWORK_MONITOR_INSTANCE.init(context);
        }
        return NETWORK_MONITOR_CLASS.NETWORK_MONITOR_INSTANCE;
    }

    private NetworkMonitor() {
        mNetworkStatus = ClazzImplUtil.getInter("NetworkStatusInterImpl");
        networkBrodcastInter = ClazzImplUtil.getInter("NetworkBrodcastImpl");
    }

    private CopyOnWriteArrayList<OnNetworkStatusChangedListener> mListeners = new CopyOnWriteArrayList<>();

    public void registerNetworkStatusChangeListener(OnNetworkStatusChangedListener listener, boolean isNotHasNetwork) {
        if (isNotHasNetwork) {
            mNetStatus = STATUS_NO_NETWORK;
        }
        registerNetworkStatusChangeListener(listener);
    }

    public void registerNetworkStatusChangeListener(OnNetworkStatusChangedListener listener) {
        if (listener == null) {
            return;
        }
        Context context = mContextWeakReference.get();
        Log.i(TAG, "registerNetworkStatusChangeListener------>start listener = " + listener);
        if (!mListeners.contains(listener)) {
            if (mListeners.size() == 0) {
                IntentFilter filter = new IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION);
                if (mNetworkStatus != null) {
                    String[] actions = mNetworkStatus.netAction();
                    if (!ListUtil.isEmpty(actions)) {
                        for (String action : actions) {
                            filter.addAction(action);
                        }
                    }
                }

                if (context != null) {
                    Log.i(TAG, "registerNetworkStatusChangeListener start");
                    context.registerReceiver(mReceiver, filter);
                }
            }
            Log.i(TAG, "registerNetworkStatusChangeListener------>listener = " + listener);
            mListeners.add(listener);
        }
        if (networkBrodcastInter != null) {
            networkBrodcastInter.registerNetworkStatusChangeListener(context);
        }
    }

    public void removeNetworkStatusChangeListener(OnNetworkStatusChangedListener listener) {
        Log.i(TAG, "removeNetworkStatusChangeListener------>listener = " + listener);
        Context context = mContextWeakReference.get();
        if (listener != null) {
            mListeners.remove(listener);

            if (mListeners.size() == 0 && context != null) {
                context.unregisterReceiver(mReceiver);
            }
        }
        if (networkBrodcastInter != null) {
            networkBrodcastInter.removeNetworkStatusChangeListener(context);
        }
    }

    private void init(Context context) {
        if (mConnectivityManager == null && context != null) {
            mConnectivityManager = (ConnectivityManager) context.getApplicationContext().getSystemService(Context.CONNECTIVITY_SERVICE);
        }
        mNetStatus = populateNetworkStatus();
    }

    private boolean isNetStatusChanged() {
        int status = populateNetworkStatus();
        Log.i(TAG, "isNetStatusChanged------->status = " + status + "--->mNetStatus = " + mNetStatus);
        if (mNetStatus != status) {
            mNetStatus = status;
            return true;
        }
        return false;
    }

    private int populateNetworkStatus() {
        NetworkInfo info = null;
        if (mConnectivityManager != null) {
            info = mConnectivityManager.getActiveNetworkInfo();
        }
        if (info != null && info.isConnected()) {
            int type = info.getType();
            Log.i(TAG, "populateNetworkStatus------->type = " + type);
            switch (type) {
                case ConnectivityManager.TYPE_MOBILE:
                case ConnectivityManager.TYPE_MOBILE_DUN:
                case ConnectivityManager.TYPE_MOBILE_HIPRI:
                case ConnectivityManager.TYPE_MOBILE_MMS:
                case ConnectivityManager.TYPE_MOBILE_SUPL:
                    return STATUS_MOBILE;
                default:
                    return STATUS_WIFI;
            }
        }
        Log.i(TAG, "populateNetworkStatus------->no netWork!");
        return STATUS_NO_NETWORK;
    }

    private ConnectivityManager mConnectivityManager;
    private BroadcastReceiver mReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            Log.i(TAG, "onReceive---action = " + action);
            int status = mNetStatus;
            if ((ConnectivityManager.CONNECTIVITY_ACTION.equals(action) || channelAction(action)) && isNetStatusChanged()) {
                if (mListeners.size() > 0) {
                    for (int i = 0/*, size = mListeners.size()*/; i < mListeners.size(); i++) {
                        OnNetworkStatusChangedListener onNetworkStatusChangedListener = mListeners.get(i);
                        Log.i(TAG, "onReceive---" + onNetworkStatusChangedListener);
                        if (onNetworkStatusChangedListener == null) {
                            continue;
                        }

                        onNetworkStatusChangedListener.onStatusChanged(mNetStatus, status);
                    }
                }
            }
        }
    };

    private boolean channelAction(String action) {
        if (mNetworkStatus != null) {
            String[] actions = mNetworkStatus.netAction();
            if (!ListUtil.isEmpty(actions)) {
                for (String s : actions) {
                    if (action.equals(s)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

}
