package com.kaolafm.kradio.lib.basedb.manager;

import com.kaolafm.kradio.lib.basedb.GreenDaoInterface;
import com.kaolafm.kradio.lib.bean.HistoryItem;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/4
 */
public interface Manager {

    /**
     * 更新历史记录
     * @param playItem
     * @param progress
     */
    void progress(PlayItem playItem, long progress);

    /**
     * 获取历史列表
     * @param callback
     */
    void getHistoryList(HttpCallback<List<HistoryItem>> callback);

    /**
     * 获取广播列表
     * @return
     */
    List<HistoryItem> getBroadcastList();

    /**
     * 根据专辑id获取当前最新的历史
     * @param albumId
     * @param listener
     */
    void queryLatestHistoryByAlbumId(String albumId, GreenDaoInterface.OnQueryListener<HistoryItem> listener);

    /**
     * 获取最近的一条历史
     * @return
     */
    HistoryItem getLasted();

    /**
     * 保存播放历史。
     * @param playItem
     * @param isUpdatePlayPosition
     */
    void saveHistory(PlayItem playItem, boolean isUpdatePlayPosition);

    /**
     * 设置监听
     * @param listener
     */
    void setListener(GreenDaoInterface.OnQueryListener<Boolean> listener);

    /**
     * App退出时同步保存历史。防止历史记录有遗漏。
     * @param playItem
     */
    void saveHistoryWhenDestroy(PlayItem playItem);

    /**
     * 清除历史
     * @param callback
     */
    void clear(HttpCallback<Boolean> callback);

    /**
     * 释放资源
     */
    void destroy();

}
