package com.kaolafm.kradio.flavor.impl;

import android.Manifest;
import android.content.Context;
import android.util.Log;

import com.kaolafm.base.internal.AdaptedDeviceId;
import com.kaolafm.base.internal.DeviceIdFactory;
import com.kaolafm.base.internal.RandomObtainDeviceId;
import com.kaolafm.kradio.common.utils.PermissionUtils;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioDeviceIdInter;

/**
 * @Description:
 * @Author: Maclay
 * @Date: 2021/11/8 11:00 上午
 */
public class KRadioDeviceIdImpl implements KRadioDeviceIdInter {
    private static final String TAG = "KRadioDeviceIdImpl";

    @Override
    public void initDeviceFactory(Context context) {
        Log.d(TAG, "initDeviceFactory");
        if (PermissionUtils.checkPermission(context, Manifest.permission.READ_EXTERNAL_STORAGE,
                Manifest.permission.WRITE_EXTERNAL_STORAGE)) {
            return;
        }
        Log.d(TAG, "initDeviceFactory , 0 -> KRadioCacheObtainDeviceId");
        DeviceIdFactory factory = DeviceIdFactory.getInstance();
        factory.set(0, new KRadioCacheObtainDeviceId());
        factory.set(1, new RandomObtainDeviceId());
    }
}