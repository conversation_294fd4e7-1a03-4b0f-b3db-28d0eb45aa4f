<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@drawable/component_card_bg_7">

    <com.kaolafm.kradio.component.ui.base.view.OvalImageView
        android:id="@+id/card_bg_iv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:oval_radius="@dimen/m8" />


    <com.kaolafm.kradio.component.ui.base.view.OvalImageView
        android:id="@+id/card_pic_iv"
        android:layout_width="@dimen/m160"
        android:layout_height="@dimen/m160"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginRight="@dimen/m20"
        app:oval_radius="@dimen/m8"
        tools:src="@drawable/splash_yunting" />

    <ImageView
        android:id="@+id/vip_icon"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/m20"
        android:layout_alignLeft="@+id/card_pic_iv"
        android:layout_alignTop="@+id/card_pic_iv"
        android:scaleType="fitStart"
        android:visibility="gone"
        tools:src="@drawable/comprehensive_icon_vip" />

    <TextView
        android:id="@+id/card_tag_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/m20"
        android:layout_marginTop="@dimen/m20"
        android:adjustViewBounds="true"
        android:background="@drawable/brand_page_tab_bg"
        android:gravity="center"
        android:paddingLeft="@dimen/m20"
        android:paddingTop="@dimen/m10"
        android:paddingRight="@dimen/m20"
        android:paddingBottom="@dimen/m10"
        android:textColor="#FFFCFB"
        android:textSize="@dimen/m20"
        tools:text="有奖互动" />

    <com.kaolafm.kradio.component.ui.base.view.KradioTextView
        android:id="@+id/card_title_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/card_tag_tv"
        android:layout_marginStart="@dimen/m20"
        android:layout_marginTop="@dimen/m13"
        android:layout_marginEnd="@dimen/m32"
        android:layout_toLeftOf="@+id/card_pic_iv"
        android:ellipsize="end"
        android:lineSpacingExtra="@dimen/m7"
        android:maxLines="1"
        android:textColor="@color/component_brand_page_title_text_color"
        android:textSize="@dimen/m26"
        app:kt_font_weight="0.3"
        tools:text="我是标题啊" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/card_title_tv"
        android:layout_toLeftOf="@+id/card_pic_iv"
        android:orientation="horizontal">

        <com.kaolafm.kradio.component.ui.base.view.KradioTextView
            android:id="@+id/card_des_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/m20"
            android:layout_marginTop="@dimen/m5"
            android:ellipsize="end"
            android:lineSpacingExtra="@dimen/m7"
            android:maxLines="1"
            android:textColor="@color/component_brand_page_des_text_color"
            android:textSize="@dimen/m24"
            tools:text="我是描述啊" />

        <View
            android:id="@+id/des_hide_view"
            android:layout_width="@dimen/m50"
            android:layout_height="@dimen/m1" />
    </LinearLayout>

    <ImageView
        android:id="@+id/card_play_iv"
        android:layout_width="@dimen/m38"
        android:layout_height="@dimen/m38"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:layout_marginRight="@dimen/m24"
        android:layout_marginBottom="@dimen/m24"
        android:src="@drawable/component_play_icon_2"
        android:visibility="gone" />

            <com.kaolafm.kradio.component.ui.base.view.RateView
                android:id="@+id/card_layout_playing"
                android:layout_width="@dimen/m38"
                android:layout_height="@dimen/m38"
                android:layout_alignParentRight="true"
                android:layout_alignParentBottom="true"
                android:layout_marginRight="@dimen/m24"
                android:layout_marginBottom="@dimen/m24"
                app:lottie_autoPlay="true"
                app:lottie_fileName="lottie/rate.json"
                app:lottie_loop="true" />
</RelativeLayout>