package com.kaolafm.kradio.huawei.convert;


import com.huawei.carmediakit.bean.Banner;
import com.huawei.carmediakit.bean.Compilation;
import com.huawei.carmediakit.bean.MediaElement;
import com.huawei.carmediakit.bean.PageContent;
import com.huawei.carmediakit.bean.PageTab;
import com.huawei.carmediakit.bean.Single;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.common.bean.HistoryItem;
import com.kaolafm.kradio.huawei.utils.Constant;
import com.kaolafm.kradio.huawei.utils.MediaIdHelper;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.opensdk.api.operation.model.category.BroadcastCategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.Category;
import com.kaolafm.opensdk.api.operation.model.category.CategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.LeafCategory;
import com.kaolafm.opensdk.api.operation.model.category.RadioCategoryMember;
import com.kaolafm.opensdk.player.logic.model.PlaylistInfo;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.utils.operation.OperationAssister;

import java.util.ArrayList;
import java.util.List;


public class DataConverterUtil {

    public static final String TAG = Constant.TAG;

    public static List<PageTab> toPageTagList(List<Category> categoryList) {
        List<PageTab> pageTabs = new ArrayList<>();
        if (categoryList == null || categoryList.isEmpty()) {
            return pageTabs;
        }

        PageTab pageTab = null;
        for (int i = 0; i < categoryList.size(); i++) {
            pageTab = toPageTag(categoryList.get(i));
            pageTabs.add(pageTab);
        }

        return pageTabs;
    }

    private static PageTab toPageTag(Category category) {
        PageTab pageTab = new PageTab();
        pageTab.setTabName(category.getName());
        pageTab.setMediaId(category.getCode());
        return pageTab;
    }

    public static PageContent toPageContent(List<Category> categoryList) {
        PageContent pageContent = new PageContent();
        List<Compilation> compilationList = new ArrayList<>(); //
        Compilation compilation = new Compilation(); //合集
        if (categoryList == null || categoryList.isEmpty()) {
            return pageContent;
        }

        //应该只有一个
        for (Category category : categoryList) {
            List<Category> childCategories = category.getChildCategories();
            if (!ListUtil.isEmpty(childCategories)) {

                for (Category childCategory : childCategories) {
                    Logger.i(TAG, "child=" + childCategory);
                    toCompilation(childCategory, compilationList);
                }
            }
        }
        pageContent.setCompilations(compilationList);

        return pageContent;
    }

    public static PageContent toPageContentWithLeafCateList(List<LeafCategory> categoryList) {
        PageContent pageContent = new PageContent();
        List<Compilation> compilationList = new ArrayList<>(); //
        List<Banner> bannerList = new ArrayList<>();

        if (categoryList == null || categoryList.isEmpty()) {
            return pageContent;
        }

        //应该只有一个
        for (Category category : categoryList) {
            List<Category> childCategories = category.getChildCategories();
            if (!ListUtil.isEmpty(childCategories)) {

                for (Category childCategory : childCategories) {
                    toCompilation(childCategory, compilationList);
                }
            } else {
                toCompilation(category, compilationList);
            }
        }
        pageContent.setCompilations(compilationList);
        Banner banner = new Banner();
        banner.setCoverUrl("https://img2.baidu.com/it/u=2111934368,1510786453&fm=26&fmt=auto");
        bannerList.add(banner);
        pageContent.setBanners(bannerList);

        return pageContent;
    }

    public static void toCompilation(Category category, List<Compilation> compilationList) {
        Compilation compilation = new Compilation();;
        //三级分类
        if (category instanceof LeafCategory) {
            compilation.setName(category.getName());
            compilation.setMediaId(category.getCode());
            compilation.setElementsType(MediaElement.ElementType.ALBUM);
            parseCategoryMemberList((LeafCategory) category, compilation);
            compilationList.add(compilation);
            //四级分类
        }else {
            List<Category> childCategories = category.getChildCategories();
            if (!ListUtil.isEmpty(childCategories)) {
                for (Category childCategory : childCategories) {
                    //解析分类
                    if (childCategory instanceof LeafCategory) {
                        compilation = new Compilation();
                        compilation.setElementsType(MediaElement.ElementType.ALBUM);
                        compilation.setName(childCategory.getName());
                        compilation.setMediaId(childCategory.getCode());
                        parseCategoryMemberList((LeafCategory) childCategory, compilation);
                        compilationList.add(compilation);
                    }
                }
            }
        }
    }

    private static void parseCategoryMemberList(LeafCategory category, Compilation compilation) {
        List<CategoryMember> categoryMembers = category.getCategoryMembers();
        List<MediaElement> mediaElementList = new ArrayList<>();
        MediaElement mediaElement;
        if (categoryMembers != null) {
            for (int i = 0; i < categoryMembers.size(); i++) {
                CategoryMember cm = categoryMembers.get(i);
                mediaElement = toMediaElement(cm);
                mediaElementList.add(mediaElement);
            }
            compilation.setTopElements(mediaElementList);
        }
    }

    public static MediaElement toMediaElement(CategoryMember cm) {
        int type;
        if (cm instanceof BroadcastCategoryMember) {
            type = PlayerConstants.RESOURCES_TYPE_BROADCAST;
        } else if (cm instanceof RadioCategoryMember) {
            type = PlayerConstants.RESOURCES_TYPE_RADIO;
        } else {
            type = PlayerConstants.RESOURCES_TYPE_ALBUM;
        }
        MediaElement element = new MediaElement();
        element.setMediaId(MediaIdHelper.getIdAndType(String.valueOf(OperationAssister.getId(cm)),
                type));
        element.setName(cm.getTitle());
        element.setCoverUrl(OperationAssister.getImage(cm));
        element.setElementType(MediaElement.ElementType.ALBUM);
        element.setDesp(cm.getDescription());

        Logger.i(TAG, "type=" + cm.getType());

        return element;
    }

    public static Single toSingle(PlaylistInfo playlistInfo) {
        Single single = new Single();
        single.setCoverUrl(playlistInfo.getAlbumPic());
        single.setName(playlistInfo.getAlbumName());
        single.setMediaId(MediaIdHelper.getIdAndType(playlistInfo.getId(), playlistInfo.getType()));

        return single;
    }

    public static Single toSingle(HistoryItem playlistInfo) {
        Single single = new Single();
        single.setCoverUrl(playlistInfo.getPicUrl());
        single.setName(playlistInfo.getAudioTitle());
        single.setMediaId(MediaIdHelper.getIdAndType(playlistInfo.getRadioId(), Integer.parseInt(playlistInfo.getType())));

        return single;
    }

}
