package com.kaolafm.kradio.online.history.ui;

import android.graphics.Rect;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;
import android.util.Log;
import android.view.View;

import com.kaolafm.kradio.common.bean.HistoryItem;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.utils.ResUtil;

/**
 * 历史列表间隔装饰。没有复用其他的是因为需要判断去登陆item和标题item
 *
 * <AUTHOR>
 * @date 2020/8/21
 */
public class HistoryItemDecoration extends RecyclerView.ItemDecoration {

    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {


        int type = parent.getLayoutManager().getItemViewType(view);
        if (type == OnlineHistoryAdapter.HEAD_UNLOGIN_TIP) {
            return;
        }
        final int spanCount = getSpanCount(parent);
        final int childCount = parent.getAdapter().getItemCount();
        final int adapterPosition = parent.getChildAdapterPosition(view);
        if (isFirst(adapterPosition, spanCount, childCount)) {
            outRect.left = ResUtil.getDimen(R.dimen.online_history_padding_left);
        } else {
            outRect.left = 0;
        }
        outRect.right = ResUtil.getDimen(R.dimen.online_history_item_decoration);
//        if (ResUtil.getOrientation() == Configuration.ORIENTATION_PORTRAIT){
//            return;
//        }
//        if (isLastColumn(view, parent)) {
//            outRect.right = 0;
//            outRect.left = ResUtil.getDimen(R.dimen.m25);
//        } else {
//            outRect.left = 0;
//            outRect.right = ResUtil.getDimen(R.dimen.m25);
//        }

    }

    private boolean isFirst(int position, int spanCount,int childCount) {
        Log.e("isFirstColumn", "position is "+position);
        Log.e("isFirstColumn", "spanCount is "+spanCount);
        Log.e("isFirstColumn", "childCount is "+childCount);
        return position < spanCount;
    }

    private boolean isLastColumn(View view, RecyclerView parent) {
        final int spanCount = getSpanCount(parent);
        final int position = parent.getChildAdapterPosition(view);
        BaseAdapter<HistoryItem> adapter = (BaseAdapter<HistoryItem>) parent.getAdapter();
        HistoryItem itemData = adapter.getItemData(0);
//        if (itemData.getTypeId() == OnlineHistoryAdapter.HISTORY_COUNT_TITLE) {
//            return position % spanCount == 0;
//        } else {
            return (position + 1) % spanCount == 0;
//        }
    }

    private int getSpanCount(RecyclerView parent) {
        RecyclerView.LayoutManager layoutManager = parent.getLayoutManager();

        if (layoutManager instanceof GridLayoutManager) {
            return ((GridLayoutManager) layoutManager).getSpanCount();
        } else if (layoutManager instanceof StaggeredGridLayoutManager) {
            return ((StaggeredGridLayoutManager) layoutManager).getSpanCount();
        } else {
            throw new UnsupportedOperationException("the GridDividerItemDecoration can only be used in " +
                    "the RecyclerView which use a GridLayoutManager or StaggeredGridLayoutManager");
        }
    }
}
