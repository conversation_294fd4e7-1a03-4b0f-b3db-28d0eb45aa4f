package com.kaolafm.kradio.flavor.media;

import android.os.Build;
import android.util.Log;
import android.view.View;

import com.kaolafm.kradio.common.widget.SquareImageView;
import com.kaolafm.kradio.k_kaolafm.home.item.BroadcastCell;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;

/**
 * @Package: com.kaolafm.kradio.flavor.media
 * @Description:
 * @Author: Maclay
 * @Date: 17:40
 */
@Aspect
public class HWAop {
    private static final String TAG = "HWAop";
    HWMediaManager hwMediaManager;

    @Around("execution(* com.kaolafm.auto.home.HubActivity.onCreate(..))")
    public void HubActivity_onCreate(ProceedingJoinPoint point) throws Throwable {
        Log.d(TAG, "HubActivity_onCreate");
        point.proceed();
        hwMediaManager = new HWMediaManager();
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
//            MyMediaSessionManager.getInstance().initMediaSession(AppDelegate.getInstance().getContext());
//        }
    }

    @Around("execution(* com.kaolafm.launcher.LauncherActivity.onDestroy(..))")
    public void LaunchActivity_onDestroy(ProceedingJoinPoint point) throws Throwable {
        Log.d(TAG, "LaunchActivity_onDestroy");
        point.proceed();
        if (hwMediaManager != null) {
            hwMediaManager.release();
            hwMediaManager = null;
        }
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
//            MyMediaSessionManager.destroyInstance();
//        }
    }

}
