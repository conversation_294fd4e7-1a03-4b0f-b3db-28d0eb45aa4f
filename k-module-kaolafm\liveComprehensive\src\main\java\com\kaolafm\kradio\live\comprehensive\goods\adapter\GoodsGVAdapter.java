package com.kaolafm.kradio.live.comprehensive.goods.adapter;

import android.content.Context;
import android.graphics.Paint;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.kaolafm.kradio.component.ui.base.view.OvalImageView;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.purchase.util.MoneyUtils;
import com.kaolafm.opensdk.api.goods.model.Goods;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.util.ReportConstants;
import com.kaolafm.report.util.ReportParameterManager;

import java.util.List;


/**
 * Created by Ren on 2023/2/9.
 */
public class GoodsGVAdapter extends RecyclerView.Adapter<GoodsGVAdapter.ViewHodler> {

    private static final String TAG = "GoodsGVAdapter";
    private List<Goods> list;
    private Context mContext;
    private boolean isNetData;

    private ViewHodler mHolder;
    private int clickTemp = -1;

    //标识选择的Item
    public void setSeclection(int position) {
        clickTemp = position;
    }

    public int getSecletion() {
        return clickTemp;
    }

    public GoodsGVAdapter(RecyclerView recyclerView, List<Goods> list, Context mContext, boolean isNetData) {
        super();
        this.list = list;
        this.mContext = mContext;
        this.isNetData = isNetData;
    }

    public void clear() {
        this.mContext = null;
    }

    @Override
    public ViewHodler onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.comprehensive_goods_item, parent, false);
        ViewHodler viewHodler = new ViewHodler(view);
        view.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mOnItemClickListener != null) {
                    final int position = viewHodler.getAdapterPosition();
                    if (position < 0) return;
                    final Goods goods = list.get(position);
                    mOnItemClickListener.onItemClick(view, goods, position);
                    clickTemp = position;
                    notifyDataSetChanged();
                }
            }
        });
        view.findViewById(R.id.item_btn).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                final int position = ((RecyclerView) parent).getChildAdapterPosition(view);
                final Goods goods = list.get(position);
                if (mShopPurchaseListener != null) {
                    mShopPurchaseListener.onShopPurchase(v, goods, position);
                }
                String radioId = null, audioId = null;
                PlayItem curPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
                if (curPlayItem != null) {
                    radioId = curPlayItem.getRadioId();
                    audioId = String.valueOf(curPlayItem.getAudioId());
                }
                ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_LIVE_ROOM_MERCHANDISE_LIST_ITEM_PURCHASE, ((TextView) v).getText().toString(), ReportParameterManager.getInstance().getPage()
                        , ReportConstants.CONTROL_TYPE_SCREEN, null, radioId, audioId, audioId, null));
            }
        });
        return viewHodler;
    }

    @Override
    public void onBindViewHolder(final ViewHodler holder, final int position) {
        final Goods goods = list.get(position);
        if (goods == null) {
            return;
        }
        int p = position + 1;
        if (p > 0) {
            holder.item_position.setText(p + "");
            holder.item_position.setVisibility(View.VISIBLE);
        } else {
            holder.item_position.setVisibility(View.GONE);
        }
        ImageLoader.getInstance().displayImage(mContext, goods.getPicUrl(), holder.item_img);
        if (!TextUtils.isEmpty(goods.getName())) {
            holder.item_title.setText(goods.getName());
        }
        if (!TextUtils.isEmpty(goods.getSellPoint())) {
            holder.item_desc.setText(goods.getSellPoint());
        }
        holder.item_origin_price.setText(MoneyUtils.changeF2Y(goods.getMarketPrice()) + "");
        holder.item_origin_price.getPaint().setFlags(Paint.STRIKE_THRU_TEXT_FLAG);
        holder.item_discount_price.setText(MoneyUtils.changeF2Y(goods.getSalesPrice()) + "");
        if (goods.getPushType() != null && goods.getPushType() == 1) {
            holder.item_explain.setVisibility(View.VISIBLE);
        } else {
            holder.item_explain.setVisibility(View.GONE);
        }
        if (goods.getShelf() != null && goods.getShelf() == 1) {
            holder.item_off_shelf.setVisibility(View.GONE);
        } else {
            holder.item_off_shelf.setVisibility(View.VISIBLE);
            Log.d(TAG, goods.getStock() + "");
            holder.item_btn.setClickable(false);
            holder.item_btn.setBackground(mContext.getResources().getDrawable(R.drawable.comprehensive_goods_item_btn_off_bg));
        }
        if (goods.getShelf() != null && goods.getShelf() == 1) {
            if (goods.getStock() != null && goods.getStock() > 0) {
                Log.d(TAG, goods.getStock() + "");
                holder.item_off_shelf.setVisibility(View.GONE);
                holder.item_btn.setClickable(true);
                holder.item_btn.setBackground(mContext.getResources().getDrawable(R.drawable.comprehensive_goods_item_btn_normal_bg));
            } else {
                holder.item_off_shelf.setVisibility(View.GONE);
                holder.item_btn.setClickable(false);
                holder.item_btn.setBackground(mContext.getResources().getDrawable(R.drawable.comprehensive_goods_item_btn_off_bg));
            }
        } else {
            holder.item_off_shelf.setVisibility(View.VISIBLE);
            holder.item_btn.setClickable(false);
            holder.item_btn.setBackground(mContext.getResources().getDrawable(R.drawable.comprehensive_goods_item_btn_off_bg));
        }
        if (clickTemp == position) {
//            holder.llroot.setBackgroundResource(R.drawable.gift_shape_chose);
            onItemSelected(holder, true);
            mHolder = holder;
        } else {
//            holder.llroot.setBackgroundResource(R.drawable.gift_shape_tran);
            onItemSelected(holder, false);
        }

        //事件上报
        String radioId = null, audioId = null;
        PlayItem curPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
        if (curPlayItem != null) {
            radioId = curPlayItem.getRadioId();
            audioId = String.valueOf(curPlayItem.getAudioId());
        }
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_LIVE_ROOM_MERCHANDISE_LIST_ITEM_PURCHASE, holder.item_btn.getText().toString(), ReportParameterManager.getInstance().getPage()
                , ReportConstants.CONTROL_TYPE_SCREEN, null, radioId, audioId, audioId, null));
    }

    @Override
    public int getItemCount() {
        return list.size();
    }

    public void clearSelection() {
        if (mHolder != null) {
//            mHolder.llroot.setBackgroundResource(R.drawable.gift_shape_tran);
            onItemSelected(mHolder, false);
            mHolder = null;
        }
    }

    class ViewHodler extends RecyclerView.ViewHolder {
        OvalImageView item_img;
        RelativeLayout item_explain;
        TextView item_position;
        TextView item_title;
        TextView item_desc;
        TextView item_origin_price;
        TextView item_discount_price;
        TextView item_btn;
        TextView item_off_shelf;


        public ViewHodler(View view) {
            super(view);

            item_img = view.findViewById(R.id.item_img);
            item_explain = view.findViewById(R.id.item_explain);
            item_position = view.findViewById(R.id.item_position);
            item_title = view.findViewById(R.id.item_title);
            item_desc = view.findViewById(R.id.item_desc);
            item_origin_price = view.findViewById(R.id.item_origin_price);
            item_discount_price = view.findViewById(R.id.item_discount_price);
            item_btn = view.findViewById(R.id.item_btn);
            item_off_shelf = view.findViewById(R.id.item_off_shelf);

        }
    }

    public interface OnItemClickListener {
        void onItemClick(View view, Goods goods, int position);
    }

    public OnItemClickListener mOnItemClickListener;

    public void setOnItemClickListener(OnItemClickListener listener) {
        mOnItemClickListener = listener;
    }

    public interface OnShopPurchaseListener {
        void onShopPurchase(View view, Goods goods, int position);
    }

    public OnShopPurchaseListener mShopPurchaseListener;

    public void setOnShopPurchaseListener(OnShopPurchaseListener listener) {
        mShopPurchaseListener = listener;
    }


    private void onItemSelected(ViewHodler holder, boolean isSelected) {
//        if(isSelected){
//            holder.itemSelected.setVisibility(View.VISIBLE);
//            holder.itemNormal.setVisibility(View.GONE);
//        }else {
//            holder.itemSelected.setVisibility(View.GONE);
//            holder.itemNormal.setVisibility(View.VISIBLE);
//        }

    }
}
