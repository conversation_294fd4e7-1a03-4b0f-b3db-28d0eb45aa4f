package com.kaolafm.kradio.lib.bean;

/******************************************
 * 类描述： 订阅详情对象 类名称：SubscribeData
 *
 * @version: 1.0
 * @author: shaoning<PERSON>ang
 * @time: 2016-7-22 14:57
 ******************************************/
public class PurchasedData {
    /**
     * 专辑，PGC，传统广播电台ID
     */
    private long id;
    /**
     * 专辑，PGC，传统广播电台名称
     */
    private String name;
    /**
     * 专辑，PGC，传统广播电台 类型
     */
    private int type;
    /**
     * 专辑，PGC，传统广播电台图片URL
     */
    private String img;
    /**
     * 更新时间 时间戳形式
     */
    private String updateTime;
    /**
     * 是否已经上线 （1是，0否）
     */
    private int isOnline;
    /**
     * 是否有版权 （1是，0否）
     */
    private int hasCopyright;
    private String desc;

    /**
     * 订阅的位置, 播放条, 播放器...
     */
    private String location;
    /**
     * 更新数
     */
    private int updateNum;

    public int getUpdateNum() {
        return updateNum;
    }

    public void setUpdateNum(int updateNum) {
        this.updateNum = updateNum;
    }

    /**
     * 收藏专辑总共期数
     */
    private int countNum;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public int getIsOnline() {
        return isOnline;
    }

    public void setIsOnline(int isOnline) {
        this.isOnline = isOnline;
    }

    public int getHasCopyright() {
        return hasCopyright;
    }

    public void setHasCopyright(int hasCopyright) {
        this.hasCopyright = hasCopyright;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public int getCountNum() {
        return countNum;
    }

    public void setCountNum(int countNum) {
        this.countNum = countNum;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }
}
