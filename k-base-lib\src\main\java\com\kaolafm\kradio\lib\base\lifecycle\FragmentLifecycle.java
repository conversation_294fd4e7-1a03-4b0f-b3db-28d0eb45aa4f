package com.kaolafm.kradio.lib.base.lifecycle;

import android.os.Bundle;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import android.util.Log;
import android.view.View;
import com.kaolafm.kradio.lib.base.mvp.IFragment;
import org.greenrobot.eventbus.EventBus;

/**
 * fragment的生命周期回调函数{@link FragmentManager.FragmentLifecycleCallbacks} 默认实现类.
 * 主要是注册了EventBus
 * <AUTHOR>
 * @date 2018/4/13
 */

public class FragmentLifecycle extends FragmentManager.FragmentLifecycleCallbacks {
    private static final String TAG = "FragmentLifecycle";
    @Override
    public void onFragmentPreCreated(FragmentManager fm, Fragment f, Bundle savedInstanceState) {
        Log.d(TAG, "-------------------onCreate Start: " + f.getClass().getSimpleName()+"---------------------------");
        super.onFragmentPreCreated(fm, f, savedInstanceState);
    }

    @Override
    public void onFragmentCreated(FragmentManager fm, Fragment f, Bundle savedInstanceState) {
        Log.d(TAG, "-------------------onCreate Finished: " + f.getClass().getSimpleName()+"---------------------------");
        super.onFragmentCreated(fm, f, savedInstanceState);
        if (f instanceof IFragment){
            if (((IFragment) f).useEventBus()){
                EventBus.getDefault().register(f);
            }
        }
    }

    @Override
    public void onFragmentViewCreated(FragmentManager fm, Fragment f, View v, Bundle savedInstanceState) {
        super.onFragmentViewCreated(fm, f, v, savedInstanceState);
        Log.d(TAG, "-------------------onFragmentViewCreated: " + f.getClass().getSimpleName()+"---------------------------");
    }

    @Override
    public void onFragmentResumed(FragmentManager fm, Fragment f) {
        super.onFragmentResumed(fm, f);
        Log.d(TAG, "-------------------onFragmentResumed: " + f.getClass().getSimpleName()+"---------------------------");
    }

    @Override
    public void onFragmentPaused(FragmentManager fm, Fragment f) {
        super.onFragmentPaused(fm, f);
        Log.d(TAG, "-------------------onFragmentPaused: " + f.getClass().getSimpleName()+"---------------------------");
    }

    @Override
    public void onFragmentStarted(FragmentManager fm, Fragment f) {
        super.onFragmentStarted(fm, f);
        Log.d(TAG, "-------------------onFragmentStarted: " + f.getClass().getSimpleName()+"---------------------------");
    }

    @Override
    public void onFragmentStopped(FragmentManager fm, Fragment f) {
        super.onFragmentStopped(fm, f);
        Log.d(TAG, "-------------------onFragmentStopped: " + f.getClass().getSimpleName()+"---------------------------");
    }


    @Override
    public void onFragmentDestroyed(FragmentManager fm, Fragment f) {
        super.onFragmentDestroyed(fm, f);
        if (f instanceof IFragment){
            if (((IFragment) f).useEventBus()){
                EventBus.getDefault().unregister(f);
            }
        }
        Log.d(TAG, "-------------------onFragmentDestroyed: " + f.getClass().getSimpleName()+"---------------------------");
    }
}
