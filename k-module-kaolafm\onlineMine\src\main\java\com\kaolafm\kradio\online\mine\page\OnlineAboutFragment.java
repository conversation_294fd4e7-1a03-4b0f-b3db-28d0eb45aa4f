package com.kaolafm.kradio.online.mine.page;

import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.net.http.SslError;
import android.os.Build;
import android.os.Bundle;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.webkit.SslErrorHandler;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ImageView;
import android.widget.ScrollView;
import android.widget.TextView;

import com.kaolafm.kradio.common.helper.SkinHelper;
import com.kaolafm.kradio.common.report.ReportUtil;
import com.kaolafm.kradio.common.utils.FlavorUtil;
import com.kaolafm.kradio.k_kaolafm.R; 
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.UserCenterInter;
import com.kaolafm.kradio.lib.base.mvp.IPresenter;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.lib.base.ui.online.BaseViewPagerFragment;
import com.kaolafm.kradio.lib.base.ui.online.BaseViewPagerLazyFragment;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.online.common.utils.LinearGradientFontSpan;
import com.kaolafm.kradio.online.common.web.OnlineWebViewActivity;
import com.trello.rxlifecycle3.LifecycleTransformer;
  
/**
 * 关于页面
 * 蔡佳彬
 */
public class OnlineAboutFragment extends BaseViewPagerLazyFragment {
  
    ImageView ivLogo; 
    TextView mPersonCenterAboutUsVersionTv; 
    TextView mPersonCenterAboutUsDetailTv; 
    TextView web_view_title_tv; 
    ConstraintLayout webViewLayout; 
    WebView webViewContent; 
    ScrollView scroll;

    private static final String TAG = OnlineAboutFragment.class.getSimpleName();

    final static int COUNTS = 10;//点击次数
    final static long DURATION = 2000;//规定有效时间
    long[] mHits = new long[COUNTS];
    private ConstraintSet mConstraintSet;
    private String CUR_PAGE;

    public OnlineAboutFragment() {
        // Required empty public constructor
    }

    @Override
    protected boolean isReportFragment() {
        return true;
    }

    public static OnlineAboutFragment newInstance() {
        OnlineAboutFragment fragment = new OnlineAboutFragment();
//        Bundle args = new Bundle();
//        args.putString(ARG_PARAM1, param1);
//        args.putString(ARG_PARAM2, param2);
//        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
//        if (getArguments() != null) {
//            mParam1 = getArguments().getString(ARG_PARAM1);
//            mParam2 = getArguments().getString(ARG_PARAM2);
//        }
    }


    @Override
    protected int getLayoutId() {
        return R.layout.online_fragment_about;
    }

    @Override
    protected IPresenter createPresenter() {
        return null;
    }

    @Override
    public String getPageId() {
        return Constants.PAGE_ID_MINE_ABOUT;
    }

    @Override
    public void initView(View view) {
        ivLogo=view.findViewById(R.id.person_center_aboutus_tingbanlogo_iv);
        mPersonCenterAboutUsVersionTv=view.findViewById(R.id.person_center_aboutus_version_tv);
        mPersonCenterAboutUsDetailTv=view.findViewById(R.id.person_center_aboutus_details_tv);
        web_view_title_tv=view.findViewById(R.id.web_view_title_tv);
        webViewLayout=view.findViewById(R.id.web_view_layout);
        webViewContent=view.findViewById(R.id.web_view_content);
        scroll=view.findViewById(R.id.scroll);
 
        
        PackageManager manager = getContext().getPackageManager();
        try {
            PackageInfo info = manager.getPackageInfo(getContext().getPackageName(), 0);
            String versionName = info.versionName;
            mPersonCenterAboutUsVersionTv.setText(getString(R.string.person_center_aboutus_version_str, versionName));
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        mPersonCenterAboutUsDetailTv.setText(getString(R.string.person_center_aboutus_details_str));
    }

    private void changeState(boolean b) {
        if (b) {
            scroll.setVisibility(View.VISIBLE);
        } else {
            scroll.setVisibility(View.GONE);
        }
    }
 
    public void onViewClicked(View view) {
        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), true)) {
            return;
        }
        int id = view.getId();

        if (AntiShake.check(view.getId())) {
            return;
        }
        if (id == R.id.person_center_aboutus_service_btn) {
//            if (SkinHelper.isNightMode()) {
//                CUR_PAGE = ResUtil.getString(R.string.http_url_server_agreement_night);
//            } else {
//            CUR_PAGE = ResUtil.getString(R.string.http_url_server_agreement);
//            }
            CUR_PAGE = ResUtil.getString(R.string.http_url_server_agreement);
            String theme = "dark";
            if (SkinHelper.isDayMode()) {
                theme = "light";
            }
            CUR_PAGE += "?theme=" + theme + "&bgColor=transparent&contentSize="
                    + 13
                    + "&showTitle=1";
            OnlineWebViewActivity.start(getActivity(), FlavorUtil.getHttp443Url(CUR_PAGE), getResources().getString(R.string.launcher_agreement0)
                    , Constants.ONLINE_PAGE_ID_ACCOUNT_PROTOCOL);
//            showWebView(FlavorUtil.getHttp443Url(CUR_PAGE));
//            changeState(false);
//            Log.i(TAG, "service=" + CUR_PAGE);
        } else if (id == R.id.person_center_aboutus_secret_btn) {
            String theme = "dark";
            if (SkinHelper.isDayMode()) {
                theme = "light";
            }
            CUR_PAGE = ResUtil.getString(R.string.http_url_policy);
            CUR_PAGE += "?theme=" + theme + "&bgColor=transparent&contentSize="
                    + 13
                    + "&showTitle=1";
            OnlineWebViewActivity.start(getActivity(), FlavorUtil.getHttp443Url(CUR_PAGE), getResources().getString(R.string.launcher_agreement1)
                    , Constants.ONLINE_PAGE_ID_ACCOUNT_PRIVATE);
//            showWebView(FlavorUtil.getHttp443Url(CUR_PAGE));
//            changeState(false);
            Log.i(TAG, "policy=" + CUR_PAGE);
        } else if (id == R.id.web_view_back) {
            startTime = System.currentTimeMillis();
            CUR_PAGE = "";
            ViewUtil.setViewVisibility(webViewLayout, View.GONE);
            ReportUtil.addPageShowEvent(startTime, Constants.ONLINE_PAGE_ID_MINE_ABOUT);
            startTime = -1;
            changeState(true);
        }
    }

    private void showWebView(String url) {

        if (webViewContent == null) {
            return;
        }
        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), true)) {
            return;
        }
//        KaolaAppConfigData kaolaAppConfigData = KaolaAppConfigData.getInstance();
//        StringBuilder sb = new StringBuilder(url);
//        String appId = kaolaAppConfigData.getAppId();
//        sb.append(appId);
//        //支持黑夜模式和白天模式
        ViewUtil.setViewVisibility(webViewLayout, View.VISIBLE);
//        UserCenterInter userCenterInter = ClazzImplUtil.getInter("UserCenterInterImpl");
//        if (userCenterInter != null && userCenterInter.supportDarkLightSwitch()) {
//            sb.append("&theme=");
//            sb.append(userCenterInter.getTheme(getContext()));
////            int skin = getContext().getResources().getConfiguration().uiMode;
////            if (TextUtils.equals(skin, "night")) {
////                sb.append("&theme=dark");
////            } else {
////                sb.append("&theme=light");
////            }
//            webViewContent = userCenterInter.getWebView(getActivity(), sb.toString());
//            webViewContent.setWebViewClient(null);
//            userCenterInter.setWebViewLongClick(webViewContent);
//        }
        setWebViewSetting();
        webViewContent.clearView();
        String httpUrl = url + "?theme=dark&bgColor=transparent&contentSize="
                + 13
                + "&showTitle=1";
        curPageUrl = httpUrl;
        Log.i("showWebView", "httpUrl:" + httpUrl);
        webViewContent.setVisibility(View.INVISIBLE);
        webViewContent.loadUrl(httpUrl);
        startTime = System.currentTimeMillis();
    }

    String curPageUrl = "";


    public void setWebViewSetting() {
        if (webViewContent == null) {
            return;
        }
        WebSettings settings = webViewContent.getSettings();
        settings.setUseWideViewPort(true);
        settings.setJavaScriptEnabled(true);
        settings.setSavePassword(false);
        if (Build.VERSION.SDK_INT < 19 && webViewContent != null) {
            webViewContent.removeJavascriptInterface("searchBoxJavaBridge_");
            webViewContent.removeJavascriptInterface("accessibility");
            webViewContent.removeJavascriptInterface("accessibilityTraversal");
        }
        settings.setTextZoom(ResUtil.getInt(R.integer.online_web_view_zoom_size));
        //自动加载图片
        settings.setLoadsImagesAutomatically(true);
        settings.setAppCacheEnabled(false);
        settings.setCacheMode(WebSettings.LOAD_NO_CACHE);//不使用缓存，只从网络获取数据.
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            // 5.0以上允许加载http和https混合的页面(5.0以下默认允许，5.0+默认禁止)
            settings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        }
        webViewContent.setBackgroundColor(Color.TRANSPARENT);
        webViewContent.setLayerType(View.LAYER_TYPE_SOFTWARE, null);
        webViewContent.setDrawingCacheEnabled(false);
        webViewContent.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                //可处理loading开始
                super.onPageStarted(view, url, favicon);
            }

            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
//                return super.shouldOverrideUrlLoading(view, url);
                UserCenterInter userCenterInter = ClazzImplUtil.getInter("UserCenterInterImpl");
                if (userCenterInter != null && userCenterInter.supportDarkLightSwitch() && !TextUtils.isEmpty(CUR_PAGE) && !url.startsWith(CUR_PAGE)) {
                    url = curPageUrl;
                }
                view.loadUrl(url);
                return true;
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                //可处理loading结束
                super.onPageFinished(view, url);
                Log.i("showWebView", "onPageFinished url :" + url);
                if (null != webViewContent) {
                    webViewContent.setVisibility(View.VISIBLE);
                    web_view_title_tv.setText(view.getTitle());
                }

            }

            @Override
            public void onReceivedSslError(WebView view, SslErrorHandler handler, SslError error) {
                handler.cancel();
            }
        });
        webViewContent.setWebChromeClient(new WebChromeClient());
    }


    public SpannableStringBuilder getRadiusGradientSpan(String string, int startColor, int endColor) {
        SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder(string);
        LinearGradientFontSpan span = new LinearGradientFontSpan(startColor, endColor);
        spannableStringBuilder.setSpan(span, 0, spannableStringBuilder.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spannableStringBuilder;

    }

    @Override
    protected void lazyLoad() {

    }

}