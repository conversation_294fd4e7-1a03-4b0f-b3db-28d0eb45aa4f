package com.kaolafm.kradio.online.home;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.util.Log;
import android.view.View;
import android.view.ViewStub;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.kaolafm.ad.KradioAdAudioManager;
import com.kaolafm.ad.conflict.AdConflict;
import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.ads.image.AdvertisingImagerImpl;
import com.kaolafm.base.utils.NetworkMonitor;
import com.kaolafm.kradio.common.KradioSDKManager;
import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.common.report.ReportParamUtil;
import com.kaolafm.kradio.common.report.ReportUtil;
import com.kaolafm.kradio.common.widget.ring.Ring;
import com.kaolafm.kradio.common.widget.ring.RingManager;
import com.kaolafm.kradio.k_kaolafm.R; 
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.ICheckUpgraderInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioPlayLogicInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioAuthInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioC211ViewSizeInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioInitPlayerInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioMultiWindowInter;
import com.kaolafm.kradio.lib.base.flavor.ThirdPlatformLoginer;
import com.kaolafm.kradio.lib.common.ResType;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.ClazzUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.RecyclerViewExposeUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.online.common.base.MBaseShowHideFragment;
import com.kaolafm.kradio.online.common.event.CancelJumpToOnlinePlayerEvent;
import com.kaolafm.kradio.online.common.event.JumpToOnlinePlayerEvent;
import com.kaolafm.kradio.online.common.event.OnlinePlayerFragmentJumpActionEvent;
import com.kaolafm.kradio.online.common.player.PlayerHelper;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.online.home.data.HomePlayerModel;
import com.kaolafm.kradio.online.home.mvp.HomePlayerContract;
import com.kaolafm.kradio.online.home.mvp.HomePlayerPresenter;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.OpenSDK;
import com.kaolafm.opensdk.api.operation.model.column.ColumnGrp;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List; 

import static com.kaolafm.kradio.lib.utils.Constants.CLIENT_EXTRA_TYPE;
import static com.kaolafm.kradio.lib.utils.Constants.INVALID_NUM;
import static com.kaolafm.kradio.lib.utils.Constants.SEARCH_BY_KEYWORDS_EXTRA_TYPE;


/**
 * 首页-猜你喜欢
 * 蔡佳彬
 */
@Route(path = RouterConstance.ONLINE_URL_HOME_LIKE)
public class HomeFragment extends MBaseShowHideFragment<HomePlayerPresenter>
        implements HomePlayerContract.View, RecyclerViewExposeUtil.OnItemExposeListener, NetworkMonitor.OnNetworkStatusChangedListener {

    private static final String TAG = "HomeFragment";
 
    Ring mRing; 
    ViewStub mLoading; 
    ViewStub mErrorPage;

    public View mErrorRl;
    public View mLoadingCL;

    private KRadioAudioPlayLogicInter mKRadioAudioPlayLogicInter;


    /**
     * 跳转到播放页的Handler消息，广播和直播收听10秒后跳转，专辑不主动跳转。三种都支持点击进入播放页
     */
    public static final int HANDLER_GOTO_PLAYER_ACTIVITY = 12345;
    /**
     * 正在播放的HomeCell
     */
    private HomeCell mPlayingHomeCell;

    public HomeFragment() {
        // Required empty public constructor
    }

    public static HomeFragment newInstance() {
        HomeFragment fragment = new HomeFragment();
//        Bundle args = new Bundle();
//        args.putString(ARG_PARAM1, param1);
//        args.putString(ARG_PARAM2, param2);
//        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected boolean isReportFragment() {
        return true;
    }

    @Override
    protected int getLayoutId() {
        return R.layout.online_fragment_home;
    }

    @Override
    protected HomePlayerPresenter createPresenter() {
        return new HomePlayerPresenter(getContext(), this, HomePlayerModel.getInstance());
    }

    @Override
    public String getPageId() {
        return Constants.PAGE_ID_HOME_LIKE;
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        mKRadioAudioPlayLogicInter = ClazzImplUtil.getInter("KRadioAudioPlayLogicImpl");
    }

    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
        mKRadioAudioPlayLogicInter = ClazzImplUtil.getInter("KRadioAudioPlayLogicImpl");
//        KradioAdAudioManager.getInstance().mainActivityStart(activity);
        PlayerManagerHelper.getInstance().finishAudioAd();
        Intent intent = activity.getIntent();
        boolean isImageSKip = intent.getBooleanExtra("isImageSkip", false);
        boolean isSkip = intent.getBooleanExtra("isSkip", false);
        AdvertisingImagerImpl advertisingImager = (AdvertisingImagerImpl) AdvertisingManager.getInstance().getImager();
        if (advertisingImager != null) {
            advertisingImager.displayAttachedImage(getActivity(), isSkip, isImageSKip);
        }
    }

    private void initPlayer() {
        PlayerManager playerManager = PlayerManager.getInstance();
        boolean isInitSuccess = playerManager.isPlayerInitSuccess();
        if (isInitSuccess) {
            checkPresenterIsNull();
            mPresenter.onPlayerInitSuccess();
        } else {
            playerManager.addPlayerInitComplete(new HomeOnPlayerInitCompleteListener(this));
        }
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        AdConflict.isShowHome = true;
    }

    /**
     * 主要为了解决Monkey测试出现的空指针问题
     */
    private void checkPresenterIsNull() {
        if (mPresenter == null) {
            mPresenter = createPresenter();
        }
    }

    public void onPlayerInitComplete(boolean isSuccess) {
        Log.i(TAG, "onPlayerInitComplete-------->isSuccess = " + isSuccess);
        if (isSuccess) {
            checkPresenterIsNull();
            mPresenter.onPlayerInitSuccess();
            if (mPresenter != null) {
                mPresenter.onPlayerInitSuccess();
                KRadioInitPlayerInter inter = ClazzImplUtil.getInter("KRadioInitPlayerImpl");
                if (inter != null && inter.isInit() && KradioAdAudioManager.getInstance().getAdvertPlayerimpl() == null) {
                    initPlayerContext();
                }
            }
        }
    }

    @Override
    public void initView(View view) {

        mRing = view.findViewById(R.id.ring);
        mLoading = view.findViewById(R.id.loading);
        mErrorPage = view.findViewById(R.id.error_page);
   
        
        
        NetworkMonitor.getInstance(getContext()).registerNetworkStatusChangeListener(this);
        mRing.setPageId(Constants.PAGE_ID_HOME_LIKE);
        mRing.setOnSelectListener(new Ring.OnSelectListener() {
            @Override
            public void onSelect(int postion) {
                if (postion >= RingManager.getInstance().getDatas().size()) return;
                onItemClick(null, RingManager.getInstance().getDatas().get(postion), postion);
            }
        });
        initPlayerContext();
        initData();
    }

    public void initData() {
        if (mLoading != null) {
            showLoading();
        }
        mPresenter.start();
    }

    @Override
    public void onResume() {
        super.onResume();
        if (mRing != null && PlayerManager.getInstance().isPlaying()) {
            mRing.playAnim();
        }
        if (mErrorRl != null && mErrorRl.getVisibility() == View.VISIBLE) {
            if (getActivity() instanceof MainActivity) {
                ((MainActivity) getActivity()).mainFrameLayout.bringToFront();
            }
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        if (mRing != null) {
            mRing.pauseAnim();
        }
    }

    /**
     * 根据横竖屏显示。
     */
    @Override
    protected void showAccordingToScreen(int orientation) {
        super.showAccordingToScreen(orientation);
        //内容recycleview的右边间距。横屏为零，竖屏为30
        int contentRight = ResUtil.getDimen(R.dimen.online_home_content_padding_right);
        int contentBottom = ResUtil.getDimen(R.dimen.online_home_content_padding_bottom);
        //竖屏的时候top的padding为零，因为在HomeItemDecoration中做了便宜。
        int contentTop = ResUtil.getDimen(R.dimen.online_home_content_padding_top);

        KRadioC211ViewSizeInter inter = ClazzImplUtil.getInter("KRadioC211ViewSizeImpl");
        if (inter != null && inter.isNeedReset()) {
            contentBottom = ResUtil.getDimen(R.dimen.y35);
            contentTop = ResUtil.getDimen(R.dimen.y10);
        }

    }

    @Override
    public void onStatusChanged(int newStatus, int oldStatus) {
        Log.i(TAG, "onStatusChanged: : " + newStatus);
        if (newStatus == NetworkMonitor.STATUS_MOBILE || newStatus == NetworkMonitor.STATUS_WIFI) {
            initPlayerContext();
            initData();
        }
    }

    @Override
    public void showContent(List<ColumnGrp> columnGrps) {
        Log.d("caicai", "ColumnGrp.size---------" + columnGrps.size());
        Log.d("caicai", "ColumnGrp---------" + columnGrps);
        hideErrorLayout();
        hideLoading();

        List<HomeCell> cells = RingManager.getInstance().transColumnGrpsToHomeCells(columnGrps);
        Log.d("caicai", "cells.size---------" + cells.size());
        Log.d("caicai", "cells---------" + cells);

        RingManager.getInstance().setDatas(cells);
        Log.d("caicai", "RingManager.cells.size---------" + RingManager.getInstance().getDatas().size());
        Log.d("caicai", "RingManager.cells---------" + RingManager.getInstance().getDatas());

        Log.d("Ring.rsq", "----showContent-----");
        mRing.notifyDataSetChanged();
    }

    @Override
    public void showLoading() {
        if (mLoading != null) {
            if (mLoadingCL == null) {
                mLoadingCL = mLoading.inflate();
            }
        }
        ViewUtil.setViewVisibility(mLoadingCL, View.VISIBLE);
    }

    @Override
    public void hideLoading() {
        ViewUtil.setViewVisibility(mLoadingCL, View.GONE);
    }

    @Override
    public void showError(String error) {
        Log.i(TAG, "showError----->error = " + error);
        hideLoading();
        if (getActivity() instanceof MainActivity) {
            ((MainActivity) getActivity()).mainFrameLayout.bringToFront();
        }
        showNoNetWorkView(error);
    }

    @Override
    public void hideErrorLayout() {
        Log.i(TAG, "hideErrorLayout");
        ViewUtil.setViewVisibility(mErrorRl, View.GONE);
        if (getActivity() instanceof MainActivity) {
            ((MainActivity) getActivity()).roadIv.bringToFront();
            ((MainActivity) getActivity()).playBar.bringToFront();
        }
    }

    @Override
    public void onItemViewVisible(boolean visible, int position) {

    }

    public void showNoNetWorkView(String error) {
        if (mErrorPage != null) {
            if (mErrorRl == null) {
                mErrorRl = mErrorPage.inflate();
            }
            if (StringUtil.isNotEmpty(error)) {
                TextView tvError = mErrorRl.findViewById(R.id.tv_error);
                tvError.setText(error);
            }
            // 支持点击重试
            mErrorRl.setOnClickListener(v -> {
                hideErrorLayout();
                if (!OpenSDK.getInstance().isActivate()) {
                    KradioSDKManager.getInstance().addUsableObserver(() -> {
                        initPlayerContext();
                        initData();
                    });
                    KradioSDKManager.getInstance().initAndActivate();
                } else {
                    initPlayerContext();
                    initData();
                }
            });
        }
        ViewUtil.setViewVisibility(mErrorRl, View.VISIBLE);
    }

    private boolean isJumpingToPlayerFragment = false;

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void observeEvent(OnlinePlayerFragmentJumpActionEvent event) {
        isJumpingToPlayerFragment = event.getAction() == OnlinePlayerFragmentJumpActionEvent.ACTION_START;
    }

    @Override
    public boolean useEventBus() {
        return true;
    }

    /**
     * Item的点击事件处理。这里有渠道使用到了AOP进行鉴权拦截。
     */
    private void onItemClick(View view1, HomeCell homeCell, int position) {
        //在所有播放事件开始前发送一次取消延时跳转播放页的逻辑，防止加载过程中跳转到了播放页导致播放页内容与光圈数据不一致
        EventBus.getDefault().post(new CancelJumpToOnlinePlayerEvent());
        if (isJumpingToPlayerFragment) {
            return;
        }

        //点击事件上报
        ReportUtil.addContentClickEvent(homeCell.getPlayId() + "", ReportParamUtil.getRadioType(homeCell.getResType()),
                homeCell.isVip() || homeCell.isFine() ? "1" : "0",
                String.valueOf(homeCell.playId), ReportParamUtil.getEventTag(homeCell.isVip(), homeCell.isFine()),
                getPageId(), homeCell.code, "" + position);
        //内容曝光上报
//        ReportUtil.addContentShowEvent(homeCell.getPlayId() + "", ReportParamUtil.getRadioType(homeCell.resType),
//                homeCell.isVip() || homeCell.isFine() ? "1" : "0",
//                String.valueOf(homeCell.playId), ReportParamUtil.getEventTag(homeCell.isVip(), homeCell.isFine()),
//                getPageId(), homeCell.code, "" + position);

        if (homeCell.resType == ResType.LIVE_TYPE) {
//            if (mKRadioAuthInter != null && !mKRadioAuthInter.authStatus()) {
//                Log.i(TAG, "onCardClick: mKRadioAuthInter.authStatus() = " + mKRadioAuthInter.authStatus());
//                //如果流量鉴权不通过，进行流量鉴权的check,不要直接跳转到直播界面
//                mKRadioAuthInter.doCheckAuth(homeCell, KRadioAuthInter.METHOD_LIVING, true);
//            } else {
            if (PlayerManagerHelper.getInstance().isPlayCurrentRadio(String.valueOf(homeCell.playId))) {
                return;
            }
            clickToPlay(homeCell);
//            }
        } else if (homeCell.resType == ResType.FUNCTION_ENTER_SMALL) {

//            AllCategoriesFragment allCategoriesFragment = (AllCategoriesFragment) RouterManager.getInstance()
//                    .getRouterFragment(RouterConstance.FRAGMENT_URL_CATEGORIES);
//            allCategoriesFragment.setCode(Long.parseLong(homeCell.firstCode), Long.parseLong(homeCell.secondCode));
////            loadRootFragment(R.id.online_main_fl,
////                    allCategoriesFragment,
////                    false, false);
////            if (getActivity() instanceof MainActivity) {
////                ((MainActivity) getActivity()).changePage(allCategoriesFragment);
////            }
//            EventBus.getDefault().post(allCategoriesFragment);
        } else {
            if (PlayerManagerHelper.getInstance().isPlayCurrentRadio(String.valueOf(homeCell.playId))) {
                return;
            }
            //从分类点击播放之后会添加list，但是从光圈切换到广播后不会清楚list，导致上下一曲不对，所以在播放之前清除list'
            if (homeCell.resType == ResType.BROADCAST_TYPE||homeCell.resType == ResType.TV_TYPE)
                PlayerManagerHelper.getInstance().addBroadcastRadioSimpleItems(new ArrayList<>());

//                ReportUtil.addRecommendSelectEvent(homeCell.outputMode, homeCell.callBack);
            clickToPlay(homeCell);
        }
        Log.i(TAG, "click end : id = " + homeCell.playId);

    }

    public void initPlayerContext() {
        Log.i(TAG, "initPlayerContext......");
        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
            return;
        }
//        if (isInitPlayerContext) {
//            return;
//        }
//        isInitPlayerContext = true;
        Activity activity = getActivity();
        boolean hasPlayExtra = false;
        if (activity != null) {
            Intent intent = activity.getIntent();
            int extraType = INVALID_NUM;
            try {
                extraType = intent.getIntExtra(CLIENT_EXTRA_TYPE, INVALID_NUM);
            } catch (Exception e) {
            }
            Log.i(TAG, "demoInitPBP--------->extraType = " + extraType);
            if (extraType == SEARCH_BY_KEYWORDS_EXTRA_TYPE) {
                hasPlayExtra = true;
            }
        }
        //此处逻辑跟120版本差距较大，后期需要合并120新增逻辑
        PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
        if (hasPlayExtra || (playItem != null && playItem.getType() != PlayerConstants.RESOURCES_TYPE_INVALID)) {
            // 播放器 还在播放,启动考拉
            Log.i(TAG, "demoInitPBP--------->" + playItem);
            ThirdPlatformLoginer thirdPlatformLoginer = ClazzImplUtil.getInter("ThirdPlatformLoginerImpl");
            if (thirdPlatformLoginer != null) {
                //playHistory();
                thirdPlatformLoginer.login(AppDelegate.getInstance().getContext().getApplicationContext(),
                        new ThirdPlatformLoginer.Callback() {
                            @Override
                            public void onSuccess() {
                            }

                            @Override
                            public void onFailure() {
                            }
                        });
            }
            if (mKRadioAudioPlayLogicInter != null) {
                mKRadioAudioPlayLogicInter.doStartInPlay();
            }
        } else {
            // 正常的app 退出逻辑
            if (mKRadioAudioPlayLogicInter != null) {
                //播放指定渠道中的播放逻辑
                boolean status = mKRadioAudioPlayLogicInter.autoPlayAudio(AppDelegate.getInstance().getContext());
                if (!status) {
//                    checkPresenterIsNull();
                    mPresenter.autoPlay();
                } else {
                    ThirdPlatformLoginer thirdPlatformLoginer = ClazzImplUtil
                            .getInter("ThirdPlatformLoginerImpl");
                    if (thirdPlatformLoginer != null) {
                        //playHistory();
                        thirdPlatformLoginer.login(AppDelegate.getInstance().getContext().getApplicationContext(),
                                new ThirdPlatformLoginer.Callback() {
                                    @Override
                                    public void onSuccess() {
                                    }

                                    @Override
                                    public void onFailure() {
                                    }
                                });
                    }
                }
            } else {
                checkPresenterIsNull();
                mPresenter.autoPlay();
            }

            ICheckUpgraderInter mICheckUpgraderInter = ClazzImplUtil.getInter("CheckUpgraderImpl");
            if (mICheckUpgraderInter != null) {
                mICheckUpgraderInter.checkUpgrade(false);
            }
        }
    }

    private void clickToPlay(HomeCell homeCell) {
        PlayerHelper.play(homeCell);

        //只有广播和直播才会跳转
        if (homeCell.resType != PlayerConstants.RESOURCES_TYPE_BROADCAST &&
                homeCell.resType != PlayerConstants.RESOURCES_TYPE_TV &&
                homeCell.resType != PlayerConstants.RESOURCES_TYPE_ALBUM &&
                homeCell.resType != PlayerConstants.RESOURCES_TYPE_RADIO &&
                homeCell.resType != PlayerConstants.RESOURCES_TYPE_LIVING) {
            return;
        }
        mPlayingHomeCell = homeCell;

        //更新节目id
        long playId = mPlayingHomeCell.playId;
        //发送延迟事件
        EventBus.getDefault().post(new JumpToOnlinePlayerEvent(playId, true));
    }


    /**
     * 是否禁用节目切换
     *
     * @param enable
     */
    public void setEnablePlayItemChange(boolean enable) {
        mRing.setTouchEnable(enable);
    }

    public int getRingWidth() {
        return mRing.getMeasuredWidth();
    }

    public int getRingHeight() {
        return mRing.getMeasuredHeight();
    }

    public int getRingMarginBottom() {
        return ((FrameLayout.LayoutParams) mRing.getLayoutParams()).bottomMargin;
    }
}