package com.kaolafm.kradio.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.kaolafm.kradio.lib.utils.ClazzImplUtil;

/**
 * 比亚迪账号发起绑定三方账号查询三方登录的账号信息广播（定向静态广播）
 */
public class BydThirdAccountBindReceiver extends BroadcastReceiver {
    private final String ACCOUNT_STATE_ACTION =  "com.byd.action.query_tri_part_account_state";

    private String TAG = "BydThirdAccountBindReceiver";

    @Override
    public void onReceive(Context context, Intent intent) {
        if (ACCOUNT_STATE_ACTION.equals(intent.getAction())){
            Log.i(TAG,"onReceive");
            AccountInterworkInter mAccountInterworkInter = ClazzImplUtil.getInter("AccountInterworkImpl");
            if(mAccountInterworkInter != null && mAccountInterworkInter.isOpenThirdPartyAccount()){
                Log.i(TAG,"onReceive，setAccountBind");
                mAccountInterworkInter.setAccountBind(1);
            }
        }
    }
}
