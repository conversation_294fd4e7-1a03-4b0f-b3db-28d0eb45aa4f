package com.kaolafm.kradio.flavor.impl;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.util.Log;

import com.szlanyou.usercenter.IUserInterface;

/**
 * <AUTHOR>
 **/
public class RiChanHelper {

    private static final String TAG = "RiChanHelper";
    private volatile static RiChanHelper mInstance;
    private Context mContext;
    private IUserInterface mService;
    private ServiceConnection mConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            mService = IUserInterface.Stub.asInterface(service);
            Log.i(TAG, "onServiceConnected:" + name);
            try {
                service.linkToDeath(mDeathRecipient, 0);
                Log.i(TAG, "linkToDeath start");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {

            Log.i(TAG, "onServiceDisconnected:" + name);

        }
    };

    private IBinder.DeathRecipient mDeathRecipient = new IBinder.DeathRecipient() {
        @Override
        public void binderDied() {
            Log.i(TAG, "binderDied start");
            if (mService != null) {
                mService.asBinder().unlinkToDeath(mDeathRecipient, 0);
            }
            init();
        }
    };

    private RiChanHelper(Context context) {
        mContext = context;
    }

    public static RiChanHelper getInstance(Context context) {
        if (mInstance == null) {
            synchronized (RiChanHelper.class) {
                if (mInstance == null) {
                    mInstance = new RiChanHelper(context);
                }
            }
        }
        return mInstance;
    }


    public boolean init() {
        Log.i(TAG, "init start ");
        Intent intent = new Intent("com.szlanyou.usercenter.service.JiDouUserService");
        intent.setPackage("com.szlanyou.usercenter");
        intent.setAction("com.szlanyou.usercenter.AIDL_USERINFO_ACTION");
        return mContext.bindService(intent, mConnection, Context.BIND_AUTO_CREATE);
    }

    /**
     * 判断日产的[个人中心]是否登录
     *
     * @return
     */
    public boolean getLoginStatus() {
        boolean rst = false;
        try {
            String loginStatus = mService.getLoginStatus();
            Log.i(TAG, "getLoginStatus: loginStatus=" + loginStatus);
            //返回结果只是一个String类型的字符串，1代表已登录，0代表未登录
            rst = "1".equals(loginStatus);
        } catch (Exception e) {
            e.printStackTrace();
            Log.i(TAG, "getLoginStatus: error =" + e.getLocalizedMessage());
            init();
        }

        return rst;
    }

    /**
     * 显示日产[个人中心-登录]页面
     */
    public void showLoginPage() {
        Intent intent = new Intent();
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.setClassName("com.szlanyou.usercenter", "com.szlanyou.usercenter.view.user.QRCodeActivity");
        intent.putExtra("sourceName", "kaola");
        mContext.startActivity(intent);
        Log.i(TAG, "showLoginPage start");
    }
}
