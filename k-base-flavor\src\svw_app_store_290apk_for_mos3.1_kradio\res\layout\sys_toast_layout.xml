<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"

    android:gravity="center"
    android:paddingStart="@dimen/x20"
    android:paddingEnd="@dimen/x20">

    <TextView
        android:id="@+id/toast_msg"
        android:layout_width="wrap_content"

        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        android:paddingRight="20dp"
        android:paddingLeft="20dp"
        android:background="@drawable/shape_sys_toast_layout_bg"
        android:layout_height="@dimen/y90"
        android:gravity="center"
        android:shadowColor="#BB000000"
        android:shadowRadius="2.75"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size3"
        tools:text="我是Toast" />
</LinearLayout>
