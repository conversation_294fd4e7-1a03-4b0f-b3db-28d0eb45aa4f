package com.kaolafm.kradio.common.http;

import android.util.Log;

import com.kaolafm.ad.AdvertOptions;
import com.kaolafm.kradio.common.http.vehicle.KlSdkVehicle;
import com.kaolafm.kradio.lib.BuildConfig;
import com.kaolafm.kradio.lib.base.flavor.InterceptOptions;
import com.kaolafm.kradio.lib.base.flavor.LoginHttpUrl;
import com.kaolafm.kradio.lib.base.flavor.RequestOptions;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.opensdk.Options;
import com.kaolafm.opensdk.api.login.KRadioApiConstants;
import com.kaolafm.opensdk.utils.BaseHttpsStrategy;

import okhttp3.HttpUrl;
import okhttp3.HttpUrl.Builder;
import okhttp3.Request;
import okhttp3.Response;

/**
 * @ClassName RequestInterceptManager
 * @Description 网络请求拦截器, 替换 获取验证码, 用验证码登录, 退出登录与 获取token请求的sign值
 * <AUTHOR>
 * @Date 2020-02-11 17:59
 * @Version 1.0
 */
public class RequestInterceptManager {
    public static final String TAG = "RequestInterceptManager";

    private RequestOptions mRequestOptions;
    private InterceptOptions mInterceptOptions;
    private LoginHttpUrl mLoginHttpUrl;

    class RequestOptionsDefault implements RequestOptions {
        public class DefaultHttpsStrategy extends BaseHttpsStrategy {
            @Override
            public void updateChannelHttpsStrategy() {
                this.mHttpsMap.put("getHost", true);
                this.mHttpsMap.put("replaceUrl", true);
                this.mHttpsMap.put("adParam", true);
                this.mHttpsMap.put("media", true);
                this.mHttpsMap.put("httpsPort", false);
            }
        }

        @Override
        public BaseHttpsStrategy getHttpsStrategy() {
            return new DefaultHttpsStrategy();
        }
    }

    private RequestInterceptManager() {
        mRequestOptions = ClazzImplUtil.getInter("RequestOptionsImpl");
        if (mRequestOptions == null) {
            mRequestOptions = new RequestOptionsDefault();
        }
        mInterceptOptions = ClazzImplUtil.getInter("InterceptOptionsImpl");
    }

    private static final class INNER_CLASS {
        private static RequestInterceptManager S_INSTANCE = new RequestInterceptManager();
    }

    public static RequestInterceptManager getInstance() {
        return INNER_CLASS.S_INSTANCE;
    }

    public Options getSDKOptions() {
        String carType = KlSdkVehicle.getInstance().getCarType();
        Log.i(TAG, "carType = " + carType);
        Options options = new AdvertOptions.Builder()
                .carType(carType)
                .deviceType(1)
                .reportVersion(generateVersion())
                .reportChannel(generateChannel())
                .useHttps(getHttpsStrategy())
                .interceptor(chain -> {
                    Request request = chain.request();
//                    Log.i(TAG, "intercept url= " + request.url());
                    Request newRequest = request;
                    if (hasNeedInterceptRequest(request.url().toString())) {
                        newRequest = intercept(request);
                    }

                    Request portRequest = interceptPort(newRequest);

                    Log.i(TAG, "替换完成 url= " + portRequest.url() + " timestamp:" + System.currentTimeMillis());
                    Response r = chain.proceed(portRequest);
                    r = getHttpsInterceptor(r);
                    return r;
                }).build();


        return options;
    }

    //TODO：临时先内部生成吧
    private String generateVersion() {
        String flavor = generateChannel();
        String buildType = BuildConfig.BUILD_TYPE;
        String versionName = BuildConfig.VERSION_NAME;
        String versionSuffix = versionName.substring(versionName.length() - 4);

        return flavor + "_" + buildType + "_30012" + versionSuffix;
    }

    private String generateChannel(){
        return BuildConfig.FLAVOR.replace("_kradio", "");
    }

    public BaseHttpsStrategy getHttpsStrategy() {
        if (mRequestOptions != null) {
            return mRequestOptions.getHttpsStrategy();
        }
        return null;
    }

    public Response getHttpsInterceptor(Response r) {
        if (mInterceptOptions != null) {
            return mInterceptOptions.getHttpsIntercept(r);
        }
        return r;
    }

    /**
     * 是否包含要拦截的请求
     *
     * @param url
     * @return
     */
    private boolean hasNeedInterceptRequest(String url) {
        if (url.contains(KRadioApiConstants.REQUEST_VERIFY_CODE_URL)) {
            Log.i(TAG, "拦截到 获取验证码 请求");
            return true;
        }
        if (url.contains(KRadioApiConstants.REQUEST_LOGIN_URL)) {
            Log.i(TAG, "拦截到 登录 请求");
            return true;
        }
        if (url.contains(KRadioApiConstants.REQUEST_LOGOUT_URL)) {
            Log.i(TAG, "拦截到 退出登录 请求");
            return true;
        }
        if (url.contains(KRadioApiConstants.REQUEST_TOKEN_URL)) {
            Log.i(TAG, "拦截到 token 请求");
            return true;
        }
        return false;
    }

    /**
     * 替换签名
     *
     * @param request
     * @return
     */
    private Request intercept(Request request) {
        HttpUrl oldHttpUrl = request.url();
        Builder builder = oldHttpUrl
                .newBuilder()
                .scheme(oldHttpUrl.scheme())
                .host(oldHttpUrl.host())
                .port(oldHttpUrl.port())
                .setQueryParameter(KRadioApiConstants.KEY_SIGN, SignUtil.makeSign());
        if (mLoginHttpUrl != null) {
            mLoginHttpUrl.newBuild(builder, oldHttpUrl);
        }
        HttpUrl newFullUrl = builder.build();

        return request.newBuilder().url(newFullUrl).build();
    }

    public void setLoginHttpUrl(LoginHttpUrl loginHttpUrl) {
        mLoginHttpUrl = loginHttpUrl;
    }

    private Request interceptPort(Request request) {
        mRequestOptions = ClazzImplUtil.getInter("RequestOptionsImpl");
        if (mRequestOptions == null) {
            mRequestOptions = new RequestOptionsDefault();
            return request;
        }
        HttpUrl oldHttpUrl = request.url();
        String scheme = oldHttpUrl.scheme();
        int port = oldHttpUrl.port();
        if (mRequestOptions != null && !mRequestOptions.getHttpsStrategy().isHttps(BaseHttpsStrategy.REPLACE_URL)) {
            scheme = "http";
            port = 443;
        }

        Builder builder = oldHttpUrl
                .newBuilder()
                .scheme(scheme)
                .host(oldHttpUrl.host())
                .port(port);
        if (mLoginHttpUrl != null) {
            mLoginHttpUrl.newBuild(builder, oldHttpUrl);
        }
        HttpUrl newFullUrl = builder.build();

        return request.newBuilder().url(newFullUrl).build();
    }
}

