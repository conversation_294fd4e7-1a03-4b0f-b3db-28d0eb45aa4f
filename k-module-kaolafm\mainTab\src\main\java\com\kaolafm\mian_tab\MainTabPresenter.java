package com.kaolafm.mian_tab;

import android.os.Handler;
import android.os.Looper;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.YTLogUtil;
import com.kaolafm.opensdk.api.maintab.model.MainTabBean;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import java.util.List;


public class MainTabPresenter extends BasePresenter<MainTabModel, IMainTabView> {
    private static final String TAG = "MainTabPresenter";

    public MainTabPresenter(IMainTabView view) {
        super(view);
    }

    @Override
    protected MainTabModel createModel() {
        return new MainTabModel();
    }

    public void loadDate() {
        getTabList();
    }

    private void getTabList() {
        YTLogUtil.logStart(TAG, "getTabList", "");
        if (NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)){
            if (mView != null) {
                mView.onLoading();
            }
        } else {
            if (mView != null) {
                mView.onTabDateError(ResUtil.getString(R.string.home_network_nosigin_or_retry));
            }
            return;
        }

//        CompletableFuture<List<MainTabBean>> cachedDataFuture = CompletableFuture.supplyAsync(this::waitForCachedDataWithTimeout);
//        CompletableFuture<List<MainTabBean>> serverDataFuture = CompletableFuture.supplyAsync(this::fetchFromServer);
//
//        // 只处理第一个返回的数据
//        CompletableFuture.anyOf(cachedDataFuture, serverDataFuture).thenAccept(result -> {
//            new Handler(Looper.getMainLooper()).post(() -> {
//                if (result != null) {
//                    processData((List<MainTabBean>) result);
//                }
//            });
//        });
//        YTDataCache.fetchTabList().thenAccept(result -> {
//            new Handler(Looper.getMainLooper()).post(() -> {
//                if (result != null) {
//                    processData(result);
//                }
//            });
//        });
        mModel.getTabList(new HttpCallback<List<MainTabBean>>() {

            @Override
            public void onSuccess(List<MainTabBean> mainTabBeans) {
                processData(mainTabBeans);
            }

            @Override
            public void onError(ApiException e) {
                YTLogUtil.logStart(TAG, "getTabList", "onError" + e.getCode() + " " + e.getMessage());
            }
        });
    }

//    private List<MainTabBean> waitForCachedDataWithTimeout() {
//        YTLogUtil.logStart(TAG, "waitForCachedDataWithTimeout", "");
//        for (int tryCount = 0; tryCount < 10; tryCount++) {
//            List<MainTabBean> cachedTabs = HomeCache.getCachedTabList();
//            if (!ListUtil.isEmpty(cachedTabs)) {
//                YTLogUtil.logStart(TAG, "waitForCachedDataWithTimeout", "success");
//                return cachedTabs; // 找到了缓存数据
//            }
//            try {
//                TimeUnit.MILLISECONDS.sleep(50); // 休眠50毫秒
//            } catch (InterruptedException e) {
//                Thread.currentThread().interrupt(); // 处理异常
//                break;
//            }
//        }
//        return null; // 超过重试次数后返回null
//    }
//
//    private List<MainTabBean> fetchFromServer() {
//        YTLogUtil.logStart(TAG, "fetchFromServer", "");
//        CompletableFuture<List<MainTabBean>> future = new CompletableFuture<>();
//        mModel.getTabList(new HttpCallback<List<MainTabBean>>() {
//            @Override
//            public void onSuccess(List<MainTabBean> mainTabBeans) {
//                YTLogUtil.logStart(TAG, "fetchFromServer", "onSuccess");
//                future.complete(mainTabBeans);
//            }
//
//            @Override
//            public void onError(ApiException e) {
//                YTLogUtil.logStart(TAG, "fetchFromServer", "onError" + e.getCode() + " " + e.getMessage());
//                future.complete(null);
//                if (mView != null) {
//                    mView.onLoadFinish();
//                    mView.onTabDateError(e.getMessage());
//                }
//            }
//        });
//        return future.join();
//    }

    private void processData(List<MainTabBean> mainTabBeans) {
        YTLogUtil.logStart(TAG, "processData", "");
        new Handler(Looper.getMainLooper()).post(() -> {
            if (mView != null) {
                mView.onLoadFinish();
                if (mainTabBeans != null && !mainTabBeans.isEmpty()) {
                    mView.onTabDate(mainTabBeans);
                }
            }
        });
    }

    @Override
    public void start() {
        super.start();
    }

    @Override
    public void destroy() {

    }
}
