package com.kaolafm.kradio.flavor.aop;

import android.content.Context;
import android.util.Log;

import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.kaolafm.kradio.lib.toast.SuperToast;
import com.kaolafm.kradio.lib.utils.imageloader.ImageConfigImpl;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;


@Aspect
public class ImageloaderAop {

    private static final String TAG = "ImageloaderAop";
    private static SuperToast mToast;

    @Around("execution(* com.kaolafm.kradio.lib.utils.imageloader.GlideImageLoaderStrategy.loadImage(..))")
    public void loadImageAop(ProceedingJoinPoint point) throws Throwable {
        try {
            //todo 广汽定制了缓存策略DiskCacheStrategy.NONE
            Context context = (Context) (point.getArgs()[0]);
            ImageConfigImpl config = (ImageConfigImpl) (point.getArgs()[1]);
            if (context == null || config == null) {
                point.proceed();
            } else {
                config.setCacheStrategy(DiskCacheStrategy.NONE);
                Log.i("ImageloaderAop", "loadImageAop: setCacheStrategy = DiskCacheStrategy.NONE");
                point.proceed();
            }
        } catch (Exception e) {
            e.printStackTrace();
            point.proceed();
        }
    }
}
