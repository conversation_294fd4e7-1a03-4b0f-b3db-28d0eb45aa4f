package com.kaolafm.auto.appwidget;

import android.appwidget.AppWidgetManager;
import android.appwidget.AppWidgetProvider;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;

import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.service.BYDWidgetService;
import com.kaolafm.kradio.service.HanWidgetService;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import cmgyunting.vehicleplayer.cnr.YunTingWidgetService;


/**
 * Implementation of App Widget functionality.
 */
public class KLAppWidget extends AppWidgetProvider {
//    private static final String TAG = "BYDWidgetProvider";
//
//    @Override
//    public void onReceive(Context context, Intent intent) {
//        Log.i(TAG, "onReceive----->" + intent.getAction());
//        if (Constants.APPEXIT_ACTION.equals(intent.getAction())) {
//            AppWidgetManager appWidgetManager = AppWidgetManager.getInstance(context);
//            ComponentName componentName = new ComponentName(context, KLAppWidget.class);
//            for (int appWidgetId : appWidgetManager.getAppWidgetIds(componentName)) {
//                updateAppWidget(context, appWidgetManager, appWidgetId, Constants.APPEXIT_ACTION);
//            }
//        } else {
//            super.onReceive(context, intent);
//        }
//    }
//
//    @Override
//    public void onEnabled(Context context) {
//        Log.i(TAG, "onEnable ----- ");
//        Intent intent = new Intent(context, BYDWidgetService.class);
//        intent.setAction(BYDWidgetService.WIDGET_ACTION_REFRESH);
////        Intent intent = new Intent(context, HanWidgetService.class);
////        intent.setAction(HanWidgetService.WIDGET_ACTION_REFRESH);
//        context.startService(intent);
//    }
//
//    @Override
//    public void onUpdate(Context context, AppWidgetManager appWidgetManager, int[] appWidgetIds) {
//        Log.i(TAG, "onUpdate ----- appwidgetId.size appWidgetIds size = " + appWidgetIds.length + "-----appWidgetIds = " + appWidgetIds);
//        for (int appWidgetId : appWidgetIds) {
//            updateAppWidget(context, appWidgetManager, appWidgetId, "android.appwidget.action.APPWIDGET_UPDATE");
//        }
//    }
//
//    @Override
//    public void onDisabled(Context context) {
//        Log.i(TAG, "onDisabled ----- ");
//        Intent intent = new Intent(context, BYDWidgetService.class);
//        intent.setAction(BYDWidgetService.WIDGET_ACTION_REFRESH);
////        Intent intent = new Intent(context, HanWidgetService.class);
////        intent.setAction(HanWidgetService.WIDGET_ACTION_REFRESH);
//        context.startService(intent);
//    }
//
//    @Override
//    public void onAppWidgetOptionsChanged(Context context, AppWidgetManager appWidgetManager, int appWidgetId, Bundle newOptions) {
//        super.onAppWidgetOptionsChanged(context, appWidgetManager, appWidgetId, newOptions);
//    }
//
//    void updateAppWidget(final Context pContext, AppWidgetManager appWidgetManager, final int appWidgetId, String action) {
//        Log.i(TAG, "updateAppWidget ----- appWidgetId:" + appWidgetId);
//        PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
////        if (Constants.APPEXIT_ACTION.equals(action) || playItem == null) {
////            RemoteViews views = new RemoteViews(pContext.getPackageName(), BYDWidgetService.getRemoteViewsLayoutId());
////            views.setTextViewText(R.id.widget_audio_name, "");
////            views.setTextViewText(R.id.widget_album_name, "");
////            views.setImageViewBitmap(R.id.widget_cover,null);
////            views.setImageViewBitmap(R.id.widget_blur_imageview,null);
////            views.setImageViewBitmap(R.id.widget_broadcast_label,null);
////            views.setImageViewResource(R.id.widget_play_or_pause, R.drawable.selector_widget_btn_pause);
////            views.setProgressBar(R.id.widget_progressBar,0,0,false);
////            views.setTextViewText(R.id.widget_duration, "");
////            views.setTextViewText(R.id.widget_cur_time,  "");
////            views.setTextViewText(R.id.widget_broadcast_label_textview,  "");
////            views.setImageViewResource(R.id.widget_collection,  R.drawable.selector_widget_btn_uncollection);
////            views.setOnClickPendingIntent(R.id.widget_playinfo_layout, PendingIntent.getActivity(MyApplication.mContext, 0, new Intent(MyApplication.mContext, HomeActivity.class), 0));
////            appWidgetManager.updateAppWidget(appWidgetId, views);
////        } else {
////        if (Constants.APPEXIT_ACTION.equals(action) || playItem == null) {
////            intent.setAction(BYDWidgetService.WIDGET_ACTION_EXIT);
////        } else {
////        }
//        Intent intent = new Intent(pContext, BYDWidgetService.class);
//        intent.setAction(BYDWidgetService.WIDGET_ACTION_REFRESH);
////        Intent intent = new Intent(pContext, HanWidgetService.class);
////        intent.setAction(HanWidgetService.WIDGET_ACTION_REFRESH);
//        pContext.startService(intent);
//    }

//---------------- yunBydWidget    test only ---------------------//
//    private static final String TAG = "YunBydWidget";
//
//    @Override
//    public void onReceive(Context context, Intent intent) {
//        Log.i(TAG, "onReceive----->" + intent.getAction());
//        if (Constants.APPEXIT_ACTION.equals(intent.getAction())) {
//            AppWidgetManager appWidgetManager = AppWidgetManager.getInstance(context);
//            ComponentName componentName = new ComponentName(context, KLAppWidget.class);
//            for (int appWidgetId : appWidgetManager.getAppWidgetIds(componentName)) {
//                updateAppWidget(context, appWidgetManager, appWidgetId, Constants.APPEXIT_ACTION);
//            }
//        } else {
//            super.onReceive(context, intent);
//        }
//    }
//
//    @Override
//    public void onEnabled(Context context) {
//        Log.i(TAG, "onEnable ----- ");
//        Intent intent = new Intent(context, YunTingWidgetService.class);
//        intent.setAction(YunTingWidgetService.WIDGET_ACTION_REFRESH);
////        context.startService(intent);
//        startServiceCompat(context,intent);
//    }
//
//    private void startServiceCompat(Context context, Intent intent) {
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//            context.startForegroundService(intent);
//        }else {
//            context.startService(intent);
//        }
//    }
//
//    @Override
//    public void onUpdate(Context context, AppWidgetManager appWidgetManager, int[] appWidgetIds) {
//        Log.i(TAG, "onUpdate ----- appwidgetId.size appWidgetIds size = " + appWidgetIds.length + "-----appWidgetIds = " + appWidgetIds);
//        for (int appWidgetId : appWidgetIds) {
//            updateAppWidget(context, appWidgetManager, appWidgetId, "android.appwidget.action.APPWIDGET_UPDATE");
//        }
//    }
//
//    @Override
//    public void onDisabled(Context context) {
//        Log.i(TAG, "onDisabled ----- ");
//        Intent intent = new Intent(context, YunTingWidgetService.class);
//        intent.setAction(YunTingWidgetService.WIDGET_ACTION_REFRESH);
////        context.startService(intent);
//        startServiceCompat(context,intent);
//    }
//
//
//    @Override
//    public void onAppWidgetOptionsChanged(Context context, AppWidgetManager appWidgetManager, int appWidgetId, Bundle newOptions) {
//        super.onAppWidgetOptionsChanged(context, appWidgetManager, appWidgetId, newOptions);
//    }
//
//    void updateAppWidget(final Context pContext, AppWidgetManager appWidgetManager, final int appWidgetId, String action) {
//        Log.i(TAG, "updateAppWidget ----- appWidgetId:" + appWidgetId);
//        Intent intent = new Intent(pContext, YunTingWidgetService.class);
//        intent.setAction(YunTingWidgetService.WIDGET_ACTION_REFRESH);
////        pContext.startService(intent);
//        startServiceCompat(pContext,intent);
//    }

//---------------- yunBydWidget    test only ---------------------//


//---------------- YunBydDophinWidget    test only ---------------------//
    private static final String TAG = "YunBydDophinWidget";

    private PlayerManager playerManager = PlayerManager.getInstance();

    @Override
    public void onUpdate(Context context, AppWidgetManager appWidgetManager, int[] appWidgetIds) {
        Log.i(TAG, "onUpdate ----- appwidgetId.size appWidgetIds size = " + appWidgetIds.length + "-----appWidgetIds = " + appWidgetIds);
        for (int appWidgetId : appWidgetIds) {
            updateAppWidget(context, appWidgetManager, appWidgetId, "android.appwidget.action.APPWIDGET_UPDATE");
        }
    }

    @Override
    public void onEnabled(Context context) {
        // TODO: 4/5/21 参考widgetDemo修改现有Service启动逻辑，解决service启动的各种异常
        Log.i(TAG, "onEnable ----- ");
        Intent intent = new Intent(context, YunTingWidgetService.class);
        intent.setAction(YunTingWidgetService.WIDGET_ACTION_REFRESH);
//        context.startService(intent);
        startServiceCompat(context, intent);
    }


    @Override
    public void onDisabled(Context context) {
        Log.i(TAG, "onDisabled ----- ");
        Intent intent = new Intent(context, YunTingWidgetService.class);
        intent.setAction(YunTingWidgetService.WIDGET_ACTION_REFRESH);
//        context.startService(intent);
        startServiceCompat(context,intent);
    }

    @Override
    public void onDeleted(Context context, int[] appWidgetIds) {
        super.onDeleted(context, appWidgetIds);
    }

    @Override
    public void onRestored(Context context, int[] oldWidgetIds, int[] newWidgetIds) {
        super.onRestored(context, oldWidgetIds, newWidgetIds);
    }


    @Override
    public void onReceive(Context context, Intent intent) {
        Log.i(TAG, "onReceive----->" + intent.getAction());
        if (Constants.APPEXIT_ACTION.equals(intent.getAction())) {
            AppWidgetManager appWidgetManager = AppWidgetManager.getInstance(context);
            ComponentName componentName = new ComponentName(context, KLAppWidget.class);
            for (int appWidgetId : appWidgetManager.getAppWidgetIds(componentName)) {
                updateAppWidget(context, appWidgetManager, appWidgetId, Constants.APPEXIT_ACTION);
            }
        } else {
            super.onReceive(context, intent);
        }
    }

    @Override
    public void onAppWidgetOptionsChanged(Context context, AppWidgetManager appWidgetManager, int appWidgetId, Bundle newOptions) {
        super.onAppWidgetOptionsChanged(context, appWidgetManager, appWidgetId, newOptions);
    }

    void updateAppWidget(final Context pContext, AppWidgetManager appWidgetManager, final int appWidgetId, String action) {
        Log.i(TAG, "updateAppWidget ----- appWidgetId:" + appWidgetId);
        Intent intent = new Intent(pContext, YunTingWidgetService.class);
        intent.setAction(YunTingWidgetService.WIDGET_ACTION_REFRESH);
//        pContext.startService(intent);
        startServiceCompat(pContext, intent);
    }

    private void startServiceCompat(Context context, Intent intent) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.startForegroundService(intent);
        } else {
            context.startService(intent);
        }
    }
//---------------- YunBydDophinWidget    test only ---------------------//



}

