package com.kaolafm.kradio.online.player.mvp;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.lib.base.mvp.BaseModel;
import com.kaolafm.opensdk.api.broadcast.BroadcastRequest;
import com.kaolafm.opensdk.api.broadcast.ProgramDetails;
import com.kaolafm.opensdk.api.tv.TVProgramDetails;
import com.kaolafm.opensdk.api.tv.TVRequest;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.playlist.util.PlayListUtils;
import com.trello.rxlifecycle3.LifecycleTransformer;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/3/6
 */
public class ListenTVProgramListModel extends BaseModel {

    private final LifecycleTransformer mLifecycleTransformer;

    ListenTVProgramListModel(LifecycleTransformer lifecycleTransformer) {
        mLifecycleTransformer = lifecycleTransformer;
    }

    void getListenTVProgramList(long id, String date, long listenCount, HttpCallback<ArrayList<PlayItem>> httpCallback) {
        TVRequest tvRequest = new TVRequest().bindLifecycle(mLifecycleTransformer);
        HttpCallback callback = new HttpCallback<List<TVProgramDetails>>() {
            @Override
            public void onSuccess(List<TVProgramDetails> programDetailsList) {
                if (httpCallback == null) {
                    return;
                }
                if (ListUtil.isEmpty(programDetailsList)) {
                    httpCallback.onError(new ApiException(-1, "数据为空"));
                } else {
                    ArrayList<PlayItem> playItemArrayList = PlayListUtils.programDetailsToTVPlayItem(programDetailsList, "",listenCount);
                    httpCallback.onSuccess(playItemArrayList);
                }
            }

            @Override
            public void onError(ApiException e) {

            }
        };
        tvRequest.getTVProgramList(id, date, callback);

    }

    @Override
    public void destroy() {

    }
}
