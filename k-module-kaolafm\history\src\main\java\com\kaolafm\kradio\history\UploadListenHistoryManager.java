package com.kaolafm.kradio.history;

import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.common.http.CommonRequestParamsUtil;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.bean.ListenHistoryDataBean;
import com.kaolafm.kradio.lib.utils.KaolaAppConfigData;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.account.token.AccessTokenManager;
import com.kaolafm.opensdk.account.token.KaolaAccessToken;
import com.kaolafm.opensdk.api.history.model.SyncHistoryStatus;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.socket.SocketApiConstants;
import com.kaolafm.opensdk.socket.SocketEvent;
import com.kaolafm.opensdk.socket.SocketListener;
import com.kaolafm.opensdk.socket.SocketManager;

import io.reactivex.Flowable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;

import static com.kaolafm.kradio.lib.utils.Constants.KEY_APP_ID;
import static com.kaolafm.kradio.lib.utils.Constants.KEY_DEVICE_ID;
import static com.kaolafm.kradio.lib.utils.Constants.KEY_OPEN_ID;

/**
 * 上传历史
 */
public class UploadListenHistoryManager {
    private static final String TAG = "UploadHistory";
    private static final String KEY_HISTORY_LIST = "userHistoryList";
    private static final String KEY_ACCESS_TOKEN = "accessToken";
    private static final int UPLOAD_TIMER = 60;
    private ListenHistoryDataBean mListenHistoryDataBean;
    private List<ListenHistoryDataBean> mUserHistoryList;

    private long mCurrentPosition;
    private long mPlayedTime;
    private Disposable mDisposable;

    private PlayItem mCurrentPlayItem;
    /**
     * 无效时间
     */
    private boolean mInvalidTime = false;
    private SocketStateListener mSocketStateListener;

    private boolean running = false;

    private UploadListenHistoryManager() {
        mSocketStateListener = new SocketStateListener();
        mUserHistoryList = new CopyOnWriteArrayList<>();
    }

    private static final class INNER_CLASS {
        private static UploadListenHistoryManager S_INSTANCE = new UploadListenHistoryManager();
    }

    public static UploadListenHistoryManager getInstance() {
        return INNER_CLASS.S_INSTANCE;
    }

    public void start() {
        if (running) {
            return;
        }
        startTimer();
        running = true;
    }

    public void clear() {
        clearPlayTime();
        mUserHistoryList.clear();
    }

    public void setCurrentPlayInvalid() {
        mInvalidTime = true;
        mCurrentPosition = 0;
    }

    public void setInvalidTime(boolean invalidTime) {
        mInvalidTime = invalidTime;
    }

    public void progress(PlayItem playItem, long progress) {
        mCurrentPlayItem = playItem;
        if (mInvalidTime) {
            Log.i(TAG, "is invalid time, throw!");
            return;
        }
        if ((playItem.getType() == PlayerConstants.RESOURCES_TYPE_BROADCAST && playItem.isLiving())
                || (playItem.getType() == PlayerConstants.RESOURCES_TYPE_TV && playItem.isLiving())) {
            if (!PlayerManagerHelper.getInstance().isPlaying()) {
                setCurrentPlayInvalid();
                return;
            }
        }

        if (mListenHistoryDataBean == null) {
            createListenHistory(playItem);
        }
        if (mListenHistoryDataBean.getContentId() != playItem.getAudioId()) {
            createListenHistory(playItem);
        }

        if (mCurrentPosition != 0) {
            long playTime = progress - mCurrentPosition;
            if (playTime > 0) {
                mPlayedTime += playTime;
                Log.i(TAG, "id = " + playItem.getAudioId() + "单次需要累加的时间： " + playTime + ", 已经累加的时间： " + mPlayedTime);
            }
        }
        mCurrentPosition = progress;
        mListenHistoryDataBean.setPlayedTime(progress);
    }

    private void createListenHistory(PlayItem playItem) {
        if (mListenHistoryDataBean != null) {
            Log.i(TAG, "createListenHistory, add new data: id = " + mListenHistoryDataBean.getContentId() + ", time = " + mPlayedTime);
            mListenHistoryDataBean.setDuration(mPlayedTime);
            mListenHistoryDataBean.setTimeStamp(System.currentTimeMillis());
            mUserHistoryList.add(mListenHistoryDataBean);
        }
        createNewListen(playItem);
    }

    private void createNewListen(PlayItem playItem) {
        if (playItem == null) {
            return;
        }
        mListenHistoryDataBean = new ListenHistoryDataBean();
        try {
            mListenHistoryDataBean.setPareContentId(Long.parseLong(playItem.getRadioId()));
        } catch (Exception e) {

        }
        mListenHistoryDataBean.setContentId(playItem.getAudioId());
        mListenHistoryDataBean.setType(playItem.getType());
        Log.i(TAG, "createNewListen: id = " + mListenHistoryDataBean.getContentId() + ", type = " + mListenHistoryDataBean.getType());
        clearPlayTime();
    }

    private void sendListenHistory() {
        Log.i(TAG, "make all history");
        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
            return;
        }
        if (mListenHistoryDataBean == null) {
            return;
        }
        mListenHistoryDataBean.setTimeStamp(System.currentTimeMillis());
        mListenHistoryDataBean.setDuration(mPlayedTime);

        if (mPlayedTime > 0) {
            Log.i(TAG, "sendListenHistory, add new data: id = " + mListenHistoryDataBean.getContentId() + ", time = " + mPlayedTime);
            mUserHistoryList.add(mListenHistoryDataBean);
        }
        createNewListen(mCurrentPlayItem);
        if (ListUtil.isEmpty(mUserHistoryList)) {
            return;
        }

        Log.i(TAG, "sendListenHistory size = " + mUserHistoryList.size());
        printfLog();
        clearPlayTime();
        SocketManager.getInstance().setMap(CommonRequestParamsUtil.getCommonParams()).setSocketHost(SocketApiConstants.SOCKET_HOST).request(mSocketStateListener);
    }

    private void printfLog() {
        List<ListenHistoryDataBean> listTemp = mUserHistoryList;
        if (ListUtil.isEmpty(listTemp)) {
            return;
        }
        for (int i = 0; i < listTemp.size(); i++) {
            ListenHistoryDataBean listenHistoryDataBean = listTemp.get(i);
            if (listenHistoryDataBean == null) {
                continue;
            }

            Log.i(TAG, "printfLog, listenHistoryDataBean is "+listenHistoryDataBean.toString());
        }
    }

    private class SocketStateListener implements SocketListener<SyncHistoryStatus> {
        @Override
        public String getEvent() {
            return SocketEvent.SAVE_HISTORY;
        }

        @Override
        public Map<String, Object> getParams(Map<String, Object> params) {
            //List<UserHistory> userHistoryList;
            //// 设备id
            //private String deviceid;
            //// 应用id
            //private String appid;
            //// openUid
            //private String kradioUid;
            //
            //private String accessToken;
            KaolaAppConfigData kaolaAppConfigData = KaolaAppConfigData.getInstance();
            KaolaAccessToken kaolaAccessToken = AccessTokenManager.getInstance().getKaolaAccessToken();
            String deviceId = kaolaAppConfigData.getUdid();
            String openId = kaolaAccessToken.getOpenId();
            String appId = kaolaAppConfigData.getAppId();
            params.put(KEY_HISTORY_LIST, mUserHistoryList);
            params.put(KEY_DEVICE_ID, deviceId);
            params.put(KEY_APP_ID, appId);
            if (!TextUtils.isEmpty(openId)) {
                params.put(KEY_OPEN_ID, openId);
            }
            params.put(KEY_HISTORY_LIST, mUserHistoryList);
            params.put(KEY_ACCESS_TOKEN, AccessTokenManager.getInstance().getKaolaAccessToken().getAccessToken());
            return params;
        }

        @Override
        public boolean isNeedParams() {
            return true;
        }

        @Override
        public boolean isNeedRequest() {
            return true;
        }

        @Override
        public void onSuccess(SyncHistoryStatus uploadHistoryResultBean) {
            Log.i(TAG, "发送历史成功");
            mUserHistoryList.clear();
        }

        @Override
        public void onError(ApiException e) {
            Log.e(TAG, "发送历史失败: " + e.getMessage());
            List<ListenHistoryDataBean> tempArrayList = mUserHistoryList;
            if (ListUtil.isEmpty(tempArrayList)) {
                return;
            }
            ListenHistoryDataBean dataBean = tempArrayList.get(tempArrayList.size() - 1);

            if (mListenHistoryDataBean.getContentId() == dataBean.getContentId()) {
                Log.e(TAG, "发送历史失败: 恢复最后一个添加的值: " + dataBean.getDuration());
                mPlayedTime = dataBean.getDuration();
                mListenHistoryDataBean = dataBean;
            }
        }
    }

    private void clearPlayTime() {
        mCurrentPosition = 0;
        mPlayedTime = 0;
    }

    private void startTimer() {
        Log.i(TAG, "开始 保存历史 timer");
        mDisposable = Flowable.interval(UPLOAD_TIMER, TimeUnit.SECONDS).onBackpressureDrop()
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(aLong -> onTimer());
    }

    private void onTimer() {
        Log.i(TAG, "发送历史timer");
        sendListenHistory();
    }

    private void stopTimer() {
        Log.i(TAG, "停止 保存历史timer");
        if (mDisposable != null && !mDisposable.isDisposed()) {
            mDisposable.dispose();
            mDisposable = null;
        }
    }

    public void destroy() {
        sendListenHistory();
        mUserHistoryList.clear();
        stopTimer();
        running = false;
    }

}
