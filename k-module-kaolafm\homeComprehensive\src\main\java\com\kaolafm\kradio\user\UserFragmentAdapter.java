package com.kaolafm.kradio.user;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;

import java.util.List;

/**
 * <pre>
 *     <AUTHOR> Wenchl
 *     e-mail : <EMAIL>
 *     time   : 2019/03/05
 *     desc   :
 *     version: 1.0
 * </pre>
 */
public class UserFragmentAdapter extends FragmentPagerAdapter {

    private List<Fragment> fragments ;

    public UserFragmentAdapter(FragmentManager fm, List<Fragment> list) {
        super(fm);
        fragments = list;
    }

    @Override
    public Fragment getItem(int position) {
        return fragments.get(position);
    }

    @Override
    public int getCount() {
        return fragments.size();
    }
}
