<?xml version="1.0" encoding="utf-8"?><!--这个padding值不能去掉 修改  #36156-->
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/colorTransparent"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/common_recyclerView"
        style="@style/PlayListVerticalScrollBar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorTransparent" />

    <!-- 隐藏控件，用于所见即可说语音执行滑动操作 -->
    <TextView
        style="@style/ContentDescriptionScrollUp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        style="@style/ContentDescriptionScrollDown"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>