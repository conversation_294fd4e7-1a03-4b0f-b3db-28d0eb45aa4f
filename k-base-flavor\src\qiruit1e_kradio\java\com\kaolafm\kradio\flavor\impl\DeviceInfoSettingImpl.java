package com.kaolafm.kradio.flavor.impl;

import android.content.Context;

import com.kaolafm.kradio.lib.utils.SystemPropertiesProxy;
import com.oem.frameworks.utils.FileUtil;

import static com.kaolafm.kradio.lib.utils.StringUtil.makeAsciiOnly;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-08-15 15:18
 ******************************************/
public final class DeviceInfoSettingImpl implements DeviceInfoSetting {
    @Override
    public String getDeviceId(Object... args) {
        String deviceId = makeAsciiOnly(FileUtil.getCid19());
        String carType = SystemPropertiesProxy.getString((Context) args[0], "ro.vendor.product.cartype");
        DeviceInfoUtil.setDeviceIdAndCarType(deviceId, carType);
    }
}
