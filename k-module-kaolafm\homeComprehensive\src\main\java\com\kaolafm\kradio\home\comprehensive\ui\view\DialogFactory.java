package com.kaolafm.kradio.home.comprehensive.ui.view;

import androidx.fragment.app.DialogFragment;
import android.view.Gravity;

import com.kaolafm.kradio.lib.base.flavor.DialogCreator;
import com.kaolafm.kradio.lib.dialog.Dialogs;

/**
 * <AUTHOR>
 **/
public class DialogFactory implements DialogCreator {

    @Override
    public DialogFragment create(Dialogs.Builder builder) {
        DialogFragment dialog = null;

        if (builder == null) {
            dialog = Center2BtnDialogFragment.create();
            return dialog;
        }

        if (builder.getType() == Dialogs.TYPE_2BTN && builder.getGravity() == Gravity.CENTER) {
            dialog = Center2BtnDialogFragment.create();
            ((Center2BtnDialogFragment) dialog).setBackground(builder.getBackground());
            ((Center2BtnDialogFragment) dialog).setOutCancel(builder.isOutCancel());
            ((Center2BtnDialogFragment) dialog).setMessage(builder.getMessage());
            ((Center2BtnDialogFragment) dialog).setLeftButton(builder.getLeftBtnText());
            ((Center2BtnDialogFragment) dialog).setRightButton(builder.getRightBtnText());
            ((Center2BtnDialogFragment) dialog).setOnNativeListener(builder.getOnNativeListener());
            ((Center2BtnDialogFragment) dialog).setOnPositiveListener(builder.getOnPositiveListener());
            ((Center2BtnDialogFragment) dialog).setOnOutsideClickListener(builder.getOnOutsideClickListener());
            ((Center2BtnDialogFragment) dialog).setCanShowButton(builder.isCanShowButton());
        } else if (builder.getType() == Dialogs.TYPE_2BTN_WITH_TITLE) {
            dialog = Center2BtnWithTitleDialogFragment.create();
            ((Center2BtnWithTitleDialogFragment) dialog).setBackground(builder.getBackground());
            ((Center2BtnWithTitleDialogFragment) dialog).setOutCancel(builder.isOutCancel());
            ((Center2BtnWithTitleDialogFragment) dialog).setTitle(builder.getTitle());
            ((Center2BtnWithTitleDialogFragment) dialog).setMessage(builder.getMessage());
            ((Center2BtnWithTitleDialogFragment) dialog).setLeftButton(builder.getLeftBtnText());
            ((Center2BtnWithTitleDialogFragment) dialog).setRightButton(builder.getRightBtnText());
            ((Center2BtnWithTitleDialogFragment) dialog).setOnNativeListener(builder.getOnNativeListener());
            ((Center2BtnWithTitleDialogFragment) dialog).setOnPositiveListener(builder.getOnPositiveListener());
            ((Center2BtnWithTitleDialogFragment) dialog).setOnOutsideClickListener(builder.getOnOutsideClickListener());
            ((Center2BtnWithTitleDialogFragment) dialog).setCanShowButton(builder.isCanShowButton());
        } else if (builder.getType() == Dialogs.TYPE_TITLE_LIST_2BTN) {
            dialog = PreferencesDialogFragment.create();
            ((PreferencesDialogFragment) dialog).setBackground(builder.getBackground());
            ((PreferencesDialogFragment) dialog).setOutCancel(builder.isOutCancel());
            ((PreferencesDialogFragment) dialog).setTitle(builder.getTitle());
            ((PreferencesDialogFragment) dialog).setSubtitle(builder.getMessage());
            ((PreferencesDialogFragment) dialog).setAdapter(builder.getAdapter());
            ((PreferencesDialogFragment) dialog).setLeftButton(builder.getLeftBtnText());
            ((PreferencesDialogFragment) dialog).setRightButton(builder.getRightBtnText());
            ((PreferencesDialogFragment) dialog).setOnNativeListener(builder.getOnNativeListener());
            ((PreferencesDialogFragment) dialog).setOnPositiveListener(builder.getOnPositiveListener());
            ((PreferencesDialogFragment) dialog).setOnOutsideClickListener(builder.getOnOutsideClickListener());
        } else if (builder.getType() == Dialogs.TYPE_LIST) {
            dialog = FullScreenDialogFragment.create();
            ((FullScreenDialogFragment) dialog).setBackground(builder.getBackground());
            ((FullScreenDialogFragment) dialog).setOutCancel(builder.isOutCancel());
            ((FullScreenDialogFragment) dialog).setTitle(builder.getTitle());
            ((FullScreenDialogFragment) dialog).setAdapter(builder.getAdapter());
            ((FullScreenDialogFragment) dialog).setOnOutsideClickListener(builder.getOnOutsideClickListener());
        } else if (builder.getType() == Dialogs.TYPE_2BTN_PERMISSION) {
            dialog = PermissionCenter2BtnDialogFragment.create();
            ((PermissionCenter2BtnDialogFragment) dialog).setBackground(builder.getBackground());
            ((PermissionCenter2BtnDialogFragment) dialog).setOutCancel(builder.isOutCancel());
            ((PermissionCenter2BtnDialogFragment) dialog).setMessage(builder.getMessage());
            ((PermissionCenter2BtnDialogFragment) dialog).setLeftButton(builder.getLeftBtnText());
            ((PermissionCenter2BtnDialogFragment) dialog).setRightButton(builder.getRightBtnText());
            ((PermissionCenter2BtnDialogFragment) dialog).setOnNativeListener(builder.getOnNativeListener());
            ((PermissionCenter2BtnDialogFragment) dialog).setOnPositiveListener(builder.getOnPositiveListener());
            ((PermissionCenter2BtnDialogFragment) dialog).setOnOutsideClickListener(builder.getOnOutsideClickListener());
            ((PermissionCenter2BtnDialogFragment) dialog).setCanShowButton(builder.isCanShowButton());
            ((PermissionCenter2BtnDialogFragment) dialog).setCanShowCheckBox(builder.isCanShowCheckBox());
        } else {
            dialog = BottomDialogFragment.create();
            ((BottomDialogFragment) dialog).setBackground(builder.getBackground());
            ((BottomDialogFragment) dialog).setOutCancel(builder.isOutCancel());
            ((BottomDialogFragment) dialog).setMessage(builder.getMessage());
            ((BottomDialogFragment) dialog).setLeftButton(builder.getLeftBtnText());
            ((BottomDialogFragment) dialog).setRightButton(builder.getRightBtnText());
            ((BottomDialogFragment) dialog).setOnNativeListener(builder.getOnNativeListener());
            ((BottomDialogFragment) dialog).setOnPositiveListener(builder.getOnPositiveListener());
            ((BottomDialogFragment) dialog).setOnOutsideClickListener(builder.getOnOutsideClickListener());
        }

        return dialog;
    }
}
