package com.kaolafm.kradio.history.tingban;

import android.app.Activity;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import com.kaolafm.kradio.lib.bean.HistoryItem;
import com.kaolafm.kradio.lib.utils.Constants;
import java.util.ArrayList;
import java.util.List;

/**
 * 听伴车载升级到Kradio 用于读取听伴的历史。
 */
public class HistoryDbManager {

    private Context mContext;
    private SQLiteDatabase mDatabase;
    private HistoryDbHelper mHistoryDbHelper;
    private volatile static HistoryDbManager sInstance;

    // private Map<String, String> mIdNameMap = new HashMap<String, String>();

    private HistoryDbManager(Context context) {
        if (context == null) {
            throw new IllegalArgumentException("context is Null!");
        }
        mContext = context;
        mHistoryDbHelper = new HistoryDbHelper(mContext);
        mDatabase = mHistoryDbHelper.getWritableDatabase();
    }

    public static HistoryDbManager getInstance(Context context) {
        if (sInstance == null) {
            synchronized (HistoryDbManager.class) {
                if (sInstance == null) {
                    sInstance = new HistoryDbManager(context instanceof Activity ?
                            context.getApplicationContext() : context);
                }
            }
        }
        return sInstance;
    }

    /**
     * 关闭数据库
     */
    public void closeDB() {
        closeDB(mDatabase);
    }

    private void closeDB(SQLiteDatabase db) {
        if (db != null) {
            try {
                db.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void closeCursor(Cursor cursor) {
        if (cursor != null) {
            try {
                cursor.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private boolean isCursorEmpty(Cursor cursor) {
        try { // 修复友盟统计异常
            if (cursor == null || cursor.getCount() <= 0 || cursor.getColumnCount() <= 0) {
                return true;
            }
        } catch (Throwable t) {
            t.printStackTrace();
        }
        return false;
    }



    public void clearAllPlayHistory() {
        mDatabase.delete(HistoryDbHelper.TABLE_PLAY_HISTORY, null, null);
    }


    /**
     * 获取所以收听历史数据
     *
     * @return
     */
    public ArrayList<HistoryItem> getHistoryItemList() {
        ArrayList<HistoryItem> historyItemList = null;
        Cursor cursor = null;
        try {
            cursor = mDatabase.rawQuery(HistoryDbHelper.SQL_QUERY_PLAY_HISTORY,
                    null);
            historyItemList = toHistoryItems(cursor);
        } catch (IllegalStateException ill) {
            ill.printStackTrace();
        } catch (Throwable t) {
            t.printStackTrace();
        } finally {
            closeCursor(cursor);
        }
        return historyItemList;
    }

    /**
     * 获取最新的一条数据
     *
     * @return
     */
    public HistoryItem getHistoryItemNewest() {
        List<HistoryItem> historyItemList = null;
        Cursor cursor = null;
        try {
            cursor = mDatabase.rawQuery(
                    HistoryDbHelper.SQL_QUERY_PLAY_HISTORY_BY_ONE, null);
            historyItemList = toHistoryItems(cursor);
        } catch (IllegalStateException ill) {
            ill.printStackTrace();
        } catch (Throwable t) { // 解决umeng http://mobile.umeng.com/apps/28bd206837b042655f921b15/error_types/show?error_type_id=51b129f556240b738602db82_6589291752198709426_4.8.4 问题
            t.printStackTrace();
        } finally {
            closeCursor(cursor);
        }
        if (historyItemList == null || historyItemList.size() == 0) {
            return null;
        }
        return historyItemList.get(0);
    }

    /**
     * @param cursor
     * @return
     */
    private ArrayList<HistoryItem> toHistoryItems(Cursor cursor) {
        ArrayList<HistoryItem> historyList = new ArrayList<>();
        if (isCursorEmpty(cursor)) {
            return historyList;
        } else {
            cursor.moveToFirst();
        }
        do {
            HistoryItem historyItemEntry = new HistoryItem();
            historyItemEntry.setType(cursor.getString(cursor
                    .getColumnIndex(HistoryDbHelper.FIELD_TYPE)));
            historyItemEntry.setRadioId(cursor.getString(cursor
                    .getColumnIndex(HistoryDbHelper.FIELD_RADIO_ID)));
            historyItemEntry.setRadioTitle(cursor.getString(cursor
                    .getColumnIndex(HistoryDbHelper.FIELD_RADIO_TITLE)));
            historyItemEntry.setAudioId(cursor.getString(cursor
                    .getColumnIndex(HistoryDbHelper.FIELD_AUDIO_ID)));
            historyItemEntry.setAudioTitle(cursor.getString(cursor
                    .getColumnIndex(HistoryDbHelper.FIELD_AUDIO_TITLE)));
            historyItemEntry.setPlayUrl(cursor.getString(cursor
                    .getColumnIndex(HistoryDbHelper.FIELD_PLAY_URL)));
            historyItemEntry.setOfflinePlayUrl(cursor.getString(cursor
                    .getColumnIndex(HistoryDbHelper.FIELD_OFFLINE_PLAY_URL)));
            historyItemEntry.setPlayedTime(cursor.getLong(cursor
                    .getColumnIndex(HistoryDbHelper.FIELD_PLAYED_TIME)));
            historyItemEntry.setDuration(cursor.getLong(cursor
                    .getColumnIndex(HistoryDbHelper.FIELD_DURATION)));
            historyItemEntry.setOffline(cursor.getInt(cursor
                    .getColumnIndex(HistoryDbHelper.FIELD_IS_OFFLINE)) == 1);
            int orderMode = Constants.ORDER_MODE_POSITIVE;
            // 调整默认值为Constants.INVALID_ID确保后期获取此PGC类型能正确查询到
            int typeId = Constants.INVALID_ID;
            int categoryId = 0;
            try {
                orderMode = cursor.getInt(cursor
                        .getColumnIndex(HistoryDbHelper.FIELD_ORDER_MODE));
                typeId = cursor.getInt(cursor
                        .getColumnIndex(HistoryDbHelper.FIELD_TYPE_ID));
                categoryId = cursor.getInt(cursor
                        .getColumnIndex(HistoryDbHelper.FIELD_CATEGORYID));
            } catch (Exception e) {
                e.fillInStackTrace();
            } finally {
                closeCursor(cursor);
            }
            orderMode = (orderMode != Constants.ORDER_MODE_POSITIVE &&
                    orderMode != Constants.ORDER_MODE_REVERSE) ? Constants.ORDER_MODE_POSITIVE : orderMode;
            historyItemEntry.setCategoryId(categoryId);
            historyItemEntry.setOrderMode(orderMode);
            historyItemEntry.setTypeId(typeId);


            historyItemEntry.setOrderNum(cursor.getLong(cursor
                    .getColumnIndex(HistoryDbHelper.FIELD_ORDER_NUM)));
            historyItemEntry.setTimeStamp(cursor.getLong(cursor
                    .getColumnIndex(HistoryDbHelper.FIELD_TIMESTAMP)));
            historyItemEntry.setPicUrl(cursor.getString(cursor
                    .getColumnIndex(HistoryDbHelper.FIELD_PIC_URL)));
            historyItemEntry.setShareUrl(cursor.getString(cursor
                    .getColumnIndex(HistoryDbHelper.FIELD_SHARE_URL)));
            historyItemEntry.setSourceUrl(cursor.getString(cursor.getColumnIndex(HistoryDbHelper.FIELD_SOURCE_URL)));
            historyList.add(historyItemEntry);
        } while (cursor.moveToNext());

        return historyList;
    }












}
