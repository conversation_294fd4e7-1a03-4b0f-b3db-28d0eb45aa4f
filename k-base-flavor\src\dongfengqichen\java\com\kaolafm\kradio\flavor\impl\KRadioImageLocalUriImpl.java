package com.kaolafm.kradio.flavor.impl;

import com.kaolafm.kradio.lib.base.flavor.KRadioImageLocalUriInter;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-04-26 11:24
 ******************************************/
public final class KRadioImageLocalUriImpl implements KRadioImageLocalUriInter {
    @Override
    public String getLocalImageUri(Object... args) {
        String tempUri = (String) args[0];
        String newUri = tempUri.replace(':', '/').replace("/0/", "/legacy/").concat(".0");
        return newUri;
    }
}
