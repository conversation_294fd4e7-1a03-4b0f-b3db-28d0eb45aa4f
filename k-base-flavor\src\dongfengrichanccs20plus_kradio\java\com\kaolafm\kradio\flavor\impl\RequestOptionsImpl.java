package com.kaolafm.kradio.flavor.impl;

import com.kaolafm.kradio.lib.base.flavor.RequestOptions;
import com.kaolafm.opensdk.utils.BaseHttpsStrategy;

/**
 * <AUTHOR>
 * @date 2020/6/5
 */
public class RequestOptionsImpl implements RequestOptions {

    class ChannelHttpsStrategy extends BaseHttpsStrategy {
        @Override
        public void updateChannelHttpsStrategy() {
            this.mHttpsMap.put("getHost", true);
            this.mHttpsMap.put("replaceUrl", true);
            this.mHttpsMap.put("adParam", true);
            this.mHttpsMap.put("media", true);
        }
    }
    @Override
    public BaseHttpsStrategy getHttpsStrategy() {
        return new ChannelHttpsStrategy();
    }
}
