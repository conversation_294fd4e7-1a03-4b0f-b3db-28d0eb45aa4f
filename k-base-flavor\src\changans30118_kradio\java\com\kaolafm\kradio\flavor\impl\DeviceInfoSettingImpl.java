package com.kaolafm.kradio.flavor.impl;

import android.content.Context;

import com.incall.proxy.constant.TboxConstantsDef;
import com.incall.proxy.setting.SettingManager;
import com.incall.proxy.tbox.TboxManager;
import com.kaolafm.kradio.flavor.utils.DeviceInfoUtil;
import com.kaolafm.kradio.lib.base.flavor.DeviceInfoSetting;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-05-22 20:50
 ******************************************/
public final class DeviceInfoSettingImpl implements DeviceInfoSetting {
    @Override
    public void setInfoForSDK(Context context) {
        String deviceId = TboxManager.getInstance().getId(TboxConstantsDef.ID_TYPE.VIN);
        String carType = SettingManager.getInstance().getCurCarType();
        DeviceInfoUtil.setDeviceIdAndCarType(deviceId, carType);
    }
}
