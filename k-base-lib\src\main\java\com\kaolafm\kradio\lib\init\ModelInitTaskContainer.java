package com.kaolafm.kradio.lib.init;

import android.util.Log;

import com.kaolafm.kradio.lib.utils.YTLogUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * 初始化任务{@link ModelInitTask}集合。内部逻辑处理使用，外部不需要关心
 * <AUTHOR>
 * @date 2022-06-30
 */
public class ModelInitTaskContainer {

    public List<ModelInitTask> modelInitTasks;

    public List<ModelInitTask> asyncInitTasks;

    public ModelInitTaskContainer() {
        modelInitTasks = new ArrayList<>();
        asyncInitTasks = new ArrayList<>();
    }

    public void add(ModelInitTask task) {
//        YTLogUtil.logStart("ModelInitTaskContainer", "add", "task = " + task.description);
        modelInitTasks.add(task);
        if (task.isAsync) {
            asyncInitTasks.add(task);
        }
    }
}
