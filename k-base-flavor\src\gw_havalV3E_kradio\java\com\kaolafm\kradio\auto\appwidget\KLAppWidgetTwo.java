package com.kaolafm.kradio.auto.appwidget;

import android.appwidget.AppWidgetManager;
import android.appwidget.AppWidgetProvider;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;

import com.kaolafm.kradio.lib.utils.Constants;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-06-11 20:13
 ******************************************/
public class KLAppWidgetTwo extends AppWidgetProvider {
    private static final String TAG = "KLAppWidgetTwo";

    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        Log.i(TAG, "onReceive action = " + action);
        if (Constants.APPEXIT_ACTION.equals(action)) {
            KLAppWidget.onReceive(context, intent, KLAppWidgetTwo.class);
        } else {
            super.onReceive(context, intent);
        }
    }

    @Override
    public void onEnabled(Context context) {
        Log.i(TAG, "onEnabled");
        KLAppWidget.onEnabled(context, KLAppWidgetTwo.class);
    }

    @Override
    public void onUpdate(Context context, AppWidgetManager appWidgetManager, int[] appWidgetIds) {
        Log.i(TAG, "onUpdate");
        KLAppWidget.onUpdate(context, appWidgetManager, appWidgetIds);
    }

    @Override
    public void onDisabled(Context context) {
        Log.i(TAG, "onDisabled");
        KLAppWidget.onDisabled(context, KLAppWidgetTwo.class);
    }

    @Override
    public void onAppWidgetOptionsChanged(Context context, AppWidgetManager appWidgetManager, int appWidgetId, Bundle newOptions) {
        Log.i(TAG, "onAppWidgetOptionsChanged");
        super.onAppWidgetOptionsChanged(context, appWidgetManager, appWidgetId, newOptions);
        KLAppWidget.onAppWidgetOptionsChanged(context, appWidgetManager, appWidgetId, newOptions);
    }

}

