package com.kaolafm.kradio.lib.location.model;

import static android.content.Context.LOCATION_SERVICE;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.pm.PackageManager;
import android.location.Location;
import android.location.LocationListener;
import android.location.LocationManager;
import android.os.Bundle;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import android.util.Log;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.opensdk.log.Logging;
import java.util.List;

/**
 * @ClassName GoogleLocation
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/5/26 14:58
 * @Version 1.0
 */
public class GoogleLocation implements IKaoLaLocation {

    private static final String TAG = "GoogleLocation";

    private LocationModel mLocation = new LocationModel();

    private LocationManager mLocationManager;

    private KaoLaLocationListener mLaLocationListener;

    public GoogleLocation() {
        init();
    }

    private void init() {
        mLaLocationListener = new KaoLaLocationListener();
        start(AppDelegate.getInstance().getContext());
    }

    @Override
    public LocationModel getLocation() {
        return mLocation;
    }


    @Override
    public void destroy() {

    }

    @Override
    public void addLocationListener(IKaoLaLocationListener listener) {
        mLaLocationListener.addLocationListener(listener);
    }

    @Override
    public void removeLocationListener(IKaoLaLocationListener listener) {
        mLaLocationListener.removeLocationListener(listener);
    }

    private void notifyLocation() {
        mLaLocationListener.locationChange(mLocation);
    }

    LocationListener locationListener = new LocationListener() {
        @Override
        public void onLocationChanged(Location location) {
            Log.i(TAG, "google location change!");
            notifyLocation(location);
        }

        @Override
        public void onStatusChanged(String provider, int status, Bundle extras) {

        }

        @Override
        @SuppressLint("MissingPermission")
        public void onProviderEnabled(String provider) {
            notifyLocation(mLocationManager.getLastKnownLocation(provider));
        }

        @Override
        public void onProviderDisabled(String provider) {
            Log.e("GoogleLocation", "onProviderDisabled: "+provider);
        }
    };

    /**
     * 定位：得到位置对象
     *
     * @return
     */
    @SuppressLint("MissingPermission")
    public void start(Context context) {
        mLocationManager = (LocationManager) context.getSystemService(LOCATION_SERVICE);
        List<String> providers = mLocationManager.getAllProviders();
        android.location.Location bestLocation = null;
        for (String provider : providers) {
            if (provider.equals(LocationManager.GPS_PROVIDER) || provider.equals(LocationManager.NETWORK_PROVIDER)) {
                if (isPermissionGranted(Manifest.permission.ACCESS_FINE_LOCATION, context)) {
                    Log.i(TAG, "添加google location listener!");
                    mLocationManager.requestLocationUpdates(provider, TIME_INTERVAL, 1000, locationListener);
                }
            }
            android.location.Location l = null;
            if (isPermissionGranted(Manifest.permission.ACCESS_FINE_LOCATION, context)) {
                l = mLocationManager.getLastKnownLocation(provider);
            }else{
                //ActivityCompat.requestPermissions((Activity)context,new String[]{Manifest.permission.ACCESS_FINE_LOCATION},1);
            }
            if (l == null) {
                continue;
            }
            if (bestLocation == null || l.getAccuracy() < bestLocation.getAccuracy()) {
                bestLocation = l;
            }
        }
        notifyLocation(bestLocation);
    }

    public boolean isPermissionGranted(String permission, Context c) {
        //int res = ContextCompat.checkSelfPermission(context, permission);
        return (ContextCompat.checkSelfPermission(c, permission) == PackageManager.PERMISSION_GRANTED);
    }

    private void notifyLocation(Location location) {
        Logging.i(TAG, "location="+location);
        if (location == null) {
            return;
        }
        mLocation.setLongitude(location.getLongitude());
        mLocation.setLatitude(location.getLatitude());
        notifyLocation();
    }

}
