package com.kaolafm.kradio.lib.init;

import androidx.annotation.IntDef;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * 进程标记。用在{@link AppInit}注解中。<br>
 * 包括：
 * {@link #ALL}--在所有进程中初始化。
 * {@link #MAIN}--在主进程中初始化。
 * {@link #OTHER}--在非主进程的其他进程中初始化。
 * <AUTHOR>
 * @date 2019-09-10
 */
public final class Process {

    /**
     * 所有进程
     */
    public static final int ALL = 100;

    /**
     * 主进程
     */
    public static final int MAIN = 101;

    /**
     * 其他非主进程
     */
    public static final int OTHER = 102;

    /**
     * 线程的限定注解
     */
    @IntDef({ALL, MAIN, OTHER})
    @Retention(RetentionPolicy.RUNTIME)
    public @interface ProcessValue{}
}
