<resources>

    <style name="Animation">
        <item name="android:activityOpenEnterAnimation">@null</item>
        <item name="android:activityOpenExitAnimation">@null</item>
        <item name="android:activityCloseEnterAnimation">@null</item>
        <item name="android:activityCloseExitAnimation">@null</item>
    </style>

    <style name="AppTheme" parent="Theme.AppCompat.NoActionBar">
        <!-- 设置无标题-->
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>//无ActionBar
        <item name="android:windowFullscreen">@bool/isWindowFullScreen</item>//全屏即无通知栏
        <item name="android:windowContentOverlay">@null</item>//是否有遮盖
        <!--<item name="android:windowBackground">@null</item>-->
        <!--<item name="android:windowBackground">@drawable/kradio_splash</item>-->
        <!--<item name="android:windowIsTranslucent">true</item>-->
        <item name="android:windowDisablePreview">false</item>
        <item name="android:windowEnableSplitTouch">false</item>
        <item name="android:splitMotionEvents">false</item>
        <item name="android:windowAnimationStyle">@style/Animation</item>
    </style>

    <style name="AppThemeCompat.Animation" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowNoTitle">true</item>//无标题
        <item name="android:windowActionBar">false</item>//无ActionBar
        <item name="android:windowFullscreen">@bool/isWindowFullScreen</item>//全屏即无通知栏
        <item name="android:windowContentOverlay">@null</item>//是否有遮盖
        <item name="android:windowAnimationStyle">@style/Animation</item>
    </style>

</resources>