package com.kaolafm.kradio.categories.broadcast;

import android.text.TextUtils;
import android.util.TypedValue;
import android.view.View;
import android.widget.TextView;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.bean.SubcategoryItemBean;
import com.kaolafm.kradio.categories.viewholder.BaseSubcategoryViewHolder;
import com.kaolafm.kradio.lib.utils.ResUtil;


/**
 * <AUTHOR>
 **/
public class HorizontalBroadcastLocalViewHolder extends BaseSubcategoryViewHolder {

    public static final String UNKNOW_FM = "未知频率";

    TextView tvBroadcastFrequencyType;
    TextView tvBroadcastFrequency;
    TextView tvBroadcastName;
    View layoutFm;
    View twoLine;


    private int mDoubleLineVisibility;
    private int mFreqLayoutVisibility;

    public HorizontalBroadcastLocalViewHolder(View itemView) {
        super(itemView);
        tvBroadcastFrequencyType=itemView.findViewById(R.id.tv_broadcast_frequency_type);
        tvBroadcastFrequency=itemView.findViewById(R.id.tv_broadcast_frequency);
        tvBroadcastName=itemView.findViewById(R.id.tv_broadcast_name);
        layoutFm=itemView.findViewById(R.id.layoutFm);
        twoLine=itemView.findViewById(R.id.twoLine);


        mFreqLayoutVisibility = ResUtil.getInt(R.integer.broadcast_freq_visibility);
        mDoubleLineVisibility = ResUtil.getInt(R.integer.broadcast_default_d_line_visibility);
    }

    @Override
    public void setupData(SubcategoryItemBean subcategoryItemBean, int position) {
        super.setupData(subcategoryItemBean, position);
        String type;
        String frequency = subcategoryItemBean.getName();

        if (!TextUtils.isEmpty(frequency)) {
            if (frequency.startsWith("FM")) {
                type = "FM";
                frequency = frequency.substring(2);
            } else if (frequency.startsWith("AM")) {
                type = "AM";
                frequency = frequency.substring(2);
            } else {
                type = "";
            }
        } else {
            type = "";
            frequency = UNKNOW_FM;
        }

        if (UNKNOW_FM.equals(frequency)) {
            //如果没有fm信息
//            layoutFm.setVisibility(View.INVISIBLE);
//            twoLine.setVisibility(View.VISIBLE);
            // 解决https://app.huoban.com/tables/2100000007530121/items/2300001293389347问题
            layoutFm.setVisibility(mFreqLayoutVisibility);
            twoLine.setVisibility(mDoubleLineVisibility);
            tvBroadcastFrequencyType.setText(type);
            tvBroadcastFrequency.setText(frequency);
        } else {
            //
            layoutFm.setVisibility(View.VISIBLE);
            twoLine.setVisibility(View.GONE);
            tvBroadcastFrequencyType.setText(type);
            tvBroadcastFrequency.setText(frequency);
        }
        //广播名
        tvBroadcastName.setText(subcategoryItemBean.getTitle());
        tvBroadcastFrequencyType.setTextSize(TypedValue.COMPLEX_UNIT_PX, ResUtil.getDimen(R.dimen.subcategory_item_broadcast_local_text_size_fm));
        tvBroadcastFrequency.setTextSize(TypedValue.COMPLEX_UNIT_PX, ResUtil.getDimen(R.dimen.subcategory_item_broadcast_local_text_size_freq));

        setPlay(subcategoryItemBean);
    }

    /**
     * 设置播放动画
     *
     * @param subcategoryItemBean
     */
    private void setPlay(SubcategoryItemBean subcategoryItemBean) {
        if (subcategoryItemBean.isSelected()) {
            tvBroadcastFrequencyType.setSelected(true);
            tvBroadcastFrequency.setSelected(true);
            tvBroadcastName.setSelected(true);
        } else {
            tvBroadcastFrequencyType.setSelected(false);
            tvBroadcastFrequency.setSelected(false);
            tvBroadcastName.setSelected(false);
        }
    }
}
