package com.kaolafm.kradio.k_kaolafm.categories;

import android.util.Log;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;

@Aspect
public class AllCategoriesAspect {

    private String TAG = "AllCategoriesAspect";

    @Around("execution(* AllCategoriesFragment.changeTabTextSize(..))")
    public void changeTabTextSize(ProceedingJoinPoint point) throws Throwable {
        AllCategoriesFragment fragment = (AllCategoriesFragment) point.getThis();
        Object[] args = point.getArgs();
        Log.i(TAG,"origin args:"+args[0]);
        args[0] = true;
        Log.i(TAG,"changed args:"+args[0]);
        point.proceed(args);
    }

}
