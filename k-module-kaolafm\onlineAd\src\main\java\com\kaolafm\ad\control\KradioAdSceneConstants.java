package com.kaolafm.ad.control;

/**
 * 广告曝光场景id
 */
public class KradioAdSceneConstants {

    public final static int SPREND_SCENE = 7; // 开屏广告
    public final static int ALBUM_SCENE = 8; //节目切换广告曝光场景 的 专辑下碎片播放
    public final static int RADIO_SCENE = 9;//节目切换广告曝光场景的 智能电台碎片播放
    public final static int BRAND_SCENE = 10;//节目切换广告曝光场景的  品牌电台播放
    public final static int HOME_PAGE_COLOUM_SCENE = 5; // 首页栏目成员封面
    //    public final static int PROGROM_IMPLANTATION_SCRENE = 6;// 节目植入广告
    public final static int TIMER_SCENE = 11;// 定时广告

    public final static int SUB_TYPE_SWITCH_PROGROM = 10001; // 切换节目广告 subtype
    public final static int SUB_TYPE_SPRAND_SCREEN = 10002;  // 开屏广告 subtype
    public final static int SUB_TYPE_COLOUM = 10003;  // 首页栏目广告 subtype
    public final static int SUB_TYPE_IMPLANTATION = 10004;  // 节目植入 subtype
    public final static int AD_TYPE_SWITCH_RADIO_AUDIO = 10005; //编排位切换节目广告

    public final static int AD_TYPE_AUDIO = 3;// 广告类型：音频
    public final static int AD_TYPE_IMAGE = 4;// 广告类型：图片
    public final static int AD_TYPE_AUDIO_IMAGE = 5;// 广告类型：音图

    public final static int INTERACT_AD_OPPORTUNITY_BEFORE = 1;//二次互动曝光时机：广告曝光时
    public final static int INTERACT_AD_OPPORTUNITY_AFTER = 2;//广告曝光后

    public static String TYPE_AUDIO_AD = "audio_type";
}
