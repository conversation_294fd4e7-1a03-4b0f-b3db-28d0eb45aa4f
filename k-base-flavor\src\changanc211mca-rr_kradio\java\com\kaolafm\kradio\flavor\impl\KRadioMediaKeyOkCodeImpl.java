package com.kaolafm.kradio.flavor.impl;

import com.kaolafm.kradio.common.report.ReportUtil;
import com.kaolafm.kradio.lib.base.flavor.KRadioMediaKeyOkCodeInter;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.report.event.PlayerUiControlReportEvent;

public class KRadioMediaKeyOkCodeImpl implements KRadioMediaKeyOkCodeInter {
    @Override
    public void onClick() {
        boolean isPlaying = PlayerManager.getInstance().isPlaying();
        if (isPlaying) {
            ReportUtil.addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_PAUSE, PlayerUiControlReportEvent.CONTROL_TYPE_HARD_WARE, null);
        } else {
            ReportUtil.addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_PLAY, PlayerUiControlReportEvent.CONTROL_TYPE_HARD_WARE, null);
        }
        PlayerManagerHelper.getInstance().switchPlayerStatus(true);
    }
}
