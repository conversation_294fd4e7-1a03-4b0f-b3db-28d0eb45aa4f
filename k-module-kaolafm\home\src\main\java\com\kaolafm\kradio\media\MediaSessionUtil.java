package com.kaolafm.kradio.media;

import android.support.v4.media.session.MediaSessionCompat;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioMediaSessionInter;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.media.session.KRMediaSessionHelper;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-06-05 15:51
 ******************************************/
public final class MediaSessionUtil {
    private KRadioMediaSessionInter mKRadioMediaSessionInter;

    private MediaSessionUtil() {
        mKRadioMediaSessionInter = ClazzImplUtil.getInter("KRadioMediaSessionImpl");
    }

    private static class MediaSessionUtilInstance {
        private static final MediaSessionUtil MEDIA_SESSION_UTIL = new MediaSessionUtil();
    }

    public static MediaSessionUtil getInstance() {
        return MediaSessionUtilInstance.MEDIA_SESSION_UTIL;
    }

    private KRMediaSessionHelper mKRMediaSessionHelper;

    /**
     * 注册多媒体按键
     */
    public void registerMediaSession() {
        if (mKRadioMediaSessionInter != null) {
            mKRadioMediaSessionInter.registerMediaSession(AppDelegate.getInstance().getContext());
        } else {
            if (mKRMediaSessionHelper == null) {
                mKRMediaSessionHelper = new KRMediaSessionHelper();
            }
            mKRMediaSessionHelper.registerMediaSession(AppDelegate.getInstance().getContext());
        }
    }

    /**
     * 注销多媒体按键
     */
    public void unregisterMediaSession() {
        if (mKRadioMediaSessionInter != null) {
            mKRadioMediaSessionInter.unregisterMediaSession(AppDelegate.getInstance().getContext());
        } else if (mKRMediaSessionHelper != null) {
            mKRMediaSessionHelper.unregisterMediaSession(AppDelegate.getInstance().getContext());
        }
    }

    public MediaSessionCompat getMediaSession() {
        if (mKRadioMediaSessionInter != null) {
            //定制需求不会使用MediaSessionCompat对象，故此处暂返null
            return null;
        } else if (mKRMediaSessionHelper != null) {
            return mKRMediaSessionHelper.getMediaSession();
        }
        return null;
    }

    public void release() {
        if (mKRadioMediaSessionInter != null) {
            mKRadioMediaSessionInter.release();
        } else {
            if (mKRMediaSessionHelper != null) {
                mKRMediaSessionHelper.release();
            }
        }
    }
}