package com.kaolafm.kradio.search;

import com.kaolafm.opensdk.api.search.model.SearchProgramBean;

public class SearchProgramBeanResult extends SearchProgramBean {
    public SearchProgramBeanResult(SearchProgramBean in) {
        isPlaying = false;
        copy(in);
    }

    private void copy(SearchProgramBean in) {
        setId(in.getId());
        setImg(in.getImg());
        setName(in.getName());
//        setOldId(in.getOldId());
        setPlayUrl(in.getPlayUrl());
//        setSource(in.getSource());
//        setSourceName(in.getSourceName());
        setType(in.getType());
        setAlbumName(in.getAlbumName());
        setComperes(in.getComperes());
        setDuration(in.getDuration());
        setFreq(in.getFreq());
        setCallback(in.getCallback());
        setListenNum(in.getListenNum());
        setHighlight(in.getHighlight());
        setFine(in.getFine());
        setVip(in.getVip());
        setProgramEnable(in.getProgramEnable());
        setCurrentProgram(in.getCurrentProgram());
        setRecommend(in.getRecommend());
    }

    public boolean isPlaying;
}
