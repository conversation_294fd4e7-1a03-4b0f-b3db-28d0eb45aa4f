package com.kaolafm.ads.image;

import android.app.Activity;
import android.content.Context;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.View;

import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.common.router.RouterManager;

import java.net.URLDecoder;
import java.util.HashMap;
import java.util.Map;

public class AdInteractWithWebviewContentView extends AdInteractContentView {
    public AdInteractWithWebviewContentView(Context context) {
        super(context);
    }

    public AdInteractWithWebviewContentView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public AdInteractWithWebviewContentView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected void onViewClick(View view) {
        super.onViewClick(view);
        if (!showWeb(getContext())) {
            Activity topActivity = AppManager.getInstance().getTopActivity();
            showWeb(topActivity);
        }
    }

    private boolean showWeb(Context context) {
        if (context != null) {
            Logger.e("AdInteractWithWebviewContentView", "url=" + mDestUrl);
            if (!interceptApplicationJumpEvent(context, mDestUrl)) {
                //不属于应用内跳转
                WebViewActivity.start(context, mDestUrl);
            }
            return true;
        }
        return false;
    }

    /**
     * 拦截应用内跳转事件
     *
     * @param context
     * @param mDestUrl
     * @return
     */
    private boolean interceptApplicationJumpEvent(Context context, String mDestUrl) {
        if (StringUtil.isNotEmpty(mDestUrl)) {
            if (mDestUrl.startsWith(Constants.ROUTER_IDENTIFICATION)) {
                Map<String, String> params = parseUrlParams(mDestUrl);
                if (params.containsKey(Constants.ROUTER_PARAMS_KEY_PAGE_ID)) {
                    String pageId = params.get(Constants.ROUTER_PARAMS_KEY_PAGE_ID);
                    RouterManager.getInstance().navigateToPage(context, pageId, params);
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 通过url解析出参数
     *
     * @param url
     * @return
     */
    private Map<String, String> parseUrlParams(String url) {
        Map<String, String> map = new HashMap<>();
        try {
            final String charset = "utf-8";
            url = URLDecoder.decode(url, charset);
            if (url.indexOf('?') != -1 && !url.endsWith("?")) {
                final String contents = url.substring(url.indexOf('?') + 1);
                String[] keyValues = contents.split("&");
                for (int i = 0; i < keyValues.length; i++) {
                    String key = keyValues[i].substring(0, keyValues[i].indexOf("="));
                    String value = keyValues[i].substring(keyValues[i].indexOf("=") + 1);
                    map.put(key, value);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return map;
    }
}
