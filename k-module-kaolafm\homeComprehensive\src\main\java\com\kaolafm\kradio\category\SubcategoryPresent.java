package com.kaolafm.kradio.category;

import android.text.TextUtils;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.categories.ISubcategoryView;
import com.kaolafm.kradio.categories.Subcategory;
import com.kaolafm.kradio.categories.SubcategoryModel;
import com.kaolafm.kradio.lib.bean.SubcategoryItemBean;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.opensdk.api.operation.model.category.Category;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.trello.rxlifecycle3.android.FragmentEvent;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-07-23
 */
public class SubcategoryPresent extends BasePresenter<SubcategoryModel, ISubcategoryView> {

    private long mCategoryId;
    List<SubcategoryItemBean> subcategoryItemBeans = new ArrayList<>();

    private long mShowId;

    public SubcategoryPresent(ISubcategoryView view, long categoryId, long showId) {
        super(view);
        mCategoryId = categoryId;
        mShowId = showId;
    }

    public int getPositionByTabCode(List<SubcategoryItemBean> dataList, String code) {
        if (!ListUtil.isEmpty(dataList)) {
            for (int i = 0; i < dataList.size(); i++) {
                String parentCode = dataList.get(i).getParentCode();
                if (!TextUtils.isEmpty(parentCode) && parentCode.equals(code)) {
                    return i;
                }
            }
        }
        return -1;
    }

    public void onClick(SubcategoryItemBean subcategoryItemBean, int position) {
        ClickHelper.onClick(subcategoryItemBean, position);
    }

    @Override
    protected SubcategoryModel createModel() {
        return new SubcategoryModel(((BaseFragment) mView).bindUntilEvent(FragmentEvent.DESTROY));
    }

    @Override
    public void start() {
        super.start();
        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(),false)) {
            if (mView != null) {
                mView.showError(new ApiException(ErrorCode.NO_NET, "数据为空"));
            }
            return;
        }
        mModel.getSubcategory(mCategoryId, new HttpCallback<Subcategory>() {
            @Override
            public void onSuccess(Subcategory subcategory) {
                if (mView != null) {
                    ArrayList<SubcategoryItemBean> itemBeans = subcategory.itemBeans;
                    if (!ListUtil.isEmpty(itemBeans)) {
                        mView.showSubcontent(itemBeans);
                        mView.showSubtitles(subcategory.tabs);
                    } else {
                        mView.showError(new ApiException("数据为空"));
                    }
                }
            }

            @Override
            public void onError(ApiException e) {
                if (mView != null) {
                    mView.showError(e);
                }
            }
        });
    }

    private void getChildOrMember(List<SubcategoryItemBean> subcategoryItemBeans, Category category) {
        mModel.getSubcategoryItemBeanByParentId(Long.parseLong(category.getCode()), true,
                new HttpCallback<List<SubcategoryItemBean>>() {
                    @Override
                    public void onSuccess(List<SubcategoryItemBean> itemBeanList) {
                        subcategoryItemBeans.addAll(itemBeanList);
                    }

                    @Override
                    public void onError(ApiException e) {

                    }
                });
    }

}
