package com.kaolafm.kradio.online.player.mvp;

import com.kaolafm.kradio.lib.base.mvp.IView;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.media.model.AudioDetails;

import java.util.List;

public interface AlbumProgramListView extends IView {
    void onGetAlbumProgramListDataSuccess(BasePageResult<List<AudioDetails>> playItemArrayList);

    void onGetAlbumProgramListDataError(int code, String msg);
}
