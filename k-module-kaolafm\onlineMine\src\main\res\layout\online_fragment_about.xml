<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.kaolafm.kradio.online.mine.page.OnlineAboutFragment">

    <ScrollView
        app:layout_constraintBottom_toBottomOf="parent"
        android:id="@+id/scroll"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_gravity="center"
            android:id="@+id/container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/person_center_aboutus_details_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/aboutus_magin"
                android:layout_marginTop="@dimen/y20"
                android:layout_marginRight="@dimen/aboutus_magin"
                android:lineSpacingMultiplier="1.6"
                android:textColor="@color/online_about_content_text_color"
                android:textSize="@dimen/m24"
                tools:text="@string/person_center_aboutus_details_str" />


            <ImageView
                android:id="@+id/person_center_aboutus_tingbanlogo_iv"
                android:layout_width="@dimen/m90"
                android:layout_height="@dimen/m90"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/y30"
                android:scaleType="centerInside"
                android:src="@drawable/ic_launcher_car"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@id/person_center_aboutus_version_tv"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintVertical_bias="0.77" />

            <TextView
                android:id="@+id/person_center_aboutus_version_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/y11"
                android:layout_marginBottom="@dimen/y24"
                android:gravity="center"
                android:lineSpacingMultiplier="1.5"
                android:text="@string/person_center_aboutus_version_str"
                android:textColor="@color/colorWhite"
                android:textSize="@dimen/text_size3"
                android:visibility="gone" />

            <LinearLayout
                android:id="@+id/person_center_aboutus_ll"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/y70"
                android:gravity="center"
                android:orientation="horizontal"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@id/person_center_aboutus_version_tv">

                <Button
                    android:id="@+id/person_center_aboutus_service_btn"
                    android:layout_width="@dimen/x208"
                    android:layout_height="@dimen/y56"
                    android:background="@drawable/online_about_tv_bg"
                    android:gravity="center"
                    android:text="@string/person_center_aboutus_service_str"
                    android:textColor="@color/online_about_btn_text_color"
                    android:textSize="@dimen/m24" />

                <Button
                    android:id="@+id/person_center_aboutus_secret_btn"
                    android:layout_width="@dimen/x208"
                    android:layout_height="@dimen/y56"
                    android:layout_marginLeft="@dimen/x120"
                    android:background="@drawable/online_about_tv_bg"
                    android:gravity="center"
                    android:text="@string/person_center_aboutus_private_str"
                    android:textColor="@color/online_about_btn_text_color"
                    android:textSize="@dimen/m24" />
            </LinearLayout>
        </LinearLayout>

    </ScrollView>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/web_view_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="@dimen/x50"
        android:layout_marginRight="@dimen/x50"
        android:visibility="gone"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/web_view_back"
            style="@style/FragmentBackButton"
            android:tint="@color/colorWhite"
            tools:ignore="MissingConstraints" />

        <TextView
            android:id="@+id/web_view_title_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/m32"
            app:layout_constraintBottom_toBottomOf="@+id/web_view_back"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/web_view_back" />

        <WebView
            android:id="@+id/web_view_content"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/web_view_back" />


    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>