package com.kaolafm.kradio.live.player;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.google.gson.Gson;
import com.kaolafm.kradio.common.http.CommonRequestParamsUtil;
import com.kaolafm.kradio.component.ComponentClient;
import com.kaolafm.kradio.component.DynamicComponent;
import com.kaolafm.kradio.component.RealCaller;
import com.kaolafm.kradio.constant.SettingPropertiesProcessorConst;
import com.kaolafm.kradio.constant.UserComponentConst;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.ComponentUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.live.mvp.LivePresenter;
import com.kaolafm.kradio.live.utils.LiveBeanTransUtils;
import com.kaolafm.kradio.live.utils.LiveUtil;
import com.kaolafm.kradio.user.UserInfoManager;
import com.kaolafm.opensdk.api.live.model.ChatUserInfo;
import com.kaolafm.opensdk.api.live.model.LiveChatRoomMessageNew;
import com.kaolafm.opensdk.api.live.model.LiveStatusCode;
import com.kaolafm.opensdk.api.live.model.MessageBean;
import com.kaolafm.opensdk.api.login.model.UserInfo;
import com.kaolafm.opensdk.api.yunxin.model.GiftMsg;
import com.kaolafm.opensdk.api.yunxin.model.GiftRankMsg;
import com.kaolafm.opensdk.api.yunxin.model.GoodsCardMsg;
import com.kaolafm.opensdk.api.yunxin.model.LiveCommentStatusMsg;
import com.kaolafm.opensdk.api.yunxin.model.LiveOtherUserEnterExitMsg;
import com.kaolafm.opensdk.api.yunxin.model.LiveStatusMsg;
import com.kaolafm.opensdk.api.yunxin.model.LiveTextMsg;
import com.kaolafm.opensdk.live.LiveRecvMsgListener;
import com.kaolafm.opensdk.live.LiveSocketManager;
import com.kaolafm.opensdk.live.LiveUserEnterChatroomListener;
import com.kaolafm.opensdk.live.LiveUserExitChatroomListener;
import com.kaolafm.opensdk.live.LiveUserForbiddenListener;
import com.kaolafm.opensdk.socket.SocketApiConstants;
import com.kaolafm.opensdk.socket.SocketManager;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.HashMap;

/**
 * 管理云信的登录登出，聊天室的进入退出，消息的发送，接收，云信token的管理。
 * 需要注意的是，登录云信和进入聊天室是不同的。要登录云信，先得注册云信的token，这一步由考拉FM的服务器中转
 * 完成，所以客户端只需要请求中转服务的接口，取到token后，自动登录云信，并且进入聊天室。这一个环节一般没有
 * 问题，但也可能出现中转服务器返回的token不能登录云信的问题，目前这种情况在客户端没有提示。表现形式是：
 * 看不到“XXX进入聊天室”的动画。
 *
 * <AUTHOR> Huangui
 */
public class NimManager {

    public static final String TAG = "NimManager";

    private static Context sContext = AppDelegate.getInstance().getContext().getApplicationContext();


    /**
     * 未登录即加入聊天室异常错误
     */
    public static final int UNLOGIN_TO_JOIN_CHATROOM_ERROR = 1000;

    /**
     * 服务异常错误码
     */
    public static final int SERVICE_ERROR_CODE = 50802;

    /**
     * 未知Host异常错误码
     */
    public static final int UNKNOWNHOST_ERROR_CODE = 50803;

    /**
     * 用户区分UDID、UID和TOKEN的分隔符
     */
    private static final String SPECIAL_DELIMITER_STR = "#";

    /**
     * 加入聊天室使用的扩展字段（昵称）key值
     */
    public static final String NICK_NAME_KEY = "nickName";
    /**
     * 加入聊天室使用的扩展字段（头像）key值
     */
    public static final String AVATAR_KEY = "avatar";

    public static final String TYPE_KEY = "type";
    public static final String CONTENT_KEY = "msg";


    /**
     * 黑名单操作相关数据类型
     */
    public final static int MSG_TYPE_BLACKLIST = 5;

    /**
     * 禁言操作相关数据类型
     */
    public final static int MSG_TYPE_MUTE = 6;

    /**
     * 接收消息
     */
    public final static int MSG_TYPE_RECEIVE = 0;

    /**
     * 发送消息
     */
    public final static int MSG_TYPE_SEND = 1;

    /**
     * 删除消息
     */
    public final static int MSG_TYPE_REMOVE = 2;


    public final static Integer LIVE_CUSTOM_MSG_STATUS_TYPE = 1;//1：直播状态变更消息；
    public final static Integer LIVE_CUSTOM_MSG_RANK_TYPE = 2;//2：排行榜更新消息；
    public final static Integer LIVE_CUSTOM_MSG_GIFTS_TYPE = 3;//3：打赏消息；
    public final static Integer LIVE_CUSTOM_MSG_GOODS_CARD_TYPE = 4;//4：讲解中商品消息;

    /**
     * 禁言用户
     */
    private static final int MEMBER_IN_MUTED_LIST_ERROR_CODE = 13004;

    /**
     * 当前聊天室ID
     */
    private String mRoomId;

    /**
     * 当前登录用户是否已经被踢出房间 true为是，false为否
     */
    private boolean isUserKickOut;

    /**
     * 用户账户
     */
    private String mAccount;

    /**
     * 用户昵称
     */
    private String mNickName;

    /**
     * 当前用户是否被禁言
     */
    private boolean isVoiceRevoked;

    /**
     * 是否已经进入聊天室
     */
    private boolean bChatRoomEntered;

    /**
     * 避免fragment创建时重复初始化云信SDK
     */
    private boolean bNimInited;

    private Long mLiveId;

    private NimLoginComponent mNimLoginComponent;

    private ArrayList<OnChatMessageReceivedListener> mOnChatMessageReceivedListenerList
            = new ArrayList<OnChatMessageReceivedListener>();
    private ArrayList<RoomMemberChangedObserver> mRoomMemberChangedObservers
            = new ArrayList<RoomMemberChangedObserver>();


    private NimManager() {
        addUserLoginListener();
    }

    private static final class INSTANCE_HOLDER {
        private static final NimManager INSTANCE = new NimManager();
    }

    public static NimManager getInstance() {
        return INSTANCE_HOLDER.INSTANCE;
    }

    public void initNim() {
        if (!bNimInited) {
            bNimInited = true;
        }
    }

    public void setLiveId(Long liveId) {
        this.mLiveId = liveId;
    }

    /**
     * 监听用户退出行为，如果用户退出了账号，就没有理由留在云信或聊天室
     */
    private void addUserLoginListener() {
        mNimLoginComponent = new NimLoginComponent();
        ComponentUtil.addObserver(UserComponentConst.NAME, mNimLoginComponent);
    }

    private class NimLoginComponent implements DynamicComponent {

        @Override
        public String getName() {
            return NimManager.this.getClass().getName();
        }

        @Override
        public boolean onCall(RealCaller caller) {
            String actionName = caller.actionName();
            if ("logout".equals(actionName)) {
                logoutNim();
            }
            return false;
        }
    }

    /**
     * 进入聊天室，如果已经在聊天室，首先判断云信登录状态，如果已经登录，直接进入聊天室，否则，要会登录
     *
     * @param context
     * @param rid      聊天室id
     * @param listener
     */
    public void enterChatRoom(Context context, String rid, NimManager.EnterChatRoomListener listener) {
        enterChatRoom(false, context, rid, listener);
    }

    /**
     * 进入聊天室，如果已经在聊天室，首先判断云信登录状态，如果已经登录，直接进入聊天室，否则，要会登录
     *
     * @param context
     * @param rid     聊天室id
     */
    public void enterChatRoom(boolean ignoreLoginState, Context context, String rid, NimManager.EnterChatRoomListener enterChatRoomListener) {
        String roomId = String.valueOf(rid);
        boolean isBound = isUserBound();
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "enterChatRoom roomId: " + rid + ", mRoomId: " + mRoomId
                    + ", login: " + isBound
                    + ", bChatRoomEntered: " + bChatRoomEntered);
        }
        // do live 新接口逻辑
        if (bChatRoomEntered) {
            //如果已经在聊天室，并且要进入的是同一个聊天室，返回，如果不是，先退出前一个聊天室
            if (roomId.equals(mRoomId)) {
                return;
            } else {
                exitChatRoom(false);
            }
        }
        mRoomId = roomId;
        if (!isBound) {
            mAccount = null;
            mNickName = null;
            if (!ignoreLoginState)
                return;
        }
        // 非云信SDK不需要判断是否登录云信服务器这一步
        mAccount = getUserAccount();
        mNickName = getUserNickName();
        enterSocketChatRoom();
        // 目前后端不给进入直播间事件的回调，先直接默认进入成功
        if (enterChatRoomListener != null) {
            enterChatRoomListener.enterChatRoomSuccess();
        }
    }

    public boolean isChatRoomEntered() {
        return bChatRoomEntered;
    }

    public void setChatRoomEntered(boolean chatRoomEntered) {
        this.bChatRoomEntered = chatRoomEntered;
    }

    /**
     * 退出Nim服务器（注销用户）
     * 注意：不要在 Activity(Fragment) 的 onDestroy 方法中调用
     */
    public void logoutNim() {
        if (TextUtils.isEmpty(mAccount)) {
            return;
        }
        setChatRoomEntered(false);
        mAccount = null;
        mNickName = null;
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "logoutNim start");
        }
    }

    private void managerUserKickOut() {
        if (!isUserKickOut) {
            notifyUserKickOut();
        }
        isUserKickOut = true;
        onUserInfoInvalid(sContext);
    }

    /**
     * 处理用户token失效问题
     */
    public void onUserInfoInvalid(Context context) {
        if (mRoomId != null) {
            exitChatRoom();
        }
        logoutNim();
        clearNimUIDAndTokenAccountValue(context);
    }

    /**
     * 针对登录用户切换处理逻辑
     *
     * @param context
     */
    private void clearNimUIDAndTokenAccountValue(Context context) {
        setLivingUidToken(Constants.BLANK_STR);
    }

    private void notifyUserKickOut() {
    }


    /**
     * 退出聊天室，清理相关监听
     */
    public void exitChatRoom() {
        exitChatRoom(true);
    }

    /**
     * 退出聊天室，clearObserver为true则清理相关监听
     *
     * @param clearObserver
     */
    public void exitChatRoom(boolean clearObserver) {
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "exitChatRoom");
        }
        // do live 新接口逻辑
        isUserKickOut = false;
        bChatRoomEntered = false;
        exitSocketChatRoom();
        mRoomId = null;
        mAccount = null;
        mNickName = null;
        setChatRoomEntered(false);
        if (clearObserver) {
            if (mRoomMemberChangedObservers.size() > 0) {
                mRoomMemberChangedObservers.clear();
            }
            if (mOnChatMessageReceivedListenerList.size() > 0) {
                mOnChatMessageReceivedListenerList.clear();
            }
        }
    }

    /**
     * 解析自定义消息
     *
     * @param type
     * @param msg
     */
    public void parseCustomMsg(Integer type, String msg) {
        try {
            // 开始正式解析自定义消息
            if (type.equals(LIVE_CUSTOM_MSG_STATUS_TYPE)) {//1：直播状态变更消息；
                LiveStatusMsg liveStatusMsg = new Gson().fromJson(msg, LiveStatusMsg.class);
                EventBus.getDefault().post(liveStatusMsg);
            } else if (type.equals(LIVE_CUSTOM_MSG_RANK_TYPE)) {//2：排行榜更新消息；
                GiftRankMsg giftRankMsg = new Gson().fromJson(msg, GiftRankMsg.class);
                EventBus.getDefault().post(giftRankMsg);
            } else if (type.equals(LIVE_CUSTOM_MSG_GIFTS_TYPE)) {//3：打赏消息；
                GiftMsg giftMsg = new Gson().fromJson(msg, GiftMsg.class);
                EventBus.getDefault().post(giftMsg);
            } else if (type.equals(LIVE_CUSTOM_MSG_GOODS_CARD_TYPE)) {//4：讲解中商品消息;
                GoodsCardMsg shopCardMsg = new Gson().fromJson(msg, GoodsCardMsg.class);
                EventBus.getDefault().post(shopCardMsg);
            } else {

            }
        } catch (Exception e) {
            Log.e(TAG, "parseCustomMsg:error=" + e.getMessage());
        }
    }

    /**
     * 更新消息列表，一般在收到消息以后使用，
     *
     * @param data
     */
    private void updateMsgListData(final MessageBean data) {
        ArrayList<MessageBean> messageBeanArrayList = new ArrayList<>(1);
        messageBeanArrayList.add(data);
        notifyMessageReceived(messageBeanArrayList);
    }

    private void notifyMessageReceived(ArrayList<MessageBean> messageData) {
        ArrayList<OnChatMessageReceivedListener> onChatMessageReceivedListeners =
                (ArrayList<OnChatMessageReceivedListener>) mOnChatMessageReceivedListenerList.clone();
        for (int i = 0, size = onChatMessageReceivedListeners.size(); i < size; i++) {
            OnChatMessageReceivedListener onChatMessageReceivedListener = onChatMessageReceivedListeners.get(i);
            if (onChatMessageReceivedListener == null) {
                continue;
            }
            onChatMessageReceivedListener.onChatMessageReceived(messageData);
        }
    }

    private boolean isUserBound() {
        return LiveUtil.isUserBound();
    }

    /**
     * 获取当前聊天室登录账号
     *
     * @return
     */
    public String getAccount() {
        return mAccount;
    }

    public String getNickName() {
        return mNickName;
    }

    public void setAccount(String account) {
        mAccount = account;
    }

    public void setNickName(String nickName) {
        mNickName = nickName;
    }

    /**
     * 获取当前聊天室ID
     *
     * @return
     */
    public String getRoomId() {
        return mRoomId;
    }

    public void setRoomId(String roomId) {
        mRoomId = roomId;
    }

    /**
     * 监听收到聊天室消息
     */
    public interface OnChatMessageReceivedListener {
        void onChatMessageReceived(ArrayList<MessageBean> messageData);
    }

    /**
     * 监听聊天室其它成员的进入退出
     */
    public interface RoomMemberChangedObserver {
        void onRoomMemberIn(ChatUserInfo chatUserInfo);

        void onRoomMemberExit(ChatUserInfo chatUserInfo);
    }

    public interface EnterChatRoomListener {
        /**
         * 登录直播聊天室或加入直播聊天室出现异常
         *
         * @param throwable
         */
        void onException(Throwable throwable);

        /**
         * 登录失败
         *
         * @param code
         */
        void loginFailed(int code);

        /**
         * 登录聊天室成功
         */
        void loginSuccess(String account);

        void enterChatRoomSuccess();

        /**
         * 加入聊天室失败
         */
        void enterChatRoomFailed(int code);
    }

    public void addRoomMemberChangedObserver(RoomMemberChangedObserver o) {
        if (!mRoomMemberChangedObservers.contains(o)) {
            mRoomMemberChangedObservers.add(o);
        }
    }

    public void addChatMessageReceivedListener(OnChatMessageReceivedListener o) {
        if (!mOnChatMessageReceivedListenerList.contains(o)) {
            mOnChatMessageReceivedListenerList.add(o);
        }
    }

    private void setLivingUidToken(String livingUidToken) {
        ComponentClient.obtainBuilder(UserComponentConst.NAME)
                .setActionName(SettingPropertiesProcessorConst.SET_LIVING_UID_TOKEN)
                .addParam(SettingPropertiesProcessorConst.SET_LIVING_UID_TOKEN, livingUidToken)
                .build().call();
        ComponentUtil.call(UserComponentConst.NAME, SettingPropertiesProcessorConst.SET_LIVING_UID_TOKEN);
    }

    /*
     *********************************************************************
     *************************新版直播间接口*********************************
     *********************************************************************
     */

    /**
     * 新版进入直播间
     */
    public void enterSocketChatRoom() {
        callUserEnterChatRoomListener();
        registerLiveSocketListener();
    }

    /**
     * 新版退出直播间
     */
    public void exitSocketChatRoom() {
        callUserExitChatRoomListener();
        unRegisterLiveSocketListener();
    }

    /**
     * 统一注册直播的长链接监听
     */
    public void registerLiveSocketListener() {
        registerLiveRecvMsgListener();
        registerUserForbiddenListener();
    }

    /**
     * 统一解除直播的长链接监听
     */
    public void unRegisterLiveSocketListener() {
        unRegisterUserEnterChatRoomListener();
        unRegisterUserExitChatRoomListener();
        unRegisterLiveRecvMsgListener();
        unRegisterUserForbiddenListener();
    }

    public String getUserAccount() {
        return UserInfoManager.getInstance().getUserId();
    }

    public String getUserAvatar() {
        UserInfo userInfo = UserInfoManager.getInstance().getUserInfo();
        if (userInfo == null) {
            return "";
        }
        return userInfo.getAvatar();
    }

    public String getUserNickName() {
        UserInfo userInfo = UserInfoManager.getInstance().getUserInfo();
        if (userInfo == null) {
            return "";
        }
        return userInfo.getNickName();
    }

    /**
     * Socket的参数
     */
    private HashMap<String, String> getSocketParams() {
        HashMap<String, String> commonParams = CommonRequestParamsUtil.getCommonParams();
        commonParams.put("liveId", String.valueOf(mLiveId));
        commonParams.put("roomId", getRoomId());
        commonParams.put("userType", LiveUtil.isUserBound() ? "1" : "0");
        commonParams.put("account", getUserAccount());
        commonParams.put("avatar", getUserAvatar());
        commonParams.put("nickName", getUserNickName());
        return commonParams;
    }

    /**
     * 注册监听 - 进入直播间
     */
    public void callUserEnterChatRoomListener() {
        Log.d(TAG, "live--- callUserEnterChatRoomListener()");
        SocketManager.getInstance()
                .setMap(getSocketParams())
                .setSocketHost(SocketApiConstants.SOCKET_HOST)
                .request(LiveUserEnterChatroomListener.INSTANCE);
    }

    /**
     * 解除监听 - 进入直播间
     */
    public void unRegisterUserEnterChatRoomListener() {
        Log.d(TAG, "live--- unRegisterUserEnterChatRoomListener()");
        SocketManager.getInstance().removeListener(LiveUserEnterChatroomListener.INSTANCE);
    }

    /**
     * 注册监听 - 退出直播间
     */
    public void callUserExitChatRoomListener() {
        Log.d(TAG, "live--- callUserExitChatRoomListener()");
        SocketManager.getInstance()
                .setMap(getSocketParams())
                .setSocketHost(SocketApiConstants.SOCKET_HOST)
                .request(LiveUserExitChatroomListener.INSTANCE);
    }

    /**
     * 解除监听 - 退出直播间
     */
    public void unRegisterUserExitChatRoomListener() {
        Log.d(TAG, "live--- unRegisterUserExitChatRoomListener()");
        SocketManager.getInstance().removeListener(LiveUserExitChatroomListener.INSTANCE);
    }


    /**
     * 注册监听 - 直播 - 实时接收聊天室消息（用户消息以及直播间通知） - 客户端监听事件
     */
    public void registerLiveRecvMsgListener() {
        Log.d(TAG, "live--- registerLiveRecvMsgListener()");
        LiveSocketManager.getInstance().registerLiveRecvMsgListener(mOnLiveRecvMsgListener);
        HashMap<String, String> commonParams = CommonRequestParamsUtil.getCommonParams();
        SocketManager.getInstance()
                .setMap(commonParams)
                .setSocketHost(SocketApiConstants.SOCKET_HOST)
                .request(LiveRecvMsgListener.INSTANCE);
    }

    /**
     * 解除监听 - 直播 - 实时接收聊天室消息（用户消息以及直播间通知） - 客户端监听事件
     */
    public void unRegisterLiveRecvMsgListener() {
        Log.d(TAG, "live--- unRegisterLiveRecvMsgListener()");
        LiveSocketManager.getInstance().unRegisterLiveRecvMsgListener(mOnLiveRecvMsgListener);
        SocketManager.getInstance().removeListener(LiveRecvMsgListener.INSTANCE);
    }

    /**
     * 注册监听 - 用户被禁言，被踢出直播间
     */
    public void registerUserForbiddenListener() {
        Log.d(TAG, "live--- registerUserForbiddenListener()");
        LiveSocketManager.getInstance().registerUserForbiddenListener(mOnUserForbiddenListener);
        HashMap<String, String> commonParams = CommonRequestParamsUtil.getCommonParams();
        SocketManager.getInstance()
                .setMap(commonParams)
                .setSocketHost(SocketApiConstants.SOCKET_HOST)
                .request(LiveUserForbiddenListener.INSTANCE);
    }

    /**
     * 解除监听 - 用户被禁言，被踢出直播间
     */
    public void unRegisterUserForbiddenListener() {
        Log.d(TAG, "live--- unRegisterUserForbiddenListener()");
        LiveSocketManager.getInstance().unRegisterUserForbiddenListener(mOnUserForbiddenListener);
        SocketManager.getInstance().removeListener(LiveUserForbiddenListener.INSTANCE);
    }

    /**
     * 接受消息监听
     */
    private LiveSocketManager.OnLiveRecvMsgListener mOnLiveRecvMsgListener = new LiveSocketManager.OnLiveRecvMsgListener() {
        @Override
        public void onLiveRecvMsg(LiveChatRoomMessageNew liveChatRoomMessageNew) {
            Log.e(TAG, "live--- mOnLiveRecvMsgListener onLiveRecvMsg liveChatRoomMessageNew=" + liveChatRoomMessageNew);
            if (liveChatRoomMessageNew == null) {
                return;
            }
            String msgInfo = new Gson().toJson(liveChatRoomMessageNew.getMsgInfo());
            Log.e(TAG, "live--- mOnLiveRecvMsgListener onLiveRecvMsg msgInfo=" + msgInfo);
            if (TextUtils.isEmpty(msgInfo)) {
                return;
            }
            String msgType = liveChatRoomMessageNew.getType();
            Log.e(TAG, "live--- mOnLiveRecvMsgListener onLiveRecvMsg msgType=" + msgType);
            switch (msgType) {
                case LiveChatRoomMessageNew.MSG_TYPE_TEXT:// 文本消息
                case LiveChatRoomMessageNew.MSG_TYPE_AUDIO:// 语音消息
                    // 目前只能发送语音，发送的语音最终会被转成文字推送给客户端，所以发送的语音和文本消息都走这里
                    LiveTextMsg liveTextMsg = new Gson().fromJson(msgInfo, LiveTextMsg.class);
                    Log.e(TAG, "live--- mOnLiveRecvMsgListener transTo liveTextMsg=" + liveTextMsg);
                    MessageBean data = LiveBeanTransUtils.createMessageBeanFromSocketTextMsg(liveTextMsg);
                    updateMsgListData(data);
                    break;

                case LiveChatRoomMessageNew.MSG_TYPE_NOTIFICATION:
                    // 系统通知 - 其他用户进入退出聊天室通知-subType : 1-进入聊天室通知。2-离开聊天室通知
                    LiveOtherUserEnterExitMsg otherUserEnterExitMsg = new Gson().fromJson(msgInfo, LiveOtherUserEnterExitMsg.class);
                    Log.e(TAG, "live--- mOnLiveRecvMsgListener transTo otherUserEnterExitMsg=" + otherUserEnterExitMsg);
                    manageRoomUserChanged(otherUserEnterExitMsg);
                    break;

                case LiveChatRoomMessageNew.MSG_TYPE_LIVE_RANK:
                    // 排行榜更新消息
                    // 不过目前只使用了 GiftRankMsg#GiftRankUser#avatar, 此字段没有修改
                    GiftRankMsg giftRankMsg = new Gson().fromJson(msgInfo, GiftRankMsg.class);
                    Log.e(TAG, "live--- mOnLiveRecvMsgListener transTo giftRankMsg=" + giftRankMsg);
                    EventBus.getDefault().post(giftRankMsg);
                    break;

                case LiveChatRoomMessageNew.MSG_TYPE_LIVE_GIFT:
                    // 打赏消息
                    GiftMsg giftMsg = new Gson().fromJson(msgInfo, GiftMsg.class);
                    Log.e(TAG, "live--- mOnLiveRecvMsgListener transTo giftMsg=" + giftMsg);
                    EventBus.getDefault().post(giftMsg);
                    break;

                case LiveChatRoomMessageNew.MSG_TYPE_LIVE_GOODS_CARD:
                    // 讲解中商品消息
                    // GoodsCardMsg字段没有做修改
                    GoodsCardMsg shopCardMsg = new Gson().fromJson(msgInfo, GoodsCardMsg.class);
                    Log.e(TAG, "live--- mOnLiveRecvMsgListener transTo shopCardMsg=" + shopCardMsg);
                    EventBus.getDefault().post(shopCardMsg);
                    break;

                case LiveChatRoomMessageNew.MSG_TYPE_LIVE_LIVE_STATUS_CHANGED:
                    // 直播间状态变更 - Integer liveStatus 直播间状态 1-开播，2-下播
                    LiveStatusMsg liveStatusMsg = new Gson().fromJson(msgInfo, LiveStatusMsg.class);
                    Log.e(TAG, "live--- mOnLiveRecvMsgListener transTo liveStatusMsg=" + liveStatusMsg);
                    if (liveStatusMsg != null) {
                        Log.e(TAG, "live--- mOnLiveRecvMsgListener transTo liveStatusMsg.getLiveStatus()=" + liveStatusMsg.getLiveStatus());
                        EventBus.getDefault().post(liveStatusMsg);
                    }
                    break;

                case LiveChatRoomMessageNew.MSG_TYPE_LIVE_COMMENT_STATUS_CHANGED:
                    // 评论消息状态 - Integer status - 评论状态：1-通过，2-暂停
                    LiveCommentStatusMsg liveCommentStatusMsg = new Gson().fromJson(msgInfo, LiveCommentStatusMsg.class);
                    Log.e(TAG, "live--- mOnLiveRecvMsgListener transTo liveCommentStatusMsg=" + liveCommentStatusMsg);
                    if (liveCommentStatusMsg != null && liveCommentStatusMsg.getStatus() == LiveCommentStatusMsg.STATUS_REMOVE) {
                        EventBus.getDefault().post(liveCommentStatusMsg);
                    }
                    break;

                default:
                    break;
            }
        }
    };

    private LiveSocketManager.OnUserForbiddenListener mOnUserForbiddenListener = new LiveSocketManager.OnUserForbiddenListener() {
        @Override
        public void onUserForbidden(LiveStatusCode liveStatusCode) {
            Log.e(TAG, "live--- mOnUserForbiddenListener onUserForbidden liveStatusCode=" + liveStatusCode);
            // 监听直播间 用户被禁言，被踢出直播间事件
            if (liveStatusCode == null) {
                return;
            }
            if (!TextUtils.isEmpty(mRoomId)) {
                String reason;
                switch (liveStatusCode.getType()) {
                    case "1":
                        reason = "您已被禁言";
                        break;
                    case "2":
                        reason = "您已被踢出直播间";
                        break;
                    default:
                        reason = "出错未知情况";
                        break;
                }
                ToastUtil.showError(sContext, reason);
            }
            if (!TextUtils.equals("1", liveStatusCode.getType())) {
                managerUserKickOut();
            }
        }
    };

    private void manageRoomUserChanged(LiveOtherUserEnterExitMsg otherUserEnterExitMsg) {
        // 系统通知 - 其他用户进入退出聊天室通知-subType : 1-进入聊天室通知。2-离开聊天室通知
        if (otherUserEnterExitMsg != null) {
            Integer subType = otherUserEnterExitMsg.getSubType();
            // todo 监听用户进入退出
            if (subType == 1) {
                Log.d(TAG, "live--- MSG_TYPE_NOTIFICATION onChatRoomUserEnter otherUserEnterExitMsg=" + otherUserEnterExitMsg);
                // 监听直播间有用户进入
                for (int i = 0, size = mRoomMemberChangedObservers.size(); i < size; i++) {
                    RoomMemberChangedObserver roomMemberChangedObserver = mRoomMemberChangedObservers.get(i);
                    if (roomMemberChangedObserver == null) {
                        continue;
                    }
                    roomMemberChangedObserver.onRoomMemberIn(LiveBeanTransUtils.chatRoomMember2ChatUserInfo(otherUserEnterExitMsg));
                }
            } else if (subType == 2) {
                Log.d(TAG, "live--- MSG_TYPE_NOTIFICATION onChatRoomUserExit otherUserEnterExitMsg=" + otherUserEnterExitMsg);
                // 监听直播间有用户退出
                for (int i = 0, size = mRoomMemberChangedObservers.size(); i < size; i++) {
                    RoomMemberChangedObserver roomMemberChangedObserver = mRoomMemberChangedObservers.get(i);
                    if (roomMemberChangedObserver == null) {
                        continue;
                    }
                    roomMemberChangedObserver.onRoomMemberExit(LiveBeanTransUtils.chatRoomMember2ChatUserInfo(otherUserEnterExitMsg));
                }
            }
        }
    }
}
