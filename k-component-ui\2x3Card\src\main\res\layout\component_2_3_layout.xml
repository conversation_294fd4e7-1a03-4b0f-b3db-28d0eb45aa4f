<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:background="#a0a0">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="2"
        android:orientation="horizontal">

        <include
            android:id="@+id/layout_2_3_top_lift"
            layout="@layout/component_mini_card_layout"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <include
            android:id="@+id/layout_2_3_top_right"
            layout="@layout/component_mini_card_layout"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/m30"
            android:layout_weight="1" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_below="@+id/layout_2_3_top_lift"
        android:layout_marginTop="@dimen/m30"
        android:layout_weight="3"
        android:orientation="horizontal">

        <include
            android:id="@+id/layout_2_3_bottom_1"
            layout="@layout/component_big_card_2_3_layout"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <include
            android:id="@+id/layout_2_3_bottom_2"
            layout="@layout/component_big_card_2_3_layout"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/m30"
            android:layout_weight="1" />

        <include
            android:id="@+id/layout_2_3_bottom_3"
            layout="@layout/component_big_card_2_3_layout"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/m30"
            android:layout_weight="1" />
    </LinearLayout>

</LinearLayout>