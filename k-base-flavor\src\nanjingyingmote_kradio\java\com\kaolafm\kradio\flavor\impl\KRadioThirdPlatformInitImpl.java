package com.kaolafm.kradio.flavor.impl;

import com.kaolafm.kradio.lib.base.flavor.KRadioThirdPlatformInitInter;


/**
 * <AUTHOR>
 **/
public class KRadioThirdPlatformInitImpl implements KRadioThirdPlatformInitInter {

    @Override
    public boolean initThirdPlatform(Object... args) {
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001391696816?userId=1229522问题
        AudioStatusManager.getInstance().injectKLNotificationListener("com.kaolafm.kradio.flavor.impl.KLNotificationImpl");
        return true;
    }

    @Override
    public boolean destroyThirdPlatform(Object... args) {
        return false;
    }
}
