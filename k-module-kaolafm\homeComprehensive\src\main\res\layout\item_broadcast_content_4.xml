<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/y114"
    android:layout_marginBottom="@dimen/y24"
    android:layout_marginLeft="@dimen/x28"
    android:background="@drawable/bg_broadcast_list_item">

    <TextView
        android:id="@+id/content_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/m30"
        android:ellipsize="end"
        android:gravity="center"
        android:lines="1"
        android:textColor="#EEEEEE"
        android:textSize="@dimen/m46"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="唐山台" />

</androidx.constraintlayout.widget.ConstraintLayout>