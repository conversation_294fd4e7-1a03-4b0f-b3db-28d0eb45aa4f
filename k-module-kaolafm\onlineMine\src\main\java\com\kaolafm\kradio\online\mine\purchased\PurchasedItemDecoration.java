package com.kaolafm.kradio.online.mine.purchased;

import android.graphics.Canvas;
import android.graphics.Rect;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;
import android.util.Log;
import android.view.View;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.ResUtil;

/**
 * <AUTHOR>
 * @date 2022-08-04
 */
public class PurchasedItemDecoration extends RecyclerView.ItemDecoration {

    /**
     * 设置ItemView的内嵌偏移长度（inset）
     * 其实RecyclerView 中的 ItemView 外面会包裹着一个矩形（outRect）
     * 内嵌偏移长度 是指：该矩形（outRect）与 ItemView的间隔
     * 内嵌偏移长度分为4个方向：上、下、左、右，并由outRect 中的 top、left、right、bottom参数 控制
     * outRect4个属性值影响着ItemView的Padding值
     * 具体过程：在RecyclerView进行子View宽高测量时（measureChild（）），会将getItemOffsets（）里设置的 outRect4个属性值（Top、Bottom、Left、Right）通过insert值累加 ，并最终添加到子View的 Padding属性中
     *
     * @param outRect
     * @param view
     * @param parent
     * @param state
     */
    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
//        final int spanCount = getSpanCount(parent);
//        final int childCount = parent.getAdapter().getItemCount();
//        final int adapterPosition = parent.getChildAdapterPosition(view);
//        if (isFirst(adapterPosition, spanCount, childCount)) {
//            outRect.left = ResUtil.getDimen(R.dimen.online_history_padding_left);
//        } else {
//            outRect.left = 0;
//        }
//        outRect.top = ResUtil.getDimen(R.dimen.m35);
        outRect.right = ResUtil.getDimen(R.dimen.m43);
//        outRect.left = 0;
//        outRect.bottom = 0;
    }

    /**
     * 通过 Canvas 对象绘制内容
     *
     * Itemdecoration的onDraw()绘制会先于ItemView的onDraw()绘制，所以如果在Itemdecoration的onDraw()中绘制的内容在ItemView边界内，就会被ItemView遮挡住。
     * 解决方案：配合前面的 getItemOffsets（） 一起使用在outRect矩形 与 ItemView的间隔区域 绘制内容
     * 即：通过getItemOffsets（） 设置与 Item 的间隔区域，从而获得与ItemView不重叠的绘制区域
     *
     * getItemOffsets() 针对是每一个 ItemView的，而 onDraw（） 针对 RecyclerView 本身
     * 解决方案：在 使用onDraw（）绘制时，需要先遍历RecyclerView 的所有ItemView分别获取它们的位置信息，然后再绘制内容
     *
     * @param c
     * @param parent
     * @param state
     */
    @Override
    public void onDraw(Canvas c, RecyclerView parent, RecyclerView.State state) {
        super.onDraw(c, parent, state);
    }

    /**
     * 与onDraw（）类似，都是绘制内容
     * 但与onDraw（）的区别是：Itemdecoration的onDrawOver（）绘制 是后于 ItemView的onDraw()绘制
     * 即不需要考虑绘制内容被ItemView遮挡的问题，反而 ItemView会被onDrawOver（）绘制的内容遮挡
     * 绘制时机比较： Itemdecoration.onDraw（）> ItemView.onDraw() > Itemdecoration.onDrawOver（）
     *
     * @param c
     * @param parent
     * @param state
     */
    @Override
    public void onDrawOver(Canvas c, RecyclerView parent, RecyclerView.State state) {
        super.onDrawOver(c, parent, state);
    }

    private boolean isFirst(int position, int spanCount,int childCount) {
        Log.e("isFirst", "position is "+position);
        Log.e("isFirst", "spanCount is "+spanCount);
        Log.e("isFirst", "childCount is "+childCount);
        return position < spanCount;
    }

    private int getSpanCount(RecyclerView parent) {
        RecyclerView.LayoutManager layoutManager = parent.getLayoutManager();

        if (layoutManager instanceof GridLayoutManager) {
            return ((GridLayoutManager) layoutManager).getSpanCount();
        } else if (layoutManager instanceof StaggeredGridLayoutManager) {
            return ((StaggeredGridLayoutManager) layoutManager).getSpanCount();
        } else {
            throw new UnsupportedOperationException("the GridDividerItemDecoration can only be used in " +
                    "the RecyclerView which use a GridLayoutManager or StaggeredGridLayoutManager");
        }
    }
}
