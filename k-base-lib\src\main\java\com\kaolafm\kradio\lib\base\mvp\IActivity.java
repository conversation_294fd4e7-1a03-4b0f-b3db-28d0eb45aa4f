package com.kaolafm.kradio.lib.base.mvp;

import android.os.Bundle;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import org.greenrobot.eventbus.EventBus;

/**
 * Activity接口，只是为了规范方法。
 * <AUTHOR>
 * @date 2018/4/13
 */

public interface IActivity {
    /**
     * 是否使用 {@link EventBus}，默认false。<br/>
     * {@link EventBus}在生命周期中注册或注销
     * @return
     */
    boolean useEventBus();

    /**
     * 初始化 View,如果initView返回0,则不会调用{@link FragmentActivity#setContentView(int)}
     *
     * @return
     */
    int getLayoutId();

    int getLayoutId_Tow();

    /**
     * view相关操作
     *
     * @param savedInstanceState
     */
    void initView(Bundle savedInstanceState);

    /**
     * 初始化必要数据
     */
    void initData();

    /**
     * 这个Activity是否会使用Fragment根据这个属性判断是否注册{@link FragmentManager.FragmentLifecycleCallbacks}
     * 如果返回false,那意味着这个Activity不需要绑定Fragment,那你再在这个Activity中绑定继承于 {@link BaseFragment} 的Fragment将不起任何作用
     * 默认true
     * @return
     */
    boolean useFragment();

    /**
     * 显示加载
     */
    void showLoading();

    /**
     * 隐藏加载
     */
    void hideLoading();
}
