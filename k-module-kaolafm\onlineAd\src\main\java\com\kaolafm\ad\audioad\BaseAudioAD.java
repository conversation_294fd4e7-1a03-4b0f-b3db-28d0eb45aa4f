package com.kaolafm.ad.audioad;

import android.util.Log;

import com.kaolafm.ad.api.model.AudioAdvert;
import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.ad.expose.AdvertisingPlayer;
import com.kaolafm.ad.KradioAdAudioManager;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.TempTaskPlayItem;

/**
 * @ClassName BaseAudioAD
 * @Description 音频广告基类
 * <AUTHOR>
 * @Date 2020-03-11 17:22
 * @Version 1.0
 */
public abstract class BaseAudioAD implements AdvertisingPlayer, IADPlayEndCallback {
    private static final int MILLISECONDS = 1000;

    public final String TAG = "AudioAD" + getClass().getName();
    protected AudioAdvert mAudioAdvert;


    @Override
    public void play(AudioAdvert audioAdvert) {
        if (audioAdvert == null) {
            Log.i(TAG, "收到广告曝光, 数据为空");
            audioAdvertNull();
            return;
        }
        Log.i(TAG, "收到广告曝光:");
        mAudioAdvert = audioAdvert;
        showAudioAdView();
        onGetData();
    }

    @Override
    public void stop(AudioAdvert audioAdvert) {

    }

    @Override
    public void pause(AudioAdvert audioAdvert) {

    }

    @Override
    public void error(String adZoneId, int subtype, ApiException e) {
        onError();
    }

    /**
     * 音频广告是空
     */
    protected void audioAdvertNull() {

    }

    protected TempTaskPlayItem makeAudioAdPlayItem(String url, int type) {
        TempTaskPlayItem tempTaskPlayItem = new TempTaskPlayItem();
        tempTaskPlayItem.setPlayUrl(url);
        tempTaskPlayItem.setTempTaskType(type);
        tempTaskPlayItem.setNeedPlayStateCallBack(true);
        return tempTaskPlayItem;
    }


    protected void startPlay(TempTaskPlayItem adItem) {
        PlayerManager.getInstance().startTempTask(adItem);
    }

    /**
     * 显示音频广告view
     */
    public void showAudioAdView() {
        if (mAudioAdvert.isJump() && mAudioAdvert.getJumpSeconds() != 0) {
            KradioAdAudioManager.getInstance().showAudioAdView(mAudioAdvert.getJumpSeconds() * MILLISECONDS);
        }
    }

    /**
     * 播放音频广告结束
     */
    protected void playAudioAdEnd() {
        Log.i(TAG, "播放结束");
        if (mAudioAdvert != null) {
            Log.i(TAG, "播放结束, 二次互动");
            AdvertisingManager.getInstance().expose(mAudioAdvert);
        }
    }

    /**
     * 发生错误
     */
    public abstract void onError();

    /**
     * 获取到数据
     */
    public abstract void onGetData();


}
