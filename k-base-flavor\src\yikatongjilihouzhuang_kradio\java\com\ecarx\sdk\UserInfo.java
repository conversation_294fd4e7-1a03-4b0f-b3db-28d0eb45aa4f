package com.ecarx.sdk;

import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 **/
public class UserInfo {

    /**
     * openId : 2342326643
     * unionId : 2213123131323131
     * mobile : 155455662245
     * userIco : http://www.ecarx.com
     * nickName : 张小三
     * userCity : 杭州
     * gender : 男
     */

    @SerializedName("openId")
    private String openId;
    @SerializedName("unionId")
    private String unionId;
    @SerializedName("mobile")
    private String mobile;
    @SerializedName("userIco")
    private String userIco;
    @SerializedName("nickName")
    private String nickName;
    @SerializedName("userCity")
    private String userCity;
    @SerializedName("gender")
    private String gender;

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getUserIco() {
        return userIco;
    }

    public void setUserIco(String userIco) {
        this.userIco = userIco;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getUserCity() {
        return userCity;
    }

    public void setUserCity(String userCity) {
        this.userCity = userCity;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }
}
