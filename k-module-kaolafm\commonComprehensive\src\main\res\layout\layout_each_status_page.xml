<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:id="@+id/each_status_page_main_layout"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/tv_status_page_network_error"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="@string/content_desc_refresh_no_network"
        android:drawableTop="@drawable/ic_network_error"
        android:drawablePadding="@dimen/y50"
        android:text="@string/network_nosigin"
        android:textColor="@color/text_color_7"
        android:textSize="@dimen/text_size4"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintVertical_bias="0.35"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="UnusedAttribute" />
</androidx.constraintlayout.widget.ConstraintLayout>
