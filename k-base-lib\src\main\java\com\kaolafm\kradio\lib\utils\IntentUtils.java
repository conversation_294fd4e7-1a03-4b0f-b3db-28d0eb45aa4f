package com.kaolafm.kradio.lib.utils;

import android.app.Activity;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.util.Log;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.report.event.StartReportEvent;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-04-02 20:37
 ******************************************/
public final class IntentUtils {
    private static final String TAG = "IntentUtils";
    /**
     * 启动参数
     */
    public static final String START_TYPE = "start_type";

    public static final String AUTO_PLAY_EXTRA = "auto_play";

    public IntentUtils() {

    }

    private static IntentUtils mIntentUtils;

    static public IntentUtils getInstance() {
        if (mIntentUtils == null) {
            synchronized (IntentUtils.class) {
                if (mIntentUtils == null)
                    mIntentUtils = new IntentUtils();
            }
        }
        return mIntentUtils;
    }


    /**
     * 回至系统Launcher界面
     *
     * @param activity
     */
    public void startLauncher(Activity activity) {
        Intent intent = new Intent();
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.setAction(Intent.ACTION_MAIN);
        intent.addCategory(Intent.CATEGORY_HOME);
        activity.startActivity(intent);
    }

    /**
     * 监测APP是否在前台运行
     *
     * @return true为是，false为否
     */
    public boolean isAppOnForeground() {
        return AppDelegate.getInstance().isAppForeground();
    }

    /**
     * 获取launcherIntent用于widget
     *
     * @param context
     * @return
     */
    public Intent getLauncherIntentUseWidget(Context context) {
        if (context == null) {
            return null;
        }
        Intent launcherIntent = context.getPackageManager().getLaunchIntentForPackage(context.getPackageName());
        if (launcherIntent == null) {
            return null;
        }
        launcherIntent.putExtra(START_TYPE, StartReportEvent.TYPE_WIDGET);
        return launcherIntent;
    }

    /**
     * 通知语音搜索服务APP打开通知
     */
    public void notifyAppOpen(Context context) {
        Log.i(TAG, "notifyAppOpen------------>");
        Intent intent = new Intent();
        intent.setAction("com.kaolafm.auto.home.appOpen.action");
        context.sendBroadcast(intent);
    }

    /**
     * 通知语音搜索服务APP回前台
     */
    public void notifyAppOnResume(Context context) {
        Log.i(TAG, "notifyAppOnResume------------>");
        Intent intent = new Intent();
        intent.setAction("com.kaolafm.client.ACTION_ONRESUME");
        context.sendBroadcast(intent);
    }

    /**
     * 通知语音搜索服务APP退后台
     */
    public void notifyAppOnPause(Context context) {
        Log.i(TAG, "notifyAppOnPause------------>");
        Intent intent = new Intent();
        intent.setAction("com.kaolafm.client.ACTION_ONPAUSE");
        context.sendBroadcast(intent);
    }

    /**
     * 启动服务
     *
     * @param context
     * @param intent
     * @return true 启动服务成功， false 启动服务失败
     */
    public boolean startService(Context context, Intent intent) {
        try {
            if (Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                context.startForegroundService(intent);
            } else {
                context.startService(intent);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * @param service
     */
    public void startForeground(Service service) {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            String CHANNEL_ID = "kl_kradio";
            Context context = service.getApplicationContext();
            NotificationChannel channel = new NotificationChannel(CHANNEL_ID, "kl_kradio_", NotificationManager.IMPORTANCE_DEFAULT);
            NotificationManager manager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
            manager.createNotificationChannel(channel);
            Notification notification = new Notification.Builder(context, CHANNEL_ID).build();
            service.startForeground(100, notification);
        }
    }

    /**
     * 针对语音外调SDK launchApp函数传入true参数统一处理
     *
     * @param intent
     * @return
     */
    public boolean isAutoPlay(Intent intent) {
        boolean flag = false;
        if (intent == null) {
            return flag;
        }
        flag = intent.getBooleanExtra(AUTO_PLAY_EXTRA, false);
        // 用完后将AUTO_PLAY_EXTRA置为false，以免在Activity其他生命周期中使用出现问题
        intent.putExtra(AUTO_PLAY_EXTRA, false);
        return flag;
    }

    /**
     * @param service
     */
    public void startForeground(int id, Service service) {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            String CHANNEL_ID = "kl_kradio";
            Context context = service.getApplicationContext();
            NotificationChannel channel = new NotificationChannel(CHANNEL_ID, "kl_kradio_", NotificationManager.IMPORTANCE_DEFAULT);
            NotificationManager manager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
            manager.createNotificationChannel(channel);
            Notification notification = new Notification.Builder(context, CHANNEL_ID).build();
            service.startForeground(id, notification);
            Log.w(TAG,"startForeground:" + service.getPackageName() + " Name:" + service.getClass().getSimpleName());
        }
    }
}
