package com.kaolafm.kradio.lib.utils;

/**
 * Created by kaolafm on 2018/5/9.
 */

public class CallTimerManager {

    CallTimer mCallTimer;

    public CallTimerManager(){

    }

    public CallTimerManager(TimeCallBack callback){
        mCallTimer = new CallTimer(new Runnable() {
            @Override
            public void run() {
                callback.update();
            }
        });
    }

    public void setUpdateAtOnce(boolean updateAtOnce) {
        mCallTimer.setUpdateAtOnce(updateAtOnce);
    }

    public boolean start(long interval){
        return mCallTimer.start(interval);
    }

    public void cancel() {
        mCallTimer.cancel();
    }

    public interface TimeCallBack{
        void update();
    }
}
