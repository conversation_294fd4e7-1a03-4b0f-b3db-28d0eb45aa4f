<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:kaola="http://schemas.android.com/apk/res-auto"
    android:id="@+id/live_root_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_home">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/live_image_top_guideline"
        style="@style/secondary_page_top_guideline_port" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/live_image_second_guideline"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.2557" />

    <ImageView
        android:id="@+id/live_image"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:scaleType="center"
        android:src="@drawable/media_default_pic"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/live_image_third_guideline"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.65" />

    <ImageView
        android:id="@+id/live_finish_image"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:scaleType="fitXY"
        android:src="@drawable/live_finish_cover"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/live_image"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/live_top_gradient"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/live_image_top_gradient"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.30"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/live_image" />

    <ImageView
        android:id="@+id/live_bottom_gradient"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/live_image_bottom_gradient"
        app:layout_constraintBottom_toBottomOf="@id/live_image"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.30"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <ImageView
        android:id="@+id/live_minimum"
        style="@style/Comprehensive_FragmentBackButton_white" />

    <ImageView
        android:id="@+id/live_stop"
        android:layout_width="@dimen/m80"
        android:layout_height="@dimen/m80"
        android:layout_marginEnd="@dimen/x30"
        android:background="@drawable/color_main_button_click_selector"
        android:padding="@dimen/m22"
        android:scaleType="centerInside"
        android:src="@drawable/live_close_exit"
        android:visibility="gone"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.6" />

    <TextView
        android:id="@+id/live_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:maxLines="1"
        android:textColor="#FFFFFFFF"
        android:textSize="@dimen/text_size7"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_default="percent"
        app:layout_constraintWidth_percent="0.55" />

    <TextView
        android:id="@+id/live_player"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/living_player_hostess_color"
        android:textSize="@dimen/home_live_login_prompt_text_size"
        app:layout_constraintBottom_toBottomOf="@id/live_image_second_guideline"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/live_player_vertical_line"
        app:layout_constraintTop_toBottomOf="@id/live_name"
        app:layout_constraintVertical_bias="0.1" />

    <ImageView
        android:id="@+id/live_player_vertical_line"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:scaleType="center"
        android:src="@drawable/live_player_vertical_line"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/live_player"
        app:layout_constraintLeft_toRightOf="@id/live_player"
        app:layout_constraintRight_toLeftOf="@id/live_listening_number"
        app:layout_constraintTop_toTopOf="@id/live_player"
        app:layout_constraintWidth_default="percent"
        app:layout_constraintWidth_percent="0.04" />

    <TextView
        android:id="@+id/live_listening_number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/living_player_hostess_color"
        android:textSize="@dimen/m18"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/live_player"
        app:layout_constraintLeft_toRightOf="@id/live_player_vertical_line"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/live_player" />

    <FrameLayout
        android:id="@+id/live_screen_bullet_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@id/live_image_third_guideline"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/live_image" />

    <!--试听-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/live_listen_button_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="W,1:1"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.0816"
        app:layout_constraintHorizontal_bias="0.8572"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/live_leave_a_message"
        app:layout_constraintTop_toBottomOf="@id/live_image_third_guideline">

        <ImageView
            android:id="@+id/live_listen_message_image"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/live_cancel_message_selector"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.kaolafm.kradio.common.widget.PlayingIndicator
            android:id="@+id/live_listen_anim_image"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone"
            app:bar_color="@color/play_indicator_color"
            app:bar_num="5"
            app:duration="3000"
            app:is_two_way="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="W,1:1"
            app:layout_constraintHeight_default="percent"
            app:layout_constraintHeight_percent="0.5"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:min_height="@dimen/y10"
            app:step_num="10" />

        <TextView
            android:id="@+id/live_listen_message_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/voice_message_audition"
            android:textColor="#FFFFFFFF"
            android:textSize="@dimen/m18"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!--取消-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/live_cancel_button_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="W,1:1"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.0816"
        app:layout_constraintHorizontal_bias="0.1428"
        app:layout_constraintLeft_toRightOf="@id/live_leave_a_message"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/live_image_third_guideline">

        <ImageView
            android:id="@+id/live_cancel_message_image"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/live_cancel_message_selector"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/live_cancel_message_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/voice_message_cancel"
            android:textColor="#FFFFFFFF"
            android:textSize="@dimen/m18"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!--录音-->
    <ImageView
        android:id="@+id/live_record_anim_image"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:src="@drawable/live_record_sound_update"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="W,1:1"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.14"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/live_image_third_guideline" />


    <com.kaolafm.kradio.common.widget.CircleProgressImageView
        android:id="@+id/live_leave_a_message"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:src="@drawable/live_leave_a_message"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="W,1:1"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="@string/live_recorder_button_ratio"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/live_image_third_guideline"
        kaola:back_color="@color/play_live_progress_bg"
        kaola:progress_color="@color/play_live_progress"
        kaola:progress_width="@dimen/m4" />

    <TextView
        android:id="@+id/live_login_prompt_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#FFFFFF"
        android:textSize="@dimen/home_live_login_prompt_text_size"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/live_leave_a_message"
        app:layout_constraintVertical_bias="0.25" />


    <TextView
        android:id="@+id/live_countdown_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#FFFFFFFF"
        android:textSize="@dimen/m18"
        app:layout_constraintBottom_toBottomOf="@id/live_leave_a_message"
        app:layout_constraintLeft_toLeftOf="@id/live_leave_a_message"
        app:layout_constraintRight_toRightOf="@id/live_leave_a_message"
        app:layout_constraintTop_toTopOf="@id/live_leave_a_message" />

    <ImageView
        android:id="@+id/live_speak_image"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:src="@drawable/live_leave_message_speak"
        android:textColor="#FFFFFFFF"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/live_leave_a_message"
        app:layout_constraintDimensionRatio="W,1:1"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.0602"
        app:layout_constraintLeft_toLeftOf="@id/live_leave_a_message"
        app:layout_constraintRight_toRightOf="@id/live_leave_a_message"
        app:layout_constraintTop_toTopOf="@id/live_leave_a_message" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/live_not_start_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/live_image_third_guideline">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:text="@string/live_start_interval"
            android:textColor="#FFFFFF"
            android:textSize="@dimen/m28"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.2368" />

        <TextView
            android:id="@+id/live_not_start_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#FFFFFFFF"
            android:textSize="@dimen/m48"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.4736" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/live_finish_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/live_finished"
            android:textColor="#FFFFFFFF"
            android:textSize="@dimen/m28"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/live_error_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/live_image_third_guideline">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/live_load_error"
            android:textColor="#FFFFFFFF"
            android:textSize="@dimen/m28"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.2789" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/live_go_listen_other"
            android:textColor="#66FFFFFF"
            android:textSize="@dimen/m20"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.4842" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/live_coming_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/live_image_third_guideline">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:text="@string/live_coming"
            android:textColor="#FFFFFF"
            android:textSize="@dimen/m28"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.2789" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:text="@string/live_go_listen_other"
            android:textColor="#66FFFFFF"
            android:textSize="@dimen/m20"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.4842" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>

