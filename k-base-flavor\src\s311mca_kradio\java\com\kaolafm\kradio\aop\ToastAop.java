package com.kaolafm.kradio.aop;

import android.app.Activity;
import android.content.Context;
import android.util.Log;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.toast.SuperToast;
import com.kaolafm.kradio.lib.toast.ToastStyle;
import com.kaolafm.kradio.lib.utils.IntentUtils;
import com.kaolafm.kradio.lib.utils.ResUtil;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;


@Aspect
public class ToastAop {

    private static final String TAG = "ToastAop";
    private static SuperToast mToast;

    @Around("execution(* com.kaolafm.kradio.lib.toast.ToastUtil.showOnly(..))")
    public void toastShowOnActivity(ProceedingJoinPoint point) throws Throwable {
        boolean isAppOnForeground = IntentUtils.getInstance().isAppOnForeground();
        try {
            if (isAppOnForeground) {
                Context context = (Context) (point.getArgs()[0]);
                String msg;
                if (point.getArgs()[1] instanceof String) {
                    Log.i(TAG, "toastShowOnActivity: msg  instanceof String ");
                    msg = (String) (point.getArgs()[1]);
                } else {
                    Log.i(TAG, "toastShowOnActivity: msg  instanceof int ");
                    int resId = (int) (point.getArgs()[1]);
                    msg = ResUtil.getString(resId);
                }

                if (context == null || !(context instanceof Activity)) {
                    context = AppManager.getInstance().getMainActivity();
                }
                mToast = new SuperToast(context);
                mToast.cancelAllToasts();
                mToast.setMessage(msg)
                        .setDuration(ToastStyle.DURATION_LONG)
                        .setDisplayLevel(ToastStyle.LEVEL_ACTIVITY)
                        .create()
                        .show();
            }
        } catch (Exception e) {
            point.proceed();
            e.printStackTrace();
        }
    }
}
