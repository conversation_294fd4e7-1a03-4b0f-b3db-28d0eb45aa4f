package com.kaolafm.kradio.online.player.pages;

import androidx.lifecycle.Lifecycle;
import android.content.res.Configuration;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.LayoutAnimationController;
import android.widget.ImageView;
import android.widget.TextView;

import com.google.gson.Gson;
import com.kaolafm.ad.expose.AdvertisingImager;
import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.common.ErrorInfo;
import com.kaolafm.kradio.common.event.PlayerCheckPreOrNextButtonEBData;
import com.kaolafm.kradio.common.report.ReportParamUtil;
import com.kaolafm.kradio.common.report.ReportUtil;
import com.kaolafm.kradio.component.ComponentClient;
import com.kaolafm.kradio.component.DynamicComponent;
import com.kaolafm.kradio.component.MainThreadable;
import com.kaolafm.kradio.component.RealCaller;
import com.kaolafm.kradio.constant.UserComponentConst;
import com.kaolafm.kradio.constant.UserStateObserverProcessorConst;
import com.kaolafm.kradio.k_kaolafm.R; 
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.ComponentUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.RecyclerViewExposeUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.widget.tab.SlidingTabLayout;
import com.kaolafm.kradio.online.common.base.MBaseShowHideFragment;
import com.kaolafm.kradio.online.common.event.ScrollToPlayingPlayItemPositionEvent;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.kradio.online.player.adapters.AlbumPlayListRvAdapter;
import com.kaolafm.kradio.online.player.adapters.OnlinePlayItemDiffCallback;
import com.kaolafm.kradio.online.player.base.OnlineBasePlayListRvAdapter;
import com.kaolafm.kradio.online.player.mvp.OnlinePlayerBasePresenter;
import com.kaolafm.kradio.online.player.mvp.OnlineRadioPlayerPresenter;
import com.kaolafm.kradio.online.player.mvp.OnlineRadioPlayerView;
import com.kaolafm.kradio.online.player.mvp.OnlineSubscribeCallback;
import com.kaolafm.kradio.online.player.utils.CenterLayoutManager;
import com.kaolafm.kradio.online.player.utils.PlayerFragmentHelper;
import com.kaolafm.kradio.online.player.utils.TopLinearSmoothScroller;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.player.helper.intercept.HintInterceptManager;
import com.kaolafm.kradio.player.utils.AudioSubscribeCacheUtil;
import com.kaolafm.kradio.purchase.PayManager;
import com.kaolafm.kradio.purchase.constant.PayConst;
import com.kaolafm.kradio.purchase.model.PayResult;
import com.kaolafm.kradio.purchase.observer.AlbumPayListener;
import com.kaolafm.kradio.purchase.observer.VipPayListener;
import com.kaolafm.kradio.user.UserInfoManager;
import com.kaolafm.opensdk.api.media.model.AlbumDetails;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.db.manager.RadioSortTypeDaoManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayListControl;
import com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayListStateListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayerStateListener;
import com.kaolafm.opensdk.player.logic.model.PlaylistInfo;
import com.kaolafm.opensdk.player.logic.model.item.AlbumPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.RadioPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.playlist.AlbumPlayListControl;
import com.kaolafm.opensdk.player.logic.playlist.RadioPlayListControl;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonClickReportEvent;
import com.kaolafm.report.event.LoginReportEvent;
import com.kaolafm.report.event.PlayerUiControlReportEvent; 

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
 
import kotlin.Unit;
import kotlin.jvm.functions.Function1;

import static android.view.View.GONE;
import static android.view.View.VISIBLE;

public class OnlineAlbumPlayListRebuildFragment extends MBaseShowHideFragment<OnlinePlayerBasePresenter> implements OnlineRadioPlayerView, RecyclerViewExposeUtil.OnItemExposeListener {
    protected String TAG = OnlineAlbumPlayListRebuildFragment.class.getSimpleName();

    //是否普通专辑点击一键置顶时加载第一页数据
    private final boolean LOAD_FIRST_PAGE_WHEN_SCROLL_TO_TOP = false;

    private static final String BREAK_POINT_CONTINUE_TIME = "2";
    private final int DELAY_RENDER_TIME_MS = 200;   //渲染延时，用于滚动完毕后延迟重渲染
    private final int DELAY_SCROLL_TIME_MS = 500;   //渲染延时，用于列表显示后延迟滚动到播放项、滚动完毕后延迟重渲染

    private static final int LOAD_PLAY_LIST_WITH_CURRENT_PLAYITEM = 2899;    //加载包含当前播放项的一页播单
    private static final int LOAD_PLAY_LIST_FIRST_PAGE = 2900;   //加载第一页，适用于具有时效性的专辑一键置顶操作时

    private static final int HANDLER_SCROLL_TO_CURRENT = 3100;  //滚动到当前播放位置
    private static final int HANDLER_RENDER_ITEM = 3101;        //只渲染当前播放项，不滚动
    private static final int HANDLER_SCROLL_AND_RENDER_ITEM = 3102; //滚动到当前播放位置后渲染当前播放项
    private static final int HANDLER_RENDER_ITEM_REAL = 3110;   //开始真正的渲染操作

    public static final int LOAD_DATA_IDLE = 0;
    public static final int LOAD_DATA_SELF = 1;
    public static final int LOAD_DATA_NEXT_PAGE = 2;
    public static final int LOAD_DATA_PREV_PAGE = 3;
    private int mLoadDataFlag = LOAD_DATA_IDLE;
 
    ImageView scrollToTopIv; 
    protected TextView albumTitleTv; 
    protected TextView playItemCountTv; 
    protected TextView albumFlagTv; 
    protected ImageView sortIcon; 
    protected TextView SortTipTv; 
    protected View divider; 
    protected SmartRefreshLayout refreshLayout; 
    protected RecyclerView recyclerView; 
    protected SlidingTabLayout tabLayout;

    private boolean isDestroy = false;

    private DynamicComponent mRadioPlayerUserObserver;
    private PlayerFragmentHelper mPlayerFragmentHelper;

    private int mFirstPageCount;   //第一页有多少条数据
    private boolean isUserBound = false;
    private VipPayListener vipPayListener;
    private AlbumPayListener albumPayListener;

    private PlayItem mPlayItem;
    /**
     * 支付、登录状态发生改变时，用于更新播单的时候
     */
    private final AtomicBoolean isResetDatas = new AtomicBoolean(false);
    /**
     * 正倒序切换时，用于更新播单
     */
    private final AtomicBoolean isSortTypeChanged = new AtomicBoolean(false);

    private final AtomicBoolean prepareNotFoundPlayItem = new AtomicBoolean(false);

    /**
     * 是否第一次进入页面
     */
    private final AtomicBoolean isFirstEnterPage = new AtomicBoolean(true);

    private OnlineBasePlayListRvAdapter mAdapter;

    private List<PlayItem> originPlayList;//刷新整个PlayList之前的播单列表，只为了计算刷新播单前后播单数据的变化

    protected CenterLayoutManager centerLayoutManager;
    protected LinearLayoutManager linearLayoutManager;
    private final boolean useDefaultLayoutManager = false;

    //是否需要重新设置排序方式
//    private AtomicBoolean needResetSortType = new AtomicBoolean(false);


    private final RecyclerView.OnScrollListener mRenderScrollListener = new RecyclerView.OnScrollListener() {
        @Override
        public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
            super.onScrollStateChanged(recyclerView, newState);
            if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                recyclerView.removeOnScrollListener(this);
                mHandler.sendEmptyMessageDelayed(HANDLER_RENDER_ITEM_REAL, DELAY_RENDER_TIME_MS);
            }
        }
    };

    private final Handler mHandler = new Handler(Looper.getMainLooper(), new Handler.Callback() {
        @Override
        public boolean handleMessage(@NonNull Message msg) {
            if (msg.what == LOAD_PLAY_LIST_WITH_CURRENT_PLAYITEM) {
                PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
                Log.e(TAG, "加载数据" + new Gson().toJson(playItem));
                loadPageData(playItem.getAudioId(), 1);
            } else if (msg.what == LOAD_PLAY_LIST_FIRST_PAGE) {
                Log.e(TAG, "加载数据audioId=0");
                loadPageData(0, 1);
            } else if (msg.what == HANDLER_SCROLL_TO_CURRENT) {
                if (mAdapter == null) return false;
                int newPosition = mAdapter.findSelectPosition();
                if (newPosition == -1) {
                    mHandler.sendEmptyMessage(LOAD_PLAY_LIST_WITH_CURRENT_PLAYITEM);
                    return false;
                }
                //判断目标Item是否已经在RecyclerView中间位置
                if (useDefaultLayoutManager) {
                    View viewByPosition = linearLayoutManager.findViewByPosition(newPosition);
                    if (viewByPosition != null) {
                        final int top = linearLayoutManager.getDecoratedTop(viewByPosition);
                        final int start = linearLayoutManager.getPaddingTop();
                        if (top == start) {
                            return false;
                        }
                    }
                } else {
                    View viewByPosition = centerLayoutManager.findViewByPosition(newPosition);
                    if (viewByPosition != null) {
                        final RecyclerView.LayoutParams params = (RecyclerView.LayoutParams)
                                viewByPosition.getLayoutParams();
                        final int top = centerLayoutManager.getDecoratedTop(viewByPosition) - params.topMargin;
                        final int bottom = centerLayoutManager.getDecoratedBottom(viewByPosition) + params.bottomMargin;
                        final int start = centerLayoutManager.getPaddingTop();
                        final int end = centerLayoutManager.getHeight() - centerLayoutManager.getPaddingBottom();
                        int offset = (start + (end - start) / 2) - (top + (bottom - top) / 2);
                        if (offset == 0) {
                            return false;
                        }
                    }
                }
                //将会开始平滑滑动
                recyclerView.smoothScrollToPosition(newPosition);
            } else if (msg.what == HANDLER_SCROLL_AND_RENDER_ITEM) {
                if (mAdapter == null) return false;
                boolean needReRender = true;
                int newPosition = mAdapter.findSelectPosition();
                if (newPosition == -1) {
                    mAdapter.unSelectItem();
                    return false;
                }
                if (newPosition != mAdapter.getSelectPosition()) {
                    mAdapter.selectItem(newPosition);
                    needReRender = false;
                }

                //判断目标Item是否已经在RecyclerView中间位置
                if (useDefaultLayoutManager) {
                    View viewByPosition = linearLayoutManager.findViewByPosition(newPosition);
                    if (viewByPosition != null) {
                        final int top = linearLayoutManager.getDecoratedTop(viewByPosition);
                        final int start = linearLayoutManager.getPaddingTop();
                        if (top == start) {
                            if (needReRender)
                                mHandler.sendEmptyMessageDelayed(HANDLER_RENDER_ITEM_REAL, DELAY_RENDER_TIME_MS);
                            return false;
                        }
                    }
                } else {
                    View viewByPosition = centerLayoutManager.findViewByPosition(newPosition);
                    if (viewByPosition != null) {
                        final RecyclerView.LayoutParams params = (RecyclerView.LayoutParams)
                                viewByPosition.getLayoutParams();
                        final int top = centerLayoutManager.getDecoratedTop(viewByPosition) - params.topMargin;
                        final int bottom = centerLayoutManager.getDecoratedBottom(viewByPosition) + params.bottomMargin;
                        final int start = centerLayoutManager.getPaddingTop();
                        final int end = centerLayoutManager.getHeight() - centerLayoutManager.getPaddingBottom();
                        int offset = (start + (end - start) / 2) - (top + (bottom - top) / 2);
                        if (offset == 0) {
                            //已经在中间，不需要滚动，直接渲染即可
                            if (needReRender)
                                mHandler.sendEmptyMessageDelayed(HANDLER_RENDER_ITEM_REAL, DELAY_RENDER_TIME_MS);
                            return false;
                        }
                    }
                }
                //添加滚动监听，当停止滚动时，重新渲染播放项
                if (needReRender) {
                    recyclerView.removeOnScrollListener(OnlineAlbumPlayListRebuildFragment.this.mRenderScrollListener);
                    recyclerView.addOnScrollListener(OnlineAlbumPlayListRebuildFragment.this.mRenderScrollListener);
                }
                //将会开始平滑滑动
                recyclerView.smoothScrollToPosition(newPosition);
            } else if (msg.what == HANDLER_RENDER_ITEM) {
                if (mAdapter == null) return false;
                boolean needReRender = true;
                int newPosition = mAdapter.findSelectPosition();
                if (newPosition == -1) {
                    mAdapter.unSelectItem();
                    return false;
                }
                if (newPosition != mAdapter.getSelectPosition()) {
                    mAdapter.selectItem(newPosition);
                    needReRender = false;
                }
                if (needReRender)
                    mHandler.sendEmptyMessageDelayed(HANDLER_RENDER_ITEM_REAL, DELAY_RENDER_TIME_MS);
            } else if (msg.what == HANDLER_RENDER_ITEM_REAL) {
                //重新渲染正在直播的item
                if (mAdapter != null) {
                    mAdapter.reRenderPlayingItem();
                }
            }
            return false;
        }
    });

    private IPlayListStateListener mPlayListControlStateCallback = new IPlayListStateListener() {
        @Override
        public void onPlayListChange(List<PlayItem> playItems) {
            if (isDestroy) return;
            if (PlayerManagerHelper.getInstance().getCurrentPlayType() != PlayerConstants.RESOURCES_TYPE_ALBUM
                    && PlayerManagerHelper.getInstance().getCurrentPlayType() != PlayerConstants.RESOURCES_TYPE_RADIO)
                return;
            updatePlayListInfo();
            if (mAdapter == null) {
                setPlayListAdapter(playItems);
                return;
            }
            Log.e(TAG, "专辑播单已更新------" + playItems.size() + "条------------\n" + new Gson().toJson(playItems));
            updatePlayList(playItems);
        }

        @Override
        public void onPlayListChangeError(PlayItem playItem, int i, int i1) {

        }

    };
    private IPlayListGetListener mNextPageGetListener;
    private IPlayListGetListener mPrevPageGetListener;
    private IPlayerStateListener stateListener = new BasePlayStateListener() {
        @Override
        public void onPlayerPreparing(PlayItem playItem) {
            super.onPlayerPreparing(playItem);
            mPlayItem = playItem;
//            if (needResetSortType.compareAndSet(true, false)) {
//                querySortTypeFromDB();
//            }

            if (isDestroy) return;
            if (mAdapter == null) return;
            //如果是相同的节目，不做处理
            if (playItem != null && mAdapter.getPlayingItem() != null && playItem.getAudioId() == mAdapter.getPlayingItem().getAudioId())
                return;
            int index = -1;
            List<PlayItem> dataList = mAdapter.getDataList();
            for (int position = 0; position < dataList.size(); position++) {
                if (dataList.get(position).getAudioId() == playItem.getAudioId()) {
                    index = position;
                    break;
                }
            }
            if (index != -1) {
                if (mHandler.hasMessages(HANDLER_SCROLL_AND_RENDER_ITEM)) {
                    mHandler.removeMessages(HANDLER_SCROLL_AND_RENDER_ITEM);
                }
                mHandler.sendEmptyMessage(HANDLER_SCROLL_AND_RENDER_ITEM);
            } else {
                //标记没有找到，此时是因为播单还没更新
                prepareNotFoundPlayItem.set(true);
            }
        }

        @Override
        public void onPlayerPlaying(PlayItem playItem) {
            super.onPlayerPlaying(playItem);
            if (isDestroy) return;
            if (mAdapter == null) return;
            mAdapter.notifyAdapterPlayItemResume(playItem);
        }

        @Override
        public void onPlayerEnd(PlayItem playItem) {
            super.onPlayerEnd(playItem);
            onPlayItemEnd(playItem);
        }

        @Override
        public void onPlayerFailed(PlayItem playItem, int what, int extra) {
            super.onPlayerFailed(playItem, what, extra);
            onPlayItemFailed(playItem);
        }

        @Override
        public void onPlayerPaused(PlayItem playItem) {
            super.onPlayerPaused(playItem);
            onPlayItemPause(playItem);
        }
    };
    private RecyclerView.OnScrollListener mScrollTopListener;

    @Override
    protected int getLayoutId() {
        return R.layout.online_player_fragment_boradcast_list;
    }

    @Override
    protected OnlineRadioPlayerPresenter createPresenter() {
        return new OnlineRadioPlayerPresenter(this);
    }

    @Override
    public void initView(View view) {

        scrollToTopIv=view.findViewById(R.id.scrollToTopIv);
        albumTitleTv=view.findViewById(R.id.albumTitleTv);
        playItemCountTv=view.findViewById(R.id.playItemCountTv);
        albumFlagTv=view.findViewById(R.id.albumFlagTv);
        sortIcon=view.findViewById(R.id.sortIcon);
        SortTipTv=view.findViewById(R.id.SortTipTv);
        divider=view.findViewById(R.id.divider);
        refreshLayout=view.findViewById(R.id.refreshLayout);
        recyclerView=view.findViewById(R.id.recyclerView);
        tabLayout=view.findViewById(R.id.tabLayout);
       
        
        mPlayItem = PlayerManager.getInstance().getCurPlayItem();

        mPlayerFragmentHelper = new PlayerFragmentHelper();
        mPlayerFragmentHelper.setPresenter(mPresenter);

        Log.e(TAG, mPlayItem.toString());

        if (useDefaultLayoutManager) {
            TopLinearSmoothScroller scroller = new TopLinearSmoothScroller(getContext());
            linearLayoutManager = new LinearLayoutManager(getContext()) {
                @Override
                public void smoothScrollToPosition(RecyclerView recyclerView, RecyclerView.State state, int position) {
                    super.smoothScrollToPosition(recyclerView, state, position);
                    scroller.setTargetPosition(position);
                    startSmoothScroll(scroller);
                }
            };
            recyclerView.setLayoutManager(linearLayoutManager);
        } else {
            centerLayoutManager = new CenterLayoutManager(getContext());
            recyclerView.setLayoutManager(centerLayoutManager);
        }

        if (mPlayItem.getType() == PlayerConstants.RESOURCES_TYPE_ALBUM) {
            ViewGroup.LayoutParams layoutParams = refreshLayout.getLayoutParams();
            if (layoutParams instanceof ConstraintLayout.LayoutParams) {
                ((ConstraintLayout.LayoutParams) layoutParams).topMargin = ResUtil.getDimen(R.dimen.y170);
                refreshLayout.setLayoutParams(layoutParams);
            }
        }

        refreshLayout.setEnableRefresh(true);
        refreshLayout.setEnableLoadMore(true);
        refreshLayout.setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                loadPrevPage();
            }
        });
        refreshLayout.setOnLoadMoreListener(new OnLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                loadNextPager();
            }
        });

        PlayerManager.getInstance().addPlayControlStateCallback(this.stateListener);

        tabLayout.setVisibility(View.GONE);

        PlayerManager.getInstance().addPlayListControlStateCallback(mPlayListControlStateCallback);

        mRadioPlayerUserObserver = new RadioPlayerUserObserver();
        ComponentUtil.addObserver(UserComponentConst.NAME, mRadioPlayerUserObserver);

        isUserBound = UserInfoManager.getInstance().isUserBound();
        ViewUtil.setViewVisibility(view.findViewById(R.id.tabLayout), View.GONE);
        if (PlayerManagerHelper.getInstance().getCurrentPlayType() == PlayerConstants.RESOURCES_TYPE_ALBUM) {
            IPlayListControl playListControl = PlayerManager.getInstance().getPlayListControl();
            boolean showSort = true;
            //和主站同步，使用是否支持订阅来判断是否支持正倒序。
            if (PlayerManager.getInstance().getPlayListInfo() != null) {
                showSort = PlayerManager.getInstance().getPlayListInfo().getNoSubscribe() != 1;
            }
            //以前的逻辑，只有非即时性专辑才支持正倒序
//            if (playListControl != null) {
//                PlaylistInfo playListInfo = playListControl.getPlayListInfo();
//                if (playListInfo != null) {
//                    if (BREAK_POINT_CONTINUE_TIME.equals(playListInfo.getBreakPointContinue())) {
//                        setItemCountViewGroupVisibilityWithoutSort(View.VISIBLE);
//                        showSort = false;
//                    }
//                }
//            }
            if (showSort)
                setItemCountViewGroupVisibility(View.VISIBLE);
            initScrollToTopView();
            setAlbumViews(showSort);
            //专辑，需要先查询排序方式后请求数据

        } else if (PlayerManagerHelper.getInstance().getCurrentPlayType() == PlayerConstants.RESOURCES_TYPE_RADIO) {
            setItemCountViewGroupVisibilityWithoutTitleView(View.GONE);
            initScrollToTopView();
            IPlayListControl playListControl = PlayerManager.getInstance().getPlayListControl();
            if (playListControl != null) {
                mFirstPageCount = playListControl.getPlayList().size();
                updatePlayListInfo();
                setPlayListAdapter(null);
            }
        }
    }

    @Override
    public boolean useEventBus() {
        return true;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void scrollToPlayingPlayItemPositionEvent(ScrollToPlayingPlayItemPositionEvent event) {
        addScrollEvent(false, false);
    }

    private void initScrollToTopView() {
        mScrollTopListener = new RecyclerView.OnScrollListener() {
            RecyclerView.LayoutManager layoutManager;

            @Override
            public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                Log.d(TAG, "recyclerView滚动装填改变：" + newState);
            }

            @Override
            public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                IPlayListControl playListControl = PlayerManager.getInstance().getPlayListControl();
                if (playListControl == null) return;
                boolean hasPrePage = playListControl != null && playListControl.hasPrePage();
                layoutManager = recyclerView.getLayoutManager();
                if (layoutManager != null && mFirstPageCount != 0 && layoutManager instanceof LinearLayoutManager) {
                    boolean isShow = LOAD_FIRST_PAGE_WHEN_SCROLL_TO_TOP && (playListControl.hasPrePage() || ((LinearLayoutManager) layoutManager).findLastVisibleItemPosition() > mFirstPageCount)
                            || !LOAD_FIRST_PAGE_WHEN_SCROLL_TO_TOP && (((LinearLayoutManager) layoutManager).findLastVisibleItemPosition() > mFirstPageCount);
                    if (isShow) {
                        ViewUtil.setViewVisibility(scrollToTopIv, View.VISIBLE);
                    } else {
                        ViewUtil.setViewVisibility(scrollToTopIv, View.GONE);
                    }
                }
            }
        };

        recyclerView.addOnScrollListener(mScrollTopListener);
        scrollToTopIv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                recyclerView.scrollToPosition(0);
                IPlayListControl playListControl = PlayerManager.getInstance().getPlayListControl();
                if (playListControl == null) return;
                PlaylistInfo playListInfo = playListControl.getPlayListInfo();
                //综合版是只有具有时效的专辑才会加载第一页，现在产品说没有这个限制。。。所以不再判断是否是具有时效的专辑了
                boolean loadFirstPage = playListInfo != null && (LOAD_FIRST_PAGE_WHEN_SCROLL_TO_TOP || BREAK_POINT_CONTINUE_TIME.equals(playListInfo.getBreakPointContinue()));
                if (loadFirstPage) {
                    if (mHandler.hasMessages(LOAD_PLAY_LIST_FIRST_PAGE))
                        mHandler.removeMessages(LOAD_PLAY_LIST_FIRST_PAGE);
                    mHandler.sendEmptyMessage(LOAD_PLAY_LIST_FIRST_PAGE);
                }
                //数据上报
                ButtonClickReportEvent event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_PLAY_TOP);
                ReportHelper.getInstance().addEvent(event);
            }
        });
    }

    /**
     * 初始化专辑相关view
     *
     * @param showSort 是否显示排序，如果是时效性专辑，则为false，否则为true
     */
    private void setAlbumViews(boolean showSort) {
        if (!showSort) {
            if (PlayerManagerHelper.getInstance().getCurrentPlayType() == PlayerConstants.RESOURCES_TYPE_ALBUM)
                mHandler.sendEmptyMessage(LOAD_PLAY_LIST_WITH_CURRENT_PLAYITEM);
            return;
        }
        SortTipTv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                IPlayListControl playListControl = PlayerManager.getInstance().getPlayListControl();
                if (playListControl instanceof AlbumPlayListControl) {
                    if (playListControl.getPlayListInfo().getSort() == PlayerConstants.SORT_ACS) {
                        //正序切换倒序
                        playListControl.getPlayListInfo().setSort(PlayerConstants.SORT_DESC);
                        SortTipTv.setText(getResources().getString(R.string.online_player_sort_negative));
                        sortIcon.setImageResource(R.drawable.online_search_sort_bottom);
                    } else {
                        //倒序切换正序
                        playListControl.getPlayListInfo().setSort(PlayerConstants.SORT_ACS);
                        SortTipTv.setText(getResources().getString(R.string.online_player_sort_positive));
                        sortIcon.setImageResource(R.drawable.online_search_sort_up);
                    }
                    //正倒序需要切换后定位到当前播放项
                    isSortTypeChanged.set(true);
                    if (mHandler.hasMessages(LOAD_PLAY_LIST_WITH_CURRENT_PLAYITEM))
                        mHandler.removeMessages(LOAD_PLAY_LIST_WITH_CURRENT_PLAYITEM);
                    mHandler.sendEmptyMessage(LOAD_PLAY_LIST_WITH_CURRENT_PLAYITEM);

                    RadioSortTypeDaoManager.getInstance().save(PlayerManager.getInstance().getCurPlayItem(), playListControl.getPlayListInfo().getSort());
                    //数据上报
                    ButtonClickReportEvent event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_PLAY_ODER);
                    ReportHelper.getInstance().addEvent(event);
                }
            }
        });
        querySortTypeFromDB();
    }

    /**
     * 查询排序方式，并设置
     */
    private void querySortTypeFromDB() {
        PlayItem curPlayItem = PlayerManager.getInstance().getCurPlayItem();
        if (curPlayItem != null) {
            //需要从数据库中查询该专辑的排序方式
            PlayerManagerHelper.getInstance().getAlbumSortType(curPlayItem.getType(), Long.parseLong(curPlayItem.getAlbumId()), new Function1<Boolean, Unit>() {
                @Override
                public Unit invoke(Boolean aBoolean) {
                    IPlayListControl playListControl = PlayerManager.getInstance().getPlayListControl();
                    if (playListControl == null) {
                        return null;
                    }
                    if (aBoolean == null) {
                        int sortType = playListControl.getPlayListInfo().getSort();
                        //如果数据库里没有该专辑的排序方式或者排序方式有误，则保存
                        RadioSortTypeDaoManager.getInstance().save(PlayerManager.getInstance().getCurPlayItem(), sortType);
                    } else {
                        if (aBoolean) {
                            playListControl.getPlayListInfo().setSort(PlayerConstants.SORT_ACS);
                            SortTipTv.setText(getResources().getString(R.string.online_player_sort_positive));
                            sortIcon.setImageResource(R.drawable.online_search_sort_up);
                        } else {
                            playListControl.getPlayListInfo().setSort(PlayerConstants.SORT_DESC);
                            SortTipTv.setText(getResources().getString(R.string.online_player_sort_negative));
                            sortIcon.setImageResource(R.drawable.online_search_sort_bottom);
                        }
                    }
                    if (PlayerManagerHelper.getInstance().getCurrentPlayType() == PlayerConstants.RESOURCES_TYPE_ALBUM)
                        mHandler.sendEmptyMessage(LOAD_PLAY_LIST_WITH_CURRENT_PLAYITEM);
                    return null;
                }
            });
        }
    }

    private void loadPrevPage() {
        IPlayListControl playListControl = PlayerManager.getInstance().getPlayListControl();
        if ((playListControl instanceof AlbumPlayListControl ||
                playListControl instanceof RadioPlayListControl)
                && playListControl.hasPrePage()) {
            if (this.mPrevPageGetListener == null) {
                this.mPrevPageGetListener = new IPlayListGetListener() {
                    @Override
                    public void onDataGet(PlayItem playItem, List<PlayItem> list) {
                        mFirstPageCount = ListUtil.isEmpty(list) ? 0 : list.size();
                        resetSmartRefreshLayoutNormal(true);
                        updatePlayListInfo();
                    }

                    @Override
                    public void onDataGetError(PlayItem playItem, int i, int i1) {
                        resetSmartRefreshLayoutNormal(false);
                        updatePlayListInfo();
                    }


                };
            }
            playListControl.loadPrePage(this.mPrevPageGetListener);
        }
    }

    private void loadNextPager() {
        IPlayListControl playListControl = PlayerManager.getInstance().getPlayListControl();
        if ((playListControl instanceof AlbumPlayListControl ||
                playListControl instanceof RadioPlayListControl)
                && playListControl.hasNextPage()) {
            if (this.mNextPageGetListener == null) {
                this.mNextPageGetListener = new IPlayListGetListener() {
                    @Override
                    public void onDataGet(PlayItem playItem, List<PlayItem> list) {
                        resetSmartRefreshLayoutNormal(true);
                        updatePlayListInfo();
                    }

                    @Override
                    public void onDataGetError(PlayItem playItem, int i, int i1) {
                        resetSmartRefreshLayoutNormal(false);
                        updatePlayListInfo();
                    }

                };
            }
            playListControl.loadNextPage(this.mNextPageGetListener);
        }
    }


    @Override
    public void showPayInfo(AlbumDetails albumDetails, boolean needBuy) {
        Log.d(TAG, "showPayInfo ,album buy status " + albumDetails.getBuyStatus() + " , album vip "
                + albumDetails.getVip() + " , album fine " + albumDetails.getFine() + " , user vip " + UserInfoManager.getInstance().getVip());
        if (albumDetails.getBuyStatus() == AlbumDetails.BUY_STATUS_NOT_PURCHASE) {
            if (albumDetails.getVip() == 1 && (!UserInfoManager.getInstance().isUserLogin()
                    || UserInfoManager.getInstance().getVip() != 1)) {
                ViewUtil.setViewVisibility(albumFlagTv, VISIBLE);
                albumFlagTv.setText(R.string.online_vip_btn);
                albumFlagTv.setSelected(true);
                albumFlagTv.setOnClickListener(v -> {
                    if (AntiShake.check(v.getId())) {
                        return;
                    }
                    ButtonClickReportEvent event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_PLAY_VIP_LISTEN);
                    ReportHelper.getInstance().addEvent(event);
                    if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(v.getContext())) {
                        return;
                    }
                    isUserBound = UserInfoManager.getInstance().isUserBound();
                    if (!isUserBound) {
                        Bundle bundle = new Bundle();
                        bundle.putString("type", LoginReportEvent.ONLINE_REMARKS1_PLAY_CAROUSEL);
                        RouterManager.getInstance().jumpPage(RouterConstance.ACTIVITY_URL_LOGIN, bundle);
                        return;
                    }
                    buyAlbum(PayConst.PAY_TYPE_VIP);

                });
                PayManager.getInstance().addPayListener(null, vipPayListener = new VipPayListener() {

                    @Override
                    public void payResponse(PayResult payResult, PlayItem playItem, Long vipTime) {
                        Log.d(TAG, "buyAlbum VipPayListener payResponse " + payResult.toString());
                        if (payResult.getPurchaseSucess().getStatus() == 1) {
                            HintInterceptManager.getInstance().notifyPaySuccess(); //通知支付成功
                            ViewUtil.setViewVisibility(albumFlagTv, GONE);
                            UserInfoManager.getInstance().setVip(1);
                            PlayerManagerHelper.getInstance().refreshPlayList();
                            //支付成功，需要刷新全部播单项
                            notifyCheckPlayList();
                            PayManager.getInstance().removeItemSingleListener(mPlayItem, this);
                            vipPayListener = null;
                        }
                    }
                });
                if (needBuy) {
                    buyAlbum(PayConst.PAY_TYPE_VIP);
                }
            } else if (albumDetails.getFine() == 1 && albumDetails.getBuyType() == AlbumDetails.BUY_TYPE_ALBUM) {
                ViewUtil.setViewVisibility(albumFlagTv, VISIBLE);
                albumFlagTv.setText(R.string.online_vip_btn2);
                albumFlagTv.setSelected(false);
                albumFlagTv.setOnClickListener(v -> {
                    if (AntiShake.check(v.getId())) {
                        return;
                    }
                    ButtonClickReportEvent event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_PLAY_PAY);
                    ReportHelper.getInstance().addEvent(event);
                    if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(v.getContext())) {
                        return;
                    }
                    isUserBound = UserInfoManager.getInstance().isUserBound();
                    if (!isUserBound) {
                        Bundle bundle = new Bundle();
                        bundle.putString("type", LoginReportEvent.ONLINE_REMARKS1_PLAY_CAROUSEL);
                        RouterManager.getInstance().jumpPage(RouterConstance.ACTIVITY_URL_LOGIN, bundle);
                        return;
                    }
                    PlayItem mPlayItem = PlayerManager.getInstance().getCurPlayItem();
                    PayManager.getInstance().addPayListener(PlayerManager.getInstance().getCurPlayItem(), albumPayListener = new AlbumPayListener() {

                        @Override
                        public void payResponse(PayResult payResult, PlayItem playItem) {
                            Log.d(TAG, "buyAlbum AlbumPayListener payResponse " + payResult.toString());
                            if (payResult.getPurchaseSucess().getStatus() == 1) {
                                HintInterceptManager.getInstance().notifyPaySuccess(); //通知支付成功
                                PlayerManagerHelper.getInstance().refreshPlayList();
                                //支付成功，需要刷新全部播单项
                                notifyCheckPlayList();
                                ViewUtil.setViewVisibility(albumFlagTv, GONE);
                                PayManager.getInstance().removeItemSingleListener(mPlayItem, this);
                                albumPayListener = null;
                            }
                        }
                    });
                    buyAlbum(PayConst.PAY_TYPE_ALBUM);
                });
                PlayItem mPlayItem = PlayerManager.getInstance().getCurPlayItem();
                PayManager.getInstance().addPayListener(PlayerManager.getInstance().getCurPlayItem(), albumPayListener = new AlbumPayListener() {

                    @Override
                    public void payResponse(PayResult payResult, PlayItem playItem) {
                        Log.d(TAG, "buyAlbum AlbumPayListener payResponse " + payResult.toString());
                        if (payResult.getPurchaseSucess().getStatus() == 1) {
                            HintInterceptManager.getInstance().notifyPaySuccess(); //通知支付成功
                            PlayerManagerHelper.getInstance().refreshPlayList();
                            ViewUtil.setViewVisibility(albumFlagTv, GONE);
                            PayManager.getInstance().removeItemSingleListener(mPlayItem, this);
                            albumPayListener = null;
                        }
                    }
                });
//                HintInterceptManager.getInstance().addOnCurrentPlayItemIntercept(currentPlayItemIntercept = new HintInterceptManager.OnCurrentPlayItemIntercept() {
//                    @Override
//                    public void getHintInterceptState(PlayItem playItem, int hintType, int buyType, int buyStatus, boolean isUserLogin) {
//                        Log.i(TAG, "getHintInterceptState :" + playItem.getBuyStatus());
//                        switch (buyType) {
//                            case TYPE_ALBUM: //专辑购买
//                                PayManager.getInstance().addPayListener(playItem, albumItemPayListener = new AlbumPayListener() {
//
//                                    @Override
//                                    public void payResponse(PayResult payResult, PlayItem playItem) {
//                                        Log.d(TAG, "buyAlbum albumItemPayListener payResponse " + payResult.toString());
//                                        if (payResult.getPurchaseSucess().getStatus() == 1) {
//                                            ViewUtil.setViewVisibility(tvBuy, GONE);
//                                            PayManager.getInstance().removeItemSingleListener(mPlayItem, this);
//                                            albumItemPayListener = null;
//                                        }
//                                    }
//                                });
//                                break;
//                            default:
//                                break;
//                        }
//                    }
//                });
                if (needBuy) {
                    buyAlbum(PayConst.PAY_TYPE_ALBUM);
                }
            } else {
                ViewUtil.setViewVisibility(albumFlagTv, GONE);
            }
        } else {
            ViewUtil.setViewVisibility(albumFlagTv, GONE);
        }
    }

    /**
     * 通知检查播单
     */
    private void notifyCheckPlayList() {
        this.isResetDatas.set(true);
    }

    private void buyAlbum(int type) {
        PayManager.getInstance().pay(type, PlayerManager.getInstance().getCurPlayItem());
    }

    @Override
    public void onSubscribeSuccess(int status) {

    }

    @Override
    public void onSubscribeError() {

    }

    @Override
    public void onUnSubscribeSuccess(int status) {

    }

    @Override
    public void onUnSubscribeError() {

    }

    @Override
    public void updateSubscribe(boolean isSubscribe) {

    }

    private void loadPageData(long audioId, int pageNum) {
        //只有专辑才清除选中，因为ai电台的loadPageData方法为空实现。
        if (mAdapter != null && PlayerManagerHelper.getInstance().getCurrentPlayType() == PlayerConstants.RESOURCES_TYPE_ALBUM)
            mAdapter.unSelectItem();
        PlayerManager.getInstance().loadPageData(audioId, pageNum, new IPlayListGetListener() {
            @Override
            public void onDataGet(PlayItem playItem, List<PlayItem> arrayList) {
                mFirstPageCount = arrayList.size();
                updatePlayListInfo();
            }

            @Override
            public void onDataGetError(PlayItem playItem, int i, int i1) {

            }
        });
    }

    private void addScrollEvent(boolean needRender) {
        addScrollEvent(needRender, true);
    }

    private void addScrollEvent(boolean needRender, boolean needDelay) {
        int msg;
        if (needRender) msg = HANDLER_SCROLL_AND_RENDER_ITEM;
        else msg = HANDLER_SCROLL_TO_CURRENT;
        //检查是否已经存在一个重新渲染的请求，如果存在则应删除。防止快速切换时出现重新渲染多次的情况
        if (mHandler.hasMessages(msg)) {
            mHandler.removeMessages(msg);
        }

        long delay = 0;
        if (needDelay) {
            delay = DELAY_SCROLL_TIME_MS;
        }
        //按照视频里面的效果，先展示普通列表，过一段时间后再高亮直播项
        mHandler.sendEmptyMessageDelayed(msg, delay);
    }

    private void updateOriginPlayList(List<PlayItem> playItems) {
        IPlayListControl playListControl = PlayerManager.getInstance().getPlayListControl();
        if (originPlayList == null) originPlayList = new ArrayList<>();
        else originPlayList.clear();
        if (playListControl == null) {
            originPlayList.addAll(playItems);
            return;
        }
        List<PlayItem> playList = playListControl.getPlayList();
        originPlayList.addAll(playList);
        if (mFirstPageCount <= 0) {
            mFirstPageCount = playList.size();
        }
    }

    private void setPlayListAdapter(List<PlayItem> playItems) {
        //修改登录状态改变时进入播单页加载下一页会刷新整个列表的问题
        isResetDatas.set(false);
        if (mAdapter == null) {
            //setLayoutAnimation来实现第一次显示的时候item的延迟加载动画
            Animation animation = AnimationUtils.loadAnimation(getContext(), R.anim.online_player_anim_item_play_item);
            LayoutAnimationController controller = new LayoutAnimationController(animation);
            controller.setDelay(0.1f);
            controller.setOrder(LayoutAnimationController.ORDER_NORMAL);
            recyclerView.setLayoutAnimation(controller);

            //保存更新数据前的播单列表，因为一旦刷新，adapter的数据会自动刷新，因为adapter的数据以及sdk内维护的播单列表是同一个list
            updateOriginPlayList(playItems);
            mAdapter = new AlbumPlayListRvAdapter(originPlayList);
            recyclerView.setAdapter(mAdapter);
            RecyclerViewExposeUtil exposeUtil = new RecyclerViewExposeUtil();
            exposeUtil.setRecyclerItemExposeListener(recyclerView, this);
            mAdapter.setOnItemClickListener(new BaseAdapter.OnItemClickListener<PlayItem>() {
                @Override
                public void onItemClick(View view, int viewType, PlayItem playItem, int position) {
                    onPlayListItemClick(view, viewType, playItem, position);
                }
            });
            addScrollEvent(true);
            return;
        }
        addScrollEvent(false);
    }

    private void updatePlayList(List<PlayItem> playItems) {
        if (mAdapter == null || ListUtil.isEmpty(playItems)) {
            return;
        }
        IPlayListControl playListControl = PlayerManager.getInstance().getPlayListControl();
        if (playListControl == null) return;
        List<PlayItem> arrayList = playListControl.getPlayList();
        if (arrayList == null) return;

        Log.d(TAG, "updatePlayList---》playList=" + arrayList.hashCode() + " ,originLIst=" + originPlayList.hashCode());
//        mAdapter.setDataList(arrayList, false);
        boolean isSortTypeChanged = this.isSortTypeChanged.compareAndSet(true, false);
        boolean isDataReset = isSortTypeChanged || isResetDatas.compareAndSet(true, false);
        if (isDataReset) {
            //支付成功或登陆状态改变后刷新播单
            updateOriginPlayList(playItems);
            mAdapter.notifyDataSetChanged();
            if (mHandler.hasMessages(HANDLER_SCROLL_AND_RENDER_ITEM)) {
                mHandler.removeMessages(HANDLER_SCROLL_AND_RENDER_ITEM);
            }
            mHandler.sendEmptyMessage(HANDLER_SCROLL_AND_RENDER_ITEM);
            if (isSortTypeChanged) {
                EventBus.getDefault().post(new PlayerCheckPreOrNextButtonEBData());
            }
        } else {
            //利用DiffUtil.calculateDiff()方法，传入一个规则DiffUtil.Callback对象，和是否检测移动item的 boolean变量，得到DiffUtil.DiffResult 的对象
            DiffUtil.DiffResult diffResult = DiffUtil.calculateDiff(new OnlinePlayItemDiffCallback(originPlayList, arrayList), true);
            //保存更新数据前的播单列表，因为一旦刷新，adapter的数据会自动刷新，因为adapter的数据以及sdk内维护的播单列表是同一个list
            updateOriginPlayList(playItems);
            //利用DiffUtil.DiffResult对象的dispatchUpdatesTo（）方法，传入RecyclerView的Adapter
            diffResult.dispatchUpdatesTo(mAdapter);

            //如果切换了碎片但是onPlayerPrepare方法回调时没有找到，那么就需要在播单更新时移动
            if (prepareNotFoundPlayItem.compareAndSet(true, false)) {
                if (mHandler.hasMessages(HANDLER_SCROLL_AND_RENDER_ITEM)) {
                    mHandler.removeMessages(HANDLER_SCROLL_AND_RENDER_ITEM);
                }
                mHandler.sendEmptyMessage(HANDLER_SCROLL_AND_RENDER_ITEM);
            } else {
                if (mHandler.hasMessages(HANDLER_RENDER_ITEM)) {
                    mHandler.removeMessages(HANDLER_RENDER_ITEM);
                }
                mHandler.sendEmptyMessage(HANDLER_RENDER_ITEM);
            }
        }
    }

    private boolean isPlayListUnChanged(List<PlayItem> allList, List<PlayItem> partList) {
        if (originPlayList == null || originPlayList.size() == 0 && !ListUtil.isEmpty(allList)) {
            return false;
        }
        return allList.get(0).getAudioId() == partList.get(0).getAudioId() && partList.size() == allList.size()
                && allList.get(0).getAudioId() == originPlayList.get(0).getAudioId() && allList.size() == originPlayList.size();
    }

    public void resetSmartRefreshLayoutNormal(boolean succeed) {
        if (refreshLayout == null) return;
        refreshLayout.finishLoadMore(succeed);
        refreshLayout.finishRefresh(succeed);
    }

    private void onPlayListItemClick(View view, int viewType, PlayItem playItem, int position) {
        if (AntiShake.check(playItem.getAudioId())) {
            return;
        }
        if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(getContext())) {
            return;
        }
        //接口修改后，playItem中所有的播放url都会置空，需要再单独请求一个接口获取真正播放的url
        String playUrl = playItem.getPlayUrl();
//                    if (TextUtils.isEmpty(playUrl)) { //url为空直接提示
//                        ToastUtil.showNormal(AppDelegate.getInstance().getContext(), R.string.is_not_online);
//                    } else { //url不为空调用播放逻辑
        //为了统一处理播放拦截逻辑，此处playUrl为空时也需要调用播放逻辑，在PlayControl中再去请求播放的url地址
        PlayerManager playerManager = PlayerManager.getInstance();
        String currentPlayUrl = playerManager.getCurPlayItem().getPlayUrl();
        if (StringUtil.isNotEmpty(currentPlayUrl) && currentPlayUrl.equals(playUrl)
        ) {
            if (PlayerManagerHelper.getInstance().isPlayingAd()) {
                if (PlayerManagerHelper.getInstance().isAudioAdPlayLockOver()) {
                    return;
                }
                PlayerManagerHelper.getInstance().finishAudioAd();
                return;
            }
            if (!playerManager.isPlaying()) {
                PlayerManagerHelper.getInstance().play(true);
            }
            return;
        }
        if (playItem instanceof RadioPlayItem) {
            if (((RadioPlayItem) playItem).getRadioInfoData().getRadioSubTagType()
                    == Constants.AI_RADIO_CONTENT_TYPE_RECOMMEND) {
                ReportUtil.addRecommendSelectEvent("0", playItem.getCallback());
            }
        }
        ReportUtil.addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_PLAY_LIST, PlayerUiControlReportEvent.CONTROL_TYPE_CLICK, PlayerUiControlReportEvent.POSITION_PLAY_FRAGEMNT);
        reportContentClickEvent(playItem, position);
        PlayerLogUtil.log(getClass().getSimpleName(), "play list click");
        PlayerManagerHelper.getInstance().startPlayItemInList(playItem, true); //调用播放
    }

    /**
     * 点击item事件上报
     *
     * @param item
     */
    private void reportContentClickEvent(PlayItem item, int position) {
        ReportUtil.addContentClickEvent(String.valueOf(item.getAudioId()),
                ReportParamUtil.getRadioType(item.getType()),
                ReportParamUtil.getAudioType(item),
                item.getRadioId(),
                ReportParamUtil.getEventTag(item.getVip() == 1, item.getFine() == 1),
                getPageId(), getPageId(), "" + position);
    }

    /**
     * 创建订阅回调
     *
     * @param isSubscribe 订阅（true）或取消订阅（false）
     * @param playItem    playItem
     * @param position    playItem在列表中的位置
     * @return
     */
    private OnlineSubscribeCallback createSubscribeCallback(boolean isSubscribe, Object playItem, int position) {
        return new OnlineSubscribeCallback(null) {
            @Override
            public void onResult(boolean result, int status) {
                if (result) {
                    if (playItem instanceof AlbumPlayItem) {
                        AlbumPlayItem album = (AlbumPlayItem) playItem;
                        album.getInfoData().setIsLiked(isSubscribe ? 1 : 0);
                    } else {
                        RadioPlayItem album = (RadioPlayItem) playItem;
                        album.getInfoData().setIsLiked(isSubscribe ? 1 : 0);
                    }
                } else {
                    if (playItem instanceof AlbumPlayItem) {
                        AlbumPlayItem album = (AlbumPlayItem) playItem;
                        album.getInfoData().setIsLiked(isSubscribe ? 0 : 1);
                    } else {
                        RadioPlayItem album = (RadioPlayItem) playItem;
                        album.getInfoData().setIsLiked(isSubscribe ? 0 : 1);
                    }
                }

                mAdapter.notifyItemChanged(position, false);
            }

            @Override
            public void onFailure(ErrorInfo errorInfo) {
                Log.e(TAG, errorInfo.exception.getMessage());
            }
        };
    }


    private void updatePlayListInfo() {
        if (!getLifecycle().getCurrentState().isAtLeast(Lifecycle.State.CREATED)) return;
        IPlayListControl playListControl = PlayerManager.getInstance().getPlayListControl();
        if (playListControl != null) {
            setEnableRefresh(playListControl.hasPrePage());
            setEnableLoadMore(playListControl.hasNextPage());

            PlaylistInfo playListInfo = playListControl.getPlayListInfo();
            if (playListInfo == null) return;
            albumTitleTv.setText(StringUtil.getMaxString(playListInfo.getAlbumName(), 10));
            if (PlayerManagerHelper.getInstance().getCurrentPlayType() == PlayerConstants.RESOURCES_TYPE_ALBUM) {
                ViewUtil.setViewVisibility(playItemCountTv, View.VISIBLE);
                playItemCountTv.setText(getContext().getString(R.string.online_player_album_play_item_count, playListInfo.getCountNum()));
            } else if (PlayerManagerHelper.getInstance().getCurrentPlayType() == PlayerConstants.RESOURCES_TYPE_RADIO) {
                ViewUtil.setViewVisibility(playItemCountTv, View.GONE);
            }
            getPayInfo();

            RecyclerView.LayoutManager layoutManager = recyclerView.getLayoutManager();
            if (layoutManager != null && mFirstPageCount != 0 && layoutManager instanceof LinearLayoutManager) {
                boolean isShow = LOAD_FIRST_PAGE_WHEN_SCROLL_TO_TOP && (playListControl.hasPrePage() || ((LinearLayoutManager) layoutManager).findLastVisibleItemPosition() > mFirstPageCount)
                        || !LOAD_FIRST_PAGE_WHEN_SCROLL_TO_TOP && (((LinearLayoutManager) layoutManager).findLastVisibleItemPosition() > mFirstPageCount);
                if (isShow) {
                    ViewUtil.setViewVisibility(scrollToTopIv, View.VISIBLE);
                } else {
                    ViewUtil.setViewVisibility(scrollToTopIv, View.GONE);
                }
            }
        }
    }

    public void setEnableRefresh(boolean enable) {
        if (refreshLayout != null)
            refreshLayout.setEnableRefresh(enable);
    }

    public void setEnableLoadMore(boolean enable) {
        if (refreshLayout != null)
            refreshLayout.setEnableLoadMore(enable);
    }

    private void getPayInfo() {
        if (NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
            if (mPresenter instanceof OnlineRadioPlayerPresenter)
                ((OnlineRadioPlayerPresenter) mPresenter).getPayInfo(Long.parseLong(PlayerManager.getInstance().getCurPlayItem().getAlbumId()));
        }
    }

    @Override
    public String getPageId() {
        return Constants.ONLINE_PAGE_ID_PLAYER_ALBUM;
    }

    @Override
    public void onDestroyView() {
        isDestroy = true;
        removeAllListener();
        super.onDestroyView();
        AudioSubscribeCacheUtil.getInstance().clear();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        AdvertisingImager advertisingImager = AdvertisingManager.getInstance().getImager();
        if (advertisingImager != null) {
            advertisingImager.skip(null);
        }
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        RecyclerView.Adapter adapter = recyclerView.getAdapter();
        if (adapter != null) {
            recyclerView.getAdapter().notifyDataSetChanged();
        }

        recyclerView.setAdapter(null);
        recyclerView.setAdapter(adapter);
    }

    private void removeAllListener() {
        vipPayListener = null;
        albumPayListener = null;
        mHandler.removeCallbacksAndMessages(null);
        PlayerManager.getInstance().removePlayControlStateCallback(this.stateListener);
        PlayerManager.getInstance().removePlayListControlStateCallback(mPlayListControlStateCallback);
        if (mScrollTopListener != null)
            recyclerView.removeOnScrollListener(mScrollTopListener);
        if (mPlayerFragmentHelper != null) {
            mPlayerFragmentHelper.onDestroyPlayerFragmentHelper();
        }
        ComponentUtil.removeObserver(UserComponentConst.NAME, mRadioPlayerUserObserver);
        PayManager.getInstance().clear();
    }


    public void setItemCountViewGroupVisibility(int visibility) {
        ViewUtil.setViewVisibility(albumTitleTv, visibility);
        ViewUtil.setViewVisibility(playItemCountTv, visibility);
        ViewUtil.setViewVisibility(sortIcon, visibility);
        ViewUtil.setViewVisibility(SortTipTv, visibility);
        ViewUtil.setViewVisibility(divider, visibility);
    }

    public void setItemCountViewGroupVisibilityWithoutSort(int visibility) {
        ViewUtil.setViewVisibility(albumTitleTv, visibility);
        ViewUtil.setViewVisibility(playItemCountTv, visibility);
        ViewUtil.setViewVisibility(divider, visibility);
    }

    public void setItemCountViewGroupVisibilityWithoutTitleView(int visibility) {
        ViewUtil.setViewVisibility(playItemCountTv, visibility);
        ViewUtil.setViewVisibility(sortIcon, visibility);
        ViewUtil.setViewVisibility(SortTipTv, visibility);
        ViewUtil.setViewVisibility(divider, visibility);
    }

    protected void onPlayItemPause(PlayItem playItem) {
        if (mAdapter != null)
            mAdapter.notifyAdapterPlayItemPaused(playItem);
    }

    protected void onPlayItemFailed(PlayItem playItem) {
        if (mAdapter != null)
            mAdapter.notifyAdapterPlayItemPaused(playItem);
    }

    protected void onPlayItemEnd(PlayItem playItem) {
        if (mAdapter != null)
            mAdapter.notifyAdapterPlayItemPaused(playItem);
    }


    @Override
    public void onResume() {
        super.onResume();
        isDestroy = false;
    }

    private void watchUserStateChangeAndReplayAudioList() {
        UserInfoManager userInfoManager = UserInfoManager.getInstance();
        boolean loinStateChange = userInfoManager.isLoginStateChange();
        boolean vipStateChange = userInfoManager.isVipStateChange();
        Log.i(TAG, "loinStateChange:" + loinStateChange + " vipStateChange:" + vipStateChange);
        if (isFirstEnterPage.compareAndSet(true, false)) {
            return;
        }
        if (loinStateChange || vipStateChange) {
            if (mAdapter instanceof AlbumPlayListRvAdapter)
                ((AlbumPlayListRvAdapter) mAdapter).autoSubscribe();
            //登陆状态改变或者vip状态改变，需要刷新全部播单项
            notifyCheckPlayList();
            replayAudioList();
        }
//        replayAudioList();
    }

    @Override
    public void onUserVisible() {
        super.onUserVisible();
        watchUserStateChangeAndReplayAudioList();
    }

    @Override
    public void onUserInvisible() {
        super.onUserInvisible();
        AdvertisingImager advertisingImager = AdvertisingManager.getInstance().getImager();
        if (advertisingImager != null) {
            advertisingImager.skip(null);
        }
    }

    private void replayAudioList() {
        recyclerView.postDelayed(new Runnable() {
            @Override
            public void run() {
                Log.i(TAG, "replayAudioList");
                long mRadioId = mPlayerFragmentHelper.getCurrentAlbumId();
                int mRadioType = PlayerManager.getInstance().getCurPlayItem().getType();
                PlayerManagerHelper.getInstance().restart(String.valueOf(mRadioId), mRadioType);
            }
        }, 200);

//        this.needResetSortType.set(true);
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    /**
     * 是否在前台
     *
     * @return
     */
    public boolean isDestroy() {
        return isDestroy;
    }

    public static OnlineAlbumPlayListRebuildFragment newInstance(String mTag) {
        OnlineAlbumPlayListRebuildFragment fragment = new OnlineAlbumPlayListRebuildFragment();
        Bundle args = new Bundle();
        args.putString(ARGUMENT_TAG, mTag);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onItemViewVisible(boolean visible, int position) {
        if (visible && recyclerView != null) {
            AlbumPlayListRvAdapter playerListAdapter = (AlbumPlayListRvAdapter) recyclerView.getAdapter();
            if (playerListAdapter != null) {
                PlayItem bean = playerListAdapter.getItemData(position);
                ReportUtil.addContentShowEvent(String.valueOf(bean.getAudioId()),
                        ReportParamUtil.getRadioType(bean.getType()),
                        ReportParamUtil.getAudioType(bean),
                        String.valueOf(bean.getRadioId()),
                        ReportParamUtil.getEventTag(bean.getVip() == 1, bean.getFine() == 1),
                        Constants.ONLINE_PAGE_ID_PLAYER_ALBUM, "", "" + position);
            }
        }
    }

    private class RadioPlayerUserObserver implements DynamicComponent, MainThreadable {

        @Override
        public String getName() {
            return "RadioPlayer-UserObserver";
        }

        @Override
        public boolean onCall(RealCaller caller) {
            String actionName = caller.actionName();
            switch (actionName) {
                case UserStateObserverProcessorConst.USER_LOGIN:
                case UserStateObserverProcessorConst.USER_LOGOUT:
                    getPayInfo();
                    replayAudioList();
                    break;
                default:
                    break;
            }
            return false;
        }

        @Override
        public Boolean shouldActionRunOnMainThread(String actionName, ComponentClient caller) {
            return true;
        }
    }
}
