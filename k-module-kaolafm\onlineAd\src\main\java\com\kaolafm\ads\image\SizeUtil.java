package com.kaolafm.ads.image;

import android.content.Context;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.WindowManager;

import com.kaolafm.ads.image.AdSize;

public class SizeUtil {

    private static float DEFAULT_WIDTH;
    private static float DEFAULT_HEIGHT;
    private static float DEFAULT_RATIO;

    /**
     * 1、如果车机屏幕宽 ≤ 屏幕高，为竖屏：[qww
     *  a. 如果车机屏幕宽 ≤ 标准图片宽度，则取宽的2/3，对标准图进行等比压缩；
     *
     *  b. 如果车机屏幕宽 > 标准图片宽度，则放标准图原图；
     *
     * 2、如果车机屏幕宽 > 屏幕高，为横屏。（注：为了将图片放在屏幕的上半部分进行展示，以确保视觉效果的美观，因此都会取屏幕高的二分之一进行处理。）
     *
     *  a.  车机屏幕宽 / 二分之一高 > 标准图片宽高比：
     *
     *      a.1 当屏幕高/2 ≥ 标准图片高时，放标准图原图；
     *
     *      a.2 当屏幕高/2 < 标准图片高时，取屏幕高/2的值，对标准图进行等比压缩；
     *
     *  b.  车机屏幕宽 / 二分之一高 = 标准图片宽高比：
     *
     *      b.1 当屏幕高/2 > 标准图片高时，放标准图原图；
     *
     *      b.2 当屏幕高/2 ≤ 标准图片高时，取屏幕宽的2/3，对标准图进行等比压缩；
     *
     *  c.  车机屏幕宽 / 二分之一高 < 标准图片宽高比：
     *
     *      c.1 当屏幕宽 > 标准图片宽，且屏幕高/2 > 标准图片高时，放标准图原图；
     *
     *      c.2 当屏幕宽 ≤ 标准图片宽时，取屏幕宽的2/3，对标准图进行等比缩放；
     * @param context
     * @return 图片尺寸
     */
    public static AdSize getAdSize(Context context, float imageWidth, float imageHeight) {
        DEFAULT_WIDTH = imageWidth;
        DEFAULT_HEIGHT = imageHeight;
        DEFAULT_RATIO = DEFAULT_WIDTH / DEFAULT_HEIGHT;
        AdSize adSize = new AdSize();
        float sw = getScreenWidth(context);
        float sh = getScreenHeight(context);
        if(sw > sh && 2 * sw / sh > DEFAULT_RATIO && sh / 2 < DEFAULT_HEIGHT){
            Log.i("adsize", "1" );
            return getAdSizeBaseOnHeight(adSize, sw, sh);
        }
        if (sw <= sh && sw <= DEFAULT_WIDTH){
            Log.i("adsize", "2" );
            return getAdSizeBaseOnWidth(adSize, sw, sh);
        }
        if(sw > sh && ((2 * sw /sh == DEFAULT_RATIO && sh / 2 <= DEFAULT_HEIGHT)
                || (2 * sw /sh < DEFAULT_RATIO && sw <= DEFAULT_WIDTH))){
            Log.i("adsize", "3" );
            return getAdSizeBaseOnWidth(adSize, sw, sh);
        }
        Log.i("adsize", "4" );
        return getDefaultAdSize(adSize);
    }

    private static AdSize getDefaultAdSize(AdSize adSize) {
        adSize.width = (int) DEFAULT_WIDTH;
        adSize.height = (int) DEFAULT_HEIGHT;
        return adSize;
    }

    private static AdSize getAdSizeBaseOnWidth(AdSize adSize, float sw, float sh) {
        adSize.width = (int) (2 * sw / 3);
        adSize.height = (int) (adSize.width / DEFAULT_RATIO);
        return adSize;
    }

    private static AdSize getAdSizeBaseOnHeight(AdSize adSize, float sw, float sh) {
        adSize.height = (int) (sh /2);
        adSize.width = (int) (adSize.height * DEFAULT_RATIO);
        return adSize;
    }

    /**
     * 获取屏幕宽度 px
     */
    public static int getScreenWidth(Context context) {
        WindowManager wm = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        DisplayMetrics displayMetrics = new DisplayMetrics();
        wm.getDefaultDisplay().getRealMetrics(displayMetrics);
        return displayMetrics.widthPixels;
    }

    /**
     * 获取屏幕高度
     */
    public static int getScreenHeight(Context context) {
        WindowManager wm = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        DisplayMetrics displayMetrics = new DisplayMetrics();
        wm.getDefaultDisplay().getRealMetrics(displayMetrics);
        return displayMetrics.heightPixels;
    }
}
