package com.kaolafm.kradio.flavor.impl;

import android.util.Log;

import com.hsae.autosdk.source.Source;
import com.hsae.autosdk.source.SourceConst;
import com.kaolafm.kradio.lib.base.flavor.KRadioPhoneStateInter;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-10-15 16:28
 ******************************************/
public class KRadioPhoneStateImpl implements KRadioPhoneStateInter {
    private static final String TAG = "KRadioPhoneStateImpl";

    @Override
    public boolean isInCall() {
        boolean flag = true;
        try {
            flag = new Source().isAllowPlay(SourceConst.App.RADIO);
        } catch (Throwable t) {
        }
        Log.i(TAG, "isInCall flag = " + flag);
        return !flag;
    }
}
