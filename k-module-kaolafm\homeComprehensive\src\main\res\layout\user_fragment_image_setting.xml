<?xml version="1.0" encoding="utf-8"?>

<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="@dimen/m30">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="left"
                android:gravity="center"
                android:text="图片尺寸"
                android:textColor="@color/text_color_5"
                android:textSize="@dimen/text_size5" />

            <RadioGroup
                android:id="@+id/image_radiogroup"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/m20"
                android:orientation="horizontal">

                <RadioButton
                    android:id="@+id/image_high"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@null"
                    android:gravity="center"
                    android:text="550*550\n(适用高配-2G车机)"
                    android:textColor="@color/text_color_5" />

                <RadioButton
                    android:id="@+id/image_medium"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="340*340\n(适用高配-1.5~2G车机)"
                    android:textColor="@color/text_color_5" />

                <RadioButton
                    android:id="@+id/image_low"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="250*250\n(适用高配-1G车机)"
                    android:textColor="@color/text_color_5" />

            </RadioGroup>

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/m20">

                <EditText
                    android:id="@+id/image_customize"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"

                    android:layout_gravity="left"
                    android:hint="请输入图片宽高"
                    android:inputType="number|numberDecimal"
                    android:textColor="@color/text_color_white" />

                <Button
                    android:id="@+id/image_size_commit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="right"
                    android:text="提交" />
            </FrameLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="left"
                android:gravity="center"
                android:text="图片特效"
                android:textColor="@color/text_color_5"
                android:textSize="@dimen/text_size5" />

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/jpgorwebp_switch"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="right|center"
                android:layout_marginTop="@dimen/m20"
                android:text="开启webp图片格式"
                android:textColor="@color/text_color_5"
                android:textSize="@dimen/text_size4" />

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/splash_switch"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="right|center"
                android:layout_marginTop="@dimen/m20"
                android:text="开启闪屏 3 S"
                android:textColor="@color/text_color_5"
                android:textSize="@dimen/text_size4" />

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/player_blur_switch"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="right|center"
                android:layout_marginTop="@dimen/m20"
                android:text="音乐播放器高斯模糊"
                android:textColor="@color/text_color_5"
                android:textSize="@dimen/text_size4" />

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/broadcast_playlist_switch"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="right|center"
                android:layout_marginTop="@dimen/m20"
                android:text="广播播放器节目列表蒙层"
                android:textColor="@color/text_color_5"
                android:textSize="@dimen/text_size4" />

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/home_image_switch"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="right|center"
                android:layout_marginTop="@dimen/m20"
                android:text="首页图片蒙层"
                android:textColor="@color/text_color_5"
                android:textSize="@dimen/text_size4" />

        </LinearLayout>

    </LinearLayout>
</ScrollView>