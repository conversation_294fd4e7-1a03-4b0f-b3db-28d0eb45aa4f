package com.kaolafm.ad.comprehensive.audioad;

import android.util.Log;

import com.kaolafm.ad.comprehensive.control.KradioAdSceneConstants;
import com.kaolafm.kradio.player.helper.intercept.AdPlayChainIntercept;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.TempTaskPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

/**
 * @ClassName AudioStartAudioAD
 * @Description 节目开始音频广告(节目切换广告)
 * <AUTHOR>
 * @Date 2020-03-11 17:40
 * @Version 1.0
 */
public class AudioStartAudioAD extends BaseAudioAD {

    private AdPlayChainIntercept.IPlayAdEndCallback mPlayAdEndCallback;

    @Override
    public void onGetData() {
        startPlay(makeAudioAdPlayItem(mAudioAdvert.getUrl(), KradioAdSceneConstants.SUB_TYPE_SWITCH_PROGROM));
    }

    @Override
    protected TempTaskPlayItem makeAudioAdPlayItem(String url, int type) {
        TempTaskPlayItem adPlayItem = super.makeAudioAdPlayItem(url, type);
        adPlayItem.setNeedNextInnerAction(false);
        adPlayItem.setPlayStateListener(new BasePlayStateListener() {
            @Override
            public void onPlayerEnd(PlayItem playItem) {
                notifyPlayEnd();
                playAudioAdEnd();
            }
        });
        return adPlayItem;
    }


    public void setPlayEndCallback(AdPlayChainIntercept.IPlayAdEndCallback iPlayAdEndCallback) {
        Log.i(TAG, "设置广告请求数据返回callback.");
        mPlayAdEndCallback = iPlayAdEndCallback;
    }

    private void notifyPlayEnd() {
        if (mPlayAdEndCallback != null) {
            mPlayAdEndCallback.playEnd();
        }
    }

    @Override
    public void onError() {
        notifyPlayEnd();
    }

    @Override
    public void setCallback(Object o) {
        if (o instanceof AdPlayChainIntercept.IPlayAdEndCallback) {
            mPlayAdEndCallback = (AdPlayChainIntercept.IPlayAdEndCallback) o;
        }
    }
}
