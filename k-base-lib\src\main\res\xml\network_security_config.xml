<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
<!--    <domain-config>-->
<!--        <domain includeSubdomains="true">bv.lnk0.com</domain>-->
<!--        <domain includeSubdomains="true">b.lnk0.com</domain>-->
<!--        <trust-anchors>-->
<!--            <certificates src="user" />-->
<!--        </trust-anchors>-->
<!--    </domain-config>-->
    <!-- Trust user added CAs while debuggable only -->
    <debug-overrides>
        <trust-anchors>
            <!--信任用户安装的证书-->
            <certificates src="user" />
        </trust-anchors>
    </debug-overrides>
    <base-config cleartextTrafficPermitted="true" />

</network-security-config>
