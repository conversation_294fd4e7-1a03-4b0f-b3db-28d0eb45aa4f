<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_broadcast"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginRight="@dimen/x41"
    android:layout_marginBottom="@dimen/y24"
    android:background="@drawable/bg_broadcast_list_item"
    android:orientation="horizontal"
    android:paddingEnd="@dimen/x20"
    tools:ignore="RtlSymmetry">

    <com.kaolafm.kradio.component.ui.base.view.OvalImageView
        android:id="@+id/iv_broadcast_cover"
        android:layout_width="@dimen/m114"
        android:layout_height="@dimen/m114"
        android:contentDescription="@null"
        android:scaleType="fitXY"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:rid_type="4"
        tools:src="@drawable/ic_launcher" />

    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/sh_bg_r8l"
        app:layout_constraintBottom_toBottomOf="@id/iv_broadcast_cover"
        app:layout_constraintEnd_toEndOf="@id/iv_broadcast_cover"
        app:layout_constraintStart_toStartOf="@id/iv_broadcast_cover"
        app:layout_constraintTop_toTopOf="@id/iv_broadcast_cover" />

    <LinearLayout
        android:id="@+id/broadcast_name_ll"
        android:layout_marginStart="@dimen/x30"
        android:layout_marginTop="@dimen/x18"
        android:layout_marginEnd="@dimen/x18"
        android:layout_width="0dp"
        android:orientation="horizontal"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_broadcast_cover"
        app:layout_constraintTop_toTopOf="parent">

        <com.kaolafm.kradio.component.ui.base.view.RateView
            android:id="@+id/card_layout_playing"
            android:layout_width="@dimen/m38"
            android:layout_height="@dimen/m38"
            android:layout_marginRight="@dimen/m15"
            app:lottie_autoPlay="true"
            app:lottie_fileName="lottie/rate.json"
            app:lottie_loop="true" />

        <TextView
            android:id="@+id/tv_broadcast_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical|start"

            android:ellipsize="end"
            android:gravity="center_vertical|start"
            android:lines="1"
            android:singleLine="true"
            android:textColor="@color/text_color_7"
            android:textSize="@dimen/text_size4"

            tools:text="北京FM  2.2.1" />
    </LinearLayout>

    <ImageView
        android:id="@+id/ivPlay"
        android:layout_width="@dimen/m20"
        android:layout_height="@dimen/m20"
        android:layout_marginTop="@dimen/y13"
        android:scaleType="centerInside"
        android:src="@drawable/sl_item_subcategory_broadcast_subimg"
        app:layout_constraintStart_toStartOf="@id/broadcast_name_ll"
        app:layout_constraintTop_toBottomOf="@id/broadcast_name_ll"
        tools:text="91.8万" />

    <TextView
        android:id="@+id/tvListenNum"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/x7"
        android:textColor="@color/text_color_8"
        android:textSize="@dimen/text_size2"
        app:layout_constraintBottom_toBottomOf="@id/ivPlay"
        app:layout_constraintStart_toEndOf="@id/ivPlay"
        app:layout_constraintTop_toTopOf="@id/ivPlay"
        tools:text="91.8万" />

    <ImageView
        android:id="@+id/live_icon"
        android:layout_width="@dimen/m52"
        android:layout_height="@dimen/m24"
        android:layout_alignStart="@+id/iv_item_home_cover"
        android:layout_alignTop="@+id/iv_item_home_cover"
        android:scaleType="centerCrop"
        android:src="@drawable/comprehensive_icon_live_class"
        tools:ignore="MissingConstraints" />
    <!--    <ImageView-->
    <!--        android:id="@+id/pi_broadcast_playing_indicator"-->
    <!--        android:layout_width="@dimen/player_list_playing_bar_size"-->
    <!--        android:layout_height="@dimen/player_list_playing_bar_size"-->
    <!--        android:layout_gravity="center_vertical"-->
    <!--        android:layout_marginRight="@dimen/m30"-->
    <!--        app:layout_constraintEnd_toEndOf="parent"-->
    <!--        android:visibility="gone"-->
    <!--        app:layout_constraintBottom_toBottomOf="parent"-->
    <!--        app:layout_constraintTop_toTopOf="parent"-->
    <!--        android:layout_marginStart="@dimen/x20"-->
    <!--        android:background="@drawable/playing_bar_chart" />-->

</androidx.constraintlayout.widget.ConstraintLayout>
