package com.kaolafm.kradio.lib.base.ui;

import android.view.View;

import com.kaolafm.kradio.lib.base.mvp.IPresenter;

public abstract class MBaseViewPagerFragment<P extends IPresenter> extends BaseViewPagerFragment<P> {
    /**
     * 此函数针对BaseFragment做了空实现，不能去掉
     *
     * @param view
     */
    @Override
    protected void changeViewLayoutForStatusBar(View view) {

    }

    @Override
    protected void addFragmentRootViewPadding(View view) {

    }

    @Override
    protected boolean isReportFragment() {
        return true;
    }
}
