package com.kaolafm.kradio.message.comprehensive;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.bean.CrashMessageBean;
import com.kaolafm.kradio.lib.utils.StringUtil;

import java.util.BitSet;

public class MessageDialogHelper {

    static boolean isEbPushMessage(CrashMessageBean bean){
        return bean.getMsgType().equals("01");
    }

    static int getEbPushMessageIcon(CrashMessageBean bean){
        String msgLevel = bean.getMsgLevel();
        if(msgLevel.equals("1")){
            return R.drawable.message_red_iconr;
        } else if(msgLevel.equals("2")){
            return R.drawable.message_blue_icon;
        } else {
            return R.drawable.message_green_icon;
        }
    }
//    /**
//     * 重播按钮一直显示，重播按钮两边按钮存在与否有四种情况：
//     * 00 两边无按钮
//     * 01 右边有按钮
//     * 10 左边有按钮
//     * 11 左右都有按钮
//     * */
//    public static BitSet getButtonLayoutStyle(CrashMessageBean bean){
//        BitSet bs = new BitSet(2);
//        bs.clear();
//
//        if( !StringUtil.isEmpty(bean.getMsgBubbleBtnTextLeft())){
//            bs.set(1);
//        }
//        if( !StringUtil.isEmpty( bean.getMsgBubbleBtnTextRight())){
//            bs.set(0);
//        }
//        return bs;
////        int intValue = 0;
////        for (int i = bs.length() - 1; i >= 0 ; i--) {
////            if (bs.get(i)) {
////                intValue |= (1 << i);
////            }
////        }
////        return intValue;
//    }


    public static int getSubTitleMaxLines(CrashMessageBean bean){
//        private int msgStyleType;//0-文 1-文+图 2-文+按钮 3-文+图+按钮
//        if (bean.getMsgStyleType() == 0 || bean.getMsgStyleType() == 2) {

        if(StringUtil.isEmpty(bean.getMsgPicUrl())){
            return 3;
        } else {
            return  2;
        }
    }
}
