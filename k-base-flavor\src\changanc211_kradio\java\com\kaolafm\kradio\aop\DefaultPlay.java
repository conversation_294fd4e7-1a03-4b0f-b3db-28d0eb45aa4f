package com.kaolafm.kradio.aop;

import android.util.Log;


import com.kaolafm.kradio.network.FlavorApiRequest;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.sdk.core.model.DefaultPlayData;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;

import java.util.List;

@Aspect
public class DefaultPlay {
    private boolean isFirstTime = true;
    private FlavorApiRequest mFlavorApiRequest;

    @Around("execution(* com.kaolafm.opensdk.player.logic.PlayerManager.start(..))")
    public void defaultPlayerManager(ProceedingJoinPoint point) throws Throwable {
        Log.i("DefaultPlay", "defaultPlayerManager: isFirstTime = " + isFirstTime);
        if (isFirstTime) {
            mFlavorApiRequest = FlavorApiRequest.getInstance();
            mFlavorApiRequest.getDefaultPlayInfo(new HttpCallback<DefaultPlayData>() {
                @Override
                public void onSuccess(DefaultPlayData defaultPlayData) {
                    if (defaultPlayData == null) {
                        try {
                            point.proceed();
                        } catch (Throwable throwable) {
                            throwable.printStackTrace();
                        }
                        return;
                    }
                    Log.i("DefaultPlay", "getDefaultPlay onSuccess: " + defaultPlayData.getName() + "        " + defaultPlayData.getId() + "        " + defaultPlayData.getType());
                    PlayerManager.getInstance().play(defaultPlayData.getId(), String.valueOf(defaultPlayData.getType()));
                }

                @Override
                public void onError(ApiException e) {
                    Log.i("DefaultPlay", "getDefaultPlay onError: " + e.toString());
                    try {
                        point.proceed();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                    }
                }
            });
        } else {
            point.proceed();
        }
        isFirstTime = false;
    }



    @Around("execution(* com.kaolafm.sdk.core.mediaplayer.BroadcastRadioPlayerManager.start(..))")
    public void defaultBroadcastRadioPlayerManager(ProceedingJoinPoint point) throws Throwable {
        Log.i("DefaultPlay", "defaultBroadcastRadioPlayerManager: isFirstTime = " + isFirstTime);
        if (isFirstTime) {
            if (mFlavorApiRequest == null) {
                mFlavorApiRequest = new FlavorApiRequest();
            }
            mFlavorApiRequest.getDefaultPlay(new HttpCallback<List<DefaultPlayData>>() {
                @Override
                public void onSuccess(List<DefaultPlayData> listdata) {
                    if (listdata == null || listdata.size() == 0) {
                        try {
                            point.proceed();
                        } catch (Throwable throwable) {
                            throwable.printStackTrace();
                        }
                        return;
                    }
                    DefaultPlayData defaultPlayData = listdata.get(0);
                    if (defaultPlayData != null && defaultPlayData instanceof DefaultPlayData) {
                        Log.i("DefaultPlay", "getDefaultPlay onSuccess: " + defaultPlayData.getName() + "        " + defaultPlayData.getId() + "        " + defaultPlayData.getType());
                        PlayerManager.getInstance().play(defaultPlayData.getId(), String.valueOf(defaultPlayData.getType()));
                    } else {
                        try {
                            point.proceed();
                        } catch (Throwable throwable) {
                            throwable.printStackTrace();
                        }
                    }
                }

                @Override
                public void onError(ApiException e) {
                    Log.i("DefaultPlay", "getDefaultPlay onError: " + e.toString());
                    try {
                        point.proceed();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                    }
                }
            });
        } else {
            point.proceed();
        }
        isFirstTime = false;
    }
}
