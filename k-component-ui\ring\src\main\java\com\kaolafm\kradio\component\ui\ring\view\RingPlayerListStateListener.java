package com.kaolafm.kradio.component.ui.ring.view;

import android.util.Log;

import com.kaolafm.kradio.component.ui.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.opensdk.api.live.model.LiveInfoDetail;
import com.kaolafm.opensdk.player.logic.listener.IPlayListStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

import java.lang.ref.WeakReference;
import java.util.List;

/**
 * RingView播放状态监听。
 *
 * <AUTHOR>
 * @date 2022-07-25
 */
public class RingPlayerListStateListener implements IPlayListStateListener {

    private final String TAG = "Ring.ListStateListener";

    private WeakReference<Ring> weakReference;

    public RingPlayerListStateListener(Ring ring) {
        weakReference = new WeakReference<>(ring);
    }

    @Override
    public void onPlayListChange(List<PlayItem> list) {

    }

    @Override
    public void onPlayListChangeError(PlayItem playItem, int i, int i1) {
        Log.i(TAG, "onPlayListChangeError,"+playItem.toString()+", errorCode="+i+"; errorExtra="+i1);
        if(i == PlayerConstants.ERROR_CODE_PLAY_LIST_URL_DECODE && i1 == 50811){

        }else if(i == PlayerConstants.ERROR_CODE_PLAY_LIST_URL_LIVE_NULL){
            if(i1 == LiveInfoDetail.STATUS_FINISHED){
                ToastUtil.showOnly(AppDelegate.getInstance().getContext(), R.string.player_failed_live_finished);
            }else if(i1 == LiveInfoDetail.STATUS_COMING
            || i1 == LiveInfoDetail.STATUS_START_TODAY
            || i1 == LiveInfoDetail.STATUS_START_TOMORROW
            || i1 == LiveInfoDetail.STATUS_START_AFTER_TOMORROW
            || i1 == LiveInfoDetail.STATUS_NOT_START){
                ToastUtil.showOnly(AppDelegate.getInstance().getContext(), R.string.player_failed_live_not_start);
            }else if(i1 == LiveInfoDetail.STATUS_DELAYED){
                ToastUtil.showOnly(AppDelegate.getInstance().getContext(), R.string.player_failed_live_delay);
            }
        }else {
            if(NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext())){
                ToastUtil.showOnly(AppDelegate.getInstance().getContext(), R.string.player_failed);
            }else {
                ToastUtil.showOnly(AppDelegate.getInstance().getContext(), R.string.no_net_work_str);
            }
        }
//        Ring ring = weakReference.get();
//        if(ring != null){
//            PlayItem curPlayItem = PlayerManager.getInstance().getCurPlayItem();
//            RingManager.getInstance().deleteData(curPlayItem);
//            ring.notifyDataSetChanged();
//        }
    }
}
