package com.kaolafm.kradio.lib.widget.ratio;

import android.content.Context;
import android.content.res.TypedArray;
import androidx.constraintlayout.widget.ConstraintLayout;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.ViewGroup;
import com.kaolafm.kradio.lib.R;
import com.kaolafm.kradio.lib.utils.AnimUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;

/**
 * 可以设置自身宽高比的ConstraintLayout。宽高比的确定是根据横竖屏确定的，如果是横屏的话就根据高度和比例确定宽度，如果是竖屏就根据宽度和比例确定高度。
 * 默认有点击缩放效果，可以设置是否有缩放效果。
 * <AUTHOR>
 * @date 2019-07-29
 */
public class RatioConstraintLayout extends ConstraintLayout {

    private boolean canScale;

    private double mHeightRatio;

    private double mWidthRatio;

    public RatioConstraintLayout(Context context) {
        this(context, null);
    }

    public RatioConstraintLayout(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public RatioConstraintLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }
    private void init(Context context, AttributeSet attrs) {
        TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.RatioConstraintLayout);
        canScale = ta.getBoolean(R.styleable.RatioConstraintLayout_canScale, true);
        String ratio = ta.getString(R.styleable.RatioConstraintLayout_wh_ratio);
        if (!TextUtils.isEmpty(ratio)) {
            String[] wh = ratio.split("\\:");
            if (wh.length == 2) {
                mWidthRatio = Double.valueOf(wh[0]);
                mHeightRatio = Double.valueOf(wh[1]);
            }
        }
        ta.recycle();
    }

    public void setRatio(String whRation) {
        if (!TextUtils.isEmpty(whRation)) {
            String[] wh = whRation.split("\\:");
            if (wh.length == 2) {
                mWidthRatio = Double.valueOf(wh[0]);
                mHeightRatio = Double.valueOf(wh[1]);
            }
        }
    }

    @Override
    public ViewGroup.LayoutParams getLayoutParams() {
        ViewGroup.LayoutParams layoutParams = super.getLayoutParams();
        ViewUtil.switchWH(layoutParams, ResUtil.getOrientation());
        return layoutParams;
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        setMeasuredDimension(getDefaultSize(0, widthMeasureSpec), getDefaultSize(0, heightMeasureSpec));
        int[] specs = ViewUtil.measureByRatio(this, mWidthRatio, mHeightRatio, widthMeasureSpec, heightMeasureSpec);
        super.onMeasure(specs[0], specs[1]);
    }
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (canScale) {
            AnimUtil.onTouchEvent(this, event);
        }
        return super.onTouchEvent(event);
    }
}
