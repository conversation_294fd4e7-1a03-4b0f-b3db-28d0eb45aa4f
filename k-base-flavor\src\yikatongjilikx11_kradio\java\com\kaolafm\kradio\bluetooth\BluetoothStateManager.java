package com.kaolafm.kradio.bluetooth;

public class BluetoothStateManager {
    private static BluetoothStateManager instance;

    private boolean isBluetoothAddedOnPsd;

    private BluetoothStateManager() {
    }

    public static BluetoothStateManager getInstance() {
        if (instance == null) {
            instance = new BluetoothStateManager();
        }

        return instance;
    }

    public boolean isBluetoothAddedOnPsd() {
        return isBluetoothAddedOnPsd;
    }

    public void setBluetoothAddedOnPsd(boolean isAdded) {
        isBluetoothAddedOnPsd = isAdded;
    }
}
