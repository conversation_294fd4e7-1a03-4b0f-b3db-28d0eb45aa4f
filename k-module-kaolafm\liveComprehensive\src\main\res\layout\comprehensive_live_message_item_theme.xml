<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/comprehensive_live_message_item_bg"
    android:paddingVertical="@dimen/comprehensive_live_message_margin_vertical"
    android:paddingHorizontal="@dimen/comprehensive_live_message_margin_horizontal"
    android:minHeight="@dimen/comprehensive_live_message_min_height">

    <LinearLayout
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/typeTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/comprehensive_live_message_system_color"
            android:textSize="@dimen/comprehensive_live_message_text_size"
            android:text="直播公告："/>

        <TextView
            android:id="@+id/contentTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="哈哈哈哈符合身份健康的飞机的设计发空间合肥将卡换地方就开始打飞机快乐福卡收到回复跨境电商"
            android:textColor="@color/comprehensive_live_message_normal_color"
            android:textSize="@dimen/m24" />
    </LinearLayout>



</androidx.constraintlayout.widget.ConstraintLayout>