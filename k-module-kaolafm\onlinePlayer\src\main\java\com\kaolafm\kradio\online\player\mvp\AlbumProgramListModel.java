package com.kaolafm.kradio.online.player.mvp;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.lib.base.mvp.BaseModel;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.media.AlbumRequest;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.trello.rxlifecycle3.LifecycleTransformer;

import java.util.List;

public class AlbumProgramListModel extends BaseModel {
    private final LifecycleTransformer mLifecycleTransformer;

    AlbumProgramListModel(LifecycleTransformer lifecycleTransformer) {
        mLifecycleTransformer = lifecycleTransformer;
    }

    void getAlbumProgramList(long albumId, @AlbumRequest.Sort int sort, int pageSize, int pageNum, HttpCallback<BasePageResult<List<AudioDetails>>> httpCallback) {
        AlbumRequest mAlbumRequest = new AlbumRequest().bindLifecycle(mLifecycleTransformer);
        HttpCallback callback = new HttpCallback<BasePageResult<List<AudioDetails>>>() {

            @Override
            public void onSuccess(BasePageResult<List<AudioDetails>> programDetailsList) {
                if (httpCallback == null) {
                    return;
                }
                if (programDetailsList.getCount() == 0 || ListUtil.isEmpty(programDetailsList.getDataList())) {
                    httpCallback.onError(new ApiException(-1, "数据为空"));
                } else {
                    httpCallback.onSuccess(programDetailsList);
                }
            }

            @Override
            public void onError(ApiException e) {

            }
        };
        mAlbumRequest.getPlaylist(albumId, sort, pageSize, pageNum, callback);
    }

    void getAudioPageAlbumProgramList(long albumId, long audioId, @AlbumRequest.Sort int sort, int pageSize, HttpCallback<BasePageResult<List<AudioDetails>>> httpCallback) {
        AlbumRequest mAlbumRequest = new AlbumRequest().bindLifecycle(mLifecycleTransformer);
        HttpCallback callback = new HttpCallback<BasePageResult<List<AudioDetails>>>() {

            @Override
            public void onSuccess(BasePageResult<List<AudioDetails>> programDetailsList) {
                if (httpCallback == null) {
                    return;
                }
                if (programDetailsList.getCount() == 0 || ListUtil.isEmpty(programDetailsList.getDataList())) {
                    httpCallback.onError(new ApiException(-1, "数据为空"));
                } else {
                    httpCallback.onSuccess(programDetailsList);
                }
            }

            @Override
            public void onError(ApiException e) {

            }
        };
        //传入audioId时，pageNum无效，直接赋值1即可
        mAlbumRequest.getPlaylist(albumId, audioId, sort, pageSize, 1, callback);
    }

    @Override
    public void destroy() {

    }
}
