<?xml version="1.0" encoding="utf-8"?>
<com.kaolafm.kradio.lib.widget.ratio.RatioConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:canScale="true"
    app:wh_ratio="1:1">

    <!--    必须有一个显示的子view是match，不然会出现设置空隙(offset)不起作用-->

    <com.kaolafm.kradio.lib.widget.square.SquareImageView
        android:id="@+id/iv_cover"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:contentDescription="@null"
        app:canScale="false"
        tools:src="@drawable/online_what_i_like" />

    <View
        android:id="@+id/view_item_home_playing"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/selector_home_broadcast_bg" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/gl_item_guide"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.43" />

    <TextView
        android:id="@+id/tv_item_home_broadcast_name"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:paddingStart="@dimen/x14"
        android:paddingEnd="@dimen/x14"
        android:gravity="center"
        android:lines="2"
        android:maxEms="6"
        android:textColor="@color/category_item_radio_title_text_color"
        android:textSize="@dimen/home_item_broadcast_text_size"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="w,3:7"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="北京音乐广播北京音乐广播" />

</com.kaolafm.kradio.lib.widget.ratio.RatioConstraintLayout>


