package com.kaolafm.kradio.common.widget;


import android.content.Context;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import android.util.AttributeSet;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-04-11 16:52
 ******************************************/

public final class KLRecyclerView extends RecyclerView {

    public KLRecyclerView(Context context) {
        super(context);
        init();
    }

    public KLRecyclerView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public KLRecyclerView(Context context, @Nullable AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        init();
    }

    private void init() {
        setHasFixedSize(true);
    }

    @Override
    protected float getBottomFadingEdgeStrength() {
        return 0;
    }
}
