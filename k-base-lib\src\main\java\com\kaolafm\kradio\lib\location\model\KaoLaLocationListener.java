package com.kaolafm.kradio.lib.location.model;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName KaoLaLocationListener
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/5/26 15:22
 * @Version 1.0
 */
public class KaoLaLocationListener {
    private List<IKaoLaLocation.IKaoLaLocationListener> mListeners = new ArrayList<>();

    public void locationChange(LocationModel location) {
        for (int i = 0; i < mListeners.size(); i++) {
            IKaoLaLocation.IKaoLaLocationListener iKaoLaLocationListener = mListeners.get(i);
            if (iKaoLaLocationListener == null) {
                continue;
            }
            iKaoLaLocationListener.locationChange(location);
        }
    }

    public void addLocationListener(IKaoLaLocation.IKaoLaLocationListener listener) {
        if (listener == null) {
            return;
        }
        if (!mListeners.contains(listener)) {
            mListeners.add(listener);
        }
    }

    public void removeLocationListener(IKaoLaLocation.IKaoLaLocationListener listener) {
        if (listener == null) {
            return;
        }
        mListeners.remove(listener);
    }

}
