package com.kaolafm.kradio.common.widget;

import android.content.Context;
import android.graphics.drawable.ClipDrawable;
import android.os.Handler;
import android.os.Message;
import androidx.annotation.DrawableRes;
import androidx.appcompat.widget.AppCompatImageView;
import android.util.AttributeSet;
import com.kaolafm.kradio.lib.utils.ResUtil;
import java.lang.ref.WeakReference;
import java.util.Timer;
import java.util.TimerTask;

/**
 * <AUTHOR>
 * @date 2022-07-06
 */
public class ProgressImageView extends AppCompatImageView {

    private int resId = 0;
    private static int progress = 0;
    private int totalProgress = 100;
    private long animDuration = 500;// 默认0.5s
    private long animDelay = 500;
    private AnimFinishCallback animFinishCallback;

    // 子线程
    private Handler handler =new  MyHandler(this);
    private static class MyHandler extends Handler
    {
        private WeakReference<ProgressImageView> weakOwner;
        public MyHandler(ProgressImageView view)
        {
            weakOwner = new WeakReference<>(view);
        }
        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case 1:
                    ProgressImageView progressImageView=weakOwner.get();
                    if(progressImageView==null)
                    {
                        return;
                    }
                    progressImageView.setProgress(progress);
                    break;
            }
        }
    }
    public ProgressImageView(Context context) {
        this(context,null);
    }

    public ProgressImageView(Context context, AttributeSet attrs) {
        this(context, attrs,0);
    }

    public ProgressImageView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    public void setProgress(int progress){
        ClipDrawable clipDrawable = (ClipDrawable) getDrawable();
        clipDrawable.setLevel(progress * 100);
        if(progress >= 100){
            if(animFinishCallback != null){
                animFinishCallback.onFinish();
            }
            if(handler!=null){
                handler.removeCallbacksAndMessages(null);
                handler = null;
            }
        }
    }

    public ProgressImageView setImgRes(@DrawableRes int resId){
        this.resId = resId;
        super.setImageDrawable(ResUtil.getDrawable(resId));
        return this;
    }


    public int getImgRes(){
        return this.resId;
    }

    public ProgressImageView startAnim(){
        if(animDuration>0){
            long perAnimDuration = animDuration/totalProgress;
            long perProgress = 100/totalProgress;
            Timer timer = new Timer();
            TimerTask task = new TimerTask() {
                @Override
                public void run() {
                    progress += perProgress;
                    if(handler!=null){
                        Message message = new Message();
                        message.what = 1;
                        handler.sendMessage(message);
                    }
                    if(progress >= 100){
                        timer.cancel();
                    }
                }
            };
            timer.schedule(task,animDelay,perAnimDuration);
        }else {
            setProgress(100);
        }
        return this;
    }

    public ProgressImageView startAnim(long duration){
        if(duration < 0){
            animDuration = 0;
        }else {
            animDuration = duration;
        }
        startAnim();
        return this;
    }

    public ProgressImageView setAnimFinishCallback(AnimFinishCallback animFinishCallback){
        this.animFinishCallback = animFinishCallback;
        return this;
    }

    public interface AnimFinishCallback{
        void onFinish();
    }

}
