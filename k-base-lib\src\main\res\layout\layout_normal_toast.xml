<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:paddingStart="@dimen/x10"
    android:paddingEnd="@dimen/x10"
    android:gravity="center">

    <TextView
        android:id="@+id/tv_toast_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:shadowColor="#BB000000"
        android:shadowRadius="2.75"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size3"
        android:singleLine="true"
        android:lines="1"
        tools:text="我是toast"
        />
</LinearLayout>
