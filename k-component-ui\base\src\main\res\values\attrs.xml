<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="OvalImageView">
        <attr name="rid_type" format="integer" />
        <attr name="circle" format="boolean" />
        <attr name="has_boder" format="boolean" />
        <attr name="color_edge" format="color" />
        <attr name="width_edge" format="dimension" />
        <attr name="oval_radius" format="dimension" />
        <attr name="wRatio" format="integer" />
        <attr name="hRatio" format="integer" />
    </declare-styleable>

    <!-- 自定义圆角布局 ,可以设置圆角，圆形  -->
    <declare-styleable name="RoundCircleImageView">
        <attr name="isCircle" format="boolean" />
        <attr name="round_background_color" format="color" />
        <attr name="round_background_drawable" format="reference" />
        <attr name="round_radius" format="dimension" />
        <attr name="topLeft" format="dimension" />
        <attr name="topRight" format="dimension" />
        <attr name="bottomRight" format="dimension" />
        <attr name="bottomLeft" format="dimension" />
    </declare-styleable>
    <declare-styleable name="KradioTextView">
        <!--    字体粗细，0=normal，0.7=medium    -->
        <attr name="kt_font_weight" format="float|reference" />
        <!--    字体渐变，开始颜色    -->
        <attr name="kt_start_color" format="color|reference" />
        <!--    字体渐变，结束颜色    -->
        <attr name="kt_end_color" format="color|reference" />
        <!--    字体渐变，渐变方向    -->
        <attr name="kt_gradient_angle" format="integer|reference" />
    </declare-styleable>
</resources>