<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/y120"
    android:id="@+id/fl_title"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingStart="@dimen/y20"
    tools:ignore="RtlSymmetry">

    <ImageView
        android:id="@+id/iv_title_back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:background="@drawable/color_main_button_click_selector"
        android:contentDescription="@null"
        android:padding="@dimen/stand_oval_click_button_padding"
        android:scaleType="centerInside"
        android:src="@drawable/left_white_arrow_selector" />

    <TextView
        android:id="@+id/tv_title_center"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size7"
        tools:text="音乐账号-QQ登录" />
</FrameLayout>
