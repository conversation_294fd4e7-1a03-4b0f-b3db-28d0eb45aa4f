package com.kaolafm.kradio.lib.utils;

import android.net.Uri;
import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.kradio.lib.base.flavor.KRadioImageLocalUriInter;
import com.kaolafm.kradio.lib.sdk.utils.PerformanceSettingMananger;
import com.kaolafm.kradio.lib.utils.glide.ImageCacheUtils;
import com.kaolafm.opensdk.api.operation.model.ImageFile;
import com.kaolafm.opensdk.utils.operation.OperationAssister;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/******************************************
 * 类描述： 处理url工具类 类名称：UrlUtil
 *
 * @version: 1.0
 * @author: shaoningYang
 * @time: 2016-7-19 10:24
 ******************************************/

public final class UrlUtil {
    private UrlUtil() {
    }

    public static final String QQ_MUSIC_IMAGE_DEFAULT_SIZE = "120x120";
    public static final String QQ_MUSIC_IMAGE_300_SIZE = "300x300";

    public static final String PIC_100_100 = "/100_100";
    public static final String PIC_250_250 = "/250_250";
    public static final String PIC_340_340 = "/340_340";
    public static final String PIC_720_254 = "/720_254";
    public static final String PIC_550_550 = "/550_550";
    public static final String PIC_1280_1280 = "/1280_1280";
    public static final String PIC_DEFAULT = "/default";

    public static final String WEBP_DETAIL = "?f=webp&resize=p_5,";

    public static final String WEBP_100_100 = "w_100,h_100";
    public static final String WEBP_250_250 = "w_250,h_250";
    public static final String WEBP_340_340 = "w_340,h_340";
    public static final String WEBP_550_550 = "w_549,h_549";
    private static String[] PIC_TYPES = new String[]{PIC_100_100, PIC_250_250, PIC_340_340,
            PIC_720_254,
            PIC_550_550,
            PIC_1280_1280,
            PIC_DEFAULT};

    private static final String JPG_STR = ".JPG";
    private static final String JPEG_STR = ".JPEG";
    private static final String PNG_STR = ".PNG";


    /**
     * 兼容如果不通过车机适配里面，比如在播放条上封面等，传输过来固定的图片尺寸。
     */
    public static String getCustomPicUrl(String type, String url) {
        if (TextUtils.isEmpty(url)) {
            return Constants.BLANK_STR;
        }

        if (TextUtils.isEmpty(type)) {
            return url;
        }
        if (PerformanceSettingMananger.getInstance().getHomeIsJpgOrWebp()) {
            return getDefaultConfigPicUrl(url);
        } else {
            return getJointUrl(type, url);
        }

    }

    /**
     * 从集合中获取对应的图片url地址
     * @param imageFiles ImageFile集合
     */
    private static String getCoverImage(Map<String, ImageFile> imageFiles) {
        if (imageFiles != null) {
            ImageFile imageFile = imageFiles.get("cover");
            if (imageFile != null) {
                return imageFile.getUrl();
            }
        }
        return "";
    }
    public static String getCoverPicUrl(Map<String, ImageFile> map) {
        String img = getCoverImage(map);
        String url = UrlUtil.getDefaultConfigPicUrl(img);
        return url;
    }
    public static String getCarPicUrl(Map<String, ImageFile> map) {
        String img = OperationAssister.getCardImgImage(map);
        String url = UrlUtil.getDefaultConfigPicUrl(img);
        return url;
    }

    public static String getCardPicUrl(Map<String, ImageFile> map) {
        String img = OperationAssister.getCardImage(map);
        String url = UrlUtil.getDefaultConfigPicUrl(img);
        return url;
    }
    public static String getCardBgUrl(Map<String, ImageFile> map) {
        String img = OperationAssister.getCardBgImage(map);
//        String url = UrlUtil.getDefaultConfigPicUrl(img);
        return img;
    }
    public static String getCardLogoUrl(Map<String, ImageFile> map) {
        String img = OperationAssister.getCardLogoImage(map);
        String url = UrlUtil.getDefaultConfigPicUrl(img);
        return url;
    }

    public static String getDefaultConfigPicUrl(String url) {
        if (TextUtils.isEmpty(url)) {
            return Constants.BLANK_STR;
        }
        if (PerformanceSettingMananger.getInstance().getHomeIsJpgOrWebp()) {
            return getDefaultConfigPicUrlforWebp(url);
        } else {
            return getDefaultConfigPicUrlforJPG(url);
        }
    }

    /**
     * 转化成webp格式网址
     */
    public static String getDefaultConfigPicUrlforWebp(String url) {
        if (url == null) {
            return Constants.BLANK_STR;
        }
        url = url + WEBP_DETAIL;

//        boolean canPicConfig = PerformanceSettingMananger.getInstance().isShowConfig();
        String type = "";
//        不依赖于点击5次，出现车机适配界面，默认值是0，高分辨率 250_250
        int homeImageSize = PerformanceSettingMananger.getInstance().getHomeImageSize();
        if (100 == homeImageSize) {
            url = url + WEBP_100_100;
        } else if (PerformanceSettingMananger.LOW_SIZE == homeImageSize) {
            // CPU优化：极致降低图片尺寸 150->100
            url = url + "w_100,h_100";
        } else if (PerformanceSettingMananger.MEDIUM_SIZE == homeImageSize) {
            // CPU优化：极致降低图片尺寸 200->150
            url = url + "w_150,h_150";
        } else if (PerformanceSettingMananger.HIGH_SIZE == homeImageSize) {
            // CPU优化：极致降低图片尺寸 300->200
            url = url + "w_200,h_200";
        } else {
            url = url + "w_" + homeImageSize + ",h_" + homeImageSize;
        }
//        Log.i("kradio.img", "设置图片分辨率:getCustomPicUrl: type=" + type);

        if (TextUtils.isEmpty(type)) {
            return url;
        }
        return url;
    }


    /**
     * 设置指定的图片尺寸
     */
    public static String getAssignPicUrlforWebp(String type, String url) {
        if (url == null) {
            return Constants.BLANK_STR;
        }
        url = url + WEBP_DETAIL + type;
        return url;
    }

    /**
     * 没有转化成webp格式，如果在部分车机上出现问题。
     */
    public static String getDefaultConfigPicUrlforJPG(String url) {
        if (url == null) {
            return Constants.BLANK_STR;
        }
        String type = "";
//        不依赖于点击5次，出现车机适配界面，默认值是0，高分辨率 250_250
        int homeImageSize = PerformanceSettingMananger.getInstance().getHomeImageSize();
        if (100 == homeImageSize) {
            type = PIC_100_100;
        } else if (PerformanceSettingMananger.LOW_SIZE == homeImageSize) {
            type = PIC_250_250;
        } else if (PerformanceSettingMananger.MEDIUM_SIZE == homeImageSize) {
            type = PIC_340_340;
        } else if (PerformanceSettingMananger.HIGH_SIZE == homeImageSize) {
            type = PIC_550_550;
        }
        Log.i("kradio.img", "设置图片分辨率:getCustomPicUrl: type=" + type);

        if (TextUtils.isEmpty(type)) {
            return url;
        }

        return getJointUrl(type, url);
    }

    public static String getDefaultConfigPicUrl(String url, String type) {
        if (url == null) {
            return Constants.BLANK_STR;
        }
        if (TextUtils.isEmpty(type)) {
            return url;
        }

        return getJointUrl(type, url);
    }

    private static String getJointUrl(String type, String url) {
        List<StringUtil.ReplaceRule> rules = new ArrayList<>();
        for (int iType = 0; iType < PIC_TYPES.length; iType++) {
            if (PIC_TYPES[iType].equals(type)) {
                continue;
            }
            rules.add(new StringUtil.ReplaceRule(".*" + PIC_TYPES[iType] + ".jpg$", PIC_TYPES[iType] + ".jpg", type + ".jpg"));
            rules.add(new StringUtil.ReplaceRule(".*" + PIC_TYPES[iType] + ".jpeg$", PIC_TYPES[iType] + ".jpeg", type + ".jpeg"));
            rules.add(new StringUtil.ReplaceRule(".*" + PIC_TYPES[iType] + ".png$", PIC_TYPES[iType] + ".png", type + ".png"));


        }

        return StringUtil.replace(rules, url);
    }

//    public static String get250PicUrl(String url) {
//        return getCustomPicUrl(PIC_250_250, url);
//    }

    /**
     * 获取QQ音乐默认图片大小
     *
     * @param imageSize
     * @param url
     * @return
     */
    public static String getQqMusicImageSize(String imageSize, String url) {
        if (url == null) {
            return null;
        }
        return url.replace(QQ_MUSIC_IMAGE_DEFAULT_SIZE, imageSize);
    }

    /**
     * 拼接URL参数，用于GET请求
     *
     * @param builder 参数对象
     * @param key     参数名
     * @param value   参数值
     */
    public static void appendValue(StringBuilder builder, String key, String value) {
        if (builder == null) {
            return;
        }
        if (TextUtils.isEmpty(key) || TextUtils.isEmpty(value)) {
            return;
        }
        key = Uri.encode(key, "UTF-8");
        value = Uri.encode(value, "UTF-8");
        builder.append(key).append("=").append(value).append("&");
    }

    private static KRadioImageLocalUriInter mKRadioImageLocalUriInter = ClazzImplUtil.getInter("KRadioImageLocalUriImpl");

    public static String getLocalPicUri(String url) {
        if (TextUtils.isEmpty(url)) {
            return null;
        }
        // 解决http://redmine.itings.cn/issues/40770问题
//        String picUrl = UrlUtil.getDefaultConfigPicUrl(url);
        String localPath = getLocalPicUriByCustomUrl(url);
        File file = new File(localPath);
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001210372826?userId=1881599问题
        if (!file.exists()) {
            String tempPicUrl = getDefaultConfigPicUrl(url, PIC_250_250);
            localPath = getLocalPicUriByCustomUrl(tempPicUrl);
        }
        return localPath;
    }

    private static String getLocalPicUriByCustomUrl(String url) {
        String localPicUrl = null;
        try {
            localPicUrl = ImageCacheUtils.getCacheFile2(url);
            Log.i("kradio.client", "getLocalPicUri: picUrl=" + url);
            Log.i("kradio.client", "              : localPicUrl=" + localPicUrl);
        } catch (Exception e) {
            Log.i("kradio.client", "getLocalPicUri: error=" + e);
        }
        if (mKRadioImageLocalUriInter != null) {
            localPicUrl = mKRadioImageLocalUriInter.getLocalImageUri(localPicUrl);
        }
        return localPicUrl;
    }
}