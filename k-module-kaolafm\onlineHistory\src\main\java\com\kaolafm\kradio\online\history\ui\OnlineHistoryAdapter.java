package com.kaolafm.kradio.online.history.ui;

import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewGroup.LayoutParams;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.kaolafm.kradio.common.bean.HistoryItem;
import com.kaolafm.kradio.common.widget.KradioTextView;
import com.kaolafm.kradio.basedb.bean.HeadTitleItem;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.base.ui.online.BaseViewPagerLazyFragment;
import com.kaolafm.kradio.lib.common.ResType;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.UrlUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.AlbumPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.OneKeyPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.report.event.LoginReportEvent;


/**
 * <AUTHOR> Yan
 * @date 2020/8/19
 */
public class OnlineHistoryAdapter extends BaseAdapter<HistoryItem> {

    private Context mContext;

    public static final int HEAD_UNLOGIN_TIP = 151;

//    public static final int HISTORY_COUNT_TITLE = 152;

    private String mCurrentPlayingId = PlayerManager.getInstance().getCurPlayItem().getRadioId();

    private BaseViewPagerLazyFragment mFragment;

    public OnlineHistoryAdapter(BaseViewPagerLazyFragment fragment) {
        mContext = fragment.getContext();
        mFragment = fragment;
    }

    @Override
    protected BaseHolder<HistoryItem> getViewHolder(ViewGroup parent, int viewType) {
        if (viewType == HEAD_UNLOGIN_TIP) {
            return new HeadHolder(inflate(parent, R.layout.online_item_history_head, viewType));
        }
//        else if (viewType == HISTORY_COUNT_TITLE) {
//            return new CountTitleHolder(inflate(parent, R.layout.online_item_history_count, viewType));
//        }
        return new HistoryHolder(inflate(parent, R.layout.online_item_user_tab_base, viewType));
    }

    /**
     * 设置播放状态
     */
    public void setPlaying(String playingId) {
        for (int i = 0; i < getItemCount(); i++) {
            HistoryItem itemBean = mDataList.get(i);
            //ID一样就显示播放状态
            if (TextUtils.equals(String.valueOf(itemBean.getRadioId()), playingId)) {
                itemBean.setPlaying(true);
                notifyItemChanged(i);
            }
            if (TextUtils.equals(String.valueOf(itemBean.getRadioId()), mCurrentPlayingId)) {
                itemBean.setPlaying(false);
                notifyItemChanged(i);
            }
        }
        mCurrentPlayingId = playingId;
    }

    @Override
    public int getItemViewType(int position) {
        return getItemData(position).getTypeId();
    }

    class HistoryHolder extends BaseHolder<HistoryItem> {

        View backgroundIv;
        ImageView ivPlayCover;
        KradioTextView userTabTitle;
        TextView userTabContent;
        KradioTextView userTabDesc;
        View rootView;
        ImageView ivPurchaseType;

        public HistoryHolder(View itemView) {
            super(itemView);
            backgroundIv = itemView.findViewById(R.id.backgroundIv);
            ivPlayCover = itemView.findViewById(R.id.iv_play_cover);
            userTabTitle = itemView.findViewById(R.id.user_tab_title);
            userTabContent = itemView.findViewById(R.id.user_tab_content);
            userTabDesc = itemView.findViewById(R.id.user_tab_desc);
            rootView = itemView.findViewById(R.id.rootView);
            ivPurchaseType = itemView.findViewById(R.id.iv_purchase_type);
        }

        @Override
        public void setupData(HistoryItem historyItem, int position) {
            PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
            String curPlayRadioId = "";
            if (playItem != null) {
                curPlayRadioId = playItem.getRadioId();
//                if (playItem instanceof OneKeyPlayItem) {
//                    curPlayRadioId = playItem.getAlbumId();
//                }
            }

            boolean offline = historyItem.isOffline();
            if(offline){
                itemView.setAlpha(0.6f);
            }
            boolean playing = String.valueOf(curPlayRadioId).equals(historyItem.getRadioId()) || historyItem.isPlaying();
            backgroundIv.setBackgroundResource(playing?R.drawable.online_ring_selected:R.drawable.online_ring_unselected);
            userTabTitle.setSelected(playing);
            userTabTitle.setGradientColor(ResUtil.getColor(R.color.online_history_item_title_text_start), ResUtil.getColor(R.color.online_history_item_title_text_end), 270);
            userTabTitle.setText(StringUtil.getMaxString(historyItem.getRadioTitle(), 6));
            userTabContent.setText(historyItem.getFreq()==null?"":historyItem.getFreq());
            userTabDesc.setGradientColor(ResUtil.getColor(R.color.online_history_item_title_text_start), ResUtil.getColor(R.color.online_history_item_title_text_end), 270);
            userTabDesc.setText("播放量"+StringUtil.getTenThousandNumString(historyItem.getListenCount()));
            ImageLoader.getInstance().displayImage(mContext, UrlUtil.getCustomPicUrl(UrlUtil.PIC_250_250, historyItem.getPicUrl()), ivPlayCover);
            if (historyItem.getVip() == 1) {
                ivPurchaseType.setBackground(mContext.getResources().getDrawable(R.drawable.online_ring_ic_vip));
            } else if (historyItem.getFine() == 1) {
                ivPurchaseType.setBackground(mContext.getResources().getDrawable(R.drawable.online_ring_ic_supreme));
            }else if(Integer.parseInt(historyItem.getType()) == ResType.BROADCAST_TYPE ||
                    Integer.parseInt(historyItem.getType()) == ResType.TV_TYPE){
                ivPurchaseType.setBackground(mContext.getResources().getDrawable(R.drawable.online_ring_ic_live));
            }else {
                ivPurchaseType.setBackground(null);
            }
            if(playing){
                backgroundIv.bringToFront();
            }else {
                ivPurchaseType.bringToFront();
            }
            historyItem.setPlaying(playing);


//            PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
//            String curPlayRadioId = "";
//            if (playItem != null) {
//                curPlayRadioId = playItem.getRadioId();
////                if (playItem instanceof OneKeyPlayItem) {
////                    curPlayRadioId = playItem.getAlbumId();
////                }
//            }
//
//            boolean isPlaying = String.valueOf(curPlayRadioId).equals(historyItem.getRadioId()) || historyItem.isPlaying();
//            String subtitle = createSubTitle(historyItem, isPlaying);
//            mTvHistoryTitle.setText(historyItem.getRadioTitle());
////            mTvHistoryContent.setText(subtitle);
//            ImageLoader.getInstance().displayImage(mContext,
//                    UrlUtil.getCustomPicUrl(UrlUtil.PIC_250_250, historyItem.getPicUrl()), mIvPlayCover);
//            rootView.setActivated(isPlaying);
//            mTvHistoryTitle.setSelected(isPlaying);
////            mTvHistoryContent.setSelected(isPlaying);
//
//            boolean offline = historyItem.isOffline();
////            mCoverOffline.setVisibility(offline ? View.VISIBLE : View.GONE);
//
//            historyItem.setPlaying(isPlaying);
////            OritationViewUtils.handleOrientationTextSize(mTvHistoryTitle, mTvHistoryTitle,
////                    R.dimen.text_size4,
////                    R.dimen.text_size4);
////            OritationViewUtils.handleOrientationTextSize(mTvHistoryContent, mTvHistoryContent,
////                    R.dimen.text_size2,
////                    R.dimen.text_size2);
//
//            VipCornerUtil.setVipCorner(mVipIcon, historyItem.getVip(),
//                    historyItem.getFine(), true);
        }

        protected String createSubTitle(HistoryItem item, boolean isCurrentAudio) {
            String result = ResUtil.getString(R.string.unknown);
            if (item != null) {
                int typeInt = PlayerConstants.RESOURCES_TYPE_INVALID;
                try {
                    typeInt = Integer.parseInt(item.getType());
                } catch (Exception e) {

                }

                switch (typeInt) {
                    case PlayerConstants.RESOURCES_TYPE_RADIO:
                        result = ResUtil.getString(R.string.pgc);
                        break;
                    case PlayerConstants.RESOURCES_TYPE_BROADCAST:
                        result = ResUtil.getString(R.string.broadcast);
                        break;
                    case PlayerConstants.RESOURCES_TYPE_ONE_KEY_LISTENER_TYPE:
                    case PlayerConstants.RESOURCES_TYPE_PURCHASE_ONE_KEY_LISTENER_TYPE: {
                        String orderStr = Constants.BLANK_STR;
                        if (item.getOrderNum() > 0) {
                            orderStr = String.format(ResUtil.getString(R.string.audio_num), item.getOrderNum());
                        }
                        if (isCurrentAudio) {
                            PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
                            if (playItem instanceof  OneKeyPlayItem){
                                OneKeyPlayItem oneKeyPlayItem = (OneKeyPlayItem) playItem;

                                orderStr = String.format(ResUtil.getString(R.string.audio_num),
                                        oneKeyPlayItem.getInfoData().getOrderNum());
                                result = StringUtil.join(ResUtil.getString(R.string.is_playing),
                                        orderStr,
                                        oneKeyPlayItem.getInfoData().getTitle());
                            }
                        }else{
                            result = StringUtil.join(
                                    ResUtil.getString(R.string.last_play),
                                    orderStr,
                                    item.getAudioTitle());
                        }
                    }
                    break;
                    case PlayerConstants.RESOURCES_TYPE_ALBUM:
                        String orderStr = Constants.BLANK_STR;
                        if (item.getOrderNum() > 0) {
                            orderStr = String.format(ResUtil.getString(R.string.audio_num), item.getOrderNum());
                        }
                        if (isCurrentAudio) {
                            PlayItem currPlayItem = PlayerManager.getInstance().getCurPlayItem();
                            if (currPlayItem instanceof AlbumPlayItem) {
                                orderStr = String.format(ResUtil.getString(R.string.audio_num),
                                        ((AlbumPlayItem) currPlayItem).getInfoData().getOrderNum());
                                result = StringUtil.join(ResUtil.getString(R.string.is_playing),
                                        orderStr,
                                        ((AlbumPlayItem) currPlayItem).getInfoData().getTitle());
                            }

                        } else {
                            result = StringUtil.join(
                                    ResUtil.getString(R.string.last_play),
                                    orderStr,
                                    item.getAudioTitle());
                        }
                        break;
                    default:
                        result = ResUtil.getString(R.string.unknown);
                        break;
                }
            }

            return result;
        }
    }

    class HeadHolder extends BaseHolder<HistoryItem> {

        View root;
        LinearLayout llHistoryLogin;
        KradioTextView tvHistoryLogin;

        HeadHolder(View itemView) {
            super(itemView);

            root = itemView.findViewById(R.id.root);
            llHistoryLogin = itemView.findViewById(R.id.ll_history_login);
            tvHistoryLogin = itemView.findViewById(R.id.tv_history_login);

            itemView.setEnabled(false);
            tvHistoryLogin.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Bundle bundle=new Bundle();
                    bundle.putString("type", LoginReportEvent.ONLINE_REMARKS1_HISTORY_PAGE);
                    RouterManager.getInstance().jumpPage(RouterConstance.ACTIVITY_URL_LOGIN,bundle);
                }
            });
        }

        @Override
        public void setupData(HistoryItem historyItem, int position) {
            if (historyItem instanceof HeadTitleItem) {
                int screenWidth = (((OnlineHistoryFragment)mFragment).mRvHistoryList).getMeasuredWidth();

                int spec = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED);
                llHistoryLogin.measure(spec, spec);
                int unLoginWidth = llHistoryLogin.getMeasuredWidth();

                int paddingLeft = screenWidth/2-unLoginWidth/2;
                int paddingRight = ResUtil.getDimen(R.dimen.online_history_un_login_item_padding_right);

                LayoutParams lP = itemView.getLayoutParams();
                lP.width = paddingLeft + unLoginWidth + paddingRight;
                itemView.setLayoutParams(lP);
                itemView.setPadding(paddingLeft, 0, paddingRight, 0);
            }

        }
    }
}
