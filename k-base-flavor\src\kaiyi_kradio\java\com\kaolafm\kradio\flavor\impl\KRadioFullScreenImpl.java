package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.os.Build;
import android.util.Log;
import android.view.View;

import com.kaolafm.kradio.lib.base.flavor.KRadioFullScreenInter;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-07-25 12:33
 ******************************************/
public final class KRadioFullScreenImpl implements KRadioFullScreenInter {

    private View mDecorView;

    private static final String TAG = "KRadioFullScreenImpl";

    @Override
    public boolean initFullScreen(Object... args) {
        Log.i(TAG,"initFullScreen :"+mDecorView);
        if (mDecorView == null) {
            Activity activity = (Activity) args[0];
            mDecorView = activity.getWindow().getDecorView();
            //解决https://app.huoban.com/tables/2100000007530121/items/2300001139583791?userId=1229522问题
            mDecorView.setOnSystemUiVisibilityChangeListener(new View.OnSystemUiVisibilityChangeListener() {
                @Override
                public void onSystemUiVisibilityChange(int visibility) {
                    Log.i(TAG,"onSystemUiVisibilityChange visibility:"+visibility);
                    changeDecorViewProp();
                }
            });
        }
        changeDecorViewProp();
        return true;
    }

    @Override
    public boolean hideNavBarOnResume(Object... args) {
        boolean flag = initFullScreen(args);
        return flag;
    }

    private void changeDecorViewProp() {
        Log.i(TAG,"changeDecorViewProp :"+Build.VERSION.SDK_INT+" mDecorView:"+mDecorView);
        if (Build.VERSION.SDK_INT > 11 && Build.VERSION.SDK_INT < 19) { // lower api
            mDecorView.setSystemUiVisibility(View.GONE);
        } else if (Build.VERSION.SDK_INT >= 19) {
            int uiOptions = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                    | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY;
            mDecorView.setSystemUiVisibility(uiOptions);
        }
    }
}
