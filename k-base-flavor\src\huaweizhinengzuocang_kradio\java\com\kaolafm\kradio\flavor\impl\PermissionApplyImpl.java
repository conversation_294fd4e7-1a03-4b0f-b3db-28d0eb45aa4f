package com.kaolafm.kradio.flavor.impl;

import android.Manifest;

import com.kaolafm.kradio.lib.base.flavor.PermissionApplyInter;

public class PermissionApplyImpl implements PermissionApplyInter {


    @Override
    public String[] getPermission() {
        String[] perms = {
                Manifest.permission.WRITE_EXTERNAL_STORAGE,
//                Manifest.permission.READ_PHONE_STATE,
                Manifest.permission.RECORD_AUDIO,
                Manifest.permission.ACCESS_FINE_LOCATION ,
                Manifest.permission.ACCESS_COARSE_LOCATION,
        };
        return perms;
    }
}
