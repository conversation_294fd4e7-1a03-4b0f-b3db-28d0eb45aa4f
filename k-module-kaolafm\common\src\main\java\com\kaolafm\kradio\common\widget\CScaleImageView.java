package com.kaolafm.kradio.common.widget;

import android.content.Context;
import androidx.appcompat.widget.AppCompatImageView;
import android.util.AttributeSet;
import android.view.MotionEvent;

import skin.support.widget.SkinCompatImageView;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-04-18 14:54
 ******************************************/

public final class CScaleImageView extends SkinCompatImageView implements CScaleWidgetInter{
    private boolean canMakeAnimation = true;

    private MakeAnimationImpl mMakeAnimationImpl;

    public CScaleImageView(Context context) {
        this(context, null);
    }

    public CScaleImageView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CScaleImageView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (canMakeAnimation) {
            int action = event.getAction();
            switch (action) {
                case MotionEvent.ACTION_DOWN:
                    if (mMakeAnimationImpl != null) {
                        mMakeAnimationImpl.makeAnimationPress(this);
                    }
                    break;
                case MotionEvent.ACTION_OUTSIDE:
                case MotionEvent.ACTION_CANCEL:
                case MotionEvent.ACTION_UP:
                    if (mMakeAnimationImpl != null) {
                        mMakeAnimationImpl.makeAnimationRelease(this);
                    }
                    break;
                default:
                    break;
            }
        }
        return super.onTouchEvent(event);
    }

    @Override
    public void setMakeAnimationImpl(MakeAnimationImpl makeAnimationImpl) {
        this.mMakeAnimationImpl = makeAnimationImpl;
    }
}
