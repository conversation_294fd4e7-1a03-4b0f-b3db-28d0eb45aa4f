package com.kaolafm.kradio.flavor.aop;

import android.text.TextUtils;

import com.kaolafm.kradio.setting.SettingItem;


import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;

import java.util.Iterator;
import java.util.List;

@Aspect
public class ToneQualityAop {
//    @Before("execution(* com.kaolafm.kradio.k_kaolafm.home.HorizontalHomePlayerFragment.onAttach(..))")
//    public void checkAuthstart(JoinPoint point) throws Throwable {
//        Log.i("zsjxxxx", "checkAuthstart: ToneQualityHelper.getInstance().getToneQuality() = "+ToneQualityHelper.getInstance().getToneQuality() );
//        if (ToneQualityHelper.getInstance().getToneQuality() != ToneQuality.LOW_TONE_QUALITY) {
//            ToneQualityHelper.getInstance().setToneQuality(ToneQuality.LOW_TONE_QUALITY);
//        }
//    }

    @After("execution(* PlayerManager.setPlayUrl(..))")
    public void playerManagerSetPlayUrl(JoinPoint point) throws Throwable {
        PlayItem playItem = (PlayItem) point.getArgs()[0];
        //129 音乐曲库， 141 音乐
        String tempUrl = null;
        int ctgId = (int) playItem.getCategoryId();
        if (ctgId == 141 || ctgId == 129) {
            tempUrl = playItem.getAacPlayUrl64();
        } else {
            tempUrl = playItem.getAacPlayUrl32();
        }
        if (!TextUtils.isEmpty(tempUrl)) {
            playItem.setPlayUrl(tempUrl);
        }
    }

    @AfterReturning(value = "execution(* com.kaolafm.kradio.setting.SettingModel.getItemList(..))", returning = "itemList")
    public void excludeChangeSkin(List<SettingItem> itemList, JoinPoint point) throws Throwable {
        if (itemList != null) {
            Iterator<SettingItem> iterator = itemList.iterator();
            while (iterator.hasNext()){
                if (iterator.next().getFunctionId()==SettingItem.ITEM_FUNCTION_PLAY_QUALITY){
                    iterator.remove();
                }
            }
        }
    }
}


