<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.kaolafm.kradio.flavor"
    android:sharedUserId="android.uid.system">

    <!--monkey测试测试出来的缺少以下权限，说是系统方处理，此处也先添加上. BYD issue T201981-->
    <uses-permission android:name="android.permission.BYDAUTO_AUDIO_SET"/>
    <uses-permission android:name="android.permission.BYDAUTO_BODYWORK_COMMON" />
    <uses-permission android:name="android.permission.BYDAUTO_BODYWORK_GET" />
    <uses-permission android:name="android.permission.BYDAUTO_INSTRUMENT_COMMON" />
    <uses-permission android:name="android.permission.BYDAUTO_INSTRUMENT_SET" />
    <uses-permission android:name="android.permission.BYDAUTO_AUDIO_GET" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <uses-permission
        android:name="com.byd.mediacenter.STARTSERVER"
        android:protectionLevel="signatureOrSystem" />
    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" />
    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS" />
    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" />


    <!--    <application android:persistent="true">-->
    <application>

        <!--<activity
            android:name="com.kaolafm.auto.home.MainActivity"
            android:configChanges="orientation|screenSize|keyboardHidden|locale|layoutDirection|keyboard|uiMode"
            android:exported="true"
            android:screenOrientation="unspecified"
            android:theme="@style/AppThemeCompat.splash">

            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.MONKEY" />
            </intent-filter>
            <meta-data
                android:name="distractionOptimized"
                android:value="true" />
        </activity>

        <activity-alias
            android:name="com.main.activity.alias"
            android:targetActivity="com.kaolafm.auto.home.HubActivity"
            tools:node="remove" />-->

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.kaolafm.kradio.k_radio_horizontal.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>


        <service
            android:name="com.kaolafm.kradio.service.AutoPlayService"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com.kaolafm.auto.flavor.service.AUTO_PLAY" />
            </intent-filter>
        </service>

        <receiver
            android:name="com.kaolafm.kradio.receiver.BYDAutoPlayReceiver"
            android:exported="true">
            <intent-filter android:priority="1000">
                <action android:name="byd.intent.action.AUTO_PLAY" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.kaolafm.kradio.receiver.BydSpeechCtlAuthReceiver"
            android:exported="true">
            <intent-filter android:priority="1000">
                <action android:name="com.byd.action.FUNCTION_UPDATE_RESULT”" />
            </intent-filter>
        </receiver>

        <!--        <service-->
        <!--            android:name="com.kaolafm.kradio.service.BYDWidgetService"-->
        <!--            android:enabled="true"-->
        <!--            android:exported="true"/>-->

        <service
            android:name="cmgyunting.vehicleplayer.cnr.YunTingWidgetService"
            android:enabled="true"
            android:exported="true" />

        <service
            android:name="cmgyunting.vehicleplayer.cnr.widget.YunTingDophoinWidgetService"
            android:enabled="true"
            android:exported="true" />

        <!--        <service android:name="com.kaolafm.kradio.service.HanWidgetService"-->
        <!--            android:enabled="true"-->
        <!--            android:exported="true"/>-->

        <!--暂时不需要适配byd30UI-->
        <!--        <receiver android:name="com.kaolafm.auto.appwidget.KLAppWidget">-->
        <!--            <intent-filter android:priority="1000">-->
        <!--                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />-->
        <!--            </intent-filter>-->
        <!--            <intent-filter>-->
        <!--                <action android:name="com.kaolafm.auto.home.appExit.action" />-->
        <!--            </intent-filter>-->

        <!--            <meta-data-->
        <!--                android:name="android.appwidget.provider"-->
        <!--                android:resource="@xml/byd_widget_info" />-->
        <!--        </receiver>-->

        <!-- byd3.5UI版本不需要开机自启-->
                <receiver android:name="com.kaolafm.kradio.receiver.BootBroadcastReceiver">
                    <intent-filter android:priority="1000">
                        <action android:name="android.intent.action.BOOT_COMPLETED" />
                    </intent-filter>
                </receiver>

        <receiver android:name="com.kaolafm.kradio.receiver.ShutdownBroadcastReceiver">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.ACTION_SHUTDOWN" />
            </intent-filter>
        </receiver>

        <receiver android:name="com.kaolafm.kradio.flavor.impl.KillBroadcastReceiver">
            <intent-filter android:priority="1000">
                <action android:name="byd.intent.action.KILL_EDOG_CAR" />
                <action android:name="byd.intent.action.KILL_YUNTING" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.kaolafm.kradio.receiver.BYDMediaModeReceiver"
            android:exported="true">
            <intent-filter android:priority="1000">
                <action android:name="byd.intent.action.MEDIA_MODE" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.kaolafm.kradio.receiver.BYDMediaButtonReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="byd.intent.action.MEDIA_BUTTON" />
            </intent-filter>
        </receiver>

        <!-- 比亚迪模式切换 -->
        <!--        <receiver android:name="com.kaolafm.kradio.receiver.BydThemeBroadcastReceiver">-->
        <!--            <intent-filter android:priority="1000">-->
        <!--                <action android:name="com.byd.changebydtheme" />-->
        <!--            </intent-filter>-->
        <!--        </receiver>-->

        <receiver
            android:name="cmgyunting.vehicleplayer.cnr.YunBydWidget"
            android:exported="true">
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/byd_35widget_config" />

            <intent-filter android:priority="1000">
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
                <action android:name="android.appwidget.action.APPWIDGET_RESTORED" />
                <action android:name="android.appwidget.action.APPWIDGET_DELETED" />
                <action android:name="com.byd.changebydtheme" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.kaolafm.auto.home.appExit.action" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="cmgyunting.vehicleplayer.cnr.widget.YunBydDophinWidget"
            android:exported="true">
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/byd_dolphin_35widget_config" />

            <intent-filter android:priority="1000">
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.kaolafm.auto.home.appExit.action" />
            </intent-filter>
        </receiver>

        <receiver android:name="com.kaolafm.kradio.receiver.BydSpeechCtlReceiver">
            <intent-filter android:priority="1000">
                <action android:name="com.byd.action.FUNCTION_UPDATE_RESULT" />
            </intent-filter>
        </receiver>

            <!--改用动态广播-->
<!--        <receiver android:name="com.kaolafm.kradio.receiver.BydAccountStateReceiver">-->
<!--            <intent-filter android:priority="1000">-->
<!--                <action android:name="com.byd.action.byd_account_state_change" />-->
<!--            </intent-filter>-->
<!--        </receiver>-->

        <receiver android:name="com.kaolafm.kradio.receiver.BydThirdAccountBindReceiver">
            <intent-filter android:priority="1000">
                <action android:name="com.byd.action.query_tri_part_account_state" />
            </intent-filter>
        </receiver>

        <receiver android:name="com.kaolafm.kradio.receiver.BydThirdLaunchLogin">
            <intent-filter android:priority="1000">
                <action android:name="com.byd.action.byd_account_go_tripart_login_page" />
            </intent-filter>
        </receiver>
    </application>

</manifest>


