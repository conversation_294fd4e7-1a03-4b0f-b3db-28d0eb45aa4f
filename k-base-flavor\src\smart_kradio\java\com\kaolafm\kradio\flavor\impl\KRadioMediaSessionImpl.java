package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.app.ActivityManager;
import android.app.Application;
import android.app.PendingIntent;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.media.AudioManager;
import android.net.Uri;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import androidx.annotation.RequiresApi;
import android.util.Log;

import com.ecarx.sdk.ECarXAPIBase;
import com.ecarx.sdk.mediacenter.AbstractVideoClient;
import com.ecarx.sdk.mediacenter.ContentInfo;
import com.ecarx.sdk.mediacenter.IMusicRecoveryCallback;
import com.ecarx.sdk.mediacenter.MediaCenterAPI;
import com.ecarx.sdk.mediacenter.MediaInfo;
import com.ecarx.sdk.mediacenter.MediaListInfo;
import com.ecarx.sdk.mediacenter.MediaListsInfo;
import com.ecarx.sdk.mediacenter.MusicClient;
import com.ecarx.sdk.mediacenter.MusicPlaybackInfo;
import com.ecarx.sdk.mediacenter.RecommendInfo;
import com.ecarx.sdk.mediacenter.RecoveryIntentType;
import com.ecarx.sdk.mediacenter.control.IMediaControlClientAPI;
import com.ecarx.sdk.mediacenter.control.IMediaControllerAPI;
import com.ecarx.sdk.mediacenter.control.MediaControlClient;
import com.ecarx.sdk.mediacenter.control.MediaController;
import com.ecarx.sdk.mediacenter.control.bean.Media;
import com.ecarx.sdk.mediacenter.exception.MediaCenterException;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.common.CP;
import com.kaolafm.kradio.common.ErrorInfo;
import com.kaolafm.kradio.common.ResultCallback;
import com.kaolafm.kradio.common.SubscribeData;
import com.kaolafm.kradio.common.helper.SubscribeHelper;
import com.kaolafm.kradio.constant.UserComponentConst;
import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.k_kaolafm.home.player.PlayerHelper;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.base.flavor.KRadioMediaSessionInter;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.ComponentUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.IPlayerStateListener;
import com.kaolafm.opensdk.player.logic.model.PlayItemType;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.report.event.SubscibeReportEvent;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

import ecarx.xsf.mediacenter.bean.IMediaContentType;

import static com.ecarx.sdk.mediacenter.AbstractMusicPlaybackInfo.PLAYBACK_STATUS_INTERRUPT;
import static com.ecarx.sdk.mediacenter.AbstractMusicPlaybackInfo.PLAYBACK_STATUS_PAUSED;
import static com.ecarx.sdk.mediacenter.AbstractMusicPlaybackInfo.PLAYBACK_STATUS_PLAYING;
import static com.ecarx.sdk.mediacenter.AbstractMusicPlaybackInfo.PLAYBACK_STATUS_PREPARE;
import static com.ecarx.sdk.mediacenter.AbstractVideoClient.TYPE_COLLECTION_MUSIC;
import static com.ecarx.sdk.mediacenter.SourceType.SOURCE_TYPE_ONLINE;
import static com.kaolafm.kradio.lib.utils.UrlUtil.PIC_100_100;
import static com.kaolafm.kradio.lib.utils.UrlUtil.PIC_250_250;
import static com.kaolafm.kradio.lib.utils.UrlUtil.getDefaultConfigPicUrl;
import static com.kaolafm.opensdk.player.logic.util.PlayerConstants.RESOURCES_TYPE_BROADCAST;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: JackLee
 * @time: 2021-3-25 13:51
 ******************************************/
public final class KRadioMediaSessionImpl implements KRadioMediaSessionInter {
    private static final String TAG = "KRadioMediaSessionImpl";
    private MyOnAudioFocusChangeInter myOnAudioFocusChangeInter;
    private MediaSessionIPlayerStateListener mMediaSessionIPlayerStateListener;
//    private MyIPlayChangedListener myIPlayChangedListener;

    private MyMusicClient myMusicClient;

    private boolean isRelease;

    private Object mToken;

    private static String mAppName;
    private static String mAppIcon;
    private Object mControlClientToken;
    private IMediaControlClientAPI mControlClientAPI;
    private static String currentRequestClient;

    private static MusicPlaybackInfo mMusicPlaybackInfo;

    public KRadioMediaSessionImpl() {
        initAppName();
        myOnAudioFocusChangeInter = new MyOnAudioFocusChangeInter(this);
        mMediaSessionIPlayerStateListener = new MediaSessionIPlayerStateListener(this);
        PlayerManager.getInstance().addPlayControlStateCallback(mMediaSessionIPlayerStateListener);
        PlayerManager.getInstance().addAudioFocusListener(myOnAudioFocusChangeInter);

        //tts test
//        new Handler().postDelayed(() -> {
//            if (myMusicClient == null) {
//                myMusicClient = new MyMusicClient();
//            }
//            myMusicClient.onPause();
//            myMusicClient.onCollect(1, true);
//            myMusicClient.onExit();
//        }, 10000);
    }

    private void initAppName() {
        Context context = AppDelegate.getInstance().getContext();
        try {
            PackageManager packageManager = context.getPackageManager();
            PackageInfo packageInfo = packageManager.getPackageInfo(
                    context.getPackageName(), 0);
            int labelRes = packageInfo.applicationInfo.labelRes;
            mAppName = context.getResources().getString(labelRes);
            mAppIcon = getAppIcon();
            Log.i(TAG, "initAppName-------->" + mAppName);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void registerMediaSession(Object... args) {
        Context context = (Context) args[0];
        MediaCenterAPI mediaCenterAPI = MediaCenterAPI.get(context);
        if (mediaCenterAPI == null) {
            Log.i(TAG, "mediaCenterAPI ::" + mediaCenterAPI);
            return;
        }
        mediaCenterAPI.init(context, success -> {
            Log.i(TAG, "initMediaCenterAPI-------->" + success);
            if (success) {
                if (myMusicClient == null) {
                    myMusicClient = new MyMusicClient();
                    initToken();
                }
                Log.i(TAG, "registerMediaSession-------->mToken = " + mToken);

                if (isTokenAvailable()) {
                    mediaCenterAPI.requestPlay(mToken);
                    mediaCenterAPI.updateCurrentSourceType(mToken, SOURCE_TYPE_ONLINE);
                    // 注册保持能力恢复数据的业务处理。
                    try {
                        mediaCenterAPI.registerMusicRecoveryIntent(mToken, RecoveryIntentType.INTENT_TYPE_SERVICE, getIntent());
                        mediaCenterAPI.getRecoveryMediaList(mToken);
                        mediaCenterAPI.getRecoveryMusicPlaybackInfo(mToken);
                        mediaCenterAPI.setMusicRecoveryCallback(mToken, new IMusicRecoveryCallback() {
                            @Override
                            public void onGetMediaList(MediaListInfo mediaListInfo) {
                                if (mediaListInfo != null) {
                                    Log.i(TAG, "title:" + mediaListInfo.getTitle());
                                }
                            }

                            @Override
                            public void onGetMusicPlaybackInfo(MusicPlaybackInfo musicPlaybackInfo) {
                                if (musicPlaybackInfo != null) {
                                    Log.i(TAG, "title:" + musicPlaybackInfo.getTitle());
                                }
                            }
                        });
                        mediaCenterAPI.onMusicRecoveryComplete(mToken);

                        //-------------------------------------
                        //APP控制
                        // 获取媒体被控制端API实例
                        mControlClientAPI = mediaCenterAPI.getMediaControlClientApi();
                        MediaControlClient mMediaControlClient = new CustomMediaControlClient();
                        // 注册获取被控制端Token（后续所有IMediaControlClientAPI的接口需要传递此token）
                        mControlClientToken = mControlClientAPI.register(context.getPackageName(), mMediaControlClient);
                        // 云听申请被控制权（只有申请成功后，才能被其它App控制,控制端即VR、widget、方控等）
                        mControlClientAPI.requestControlled(mControlClientToken);

                        //控制端
                        // 获取控制端API实例
                        IMediaControllerAPI mControllerAPI = mediaCenterAPI.getMediaControllerApi();
                        // 注册获取App控制Token（后续所有IMediaControllerAPI的接口需要传递此token）
                        Object mControllerToken = mControllerAPI.register(context.getPackageName(), mMediaController);
                        // 申请控制权（只有申请成功后，才能控制媒体端）
                        mControllerAPI.requestControl(mControllerToken);

                    } catch (Exception e) {
                        e.printStackTrace();
                    }

//            resetMediaSessionResource();
                }
                Log.i(TAG, "registerMediaSession-------->updateCurrentSourceType = " + SOURCE_TYPE_ONLINE);
            }
        });

    }

    @Override
    public void unregisterMediaSession(Object... args) {
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001270547010?userId=1229522问题
        updatePausedStatus();
        Log.i(TAG, "unregisterMediaSession-------->" + myMediaListInfo);
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001297677193?userId=1229522问题
        if (myMediaListInfo != null) {
            myMediaListInfo.setPlayItems(null);
        }
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001336934199?userId=1229522问题
//        unregisterToken();
        Context context = (Context) args[0];
        try {
            if (mToken != null) {
                MediaCenterAPI.get(context).unRegisterMusicRecoveryIntent(mToken);
            }
            if (mControlClientToken != null && mControlClientAPI != null) {
                mControlClientAPI.unregister(mControlClientToken);
            }
        } catch (MediaCenterException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void release(Object... args) {
        if (isRelease) {
            return;
        }

        PlayerManager.getInstance().removePlayControlStateCallback(mMediaSessionIPlayerStateListener);

        isRelease = true;
        PlayerManager.getInstance().removeAudioFocusListener(myOnAudioFocusChangeInter);
        resetMediaSessionResource();
    }

//    private void unregisterToken() {
//        if (mToken != null) {
//            MediaCenterAPI mediaCenterAPI = MediaCenterAPI.get(AppDelegate.getInstance().getContext());
//            mediaCenterAPI.unregister(mToken);
//            myMusicClient = null;
//        }
//    }

    private void resetMediaSessionResource() {
        MediaCenterAPI mediaCenterAPI = MediaCenterAPI.get(AppDelegate.getInstance().getContext());
        if (mediaCenterAPI != null && isTokenAvailable()) {
            mediaCenterAPI.updateCurrentSourceType(mToken, -1);
            int playBackStatus = PlayerManager.getInstance().getCurrentAudioFocusStatus() < 0
                    ? PLAYBACK_STATUS_INTERRUPT
                    : PlayerManager.getInstance().isPlaying() ? PLAYBACK_STATUS_PLAYING : PLAYBACK_STATUS_PAUSED;
            mMusicPlaybackInfo = new MyMusicPlaybackInfo(playBackStatus,
                    -1, mToken, mMediaSessionIPlayerStateListener.getDuration());
            mediaCenterAPI.updateMusicPlaybackState(mToken, mMusicPlaybackInfo);
        }
    }

    private static class MyOnAudioFocusChangeInter implements OnAudioFocusChangeInter {
        private WeakReference<KRadioMediaSessionImpl> weakReference;

        public MyOnAudioFocusChangeInter(KRadioMediaSessionImpl kRadioMediaSessionImpl) {
            weakReference = new WeakReference<>(kRadioMediaSessionImpl);
        }

        @Override
        public void onAudioFocusChange(int i) {
            KRadioMediaSessionImpl kRadioMediaSessionImpl = weakReference.get();
            if (kRadioMediaSessionImpl == null) {
                return;
            }
            Log.i(TAG, "onAudioFocusChange--------->focus = " + i);
            if (i == AudioManager.AUDIOFOCUS_GAIN || i == AudioManager.AUDIOFOCUS_GAIN_TRANSIENT) {
                kRadioMediaSessionImpl.registerMediaSession(AppDelegate.getInstance().getContext());
            } else if (i == AudioManager.AUDIOFOCUS_LOSS || i == AudioManager.AUDIOFOCUS_LOSS_TRANSIENT || i == AudioManager.AUDIOFOCUS_NONE) {
                kRadioMediaSessionImpl.unregisterMediaSession(AppDelegate.getInstance().getContext());
            }
        }
    }

    private void updatePausedStatus() {
        int focusStatus = PlayerManager.getInstance().getCurrentAudioFocusStatus();
        Log.i(TAG, "onPlayerPaused----------->focusStatus = " + focusStatus);
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001276965757?userId=1545533问题
        updatePlayStatus(focusStatus < 0 ? PLAYBACK_STATUS_INTERRUPT
                : PlayerManager.getInstance().isPlaying() ? PLAYBACK_STATUS_PLAYING : PLAYBACK_STATUS_PAUSED);
    }

    private void updatePlayStatus(int playBackStatus) {
        if (isTokenAvailable()) {
            mMusicPlaybackInfo = new MyMusicPlaybackInfo(playBackStatus,
                    SOURCE_TYPE_ONLINE, mToken, mMediaSessionIPlayerStateListener.getDuration());
            MediaCenterAPI.get(AppDelegate.getInstance().getContext()).updateMusicPlaybackState(mToken,
                    mMusicPlaybackInfo);
        }
    }

    public static class MyMusicPlaybackInfo extends MusicPlaybackInfo {
        int playBackStatus, sourceType;
        Object token;
        long duration;

        public MyMusicPlaybackInfo(int playBackStatus, int sourceType, Object token, long duration) {
            this.playBackStatus = playBackStatus;
            this.sourceType = sourceType;
            this.token = token;
            this.duration = duration;
        }

        @Override
        public String getTitle() {
            PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
            String title = playItem.getTitle();
            Log.i(TAG, "getTitle------->title = " + title);
            return title;
        }

        @Override
        public String getAppName() {
            return mAppName;
        }

        @Override
        public String getAppIcon() {
            return mAppIcon;
        }

        @Override
        public String getAlbum() {
            String radioName = PlayerManager.getInstance().getCurPlayItem().getRadioName();
            Log.i(TAG, "getAlbum------->radioName = " + radioName);
            return radioName;
        }

        @Override
        public String getArtist() {
            String radioName = PlayerManager.getInstance().getCurPlayItem().getRadioName();
            Log.i(TAG, "getArtist------->radioName = " + radioName);
            return radioName;
        }

        @Override
        public long getDuration() {
            if (duration > 0) {
                return duration;
            }

            PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
            if (playItem != null) {
                return playItem.getDuration();
            }
            return super.getDuration();
        }

        @Override
        public int getSourceType() {
            Log.i(TAG, "getSourceType------->src = " + sourceType);
            return sourceType;
        }

        @Override
        public PendingIntent getLaunchIntent() {
            Intent intent;
            Context context = AppDelegate.getInstance().getContext();
            Activity activity = AppManager.getInstance().getTopActivity();
            if (activity == null) {
                intent = context.getPackageManager().getLaunchIntentForPackage(context.getPackageName());
            } else {
                intent = new Intent();
                ComponentName componentName = new ComponentName(AppDelegate.getInstance().getContext().getPackageName(), activity.getLocalClassName());
                intent.setComponent(componentName);
//                        Log.i(TAG, "getLaunchIntent------->start = " + componentName);
            }

            if (ECarXAPIBase.VERSION_INT == 341) {
                //@since {@com.ecarx.sdk.mediacenter.MusicClient#onMediaCenterFocusChanged(String currentRequestClient)}
                //三方多媒体可以在上述回调中保存当前拥有媒体中心控制焦点的应用包名,this.currentRequestClient=currentRequestClient;
                //如果当前应用不是媒体中心控制焦点的应用,则更新运营跳转内容列表.
                if (!(context.getPackageName().equals(currentRequestClient))) {
                    boolean success = MediaCenterAPI.get(context).requestPlay(token);
                    if (success) {
                        currentRequestClient = context.getPackageName();
                        try {
                            MediaCenterAPI.get(context).updateMediaContent(token, new ArrayList<ContentInfo>());
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }

            } else if (ECarXAPIBase.VERSION_INT >= 342 && ECarXAPIBase.VERSION_INT < 346) {
                String currentFocuesClient = null;
                try {
                    currentFocuesClient = MediaCenterAPI.get(context).queryCurrentFocusClient(token);
                } catch (MediaCenterException e) {
                    e.printStackTrace();
                }
                if (!(context.getPackageName().equals(currentFocuesClient))) {
                    boolean success = MediaCenterAPI.get(context).requestPlay(token);
                    if (success) {
                        try {
                            MediaCenterAPI.get(context).updateMediaContent(token, new ArrayList<ContentInfo>());
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
            }

            Log.i(TAG, "getLaunchIntent------>");
            return PendingIntent.getActivity(AppDelegate.getInstance().getContext(), 101, intent, PendingIntent.FLAG_UPDATE_CURRENT);
        }

        @Override
        public int getPlaybackStatus() {
            Log.i(TAG, "getPlaybackStatus-------> " + playBackStatus);
            return playBackStatus;
        }

        @Override
        public Uri getArtwork() {
            PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
            if (playItem != null) {
                String picUrl = getDefaultConfigPicUrl(playItem.getPicUrl(), PIC_250_250);
                return Uri.parse(picUrl);
            }
            return super.getArtwork();
        }

        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001285486601?userId=1229522问题
        @Override
        public String getUuid() {
            List<PlayItem> playItemArrayList = PlayerManager.getInstance().getPlayList();
            PlayItem playItem = null;
            if (ListUtil.isEmpty(playItemArrayList)) {
                playItem = PlayerManager.getInstance().getCurPlayItem();
            } else {
                int position = PlayerManager.getInstance().getPlayListCurrentPosition();
                int size = playItemArrayList.size();
                if (position < size && position >= 0) {
                    playItem = playItemArrayList.get(position);
                }
            }
            if (playItem != null) {
                long audioId = playItem.getAudioId();
                Log.i(TAG, "getUuid------->audioId = " + audioId);
                return String.valueOf(audioId);
            }
            return super.getUuid();
        }

        @Override
        public int getPlayingItemPositionInQueue() {
            int position = PlayerManager.getInstance().getPlayListCurrentPosition();
            Log.i(TAG, "getPlayingItemPositionInQueue------>" + position);
            // 解决https://app.huoban.com/tables/2100000007530121/items/2300001336321361?userId=1545533问题
            return position;
        }

        @Override
        public String getPackageName() {
            return AppDelegate.getInstance().getContext().getPackageName();
        }

        public PendingIntent getPlayerIntent() {
            Intent intent;
            Context context = AppDelegate.getInstance().getContext();
            Activity activity = AppManager.getInstance().getTopActivity();
            if (activity == null) {
                intent = context.getPackageManager().getLaunchIntentForPackage(context.getPackageName());
            } else {
                intent = new Intent();
                ComponentName componentName = new ComponentName(AppDelegate.getInstance().getContext().getPackageName(), activity.getLocalClassName());
                intent.setComponent(componentName);
//                        Log.i(TAG, "getLaunchIntent------->start = " + componentName);
            }

            if (ECarXAPIBase.VERSION_INT == 341) {
                //@since {@com.ecarx.sdk.mediacenter.MusicClient#onMediaCenterFocusChanged(String currentRequestClient)}
                //三方多媒体可以在上述回调中保存当前拥有媒体中心控制焦点的应用包名,this.currentRequestClient=currentRequestClient;
                //如果当前应用不是媒体中心控制焦点的应用,则更新运营跳转内容列表.
                if (!(context.getPackageName().equals(currentRequestClient))) {
                    boolean success = MediaCenterAPI.get(context).requestPlay(token);
                    if (success) {
                        currentRequestClient = context.getPackageName();
                        try {
                            MediaCenterAPI.get(context).updateMediaContent(token, new ArrayList<ContentInfo>());
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }

            } else if (ECarXAPIBase.VERSION_INT >= 342 && ECarXAPIBase.VERSION_INT < 346) {
                String currentFocuesClient = null;
                try {
                    currentFocuesClient = MediaCenterAPI.get(context).queryCurrentFocusClient(token);
                } catch (MediaCenterException e) {
                    e.printStackTrace();
                }
                if (!(context.getPackageName().equals(currentFocuesClient))) {
                    boolean success = MediaCenterAPI.get(context).requestPlay(token);
                    if (success) {
                        try {
                            MediaCenterAPI.get(context).updateMediaContent(token, new ArrayList<ContentInfo>());
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
            }

            Log.i(TAG, "getPlayerIntent------>");
            return PendingIntent.getActivity(AppDelegate.getInstance().getContext(), 101, intent, PendingIntent.FLAG_UPDATE_CURRENT);
        }


        /**
         * 用户VIP
         *
         * @return VIP -1 未登录 0 普通用户 大于0 VIP
         * @since {@link com.ecarx.sdk.ECarXAPIBase#VERSION_INT} 341
         */
        @Override
        public int getVip() {
            boolean isLogin = false;
            try {
                isLogin = ComponentUtil.getResultValue(UserComponentConst.NAME, UserComponentConst.IS_USER_BOUND);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (isLogin) {
                return 0;
            }
            return -1;
        }
    }

    private void updateProgress(long progress) {
        if (isTokenAvailable()) {
            MediaCenterAPI.get(AppDelegate.getInstance().getContext()).updateCurrentProgress(mToken, progress);
        }
    }

    private static class MediaSessionIPlayerStateListener implements IPlayerStateListener {
        private WeakReference<KRadioMediaSessionImpl> mWeakReference;
        private long mDuration;

        public MediaSessionIPlayerStateListener(KRadioMediaSessionImpl kRadioMediaSessionImpl) {
            mWeakReference = new WeakReference<>(kRadioMediaSessionImpl);
        }

        public long getDuration() {
            return mDuration;
        }

        @Override
        public void onIdle(PlayItem playItem) {
            Log.i(TAG, "onIdle----------->");
            mDuration = 0;
        }

        @Override
        public void onPlayerPreparing(PlayItem playItem) {
            Log.i(TAG, "onPlayerPreparing----------->");
            KRadioMediaSessionImpl kRadioMediaSessionImpl = mWeakReference.get();
            if (kRadioMediaSessionImpl != null) {
                kRadioMediaSessionImpl.updatePlayStatus(PLAYBACK_STATUS_PREPARE);
            }
        }

        @Override
        public void onPlayerPlaying(PlayItem playItem) {
            Log.i(TAG, "onPlayerPlaying----------->");
            mDuration = 0;
            KRadioMediaSessionImpl kRadioMediaSessionImpl = mWeakReference.get();
            if (kRadioMediaSessionImpl != null) {
                kRadioMediaSessionImpl.updatePlayList();
                kRadioMediaSessionImpl.updatePlayStatus(PLAYBACK_STATUS_PLAYING);
            }
        }

        @Override
        public void onPlayerPaused(PlayItem playItem) {
            Log.i(TAG, "onPlayerPaused(PlayItem playItem)");
            KRadioMediaSessionImpl kRadioMediaSessionImpl = mWeakReference.get();
            if (kRadioMediaSessionImpl != null) {
                kRadioMediaSessionImpl.updatePausedStatus();
            }
        }

        @Override
        public void onProgress(PlayItem s, long progress, long duration) {
            mDuration = duration;
            KRadioMediaSessionImpl kRadioMediaSessionImpl = mWeakReference.get();
            if (kRadioMediaSessionImpl != null) {
                kRadioMediaSessionImpl.updateProgress(progress);
            }
        }

        @Override
        public void onPlayerFailed(PlayItem playItem, int i, int i1) {
            Log.i(TAG, "onPlayerFailed----------->");
            KRadioMediaSessionImpl kRadioMediaSessionImpl = mWeakReference.get();
            if (kRadioMediaSessionImpl != null) {
                kRadioMediaSessionImpl.updatePausedStatus();
            }
        }

        @Override
        public void onPlayerEnd(PlayItem playItem) {
            Log.i(TAG, "onPlayerEnd----------->");
            // 解决https://app.huoban.com/tables/2100000007530121/items/2300001315861232?userId=1229522问题
            KRadioMediaSessionImpl kRadioMediaSessionImpl = mWeakReference.get();
            if (kRadioMediaSessionImpl != null) {
                kRadioMediaSessionImpl.updatePausedStatus();
            }
        }

        @Override
        public void onSeekStart(PlayItem s) {
            Log.i(TAG, "onSeekStart----------->");
        }

        @Override
        public void onSeekComplete(PlayItem s) {
            Log.i(TAG, "onSeekComplete----------->");
        }

        @Override
        public void onBufferingStart(PlayItem playItem) {
            Log.i(TAG, "onBufferingStart----------->");
        }

        @Override
        public void onBufferingEnd(PlayItem playItem) {
            Log.i(TAG, "onBufferingEnd----------->");
        }

        @Override
        public void onDownloadProgress(PlayItem playItem, long l, long l1) {
            Log.i(TAG, "onDownloadProgress----------->");
        }
    }

    private static class MyMusicClient extends MusicClient {
        @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
        @Override
        public boolean onExit() {
            Log.i(TAG, "onExit");
            AppManager.getInstance().appExit();
            return true;
        }

        @Override
        public boolean onPlay() {
            Log.i(TAG, "onPlay");
            new Handler(Looper.getMainLooper()).post(new Runnable() {
                @Override
                public void run() {
                    Log.i(TAG, "onPlay--------->start");
                    PlayerManagerHelper.getInstance().play(true);
                }
            });
            return true;
        }

        @Override
        public boolean onPause() {
            Log.i(TAG, "onPause");
            new Handler(Looper.getMainLooper()).post(new Runnable() {
                @Override
                public void run() {
                    Log.i(TAG, "onPause--------->start");
                    PlayerManagerHelper.getInstance().pause(true);
//                    PlayerManagerHelper.getInstance().switchPlayerStatus(true);
                }
            });
            return true;
        }

        @Override
        public boolean onNext() {
            Log.i(TAG, "onNext");
            new Handler(Looper.getMainLooper()).post(new Runnable() {
                @Override
                public void run() {
                    Log.i(TAG, "onNext--------->start");
                    PlayerManagerHelper pmHelper = PlayerManagerHelper.getInstance();
                    if (pmHelper.hasNextItem()) {
                        pmHelper.playNext(true);
                    } else {
                        Context context = AppDelegate.getInstance().getContext();
                        ToastUtil.showError(context, R.string.is_last_one_warning_str);
                    }
                }
            });
            return true;
        }

        @Override
        public boolean onPrevious() {
            Log.i(TAG, "onPrevious");
            new Handler(Looper.getMainLooper()).post(new Runnable() {
                @Override
                public void run() {
                    Log.i(TAG, "onPrevious--------->start");
                    PlayerManagerHelper pmHelper = PlayerManagerHelper.getInstance();
                    if (pmHelper.hasPreItem()) {
                        pmHelper.playPre(true);
                    } else {
                        Context context = AppDelegate.getInstance().getContext();
                        ToastUtil.showError(context, R.string.is_first_one_warning_str);
                    }
                }
            });
            return true;
        }

        @Override
        public boolean onForward() {
            Log.i(TAG, "onForward");
            new Handler(Looper.getMainLooper()).post(new Runnable() {
                @Override
                public void run() {
                    Log.i(TAG, "onForward--------->start");
                    PlayerManagerHelper.getInstance().fastForward();
                }
            });
            return true;
        }

        @Override
        public boolean onRewind() {
            Log.i(TAG, "onRewind");
            new Handler(Looper.getMainLooper()).post(new Runnable() {
                @Override
                public void run() {
                    Log.i(TAG, "onRewind--------->start");
                    PlayerManagerHelper.getInstance().fastBackward();
                }
            });
            return true;
        }

        /**
         * 收藏回调
         *
         * @param type      收藏/取消收藏的音源类型，一般是在declareSupportCollectTypes声明列表中，否则无法回调
         * @param isCollect true表示收藏，false表示取消收藏
         * @return 执行收藏/取消收藏成功返回true， 失败返回false
         */
        @Override
        public boolean onCollect(int type, boolean isCollect) {
            Log.i(TAG, "onCollect--------->start ， isCollect " + isCollect + " ， " + type);
            boolean userBound = UserInfoManager.getInstance().isUserBound();
            if (!userBound) {
                Log.i(TAG, "onCollect--------->user not bound");
                return false;
            }
            long id = PlayerHelper.getSubscribeId();
            SubscribeData sd = getSubscribeData(id);
            if (sd == null) {
                return false;
            }
            if (isCollect) {
                SubscribeHelper.isSubscribed(CP.KaoLaFM, String.valueOf(id), new ResultCallback() {
                    @Override
                    public void onResult(boolean result, int code) {
                        if (result) {
                            Log.i(TAG, "onCollect--------->has coll ");
                        } else {
                            SubscribeHelper.subscribe(CP.KaoLaFM, sd, null);
                        }

                    }

                    @Override
                    public void onFailure(ErrorInfo errorInfo) {
                        Log.i(TAG, "onCollect--------->errorInfo " + errorInfo.toString());

                    }
                });
            } else {
                SubscribeHelper.unsubscribe(CP.KaoLaFM, sd, null);
            }
            return true;
        }

        private SubscribeData getSubscribeData(long id) {
            PlayItem mCurrentPlayItem = PlayerManager.getInstance().getCurPlayItem();
            if (mCurrentPlayItem == null) {
                return null;
            }

            SubscribeData sd = new SubscribeData();
            sd.setId(id);
            sd.setName(mCurrentPlayItem.getAlbumTitle());
            sd.setType(mCurrentPlayItem.getType());
            sd.setImg(mCurrentPlayItem.getPicUrl());
            sd.setUpdateTime(mCurrentPlayItem.getUpdateTime());
            sd.setLocation(SubscibeReportEvent.POSITION_PLAY_BAR);
            return sd;
        }

        @Override
        public boolean onDownload(int i, boolean b) {
            return false;
        }

        @Override
        public boolean onLoopModeChange(int i) {
            return false;
        }

        @Override
        public boolean onSourceSelected(int i) {
            return false;
        }

        @Override
        public boolean onSourceChanged(int i, String s) {
            return false;
        }

        @Override
        public boolean onMediaSelected(MediaInfo mediaInfo) {
            return false;
        }

        @Override
        public MusicPlaybackInfo getMusicPlaybackInfo() {
            return mMusicPlaybackInfo;
        }

        @Override
        public int[] getMediaSourceTypeList() {
            return new int[0];
        }

        @Override
        public int getCurrentSourceType() {
            return 0;
        }

        @Override
        public long getCurrentProgress() {
            PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
            if (playItem != null) {
                return playItem.getPosition();
            }
            return 0;
        }

        @Override
        public List<MediaInfo> getPlaylist(int i) {
            return null;
        }

        @Override
        public boolean onReplay() {
            return false;
        }

        @Override
        public boolean onPlayRecommend(RecommendInfo recommendInfo) {
            return false;
        }

        @Override
        public boolean onCancelRecommend(RecommendInfo recommendInfo) {
            return false;
        }

        @Override
        public boolean onMediaSelected(int index, String s) {
//            ArrayList<PlayItem> playItems = KLAutoPlayerManager.getInstance().getPlayList(true);
            PlayerManagerHelper pmHelper = PlayerManagerHelper.getInstance();
            List<PlayItem> playItems = pmHelper.getPlayList(true);
            if (ListUtil.isEmpty(playItems)) {
                return false;
            }
            for (int i = 0, size = playItems.size(); i < size; i++) {
                PlayItem playItem = playItems.get(i);
                if (playItem == null) {
                    continue;
                }
                long audioId = playItem.getAudioId();
                if (StringUtil.equals(String.valueOf(audioId), s)) {
                    new Handler(Looper.getMainLooper()).post(new Runnable() {
                        @Override
                        public void run() {
                            if (playItem.getType() == PlayItemType.BROADCAST_LIVING.ordinal()) {
                                pmHelper.start(String.valueOf(audioId), RESOURCES_TYPE_BROADCAST);
                            } else {
                                pmHelper.playStreamLiving(playItem);
                            }
                        }
                    });
                    break;
                }
            }
            return false;
        }

        @Override
        public boolean onMediaForward(boolean b) {
            return false;
        }

        @Override
        public boolean onMediaRewind(boolean b) {
            return false;
        }

        @Override
        public boolean onMediaQualityChange(int i) {
            return false;
        }

        @Override
        public void onMediaCenterFocusChanged(String s) {
            currentRequestClient = s;
        }

        public List<ContentInfo> getContentList() {
            return new ArrayList<ContentInfo>();
        }

        public MediaListsInfo getMultiMediaList(int[] mediaListType) {
            return null;
        }

        public boolean ctrlPlayMediaList(int mediaListType) {
            return false;
        }

        public boolean ctrlPauseMediaList(int mediaListType) {
            return false;
        }

        public void operationType(int type) {
            Log.i(TAG, "operationType ---------> :" + type);
        }

    }

    private boolean isTokenAvailable() {
        boolean flag = mToken != null;
        if (!flag) {
            initToken();
        }
        flag = mToken != null;
        return flag;
    }

    private void initToken() {
        Context context = AppDelegate.getInstance().getContext();
        MediaCenterAPI mediaCenterAPI = MediaCenterAPI.get(context);
        if (mediaCenterAPI != null) {
            mToken = mediaCenterAPI.registerMusic(context.getPackageName(), myMusicClient);
        }
        if (mToken != null) {
            mediaCenterAPI.requestPlay(mToken);
            mediaCenterAPI.updateCurrentSourceType(mToken, SOURCE_TYPE_ONLINE);
//  声明支持vr语音收藏，当声明后，可在MusicClient中收到onCollect回调，types 是支持收藏指令类型，如
//            TYPE_COLLECTION_MUSIC	收藏音乐
//            TYPE_COLLECTION_VIDEO	收藏视频
//            TYPE_COLLECTION_RADIO	收藏电台
//            TYPE_COLLECTION_NEWS	收藏新闻
            mediaCenterAPI.declareSupportCollectTypes(mToken, new int[]{
                    AbstractVideoClient.TYPE_COLLECTION_MUSIC, AbstractVideoClient.TYPE_COLLECTION_RADIO, AbstractVideoClient.TYPE_COLLECTION_NEWS
            });
        }
        Log.i(TAG, "mt is null ? " + (mToken == null));
    }

//    private static class MyIPlayChangedListener implements IPlayChangedListener {
//        private WeakReference<KRadioMediaSessionImpl> mWeakReference;
//
//        public MyIPlayChangedListener(KRadioMediaSessionImpl kRadioMediaSessionImpl) {
//            mWeakReference = new WeakReference<>(kRadioMediaSessionImpl);
//        }
//
//        @Override
//        public void onPlayChangeChanged(PlayItem playItem) {
//            KRadioMediaSessionImpl kRadioMediaSessionImpl = mWeakReference.get();
//            Log.i(TAG, "onPlayChangeChanged-------->" + kRadioMediaSessionImpl);
//            if (kRadioMediaSessionImpl != null) {
//                kRadioMediaSessionImpl.updatePlayList();
//            }
//        }
//    }

    private class MyMediaListInfo extends MediaListInfo {
        private ArrayList<PlayItem> mPlayItems;

        public ArrayList<PlayItem> getPlayItems() {
            return mPlayItems;
        }

        public void setPlayItems(ArrayList<PlayItem> mPlayItems) {
            if (ListUtil.isEmpty(mPlayItems)) {
                this.mPlayItems = null;
                return;
            }
            this.mPlayItems = (ArrayList<PlayItem>) mPlayItems.clone();
        }

        @Override
        public int getMediaListType() {
            // 解决https://app.huoban.com/tables/2100000007530121/items/2300001337039189?userId=1229522问题
            return 0;
        }

        @Override
        public int getSourceType() {
            return SOURCE_TYPE_ONLINE;
        }

        @Override
        public List<MediaInfo> getMediaList() {
            Log.i(TAG, "getMediaList--------->");
            PlayerManagerHelper pmHelper = PlayerManagerHelper.getInstance();
            List<PlayItem> playItemArrayList = pmHelper.getPlayList(true);

            if (!ListUtil.isEmpty(playItemArrayList)) {
                int size = playItemArrayList.size();
                List<MediaInfo> mediaInfoList = new ArrayList<>(size);
                for (int i = 0; i < size; i++) {
                    PlayItem playItem = playItemArrayList.get(i);
                    if (playItem == null) {
                        continue;
                    }
                    MyMediaInfo mediaInfo = new MyMediaInfo(playItem, i);
                    mediaInfoList.add(mediaInfo);
                }
                Log.i(TAG, "getMediaList--------->mediaInfoList size = " + mediaInfoList.size());
                return mediaInfoList;
            }
            return super.getMediaList();
        }
    }


    private MyMediaListInfo myMediaListInfo;

    private void updatePlayList() {
        Log.i(TAG, "updatePlayList-------->mToken = " + mToken);
        if (isTokenAvailable()) {
            if (myMediaListInfo == null) {
                myMediaListInfo = new MyMediaListInfo();
            }
            Log.i(TAG, "updatePlayList-------->");
            MediaCenterAPI.get(AppDelegate.getInstance().getContext()).updateMediaList(mToken, myMediaListInfo);
        }
    }

    private class MyMediaInfo extends MediaInfo {
        private PlayItem mPlayItem;
        private int mIndex;

        public MyMediaInfo(PlayItem playItem, int index) {
            mPlayItem = playItem;
            mIndex = index;
        }

        @Override
        public String getTitle() {
            return mPlayItem.getTitle();
        }

        @Override
        public String getUuid() {
            return String.valueOf(mPlayItem.getAudioId());
        }

        @Override
        public String getArtist() {
            return PlayerManager.getInstance().getCurPlayItem().getRadioName();
        }

        @Override
        public Uri getArtwork() {
            String picUrl = getPic(mPlayItem, PIC_100_100);
            return Uri.parse(picUrl);
        }

        @Override
        public int getSourceType() {
            return SOURCE_TYPE_ONLINE;
        }

        @Override
        public int getAlbumIndex() {
            return mIndex;
        }

        @Override
        public int getPlayingItemPositionInQueue() {
            int position = PlayerManager.getInstance().getPlayListCurrentPosition();
            Log.i(TAG, "MediaInfo getPlayingItemPositionInQueue------>" + position);
            // 解决https://app.huoban.com/tables/2100000007530121/items/2300001336321361?userId=1545533问题
            return position;
        }
    }

    private String getPic(PlayItem playItem, String type) {
        String picUrl = playItem.getPicUrl();
        return getDefaultConfigPicUrl(picUrl, type);
    }


    public String getAppIcon() {
        return "drawable://ic_launcher_yt";
    }

    public Intent getIntent() {
        Intent intent;
        Context context = AppDelegate.getInstance().getContext();
        Activity activity = AppManager.getInstance().getTopActivity();
        if (activity == null) {
            intent = context.getPackageManager().getLaunchIntentForPackage(context.getPackageName());
        } else {
            intent = new Intent();
            ComponentName componentName = new ComponentName(AppDelegate.getInstance().getContext().getPackageName(), activity.getLocalClassName());
            intent.setComponent(componentName);
        }
        return intent;
    }

    // App控制权变化的回调
    private MediaController mMediaController = new MediaController() {
        @Override
        public void onControllerChanged(String controllerPackageName) {
            // 控制权变化时，会收到此回调（此时控制权已被controllerPakcageName对应的App抢占）
            Log.i(TAG, "controllerPackageName-------->");
        }

        @Override
        public void updatePlaylist(int sourceType, List<Media> playlist) {
            Log.i(TAG, "updatePlaylist");
            // @since {@link com.ecarx.sdk.ECarXAPIBase#VERSION_INT} 341
            // 接收来自受控端多媒体播放列表更新的回调
            // 这里可能引起递归调用，由于没有机器进行验证，暂时先注释掉
            // MediaCenterAPI.get(AppDelegate.getInstance().getContext()).updateMediaList(mToken, myMediaListInfo);
        }

        @Override
        public void updatePlaybackInfo(com.ecarx.sdk.mediacenter.control.bean.MusicPlaybackInfo playbackInfo) {
            Log.i(TAG, "updatePlaybackInfo");
            // @since {@link com.ecarx.sdk.ECarXAPIBase#VERSION_INT} 341
            // 接收来自受控端多媒体当前播放媒体信息更新的回调
            // 这里可能引起递归调用，由于没有机器进行验证，暂时先注释掉
            // updatePausedStatus();
        }

        @Override
        public void updateErrorMsg(int sourceType, String errorMsg) {
            Log.i(TAG, "updateErrorMsg=" + sourceType + ":" + errorMsg);
            // @since {@link com.ecarx.sdk.ECarXAPIBase#VERSION_INT} 341
            // 接收异常状态信息的回调，例如蓝牙断开，USB断开等等
        }

        @Override
        public void updateCurrentProgress(long progress) {
            // @since {@link com.ecarx.sdk.ECarXAPIBase#VERSION_INT} 341
            if (isTokenAvailable()) {
                // 这里可能引起递归调用，由于没有机器进行验证，暂时先注释掉
                //MediaCenterAPI.get(AppDelegate.getInstance().getContext()).updateCurrentProgress(mToken, progress);
            }
        }

        @Override
        public void updateMediaContentTypeList(List<IMediaContentType> mediaContentTypeList) {
            // @since {@link com.ecarx.sdk.ECarXAPIBase#VERSION_INT} 342
            // 接收来自受控端多媒体内容分类列表更新的回调
        }
    };

    public class CustomMediaControlClient extends MediaControlClient {

        @Override
        public void onControlledChanged(String controlledPackageName) {
            //被控制权变化的回调，controllerPakcageName为当前获得到控制权媒体端的包名
            Log.i(TAG, "controlledPackageName-------->");
        }

        @Override
        public List<IMediaContentType> getMediaContentTypeList() {
            //实现返回媒体内容分类列表
            return null;
        }

        @Override
        public boolean onPlay(int soundSourceType, String mediaContentTypeId) {
            Log.i(TAG, "onPlay..");
            //实现指定音源且指定媒体内容分类的播放
            new Handler(Looper.getMainLooper()).post(new Runnable() {
                @Override
                public void run() {
                    Log.i(TAG, "onPlay--------->start");
                    PlayerManagerHelper.getInstance().play(true);
                }
            });
            return true; // 执行成功，则返回true
        }

        @Override
        public boolean onPlayByContent(int soundSourceType, String filterContent) {
            //实现指定音源且指定过滤内容关键字的播放
            return false;// 执行成功，则返回true
        }

        @Override
        public boolean onPause(int soundSourceType) {
            Log.i(TAG, "onPause..");
            //  实现暂停播放指定音源
            new Handler(Looper.getMainLooper()).post(new Runnable() {
                @Override
                public void run() {
                    Log.i(TAG, "onPause--------->" + soundSourceType);
                    PlayerManagerHelper.getInstance().pause(true);
                }
            });
            return true; // 执行成功，则返回true
        }

        @Override
        public boolean onResumeNow() {
            //  实现恢复播放
            new Handler(Looper.getMainLooper()).post(new Runnable() {
                @Override
                public void run() {
                    Log.i(TAG, "onResumeNow--------->start");
                    PlayerManagerHelper.getInstance().play(true);
                }
            });
            return true;// 执行成功，则返回true
        }

        @Override
        public boolean onPauseNow() {
            //  实现暂停播放
            //   @since {@link com.ecarx.sdk.ECarXAPIBase#VERSION_INT} 341
            new Handler(Looper.getMainLooper()).post(new Runnable() {
                @Override
                public void run() {
                    Log.i(TAG, "onPauseNow--------->start");
                    PlayerManagerHelper.getInstance().pause(true);
                }
            });
            return true; // 执行成功，则返回true
        }

        @Override
        public boolean onPlayByMediaId(int soundSourceType, String uuid) {
            //  实现指定音源指定媒体ID的播放
            //   @since {@link com.ecarx.sdk.ECarXAPIBase#VERSION_INT} 341
            return false; // 执行成功，则返回true
        }
    }


}