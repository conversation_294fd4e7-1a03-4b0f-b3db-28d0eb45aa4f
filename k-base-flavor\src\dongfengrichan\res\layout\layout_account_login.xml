<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical"
    >

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/richan_account_login_icon"
        />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/account_login_str"
        android:textColor="@color/colorWhite"
        android:textSize="@dimen/text_size4"
        android:layout_marginTop="@dimen/y60"
        />

    <Button
        android:id="@+id/richan_account_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="#384282"
        android:text="@string/account_login_btn"
        android:textSize="@dimen/text_size4"
        android:textColor="@color/colorWhite"
        android:layout_marginTop="@dimen/y20"
        android:paddingLeft="@dimen/x65"
        android:paddingRight="@dimen/x65"
        android:paddingTop="@dimen/y15"
        android:paddingBottom="@dimen/y15"
        />

</LinearLayout>