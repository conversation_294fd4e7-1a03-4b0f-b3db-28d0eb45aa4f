<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/player_card_magic_one"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/player_magic_card_margin_x_big_size"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/player_magic_card_margin_small_size"
        android:visibility="visible" />

    <View
        android:id="@+id/player_card_magic_two"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/player_magic_card_margin_big_size"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/player_magic_card_margin_normal_size"
        android:background="@drawable/shape_white30_top_round_rectangle"
        android:visibility="gone" />

    <View
        android:id="@+id/player_card_magic_three"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/player_magic_card_margin_normal_size"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/player_magic_card_margin_big_size"
        android:background="@drawable/shape_white30_top_round_rectangle"
        android:visibility="gone" />

    <View
        android:id="@+id/player_card_magic_four"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/player_magic_card_margin_normal_size"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/player_magic_card_margin_x_big_size"
        android:background="@drawable/shape_white30_top_round_rectangle"
        android:visibility="gone" />

</merge>
