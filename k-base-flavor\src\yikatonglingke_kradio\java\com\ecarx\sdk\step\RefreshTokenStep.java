package com.ecarx.sdk.step;

import android.text.TextUtils;
import android.util.Log;

import com.ecarx.sdk.AccessTokenResult;
import com.ecarx.sdk.ECarX;
import com.ecarx.sdk.ECarXSdk;
import com.ecarx.sdk.TokenInfo;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

/**
 * <AUTHOR>
 **/
public class RefreshTokenStep extends Step {
    private final ECarX ecarx;

    public RefreshTokenStep(ECarX eCarX) {
        this.ecarx = eCarX;
    }

    @Override
    public void exe() {
        Log.i(ECarX.TAG, "exe:"+getClass().getSimpleName());
        TokenInfo tokenInfo = ecarx.getEcarxTokenInfo();
        if (tokenInfo == null || tokenInfo.getAccessTokenResult() == null || TextUtils.isEmpty(tokenInfo.getAccessTokenResult().getRefreshToken())) {
            ecarx.updateStep();
            ecarx.nextStep();
        } else {
            String refreshToken = tokenInfo.getAccessTokenResult().getRefreshToken();
            ECarXSdk.getInstance().refreshToken(ECarXSdk.APP_ID, ECarXSdk.PACKAGE_NAME, ECarXSdk.SHA1_DEBUG, refreshToken, new HttpCallback<AccessTokenResult>() {
                @Override
                public void onSuccess(AccessTokenResult accessTokenResult) {
                    TokenInfo ti = new TokenInfo();
                    ti.setAccessTokenResult(accessTokenResult);
                    ti.setAccessTokenTime(System.currentTimeMillis());
                    ecarx.updateEcarxTokenInfo(ti);
                    ecarx.updateStep();
                    ecarx.nextStep();
                }

                @Override
                public void onError(ApiException e) {
//                    if (exception instanceof ECarXRefreshTokenExpiredException) {
//                        //重新授权登录
//                        ecarx.updateEcarxTokenInfo(null);
//                        ecarx.updateStep();
//                        ecarx.nextStep();
//                    }
                    Log.i(ECarX.TAG, "   onError: error=" + e);
                    ecarx.error(e);
                }
            });
        }
    }
}
