package com.kaolafm.kradio.online.mine.login;

import android.content.Context;
import android.database.ContentObserver;
import android.net.Uri;
import android.os.Handler;
import android.provider.Settings;
import android.util.Log;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.kaolafm.kradio.k_kaolafm.R; 
import com.kaolafm.kradio.lib.base.flavor.KRadioHideSoftInputInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioSinglePhoneNumGetSmsInter;
import com.kaolafm.kradio.lib.base.flavor.LoginFragmentInter;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.TimerUtil;
import com.kaolafm.kradio.online.common.event.OnlineLoginEvent;
import com.kaolafm.kradio.onlineuser.InputDiaplayInter;
import com.kaolafm.kradio.onlineuser.ui.ILoginView;
import com.kaolafm.kradio.onlineuser.ui.LoginPresenter;
import com.kaolafm.opensdk.http.error.ApiException;

import org.greenrobot.eventbus.EventBus; 

public class OnlineLoginFragment extends BaseFragment<LoginPresenter> implements ILoginView {
 
    LinearLayout mRootLayout; 
    TextView mPhoneTip; 
    Button btnGetCode; 
    EditText mEtPhoneNum; 
    EditText mEtVerificationCode; 
    Button loginButton;

    View mLoginFragmentLayout;
    private InputMethodManager mInputManager;
    private Handler mMainHandler;
    private ContentObserver mCloseScreenObserver;
    private String TAG = "LoginFragment";

    LoginFragmentInter loginFragmentInter;

    /**
     * true登录成功。
     */
    private boolean mLoginSuccess = false;

    private TimerUtil mTimerUtil;
    @Override
    protected boolean isReportFragment() {
        return true;
    }
    @Override
    protected int getLayoutId() {
        return R.layout.online_fragment_phone_login;
    }

    @Override
    protected LoginPresenter createPresenter() {
        return new LoginPresenter(this);
    }

    KRadioHideSoftInputInter mHideSoftInputInter;

    @Override
    public void initView(View view) {

        mRootLayout=view.findViewById(R.id.phone_root_layout);
        mPhoneTip=view.findViewById(R.id.phone_tip);
        btnGetCode=view.findViewById(R.id.btn_get_code);
        mEtPhoneNum=view.findViewById(R.id.et_input_num);
        mEtVerificationCode=view.findViewById(R.id.et_input_code);
        loginButton=view.findViewById(R.id.btn_login);

        btnGetCode.setOnClickListener(v -> onViewClicked(v) );
        loginButton.setOnClickListener(v -> onViewClicked(v) );
        mEtPhoneNum.setOnClickListener(v -> onViewClicked(v) );
        mEtVerificationCode.setOnClickListener(v -> onViewClicked(v) );
        
        mHideSoftInputInter = ClazzImplUtil.getInter("KRadioHideSoftInputImpl");

        InputDiaplayInter mInputDiaplayInter = ClazzImplUtil.getInter("InputDiaplayImpl");
        if (mInputDiaplayInter != null && mInputDiaplayInter.isCustomDisplay()) {
            mMainHandler = new Handler();
            mCloseScreenObserver = new ContentObserver(mMainHandler) {
                @Override
                public void onChange(boolean selfChange, Uri uri) {
                    super.onChange(selfChange, uri);
                    Log.i(TAG, "onChange: uri = " + uri);
                    Context ctx = getContext();
                    if (ctx != null) {
                        //get state of close screen window
                        int displayState = Settings.System.getInt(ctx.getContentResolver(), "DISP_STATE", 1);
                        Log.i(TAG, "onChange: get DISPLAY_STATE = " + displayState);
                        if (displayState == 0) {
                            destroyInputMethod(mEtPhoneNum);
                            destroyInputMethod(mEtVerificationCode);
                        }
                    } else {
                        Log.i(TAG, "onChange: get context is null");
                    }
                }
            };
            getActivity().getContentResolver().registerContentObserver(Settings.System.CONTENT_URI, true, mCloseScreenObserver);
        } else {
            getActivity().getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN);
        }
        loginFragmentInter = ClazzImplUtil.getInter("LoginFragmentImpl");
        if (loginFragmentInter != null) {
            loginFragmentInter.initView(getActivity(), mEtPhoneNum, mEtVerificationCode);
        }

        mLoginFragmentLayout = view.findViewById(R.id.phone_root_layout);
        if (isCanHideInput() && mLoginFragmentLayout != null) {
            //亿咖通EASODP-10754云听-账号登陆界面登陆完成后键盘无法隐藏 通用布局添加了一个id
            // 防止有些flaver BindView 崩溃 单独写点击事件
            mLoginFragmentLayout.setOnTouchListener(new View.OnTouchListener() {
                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    if (event.getAction() == MotionEvent.ACTION_DOWN) {
                        hideSoftInput();
                    }
                    return false;
                }
            });
        }
        mEtVerificationCode.setOnKeyListener(onKeyListener);
    }

    View.OnKeyListener onKeyListener = new View.OnKeyListener() {
        @Override
        public boolean onKey(View v, int keyCode, KeyEvent event) {
            Log.i(TAG, "onKeyListener keyCode:" + keyCode);
            switch (keyCode) {
                case KeyEvent.KEYCODE_ENTER:
                case EditorInfo.IME_ACTION_DONE:
                    hideSoftInput();
                    return true;
            }
            return false;
        }
    };

    private void createInputMethod(View view) {
        if (view != null && mInputManager == null) {
            mInputManager = (InputMethodManager) view.getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
            mInputManager.showSoftInput(view, InputMethodManager.SHOW_FORCED);
        }
    }


    private void destroyInputMethod(View view) {
        try {
            if (mInputManager != null && view != null) {
                Log.i(TAG, "destroyInputMethod");
                mInputManager.hideSoftInputFromWindow(view.getWindowToken(), 0);
            } else {
                Log.i(TAG, "destroyInputMethod: out of bound");
            }
        } catch (Exception e) {
            Log.i(TAG, "destroyInputMethod: exception = " + e.getMessage());
            e.printStackTrace();
        } finally {
            mInputManager = null;
        }
    } 
    public void onViewClicked(View view) {
        int id = view.getId();
        if (!AntiShake.check(id)) {
            if (isNetworkUnavailable()) {
                return;
            }
            if (id == R.id.btn_get_code) {
                getVerificationCode();
            } else if (id == R.id.btn_login) {
                setViewEnable(loginButton, false);
                hasHideInput();
                if (!mLoginSuccess) {
                    mPresenter.login(mEtPhoneNum.getText().toString(), mEtVerificationCode.getText().toString());
                }
            } else if (id == R.id.et_input_code) {
                createInputMethod(mEtVerificationCode);
            } else if (id == R.id.et_input_num) {
                createInputMethod(mEtPhoneNum);
            }
        }
    }

    private void hasHideInput() {//账号登陆界面登陆完成后键盘无法隐藏
        if (isCanHideInput() && mLoginFragmentLayout != null) {
            destroyInputMethod(mLoginFragmentLayout);
        }
    }

    private boolean isCanHideInput() {
        return null != mHideSoftInputInter && mHideSoftInputInter.hasHideSoftInput();
    }

    private boolean isNetworkUnavailable() {
        if (!NetworkUtil.isNetworkAvailable(getContext(), false)) {
            toast(R.string.no_net_work_str);
            return true;
        }
        return false;
    }

    private void getVerificationCode() {
        if (mTimerUtil != null && mTimerUtil.isCountDownInProgress()) {
            KRadioSinglePhoneNumGetSmsInter inter = ClazzImplUtil.getInter("KRadioSinglePhoneNumGetSmsImpl");
            if (inter != null && inter.isOnlyOnePhoneNumGetSms()) {
                toast("1分钟内最多获得一次验证码");
                return;
            }
        }
        mPresenter.getVerificationCode(mEtPhoneNum.getText().toString().trim());
    }

    @Override
    public void failedToGetCode(ApiException e) {
        enableButton();
        toast(e.getMessage());
    }

    @Override
    public void startCountdown() {
        btnGetCode.setEnabled(false);
        mTimerUtil = TimerUtil.newInstance();
        mTimerUtil.countdown(60, num -> {
            if (btnGetCode != null) {
                btnGetCode.setText("重新发送(" + num + "s)");
            }
        }, this::enableButton);
    }

    /**
     * 使获取验证码按钮恢复可点击
     */
    private void enableButton() {
        if (btnGetCode != null) {
            cancelCountdown();
            btnGetCode.setEnabled(true);
            btnGetCode.setText(R.string.user_get_verification_code);
        }
    }

    /**
     * 重置登录UI
     */
    public void resetView() {
        mEtVerificationCode.setText("");
        mEtPhoneNum.setText("");
        enableButton();
        setViewEnable(loginButton, true);
    }

    /**
     * 登录
     */
    @Override
    public void loginSuccess() {
        hasHideInput();
        mLoginSuccess = true;
//        EventBus.getDefault().post(new OnlineLoginEvent());
        if (getActivity() instanceof OnlineLoginActivity){
            ((OnlineLoginActivity)getActivity()).reportLoginEvent();
        }
        getActivity().finish();
    }

    @Override
    public void loginError() {
        setViewEnable(loginButton, true);
    }

    /**
     * 退出登录
     */
    @Override
    public void logoutSuccess() {
//        EventBus.getDefault().post(new OnlineLoginEvent());
    }

    /**
     * 退出登录
     */
    @Override
    public void logoutSError() {

    }

    private void setViewEnable(View view, boolean enable) {
        if (view == null) {
            return;
        }
        view.setEnabled(enable);
    }

    @Override
    public void onSupportInvisible() {
        super.onSupportInvisible();
        if (mLoginSuccess) {
            resetView();
        }
    }

    @Override
    protected void showAccordingToScreen(int orientation) {
        super.showAccordingToScreen(orientation);

//        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
//            uploadView(true);
//        } else {
//            uploadView(false);
//        }
    }

    private void uploadView(boolean isLand) {
//        ConstraintSet set = new ConstraintSet();
//        set.clone(mRootLayout);
//        if (isLand) {
//            set.setHorizontalBias(mPhoneTip.getId(), 0.4f);
//        } else {
//            set.setHorizontalBias(mPhoneTip.getId(), 0.5f);
//        }
//        set.applyTo(mRootLayout);
    }

    @Override
    public void toast(String msg) {
        ToastUtil.showOnActivity(getContext(), msg);
    }

    @Override
    public void onPause() {
        if (loginFragmentInter != null) {
            loginFragmentInter.onPause();
        }
        super.onPause();
    }

    @Override
    public void onResume() {
        super.onResume();
        if (loginFragmentInter != null) {
            loginFragmentInter.onResume();
        }
    }

    @Override
    public void onDestroy() {
        if (mCloseScreenObserver != null) {
            getActivity().getContentResolver().unregisterContentObserver(mCloseScreenObserver);
        }
        if (mMainHandler != null) {
            mMainHandler.removeCallbacksAndMessages(null);
        }
        mCloseScreenObserver = null;
        mMainHandler = null;
        destroyInputMethod(mEtPhoneNum);
        destroyInputMethod(mEtVerificationCode);

        if (loginFragmentInter != null) {
            loginFragmentInter.onDestroy();
        }
        super.onDestroy();
        cancelCountdown();
    }

    private void cancelCountdown() {
        if (mTimerUtil != null) {
            mTimerUtil.cancel();
            mTimerUtil = null;
        }
    }

    private void toast(int resId) {
        toast(ResUtil.getString(resId));
    }

    @Override
    protected void changeViewLayoutForStatusBar(View view) {
    }

    @Override
    protected void addFragmentRootViewPadding(View view) {
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (!isVisibleToUser) {
            hasHideInput();
        }

    }
}
