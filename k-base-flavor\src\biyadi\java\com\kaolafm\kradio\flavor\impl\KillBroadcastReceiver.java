package com.kaolafm.kradio.flavor.impl;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;


import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.service.BYDWidgetService;


/**
 * 比亚迪车机在近期任务列表 kill 掉考拉fm进程发出通知
 *
 * Created by Wenchl on 2018/3/29.
 */

public class KillBroadcastReceiver extends BroadcastReceiver {

    private static final String KILL_EDOG_CAR = "byd.intent.action.KILL_EDOG_CAR";

    @Override
    public void onReceive(Context context, Intent intent) {
        if (KILL_EDOG_CAR.equals(intent.getAction())) {
            Log.i("KillBroadcastReceiver","action:"+intent.getAction());
//            context.sendBroadcast(new Intent(ABANDON_AUDIOFOCUS_ACTION));
            PlayerManagerHelper.getInstance().reset();
            Intent appIntent = new Intent(context, BYDWidgetService.class);
            appIntent.setAction(BYDWidgetService.WIDGET_ACTION_EXIT);
            context.startService(appIntent);
            AppManager.getInstance().killAll();
        }
    }
}
