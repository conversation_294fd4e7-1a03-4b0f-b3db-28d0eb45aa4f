package com.kaolafm.kradio.live.player;

public class ChatRoomMemberCache {

    private ChatRoomMemberCache() {
    }

    public static ChatRoomMemberCache getInstance() {
        return InstanceHolder.instance;
    }

    public void clear() {
    }

    /**
     * ************************************ 单例 ***************************************
     */
    private static class InstanceHolder {
        private final static ChatRoomMemberCache instance = new ChatRoomMemberCache();
    }

    /**
     * ********************************** 监听 ********************************
     */

    /**
     * ************************** 在线用户变化通知 ****************************
     */
}