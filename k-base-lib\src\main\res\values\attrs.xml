<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="TwinklingRefreshLayout">
        <attr name="tr_max_head_height" format="dimension"/>
        <attr name="tr_head_height" format="dimension"/>
        <attr name="tr_max_bottom_height" format="dimension"/>
        <attr name="tr_bottom_height" format="dimension"/>
        <attr name="tr_overscroll_height" format="dimension"/>
        <attr name="tr_enable_refresh" format="boolean"/>
        <attr name="tr_enable_loadmore" format="boolean"/>
        <attr name="tr_pureScrollMode_on" format="boolean"/>
        <attr name="tr_overscroll_top_show" format="boolean"/>
        <attr name="tr_overscroll_bottom_show" format="boolean"/>
        <attr name="tr_enable_overscroll" format="boolean"/>
        <attr name="tr_headerView" format="dimension"/>
        <attr name="tr_bottomView" format="dimension"/>
        <attr name="tr_floatRefresh" format="boolean"/>
        <attr name="tr_autoLoadMore" format="boolean"/>
        <attr name="tr_enable_keepIView" format="boolean"/>
        <attr name="tr_showRefreshingWhenOverScroll" format="boolean"/>
        <attr name="tr_showLoadingWhenOverScroll" format="boolean"/>
    </declare-styleable>
    <attr name="canScale" format="boolean" />
    <attr name="wh_ratio" format="string" />

    <declare-styleable name="SquareImageView">
        <attr name="canScale" />
    </declare-styleable>
    <declare-styleable name="SquareTextView">
        <attr name="canScale" />
    </declare-styleable>

    <declare-styleable name="SquareFrameLayout">
        <attr name="canScale" />
        <attr name="wh_ratio" />
    </declare-styleable>

    <declare-styleable name="SquareLinearLayout">
        <attr name="canScale" />
    </declare-styleable>

    <declare-styleable name="RatioConstraintLayout">
        <attr name="canScale" />
        <attr name="wh_ratio" />
    </declare-styleable>
    <declare-styleable name="RatioLinearLayout">
        <attr name="wh_ratio" />
    </declare-styleable>
    <declare-styleable name="ScaleTextView">
        <attr name="canScale" />
    </declare-styleable>

    <declare-styleable name="RatioTextView">
        <attr name="canScale" />
        <attr name="wh_ratio" />
    </declare-styleable>

    <attr name="tl_divider_color" format="color" />
    <attr name="tl_divider_padding" format="dimension" />
    <attr name="tl_divider_width" format="dimension" />
    <attr name="tl_indicator_anim_duration" format="integer" />
    <attr name="tl_indicator_anim_enable" format="boolean" />
    <attr name="tl_indicator_bounce_enable" format="boolean" />
    <attr name="tl_indicator_color" format="color" />
    <attr name="tl_indicator_corner_radius" format="dimension" />
    <attr name="tl_indicator_gravity" format="enum">
        <enum name="TOP" value="48" />
        <enum name="BOTTOM" value="80" />
    </attr>
    <attr name="tl_indicator_height" format="dimension" />
    <attr name="tl_indicator_margin_bottom" format="dimension" />
    <attr name="tl_indicator_margin_left" format="dimension" />
    <attr name="tl_indicator_margin_right" format="dimension" />
    <attr name="tl_indicator_margin_top" format="dimension" />
    <attr name="tl_indicator_style" format="enum">
        <enum name="NORMAL" value="0" />
        <enum name="TRIANGLE" value="1" />
        <enum name="BLOCK" value="2" />
        <enum name="TRIANGLE_INVERTED" value="3" />
        <enum name="CUSTOM_DRAWABLE" value="4" />
    </attr>
    <attr name="tl_indicator_width" format="dimension" />
    <attr name="tl_indicator_width_equal_title" format="boolean" />
    <attr name="tl_indicator_drawable" format="reference"/>
    <attr name="tl_tab_padding" format="dimension" />
    <attr name="tl_tab_space_equal" format="boolean" />
    <attr name="tl_tab_width" format="dimension" />
    <attr name="tl_textAllCaps" format="boolean" />
    <attr name="tl_textBold" format="enum">
        <enum name="NONE" value="0" />
        <enum name="SELECT" value="1" />
        <enum name="BOTH" value="2" />
    </attr>
    <attr name="tl_textSelectColor" format="color" />
    <attr name="tl_textUnselectColor" format="color" />
    <attr name="kradio_tl_textSize" format="dimension" />
    <attr name="kradio_tl_textSelectSize" format="dimension" />
    <attr name="tl_textSize" format="dimension" />
    <attr name="tl_textSelectSize" format="dimension" />
    <attr name="tl_underline_color" format="color" />
    <attr name="tl_underline_gravity" format="enum">
        <enum name="TOP" value="48" />
        <enum name="BOTTOM" value="80" />
    </attr>
    <attr name="tl_underline_height" format="dimension" />
    <attr name="tl_first_no_padding" format="boolean"/>

    <declare-styleable name="SlidingTabLayout">
        <attr name="android:background" />
        <attr name="tl_indicator_color" />
        <attr name="tl_indicator_height" />
        <attr name="tl_indicator_width" />
        <attr name="tl_indicator_margin_left" />
        <attr name="tl_indicator_margin_top" />
        <attr name="tl_indicator_margin_right" />
        <attr name="tl_indicator_margin_bottom" />
        <attr name="tl_indicator_corner_radius" />
        <attr name="tl_indicator_gravity" />
        <attr name="tl_indicator_style" />
        <attr name="tl_indicator_width_equal_title" />
        <attr name="tl_indicator_drawable"/>
        <attr name="tl_indicator_anim_enable"/>
        <attr name="tl_underline_color" />
        <attr name="tl_underline_height" />
        <attr name="tl_underline_gravity" />
        <attr name="tl_divider_color" />
        <attr name="tl_divider_width" />
        <attr name="tl_divider_padding" />
        <attr name="tl_tab_padding" />
        <attr name="tl_tab_space_equal" />
        <attr name="tl_tab_width" />
        <attr name="kradio_tl_textSize" />
        <attr name="kradio_tl_textSelectSize" />
        <attr name="tl_textSize" />
        <attr name="tl_textSelectSize" />
        <attr name="tl_textSelectColor" />
        <attr name="tl_textUnselectColor" />
        <attr name="tl_textBold" />
        <attr name="tl_textAllCaps" />
        <attr name="tl_first_no_padding"/>

    </declare-styleable>
    <declare-styleable name="SlidingRecycleTabLayout">
        <attr name="tl_indicator_drawable"/>
    </declare-styleable>
    <!-- smartrefresh -->
    <!--<attr name="srlStyle" format="reference"/>&lt;!&ndash;样式&ndash;&gt;-->

    <attr name="srlDrawableSize" format="dimension"/><!--图片尺寸-->
    <attr name="srlDrawableArrowSize" format="dimension"/><!--箭头图片尺寸-->
    <attr name="srlDrawableProgressSize" format="dimension"/><!--箭头图片尺寸-->
    <attr name="srlDrawableMarginRight" format="dimension"/><!--图片和文字的间距-->
    <attr name="srlTextSizeTitle" format="dimension"/><!--标题字体-->
    <attr name="srlTextSizeTime" format="dimension"/><!--时间字体-->
    <attr name="srlFinishDuration" format="integer"/><!--完成时停留时间-->
    <attr name="srlPrimaryColor" format="color"/><!--主要颜色-->
    <attr name="srlAccentColor" format="color"/><!--强调颜色-->
    <attr name="srlDrawableArrow" format="reference"/><!--箭头图片-->
    <attr name="srlDrawableProgress" format="reference"/><!--转动图片-->
    <attr name="srlEnableHorizontalDrag" format="boolean"/><!--支持水平拖动-->

    <attr name="srlTextPulling" format="string"/>
    <attr name="srlTextLoading" format="string"/>
    <attr name="srlTextRelease" format="string"/>
    <attr name="srlTextFinish" format="string"/>
    <attr name="srlTextFailed" format="string"/>
    <attr name="srlTextUpdate" format="string"/>
    <attr name="srlTextSecondary" format="string"/>
    <attr name="srlTextRefreshing" format="string"/>

    <!--<attr name="srlTextPulling" format="string" />-->
    <!--<attr name="srlTextRelease" format="string" />-->
    <!--<attr name="srlTextLoading" format="string" />-->
    <!--<attr name="srlTextRefreshing" format="string" />-->
    <!--<attr name="srlTextFinish" format="string" />-->
    <!--<attr name="srlTextFailed" format="string" />-->
    <attr name="srlTextNothing" format="string" />

    <attr name="srlClassicsSpinnerStyle" format="enum">
        <enum name="Translate" value="0"/><!--平行移动-->
        <enum name="Scale" value="1"/><!--拉伸形变-->
        <enum name="FixedBehind" value="2"/><!--固定在背后-->
    </attr>

    <attr name="layout_srlSpinnerStyle" format="enum">
        <enum name="Translate" value="0"/><!--平行移动-->
        <enum name="Scale" value="1"/><!--拉伸形变-->
        <enum name="FixedBehind" value="2"/><!--固定在背后-->
        <enum name="FixedFront" value="3"/><!--固定在前面-->
        <enum name="MatchLayout" value="4"/><!--填满布局-->
    </attr>

    <declare-styleable name="SmartRefreshLayout">
        <attr name="android:clipChildren"/>
        <attr name="android:clipToPadding"/>
        <attr name="srlPrimaryColor"/>
        <attr name="srlAccentColor"/>
        <attr name="srlReboundDuration" format="integer"/>
        <attr name="srlHeaderHeight" format="dimension"/>
        <attr name="srlFooterHeight" format="dimension"/>
        <attr name="srlHeaderInsetStart" format="dimension"/>
        <attr name="srlFooterInsetStart" format="dimension"/>
        <attr name="srlDragRate" format="float"/>
        <attr name="srlHeaderMaxDragRate" format="float"/>
        <attr name="srlFooterMaxDragRate" format="float"/>
        <attr name="srlHeaderTriggerRate" format="float"/>
        <attr name="srlFooterTriggerRate" format="float"/>
        <attr name="srlEnableRefresh" format="boolean"/>
        <attr name="srlEnableLoadMore" format="boolean"/>
        <attr name="srlEnableHeaderTranslationContent" format="boolean"/>
        <attr name="srlEnableFooterTranslationContent" format="boolean"/>
        <attr name="srlHeaderTranslationViewId" format="reference"/>
        <attr name="srlFooterTranslationViewId" format="reference"/>
        <attr name="srlEnablePreviewInEditMode" format="boolean"/>
        <attr name="srlEnableAutoLoadMore" format="boolean"/>
        <attr name="srlEnableOverScrollBounce" format="boolean"/>
        <attr name="srlEnablePureScrollMode" format="boolean"/>
        <attr name="srlEnableNestedScrolling" format="boolean"/>
        <attr name="srlEnableScrollContentWhenLoaded" format="boolean"/>
        <attr name="srlEnableScrollContentWhenRefreshed" format="boolean"/>
        <attr name="srlEnableLoadMoreWhenContentNotFull" format="boolean"/>
        <attr name="srlEnableFooterFollowWhenLoadFinished" format="boolean"/>
        <attr name="srlEnableFooterFollowWhenNoMoreData" format="boolean"/>
        <attr name="srlEnableClipHeaderWhenFixedBehind" format="boolean"/>
        <attr name="srlEnableClipFooterWhenFixedBehind" format="boolean"/>
        <attr name="srlEnableOverScrollDrag" format="boolean"/>
        <attr name="srlDisableContentWhenRefresh" format="boolean"/>
        <attr name="srlDisableContentWhenLoading" format="boolean"/>
        <attr name="srlFixedHeaderViewId" format="reference"/>
        <attr name="srlFixedFooterViewId" format="reference"/>
    </declare-styleable>

    <declare-styleable name="SmartRefreshLayout_Layout">
        <attr name="layout_srlSpinnerStyle"/>
        <attr name="layout_srlBackgroundColor" format="color"/>
    </declare-styleable>

    <declare-styleable name="BezierRadarHeader">
        <attr name="srlPrimaryColor"/>
        <attr name="srlAccentColor"/>
        <attr name="srlEnableHorizontalDrag"/>
    </declare-styleable>

    <declare-styleable name="BallPulseFooter">
        <attr name="srlClassicsSpinnerStyle"/>
        <attr name="srlAnimatingColor" format="color"/>
        <attr name="srlNormalColor" format="color"/>
    </declare-styleable>

    <declare-styleable name="ClassicsHeader">
        <attr name="srlClassicsSpinnerStyle"/>
        <attr name="srlPrimaryColor"/>
        <attr name="srlAccentColor"/>
        <attr name="srlFinishDuration"/>
        <attr name="srlDrawableArrow"/>
        <attr name="srlDrawableProgress"/>
        <attr name="srlDrawableMarginRight"/>
        <attr name="srlDrawableSize"/>
        <attr name="srlDrawableArrowSize"/>
        <attr name="srlDrawableProgressSize"/>
        <attr name="srlTextSizeTitle"/>
        <attr name="srlTextSizeTime"/>
        <attr name="srlTextTimeMarginTop" format="dimension"/>
        <attr name="srlEnableLastTime" format="boolean"/>

        <attr name="srlTextPulling"/>
        <attr name="srlTextLoading"/>
        <attr name="srlTextRelease"/>
        <attr name="srlTextFinish"/>
        <attr name="srlTextFailed"/>
        <attr name="srlTextUpdate"/>
        <attr name="srlTextSecondary"/>
        <attr name="srlTextRefreshing"/>

    </declare-styleable>

    <declare-styleable name="ClassicsFooter">
        <attr name="srlClassicsSpinnerStyle"/>
        <attr name="srlPrimaryColor"/>
        <attr name="srlAccentColor"/>
        <attr name="srlFinishDuration"/>
        <attr name="srlTextSizeTitle"/>
        <attr name="srlDrawableArrow"/>
        <attr name="srlDrawableProgress"/>
        <attr name="srlDrawableMarginRight"/>
        <attr name="srlDrawableSize"/>
        <attr name="srlDrawableArrowSize"/>
        <attr name="srlDrawableProgressSize"/>

        <attr name="srlTextPulling"/>
        <attr name="srlTextRelease"/>
        <attr name="srlTextLoading"/>
        <attr name="srlTextRefreshing"/>
        <attr name="srlTextFinish"/>
        <attr name="srlTextFailed"/>
        <attr name="srlTextNothing"/>

    </declare-styleable>

    <declare-styleable name="TwoLevelHeader">
        <attr name="srlMaxRage" format="float"/>
        <attr name="srlFloorRage" format="float"/>
        <attr name="srlRefreshRage" format="float"/>
        <attr name="srlFloorDuration" format="integer"/>
        <attr name="srlEnableTwoLevel" format="boolean"/>
        <attr name="srlEnablePullToCloseTwoLevel" format="boolean"/>
    </declare-styleable>
    <!-- end smartrefresh -->
    <declare-styleable name="CircularProgressBar">
        <attr name="cpbStyle" format="reference"/>
        <attr name="cpb_color" format="color"/>
        <attr name="cpb_colors" format="reference"/>
        <attr name="cpb_stroke_width" format="dimension"/>
        <attr name="cpb_min_sweep_angle" format="integer"/>
        <attr name="cpb_max_sweep_angle" format="integer"/>
        <attr name="cpb_sweep_speed" format="float"/>
        <attr name="cpb_rotation_speed" format="float"/>
    </declare-styleable>
</resources>