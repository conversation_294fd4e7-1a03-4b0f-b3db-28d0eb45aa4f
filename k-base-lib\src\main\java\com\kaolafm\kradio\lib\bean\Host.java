package com.kaolafm.kradio.lib.bean;

import android.os.Parcel;
import android.os.Parcelable;


public class Host implements Parcelable {
    private String oid;
    private String des;
    private String img;
    private String name;

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getOid() {
        return this.oid;
    }
    public void setDes(String des) {
        this.des = des;
    }

    public String getDes() {
        return this.des;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getImg() {
        return this.img;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return this.name;
    }

    public Host() {
    }

    private Host(Parcel in) {
        this.des = in.readString();
        this.img = in.readString();
        this.oid = in.readString();
        this.name = in.readString();
    }

    public static final Creator<Host> CREATOR = new Creator<Host>() {
        public Host createFromParcel(Parcel source) {
            return new Host(source);
        }

        public Host[] newArray(int size) {
            return new Host[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(des);
        dest.writeString(img);
        dest.writeString(oid);
        dest.writeString(name);
    }
}
