package com.kaolafm.kradio.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.utils.SharedPreferenceUtil;

/**
 * Created by <PERSON><PERSON>l on 2018/3/5.
 */

public class BootBroadcastReceiver extends BroadcastReceiver {

    @Override
    public void onReceive(Context context, Intent intent) {
        Log.i("BootBroadcastReceiver", "BootBroadcastReceiver:" + intent.getAction());
        if (Intent.ACTION_BOOT_COMPLETED.equals(intent.getAction())) {
            //byd3.5UI版本不需要开机自启
//            BootCompletedLogicListener bootCompletedLogicListener = InjectManager.getInstance().getBootCompletedLogicListener();
//            if (bootCompletedLogicListener != null) {
//                bootCompletedLogicListener.onBootCompletedLogic(context,intent);
//            }

            // 存储一个变量，用来判断应用是否是首次启动
            try {
                Context ctx = AppManager.getInstance().getCurrentActivity();
                if (ctx == null) {
                    ctx = context;
                }
                if (ctx == null) {
                    ctx = AppDelegate.getInstance().getContext();
                }
                SharedPreferenceUtil sP = SharedPreferenceUtil.getInstance(ctx,
                        "k_os_boot_complete",
                        Context.MODE_PRIVATE);
                if (sP != null) {
                    sP.putBoolean("isFirstStart", true);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
