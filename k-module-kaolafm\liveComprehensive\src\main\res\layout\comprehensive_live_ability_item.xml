<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:minHeight="@dimen/comprehensive_live_bottom_height">


    <RelativeLayout
        android:id="@+id/live_ability"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginTop="@dimen/comprehensive_live_bottom_margin_top"
        android:layout_marginBottom="@dimen/comprehensive_live_bottom_margin_bottom"
        android:layout_marginHorizontal="@dimen/comprehensive_live_bottom_margin_horizontal"
        android:layout_width="@dimen/comprehensive_live_bottom_item_width"
        android:layout_height="@dimen/comprehensive_live_bottom_item_width"
        android:gravity="center"
        android:background="@drawable/comprehensive_live_bottom_btn_bg">

        <ImageView
            android:id="@+id/live_ability_img"
            android:layout_width="@dimen/comprehensive_live_bottom_item_img_width"
            android:layout_height="@dimen/comprehensive_live_bottom_item_img_width"
            android:scaleType="fitXY"
            android:src="@drawable/comprehensive_live_bottom_gift_img"/>

    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>