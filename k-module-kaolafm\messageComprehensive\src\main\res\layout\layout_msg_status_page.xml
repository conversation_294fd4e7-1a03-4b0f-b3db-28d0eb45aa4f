<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/each_status_page_main_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:background="@drawable/bg_home">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_width="@dimen/m360"
            android:layout_height="@dimen/m142"
            android:src="@drawable/ic_msg_no_list" />

        <TextView
            android:layout_gravity="center_horizontal"
            android:id="@+id/tv_status_page_network_error"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/y32"
            android:text="当前暂无消息"
            android:textColor="@color/text_color_7"
            android:textSize="@dimen/text_size4" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
