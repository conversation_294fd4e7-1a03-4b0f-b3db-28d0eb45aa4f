<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:visibility="gone"
    android:id="@+id/live_message_item_hello"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/live_screen_welcome_me_enter"
    android:paddingVertical="@dimen/comprehensive_live_message_margin_vertical"
    android:paddingHorizontal="@dimen/comprehensive_live_message_margin_horizontal"
    android:minHeight="@dimen/comprehensive_live_message_min_height">

    <LinearLayout
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/helloStartTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/comprehensive_live_message_normal_color"
            android:textSize="@dimen/comprehensive_live_message_text_size"
            android:text="欢迎"/>

        <TextView
            android:id="@+id/nameTv"
            android:layout_marginHorizontal="@dimen/m16"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:maxLines="1"
            android:ellipsize="end"
            android:textColor="@color/comprehensive_live_message_system_color"
            android:textSize="@dimen/comprehensive_live_message_text_size"
            tools:text="合肥撒的空间发挥的书房空间大还是分开就撒到符号的撒谎飞机卡换地积分卡戴珊飞机快乐的啥饭了和弗拉索夫客户接口方了"/>

        <TextView
            android:id="@+id/helloEndTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/comprehensive_live_message_normal_color"
            android:textSize="@dimen/m24"
            android:text="进入直播间"/>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>