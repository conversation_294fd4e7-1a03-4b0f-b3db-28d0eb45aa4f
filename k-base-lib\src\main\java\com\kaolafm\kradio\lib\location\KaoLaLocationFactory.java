package com.kaolafm.kradio.lib.location;

import com.kaolafm.kradio.lib.base.flavor.CustomizeLocationInter;
import com.kaolafm.kradio.lib.location.model.GaoDeLocation;
import com.kaolafm.kradio.lib.location.model.GoogleLocation;
import com.kaolafm.kradio.lib.location.model.IKaoLaLocation;
import com.kaolafm.kradio.lib.location.model.LocationModel;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;

import java.util.concurrent.ConcurrentHashMap;

/**
 * @ClassName KaoLaLocationFactory
 * @Description 获取经纬度工厂, 暂时只支持 google 与
 * <AUTHOR>
 * @Date 2020/5/26 14:52
 * @Version 1.0
 */
public class KaoLaLocationFactory {
    public static final String GOOGLE_API = "1";
    public static final String GAODE_API = "2";
    public static final String CUSTOMIZED_API = "3";

    private static ConcurrentHashMap mLocationMap = new ConcurrentHashMap();

    public static IKaoLaLocation getLocationManager(String type) {
        IKaoLaLocation laLocation = (IKaoLaLocation) mLocationMap.get(type);
        if (laLocation != null) {
            LocationModel location = laLocation.getLocation();
            //fixed 初始化值为0表示初始化失败，需要重新获取
            if (location!=null){
                double longitude =  location.getLongitude();
                double latitude = location.getLatitude();
                if (longitude!=0 && latitude!=0){
                    return laLocation;
                }
            }

        }
        if (GOOGLE_API.equals(type)) {

            laLocation = new GoogleLocation();

        } else if (CUSTOMIZED_API.equals(type)) {

            CustomizeLocationInter customizeLocationInter = ClazzImplUtil.getInter("CustomizeLocationInterImpl");
            laLocation = customizeLocationInter.get();

        } else {

            laLocation = new GaoDeLocation();

        }
        mLocationMap.put(type, laLocation);
        return laLocation;
    }
}
