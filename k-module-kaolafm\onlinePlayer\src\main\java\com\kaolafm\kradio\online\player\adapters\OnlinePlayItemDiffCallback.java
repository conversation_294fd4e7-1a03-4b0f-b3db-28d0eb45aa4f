package com.kaolafm.kradio.online.player.adapters;

import androidx.recyclerview.widget.DiffUtil;

import com.kaolafm.opensdk.player.logic.model.item.AlbumPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.BroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import java.util.List;

public class OnlinePlayItemDiffCallback extends DiffUtil.Callback {
    private List<PlayItem> mOldDatas, mNewDatas;//看名字

    public OnlinePlayItemDiffCallback(List<PlayItem> mOldDatas, List<PlayItem> mNewDatas) {
        this.mOldDatas = mOldDatas;
        this.mNewDatas = mNewDatas;
    }

    @Override
    public int getOldListSize() {
        return mOldDatas != null ? mOldDatas.size() : 0;
    }

    @Override
    public int getNewListSize() {
        return mNewDatas != null ? mNewDatas.size() : 0;
    }

    @Override
    public boolean areItemsTheSame(int oldItemPosition, int newItemPosition) {
        return mOldDatas.get(oldItemPosition).getAudioId() == mNewDatas.get(newItemPosition).getAudioId();
    }

    @Override
    public boolean areContentsTheSame(int oldItemPosition, int newItemPosition) {
        PlayItem beanOld = mOldDatas.get(oldItemPosition);
        PlayItem beanNew = mNewDatas.get(newItemPosition);
        if (beanOld.getStatus() != beanNew.getStatus()) {
            return false;
        }
        if (beanOld.getHost() == null && beanNew.getHost() != null) {
            return false;
        }
        if (beanOld.getHost() != null && beanNew.getHost() == null) {
            return false;
        }
        if (beanOld.getHost() != null && beanNew.getHost() != null && !beanOld.getHost().equals(beanNew.getHost())) {
            return false;
        }
        if (!beanOld.getBeginTime().equals(beanNew.getBeginTime())) {
            return false;
        }
        if (!beanOld.getEndTime().equals(beanNew.getEndTime())) {
            return false;
        }
        if (beanOld instanceof BroadcastPlayItem && beanNew instanceof BroadcastPlayItem &&
                ((BroadcastPlayItem) beanOld).getInfoData().getIsLiked() != ((BroadcastPlayItem) beanNew).getInfoData().getIsLiked()) {
            return false;
        }

        if (beanOld instanceof AlbumPlayItem && beanNew instanceof AlbumPlayItem) {
            AlbumPlayItem beanOld1 = (AlbumPlayItem) beanOld;
            AlbumPlayItem beanNew1 = (AlbumPlayItem) beanNew;
            if (beanOld1.getInfoData().getIsLiked() != beanNew1.getInfoData().getIsLiked()
                    || beanOld1.getBuyType() != beanNew1.getBuyType()
                    || beanOld1.getBuyStatus() != beanNew1.getBuyStatus()
                    || beanOld1.getAudition() != beanNew1.getAudition()
                    || beanOld1.getVip() != beanNew1.getVip()
                    || beanOld1.getFine() != beanNew1.getFine()
            ) {
                return false;
            }
        }
        return true;
    }
}
