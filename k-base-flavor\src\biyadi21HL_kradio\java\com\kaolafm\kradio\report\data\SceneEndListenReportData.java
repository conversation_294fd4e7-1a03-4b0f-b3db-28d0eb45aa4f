package com.kaolafm.kradio.report.data;

import com.kaolafm.report.event.EndListenReportEvent;

import java.util.UUID;

import static com.kaolafm.kradio.uitl.Constants.RADIO_PLAY_END_EVENT_CODE;
import static com.kaolafm.kradio.uitl.Constants.SCENE_REMARK_VALUE;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-03-04 20:14
 ******************************************/
public class SceneEndListenReportData extends EndListenReportEvent {

    public SceneEndListenReportData(EndListenReportEvent endListenReportEvent) {
        super();
        setEventcode(RADIO_PLAY_END_EVENT_CODE);
        setRemarks8(SCENE_REMARK_VALUE);

        setAudioid(endListenReportEvent.getAudioid());
        setRadioid(endListenReportEvent.getRadioid());
        setAlbumid(endListenReportEvent.getAlbumid());
        setType(endListenReportEvent.getType());
        setPosition(endListenReportEvent.getPosition());
        setPlaytime(endListenReportEvent.getPlaytime());
        setPlayrate(endListenReportEvent.getPlayrate());
        setLength(endListenReportEvent.getLength());
        setRemarks4(endListenReportEvent.getRemarks4());
        setRemarks6(endListenReportEvent.getRemarks6());
        setRemarks7(endListenReportEvent.getRemarks7());
        setRemarks9(endListenReportEvent.getRemarks9());
        setAi_mz_location(endListenReportEvent.getAi_mz_location());
        setRemarks10(endListenReportEvent.getRemarks10());
        setSource(endListenReportEvent.getSource());
        setRemarks11(endListenReportEvent.getRemarks11());
    }

    /**
     * 场景推送session
     */
    private String cjsession = UUID.randomUUID().toString();

    /**
     * 场景内容code
     */
    private String cjcode;

    /**
     * 运营类型
     */
    private String cjtype;

    public String getCjsession() {
        return cjsession;
    }

    public void setCjsession(String cjsession) {
        this.cjsession = cjsession;
    }

    public String getCjcode() {
        return cjcode;
    }

    public void setCjcode(String cjcode) {
        this.cjcode = cjcode;
    }

    public String getCjtype() {
        return cjtype;
    }

    public void setCjtype(String cjtype) {
        this.cjtype = cjtype;
    }
}
