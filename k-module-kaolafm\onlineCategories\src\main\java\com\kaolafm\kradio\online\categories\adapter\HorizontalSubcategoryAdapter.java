package com.kaolafm.kradio.online.categories.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.kaolafm.kradio.common.SubcategoryItemBean;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.flavor.KRadioPicSettingInter;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.sdk.utils.PerformanceSettingMananger;
import com.kaolafm.kradio.lib.utils.ChannelSetingUtil;
import com.kaolafm.kradio.lib.utils.FlavorImplName;
import com.kaolafm.kradio.online.categories.holder.HorizontalSubscriptionViewHolder;
import com.kaolafm.kradio.online.categories.holder.HorizontalTitleViewHolder;

/**
 * 具体每一页二级分类的adapter
 *
 * <AUTHOR>
 * @date 2018/4/25
 */

public class HorizontalSubcategoryAdapter extends SubcategoryAdapter {

    private KRadioPicSettingInter mPicSetting;

    public HorizontalSubcategoryAdapter() {
        super();
        mPicSetting = (KRadioPicSettingInter) ChannelSetingUtil.getChannelImpl(FlavorImplName.QICHENRICHANSETTINGPIC);
    }


    @Override
    protected BaseHolder<SubcategoryItemBean> getViewHolder(ViewGroup parent, int viewType) {
        BaseHolder<SubcategoryItemBean> subcategoryItemBeanBaseHolder;
        switch (viewType) {
            case SubcategoryItemBean.TYPE_ITEM_TITLE: {// AI电台
                View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.online_item_subcategory_title_horizontal, parent, false);
                subcategoryItemBeanBaseHolder = new HorizontalTitleViewHolder(view);
            }
            break;
            case SubcategoryItemBean.TYPE_ITEM_BROADCAST_CATEGORY:// 在线广播-省市台，按分类
            case SubcategoryItemBean.TYPE_ITEM_BROADCAST_LOCAL: // 在线广播-本地广播
            case SubcategoryItemBean.TYPE_ITEM_TV: {// 听电视
                View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.online_item_subcategory_my_subscription_broadcast_horizontal, parent, false);
                subcategoryItemBeanBaseHolder = new HorizontalSubscriptionViewHolder(view, mPicSetting);
            }
            break;
            case SubcategoryItemBean.TYPE_ITEM_RADIO_CHANNEL:// AI电台or场景电台
            case SubcategoryItemBean.TYPE_ITEM_ALBUM:{  // 专栏节目
                View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.online_item_subcategory_my_subscription_horizontal, parent, false);
                subcategoryItemBeanBaseHolder = new HorizontalSubscriptionViewHolder(view, mPicSetting);
        }
        break;
//            case SubcategoryItemBean.TYPE_ITEM_BROADCAST_LOCAL: {// 在线广播-本地广播
//                View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_subcategory_broadcast_local_horizontal, parent, false);
//                subcategoryItemBeanBaseHolder = new HorizontalBroadcastLocalViewHolder(view);
//            }
//            break;
//            case SubcategoryItemBean.TYPE_ITEM_BROADCAST_CATEGORY: {// 在线广播-省市台，按分类
//                View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.online_item_subcategory_broadcast_category_horizontal, parent, false);
//                subcategoryItemBeanBaseHolder = new BroadcastCategoryViewHolder(view);
//            }
//            break;
//            case SubcategoryItemBean.TYPE_ITEM_RADIO_CHANNEL: {// AI电台
//                View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_subcategory_radio_channel_horizontal, parent, false);
//                subcategoryItemBeanBaseHolder = new RadioChannelViewHolder(view, mPicSetting);
//            }
//            break;
        default: {
            subcategoryItemBeanBaseHolder = super.getViewHolder(parent, viewType);
        }
        break;
    }

    //根据不同配置画不同尺寸图片
        if(subcategoryItemBeanBaseHolder !=null)

    {
        int imgSize = PerformanceSettingMananger.getInstance()
                .getImgSize(PerformanceSettingMananger.catImgSizeMap);
        subcategoryItemBeanBaseHolder.setImgSize(imgSize);
    }

        return subcategoryItemBeanBaseHolder;

}


}
