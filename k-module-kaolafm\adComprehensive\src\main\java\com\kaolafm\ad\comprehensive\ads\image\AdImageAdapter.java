package com.kaolafm.ad.comprehensive.ads.image;

import android.content.Context;

import com.kaolafm.ad.comprehensive.ads.image.base.BaseAdImageAdapter;
import com.kaolafm.ad.comprehensive.ads.image.base.BaseAdContentView;

public class AdImageAdapter extends BaseAdImageAdapter<AdContentInfo> {

    @Override
    public BaseAdContentView<AdContentInfo> onCreateAdView(Context context) {
        return new AdImageView(context);
    }

    @Override
    public void onBindAdView(BaseAdContentView<AdContentInfo> baseAdContentView, AdContentInfo adContentInfo) {
        baseAdContentView.loadAdContent(adContentInfo);
    }
}
