package com.kaolafm.kradio.lib.ti;

import androidx.lifecycle.Lifecycle;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import androidx.fragment.app.Fragment;
import android.util.Log;

import java.lang.ref.WeakReference;

public class HideBroadcastReceiver extends BroadcastReceiver {
    public static final String ACTION = "com.kaolafm.kradio.action.ON_HIDDEN_CHANGED";
    public static final String HIDDEN = "extra_idden";
    public static final String TAG = "tag";
    private final WeakReference<TouchInterceptorObserver> refTIO;

    public HideBroadcastReceiver(TouchInterceptorObserver touchInterceptorObserver) {
        this.refTIO = new WeakReference<>(touchInterceptorObserver);
    }

    @Override
    public void onReceive(Context context, Intent intent) {

        TouchInterceptorObserver tio = this.refTIO.get();
        if (tio == null) {
            return;
        }

        Fragment fragment = (Fragment) tio.owner;
        if (fragment == null) {
            return;
        }
        String fragmentTag = fragment.getTag();

        String tag = intent.getStringExtra(TAG);
        if (tag != null && tag.equals(fragmentTag)) {
            boolean hidden = intent.getBooleanExtra(HIDDEN, false);
            Lifecycle.Event event = hidden ? Lifecycle.Event.ON_PAUSE : Lifecycle.Event.ON_RESUME;
            tio.onStateChanged(tio.owner, event);

            Log.i("k.ti.o", "onReceive: hidden=" + hidden);
            Log.i("k.ti.o", "         : hidden=" + hidden);
            Log.i("k.ti.o", "         : tag=" + fragmentTag);
        }
    }
}