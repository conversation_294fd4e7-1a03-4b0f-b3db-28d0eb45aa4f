package com.kaolafm.kradio.huawei.convert;

import android.util.Log;

import com.huawei.carmediakit.bean.MediaElement;
import com.kaolafm.kradio.common.bean.HistoryItem;
import com.kaolafm.kradio.huawei.utils.Constant;

import java.util.ArrayList;
import java.util.List;

import static com.kaolafm.kradio.huawei.utils.MediaIdHelper.getMediaId;

public class HistoryConvertUtil {

    private static final String TAG = Constant.TAG;

    public static MediaElement toMediaElement(HistoryItem listeningHistory) {
        MediaElement mediaElement = new MediaElement();
        mediaElement.setPlaying(false);
        if (listeningHistory == null) {
            return mediaElement;
        }

        Log.i(TAG, "title=" + listeningHistory.getType() + ":url="
                + listeningHistory.getPicUrl());
        mediaElement.setName(listeningHistory.getRadioTitle());
        mediaElement.setMediaId(getMediaId(listeningHistory));
        mediaElement.setCoverUrl(listeningHistory.getPicUrl());
        mediaElement.setElementType(MediaElement.ElementType.ALBUM);
        mediaElement.setDesp(listeningHistory.getAudioTitle());

        return mediaElement;

    }

    public static List<MediaElement> toMediaElementList(List<HistoryItem> historyList) {
        List<MediaElement> mediaElementList = new ArrayList<>();
        MediaElement mediaElement;
        for (HistoryItem history: historyList) {
            mediaElement = toMediaElement(history);
            mediaElementList.add(mediaElement);
        }

        return mediaElementList;
    }
}
