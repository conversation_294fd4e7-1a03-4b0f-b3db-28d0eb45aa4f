package com.kaolafm.kradio.flavor.impl;
import com.kaolafm.kradio.flavor.impl.DebugImpl;
import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.util.Log;

import com.kaolafm.kradio.lib.base.flavor.NetworkStatusInter;
import com.kaolafm.kradio.lib.utils.NetworkManager;

public class NetworkStatusInterImpl implements NetworkStatusInter {
    @Override
    public boolean getNetStatus(Context context) {
        try {
            ConnectivityManager connectivity = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            if (connectivity != null) {
                boolean isNetOn = connectivity.getNetworkStatus();
                Log.d("NetworkStatusInterImpl","status = "+isNetOn);
//                isNetOn = true;
                if (DebugImpl.isDebug()) {
                    Log.d("NetworkStatusInterImpl", "status isDebug= " + isNetOn);
                    return true;
                }
                return isNetOn;
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
        if (DebugImpl.isDebug()) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public String[] netAction() {
        return new String[]{/*ConnectivityManager.ACTION_NETWORK_CHANGED*/};
    }
}
