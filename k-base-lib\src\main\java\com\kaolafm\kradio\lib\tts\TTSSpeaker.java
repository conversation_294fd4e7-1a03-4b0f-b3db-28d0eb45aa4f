package com.kaolafm.kradio.lib.tts;

import android.content.Context;
import android.os.Bundle;
import android.os.Environment;
import android.util.Log;

import com.iflytek.cloud.ErrorCode;
import com.iflytek.cloud.SpeechConstant;
import com.iflytek.cloud.SpeechError;
import com.iflytek.cloud.SpeechSynthesizer;
import com.iflytek.cloud.SpeechUtility;
import com.iflytek.cloud.SynthesizerListener;

/**
 * <AUTHOR>
 **/
public class TTSSpeaker {

    private static final String TAG = "kradio.tts";

    private SpeechSynthesizer mTts;
    // 引擎类型
    private String mEngineType = SpeechConstant.TYPE_CLOUD;

    /***************************************************************************************************************/

    private static TTSSpeaker mInstance;

    private TTSSpeaker() {

    }

    public static TTSSpeaker getInstance() {
        if (mInstance == null) {
            synchronized (TTSSpeaker.class) {
                if (mInstance == null) {
                    mInstance = new TTSSpeaker();
                }
            }
        }

        return mInstance;
    }


    /**
     * @param context
     * @param appId
     *************************************************************************************************************/

    public void attachApp(Context context, String appId) {
        SpeechUtility.createUtility(context, "appid=" + appId);
    }

    public void initTTS(Context context, TTSSpeaker.InitListener listener) {
        mTts = SpeechSynthesizer.createSynthesizer(context, new WapperInitListener(listener));
    }

    /**
     * @param voicer     发音人
     * @param speed      语速
     * @param pitch      音调
     * @param volume     音量
     * @param streamType 音频流类型
     */
    public void setParam(String voicer, int speed, int pitch, int volume, int streamType) {
        // 清空参数
        mTts.setParameter(SpeechConstant.PARAMS, null);
        // 根据合成引擎设置相应参数
        if (mEngineType.equals(SpeechConstant.TYPE_CLOUD)) {
            mTts.setParameter(SpeechConstant.ENGINE_TYPE, SpeechConstant.TYPE_CLOUD);
            mTts.setParameter(SpeechConstant.TTS_DATA_NOTIFY, "1");
            // 设置在线合成发音人
            mTts.setParameter(SpeechConstant.VOICE_NAME, voicer);
            //设置合成语速
            mTts.setParameter(SpeechConstant.SPEED, String.valueOf(speed));
            //设置合成音调
            mTts.setParameter(SpeechConstant.PITCH, String.valueOf(pitch));
            //设置合成音量
            mTts.setParameter(SpeechConstant.VOLUME, String.valueOf(volume));
        } else {
            mTts.setParameter(SpeechConstant.ENGINE_TYPE, SpeechConstant.TYPE_LOCAL);
            // 设置本地合成发音人 voicer为空，默认通过语记界面指定发音人。
            mTts.setParameter(SpeechConstant.VOICE_NAME, "");
        }
        //设置播放器音频流类型
        mTts.setParameter(SpeechConstant.STREAM_TYPE, String.valueOf(streamType));
        // 设置播放合成音频打断音乐播放，默认为true
        mTts.setParameter(SpeechConstant.KEY_REQUEST_FOCUS, "true");

        // 设置音频保存路径，保存音频格式支持pcm、wav，设置路径为sd卡请注意WRITE_EXTERNAL_STORAGE权限
        // 注：AUDIO_FORMAT参数语记需要更新版本才能生效
        mTts.setParameter(SpeechConstant.AUDIO_FORMAT, "pcm");
        mTts.setParameter(SpeechConstant.TTS_AUDIO_PATH, Environment.getExternalStorageDirectory() + "/msc/tts.pcm");
    }

    /**
     * 播放
     *
     * @param text             语音文本
     * @param ttsSpeakListener 监听
     */
    public void speak(String text, TTSSpeakListener ttsSpeakListener) {
        Log.i(TAG, "调用tts播放: " + text);
        if (mTts == null) {
            return;
        }
        int code = mTts.startSpeaking(text, new WapperSynthesizerListener(ttsSpeakListener));
        Log.i(TAG, "调用tts播放: 结果: " + code);
        if (code != ErrorCode.SUCCESS) {
            ttsSpeakListener.onSuccess();
        } else {
            ttsSpeakListener.onError(code);
        }
    }


    /**
     * 暂停
     */
    public void pause() {
        if (mTts != null) {
            mTts.pauseSpeaking();
        }
    }

    /**
     * 暂停后继续播放
     */
    public void resume() {
        if (mTts != null) {
            mTts.resumeSpeaking();
        }
    }

    /**
     * 停止
     */
    public void stop() {
        if (mTts != null) {
            mTts.stopSpeaking();
        }
    }

    /**
     * 范围0-100
     *
     * @param volume
     */
    public void setVolume(int volume) {
        //设置合成音量[0,100]
        if (mTts != null) {
            mTts.setParameter(SpeechConstant.VOLUME, String.valueOf(volume));
        }
    }

    /***************************************************************************************************************/
    /**
     * 多加了2个结果回调
     */
    public interface TTSSpeakListener {

        void onSuccess();

        void onError(int code);

        void onSpeakBegin();

        void onBufferProgress(int var1, int var2, int var3, String var4);

        void onSpeakPaused();

        void onSpeakResumed();

        void onSpeakProgress(int var1, int var2, int var3);

        void onCompleted(Exception e);

        void onEvent(int var1, int var2, int var3, Bundle var4);
    }

    /**
     * 初始化回调
     */
    public interface InitListener {
        void onInit(int var1);
    }

    /************************************************* 基础类 **************************************************************/
    public static class BaseTTSSpeakListener implements TTSSpeakListener {
        @Override
        public void onSuccess() {
            Log.i(TAG, "onSuccess: ");
        }

        @Override
        public void onError(int code) {
            Log.i(TAG, "onError: " + code);
        }

        @Override
        public void onSpeakBegin() {
            Log.i(TAG, "onSpeakBegin: ");
        }

        @Override
        public void onBufferProgress(int var1, int var2, int var3, String var4) {
            Log.i(TAG, "onBufferProgress: ");
        }

        @Override
        public void onSpeakPaused() {
            Log.i(TAG, "onSpeakPaused: ");
        }

        @Override
        public void onSpeakResumed() {
            Log.i(TAG, "onSpeakResumed: ");
        }

        @Override
        public void onSpeakProgress(int var1, int var2, int var3) {
            Log.i(TAG, "onSpeakProgress: ");
        }

        @Override
        public void onCompleted(Exception e) {
            Log.i(TAG, "onCompleted: " + e);
        }

        @Override
        public void onEvent(int var1, int var2, int var3, Bundle var4) {
            Log.i(TAG, "onEvent: var1=" + var1 + ",var2=" + var2 + ",var3=" + var3 + ",var4=" + var4);
        }
    }

    /************************************************* 包装类 **************************************************************/

    class WapperInitListener implements com.iflytek.cloud.InitListener {
        private final TTSSpeaker.InitListener mInitListener;

        public WapperInitListener(TTSSpeaker.InitListener listener) {
            this.mInitListener = listener;
        }

        @Override
        public void onInit(int i) {
            if (mInitListener != null) {
                mInitListener.onInit(i);
            }
        }
    }

    class WapperSynthesizerListener implements SynthesizerListener {
        private final TTSSpeaker.TTSSpeakListener mTTSSpeakListener;

        public WapperSynthesizerListener(TTSSpeaker.TTSSpeakListener ttsSpeakListener) {
            this.mTTSSpeakListener = ttsSpeakListener;
        }

        @Override
        public void onSpeakBegin() {
            if (mTTSSpeakListener != null) {
                mTTSSpeakListener.onSpeakBegin();
            }

        }

        @Override
        public void onBufferProgress(int i, int i1, int i2, String s) {
            if (mTTSSpeakListener != null) {
                mTTSSpeakListener.onBufferProgress(i, i1, i2, s);
            }
        }

        @Override
        public void onSpeakPaused() {
            if (mTTSSpeakListener != null) {
                mTTSSpeakListener.onSpeakPaused();
            }
        }

        @Override
        public void onSpeakResumed() {
            if (mTTSSpeakListener != null) {
                mTTSSpeakListener.onSpeakResumed();
            }
        }

        @Override
        public void onSpeakProgress(int i, int i1, int i2) {
            if (mTTSSpeakListener != null) {
                mTTSSpeakListener.onSpeakProgress(i, i1, i2);
            }
        }

        @Override
        public void onCompleted(SpeechError speechError) {
            if (mTTSSpeakListener != null) {
                mTTSSpeakListener.onCompleted(speechError);
            }
        }

        @Override
        public void onEvent(int i, int i1, int i2, Bundle bundle) {
            if (mTTSSpeakListener != null) {
                mTTSSpeakListener.onEvent(i, i1, i2, bundle);
            }
        }
    }

    /***************************************************************************************************************/
}
