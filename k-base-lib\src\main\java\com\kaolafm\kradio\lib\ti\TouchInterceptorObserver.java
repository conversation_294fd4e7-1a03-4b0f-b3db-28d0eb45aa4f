package com.kaolafm.kradio.lib.ti;

import androidx.lifecycle.GenericLifecycleObserver;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleOwner;
import android.content.IntentFilter;
import android.os.Handler;
import androidx.fragment.app.Fragment;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;

import com.kaolafm.kradio.lib.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.ui.BaseActivity;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;

import java.lang.ref.WeakReference;
import java.util.List;

public class TouchInterceptorObserver implements GenericLifecycleObserver {


    private static final String TAG = "k.ti.o";
    final LifecycleOwner owner;
    private final List<View> views;
    private final WeakReference<BaseActivity> activityRef;
    private final HideBroadcastReceiver mReceiver;
    private TouchInterceptor mTouchInterceptor;
    private boolean isInterceptor = false;
    private boolean isDiscard;

    public TouchInterceptorObserver(LifecycleOwner owner, BaseActivity activity, List<View> views) {
        this.owner = owner;
        this.activityRef = new WeakReference<>(activity);
        this.views = views;

        mReceiver = new HideBroadcastReceiver(this);
        IntentFilter filter = new IntentFilter();
        filter.addAction(HideBroadcastReceiver.ACTION);
        LocalBroadcastManager.getInstance(AppDelegate.getInstance().getContext()).registerReceiver(mReceiver, filter);
    }

    @Override
    public void onStateChanged(LifecycleOwner source, Lifecycle.Event event) {
        Log.i(TAG, "onStateChanged:tag=" + (((Fragment) owner)).getTag());
        Log.i(TAG, "              :event=" + event.name());
        Log.i(TAG, "              :isDiscard=" + isDiscard);
        if (event == Lifecycle.Event.ON_RESUME) {
            if (!isDiscard) {
                createTouchInterceptor();
                isInterceptor = true;
                if (activityRef != null && activityRef.get() != null) {
                    activityRef.get().setTouchInterceptor(mTouchInterceptor);
                }
            }
        } else if (event == Lifecycle.Event.ON_PAUSE) {
            isInterceptor = false;
        }
    }

    /**
     * 屏蔽指定区域的touch事件
     */
    private void createTouchInterceptor() {
        if (mTouchInterceptor == null) {
            mTouchInterceptor = new TouchInterceptor() {
                @Override
                public boolean dispatchTouchEvent(BaseActivity activity, MotionEvent ev) {
                    if (!isInterceptor) {
                        return false;
                    }

                    boolean inTouchZone = false;
                    for (int i = 0; i < views.size(); i++) {
                        inTouchZone |= ViewUtil.isInTouchZone(views.get(i), (int) ev.getRawX(), (int) ev.getRawY());
                    }

                    Log.i(TAG, "dispatchTouchEvent: tag=" + (((Fragment) owner)).getTag());
                    Log.i(TAG, "                  : inTouchZone=" + inTouchZone);
                    if (inTouchZone) {
                        switch (ev.getAction()) {
                            case MotionEvent.ACTION_UP:
                                mTiHandler.removeCallbacks(mPendingCheckForTap);
                                mTiHandler.removeCallbacks(mPendingCheckForLongPress);
                                if (!mHasPerformedLongPress) {
                                    performClick();
                                }
                                break;
                            case MotionEvent.ACTION_MOVE:
                            case MotionEvent.ACTION_CANCEL:
                                mTiHandler.removeCallbacks(mPendingCheckForTap);
                                mTiHandler.removeCallbacks(mPendingCheckForLongPress);
                                break;
                            case MotionEvent.ACTION_DOWN:
                                mTiHandler.removeCallbacks(mPendingCheckForTap);
                                mTiHandler.removeCallbacks(mPendingCheckForLongPress);
                                if (mPendingCheckForTap == null) {
                                    mPendingCheckForTap = new CheckForTap();
                                }
                                mTiHandler.postDelayed(mPendingCheckForTap, ViewConfiguration.getTapTimeout());
                                break;
                            default:
                                break;
                        }
                        return true;
                    } else {
                        return false;
                    }
                }
            };
        }
    }

    private void performClick() {
        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), true)) {
            return;
        }
        ToastUtil.showInfo(AppDelegate.getInstance().getContext(), ResUtil.getString(R.string.ad_can_not_touch));
    }


    private Handler mTiHandler = new Handler();
    private CheckForLongPress mPendingCheckForLongPress;
    private CheckForTap mPendingCheckForTap;
    private boolean mHasPerformedLongPress;

    public void discard() {
        Log.i(TAG, "discard: tag=" + (((Fragment) owner)).getTag());
        isDiscard = true;
        isInterceptor = false;
        mTouchInterceptor = null;
        owner.getLifecycle().removeObserver(this);
        if (mReceiver != null) {
            LocalBroadcastManager.getInstance(AppDelegate.getInstance().getContext()).unregisterReceiver(mReceiver);
        }
    }

    private final class CheckForTap implements Runnable {
        public float x;
        public float y;

        @Override
        public void run() {
            checkForLongClick(ViewConfiguration.getTapTimeout(), x, y);
        }
    }

    private void checkForLongClick(int delayOffset, float x, float y) {
        mHasPerformedLongPress = false;

        if (mPendingCheckForLongPress == null) {
            mPendingCheckForLongPress = new CheckForLongPress();
        }
        mTiHandler.postDelayed(mPendingCheckForLongPress, ViewConfiguration.getLongPressTimeout() - delayOffset);
    }


    private final class CheckForLongPress implements Runnable {

        @Override
        public void run() {
            if (performLongClick()) {
                mHasPerformedLongPress = true;
            }
        }

    }

    private boolean performLongClick() {
        return true;
    }


}