<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/pc_age_rl"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/pc_slect_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@id/ll_first"
        android:layout_centerInParent="true"
        android:layout_margin="@dimen/y100"
        android:gravity="center"
        android:text="@string/personlityrecommendation_age"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/m28" />

    <LinearLayout
        android:id="@+id/ll_first"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center">

        <TextView
            android:id="@+id/age_one"
            android:layout_width="@dimen/pc_age_item_width"
            android:layout_height="@dimen/pc_age_item_height"
            android:layout_alignTop="@+id/age_three"
            android:background="@drawable/pc_round_bg"
            android:gravity="center"
            android:text="00后"
            android:textColor="@color/pc_text_selector"
            android:textSize="@dimen/text_size5" />

        <TextView
            android:id="@+id/age_two"
            android:layout_width="@dimen/pc_age_item_width"
            android:layout_height="@dimen/pc_age_item_height"
            android:layout_marginLeft="@dimen/y26"
            android:layout_marginRight="@dimen/y13"
            android:background="@drawable/pc_round_bg"
            android:gravity="center"
            android:text="90后"
            android:textColor="@color/pc_text_selector"
            android:textSize="@dimen/text_size5" />

        <TextView
            android:id="@+id/age_three"
            android:layout_width="@dimen/pc_age_item_width"
            android:layout_height="@dimen/pc_age_item_height"
            android:layout_marginLeft="@dimen/y13"
            android:layout_marginRight="@dimen/y26"
            android:background="@drawable/pc_round_bg"
            android:gravity="center"
            android:text="80后"
            android:textColor="@color/pc_text_selector"
            android:textSize="@dimen/text_size5" />

        <TextView
            android:id="@+id/age_four"
            android:layout_width="@dimen/pc_age_item_width"
            android:layout_height="@dimen/pc_age_item_height"

            android:background="@drawable/pc_round_bg"
            android:gravity="center"
            android:text="70后"
            android:textColor="@color/pc_text_selector"
            android:textSize="@dimen/text_size5" />


    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_second"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/ll_first"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/y50"
        android:gravity="center">

        <TextView
            android:id="@+id/age_five"
            android:layout_width="@dimen/pc_age_item_width"
            android:layout_height="@dimen/pc_age_item_height"
            android:layout_marginRight="@dimen/y13"
            android:background="@drawable/pc_round_bg"
            android:gravity="center"
            android:text="60后"
            android:textColor="@color/pc_text_selector"
            android:textSize="@dimen/text_size5" />

        <TextView
            android:id="@+id/age_six"
            android:layout_width="@dimen/pc_age_item_width"
            android:layout_height="@dimen/pc_age_item_height"
            android:layout_marginLeft="@dimen/y13"
            android:background="@drawable/pc_round_bg"
            android:gravity="center"
            android:text="50后"
            android:textColor="@color/pc_text_selector"
            android:textSize="@dimen/text_size5" />
    </LinearLayout>

    <ImageView
        android:id="@+id/age_durition_progress"
        android:layout_width="@dimen/x100"
        android:layout_height="@dimen/m7"
        android:layout_below="@id/ll_second"
        android:layout_centerHorizontal="true"
        android:layout_margin="@dimen/x50"
        android:background="@drawable/pc_progress_unselect"
        android:gravity="center" />

</RelativeLayout>