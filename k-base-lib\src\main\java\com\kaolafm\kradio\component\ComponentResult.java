package com.kaolafm.kradio.component;

import android.app.Application;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-07-01
 */
public class ComponentResult {

    private int code;

    private boolean success;

    private Map<String, Object> data = new HashMap<>();

    public static ComponentResult defaultNullResult() {
        return error(Code.ERROR_NULL_RESULT);
    }

    public static ComponentResult defaultExceptionResult(Exception e) {
        if (e != null) {
            e.printStackTrace();
        }
        return error(Code.ERROR_EXCEPTION_RESULT);
    }

    /**
     * 带有成功数据集合map的成功结果
     * @param data 数据Map
     * @return 成功结果
     */
    public static ComponentResult success(Map<String, Object> data) {
        ComponentResult result = new ComponentResult();
        result.code = Code.SUCCESS;
        result.success = true;
        result.data = data;
        return result;
    }

    /**
     * 带有数据的成功结果
     *
     * @param key   键
     * @param value 值
     * @return 成功结果
     */
    public static ComponentResult success(String key, Object value) {
        Map<String, Object> data = new HashMap<>(2);
        data.put(key, value);
        return success(data);
    }

    /**
     * 没有数据的成功结果
     *
     * @return 成功结果
     */
    public static ComponentResult success() {
        return success(null);
    }

    /**
     * 只有错误码的错误结果。
     *
     * @param code 错误码
     * @return 错误的结果
     */
    public static ComponentResult error(int code) {
        ComponentResult result = new ComponentResult();
        result.code = code;
        result.success = false;
        return result;
    }

    /**
     * 添加组件调用的返回信息内容
     *
     * @param key   返回信息内容的key
     * @param value 返回信息内容的value
     * @return ComponentResult对象，用于链式添加
     */
    public ComponentResult addData(String key, Object value) {
        if (data == null) {
            data = new HashMap<>(16);
        }
        data.put(key, value);
        return this;
    }

    /**
     * 设置数据，会替换掉原来的数据。
     * @param data 数据Map
     * @return
     */
    public ComponentResult setData(Map<String, Object> data) {
        this.data = data;
        return this;
    }

    public int getCode() {
        return code;
    }

    public boolean isSuccess() {
        return success;
    }

    public Map<String, Object> getData() {
        return data;
    }

    public <T> T getValue(String key) {
        return (T) data.get(key);
    }

    public static class Code {

        /**
         * 调用成功
         */
        public static final int SUCCESS = 0;

        /**
         * 组件调用成功，但业务逻辑判定为失败
         */
        public static final int ERROR_BUSINESS = 1;

        /**
         * 保留状态码：默认的请求错误code
         */
        public static final int ERROR_DEFAULT = -1;

        /**
         * 没有指定组件名称
         */
        public static final int ERROR_COMPONENT_NAME_EMPTY = -2;

        /**
         * result为null
         */
        public static final int ERROR_NULL_RESULT = -3;

        /**
         * 调用过程中出现exception
         */
        public static final int ERROR_EXCEPTION_RESULT = -4;

        /**
         * 没有找到组件能处理此次调用请求
         */
        public static final int ERROR_NO_COMPONENT_FOUND = -5;

        /**
         * context 为null，自动获取application失败。
         * 需要在首次调用{@link ComponentClient}之前手动执行{@link ComponentClient}的初始化：{@link ComponentClient#init(Application)};
         */
        public static final int ERROR_CONTEXT_NULL = -6;

        /**
         * 取消
         */
        public static final int ERROR_CANCELED = -7;

        /**
         * 超时
         */
        public static final int ERROR_TIMEOUT = -8;

        /**
         * 未调用{@link ComponentClient#sendResult(String, ComponentResult)}方法
         */
        public static final int ERROR_CALLBACK_NOT_INVOKED = -9;

        /**
         * 跨进程组件调用时对象传输出错，可能是自定义类型没有共用
         */
        public static final int ERROR_REMOTE_CC_DELIVERY_FAILED = -10;

        /**
         * 组件不支持该actionName
         */
        public static final int ERROR_UNSUPPORTED_ACTION_NAME = -11;
    }
}
