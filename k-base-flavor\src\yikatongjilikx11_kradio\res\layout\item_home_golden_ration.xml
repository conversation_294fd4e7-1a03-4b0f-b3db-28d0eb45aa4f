<?xml version="1.0" encoding="utf-8"?>
<com.kaolafm.kradio.lib.widget.ratio.RatioConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rcl_item_home_content"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/card_big_bottom"
    app:wh_ratio="0.7:1">

    <com.kaolafm.kradio.lib.widget.square.SquareLayout
        android:id="@+id/sv_item_home_place"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_item_home_cover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="1dp"
            android:scaleType="centerCrop" />

        <View
            android:id="@+id/view_item_home_cover_bg"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/card_big_fg" />
    </com.kaolafm.kradio.lib.widget.square.SquareLayout>


    <TextView
        android:id="@+id/tv_item_home_live_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/flag_live_bg"
        android:paddingLeft="@dimen/x10"
        android:paddingTop="@dimen/y5"
        android:paddingRight="@dimen/x10"
        android:paddingBottom="@dimen/y5"
        android:text="@string/live"
        android:textColor="@color/home_bar_player_living_text_color"
        android:textSize="@dimen/text_size_title1"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_item_home_ad_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_ad_mark"
        android:paddingLeft="@dimen/x10"
        android:paddingTop="@dimen/y5"
        android:paddingRight="@dimen/x10"
        android:paddingBottom="@dimen/y5"
        android:text="@string/ad"
        android:textColor="@color/home_bar_player_living_text_color"
        android:textSize="@dimen/text_size_title1"
        android:visibility="gone"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/view_item_home_text_bg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@drawable/card_big_bottom"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/sv_item_home_place" />

    <TextView
        android:id="@+id/tv_item_home_title"
        android:layout_width="0dp"
        android:layout_height="@dimen/y100"
        android:ellipsize="end"
        android:gravity="center_horizontal|bottom"
        android:maxEms="6"
        android:maxLines="2"
        android:paddingStart="@dimen/x8"
        android:paddingEnd="@dimen/y8"
        android:paddingBottom="@dimen/y15"
        android:textColor="@color/card_big_title_text_color"
        android:textSize="@dimen/text_size_title1"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        tools:text="二货一箩筐二货一箩筐二货一箩筐一箩二货一箩筐一箩筐" />

</com.kaolafm.kradio.lib.widget.ratio.RatioConstraintLayout>
