package com.kaolafm.kradio.mine;

import com.kaolafm.kradio.component.ActionProcessor;
import com.kaolafm.kradio.component.ComponentClient;
import com.kaolafm.kradio.component.ComponentResult;
import com.kaolafm.kradio.component.RealCaller;
import com.kaolafm.kradio.component.SharedConst;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.user.UserInfoManager;
import com.kaolafm.opensdk.log.Logging;

/**
 * 显示登录页面
 * <AUTHOR>
 * @date 2019-10-29
 */
@SharedConst
public class ShowLoginFragmentProcessor implements ActionProcessor {

    private static final String SHOW_LOGIN_FRAGMENT_IF_NOT_LOGIN = "showLoginFragmentIfNotLogin";

    private static final String CONTEXT = "context";

    private static final String KEY_IS_USER_LOGIN = "isUserLogin";

    @Override
    public String actionName() {
        return SHOW_LOGIN_FRAGMENT_IF_NOT_LOGIN;
    }

    @Override
    public boolean onAction(RealCaller caller) {
        boolean userBound = UserInfoManager.getInstance().isUserBound();
        Logging.d("检查是否登录=%s，未登录显示登录页面", userBound);
        if (!userBound) {
            BaseFragment baseFragment = caller.getParamValue(CONTEXT);
            if (baseFragment != null) {
                baseFragment.extraTransaction().start(MineUtil.INSTANCE.showLoginFragment(true));
            }
        }
        ComponentClient.sendResult(caller.getCallId(), ComponentResult.success(KEY_IS_USER_LOGIN, userBound));
        return false;
    }
}
