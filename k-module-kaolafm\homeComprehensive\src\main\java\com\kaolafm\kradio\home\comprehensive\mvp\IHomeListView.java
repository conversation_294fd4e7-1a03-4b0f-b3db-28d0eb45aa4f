package com.kaolafm.kradio.home.comprehensive.mvp;

import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.lib.base.mvp.IView;

import java.util.List;

public interface IHomeListView extends IView {

    void showContent(List<HomeCell> cells);

    void showLoading();

    void hideLoading();

    void showError(String error);

    void hideErrorLayout();

    void showImage(long id,String img,String title,String desc);

}
