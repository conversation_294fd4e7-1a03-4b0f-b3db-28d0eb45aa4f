package com.kaolafm.kradio.common.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.RectF;

import androidx.appcompat.widget.AppCompatSeekBar;
import android.text.TextPaint;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.View;
import android.widget.SeekBar;

import com.kaolafm.kradio.k_kaolafm.R;

import skin.support.widget.SkinCompatSeekBar;

public class IndicatorSeekBar extends SkinCompatSeekBar {

    // 画笔
    private Paint mPaint, rectPaint;
    // 进度文字位置信息
    private Rect mProgressTextRect = new Rect();
    // 滑块按钮宽度
    private float mThumbWidth = 0;
    // 进度指示器宽度
    private int mIndicatorWidth = dp2px(50);
    private int mRadius;
    private int mTxtOffset;
    // 进度监听
    private OnIndicatorSeekBarChangeListener mIndicatorSeekBarChangeListener;
    private static final String TAG = "IndicatorSeekBar";

    private boolean needComputeWidth = true;
    private float txtWidth = -1;

    public IndicatorSeekBar(Context context) {
        this(context, null);
    }

    public IndicatorSeekBar(Context context, AttributeSet attrs) {
        this(context, attrs, R.attr.seekBarStyle);
    }

    public IndicatorSeekBar(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.IndicatorSeekBar);
        int txtColor = 0xfff8525e;
        int txtBgColor = 0xffffffff;
        int txtSize = sp2px(12f);
        if (typedArray != null) {
            txtColor = typedArray.getColor(R.styleable.IndicatorSeekBar_seek_color, txtColor);
            txtBgColor = typedArray.getColor(R.styleable.IndicatorSeekBar_seek_bg_color, txtBgColor);
            txtSize = (int) typedArray.getDimension(R.styleable.IndicatorSeekBar_seek_txt_size, txtSize);
            mRadius = (int) typedArray.getDimension(R.styleable.IndicatorSeekBar_seek_bg_radius, 32);
            mTxtOffset = (int) typedArray.getDimension(R.styleable.IndicatorSeekBar_txt_offset, 15);
            typedArray.recycle();
        }
        init(txtColor, txtBgColor, txtSize);
        setLayerType(View.LAYER_TYPE_SOFTWARE, null);
    }

    private void init(int txtColor, int txtBgColor, int txtSize) {
        mPaint = new TextPaint();
        mPaint.setAntiAlias(true);
        mPaint.setColor(txtColor);//文字颜色
        mPaint.setTextSize(txtSize);
//        mPaint.setTypeface(Typeface.MONOSPACE);//等宽字体

        rectPaint = new Paint();
        rectPaint.setAntiAlias(true); // 去锯齿
        rectPaint.setStyle(Paint.Style.FILL);
        rectPaint.setColor(txtBgColor);//文字背景
        //画阴影
        //绘制拖动中的阴影：阴影半径，阴影x坐标偏移，阴影y坐标偏移，阴影颜色
        rectPaint.setShadowLayer(5f, 0, 3, 0x80000000);

        // 如果不设置padding，当滑动到最左边或最右边时，滑块会显示不全
//        setPadding(mThumbWidth / 2, 0, mThumbWidth / 2, 0);

        // 设置滑动监听
        this.setOnSeekBarChangeListener(new OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                // NO OP
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                if (mIndicatorSeekBarChangeListener != null) {
                    mIndicatorSeekBarChangeListener.onStartTrackingTouch(seekBar);
                }
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                if (mIndicatorSeekBarChangeListener != null) {
                    mIndicatorSeekBarChangeListener.onStopTrackingTouch(seekBar);
                }
            }
        });
    }

    private int lastTxtLength = -1;

    @Override
    protected synchronized void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        String progressText = "";

        if (mIndicatorSeekBarChangeListener != null) {
            progressText = mIndicatorSeekBarChangeListener.getProgressText(getProgress());
            if (progressText == null) {
                progressText = "";
            }
            int l = progressText.length();
            needComputeWidth = l != lastTxtLength;
            lastTxtLength = l;
        } else {
            progressText = getProgress() + "%";
        }
        mPaint.getTextBounds(progressText, 0, progressText.length(), mProgressTextRect);

        // 进度百分比
        float progressRatio = 0;
        if (getMax() > 0) progressRatio = (float) getProgress() / getMax();
        if (needComputeWidth) {
            txtWidth = mPaint.measureText(progressText);
        }
        mThumbWidth = txtWidth + mTxtOffset * 2;
        // thumb偏移量 考虑文字背景宽度所占空间不应超出范围
        float thumbOffset = (mThumbWidth - mProgressTextRect.width()) / 2 - mThumbWidth * progressRatio;//文字区域左侧x坐标
        float thumbX = getWidth() * progressRatio + thumbOffset;
//        float thumbY = getHeight() / 2f + mProgressTextRect.height() / 2f;

        //画背景
        @SuppressLint("DrawAllocation") RectF rectF = new RectF(thumbX + mProgressTextRect.width() / 2 - 6 * mThumbWidth / 12,
                getHeight() / 2 - mRadius, thumbX + mProgressTextRect.width() / 2 + 6 * mThumbWidth / 12, getHeight() / 2 + mRadius);
        canvas.drawRoundRect(rectF, mRadius, mRadius, rectPaint);
        canvas.drawText(progressText, thumbX, getHeight() / 2 + Math.abs(mPaint.ascent() + mPaint.descent()) / 2, mPaint);

        if (mIndicatorSeekBarChangeListener != null) {
            float indicatorOffset = getWidth() * progressRatio - (mIndicatorWidth - mThumbWidth) / 2 - mThumbWidth * progressRatio;
            mIndicatorSeekBarChangeListener.onProgressChanged(this, getProgress(), indicatorOffset);
        }
    }

    @Override
    public synchronized void setProgress(int progress) {
        super.setProgress(progress);
    }

    @Override
    public synchronized void setMax(int max) {
        super.setMax(max);
    }

    /**
     * 设置进度监听
     *
     * @param listener OnIndicatorSeekBarChangeListener
     */
    public void setOnSeekBarChangeListener(OnIndicatorSeekBarChangeListener listener) {
        this.mIndicatorSeekBarChangeListener = listener;
    }

    @Override
    public void applySkin() {
        Rect bounds = getProgressDrawable().getBounds();//drawable的更换会使maxHeight 不生效，单独修改下。
        super.applySkin();
        getProgressDrawable().setBounds(bounds);
    }

    /**
     * 进度监听
     */
    public interface OnIndicatorSeekBarChangeListener {
        /**
         * 进度监听回调
         *
         * @param seekBar         SeekBar
         * @param progress        进度
         * @param indicatorOffset 指示器偏移量
         */
        public void onProgressChanged(SeekBar seekBar, int progress, float indicatorOffset);

        /**
         * 开始拖动
         *
         * @param seekBar SeekBar
         */
        public void onStartTrackingTouch(SeekBar seekBar);

        /**
         * 停止拖动
         *
         * @param seekBar SeekBar
         */
        public void onStopTrackingTouch(SeekBar seekBar);

        public String getProgressText(int progress);
    }

    /**
     * dp转px
     *
     * @param dp dp值
     * @return px值
     */
    public int dp2px(float dp) {
        return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dp,
                getResources().getDisplayMetrics());
    }

    /**
     * sp转px
     *
     * @param sp sp值
     * @return px值
     */
    private int sp2px(float sp) {
        return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_SP, sp,
                getResources().getDisplayMetrics());
    }
}