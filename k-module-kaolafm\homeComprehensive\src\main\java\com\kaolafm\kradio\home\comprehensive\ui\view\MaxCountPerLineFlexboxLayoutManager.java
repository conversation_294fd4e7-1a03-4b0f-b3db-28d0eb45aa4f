package com.kaolafm.kradio.home.comprehensive.ui.view;

import android.content.Context;
import android.view.View;

import com.google.android.flexbox.FlexboxLayoutManager;

public class MaxCountPerLineFlexboxLayoutManager extends FlexboxLayoutManager {
    private int maxCountPerLine = NOT_SET;
    private int widthPixels;

    public MaxCountPerLineFlexboxLayoutManager(Context context, int maxCountPerLine) {
        super(context);
        widthPixels = context.getResources().getDisplayMetrics().widthPixels;
        this.maxCountPerLine = maxCountPerLine;
    }


    @Override
    public int getDecorationLengthMainAxis(View view, int index, int indexInFlexLine) {
        if (maxCountPerLine != NOT_SET && indexInFlexLine >= maxCountPerLine)
            return widthPixels;
        return super.getDecorationLengthMainAxis(view, index, indexInFlexLine);
    }
}