package com.kaolafm.kradio.flavor.impl;

import android.view.KeyEvent;

import com.kaolafm.kradio.lib.base.flavor.KRadioConvertMediaKeyCodeInter;

public class KRadioConvertMediaKeyCodeImpl implements KRadioConvertMediaKeyCodeInter {
    @Override
    public int convertKeyCode(int originKeycode) {
        if(originKeycode == KeyEvent.KEYCODE_MEDIA_STOP) {
            return KeyEvent.KEYCODE_MEDIA_PAUSE;
        }
        return originKeycode;
    }
}
