<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/fl_dialog_fragment_content_wrapper"
    android:background="@color/transparent_color"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center" >

    <FrameLayout
        android:id="@+id/fl_dialog_fragment_content"
        android:background="@color/transparent_color"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center" />

    <!-- 隐藏控件，用于所见即可说语音执行关闭操作 -->
    <TextView
        android:id="@+id/cd_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        android:contentDescription="@string/content_desc_close_dialog"
        android:text="@string/content_desc_close_dialog"
        android:textColor="@android:color/transparent"
        android:textSize="1sp"
        />

</FrameLayout>

    <!--android:background="@color/information_main_color"-->