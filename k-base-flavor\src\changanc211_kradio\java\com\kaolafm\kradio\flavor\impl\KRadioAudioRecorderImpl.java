package com.kaolafm.kradio.flavor.impl;

import com.incall.proxy.constant.SourceConstantsDef;
import com.incall.proxy.source.SourceManager;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioRecorderInter;
import com.kaolafm.kradio.lib.base.flavor.live.KradioRecorderInterface;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-05-22 17:57
 ******************************************/
public final class KRadioAudioRecorderImpl implements KRadioAudioRecorderInter {
    @Override
    public boolean initVR(Object... args) {
        return false;
    }

    @Override
    public boolean onAudioRecordStart(Object... args) {
        SourceConstantsDef.SourceID sourceID = SourceManager.getInstance().getCurrentSource();
        String curId = sourceID == null ? null : sourceID.name();
        if (SourceConstantsDef.SourceID.APP.name().equals(curId)) {
            return true;
        }
        boolean flag = SourceManager.getInstance().requestSource(SourceConstantsDef.SourceID.APP,false);
        return flag;
    }

    @Override
    public boolean onAudioRecordStop(Object... args) {
        // 解决36493问题
//        SourceConstantsDef.SourceID sourceID = SourceManager.getInstance().getCurrentSource();
//        String curId = sourceID == null ? null : sourceID.name();
//        if (SourceConstantsDef.SourceID.APP.name().equals(curId)) {
//            boolean flag = SourceManager.getInstance().releaseSource(SourceConstantsDef.SourceID.APP);
//            return flag;
//        }
        return true;
    }

    @Override
    public KradioRecorderInterface getRecorder() {
        return null;
    }

    @Override
    public void setVrStatusListener(OnVRStatusListener onVRStatusListener) {

    }
}
