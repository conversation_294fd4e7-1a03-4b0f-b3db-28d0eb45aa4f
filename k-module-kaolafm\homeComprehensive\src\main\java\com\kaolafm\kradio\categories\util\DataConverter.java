package com.kaolafm.kradio.categories.util;

import android.text.TextUtils;


import com.kaolafm.kradio.lib.bean.SubcategoryItemBean;
import com.kaolafm.kradio.lib.common.ResType;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.api.operation.model.ImageFile;
import com.kaolafm.opensdk.api.operation.model.category.Category;
import com.kaolafm.opensdk.api.operation.model.category.CategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.LeafCategory;
import com.kaolafm.opensdk.api.operation.model.category.RadioCategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.RadioQQMusicCategoryMember;
import com.kaolafm.opensdk.api.operation.model.column.AlbumDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.Column;
import com.kaolafm.opensdk.api.operation.model.column.ColumnGrp;
import com.kaolafm.opensdk.api.operation.model.column.ColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.RadioDetailColumnMember;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.utils.operation.OperationAssister;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 **/
public final class DataConverter {

    /**
     * 分类模块中,全部转换成SubcategoryItemBean
     * <p>论树的遍历
     *
     * @param columnGrp
     * @return
     */
    public static void toSubcategoryItemBeans(List<SubcategoryItemBean> rst, ColumnGrp columnGrp, boolean withSelf) {
        if (columnGrp == null) {
            return;
        }

        if (withSelf) {
            //如果需要解析父类信息
            SubcategoryItemBean parentSIB = new SubcategoryItemBean();
            parentSIB.setId(Long.valueOf(columnGrp.getCode()));
            parentSIB.setTitle(columnGrp.getTitle());
            // 统一使用SubcategoryItemBean.TYPE_ITEM_TITLE作为子分类的标题显示
            parentSIB.setItemType(SubcategoryItemBean.TYPE_ITEM_TITLE);
            parentSIB.setCoverUrl(getCoverUrl(columnGrp));
            parentSIB.setDes(columnGrp.getDescription());

            rst.add(parentSIB);
        }


        if (columnGrp instanceof Column) {
            List<? extends ColumnMember> columnMembers = ((Column) columnGrp).getColumnMembers();
            if (columnMembers != null && !columnMembers.isEmpty()) {
                //如果存在成员列表
                for (int i = 0; i < columnMembers.size(); i++) {
                    ColumnMember columnMember = columnMembers.get(i);
                    if (columnMember != null) {
                        SubcategoryItemBean subcategoryItemBean = toSubcategoryItemBean(columnMember);
                        if (subcategoryItemBean != null) {
                            rst.add(subcategoryItemBean);
                        }
                    }
                }
            }
        } else {
            List<? extends ColumnGrp> childColumns = columnGrp.getChildColumns();
            if (childColumns != null && !childColumns.isEmpty()) {
                //如果存在子列表,遍历子列表
                for (int i = 0; i < childColumns.size(); i++) {
                    toSubcategoryItemBeans(rst, childColumns.get(i), true);
                }
            }
        }

    }

//    public static List<SubcategoryItemBean> toSubcategoryItemBeans(ColumnGrp columnGrp) {
//        if (columnGrp == null) {
//            return null;
//        }
//
//
//        if (columnGrp instanceof Column) {
//            //栏目下的ColumnMembers
//            List<? extends ColumnMember> columnMembers = ((Column) columnGrp).getColumnMembers();
//            if (columnMembers == null || columnMembers.isEmpty()) {
//                return null;
//            } else {
//                List<SubcategoryItemBean> rst = new ArrayList<>();
//
//                for (int i = 0; i < columnMembers.size(); i++) {
//                    SubcategoryItemBean subcategoryItemBean = DataConverter.toSubcategoryItemBean(columnMembers.get(i));
//                    if (subcategoryItemBean == null) {
//                        continue;
//                    }
//                    rst.add(subcategoryItemBean);
//                }
//                return rst;
//            }
//        } else {
//            //栏目下的ChildColumns
//            List<? extends ColumnGrp> childColumns = columnGrp.getChildColumns();
//            if (childColumns == null || childColumns.isEmpty()) {
//                return null;
//            } else {
//                List<SubcategoryItemBean> rst = new ArrayList<>();
//
//                for (int i = 0; i < childColumns.size(); i++) {
//                    SubcategoryItemBean subcategoryItemBean = DataConverter.toSubcategoryItemBean(childColumns.get(i));
//                    if (subcategoryItemBean == null) {
//                        continue;
//                    }
//                    rst.add(subcategoryItemBean);
//                }
//
//                return rst;
//            }
//        }
//
//    }


    /**
     * 获取Ai电台下的子分类
     *
     * @param columnGrp
     * @return
     */
    public static Category toCategory(ColumnGrp columnGrp) {
        if (columnGrp == null) {
            return null;
        }

        Category rst = new Category();

        rst.setCode(columnGrp.getCode());
        rst.setName(columnGrp.getTitle());

        rst.setImageFiles(columnGrp.getImageFiles());
        rst.setExtInfo(columnGrp.getExtInfo());
        rst.setChildCategories(getChildColumns(columnGrp));

        if (columnGrp instanceof Column) {
            //List<? extends ColumnMember> columnMembers = ((Column) columnGrp).getColumnMembers();
        } else {

        }

        return rst;
    }


    private static List<Category> getChildColumns(ColumnGrp columnGrps) {
        if (columnGrps == null) {
            return null;
        }

        List<? extends ColumnGrp> columns = columnGrps.getChildColumns();

        if (columns == null || columns.isEmpty()) {
            return null;
        }

        List<Category> rst = new ArrayList<>();

        for (int i = 0; i < columns.size(); i++) {
            Category category = toCategory(columns.get(i));
            if (category == null) {
                continue;
            }
            rst.add(category);
        }

        return rst;
    }

    /**
     * 获取封面:如果cover不存在,则获取icon
     *
     * @param columnGrp
     * @return
     */
    private static String getCoverUrl(ColumnGrp columnGrp) {
        if (columnGrp == null) {
            return null;
        }

        String rst = null;

        Map<String, ImageFile> imageFiles = columnGrp.getImageFiles();
        ImageFile cover = imageFiles.get("cover");
        if (cover != null) {
            rst = cover.getUrl();
        } else {
            ImageFile icon = imageFiles.get("icon");
            if (icon != null) {
                rst = icon.getUrl();
            }
        }

        return rst;
    }


    /**
     * 分类:SubcategoryItemBean 对应 ColumnMember
     * <p>两个都是最小单元
     *
     * @param columnMember
     * @return
     */
    public static SubcategoryItemBean toSubcategoryItemBean(ColumnMember columnMember) {
        if (columnMember == null) {
            return null;
        }

        SubcategoryItemBean rst = new SubcategoryItemBean();

        rst.setId(OperationAssister.getId(columnMember));
        rst.setName(columnMember.getTitle());
        if (columnMember instanceof AlbumDetailColumnMember) {
            int playTimes = ((AlbumDetailColumnMember) columnMember).getPlayTimes();
            rst.setListenNum(playTimes);
        }
        rst.setItemType(getItemType(columnMember));
        //#34761 AI电台下，媒资中配置【自动运营】的专辑，客户端对应的AI电台下的专辑图片展示不出来  他们给的数据不是 cover 是 icon 只能适配
        if (OperationAssister.getCover(columnMember).equals("")) {
            rst.setCoverUrl(OperationAssister.getIcon(columnMember));
        } else {
            rst.setCoverUrl(OperationAssister.getCover(columnMember));
        }
        rst.setDes(columnMember.getDescription());
        rst.setResType(getResType(columnMember));

        return rst;
    }

    private static int getResType(ColumnMember cocolumnMemberumn) {
        int rst;
        if (cocolumnMemberumn instanceof RadioDetailColumnMember) {
            rst = ResType.RADIO_TYPE;
        } else if (cocolumnMemberumn instanceof AlbumDetailColumnMember) {
            rst = ResType.ALBUM_TYPE;
        } else {
            rst = ResType.RADIO_TYPE;
        }
        return rst;
    }

    private static int getItemType(ColumnMember cocolumnMemberumn) {
        int rst;
        if (cocolumnMemberumn instanceof RadioDetailColumnMember) {
            rst = SubcategoryItemBean.TYPE_ITEM_RADIO_CHANNEL;
        } else if (cocolumnMemberumn instanceof AlbumDetailColumnMember) {
            rst = SubcategoryItemBean.TYPE_ITEM_ALBUM;
        } else {
            rst = SubcategoryItemBean.TYPE_ITEM_RADIO_CHANNEL;
        }
        return rst;
    }


    /***************************************************************************************************************/
    /**
     * 用于[音乐]分类下的子分类
     * 从考拉获取的分类数据转换为item数据
     */
    public static ArrayList<SubcategoryItemBean> categoryToItemBean(List<Category> categoryList) {
        ArrayList<SubcategoryItemBean> itemBeanList = new ArrayList<>();
        if (categoryList == null || categoryList.size() == 0) {
            return itemBeanList;
        }
        //获取正在播放的id
        String radioId = PlayerManagerHelper.getInstance().getCurPlayItem().getRadioId();

        for (int i = 0, size = categoryList.size(); i < size; i++) {
            Category category = categoryList.get(i);
            if (category instanceof LeafCategory) {
                List<CategoryMember> categoryMemberList = ((LeafCategory) category).getCategoryMembers();
                if (categoryMemberList != null && categoryMemberList.size() > 0) {
                    //标题
                    SubcategoryItemBean itemBeanTitle = new SubcategoryItemBean();
                    itemBeanTitle.setItemType(SubcategoryItemBean.TYPE_ITEM_TITLE);
                    itemBeanTitle.setTitle(category.getName());
                    itemBeanList.add(itemBeanTitle);
                    //由第一个item的类型确定其他同类显示样式。
                    int itemType = SubcategoryItemBean.TYPE_ITEM_ALBUM;
                    CategoryMember firstCategoryMember = categoryMemberList.get(0);
                    if (firstCategoryMember instanceof RadioCategoryMember
                            || firstCategoryMember instanceof RadioQQMusicCategoryMember) {
                        itemType = SubcategoryItemBean.TYPE_ITEM_RADIO_CHANNEL;
                    }
                    //grid内容
                    for (int j = 0, contentSize = categoryMemberList.size(); j < contentSize; j++) {
                        CategoryMember categoryMember = categoryMemberList.get(j);
                        SubcategoryItemBean itemBeanContent = new SubcategoryItemBean();
                        //item类型
                        itemBeanContent.setItemType(itemType);
                        //节目名字
                        itemBeanContent.setName(categoryMember.getTitle());
                        //图片
                        String coverUrl = null;
                        Map<String, ImageFile> imageFiles = categoryMember.getImageFiles();
                        if (imageFiles != null) {
                            ImageFile cover = imageFiles.get(ImageFile.KEY_ICON);
                            if (cover != null) {
                                coverUrl = cover.getUrl();
                            } else {
                                cover = imageFiles.get(ImageFile.KEY_COVER);
                                if (cover != null) {
                                    coverUrl = cover.getUrl();
                                }
                            }
                        }
                        itemBeanContent.setCoverUrl(coverUrl);
                        //资源类型，用于区分播放
                        int resType = OperationAssister.getType(categoryMember);
                        itemBeanContent.setResType(resType);
                        //操作用的节目id
                        long contentId = OperationAssister.getId(categoryMember);
                        itemBeanContent.setId(contentId);
                        //设置正在播放的节目播放状态，播放音乐时正在播放该类型的歌曲并且id一样才显示播放状态
                        boolean isPlaying = TextUtils.equals(String.valueOf(contentId), radioId);
                        itemBeanContent.setSelected(isPlaying);
                        //收听数
                        itemBeanContent.setListenNum(OperationAssister.getListenNum(categoryMember));
                        itemBeanList.add(itemBeanContent);
                    }
                }
            }

        }
        return itemBeanList;
    }
}
