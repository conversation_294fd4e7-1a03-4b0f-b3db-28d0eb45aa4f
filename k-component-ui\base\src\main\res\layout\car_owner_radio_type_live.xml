<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/car_owner_radio_type_live"
    android:layout_width="@dimen/m572"
    android:layout_height="@dimen/m614"
    android:layout_gravity="center"
    android:focusable="true"
    android:visibility="gone"
    tools:visibility="visible">

    <ImageView
        android:id="@+id/radio_live_cover"
        android:layout_width="@dimen/car_owner_radio_center_width"
        android:layout_height="@dimen/car_owner_radio_center_width"
        android:layout_marginTop="@dimen/m116"
        android:src="@drawable/car_owner_radio_entrance_cover"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:layout_width="@dimen/car_owner_radio_center_width"
        android:layout_height="@dimen/car_owner_radio_center_width"
        android:layout_marginTop="@dimen/m116"
        android:src="@drawable/car_owner_radio_entrance_shade"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.kaolafm.kradio.component.ui.base.view.KradioTextView
        android:id="@+id/radio_live_btn"
        android:layout_width="@dimen/m232"
        android:layout_height="@dimen/m86"
        android:layout_marginBottom="@dimen/m60"
        android:background="@drawable/car_owner_radio_entrance_btn"
        android:gravity="center"
        android:paddingBottom="@dimen/m6"
        android:text="进入直播间"
        android:textColor="#FFFFFFFF"
        android:textSize="@dimen/m26"
        app:kt_font_weight="0.7"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <com.kaolafm.kradio.component.ui.base.view.KradioTextView
        android:id="@+id/radio_live_title"
        android:layout_width="@dimen/m250"
        android:layout_height="@dimen/m47"
        android:ellipsize="marquee"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:gravity="center"
        android:singleLine="true"
        android:text="直播间名称"
        android:textSize="@dimen/m32"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/radio_live_btn"
        app:layout_constraintEnd_toEndOf="@+id/radio_live_cover"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintStart_toStartOf="@+id/radio_live_cover" />

    <LinearLayout
        android:id="@+id/radio_live_tag"
        android:layout_width="@dimen/m146"
        android:layout_height="@dimen/m48"
        android:layout_marginBottom="@dimen/m10"
        android:background="@drawable/car_owner_radio_live_tag_bg"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintBottom_toTopOf="@id/radio_live_title"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <FrameLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <com.kaolafm.kradio.component.ui.base.view.RateView
                android:id="@+id/radio_live_tag_playing"
                android:layout_width="@dimen/m33"
                android:layout_height="@dimen/m33"
                app:lottie_autoPlay="true"
                app:lottie_fileName="lottie/rate.json"
                app:lottie_loop="true" />
            <ImageView
                android:id="@+id/card_play_iv"
                android:layout_width="@dimen/m33"
                android:layout_height="@dimen/m33"
                android:src="@drawable/component_play_icon_2"
                android:visibility="gone" />
        </FrameLayout>

        <TextView
            android:id="@+id/radio_live_tag_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/m10"
            android:text="直播中"
            android:textColor="#FFFFFFFF"
            android:textSize="@dimen/m24" />
    </LinearLayout>

    <!--    <ImageView-->
    <!--        android:id="@+id/radio_player"-->
    <!--        app:layout_constraintTop_toTopOf="@id/radio_mask"-->
    <!--        app:layout_constraintLeft_toLeftOf="parent"-->
    <!--        app:layout_constraintRight_toRightOf="parent"-->
    <!--        android:layout_marginTop="@dimen/m134"-->
    <!--        android:layout_width="@dimen/m72"-->
    <!--        android:layout_height="@dimen/m72"-->
    <!--        android:src="@drawable/car_owner_radio_pause"/>-->

</androidx.constraintlayout.widget.ConstraintLayout>