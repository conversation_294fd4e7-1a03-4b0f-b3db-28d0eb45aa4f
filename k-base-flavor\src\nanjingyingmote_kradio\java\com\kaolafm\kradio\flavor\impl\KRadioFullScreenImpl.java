package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.util.Log;
import android.view.View;

import com.kaolafm.kradio.lib.base.flavor.KRadioFullScreenInter;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-12-02 17:42
 ******************************************/
public final class KRadioFullScreenImpl implements KRadioFullScreenInter {

    private View mDecorView;

    private static final String TAG = "KRadioFullScreenImpl";

    @Override
    public boolean initFullScreen(Object... args) {
        Log.i(TAG, "initFullScreen :" + mDecorView);
        if (mDecorView == null) {
            Activity activity = (Activity) args[0];
            mDecorView = activity.getWindow().getDecorView();
        }
        changeDecorViewProp();
        return true;
    }

    @Override
    public boolean hideNavBarOnResume(Object... args) {
        boolean flag = initFullScreen(args);
        return flag;
    }

    private void changeDecorViewProp() {
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001420029321?userId=1229522问题
        int uiFlags = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                | View.SYSTEM_UI_FLAG_FULLSCREEN
                | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY;
        mDecorView.setSystemUiVisibility(uiFlags);
    }
}
