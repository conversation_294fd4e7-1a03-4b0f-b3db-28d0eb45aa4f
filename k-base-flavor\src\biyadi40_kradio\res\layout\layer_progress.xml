<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/radio_fragment_drag_progress_bg_color">

    <TextView
        android:id="@+id/tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@+id/seekbar"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="@dimen/y100"
        android:textColor="@color/white"
        android:textSize="@dimen/text_size13"
        tools:text="1:30/3:20" />

    <ProgressBar
        android:id="@+id/seekbar"
        style="@style/Widget.AppCompat.ProgressBar.Horizontal"
        android:layout_width="match_parent"
        android:layout_height="12dp"
        android:layout_centerVertical="true"
        android:layout_gravity="center"
        android:layout_margin="24dp"
        android:max="100"
        android:progressDrawable="@drawable/drag_progress_drawable"
        tools:progress="40" />

</RelativeLayout>

