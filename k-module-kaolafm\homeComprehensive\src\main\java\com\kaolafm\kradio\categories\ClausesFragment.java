package com.kaolafm.kradio.categories;

import android.graphics.Color;
import android.text.SpannableStringBuilder;
import android.text.method.ScrollingMovementMethod;
import android.view.View;
import android.webkit.WebView;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.mvp.IPresenter;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import io.reactivex.Single;
import io.reactivex.SingleObserver;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URL;

/**
 * 服务条款
 *
 * <AUTHOR>
 * @date 2018/6/22
 */

public class ClausesFragment extends BaseFragment<IPresenter> {


    FrameLayout mFlTitle;
    ImageView mIvTitleBack;
    TextView mTvClausesContent;
    TextView mTvTitleCenter;
    WebView mWvClausesContent;

    @Override
    public void initView(View view) {
        mFlTitle=view.findViewById(R.id.fl_title);
        mIvTitleBack=view.findViewById(R.id.iv_title_back);
        mTvClausesContent=view.findViewById(R.id.tv_clauses_content);
        mTvTitleCenter=view.findViewById(R.id.tv_title_center);
        mWvClausesContent=view.findViewById(R.id.wv_clauses_content);
        mIvTitleBack.setOnClickListener(v -> pop());

        mTvTitleCenter.setText(R.string.qqmusic_clauses_of_service);
        mFlTitle.setBackgroundColor(Color.parseColor("#0A0F29"));
        mTvClausesContent.setMovementMethod(ScrollingMovementMethod.getInstance());
        mWvClausesContent.setBackgroundColor(0);
        getContentTextFromUrl();
    }

    private void getContentTextFromUrl() {
        Single.fromCallable(() -> {
            URL url = new URL("http://y.qq.com/y/static/tips/service_tips.html");
            InputStream is = url.openStream();
            BufferedReader br = new BufferedReader(new InputStreamReader(is));
            String line;
            SpannableStringBuilder ssb = new SpannableStringBuilder();
            while ((line = br.readLine()) != null) {
                if (line.contains("<h1")) {
                    line = line.replace("<h1", "<h1 style=\"display:none\"");
                }
                if (line.contains("<div class=\"content\"")) {
                    line = line.replace("<div class=\"content\"",
                            "<div class=\"content\" style=\"margin:0px;padding:0px;background:#0A0F29;max-width:3000px\"");
                }
                if (line.contains("<div class=\"wrap\"")) {
                    line = line.replace("<div class=\"wrap\"", "<div class=\"wrap\" style=\"color:#fff\"");
                }
                if (line.contains("<p")) {
                    line = line.replace("<p", "<p style=\"font-size:16px; color:#fff\"");
                }
                if (line.contains("<strong")) {
                    line = line.replace("<strong", "<strong style=\"color:#fff\"");
                }
                if (line.contains("class=\"company\"")) {
                    line = line.replace("class=\"company\"", "class=\"company\" style=\"margin-bottom:0px\"");
                }
                if (line.contains("<html")) {
                    line = line.replace("<html", "<html style=\"background:#0A0F29\"");
                }
                ssb.append(line);
            }
            return ssb.toString();
        }).subscribeOn(Schedulers.io())
                .unsubscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new SingleObserver<String>() {

                    private Disposable mDisposable;

                    @Override
                    public void onSubscribe(Disposable d) {
                        mDisposable = d;

                    }

                    @Override
                    public void onSuccess(String data) {
                        if (mWvClausesContent == null) {
                            return;
                        }
                        mWvClausesContent
                                .loadDataWithBaseURL("http://y.qq.com", data, "text/html", "utf-8", null);
                        dispose(mDisposable);
                    }

                    @Override
                    public void onError(Throwable e) {
                        dispose(mDisposable);
                    }
                });

    }

    @Override
    protected int getLayoutId() {
        return R.layout.fragment_clauses;
    }

    @Override
    protected int getLayoutId_Tow() {
        return 0;
    }

    @Override
    protected IPresenter createPresenter() {
        return null;
    }

    private void dispose(Disposable disposable) {
        if (disposable != null) {
            disposable.dispose();
        }
    }

}
