package com.kaolafm.kradio.online.common.event;

/**
 * 跳转动作开始/结束
 */
public class OnlinePlayerFragmentJumpActionEvent {
    public static final int ACTION_START = 0;
    public static final int ACTION_STOP = 1;
    private int action = ACTION_START;

    public OnlinePlayerFragmentJumpActionEvent(int action) {
        this.action = action;
    }

    public int getAction() {
        return action;
    }
}
