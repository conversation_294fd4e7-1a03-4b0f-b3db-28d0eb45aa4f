package com.kaolafm.kradio.flavor.impl;

import android.os.Build;
import android.view.View;
import android.view.Window;

import com.kaolafm.kradio.lib.base.flavor.KRadioDialogPropInter;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-09-17 11:14
 ******************************************/
public final class KRadioDialogPropImpl implements KRadioDialogPropInter {
    private View mDecorView;

    @Override
    public boolean addDialogWindowFlags(Object... args) {
        return false;
    }

    @Override
    public boolean hideNavigationBar(Object... args) {
        if (mDecorView == null) {
            Window window = (Window) args[0];
            mDecorView = window.getDecorView();
            mDecorView.setOnSystemUiVisibilityChangeListener(new View.OnSystemUiVisibilityChangeListener() {
                @Override
                public void onSystemUiVisibilityChange(int visibility) {
                    changeDecorViewProp();
                }
            });
        }
        changeDecorViewProp();
        return true;
    }

    private void changeDecorViewProp() {
        if (Build.VERSION.SDK_INT > 11 && Build.VERSION.SDK_INT < 19) { // lower api
            mDecorView.setSystemUiVisibility(View.GONE);
        } else if (Build.VERSION.SDK_INT >= 19) {
            int uiOptions = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                    | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY;
            mDecorView.setSystemUiVisibility(uiOptions);
        }
    }
}
