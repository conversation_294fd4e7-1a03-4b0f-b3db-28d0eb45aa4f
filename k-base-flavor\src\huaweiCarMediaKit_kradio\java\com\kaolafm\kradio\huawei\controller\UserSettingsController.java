package com.kaolafm.kradio.huawei.controller;


import android.content.Context;

import com.huawei.carmediakit.bean.OperResult;
import com.huawei.carmediakit.bean.SettingItem;
import com.huawei.carmediakit.bean.SettingValue;
import com.huawei.carmediakit.controller.IUserSettingsController;
import com.huawei.carmediakit.reporter.UserSettingsReporter;
import com.iflytek.cloud.ErrorCode;
import com.kaolafm.kradio.huawei.utils.Constant;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.kradio.lib.utils.SharedPreferenceUtil;

import java.util.Collections;
import java.util.List;

public class UserSettingsController implements IUserSettingsController {
    public static final String TAG = Constant.TAG;

    @Override
    public OperResult updateSettingValue(SettingItem settingItem) {
        Logger.i(TAG, "settingItem=id=" + settingItem.getId() + "title=" + settingItem.getTitle());

        SharedPreferenceUtil sp = SharedPreferenceUtil.getInstance(
                AppDelegate.getInstance().getContext(),
                "USER_SETTING_INFO",
                Context.MODE_PRIVATE);

        int i = settingItem.getId();
        if (i == 1) {
            SettingValue.OptionSettingValue optionSettingValue = (SettingValue.OptionSettingValue)settingItem.getSettingValue();
            if (optionSettingValue != null) {
                List list = optionSettingValue.getOptions();
                if (list != null) {
                    for (SettingValue.SettingOption settingOption : (Iterable<SettingValue.SettingOption>) list) {
                        saveOptionData(i, settingOption, sp);
                    }
                }
            }
            UserSettingsReporter.reportSettings(Collections.singletonList(settingItem));
            return new OperResult(0, "");
        }
        if (i == 2) {
            SettingValue.SwitchSettingValue switchSettingValue = (SettingValue.SwitchSettingValue)settingItem.getSettingValue();
            boolean bool = true;
            if (switchSettingValue != null) {
                bool = switchSettingValue.getValue();
            }
            sp.putBoolean("listen_cache", bool);
        } else {
            SettingValue.JumpSettingValue jumpSettingValue = (SettingValue.JumpSettingValue)settingItem.getSettingValue();
            if (jumpSettingValue != null) {
                sp.putString("setting_about", jumpSettingValue.getValue());
            }
        }
        UserSettingsReporter.reportSettings(Collections.singletonList(settingItem));

        return new OperResult(ErrorCode.SUCCESS, "Success");
    }

    public void saveAutoOptionValue(SettingValue.SettingOption paramSettingOption, SharedPreferenceUtil sp) {
        if (paramSettingOption.getValue() != null) {
            if (!paramSettingOption.isSelected())
                return;
            if (paramSettingOption.getValue().equals("自动")) {
                sp.putInt("auto_option", 0);
                return;
            }
            if (paramSettingOption.getValue().equals("高品质")) {
                sp.putInt("auto_option", 1);
                return;
            }
            if (paramSettingOption.getValue().equals("中品质")) {
                sp.putInt("auto_option", 2);
                return;
            }
            sp.putInt("auto_option", 3);
        }
    }

    public void saveOptionData(int paramInt, SettingValue.SettingOption paramSettingOption, SharedPreferenceUtil sp) {
        if (paramInt == 1) {
            saveAutoOptionValue(paramSettingOption, sp);
            return;
        }
        saveQualityOptionValue(paramSettingOption);
    }

    private void saveQualityOptionValue(SettingValue.SettingOption paramSettingOption) {
    }
}
