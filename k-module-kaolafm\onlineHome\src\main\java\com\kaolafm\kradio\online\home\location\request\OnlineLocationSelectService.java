package com.kaolafm.kradio.online.home.location.request;

import com.kaolafm.kradio.online.home.location.bean.OnlineRecomandCityBean;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.HostConstant;

import java.util.List;
import java.util.Map;

import io.reactivex.Single;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.QueryMap;

public interface OnlineLocationSelectService {
    /**
     * 热门搜索城市接口
     *
     * @return
     */
    @Headers({HostConstant.DOMAIN_HEADER_OPEN_KAOLA, HostConstant.DOMAIN_HEADER_HTTPS_PROTOCOL})
    @GET(BuildConfig.NET_REQUEST_VERSION+"/broadcast/recomandCity")
    Single<BaseResult<List<OnlineRecomandCityBean>>> recomandCity();

    /**
     * 城市搜索接口
     *
     * @return
     */
    @Headers({HostConstant.DOMAIN_HEADER_OPEN_KAOLA, HostConstant.DOMAIN_HEADER_HTTPS_PROTOCOL})
    @GET(BuildConfig.NET_REQUEST_VERSION+"/gis/getCityTips")
    Single<BaseResult<List<OnlineRecomandCityBean>>> getTips(@QueryMap Map<String, Object> map);
}
