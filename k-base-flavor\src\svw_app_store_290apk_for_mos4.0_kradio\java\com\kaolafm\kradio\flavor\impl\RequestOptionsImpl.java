package com.kaolafm.kradio.flavor.impl;

import com.kaolafm.KradioApplication;
import com.kaolafm.kradio.lib.base.flavor.NetworkStatusInter;
import com.kaolafm.kradio.lib.base.flavor.RequestOptions;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.opensdk.utils.BaseHttpsStrategy;

public class RequestOptionsImpl implements RequestOptions {

    class ChannelHttpsStrategy extends BaseHttpsStrategy {
        @Override
        public void updateChannelHttpsStrategy() {
            this.mHttpsMap.put("getHost", true);
            this.mHttpsMap.put("replaceUrl", false);
            this.mHttpsMap.put("adParam", true);
            this.mHttpsMap.put("media", true);
            /**
             * 已确认和280保持一致 不需要添加单独判断
             */
            this.mHttpsMap.put("httpsPort", true);
            if (DebugImpl.isDebug()) {
                this.mHttpsMap.put("replaceUrl", true);
                this.mHttpsMap.put("media", false);
                this.mHttpsMap.put("httpsPort", false);
            }
        }
    }

    @Override
    public BaseHttpsStrategy getHttpsStrategy() {
        return new ChannelHttpsStrategy();
    }
}
