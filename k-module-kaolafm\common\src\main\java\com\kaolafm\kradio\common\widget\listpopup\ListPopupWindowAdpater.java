package com.kaolafm.kradio.common.widget.listpopup;

import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.common.view.RecyclerItemBaseHolder;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

public class ListPopupWindowAdpater extends RecyclerView.Adapter<RecyclerView.ViewHolder>{

    private Context context;
    private List<PopupItemBean> datas;
    private OnItemClickListener mOnItemClickListener;

    /**
     * 构造函数
     */
    public ListPopupWindowAdpater(Context context, List<PopupItemBean> datas) {
        this.context = context;
        this.datas = datas ;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View v = LayoutInflater.from(context).inflate(R.layout.list_popup_item, parent, false);
        final RecyclerView.ViewHolder holder = new MyViewHolder(context, v);
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if(mOnItemClickListener!=null){
                    mOnItemClickListener.onItemClick(holder.itemView, holder.getAdapterPosition());
                }
            }
        });
        return holder;
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        MyViewHolder myViewHolder = (MyViewHolder) holder;
        myViewHolder.setRecyclerBaseAdapter(this);
        myViewHolder.onBind(position, datas.get(position));

    }

    @Override
    public int getItemCount() {
        return datas.size();
    }

    /**
     * 对应Item布局中的组件
     */
    class MyViewHolder extends RecyclerItemBaseHolder {

        private TextView textView;
        public MyViewHolder(Context context, @NonNull View itemView) {
            super(itemView);
            textView = itemView.findViewById(R.id.id_tv_popupItemText);
        }

        public void onBind(int position, PopupItemBean bean){
            textView.setTextColor(bean.isSelected()?Color.parseColor("#FFD7000F"):Color.parseColor("#FFFFFFFF"));
            // 设置Item中的值
            textView.setText(bean.getText()+"");
        }
    }

    interface OnItemClickListener{
        void onItemClick(View v, int pos);
    }
    public void setOnItemClickListener(OnItemClickListener onItemClickListener){
        mOnItemClickListener = onItemClickListener;
    }
}
