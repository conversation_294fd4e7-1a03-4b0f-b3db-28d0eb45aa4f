package com.kaolafm.kradio.flavor.impl;


import android.app.Application;
import android.content.Context;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;

import com.kaolafm.kradio.flavor.carnetwork.carnetworkutils.CarAuthUtil;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioAuthInter;


public class KRadioAuthImpl implements KRadioAuthInter {

    private ConnectivityManager mConnectivityManager;
    private int oldStatus;
    private int newStatus;
    private Application context;
    private NetworkInfo info;

    private static class InstanceHolder {
        private final static KRadioAuthImpl sInstance = new KRadioAuthImpl();
    }

    public static KRadioAuthImpl getInstance() {
        return InstanceHolder.sInstance;
    }

    @Override
    public boolean doInitCheckCanPlayInter() {
        PlayerCustomizeManager.getInstance().injectKLCheckCanPlayListener("com.kaolafm.kradio.flavor.impl.CheckCanPlayImpl");
        context = AppDelegate.getInstance().getContext();
        IntentFilter filter = new IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION);
        if (AppDelegate.getInstance().getContext() != null) {
            mConnectivityManager = (ConnectivityManager) AppDelegate.getInstance().getContext().getSystemService(Context.CONNECTIVITY_SERVICE);
            info = mConnectivityManager.getActiveNetworkInfo();
            newStatus = oldStatus = info.getType();
        }
        return true;
    }

    @Override
    public boolean unInitCheckCanPlayInter() {

        return true;
    }

    @Override
    public boolean doCheckAuth(Object... args) {
        if (PlayerCustomizeManager.getInstance().getCheckCanPlayInter() != null) {
            PlayerCustomizeManager.getInstance().getCheckCanPlayInter().checkPlay(args);
        }
        return true;
    }

    @Override
    public boolean authStatus(Object... args) {
        //一个本地保存的鉴权状态  一个是网络是否需要去鉴权
//        !CarAuthUtil.needAuthByNetWork() || CarAuthUtil.AUTH_THROUGH;
        return CarAuthUtil.AUTH_THROUGH;
    }

    @Override
    public boolean netWorkStatus(Object... args) {
        return CarAuthUtil.needAuthByNetWork();
    }


}
