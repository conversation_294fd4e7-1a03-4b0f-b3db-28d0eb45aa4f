package com.kaolafm.kradio.aop;

import android.util.Log;


import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.utils.IntentUtils;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;

@Aspect
public class AppAop {
    private static final String TAG = "AppAop";

    @Around("execution(* com.kaolafm.kradio.lib.base.AppManager.killAll(..))")
    public void AppManager_killAll(ProceedingJoinPoint point) throws Throwable {
        Log.d(TAG,"AppManager_killAll");
        if (AppManager.getInstance().getCurrentActivity() != null) {
            PlayerManagerHelper.getInstance().pause(false);
            IntentUtils.getInstance().startLauncher(AppManager.getInstance().getCurrentActivity());
        } else {
            Log.i(TAG, "killProcess");

            // 添加详细的退出日志
            android.util.Log.e("AppAop", "=== 应用退出被触发 ===");
            android.util.Log.e("AppAop", "方法: AppAop.killProcess()");
            android.util.Log.e("AppAop", "原因: AOP切面杀死进程");
            android.util.Log.e("AppAop", "调用栈:", new Exception("退出调用栈"));
            android.util.Log.e("AppAop", "=== 即将调用 System.exit(0) ===");

            android.os.Process.killProcess(android.os.Process.myPid());
            System.exit(0);
        }
    }
}
