package com.kaolafm.kradio.util;

import android.app.Activity;
import android.util.Log;

import com.mega.nexus.MegaMoveToDisplayStatus;
import com.mega.nexus.app.MegaActivityManager;

public class FlyScreenUtil {

    public static final String TAG = "FlyScreenUtil";

    public static void init() {
        //通过MegaActivityManager的setFlyingScreenMode方法设置飞屏模式
        // 参数mode有四种模式
        // MegaMoveToDisplayStatus.ALLOWED_FLYING
        // MegaMoveToDisplayStatus.ONLY_FLYING_TO_RIGHT
        // MegaMoveToDisplayStatus.ONLY_FLYING_TO_LEFT
        // MegaMoveToDisplayStatus.FORBIDDEN_FLYING
        try {
            Log.i(TAG, "begin init");
            MegaActivityManager.getInstance().setFlyingScreenMode(MegaMoveToDisplayStatus.ALLOWED_FLYING);
        } catch (Exception e) {
            Log.e(TAG, e.toString());
        }
    }
}
