package com.kaolafm.kradio.clientControlerForKradio.ex;

import com.kaolafm.kradio.lib.bean.HistoryItem;

/**
 * <AUTHOR>
 **/
public class DataCorverter {

    static com.kaolafm.sdk.core.ex.bean.HistoryItem toClientHistoryItem(HistoryItem historyItem) {
        if (historyItem == null) return null;
        com.kaolafm.sdk.core.ex.bean.HistoryItem hi = new com.kaolafm.sdk.core.ex.bean.HistoryItem();
        hi.setAudioId(historyItem.getAudioId());
        hi.setAudioTitle(historyItem.getAudioTitle());
        hi.setCategoryId(historyItem.getCategoryId());
        hi.setDuration(historyItem.getDuration());
        hi.setOffline(historyItem.isOffline());
        hi.setOfflinePlayUrl(historyItem.getOfflinePlayUrl());
        hi.setOrderMode(historyItem.getOrderMode());
        hi.setOrderNum(historyItem.getOrderNum());
        hi.setParamOne(historyItem.getParamOne());
        hi.setParamTwo(historyItem.getParamTwo());
        hi.setPicUrl(historyItem.getPicUrl());
        hi.setPlayedTime(historyItem.getPlayedTime());
        hi.setPlayUrl(historyItem.getPlayUrl());
        hi.setRadioId(historyItem.getRadioId());
        hi.setRadioTitle(historyItem.getRadioTitle());
        hi.setShareUrl(historyItem.getShareUrl());
        hi.setSourceUrl(historyItem.getSourceUrl());
        hi.setTimeStamp(historyItem.getTimeStamp());
        hi.setType(historyItem.getType());
        hi.setTypeId(historyItem.getTypeId());
        return hi;
    }
}

