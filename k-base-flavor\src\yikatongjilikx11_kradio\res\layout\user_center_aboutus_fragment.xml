<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/aboutus_homeroom"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_home"
    android:paddingTop="@dimen/y40">

    <RelativeLayout
        android:id="@+id/user_center_aboutus_title_rv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/login_close_btn"
            android:layout_width="@dimen/m80"
            android:layout_height="@dimen/m80"
            android:layout_centerVertical="true"
            android:layout_marginLeft="@dimen/m10"
            android:background="@drawable/color_main_button_click_selector"
            android:padding="@dimen/m22"
            android:scaleType="centerInside"
            android:src="@drawable/globle_arrow_normal" />

        <TextView
            android:id="@+id/login_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:text="@string/person_center_aboutus_str"
            android:textColor="@color/global_title_text_color"
            android:textSize="@dimen/text_size_title1" />
    </RelativeLayout>

    <View
        android:id="@+id/view_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/y1"
        android:background="#19FFFFFF"
        app:layout_constraintTop_toBottomOf="@+id/user_center_aboutus_title_rv" />


    <ScrollView
        android:id="@+id/scroll"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@+id/view_divider">

        <LinearLayout
            android:id="@+id/container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/person_center_aboutus_details_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/aboutus_magin"
                android:layout_marginTop="@dimen/y50"
                android:layout_marginRight="@dimen/aboutus_magin"
                android:lineSpacingMultiplier="1.5"
                android:text="@string/person_center_aboutus_details_str"
                android:textColor="@color/person_center_aboutus_details_color"
                android:textSize="@dimen/text_size_title4"
                app:layout_constraintTop_toBottomOf="@id/user_center_aboutus_title_rv" />

            <TextView
                android:id="@+id/person_center_aboutus_mail_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/aboutus_magin"
                android:layout_marginTop="@dimen/y14"
                android:layout_marginRight="@dimen/aboutus_magin"
                android:lineSpacingMultiplier="1.5"
                android:text="@string/person_center_aboutus_mail_str"
                android:textColor="@color/person_center_aboutus_details_color"
                android:textSize="@dimen/text_size1"
                app:layout_constraintTop_toBottomOf="@id/person_center_aboutus_details_tv" />

            <TextView
                android:id="@+id/person_center_aboutus_gongzhonghao_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/aboutus_magin"
                android:layout_marginTop="@dimen/y14"
                android:layout_marginRight="@dimen/aboutus_magin"
                android:lineSpacingMultiplier="1.5"
                android:text="@string/person_center_aboutus_gongzhonghao_str"
                android:textColor="@color/person_center_aboutus_details_color"
                android:textSize="@dimen/text_size1"
                app:layout_constraintTop_toBottomOf="@id/person_center_aboutus_mail_tv" />

            <ImageView
                android:id="@+id/person_center_aboutus_tingbanlogo_iv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:scaleType="centerInside"
                android:src="@drawable/ic_launcher_yt"
                app:layout_constraintBottom_toTopOf="@id/person_center_aboutus_version_tv"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintVertical_bias="0.77" />

            <TextView
                android:id="@+id/person_center_aboutus_version_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/y24"
                android:gravity="center"
                android:lineSpacingMultiplier="1.5"
                android:text="@string/person_center_aboutus_version_str"
                android:textColor="@color/person_center_aboutus_details_color"
                android:textSize="@dimen/text_size_title4"
                app:layout_constraintBottom_toTopOf="@id/person_center_aboutus_ll" />

            <LinearLayout
                android:id="@+id/person_center_aboutus_ll"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/y130"
                android:gravity="center"
                android:orientation="horizontal"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@id/person_center_aboutus_version_tv">

                <Button
                    android:id="@+id/person_center_aboutus_service_btn"
                    android:layout_width="@dimen/x137"
                    android:layout_height="@dimen/y52"
                    android:layout_marginRight="@dimen/x70"
                    android:background="@drawable/about_us_btn_bg"
                    android:text="@string/person_center_aboutus_service_str"
                    android:textColor="@color/about_us_btn_text_color"
                    android:textSize="@dimen/text_size_title4" />

                <Button
                    android:id="@+id/person_center_aboutus_secret_btn"
                    android:layout_width="@dimen/x137"
                    android:layout_height="@dimen/y52"
                    android:background="@drawable/about_us_btn_bg"
                    android:text="@string/person_center_aboutus_private_str"
                    android:textColor="@color/about_us_btn_text_color"
                    android:textSize="@dimen/text_size_title4" />
            </LinearLayout>
        </LinearLayout>

    </ScrollView>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/web_view_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:background="@drawable/bg_home">

        <WebView
            android:id="@+id/web_view_content"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintWidth_percent="0.9"
            app:layout_constraintHeight_percent="0.9"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/web_view_back"
            style="@style/FragmentBackButton"
            android:layout_marginTop="@dimen/y20"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>