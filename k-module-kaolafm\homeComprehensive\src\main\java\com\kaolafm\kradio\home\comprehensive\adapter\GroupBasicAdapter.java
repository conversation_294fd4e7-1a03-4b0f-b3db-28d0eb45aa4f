package com.kaolafm.kradio.home.comprehensive.adapter;

import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.kaolafm.kradio.component.ui.base.cell.BaseCell;
import com.kaolafm.kradio.component.ui.base.cell.Cell;
import com.kaolafm.kradio.component.ui.base.BindViewHolder;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;

/**
 * 可用于多种类型显示的adpater。对应的数据只需要实现对应的接口或继承对应的基类就可以显示不同类型的item。
 *
 * <AUTHOR>
 * @date 2019-08-15
 */
public class GroupBasicAdapter<V extends View, D extends Cell> extends BaseAdapter<D> {

    private int mCurrentPosition;

    @Override
    protected BaseHolder<D> getViewHolder(ViewGroup parent, int layoutId) {
        View rootView = LayoutInflater.from(parent.getContext()).inflate(R.layout.adapter_base_root_layout, parent, false);
        FrameLayout frameLayout = rootView.findViewById(R.id.root_layout);
        V view = (V) LayoutInflater.from(parent.getContext()).inflate(layoutId, parent, false);
        frameLayout.addView(view);
        return new BindViewHolder<>(rootView);
    }

    @Override
    public int getItemViewType(int position) {
        Cell cell = getItemData(position);
        return cell != null ? cell.getItemType() : RecyclerView.INVALID_TYPE;
    }

    @Override
    public void onViewRecycled(BaseHolder<D> holder) {
        super.onViewRecycled(holder);
        if (holder instanceof BindViewHolder) {
            ((BindViewHolder) holder).unbind();
        }
    }

    public void select(int position) {
        if (position != mCurrentPosition) {
            D selectedItem = getItemData(position);
            if (selectedItem instanceof BaseCell) {
                ((BaseCell) selectedItem).selected = true;
                notifyItemChanged(position);
            }
            D currentItem = getItemData(mCurrentPosition);
            if (currentItem instanceof BaseCell) {
                ((BaseCell) currentItem).selected = false;
                notifyItemChanged(mCurrentPosition);
            }
            mCurrentPosition = position;
        }
    }
}
