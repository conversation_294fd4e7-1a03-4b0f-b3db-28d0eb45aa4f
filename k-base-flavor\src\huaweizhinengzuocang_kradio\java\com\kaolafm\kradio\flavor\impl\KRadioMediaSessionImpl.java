package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.os.Build;

import com.kaolafm.kradio.flavor.media.MyMediaSessionManager;
import com.kaolafm.kradio.lib.base.flavor.KRadioMediaSessionInter;

public class KRadioMediaSessionImpl implements KRadioMediaSessionInter {
    @Override
    public void release(Object... args) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            MyMediaSessionManager.destroyInstance();
        }
    }

    @Override
    public void registerMediaSession(Object... args) {
        Context context = (Context) args[0];
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            MyMediaSessionManager.getInstance().initMediaSession(context);
        }
    }

    @Override
    public void unregisterMediaSession(Object... args) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            MyMediaSessionManager.getInstance().releaseMediaSession();
        }
    }
}
