package com.kaolafm.kradio.flavor.impl;

import android.util.Log;

import com.aptiv.sdssemanticmanager.ISdsSemanticClientListener;
import com.aptiv.sdssemanticmanager.ISdsSemanticConnectionCallback;
import com.aptiv.sdssemanticmanager.SdsSemanticDispatcher;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.IPlayerStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

public class LauncherImpl implements LauncherInter {
    public static String TAG = "LauncherImpl";

    @Override
    public void onStart(Object... args) {
        if (!SdsSemanticDispatcher.getInstance().isConnected()) {
            init();
        }
    }

    @Override
    public void onCreate(Object... args) {

    }

    @Override
    public void onResume(Object... args) {

    }

    @Override
    public void onPause(Object... args) {

    }

    @Override
    public void onStop(Object... args) {

    }

    @Override
    public void onRestart(Object... args) {

    }

    @Override
    public void onDestory(Object... args) {

    }

    private void init() {

        IPlayerStateListener iPlayerStateListener = new IPlayerStateListener() {
            @Override
            public void onIdle(PlayItem playItem) {

            }

            @Override
            public void onPlayerPreparing(PlayItem playItem) {

            }

            @Override
            public void onPlayerPlaying(PlayItem playItem) {
                sendPlayStatusData();
            }

            @Override
            public void onPlayerPaused(PlayItem playItem) {
                sendPlayStatusData();
            }

            @Override
            public void onProgress(PlayItem playItem, long l, long l1) {

            }

            @Override
            public void onPlayerFailed(PlayItem playItem, int i, int i1) {

            }

            @Override
            public void onPlayerEnd(PlayItem playItem) {

            }

            @Override
            public void onSeekStart(PlayItem playItem) {

            }

            @Override
            public void onSeekComplete(PlayItem playItem) {

            }

            @Override
            public void onBufferingStart(PlayItem playItem) {

            }

            @Override
            public void onBufferingEnd(PlayItem playItem) {

            }

            @Override
            public void onDownloadProgress(PlayItem playItem, long l, long l1) {

            }
        };
        PlayerManager.getInstance().addPlayControlStateCallback(iPlayerStateListener);

        SdsSemanticDispatcher.getInstance().initService(AppDelegate.getInstance().getContext(), new ISdsSemanticConnectionCallback() {
            @Override
            public void onServiceConnected() {
                Log.i(TAG, "onServiceConnected:");
                sendPlayStatusData();
                registerListener();
            }

            @Override
            public void onServiceDisconnected() {
                unregisterListener();
            }
        });


    }

    private void unregisterListener() {
        Logger.e(TAG, "unregisterListener: ");
        SdsSemanticDispatcher.getInstance().unregisterListener(SdsSemanticDispatcher.SEMANTIC_CLIENT_TYPE_ONLINE_RADIO);
    }

    private void registerListener() {
        Logger.e(TAG, "registerListener: ");
        SdsSemanticDispatcher.getInstance().registerListener(new ISdsSemanticClientListener() {
            @Override
            public void onSemanticReceived(String jsonString) {
                Log.i(TAG, "onSemanticReceived:json=" + jsonString);
                //处理operation，app根据operation，执行相关的操作
                Operation.parse(jsonString).exe();
            }
        }, SdsSemanticDispatcher.SEMANTIC_CLIENT_TYPE_ONLINE_RADIO);
    }

    private void sendPlayStatusData() {
        int activeStatus = AppDelegate.getInstance().isAppForeground()
                ? SdsSemanticDispatcher.SEMANTIC_CLIENT_ACTIVE_STATUS_FOREGROUND
                : SdsSemanticDispatcher.SEMANTIC_CLIENT_ACTIVE_STATUS_BACKGROUND;
        int playbackStatus = PlayerManager.getInstance().isPlaying()
                ? SdsSemanticDispatcher.SEMANTIC_CLIENT_PLAYBACK_STATUS_PLAYING
                : SdsSemanticDispatcher.SEMANTIC_CLIENT_PLAYBACK_STATUS_PAUSED;
        Log.i(TAG, "SdsSemanticDispatcher.reportMediaStatus with : activeStatus [" + activeStatus + "], playbackStatus [" + playbackStatus + "].");
        SdsSemanticDispatcher.getInstance()
                .reportMediaStatus(SdsSemanticDispatcher.SEMANTIC_CLIENT_TYPE_ONLINE_RADIO, activeStatus, playbackStatus);
    }
}
