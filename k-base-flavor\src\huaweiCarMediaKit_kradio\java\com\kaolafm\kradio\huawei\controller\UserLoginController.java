package com.kaolafm.kradio.huawei.controller;

import com.huawei.carmediakit.bean.LoginInfo;
import com.huawei.carmediakit.bean.OperResult;
import com.huawei.carmediakit.bean.UserInfo;
import com.huawei.carmediakit.constant.ErrorCode;
import com.huawei.carmediakit.controller.IUserLoginController;
import com.huawei.carmediakit.reporter.UserSettingsReporter;
import com.kaolafm.kradio.common.event.LogoutBindEvent;
import com.kaolafm.kradio.huawei.utils.Constant;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.opensdk.account.token.AccessTokenManager;

import org.greenrobot.eventbus.EventBus;

public class UserLoginController implements IUserLoginController {
    public static final String TAG = Constant.TAG;

    @Override
    public OperResult logout() {
        Logger.i(TAG, "logout");

        UserInfoManager.getInstance().logout();
        AccessTokenManager.getInstance().logoutKaola();
        EventBus.getDefault().post(new LogoutBindEvent(LogoutBindEvent.LOGOUT));

        UserInfo userInfo = new UserInfo();
        userInfo.setNickName(UserInfoManager.getInstance().getUserNickName());
        userInfo.setProfilePicUrl(UserInfoManager.getInstance().getUserFavicon());
        userInfo.setOnline(false);
        UserSettingsReporter.reportUserInfo(userInfo);
        UserSettingsReporter.reportUserLogout();
        return null;
    }

    @Override
    public OperResult loginByType(LoginInfo.LoginType loginType) {
        Logger.i(TAG, "loginByType");
        UserInfo userInfo = new UserInfo();
        userInfo.setNickName(UserInfoManager.getInstance().getUserNickName());
        userInfo.setProfilePicUrl(UserInfoManager.getInstance().getUserFavicon());
        userInfo.setOnline(true);
        UserSettingsReporter.reportUserInfo(userInfo);
        return new OperResult(ErrorCode.SUCCESS, "success");
    }

    @Override
    public OperResult onLoginCancel(LoginInfo.LoginType loginType) {
        return null;
    }
}
