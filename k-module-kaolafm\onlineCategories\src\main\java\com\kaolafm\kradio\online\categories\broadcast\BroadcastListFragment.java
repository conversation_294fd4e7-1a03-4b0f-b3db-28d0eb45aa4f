package com.kaolafm.kradio.online.categories.broadcast;

import android.content.res.Configuration;
import android.graphics.Rect;
import android.os.Bundle;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.kradio.common.bean.BroadcastRadioDetailData;
import com.kaolafm.kradio.common.report.ReportUtil;
import com.kaolafm.kradio.common.widget.CustomerRefreshBottom;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.RecyclerViewExposeUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.UrlUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.online.categories.BaseBackFragment;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.lcodecore.tkrefreshlayout.RefreshListenerAdapter;
import com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout;

import java.util.List;

/**
 * 分类广播列表页面
 * Created by kaolafm on 2018/4/25.
 */

public class BroadcastListFragment extends BaseBackFragment<BroadcastPresent>
        implements IBroadcastView, RecyclerViewExposeUtil.OnItemExposeListener {

    RecyclerView mRvBroadcastList;

    TwinklingRefreshLayout mTrflBroadcastListRefresh;

    private BroadcastAdapter mBroadcastAdapter;

    private static final String KEY_BROADCAST_CATEGORY_ID = "broadcastCategoryId";

    private static final String KEY_BROADCAST_CATEGORY_NAME = "categoryName";

    private int mCategoryId;

    private String mCategoryName;

    private BasePlayStateListener mPlayerStateListener;
    private int lineCount = 2;// 默认横板，设置初始化值
    private GridLayoutManager gridLayoutManager;
    View mLoadingView;

    public static BroadcastListFragment newInstance(int categoryId, String categoryName) {
        Bundle args = new Bundle();
        args.putInt(KEY_BROADCAST_CATEGORY_ID, categoryId);
        args.putString(KEY_BROADCAST_CATEGORY_NAME, categoryName);
        BroadcastListFragment fragment = new BroadcastListFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void initView(View view) {
        super.initView(view);

        TextView titleView = (TextView) View.inflate(getContext(), R.layout.bbf_title_center_textview, null);
        titleView.setText(mCategoryName);
        this.addTitleCenterView(titleView);
        this.setTextlineVisible();
//      设置内容区距离上面的间距
//        this.setContentMaginTop();
        View contentView = View.inflate(getContext(), R.layout.online_fragment_broadcast, null);
        this.addContentView(contentView);
        mLoadingView = contentView.findViewById(R.id.loadingView);
        mTrflBroadcastListRefresh = contentView.findViewById(R.id.trfl_broadcast_list_refresh);
        mRvBroadcastList = contentView.findViewById(R.id.rv_broadcast_list);

        int mCurrentOrientation = ResUtil.getOrientation();
        initBroadcastListView(mCurrentOrientation);

        gridLayoutManager = new GridLayoutManager(getContext(), lineCount);
        gridLayoutManager.setSpanCount(lineCount);

        mRvBroadcastList.setLayoutManager(gridLayoutManager);
        ((DefaultItemAnimator) mRvBroadcastList.getItemAnimator()).setSupportsChangeAnimations(false);
        mRvBroadcastList.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                super.getItemOffsets(outRect, view, parent, state);
                if (parent.getChildLayoutPosition(view) < lineCount) {
                    outRect.top = ResUtil.getDimen(R.dimen.y30);
                }
            }
        });
        mBroadcastAdapter = new BroadcastAdapter();

        mBroadcastAdapter.setOnItemClickListener((itemView, viewType, broadcastRadioDetailData, position) -> {
            if (NetworkUtil.isNetworkAvailable(getContext(), true)) {
                PlayerManagerHelper.getInstance().addBroadcastRadioSimpleItems(mPresenter.itemBean2Simple(mBroadcastAdapter.getDataList()));
                PlayerManagerHelper.getInstance().start(String.valueOf(broadcastRadioDetailData.getBroadcastId()), PlayerConstants.RESOURCES_TYPE_BROADCAST);
            }
        });
        mRvBroadcastList.setAdapter(mBroadcastAdapter);

        RecyclerViewExposeUtil exposeUtil = new RecyclerViewExposeUtil();
        exposeUtil.setRecyclerItemExposeListener(mRvBroadcastList, this);

        mPlayerStateListener = new BasePlayStateListener() {
            @Override
            public void onPlayerPlaying(PlayItem playItem) {
                setPlaying();
            }

            @Override
            public void onPlayerPaused(PlayItem playItem) {
                setPlaying();
            }
        };
        PlayerManager.getInstance().addPlayControlStateCallback(mPlayerStateListener);
        mTrflBroadcastListRefresh.setBottomView(new CustomerRefreshBottom(getContext()));
        mTrflBroadcastListRefresh.setHeaderView(null);
        mTrflBroadcastListRefresh.setEnableRefresh(false);
        mTrflBroadcastListRefresh.setAnimation(null);
        mTrflBroadcastListRefresh.setOnRefreshListener(new RefreshListenerAdapter() {
            @Override
            public void onLoadMore(TwinklingRefreshLayout refreshLayout) {
                if (mPresenter != null) {
                    mPresenter.loadMore();
                }
            }
        });
        initBroadcastListView(ResUtil.getOrientation());
    }

    private void setPlaying() {
        PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
        if (playItem != null) {
            long id = 0;
            try {
                id = Long.parseLong(playItem.getAlbumId());
            } catch (Exception e) {

            }
            if (id > 0) {
                mBroadcastAdapter.setSelected(id);
            }
        }
    }


    @Override
    protected BroadcastPresent createPresenter() {
        return new BroadcastPresent(this);
    }

    @Override
    public void initArgs() {
        Bundle args = getArguments();
        if (args != null) {
            this.mCategoryId = args.getInt(KEY_BROADCAST_CATEGORY_ID);
            this.mCategoryName = args.getString(KEY_BROADCAST_CATEGORY_NAME);
        }
    }

    @Override
    public void onStart() {
        super.onStart();
        mPresenter.loadFirstData(mCategoryId);
        showLoading();
    }

    @Override
    public void notifyDataChange(List<BroadcastRadioDetailData> resultList, boolean isLoadMore) {
        boolean enableLoadmore = mPresenter.enableLoadmore();
        mTrflBroadcastListRefresh.setEnableLoadmore(enableLoadmore);
        mTrflBroadcastListRefresh.setAutoLoadMore(enableLoadmore);
        mTrflBroadcastListRefresh.setOverScrollRefreshShow(enableLoadmore);

        mTrflBroadcastListRefresh.finishLoadmore();
        if (isLoadMore) {
            mBroadcastAdapter.addDataList(resultList);
        } else {
            mBroadcastAdapter.setDataList(resultList);
        }
        setPlaying();
        hideLoading();
    }

    @Override
    protected void showLoading() {
        super.showLoading();
        ViewUtil.setViewVisibility(mLoadingView, View.VISIBLE);
    }

    @Override
    protected void hideLoading() {
        super.hideLoading();
        ViewUtil.setViewVisibility(mLoadingView, View.GONE);
    }

    @Override
    public void showError() {
        mTrflBroadcastListRefresh.finishLoadmore();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        PlayerManager.getInstance().removePlayControlStateCallback(mPlayerStateListener);
    }

    @Override
    public void onItemViewVisible(boolean visible, int position) {
        if (visible && mBroadcastAdapter != null) {
            BroadcastRadioDetailData bean = mBroadcastAdapter.getItemData(position);
            ReportUtil.addContentShowEvent("", "1", "",
                    String.valueOf(bean.getBroadcastId()), "无",
                    getPageId(), String.valueOf(bean.getClassifyId()), "" + position);
        }
    }

    static class BroadcastAdapter extends BaseAdapter<BroadcastRadioDetailData> {

        private long mCurrentPlayingId;
        private int mCurrentPosition = -1;

        public BroadcastAdapter() {
        }


        @Override
        protected BaseHolder<BroadcastRadioDetailData> getViewHolder(ViewGroup parent, int viewType) {
            return new BroadcastViewHolder(inflate(parent, R.layout.online_item_subcategory_broadcast_local, viewType));
        }


        @Override
        public long getItemId(int position) {
            return position;
        }

        @Override
        public int getItemCount() {
            return super.getItemCount();
        }

        public void setSelected(long playingId) {
            if (mCurrentPlayingId != playingId) {
                for (int i = 0, size = getItemCount(); i < size; i++) {
                    BroadcastRadioDetailData itemBean = mDataList.get(i);
                    //取消上一个播放状态
                    long broadcastId = itemBean.getBroadcastId();
                    if (broadcastId == mCurrentPlayingId) {
                        itemBean.setPlaying(false);
                        notifyItemChanged(i);
                    }
                    //ID一样就显示播放状态
                    if (broadcastId == playingId) {
                        itemBean.setPlaying(true);
                        notifyItemChanged(i);
                        mCurrentPosition = i;
                    }
                }
                mCurrentPlayingId = playingId;
            } else {
                if (mCurrentPosition != -1) {
                    notifyItemChanged(mCurrentPosition);
                }
            }
        }

        static class BroadcastViewHolder extends BaseHolder<BroadcastRadioDetailData> {

            ImageView mIvBroadcastCover;
            TextView mTvBroadcastName;
            TextView tvListenNum;
            ImageView ivPlay;


            BroadcastViewHolder(final View itemView) {
                super(itemView);
                tvListenNum = itemView.findViewById(R.id.tvListenNum);
                ivPlay = itemView.findViewById(R.id.ivPlay);
                mTvBroadcastName = itemView.findViewById(R.id.tv_broadcast_name);
                mIvBroadcastCover = itemView.findViewById(R.id.iv_broadcast_cover);
            }

            @Override
            public void setupData(BroadcastRadioDetailData broadcastRadioDetailData, int position) {

                if (broadcastRadioDetailData.isPlaying()) {
                    mTvBroadcastName.setActivated(true);
                    itemView.setActivated(true);
                    tvListenNum.setSelected(true);
                    ivPlay.setSelected(true);
                } else {
                    mTvBroadcastName.setActivated(false);
                    itemView.setActivated(false);
                    tvListenNum.setSelected(false);
                    ivPlay.setSelected(false);
                }
                tvListenNum.setText(StringUtil.formatNum(broadcastRadioDetailData.getPlayTimes()));
                mTvBroadcastName.setText(broadcastRadioDetailData.getName() + "  " + broadcastRadioDetailData.getFreq());
                Log.i("BroadcastListFragment", "url = " + broadcastRadioDetailData.getIcon());
                ImageLoader.getInstance().displayImage(itemView.getContext(), UrlUtil.getCustomPicUrl(UrlUtil.PIC_100_100, broadcastRadioDetailData.getIcon()), mIvBroadcastCover,
                        ResUtil.getDrawable(R.drawable.media_default_pic));
            }
        }
    }

    @Override
    protected void showAccordingToScreen(int orientation) {
        super.showAccordingToScreen(orientation);
        ConstraintLayout.LayoutParams lp = (ConstraintLayout.LayoutParams) mLoadingView.findViewById(R.id.llRefresh).getLayoutParams();
        lp.verticalBias = ResUtil.getFloat(R.dimen.online_loading_vertical_bias);
        mLoadingView.findViewById(R.id.llRefresh).setLayoutParams(lp);
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        initBroadcastListView(newConfig.orientation);
        gridLayoutManager.setSpanCount(lineCount);
        mRvBroadcastList.setLayoutManager(gridLayoutManager);
        mBroadcastAdapter.notifyDataSetChanged();
    }

    @Override
    public void onMultiWindowModeChanged(boolean isInMultiWindowMode) {
        super.onMultiWindowModeChanged(isInMultiWindowMode);
        RecyclerView.Adapter adapter = mRvBroadcastList.getAdapter();
        RecyclerView.LayoutManager manager = mRvBroadcastList.getLayoutManager();
        mRvBroadcastList.setAdapter(null);
        mRvBroadcastList.setLayoutManager(null);
        mRvBroadcastList.getRecycledViewPool().clear();
        mRvBroadcastList.setLayoutManager(manager);
        mRvBroadcastList.setAdapter(adapter);
    }

    private void initBroadcastListView(int orientation) {
        int padding = ResUtil.getDimen(R.dimen.y10);

        int globalPaddingRight;
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            lineCount = 2;
            globalPaddingRight = ResUtil.getDimen(com.kaolafm.kradio.lib.R.dimen.x30);
        } else {
            lineCount = 1;
            globalPaddingRight = 0;
        }
        int globalPaddingLeft = ScreenUtil.getGlobalPaddingLeft(orientation) - padding;
        mRvBroadcastList.setPadding(globalPaddingLeft, 0, globalPaddingRight, 0);
    }

}
