<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.kaolafm.kradio.clientControlerForKradio">
        <application>
                <service
                    android:name="com.kaolafm.kradio.clientControlerForKradio.ClientService"
                    android:enabled="true"
                    android:exported="${HAS_EXPORTED}">
                        <intent-filter>
                                <action android:name="com.kaolafm.sdk.client" />
                        </intent-filter>
                </service>
                <receiver android:name="com.kaolafm.kradio.clientControlerForKradio.ClientReceiver">
                        <intent-filter>
                                <action android:name="com.edog.car.server.ACTION" />
                                <action android:name="com.kaolafm.sdk.client" />
                        </intent-filter>
                </receiver>
        </application>
</manifest>