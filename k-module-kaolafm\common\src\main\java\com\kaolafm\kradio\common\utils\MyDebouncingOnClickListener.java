package com.kaolafm.kradio.common.utils;

import android.os.SystemClock;
import android.view.View;

/******************************************
 * 类描述: 自定义View.OnClickListener
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2017-08-11 10:22
 ******************************************/

public abstract class MyDebouncingOnClickListener implements View.OnClickListener {

//    private static boolean enabled = true;

    private long lastClickTime = 0L;
    /**
     * 两次点击时间间隔单位MS
     */
    private long timeInterval = 500L;

//    private static final Runnable ENABLE_AGAIN = new Runnable() {
//        @Override
//        public void run() {
//            enabled = true;
//        }
//    };


    public void setTimeInterval(long timeInterval) {
        this.timeInterval = timeInterval;
    }

    @Override
    public final void onClick(View v) {
//        if (enabled) {
//            enabled = false;
//            v.postDelayed(ENABLE_AGAIN, 500);
//            doClick(v);
//        }
        long nowTime = SystemClock.elapsedRealtime();
        if (nowTime - lastClickTime > timeInterval) {
            doClick(v);
        }
        lastClickTime = SystemClock.elapsedRealtime();
    }

    public abstract void doClick(View v);
}
