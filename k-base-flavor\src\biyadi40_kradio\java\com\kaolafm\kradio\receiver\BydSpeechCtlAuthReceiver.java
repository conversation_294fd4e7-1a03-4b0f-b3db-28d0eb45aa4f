package com.kaolafm.kradio.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

public class BydSpeechCtlAuthReceiver extends BroadcastReceiver {

    private final String SPEECH_CTL_AUTH_ACTION =  "com.byd.action.FUNCTION_UPDATE_RESULT";

    private String TAG = "BydSpeechCtlAuthReceiver";

    @Override
    public void onReceive(Context context, Intent intent) {
        if (SPEECH_CTL_AUTH_ACTION.equals(intent.getAction())){
            String extra_result_code = intent.getStringExtra("EXTRA_RESULT_CODE");//类型为字符串，必填
            String extra_result_message = intent.getStringExtra("EXTRA_RESULT_MESSAGE");//类型为字符串，必填
            Log.i(TAG,"extra_result_code:"+extra_result_code);
            Log.i(TAG,"extra_result_message:"+extra_result_message);
        }
    }
}
