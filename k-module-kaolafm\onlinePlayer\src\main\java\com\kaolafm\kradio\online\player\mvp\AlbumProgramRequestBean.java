package com.kaolafm.kradio.online.player.mvp;

import com.kaolafm.opensdk.api.media.AlbumRequest;

import static com.kaolafm.opensdk.api.media.AlbumRequest.SORT_ACS;

public class AlbumProgramRequestBean {
    private long albumId, audioId;
    private int pageSize, pageNum;
    @AlbumRequest.Sort
    private int sort = SORT_ACS;

    public long getAlbumId() {
        return albumId;
    }

    public void setAlbumId(long albumId) {
        this.albumId = albumId;
    }

    public long getAudioId() {
        return audioId;
    }

    public void setAudioId(long audioId) {
        this.audioId = audioId;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    @Override
    public String toString() {
        return "AlbumProgramRequestBean{" +
                "albumId=" + albumId +
                ", audioId=" + audioId +
                ", pageSize=" + pageSize +
                ", pageNum=" + pageNum +
                ", sort=" + sort +
                '}';
    }
}
