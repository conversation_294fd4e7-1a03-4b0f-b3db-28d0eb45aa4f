package com.kaolafm.kradio.category.radio;

import androidx.fragment.app.Fragment;

import com.kaolafm.kradio.category.FragmentFactory;
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;

/**
 * <AUTHOR>
 **/
public class RadioDispatchPresenter extends BasePresenter<RadioDispatchModel, RadioDispatchContract.IView> implements RadioDispatchContract.IPresenter {

    private Fragment mFragmentLoad;

    public RadioDispatchPresenter(RadioDispatchContract.IView view) {
        super(view);
    }

    @Override
    public void loadData(long categoryId) {
        if (mFragmentLoad == null) {
            mFragmentLoad = FragmentFactory.createRadioTabFragment(categoryId, 0);
        }
        mView.showData(mFragmentLoad);
    }
}
