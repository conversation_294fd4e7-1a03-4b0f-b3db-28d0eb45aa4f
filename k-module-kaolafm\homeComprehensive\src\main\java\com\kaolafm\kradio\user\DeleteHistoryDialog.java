package com.kaolafm.kradio.user;

import android.content.Context;
import androidx.annotation.StringRes;
import android.view.View;
import android.widget.TextView;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.dialog.BaseCenterRectangleDialog;
import com.kaolafm.kradio.lib.utils.AntiShake;


/**
 * <pre>
 *     <AUTHOR> Wenchl
 *     e-mail : <EMAIL>
 *     time   : 2019/03/14
 *     desc   :
 *     version: 1.0
 * </pre>
 */
//历史删除框 暂时改为了CommonBottomDialog ，ui 提出的需求。
public class DeleteHistoryDialog extends BaseCenterRectangleDialog {

    TextView tvDialogCancel;
    TextView tvDialogDefine;
    TextView tvDialogMessage;

    public void onViewClicked(View view) {
        int id = view.getId();
        if (!AntiShake.check(id)){
            if (id == R.id.tv_dialog_cancel){
                dismiss();

            }else if (id == R.id.tv_dialog_define){
                if (mPositiveListener != null){
                    mPositiveListener.onClick(this);
                }
            }
        }
    }

    public DeleteHistoryDialog(Context context) {
        super(context);
    }

    @Override
    protected void initView(View view) {
        tvDialogCancel=view.findViewById(R.id.tv_dialog_cancel);
        tvDialogDefine=view.findViewById(R.id.tv_dialog_define);
        tvDialogMessage=view.findViewById(R.id.tv_dialog_message);
        tvDialogCancel.setOnClickListener(v -> onViewClicked(v));
        tvDialogDefine.setOnClickListener(v -> onViewClicked(v));
    }

    @Override
    protected int getLayoutId() {
        return R.layout.dialog_history_delete;
    }

    public DeleteHistoryDialog setMessage(String message){
        tvDialogMessage.setText(message);
        return this;
    }

    public DeleteHistoryDialog setMessage(@StringRes int resId){
        tvDialogMessage.setText(resId);
        return this;
    }

}
