package com.kaolafm.kradio.user.channel;

import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.http.core.HttpCallback;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/4/29
 */
public class ChannelRequest extends BaseRequest {

    private final ChannelService mChannelService;

    public ChannelRequest() {
        mChannelService = obtainRetrofitService(ChannelService.class);
    }

    public void getChannelList(HttpCallback<List<Channel>> callback) {
        doHttpDeal(mChannelService.getChannelList(), BaseResult::getResult, callback);
    }
}
