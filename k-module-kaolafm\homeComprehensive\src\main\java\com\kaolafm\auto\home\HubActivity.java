package com.kaolafm.auto.home;

import static com.kaolafm.kradio.lib.utils.Constants.CLIENT_EXTRA_TYPE;
import static com.kaolafm.kradio.lib.utils.Constants.INVALID_NUM;
import static com.kaolafm.kradio.lib.utils.Constants.START_PAGE;
import static com.kaolafm.report.event.ButtonClickReportEvent.BUTTON_DISAGREE;
import static com.kaolafm.report.event.ButtonClickReportEvent.BUTTON_START;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.content.res.Configuration;
import android.graphics.Color;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.SystemClock;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Display;
import android.view.WindowManager;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewStub;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.Group;
import androidx.core.app.ActivityCompat;

import com.kaolafm.ad.comprehensive.ADDataHandle.ADMapCallback;
import com.kaolafm.ad.comprehensive.ADDataHandle.GetAdDataMap;
import com.kaolafm.ad.comprehensive.KradioAdAudioManager;
import com.kaolafm.ad.comprehensive.adreport.AdReportImpl;
import com.kaolafm.ad.comprehensive.ads.image.AdvertisingImagerImpl;
import com.kaolafm.ad.comprehensive.ads.image.SplashAdHelper;
import com.kaolafm.ad.comprehensive.conflict.AdConflict;
import com.kaolafm.ad.comprehensive.control.KradioAdSceneConstants;
import com.kaolafm.ad.comprehensive.utils.ADUtils;
import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.ad.timer.TimedAdvertManager;
import com.kaolafm.base.utils.SpUtil;
import com.kaolafm.flavor.AccountInterworkInter;
import com.kaolafm.kradio.common.KradioSDKManager;
import com.kaolafm.kradio.common.KradioSDKManager.OnUsableObserver;
import com.kaolafm.kradio.common.base.BaseApplication;
import com.kaolafm.kradio.common.base.BaseSkinAppCompatActivity;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.kradio.common.utils.CommonUtils;
import com.kaolafm.kradio.common.utils.DiskUtil;
import com.kaolafm.kradio.common.utils.FlavorUtil;
import com.kaolafm.kradio.common.utils.PermissionUtils;
import com.kaolafm.kradio.component.ui.base.skin.SkinHelper;
import com.kaolafm.kradio.config.ConfigSettingManager;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioAppBackgroundInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioApplicationRequestAudioFocusInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioCustomizedStartupLogicInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioDialogActivityInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioFullScreenInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioHubInitInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioHubUserPromptInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioLazyInitInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioOperateClickCellInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioRequestAudioFocusInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioTransStatusBarInter;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import com.kaolafm.kradio.lib.base.flavor.UserCenterInter;
import com.kaolafm.kradio.lib.base.mvp.IPresenter;
import com.kaolafm.kradio.lib.common.ModelConstant;
import com.kaolafm.kradio.lib.init.ModelManager;
import com.kaolafm.kradio.lib.report.ReportUtil;
import com.kaolafm.kradio.lib.sdk.utils.PerformanceSettingMananger;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.ClientConnectControl;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.IntentUtils;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.lib.utils.SharedPreferenceUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.utils.YTLogUtil;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.scene.launcher.InitService;
import com.kaolafm.launcher.LauncherActivity;
import com.kaolafm.opensdk.api.config.ConfigSettingOption;
import com.kaolafm.opensdk.api.config.IConfigSettingOptionListener;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonClickReportEvent;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.util.ReportConstants;

import java.lang.ref.WeakReference;
import java.lang.reflect.Field;


/**
 * 闪屏页，第一个启动页
 * 承载 闪屏功能模块，开屏广告模块，初始化模块
 * 后续会配置语音外调等其他的调用方式
 */
public class HubActivity extends BaseSkinAppCompatActivity {
    // 解决https://app.huoban.com/tables/2100000007530121/items/2300001182976364问题
    private static int delayTime = 1000; // 延迟加载时间
    //    private boolean isGoLauncher = true;
    PermissionUtils mPermissionUtils;
    GetAdDataMap mGetAdDataMap;
    private ViewStub launch_notice;
    private OnUsableObserver mUsableObserver;
    private AdvertisingImagerImpl mAdvertisingImager;

    HubHandler mHubHandler;

    private boolean hubActivityInBackground = false;

    private boolean canGoNextPage = false;

    private boolean isFirstOnResume = true;

//    public static final int STATE_NONE = 100;
//    public static final int STATE_NORMAL_INIT = 101;
//    public static final int STATE_NO_INIT = 102;
//    public static final int STATE_REQUEST_PERMISSION = 103;
//    public static final int STATE_GET_ADDATA = 104;
//    public static final int STATE_GOTO_NEXTPAGE = 105;
//    public static final int STATE_GETDEVICEID_ASYNC = 106;

    private static final int PERMISSION_REQUEST_CODE = 10000;

//    private int mState = STATE_NONE;
//    Map<Integer, Integer> mStateMap = new HashMap<>();

    //    private boolean isExecute = false;
    private static final String TAG = "HubActivity";
    boolean showNotice;

    //是否在权限拒绝退出的时候自我释放音频焦点
    private final boolean isAbandonAudioFocusSelf = false;

    //统计tips显示时常
    protected long startTime = -1;
    private boolean isFromCar = false;

    private boolean shouldShow;

    private TextView tvTitle;
    private TextView tvStart ;
    private TextView tvExit ;
    private CheckBox  checkBox ;
    private TextView tvCb;
    private TextView tvContent;
    private View bgHome;


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        YTLogUtil.logStart(TAG, "onCreate", "start");
//        // 设置闪屏背景黑白适配
//        initWindowBackground();
        getWindow().setBackgroundDrawable(ResUtil.getDrawable(R.drawable.background_splash_comprehensive));
        super.onCreate(savedInstanceState);


        saveConfiguration(false);
        SpUtil.putBoolean(Constants.SP_IS_CLICK_HUB_AGREE, false);
        CommonUtils.getInstance().initGreyStyle(getWindow());
        ModelManager.getInstance().setModel(ModelConstant.MODEL_COMPREHENSIVE);
        KRadioAppBackgroundInter kRadioAppBackgroundImpl = ClazzImplUtil.getInter("KRadioAppBackgroundImpl");
        if (null != kRadioAppBackgroundImpl && kRadioAppBackgroundImpl.isBackgroundStart(this)) {
            startToLauncher(false);
            finish();
            return;
        }
        CommonUtils.getInstance().initGreyStyle(getWindow());
        ModelManager.getInstance().setModel(ModelConstant.MODEL_COMPREHENSIVE);
        Constants.HubActivityStartTime = SystemClock.elapsedRealtime();
        mPermissionUtils = new PermissionUtils(this);

        ConfigSettingManager.getInstance().getConfigSetting(new IConfigSettingOptionListener() {
            @Override
            public void onGetSuccess(ConfigSettingOption configSettingOption) {
                if (configSettingOption.getIsSilent() != null && configSettingOption.getIsSilent() == 1) {
                    Constants.switchGreyStyle = true;
                } else {
                    Constants.switchGreyStyle = false;
                }
                CommonUtils.getInstance().initGreyStyle(getWindow());
            }

            @Override
            public void onGetFailure(ApiException e) {

            }
        });

        KRadioHubUserPromptInter kRadioHubUserPromptInter = ClazzImplUtil.getInter("KRadioHubUserPromptImpl");
        if (kRadioHubUserPromptInter != null && kRadioHubUserPromptInter.showUserPrompt(this)) {
            kRadioHubUserPromptInter.showPrompt(this);
        } else {
            normalOnCreate();
        }

        KRadioDialogActivityInter inter = ClazzImplUtil.getInter("KRadioDialogActivityImpl");
        if (inter != null) inter.handleHubActivity(this);

        ReportHelper.getInstance().initBySdk();
        Intent i = getIntent();
        isFromCar = i.getBooleanExtra("isFromCar", false);

    }

    public void normalOnCreate() {
//        initState();
        appEntryCheck();
        init();
    }

    private void init() {
        ((BaseApplication) getApplication()).addActivity(this);

        //此定制类存在,是需要在权限申请前，申请音频焦点
        KRadioApplicationRequestAudioFocusInter kRadioRequestAudioFocusInter = ClazzImplUtil.getInter("KRadioApplicationRequestAudioFocusImpl");
        if (kRadioRequestAudioFocusInter != null) {
            YTLogUtil.logStart(TAG, "init", "kRadioRequestAudioFocusInter:" + kRadioRequestAudioFocusInter);
            kRadioRequestAudioFocusInter.requestAudioFocusBySelf();
        }
        mPermissionUtils = new PermissionUtils(this);
        // 权限申请挪到LauncherActivity中
        // 其他操作保留
        // requestPermission();
        boolean flag = SharedPreferenceUtil.getInstance(this).getBoolean(SharedPreferenceUtil.PERMISSION_REQUEST, false);
        YTLogUtil.logStart(TAG, "init", "requestPermission flag:" + flag);
        if (flag) {
            appEntryCheck();
        }
        state_normal_init();

        boolean isWindowFullScreen = getResources().getBoolean(com.kaolafm.kradio.lib.R.bool.isWindowFullScreen);
        if (!isWindowFullScreen) {
            KRadioTransStatusBarInter kRadioTransStatusBarInter = ClazzImplUtil.getInter("KRadioTransStatusBarImpl");
            if (kRadioTransStatusBarInter != null) {
                kRadioTransStatusBarInter.changeStatusBarColor(this, com.kaolafm.kradio.lib.R.color.transparent_color);
            }
        }

    }

    @Override
    public int getLayoutId() {
        return R.layout.splash_layout;
    }

    @Override
    public int getLayoutId_Tow() {
        return 0;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        launch_notice = findViewById(R.id.launch_notice);
    }

    @Override
    public void initData() {

    }


    private static class NoticeHolder {
        boolean isChecked = false;
        int web = 0;
    }

    private NoticeHolder noticeHolder;

    private NoticeHolder initNoticeHolder() {
        if (noticeHolder == null) {
            noticeHolder = new NoticeHolder();
        }
        return noticeHolder;
    }

    @Override
    public void initDecorView() {
        try {
            // 只设置状态栏透明，不添加padding
            Class decorViewClazz = Class.forName("com.android.internal.policy.DecorView");
            @SuppressLint("BlockedPrivateApi")
            Field field = decorViewClazz.getDeclaredField("mSemiTransparentBarColor");
            field.setAccessible(true);
            field.setInt(getWindow().getDecorView(), Color.TRANSPARENT);
        } catch (Exception e) {
            Log.w(TAG, "设置状态栏透明失败", e);
        }
    }



    @SuppressLint("CutPasteId")
    private void showNotice() {
        YTLogUtil.logStart(TAG, "showNotice", "start");
        launch_notice.setVisibility(View.VISIBLE);
        if (noticeHolder == null) {
            ToastUtil.showInfo(this, R.string.launcher_agreement_first_toast);
        }
        showNotice = true;
        startTime = System.currentTimeMillis();
//        TextView tvContent = findViewById(R.id.tvContent);
//        TextView tvTitle = findViewById(R.id.tvTitle);
        String content = getString(R.string.launch_notice_content);
        Group group = findViewById(R.id.group);
        Group group1 = findViewById(R.id.group1);
        ImageView ivBack = findViewById(R.id.ivBack);
        int expand = (int) getResources().getDimension(R.dimen.m60);
        ViewUtil.expandViewTouchDelegate(ivBack, expand, expand, expand, expand);

        tvTitle = findViewById(R.id.tvTitle);
        tvStart = findViewById(R.id.tvStart);
        tvExit = findViewById(R.id.tvExit);
        checkBox = findViewById(R.id.cb);
        tvCb = findViewById(R.id.tvCb);
        tvContent = findViewById(R.id.tvContent);
        bgHome = findViewById(R.id.launch_notice_id);

        // 初始化主题
        updateThemeFromSettings();

        ivBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                group1.setVisibility(View.GONE);
                group.setVisibility(View.VISIBLE);
                tvTitle.setText(R.string.launch_notice);
                initNoticeHolder().web = 0;
            }
        });
//        WebView webView = findViewById(R.id.webView);
        tvContent.setText(CommonUtils.getInstance().initAgreementTextAndClick(content
                , v -> {
                    if (AntiShake.check(v.getId())) {
                        return;
                    }
                    if (!NetworkUtil.isNetworkAvailable(HubActivity.this, true)) {
                        return;
                    }
                    String theme = "dark";
                    if ((getResources().getConfiguration().uiMode &
                            Configuration.UI_MODE_NIGHT_MASK) != Configuration.UI_MODE_NIGHT_YES) {
                        theme = "light";
                    }
                    String web = ResUtil.getString(R.string.http_url_server_agreement)
                            + "?theme=" + theme + "&bgColor=transparent&contentSize="
                            + (int) ResUtil.getDimension(R.dimen.m22)
                            + "&showTitle=1"
                            + "&marginL=0"
                            + "&unit=1"
                            + "&marginR=" + ResUtil.getDimen(R.dimen.n33)
                            + "&textIndent=0";
                    Bundle bundle = new Bundle();
                    bundle.putString(Constants.ROUTER_PARAMS_KEY_WEB_URL, FlavorUtil.getHttp443Url(web));
                    bundle.putString(Constants.ROUTER_PARAMS_KEY_WEB_TITLE, getResources().getString(R.string.launcher_agreement0));
                    bundle.putString(Constants.ROUTER_PARAMS_KEY_WEB_PAGE_ID, Constants.PAGE_ID_ACCOUNT_PROTOCOL);
                    RouterManager.getInstance().jumpPage(RouterConstance.WEBVIEW_COMPREHENSIVE_URL, bundle);
                }, v -> {
                    if (AntiShake.check(v.getId())) {
                        return;
                    }
                    if (!NetworkUtil.isNetworkAvailable(HubActivity.this, true)) {
                        return;
                    }
                    String theme = "dark";
                    if ((getResources().getConfiguration().uiMode &
                            Configuration.UI_MODE_NIGHT_MASK) != Configuration.UI_MODE_NIGHT_YES) {
                        theme = "light";
                    }
                    String CUR_PAGE = ResUtil.getString(R.string.http_url_policy)
                            + "?theme=" + theme + "&bgColor=transparent&contentSize="
                            + (int) ResUtil.getDimension(R.dimen.m22)
                            + "&showTitle=1"
                            + "&marginL=0"
                            + "&unit=1"
                            + "&marginR=" + ResUtil.getDimen(R.dimen.n33)
                            + "&textIndent=0";

                    Bundle bundle = new Bundle();
                    bundle.putString(Constants.ROUTER_PARAMS_KEY_WEB_URL, FlavorUtil.getHttp443Url(CUR_PAGE));
                    bundle.putString(Constants.ROUTER_PARAMS_KEY_WEB_TITLE, getResources().getString(R.string.launcher_agreement1));
                    bundle.putString(Constants.ROUTER_PARAMS_KEY_WEB_PAGE_ID, Constants.PAGE_ID_ACCOUNT_POLICY);
                    RouterManager.getInstance().jumpPage(RouterConstance.WEBVIEW_COMPREHENSIVE_URL, bundle);
                }));
        tvContent.setMovementMethod(LinkMovementMethod.getInstance());
        CheckBox cb = findViewById(R.id.cb);
        TextView tvCb = findViewById(R.id.tvCb);
        tvCb.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                cb.performClick();
            }
        });
        cb.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                initNoticeHolder().isChecked = isChecked;
            }
        });
        TextView agreeBtn = findViewById(R.id.tvStart);
        agreeBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if(AntiShake.check(this, 1500)){
                    return;
                }
                //点击事件上报
                ButtonClickReportEvent event = new ButtonClickReportEvent(BUTTON_START);
                ReportHelper.getInstance().addEvent(event);
                ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_USAGE_NOTICE_AGREE, agreeBtn.getText().toString(),
                        getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));

                boolean agree = cb.isChecked();
                if (agree) {
                    SpUtil.putBoolean(Constants.SP_IS_CLICK_HUB_AGREE, true);
                    ClientConnectControl.instance.notifyProtocolReceived();
                    showNotice = false;
                    ReportUtil.addPageShowEvent(startTime, getPageId());
                    startTime = -1;
                    ConfigSettingManager.getInstance().getConfigSetting(new IConfigSettingOptionListener() {
                        @Override
                        public void onGetSuccess(ConfigSettingOption configSettingOption) {
                            if (configSettingOption.getAgreementVersion() != null
                                    && configSettingOption.getPrivacyPolicyVersion() != null) {
                                SpUtil.putInt(Constants.SP_POLICY_VERSION, configSettingOption.getPrivacyPolicyVersion());
                                SpUtil.putInt(Constants.SP_AGREEMENT_VERSION, configSettingOption.getAgreementVersion());
                            }
                        }

                        @Override
                        public void onGetFailure(ApiException e) {

                        }
                    });
                    ClientConnectControl.instance.notifyProtocolReceived();
                    normalInit();
                    agreeResume();
                } else {
                    ToastUtil.showInfo(HubActivity.this, R.string.launcher_agreement_first_toast);
                }
            }
        });
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_USAGE_NOTICE_AGREE, agreeBtn.getText().toString(),
                getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
        TextView exitBtn = findViewById(R.id.tvExit);
        exitBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //点击事件上报
                ButtonClickReportEvent event = new ButtonClickReportEvent(BUTTON_DISAGREE);
                ReportHelper.getInstance().addEvent(event);
                ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_USAGE_NOTICE_DISAGREE, exitBtn.getText().toString(),
                        getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
                ReportUtil.addPageShowEvent(startTime, getPageId());
                startTime = -1;
                ClientConnectControl.instance.notifyProtocolRejected();
                finish();
            }
        });
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_USAGE_NOTICE_DISAGREE, exitBtn.getText().toString(),
                getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
        if (noticeHolder != null) {
            cb.setChecked(noticeHolder.isChecked);
            switch (noticeHolder.web) {
                case 0:
                    break;
                case 1:
                    group1.setVisibility(View.VISIBLE);
                    group.setVisibility(View.GONE);
                    tvTitle.setText("");
                    break;
                case 2:
                    group1.setVisibility(View.VISIBLE);
                    group.setVisibility(View.GONE);
                    tvTitle.setText("");
                    break;
            }
        }
    }

    private void agreeResume() {
        YTLogUtil.logStart(TAG, "onResume.agreeResume", "start , canGoNextPage : " + canGoNextPage);
        hubActivityInBackground = false;
        if (showNotice) {
            return;
        }
        if (canGoNextPage) {
            exposeAd();
        }
        if (!isFirstOnResume && !isAbandonAudioFocusSelf) {
            initAudioFocus();
        }
        isFirstOnResume = false;
    }

//    private void initState() {
//        mState = STATE_NONE;
//        //这里记录不能进行状态跳转的状态组合
//        mStateMap.put(STATE_NO_INIT, STATE_NORMAL_INIT);
//    }

//    private void updateState(int aState) {
//        mState = aState;
//        updateState();
//    }

//    private void updateNextState(int toState) {
//        //查找当前状态是否可以跳转到下一个状态
//        if (mStateMap.get(mState) != null) {
//            return;
//        }
//        updateState(toState);
//    }

//    private void updateState() {
//        YTLogUtil.logStart(TAG, "updateState", "mState = " + mState);
//        switch (mState) {
//            case STATE_NO_INIT:
//            case STATE_GETDEVICEID_ASYNC: {
//                break;
//            }
//            case STATE_NORMAL_INIT: {
////                initAndActivateSDK();
////                //是否显示云听使用提示
////                shouldShow = !ClientConnectControl.instance.isProtocolReceived();
////                if (shouldShow) {
////                    ClientConnectControl.instance.notifyProtocolRejected();
////                    showNotice();
////                    return;
////                }
////                ClientConnectControl.instance.notifyProtocolReceived();
////                normalInit();
//                break;
//            }
//            case STATE_GET_ADDATA: {
////                initSDKPre();
//                break;
//            }
//            case STATE_GOTO_NEXTPAGE: {
//
////                goNextPage();
//                break;
//            }
//            default:
//                break;
//        }
//    }

    private boolean isNormalInited = false;
    private void state_normal_init(){
        if (isNormalInited){
            return;
        }
        isNormalInited = true;
//        initAndActivateSDK();
        //是否显示云听使用提示
        shouldShow = !ClientConnectControl.instance.isProtocolReceived();
        if (shouldShow) {
            ClientConnectControl.instance.notifyProtocolRejected();
            showNotice();
            return;
        }
        ClientConnectControl.instance.notifyProtocolReceived();
        normalInit();
    }

    private void initLocation() {
        InitService.getLocation();
    }

    public void normalInit() {
        initTheme();
        initFullScreen();

        mGetAdDataMap = GetAdDataMap.getInstance();
        mHubHandler = new HubHandler(this);

        initAudioFocus();
        handleOutCallAudio();
        notifyAppOpen();
        ClientConnectControl.instance.notifyAppOpen();
        KRadioHubInitInter kRadioHubInitInter = ClazzImplUtil.getInter("KRadioHubInitImpl");
        if (kRadioHubInitInter != null && kRadioHubInitInter.hasPrivacyMode(this)) {
            return;
        }
//        updateState(STATE_GET_ADDATA);
        initSDKPre();
    }

    private void notifyAppOpen() {
        IntentUtils.getInstance().notifyAppOpen(AppDelegate.getInstance().getContext());
    }

    /**
     * 所有需要改变启动正常初始化流程的逻辑放在这里
     */
    private void appEntryCheck() {
        //空间不足判断
        if (!DiskUtil.checkFreeSpace(this)) {
            //弹出空间不足的提示
            DiskUtil.createDialog(this);
//            updateState(STATE_NO_INIT);
        }
        state_normal_init();
//        updateNextState(STATE_NORMAL_INIT);
    }

    /**
     * 新增语音外调SDK launchApp自动播放逻辑兼容
     */
    private void handleOutCallAudio() {
        Intent intent = getIntent();
        if (intent != null) {
            boolean autoPlay = IntentUtils.getInstance().isAutoPlay(intent);
            YTLogUtil.logStart(TAG, "onCreate", "start autoPlay = " + autoPlay);
            if (autoPlay) {
                if (!PlayerManagerHelper.getInstance().isPlaying()) {
                    if(gainAudioFocusSucceed()){
                        PlayerManagerHelper.getInstance().play(true);
                    }
                }
            }
        }
    }

    /**能抢到焦点时才播放*/
    private boolean gainAudioFocusSucceed(){
        if (PlayerManager.getInstance().getCurrentAudioFocusStatus() > 0) {
            return true;
        }

        return PlayerManager.getInstance().requestAudioFocus();
    }

    private void initAudioFocus() {
        YTLogUtil.logStart(TAG, "initAudioFocus", "start");
        //KRadioApplicationRequestAudioFocusImpl 定制实现类存在则代码在权限申请前就请求音频焦点了（见com.kaolafm.auto.home.HubActivity.onCreate），
        // 不存在，再使用原有音频焦点申请逻辑。
        KRadioApplicationRequestAudioFocusInter kRadioApplicationRequestAudioFocusInter = ClazzImplUtil.getInter("KRadioApplicationRequestAudioFocusImpl");
        KRadioRequestAudioFocusInter kRadioRequestAudioFocusInter = null;
        if (kRadioApplicationRequestAudioFocusInter == null) {
            kRadioRequestAudioFocusInter = ClazzImplUtil.getInter("KRadioRequestAudioFocusImpl");
            if (kRadioRequestAudioFocusInter != null) {
                kRadioRequestAudioFocusInter.requestAudioFocusBySelf();
            }
        }
        YTLogUtil.logStart(TAG, "initAudioFocus", "end, infer: = " + kRadioRequestAudioFocusInter);
    }

    private void initTheme() {
        if (setTheme()) {
            setTheme(R.style.AppThemeCompat_spempty);
        }
    }

    private void initFullScreen() {
        final KRadioFullScreenInter kRadioFullScreenInter = ClazzImplUtil.getInter("KRadioFullScreenImpl");
        if (kRadioFullScreenInter != null) {
            kRadioFullScreenInter.initFullScreen(this);
        }
    }

    public void exposeSprendAd() {
        int id = GetAdDataMap.getADSpaceBySceneID(KradioAdSceneConstants.SPREND_SCENE);
//        id = -1;// 去掉启动时开屏广告
        String acceptedAdTypes = ADUtils.getADTypeStr(KradioAdSceneConstants.AD_TYPE_AUDIO,
                KradioAdSceneConstants.AD_TYPE_IMAGE,
                KradioAdSceneConstants.AD_TYPE_AUDIO_IMAGE);

        AdvertisingManager.getInstance().exposePreloading(
                String.valueOf(id),
                KradioAdSceneConstants.SUB_TYPE_SPRAND_SCREEN,
                String.valueOf(ScreenUtil.getScreenWidth()),
                String.valueOf(ScreenUtil.getScreenHeight()),
                acceptedAdTypes,
                ""
        );
    }

    private void initGetAdData() {
        mGetAdDataMap.registerCallBack(new ADMapCallback() {
            @Override
            public void updateADMapData() {
//                updateState(STATE_GOTO_NEXTPAGE);
                goNextPage();
                mGetAdDataMap.unregisterCallBack(this);
            }

            @Override
            public void handleFail() {
//                updateState(STATE_GOTO_NEXTPAGE);
                goNextPage();
                mGetAdDataMap.unregisterCallBack(this);
            }
        });
        mGetAdDataMap.getAdData();
    }

    private void exposeAd() {
        YTLogUtil.logStart(TAG, "exposeAd", "start");
        KradioAdAudioManager.getInstance().init();
        if (mPermissionUtils.isShowDialog()) {
            return;
        }
        if (isFromCar) {
            startToLauncher();
            return;
        }
        SplashAdHelper.setTargetActivity(LauncherActivity.class);
        mAdvertisingImager = new AdvertisingImagerImpl();
        mAdvertisingImager.createSplashAdView(this);
        mAdvertisingImager.registAdListener(adListener);

        AdvertisingManager.getInstance().setImager(mAdvertisingImager);
        AdvertisingManager.getInstance().setReporter(new AdReportImpl());

        int timedAdvertId = GetAdDataMap.getADSpaceBySceneID(KradioAdSceneConstants.TIMER_SCENE);
        if (timedAdvertId > 0) {
            TimedAdvertManager.getInstance().start(timedAdvertId, String.valueOf(ScreenUtil.getScreenWidth()), String.valueOf(ScreenUtil.getScreenHeight()));
        }
        AdConflict.init();
        exposeSprendAd();
    }

    private final AdvertisingImagerImpl.AdListener adListener = () -> {
        startToLauncher();
        finish();
    };

    // AOP 会侵入修改这个方法，在启辰和日产上会切换主题
    public boolean setTheme() {
        return false;
    }

    private void requestPermission() {
        if (SharedPreferenceUtil.getInstance(this).getBoolean(SharedPreferenceUtil.PERMISSION_REQUEST, false)) {
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                Log.d(TAG, "no requestPermission");
            }, 500);
            appEntryCheck();
//            updateState(STATE_NORMAL_INIT);
            state_normal_init();
        } else {
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                Log.d(TAG, "do requestPermission");
            }, 500);
            ActivityCompat.requestPermissions(this, mPermissionUtils.getPerms(), PERMISSION_REQUEST_CODE);
            SharedPreferenceUtil.getInstance(this).putBoolean(SharedPreferenceUtil.PERMISSION_REQUEST, true);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        Log.d(TAG, "do onRequestPermissionsResult");
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
//        if (mState != STATE_NO_INIT) {
//            updateState(STATE_NORMAL_INIT);
//            state_normal_init();
//        }

        state_normal_init();
    }

    private void initSDK() {
        //这里如果sdk首次启动，等待初始化完成再调用广告接口，否则直接调用
        mUsableObserver = this::initGetAdData;
        KradioSDKManager.getInstance().addUsableObserver(mUsableObserver);

        // 注册登录接口
        AccountInterworkInter mAccountInterworkInter = ClazzImplUtil.getInter("AccountInterworkImpl");
        if (mAccountInterworkInter != null && mAccountInterworkInter.isOpenThirdPartyAccount()) {
            mAccountInterworkInter.bindAccount();
        }

        //2秒接口没有返回直接当作失败处理
        mHubHandler.postDelayed(() -> {
            Message msg = new Message();
            msg.what = 1;
            mHubHandler.sendMessage(msg);
        }, 2 * 1000);
    }

    private void initAndActivateSDK() {
//        KRadioLazyInitInter inter = ClazzImplUtil.getInter("KRadioLazyInitImpl");
//        if (inter == null || !inter.isEnableLazyInit()) {
//            KradioSDKManager.getInstance().initAndActivate();
//        }
    }

    private void initSDKPre() {
        YTLogUtil.logStart(TAG, "initSDKPre", "");
//      这块是为了确保在第一次启动的情况下，同意权限，haspermission 和 onPermissionsGranted 会走两遍，避免重复启动。
        KRadioLazyInitInter inter = ClazzImplUtil.getInter("KRadioLazyInitImpl");
        if (shouldShow && inter != null) {
            inter.afterPrivacyEnable();
        }
        //fixed 初次初始化时还没有定位权限,所以会导致定位失败，需要在获取权限后重新定位
        initLocation();
//        if (isGoLauncher) {
//            isGoLauncher = false;
//        } else {
//            return;
//        }
        initSDK();
        //      默认不开启
        if (PerformanceSettingMananger.getInstance().getHomeIsStartSplash()) {
            delayTime = 3000;
        }
    }

    // 降级，避免清栈导致的进程重启
    protected void startToLauncher() {
        // 如果是副屏来源，默认不使用 NEW_TASK|CLEAR_TASK，走复用/前置策略
        startToLauncher(!isFromCar);
    }

    protected void startToLauncher(boolean newTask) {
        // 使用全新 Intent，避免复用当前 Intent 上的历史 flags；保留必要的 extras 便于后续页面跳转
        Intent intent = new Intent(HubActivity.this, LauncherActivity.class);
        Intent src = getIntent();
        if (src != null && src.getExtras() != null) {
            intent.putExtras(src.getExtras());
        }

        // 选择 flags：
        // 1) 默认（非副屏/常规冷启动）可使用 NEW_TASK，并按需 CLEAR_TASK
        // 2) 副屏/二次启动等场景：避免 CLEAR_TASK，优先 REORDER_TO_FRONT/RESET_TASK_IF_NEEDED，尽量复用现有任务
        if (newTask) {
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            if (!isFromCar) {
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);
            } else {
                intent.addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT | Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED);
            }
        } else {
            intent.addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT | Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED);
        }

        // 透传/覆盖关键参数
        intent.putExtra(CLIENT_EXTRA_TYPE, src != null ? src.getIntExtra(CLIENT_EXTRA_TYPE, INVALID_NUM) : INVALID_NUM);
        intent.putExtra(START_PAGE, src != null ? src.getIntExtra(START_PAGE, INVALID_NUM) : INVALID_NUM);

        // 记录来源与最终 flags，便于后续排查
        YTLogUtil.logStart(TAG, "startToLauncher", "newTask=" + newTask + ", isFromCar=" + isFromCar
                + ", intentFlags=0x" + Integer.toHexString(intent.getFlags()));

        startActivity(intent);
    }


    /**
     * true -> 白  false ->黑
     */
    private void setThemeByIsDay(boolean isDayModel) {

        tvTitle.setTextColor(ResUtil.getColor(isDayModel ? R.color.global_title_text_color_day : R.color.global_title_text_color));
        tvStart.setTextColor(ResUtil.getColor(isDayModel ? R.color.global_title_text_color_day : R.color.global_title_text_color));
        tvStart.setBackgroundResource(isDayModel ? R.drawable.message_details_btn_bg_day : R.drawable.message_details_btn_bg);
        tvExit.setTextColor(ResUtil.getColor(isDayModel ? R.color.global_title_text_color_day : R.color.global_title_text_color));
        tvExit.setBackgroundResource(isDayModel ? R.drawable.message_details_btn_bg2_day : R.drawable.message_details_btn_bg2);
        checkBox.setBackgroundResource(isDayModel ? R.drawable.sl_cb_day : R.drawable.sl_cb);
        tvCb.setTextColor(ResUtil.getColor(isDayModel ? R.color.global_title_text_color_day : R.color.global_title_text_color));
        tvContent.setTextColor(ResUtil.getColor(isDayModel ? R.color.global_title_text_color_day : R.color.global_title_text_color));
        bgHome.setBackgroundResource(isDayModel ? R.drawable.bg_home_day : R.drawable.bg_home);
    }

//    private void initWindowBackground() {
//        boolean isDayMode = getCurrentThemeIsDayMode();
//        getWindow().setBackgroundDrawable(ResUtil.getDrawable(isDayMode ? R.drawable.background_splash_comprehensive_day : R.drawable.background_splash_comprehensive_night));
//        Log.d(TAG, "初始化闪屏背景: " + (isDayMode ? "白天" : "黑夜"));
//    }
//
//    /**
//     * 获取当前主题是否为白天模式
//     * @return true=白天模式, false=黑夜模式
//     */
//    private boolean getCurrentThemeIsDayMode() {
//        try {
//            String theme = getCurrentThemeFromSettings();
//            if (!TextUtils.isEmpty(theme)) {
//                return !"theme.night".equals(theme);
//            }
//            return true;
//        } catch (Exception e) {
//            Log.w(TAG, "读取主题失败，默认白天模式", e);
//            return true;
//        }
//    }

    /**
     * 从Settings数据库更新主题
     */
    private void updateThemeFromSettings() {
        String settingsTheme = getCurrentThemeFromSettings();
        if ("theme.night".equals(settingsTheme)) {
            Log.d(TAG, "变为黑夜模式");
            setThemeByIsDay(false);
        } else {
            Log.d(TAG, "变为白天模式");
            setThemeByIsDay(true);
        }
    }

    /**
     * 监听主题变化事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onThemeChangeEvent(UserCenterInter.ThemeChangeEvent event) {
        Log.d(TAG, "收到主题变化事件: " + event.getTheme());
        if (tvTitle == null || tvStart == null || tvExit == null ||
            checkBox == null || tvCb == null || tvContent == null || bgHome == null) {
            Log.w(TAG, "UI控件未初始化，跳过主题更新");
            return;
        }

        // 处理isSameTheme事件：读取当前Settings确认主题状态
        if ("isSameTheme".equals(event.getTheme())) {
            Log.d(TAG, "收到isSameTheme事件，读取当前Settings主题状态");
            updateThemeFromSettings();
            return;
        }

        // 根据皮肤框架的主题事件更新UI
        // SkinHelper.NIGHT_SKIN = "night.skin", SkinHelper.DAY_SKIN = "day.skin"
        if ("night.skin".equals(event.getTheme())) {
            Log.d(TAG, "EventBus: 变为黑夜模式");
            setThemeByIsDay(false);
        } else if ("day.skin".equals(event.getTheme())) {
            Log.d(TAG, "EventBus: 变为白天模式");
            setThemeByIsDay(true);
        } else {
            Log.d(TAG, "EventBus: 未知主题 " + event.getTheme() + "，保持当前状态");
            // 不做任何操作，保持当前主题状态
        }
    }

    private void goNextPage() {
        // 此处与onResume配合，保证HubActivity在前台时才跳转，在后台时不跳转；直到其回到前台时再跳转
        YTLogUtil.logStart(TAG, "goNextPage", "hubActivityInBackground : " + hubActivityInBackground);
        mGetAdDataMap.clearAllCB();
        canGoNextPage = true;
        if (!hubActivityInBackground) {
            exposeAd();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        hubActivityInBackground = true;
    }

    @Override
    protected void onStart() {
        super.onStart();
    }

    @Override
    protected IPresenter createPresenter() {
        return null;
    }

    @Override
    protected void onStop() {
        super.onStop();
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        YTLogUtil.logStart(TAG, "onNewIntent", "start");
        KRadioOperateClickCellInter settingModelInter = ClazzImplUtil.getInter("KRadioOperateClickImpl");
        if (settingModelInter != null && settingModelInter.startToLauncher() && settingModelInter.isOperate(intent)) {
            startToLauncher();
        }

    }

    @Override
    public void onResume() {
        super.onResume();
        YTLogUtil.logStart(TAG, "onResume", "start");



        hubActivityInBackground = false;
        if (showNotice) {
            return;
        }
        if (canGoNextPage) {
            exposeAd();
        }
        if (!isFirstOnResume && !isAbandonAudioFocusSelf) {
            initAudioFocus();
        }
        isFirstOnResume = false;
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);

        // 记录配置变化，用于多屏适配调试
        try {
            WindowManager wm = getWindowManager();
            Display display = wm.getDefaultDisplay();
            DisplayMetrics metrics = new DisplayMetrics();
            display.getRealMetrics(metrics);

            Log.d(TAG, String.format("onConfigurationChanged: displayId=%d, size=%dx%d, density=%.2f",
                  display.getDisplayId(), metrics.widthPixels, metrics.heightPixels, metrics.density));
        } catch (Exception e) {
            Log.w(TAG, "记录配置变化失败", e);
        }
    }

    @Override
    public void finish() {
        super.finish();
    }

    @Override
    protected void onRestart() {
        super.onRestart();
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && !hubActivityInBackground) {
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public boolean useEventBus() {
        return true;
    }

    @Override
    protected void onDestroy() {
        if (mHubHandler != null) {
            mHubHandler.removeCallbacksAndMessages(null);
        }

        super.onDestroy();
        ((BaseApplication) getApplication()).removeActivity(this);
        if (mAdvertisingImager != null && adListener != null) {
            mAdvertisingImager.unregirstAdListener(adListener);
            mAdvertisingImager.destroySplashAdView();
            mAdvertisingImager.destroyAdView();
        }
        KradioSDKManager.getInstance().removeUsableObserver(mUsableObserver);

        //大众账号需求
        KRadioCustomizedStartupLogicInter kRadioCustomizedStartupLogicInter = ClazzImplUtil.getInter("KRadioCustomizedStartupLogicInterImpl");
        if (null != kRadioCustomizedStartupLogicInter) {
            kRadioCustomizedStartupLogicInter.release();
        }
    }

    //防止内存泄漏
    static class HubHandler extends Handler {
        private final WeakReference<Activity> mWeakReference;

        public HubHandler(Activity act) {
            mWeakReference = new WeakReference<>(act);
        }

        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            HubActivity mainActivity = (HubActivity) mWeakReference.get();
            if (msg.what == 1) {
                if (mainActivity != null) {
                    handleADGetFail(mainActivity);
                }
            }
            mWeakReference.clear();
        }
    }

    private static void handleADGetFail(HubActivity mainActivity) {
        YTLogUtil.logStart(TAG, "handleADGetFail", "2秒接口没有返回直接当作失败处理");
        if (mainActivity.mGetAdDataMap.mState != GetAdDataMap.STATE_ERROR && mainActivity.mGetAdDataMap.mState != GetAdDataMap.STATE_SUCCESS) {
            mainActivity.mGetAdDataMap.mState = GetAdDataMap.STATE_NONE;

//            mainActivity.updateState(STATE_GOTO_NEXTPAGE);
            mainActivity.goNextPage();
        }
    }

    public String getPageId() {
        return Constants.PAGE_ID_USE_TIPS;
    }
}
