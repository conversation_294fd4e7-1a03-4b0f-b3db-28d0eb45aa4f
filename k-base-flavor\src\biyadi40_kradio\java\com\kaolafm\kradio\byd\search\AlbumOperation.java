package com.kaolafm.kradio.byd.search;

import android.content.Intent;
import android.util.Log;

import com.kaolafm.kradio.clientControlerForKradio.ClientImpl;

/**
 * 音乐节目搜索
 * 参数定义：
 * 除来源/专辑、季/季次、期/集以外，其余参数字段可任意组合，三方应用可以根据应用功能选择对接字段，
 * 若不支持按照单独的参数进行搜索，可以将单独参数字段，按照关键词进行搜索，但是智能语音传过来的这些参数必须处理
 *
 * intent.putExtra("IS_BACKGROUND", "TRUE");//字符串类型，该参数非必须项，参数值为"TRUE"时为导航在前台，
 * 参数值为"FALSE"时，默认前台
 * intent.putExtra("EXTRA_KEYWORDS_SEARCH","泡沫");//字符串类型,歌名
 * intent.putExtra("EXTRA_ARTIST_SEARCH","邓紫棋|华晨宇|beyond");//字符串类型，歌手/乐队,多个歌手时用"|"分隔,
 * intent.putExtra("EXTRA_GENDER_SEARCH","男|女");//字符串类型，性别，多个性别时用"|"分隔
 * intent.putExtra("EXTRA_SOURCE_SEARCH","贝瓦儿歌");//字符串类型，来源/专辑/榜单
 * intent.putExtra("EXTRA_PROGRAM_TAGS","80年代|热门");//字符串类型，标签（包含乐器、场景、年代、心情、主题、影视曲类、热门、人群），多个标签时用"|"分隔
 * intent.putExtra("EXTRA_GENRE_SEARCH","流行|摇滚");//字符串类型，流派，多个流派时用"|"分隔
 * intent.putExtra("EXTRA_AREA_SEARCH","日韩|港台");//字符串类型，区域，多个区域时用"|"分隔
 * intent.putExtra("EXTRA_LANG_SEARCH","华语|日语");//字符串类型，语言，多个语言时用"|"分隔
 * intent.putExtra("EXTRA_PROGRAM_TAGS","现场版");//字符串类型，版本
 * intent.putExtra("EXTRA_SEASON_TAGS","第一季");//字符串类型，季/季次
 * intent.putExtra("EXTRA_EPISODE_TAGS","第一期");//字符串类型，期/集
 * intent.putExtra("EXTRA_QUALITY_TAGS","高品质");//字符串类型，品质
 * 返回值定义：
 * 0：执行成功
 * 1：其他执行失败
 * 2：超出可设置的范围
 * 3：当前场景不支持该功能
 * 4：需手动操作用户协议后执行
 * 5：需登录后支持
 * 6：网络异常
 * 7：当前功能中暂无数据信息
 */
public class AlbumOperation extends Operation{
    private static final String TAG = AlbumOperation.class.getSimpleName();

    public String category = "129"; //音乐曲库

    public AlbumOperation(ClientImpl client, Intent intent) {
        super(client, intent);
    }

    @Override
    public void exe() {
        Log.i(TAG, "exe");
        String keywords = intent.getStringExtra("EXTRA_KEYWORDS_SEARCH");
        String artist = intent.getStringExtra("EXTRA_ARTIST_SEARCH");
        String gender = intent.getStringExtra("EXTRA_GENDER_SEARCH");
        String programTags = intent.getStringExtra("EXTRA_PROGRAM_TAGS");
        String quality = intent.getStringExtra("EXTRA_QUALITY_TAGS");

        int qualityType = 0;
        if ("高品质".equals(quality)) {
            qualityType = 1;
        }

        doSearch(keywords, qualityType, 1, artist, null,
                null, category);
    }
}
