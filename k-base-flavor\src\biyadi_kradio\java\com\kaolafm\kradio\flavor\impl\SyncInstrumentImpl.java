package com.kaolafm.kradio.flavor.impl;

import android.content.Intent;
import android.hardware.bydauto.instrument.BYDAutoInstrumentDevice;
import android.util.Log;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.SyncInstrumentInter;
import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

public class SyncInstrumentImpl implements SyncInstrumentInter {
    private static final String TAG = "SyncInstrumentImpl";
    private BYDAutoInstrumentDevice mBYDAutoInstrumentDevice;
    public static boolean isFormRelease = false;
    public static boolean isFormShutDown = false;

    private void initPlayer() {
        PlayerManager playerManager = PlayerManager.getInstance();
        boolean isInitSuccess = playerManager.isPlayerInitSuccess();
        if (isInitSuccess) {
            try {
                playerManager.addPlayControlStateCallback(basePlayStateListener);
                mBYDAutoInstrumentDevice = BYDAutoInstrumentDevice.getInstance(AppDelegate.getInstance().getContext());
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            playerManager.addPlayerInitComplete(onPlayerInitCompleteListener);
        }
    }

    @Override
    public boolean initSyncInstrument() {
        //初始化注册监听接口
        isFormRelease = false;
        initPlayer();
        PlayerManager.getInstance().addAudioFocusListener(onAudioFocusChangeInter);
        return true;
    }

    @Override
    public boolean releaseSyncInstrument() {
        //反注册监听接口
        if (mBYDAutoInstrumentDevice == null) {
            mBYDAutoInstrumentDevice = BYDAutoInstrumentDevice.getInstance(AppDelegate.getInstance().getContext());
            if (mBYDAutoInstrumentDevice != null) {
                Log.i(TAG, "releaseSyncInstrument: BYDAutoInstrumentDevice.MUSIC_STOP");
                isFormRelease = true;
                mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_STOP);
            }
        } else {
            Log.i(TAG, "releaseSyncInstrument: BYDAutoInstrumentDevice.MUSIC_STOP");
            isFormRelease = true;
            mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_STOP);
        }
        Log.i(TAG, "releaseSyncInstrument: isFormRelease = " + isFormRelease);

        PlayerManager.getInstance().removePlayControlStateCallback(basePlayStateListener);
        PlayerManager.getInstance().removeAudioFocusListener(onAudioFocusChangeInter);
        return true;
    }

    OnAudioFocusChangeInter onAudioFocusChangeInter = new OnAudioFocusChangeInter() {
        @Override
        public void onAudioFocusChange(int i) {
            Log.i(TAG, "onAudioFocusChange: AudioFocus = " + i);
            if (i < 0) {
                try {
                    Log.i(TAG, "onAudioFocusChange: BYDAutoInstrumentDevice.MUSIC_STOP");
                    mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_STOP);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }else {
                try {
                    Log.i(TAG, "onAudioFocusChange: BYDAutoInstrumentDevice.MUSIC_PAUSE");
                    mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_PAUSE);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    };

    private IPlayerInitCompleteListener onPlayerInitCompleteListener = new IPlayerInitCompleteListener() {
        @Override
        public void onPlayerInitComplete(boolean b) {
            try {
                PlayerManager.getInstance().removePlayerInitComplete(onPlayerInitCompleteListener);
                PlayerManager.getInstance().addPlayControlStateCallback(basePlayStateListener);
                mBYDAutoInstrumentDevice = BYDAutoInstrumentDevice.getInstance(AppDelegate.getInstance().getContext());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    };


    private BasePlayStateListener basePlayStateListener = new BasePlayStateListener() {

        @Override
        public void onPlayerPreparing(PlayItem playItem) {
            try {
                Log.i(TAG, "onPlayerPreparing: playItem.getTitle() = " + playItem.getTitle());
                mBYDAutoInstrumentDevice.sendMusicName(playItem.getTitle());
                mBYDAutoInstrumentDevice.sendMusicPlaybackProgress(0);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onPlayerPlaying(PlayItem playItem) {
            try {
                Log.i(TAG, "onPlayerPlaying: playItem.getTitle() = " + playItem.getTitle());
                Log.i(TAG, "onPlayerPlaying: BYDAutoInstrumentDevice.MUSIC_PLAY ");
                mBYDAutoInstrumentDevice.sendMusicName(playItem.getTitle());
                mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_PLAY);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onPlayerPaused(PlayItem playItem) {
            try {
                if (PlayerManager.getInstance().getCurrentAudioFocusStatus() < 0) {
                    Log.i(TAG, "onPlayerPaused: isFormRelease = " + isFormRelease);
                    if (!isFormRelease) {
                        Log.i(TAG, "onPlayerPaused: BYDAutoInstrumentDevice.MUSIC_STOP ");
                        mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_STOP);
                        return;
                    }
                    isFormRelease = false;
                } else {
                    if (isFormShutDown) {
                        Log.i(TAG, "onPlayerPaused: BYDAutoInstrumentDevice.MUSIC_STOP ");
                        mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_STOP);
                        isFormShutDown = false;
                    } else {
                        Log.i(TAG, "onPlayerPaused: BYDAutoInstrumentDevice.MUSIC_PAUSE ");
                        mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_PAUSE);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onProgress(PlayItem playItem, long progress, long l) {
            try {
                Log.i(TAG, "onProgress: " + progress / 1000);
                mBYDAutoInstrumentDevice.sendMusicPlaybackProgress((int)progress/ 1000);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onPlayerFailed(PlayItem playItem, int i, int i1) {
            try {
                Log.i(TAG, "onPlayerFailed: BYDAutoInstrumentDevice.MUSIC_PAUSE");
                mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_PAUSE);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onPlayerEnd(PlayItem playItem) {
            try {
                Log.i(TAG, "onPlayerEnd: BYDAutoInstrumentDevice.MUSIC_PAUSE");
                mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_PAUSE);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    };
}