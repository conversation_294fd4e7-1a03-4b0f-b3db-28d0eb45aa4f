<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:paddingBottom="@dimen/y100"
    tools:context="com.kaolafm.kradio.online.mine.page.OnlineUserFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/y34">

            <ImageView
                android:id="@+id/user_oiv_bg"
                android:layout_width="@dimen/m83"
                android:layout_height="@dimen/m83"
                android:layout_centerInParent="true"
                android:src="@drawable/online_user_pic_bg"
                android:visibility="gone"
                tools:visibility="visible" />

            <com.kaolafm.kradio.component.ui.base.view.OvalImageView
                android:id="@+id/user_oiv"
                android:layout_width="@dimen/m78"
                android:layout_height="@dimen/m78"
                android:layout_centerInParent="true"
                android:padding="@dimen/m3"
                android:src="@drawable/online_user_no_login_icon"
                app:circle="true" />

            <ImageView
                android:id="@+id/online_main_user_vip_iv"
                android:layout_width="@dimen/m68"
                android:layout_height="@dimen/m25"
                android:layout_below="@+id/user_oiv"
                android:layout_centerHorizontal="true"
                android:layout_marginBottom="@dimen/y10"
                android:src="@drawable/online_user_vip_icon"
                android:visibility="gone"
                tools:visibility="visible" />
        </RelativeLayout>

        <LinearLayout
            android:id="@+id/user_dateil_ll"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/y19"
            android:orientation="vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="@dimen/x80"
                    android:layout_height="wrap_content"
                    android:gravity="right"
                    android:text="@string/mine_user_name_text"
                    android:textColor="@color/user_info_key_color"
                    android:textSize="@dimen/m24" />

                <TextView
                    android:id="@+id/mine_user_name_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/m20"
                    android:ellipsize="end"
                    android:maxLines="2"
                    android:textColor="@color/user_info_value_color"
                    android:textSize="@dimen/m24"
                    tools:text="123" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/y24"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="@dimen/x80"
                    android:layout_height="wrap_content"
                    android:gravity="right"
                    android:text="@string/mine_user_gender_text"
                    android:textColor="@color/user_info_key_color"
                    android:textSize="@dimen/m24" />

                <TextView
                    android:id="@+id/mine_user_gender_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/m20"
                    android:textColor="@color/user_info_value_color"
                    android:textSize="@dimen/m24"
                    tools:text="123" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/y24"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="@dimen/x80"
                    android:layout_height="wrap_content"
                    android:gravity="right"
                    android:text="@string/mine_user_area_text"
                    android:textColor="@color/user_info_key_color"
                    android:textSize="@dimen/m24" />

                <TextView
                    android:id="@+id/mine_user_area_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/m20"
                    android:textColor="@color/user_info_value_color"
                    android:textSize="@dimen/m24"
                    tools:text="123" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:visibility="gone">

                <TextView
                    android:layout_width="@dimen/x80"
                    android:layout_height="wrap_content"
                    android:gravity="right"
                    android:text="@string/mine_user_car_type_text"
                    android:textColor="@color/colorWhite_60"
                    android:textSize="@dimen/m24" />

                <TextView
                    android:id="@+id/mine_user_car_type_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/m20"
                    android:textColor="@color/colorWhite"
                    android:textSize="@dimen/m24"
                    tools:text="123" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:visibility="gone">

                <TextView
                    android:layout_width="@dimen/x80"
                    android:layout_height="wrap_content"
                    android:gravity="right"
                    android:text="@string/mine_user_car_code_text"
                    android:textColor="@color/colorWhite_60"
                    android:textSize="@dimen/m24" />

                <TextView
                    android:id="@+id/mine_user_car_code_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/m20"
                    android:textColor="@color/colorWhite"
                    android:textSize="@dimen/m24"
                    tools:text="123" />
            </LinearLayout>
        </LinearLayout>

        <TextView
            android:id="@+id/user_login_tv"
            android:layout_width="@dimen/m240"
            android:layout_height="@dimen/m64"
            android:layout_marginTop="@dimen/y51"
            android:background="@drawable/user_not_login_tv_bg"
            android:gravity="center"
            android:text="@string/mine_user_not_login_tv_text"
            android:textColor="@color/user_info_login_btn_color"
            android:textSize="@dimen/m24" />
    </LinearLayout>

</RelativeLayout>