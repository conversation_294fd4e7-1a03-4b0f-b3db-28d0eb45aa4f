package com.kaolafm.kradio.message.comprehensive;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.GradientDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.bumptech.glide.Glide;
import com.kaolafm.kradio.common.comprehensive.event.ChangeBadgeViewEvent;
import com.kaolafm.kradio.common.utils.CommonUtils;
import com.kaolafm.kradio.component.ui.base.view.OvalImageView;
import com.kaolafm.kradio.home.utils.AppDateUtils;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.basedb.manager.MessageDaoManager;
import com.kaolafm.kradio.lib.bean.CrashMessageBean;
import com.kaolafm.kradio.lib.report.ReportUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.message.utils.OnlineMessageUtils;
import com.kaolafm.opensdk.api.CrashMessageBaseBean;
import com.kaolafm.opensdk.crash.CrashPlayerHelper;
import com.kaolafm.opensdk.crash.Icrashstate;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.event.DialogExposureEvent;
import com.kaolafm.report.event.PageShowReportEvent;
import com.kaolafm.report.util.ReportConstants;
import com.kaolafm.report.util.ReportParameterManager;

import org.greenrobot.eventbus.EventBus;

import java.util.BitSet;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

/**
 * 消息泡泡
 * 调用示例：
 * MessageBubbleDialogFragment dialogFragment = (MessageBubbleDialogFragment) new Dialogs.Builder().setType(Dialogs.TYPE_MESSAGE_BUBBLE).create();
 * dialogFragment.setBubbleType(MessageBubbleDialogFragment.TYPE_DANGER)
 * .setSceneBitmap(BitmapFactory.decodeResource(getResources(), R.drawable.online_message_bubble_thunderbolt))
 * .setTitle("国家应急广播")
 * .setSubTitle("石家庄市发布暴雨雷电红色预警")
 * .setButtons(new ArrayList<MessageBubbleDialogFragment.MessageBubbleButton>() {{
 * add(new MessageBubbleDialogFragment.MessageBubbleButton("查看详情", R.id.online_message_bubble_button_1_id));
 * add(new MessageBubbleDialogFragment.MessageBubbleButton("倒计时", 20L, R.id.online_message_bubble_button_2_id));
 * add(new MessageBubbleDialogFragment.MessageBubbleButton("删除", R.drawable.online_search_icon_delete, R.id.online_message_bubble_button_3_id));
 * add(new MessageBubbleDialogFragment.MessageBubbleButton("确认", R.drawable.online_user_no_login_icon, 15, R.id.online_message_bubble_button_4_id));
 * }}, new View.OnClickListener() {
 *
 * @Override public void onClick(View v) {
 * int id = v.getId();
 * if (id == R.id.online_message_bubble_button_1_id) {
 * Log.e(TAG, "点击按钮：1");
 * } else if (id == R.id.online_message_bubble_button_2_id) {
 * Log.e(TAG, "点击按钮：2");
 * } else if (id == R.id.online_message_bubble_button_3_id) {
 * Log.e(TAG, "点击按钮：3");
 * } else if (id == R.id.online_message_bubble_button_4_id) {
 * Log.e(TAG, "点击按钮：4");
 * }
 * }
 * }).show(getSupportFragmentManager(), "MessageBubbleDialogFragment");
 */
public class MessageBubbleDialogFragment extends Dialog {
    private TextView bubble_replay;
    private View loading;
    private TextView timerTv;

    private OnShowListener mOnShowListener;
    private OnDismissListener mOnDismissListener;
    private OnButtonClickListener mViewClickListener;

    private CrashMessageBean crashMessageBean;

    private int closeTime = 8;
    protected long startTime = -1;
    private boolean isShowDialogBg;//是否显示dialog半透明背景
    private boolean isCloseTime = false;//是否读秒结束

    private Context mContext;

    private Handler handler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            if (closeTime > 0) {
                if (timerTv != null) {
                    timerTv.setVisibility(View.VISIBLE);
                    timerTv.setText(String.format(ResUtil.getString(R.string.message_bubble_timer), closeTime));
                }

                closeTime--;
                handler.sendEmptyMessageDelayed(1, 1000);
            } else {
                isCloseTime = true;
                dismiss();
            }
        }
    };
    private float dimAmount = 0f;
    private String lastPageId = ""; //之前的pageId

    public MessageBubbleDialogFragment(@NonNull Context context, int themeResId) {
        super(context, themeResId);
        this.mContext = context;
    }

    public MessageBubbleDialogFragment(@NonNull Context context) {
        super(context, R.style.FullScreenDialogTheme);
        this.mContext = context;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.layout_message_bubble);

        handleCardInsideOutside();

        handleCardBg();

        handleIcon();

        handleTitles();

        handleMsgPic();

        handleWindow();

        handleButtons();

        handlePlay();
    }

    private void handleCardBg(){
        View card_bg_iv = findViewById(R.id.card_bg_iv);
        try {
            GradientDrawable gradientDrawable = (GradientDrawable) card_bg_iv.getBackground();
            gradientDrawable.setCornerRadius(ResUtil.getDimen(R.dimen.m16));
            card_bg_iv.setBackground(gradientDrawable);
        } catch (Exception e) {
        }

        OvalImageView dialog_right_half_bg = findViewById(R.id.dialog_right_half_bg);
        dialog_right_half_bg.setOnClickListener(v-> handleGoDetail());
        if( !StringUtil.isEmpty(crashMessageBean.getCardBgUrl())){
            ImageLoader.getInstance().displayImage(getContext(), crashMessageBean.getCardBgUrl(), dialog_right_half_bg);
        }
    }

    private void handleIcon(){
        ImageView bubbleIcon = findViewById(R.id.bubbleIcon);

        bubbleIcon.setOnClickListener(v-> handleGoDetail());

        CrashMessageBean bean = this.crashMessageBean;

        if( !StringUtil.isEmpty(crashMessageBean.getMsgIconUrl())){
            ImageLoader.getInstance().displayImage(getContext(), crashMessageBean.getMsgIconUrl(), bubbleIcon);
        } else {
            if(MessageDialogHelper.isEbPushMessage(bean)){ //紧急消息
                bubbleIcon.setImageResource(R.drawable.message_red_iconr);
            } else {
                bubbleIcon.setImageResource(R.drawable.media_default_pic);
            }
        }
    }
    private void handleTitles(){
        TextView bubbleTitle = findViewById(R.id.bubbleTitle);
        TextView bubbleSubTitle = findViewById(R.id.bubbleSubTitle);
        bubbleTitle.setOnClickListener(v->handleGoDetail());
        bubbleSubTitle.setOnClickListener(v-> handleGoDetail());

        bubbleTitle.setText(crashMessageBean.getTipsTitle());
        bubbleSubTitle.setMaxLines(MessageDialogHelper.getSubTitleMaxLines(crashMessageBean));
        bubbleSubTitle.setText(crashMessageBean.getEventDescriptionExtract());
    }

    private void handleMsgPic(){
        ImageView msg_tips_pic_iv = findViewById(R.id.msg_tips_pic_iv);

        if ( !StringUtil.isEmpty(crashMessageBean.getMsgPicUrl()) ) {
            //应急广播要显示标题下边的图片
            msg_tips_pic_iv.setVisibility(View.VISIBLE);
//            ImageLoader.getInstance().displayImage(getContext(), crashMessageBean.getMsgPicUrl(), msg_tips_pic_iv);
            Glide.with(getContext())
                    .asBitmap()
                    .load(crashMessageBean.getMsgPicUrl())
//                    .placeholder(R.drawable.media_default_pic) // 不可设置此选项，否则会导致 ImageView 设置 android:adjustViewBounds="true" 后，图片显示很模糊
                    .error(R.drawable.media_default_pic)
                    .into(msg_tips_pic_iv);
        } else {
            if (MessageDialogHelper.isEbPushMessage(this.crashMessageBean)) {
                //应急广播要显示标题下边的图片
                msg_tips_pic_iv.setVisibility(View.VISIBLE);
                msg_tips_pic_iv.setImageDrawable(ResUtil.getDrawable(R.drawable.msg_tips_pic));
            }
        }
    }

    private void enableReplayButton(){
        if(bubble_replay != null){
            bubble_replay.setEnabled(true);
        }
    }

    private void disableReplayButton(){
        if(bubble_replay != null){
            bubble_replay.setEnabled(false);
        }
    }
    private void startDismissCountDown(){
        if (handler != null)
            handler.sendEmptyMessage(1);
    }

    private void removeDismissCountdown(){
        if(handler != null){
            handler.removeMessages(1);
        }

        timerTv.setVisibility(View.GONE);
        closeTime = 8;
    }

    private void obFirstTimePlayEnd(){
        CrashPlayerHelper.getInstance().setIcrashstate(new Icrashstate() {
            @Override
            public void onCrashstate(int i) {
                if (i == 0) {   //播放完成
                    Log.d("DismissCountDown", "obFirstTimePlayEnd");
                    onPlayEnd();
                }
            }

            @Override
            public void onBufferingUpdate(CrashMessageBaseBean crashMessageBaseBean) {
            }

            @Override
            public void onPlayerFailed(PlayerFailedType playerFailedType) {

            }
        });
    }

    private void onPlayEnd(){
        //播放完成
        OnlineMessageUtils.getInstance().setShowMsgDetails(false);
        Log.d("DismissCountDown", "onPlayEnd");
        startDismissCountDown();
        //播放完了，重播按钮可用
        enableReplayButton();
        bubble_replay.setText(R.string.message_bubble_replay);
    }

    private void handlePlay(){
        loading = findViewById(R.id.loading);
        bubble_replay = findViewById(R.id.bubble_replay);

        TextView cdClose = findViewById(R.id.cd_close);
        cdClose.setOnClickListener(v->{
            cdClose.postDelayed(() -> {
                dismiss();
                CrashPlayerHelper.getInstance().playEnd();
            }, 1000);
        });

        bubble_replay.setOnClickListener((v)->{

            if(crashMessageBean == null){
                return;
            }

            removeDismissCountdown();

            //开始播放后，重播按钮不可用
            disableReplayButton();

            loading.setVisibility(View.VISIBLE);

            CrashPlayerHelper.getInstance().setIcrashstate(new Icrashstate() {
                @Override
                public void onCrashstate(int i) {
                    if (i == 0) {   //播放完成
                        Log.d("DismissCountDown", "bubble_replay");
                        onPlayEnd();
                    }
                }

                @Override
                public void onBufferingUpdate(CrashMessageBaseBean crashMessageBaseBean) {
                    //缓冲完成
                    OnlineMessageUtils.getInstance().setShowMsgDetails(true);
                    loading.setVisibility(View.GONE);
                }

                @Override
                public void onPlayerFailed(PlayerFailedType playerFailedType) {

                }
            });
            CrashPlayerHelper.getInstance().addImmediatelyplayDate(AppDateUtils.getInstance().changeDate(crashMessageBean)).startPlay();

        });

        String playType = crashMessageBean.getPlayType();

        if( playType != null &&  playType.equals("2")){ //不自动播放的，启动倒计时
            Log.d("DismissCountDown", "handlePlay");
            startDismissCountDown();

            bubble_replay.setText(R.string.message_bubble_play);
        } else { //自动播放的
            bubble_replay.setText(R.string.message_bubble_replay);
            if(!StringUtil.isEmpty(crashMessageBean.getEventDescriptionPath())){    //且有音频就disableReplayButton，如果无音频，则在handleButton里View。GONE了
                disableReplayButton();
            }
            obFirstTimePlayEnd();
        }
    }
    private void handleWindow(){
        Window window = this.getWindow();
        //设置弹出位置
//        window.setGravity(Gravity.BOTTOM | Gravity.START);

        int matchParent = ViewGroup.LayoutParams.MATCH_PARENT;//父布局的宽度

//        Window dialogWindow = this.getWindow();
//        dialogWindow.setGravity(Gravity.TOP | Gravity.RIGHT);
        WindowManager.LayoutParams lp = window.getAttributes();
        lp.width = matchParent;
        lp.height = matchParent;
//        lp.x = matchParent;
//        lp.y = 300;  //设置出现的高度，距离顶部
        window.setAttributes(lp);
        //去除系统自带的margin
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        //背景全透明
        window.setDimAmount(0f);
        //设置弹出动画
        window.setWindowAnimations(R.style.MessageBubbleAnimation);
        //设置对话框大小
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        CommonUtils.getInstance().initGreyStyle(window);
    }

    private void handleGoDetail(){
        if (crashMessageBean == null) {
            return;
        }
        dismiss();
        MessageDetailsDialogFragment dialogFragment = new MessageDetailsDialogFragment(getContext());
        dialogFragment.setCrashMessageBean(crashMessageBean).show();
        //上报点击
        ReportUtil.addMessageClike(getPageId(), crashMessageBean.getMsgContentType(), crashMessageBean.getMsgId());
    }
    private void handleCardInsideOutside(){
        ConstraintLayout root_view = findViewById(R.id.root_view);

        if (isShowDialogBg) {
            root_view.setBackgroundColor(ResUtil.getColor(R.color.message_details_bg));
        } else {
            root_view.setBackgroundColor(Color.TRANSPARENT);
        }
        root_view.setOnClickListener(v -> {
            if (CrashPlayerHelper.getInstance().isPlay()) {
                CrashPlayerHelper.getInstance().playEnd();
            }
            dismiss();
        });
        findViewById(R.id.card_bg_iv).setOnClickListener(v -> handleGoDetail());
    }

    public MessageBubbleDialogFragment showDialogBg(boolean isShowDialogBg) {
        this.isShowDialogBg = isShowDialogBg;
        return this;
    }

    public CrashMessageBean getCrashMessageBean() {
        return crashMessageBean;
    }

    @Override
    public void show() {
        Log.d("messageUI", "dialogFragment show");
        super.show();

        handleMsgLooked();

        //上报展示
        ReportUtil.addMessageShow(getPageId(), crashMessageBean.getMsgContentType(), crashMessageBean.getMsgId());
        //重置pageId
        this.lastPageId = ReportParameterManager.getInstance().getPage();
        ReportHelper.getInstance().setPage(getPageId());

        if (mOnShowListener != null) {
            mOnShowListener.onShow();
        }

    }

    private Disposable handleMsgLookedJob;
    private void handleMsgLooked(){
        cancelHandleMsgLookedJob();
        handleMsgLookedJob = Observable.just(1)
                .subscribeOn(Schedulers.io())
                .doOnNext(it->{
                    MessageDaoManager.getInstance().updateLookSync(crashMessageBean.getMsgId());
                })
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(it -> EventBus.getDefault().post(new ChangeBadgeViewEvent()), throwable -> { });
    }
    private void cancelHandleMsgLookedJob(){
        if(handleMsgLookedJob != null && !handleMsgLookedJob.isDisposed()){
            handleMsgLookedJob.dispose();
        }
    }
    @Override
    public void dismiss() {
        Log.d("messageUI", "dialogFragment dismiss");
        if (handler != null) {
            handler.removeMessages(1);
            handler = null;
        }

        cancelHandleMsgLookedJob();

        if (mContext instanceof Activity) {
            Activity activity = (Activity) mContext;
            if (!activity.isDestroyed() && !activity.isFinishing()) {
                super.dismiss();
            }
        } else {
            super.dismiss();
        }
        reportPageShowEvent();

        //还原pageId
        Log.i(ReportConstants.REPORT_TAG, "设置pageid= " + lastPageId);
        ReportHelper.getInstance().setPage(this.lastPageId);
        if (mOnDismissListener != null) {
            mOnDismissListener.onDisMiss();
        }
    }

    @Override
    public void hide() {
        super.hide();
        //还原pageId
        Log.i(ReportConstants.REPORT_TAG, "设置pageid= " + lastPageId);
        ReportHelper.getInstance().setPage(this.lastPageId);
    }

    public MessageBubbleDialogFragment setMDimAmount(float dimAmount) {
        this.dimAmount = dimAmount;
        return this;
    }

    public MessageBubbleDialogFragment setCrashMessageBean(CrashMessageBean messageBean) {
        this.crashMessageBean = messageBean;
        return this;
    }
    /**
     * 每行最多17个字符，最多显示两行，长于34个字符则用“...”省略
     *
     * @param subTitle
     * @return
     */
    private String getMaxString(String subTitle) {
        if (StringUtil.isEmpty(subTitle)) return null;
        if (subTitle.length() <= 17) return subTitle;
        StringBuilder newSubTitle = new StringBuilder();
        newSubTitle.append(subTitle.substring(0, 17)).append("\n");
        if (subTitle.length() > 34) {
            newSubTitle.append(subTitle.substring(17, 33)).append("...");
        } else {
            newSubTitle.append(subTitle.substring(17));
        }
        return newSubTitle.toString();
    }

    public MessageBubbleDialogFragment setButtonsListener(OnButtonClickListener onClickListener) {
        this.mViewClickListener = onClickListener;
        return this;
    }

    private BitSet getButtonStatus(){
        BitSet bs = new BitSet(3);

        CrashMessageBean bean = this.crashMessageBean;

        if( !StringUtil.isEmpty(bean.getMsgBubbleBtnTextLeft()) ){
            bs.set(2);
        }
        if( !StringUtil.isEmpty(crashMessageBean.getEventDescriptionPath())) { //有音频
            bs.set(1);
        }

        if( !StringUtil.isEmpty(bean.getMsgBubbleBtnTextRight()) ){
            bs.set(0);
        }
        return bs;
    }

    private void  handleButtons() {
        TextView leftButton = findViewById(R.id.message_bubble_button_1_id);
        LinearLayout rightButton = findViewById(R.id.message_bubble_button_2_id);
        TextView rightButtonTv = findViewById(R.id.messageBubbleTextTv);
        timerTv = findViewById(R.id.messageBubbleTimerTv);

        View col1 = findViewById(R.id.col1);
        View col2 = findViewById(R.id.col2);
        View col3 = findViewById(R.id.col3);
        View col4 = findViewById(R.id.col4);
        View col5 = findViewById(R.id.col5);
        View col6 = findViewById(R.id.col6);
        View col7 = findViewById(R.id.col7);

        CrashMessageBean bean = this.crashMessageBean;

        if( !StringUtil.isEmpty(bean.getMsgBubbleBtnTextLeft())){
            leftButton.setText(bean.getMsgBubbleBtnTextLeft());
        }

        if( !StringUtil.isEmpty(bean.getMsgBubbleBtnTextRight())){
            rightButtonTv.setText(bean.getMsgBubbleBtnTextRight());
        }

        if(this.mViewClickListener != null){
            leftButton.setOnClickListener(v -> mViewClickListener.onButtonClick(this, v));
            rightButton.setOnClickListener(v -> mViewClickListener.onButtonClick(this, v));
        }

        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_MESSAGE_BUBBLE_SEE_DETAILS
                , bean.getMsgDetailsBtnTextLeft(), Constants.PAGE_ID_MESSAGE_CARD, ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_MESSAGE_BUBBLE));
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_MESSAGE_BUBBLE_CLOSE, bean.getMsgDetailsBtnTextRight(), Constants.PAGE_ID_MESSAGE_CARD, ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_MESSAGE_BUBBLE));

        if(MessageDialogHelper.isEbPushMessage(bean)){ //紧急消息
            //展示所有按钮
            return;
        }

        BitSet bs = getButtonStatus();
        if(bs.get(2)){
            col1.setVisibility(View.VISIBLE);
            col2.setVisibility(View.VISIBLE);
        } else {
            col1.setVisibility(View.GONE);
            col2.setVisibility(View.GONE);
        }

        if(bs.get(1)){
            col3.setVisibility(View.VISIBLE);
        } else {
            col3.setVisibility(View.GONE);
        }

        if(bs.get(0)){
            col4.setVisibility(View.VISIBLE);
            col5.setVisibility(View.VISIBLE);
        } else {
            col4.setVisibility(View.GONE);
            col5.setVisibility(View.GONE);
        }

        if(bs.get(1)){  //有音频，显示重播按钮

            if( !bs.get(2) && !bs.get(0) ){ // 左右按钮都无，重播按钮靠左
                col4.setVisibility(View.INVISIBLE);
                col5.setVisibility(View.INVISIBLE);
            }

        } else {    //没有音频，隐藏重播按钮

            if( bs.get(2) && !bs.get(0)){   //仅有左按钮，靠左，右边占位
                col3.setVisibility(View.INVISIBLE);
            }

            if( !bs.get(2) && bs.get(0)){   //仅有右按钮，也靠左，有边占位
                col3.setVisibility(View.GONE);
                col4.setVisibility(View.GONE);
                col6.setVisibility(View.INVISIBLE);
                col7.setVisibility(View.INVISIBLE);
            }
        }

//        if( !StringUtil.isEmpty(crashMessageBean.getEventDescriptionPath())) { //有音频，显示重播按钮
//            col3.setVisibility(View.VISIBLE);
//
//            if ( !StringUtil.isEmpty(bean.getMsgBubbleBtnTextLeft()) ) {    //有左按钮
//                col1.setVisibility(View.VISIBLE);
//                col2.setVisibility(View.VISIBLE);
//            } else {
//                col1.setVisibility(View.GONE);
//                col2.setVisibility(View.GONE);
//            }
//            if( !StringUtil.isEmpty(bean.getMsgBubbleBtnTextRight()) ){
//                col4.setVisibility(View.VISIBLE);
//                col5.setVisibility(View.VISIBLE);
//            } else {
//                col4.setVisibility(View.GONE);
//                col5.setVisibility(View.GONE);
//            }
//
//            if(StringUtil.isEmpty(bean.getMsgBubbleBtnTextLeft()) && StringUtil.isEmpty(bean.getMsgBubbleBtnTextRight())){  // 左右按钮都无，重播按钮靠做
//                col4.setVisibility(View.INVISIBLE);
//                col5.setVisibility(View.INVISIBLE);
//            }
//
//        } else {    //没有音频，隐藏重播按钮
//            col3.setVisibility(View.GONE);
//
//            if ( !StringUtil.isEmpty(bean.getMsgBubbleBtnTextLeft()) ) {    //有左按钮
//                col1.setVisibility(View.VISIBLE);
//                col2.setVisibility(View.VISIBLE);
//            } else {
//                col1.setVisibility(View.GONE);
//                col2.setVisibility(View.GONE);
//            }
//
//            if ( !StringUtil.isEmpty(bean.getMsgBubbleBtnTextRight()) ) {    //有右按钮
//                col4.setVisibility(View.VISIBLE);
//                col5.setVisibility(View.VISIBLE);
//            } else {
//                col4.setVisibility(View.GONE);
//                col5.setVisibility(View.GONE);
//            }
//        }
    }

    public MessageBubbleDialogFragment setOnShowListener(OnShowListener onShowListener) {
        mOnShowListener = onShowListener;
        return this;
    }

    public interface OnShowListener {
        void onShow();
    }

    public MessageBubbleDialogFragment setOnDisMissListener(OnDismissListener onDismissListener) {
        mOnDismissListener = onDismissListener;
        return this;
    }

    public interface OnButtonClickListener {
        void onButtonClick(MessageBubbleDialogFragment fragment, View btn);
    }

    public interface OnDismissListener {
        void onDisMiss();
    }


    public String getPageId() {
        return Constants.PAGE_ID_MESSAGE_CARD;
    }

    @Override
    protected void onStart() {
        super.onStart();
//        String pageId = getPageId();
//        if (!StringUtil.isEmpty(pageId)) {
//            Log.i(ReportConstants.REPORT_TAG, "设置pageid= " + pageId);
//            ReportHelper.getInstance().setPage(pageId);
//        }

        startTime = System.currentTimeMillis();
    }

    /**
     * 页面曝光事件上报
     */
    private void reportPageShowEvent() {
        long duration = System.currentTimeMillis() - startTime;
        if (startTime < 0 || duration < 300) {
            return;
        }

        PageShowReportEvent event = new PageShowReportEvent();
        event.setPage(getPageId());
        event.setPageId(getPageId());
        event.setPageTime(String.valueOf(duration));
        ReportHelper.getInstance().addEvent(event);

        DialogExposureEvent reportEventBean = new DialogExposureEvent(ReportConstants.DIALOG_ID_MESSAGE_BUBBLE, lastPageId, duration, null);
        reportEventBean.setPage(lastPageId);
        ReportHelper.getInstance().addEvent(reportEventBean);
        Log.i("BaseDialogFragment", "report=" + duration);
        startTime = -1;
    }
}