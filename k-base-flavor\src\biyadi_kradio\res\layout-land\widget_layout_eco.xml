<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="239dp"
    android:layout_height="268dp"
    >


    <RelativeLayout
        android:id="@+id/widget_playinfo_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/widget_play_operation_layout"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/widget_blur_imageview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/widget_default_bg_land"
            />

        <ImageView
            android:id="@+id/widget_blur_bg"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone"
            android:background="@drawable/shape_biyadi_widget_blur_bg"
            />

        <ImageView
            android:id="@+id/widget_kaolafm_log"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/widget_logo_land"
            android:layout_marginTop="@dimen/m14"
            android:layout_marginLeft="@dimen/m17"
            />


        <FrameLayout
            android:id="@+id/widget_audiocover_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/m19"
            android:layout_marginBottom="@dimen/m19"
            android:layout_below="@+id/widget_kaolafm_log"
            android:layout_centerHorizontal="true"
            >

            <ImageView
                android:id="@+id/widget_cover"
                android:layout_width="90dp"
                android:layout_height="90dp"
                android:layout_marginTop="2dp"
                 />

            <ImageView
                android:id="@+id/widget_broadcast_label"
                android:layout_width="50dp"
                android:layout_height="17dp"
                android:visibility="gone"
                />

            <TextView
                android:id="@+id/widget_broadcast_label_textview"
                android:layout_width="55dp"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textSize="11sp"
                android:textColor="@color/colorWhite"
                android:visibility="gone"
                />

        </FrameLayout>

        <TextView
            android:id="@+id/widget_audio_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/widget_audiocover_layout"
            android:layout_centerHorizontal="true"
            android:ellipsize="end"
            android:lines="1"
            android:maxLines="1"
            android:paddingLeft="@dimen/m16"
            android:paddingRight="@dimen/m16"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/text_size5" />

        <TextView
            android:id="@+id/widget_album_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/widget_audio_name"
            android:layout_centerHorizontal="true"
            android:ellipsize="end"
            android:lines="1"
            android:maxLines="1"
            android:paddingLeft="@dimen/m16"
            android:paddingRight="@dimen/m16"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/text_size4" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_above="@+id/widget_progressBar"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="@dimen/m14"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/widget_cur_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/colorWhite"
                android:textSize="@dimen/text_size4" />

            <TextView
                android:id="@+id/widget_duration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/colorWhite"
                android:textSize="@dimen/text_size4" />

        </LinearLayout>

        <ProgressBar
            android:id="@+id/widget_progressBar"
            style="@style/Widget.AppCompat.ProgressBar.Horizontal"
            android:layout_width="match_parent"
            android:layout_height="@dimen/m4"
            android:layout_alignParentBottom="true"
            android:progressDrawable="@drawable/widget_seekbar_bar"
            android:scrollbarStyle="insideInset" />

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/widget_play_operation_layout"
        android:layout_width="match_parent"
        android:layout_height="49dp"
        android:layout_alignParentBottom="true"
        android:gravity="center"
        android:background="@drawable/shape_biyadi_eco_radius_bg"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/widget_prev"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:src="@drawable/selector_widget_btn_play_prev" />

        <ImageView
            android:id="@+id/widget_play_or_pause"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:src="@drawable/selector_widget_btn_pause" />

        <ImageView
            android:id="@+id/widget_next"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:src="@drawable/selector_widget_btn_play_next" />

        <ImageView
            android:id="@+id/widget_collection"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:src="@drawable/selector_widget_btn_uncollection" />
    </LinearLayout>

</RelativeLayout>