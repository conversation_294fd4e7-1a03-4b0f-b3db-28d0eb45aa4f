package com.kaolafm.kradio.huawei;

import com.huawei.carmediakit.session.ControllerCenter;
import com.huawei.carmediakit.session.ICarMediaUILifecycleObserver;
import com.huawei.carmediakit.session.ProviderCenter;
import com.kaolafm.kradio.huawei.controller.DialogController;
import com.kaolafm.kradio.huawei.controller.MediaContentController;
import com.kaolafm.kradio.huawei.controller.MediaPlayingController;
import com.kaolafm.kradio.huawei.controller.MediaSearchController;
import com.kaolafm.kradio.huawei.controller.UserLoginController;
import com.kaolafm.kradio.huawei.controller.UserSettingsController;
import com.kaolafm.kradio.huawei.provider.DialogProvider;
import com.kaolafm.kradio.huawei.provider.MediaDataProvider;
import com.kaolafm.kradio.huawei.provider.MediaPlayingProvider;
import com.kaolafm.kradio.huawei.provider.MediaSearchProvider;
import com.kaolafm.kradio.huawei.provider.UserLoginProvider;
import com.kaolafm.kradio.huawei.provider.UserSettingsProvider;

public class CarMediaUILifecycleObserver implements ICarMediaUILifecycleObserver {
    @Override
    public void onCarMediaUIStarted() {
        // 向 CarMediaKit 注册音频数据查询接口的实现类。
        ProviderCenter.getInstance().registerMediaDataProvider(new
                MediaDataProvider());
        // 向 CarMediaKit 注册音频播放相关的查询接口的实现类。
        ProviderCenter.getInstance().registerMediaPlayingProvider(new
                MediaPlayingProvider());
        // 向 CarMediaKit 注册内容搜索相关的接口的实现类。
        ProviderCenter.getInstance().registerMediaSearchProvider(new
                MediaSearchProvider());
        // 向 CarMediaKit 注册用户和设置相关查询接口的实现类。
        ProviderCenter.getInstance().registerUserSettingsProvider(new
                UserSettingsProvider());
        ProviderCenter.getInstance().registerUserLoginProvider(new
                UserLoginProvider());
        ProviderCenter.getInstance().registerDialogProvider(new
                DialogProvider());
        // 向 CarMediaKit 注册音频数据控制接口的实现类。
        ControllerCenter.getInstance().registerMediaContentController(new
                MediaContentController());
        // 向 CarMediaKit 注册音频播放控制接口的实现类。
        ControllerCenter.getInstance().registerMediaPlayingController(new
                MediaPlayingController());
        // 向 CarMediaKit 注册用户和设置相关的控制接口的实现类。
        ControllerCenter.getInstance().registerUserSettingsController(new
                UserSettingsController());
        // 向 CarMediaKit 注册音频内容搜索相关的控制接口的实现类。
        ControllerCenter.getInstance().registerMediaSearchController(new
                MediaSearchController());
        // 向 CarMediaKit 注册弹框控制接口的实现类。
        ControllerCenter.getInstance().registerDialogController(new
                DialogController());
        ControllerCenter.getInstance().registerUserLoginController(new
                UserLoginController());
        // 其他必要的工作。
        //doSomething();
    }

}
