package com.kaolafm.kradio.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;


import com.kaolafm.base.utils.NetworkMonitor;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.uitl.PlayerUtil;



import java.lang.ref.WeakReference;

public class BYDAutoPlayReceiver extends BroadcastReceiver {
    public static final String AUTO_PLAY_ACTION = "byd.intent.action.AUTO_PLAY";

    private BYDAutoPlayReceiver.MyOnNetworkStatusChangedListener myOnNetworkStatusChangedListener;
    private BYDAutoPlayReceiver.MyOnPlayerInitCompleteListener myOnPlayerInitCompleteListener;

    @Override
    public void onReceive(Context context, Intent intent) {
        if (myOnNetworkStatusChangedListener == null) {
            myOnNetworkStatusChangedListener = new BYDAutoPlayReceiver.MyOnNetworkStatusChangedListener(this);
        }
        if (myOnPlayerInitCompleteListener == null) {
            myOnPlayerInitCompleteListener = new BYDAutoPlayReceiver.MyOnPlayerInitCompleteListener();
        }
        if (AUTO_PLAY_ACTION.equals(intent.getAction())) {
            Log.i("zsj", "onReceive: AUTO_PLAY_ACTION = " + AUTO_PLAY_ACTION);
            String pkgName = intent.getStringExtra("pkgName");
            Log.i("zsj", "onReceive: pkgName = " + pkgName);
            //比亚迪需求根据系统广播执行播放
            if (context.getPackageName().equals(pkgName)) {
                Log.i("zsj", "onReceive: to play");
                if (NetworkUtil.isNetworkAvailable(context)) {
                    playInit();
                } else {
                    NetworkMonitor.getInstance(context).registerNetworkStatusChangeListener(myOnNetworkStatusChangedListener);
                }
            }
        }
    }


    private static class MyOnPlayerInitCompleteListener implements IPlayerInitCompleteListener {
        @Override
        public void onPlayerInitComplete(boolean b) {
            Log.i("zsj", "onPlayerInitComplete ");
            final PlayerManager playerManager = PlayerManager.getInstance(null);
            playerManager.removePlayerInitComplete(this);
            playAudio();
        }
    }


    private static class MyOnNetworkStatusChangedListener implements NetworkMonitor.OnNetworkStatusChangedListener {

        private WeakReference<BYDAutoPlayReceiver> weakReference;

        public MyOnNetworkStatusChangedListener(BYDAutoPlayReceiver autoPlayReceiver) {
            weakReference = new WeakReference<>(autoPlayReceiver);
        }

        @Override
        public void onStatusChanged(int i, int i1) {
            BYDAutoPlayReceiver autoPlayReceiver = weakReference.get();
            Log.i("zsj", "onStatusChanged ");
            if (autoPlayReceiver == null) {
                return;
            }
            NetworkMonitor.getInstance(AppDelegate.getInstance().getContext()).removeNetworkStatusChangeListener(this);
            if (i == NetworkMonitor.STATUS_MOBILE || i == NetworkMonitor.STATUS_WIFI) {
                autoPlayReceiver.playInit();
            }
        }
    }

    private void playInit() {
        Log.i("zsj", "playInit ");

        final PlayerManager playerManager = PlayerManager.getInstance();
        playerManager.init(AppDelegate.getInstance().getContext());
        playerManager.addPlayerInitComplete(myOnPlayerInitCompleteListener);
    }

    private static void playAudio() {
        Log.i("zsj", "playAudio ");
        //走首页的播放逻辑
        PlayerUtil.playNetOrLocal();
    }

}
