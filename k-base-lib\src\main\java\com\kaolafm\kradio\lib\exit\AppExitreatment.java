package com.kaolafm.kradio.lib.exit;

import android.app.Application;

import com.kaolafm.kradio.lib.init.AppInit;
import com.kaolafm.kradio.lib.init.AppInitManager;

/**
 * 实现该接口并添加注解{@link AppInit}的类会被自动添加到管理中{@link AppInitManager}根据其配置进行初始化相关接口的回调。
 * <AUTHOR>
 * @date 2019-09-10
 */
public interface AppExitreatment {

    /**
     * 会在Application的onExitApplication()方法中同步回调。
     * @param application
     */
    void onExit(Application application);

    /**
     * 在Application的onExitApplication()方法中开启线程异步回调，该方法只有在{@link AppExit#isAsync()}返回true的时候才会执行。
     * @param application
     */
    void asyncExit(Application application);

}
