package com.kaolafm.kradio.common.http.api.feedback;

import com.kaolafm.kradio.lib.bean.WxQrcode;
import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BaseResult;

import java.util.HashMap;

import io.reactivex.Single;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.QueryMap;

/**
 * <AUTHOR> on 2019-07-18.
 */

interface FeedbackApiService {

    /**
     * 负反馈
     * @param tempMap
     * @return
     */
    @Headers(FeedbackRequestConstant.DOMAIN_HEADER_MINUS_FEED_BACK)
    @GET(FeedbackRequestConstant.REQUEST_GET_MINUS_FEED_BACK)
    Single<BaseResult> getMinusFeedback(@QueryMap HashMap<String, String> tempMap);

    /**
     * 获取意见反馈的微信二维码
     * @return
     */
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(FeedbackRequestConstant.GET_FEEDBACK_WX_QRCODE)
    Single<BaseResult<WxQrcode>> getWechatQRCodeForFeedback();
}
