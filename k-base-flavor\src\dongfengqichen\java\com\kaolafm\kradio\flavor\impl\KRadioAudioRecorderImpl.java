package com.kaolafm.kradio.flavor.impl;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.hsae.hsaerecord.HsaeRecord;
import com.kaolafm.kradio.flavor.recorder.QichenRecorder;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioRecorderInter;
import com.kaolafm.kradio.lib.base.flavor.live.KradioRecorderInterface;
import com.kaolafm.kradio.live1.player.LiveManager;


/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-07-05 12:16
 ******************************************/
public final class KRadioAudioRecorderImpl implements KRadioAudioRecorderInter {

    @Override
    public boolean initVR(Object... args) {
        return false;
    }

    @Override
    public boolean onAudioRecordStart(Object... args) {
        Intent intent = new Intent(LiveManager.START_RECORD_REQUEST_ACTION);
        AppDelegate.getInstance().getContext().sendBroadcast(intent);
        return false;
    }

    @Override
    public boolean onAudioRecordStop(Object... args) {
        Intent intent = new Intent(LiveManager.STOP_RECORD_REQUEST_ACTION);
        AppDelegate.getInstance().getContext().sendBroadcast(intent);
        return false;
    }

    @Override
    public void setVrStatusListener(OnVRStatusListener onVRStatusListener) {

    }

    @Override
    public KradioRecorderInterface getRecorder() {
        return new QichenRecorder();
    }

}
