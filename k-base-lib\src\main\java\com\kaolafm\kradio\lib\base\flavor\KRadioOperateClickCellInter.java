package com.kaolafm.kradio.lib.base.flavor;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;

import java.util.List;

/**
 * @Package: com.kaolafm.kradio.lib.base.flavor
 * @Description: 用于运营位跳转链接开发
 * @Date: 10:54
 */
public interface KRadioOperateClickCellInter {
    String PAGEID = "pageid";
    String RADIOID = "radioid";
    String RADIOTYPE = "radiotype";

    //设置传输的参数 上层自己定义
    void setIntent(Intent intent);

    //获取传输的参数
    Uri getIntentUri(Intent intent);

    boolean startToLauncher();

    Intent getDataIntent();

    void setDataIntent(Intent intent);

    void toPlay();
    boolean isOperate(Intent intent);
}
