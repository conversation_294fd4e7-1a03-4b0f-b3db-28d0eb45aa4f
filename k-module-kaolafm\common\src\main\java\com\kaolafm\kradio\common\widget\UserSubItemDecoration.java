package com.kaolafm.kradio.common.widget;

import android.graphics.Rect;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;
import android.util.Log;
import android.view.View;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.ResUtil;

/**
 * <pre>
 *     <AUTHOR> Wenchl
 *     e-mail : <EMAIL>
 *     time   : 2019/03/06
 *     desc   :
 *     version: 1.0
 * </pre>
 */
public class UserSubItemDecoration extends RecyclerView.ItemDecoration {

    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
        outRect.bottom = ResUtil.getDimen(R.dimen.m24);
        final int spanCount = getSpanCount(parent);
        final int childCount = parent.getAdapter().getItemCount();
        final int adapterPosition = parent.getChildAdapterPosition(view);
        if (isLastColumn(adapterPosition, spanCount, childCount)) {
            outRect.left = ResUtil.getDimen(R.dimen.m25);
            outRect.right = 0;
        } else {
            outRect.right = ResUtil.getDimen(R.dimen.m25);
            outRect.left = 0;
        }
    }

    private boolean isLastColumn(int position, int spanCount, int childCount) {
        return (position + 1) % spanCount == 0;
    }

    private boolean isFirst(int position, int spanCount, int childCount) {
        Log.e("isFirstColumn", "position is " + position);
        Log.e("isFirstColumn", "spanCount is " + spanCount);
        Log.e("isFirstColumn", "childCount is " + childCount);
        return position < spanCount;
    }

//    private boolean isLast(int position, int spanCount,int childCount) {
//        Log.e("isFirstColumn", "position is "+position);
//        Log.e("isFirstColumn", "spanCount is "+spanCount);
//        Log.e("isFirstColumn", "childCount is "+childCount);
//        Log.e("isFirstColumn", "position % spanCount  is "+position % spanCount );
//        return childCount - 1 - position < spanCount;
//    }

    private int getSpanCount(RecyclerView parent) {
        RecyclerView.LayoutManager layoutManager = parent.getLayoutManager();

        if (layoutManager instanceof GridLayoutManager) {
            return ((GridLayoutManager) layoutManager).getSpanCount();
        } else if (layoutManager instanceof StaggeredGridLayoutManager) {
            return ((StaggeredGridLayoutManager) layoutManager).getSpanCount();
        } else {
            throw new UnsupportedOperationException("the GridDividerItemDecoration can only be used in " +
                    "the RecyclerView which use a GridLayoutManager or StaggeredGridLayoutManager");
        }
    }
}
