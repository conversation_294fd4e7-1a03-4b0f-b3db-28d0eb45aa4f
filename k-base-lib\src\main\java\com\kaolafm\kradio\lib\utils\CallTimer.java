package com.kaolafm.kradio.lib.utils;

/**
 * Created by yls on 2018/5/9.
 */

import android.os.Handler;
import android.os.SystemClock;
import android.util.Log;

import com.bumptech.glide.util.Preconditions;


/**
 * Helper class used to keep track of events requiring regular intervals.
 */
public class CallTimer extends Handler {
    private Runnable mInternalCallback;
    private Runnable mCallback;
    private long mLastReportedTime;
    private long mInterval;
    private boolean mRunning;

    private boolean isUpdateAtOnce = true;
    private boolean isFirst = true;

    public boolean isUpdateAtOnce() {
        return isUpdateAtOnce;
    }

    public void setUpdateAtOnce(boolean updateAtOnce) {
        isUpdateAtOnce = updateAtOnce;
    }

    public CallTimer(Runnable callback) {
        Preconditions.checkNotNull(callback);

        mInterval = 0;
        mLastReportedTime = 0;
        mRunning = false;
        mCallback = callback;
        mInternalCallback = new CallTimerCallback();
    }

    public boolean start(long interval) {
        if (interval <= 0) {
            return false;
        }

        // cancel any previous timer
        cancel();

        mInterval = interval;
        mLastReportedTime = SystemClock.uptimeMillis();

        mRunning = true;
        periodicUpdateTimer();

        return true;
    }

    public void cancel() {
        removeCallbacks(mInternalCallback);
        mRunning = false;
        isFirst = true;
    }

    private void periodicUpdateTimer() {
        if (!mRunning) {
            return;
        }

        final long now = SystemClock.uptimeMillis();
        long nextReport = mLastReportedTime + mInterval;
        while (now >= nextReport) {
            nextReport += mInterval;
        }

        postAtTime(mInternalCallback, nextReport);
        mLastReportedTime = nextReport;

        // Run the callback
        if(isFirst && !isUpdateAtOnce){
            isFirst = false;
            return;
        }
        mCallback.run();
    }

    private class CallTimerCallback implements Runnable {
        @Override
        public void run() {
            periodicUpdateTimer();
        }
    }
}
