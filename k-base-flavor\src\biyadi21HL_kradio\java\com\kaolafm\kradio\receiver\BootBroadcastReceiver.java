package com.kaolafm.kradio.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.kaolafm.kradio.lib.base.flavor.BootCompletedLogicListener;
import com.kaolafm.kradio.lib.utils.InjectManager;
import com.kaolafm.kradio.lib.utils.SharedPreferenceUtil;
import com.kaolafm.kradio.report.ReportManager;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.StartReportEvent;


import static com.kaolafm.kradio.uitl.Constants.CAR_START_EVENT_CODE;

/**
 * Created by <PERSON><PERSON>l on 2018/3/5.
 */

public class BootBroadcastReceiver extends BroadcastReceiver {

    private static final String FIRST_BOOT_XML_NAME = "firstBoot";

    private static final String FIRST_BOOT_VALUE = "firstBootValue";

    private SharedPreferenceUtil mSharedPreferenceUtil;

    @Override
    public void onReceive(Context context, Intent intent) {
        Log.i("BootBroadcastReceiver", "BootBroadcastReceiver:" + intent.getAction());
        if (Intent.ACTION_BOOT_COMPLETED.equals(intent.getAction())) {
            BootCompletedLogicListener bootCompletedLogicListener = InjectManager.getInstance().getBootCompletedLogicListener();
            if (bootCompletedLogicListener != null) {
                bootCompletedLogicListener.onBootCompletedLogic(context, intent);
            }
        }

        if (mSharedPreferenceUtil == null) {
            mSharedPreferenceUtil = SharedPreferenceUtil.newInstance(context, FIRST_BOOT_XML_NAME, Context.MODE_PRIVATE);
        }

        if (ReportManager.getInstance().isInterceptReport()) {
            return;
        }

        StartReportEvent startReportEvent = new StartReportEvent();
        startReportEvent.setEventcode(CAR_START_EVENT_CODE);
        boolean flag = mSharedPreferenceUtil.getBoolean(FIRST_BOOT_VALUE, true);
        String flow;
        if (flag) {
            flow = "1";
            mSharedPreferenceUtil.putBoolean(FIRST_BOOT_VALUE, false);
        } else {
            flow = "0";
        }
        startReportEvent.setFlow(flow);
        ReportHelper.getInstance().addEvent(startReportEvent, true);
    }
}
