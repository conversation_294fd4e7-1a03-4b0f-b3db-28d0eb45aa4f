package com.kaolafm.kradio.flavor.splash;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;

@Aspect
public class SplashAspect {
    //https://app.huoban.com/tables/2100000007530121/items/2300001665219025?userId=1545533
    @Around("execution(* com.kaolafm.auto.home.HubActivity.getDelayTime(..))")
    public Object setSplashTime(ProceedingJoinPoint joinPoint) throws Throwable {
        return 100;
    }
}
