<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/coin_main_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guidelineV"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.5" />

    <!-- 扫码部分-->
    <ImageView
        android:id="@+id/qrCode"
        android:layout_width="@dimen/m200"
        android:layout_height="@dimen/m200"
        android:src="@drawable/media_default_pic"
        app:layout_constraintBottom_toTopOf="@+id/qrText"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/guidelineV"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/qrText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y40"
        android:gravity="center_horizontal"
        android:text="@string/scan_qrcode_to_login_and_buy_from_misco_program"
        android:textColor="@color/text_color_2"
        android:textSize="@dimen/text_size_title4"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/guidelineV"
        app:layout_constraintTop_toBottomOf="@+id/qrCode" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupQr"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="qrCode,qrText" />

    <!-- 积分部分-->
    <TextView
        android:id="@+id/iconAndText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/y18"
        android:drawableLeft="@drawable/ic_coin"
        android:drawablePadding="@dimen/x12"
        android:gravity="center"
        android:text="积分"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size5"
        app:layout_constraintBottom_toTopOf="@+id/scrollNumberView"
        app:layout_constraintLeft_toLeftOf="@+id/guidelineV"
        app:layout_constraintRight_toRightOf="parent" />

    <com.kaolafm.view.ScrollNumberView
        android:id="@+id/scrollNumberView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/color_1"
        android:textSize="@dimen/m98"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@+id/guidelineV"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:snv_textColor="@color/color_1"
        app:snv_textSize="@dimen/m98" />

    <TextView
        android:id="@+id/tvFen"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/x12"
        android:layout_marginBottom="@dimen/x24"
        android:text="分"
        android:textColor="@color/color_1"
        android:textSize="@dimen/text_size15"
        app:layout_constraintBottom_toBottomOf="@+id/scrollNumberView"
        app:layout_constraintLeft_toRightOf="@+id/scrollNumberView" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupCoin"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="scrollNumberView,iconAndText,tvFen" />
</androidx.constraintlayout.widget.ConstraintLayout>