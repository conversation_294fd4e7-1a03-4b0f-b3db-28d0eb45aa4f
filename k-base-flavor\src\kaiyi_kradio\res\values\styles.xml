<resources xmlns:tools="http://schemas.android.com/tools">

    <style name="AppThemeCompat" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowNoTitle">true</item>//无标题
        <item name="android:windowActionBar">false</item>//无ActionBar
        <item name="android:windowFullscreen">@bool/isWindowFullScreen</item>//全屏即无通知栏
        <item name="android:windowContentOverlay">@null</item>//是否有遮盖
        <item name="android:windowBackground">@null</item>
        <item name="android:colorControlHighlight" tools:targetApi="lollipop">
            @android:color/transparent
        </item>
    </style>

    <style name="AppTheme" parent="Theme.AppCompat.NoActionBar">
        <!-- 设置无标题-->
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>//无ActionBar
        <item name="android:windowFullscreen">@bool/isWindowFullScreen</item>//全屏即无通知栏
        <item name="android:windowContentOverlay">@null</item>//是否有遮盖
        <!--<item name="android:windowBackground">@null</item>-->
        <!--<item name="android:windowBackground">@drawable/kradio_splash</item>-->
        <!--<item name="android:windowIsTranslucent">true</item>-->
        <item name="android:windowDisablePreview">false</item>
        <item name="android:colorControlHighlight" tools:targetApi="lollipop">
            @android:color/transparent
        </item>
    </style>

    <style name="AppThemeCompat.splash" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowNoTitle">true</item>//无标题
        <item name="android:windowActionBar">false</item>//无ActionBar
        <item name="android:windowFullscreen">@bool/isWindowFullScreen</item>//全屏即无通知栏
        <item name="android:windowContentOverlay">@null</item>//是否有遮盖
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@drawable/kradio_splash</item>
        <!--<item name="android:windowAnimationStyle">@style/activityDefaultAnimation</item>-->
        <!--<item name="android:windowAnimationStyle">@style/activityAnim</item>-->
    </style>
</resources>


