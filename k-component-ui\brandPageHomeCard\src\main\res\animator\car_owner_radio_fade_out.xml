<?xml version="1.0" encoding="utf-8"?>
<objectAnimator xmlns:android="http://schemas.android.com/apk/res/android"
    android:duration="500"
    android:propertyName="alpha"
    android:valueFrom="1f"
    android:valueTo="0f"
    >

<!--    &lt;!&ndash;放大&ndash;&gt;-->
<!--    <objectAnimator-->
<!--        android:duration="500"-->
<!--        android:propertyName="scaleX"-->
<!--        android:valueFrom="1.0"-->
<!--        android:valueTo="1.2"/>-->

<!--    <objectAnimator-->
<!--        android:duration="500"-->
<!--        android:propertyName="scaleY"-->
<!--        android:valueFrom="1.0"-->
<!--        android:valueTo="1.2"/>-->

<!--    &lt;!&ndash;缩小&ndash;&gt;-->
<!--    <objectAnimator-->
<!--        android:duration="500"-->
<!--        android:startOffset="500"-->
<!--        android:propertyName="scaleX"-->
<!--        android:valueFrom="1.2"-->
<!--        android:valueTo="1.0"/>-->

<!--    &lt;!&ndash;缩小&ndash;&gt;-->
<!--    <objectAnimator-->
<!--        android:duration="500"-->
<!--        android:startOffset="500"-->
<!--        android:propertyName="scaleY"-->
<!--        android:valueFrom="1.2"-->
<!--        android:valueTo="1.0"/>-->

</objectAnimator>