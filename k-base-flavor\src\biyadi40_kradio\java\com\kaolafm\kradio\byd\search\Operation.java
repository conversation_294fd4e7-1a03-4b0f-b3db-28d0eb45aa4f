package com.kaolafm.kradio.byd.search;

import android.content.Intent;
import android.os.RemoteException;

import com.kaolafm.kradio.byd.BydSpeechCtlActionConstant;
import com.kaolafm.kradio.clientControlerForKradio.ClientImpl;
import com.kaolafm.sdk.client.ErrorInfo;
import com.kaolafm.sdk.client.PlayResult;

import static com.kaolafm.kradio.byd.BydSpeechCtlActionConstant.RESULT_FAILED;
import static com.kaolafm.kradio.byd.BydSpeechCtlActionConstant.RESULT_SUCCESS;
import static com.kaolafm.kradio.uitl.SpeechUtil.sendCmdResult;


public abstract class Operation {
    protected ClientImpl client;
    protected Intent intent;

    public Operation(ClientImpl client, Intent intent) {
        this.client = client;
        this.intent = intent;
    }

    protected void doSearch(String keywords, int qulityType, int field, String artist,
                            String audioName, String albumName, String category) {
        try {
            client.search4("kaola", qulityType, field, 1, artist,
                    audioName, albumName, category, keywords, null, new PlayResult() {
                        @Override
                        public void onSuccuss() throws RemoteException {
                            sendCmdResult(BydSpeechCtlActionConstant.AUTOVOICE_SEARCH, RESULT_SUCCESS, "");
                        }

                        @Override
                        public void onFailure(ErrorInfo errorInfo) throws RemoteException {
                            sendCmdResult(BydSpeechCtlActionConstant.AUTOVOICE_SEARCH, RESULT_FAILED, "");
                        }
                    });
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public abstract void exe();
}
