package com.kaolafm.kradio.brand.mvp;


import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.lib.base.mvp.IView;
import com.kaolafm.opensdk.api.brandpage.model.BrandPageListBean;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-03-01
 */
public interface IBrandPageView extends IView {

    void onLoaing();

    void onLoadFinish();

    void showTabAndList(BrandPageListBean brandPageListBean);

    void showTabAndFailure(Exception e);

    void onShowContent(List<HomeCell> cells);

    void onUpdateLiveStatus(List<HomeCell> newCells);

    void onShowContentFailure(Exception e);
}
