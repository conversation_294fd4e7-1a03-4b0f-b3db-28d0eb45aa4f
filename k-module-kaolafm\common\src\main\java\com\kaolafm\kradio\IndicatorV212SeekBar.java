package com.kaolafm.kradio;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.LinearGradient;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.Region;
import android.graphics.Shader;
import android.graphics.drawable.GradientDrawable;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import androidx.annotation.NonNull;
import android.text.TextPaint;
import android.util.AttributeSet;
import android.util.Log;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.SeekBar;
import android.widget.TextView;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.ResUtil;

import skin.support.widget.SkinCompatSeekBar;

public class IndicatorV212SeekBar extends SkinCompatSeekBar {
    private final String TAG = IndicatorV212SeekBar.class.getSimpleName();
    private Paint mPaint;
    //
    private float radiusTopLeft, radiusTopRight, radiusBottomLeft, radiusBottomRight;
    private int startColorBg;
    private int endColorBg;
    private int positionStartColorBg;
    private int positionEndColorBg;
    private int startColorSeek;
    private int centerColorSeek;
    private int endColorSeek;
    private int startColorThumb;
    private int endColorThumb;
    private int textColorThumb;
    private int textSizeThumb;
    private boolean needInnerThumbView = true;

    private LinearGradient mBgPositionShader, mBgUnTouchShader, mPorgressShader, mThumbShader;
    private Matrix mBgMatrix, mProgressMatrix, mThumbMatrix;
    private Path mBgPath;
    private RectF mBgRect;
    private int bgPaddingTop;

    private int realWidth;
    private int mThumbHeight;

    // 进度文字位置信息
    private Rect mProgressTextRect = new Rect();
    private boolean needComputeWidth = true;
    private float txtWidth = -1;
    // 滑块按钮宽度
    private float mThumbWidth = 0;
    private RectF mThumbRect;
    private int mIndicatorWidth = dp2px(50);

    //滑块所在的横向可点击区域，点击该区域可以实现seek功能
    private Region mTouchRegion;

    //支持给进度条传入一个TextView作为滑块使用
    private TextView mThumbView;
    private GradientDrawable mThumbViewBgDrawable;
    private String mThumbText;

    private Handler mHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            if (msg.what == SET_THUMB_VISIBILITY) {
                isShowThumb = true;
                invalidate();
            } else if (msg.what == SET_THUMB_INVISIBILITY) {
                isShowThumb = false;
                invalidate();
            }
        }
    };
    private static final int SET_THUMB_INVISIBILITY = 10000;
    private static final int SET_THUMB_VISIBILITY = 10001;
    private static final long SHOW_THUMB_TIME = 2000;   //滑块显示的时间

    private OnIndicatorSeekBarChangeListener mIndicatorSeekBarChangeListener;

    private boolean isTouching = false; //是否正在拖动
    private int mProgressFromUser = 0;  //用户拖动时的进度
    private boolean isShowThumb = false;    //是否显示滑块


    public IndicatorV212SeekBar(Context context) {
        this(context, null);
    }

    public IndicatorV212SeekBar(Context context, AttributeSet attrs) {
        this(context, attrs, R.attr.seekBarStyle);
    }

    public IndicatorV212SeekBar(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.IndicatorV212SeekBar);

        if (typedArray != null) {
            startColorBg = typedArray.getColor(R.styleable.IndicatorV212SeekBar_start_color_bg, ResUtil.getColor(R.color.playerbar_start_color_bg));
            endColorBg = typedArray.getColor(R.styleable.IndicatorV212SeekBar_end_color_bg, ResUtil.getColor(R.color.playerbar_end_color_bg));
            positionStartColorBg = typedArray.getColor(R.styleable.IndicatorV212SeekBar_position_start_color_bg, ResUtil.getColor(R.color.playerbar_position_start_color_bg));
            positionEndColorBg = typedArray.getColor(R.styleable.IndicatorV212SeekBar_position_end_color_bg, ResUtil.getColor(R.color.playerbar_position_end_color_bg));
            radiusTopLeft = typedArray.getDimension(R.styleable.IndicatorV212SeekBar_radius_top_left, ResUtil.getDimen(R.dimen.m26));
            radiusTopRight = typedArray.getDimension(R.styleable.IndicatorV212SeekBar_radius_top_right, ResUtil.getDimen(R.dimen.m26));
            radiusBottomLeft = typedArray.getDimension(R.styleable.IndicatorV212SeekBar_radius_bottom_left, ResUtil.getDimen(R.dimen.m26));
            radiusBottomRight = typedArray.getDimension(R.styleable.IndicatorV212SeekBar_radius_bottom_right, ResUtil.getDimen(R.dimen.m26));
            startColorSeek = typedArray.getColor(R.styleable.IndicatorV212SeekBar_start_color_seek, ResUtil.getColor(R.color.playerbar_start_color_seek));
            centerColorSeek = typedArray.getColor(R.styleable.IndicatorV212SeekBar_center_color_seek, ResUtil.getColor(R.color.playerbar_center_color_seek));
            endColorSeek = typedArray.getColor(R.styleable.IndicatorV212SeekBar_end_color_seek, ResUtil.getColor(R.color.playerbar_end_color_seek));
            startColorThumb = typedArray.getColor(R.styleable.IndicatorV212SeekBar_start_color_thumb, ResUtil.getColor(R.color.playerbar_start_color_thumb));
            endColorThumb = typedArray.getColor(R.styleable.IndicatorV212SeekBar_end_color_thumb, ResUtil.getColor(R.color.playerbar_end_color_thumb));
            textColorThumb = typedArray.getColor(R.styleable.IndicatorV212SeekBar_text_color_thumb, ResUtil.getColor(R.color.playerbar_text_color_thumb));
            textSizeThumb = typedArray.getColor(R.styleable.IndicatorV212SeekBar_text_size_thumb, ResUtil.getDimen(R.dimen.m20));
            needInnerThumbView = typedArray.getBoolean(R.styleable.IndicatorV212SeekBar_need_inner_thumb_view, true);
            typedArray.recycle();
        }
        init();
        setLayerType(View.LAYER_TYPE_SOFTWARE, null);
    }

    private void init() {
        mPaint = new TextPaint();
        mPaint.setAntiAlias(true);
        mPaint.setColor(textColorThumb);//文字颜色
        mPaint.setTextSize(textSizeThumb);
        mBgPositionShader = new LinearGradient(0, 0, 0, 1, positionStartColorBg, positionEndColorBg, Shader.TileMode.CLAMP);
        mBgUnTouchShader = new LinearGradient(0, 0, 0, 1, startColorBg, endColorBg, Shader.TileMode.CLAMP);
        mBgMatrix = new Matrix();
        mBgPositionShader.setLocalMatrix(mBgMatrix);
        mBgPath = new Path();
        mBgRect = new RectF();
        mThumbRect = new RectF();

        mPorgressShader = new LinearGradient(0, 0, 1, 0, new int[]{startColorSeek, centerColorSeek, endColorSeek}, new float[]{0, 0.9f, 1.0f}, Shader.TileMode.CLAMP);
        mProgressMatrix = new Matrix();
        mPorgressShader.setLocalMatrix(mProgressMatrix);

        if (needInnerThumbView) {
            mThumbShader = new LinearGradient(0, 0, 0, 1, startColorThumb, endColorThumb, Shader.TileMode.CLAMP);
            mThumbMatrix = new Matrix();
            mThumbShader.setLocalMatrix(mThumbMatrix);
        }

        mTouchRegion = new Region();

        // 设置滑动监听
        this.setOnSeekBarChangeListener(new OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                // NO OP
                if (fromUser) {
                    Log.d(TAG, "onProgressChanged progress=" + progress + " ,fromUser=" + fromUser);
                    mProgressFromUser = progress;
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                Log.d(TAG, "onStartTrackingTouch");
                isTouching = true;
                mHandler.removeMessages(SET_THUMB_INVISIBILITY);
                mHandler.sendEmptyMessage(SET_THUMB_VISIBILITY);
                if (mIndicatorSeekBarChangeListener != null) {
                    mIndicatorSeekBarChangeListener.onStartTrackingTouch(seekBar);
                }
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                Log.d(TAG, "onStopTrackingTouch");
                isTouching = false;
                mHandler.removeMessages(SET_THUMB_VISIBILITY);
                mHandler.sendEmptyMessageDelayed(SET_THUMB_INVISIBILITY, SHOW_THUMB_TIME);
                if (mIndicatorSeekBarChangeListener != null) {
                    mIndicatorSeekBarChangeListener.onStopTrackingTouch(seekBar);
                }
            }
        });
    }

    private int lastTxtLength = -1;

    @Override
    protected synchronized void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        bgPaddingTop = ResUtil.getDimen(R.dimen.y11);
        mBgMatrix.setScale(1, getMeasuredHeight() - bgPaddingTop, 0, 0);
        mBgMatrix.postTranslate(0, bgPaddingTop);
        mBgPositionShader.setLocalMatrix(mBgMatrix);
        mBgUnTouchShader.setLocalMatrix(mBgMatrix);
        realWidth = (int) (getMeasuredWidth() - radiusTopLeft - radiusTopRight);

        mThumbHeight = ResUtil.getDimen(R.dimen.y28);
        if (mThumbViewBgDrawable != null) mThumbViewBgDrawable.setCornerRadius(mThumbHeight / 2f);
        if (mThumbView != null) mThumbView.setBackground(mThumbViewBgDrawable);

        mTouchRegion.set((int) radiusTopLeft, 0, (int) (getMeasuredWidth() - radiusTopRight), mThumbHeight);
    }

    @Override
    protected synchronized void onDraw(Canvas canvas) {
        if (getMax() == 0) {
            mPaint.setStyle(Paint.Style.FILL);
            mPaint.setShader(mBgUnTouchShader);
            mBgPath.reset();
            mBgRect.set(0, bgPaddingTop, radiusTopLeft * 2, radiusTopLeft * 2 + bgPaddingTop);
            mBgPath.addArc(mBgRect, 180, 90);
            mBgPath.lineTo(getMeasuredWidth() - radiusTopRight, bgPaddingTop);
            mBgRect.set(getMeasuredWidth() - radiusTopRight * 2, bgPaddingTop, getMeasuredWidth(), bgPaddingTop + radiusTopRight * 2);
            mBgPath.arcTo(mBgRect, 270, 90);
            mBgPath.rLineTo(0, getMeasuredHeight() - bgPaddingTop - radiusBottomRight);
            mBgRect.set(getMeasuredWidth() - radiusBottomRight * 2, getMeasuredHeight() - radiusBottomRight * 2, getMeasuredWidth(), getMeasuredHeight());
            mBgPath.arcTo(mBgRect, 0, 90);
            mBgPath.lineTo(radiusBottomLeft, getMeasuredHeight());
            mBgRect.set(0, getMeasuredHeight() - radiusBottomRight * 2, radiusBottomLeft * 2, getMeasuredHeight());
            mBgPath.arcTo(mBgRect, 90, 90);
            mBgPath.close();
            canvas.drawPath(mBgPath, mPaint);
            return;
        }
        int progress = isTouching ? mProgressFromUser : getProgress();
        Log.d(TAG, "isTouching=" + isTouching + " ,progress=" + progress);


        //画背景
        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setShader(mBgPositionShader);

        mBgPath.reset();
        mBgRect.set(0, bgPaddingTop, radiusTopLeft * 2, radiusTopLeft * 2 + bgPaddingTop);
        mBgPath.addArc(mBgRect, 180, 90);
        float positionX = realWidth * (float) progress / getMax();
        mBgPath.rLineTo(positionX, 0);
        mBgPath.rLineTo(0, getMeasuredHeight() - bgPaddingTop);
        mBgPath.lineTo(radiusBottomLeft, getMeasuredHeight());
        mBgRect.set(0, getMeasuredHeight() - radiusBottomRight * 2, radiusBottomLeft * 2, getMeasuredHeight());
        mBgPath.arcTo(mBgRect, 90, 90);
        mBgPath.close();
        canvas.drawPath(mBgPath, mPaint);

        mPaint.setShader(mBgUnTouchShader);
        mBgPath.reset();
        mBgPath.moveTo(positionX + radiusTopLeft, bgPaddingTop);
        mBgPath.lineTo(getMeasuredWidth() - radiusTopRight, bgPaddingTop);
        mBgRect.set(getMeasuredWidth() - radiusTopRight * 2, bgPaddingTop, getMeasuredWidth(), bgPaddingTop + radiusTopRight * 2);
        mBgPath.arcTo(mBgRect, 270, 90);
        mBgPath.rLineTo(0, getMeasuredHeight() - bgPaddingTop - radiusBottomRight);
        mBgRect.set(getMeasuredWidth() - radiusBottomRight * 2, getMeasuredHeight() - radiusBottomRight * 2, getMeasuredWidth(), getMeasuredHeight());
        mBgPath.arcTo(mBgRect, 0, 90);
        mBgPath.lineTo(positionX + radiusTopLeft, getMeasuredHeight());
        mBgPath.close();
        canvas.drawPath(mBgPath, mPaint);

        mProgressMatrix.setScale(positionX, 1, 0, 0);
        mProgressMatrix.postTranslate(radiusTopLeft, bgPaddingTop);
        mPorgressShader.setLocalMatrix(mProgressMatrix);
        mPaint.setShader(mPorgressShader);
        mBgRect.set(radiusTopLeft, bgPaddingTop, radiusTopLeft + positionX, bgPaddingTop + ResUtil.getDimen(R.dimen.y5));
        canvas.drawRect(mBgRect, mPaint);


//        super.onDraw(canvas);

        if (mIndicatorSeekBarChangeListener != null) {
            mThumbText = mIndicatorSeekBarChangeListener.getProgressText(progress);
            if (mThumbText == null) {
                mThumbText = "";
            }
            int l = mThumbText.length();
            needComputeWidth = l != lastTxtLength;
            lastTxtLength = l;
        } else {
            mThumbText = progress + "%";
        }
        mPaint.getTextBounds(mThumbText, 0, mThumbText.length(), mProgressTextRect);

        // 进度百分比
        float progressRatio = 0;
        if (getMax() > 0) progressRatio = (float) progress / getMax();
        if (needComputeWidth) {
            txtWidth = mPaint.measureText(mThumbText);
        }
        int paddingHThumb = ResUtil.getDimen(R.dimen.x15);
        mThumbWidth = txtWidth + paddingHThumb * 2;

        float thumbX = 0;
        if (positionX < mThumbWidth / 2f) {
            thumbX = radiusTopLeft;
        } else if (positionX + radiusTopLeft + mThumbWidth / 2f > getMeasuredWidth() - radiusTopRight) {
            thumbX = getMeasuredWidth() - radiusTopRight - mThumbWidth;
        } else {
            thumbX = positionX - mThumbWidth / 2f + radiusTopLeft;
        }

        //画背景
        mThumbRect.set(thumbX, 0, thumbX + mThumbWidth, mThumbHeight);
        if (isShowThumb) {
            if (needInnerThumbView) {
                mThumbMatrix.setScale(1f, mThumbHeight, 0, 0);
                mThumbShader.setLocalMatrix(mThumbMatrix);
                mPaint.setShader(mThumbShader);
                canvas.drawRoundRect(mThumbRect, mThumbHeight / 2f, mThumbHeight / 2f, mPaint);
                mPaint.setShader(null);
                canvas.drawText(mThumbText, thumbX + paddingHThumb, mThumbHeight / 2f + Math.abs(mPaint.ascent() + mPaint.descent()) / 2, mPaint);
            } else if (mThumbView != null) {
                updateThumbView();
                if (mThumbView.getVisibility() != View.VISIBLE)
                    mThumbView.setVisibility(View.VISIBLE);
            }
        } else if (!needInnerThumbView && mThumbView != null) {
            if (mThumbView.getVisibility() != View.GONE)
                mThumbView.setVisibility(View.GONE);
        }

        if (mIndicatorSeekBarChangeListener != null) {
            float indicatorOffset = getWidth() * progressRatio - (mIndicatorWidth - mThumbWidth) / 2 - mThumbWidth * progressRatio;
            mIndicatorSeekBarChangeListener.onProgressChanged(this, progress, indicatorOffset);
        }
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (event.getAction() == MotionEvent.ACTION_DOWN) {
            mHandler.removeMessages(SET_THUMB_INVISIBILITY);
            mHandler.sendEmptyMessage(SET_THUMB_VISIBILITY);
            mHandler.sendEmptyMessageDelayed(SET_THUMB_INVISIBILITY, SHOW_THUMB_TIME);
            Region region = new Region();
            region.set((int) mThumbRect.left, (int) mThumbRect.top, (int) mThumbRect.right, (int) mThumbRect.bottom);
            if (!region.contains((int) event.getX(), (int) event.getY())) {
                if (mTouchRegion.contains((int) event.getX(), (int) event.getY())) {
                    return super.onTouchEvent(event);
                }
                return false;
            }
        }
        return super.onTouchEvent(event);
    }

    @Override
    public synchronized void setProgress(int progress) {
        super.setProgress(progress);
        invalidate();
    }

    @Override
    public synchronized void setMax(int max) {
        super.setMax(max);
        invalidate();
    }

    /**
     * 设置进度监听
     *
     * @param listener OnIndicatorSeekBarChangeListener
     */
    public void setOnSeekBarChangeListener(OnIndicatorSeekBarChangeListener listener) {
        this.mIndicatorSeekBarChangeListener = listener;
    }

    @Override
    public void applySkin() {
        Rect bounds = getProgressDrawable().getBounds();//drawable的更换会使maxHeight 不生效，单独修改下。
        super.applySkin();
        getProgressDrawable().setBounds(bounds);
    }

    /**
     * 进度监听
     */
    public interface OnIndicatorSeekBarChangeListener {
        /**
         * 进度监听回调
         *
         * @param seekBar         SeekBar
         * @param progress        进度
         * @param indicatorOffset 指示器偏移量
         */
        public void onProgressChanged(SeekBar seekBar, int progress, float indicatorOffset);

        /**
         * 开始拖动
         *
         * @param seekBar SeekBar
         */
        public void onStartTrackingTouch(SeekBar seekBar);

        /**
         * 停止拖动
         *
         * @param seekBar SeekBar
         */
        public void onStopTrackingTouch(SeekBar seekBar);

        public String getProgressText(int progress);
    }

    /**
     * dp转px
     *
     * @param dp dp值
     * @return px值
     */
    public int dp2px(float dp) {
        return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dp,
                getResources().getDisplayMetrics());
    }

    /**
     * sp转px
     *
     * @param sp sp值
     * @return px值
     */
    private int sp2px(float sp) {
        return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_SP, sp,
                getResources().getDisplayMetrics());
    }

    @Override
    protected void onDetachedFromWindow() {
        mHandler.removeCallbacksAndMessages(null);
        super.onDetachedFromWindow();
    }

    public void setStartColorBg(int start_color_bg) {
        this.startColorBg = start_color_bg;
        mBgUnTouchShader = new LinearGradient(0, 0, 0, 1, start_color_bg, endColorBg, Shader.TileMode.CLAMP);
        mBgUnTouchShader.setLocalMatrix(mBgMatrix);
    }

    public void setEndColorBg(int end_color_bg) {
        this.endColorBg = end_color_bg;
        mBgUnTouchShader = new LinearGradient(0, 0, 0, 1, startColorBg, end_color_bg, Shader.TileMode.CLAMP);
        mBgUnTouchShader.setLocalMatrix(mBgMatrix);
    }

    public void setPositionStartColorBg(int position_start_color_bg) {
        this.positionStartColorBg = position_start_color_bg;
        mBgPositionShader = new LinearGradient(0, 0, 0, 1, position_start_color_bg, positionEndColorBg, Shader.TileMode.CLAMP);
        mBgPositionShader.setLocalMatrix(mBgMatrix);
    }

    public void setPositionEndColorBg(int position_end_color_bg) {
        this.positionEndColorBg = position_end_color_bg;
        mBgPositionShader = new LinearGradient(0, 0, 0, 1, positionStartColorBg, position_end_color_bg, Shader.TileMode.CLAMP);
        mBgPositionShader.setLocalMatrix(mBgMatrix);
    }

    public void setStartColorSeek(int start_color_seek) {
        this.startColorSeek = start_color_seek;
        mPorgressShader = new LinearGradient(0, 0, 1, 0, new int[]{start_color_seek, centerColorSeek, endColorSeek}, new float[]{0, 0.9f, 1.0f}, Shader.TileMode.CLAMP);
    }

    public void setCenterColorSeek(int center_color_seek) {
        this.centerColorSeek = center_color_seek;
        mPorgressShader = new LinearGradient(0, 0, 1, 0, new int[]{startColorSeek, center_color_seek, endColorSeek}, new float[]{0, 0.9f, 1.0f}, Shader.TileMode.CLAMP);
    }

    public void setEndColorSeek(int end_color_seek) {
        this.endColorSeek = end_color_seek;
        mPorgressShader = new LinearGradient(0, 0, 1, 0, new int[]{startColorSeek, centerColorSeek, end_color_seek}, new float[]{0, 0.9f, 1.0f}, Shader.TileMode.CLAMP);
    }

    public void setStartColorThumb(int start_color_thumb) {
        this.startColorThumb = start_color_thumb;
        if (needInnerThumbView) {
            mThumbShader = new LinearGradient(0, 0, 0, 1, start_color_thumb, endColorThumb, Shader.TileMode.CLAMP);
            return;
        }
        if (mThumbViewBgDrawable != null)
            mThumbViewBgDrawable.setColors(new int[]{startColorThumb, endColorThumb});
        if (mThumbView != null) mThumbView.setBackground(mThumbViewBgDrawable);
    }

    public void setEndColorThumb(int end_color_thumb) {
        this.endColorThumb = end_color_thumb;
        if (needInnerThumbView) {
            mThumbShader = new LinearGradient(0, 0, 0, 1, startColorThumb, end_color_thumb, Shader.TileMode.CLAMP);
            return;
        }
        if (mThumbViewBgDrawable != null)
            mThumbViewBgDrawable.setColors(new int[]{startColorThumb, endColorThumb});
        if (mThumbView != null) mThumbView.setBackground(mThumbViewBgDrawable);
    }

    public void setTextColorThumb(int text_color_thumb) {
        this.textColorThumb = text_color_thumb;
        mPaint.setColor(textColorThumb);//文字颜色
        if (mThumbView != null) mThumbView.setTextColor(textColorThumb);
    }

    public void setThumbView(TextView thumbView) {
        this.mThumbView = thumbView;
        if (this.mThumbView == null) return;
        //设置背景
        if (mThumbViewBgDrawable == null) {
            mThumbViewBgDrawable = new GradientDrawable();
            mThumbViewBgDrawable.setShape(GradientDrawable.RECTANGLE);
            mThumbViewBgDrawable.setGradientType(GradientDrawable.LINEAR_GRADIENT);
            mThumbViewBgDrawable.setOrientation(GradientDrawable.Orientation.TOP_BOTTOM);
        }
        mThumbViewBgDrawable.setCornerRadius(mThumbHeight / 2f);
        mThumbViewBgDrawable.setColors(new int[]{startColorThumb, endColorThumb});
        this.mThumbView.setBackground(mThumbViewBgDrawable);

        mThumbView.setTextSize(TypedValue.COMPLEX_UNIT_PX, textSizeThumb);
        mThumbView.setGravity(Gravity.CENTER);
        //设置内容
        updateThumbView();
    }

    private void updateThumbView() {
        mThumbView.setText(mThumbText);
        int width = (int) (mThumbRect.right - mThumbRect.left);
        int height = (int) (mThumbRect.bottom - mThumbRect.top);
        ViewGroup.LayoutParams layoutParams = mThumbView.getLayoutParams();
        if (layoutParams.width != width || layoutParams.height != height) {
            layoutParams.width = width;
            layoutParams.height = height;
            mThumbView.setLayoutParams(layoutParams);
        }
        mThumbView.setX(mThumbRect.left + getX());
        mThumbView.setTextColor(textColorThumb);
    }

}