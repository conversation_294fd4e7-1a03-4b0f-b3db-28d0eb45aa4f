package com.kaolafm.kradio.common.view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Paint.Style;
import android.util.AttributeSet;

import com.kaolafm.kradio.k_kaolafm.R.dimen;
import com.kaolafm.kradio.lib.utils.ResUtil;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public final class StrokeRoundImageView extends RoundImageView {
   private Paint mCirclePaint = new Paint(1);
   private float mStrokeWidth;

   public StrokeRoundImageView(@Nullable Context context) {
      this(context,null);
   }

   public StrokeRoundImageView(@Nullable Context context, @Nullable AttributeSet attrs) {
      this(context, attrs,0);
   }

   public StrokeRoundImageView(@Nullable Context context, @Nullable AttributeSet attrs, int defStyle) {
      super(context, attrs, defStyle);
      Paint var10000 = this.mCirclePaint;
      var10000.setStyle(Style.STROKE);
      var10000 = this.mCirclePaint;
      var10000.setColor(-1);
      this.mStrokeWidth = ResUtil.getDimension(dimen.m1);
      var10000 = this.mCirclePaint;
      var10000.setStrokeWidth(this.mStrokeWidth);
   }

   @Override
   protected void onDraw(@NotNull Canvas canvas) {
      super.onDraw(canvas);
      float var10001 = (float)this.getMeasuredWidth() / 2.0F;
      float var10002 = (float)this.getMeasuredHeight() / 2.0F;
      float var10003 = (float)this.getMeasuredWidth() / 2.0F - (float)2 * this.mStrokeWidth;
      Paint var10004 = this.mCirclePaint;
      canvas.drawCircle(var10001, var10002, var10003, var10004);
   }
}
