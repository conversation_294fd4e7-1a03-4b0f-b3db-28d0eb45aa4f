package com.kaolafm.kradio.category.qq;

import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.OnLifecycleEvent;
import androidx.fragment.app.Fragment;

import com.kaolafm.kradio.category.FragmentFactory;
import com.kaolafm.kradio.categories.CategoryConstant;
import com.kaolafm.kradio.common.CP;
import com.kaolafm.kradio.music.bean.QQMusicLoginInfoManager;
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.kradio.user.LoginManager;

public class QqDispatchPresenter extends BasePresenter<QqDispatchModel, QqDispatchContract.IView> implements QqDispatchContract.IPresenter {
    private LoginManager.LoginListener loginListener;
    private Fragment mFragmentLoad;
    private Fragment mFragmentUnload;
    private long mCategoryId;


//    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
//    public void loginOrLogout(QQMusicLoginEvent resultCode) {
//        Log.i("novelot", "loginOrLogout: resultCode=" + resultCode);
//        if (resultCode.getResultCode() == CategoryConstant.LOGOUT_RESULT_CODE) {
////            mFragmentAdapter.replace(0, QRCodeFragment.newInstance(CategoryConstant.LOGIN_TYPE_WECHAT));
////            isLogin = false;
//            mView.showData(false);
//        } else if (resultCode.getResultCode() == CategoryConstant.LOGIN_RESULT_SUCCESS_CODE) {
////            isLogin = true;
////            if (isToLogin) {
////                removeFrag(this);
////            } else {
////                mFragmentAdapter.replace(0, HorizontalSubcategoryFragment.newInstance(CategoryConstant.MEDIA_TYPE_MUSIC, -1));
////            }
//            mView.showData(true);
//        }
//
//    }


    public QqDispatchPresenter(QqDispatchContract.IView view) {
        super(view);
        if (loginListener == null) {
            loginListener = new LoginManager.LoginListener() {
                @Override
                public void onLoginStateChange(int cp, boolean isLogin) {
                    if (CP.QQ == cp) {
                        if (mView == null) {
                        } else {
                            loadData(mCategoryId);
                        }
                    }
                }

                @Override
                public void onCancel() {
                }
            };
        }
        LoginManager.getInstance().registerLoginListener(loginListener);
    }


    /**
     * 生命周期的监听处理,可以有效避免内存泄漏
     */
    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    public void unregisterLoginListener() {
        LoginManager.getInstance().unregisterLoginListener(loginListener);
    }


    @Override
    public void loadData(long categoryId) {
        mCategoryId = categoryId;
        Fragment f = mFragmentUnload;
        boolean isLogin = QQMusicLoginInfoManager.getInstance().isLogin();

        if (isLogin) {
            if (mFragmentLoad == null) {

                f = mFragmentLoad = FragmentFactory.createQqTabFragment(categoryId);
            }
        } else {
            if (mFragmentUnload == null) {
                f = mFragmentUnload = FragmentFactory.createQRCodeFragment(CategoryConstant.LOGIN_TYPE_WECHAT);
            }
        }

        mView.showData(f);

        //调试webview用
//        mFragmentUnload = FragmentFactory.createQRCodeFragment(CategoryConstant.LOGIN_TYPE_WECHAT);
//        mView.showData(mFragmentUnload);
    }
}