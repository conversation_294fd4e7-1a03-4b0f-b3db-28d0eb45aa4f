package com.kaolafm.kradio.online.categories;

import android.annotation.SuppressLint;
import android.os.Bundle;
import androidx.fragment.app.Fragment;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.online.categories.radio.RadioTabFragment;
import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.operation.model.category.Category;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import java.util.ArrayList;
import java.util.List;

import static com.kaolafm.kradio.lib.utils.Constants.PAGE_ID_CATEGORIES_AI;
import static com.kaolafm.kradio.lib.utils.Constants.PAGE_ID_CATEGORIES_ALBUM;
import static com.kaolafm.kradio.lib.utils.Constants.PAGE_ID_CATEGORIES_BROADCAST;
import static com.kaolafm.kradio.lib.utils.Constants.PAGE_ID_CATEGORIES_TV;

/**
 * <AUTHOR>
 * @date 2018/4/16
 */

class AllCategoriesModel extends SubcategoryModel {

    public AllCategoriesModel() {
        super(null);
    }

    public void getTopTabsAndFragments(long showId, HttpCallback<AllCategory> callback) {
        mOperationRequest.getCategoryRoot(ResType.TYPE_ALL, new HttpCallback<List<Category>>() {
            @SuppressLint("WrongConstant")
            @Override
            public void onSuccess(List<Category> categories) {
                if (!ListUtil.isEmpty(categories)) {
                    AllCategory allCategory = new AllCategory();
                    ArrayList<Fragment> fragments = new ArrayList<>();
                    String[] tabNames = new String[categories.size()];
                    String[] code = new String[categories.size()];
                    for (int i = 0, size = categories.size(); i < size; i++) {
                        Category category = categories.get(i);
                        String name = category.getName();
                        tabNames[i] = name;
                        code[i] = category.getCode();
                        long categoryId = Long.parseLong(category.getCode());
                        if (categoryId == showId) {
                            allCategory.showIndex = i;
                        }
                        switch (name) {
                            case "AI电台":
                                fragments.add(createRadioTabFragment(categoryId, 0, CategoryConstant.MEDIA_TYPE_RADIO, PAGE_ID_CATEGORIES_AI));
                                break;
                            case "专栏节目":
                                fragments.add(createRadioTabFragment(categoryId, 0, CategoryConstant.MEDIA_TYPE_MUSIC, PAGE_ID_CATEGORIES_ALBUM));
                                break;
                            case "在线广播":
                                fragments.add(createRadioTabFragment(categoryId, 0, CategoryConstant.MEDIA_TYPE_BROADCAST, PAGE_ID_CATEGORIES_BROADCAST));
                                break;
                            case "听电视":
                                fragments.add(createRadioTabFragment(categoryId, 0, CategoryConstant.MEDIA_TYPE_TV, PAGE_ID_CATEGORIES_TV));
                                break;
                        }
                    }
                    allCategory.fragments = fragments;
                    allCategory.tabNames = tabNames;
                    allCategory.code = code;
                    if (callback != null) {
                        callback.onSuccess(allCategory);
                    }
                } else {
                    if (callback != null) {
                        callback.onError(new ApiException("没有数据"));
                    }
                }
            }

            @Override
            public void onError(ApiException e) {
                if (callback != null) {
                    callback.onError(e);
                }
            }
        });
    }

    public static Fragment createRadioTabFragment(long showTabId, long sonId, String pageId) {
        RadioTabFragment fragment = new RadioTabFragment();
        Bundle args = new Bundle();
        args.putLong(CategoryConstant.CATEGORY_ID, showTabId);
        args.putLong(CategoryConstant.SUBCATEGORY_ID, sonId);
        args.putString(CategoryConstant.PAGE_ID, pageId);
        fragment.setArguments(args);
        return fragment;
    }

    public Fragment createRadioTabFragment(long showTabId, long sonId, @CategoryConstant.MediaType int type, String pageId) {
        RadioTabFragment fragment = new RadioTabFragment();
        Bundle args = new Bundle();
        args.putLong(CategoryConstant.CATEGORY_ID, showTabId);
        args.putLong(CategoryConstant.SUBCATEGORY_ID, sonId);
        args.putLong(CategoryConstant.MEDIA_TYPE, type);
        args.putString(CategoryConstant.PAGE_ID, pageId);
        fragment.setArguments(args);
        return fragment;
    }
}
