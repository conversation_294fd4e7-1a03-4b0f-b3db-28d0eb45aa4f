package com.kaolafm.kradio.home.comprehensive.playerbar;

import org.aspectj.lang.annotation.Aspect;

/**
 * @Description:
 * @Author: Maclay
 * @Date: 2022/1/26 3:08 下午
 */
@Aspect
public class ComprehensivePlayerBarAop {
//    private static final String TAG = "PlayerBarAop";
//
//    private static List<Class> showClass = new ArrayList<Class>() {
//        {
//            add(HorizontalHomePlayerFragment.class);
//        }
//    };
//
//    @Around("execution(void com.kaolafm.kradio.lib.base.ui.BaseFragment.onAttach(android.content.Context))")
//    public void onAttach(ProceedingJoinPoint point) throws Throwable {
//        point.proceed();
//        Log.d(TAG, "onAttach " + point.getThis().getClass().toString() + "--" + point.toString());
//        check(point.getThis().getClass());
//    }
//
//    @Around("execution(* com.kaolafm.kradio.lib.base.ui.BaseFragment.onResume(..))")
//    public void onResume(ProceedingJoinPoint point) throws Throwable {
//        point.proceed();
//        Log.d(TAG, "onResume " + point.getThis().getClass().toString() + "--" + point.toString());
//
//    }
//
//    public void check(Class clazz) {
//        if (showClass.contains(clazz)) {
//            showPlayerBar();
//        } else {
//            hidePlayBar();
//        }
//    }
//
//    public void showPlayerBar() {
//        Activity activity = AppManager.getInstance().getCurrentActivity();
//        if (activity != null) {
//            View view = activity.findViewById(R.id.playerBar);
//            if (view != null && view.getVisibility() != View.VISIBLE) {
//                view.setVisibility(View.VISIBLE);
//            }
//        }
//    }
//
//    public void hidePlayBar() {
//        Activity activity = AppManager.getInstance().getCurrentActivity();
//        if (activity != null) {
//            View view = activity.findViewById(R.id.playerBar);
//            if (view != null && view.getVisibility() != View.GONE) {
//                view.setVisibility(View.GONE);
//            }
//        }
//    }
}