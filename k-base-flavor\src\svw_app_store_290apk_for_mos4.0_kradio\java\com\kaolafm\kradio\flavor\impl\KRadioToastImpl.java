package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.view.Gravity;
import android.widget.Toast;

import com.kaolafm.kradio.lib.base.flavor.KRadioToastInter;
import com.kaolafm.kradio.lib.toast.ToastStyle;
import com.kaolafm.kradio.lib.toast.ToastUtil;

public class KRadioToastImpl implements KRadioToastInter {
    @Override
    public void showToast(Context context, String msg, int dur) {
//        Toast toast = Toast.makeText(context, msg, Toast.LENGTH_SHORT);
//        toast.setGravity(Gravity.TOP | Gravity.CENTER, 0, 0);
//        toast.show();
        IcasToast.show(context,msg,dur);
    }

    @Override
    public void showToast(int from, Context context, String msg) {
        ToastUtil.showOnly(context, msg);
    }

    @Override
    public int displayLevel(Object... args) {
        ToastUtil.dismiss();
        return ToastStyle.LEVEL_SYSTEM;
    }
}
