package com.kaolafm.ads.image;

import android.content.Context;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import com.kaolafm.ad.api.model.InteractionAdvert;
import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.ads.image.base.BaseInteractContentView;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.lib.utils.imageloader.listener.OnImageLoaderListener;

public class AdInteractContentView extends BaseInteractContentView {
    private TextView mIvAdMsg;
    private ImageView mIvAdClose;

    public AdInteractContentView(Context context) {
        super(context);
        init(context);
    }

    public AdInteractContentView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public AdInteractContentView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context){
        LayoutInflater.from(context).inflate(R.layout.ad_interact_view_layout, this, true);
        mImageView = findViewById(R.id.iv_ad_interact);
        mIvAdMsg = findViewById(R.id.tv_ad_msg);
        mIvAdClose = findViewById(R.id.iv_ad_close);
        mIvAdClose.setOnClickListener((v) -> {
            mAdImageListener.onAdSkip();
            hideWithAnim();
        });
        this.setOnClickListener(this::onViewClick);
        this.setVisibility(INVISIBLE);
    }

    @Override
    protected void displayImage(ImageView view, String url, OnImageLoaderListener listener) {
        ImageLoader.getInstance().displayCircleImage(getContext(), url, view, listener);
    }

    @Override
    protected void displayLocalImage(ImageView view, String path) {
        ImageLoader.getInstance().displayLocalCircleImage(getContext(), path, view);
    }

    @Override
    public void loadAdContent(InteractionAdvert interactionAdvert) {
        super.loadAdContent(interactionAdvert);
        mIvAdMsg.setText(interactionAdvert.getDescription());
    }

    @Override
    public void countdownToCloseAd() {
        if(mDuration != 0) {
            super.countdownToCloseAd();
        }
    }

    protected void onViewClick(View view){
        AdvertisingManager.getInstance().getReporter().click(interactionAdvert);
    }
}
