package com.kaolafm.kradio.lib.widget;

import android.content.Context;
import android.content.res.TypedArray;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.FrameLayout;
import com.kaolafm.kradio.lib.R;
import com.kaolafm.kradio.lib.utils.AnimUtil;

/**
 * 正方形的FrameLayout。宽高比的确定是根据横竖屏确定的，如果是横屏的话就根据高度和比例确定宽度，如果是竖屏就根据宽度和比例确定高度。
 * 默认有点击缩放效果，可以设置是否有缩放效果。
 * <AUTHOR>
 * @date 2018/4/24
 */

public class SquareFrameLayout extends FrameLayout {

    /**
     * 是否有点击播放效果，默认true有。
     */
    private boolean canScale;

    public SquareFrameLayout(@NonNull Context context) {
        this(context, null);
    }

    public SquareFrameLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SquareFrameLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    private void init(Context context, AttributeSet attrs) {
        TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.SquareFrameLayout);
        canScale = ta.getBoolean(R.styleable.SquareFrameLayout_canScale, true);
        ta.recycle();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        setMeasuredDimension(getDefaultSize(0, widthMeasureSpec), getDefaultSize(0, heightMeasureSpec));
        int measuredWidth = getMeasuredHeight();
        widthMeasureSpec = heightMeasureSpec = MeasureSpec.makeMeasureSpec(measuredWidth, MeasureSpec.EXACTLY);
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (canScale) {
            int action = event.getAction();
            switch (action) {
                case MotionEvent.ACTION_DOWN:
                    AnimUtil.startScalePress(this);
                    break;
                case MotionEvent.ACTION_OUTSIDE:
                case MotionEvent.ACTION_CANCEL:
                case MotionEvent.ACTION_UP:
                    AnimUtil.startScaleRelease(this);
                    break;
                default:
                    break;
            }
        }
        return super.onTouchEvent(event);
    }
}
