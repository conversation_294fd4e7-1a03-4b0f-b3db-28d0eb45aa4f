package com.kaolafm.kradio.home.comprehensive;


import android.util.Log;

import com.kaolafm.kradio.lib.utils.YTLogUtil;
import com.kaolafm.launcher.LauncherActivity;
import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.opensdk.player.logic.PlayerManager;

import java.lang.ref.WeakReference;

/**
 * <AUTHOR>
 * @date 2019-09-01
 */
public class LauncherOnPlayerInitCompleteListener implements IPlayerInitCompleteListener {

    private WeakReference<LauncherActivity> weakReference;

    private int type;

    /**
     * 初始化首页播放逻辑类型
     */
    public static final int INIT_PBP_TYPE = 2;

    public LauncherOnPlayerInitCompleteListener(LauncherActivity launcherActivity) {
        weakReference = new WeakReference<>(launcherActivity);
    }

    @Override
    public void onPlayerInitComplete(boolean b) {
        YTLogUtil.logStart(getClass().getSimpleName(), "onPlayerInitComplete", "isSuccess = " + b);
        PlayerManager.getInstance().removePlayerInitComplete(this);
        LauncherActivity launcherActivity = weakReference.get();
        if (launcherActivity != null) {
            if (type == INIT_PBP_TYPE) {
//                launcherActivity.initPlayerContext();
            } else {
//                launcherActivity.onPlayerInitComplete(b);
            }
        }
    }

    public void setType(int type) {
        this.type = type;
    }
}
