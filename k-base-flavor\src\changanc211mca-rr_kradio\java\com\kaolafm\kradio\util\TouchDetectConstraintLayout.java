package com.kaolafm.kradio.util;

import android.content.Context;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import android.util.AttributeSet;
import android.view.MotionEvent;

import com.kaolafm.kradio.lib.base.AppDelegate;

public class TouchDetectConstraintLayout extends ConstraintLayout {

    public TouchDetectConstraintLayout(Context context) {
        super(context);
    }

    public TouchDetectConstraintLayout(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public TouchDetectConstraintLayout(Context context, @Nullable AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent event) {
        MultiGestureMonitor.getInstance(AppDelegate.getInstance().getContext())
                .onTouchEvent(event);
        return super.onInterceptTouchEvent(event);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        MultiGestureMonitor.getInstance(AppDelegate.getInstance().getContext())
                .onTouchEvent(event);
        return super.onTouchEvent(event);
    }


}
