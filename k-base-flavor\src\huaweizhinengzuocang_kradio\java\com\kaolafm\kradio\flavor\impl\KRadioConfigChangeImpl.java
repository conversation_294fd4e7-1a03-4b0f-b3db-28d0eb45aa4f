package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.content.Context;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.KradioApplication;
import com.kaolafm.kradio.common.helper.SkinHelper;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.base.flavor.ConfigChangeInter;
import com.kaolafm.kradio.lib.base.flavor.UserCenterInter;
import com.kaolafm.kradio.lib.base.ui.BaseActivity;

import org.greenrobot.eventbus.EventBus;

import skin.support.SkinCompatManager;
import skin.support.utils.SkinPreference;

import static android.content.res.Configuration.UI_MODE_NIGHT_NO;
import static android.content.res.Configuration.UI_MODE_NIGHT_YES;

public class KRadioConfigChangeImpl implements ConfigChangeInter {

    private static final int MODE_NORMAL = 19;
    private static final int MODE_NIGHT = 35;
    private static final int MODE_NORMAL2 = 17;
    private static final int MODE_NIGHT2 = 33;
    private String TAG = "KRadioConfigChangeImpl";
    private int preUiMode = -1;

    @Override
    public void onConfigChanged(Configuration newConfig) {
        int uiMode = newConfig.uiMode;
        Log.i(TAG, "onConfigurationChanged...UI_MODE_CODE_PRE : " + preUiMode);
        if (preUiMode == uiMode) return;
        Log.i(TAG, "onConfigurationChanged...UI_MODE_CODE_NEW : " + uiMode);
        switch (uiMode) {
            case UI_MODE_NIGHT_NO:
            case MODE_NORMAL:
            case MODE_NORMAL2:
                Log.i(TAG, "changeTheme...UI_MODE: " + "NORMAL");
                SkinCompatManager.getInstance().restoreDefaultTheme(); //恢复默认
                SkinHelper.setDaySkinName(AppDelegate.getInstance().getContext(), SkinHelper.DAY_SKIN);
                postThemeChangeEvent("light");
                resetStatusBar();
                break;
            case UI_MODE_NIGHT_YES:
            case MODE_NIGHT:
            case MODE_NIGHT2:
                Log.i(TAG, "changeTheme...UI_MODE: " + "NIGHT");
                SkinCompatManager.getInstance().loadSkin(SkinHelper.NIGHT_SKIN, SkinCompatManager.SKIN_LOADER_STRATEGY_ASSETS);
                SkinHelper.setDaySkinName(AppDelegate.getInstance().getContext(), SkinHelper.NIGHT_SKIN);
                postThemeChangeEvent("dark");
                resetStatusBar();
                break;
            default:
                Log.i(TAG, "onConfigurationChanged... do nothing");
                break;
        }
        preUiMode = newConfig.uiMode;
    }

    private void postThemeChangeEvent(String theme) {
        UserCenterInter.ThemeChangeEvent themeChangeEvent = new UserCenterInter.ThemeChangeEvent();
        themeChangeEvent.setTheme(theme);
        EventBus.getDefault().post(themeChangeEvent);
    }

    @Override
    public void saveConfiguration(Configuration configuration) {
        // 解决第一次进来的时候资源覆盖问题
        //第一次没有初始化的时候判断下当前的
        String skin = SkinPreference.getInstance().getSkinName();
        Log.i(TAG, "onConfigurationChanged...saveConfiguration: old skin " + skin + " , oldUI mode " + preUiMode);
        if (preUiMode == -1) {
            boolean isNight = TextUtils.equals(skin, "night");
            if (isNight) {
                if (configuration.uiMode == MODE_NORMAL || configuration.uiMode == UI_MODE_NIGHT_NO || configuration.uiMode == MODE_NORMAL2) {
                    //不一致
                    onConfigChanged(configuration);
                }
            } else {
                if (configuration.uiMode == UI_MODE_NIGHT_YES || configuration.uiMode == MODE_NIGHT || configuration.uiMode == MODE_NIGHT2) {
                    onConfigChanged(configuration);
                }
            }
            Log.i(TAG, "onConfigurationChanged...saveConfiguration: old skin " + skin);
        }
        preUiMode = configuration.uiMode;
        Log.i(TAG, "onConfigurationChanged...SAVE_UI_MODE: " + preUiMode);
    }


    private void resetStatusBar() {
        new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
            @Override
            public void run() {
                Activity activity = AppManager.getInstance().getCurrentActivity();
                if (activity instanceof BaseActivity) {
                    ((BaseActivity) activity).transStatusBar();
                }
            }
        } ,800);
    }
}
