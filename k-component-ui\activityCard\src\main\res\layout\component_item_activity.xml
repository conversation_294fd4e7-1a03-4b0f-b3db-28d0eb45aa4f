<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/item_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/item_activitys_bg_iv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/comprehensive_activity_item_blue" />

    <LinearLayout
        android:orientation="vertical"
        android:layout_centerInParent="true"
        android:id="@+id/pic_rl"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center" >

        <RelativeLayout
            android:id="@+id/row_icon"
            android:layout_width="match_parent"
            android:gravity="center"
            android:layout_height="0dp"
            android:layout_weight="106" >

            <ImageView
                android:layout_alignParentBottom="true"
                android:id="@+id/qr_bg"
                android:layout_width="@dimen/y190"
                android:layout_height="@dimen/y190"
                android:layout_centerHorizontal="true"
                android:src="@drawable/compoment_activity_item_qr_bg" />

            <ImageView
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:layout_marginBottom="@dimen/m11"
                android:id="@+id/qrCode_image"
                android:layout_width="@dimen/y168"
                android:layout_height="@dimen/y168"
                tools:background="@color/blue01"
                tools:ignore="MissingConstraints" />

        </RelativeLayout>
        <LinearLayout
            android:id="@+id/row_title"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:gravity="center"
            android:layout_height="0dp"
            android:layout_weight="24" >

            <com.kaolafm.kradio.component.ui.base.view.KradioTextView
                android:id="@+id/title_activity"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/m10"
                android:layout_marginRight="@dimen/m10"
                android:gravity="center"
                android:maxLines="1"
                android:paddingLeft="@dimen/x5"
                android:paddingRight="@dimen/x5"
                android:textColor="#E9EDFF"
                android:textSize="@dimen/m26"
                android:textStyle="bold"
                tools:ignore="MissingConstraints"
                tools:text="活动标题活动标题活动标题" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/row_time"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:gravity="center"
            android:layout_height="0dp"
            android:layout_weight="18" >
            <com.kaolafm.kradio.component.ui.base.view.KradioTextView
                android:id="@+id/date_activity"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:maxEms="12"
                android:maxLines="1"
                android:textColor="#ffffff"
                android:textSize="@dimen/m24"
                tools:text="2022.03.12" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/row_content"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:gravity="start|top"
            android:layout_height="0dp"
            android:layout_weight="46" >

            <TextView
                android:id="@+id/des_activity"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/y20"
                android:maxLines="2"
                android:lineSpacingExtra="@dimen/m4"
                android:layout_marginStart="@dimen/m28"
                android:layout_marginEnd="@dimen/m28"
                android:textColor="#ccffffff"
                android:textSize="@dimen/m24"
                tools:text="新年活动新年活动新年活动新年活动新年活动新年活动新年活动新年活动新年活动新年活动新年活动" />

        </LinearLayout>
        <LinearLayout
            android:id="@+id/row_button"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="16" >

        </LinearLayout>

    </LinearLayout>

</RelativeLayout>