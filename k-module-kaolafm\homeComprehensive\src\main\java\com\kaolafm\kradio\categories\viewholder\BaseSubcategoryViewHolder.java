package com.kaolafm.kradio.categories.viewholder;

import android.content.Context;
import android.content.res.Resources.NotFoundException;
import android.view.View;
import android.view.ViewStub;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.bean.SubcategoryItemBean;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;

/**
 * 次级分类的ViewHolder的基类。做一些功能操作。
 *
 * <AUTHOR>
 * @date 2018/4/25
 */

public class BaseSubcategoryViewHolder extends BaseHolder<SubcategoryItemBean> {

    protected final Context mContext;

    ViewStub mVsLayoutPlaying;
    private View mPlayingView;

    public BaseSubcategoryViewHolder(View itemView) {
        super(itemView);
        mContext = itemView.getContext();
        try {
            mVsLayoutPlaying = itemView.findViewById(R.id.vs_layout_playing);
        } catch (NotFoundException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void setupData(SubcategoryItemBean subcategoryItemBean, int position) {
        setPlay(subcategoryItemBean);
    }

    /**
     * 设置播放动画
     *
     * @param subcategoryItemBean
     */
    private void setPlay(SubcategoryItemBean subcategoryItemBean) {
        if (subcategoryItemBean.isSelected()) {
            if (mVsLayoutPlaying != null) {
                mPlayingView = mVsLayoutPlaying.inflate();
                mVsLayoutPlaying = null;
            } else {
                if (mPlayingView != null) {
                    mPlayingView.setVisibility(View.VISIBLE);
                }
            }
        } else {
            if (mPlayingView != null) {
                mPlayingView.setVisibility(View.GONE);
            }
        }
    }
}
