<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingTop="@dimen/y18">

    <com.kaolafm.kradio.common.widget.NotScrollViewPager
        android:id="@+id/vp_all_category_content"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:paddingBottom="@dimen/online_fragment_categories_bottom"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
        android:id="@+id/vRootCate"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ViewStub
        android:id="@+id/all_category_no_network"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="@dimen/m255"
        android:layout="@layout/online_error_layout" />

    <include
        android:id="@+id/loading"
        layout="@layout/online_refresh_center"
        android:layout_width="400dp"
        android:layout_height="400dp"
        android:visibility="gone"
        android:layout_marginBottom="@dimen/online_fragment_categories_bottom"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>