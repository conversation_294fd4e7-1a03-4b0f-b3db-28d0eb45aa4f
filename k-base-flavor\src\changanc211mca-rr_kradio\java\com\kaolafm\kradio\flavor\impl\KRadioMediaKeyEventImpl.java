package com.kaolafm.kradio.flavor.impl;

import android.os.Handler;
import android.support.v4.media.session.MediaSessionCompat;
import android.util.Log;
import android.view.KeyEvent;


import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.base.flavor.KRadioMediaKeyEventInter;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.utils.MediaSessionUtil;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-12-11 18:25
 ******************************************/
public class KRadioMediaKeyEventImpl implements KRadioMediaKeyEventInter {
    private static final String TAG = "KRadioMediaKeyEventImpl";

    @Override
    public boolean onKeyUp(int keyCode, KeyEvent event) {
        Log.i(TAG, "onKeyUp----->keyCode = " + keyCode);
        MediaSessionCompat mediaSession = MediaSessionUtil.getInstance().getMediaSession();
        if (mediaSession != null && mediaSession.isActive()) {
            Log.w(TAG, "mediaSession is Active");
        }

        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                if (AntiShake.check(1000, AppManager.getInstance())) { //见KRadioConvertMediaKeyCodeImpl
                    Log.w(TAG, "1000ms内 执行过了mediaSession, return");
                    return;
                }
                keyCodeOptions(keyCode);
            }
        }, 100); //晚一点，在media button之后执行
        return true;
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        return false;
    }

    private void keyCodeOptions(int keyCode) {
        Log.w(TAG, "handle keycode:" + keyCode);
        if (keyCode == KeyEvent.KEYCODE_MEDIA_NEXT
                || keyCode == KeyEvent.KEYCODE_MEDIA_PREVIOUS
                || keyCode == KeyEvent.KEYCODE_ZOOM_IN
                || keyCode == KeyEvent.KEYCODE_ZOOM_OUT
                || keyCode == KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE) {
            if (keyCode == KeyEvent.KEYCODE_MEDIA_NEXT
                    || keyCode == KeyEvent.KEYCODE_ZOOM_IN) {
                //下一首
                PlayerManager.getInstance().playNext();
            } else if (keyCode == KeyEvent.KEYCODE_MEDIA_PREVIOUS
                    || keyCode == KeyEvent.KEYCODE_ZOOM_OUT) {
                //上一首
                PlayerManager.getInstance().playPre();
            } else if (keyCode == KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE) {
                //暂停、播放
                PlayerManagerHelper.getInstance().switchPlayerStatus(true);
            }
        }
    }
}