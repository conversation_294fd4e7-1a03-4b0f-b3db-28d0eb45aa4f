<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingTop="@dimen/m30"
    android:paddingBottom="@dimen/m5">

    <View
        android:id="@+id/top_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/m1"
        android:background="@color/activity_line_bg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/m30"
        android:paddingLeft="@dimen/m80"
        android:paddingRight="@dimen/m80"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/top_divider">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_tab"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:fadeScrollbars="false"
            android:overScrollMode="never"
            android:paddingRight="@dimen/m28"
            android:scrollbarFadeDuration="0"
            android:scrollbarSize="@dimen/m4"
            android:scrollbarStyle="outsideInset"
            android:scrollbarThumbVertical="@color/broadcast_scroll_bar_color"
            android:scrollbarTrackVertical="@color/broadcast_scroll_bar_bg_color"
            android:scrollbars="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:layout_width="@dimen/m188" />

        <com.kaolafm.kradio.common.widget.NotScrollViewPager
            android:id="@+id/vp_content"
            android:layout_width="0dp"
            android:layout_height="0dp"
            tools:background="#a0a0"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/rv_tab"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 隐藏控件，用于所见即可说语音执行滑动操作 -->
        <TextView
            style="@style/ContentDescriptionScrollUp"
            app:layout_constraintLeft_toLeftOf="@id/vp_content"
            app:layout_constraintRight_toRightOf="@id/vp_content"
            app:layout_constraintTop_toTopOf="@id/vp_content" />

        <TextView
            style="@style/ContentDescriptionScrollDown"
            app:layout_constraintBottom_toBottomOf="@id/vp_content"
            app:layout_constraintLeft_toLeftOf="@id/vp_content"
            app:layout_constraintRight_toRightOf="@id/vp_content" />

        <View
            android:id="@+id/bottom_mask"
            android:layout_width="0dp"
            android:layout_height="@dimen/m20"
            android:background="@drawable/broadcast_bottom_bg"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <include
        android:id="@+id/loading_view"
        layout="@layout/refresh_center"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        tools:visibility="visible" />

    <ViewStub
        android:id="@+id/vs_layout_error_page"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout="@layout/layout_each_status_page" />
</androidx.constraintlayout.widget.ConstraintLayout>