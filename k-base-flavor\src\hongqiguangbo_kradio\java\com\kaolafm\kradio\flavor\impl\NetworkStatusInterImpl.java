package com.kaolafm.kradio.flavor.impl;

import android.car.MobileInfo;
import android.content.Context;
import android.util.Log;

import com.kaolafm.kradio.flavor.BuildConfig;
import com.kaolafm.kradio.lib.base.flavor.NetworkStatusInter;

public class NetworkStatusInterImpl implements NetworkStatusInter {

    String TAG = "NetworkStatusInterImpl";

    @Override
    public boolean getNetStatus(Context context) {
        try {
            //方便开发是时候测试
            if (BuildConfig.DEBUG) {
                return true;
            }

            if (KRadioAppInitImpl.settingsIF != null) {
                MobileInfo mobileInfo = KRadioAppInitImpl.settingsIF.queryMobileStatus();
                Log.d(TAG, "STAStatus=" + KRadioAppInitImpl.settingsIF.querySTALinkStatus() + "-mobileInfo-" + mobileInfo.mMobileDataSwitchStatus + "-KRadioAppInitImpl.isNetstatus-" + KRadioAppInitImpl.isNetstatus);
//                if (1 == mobileInfo.mMobileDataSwitchStatus) {
                //流量开关比较准确      //wifi开关不准确
                return KRadioAppInitImpl.isNetstatus || 1 == mobileInfo.mMobileDataSwitchStatus;
//                    return NetworkUtil.isNetworkAvailableDefault(context)

            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return false;
    }

    @Override
    public String[] netAction() {
        return new String[]{/*ConnectivityManager.ACTION_NETWORK_CHANGED*/};
    }
}
