package com.kaolafm.kradio.common.event;

import androidx.annotation.NonNull;

/**
 * <pre>
 *     <AUTHOR> Wenchl
 *     e-mail : <EMAIL>
 *     time   : 2019/03/19
 *     desc   : 用户在账号页面退出绑定消息
 *     version: 1.0
 * </pre>
 */
public class LogoutBindEvent {

    public int status;

    public LogoutBindEvent(int status) {
        this.status = status;
    }

    public static final int LOGIN = 1;

    public static final int LOGOUT = 2;


    @NonNull
    @Override
    public String toString() {
        return "Login status " + status;
    }
}
