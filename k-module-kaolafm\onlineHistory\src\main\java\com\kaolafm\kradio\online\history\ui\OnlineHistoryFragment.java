package com.kaolafm.kradio.online.history.ui;

import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewStub;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.kaolafm.kradio.common.bean.HistoryItem;
import com.kaolafm.kradio.common.report.ReportParamUtil;
import com.kaolafm.kradio.common.report.ReportUtil;
import com.kaolafm.kradio.constant.UserComponentConst;
import com.kaolafm.kradio.basedb.bean.HeadTitleItem;
import com.kaolafm.kradio.history.mvp.IHistoryView;
import com.kaolafm.kradio.k_kaolafm.R; 
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioItemClickInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioMultiWindowInter;
import com.kaolafm.kradio.lib.base.ui.BaseLazyFragment;
import com.kaolafm.kradio.lib.base.ui.online.BaseViewPagerLazyFragment;
import com.kaolafm.kradio.lib.dialog.Dialogs;
import com.kaolafm.kradio.lib.dialog.Dialogs.Builder;
import com.kaolafm.kradio.lib.toast.SuperToast;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.ComponentUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.RecyclerViewExposeUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.common.bean.BroadcastRadioSimpleData;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonClickReportEvent;

import java.util.List; 


/**
 * 电台的收听历史
 * Created by kaolafm on 2018/5/2.
 */
@Route(path = RouterConstance.ONLINE_URL_HISTORY)
public class OnlineHistoryFragment extends BaseViewPagerLazyFragment<HistoryPresent>
        implements IHistoryView, RecyclerViewExposeUtil.OnItemExposeListener {
    private static final String TAG = "OnlineHistoryFragment";
 
    View mClearHistory; 
    RecyclerView mRvHistoryList;  
    TextView mTvHistoryCount; 
    ConstraintLayout mRootLayout; 
    View mHistoryLoading; 
    ViewStub mHistoryNetworkError;

    private OnlineHistoryAdapter mOnlineHistoryAdapter;

    private volatile int lineCount = 1;

    GridLayoutManager layoutManager;

    public KRadioMultiWindowInter mKRadioMultiWindowInter;

    private BasePlayStateListener mPlayerStateListener;

    RelativeLayout mErrorLayout;
    RecyclerViewExposeUtil exposeUtil;

    private static SuperToast mToast;

    @Override
    public void initView(View view) {

        mClearHistory=itemView.findViewById(R.id.user_clear_his);
        mRvHistoryList=itemView.findViewById(R.id.rv_history_list);
        mTvHistoryCount=itemView.findViewById(R.id.tv_history_count);
        mRootLayout=itemView.findViewById(R.id.history_main_layout);
        mHistoryLoading=itemView.findViewById(R.id.history_loading);
        mHistoryNetworkError=itemView.findViewById(R.id.vs_history_net_error);
 
        mClearHistory.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (AntiShake.check(view.getId())) {
                    return;
                }
                clickClear();
                //点击一键清空事件上报
                ButtonClickReportEvent event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_CLICK_CLEAR);
                ReportHelper.getInstance().addEvent(event);
            }
        });

        layoutManager = new GridLayoutManager(getContext(), lineCount, LinearLayoutManager.HORIZONTAL, false);
        //todo byd分屏适配需求
        layoutManager.setSpanSizeLookup(new GridLayoutManager.SpanSizeLookup() {
            @Override
            public int getSpanSize(int position) {
                int viewType = mOnlineHistoryAdapter.getItemViewType(position);
//                if (viewType == OnlineHistoryAdapter.HEAD_UNLOGIN_TIP || viewType == OnlineHistoryAdapter.HISTORY_COUNT_TITLE) {
                if (viewType == OnlineHistoryAdapter.HEAD_UNLOGIN_TIP) {
                    return lineCount;
                }
                return 1;
            }
        });
        mRvHistoryList.setLayoutManager(layoutManager);
        ((DefaultItemAnimator) mRvHistoryList.getItemAnimator()).setSupportsChangeAnimations(false);
        mOnlineHistoryAdapter = new OnlineHistoryAdapter(this);
        mOnlineHistoryAdapter.setOnItemClickListener((itemView, viewType, historyItem, position) -> {
            if (historyItem == null) {
                return;
            }
            if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
                return;
            }
            //点击上报事件
            reportContentClickEvent(historyItem, position);
            if (historyItem.isOffline()) {
                ToastUtil.showOnActivity(getActivity(), getString(R.string.online_res_drop_off));
                return;
            }
            if (PlayerManagerHelper.getInstance().isPlayCurrentRadio(historyItem.getRadioId())) {
                KRadioItemClickInter kRadioItemClickInter = ClazzImplUtil
                        .getInter("KRadioItemClickImpl");
                if (kRadioItemClickInter != null) {
                    kRadioItemClickInter.doItemClick();
                }
                return;
            }
            if (StringUtil.equals(String.valueOf(PlayerConstants.RESOURCES_TYPE_BROADCAST), historyItem.getType())) {
                BroadcastRadioSimpleData data = new BroadcastRadioSimpleData();
                data.setBroadcastId(Long.valueOf(historyItem.getRadioId()));
                data.setImg(historyItem.getPicUrl());
                data.setName(historyItem.getRadioTitle());
                PlayerManagerHelper.getInstance().addBroadcastRadioSimpleItem(data);
            }
            PlayerManagerHelper.getInstance().startHistory(historyItem);
        });

        mPlayerStateListener = new BasePlayStateListener() {
            @Override
            public void onPlayerPreparing(PlayItem playItem) {
                if (mOnlineHistoryAdapter == null) {
                    return;
                }

                mOnlineHistoryAdapter.setPlaying(playItem.getRadioId());
            }
        };

        PlayerManager.getInstance().addPlayControlStateCallback(mPlayerStateListener);
        mRvHistoryList.setAdapter(mOnlineHistoryAdapter);
        mRvHistoryList.addItemDecoration(new HistoryItemDecoration());

        exposeUtil = new RecyclerViewExposeUtil();
        exposeUtil.setRecyclerItemExposeListener(mRvHistoryList, this);

        mKRadioMultiWindowInter = ClazzImplUtil.getInter("KradioMultiWindowImpl");
    }

    /**
     * 点击item事件上报
     *
     * @param item
     */
    private void reportContentClickEvent(HistoryItem item, int position) {
        int typeInt = PlayerConstants.RESOURCES_TYPE_INVALID;
        try {
            typeInt = Integer.parseInt(item.getType());
        } catch (Exception e) {
            Log.i(TAG, "error=" + e.toString());
        }
        ReportUtil.addContentClickEvent("", ReportParamUtil.getRadioType(typeInt),
                "", String.valueOf(item.getRadioId()),
                ReportParamUtil.getEventTag(item.isVIP(), item.isFine()),
                getPageId(), String.valueOf(item.getCategoryId()), "" + position);
    }

    @Override
    public String getPageId() {
        return Constants.PAGE_ID_LISTENING_TRACE_HISTORY;
    }

    @Override
    protected int getLayoutId() {
        return R.layout.online_fragment_history;
    }

    @Override
    protected HistoryPresent createPresenter() {
        return new HistoryPresent(this);
    }

    @Override
    public void showButton(boolean show) {
        Fragment fragment = getParentFragment();
        if (fragment instanceof ButtonOperation) {
            ((ButtonOperation) fragment).showOrHide(show);
        }
    }

    @Override
    public void showHistory(List<HistoryItem> historyItems) {
        showHistory(true);
        String format = ResUtil.getString(R.string.history_count);
        int size = 0;
        for (int i = 0; i < historyItems.size(); i++) {
            if (!(historyItems.get(i) instanceof HeadTitleItem)) {
                size += 1;
            }
        }
        Log.e(TAG, "historyItems.size: " + size);
        if (size > 0) {
            mTvHistoryCount.setText(String.format(format, size));
            mTvHistoryCount.setVisibility(View.VISIBLE);
            mClearHistory.setVisibility(View.VISIBLE);
        } else {
            mTvHistoryCount.setVisibility(View.GONE);
            mClearHistory.setVisibility(View.GONE);
//            showHistory(false);
//            showErrorLayout(ResUtil.getString(R.string.online_no_listening_history), false);
        }

        mOnlineHistoryAdapter.setDataList(historyItems);
        if (exposeUtil != null) {
            mRvHistoryList.postDelayed(new Runnable() {
                @Override
                public void run() {
                    exposeUtil.handleCurrentVisibleItems();
                }
            }, 200);
        }
    }

    @Override
    public void showEmpty() {
        showHistory(false);
        showError(ResUtil.getString(R.string.online_no_listening_history), false);
    }

    @Override
    public void prepareFragmentStateForShowLoading() {
        mOnlineHistoryAdapter.setDataList(null);
        ViewUtil.setViewVisibility(mErrorLayout, View.GONE);
    }

    @Override
    public void showLoading() {
        ViewUtil.setViewVisibility(mHistoryLoading, View.VISIBLE);
    }

    @Override
    public void hideLoading() {
        ViewUtil.setViewVisibility(mHistoryLoading, View.GONE);
    }

    private void showHistory(boolean haveHistory) {
        ViewUtil.setViewVisibility(mRvHistoryList, haveHistory ? View.VISIBLE : View.GONE);
        ViewUtil.setViewVisibility(mErrorLayout, haveHistory ? View.GONE : View.VISIBLE);
        ViewUtil.setViewVisibility(mTvHistoryCount, haveHistory ? View.VISIBLE : View.GONE);
        ViewUtil.setViewVisibility(mClearHistory, haveHistory ? View.VISIBLE : View.GONE);
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (mPresenter != null) {
            mPresenter.start();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        PlayerManager.getInstance().removePlayControlStateCallback(mPlayerStateListener);
    }

    @Override
    protected void lazyLoad() {

    }

    @Override
    public void onResume() {
        super.onResume();
        //因为需要每次显示历史都刷新数据，所以lazyLoad不适用
        NetworkUtil.isNetworkAvailableWidthDefaultToast(getContext());
        if (mPresenter != null) {
            mPresenter.getHistoryList();
        }
    }

    public void clickClear() {

        boolean isLogin = false;
        try {
            isLogin = ComponentUtil.getResultValue(UserComponentConst.NAME, UserComponentConst.IS_USER_BOUND);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (isLogin && !NetworkUtil.isNetworkAvailable(getContext(), false)) {
            showToast(R.string.no_net_work_str);
            return;
        }

        DialogFragment dialogFragment = new Builder()
                .setBackground(ResUtil.getColor(R.color.history_clear_dialog_bg))
                .setType(Dialogs.TYPE_2BTN)
                .setGravity(Gravity.CENTER)
                .setMessage(ResUtil.getString(R.string.online_are_you_sure_to_clear_your_listening_history))
                .setOnPositiveListener(dialog -> {
                    mPresenter.clearHistory();
                    dialog.dismiss();
                })
                .create();
        dialogFragment.show(getFragmentManager(), "clear_history");
    }


//    private void updateView(boolean isLand) {
//        ConstraintSet set = new ConstraintSet();
//        set.clone(mRootLayout);
//        if (isLand) {
//            set.setVerticalBias(mTvHistoryListEmpty.getId(), 0.5f);
//        } else {
//            set.setVerticalBias(mTvHistoryListEmpty.getId(), 0.36f);
//        }
//        set.applyTo(mRootLayout);
//    }

//    @Override
//    protected void showAccordingToScreen(int orientation) {
//        int paddingLeft = ScreenUtil.getGlobalPaddingLeft(orientation);
//        int paddingRight = ScreenUtil.getGlobalPaddingRight(orientation);
//
//        mRootLayout.setPadding(paddingLeft, 0, paddingRight, 0);
//
//        //
//        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
//            lineCount = 2;
//            updateView(true);
//        } else {
//            lineCount = 1;
//            updateView(false);
//        }
//
//        if (mKRadioMultiWindowInter == null || !mKRadioMultiWindowInter.setHistoryGridLayoutManager(layoutManager)) {
//            layoutManager.setSpanCount(lineCount);
//        }
//
//        mRvHistoryList.setLayoutManager(layoutManager);
//        mOnlineHistoryAdapter.notifyDataSetChanged();
//
//    }

    private void showErrorLayout(String error, boolean clickToRetry) {
        if (mErrorLayout == null) {
            mErrorLayout = (RelativeLayout) mHistoryNetworkError.inflate();
            TextView tvNetworkNosign = mErrorLayout.findViewById(R.id.tv_error);
            tvNetworkNosign.setText(error);
            ImageView ivNetworkNoSign = mErrorLayout.findViewById(R.id.iv_error);
            // 支持点击重试
            if (clickToRetry) {
                ivNetworkNoSign.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        hideErrorLayout();
                        mPresenter.getHistoryList();
                    }
                });
            } else {
                ivNetworkNoSign.setImageResource(R.drawable.online_error_empty);
            }
        }
        ViewUtil.setViewVisibility(mErrorLayout, View.VISIBLE);
    }

    @Override
    public void hideErrorLayout() {
        if (mErrorLayout == null) {
            return;
        }
        ViewUtil.setViewVisibility(mErrorLayout, View.GONE);
    }

    @Override
    public void showError(String error, boolean clickToRetry) {
        ViewUtil.setViewVisibility(mRvHistoryList, View.GONE);
        hideLoading();
        showErrorLayout(error, clickToRetry);
    }

    @Override
    public void showToast(int resId) {
        ToastUtil.showError(getContext(), resId);
    }

    @Override
    protected void changeViewLayoutForStatusBar(View view) {
    }

    @Override
    protected void addFragmentRootViewPadding(View view) {
    }

    @Override
    protected void showAccordingToScreen(int orientation) {
//        boolean isLand = ResUtil.getOrientation() == Configuration.ORIENTATION_LANDSCAPE;
//        int paddingLeft = isLand ? ResUtil.getDimen(R.dimen.online_history_padding_left) : ResUtil.getDimen(R.dimen.x50);//ScreenUtil.getGlobalPaddingLeft(orientation);
//        int paddingRight = isLand ? 0 : ResUtil.getDimen(R.dimen.m50);//ScreenUtil.getGlobalPaddingRight(orientation);
//
////        mRootLayout.setPadding(paddingLeft, 0, paddingRight, 0);
//
////        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
////            lineCount = 1;
////            updateView(true);
////        } else {
////            lineCount = 1;
////            updateView(false);
////        }
//
//        Fragment fragment = getParentFragment();
//        boolean multi = false;
////        if (fragment instanceof BackUserFragment) {
////            if (((BackUserFragment) fragment).mKRadioMultiWindowInter != null && ((BackUserFragment) fragment).mKRadioMultiWindowInter.setHistoryGridLayoutManager(layoutManager)) {
////                multi = true;
////            }
////        }
//
//        KRadioMultiWindowInter multiWindowImpl = ClazzImplUtil.getInter("KradioMultiWindowImpl");
//        if (multiWindowImpl != null) {
//            boolean success = mKRadioMultiWindowInter.setHistoryGridLayoutManager(layoutManager);
//            if (success) multi = true;
//        }
//
//        if (!multi) {
//            layoutManager.setSpanCount(lineCount);
//            // 解决横竖屏切换后adapter item位置错乱的问题
//            mRvHistoryList.setAdapter(null);
//            mRvHistoryList.setLayoutManager(null);
//            mRvHistoryList.getRecycledViewPool().clear();
//            mRvHistoryList.setAdapter(mOnlineHistoryAdapter);
//        }
//        mRvHistoryList.setLayoutManager(layoutManager);
//        mOnlineHistoryAdapter.notifyDataSetChanged();

    }

    @Override
    public void onMultiWindowModeChanged(boolean isInMultiWindowMode) {
        super.onMultiWindowModeChanged(isInMultiWindowMode);
        if (mRvHistoryList != null) {
            RecyclerView.Adapter adapter = mRvHistoryList.getAdapter();
            RecyclerView.LayoutManager manager = mRvHistoryList.getLayoutManager();
            mRvHistoryList.setAdapter(null);
            mRvHistoryList.setLayoutManager(null);
            mRvHistoryList.getRecycledViewPool().clear();
            mRvHistoryList.setLayoutManager(manager);
            mRvHistoryList.setAdapter(adapter);
        }
    }

    protected boolean isReportFragment() {
        return true;
    }

    @Override
    public void onItemViewVisible(boolean visible, int position) {
        if (visible && mOnlineHistoryAdapter != null) {
            HistoryItem item = mOnlineHistoryAdapter.getItemData(position);
            reportContentShowEvent(item, position);
        }
    }

    private void reportContentShowEvent(HistoryItem item, int position) {
        if (TextUtils.isEmpty(item.getRadioId()) && TextUtils.isEmpty(item.getAudioId())) {
            //两个id都为null说明是未登录的头item，所以不上报
            return;
        }
        int typeInt = PlayerConstants.RESOURCES_TYPE_INVALID;
        try {
            typeInt = Integer.parseInt(item.getType());
        } catch (Exception e) {
            Log.i(TAG, "error=" + e.toString());
        }
        ReportUtil.addContentShowEvent("", ReportParamUtil.getRadioType(typeInt),
                "", String.valueOf(item.getRadioId()),
                ReportParamUtil.getEventTag(item.isVIP(), item.isFine()),
                getPageId(), String.valueOf(item.getCategoryId()), "" + position);
    }
}
