<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <gradient
                android:angle="180"
                android:endColor="#ffc489ff"
                android:startColor="#ff155fe0"
                android:type="linear" />
            <corners android:radius="@dimen/m8" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:angle="180"
                android:endColor="#ffc489ff"
                android:startColor="#ff155fe0"
                android:type="linear" />
            <corners android:radius="@dimen/m8" />
        </shape>
    </item>
</selector>