<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@drawable/bg_home">

    <com.kaolafm.kradio.component.ui.base.view.OvalImageView
        android:id="@+id/card_bg_iv"
        android:background="@drawable/component_card_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:oval_radius="@dimen/m8" />

    <com.kaolafm.kradio.component.ui.base.view.KradioTextView
        android:id="@+id/rotation_title_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/m40"
        android:layout_marginTop="@dimen/m30"
        android:layout_marginRight="@dimen/m40"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="我是标题啊啊"
        android:textColor="@color/text_color_7"
        android:textSize="@dimen/m26"
        app:kt_font_weight="0.3" />

    <View
        android:id="@+id/rotation_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/m1"
        android:layout_below="@+id/rotation_title_tv"
        android:layout_marginLeft="@dimen/m40"
        android:layout_marginTop="@dimen/m10"
        android:layout_marginRight="@dimen/m40"
        android:background="#33EEEEEE" />

    <com.kaolafm.kradio.component.ui.base.view.NoTouchRecyclerview
        android:id="@+id/rotation_rv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/rotation_line"
        android:layout_marginLeft="@dimen/m40"
        android:layout_marginRight="@dimen/m40" />

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:paddingRight="@dimen/m20"
        android:paddingBottom="@dimen/m20">

        <ImageView
            android:id="@+id/card_play_iv"
            android:layout_width="@dimen/m38"
            android:layout_height="@dimen/m38"
            android:src="@drawable/component_play_icon_2" />

        <com.kaolafm.kradio.component.ui.base.view.RateView
            android:id="@+id/card_layout_playing"
            android:layout_width="@dimen/m38"
            android:layout_height="@dimen/m38"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:layout_marginRight="@dimen/m20"
            android:layout_marginBottom="@dimen/m20"
            app:lottie_autoPlay="true"
            app:lottie_fileName="lottie/rate.json"
            app:lottie_loop="true" />
    </RelativeLayout>
    <!--    <View-->
    <!--        android:id="@+id/item_click_viwe"-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="match_parent"/>-->
</RelativeLayout>