package com.kaolafm.kradio.lib.bean;


import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-04-12 17:50
 ******************************************/
public class PlayerListItemData {
    /**
     * 音乐播单类型
     */
    public static final int PLAYER_LIST_MUSIC_TYPE = 1;
    /**
     * PGC播单类型
     */
    public static final int PLAYER_LIST_PGC_TYPE = 2;
    /**
     * 专辑/播单类型
     */
    public static final int PLAYER_LIST_ALBUM_TYPE = 3;

    /**
     * Footer 类型
     */
    public static final int PLAYER_LIST_FOOTER_TYPE = 1001;

    public PlayItem playItem;
    public String radioName;
    public boolean isSelected;
    public boolean hasLine = true;//标识选中的上一条item是否有线
    public int viewType;
}