package com.kaolafm.kradio.search;

import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.SharedPreferenceUtil;

import java.util.ArrayList;
import java.util.List;

public class SearchHistoryManager {

    private static final String SP_SEARCH_HISTORY = "searchHistory";

    private static final String SP_KEY_RECENT_SEARCH_TAG = "recentSearchTag";

    private static final int MAX_SEARCH_HISTORY_NUM = 5;

    private static final class SearchHistoryManagerHolder {
        private static final SearchHistoryManager INSTANCE = new SearchHistoryManager();
    }

    public static SearchHistoryManager getInstance() {
        return SearchHistoryManagerHolder.INSTANCE;
    }

    private SearchHistoryManager() {
    }

    public void addRecentSearchTag(String tag) {
        if (TextUtils.isEmpty(tag)) {
            return;
        }
        List<String> tags = getRecentSearchTags();
        if (tags.contains(tag)) {
            tags.remove(tag);
        }
        tags.add(0, tag);
        if (tags.size() > MAX_SEARCH_HISTORY_NUM) {
            tags.remove(MAX_SEARCH_HISTORY_NUM);
        }
        Gson gson = new Gson();
        String tagStr = gson.toJson(tags);
        SharedPreferenceUtil
                .getInstance(AppDelegate.getInstance().getContext(), SP_SEARCH_HISTORY).putString(SP_KEY_RECENT_SEARCH_TAG, tagStr);
    }


    public List<String> getRecentSearchTags() {
        List<String> results = new ArrayList<>();
        String tags = SharedPreferenceUtil
                .getInstance(AppDelegate.getInstance().getContext(), SP_SEARCH_HISTORY).getString(SP_KEY_RECENT_SEARCH_TAG, null);
        if (TextUtils.isEmpty(tags)) {
            return results;
        }
        Gson gson = new Gson();
        results = gson.fromJson(tags, new TypeToken<List<String>>() {
        }.getType());
        return results;
    }

    public void clearAllRecentSearchTags() {
        SharedPreferenceUtil
                .getInstance(AppDelegate.getInstance().getContext(), SP_SEARCH_HISTORY).remove(SP_KEY_RECENT_SEARCH_TAG);
    }
}
