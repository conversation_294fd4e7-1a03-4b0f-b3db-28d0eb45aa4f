package com.kaolafm.kradio.lib.utils.imageloader;

import android.content.Context;
import androidx.annotation.NonNull;
import com.bumptech.glide.Glide;
import com.bumptech.glide.GlideBuilder;
import com.bumptech.glide.Registry;
import com.bumptech.glide.annotation.GlideModule;
import com.bumptech.glide.load.engine.bitmap_recycle.LruBitmapPool;
import com.bumptech.glide.load.engine.cache.DiskLruCacheWrapper;
import com.bumptech.glide.load.engine.cache.LruResourceCache;
import com.bumptech.glide.load.engine.cache.MemorySizeCalculator;
import com.bumptech.glide.load.engine.cache.MemorySizeCalculator.Builder;
import com.bumptech.glide.load.model.GlideUrl;
import com.bumptech.glide.module.AppGlideModule;
import com.kaolafm.kradio.lib.base.flavor.GlideHttpClientConfig;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.imageloader.OkHttpUrlLoader.Factory;
import java.io.File;
import java.io.InputStream;
import okhttp3.OkHttpClient;

/**
 * 该类用来配置glide的，比如缓存，网络图片请求方式等。<br/>
 * 目前自定义的磁盘缓存的路径(kradio/image_cache)和最大值100M及内存缓存(默认缓存的0.8倍)<br/>
 * 网络图片请求方式更换为okhttp。
 * <AUTHOR>
 * @date 2018/4/15
 */
@GlideModule
public final class GlideModuleConfiguration extends AppGlideModule {

    /**
     * 磁盘缓存的路径。
     */
    public static final String IMAGE_DISK_CACHE_NAME = "kradio/image_cache";

    /**
     * 磁盘缓存的最大值。
     */
    public static final int IMAGE_DISK_CACHE_MAX_SIZE = 100 * 1024 *1024;

    @Override
    public void applyOptions(@NonNull Context context, @NonNull GlideBuilder builder) {
        builder.setDiskCache(() -> {
            File file = new File(context.getExternalCacheDir(), IMAGE_DISK_CACHE_NAME);
            file.mkdirs();
            return DiskLruCacheWrapper.create(file, IMAGE_DISK_CACHE_MAX_SIZE);
        });
        //自定义内存缓存 - CPU优化：极致降低内存使用以提升性能
        MemorySizeCalculator sizeCalculator = new Builder(context)
                .setMemoryCacheScreens(0.5f)  // 1.0f -> 0.5f 极致降低内存缓存屏幕数
                .setBitmapPoolScreens(1.0f)   // 1.5f -> 1.0f 极致降低位图池屏幕数
                .build();
        int defaultMemoryCacheSize = sizeCalculator.getMemoryCacheSize();
        int defaultBitmapPoolSize = sizeCalculator.getBitmapPoolSize();
        builder.setMemoryCache(new LruResourceCache((long) (defaultMemoryCacheSize * 0.05))); // 0.1 -> 0.05
        builder.setBitmapPool(new LruBitmapPool((long) (defaultBitmapPoolSize * 0.2)));        // 0.4 -> 0.2

    }

    @Override
    public void registerComponents(@NonNull Context context, @NonNull Glide glide, @NonNull Registry registry) {
        //Glide默认使用 HttpURLConnection 做网络请求,在这切换成 OkHttp 请求
        OkHttpClient.Builder builder = new OkHttpClient.Builder();
        GlideHttpClientConfig glideHttpClientConfig = ClazzImplUtil.getInter("GlideHttpClientConfigImpl");
        if (glideHttpClientConfig != null) {
            builder = glideHttpClientConfig.build(builder);
        }
        OkHttpClient okHttpClient = builder.build();
        registry.replace(GlideUrl.class, InputStream.class, new Factory(okHttpClient));
    }

    @Override
    public boolean isManifestParsingEnabled() {
        //返回false不检查清单文件
        return false;
    }
}