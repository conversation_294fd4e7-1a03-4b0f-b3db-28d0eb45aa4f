<?xml version="1.0" encoding="utf-8"?>
<!-- 需要外层套一个layout，否则所见即可说识别不到 -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/root_view"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center">

    <TextView
        android:id="@+id/tv_search_history_tag"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/y52"
        android:background="@drawable/bg_search_history"
        android:ellipsize="end"
        android:gravity="center"
        android:includeFontPadding="false"
        android:maxEms="12"
        android:minWidth="@dimen/x70"
        android:paddingLeft="@dimen/m40"
        android:paddingRight="@dimen/m40"
        android:singleLine="true"
        android:textColor="@color/comprehensive_search_history_tag_text_color"
        android:textSize="@dimen/m26" />
</LinearLayout>