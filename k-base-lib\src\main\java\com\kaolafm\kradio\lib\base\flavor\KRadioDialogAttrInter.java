package com.kaolafm.kradio.lib.base.flavor;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-01-13 17:06
 ******************************************/
public interface KRadioDialogAttrInter {
    /**
     * @param args （args[0] = Dialog）
     * @return true成功处理相关逻辑，false未成功处理相关逻辑
     */
    boolean beforeDialogShow(Object... args);

    /**
     * @param args （args[0] = Dialog）
     * @return true成功处理相关逻辑，false未成功处理相关逻辑
     */
    boolean afterDialogShow(Object... args);
}
