package com.kaolafm.kradio.upgrader.net.model;

import java.util.List;

/**
 * <AUTHOR> on 2019/4/1.
 */
public class QueryVersionData {


    /**
     * msg : success
     * code : 01
     * data : {"appName":"考拉","packgeName":"com.edog.car","versionName":"1.0.1","versionCode":100010025,"versionDescription":"考拉","upgradeContent":"考拉","upgradeStyle":1,"packageSize":7979,"downloadUrl":"http://testupgrade.byd.com:8055/582b19a2a5234e3d82fcdd4ff51b6245.apk","timeInterval":0,"preloadVehicles":[{"modelSeries":"1","modelName":"58","padVersion":"1.0"},{"modelSeries":"1","modelName":"70","padVersion":"1.0"},{"modelSeries":"1","modelName":"67","padVersion":"1.0"},{"modelSeries":"1","modelName":"43","padVersion":"1.0"},{"modelSeries":"1","modelName":"51","padVersion":"1.0"},{"modelSeries":"1","modelName":"47","padVersion":"1.0"},{"modelSeries":"1","modelName":"55","padVersion":"1.0"},{"modelSeries":"1","modelName":"54","padVersion":"1.0"},{"modelSeries":"1","modelName":"23","padVersion":"1.0"},{"modelSeries":"1","modelName":"11","padVersion":"1.0"},{"modelSeries":"1","modelName":"32","padVersion":"1.0"},{"modelSeries":"1","modelName":"41","padVersion":"1.0"},{"modelSeries":"1","modelName":"48","padVersion":"1.0"},{"modelSeries":"1","modelName":"46","padVersion":"1.0"},{"modelSeries":"1","modelName":"49","padVersion":"1.0"},{"modelSeries":"1","modelName":"29","padVersion":"1.0"},{"modelSeries":"1","modelName":"42","padVersion":"1.0"},{"modelSeries":"1","modelName":"73","padVersion":"1.0"},{"modelSeries":"1","modelName":"66","padVersion":"1.0"},{"modelSeries":"1","modelName":"88","padVersion":"1.0"},{"modelSeries":"1","modelName":"94","padVersion":"1.0"},{"modelSeries":"1","modelName":"69","padVersion":"1.0"},{"modelSeries":"1","modelName":"62","padVersion":"1.0"},{"modelSeries":"1","modelName":"78","padVersion":"1.0"},{"modelSeries":"1","modelName":"255","padVersion":"1.0"},{"modelSeries":"1","modelName":"82","padVersion":"1.0"},{"modelSeries":"1","modelName":"40","padVersion":"1.0"},{"modelSeries":"1","modelName":"39","padVersion":"1.0"},{"modelSeries":"1","modelName":"37","padVersion":"1.0"},{"modelSeries":"1","modelName":"253","padVersion":"1.0"},{"modelSeries":"1","modelName":"75","padVersion":"1.0"},{"modelSeries":"1","modelName":"254","padVersion":"1.0"},{"modelSeries":"1","modelName":"95","padVersion":"1.0"},{"modelSeries":"1","modelName":"50","padVersion":"1.0"},{"modelSeries":"1","modelName":"85","padVersion":"1.0"}]}
     */

    private String msg;
    private String code;
    private DataBean data;

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public DataBean getData() {
        return data;
    }

    public void setData(DataBean data) {
        this.data = data;
    }

    public static class DataBean {
        /**
         * appName : 考拉
         * packgeName : com.edog.car
         * versionName : 1.0.1
         * versionCode : 100010025
         * versionDescription : 考拉
         * upgradeContent : 考拉
         * upgradeStyle : 1
         * packageSize : 7979
         * downloadUrl : http://testupgrade.byd.com:8055/582b19a2a5234e3d82fcdd4ff51b6245.apk
         * timeInterval : 0
         * preloadVehicles : [{"modelSeries":"1","modelName":"58","padVersion":"1.0"},{"modelSeries":"1","modelName":"70","padVersion":"1.0"},{"modelSeries":"1","modelName":"67","padVersion":"1.0"},{"modelSeries":"1","modelName":"43","padVersion":"1.0"},{"modelSeries":"1","modelName":"51","padVersion":"1.0"},{"modelSeries":"1","modelName":"47","padVersion":"1.0"},{"modelSeries":"1","modelName":"55","padVersion":"1.0"},{"modelSeries":"1","modelName":"54","padVersion":"1.0"},{"modelSeries":"1","modelName":"23","padVersion":"1.0"},{"modelSeries":"1","modelName":"11","padVersion":"1.0"},{"modelSeries":"1","modelName":"32","padVersion":"1.0"},{"modelSeries":"1","modelName":"41","padVersion":"1.0"},{"modelSeries":"1","modelName":"48","padVersion":"1.0"},{"modelSeries":"1","modelName":"46","padVersion":"1.0"},{"modelSeries":"1","modelName":"49","padVersion":"1.0"},{"modelSeries":"1","modelName":"29","padVersion":"1.0"},{"modelSeries":"1","modelName":"42","padVersion":"1.0"},{"modelSeries":"1","modelName":"73","padVersion":"1.0"},{"modelSeries":"1","modelName":"66","padVersion":"1.0"},{"modelSeries":"1","modelName":"88","padVersion":"1.0"},{"modelSeries":"1","modelName":"94","padVersion":"1.0"},{"modelSeries":"1","modelName":"69","padVersion":"1.0"},{"modelSeries":"1","modelName":"62","padVersion":"1.0"},{"modelSeries":"1","modelName":"78","padVersion":"1.0"},{"modelSeries":"1","modelName":"255","padVersion":"1.0"},{"modelSeries":"1","modelName":"82","padVersion":"1.0"},{"modelSeries":"1","modelName":"40","padVersion":"1.0"},{"modelSeries":"1","modelName":"39","padVersion":"1.0"},{"modelSeries":"1","modelName":"37","padVersion":"1.0"},{"modelSeries":"1","modelName":"253","padVersion":"1.0"},{"modelSeries":"1","modelName":"75","padVersion":"1.0"},{"modelSeries":"1","modelName":"254","padVersion":"1.0"},{"modelSeries":"1","modelName":"95","padVersion":"1.0"},{"modelSeries":"1","modelName":"50","padVersion":"1.0"},{"modelSeries":"1","modelName":"85","padVersion":"1.0"}]
         */

        private String appName;
        private String packgeName;
        private String versionName;
        private int versionCode;
        private String versionDescription;
        private String upgradeContent;
        private int upgradeStyle;
        private int packageSize;
        private String downloadUrl;
        private int timeInterval;
        private List<PreloadVehiclesBean> preloadVehicles;

        public String getAppName() {
            return appName;
        }

        public void setAppName(String appName) {
            this.appName = appName;
        }

        public String getPackgeName() {
            return packgeName;
        }

        public void setPackgeName(String packgeName) {
            this.packgeName = packgeName;
        }

        public String getVersionName() {
            return versionName;
        }

        public void setVersionName(String versionName) {
            this.versionName = versionName;
        }

        public int getVersionCode() {
            return versionCode;
        }

        public void setVersionCode(int versionCode) {
            this.versionCode = versionCode;
        }

        public String getVersionDescription() {
            return versionDescription;
        }

        public void setVersionDescription(String versionDescription) {
            this.versionDescription = versionDescription;
        }

        public String getUpgradeContent() {
            return upgradeContent;
        }

        public void setUpgradeContent(String upgradeContent) {
            this.upgradeContent = upgradeContent;
        }

        public int getUpgradeStyle() {
            return upgradeStyle;
        }

        public void setUpgradeStyle(int upgradeStyle) {
            this.upgradeStyle = upgradeStyle;
        }

        public int getPackageSize() {
            return packageSize;
        }

        public void setPackageSize(int packageSize) {
            this.packageSize = packageSize;
        }

        public String getDownloadUrl() {
            return downloadUrl;
        }

        public void setDownloadUrl(String downloadUrl) {
            this.downloadUrl = downloadUrl;
        }

        public int getTimeInterval() {
            return timeInterval;
        }

        public void setTimeInterval(int timeInterval) {
            this.timeInterval = timeInterval;
        }

        public List<PreloadVehiclesBean> getPreloadVehicles() {
            return preloadVehicles;
        }

        public void setPreloadVehicles(List<PreloadVehiclesBean> preloadVehicles) {
            this.preloadVehicles = preloadVehicles;
        }

        public static class PreloadVehiclesBean {
            /**
             * modelSeries : 1
             * modelName : 58
             * padVersion : 1.0
             */

            private String modelSeries;
            private String modelName;
            private String padVersion;

            public String getModelSeries() {
                return modelSeries;
            }

            public void setModelSeries(String modelSeries) {
                this.modelSeries = modelSeries;
            }

            public String getModelName() {
                return modelName;
            }

            public void setModelName(String modelName) {
                this.modelName = modelName;
            }

            public String getPadVersion() {
                return padVersion;
            }

            public void setPadVersion(String padVersion) {
                this.padVersion = padVersion;
            }
        }
    }
}
