package com.kaolafm.kradio.online.mine.order;

/**
 * @Package: com.kaolafm.kradio.comprehensive.user.order
 * @Description:
 * @Author: Maclay
 * @Date: 15:40
 */
public class OrderItemBean {
    /**
     * 订单内容类型，1.专辑：专辑名称、2.单条碎片：专辑名（碎片名称）、3.多条碎片：专辑名（共xx集）、4.VIP会员x个月
     */
    int type;
    String id;
    String name;
    /**
     * 支付方式，1:微信，2:支付宝，3:云币
     */
    int payWay;
    /**
     * 1:macos,2:android,3:其它
     */
    int playClient;
    String createTime;
    /**
     * 专辑封面
     */
    String cover;
    /**
     * 碎片图片
     */
    String img;
    /**
     * 1:待支付,2: 支付成功,3:支付失败,4:重新支付,5:取消订单
     */
    int status;
    String number;
    String finishTime;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getPayWay() {
        return payWay;
    }

    public void setPayWay(int payWay) {
        this.payWay = payWay;
    }

    public int getPlayClient() {
        return playClient;
    }

    public void setPlayClient(int playClient) {
        this.playClient = playClient;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(String finishTime) {
        this.finishTime = finishTime;
    }
}
