<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/message_details_bg">

    <RelativeLayout
        android:id="@+id/root_rl"
        android:layout_width="@dimen/m1050"
        android:layout_height="@dimen/m520"
        tools:background="#9D000000"
        android:layout_centerInParent="true"
        android:background="@drawable/msg_dialog_bg">

        <com.kaolafm.kradio.component.ui.base.view.OvalImageView
            android:id="@+id/dialog_right_half_bg"
            android:layout_width="@dimen/m520"
            android:layout_height="@dimen/m520"
            tools:visibility="visible"
            android:layout_alignParentEnd="true"
            android:scaleType="centerInside"
            app:oval_radius="@dimen/m16" />

        <com.kaolafm.kradio.component.ui.base.view.OvalImageView
            android:id="@+id/dialog_right_half_bg_mask"
            android:src="@drawable/dialog_right_half_bg_mask"
            tools:visibility="visible"
            android:layout_width="@dimen/m520"
            android:layout_height="@dimen/m520"
            android:layout_alignParentEnd="true"
            android:scaleType="centerCrop"
            app:rid_type="5"
            app:oval_radius="@dimen/m16"/>

        <LinearLayout
            android:id="@+id/front_ground_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="@dimen/m16"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/content_ll"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:paddingLeft="@dimen/m24"
                android:paddingRight="@dimen/m24"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/content_left_panel"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="@dimen/m32"
                    android:orientation="vertical">

                    <LinearLayout
                        android:id="@+id/bubbleTitle_ll"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/m40"
                        android:gravity="center_vertical">

                        <ImageView
                            android:id="@+id/bubbleIcon"
                            android:layout_width="@dimen/m68"
                            android:layout_height="@dimen/m68"
                            android:scaleType="centerInside"
                            tools:src="@drawable/message_green_icon" />

                        <TextView
                            android:id="@+id/bubbleTitle"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:maxLines="3"
                            android:ellipsize="end"
                            android:layout_marginStart="@dimen/m12"
                            android:textColor="@color/text_color_7"
                            android:textSize="@dimen/m30"
                            tools:text="路况交通路况交通路况交通路况交通路况交通路况交通路况交通路况交通" />
                    </LinearLayout>

                    <ImageView
                        android:id="@+id/msg_tips_pic_iv"
                        android:src="@drawable/msg_tips_pic"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/m28"
                        android:adjustViewBounds="true"
                        android:layout_marginTop="@dimen/m18"
                        tools:visibility="visible"
                        android:visibility="gone" />

                    <LinearLayout
                        android:id="@+id/time_panel"
                        android:layout_marginTop="@dimen/m16"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical">

                        <TextView
                            android:includeFontPadding="false"
                            android:id="@+id/activity_time_tip"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/text_color_7"
                            android:visibility="gone"
                            android:layout_marginEnd="@dimen/m8"
                            android:textSize="@dimen/m26"
                            android:text="活动时间:" />
                        <TextView
                            android:includeFontPadding="false"
                            android:id="@+id/activity_time"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/text_color_7"
                            android:textSize="@dimen/m26"
                            tools:text="2022.05.31 14:08" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/cd_up_down_panel"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <!-- 隐藏控件，用于所见即可说语音执行滑动操作 -->
                        <TextView
                            android:id="@+id/cd_up"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:layout_height="wrap_content"
                            android:background="@color/transparent"
                            android:contentDescription="@string/content_desc_message_detail_pre"
                            android:text="@string/content_desc_message_detail_pre"
                            android:textColor="@color/transparent"
                            android:textSize="1sp"
                            tools:ignore="SmallSp" />
                        <TextView
                            android:id="@+id/cd_down"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:layout_height="wrap_content"
                            android:background="@color/transparent"
                            android:contentDescription="@string/content_desc_message_detail_next"
                            android:text="@string/content_desc_message_detail_next"
                            android:textColor="@color/transparent"
                            android:textSize="1sp"
                            tools:ignore="SmallSp" />
                    </LinearLayout>

                    <ScrollView
                        android:id="@+id/scroll_view"
                        android:layout_marginTop="@dimen/m24"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginBottom="@dimen/m16"
                        android:scrollbarStyle="outsideOverlay"
                        android:scrollbarThumbVertical="@drawable/sl_sh_sb_thumb"
                        android:scrollbarTrackVertical="@drawable/sl_sh_sb_track"
                        android:scrollbars="vertical">

                        <TextView
                            android:id="@+id/bubbleContent"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:textColor="@color/msg_desc_text_color"
                            android:textSize="@dimen/m24"
                            android:lineSpacingExtra="@dimen/m7"
                            tools:text="前方3公里正在进行道路施工车辆行驶缓慢前方3公里正在进行道路施工车辆行驶缓慢前方3公里正在进行道路施工车辆行驶缓慢前方3公里正在进行道路施工车辆行驶缓慢，请谨慎驾驶谨慎驾驶谨慎驾驶重要事情说请谨慎驾驶谨慎驾驶谨慎驾驶重要事情说" />

                    </ScrollView>

                </LinearLayout>

                <RelativeLayout
                    android:id="@+id/content_right_panel"
                    android:layout_width="@dimen/m224"
                    android:layout_height="match_parent"
                    android:visibility="visible">

                    <View
                        android:layout_marginTop="@dimen/m93"
                        android:background="#7fffffff"
                        android:layout_width="@dimen/m1"
                        android:layout_height="@dimen/m257" />

                    <LinearLayout
                        android:id="@+id/bubble_qr_ll"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentTop="true"
                        android:layout_alignParentStart="true"
                        android:layout_marginStart="@dimen/m33"
                        android:layout_marginTop="@dimen/m93"
                        android:gravity="center"
                        android:orientation="vertical">

                        <com.kaolafm.kradio.component.ui.base.view.OvalImageView
                            android:id="@+id/bubble_qr_iv"
                            android:contentDescription="@string/can_see_can_say_5_3"
                            android:layout_width="@dimen/m192"
                            android:layout_height="@dimen/m192"
                            android:background="@drawable/message_qr_bg_frame"
                            android:scaleType="centerInside"
                            app:oval_radius="@dimen/m10"
                            tools:src="@drawable/message_green_icon" />

                        <TextView
                            android:id="@+id/bubble_qr_desc_tv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/m16"
                            android:text="扫描二维码 参加活动"
                            android:textColor="#cccccc"
                            android:textSize="@dimen/m20" />
                    </LinearLayout>
                </RelativeLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/msg_btn_ll"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/m20"
                android:layout_marginBottom="@dimen/m20"
                android:paddingLeft="@dimen/m24"
                android:paddingRight="@dimen/m24"
                android:gravity="center"
                >

                <TextView
                    android:id="@+id/msg_btn_lift_tv"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="@dimen/m72"
                    android:background="@drawable/message_details_btn_bg"
                    android:gravity="center"
                    android:textColor="@color/message_details_btn_text_color"
                    android:textSize="@dimen/m26"
                    tools:text="按钮一" />

                <View
                    android:id="@+id/sep"
                    android:layout_width="@dimen/m16"
                    android:layout_height="@dimen/m1" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:id="@+id/msg_btn_right_tv"
                    android:layout_height="@dimen/m72"
                    android:background="@drawable/message_details_btn_bg2"
                    android:gravity="center"
                    android:textColor="@color/message_details_btn_text_color"
                    android:textSize="@dimen/m26"
                    tools:text="按钮二" />

                <View
                    android:visibility="gone"
                    android:id="@+id/placeholder1"
                    android:layout_width="@dimen/m16"
                    android:layout_height="@dimen/m1" />

                <TextView
                    android:visibility="gone"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:id="@+id/placeholder2"
                    android:layout_height="@dimen/m72"
                    android:background="@drawable/message_details_btn_bg2"
                    android:gravity="center"
                    android:textColor="@color/message_details_btn_text_color"
                    android:textSize="@dimen/m26"
                    tools:text="按钮二" />


            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_marginTop="@dimen/m24"
            android:layout_marginEnd="@dimen/m40"
            android:layout_alignParentEnd="true"
            android:layout_alignParentTop="true"
            android:id="@+id/replay_panel"
            android:background="@drawable/message_detail_replay_panel_bg"
            android:layout_width="@dimen/m124"
            android:enabled="false"
            android:gravity="center"
            android:layout_height="@dimen/m38" >
            <View
                android:enabled="false"
                android:id="@+id/replay_panel_state_icon"
                android:background="@drawable/message_detail_replay_state_icon_playing"
                android:layout_width="@dimen/m24"
                android:layout_height="@dimen/m24" />

            <TextView
                android:layout_marginStart="@dimen/m6"
                android:id="@+id/play_iv"
                android:enabled="false"
                android:contentDescription="@string/message_bubble_replay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/message_replay_btn_text_color_state_enable"
                android:textSize="@dimen/m20"
                android:text="播报中" />
        </LinearLayout>

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/pic_big_rl"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/message_details_bg"
        android:visibility="gone">

        <ImageView
            android:id="@+id/pic_big_iv"
            android:layout_width="@dimen/m888"
            android:layout_height="@dimen/m500"
            android:scaleType="fitCenter"
            android:layout_centerInParent="true" />

        <!-- 隐藏控件，用于所见即可说语音执行关闭操作 -->
        <TextView
            android:id="@+id/cd_close_pic_big"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@android:color/transparent"
            android:contentDescription="@string/content_desc_close_dialog"
            android:text="@string/content_desc_close_dialog"
            android:textColor="@android:color/transparent"
            android:layout_toEndOf="@id/pic_big_iv"
            android:layout_alignTop="@id/pic_big_iv"
            android:textSize="1sp"
            />
    </RelativeLayout>

    <!--  活动报名  -->
    <RelativeLayout
        android:id="@+id/activity_register_big_rl"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone">

        <RelativeLayout
            android:background="@drawable/msg_dialog_bg"
            android:id="@+id/ok_ll"
            android:layout_width="@dimen/m504"
            android:layout_height="@dimen/m504"
            android:layout_centerInParent="true">
            <LinearLayout
                android:layout_centerInParent="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/m35"
                android:layout_marginBottom="@dimen/m63"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="visible">

                <com.kaolafm.kradio.component.ui.base.view.OvalImageView
                    android:padding="@dimen/m16"
                    android:id="@+id/ok_qr_iv"
                    android:layout_width="@dimen/m280"
                    android:layout_height="@dimen/m280"
                    android:background="@drawable/message_qr_bg"
                    android:scaleType="centerInside"
                    tools:src="@drawable/message_green_icon" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/m20"
                    android:gravity="center"
                    android:text="请使用手机扫码参与\n活动并查看详情"
                    android:textColor="@color/text_color_7"
                    android:textSize="@dimen/m24" />
            </LinearLayout>
        </RelativeLayout>

    </RelativeLayout>

    <!-- 隐藏控件，用于所见即可说语音执行关闭操作 -->
    <TextView
        android:id="@+id/cd_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        android:contentDescription="@string/content_desc_close_dialog"
        android:text="@string/content_desc_close_dialog"
        android:textColor="@android:color/transparent"
        android:layout_toEndOf="@id/root_rl"
        android:layout_alignTop="@id/root_rl"
        android:textSize="1sp"
        />
    <include
        android:id="@+id/loading"
        layout="@layout/refresh_center"
        android:layout_width="@dimen/m1000"
        android:layout_height="@dimen/m365"
        android:layout_centerInParent="true"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</RelativeLayout>