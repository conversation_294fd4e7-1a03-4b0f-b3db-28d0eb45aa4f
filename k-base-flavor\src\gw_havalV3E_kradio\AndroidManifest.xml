<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.kaolafm.kradio.flavor">

    <application>
        <service
            android:name="com.kaolafm.kradio.auto.appwidget.WidgetService"
            android:enabled="true"
            android:exported="true"></service>

        <receiver android:name="com.kaolafm.kradio.auto.appwidget.KLAppWidgetOne">
            <intent-filter android:priority="1000">
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.kaolafm.auto.home.appExit.action" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/appwidget_provider_one" />
        </receiver>
        <receiver android:name="com.kaolafm.kradio.auto.appwidget.KLAppWidgetTwo">
            <intent-filter android:priority="1000">
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.kaolafm.auto.home.appExit.action" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/appwidget_provider_two" />
        </receiver>
        <receiver android:name="com.kaolafm.kradio.auto.appwidget.KLAppWidgetThree">
            <intent-filter android:priority="1000">
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.kaolafm.auto.home.appExit.action" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/appwidget_provider_three" />
        </receiver>
        <receiver android:name="com.kaolafm.kradio.auto.appwidget.KLAppWidgetFour">
            <intent-filter android:priority="1000">
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.kaolafm.auto.home.appExit.action" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/appwidget_provider_four" />
        </receiver>

        <receiver android:name="com.kaolafm.kradio.receiver.BootBroadcastReceiver">
            <intent-filter android:priority="1000">
                <action android:name="aptiv.intent.action.stage.first" />
            </intent-filter>
        </receiver>

        <receiver android:name="com.kaolafm.kradio.receiver.ShutdownBroadcastReceiver">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.ACTION_SHUTDOWN" />
            </intent-filter>
        </receiver>

        <service
            android:name="com.kaolafm.kradio.flavor.service.AutoPlayService"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com.kaolafm.auto.flavor.service.AUTO_PLAY" />
            </intent-filter>
        </service>

        <service android:name="com.kaolafm.media.session.KRMediaButtonService">
            <intent-filter>
                <action android:name="android.intent.action.MEDIA_BUTTON" />
            </intent-filter>
        </service>

        <receiver android:name=".receiver.BootCompleteReceiver">
            <intent-filter>
                <!--注册开机广播地址-->
                <action android:name="aptiv.intent.action.stage.first" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </receiver>
    </application>
</manifest>


