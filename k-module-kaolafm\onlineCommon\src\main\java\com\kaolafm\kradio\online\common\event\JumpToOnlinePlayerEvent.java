package com.kaolafm.kradio.online.common.event;


/**
 * eventBus事件
 * 通知跳转到播放页
 */
public class JumpToOnlinePlayerEvent {
    /**
     * 如果正在播放直播节目，则该字段应有合法值
     * 如果正在播放的不是直播，则为0
     */
    private long livingProgramId;

    private boolean isDelay = false;

    public static final long PROGRAM_NOT_SET = 0;

    public JumpToOnlinePlayerEvent(long livingProgramId) {
       this(livingProgramId, false);
    }

    public JumpToOnlinePlayerEvent(long livingProgramId, boolean isDelay) {
        this.livingProgramId = livingProgramId;
        this.isDelay = isDelay;
    }

    public long getLivingProgramId() {
        return livingProgramId;
    }

    public boolean isDelay() {
        return isDelay;
    }
}
