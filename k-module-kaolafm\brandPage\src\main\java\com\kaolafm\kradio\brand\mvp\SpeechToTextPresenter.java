package com.kaolafm.kradio.brand.mvp;

import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.os.SystemClock;
import android.util.Log;
import com.alibaba.fastjson.JSON;
import com.kaolafm.kradio.brand.audiorecorder.AudioRecorder;
import com.kaolafm.kradio.brand.audiorecorder.RecordStreamListener;
import com.kaolafm.kradio.brand.audiorecorder.AudioRecorder.RecordingStateCallback;
import com.kaolafm.kradio.brand.audiorecorder.AudioRecorder.Status;
import com.kaolafm.kradio.common.http.CommonRequestParamsUtil;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.kradio.lib.base.mvp.IView;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.opensdk.api.speech2text.model.SpeechSocketMessageSender;
import com.kaolafm.opensdk.api.speech2text.model.SpeechToTextError;
import com.kaolafm.opensdk.api.speech2text.model.SpeechToTextResult;
import com.kaolafm.opensdk.api.speech2text.model.SpeechSocketMessageSender.StartSendMessageParams;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.socket.ConnectErrorListener;
import com.kaolafm.opensdk.socket.ConnectLostListener;
import com.kaolafm.opensdk.socket.SocketApiConstants;
import com.kaolafm.opensdk.socket.SocketListener;
import com.kaolafm.opensdk.socket.SocketManager;
import java.io.File;
import java.util.Map;
import java.util.UUID;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public final class SpeechToTextPresenter extends BasePresenter implements RecordingStateCallback {
   private final String TAG;
   //录音机对象
   private AudioRecorder mAudioRecorder;
   private long mRecordStartTime;
   private int mRecordDuration;
   private final int RECORD_MINIMUM_TIME;
   //长连接发送数据时的Key
   private final String PARAM_NAME_SPEECH_START;
   private final String PARAM_NAME_SPEECH_VOICE;
   private final String PARAM_NAME_SPEECH_END;
   private String mTaskId;
   private final String AUDIO_FILE_NAME;
   private final Handler mMainHandler;
   private SocketListener mSocketResultListener;
   private SocketListener mSocketErrorListener;
   private SpeechToTextPresenter.SpeechVoiceSendListener mVoiceSendSocketListener;
   private SocketListener mVoiceSendStartSocketListener;
   private SocketListener mVoiceSendEndSocketListener;

   public final void startRecord() {
      try {
         this.mAudioRecorder.createDefaultAudio(this.createNewAudioFile(), this.AUDIO_FILE_NAME);
         AudioRecorder.getInstance().startRecord((RecordStreamListener)(new RecordStreamListener() {
            @Override
            public final void recordOfByte(byte[] data, int begin, int end) {
               if (SpeechToTextPresenter.this.mVoiceSendSocketListener == null) {
                  SpeechToTextPresenter.this.mVoiceSendSocketListener = SpeechToTextPresenter.this.new SpeechVoiceSendListener();
               }

               SpeechToTextPresenter.SpeechVoiceSendListener var10000 = SpeechToTextPresenter.this.mVoiceSendSocketListener;
               var10000.setData(data);
               SocketManager.getInstance().setMap((Map)CommonRequestParamsUtil.getCommonParams()).setSocketHost(SocketApiConstants.SOCKET_HOST).request((SocketListener)SpeechToTextPresenter.this.mVoiceSendSocketListener);
            }
         }));
         this.mRecordStartTime = SystemClock.elapsedRealtime();
         Log.i(this.TAG, "mKradioRecorderInterface startRecord");
      } catch (Exception var2) {
         var2.printStackTrace();
      }

   }

   public final void stopRecording(boolean fromUser) {
      boolean stop = false;
      AudioRecorder var10000 = AudioRecorder.getInstance();
      if (var10000.getStatus() == Status.STATUS_START) {
         AudioRecorder.getInstance().stopRecord();
         this.mRecordDuration = (int)(SystemClock.elapsedRealtime() - this.mRecordStartTime);
         Log.i(this.TAG, "mKradioRecorderInterface stopRecord");
         stop = true;
      }

      Log.i(this.TAG, "mKradioRecorderInterface stopRecord status:" + stop);
      if (fromUser && this.mRecordDuration <= this.RECORD_MINIMUM_TIME) {
         this.notifyRecordDurationTooShort();
      }

   }

   // $FF: synthetic method
   public static void stopRecording$default(SpeechToTextPresenter var0, boolean var1, int var2, Object var3) {
      if ((var2 & 1) != 0) {
         var1 = false;
      }

      var0.stopRecording(var1);
   }
@Override
   public void destroy() {
      if (this.mAudioRecorder.getStatus() == Status.STATUS_START) {
         this.mAudioRecorder.stopRecord();
      } else {
         this.mAudioRecorder.cancel();
      }

      if (this.mVoiceSendStartSocketListener != null) {
         SocketManager.getInstance().removeListener(this.mVoiceSendStartSocketListener);
      }

      if (this.mVoiceSendSocketListener != null) {
         SocketManager.getInstance().removeListener((SocketListener)this.mVoiceSendSocketListener);
      }

      if (this.mVoiceSendEndSocketListener != null) {
         SocketManager.getInstance().removeListener(this.mVoiceSendEndSocketListener);
      }

      super.destroy();
   }

   public final boolean isRecording() {
      return this.mAudioRecorder.getStatus() == Status.STATUS_START || this.mAudioRecorder.getStatus() == Status.STATUS_PAUSE;
   }

   public final boolean canRelease() {
      return this.mAudioRecorder.getStatus() != Status.STATUS_NO_READY;
   }
@Override
   public void onRecordingStarted() {
      this.notifyStartRecord();
      if (this.mVoiceSendStartSocketListener == null) {
         this.mVoiceSendStartSocketListener = (SocketListener)(new SocketListener<SpeechSocketMessageSender>() {
            @NotNull
            @Override
            public String getEvent() {
               return "speechStart";
            }

            @Nullable
            @Override
            public Map getParams(@Nullable Map params) {
               SpeechToTextPresenter.this.mTaskId = UUID.randomUUID().toString();
               SpeechSocketMessageSender var3 = new SpeechSocketMessageSender();
               var3.setTaskId(SpeechToTextPresenter.this.mTaskId);
               var3.setMessageId(UUID.randomUUID().toString());
               StartSendMessageParams var6 = new StartSendMessageParams();
               var6.setFormat("pcm");
               var6.setSampleRate(SpeechToTextPresenter.this.mAudioRecorder.getAudioSampleRate());
               var3.setStartSendMessageParams(var6);
               if (params != null) {
                  params.put(SpeechToTextPresenter.this.PARAM_NAME_SPEECH_START, var3);
               }

               return params;
            }
            @Override
            public boolean isNeedRequest() {
               return true;
            }
            @Override
            public boolean isNeedParams() {
               return true;
            }

            @Override
            public void onError(@Nullable ApiException p0) {
               if (p0 != null) {
                  p0.printStackTrace();
               }

               SpeechToTextPresenter var10000 = SpeechToTextPresenter.this;
               SpeechToTextError var2 = new SpeechToTextError();
               SpeechToTextPresenter var5 = var10000;
               var2.setErrorCode("-1");
               var2.setErrMsg("语音转换失败");
               var5.notifyRecordingError(var2);
            }
            @Override
            public void onSuccess(@Nullable SpeechSocketMessageSender p0) {
               Logger.d(SpeechToTextPresenter.this.TAG, "开始上传音频二进制：" + JSON.toJSONString(p0));
            }
         });
      }

      SocketManager.getInstance().setMap((Map)CommonRequestParamsUtil.getCommonParams()).setSocketHost(SocketApiConstants.SOCKET_HOST).request(this.mVoiceSendStartSocketListener);
      if (this.mSocketResultListener == null) {
         this.mSocketResultListener = (SocketListener)(new SocketListener<SpeechToTextResult>() {
            @NotNull
            @Override
            public String getEvent() {
               return "speechVoiceResult";
            }

            @Nullable
            @Override
            public Map getParams(@Nullable Map p0) {
               return p0;
            }
            @Override
            public boolean isNeedRequest() {
               return false;
            }
            @Override
            public void onError(@Nullable ApiException p0) {
               if (p0 != null) {
                  p0.printStackTrace();
               }

            }
            @Override
            public boolean isNeedParams() {
               return false;
            }

            @Override
            public void onSuccess(@Nullable SpeechToTextResult result) {
               if (result != null && !StringUtil.isEmpty(result.getTaskId())) {
                  SpeechToTextPresenter.this.notifySpeechResultReceived(result);
               }
            }
         });
      }

      SocketManager.getInstance().addListener(this.mSocketResultListener);
      if (this.mSocketErrorListener == null) {
         this.mSocketErrorListener = (SocketListener)(new SocketListener<SpeechToTextError>() {
            @NotNull
            @Override
            public String getEvent() {
               return "speechVoiceResult";
            }

            @Nullable
            @Override
            public Map getParams(@Nullable Map p0) {
               return p0;
            }
            @Override
            public boolean isNeedRequest() {
               return false;
            }
            @Override
            public void onError(@Nullable ApiException p0) {
            }
            @Override
            public boolean isNeedParams() {
               return false;
            }

            @Override
            public void onSuccess(@Nullable SpeechToTextError result) {
               if (result != null && !StringUtil.isEmpty(result.getTaskId()) && (result.getErrorCode() != null || result.getErrMsg() != null)) {
                  Logger.d(SpeechToTextPresenter.this.TAG, "语音转换文本失败：" + JSON.toJSONString(result));
                  SpeechToTextPresenter.this.notifyRecordingError(result);
                  SpeechToTextPresenter.stopRecording$default(SpeechToTextPresenter.this, false, 1, (Object)null);
               }
            }
         });
      }

      SocketManager.getInstance().addListener(this.mSocketErrorListener);
      SocketManager.getInstance().registerConnectErrorListener((ConnectErrorListener)(new ConnectErrorListener() {
         @Override
         public final void onConnectError(Object[] it) {
            SpeechToTextPresenter.this.notifyNetWordError(it);
         }
      }));
      SocketManager.getInstance().registerConnectLostListener((ConnectLostListener)(new ConnectLostListener() {
         @Override
         public final void onConnectLost(Object[] it) {
            SpeechToTextPresenter.this.notifyNetWordError(it);
         }
      }));
   }
   @Override
   public void onRecordingPaused() {
   }
   @Override
   public void onRecordingStopped() {
      if (this.mVoiceSendEndSocketListener == null) {
         this.mVoiceSendEndSocketListener = (SocketListener)(new SocketListener<SpeechSocketMessageSender>() {
            @NotNull
            @Override
            public String getEvent() {
               return "speechEnd";
            }

            @Nullable
            @Override
            public Map getParams(@Nullable Map params) {
               SpeechSocketMessageSender var3 = new SpeechSocketMessageSender();
               var3.setTaskId(SpeechToTextPresenter.this.mTaskId);
               var3.setMessageId(UUID.randomUUID().toString());
               if (params != null) {
                  params.put(SpeechToTextPresenter.this.PARAM_NAME_SPEECH_END, var3);
               }

               return params;
            }
            @Override
            public boolean isNeedRequest() {
               return true;
            }
            @Override
            public boolean isNeedParams() {
               return true;
            }
            @Override
            public void onError(@Nullable ApiException p0) {
               SpeechToTextPresenter.stopRecording$default(SpeechToTextPresenter.this, false, 1, (Object)null);
            }
            @Override
            public void onSuccess(@Nullable SpeechSocketMessageSender p0) {
               Logger.d(SpeechToTextPresenter.this.TAG, "停止上传音频二进制：" + JSON.toJSONString(p0));
            }
         });
      }

      SocketManager.getInstance().setMap((Map)CommonRequestParamsUtil.getCommonParams()).setSocketHost(SocketApiConstants.SOCKET_HOST).request(this.mVoiceSendEndSocketListener);
      SocketManager.getInstance().registerConnectErrorListener((ConnectErrorListener)null);
      SocketManager.getInstance().registerConnectLostListener((ConnectLostListener)null);
      if (this.mSocketResultListener != null) {
         SocketManager.getInstance().removeListener(this.mSocketResultListener);
      }

      if (this.mSocketErrorListener != null) {
         SocketManager.getInstance().removeListener(this.mSocketErrorListener);
      }

      this.notifyStopRecord();
   }
@Override
   public void onRecordingCanceled() {
   }

   private final String createNewAudioFile() {
      AppDelegate var10002 = AppDelegate.getInstance();
      File file = new File(var10002.getContext().getExternalFilesDir(Environment.DIRECTORY_MUSIC), "K-Radio-tts-Record");
      String var10000 = file.getAbsolutePath();
      return var10000;
   }

   private final void notifyStartRecord() {
      this.mMainHandler.post((Runnable)(new Runnable() {
         @Override
         public final void run() {
            IRecordAudioView var10000 = SpeechToTextPresenter.access$getMView$p(SpeechToTextPresenter.this);
            if (var10000 != null) {
               var10000.onRecordingStarted();
            }

         }
      }));
   }

   private final void notifyStopRecord() {
      this.mMainHandler.post((Runnable)(new Runnable() {
         @Override
         public final void run() {
            IRecordAudioView var10000 = SpeechToTextPresenter.access$getMView$p(SpeechToTextPresenter.this);
            if (var10000 != null) {
               var10000.onRecordingStopped();
            }

         }
      }));
   }

   private final void notifyRecordingError(final SpeechToTextError error) {
      this.mMainHandler.post((Runnable)(new Runnable() {
         @Override
         public final void run() {
            IRecordAudioView var10000 = SpeechToTextPresenter.access$getMView$p(SpeechToTextPresenter.this);
            if (var10000 != null) {
               var10000.onRecordingError(error);
            }

         }
      }));
   }

   private final void notifyRecordDurationTooShort() {
      this.mMainHandler.post((Runnable)(new Runnable() {
         @Override
         public final void run() {
            IRecordAudioView var10000 = SpeechToTextPresenter.access$getMView$p(SpeechToTextPresenter.this);
            if (var10000 != null) {
               var10000.onRecordDurationTooShort();
            }

         }
      }));
   }

   private final void notifySpeechResultReceived(final SpeechToTextResult result) {
      this.mMainHandler.post((Runnable)(new Runnable() {
         @Override
         public final void run() {
            Logger.d(SpeechToTextPresenter.this.TAG, "语音转换文本结果：" + JSON.toJSONString(result));
            IRecordAudioView var10000 = SpeechToTextPresenter.access$getMView$p(SpeechToTextPresenter.this);
            if (var10000 != null) {
               var10000.onSpeechResultReceived(result);
            }

         }
      }));
   }

   private final void notifyNetWordError(final Object[] args) {
      this.mMainHandler.post((Runnable)(new Runnable() {
         @Override
         public final void run() {
            IRecordAudioView var10000 = SpeechToTextPresenter.access$getMView$p(SpeechToTextPresenter.this);
            if (var10000 != null) {
               var10000.onNetWordError(args);
            }

         }
      }));
   }

   public SpeechToTextPresenter(@Nullable IRecordAudioView view) {
      super((IView)view);
      String var10001 = this.getClass().getSimpleName();
      this.TAG = var10001;
      AudioRecorder var2 = AudioRecorder.getInstance();
      this.mAudioRecorder = var2;
      this.RECORD_MINIMUM_TIME = 1000;
      this.PARAM_NAME_SPEECH_START = "speechStart";
      this.PARAM_NAME_SPEECH_VOICE = "speechVoice";
      this.PARAM_NAME_SPEECH_END = "speechEnd";
      this.AUDIO_FILE_NAME = "tts_audio";
      this.mMainHandler = new Handler(Looper.getMainLooper());
      this.mAudioRecorder.setRecordingStateCallback((RecordingStateCallback)this);
   }

   // $FF: synthetic method
   public static final void access$setMAudioRecorder$p(SpeechToTextPresenter $this, AudioRecorder var1) {
      $this.mAudioRecorder = var1;
   }

   // $FF: synthetic method
   public static final IRecordAudioView access$getMView$p(SpeechToTextPresenter $this) {
      return (IRecordAudioView)$this.mView;
   }

   // $FF: synthetic method
   public static final void access$setMView$p(SpeechToTextPresenter $this, IRecordAudioView var1) {
      $this.mView = var1;
   }
   public final class SpeechVoiceSendListener implements SocketListener {
      private byte[] data;

      public final void setData(@NotNull byte[] newData) {
         this.data = newData;
      }

      @NotNull
      @Override
      public String getEvent() {
         return "speechVoice";
      }

      @Nullable
      @Override
      public Map getParams(@Nullable Map params) {
         SpeechSocketMessageSender var3 = new SpeechSocketMessageSender();
         var3.setTaskId(SpeechToTextPresenter.this.mTaskId);
         var3.setMessageId(UUID.randomUUID().toString());
         var3.setVoice(this.data);
         if (params != null) {
            params.put(SpeechToTextPresenter.this.PARAM_NAME_SPEECH_VOICE, var3);
         }

         return params;
      }

      @Override
      public boolean isNeedRequest() {
         return true;
      }
      @Override
      public boolean isNeedParams() {
         return true;
      }
      @Override
      public void onSuccess(@NotNull Object p0) {
         Logger.d(SpeechToTextPresenter.this.TAG, "上传音频二进制：" + JSON.toJSONString(p0));
      }
      @Override
      public void onError(@Nullable ApiException p0) {
         if (p0 != null) {
            p0.printStackTrace();
         }

         SpeechToTextPresenter.stopRecording$default(SpeechToTextPresenter.this, false, 1, (Object)null);
      }
   }
}
