package com.kaolafm.kradio.lib.base.flavor;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-08-06 17:53
 ******************************************/
public interface KRadioWindowFlagInter {
    /**
     * 为系统窗口添加Flag
     *
     * @param args args[0] = context, args[1....] = flags
     * @return
     */
    boolean addActivityWindowFlags(Object... args);
}
