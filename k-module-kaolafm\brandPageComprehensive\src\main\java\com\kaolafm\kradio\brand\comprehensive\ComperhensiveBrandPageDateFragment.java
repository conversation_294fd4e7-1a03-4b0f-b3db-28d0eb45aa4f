package com.kaolafm.kradio.brand.comprehensive;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewStub;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.LayoutAnimationController;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.activity.comprehensive.ui.ActivitysDetailsDialogFragment;
import com.kaolafm.kradio.brand.mvp.BrandPagePresenter;
import com.kaolafm.kradio.brand.mvp.IBrandPageView;
import com.kaolafm.kradio.brand.mvp.TopicModel;
import com.kaolafm.kradio.common.SkinStateManager;
import com.kaolafm.kradio.common.widget.GridRecyclerView;
import com.kaolafm.kradio.component.ui.brandpage.ComponentBrandPageCardCell;
import com.kaolafm.kradio.config.ConfigSettingManager;
import com.kaolafm.kradio.lib.base.ui.BaseViewPagerFragment;
import com.kaolafm.kradio.lib.report.ReportUtil;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.kradio.common.utils.ThreadUtil;
import com.kaolafm.kradio.component.ui.activitycard.ComponentActivityCell;
import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.component.ui.base.utils.ComponentUtils;
import com.kaolafm.kradio.component.ui.bigcard.ComponentBigCardCell;
import com.kaolafm.kradio.component.ui.bigcard.ComponentBrandPageBigCardCell;
import com.kaolafm.kradio.component.ui.brandpage.carownerradio.CarOwnerRadio;
import com.kaolafm.kradio.component.ui.k_1x1card.Component1And1Cell;
import com.kaolafm.kradio.component.ui.k_1x1card.ComponentBrandPage1And1Cell;
import com.kaolafm.kradio.component.ui.k_2x1card.Component2And1Cell;
import com.kaolafm.kradio.component.ui.k_2x3card.Component2And3Cell;
import com.kaolafm.kradio.component.ui.rotationcard.ComponentRotationCell;
import com.kaolafm.kradio.component.ui.topiccard.ComponentTopicCardCell;
import com.kaolafm.kradio.home.comprehensive.JumpToFragentEvent;
import com.kaolafm.kradio.home.comprehensive.ad.KradioAdColumnManager;
import com.kaolafm.kradio.home.comprehensive.adapter.HomeAdapter;
import com.kaolafm.kradio.home.comprehensive.gallery.PageJumper;
import com.kaolafm.kradio.home.comprehensive.item.FunctionPairCell;
import com.kaolafm.kradio.home.comprehensive.mvp.HomeListPresenter;
import com.kaolafm.kradio.home.comprehensive.mvp.IHomeListView;
import com.kaolafm.kradio.home.comprehensive.playerbar.ComprehensivePlayerHelper;
import com.kaolafm.kradio.home.comprehensive.recyclerview.HomeRecyclerViewHelper;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioAuthInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioC211ViewSizeInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioClickRetryInter;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.common.ResType;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.ClazzUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.RecyclerViewExposeUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.utils.YTLogUtil;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.player.router.RouterDesturlPlayCallbackImpl;
import com.kaolafm.kradio.user.UserInfoManager;
import com.kaolafm.opensdk.api.brandpage.model.BrandPageListBean;
import com.kaolafm.opensdk.api.config.ConfigSettingOption;
import com.kaolafm.opensdk.api.config.IConfigSettingOptionListener;
import com.kaolafm.opensdk.api.live.model.LiveInfoDetail;
import com.kaolafm.opensdk.api.operation.model.ImageFile;
import com.kaolafm.opensdk.api.operation.model.column.ActivityDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.AlbumDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.ColumnContent;
import com.kaolafm.opensdk.api.operation.model.column.LiveProgramDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.TopicDetailColumnMember;
import com.kaolafm.opensdk.api.topic.TopicRequest;
import com.kaolafm.opensdk.api.topic.model.OperationResponse;
import com.kaolafm.opensdk.api.topic.model.TopicPosts;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.IGeneralListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayerStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.UIThreadUtil;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.util.ReportConstants;
import com.lcodecore.tkrefreshlayout.utils.ScrollingUtil;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


import static com.kaolafm.kradio.component.ui.brandpage.carownerradio.CarOwnerRadioEntrance.BROADCAST_RECEIVER_ACTION_CAR_ENTER_ANIMATION_COMPLETE;

/**
 * 首页列表展示
 * 蔡佳彬
 */
public class ComperhensiveBrandPageDateFragment extends BaseViewPagerFragment<HomeListPresenter>
        implements IHomeListView, RecyclerViewExposeUtil.OnItemExposeListener, IBrandPageView {

    private String TAG = "ComperhensiveBrandPageDateFragment";

    LinearLayout home_root_cl;
    View mPcLoadingView;
    ViewStub mVsHomeNoNetwork;

    View mHomeNoNetWorkRl;

    private String zone = "";
    private List<RecyclerViewExposeUtil> mExposeRvUtilList = new ArrayList<>();
    private KRadioAuthInter mKRadioAuthInter;
    public List<HomeAdapter> mHomeAdapterList = new ArrayList<>();
    private SkinStateManager.ILoadSkinListener loadSkinListener;
    private List<HomeRecyclerViewHelper> mHomeRecyclerViewHelperList = new ArrayList<>();
    private List<HomeCell> mHomeCells = new ArrayList<>();
    private List<GridRecyclerView> recyclerViewList = new ArrayList<>();
    private List<HomeCell> brandPageCell = new ArrayList<>();
    private boolean isAutoPlay;//是都需要自动播放
    private boolean isHaveBrandPageCell = false;//是否有光圈组件

    private BrandPagePresenter mBrandPagePresenter;

    private boolean isFirstResume = true;

    private RouterDesturlPlayCallbackImpl routerDesturlPlayCallback = new RouterDesturlPlayCallbackImpl();

    private IPlayerStateListener mPlayerListener = new IPlayerStateListener() {
        @Override
        public void onIdle(PlayItem playItem) {
            updateBrandLiveLottie(playItem, PLAY_STATE_TYPE_IDLE);
        }

        @Override
        public void onPlayerPreparing(PlayItem playItem) {
            updatePlayState(playItem);
            hideLoading();
            if (needJumpToPage) {
                if (TextUtils.isEmpty(destUrl)) {
                    if (playItem.getType() == PlayerConstants.RESOURCES_TYPE_LIVING) {
                        if (activity instanceof ComprehensiveBrandPageActivity) {
                            ((ComprehensiveBrandPageActivity) activity).jumpToPlayerPage();
                        }
                    }
                } else {
                    jumpPage();
                }
                needJumpToPage = false;
            }
        }

        @Override
        public void onPlayerPreparingComplete(PlayItem playItem) {

        }

        @Override
        public void onPlayerPlaying(PlayItem playItem) {
            hideLoading();
            updatePlayState(playItem);

            updateBrandLiveLottie(playItem, PLAY_STATE_TYPE_PLAYING);
        }

        @Override
        public void onPlayerPaused(PlayItem playItem) {
            hideLoading();
            updatePlayState(playItem);
        }

        @Override
        public void onProgress(PlayItem playItem, long l, long l1) {

        }

        @Override
        public void onPlayerFailed(PlayItem playItem, int i, int i1) {
            hideLoading();
            updatePlayState(playItem);
            hideErrorLayout();
        }

        @Override
        public void onPlayerEnd(PlayItem playItem) {
            hideLoading();
            updatePlayState(playItem);
        }

        @Override
        public void onSeekStart(PlayItem playItem) {

        }

        @Override
        public void onSeekComplete(PlayItem playItem) {

        }

        @Override
        public void onBufferingStart(PlayItem playItem) {

        }

        @Override
        public void onBufferingEnd(PlayItem playItem) {

        }

        @Override
        public void onDownloadProgress(PlayItem playItem, long l, long l1) {

        }
    };
    private Activity activity;
    private boolean needJumpToPage = false;
    private String destUrl;//
    private boolean showItemAnimation = false;  //是否展示item动画


    @Override
    protected boolean isReportFragment() {
        return true;
    }

    @Override
    public String getPageId() {
        return super.getPageId();
    }

    public static ComperhensiveBrandPageDateFragment newInstance() {
        ComperhensiveBrandPageDateFragment fragment = new ComperhensiveBrandPageDateFragment();
        return fragment;
    }

    private boolean isBrandPageCardCellType(HomeCell homeCell){
        return homeCell instanceof ComponentBrandPageCardCell;
    }

    private static final int PLAY_STATE_TYPE_IDLE = 0;
    private static final int PLAY_STATE_TYPE_PLAYING = 1;

    /**
     * 根据播放状态，和brand live自身的是否可播状态，更新lottie播放提示
     * @param currPlayItem 当前正在播放的项目
     * @param type 播放状态
     */
    private void updateBrandLiveLottie(PlayItem currPlayItem, int type){
        if(brandPageCell == null || brandPageCell.size() == 0){
            return;
        }
        if(mHomeAdapterList == null || mHomeAdapterList.size() == 0){
            return;
        }

        HomeCell homeCell = mHomeAdapterList.get(0).getItemData(0);

        if ( !isBrandPageCardCellType(homeCell) ) {
            return;
        }

        ComponentBrandPageCardCell componentBrandPageCardCell = (ComponentBrandPageCardCell) homeCell;
        CarOwnerRadio carOwnerRadio = componentBrandPageCardCell.car_owner_radio;
        if (carOwnerRadio == null) {
            return;
        }
        LiveProgramDetailColumnMember live = carOwnerRadio.getLiveProgramDetailColumnMember();
        if(live == null){   //只处理live的情况，以下处理的都是live
            return;
        }

        if(type == PLAY_STATE_TYPE_IDLE){   //当前播放暂停，表明所有播放动画都要暂停，那么当然brand的lottie也要暂停；如果live又是已结束，则还需要隐藏lottie

            updateLottieToPauseOrHide(carOwnerRadio, live.getLiveStatus());

        } else if(type == PLAY_STATE_TYPE_PLAYING){ //播放状态
            if( !PlayerManagerHelper.getInstance().isSameProgram(currPlayItem, live.getId()) ){   //如果播放的是其他的，则live自身暂停

                updateLottieToPauseOrHide(carOwnerRadio, live.getLiveStatus());

            } else {    //播放Brand自己
                if(live.getLiveStatus() != LiveInfoDetail.STATUS_FINISHED){ //如果自己非完成状态，则播放
                    carOwnerRadio.playLottie();
                } else {    //live是已完成的，直接隐藏播放提示
                    carOwnerRadio.hideLottie();
                }
            }
        }
    }

    private void updateLottieToPauseOrHide(CarOwnerRadio carOwnerRadio, int liveStatus){
        carOwnerRadio.pauseLottie();
        if(liveStatus == LiveInfoDetail.STATUS_FINISHED){
            carOwnerRadio.hideLottie();
        }
    }

    private void updatePlayState(PlayItem playItem) {
        if (brandPageCell.size() > 0) {
            HomeCell homeCell = mHomeAdapterList.get(0).getItemData(0);
            if (homeCell instanceof ComponentBrandPageCardCell) {
                ComponentBrandPageCardCell componentBrandPageCardCell = (ComponentBrandPageCardCell) homeCell;
                CarOwnerRadio mCarOwnerRadioEntrance = componentBrandPageCardCell.car_owner_radio;
                if (mCarOwnerRadioEntrance == null) {
                    return;
                }
                if (mCarOwnerRadioEntrance.haveBrandContent()) {
                    ColumnContent columnContent = mCarOwnerRadioEntrance.getBrandPageCell().getContentList().get(0);
                    if (columnContent.getResType() == ResType.LIVE_TYPE &&
                            PlayerManagerHelper.getInstance().isSameProgram(playItem, columnContent.getId())) {
                        //光圈内容是直播，且当前杠结束的正是光圈对应的直播
                        columnContent.setLiveStatus(playItem.getStatus());
                    }
                }
                mCarOwnerRadioEntrance.updatePlayState();
            }
        }
        if (mHomeAdapterList != null) {
            for (int i = 0; i < mHomeAdapterList.size(); i++) {
                if (brandPageCell.size() == 0 || mHomeAdapterList.size() == 1) {
                    int finalI = i;
                    //https://app.huoban.com/tables/2100000007530121/items/2300001503899968?userId=1874548
                    ThreadUtil.runOnUI(() -> mHomeAdapterList.get(finalI).changePlayingState());
                } else {
                    if (i > 0) {
                        int finalI = i;
                        //https://app.huoban.com/tables/2100000007530121/items/2300001503899968?userId=1874548
                        ThreadUtil.runOnUI(() -> mHomeAdapterList.get(finalI).changePlayingState());
                    }
                }
            }
        }
    }


    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        try {
            mKRadioAuthInter = (KRadioAuthInter) ClazzUtil
                    .invoke(Class.forName(StringUtil.join(ClazzImplUtil.CLASS_FATHER_PACKAGE, "KRadioAuthImpl")),
                            null, "getInstance", new Object[]{});
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mBrandPagePresenter = new BrandPagePresenter(this);
        loadSkinListener = () -> {
            if (mHomeAdapterList != null) {
                for (HomeAdapter homeAdapter : mHomeAdapterList) {
                    homeAdapter.notifyDataSetChanged();
                }
            }
        };
        SkinStateManager.getInstance().addLoadSkinListener(loadSkinListener);
        ConfigSettingManager.getInstance().isPlayAnimation(new ConfigSettingManager.OnResultCallback<Boolean>() {
            @Override
            public void onResult(Boolean aBoolean) {
                ComperhensiveBrandPageDateFragment.this.showItemAnimation = isAutoPlay && aBoolean;
            }
        });
    }

    /**
     * 加载数据
     *
     * @param zone
     */
    public void loadDate(String zone, boolean isAutoPlay) {
        this.zone = zone;
        this.isAutoPlay = isAutoPlay;
    }


    @Override
    protected int getLayoutId() {
        return R.layout.fragment_comperhensive_brand_date;
    }

    @Override
    protected int getLayoutId_Tow() {
        return R.layout.fragment_comperhensive_brand_date_two;
    }

    @Override
    protected HomeListPresenter createPresenter() {
        return new HomeListPresenter(this);
    }

    private String sectionId = "";

    public void setBrandPageInfo(String sectionId) {
        this.sectionId = sectionId;
        this.TAG = "ComperhensiveBrandPageDateFragment_" + sectionId;
    }


    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
        this.activity = activity;
    }

    @Override
    public void onDetach() {
        this.activity = null;
        super.onDetach();
    }

    private boolean isLikeSend = false;//是否正在点赞请求中

    @Override
    public void initView(View view) {

        home_root_cl = view.findViewById(R.id.home_root_cl);
        mPcLoadingView = view.findViewById(R.id.pc_loading);
        mVsHomeNoNetwork = view.findViewById(R.id.vs_home_no_network);

        PlayerManager.getInstance().addGeneralListener(generalListener);
        PlayerManager.getInstance().addPlayControlStateCallback(mPlayerListener);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        Log.i(TAG, "onViewCreated");
        loadData();
    }

    @Override
    public void onResume() {
        super.onResume();
        Log.i(TAG, "onResume --- isFirstResume=" + isFirstResume);
        if (!isFirstResume) {
            updateFragmentLiveStatus("ComperhensiveBrandPageDateFragment -> onResume");
        } else {
            isFirstResume = false;
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        Log.i(TAG, "onPause");
    }

    /**
     * 更新 Fragment 圆形直播间组件状态
     */
    public void updateFragmentLiveStatus(String refPath){
        Log.i(TAG, refPath + " -> updateFragmentLiveStatus");
        boolean haveBrandLive = haveBrandLive();
        Log.i(TAG, "updateFragmentLiveStatus --- haveBrandLive=" + haveBrandLive);
        if (!haveBrandLive){
            return;
        }
        mBrandPagePresenter.getBrandPageLiveStatusData(sectionId, UserInfoManager.getInstance().getUserId(), UserInfoManager.getInstance().getToken());
    }

    private boolean haveBrandLive(){
        return brandPageCell != null
                && brandPageCell.size() > 0
                && brandPageCell.get(0).getContentList() != null
                && brandPageCell.get(0).getContentList().size() > 0
                && brandPageCell.get(0).getContentList().get(0) instanceof LiveProgramDetailColumnMember;
    }

    private void loadData() {
        Log.i(TAG, "loadData");
        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext().getApplicationContext(), true)) {
            if (getContext() != null)
                ToastUtil.showInfo(getContext().getApplicationContext(), ResUtil.getString(R.string.no_net_work_str));
            showError("");
            return;
        }
//        if (zone.equals("BrandPage")) {
        mBrandPagePresenter.getBrandPageContent(sectionId, UserInfoManager.getInstance().getUserId(), UserInfoManager.getInstance().getToken());
//        } else {
//            mPresenter.initData(zone);
//        }
    }

    /**
     * 处理话题组件点赞组件
     */
    private synchronized void topicLikePosts(TopicPosts mTopicPosts, int position) {
        if (isLikeSend) {
            return;
        }
        isLikeSend = true;
        int type;
        if (mTopicPosts.getLikeStatus() == TopicPosts.STATUS_LIKED) {
            type = TopicRequest.POSTS_OPERATE_UNLIKE;
        } else {
            type = TopicRequest.POSTS_OPERATE_LIKE;
        }
        new TopicModel(null).operatePostsLike(mTopicPosts.getPostId(), type, new HttpCallback<OperationResponse>() {
            @Override
            public void onSuccess(OperationResponse likeOperation) {
                if (likeOperation.getStatus() == 1) {
                    for (int i = 0; i < mHomeCells.get(position).getContentList().get(0).getPosts().size(); i++) {
                        if (mHomeCells.get(position).getContentList().get(0).getPosts().get(i).getPostId() == mTopicPosts.getPostId()) {
                            long count = mHomeCells.get(position).getContentList().get(0).getPosts().get(i).getLikeCount();
                            if (type == TopicRequest.POSTS_OPERATE_UNLIKE) {
                                mHomeCells.get(position).getContentList().get(0).getPosts().get(i).setLikeStatus(TopicPosts.STATUS_UNLIKE);
                                mHomeCells.get(position).getContentList().get(0).getPosts().get(i).setLikeCount(--count);
                            } else {
                                mHomeCells.get(position).getContentList().get(0).getPosts().get(i).setLikeStatus(TopicPosts.STATUS_LIKED);
                                mHomeCells.get(position).getContentList().get(0).getPosts().get(i).setLikeCount(++count);
                            }
                            if (mHomeAdapterList != null) {
                                for (HomeAdapter homeAdapter : mHomeAdapterList) {
                                    ThreadUtil.runOnUI(() -> homeAdapter.notifyDataSetChanged());
                                }
                            }
                            break;
                        }
                    }
                } else {
                    if (getContext() != null)
                        ToastUtil.showError(getContext().getApplicationContext(), likeOperation.getMsg());
                }
                isLikeSend = false;
            }

            @Override
            public void onError(ApiException e) {
                if (getContext() != null)
                    ToastUtil.showError(getContext().getApplicationContext(), e.getMessage());
                isLikeSend = false;
            }
        });
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        PlayerManager.getInstance().removeGeneralListener(generalListener);
        PlayerManager.getInstance().removePlayControlStateCallback(mPlayerListener);
        KradioAdColumnManager.getInstance().unBindHomeRecyclerViewHelper();
    }

    /**
     * 打开话题详情
     *
     * @param homeCell
     * @param childposition
     */
    private void jumpToTopicDateils(HomeCell homeCell, int childposition) {
        Bundle bundle = new Bundle();
        bundle.putLong(Constants.ARGUMENT_TOPIC_ID, Long.parseLong(homeCell.contentList.get(childposition).getId()));
        RouterManager.getInstance().jumpPage(RouterConstance.BRAND_TOPIC_DETAIL_URL_COMPREHENSIVE, bundle);
    }

    /**
     * 打开组件活动详情
     *
     * @param homeCell
     */
    private void jumpToActivityDateils(HomeCell homeCell, int childposition) {
        //活动类型的点击
        ActivityDetailColumnMember activityDetailColumnMember = null;
        if (homeCell.getContentList().get(childposition) instanceof ActivityDetailColumnMember) {
            activityDetailColumnMember = (ActivityDetailColumnMember) homeCell.getContentList().get(childposition);
        }
        if (activityDetailColumnMember == null){
            return;
        }
        if (TextUtils.equals(activityDetailColumnMember.getActivityType(), "1")
                && activityDetailColumnMember.getActivityStatus().equals("1")) {
            //不是常驻的活动才可以打开详情
            String id = homeCell.getContentList().get(childposition).getId() + "";
            if (TextUtils.isEmpty(id)) {
                if (getContext() != null)
                    ToastUtil.showInfo(getContext().getApplicationContext(), "数据错误！");
                return;
            }
            ActivitysDetailsDialogFragment dialogFragment
                    = (ActivitysDetailsDialogFragment) new ActivitysDetailsDialogFragment(getContext(), id);
            dialogFragment.show();
//                ButtonClickReportEvent event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_ACTIVITY_PAGE_DATAILS);
//                ReportHelper.getInstance().addEvent(event);

        }
    }

    /**点击了其他Cell，就清空BrandLiving的播放状态*/
    private void clearBrandRateViewAnimIfNeed(HomeCell cell){

        if (cell instanceof ComponentBrandPageCardCell) {   //
            return;
        }


    }

    /**
     * Item的点击事件处理。这里有渠道使用到了AOP进行鉴权拦截。
     * do 首页 item 点击事件处理
     */
    @SuppressLint("LongLogTag")
    private void onListItemClick(View view1, HomeCell homeCell, int position) {
        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext().getApplicationContext(), true)) {
            Log.i(TAG, "fetchSubscriptionData no network");
            if (getContext() != null)
                ToastUtil.showInfo(getContext().getApplicationContext(), ResUtil.getString(R.string.no_net_work_str));
            return;
        }
        if (homeCell instanceof ComponentBrandPageCardCell) {

            if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext().getApplicationContext(), true)) {
//                    Log.i(TAG, "fetchSubscriptionData no network");
                if (getContext() != null)
                    ToastUtil.showInfo(getContext().getApplicationContext(), ResUtil.getString(R.string.no_net_work_str));
                return;
            }
            reportContentClickEvent(brandPageCell.get(0), 0);
            showLoading();
            //如果没有播放,则播放
            ComponentBrandPageCardCell componentBrandPageCardCell = (ComponentBrandPageCardCell) homeCell;
            CarOwnerRadio mCarOwnerRadioEntrance = componentBrandPageCardCell.car_owner_radio;
            if (mCarOwnerRadioEntrance.haveBrandContent()) {
                if (mCarOwnerRadioEntrance.isPlayingBrandContent()) {
                    PlayItem curPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
                    boolean isLivePlayItem = curPlayItem.getType() == PlayerConstants.RESOURCES_TYPE_LIVING;
                    if (isLivePlayItem) {
                        if (!PlayerManagerHelper.getInstance().isPlaying()) {
                            //直播进行中，但是处于暂停状态
                            PlayerManagerHelper.getInstance().play(true);
                        }
                        if (activity instanceof ComprehensiveBrandPageActivity) {
                            ((ComprehensiveBrandPageActivity) activity).jumpToPlayerPage();
                        }
                        hideLoading();
                        return;
                    }
                    //非直播，直接播放
                    if (!PlayerManagerHelper.getInstance().isPlaying())
                        PlayerManagerHelper.getInstance().play(true);
                    hideLoading();
                } else {
                    ColumnContent columnContent = brandPageCell.get(0).getContentList().get(0);
                    if (columnContent instanceof TopicDetailColumnMember) {
                        Bundle bundle = new Bundle();
                        bundle.putLong(Constants.ARGUMENT_TOPIC_ID, Long.parseLong(columnContent.getId()));
                        RouterManager.getInstance().jumpPage(RouterConstance.BRAND_TOPIC_DETAIL_URL_COMPREHENSIVE, bundle);
                        hideLoading();
                        return;
                    } else if (columnContent instanceof ActivityDetailColumnMember) {
                        jumpToActivityDateils(brandPageCell.get(0), 0);
                        hideLoading();
                        return;
                    } else if (columnContent instanceof LiveProgramDetailColumnMember) {
//                        if (columnContent.getLiveStatus() == LiveInfoDetail.STATUS_LIVING) {

                        LiveProgramDetailColumnMember live = (LiveProgramDetailColumnMember)columnContent;
//                        if(live.getLiveStatus() != 0){  //未结束的直播
//                            needJumpToPage = true;
//                            PlayerManagerHelper.getInstance().start(columnContent.getId(), columnContent.getResType());
//                        } else {
//                            ToastUtil.showNormal(getContext().getApplicationContext(), ResUtil.getString(R.string.player_failed_live_finished));
//                        }
                        // 点击圆形组件时，不再判断直播间状态，都走播放跳转逻辑
                        needJumpToPage = true;
                        PlayerManagerHelper.getInstance().start(columnContent.getId(), columnContent.getResType());
                        hideLoading();
//                        }
                    } else {
                        showLoading();
                        brandPageCell.get(0).playId = Long.parseLong(brandPageCell.get(0).contentList.get(0).getId());
                        brandPageCell.get(0).resType = brandPageCell.get(0).contentList.get(0).getResType();
                        clickToPlay(brandPageCell.get(0));
                    }
                }
            }
        } else if (homeCell instanceof Component1And1Cell || homeCell instanceof Component2And1Cell
                || homeCell instanceof Component2And3Cell || homeCell instanceof ComponentRotationCell
                || homeCell instanceof ComponentBigCardCell || homeCell instanceof ComponentBrandPage1And1Cell
                || homeCell instanceof ComponentActivityCell || homeCell instanceof ComponentTopicCardCell
                || homeCell instanceof ComponentBrandPageBigCardCell) {
            if (view1.getTag() == null) {
                return;
            }
            int childposition = (int) view1.getTag();

            if (homeCell.contentList.size() > childposition) {
                reportContentClickEvent(homeCell, childposition);
                //跳转
                if (!TextUtils.isEmpty(homeCell.contentList.get(childposition).getDestUrl())) {
                    if (homeCell.contentList.get(childposition) instanceof ActivityDetailColumnMember) {
                        //活动类型
                        ActivityDetailColumnMember activityDetailColumnMember = null;
                        if (homeCell.getContentList().get(childposition) instanceof ActivityDetailColumnMember) {
                            activityDetailColumnMember = (ActivityDetailColumnMember) homeCell.getContentList().get(childposition);
                        }
                        //如果活动类型是常驻或者已下线就不能跳转
                        if (activityDetailColumnMember == null
                                || activityDetailColumnMember.getActivityType().equals("0")
                                || !activityDetailColumnMember.getActivityStatus().equals("1")) {
                            return;
                        }
                        RouterManager.getInstance().interceptApplicationJumpEvent(getContext(),
                                homeCell.contentList.get(childposition).getDestUrl(), routerDesturlPlayCallback);
                    } else if (homeCell.contentList.get(childposition) instanceof TopicDetailColumnMember) {
                        //话题类型
                        RouterManager.getInstance().interceptApplicationJumpEvent(getContext(),
                                homeCell.contentList.get(childposition).getDestUrl(), routerDesturlPlayCallback);
                    } else {
                        if (homeCell.contentList.get(childposition).getResType() < 0) {
                            return;
                        }
                        homeCell.playId = Long.parseLong(homeCell.contentList.get(childposition).getId());
                        homeCell.resType = homeCell.contentList.get(childposition).getResType();
                        if (PlayerManagerHelper.getInstance().isPlayCurrentRadio(String.valueOf(homeCell.playId))) {
                            destUrl = homeCell.contentList.get(childposition).getDestUrl();
                            jumpPage();
                            return;
                        }
                        showLoading();
                        ReportUtil.addRecommendSelectEvent(homeCell.outputMode, homeCell.callBack);
                        clickToPlay(homeCell);
                        destUrl = homeCell.contentList.get(childposition).getDestUrl();
                        needJumpToPage = true;
                    }
                    return;
                } else {
                    if (homeCell.contentList.get(childposition) instanceof LiveProgramDetailColumnMember) {
                        //直播类型
                        if (mKRadioAuthInter != null && !mKRadioAuthInter.authStatus()) {
                            Log.i(TAG, "onCardClick: mKRadioAuthInter.authStatus() = " + mKRadioAuthInter.authStatus());
                            //如果流量鉴权不通过，进行流量鉴权的check,不要直接跳转到直播界面
                            mKRadioAuthInter.doCheckAuth(homeCell, KRadioAuthInter.METHOD_LIVING, true);
                        } else {
                            clickToPlayLive(homeCell, childposition);
                        }
                        return;
                    } else if (homeCell.contentList.get(childposition) instanceof ActivityDetailColumnMember) {
                        //活动类型
                        jumpToActivityDateils(homeCell, childposition);
                        return;
                    } else if (homeCell.contentList.get(childposition) instanceof TopicDetailColumnMember) {
                        //话题类型
                        jumpToTopicDateils(homeCell, childposition);
                        return;
                    }
                }
                homeCell.playId = Long.parseLong(homeCell.contentList.get(childposition).getId());
                homeCell.resType = homeCell.contentList.get(childposition).getResType();

                Log.i(TAG, "click start : id = " + homeCell.playId);
                if (mKRadioAuthInter != null && !mKRadioAuthInter.authStatus()) {
                    Log.i(TAG,
                            "onCardClick: mKRadioAuthInter.authStatus() = " + mKRadioAuthInter.authStatus());
                    //如果流量鉴权不通过，也要去触发播放
                    clickToPlay(homeCell);
                } else {
                    if (PlayerManagerHelper.getInstance().isPlayCurrentRadio(String.valueOf(homeCell.playId))) {
                        handleClickCurPlayItem(homeCell);
                        return;
                    }
                    ReportUtil.addRecommendSelectEvent(homeCell.outputMode, homeCell.callBack);
                    clickToPlay(homeCell);
                }
                Log.i(TAG, "click end : id = " + homeCell.playId);

            }
        } else if (homeCell instanceof FunctionPairCell) {
            Object tag = view1.getTag();
            if (tag instanceof FunctionPairCell) {
                jumpToAllCategoriesFragment(0, Long.parseLong(((FunctionPairCell) tag).firstCode),
                        Long.parseLong(((FunctionPairCell) tag).secondCode));
            }
        } else {
            Log.i(TAG, "click start : id = " + homeCell.playId);
            if (mKRadioAuthInter != null && !mKRadioAuthInter.authStatus()) {
                Log.i(TAG,
                        "onCardClick: mKRadioAuthInter.authStatus() = " + mKRadioAuthInter.authStatus());
                //如果流量鉴权不通过，也要去触发播放
                clickToPlay(homeCell);
            } else {
                if (PlayerManagerHelper.getInstance().isPlayCurrentRadio(String.valueOf(homeCell.playId))) {
                    handleClickCurPlayItem(homeCell);
                    return;
                }
                ReportUtil.addRecommendSelectEvent(homeCell.outputMode, homeCell.callBack);
                clickToPlay(homeCell);
            }
            Log.i(TAG, "click end : id = " + homeCell.playId);
        }
    }

    public void jumpPage() {
        hideLoading();
        if (!TextUtils.isEmpty(destUrl)) {
            RouterManager.getInstance().interceptApplicationJumpEvent(getContext(), destUrl, routerDesturlPlayCallback);
            destUrl = "";
        }
    }

    @Override
    public void onViewStateRestored(@Nullable Bundle savedInstanceState) {
        super.onViewStateRestored(savedInstanceState);
        recoverHomeData();
//        mCarOwnerRadio.flipCard();
    }

    private void recoverHomeData() {
        Logging.i(TAG, "recoverHomeData start");
        for (HomeRecyclerViewHelper homeRecyclerViewHelper : mHomeRecyclerViewHelperList) {
            homeRecyclerViewHelper.setDataList(mHomeCells);
        }
    }

    /**
     * 跳转到全部分类
     */
    private void jumpToAllCategoriesFragment(int type, long id, long secondId) {
//        Fragment fragment = FragmentFactory.createAllCategoriesFragment(type, id, secondId);
//        this.extraTransaction().start((SupportFragment) fragment);
        EventBus.getDefault().post(new JumpToFragentEvent(type, id, secondId));
    }

    @SuppressLint("LongLogTag")
    private void clickToPlay(HomeCell homeCell) {
        Log.i("handleClickCurPlayItem", "ComperhensiveBrandPageDateFragment -> clickToPlay");
        if (getContext() != null)
            if (NetworkUtil.isNetworkAvailableWidthDefaultToast(getContext().getApplicationContext())) {
                ComprehensivePlayerHelper.play(homeCell);
            } else {
                Log.i(TAG, "clickToPlay no Network");
            }
    }

    @SuppressLint("LongLogTag")
    private void clickToPlayLive(HomeCell homeCell, int childposition) {
        if (homeCell == null) {
            return;
        }
        Log.i(TAG, "homeCell id = " + homeCell.getContentList().get(childposition).getId()
                + "   " + homeCell.getContentList().get(childposition).getResType());
        PlayItem playItem = PlayerManagerHelper.getInstance().getCurPlayItem();
        if (PlayerManagerHelper.getInstance().isSameProgram(playItem, String.valueOf(homeCell.getContentList().get(childposition).getId()))) {
            //相同 只跳转到直播间
            PageJumper.getInstance().jumpToLivePage(Long.parseLong(homeCell.getContentList().get(childposition).getId()));
            return;
        }
        if (getContext() != null)
            if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(getContext().getApplicationContext())) {
                return;
            }
        PlayerManagerHelper.getInstance().start(String.valueOf(homeCell.getContentList().get(childposition).getId())
                , homeCell.getContentList().get(childposition).getResType());
        PageJumper.getInstance().jumpToLivePage(Long.parseLong(homeCell.getContentList().get(childposition).getId()));
    }

    private void reportContentClickEvent(HomeCell homeCell, int childposition) {
//        ReportUtil.addContentClickEvent("", ReportParamUtil.getRadioType(resType),
//                "",
//                String.valueOf(homeCell.playId), ReportParamUtil.getEventTag(homeCell.isVip(), homeCell.isFine()),
//                getPageId(), homeCell.code, "");
        int action = ComponentUtils.getInstance().getContentAction(homeCell.getContentList().get(childposition).getDestUrl()
                , homeCell.getContentList().get(childposition).getCanPlay());

        String tag = getReportTag(homeCell, childposition);
        ReportUtil.addComponentShowAndClickEvent(homeCell.getContentList().get(childposition).getId(),
                true, ComponentUtils.getInstance().getComponentReportCardId(homeCell.getComponentType() + "")
                , action, homeCell.getColumnId(), homeCell.getPositionInParent() + "", childposition + "", homeCell.getContentList().get(childposition).getId()
                , tag, Constants.PAGE_ID_BRAND_PAGE, tag);
    }

    private String getReportTag(HomeCell homeCell, int childposition) {
        String tag = "无";
        AlbumDetailColumnMember albumDetailColumnMember = null;
        if (homeCell.contentList.get(childposition) instanceof AlbumDetailColumnMember) {
            albumDetailColumnMember = (AlbumDetailColumnMember) homeCell.contentList.get(childposition);
            if (!TextUtils.isEmpty(albumDetailColumnMember.getFine() + "") && albumDetailColumnMember.getFine() == 1) {
                tag = "精品";
            } else if (albumDetailColumnMember.getVip() == 1) {
                tag = "VIP";
            }

        }


        return tag;
    }

    private void reportContentShowEvent(HomeCell homeCell) {
        if (homeCell == null) return;
        for (int i = 0; i < homeCell.getContentList().size(); i++) {

            int action = ComponentUtils.getInstance().getContentAction(homeCell.getContentList().get(i).getDestUrl()
                    , homeCell.getContentList().get(i).getCanPlay());
            ReportUtil.addComponentShowAndClickEvent(homeCell.getContentList().get(i).getId(),
                    false, ComponentUtils.getInstance().getComponentReportCardId(homeCell.getComponentType() + "")
                    , action, homeCell.getCode(), homeCell.getPositionInParent() + ""
                    , i + "", homeCell.getContentList().get(i).getId()
                    , getReportTag(homeCell, i), Constants.PAGE_ID_BRAND_PAGE, getReportTag(homeCell, i));
        }


//        ReportUtil.addContentShowEvent("", ReportParamUtil.getRadioType(homeCell.resType),
//                "",
//                String.valueOf(homeCell.playId), ReportParamUtil.getEventTag(homeCell.isVip(), homeCell.isFine()),
//                getPageId(), homeCell.code, "");
    }

    @Override
    protected void showAccordingToScreen(int orientation) {
        super.showAccordingToScreen(orientation);
        //内容recycleview的右边间距。横屏为零，竖屏为30
        int contentRight = ResUtil.getDimen(R.dimen.home_content_padding_right);
        int contentBottom = ResUtil.getDimen(R.dimen.home_content_padding_bottom);
        //竖屏的时候top的padding为零，因为在HomeItemDecoration中做了便宜。
        int contentTop = ResUtil.getDimen(R.dimen.home_content_padding_top);

        KRadioC211ViewSizeInter inter = ClazzImplUtil.getInter("KRadioC211ViewSizeImpl");
        if (inter != null && inter.isNeedReset()) {
            contentBottom = ResUtil.getDimen(R.dimen.y35);
            contentTop = ResUtil.getDimen(R.dimen.y10);
        }
//        mHomeRecyclerView.setPadding(mHomeRecyclerView.getPaddingLeft(), contentTop, contentRight, contentBottom);
        for (HomeRecyclerViewHelper homeRecyclerViewHelper : mHomeRecyclerViewHelperList) {
            homeRecyclerViewHelper.showAccordingToScreen(orientation);
        }

    }

    private IGeneralListener generalListener = new IGeneralListener() {

        @Override
        public void getPlayListError(PlayItem playItem, int code, int i1) {
            Logging.i(TAG, "getPlayListError:" + code);
            switch (code) {
                case PlayerConstants.ERROR_CODE_PLAY_LIST_IS_LAST_ONE:
                    break;
                case PlayerConstants.ERROR_CODE_RADIO_COPYRIGHT_LITE:
                    UIThreadUtil.runUIThread(() -> {
                        if (getContext() != null)
                            ToastUtil.showError(getContext().getApplicationContext(), R.string.comprehensive_radio_is_lite);
                    })
                    ;
                    break;
                default:
                    if (playItem != null && playItem.getType() == PlayerConstants.RESOURCES_TYPE_LIVING)
                        return;
                    UIThreadUtil.runUIThread(() -> {
                        if (getContext() != null)
                            ToastUtil.showError(getContext().getApplicationContext(), R.string.play_failed_str);
                    });
                    break;
            }
            if (isPlayBroadcastError()) {
                if (mHomeAdapterList != null) {
                    for (HomeAdapter homeAdapter : mHomeAdapterList) {
                        homeAdapter.changeBroadcastPlayingState();
                    }
                }
            }
        }

        @Override
        public void playUrlError(int code) {
            Logging.i(TAG, "playUrlError code = " + code);
            if (!NetworkUtil.isNetworkAvailable(getContext().getApplicationContext(), false)) {
                UIThreadUtil.runUIThread(() -> {
                    if (getContext() != null)
                        ToastUtil.showError(getContext().getApplicationContext(), com.kaolafm.kradio.lib.R.string.no_net_work_str);
                });
                return;
            }
            //fixed 修复最后一条播放完毕后，提醒"资源不支持的问题"
            if (code != PlayerConstants.ERROR_CODE_PLAY_LIST_IS_LAST_ONE) {
                UIThreadUtil.runUIThread(() -> {
                    if (getContext() != null)
                        ToastUtil.showError(getContext().getApplicationContext(), R.string.play_failed_str);
                });
            }
        }
    };

    private boolean isPlayBroadcastError() {
        PlayItem playItemTemp = PlayerManagerHelper.getInstance().getCurPlayItem();
        if (playItemTemp.getType() != PlayerConstants.RESOURCES_TYPE_INVALID) {
            return false;
        }
        if (PlayerManagerHelper.getInstance().getCurrentPlayType() != PlayerConstants.RESOURCES_TYPE_BROADCAST) {
            return false;
        }
        return true;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        SkinStateManager.getInstance().removeLoadSkinListener(loadSkinListener);
        for (RecyclerView recyclerView : recyclerViewList) {
            recyclerView.clearOnScrollListeners();
        }
    }

    @Override
    public void onUserVisible() {
        super.onUserVisible();
        if (brandPageCell != null && brandPageCell.size() > 0) {
            reportContentShowEvent(brandPageCell.get(0));
        }
        if (mExposeRvUtilList != null && recyclerViewList.size() > 0) {
            recyclerViewList.get(0).postDelayed(new Runnable() {
                @Override
                public void run() {
                    for (RecyclerViewExposeUtil recyclerViewExposeUtil : mExposeRvUtilList) {
                        recyclerViewExposeUtil.handleCurrentVisibleItems();
                    }
                }
            }, 200);
        }
    }

    @Override
    public void onItemViewVisible(boolean visible, int position) {
//        if (visible && mHomeAdapter != null) {
//            HomeCell homeCell = mHomeAdapter.getItemData(position);
//            reportContentShowEvent(homeCell);
//        }
        if (visible && mHomeAdapterList != null && mHomeAdapterList.size() > 0) {
            for (HomeAdapter homeAdapter : mHomeAdapterList) {
                HomeCell homeCell = homeAdapter.getItemData(position);
                reportContentShowEvent(homeCell);
            }
        }
    }


    public void onVisibleChanged(boolean isVisible) {
        super.onVisibleChanged(isVisible);

    }


    @Override
    public void showContent(List<HomeCell> cells) {
        YTLogUtil.logStart(TAG, "showContent", "start");
    }

    @Override
    public void showLoading() {
        showOrHideLoadingView(true);
    }

    @Override
    public void hideLoading() {
        showOrHideLoadingView(false);
    }

    @SuppressLint("LongLogTag")
    private void showOrHideLoadingView(boolean isShow) {
        Log.i(TAG, "showOrHideLoadingView: " + isShow);
        ViewUtil.setViewVisibility(mPcLoadingView, isShow ? View.VISIBLE : View.GONE);
    }

    @Override
    public void showError(String error) {
        hideLoading();
        showNoNetWorkView(error);
    }

    @Override
    public void hideErrorLayout() {
        if (mHomeNoNetWorkRl != null) {
            //这里要把无网的view移除，是因为横竖屏转换的时候设置GONE不起作用，依然会显示出来。
            //homeRoom.removeView(mHomeNoNetWorkRl);
            //mHomeNoNetWorkRl = null;
            ViewUtil.setViewVisibility(mHomeNoNetWorkRl, View.GONE);
        }
    }

    @Override
    public void showImage(long id, String img,String title, String desc) {

    }

    @SuppressLint("LongLogTag")
    public void showNoNetWorkView(String error) {
        if (mHomeAdapterList != null && mHomeAdapterList.size() > 0) {
            Log.i(TAG, "showNoNetWorkView RecyclerView has child!");
            return;
        }
        showOrHideLoadingView(false);
        if (mVsHomeNoNetwork != null) {
            if (null == mHomeNoNetWorkRl) {
                mHomeNoNetWorkRl = mVsHomeNoNetwork.inflate();
            }
            if (StringUtil.isNotEmpty(error)) {
                TextView tvNetworkNosign = mHomeNoNetWorkRl.findViewById(R.id.tv_network_nosign);
                tvNetworkNosign.setText(error);
            }
            // 支持点击重试
            KRadioClickRetryInter mKRadioClickRetryInter = ClazzImplUtil.getInter(
                    "KRadioClickRetryInterImpl");
            if (mKRadioClickRetryInter == null || mKRadioClickRetryInter.canRetry()) {
                ImageView ivNetworkNoSign = mHomeNoNetWorkRl.findViewById(R.id.network_nosigin);
                ivNetworkNoSign.setOnClickListener(v -> {
                    hideErrorLayout();
                    loadData();
                    TextView tvNetworkNosign = mHomeNoNetWorkRl.findViewById(R.id.tv_network_nosign);
                    String text = null;
                    if (tvNetworkNosign != null) {
                        text = tvNetworkNosign.getText().toString();
                    }
                    ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_NETWORK_RETRY, text, getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
                });
            }
        }
        ViewUtil.setViewVisibility(mHomeNoNetWorkRl, View.VISIBLE);
        if (mHomeNoNetWorkRl != null) {
            TextView tvNetworkNosign = mHomeNoNetWorkRl.findViewById(R.id.tv_network_nosign);
            String text = null;
            if (tvNetworkNosign != null) {
                text = tvNetworkNosign.getText().toString();
            }
            ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_NETWORK_RETRY, text, getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
        }
    }

    @Override
    public void onLoaing() {
        showLoading();
    }

    @Override
    public void onLoadFinish() {
        hideLoading();
    }

    @Override
    public void showTabAndList(BrandPageListBean brandPageListBean) {

    }

    @Override
    public void showTabAndFailure(Exception e) {
    }

    private void addListView(boolean isBrandListView) {
        //解决Monkey空指针问题。原因推测：tab切换太快导致fragment被detach造成getContext()方法返回了null
        if (!isAdded()) return;
        View view = LayoutInflater.from(getContext()).inflate(R.layout.home_date_list, home_root_cl, false);
        GridRecyclerView recyclerView = view.findViewById(R.id.rv_home_content);
        recyclerViewList.add(recyclerView);
        if (!isBrandListView) {
            view.setPadding(view.getPaddingLeft(), ResUtil.getDimen(R.dimen.m92), view.getPaddingRight(), view.getPaddingBottom());

            handleScrollLeftRight(view);
        }
        home_root_cl.addView(view);
    }

    private void handleScrollLeftRight(View view){
        TextView scrollLeft = view.findViewById(R.id.cd_left);
        TextView scrollRight = view.findViewById(R.id.cd_right);

        scrollLeft.setVisibility(View.VISIBLE);
        scrollRight.setVisibility(View.VISIBLE);

        scrollLeft.setOnClickListener((v) -> {
            if(recyclerViewList.size() > 0){
                RecyclerView last = recyclerViewList.get(recyclerViewList.size() -1);
                if(last != null){
                    ScrollingUtil.scrollListHorizontalByVoice(last, -1);
                }
            }

        });
        scrollRight.setOnClickListener((v) -> {

            RecyclerView last = recyclerViewList.get(recyclerViewList.size() -1);
            if(last != null){
                ScrollingUtil.scrollListHorizontalByVoice(last, 1);

            }
        });
    }

    private void initListDate(GridRecyclerView mHomeRecyclerView) {
        HomeRecyclerViewHelper mHomeRecyclerViewHelper = new HomeRecyclerViewHelper();
        mHomeRecyclerViewHelperList.add(mHomeRecyclerViewHelper);
        HomeAdapter mHomeAdapter = new HomeAdapter(true);
        mHomeAdapterList.add(mHomeAdapter);
        mHomeAdapter.setOnItemOhterClickListener(new BaseAdapter.OnItemOhterClickListener() {
            @Override
            public void onItemClick(View view, int position, Object o) {
                if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext().getApplicationContext(), true)) {
//                    Log.i(TAG, "fetchSubscriptionData no network");
                    if (getContext() != null)
                        ToastUtil.showInfo(getContext().getApplicationContext(), ResUtil.getString(R.string.no_net_work_str));
                    return;
                }
                if (o instanceof TopicPosts) {
                    if (isLikeSend) {
                        return;
                    }
                    //组件话题帖子点赞
                    if (!UserInfoManager.getInstance().isUserBound()) {
                        RouterManager.getInstance().jumpPage(RouterConstance.LOGIN_COMPREHENSIVE_URL);
                        return;
                    }

                    topicLikePosts((TopicPosts) o, position);
                }
            }
        });
        mHomeAdapter.setOnItemClickListener(new BaseAdapter.OnItemClickListener<HomeCell>() {
            @Override
            public void onItemClick(View view, int viewType, HomeCell homeCell, int position) {
                onListItemClick(view, homeCell, position);
            }
        });
        mHomeRecyclerViewHelper.bindRecycleView(mHomeRecyclerView, mHomeAdapter);

        RecyclerViewExposeUtil mExposeRvUtil = new RecyclerViewExposeUtil();
        mExposeRvUtilList.add(mExposeRvUtil);
        mExposeRvUtil.setRecyclerItemExposeListener(mHomeRecyclerView, this);
        KradioAdColumnManager.getInstance().bindHomeRecyclerViewHelper(mHomeRecyclerViewHelper);
    }

    @Override
    public void onShowContent(List<HomeCell> cells) {
        showItemAnimation &= mHomeAdapterList.size() == 0 || ListUtil.isEmpty(mHomeAdapterList.get(0).getDataList());

        mHomeCells = checkBrandPageCell(cells);
        addListView(false);
        if (recyclerViewList != null && recyclerViewList.size() > 0) {
            for (GridRecyclerView recyclerView : recyclerViewList) {
                initListDate(recyclerView);
            }
        }
        if (isFinalUserVisible()) {
            if (mHomeRecyclerViewHelperList != null && mHomeRecyclerViewHelperList.size() > 0) {
                if (showItemAnimation) {
                    Animation animation = AnimationUtils.loadAnimation(getContext(), R.anim.brand_page_list_item_anim);
                    LayoutAnimationController layoutAnimationController = new LayoutAnimationController(animation);
                    //设置顺序
                    layoutAnimationController.setOrder(LayoutAnimationController.ORDER_NORMAL);
                    for (int i = 0; i < recyclerViewList.size(); i++) {
                        if (brandPageCell.size() == 0) {
                            recyclerViewList.get(i).setLayoutAnimation(layoutAnimationController);
                        } else {
                            if (i > 0) {
                                recyclerViewList.get(i).setLayoutAnimation(layoutAnimationController);
                            }
                        }
//                        if (brandPageCell.size() == 0 && i != 0) {
//                            recyclerViewList.get(i).setLayoutAnimation(layoutAnimationController);
//                        }
                    }
                }
            }
        }
        for (int i = 0; i < mHomeRecyclerViewHelperList.size(); i++) {
            if (brandPageCell.size() > 0 && i == 0) {
                mHomeRecyclerViewHelperList.get(i).setDataList(brandPageCell);
            } else {
                mHomeRecyclerViewHelperList.get(i).setDataList(cells);
            }
        }
        hideLoading();
        hideErrorLayout();
        if (isAutoPlay) {
            //如果需要自动播放，则执行自动播放
            if (brandPageCell.size() > 0) {
//                mockContentList(brandPageCell.get(0));
                autoPlayContent(brandPageCell.get(0), cells);
            } else {
                autoPlayContent(null, cells);
            }

            if (!isHaveBrandPageCell) {
                //如果没有品牌电台组件，则直接执行其他内容的显示动画
                if (getActivity() != null) {
                    getActivity().sendBroadcast(new Intent(BROADCAST_RECEIVER_ACTION_CAR_ENTER_ANIMATION_COMPLETE));
                }
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    @Override
    public void onUpdateLiveStatus(List<HomeCell> newCells) {
        if (recyclerViewList == null || recyclerViewList.size() < 2){
            Log.i(TAG, "updateFragmentLiveStatus -> recyclerViewList.size() less than 2, not have brand, return");
            return;
        }
        boolean localHaveBrandLive = haveBrandLive();
        Log.i(TAG, "updateFragmentLiveStatus -> onUpdateLiveStatus --- localHaveBrandLive=" + localHaveBrandLive);
        if (!localHaveBrandLive){
            return;
        }
        LiveProgramDetailColumnMember localLiveData;
        try {
            localLiveData = (LiveProgramDetailColumnMember) brandPageCell.get(0).getContentList().get(0);
        } catch (Exception e) {
            localLiveData = null;
        }
        Log.i(TAG, "updateFragmentLiveStatus -> onUpdateLiveStatus --- localLiveStatus=" + (localLiveData == null ? null : localLiveData.getLiveStatus()));
        if (localLiveData == null){
            return;
        }
        boolean newIsBrandPageCell = isBrandPageCell(newCells);
        Log.i(TAG, "updateFragmentLiveStatus -> onUpdateLiveStatus --- newIsBrandPageCell=" + newIsBrandPageCell);
        if (!newIsBrandPageCell){
            return;
        }
        LiveProgramDetailColumnMember newLiveData = null;
        HomeCell newHomeCell = newCells.get(0);
        if (newHomeCell.getContentList() != null && newHomeCell.getContentList().size() > 0){
            try {
                newLiveData = (LiveProgramDetailColumnMember) newHomeCell.getContentList().get(0);
            } catch (Exception e) {
            }
        }
        boolean newBrandHaveLiveData = newLiveData != null;
        Log.i(TAG, "updateFragmentLiveStatus -> onUpdateLiveStatus --- newBrandHaveLiveData=" + newBrandHaveLiveData);
        Log.i(TAG, "updateFragmentLiveStatus -> onUpdateLiveStatus --- newLiveData=" + (newLiveData == null ? null : newLiveData.getLiveStatus()));
        if (!newBrandHaveLiveData){
            return;
        }
        RecyclerView.Adapter brandAdapter = recyclerViewList.get(0).getAdapter();
        if (brandAdapter == null){
            Log.i(TAG, "updateFragmentLiveStatus -> brandAdapter is null, return");
            return;
        }
        brandPageCell.get(0).getContentList().remove(0);
        brandPageCell.get(0).getContentList().add(0, newLiveData);
        brandAdapter.notifyDataSetChanged();
    }

    private void mockContentList(HomeCell data){
        LiveProgramDetailColumnMember live = new LiveProgramDetailColumnMember();
        live.setId("2333");
        live.setCode("10073184");
        live.setTitle("经济之声~~~~~~~~ abcdefghijklmnopqrstuvwxyz");
        live.setLiveStatus(LiveInfoDetail.STATUS_FINISHED);
        live.setResType(com.kaolafm.opensdk.ResType.TYPE_LIVE);
        Map<String, ImageFile> map = new HashMap<>();
        ImageFile imageFile = new ImageFile();
        imageFile.setUrl("https://iovimg.radio.cn/mz/images/202310/8391e0cf-0975-4857-bd59-7fcad5931e14/default.jpg");
        imageFile.setWidth(550);
        imageFile.setHeight(550);
        map.put("cover", imageFile);
        live.setImageFiles(map);
        List<ColumnContent> list = new ArrayList<>();
        list.add(live);
        data.setContentList(list);

    }

    /**
     * 校验是否有品牌主页
     */
    private List<HomeCell> checkBrandPageCell(List<HomeCell> cells) {
        //是否配置了品牌电台光圈组件
        if (isBrandPageCell(cells)) {
            //如果是品牌主页入口类型就要从列表移除
            brandPageCell.add(cells.get(0));
            cells.remove(0);
            for (HomeCell cell : cells) {
                //因为去除了光圈组件所以所有的索引应该-1
                cell.setPositionInParent(cell.getPositionInParent() - 1);
            }
            showBrandPage();
            isHaveBrandPageCell = true;
        } else {
            boolean b = false;
            for (int i = 0; i < cells.size(); i++) {
                //如果入口组件配置在非第0个位置就要移除，不显示
                if (cells.get(i).itemType == ResType.HOME_ITEM_TYPE_BRAND) {
                    cells.remove(i);
                    b = true;
                }
            }
            if (b) {
                //如果进行了移除操作，就要是对索引进行重新排序
                for (int i = 0; i < cells.size(); i++) {
                    cells.get(i).setPositionInParent(i);
                }
            }
        }

        return cells;
    }

    /**
     * 自动播放逻辑
     * 进入品牌页面后自动播放逻辑 do
     *
     * @param homeCell
     * @param cells
     */
    private void autoPlayContent(HomeCell homeCell, List<HomeCell> cells) {
        //如果是空，则从cells中往下找能播的去播
        if (homeCell == null || ListUtil.isEmpty(homeCell.getContentList())) {
            if (ListUtil.isEmpty(cells)) return;
            if (cells.size() > 1) {
                List<HomeCell> newCells = cells.subList(1, cells.size());
                autoPlayContent(cells.get(0), newCells);
            } else {
                autoPlayContent(cells.get(0), new ArrayList<>());
            }
            return;
        }

        List<ColumnContent> contentList = homeCell.getContentList();
        for (ColumnContent columnContent : contentList) {
            if (canPlay(columnContent)) {
                //如果能播放，就播放
                if (columnContent.getResType() == ResType.LIVE_TYPE) {
                    // do 如果是直播，且已经结束，则继续向下寻找下一个可播内容
                    switch (columnContent.getLiveStatus()) {
                        case LiveInfoDetail.STATUS_NOT_START:
                            break;
                        case LiveInfoDetail.STATUS_FINISHED:
                            //如果已完成的直播，继续往下找
                            continue;
                        case LiveInfoDetail.STATUS_LIVING:
                            break;
                        case LiveInfoDetail.STATUS_COMING:
                            break;
                        case LiveInfoDetail.STATUS_DELAYED:
                            break;
                        default:
                            //未知状态
                            break;
                    }
                }
                if (PlayerManagerHelper.getInstance().isSameProgram(columnContent.getId())) {
                    //产品需求：进入品牌电台页面需要播放第一个可播内容，如果正在播的就是第一个内容，
                    // 则不需要进行操作，保持原来的播放或暂停状态即可。
                    return;
                }
                PlayerManagerHelper.getInstance().start(columnContent.getId(), columnContent.getResType());
                return;
            }
        }
        //如果ContentList里面所有的都不能播放，则找下一个homecell
        if (ListUtil.isEmpty(cells)) return;
        if (cells.size() > 1) {
            List<HomeCell> newCells = cells.subList(1, cells.size());
            autoPlayContent(cells.get(0), newCells);
        } else {
            autoPlayContent(cells.get(0), new ArrayList<>());
        }
    }

    private boolean canPlay(ColumnContent columnContent) {
        if (columnContent == null) return false;
        switch (columnContent.getResType()) {
            case ResType.BROADCAST_TYPE:
            case ResType.TV_TYPE:
            case ResType.ALBUM_TYPE:
            case ResType.RADIO_TYPE:
            case ResType.FEATURE_TYPE:
            case ResType.LIVE_TYPE:
            case ResType.AUDIO_TYPE:
                return true;
            default:
                return false;
        }
    }

    private boolean isBrandPageCell(List<HomeCell> cells) {
        if (cells != null && cells.size() > 0) {
            if (cells.get(0).itemType == ResType.HOME_ITEM_TYPE_BRAND) {
                return true;
            }
        }
        return false;
    }

    /**
     * 展示品牌入口数据
     */
    private void showBrandPage() {
        if (brandPageCell != null) {
            ConfigSettingManager.getInstance().getConfigSetting(new IConfigSettingOptionListener() {
                @Override
                public void onGetSuccess(ConfigSettingOption configSettingOption) {
                    if (configSettingOption.getShowBrandEntries() != null && configSettingOption.getShowBrandEntries() == 1) {
                        //需要展示
                        addListView(true);
                        if (brandPageCell != null && brandPageCell.size() > 0) {
                            reportContentShowEvent(brandPageCell.get(0));
                        }
                    } else {
                        brandPageCell.clear();
                    }
                }

                @Override
                public void onGetFailure(ApiException e) {

                }
            });
        }
    }

    @Override
    public void onShowContentFailure(Exception e) {
        showError("");
    }

    /**
     * 需求功能点：
     *  1、当前播放内容为A，暂停时，再次点击A卡片，A可以接续播放;
     *  2、当前播放内容为A，播放期间，再次点击A卡片，不影响当前播放状态，但是会进入专辑或专题详情页。
     */
    private void handleClickCurPlayItem(HomeCell homeCell){
        boolean isPlaying = PlayerManagerHelper.getInstance().isPlaying();
        Log.i("handleClickCurPlayItem", "ComperhensiveBrandPageDateFragment -> handleClickCurPlayItem -> isPlaying=" + isPlaying);
        if (isPlaying){
            handleItemClickPlayingItem();
        } else {
//            PlayerManagerHelper.getInstance().switchPlayerStatus(true);
            clickToPlay(homeCell);
        }
    }

    /**
     * 处理点击正在播放中的 item
     */
    private void handleItemClickPlayingItem(){
        Log.i("handleClickCurPlayItem", "ComperhensiveBrandPageDateFragment -> handleItemClickPlayingItem");
        if (activity instanceof ComprehensiveBrandPageActivity) {
            ((ComprehensiveBrandPageActivity) activity).jumpToPlayerPage();
        }
    }
}