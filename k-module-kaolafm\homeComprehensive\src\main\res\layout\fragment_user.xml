<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/user_center_main_layout"
    android:background="@drawable/bg_home">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/user_center_top_guideline"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.1"/>

    <ImageView
        android:id="@+id/user_back"
        style="@style/FragmentBackButton"
        android:layout_marginTop="0dp"
        android:layout_marginStart="@dimen/m70"
        app:layout_constraintBottom_toBottomOf="@id/login_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/login_title"/>

    <TextView
        android:id="@+id/login_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/user_title"
        android:textColor="@color/global_title_text_color"
        android:textSize="@dimen/m32"
        app:layout_constraintBottom_toTopOf="@id/user_center_top_guideline"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/user_right_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/globle_round_bg"
        android:drawablePadding="@dimen/x5"
        android:gravity="center"
        android:paddingLeft="@dimen/x16"
        android:paddingTop="@dimen/y10"
        android:paddingRight="@dimen/x16"
        android:paddingBottom="@dimen/y10"
        android:textColor="@color/user_right_textview_color"
        android:textSize="@dimen/text_size1"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/view_divider"
        app:layout_constraintHorizontal_bias="0.95"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/user_tab_title" />
    <!--注意:为了对齐fragment,必须满足:tl_tab_padding + layout_marginLeft == x90-->
    <com.flyco.tablayout.SlidingTabLayout
        android:id="@+id/user_tab_title"
        android:layout_width="0dp"
        android:layout_height="@dimen/y70"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/user_center_top_guideline"
        app:tl_indicator_anim_enable="true"
        app:tl_indicator_color="@color/tab_indicator_underline_color"
        app:tl_indicator_height="@dimen/tab_indicator_height"
        app:tl_indicator_width="@dimen/tab_indicator_width"
        app:tl_indicator_width_equal_title="true"
        app:tl_textBold="SELECT"
        app:tl_textSelectColor="@color/tab_indicator_select_color"
        app:tl_textSelectSize="@dimen/text_size4"
        app:tl_textUnselectColor="@color/tab_indicator_unselect_color"
        app:tl_textsize="@dimen/text_size4" />

    <View
        android:id="@+id/view_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/y1"
        android:layout_alignParentTop="true"
        android:background="@color/user_setting_view_divider"
        app:layout_constraintTop_toBottomOf="@+id/user_tab_title" />

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/user_viewpager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:overScrollMode="never"
        android:paddingLeft="@dimen/x1"
        android:paddingRight="@dimen/x1"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/view_divider" />

</androidx.constraintlayout.widget.ConstraintLayout>