// MicroModule build file where you can declare MicroModule dependencies.
dependencies {
    implementation fileTree(dir: 'liveComprehensive/libs', include: ['*.jar'])
    implementation microModule(':common')
    implementation microModule(':live')
    implementation microModule(':purchase')
    implementation microModule(':userComprehensive')
    implementation microModule(':purchaseComprehensive')
}
