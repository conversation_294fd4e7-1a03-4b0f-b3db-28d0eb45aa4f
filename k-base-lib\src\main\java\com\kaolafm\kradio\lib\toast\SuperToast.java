package com.kaolafm.kradio.lib.toast;

import android.animation.Animator;
import android.animation.AnimatorInflater;
import android.app.Activity;
import android.content.Context;
import android.content.res.Resources.NotFoundException;
import android.graphics.PixelFormat;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.os.Build.VERSION;
import android.os.Build.VERSION_CODES;
import androidx.annotation.AnimatorRes;
import androidx.annotation.ColorInt;
import androidx.annotation.DrawableRes;
import androidx.annotation.LayoutRes;
import androidx.annotation.StringRes;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.View.OnTouchListener;
import android.view.ViewGroup;
import android.view.ViewGroup.LayoutParams;
import android.view.WindowManager;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityManager;
import android.widget.FrameLayout;
import android.widget.TextView;
import com.kaolafm.kradio.lib.R;
import com.kaolafm.kradio.lib.toast.ToastStyle.DisplayLevel;
import com.kaolafm.kradio.lib.toast.ToastStyle.IconPosition;
import com.kaolafm.kradio.lib.toast.ToastStyle.PriorityLevel;
import com.kaolafm.kradio.lib.utils.ResUtil;

/**
 * toast显示，用来代替系统toast和snackbar。相对于系统控件更加灵活，比如可以设置任意时间等。<br/>
 * <p>
 * 如果要系统层级显示toast设置参数{@link ToastStyle#LEVEL_SYSTEM}，如果是在App页面内显示即页面退出toast也消失，设置参数{@link
 * ToastStyle#LEVEL_ACTIVITY}。<br/>
 * <p>
 * {@link #create()}必须调用，且在设置参数之后{@link #show()}方法之前。<br/>
 * <p>
 * 显示时间默认3000毫秒。
 *
 * <AUTHOR>
 * @date 2018/4/11
 */

public class SuperToast {

    private Context mContext;

    private OnDismissListener mDismissListener;

    private ToastStyle mToastStyle;

    private View mView;

    private TextView mTvMessage;

    private ViewGroup mViewGroup;

    public SuperToast(Context context) {
        mContext = context;
        mToastStyle = new ToastStyle();
    }

    public OnDismissListener getDismissListener() {
        return mDismissListener;
    }

    @PriorityLevel
    public int getPriorityLevel() {
        return mToastStyle.priorityLevel;
    }

    public Animator getShowAnimator() {
        return mToastStyle.showAnimator;
    }

    public Animator getHideAnimator() {
        return mToastStyle.hideAnimator;
    }

    public Context getContext() {
        return mContext;
    }

    public int getDisplayLevel() {
        return mToastStyle.displayLevel;
    }

    public int getDuration() {
        return mToastStyle.duration;
    }

    public long getTimestamp() {
        return mToastStyle.timestamp;
    }


    public int getGravity() {
        return mToastStyle.gravity;
    }


    public View getView() {
        return mView;
    }

    public ViewGroup getViewGroup() {
        if (mViewGroup == null) {
            if (!(mContext instanceof Activity)) {
                throw new IllegalArgumentException("使用LEVEL_ACTIVITY层级的toast Context需要是Activity");
            }
            mViewGroup = ((Activity) mContext).findViewById(android.R.id.content);
        }
        return mViewGroup;
    }

    public LayoutParams getWindowParams() {
        WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams();
        layoutParams.height = mToastStyle.height;
        layoutParams.width = mToastStyle.width;
        if (mToastStyle.canTouch) {
            layoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE |
                    WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH |
                    WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS |
                    WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL |
                    WindowManager.LayoutParams.FLAG_LAYOUT_INSET_DECOR |
                    WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN;
        } else {
            layoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
                    | WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE
                    | WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON;
        }
        layoutParams.format = PixelFormat.TRANSLUCENT;
        if (VERSION.SDK_INT > VERSION_CODES.O) {
            layoutParams.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
        }else if (VERSION.SDK_INT > VERSION_CODES.N) {
            layoutParams.type = WindowManager.LayoutParams.TYPE_PHONE;
        } else {
            layoutParams.type = WindowManager.LayoutParams.TYPE_TOAST;
        }
        layoutParams.windowAnimations = mToastStyle.animationId;
        layoutParams.gravity = mToastStyle.gravity;
        layoutParams.x = mToastStyle.xOffset;
        layoutParams.y = mToastStyle.yOffset;
        return layoutParams;
    }

    /**
     * 自定义显示样式
     */
    public SuperToast setToastStyle(ToastStyle toastStyle) {
        mToastStyle = toastStyle;
        return this;
    }

    /**
     * 自定义显示的View
     *
     * @param viewId 自定义view的layout
     */
    public SuperToast setView(@LayoutRes int viewId) {
        mToastStyle.layoutId = viewId;
        return this;
    }

    /**
     * 自定义显示的View
     *
     * @param view 自定义view
     */
    public SuperToast setView(View view) {
        mView = view;
        return this;
    }

    /**
     * LEVEL_ACTIVITY显示层级，设置toast依附的ViewGroup，默认是根ViewGroup
     */
    public SuperToast setViewGroup(ViewGroup viewGroup) {
        mViewGroup = viewGroup;
        return this;
    }

    /**
     * 设置显示的文本
     */
    public SuperToast setMessage(String message) {
        mToastStyle.message = message;
        return this;
    }

    /**
     * 设置显示的文本
     *
     * @param message 资源id
     */
    public SuperToast setMessage(@StringRes int message) {
        mToastStyle.message = mContext.getString(message);
        return this;
    }

    /**
     * 设置显示文本的颜色
     *
     * @param color 色值
     */
    public SuperToast setTextColor(@ColorInt int color) {
        mToastStyle.messageTextColor = color;
        return this;
    }

    /**
     * 设置显示文本的大小
     *
     * @param textSize px值
     */
    public SuperToast setTextSize(int textSize) {
        mToastStyle.messageTextSize = textSize;
        return this;
    }

    /**
     * 设置文本旁边的icon
     *
     * @param iconRes icon资源id
     */
    public SuperToast setIconRes(@DrawableRes int iconRes) {
        mToastStyle.messageIconRes = iconRes;
        return this;
    }

    /**
     * 设置icon在文字显示的位置
     *
     * @param position 上下左右
     */
    public SuperToast setIconPosition(@IconPosition int position) {
        mToastStyle.messageIconPosition = position;
        return this;
    }

    /**
     * 设置背景色
     *
     * @param background 色值
     */
    public SuperToast setBackground(@ColorInt int background) {
        mToastStyle.backgroundColor = background;
        return this;
    }

    /**
     * 设置背景图片
     *
     * @param background 图片
     */
    public SuperToast setBackground(Drawable background) {
        mToastStyle.background = background;
        return this;
    }


    /**
     * 设置toast的圆角度数
     */
    public SuperToast setRadius(int radius) {
        mToastStyle.radius = radius;
        return this;
    }

    /**
     * 系统层级显示toast设置参数{@link ToastStyle#LEVEL_SYSTEM}；<br/>
     * App页面内显示即页面退出toast也消失，设置参数{@link ToastStyle#LEVEL_ACTIVITY}。
     *
     * @param displayLevel 显示层级
     */
    public SuperToast setDisplayLevel(@DisplayLevel int displayLevel) {
        mToastStyle.displayLevel = displayLevel;
        return this;
    }

    /**
     * 设置toast是否可操作。默认不可操作。在LEVEL_ACTIVITY层级一直可以操作。
     *
     * @param canTouch true 可以操作
     */
    public SuperToast setCanTouch(boolean canTouch) {
        mToastStyle.canTouch = canTouch;
        return this;
    }


    /**
     * 设置toast显示时间，小于0表示一直显示。默认三秒
     *
     * @param duration 毫秒值
     */
    public SuperToast setDuration(int duration) {
        mToastStyle.duration = duration;
        return this;
    }

    /**
     * 设置toast显示的优先级，
     *
     * @param priorityLevel 高({@link ToastStyle#PRIORITY_HIGH}),中({@link ToastStyle#PRIORITY_MEDIUM}),低({@link
     *                      ToastStyle#PRIORITY_LOW})
     */
    public SuperToast setPriorityLevel(@PriorityLevel int priorityLevel) {
        mToastStyle.priorityLevel = priorityLevel;
        return this;
    }

    /**
     * 设置toast显示动画，该动画只在{@link ToastStyle#LEVEL_ACTIVITY}层级有效果
     */
    public SuperToast setShowAnimator(Animator showAnimator) {
        mToastStyle.showAnimator = showAnimator;
        return this;
    }

    /**
     * 设置toast显示动画，该动画只在{@link ToastStyle#LEVEL_ACTIVITY}层级有效果
     *
     * @param showAnimator 动画id
     */
    public SuperToast setShowAnimator(@AnimatorRes int showAnimator) {
        mToastStyle.showAnimator = AnimatorInflater.loadAnimator(mContext, showAnimator);
        return this;
    }

    /**
     * 设置toast隐藏动画,该动画只在{@link ToastStyle#LEVEL_ACTIVITY}层级有效果
     */
    public SuperToast setHideAnimator(Animator hideAnimator) {
        mToastStyle.hideAnimator = hideAnimator;
        return this;
    }

    /**
     * 设置toast隐藏动画,该动画只在{@link ToastStyle#LEVEL_ACTIVITY}层级有效果
     *
     * @param hideAnimator 动画id
     */
    public SuperToast setHideAnimator(@AnimatorRes int hideAnimator) {
        mToastStyle.hideAnimator = AnimatorInflater.loadAnimator(mContext, hideAnimator);
        return this;
    }

    /**
     * 用于{@link ToastStyle#LEVEL_SYSTEM}层级的toast的显示隐藏动画，且只能用系统动画资源，默认效果{@link android.R.style#Animation_Toast}
     *
     * @param animId 系统动画id
     */
    public SuperToast setAnimations(int animId) {
        mToastStyle.animationId = animId;
        return this;
    }

    /**
     * 设置toast的显示位置
     */
    public SuperToast setGravity( int gravity) {
        mToastStyle.gravity = gravity;
        return this;
    }

    /**
     * 设置水平方向偏移量，左右的margin值
     *
     * @param xOffset 偏移量值px
     */
    public SuperToast setXOffset(int xOffset) {
        mToastStyle.xOffset = xOffset;
        return this;
    }

    /**
     * 设置竖直方向偏移量，上下的margin值
     *
     * @param yOffset 偏移量值px
     */
    public SuperToast setYOffset(int yOffset) {
        mToastStyle.yOffset = yOffset;
        return this;
    }

    /**
     * 设置宽 px，如果要用Match和Wrap 请使用{@link android.view.ViewGroup.LayoutParams#MATCH_PARENT } 或 {@link
     * ViewGroup.LayoutParams#WRAP_CONTENT}
     */
    public SuperToast setWidth(int width) {
        mToastStyle.width = width;
        return this;
    }

    /**
     * 设置高 px；如果要用Match和Wrap 请使用{@link ViewGroup.LayoutParams#MATCH_PARENT } 或 {@link
     * ViewGroup.LayoutParams#WRAP_CONTENT}
     */
    public SuperToast setHeight(int height) {
        mToastStyle.height = height;
        return this;
    }

    /**
     * 是否是一直显示
     */
    public boolean isIndeterminate() {
        return mToastStyle.duration < 0;
    }

    /**
     * 判断是不是横竖屏转换，如果是，就不执行toast动画，否则就执行。
     */
    protected boolean isFromOrientationChange() {
        return false;
    }

    /**
     * 判断当前toast是否正在显示中
     */
    public boolean isShowing() {
        //这里有问题,如果App退到后台,这时mView.isShown()是false;再次进入App,toast还是显示的.
        return mView != null && mView.isShown();
    }

    /**
     * 创建toast显示的View，默认是系统toast的布局
     */
    protected View onCreateView() {
        if (mView != null) {
            return mView;
        }
        View view;
        if (mToastStyle.displayLevel == ToastStyle.LEVEL_ACTIVITY) {
            if (!(mContext instanceof Activity)) {
                throw new IllegalArgumentException("使用LEVEL_ACTIVITY层级的toast Context需要是Activity");
            }
            mViewGroup = ((Activity) mContext).findViewById(android.R.id.content);
            view = LayoutInflater.from(mContext)
                    .inflate(mToastStyle.layoutId, mViewGroup, false);
        } else {
            view = LayoutInflater.from(mContext)
                    .inflate(mToastStyle.layoutId, null);
        }
        //如果用的自定义的布局就不存在。
        try {
            mTvMessage = view.findViewById(R.id.tv_toast_message);
        } catch (NotFoundException e) {
            mTvMessage = null;
        }
        return view;
    }

    public SuperToast show() {
        ToastHandler.getInstance().add(this);
        sendAccessibilityEvent();
        return this;
    }

    /**
     * 创建布局，一定要在参数设置之后，show之前调用。
     */
    public SuperToast create() {
        mView = onCreateView();
        if (mView.getBackground() == null) {
            mView.setBackground(getBackground());
        }
        //api21以上加阴影效果。
        if (VERSION.SDK_INT >= VERSION_CODES.LOLLIPOP) {
            mView.setElevation(3F);
        }
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(mToastStyle.width, mToastStyle.height);
        layoutParams.width = mToastStyle.width;
        layoutParams.height = mToastStyle.height;
        layoutParams.bottomMargin = mToastStyle.yOffset;
        layoutParams.topMargin = mToastStyle.yOffset;
        layoutParams.leftMargin = mToastStyle.xOffset;
        layoutParams.rightMargin = mToastStyle.xOffset;
        layoutParams.gravity = mToastStyle.gravity | Gravity.CENTER_HORIZONTAL;
        mView.setLayoutParams(layoutParams);
        mToastStyle.timestamp = System.currentTimeMillis();

        //不等于空说明用的是默认布局。
        if (mTvMessage != null) {
            mView.setMinimumWidth(ResUtil.getDimen(R.dimen.x342));
            mTvMessage.setText(mToastStyle.message);
            mTvMessage.setTextColor(mToastStyle.messageTextColor);
            mTvMessage.setTextSize(TypedValue.COMPLEX_UNIT_PX, mToastStyle.messageTextSize);
            if (mToastStyle.messageIconRes > 0) {
                switch (mToastStyle.messageIconPosition) {
                    case ToastStyle.ICON_POSITION_LEFT:
                        mTvMessage.setCompoundDrawablesWithIntrinsicBounds(mToastStyle.messageIconRes, 0, 0, 0);
                        break;
                    case ToastStyle.ICON_POSITION_TOP:
                        mTvMessage.setCompoundDrawablesWithIntrinsicBounds(0, mToastStyle.messageIconRes, 0, 0);
                        break;
                    case ToastStyle.ICON_POSITION_RIGHT:
                        mTvMessage.setCompoundDrawablesWithIntrinsicBounds(0, 0, mToastStyle.messageIconRes, 0);
                        break;
                    case ToastStyle.ICON_POSITION_BOTTOM:
                        mTvMessage.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, mToastStyle.messageIconRes);
                        break;
                    default:
                }
            }
        }
        //好像不起作用
        if (!mToastStyle.canTouch) {
            mView.setOnTouchListener(new OnTouchListener() {
                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    return true;
                }
            });
        }
        return this;
    }

    /**
     * 取消正在显示的toast并清空消息队列。
     */
    public void cancelAllToasts() {
        ToastHandler.getInstance().cancelAllToasts();
    }

    /**
     * toast的背景
     */
    private Drawable getBackground() {
        if (mToastStyle.background == null) {
            GradientDrawable gradientDrawable = new GradientDrawable();
            gradientDrawable.setColor(mToastStyle.backgroundColor);
            gradientDrawable.setCornerRadius(mToastStyle.radius);
            return gradientDrawable;
        } else {
            return mToastStyle.background;
        }
    }

    /**
     * 发送辅助功能事件。主要是了为了辅助残疾人士更好使用设备。
     */
    protected void sendAccessibilityEvent() {
        AccessibilityManager am = (AccessibilityManager) mContext.getApplicationContext()
                .getSystemService(Context.ACCESSIBILITY_SERVICE);
        if (am == null || !am.isEnabled()) {
            return;
        }
        final AccessibilityEvent accessibilityEvent = AccessibilityEvent
                .obtain(AccessibilityEvent.TYPE_NOTIFICATION_STATE_CHANGED);
        accessibilityEvent.setClassName(mView.getClass().getName());
        accessibilityEvent.setPackageName(mContext.getPackageName());
        mView.dispatchPopulateAccessibilityEvent(accessibilityEvent);
        am.sendAccessibilityEvent(accessibilityEvent);
    }

    /**
     * 手动让toast的消失。
     */
    public void dismiss() {
        ToastHandler.getInstance().removeToast(this);
    }

    public SuperToast setOnDismissListener(OnDismissListener dismissListener) {
        mDismissListener = dismissListener;
        return this;
    }

    /**
     * toast消失时的回调
     */
    public interface OnDismissListener {

        /**
         * toast消失时会回调该方法。
         */
        void onDismiss(View view);
    }


}
