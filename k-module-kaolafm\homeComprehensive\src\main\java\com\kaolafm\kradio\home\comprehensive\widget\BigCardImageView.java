package com.kaolafm.kradio.home.comprehensive.widget;

import android.content.Context;
import androidx.appcompat.widget.AppCompatImageView;
import android.util.AttributeSet;

/**
 * <AUTHOR>
 **/
public class BigCardImageView extends AppCompatImageView {
    private boolean isPlaying = false;

    public BigCardImageView(Context context) {
        super(context);
    }

    public BigCardImageView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public BigCardImageView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void setPlayState(boolean isPlaying) {
        //防止onMeasure执行多次
        if (this.isPlaying != isPlaying) {
            this.isPlaying = isPlaying;
            requestLayout();
        }
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        setMeasuredDimension(getDefaultSize(0, widthMeasureSpec), getDefaultSize(0, heightMeasureSpec));
        int childWidthSize = getMeasuredWidth();
        //高度和宽度一样
        if (isPlaying) {
            widthMeasureSpec = MeasureSpec.makeMeasureSpec(childWidthSize, MeasureSpec.EXACTLY);
            heightMeasureSpec = MeasureSpec.makeMeasureSpec(getMeasuredHeight(), MeasureSpec.EXACTLY);
        } else {
            heightMeasureSpec = widthMeasureSpec = MeasureSpec.makeMeasureSpec(childWidthSize, MeasureSpec.EXACTLY);
        }
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }
}
