package com.kaolafm.kradio.lib.utils;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.NetworkInfo.State;
import android.util.Log;

import com.kaolafm.kradio.lib.R;
import com.kaolafm.kradio.lib.base.flavor.NetworkStatusInter;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.opensdk.player.logic.util.UIThreadUtil;

public class NetworkUtil {

    private static final String TAG = "APK_NetworkUtil";


    /**
     * Returns whether the network is available
     */
    private static boolean isNetworkAvailableDefault(Context context) {

        try {
            ConnectivityManager connectivity = (ConnectivityManager) context.getApplicationContext().getSystemService(Context.CONNECTIVITY_SERVICE);

            if (connectivity == null) {
                Log.i(TAG, "Get ConnectivityManager Service failed");
            } else {
                NetworkInfo infos = connectivity.getActiveNetworkInfo();
                Log.i(TAG, "isNetworkAvailableDefault infos = " + infos);
                if (infos == null) {
                    return false;
                }
                boolean isConnected = infos.isConnected();
                // ConnectivityManager.TYPE_NONE(-1), ConnectivityManager.TYPE_MOBILE(0), ConnectivityManager.TYPE_WIFI(1) ...
                int netType = infos.getType();
                Log.i(TAG, "isNetworkAvailableDefault isConnected = " + isConnected + ", netType = " + netType);
                if (isConnected) {
                    int networkState = NetworkManager.getInstance().getNetworkState();
                    Log.i(TAG, "isNetworkAvailableDefault networkState = " + networkState);
                    return networkState != NetworkManager.NETWORK_NO;
                }
            }
        } catch (Throwable e) {
            Log.e(TAG, "Check network status failed.", e);
        }
        return false;
    }

    /**
     * Returns whether the network is available
     */
    public static boolean isNetworkAvailableWidthDefaultToast(Context context) {
        return isNetworkAvailable(context, true);
    }


    /**
     * 检测当前网络是否连接
     *
     * @param context
     * @param bNoNetToast 是否需要弹出默认Toast true为是，false为否
     * @return
     */
    public static boolean isNetworkAvailable(Context context, boolean bNoNetToast) {
        boolean networkUsable = false;

        NetworkStatusInter networkStatusInter = ClazzImplUtil.getInter("NetworkStatusInterImpl");

        if (networkStatusInter != null) {
            networkUsable = networkStatusInter.getNetStatus(context);
        } else {
            networkUsable = isNetworkAvailableDefault(context);
        }

        if (!networkUsable && bNoNetToast) {
            UIThreadUtil.runUIThread(() -> ToastUtil.showError(context, R.string.no_net_work_str));
        }
        return networkUsable;
    }


    public static boolean isNetworkAvailable(Context context) {
        return isNetworkAvailable(context, false);
    }


    public static State getState(Context context) {
        NetworkInfo info = ((ConnectivityManager) context.getApplicationContext().getSystemService(Context.CONNECTIVITY_SERVICE))
                .getActiveNetworkInfo();
        if (info == null) {
            return NetworkInfo.State.UNKNOWN;
        }
        return info.getState();
    }

}
