package com.kaolafm.kradio.common.helper;

import android.util.Log;

import com.kaolafm.kradio.lib.bean.SubscribeData;
import com.kaolafm.kradio.lib.utils.AppExecutors;
import com.kaolafm.kradio.component.DynamicComponent;
import com.kaolafm.kradio.component.RealCaller;

import java.util.List;

public abstract class SubscribeChangeListenerComponent implements DynamicComponent {
    public static final String NAME = "SubscribeChangeListenerComponent";
    public static final String FUN_NAME = "onSubscribesChanged";
    private static final String TAG = "subscribe";

    private long listenerId;

    public SubscribeChangeListenerComponent() {
        this.listenerId = System.currentTimeMillis();
    }

    @Override
    public String getName() {
        return String.valueOf(listenerId);
    }

    @Override
    public boolean onCall(RealCaller caller) {
        String actionName = caller.actionName();
        Log.i(TAG, "[SubscribeChangeListenerComponent]动态组件收到回调: actionName=" + actionName);

        if (FUN_NAME.equals(actionName)) {
            List<SubscribeData> subscribes = caller.getParamValue("resultOfOnSubscribesChanged");
            AppExecutors.getInstance().mainThread().execute(() -> onSubscribesChanged(subscribes));
        }
        return false;
    }

    protected abstract void onSubscribesChanged(List<SubscribeData> subscribes);
}