package com.kaolafm.kradio.flavor.impl;


import android.content.Context;

import com.kaolafm.kradio.lib.base.flavor.KRadioPicSettingInter;
import com.kaolafm.kradio.lib.utils.UrlUtil;

public final class KRadioPicSettingImpl implements KRadioPicSettingInter {
    private final static String TAG = "KRadioPicSettingImpl";
    private final static String FIRSTPAGE_PICSIZE = "w_200,h_200";
    private final static String ALLCATEGERY_PICSIZE = "w_100,h_100";

    @Override
    public String getHomePicUrl(String image) {
        return UrlUtil.getAssignPicUrlforWebp(FIRSTPAGE_PICSIZE, image);
    }

    @Override
    public String getAllCategoryPicUrl(String image) {
        return UrlUtil.getAssignPicUrlforWebp(ALLCATEGERY_PICSIZE, image);
    }
}
