package com.kaolafm.kradio.processor;

import com.kaolafm.kradio.component.SharedConst;
import com.squareup.javapoet.ClassName;
import com.squareup.javapoet.FieldSpec;
import com.squareup.javapoet.MethodSpec.Builder;
import com.squareup.javapoet.TypeSpec;

import java.util.ArrayList;
import java.util.Set;
import java.util.function.Consumer;

import javax.lang.model.element.Element;
import javax.lang.model.element.ElementKind;
import javax.lang.model.element.Modifier;
import javax.lang.model.element.Name;
import javax.lang.model.element.TypeElement;
import javax.lang.model.element.VariableElement;

/**
 * 将{@link SharedConst}注解的类生成对应的public常量
 * <AUTHOR>
 * @date 2019-10-23
 */
public class ConstantProcessor extends BaseProcessor {

    private static final String PACKAGE_NAME = "com.kaolafm.kradio.constant";

    private static final String CLASS_NAME_SUFFIX = "Const";

    private Name mClassName;

    public ConstantProcessor(CoreProcessor processor) {
        super(processor);
    }

    @Override
    public String getAnnotationName() {
        return SharedConst.class.getCanonicalName();
    }

    @Override
    protected void insertStatementBefore(Set<TypeElement> elements, Builder constructorMethod) {

    }

    @Override
    protected boolean validateAnnotateElement(Element element) {
        return true;
    }

    @Override
    protected String getClassName() {
        return mClassName + CLASS_NAME_SUFFIX;
    }

    @Override
    protected String getGeneratedPackageName() {
        return PACKAGE_NAME;
    }

    @Override
    boolean generateOneClass() {
        return false;
    }

    @Override
    protected void processElement(TypeElement element, Builder constructorMethod) {
        //注解所在的类名
        mClassName = element.getSimpleName();
        log("mClassName="+mClassName);
        ArrayList<FieldSpec> fieldSpecs = new ArrayList<>();
        //遍历类下的所有元素
        element.getEnclosedElements().forEach((Consumer<Element>) element1 -> {
            if (isConstant(element1)) {
                FieldSpec fieldSpec = FieldSpec
                        .builder(ClassName.get(element1.asType()), element1.getSimpleName().toString(),
                                Modifier.PUBLIC, Modifier.STATIC, Modifier.FINAL)
                        .initializer("$S",((VariableElement)element1).getConstantValue().toString())
                        .build();
                fieldSpecs.add(fieldSpec);
            }
        });
        //填一个组件名的常量
        FieldSpec name = FieldSpec
                .builder(ClassName.get(String.class), "NAME", Modifier.PUBLIC, Modifier.STATIC, Modifier.FINAL)
                .initializer("$S", mClassName)
                .build();
        fieldSpecs.add(name);

        TypeSpec.Builder typeBuilder = getTypeSpec(null).addFields(fieldSpecs);
        writeJavaFile(typeBuilder.build());
    }

    /**
     * 判断是否常量
     * @param element
     * @return
     */
    private boolean isConstant(Element element) {
//        log("element="+element);
        boolean isString = element.getKind() == ElementKind.FIELD && "java.lang.String".equals(element.asType().toString());
        if (isString) {
            Set<Modifier> modifiers = element.getModifiers();
            return modifiers.contains(Modifier.FINAL) && modifiers.contains(Modifier.STATIC);
        }
        return false;
    }
}
