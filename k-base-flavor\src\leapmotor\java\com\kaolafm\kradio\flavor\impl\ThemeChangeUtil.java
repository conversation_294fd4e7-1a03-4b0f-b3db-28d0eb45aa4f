package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.content.res.Configuration;
import android.database.ContentObserver;
import android.net.Uri;
import android.os.Handler;
import android.provider.Settings;
import android.util.Log;

import com.kaolafm.kradio.common.helper.SkinHelper;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.ConfigChangeInter;
import com.kaolafm.kradio.lib.base.flavor.UserCenterInter;

import org.greenrobot.eventbus.EventBus;

import skin.support.SkinCompatManager;

public class ThemeChangeUtil {

    private static ThemeChangeUtil instance = new ThemeChangeUtil();

    private ThemeChangeUtil() {}

    public static ThemeChangeUtil getInstance() {
        return instance;
    }

    private String TAG = "ThemeChangeUtil";

    //获取当前白天黑夜模式：0：黑夜；1：白天
    public boolean isSystemNightMode() {
        int currentMode = Settings.Global.getInt(AppDelegate.getInstance().getContext().getContentResolver(), "C11_SREENMODE", 0);
        return currentMode != 1;
    }

    public void addThemeListener(Context context) {
        context.getContentResolver().registerContentObserver(Settings.Global.getUriFor("C11_SREENMODE")
                , true
                , new ContentObserver(new Handler()) {
                    @Override
                    public void onChange(boolean selfChange, Uri uri) {
                        super.onChange(selfChange, uri);
                        changeTheme();
                    }
                });
    }

    public void changeTheme() {

        boolean isSystemNightMode = isSystemNightMode();
        boolean isAppNightMode = SkinHelper.isNightMode();
        Log.i(TAG, "changeTheme systemNightMode" + isSystemNightMode + " appNightMode" + isAppNightMode);

        if (isSystemNightMode == isAppNightMode) { //系统颜色和当前不同
            return;
        }

        if (isSystemNightMode) {
            Log.i(TAG, "changeTheme...UI_MODE: " + "NIGHT");
            SkinCompatManager.getInstance().loadSkin(SkinHelper.NIGHT_SKIN, SkinCompatManager.SKIN_LOADER_STRATEGY_ASSETS);
            SkinHelper.setDaySkinName(AppDelegate.getInstance().getContext(), SkinHelper.NIGHT_SKIN);
            postThemeChangeEvent("dark");
        } else {
            Log.i(TAG, "changeTheme...UI_MODE: " + "NORMAL");
            SkinCompatManager.getInstance().restoreDefaultTheme(); //恢复默认
            SkinHelper.setDaySkinName(AppDelegate.getInstance().getContext(), SkinHelper.DAY_SKIN);
            postThemeChangeEvent("light");
        }
    }

    private void postThemeChangeEvent(String theme) {
        UserCenterInter.ThemeChangeEvent themeChangeEvent = new UserCenterInter.ThemeChangeEvent();
        themeChangeEvent.setTheme(theme);
        EventBus.getDefault().post(themeChangeEvent);
    }
}
