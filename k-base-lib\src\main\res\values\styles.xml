<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="BaseDialogTheme" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:backgroundDimAmount">0.7</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowIsFloating">true</item>
    </style>
    <style name="DefaultAnimation" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/enter_anim</item>
        <item name="android:windowExitAnimation">@anim/exit_anim</item>
    </style>

    <style name="AppTheme" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowFullscreen">@bool/isWindowFullScreen</item>
        <item name="android:windowEnableSplitTouch">false</item>
        <item name="android:splitMotionEvents">false</item>
    </style>

    <style name="AppThemeCompat.Animation" parent="Theme.AppCompat.Light.NoActionBar">
         <item name="android:windowFullscreen">@bool/isWindowFullScreen</item>//全屏即无通知栏
        <item name="android:windowContentOverlay">@null</item>//是否有遮盖
        <item name="android:windowAnimationStyle">@style/activityDefaultAnimation</item>
    </style>

    <style name="activityDefaultAnimation" parent="@android:style/Animation.Activity">
        <item name="android:activityOpenEnterAnimation">@anim/enter_default_transition_animation</item>
        <!--<item name="android:activityOpenExitAnimation">@anim/exit_default_transition_animation</item>-->
        <!--<item name="android:activityCloseEnterAnimation">@anim/exit_default_transition_animation</item>-->
        <item name="android:activityCloseExitAnimation">@anim/exit_default_transition_animation</item>
    </style>

    <declare-styleable name="BlurView">
        <attr name="blurOverlayColor" format="color"/>
    </declare-styleable>

    <style name="Theme.FullDialog" parent="android:style/Theme.Dialog">
        <!--背景颜色及透明程度-->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!--是否有标题 -->
        <item name="android:windowNoTitle">false</item>
        <!--是否浮现在activity之上-->
        <item name="android:windowIsFloating">true</item>
        <!--是否模糊-->
        <item name="android:backgroundDimEnabled">true</item>

        <item name="android:backgroundDimAmount">0.5</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>

        <item name="android:windowFullscreen">@bool/isWindowFullScreen</item>
    </style>

    <style name="CountDownTimerDialog" parent="Theme.AppCompat.Dialog">
        <item name="android:windowFrame">@null</item><!--取消默认Dialog的windowFrame框-->
        <item name="android:windowNoTitle">true</item><!--设置无标题Dialog-->
        <item name="android:backgroundDimEnabled">true</item><!--是否四周变暗-->
        <item name="android:windowIsFloating">true</item><!-- 是否悬浮在activity上 -->
        <item name="android:windowContentOverlay">@null</item><!-- 取消默认ContentOverlay背景 -->
        <item name="android:windowBackground">@android:color/transparent</item><!--取消window默认背景 不然四角会有黑影-->
    </style>

    <attr name="srlTextColorTitle" format="color|reference" />
    <attr name="srlTextColorTime" format="color|reference" />
    <attr name="srlProgressColor" format="color|reference" />
    <attr name="srlArrowColor" format="color|reference" />
    <declare-styleable name="ColorSettableClassicsHeader">
        <attr name="srlTextColorTitle" />
        <attr name="srlTextColorTime" />
        <attr name="srlProgressColor" />
        <attr name="srlArrowColor" />
    </declare-styleable>
    <declare-styleable name="ColorSettableClassicsFooter">
        <attr name="srlTextColorTitle" />
        <attr name="srlProgressColor" />
        <attr name="srlArrowColor" />
    </declare-styleable>
    <style name="CPB" parent="android:Widget.Holo.ProgressBar">
    </style>

    <style name="CircularProgressBar" parent="CPB">
        <item name="cpb_color">@color/cpb_default_color</item>
        <item name="cpb_stroke_width">@dimen/cpb_default_stroke_width</item>
        <item name="cpb_min_sweep_angle">@integer/cpb_default_min_sweep_angle</item>
        <item name="cpb_max_sweep_angle">@integer/cpb_default_max_sweep_angle</item>
        <item name="cpb_sweep_speed">@string/cpb_default_sweep_speed</item>
        <item name="cpb_rotation_speed">@string/cpb_default_rotation_speed</item>
    </style>
</resources>