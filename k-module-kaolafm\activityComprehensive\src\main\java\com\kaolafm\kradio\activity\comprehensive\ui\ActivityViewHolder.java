package com.kaolafm.kradio.activity.comprehensive.ui;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.UrlUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.opensdk.api.activity.model.Activity;


public class ActivityViewHolder extends BaseHolder<Activity> {

    TextView titleTextView;
    TextView desTextView;
    ImageView qrCodeImage;
    TextView qrTextView;
    View qrExpireView;
    View qrExpireIcon;

    public ActivityViewHolder(View itemView) {
        super(itemView);
        titleTextView=itemView.findViewById(R.id.title_activity);
        desTextView=itemView.findViewById(R.id.des_activity);
        qrCodeImage=itemView.findViewById(R.id.qrCode_image);
        qrTextView=itemView.findViewById(R.id.qrCode_textView);
        qrExpireView=itemView.findViewById(R.id.qr_view_expire);
        qrExpireIcon=itemView.findViewById(R.id.qr_expire_icon);
    }

    @Override
    public void setupData(Activity activity, int position) {
        titleTextView.setText(activity.getName());
        desTextView.setText(activity.getDescription());

        ImageLoader.getInstance().displayImage(AppDelegate.getInstance().getContext(),
                UrlUtil.getCustomPicUrl(UrlUtil.PIC_250_250, activity.getQrCodeUrl()), qrCodeImage);
        qrTextView.setText(activity.getCodeDes());

        if (activity.getStatus() == 1) {
            qrExpireView.setVisibility(View.GONE);
            qrExpireIcon.setVisibility(View.GONE);
            titleTextView.setTextColor(ResUtil.getColor(R.color.text_color_1));
            desTextView.setTextColor(ResUtil.getColor(R.color.text_color_1));
        } else {
            qrExpireView.setVisibility(View.VISIBLE);
            qrExpireIcon.setVisibility(View.VISIBLE);
            titleTextView.setTextColor(ResUtil.getColor(R.color.text_color_2));
            desTextView.setTextColor(ResUtil.getColor(R.color.text_color_2));
        }
    }
}
