package com.kaolafm.kradio.online.player.mvp;

import android.util.Log;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.common.CP;
import com.kaolafm.kradio.common.ErrorInfo;
import com.kaolafm.kradio.common.SubscribeData;
import com.kaolafm.kradio.common.helper.SubscribeChangeListenerComponent;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.subscribe.SubscribeHelper;
import com.kaolafm.opensdk.api.media.AlbumRequest;
import com.kaolafm.opensdk.api.media.model.AlbumDetails;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.PlayerManager;

import java.util.List;

/**
 * <AUTHOR>
 */
public class OnlineRadioPlayerPresenter extends OnlinePlayerBasePresenter<OnlineRadioPlayerView> {

    private static final String TAG = "RadioPlayerPresenter";
    private SubscribeChangeListenerComponent kaolasubscribeListener;

    public OnlineRadioPlayerPresenter(OnlineRadioPlayerView view) {
        super(view);
    }
//
//    public void getLive() {
//        PlayItem playItem = PlayerManager.getInstance().getCurItem();
//        int type = playItem.getType();
//        if (type != PlayerConstants.RESOURCES_TYPE_LIVE_STREAM && type != PlayerConstants.RESOURCES_TYPE_RADIO) {
//            if (mView != null) {
//                mView.hideLiveItemWithAnimation();
//            }
//            return;
//        }
//
//        if (type == PlayerConstants.RESOURCES_TYPE_LIVE_STREAM) {
//            RadioLiveInfo radioLiveInfo = LiveInfoHelper.getInstance().getRadioLiveInfo(playItem.getRadioId());
//            mView.showLiveItem(radioLiveInfo);
//            mView.showLiveCover();
//            return;
//        }
//        getLiveFromDB();
//    }
//
//    public void getLiveFromDB() {
//        LiveStateManager.getInstance().getLivesWithRadioId(Long.parseLong(PlayerManager.getInstance().getCurItem().getRadioId()), new RadioLiveCallback() {
//            @Override
//            public void onResult(List<RadioLiveInfo> radioLiveInfos) {
//                if (mView == null) {
//                    return;
//                }
//                if (!ListUtil.isEmpty(radioLiveInfos)) {
//                    RadioLiveInfo radioLiveInfo = radioLiveInfos.get(0);
//                    mView.showLiveItem(radioLiveInfo);
//                    return;
//                }
//                mView.hideLiveItemWithAnimation();
//            }
//        });
//    }

    @Override
    protected void initSubscribeCallback() {
        if (kaolasubscribeListener == null) {
            kaolasubscribeListener = new SubscribeChangeListenerComponent() {
                @Override
                protected void onSubscribesChanged(List<SubscribeData> subscribes) {
                    Log.i("subscribe", "[动态组件]收到订阅变化:");
                    OnlineRadioPlayerPresenter.this.onSubscribesChanged(subscribes);
                }
            };
        }
        Log.i(TAG, "[RPP]初始化动态监听");
        SubscribeHelper.addSubscribeChangeListener(CP.KaoLaFM, kaolasubscribeListener);
        mSubscribeCallback = new OnlineSubscribeCallback(this) {
            @Override
            public void onResult(boolean result, int status) {
                Log.i("subscribe", "RadioPlayerPresent::onResult: status=" + status);

                OnlinePlayerBasePresenter presenter = weakReference.get();
                if (presenter == null) {
                    return;
                }
                OnlineRadioPlayerPresenter playerPresenter = (OnlineRadioPlayerPresenter) presenter;
                if (result) {
                    playerPresenter.onSubscribeSuccess(status);
                } else {
                    playerPresenter.onSubscribeError();
                }
            }

            @Override
            public void onFailure(ErrorInfo errorInfo) {

                Log.i("subscribe", "RadioPlayerPresent::onFailure: errorInfo=" + errorInfo);

                OnlinePlayerBasePresenter presenter = weakReference.get();
                if (presenter == null) {
                    return;
                }
                OnlineRadioPlayerPresenter playerPresenter = (OnlineRadioPlayerPresenter) presenter;
                playerPresenter.onSubscribeError();
            }
        };
        mUnSubscribeCallback = new OnlineSubscribeCallback(this) {
            @Override
            public void onResult(boolean result, int status) {
                OnlinePlayerBasePresenter presenter = weakReference.get();
                if (presenter == null) {
                    return;
                }
                OnlineRadioPlayerPresenter playerPresenter = (OnlineRadioPlayerPresenter) presenter;
                if (result) {
                    playerPresenter.onUnSubscribeSuccess(status);
                } else {
                    playerPresenter.onUnSubscribeError();
                }
            }

            @Override
            public void onFailure(ErrorInfo errorInfo) {
                OnlinePlayerBasePresenter presenter = weakReference.get();
                if (presenter == null) {
                    return;
                }
                OnlineRadioPlayerPresenter playerPresenter = (OnlineRadioPlayerPresenter) presenter;
                playerPresenter.onUnSubscribeError();
            }
        };
    }

    AlbumRequest albumRequest;

    /**
     * 购买信息刷新
     */
    public void getPayInfo(long albumId) {
        getPayInfo(albumId, false);
    }

    public void getPayInfo(long albumId, boolean needBuy) {
        if (albumRequest == null) {
            albumRequest = new AlbumRequest().setTag(TAG);
        }
        Log.d(TAG, "initPayInfo albumId: " + albumId);
        albumRequest.getAlbumDetails(albumId, new HttpCallback<AlbumDetails>() {
            @Override
            public void onSuccess(AlbumDetails albumDetails) {
                Log.d(TAG, "initPayInfo result albumId: " + albumId+" , player album id "+PlayerManager.getInstance().getCurPlayItem().getAlbumId());
                if (mView != null) {
                    mView.showPayInfo(albumDetails, needBuy);
                }
            }

            @Override
            public void onError(ApiException e) {

            }
        });
    }

    private void onSubscribeSuccess(int status) {
        if (mView == null) {
            return;

        }
        mView.onSubscribeSuccess(status);
    }

    private void onSubscribeError() {
        Log.i(TAG, "onSubscribeError mView: " + mView);
        if (mView == null) {
            return;
        }
        mView.onSubscribeError();
    }

    private void onUnSubscribeSuccess(int status) {
        if (mView == null) {
            return;

        }
        mView.onUnSubscribeSuccess(status);
    }

    private void onUnSubscribeError() {
        Log.i(TAG, "onSubscribeError mView: " + mView);
        if (mView == null) {
            return;
        }
        mView.onUnSubscribeError();
    }

    @Override
    public void destroy() {
        super.destroy();
        SubscribeHelper.removeSubscribeChangeListener(CP.KaoLaFM, kaolasubscribeListener);
    }

    public void onSubscribesChanged(List<SubscribeData> subscribes) {
        Log.i(TAG, "RadioPlayerPresenter收到订阅变化:");
        boolean sub = false;
        if (!ListUtil.isEmpty(subscribes)) {
            for (SubscribeData s : subscribes) {
                if (s.getId() == PlayerManagerHelper.getInstance().getSubscribeId()) {
                    sub = true;
                    break;
                }
            }
        }
        updateSubscribe(sub);
    }
}
