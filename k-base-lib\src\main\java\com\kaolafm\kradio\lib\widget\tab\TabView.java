package com.kaolafm.kradio.lib.widget.tab;

import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import androidx.appcompat.widget.AppCompatTextView;
import android.text.TextPaint;
import android.util.AttributeSet;
import android.util.TypedValue;

/**
 * <AUTHOR>
 * @date 2019-11-27
 */
public class TabView extends AppCompatTextView {

    private boolean mCanAnimator;

    private ValueAnimator mValueAnimator;

    public TabView(Context context) {
        this(context, null);
    }

    public TabView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public TabView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mValueAnimator = new ValueAnimator();
        mValueAnimator.addUpdateListener(animation -> {
            super.setTextSize(TypedValue.COMPLEX_UNIT_PX, (Integer) animation.getAnimatedValue());
        });
        mValueAnimator.setDuration(SlidingTabLayout.ANIMATION_DURATION);
    }

    public void setCanAnimator(boolean canAnimator) {
        mCanAnimator = canAnimator;
    }

    @Override
    protected void onDraw(Canvas canvas) {

        //获取当前控件的画笔
        TextPaint paint = getPaint();
        //设置画笔的描边宽度值
        paint.setStrokeWidth(mStrokeWidth);
        paint.setStyle(Paint.Style.FILL_AND_STROKE);

        super.onDraw(canvas);
    }

    private float mStrokeWidth = 0f;

    public void setFontWidth(float fontWidth) {
        this.mStrokeWidth = fontWidth;
        invalidate();
    }

    @Override
    public void setTextSize(int unit, float size) {
        if (getTextSize() != size) {
            if (mCanAnimator && mValueAnimator != null) {
                mValueAnimator.setIntValues((int) getTextSize(), (int) size);//使用float 会有抖动，用的px，直接转成int
                mValueAnimator.start();
            } else {
                super.setTextSize(unit, size);
            }
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (mValueAnimator != null) {
            mValueAnimator.removeAllUpdateListeners();
            mValueAnimator = null;
        }
    }
}
