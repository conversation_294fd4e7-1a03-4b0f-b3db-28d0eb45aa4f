package com.kaolafm.ad.comprehensive.ads.image;

import android.app.Activity;
import android.content.Context;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.View;

import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.kradio.player.router.RouterDesturlPlayCallbackImpl;

public class AdInteractWithWebviewContentView extends AdInteractContentView {

    private RouterDesturlPlayCallbackImpl routerDesturlPlayCallback = new RouterDesturlPlayCallbackImpl();

    public AdInteractWithWebviewContentView(Context context) {
        super(context);
    }

    public AdInteractWithWebviewContentView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public AdInteractWithWebviewContentView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected void onViewClick(View view) {
        super.onViewClick(view);
        if (!showWeb(getContext())) {
            Activity topActivity = AppManager.getInstance().getTopActivity();
            showWeb(topActivity);
        }
    }

    private boolean showWeb(Context context) {
        if (context != null) {
            Logger.e("AdInteractWithWebviewContentView", "url=" + mDestUrl);
            if (!RouterManager.getInstance().interceptApplicationJumpEvent(context, mDestUrl, routerDesturlPlayCallback)) {
                //不属于应用内跳转
                WebViewActivity.start(context, mDestUrl);
            }
            return true;
        }
        return false;
    }
}
