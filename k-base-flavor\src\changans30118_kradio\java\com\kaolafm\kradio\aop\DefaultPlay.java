package com.kaolafm.kradio.aop;

import android.util.Log;

import com.kaolafm.kradio.network.FlavorApiRequest;
import com.kaolafm.kradio.network.model.DefaultPlayData;

import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.PlayerManager;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;

@Aspect
public class DefaultPlay {
    private boolean isFirstTime = true;
    private FlavorApiRequest mFlavorApiRequest;

    @Around("execution(* com.kaolafm.opensdk.player.logic.PlayerManager.start(..))")
    public void defaultPlayerManager(ProceedingJoinPoint point) throws Throwable {
        Log.e("DefaultPlay", "defaultPlayerManager: isFirstTime = " + isFirstTime);
        if (isFirstTime) {
            mFlavorApiRequest = FlavorApiRequest.getInstance();
            mFlavorApiRequest.getDefaultPlayInfo(new HttpCallback<DefaultPlayData>() {
                @Override
                public void onSuccess(DefaultPlayData defaultPlayData) {
                    if (defaultPlayData == null) {
                        try {
                            point.proceed();
                        } catch (Throwable throwable) {
                            throwable.printStackTrace();
                        }
                        return;
                    }
                    Log.e("DefaultPlay", "getDefaultPlay onSuccess: " + defaultPlayData.getName() + "        " + defaultPlayData.getId() + "        " + defaultPlayData.getType());
                    PlayerManagerHelper.getInstance().start(String.valueOf(defaultPlayData.getId()), defaultPlayData.getType());
                }

                @Override
                public void onError(ApiException e) {
                    Log.e("DefaultPlay", "getDefaultPlay onError: " + e.toString());
                    try {
                        point.proceed();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                    }
                }
            });
        } else {
            point.proceed();
        }
        isFirstTime = false;
    }
}
