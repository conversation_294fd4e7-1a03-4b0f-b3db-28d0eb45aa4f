package com.kaolafm.kradio.flavor.media;

import android.content.Context;
import android.content.Intent;
import android.media.session.MediaSession;
import android.media.session.PlaybackState;
import android.nfc.Tag;
import android.os.Build;
import android.os.Bundle;
import android.os.ResultReceiver;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import android.util.Log;
import android.view.KeyEvent;

import com.kaolafm.utils.MediaButtonManagerUtil;

/**
 * @Package: com.kaolafm.kradio.flavor.media
 * @Description:
 * @Author: Maclay
 * @Date: 16:23
 */
public class MyMediaSessionManager {
    private static volatile MyMediaSessionManager myMediaSessionManager;
    private static final int POSITION = 1;
    private Context context;
    private MediaSession mediaSession;

    // 获取实例
    public static MyMediaSessionManager getInstance() {
        if (myMediaSessionManager == null) {
            synchronized (MyMediaSessionManager.class) {
                if (myMediaSessionManager == null) {
                    myMediaSessionManager = new MyMediaSessionManager();
                }
            }
        }
        return myMediaSessionManager;
    }

    // 初始化 mediasession
    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public void initMediaSession(Context context) {
        this.context = context;
        if (mediaSession == null) {
            mediaSession = new MediaSession(context, "cmgyunting.vehicleplayer.cnr");
        } else {
            Log.d("MyMediaSessionManager", "mediaSession is inited");
        }
        MediaButtonManagerUtil mMediaButtonManagerUtil = new MediaButtonManagerUtil();
        Log.d("MyMediaSessionManager", "initMediaSession ");
        mediaSession.setCallback(new MediaSession.Callback() {
            @Override
            public void onCommand(@NonNull String command, @Nullable Bundle args,
                                  @Nullable ResultReceiver cb) {
                super.onCommand(command, args, cb);
            }

            @Override
            public boolean onMediaButtonEvent(@NonNull Intent mediaButtonIntent) {
                if (!Intent.ACTION_MEDIA_BUTTON.equals(mediaButtonIntent.getAction())) {
                    return super.onMediaButtonEvent(mediaButtonIntent);
                }
                KeyEvent key = mediaButtonIntent.getParcelableExtra(Intent.EXTRA_KEY_EVENT);
                // 解决https://app.huoban.com/tables/2100000007530121/items/2300001928514035?userId=1229522问题
                if (key == null || key.getRepeatCount() > 0) {
                    return super.onMediaButtonEvent(mediaButtonIntent);
                }
                int keyAction = key.getAction();
                if (keyAction == KeyEvent.ACTION_UP) {
                    return super.onMediaButtonEvent(mediaButtonIntent);
                }
                int keyCode = key.getKeyCode();
                mMediaButtonManagerUtil.manageMediaButtonClick(keyCode);
                return true;
            }
        });
        mediaSession.setActive(true);
    }

    // 更新媒体当前播放状态
    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public void updateMediaSession(int state) {
        if (mediaSession == null) {
            return;
        }
        mediaSession.setPlaybackState(
                new PlaybackState.Builder()
                        .setState(state, POSITION, 0.1f)
                        .build());
    }

    // 释放 mediasession
    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public void releaseMediaSession() {
        Log.d("MyMediaSessionManager", "release begin");
        if (this.mediaSession == null) {
            return;
        }
        this.mediaSession.setActive(false);
        Log.d("MyMediaSessionManager", "do release");
        this.mediaSession.release();
        Log.d("MyMediaSessionManager", "set null");
        this.mediaSession = null;
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public static void destroyInstance() {
        if (myMediaSessionManager != null) {
            synchronized (MyMediaSessionManager.class) {
                if (myMediaSessionManager != null) {
                    myMediaSessionManager.releaseMediaSession();
                    myMediaSessionManager = null;
                }
            }
        }
    }
}
