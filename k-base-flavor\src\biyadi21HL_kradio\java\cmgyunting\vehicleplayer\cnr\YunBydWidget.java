package cmgyunting.vehicleplayer.cnr;

import android.appwidget.AppWidgetManager;
import android.appwidget.AppWidgetProvider;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;


import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.service.BYDWidgetService;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;


/**
 * Implementation of App Widget functionality.
 */
public class YunBydWidget extends AppWidgetProvider {
    private static final String TAG = "k.byd.klaw";

    @Override
    public void onReceive(Context context, Intent intent) {
        Log.d(TAG, "onReceive----->" + intent.getAction());
        if (Constants.APPEXIT_ACTION.equals(intent.getAction())) {
            AppWidgetManager appWidgetManager = AppWidgetManager.getInstance(context);
            ComponentName componentName = new ComponentName(context, YunBydWidget.class);
            for (int appWidgetId : appWidgetManager.getAppWidgetIds(componentName)) {
                updateAppWidget(context, appWidgetManager, appWidgetId, Constants.APPEXIT_ACTION);
            }
        } else {
            super.onReceive(context, intent);
        }
    }

    @Override
    public void onEnabled(Context context) {
        Log.d(TAG, "onEnable ----- ");
        Intent intent = new Intent(context, BYDWidgetService.class);
        intent.setAction(BYDWidgetService.WIDGET_ACTION_REFRESH);
        context.startService(intent);
    }

    @Override
    public void onUpdate(Context context, AppWidgetManager appWidgetManager, int[] appWidgetIds) {
        Log.d(TAG, "onUpdate ----- appwidgetId.size appWidgetIds size = " + appWidgetIds.length + "-----appWidgetIds = " + appWidgetIds);
        for (int appWidgetId : appWidgetIds) {
            updateAppWidget(context, appWidgetManager, appWidgetId, "android.appwidget.action.APPWIDGET_UPDATE");
        }
    }

    @Override
    public void onDisabled(Context context) {
        Log.d(TAG, "onDisabled ----- ");
        Intent intent = new Intent(context, BYDWidgetService.class);
        intent.setAction(BYDWidgetService.WIDGET_ACTION_REFRESH);
        context.startService(intent);
    }

    @Override
    public void onAppWidgetOptionsChanged(Context context, AppWidgetManager appWidgetManager, int appWidgetId, Bundle newOptions) {
        super.onAppWidgetOptionsChanged(context, appWidgetManager, appWidgetId, newOptions);
    }

    void updateAppWidget(final Context pContext, AppWidgetManager appWidgetManager, final int appWidgetId, String action) {
        Log.d(TAG, "updateAppWidget ----- appWidgetId:" + appWidgetId);
//        PlayItem playItem = PlayerManager.getInstance().getCurrentPlayItem();
//        if (Constants.APPEXIT_ACTION.equals(action) || playItem == null) {
//            RemoteViews views = new RemoteViews(pContext.getPackageName(), BYDWidgetService.getRemoteViewsLayoutId());
//            views.setTextViewText(R.id.widget_audio_name, "");
//            views.setTextViewText(R.id.widget_album_name, "");
//            views.setImageViewBitmap(R.id.widget_cover,null);
//            views.setImageViewBitmap(R.id.widget_blur_imageview,null);
//            views.setImageViewBitmap(R.id.widget_broadcast_label,null);
//            views.setImageViewResource(R.id.widget_play_or_pause, R.drawable.selector_widget_btn_pause);
//            views.setProgressBar(R.id.widget_progressBar,0,0,false);
//            views.setTextViewText(R.id.widget_duration, "");
//            views.setTextViewText(R.id.widget_cur_time,  "");
//            views.setTextViewText(R.id.widget_broadcast_label_textview,  "");
//            views.setImageViewResource(R.id.widget_collection,  R.drawable.selector_widget_btn_uncollection);
//            views.setOnClickPendingIntent(R.id.widget_playinfo_layout, PendingIntent.getActivity(MyApplication.mContext, 0, new Intent(MyApplication.mContext, HomeActivity.class), 0));
//            appWidgetManager.updateAppWidget(appWidgetId, views);
//        } else {
        Intent intent = new Intent(pContext, BYDWidgetService.class);
//        if (Constants.APPEXIT_ACTION.equals(action) || playItem == null) {
//            intent.setAction(BYDWidgetService.WIDGET_ACTION_EXIT);
//        } else {
//        }
        intent.setAction(BYDWidgetService.WIDGET_ACTION_REFRESH);
        pContext.startService(intent);
    }
}

