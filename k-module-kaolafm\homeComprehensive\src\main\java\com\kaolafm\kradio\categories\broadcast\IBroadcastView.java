package com.kaolafm.kradio.categories.broadcast;

import com.kaolafm.kradio.lib.base.mvp.IView;
import com.kaolafm.kradio.lib.bean.BroadcastRadioDetailData;

import java.util.List;

/**
 * Created by kaolafm on 2018/4/25.
 */

public interface IBroadcastView extends IView{

    /**
     * 数据更新
     * @param resultList
     * @param isLoadMore 是否是加载更多，true是
     */
    void notifyDataChange(List<BroadcastRadioDetailData> resultList, boolean isLoadMore);

    /**
     * 显示错误，包括无数据
     */
    void showError();

    void showImage(long broadcastId, String programImg,String desc);
}
