package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.content.DialogInterface;
import android.view.Gravity;

import com.kaolafm.kradio.flavor.view.CustomAlertDialog;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.base.flavor.KRadioPermissionInter;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-11-06 17:11
 ******************************************/
public class KRadioPermissionImpl implements KRadioPermissionInter {

    static int dialogWhich = 0;

    @Override
    public boolean onPermissionDenied(Object... args) {
        //AppManager.getInstance().appExit();
        Context context = (Context)args[0];

//        AlertDialog.Builder builder = new AlertDialog.Builder(context, R.style.AlertDialog);
//        builder.setCancelable(false);
//        builder.setPositiveButton(context.getResources().getString(com.kaolafm.kradio.k_kaolafm.R.string.go_setpermission), new DialogInterface.OnClickListener() {
//            @Override
//            public void onClick(DialogInterface dialog, int which) {
//                AppManager.getInstance().appExit();
//            }
//        });
//        AlertDialog alertDialog = builder.create();
//        TextView msg = new TextView(context);
//        msg.setText(context.getResources().getString(com.kaolafm.kradio.k_kaolafm.R.string.permissions_app_tip));
//        msg.setPadding(10, 10, 10, 10);
//        msg.setGravity(Gravity.CENTER);
//        msg.setTextSize(24);
//        alertDialog.setView(msg);
//
//        TextView title = new TextView(context);
//        title.setText(context.getResources().getString(com.kaolafm.kradio.k_kaolafm.R.string.permissions_tip));
//        title.setPadding(10, 20, 10, 10);//边距
//        title.setGravity(Gravity.CENTER);//位置
//        title.setTextSize(24);//字体的大小
//        alertDialog.setCustomTitle(title);//设置字体
//        alertDialog.show();
//        Button button = alertDialog.getButton(AlertDialog.BUTTON_POSITIVE);
//        LinearLayout.LayoutParams cancelBtnPara = (LinearLayout.LayoutParams) button.getLayoutParams();
//        //设置按钮的大小
//        cancelBtnPara.height = LinearLayout.LayoutParams.WRAP_CONTENT;
//        cancelBtnPara.width = LinearLayout.LayoutParams.MATCH_PARENT;
//        //设置按钮居中
//        cancelBtnPara.gravity = Gravity.RIGHT;
//        //设置按钮左上右下的距离
//        cancelBtnPara.setMargins(100, 20, 100, 20);
//        button.setLayoutParams(cancelBtnPara);
//        //设置文字居中
////        button.setGravity(Gravity.CENTER);
//        button.setTextSize(24);
//        button.setTextColor(context.getResources().getColor(R.color.text_color_1));
//
//        //
//        int strokeWidth = 1; // 1dp 边框宽度
//        int roundRadius = 3; // 2dp 圆角半径
//        int strokeColor = Color.parseColor("#FFFFFF");//边框颜色
//        int fillColor = Color.parseColor("#333333");//内部填充颜色
//        GradientDrawable gd = new GradientDrawable();//创建drawable
//        gd.setColor(fillColor);
//        gd.setCornerRadius(roundRadius);
//        gd.setStroke(strokeWidth, strokeColor);
//        button.setBackground(gd);

//        DialogUtil.showAlertDialog(context,
//                context.getResources().getString(com.kaolafm.kradio.k_kaolafm.R.string.permissions_tip),
//                context.getResources().getString(com.kaolafm.kradio.k_kaolafm.R.string.permissions_app_tip),
//                Gravity.CENTER,
//                "",
//                "知道了",
//                false,
//                null,
//                new DialogUtil.NegativeBtnClickListener() {
//                    @Override
//                    public void click() {
//                        AppManager.getInstance().appExit();
//                    }
//                });

        CustomAlertDialog.Builder builder = new CustomAlertDialog.Builder(context);
        builder.setCancelable(false);
        builder.setCanceledOnTouchOutside(false);
        builder.setTitle(context.getResources().getString(com.kaolafm.kradio.k_kaolafm.R.string.permissions_tip));
        builder.setMessage(context.getResources().getString(com.kaolafm.kradio.k_kaolafm.R.string.permissions_app_tip));
        builder.setGravity(Gravity.CENTER);
//        builder.setPositiveButton("确定", new DialogInterface.OnClickListener() {
//            public void onClick(DialogInterface dialog, int which) {
//                dialog.dismiss();
//                //设置你的操作事项
//                AppManager.getInstance().appExit();
//            }
//        });

        builder.setNegativeButton("知道了",
                new android.content.DialogInterface.OnClickListener() {
                    public void onClick(DialogInterface dialog, int which) {
                        AppManager.getInstance().appExit();
                    }
                });

        builder.create().show();

        return true;
    }
}
