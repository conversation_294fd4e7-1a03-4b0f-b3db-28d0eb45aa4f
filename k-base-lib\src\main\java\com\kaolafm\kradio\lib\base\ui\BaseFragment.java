package com.kaolafm.kradio.lib.base.ui;

import android.app.Activity;
import android.content.Context;
import android.content.res.Configuration;
import android.content.res.Resources.NotFoundException;
import android.os.Bundle;
import android.os.Handler;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.kaolafm.kradio.lib.BuildConfig;
import com.kaolafm.kradio.lib.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.arouter.ARouterBaseFragment;
import com.kaolafm.kradio.lib.base.flavor.KRadioFragmentPaddingInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioVehicleSafetyCallback;
import com.kaolafm.kradio.lib.base.lifecycle.FragmentLifecycleable;
import com.kaolafm.kradio.lib.base.mvp.IFragment;
import com.kaolafm.kradio.lib.base.mvp.IPresenter;
import com.kaolafm.kradio.lib.sdk.utils.PerformanceSettingMananger;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.MultiUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.PageShowReportEvent;
import com.kaolafm.report.util.ReportConstants;
import com.trello.rxlifecycle3.LifecycleProvider;
import com.trello.rxlifecycle3.LifecycleTransformer;
import com.trello.rxlifecycle3.RxLifecycle;
import com.trello.rxlifecycle3.android.FragmentEvent;
import com.trello.rxlifecycle3.android.RxLifecycleAndroid;

import java.lang.ref.WeakReference;
import java.util.Objects;

import io.reactivex.Observable;
import io.reactivex.subjects.BehaviorSubject;
import io.reactivex.subjects.Subject;
import me.yokeyword.fragmentation.anim.DefaultNoAnimator;
import me.yokeyword.fragmentation.anim.FragmentAnimator;

/**
 * Fragment基类<br/>
 * 如果要使用EventBus只要{@link #useEventBus()}返回true。默认是不使用。<br/>
 *
 * <AUTHOR>
 * @date 2018/4/13
 */

public abstract class BaseFragment<P extends IPresenter> extends ARouterBaseFragment
        implements IFragment, FragmentLifecycleable, LifecycleProvider<FragmentEvent>, KRadioVehicleSafetyCallback {

    private static final String TAG = "FragmentName";
    private final BehaviorSubject<FragmentEvent> mLifecycleSubject = BehaviorSubject.create();

    protected P mPresenter;

    protected BaseHandler mHandler;

    public boolean isInMultiWindowMode = false;

    protected long startTime = -1;
    private boolean isCanShowing = true;
    private boolean isFirstSetUserVisibleHint = false;
    private View backView;

    @NonNull
    @Override
    public Subject<FragmentEvent> provideLifecycleSubject() {
        return mLifecycleSubject;
    }

    @NonNull
    @Override
    public Observable<FragmentEvent> lifecycle() {
        return mLifecycleSubject.hide();
    }

    @NonNull
    @Override
    public <T> LifecycleTransformer<T> bindUntilEvent(FragmentEvent event) {
        return RxLifecycle.bindUntilEvent(mLifecycleSubject, event);
    }

    @NonNull
    @Override
    public <T> LifecycleTransformer<T> bindToLifecycle() {
        return RxLifecycleAndroid.bindFragment(mLifecycleSubject);
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mHandler = new BaseHandler(this);
        initArgs();
        mPresenter = createPresenter();
        if (mPresenter != null) {
            getLifecycle().addObserver(mPresenter);
        }
        Log.i(TAG, "onCreate " + getClass().getSimpleName() + " =======>> " + getActivity().getClass().getSimpleName());
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        this.isInMultiWindowMode = MultiUtil.isInMultiWindowMode;
        View view;
        //如果通过布局id找不到view，就尝试直接获取view。
        try {
            int layoutId = 0;
            if (BuildConfig.LAYOUT_TYPE == 0) {
                layoutId = getLayoutId();
            } else if (BuildConfig.LAYOUT_TYPE == 1) {
                layoutId = getLayoutId_Tow();
                if (layoutId == 0) {
                    layoutId = getLayoutId();
                }
            }
            view = inflater.inflate(layoutId, container, false);
        } catch (NotFoundException e) {
            view = getFragmentView();
        }
        //消费事件, 防止touch事件传递到地图
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001276971566?userId=1229522问题
//        view.setOnClickListener(v -> {
//        });
        initView(view);
        backView = view.findViewById(R.id.backView);
        if (backView != null) {
            if (!backView.hasOnClickListeners())
                backView.setOnClickListener(this::clickBack);
            updateBackView();
        }
        changeViewLayoutForStatusBar(view);
        addFragmentRootViewPadding(view);
        return view;
    }

    private void updateBackView() {
        if (autoSetBackViewMarginLeft() && backView != null) {
            ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) backView.getLayoutParams();
            layoutParams.leftMargin = ScreenUtil.getGlobalBackMarginLeft(backView, ResUtil.getOrientation());
        }
    }


    protected boolean autoSetBackViewMarginLeft() {
        return true;
    }


    private void clickBack(View v) {
        if (AntiShake.check(v.getId())) {
            return;
        }
        pop();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        synchronized (TAG) {
            showAccordingToScreen(ResUtil.getOrientation());
        }
    }

    @Override
    public void initArgs() {
    }

    protected View getFragmentView() {
        return null;
    }

    /**
     * 获取fragment的布局id
     */
    protected abstract int getLayoutId();

    protected abstract int getLayoutId_Tow();

    /**
     * 创建当前view对应的presenter
     */
    protected abstract P createPresenter();


    @Override
    public boolean useEventBus() {
        return false;
    }


    protected void showLoading() {

    }

    protected void hideLoading() {

    }

    /**
     * 弹toast显示错误信息
     *
     * @param msg
     */
    protected void showErrorToast(String msg) {
        ToastUtil.showError(AppDelegate.getInstance().getContext(), msg);
    }

    /**
     * 弹toast显示错误信息
     *
     * @param resId
     */
    protected void showErrorToast(int resId) {
        showErrorToast(ResUtil.getString(resId));
    }

    protected void showErrorToast(Exception excetion) {
        showErrorToast(excetion.getMessage());
    }


    @Override
    public void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        Log.i(TAG, "onSaveInstanceState start " + this);
    }

    @Override
    public void onViewStateRestored(@Nullable Bundle savedInstanceState) {
        super.onViewStateRestored(savedInstanceState);
        Log.i(TAG, "onSaveInstanceState start " + this);
    }

    @Override
    public void onPause() {
        super.onPause();
        Log.i(TAG, isCanShowing + ":onPause start " + this);
        if (isCanShowing || !isFirstSetUserVisibleHint) {
            reportPageShowEvent();
        }
    }

    @Override
    public void onStop() {
        super.onStop();
        Log.i(TAG, "onStop start " + this);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        Log.i(TAG, "onDestroyView start " + this);
    }

    @Override
    public void onDestroy() {
        // 先保存 presenter 引用，避免在 super.onDestroy() 过程中被其他回调访问到 null
        P presenterToDestroy = mPresenter;
        mPresenter = null; // 立即置空，防止后续访问

        if (presenterToDestroy != null) {
            presenterToDestroy.destroy();
        }

        super.onDestroy();
        mHandler.removeCallbacksAndMessages(null);
        _mActivity = null;
        Log.i(TAG, "onDestroy start " + this);
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
    }

    @Override
    public void onDetach() {
        super.onDetach();
    }

    protected static class BaseHandler extends Handler {
        private WeakReference<BaseFragment> mReference;

        protected BaseHandler(BaseFragment fragment) {
            mReference = new WeakReference<>(fragment);
        }
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        Log.i(TAG, "onHiddenChanged,hidden = " + hidden + " fragment = " + this.getClass().getSimpleName());
//        ViewUtil.setViewVisibility(getView(), hidden ? View.GONE : View.VISIBLE);
        Log.i(TAG, "onHiddenChanged=" + hidden);
        isCanShowing = !hidden;
        onVisibleChanged(isVisibleOnScreen());
    }

    @Override
    public FragmentAnimator onCreateFragmentAnimator() {
        boolean isNeedAnimation = PerformanceSettingMananger.getInstance().getIsNeedAnimation();
        Log.i(TAG, "isNeedAnimation:" + isNeedAnimation);
        if (isNeedAnimation) {
            return new FragmentAnimator(R.anim.enter_default_transition_animation, R.anim.exit_default_transition_animation, R.anim.from_exit_default_transition_animation,
                    R.anim.from_enter_default_transition_animation);
        } else {
            return new DefaultNoAnimator();
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        String pageId = getPageId();
        if (!StringUtil.isEmpty(pageId)) {
            Log.i(ReportConstants.REPORT_TAG, getClass().getSimpleName() +
                    " ,设置pageid= " + pageId);
            ReportHelper.getInstance().setPage(pageId);
        }

        FragmentActivity activity = getActivity();
        if (activity == null) return;
        FragmentActivity fragmentActivity = Objects.requireNonNull(activity);
        Class<? extends BaseFragment> aClass = getClass();
        Class<? extends FragmentActivity> bClass = fragmentActivity.getClass();
        Log.i(TAG, "onResume " + aClass.getSimpleName() + " =======>> " + bClass.getSimpleName());
//        Toast.makeText(this.getActivity(), "This is " + getClass().getSimpleName(), Toast.LENGTH_SHORT).show();
        isCanShowing = isVisibleOnScreen();
        startTime = System.currentTimeMillis();
    }

    @Override
    public void onMultiWindowModeChanged(boolean isInMultiWindowMode) {
        super.onMultiWindowModeChanged(isInMultiWindowMode);
        this.isInMultiWindowMode = isInMultiWindowMode;
    }

    /**
     * 获取pageid
     *
     * @return
     */
    public String getPageId() {
        return Constants.BLANK_STR;
    }

    /**
     * 沉浸式状态栏针对Fragment根布局做padding定制（如果BaseFragment子类无需添加padding可重写此函数）
     *
     * @param view
     */
    protected void changeViewLayoutForStatusBar(View view) {
        Activity activity = getActivity();
        if (activity instanceof BaseActivity) {
            ((BaseActivity) activity).changeViewLayoutForStatusBar(view);
        }
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        synchronized (TAG) {
            showAccordingToScreen(ResUtil.getOrientation());
        }
        setReportLandOrPortrait(ResUtil.getOrientation());
    }

    protected void showAccordingToScreen(int orientation) {
    }

    private void setReportLandOrPortrait(int orientation) {
        if (orientation == Configuration.ORIENTATION_PORTRAIT) {
            ReportHelper.getInstance().setLandOrPortrait(ReportConstants.ORIENTATION_PORTRAIT);
        } else {
            ReportHelper.getInstance().setLandOrPortrait(ReportConstants.ORIENTATION_LANDSCAPE);
        }
    }

    protected void addFragmentRootViewPadding(View view) {
        KRadioFragmentPaddingInter kRadioFragmentPaddingInter = ClazzImplUtil.getInter("KRadioFragmentPaddingImpl");
        if (kRadioFragmentPaddingInter != null) {
            kRadioFragmentPaddingInter.doFragmentPadding(view);
        }
    }

    /**
     * ###################################check vehicle safety start###############################################
     */

    @Override
    public void checkImage(boolean result, boolean canShowCheckInfo) {

    }

    @Override
    public void checkLongText(boolean result, boolean canShowCheckInfo) {

    }

    public void registerVehicleSafetyCheckCallback() {
        Activity activity = getActivity();
        if (activity instanceof BaseActivity) {
            ((BaseActivity) activity).registerVehicleSafetyCheckCallback(this);
        }
    }

    public void unregisterVehicleSafetyCheckCallback() {
        Activity activity = getActivity();
        if (activity instanceof BaseActivity) {
            ((BaseActivity) activity).unregisterVehicleSafetyCheckCallback(this);
        }
    }

    protected boolean resumeCheck() {
        Activity activity = getActivity();
        if (activity instanceof BaseActivity) {
            return ((BaseActivity) activity).resumeCheck(this);
        }
        return true;
    }

    /**
     * ###################################check vehicle safety end###############################################
     */

    /**
     * 判断可见性，对手动显示与PagerAdapter方式均有效，且跟随父fragment可见性状态
     * https://blog.csdn.net/dbpggg/article/details/80818488
     *
     * @return
     */
    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        Log.i(TAG, "setUserVisibleHint" + isVisibleToUser);
        isFirstSetUserVisibleHint = true;
        isCanShowing = isVisibleToUser;
        onVisibleChanged(isVisibleOnScreen());
    }

    protected void onVisibleChanged(boolean isVisible) {
        Log.i(TAG, this + "=onVisibleChanged=" + isVisible);
        if (!isVisible) {
            reportPageShowEvent();
        } else {
            startTime = System.currentTimeMillis();
        }
    }

    public boolean isVisibleOnScreen() {
        if (isCanShowing && getUserVisibleHint() && isVisible()) {
            if (getParentFragment() == null) {
                return true;
            }

            if (getParentFragment() instanceof BaseFragment) {
                return ((BaseFragment) getParentFragment()).isVisibleOnScreen();
            } else if (getParentFragment().getParentFragment() instanceof BaseFragment) {
                return ((BaseFragment) getParentFragment().getParentFragment()).isVisibleOnScreen();
            } else {
                return getParentFragment().isVisible();
            }
        }

        return false;
    }

    /**
     * 页面曝光事件上报
     */
    public void reportPageShowEvent() {
        long duration = System.currentTimeMillis() - startTime;
        if (!isReportFragment() || startTime < 0 || duration < 300) {
            return;
        }

        PageShowReportEvent event = new PageShowReportEvent();
        event.setPageId(getPageId());
        event.setPageTime(String.valueOf(duration));
        ReportHelper.getInstance().addEvent(event);
        Log.i(TAG, "report=" + duration);
        startTime = -1;
    }

    /**
     * 用于fragment曝光统计：是否需要统计，默认不需要
     *
     * @return
     */
    protected boolean isReportFragment() {
        return false;
    }

    public long getStartTime() {
        return this.startTime;
    }
}
