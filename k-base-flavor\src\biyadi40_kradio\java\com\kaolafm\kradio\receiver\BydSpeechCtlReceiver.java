package com.kaolafm.kradio.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.kradio.byd.BydSpeechCtlActionConstant;
import com.kaolafm.kradio.byd.BydSpeechCtlManager;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.sdk.client.ex.bean.RecommendData;

import java.util.List;

public class BydSpeechCtlReceiver extends BroadcastReceiver implements BydSpeechCtlActionConstant {

    private static final String TAG = "BydSpeechCtlReceiver";
    private static final String SUCCESS = "0";
    private boolean speechCtlActive = false;


    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        Log.i(TAG, "get action:" + action);
        if (action == null || StringUtil.isEmpty(action)) return;
        switch (action) {
            case FUNCTION_UPDATE_RESULT:
                String result = intent.getStringExtra("EXTRA_RESULT_CODE");
                if (result != null && result.equals(SUCCESS)) {
                    Log.i(TAG, "FUNCTION_UPDATE: SUCCESS");
                } else {
                    Log.e(TAG, "FUNCTION_UPDATE: ERROR");
                }
                break;
            case AUTOVOICE_BOOK:
                //执行指令
                //adb模拟测试：am broadcast -a com.byd.action.AUTOVOICE_BOOK
                BydSpeechCtlManager.getInstance().book();
                break;
            case AUTOVOICE_UNBOOK:
                //执行指令
                //adb模拟测试：am broadcast -a com.byd.action.AUTOVOICE_UNBOOK
                BydSpeechCtlManager.getInstance().unBook();
                break;
            case AUTOVOICE_FORWARD:
                //adb模拟测试：am broadcast -a com.byd.action.AUTOVOICE_FORWARD --es "EXTRA_SET_TIME" "3"
                String forwardSecondsStr = intent.getStringExtra("EXTRA_SET_TIME");
                int forwardSeconds = translateStringToSeconds(forwardSecondsStr);
                //执行指令
                BydSpeechCtlManager.getInstance().forward(forwardSeconds);
                //反馈执行结果
                BydSpeechCtlManager.getInstance().sendCmdResult(AUTOVOICE_FORWARD, RESULT_SUCCESS, "");
                break;
            case AUTOVOICE_REWIND:
                //adb模拟测试：am broadcast -a com.byd.action.AUTOVOICE_REWIND --es "EXTRA_SET_TIME" "3"
                String rewindSecondsStr = intent.getStringExtra("EXTRA_SET_TIME");
                int rewindSeconds = translateStringToSeconds(rewindSecondsStr);
                //执行指令
                BydSpeechCtlManager.getInstance().rewind(rewindSeconds);
                //反馈执行结果
                BydSpeechCtlManager.getInstance().sendCmdResult(AUTOVOICE_FORWARD, RESULT_SUCCESS, "");
                break;
            case AUTOVOICE_JUMP_TO:
                //adb模拟测试：am broadcast -a com.byd.action.AUTOVOICE_JUMP_TO --es "EXTRA_SET_TIME" "120"
                String jumpTotimeStr = intent.getStringExtra("EXTRA_SET_TIME");
                int secondsPosition = translateStringToSeconds(jumpTotimeStr);
                if (secondsPosition != -1) {
                    //执行指令
                    BydSpeechCtlManager.getInstance().jumpTo(secondsPosition);
                    //反馈结果（成功）
                    BydSpeechCtlManager.getInstance().sendCmdResult(AUTOVOICE_FORWARD, RESULT_SUCCESS, "");
                } else {
                    //反馈结果（失败）
                    BydSpeechCtlManager.getInstance().sendCmdResult(AUTOVOICE_FORWARD, RESULT_FAILED, "跳转时间参数错误");
                }
                break;
            case AUTOVOICE_QUIT:
                //adb模拟测试：am broadcast -a com.byd.action.AUTOVOICE_QUIT
                BydSpeechCtlManager.getInstance().quit();
                break;
            case AUTOVOICE_QUALITY:
                //adb模拟测试：am broadcast -a com.byd.action.AUTOVOICE_QUALITY --es "EXTRA_QUALITY" "标准"
                String quality = intent.getStringExtra("EXTRA_QUALITY");
                if (!TextUtils.isEmpty(quality))
                    BydSpeechCtlManager.getInstance().quality(quality);
                break;
            case AUTOVOICE_SEARCH:
                //adb模拟测试： am broadcast -a com.byd.action.AUTOVOICE_SEARCH --es "EXTRA_KEYWORDS_SEARCH" "郭德纲"
                BydSpeechCtlManager.getInstance().search(intent);
                break;
            case AUTOVOICE_RECOMMEND:
                //adb模拟测试： am broadcast -a com.byd.action.AUTOVOICE_RECOMMEND
                BydSpeechCtlManager.getInstance().playRecommend();
                break;
            case AUTOVOICE_PLAY_LATELY:
                //adb模拟测试： am broadcast -a com.byd.action.AUTOVOICE_PLAY_LATELY
                BydSpeechCtlManager.getInstance().playLately();
                break;
            case AUTOVOICE_PLAY_BOOK:
                //adb模拟测试： am broadcast -a com.byd.action.AUTOVOICE_PLAY_BOOK
                BydSpeechCtlManager.getInstance().playBook();
                break;
            case AUTOVOICE_COLLECT:
                BydSpeechCtlManager.getInstance().collect();
                break;
            case AUTOVOICE_UNCOLLECT:
                BydSpeechCtlManager.getInstance().unCollect();
                break;
            case AUTOVOICE_OPEN_LYRIC:
                BydSpeechCtlManager.getInstance().openLyric();
                break;
            case AUTOVOICE_CLOSE_LYRIC:
                BydSpeechCtlManager.getInstance().closeLyric();
                break;
            case AUTOVOICE_PLAY_FAVORITE:
                BydSpeechCtlManager.getInstance().playFavorite();
                break;
            case AUTOVOICE_PLAY_LIKE:
                BydSpeechCtlManager.getInstance().playLike();
                break;
            case AUTOVOICE_PLAY_DOWNLOAD:
                BydSpeechCtlManager.getInstance().download();
                break;
            case AUTOVOICE_PLAY_MODE:
                BydSpeechCtlManager.getInstance().playMode();
                break;
            case AUTOVOICE_SPEED_ADJUST:
                BydSpeechCtlManager.getInstance().speedAdjust();
                break;
            case AUTOVOICE_ACTIVECARE:
                //场景电台
                //参数定义：
                // 当传入参数为“场景code=0” 时，需要继续播放当前播放的节目（若当前没有播放的内容，生态内
                // 容可播放猜你喜欢类的节目/专辑）。除此之外，其他场景code值输入均为播放该code对应的场景
                // 节目单。 intent.putExtra("IS_BACKGROUND", "TRUE");//字符串类型，该参数非必须项，参
                // 数值为"TRUE"时为导航在前台，参数值为"FALSE"时，
                // 默认前台 intent.putExtra("EXTRA_SCENE_ID","1031");
                // 字符串类型，场景code类型（0|1031|1051）
                // 返回值定义： 0：执行成功 1：其他执行失败 2：超出可设置的范围 3：当前场景不支持该功能
                // 4：需手动操作用户协议后执行 5：需登录后支持 6：网络异常 7：当前功能中暂无数据信息

                String code = intent.getStringExtra("EXTRA_SCENE_ID");
                String strBackGround = intent.getStringExtra("IS_BACKGROUND");
                boolean isBackGround = !TextUtils.isEmpty(strBackGround) && "TRUE".equals(strBackGround);
                BydSpeechCtlManager.getInstance().playScene(code,isBackGround);
                break;
            default:
                break;
        }
    }

    private void printRecommend(List<RecommendData> recommendDatas) {
        for (RecommendData data : recommendDatas) {
            Log.i("BydSpeechCtlReceiver", "getRecommend:" + data.getRname() + data.getRid());
        }
    }

    /**
     * 将String转化为int值
     *
     * @param secondStr
     * @return
     */
    private int translateStringToSeconds(String secondStr) {
        int result = -1;
        if (!StringUtil.isEmpty(secondStr)) {
            try {
                result = Integer.parseInt(secondStr);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return result;
    }
}
