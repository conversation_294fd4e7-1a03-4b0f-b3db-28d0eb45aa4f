package com.kaolafm.kradio.home.comprehensive.item;

import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.ResUtil;

/**
 * <AUTHOR>
 * @date 2019-08-21
 */
public class SpaceCell extends HomeCell {

    @Override
    public int getItemType() {
        return R.layout.item_home_space;
    }

    @Override
    public int spanSize() {
        return ResUtil.getInt(R.integer.home_item_spans);
    }
}
