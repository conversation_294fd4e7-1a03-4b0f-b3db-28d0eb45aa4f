package com.kaolafm.kradio.flavor.customer.utils;

import android.content.Context;
import android.os.RemoteException;
import android.util.Log;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter;
import com.kaolafm.opensdk.player.logic.PlayerManager;

import aptiv.car.audio.AptivCarAudioManager;
import aptiv.car.audio.IAptivAudioEffectEventListener;
import aptiv.car.sourcemanager.AptivSourceManager;
import aptiv.car.sourcemanager.Source;
import aptiv.support.car.AptivCarHelper;

import static android.media.AudioManager.AUDIOFOCUS_LOSS;
import static aptiv.car.sourcemanager.AptivSourceManager.SSM_SOURCE_KAOLAFM;
import static aptiv.car.sourcemanager.AptivSourceManager.SSM_SOURCE_NONE;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-07-03 16:48
 ******************************************/
public class TbAptivSrcManager {
    private static final String TAG = "TbAptivSrcManager";
    private AptivCarHelper mAptivCarHelper;
    private AptivSourceManager mAptivSourceManager;
    private AptivCarAudioManager mAptivCarAudioManager;
    private HardwareConnectCallback mConnectionCallback;
    private Source mSource = new Source(SSM_SOURCE_KAOLAFM);

    private TbAptivSrcManager() {
    }

    private static class TB_APTIV_INNER_INSTANCE {
        private static final TbAptivSrcManager TB_APTIV_SRC_MANAGER = new TbAptivSrcManager();
    }

    public static TbAptivSrcManager getInstance() {
        return TB_APTIV_INNER_INSTANCE.TB_APTIV_SRC_MANAGER;
    }

    public void init(Context context) {
        PlayerManager.getInstance().addAudioFocusListener(onAudioFocusChangeInter);
        mConnectionCallback = new HardwareConnectCallback();
        mAptivCarHelper = new AptivCarHelper(context, mConnectionCallback);
    }

    OnAudioFocusChangeInter onAudioFocusChangeInter = new OnAudioFocusChangeInter() {
        @Override
        public void onAudioFocusChange(int i) {
            Log.i(TAG, "onAudioFocusChange i = " + i);
            if (i > 0) {
                setSrcEnable();
            } else {
                setSrcConnected();
            }
        }
    };


    /**
     * 1.页面回前台时
     * 2.Service onStartCommand
     * 3. 得到音频焦点
     * 以上三种场景下需调用此函数
     */
    public void setSrcEnable() {
        mSource.setSourceStatus(AptivSourceManager.SOURCE_ENABLED);
        boolean isAppForeground = AppDelegate.getInstance().isAppForeground();
        Log.i(TAG, "setSrcEnable isAppForeground = " + isAppForeground);
        mSource.setSourceFront(isAppForeground ? AptivSourceManager.SOURCE_FOREGROUND : AptivSourceManager.SOURCE_BACKGROUND);
        if (mAptivSourceManager != null) {
            mAptivSourceManager.setSourceInfo(mSource);
        } else {
            Log.i(TAG, "setSrcEnable mAptivSourceManager is null");
        }
    }

    /**
     * 1.音频焦点被抢走
     * 2. Activity 退后台
     * 以上两种场景下需调用此函数
     */
    public void setSrcConnected() {
        mSource.setSourceStatus(AptivSourceManager.SOURCE_CONNECTED);
        boolean isAppForeground = AppDelegate.getInstance().isAppForeground();
        Log.i(TAG, "setSrcConnected isAppForeground = " + isAppForeground);
        mSource.setSourceFront(isAppForeground ? AptivSourceManager.SOURCE_FOREGROUND : AptivSourceManager.SOURCE_BACKGROUND);
        if (mAptivSourceManager != null) {
            mAptivSourceManager.setSourceInfo(mSource);
        } else {
            Log.i(TAG, "setSrcConnected mAptivSourceManager is null");
        }
    }

    /**
     * 重置源
     */
    private void resetSrc() {
        mSource.setSourceStatus(AptivSourceManager.SOURCE_CONNECTED);
        mSource.setSourceFront(AptivSourceManager.SOURCE_BACKGROUND);
        if (mAptivSourceManager != null) {
            mAptivSourceManager.setSourceInfo(mSource);

            Source resetSource = new Source(SSM_SOURCE_NONE);
            mAptivSourceManager.setSourceInfo(resetSource);
        } else {
            Log.i(TAG, "resetSrc mAptivSourceManager is null");
        }
    }

    private AptivCarAudioManager.AptivAudioPropertyValueChangeListerner aptivAudioPropertyValueChangeListerner = new AptivCarAudioManager.AptivAudioPropertyValueChangeListerner() {
        @Override
        public void MasterMuteStatuscallback(boolean isMute) {
            Log.i(TAG, "MasterMuteStatuscallback isMute = " + isMute);
            //mute(isMute);
        }
    };

    private void mute(boolean isMute) {
        Log.i(TAG, "mute(" + isMute + ")");
        Source source = mAptivSourceManager == null ? null : mAptivSourceManager.getSourceInfo(SSM_SOURCE_KAOLAFM);
        Log.i(TAG, "source=" + source);
        if (source != null && !SSM_SOURCE_KAOLAFM.equals(source.getSourceType())) {
            Log.i(TAG, "AptivSourceManager.SOURCE_ENABLED not equals source !!!!!!!! return");
            return;
        }

        if (isMute) {
            PlayerManager.getInstance().pause(true);
        } else {
            //如果当前是永久失去焦点,不再播放
            try {
                Thread.sleep(2000);
            } catch (Exception e) {
            }
            if (PlayerManager.getInstance().getCurrentAudioFocusStatus() == AUDIOFOCUS_LOSS) {
                return;
            }
            PlayerManager.getInstance().play(true);
        }
    }

    private final IAptivAudioEffectEventListener mAudioEffectEventCb = new IAptivAudioEffectEventListener.Stub() {

        @Override
        public void ANCstatuscallback(boolean status){}

        @Override
        public void Balancecallback(int balance){}

        @Override
        public void BalanceFadeResetcallback(int balance , int fade){}

        @Override
        public void Beepstatuscallback(boolean status){}

        @Override
        public void BestPositionModecallback(int position ){}

        @Override
        public void BusVolumecallback(int bus,int index){}

        @Override
        public void DTSstatuscallback(boolean status){}

        @Override
        public void EQResetcallback(int bass, int mid, int treble){}

        @Override
        public void EQvaluesetcallback(int EQtype,int value){}

        @Override
        public void Fadecallback(int fade_val){}

        @Override
        public void IESSstatecallback(int state){}

        @Override
        public void callbackforIESSstatus(boolean enable){}

        @Override
        public void allGroupVolumeCallback(int i) throws RemoteException {
        }

        @Override
        public void MasterMuteStatuscallback(boolean status) {
            Log.i(TAG, "MasterMuteStatuscallback " + status);
            mute(status);
        }

        @Override
        public void UsageMuteStatuscallback(int muteUsage, boolean muteStatus){
        }

        @Override
        public void NaturalSoundstatuscallback(boolean status){}

        @Override
        public void QLIstatuscallback(boolean status){}

        @Override
        public void Clarifistatuscallback(boolean status){}

        @Override
        public void SpeedCompositionModecallback(int mode){}

        @Override
        public void setMediaDuckingGainNavCallback(int index){}

        @Override
        public void setMediaDuckingGainRVCCallback(int index){}

        @Override
        public void ChimeVolumeCallback(int index){}

    };

    private class HardwareConnectCallback implements AptivCarHelper.ConnectionCallback {
        @Override
        public void onAptivCarConnected() {
            Log.i(TAG, "onAptivCarConnected start");
            mAptivSourceManager = mAptivCarHelper.getAptivSourceManager();

            mAptivCarAudioManager = mAptivCarHelper.getAptivCarAudioManager();
            setAudioPropertyValueChangeListerner();
            registerAptivAudioEffectEventListener();
        }

        @Override
        public void onAptivCarDisconnected() {
            Log.i(TAG, "onAptivCarDisconnected start");
        }
    }

    public void destroy() {
        Log.i(TAG, "destroy");
        resetSrc();
        unsetAudioPropertyValueChangeListerner();
        unregisterAptivAudioEffectEventListener();
    }

    private void setAudioPropertyValueChangeListerner() {
        if (mAptivCarAudioManager != null) {
            mAptivCarAudioManager.setAudioPropertyValueChangeListerner(aptivAudioPropertyValueChangeListerner);
        }
    }

    private void unsetAudioPropertyValueChangeListerner() {
        Log.i(TAG, "unsetAudioPropertyValueChangeListerner");
        if (mAptivCarAudioManager != null) {
            mAptivCarAudioManager.unsetAudioPropertyValueChangeListerner(aptivAudioPropertyValueChangeListerner);
        }
    }

    private void registerAptivAudioEffectEventListener() {
        if (mAptivCarAudioManager != null) {
            mAptivCarAudioManager.registerListener(mAudioEffectEventCb);
        }
    }

    private void unregisterAptivAudioEffectEventListener() {
        if (mAptivCarAudioManager != null) {
            mAptivCarAudioManager.unregisterListener(mAudioEffectEventCb);
        }
    }
}
