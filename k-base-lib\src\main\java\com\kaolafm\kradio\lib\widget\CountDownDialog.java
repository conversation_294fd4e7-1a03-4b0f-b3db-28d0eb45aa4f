package com.kaolafm.kradio.lib.widget;

import android.app.Dialog;
import android.content.Context;
import androidx.annotation.NonNull;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.view.animation.AlphaAnimation;
import android.view.animation.ScaleAnimation;
import android.widget.TextView;

import com.kaolafm.kradio.lib.R;
import com.kaolafm.kradio.lib.utils.CountDownUtil;

public class CountDownDialog extends Dialog implements CountDownUtil.CountDownListener {
    private static final long DURATION = 6000;
    private static final long INTERVAL = 1000;

    private TextView tvCountdown;
    private String strCountdown = "倒计时";

    private CountDownUtil timerCountDown;
    private AlphaAnimation alphaAnimation;
    private ScaleAnimation scaleAnimation;

    public CountDownDialog(@NonNull Context context) {
        this(context, R.style.CountDownTimerDialog);
    }

    public CountDownDialog(@NonNull Context context, int themeResId) {
        super(context, themeResId);

        Window window = getWindow();
        window.setGravity(Gravity.CENTER); // 此处可以设置dialog显示的位置
        WindowManager.LayoutParams params = window.getAttributes();
        params.width = WindowManager.LayoutParams.WRAP_CONTENT;
        params.height = WindowManager.LayoutParams.WRAP_CONTENT;
        getWindow().setAttributes(params);
        setCancelable(false);
        setCanceledOnTouchOutside(false);

        LayoutInflater factory = LayoutInflater.from(context);
        View view = factory.inflate(R.layout.dialog_count_down_timer, null);
        setContentView(view);
        tvCountdown = findViewById(R.id.tv_countdown);

//        alphaAnimation = new AlphaAnimation(0, 1);
//        scaleAnimation = new ScaleAnimation(0.5f, 1.5f, 0.5f, 1.5f,
//                Animation.RELATIVE_TO_SELF, 0.5f, Animation.RELATIVE_TO_SELF, 0.5f);

        timerCountDown = new CountDownUtil(DURATION, INTERVAL, this);
        timerCountDown.setCountDownListener(this);
    }

    @Override
    public void show() {
        super.show();
        timerCountDown.start();
    }

    @Override
    public void dismiss() {
        super.dismiss();
        timerCountDown.cancel();
    }

    public void setText(String msg){
        strCountdown = msg;
    }


    @Override
    public void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (alphaAnimation != null) {
            alphaAnimation.cancel();
            alphaAnimation = null;
        }
        if (scaleAnimation != null) {
            scaleAnimation.cancel();
            scaleAnimation = null;
        }
    }

    @Override
    public void onTick(long millisUntilFinished) {
        tvCountdown.setText(strCountdown + "("+millisUntilFinished / INTERVAL + "S)");
        // 设置透明度渐变动画

//        //设置动画持续时间
////        alphaAnimation.setDuration(INTERVAL / 2);
////        tvCountdown.startAnimation(alphaAnimation);
////        // 设置缩放渐变动画
////        scaleAnimation.setDuration(INTERVAL / 2);
//        tvCountdown.startAnimation(scaleAnimation);
        if(mCountDownListener != null){
            mCountDownListener.onTick(millisUntilFinished);
        }
    }

    @Override
    public void onFinish() {
        if(mCountDownListener != null){
            mCountDownListener.onFinish();
        }
        dismiss();
    }

    private CountDownListener mCountDownListener;

    public void setCountDownListener(CountDownListener countDownListener) {
        this.mCountDownListener = countDownListener;
    }

    public interface CountDownListener {
        /**
         * 倒计时
         */
        void onTick(long millisUntilFinished);

        /**
         * 倒计时完成
         */
        void onFinish();

    }
}
