package com.kaolafm.kradio.lib.utils;

import android.app.ActivityManager;
import android.content.Context;
import android.os.Process;

public class ProcessUtil {

    public static boolean isMainProcess(Context context) {
        String currentProcessName = getCurrentProcessName(context);
        String packageName = context.getPackageName();
        return packageName.equals(currentProcessName);
    }

    public static String getCurrentProcessName(Context context) {
        int pid = Process.myPid();
        ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        for (ActivityManager.RunningAppProcessInfo processInfo : activityManager.getRunningAppProcesses()) {
            if (processInfo.pid == pid) {
                return processInfo.processName;
            }
        }
        return null;
    }
}
