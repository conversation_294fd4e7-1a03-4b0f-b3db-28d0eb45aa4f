package com.kaolafm.kradio.search;

import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.util.ReportConstants;


public class SearchTypeAdapter extends BaseAdapter<SearchType> {

    private String pageId = Constants.PAGE_ID_SEARCH;

    @Override
    protected BaseHolder<SearchType> getViewHolder(ViewGroup parent, int viewType) {
        return new SearchTypeHolder(inflate(parent, R.layout.item_search_type, viewType));
    }

    public class SearchTypeHolder extends BaseHolder<SearchType> {

        TextView mTvSearchType;
        View mRootView;

        public SearchTypeHolder(View itemView) {
            super(itemView);
            mTvSearchType=itemView.findViewById(R.id.tv_search_type);
            mRootView = itemView.findViewById(R.id.root_view);
        }

        @Override
        public void setupData(SearchType searchType, int position) {
            mRootView.setContentDescription(searchType.getTypeName());
            mTvSearchType.setText(searchType.getTypeName());
            mTvSearchType.setSelected(searchType.isSelect());
        }
    }

    public void setPageId(String pageId) {
        this.pageId = pageId;
    }

    @Override
    public void onViewAttachedToWindow(BaseHolder<SearchType> holder) {
        super.onViewAttachedToWindow(holder);
        if (holder instanceof SearchTypeHolder) {
            ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_SEARCH_CATEGORY_LIST_ITEM
                    , ((SearchTypeHolder) holder).mTvSearchType.getText().toString(), this.pageId, ReportConstants.CONTROL_TYPE_SCREEN));
        }
    }
}
