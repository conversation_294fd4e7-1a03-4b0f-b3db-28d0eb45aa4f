package com.kaolafm.kradio.media.session;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;
import android.view.KeyEvent;

import com.kaolafm.kradio.media.MediaButtonManagerUtil;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-04-09 17:38
 ******************************************/
public final class KRMediaButtonReceiver extends BroadcastReceiver {
    private static final String TAG = "KRMediaButtonReceiver";
    private MediaButtonManagerUtil mMediaButtonManagerUtil = new MediaButtonManagerUtil();

    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        Log.i(TAG, "收到 媒体消息: "+ action);
        if (Intent.ACTION_MEDIA_BUTTON.equals(action)) {
            KeyEvent key = intent.getParcelableExtra(Intent.EXTRA_KEY_EVENT);
            Log.i(TAG, "onReceive------->key = " + key);
            if (key == null) {
                return;
            }
            int keyAction = key.getAction();
            Log.i(TAG, "onReceive------->keyAction = " + keyAction);
            if (keyAction == KeyEvent.ACTION_UP) {
                return;
            }
            int keyCode = key.getKeyCode();
            Log.i(TAG, "onReceive------->keyCode = " + keyCode);
            mMediaButtonManagerUtil.manageMediaButtonClick(keyCode);
        }
    }
}
