<?xml version="1.0" encoding="utf-8"?>
<com.kaolafm.kradio.lib.widget.ratio.RatioConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/big_card_root_for_guide"
    app:canScale="false"
    app:wh_ratio="0.813:1">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.kaolafm.kradio.component.ui.base.view.OvalImageView
            android:id="@+id/card_bg_iv"
            android:background="@drawable/component_card_bg"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:oval_radius="@dimen/m8" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerInParent="true">

            <RelativeLayout
                android:id="@+id/card_pic_iv_rl"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginStart="@dimen/m40"
                android:layout_marginEnd="@dimen/m40"
                android:layout_marginTop="@dimen/m40">

                <com.kaolafm.kradio.lib.widget.square.SquareLayout
                    android:id="@+id/sv_item_home_place"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.kaolafm.kradio.component.ui.base.view.OvalImageView

                        android:id="@+id/card_pic_iv"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        app:layout_constraintDimensionRatio="w,1:1"
                        app:oval_radius="@dimen/m8"
                        tools:src="@drawable/splash_yunting" />

                    <ImageView
                        android:id="@+id/card_tag_iv"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/m30"
                        android:adjustViewBounds="true"
                        android:scaleType="fitStart"
                        tools:src="@drawable/icon_vip_home" />

                    <ImageView
                        android:id="@+id/card_play_iv"
                        android:layout_width="@dimen/m38"
                        android:layout_height="@dimen/m38"
                        android:layout_alignEnd="@+id/card_pic_iv"
                        android:layout_alignBottom="@+id/card_pic_iv"
                        android:layout_marginRight="@dimen/m20"
                        android:layout_marginBottom="@dimen/m20"
                        android:src="@drawable/component_play_icon_2"
                        android:visibility="gone" />

                    <com.kaolafm.kradio.component.ui.base.view.RateView
                        android:id="@+id/card_layout_playing"
                        android:layout_width="@dimen/m38"
                        android:layout_height="@dimen/m38"
                        android:layout_alignEnd="@+id/card_pic_iv"
                        android:layout_alignBottom="@+id/card_pic_iv"
                        android:layout_marginRight="@dimen/m20"
                        android:layout_marginBottom="@dimen/m20"
                        android:visibility="gone" />
                </com.kaolafm.kradio.lib.widget.square.SquareLayout>
            </RelativeLayout>


            <TextView
                android:id="@+id/card_title_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/card_pic_iv_rl"
                android:layout_alignStart="@+id/card_pic_iv_rl"
                android:layout_alignEnd="@+id/card_pic_iv_rl"
                android:layout_marginTop="@dimen/m16"
                android:ellipsize="end"
                android:lineSpacingExtra="@dimen/m7"
                android:maxWidth="@dimen/m260"
                android:maxLines="2"
                android:textColor="@color/text_color_7"
                android:textSize="@dimen/m26"
                tools:text="我是标题啊我标啊" />

        </RelativeLayout>
    </RelativeLayout>
</com.kaolafm.kradio.lib.widget.ratio.RatioConstraintLayout>