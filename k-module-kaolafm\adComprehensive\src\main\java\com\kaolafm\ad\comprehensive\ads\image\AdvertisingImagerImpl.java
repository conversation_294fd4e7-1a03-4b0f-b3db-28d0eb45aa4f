package com.kaolafm.ad.comprehensive.ads.image;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.ad.api.model.Advert;
import com.kaolafm.ad.api.model.AttachImage;
import com.kaolafm.ad.api.model.ImageAdvert;
import com.kaolafm.ad.api.model.InteractionAdvert;
import com.kaolafm.ad.comprehensive.ads.image.base.BaseAdContentView;
import com.kaolafm.ad.comprehensive.ads.image.base.BaseAdImageAdapter;
import com.kaolafm.ad.comprehensive.control.KradioAdSceneConstants;
import com.kaolafm.ad.expose.AdvertisingImager;
import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.opensdk.http.error.ApiException;

import java.util.ArrayList;
import java.util.List;

public class AdvertisingImagerImpl implements AdvertisingImager {
    private static final String TAG = "AdvertisingImagerImpl";

    AdView adView;
    AdInteractView adInteractView;
    SplashAdView splashAdView;

    private ImageAdvert mAdvert;
    private InteractionAdvert mInteractionAdvert;

    private List<AdListener> adListeners = new ArrayList<>();

    //能否显示广告主图。需求来自：在线电台，只在猜你喜欢、专辑/AI电台播放页、广播/听电视播放页显示广告主图，且切换页面时隐藏广告主图
    private boolean isCanShowAdImage = true;

    public AdvertisingImagerImpl() {

    }

    public void registAdListener(AdListener adListener) {
        if (!adListeners.contains(adListener)) {
            adListeners.add(adListener);
        }
    }

    public void unregirstAdListener(AdListener adListener) {
        if (adListeners.contains(adListener)) {
            adListeners.remove(adListener);
        }
    }

    public void setCanShowAdImage(boolean canShowAdImage) {
        isCanShowAdImage = canShowAdImage;
    }

    public void notifyAdError() {
        for (AdListener adListener : adListeners) {
            adListener.onAdError();
        }
    }

    @Override
    public void display(ImageAdvert advert) {
        Log.d(TAG, "display");
        if (advert == null || advert.getId() == -1) {
            return;
        }

        //隐藏二次互动的大图
        hideInteractionBannerView();

        mAdvert = advert;
        Log.d(TAG, "display , subType " + advert.getSubtype());

        if (advert.getSubtype() == KradioAdSceneConstants.SUB_TYPE_SPRAND_SCREEN) {
            if (splashAdView != null) {
                splashAdView.loadAd(advert);
            }
        } else {
            if (adView != null) {
                Log.i(TAG, "display Advert--->" + advert);
                adView.loadAd(advert, isCanShowAdImage);
            }
        }
    }

    public void hideInteractionBannerView() {
        if (adInteractView != null) {
            BaseAdImageAdapter adapter = adInteractView.getAdapter();
            if (adapter != null) {
                BaseAdContentView baseAdContentView = adapter.getBaseAdContentView();
                if (baseAdContentView instanceof AdInteractWithBannerContentView) {
                    ((AdInteractWithBannerContentView) baseAdContentView).hideBannerView();
                }
            }
        }
    }

    @Override
    public void hide(ImageAdvert advert) {
        if (advert == null) {
            if (mAdvert != null) {
                if (mAdvert.getSubtype() == KradioAdSceneConstants.SUB_TYPE_SPRAND_SCREEN && splashAdView != null) {
                    splashAdView.hide();
                }
                AdvertisingManager.getInstance().close(mAdvert);
            }
            return;
        }
        if (adView != null) {
            Log.i(TAG, "hide Advert--->" + advert);
            adView.hide();
        }
    }

    @Override
    public void skip(ImageAdvert advert) {
        if (mAdvert == null) {
            return;
        }

//        InteractionAdvert interactionAdvert = mAdvert.getInteractionAdvert();
        if (adView != null && adView.isShow()) {
            AdvertisingManager.getInstance().close(mAdvert);
//            if (adInteractView != null
//                    && mAdvert.getType() == KradioAdSceneConstants.AD_TYPE_IMAGE
//                    && interactionAdvert != null
//                    && interactionAdvert.getOpportunity() == KradioAdSceneConstants.INTERACT_AD_OPPORTUNITY_BEFORE) {
//                AdvertisingManager.getInstance().close(interactionAdvert);
//            }

            if (mAdvert.getType() != KradioAdSceneConstants.AD_TYPE_AUDIO_IMAGE) {
                AdvertisingManager.getInstance().getReporter().skip(mAdvert);
            }

        }
    }

    @Override
    public void displayInteraction(InteractionAdvert advert) {
        if (advert == null || advert.getId() == -1) {
            return;
        }
        AdvertisingManager.getInstance().getReporter().displayInteraction(advert);
        mInteractionAdvert = advert;
        if (adInteractView != null) {
            Log.i(TAG, "display InteractionAdvert--->" + advert);
            adInteractView.loadAd(advert);
        }
    }

    @Override
    public void hideInteraction(InteractionAdvert advert) {
        if (advert == null) {
            if (mInteractionAdvert != null) {
                AdvertisingManager.getInstance().close(mInteractionAdvert);
            }
            return;
        }
        if (adInteractView != null) {
            Log.i(TAG, "hide InteractionAdvert--->" + advert);
            adInteractView.hide();
        }
    }

    @Override
    public void click(InteractionAdvert advert) {

    }

    @Override
    public void error(String adZoneId, int subtype, ApiException e) {
        notifyAdError();
    }

    public void destroySplashAdView() {
        splashAdView = null;
    }

    public void destroyAdView() {
        if (adView != null) {
            adView.destroy();
            adView = null;
        }
        adInteractView = null;
    }

    public void createSplashAdView(Context context) {
        splashAdView = new SplashAdView(context);
    }

    private AdView lastAdView;
    private AdInteractView lastAdInteractView;

    public void displayAttachedImageInBrand(Context context, boolean isSkip, boolean isImageSkip) {
        Log.i(TAG, "displayAttachedImageInBrand --- before --- lastAdView=" + lastAdView);
        Log.i(TAG, "displayAttachedImageInBrand --- before --- lastAdInteractView=" + lastAdInteractView);
        lastAdView = adView;
        lastAdInteractView = adInteractView;
        Log.i(TAG, "displayAttachedImageInBrand --- after --- lastAdView=" + lastAdView);
        Log.i(TAG, "displayAttachedImageInBrand --- after --- lastAdInteractView=" + lastAdInteractView);
        displayAttachedImage(context, isSkip, isImageSkip);
    }
    public void destroyAdViewInBrand() {
        Log.i(TAG, "destroyAdViewInBrand --- before --- lastAdView=" + lastAdView);
        Log.i(TAG, "destroyAdViewInBrand --- before --- lastAdInteractView=" + lastAdInteractView);
        destroyAdView();
        adView = lastAdView;
        adInteractView = lastAdInteractView;
        lastAdView = null;
        lastAdInteractView = null;
        Log.i(TAG, "destroyAdViewInBrand --- after --- lastAdView=" + lastAdView);
        Log.i(TAG, "destroyAdViewInBrand --- after --- lastAdInteractView=" + lastAdInteractView);
    }

    public void displayAttachedImage(Context context, boolean isSkip, boolean isImageSkip) {
        destroySplashAdView();
        adView = new AdView(context);
        adInteractView = new AdInteractView(context);
        if (mAdvert != null) {
            InteractionAdvert interactionAdvert = mAdvert.getInteractionAdvert();
            if (isImageSkip) {
                if (mAdvert.getType() == KradioAdSceneConstants.AD_TYPE_AUDIO_IMAGE
                        && interactionAdvert != null && interactionAdvert.getOpportunity() == KradioAdSceneConstants.INTERACT_AD_OPPORTUNITY_BEFORE) {
                    AdvertisingManager.getInstance().expose(interactionAdvert);
                }
                return;
            }
            if (!TextUtils.isEmpty(mAdvert.getAttachImage().getLocalPath())) {
                adView.loadAd(createAdContentInfo(), isCanShowAdImage);
                return;
            }
            ;
            if (interactionAdvert != null && (interactionAdvert.getOpportunity() == KradioAdSceneConstants.INTERACT_AD_OPPORTUNITY_BEFORE
                    || (interactionAdvert.getOpportunity() == KradioAdSceneConstants.INTERACT_AD_OPPORTUNITY_AFTER
                    && mAdvert.getType() == KradioAdSceneConstants.AD_TYPE_IMAGE))) {
                AdvertisingManager.getInstance().expose(interactionAdvert);
            }
        }
    }

    private ImageAdvert createAdContentInfo() {
        mAdvert.setJumpSeconds((int) (mAdvert.getJumpSeconds() - mAdvert.getExposeDuration()));
        AttachImage attachImage = mAdvert.getAttachImage();
        mAdvert.setExposeDuration(attachImage.getExposeDuration());
        mAdvert.setLocalPath(attachImage.getLocalPath());
        mAdvert.setWidth(ResUtil.getDimen(R.dimen.m880));
        mAdvert.setHeight(ResUtil.getDimen(R.dimen.m320));
        return mAdvert;
    }

    public boolean isAdViewShowing() {
        return adView != null && adView.isShow();
    }

    public Advert getAdvert() {
        return mAdvert;
    }

    public interface AdListener {
        void onAdError();
    }
}
