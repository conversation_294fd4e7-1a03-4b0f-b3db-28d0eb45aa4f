package com.kaolafm.kradio.flavor.impl;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;
import android.view.View;

import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.base.flavor.KRadioLoginVipCardLayoutInter;

/**
 * @Description:
 * @Author: Maclay
 * @Date: 2022/7/5 5:23 下午
 */
public class KRadioLoginVipCardLayoutImpl implements KRadioLoginVipCardLayoutInter {
    @Override
    public void customLayout(ConstraintSet set, View vipView, ConstraintLayout layout) {
        set.connect(vipView.getId(), ConstraintSet.TOP, R.id.user_avatar, ConstraintSet.TOP);
        set.connect(vipView.getId(), ConstraintSet.BOTTOM, R.id.btn_logout, ConstraintSet.BOTTOM);
    }
}