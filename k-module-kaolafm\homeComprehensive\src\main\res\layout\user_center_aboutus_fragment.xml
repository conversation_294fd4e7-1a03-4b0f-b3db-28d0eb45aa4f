<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/aboutus_homeroom"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_home"
    android:scrollbars="none">

    <ImageView
        android:id="@+id/back_view"
        android:layout_width="@dimen/m36"
        android:layout_height="@dimen/m36"
        android:layout_marginStart="@dimen/m70"
        android:background="@color/transparent"
        android:scaleType="centerInside"
        android:src="@drawable/player_ic_back"
        app:layout_constraintBottom_toBottomOf="@id/tv_activity_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_activity_title" />

    <com.kaolafm.kradio.component.ui.base.view.KradioTextView
        android:id="@+id/tv_activity_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/m50"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:text="@string/person_center_aboutus_str"
        android:textColor="@color/text_color_7"
        android:textSize="@dimen/m30"
        app:kt_font_weight="0.3"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/deliver"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="@dimen/m34"
        android:background="@color/activity_line_bg"
        app:layout_constraintTop_toBottomOf="@+id/back_view"
        tools:ignore="MissingConstraints" />


    <ScrollView
        android:id="@+id/scroll"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:scrollbars="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/deliver">

        <LinearLayout
            android:id="@+id/container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingLeft="@dimen/m80"
            android:paddingTop="@dimen/m62"
            android:paddingRight="@dimen/m80"
            android:paddingBottom="@dimen/m50">

            <TextView
                android:id="@+id/person_center_aboutus_details_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:lineSpacingMultiplier="1.5"
                android:text="@string/person_center_aboutus_details_str"
                android:textColor="@color/text_color_7"
                android:textSize="@dimen/text_size4" />

            <TextView
                android:id="@+id/person_center_aboutus_mail_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/aboutus_magin"
                android:layout_marginTop="@dimen/y14"
                android:layout_marginRight="@dimen/aboutus_magin"
                android:lineSpacingMultiplier="1.5"
                android:text="@string/person_center_aboutus_mail_str"
                android:textColor="@color/person_center_aboutus_details_color"
                android:textSize="@dimen/text_size1"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/person_center_aboutus_details_tv" />

            <TextView
                android:id="@+id/person_center_aboutus_gongzhonghao_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/aboutus_magin"
                android:layout_marginTop="@dimen/y14"
                android:layout_marginRight="@dimen/aboutus_magin"
                android:lineSpacingMultiplier="1.5"
                android:text="@string/person_center_aboutus_gongzhonghao_str"
                android:textColor="@color/person_center_aboutus_details_color"
                android:textSize="@dimen/text_size1"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/person_center_aboutus_mail_tv" />

            <com.kaolafm.kradio.component.ui.base.view.OvalImageView
                android:id="@+id/person_center_aboutus_tingbanlogo_iv"
                android:layout_width="@dimen/m90"
                android:layout_height="@dimen/m90"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/y30"
                android:scaleType="centerInside"
                android:src="@drawable/ic_launcher_yt"
                app:layout_constraintBottom_toTopOf="@id/person_center_aboutus_version_tv"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintVertical_bias="0.77"
                app:oval_radius="@dimen/m18" />

            <TextView
                android:id="@+id/person_center_aboutus_version_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/y11"
                android:layout_marginBottom="@dimen/y21"
                android:gravity="center"
                android:lineSpacingMultiplier="1.5"
                android:text="@string/person_center_aboutus_version_str"
                android:textColor="@color/text_color_7"
                android:textSize="@dimen/text_size3"
                app:layout_constraintBottom_toTopOf="@id/person_center_aboutus_ll" />

            <LinearLayout
                android:id="@+id/person_center_aboutus_ll"
                android:layout_width="match_parent"
                android:layout_height="@dimen/m60"
                android:layout_marginBottom="@dimen/y24"
                android:gravity="center"
                android:orientation="horizontal"
                app:layout_constraintTop_toBottomOf="@id/person_center_aboutus_version_tv">

                <Button
                    android:id="@+id/person_center_aboutus_service_btn"
                    style="?android:attr/borderlessButtonStyle"
                    android:layout_width="@dimen/m180"
                    android:layout_height="match_parent"
                    android:layout_marginRight="@dimen/x70"
                    android:background="@drawable/about_us_btn_bg"
                    android:paddingLeft="@dimen/m18"
                    android:paddingTop="@dimen/m12"
                    android:paddingRight="@dimen/m18"
                    android:paddingBottom="@dimen/m12"
                    android:text="@string/person_center_aboutus_service_str"
                    android:textColor="@color/text_color_7"
                    android:textSize="@dimen/text_size4" />

                <Button
                    android:id="@+id/person_center_aboutus_secret_btn"
                    android:layout_width="@dimen/m180"
                    android:layout_height="match_parent"
                    android:background="@drawable/about_us_btn_bg"
                    android:paddingLeft="@dimen/m18"
                    android:paddingTop="@dimen/m12"
                    android:paddingRight="@dimen/m18"
                    android:paddingBottom="@dimen/m12"
                    android:text="@string/person_center_aboutus_private_str"
                    android:textColor="@color/text_color_7"
                    android:textSize="@dimen/text_size4" />
            </LinearLayout>

            <TextView
                android:id="@+id/person_center_aboutus_corp_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/y30"
                android:gravity="center"
                android:lineSpacingMultiplier="1.5"
                android:text="@string/person_center_aboutus_corp_str"
                android:textColor="@color/text_color_7"
                android:textSize="@dimen/text_size3" />

            <View
                android:layout_width="wrap_content"
                android:layout_height="@dimen/y30" />
        </LinearLayout>

    </ScrollView>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/web_view_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@drawable/bg_home"
        android:paddingStart="@dimen/default_edge_start"
        android:paddingTop="@dimen/y21"
        android:paddingEnd="@dimen/default_edge_end"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/deliver"
        tools:visibility="gone">

        <WebView
            android:id="@+id/web_view_content"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 隐藏控件，用于所见即可说语音执行滑动操作 -->
    <TextView
        style="@style/ContentDescriptionScrollUp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/scroll" />

    <TextView
        style="@style/ContentDescriptionScrollDown"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>