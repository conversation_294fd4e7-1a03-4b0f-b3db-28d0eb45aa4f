<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:background="@drawable/bg_home"
    android:layout_marginTop="@dimen/y44"
    android:padding="@dimen/m10"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/iv_setting_item_icon"
        android:layout_width="@dimen/m28"
        android:layout_height="@dimen/m28"
        android:scaleType="centerCrop"
        tools:src="@drawable/settings_aboutus" />

    <TextView
        android:layout_weight="1"
        android:layout_marginLeft="@dimen/x10"
        android:id="@+id/tv_setting_item_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/text_color_7"
        android:textSize="@dimen/settings_item_text_size"
        tools:text="关于我们" />
    <ImageView
        android:layout_width="@dimen/m28"
        android:layout_height="@dimen/m28"
        android:layout_gravity="center_vertical"
        android:src="@drawable/message_arrow_right" />
</LinearLayout>