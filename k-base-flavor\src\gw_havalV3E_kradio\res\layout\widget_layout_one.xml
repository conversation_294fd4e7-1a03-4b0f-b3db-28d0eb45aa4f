<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/widget_layout_one_main_layout"
    android:layout_width="580px"
    android:layout_height="295px"
    android:background="@color/transparent_color"
    android:minWidth="580px"
    android:minHeight="295px"
    tools:ignore="PxUsage">

    <RelativeLayout
        android:id="@+id/widget_playinfo_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/widget_cover"
            android:layout_width="128px"
            android:layout_height="128px"
            android:layout_centerVertical="true"
            android:layout_marginLeft="67px"
            android:background="@drawable/ic_launcher" />


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="54px"
            android:layout_marginRight="54px"
            android:layout_toRightOf="@+id/widget_cover"
            android:orientation="vertical">

            <TextView
                android:id="@+id/widget_audio_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/widget_audiocover_layout"
                android:ellipsize="end"
                android:gravity="left"
                android:lines="1"
                android:maxLines="1"
                android:text=""
                android:textColor="@color/widget_audio_name_textcolor"
                android:textSize="@dimen/audio_name_text_size" />

            <TextView
                android:id="@+id/widget_album_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/widget_audio_name"
                android:layout_marginTop="20px"
                android:ellipsize="end"
                android:gravity="left"
                android:lines="1"
                android:maxLines="1"
                android:text=""
                android:textColor="@color/widget_album_name_textcolor"
                android:textSize="@dimen/album_name_text_size" />
        </LinearLayout>
    </RelativeLayout>
</RelativeLayout>