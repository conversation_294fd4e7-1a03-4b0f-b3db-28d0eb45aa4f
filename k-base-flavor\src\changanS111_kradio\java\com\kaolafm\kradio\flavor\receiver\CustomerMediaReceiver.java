package com.kaolafm.kradio.flavor.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.kaolafm.kradio.player.helper.PlayerManagerHelper;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-07-13 18:11
 ******************************************/
public class CustomerMediaReceiver extends BroadcastReceiver {
    private static final String MEDIA_CONTROL = "com.iflytek.autofly.mediaControl";
    private static final String MEDIA_CONTROL_OPERATION = "operation";

    private static final int PLAY = 1; //播放
    private static final int PAUSE = 0; //暂停
    private static final int NEXT = 2;  //下一曲
    private static final int PREVIOUS = 3; //上一曲
    private static final String MEDIA_CONTROL_TYPE = "type";

    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        if (MEDIA_CONTROL.equals(action)) {
            final int code = intent.getIntExtra(MEDIA_CONTROL_OPERATION, -1);
            switch (code) {
                case PLAY:
                    PlayerManagerHelper.getInstance().play(true);
                    break;
                case PAUSE:
                    PlayerManagerHelper.getInstance().pause(true);
                    break;
                case NEXT:
                    PlayerManagerHelper.getInstance().playNext();
                    break;
                case PREVIOUS:
                    PlayerManagerHelper.getInstance().playPre();
                    break;
                default:
                    break;
            }
        }
    }
}
