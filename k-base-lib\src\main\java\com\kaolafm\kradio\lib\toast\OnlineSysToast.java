package com.kaolafm.kradio.lib.toast;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.kaolafm.kradio.lib.R;
import com.kaolafm.kradio.lib.base.flavor.SystemToastInter;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;

/**
 * @Description:
 */
public class OnlineSysToast {
    private static Toast toast;
    private static String lastMsg;
    static long shoutime;

    public static void show(Context context, String msg, int lengthShort) {
        if (System.currentTimeMillis() - shoutime < 3000 && TextUtils.equals(lastMsg, msg)) {
            return;
        }
        View view = ((LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE)).inflate(R.layout.online_sys_toast_layout, null, false);
        TextView text = (TextView) view.findViewById(R.id.toast_msg);
        text.setText(msg);
//        text.setPadding();
        ((View) text.getParent()).setLayoutParams(new FrameLayout.LayoutParams((int) (ScreenUtil.getScreenWidth() * 0.8), ViewGroup.LayoutParams.WRAP_CONTENT));
        //解决https://app.huoban.com/tables/2100000007530121/items/2300001672042057?userId=1229522问题
        if (toast == null) {
            toast = new Toast(context);
        } else {
            toast.cancel();
            toast = null;
            toast = new Toast(context);
        }

        toast.setGravity(Gravity.CENTER, 0, 0);
        toast.setDuration(Toast.LENGTH_SHORT);
        SystemToastInter inter = ClazzImplUtil.getInter("SystemToastImpl");
        if (inter != null && inter.getSystemToast()) {
            toast.setText(msg);
            Log.d("SysToast", "getSystemToast");
        } else {
            Log.d("SysToast", "setView");
            toast.setView(view);
        }
        toast.show();
        shoutime = System.currentTimeMillis();
        lastMsg = msg;
    }

    public static void release() {
        toast = null;
    }
}