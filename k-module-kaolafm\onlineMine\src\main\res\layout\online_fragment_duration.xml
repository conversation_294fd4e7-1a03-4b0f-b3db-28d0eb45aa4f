<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="bottom"
    android:orientation="vertical"
    android:paddingBottom="@dimen/y190"
    tools:context="com.kaolafm.kradio.online.mine.page.OnlineDurationFragment">

    <LinearLayout
        android:id="@+id/mine_duration_ll"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:layout_gravity="center_horizontal">

        <TextView
            android:textStyle="bold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/mine_duration_text"
            android:textColor="@color/user_info_value_color"
            android:textSize="@dimen/m24" />

        <TextView
            android:textStyle="bold"
            android:id="@+id/mine_duration_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="@dimen/m5"
            android:paddingRight="@dimen/m5"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/m36"
            tools:text="12" />

        <TextView
            android:textStyle="bold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/mine_duration_text2"
            android:textColor="@color/user_info_value_color"
            android:textSize="@dimen/m24" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/mine_duration_ll2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/y8"
        android:gravity="center_vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/mine_duration_text3"
            android:textColor="@color/user_info_value_color"
            android:textSize="@dimen/m20" />

        <TextView
            android:id="@+id/mine_duration_tv2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:paddingLeft="@dimen/m5"
            android:paddingRight="@dimen/m5"
            android:textStyle="bold"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/m28"
            tools:text="99%" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/mine_duration_text4"
            android:textColor="@color/user_info_value_color"
            android:textSize="@dimen/m20" />
    </LinearLayout>

    <RelativeLayout
        android:layout_width="@dimen/m182"
        android:layout_height="@dimen/m182"
        android:layout_gravity="center_horizontal"
        android:gravity="center"
        android:layout_marginTop="@dimen/y36"
        android:background="@drawable/mine_duration_nub_bg">

        <TextView
            android:id="@+id/tv1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/y13"
            android:text="@string/mine_duration_nub_text"
            android:textColor="@color/mine_duration_tv_color"
            android:textSize="@dimen/m24" />

        <ImageView
            android:id="@+id/iv"
            android:layout_width="@dimen/x48"
            android:layout_height="@dimen/y48"
            android:layout_below="@+id/tv1"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/y12"
            android:src="@drawable/mine_duration_nub_icon" />

        <TextView
            android:id="@+id/mine_duration_nub_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/iv"
            android:layout_centerHorizontal="true"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/y4"
            android:textColor="@color/colorWhite_80"
            android:textSize="@dimen/m28"
            tools:text="99h" />
    </RelativeLayout>

    <TextView
        android:id="@+id/mine_duration_not_login_hint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/y76"
        android:text="@string/mine_duration_not_login_hint_text"
        android:textColor="@color/user_info_value_color"
        android:textSize="@dimen/m28"
        android:visibility="gone"
        tools:visibility="visible" />
</LinearLayout>