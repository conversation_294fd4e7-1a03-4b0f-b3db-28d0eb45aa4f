package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.view.View;
import android.view.Window;

import com.kaolafm.kradio.lib.base.flavor.KRadioTransStatusBarInter;
import com.kaolafm.kradio.lib.utils.ScreenUtil;

import static com.kaolafm.kradio.lib.utils.ScreenUtil.setStatusBar;
import static com.kaolafm.kradio.lib.utils.ViewUtil.addPaddingForView;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-11-26 11:35
 ******************************************/
public final class KRadioTransStatusBarImpl implements KRadioTransStatusBarInter {

    private int mStatusBarHeight;

    public KRadioTransStatusBarImpl() {
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001708739183?userId=2169710问题
        mStatusBarHeight = ScreenUtil.getStatusBarHeight();
    }

    @Override
    public boolean changeStatusBarColor(Activity activity, int colorRes) {
        Window window = activity.getWindow();
        //解决https://app.huoban.com/tables/2100000007530121/items/2300001349816285?userId=1229522问题
        setStatusBar(window, true);
        return true;
    }

    @Override
    public boolean changeViewLayoutForStatusBar(View view, int id) {
        addPaddingForView(view, 0, mStatusBarHeight, 0, 0);
        return true;
    }

    @Override
    public boolean canChangeViewLayoutForStatusBar(Object... args) {
        return true;
    }

    @Override
    public int getStatusBarHeight(Object... args) {
        return mStatusBarHeight;
    }
}
