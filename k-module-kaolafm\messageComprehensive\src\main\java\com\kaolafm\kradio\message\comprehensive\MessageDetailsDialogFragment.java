package com.kaolafm.kradio.message.comprehensive;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.GradientDrawable;
import android.os.Bundle;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.annotation.IdRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.kaolafm.kradio.common.utils.CommonUtils;
import com.kaolafm.kradio.component.ComponentClient;
import com.kaolafm.kradio.component.ui.base.view.OvalImageView;
import com.kaolafm.kradio.constant.MessageButtonActionComponentConst;
import com.kaolafm.kradio.home.utils.AppDateUtils;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.basedb.manager.MessageDaoManager;
import com.kaolafm.kradio.lib.bean.CrashMessageBean;
import com.kaolafm.kradio.lib.report.ReportUtil;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.DateUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.lib.widget.tab.LinearGradientFontSpan;
import com.kaolafm.message.utils.OnlineMessageUtils;
import com.kaolafm.opensdk.api.CrashMessageBaseBean;
import com.kaolafm.opensdk.api.CrashMessageButtonActionBean;
import com.kaolafm.opensdk.crash.CrashPlayerHelper;
import com.kaolafm.opensdk.crash.Icrashstate;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonClickReportEvent;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.event.DialogExposureEvent;
import com.kaolafm.report.event.MessageShowReportEvent;
import com.kaolafm.report.event.PageShowReportEvent;
import com.kaolafm.report.util.ReportConstants;
import com.kaolafm.report.util.ReportParameterManager;
import com.lcodecore.tkrefreshlayout.utils.ScrollingUtil;

import java.util.BitSet;

/**
 * 消息泡泡详情
 * 调用示例：
 * MessageBubbleDialogFragment dialogFragment = (MessageBubbleDialogFragment) new Dialogs.Builder().setType(Dialogs.TYPE_MESSAGE_BUBBLE).create();
 * dialogFragment.setBubbleType(MessageBubbleDialogFragment.TYPE_DANGER)
 * .setSceneBitmap(BitmapFactory.decodeResource(getResources(), R.drawable.message_bubble_thunderbolt))
 * .setTitle("国家应急广播")
 * .setSubTitle("石家庄市发布暴雨雷电红色预警")
 * .setButtons(new ArrayList<MessageBubbleDialogFragment.MessageBubbleButton>() {{
 * add(new MessageBubbleDialogFragment.MessageBubbleButton("查看详情", R.id.message_bubble_button_1_id));
 * add(new MessageBubbleDialogFragment.MessageBubbleButton("倒计时", 20L, R.id.message_bubble_button_2_id));
 * add(new MessageBubbleDialogFragment.MessageBubbleButton("删除", R.drawable.search_icon_delete, R.id.message_bubble_button_3_id));
 * add(new MessageBubbleDialogFragment.MessageBubbleButton("确认", R.drawable.user_no_login_icon, 15, R.id.message_bubble_button_4_id));
 * }}, new View.OnClickListener() {
 *
 * @Override public void onClick(View v) {
 * int id = v.getId();
 * if (id == R.id.message_bubble_button_1_id) {
 * Log.e(TAG, "点击按钮：1");
 * } else if (id == R.id.message_bubble_button_2_id) {
 * Log.e(TAG, "点击按钮：2");
 * } else if (id == R.id.message_bubble_button_3_id) {
 * Log.e(TAG, "点击按钮：3");
 * } else if (id == R.id.message_bubble_button_4_id) {
 * Log.e(TAG, "点击按钮：4");
 * }
 * }
 * }).show(getSupportFragmentManager(), "MessageBubbleDialogFragment");
 */
public class MessageDetailsDialogFragment extends Dialog {

    //    private RelativeLayout bubble_pic_rl;
//    private RelativeLayout pic_big_rl;
//    private SubsamplingScaleImageView pic_big_iv;
//    private String mIconResource;
    private CrashMessageBean crashMessageBean;

    protected long startTime = -1;
    private boolean isPlay;//是否正在播放
    private boolean isMsgList;//是否从消息列表打开的
    private String lastPageId = "";//上一页面的pageId，用于隐藏本dialog时还原pageId

    public MessageDetailsDialogFragment(@NonNull Context context) {
        super(context, R.style.NormalDialogTheme);
        Log.i("MessageDetailsDialogFragment", "MessageDetailsDialogFragment: xxx");
    }

//    public static MessageDetailsDialogFragment create() {
//        MessageDetailsDialogFragment fragment = new MessageDetailsDialogFragment();
//        return fragment;
//    }
//
//    @Override
//    public String getPageId() {
//        return Constants.PAGE_ID_MESSAGE_DETAILS;
//    }


    private void handleCardInsideOutside(){
        View root_view = findViewById(R.id.root_view);
        root_view.setOnClickListener(v -> {
            MessageDaoManager.getInstance().updateLook(crashMessageBean.getMsgId());
            if (CrashPlayerHelper.getInstance().isPlay()) {
                CrashPlayerHelper.getInstance().playEnd();
            }
            dismiss();
        });

        findViewById(R.id.root_rl).setOnClickListener(v -> {
            //do nothing，拦截点击事件不要到root_view上
        });
    }



    private void handleIcon(){
        ImageView bubbleIcon = findViewById(R.id.bubbleIcon);

        CrashMessageBean bean = this.crashMessageBean;

        if( !StringUtil.isEmpty(bean.getMsgIconUrl())){
            ImageLoader.getInstance().displayImage(getContext(), bean.getMsgIconUrl(), bubbleIcon);
        } else {
            bubbleIcon.setImageResource(R.drawable.message_red_iconr);
        }
    }
    private void handleCardBg(){
        View root_rl = findViewById(R.id.root_rl);
        try {
            GradientDrawable gradientDrawable = (GradientDrawable) root_rl.getBackground();
            gradientDrawable.setCornerRadius(ResUtil.getDimen(R.dimen.m16));
            root_rl.setBackground(gradientDrawable);
        } catch (Exception e) {
        }

        OvalImageView dialog_right_half_bg = findViewById(R.id.dialog_right_half_bg);
        if( !StringUtil.isEmpty(crashMessageBean.getMsgDetailsBgUrl())){
            ImageLoader.getInstance().displayImage(getContext(), crashMessageBean.getMsgDetailsBgUrl(), dialog_right_half_bg);
        }
    }

    private View activity_register_big_rl;
    private ImageView ok_qr_iv;
    private void handleActivityRegister(){
        activity_register_big_rl = findViewById(R.id.activity_register_big_rl);
        ok_qr_iv = findViewById(R.id.ok_qr_iv);
        activity_register_big_rl.setOnClickListener(v-> {
//            info_ll.setVisibility(View.VISIBLE);
            v.setVisibility(View.GONE);
        });
    }

    private void handleTitleTime(){
        TextView bubbleTimeTv = findViewById(R.id.activity_time);
        TextView activity_time_tip = findViewById(R.id.activity_time_tip);

        //    private ImageView bubble_pic_iv;
        TextView bubbleTitle = findViewById(R.id.bubbleTitle);

        bubbleTitle.setText(crashMessageBean.getHeadline());

        bubbleTimeTv.setText(crashMessageBean.getPublishTime());

        if(this.crashMessageBean.getMsgType().equals("02")){
            activity_time_tip.setVisibility(View.VISIBLE);
            bubbleTimeTv.setText(getActivityTimeForDisplay(this.crashMessageBean.getMsgTime()));
        } else {
            bubbleTimeTv.setText(DateUtil.formatMillis("yyyy-MM-dd HH:mm", Long.parseLong(crashMessageBean.getPublishTime())));
        }
    }

    /**活动起始结束时间，以逗号分隔* */
    private String getActivityTimeForDisplay(String time){
        if(StringUtil.isEmpty(time)){
            return "";
        }
        String[] segs = time.split(",");
        if(segs == null){
            return "";
        }
        if(segs.length == 2){
            return segs[0] + " - "+ segs[1];
        }

        return time;
    }

    private void handleWindow(){

        Window window = this.getWindow();
        int matchParent = ViewGroup.LayoutParams.MATCH_PARENT;//父布局的宽度

        WindowManager.LayoutParams lp = window.getAttributes();
        lp.width = matchParent;
        lp.height = matchParent;
//        lp.x = matchParent;
//        lp.y = 300;  //设置出现的高度，距离顶部
        window.setAttributes(lp);

        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
        window.setStatusBarColor(Color.TRANSPARENT);

        // 保持沉浸式模式
        window.getDecorView().setSystemUiVisibility(
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                        | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                        | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION);

        //去除系统自带的margin
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        //背景全透明
        window.setDimAmount(0f);
        //设置弹出动画
        window.setWindowAnimations(R.style.MessageBubbleAnimation);
        //设置对话框大小
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
    }

    private void handlePlay(){
        TextView play_iv = findViewById(R.id.play_iv);
        View replay_panel_state_icon = findViewById(R.id.replay_panel_state_icon);

        View replay_panel = findViewById(R.id.replay_panel);

        isPlay = CrashPlayerHelper.getInstance().isPlay();

        View loading = findViewById(R.id.loading);

        loading.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) { }
        });

        if(StringUtil.isEmpty(crashMessageBean.getEventDescriptionPath())){
            replay_panel.setVisibility(View.INVISIBLE);
        }

        TextView cdClose = findViewById(R.id.cd_close);
        cdClose.setOnClickListener(v->{
            cdClose.postDelayed(() -> {
                dismiss();
                CrashPlayerHelper.getInstance().playEnd();
            }, 1000);
        });

        if (isPlay) {
            CrashPlayerHelper.getInstance().setIcrashstate(new Icrashstate() {
                @Override
                public void onCrashstate(int i) {
                    //播放完成
                    OnlineMessageUtils.getInstance().setShowMsgDetails(false);
                    isPlay = false;
                    play_iv.setText("重播");
                    play_iv.setEnabled(true);
                    replay_panel_state_icon.setBackgroundResource(R.drawable.message_detail_replay_state_icon_can_replay);
                    replay_panel.setEnabled(true);
//                    play_iv.setImageDrawable(ResUtil.getDrawable(R.drawable.msg_play_icon));
                }

                @Override
                public void onBufferingUpdate(CrashMessageBaseBean crashMessageBaseBean) {
                    //缓冲完成
                }

                @Override
                public void onPlayerFailed(PlayerFailedType playerFailedType) {

                }
            });
        }else {
            play_iv.setText("重播");
            play_iv.setEnabled(true);
            replay_panel_state_icon.setBackgroundResource(R.drawable.message_detail_replay_state_icon_can_replay);
            replay_panel.setEnabled(true);
        }

        replay_panel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ButtonExposureOrClickReportEvent exposureOrClickReportEvent = new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_MESSAGE_BUBBLE_DETAIL_VOICE_BROADCAST, "语音播报",
                        getPageId(),
                        ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_MESSAGE_BUBBLE);
                exposureOrClickReportEvent.setPage(getPageId());
                ReportHelper.getInstance().addEvent(exposureOrClickReportEvent);
                if (isPlay) {
                    return;
                }
                if (crashMessageBean != null) {
                    isPlay = true;
//                    play_iv.setImageDrawable(ResUtil.getDrawable(R.drawable.msg_play_not_icon));
                    loading.setVisibility(View.VISIBLE);

                    play_iv.setText("播放中");
                    play_iv.setEnabled(false);
                    replay_panel_state_icon.setBackgroundResource(R.drawable.message_detail_replay_state_icon_playing);
                    replay_panel.setEnabled(false);
                    CrashPlayerHelper.getInstance().setIcrashstate(new Icrashstate() {
                        @Override
                        public void onCrashstate(int i) {
                            //播放完成
                            OnlineMessageUtils.getInstance().setShowMsgDetails(false);
                            isPlay = false;
//                            play_iv.setImageDrawable(ResUtil.getDrawable(R.drawable.msg_play_icon));
                            play_iv.setText("重播");
                            play_iv.setEnabled(true);
                            replay_panel_state_icon.setBackgroundResource(R.drawable.message_detail_replay_state_icon_can_replay);
                            replay_panel.setEnabled(true);
                            loading.setVisibility(View.GONE);
                        }

                        @Override
                        public void onBufferingUpdate(CrashMessageBaseBean crashMessageBaseBean) {
                            //缓冲完成
                            OnlineMessageUtils.getInstance().setShowMsgDetails(true);
                            loading.setVisibility(View.GONE);
                        }

                        @Override
                        public void onPlayerFailed(PlayerFailedType playerFailedType) {

                        }
                    });
                    CrashPlayerHelper.getInstance().addImmediatelyplayDate(AppDateUtils.getInstance().changeDate(crashMessageBean)).startPlay();
                }
                //上报点击
                ReportUtil.addMessageClike(getPageId(), crashMessageBean.getMsgContentType(), crashMessageBean.getMsgId());
                ButtonClickReportEvent event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_MSG_PLAY_ICON);
                ReportHelper.getInstance().addEvent(event);
            }
        });
    }


    private void handleMsgTipPic(){
//        ImageView msg_tips_pic_iv = findViewById(R.id.msg_tips_pic_iv);
//        if (crashMessageBean.getMsgLevel().equals("3")) {
//            //应急广播要显示标题下边的图片
//            msg_tips_pic_iv.setVisibility(View.VISIBLE);
//            msg_tips_pic_iv.setImageDrawable(ResUtil.getDrawable(R.drawable.msg_tips_pic));
//        } else {
//            if( StringUtil.isEmpty(crashMessageBean.getMsgPicUrl())){
//                return;
//            }
//            ImageLoader.getInstance().displayImage(getContext(), crashMessageBean.getMsgPicUrl(), msg_tips_pic_iv);
//        }
        ImageView msg_tips_pic_iv = findViewById(R.id.msg_tips_pic_iv);

        String picUrl = crashMessageBean.getMsgPicUrl();
        if ( !StringUtil.isEmpty(picUrl)) {
            //应急广播要显示标题下边的图片
            msg_tips_pic_iv.setVisibility(View.VISIBLE);
            Glide.with(getContext())
                    .asBitmap()
                    .load(crashMessageBean.getMsgPicUrl())
//                    .placeholder(R.drawable.media_default_pic) // 不可设置此选项，否则会导致 ImageView 设置 android:adjustViewBounds="true" 后，图片显示很模糊
                    .error(R.drawable.media_default_pic)
                    .into(msg_tips_pic_iv);
        } else {
            if (MessageDialogHelper.isEbPushMessage(this.crashMessageBean)) {
                //应急广播要显示标题下边的图片
                msg_tips_pic_iv.setVisibility(View.VISIBLE);
                msg_tips_pic_iv.setImageDrawable(ResUtil.getDrawable(R.drawable.msg_tips_pic));
            }
        }
    }

    private void handleRightHalf(){
        RelativeLayout content_right_panel = findViewById(R.id.content_right_panel);

        if(StringUtil.isEmpty(crashMessageBean.getMsgDetailsQrUrl())) {
            content_right_panel.setVisibility(View.GONE);
            return;
        }
        ImageView pic_big_iv = findViewById(R.id.pic_big_iv);
        RelativeLayout pic_big_rl = findViewById(R.id.pic_big_rl);

        ImageView bubble_qr_iv = findViewById(R.id.bubble_qr_iv);

        RequestOptions rqOpts = new RequestOptions().placeholder(R.drawable.glide_loading_animation);


//        ImageLoader.getInstance().displayImage(getContext(), crashMessageBean.getMsgDetailsQrUrl(), bubble_qr_iv);
        Glide.with(getContext()).applyDefaultRequestOptions(rqOpts).load(crashMessageBean.getMsgDetailsQrUrl()).into(bubble_qr_iv);

        Glide.with(getContext())
                .applyDefaultRequestOptions(rqOpts)
                .load(crashMessageBean.getMsgDetailsQrUrl())
                .into(pic_big_iv);
//                .into(new SimpleTarget<File>() {
//                    @Override
//                    public void onResourceReady(@NonNull File resource, @Nullable Transition<? super File> transition) {
//                        pic_big_iv.setMinimumScaleType(SubsamplingScaleImageView.SCALE_TYPE_START);
//                        pic_big_iv.setImage(ImageSource.uri(resource.getAbsolutePath()),
//                                new ImageViewState(0f, new PointF(0f, 0f), 0));
//                    }
//                });

        TextView bubble_qr_desc_tv = findViewById(R.id.bubble_qr_desc_tv);
        bubble_qr_desc_tv.setText(crashMessageBean.getMsgDetailsPicTips());

        bubble_qr_iv.setOnClickListener(v -> {
            //显示大图
            pic_big_rl.setVisibility(View.VISIBLE);
            ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_MESSAGE_BUBBLE_DETAIL_COVER
                    , "", getPageId(), ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_MESSAGE_BUBBLE));
        });



        pic_big_iv.setOnClickListener(v -> pic_big_rl.setVisibility(View.GONE));
        pic_big_rl.setOnClickListener(v -> pic_big_rl.setVisibility(View.GONE));

        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_MESSAGE_BUBBLE_DETAIL_COVER
                , "", getPageId(), ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_MESSAGE_BUBBLE));
    }

    @SuppressLint("LongLogTag")
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.layout_message_details);

        handleCardInsideOutside();

        handleIcon();

        handleCardBg();

        handleActivityRegister();

        handleTitleTime();

        handleMsgTipPic();

        handleRightHalf();

        ScrollView scrollView = findViewById(R.id.scroll_view);
        handleVoiceScroll(R.id.cd_up, R.id.cd_down, scrollView);

        handleWindow();

        handleContent();

        setButtons();

        handlePlay();
    }

    private void handleVoiceScroll(@IdRes int upViewId, @IdRes int downViewId, ScrollView scrollView){
        View.OnClickListener listener = v -> {
            if(upViewId == v.getId()){
                ScrollingUtil.scrollListByVoice(scrollView, -1);
                ToastUtil.showNormal(AppDelegate.getInstance().getContext(), "向上滑动");
            } else if(downViewId == v.getId()){
                ScrollingUtil.scrollListByVoice(scrollView, 1);
                ToastUtil.showNormal(AppDelegate.getInstance().getContext(), "向下滑动");
            }
        };
        // 所见即可说
        TextView upScroll = findViewById(R.id.cd_up);
        if(upScroll != null){
            upScroll.setOnClickListener(listener);
        }
        TextView downScroll = findViewById(R.id.cd_down);
        if(downScroll != null){
            downScroll.setOnClickListener(listener);
        }
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        MessageDaoManager.getInstance().updateLook(crashMessageBean.getMsgId());
        if (CrashPlayerHelper.getInstance().isPlay()) {
            CrashPlayerHelper.getInstance().playEnd();
        }
        dismiss();
    }

//    /**
//     * 加载大图显示
//     */
//    private void loadBigPic(String url) {
//        Glide.with(getContext()).downloadOnly()
//                .load(url)
//                .into(new SimpleTarget<File>() {
//                    @Override
//                    public void onResourceReady(@NonNull File resource, @Nullable Transition<? super File> transition) {
//                        pic_big_iv.setMinimumScaleType(SubsamplingScaleImageView.SCALE_TYPE_CENTER_CROP);
//                        pic_big_iv.setImage(ImageSource.uri(resource.getAbsolutePath()),
//                                new ImageViewState(0f, new PointF(0f, 0f), 0));
//                    }
//                });
//    }

    public MessageDetailsDialogFragment setMsgList(boolean isMsgList) {
        this.isMsgList = isMsgList;
        return this;
    }

    @Override
    public void hide() {
        super.hide();
        Log.i(ReportConstants.REPORT_TAG, "设置pageid= " + lastPageId);
        ReportHelper.getInstance().setPage(this.lastPageId);
    }

    @Override
    public void dismiss() {
        super.dismiss();
        if (isPlay) {
            CrashPlayerHelper.getInstance().terminatePlayMsg(crashMessageBean.getMsgId());
        }
        OnlineMessageUtils.getInstance().setShowMsgDetails(false);
        isPlay = false;
        reportPageShowEvent();
        Log.i(ReportConstants.REPORT_TAG, "设置pageid= " + lastPageId);
        ReportHelper.getInstance().setPage(this.lastPageId);
    }

    @Override
    public void show() {
        super.show();
        //上报
//        ReportUtil.addMessageShow(getPageId(), crashMessageBean.getMsgContentType(), crashMessageBean.getMsgId());
        MessageShowReportEvent messageShowReportEvent = new MessageShowReportEvent();
        messageShowReportEvent.setRadiotype(crashMessageBean.getMsgContentType());
        messageShowReportEvent.setRemarks2(crashMessageBean.getMsgId());
        messageShowReportEvent.setPageid(getPageId());
        Log.d("1233", "------------" + isMsgList);
        if (isMsgList) {
            messageShowReportEvent.setPage(Constants.PAGE_ID_MESSAGE);
        } else {
            messageShowReportEvent.setPage(Constants.PAGE_ID_MESSAGE_CARD);
        }
        ReportHelper.getInstance().addEvent(messageShowReportEvent);

        //按钮曝光
        ButtonExposureOrClickReportEvent exposureOrClickReportEvent = new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_MESSAGE_BUBBLE_DETAIL_VOICE_BROADCAST, "语音播报",
                getPageId(),
                ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_MESSAGE_BUBBLE);
        exposureOrClickReportEvent.setPage(getPageId());
        ReportHelper.getInstance().addEvent(exposureOrClickReportEvent);
    }
    private void handleContent(){
        TextView bubbleContent = findViewById(R.id.bubbleContent);
        if( !StringUtil.isEmpty(crashMessageBean.getEventDescription()) ){
            bubbleContent.setText(crashMessageBean.getEventDescription());
        } else {
            if( !StringUtil.isEmpty(crashMessageBean.getEventDescriptionExtract()) ) {
                bubbleContent.setText(crashMessageBean.getEventDescriptionExtract());
            } else {
                bubbleContent.setText("");
            }
        }
    }

    public MessageDetailsDialogFragment setCrashMessageBean(CrashMessageBean messageBean) {
        this.crashMessageBean = messageBean;
        return this;
    }

//    public MessageDetailsDialogFragment setSceneBitmap(Bitmap bitmap) {
//        return setSceneBitmap(bitmap, true);
//    }

//    /**
//     * 灾害等级:1.红色预警 2.橙色预警 3.黄色预警 4.蓝色预警
//     */
//    public MessageDetailsDialogFragment setBubbleType() {
//
////        int resourceId = 0;
//        RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) root_rl.getLayoutParams();
//        switch (crashMessageBean.getMsgLevel()) {
////            resourceId = R.drawable.message_bubble_warning;黄色预留，一期不做
//            case "1"://情感化问候
////                resourceId = R.drawable.message_blue_icon;
////                break;
//            case "2"://节目预约、社交 、礼物等
////                resourceId = R.drawable.message_green_icon;
//
//                layoutParams.width = ResUtil.getDimen(R.dimen.m790);
//                layoutParams.height = ResUtil.getDimen(R.dimen.m344);
//                break;
//            case "3"://应急广播消息
//                //0-文 1-文+图 2-文+按钮 3-文+图+按钮
//                layoutParams.height = ResUtil.getDimen(R.dimen.m344);
//                if (crashMessageBean.getMsgStyleType() == 1 || crashMessageBean.getMsgStyleType() == 3) {
//                    layoutParams.width = ResUtil.getDimen(R.dimen.m790);
//                } else {
//                    layoutParams.width = ResUtil.getDimen(R.dimen.m684);
//                }
////                switch (crashMessageBean.getEventLevel()) {
////                    case "1":
////                resourceId = R.drawable.message_red_iconr;
////                        break;
////                    case "2":
////                        resourceId = R.drawable.message_bubble_cheng;
////                        break;
////                    case "3":
////                        resourceId = R.drawable.message_bubble_huang;
////                        break;
////                    case "4":
////                        resourceId = R.drawable.message_bubble_lan;
////                        break;
////                }
//                break;
//        }
//        root_rl.setLayoutParams(layoutParams);
////        if (resourceId != 0) {
////            bubbleIcon.setVisibility(View.VISIBLE);
////            bubbleIcon.setImageResource(resourceId);
////        } else {
////            bubbleIcon.setVisibility(View.GONE);
////        }
//        return this;
//    }

    private BitSet getButtonStatus(){
        BitSet bs = new BitSet(2);

        CrashMessageBean bean = this.crashMessageBean;

        if( !StringUtil.isEmpty(bean.getMsgDetailsBtnTextLeft()) ){
            bs.set(1);
        }

        if(!StringUtil.isEmpty(bean.getMsgDetailsBtnTextRight()) ){
            bs.set(0);
        }
        return bs;
    }

    public void setButtons() {

        LinearLayout msg_btn_ll = findViewById(R.id.msg_btn_ll);
        TextView msg_btn_lift_tv = findViewById(R.id.msg_btn_lift_tv);
        TextView msg_btn_right_tv = findViewById(R.id.msg_btn_right_tv);

        View sep = findViewById(R.id.sep);

        msg_btn_lift_tv.setOnClickListener(v -> handleLeftButton());
        msg_btn_right_tv.setOnClickListener(v -> handleRightButton());

        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_MESSAGE_BUBBLE_DETAIL_BUTTON_1
                , crashMessageBean.getMsgDetailsBtnTextLeft(), getPageId(), ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_MESSAGE_BUBBLE));
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_MESSAGE_BUBBLE_DETAIL_BUTTON_2
                , crashMessageBean.getMsgDetailsBtnTextRight(), getPageId(), ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_MESSAGE_BUBBLE));

        BitSet bs = getButtonStatus();
        if(bs.get(1)){
            msg_btn_lift_tv.setVisibility(View.VISIBLE);
            msg_btn_lift_tv.setText(crashMessageBean.getMsgDetailsBtnTextLeft());
        } else {
            msg_btn_lift_tv.setVisibility(View.GONE);
        }

        if(bs.get(0)){
            msg_btn_right_tv.setVisibility(View.VISIBLE);
            msg_btn_right_tv.setText(crashMessageBean.getMsgDetailsBtnTextRight());
        } else {
            msg_btn_right_tv.setVisibility(View.GONE);
        }

        if(bs.get(1) && !bs.get(0)){    //仅左边有按钮，右边占位
            msg_btn_right_tv.setVisibility(View.INVISIBLE);
        }

        if( !bs.get(1) && bs.get(0) ){  //仅右边有按钮，向左靠
            sep.setVisibility(View.GONE);
            findViewById(R.id.placeholder1).setVisibility(View.INVISIBLE);
            findViewById(R.id.placeholder2).setVisibility(View.INVISIBLE);
        }
    }

    private void handleLeftButton(){
//        ToastUtil.showInfo(getContext(), crashMessageBean.getMsgDetailsBtnActionLeft().toString());
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK,
                ButtonExposureOrClickReportEvent.BUTTON_ID_MESSAGE_BUBBLE_DETAIL_BUTTON_1
                , crashMessageBean.getMsgDetailsBtnTextLeft(), getPageId(), ReportConstants.CONTROL_TYPE_SCREEN,
                ReportConstants.DIALOG_ID_MESSAGE_BUBBLE));


        if (CrashPlayerHelper.getInstance().isPlay()) {
            CrashPlayerHelper.getInstance().playEnd();
        }

        CrashMessageButtonActionBean action = crashMessageBean.getMsgDetailsBtnActionLeft();
        if(action == null) {
            dismiss();
            return;
        }
        if(action.getActionType() == 5){
            if( StringUtil.isEmpty(crashMessageBean.getMsgQrUrl()) ){
                dismiss();
                return;
            }

            activity_register_big_rl.setVisibility(View.VISIBLE);
            RequestOptions rqOpts = new RequestOptions().placeholder(R.drawable.glide_loading_animation);
            Glide.with(getContext()).applyDefaultRequestOptions(rqOpts).load(crashMessageBean.getMsgQrUrl()).into(ok_qr_iv);

        } else {
            dismiss();
            execButtonAction(action);
        }

//        MessageButtonActionHelper.handleButtonAction(this.getContext(), action);
    }

    private void execButtonAction(CrashMessageButtonActionBean action){
        ComponentClient.obtainBuilder(MessageButtonActionComponentConst.NAME)
                .setActionName(MessageButtonActionComponentConst.EXEC_MESSAGE_BUTTON_ACTION)
                .addParam("context", getContext())
                .addParam("buttonAction", action)
                .build().call();
    }

    private void handleRightButton(){
//        ToastUtil.showInfo(getContext(), crashMessageBean.getMsgDetailsBtnActionRight().toString());
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK,
                ButtonExposureOrClickReportEvent.BUTTON_ID_MESSAGE_BUBBLE_DETAIL_BUTTON_2
                , crashMessageBean.getMsgDetailsBtnTextRight(), getPageId(), ReportConstants.CONTROL_TYPE_SCREEN,
                ReportConstants.DIALOG_ID_MESSAGE_BUBBLE));


        dismiss();
        if (CrashPlayerHelper.getInstance().isPlay()) {
            CrashPlayerHelper.getInstance().playEnd();
        }

        CrashMessageButtonActionBean action = crashMessageBean.getMsgDetailsBtnActionRight();
        if(action == null) {
            return;
        }
        execButtonAction(action);
//        MessageButtonActionHelper.handleButtonAction(this.getContext(), action);
    }


    public String getPageId() {
        return Constants.PAGE_ID_MESSAGE_DETAILS;
    }

    @Override
    protected void onStart() {
        super.onStart();
        CommonUtils.getInstance().initGreyStyle(getWindow());
        this.lastPageId = ReportParameterManager.getInstance().getPage();
        String pageId = getPageId();
        if (!StringUtil.isEmpty(pageId)) {
            Log.i(ReportConstants.REPORT_TAG, "设置pageid= " + pageId);
            ReportHelper.getInstance().setPage(pageId);
        }

        startTime = System.currentTimeMillis();

//        resetPaddingMargetFromTargetToDecor(R.id.root_view);
    }

    private void resetPaddingMargetFromTargetToDecor(int target){
        Window window = getWindow();
        if(window == null){
            return;
        }

        View targetView = window.getDecorView().findViewById(target);

        View tmp = targetView;

        while (tmp != null){
            tmp.setPadding(0, 0, 0, 0);
            ViewGroup.LayoutParams lp = tmp.getLayoutParams();
            if(lp instanceof ViewGroup.MarginLayoutParams){
                ViewGroup.MarginLayoutParams mp = (ViewGroup.MarginLayoutParams) lp;
                mp.leftMargin = 0;
                mp.topMargin = 0;
                mp.rightMargin = 0;
                mp.bottomMargin = 0;
            }
            ViewParent vp = tmp.getParent();
            if(vp instanceof ViewGroup){
                tmp = (ViewGroup)vp;
            } else {
                tmp = null;
            }
        }
    }

    public SpannableStringBuilder getRadiusGradientSpan(String string, int lineHeight) {
        SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder(string);
        LinearGradientFontSpan span = new LinearGradientFontSpan(
                ResUtil.getColor(R.color.activity_dateils_time_text_start_color)
                , ResUtil.getColor(R.color.activity_dateils_time_text_end_color));
        spannableStringBuilder.setSpan(span, 0, spannableStringBuilder.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spannableStringBuilder;

    }

    /**
     * 页面曝光事件上报
     */
    private void reportPageShowEvent() {
        long duration = System.currentTimeMillis() - startTime;
        if (startTime < 0 || duration < 300) {
            return;
        }

        PageShowReportEvent event = new PageShowReportEvent();
        event.setPageId(getPageId());
        event.setPageTime(String.valueOf(duration));
        ReportHelper.getInstance().addEvent(event);

        DialogExposureEvent reportEventBean = new DialogExposureEvent(ReportConstants.DIALOG_ID_MESSAGE_BUBBLE, getPageId(), duration, null);
        if (isMsgList) {
            reportEventBean.setPageId(Constants.PAGE_ID_MESSAGE);
            reportEventBean.setPage(Constants.PAGE_ID_MESSAGE);
        } else {
            reportEventBean.setPageId(Constants.PAGE_ID_MESSAGE_CARD);
            reportEventBean.setPage(Constants.PAGE_ID_MESSAGE_CARD);
        }
        ReportHelper.getInstance().addEvent(reportEventBean);
        Log.i("BaseDialogFragment", "report=" + duration);
        startTime = -1;
    }
}
