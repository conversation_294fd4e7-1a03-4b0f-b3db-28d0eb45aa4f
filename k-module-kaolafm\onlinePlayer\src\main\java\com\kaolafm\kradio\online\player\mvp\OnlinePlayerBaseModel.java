package com.kaolafm.kradio.online.player.mvp;

import com.kaolafm.kradio.lib.base.mvp.BaseModel;

/**
 * <AUTHOR>
 * @date 2019/3/6
 */
public class OnlinePlayerBaseModel extends BaseModel {

//    private LifecycleTransformer mLifecycleTransformer;

    OnlinePlayerBaseModel() {
    }
//    PlayerBaseModel(LifecycleTransformer lifecycleTransformer) {
//        mLifecycleTransformer = lifecycleTransformer;
//    }

    @Override
    public void destroy() {

    }

//    public void getAlbumDetails(long id, HttpCallback<AlbumDetails> callback) {
//        AlbumRequest albumRequest = new AlbumRequest();
//        albumRequest.getAlbumDetails(id, callback);
//    }
//
//    public void getLyrics(long audioId, HttpCallback<String> httpCallback) {
//        QQMusicRequest qqMusicRequest = new QQMusicRequest();
//        qqMusicRequest.getLyrics(audioId, httpCallback);
//    }
//
//    public void getRadioDetails(long id, HttpCallback<RadioDetails> callback) {
//        RadioRequest radioRequest = new RadioRequest();
//        radioRequest.getRadioDetails(id, callback);
//    }
}
