<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/widget_layout"
    android:layout_width="390dp"
    android:layout_height="530dp">
    <!--android:layout_width="390px"-->
    <!--android:layout_height="530px"-->

    <LinearLayout
        android:id="@+id/logo_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginLeft="19dp"
        android:layout_marginTop="19dp"
        >

        <ImageView
            android:id="@+id/widge_logo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/widget_logo" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignLeft="@+id/widge_logo"
            android:layout_alignTop="@+id/widge_logo"
            android:layout_marginLeft="5px"
            android:text="吉讯电台"
            android:textColor="#A3A3A3"
            android:layout_gravity="center_vertical"
            android:textSize="15sp" />

    </LinearLayout>

    <FrameLayout
        android:id="@+id/widget_cover_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_below="@+id/logo_layout"
        android:layout_marginTop="38dp">

        <ImageView
            android:id="@+id/widget_radio_cover"
            android:layout_width="132dp"
            android:layout_height="132dp"
            android:src="@drawable/widget_default_cover" />

        <ImageView
            android:id="@+id/widget_radio_flag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/widget_live_flag"
            android:visibility="gone" />

    </FrameLayout>

    <TextView
        android:id="@+id/widget_audio_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/widget_cover_layout"
        android:layout_marginTop="30dp"
        android:ellipsize="end"
        android:gravity="center"
        android:minLines="1"
        android:paddingLeft="30dp"
        android:paddingRight="30dp"
        android:singleLine="true"
        android:textColor="@color/colorWhite"
        android:textSize="26sp" />

    <TextView
        android:id="@+id/widget_radio_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/widget_audio_name"
        android:layout_marginTop="8dp"
        android:ellipsize="end"
        android:gravity="center"
        android:minLines="1"
        android:paddingLeft="30dp"
        android:paddingRight="30dp"
        android:singleLine="true"
        android:textColor="#A3A3A3"
        android:textSize="23sp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="20dp"
        android:gravity="center"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/widget_left_btn"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:layout_weight="1"
            android:src="@drawable/widget_unsubscription" />

        <ImageView
            android:id="@+id/widget_play_pause"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:layout_weight="1"
            android:src="@drawable/selector_widget_play" />

        <ImageView
            android:id="@+id/widget_right_btn"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:layout_weight="1"
            android:src="@drawable/selector_widget_next" />

    </LinearLayout>

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/widget_yikatong_bg" />

</RelativeLayout>