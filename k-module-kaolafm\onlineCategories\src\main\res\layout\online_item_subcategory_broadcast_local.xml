<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_broadcast"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginRight="@dimen/m30"
    android:layout_marginBottom="@dimen/m30"
    android:background="@drawable/online_bg_broadcast_list_item"
    android:orientation="horizontal"
    android:paddingEnd="@dimen/x20"
    tools:ignore="RtlSymmetry">

    <com.kaolafm.kradio.component.ui.base.view.OvalImageView
        android:id="@+id/iv_broadcast_cover"
        android:layout_width="@dimen/m114"
        android:layout_height="@dimen/m114"
        android:background="@drawable/online_sh_bg_r8l_fff"
        android:contentDescription="@null"
        android:scaleType="fitXY"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:rid_type="4"
        tools:src="@drawable/ic_launcher" />

    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/online_sh_bg_r8l"
        app:layout_constraintBottom_toBottomOf="@id/iv_broadcast_cover"
        app:layout_constraintEnd_toEndOf="@id/iv_broadcast_cover"
        app:layout_constraintStart_toStartOf="@id/iv_broadcast_cover"
        app:layout_constraintTop_toTopOf="@id/iv_broadcast_cover" />

    <TextView
        android:id="@+id/tv_broadcast_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical|start"
        android:layout_marginStart="@dimen/x30"
        android:layout_marginTop="@dimen/x18"
        android:layout_marginEnd="@dimen/x18"
        android:ellipsize="end"
        android:gravity="center_vertical|start"
        android:lines="1"
        android:singleLine="true"
        android:textColor="@color/online_broadcast_list_item_text_color"
        android:textSize="@dimen/text_size4"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_broadcast_cover"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="北京FM  2.2.1" />


    <ImageView
        android:id="@+id/ivPlay"
        android:layout_width="@dimen/m13"
        android:layout_height="@dimen/m18"
        android:layout_marginTop="@dimen/y13"
        android:scaleType="centerInside"
        android:src="@drawable/online_sl_item_subcategory_broadcast_subimg"
        app:layout_constraintStart_toStartOf="@id/tv_broadcast_name"
        app:layout_constraintTop_toBottomOf="@id/tv_broadcast_name"
        tools:text="91.8万" />

    <TextView
        android:id="@+id/tvListenNum"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/x7"
        android:textColor="@color/online_sl_item_subcategory_broadcast_subtxt"
        android:textSize="@dimen/text_size2"
        app:layout_constraintBottom_toBottomOf="@id/ivPlay"
        app:layout_constraintStart_toEndOf="@id/ivPlay"
        app:layout_constraintTop_toTopOf="@id/ivPlay"
        tools:text="91.8万" />
    <!--    <ImageView-->
    <!--        android:id="@+id/pi_broadcast_playing_indicator"-->
    <!--        android:layout_width="@dimen/player_list_playing_bar_size"-->
    <!--        android:layout_height="@dimen/player_list_playing_bar_size"-->
    <!--        android:layout_gravity="center_vertical"-->
    <!--        android:layout_marginRight="@dimen/m30"-->
    <!--        app:layout_constraintEnd_toEndOf="parent"-->
    <!--        android:visibility="gone"-->
    <!--        app:layout_constraintBottom_toBottomOf="parent"-->
    <!--        app:layout_constraintTop_toTopOf="parent"-->
    <!--        android:layout_marginStart="@dimen/x20"-->
    <!--        android:background="@drawable/playing_bar_chart" />-->

</androidx.constraintlayout.widget.ConstraintLayout>
