package com.kaolafm.kradio.flavor.impl;

import android.app.ActivityManager;
import android.content.Context;
import android.os.Build;
import androidx.annotation.RequiresApi;
import android.view.View;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioHomeExitInter;

import java.util.List;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: lidan
 * @time: 2021-05-27 15:54
 ******************************************/
public class KRadioHomeExitImpl implements KRadioHomeExitInter {

    private static final String TAG = "KRadioHomeExitImpl";

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    @Override
    public boolean isShowHomeExitBtn(View view, boolean show) {
        view.setVisibility(show ? View.VISIBLE : View.GONE);
        view.setOnClickListener(v -> {
            // 销毁任务栈
            ActivityManager activityManager = (ActivityManager) AppDelegate.getInstance().getContext().getSystemService(Context.ACTIVITY_SERVICE);
            List<ActivityManager.AppTask> appTaskList = activityManager.getAppTasks();
            for (ActivityManager.AppTask appTask : appTaskList) {
                appTask.finishAndRemoveTask();
            }
        });

        return false;
    }
}
