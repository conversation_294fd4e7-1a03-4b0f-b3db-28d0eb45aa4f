package com.kaolafm.kradio.component.ui.brandpage;

import androidx.annotation.NonNull;
import android.view.View;

import com.kaolafm.kradio.component.ui.R;
import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.component.ui.base.CellBinder;
import com.kaolafm.kradio.component.ui.base.ItemClickSupport;
import com.kaolafm.kradio.component.ui.brandpage.carownerradio.CarOwnerRadio;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.live.model.LiveInfoDetail;
import com.kaolafm.opensdk.api.operation.model.ImageFile;
import com.kaolafm.opensdk.api.operation.model.column.ColumnContent;
import com.kaolafm.opensdk.api.operation.model.column.LiveProgramDetailColumnMember;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 品牌电台内容组件
 */
public class ComponentBrandPageCardCell extends HomeCell implements CellBinder<View, ComponentBrandPageCardCell>, ItemClickSupport {

    public CarOwnerRadio car_owner_radio;
    private boolean isShow;
    @Override
    public void mountView(@NonNull ComponentBrandPageCardCell data, @NonNull View view, int position) {
        if (data.getContentList() == null || data.getContentList().size() == 0) {
            return;
        }
        car_owner_radio=view.findViewById(R.id.car_owner_radio);
//        mockContentList(data);
        car_owner_radio.updateDate(data);
        view.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onViewClickListener != null) {
                    v.setTag(0);
                    onViewClickListener.onViewClick(v, getPositionInParent());
                }
            }
        });
    }

    private void mockContentList(ComponentBrandPageCardCell data){
        LiveProgramDetailColumnMember live = new LiveProgramDetailColumnMember();
        live.setId("2333");
        live.setCode("10073184");
        live.setTitle("经济之声~~~~~~~~ abcdefghijklmnopqrstuvwxyz");
        live.setLiveStatus(LiveInfoDetail.STATUS_FINISHED);
        live.setResType(ResType.TYPE_LIVE);
        Map<String, ImageFile> map = new HashMap<>();
        ImageFile imageFile = new ImageFile();
        imageFile.setUrl("https://iovimg.radio.cn/mz/images/202310/8391e0cf-0975-4857-bd59-7fcad5931e14/default.jpg");
        imageFile.setWidth(550);
        imageFile.setHeight(550);
        map.put("cover", imageFile);
        live.setImageFiles(map);
        List<ColumnContent> list = new ArrayList<>();
        list.add(live);
        data.setContentList(list);

    }

    @Override
    public int getItemType() {
        return R.layout.component_brand_page_card_layout;
    }

    private BaseHolder.OnViewClickListener onViewClickListener;

    @Override
    public void setOnItemClickListener(BaseHolder.OnViewClickListener listener) {
        onViewClickListener = listener;
    }
}
