package com.kaolafm.kradio.flavor.impl;

import android.content.Intent;
import android.util.Log;


import com.kaolafm.kradio.lib.base.flavor.KRadioAudioPlayLogicInter;




import static com.kaolafm.kradio.receiver.BYDMediaModeReceiver.MEDIA_MODE_ACTION;

/**
 * <pre>
 *     <AUTHOR> Wenchl
 *     e-mail : <EMAIL>
 *     time   : 2019/07/28
 *     desc   :
 *     version: 1.0
 * </pre>
 */
public class KRadioAudioPlayLogicImpl implements KRadioAudioPlayLogicInter {
    @Override
    public boolean autoPlayAudio(Object... args) {
        return false;
    }

    @Override
    public boolean requestAudioFocus(Object... args) {
        boolean flag = PlayerManager.getInstance().requestAudioFocus();
        return flag;
    }

    @Override
    public boolean resumeAudioPlayLogic(Object... args) {
        if (args != null && args.length > 0) {
            if (args[0] instanceof Intent) {
                Intent intent = (Intent) args[0];

                String mode = null;
                PlayerManager klAutoPlayerManager = KLAutoPlayerManager.getInstance();
                if (intent != null) {
                    mode = intent.getStringExtra(MEDIA_MODE_ACTION);
                }
                Log.i("byd KRadioAudioPlayLogicImpl", "MainActivity  mode : " + mode);

                if (MEDIA_MODE_ACTION.equals(mode)) {
                    //回到考拉FM必须要先强占音频焦点
                    if (PlayerManager.getInstance().getCurrentAudioFocusStatus() < 0) {
                        requestAudioFocus();
                    }

                    PlayItem playItem = klAutoPlayerManager.getCurrentPlayItem();
                    if (playItem != null && !klAutoPlayerManager.isPlaying()) {
                        PlayerManagerHelper.getInstance().switchPlayerStatus(true);
                    }
                } else {
                    if (!klAutoPlayerManager.isPlaying() && PlayerManager.getInstance().getCurrentAudioFocusStatus() < 0) {
                        requestAudioFocus();
                        PlayerManagerHelper.getInstance().switchPlayerStatus(true);
                    }
                }
            }
        }
        return false;
    }

    @Override
    public boolean restartAudioPlayLogic(Object... args) {
        return false;
    }

    @Override
    public boolean doStartInPlay(Object... args) {
        return false;
    }
}