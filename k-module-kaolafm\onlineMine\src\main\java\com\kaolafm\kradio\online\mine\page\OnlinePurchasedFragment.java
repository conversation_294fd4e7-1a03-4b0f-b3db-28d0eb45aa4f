package com.kaolafm.kradio.online.mine.page;

import androidx.annotation.NonNull;
import android.util.Log;
import android.view.View;
import android.widget.RelativeLayout;

import com.kaolafm.kradio.component.ComponentClient;
import com.kaolafm.kradio.component.DynamicComponent;
import com.kaolafm.kradio.component.MainThreadable;
import com.kaolafm.kradio.component.RealCaller;
import com.kaolafm.kradio.constant.UserComponentConst;
import com.kaolafm.kradio.constant.UserStateObserverProcessorConst;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.mvp.IPresenter;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.lib.base.ui.BaseLazyFragment;
import com.kaolafm.kradio.lib.base.ui.online.BaseViewPagerLazyFragment;
import com.kaolafm.kradio.lib.utils.ComponentUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.online.mine.purchased.OnlinePurchasedLoginedFragment;
import com.kaolafm.kradio.online.mine.purchased.OnlinePurchasedUnloginFragment;
import com.kaolafm.kradio.user.UserInfoManager;
import com.trello.rxlifecycle3.LifecycleTransformer;

public class OnlinePurchasedFragment extends BaseViewPagerLazyFragment {
    RelativeLayout homeRoot;

    OnlinePurchasedLoginedFragment onlinePurchasedLoginedFragment;
    OnlinePurchasedUnloginFragment pubEmptyFragment;
    SubDynamicComponent mUserStateObserver;

    private OnlinePurchasedLoginedFragment.OnKeyListenListener onKeyListenListener;

    public OnlinePurchasedFragment() {
        // Required empty public constructor
    }


    public static OnlinePurchasedFragment newInstance() {
        OnlinePurchasedFragment fragment = new OnlinePurchasedFragment();
//        Bundle args = new Bundle();
//        args.putString(ARG_PARAM1, param1);
//        args.putString(ARG_PARAM2, param2);
//        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected int getLayoutId() {
        return R.layout.online_fragment_purchased;
    }

    @Override
    protected IPresenter createPresenter() {
        return null;
    }

    @Override
    public void initView(View view) {
        homeRoot = view.findViewById(R.id.home_root);

        onlinePurchasedLoginedFragment = new OnlinePurchasedLoginedFragment();
        pubEmptyFragment = new OnlinePurchasedUnloginFragment();
        onlinePurchasedLoginedFragment.setOnKeyListenListener(onKeyListenListener);
        this.loadMultipleRootFragment(R.id.home_root, -1, onlinePurchasedLoginedFragment, pubEmptyFragment);

        addLogStatusOB();
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser) {
            updateView();
        }
    }

    @Override
    protected void lazyLoad() {

    }

    private void updateView() {
        boolean userBound = UserInfoManager.getInstance().isUserBound();
        try {
            if (userBound) {
                //每次显示已购页面都需要刷新数据
                onlinePurchasedLoginedFragment.updateData();
                showHideFragment(onlinePurchasedLoginedFragment);
            } else {
                showHideFragment(pubEmptyFragment);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void addLogStatusOB() {
        mUserStateObserver = new SubDynamicComponent();
        ComponentUtil.addObserverNoRepeat(UserComponentConst.NAME, mUserStateObserver);
    }

    class SubDynamicComponent implements DynamicComponent, MainThreadable {

        @Override
        public String getName() {
            return "PubHomeFragment-DynamicComponent";
        }

        @Override
        public boolean onCall(RealCaller caller) {
            Log.d("PurchasedHomeFragment1", "caller.actionName() = " + caller.actionName());
            switch (caller.actionName()) {
                case UserStateObserverProcessorConst.USER_LOGIN:
                case UserStateObserverProcessorConst.USER_LOGOUT: {
//                    updateView();
                    break;
                }
            }
            return false;
        }

        @Override
        public Boolean shouldActionRunOnMainThread(String actionName, ComponentClient caller) {
            return true;
        }
    }

    @NonNull
    @Override
    public LifecycleTransformer bindUntilEvent(@NonNull Object event) {
        return null;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        ComponentUtil.removeObserver(UserComponentConst.NAME, mUserStateObserver);
    }

    @Override
    public void onResume() {
        super.onResume();
        updateView();
    }

    @Override
    protected void changeViewLayoutForStatusBar(View view) {
    }

    @Override
    protected void addFragmentRootViewPadding(View view) {
    }


    public void setOnKeyListenListener(OnlinePurchasedLoginedFragment.OnKeyListenListener onKeyListenListener) {
        this.onKeyListenListener = onKeyListenListener;
    }

    public boolean isReportFragment() {
        return true;
    }

    @Override 
    public String getPageId() {
        return Constants.ONLINE_PAGE_ID_MINE_PURCHASED;
    }
}
