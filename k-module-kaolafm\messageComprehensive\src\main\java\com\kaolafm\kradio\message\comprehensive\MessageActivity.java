package com.kaolafm.kradio.message.comprehensive;

import android.content.res.Configuration;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;

import androidx.annotation.IdRes;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageButton;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.util.Log;
import android.util.TypedValue;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.kaolafm.kradio.lib.base.flavor.ThemeInter;
import com.kaolafm.kradio.lib.base.flavor.UserCenterInter;
import com.kaolafm.kradio.lib.bean.CrashMessageBean;
import com.kaolafm.kradio.lib.basedb.manager.MessageDaoManager;
import com.kaolafm.kradio.common.base.BaseSkinAppCompatActivity;
import com.kaolafm.kradio.common.utils.CommonUtils;
import com.kaolafm.kradio.component.ComponentClient;
import com.kaolafm.kradio.component.DynamicComponent;
import com.kaolafm.kradio.component.MainThreadable;
import com.kaolafm.kradio.component.RealCaller;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.kradio.constant.UserComponentConst;
import com.kaolafm.kradio.constant.UserStateObserverProcessorConst;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.ComponentUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.common.router.IRouterConsumer;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.purchase.PayManager;
import com.kaolafm.kradio.purchase.constant.PayConst;
import com.kaolafm.kradio.user.UserInfoManager;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.crash.CrashPlayerHelper;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.event.PageShowReportEvent;
import com.kaolafm.report.util.ReportConstants;
import com.kaolafm.report.util.ReportParameterManager;
import com.lcodecore.tkrefreshlayout.utils.ScrollingUtil;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

import io.reactivex.Single;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;


/**
 * 消息盒子
 */
@Route(path = RouterConstance.MESSAGE_COMPREHENSIVE_URL)
public class MessageActivity extends BaseSkinAppCompatActivity<MessagePresenter> implements IRouterConsumer {
    private static final String TAG = "OnlineMessageActivity";

    ImageView message_back_mine;
    AppCompatImageButton msg_tips_ib;
    RecyclerView msg_rv;
    View mVsLayoutErrorPage;
    private View mErrorView;
    private DynamicComponent mRadioPlayerUserObserver;
    private MessageAdapter messageAdapter;
    private List<CrashMessageBean> messageBeanList = new ArrayList<>();
    protected long startTime = -1;

    @Override
    protected MessagePresenter createPresenter() {
        return new MessagePresenter();
    }

    @Override
    public int getLayoutId() {
        return R.layout.message_activity_layout;
    }

    @Override
    public int getLayoutId_Tow() {
        return 0;
    }

    @Override
    public String getPageId() {
        return Constants.PAGE_ID_MESSAGE;
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        CommonUtils.getInstance().initGreyStyle(getWindow());
        mRadioPlayerUserObserver = new RadioPlayerUserObserver();
        ComponentUtil.addObserver(UserComponentConst.NAME, mRadioPlayerUserObserver);

        handleVoiceScroll(R.id.cd_up, R.id.cd_down, msg_rv);

        TextView all_message_read = findViewById(R.id.all_message_readed);
        all_message_read.setOnClickListener(v -> handleAllMessageReaded());
    }

    private Disposable handleAllMessageReadedJob;
    private void handleAllMessageReaded(){
        handleAllMessageReadedJob = Single.just(1)
                .subscribeOn(Schedulers.io())
                .map(it -> {
                    List<CrashMessageBean> list = MessageDaoManager.getInstance().getAllUnReadedSync();
                    if(list != null && list.size() > 0){
                        for(int i = 0; i < list.size(); i++){
                            CrashMessageBean bean = list.get(i);
                            if( bean != null && !StringUtil.isEmpty(bean.getMsgId())){
                                CrashPlayerHelper.getInstance().getMessageByIdRemove(bean.getMsgId());
                                MessageDaoManager.getInstance().updateLookSync(bean.getMsgId());
                            }
                        }
                    }
                    return 1;
                })
                .observeOn(AndroidSchedulers.mainThread())
                .doOnEvent((t1, t2) -> messageAdapter.notifyDataSetChanged())
                .subscribe((it)->{
                    ToastUtil.showNormal(MessageActivity.this, getString(R.string.all_message_readed_succeed));
                }, (it)->{
                    ToastUtil.showNormal(MessageActivity.this, getString(R.string.all_message_readed_failed)+" : "+it.getMessage());
                });
    }

    private void handleVoiceScroll(@IdRes int upViewId, @IdRes int downViewId, RecyclerView recyclerView){
        View.OnClickListener listener = v -> {
            if(upViewId == v.getId()){
                ScrollingUtil.scrollListByVoice(msg_rv, -1);
//                ToastUtil.showNormal(AppDelegate.getInstance().getContext(), "向上滑动");
            } else if(downViewId == v.getId()){
                ScrollingUtil.scrollListByVoice(msg_rv, 1);
//                ToastUtil.showNormal(AppDelegate.getInstance().getContext(), "向下滑动");
            }
        };
        // 所见即可说
        TextView upScroll = findViewById(R.id.cd_up);
        if(upScroll != null){
            upScroll.setOnClickListener(listener);
        }
        TextView downScroll = findViewById(R.id.cd_down);
        if(downScroll != null){
            downScroll.setOnClickListener(listener);
        }
    }

    @Override
    protected void onStart() {
        super.onStart();
        startTime = System.currentTimeMillis();
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        message_back_mine=findViewById(R.id.message_back_mine);
        msg_tips_ib=findViewById(R.id.msg_tips_ib);
        msg_rv=findViewById(R.id.msg_rv);
        mVsLayoutErrorPage=findViewById(R.id.vs_layout_error_page);


        RouterManager.getInstance().addRouterConsumer(this);
        message_back_mine.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        // 初始化图标，使用主题切换方法统一处理
        updateMsgTipsIcon();
        msg_tips_ib.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //提示
                ToastUtil.showNormal(AppDelegate.getInstance().getContext(), getString(R.string.message_activity_tips_text));
                ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_MESSAGE_BOX_HINT
                        , null, getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
            }
        });

        messageAdapter = new MessageAdapter();
        msg_rv.setLayoutManager(new LinearLayoutManager(this));
        msg_rv.setAdapter(messageAdapter);
        DividerItemDecoration decor = new DividerItemDecoration(this, DividerItemDecoration.VERTICAL);
        ColorDrawable drawable = new ColorDrawable(ResUtil.getColor(R.color.message_list_line_color));
        drawable.setBounds(0, 0, 0, ResUtil.getDimen(R.dimen.m1));
        decor.setDrawable(drawable);
        msg_rv.addItemDecoration(decor);
        messageAdapter.setDataList(messageBeanList);
        messageAdapter.setOnItemClickListener(new BaseAdapter.OnItemClickListener<CrashMessageBean>() {
            @Override
            public void onItemClick(View view, int viewType, CrashMessageBean onlineMessageBean, int position) {
                Log.i(TAG, "onItemClick: MessageDetailsDialogFragment");
                MessageDetailsDialogFragment dialogFragment
                        = new MessageDetailsDialogFragment(MessageActivity.this);
                dialogFragment.setCrashMessageBean(onlineMessageBean)
                        .setMsgList(true)
                        .show();
//                CrashPlayerHelper.getInstance().addImmediatelyplayDate(AppDateUtils.getInstance().changeDate(messageBeanList.get(position))).startPlay();
                MessageDaoManager.getInstance().updateLook(messageBeanList.get(position).getMsgId());
                boolean b = CrashPlayerHelper.getInstance().getMessageByIdRemove(messageBeanList.get(position).getMsgId());
                Log.d(TAG, "----------插播列表删除---------msgId=" + messageBeanList.get(position).getMsgId() + "----结果：" + b);
                msg_rv.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        //刷新列表已读状态
                        initData();
                    }
                }, 100);
            }
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_MESSAGE_BOX_HINT
                , null, getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
        initData();
        watchUserStateChangeAndReplayAudioList();
    }

    private void watchUserStateChangeAndReplayAudioList() {
        UserInfoManager userInfoManager = UserInfoManager.getInstance();
        boolean loinStateChange = userInfoManager.isLoginStateChange();
        boolean vipStateChange = userInfoManager.isVipStateChange();
        Log.i(TAG, "loinStateChange:" + loinStateChange + " vipStateChange:" + vipStateChange);
        if (loinStateChange || vipStateChange) {
            replayAudioList();
        }
//        replayAudioList();
    }

    @Override
    protected void onDestroy() {
        RouterManager.getInstance().removeRouterConsumer(this);
        super.onDestroy();
        ComponentUtil.removeObserver(UserComponentConst.NAME, mRadioPlayerUserObserver);

        if(handleAllMessageReadedJob != null && !handleAllMessageReadedJob.isDisposed()){
            handleAllMessageReadedJob.dispose();
        }
    }

    @Override
    public void initData() {
        if (messageAdapter != null) {
            messageBeanList.clear();
            messageBeanList = MessageDaoManager.getInstance().queryAllSync();
            messageAdapter.setDataList(messageBeanList);
            if (messageBeanList.isEmpty()) {
                mVsLayoutErrorPage.setVisibility(View.VISIBLE);
            } else {
                mVsLayoutErrorPage.setVisibility(View.GONE);
            }
        }
    }

    @Override
    public String consumeRoute(String pageId, Object extra) {
        switch (pageId) {
            case Constants.ONLINE_PAGE_ID_PAY_VIP: //VIP购买
                if (ComponentUtil.getResultValue(UserComponentConst.NAME, UserComponentConst.IS_USER_BOUND)) {
                    PayManager.getInstance()
                            .pay(PayConst.PAY_TYPE_VIP, PlayerManagerHelper.getInstance().getCurPlayItem());
                } else {
                    Bundle bundle = new Bundle();
                    bundle.putString("type", ReportParameterManager.getInstance().getPage());
                    RouterManager.getInstance().jumpPage(RouterConstance.LOGIN_COMPREHENSIVE_URL);
                }
                break;
        }
        return ROUTER_CONSUME_FULLY;
    }

    private class RadioPlayerUserObserver implements DynamicComponent, MainThreadable {

        @Override
        public String getName() {
            return "onlineMessage-UserObserver";
        }

        @Override
        public boolean onCall(RealCaller caller) {
            String actionName = caller.actionName();
            switch (actionName) {
                case UserStateObserverProcessorConst.USER_LOGIN:
                    break;
                case UserStateObserverProcessorConst.USER_LOGOUT:
                    replayAudioList();
                    break;
                default:
                    break;
            }
            return false;
        }

        @Override
        public Boolean shouldActionRunOnMainThread(String actionName, ComponentClient caller) {
            return true;
        }
    }

    private void replayAudioList() {
//        public static final int BUY_TYPE_FREE = 0;//免费
//        public static final int BUY_TYPE_AUDITION = 1;//试听
//        public static final int BUY_TYPE_AUDIO = 2;//单曲购买
//        public static final int BUY_TYPE_ALBUM = 3;//专辑购买
//        public static final int BUY_TYPE_VIP = 4;//vip购买
        Log.d(TAG, "replayAudioList");
        if (PlayerManagerHelper.getInstance().isPlaying()) {
            //如果退出登录当前播放的资源是需要付费的就要刷新播放
            switch (PlayerManagerHelper.getInstance().getCurPlayItem().getBuyType()) {
                case AudioDetails.BUY_TYPE_AUDIO:
                case AudioDetails.BUY_TYPE_ALBUM:
                case AudioDetails.BUY_TYPE_VIP:
                    long mRadioId = Long.parseLong(PlayerManagerHelper.getInstance().getCurPlayItem().getRadioId());
                    int mRadioType = PlayerManagerHelper.getInstance().getCurPlayItem().getType();
                    PlayerManagerHelper.getInstance().restart(String.valueOf(mRadioId), mRadioType);
                    break;
            }
        }
    }

    @Override
    public boolean useEventBus() {
        return true;
    }

    /**
     * 监听主题切换事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onThemeChangeEvent(UserCenterInter.ThemeChangeEvent event) {
        if (messageAdapter != null) {
            messageAdapter.notifyDataSetChanged();
        }
        // 更新消息提示图标
        updateMsgTipsIcon();
    }

    private void updateMsgTipsIcon() {
        if (msg_tips_ib == null) {
            return;
        }

        // 使用Settings数据库获取当前主题，与项目主题切换架构保持一致
        String currentTheme = getCurrentThemeFromSettings();
        if ("theme.nonight".equals(currentTheme)) {
            // 白天模式
            msg_tips_ib.setImageDrawable(ResUtil.getDrawable(R.drawable.msg_tips_icon_day));
        } else {
            // 夜间模式
            msg_tips_ib.setImageDrawable(ResUtil.getDrawable(R.drawable.msg_tips_icon));
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        reportPageShowEvent();
    }

    /**
     * 页面曝光事件上报
     */
    private void reportPageShowEvent() {
        long duration = System.currentTimeMillis() - startTime;
        if (startTime < 0 || duration < 300) {
            return;
        }

        PageShowReportEvent event = new PageShowReportEvent();
        event.setPageId(getPageId());
        event.setPageTime(String.valueOf(duration));
        ReportHelper.getInstance().addEvent(event);
        Log.i("BaseDialogFragment", "report=" + duration);
        startTime = -1;
    }
}
