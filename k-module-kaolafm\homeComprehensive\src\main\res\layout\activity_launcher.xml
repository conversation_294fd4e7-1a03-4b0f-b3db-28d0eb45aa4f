<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/launcher_main_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_home"
    android:paddingLeft="@dimen/activity_root_view_padding_left">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="@dimen/activity_root_view_margin_left"
        android:layout_marginBottom="@dimen/activity_root_view_margin_bottom">

        <com.kaolafm.kradio.home.comprehensive.widget.HomeBackBar
            android:id="@+id/hbr_home_back"
            android:layout_width="@dimen/home_back_bar_width"
            android:layout_height="match_parent"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <FrameLayout
            android:id="@+id/fl_launcher_root_content"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginBottom="@dimen/default_play_bar_height"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/hbr_home_back"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.kaolafm.kradio.home.comprehensive.playerbar.ComprehensivePlayerBar
            android:id="@+id/pb_home_play"
            android:layout_width="0dp"
            android:layout_marginBottom="@dimen/y20"
            android:layout_height="@dimen/player_bar_main_height"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/hbr_home_back"
            app:layout_constraintRight_toRightOf="parent" />

        <Button
            android:visibility="gone"
            tools:visibility="visible"
            android:id="@+id/on_msg_test"
            android:text="测试消息"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick="onMsgTest"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <Button
            android:visibility="gone"
            tools:visibility="visible"
            android:id="@+id/show_guide"
            android:text="可见即可说"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick="onShowGuide"
            app:layout_constraintEnd_toStartOf="@id/on_msg_test"
            app:layout_constraintBottom_toBottomOf="parent" />

        <Button
            android:visibility="gone"
            android:id="@+id/handle_focus"
            android:text="焦点处理"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick="onClickHandleFocus"
            app:layout_constraintEnd_toStartOf="@id/show_guide"
            app:layout_constraintBottom_toBottomOf="parent" />

        <Button
            android:visibility="gone"
            android:id="@+id/video_speed"
            android:text="未超过限速"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick="onSpeedChange"
            tools:visibility="visible"
            app:layout_constraintEnd_toStartOf="@id/handle_focus"
            app:layout_constraintBottom_toBottomOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <ViewStub
        android:id="@+id/water_mark_viewStub"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout="@layout/water_mark_layout" />

    <ViewStub
        android:id="@+id/app_title_viewStub"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout="@layout/common_text" />

    <View
        android:visibility="gone"
        android:id="@+id/test_guide"
        android:layout_marginStart="@dimen/m100"
        android:layout_marginTop="@dimen/m160"
        android:layout_width="@dimen/m200"
        android:layout_height="@dimen/m200"
        android:background="@color/green01" />

    <ViewStub
        android:id="@+id/guide_view_stub"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout="@layout/guide_layout" />

</FrameLayout>

