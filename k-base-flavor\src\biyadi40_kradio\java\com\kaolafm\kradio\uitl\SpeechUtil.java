package com.kaolafm.kradio.uitl;

import android.app.Application;
import android.content.Intent;

import com.kaolafm.kradio.lib.base.AppDelegate;

public class SpeechUtil {

    public static void sendCmdResult(String action, String code, String msg) {
        Application context = AppDelegate.getInstance().getContext();
        String packageName = context.getPackageName();  //三方应用自己的包名
        Intent intent = new Intent();
        intent.putExtra("EXTRA_RESULT_ACTION", action);//表示当前返回结果对应执行的指令
        intent.putExtra("EXTRA_RESULT_CODE", code);//类型为字符串，必填
        intent.putExtra("EXTRA_RESULT_MESSAGE", msg);//类型为字符串，必填
        intent.putExtra("EXTRA_PACKAGE_NAME", packageName);//类型为字符串，必填
        context.sendBroadcast(intent);
    }
}
