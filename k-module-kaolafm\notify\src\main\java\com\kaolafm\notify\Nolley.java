package com.kaolafm.notify;

import android.os.SystemClock;
import android.util.Log;

import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.AppExecutors;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * <AUTHOR>
 * 通知管理类,仿照volley的队列机制,故名Nolley
 **/
public final class Nolley implements Runnable {

    public static final String TAG = "nolley";
    private volatile static Nolley sInstance;
    private final LinkedBlockingQueue<NotifyParam> mQueue;
    private boolean lock;
    private Interceptor mInterceptor;
    private Map<Integer, Dispatcher> mapDispatchers;

    private Nolley() {
        mapDispatchers = new HashMap<>();
        mQueue = new LinkedBlockingQueue<NotifyParam>();
        new Thread(this).start();
    }

    @Override
    public void run() {
        while (true) {
            try {
                if (lock) {
                    Log.i(TAG, "run: locking...");
                    SystemClock.sleep(2 * 1000);
                    continue;
                }
                NotifyParam param = mQueue.take();
                lock = true;

                //[必须]在take()方法之后再次判断播放状态
                while (mInterceptor != null && mInterceptor.intercept(param)) {
                    Log.i(TAG, "run: [2]被拦截...");
                    SystemClock.sleep(2 * 1000);
                }

                Dispatcher dispatcher = mapDispatchers.get(param.type);
                if (dispatcher != null) {
                    dispatcher.dispatch(param);
                } else {
                    //默认通知
                    AppExecutors.getInstance().mainThread().execute(new Runnable() {
                        @Override
                        public void run() {
                            Log.i(Nolley.TAG, "run: 通知提示.");
                            new NotifyWindow(param.activity)
                                    .setIcon(param.iconUrl)
                                    .setMessage(param.msg)
                                    .setListener(param.callback)
                                    .show();
                        }
                    });
                }


            } catch (InterruptedException e) {
                lock = false;
                e.printStackTrace();
                Log.i(TAG, "error: " + e);
            }

        }
    }

    public static Nolley getInstance() {
        if (sInstance == null) {
            synchronized (Nolley.class) {
                if (sInstance == null) {
                    sInstance = new Nolley();
                }
            }
        }
        return sInstance;
    }

    public void addNotify(NotifyParam param) {
        if (param != null) {
            mQueue.offer(param);
        }
    }

    public void unlock() {
        lock = false;
        Log.i(TAG, "unlock: ");
    }


    public void setInterceptor(Interceptor interceptor) {
        mInterceptor = interceptor;
    }

    public void registerDispatcher(int type, Dispatcher dispatcher) {
        mapDispatchers.put(type, dispatcher);
    }


    public interface Dispatcher {
        void dispatch(NotifyParam param);
    }

    public interface Interceptor {
        boolean intercept(NotifyParam param);
    }

    public static void closeAllToast(){
        ToastUtil.dismiss();
        Nolley.getInstance().unlock();
        ToastUtil.dismissNotify();
    }
}
