<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.kaolafm.kradio.lib">

    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
<!--    <uses-permission android:name="android.permission.REORDER_TASKS" />-->


    <!--用于进行网络定位-->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <!--用于访问GPS定位-->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <!--获取运营商信息，用于支持提供运营商信息相关的接口-->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <!--用于访问wifi网络信息，wifi信息会用于进行网络定位-->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <!--这个权限用于获取wifi的获取权限，wifi信息会用来进行网络定位-->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/>
    <!--用于访问网络，网络定位需要上网-->
    <uses-permission android:name="android.permission.INTERNET"/>
    <!--用于申请调用A-GPS模块-->
    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS"/>
    <!--用于申请获取蓝牙信息进行室内定位-->
    <uses-permission android:name="android.permission.BLUETOOTH"/>
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN"/>
    <application>
        <meta-data
            android:name="com.amap.api.v2.apikey"
            android:value="${AMAP_API_KEY}" />
        <service android:name="com.amap.api.location.APSService" />
<!--        <service android:name=".leaks.LeaksService"/>
        <provider
            android:authorities="${applicationId}.leakcanary-installer"
            android:name="leakcanary.internal.MainProcessAppWatcherInstaller"
            android:exported="false"/>
-->
    </application>
</manifest>
