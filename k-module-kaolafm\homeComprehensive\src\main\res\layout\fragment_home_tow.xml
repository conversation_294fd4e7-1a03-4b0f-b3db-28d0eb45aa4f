<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/home_room"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_home">

    <ImageView
        android:id="@+id/iv_home_exit_btn"
        android:layout_width="@dimen/x45"
        android:layout_height="@dimen/y45"
        android:layout_margin="@dimen/m23"
        android:clickable="true"
        android:focusable="true"
        android:scaleType="fitCenter"
        android:src="@drawable/ic_home_exit"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <!--    <HomeBackBar-->
    <!--        android:id="@+id/hbr_home_back"-->
    <!--        android:layout_width="@dimen/home_back_bar_width"-->
    <!--        android:layout_height="match_parent"-->
    <!--        android:visibility="gone"-->
    <!--        app:layout_constraintBottom_toBottomOf="parent"-->
    <!--        app:layout_constraintLeft_toLeftOf="parent"-->
    <!--        app:layout_constraintTop_toTopOf="parent" />-->
    <com.kaolafm.kradio.home.comprehensive.widget.nav.HomeNavigationLayout
        android:id="@+id/hnl_home_navigation"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/m40"
        android:layout_marginEnd="@dimen/m40"
        app:layout_constraintBottom_toBottomOf="@id/home_login_ll"
        app:layout_constraintEnd_toStartOf="@+id/home_search_ll"
        app:layout_constraintStart_toEndOf="@+id/home_login_ll"
        app:layout_constraintTop_toTopOf="@id/home_login_ll" />

    <com.kaolafm.kradio.home.comprehensive.playerbar.NoScrollViewPager
        android:id="@+id/home_view_page"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/hnl_home_navigation" />

    <LinearLayout
        android:id="@+id/home_login_ll"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/m80"
        android:layout_marginTop="@dimen/m40"
        android:contentDescription="@string/content_desc_my"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="@dimen/m75">

            <RelativeLayout
                android:layout_width="@dimen/m70"
                android:layout_height="@dimen/m70">

                <com.kaolafm.kradio.component.ui.base.view.OvalImageView
                    android:id="@+id/home_main_user_pic_iv"
                    android:layout_width="@dimen/m68"
                    android:layout_height="@dimen/m68"
                    android:layout_centerInParent="true"
                    android:src="@drawable/user_no_login_icon"
                    app:circle="true" />

                <View
                    android:id="@+id/user_avatar_bg"
                    android:layout_width="@dimen/m70"
                    android:layout_height="@dimen/m70"
                    android:layout_centerInParent="true"
                    android:background="@drawable/user_avatar_bg" />
            </RelativeLayout>

            <ImageView
                android:id="@+id/home_main_user_vip_iv"
                android:layout_width="@dimen/m68"
                android:layout_height="@dimen/m28"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:src="@drawable/user_name_header_vip"
                android:visibility="invisible"
                tools:visibility="visible" />
        </RelativeLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/home_search_ll"
        android:layout_width="@dimen/m314"
        android:layout_height="@dimen/m44"
        android:layout_marginTop="@dimen/m54"
        android:layout_marginEnd="@dimen/m80"
        android:background="@drawable/comprehensive_search_home_bg"
        android:contentDescription="@string/content_desc_search"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_width="@dimen/m28"
            android:layout_height="@dimen/m28"
            android:layout_marginStart="@dimen/m30"
            android:src="@drawable/comprehensive_search_icon" />

        <com.kaolafm.kradio.common.widget.banner.KradioBannerView
            android:id="@+id/textBannerView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/m16"
            android:layout_marginEnd="@dimen/m30"
            android:layout_weight="1"
            app:autoStart="true"
            app:playWhenSingleData="false"
            app:setDirection="BOTTOM_TO_TOP" />
    </LinearLayout>


    <ViewStub
        android:id="@+id/vs_home_no_network"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout="@layout/home_no_network_rl"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/home_login_ll" />

    <!--    <ComprehensivePlayerBar-->
    <!--        android:id="@+id/pb_home_play"-->
    <!--        android:layout_width="0dp"-->
    <!--        android:layout_height="@dimen/player_bar_main_height"-->
    <!--        app:layout_constraintBottom_toBottomOf="parent"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/hbr_home_back"-->
    <!--        app:layout_constraintRight_toRightOf="parent" />-->

    <Button
        android:id="@+id/btnTest"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="+1"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <Button
        android:id="@+id/btnTest1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="+2"
        android:visibility="gone"
        app:layout_constraintLeft_toRightOf="@id/btnTest"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        android:id="@+id/pc_loading"
        layout="@layout/refresh_center"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
