package com.kaolafm.kradio.uitl;

import com.kaolafm.opensdk.player.logic.listener.IPlayerStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

/**
 * 仅做包装用，目的是不让下层展示一大堆没用着的空方法
 */
public class PlayStateListenerWrapper implements IPlayerStateListener {
    @Override
    public void onIdle(PlayItem playItem) {

    }

    @Override
    public void onPlayerPreparing(PlayItem playItem) {

    }

    @Override
    public void onPlayerPlaying(PlayItem playItem) {

    }

    @Override
    public void onPlayerPaused(PlayItem playItem) {

    }

    @Override
    public void onProgress(PlayItem playItem, long l, long l1) {

    }

    @Override
    public void onPlayerFailed(PlayItem playItem, int i, int i1) {

    }

    @Override
    public void onPlayerEnd(PlayItem playItem) {

    }

    @Override
    public void onSeekStart(PlayItem playItem) {

    }

    @Override
    public void onSeekComplete(PlayItem playItem) {

    }

    @Override
    public void onBufferingStart(PlayItem playItem) {

    }

    @Override
    public void onBufferingEnd(PlayItem playItem) {

    }

    @Override
    public void onDownloadProgress(PlayItem playItem, long l, long l1) {

    }
}
