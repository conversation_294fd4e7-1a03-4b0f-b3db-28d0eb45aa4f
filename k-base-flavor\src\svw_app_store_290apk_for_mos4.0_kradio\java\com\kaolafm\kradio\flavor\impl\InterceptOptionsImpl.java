package com.kaolafm.kradio.flavor.impl;

import android.util.Log;

import com.kaolafm.kradio.lib.base.flavor.InterceptOptions;

import java.io.IOException;
import java.util.regex.Matcher;

import kotlin.text.Regex;
import okhttp3.Interceptor;
import okhttp3.MediaType;
import okhttp3.Response;
import okhttp3.ResponseBody;

/**
 * author : wxb
 * date   : 2021/12/3
 * desc   :自定义拦截器
 * 这里有时间换成正则表达式 当前为了发版 采取的固定域名替换方式
 * https://*.radio.cn/ 转成https://*.radio.cn:443/
 */
public class InterceptOptionsImpl implements InterceptOptions {

    @Override
    public Response getHttpsIntercept(Response r) {

        try {
            if (DebugImpl.isDebug()) {
                return r;
            }
            ResponseBody responseBody = r.body();

            byte[] respBytes = responseBody.bytes();
            String bodyString = new String(respBytes);

            try {
                //http://reportv2.radio.cn/   http://reportv2.radio.cn:443
//                bodyString = bodyString.replaceAll("http://iovimage.radio.cn/mz/audios", "http://iovimage.radio.cn:443/mz/audios");
                //           https://iovliveplay.radio.cn/fm  https://iovopen.radio.cn/play.m3u8 https://iovimage.radio.cn/mz/aac_64/
                //处理返回数据
                //https://iovimg.radio.cn/*
                //https://iovaudio.radio.cn/*
                //https://iovimage.radio.cn/*
                //https://iovliveplay.radio.cn/*
                //https://iovfmpb.radio.cn/*
                //https://iovm.radio.cn/*
                //https://iovopen.radio.cn/*
                //https://iovmsg.radio.cn/*
                //https://iovwsopen.radio.cn/*
                //https://iovrec.radio.cn/*
                //https://iovapi.radio.cn/*
                //https://iovae.radio.cn/*
                //https://report.radio.cn/*
                //https://iovaew.radio.cn/*
                //https://iovat.radio.cn/*
                //https://iovsearch.radio.cn/*


                bodyString = bodyString.replaceAll("https://", "http://");
                bodyString = bodyString.replaceAll(".radio.cn", ".radio.cn:443");
                bodyString = bodyString.replaceAll(".kaolafm.net", ".kaolafm.net:443");
                bodyString = bodyString.replaceAll(".kaolafm.com", ".kaolafm.com:443");

            } catch (Exception e) {
                e.printStackTrace();
            }
            Log.d("CustomRespnseInterceptor", "bodyString=" + bodyString);
            MediaType mediaType = responseBody.contentType();
            return r.newBuilder()
                    .body(ResponseBody.create(mediaType, bodyString.getBytes())).build();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return r;
    }

}
