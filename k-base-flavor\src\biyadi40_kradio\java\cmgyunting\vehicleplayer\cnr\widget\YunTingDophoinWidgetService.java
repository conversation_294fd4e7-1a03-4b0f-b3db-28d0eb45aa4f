package cmgyunting.vehicleplayer.cnr.widget;

import android.app.Application;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.appwidget.AppWidgetManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.PixelFormat;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.IBinder;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.core.app.NotificationCompat;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.RemoteViews;
import android.widget.Toast;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.byd.BydToast;
import com.kaolafm.kradio.common.CP;
import com.kaolafm.kradio.common.ErrorInfo;
import com.kaolafm.kradio.common.ResultCallback;
import com.kaolafm.kradio.common.SubscribeData;
import com.kaolafm.kradio.common.bean.HistoryItem;
import com.kaolafm.kradio.common.report.ReportUtil;
import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.flavor.impl.KillBroadcastReceiver;
import com.kaolafm.kradio.flavor.utils.PlayerUtil;
import com.kaolafm.kradio.basedb.manager.HistoryDaoManager;
import com.kaolafm.kradio.k_kaolafm.home.player.PlayerHelper;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.IntentUtils;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageConfigImpl;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.player.bean.BroadcastStatus;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
//import com.kaolafm.kradio.receiver.BydThemeBroadcastReceiver;
import com.kaolafm.kradio.subscribe.SubscribeChangeListener;
import com.kaolafm.kradio.subscribe.SubscribeManager;
import com.kaolafm.kradio.subscribe.SubscribeModel;
import com.kaolafm.kradio.subscribe.SubscribeRepository;
import com.kaolafm.kradio.uitl.BimapPlusUtils;
import com.kaolafm.kradio.uitl.ProgressBitmapGenerator;
import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayListControl;
import com.kaolafm.opensdk.player.logic.model.item.AlbumPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.report.event.PlayerUiControlReportEvent;
import com.kaolafm.utils.DateFormatUtil;

import java.lang.reflect.Method;

import cmgyunting.vehicleplayer.cnr.WidgetUtils;
import cmgyunting.vehicleplayer.cnr.YunBydWidget;
import cmgyunting.vehicleplayer.cnr.YunTingWidgetService;
import tv.danmaku.ijk.media.player.IjkMediaPlayer;

//import com.kaolafm.kradio.common.HistoryItem;


/**
 * <AUTHOR>
 */
public class YunTingDophoinWidgetService extends Service {

    public static final String WIDGET_ACTION_NEXT = "kl.action.next";
    public static final String WIDGET_ACTION_PREV = "kl.action.prev";
    public static final String WIDGET_ACTION_PAUSE = "kl.action.pause";
    public static final String WIDGET_ACTION_KILL_FROM_RECENT = "kl.action.kill_from_recent";
    public static final String WIDGET_ACTION_PLAY = "kl.action.play";
    public static final String WIDGET_ACTION_REFRESH = "kl.action.refresh";
    public static final String WIDGET_ACTION_EXIT = "kl.action.exit";
    public static final String WIDGET_ACTION_COLLECTION = "kl.action.collection";
    public static final int WIDET_POLICY_NORAML = 0x000100;
    public static final int WIDET_POLICY_DOPHIN = 0x000102;
    String CHANNEL_NAME = "channelName";
    private NotificationManager mNotificationManager;

    public String CHANNEL_ID = "channelId";
    public static int widgetPolicy = WIDET_POLICY_DOPHIN;

    private Context context = AppDelegate.getInstance().getContext();

    private static final String TAG = "YDophoinWidgetService";
    //专辑图片
    private Bitmap mCurrentBitmap;

//    private Bitmap mBlurBitmap;
//
//    private Bitmap mBlurBitmapByLand;
//
//    private Bitmap mBlurBitmapByPort;

    private boolean subscription = false;

    private String livingTime = "";

    private int landW;
    private int landH;
    private int portW;
    private int portH;

    private SubscribeModel subscribeModel;

    private boolean isPortrait = true;

    //    private BydThemeBroadcastReceiver themeBroadcastReceiver;
    private KillBroadcastReceiver killBroadcastReceiver;

    @Override
    public void onCreate() {
        super.onCreate();
        startForeground();
//        registerBydThemeReceiver();
        registerKillRecevier();
        boolean init = PlayerManager.getInstance().isPlayerInitSuccess();
        Log.i(TAG, "onCreate init:" + init);
        if (!init) {
            PlayerManager.getInstance().addPlayerInitComplete(new IPlayerInitCompleteListener() {
                @Override
                public void onPlayerInitComplete(boolean b) {
                    if (b) {
                        PlayerManager.getInstance().removePlayerInitComplete(this);
                        init();
                    }
                }
            });
        } else {
            init();
        }
    }

    private void registerKillRecevier() {
        killBroadcastReceiver = new KillBroadcastReceiver();
        IntentFilter filter = new IntentFilter();
        filter.addAction("byd.intent.action.KILL_YUNTING");
        filter.setPriority(1000);
        registerReceiver(killBroadcastReceiver, filter);
    }

    private void registerBydThemeReceiver() {
//        themeBroadcastReceiver = new BydThemeBroadcastReceiver();
//        IntentFilter filter = new IntentFilter();
//        filter.addAction("com.byd.changebydtheme");
//        filter.setPriority(1000);
//        registerReceiver(themeBroadcastReceiver, filter);
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
//        if (intent == null) {
//            return START_NOT_STICKY;
//        }
        Log.i(TAG, "onStartCommand: ");
        Log.i(TAG, "onHandleIntent: intent=" + intent);
//        updateWidgetPolicy(intent);
        executeActionCommand(intent.getAction());
        return START_NOT_STICKY;
    }

    private void updateWidgetPolicy(Intent intent) {
        widgetPolicy = intent.getIntExtra("widget_policy", WIDET_POLICY_NORAML);
        Log.i(TAG, "widget_policy:" + widgetPolicy);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mCurrentBitmap = null;
//        unregisterReceiver(themeBroadcastReceiver);
        unregisterReceiver(killBroadcastReceiver);
        PlayerManager.getInstance().removePlayControlStateCallback(playStateListener);
        ((SubscribeRepository) SubscribeManager.getInstance(context, CP.KaoLaFM)).removeSubscribeChangeListener(subscribeChangeListener);
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
//        if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
//            mBlurBitmap = mBlurBitmapByLand;
//            isPortrait = false;
//        } else {
//            mBlurBitmap = mBlurBitmapByPort;
//            isPortrait = true;
//        }
        updateAppResource();
//        setRemoteViews(PlayerManager.getInstance().getCurPlayItem(), -1);
        super.onConfigurationChanged(newConfig);
    }

    private void init() {
        subscribeModel = new SubscribeModel();
        PlayerManager.getInstance().addPlayControlStateCallback(playStateListener);
        ((SubscribeRepository) SubscribeManager.getInstance(context, CP.KaoLaFM)).addSubscribeChangeListener(subscribeChangeListener);
        initWH();
        initPlayInfo();
        if (ResUtil.getOrientation() == Configuration.ORIENTATION_LANDSCAPE) {
            isPortrait = false;
        }
    }


    private void initWH() {
        landW = ScreenUtil.dp2px(239);
        landH = ScreenUtil.dp2px(268 - 50);
        portW = ScreenUtil.dp2px(390);
        portH = ScreenUtil.dp2px(177 - 37);
    }

    private void initPlayInfo() {
        PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
        if (playItem == null) {
            HistoryDaoManager.getInstance().queryHistoryListOrderByTime(1, historyItems -> {
                if (!ListUtil.isEmpty(historyItems)) {
                    HistoryItem historyItem = historyItems.get(0);
                    if (historyItem != null) {
                        // TODO:ZC 2020-04-27
                        //PlayItem historyPlayItem = HistoryUtils.turnPlayItem(historyItem);
                        //updateBitmap(historyPlayItem);
                    }
                }
            });
        } else if (playItem.getAudioId() > 0) {
            updateBitmap(playItem);
        }
    }


    private void executeActionCommand(String action) {
        if (TextUtils.isEmpty(action)) {
            return;
        }

        Log.i(TAG, "executeActionCommand:" + action);

        IPlayListControl playListControl = PlayerManager.getInstance().getPlayListControl();
        Log.i(TAG, "playListControl:" + playListControl);

        switch (action) {
            case WIDGET_ACTION_NEXT:
                if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
                    BydToast.show(context, "无网络", Toast.LENGTH_SHORT);
                    return;
                }
                if (playListControl == null) {
                    go2Launcher();
                    return;
                }
                Log.i(TAG, "WIDGET_ACTION_NEXT...");
                if (PlayerManager.getInstance().getCurPlayItem() == null) {
                    PlayerUtil.playNetOrLocal();
                } else {
                    WidgetUtils.tryPlayNext(this);
                }
                ReportUtil.addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_NEXT, PlayerUiControlReportEvent.CONTROL_TYPE_CLICK, PlayerUiControlReportEvent.POSITION_WIDGET);
                break;
            case WIDGET_ACTION_PREV:
                if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
                    BydToast.show(context, "无网络", Toast.LENGTH_SHORT);
                    return;
                }
                if (playListControl == null) {
                    go2Launcher();
                    return;
                }

                Log.i(TAG, "WIDGET_ACTION_PREV...");

                if (PlayerManager.getInstance().getCurPlayItem() == null) {
                    PlayerUtil.playNetOrLocal();
                } else {
                    PlayerManager.getInstance().playPre();
                }
                ReportUtil.addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_PREVIOUS, PlayerUiControlReportEvent.CONTROL_TYPE_CLICK, PlayerUiControlReportEvent.POSITION_WIDGET);

                break;
            case WIDGET_ACTION_PLAY:
                if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
                    BydToast.show(context, "无网络", Toast.LENGTH_SHORT);
                    return;
                }
                if (playListControl == null) {
                    go2Launcher();
                    return;
                }
                Log.i(TAG, "WIDGET_ACTION_PLAY...");
                if (PlayerManager.getInstance().getCurPlayItem() == null) {
                    PlayerUtil.playNetOrLocal();
                } else {
                    PlayerManagerHelper.getInstance().switchPlayerStatus(true);
                }
                ReportUtil.addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_PLAY, PlayerUiControlReportEvent.CONTROL_TYPE_CLICK, PlayerUiControlReportEvent.POSITION_WIDGET);
                break;
            case WIDGET_ACTION_PAUSE:
//                if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
//                    BydToast.show(context, "无网络", Toast.LENGTH_SHORT);
//                    return;
//                }
                Log.i(TAG, "WIDGET_ACTION_PAUSE...");
                PlayerManager.getInstance().pause(true);
                ReportUtil.addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_PAUSE, PlayerUiControlReportEvent.TYPE_SELECT_PAUSE, PlayerUiControlReportEvent.POSITION_WIDGET);
                break;
            case WIDGET_ACTION_KILL_FROM_RECENT:
                Log.i(TAG, "WIDGET_ACTION_KILL_FROM_RECENT...");
                PlayerManager.getInstance().pause(true);
                ReportUtil.addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_PAUSE, PlayerUiControlReportEvent.TYPE_SELECT_PAUSE, PlayerUiControlReportEvent.POSITION_WIDGET);
                //从最近任务KILL掉后，展示空的播放条目即可
                setRemoteViews(new AlbumPlayItem(), -1);
                break;
            case WIDGET_ACTION_COLLECTION:
                if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
                    BydToast.show(context, "无网络", Toast.LENGTH_SHORT);
                    return;
                }
                widgetSubscribe(PlayerHelper.getSubscribeId());
                break;
            case WIDGET_ACTION_REFRESH:
                Log.i(TAG, "WIDGET_ACTION_REFRESH...");
                setRemoteViews(PlayerManager.getInstance().getCurPlayItem(), -1);
                break;
            case WIDGET_ACTION_EXIT:
                Log.i(TAG, "WIDGET_ACTION_EXIT...");
                setRemoteViews(PlayerManager.getInstance().getCurPlayItem(), IjkMediaPlayer.MEDIA_PAUSED);
                break;
            default:
                break;
        }
    }

    private void go2Launcher() {
        //fixed 修复应用未启动添加widget点击按钮没有响应的问题，直接进入应用
        Intent launchAppIntent = IntentUtils.getInstance().getLauncherIntentUseWidget(context);
        context.startActivity(launchAppIntent);
    }

    private void updateBitmap(PlayItem playItem) {
        String pic = playItem.getPicUrl();
        Log.i(TAG, "updateBitmap:" + pic);
        if (TextUtils.isEmpty(pic)) {
            setRemoteViews(playItem, -1);
        } else {
            ImageConfigImpl.Builder config = ImageConfigImpl.builder()
                    .url(pic)
                    .imgType(ImageConfigImpl.TYPE_BITMAP)
                    .onGetBitmapListener(bitmap -> {
                        PlayItem curPlayItem = PlayerManager.getInstance().getCurPlayItem();
                        Log.i(TAG, "getBitmapFromCache:" + bitmap);
                        if (curPlayItem != null && curPlayItem.getAudioId() == playItem.getAudioId()) {
                            mCurrentBitmap = bitmap;
                            setRemoteViews(playItem, -1);
                        } else if (curPlayItem == null) {
                            mCurrentBitmap = bitmap;
                            setRemoteViews(playItem, -1);
                        }
//                if (bitmap == null) {
//                    mCurrentBitmap = getDefaultIconBitmap();
//                    Log.i(TAG, "updateBitmap_Default:" + mCurrentBitmap);
//                    setRemoteViews(playItem);
//                } else {
//                    if (curPlayItem != null && curPlayItem.getAudioId() == playItem.getAudioId()) {
//                        mCurrentBitmap = bitmap;
//                        setRemoteViews(playItem);
//                    } else if (curPlayItem == null) {
//                        mCurrentBitmap = bitmap;
//                        setRemoteViews(playItem);
//                    }
//                }
                    })
                    .isCircle(true)
                    .size(ProgressBitmapGenerator.dpToPx(context, 116));
            ImageLoader.getInstance().displayImage(context, config);
            if (mCurrentBitmap != null) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                    Log.d(TAG, "bitmap size " + mCurrentBitmap.getByteCount() + "--" + mCurrentBitmap.getAllocationByteCount());
                } else {
                    Log.d(TAG, "bitmap size " + mCurrentBitmap.getByteCount());
                }
            }
        }

//        updateblurBitmap(playItem);
    }

//    private void updateblurBitmap(PlayItem playItem) {
//        String pic = playItem.getPicUrl();
//        ImageLoader.getInstance().getBlurBitmapFromCache(context, pic, 20, bitmap -> {
//            Disposable subscribe = Observable.create((ObservableOnSubscribe<Boolean>) emitter -> {
//
//                Bitmap portB = BitmapUtils.createScaledBitmapRGB565(bitmap, portW, portH, BitmapUtils.ScalingLogic.CROP);
//                Bitmap landB = BitmapUtils.createScaledBitmapRGB565(bitmap, landW, landH, BitmapUtils.ScalingLogic.CROP);
//
//                mBlurBitmapByPort = BitmapUtils.setBitmapRoundSize(portB, portW, portH, ScreenUtil.dp2px(10));
//                mBlurBitmapByLand = BitmapUtils.setBitmapRoundSize(landB, landW, landH, ScreenUtil.dp2px(10));
//
//                if (ResUtil.getOrientation() == Configuration.ORIENTATION_LANDSCAPE) {
//                    mBlurBitmap = mBlurBitmapByLand;
//                } else {
//                    mBlurBitmap = mBlurBitmapByPort;
//                }
//
//                emitter.onNext(true);
//            }).subscribeOn(Schedulers.io())
//                    .observeOn(AndroidSchedulers.mainThread())
//                    .subscribe(b -> setRemoteViews(playItem));
//        });
//    }

    public void setRemoteViews(PlayItem playItem, int state) {
        Log.i(TAG, "If you see this line,means the new version is running!");
        Log.i(TAG, "setRemoteViews...");
        final AppWidgetManager manager = AppWidgetManager.getInstance(context);
//        final ComponentName componentName = new ComponentName(context, KLAppWidget.class);
//        final ComponentName componentName = new ComponentName(context, KLAppWidget.class);
//        RemoteViews views = new RemoteViews(context.getPackageName(), R.layout.widget_layout_sport);
//        RemoteViews views = new RemoteViews(context.getPackageName(), R.layout.byd_35widget_layout);
        Class widgetClass = null;
        int layoutId = 0;
        switch (widgetPolicy) {
            case WIDET_POLICY_NORAML:
            default:
                widgetClass = YunBydWidget.class;
                layoutId = R.layout.byd_35widget_layout;
                break;
            case WIDET_POLICY_DOPHIN:
                Log.i(TAG, "setRemoteViews for dophin:" + WIDET_POLICY_NORAML);
                widgetClass = YunBydDophinWidget.class;
                layoutId = R.layout.byd_dolphin_35widget_layout;

                Log.i(TAG, "WidgetClass:" + widgetClass + " ~~~  widgetPolicy:" + widgetPolicy);
                final ComponentName componentName = new ComponentName(context, widgetClass);
                RemoteViews views = new RemoteViews(context.getPackageName(), layoutId);
                updateThemeResources(views);
                //设置播放暂停按钮
                if (PlayerManager.getInstance().isPlaying() && state != IjkMediaPlayer.MEDIA_PAUSED) {//playermanager的状态可能不对
                    Log.i(TAG, "showPlayBtn");
                    showPlayBtn(views);
                } else {
                    Log.i(TAG, "showPauseBtn");
                    showPauseBtn(views);
                }
                //设置点击封面进入应用
                Intent launchAppIntent = IntentUtils.getInstance().getLauncherIntentUseWidget(context);
                views.setOnClickPendingIntent(R.id.widget_playinfo_layout, PendingIntent.getActivity(context, 0, launchAppIntent, 0));
                //设置上一首
                Intent intent = new Intent(context, YunTingWidgetService.class);
                intent.setAction(WIDGET_ACTION_PREV);
                dealIntentWithPolicy(intent);
                views.setOnClickPendingIntent(R.id.widget_prev, PendingIntent.getService(context, 0, intent, 0));
                //设置下一首
                Intent intent1 = new Intent(context, YunTingWidgetService.class);
                intent1.setAction(WIDGET_ACTION_NEXT);
                dealIntentWithPolicy(intent1);
                views.setOnClickPendingIntent(R.id.widget_next, PendingIntent.getService(context, 0, intent1, 0));
                //设置收藏
                Intent collection = new Intent(context, YunTingWidgetService.class);
                collection.setAction(WIDGET_ACTION_COLLECTION);
                views.setOnClickPendingIntent(R.id.widget_collection, PendingIntent.getService(context, 0, collection, 0));

                if (playItem != null) {
                    Log.i(TAG, "setRemoteViews:" + playItem.getAlbumTitle());
                    updateByPlayItem(playItem, views);
                    manager.updateAppWidget(componentName, views);
                } else {
                    HistoryDaoManager.getInstance().queryHistoryListOrderByTime(1, historyItems -> {
                        if (!ListUtil.isEmpty(historyItems)) {
                            HistoryItem historyItem = historyItems.get(0);
                            Log.i(TAG, "historyItem:" + historyItem);
                            // TODO:ZC 2020-04-27
//                    PlayItem historyPlayItem = HistoryUtils.turnPlayItem(historyItem);
//                    updateByPlayItem(historyPlayItem, views);
                        }
                        manager.updateAppWidget(componentName, views);
                    });
                }
                break;
        }
    }

    private void dealWithNetwork() {
        if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
            BydToast.show(context, "无网络", Toast.LENGTH_SHORT);
        }
    }

    /**
     * 黑夜白天模式切换时需要重新设置所有资源，来更新对应的UI
     *
     * @param views
     */
    private void updateThemeResources(RemoteViews views) {
//        views.setImageViewResource(R.id.widget_bg, R.drawable.byd_35widget_bg);
        views.setImageViewResource(R.id.widget_bg, R.drawable.byd_dolphin_35widget_bg);
//        views.setImageViewResource(R.id.widget_bg_shadow, R.drawable.byd_35widget_bg_shadow);
        views.setImageViewResource(R.id.widget_cover, R.drawable.byd_dolphin_35widget_thumb_default);
//        views.setImageViewResource(R.id.widget_prev, R.drawable.byd_35widget_pre);
        views.setImageViewResource(R.id.widget_prev, R.drawable.byd_dolphin_35widget_pre);
//        views.setImageViewResource(R.id.widget_next, R.drawable.byd_35widget_next);
        views.setImageViewResource(R.id.widget_next, R.drawable.byd_dolphin_35widget_next);
//        views.setImageViewResource(R.id.widget_play_or_pause, R.drawable.byd_35widget_play);
        views.setTextColor(R.id.widget_title, context.getResources().getColor(R.color.byd_35widget_title_color));
        views.setTextColor(R.id.widget_audio_name, context.getResources().getColor(R.color.byd_35widget_content_color));
        views.setTextColor(R.id.widget_album_name, context.getResources().getColor(R.color.byd_35widget_content_color));

        // Step2. 获取icon
        Drawable appIcon = BimapPlusUtils.getAppIcon(context, "cmgyunting.vehicleplayer.cnr");
        views.setImageViewBitmap(R.id.widget_icon, BimapPlusUtils.drawableToBitmapWithSize(appIcon, 42, 42));
    }

    private void updateAppResource() {
        try {
            // Step1. 更新资源
            Class<?> classes = Application.class;
            Method method = classes.getMethod("updateResources");
            method.invoke(AppDelegate.getInstance().getContext());
            Log.i(TAG, "updateAppIcon success!");
        } catch (Exception e) {
            e.printStackTrace();
            Log.i(TAG, "updateAppIcon error:" + e.toString());
        }
    }


    /**
     * 根据不同的policy设置不同的inient数据
     *
     * @param intent
     */
    private void dealIntentWithPolicy(Intent intent) {
        //todo fixme 两个widget耦合严重，且无法同时更新wiget
        switch (widgetPolicy) {
            case WIDET_POLICY_DOPHIN:
                intent.putExtra("widget_policy", YunTingWidgetService.WIDET_POLICY_DOPHIN);
                break;
            case WIDET_POLICY_NORAML:
            default:
                intent.putExtra("widget_policy", YunTingWidgetService.WIDET_POLICY_NORAML);
                break;
        }
    }


    private void updateByPlayItem(PlayItem playItem, RemoteViews views) {
        //todo playItem==null?
        if (playItem != null) {
            Log.i(TAG, "updateByPlayItem：" + playItem.getTitle());
            //设置封面图片
            if (!TextUtils.isEmpty(playItem.getPicUrl())) {
                views.setImageViewBitmap(R.id.widget_cover, mCurrentBitmap);
            } else if (playItem.getAudioId() > 0) {//默认的空数据不处理
                views.setImageViewResource(R.id.widget_cover, R.drawable.byd_dolphin_35widget_thumb_default);
            }
            //设置高斯模糊图片
//            views.setImageViewBitmap(R.id.widget_blur_imageview, mBlurBitmap);

            views.setViewVisibility(R.id.widget_blur_bg, View.VISIBLE);

            //设置碎片名称
            CharSequence widgetText = playItem.getTitle();
            if (!TextUtils.isEmpty(widgetText)) {
                views.setTextViewText(R.id.widget_audio_name, widgetText);
            } else if (playItem.getAudioId() > 0) {//默认的空数据不处理
                views.setTextViewText(R.id.widget_audio_name, "暂无节目信息");
            }
            //设置专辑名称
            CharSequence albumName = playItem.getAlbumTitle();
            if (!TextUtils.isEmpty(albumName)) {
                views.setTextViewText(R.id.widget_album_name, albumName);
            }

            if (playItem.isLiving()) {
                if (playItem.getFinishTime() <= 0) {
                    views.setViewVisibility(R.id.widget_duration, View.INVISIBLE);
                    views.setViewVisibility(R.id.widget_cur_time, View.INVISIBLE);
                    views.setProgressBar(R.id.widget_progressBar, 0, 0, false);
                } else {
                    views.setViewVisibility(R.id.widget_duration, View.VISIBLE);
                    views.setViewVisibility(R.id.widget_cur_time, View.VISIBLE);
                    views.setTextViewText(R.id.widget_duration, DateFormatUtil.getCurrDate(playItem.getFinishTime()));
                    views.setProgressBar(R.id.widget_progressBar, 0, 0, false);
                    views.setTextViewText(R.id.widget_cur_time, livingTime + " / ");
                }
            } else {
                views.setViewVisibility(R.id.widget_duration, View.VISIBLE);
                views.setViewVisibility(R.id.widget_cur_time, View.VISIBLE);
                int duration = playItem.getDuration();
                Log.i(TAG, "updateByPlayItem----------------->position = " + playItem.getPosition() + "---->duration = " + duration);
                views.setProgressBar(R.id.widget_progressBar, duration, playItem.getPosition(), false);
                views.setTextViewText(R.id.widget_duration, DateFormatUtil.getDescriptiveTime(duration));
                views.setTextViewText(R.id.widget_cur_time, DateFormatUtil.getDescriptiveTime(playItem.getPosition()) + " / ");
            }
        } else {
            views.setViewVisibility(R.id.widget_blur_bg, View.GONE);
        }
        //设置收藏状态
        if (subscription) {
            views.setImageViewResource(R.id.widget_collection, R.drawable.selector_widget_btn_collection);
        } else {
            views.setImageViewResource(R.id.widget_collection, R.drawable.selector_widget_btn_uncollection);
        }

        //设置直播中小标
        updateBroadcastLabel(views);
        updateProgress(playItem, views);
    }

    private void updateProgress(PlayItem playItem, RemoteViews views) {
        Bitmap bitmap;
        int layoutId;
        switch (widgetPolicy) {
            case WIDET_POLICY_DOPHIN:
                bitmap = ProgressBitmapGenerator.generateDolphinMusicWidgetProgress(context, playItem == null ? 0 : playItem.getDuration(), playItem == null ? 100 : playItem.getPosition(), R.drawable.byd_dolphin_35widget_bg
                        , context.getResources().getColor(R.color.byd_dolphin_35widget_progress_start_color),
                        context.getResources().getColor(R.color.byd_dolphin_35widget_progress_end_color),
                        context.getResources().getDimensionPixelSize(R.dimen.byd_dolphin_35widget_progress_canvas_width),
                        context.getResources().getDimensionPixelSize(R.dimen.byd_dolphin_35widget_progress_canvas_height),
                        context.getResources().getDimensionPixelSize(R.dimen.byd_dolphin_35widget_progress_ring_outSize),
                        context.getResources().getDimensionPixelSize(R.dimen.byd_dolphin_35widget_progress_ring_strokeWidth));
                layoutId = R.id.widget_bg;
                break;
            case WIDET_POLICY_NORAML:
            default:
                bitmap = ProgressBitmapGenerator.generateMusicWidgetProgress(context,
                        playItem == null ? 0 : playItem.getDuration(),
                        playItem == null ? 100 : playItem.getPosition());
                layoutId = R.id.widget_progress;
                break;
        }
        Log.i(TAG, "updateProgress || bitmap:" + bitmap + " --layoutId:" + layoutId);
        views.setImageViewBitmap(layoutId, bitmap);
    }

    private void updateBroadcastLabel(RemoteViews remoteViews) {
        if (remoteViews == null) {
            return;
        }
        PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
        if (playItem == null) {
            return;
        }

        if (playItem.getType() == PlayerConstants.RESOURCES_TYPE_BROADCAST) {
            remoteViews.setViewVisibility(R.id.widget_broadcast_label, View.VISIBLE);
            remoteViews.setViewVisibility(R.id.widget_broadcast_label_textview, View.VISIBLE);
            switch (playItem.getStatus()) {
                //直播
                case BroadcastStatus.BROADCAST_STATUS_LIVING:
                    remoteViews.setImageViewResource(R.id.widget_broadcast_label, R.drawable.icon_player_living);
                    remoteViews.setTextViewText(R.id.widget_broadcast_label_textview, "直播中");
                    break;
                //回放
                case BroadcastStatus.BROADCAST_STATUS_PLAYBACK:
                    remoteViews.setImageViewResource(R.id.widget_broadcast_label, R.drawable.icon_player_playback);
                    remoteViews.setTextViewText(R.id.widget_broadcast_label_textview, "回听");
                    break;
            }
        } else {
            remoteViews.setViewVisibility(R.id.widget_broadcast_label, View.GONE);
            remoteViews.setViewVisibility(R.id.widget_broadcast_label_textview, View.GONE);
        }
    }

    private void showPlayBtn(RemoteViews views) {
//        views.setImageViewResource(R.id.widget_play_or_pause, R.drawable.byd_35widget_play);
        views.setImageViewResource(R.id.widget_play_or_pause, R.drawable.byd_dolphin_35widget_play);
        Intent intent = new Intent(context, YunTingWidgetService.class);
        intent.setAction(WIDGET_ACTION_PAUSE);
        dealIntentWithPolicy(intent);
        views.setOnClickPendingIntent(R.id.widget_play_or_pause, PendingIntent.getService(context, 0, intent, 0));
    }

    private void showPauseBtn(RemoteViews views) {
//        views.setImageViewResource(R.id.widget_play_or_pause, R.drawable.byd_35widget_stop);
        views.setImageViewResource(R.id.widget_play_or_pause, R.drawable.byd_dolphin_35widget_stop);
        Intent intent = new Intent(context, YunTingWidgetService.class);
        intent.setAction(WIDGET_ACTION_PLAY);
        dealIntentWithPolicy(intent);
        views.setOnClickPendingIntent(R.id.widget_play_or_pause, PendingIntent.getService(context, 0, intent, 0));
    }

    public void widgetSubscribe(final long id) {
        if (subscribeModel == null) {
            return;
        }
        subscribeModel.isSubscribed(String.valueOf(id), new ResultCallback() {
            @Override
            public void onResult(boolean result, int code) {
                if (result) {
                    unSubscribe(id);
                } else {
                    subscribe(id);
                }
            }

            @Override
            public void onFailure(ErrorInfo errorInfo) {

            }
        });
    }

    private void subscribe(long id) {
        if (subscribeModel == null) {
            return;
        }
        SubscribeData subscribeData = new SubscribeData();
        subscribeData.setId(id);
        subscribeModel.subscribe(subscribeData, new ResultCallback() {
            @Override
            public void onResult(boolean result, int code) {
                subscription = result;
                setRemoteViews(PlayerManager.getInstance().getCurPlayItem(), -1);
            }

            @Override
            public void onFailure(ErrorInfo errorInfo) {

            }
        });
    }

    private void unSubscribe(long id) {
        if (subscribeModel == null) {
            return;
        }
        SubscribeData sd = new SubscribeData();
        sd.setId(id);
        subscribeModel.unsubscribe(sd, new ResultCallback() {
            @Override
            public void onResult(boolean result, int code) {
                if (result) {
                    subscription = false;
                }
                setRemoteViews(PlayerManager.getInstance().getCurPlayItem(), -1);
            }

            @Override
            public void onFailure(ErrorInfo errorInfo) {

            }
        });
    }

    private void setSubscribeState() {
        if (subscribeModel == null) {
            return;
        }
        subscribeModel.isSubscribed(String.valueOf(PlayerHelper.getSubscribeId()), new ResultCallback() {
            @Override
            public void onResult(boolean result, int code) {
                subscription = result;
                setRemoteViews(PlayerManager.getInstance().getCurPlayItem(), -1);
            }

            @Override
            public void onFailure(ErrorInfo errorInfo) {

            }
        });
    }

    SubscribeChangeListener subscribeChangeListener = subscribes -> {
        subscription = false;
        if (!ListUtil.isEmpty(subscribes)) {
            for (SubscribeData s : subscribes) {
                if (s.getId() == PlayerHelper.getSubscribeId()) {
                    subscription = true;
                    break;
                }
            }
        }
        setRemoteViews(PlayerManager.getInstance().getCurPlayItem(), -1);
    };

    BasePlayStateListener playStateListener = new BasePlayStateListener() {
        @Override
        public void onPlayerPreparing(final PlayItem playItem) {
            Log.i(TAG, "onPlayerPreparing");
            if (playItem == null) {
                Log.i(TAG, "playitem null");
                return;
            }
            setRemoteViews(playItem, -1);
            setSubscribeState();
            updateBitmap(playItem);
        }

        @Override
        public void onPlayerPlaying(final PlayItem playItem) {
            Log.i(TAG, "onPlayerPlaying");
            setRemoteViews(playItem, -1);
        }

        @Override
        public void onPlayerPaused(PlayItem playItem) {
            Log.i(TAG, "onPlayerPaused");
            setRemoteViews(playItem, IjkMediaPlayer.MEDIA_PAUSED);
        }

        @Override
        public void onPlayerEnd(PlayItem playItem) {
            super.onPlayerEnd(playItem);
            Log.i(TAG, "onPlayerEnd");
            setRemoteViews(playItem, IjkMediaPlayer.MEDIA_PAUSED);
        }

        @Override
        public void onProgress(PlayItem playItem, long progress, long l) {
            Log.i(TAG, "onProgress isRefresh:");
            Log.i(TAG, "playItem playback position:" + playItem.getTitle() + " " + progress + "/" + l);
            Log.i(TAG, "playItem position:" + playItem.getPosition() + "/" + +playItem.getDuration());
            if (playItem.isLiving()) {
                playItem.setPosition((int) progress);
                playItem.setDuration((int) l);
            }
//            if (!playItem.isLiving()) {
            setRemoteViews(playItem, -1);
//            }
        }
    };


//    private Notification getNotification() {
//        Notification.Builder builder = new Notification.Builder(this)
//                .setSmallIcon(Icon.createWithBitmap(BimapPlusUtils.drawableToBitmapWithSize(BimapPlusUtils.getAppIcon(this, "cn.kuwo.kwmusiccar"),80,80)))
//                .setContentTitle("正在运行");
//        builder.setChannelId(CHANNEL_ID);
//        Notification notification = builder.build();
//        return notification;
//    }


    private void startForeground() {
        String CHANNEL_ONE_ID = "cmgyunting.vehicleplayer.cnr";
        String CHANNEL_ONE_NAME = "Widget One";
        NotificationChannel notificationChannel = null;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            notificationChannel = new NotificationChannel(CHANNEL_ONE_ID,
                    CHANNEL_ONE_NAME, NotificationManager.IMPORTANCE_DEFAULT);
            NotificationManager manager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
            assert manager != null;
            manager.createNotificationChannel(notificationChannel);
            startForeground(1, new NotificationCompat.Builder(this, CHANNEL_ONE_ID).build());
        }
    }


    public static Bitmap drawableToBitmapWithSize(Drawable drawable, int newWidth, int newHeight) {
        Bitmap bitmap = Bitmap.createBitmap(newWidth, newHeight,
                drawable.getOpacity() != PixelFormat.OPAQUE ? Bitmap.Config.ARGB_8888 : Bitmap.Config.RGB_565);
        Canvas canvas = new Canvas(bitmap);
        drawable.setBounds(0, 0, newWidth, newHeight);
        drawable.draw(canvas);
        return bitmap;
    }

    public static Drawable getAppIcon(Context context, String packageName) {
        Drawable icon = context.getDrawable(R.drawable.byd_35_app_icon);
        try {
            icon = context.getPackageManager().getApplicationIcon(packageName);
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        return icon;
    }

    @RequiresApi(Build.VERSION_CODES.O)
    private String createNotificationChannel(String channelId, String channelName) {
        NotificationChannel chan = new NotificationChannel(channelId, channelName, NotificationManager.IMPORTANCE_NONE);
        chan.setLightColor(Color.BLUE);
        chan.setLockscreenVisibility(Notification.VISIBILITY_PRIVATE);
        NotificationManager service = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
        service.createNotificationChannel(chan);
        return channelId;
    }

    public static Bitmap circleBitmap(Bitmap source) {
        //获取Bitmap的宽度
        int width = source.getWidth();
        //以Bitmap的宽度值作为新的bitmap的宽高值。
        Bitmap bitmap = Bitmap.createBitmap(width, width, Bitmap.Config.ARGB_8888);
        //以此bitmap为基准，创建一个画布
        Canvas canvas = new Canvas(bitmap);
        Paint paint = new Paint();
        paint.setAntiAlias(true);
        //在画布上画一个圆
        canvas.drawCircle(width / 2, width / 2, width / 2, paint);

        //设置图片相交情况下的处理方式
        paint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.SRC_IN));
        //在画布上绘制bitmap
        canvas.drawBitmap(source, 0, 0, paint);

        return bitmap;

    }


}
