package com.kaolafm.kradio.categories.login;

import android.graphics.Color;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebSettings;
import android.webkit.WebSettings.LayoutAlgorithm;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.kradio.categories.AllCategoriesFragment;
import com.kaolafm.kradio.categories.CategoryConstant;
import com.kaolafm.kradio.categories.ClausesFragment;
import com.kaolafm.kradio.categories.bean.QQMusicLoginEvent;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.NetworkMonitor;
import com.kaolafm.kradio.lib.utils.NetworkMonitor.OnNetworkStatusChangedListener;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;

import org.greenrobot.eventbus.EventBus;

/**
 * <AUTHOR>
 * @date 2018/5/1
 */

public class QRCodeFragment extends BaseFragment<LoginPresenter> implements ILoginView {

    ImageView mIvLoginTitleBack;

    ImageView mIvQrCodeImage;
    TextView mTvLoginTitleCenter;
    TextView mTvQrCodeName;
    TextView mTvQrCodeTeamsOfService;
    WebView mWvQrCodeShow;

    ViewGroup mWebviewContainer;

    /**
     * 页面是否不可见，true不可见
     */
    private boolean isHidden;

    /**
     * 是否已经登录成功 true登录成功
     */
    private boolean isLoginSuccess = false;


    private int mLoginType = CategoryConstant.LOGIN_TYPE_WECHAT;

    private OnNetworkStatusChangedListener mNetworkStatusChangedListener;



    @Override
    public void initArgs() {
        Bundle arg = getArguments();
        if (arg != null) {
            mLoginType = arg.getInt(CategoryConstant.LOGIN_TYPE);
        }
    }

    @Override
    public void initView(View view) {

        mIvLoginTitleBack=view.findViewById(R.id.iv_title_back);
        mIvQrCodeImage=view.findViewById(R.id.iv_qr_code_image);
        mTvLoginTitleCenter=view.findViewById(R.id.tv_title_center);
        mTvQrCodeName=view.findViewById(R.id.tv_qr_code_name);
        mTvQrCodeTeamsOfService=view.findViewById(R.id.tv_qr_code_teams_of_service);
        mWvQrCodeShow=view.findViewById(R.id.wv_qr_code_show);
        mIvLoginTitleBack.setOnClickListener(v -> {
            if (!AntiShake.check(view.getId())) {
                pop();
                postEvent(CategoryConstant.LOGIN_RESULT_CANCEL_CODE, getString(R.string.cancel_login));
            }
        });


        mWebviewContainer = (ViewGroup) view;
        if (mLoginType == CategoryConstant.LOGIN_TYPE_WECHAT) {
            mTvLoginTitleCenter.setText(R.string.music_wechat_login);
            mTvQrCodeName.setText(R.string.login_with_wechat_scan);
        } else {
            mTvLoginTitleCenter.setText(R.string.music_qq_login);
            mTvQrCodeName.setText(R.string.login_with_qq_scan);
        }
        SpannableString spannableString = new SpannableString(
                ResUtil.getString(R.string.read_and_agree_to_the_terms_of_service_single_line));
        ForegroundColorSpan colorSpan = new ForegroundColorSpan(ResUtil.getColor(R.color.colorMain));
        spannableString.setSpan(colorSpan, spannableString.length() - 4, spannableString.length(),
                Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        ClickableSpan clickableSpan = new ClickableSpan() {
            @Override
            public void onClick(View widget) {
                if (!AntiShake.check(widget.getId())) {
//                    AllCategoriesFragment parentFragment = (AllCategoriesFragment) QRCodeFragment.this.getParentFragment();
                    AllCategoriesFragment parentFragment = (AllCategoriesFragment) RouterManager.getInstance().getRouterFragment(RouterConstance.CATEGORIES_COMPREHENSIVE_URL);
                    if (parentFragment != null) {
                        parentFragment.extraTransaction().start(new ClausesFragment());
                    }
                }
            }

            @Override
            public void updateDrawState(TextPaint ds) {
                ds.setColor(ds.linkColor);
                //不显示下划线
                ds.setUnderlineText(false);
            }
        };
        spannableString.setSpan(clickableSpan, spannableString.length() - 4, spannableString.length(),
                Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        mTvQrCodeTeamsOfService.setHighlightColor(Color.TRANSPARENT);
        mTvQrCodeTeamsOfService.setMovementMethod(LinkMovementMethod.getInstance());
        mTvQrCodeTeamsOfService.setText(spannableString);

        WebSettings settings = mWvQrCodeShow.getSettings();
        settings.setSavePassword(false);
        settings.setDisplayZoomControls(false);
        settings.setSupportZoom(false);
        settings.setDomStorageEnabled(true);
        settings.setCacheMode(WebSettings.LOAD_NO_CACHE);
        settings.setLoadsImagesAutomatically(true);
        settings.setUseWideViewPort(true);
        settings.setLoadWithOverviewMode(true);
        settings.setLayoutAlgorithm(LayoutAlgorithm.SINGLE_COLUMN);
        settings.setJavaScriptEnabled(false);
        mWvQrCodeShow.setVerticalScrollBarEnabled(false);
        mWvQrCodeShow.setHorizontalScrollBarEnabled(false);
        mWvQrCodeShow.setScrollContainer(false);
        mWvQrCodeShow.setOverScrollMode(View.OVER_SCROLL_NEVER);
        mWvQrCodeShow.setFitsSystemWindows(true);
        mWvQrCodeShow.setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                if (mPresenter != null) {
                    mPresenter.login(mLoginType, url);
                }
                return true;
            }
        });
        mNetworkStatusChangedListener = (newStatus, oldStatus) -> {
            Log.i("QRCodeFragment", "onStatusChanged: newStatus=" + newStatus + ", oldStatus=" + oldStatus);
            if (newStatus != NetworkMonitor.STATUS_NO_NETWORK) {
                refreshQRCode();
            }
        };
        NetworkMonitor.getInstance(getContext()).registerNetworkStatusChangeListener(
                mNetworkStatusChangedListener);
    }

    @Override
    public void onStart() {
        super.onStart();
        mPresenter.start();
        isHidden = false;
    }

    @Override
    public void onSupportVisible() {
        super.onSupportVisible();
        refreshQRCode();
    }

    public void refreshQRCode() {
        if (mLoginType == CategoryConstant.LOGIN_TYPE_WECHAT) {
            mPresenter.loadWeChatQRCodeForLogin();
        } else {
            mPresenter.loadQQQRCodeForLogin();
        }
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        isHidden = hidden;
        if (!hidden && isLoginSuccess) {
            postEvent(CategoryConstant.LOGIN_RESULT_SUCCESS_CODE, getString(R.string.login_success));
        }
    }

    @Override
    public void loginSuccess() {
        ToastUtil.showOnly(getContext(), R.string.login_success);
        //页面退出逻辑是当二维码页面被服务条款页面覆盖时，不退出页面；只有当该页面再次显示出来在发信息退出。
        if (!isHidden) {
            postEvent(CategoryConstant.LOGIN_RESULT_SUCCESS_CODE, getString(R.string.login_success));
        } else {
            isLoginSuccess = true;
        }
    }
    /**
     * 发送登录相关event事件
     */
    private void postEvent(int code, String msg) {
        setFragmentResult(code, null);
        EventBus.getDefault().post(QQMusicLoginEvent.loginInstance());
//        popTo(AllCategoriesFragment.class, false);
//        pop();
    }

    @Override
    public void loadWebView(String url) {
        mWvQrCodeShow.loadDataWithBaseURL("http://open.weixin.qq.com", url, "text/html", "utf-8", null);
    }

    @Override
    public void showQRCode(String imgUrl) {
        ImageLoader.getInstance().displayImage(getContext(), imgUrl, mIvQrCodeImage);
    }


    @Override
    protected int getLayoutId() {
        return R.layout.fragment_qr_code;
    }

    @Override
    protected int getLayoutId_Tow() {
        return 0;
    }

    @Override
    protected LoginPresenter createPresenter() {
        return new LoginPresenter(this);
    }

    @Override
    public boolean onBackPressedSupport() {
        EventBus.getDefault().post(QQMusicLoginEvent.cancelInstance());
        return super.onBackPressedSupport();
    }

    @Override
    public void onDestroyView() {
        mWebviewContainer.removeView(mWvQrCodeShow);
        mWvQrCodeShow.removeAllViews();
        mWvQrCodeShow.destroy();
        NetworkMonitor.getInstance(getContext()).removeNetworkStatusChangeListener(mNetworkStatusChangedListener);
        super.onDestroyView();
    }
}
