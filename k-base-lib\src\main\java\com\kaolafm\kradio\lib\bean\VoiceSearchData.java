package com.kaolafm.kradio.lib.bean;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.ArrayList;

/******************************************
 * 类描述： 语音搜索结果对象 类名称：VoiceSearchData
 *
 * @version: 1.0
 * @author: shaoningYang
 * @time: 2016-7-27 21:24
 ******************************************/
public class VoiceSearchData implements Parcelable {
    public VoiceSearchData() {
    }

    private VoiceSearchData(Parcel in) {
        this.id = in.readLong();
        this.type = in.readInt();
        this.listenNum = in.readLong();
        this.img = in.readString();
        this.name = in.readString();
        this.albumName = in.readString();
        in.readTypedList(host, Host.CREATOR);
//        Parcelable[] pars = in.readParcelableArray(Host.class.getClassLoader());
//        host = (ArrayList<Host>) Arrays.asList(Arrays.asList(pars).toArray(new Host[pars.length]));
    }

    /**
     * 搜索资源ID
     */
    private long id;
    /**
     * 资源对应跳转类型
     */
    private int type;
    /**
     * 资源对应收听数
     */
    private long listenNum;
    /**
     * 搜索资源名称
     */
    private String name;
    /**
     * 搜索资源图片
     */
    private String img;
    /**
     * 资源对应主播列表
     */
    private ArrayList<Host> host = new ArrayList<>();
    /**
     * 专辑名称
     */
    private String albumName;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public long getListenNum() {
        return listenNum;
    }

    public void setListenNum(long listenNum) {
        this.listenNum = listenNum;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public ArrayList<Host> getHost() {
        return host;
    }

    public void setHost(ArrayList<Host> host) {
        this.host = host;
    }

    public String getAlbumName() {
        return albumName;
    }

    public void setAlbumName(String albumName) {
        this.albumName = albumName;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(id);
        dest.writeInt(type);
        dest.writeLong(listenNum);
        dest.writeString(img);
        dest.writeString(name);
        dest.writeString(albumName);
        dest.writeTypedList(host);
//        dest.writeParcelableArray((host.toArray(new Host[host.size()])), flags);
    }

    public static final Creator<VoiceSearchData> CREATOR = new Creator<VoiceSearchData>() {
        public VoiceSearchData createFromParcel(Parcel source) {
            return new VoiceSearchData(source);
        }

        public VoiceSearchData[] newArray(int size) {
            return new VoiceSearchData[size];
        }
    };
}
