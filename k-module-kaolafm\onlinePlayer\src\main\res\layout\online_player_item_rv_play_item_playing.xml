<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="@dimen/y92"
    tools:background="@drawable/online_player_sound_selected">

    <ImageView
        android:id="@+id/ivPlaying"
        android:layout_width="@dimen/x18"
        android:layout_height="@dimen/y18"
        android:layout_marginStart="@dimen/m17"
        android:src="@drawable/online_player_animated_sound_dump"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.kaolafm.kradio.online.common.view.OnlineAutoMarqueenTextView
        android:id="@+id/tvItemTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/m48"
        android:layout_marginTop="@dimen/y14"
        android:ellipsize="end"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:marqueeRepeatLimit="marquee_forever"
        android:singleLine="true"
        android:textColor="@color/online_player_broadcast_title"
        android:textSize="@dimen/m24"
        android:textStyle="bold"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@id/subtitle"
        app:layout_constraintEnd_toStartOf="@id/tvItemStatus"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginBottom="@dimen/y14"
        tools:text="乐海星哥乐海星哥乐海星哥乐海星哥乐海星哥乐海星哥乐海星哥乐海星哥乐海星哥乐海星哥乐海星哥乐海星哥乐海星哥乐海星哥乐海星哥乐海星哥" />

    <TextView
        android:id="@+id/tvItemStatus"
        android:layout_width="@dimen/x48"
        android:layout_height="@dimen/y24"
        android:layout_marginStart="@dimen/m8"
        android:layout_marginEnd="@dimen/x20"
        android:background="@drawable/online_player_bg_live"
        android:gravity="center"
        android:text="直播"
        android:textColor="@color/text_color_white"
        android:textSize="@dimen/m16"
        app:layout_constraintBottom_toBottomOf="@id/tvItemTitle"
        app:layout_constraintEnd_toStartOf="@id/tvDuration"
        app:layout_constraintStart_toEndOf="@id/tvItemTitle"
        app:layout_constraintTop_toTopOf="@id/tvItemTitle" />

    <TextView
        android:id="@+id/subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/y14"
        android:alpha="0.6"
        android:text="主播"
        android:textColor="@color/online_player_broadcast_subtitle"
        android:textSize="@dimen/m20"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@id/tvItemTitle"
        app:layout_constraintTop_toBottomOf="@id/tvItemTitle" />

    <TextView
        android:id="@+id/tvAnchor"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/m10"
        android:alpha="0.6"
        android:textColor="@color/online_player_broadcast_subtitle"
        android:textSize="@dimen/m20"
        app:layout_constraintBottom_toBottomOf="@id/subtitle"
        app:layout_constraintStart_toEndOf="@id/subtitle"
        app:layout_constraintTop_toTopOf="@id/subtitle"
        tools:text="疏影" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/anchorView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="subtitle,tvAnchor" />

    <TextView
        android:id="@+id/tvDuration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/m30"
        android:textColor="@color/online_player_broadcast_title"
        android:textSize="@dimen/m24"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvItemTitle"
        tools:text="01:00-05:29" />
</androidx.constraintlayout.widget.ConstraintLayout>