package com.kaolafm.gradle.plugin.micromodule

import com.android.build.gradle.*
import com.android.build.gradle.api.BaseVariant
import com.android.builder.model.ProductFlavor
import com.android.manifmerger.ManifestMerger2
import com.android.manifmerger.MergingReport
import com.android.manifmerger.XmlDocument
import com.android.utils.ILogger
import com.kaolafm.gradle.plugin.Util
import org.gradle.BuildAdapter
import org.gradle.BuildListener
import org.gradle.BuildResult
import org.gradle.api.*
import org.gradle.api.artifacts.Configuration
/**
 * pins结构项目插件。这里说的module均指pins项目中的一个小的功能模块，根module指的是pins项目上一级的真正的module。
 */
class MicroModulePlugin implements Plugin<Project> {

    private final static String NORMAL = 'normal'
    private final static String ASSEMBLE_OR_GENERATE = 'assemble_or_generate'

    private final static String APPLY_NORMAL_MICRO_MODULE_SCRIPT = 'apply_normal_micro_module_script'
    private final static String APPLY_INCLUDE_MICRO_MODULE_SCRIPT = 'apply_include_micro_module_script'
    private final static String APPLY_EXPORT_MICRO_MODULE_SCRIPT = 'apply_export_micro_module_script'

    private final static BuildListener buildListener = new BuildAdapter() {

        @Override
        void buildFinished(BuildResult buildResult) {
            // generate microModules.xml for MicroModule IDEA plugin.
            //生成microModules.xml文件
            def ideaFile = new File(buildResult.gradle.rootProject.rootDir, '.idea')
            if (!ideaFile.exists()) return

            def microModuleInfo = '<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<modules>\n'
            buildResult.gradle.rootProject.allprojects.each {
                MicroModulePlugin microModulePlugin = it.plugins.findPlugin('micro-module')
                if (microModulePlugin == null) return

                def displayName = it.displayName
                microModuleInfo += '    <module name=\"' + displayName.substring(displayName.indexOf("'") + 1, displayName.lastIndexOf("'")) + '\" path=\"' + it.projectDir.getCanonicalPath() + '\">\n'
                microModulePlugin.microModuleInfo.includeMicroModules.each {
                    MicroModule microModule = it.value
                    microModuleInfo += '        <microModule name=\"' + microModule.name + '\" path=\"' + microModule.microModuleDir.getCanonicalPath() + '\" />\n'
                }
                microModuleInfo += '    </module>\n'
            }
            microModuleInfo += '</modules>'

            def microModules = new File(ideaFile, 'microModules.xml')
            microModules.write(microModuleInfo, 'utf-8')
        }
    }

    Project project

    String startTaskState = NORMAL

    MicroModuleInfo microModuleInfo
    ProductFlavorInfo productFlavorInfo

    MicroModule currentMicroModule
    String applyScriptState

    boolean appliedLibraryPlugin

    boolean clearedOriginSourceSets

    void apply(Project project) {
        this.project = project
        this.microModuleInfo = new MicroModuleInfo(project)

        project.gradle.removeListener(buildListener)
        //添加build监听，在build结束后生成microModules.xml文件
        project.gradle.addBuildListener(buildListener)

        if (project.gradle.getStartParameter().taskNames.size() == 0) {
            startTaskState = NORMAL
        } else {
            startTaskState = ASSEMBLE_OR_GENERATE
        }

        if (startTaskState != NORMAL) {
            //添加gradle添加配置回调。
            project.getConfigurations().whenObjectAdded {
                Configuration configuration = it
                //添加依赖回调。
                configuration.dependencies.whenObjectAdded {
                    if (applyScriptState == APPLY_INCLUDE_MICRO_MODULE_SCRIPT) {
                        configuration.dependencies.remove(it)
                        return
                    } else if (applyScriptState == APPLY_NORMAL_MICRO_MODULE_SCRIPT
                            || applyScriptState == APPLY_EXPORT_MICRO_MODULE_SCRIPT) {
                        return
                    } else if (currentMicroModule == null && startTaskState == ASSEMBLE_OR_GENERATE) {
                        return
                    } else if (it.group != null && it.group.startsWith('com.android.tools')) {
                        return
                    }

                    configuration.dependencies.remove(it)
                }
            }
        }

        DefaultMicroModuleExtension microModuleExtension = project.extensions.create(MicroModuleExtension, 'microModule', DefaultMicroModuleExtension, project)
        microModuleExtension.onMicroModuleListener = new OnMicroModuleListener() {

            @Override
            void addIncludeMicroModule(MicroModule microModule, boolean mainMicroModule) {
                if (mainMicroModule) {
                    microModuleInfo.setMainMicroModule(microModule)
                } else {
                    microModuleInfo.addIncludeMicroModule(microModule)
                }

                //清空系统默认的代码路径。只执行一次
                if(!clearedOriginSourceSets) {
                    productFlavorInfo = new ProductFlavorInfo(project)
                    clearedOriginSourceSets = true
                    clearOriginSourceSet()

                    if(microModuleInfo.mainMicroModule != null) {
                        addMicroModuleSourceSet(microModuleInfo.mainMicroModule)
                    }
                }

                addMicroModuleSourceSet(microModule)
            }

            @Override
            void addExportMicroModule(String... microModulePaths) {
                microModulePaths.each {
                    microModuleInfo.addExportMicroModule(it)
                }
            }

        }

        project.dependencies.metaClass.microModule { String path ->
            if (currentMicroModule == null || applyScriptState == APPLY_NORMAL_MICRO_MODULE_SCRIPT) {
                return []
            }

            if (applyScriptState == APPLY_INCLUDE_MICRO_MODULE_SCRIPT) {
                //设置依赖 gradle中示例 implementation microModule(':p_base')
                microModuleInfo.setMicroModuleDependency(currentMicroModule.name, path)
                return []
            }

            //获取该module包含的其他的module
            MicroModule microModule = microModuleInfo.getMicroModule(path)

            def result = []
            if (startTaskState == ASSEMBLE_OR_GENERATE) {
                addMicroModuleSourceSet(microModule)
                applyMicroModuleScript(microModule)
                microModule.appliedScript = true
            }
            return result
        }

        project.plugins.all {
            Class extensionClass
            if (it instanceof AppPlugin) {
                extensionClass = AppExtension
            } else if (it instanceof LibraryPlugin) {
                extensionClass = LibraryExtension
            } else {
                return
            }

            project.extensions.configure(extensionClass, new Action<? extends TestedExtension>() {
                @Override
                void execute(TestedExtension testedExtension) {
                    boolean isLibrary
                    DomainObjectSet<BaseVariant> baseVariants
                    if (testedExtension instanceof AppExtension) {
                        AppExtension appExtension = (AppExtension) testedExtension
                        baseVariants = appExtension.applicationVariants
                    } else {
                        LibraryExtension libraryExtension = (LibraryExtension) testedExtension
                        baseVariants = libraryExtension.libraryVariants
                        isLibrary = true
                    }

                    //遍历各个变体(buildType+productFlavors)，并检查其边界。
                    baseVariants.all { BaseVariant variant ->
                        if (microModuleExtension.codeCheckEnabled) {
                            def taskNamePrefix = isLibrary ? 'package' : 'merge'
                            List<String> sourceFolders = new ArrayList<>()
                            sourceFolders.add('main')
                            sourceFolders.add(variant.buildType.name)
                            if (variant.productFlavors.size() > 0) {
                                sourceFolders.add(variant.name)
                                sourceFolders.add(variant.flavorName)
                                for (ProductFlavor productFlavor : variant.productFlavors) {
                                    sourceFolders.add(productFlavor.name)
                                }
                                checkMicroModuleBoundary(taskNamePrefix, variant.buildType.name, variant.flavorName, sourceFolders)
                            } else {
                                checkMicroModuleBoundary(taskNamePrefix, variant.buildType.name, null, sourceFolders)
                            }
                        }
                    }
                }
            })
        }

        project.afterEvaluate {
            microModuleExtension.onMicroModuleListener = null
            if (microModuleInfo.mainMicroModule == null) {
                throw new GradleException("the main MicroModule could not be found in ${project.getDisplayName()}.")
            }

            appliedLibraryPlugin = project.pluginManager.hasPlugin('com.android.library')

            productFlavorInfo = new ProductFlavorInfo(project)

            applyScriptState = APPLY_INCLUDE_MICRO_MODULE_SCRIPT
            microModuleInfo.includeMicroModules.each {
                MicroModule microModule = it.value
                microModuleInfo.dependencyGraph.add(microModule.name)
                applyMicroModuleScript(microModule)
            }

            clearOriginSourceSet()
            if (startTaskState == ASSEMBLE_OR_GENERATE) {
                applyScriptState = APPLY_EXPORT_MICRO_MODULE_SCRIPT
                boolean hasExportMainMicroModule = false
                boolean isEmpty = microModuleInfo.exportMicroModules.isEmpty()
                List<String> dependencySort = microModuleInfo.dependencyGraph.topSort()
                dependencySort.each {
                    if (isEmpty || microModuleInfo.exportMicroModules.containsKey(it)) {
                        MicroModule microModule = microModuleInfo.getMicroModule(it)
                        if (microModule == null) {
                            throw new GradleException("MicroModule with path '${it}' could not be found in ${project.getDisplayName()}.")
                        }

                        if (microModule == microModuleInfo.mainMicroModule) {
                            hasExportMainMicroModule = true
                        }

                        if (microModule.appliedScript) return

                        addMicroModuleSourceSet(microModule)
                        applyMicroModuleScript(microModule)
                        microModule.appliedScript = true
                    }
                }

                if (!hasExportMainMicroModule) {
                    throw new GradleException("the main MicroModule '${microModuleInfo.mainMicroModule.name}' is not in the export list.")
                }
            } else {
                applyScriptState = APPLY_NORMAL_MICRO_MODULE_SCRIPT
                microModuleInfo.includeMicroModules.each {
                    MicroModule microModule = it.value
                    addMicroModuleSourceSet(microModule)
                    applyMicroModuleScript(microModule)
                }
            }
            currentMicroModule = null

            generateAndroidManifest()

            project.tasks.preBuild.doFirst {
                clearOriginSourceSet()
                if (startTaskState == ASSEMBLE_OR_GENERATE) {
                    microModuleInfo.includeMicroModules.each {
                        MicroModule microModule = it.value
                        if (microModule.appliedScript) {
                            addMicroModuleSourceSet(microModule)
                        }
                    }
                } else {
                    microModuleInfo.includeMicroModules.each {
                        addMicroModuleSourceSet(it.value)
                    }
                }
                generateAndroidManifest()
            }
        }
    }

    /**
     * 生成清单文件。
     * @return
     */
    def generateAndroidManifest() {
        if ((startTaskState == ASSEMBLE_OR_GENERATE || !microModuleInfo.exportMicroModules.isEmpty()) && isMainSourceSetEmpty()) {
            setMainSourceSetManifest()
            return
        }
        mergeAndroidManifest('main')

        productFlavorInfo.buildTypes.each {
            mergeAndroidManifest(it)
        }

        if (!productFlavorInfo.singleDimension) {
            productFlavorInfo.productFlavors.each {
                mergeAndroidManifest(it)
            }
        }

        productFlavorInfo.combinedProductFlavors.each {
            mergeAndroidManifest(it)

            def productFlavor = it
            productFlavorInfo.buildTypes.each {
                mergeAndroidManifest(productFlavor + Util.upperCase(it))
            }
        }

        def androidTest = 'androidTest'
        mergeAndroidManifest(androidTest)
        mergeAndroidManifest(androidTest + 'Debug')
        if (!productFlavorInfo.singleDimension) {
            productFlavorInfo.productFlavors.each {
                mergeAndroidManifest(androidTest + Util.upperCase(it))
            }
        }
        productFlavorInfo.combinedProductFlavors.each {
            mergeAndroidManifest(androidTest + Util.upperCase(it))
            mergeAndroidManifest(androidTest + Util.upperCase(it) + 'Debug')
        }
    }

    /**
     * 将指定的变种名作为该module下的主AndroidManifest合并其他变种的AndroidManifest。
     * @param variantName 变种名
     * @return
     */
    def mergeAndroidManifest(String variantName) {
        File mainManifestFile = new File(microModuleInfo.mainMicroModule.microModuleDir, "/src/${variantName}/AndroidManifest.xml")
        if (!mainManifestFile.exists()) return
        ManifestMerger2.MergeType mergeType = ManifestMerger2.MergeType.APPLICATION
        XmlDocument.Type documentType = XmlDocument.Type.MAIN
        def logger = new ILogger() {
            @Override
            void error(Throwable t, String msgFormat, Object... args) {
                println(msgFormat)
            }

            @Override
            void warning(String msgFormat, Object... args) {

            }

            @Override
            void info(String msgFormat, Object... args) {

            }

            @Override
            void verbose(String msgFormat, Object... args) {

            }
        }
        ManifestMerger2.Invoker invoker = new ManifestMerger2.Invoker(mainManifestFile, logger, mergeType, documentType)
        invoker.withFeatures(ManifestMerger2.Invoker.Feature.NO_PLACEHOLDER_REPLACEMENT)

        microModuleInfo.includeMicroModules.each {
            MicroModule microModule = it.value
            if (startTaskState == ASSEMBLE_OR_GENERATE && !microModule.appliedScript) return
            if (microModule.name == microModuleInfo.mainMicroModule.name) return
            def microManifestFile = new File(microModule.microModuleDir, "/src/${variantName}/AndroidManifest.xml")
            if (microManifestFile.exists()) {
                invoker.addLibraryManifest(microManifestFile)
            }
        }

        def mergingReport = invoker.merge()
        if (!mergingReport.result.success) {
            mergingReport.log(logger)
            throw new GradleException(mergingReport.reportString)
        }
        def moduleAndroidManifest = mergingReport.getMergedDocument(MergingReport.MergedManifestKind.MERGED)
        moduleAndroidManifest = new String(moduleAndroidManifest.getBytes('UTF-8'))

        def saveDir = new File(project.projectDir, "build/microModule/merge-manifest/${variantName}")
        saveDir.mkdirs()
        def AndroidManifestFile = new File(saveDir, 'AndroidManifest.xml')
        AndroidManifestFile.createNewFile()
        AndroidManifestFile.write(moduleAndroidManifest,"utf-8")

        def extensionContainer = project.getExtensions()
        BaseExtension android = extensionContainer.getByName('android')
        def obj = android.sourceSets.findByName(variantName)
        if (obj == null) {
            return
        }
        obj.manifest.srcFile project.projectDir.absolutePath + "/build/microModule/merge-manifest/${variantName}/AndroidManifest.xml"
    }

    /**
     * 添加main module和所有渠道的的源码路径
     * @param microModule
     * @return
     */
    def addMicroModuleSourceSet(MicroModule microModule) {
        addVariantSourceSet(microModule, 'main')

        productFlavorInfo.buildTypes.each {
            addVariantSourceSet(microModule, it)
        }

        if (!productFlavorInfo.singleDimension) {
            productFlavorInfo.productFlavors.each {
                addVariantSourceSet(microModule, it)
            }
        }

        productFlavorInfo.combinedProductFlavors.each {
            addVariantSourceSet(microModule, it)
            def flavorName = it
            productFlavorInfo.buildTypes.each {
                addVariantSourceSet(microModule, flavorName + Util.upperCase(it))
            }
        }

        def testTypes = ['androidTest', 'test']
        testTypes.each {
            def testType = it
            addVariantSourceSet(microModule, testType)

            if (testType == 'test') {
                productFlavorInfo.buildTypes.each {
                    addVariantSourceSet(microModule, testType + Util.upperCase(it))
                }
            } else {
                addVariantSourceSet(microModule, testType + 'Debug')
            }

            if (!productFlavorInfo.singleDimension) {
                productFlavorInfo.productFlavors.each {
                    addVariantSourceSet(microModule, testType + Util.upperCase(it))
                }
            }

            productFlavorInfo.combinedProductFlavors.each {
                def productFlavorName = testType + Util.upperCase(it)
                addVariantSourceSet(microModule, productFlavorName)

                if (testType == 'test') {
                    productFlavorInfo.buildTypes.each {
                        addVariantSourceSet(microModule, productFlavorName + Util.upperCase(it))
                    }
                } else {
                    addVariantSourceSet(microModule, productFlavorName + 'Debug')
                }
            }
        }
    }

    /**
     * 清空系统默认的源码路径
     * @return
     */
    def clearOriginSourceSet() {
        clearModuleSourceSet('main')

        // buildTypes
        productFlavorInfo.buildTypes.each {
            clearModuleSourceSet(it)
        }

        if (!productFlavorInfo.singleDimension) {
            productFlavorInfo.productFlavors.each {
                clearModuleSourceSet(it)
            }
        }

        productFlavorInfo.combinedProductFlavors.each {
            clearModuleSourceSet(it)
            def flavorName = it
            productFlavorInfo.buildTypes.each {
                clearModuleSourceSet(flavorName + Util.upperCase(it))
            }
        }

        def testTypes = ['androidTest', 'test']
        testTypes.each {
            def testType = it
            clearModuleSourceSet(testType)

            if (testType == 'test') {
                productFlavorInfo.buildTypes.each {
                    clearModuleSourceSet(testType + Util.upperCase(it))
                }
            } else {
                clearModuleSourceSet(testType + 'Debug')
            }

            if (!productFlavorInfo.singleDimension) {
                productFlavorInfo.productFlavors.each {
                    clearModuleSourceSet(testType + Util.upperCase(it))
                }
            }

            productFlavorInfo.combinedProductFlavors.each {
                def productFlavorName = testType + Util.upperCase(it)
                clearModuleSourceSet(productFlavorName)

                if (testType == 'test') {
                    productFlavorInfo.buildTypes.each {
                        clearModuleSourceSet(productFlavorName + Util.upperCase(it))
                    }
                } else {
                    clearModuleSourceSet(productFlavorName + 'Debug')
                }
            }
        }
    }

    def isMainSourceSetEmpty() {
        BaseExtension android = project.extensions.getByName('android')
        def obj = android.sourceSets.findByName('main')
        if (obj == null) {
            return true
        }
        return obj.java.srcDirs.size() == 0;
    }

    /**
     * 设置main的清单文件路径。
     * @return
     */
    def setMainSourceSetManifest() {
        BaseExtension android = project.extensions.getByName('android')
        def obj = android.sourceSets.findByName('main')
        if (obj == null) {
            obj = android.sourceSets.create('main')
        }
        File mainManifestFile = new File(microModuleInfo.mainMicroModule.microModuleDir, '/src/main/AndroidManifest.xml')
        obj.manifest.srcFile mainManifestFile
    }

    /**
     * 添加指定module的源码路径，绝对路径+/src/${type}/java。如xx/k-module-kaolafm/main/src/main/java
     * @param microModule
     * @param type
     * @return
     */
    def addVariantSourceSet(MicroModule microModule, def type) {
        def absolutePath = microModule.microModuleDir.absolutePath
        BaseExtension android = project.extensions.getByName('android')
        def obj = android.sourceSets.findByName(type)
        if (obj == null) {
            obj = android.sourceSets.create(type)
        }
        obj.java.srcDir(absolutePath + "/src/${type}/java")
        obj.java.srcDir(absolutePath + "/src/${type}/kotlin")
        obj.res.srcDir(absolutePath + "/src/${type}/res")
        obj.jni.srcDir(absolutePath + "/src/${type}/jni")
        obj.jniLibs.srcDir(absolutePath + "/src/${type}/jniLibs")
        obj.aidl.srcDir(absolutePath + "/src/${type}/aidl")
        obj.assets.srcDir(absolutePath + "/src/${type}/assets")
        obj.shaders.srcDir(absolutePath + "/src/${type}/shaders")
        obj.resources.srcDir(absolutePath + "/src/${type}/resources")
        obj.renderscript.srcDir(absolutePath + "/src/${type}/rs")
    }

    /**
     * 清空指定module的原始源码路径
     * @param type
     * @return
     */
    def clearModuleSourceSet(def type) {
        def srcDirs = []
        BaseExtension android = project.extensions.getByName('android')
        def obj = android.sourceSets.findByName(type)
        if (obj == null) {
            return
        }
        obj.java.srcDirs = srcDirs
        obj.res.srcDirs = srcDirs
        obj.jni.srcDirs = srcDirs
        obj.jniLibs.srcDirs = srcDirs
        obj.aidl.srcDirs = srcDirs
        obj.assets.srcDirs = srcDirs
        obj.shaders.srcDirs = srcDirs
        obj.resources.srcDirs = srcDirs
        obj.renderscript.srcDirs = srcDirs
    }

    /**
     * 将指定module下的build.gradle 添加到根module的build.gradle中。
     * @param microModule
     */
    void applyMicroModuleScript(MicroModule microModule) {
        def microModuleBuild = new File(microModule.microModuleDir, 'build.gradle')
        if (microModuleBuild.exists()) {
            MicroModule tempMicroModule = currentMicroModule
            currentMicroModule = microModule
            project.apply from: microModuleBuild.absolutePath
            currentMicroModule = tempMicroModule
        }
    }

    /**
     * 检查module的边界。
     * @param taskPrefix task前缀 'package' 或 'merge'
     * @param buildType android {
                             buildType{}
                        }
     * @param flavorName 渠道名
     * @param sourceFolders 源码文件路径 main或变种
     * @return
     */
    def checkMicroModuleBoundary(String taskPrefix, String buildType, String flavorName, List<String> sourceFolders) {
        CodeChecker codeChecker

        def buildTypeFirstUp = Util.upperCase(buildType)
        def productFlavorFirstUp = flavorName != null ? Util.upperCase(flavorName) : ""

        def mergeResourcesTaskName = taskPrefix + productFlavorFirstUp + buildTypeFirstUp + 'Resources'
        def packageResourcesTask = project.tasks.findByName(mergeResourcesTaskName)
        if (packageResourcesTask != null) {
            codeChecker = new CodeChecker(project, microModuleInfo, productFlavorInfo, buildType, flavorName)
            packageResourcesTask.doLast {
                codeChecker.checkResources(mergeResourcesTaskName, sourceFolders)
            }
        }

        def compileJavaTaskName = "compile${productFlavorFirstUp}${buildTypeFirstUp}JavaWithJavac"
        def compileJavaTask = project.tasks.findByName(compileJavaTaskName)
        if (compileJavaTask != null) {
            compileJavaTask.doLast {
                if (codeChecker == null) {
                    codeChecker = new CodeChecker(project, microModuleInfo, productFlavorInfo, buildType, flavorName)
                }
                codeChecker.checkClasses(mergeResourcesTaskName, sourceFolders)
            }
        }
    }

}