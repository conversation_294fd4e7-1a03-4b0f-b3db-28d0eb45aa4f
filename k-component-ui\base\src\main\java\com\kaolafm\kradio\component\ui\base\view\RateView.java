package com.kaolafm.kradio.component.ui.base.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;

import androidx.appcompat.widget.AppCompatImageView;

import com.github.penfeizhou.animation.apng.APNGDrawable;
import com.github.penfeizhou.animation.loader.AssetStreamLoader;
import com.kaolafm.kradio.component.ui.R;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.opensdk.player.logic.PlayerManager;

/**
 * @Description:
 * @Author: Maclay
 * @Date: 2022/1/11 6:44 下午
 */
public class RateView extends AppCompatImageView {

    public static final String ASSETS_LOADING_APNG = "playAnim.png";
    private APNGDrawable apngDrawable;

    public RateView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    public void setVisibility(int visibility) {
        super.setVisibility(visibility);
        if (visibility == View.VISIBLE) {
            updatePlayState();
        } else {
            cancelAnimation();
        }
    }

    private void updatePlayState() {
        try {
            if (PlayerManager.getInstance().isPlaying()) {
                playAnimation();
            } else {
                pauseAnimation();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void startAnimation() {
        try {
            playAnimation();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void playAnimation() {
        showPlayAnim(this);
    }

    public void stopAnimation() {
        try {
            pauseAnimation();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void pauseAnimation() {
        pausePlayAnim();
    }


    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        releaseLottie();
    }

    public void showPlayAnim(ImageView animIcon){
        ViewUtil.setViewVisibility(animIcon,View.VISIBLE);
        AssetStreamLoader assetLoader = new AssetStreamLoader(animIcon.getContext(), ASSETS_LOADING_APNG);
        apngDrawable = new APNGDrawable(assetLoader);
        animIcon.setImageDrawable(apngDrawable);
        apngDrawable.start();
    }

    private void pausePlayAnim() {
        if(apngDrawable != null){
            apngDrawable.stop();
            apngDrawable = null;
        }
        this.setImageDrawable(ResUtil.getDrawable(R.drawable.play_anim));
    }
    private void resumePlayAnim(ImageView animIcon) {
        if(apngDrawable != null){
            apngDrawable.resume();
        }else {
            showPlayAnim(animIcon);
        }
    }
    private void cancelAnimation() {
        pausePlayAnim();
    }


    private void releaseLottie() {
        apngDrawable = null;
    }

    public boolean isAnimating() {
        if(apngDrawable != null){
            apngDrawable.isRunning();
        }
        return false;
    }
}