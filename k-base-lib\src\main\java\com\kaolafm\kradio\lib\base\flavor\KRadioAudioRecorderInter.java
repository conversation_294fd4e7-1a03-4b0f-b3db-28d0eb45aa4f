package com.kaolafm.kradio.lib.base.flavor;

import com.kaolafm.kradio.lib.base.flavor.live.KradioRecorderInterface;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-03-12 11:54
 ******************************************/
public interface KRadioAudioRecorderInter {

    /**
     * 上层APP初始化VR
     *
     * @param args
     * @return
     */
    boolean initVR(Object... args);

    /**
     * 上层APP开始录音
     *
     * @param args
     * @return true 已处理开始录音逻辑，false 未处理开始录音逻辑
     */
    boolean onAudioRecordStart(Object... args);

    /**
     * 上层APP停止录音
     *
     * @param args
     * @return true 已处理停止录音逻辑，false 未处理停止录音逻辑
     */
    boolean onAudioRecordStop(Object... args);

    /**
     * @param args
     * @return
     */
    boolean onAudioRecordStopAfter(Object... args);

    /**
     * 获取三方录音机
     *
     * @return 返回一个录音对象
     */
    KradioRecorderInterface getRecorder();

    /**
     * 上层APP设置语音状态监听
     *
     * @param onVRStatusListener
     * @return
     */
    void setVrStatusListener(OnVRStatusListener onVRStatusListener);

    interface OnVRStatusListener {
        void onSpeakBegin();

        void onSpeakCompleted();
    }

    interface OnMicFocusStatusListener {
        void onMicFocusGain();

        void onMicFocusLoss();
    }
}
