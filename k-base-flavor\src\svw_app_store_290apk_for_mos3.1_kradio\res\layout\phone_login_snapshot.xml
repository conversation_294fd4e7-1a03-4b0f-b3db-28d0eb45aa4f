<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:fitsSystemWindows="true"
    android:id="@+id/phone_root_view"
    android:background="@drawable/bg_home"
    android:paddingTop="@dimen/y50"
    android:focusable="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/search_top_guideline"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.15" />

    <ImageView
        android:id="@+id/back_img"
        style="@style/FragmentBackButton"
        android:src="@drawable/user_ic_left_back"
        android:layout_marginStart="@dimen/x22"
        app:layout_constraintBottom_toTopOf="@id/search_top_guideline"
        tools:ignore="MissingConstraints" />
    <EditText
        android:id="@+id/input_view"
        android:layout_width="@dimen/x375"
        android:layout_height="@dimen/y50"
        android:background="@null"
        android:hint="@string/user_input_phone_num"
        android:textSize="@dimen/text_size2"
        android:inputType="number"
        android:maxLength="11"
        android:imeOptions="actionDone"
        android:layout_marginTop="@dimen/y8"
        android:maxLines="1"
        android:gravity="center_vertical"
        android:textColor="@color/text_color_1"
        android:textColorHint="@color/text_color_2"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toTopOf="@id/search_top_guideline"
        app:layout_constraintTop_toTopOf="parent"/>

    <View
        android:id="@+id/num_line"
        android:layout_width="0dp"
        android:layout_height="@dimen/y1"
        android:background="@color/user_login_line_color"
        app:layout_constraintLeft_toLeftOf="@id/input_view"
        app:layout_constraintRight_toRightOf="@id/input_view"
        app:layout_constraintTop_toBottomOf="@id/input_view"
        android:layout_marginBottom="@dimen/y100"
        tools:ignore="NotSibling" />

    <TextView
        android:id="@+id/tv_ok"
        android:layout_width="@dimen/x130"
        android:layout_height="@dimen/y68"
        android:background="@drawable/bg_search_btn"
        app:layout_constraintBottom_toTopOf="@id/search_top_guideline"
        app:layout_constraintTop_toTopOf="parent"
        android:gravity="center"
        android:layout_marginEnd="@dimen/x25"
        android:text="@string/ok"
        android:textColor="@color/search_btn_text_color"
        android:textSize="@dimen/text_size5"
        app:layout_constraintRight_toRightOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>