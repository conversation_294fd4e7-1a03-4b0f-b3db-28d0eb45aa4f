package com.kaolafm.kradio.online.mine.login;

import android.animation.ObjectAnimator;
import android.os.Build;
import androidx.annotation.RequiresApi;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.kaolafm.kradio.common.widget.OvalImageView;
import com.kaolafm.kradio.k_kaolafm.R; 
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.ui.online.BaseViewPagerFragment;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.AnimUtil;
import com.kaolafm.kradio.lib.utils.NetworkManager;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.lib.utils.imageloader.listener.OnImageLoaderListener;
import com.kaolafm.kradio.user.UserInfoManager;
import com.kaolafm.kradio.user.ui.AccountLoginPresenter;
import com.kaolafm.kradio.user.ui.IAccountLoginView;
import com.kaolafm.opensdk.api.login.model.UserInfo;

import java.lang.ref.WeakReference;
 

/**
 * <pre>
 *     <AUTHOR> Wenchl
 *     e-mail : <EMAIL>
 *     time   : 2019/03/06
 *     desc   :
 *     version: 1.0
 * </pre>
 */
public class OnlineAccountLoginFragment extends BaseViewPagerFragment<AccountLoginPresenter> implements IAccountLoginView {
 
    TextView tvLoginTitle; 
    OvalImageView ivLoginQr; 
    TextView tvLoginDownloadApp; 
    LinearLayout userLoginLayout; 
    View no_network_view; 
    RelativeLayout mRootLayout; 
    LinearLayout userThirdPlatformView; 
    TextView qrErrorMsg; 
    ImageView loadingView;

    NetWorkListener mNetWorkListener;
    ObjectAnimator animator;

    @Override
    public void onDestroy() {
        super.onDestroy();
        NetworkManager.getInstance().removeNetworkReadyListener(mNetWorkListener);
    }
    @Override
    protected boolean isReportFragment() {
        return true;
    }
    @Override
    protected int getLayoutId() {
        return R.layout.online_fragment_qr_login;
    }

    @Override
    protected com.kaolafm.kradio.user.ui.AccountLoginPresenter createPresenter() {
        return new AccountLoginPresenter(this);
    }

    @Override
    public void initView(View view) {

        tvLoginTitle = view.findViewById(R.id.tv_login_title);
        ivLoginQr = view.findViewById(R.id.iv_login_qr);
        tvLoginDownloadApp = view.findViewById(R.id.tv_login_download_app);
        userLoginLayout = view.findViewById(R.id.user_qr_layout);
        no_network_view = view.findViewById(R.id.user_no_network_rel);
        mRootLayout = view.findViewById(R.id.user_account_main_layout);
        userThirdPlatformView = view.findViewById(R.id.user_third_platform_view);
        qrErrorMsg = view.findViewById(R.id.tv_error_msg);
        loadingView = view.findViewById(R.id.network_nosigin);
 
 
        initNetworkListener();
        no_network_view.setOnClickListener(v -> {
            mPresenter.getData();
        });
    }

    @Override
    public void showQRCode(String url) {

        ImageLoader.getInstance().displayImage(getContext(), url, ivLoginQr,
                ResUtil.getDrawable(R.drawable.user_qr_error_bg), new OnImageLoaderListener() {
                    @Override
                    public void onLoadingFailed(String url, ImageView target, Exception exception) {
                        showNoNetWork();
                    }

                    @Override
                    public void onLoadingComplete(String url, ImageView target) {
                        ViewUtil.setViewVisibility(userLoginLayout, View.VISIBLE);
                        ViewUtil.setViewVisibility(userThirdPlatformView, View.GONE);
                        ViewUtil.setViewVisibility(no_network_view, View.GONE);
                    }
                });


    }

    @Override
    public void showBindSuccess(UserInfo userInfo) {
        ToastUtil.showOnActivity(getActivity(),
                ResUtil.getString(R.string.user_login_success));
//        EventBus.getDefault().post(new OnlineLoginEvent());
        if (getActivity() instanceof OnlineLoginActivity){
            ((OnlineLoginActivity)getActivity()).reportLoginEvent();
        }
        getActivity().finish();
    }

    @Override
    public void onResume() {
        super.onResume();
        if (UserInfoManager.getInstance().isQrScanned() && mPresenter != null) {
            mPresenter.checkQRCodeStatusAndInfo();
            return;
        }
        if (mPresenter != null) {
            mPresenter.refreshData();
        }
    }

    @Override
    public void onSupportInvisible() {
        super.onSupportInvisible();
        if (mPresenter != null) {
            mPresenter.cancelCheck();
        }
    }

    @Override
    public void onSupportVisible() {
        super.onSupportVisible();
        if (UserInfoManager.getInstance().isQrScanned() && mPresenter != null) {
            mPresenter.checkQRCodeStatusAndInfo();
            return;
        }
        if (mPresenter != null) {
            mPresenter.getData();
        }
    }



    @Override
    public void showNoNetWork() {
        ViewUtil.setViewVisibility(userLoginLayout, View.INVISIBLE);
        ViewUtil.setViewVisibility(no_network_view, View.VISIBLE);
        ToastUtil.showInfo(AppDelegate.getInstance().getContext(), ResUtil.getString(R.string.network_error_toast));
        qrErrorMsg.setText(ResUtil.getString(R.string.user_qr_error_no_network_msg));
        AnimUtil.stopAnimation(animator);
    }

    @Override
    public void showQrExpire() {
        ViewUtil.setViewVisibility(userLoginLayout, View.INVISIBLE);
        ViewUtil.setViewVisibility(no_network_view, View.VISIBLE);
//        ToastUtil.showOnActivity(getContext(), ResUtil.getString(R.string.user_qr_expire_toast));
        qrErrorMsg.setText(ResUtil.getString(R.string.user_qr_expire_msg));
        AnimUtil.stopAnimation(animator);
        if (getActivity() instanceof OnlineLoginActivity) {
            ((OnlineLoginActivity) getActivity()).hideBindSuccess();
        }
    }

    @Override
    public void showQrScaned(String avatar, String name) {
        if (getActivity() instanceof OnlineLoginActivity) {
            ((OnlineLoginActivity) getActivity()).showBindSuccess(avatar, name);
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    @Override
    public void showLoading() {
        ViewUtil.setViewVisibility(no_network_view, View.VISIBLE);
        qrErrorMsg.setText(ResUtil.getString(R.string.user_qr_loading));
        if (animator == null) {
            animator = AnimUtil.startRotation(loadingView, 500);
        } else {
            animator.resume();
        }
    }

    @Override
    public void hideLoading() {
        AnimUtil.stopAnimation(animator);
    }

    @Override
    public void showIntercepterView(View view) {
        userThirdPlatformView.removeAllViews();
        userThirdPlatformView.addView(view);
        userThirdPlatformView.setVisibility(View.VISIBLE);
        userLoginLayout.setVisibility(View.INVISIBLE);
        ViewUtil.setViewVisibility(no_network_view, View.GONE);
    }




    @Override
    protected void showAccordingToScreen(int orientation) {
        super.showAccordingToScreen(orientation);
//        int paddingLeft = ScreenUtil.getGlobalPaddingLeft(orientation);
//        int paddingRight = ScreenUtil.getGlobalPaddingRight(orientation);

//        mRootLayout.setPadding(paddingLeft, 0, paddingRight, 0);

    }

    @Override
    protected void changeViewLayoutForStatusBar(View view) {
    }

    @Override
    protected void addFragmentRootViewPadding(View view) {
    }

    private void initNetworkListener() {
        if (UserInfoManager.getInstance().isUserBound()) {
            return;
        }

        mNetWorkListener = new NetWorkListener(this);
        NetworkManager.getInstance().addNetworkReadyListener(mNetWorkListener);

    }

    public static class NetWorkListener implements NetworkManager.INetworkReady {
        private WeakReference accountLoginWeakReference;

        public NetWorkListener(OnlineAccountLoginFragment accountLoginFragment) {
            accountLoginWeakReference = new WeakReference<>(accountLoginFragment);
        }

        @Override
        public void networkChange(boolean hasNetwork) {
            if (!hasNetwork) {
                return;
            }
            OnlineAccountLoginFragment accountLoginFragment = (OnlineAccountLoginFragment) accountLoginWeakReference.get();
            if (accountLoginFragment == null) {
                return;
            }
            accountLoginFragment.mPresenter.getData();
        }
    }
}
