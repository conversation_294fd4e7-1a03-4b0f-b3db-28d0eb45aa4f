<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.kaolafm.kradio.home.comprehensive">

    <!-- 使用考拉FM车载SDK所需权限 start -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<!--    <uses-permission android:name="android.permission.READ_PHONE_STATE" />-->
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <!-- 使用考拉FM车载SDK所需权限 end -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <!-- SD卡权限 -->
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
<!--    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />-->
<!--    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />-->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />

    <!--网易聊天室需要-->
    <uses-permission android:name="android.permission.FLASHLIGHT" />
    <uses-permission android:name="android.permission.VIBRATE" />
       
    <uses-permission android:name="android.permission.BLUETOOTH" />
       
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <!--JobIntentService的权限-->
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <!--android:resizeableActivity="true" 不添加此配置会导致APP在Android9.0系统上显示不全问题，具体bug可参考https://app.huoban.com/tables/2100000007530121/items/2300000932317385?userId=1917386-->
    <application>
        <!--com.kaolafm.auto.home.HubActivity-->
<!--        <activity-->
<!--            android:name="com.kaolafm.auto.home.HubActivity"-->
<!--            android:configChanges="orientation|screenSize|keyboardHidden|locale|layoutDirection|keyboard|uiMode"-->
<!--            android:launchMode="${MAIN_ACTIVITY_LAUNCH_MODE}"-->
<!--            android:screenOrientation="unspecified"-->
<!--            android:theme="@style/AppThemeCompat.splash">-->
<!--            <intent-filter>-->
<!--                <action android:name="android.intent.action.MAIN" />-->
<!--                <category android:name="android.intent.category.LAUNCHER" />-->
<!--                <category android:name="android.intent.category.DEFAULT" />-->
<!--                <category android:name="android.intent.category.MONKEY" />-->
<!--            </intent-filter>-->
<!--            <meta-data-->
<!--                android:name="distractionOptimized"-->
<!--                android:value="true" />-->
<!--        </activity>-->

        <activity
            android:name="com.kaolafm.launcher.LauncherActivity"
            android:configChanges="orientation|screenSize|smallestScreenSize|screenLayout|keyboardHidden|locale|layoutDirection|keyboard|uiMode|fontScale|density"
            android:launchMode="singleTask"
            android:exported="${HAS_EXPORTED}"
            android:screenOrientation="landscape"
            android:theme="@style/ComprehensiveAppThemeCompat"
            android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
            </intent-filter>
            <meta-data
                android:name="distractionOptimized"
                android:value="true" />
        </activity>

        <activity
            android:name="com.kaolafm.kradio.search.SearchActivity"
            android:configChanges="orientation|screenSize|locale|layoutDirection|keyboard|screenLayout|uiMode|fontScale"
            android:launchMode="singleTop"
            android:screenOrientation="landscape"
            android:theme="@style/ComprehensiveAppThemeCompat"
            android:windowSoftInputMode="adjustPan" />

        <activity
            android:name="com.kaolafm.kradio.user.comprehensive.order.MyOrderActivity"
            android:configChanges="orientation|screenSize|locale|layoutDirection|keyboard|screenLayout|uiMode"
            android:launchMode="singleTop"
            android:screenOrientation="landscape"
            android:theme="@style/ComprehensiveAppThemeCompat"
            android:windowSoftInputMode="adjustPan" />


        <receiver android:name="com.kaolafm.notification.NotificationDeleteReceiver"
            android:exported="${HAS_EXPORTED}">
            <intent-filter>
                <action android:name="com.kradio.delete.notification" />
            </intent-filter>
        </receiver>
    </application>
</manifest>
