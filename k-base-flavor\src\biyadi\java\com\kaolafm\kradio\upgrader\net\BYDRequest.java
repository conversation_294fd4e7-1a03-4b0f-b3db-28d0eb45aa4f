package com.kaolafm.kradio.upgrader.net;

import com.google.gson.Gson;
import com.kaolafm.kradio.upgrader.net.model.QueryRequestData;

import okhttp3.MediaType;
import okhttp3.RequestBody;

/**
 * <AUTHOR> on 2019/4/1.
 */

public class BYDRequest {

    public RequestBody getQuery(QueryRequestData queryRequestData) {
        String json = new Gson().toJson(queryRequestData);
        return RequestBody.create(MediaType.parse("application/json; charset=utf-8"), json);
    }

}
