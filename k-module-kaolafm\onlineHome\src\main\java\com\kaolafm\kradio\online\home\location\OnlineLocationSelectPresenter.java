package com.kaolafm.kradio.online.home.location;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.common.report.ReportParamUtil;
import com.kaolafm.kradio.common.report.ReportUtil;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.online.home.location.bean.OnlineRecomandCityBean;
import com.kaolafm.opensdk.api.search.model.SearchProgramBean;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import java.util.List;

public class OnlineLocationSelectPresenter extends BasePresenter<OnlineLocationSelectModel, ILocationSelectView> {

    public OnlineLocationSelectPresenter(ILocationSelectView view) {
        super(view);
    }

    @Override
    protected OnlineLocationSelectModel createModel() {
        return new OnlineLocationSelectModel();
    }

    @Override
    public void destroy() {
        cancelRequest();
        super.destroy();
    }

    /**
     * 获取联想词
     *
     * @param keyword 关键词
     */
    public void getAssociateWords(String keyword) {
        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
            mView.showNoNetView();
            return;
        }
        mModel.getAssociateWords(keyword, new HttpCallback<List<OnlineRecomandCityBean>>() {

            @Override
            public void onSuccess(List<OnlineRecomandCityBean> onlineGetCityResultBeans) {
                //                List<AssociateInfo> associateInfos = new ArrayList<>();
//                for (String word : strings) {
//                    AssociateInfo associateInfo = new AssociateInfo();
//                    associateInfo.setAssociateWord(word);
//                    associateInfo.setKeyWord(keyword);
//                    associateInfos.add(associateInfo);
//                }
                mView.showAssociateWordsView(onlineGetCityResultBeans);
            }

            @Override
            public void onError(ApiException e) {
                mView.showAssociateWordsView(null);
            }
        });
    }

    /**
     * 获取热词
     */
    public void getHotSearchWords() {
        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
            mView.showNoNetView();
            return;
        }
        mModel.getHotSearchWords(new HttpCallback<List<OnlineRecomandCityBean>>() {
            @Override
            public void onSuccess(List<OnlineRecomandCityBean> hotWords) {
                if (ListUtil.isEmpty(hotWords)) {
                    return;
                }

                mView.showHotSearchWordsView(hotWords);
            }

            @Override
            public void onError(ApiException e) {
                mView.showErrorView(e.getCode());
            }
        });
    }

    /**
     * /**
     * 上报搜索播放事件
     *
     * @param searchProgramBean 搜索条目
     * @param postion           位置
     */
    public void reportSearchResultPlayEvent(SearchProgramBean searchProgramBean, int postion) {
        ReportUtil.reportSearchSelectResult("2", null, String.valueOf(searchProgramBean.getId()),
                String.valueOf(postion + 1), searchProgramBean.getCallback());
        ReportUtil.reportSearchToPlay("2", searchProgramBean.getCallback());
    }

    /**
     * 上报内容被点击事件
     *
     * @param bean
     * @param position
     */
    public void reportContentClickEvent(SearchProgramBean bean, int position) {
        ReportUtil.addContentClickEvent("", ReportParamUtil.getRadioType(bean.getType()),
                "", String.valueOf(bean.getId()),
                ReportParamUtil.getEventTag(bean.getVip() == 1, bean.getFine() == 1),
                Constants.PAGE_ID_LOCATION, Constants.PAGE_ID_LOCATION, String.valueOf(position));
    }

    public void reportContentShowEvent(SearchProgramBean bean, int position) {
        ReportUtil.addContentShowEvent("", ReportParamUtil.getRadioType(bean.getType()),
                "", String.valueOf(bean.getId()),
                ReportParamUtil.getEventTag(bean.getVip() == 1, bean.getFine() == 1),
                Constants.PAGE_ID_LOCATION, Constants.PAGE_ID_LOCATION, String.valueOf(position));
    }

    /**
     * 上报搜索结果事件
     *
     * @param searchType  搜索方式
     * @param result      搜索结果
     * @param keyword     关键词
     * @param contentType 内容分类
     */
    public void reportSearchResultEvent(String searchType, String result, String keyword, String contentType) {
        ReportUtil.reportSearchResult(keyword, searchType, result, "0", null, contentType);
    }

    public void cancelRequest() {
        mModel.cancelRequest();
    }
}
