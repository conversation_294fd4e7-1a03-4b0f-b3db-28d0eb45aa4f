package com.kaolafm.kradio.common.utils;

import android.graphics.ColorMatrix;
import android.graphics.ColorMatrixColorFilter;
import android.graphics.Paint;

import androidx.annotation.NonNull;

import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.style.ClickableSpan;
import android.view.View;
import android.view.Window;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.ResUtil;

public class CommonUtils {
    private static CommonUtils commonUtils;

    public static CommonUtils getInstance() {
        if (commonUtils == null) {
            synchronized (CommonUtils.class) {
                if (commonUtils == null) {
                    commonUtils = new CommonUtils();
                }
            }
        }
        return commonUtils;
    }

    public SpannableStringBuilder initAgreementTextAndClick(String contentText,
                                                            View.OnClickListener onClickListenerOne, View.OnClickListener onClickListenerTwo) {
        int index_start = contentText.indexOf("《", 0);
        int index_end = contentText.indexOf("》", index_start) + 1;
        int index_start1 = contentText.indexOf("《", index_end);
        int index_end1 = contentText.indexOf("》", index_start1) + 1;

        SpannableStringBuilder spannable = new SpannableStringBuilder(contentText);
        if (index_start > 0 && index_end > 0) {
            spannable.setSpan(new ClickableSpan() {
                                  @Override
                                  public void onClick(@NonNull View widget) {
                                      onClickListenerOne.onClick(widget);
                                  }

                                  @Override
                                  public void updateDrawState(@NonNull TextPaint ds) {
                                      ds.setColor(ResUtil.getColor(R.color.agreement_content_jump_txt));
                                      ds.setUnderlineText(false);
                                  }
                              }, index_start, index_end
                    , Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
//        spannable.setSpan(new ForegroundColorSpan(getResources().getColor(R.color.agreement_content_jump_txt)), index_start, index_end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        if (index_start1 > 0 && index_end1 > 0) {
            spannable.setSpan(new ClickableSpan() {
                                  @Override
                                  public void onClick(@NonNull View widget) {
                                      onClickListenerTwo.onClick(widget);
                                  }

                                  @Override
                                  public void updateDrawState(@NonNull TextPaint ds) {
                                      ds.setColor(ResUtil.getColor(R.color.agreement_content_jump_txt));
                                      ds.setUnderlineText(false);
                                  }
                              }, index_start1, index_end1
                    , Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
//        spannable.setSpan(new ForegroundColorSpan(getResources().getColor(R.color.agreement_content_jump_txt)), index_start1, index_end1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

        return spannable;
    }

    /**
     * app静默模式 窗口对象
     *
     * @param window
     */
    public void initGreyStyle(Window window) {
        if (!Constants.switchGreyStyle || window == null) return;

        Paint paint = new Paint();
        ColorMatrix cm = new ColorMatrix();
        cm.setSaturation(0); // 设置色彩饱和度为0
        paint.setColorFilter(new ColorMatrixColorFilter(cm));
        window.getDecorView().setLayerType(View.LAYER_TYPE_HARDWARE, paint);
    }

    /**
     * app静默模式  view对象
     *
     * @param window
     */
    public void initGreyStyle(View window) {
        if (!Constants.switchGreyStyle || window == null) return;

        Paint paint = new Paint();
        ColorMatrix cm = new ColorMatrix();
        cm.setSaturation(0); // 设置色彩饱和度为0
        paint.setColorFilter(new ColorMatrixColorFilter(cm));
        window.setLayerType(View.LAYER_TYPE_HARDWARE, paint);
    }
}
