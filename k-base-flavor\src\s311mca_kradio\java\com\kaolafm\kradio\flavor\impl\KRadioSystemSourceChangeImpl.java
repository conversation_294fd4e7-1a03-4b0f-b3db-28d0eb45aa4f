package com.kaolafm.kradio.flavor.impl;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioSystemSourceChangeInter;
import com.kaolafm.kradio.util.AudioSourceManager;
import com.kaolafm.opensdk.player.logic.playcontrol.PlayControl;

public class KRadioSystemSourceChangeImpl implements KRadioSystemSourceChangeInter {

    private AudioSourceManager mAudioSourceManager;

    @Override
    public boolean registerSourceChanged(Object... args) {
        if (mAudioSourceManager == null) {
            mAudioSourceManager = new AudioSourceManager(AppDelegate.getInstance().getContext());
            PlayControl.getInstance().setCustomAudioFocus(mAudioSourceManager);
            //mAudioSourceManager.registerSourceChanged();
        }
        return true;
    }

    @Override
    public boolean unregisterSourceChanged(Object... args) {
        if (mAudioSourceManager != null) {
            //mAudioSourceManager.unregisterSourceChanged();
        }
        return true;
    }
}
