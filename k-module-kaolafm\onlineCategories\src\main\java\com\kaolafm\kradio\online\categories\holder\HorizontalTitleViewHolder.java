package com.kaolafm.kradio.online.categories.holder;

import android.content.res.Configuration;
import android.util.Log;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.kaolafm.kradio.common.SubcategoryItemBean;
import com.kaolafm.kradio.common.widget.KradioTextView;
import com.kaolafm.kradio.k_kaolafm.R; 
import com.kaolafm.kradio.lib.utils.ResUtil;
 

/**
 * <AUTHOR>
 * @date 2018/4/25
 */

public class HorizontalTitleViewHolder extends BaseSubcategoryViewHolder {

    private String TAG = "HorizontalTitleViewHolder";
 
    KradioTextView mItemSubcategoryTitle;

    public HorizontalTitleViewHolder(View itemView) {
        super(itemView);
        mItemSubcategoryTitle= view.findViewById(R.id.item_subcategory_title);
    }

    @Override
    public void setupData(SubcategoryItemBean subcategoryItemBean, int position) {

        int orientation = ResUtil.getOrientation();
        LayoutParams layoutParams = itemView.getLayoutParams();
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            layoutParams.width = ResUtil.getDimen(R.dimen.online_subcategory_grid_title_width);
            layoutParams.height = LayoutParams.MATCH_PARENT;
            itemView.setBackground(ResUtil.getDrawable(R.drawable.online_category_item_title_bg));
//            mItemSubcategoryTitle.setMaxEms(1);

            FrameLayout.LayoutParams tvLP = (FrameLayout.LayoutParams) mItemSubcategoryTitle.getLayoutParams();
            tvLP.gravity = Gravity.CENTER_HORIZONTAL;
        } else {
            layoutParams.height = LayoutParams.WRAP_CONTENT;
            layoutParams.width = LayoutParams.WRAP_CONTENT;
            itemView.setBackground(null);
//            mItemSubcategoryTitle.setMaxEms(100);

        }
        int padding = ResUtil.getDimen(R.dimen.online_subcategory_item_title_padding);
        itemView.setPadding(padding, padding, padding, padding);

        itemView.setLayoutParams(layoutParams);
        String title = subcategoryItemBean.getTitle();
        mItemSubcategoryTitle.setGradientColor(ResUtil.getColor(R.color.online_category_item_title_text_start),
                ResUtil.getColor(R.color.online_category_item_title_text_end),
                270);
        mItemSubcategoryTitle.setFontWeight(0.7f);
        mItemSubcategoryTitle.setText(title);
        int size = ResUtil.getDimen(R.dimen.online_subcategory_item_title_text_size);
        mItemSubcategoryTitle.setTextSize(TypedValue.COMPLEX_UNIT_PX, size);
        Log.i(TAG,"title name:"+ title +" textsize:"+size);

    }
}
