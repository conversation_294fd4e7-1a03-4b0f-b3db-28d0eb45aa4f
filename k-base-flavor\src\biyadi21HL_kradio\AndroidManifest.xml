<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.kaolafm.kradio.flavor">

    <uses-permission android:name="android.permission.BYDAUTO_BODYWORK_COMMON" />
    <uses-permission android:name="android.permission.BYDAUTO_BODYWORK_GET" />
    <uses-permission android:name="android.permission.BYDAUTO_INSTRUMENT_COMMON" />
    <uses-permission android:name="android.permission.BYDAUTO_INSTRUMENT_SET" />
    <uses-permission android:name="android.permission.BYDAUTO_AUDIO_GET" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.BYDAUTO_AUDIO_SET" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <application>

        <!--<activity
            android:name="com.kaolafm.auto.home.MainActivity"
            android:configChanges="orientation|screenSize|keyboardHidden|locale|layoutDirection|keyboard|uiMode"
            android:exported="true"
            android:screenOrientation="unspecified"
            android:theme="@style/AppThemeCompat.splash">

            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.MONKEY" />
            </intent-filter>
            <meta-data
                android:name="distractionOptimized"
                android:value="true" />
        </activity>

        <activity-alias
            android:name="com.main.activity.alias"
            android:targetActivity="com.kaolafm.auto.home.HubActivity"
            tools:node="remove" />-->

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.kaolafm.kradio.k_radio_horizontal.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>


        <service
            android:name="com.kaolafm.kradio.service.AutoPlayService"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com.kaolafm.auto.flavor.service.AUTO_PLAY" />
            </intent-filter>
        </service>

        <receiver
            android:name="com.kaolafm.kradio.receiver.BYDAutoPlayReceiver"
            android:exported="true">
            <intent-filter android:priority="1000">
                <action android:name="byd.intent.action.AUTO_PLAY" />
            </intent-filter>
        </receiver>

        <service
            android:name="com.kaolafm.kradio.service.BYDWidgetService"
            android:enabled="true"
            android:exported="true"/>

        <receiver android:name="cmgyunting.vehicleplayer.cnr.YunBydWidget">
            <intent-filter android:priority="1000">
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.kaolafm.auto.home.appExit.action" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/byd_widget_info" />
        </receiver>

        <receiver android:name="com.kaolafm.kradio.receiver.BootBroadcastReceiver">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>

        <receiver android:name="com.kaolafm.kradio.receiver.ShutdownBroadcastReceiver">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.ACTION_SHUTDOWN" />
            </intent-filter>
        </receiver>

        <receiver android:name="com.kaolafm.kradio.flavor.impl.KillBroadcastReceiver">
            <intent-filter android:priority="1000">
                <action android:name="byd.intent.action.KILL_EDOG_CAR" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.kaolafm.kradio.receiver.BYDMediaModeReceiver"
            android:exported="true">
            <intent-filter android:priority="1000">
                <action android:name="byd.intent.action.MEDIA_MODE" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.kaolafm.kradio.receiver.BYDMediaButtonReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="byd.intent.action.MEDIA_BUTTON" />
            </intent-filter>
        </receiver>

    </application>
</manifest>


