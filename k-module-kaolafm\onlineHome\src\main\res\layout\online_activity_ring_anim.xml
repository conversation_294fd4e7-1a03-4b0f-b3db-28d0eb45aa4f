<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal"
    android:background="@drawable/online_bg_home">

    <!-- CPU优化：删除Lottie动画以降低CPU使用率 -->
    <!-- 原有的两个LottieAnimationView已删除 -->
    <RelativeLayout
        android:layout_weight="1"
        android:layout_width="0dp"
        android:layout_height="match_parent">
        <!-- 动画已删除，保留空布局 -->
    </RelativeLayout>

    <RelativeLayout
        android:layout_weight="1"
        android:layout_width="0dp"
        android:layout_height="match_parent">
        <!-- 动画已删除，保留空布局 -->
    </RelativeLayout>

</LinearLayout>