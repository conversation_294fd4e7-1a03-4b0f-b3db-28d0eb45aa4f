package com.kaolafm.kradio.lib.init;

import androidx.annotation.NonNull;

import com.kaolafm.kradio.lib.utils.YTLogUtil;

/**
 * 初始化任务。内部逻辑处理使用，外部不需要关心
 * <AUTHOR>
 * @date 2022-06-30
 */
public class ModelInitTask implements Comparable<ModelInitTask> {

    public int priority;

    public int process;

    public String description;

    public boolean isAsync;

    public ModelInitializable initializer;

    public ModelInitTask(int priority, int process, String description, boolean isAsync, ModelInitializable initializer) {
        YTLogUtil.logStart("ModelInitTask", "ModelInitTask", "priority = " + priority + ", process = " + process + ", description = " + description + ", isAsync = " + isAsync + ", initializer = " + initializer);
        this.priority = priority;
        this.process = process;
        this.description = description;
        this.isAsync = isAsync;
        this.initializer = initializer;
    }

    @Override
    public int compareTo(@NonNull ModelInitTask item) {
        return Integer.compare(this.priority, item.priority);
    }

    public boolean inAllProcess() {
        return process == Process.ALL;
    }

    public boolean inMainProcess() {
        return process == Process.MAIN || inAllProcess();
    }

    public boolean inOtherProcess() {
        return process == Process.OTHER || inAllProcess();
    }
}
