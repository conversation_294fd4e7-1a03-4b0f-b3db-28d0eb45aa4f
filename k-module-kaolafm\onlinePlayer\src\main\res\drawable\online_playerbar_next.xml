<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <selector>
            <item android:drawable="@drawable/online_ic_player_bar_next_pressed" android:state_activated="true" android:state_enabled="true" />
            <item android:drawable="@drawable/online_ic_player_bar_next_invalid" android:state_enabled="false" />
            <item android:drawable="@drawable/online_ic_player_bar_next_invalid" android:state_activated="false" android:state_enabled="true" />
            <item android:drawable="@drawable/online_ic_player_bar_next_pressed" />
        </selector>
    </item>
    <item android:state_pressed="false">
        <selector>
            <item android:drawable="@drawable/online_ic_player_bar_next_normal" android:state_activated="true" android:state_enabled="true" />
            <item android:drawable="@drawable/online_ic_player_bar_next_invalid" android:state_enabled="false" />
            <item android:drawable="@drawable/online_ic_player_bar_next_invalid" android:state_activated="false" android:state_enabled="true" />
            <item android:drawable="@drawable/online_ic_player_bar_next_normal" />
        </selector>
    </item>

</selector>