<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/pc_age_detail_rl"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/pc_slect_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@id/ll_first"
        android:layout_centerInParent="true"
        android:layout_margin="@dimen/y80"
        android:gravity="center"
        android:text="@string/personlityrecommendation_age"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/m28" />

    <LinearLayout
        android:id="@+id/ll_first"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center">

        <TextView
            android:id="@+id/age_detail_one"
            android:layout_width="@dimen/pc_age_detail_item_width"
            android:layout_height="@dimen/pc_age_detail_item_height"
            android:layout_alignTop="@+id/age_three"
            android:background="@drawable/pc_round_bg"
            android:gravity="center"
            android:textColor="@color/pc_text_selector"
            android:textSize="@dimen/text_size5" />

        <TextView
            android:id="@+id/age_detail_two"
            android:layout_width="@dimen/pc_age_detail_item_width"
            android:layout_height="@dimen/pc_age_detail_item_height"
            android:layout_marginLeft="@dimen/y14"
            android:layout_marginRight="@dimen/y7"
            android:background="@drawable/pc_round_bg"
            android:gravity="center"
            android:textColor="@color/pc_text_selector"
            android:textSize="@dimen/text_size5" />

        <TextView
            android:id="@+id/age_detail_three"
            android:layout_width="@dimen/pc_age_detail_item_width"
            android:layout_height="@dimen/pc_age_detail_item_height"
            android:layout_marginLeft="@dimen/y7"
            android:layout_marginRight="@dimen/y14"
            android:background="@drawable/pc_round_bg"
            android:gravity="center"
            android:textColor="@color/pc_text_selector"
            android:textSize="@dimen/text_size5" />

        <TextView
            android:id="@+id/age_detail_four"
            android:layout_width="@dimen/pc_age_detail_item_width"
            android:layout_height="@dimen/pc_age_detail_item_height"
            android:layout_marginRight="@dimen/y14"
            android:background="@drawable/pc_round_bg"
            android:gravity="center"
            android:textColor="@color/pc_text_selector"
            android:textSize="@dimen/text_size5" />

        <TextView
            android:id="@+id/age_detail_five"
            android:layout_width="@dimen/pc_age_detail_item_width"
            android:layout_height="@dimen/pc_age_detail_item_height"
            android:background="@drawable/pc_round_bg"
            android:gravity="center"
            android:textColor="@color/pc_text_selector"
            android:textSize="@dimen/text_size5" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_second"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/ll_first"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/y50"
        android:gravity="center">

        <TextView
            android:id="@+id/age_detail_six"
            android:layout_width="@dimen/pc_age_detail_item_width"
            android:layout_height="@dimen/pc_age_detail_item_height"
            android:layout_alignTop="@+id/age_three"
            android:background="@drawable/pc_round_bg"
            android:gravity="center"
            android:textColor="@color/pc_text_selector"
            android:textSize="@dimen/text_size5" />

        <TextView
            android:id="@+id/age_detail_seven"
            android:layout_width="@dimen/pc_age_detail_item_width"
            android:layout_height="@dimen/pc_age_detail_item_height"
            android:layout_marginLeft="@dimen/y14"
            android:layout_marginRight="@dimen/y7"
            android:background="@drawable/pc_round_bg"
            android:gravity="center"
            android:textColor="@color/pc_text_selector"
            android:textSize="@dimen/text_size5" />

        <TextView
            android:id="@+id/age_detail_eight"
            android:layout_width="@dimen/pc_age_detail_item_width"
            android:layout_height="@dimen/pc_age_detail_item_height"
            android:layout_marginLeft="@dimen/y7"
            android:layout_marginRight="@dimen/y14"
            android:background="@drawable/pc_round_bg"
            android:gravity="center"
            android:textColor="@color/pc_text_selector"
            android:textSize="@dimen/text_size5" />

        <TextView
            android:id="@+id/age_detail_nice"
            android:layout_width="@dimen/pc_age_detail_item_width"
            android:layout_height="@dimen/pc_age_detail_item_height"
            android:layout_marginRight="@dimen/y14"
            android:background="@drawable/pc_round_bg"
            android:gravity="center"
            android:textColor="@color/pc_text_selector"
            android:textSize="@dimen/text_size5" />

        <TextView
            android:id="@+id/age_detail_ten"
            android:layout_width="@dimen/pc_age_detail_item_width"
            android:layout_height="@dimen/pc_age_detail_item_height"
            android:background="@drawable/pc_round_bg"
            android:gravity="center"
            android:textColor="@color/pc_text_selector"
            android:textSize="@dimen/text_size5" />
    </LinearLayout>

    <ImageView
        android:id="@+id/age_detail_progress_control"
        android:layout_width="@dimen/x100"
        android:layout_height="@dimen/m7"
        android:layout_below="@id/ll_second"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/y65"
        android:background="@drawable/pc_progress_select"
        android:gravity="center" />

    <TextView
        android:id="@+id/age_detail_next"
        android:layout_width="@dimen/family_recommend_button_width"
        android:layout_height="@dimen/family_recommend_button_height"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="@dimen/y60"
        android:background="@drawable/pc_next_bg"
        android:gravity="center"
        android:text="@string/next_step_str"
        android:textColor="@color/pc_next_text_selector"
        android:textSize="@dimen/text_size8" />

</RelativeLayout>