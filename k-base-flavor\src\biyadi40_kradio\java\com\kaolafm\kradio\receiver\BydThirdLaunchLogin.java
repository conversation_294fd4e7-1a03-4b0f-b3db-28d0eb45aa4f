package com.kaolafm.kradio.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import android.util.Log;

import com.byd.diLinkAccount.DiLinkAccountService;
import com.kaolafm.kradio.k_kaolafm.home.LoginManager;
import com.kaolafm.kradio.k_kaolafm.home.gallery.PageJumper;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;

public class BydThirdLaunchLogin extends BroadcastReceiver {
    private final String ACCOUNT_STATE_ACTION = "com.byd.action.byd_account_go_tripart_login_page";

    private static final String TAG = "BydThirdLaunchLogin";

    @Override
    public void onReceive(Context context, Intent intent) {
        if (ACCOUNT_STATE_ACTION.equals(intent.getAction())) {
            Log.i(TAG, "onReceive");
            startAppToFront(context);
            startLoginPage();
        }
    }

    private void startLoginPage() {
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                LoginManager.getInstance().registerLoginListener(
                        new LoginManager.LoginListener() {
                            @Override
                            public void onLoginStateChange(int cp, boolean isLogin) {
                                if (isLogin) {
                                    AccountInterworkInter inter = ClazzImplUtil.getInter("AccountInterworkImpl");
                                    if(inter != null && inter.isOpenThirdPartyAccount()){
                                        Log.i(TAG,"onChange, setAccountBind,action=5");
                                        inter.setAccountBind(5);
                                    }

                                }
                                LoginManager.getInstance().unregisterLoginListener(this);
                            }

                            @Override
                            public void onCancel() {
                                LoginManager.getInstance().unregisterLoginListener(this);
                            }
                        }
                );
                ThirdAccountLoginHelp.getInstance().setOnChange(true);
                PageJumper.getInstance().jumpToKRadioLoginPage();
            }
        }, 1000);
    }

    private void startAppToFront(Context context) {
        Log.i(TAG, "startActivity");
        boolean isForeground = AppDelegate.getInstance().isAppForeground();
        if (!isForeground) {
            String packageName = context.getPackageName();
            Log.i(TAG, "background:" + packageName);
            Intent startIntent = context.getPackageManager().getLaunchIntentForPackage(packageName);
            if (startIntent != null) {
                Log.i(TAG, "startActivity: nonnull");
                context.startActivity(startIntent);
            }
        }
    }
}
