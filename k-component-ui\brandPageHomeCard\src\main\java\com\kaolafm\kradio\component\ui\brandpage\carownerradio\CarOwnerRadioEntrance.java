package com.kaolafm.kradio.component.ui.brandpage.carownerradio;

import android.animation.Animator;
import android.animation.AnimatorInflater;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.FloatEvaluator;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import android.os.Looper;
import androidx.constraintlayout.widget.ConstraintLayout;
import android.text.Html;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.animation.LinearInterpolator;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.kradio.component.ui.R;
import com.kaolafm.kradio.config.ConfigSettingManager;
import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.component.ui.base.utils.ComponentUtils;
import com.kaolafm.kradio.component.ui.base.view.KradioTextView;

import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.UrlUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.lib.widget.tab.LinearGradientFontSpan;
import com.kaolafm.opensdk.api.operation.model.column.ColumnContent;
import com.kaolafm.opensdk.api.operation.model.column.TopicDetailColumnMember;

/**
 * 首页-推荐页面配置的车主电台组件
 *
 * <AUTHOR> shiqian
 * @date 2023-02-28
 */
public class CarOwnerRadioEntrance extends ConstraintLayout {
    private static final String TAG = "CarOwnerRadioEntrance";

    private ConstraintLayout radioCard, radioCardFront, radioCardBack,
            carOwnerRadioCardCenter;//光圈内部的正反面组件
    private KradioTextView radioName, radioInBtn;  //“进入听友社区”按钮,只有品牌电台页有该组件
    private ImageView radioBg, radioLogo, radioCar, radioFrontCover, radioBackCover;
    private TextView radioTitle, radioSubTitle, radioTag;

    private boolean mIsShowBack;
    private AnimatorSet mRightOutSet, mLeftInSet,//翻转动画
            mScaleSet,  // 点击时“进入听友社区”按钮的缩放和渐隐动画动画,在品牌电台页时为null
            mFadeOutSet,    //点击时光圈、内部内容整体的渐隐动画
            enterAnimatorSet,    //进入品牌电台页面的动画
            mActivityFadeOutSet;    //跳转到品牌电台页面时的包含了mScaleSet和mFadeOutSet的动画
    private OnClickListener mOnClickListener;

    private Handler mHandler = new Handler(Looper.getMainLooper()); // 全局变量
    private Runnable mTimeCounterRunnable = new Runnable() {
        @Override
        public void run() {//在此添加需轮寻的接口
            flipCard();
            mHandler.postDelayed(mTimeCounterRunnable, 10 * 1000);
        }
    };
    //广播事件：通知该组件进入动画完成
    public static final String BROADCAST_RECEIVER_ACTION_CAR_ENTER_ANIMATION_COMPLETE = "BROADCAST_RECEIVER_ACTION_CAR_ENTER_ANIMATION_COMPLETE";

    private HomeCell brandPageCell;

    public CarOwnerRadioEntrance(Context context) {
        this(context, null);
    }

    public CarOwnerRadioEntrance(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CarOwnerRadioEntrance(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs, defStyleAttr);
    }

    @Override
    protected void onDetachedFromWindow() {
        cancelAnimators();
        super.onDetachedFromWindow();
        //关闭定时任务
        mHandler.removeCallbacks(mTimeCounterRunnable);
    }

    /**
     * 取消动画
     */
    private void cancelAnimators() {
        if (mActivityFadeOutSet != null && mActivityFadeOutSet.isRunning())
            mActivityFadeOutSet.cancel();
        if (mRightOutSet != null && mRightOutSet.isRunning())
            mRightOutSet.cancel();
        if (mLeftInSet != null && mLeftInSet.isRunning())
            mLeftInSet.cancel();
        if (enterAnimatorSet != null && enterAnimatorSet.isRunning())
            enterAnimatorSet.cancel();
    }

    private void init(Context context, AttributeSet attrs, int defStyleAttr) {
        LayoutInflater.from(context).inflate(R.layout.car_owner_radio_entrance, this);
        radioInBtn = findViewById(R.id.radio_in_btn);

        radioBg = findViewById(R.id.radio_bg);
        radioFrontCover = findViewById(R.id.radio_front_cover);
        radioName = findViewById(R.id.radio_name);
        radioLogo = findViewById(R.id.radio_logo);

        carOwnerRadioCardCenter = findViewById(R.id.car_owner_radio_card_center);

        radioBackCover = findViewById(R.id.radio_back_cover);
        radioTitle = findViewById(R.id.radio_title);
        radioSubTitle = findViewById(R.id.radio_sub_title);
        radioTag = findViewById(R.id.radio_tag);


        radioCard = findViewById(R.id.car_owner_radio_card);
        radioCardFront = findViewById(R.id.car_owner_radio_card_front);
        radioCardBack = findViewById(R.id.car_owner_radio_card_back);
        radioCar = findViewById(R.id.radio_car);


        setAnimators();
        setCameraDistance();

        if (mIsShowBack) {
            radioCardFront.setAlpha(0);
            radioCardBack.setAlpha(1);
        } else {
            radioCardBack.setAlpha(0);
            radioCardFront.setAlpha(1);
        }
        mHandler.postDelayed(mTimeCounterRunnable, 10 * 1000);

    }

    public void updateDate(HomeCell homeCell) {
        this.brandPageCell = homeCell;
        if (brandPageCell.getContentList() != null) {
            if (brandPageCell.getContentList().size() > 0) {
                // 设置车主电台组件正面数据
                radioName.setText(getRadiusGradientSpan("-" + brandPageCell.getName() + "-"));
                radioName.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        //这里需要延时在重新设置跑马灯效果，否则会获取焦点失败
                        radioName.setEllipsize(TextUtils.TruncateAt.MARQUEE);
                        radioName.setSingleLine(true);
                        radioName.setSelected(true);
                        radioName.setFocusable(true);
                        radioName.setFocusableInTouchMode(true);
                        radioName.requestFocus();
                    }
                },1000);

                String bgUrl = UrlUtil.getCardBgUrl(brandPageCell.getImageFiles());
                ImageLoader.getInstance().displayImage(getContext(), bgUrl, radioFrontCover);
                String logoUrl = UrlUtil.getCardLogoUrl(brandPageCell.getImageFiles());
                ImageLoader.getInstance().displayCircleImage(getContext(), logoUrl, radioLogo);
                String urlCar = UrlUtil.getCarPicUrl(brandPageCell.getImageFiles());
                ImageLoader.getInstance().displayImage(getContext(), urlCar, radioCar);

                // 根据不同成员类型解析并设置组件背面要展示的数据
                ColumnContent columnContent = brandPageCell.getContentList().get(0);
                if (columnContent == null) {
                    return;
                }
                String urlBg = UrlUtil.getCardPicUrl(columnContent.getImageFiles());
                ImageLoader.getInstance().displayCircleImage(getContext(), urlBg, radioBackCover);
                if(TextUtils.isEmpty(columnContent.getTag())){
                    radioTag.setVisibility(GONE);
                }else {
                    radioTag.setVisibility(VISIBLE);
                    radioTag.setText(columnContent.getTag() + "");
                }
                ComponentUtils.getInstance().setTagStyle(radioTag, columnContent.getTagColor());

                if(columnContent instanceof TopicDetailColumnMember){
                    radioTitle.setText("#"+columnContent.getTitle()+"");
                    String numText = ComponentUtils.getInstance().formatNumber(brandPageCell.getContentList().get(0).getUserCount(),1)
                            + "<font size=" + ResUtil.getDimen(R.dimen.m18) + ">人参与</font> " +
                            ComponentUtils.getInstance().formatNumber(brandPageCell.getContentList().get(0).getReadCount(),1) +
                            "<font size=" + ResUtil.getDimen(R.dimen.m18) + ">阅读量</font>";
                    radioSubTitle.setText(Html.fromHtml(numText));
                }else {
                    radioTitle.setText(columnContent.getTitle()+"");
                    radioSubTitle.setText(columnContent.getSubtitle()+"");
                }


            }
        }
    }

    /**
     * 生成渐变文字
     *
     * @param string
     * @return
     */
    public SpannableStringBuilder getRadiusGradientSpan(String string) {
        SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder(string);
        LinearGradientFontSpan span = new LinearGradientFontSpan(ResUtil.getColor(R.color.car_owner_text_color_start)
                , ResUtil.getColor(R.color.car_owner_text_color_end));
        spannableStringBuilder.setSpan(span, 0, spannableStringBuilder.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spannableStringBuilder;

    }

    @Override
    public void setOnClickListener(OnClickListener onClickListener) {
        Log.e(TAG, "");
        mOnClickListener = onClickListener;

        super.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mOnClickListener != null) {
                    ConfigSettingManager.getInstance().isPlayAnimation(new ConfigSettingManager.OnResultCallback<Boolean>() {
                        @Override
                        public void onResult(Boolean aBoolean) {
                            transitionScene(aBoolean);
                        }
                    });
                }
            }
        });
    }

    /**
     * 转场
     *
     * @param isPlayAnimation
     */
    private void transitionScene(Boolean isPlayAnimation) {
        if (!isPlayAnimation) {
            mOnClickListener.onClick(CarOwnerRadioEntrance.this);
            return;
        }
        mActivityFadeOutSet = new AnimatorSet();
        mActivityFadeOutSet.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationStart(Animator animation) {
                super.onAnimationStart(animation);
                CarOwnerRadioEntrance.this.setClickable(false);
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
                mOnClickListener.onClick(CarOwnerRadioEntrance.this);
                CarOwnerRadioEntrance.this.setClickable(true);
            }
        });
        mActivityFadeOutSet.play(mScaleSet).before(mFadeOutSet);
        mActivityFadeOutSet.start();
    }


    // 开启动画，翻转卡片
    private void flipCard() {
        ConfigSettingManager.getInstance().isPlayAnimation(isPlayAnimation -> {
            if (!isPlayAnimation) {
                if (!mIsShowBack) {
                    radioCardFront.setAlpha(0);
                    radioCardBack.setAlpha(1);
                    mIsShowBack = true;
                } else {
                    radioCardBack.setAlpha(0);
                    radioCardFront.setAlpha(1);
                    mIsShowBack = false;
                }
                return;
            }
            if (mRightOutSet == null || mLeftInSet == null) initFlipAnimation();
            // 正面朝上
            if (!mIsShowBack) {
                mRightOutSet.setTarget(radioCardFront);
                mLeftInSet.setTarget(radioCardBack);
                mRightOutSet.start();
                mLeftInSet.start();
                mIsShowBack = true;
            } else { // 背面朝上
                mRightOutSet.setTarget(radioCardBack);
                mLeftInSet.setTarget(radioCardFront);
                mRightOutSet.start();
                mLeftInSet.start();
                mIsShowBack = false;
            }
        });
    }

    //设置动画
    private void setAnimators() {
        ConfigSettingManager.getInstance().isPlayAnimation(isPlayAnimation -> {
            if (!isPlayAnimation) return;
            initFlipAnimation();
            initButtonAnimations();
            initAllFadeOutAnimation();
        });
    }

    // 设置翻转动画
    private void initFlipAnimation() {
        mRightOutSet = (AnimatorSet) AnimatorInflater.loadAnimator(getContext(), R.animator.car_owner_radio_anim_out);
        mLeftInSet = (AnimatorSet) AnimatorInflater.loadAnimator(getContext(), R.animator.car_owner_radio_anim_in);
    }

    //初始化按钮缩放渐隐动画
    private void initButtonAnimations() {
        if (radioInBtn != null) {
            mScaleSet = (AnimatorSet) AnimatorInflater.loadAnimator(getContext(), R.animator.car_owner_radio_btn_anim);
            mScaleSet.setTarget(radioInBtn);
        }
    }

    //初始化即将转场时的整体渐隐动画
    private void initAllFadeOutAnimation() {
        ObjectAnimator bgFadeOut = ObjectAnimator.ofFloat(radioBg, "alpha", 1f, 0.5f).setDuration(500);
        ObjectAnimator carOwnerRadioCardCenterFadeOut = ObjectAnimator.ofFloat(carOwnerRadioCardCenter, "alpha", 1f, 0.5f).setDuration(500);
        mFadeOutSet = new AnimatorSet();
        mFadeOutSet.playTogether(bgFadeOut, carOwnerRadioCardCenterFadeOut);
    }

    // 改变视角距离, 贴近屏幕
    private void setCameraDistance() {
        int distance = 16000;
        float scale = getResources().getDisplayMetrics().density * distance;
        radioCardFront.setCameraDistance(scale);
        radioCardBack.setCameraDistance(scale);
    }

    public void runAnimationOnEnter(boolean showAnimator, boolean showBrandEnterButton) {
        if (showAnimator) {
            if (!showBrandEnterButton) {
                //从首页进入到品牌电台页
//                ViewUtil.setViewVisibility(radioInBtn, View.GONE);
                ObjectAnimator translationY = ObjectAnimator.ofObject(radioCar, "translationY", new FloatEvaluator(), ResUtil.getDimen(R.dimen.m236), 0).setDuration(3000);
                translationY.setDuration(1000);
                AnimatorSet scaleBg = (AnimatorSet) AnimatorInflater.loadAnimator(getContext(), R.animator.car_owner_radio_scale_in);
                scaleBg.setTarget(radioBg);
                AnimatorSet scaleCardCenter = (AnimatorSet) AnimatorInflater.loadAnimator(getContext(), R.animator.car_owner_radio_scale_in);
                scaleCardCenter.setTarget(carOwnerRadioCardCenter);
                if (enterAnimatorSet != null && enterAnimatorSet.isRunning()) {
                    enterAnimatorSet.cancel();
                }
                enterAnimatorSet = new AnimatorSet();
                enterAnimatorSet.setInterpolator(new LinearInterpolator());
                enterAnimatorSet.playTogether(translationY, scaleBg, scaleCardCenter);
                enterAnimatorSet.addListener(new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationEnd(Animator animation) {
                        super.onAnimationEnd(animation);
                        getContext().sendBroadcast(new Intent(BROADCAST_RECEIVER_ACTION_CAR_ENTER_ANIMATION_COMPLETE));
                    }
                });
                enterAnimatorSet.start();
                return;
            } else {
                //从品牌电台返回首页
            }
        } else {
            //刚进入到首页
        }
        if (radioInBtn != null)
            radioInBtn.setAlpha(1f);
        if (radioBg != null)
            radioBg.setAlpha(1f);
        if (carOwnerRadioCardCenter != null)
            carOwnerRadioCardCenter.setAlpha(1f);
        radioCar.setTranslationY(0);
        getContext().sendBroadcast(new Intent(BROADCAST_RECEIVER_ACTION_CAR_ENTER_ANIMATION_COMPLETE));
    }
}
