package com.kaolafm.kradio.component.ui.bigcard;

import androidx.annotation.NonNull;

import android.app.Activity;
import android.content.Context;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.view.View;
import android.view.ViewTreeObserver;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;

import com.kaolafm.kradio.component.ui.R;
import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.component.ui.base.utils.ComponentUtils;
import com.kaolafm.kradio.component.ui.base.view.OvalImageView;
import com.kaolafm.kradio.component.ui.base.view.RateView;
import com.kaolafm.kradio.component.ui.base.CellBinder;
import com.kaolafm.kradio.component.ui.base.ItemClickSupport;
import com.kaolafm.kradio.component.ui.base.utils.VipCornerUtil;
import com.kaolafm.kradio.component.ui.base.utils.RoundBackGroundColorSpan;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.common.ResType;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.UrlUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.opensdk.api.operation.model.column.ActivityDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.AlbumDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.PageRedirectionColumnMember;

/**
 * 首页组件大卡片
 */
public class ComponentBigCardCell extends HomeCell implements CellBinder<View, ComponentBigCardCell>, ItemClickSupport {

    OvalImageView card_bg_iv;
    OvalImageView card_pic_iv;
    ImageView card_tag_iv;
    ImageView card_play_iv;
    TextView card_title_tv;
    RateView card_layout_playing;

    private static final String TAG = "ComponentBigCardCell";

    @Override
    public void mountView(@NonNull ComponentBigCardCell data, @NonNull View view, int position) {
        if (data.getContentList() == null || data.getContentList().size() == 0) {
            return;
        }
        card_bg_iv = view.findViewById(R.id.card_bg_iv);
        card_pic_iv = view.findViewById(R.id.card_pic_iv);
        card_tag_iv = view.findViewById(R.id.card_tag_iv);
        card_play_iv = view.findViewById(R.id.card_play_iv);
        card_title_tv = view.findViewById(R.id.card_title_tv);
        card_layout_playing = view.findViewById(R.id.card_layout_playing);
        if (!TextUtils.isEmpty(data.getContentList().get(0).getTitle())) {
            card_title_tv.setText(data.getContentList().get(0).getTitle());
        } else if(!TextUtils.isEmpty(data.getContentList().get(0).getProgramDesc())){
            card_title_tv.setText(data.getContentList().get(0).getProgramDesc());
        }else{
            card_title_tv.setText(data.getContentList().get(0).getProgramTitle());
        }
//        if (TextUtils.isEmpty(data.getContentList().get(0).getTag())) {
//            card_title_tv.setText(data.getContentList().get(0).getTitle());
//        } else {
//            String name = StringUtil.getMaxSubstring(data.getContentList().get(0).getTag(), 9) + data.getContentList().get(0).getTitle();
//            int end = 9;
//            if (data.getContentList().get(0).getTag().length() < end) {
//                end = data.getContentList().get(0).getTag().length();
//            }
//            initTitle(name, end);
//        }
        card_bg_iv.setBackground(ResUtil.getDrawable(R.drawable.component_card_bg));
        if (!TextUtils.isEmpty(UrlUtil.getCardBgUrl(data.getContentList().get(0).getImageFiles())))
            ImageLoader.getInstance().displayImage(card_bg_iv.getContext(),
                    UrlUtil.getCardBgUrl(data.getContentList().get(0).getImageFiles()), card_bg_iv);

        String url = UrlUtil.getCardPicUrl(data.getContentList().get(0).getImageFiles());
        if (!TextUtils.isEmpty(url)) {
            card_pic_iv.setVisibility(View.VISIBLE);
            card_tag_iv.setVisibility(View.VISIBLE);
            ImageLoader.getInstance().displayImage(view.getContext(),
                    url, card_pic_iv);

            if (data.getContentList().get(0).getResType() == ResType.TV_TYPE
                    || data.getContentList().get(0).getResType() == ResType.BROADCAST_TYPE) {
                card_tag_iv.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_icon_live_class));
            } else if (data.getContentList().get(0).getResType() == ResType.LIVE_TYPE) {
                if (data.getContentList().get(0).getLiveStatus() == 0 || data.getContentList().get(0).getLiveStatus() == 6) {
                    card_tag_iv.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_live_icon_reday));
                } else if (data.getContentList().get(0).getLiveStatus() == 1) {
                    card_tag_iv.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_live_icon_playing));
                }
            } else if (data.getContentList().get(0).getResType() == com.kaolafm.opensdk.ResType.TYPE_VIDEO_AUDIO ||
                    data.getContentList().get(0).getResType() == com.kaolafm.opensdk.ResType.TYPE_VIDEO_ALBUM) {
                card_tag_iv.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_icon_video));
            } else {
                //vip or 精品
//                VipCornerUtil.setVipCorner(card_tag_iv, data.getContentList().get(0).getVip(), data.getContentList().get(0).getFine());
                if (data.contentList.get(0) instanceof AlbumDetailColumnMember) {
                    AlbumDetailColumnMember albumDetailColumnMember = (AlbumDetailColumnMember) data.contentList.get(0);
                    VipCornerUtil.setVipCorner(card_tag_iv, albumDetailColumnMember.getVip(), albumDetailColumnMember.getFine(), false);
                } else {
                    card_tag_iv.setVisibility(View.GONE);
                }
            }
//            if (data.getContentList().get(0).getResType() == ResType.LIVE_TYPE
//                    || data.getContentList().get(0).getResType() == ResType.BROADCAST_TYPE) {
//                card_tag_iv.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_icon_live_class));
//            } else if (data.getContentList().get(0).getResType() == ResType.TV_TYPE) {
//                if (data.getContentList().get(0).getLiveStatus()==0){
//                    card_tag_iv.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_live_icon_reday));
//                }else if (data.getContentList().get(0).getLiveStatus()==1){
//                    card_tag_iv.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_live_icon_playing));
//                }
//            } else {
//                //vip or 精品
//                VipCornerUtil.setVipCorner(card_tag_iv, data.getContentList().get(0).getVip(), data.getContentList().get(0).getFine());
//            }

            boolean notShowPlaying = data.getContentList().get(0) instanceof PageRedirectionColumnMember
                || data.getContentList().get(0) instanceof ActivityDetailColumnMember;
            if (!notShowPlaying) {
                card_layout_playing.setVisibility(data.selected
                        && ComponentUtils.getInstance().getCardHomePlayIdIsPlaying(data.contentList.get(0).getId(), data.contentList.get(0).getCanPlay()) ?
                        View.VISIBLE : View.GONE);
                card_play_iv.setVisibility(card_layout_playing.getVisibility() == View.GONE ? View.VISIBLE : View.GONE);
            } else {
                card_layout_playing.setVisibility(View.GONE);
                card_play_iv.setVisibility(View.GONE);
            }
        } else {
            card_pic_iv.setVisibility(View.INVISIBLE);
            card_tag_iv.setVisibility(View.GONE);
            card_play_iv.setVisibility(View.GONE);
            card_layout_playing.setVisibility(View.GONE);
        }
        view.setContentDescription(data.getContentList().get(0).getTitle());
        view.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onViewClickListener != null) {
                    v.setTag(0);
                    onViewClickListener.onViewClick(v, getPositionInParent());
                }
            }
        });

    }

    private void initTitle(String title, int end) {
        // 注意:OnGlobalLayoutListener可能会被多次触发，因此在得到了高度之后，要将OnGlobalLayoutListener注销掉。
        ViewTreeObserver vto2 = card_title_tv.getViewTreeObserver();
        vto2.addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                TextPaint mTextPaint = card_title_tv.getPaint();
                mTextPaint.setTextSize(card_title_tv.getTextSize());
                int mTextViewWidth = (int) mTextPaint.measureText(title);
                SpannableString spannedString = new SpannableString(title);
                spannedString.setSpan(new RoundBackGroundColorSpan(ResUtil.getColor(R.color.home_card_big_tag_text_color)
                                , ResUtil.getColor(R.color.home_card_big_tag_text_color), mTextViewWidth > card_title_tv.getWidth()),
                        0, end, SpannableString.SPAN_COMPOSING);
                spannedString.setSpan(new AbsoluteSizeSpan(ResUtil.getDimen(R.dimen.m20)), 0, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                card_title_tv.setText(spannedString);
                //移除观察者
                card_title_tv.getViewTreeObserver().removeOnGlobalLayoutListener(this);
            }
        });
    }

    @Override
    public int getItemType() {
        return R.layout.component_big_card_layout;
    }

    private BaseHolder.OnViewClickListener onViewClickListener;

    @Override
    public void setOnItemClickListener(BaseHolder.OnViewClickListener listener) {
        onViewClickListener = listener;
    }

    @Override
    public void release() {
        super.release();
        // CPU优化：安全地清理Glide图片资源
        if (card_pic_iv != null) {
            Context context = card_pic_iv.getContext();
            if (context != null && !isContextDestroyed(context)) {
                Logger.i(TAG, "清理card_pic_iv图片资源");
                Glide.with(context).clear(card_pic_iv);
            }
        }
    }

    /**
     * 检查Context是否已被销毁
     */
    private boolean isContextDestroyed(Context context) {
        if (context instanceof Activity) {
            return ((Activity) context).isDestroyed();
        }
        return false;
    }
}
