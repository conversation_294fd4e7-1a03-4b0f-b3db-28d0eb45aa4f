package com.kaolafm.kradio.component.ui.base.utils;

import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.text.TextUtils;
import android.widget.TextView;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.opensdk.player.logic.PlayerManager;

public class ComponentUtils {
    private static ComponentUtils componentUtils;

    public static ComponentUtils getInstance() {
        if (componentUtils == null) {
            synchronized (ComponentUtils.class) {
                if (componentUtils == null) {
                    componentUtils = new ComponentUtils();
                }
            }
        }
        return componentUtils;
    }

    /**
     * 将数字格式化成字符串，规则：小于10000时保持原样，否则以万为单位并保留2位小数
     *
     * @param count
     * @param decimalPlaces
     * @return
     */
    public String formatNumber(long count, int decimalPlaces) {
        if (count < 10000) return String.valueOf(count);
        return String.format("%." + decimalPlaces + "fw", count / 10_000f);
    }

    public String getReportTag(String freq, int vip) {
        String tag = "无";

        if (!TextUtils.isEmpty(freq)
                && freq.equals("1")) {
            tag = "精品";
        } else if (vip == 1) {
            tag = "VIP";
        }

        return tag;
    }

    public int getContentAction(String desUrl, int canPlay) {
        int action = -1;
        if (!TextUtils.isEmpty(desUrl)) {
            //有跳转地址为跳转
            action = 1;
        }
        if (canPlay == 1) {
            //可播放资源
            if (action > -1) {
                //既是跳转也是播放
                action = 3;
            } else {
                //播放类型
                action = 2;
            }
        }
        return action == -1 ? 1 : action;
    }

    public String getComponentReportCardId(String cardType) {
//        1  上2下1组件
//        2  轮播组件
//        3  上2下3组件
//        4  上1下1组件
//        5  单内容大卡组件
//        6  活动类型组件
//        7  品牌入口组件
//        10    圆形组件
//        11    话题大卡组件
//        12    话题小卡组件
//        13    品牌主页大卡
//        14    品牌主页 1+1
//        15    车主活动类型组件
        String cardId = "0";
        switch (cardType) {
            case "1":
                cardId = "1";
                break;
            case "2":
                cardId = "4";
                break;
            case "3":
                cardId = "5";
                break;
            case "4":
                cardId = "6";
                break;
            case "5":
                cardId = "7";
                break;
            case "6":
            case "15":
                cardId = "23";
                break;
            case "7":
                cardId = "11";
                break;
            case "10":
                cardId = "12";
                break;
            case "11":
                cardId = "15";
                break;
            case "12":
                cardId = "16";
                break;
            case "13":
                cardId = "13";
                break;
            case "14":
                cardId = "14";
                break;
        }
        return cardId;
    }

    /**
     * 判断当前播放的内容是不是首页卡片里的
     *
     * @param itemId
     * @return
     */
    public boolean getCardHomePlayIdIsPlaying(String itemId, int canPlay) {
        if (canPlay != 1) {
            //不可播放状态
            return false;
        }
        long audioId = PlayerManager.getInstance().getCurPlayItem().getAudioId();
        String radioId = PlayerManager.getInstance().getCurPlayItem().getRadioId();
        String albumId = PlayerManager.getInstance().getCurPlayItem().getAlbumId();

        if (itemId.equals(audioId + "")) {
            return true;
        }
        if (itemId.equals(albumId)) {
            return true;
        }
        if (itemId.equals(radioId)) {
            return true;
        }
        return false;

    }

    /**
     * 判断当前播放的内容是不是首页卡片里的
     *
     * @param itemId
     * @return
     */
    public boolean getCardHomePlayIdIsPlaying(String itemId) {

        long audioId = PlayerManager.getInstance().getCurPlayItem().getAudioId();
        String radioId = PlayerManager.getInstance().getCurPlayItem().getRadioId();
        String albumId = PlayerManager.getInstance().getCurPlayItem().getAlbumId();

        if (itemId.equals(audioId + "")) {
            return true;
        }
        if (itemId.equals(albumId)) {
            return true;
        }
        if (itemId.equals(radioId)) {
            return true;
        }
        return false;
    }

    public void setTagStyle(TextView textView, String color) {
        try {
            GradientDrawable gradientDrawable = (GradientDrawable) textView.getBackground();
            gradientDrawable.setColor(Color.parseColor(color));
        } catch (Exception e) {
            Logger.e("ComponentUtils",e.getMessage());
        }
    }
}
