package com.kaolafm.kradio.brand.mvp;

import com.kaolafm.kradio.lib.base.mvp.BaseModel;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.topic.TopicRequest;
import com.kaolafm.opensdk.api.topic.model.OperationResponse;
import com.kaolafm.opensdk.api.topic.model.TopicDetail;
import com.kaolafm.opensdk.api.topic.model.TopicPosts;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.trello.rxlifecycle3.LifecycleTransformer;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-03-01
 */
public class TopicModel extends BaseModel {

    private TopicRequest mTopicRequest;
    private final LifecycleTransformer mLifecycleTransformer;

    public TopicModel(LifecycleTransformer lifecycleTransformer) {
        mLifecycleTransformer = lifecycleTransformer;
        mTopicRequest = new TopicRequest();
        if (mLifecycleTransformer != null) {
            mTopicRequest.bindLifecycle(lifecycleTransformer).setTag(this.toString());
        }

    }

    /**
     * 获取话题详情
     *
     * @param topicId
     * @param callback
     */
    public void getTopicDetail(long topicId, HttpCallback<TopicDetail> callback) {
        mTopicRequest.getTopicDetail(topicId, callback);
    }

    /**
     * 加载帖子列表
     *
     * @param topicId
     * @param orderWay
     * @param pageNum
     * @param pageSize
     * @param callback
     */
    public void getPostsList(long topicId, int orderWay, int pageNum, int pageSize, HttpCallback<BasePageResult<List<TopicPosts>>> callback) {
        mTopicRequest.getTopicPostsList(topicId, orderWay, pageNum, pageSize, callback);
    }

    /**
     * 点赞、取消点赞帖子
     *
     * @param postsId
     * @param type
     * @param callback
     */
    public void operatePostsLike(long postsId, int type, HttpCallback<OperationResponse> callback) {
        mTopicRequest.operatePosts(postsId, type, callback);
    }

    /**
     * 发布帖子
     *
     * @param topicId
     * @param content
     * @param callback
     */
    public void publishPosts(long topicId, String content, HttpCallback<OperationResponse> callback) {
        mTopicRequest.publishPosts(topicId, content, callback);
    }

    @Override
    public void destroy() {

    }
}
