package com.kaolafm.kradio.lib.utils;



public class ChannelSetingUtil {

    /**
     *  判断channel 在某个渠道是否有实现
     * @param channelName
     * @return
     */
    public static boolean isChannelSettingEmpty(String channelName) {

        if (ClazzImplUtil.getInter(channelName) != null) {
            return true;
        }
        return false;
    }

    /**
     *  获取渠道的具体实现，调用其中的方法
     * @param channelName
     * @return
     */
    public static <T>Object  getChannelImpl(String channelName) {
        return ClazzImplUtil.getInter(channelName);
    }

}
