package com.kaolafm.kradio.flavor.impl;

public class SpeechResponse {

    /**
     * messageType : REQUEST
     * focus : video
     * needResponse : YES
     * requestCode : 10001
     * semantic : {"service":"VIDEO_CONTROL","operation":"OPEN","mediaSource":"收藏记录","playbackSource":"爱奇艺"}
     * version : v1.0
     * operationApp : speech
     */

    private String messageType;
    private String focus;
    private String needResponse;
    private String requestCode;
    private SemanticBean semantic;
    private String version;
    private String operationApp;

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public String getFocus() {
        return focus;
    }

    public void setFocus(String focus) {
        this.focus = focus;
    }

    public String getNeedResponse() {
        return needResponse;
    }

    public void setNeedResponse(String needResponse) {
        this.needResponse = needResponse;
    }

    public String getRequestCode() {
        return requestCode;
    }

    public void setRequestCode(String requestCode) {
        this.requestCode = requestCode;
    }

    public SemanticBean getSemantic() {
        return semantic;
    }

    public void setSemantic(SemanticBean semantic) {
        this.semantic = semantic;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getOperationApp() {
        return operationApp;
    }

    public void setOperationApp(String operationApp) {
        this.operationApp = operationApp;
    }

    public static class SemanticBean {
        /**
         * service : VIDEO_CONTROL
         * operation : OPEN
         * mediaSource : 收藏记录
         * playbackSource : 爱奇艺
         * name: 中国好声音
         * rankValue: ++1, 1, max
         * season: 2
         * episode: 2
         * tags: 视频，电影
         * label: 热播
         * minute: 3min
         * second: 20s
         */

        public String operation;
        public String presenter;
        public String program;
        public String label;
        public String category;
        public String chapter;
        public String datetime;
        public String source;
        public String tags;
        public String insType;

        @Override
        public String toString() {
            return "SemanticBean{" +
                    "operation='" + operation + '\'' +
                    ", presenter='" + presenter + '\'' +
                    ", program='" + program + '\'' +
                    ", label='" + label + '\'' +
                    ", category='" + category + '\'' +
                    ", chapter='" + chapter + '\'' +
                    ", datetime='" + datetime + '\'' +
                    ", source='" + source + '\'' +
                    ", tags='" + tags + '\'' +
                    ", insType='" + insType + '\'' +
                    '}';
        }
    }
}
