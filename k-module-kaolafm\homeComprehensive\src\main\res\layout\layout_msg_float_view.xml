<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/msgFloatRoot"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintStart_toStartOf="parent">


<!--    <ImageView-->
<!--        android:id="@+id/msgRootView"-->
<!--        android:layout_width="@dimen/x176"-->
<!--        android:layout_height="@dimen/y96"-->
<!--        android:scaleType="fitXY"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:layout_constraintDimensionRatio="1.692"-->
<!--        app:layout_constraintStart_toStartOf="parent" />-->

    <ImageView
        android:id="@+id/msgIconView"
        android:layout_width="@dimen/x74"
        android:layout_height="@dimen/y74"
        android:src="@drawable/comprehensive_message_badge_view"
        app:layout_constraintDimensionRatio="1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!--
     app:layout_constraintStart_toStartOf="parent"
            android:layout_marginStart="@dimen/m74"-->
    <ImageView
        android:id="@+id/badgeView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/m3"
        android:layout_marginEnd="@dimen/m3"
        android:visibility="gone"
        android:src="@drawable/comprehensive_red_point"
        app:layout_constraintEnd_toEndOf="@id/msgIconView"
        app:layout_constraintTop_toTopOf="@id/msgIconView" />
</androidx.constraintlayout.widget.ConstraintLayout>