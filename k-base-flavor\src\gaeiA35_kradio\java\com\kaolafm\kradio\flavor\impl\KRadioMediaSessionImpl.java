package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.media.AudioManager;
import android.util.Log;
import android.vehicle.BackKeyListener;
import android.vehicle.KeyBackManager;
import android.view.KeyEvent;


import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioMediaSessionInter;


import com.kaolafm.sdk.core.mediaplayer.OnAudioFocusChangeInter;

import java.lang.ref.WeakReference;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-05-22 20:59
 ******************************************/
public final class KRadioMediaSessionImpl implements KRadioMediaSessionInter {
    private static final String TAG = "KRadioMediaSessionImpl";

    private MyBackKeyListener myBackKeyListener;

    private MyOnAudioFocusChangeInter myOnAudioFocusChangeInter;
    private static final String NAME = "com.edog.car";

    public KRadioMediaSessionImpl() {
        myBackKeyListener = new MyBackKeyListener();
        // TODO 此逻辑暂时先做成标准逻辑（KRadio在失去音频焦点的情况下理论上是不应该响应多媒体按键事件的）
        myOnAudioFocusChangeInter = new MyOnAudioFocusChangeInter(this);
        PlayerManager.getInstance().addAudioFocusListener(myOnAudioFocusChangeInter);
    }

    @Override
    public void registerMediaSession(Object... args) {
        try {
            Log.i(TAG, "registerKeyInfo----------->" + args[0]);
            KeyBackManager keyBackManager = (KeyBackManager) ((Context) args[0]).getSystemService("keyback");
            keyBackManager.registerKeyInfo(myBackKeyListener, null, NAME);
        } catch (NoClassDefFoundError e) {
            e.printStackTrace();
            Log.i(TAG, "registerKeyInfo-----------> error " + e.getLocalizedMessage());
        } catch (Exception e) {
            e.printStackTrace();
            Log.i(TAG, "registerKeyInfo-----------> error " + e.getLocalizedMessage());
        }
    }

    @Override
    public void unregisterMediaSession(Object... args) {
        try {
            Log.i(TAG, "unRegisterKeyInfo----------->" + args[0]);
            KeyBackManager keyBackManager = (KeyBackManager) ((Context) args[0]).getSystemService("keyback");
            keyBackManager.removeKeyInfo(myBackKeyListener, NAME);
        } catch (NoClassDefFoundError e) {
            e.printStackTrace();
            Log.i(TAG, "unRegisterKeyInfo-----------> error " + e.getLocalizedMessage());
        } catch (Exception e) {
            e.printStackTrace();
            Log.i(TAG, "registerKeyInfo-----------> error " + e.getLocalizedMessage());
        }
    }

    @Override
    public void release(Object... args) {
        PlayerManager.getInstance().removeAudioFocusListener(myOnAudioFocusChangeInter);
    }


    private static class MyBackKeyListener implements BackKeyListener {

        @Override
        public void onKeyUp(int keyCode) {
            if (keyCode == KeyEvent.KEYCODE_MEDIA_NEXT
                    || keyCode == KeyEvent.KEYCODE_MEDIA_PREVIOUS
                    || keyCode == KeyEvent.KEYCODE_ZOOM_IN
                    || keyCode == KeyEvent.KEYCODE_ZOOM_OUT
                    || keyCode == KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE) {
                if (keyCode == KeyEvent.KEYCODE_MEDIA_NEXT
                        || keyCode == KeyEvent.KEYCODE_ZOOM_IN) {
                    //下一首
                    PlayerManager.getInstance().playNext();
                } else if (keyCode == KeyEvent.KEYCODE_MEDIA_PREVIOUS
                        || keyCode == KeyEvent.KEYCODE_ZOOM_OUT) {
                    //上一首
                    PlayerManager.getInstance().playPre();
                } else if (keyCode == KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE) {
                    //暂停、播放
                    PlayerManagerHelper.getInstance().switchPlayerStatus(true);
                }
            }
        }

        @Override
        public void onKeyDown(int i) {

        }

        @Override
        public void onKeyLongPress(int i) {

        }
    }

//    private void keyCodeOptions(int keyCode) {
//        if (keyCode == KeyEvent.KEYCODE_MEDIA_NEXT
//                || keyCode == KeyEvent.KEYCODE_MEDIA_PREVIOUS
//                || keyCode == KeyEvent.KEYCODE_ZOOM_IN
//                || keyCode == KeyEvent.KEYCODE_ZOOM_OUT
//                || keyCode == KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE) {
//            if (keyCode == KeyEvent.KEYCODE_MEDIA_NEXT
//                    || keyCode == KeyEvent.KEYCODE_ZOOM_IN) {
//                //下一首
//                PlayerManager.getInstance().playNext();
//            } else if (keyCode == KeyEvent.KEYCODE_MEDIA_PREVIOUS
//                    || keyCode == KeyEvent.KEYCODE_ZOOM_OUT) {
//                //上一首
//                PlayerManager.getInstance().playPre();
//            } else if (keyCode == KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE) {
//                //暂停、播放
//                PlayerManagerHelper.getInstance().switchPlayerStatus(true);
//            }
//        }
//    }

    private static class MyOnAudioFocusChangeInter implements OnAudioFocusChangeInter {
        private WeakReference<KRadioMediaSessionImpl> weakReference;

        public MyOnAudioFocusChangeInter(KRadioMediaSessionImpl kRadioMediaSessionImpl) {
            weakReference = new WeakReference<>(kRadioMediaSessionImpl);
        }

        @Override
        public void onAudioFocusChange(int i) {
            Log.i(TAG, "onAudioFocusChange--------> i = " + i);
            KRadioMediaSessionImpl kRadioMediaSessionImpl = weakReference.get();
            if (kRadioMediaSessionImpl == null) {
                return;
            }
            if (i == AudioManager.AUDIOFOCUS_GAIN) {
                kRadioMediaSessionImpl.registerMediaSession(AppDelegate.getInstance().getContext());
            } else if (i == AudioManager.AUDIOFOCUS_LOSS || i == AudioManager.AUDIOFOCUS_LOSS_TRANSIENT) {
                kRadioMediaSessionImpl.unregisterMediaSession(AppDelegate.getInstance().getContext());
            }
        }
    }
}