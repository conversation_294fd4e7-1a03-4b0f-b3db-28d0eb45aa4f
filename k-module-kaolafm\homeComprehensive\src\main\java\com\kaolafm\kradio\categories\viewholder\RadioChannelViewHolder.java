package com.kaolafm.kradio.categories.viewholder;

import android.util.Log;
import android.util.TypedValue;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;


import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.bean.SubcategoryItemBean;
import com.kaolafm.kradio.lib.base.flavor.KRadioPicSettingInter;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;

/**
 * <AUTHOR>
 * @date 2018/4/25
 */

public class RadioChannelViewHolder extends BaseSubcategoryViewHolder {

    private String TAG = "RadioChannelViewHolder";

    ImageView ivCover;
    TextView tvName;
    View viewRadioMongolian;

    private KRadioPicSettingInter mPicSetting;

    public RadioChannelViewHolder(View itemView, KRadioPicSettingInter picSetting) {
        super(itemView);
        mPicSetting = picSetting;
        ivCover=itemView.findViewById(R.id.iv_cover);
        tvName=itemView.findViewById(R.id.tv_name);
        viewRadioMongolian=itemView.findViewById(R.id.view_radio_mongolian);
    }

    @Override
    public void setupData(SubcategoryItemBean subcategoryItemBean, int position) {
        super.setupData(subcategoryItemBean, position);

        String picUrl;
        picUrl = HolderUtils.getUrlString(subcategoryItemBean, mPicSetting);
        ImageLoader.getInstance().displayImage(mContext, picUrl, ivCover);
        String name = subcategoryItemBean.getName();
        Log.d("SubcategoryViewHolder", "RadioChannelViewHolder-setupData-" + name + "--" + name.length());
//           上汽大众37w AI电台闪退 过滤下制表符防止特定的某些车机出现展示问题
//            09-22 10:42:09.066  1915  1915 D SubcategoryViewHolder: RadioChannelViewHolder-setupData-环球新闻眼	--6   "title": "环球新闻眼\t",
//            09-22 10:42:09.069  1915  1915 W System.err: java.lang.ArrayIndexOutOfBoundsException: length=6; index=6
        if (name.contains("\t"))
            name = name.replaceAll("\t", "");

        tvName.setText(name);
        int size = ResUtil.getDimen(R.dimen.subcategory_item_radio_text_size);
        tvName.setTextSize(TypedValue.COMPLEX_UNIT_PX, size);
        tvName.setSelected(subcategoryItemBean.isSelected());
        Log.i(TAG,"name:"+name+" size:"+size);
        ViewUtil.setViewVisibilityAccordingToSetting(viewRadioMongolian);

    }

}
