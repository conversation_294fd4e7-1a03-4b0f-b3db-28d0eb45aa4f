package com.kaolafm.ad.comprehensive.processor;

import android.util.Log;

import com.kaolafm.ad.comprehensive.KradioAdAudioManager;
import com.kaolafm.ad.api.model.AudioAdvert;
import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.kradio.component.ActionProcessor;
import com.kaolafm.kradio.component.RealCaller;
import com.kaolafm.kradio.component.SharedConst;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.NetworkUtil;

@SharedConst
public class AdControlProcessor implements ActionProcessor {
    private static final String ACTION_CONTROL = "action_control";

    private static final String ADCONTROL_TAG = "AdControlProcessor";

    private static final String AUDIO_AD_PLAY_OVER = "audio_ad_play_over";

    private static final String CANCEL = "cancel";
    private static final String AUDIO_AD_PLAY_DURITION = "audio_ad_play_durition";

    @Override
    public String actionName() {
        return ACTION_CONTROL;
    }

    @Override
    public boolean onAction(RealCaller caller) {

        String audioAdPlayOver = caller.getParamValue(AUDIO_AD_PLAY_OVER);
        int adPlayDrition = caller.getParamValue(AUDIO_AD_PLAY_DURITION);
        if (audioAdPlayOver.equals(CANCEL)) {
            Log.i(ADCONTROL_TAG, "xxxcancel");
            KradioAdAudioManager.getInstance().cancel();
            AudioAdvert audioAdvert = KradioAdAudioManager.getInstance().getAudioAdvert();

            if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(),false)){
                ToastUtil.showOnly(AppDelegate.getInstance().getContext(), R.string.no_net_work_str);
            }

            if (audioAdvert != null) {
                AdvertisingManager.getInstance().close(audioAdvert);
            }
        }
        return false;
    }

}
