//package com.ecarx.sdk;
//
//import com.ecarx.sdk.oauth.impl.IMCApi;
//import com.ecarx.sdk.oauth.impl.MCApiFactory;
//import com.kaolafm.kradio.lib.base.AppDelegate;
//
//public class MCOAuthApiUtils {
//
//    private static IMCApi imcApi;
//
//    public static IMCApi getMCApi() {
//        if (imcApi == null) {
//            synchronized (MCOAuthApiUtils.class) {
//                if (imcApi == null) {
//                    imcApi = MCApiFactory.create(AppDelegate.getInstance().getContext());
//                }
//            }
//        }
//        return imcApi;
//    }
//
//
//
//}