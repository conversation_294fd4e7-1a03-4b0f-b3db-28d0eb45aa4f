package com.kaolafm.kradio.lib.utils;

import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.base.utils.DeviceUtil;
import com.kaolafm.kradio.lib.BuildConfig;
import com.kaolafm.kradio.lib.base.AppDelegate;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-05-10 16:18
 ******************************************/
public final class KaolaAppConfigData {
    private String mAppId;
    private String mAppKey;
    private String mChannel;
    private String mChannelVersion;
    private String mPackageName;
    private String mVersionName;
    private String cityName;
    /**
     * 经度
     */
    private String mLng;
    /**
     * 纬度
     */
    private String mLat;

    /**
     * 设备唯一标识
     */
    private String mUdid;
    /**
     * 1000460(竖屏) 1000400（横屏）
     */
    private String mAppType = "1000460";

    private KaolaAppConfigData() {
        initAppKeyAndId();
    }

    private final static class KAOLA_APP_CONFIG_META_DATA_INNER_CLAZZ {
        private static final KaolaAppConfigData KAOLAAPPCONFIGMETADATA = new KaolaAppConfigData();
    }

    public static KaolaAppConfigData getInstance() {
        return KAOLA_APP_CONFIG_META_DATA_INNER_CLAZZ.KAOLAAPPCONFIGMETADATA;
    }


    private void initAppKeyAndId() {
        Context context = AppDelegate.getInstance().getContext();
        initVersionName(context);
        if (!TextUtils.isEmpty(mAppId)) {
            return;
        }
        mPackageName = context.getPackageName();
        ApplicationInfo appInfo = null;
        try {
            appInfo = context.getPackageManager().getApplicationInfo(mPackageName, PackageManager.GET_META_DATA);
        } catch (Throwable e) {
            e.printStackTrace();
        }
        if (appInfo != null && appInfo.metaData != null) {
            mAppKey = String.valueOf(appInfo.metaData.get("com.kaolafm.open.sdk.AppKey"));
            mAppId = String.valueOf(appInfo.metaData.get("com.kaolafm.open.sdk.AppId"));
            mChannel = String.valueOf(appInfo.metaData.get("com.kaolafm.open.sdk.Channel"));
            boolean isAppendSuffix = appInfo.metaData.getBoolean("com.kaolafm.open.sdk.Suffix");
            if (isAppendSuffix && !TextUtils.isEmpty(mChannel)) {
                mPackageName = mPackageName + "." + mChannel;
            }
            Log.i("KaolaAppConfigData", "initAppKeyAndId--------->mAppId = " + mAppId);
        }
    }

    public boolean isShowMainBackHomeSwitch() {
        return BuildConfig.IS_SHOW_HOME_BACK;
    }

    /**
     * 是否显示激活页导航栏
     *
     * @return true为是，false为否
     */
    public boolean isShowActivationHomeBack() {
        return BuildConfig.IS_SHOW_ACTIVATION_HOME_BACK;
    }

//    public boolean showQrCodeLogin(){
//        return BuildConfig.SHOW_Q_R_CODE_LOGIN;
//    }

    private void initVersionName(Context context) {
        PackageInfo pi;
        try {
            PackageManager pm = context.getPackageManager();
            pi = pm.getPackageInfo(context.getPackageName(),
                    PackageManager.GET_CONFIGURATIONS);
            mVersionName = pi.versionName;
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取APP配置在AndroidManifest.xml中的appId
     *
     * @return
     */
    public String getAppId() {
        return mAppId;
    }

    /**
     * 获取APP传入的AppKey
     *
     * @return
     */
    public String getAppKey() {
        return mAppKey;
    }

    /**
     * 获取渠道名
     *
     * @return
     */
    public String getChannel() {
        return mChannel;
    }

    public void setAppId(String appId) {
        mAppId = appId;
    }

    public void setAppKey(String appKey) {
        mAppKey = appKey;
    }

    public void setChannel(String channel) {
        mChannel = channel;
    }

    public void setPackageName(String packageName) {
        mPackageName = packageName;
    }

    /**
     * 获取渠道版本号
     *
     * @return
     */
    public String getChannelVersion() {
        return BuildConfig.VERSION_NAME;
    }


    /**
     * 获取app包名（ApplicationId）
     *
     * @return
     */
    public String getPackageName() {
        return mPackageName;
    }

    /**
     * 获取当前app版本名称
     *
     * @return
     */
    public String getVersionName() {
        return mVersionName;
    }

    /**
     * 设置横竖屏产品线
     *
     * @param appType
     */
    public void setAppType(String appType) {
        this.mAppType = appType;
    }

    public String getAppType() {
        return this.mAppType;
    }

    public String getUdid() {
        Context context = AppDelegate.getInstance().getContext();
        if (context == null) {
            return null;
        }
        if (mUdid == null) {
            mUdid = DeviceUtil.getDeviceId(context);
        }
        return mUdid;
    }

    public void setUdid(String mUdid) {
        this.mUdid = mUdid;
    }

    public String getLat() {
        return mLat;
    }

    public void setLat(String lat) {
        this.mLat = lat;
    }

    public String getLng() {
        return mLng;
    }

    public void setLng(String lng) {
        this.mLng = lng;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }
}
