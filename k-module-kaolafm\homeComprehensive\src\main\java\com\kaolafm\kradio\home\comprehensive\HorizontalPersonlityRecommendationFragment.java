package com.kaolafm.kradio.home.comprehensive;

import android.annotation.SuppressLint;
import android.content.res.Configuration;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import android.util.TypedValue;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.kaolafm.kradio.common.event.HideShowEBData;
import com.kaolafm.kradio.constant.LoginProcessorConst;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.home.comprehensive.mvp.HorizontalPersonlityRecommendationPresenter;
import com.kaolafm.kradio.home.comprehensive.mvp.HorizontalPersonlityRecommendationPresenterContact;
import com.kaolafm.kradio.user.BackUserFragment;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.opensdk.api.personalise.PersonalizedRequest;
import com.kaolafm.opensdk.api.personalise.model.InterestTag;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.util.ReportConstants;

import java.util.ArrayList;
import java.util.List;

import org.greenrobot.eventbus.EventBus;

public class HorizontalPersonlityRecommendationFragment extends BaseFragment<HorizontalPersonlityRecommendationPresenter> implements HorizontalPersonlityRecommendationPresenterContact.View {
    private ArrayList<String> tagList = new ArrayList<>();
    public static final int STATUS_SEX = 1;
    public static final int STATUS_AGE_DURITION = 2;
    public static final int STATUS_AGE = 3;
    public static final int STATUS_INTEREST = 4;
    public static final int INTESTING_SIZE = 10;// 标准数据是10个推荐内容
    public static final String AGE_DURITION_90 = "age_90";
    public static final String AGE_DURITION_80 = "age_80";
    public static final String AGE_DURITION_70 = "age_70";
    public static final String AGE_DURITION_60 = "age_60";
    public static final String AGE_DURITION_50 = "age_50";
    public static final String AGE_DURITION_00 = "age_00";
    private int mSelectedSex;// 0 男 ，1 女
    private int mCheChangStaus;// 车厂状态联调 ，0：未登录听伴账号   1：登录听伴账号，未 保存车厂数据到服务器  2：登录听伴账号， 保存车厂数据到服务器
    private String mAgeDurition;
    private String mSelectedAge;
    ImageView mPcIvBack, ageDetailProgressControl, mPcIvManIv, mPcIvFemailIv, ageDuritionProgress, mNetworknosigin;
    TextView mPcJump, mPersonCommendTitle;
    TextView mPcSelectTv;
    ConstraintLayout mYiKaTonglayout; // yikatong
    ConstraintLayout mConsHeadlayout; // 头部布局
    ConstraintLayout mRlSelectSex; // 性别布局
    RelativeLayout ageduritionlayout;// 年代布局
    RelativeLayout agelayout; // 年龄布局
    ConstraintLayout constraintLayout; // 兴趣布局
    ConstraintLayout constraintPortLayout;
    RelativeLayout mSexManRl, mSexFemailRl;
    LinearLayout mPcLoadingView;
    List<InterestTag> mInterestTags = new ArrayList<>();
    List<TextView> ageDuritionViews = new ArrayList<>();
    List<TextView> ageViews = new ArrayList<>();
    List<ImageView> IntestingViews = new ArrayList<>();
    List<ImageView> IntestingPortViews = new ArrayList<>();
    Button button1, button2, button3, button4;
    //  年龄
    TextView agetv0, agetv1, agetv2, agetv3, agetv4, agetv5, agetv6, agetv7, agetv8, agetv9, ageSubmitTv;
    private int status;
    private boolean isSetUserAttribute = false;

    //    ----------------兴趣标签------------------
    TextView mRecommendTvOne, mRecommendTvTwo, mRecommendTvThree, mRecommendTvFour, mRecommendTvFive, mRecommendTvSix, mRecommendTvSeven, mRecommendTvEight, mRecommendTvNice, mRecommendTvTen, mRecommendTvSubmit;

    ImageView mRecommendIvOne, mRecommendIvTwo, mRecommendIvThree, mRecommendIvFour, mRecommendIvFive, mRecommendIvSix, mRecommendIvSeven, mRecommendIvEight, mRecommendIvNice, mRecommendIvTen;

    TextView mRecommendTvOnePort, mRecommendTvTwoPort, mRecommendTvThreePort, mRecommendTvFourPort, mRecommendTvFivePort, mRecommendTvSixPort,
            mRecommendTvSevenPort, mRecommendTvEightPort, mRecommendTvNicePort, mRecommendTvTenPort, mRecommendTvSubmitPort;

    ImageView mRecommendIvOnePort, mRecommendIvTwoPort, mRecommendIvThreePort, mRecommendIvFourPort, mRecommendIvFivePort,
            mRecommendIvSixPort, mRecommendIvSevenPort, mRecommendIvEightPort, mRecommendIvNicePort, mRecommendIvTenPort;


    //    -----------------------
    TextView ageDritationTv1, ageDritationTv2, ageDritationTv3, ageDritationTv4, ageDritationTv5, ageDritationTv6;
    RelativeLayout mHomeNoNetWorkRl;
    TextView mTvNetworkNosign;


    public static HorizontalPersonlityRecommendationFragment getInstance() {
        return new HorizontalPersonlityRecommendationFragment();
    }

    @Override
    protected int getLayoutId() {
        return R.layout.fragment_personlityrecommendation;
    }

    @Override
    protected int getLayoutId_Tow() {
        return 0;
    }

    @Override
    protected void showAccordingToScreen(int orientation) {
        super.showAccordingToScreen(orientation);
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            if (constraintLayout.isShown() || constraintPortLayout.isShown()) {
                constraintLayout.setVisibility(View.VISIBLE);
                constraintPortLayout.setVisibility(View.GONE);
                updateView(IntestingViews);
            }
        } else {
            if (constraintLayout.isShown() || constraintPortLayout.isShown()) {
                constraintLayout.setVisibility(View.GONE);
                constraintPortLayout.setVisibility(View.VISIBLE);
                updateView(IntestingPortViews);
            }
        }
//      统一更换字体大小
        updateTextsize(orientation);
    }

    //  根据横竖屏切换，设置view的状态,选中状态同步
    private void updateView(List<ImageView> imageViews) {
        for (int i = 0; i < imageViews.size(); i++) {
            String title = mInterestTags.get(i).getName();
            if (tagList.contains(title)) {
                imageViews.get(i).setImageDrawable(ResUtil.getDrawable(R.drawable.pc_bg_select));
            } else {
                imageViews.get(i).setImageDrawable(ResUtil.getDrawable(R.drawable.pc_bg_unselect));
            }
        }
    }

    private void updateTextsize(int orientation) {
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            mPersonCommendTitle.setTextSize(TypedValue.COMPLEX_UNIT_PX, getResources().getDimensionPixelOffset(R.dimen.m34));
            ageSubmitTv.setTextSize(TypedValue.COMPLEX_UNIT_PX, getResources().getDimensionPixelOffset(R.dimen.m34));
        } else {
            mPersonCommendTitle.setTextSize(TypedValue.COMPLEX_UNIT_PX, getResources().getDimensionPixelOffset(R.dimen.m26));
            ageSubmitTv.setTextSize(TypedValue.COMPLEX_UNIT_PX, getResources().getDimensionPixelOffset(R.dimen.m26));

        }
    }

    @Override
    protected HorizontalPersonlityRecommendationPresenter createPresenter() {
        return new HorizontalPersonlityRecommendationPresenter(getContext(), this);
    }

    @Override
    public void initView(View view) {
        status = STATUS_SEX;// 默认进入性别设置界面
        mHomeNoNetWorkRl = (RelativeLayout) view.findViewById(R.id.home_no_network_rel);
        mNetworknosigin = (ImageView) view.findViewById(R.id.network_nosigin);
        mTvNetworkNosign = (TextView) view.findViewById(R.id.tv_network_nosign);
        findYiKaTongTestView(view);
        findHead(view);
        findSexView(view);
        findAgeDuritionView(view);// 选择年代
        findAgeView(view);        // 选择年龄
        findInterstingView(view);//      兴趣标签
        findInterstingPortView(view);
        mPcLoadingView = (LinearLayout) view.findViewById(R.id.pc_loading);
        HorizontalPersonlityRecommendationPresenter.mCheChangStaus = 0;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        goRecommendation();
    }

    public void showToast(String text) {
        ToastUtil.showOnly(getContext(), text);
    }


    public void refreashAgeData(TextView tv, int position) {
        mSelectedAge = tv.getText().toString();
        tv.setSelected(true);
        for (int i = 0; i < ageViews.size(); i++) {
            if (i != position) {
                ageViews.get(i).setSelected(false);
            }
        }
    }

    public void refreashAge() {
        for (int i = 0; i < ageViews.size(); i++) {
            ageViews.get(i).setSelected(false);
        }
    }

    public void refreshIntest() {
        for (int i = 0; i < IntestingViews.size(); i++) {
            IntestingViews.get(i).setImageDrawable(ResUtil.getDrawable(R.drawable.pc_bg_unselect));
            IntestingPortViews.get(i).setImageDrawable(ResUtil.getDrawable(R.drawable.pc_bg_unselect));
        }
    }

    public void refreashAgeDuritionView(int position) {
        for (int i = 0; i < ageDuritionViews.size(); i++) {
            if (position != i) {
                ageDuritionViews.get(i).setSelected(false);
            }
        }
    }

    //  更新年龄数据
    public void updateAgeStatus(String ageDuritation) {
        mPcIvBack.setVisibility(View.VISIBLE);
        status = STATUS_AGE;
        ageduritionlayout.setVisibility(View.GONE);
        agelayout.setVisibility(View.VISIBLE);
        refreashAge();
        mPcIvBack.setVisibility(View.VISIBLE);
        String[] title;
        if (ageDuritation.equals(AGE_DURITION_00)) {
            title = ResUtil.getStringArray(R.array.age_00);
            updateage(title);
        } else if (ageDuritation.equals(AGE_DURITION_90)) {
            title = ResUtil.getStringArray(R.array.age_90);
            updateage(title);
        } else if (ageDuritation.equals(AGE_DURITION_80)) {
            title = ResUtil.getStringArray(R.array.age_80);
            updateage(title);
        } else if (ageDuritation.equals(AGE_DURITION_70)) {
            title = ResUtil.getStringArray(R.array.age_70);
            updateage(title);
        } else if (ageDuritation.equals(AGE_DURITION_60)) {
            title = ResUtil.getStringArray(R.array.age_60);
            updateage(title);
        } else if (ageDuritation.equals(AGE_DURITION_50)) {
            title = ResUtil.getStringArray(R.array.age_50);
            updateage(title);
        }
    }

    public void updateage(String[] title) {
        agetv0.setText(title[0]);
        agetv1.setText(title[1]);
        agetv2.setText(title[2]);
        agetv3.setText(title[3]);
        agetv4.setText(title[4]);
        agetv5.setText(title[5]);
        agetv6.setText(title[6]);
        agetv7.setText(title[7]);
        agetv8.setText(title[8]);
        agetv9.setText(title[9]);

        agetv0.setSelected(false);
        agetv1.setSelected(false);
        agetv2.setSelected(false);
        agetv3.setSelected(false);
        agetv4.setSelected(false);
        agetv5.setSelected(false);
        agetv6.setSelected(false);
        agetv7.setSelected(false);
        agetv8.setSelected(false);
        agetv9.setSelected(false);
    }

    public void goToAgeDruitionStatus(int i) {
        if (i == 0) {
            mSexManRl.setBackgroundResource(R.drawable.pc_bg_select);
            mSexFemailRl.setBackgroundResource(R.drawable.pc_bg_unselect);

            Drawable drawable = ResUtil.getDrawable(R.drawable.select_man);
            mPcIvManIv.setImageDrawable(drawable);
            mPcIvFemailIv.setImageResource(R.drawable.pc_femail);
        } else if (i == 1) {
            mSexManRl.setBackgroundResource(R.drawable.pc_bg_unselect);
            mSexFemailRl.setBackgroundResource(R.drawable.pc_bg_select);

            Drawable drawable = ResUtil.getDrawable(R.drawable.selected_femail);
            mPcIvFemailIv.setImageDrawable(drawable);
            mPcIvManIv.setImageResource(R.drawable.pc_man);
        }
        status = STATUS_AGE_DURITION;
        mRlSelectSex.setVisibility(View.GONE);
        ageduritionlayout.setVisibility(View.VISIBLE);
        refreashAgeDuritionView(-1);
        mPcIvBack.setVisibility(View.VISIBLE);
    }


    @Override
    public void saveError(ApiException exception) {
        toast("保存失败");
        goHorizontalHomeFragment();
    }

    @Override
    public void saveSuccess(String msg) {
        toast(msg);
        goHorizontalHomeFragment();
    }

    @Override
    public void showFirst(List<InterestTag> interestTags) {
        showOrHideLoadingView(false);
        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
            mHomeNoNetWorkRl.setVisibility(View.VISIBLE);
            constraintLayout.setVisibility(View.GONE);
            constraintPortLayout.setVisibility(View.GONE);
            mPcIvBack.setVisibility(View.GONE);
            return;
        }
//        第一次请求回来逻辑处理
        if (interestTags == null || interestTags.size() == 0) {
//          进入用户属性选择界面
            status = STATUS_SEX;
            isSetUserAttribute = false;
            mRlSelectSex.setVisibility(View.VISIBLE);
            mPcIvBack.setVisibility(View.GONE);
            mPcJump.setVisibility(View.VISIBLE);
        } else {
//            进入兴趣标签选择界面
            status = STATUS_INTEREST;
            isSetUserAttribute = true;
            mPcJump.setVisibility(View.VISIBLE);
            int ori = ResUtil.getOrientation(); //获取屏幕方向
            if (ori == Configuration.ORIENTATION_LANDSCAPE) {
                //横屏
                mHomeNoNetWorkRl.setVisibility(View.INVISIBLE);
                constraintLayout.setVisibility(View.VISIBLE);
                constraintPortLayout.setVisibility(View.GONE);
            } else if (ori == Configuration.ORIENTATION_PORTRAIT) {
                //竖屏
                constraintPortLayout.setVisibility(View.VISIBLE);
                constraintLayout.setVisibility(View.GONE);
                mHomeNoNetWorkRl.setVisibility(View.INVISIBLE);
            }
            if (interestTags != null) {
                int size = interestTags.size();
                if (size > INTESTING_SIZE) {
                    //解决https://app.huoban.com/tables/2100000007530121/items/2300001633090797?userId=1229522问题
                    ArrayList<InterestTag> tagArrayList = new ArrayList<>(INTESTING_SIZE);
                    for (int i = 0; i < INTESTING_SIZE; i++) {
                        tagArrayList.add(interestTags.get(i));
                    }
                    mInterestTags = tagArrayList;
                } else {
                    mInterestTags = interestTags;
                }
                mPcIvBack.setVisibility(View.GONE);
                updateInterstingData();
            } else {
                goHorizontalHomeFragment();
            }
        }
    }

    private void updateInterstingData() {
        if (mInterestTags != null && mInterestTags.size() == INTESTING_SIZE) {
            mRecommendTvOne.setText(mInterestTags.get(0).getName());
            mRecommendTvTwo.setText(mInterestTags.get(1).getName());
            mRecommendTvThree.setText(mInterestTags.get(2).getName());
            mRecommendTvFour.setText(mInterestTags.get(3).getName());
            mRecommendTvFive.setText(mInterestTags.get(4).getName());
            mRecommendTvSix.setText(mInterestTags.get(5).getName());
            mRecommendTvSeven.setText(mInterestTags.get(6).getName());
            mRecommendTvEight.setText(mInterestTags.get(7).getName());
            mRecommendTvNice.setText(mInterestTags.get(8).getName());
            mRecommendTvTen.setText(mInterestTags.get(9).getName());

            mRecommendTvOnePort.setText(mInterestTags.get(0).getName());
            mRecommendTvTwoPort.setText(mInterestTags.get(1).getName());
            mRecommendTvThreePort.setText(mInterestTags.get(2).getName());
            mRecommendTvFourPort.setText(mInterestTags.get(3).getName());
            mRecommendTvFivePort.setText(mInterestTags.get(4).getName());
            mRecommendTvSixPort.setText(mInterestTags.get(5).getName());
            mRecommendTvSevenPort.setText(mInterestTags.get(6).getName());
            mRecommendTvEightPort.setText(mInterestTags.get(7).getName());
            mRecommendTvNicePort.setText(mInterestTags.get(8).getName());
            mRecommendTvTenPort.setText(mInterestTags.get(9).getName());

        }
    }

    private void goHorizontalHomeFragment() {
        HideShowEBData hideShowEBData = new HideShowEBData();
        hideShowEBData.setType(HideShowEBData.HIDE_RECOMMEND);
        EventBus.getDefault().post(hideShowEBData);
    }

    private void findAgeView(View view) {
        //        选择年龄
        agelayout = (RelativeLayout) view.findViewById(R.id.pc_age_detail_rl);
        agetv0 = (TextView) agelayout.findViewById(R.id.age_detail_one);
        agetv1 = (TextView) agelayout.findViewById(R.id.age_detail_two);
        agetv2 = (TextView) agelayout.findViewById(R.id.age_detail_three);
        agetv3 = (TextView) agelayout.findViewById(R.id.age_detail_four);
        agetv4 = (TextView) agelayout.findViewById(R.id.age_detail_five);
        agetv5 = (TextView) agelayout.findViewById(R.id.age_detail_six);
        agetv6 = (TextView) agelayout.findViewById(R.id.age_detail_seven);
        agetv7 = (TextView) agelayout.findViewById(R.id.age_detail_eight);
        agetv8 = (TextView) agelayout.findViewById(R.id.age_detail_nice);
        agetv9 = (TextView) agelayout.findViewById(R.id.age_detail_ten);
        ageDetailProgressControl = (ImageView) agelayout.findViewById(R.id.age_detail_progress_control);
        ageSubmitTv = (TextView) agelayout.findViewById(R.id.age_detail_next);
        ageViews.add(agetv0);
        ageViews.add(agetv1);
        ageViews.add(agetv2);
        ageViews.add(agetv3);
        ageViews.add(agetv4);
        ageViews.add(agetv5);
        ageViews.add(agetv6);
        ageViews.add(agetv7);
        ageViews.add(agetv8);
        ageViews.add(agetv9);

        for (int i = 0; i < ageViews.size(); i++) {
            TextView textView = ageViews.get(i);
            int position = i;
            ageViews.get(i).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    textView.setSelected(true);
                    ageDetailProgressControl.setBackgroundResource(R.drawable.pc_progress_finish);
                    refreashAgeData(textView, position);
                }
            });
        }
        ageSubmitTv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                if (AntiShake.check(v.getId())) {
                    return;
                }

                if (mSelectedAge == null || mSelectedAge.equals("")) {
                    ToastUtil.showOnly(getContext(), ResUtil.getString(R.string.choose_age));
                } else {

                    if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
                        ToastUtil.showOnly(getContext(), ResUtil.getString(R.string.no_net_work_str));
                        return;
                    } else {
                        mPcIvBack.setVisibility(View.VISIBLE);
                        new PersonalizedRequest().saveUserAttribute(mSelectedAge, mSelectedSex, new HttpCallback<List<InterestTag>>() {
                            @Override
                            public void onSuccess(List<InterestTag> interestTags) {
                                status = STATUS_INTEREST;
                                agelayout.setVisibility(View.GONE);
                                mInterestTags = interestTags;
                                mPcJump.setVisibility(View.VISIBLE);
                                mPcIvBack.setVisibility(View.VISIBLE);
                                refreshIntest();
                                updateInterstingData();
                                agelayout.setVisibility(View.GONE);
                                int ori = ResUtil.getOrientation(); //获取屏幕方向
                                if (ori == Configuration.ORIENTATION_LANDSCAPE) {
                                    //横屏
                                    constraintLayout.setVisibility(View.VISIBLE);
                                } else if (ori == Configuration.ORIENTATION_PORTRAIT) {
                                    //竖屏
                                    constraintPortLayout.setVisibility(View.VISIBLE);
                                }
                            }

                            @Override
                            public void onError(ApiException e) {
//                      出现问题统一跳转到
                                goHorizontalHomeFragment();
                            }
                        });
                    }
                }
            }
        });
    }

    private void toast(String msg) {
        ToastUtil.showNormal(getContext(), msg);
    }

    private void findHead(View view) {
        mConsHeadlayout = (ConstraintLayout) view.findViewById(R.id.pc_all_head);
        mPcIvBack = (ImageView) view.findViewById(R.id.pc_iv_back);
        mPersonCommendTitle = (TextView) view.findViewById(R.id.personCommend_title);
        mPcJump = (TextView) view.findViewById(R.id.pc_jump);
        mPcJump.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (AntiShake.check(v.getId())) {
                    return;
                }
                showOrHideLoadingView(false);
                goHorizontalHomeFragment();
                mPcJump.setClickable(false);
            }
        });
        mPcIvBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!isSetUserAttribute) {
                    if (status == STATUS_INTEREST) {
                        tagList.clear();
                        status = STATUS_AGE;
                        agelayout.setVisibility(View.VISIBLE);
                        constraintLayout.setVisibility(View.GONE);
                        constraintPortLayout.setVisibility(View.GONE);
                        mPcJump.setVisibility(View.GONE);
                    } else if (status == STATUS_AGE) {
                        status = STATUS_AGE_DURITION;
                        mSelectedAge = "";
                        ageduritionlayout.setVisibility(View.VISIBLE);
                        agelayout.setVisibility(View.GONE);
                        mPcJump.setVisibility(View.GONE);
                    } else if (status == STATUS_AGE_DURITION) {
                        status = STATUS_SEX;
                        agelayout.setVisibility(View.GONE);
                        ageduritionlayout.setVisibility(View.GONE);
                        mRlSelectSex.setVisibility(View.VISIBLE);
                        mPcIvBack.setVisibility(View.INVISIBLE);
                        mPcJump.setVisibility(View.VISIBLE);
                    } else {
                        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001631954380?userId=1229522问题
                        getActivity().finish();
                    }
                }
            }
        });
    }

    Button button5;
    Button button6;

    private void findYiKaTongTestView(View view) {
        mYiKaTonglayout = (ConstraintLayout) view.findViewById(R.id.pc_yikatong);
        button1 = (Button) view.findViewById(R.id.button);
        button2 = (Button) view.findViewById(R.id.button2);
        button3 = (Button) view.findViewById(R.id.button3);
        button4 = (Button) view.findViewById(R.id.button4);
        button5 = (Button) view.findViewById(R.id.button5);
        button6 = (Button) view.findViewById(R.id.button6);
        button1.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (AntiShake.check(v.getId())) {
                    return;
                }
                HorizontalPersonlityRecommendationPresenter.mCheChangStaus = 1;
                BackUserFragment backFrag = BackUserFragment.newInstance(2);
                backFrag.setBackData(LoginProcessorConst.BACKTYPE_POP, -1);
                HorizontalPersonlityRecommendationFragment.this.extraTransaction().start(backFrag);
            }
        });
//    听伴账号登录，将车厂账号和听伴账号Uid进行绑定，但是不跳转到个性推荐界面
        button5.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (AntiShake.check(v.getId())) {
                    return;
                }
                HorizontalPersonlityRecommendationPresenter.mCheChangStaus = 2;
//              伪造的假数据
                String phomenumber = "15321883907";
                String nickname = "ww_kaola";
                int gender = 1;
                int age = 30;
                String city = "beijing";
                String avatar = "wwww";
                new PersonalizedRequest().saveThirdUser(phomenumber, avatar, nickname, gender, age, city, new HttpCallback<Boolean>() {
                    @Override
                    public void onSuccess(Boolean aBoolean) {

                        if (aBoolean) {
                            ToastUtil.showOnly(getContext(), "保存用户成功，将车厂账号和听伴账号绑定成功");
                        } else {
                            ToastUtil.showOnly(getContext(), "保存用户失败，将车厂账号和听伴账号绑定失败");
                        }
//                        goRecommendation();
                    }

                    @Override
                    public void onError(ApiException e) {
                        ToastUtil.showOnly(getContext(), "保存用户失败，将车厂账号和听伴账号绑定失败" + e.toString());
//                        goRecommendation();
                    }
                });
            }
        });
//听伴账号登录，将车厂账号和听伴账号Uid进行绑定
        button2.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (AntiShake.check(v.getId())) {
                    return;
                }
                HorizontalPersonlityRecommendationPresenter.mCheChangStaus = 2;
//              伪造的假数据
                String phomenumber = "15321883907";
                String nickname = "ww_kaola";
                int gender = 1;
                int age = 30;
                String city = "beijing";
                String avatar = "wwww";
                new PersonalizedRequest().saveThirdUser(phomenumber, avatar, nickname, gender, age, city, new HttpCallback<Boolean>() {
                    @Override
                    public void onSuccess(Boolean aBoolean) {

                        if (aBoolean) {
                            ToastUtil.showOnly(getContext(), "保存用户成功，将车厂账号和听伴账号绑定成功");
                        } else {
                            ToastUtil.showOnly(getContext(), "保存用户失败，将车厂账号和听伴账号绑定失败");
                        }
                        goRecommendation();
                    }

                    @Override
                    public void onError(ApiException e) {
                        ToastUtil.showOnly(getContext(), "保存用户失败，将车厂账号和听伴账号绑定失败" + e.toString());
                        goRecommendation();
                    }
                });

            }
        });
//      听伴账号登录，未将车厂账号和听伴账号Uid进行绑定
        button3.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (AntiShake.check(v.getId())) {
                    return;
                }
                HorizontalPersonlityRecommendationPresenter.mCheChangStaus = 1;
                goRecommendation();
            }
        });
//     只带devicid 进入个性推荐界面
        button4.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (AntiShake.check(v.getId())) {
                    return;
                }
                HorizontalPersonlityRecommendationPresenter.mCheChangStaus = 0;
                goRecommendation();
            }
        });
//      听伴账号登录，已经将车厂数据和听伴账号绑定，但车厂无用户属性信息，跳转个性推荐
        button6.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                HorizontalPersonlityRecommendationPresenter.mCheChangStaus = 1;
                goRecommendation();
            }
        });

    }

    private void goRecommendation() {
        showOrHideLoadingView(true);
        mYiKaTonglayout.setVisibility(View.GONE);
        mConsHeadlayout.setVisibility(View.VISIBLE);
    }

    private void findSexView(View view) {
//      获取几个子布局
//      选择男女
        mRlSelectSex = (ConstraintLayout) view.findViewById(R.id.rl_selcet_sex);
        mRlSelectSex.setVisibility(View.GONE);
        mPcIvManIv = (ImageView) view.findViewById(R.id.pc_iv_man);
        mPcIvFemailIv = (ImageView) view.findViewById(R.id.pc_iv_femail);
        mSexManRl = (RelativeLayout) view.findViewById(R.id.pc_man_rl);
        mSexFemailRl = (RelativeLayout) view.findViewById(R.id.pc_femail_rl);
        mSexFemailRl.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mSelectedSex = 1;
                mPcJump.setVisibility(View.GONE);
                goToAgeDruitionStatus(1);
            }
        });
// 第一层
        mSexManRl.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mPcJump.setVisibility(View.GONE);
                mSelectedSex = 0;
                goToAgeDruitionStatus(0);
            }
        });
    }

    private void findAgeDuritionView(View view) {
        // 选择年代
        ageduritionlayout = (RelativeLayout) view.findViewById(R.id.pc_age_rl);
        ageDritationTv1 = ageduritionlayout.findViewById(R.id.age_one);
        ageDritationTv2 = ageduritionlayout.findViewById(R.id.age_two);
        ageDritationTv3 = ageduritionlayout.findViewById(R.id.age_three);
        ageDritationTv4 = ageduritionlayout.findViewById(R.id.age_four);
        ageDritationTv5 = ageduritionlayout.findViewById(R.id.age_five);
        ageDritationTv6 = ageduritionlayout.findViewById(R.id.age_six);
        ageDuritionProgress = ageduritionlayout.findViewById(R.id.age_durition_progress);
        ageDuritionViews.add(ageDritationTv1);
        ageDuritionViews.add(ageDritationTv2);
        ageDuritionViews.add(ageDritationTv3);
        ageDuritionViews.add(ageDritationTv4);
        ageDuritionViews.add(ageDritationTv5);
        ageDuritionViews.add(ageDritationTv6);
        for (int i = 0; i < ageDuritionViews.size(); i++) {
            TextView textView = ageDuritionViews.get(i);
            int po = i;
            ageDuritionViews.get(i).setOnClickListener(new View.OnClickListener() {
                @SuppressLint("ResourceAsColor")
                @Override
                public void onClick(View v) {
                    textView.setSelected(true);
                    refreashAgeDuritionView(po);
                    ageDuritionProgress.setBackgroundResource(R.drawable.pc_progress_select);
                    ageDetailProgressControl.setBackgroundResource(R.drawable.pc_progress_select);
                    mPcJump.setVisibility(View.GONE);
                    mAgeDurition = AGE_DURITION_00;
                    if (po == 0) {
                        updateAgeStatus(AGE_DURITION_00);
                    } else if (po == 1) {
                        updateAgeStatus(AGE_DURITION_90);
                    } else if (po == 2) {
                        updateAgeStatus(AGE_DURITION_80);
                    } else if (po == 3) {
                        updateAgeStatus(AGE_DURITION_70);
                    } else if (po == 4) {
                        updateAgeStatus(AGE_DURITION_60);
                    } else if (po == 5) {
                        updateAgeStatus(AGE_DURITION_50);
                    }
                }
            });
        }
    }


    private void findInterstingView(View view) {
        constraintLayout = (ConstraintLayout) view.findViewById(R.id.intesting_cslayout);

        mRecommendTvOne = (TextView) view.findViewById(R.id.recommend_text_one);
        mRecommendTvTwo = (TextView) view.findViewById(R.id.recommend_text_two);
        mRecommendTvThree = (TextView) view.findViewById(R.id.recommend_text_three);
        mRecommendTvFour = (TextView) view.findViewById(R.id.recommend_text_four);
        mRecommendTvFive = (TextView) view.findViewById(R.id.recommend_text_five);
        mRecommendTvSix = (TextView) view.findViewById(R.id.recommend_text_six);
        mRecommendTvSeven = (TextView) view.findViewById(R.id.recommend_text_seven);
        mRecommendTvEight = (TextView) view.findViewById(R.id.recommend_text_eight);
        mRecommendTvNice = (TextView) view.findViewById(R.id.recommend_text_nice);
        mRecommendTvTen = (TextView) view.findViewById(R.id.recommend_text_ten);
        mRecommendTvSubmit = (TextView) view.findViewById(R.id.interstedSubmit);

        mRecommendIvOne = (ImageView) view.findViewById(R.id.recommend_image_one);
        mRecommendIvTwo = (ImageView) view.findViewById(R.id.recommend_image_two);
        mRecommendIvThree = (ImageView) view.findViewById(R.id.recommend_image_three);
        mRecommendIvFour = (ImageView) view.findViewById(R.id.recommend_image_four);
        mRecommendIvFive = (ImageView) view.findViewById(R.id.recommend_image_five);
        mRecommendIvSix = (ImageView) view.findViewById(R.id.recommend_image_six);
        mRecommendIvSeven = (ImageView) view.findViewById(R.id.recommend_image_seven);
        mRecommendIvEight = (ImageView) view.findViewById(R.id.recommend_image_eight);
        mRecommendIvNice = (ImageView) view.findViewById(R.id.recommend_image_nice);
        mRecommendIvTen = (ImageView) view.findViewById(R.id.recommend_image_ten);

        IntestingViews.add(mRecommendIvOne);
        IntestingViews.add(mRecommendIvTwo);
        IntestingViews.add(mRecommendIvThree);
        IntestingViews.add(mRecommendIvFour);
        IntestingViews.add(mRecommendIvFive);
        IntestingViews.add(mRecommendIvSix);
        IntestingViews.add(mRecommendIvSeven);
        IntestingViews.add(mRecommendIvEight);
        IntestingViews.add(mRecommendIvNice);
        IntestingViews.add(mRecommendIvTen);

        for (int i = 0; i < IntestingViews.size(); i++) {
            int position = i;
            IntestingViews.get(i).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    String title = mInterestTags.get(position).getName();
                    if (!tagList.contains(title)) {
                        tagList.add(title);
                        IntestingViews.get(position).setImageDrawable(ResUtil.getDrawable(R.drawable.pc_bg_select));
                    } else {
                        IntestingViews.get(position).setImageDrawable(ResUtil.getDrawable(R.drawable.pc_bg_unselect));
                        tagList.remove(title);
                    }
                }
            });
        }

        mRecommendTvSubmit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (AntiShake.check(v.getId())) {
                    return;
                }
                if (tagList.size() == 0) {
                    ToastUtil.showOnly(getContext(), getResources().getString(R.string.slect_intersting));
                } else {
                    if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
                        ToastUtil.showOnly(getContext(), ResUtil.getString(R.string.no_net_work_str));
                        return;
                    } else {
                        mRecommendTvSubmit.setClickable(false);
                        //  保存兴趣标签
                        mPresenter.saveInterestTags(tagList);
                    }
                }
            }
        });
    }

    private void findInterstingPortView(View view) {
        constraintPortLayout = (ConstraintLayout) view.findViewById(R.id.intesting_cslayout_port);
        mRecommendTvOnePort = (TextView) view.findViewById(R.id.recommend_text_one_port);
        mRecommendTvTwoPort = (TextView) view.findViewById(R.id.recommend_text_two_port);
        mRecommendTvThreePort = (TextView) view.findViewById(R.id.recommend_text_three_port);
        mRecommendTvFourPort = (TextView) view.findViewById(R.id.recommend_text_four_port);
        mRecommendTvFivePort = (TextView) view.findViewById(R.id.recommend_text_five_port);
        mRecommendTvSixPort = (TextView) view.findViewById(R.id.recommend_text_six_port);
        mRecommendTvSevenPort = (TextView) view.findViewById(R.id.recommend_text_seven_port);
        mRecommendTvEightPort = (TextView) view.findViewById(R.id.recommend_text_eight_port);
        mRecommendTvNicePort = (TextView) view.findViewById(R.id.recommend_text_nice_port);
        mRecommendTvTenPort = (TextView) view.findViewById(R.id.recommend_text_ten_port);
        mRecommendTvSubmitPort = (TextView) view.findViewById(R.id.interstedSubmit_port);

        mRecommendIvOnePort = (ImageView) view.findViewById(R.id.recommend_image_one_port);
        mRecommendIvTwoPort = (ImageView) view.findViewById(R.id.recommend_image_two_port);
        mRecommendIvThreePort = (ImageView) view.findViewById(R.id.recommend_image_three_port);
        mRecommendIvFourPort = (ImageView) view.findViewById(R.id.recommend_image_four_port);
        mRecommendIvFivePort = (ImageView) view.findViewById(R.id.recommend_image_five_port);
        mRecommendIvSixPort = (ImageView) view.findViewById(R.id.recommend_image_six_port);
        mRecommendIvSevenPort = (ImageView) view.findViewById(R.id.recommend_image_seven_port);
        mRecommendIvEightPort = (ImageView) view.findViewById(R.id.recommend_image_eight_port);
        mRecommendIvNicePort = (ImageView) view.findViewById(R.id.recommend_image_nice_port);
        mRecommendIvTenPort = (ImageView) view.findViewById(R.id.recommend_image_ten_port);

        IntestingPortViews.add(mRecommendIvOnePort);
        IntestingPortViews.add(mRecommendIvTwoPort);
        IntestingPortViews.add(mRecommendIvThreePort);
        IntestingPortViews.add(mRecommendIvFourPort);
        IntestingPortViews.add(mRecommendIvFivePort);
        IntestingPortViews.add(mRecommendIvSixPort);
        IntestingPortViews.add(mRecommendIvSevenPort);
        IntestingPortViews.add(mRecommendIvEightPort);
        IntestingPortViews.add(mRecommendIvNicePort);
        IntestingPortViews.add(mRecommendIvTenPort);

        for (int i = 0; i < IntestingPortViews.size(); i++) {
            int position = i;
            IntestingPortViews.get(i).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    String title = mInterestTags.get(position).getName();
                    if (!tagList.contains(title)) {
                        tagList.add(title);
                        IntestingPortViews.get(position).setImageDrawable(ResUtil.getDrawable(R.drawable.pc_bg_select));
                    } else {
                        IntestingPortViews.get(position).setImageDrawable(ResUtil.getDrawable(R.drawable.pc_bg_unselect));
                        tagList.remove(title);
                    }
                }
            });
        }

        mRecommendTvSubmitPort.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (AntiShake.check(v.getId())) {
                    return;
                }
                if (tagList.size() == 0) {
                    ToastUtil.showOnly(getContext(), getResources().getString(R.string.slect_intersting));
                } else {
                    if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
                        ToastUtil.showOnly(getContext(), ResUtil.getString(R.string.no_net_work_str));
                        return;
                    } else {
                        mRecommendTvSubmitPort.setClickable(false);
                        //  保存兴趣标签
                        mPresenter.saveInterestTags(tagList);
                    }
                }
            }
        });
    }

    @Override
    public void hideLoading() {

    }

    @Override
    public void showError(String s) {
        showOrHideLoadingView(false);
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001780236930?userId=2169710问题
        ViewUtil.setViewVisibility(mHomeNoNetWorkRl, View.VISIBLE);
        ViewUtil.setViewVisibility(constraintLayout, View.GONE);
        ViewUtil.setViewVisibility(constraintPortLayout, View.GONE);
        ViewUtil.setViewVisibility(mPcIvBack, View.GONE);
        ViewUtil.setViewVisibility(mPcJump, View.VISIBLE);
        if (mNetworknosigin != null) {
            mNetworknosigin.setImageResource(R.drawable.timerout);
//        mNetworknosigin.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                showOrHideLoadingView(true);
//                mHomeNoNetWorkRl.setVisibility(View.INVISIBLE);
//                goRecommendation();
//            }
//        });
            if (s.equals("604")) {
//            网络超时
                mTvNetworkNosign.setText(R.string.home_network_timerout);
            } else {
                mTvNetworkNosign.setText(R.string.home_network_failed);
            }
            mNetworknosigin.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    showOrHideLoadingView(true);
                    ViewUtil.setViewVisibility(mHomeNoNetWorkRl, View.INVISIBLE);
                    goRecommendation();
                    mPresenter.getInterestTagList();
                    String text = mTvNetworkNosign.getText().toString();
                    ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_NETWORK_RETRY, text, getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
                }
            });
        }
        String text = mTvNetworkNosign.getText().toString();
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_NETWORK_RETRY, text, getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
    }

    private void showOrHideLoadingView(boolean isShow) {
        ViewUtil.setViewVisibility(mPcLoadingView, isShow ? View.VISIBLE : View.GONE);
    }
}