package com.kaolafm.kradio.flavor.impl;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.k_kaolafm.home.item.BroadcastCell;
import com.kaolafm.kradio.k_kaolafm.home.item.FunctionPairCell;
import com.kaolafm.kradio.k_kaolafm.home.item.SpaceCell;
import com.kaolafm.kradio.lib.base.flavor.KRadioHomeCellInter;
import com.kaolafm.opensdk.api.operation.model.column.BroadcastDetailColumnMember;

import java.util.List;

/**
 * @Package: com.kaolafm.kradio.flavor.impl
 * @Description:
 * @Author: Maclay
 * @Date: 15:33
 */
public class KRadioHomeCellImpl implements KRadioHomeCellInter {
    @Override
    public void computeCellSpace(int groupIndex, List cells) {
        if (groupIndex > 0 && !ListUtil.isEmpty(cells)) {
            if (!(cells.get(0) instanceof FunctionPairCell)
                    && !(cells.get(0) instanceof BroadcastDetailColumnMember)) {
                cells.add(0, new SpaceCell());
            }
        }
    }
}
