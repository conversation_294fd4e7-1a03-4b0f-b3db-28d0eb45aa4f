package com.kaolafm.kradio.component.ui.topiccard;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.Adapter;
import androidx.recyclerview.widget.RecyclerView.ItemDecoration;
import androidx.recyclerview.widget.RecyclerView.LayoutManager;
import androidx.recyclerview.widget.RecyclerView.State;
import android.text.Html;
import android.text.TextUtils;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup.LayoutParams;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import com.kaolafm.kradio.component.ui.R.dimen;
import com.kaolafm.kradio.component.ui.R.id;
import com.kaolafm.kradio.component.ui.R.layout;
import com.kaolafm.kradio.component.ui.base.CellBinder;
import com.kaolafm.kradio.component.ui.base.ItemClickSupport;
import com.kaolafm.kradio.component.ui.base.ItemOhterClickSupport;
import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.component.ui.base.utils.ComponentUtils;
import com.kaolafm.kradio.component.ui.base.view.KradioTextView;
import com.kaolafm.kradio.component.ui.base.view.OvalImageView;
import com.kaolafm.kradio.component.ui.topiccard.adapter.ComponentPostsAdapter;
import com.kaolafm.kradio.component.ui.topiccard.adapter.ComponentPostsAdapter.OnItemClickedListener;
import com.kaolafm.kradio.component.ui.topiccard.adapter.ComponentPostsAdapter.OnItemLikeIconClickedListener;
import com.kaolafm.kradio.lib.base.ui.BaseHolder.OnViewCOtherlickListener;
import com.kaolafm.kradio.lib.base.ui.BaseHolder.OnViewClickListener;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.UrlUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.opensdk.api.operation.model.column.ColumnContent;
import com.kaolafm.opensdk.api.topic.model.TopicPosts;
import kotlin.TypeCastException;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class ComponentTopicCardCell extends HomeCell implements CellBinder, ItemClickSupport, ItemOhterClickSupport {
    public OvalImageView card_bg_iv;
    public TextView radio_topic_tag_tv;
    public TextView radio_topic_name_tv;
    public TextView radio_num_tv;
    public KradioTextView radio_topic_des_tv;
    public RecyclerView tipic_min_card_rv;
    public RelativeLayout radio_topic_rl;
    @Nullable
    private OnViewClickListener listener;
    @Nullable
    private OnViewCOtherlickListener ohterListener;
    public ComponentPostsAdapter componentPostsAdapter;

    @NotNull
    public final OvalImageView getCard_bg_iv() {
        OvalImageView var10000 = this.card_bg_iv;
        if (var10000 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("card_bg_iv");
        }

        return var10000;
    }

    public final void setCard_bg_iv(@NotNull OvalImageView var1) {
        Intrinsics.checkParameterIsNotNull(var1, "<set-?>");
        this.card_bg_iv = var1;
    }

    @NotNull
    public final TextView getRadio_topic_tag_tv() {
        TextView var10000 = this.radio_topic_tag_tv;
        if (var10000 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("radio_topic_tag_tv");
        }

        return var10000;
    }

    public final void setRadio_topic_tag_tv(@NotNull TextView var1) {
        Intrinsics.checkParameterIsNotNull(var1, "<set-?>");
        this.radio_topic_tag_tv = var1;
    }

    @NotNull
    public final TextView getRadio_topic_name_tv() {
        TextView var10000 = this.radio_topic_name_tv;
        if (var10000 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("radio_topic_name_tv");
        }

        return var10000;
    }

    public final void setRadio_topic_name_tv(@NotNull TextView var1) {
        Intrinsics.checkParameterIsNotNull(var1, "<set-?>");
        this.radio_topic_name_tv = var1;
    }

    @NotNull
    public final TextView getRadio_num_tv() {
        TextView var10000 = this.radio_num_tv;
        if (var10000 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("radio_num_tv");
        }

        return var10000;
    }

    public final void setRadio_num_tv(@NotNull TextView var1) {
        Intrinsics.checkParameterIsNotNull(var1, "<set-?>");
        this.radio_num_tv = var1;
    }

    @NotNull
    public final KradioTextView getRadio_topic_des_tv() {
        KradioTextView var10000 = this.radio_topic_des_tv;
        if (var10000 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("radio_topic_des_tv");
        }

        return var10000;
    }

    public final void setRadio_topic_des_tv(@NotNull KradioTextView var1) {
        Intrinsics.checkParameterIsNotNull(var1, "<set-?>");
        this.radio_topic_des_tv = var1;
    }

    @NotNull
    public final RecyclerView getTipic_min_card_rv() {
        RecyclerView var10000 = this.tipic_min_card_rv;
        if (var10000 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("tipic_min_card_rv");
        }

        return var10000;
    }

    public final void setTipic_min_card_rv(@NotNull RecyclerView var1) {
        Intrinsics.checkParameterIsNotNull(var1, "<set-?>");
        this.tipic_min_card_rv = var1;
    }

    @NotNull
    public final RelativeLayout getRadio_topic_rl() {
        RelativeLayout var10000 = this.radio_topic_rl;
        if (var10000 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("radio_topic_rl");
        }

        return var10000;
    }

    public final void setRadio_topic_rl(@NotNull RelativeLayout var1) {
        Intrinsics.checkParameterIsNotNull(var1, "<set-?>");
        this.radio_topic_rl = var1;
    }

    @Nullable
    public final OnViewClickListener getListener() {
        return this.listener;
    }

    public final void setListener(@Nullable OnViewClickListener var1) {
        this.listener = var1;
    }

    @Nullable
    public final OnViewCOtherlickListener getOhterListener() {
        return this.ohterListener;
    }

    public final void setOhterListener(@Nullable OnViewCOtherlickListener var1) {
        this.ohterListener = var1;
    }

    @NotNull
    public final ComponentPostsAdapter getComponentPostsAdapter() {
        ComponentPostsAdapter var10000 = this.componentPostsAdapter;
        if (var10000 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("componentPostsAdapter");
        }

        return var10000;
    }

    public final void setComponentPostsAdapter(@NotNull ComponentPostsAdapter var1) {
        Intrinsics.checkParameterIsNotNull(var1, "<set-?>");
        this.componentPostsAdapter = var1;
    }

    public int getItemType() {
        return layout.component_tipic_card_layout;
    }

    public final void initView(@NotNull View view) {
        Intrinsics.checkParameterIsNotNull(view, "view");
        View var10001 = view.findViewById(id.card_bg_iv);
        Intrinsics.checkExpressionValueIsNotNull(var10001, "view.findViewById(R.id.card_bg_iv)");
        this.card_bg_iv = (OvalImageView)var10001;
        var10001 = view.findViewById(id.radio_topic_tag_tv);
        Intrinsics.checkExpressionValueIsNotNull(var10001, "view.findViewById(R.id.radio_topic_tag_tv)");
        this.radio_topic_tag_tv = (TextView)var10001;
        var10001 = view.findViewById(id.radio_topic_name_tv);
        Intrinsics.checkExpressionValueIsNotNull(var10001, "view.findViewById(R.id.radio_topic_name_tv)");
        this.radio_topic_name_tv = (TextView)var10001;
        var10001 = view.findViewById(id.radio_num_tv);
        Intrinsics.checkExpressionValueIsNotNull(var10001, "view.findViewById(R.id.radio_num_tv)");
        this.radio_num_tv = (TextView)var10001;
        var10001 = view.findViewById(id.radio_topic_des_tv);
        Intrinsics.checkExpressionValueIsNotNull(var10001, "view.findViewById(R.id.radio_topic_des_tv)");
        this.radio_topic_des_tv = (KradioTextView)var10001;
        var10001 = view.findViewById(id.tipic_min_card_rv);
        Intrinsics.checkExpressionValueIsNotNull(var10001, "view.findViewById(R.id.tipic_min_card_rv)");
        this.tipic_min_card_rv = (RecyclerView)var10001;
        var10001 = view.findViewById(id.radio_topic_rl);
        Intrinsics.checkExpressionValueIsNotNull(var10001, "view.findViewById(R.id.radio_topic_rl)");
        this.radio_topic_rl = (RelativeLayout)var10001;
    }

    public void mountView(@NotNull ComponentTopicCardCell data, @NotNull final View view, int position) {
        Intrinsics.checkParameterIsNotNull(data, "data");
        Intrinsics.checkParameterIsNotNull(view, "view");
        if (!data.getContentList().isEmpty()) {
            this.initView(view);
            TextView var10000 = this.radio_topic_name_tv;
            if (var10000 == null) {
                Intrinsics.throwUninitializedPropertyAccessException("radio_topic_name_tv");
            }

            Object var10001 = data.getContentList().get(0);
            Intrinsics.checkExpressionValueIsNotNull(var10001, "data.getContentList()[0]");
            var10000.setText((CharSequence)((ColumnContent)var10001).getTitle());
            KradioTextView var16 = this.radio_topic_des_tv;
            if (var16 == null) {
                Intrinsics.throwUninitializedPropertyAccessException("radio_topic_des_tv");
            }

            var10001 = data.getContentList().get(0);
            Intrinsics.checkExpressionValueIsNotNull(var10001, "data.getContentList()[0]");
            var16.setText((CharSequence)((ColumnContent)var10001).getSubtitle());
            var10000 = this.radio_topic_tag_tv;
            if (var10000 == null) {
                Intrinsics.throwUninitializedPropertyAccessException("radio_topic_tag_tv");
            }

            var10001 = data.getContentList().get(0);
            Intrinsics.checkExpressionValueIsNotNull(var10001, "data.getContentList()[0]");
            CharSequence topicTagText = ((ColumnContent) var10001).getTag();
            var10000.setText(topicTagText);
            var10000.setVisibility(TextUtils.isEmpty(topicTagText) ? View.GONE : View.VISIBLE);
            var10000 = this.radio_topic_tag_tv;
            if (var10000 == null) {
                Intrinsics.throwUninitializedPropertyAccessException("radio_topic_tag_tv");
            }

            Drawable var17 = var10000.getBackground();
            if (var17 == null) {
                throw new TypeCastException("null cannot be cast to non-null type android.graphics.drawable.GradientDrawable");
            } else {
                GradientDrawable gradientDrawable = (GradientDrawable)var17;

                boolean var7;
                Object var18;
                try {
                    var7 = false;
                    var18 = data.getContentList().get(0);
                    Intrinsics.checkExpressionValueIsNotNull(var18, "data.getContentList()[0]");
                    if (!TextUtils.isEmpty((CharSequence)((ColumnContent)var18).getTagColor())) {
                        var10001 = data.getContentList().get(0);
                        Intrinsics.checkExpressionValueIsNotNull(var10001, "data.getContentList()[0]");
                        gradientDrawable.setColor(Color.parseColor(((ColumnContent)var10001).getTagColor()));
                    }
                } catch (Exception var12) {
                    Logger.e("ComponentTopicCardCell",var12.getMessage());
                }

                String url = UrlUtil.getCardBgUrl(data.imageFiles);
                if (!TextUtils.isEmpty((CharSequence)url)) {
                    ImageLoader var19 = ImageLoader.getInstance();
                    OvalImageView var20 = this.card_bg_iv;
                    if (var20 == null) {
                        Intrinsics.throwUninitializedPropertyAccessException("card_bg_iv");
                    }

                    Context var21 = var20.getContext();
                    OvalImageView var10003 = this.card_bg_iv;
                    if (var10003 == null) {
                        Intrinsics.throwUninitializedPropertyAccessException("card_bg_iv");
                    }

                    var19.displayImage(var21, url, (ImageView)var10003);
                }

                var10000 = this.radio_num_tv;
                if (var10000 == null) {
                    Intrinsics.throwUninitializedPropertyAccessException("radio_num_tv");
                }

                TextView var13 = var10000;
                var7 = false;
                boolean isBigCard = false;
                StringBuilder var22 = new StringBuilder();
                ComponentUtils var23 = ComponentUtils.getInstance();
                Object var10002 = data.getContentList().get(0);
                Intrinsics.checkExpressionValueIsNotNull(var10002, "data.getContentList()[0]");
                var22 = var22.append(var23.formatNumber(((ColumnContent)var10002).getUserCount(), 1)).append("<font size=").append(ResUtil.getDimen(dimen.m18)).append(">人参与</font> ");
                var23 = ComponentUtils.getInstance();
                var10002 = data.getContentList().get(0);
                Intrinsics.checkExpressionValueIsNotNull(var10002, "data.getContentList()[0]");
                String numText = var22.append(var23.formatNumber(((ColumnContent)var10002).getReadCount(), 1)).append("<font size=").append(ResUtil.getDimen(dimen.m18)).append(">阅读量</font>").toString();
                var13.setText((CharSequence)Html.fromHtml(numText));
                var18 = data.getContentList().get(0);
                Intrinsics.checkExpressionValueIsNotNull(var18, "data.getContentList()[0]");
                LayoutParams layoutParams = card_bg_iv.getLayoutParams();
                LayoutParams topicLayoutParams = radio_topic_rl.getLayoutParams();
                int layoutWidth = 0;
                // 话题大卡
                if (data.getComponentType() == 11) {
                    layoutWidth = ResUtil.getDimen(dimen.m900);
                    radio_num_tv.setVisibility(View.VISIBLE);
                    isBigCard = true;
                } else {
                    layoutWidth = ResUtil.getDimen(dimen.m440);
                    radio_num_tv.setVisibility(View.GONE);
                    isBigCard = false;
                }
                layoutParams.width = layoutWidth;
                topicLayoutParams.width = layoutWidth;

                if (((ColumnContent)var18).getPosts() != null) {
                    var18 = data.getContentList().get(0);
                    Intrinsics.checkExpressionValueIsNotNull(var18, "data.getContentList()[0]");
                    if (((ColumnContent)var18).getPosts().size() > 0) {
                        OvalImageView var27 = this.card_bg_iv;
                        if (var27 == null) {
                            Intrinsics.throwUninitializedPropertyAccessException("card_bg_iv");
                        }
                        RecyclerView var31;
                        if (data.getComponentType() == 11) {
                            var31 = this.tipic_min_card_rv;
                            if (var31 == null) {
                                Intrinsics.throwUninitializedPropertyAccessException("tipic_min_card_rv");
                            }

                            if (var31.getItemDecorationCount() == 0) {
                                var31 = this.tipic_min_card_rv;
                                if (var31 == null) {
                                    Intrinsics.throwUninitializedPropertyAccessException("tipic_min_card_rv");
                                }

                                var31.addItemDecoration((ItemDecoration)(new ItemDecoration() {
                                    public void getItemOffsets(@Nullable Rect outRect, @Nullable View view, @Nullable RecyclerView parent, @Nullable State state) {
                                        super.getItemOffsets(outRect, view, parent, state);
                                        if (parent == null) {
                                            Intrinsics.throwNpe();
                                        }

                                        int pos = parent.getChildLayoutPosition(view);
                                        if (pos == 0) {
                                            if (outRect == null) {
                                                Intrinsics.throwNpe();
                                            }

                                            outRect.right = ResUtil.getDimen(dimen.m20);
                                        }

                                    }
                                }));
                            }
                        }
                        card_bg_iv.setLayoutParams(layoutParams);
                        radio_topic_rl.setLayoutParams(topicLayoutParams);
                        Object var24 = data.getContentList().get(0);
                        Intrinsics.checkExpressionValueIsNotNull(var24, "data.getContentList()[0]");
                        this.componentPostsAdapter = new ComponentPostsAdapter(((ColumnContent)var24).getPosts(), isBigCard, (OnItemLikeIconClickedListener)(new OnItemLikeIconClickedListener() {
                            public void onItemLikeClicked(@NotNull TopicPosts mTopicPosts, int position) {
                                Intrinsics.checkParameterIsNotNull(mTopicPosts, "mTopicPosts");
                                if (ComponentTopicCardCell.this.getOhterListener() != null) {
                                    OnViewCOtherlickListener var10000 = ComponentTopicCardCell.this.getOhterListener();
                                    if (var10000 == null) {
                                        Intrinsics.throwNpe();
                                    }

                                    var10000.onViewOtherClick(view, ComponentTopicCardCell.this.getPositionInParent(), mTopicPosts);
                                }

                            }
                        }), (OnItemClickedListener)(new OnItemClickedListener() {
                            public void onItemClicked(@NotNull View view, int position) {
                                Intrinsics.checkParameterIsNotNull(view, "view");
                                if (ComponentTopicCardCell.this.getListener() != null) {
                                    view.setTag(0);
                                    OnViewClickListener var10000 = ComponentTopicCardCell.this.getListener();
                                    if (var10000 == null) {
                                        Intrinsics.throwNpe();
                                    }

                                    var10000.onViewClick(view, ComponentTopicCardCell.this.getPositionInParent());
                                }

                            }
                        }));
                        var31 = this.tipic_min_card_rv;
                        if (var31 == null) {
                            Intrinsics.throwUninitializedPropertyAccessException("tipic_min_card_rv");
                        }

                        LinearLayoutManager var25 = new LinearLayoutManager(tipic_min_card_rv.getContext(), LinearLayoutManager.HORIZONTAL, false);
                        RecyclerView var26 = this.tipic_min_card_rv;
                        if (var26 == null) {
                            Intrinsics.throwUninitializedPropertyAccessException("tipic_min_card_rv");
                        }

                        var31.setLayoutManager((LayoutManager)var25);
                        var31 = this.tipic_min_card_rv;
                        if (var31 == null) {
                            Intrinsics.throwUninitializedPropertyAccessException("tipic_min_card_rv");
                        }

                        var31.requestDisallowInterceptTouchEvent(true);
                        var31 = this.tipic_min_card_rv;
                        if (var31 == null) {
                            Intrinsics.throwUninitializedPropertyAccessException("tipic_min_card_rv");
                        }

                        ComponentPostsAdapter var28 = this.componentPostsAdapter;
                        if (var28 == null) {
                            Intrinsics.throwUninitializedPropertyAccessException("componentPostsAdapter");
                        }

                        var31.setAdapter((Adapter)var28);
                    }
                }

                view.setOnClickListener((OnClickListener)(new OnClickListener() {
                    public final void onClick(View it) {
                        if (ComponentTopicCardCell.this.getListener() != null) {
                            view.setTag(0);
                            OnViewClickListener var10000 = ComponentTopicCardCell.this.getListener();
                            if (var10000 == null) {
                                Intrinsics.throwNpe();
                            }

                            var10000.onViewClick(it, ComponentTopicCardCell.this.getPositionInParent());
                        }

                    }
                }));
            }
        }
    }


    public void mountView(Object var1, View var2, int var3) {
        this.mountView((ComponentTopicCardCell)var1, var2, var3);
    }

    public void setOnItemClickListener(@Nullable OnViewClickListener listener) {
        this.listener = listener;
    }

    public void setOnItemOhterClickListener(@Nullable OnViewCOtherlickListener listener) {
        this.ohterListener = listener;
    }
}
