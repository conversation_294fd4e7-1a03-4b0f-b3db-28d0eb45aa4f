package com.kaolafm.kradio.online.mine;

import android.content.Intent;
import android.os.Bundle;
import android.os.Parcelable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentPagerAdapter;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.kaolafm.kradio.basedb.GreenDaoInterface;
import com.kaolafm.kradio.basedb.entity.meaasge.CrashMessageBean;
import com.kaolafm.kradio.basedb.manager.MessageDaoManager;
import com.kaolafm.kradio.common.base.BaseSkinAppCompatActivity;
import com.kaolafm.kradio.common.widget.NotScrollViewPager;
import com.kaolafm.kradio.component.ComponentClient;
import com.kaolafm.kradio.component.DynamicComponent;
import com.kaolafm.kradio.component.MainThreadable;
import com.kaolafm.kradio.component.RealCaller;
import com.kaolafm.kradio.constant.UserComponentConst;
import com.kaolafm.kradio.constant.UserStateObserverProcessorConst;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.mvp.IPresenter;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.ComponentUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkManager;
import com.kaolafm.kradio.lib.widget.tab.OnTabSelectListener;
import com.kaolafm.kradio.lib.widget.tab.SlidingTabLayout;
import com.kaolafm.kradio.lib.widget.tab.Tab;
import com.kaolafm.kradio.online.common.event.OnlineMessageEvent;
import com.kaolafm.kradio.common.router.IRouterConsumer;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.kradio.online.mine.page.OnlineAboutFragment;
import com.kaolafm.kradio.online.mine.page.OnlineDurationFragment;
import com.kaolafm.kradio.online.mine.page.OnlineMembersFragment;
import com.kaolafm.kradio.online.mine.page.OnlinePurchasedFragment;
import com.kaolafm.kradio.online.mine.page.OnlineSettingFragment;
import com.kaolafm.kradio.online.mine.page.OnlineUserFragment;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.purchase.PayManager;
import com.kaolafm.kradio.purchase.constant.PayConst;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.report.util.ReportParameterManager;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

/**
 * 我的页面
 */
@Route(path = RouterConstance.ACTIVITY_URL_MINE)
public class MineActivity extends BaseSkinAppCompatActivity implements IRouterConsumer {
    private static final String TAG = "MineActivity";

    ImageView back_mine;
    SlidingTabLayout mine_tablayout;
    FrameLayout mine_user_fl;
    NotScrollViewPager mine_viewpage;
    RelativeLayout root_layout;
    ImageView badgeView;
    ConstraintLayout msgFloatRoot;

    private String[] tabTitle = {"会员中心", "收听时长", "已购", "设置", "关于云听"};
    private ArrayList<Fragment> fragmentList = new ArrayList<>();
    private OnlineUserFragment onlineUserFragment;
    private OnlineMembersFragment onlineMembersFragment;
    private OnlineDurationFragment onlineDurationFragment;
    private OnlinePurchasedFragment onlinePurchasedFragment;
    private OnlineSettingFragment onlineSettingFragment;
    private OnlineAboutFragment onlineAboutFragment;
    private DynamicComponent mRadioPlayerUserObserver;
    private NetWorkListener mNetWorkListener;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mRadioPlayerUserObserver = new RadioPlayerUserObserver();
        ComponentUtil.addObserver(UserComponentConst.NAME, mRadioPlayerUserObserver);
        mNetWorkListener = new NetWorkListener(this);
        NetworkManager.getInstance().addNetworkReadyListener(mNetWorkListener);
        RouterManager.getInstance().addRouterConsumer(this);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        interceptApplicationJumpEvent(intent);
    }

    private void interceptApplicationJumpEvent(Intent intent) {
        if (intent == null) intent = getIntent();
        String pageId = intent.getStringExtra(Constants.ROUTER_PARAMS_KEY_PAGE_ID);
        Parcelable extra = intent.getParcelableExtra(Constants.ROUTER_PARAMS_KEY_EXTRA);

        if (pageId != null) {
            consumeRoute(pageId, extra);
        }
    }

    @Override
    protected IPresenter createPresenter() {
        return null;
    }

    @Override
    public boolean useEventBus() {
        return true;
    }

    @Override
    public int getLayoutId() {
        return R.layout.online_activity_mine;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        back_mine = findViewById(R.id.back_mine);
        mine_tablayout = findViewById(R.id.mine_tablayout);
        mine_user_fl = findViewById(R.id.mine_user_fl);
        mine_viewpage = findViewById(R.id.mine_viewpage);
        root_layout = findViewById(R.id.root_layout);
        badgeView = findViewById(R.id.badgeView);
        msgFloatRoot = findViewById(R.id.msgFloatRoot);
        msgFloatRoot.setOnClickListener(v -> showMessageBubble(v));

        back_mine.setOnClickListener(v -> finish());

        interceptApplicationJumpEvent(null);
    }
    public void showMessageBubble(View v) {
        if (!AntiShake.check(v.getId())) {
            RouterManager.getInstance().jumpPage(RouterConstance.ACTIVITY_URL_MESSAGE);
        }
    }

    public void updateSink() {
        if (onlineUserFragment != null) {
            onlineUserFragment.updateLogin();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        changeBadgeView(new OnlineMessageEvent());
    }

    FragmentPagerAdapter fragmentPagerAdapter;

    @Override
    public void initData() {
        onlineUserFragment = OnlineUserFragment.newInstance();
        onlineMembersFragment = OnlineMembersFragment.newInstance();
        onlineDurationFragment = OnlineDurationFragment.newInstance();
        onlinePurchasedFragment = OnlinePurchasedFragment.newInstance();
        onlineSettingFragment = OnlineSettingFragment.newInstance();
        onlineAboutFragment = OnlineAboutFragment.newInstance();
        fragmentList.clear();
        fragmentList.add(onlineMembersFragment);
        fragmentList.add(onlineDurationFragment);
        fragmentList.add(onlinePurchasedFragment);
        fragmentList.add(onlineSettingFragment);
        fragmentList.add(onlineAboutFragment);

        loadRootFragment(R.id.mine_user_fl, onlineUserFragment);
//        mine_tablayout.setTextsize(ResUtil.getDimen(R.dimen.m24));
//        mine_tablayout.setTextSelectSize(ResUtil.getDimen(R.dimen.m28));
        mine_viewpage.setOffscreenPageLimit(0);
        mine_viewpage.setScanScroll(false);
        mine_viewpage.setAdapter(fragmentPagerAdapter = new FragmentPagerAdapter(getSupportFragmentManager()) {
            @Override
            public Fragment getItem(int position) {
                return fragmentList.get(position);
            }

            @Override
            public int getCount() {
                return fragmentList.size();
            }
        });
        mine_viewpage.setCurrentItem(0);
        mine_tablayout.setTabs(getTabList());
        mine_tablayout.setCurrentTab(0);
        mine_tablayout.setOnTabSelectListener(new OnTabSelectListener() {
            @Override
            public void onTabSelect(int position) {
                //点击上报  "会员中心", "收听时长", "已购", "设置", "关于云听"
                mine_viewpage.setCurrentItem(position);
                switch (tabTitle[position]) {
                    case "会员中心":
//                        ButtonClickReportEvent event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_MINE_CHANG);
//                        ReportHelper.getInstance().addEvent(event);
                        MinePageUtils.page = Constants.PAGE_ID_MINE_MEMBERS;
                        break;
                    case "收听时长":
//                        ButtonClickReportEvent event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_MINE_CHANG);
//                        ReportHelper.getInstance().addEvent(event);
                        fragmentList.get(position).onResume();
                        MinePageUtils.page = Constants.PAGE_ID_MINE_DURATION;
                        break;
                    case "已购":
//                        ButtonClickReportEvent event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_MINE_CHANG);
//                        ReportHelper.getInstance().addEvent(event);
                        MinePageUtils.page = Constants.ONLINE_PAGE_ID_MINE_PURCHASED;
                        break;
                    case "设置":
//                        ButtonClickReportEvent event2 = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_MINE_SETTING);
//                        ReportHelper.getInstance().addEvent(event2);
                        fragmentList.get(position).onResume();
                        MinePageUtils.page = Constants.ONLINE_PAGE_ID_MINE_SETTING;
                        break;
                    case "关于云听":
//                        ButtonClickReportEvent event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_MINE_CHANG);
//                        ReportHelper.getInstance().addEvent(event);
                        MinePageUtils.page = Constants.ONLINE_PAGE_ID_MINE_ABOUT;
                        break;

                }
            }

            @Override
            public void onTabReselect(int position) {

            }
        });
    }

    private List<Tab> getTabList() {
        List<Tab> list = new ArrayList<>();
        for (int i = 0; i < tabTitle.length; i++) {
            Tab tab = new Tab();
            tab.title = tabTitle[i];
            tab.position = i;
            tab.code = i + "";
            list.add(tab);
        }
        return list;
    }



    /**
     * 更新小红点
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void changeBadgeView(OnlineMessageEvent messageEvent) {
        MessageDaoManager.getInstance().queryMsgLook(new GreenDaoInterface.OnQueryListener<List<CrashMessageBean>>() {
            @Override
            public void onQuery(List<CrashMessageBean> crashMessageBeans) {
                if (crashMessageBeans != null && crashMessageBeans.size() > 0 && badgeView != null) {
                    badgeView.setVisibility(View.VISIBLE);
                } else {
                    badgeView.setVisibility(View.GONE);
                }
            }
        });

    }

    @Override
    protected void onDestroy() {
        RouterManager.getInstance().removeRouterConsumer(this);
        super.onDestroy();
        NetworkManager.getInstance().removeNetworkReadyListener(mNetWorkListener);
        ComponentUtil.removeObserver(UserComponentConst.NAME, mRadioPlayerUserObserver);
    }

    @Override
    public String consumeRoute(String pageId, Object extra) {
        int tabPosition = 0;
        switch (pageId) {
            case Constants.PAGE_ID_MINE_MEMBERS:    //用户中心--会员中心
                tabPosition = 0;
                break;
            case Constants.PAGE_ID_MINE_DURATION:   //用户中心--收听时长页面
                tabPosition = 1;
                break;
            case Constants.ONLINE_PAGE_ID_MINE_PURCHASED:   //用户中心-已购
                tabPosition = 2;
                break;
            case Constants.ONLINE_PAGE_ID_MINE_SETTING: //用户中心-设置
                tabPosition = 3;
                break;
            case Constants.ONLINE_PAGE_ID_MINE_ABOUT:   //用户中心-关于
                tabPosition = 4;
                break;
            case Constants.ONLINE_PAGE_ID_MINE_PURCHASED_MY:   //用户中心—已购—我的订单
                tabPosition = 0;
                onlineMembersFragment.showOrderFragment();
                break;
            case Constants.ONLINE_PAGE_ID_PAY_VIP: //VIP购买
                if (ComponentUtil.getResultValue(UserComponentConst.NAME, UserComponentConst.IS_USER_BOUND)) {
                    PayManager.getInstance()
                            .pay(PayConst.PAY_TYPE_VIP, PlayerManager.getInstance().getCurPlayItem());
                } else {
                    Bundle bundle = new Bundle();
                    bundle.putString("type", ReportParameterManager.getInstance().getPage());
                    RouterManager.getInstance().jumpPage(RouterConstance.ACTIVITY_URL_LOGIN, bundle);
                }
                return IRouterConsumer.ROUTER_CONSUME_FULLY;
        }
        if (mine_tablayout.getTabCount() > tabPosition)
            mine_tablayout.getTab(tabPosition).tabView.performClick();
        return IRouterConsumer.ROUTER_CONSUME_FULLY;
    }

    public static class NetWorkListener implements NetworkManager.INetworkReady {
        private WeakReference<MineActivity> mMineActivity;

        public NetWorkListener(MineActivity mineActivity) {
            mMineActivity = new WeakReference<>(mineActivity);
        }

        @Override
        public void networkChange(boolean hasNetwork) {
            Log.i("networkChange", "Network state changed, param [hasNetwork] value is : " + hasNetwork);
            if (!hasNetwork) {
                return;
            }

            if (mMineActivity.get() != null && mMineActivity.get().mPresenter != null) {
                mMineActivity.get().initData();
            }
        }
    }

    private class RadioPlayerUserObserver implements DynamicComponent, MainThreadable {

        @Override
        public String getName() {
            return "onlineMine-UserObserver";
        }

        @Override
        public boolean onCall(RealCaller caller) {
            String actionName = caller.actionName();
            switch (actionName) {
                case UserStateObserverProcessorConst.USER_LOGIN:
                    if (onlineMembersFragment != null) {
                        onlineMembersFragment.showMembersUI();
                    }
                    break;
                case UserStateObserverProcessorConst.USER_LOGOUT:
                    if (onlineMembersFragment != null) {
                        onlineMembersFragment.showMembersUI();
                    }
                    if (PlayerManager.getInstance().isPlaying()) {
                        //如果退出登录当前播放的资源是需要付费的就要刷新播放
                        switch (PlayerManager.getInstance().getCurPlayItem().getBuyType()) {
                            case AudioDetails.BUY_TYPE_AUDIO:
                            case AudioDetails.BUY_TYPE_ALBUM:
                            case AudioDetails.BUY_TYPE_VIP:
                                long mRadioId = Long.parseLong(PlayerManager.getInstance().getCurPlayItem().getRadioId());
                                int mRadioType = PlayerManager.getInstance().getCurPlayItem().getType();
                                PlayerManagerHelper.getInstance().restart(String.valueOf(mRadioId), mRadioType);
                                break;
                        }
                    }
                    break;
                default:
                    break;
            }
            return false;
        }

        @Override
        public Boolean shouldActionRunOnMainThread(String actionName, ComponentClient caller) {
            return true;
        }
    }

    private void replayAudioList() {
//        public static final int BUY_TYPE_FREE = 0;//免费
//        public static final int BUY_TYPE_AUDITION = 1;//试听
//        public static final int BUY_TYPE_AUDIO = 2;//单曲购买
//        public static final int BUY_TYPE_ALBUM = 3;//专辑购买
//        public static final int BUY_TYPE_VIP = 4;//vip购买
//        if (PlayerManager.getInstance().getCurPlayItem().getBuyType() == AudioDetails.BUY_TYPE_FREE) {
//            //只有付费的去刷新
//            return;
//        }
//        Log.d(TAG, "replayAudioList");
//        long mRadioId = getCurrentAlbumId();
//        int mRadioType = PlayerManager.getInstance().getCurPlayItem().getType();
//        PlayerManagerHelper.getInstance().restart(String.valueOf(mRadioId), mRadioType);
    }

    public long getCurrentAlbumId() {

        String rId = PlayerManager.getInstance().getCurPlayItem().getRadioId();
        long albumId = 0L;
        if (!TextUtils.isEmpty(rId) && TextUtils.isDigitsOnly(rId)) {
            try {
                albumId = Long.parseLong(rId);
            } catch (NumberFormatException nfe) {
                nfe.printStackTrace();
            }
        }
        return albumId;
    }
}