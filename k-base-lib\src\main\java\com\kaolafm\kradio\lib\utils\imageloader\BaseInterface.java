package com.kaolafm.kradio.lib.utils.imageloader;

import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;

import com.kaolafm.kradio.lib.utils.imageloader.listener.OnGetBitmapListener;

import java.io.File;

/**
 * 该接口包含 需要{@link ImageLoader}和{@link BaseImageLoaderStrategy}都实现的方法
 *
 * <AUTHOR>
 * @date 2018/5/8
 */

public interface BaseInterface {

    File getCacheDir(Context context);

    void clearMemoryCache(Context context);

    void clearDiskCache(Context context);

    Bitmap getBitmapFromCache(Context context, String url);

    Bitmap getBitmapFromCache(Context context, String url, int size);

    void getBitmapFromCache(Context context, String url, OnGetBitmapListener listener);

    void getBitmapFromCache(Context context, String url, int radius, OnGetBitmapListener listener);

    void getBitmapFromCache(Context context, int resId, int radius, OnGetBitmapListener listener);

    void pauseRequests(Context context);

    void resumeRequests(Context context);

    void pauseRequests(Activity activity);

    void resumeRequests(Activity activity);

    void pauseRequests(FragmentActivity fragmentActivity);

    void resumeRequests(FragmentActivity fragmentActivity);

    void pauseRequests(android.app.Fragment fragment);

    void resumeRequests(android.app.Fragment fragment);

    void pauseRequests(Fragment fragment);

    void resumeRequests(Fragment fragment);
}
