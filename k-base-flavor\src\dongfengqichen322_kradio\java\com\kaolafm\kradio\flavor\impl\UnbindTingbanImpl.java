package com.kaolafm.kradio.flavor.impl;


import android.util.Log;

import com.kaolafm.kradio.lib.base.flavor.UnbindTingban;
import com.kaolafm.opensdk.account.token.AccessTokenManager;

/**
 * 使用二维码登录前拦截
 *
 * <AUTHOR>
 **/
public class UnbindTingbanImpl implements UnbindTingban {
    private static final String TAG = "UnbindTingbanImpl";

    @Override
    public void unbindTingban(Object... args) {
        Log.i("novelot", "unbindTingban:解绑听伴.");
        AccessTokenManager.getInstance().logoutAll();
        UserInfoManager.getInstance().localLogout();
        if (args != null && args.length > 0) {
            ((AccountLoginModel.AccountCallBack) args[0]).onUnbindSuccess(true);
        }
    }
}