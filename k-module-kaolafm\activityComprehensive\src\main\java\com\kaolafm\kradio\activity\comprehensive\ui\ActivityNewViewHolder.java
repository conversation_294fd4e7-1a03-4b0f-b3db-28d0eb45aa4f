package com.kaolafm.kradio.activity.comprehensive.ui;

import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.base.utils.DateUtil;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.opensdk.api.activity.model.Activity;

public class ActivityNewViewHolder extends BaseHolder<Activity> {

    TextView titleTextView;
    TextView desTextView;
    ImageView item_activitys_bg_iv;
    ImageView pic_image;
    TextView date_activity;
    View qrExpireView;
    View qrExpireIcon;

    public ActivityNewViewHolder(View itemView) {
        super(itemView);
        titleTextView=itemView.findViewById(R.id.title_activity);
        desTextView=itemView.findViewById(R.id.des_activity);
        item_activitys_bg_iv=itemView.findViewById(R.id.item_activitys_bg_iv);
        pic_image=itemView.findViewById(R.id.pic_image);
        date_activity=itemView.findViewById(R.id.date_activity);
        qrExpireView=itemView.findViewById(R.id.qr_view_expire);
        qrExpireIcon=itemView.findViewById(R.id.qr_expire_icon);
    }

    @Override
    public void setupData(Activity activity, int position) {
        titleTextView.setText(activity.getName());
        desTextView.setText(activity.getDescription());
        if (!TextUtils.isEmpty(activity.getBackgroundUrl()))
            ImageLoader.getInstance().displayImage(AppDelegate.getInstance().getContext(),
                    activity.getBackgroundUrl(), item_activitys_bg_iv);


        String start = DateUtil.formatMillis("MM.dd", Long.parseLong(activity.getStartTime()));
        String end = DateUtil.formatMillis("MM.dd", Long.parseLong(activity.getEndTime()));
        date_activity.setText(start + " - " + end);
        date_activity.setVisibility(View.VISIBLE);
        pic_image.setVisibility(View.VISIBLE);


        if (activity.getStatus() == 1) {
            qrExpireView.setVisibility(View.GONE);
            qrExpireIcon.setVisibility(View.GONE);
//            titleTextView.setTextColor(ResUtil.getColor(R.color.color_2));
//            desTextView.setTextColor(ResUtil.getColor(R.color.color_2));
        } else {
            qrExpireView.setVisibility(View.VISIBLE);
            qrExpireIcon.setVisibility(View.VISIBLE);
//            titleTextView.setTextColor(ResUtil.getColor(R.color.color_2));
//            desTextView.setTextColor(ResUtil.getColor(R.color.color_2));
        }
    }
}
