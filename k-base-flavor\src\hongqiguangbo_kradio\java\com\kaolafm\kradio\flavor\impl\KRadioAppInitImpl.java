package com.kaolafm.kradio.flavor.impl;

import android.car.Car;
import android.car.MobileInfo;
import android.car.StaInfo;
import android.content.ComponentName;
import android.content.IntentFilter;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.util.Log;


import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioAppInitInter;
import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.ToneQualityHelper;

import java.util.List;

import faw.settingsif.SettingsIF;

public class KRadioAppInitImpl implements KRadioAppInitInter {
    private static final String TAG = "KRadioAppInitImpl";
    private Car mCar;
    public static SettingsIF settingsIF;
    static boolean isNetstatus = true;

    @Override
    public void onAppInit() {
        try {
            mCar = Car.createCar(AppDelegate.getInstance().getContext(), new ServiceConnection() {         // 创建Car把context和ServiceConnection传给Car
                @Override
                public void onServiceConnected(ComponentName name, IBinder service) {   // 连接Car成功会走这个回调函数，然后拿到settingsIF对象，调用相关接口（PS：后续操作必须在Car连接成功后处理）
                    Log.d(TAG, "onServiceConnected  ComponentName=" + name);
                    settingsIF = SettingsIF.getInstance(mCar);
                    if (settingsIF != null){
                        MobileInfo mobileInfo = settingsIF.queryMobileStatus();
                        int STALinkStatus = settingsIF.querySTALinkStatus();
                        Log.d(TAG, "getNetStatus mobileInfo: " + mobileInfo.toString() + ", STALinkStatus: " + STALinkStatus);
                        settingsIF.registerCallback(new SettingsIF.SettingsIFCallback() {
                            @Override
                            public void onTboxStatusNotification(int[] ints) {
                                Log.d(TAG,"TBox状态变化通知：" + ints.toString());
                            }

                            @Override
                            public void onTboxWifiStatusNotification(int i) {
                                Log.d(TAG,"Wifi开关状态变化通知：" + i);
                                if(i==0){
                                    isNetstatus=false;
                                }
                            }

                            @Override
                            public void onTboxStaConnectedStatusNotification(StaInfo staInfo) {
                                Log.d(TAG,"STA连接状态变化通知：" + staInfo.toString());
//                                Log.d(TAG,"onTboxStaConnectedStatusNotification  "+staInfo.toString());
                                isNetstatus = (1 == staInfo.mConnectStatus);

                            }

                            @Override
                            public void onTboxWifiListStatusNotification(List<StaInfo> list) {
                                Log.d(TAG,"Tbox扫描热点完成后发送通知");
                            }

                            @Override
                            public void onTboxConnResultStatusNotification(int i) {
                                Log.d(TAG,"连接热点操作结果反馈：" + i);
                            }

                            @Override
                            public void onTboxMobileStatusNotification(MobileInfo mobileInfo) {
                                Log.d(TAG,"数据网络状态变化通知：" + mobileInfo.toString());
                            }
                        });


                    }else {
                        Log.d(TAG,"Can not get SettingsIF!!!");
                    }

                }

                @Override
                public void onServiceDisconnected(ComponentName name) { //连接Car失败，可以做重连处理，也可以不处理（理论Car会自动重连）
                    Log.d(TAG, "onServiceDisconnected  ComponentName=" + name);
                }
            });
            mCar.connect();     // 连接Car
        } catch (Throwable e) {
            e.printStackTrace();
        }
//        IntentFilter intentFilter=new IntentFilter();
//        intentFilter.addAction(Config.NET_STATUS_CHANGE);
//        intentFilter.addAction(Config.WiFI_NET_STATUS_CHANGE);
//        intentFilter.addAction(Config.VEHICLESDK_NET_STATUS_CHANGE);
//        intentFilter.addAction(Config.VEHICLESDK_WiFI_NET_STATUS_CHANGE);
//        AppDelegate.getInstance().getContext().registerReceiver(netWorkBroadcastReceiver,intentFilter);
        PlayerManager playerManager = PlayerManager.getInstance();
        if (playerManager.isPlayerInitSuccess()) {
            ToneQualityHelper.getInstance().setToneQuality(PlayerConstants.ToneQuality.LOW_TONE_QUALITY);
        } else {
            playerManager.addPlayerInitComplete(new IPlayerInitCompleteListener() {
                @Override
                public void onPlayerInitComplete(boolean b) {
                    ToneQualityHelper.getInstance().setToneQuality(PlayerConstants.ToneQuality.LOW_TONE_QUALITY);
                }
            });
        }
    }

}
