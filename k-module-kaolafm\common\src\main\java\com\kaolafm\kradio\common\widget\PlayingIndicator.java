package com.kaolafm.kradio.common.widget;

import android.animation.ValueAnimator;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;
import android.view.animation.LinearInterpolator;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.sdk.utils.PerformanceSettingMananger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.ListIterator;

/**
 * 播放跳动动画
 *
 * <AUTHOR>
 * @date 2018/1/18
 */
public class PlayingIndicator extends View {
    public static String TAG = PlayingIndicator.class.getSimpleName();
    private boolean canRun = true;

    /**
     * true表示从中间向两边缩放，false表示从下往上缩放。
     */
    private boolean isTwoWay;

    /**
     * 指针最低高度
     */
    private int minHeight;

    private Paint paint;

    private int stepNum;

    private int duration;

    private int barNum;

    private int barColor = 0xff000000;

    private int viewHeight;

    private float viewWidth;

    static private List<ValueAnimator> animList;

    private List<Float> animStopList;

    //private List<Float> mFloat;

    static private ValueAnimator mAnim;

    public float mSpaceWidth;

    public float mBarWidth;

    private double mSumWidth;

    public PlayingIndicator(Context context) {
        this(context, null);
    }

    public PlayingIndicator(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public PlayingIndicator(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    private void init(Context context, @Nullable AttributeSet attrs) {
        TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.PlayingIndicator, 0, 0);
        try {
            barNum = ta.getInt(R.styleable.PlayingIndicator_bar_num, 4);
            //每一次增加的高度
            stepNum = ta.getInt(R.styleable.PlayingIndicator_step_num, 10);
            duration = ta.getInt(R.styleable.PlayingIndicator_duration, 3000);
            barColor = ta.getColor(R.styleable.PlayingIndicator_bar_color, 0xff000000);
            isTwoWay = ta.getBoolean(R.styleable.PlayingIndicator_is_two_way, false);
            minHeight = ta.getDimensionPixelSize(R.styleable.PlayingIndicator_min_height, 0);
        } finally {
            ta.recycle();
        }
        mSpaceWidth = context.getResources().getDimensionPixelSize(R.dimen.m2);
        mBarWidth = context.getResources().getDimensionPixelOffset(R.dimen.m2);
        paint = new Paint();
        paint.setColor(barColor);
        //paint.setStrokeCap(Paint.Cap.ROUND);

        canRun = PerformanceSettingMananger.getInstance().getIsNeedAnimation();
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
        List<Float> floatList = getGraduateFloatList(stepNum, viewHeight);
      //  mFloat = floatList;
        generateAnim(floatList, barNum);
        animStopList = shuffle();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        Log.i(TAG,"onDraw");
        drawIndicator(canvas, barNum);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        viewWidth = MeasureSpec.getSize(widthMeasureSpec);
        viewHeight = MeasureSpec.getSize(heightMeasureSpec);
        mSpaceWidth = mBarWidth = viewWidth / ((2 * barNum));
        mSumWidth = mSpaceWidth + mBarWidth;
        paint.setStrokeWidth(mBarWidth);
        this.setMeasuredDimension((int) viewWidth, viewHeight);
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }

    private List<Float> getGraduateFloatList(int arraySize, int max) {
        List<Float> floatList = new ArrayList<>();
        double dividedMax = (max - minHeight) / arraySize;
        for (int i = 1; i <= arraySize; i++) {
            floatList.add((float) (i * dividedMax) + minHeight);
        }
        floatList.set(floatList.size() - 1, floatList.get(0));
        return floatList;
    }

    private void generateAnim(List<Float> floatList, int barNum) {
        removeListener();
        if( animList == null )
            animList = new ArrayList<>();
        else
            animList.clear();
        for (int i = 0; i < barNum; i++) {
            Collections.shuffle(floatList);
            floatList.set(floatList.size() - 1, floatList.get(0));

            float[] floatArray = new float[floatList.size()];
            int j = 0;
            for (Float f : floatList) {
                floatArray[j++] = (f != null ? f : Float.NaN);
            }

            mAnim = ValueAnimator.ofFloat(floatArray);
            mAnim.setDuration(duration);
            mAnim.setRepeatCount(ValueAnimator.INFINITE);
            mAnim.setInterpolator(new LinearInterpolator());

            YfAnimCallBack cb = new YfAnimCallBack(String.valueOf(System.currentTimeMillis())) {
                int interpolator = 0;

                @Override
                public void onAnimationUpdate(ValueAnimator animation) {
                    //防止极限刷新，cpu占用过高
                    if ((interpolator++) % 15 == 0) {
                        if(canRun) {
                            invalidate();
                        }
                    }
                }
            };
            mAnim.addUpdateListener(cb);

            mAnim.start();

            animList.add(mAnim);
        }
    }

    private List<Float> shuffle(){
        List<Float> list = getGraduateFloatList(stepNum, viewHeight);

        if (list.size() > barNum) {
            Object[] arr = list.toArray();
            int j = 0;
            for (int i = list.size() - 1; i > 0; i--) {
                if (j == barNum) {
                    break;
                }
                Object tmp = arr[i];
                arr[i] = arr[j];
                arr[j] = tmp;
                j++;
            }

            ListIterator it = list.listIterator();
            for (int i=0; i<arr.length; i++) {
                it.next();
                it.set(arr[i]);
            }

        }

        return list;
    }

    private void drawIndicator(Canvas canvas, int barNum) {
        for (int i = 0; i < barNum; i++) {
            float nextHeight ;
            if (canRun) {
                nextHeight = (float) (animList.get(i).getAnimatedValue());
            } else {
                nextHeight = animStopList.get(i);
            }
            float startX = (float) (i * mSumWidth + mSpaceWidth / 2);
            float startY, stopY;
            if (isTwoWay) {
                startY = (canvas.getHeight() - nextHeight) / 2;
                stopY = (canvas.getHeight() + nextHeight) / 2;
            } else {
                startY = canvas.getHeight();
                stopY = canvas.getHeight() - nextHeight;
            }
            canvas.drawLine(startX, startY, startX, stopY, paint);
        }
    }

    public void setStepNum(int stepNum) {
        this.stepNum = stepNum;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public void setBarNum(int barNum) {
        this.barNum = barNum;
    }

    public void setBarColor(int barColor) {
        this.barColor = barColor;
        paint.setColor(barColor);
    }

    public void start() {
        if(PerformanceSettingMananger.getInstance().getIsNeedAnimation()) {
            canRun = true;
        }
    }

    public void stop() {
        canRun = false;
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        /**
         * #36464
         */
//        removeListener();
    }

    public void removeListener(){
        if (!ListUtil.isEmpty(animList)) {
            for (int i = 0; i < animList.size(); i++) {
                ValueAnimator valueAnimator = animList.get(i);
                if (valueAnimator == null) {
                    continue;
                }
                valueAnimator.removeAllUpdateListeners();
                valueAnimator.cancel();
            }
        }
    }

    class YfAnimCallBack implements ValueAnimator.AnimatorUpdateListener {
        String name;
        public YfAnimCallBack(String name){
            this.name = name;
        }
        @Override
        public void onAnimationUpdate(ValueAnimator animation) {

        }
    }

}
