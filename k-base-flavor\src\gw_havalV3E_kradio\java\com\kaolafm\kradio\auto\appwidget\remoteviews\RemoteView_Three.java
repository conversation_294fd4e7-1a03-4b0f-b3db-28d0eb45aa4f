package com.kaolafm.kradio.auto.appwidget.remoteviews;

import android.app.PendingIntent;
import android.appwidget.AppWidgetManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Build;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.RemoteViews;

import com.kaolafm.kradio.auto.appwidget.KLAppWidgetThree;
import com.kaolafm.kradio.auto.appwidget.WidgetService;
import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.IntentUtils;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.utils.DateFormatUtil;

import static com.kaolafm.kradio.auto.appwidget.WidgetService.WIDGET_ACTION_COLLECTION;
import static com.kaolafm.kradio.auto.appwidget.WidgetService.WIDGET_ACTION_NEXT;
import static com.kaolafm.kradio.auto.appwidget.WidgetService.WIDGET_ACTION_PAUSE;
import static com.kaolafm.kradio.auto.appwidget.WidgetService.WIDGET_ACTION_PLAY;
import static com.kaolafm.kradio.auto.appwidget.WidgetService.WIDGET_ACTION_PREV;

public class RemoteView_Three implements IRemoteViews {
    private static final String TAG = "KLAppWidge_Three";
    public PlayItem playItem;
    public Bitmap mCurrentBitmap;
    public Bitmap mBlurBitmap;
    public String livingTime = "";
    public boolean subscription = false;

    ProgressView progressView;

    private Context context = AppDelegate.getInstance().getContext();

    @Override
    public void setRemoteViews(RemoteData remoteData) {
        initData(remoteData);
        final AppWidgetManager manager = AppWidgetManager.getInstance(context);
        final ComponentName componentName = new ComponentName(context, KLAppWidgetThree.class);
        RemoteViews views = new RemoteViews(context.getPackageName(), R.layout.widget_layout_three);
        //设置播放暂停按钮
        if (PlayerManager.getInstance().isPlaying()) {
            Log.i(TAG,"显示播放");
            showPlayBtn(views);
        } else {
            Log.i(TAG,"显示暂停");
            showPauseBtn(views);
        }
        //设置点击封面进入应用
        Intent launchAppIntent = new IntentUtils().getLauncherIntentUseWidget(context);
        views.setOnClickPendingIntent(R.id.widget_layout_three_main_layout, PendingIntent.getActivity(context, 0, launchAppIntent, PendingIntent.FLAG_CANCEL_CURRENT));
        //设置上一首
        Intent intent = new Intent(context, WidgetService.class);
        intent.setAction(WIDGET_ACTION_PREV);
        views.setOnClickPendingIntent(R.id.widget_prev, getPendingIntent(context, 0, intent, PendingIntent.FLAG_CANCEL_CURRENT));
        //设置下一首
        Intent intent1 = new Intent(context, WidgetService.class);
        intent1.setAction(WIDGET_ACTION_NEXT);
        views.setOnClickPendingIntent(R.id.widget_next, getPendingIntent(context, 0, intent1, PendingIntent.FLAG_CANCEL_CURRENT));

        //设置收藏
        Intent collection = new Intent(context, WidgetService.class);
        collection.setAction(WIDGET_ACTION_COLLECTION);
        views.setOnClickPendingIntent(R.id.widget_collection, getPendingIntent(context, 0, collection, PendingIntent.FLAG_CANCEL_CURRENT));

        updateByPlayItem(playItem, views);
        Log.i("WidgetService", "RemoteView_Three playItem = " + playItem);
        if (playItem != null) {
            if (remoteData.getAppWidgetId() == AppWidgetManager.INVALID_APPWIDGET_ID) {
                manager.updateAppWidget(  componentName  , views);
            } else {
                manager.updateAppWidget(  remoteData.getAppWidgetId() , views);
            }
        }
//        else {
//            HistoryDaoManager.getInstance().queryHistoryListOrderByTime(1, historyItems -> {
//                if (!ListUtil.isEmpty(historyItems)) {
//                    HistoryItem historyItem = historyItems.get(0);
//                    PlayItem historyPlayItem = HistoryUtils.turnPlayItem(historyItem);
//                    updateByPlayItem(historyPlayItem, views);
//                }
//                if (remoteData.getAppWidgetId() == AppWidgetManager.INVALID_APPWIDGET_ID) {
//                    manager.updateAppWidget(  componentName  , views);
//                } else {
//                    manager.updateAppWidget(  remoteData.getAppWidgetId() , views);
//                }
//            });
//        }
    }

    private void initData(RemoteData remoteData) {
        playItem = remoteData.mPlayItem;
        mCurrentBitmap = remoteData.mCurrentBitmap;
        mBlurBitmap = remoteData.mBlurBitmap;
        livingTime = remoteData.livingTime;
        subscription = remoteData.subscription;
        if (progressView == null) {
            progressView = new ProgressView(AppDelegate.getInstance().getContext(),
                    ResUtil.getDimen(R.dimen.widget_progress_bar_three_width),
                    ResUtil.getDimen(R.dimen.widget_progress_bar_height),
                    ResUtil.getDimen(R.dimen.widget_progress_bar_line_height));
        }
    }

    private void showPlayBtn(RemoteViews views) {
        views.setImageViewResource(R.id.widget_play_or_pause, R.drawable.selector_widget_btn_play);
        Intent intent = new Intent(context, WidgetService.class);
        intent.setAction(WIDGET_ACTION_PAUSE);
        views.setOnClickPendingIntent(R.id.widget_play_or_pause, getPendingIntent(context, 0, intent, 0));
    }

    private void showPauseBtn(RemoteViews views) {
        views.setImageViewResource(R.id.widget_play_or_pause, R.drawable.selector_widget_btn_pause);
        Intent intent = new Intent(context, WidgetService.class);
        intent.setAction(WIDGET_ACTION_PLAY);
        views.setOnClickPendingIntent(R.id.widget_play_or_pause, getPendingIntent(context, 0, intent, 0));
    }

    private void updateByPlayItem(PlayItem playItem, RemoteViews views) {

        if (playItem != null) {
            Log.i(TAG, "updateByPlayItem：" + playItem.getTitle());
            //设置封面图片
            views.setImageViewBitmap(R.id.widget_cover, mCurrentBitmap);

            //设置碎片名称
            CharSequence widgetText = TextUtils.isEmpty(playItem.getSourceName())?playItem.getTitle():playItem.getSourceName();
            if (!TextUtils.isEmpty(widgetText)) {
                views.setTextViewText(R.id.widget_audio_name, widgetText);
            } else {
                views.setTextViewText(R.id.widget_audio_name, "暂无节目信息");
            }
            //设置专辑名称
            CharSequence albumName = playItem.getAlbumTitle();
            if (!TextUtils.isEmpty(albumName)) {
                views.setTextViewText(R.id.widget_album_name, albumName);
            }

            //设置收藏状态
            if (subscription) {
                views.setImageViewResource(R.id.widget_collection, R.drawable.selector_widget_btn_collection);
            } else {
                views.setImageViewResource(R.id.widget_collection, R.drawable.selector_widget_btn_uncollection);
            }

            if (playItem.isLiving()) {
                if (playItem.getFinishTime() <= 0) {
                    views.setViewVisibility(R.id.widget_duration, View.INVISIBLE);
                    views.setViewVisibility(R.id.widget_cur_time, View.INVISIBLE);
                    progressView.drawToRemoteViews(views, R.id.widget_progressBar, 0);
                } else {
                    views.setViewVisibility(R.id.widget_duration, View.VISIBLE);
                    views.setViewVisibility(R.id.widget_cur_time, View.VISIBLE);
                    views.setTextViewText(R.id.widget_duration, DateFormatUtil.getCurrDate(playItem.getFinishTime()));
                    views.setTextViewText(R.id.widget_cur_time, livingTime + "");
                    progressView.drawToRemoteViews(views, R.id.widget_progressBar, 0);
                }
            } else {
                views.setViewVisibility(R.id.widget_duration, View.VISIBLE);
                views.setViewVisibility(R.id.widget_cur_time, View.VISIBLE);
                int duration = playItem.getDuration();
                Log.i(TAG, "updateByPlayItem----------------->position = " + playItem.getPosition() + "---->duration = " + duration);
//                views.setProgressBar(R.id.widget_progressBar, duration, playItem.getPosition(), false);
                views.setTextViewText(R.id.widget_duration, DateFormatUtil.getDescriptiveTime(duration));
                views.setTextViewText(R.id.widget_cur_time, DateFormatUtil.getDescriptiveTime(playItem.getPosition()));
                int position = playItem.getPosition();
                progressView.drawToRemoteViews(views, R.id.widget_progressBar, (float) position / duration);

            }
        } else {
            if (mCurrentBitmap == null) {
                mCurrentBitmap = BitmapUtil.makeRoundCycle(BitmapUtil.getBitmapFromResource(AppDelegate.getInstance().getContext().getResources(), R.drawable.widget_cover_default));
            }
            views.setImageViewBitmap(R.id.widget_cover, mCurrentBitmap);
        }
    }

    private void go2Launcher() {
        //fixed 修复应用未启动添加widget点击按钮没有响应的问题，直接进入应用
        Intent launchAppIntent = IntentUtils.getInstance().getLauncherIntentUseWidget(context);
        context.startActivity(launchAppIntent);
    }

    private PendingIntent getPendingIntent(Context context, int questCode, Intent intent, int flags) {
        if (Build.VERSION.SDK_INT >= 26) {
            return PendingIntent.getForegroundService(context, questCode, intent, flags);
        } else {
            return PendingIntent.getService(context, questCode, intent, flags);
        }
    }

}

