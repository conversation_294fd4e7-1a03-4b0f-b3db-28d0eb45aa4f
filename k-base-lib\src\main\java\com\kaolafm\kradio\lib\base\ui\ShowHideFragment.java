package com.kaolafm.kradio.lib.base.ui;

import android.os.Bundle;

import android.util.Log;
import android.view.View;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;

import com.kaolafm.kradio.lib.base.arouter.ARouterBaseFragment;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.Iterator;
import java.util.List;


public class ShowHideFragment extends ARouterBaseFragment implements IFragmentVisibility {
    @NotNull
    private String mTag = "";
    private boolean isRuningHiddenChanged;
    private boolean isUserVisible;
    private boolean isUserVisibleFinal;
    private IFragmentVisibility mFragment;
    @NotNull
    public static final String TAG = "ShowHideFragment";

    @NotNull
    protected final String getMTag() {
        return this.mTag;
    }

    protected final void setMTag(@NotNull String var1) {
        this.mTag = var1;
    }

    @Override
    public void onViewCreated(@NotNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        Log.d("ShowHideFragment", this.mTag + " ===  onViewCreated");
    }

    @Override
    public void onResume() {
        super.onResume();
        if (this.isParentFragmentFinalVisible()) {
            if (!this.isRuningHiddenChanged) {
                this.isRuningHiddenChanged = true;
                this.isUserVisible = true;
                this.onUserVisible();
            } else if (this.isUserVisible) {
                this.onUserVisible();
            }
        }

    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        if (hidden) {
            if (this.isUserVisible) {
                this.isUserVisible = false;
                this.onUserInvisible();
            }
        } else if (this.isParentFragmentFinalVisible()) {
            this.onUserVisible();
            this.isUserVisible = true;
        }

        this.isRuningHiddenChanged = true;
    }

    @Override
    public void onPause() {
        super.onPause();
        if (this.isUserVisible) {
            this.onUserInvisible();
        }

    }

    @Override
    public void onUserVisible() {
        Log.e("ShowHideFragment", this.mTag + " ===  onUserVisible");
        this.isUserVisibleFinal = true;
        if (this.isAdded()) {
            FragmentManager var10000 = this.getChildFragmentManager();
            List fragments = var10000.getFragments();
            Iterable $receiver$iv = (Iterable) fragments;
            Iterator var3 = $receiver$iv.iterator();

            while (var3.hasNext()) {
                Object element$iv = var3.next();
                Fragment fragment = (Fragment) element$iv;
                boolean visible = fragment instanceof IFragmentVisibility ? ((IFragmentVisibility) fragment).isFinalUserVisible() : fragment != null && fragment.isVisible();
                if (this.mFragment == fragment || visible && fragment instanceof IFragmentVisibility) {
                    ((IFragmentVisibility) fragment).onUserVisible();
                    this.mFragment = (IFragmentVisibility) null;
                }
            }

        }
    }

    @Override
    public void onUserInvisible() {
        Log.d("ShowHideFragment", this.mTag + " ===  onUserInvisible");
        this.isUserVisibleFinal = false;
        FragmentManager var10000 = this.getChildFragmentManager();
        List fragments = var10000.getFragments();
        Iterable $receiver$iv = (Iterable) fragments;
        Iterator var3 = $receiver$iv.iterator();

        while (var3.hasNext()) {
            Object element$iv = var3.next();
            Fragment fragment = (Fragment) element$iv;
            boolean visible = fragment instanceof IFragmentVisibility ? ((IFragmentVisibility) fragment).isFinalUserVisible() : fragment != null && fragment.isVisible();
            if (visible && fragment instanceof IFragmentVisibility) {
                ((IFragmentVisibility) fragment).onUserInvisible();
                this.mFragment = (IFragmentVisibility) fragment;
            }
        }

    }

    @Override
    public boolean isFinalUserVisible() {
        return this.isUserVisibleFinal;
    }

    private final boolean isParentFragmentFinalVisible() {
        Fragment var10000;
        boolean var1;
        if (this.getParentFragment() instanceof IFragmentVisibility) {
            var10000 = this.getParentFragment();
            var1 = ((IFragmentVisibility) var10000).isFinalUserVisible();
        } else {
            if (this.getParentFragment() != null) {
                var10000 = this.getParentFragment();
                if (!var10000.isVisible()) {
                    var1 = false;
                    return var1;
                }
            }

            var1 = true;
        }

        return var1;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }

    public static final class Companion {
        @NotNull
        public final ShowHideFragment newInstance(@NotNull String tag) {
            ShowHideFragment fragment = new ShowHideFragment();
            fragment.setMTag(tag);
            return fragment;
        }

        private Companion() {
        }
    }
}
