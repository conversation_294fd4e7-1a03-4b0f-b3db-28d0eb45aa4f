package com.kaolafm.kradio.uitl;

import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.PixelFormat;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.Rect;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.util.Log;

import com.kaolafm.kradio.flavor.R;

public class BimapPlusUtils {

    static final String TAG = "BimapPlusUtils";

    public static Bitmap drawableToBitmapWithSize(Drawable drawable, int newWidth, int newHeight) {
        Bitmap bitmap = Bitmap.createBitmap(newWidth, newHeight,
                drawable.getOpacity() != PixelFormat.OPAQUE ? Bitmap.Config.ARGB_8888 : Bitmap.Config.RGB_565);
        Canvas canvas = new Canvas(bitmap);
        drawable.setBounds(0, 0, newWidth, newHeight);
        drawable.draw(canvas);
        return bitmap;
    }

    public static Drawable getAppIcon(Context context, String packageName) {
        Drawable icon = context.getDrawable(R.drawable.byd_35_app_icon);
        try {
            icon = context.getPackageManager().getApplicationIcon(packageName);
            Log.i(TAG, "getAppIcon for " + packageName + " success:" + icon);
        } catch (PackageManager.NameNotFoundException e) {
            Log.i(TAG, "getAppIcon for " + packageName + " error:" + e.toString());
        }
        return icon;
    }

    public static Bitmap createBitmapWithMasklayer(Bitmap src, Bitmap dst) {
        int width = src.getWidth();
        int height = src.getHeight();
        final Canvas canvas = new Canvas();
        Bitmap bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        canvas.setBitmap(bitmap);

        Paint paint = new Paint();
        paint.setAntiAlias(true);
        paint.setFilterBitmap(true);
        canvas.drawBitmap(src, new Rect(0, 0, src.getWidth(), src.getHeight()), new Rect(0, 0, width, height), paint);
        paint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.DST_IN));
        canvas.drawBitmap(dst, new Rect(0, 0, dst.getWidth(), dst.getHeight()), new Rect(0, 0, width, height), paint);
        canvas.setBitmap(null);
        return bitmap;
    }
}
