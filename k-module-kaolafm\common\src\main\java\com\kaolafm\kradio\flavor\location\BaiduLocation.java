package com.kaolafm.kradio.flavor.location;

import android.Manifest;
import android.util.Log;

import com.baidu.location.BDAbstractLocationListener;
import com.baidu.location.BDLocation;
import com.baidu.location.LocationClient;
import com.baidu.location.LocationClientOption;
import com.kaolafm.kradio.common.utils.PermissionUtils;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.location.model.IKaoLaLocation;
import com.kaolafm.kradio.lib.location.model.LocationModel;

import java.util.ArrayList;
import java.util.List;


/**
 * 自定义百度位置信息获取
 */

public class BaiduLocation implements IKaoLaLocation {

    private static final String TAG = "BaiduLocation";

    private static BaiduLocation baiduLocation;

    private LocationClient mLocationClient;

    private List<IKaoLaLocationListener> mListeners;

    private BaiduLocation() {
    }

    public static IKaoLaLocation getInstance() {
        if (baiduLocation == null) {
            synchronized (BaiduLocation.class) {
                if (baiduLocation == null) {
                    baiduLocation = new BaiduLocation();
                }
            }
        }
        if (baiduLocation.mLocationClient == null) {
            baiduLocation.init();
        }
        return baiduLocation;
    }

    private void init() {
        if (PermissionUtils.checkPermission(AppDelegate.getInstance().getContext()
                , Manifest.permission.ACCESS_COARSE_LOCATION
                , Manifest.permission.ACCESS_FINE_LOCATION)) {
            Log.w(TAG, "permission true");
        } else {
            Log.w(TAG, "permission false");
            return;
        }
        LocationClient.setAgreePrivacy(true);
        try {
            mLocationClient = new LocationClient(AppDelegate.getInstance().getContext());
            Log.w(TAG, "inited");
        } catch (Exception e) {
            Log.w(TAG, "error:" + e.toString());
        }
        start();
    }

    @Override
    public boolean initSuccess() {
        return mLocationClient != null;
    }

    @Override
    public void reInit() {
        init();
    }

    @Override
    public LocationModel getLocation() {
        LocationModel model = new LocationModel();
        if (mLocationClient != null) {
            BDLocation bdLocation = mLocationClient.getLastKnownLocation();
            if (bdLocation != null && (bdLocation.getLatitude() != 0 || bdLocation.getLongitude() != 0)) {
                model.setLatitude(bdLocation.getLatitude());
                model.setLatitude(bdLocation.getLongitude());
                Log.w(TAG, "return last known:" + model.getLatitude() + ":" + model.getLongitude());
            }
        }
        return model;
    }

    @Override
    public void addLocationListener(IKaoLaLocationListener listener) {
        if (mListeners == null) {
            mListeners = new ArrayList<>();
        }
        Log.w(TAG, "listener add");
        mListeners.add(listener);

    }

    @Override
    public void removeLocationListener(IKaoLaLocationListener listener) {
        if (mListeners != null) {
            Log.w(TAG, "location remove");
            mListeners.remove(listener);
        }
    }

    private void start() {
        Log.w(TAG, "location start");
        LocationClientOption option = new LocationClientOption();
        option.setLocationMode(LocationClientOption.LocationMode.Hight_Accuracy);
        option.setCoorType("bd09ll");
        option.setFirstLocType(LocationClientOption.FirstLocType.SPEED_IN_FIRST_LOC);
        option.setScanSpan(TIME_INTERVAL);
        option.setOpenGps(true);
        option.setLocationNotify(true);
        option.setIgnoreKillProcess(false);
        option.SetIgnoreCacheException(false);
        option.setWifiCacheTimeOut(TIME_INTERVAL);
        option.setEnableSimulateGps(false);
        option.setNeedNewVersionRgc(true);
        mLocationClient.setLocOption(option);
        mLocationClient.registerLocationListener(new BDAbstractLocationListener() {
            @Override
            public void onReceiveLocation(BDLocation bdLocation) {
                double latitude = bdLocation.getLatitude();    //获取纬度信息
                double longitude = bdLocation.getLongitude();    //获取经度信息
                int errorCode = bdLocation.getLocType();
                String errorMsg = bdLocation.getLocationDescribe();
                Log.w(TAG, "location update:" + latitude + ":" + longitude + "  errorMsg-" + errorMsg + " errorCode-" + errorCode);
                LocationModel model = new LocationModel();
                model.setLongitude(longitude);
                model.setLatitude(latitude);
                model.setCityName(bdLocation.getCity());
                if (mListeners != null) {
                    for (IKaoLaLocationListener listener : mListeners) {
                        listener.locationChange(model);
                    }
                }
            }
        });
        mLocationClient.start();

        LocationModel model = getLocation();
        if (mListeners != null && model != null) {
            Log.w(TAG, "last known have:" + model.getLatitude() + ":" + model.getLongitude());
            if (model.getLongitude() != 0 || model.getLatitude() != 0) {
                for (IKaoLaLocationListener listener : mListeners) {
                    listener.locationChange(model);
                }
            }
        }
    }

    @Override
    public void destroy() {
        if (mLocationClient != null) {
            mLocationClient.stop();
        }
        mLocationClient = null;

    }
}
