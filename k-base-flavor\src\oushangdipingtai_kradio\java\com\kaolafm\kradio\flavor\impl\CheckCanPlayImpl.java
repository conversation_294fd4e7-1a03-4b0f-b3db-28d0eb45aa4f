package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import android.os.Looper;
import android.os.RemoteException;
import android.provider.Settings;
import android.util.Log;

import com.kaolafm.clockplayer.ClockPlayer;
import com.kaolafm.kradio.flavor.carnetwork.api.AuthApiRequest;
import com.kaolafm.kradio.flavor.carnetwork.carnetworkutils.AuthListener;
import com.kaolafm.kradio.flavor.carnetwork.carnetworkutils.CarAuthUtil;
import com.kaolafm.kradio.flavor.carnetwork.carnetworkutils.OuShangAuthConstants;
import com.kaolafm.kradio.flavor.carnetwork.dialog.OuShangDialog;

import com.kaolafm.kradio.k_kaolafm.home.data.Category;
import com.kaolafm.kradio.k_kaolafm.home.gallery.PageJumper;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.base.flavor.KRadioAuthInter;
import com.kaolafm.kradio.lib.common.ResType;
import com.kaolafm.kradio.lib.utils.SharedPreferenceUtil;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import com.kaolafm.sdk.core.mediaplayer.ICheckCanPlayInter;


import com.kaolafm.sdk.core.mediaplayer.PlayerService;

public class CheckCanPlayImpl implements ICheckCanPlayInter {
    private Category.Item mCategoryItem;
    private PlayItem mPlayItem;
    private boolean fromUser;
    private String mType;
    private PlayerService.PlayerBinder playerBinder;

    private SharedPreferenceUtil mSPUtil;
    private AuthApiRequest mChangAnApiRequest;
    private OuShangDialog mOuShangDialog;

    private Activity preActivity;
    private Activity curActivity;
    private Handler mainHandler;

    private boolean isFromTTS = false;//防止语音播报通过后继续调用播放器导致流程走两次
    public static final String ACTION_CA_TOKEN_SEND = "JSBD_ACTION_CA_TOKEN_SEND";
    public static final String CA_HTTPS_ADDR = "CA_HTTPS_ADDR";
    private AuthListener mAuthListener = new AuthListener() {
        @Override
        public void onIsCanUse(boolean isCanUse) {
            Log.i("zsj", "onIsCanUse = " + isCanUse + ",mType = " + mType);
//                PlayerManager.getInstance().notifyIPlayChangedListener(mPlayItem);
            if (isCanUse) {
                //可以播放
                runUIdismiss();
                doPlay(mType);
            } else {
                //不可播放
                doPause(mType);
            }
        }

        @Override
        public void Error(int errCode, String errMsg) {
            //不可播放
            doPause(mType);
        }
    };

    private static class InstanceHolder {
        private final static ICheckCanPlayInter CheckCanPlay = PlayerCustomizeManager.getInstance().getCheckCanPlayInter();
    }

    public static ICheckCanPlayInter getInstance() {
        return InstanceHolder.CheckCanPlay;
    }


    @Override
    public boolean checkPlay(Object... args) {
        mType = (String) args[1];
        //如果是直播获取到的item为Category.Item类型
        if (mType.equals(KRadioAuthInter.METHOD_LIVING)) {
            mCategoryItem = (Category.Item) args[0];
        } else {
            mPlayItem = (PlayItem) args[0];
        }
        fromUser = (boolean) args[2];

        if (playerBinder == null) {
            playerBinder = PlayerManager.getInstance().getPlayerBinder();
        }
        if (mainHandler == null) {
            mainHandler = new Handler(Looper.getMainLooper());
        }

        if (isFromTTS) {
            isFromTTS = false;
            doPlay(mType);
            return true;
        }


        checkAction(mAuthListener);

        return true;
    }

    @Override
    public void addListener(Object... objects) {

    }

    @Override
    public void removeListener(Object... objects) {

    }


    //执行播放操作
    private void doPlay(String type) {
        if (mPlayItem != null) {
            PlayerManager.getInstance().notifyIPlayChangedListener(mPlayItem);
        }
        switch (type) {
            case ICheckCanPlayInter.METHOD_PLAY:
                try {
                    if (playerBinder != null) {
                        playerBinder.playItem(fromUser);
                    }
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
                break;

            case ICheckCanPlayInter.METHOD_START:
                if (playerBinder != null) {
                    playerBinder.startItem(mPlayItem);
                }
                break;

            case KRadioAuthInter.METHOD_TTS:
//                isFromTTS = true;
//                boolean isIntercept = ClockPlayer.getInstance(AppDelegate.getInstance().getContext()).intercept();
//                String radioType = PlayItemUtils.getRadioType();
//                if (isIntercept && String.valueOf(ResType.RADIO_TYPE).equals(radioType)) {
//                    PlayerManager.getInstance().reset();
//                    ClockPlayer.getInstance(AppDelegate.getInstance().getContext())
//                            .setClockPlayDoneListener(new ClockPlayer.ClockPlayDoneListener() {
//                                @Override
//                                public void onClockPlayDone() {
//                                    ClockPlayer.getInstance(AppDelegate.getInstance().getContext())
//                                            .setClockPlayDoneListener(null);
//                                    PlayerManager.getInstance().originStart(mPlayItem);
//                                }
//                            });
//                    ClockPlayer.getInstance(AppDelegate.getInstance().getContext()).play();
//                    PlayerManager.getInstance().notifyIPlayChangedListener(mPlayItem);
//                } else {
//                    PlayerManager.getInstance().originStart(mPlayItem);
//                }
                break;
            case KRadioAuthInter.METHOD_NETCHANGE:
                //如果是网络切换不进行播放动作的执行，防止正在播放中的重复播放
                break;
            case KRadioAuthInter.METHOD_LIVING:
                //跳转至直播界面
                PageJumper.getInstance().jumpToLivePage(mCategoryItem);
                break;
            default:
        }
    }

    //执行暂停操作
    private void doPause(String type) {
        //鉴权不通过通知卡片切换
        if (mainHandler != null) {
            mainHandler.post(new Runnable() {
                @Override
                public void run() {
                    //已在主线程中，可以更新UI
                    if (mPlayItem != null) {
                        PlayerManager.getInstance().notifyIPlayChangedListener(mPlayItem);
                    }
                }
            });
        }

        if (playerBinder != null && playerBinder.isPlaying()) {
            try {
                playerBinder.pause(false);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }


    public void checkAction(final AuthListener mListener) {


        OuShangAuthConstants.BASEURL = OuShangAuthConstants.BASEURL_OFFICIAL;
//        OuShangAuthConstants.BASEURL = OuShangAuthConstants.BASEURL_TEST;

        String address = Settings.System.getString(AppDelegate.getInstance().getContext().getContentResolver(), CA_HTTPS_ADDR);
        if ("incall.changan.com.cn".equals(address)) {
            OuShangAuthConstants.BASEURL = OuShangAuthConstants.BASEURL_OFFICIAL;
        } else if ("pre-incall.changan.com.cn".equals(address)) {
            OuShangAuthConstants.BASEURL = OuShangAuthConstants.BASEURL_TEST;
        } else {
            OuShangAuthConstants.BASEURL = OuShangAuthConstants.BASEURL_OFFICIAL;
        }

        Log.i("zsj", "checkAction:  address = " + address + "  ,BASEURL = " + OuShangAuthConstants.BASEURL);
        if (!CarAuthUtil.checkAuthStatusAndDate()) {
            mAuthListener.onIsCanUse(true);
            return;
        }
        curActivity = AppManager.getInstance().getCurrentActivity();
        if (preActivity != curActivity && curActivity != null) {
            preActivity = curActivity;
            if (curActivity != null) mOuShangDialog = new OuShangDialog((Context) curActivity);
        } else {
            if (mOuShangDialog == null && curActivity != null) {
                mOuShangDialog = new OuShangDialog((Context) curActivity);
            }
        }

        //显示loading状态
        runUIshow(OuShangAuthConstants.CODE_LOADING);
        String token = Settings.Global.getString(AppDelegate.getInstance().getContext().getContentResolver(), "ca_access_token");
//        String token = Settings.System.getString(AppDelegate.getInstance().getContext().getContentResolver(), "CA_ACCESS_TOKEN");

        //获取token成功
        ChangAnAuthRequest(mAuthListener, token);
    }


    /**
     * 判断当前网络是否需要鉴权，获取当前的Access token 并 请求联通结果获取流量鉴权结果
     */
    private void ChangAnAuthRequest(final AuthListener mListener, String token) {

        String appid = OuShangAuthConstants.APPID;
        String vid = token;
        String itemid = OuShangAuthConstants.ITEMID;
        long timestamp = System.currentTimeMillis();
        Log.i("zsj", "ChangAnAuth  getChangAnAuth  param:  " + "appid = " + appid + ",vid = " + vid + ",itemid = " + itemid + ",timestamp = " + timestamp);
        if (mChangAnApiRequest == null) {
            mChangAnApiRequest = new AuthApiRequest();
        }
        mChangAnApiRequest.getChangAnAuth(appid, vid, itemid, timestamp, new HttpCallback<Integer>() {
            @Override
            public void onSuccess(Integer integer) {
                if (integer <= 0) {
                    runUIshow(integer);
                    CarAuthUtil.saveAuthAndDate(String.valueOf(System.currentTimeMillis()), false);
                    mListener.onIsCanUse(false);
                    reflashToken(integer);
                } else if (integer == 1) {
                    runUIdismiss();
                    CarAuthUtil.saveAuthAndDate(String.valueOf(System.currentTimeMillis()), true);
                    mListener.onIsCanUse(true);
                }
            }

            @Override
            public void onError(ApiException e) {
                //由于对方接口返回数据与我们的不一致,导致一直走onError.可以通过e.getCode()判断是否成功
                int code = e.getCode();
                Log.i("zsj", "ChangAnAuth getChangAnAuth json :  = " + e.toString());
                if (code <= 0) {
                    runUIshow(code);
                    CarAuthUtil.saveAuthAndDate(String.valueOf(System.currentTimeMillis()), false);
                    mListener.onIsCanUse(false);
                    reflashToken(code);
                } else if (code == 1) {
                    runUIdismiss();
                    CarAuthUtil.saveAuthAndDate(String.valueOf(System.currentTimeMillis()), true);
                    mListener.onIsCanUse(true);
                } else if (code == 604 || code == 408) {
                    //超时和其他错误  sdkapi超时errorcode没有暴露  超时状态至为604和408
                    runUIshow(OuShangAuthConstants.CODE_TIMEOUT);
                    CarAuthUtil.saveAuthAndDate(String.valueOf(System.currentTimeMillis()), false);
                    mListener.Error(code, e.getMessage());
                } else {
                    runUIshow(OuShangAuthConstants.CODE_ERROR);
                    CarAuthUtil.saveAuthAndDate(String.valueOf(System.currentTimeMillis()), false);
                    mListener.Error(code, e.getMessage());
                }
            }
        });
    }

    //刷新token
    private void reflashToken(int code) {
        if (code == -7) {
            Intent i = new Intent(ACTION_CA_TOKEN_SEND);
            AppDelegate.getInstance().getContext().sendBroadcast(i);
        }
    }


    private void runUIshow(final Integer code) {
        try {
            if (mainHandler != null) {
                mainHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        //已在主线程中，可以更新UI
                        if (mOuShangDialog != null) {
                            mOuShangDialog.setCode(code);
                            mOuShangDialog.show();
                        }
                    }
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
            runUIshow(OuShangAuthConstants.CODE_ERROR);
        }
    }

    private void runUIdismiss() {
        try {
            if (mainHandler != null) {
                mainHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        //已在主线程中，可以更新UI
                        if (mOuShangDialog != null) {
                            mOuShangDialog.dismiss();
                        }
                    }
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

