package com.kaolafm.kradio.lib.base.ui;

import android.os.Bundle;

import android.util.Log;
import android.view.View;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;

import com.kaolafm.kradio.lib.base.arouter.ARouterBaseFragment;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import kotlin.TypeCastException;

public class ViewPagerFragment extends ARouterBaseFragment implements IFragmentVisibility {
    @NotNull
    private String mTag = "";
    private boolean isFirstResume = true;
    private boolean isFirstVisible = true;
    private boolean isUserVisible;
    private boolean isPrepared;
    private boolean isUserVisibleFinal;
    private IFragmentVisibility mFragment;
    @NotNull
    public static final String TAG = "ViewPagerFragment";
    @NotNull
    public static final ViewPagerFragment.Companion Companion = new ViewPagerFragment.Companion();

    @NotNull
    protected final String getMTag() {
        return this.mTag;
    }

    protected final void setMTag(@NotNull String var1) {
        this.mTag = var1;
    }

    @Override
    public void onViewCreated(@NotNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        Log.d("ViewPagerFragment", this.mTag + " ===  onViewCreated");
    }

    @Override
    public void onResume() {
        super.onResume();
        if (this.isFirstResume) {
            this.isFirstResume = false;
        } else if (this.getUserVisibleHint() && this.isParentFragmentFinalVisible()) {
            this.isUserVisible = true;
            this.onUserVisible();
        }

    }

    @Override
    public void onPause() {
        super.onPause();
        if (this.isFirstResume) {
            this.isFirstResume = false;
        } else if (this.getUserVisibleHint() && this.isUserVisible) {
            this.isUserVisible = false;
            this.onUserInvisible();
        }

    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        this.initPrepare();
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser) {
            if (this.isFirstVisible) {
                this.isFirstVisible = false;
                this.initPrepare();
            } else if (this.isParentFragmentFinalVisible()) {
                this.isUserVisible = true;
                this.onUserVisible();
            }
        } else if (this.isUserVisible) {
            this.isUserVisible = false;
            this.onUserInvisible();
        }

    }

    private final void initPrepare() {
        if (this.isPrepared && this.isParentFragmentFinalVisible()) {
            this.isUserVisible = true;
            this.onUserVisible();
        } else {
            this.isPrepared = true;
        }

    }

    @Override
    public void onUserVisible() {
        Log.e("ViewPagerFragment", this.mTag + " ===  onUserVisible");
        this.isUserVisibleFinal = true;
        if (this.isAdded()) {
            FragmentManager var10000 = this.getChildFragmentManager();
            List fragments = var10000.getFragments();
            Iterable $receiver$iv = (Iterable) fragments;
            Iterator var3 = $receiver$iv.iterator();

            while (var3.hasNext()) {
                Object element$iv = var3.next();
                Fragment fragment = (Fragment) element$iv;
                boolean visible = fragment instanceof IFragmentVisibility ? ((IFragmentVisibility) fragment).isFinalUserVisible() : fragment != null && fragment.isVisible();
                if (this.mFragment == fragment || visible && fragment instanceof IFragmentVisibility) {
                    if (fragment == null) {
                        throw new TypeCastException("null cannot be cast to non-null type com.kaolafm.kradio.lib.base.ui.IFragmentVisibility");
                    }

                    ((IFragmentVisibility) fragment).onUserVisible();
                    this.mFragment = (IFragmentVisibility) null;
                }
            }

        }
    }
@Override
    public void onUserInvisible() {
        Log.d("ViewPagerFragment", this.mTag + " ===  onUserInvisible");
        this.isUserVisibleFinal = false;
        FragmentManager var10000 = this.getChildFragmentManager();
        List fragments = var10000.getFragments();
        Iterable $receiver$iv = (Iterable) fragments;
        Iterator var3 = $receiver$iv.iterator();

        while (var3.hasNext()) {
            Object element$iv = var3.next();
            Fragment fragment = (Fragment) element$iv;
            boolean visible = fragment instanceof IFragmentVisibility ? ((IFragmentVisibility) fragment).isFinalUserVisible() : fragment != null && fragment.isVisible();
            if (visible && fragment instanceof IFragmentVisibility) {
                ((IFragmentVisibility) fragment).onUserInvisible();
                this.mFragment = (IFragmentVisibility) fragment;
            }
        }

    }
@Override
    public void onDestroyView() {
        super.onDestroyView();
        this.isFirstResume = true;
        this.isFirstVisible = true;
        this.isUserVisible = false;
        this.isPrepared = false;
    }
@Override
    public boolean isFinalUserVisible() {
        return this.isUserVisibleFinal;
    }

    private final boolean isParentFragmentFinalVisible() {
        Fragment var10000;
        boolean var1;
        if (this.getParentFragment() instanceof IFragmentVisibility) {
            var10000 = this.getParentFragment();
            if (var10000 == null) {
                throw new TypeCastException("null cannot be cast to non-null type com.kaolafm.kradio.lib.base.ui.IFragmentVisibility");
            }

            var1 = ((IFragmentVisibility) var10000).isFinalUserVisible();
        } else {
            if (this.getParentFragment() != null) {
                var10000 = this.getParentFragment();
                if (!var10000.isVisible()) {
                    var1 = false;
                    return var1;
                }
            }

            var1 = true;
        }

        return var1;
    }

    public static final class Companion {
        @NotNull
        public final ViewPagerFragment newInstance(@NotNull String tag) {
            ViewPagerFragment fragment = new ViewPagerFragment();
            fragment.setMTag(tag);
            return fragment;
        }

        private Companion() {
        }

    }
}
