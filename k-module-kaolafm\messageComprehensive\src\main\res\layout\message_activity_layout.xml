<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_home">

    <ImageView
        style="@style/ComprehensiveFragmentBackButton"
        android:id="@+id/message_back_mine"
        android:layout_marginTop="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/message_title_text"
        app:layout_constraintBottom_toBottomOf="@+id/message_title_text"/>

    <com.kaolafm.kradio.component.ui.base.view.KradioTextView
        android:id="@+id/message_title_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/m50"
        android:gravity="center"
        android:text="@string/message_activity_title_text"
        android:textColor="@color/message_title_text_color"
        android:textSize="@dimen/m30"
        app:kt_font_weight="0.3"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageButton
        android:id="@+id/msg_tips_ib"
        android:layout_width="@dimen/m44"
        android:layout_height="@dimen/m44"
        android:layout_marginTop="@dimen/m55"
        android:layout_marginEnd="@dimen/m66"
        android:background="@color/transparent"
        android:padding="@dimen/m6"
        android:scaleType="centerInside"
        android:contentDescription="@string/content_desc_message_activity_tip"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.kaolafm.kradio.component.ui.base.view.KradioTextView
        android:id="@+id/all_message_readed"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/m50"
        android:layout_marginStart="@dimen/m16"
        android:layout_marginEnd="@dimen/m16"
        android:paddingTop="@dimen/m8"
        android:paddingBottom="@dimen/m8"
        android:paddingLeft="@dimen/m16"
        android:paddingRight="@dimen/m16"
        android:contentDescription="@string/content_desc_all_readed"
        android:background="@drawable/message_details_btn_bg"
        android:gravity="center"
        android:textColor="@color/bubble_btn_text_color"
        android:textSize="@dimen/m26"
        app:kt_font_weight="0.3"
        android:text="@string/content_desc_all_readed"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toStartOf="@id/msg_tips_ib" />

    <View
        android:id="@+id/divider_line"
        android:layout_width="0dp"
        android:layout_height="@dimen/y1"
        android:layout_marginTop="@dimen/y125"
        android:background="@color/message_line_color"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 隐藏控件，用于所见即可说语音执行滑动操作 -->
    <com.kaolafm.kradio.component.ui.base.view.KradioTextView
        android:id="@+id/cd_up"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@color/transparent"
        android:contentDescription="@string/content_desc_up_scroll"
        android:text="@string/content_desc_up_scroll"
        android:textColor="@color/transparent"
        android:textSize="1sp"
        app:kt_font_weight="0.3"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/divider_line"
        tools:ignore="SmallSp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/msg_rv"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:paddingLeft="@dimen/x80"
        android:paddingRight="@dimen/x80"
        android:layout_marginBottom="@dimen/m16"
        android:scrollbars="none"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cd_up"
        app:layout_constraintBottom_toTopOf="@id/bottom_background" />

    <ViewStub
        android:id="@+id/vs_layout_error_page"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/m134"
        android:layout="@layout/layout_msg_status_page"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cd_up" />

    <com.kaolafm.kradio.component.ui.base.view.KradioTextView
        android:id="@+id/bottom_background"
        android:layout_width="0dp"
        android:layout_height="@dimen/y119"
        android:background="@drawable/app_bottom_bg"
        app:kt_font_weight="0.3"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <com.kaolafm.kradio.component.ui.base.view.KradioTextView
        android:id="@+id/cd_down"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@color/transparent"
        android:contentDescription="@string/content_desc_down_scroll"
        android:text="@string/content_desc_down_scroll"
        android:textColor="@color/transparent"
        android:textSize="1sp"
        app:kt_font_weight="0.3"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:ignore="SmallSp" />

</androidx.constraintlayout.widget.ConstraintLayout>