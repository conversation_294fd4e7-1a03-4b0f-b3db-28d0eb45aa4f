<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/widget_layout_two_main_layout"
    android:layout_width="580px"
    android:layout_height="600px"
    android:background="@color/transparent_color"
    android:minWidth="580px"
    android:minHeight="600px"
    tools:ignore="PxUsage">

    <RelativeLayout
        android:id="@+id/widget_playinfo_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="49px"
            android:orientation="vertical">

            <TextView
                android:id="@+id/widget_audio_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center"
                android:lines="1"
                android:maxLines="1"
                android:paddingLeft="80px"
                android:paddingRight="80px"
                android:paddingBottom="@dimen/widget_title_padding_bottom"
                android:text=""
                android:textColor="@color/widget_audio_name_textcolor"
                android:textSize="@dimen/audio_name_text_size" />

            <TextView
                android:id="@+id/widget_album_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center"
                android:lines="1"
                android:maxLines="1"
                android:paddingLeft="80px"
                android:paddingRight="80px"
                android:text=""
                android:textColor="@color/widget_album_name_textcolor"
                android:textSize="@dimen/album_name_text_size" />
        </LinearLayout>


        <ImageView
            android:id="@+id/widget_cover_bg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="103px"
            android:layout_marginTop="147px"
            android:src="@drawable/widget_cover_bg_two" />

        <ImageView
            android:id="@+id/widget_cover"
            android:layout_width="202px"
            android:layout_height="202px"
            android:layout_marginLeft="189px"
            android:layout_marginTop="184px" />

        <ImageView
            android:id="@+id/widget_progressBar"
            android:layout_width="@dimen/widget_progress_bar_two_width"
            android:layout_height="@dimen/widget_progress_bar_height"
            android:layout_below="@+id/widget_cover_bg"
            android:layout_centerInParent="true"
            android:background="@drawable/widget_progress_bar_bg" />

        <RelativeLayout
            android:layout_width="380px"
            android:layout_height="wrap_content"
            android:layout_below="@+id/widget_progressBar"
            android:layout_centerHorizontal="true"
            android:layout_marginLeft="111px"
            android:layout_marginTop="8px"
            android:layout_marginRight="111px"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/widget_cur_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:text="00:00"
                android:textColor="@color/widget_timecolor"
                android:textSize="@dimen/time_text_size" />

            <TextView
                android:id="@+id/widget_duration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:text="00:00"
                android:textColor="@color/widget_timecolor"
                android:textSize="@dimen/time_text_size" />

        </RelativeLayout>


        <LinearLayout
            android:id="@+id/widget_play_operation_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginLeft="50px"
            android:layout_marginRight="50px"
            android:layout_marginBottom="20px"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/widget_prev"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:paddingTop="@dimen/widget_action_button_v_padding"
                android:paddingBottom="@dimen/widget_action_button_v_padding"
                android:src="@drawable/selector_widget_btn_play_prev" />

            <ImageView
                android:id="@+id/widget_play_or_pause"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:paddingTop="@dimen/widget_action_button_v_padding"
                android:paddingBottom="@dimen/widget_action_button_v_padding"
                android:src="@drawable/selector_widget_btn_pause" />

            <ImageView
                android:id="@+id/widget_next"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingTop="@dimen/widget_action_button_v_padding"
                android:paddingBottom="@dimen/widget_action_button_v_padding"
                android:layout_weight="1"
                android:src="@drawable/selector_widget_btn_play_next" />
        </LinearLayout>
    </RelativeLayout>
</RelativeLayout>