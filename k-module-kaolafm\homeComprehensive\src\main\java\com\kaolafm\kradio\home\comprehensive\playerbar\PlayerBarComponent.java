package com.kaolafm.kradio.home.comprehensive.playerbar;

import com.kaolafm.kradio.component.Component;
import com.kaolafm.kradio.component.RealCaller;

import java.lang.ref.WeakReference;

/**
 * <AUTHOR>
 * @date 2019-07-10
 */
public class PlayerBarComponent implements Component {

    private WeakReference<ComprehensivePlayerbarContract.IPlayerView> mPlayerViewWeakReference;

    private static final String ON_PROGRESS = "onProgress";

    public PlayerBarComponent() {
    }

    public PlayerBarComponent(ComprehensivePlayerbarContract.IPlayerView playerView) {
        mPlayerViewWeakReference = new WeakReference<>(playerView);
    }

    @Override
    public boolean onCall(RealCaller caller) {
        String actionName = caller.actionName();
        if (ON_PROGRESS.equals(actionName)) {
            if (mPlayerViewWeakReference != null ) {
                ComprehensivePlayerbarContract.IPlayerView playerView = mPlayerViewWeakReference.get();
                if (playerView != null) {
                    playerView.updateProgress(0);
                }
            }
        }
        return false;
    }
}
