package com.kaolafm.kradio.home.comprehensive.gallery;

import android.content.Context;
import android.util.Log;
import com.kaolafm.kradio.lib.bean.HistoryItem;
import com.kaolafm.kradio.history.HistoryManager;
import com.kaolafm.kradio.home.data.Category;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.home.comprehensive.util.HomeUtil;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.common.ResType;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;


/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: DataHandler.java                                               
 *                                                                  *
 * Created in 2018/4/26 下午6:34                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
public class DataHandler {

    public static final String MINE_STR = "我的";

    public static final String GLIDE_DRAWABLE_PREX = "android.resource://" + AppDelegate.getInstance().getContext().getPackageName() + "/drawable/";

    public static final String MUSIC_MINE_ILIKE = "我喜欢的";
    public static final String MUSIC_MINE_DAY30 = "每日30首";
    public static final String MUSIC_MINE_PRIVATE_FM = "私人FM";
    public static final String MUSIC_MINE_FAVOURITE = "我的收藏";

    public static final String RECOMMEND_MINE_MY_MUSIC = "我的音乐";
    public static final String RECOMMEND_MINE_MY_RADIO = "我的电台";

    public static final long ID_QQ_PRIVATE_FM = 0x1230123L;//私人电台
    public static final long ID_QQ_DAY_30 = 0x1230124L;//每日30首
    public static final long ID_QQ_I_LIKE = 0x1230125L;//我喜欢
    public static final long ID_QQ_FAVOURITE = 0x1230126L;//收藏

    private static int[] music_mine_day = new int[]{
            R.drawable.music_mine_day_1,
            R.drawable.music_mine_day_2,
            R.drawable.music_mine_day_3,
            R.drawable.music_mine_day_4,
            R.drawable.music_mine_day_5,
            R.drawable.music_mine_day_6,
            R.drawable.music_mine_day_7,
            R.drawable.music_mine_day_8,
            R.drawable.music_mine_day_9,
            R.drawable.music_mine_day_10,
            R.drawable.music_mine_day_11,
            R.drawable.music_mine_day_12,
            R.drawable.music_mine_day_13,
            R.drawable.music_mine_day_14,
            R.drawable.music_mine_day_15,
            R.drawable.music_mine_day_16,
            R.drawable.music_mine_day_17,
            R.drawable.music_mine_day_18,
            R.drawable.music_mine_day_19,
            R.drawable.music_mine_day_20,
            R.drawable.music_mine_day_21,
            R.drawable.music_mine_day_22,
            R.drawable.music_mine_day_23,
            R.drawable.music_mine_day_24,
            R.drawable.music_mine_day_25,
            R.drawable.music_mine_day_26,
            R.drawable.music_mine_day_27,
            R.drawable.music_mine_day_28,
            R.drawable.music_mine_day_29,
            R.drawable.music_mine_day_30,
            R.drawable.music_mine_day_31
    };

    /**
     * 数据加工
     *
     * @param groups
     * @return
     */
    public static List<Category.Group> handleData(Context context, long cid, List<Category.Group> groups) {

        for (int i = 0; i < groups.size(); i++) {
            Category.Group group = groups.get(i);

            switch (group.appControlType) {
                case Category.LATEST_BROADCAST:
                    handleGroupBroadcast(group);
                    break;
                case Category.MY_MUSIC:
                    handleGroupMusic(group);
                    break;
                case Category.FUNCTION_MODULE:
//                    handleGroupRecommand(group);
                    break;
                default:
                    // Do-Nothing
                    break;
            }
        }

        //删除datalist为空的group
        for (int i = (groups.size() - 1); i >= 0; i--) {
            List<Category.Item> items = groups.get(i).items;
            if (items == null || items.isEmpty()) {
                groups.remove(i);
            }
        }


        return groups;
    }


    private static void handleGroupRecommand(Category.Group group) {
        //推荐:我的电台,我的音乐
        if (group.items == null) {
            group.items = new ArrayList<>();
        }

        // 去掉 首页 我的音乐 小卡片 ,有需要，直接解开代码即可
        Category.Item itemMyMusic = new Category.Item.Builder()
                .id(-1)
                .title(RECOMMEND_MINE_MY_MUSIC)
                .resType(ResType.FUNCTION_ENTER_SMALL)
                .coverImgUrl(GLIDE_DRAWABLE_PREX + R.drawable.home_my_music)
                .cardType(Category.CARD_TYPE_SMALL_FUNCTION)
                .operateType(Category.OPERATE_TYPE_ENTRA)
                .build();


        Category.Item itemMyRadio = new Category.Item.Builder()
                .id(-1)
                .title(RECOMMEND_MINE_MY_RADIO)
                .resType(ResType.FUNCTION_ENTER_SMALL)
                .coverImgUrl(GLIDE_DRAWABLE_PREX + R.drawable.home_my_radio)
                .cardType(Category.CARD_TYPE_SMALL_FUNCTION)
                .operateType(Category.OPERATE_TYPE_ENTRA)
                .build();


        group.items.add(itemMyMusic);
        group.items.add(itemMyRadio);

    }

    private static void handleGroupMusic(Category.Group group) {
        //音乐:
        if (group.items == null) {
            group.items = new ArrayList<>();
        }

        //私人电台
        Category.Item itemPrivateFm = new Category.Item.Builder()
                .id(ID_QQ_PRIVATE_FM)
                .title(MUSIC_MINE_PRIVATE_FM)
                .resType(ResType.MUSIC_MINE_PRIVATE_FM)
                .coverImgUrl(GLIDE_DRAWABLE_PREX + R.drawable.home_music_mine_fm)
                .cardType(Category.CARD_TYPE_SMALL_FUNCTION)
                .operateType(Category.OPERATE_TYPE_PLAY)
                .build();
        itemPrivateFm.firstInGroup = true;

        //每日30首
        int day = Calendar.getInstance().get(Calendar.DAY_OF_MONTH) - 1;
        Category.Item itemDay30 = new Category.Item.Builder()
                .id(ID_QQ_DAY_30)
                .title(MUSIC_MINE_DAY30)
                .resType(ResType.MUSIC_MINE_DAY)
                .coverImgUrl(GLIDE_DRAWABLE_PREX + music_mine_day[day])
                .cardType(Category.CARD_TYPE_SMALL_FUNCTION)
                .operateType(Category.OPERATE_TYPE_PLAY)
                .build();

        //我喜欢
        Category.Item itemILike = new Category.Item.Builder()
                .id(ID_QQ_I_LIKE)
                .title(MUSIC_MINE_ILIKE)
                .resType(ResType.MUSIC_MINE_LIKE)
                .coverImgUrl(GLIDE_DRAWABLE_PREX + R.drawable.home_music_mine_like)
                .cardType(Category.CARD_TYPE_SMALL_FUNCTION)
                .operateType(Category.OPERATE_TYPE_PLAY)
                .build();

        //收藏
        Category.Item itemFavourite = new Category.Item.Builder()
                .id(ID_QQ_FAVOURITE)
                .title(MUSIC_MINE_FAVOURITE)
                .resType(ResType.MUSIC_MINE_FAVOURITE)
                .coverImgUrl(GLIDE_DRAWABLE_PREX + R.drawable.home_music_mine_favourite)
                .cardType(Category.CARD_TYPE_SMALL_FUNCTION)
                .operateType(Category.OPERATE_TYPE_ENTRA)
                .build();


        group.items.add(itemPrivateFm);
        group.items.add(itemDay30);
        group.items.add(itemILike);
        group.items.add(itemFavourite);

    }

    private static void handleGroupBroadcast(Category.Group group) {
        List<HistoryItem> historyItemList = HistoryManager.getInstance().getBroadcastList();
        Log.i("kraido.his", "广播历史个数: " + historyItemList == null ? "0" : "" + historyItemList.size());
        if (historyItemList != null && !historyItemList.isEmpty()) {
            group.items = new ArrayList<>();
            for (int index = 0; index < historyItemList.size(); index++) {
                HistoryItem historyItem = historyItemList.get(index);

                long id = HomeUtil.getLongMaybeError(historyItem.getRadioId());

                String[] temp = historyItem.getRadioTitle().split("  ");
                int size = temp.length;
                String title = temp[0];
                String freq;
                if (size == 1) {
                    freq = "";
                } else {
                    freq = temp[1];
                }

                Category.Item item = new Category.Item.Builder()
                        .id(id)
                        .title(title)
                        .freq(freq)
                        .resType(ResType.BROADCAST_TYPE)
                        .coverImgUrl(historyItem.getPicUrl())
                        .cardType(Category.CARD_TYPE_SMALL_BROADCAST)
                        .build();
                item.parent = group;
                if (index == 0) {
                    item.firstInGroup = true;
                }
                group.items.add(item);
            }
            group.viewType = Category.ID_BROADCAST;

        }

    }
}
