package com.kaolafm.kradio.coin;

import android.app.Activity;
import android.content.res.Configuration;
import android.graphics.Color;
import android.graphics.drawable.AnimationDrawable;
import android.graphics.drawable.Drawable;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.ufreedom.uikit.FloatingText;
import com.ufreedom.uikit.effect.TranslateFloatingAnimator;

/**
 * <AUTHOR>
 **/
public class CoinAnimHelper {

    private static final String TAG = "k.coin";

    private volatile static CoinAnimHelper sInstance;

    private CoinAnimRunnable mCoinAnimRunnable;

    private View.OnClickListener mOnClickListener;

    private CoinAnimHelper() {
    }

    public static CoinAnimHelper getInstance() {
        if (sInstance == null) {
            synchronized (CoinAnimHelper.class) {
                if (sInstance == null) {
                    sInstance = new CoinAnimHelper();
                }
            }
        }
        return sInstance;
    }

    public void setClickListener(View.OnClickListener l) {
        mOnClickListener = l;
    }

    class CoinAnimRunnable implements Runnable {

        ViewGroup mHfvHomeFunctions;

        public CoinAnimRunnable(ViewGroup hfvHomeFunctions) {
            this.mHfvHomeFunctions = hfvHomeFunctions;
        }

        @Override
        public void run() {

            ImageView personIcon = getHomeFunctionsView(mHfvHomeFunctions);
            CoinTag coinTag = new CoinTag();
            coinTag.showCoinAnim = true;
            personIcon.setTag(coinTag);
            AnimationDrawable animDrawable = (AnimationDrawable) ResUtil.getDrawable(R.drawable.anim_person_gold);
            personIcon.setImageDrawable(animDrawable);
            animDrawable.start();

        }

        public void release() {
            this.mHfvHomeFunctions = null;
        }
    }

    public void applyCoinAnim(ViewGroup hfvHomeFunctions) {
        if (mCoinAnimRunnable == null) {
            mCoinAnimRunnable = new CoinAnimRunnable(hfvHomeFunctions);
            hfvHomeFunctions.post(mCoinAnimRunnable);
        }
    }

    public void stop(ViewGroup mHfvHomeFunctions) {
        Logger.e(TAG, "stop: ");
        if (mCoinAnimRunnable != null) {
            mCoinAnimRunnable.release();
        }

        if (mHfvHomeFunctions == null) {
            return;
        }
        mHfvHomeFunctions.removeCallbacks(mCoinAnimRunnable);
        mCoinAnimRunnable = null;
        ImageView personIcon = getHomeFunctionsView(mHfvHomeFunctions);

        Drawable drawable = personIcon.getDrawable();
        if (drawable instanceof AnimationDrawable) {
            ((AnimationDrawable) drawable).stop();
            Drawable d = ResUtil.getDrawable(R.drawable.ic_home_person_normal);
            int dimen = ResUtil.getDimen(R.dimen.m28);
            d.setBounds(0, 0, dimen, dimen);
            personIcon.setImageDrawable(d);
        }

        if (personIcon.getTag() instanceof CoinTag) {
            CoinTag coinTag = (CoinTag) personIcon.getTag();
            coinTag.showCoinAnim = false;
            personIcon.setTag(coinTag);
        } else {
            CoinTag coinTag = new CoinTag();
            coinTag.showCoinAnim = false;
            personIcon.setTag(coinTag);
        }

    }

    private ImageView getHomeFunctionsView(ViewGroup hfvHomeFunctions) {
        //根据需求HomeFunctions第四个view是imageview，针对imageview进行的操作。
        //所以这里强转，如果后期更改需要改这里。防止忘记改这里造成难以查找的问题，所以不做兼容直接强转。
        return (ImageView) hfvHomeFunctions.getChildAt(1);
    }

    public void plusOne(Activity activity, ViewGroup mHfvHomeFunctions, int diff) {
        int offsetY = -30;
        if (ResUtil.getOrientation() == Configuration.ORIENTATION_LANDSCAPE) {
            offsetY = -100;
        }
        // +1 向上移动效果
        FloatingText floatingText = new FloatingText.FloatingTextBuilder(activity)
                .textColor(Color.parseColor("#E34545")) // 漂浮字体的颜色
                .textSize(ResUtil.getDimen(R.dimen.text_size7))  // 浮字体的大小
                .textContent("+" + diff) // 浮字体的内容
                //.offsetX(100) // FloatingText 相对其所贴附View的水平位移偏移量
                .offsetY(offsetY) // FloatingText 相对其所贴附View的垂直位移偏移量
                .floatingAnimatorEffect(new TranslateFloatingAnimator(-200f, 2000)) // 漂浮动画
                //.floatingPathEffect(FloatingPathEffect) // 漂浮的路径
                .build();

        floatingText.attach2Window(); //将FloatingText贴附在Window上

        //启动漂浮效果
        floatingText.startFloating(mHfvHomeFunctions.getChildAt(4)); // 传入一个View，FloatingText 就会相对于这个View执行漂浮效果
    }
}
