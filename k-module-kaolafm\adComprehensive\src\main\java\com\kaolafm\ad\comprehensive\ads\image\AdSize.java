package com.kaolafm.ad.comprehensive.ads.image;

import androidx.annotation.Nullable;

public class AdSize {
    public int width;
    public int height;

    @Override
    public boolean equals(@Nullable Object obj) {
        if(this == obj){
            return true;
        }
        if (obj instanceof AdSize){
            AdSize adSize = (AdSize) obj;
            return this.width == adSize.width && this.height == adSize.height;
        }
        return false;
    }
}
