<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/llRefresh"
        android:layout_width="@dimen/m332"
        android:layout_height="@dimen/m120"
        android:layout_gravity="center"
        android:background="@drawable/shape_sys_toast_layout_bg"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="@dimen/loading_vertical_bias">

        <ProgressBar
            android:id="@+id/refresh_bottom_progressBar"
            android:layout_width="@dimen/m70"
            android:layout_height="@dimen/m70"
            android:layout_marginEnd="@dimen/x30"
            android:indeterminateDrawable="@anim/rotate"
            android:maxWidth="@dimen/m70"
            android:maxHeight="@dimen/m70"
            android:minWidth="@dimen/m70"
            android:minHeight="@dimen/m70" />

        <!--在搜索界面 使用 ConstraintLayout 必须在布局的每一个子view 添加id-->
        <com.kaolafm.kradio.common.widget.CTextView
            android:id="@+id/kradio_widget_ctextview"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/user_qr_loading"
            android:textColor="#eeeeee"
            android:textSize="@dimen/text_size5" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>