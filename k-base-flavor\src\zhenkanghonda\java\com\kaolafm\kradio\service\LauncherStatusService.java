package com.kaolafm.kradio.service;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;

import android.os.RemoteException;
import androidx.annotation.Nullable;
import android.util.Log;

import com.zhenkang.IUpdateLauncherStatusListener;

public class LauncherStatusService extends Service {
    public static final String TAG = "LauncherStatusService";

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return new UpdateLauncherStatus();
    }

    public static class UpdateLauncherStatus extends IUpdateLauncherStatusListener.Stub {

        @Override
        public void updateDrawerStatus(boolean isDrawerOpened) throws RemoteException {
            Log.i(TAG, "updateDrawerStatus");
        }

        @Override
        public void launcherStatus(boolean isRunningForeground) throws RemoteException {
            Log.i(TAG, "launcherStatus");
        }

        @Override
        public void smallCardInstallStatus(boolean isInstall) throws RemoteException {
            Log.i(TAG, "smallCardInstallStatus");
        }

        @Override
        public void bigCardInstallStatus(boolean isInstall) throws RemoteException {
            Log.i(TAG, "bigCardInstallStatus");
        }
    }
}
