/*
 * Copyright (C) 2015 AutoRadio
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.kaolafm.kradio.receiver;

import android.content.Context;
import android.content.Intent;
import androidx.media.session.MediaButtonReceiver;
import android.util.Log;
import android.view.KeyEvent;

import com.kaolafm.utils.MediaButtonManagerUtil;

/******************************************
 * 类描述： 音频远端控制接收者
 *
 * @version: 1.0
 * @author: shaoningYang
 * @time: 2015-6-17 下午8:51:10
 ******************************************/
public class MyRemoteControlEventReceiver extends MediaButtonReceiver {
    private MediaButtonManagerUtil mMediaButtonManagerUtil = new MediaButtonManagerUtil();

    /*
     * (non-Javadoc)
     *
     * @see android.content.BroadcastReceiver#onReceive(android.content.Context,
     * android.content.Intent)
     */
    @Override
    public void onReceive(Context context, Intent intent) {
        KeyEvent key = intent.getParcelableExtra(Intent.EXTRA_KEY_EVENT);
        if (key == null) {
            return;
        }
        int keyAction = key.getAction();
        if (keyAction == KeyEvent.ACTION_UP) {
            return;
        }
        int keyCode = key.getKeyCode();
        //todo audiofocus
//        int currentFocus = KlSdkVehicle.getInstance().getc
        Log.i("MyRemoteControlEventReceiver", "onReceive---->keyCode = " + keyCode + "--->currentFocus = ");
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001203249507?userId=1229522问题
//        if (currentFocus >= 0) {
//            mMediaButtonManagerUtil.manageMediaButtonClick(context, keyCode);
//        }
    }
}
