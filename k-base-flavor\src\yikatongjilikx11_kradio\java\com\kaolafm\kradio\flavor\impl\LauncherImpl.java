package com.kaolafm.kradio.flavor.impl;


import com.kaolafm.kradio.bluetooth.VehicleDisplayManager;

public class LauncherImpl implements LauncherInter {
    @Override
    public void onStart(Object... args) {

    }

    @Override
    public void onCreate(Object... args) {

    }

    @Override
    public void onResume(Object... args) {
        VehicleDisplayManager.getInstance().initDisplayId();
    }

    @Override
    public void onPause(Object... args) {

    }

    @Override
    public void onStop(Object... args) {

    }

    @Override
    public void onRestart(Object... args) {

    }

    @Override
    public void onDestory(Object... args) {

    }
}
