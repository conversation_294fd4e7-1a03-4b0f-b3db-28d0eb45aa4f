package com.kaolafm.kradio.online.player.utils;

import android.content.Context;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.LinearSmoothScroller;
import androidx.recyclerview.widget.RecyclerView;
import android.util.AttributeSet;
import android.util.DisplayMetrics;

/**
 * Created by iblade.Wang on 2019/5/22 17:08
 */
public class CenterLayoutManager extends LinearLayoutManager {
    private boolean isSpeedFast = true;
    private CenterSmoothScroller mCenterSmoothScroller;

    public CenterLayoutManager(Context context) {
        super(context);
    }

    public CenterLayoutManager(Context context, int orientation, boolean reverseLayout) {
        super(context, orientation, reverseLayout);
    }

    public CenterLayoutManager(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    public void setSpeedFast(boolean enable) {
        isSpeedFast = enable;
    }

    @Override
    public void smoothScrollToPosition(RecyclerView recyclerView, RecyclerView.State state, int position) {
        if (mCenterSmoothScroller == null)
            mCenterSmoothScroller = new CenterSmoothScroller(recyclerView.getContext(), isSpeedFast);
        mCenterSmoothScroller.setTargetPosition(position);
        startSmoothScroll(mCenterSmoothScroller);
    }

    public static class CenterSmoothScroller extends LinearSmoothScroller {
        private boolean isSpeedFast = true;

        public CenterSmoothScroller(Context context, boolean isSpeedFast) {
            super(context);
            this.isSpeedFast = isSpeedFast;
        }

        @Override
        public int calculateDtToFit(int viewStart, int viewEnd, int boxStart, int boxEnd, int snapPreference) {
            return (boxStart + (boxEnd - boxStart) / 2) - (viewStart + (viewEnd - viewStart) / 2);
        }

//        @Override
//        protected float calculateSpeedPerPixel(DisplayMetrics displayMetrics) {
//            if (isSpeedFast)
//                return super.calculateSpeedPerPixel(displayMetrics);
//            else return 100f / displayMetrics.densityDpi;
//        }
    }
}
