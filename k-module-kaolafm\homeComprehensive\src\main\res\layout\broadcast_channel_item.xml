<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/m230"
    android:layout_height="wrap_content">

    <com.kaolafm.kradio.component.ui.base.view.OvalImageView
        android:id="@+id/ivLogo"
        android:layout_width="match_parent"
        android:layout_height="@dimen/m230"
        android:background="@drawable/sh_bg_r8l_fff"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/tv_broadcast_type"
        android:layout_width="@dimen/m62"
        android:layout_height="@dimen/m30"
        android:background="@drawable/flag_live_bg"
        android:gravity="center"
        android:text="直播"
        android:textColor="#ffffff"
        android:textSize="@dimen/text_size1"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@id/ivLogo"
        app:layout_constraintTop_toTopOf="@id/ivLogo" />


    <LinearLayout
        android:id="@+id/llFreq"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/m12"
        android:background="@drawable/sh_bg_freq"
        android:ellipsize="end"
        android:maxLines="1"
        android:paddingStart="@dimen/x12"
        android:paddingEnd="@dimen/x12"
        android:paddingBottom="@dimen/m2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="FM "
            android:textColor="@color/item_subcategory_freq"
            android:textSize="@dimen/m16" />

        <TextView
            android:id="@+id/tv_broadcast_freq"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/y24"
            android:textColor="@color/item_subcategory_freq"
            android:textSize="@dimen/text_size1" />
    </LinearLayout>

    <TextView
        android:id="@+id/tv_broadcast_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y17"
        android:ellipsize="end"
        android:gravity="center_horizontal"
        android:maxLines="1"
        android:paddingLeft="@dimen/x10"
        android:paddingRight="@dimen/x10"
        android:text="北京城市电台"
        android:textColor="@color/tv_broadcast_name_color"
        android:textSize="@dimen/text_size5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivLogo" />

</androidx.constraintlayout.widget.ConstraintLayout>
