<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent"
    tools:context="com.kaolafm.kradio.online.mine.login.OnlineLoginActivity">

    <LinearLayout
        android:id="@+id/login_fl_ll"
        android:layout_width="@dimen/x724"
        android:layout_height="@dimen/y441"
        android:layout_centerInParent="true"
        tools:visibility="invisible"
        android:background="@drawable/online_user_login_bg_pic">

        <FrameLayout
            android:id="@+id/login_qr_fl"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="5.5" />

        <FrameLayout
            android:id="@+id/login_fl"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="3" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/login_fl_ll2"
        android:layout_width="@dimen/x724"
        android:layout_height="@dimen/y441"
        android:layout_centerInParent="true"
        android:background="@drawable/online_user_login_bg_pic"
        android:visibility="gone">

        <FrameLayout
            android:id="@+id/login_qr_confirm_fl"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/login_fl_ll3"
        android:layout_width="@dimen/x600"
        android:layout_height="@dimen/y360"
        android:layout_centerInParent="true"
        android:visibility="gone"
        tools:visibility="visible"
        android:background="@drawable/online_user_login_bg_pic">

        <FrameLayout
            android:id="@+id/login_out_fl"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            />
    </LinearLayout>

    <ImageView
        android:visibility="visible"
        android:id="@+id/login_close_iv"
        android:layout_width="@dimen/x44"
        android:layout_height="@dimen/y44"
        android:layout_alignLeft="@+id/login_fl_ll"
        android:layout_alignTop="@+id/login_fl_ll"
        android:layout_marginLeft="@dimen/m12"
        android:layout_marginTop="@dimen/m12"
        android:src="@drawable/online_user_login_close_icon" />
</RelativeLayout>