package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.util.Log;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioClientInter;
import com.kaolafm.kradio.lib.utils.SharedPreferenceUtil;

import static com.kaolafm.kradio.lib.utils.Constants.USER_PROMPT_AGREE;
import static com.kaolafm.kradio.lib.utils.Constants.USER_PROMPT_AGREE_SP_FILE_NAME;
import static com.kaolafm.kradio.lib.utils.Constants.USER_PROMPT_FIRST_SP_FILE_NAME;

public class KRadioClientImpl implements KRadioClientInter {
    @Override
    public boolean canIgnoreLaunchApp(Object... args) {
        return false;
    }

    @Override
    public boolean isAllowPlay(Object... args) {
        SharedPreferenceUtil sharedPreferenceUtil = SharedPreferenceUtil
                .getInstance(AppDelegate.getInstance().getContext(), USER_PROMPT_AGREE_SP_FILE_NAME, Context.MODE_PRIVATE);
        boolean userPromptAgree = sharedPreferenceUtil.getBoolean(USER_PROMPT_AGREE, false);
        return userPromptAgree;
    }
}
