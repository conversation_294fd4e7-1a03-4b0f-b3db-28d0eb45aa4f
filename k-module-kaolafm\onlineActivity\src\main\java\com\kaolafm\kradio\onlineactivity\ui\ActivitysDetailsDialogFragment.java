package com.kaolafm.kradio.onlineactivity.ui;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.PointF;
import android.graphics.drawable.ColorDrawable;
import android.media.MediaMetadataRetriever;
import android.media.MediaPlayer;
import android.os.Bundle;
import androidx.annotation.DrawableRes;
import androidx.annotation.IdRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.VideoView;


import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.bitmap_recycle.BitmapPool;
import com.bumptech.glide.load.resource.bitmap.BitmapTransformation;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.transition.Transition;
import com.davemorrissey.labs.subscaleview.ImageSource;
import com.davemorrissey.labs.subscaleview.ImageViewState;
import com.davemorrissey.labs.subscaleview.SubsamplingScaleImageView;
import com.kaolafm.base.utils.DateUtil;
import com.kaolafm.kradio.common.report.ReportUtil;
import com.kaolafm.kradio.common.widget.OvalImageView;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.utils.imageloader.GlideApp;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.online.common.utils.LinearGradientFontSpan;
import com.kaolafm.opensdk.api.CrashMessageBaseBean;
import com.kaolafm.opensdk.api.activity.model.Activity;
import com.kaolafm.opensdk.crash.CrashPlayerHelper;
import com.kaolafm.opensdk.crash.Icrashstate;

import java.io.File;
import java.security.MessageDigest;
import java.util.List;

import static com.bumptech.glide.load.resource.bitmap.VideoDecoder.FRAME_OPTION;

/**
 * 活动详情
 * 调用示例：
 * MessageBubbleDialogFragment dialogFragment = (MessageBubbleDialogFragment) new Dialogs.Builder().setType(Dialogs.TYPE_MESSAGE_BUBBLE).create();
 * dialogFragment.setBubbleType(MessageBubbleDialogFragment.TYPE_DANGER)
 * .setSceneBitmap(BitmapFactory.decodeResource(getResources(), R.drawable.online_message_bubble_thunderbolt))
 * .setTitle("国家应急广播")
 * .setSubTitle("石家庄市发布暴雨雷电红色预警")
 * .setButtons(new ArrayList<MessageBubbleDialogFragment.MessageBubbleButton>() {{
 * add(new MessageBubbleDialogFragment.MessageBubbleButton("查看详情", R.id.online_message_bubble_button_1_id));
 * add(new MessageBubbleDialogFragment.MessageBubbleButton("倒计时", 20L, R.id.online_message_bubble_button_2_id));
 * add(new MessageBubbleDialogFragment.MessageBubbleButton("删除", R.drawable.online_search_icon_delete, R.id.online_message_bubble_button_3_id));
 * add(new MessageBubbleDialogFragment.MessageBubbleButton("确认", R.drawable.online_user_no_login_icon, 15, R.id.online_message_bubble_button_4_id));
 * }}, new View.OnClickListener() {
 *
 * @Override public void onClick(View v) {
 * int id = v.getId();
 * if (id == R.id.online_message_bubble_button_1_id) {
 * Log.e(TAG, "点击按钮：1");
 * } else if (id == R.id.online_message_bubble_button_2_id) {
 * Log.e(TAG, "点击按钮：2");
 * } else if (id == R.id.online_message_bubble_button_3_id) {
 * Log.e(TAG, "点击按钮：3");
 * } else if (id == R.id.online_message_bubble_button_4_id) {
 * Log.e(TAG, "点击按钮：4");
 * }
 * }
 * }).show(getSupportFragmentManager(), "MessageBubbleDialogFragment");
 */
public class ActivitysDetailsDialogFragment extends Dialog {
    private ImageView mXfermodeView;
    private ImageView bubbleIcon;
    private ImageView play_iv;
    private ImageView viedo_iv;
    private ImageView viedo_replay;
    private TextView bubbleTitle;
    private TextView bubbleSubTitle;
    private TextView bubbleContent;
    private LinearLayout bubbleButtonParent;
    private LinearLayout content_ll;
    private VideoView video_view;
    private ImageView bubble_pic_iv;
    private ImageView bubble_pic_detils_iv;
    private SubsamplingScaleImageView pic_big_iv;
    private RelativeLayout video_view_rl, activity_video_rl, root_view, bubble_pic_rl, pic_big_rl;
    private View loading;

    private String mTitle, mSubTitle;
    private String mIconResource;
    private Activity activityBean;
    private Bitmap destBitmap;
    private Bitmap srcBitmap;
    private long startTimr = -1;
    private boolean hasSceneBg = false;
    private boolean isPlay = false;
    //    private List<MessageBubbleButton> mButtons;
    private View.OnClickListener mViewClickListener;

    public ActivitysDetailsDialogFragment(@NonNull Context context) {
        super(context, R.style.FullScreenDialogTheme);
    }


//    public static ActivitysDetailsDialogFragment create() {
//        ActivitysDetailsDialogFragment fragment = new ActivitysDetailsDialogFragment();
//        return fragment;
//    }

//    @Override
//    public void onStart() {
//        setWidth(MATCH_PARENT);
//        setHeight(MATCH_PARENT);
//        setDimAmount(0);
//        setOutCancel(true);
//        setAnimStyle(R.style.OnlineMessageBubbleAnimation);
//        setGravity(Gravity.CENTER);
//        super.onStart();
//    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.online_layout_activity_details);
        root_view = findViewById(R.id.root_view);
        mXfermodeView = findViewById(R.id.bubbleBg);
        bubbleIcon = findViewById(R.id.bubbleIcon);
        bubbleTitle = findViewById(R.id.bubbleTitle);
        play_iv = findViewById(R.id.play_iv);
        viedo_iv = findViewById(R.id.viedo_iv);
        bubbleSubTitle = findViewById(R.id.bubbleSubTitle);
        bubbleContent = findViewById(R.id.bubbleContent);
        bubbleButtonParent = findViewById(R.id.bubbleButtonParent);
        video_view = findViewById(R.id.video_view);
        viedo_replay = findViewById(R.id.viedo_replay);
        video_view_rl = findViewById(R.id.video_view_rl);
        activity_video_rl = findViewById(R.id.activity_video_rl);
        content_ll = findViewById(R.id.content_ll);
        bubble_pic_iv = findViewById(R.id.bubble_pic_iv);
        bubble_pic_detils_iv = findViewById(R.id.bubble_pic_detils_iv);
        bubble_pic_rl = findViewById(R.id.bubble_pic_rl);
        pic_big_rl = findViewById(R.id.pic_big_rl);
        pic_big_iv = findViewById(R.id.pic_big_iv);
        loading = findViewById(R.id.loading);

        Window window = this.getWindow();
        //设置弹出位置
//        window.setGravity(Gravity.BOTTOM | Gravity.START);

        int matchParent = ViewGroup.LayoutParams.MATCH_PARENT;//父布局的宽度

        WindowManager.LayoutParams lp = window.getAttributes();
        lp.width = matchParent;
        lp.height = matchParent;
//        lp.x = matchParent;
//        lp.y = 300;  //设置出现的高度，距离顶部
        window.setAttributes(lp);
        //去除系统自带的margin
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        //背景全透明
        window.setDimAmount(0f);
        //设置弹出动画
        window.setWindowAnimations(R.style.OnlineMessageBubbleAnimation);
        //设置对话框大小
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
//        setBubbleType(activityBean.getEventLevel());
        setTitle(activityBean.getName());
        setSubTitle(DateUtil.formatMillis("yyyy.MM.dd", Long.parseLong(activityBean.getStartTime()))
                + "-" + DateUtil.formatMillis("yyyy.MM.dd", Long.parseLong(activityBean.getEndTime())));
        setBubbleContent(activityBean.getDescription());
        setSceneBitmap(activityBean.getBackgroundUrl());
//        setButtons(mButtons, mViewClickListener);
        if (!TextUtils.isEmpty(activityBean.getVedioUrl())) {
            //显示视频
            bubble_pic_rl.setVisibility(View.GONE);
            activity_video_rl.setVisibility(View.VISIBLE);
            loadVideoScreenshot(activityBean.getVedioUrl(), viedo_iv, 3000);
        } else if (!TextUtils.isEmpty(activityBean.getImgUrl())) {
            //显示图片
            activity_video_rl.setVisibility(View.GONE);
            bubble_pic_rl.setVisibility(View.VISIBLE);
            ImageLoader.getInstance().displayImage(getContext(), activityBean.getImgUrl()
                    , bubble_pic_iv, ResUtil.getDrawable(R.drawable.message_pic_error));
            loadBigPic(activityBean.getImgUrl());
            bubble_pic_iv.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    //显示大图
                    pic_big_rl.setVisibility(View.VISIBLE);
                }
            });
            pic_big_rl.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    pic_big_rl.setVisibility(View.GONE);
                }
            });
        }
        //音频是都有的，视频和图片二选一
        if (!TextUtils.isEmpty(activityBean.getRadioUrl())) {
            //显示音频
            play_iv.setVisibility(View.VISIBLE);
        } else {
            play_iv.setVisibility(View.GONE);
        }
        root_view.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (viedo_iv != null) {
                    video_view.stopPlayback();
                }
                if (CrashPlayerHelper.getInstance().isPlay()) {
                    CrashPlayerHelper.getInstance().stopPlayTips();
                }
                dismiss();
            }
        });
        play_iv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (activityBean != null) {
                    if (TextUtils.isEmpty(activityBean.getRadioUrl())) {
                        ToastUtil.showNormal(getContext(), "地址为空");
                        return;
                    }
                    if (isPlay) {
                        return;
                    }
                    isPlay = true;
                    play_iv.setImageDrawable(ResUtil.getDrawable(R.drawable.online_msg_play_not_icon));
                    loading.setVisibility(View.VISIBLE);
                    CrashPlayerHelper.getInstance().setIcrashstate(new Icrashstate() {
                        @Override
                        public void onCrashstate(int i) {
                            isPlay = false;
                            play_iv.setImageDrawable(ResUtil.getDrawable(R.drawable.online_msg_play_icon));
                        }

                        @Override
                        public void onBufferingUpdate(CrashMessageBaseBean crashMessageBaseBean) {
                            loading.setVisibility(View.GONE);
                        }
                    }).playTips(activityBean.getRadioUrl());
                }
            }
        });
        viedo_iv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (video_view != null) {
                    content_ll.setVisibility(View.GONE);
                    mXfermodeView.setVisibility(View.GONE);
                    play_iv.setVisibility(View.GONE);

                    playVideo();
                }
            }
        });
        viedo_replay.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (video_view != null) {
                    viedo_replay.setVisibility(View.GONE);
                    playVideo();
                }
            }
        });

    }

    /**
     * 加载大图显示
     */
    private void loadBigPic(String url) {
        Glide.with(getContext()).downloadOnly()
                .load(url)
                .into(new SimpleTarget<File>() {
                    @Override
                    public void onResourceReady(@NonNull File resource, @Nullable Transition<? super File> transition) {
                        pic_big_iv.setMinimumScaleType(SubsamplingScaleImageView.SCALE_TYPE_CENTER_CROP);
                        pic_big_iv.setImage(ImageSource.uri(resource.getAbsolutePath()),
                                new ImageViewState(0f, new PointF(0f, 0f), 0));
                    }
                });
    }

    private void playVideo() {
        if (TextUtils.isEmpty(activityBean.getVedioUrl())) {
            ToastUtil.showNormal(getContext(), "播放失败");
            return;
        }
        video_view_rl.setVisibility(View.VISIBLE);
        video_view.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
            @Override
            public void onCompletion(MediaPlayer mp) {
                viedo_replay.setVisibility(View.VISIBLE);
            }
        });
        video_view.setVideoPath(activityBean.getVedioUrl());
        video_view.requestFocus();
        video_view.start();
//使视频能够暂停、播放、进度条显示等控制
//        MediaController mediaController = new MediaController(getContext());
//        video_view.setMediaController(mediaController);
//        mediaController.setMediaPlayer(video_view);
    }

    @Override
    public void show() {
        super.show();
    }

    @Override
    public void dismiss() {
        startTimr = System.currentTimeMillis();
        ReportUtil.addPageShowEvent(startTimr, getPageId());
        startTimr = -1;
        super.dismiss();
    }

    private String getPageId() {
        return Constants.ONLINE_PAGE_ID_ACTIVITY_DATEILS;
    }

    public ActivitysDetailsDialogFragment setTitle(String title) {
        this.mTitle = title;
        if (bubbleTitle != null)
            bubbleTitle.setText(title);
        return this;
    }

    public ActivitysDetailsDialogFragment setSubTitle(String subtitle) {
        this.mSubTitle = subtitle;
        if (bubbleSubTitle != null)
            bubbleSubTitle.setText(getRadiusGradientSpan(subtitle, 0));
        return this;
    }

    public ActivitysDetailsDialogFragment setBubbleContent(String content) {

        if (bubbleContent != null)
            bubbleContent.setText(content);
        return this;
    }

    public ActivitysDetailsDialogFragment setActivityBean(Activity activityBean) {
        this.activityBean = activityBean;
        return this;
    }


    private ActivitysDetailsDialogFragment setSceneBitmap(String url) {
        if (!TextUtils.isEmpty(url))
            ImageLoader.getInstance().displayImage(getContext(), url, mXfermodeView);
        return this;
    }

//    public MessageDetailsDialogFragment setSceneBitmap(Bitmap bitmap) {
//        return setSceneBitmap(bitmap, true);
//    }

    private void setDestBitmap(Bitmap bitmap) {
        if (destBitmap != null && !destBitmap.isRecycled()) {
            destBitmap.recycle();
        }
        destBitmap = bitmap;

    }

    /**
     * 灾害等级:1.红色预警 2.橙色预警 3.黄色预警 4.蓝色预警
     */
    public ActivitysDetailsDialogFragment setBubbleType(String type) {

        int resourceId = 0;
        switch (Integer.parseInt(type)) {
            case 1:
                resourceId = R.drawable.online_message_red_iconr;
                break;
            case 2:
                resourceId = R.drawable.online_message_yellow_icon;
                break;
            case 3:
                resourceId = R.drawable.online_message_blue_icon;
                break;
            case 4:
                resourceId = R.drawable.online_message_green_icon;
                break;
            default:
                resourceId = 0;
        }
        if (resourceId != 0) {
            bubbleIcon.setImageResource(resourceId);
        }
        return this;
    }

    public ActivitysDetailsDialogFragment setButtons(List<MessageBubbleButton> buttonTexts, View.OnClickListener onClickListener) {
//        this.mButtons = buttonTexts;
        this.mViewClickListener = onClickListener;
        if (bubbleButtonParent == null) return this;
        boolean hasButton = bubbleButtonParent.getChildCount() > 0;
        bubbleButtonParent.removeAllViews();
        if (buttonTexts == null || buttonTexts.isEmpty()) {
            if (hasButton) {
                Bitmap resetDestBitmap;
                if (hasSceneBg) {
                    resetDestBitmap = BitmapFactory.decodeResource(getContext().getResources(), R.drawable.online_message_bubble_nonebutton_bg_board);
                } else {
                    resetDestBitmap = BitmapFactory.decodeResource(getContext().getResources(), R.drawable.online_message_bubble_nonebutton_bg);
                }
                setDestBitmap(resetDestBitmap);
            }
            return this;
        }

        int perPadding = ResUtil.getDimen(R.dimen.y6) / (buttonTexts.size() - 1);
        for (int i = 0; i < buttonTexts.size(); i++) {
            MessageBubbleButton bt = buttonTexts.get(i);
            View button = createButtonView(bubbleButtonParent, bt, perPadding * i);
            button.setOnClickListener(onClickListener);
            if (i < buttonTexts.size() - 1)
                createButtonDividerView(bubbleButtonParent, perPadding * (i + 1) / 2);
        }
        if (!hasButton) {
            Bitmap resetDestBitmap;
            if (hasSceneBg) {
                resetDestBitmap = BitmapFactory.decodeResource(getContext().getResources(), R.drawable.online_message_bubble_button_bg);
            } else {
                resetDestBitmap = BitmapFactory.decodeResource(getContext().getResources(), R.drawable.online_message_bubble_button_bg);
            }
            setDestBitmap(resetDestBitmap);
        }
        return this;
    }

    private View createButtonDividerView(ViewGroup parent, int dPadding) {
        View view = new View(getContext());
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(ResUtil.getDimen(R.dimen.x1), LinearLayout.LayoutParams.MATCH_PARENT);
        params.setMargins(0, ResUtil.getDimen(R.dimen.y21) - dPadding, 0, ResUtil.getDimen(R.dimen.y26) + dPadding);
        view.setLayoutParams(params);
        view.setBackgroundResource(R.drawable.online_message_bubble_button_divider);
        parent.addView(view);
        return view;
    }

    /**
     * 创建按钮
     *
     * @param parent
     * @param bt
     * @param dPadding 按钮从左到右依次上移，该值表示当前按钮应上移的距离，px
     * @return
     */
    private View createButtonView(ViewGroup parent, MessageBubbleButton bt, int dPadding) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.online_bubble_button, parent, false);
        view.setPadding(view.getPaddingStart(), view.getPaddingTop() - dPadding, view.getPaddingEnd(), view.getPaddingBottom() + dPadding);

        ImageView iconIv = view.findViewById(R.id.messageBubbleIconIv);
        TextView titleTv = view.findViewById(R.id.messageBubbleTextTv);
        TextView timerTv = view.findViewById(R.id.messageBubbleTimerTv);
        if (bt.icon == -1) {
            iconIv.setVisibility(View.GONE);
        } else {
            iconIv.setImageResource(bt.icon);
        }
        if (bt.timer == -1) {
            timerTv.setVisibility(View.GONE);
        } else {
            timerTv.setText(String.format(ResUtil.getString(R.string.online_message_bubble_timer), bt.timer));
        }
        titleTv.setText(bt.text);
        view.setId(bt.id);
        parent.addView(view);
        return view;
    }

    public static final class MessageBubbleButton {

        private String text;
        @DrawableRes
        private int icon = -1;
        private long timer = -1;
        @IdRes
        private int id;

        public MessageBubbleButton(String text, int id) {
            this.text = text;
            this.id = id;
        }

        public MessageBubbleButton(String text, long timer, int id) {
            this.text = text;
            this.timer = timer;
            this.id = id;
        }

        public MessageBubbleButton(String text, int icon, int id) {
            this.text = text;
            this.icon = icon;
            this.id = id;
        }

        public MessageBubbleButton(String text, int icon, long timer, int id) {
            this.text = text;
            this.icon = icon;
            this.timer = timer;
            this.id = id;
        }
    }

    public SpannableStringBuilder getRadiusGradientSpan(String string, int lineHeight) {
        SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder(string);
        LinearGradientFontSpan span = new LinearGradientFontSpan(
                ResUtil.getColor(R.color.online_activity_dateils_time_text_start_color)
                , ResUtil.getColor(R.color.online_activity_dateils_time_text_end_color));
        spannableStringBuilder.setSpan(span, 0, spannableStringBuilder.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spannableStringBuilder;

    }

    /**
     * 使用Glide方式获取视频某一帧
     *
     * @param uri             视频地址
     * @param imageView       设置image
     * @param frameTimeMicros 获取某一时间帧.
     */
    public void loadVideoScreenshot(String uri, ImageView imageView, long frameTimeMicros) {
        RequestOptions requestOptions = RequestOptions.frameOf(frameTimeMicros);
        requestOptions.set(FRAME_OPTION, MediaMetadataRetriever.OPTION_CLOSEST);
        requestOptions.transform(new BitmapTransformation() {
            @Override
            protected Bitmap transform(@NonNull BitmapPool pool, @NonNull Bitmap toTransform, int outWidth, int outHeight) {
                Log.d("--使用glide方式--", "高度为" + toTransform.getHeight() + "寛度为" + toTransform.getWidth());
                return toTransform;
            }

            @Override
            public void updateDiskCacheKey(MessageDigest messageDigest) {
                try {
                    messageDigest.update((getContext().getPackageName() + "RotateTransform").getBytes("utf-8"));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
        Glide.with(getContext()).load(uri).apply(requestOptions).into(imageView);
    }
}
