package com.kaolafm.kradio.component;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.opensdk.log.Logging;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2019-07-01
 */
public class ComponentChain {

    private RealCaller mCaller;

    private int mIndex = 0;

    private List<ComponentInterceptor> mInterceptors = new ArrayList<>();

    public ComponentChain(RealCaller caller) {
        mCaller = caller;
    }

    public RealCaller caller() {
        return mCaller;
    }
    public ComponentClient client() {
        return mCaller.client();
    }

    void addInterceptors(Collection<ComponentInterceptor> interceptors) {
        if (!ListUtil.isEmpty(interceptors)) {
            mInterceptors.addAll(interceptors);
        }
    }

    void addInterceptor(ComponentInterceptor interceptor) {
        if (interceptor != null) {
            mInterceptors.add(interceptor);
        }
    }

    public ComponentResult proceed() {

        if (mIndex >= mInterceptors.size()) {
            return ComponentResult.defaultNullResult();
        }

        ComponentInterceptor interceptor = mInterceptors.get(mIndex++);
        if (interceptor == null) {
            return proceed();
        }

        ComponentResult result;
        if (mCaller.isFinished()) {
            result = mCaller.result();
        }else {
            try {
                result = interceptor.intercept(this);
            } catch (Exception e) {
                result = ComponentResult.defaultExceptionResult(e);
            }
        }
        if (result == null) {
            result = ComponentResult.defaultNullResult();
        }
        Logging.d("proceed: result="+result.getCode());
        mCaller.setResult(result);
        return result;
    }
}
