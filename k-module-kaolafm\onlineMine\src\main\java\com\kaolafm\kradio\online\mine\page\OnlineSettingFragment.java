package com.kaolafm.kradio.online.mine.page;

import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import androidx.annotation.CallSuper;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.CompoundButton;
import android.widget.LinearLayout;
import android.widget.Switch;
import android.widget.TextView;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.base.utils.SpUtil;
import com.kaolafm.kradio.common.SkinStateManager;
import com.kaolafm.kradio.common.helper.SkinHelper;
import com.kaolafm.kradio.common.report.ReportUtil;
import com.kaolafm.kradio.k_kaolafm.R; 
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.ui.online.BaseViewPagerLazyFragment;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.common.utils.OnlineConstants;
import com.kaolafm.kradio.online.mine.MineActivity;
import com.kaolafm.kradio.onlinesetting.ISettingView;
import com.kaolafm.kradio.onlinesetting.Item;
import com.kaolafm.kradio.onlinesetting.bean.OnlineToneQuality;
import com.kaolafm.kradio.onlinesetting.SettingItem;
import com.kaolafm.kradio.onlinesetting.SettingModel;
import com.kaolafm.kradio.onlinesetting.SettingPresent;
import com.kaolafm.kradio.onlinesetting.bean.SettingConfigBean;
import com.kaolafm.opensdk.api.media.model.ToneQuality;
import com.kaolafm.opensdk.player.logic.util.ToneQualityHelper;

import java.util.List;
 
import skin.support.SkinCompatManager;

/**
 * 设置
 * 蔡佳彬
 */
public class OnlineSettingFragment extends BaseViewPagerLazyFragment<SettingPresent> implements ISettingView {

 
    LinearLayout mine_setting_theme_a_ll;//白天 
    TextView mine_setting_theme_a_tv; 
    LinearLayout mine_setting_theme_b_ll;//黑夜 
    TextView mine_setting_theme_b_tv; 
    Switch setting_msg_switch;//消息播放 
    Switch setting_msg_helper_switch;//小助手 
    Switch setting_msg_travel_switch;//出行 
    TextView rl_a_tv; 
    TextView rl_b_tv; 
    TextView rl_b_tips_tv; 
    TextView rl_c_tv; 
    TextView rl_c_tips_tv; 
    LinearLayout soundQualitiesParentView; 
    LinearLayout toneQualityTitle; 
    LinearLayout skinTitleView;

    SettingModel settingModel;
    private List<OnlineToneQuality> soundQuality;

    public OnlineSettingFragment() {
        // Required empty public constructor
    }


    public static OnlineSettingFragment newInstance() {
        OnlineSettingFragment fragment = new OnlineSettingFragment();
        return fragment;
    }

    @Override
    protected boolean isReportFragment() {
        return true;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }


    @Override
    protected int getLayoutId() {
        return R.layout.online_fragment_setting;
    }

    @Override
    protected SettingPresent createPresenter() {
        return new SettingPresent(this);
    }

    @Override
    public void initView(View view) {

        mine_setting_theme_a_ll=view.findViewById(R.id.mine_setting_theme_a_ll);
        mine_setting_theme_a_tv=view.findViewById(R.id.mine_setting_theme_a_tv);
        mine_setting_theme_b_ll=view.findViewById(R.id.mine_setting_theme_b_ll);
        mine_setting_theme_b_tv=view.findViewById(R.id.mine_setting_theme_b_tv);
        setting_msg_switch=view.findViewById(R.id.setting_msg_switch);
        setting_msg_helper_switch=view.findViewById(R.id.setting_msg_helper_switch);
        setting_msg_travel_switch=view.findViewById(R.id.setting_msg_travel_switch);
        rl_a_tv=view.findViewById(R.id.rl_a_tv);
        rl_b_tv=view.findViewById(R.id.rl_b_tv);
        rl_b_tips_tv=view.findViewById(R.id.rl_b_tips_tv);
        rl_c_tv=view.findViewById(R.id.rl_c_tv);
        rl_c_tips_tv=view.findViewById(R.id.rl_c_tips_tv);
        soundQualitiesParentView=view.findViewById(R.id.soundQualitiesParentView);
        toneQualityTitle=view.findViewById(R.id.toneQualityTitle);
        skinTitleView=view.findViewById(R.id.skinTitleView);
 
        
        setting_msg_helper_switch.setEnabled(false);
        setting_msg_travel_switch.setEnabled(false);
        initThemeTabLayout();
//        mPresenter.getSoundQualities();
//        mPresenter.getDayNightSkins();
//        mPresenter.getSettingConfig();
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
            ToastUtil.showInfo(getContext(), ResUtil.getString(R.string.network_error_toast));
            return;
        }
        mPresenter.getSoundQualities();
        mPresenter.getDayNightSkins();
        mPresenter.getSettingConfig();
    }

    private void initSwitch() {
        setting_msg_switch.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                SpUtil.putInt(OnlineConstants.VOICE_SPEAK, isChecked ? 2 : 1);
                //上报开关状态
                ReportUtil.addSettingVoiceSwiych(isChecked ? 0 : 1);
            }
        });
        setting_msg_helper_switch.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                SpUtil.putInt(OnlineConstants.VOICE_ASSISTANT, isChecked ? 2 : 1);
            }
        });
        setting_msg_travel_switch.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                SpUtil.putInt(OnlineConstants.TRAVEL_SERVICE, isChecked ? 2 : 1);
            }
        });
    }

    /**
     * 加载换肤相关
     */
    public void initThemeTabLayout() {
        //白天
        mine_setting_theme_a_ll.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                setTheme(SkinHelper.DAY_SKIN);
//                changetheme(false);
            }
        });
        //黑夜
        mine_setting_theme_b_ll.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                setTheme(SkinHelper.NIGHT_SKIN);
//                changetheme(true);

            }
        });
    }

    /**
     * 改变皮肤选择状态
     *
     * @param b true-黑夜
     */
    private void changetheme(boolean b) {
        if (!b) {
            mine_setting_theme_a_tv.setTextColor(ResUtil.getColor(R.color.online_setting_tab_text_color));

            mine_setting_theme_b_tv.setTextColor(ResUtil.getColor(R.color.online_setting_tab_text_color_not));

            mine_setting_theme_a_ll.setBackground(ResUtil.getDrawable(R.drawable.tab_setting_indicator_bg));
            mine_setting_theme_b_ll.setBackground(new ColorDrawable());
        } else {
            mine_setting_theme_a_tv.setTextColor(ResUtil.getColor(R.color.online_setting_tab_text_color_not));
            mine_setting_theme_b_tv.setTextColor(ResUtil.getColor(R.color.online_setting_tab_text_color));

            mine_setting_theme_b_ll.setBackground(ResUtil.getDrawable(R.drawable.tab_setting_indicator_bg));
            mine_setting_theme_a_ll.setBackground(new ColorDrawable());
        }
    }

    private void setTheme(String skin) {

        SkinCompatManager.getInstance().loadSkin(skin, new SkinCompatManager.SkinLoaderListener() {
            @Override
            public void onStart() {

            }

            @Override
            public void onSuccess() {
                ImageLoader
                        .getInstance().initDefault(ResUtil.getDrawable(R.drawable.media_default_pic), ResUtil.getDrawable(R.drawable.media_default_pic));
                SkinStateManager.getInstance().notifyLoadSkinSuccess();

                //因为状态是动态设置的所以切换皮肤之后要触发一次刷新
                if (skin.equals(SkinHelper.NIGHT_SKIN)) {
                    changetheme(true);//更新皮肤选择的
                }else {
                    changetheme(false);//更新皮肤选择的
                }
                if (getContext() instanceof MineActivity) {
                    ((MineActivity) getContext()).updateSink();
                }
                //更新音质选择
                if(ListUtil.isEmpty(soundQuality))return;
                for (int i = 0; i < soundQuality.size(); i++) {
                    OnlineToneQuality onlineToneQuality = soundQuality.get(i);
                    changeAcoustics(onlineToneQuality, soundQualitiesParentView.getChildAt(i));
                }
            }

            @Override
            public void onFailed(String errMsg) {

            }
        }, SkinCompatManager.SKIN_LOADER_STRATEGY_ASSETS);
        SkinHelper.setSkinName(getContext(), skin);
    }

    @Override
    @CallSuper
    protected void showAccordingToScreen(int orientation) {
        super.showAccordingToScreen(orientation);
//        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
//            mTabLayout.setTabPadding(ScreenUtil.px2dp(ResUtil.getDimen(R.dimen.x35)));
//        } else {
//            mTabLayout.setTabPadding(ScreenUtil.px2dp(ResUtil.getDimen(R.dimen.x20)));
//        }

    }


    @Override
    public void showDayNightSkins(List<Item> dayNightSkins) {
        for (Item item : dayNightSkins) {
            if (item.check) {
                if (item.skin == SkinHelper.DAY_SKIN) {
                    //白天
                    changetheme(false);
                } else {
                    //黑夜
                    changetheme(true);
                }
            }
        }
    }


    @Override
    public void showItems(List<SettingItem> itemList) {

    }

    @Override
    public void showSoundQuality(List<OnlineToneQuality> soundQualities, ToneQuality currQuality) {
        this.soundQuality = soundQualities;
        soundQualitiesParentView.removeAllViews();
        if (ListUtil.isEmpty(soundQualities)) {
            ViewUtil.setViewVisibility(toneQualityTitle, View.GONE);
            ViewUtil.setViewVisibility(soundQualitiesParentView, View.GONE);
            LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) skinTitleView.getLayoutParams();
            layoutParams.setMargins(0, 0, 0, 0);
            skinTitleView.setLayoutParams(layoutParams);
            return;
        }
        ViewUtil.setViewVisibility(toneQualityTitle, View.VISIBLE);
        ViewUtil.setViewVisibility(soundQualitiesParentView, View.VISIBLE);
        boolean isFoundCurrQuality = false;
        for (int i = 0; i < soundQualities.size(); i++) {
            OnlineToneQuality onlineToneQuality = soundQualities.get(i);
            addToneQualityView(onlineToneQuality, i);
            if (onlineToneQuality.isCheck()) {
                isFoundCurrQuality = true;
            }
        }
        if (!isFoundCurrQuality) {
            if (currQuality != null) {
                showErrorToast(String.format(ResUtil.getString(R.string.online_not_found_current_quality),
                        currQuality.getTitle(), soundQualities.get(0).getQuality().getTitle()));
            }
            soundQualitiesParentView.getChildAt(0).performClick();
        }
    }

    private void addToneQualityView(OnlineToneQuality onlineToneQuality, int index) {
        if (onlineToneQuality == null || onlineToneQuality.getQuality() == null) return;
        View child = LayoutInflater.from(getActivity()).inflate(R.layout.online_item_sound_quality, soundQualitiesParentView, false);
        child.setTag(index);
        TextView titleView = child.findViewById(R.id.mine_setting_acoustics_tv);
        TextView subTitleView = child.findViewById(R.id.mine_setting_acoustics_tips_tv);

        titleView.setText(onlineToneQuality.getQuality().getTitle());
        subTitleView.setText(onlineToneQuality.getQuality().getDes());

        changeAcoustics(onlineToneQuality, child, titleView, subTitleView);

        child.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Integer position = (Integer) v.getTag();
                setToneQualitiesToChecked(position);
            }
        });

        soundQualitiesParentView.addView(child);
    }

    private void setToneQualitiesToChecked(int index) {
        OnlineToneQuality onlineToneQuality;
        for (int i = 0; i < this.soundQuality.size(); i++) {
            onlineToneQuality = this.soundQuality.get(i);
            if (onlineToneQuality.isCheck() && i != index) {
                onlineToneQuality.setCheck(false);
                changeAcoustics(onlineToneQuality, soundQualitiesParentView.getChildAt(i));
            }
            if (i == index && !onlineToneQuality.isCheck()) {
                onlineToneQuality.setCheck(true);
                changeAcoustics(onlineToneQuality, soundQualitiesParentView.getChildAt(i));
                // 音质选择埋点上报
                ReportUtil.addToneSelectEvent(onlineToneQuality.getQuality().getType());
            }
        }
    }

    /**
     * 改变音质选择状态
     */
    private void changeAcoustics(OnlineToneQuality onlineToneQuality, View child) {
        TextView titleView = child.findViewById(R.id.mine_setting_acoustics_tv);
        TextView subTitleView = child.findViewById(R.id.mine_setting_acoustics_tips_tv);
        changeAcoustics(onlineToneQuality, child, titleView, subTitleView);
    }

    /**
     * 改变音质选择状态
     */
    private void changeAcoustics(OnlineToneQuality onlineToneQuality, View child, TextView titleView, TextView subTitleView) {
        if (onlineToneQuality.isCheck()) {
            titleView.setTextColor(ResUtil.getColor(R.color.online_setting_tab_text_color));
            subTitleView.setTextColor(ResUtil.getColor(R.color.online_setting_tab_text_color_50));
            child.setBackground(ResUtil.getDrawable(R.drawable.tab_setting_indicator_bg));
            ToneQualityHelper.getInstance().setToneQuality(onlineToneQuality.getQuality());

        } else {
            titleView.setTextColor(ResUtil.getColor(R.color.online_setting_tab_text_color_not));
            subTitleView.setTextColor(ResUtil.getColor(R.color.online_setting_tab_text_color_not_50));
            child.setBackground(null);
        }
    }

    @Override
    public void showCustomizeDialog() {

    }

    @Override
    public void showSwitchConfig(SettingConfigBean settingConfigBean) {
        if (settingConfigBean != null && settingConfigBean.switches != null && settingConfigBean.switches.size() > 0) {
            for (SettingConfigBean.SettingConfig aSwitch : settingConfigBean.switches) {
                switch (aSwitch.key) {
                    case OnlineConstants.VOICE_SPEAK://消息语音播报
                        rl_a_tv.setText(aSwitch.title);
                        setting_msg_switch.setChecked(SpUtil.getInt(OnlineConstants.VOICE_SPEAK, 0) > 1);
                        break;
                    case OnlineConstants.VOICE_ASSISTANT://收听小助手
                        rl_b_tv.setText(aSwitch.title);
                        rl_b_tips_tv.setText(aSwitch.desc);
                        setting_msg_helper_switch.setChecked(SpUtil.getInt(OnlineConstants.VOICE_ASSISTANT, 0) > 1);
                        break;
                    case OnlineConstants.TRAVEL_SERVICE://出行服务消息
                        rl_c_tv.setText(aSwitch.title);
                        rl_c_tips_tv.setText(aSwitch.desc);
                        setting_msg_travel_switch.setChecked(SpUtil.getInt(OnlineConstants.TRAVEL_SERVICE, 0) > 1);
                        break;
                }
            }
        }
        initSwitch();
    }

    @Override
    public String getPageId() {
        return Constants.ONLINE_PAGE_ID_MINE_SETTING;
    }

    @Override
    protected void lazyLoad() {

    }
}