<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/car_owner_radio_type_radio"
    android:visibility="gone"
    tools:visibility="visible"
    android:layout_width="@dimen/m572"
    android:layout_height="@dimen/m614"
    android:layout_gravity="center">

    <ImageView
        android:id="@+id/radio_shade"
        android:layout_width="@dimen/car_owner_radio_center_width"
        android:layout_height="@dimen/car_owner_radio_center_width"
        android:layout_marginTop="@dimen/m116"
        android:src="@drawable/car_owner_radio_entrance_shade"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/radio_logo"
        android:layout_width="@dimen/m74"
        android:layout_height="@dimen/m74"
        android:layout_marginTop="@dimen/m40"
        android:src="@drawable/car_owner_radio_entrance_logo"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/radio_shade" />

    <TextView
        android:id="@+id/radio_play_name"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/m36"
        android:layout_marginTop="@dimen/m162"
        tools:text="正在播放：平凡之路"
        android:textColor="#FFFFFFFF"
        android:textSize="@dimen/m24"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/radio_shade" />

    <com.kaolafm.kradio.component.ui.base.view.KradioTextView
        android:id="@+id/radio_name"
        android:layout_width="@dimen/m250"
        android:layout_height="@dimen/m47"
        android:layout_marginTop="@dimen/m206"
        android:gravity="center"
        android:text="- 车主电台名称 -"
        android:textSize="@dimen/m32"
        android:ellipsize="end"
        android:maxLines="1"
        android:textStyle="bold"
        app:kt_end_color="@color/car_owner_radio_name_text_end_color"
        app:kt_gradient_angle="90"
        app:kt_start_color="@color/car_owner_radio_name_text_start_color"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/radio_shade" />


    <ImageView
        android:id="@+id/radio_car"
        android:layout_width="@dimen/m180"
        android:layout_height="@dimen/m180"
        android:layout_marginTop="@dimen/m10"
        android:src="@drawable/car_owner_radio_car_new"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/radio_name" />

    <!--    <ImageView-->
    <!--        android:id="@+id/radio_player"-->
    <!--        app:layout_constraintTop_toTopOf="@id/radio_mask"-->
    <!--        app:layout_constraintLeft_toLeftOf="parent"-->
    <!--        app:layout_constraintRight_toRightOf="parent"-->
    <!--        android:layout_marginTop="@dimen/m134"-->
    <!--        android:layout_width="@dimen/m72"-->
    <!--        android:layout_height="@dimen/m72"-->
    <!--        android:src="@drawable/car_owner_radio_pause"/>-->

</androidx.constraintlayout.widget.ConstraintLayout>