package com.kaolafm.kradio.live.mvp;


import com.kaolafm.kradio.lib.base.mvp.IView;
import com.kaolafm.opensdk.api.live.model.ChatUserInfo;
import com.kaolafm.opensdk.api.live.model.MessageBean;
import com.kaolafm.kradio.live.player.ErrorStatus;
import com.kaolafm.opensdk.api.live.model.LiveInfoDetail;
import java.util.ArrayList;

public interface LiveView extends IView {

    void showListenerNumber(int number);

    void showFileNotExist();

    void showRecordUploadProgress(int progress);

    void showRecordUploadSuccess();

    void showRecordUploadFailure();

    void showRoomMemberEnter(ChatUserInfo member);

    void showChatMessageReceived(ArrayList<MessageBean> messageData);

}
