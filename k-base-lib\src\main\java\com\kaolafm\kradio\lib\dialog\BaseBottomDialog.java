package com.kaolafm.kradio.lib.dialog;

import android.content.Context;
import android.content.res.Resources.NotFoundException;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import com.kaolafm.kradio.lib.R;
import com.kaolafm.kradio.lib.dialog.BaseDialog.Build;

/**
 * 在底部显示的dialog基类
 * <AUTHOR>
 * @date 2018/5/14
 */

public abstract class BaseBottomDialog {

    protected final Context mContext;

    protected BaseDialog mDialog;

    public BaseBottomDialog(Context context) {
        mContext = context;
        mDialog = new Build(context)
                .setWidthScale(1F)
                .setHeightScale(0.14F)
                .setGravity(Gravity.BOTTOM)
                .setDialogView(R.layout.dialog_base_bottom)
                .build();
        View contentView = null;
        try {
            contentView = LayoutInflater.from(context).inflate(getLayoutId(), null);
            initView(contentView);
        } catch (NotFoundException e) {
            contentView = getContentView();
        }
        ((FrameLayout)mDialog.getDialogView()).addView(contentView);
    }

    public abstract int getLayoutId();

    private View getContentView() {
        return null;
    }
    public abstract void initView(View view);

    public void dismiss(){
        if (mDialog != null){
            mDialog.dismiss();
            mDialog = null;
        }
    }

    public void show(){
        if (mDialog != null){
            mDialog.show();
        }
    }
}
