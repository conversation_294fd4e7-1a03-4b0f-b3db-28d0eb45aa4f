package com.kaolafm.kradio.clientControlerForKradio.ex;

import android.os.RemoteException;

import com.kaolafm.sdk.core.IExecuteResult;


/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: Executor.java                                               
 *                                                                  *
 * Created in 2018/8/2 下午5:41                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
public abstract class Executor {
    public abstract void execute(String method, String[] param, IExecuteResult executeResult) throws RemoteException;

    public abstract String execute(String method, String[] param);
}
