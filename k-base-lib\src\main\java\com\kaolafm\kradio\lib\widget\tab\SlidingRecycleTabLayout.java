package com.kaolafm.kradio.lib.widget.tab;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import androidx.annotation.ColorInt;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.kaolafm.kradio.lib.R;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.utils.ResUtil;

import java.util.List;

/**
 * 导航栏, 使用RecycleView实现
 *
 * <AUTHOR>
 * @date 2019-08-20
 */
public class SlidingRecycleTabLayout extends RecyclerView {

    private int mCurrentTab = -1;

    protected int mIndicatorId;

    protected Drawable mIndicatorDrawable;

    private LinearLayoutManager mLinearLayoutManager;

    private OnTabSelectListener mListener;

    /**
     * 文字颜色，未选中的颜色
     */
    private int mTextColor = 0;

    /**
     * 选中的文字颜色。
     */
    private int mTextSelectorColor = 0;

    private float mTextSize;

    private float mTextSelectedSize;

    protected TabAdapter mTabAdapter;

    private int mTabCount;

    public SlidingRecycleTabLayout(Context context) {
        this(context, null);
    }

    public SlidingRecycleTabLayout(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SlidingRecycleTabLayout(Context context, @Nullable AttributeSet attrs,
            int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        obtainAttributes(context, attrs);
        init();
    }

    protected void obtainAttributes(Context context, AttributeSet attrs) {
        if (attrs != null) {
            TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.SlidingRecycleTabLayout);
            mIndicatorId = ta.getResourceId(R.styleable.SlidingRecycleTabLayout_tl_indicator_drawable, 0);
            ta.recycle();
        }
    }

    private void init() {
        mTabAdapter = new TabAdapter();
        mTabAdapter.setOnItemClickListener((view, viewType, tab, position) -> {
            if (mCurrentTab != position) {
                mCurrentTab = position;
                if (mListener != null) {
                    mListener.onTabSelect(mCurrentTab);
                }
                scrollToCurrentTab();
            } else {
                if (mListener != null) {
                    mListener.onTabReselect(position);
                }
            }

        });
        setAdapter(mTabAdapter);
        mLinearLayoutManager = new LinearLayoutManager(getContext(),
                LinearLayoutManager.HORIZONTAL, false);
        setLayoutManager(mLinearLayoutManager);
        addItemDecoration(new TabItemDecoration(ResUtil.getDimen(R.dimen.x40)));

        mTextSize = ResUtil.getDimen(R.dimen.text_size4);
        mTextSelectedSize = ResUtil.getDimen(R.dimen.text_size15);

    }


    /**
     * HorizontalScrollView滚到当前tab,并且居中显示
     */
    private void scrollToCurrentTab() {
        if (mTabAdapter.getItemCount() <= 0) {
            return;
        }
        mTabAdapter.setSelected(mCurrentTab);
        smoothScrollToPosition(mCurrentTab);
    }


    public void setTabs(List<Tab> tabs) {
        mTabAdapter.setDataList(tabs);
        mTabCount = mTabAdapter.getItemCount();
    }

    /**
     * 强制设置当前tab的位置，不管是否和上一个位置相同
     */
    public void setCurrentTabForce(int position) {
        mCurrentTab = position;
        scrollToCurrentTab();
    }

    /**
     * 设置当前tab的位置，如果和当前是同一个位置就不在设置。
     */
    public void setCurrentTab(int position) {
        if (position != mCurrentTab) {
            setCurrentTabForce(position);
        }
    }

    public Tab getCurrentTab() {
        return mTabAdapter.getItemData(mCurrentTab);
    }

    public int getCurrentPosition() {
        return mCurrentTab;
    }

    public void setTextSize(float size) {
        mTextSize = size;
        mTabAdapter.notifyDataSetChanged();
    }

    public void setTextSelectedSize(float size) {
        mTextSelectedSize = size;
    }

    public void setOnTabSelectListener(OnTabSelectListener listener) {
        mListener = listener;
    }

    public void setTextColor(@ColorInt int color) {
        mTextColor = color;
    }

    public int getTabCount() {
        return mTabCount;
    }

    public void release() {
        mLinearLayoutManager.setRecycleChildrenOnDetach(true);
    }

    public Tab getTab(int position) {
        return mTabAdapter.getItemData(position);
    }

    public boolean isLastItem() {
        int lastCompletelyVisibleItemPosition = mLinearLayoutManager.findLastCompletelyVisibleItemPosition();
        return lastCompletelyVisibleItemPosition == mTabAdapter.getItemCount() - 1;
    }
    public void notifyDataSetChanged() {
        if (mTabAdapter != null) {
            mTabAdapter.notifyDataSetChanged();
        }
    }


    class TabAdapter extends BaseAdapter<Tab> {

        private int currentPosition = -1;

        @Override
        protected BaseHolder<Tab> getViewHolder(ViewGroup parent, int viewType) {
            return new TabLayoutViewHolder(inflate(parent, R.layout.item_sliding_tab, viewType));
        }

        public void setSelected(int position) {
            Tab preTab = getItemData(currentPosition);
            if (preTab != null) {
                preTab.select = false;
            }

            Tab currentTab = getItemData(position);
            if (currentTab != null) {
                currentTab.select = true;
            }
            currentPosition = position;
            notifyDataSetChanged();
        }
    }


    class TabLayoutViewHolder extends BaseHolder<Tab> {

        TextView mTvTabTitle;

        public TabLayoutViewHolder(View itemView) {
            super(itemView);
            mTvTabTitle=itemView.findViewById(R.id.tv_tab_title);
        }

        @Override
        public void setupData(Tab tab, int position) {
            mTvTabTitle.setText(tab.title);
            mTvTabTitle.setSelected(tab.select);
            if (tab.select) {
                mTvTabTitle.setTextSize(TypedValue.COMPLEX_UNIT_PX, mTextSelectedSize);
                if (mTextSelectorColor != 0) {
                    mTvTabTitle.setTextColor(mTextSelectorColor);
                }
                mTvTabTitle.setCompoundDrawablesWithIntrinsicBounds(null, null, null, mIndicatorDrawable);
            } else {
                if (mTextColor != 0) {
                    mTvTabTitle.setTextColor(mTextColor);
                }
                mTvTabTitle.setTextSize(TypedValue.COMPLEX_UNIT_PX, mTextSize);
                mTvTabTitle.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null);
            }
        }
    }

}
