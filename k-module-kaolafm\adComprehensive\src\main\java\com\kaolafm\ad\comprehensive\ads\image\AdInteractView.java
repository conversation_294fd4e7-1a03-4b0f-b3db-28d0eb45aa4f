package com.kaolafm.ad.comprehensive.ads.image;

import android.content.Context;
import android.util.Log;
import android.view.Gravity;
import android.view.ViewGroup;

import com.kaolafm.ad.api.model.InteractionAdvert;
import com.kaolafm.ad.comprehensive.ads.image.base.AdImageListener;
import com.kaolafm.ad.comprehensive.ads.image.base.BaseAdImageAdapter;
import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.toast.AdToast;
import com.kaolafm.kradio.lib.toast.SuperToast;
import com.kaolafm.kradio.lib.toast.ToastStyle;
import com.kaolafm.kradio.lib.utils.ResUtil;

public class AdInteractView {
    private static final String TAG = "AdInteractView";

    private BaseAdImageAdapter baseAdImageAdapter;

    private Context mContext;

    private SuperToast mAdToast;

    private InteractionAdvert mAdvert;

    public AdInteractView(Context context){
        mContext = context;
    }

    public void setContext(Context context){
        mContext = context;
    }

    private AdImageListener mAdImageListener = new AdImageListener() {

        @Override
        public void onAdImageLoadFailed() {
            Log.i(TAG,"onAdImageLoadFailed:" + mAdvert);
            mAdToast.dismiss();
        }

        @Override
        public void onAdImageCollapsed() {
            Log.i(TAG,"onAdImageCollapsed:" + mAdvert);
            mAdToast.dismiss();
        }

        @Override
        public void onAdSkip() {
            Log.i(TAG,"onAdSkip:" + mAdvert);
            AdvertisingManager.getInstance().close(mAdvert);
            if (mAdvert != null) {
                AdvertisingManager.getInstance().getReporter().skip(mAdvert);
            }
        }

        @Override
        public void onAdComplete() {
            Log.i(TAG,"onAdComplete:" + mAdvert);
            AdvertisingManager.getInstance().close(mAdvert);
            if (mAdvert != null) {
                AdvertisingManager.getInstance().getReporter().endInteraction(mAdvert);
            }
        }
    };

    public void loadAd(InteractionAdvert advert){
        String className = AdTypeTranslator.getAdImageTypeName(advert);
        try {
            baseAdImageAdapter = AdImageAdapterFactory.create(className);
        } catch (Exception e) {
            Log.e(TAG, "Create BaseAdImageAdapter instance failed.", e);
        }
        baseAdImageAdapter.init(mContext);

        mAdvert = advert;
        hide();
        mAdToast = new AdToast(mContext)
                .setCanTouch(true)
                .setDisplayLevel(ToastStyle.LEVEL_ACTIVITY)
                .setView(baseAdImageAdapter.getBaseAdContentView())
                .setWidth(ViewGroup.LayoutParams.WRAP_CONTENT)
                .setGravity(Gravity.BOTTOM | Gravity.LEFT)
                .setXOffset(ResUtil.getDimen(R.dimen.m46))
                .setYOffset(ResUtil.getDimen(R.dimen.m146))
                .setDuration(-1)
                .setHeight(ViewGroup.LayoutParams.WRAP_CONTENT)
                .setBackground(ResUtil.getColor(R.color.transparent_color))
                .create()
                .show();
        baseAdImageAdapter.loadAd(advert, mAdImageListener);
        Log.i(TAG, "loadAd:" + mAdvert);
    }

    public BaseAdImageAdapter getAdapter(){
        return baseAdImageAdapter;
    }

    public boolean isShow(){
        return mAdToast != null && mAdToast.isShowing();
    }

    public void hide(){
        if(mAdToast != null) {
            Log.i(TAG, "hide:" + mAdvert);
            mAdToast.dismiss();
        }
    }
}
