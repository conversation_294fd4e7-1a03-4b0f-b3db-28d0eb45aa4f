package com.kaolafm.kradio.flavor.impl;


import android.util.Log;

import com.kaolafm.kradio.common.event.LogoutBindEvent;
import com.kaolafm.kradio.lib.base.flavor.UnbindTingban;


import org.greenrobot.eventbus.EventBus;

/**
 * 使用二维码登录前拦截
 *
 * <AUTHOR>
 **/
public class UnbindTingbanImpl implements UnbindTingban {

    @Override
    public void unbindTingban(Object... args) {
        Log.e("novelot", "unbindTingban:解绑听伴.");

        UserInfoManager userInfoManager = UserInfoManager.getInstance();
        userInfoManager.setUserFavicon("");
        userInfoManager.setUserNickName("");
        userInfoManager.localLogout();
        EventBus.getDefault().post(new LogoutBindEvent(LogoutBindEvent.LOGOUT));

        if (args != null && args.length > 0) {
            ((AccountLoginModel.AccountCallBack) args[0]).onUnbindSuccess(true);
        }
    }
}