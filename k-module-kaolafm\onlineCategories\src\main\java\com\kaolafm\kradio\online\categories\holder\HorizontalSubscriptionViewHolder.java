package com.kaolafm.kradio.online.categories.holder;

import android.text.TextUtils;
import android.util.Log;
import android.util.TypedValue;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.kradio.common.SubcategoryItemBean;
import com.kaolafm.kradio.common.widget.KradioTextView;
import com.kaolafm.kradio.k_kaolafm.R; 
import com.kaolafm.kradio.lib.base.flavor.KRadioPicSettingInter;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.online.common.utils.OnlineVipCornerUtil;
import com.kaolafm.kradio.online.categories.HolderUtils;
 

/**
 * <AUTHOR>
 **/
public class HorizontalSubscriptionViewHolder extends BaseSubcategoryViewHolder {

    private static final int WAN = 10000;
    private static final String DONGFENGRICHAN = "KRadioPicSettingImpl";
 
    ImageView ivCover;
 
    KradioTextView tvName;
 
    TextView tvNumber;
 
    View layout_offline;
 
    View viewItemSubscriptionMongolian;
 
    ImageView vipIcon; 
    View llFreq; 
    TextView tvFreq;

    private KRadioPicSettingInter mPicSetting;


    public HorizontalSubscriptionViewHolder(View itemView, KRadioPicSettingInter picSetting) {
        super(itemView);
        mPicSetting = picSetting;

        ivCover=view.findViewById(R.id.iv_cover);
        tvName=view.findViewById(R.id.tv_name);
        tvNumber=view.findViewById(R.id.tv_number);
        layout_offline=view.findViewById(R.id.layout_offline);
        viewItemSubscriptionMongolian=view.findViewById(R.id.view_item_subscription_mongolian);
        vipIcon=view.findViewById(R.id.vip_icon);
        llFreq=view.findViewById(R.id.llFreq);
        tvFreq=view.findViewById(R.id.tvFreq);
       
    }

    @Override
    public void setupData(SubcategoryItemBean subcategoryItemBean, int position) {
        super.setupData(subcategoryItemBean, position);

        boolean hasFreq = false;
        if (subcategoryItemBean.getItemType() == SubcategoryItemBean.TYPE_ITEM_BROADCAST_LOCAL
                || subcategoryItemBean.getItemType() == SubcategoryItemBean.TYPE_ITEM_BROADCAST_CATEGORY
                || subcategoryItemBean.getItemType() == SubcategoryItemBean.TYPE_ITEM_TV) {
            String freq = subcategoryItemBean.getFreq();
            if (StringUtil.isEmpty(freq) && subcategoryItemBean.getName().contains("FM")) {
                freq = subcategoryItemBean.getName();
            }
            if (!StringUtil.isEmpty(freq)) {
                if (freq.startsWith("FM")) {
                    freq = freq.replace("FM", "");
                }
                if (freq.contains("/")) {
                    freq = freq.substring(0, freq.indexOf("/"));
                }

                tvFreq.setText(StringUtil.getMaxString(freq, 9));
                hasFreq = true;
            }
            llFreq.setVisibility(hasFreq ? View.VISIBLE : View.INVISIBLE);
        } else {
            llFreq.setVisibility(hasFreq ? View.VISIBLE : View.GONE);
        }


        String picUrl;
        picUrl = HolderUtils.getUrlString(subcategoryItemBean, mPicSetting);

        ImageLoader.getInstance().displayCircleImage(mContext, picUrl, ivCover);

        String name = "";
        if (subcategoryItemBean.getItemType() == SubcategoryItemBean.TYPE_ITEM_BROADCAST_LOCAL
                || subcategoryItemBean.getItemType() == SubcategoryItemBean.TYPE_ITEM_TV) {
            if (TextUtils.isEmpty(subcategoryItemBean.getTitle())) {
                name = subcategoryItemBean.getName();
            } else {
                name = subcategoryItemBean.getTitle();
            }
        } else {
            name = subcategoryItemBean.getName();
        }
        tvName.setGradientColor(ResUtil.getColor(R.color.online_category_item_title_text_start),
                ResUtil.getColor(R.color.online_category_item_title_text_end),
                270);
        tvName.setFontWeight(0.7f);
        tvName.setText(StringUtil.getMaxString(name, 5));
        if (subcategoryItemBean.getItemType() == SubcategoryItemBean.TYPE_ITEM_BROADCAST_CATEGORY
                || subcategoryItemBean.getItemType() == SubcategoryItemBean.TYPE_ITEM_BROADCAST_LOCAL
                || subcategoryItemBean.getItemType() == SubcategoryItemBean.TYPE_ITEM_TV) {
            vipIcon.setImageResource(R.drawable.icon_live_class);
        } else {
            vipIcon.setImageResource(0);
            OnlineVipCornerUtil.setVipCornerCategories(vipIcon, subcategoryItemBean.getVip(), subcategoryItemBean.getFine(), false);
            Log.d("cai66", "--------vip" + subcategoryItemBean.getVip() + "--精品--" + subcategoryItemBean.getFine());
//            if (subcategoryItemBean.isSelected()) {
//                tvName.setTextSize(TypedValue.COMPLEX_UNIT_PX, ResUtil.getDimen(R.dimen.m24));//AI电台，全部
//            } else {
//                tvName.setTextSize(TypedValue.COMPLEX_UNIT_PX, ResUtil.getDimen(R.dimen.m24));//AI电台，全部
//            }
        }


        //QQ
//        long listenNum = subcategoryItemBean.getListenNum();
//        if (listenNum > 0) {
//            String strListenNum = toStr(listenNum);
//            tvNumber.setText(strListenNum);
//            ViewUtil.setViewVisibility(tvNumber, View.VISIBLE);
//        } else {
//            //听伴:听伴没有listenNum字段
//            String updateTime = subcategoryItemBean.getUpdateTime();
//            if (!TextUtils.isEmpty(updateTime)) {
//                tvNumber.setText(DateUtil.getDisTimeStr(Long.valueOf(updateTime)));
//                ViewUtil.setViewVisibility(tvNumber, View.VISIBLE);
//            } else {
//                ViewUtil.setViewVisibility(tvNumber, View.INVISIBLE);
//            }
//        }

        //是否下线
        boolean online = subcategoryItemBean.isOnline();
        layout_offline.setVisibility(online ? View.GONE : View.VISIBLE);

//        tvName.setTextSize(TypedValue.COMPLEX_UNIT_PX, ResUtil.getDimen(R.dimen.subcategory_item_radio_text_size));
        //todo flavor代码迁移（yikatongkx11）
//        tvName.setTextSize(TypedValue.COMPLEX_UNIT_PX, ResUtil.getDimen(R.dimen.m24));//AI电台，全部
        tvName.setSelected(subcategoryItemBean.isSelected());
        tvName.setTextColor(mContext.getResources().getColor(R.color.online_category_item_radio_title_text_color));

//        viewItemSubscriptionMongolian.setVisibility(View.VISIBLE);
//        ViewUtil.setViewVisibilityAccordingToSetting(viewItemSubscriptionMongolian);

    }

    /**
     * 将收听量,转为文字
     *
     * @param listenNum
     * @return
     */
    private static String toStr(long listenNum) {
        StringBuilder str = new StringBuilder();

        long l = listenNum / (WAN * WAN);
        if (l > 0) {
            //上亿
            str.append(l).append("亿");
            listenNum = listenNum - l * WAN * WAN;
            l = listenNum / WAN;
            if (l > 0) {
                str.append(l).append("万");
            }
        } else {
            //上十万
            l = listenNum / WAN;
            if (l >= 10) {
                str.append(l).append("万");
            } else {
                str.append(listenNum);
            }
        }


        return str.toString();
    }
}
