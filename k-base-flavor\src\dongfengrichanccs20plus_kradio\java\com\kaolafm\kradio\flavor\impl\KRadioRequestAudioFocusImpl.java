package com.kaolafm.kradio.flavor.impl;

import android.annotation.SuppressLint;
import android.content.Context;
import android.media.AudioManager;
import android.util.Log;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioRequestAudioFocusInter;
import com.kaolafm.opensdk.player.logic.PlayerManager;


/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-12-11 20:58
 ******************************************/
public class KRadioRequestAudioFocusImpl implements KRadioRequestAudioFocusInter {
    private static final String TAG = "KRadioRequestAudioFocusImpl";

    @SuppressLint("LongLogTag")
    @Override
    public boolean requestAudioFocusBySelf(Object... args) {
        boolean flag = PlayerManager.getInstance().isPlayerInitSuccess();
        if (flag) {
            PlayerManager.getInstance().requestAudioFocus();
        } else {
            AudioManager audioManager = (AudioManager) AppDelegate.getInstance().getContext().getSystemService(Context.AUDIO_SERVICE);
            int result = audioManager.requestAudioFocus(mOnAudioFocusChangeListener, AudioManager.STREAM_MUSIC,
                    AudioManager.AUDIOFOCUS_GAIN);
            Log.i(TAG, "requestAudioFocusBySelf result = " + result);
            if (result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED) {
                PlayerManager.getInstance().notifyAudioFocus(AudioManager.AUDIOFOCUS_GAIN);
            }
        }
        return true;
    }

    private AudioManager.OnAudioFocusChangeListener mOnAudioFocusChangeListener = new AudioManager.OnAudioFocusChangeListener() {
        @SuppressLint("LongLogTag")
        @Override
        public void onAudioFocusChange(int focusChange) {
            Log.i(TAG, "onAudioFocusChange----focusChange = " + focusChange);
        }
    };
}