package com.kaolafm.kradio.flavor.impl;

import android.annotation.TargetApi;
import android.content.Context;
import android.media.AudioAttributes;
import android.media.AudioFocusRequest;
import android.media.AudioManager;
import android.provider.Settings;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioRecorderInter;
import com.kaolafm.kradio.lib.base.flavor.live.KradioRecorderInterface;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;

import static android.media.AudioManager.AUDIOFOCUS_GAIN_TRANSIENT;
import static android.media.AudioManager.AUDIOFOCUS_REQUEST_GRANTED;

public class KRadioAudioRecorderImpl implements KRadioAudioRecorderInter {

    private int mAudioSource_Mic = 0;
    private AudioManager mAudioManager = null;
    private AudioFocusRequest afr = null;

    public KRadioAudioRecorderImpl() {
        mAudioSource_Mic = Settings.Global.getInt(AppDelegate.getInstance().
                getContext().getContentResolver(), "ivi.media.streamtype.defmic", 0);
    }

    @Override
    public boolean initVR(Object... args) {
        return false;
    }

    @TargetApi(26)
    @Override
    public boolean onAudioRecordStart(Object... args) {
        AudioAttributes aa = new AudioAttributes.Builder().setLegacyStreamType(mAudioSource_Mic).build();
        int durationHint = AUDIOFOCUS_GAIN_TRANSIENT;
        afr = new AudioFocusRequest.Builder(durationHint)
                .setAudioAttributes(aa)
                .setAcceptsDelayedFocusGain(false)
                .build();
        if (mAudioManager == null) {
            mAudioManager = (AudioManager) AppDelegate.getInstance().getContext().getSystemService(Context.AUDIO_SERVICE);
        }
        int nRet = mAudioManager.requestAudioFocus(afr);
        boolean status = nRet == AUDIOFOCUS_REQUEST_GRANTED;
        if (status){
            PlayerManagerHelper.getInstance().pause(false);
        }
        return status;
    }

    @TargetApi(26)
    @Override
    public boolean onAudioRecordStop(Object... args) {
        if (mAudioManager != null && afr != null) {
            mAudioManager.abandonAudioFocusRequest(afr);
            afr = null;
            return true;
        }
        return false;
    }

    @Override
    public KradioRecorderInterface getRecorder() {
        return null;
    }

    @Override
    public void setVrStatusListener(OnVRStatusListener onVRStatusListener) {

    }
}
