package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Popup;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;

import com.cns.android.location.POIManager;
import com.kaolafm.kradio.flavor.BuildConfig;
import com.kaolafm.kradio.flavor.ExitActivity;
import com.kaolafm.kradio.flavor.OpenPrivacyActivity;
import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.base.flavor.KRadioHubInitInter;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;

/**
 * author : wxb
 * date   : 2021/10/28
 * desc   :判断是否有su root问题
 */
public class KRadioHubInitImpl implements KRadioHubInitInter {
    @Override
    public boolean hasPrivacyMode(Object... args) {
        Log.d("KRadioHubInitImpl", "hasPrivacyMode in");
        if (BuildConfig.DEBUG) {
            return false;
        }
        Activity activity = (Activity) args[0];
//        boolean iss = isCanExecute("/system/bin/su");
//        boolean is=(checkRootFile()!=null);
        boolean isa = checkSuFile();
//        isCanExecute-false-checkRoot-/sbin/su-checkSuFile-true
//        Log.d("KRadioHubInitImpl", "isCanExecute-" + iss + "-checkRoot-" + checkRootFile() + "-checkSuFile-" + isa);
        Log.d("KRadioHubInitImpl", "-checkRoot-" + checkRootFile() + "-checkSuFile-" + isa);

        if (isa) {
            //点击取消后退出应用
            Intent intent = new Intent(activity, ExitActivity.class);
            activity.startActivity(intent);
        }
        return isa;
    }

    //检查su文件是否有x或者s权限
//     filePath su 文件的路径，比如/system/bin/su
    private static boolean isCanExecute(String filePath) {
        java.lang.Process process = null;
        try {
            process = Runtime.getRuntime().exec("ls -l " + filePath);
            BufferedReader in = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String str = in.readLine();
            if (str != null && str.length() >= 4) {
                char flag = str.charAt(3);
                if (flag == 's' || flag == 'x') {
                    Runtime.getRuntime().exec("su ");
                    return true;
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (process != null) {
                process.destroy();
            }
        }
        return false;
    }

    private static boolean checkSuFile() {
        Process process = null;
        try {
            //   /system/xbin/which 或者  /system/bin/which
            process = Runtime.getRuntime().exec(new String[]{"which", "su"});
            BufferedReader in = new BufferedReader(new InputStreamReader(process.getInputStream()));
            if (in.readLine() != null) return true;
            return false;
        } catch (Throwable t) {
            return false;
        } finally {
            if (process != null) process.destroy();
        }
    }

    private static File checkRootFile() {
        File file = null;
        String[] paths = {"/sbin/su", "/system/bin/su", "/system/xbin/su", "/data/local/xbin/su", "/data/local/bin/su", "/system/sd/xbin/su",
                "/system/bin/failsafe/su", "/data/local/su"};
        for (String path : paths) {
            file = new File(path);
            if (file.exists()) return file;
        }
        return file;
    }

}
