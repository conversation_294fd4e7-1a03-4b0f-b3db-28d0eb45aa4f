<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/activity_root_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_home"
    android:paddingTop="@dimen/home_page_root_view_padding_top">

    <androidx.constraintlayout.widget.Group
        android:id="@+id/first_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:constraint_referenced_ids="rv_sub" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/second_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="qrCode_image_3,qrCode_textView_3,tv_more_title" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/activity_top_guideline"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.11" />

    <ImageView
        android:id="@+id/back_view"
        style="@style/FragmentBackButton"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="MissingConstraints" />

    <TextView
        android:id="@+id/tv_activity_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/y15"
        android:paddingTop="@dimen/y15"
        android:text="@string/activity_name"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/m32"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/back_view"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/back_view" />

    <View
        android:id="@+id/deliver"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="@dimen/y29"
        android:background="@color/activity_line_bg"
        app:layout_constraintTop_toBottomOf="@+id/tv_activity_title"
        tools:ignore="MissingConstraints" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_sub"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/m22"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/activity_top_guideline"
        app:layout_constraintWidth_percent="0.8" />

    <TextView
        android:id="@+id/tv_more_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:text="@string/activity_more_title"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/activity_top_guideline"
        app:layout_constraintVertical_bias="0.243" />

    <ImageView
        android:id="@+id/qrCode_image_3"
        android:layout_width="@dimen/m160"
        android:layout_height="@dimen/m160"
        android:layout_marginTop="@dimen/y30"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_more_title"
        tools:ignore="MissingConstraints" />

    <TextView
        android:id="@+id/qrCode_textView_3"
        android:layout_width="@dimen/x200"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y10"
        android:maxLines="2"
        app:layout_constraintLeft_toLeftOf="@+id/qrCode_image_3"
        app:layout_constraintRight_toRightOf="@+id/qrCode_image_3"
        app:layout_constraintTop_toBottomOf="@+id/qrCode_image_3"
        tools:ignore="MissingConstraints" />

    <ViewStub
        android:id="@+id/vs_layout_error_page"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout="@layout/home_no_network_rl" />

    <TextView
        android:id="@+id/tv_no_activity"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/user_purchased_no_sub_text_color"
        android:textSize="@dimen/text_size5"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        android:id="@+id/activity_loading"
        layout="@layout/refresh_bottom"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
