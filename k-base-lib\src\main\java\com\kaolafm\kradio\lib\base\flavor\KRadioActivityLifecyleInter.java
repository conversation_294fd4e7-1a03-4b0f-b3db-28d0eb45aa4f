package com.kaolafm.kradio.lib.base.flavor;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-04-02 10:39
 ******************************************/
public interface KRadioActivityLifecyleInter {
    boolean onRestart(Object... args);

    boolean onResume(Object... args);

    boolean onPause(Object... args);

    boolean onStop(Object... args);
}
