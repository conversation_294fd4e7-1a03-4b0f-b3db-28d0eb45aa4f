package com.kaolafm.kradio.component.ui.base.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.Shader;
import android.text.TextPaint;
import android.util.AttributeSet;

import com.kaolafm.kradio.component.ui.R;

import skin.support.widget.SkinCompatTextView;


public class KradioTextView extends SkinCompatTextView {
    private LinearGradient mLinearGradient;
    private int mGradientAngle = 0;// （0-从左到右渐变；90-从下到上渐变；180-从右到左渐变；270-从上到下渐变)
    private Paint mPaint;
    private int mFromColor = -1;
    private int mToColor = -1;
    private float mStrokeWidth = 0.0f;//字体粗细:默认0.0f=normal; 0.7f=medium

    public KradioTextView(Context context) {
        this(context, null);
    }

    public KradioTextView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public KradioTextView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.KradioTextView);

        if (a.hasValue(R.styleable.KradioTextView_kt_font_weight)) {
            mStrokeWidth = a.getFloat(R.styleable.KradioTextView_kt_font_weight, mStrokeWidth);
        }

        if (a.hasValue(R.styleable.KradioTextView_kt_start_color)) {
            mFromColor = a.getColor(R.styleable.KradioTextView_kt_start_color, Color.WHITE);
        }
        if (a.hasValue(R.styleable.KradioTextView_kt_end_color)) {
            mToColor = a.getColor(R.styleable.KradioTextView_kt_end_color, Color.WHITE);
        }
        if (a.hasValue(R.styleable.KradioTextView_kt_gradient_angle)) {
            mGradientAngle = a.getInt(R.styleable.KradioTextView_kt_gradient_angle, -1);
        }
        a.recycle();
    }

    public void setFontWeight(float fontWeight) {
        mStrokeWidth = fontWeight;
        invalidate();
    }

    public void setGradientColor(int fromColor, int toColor, int gradientAngle) {
        mGradientAngle = gradientAngle;
        mPaint = getPaint();
        if (mPaint == null) return;
        if (fromColor == -1 || toColor == -1) {
            mPaint.setShader(null);
            mFromColor= fromColor;
            mToColor = toColor;
            mGradientAngle = gradientAngle;
            invalidate();
            return;
        }
        if (mGradientAngle == 0 || mGradientAngle == 90 || mGradientAngle == 180 || mGradientAngle == 270) {
            mFromColor = (mGradientAngle == 0 || mGradientAngle == 270) ? fromColor : toColor;
            mToColor = (mGradientAngle == 0 || mGradientAngle == 270) ? toColor : fromColor;
            int X = (mGradientAngle == 0 || mGradientAngle == 180) ? getMeasuredWidth() : 0;
            int Y = (mGradientAngle == 90 || mGradientAngle == 270) ? getMeasuredHeight() : 0;
            mLinearGradient = new LinearGradient(0, 0, X, Y,
                    new int[]{mFromColor, mToColor},
                    null, Shader.TileMode.CLAMP);
            mPaint.setShader(mLinearGradient);
            invalidate();
        }
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        changeGradientColor(w, h, oldw, oldh);
        super.onSizeChanged(w, h, oldw, oldh);
    }

    private void changeGradientColor(int w, int h, int oldw, int oldh) {
        mPaint = getPaint();
        if (mPaint == null) return;
        if (mFromColor == -1 || mToColor == -1) {
            mPaint.setShader(null);
            invalidate();
            return;
        }
        if (mGradientAngle == 0 || mGradientAngle == 90 || mGradientAngle == 180 || mGradientAngle == 270) {
            int tempFromColor = mFromColor;
            int tempToColor = mToColor;
            mFromColor = (mGradientAngle == 0 || mGradientAngle == 270) ? tempFromColor : tempToColor;
            mToColor = (mGradientAngle == 0 || mGradientAngle == 270) ? tempToColor : tempFromColor;
            if (w != oldw || h != oldh) {
                int X = (mGradientAngle == 0 || mGradientAngle == 180) ? w : 0;
                int Y = (mGradientAngle == 90 || mGradientAngle == 270) ? h : 0;
                mLinearGradient = new LinearGradient(0, 0, X, Y,
                        new int[]{mFromColor, mToColor},
                        null, Shader.TileMode.CLAMP);
                mPaint.setShader(mLinearGradient);
            }
        }
    }

    @Override
    protected void onDraw(Canvas canvas) {
        //获取当前控件的画笔
        TextPaint paint = getPaint();
        //设置画笔的描边宽度值
        paint.setStrokeWidth(mStrokeWidth);
        paint.setStyle(Paint.Style.FILL_AND_STROKE);
        super.onDraw(canvas);
    }

    public int getGradientAngle() {
        return mGradientAngle;
    }

}