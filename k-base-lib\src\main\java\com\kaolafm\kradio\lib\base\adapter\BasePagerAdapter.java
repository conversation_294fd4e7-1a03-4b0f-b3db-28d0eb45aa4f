package com.kaolafm.kradio.lib.base.adapter;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import java.util.ArrayList;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-04-13 12:17
 ******************************************/

public abstract class BasePagerAdapter<T> extends PagerAdapter {
    private static final String TAG = "BasePagerAdapter";

    private ArrayList<T> mData;

    private LayoutInflater mLayoutInflater;

    protected ViewPager mViewPager;

//    protected SparseArray<WeakReference<View>> mViewArray;

    public BasePagerAdapter(Context context) {
        mLayoutInflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
//        mViewArray = new SparseArray<>();
    }

    protected abstract int getContentLayoutId();

    protected abstract void initViewItem(View parentView, T t, int position);

    public void setViewPager(ViewPager viewPager) {
        mViewPager = viewPager;
    }

//    public SparseArray<WeakReference<View>> getViewArray() {
//        return mViewArray;
//    }

    public ArrayList<T> getData() {
        return mData;
    }

    @Override
    public int getCount() {
        if (mData == null || mData.size() == 0) {
            return 0;
        }
        return mData.size();
    }

//    @Override
//    public int getItemPosition(final Object object) {
//        return POSITION_NONE;
//    }

    @NonNull
    @Override
    public Object instantiateItem(@NonNull ViewGroup container, int position) {
//        WeakReference<View> weakReference = mViewArray.get(position);
//        if (weakReference != null) {
//            View view = weakReference.get();
//            if (view != null) {
//                return view;
//            }
//        }
//        Log.i(TAG, "instantiateItem--------->" + position);
        View view = mLayoutInflater.inflate(getContentLayoutId(), container, false);
//        mViewArray.put(position, new WeakReference<>(view));
        initViewItem(view, mData.get(position), position);
        container.addView(view);
        return view;
    }

    @Override
    public boolean isViewFromObject(@NonNull View view, @NonNull Object object) {
        boolean isEqual = view == object;
        return isEqual;
    }

    @Override
    public void destroyItem(final ViewGroup container, final int position, final Object object) {
        container.removeView((View) object);
//        mViewArray.removeAt(position);
//        Log.i(TAG, "destroyItem--------->" + position);
    }

    public void updateData(ArrayList<T> data) {
        if (data == null || data.isEmpty()) {
            return;
        }
        if (mData != null && !mData.isEmpty()) {
            mData.clear();
        }
        appendData(data);
        notifyDataSetChanged();
    }

    public void addData(ArrayList<T> data) {
        if (data == null || data.isEmpty()) {
            return;
        }
//        int position = 0;
//        if (mData != null && mData.size() != 0) {
//            position = mData.size() - 1;
//        }
        appendData(data);
        notifyDataSetChanged();
    }

    public void addData(T data) {
        if (data == null) {
            return;
        }
        appendData(data);
        notifyDataSetChanged();
    }

    public void addData(T data, int index) {
        if (data == null) {
            return;
        }
        appendDataAtIndex(data, index);
        notifyDataSetChanged();
    }

    public void updateData(T t) {
        if (t == null) {
            return;
        }
        if (mData != null && !mData.isEmpty()) { // 去重操作
            boolean isContains = mData.contains(t);
            if (isContains) {
                return;
            }
        }
        appendData(t);
        notifyDataSetChanged();
    }

    public void clearData() {
        if (mData != null && !mData.isEmpty()) {
            mData.clear();
        }
        notifyDataSetChanged();
    }

    /**
     * 替换特定位置数据
     *
     * @param t
     * @param index
     */
    public void replacePositionData(T t, int index) {
        if (mData == null || mData.isEmpty()) {
            return;
        }
        if (mData.size() <= index) {
            return;
        }
        T tempData = mData.get(index);
        mData.remove(tempData);
        mData.add(index, t);
        notifyDataSetChanged();
    }


    private void appendData(ArrayList<T> data) {
        int size = data.size();
        if (mData == null) {
            mData = new ArrayList<>(size);
        }
        mData.addAll(data);
    }

    private void appendDataAtIndex(T t, int index) {
        if (mData == null) {
            mData = new ArrayList<>(1);
        }
        mData.add(index, t);
    }

    private void appendData(T t) {
        if (mData == null) {
            mData = new ArrayList<>(1);
        }
        mData.add(t);
    }
}
