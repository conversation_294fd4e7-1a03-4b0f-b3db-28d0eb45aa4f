package com.kaolafm.kradio.flavor.impl;
import com.kaolafm.kradio.flavor.BuildConfig;


/**
 * author : wxb
 * date   : 2021/12/21
 * desc   :
 */
public class DebugImpl {
    public static boolean isDebug() {
        return BuildConfig.DEBUG;
    }
    //是否开启代理
    public static boolean hasPoxy() {
        return !BuildConfig.DEBUG;
    }
    //强制有网
    public static boolean hasNet() {
        return false;
    }
    //隐私开关控制
    public static boolean hasPrivacyMode() {
        return true;
    }
}
