package com.kaolafm.kradio.component;

import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 用于标记可以被其他组件模块使用的常量。被该常量标记的类，插件会将该类中的常量自动生成到Module constant中
 * 该类是在库中使用，用于编译通过，实际使用lib中的注解
 * <AUTHOR>
 * @date 2019-07-18
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD, ElementType.TYPE})
@Inherited
public @interface SharedConst {

}
