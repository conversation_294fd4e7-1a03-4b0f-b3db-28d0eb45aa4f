
package com.kaolafm.kradio.coin;

import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProviders;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;
import androidx.constraintlayout.widget.Group;
import androidx.constraintlayout.widget.Guideline;
import androidx.fragment.app.Fragment;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.ImageView;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.mvp.IPresenter;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.view.ScrollNumberView;
import java.util.HashMap;
import kotlin.TypeCastException;
import kotlin.jvm.internal.Intrinsics;

public final class CoinFragment extends BaseFragment {
    private CoinViewModel mViewModel;

    private ConstraintSet constraintSetLand = new ConstraintSet();

    private ConstraintSet constraintSetPortrait = new ConstraintSet();

    public static final String TAG = "coin";

    private HashMap _$_findViewCache;


    public final ConstraintSet getConstraintSetLand() {
        return this.constraintSetLand;
    }

    public final void setConstraintSetLand(ConstraintSet var1) {
        this.constraintSetLand = var1;
    }


    public final ConstraintSet getConstraintSetPortrait() {
        return this.constraintSetPortrait;
    }

    public final void setConstraintSetPortrait(ConstraintSet var1) {
        //Intrinsics.checkParameterIsNotNull(var1, "<set-?>");
        this.constraintSetPortrait = var1;
    }

    public void initView(@Nullable View view) {
        ConstraintSet var10000 = this.constraintSetLand;

        View var10001 = view.findViewById(R.id.coin_main_layout);
        if (var10001 == null) {
            throw new TypeCastException("null cannot be cast to non-null type android.support.constraint.ConstraintLayout");
        } else {
            var10000.clone((ConstraintLayout) var10001);
        }
    }

    protected int getLayoutId() {
        return R.layout.fragment_coin;
    }


//    protected CoinP createPresenter() {
//        return new CoinP();
//    }
//
//    // $FF: synthetic method
//    // $FF: bridge method
//    public IPresenter createPresenter() {
//        return new CoinP();
//    }

    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ViewModel var10001 = ViewModelProviders.of((Fragment) this).get(CoinViewModel.class);
        //Intrinsics.checkExpressionValueIsNotNull(var10001, "ViewModelProviders.of(th…oinViewModel::class.java)");
        this.mViewModel = (CoinViewModel) var10001;
        this.constraintSetPortrait.clone(this.getContext(), R.layout.fragment_coin_portrait);
    }

    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        //Intrinsics.checkParameterIsNotNull(view, "view");
        super.onViewCreated(view, savedInstanceState);
        ImageView var3 = (ImageView) this._$_findCachedViewById(R.id.qrCode);

        var3.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {

            }
        });
        CoinViewModel var10000 = this.mViewModel;
        if (var10000 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("mViewModel");
        }

        var10000.getQrCode().observe((LifecycleOwner) this, (Observer) (new Observer() {
            // $FF: synthetic method
            // $FF: bridge method
            public void onChanged(Object var1) {
                this.onChanged((String) var1);
            }

            public final void onChanged(@Nullable String it) {
                ImageLoader.getInstance().displayImage((Fragment) CoinFragment.this, it, (ImageView) CoinFragment.this._$_findCachedViewById(R.id.qrCode));
            }
        }));
        var10000 = this.mViewModel;
        if (var10000 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("mViewModel");
        }

        var10000.getCoinCount().observe((LifecycleOwner) this, (Observer) (new Observer() {
            // $FF: synthetic method
            // $FF: bridge method
            public void onChanged(Object var1) {
                this.onChanged((Integer) var1);
            }

            public final void onChanged(@Nullable Integer it) {
                Logger.d("coin", "coinCount=" + it);
                ScrollNumberView var10000 = (ScrollNumberView) CoinFragment.this._$_findCachedViewById(R.id.scrollNumberView);
                if (it == null) {
                    Intrinsics.throwNpe();
                }

                var10000.update(it);
            }
        }));
        var10000 = this.mViewModel;
        if (var10000 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("mViewModel");
        }

        var10000.isLogined().observe((LifecycleOwner) this, (Observer) (new Observer() {
            // $FF: synthetic method
            // $FF: bridge method
            public void onChanged(Object var1) {
                this.onChanged((Boolean) var1);
            }

            public final void onChanged(@Nullable Boolean it) {
                Logger.d("coin", "登录状态:" + it);
                CoinFragment var10000 = CoinFragment.this;
                if (it == null) {
                    Intrinsics.throwNpe();
                }

                var10000.changeLoginView(it);
                if (it) {
                    CoinFragment.access$getMViewModel$p(CoinFragment.this).queryCoinCount();
                }

            }
        }));
    }

    protected void showAccordingToScreen(int orientation) {
        super.showAccordingToScreen(orientation);
        if (orientation == 1) {
            this.constraintSetPortrait.applyTo((ConstraintLayout) this._$_findCachedViewById(R.id.coin_main_layout));
        } else {
            this.constraintSetLand.applyTo((ConstraintLayout) this._$_findCachedViewById(R.id.coin_main_layout));
        }

        CoinViewModel var10001 = this.mViewModel;
        if (var10001 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("mViewModel");
        }

        Object var2 = var10001.isLogined().getValue();
        if (var2 == null) {
            throw new TypeCastException("null cannot be cast to non-null type kotlin.Boolean");
        } else {
            this.changeLoginView((Boolean) var2);
        }
    }

    public final void changeLoginView(boolean login) {
        Group var10000;
        if (login) {
            var10000 = (Group) this._$_findCachedViewById(R.id.groupCoin);
            //Intrinsics.checkExpressionValueIsNotNull(var10000, "groupCoin");
            var10000.setVisibility(View.VISIBLE);
            ((Guideline) this._$_findCachedViewById(R.id.guidelineV)).setGuidelinePercent(0.5F);
        } else {
            var10000 = (Group) this._$_findCachedViewById(R.id.groupCoin);
            //Intrinsics.checkExpressionValueIsNotNull(var10000, "groupCoin");
            var10000.setVisibility(View.GONE);
            if (ResUtil.getOrientation() == 1) {
                ((Guideline) this._$_findCachedViewById(R.id.guidelineV)).setGuidelinePercent(0.15F);
            } else {
                ((Guideline) this._$_findCachedViewById(R.id.guidelineV)).setGuidelinePercent(1.0F);
            }

            ((ScrollNumberView) this._$_findCachedViewById(R.id.scrollNumberView)).reset();
        }

    }

    protected void addFragmentRootViewPadding(View view) {
        //Intrinsics.checkParameterIsNotNull(view, "view");
    }

    protected void changeViewLayoutForStatusBar(View view) {
        //Intrinsics.checkParameterIsNotNull(view, "view");
    }

    protected int getLayoutId_Tow() {
        String var1 = "Not yet implemented";
        return 0;
    }

    @Override
    protected IPresenter createPresenter() {
        return null;
    }

    // $FF: synthetic method
    public static final CoinViewModel access$getMViewModel$p(CoinFragment $this) {
        CoinViewModel var10000 = $this.mViewModel;
        if (var10000 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("mViewModel");
        }

        return var10000;
    }

    // $FF: synthetic method
    public static final void access$setMViewModel$p(CoinFragment $this, CoinViewModel var1) {
        $this.mViewModel = var1;
    }

    public View _$_findCachedViewById(int var1) {
        if (this._$_findViewCache == null) {
            this._$_findViewCache = new HashMap();
        }

        View var2 = (View) this._$_findViewCache.get(var1);
        if (var2 == null) {
            View var10000 = this.getView();
            if (var10000 == null) {
                return null;
            }

            var2 = var10000.findViewById(var1);
            this._$_findViewCache.put(var1, var2);
        }

        return var2;
    }

    public void _$_clearFindViewByIdCache() {
        if (this._$_findViewCache != null) {
            this._$_findViewCache.clear();
        }

    }

    // $FF: synthetic method
    public void onDestroyView() {
        super.onDestroyView();
        this._$_clearFindViewByIdCache();
    }


  class CoinP implements IPresenter{

      @Override
      public void start() {

      }

      @Override
      public void destroy() {

      }
  }

}
