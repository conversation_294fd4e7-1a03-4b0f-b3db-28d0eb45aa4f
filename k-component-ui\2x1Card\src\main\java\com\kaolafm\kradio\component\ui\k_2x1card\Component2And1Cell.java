package com.kaolafm.kradio.component.ui.k_2x1card;

import static android.text.TextUtils.TruncateAt.END;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.graphics.Rect;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import java.lang.ref.WeakReference;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.PagerSnapHelper;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;

import com.kaolafm.kradio.component.ui.R;
import com.kaolafm.kradio.component.ui.base.CellBinder;
import com.kaolafm.kradio.component.ui.base.ItemClickSupport;
import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.component.ui.base.utils.ComponentUtils;
import com.kaolafm.kradio.component.ui.base.utils.VipCornerUtil;
import com.kaolafm.kradio.component.ui.base.view.OvalImageView;
import com.kaolafm.kradio.component.ui.base.view.RateView;
import com.kaolafm.kradio.lib.base.flavor.Card2x1UtilInter;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.UrlUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.operation.model.column.ActivityDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.AlbumDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.ColumnContent;
import com.kaolafm.opensdk.api.operation.model.column.PageRedirectionColumnMember;

import java.util.ArrayList;
import java.util.List;

/**
 * 首页组件2+1
 */
public class Component2And1Cell extends HomeCell implements CellBinder<View, Component2And1Cell>, ItemClickSupport {

    View layout_2_1_top_lift;
    View layout_2_1_top_right;
    RecyclerView layout_2_1_bottom;

    private OvalImageView card_bg_iv_lift, card_pic_iv_lift;
    private TextView card_title_tv_lift;
    private ImageView card_play_iv_lift;
    private ImageView vip_icon_lift;
    private RateView card_layout_playing_lift;

    private OvalImageView card_bg_iv_right, card_pic_iv_right;
    private TextView card_title_tv_right;
    private ImageView card_play_iv_right;
    private ImageView vip_icon_riht;
    private RateView card_layout_playing_right;

    private Component2And1Cell dataCell;
    private boolean isPlayTopItem = false;//是不是播放的上方的两个组件
    private int imageHeight = R.dimen.y94;
    private int imageWidth = R.dimen.y94;

    private static final String TAG = "Component2And1Cell";

    private boolean isViewInitialized = false; // 是否已初始化
    private boolean hasAppliedScaling = false;// 是否已应用了图片尺寸约束
    private Component2And1BottomAdapter bottomAdapter;

    @Override
    public void mountView(@NonNull Component2And1Cell data, @NonNull View view, int position) {
        layout_2_1_top_lift = view.findViewById(R.id.layout_2_1_top_lift);
        layout_2_1_top_right = view.findViewById(R.id.layout_2_1_top_right);
        layout_2_1_bottom = view.findViewById(R.id.layout_2_1_bottom);
        Card2x1UtilInter kRadioNavBarInter = ClazzImplUtil.getInter("Card2x1UtilImpl");
        if (kRadioNavBarInter != null) {
            imageWidth = kRadioNavBarInter.imageWidth();
            imageHeight = kRadioNavBarInter.imageHeight();
        }

        dataCell = data;
        if (!isViewInitialized) {
            initView();
            isViewInitialized = true;
            // 将post回调移到这里，确保只执行一次
            if (!hasAppliedScaling) {
                card_pic_iv_lift.post(() -> {
                    if (!view.isAttachedToWindow()) return;
                    ViewGroup.LayoutParams params = card_pic_iv_lift.getLayoutParams();
                    params.width = (int)(params.width * 0.85);
                    params.height = (int)(params.height * 0.85);
                    card_pic_iv_lift.setLayoutParams(params);
                });
                card_pic_iv_right.post(() -> {
                    if (!view.isAttachedToWindow()) return;
                    ViewGroup.LayoutParams params = card_pic_iv_right.getLayoutParams();
                    params.width = (int)(params.width * 0.85);
                    params.height = (int)(params.height * 0.85);
                    card_pic_iv_right.setLayoutParams(params);
                });
                hasAppliedScaling = true;
            }
            
            // 预先设置图片尺寸约束
            ConstraintLayout pClLeft = (ConstraintLayout) card_pic_iv_lift.getParent();
            ConstraintSet csLeft = new ConstraintSet();
            csLeft.clone(pClLeft);
            csLeft.constrainWidth(card_pic_iv_lift.getId(), ResUtil.getDimen(imageWidth));
            csLeft.constrainHeight(card_pic_iv_lift.getId(), ResUtil.getDimen(imageHeight));
            csLeft.constrainWidth(card_title_tv_lift.getId(), 0);
            csLeft.connect(card_title_tv_lift.getId(), ConstraintSet.LEFT, ConstraintSet.PARENT_ID, ConstraintSet.LEFT, ResUtil.getDimen(R.dimen.m20));
            csLeft.connect(card_title_tv_lift.getId(), ConstraintSet.RIGHT, ConstraintSet.PARENT_ID, ConstraintSet.RIGHT, ResUtil.getDimen(R.dimen.m10));
            csLeft.applyTo(pClLeft);
            
            // 同样处理右侧图片
            ConstraintLayout pClRight = (ConstraintLayout) card_pic_iv_right.getParent();
            ConstraintSet csRight = new ConstraintSet();
            csRight.clone(pClRight);
            csRight.constrainWidth(card_pic_iv_right.getId(), ResUtil.getDimen(imageWidth));
            csRight.constrainHeight(card_pic_iv_right.getId(), ResUtil.getDimen(imageHeight));
            csRight.constrainWidth(card_title_tv_right.getId(), 0);
            csRight.connect(card_title_tv_right.getId(), ConstraintSet.LEFT, ConstraintSet.PARENT_ID, ConstraintSet.LEFT, ResUtil.getDimen(R.dimen.m20));
            csRight.connect(card_title_tv_right.getId(), ConstraintSet.RIGHT, ConstraintSet.PARENT_ID, ConstraintSet.RIGHT, ResUtil.getDimen(R.dimen.m10));
            csRight.applyTo(pClRight);
        }
        
        setTopLiftViewDate(data);
        setTopRightViewDate(data);
        setBottomViewDate(data);
        // 添加生命周期管理
        setupLifecycleManagement(view);
    }

    @Override
    public int getItemType() {
        return R.layout.component_2_1_layout;
    }

    private BaseHolder.OnViewClickListener onViewClickListener;

    @Override
    public void setOnItemClickListener(BaseHolder.OnViewClickListener listener) {
        onViewClickListener = listener;
    }

    private void initView() {
        //上边左区域
        card_bg_iv_lift = layout_2_1_top_lift.findViewById(R.id.card_bg_iv);
        card_pic_iv_lift = layout_2_1_top_lift.findViewById(R.id.card_pic_iv);
        card_title_tv_lift = layout_2_1_top_lift.findViewById(R.id.card_title_tv);
        card_play_iv_lift = layout_2_1_top_lift.findViewById(R.id.card_play_iv);
        vip_icon_lift = layout_2_1_top_lift.findViewById(R.id.vip_icon);
        card_layout_playing_lift = layout_2_1_top_lift.findViewById(R.id.card_layout_playing);
        //上边右区域
        card_bg_iv_right = layout_2_1_top_right.findViewById(R.id.card_bg_iv);
        card_pic_iv_right = layout_2_1_top_right.findViewById(R.id.card_pic_iv);
        card_title_tv_right = layout_2_1_top_right.findViewById(R.id.card_title_tv);
        card_play_iv_right = layout_2_1_top_right.findViewById(R.id.card_play_iv);
        vip_icon_riht = layout_2_1_top_right.findViewById(R.id.vip_icon);
        card_layout_playing_right = layout_2_1_top_right.findViewById(R.id.card_layout_playing);


        layout_2_1_top_lift.setBackground(ResUtil.getDrawable(R.drawable.component_card_bg_2));
        layout_2_1_top_right.setBackground(ResUtil.getDrawable(R.drawable.component_card_bg_3));
        layout_2_1_bottom.setBackground(ResUtil.getDrawable(R.drawable.component_card_bg_4));

        card_title_tv_lift.setGravity(Gravity.LEFT | Gravity.CENTER_VERTICAL);
        card_title_tv_lift.setMaxLines(1);
        card_title_tv_lift.setEllipsize(END);
        card_title_tv_right.setGravity(Gravity.LEFT | Gravity.CENTER_VERTICAL);
        card_title_tv_right.setMaxLines(1);
        card_title_tv_right.setEllipsize(END);
        // 移除post回调，避免异步修改布局
//        card_pic_iv_lift.post(()->{
//            ViewGroup.LayoutParams params = card_pic_iv_lift.getLayoutParams();
//            params.width = (int)(params.width * 0.85);
//            params.height = (int)(params.height * 0.85);
//            card_pic_iv_lift.setLayoutParams(params);
//        });
//        card_pic_iv_right.post(()->{
//            ViewGroup.LayoutParams params = card_pic_iv_right.getLayoutParams();
//            params.width = (int)(params.width * 0.85);
//            params.height = (int)(params.height * 0.85);
//            card_pic_iv_right.setLayoutParams(params);
//        });
    }
    private void setTopLeftTitleViewSize(int size) {
//        ViewGroup.LayoutParams para;
//        para = card_title_tv_lift.getLayoutParams();
//        para.width = size;
//        card_title_tv_lift.setLayoutParams(para);
    }

    private void setTopRightTitleViewSize(int size) {
//        ViewGroup.LayoutParams para;
//        para = card_title_tv_right.getLayoutParams();
//        para.width = size;
//        card_title_tv_right.setLayoutParams(para);
    }

    /**
     * 设置上方左区域数据
     *
     * @param data
     */
    private void setTopLiftViewDate(Component2And1Cell data) {
        if (data.getContentList() != null && data.getContentList().size() > 0) {
            String title = "";
            if (!TextUtils.isEmpty(data.getContentList().get(0).getTitle())) {
                title = data.getContentList().get(0).getTitle();
            } else if(!TextUtils.isEmpty(data.getContentList().get(0).getProgramDesc())){
                title = data.getContentList().get(0).getProgramDesc();
            }else if(!TextUtils.isEmpty(data.getContentList().get(0).getProgramTitle())){
                title = data.getContentList().get(0).getProgramTitle();
            }else {
                title = data.getContentList().get(0).getDescription();
            }
            card_title_tv_lift.setText(title);
            if (!TextUtils.isEmpty(UrlUtil.getCardBgUrl(data.getContentList().get(0).getImageFiles())))
                ImageLoader.getInstance().displayImage(card_pic_iv_lift.getContext(),
                        UrlUtil.getCardBgUrl(data.getContentList().get(0).getImageFiles()), card_bg_iv_lift);

            layout_2_1_top_lift.setContentDescription(card_title_tv_lift.getText());
            layout_2_1_top_lift.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onViewClickListener != null) {
                        v.setTag(0);
                        onViewClickListener.onViewClick(v, getPositionInParent());
                    }
                }
            });
//            if (data.getContentList().get(0).getType().equals("PageRedirectionColumnMember")
//                    || data.getContentList().get(0).getType().equals("ActivityDetailColumnMember")) {
                if (false/*data.getContentList().get(0) instanceof PageRedirectionColumnMember
                    || data.getContentList().get(0) instanceof ActivityDetailColumnMember*/) {
                card_layout_playing_lift.setVisibility(View.GONE);
                card_pic_iv_lift.setVisibility(View.GONE);
                card_play_iv_lift.setVisibility(View.GONE);
                vip_icon_lift.setVisibility(View.GONE);
                setTopLeftTitleViewSize(ResUtil.getDimen(R.dimen.m113));
            } else {
                card_layout_playing_lift.setVisibility(View.VISIBLE);
                card_pic_iv_lift.setVisibility(View.VISIBLE);
                vip_icon_lift.setVisibility(View.VISIBLE);
                if (!TextUtils.isEmpty(UrlUtil.getCardPicUrl(data.getContentList().get(0).getImageFiles()))) {
                    setTopLeftTitleViewSize(ResUtil.getDimen(R.dimen.m113));

//                    ConstraintLayout pCl = (ConstraintLayout) card_pic_iv_lift.getParent();
//                    ConstraintSet cs = new ConstraintSet();
//                    cs.clone(pCl);
//                    cs.constrainWidth(card_pic_iv_lift.getId(), ResUtil.getDimen(imageWidth));
//                    cs.constrainHeight(card_pic_iv_lift.getId(), ResUtil.getDimen(imageHeight));
//                    cs.constrainWidth(card_title_tv_lift.getId(), 0);
//                    cs.connect(card_title_tv_lift.getId(), ConstraintSet.LEFT, ConstraintSet.PARENT_ID, ConstraintSet.LEFT, ResUtil.getDimen(R.dimen.m20));
//                    cs.connect(card_title_tv_lift.getId(), ConstraintSet.RIGHT, ConstraintSet.PARENT_ID, ConstraintSet.RIGHT, ResUtil.getDimen(R.dimen.m10));
//                    cs.applyTo(pCl);

                    card_pic_iv_lift.setVisibility(View.VISIBLE);
                    ImageLoader.getInstance().displayImageFixedSize(card_pic_iv_lift.getContext(),
                            UrlUtil.getCardPicUrl(data.getContentList().get(0).getImageFiles()), card_pic_iv_lift, ResUtil.getDimen(imageHeight));
                    if (data.getContentList().get(0).getResType() == ResType.TYPE_BROADCAST
                            || data.getContentList().get(0).getResType() == ResType.TYPE_TV) {
                        vip_icon_lift.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_icon_live_class));
                    } else if (data.getContentList().get(0).getResType() == ResType.TYPE_LIVE) {
                        if (data.getContentList().get(0).getLiveStatus()==0||data.getContentList().get(0).getLiveStatus()==6){
                            vip_icon_lift.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_live_icon_reday));
                        }else if (data.getContentList().get(0).getLiveStatus()==1){
                            vip_icon_lift.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_live_icon_playing));
                        }
                    } else if (data.getContentList().get(0).getResType() == ResType.TYPE_VIDEO_AUDIO ||
                            data.getContentList().get(0).getResType() == ResType.TYPE_VIDEO_ALBUM) {
                        vip_icon_lift.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_icon_video));
                    } else {
//                        VipCornerUtil.setVipCorner(vip_icon_lift, data.getContentList().get(0).getVip(), data.getContentList().get(0).getFine(), true);
                        if (data.contentList.get(0) instanceof AlbumDetailColumnMember) {
                            AlbumDetailColumnMember albumDetailColumnMember= (AlbumDetailColumnMember) data.contentList.get(0);
                            VipCornerUtil.setVipCorner(vip_icon_lift,albumDetailColumnMember.getVip(), albumDetailColumnMember.getFine(), false);
                        }else {
                            vip_icon_lift.setVisibility(View.GONE);
                        }
                    }
                } else {
                    setTopLeftTitleViewSize(ResUtil.getDimen(R.dimen.m113));
                    card_pic_iv_lift.setVisibility(View.GONE);
                    vip_icon_lift.setVisibility(View.GONE);
                }
                    boolean notShowPlaying = data.getContentList().get(0) instanceof PageRedirectionColumnMember
                            || data.getContentList().get(0) instanceof ActivityDetailColumnMember;
                boolean b = data.selected
                        && ComponentUtils.getInstance().getCardHomePlayIdIsPlaying(data.getContentList()
                        .get(0).getId(), data.getContentList().get(0).getCanPlay());
                    if (!notShowPlaying) {
                        card_layout_playing_lift.setVisibility(b ? View.VISIBLE : View.GONE);
                        card_play_iv_lift.setVisibility(card_layout_playing_lift.getVisibility() == View.GONE ? View.VISIBLE : View.GONE);
                    } else {
                        card_layout_playing_lift.setVisibility(View.GONE);
                        card_play_iv_lift.setVisibility(View.GONE);
                    }
                    isPlayTopItem = b;
            }


        } else {
            card_layout_playing_lift.setVisibility(View.GONE);
            card_pic_iv_lift.setVisibility(View.GONE);
            card_play_iv_lift.setVisibility(View.GONE);
            vip_icon_lift.setVisibility(View.GONE);
        }

    }

    /**
     * 设置上方右区域数据
     *
     * @param data
     */
    private void setTopRightViewDate(Component2And1Cell data) {
        if (data.getContentList() != null && data.getContentList().size() > 1) {
            String title = "";
            if (!TextUtils.isEmpty(data.getContentList().get(1).getTitle())) {
                title = data.getContentList().get(1).getTitle();
            } else if(!TextUtils.isEmpty(data.getContentList().get(1).getProgramDesc())){
                title = data.getContentList().get(1).getProgramDesc();
            }else if(!TextUtils.isEmpty(data.getContentList().get(1).getProgramTitle())){
                title = data.getContentList().get(1).getProgramTitle();
            }else {
                title = data.getContentList().get(1).getDescription();
            }
            card_title_tv_right.setText(title);
            if (!TextUtils.isEmpty(UrlUtil.getCardBgUrl(data.getContentList().get(1).getImageFiles())))
                ImageLoader.getInstance().displayImage(card_pic_iv_right.getContext(),
                        UrlUtil.getCardBgUrl(data.getContentList().get(1).getImageFiles()), card_bg_iv_right);

            layout_2_1_top_right.setContentDescription(card_title_tv_right.getText());
            layout_2_1_top_right.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onViewClickListener != null) {
                        v.setTag(1);
                        onViewClickListener.onViewClick(v, getPositionInParent());
                    }
                }
            });
            if (false/*data.getContentList().get(1) instanceof PageRedirectionColumnMember
                    || data.getContentList().get(1) instanceof ActivityDetailColumnMember*/) {
                card_layout_playing_right.setVisibility(View.GONE);
                card_pic_iv_right.setVisibility(View.GONE);
                card_play_iv_right.setVisibility(View.GONE);
                vip_icon_riht.setVisibility(View.GONE);

                setTopRightTitleViewSize(ResUtil.getDimen(R.dimen.m113));
            } else {
                card_layout_playing_right.setVisibility(View.VISIBLE);

//                ConstraintLayout pCl = (ConstraintLayout) card_pic_iv_right.getParent();
//                ConstraintSet cs = new ConstraintSet();
//                cs.clone(pCl);
//                cs.constrainWidth(card_pic_iv_right.getId(), ResUtil.getDimen(imageWidth));
//                cs.constrainHeight(card_pic_iv_right.getId(), ResUtil.getDimen(imageHeight));
//                cs.constrainWidth(card_title_tv_right.getId(), 0);
//                cs.connect(card_title_tv_right.getId(), ConstraintSet.LEFT, ConstraintSet.PARENT_ID, ConstraintSet.LEFT, ResUtil.getDimen(R.dimen.m20));
//                cs.connect(card_title_tv_right.getId(), ConstraintSet.RIGHT, ConstraintSet.PARENT_ID, ConstraintSet.RIGHT, ResUtil.getDimen(R.dimen.m10));
//                cs.applyTo(pCl);

                card_pic_iv_right.setVisibility(View.VISIBLE);

                if (!TextUtils.isEmpty(UrlUtil.getCardPicUrl(data.getContentList().get(1).getImageFiles()))) {
                    setTopRightTitleViewSize(ResUtil.getDimen(R.dimen.m113));
                    card_pic_iv_right.setVisibility(View.VISIBLE);
                    vip_icon_riht.setVisibility(View.VISIBLE);
                    ImageLoader.getInstance().displayImageFixedSize(card_pic_iv_right.getContext(),
                            UrlUtil.getCardPicUrl(data.getContentList().get(1).getImageFiles()), card_pic_iv_right, ResUtil.getDimen(imageHeight));
                    if (data.getContentList().get(1).getResType() == ResType.TYPE_BROADCAST
                            || data.getContentList().get(1).getResType() == ResType.TYPE_TV) {
                        vip_icon_riht.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_icon_live_class));
                    }  else if (data.getContentList().get(1).getResType() == ResType.TYPE_LIVE) {
                        if (data.getContentList().get(1).getLiveStatus()==0||data.getContentList().get(1).getLiveStatus()==6){
                            vip_icon_riht.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_live_icon_reday));
                        }else if (data.getContentList().get(1).getLiveStatus()==1){
                            vip_icon_riht.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_live_icon_playing));
                        }
                    } else if (data.getContentList().get(1).getResType() == ResType.TYPE_VIDEO_AUDIO ||
                            data.getContentList().get(1).getResType() == ResType.TYPE_VIDEO_ALBUM) {
                        vip_icon_riht.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_icon_video));
                    } else {
//                        VipCornerUtil.setVipCorner(vip_icon_riht, data.getContentList().get(1).getVip(), data.getContentList().get(1).getFine(), true);
                        if (data.contentList.get(1) instanceof AlbumDetailColumnMember) {
                            AlbumDetailColumnMember albumDetailColumnMember= (AlbumDetailColumnMember) data.contentList.get(1);
                            VipCornerUtil.setVipCorner(vip_icon_riht,albumDetailColumnMember.getVip(), albumDetailColumnMember.getFine(), false);
                        }else {
                            vip_icon_riht.setVisibility(View.GONE);
                        }
                    }
                } else {
                    setTopRightTitleViewSize(ResUtil.getDimen(R.dimen.m113));
                    card_pic_iv_right.setVisibility(View.GONE);
                    vip_icon_riht.setVisibility(View.GONE);
                }

                boolean notShowPlaying = data.getContentList().get(1) instanceof PageRedirectionColumnMember
                        || data.getContentList().get(1) instanceof ActivityDetailColumnMember;
                boolean b = data.selected
                        && ComponentUtils.getInstance().getCardHomePlayIdIsPlaying(data.getContentList().get(1).getId()
                        , data.getContentList().get(1).getCanPlay());
                if (!notShowPlaying) {
                    card_layout_playing_right.setVisibility(b ?
                            View.VISIBLE : View.GONE);
                    card_play_iv_right.setVisibility(!b ? View.VISIBLE : View.GONE);
                } else {
                    card_layout_playing_right.setVisibility(View.GONE);
                    card_play_iv_right.setVisibility(View.GONE);
                }
                if (!isPlayTopItem)
                    isPlayTopItem = b;
            }

        } else {
            card_layout_playing_right.setVisibility(View.GONE);
            card_pic_iv_right.setVisibility(View.GONE);
            card_play_iv_right.setVisibility(View.GONE);
            vip_icon_riht.setVisibility(View.GONE);
        }

    }

    private List<ColumnContent> contentListBottom = new ArrayList<>();
    private int selectIndex;//当前选中的page
    // CPU优化：禁用轮播功能以降低CPU使用率
    private static final boolean CAROUSEL_ENABLED = false;
    private int delayMillis = 5 * 1000;//轮播时长，优化为5秒
    private CarouselHandler mHandler = new CarouselHandler(this);

    // 可见性检查优化 - 缓存可见性状态，避免频繁调用getGlobalVisibleRect
    private boolean mLastVisibilityState = false;
    private long mLastVisibilityCheckTime = 0;
    private static final long VISIBILITY_CHECK_INTERVAL = 1000; // 1秒检查一次可见性

    /**
     * 使用弱引用Handler避免内存泄漏
     */
    private static class CarouselHandler extends Handler {
        private final WeakReference<Component2And1Cell> mCellRef;

        CarouselHandler(Component2And1Cell cell) {
            mCellRef = new WeakReference<>(cell);
        }

        @Override
        public void handleMessage(Message msg) {
            Component2And1Cell cell = mCellRef.get();
            if (cell == null) return;
            cell.handleCarouselMessage(msg);
        }
    }

    /**
     * 处理轮播消息 - CPU优化：已禁用轮播功能
     */
    private void handleCarouselMessage(Message msg) {
        // CPU优化：轮播功能已禁用，直接返回
        if (!CAROUSEL_ENABLED) {
            return;
        }
        // 优化的可见性检查 - 只在可见时执行轮播
        if (!isViewVisibleOptimized()) {
            return;
        }
        if (dataCell.selected && !isPlayTopItem) {
            return;
        }
        if (contentListBottom.size() > 0) {
            selectIndex = (selectIndex + 1) % contentListBottom.size();
            // 使用scrollToPosition替代smoothScrollToPosition以优化CPU性能
            layout_2_1_bottom.scrollToPosition(selectIndex);

            Logger.d("Component2And3Cell", "--轮播切换--"+contentListBottom.get(selectIndex).getTitle()+":" + selectIndex);
            mHandler.sendEmptyMessageDelayed(1011, delayMillis);
        }
    }

    /**
     * 检查组件是否可见 - 原始方法，保留用于兼容性
     */
    private boolean isViewVisible() {
        if (layout_2_1_bottom == null) return false;

        Rect rect = new Rect();
        boolean isVisible = layout_2_1_bottom.getGlobalVisibleRect(rect);
        return isVisible && rect.height() > 0 && rect.width() > 0;
    }

    /**
     * 优化的可见性检查 - 减少频繁的getGlobalVisibleRect调用
     * 使用时间间隔缓存机制，避免每次轮播都进行昂贵的可见性检查
     */
    private boolean isViewVisibleOptimized() {
        if (layout_2_1_bottom == null) return false;
        long currentTime = System.currentTimeMillis();
        // 如果距离上次检查时间小于间隔，直接返回缓存的结果
        if (currentTime - mLastVisibilityCheckTime < VISIBILITY_CHECK_INTERVAL) {
            return mLastVisibilityState;
        }
        // 执行实际的可见性检查
        Rect rect = new Rect();
        boolean isVisible = layout_2_1_bottom.getGlobalVisibleRect(rect);
        boolean currentVisibility = isVisible && rect.height() > 0 && rect.width() > 0;
        // 更新缓存
        mLastVisibilityState = currentVisibility;
        mLastVisibilityCheckTime = currentTime;
        return currentVisibility;
    }

    /**
     * 停止轮播
     */
    private void stopCarousel() {
        if (mHandler.hasMessages(1011)) {
            mHandler.removeMessages(1011);
        }
    }

    /**
     * 启动轮播 - CPU优化：已禁用轮播功能
     */
    private void startCarousel() {
        // CPU优化：轮播功能已禁用，直接返回
        if (!CAROUSEL_ENABLED) {
            return;
        }
        if (shouldStartCarousel()) {
            mHandler.sendEmptyMessageDelayed(1011, delayMillis);
        }
    }

    /**
     * 检查是否应该启动轮播 - CPU优化：已禁用轮播功能
     */
    private boolean shouldStartCarousel() {
        // CPU优化：轮播功能已禁用
        if (!CAROUSEL_ENABLED) {
            return false;
        }
        return contentListBottom.size() > 1
               && layout_2_1_bottom != null
               && layout_2_1_bottom.isAttachedToWindow()
               && isViewVisibleOptimized()
               && !mHandler.hasMessages(1011);
    }

    /**
     * 设置下方区域数据
     *
     * @param data
     */
    private void setBottomViewDate(Component2And1Cell data) {
        if (data.getContentList() != null && data.getContentList().size() > 2) {
            contentListBottom.clear();
            for (int i = 2; i < data.getContentList().size(); i++) {
                //从第三条数据开始都是底部轮播的数据
                contentListBottom.add(data.getContentList().get(i));
            }

            if (bottomAdapter == null) {
                // 第一次创建适配器
                layout_2_1_bottom.setLayoutManager(new LinearLayoutManager(layout_2_1_bottom.getContext()));
                bottomAdapter = new Component2And1BottomAdapter(layout_2_1_bottom.getContext(), contentListBottom, data.selected);
                layout_2_1_bottom.setAdapter(bottomAdapter);

                if (layout_2_1_bottom.getOnFlingListener() == null) {
                    //设置为ViewPage样式的list
                    PagerSnapHelper snapHelper = new PagerSnapHelper();
                    snapHelper.attachToRecyclerView(layout_2_1_bottom);
                }
                layout_2_1_bottom.setOnTouchListener(new View.OnTouchListener() {
                    @Override
                    public boolean onTouch(View v, MotionEvent event) {
                        boolean b = false;
                        if (contentListBottom.size() > 1) {
                            switch (event.getAction()) {//处理禁止用户手动滑动的逻辑
                                case MotionEvent.ACTION_MOVE://移动
                                    b = true;
                                    break;
                            }
                        }
                        return b;
                    }
                });
            } else {
                // 更新适配器数据
                bottomAdapter.updateData(contentListBottom, data.selected);
            }


            if (dataCell.selected && !isPlayTopItem) {
                boolean b = false;
                int index = -1;
                for (int i = 0; i < contentListBottom.size(); i++) {
                    if (ComponentUtils.getInstance().getCardHomePlayIdIsPlaying(contentListBottom.get(i).getId())) {
                        b = true;//如果是当前item在播放而且不是上发两个组件的话，判断是不是下边轮播里的
                        index = i;
                        break;
                    }
                }
                if (b) {
                    //如果播放的是当前组件区，并且不是上方两个组件
                    stopCarousel();
                    int finalIndex = index;
                    if (finalIndex >= 0) {
                        layout_2_1_bottom.scrollToPosition(finalIndex);
                        selectIndex = finalIndex;
                    }
                } else {
                    // 使用新的轮播启动方法
                    startCarousel();
                }
            } else {
                // 使用新的轮播启动方法
                startCarousel();
            }
        } else {
            // 没有底部数据，清空并停止轮播
            contentListBottom.clear();
            if (bottomAdapter != null) {
                bottomAdapter.updateData(contentListBottom, data.selected);
            }
            stopCarousel();
        }
    }


    public class Component2And1BottomAdapter extends RecyclerView.Adapter<Component2And1BottomAdapter.ViewHolder> {
        private Context context;
        private List<ColumnContent> contentListBottom;
        private boolean isSelect;

        public Component2And1BottomAdapter(Context context, List<ColumnContent> contentListBottom, boolean isSelect) {
            this.context = context;
            this.contentListBottom = contentListBottom;
            this.isSelect = isSelect;
        }

        public void updateData(List<ColumnContent> newData, boolean isSelected) {
            this.contentListBottom = newData;
            this.isSelect = isSelected;
            notifyDataSetChanged();
        }

        @Override
        public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
            return new ViewHolder(LayoutInflater.from(context).inflate(R.layout.component_content_big_card_layout, parent, false));
        }

        @Override
        public void onBindViewHolder(ViewHolder holder, @SuppressLint("RecyclerView") int position) {
            if (!TextUtils.isEmpty(contentListBottom.get(position).getTitle())) {
                holder.card_title_tv_bottom.setText(contentListBottom.get(position).getTitle() + "");
            }else if (!TextUtils.isEmpty(contentListBottom.get(position).getProgramTitle())) {
                holder.card_title_tv_bottom.setText(contentListBottom.get(position).getProgramTitle() + "");
            }

            if(!TextUtils.isEmpty(contentListBottom.get(position).getMainbody())){
                holder.card_des_tv_bottom.setText(contentListBottom.get(position).getMainbody() + "");// flag
            }else if(!TextUtils.isEmpty(contentListBottom.get(position).getProgramDesc())){
                holder.card_des_tv_bottom.setText(contentListBottom.get(position).getProgramDesc() + "");// flag
            }

            if (!TextUtils.isEmpty(UrlUtil.getCardBgUrl(contentListBottom.get(position).getImageFiles())))
                ImageLoader.getInstance().displayImage(context,
                        UrlUtil.getCardBgUrl(contentListBottom.get(position).getImageFiles()), holder.card_bg_iv_bottom);

            holder.itemView.setContentDescription(holder.card_title_tv_bottom.getText());
            holder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onViewClickListener != null) {
                        v.setTag(position + 2);//因为是从第三条数据开始轮播，所以要加2
                        stopCarousel();
                        Logger.d("Component2And3Cell", "-点击--"+contentListBottom.get(selectIndex).getTitle()+":" + position);
                        onViewClickListener.onViewClick(v, getPositionInParent());
//                        if (contentListBottom.get(position).getType().equals("PageRedirectionColumnMember")
//                                || contentListBottom.get(position).getType().equals("ActivityDetailColumnMember")) {
                            if (contentListBottom.get(position) instanceof PageRedirectionColumnMember
                                    || contentListBottom.get(position) instanceof ActivityDetailColumnMember) {
                            //如果是跳转类型或者活动不要打断轮播
                            return;
                        }
                        selectIndex = position;
                    }
                }
            });
            if (false/*contentListBottom.get(position) instanceof PageRedirectionColumnMember
                    || contentListBottom.get(position) instanceof ActivityDetailColumnMember*/) {
                holder.card_layout_playing_bottom.setVisibility(View.GONE);
                holder.card_pic_iv_bottom.setVisibility(View.GONE);
                holder.card_play_iv_bottom.setVisibility(View.GONE);
                holder.vip_icon_bottom.setVisibility(View.GONE);
            } else {
                holder.card_layout_playing_bottom.setVisibility(View.VISIBLE);
                holder.card_pic_iv_bottom.setVisibility(View.VISIBLE);
                holder.vip_icon_bottom.setVisibility(View.VISIBLE);
                if (!TextUtils.isEmpty(UrlUtil.getCardPicUrl(contentListBottom.get(position).getImageFiles()))) {
                    holder.card_pic_iv_bottom.setVisibility(View.VISIBLE);
                    ImageLoader.getInstance().displayImageFixedSize(card_pic_iv_right.getContext(),
                            UrlUtil.getCardPicUrl(contentListBottom.get(position).getImageFiles()), holder.card_pic_iv_bottom, ResUtil.getDimen(R.dimen.m132));
                    if (contentListBottom.get(position).getResType() == ResType.TYPE_BROADCAST
                            || contentListBottom.get(position).getResType() == ResType.TYPE_TV) {
                        holder.vip_icon_bottom.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_icon_live_class));
                    }  else if (contentListBottom.get(position).getResType() == ResType.TYPE_LIVE) {
                        if (contentListBottom.get(position).getLiveStatus()==0||contentListBottom.get(position).getLiveStatus()==6){
                            holder.vip_icon_bottom.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_live_icon_reday));
                        }else if (contentListBottom.get(position).getLiveStatus()==1){
                            holder.vip_icon_bottom.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_live_icon_playing));
                        }
                    } else if (contentListBottom.get(position).getResType() == ResType.TYPE_VIDEO_AUDIO ||
                            contentListBottom.get(position).getResType() == ResType.TYPE_VIDEO_ALBUM) {
                        holder.vip_icon_bottom.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_icon_video));
                    } else {
//                        VipCornerUtil.setVipCorner(holder.vip_icon_bottom, contentListBottom.get(position).getVip(), contentListBottom.get(position).getFine(), true);
                        if (contentListBottom.get(position) instanceof AlbumDetailColumnMember) {
                            AlbumDetailColumnMember albumDetailColumnMember= (AlbumDetailColumnMember) contentListBottom.get(position);
                            VipCornerUtil.setVipCorner(holder.vip_icon_bottom,albumDetailColumnMember.getVip(), albumDetailColumnMember.getFine(), false);
                        }else {
                            holder.vip_icon_bottom.setVisibility(View.GONE);
                        }
                    }
                } else {
                    holder.card_pic_iv_bottom.setVisibility(View.GONE);
                    holder.vip_icon_bottom.setVisibility(View.GONE);
                }
                boolean notShowPlaying = contentListBottom.get(position) instanceof PageRedirectionColumnMember
                        || contentListBottom.get(position) instanceof ActivityDetailColumnMember;
                boolean select = isSelect
                        && ComponentUtils.getInstance().getCardHomePlayIdIsPlaying(contentListBottom.get(position).getId(),
                        contentListBottom.get(position).getCanPlay());
                if (!notShowPlaying) {
                    holder.card_layout_playing_bottom.setVisibility(select ?
                            View.VISIBLE : View.GONE);
                    holder.card_play_iv_bottom.setVisibility(holder.card_layout_playing_bottom.getVisibility() != View.GONE ? View.GONE : View.VISIBLE);
                } else {
                    holder.card_layout_playing_bottom.setVisibility(View.GONE);
                    holder.card_play_iv_bottom.setVisibility(View.GONE);
                }
                if (select) {
                    selectIndex = position;
                }
            }
        }

        @Override
        public int getItemCount() {
            return contentListBottom.size();
        }

        public class ViewHolder extends RecyclerView.ViewHolder {
            private OvalImageView card_bg_iv_bottom, card_pic_iv_bottom;
            private TextView card_title_tv_bottom, card_des_tv_bottom;
            private ImageView card_play_iv_bottom;
            private ImageView vip_icon_bottom;
            private RateView card_layout_playing_bottom;

            public ViewHolder(View itemView) {
                super(itemView);
                //下方区域
                card_bg_iv_bottom = itemView.findViewById(R.id.card_big_bg_iv);
                card_pic_iv_bottom = itemView.findViewById(R.id.card_big_pic_iv);
                card_title_tv_bottom = itemView.findViewById(R.id.card_big_title_tv);
                card_des_tv_bottom = itemView.findViewById(R.id.card_big_des_tv);
                card_play_iv_bottom = itemView.findViewById(R.id.card_big_play_iv);
                vip_icon_bottom = itemView.findViewById(R.id.vip_icon);
                card_layout_playing_bottom = itemView.findViewById(R.id.card_layout_playing);

                card_title_tv_bottom.setMaxLines(1);
                card_des_tv_bottom.setMaxLines(3);
                card_des_tv_bottom.setEllipsize(END);
            }
        }
    }

    /**
     * 设置生命周期管理 - 防止后台继续执行轮播
     */
    private void setupLifecycleManagement(View view) {
        view.addOnAttachStateChangeListener(new View.OnAttachStateChangeListener() {
            @Override
            public void onViewAttachedToWindow(View v) {
                // View附加到窗口时，重置可见性缓存并启动轮播
                resetVisibilityCache();
                if (shouldStartCarousel()) {
                    startCarousel();
                }
            }

            @Override
            public void onViewDetachedFromWindow(View v) {
                // View从窗口分离时，停止轮播以节省CPU，并重置可见性缓存
                stopCarousel();
                resetVisibilityCache();
            }
        });
    }

    /**
     * 重置可见性缓存 - 在生命周期变化时调用
     */
    private void resetVisibilityCache() {
        mLastVisibilityState = false;
        mLastVisibilityCheckTime = 0;
    }

    @Override
    public void release() {
        super.release();
        // CPU优化：安全地清理Glide图片资源
        if (card_pic_iv_lift != null) {
            Context context = card_pic_iv_lift.getContext();
            if (context != null && !isContextDestroyed(context)) {
                Logger.i(TAG, "清理card_pic_iv_lift图片资源");
                Glide.with(context).clear(card_pic_iv_lift);
            }
        }
        if (card_pic_iv_right != null) {
            Context context = card_pic_iv_right.getContext();
            if (context != null && !isContextDestroyed(context)) {
                Logger.i(TAG, "清理card_pic_iv_right图片资源");
                Glide.with(context).clear(card_pic_iv_right);
            }
        }
    }

    /**
     * 检查Context是否已被销毁
     */
    private boolean isContextDestroyed(Context context) {
        if (context instanceof Activity) {
            return ((Activity) context).isDestroyed();
        }
        return false;
    }

}
