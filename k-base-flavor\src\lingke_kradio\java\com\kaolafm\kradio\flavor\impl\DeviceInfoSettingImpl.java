package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.util.Log;

import com.ecarx.sdk.device.DeviceAPI;
import com.kaolafm.kradio.flavor.utils.DeviceInfoUtil;
import com.kaolafm.kradio.lib.base.flavor.DeviceInfoSetting;

/******************************************
 * 类描述:
 *
 ******************************************/
public final class DeviceInfoSettingImpl implements DeviceInfoSetting {
    private static final String TAG = DeviceInfoSettingImpl.class.getSimpleName();

    @Override
    public void setInfoForSDK(Context context) {
        String deviceId = null;
        String carType = null;
        try {
            deviceId = DeviceAPI.get(context).getOpenVIN();
            carType = DeviceAPI.get(context).getVehicleType();
        } catch (Exception e) {
            e.printStackTrace();
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }

        Log.i(TAG, "setInfoForSDK: deviceId=" + deviceId + ":type=" + carType);
        DeviceInfoUtil.setDeviceIdAndCarType(deviceId, carType);
    }
}
