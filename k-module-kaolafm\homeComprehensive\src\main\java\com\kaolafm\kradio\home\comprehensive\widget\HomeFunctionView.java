package com.kaolafm.kradio.home.comprehensive.widget;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.kaolafm.kradio.coin.CoinTag;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.kradio.component.ComponentClient;
import com.kaolafm.kradio.component.DynamicComponent;
import com.kaolafm.kradio.component.MainThreadable;
import com.kaolafm.kradio.component.RealCaller;
import com.kaolafm.kradio.constant.LoginProcessorConst;
import com.kaolafm.kradio.constant.UserComponentConst;
import com.kaolafm.kradio.constant.UserStateObserverProcessorConst;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.ui.BaseShowHideFragment;
import com.kaolafm.kradio.user.BackUserFragment;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.ComponentUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.SharedPreferenceUtil;
import com.kaolafm.kradio.mine.MineUtil;
import com.kaolafm.kradio.user.UserInfoManager;

import me.yokeyword.fragmentation.SupportActivity;

/**
 * 首页功能入口自定义view
 *
 * <AUTHOR> Yan
 * @date 2019-08-19
 */
public class HomeFunctionView extends LinearLayout {

//    private OnFunctionViewClickListener mListener;

    protected int[] icons;

    private DynamicComponent mHomeFunctionLoginComponent;

    public HomeFunctionView(Context context) {
        this(context, null);
    }

    public HomeFunctionView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public HomeFunctionView(Context context, @Nullable AttributeSet attrs,
            int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }
    private void init() {
        setOrientation(LinearLayout.HORIZONTAL);
        removeAllViews();
        /*
        icons = new int[]{R.drawable.ic_home_search_selector, R.drawable.ic_home_subscribe_selector, R.drawable.ic_home_history_selector,
            R.drawable.ic_home_purchased_selector, R.drawable.ic_home_person_normal_selector};
         */
        icons = new int[]{R.drawable.ic_home_search_selector, /*R.drawable.ic_home_subscribe_selector, R.drawable.ic_home_history_selector,*/
                R.drawable.ic_home_person_normal_selector};
        LayoutInflater layoutInflater = LayoutInflater.from(getContext());

        for (int i = 0, length = icons.length; i < length; i++) {
            ImageView funcView = (ImageView) layoutInflater.inflate(R.layout.view_home_function, this, false);
            final int position = i;
            if(i==0) {
                funcView.setId(R.id.iv_home_search);
            } else if(i==1) {
                funcView.setId(R.id.iv_home_person_normal);
            }
            funcView.setOnClickListener(v -> {
                if (!AntiShake.check(v.getId())) {
                    onFuncClick(funcView,position);
                }
            });
            setIcon(funcView, icons[i]);
            addView(funcView);
        }
        setPersonCenterStatus();
    }

    protected void setIcon(ImageView funcView, int icon) {
        Drawable drawable = ResUtil.getDrawable(icon);
        int dimen = ResUtil.getDimen(R.dimen.m28);
        drawable.setBounds(0,0, dimen, dimen);
        funcView.setImageDrawable(drawable);
        //funcView.setBackground(ResUtil.getDrawable(R.drawable.color_main_button_click_selector));
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        mHomeFunctionLoginComponent = new HomeFunctionLoginComponent();
        ComponentUtil.addObserver(UserComponentConst.NAME, mHomeFunctionLoginComponent);
    }

    private class HomeFunctionLoginComponent implements DynamicComponent, MainThreadable {

        @Override
        public String getName() {
            return getClass().getSimpleName();
        }

        @Override
        public boolean onCall(RealCaller caller) {
            String actionName = caller.actionName();
            if (UserStateObserverProcessorConst.USER_LOGIN.equals(actionName)) {
                setPersonCenterStatus(true);
            }else if (UserStateObserverProcessorConst.USER_LOGOUT.equals(actionName)){
                setPersonCenterStatus(false);
            }
            return false;
        }

        @Override
        public Boolean shouldActionRunOnMainThread(String actionName, ComponentClient caller) {
            return true;
        }
    }

    public void setPersonCenterStatus() {
        boolean isUserBound = false;
        try {
            isUserBound = ComponentUtil.getResultValue(UserComponentConst.NAME, UserComponentConst.IS_USER_BOUND);
        } catch (Exception e) {
            e.printStackTrace();
        }
        setPersonCenterStatus(isUserBound);
    }

    public void setPersonCenterStatus(boolean isLogin) {
        ImageView child = (ImageView) getChildAt(1);
        if (child != null) {
            int iconId = /*isLogin ? R.drawable.ic_home_person_selected : */R.drawable.ic_home_person_normal_selector;
            setIcon(child, iconId);
        }
    }

    private void onFuncClick(ImageView imageView,int position) {
        BaseShowHideFragment fragment = null;
        SupportActivity activity = (SupportActivity) getContext();
        if(position == 0){
//            ComponentClient.obtainBuilder(SearchComponentConst.NAME)
//                    .setActionName(SearchComponentConst.START_ACTIVITY)
//                    .addParam("context", activity)
//                    .build().callAsync();
            Bundle bundle = new Bundle();
//            bundle.putString(SearchActivity.PARAM_SEARCH_HINT, currentData);
            RouterManager.getInstance().jumpPage(RouterConstance.SEARCH_COMPREHENSIVE_URL, bundle);
        }else if(position == 1){
            if (imageView.getTag() instanceof CoinTag) {
                CoinTag coinTag = (CoinTag) imageView.getTag();
                if (coinTag.showCoinAnim) {
                    fragment = MineUtil.INSTANCE.showCoinFragment();
                    SharedPreferenceUtil adSP = SharedPreferenceUtil.getInstance(getContext(), "k_ad");
                    adSP.putBoolean("hasclicked", true);
                    setPersonCenterStatus();
                } else {
                    fragment = MineUtil.INSTANCE.showLoginFragment();
                }
            } else {
                fragment = MineUtil.INSTANCE.showLoginFragment();
            }
            if (fragment instanceof BackUserFragment) {
                ((BackUserFragment)fragment).setBackData(LoginProcessorConst.BACKTYPE_NONE,
                        Constants.HOME_TO_LOGIN);
            }
            UserInfoManager.getInstance().setLoginType("");
            activity.extraTransaction().start(fragment);
        }

//        switch (position) {
//            case 0:
//                fragment = new SearchFragment();
//                break;
//            /*
//            case 1:
//                fragment = MineUtil.INSTANCE.showSubscribeFragment();
//                break;
//            case 2:
//                fragment = MineUtil.INSTANCE.showHistoryFragment();
//                break;
//            case 3:
//                fragment = MineUtil.INSTANCE.showPurchasedFragment();
//                break;
//             */
//            case 1:
//                if (imageView.getTag() instanceof CoinTag) {
//                    CoinTag coinTag = (CoinTag) imageView.getTag();
//                    if (coinTag.showCoinAnim) {
//                        fragment = MineUtil.INSTANCE.showCoinFragment();
//                        SharedPreferenceUtil adSP = SharedPreferenceUtil.getInstance(getContext(), "k_ad", Context.MODE_PRIVATE);
//                        adSP.putBoolean("hasclicked", true);
//                        setPersonCenterStatus();
//                    } else {
//                        fragment = MineUtil.INSTANCE.showLoginFragment();
//                    }
//                } else {
//                    fragment = MineUtil.INSTANCE.showLoginFragment();
//                }
//                if (fragment instanceof BackUserFragment) {
//                    ((BackUserFragment)fragment).setBackData(LoginProcessorConst.BACKTYPE_NONE,
//                            Constants.HOME_TO_LOGIN);
//                }
//                UserInfoManager.getInstance().setLoginType("");
//                break;
//            default:
//        }
//        if (mListener != null) {
//            mListener.onClick(fragment);
//        }
    }

//    public void setOnFunctionViewClickListener(OnFunctionViewClickListener listener) {
//        mListener = listener;
//    }


    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        ComponentUtil.removeObserver(UserComponentConst.NAME, mHomeFunctionLoginComponent);
    }

//    public interface OnFunctionViewClickListener {
//
//        void onClick(BaseFragment fragment);
//        void onClick(BaseActivity activity);
//    }


}
