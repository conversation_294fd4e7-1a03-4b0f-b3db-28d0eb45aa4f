package com.kaolafm.kradio.aop;

import android.util.Log;
import android.view.View;

import com.kaolafm.kradio.common.SubcategoryItemBean;
import com.kaolafm.kradio.common.widget.SquareImageView;
import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.k_kaolafm.home.item.BroadcastCell;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;

/**
 * @Package: com.kaolafm.kradio.aop
 * @Description:
 * @Author: Maclay
 * @Date: 17:51
 */
@Aspect
public class CellAop {
    private static final String TAG = "CellAop";

    @Around("execution(* BroadcastCell.mountView(..))")
    public void BroadcastCell_mountView(ProceedingJoinPoint point) throws Throwable {
        Log.d(TAG,"BroadcastCell_mountView");
        point.proceed();
        try {
            BroadcastCell data = (BroadcastCell) point.getThis();
            View view = (View) point.getArgs()[1];
            SquareImageView iv_cover = view.findViewById(R.id.iv_cover);
            if (iv_cover != null) {
                ImageLoader.getInstance().displayImage(view.getContext(), data.imageUrl, iv_cover);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    @Around("execution(* HorizontalSubcategoryAdapter.getViewHolder(..))")
    public BaseHolder<SubcategoryItemBean> getViewHolder(ProceedingJoinPoint point) throws Throwable {
        Log.d(TAG,"getViewHolder");
        try {
            int viewType = (int)point.getArgs()[1];
            if (viewType == SubcategoryItemBean.TYPE_ITEM_BROADCAST_LOCAL){
                return (BaseHolder<SubcategoryItemBean>) point.proceed(new Object[]{point.getArgs()[0],SubcategoryItemBean.TYPE_ITEM_ALBUM});
            }else{
                return (BaseHolder<SubcategoryItemBean>) point.proceed();
            }
        } catch (Throwable throwable) {
            throwable.printStackTrace();
            return (BaseHolder<SubcategoryItemBean>) point.proceed();
        }
    }
}
