package com.kaolafm.kradio.component.ui.base;

import androidx.annotation.NonNull;
import android.view.View;

/**
 * cell绑定接口，用于绑定数据和cell布局
 *
 * <AUTHOR>
 * @date 2019-08-15
 */
public interface CellBinder<V extends View, D> {

    /**
     * 数据绑定view
     *
     * @param data     item 数据
     * @param view     item 布局
     * @param position 该cell所在位置
     */
    void mountView(@NonNull D data, @NonNull V view, int position);
}
