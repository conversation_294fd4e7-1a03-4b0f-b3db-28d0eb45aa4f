package com.iflytek.autofly.home.aidl;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * <AUTHOR> k<PERSON><PERSON><PERSON>
 * @name AIUILauncher
 * @time 2020/1/6 11:09
 */
public class IMediaBean implements Parcelable {


    /**
     * 节目唯一标识    用来拉去歌词的，电台不需要
     */
    private String trackId;
    /**
     *    1：音乐  2：有声读物   3：电台    4：品牌电台（建议添加这里）   (电台需要传)
     */
    private int type;
    /**
     *1：本地音乐  2：蓝牙音乐  3：USB音乐 4：QQ音乐（默认）  电台建议传0
     */
    private int musicType;


    /**
     * 方控切源，1：方控  0：不是方控
     */
    private int isDirControl;

    /**
     * 是否可以播放，1：可以播放  0：不可以播放   歌曲是否有权限（歌曲列表中体现） 电台可以不管
     */
    private int isPlayable;

    /**
     *    歌曲名称  (电台需要传)
     */
    private String name;
    /**
     *演唱者(电台需要传)
     */
    private String artist;
    /**
     *    PLAY = 1;
     *    PAUSE = 0;
     *    NEXT = 2;
     *    PREVIOUS = 3;
     *    (电台需要传)
     */
    private int status;
    /**
     * @param picUrl   歌曲图片  (电台需要传)
     */
    private String picUrl;


    /**
     * 媒体总长度 秒
     */
    private int totalProgress;

    /**
     * @param currentProgress  播放进度 秒
     */
    private int currentProgress;

    protected IMediaBean(Parcel in) {
        trackId = in.readString();
        type = in.readInt();
        musicType = in.readInt();
        isDirControl = in.readInt();
        isPlayable = in.readInt();
        name = in.readString();
        artist = in.readString();
        status = in.readInt();
        picUrl = in.readString();
        totalProgress = in.readInt();
        currentProgress = in.readInt();
    }


//    public IMediaBean(int type, String name, String artist, int status, String picUrl, int totalProgress, int currentProgress) {
//        this.type = type;
//        this.name = name;
//        this.artist = artist;
//        this.status = status;
//        this.picUrl = picUrl;
//        this.totalProgress = totalProgress;
//        this.currentProgress = currentProgress;
//    }


    public IMediaBean(String trackId, int type, int musicType,int isDirControl,int isPlayable, String name, String artist, int status, String picUrl, int totalProgress, int currentProgress) {
        this.trackId = trackId;
        this.type = type;
        this.musicType = musicType;
        this.isDirControl = isDirControl;
        this.isPlayable = isPlayable;
        this.name = name;
        this.artist = artist;
        this.status = status;
        this.picUrl = picUrl;
        this.totalProgress = totalProgress;
        this.currentProgress = currentProgress;
    }

    public String getTrackId() {
        return trackId;
    }

    public void setTrackId(String trackId) {
        this.trackId = trackId;
    }

    public int getTotalProgress() {
        return totalProgress;
    }

    public void setTotalProgress(int totalProgress) {
        this.totalProgress = totalProgress;
    }

    public static final Creator<IMediaBean> CREATOR = new Creator<IMediaBean>() {
        @Override
        public IMediaBean createFromParcel(Parcel in) {
            return new IMediaBean(in);
        }

        @Override
        public IMediaBean[] newArray(int size) {
            return new IMediaBean[size];
        }
    };

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getArtist() {
        return artist;
    }

    public void setArtist(String artist) {
        this.artist = artist;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public int getCurrentProgress() {
        return currentProgress;
    }

    public void setCurrentProgress(int currentProgress) {
        this.currentProgress = currentProgress;
    }

    @Override
    public int describeContents() {
        return 0;
    }



    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(trackId);
        dest.writeInt(type);
        dest.writeInt(musicType);
        dest.writeInt(isDirControl);
        dest.writeInt(isPlayable);
        dest.writeString(name);
        dest.writeString(artist);
        dest.writeInt(status);
        dest.writeString(picUrl);
        dest.writeInt(totalProgress);
        dest.writeInt(currentProgress);
    }

    public int getMusicType() {
        return musicType;
    }

    public int getIsDirControl() {
        return isDirControl;
    }

    public void setMusicType(int musicType) {
        this.musicType = musicType;
    }

    public int getIsPlayable() {
        return isPlayable;
    }

    public void setIsPlayable(int isPlayable) {
        this.isPlayable = isPlayable;
    }


    @Override
    public String toString() {
        return "IMediaBean{" +
                "trackId='" + trackId + '\'' +
                ", type=" + type +
                ", musicType=" + musicType +
                ", isDirControl=" + isDirControl +
                ", isPlayable=" + isPlayable +
                ", name='" + name + '\'' +
                ", artist='" + artist + '\'' +
                ", status=" + status +
                ", picUrl='" + picUrl + '\'' +
                ", totalProgress=" + totalProgress +
                ", currentProgress=" + currentProgress +
                '}';
    }
}
