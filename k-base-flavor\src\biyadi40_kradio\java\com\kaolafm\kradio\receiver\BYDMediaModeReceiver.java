/*
 * Copyright (C) 2015 AutoRadio
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.kaolafm.kradio.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;


/******************************************
 * 类描述： 比亚迪mode切换应用广播监听
 * @time: 2018年04月19日10:13:34
 ******************************************/
public class BYDMediaModeReceiver extends BroadcastReceiver {

    public static final String MEDIA_MODE_ACTION = "byd.intent.action.MEDIA_MODE";

    @Override
    public void onReceive(Context context, Intent intent) {
        if (MEDIA_MODE_ACTION.equals(intent.getAction())) {
            String pkgName = intent.getStringExtra("pkgName");
            Log.i("BYDMediaModeReceiver","pkgName:"+pkgName);
            Log.i("BYDMediaModeReceiver","startactivity bydstartTest : "+pkgName);
            //比亚迪需求切换mode到APP之后需要进行播放。
            if (context.getPackageName().equals(pkgName)) {
//                Intent startIntent = new Intent(context, MainActivity.class);
//                context.startActivity(intent);
                Intent startIntent = context.getPackageManager().getLaunchIntentForPackage(context.getPackageName());
                if (startIntent != null) {
                    startIntent.putExtra(MEDIA_MODE_ACTION,MEDIA_MODE_ACTION);
                    context.startActivity(startIntent);
                }
            }
        }
    }
}
