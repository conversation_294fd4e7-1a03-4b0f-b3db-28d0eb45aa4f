package com.kaolafm.kradio.category.all;

import android.os.Bundle;
import android.util.Log;

import com.kaolafm.kradio.category.base.BaseTabFragment;
import com.kaolafm.kradio.category.base.TabContract;
import com.kaolafm.kradio.lib.bean.SubcategoryItemBean;
import java.util.List;

/**
 * <AUTHOR>
 **/
public class AllTabFragment extends BaseTabFragment {

    @Override
    public void showContent(String[] titles, List<SubcategoryItemBean> itemBeans, int showIndex) {

    }

    @Override
    protected int getLayoutId_Tow() {
        return 0;
    }

    @Override
    protected TabContract.IPresenter createPresenter() {
        return new AllTabPresenter(this);
    }

    @Override
    public void onEnterAnimationEnd(Bundle savedInstanceState) {
        Log.i("cate", "onEnterAnimationEnd: error=");
        super.onEnterAnimationEnd(savedInstanceState);
        mPresenter.loadData(-1);
    }
}
