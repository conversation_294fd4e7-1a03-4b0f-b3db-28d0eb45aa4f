package com.kaolafm.kradio.online.player.adapters;

import android.graphics.Color;
import android.graphics.drawable.AnimationDrawable;
import android.graphics.drawable.Drawable;
import androidx.constraintlayout.widget.Group;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.online.player.base.OnlineBasePlayListRvAdapter;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.player.logic.model.item.BroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.TVPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

import java.util.List;

public class BroadcastPlayListRvAdapter extends OnlineBasePlayListRvAdapter {
    public static final int PLAY_ITEM_NORMAL = 0;
    public static final int PLAY_ITEM_PLAYING = 1;
    public static final int PLAY_ITEM_RESERVE = 2;
    public static final int PLAY_ITEM_RESERVED = 3;
    public static final int PLAY_ITEM_NO_START = 4;
    private OnItemClickListener<PlayItem> unsubscribe;

    public BroadcastPlayListRvAdapter(List<PlayItem> dataList) {
        super(dataList);
    }

    @Override
    public void notifyAdapterPlayItemPaused(PlayItem playItem) {
        int position = findPlayItemPosition(playItem.getAudioId());
        if (position >= 0)
            notifyItemChanged(position, "pauseAnimation");
    }

    @Override
    public void notifyAdapterPlayItemResume(PlayItem playItem) {
        int position = findPlayItemPosition(playItem.getAudioId());
        if (position >= 0)
            notifyItemChanged(position, "resumeAnimation");
    }

    private int findPlayItemPosition(long audioId) {
        PlayItem mPlayItem;
        for (int i = 0; i < getDataList().size(); i++) {
            mPlayItem = getItemData(i);
            if (mPlayItem.getAudioId() == audioId) return i;
        }
        return -1;
    }

    @Override
    public void onBindViewHolder(BaseHolder<PlayItem> holder, int position, List<Object> payloads) {
        if (payloads == null || payloads.size() == 0) {
            super.onBindViewHolder(holder, position, payloads);
        } else {
            boolean isResume = "resumeAnimation".equals(payloads.get(0));
            ImageView mPlayingIv = null;
            if (holder instanceof PlayItemPlayingViewHolder) {
                mPlayingIv = ((PlayItemPlayingViewHolder) holder).ivPlaying;
            } else if (holder instanceof PlayItemNormalViewHolder) {
                mPlayingIv = ((PlayItemNormalViewHolder) holder).ivPlaying;
            }
            if (mPlayingIv == null) return;
            Drawable drawable = mPlayingIv.getDrawable();
            if (drawable instanceof AnimationDrawable) {
                AnimationDrawable animationDrawable = (AnimationDrawable) drawable;
                if (isResume && !animationDrawable.isRunning())
                    animationDrawable.start();
                else if (!isResume && animationDrawable.isRunning()) animationDrawable.stop();
            }
        }
    }

    @Override
    protected BaseHolder<PlayItem> getViewHolder(ViewGroup parent, int viewType) {
        BaseHolder<PlayItem> holder = null;
        switch (viewType) {
            case PLAY_ITEM_NORMAL:
                holder = new BroadcastPlayListRvAdapter.PlayItemNormalViewHolder(inflate(parent, R.layout.online_player_item_rv_play_item_normal, PLAY_ITEM_NORMAL));
                break;
            case PLAY_ITEM_PLAYING:
                holder = new BroadcastPlayListRvAdapter.PlayItemPlayingViewHolder(inflate(parent, R.layout.online_player_item_rv_play_item_playing, PLAY_ITEM_PLAYING));
                break;
            case PLAY_ITEM_RESERVE:
                holder = new BroadcastPlayListRvAdapter.PlayItemReserveViewHolder(inflate(parent, R.layout.online_player_item_rv_play_item_reserve, PLAY_ITEM_RESERVE));
                break;
            case PLAY_ITEM_RESERVED:
                holder = new BroadcastPlayListRvAdapter.PlayItemReservedViewHolder(inflate(parent, R.layout.online_player_item_rv_play_item_reserved, PLAY_ITEM_RESERVED));
                break;
            case PLAY_ITEM_NO_START:
                holder = new BroadcastPlayListRvAdapter.PlayItemReservedViewHolder(inflate(parent, R.layout.online_player_item_rv_play_item_no_start, PLAY_ITEM_RESERVED));
                break;

        }
        return holder;
    }

    public void setUnsubscribe(OnItemClickListener unsubscribe) {
        this.unsubscribe = unsubscribe;
    }

    @Override
    public int getItemViewType(int position) {
        PlayItem playItem = getItemData(position);
        switch (playItem.getStatus()) {
            case PlayerConstants.BROADCAST_STATUS_LIVING:
                return PLAY_ITEM_PLAYING;
            case PlayerConstants.BROADCAST_STATUS_NOT_ON_AIR:
                return PLAY_ITEM_NO_START;
//                if (playItem instanceof BroadcastPlayItem && ((BroadcastPlayItem) playItem).getInfoData().getIsLiked() == 1) {
//                    return PLAY_ITEM_RESERVED;
//                }
//                return PLAY_ITEM_RESERVE;
            case PlayerConstants.BROADCAST_STATUS_PLAYBACK:
            default:
                return PLAY_ITEM_NORMAL;
        }
    }

    private class PlayItemNormalViewHolder extends BaseHolder<PlayItem> {
        TextView tvItemTitle, tvItemStatus, tvAnchor, tvDuration, tvAnchorTip;
        ImageView ivPlaying;
        Group anchorView;

        public PlayItemNormalViewHolder(View itemView) {
            super(itemView);
            tvItemTitle = itemView.findViewById(R.id.tvItemTitle);
            tvItemStatus = itemView.findViewById(R.id.tvItemStatus);
            tvAnchor = itemView.findViewById(R.id.tvAnchor);
            tvAnchorTip = itemView.findViewById(R.id.subtitle);
            tvDuration = itemView.findViewById(R.id.tvDuration);
            ivPlaying = itemView.findViewById(R.id.ivPlaying);
            anchorView = itemView.findViewById(R.id.anchorView);
        }

        @Override
        public void setupData(PlayItem playItem, int position) {
            if (!(playItem instanceof BroadcastPlayItem) && !(playItem instanceof TVPlayItem))
                return;
            String titleValue = null, durationValue = null, anchorValue = null;
            if (playItem instanceof BroadcastPlayItem) {
                BroadcastPlayItem broadcast = (BroadcastPlayItem) playItem;
                titleValue = broadcast.getInfoData().getTitle();
                durationValue = String.format("%s-%s", subDurationStr(broadcast.getTimeInfoData().getBeginTime()), subDurationStr(broadcast.getTimeInfoData().getEndTime()));
                if (StringUtil.isEmpty(broadcast.getHost())) {
                    anchorValue = null;
                } else {
                    anchorValue = broadcast.getHost();
                }
                setTextColor(broadcast.getClassifyId() != 1);
            } else {
                TVPlayItem tvPlayItem = (TVPlayItem) playItem;
                titleValue = tvPlayItem.getInfoData().getTitle();
                durationValue = String.format("%s-%s", subDurationStr(tvPlayItem.getTimeInfoData().getBeginTime()), subDurationStr(tvPlayItem.getTimeInfoData().getEndTime()));
                if (StringUtil.isEmpty(tvPlayItem.getHost())) {
                    anchorValue = null;
                } else {
                    anchorValue = tvPlayItem.getHost();
                }
                setTextColor(true);
            }
            Drawable drawable = ivPlaying.getDrawable();
            if (BroadcastPlayListRvAdapter.this.selectPosition != position || !BroadcastPlayListRvAdapter.this.needInit) {
                tvItemTitle.setEllipsize(TextUtils.TruncateAt.END);
                ViewUtil.setViewVisibility(ivPlaying, View.GONE);
                if (drawable instanceof AnimationDrawable) {
                    ((AnimationDrawable) drawable).stop();
                }
                itemView.setBackgroundColor(Color.TRANSPARENT);
                tvItemStatus.setBackgroundResource(R.drawable.online_player_play_back);
            } else {
                tvItemTitle.setEllipsize(TextUtils.TruncateAt.MARQUEE);
                if (drawable instanceof AnimationDrawable) {
                    if (drawable instanceof AnimationDrawable) {
                        if (PlayerManagerHelper.getInstance().isPlaying())
                            ((AnimationDrawable) drawable).start();
                        else ((AnimationDrawable) drawable).stop();
                    }
                }
                ViewUtil.setViewVisibility(ivPlaying, View.VISIBLE);
                itemView.setBackgroundResource(R.drawable.online_player_sound_selected);
                tvItemStatus.setBackgroundResource(R.drawable.online_player_play_back_selected);
            }
            tvItemTitle.setText(titleValue);
            tvDuration.setText(durationValue);
            anchorView.setVisibility(anchorValue == null ? View.GONE : View.VISIBLE);
            tvAnchor.setText(anchorValue);
        }

        private void setTextColor(boolean isUnClick) {
            int titleColor, subtitleColor;
            if (isUnClick) {
                titleColor = ResUtil.getColor(R.color.online_player_broadcast_title_unclick);
                subtitleColor = ResUtil.getColor(R.color.online_player_broadcast_subtitle_unclick);
            } else {
                titleColor = ResUtil.getColor(R.color.online_player_broadcast_title);
                subtitleColor = ResUtil.getColor(R.color.online_player_broadcast_subtitle);
            }
            tvItemTitle.setTextColor(titleColor);
            tvDuration.setTextColor(titleColor);
            tvAnchor.setTextColor(subtitleColor);
            tvAnchorTip.setTextColor(subtitleColor);
            if (isUnClick)
                tvItemStatus.setVisibility(View.GONE);
            else tvItemStatus.setVisibility(View.VISIBLE);
        }
    }

    private class PlayItemPlayingViewHolder extends BaseHolder<PlayItem> {
        TextView tvItemTitle, tvItemStatus, tvAnchor, tvDuration;
        ImageView ivPlaying;
        Group anchorView;

        public PlayItemPlayingViewHolder(View itemView) {
            super(itemView);
            tvItemTitle = itemView.findViewById(R.id.tvItemTitle);
            tvItemStatus = itemView.findViewById(R.id.tvItemStatus);
            tvAnchor = itemView.findViewById(R.id.tvAnchor);
            tvDuration = itemView.findViewById(R.id.tvDuration);
            ivPlaying = itemView.findViewById(R.id.ivPlaying);
            anchorView = itemView.findViewById(R.id.anchorView);
        }

        @Override
        public void setupData(PlayItem playItem, int position) {
            if (!(playItem instanceof BroadcastPlayItem) && !(playItem instanceof TVPlayItem))
                return;
            String titleValue = null, durationValue = null, anchorValue = null;
            if (playItem instanceof BroadcastPlayItem) {
                BroadcastPlayItem broadcast = (BroadcastPlayItem) playItem;
                titleValue = broadcast.getInfoData().getTitle();
                durationValue = String.format("%s-%s", subDurationStr(broadcast.getTimeInfoData().getBeginTime()), subDurationStr(broadcast.getTimeInfoData().getEndTime()));
                if (StringUtil.isEmpty(broadcast.getHost())) {
                    anchorValue = null;
                } else {
                    anchorValue = broadcast.getHost();
                }
            } else {
                TVPlayItem tvPlayItem = (TVPlayItem) playItem;
                titleValue = tvPlayItem.getInfoData().getTitle();
                durationValue = String.format("%s-%s", subDurationStr(tvPlayItem.getTimeInfoData().getBeginTime()), subDurationStr(tvPlayItem.getTimeInfoData().getEndTime()));
                if (StringUtil.isEmpty(tvPlayItem.getHost())) {
                    anchorValue = null;
                } else {
                    anchorValue = tvPlayItem.getHost();
                }
            }

            tvItemTitle.setText(titleValue);
            tvDuration.setText(durationValue);

            //第一次渲染该列表，永远不显示背景
            //非第一次，如果选中的position与当前item的position相等，则显示背景，否则不显示
            Drawable drawable = ivPlaying.getDrawable();
            if (BroadcastPlayListRvAdapter.this.selectPosition != position || !BroadcastPlayListRvAdapter.this.needInit) {
                tvItemTitle.setEllipsize(TextUtils.TruncateAt.END);
                ViewUtil.setViewVisibility(ivPlaying, View.GONE);
                if (drawable instanceof AnimationDrawable) {
                    ((AnimationDrawable) drawable).stop();
                }
                itemView.setBackgroundColor(Color.TRANSPARENT);
            } else {
                tvItemTitle.setEllipsize(TextUtils.TruncateAt.MARQUEE);
                if (drawable instanceof AnimationDrawable) {
                    if (drawable instanceof AnimationDrawable) {
                        if (PlayerManagerHelper.getInstance().isPlaying())
                            ((AnimationDrawable) drawable).start();
                        else ((AnimationDrawable) drawable).stop();
                    }
                }
                ViewUtil.setViewVisibility(ivPlaying, View.VISIBLE);
                itemView.setBackgroundResource(R.drawable.online_player_sound_selected);
            }
            anchorView.setVisibility(anchorValue == null ? View.GONE : View.VISIBLE);
            tvAnchor.setText(anchorValue);
        }
    }

    private class PlayItemReserveViewHolder extends BaseHolder<PlayItem> {
        TextView tvItemTitle, tvAnchor, tvDuration, subscribeTv;
        View subscribeClickView;
        Group anchorView;

        public PlayItemReserveViewHolder(View itemView) {
            super(itemView);
            tvItemTitle = itemView.findViewById(R.id.tvItemTitle);
            tvItemTitle.setEllipsize(TextUtils.TruncateAt.END);
            tvAnchor = itemView.findViewById(R.id.tvAnchor);
            tvDuration = itemView.findViewById(R.id.tvDuration);
            subscribeTv = itemView.findViewById(R.id.subscribeTv);
            subscribeClickView = itemView.findViewById(R.id.subscribeClickView);
            anchorView = itemView.findViewById(R.id.anchorView);
            subscribeClickView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    // 触发订阅
                    if (unsubscribe != null) {
                        unsubscribe.onItemClick(v, -1, getItemData(getAdapterPosition()), getAdapterPosition());
                    }
                }
            });
        }

        @Override
        public void setupData(PlayItem playItem, int position) {
            if (!(playItem instanceof BroadcastPlayItem) && !(playItem instanceof TVPlayItem))
                return;

            String titleValue = null, durationValue = null, anchorValue = null;
            if (playItem instanceof BroadcastPlayItem) {
                BroadcastPlayItem broadcast = (BroadcastPlayItem) playItem;
                titleValue = broadcast.getInfoData().getTitle();
                durationValue = String.format("%s-%s", subDurationStr(broadcast.getTimeInfoData().getBeginTime()), subDurationStr(broadcast.getTimeInfoData().getEndTime()));
                if (StringUtil.isEmpty(broadcast.getHost())) {
                    anchorValue = null;
                } else {
                    anchorValue = broadcast.getHost();
                }
            } else {
                TVPlayItem tvPlayItem = (TVPlayItem) playItem;
                titleValue = tvPlayItem.getInfoData().getTitle();
                durationValue = String.format("%s-%s", subDurationStr(tvPlayItem.getTimeInfoData().getBeginTime()), subDurationStr(tvPlayItem.getTimeInfoData().getEndTime()));
                if (StringUtil.isEmpty(tvPlayItem.getHost())) {
                    anchorValue = null;
                } else {
                    anchorValue = tvPlayItem.getHost();
                }
            }
            tvItemTitle.setText(titleValue);
            tvDuration.setText(durationValue);
            anchorView.setVisibility(anchorValue == null ? View.GONE : View.VISIBLE);
            tvAnchor.setText(anchorValue);
        }
    }

    private class PlayItemReservedViewHolder extends BaseHolder<PlayItem> {
        TextView tvItemTitle, tvAnchor, tvDuration;
        Group anchorView;

        public PlayItemReservedViewHolder(View itemView) {
            super(itemView);
            tvItemTitle = itemView.findViewById(R.id.tvItemTitle);
            tvItemTitle.setEllipsize(TextUtils.TruncateAt.END);
            tvAnchor = itemView.findViewById(R.id.tvAnchor);
            tvDuration = itemView.findViewById(R.id.tvDuration);
            anchorView = itemView.findViewById(R.id.anchorView);
        }

        @Override
        public void setupData(PlayItem playItem, int position) {
            if (!(playItem instanceof BroadcastPlayItem) && !(playItem instanceof TVPlayItem))
                return;

            String titleValue = null, durationValue = null, anchorValue = null;
            if (playItem instanceof BroadcastPlayItem) {
                BroadcastPlayItem broadcast = (BroadcastPlayItem) playItem;
                titleValue = broadcast.getInfoData().getTitle();
                durationValue = String.format("%s-%s", subDurationStr(broadcast.getTimeInfoData().getBeginTime()), subDurationStr(broadcast.getTimeInfoData().getEndTime()));
                if (StringUtil.isEmpty(broadcast.getHost())) {
                    anchorValue = null;
                } else {
                    anchorValue = broadcast.getHost();
                }
            } else {
                TVPlayItem tvPlayItem = (TVPlayItem) playItem;
                titleValue = tvPlayItem.getInfoData().getTitle();
                durationValue = String.format("%s-%s", subDurationStr(tvPlayItem.getTimeInfoData().getBeginTime()), subDurationStr(tvPlayItem.getTimeInfoData().getEndTime()));
                if (StringUtil.isEmpty(tvPlayItem.getHost())) {
                    anchorValue = null;
                } else {
                    anchorValue = tvPlayItem.getHost();
                }
            }
            tvItemTitle.setText(titleValue);
            tvDuration.setText(durationValue);
            anchorView.setVisibility(anchorValue == null ? View.GONE : View.VISIBLE);
            tvAnchor.setText(anchorValue);
        }
    }

    /**
     * 将01:00:00截取成01:00的格式
     *
     * @param durationStr
     * @return
     */
    private String subDurationStr(String durationStr) {
        if (durationStr.contains(":"))
            return durationStr.substring(0, durationStr.lastIndexOf(":"));
        else return durationStr;
    }
}
