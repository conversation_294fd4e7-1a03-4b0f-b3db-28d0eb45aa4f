package com.kaolafm.kradio.lib.base.lifecycle;

import android.app.Activity;
import android.app.Application;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager.FragmentLifecycleCallbacks;
import android.util.Log;

import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.base.mvp.IActivity;

import org.greenrobot.eventbus.EventBus;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2018/4/13
 */

public class ActivityLifecycle implements Application.ActivityLifecycleCallbacks {

    private static final String TAG = "ActivityLifecycle";

    private final AppManager mAppManager;

    private FragmentLifecycle mFragmentLifecycle;

    private List<? extends FragmentLifecycleCallbacks> mFragmentLifecycles;

    private volatile Set<String> mForegroundActivitySet = Collections.synchronizedSet(new HashSet<>());

    public ActivityLifecycle(Application application) {
        mAppManager = AppManager.getInstance();
        if (mForegroundActivitySet == null) {
            mForegroundActivitySet = Collections.synchronizedSet(new HashSet<>());
        }
    }

    @Override
    public void onActivityPreCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {
        Log.i(TAG, "-------------------" + activity.getClass().getSimpleName() + " onCreate Start (onActivityPreCreated) -------------------");
    }

    @Override
    public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
        //如果 intent 包含了此字段,并且为 true 说明不加入到 list 进行统一管理
        Log.i(TAG, "-------------------" + activity.getClass().getSimpleName() + " onCreate Finished (onActivityCreated) -------------------");
        boolean isNotAdd = false;
        if (activity.getIntent() != null) {
            try {
                isNotAdd = activity.getIntent().getBooleanExtra(AppManager.IS_NOT_ADD_ACTIVITY_LIST, false);
            } catch (Exception e) {
            }
        }

        if (!isNotAdd) {
            mAppManager.addActivity(activity);
        }

        if (activity instanceof IActivity) {
            if (((IActivity) activity).useEventBus()) {
                EventBus.getDefault().register(activity);
            }
        }

        registerFragmentCallbacks(activity);
    }

    @Override
    public void onActivityPreStarted(@NonNull Activity activity) {
        Log.i(TAG, "-------------------" + activity.getClass().getSimpleName() + " onStart Start (onActivityPreStarted) -------------------");
    }

    @Override
    public void onActivityStarted(Activity activity) {
        Log.i(TAG, "-------------------" + activity.getClass().getSimpleName() + " onStart Finished (onActivityStarted) -------------------");
        if (mForegroundActivitySet != null) {
            mForegroundActivitySet.add(activity.getClass().getSimpleName());
            Log.d(TAG, "activity set size " + mForegroundActivitySet.size());
        }
    }

    @Override
    public void onActivityPreResumed(@NonNull Activity activity) {
        Log.i(TAG, "-------------------" + activity.getClass().getSimpleName() + " onResume Start (onActivityPreResumed) -------------------");
        if (mForegroundActivitySet != null) {
            mForegroundActivitySet.add(activity.getClass().getSimpleName());
            Log.d(TAG, "activity set size " + mForegroundActivitySet.size());
        }
    }

    @Override
    public void onActivityResumed(Activity activity) {
        mAppManager.setCurrentActivity(activity);
        Log.i(TAG, "-------------------" + activity.getClass().getSimpleName() + " onResume Finished (onActivityResumed) -------------------");
    }

    @Override
    public void onActivityPrePaused(@NonNull Activity activity) {
        Log.i(TAG, "-------------------" + activity.getClass().getSimpleName() + " onPause Start (onActivityPrePaused) -------------------");
    }

    @Override
    public void onActivityPaused(Activity activity) {
        Log.i(TAG, "-------------------" + activity.getClass().getSimpleName() + " onPause Finished (onActivityPaused) -------------------");
        if (mForegroundActivitySet != null) {
            mForegroundActivitySet.remove(activity.getClass().getSimpleName());
            Log.d(TAG, "activity set size " + mForegroundActivitySet.size());
        }
    }

    @Override
    public void onActivityPreStopped(@NonNull Activity activity) {
        Log.i(TAG, "-------------------" + activity.getClass().getSimpleName() + " onStop Start (onActivityPreStopped) -------------------");
    }

    @Override
    public void onActivityStopped(Activity activity) {
        if (mAppManager.getCurrentActivity() == activity) {
            mAppManager.setCurrentActivity(null);
        }
        // 有HubActivity不走pause直接stop的情况，所以在stop再做一次判断，去掉此处会影响应用是否在前台的判断
        Log.i(TAG, "-------------------" + activity.getClass().getSimpleName() + " onStop Finished (onActivityStopped) -------------------");
        if (mForegroundActivitySet != null) {
            mForegroundActivitySet.remove(activity.getClass().getSimpleName());
            Log.d(TAG, "activity set size " + mForegroundActivitySet.size());
        }
    }

    @Override
    public void onActivityPreSaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {
        Log.i(TAG, "-------------------" + activity.getClass().getSimpleName() + " onSaveInstanceState Start (onActivityPreSaveInstanceState) -------------------");
    }

    @Override
    public void onActivitySaveInstanceState(Activity activity, Bundle outState) {
        Log.i(TAG, "-------------------" + activity.getClass().getSimpleName() + " onSaveInstanceState Finished (onActivitySaveInstanceState) -------------------");
    }

    @Override
    public void onActivityPreDestroyed(@NonNull Activity activity) {
        Log.i(TAG, "-------------------" + activity.getClass().getSimpleName() + " onDestroy Start (onActivityPreDestroyed) -------------------");
    }

    @Override
    public void onActivityDestroyed(Activity activity) {
        if (activity instanceof IActivity) {
            if (((IActivity) activity).useEventBus()) {
                EventBus.getDefault().unregister(activity);
            }
        }
        mAppManager.removeActivity(activity);
        Log.i(TAG, "-------------------" + activity.getClass().getSimpleName() + " onDestroy Finished (onActivityDestroyed) -------------------");
    }

    @Override
    public void onActivityPostResumed(@NonNull Activity activity) {
        Log.i(TAG, "-------------------" + activity.getClass().getSimpleName() + " onPostResume Finished (onActivityPostResumed) -------------------");
    }

    @Override
    public void onActivityPostCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {
        Log.i(TAG, "-------------------" + activity.getClass().getSimpleName() + " onPostCreate Finished (onActivityPostCreated) -------------------");
    }
    public boolean isAppForeground() {
        int size = 0;
        if (mForegroundActivitySet != null) {
            size = mForegroundActivitySet.size();
        }
        return size > 0;
    }

    private void registerFragmentCallbacks(Activity activity) {
        boolean useFragment = !(activity instanceof IActivity) || ((IActivity) activity).useFragment();
        if (activity instanceof FragmentActivity && useFragment) {

            if (mFragmentLifecycle == null) {
                mFragmentLifecycle = new FragmentLifecycle();
            }

            ((FragmentActivity) activity).getSupportFragmentManager().registerFragmentLifecycleCallbacks(mFragmentLifecycle, true);

//            for (FragmentManager.FragmentLifecycleCallbacks fragmentLifecycle : mFragmentLifecycles) {
//                ((FragmentActivity) activity).getSupportFragmentManager().registerFragmentLifecycleCallbacks(fragmentLifecycle, true);
//            }
        }
    }
}
