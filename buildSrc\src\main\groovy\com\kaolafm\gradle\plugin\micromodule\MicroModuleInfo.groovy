package com.kaolafm.gradle.plugin.micromodule

import com.kaolafm.gradle.plugin.Util
import org.gradle.api.GradleException
import org.gradle.api.Project

class MicroModuleInfo {

    Project project
    MicroModule mainMicroModule
    Map<String, MicroModule> includeMicroModules
    Map<String, String> exportMicroModules

    Digraph<String> dependencyGraph

    MicroModuleInfo(Project project) {
        this.project = project
        this.includeMicroModules = new HashMap<>()
        this.exportMicroModules = new HashMap<>()
        dependencyGraph = new Digraph<String>()

        MicroModule microModule = Util.buildMicroModule(project, ':main')
        setMainMicroModule(microModule)
    }

    /**
     * 添加main module
     * @param microModule
     */
    void setMainMicroModule(MicroModule microModule) {
        if (microModule == null) {
            throw new GradleException("main MicroModule cannot be null.")
        }
        this.mainMicroModule = microModule
        addIncludeMicroModule(microModule)
    }
    /**
     * 添加包含的module
     * @param microModule
     */
    void addIncludeMicroModule(MicroModule microModule) {
        if (microModule == null) {
            throw new GradleException("MicroModule with path '${name}' could not be found in ${project.getDisplayName()}.")
        }
        includeMicroModules.put(microModule.name, microModule)
    }

    /**
     * 添加排除的module
     * @param name
     */
    void addExportMicroModule(String name) {
        MicroModule microModule = Util.buildMicroModule(project, name)
        if (microModule == null) {
            throw new GradleException("MicroModule with path '${name}' could not be found in ${project.getDisplayName()}.")
        }
        exportMicroModules.put(name, null)
    }

    MicroModule getMicroModule(String name) {
        return includeMicroModules.get(name)
    }

    /**
     * 对module设置其他module的依赖
     * @param target
     * @param dependency
     */
    void setMicroModuleDependency(String target, String dependency) {
        MicroModule dependencyMicroModule = getMicroModule(dependency)
        if(dependencyMicroModule == null) {
            if(Util.buildMicroModule(project, dependency) != null) {
                throw new GradleException("MicroModule '${target}' dependency MicroModle '${dependency}', but its not included.")
            } else {
                throw new GradleException("MicroModule with path '${path}' could not be found in ${project.getDisplayName()}.")
            }
        }

        dependencyGraph.add(target, dependency)
        if(!dependencyGraph.isDag()) {
            throw new GradleException("Circular dependency between MicroModule '${target}' and '${dependency}'.")
        }
    }

    boolean hasDependency(String target, String dependency) {
        Map<String, Integer> bfsDistance = dependencyGraph.bfsDistance(target)
        for(String key: bfsDistance.keySet()) {
            if(key == dependency) {
                return bfsDistance.get(key) != null
            }
        }
        return false
    }

}