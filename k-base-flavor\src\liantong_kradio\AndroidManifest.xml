<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.kaolafm.kradio.flavor">

    <application>

        <activity
            android:name="com.kaolafm.auto.home.MainActivity"
            android:configChanges="orientation|screenSize|keyboardHidden|locale|layoutDirection|keyboard|uiMode"
            android:exported="true"
            android:screenOrientation="unspecified"
            android:theme="@style/ComprehensiveAppThemeCompat.Splash">
            <meta-data
                android:name="distractionOptimized"
                android:value="true" />
        </activity>
        <activity-alias xmlns:tools="http://schemas.android.com/tools"
            android:name="com.main.activity.alias"
            android:targetActivity="com.kaolafm.auto.home.MainActivity"
            tools:node="replace">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.MONKEY" />
            </intent-filter>
        </activity-alias>

    </application>
</manifest>


