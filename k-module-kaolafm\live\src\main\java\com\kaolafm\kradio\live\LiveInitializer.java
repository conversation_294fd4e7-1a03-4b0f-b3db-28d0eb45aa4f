package com.kaolafm.kradio.live;

import android.app.Application;

import com.kaolafm.kradio.lib.init.AppInit;
import com.kaolafm.kradio.lib.init.BaseAppInitializer;
import com.kaolafm.kradio.lib.init.Process;
import com.kaolafm.kradio.lib.utils.YTLogUtil;

/**
 * <AUTHOR>
 * @date 2019-09-17
 */
@AppInit(priority = 10, description = "直播初始化", process = Process.ALL)
public class LiveInitializer extends BaseAppInitializer {

    /**
     * 腾讯云直播授权信息
     * https://license.vod2.myqcloud.com/license/v2/1306279282_1/v_cube.license
     * 946221ac4a96178b1de8175aa3b80259
     * 管理接口的ak和sk
     * AKIDJRO5VeW7RwA8pEMDn7AxTifLktmOjna5
     * FDuKTLwGK9gwvd4E7j47tObXCahW47tO
     */
    @Override
    public void onCreate(Application application) {
        YTLogUtil.logStart("LiveInitializer", "onCreate", "start");
        // 解决https://app.huoban.com/tables/2100000007544849/items/2300001235666841?userId=1545533问题
        YTLogUtil.logStart("LiveInitializer", "onCreate", "end");
    }
}
