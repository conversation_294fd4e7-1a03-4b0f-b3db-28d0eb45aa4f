package com.kaolafm.kradio.lib.base.flavor.player;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-02-06 08:20
 ******************************************/

public interface KLOnAudioFocusChangeListener {
    /**
     * 处理AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK事件
     *
     * @param preFocusChange 上一音频焦点状态
     * @return true事件被处理，false事件未处理
     */
    boolean onAudioFocusDuck(int preFocusChange);

    /**
     * 提前处理AudioManager.AUDIOFOCUS_GAIN
     *
     * @param preFocusChange 上一音频焦点状态
     * @return true事件被处理，false事件未处理
     */
    boolean beforeOnAudioFocusGain(int preFocusChange);

    /**
     * 处理AudioManager.AUDIOFOCUS_GAIN
     *
     * @param preFocusChange 上一音频焦点状态
     * @return true事件被处理，false事件未处理
     */
    boolean onAudioFocusGain(int preFocusChange);

    /**
     * 处理AudioManager.AUDIOFOCUS_LOSS或AudioManager.AUDIOFOCUS_LOSS_TRANSIENT
     *
     * @param preFocusChange 上一音频焦点状态
     * @return true事件被处理，false事件未处理
     */
    boolean onAudioFocusLoss(int preFocusChange);

    /**
     * 处理申请音频焦点失败逻辑
     *
     * @return true事件被处理，false事件未处理
     */
    boolean onRequestAudioFocusFailed();
}