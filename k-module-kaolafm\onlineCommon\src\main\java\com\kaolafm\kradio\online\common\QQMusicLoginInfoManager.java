package com.kaolafm.kradio.online.common;

import android.content.Context;

import com.google.gson.Gson;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.DateUtil;
import com.kaolafm.kradio.lib.utils.SharedPreferenceUtil;
import com.kaolafm.opensdk.account.token.AccessTokenManager;
import com.kaolafm.opensdk.account.token.QQMusicAccessToken;
import com.kaolafm.opensdk.api.music.qq.model.TencentUserInfo;
import com.kaolafm.opensdk.api.music.qq.model.VipInfo;

/**
 * QQ音乐登录信息管理类。
 *
 * <AUTHOR>
 * @date 2018/5/4
 */

public class QQMusicLoginInfoManager {

    private static final String SP_FILE_NAME = "QQMusicLoginInfo";

    private static final String SP_KEY_LOGIN_TYPE = "loginType";

    private static final String SP_KEY_MY_LIKE_ID = "myLikeId";

    private static final String SP_KEY_USER_INFO = "userInfo";

    private static final String SP_KEY_VIP_INFO = "VIPInfo";

    private static class QQMusicLoginInfoManagerHolder {

        private static final QQMusicLoginInfoManager INSTANCE = new QQMusicLoginInfoManager();
    }


    /***
     * QQ音乐登录用户信息
     */
    private TencentUserInfo mUserInfo;

    private long mMyLikeId = 0;

    private SharedPreferenceUtil mSPUtil;

    private VipInfo mVipInfo;

    public static QQMusicLoginInfoManager getInstance() {
        return QQMusicLoginInfoManagerHolder.INSTANCE;
    }

    private QQMusicLoginInfoManager() {
        init();
    }

    public void setLoginType(int loginType) {
        mSPUtil.putInt(SP_KEY_LOGIN_TYPE, loginType);
    }

    public int getLoginType() {
        return mSPUtil.getInt(SP_KEY_LOGIN_TYPE, -1);
    }

    private void init() {
        mSPUtil = SharedPreferenceUtil
                .newInstance(AppDelegate.getInstance().getContext(), SP_FILE_NAME, Context.MODE_PRIVATE);
        mUserInfo = getInfo();
    }

    /**
     * 保存用户信息
     */
    public void saveInfo(TencentUserInfo userInfo) {
        if (userInfo == null) {
            return;
        }
        if (mSPUtil == null) {
            mSPUtil = SharedPreferenceUtil
                    .newInstance(AppDelegate.getInstance().getContext(), SP_FILE_NAME, Context.MODE_PRIVATE);
        }
        mUserInfo = userInfo;
        mSPUtil.putString(SP_KEY_USER_INFO, new Gson().toJson(userInfo));
    }

    /**
     * 保存VIP信息
     */
    public void saveVipInfo(VipInfo vipInfo) {
        mVipInfo = vipInfo;
        //会员
        if (1 == vipInfo.getVipFlag()) {
            vipInfo.setVipTimeLeft(
                    DateUtil.millis2Day(DateUtil.string2Millis(vipInfo.getEndTime()) - DateUtil.getServerTime()));
        }
        mSPUtil.putString(SP_KEY_VIP_INFO, new Gson().toJson(vipInfo));
    }

    public VipInfo getVipInfo() {
        if (mVipInfo == null) {
            mVipInfo = new Gson().fromJson(mSPUtil.getString(SP_KEY_VIP_INFO, ""), VipInfo.class);
        }
        return mVipInfo == null ? new VipInfo() : mVipInfo;
    }

    public TencentUserInfo getInfo() {
        if (mUserInfo == null) {
            mUserInfo = new Gson().fromJson(mSPUtil.getString(SP_KEY_USER_INFO, ""), TencentUserInfo.class);
        }
        return mUserInfo == null ? new TencentUserInfo() : mUserInfo;
    }

    public void setUserInfo(TencentUserInfo userInfo) {
        mUserInfo = userInfo;
    }

    public void setMyLikeId(long dissId) {
        mMyLikeId = dissId;
        if (mSPUtil == null) {
            mSPUtil = SharedPreferenceUtil
                    .newInstance(AppDelegate.getInstance().getContext(), SP_FILE_NAME, Context.MODE_PRIVATE);
        }
        mSPUtil.putLong(SP_KEY_MY_LIKE_ID, dissId);
    }

    public long getMyLikeId() {
        if (mSPUtil == null) {
            mSPUtil = SharedPreferenceUtil
                    .newInstance(AppDelegate.getInstance().getContext(), SP_FILE_NAME, Context.MODE_PRIVATE);
        }
        return mMyLikeId != 0 ? mMyLikeId : mSPUtil.getLong(SP_KEY_MY_LIKE_ID, 0);
    }

    public void clearInfo() {
        mSPUtil.remove(SP_KEY_MY_LIKE_ID, SP_KEY_USER_INFO, SP_KEY_VIP_INFO);
        //上一次的登录方法需要保留。
        mUserInfo = null;
        mVipInfo = null;
        mMyLikeId = 0;
    }

    /**
     * 是否登录
     *
     * @return true登录，false未登录
     */
    public boolean isLogin() {
        QQMusicAccessToken qqMusicAccessToken = AccessTokenManager.getInstance().getQQMusicAccessToken();
        return qqMusicAccessToken != null && qqMusicAccessToken.isLogin();
    }
}
