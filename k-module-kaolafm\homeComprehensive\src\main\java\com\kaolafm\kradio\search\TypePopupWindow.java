package com.kaolafm.kradio.search;

import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Build;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.kaolafm.kradio.k_kaolafm.BuildConfig;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.flavor.KRadioC211ViewSizeInter;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.MultiUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.util.ReportConstants;

import java.util.List;

/**
 * 搜索类型弹出窗
 *
 * <AUTHOR>
 */
public class TypePopupWindow extends PopupWindow {

    private RecyclerView mRvType;

    private OnClickListener mOnClickListener;
    private GridLayoutManager layoutManager;
    private SearchTypeAdapter searchTypeAdapter;
    private View view;
    private LinearLayout mTypeLoading;
    private ViewStub mSearchTypeNetworkError;
    RelativeLayout mSearchTypeNetworkErrorLayout;
    private String pageId = Constants.PAGE_ID_SEARCH;

    public TypePopupWindow(Context context) {
        super(context);
        init(context);
    }

    private void init(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            setElevation(16);
        }
        setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_UNSPECIFIED);
        view = LayoutInflater.from(context).inflate(R.layout.layout_search_type, null);
//        setBackgroundDrawable(ResUtil.getDrawable(R.drawable.bg_item_corner));
        setBackgroundDrawable(new ColorDrawable(Color.parseColor("#00000000")));
        setContentView(view);
        setOutsideTouchable(true);
        setFocusable(true);
        mTypeLoading = view.findViewById(R.id.search_type_loading);
        mRvType = view.findViewById(R.id.rv_search_type);
        mSearchTypeNetworkError = view.findViewById(R.id.vs_search_type_network_error);

        layoutManager = new GridLayoutManager(context, getSpanCount(),
                GridLayoutManager.VERTICAL, false);
        mRvType.setLayoutManager(layoutManager);
        mRvType.addItemDecoration(new SearchTypeGridItemDecoration(getItemDecorationHorizontal(), ResUtil.getDimen(R.dimen.y30)));
        searchTypeAdapter = new SearchTypeAdapter();
        searchTypeAdapter.setOnItemClickListener(new BaseAdapter.OnItemClickListener<SearchType>() {
            @Override
            public void onItemClick(View view, int viewType, SearchType searchType, int position) {
                List<SearchType> searchTypeList = searchTypeAdapter.getDataList();
                for (int i = 0; i < searchTypeList.size(); i++) {
                    SearchType st = searchTypeList.get(i);
                    if (i == position && st.isSelect() == true) {
                        dismiss();
                        return;
                    }
                    if (st.isSelect() == true) {
                        st.setSelect(false);
                        searchTypeAdapter.notifyItemChanged(i);
                    }
                    if (i == position) {
                        st.setSelect(true);
                        searchTypeAdapter.notifyItemChanged(i);
                    }
                }
                mOnClickListener.onClick(searchType);
                ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_SEARCH_CATEGORY_LIST_ITEM
                        , searchType.getTypeName(), getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
                dismiss();
            }
        });
        mRvType.setAdapter(searchTypeAdapter);
        changePopupView(ResUtil.getOrientation());
    }

    public void setData(List<SearchType> searchTypes) {
        ViewUtil.setViewVisibility(mTypeLoading, View.GONE);
        if (null != mSearchTypeNetworkErrorLayout) {
            ViewUtil.setViewVisibility(mSearchTypeNetworkErrorLayout, View.GONE);
        }
        SearchTypeAdapter searchTypeAdapter = (SearchTypeAdapter) mRvType.getAdapter();
        searchTypeAdapter.setDataList(searchTypes);
    }

    public void addOnClickListener(OnClickListener onClickListener) {
        mOnClickListener = onClickListener;
    }

    public void setPageId(String pageId) {
        this.pageId = pageId;
        if (searchTypeAdapter != null) {
            searchTypeAdapter.setPageId(pageId);
        }
    }

    public String getPageId() {
        return pageId;
    }

    public interface OnClickListener {
        void onClick(SearchType searchType);
    }

    public void changePopupView(int orientation) {
        if (orientation == Configuration.ORIENTATION_PORTRAIT || MultiUtil.getMultiStatus()) {
//            setWidth(ResUtil.getDimen(R.dimen.x424));
//            setHeight(ResUtil.getDimen(R.dimen.y517));

            layoutManager.setSpanCount(2);
            changeItemDecoration(ResUtil.getDimen(R.dimen.x20), ResUtil.getDimen(R.dimen.y26));
            mRvType.setLayoutManager(layoutManager);
        } else {
            KRadioC211ViewSizeInter inter = ClazzImplUtil.getInter("KRadioC211ViewSizeImpl");
            if (inter != null && inter.isNeedReset()) {
                layoutManager.setSpanCount(7);
            } else {
//                setWidth(ResUtil.getDimen(R.dimen.x905));
                setHeight(ResUtil.getDimen(R.dimen.y293));
                layoutManager.setSpanCount(4);
            }
            changeItemDecoration(ResUtil.getDimen(R.dimen.x30), ResUtil.getDimen(R.dimen.y30));
            mRvType.setLayoutManager(layoutManager);
        }

        ViewGroup.MarginLayoutParams marginLayoutParams = (ViewGroup.MarginLayoutParams) mRvType.getLayoutParams();
        marginLayoutParams.topMargin = ResUtil.getDimen(R.dimen.search_type_spinner_recycleview_top);
        marginLayoutParams.bottomMargin = ResUtil.getDimen(R.dimen.search_type_spinner_recycleview_bottom);
        if (mRvType != null) {
            RecyclerView.Adapter adapter = mRvType.getAdapter();
            RecyclerView.LayoutManager manager = mRvType.getLayoutManager();
            mRvType.setAdapter(null);
            mRvType.setLayoutManager(null);
            mRvType.getRecycledViewPool().clear();
            mRvType.setLayoutManager(manager);
            mRvType.setAdapter(adapter);
        }
//        searchTypeAdapter.notifyDataSetChanged();
    }

    private void changeItemDecoration(int mHorizontalDivider, int mVerticalDivider) {
        RecyclerView.ItemDecoration itemDecoration = mRvType.getItemDecorationAt(0);
        if (itemDecoration != null && itemDecoration instanceof SearchTypeGridItemDecoration) {
            ((SearchTypeGridItemDecoration) itemDecoration).setmHorizontalDivider(mHorizontalDivider);
            ((SearchTypeGridItemDecoration) itemDecoration).setmVerticalDivider(mVerticalDivider);
        }
    }

    public void showNetworkError(String error) {
        if (mSearchTypeNetworkErrorLayout == null) {
            mSearchTypeNetworkErrorLayout = (RelativeLayout) mSearchTypeNetworkError.inflate();
            TextView tvNetworkNoSign = mSearchTypeNetworkErrorLayout.findViewById(R.id.tv_network_nosign);
            tvNetworkNoSign.setText(error);
        }
        ViewUtil.setViewVisibility(mSearchTypeNetworkErrorLayout, View.VISIBLE);
        if (mSearchTypeNetworkErrorLayout != null) {
            TextView tvNetworkNosign = mSearchTypeNetworkErrorLayout.findViewById(R.id.tv_network_nosign);
            String text = null;
            if (tvNetworkNosign != null) {
                text = tvNetworkNosign.getText().toString();
            }
            ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_NETWORK_RETRY, text, getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
        }
    }

    private int getSpanCount() {
        if (BuildConfig.LAYOUT_TYPE == 1) return 4;
        return 5;
    }

    private int getItemDecorationHorizontal() {
        if (BuildConfig.LAYOUT_TYPE == 1) return ResUtil.getDimen(R.dimen.x38);
        return ResUtil.getDimen(R.dimen.x46);
    }
}
