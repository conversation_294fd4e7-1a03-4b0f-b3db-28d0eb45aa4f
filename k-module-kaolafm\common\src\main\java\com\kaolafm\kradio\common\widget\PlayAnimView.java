package com.kaolafm.kradio.common.widget;

import android.animation.TimeInterpolator;
import android.animation.ValueAnimator;
import android.animation.ValueAnimator.AnimatorUpdateListener;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Paint.Style;
import android.graphics.Shader.TileMode;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.View;
import android.view.animation.LinearInterpolator;

import com.kaolafm.kradio.k_kaolafm.R.styleable;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

public class PlayAnimView extends View {
    private Paint mPaint;

    //边框线宽
    private float mStrokeWidth;
    //内容部分占据组件宽度的比例
    private float mRatioWidth;
    //线最大高,自动计算
    private int mMaxHeight;
    //线最小高，自动计算
    private int mMinHeight;
    //每条线的宽度，自动计算
    private int mLineWidth;
    //线的总条数
    private int mLineCount;
    //两边线距离左右两侧的额外padding，自动计算，防止出现组件宽度无法整除线数导致的偏移
    private int mLinePadding;
    //圆角半径，自动计算
    private float mRadius;
    //每条线的初始高度,自动计算
    private final List<Float> mOriginHeightList;
    //线条的开始颜色
    private int mFullStartColor;
    //线条的结束颜色
    private int mFullEndColor;
    //边框的开始颜色
    private int mStrokeStartColor;
    //边框的中间颜色
    private int mStrokeCenterColor;
    //边框的结束颜色
    private int mStrokeEndColor;

    private LinearGradient mFillLineLinearGradient;
    private LinearGradient mStrokeLineLinearGradient;
    private Matrix mMatrix;

    private float mProgress;
    private Drawable mBgBitmap;
    private ValueAnimator mValueAnimator;

    private void initProperties(Context context, AttributeSet attrs) {
        TypedArray a = context.obtainStyledAttributes(attrs, styleable.PlayAnimView);
        if (a.hasValue(styleable.PlayAnimView_playStrokeWidth)) {
            this.mStrokeWidth = a.getDimension(styleable.PlayAnimView_playStrokeWidth, 2.0F);
        }

        if (a.hasValue(styleable.PlayAnimView_playFullStartColor)) {
            this.mFullStartColor = a.getColor(styleable.PlayAnimView_playFullStartColor, Color.BLACK);
        }

        if (a.hasValue(styleable.PlayAnimView_playFullEndColor)) {
            this.mFullEndColor = a.getColor(styleable.PlayAnimView_playFullEndColor, Color.BLACK);
        }

        if (a.hasValue(styleable.PlayAnimView_playStrokeStartColor)) {
            this.mStrokeStartColor = a.getColor(styleable.PlayAnimView_playStrokeStartColor, Color.BLACK);
        }

        if (a.hasValue(styleable.PlayAnimView_playStrokeCenterColor)) {
            this.mStrokeCenterColor = a.getColor(styleable.PlayAnimView_playStrokeCenterColor, Color.BLACK);
        }

        if (a.hasValue(styleable.PlayAnimView_playStrokeEndColor)) {
            this.mStrokeEndColor = a.getColor(styleable.PlayAnimView_playStrokeEndColor, Color.BLACK);
        }

        if (a.hasValue(styleable.PlayAnimView_playLineCount)) {
            this.mLineCount = a.getInteger(styleable.PlayAnimView_playLineCount, 5);
        }

        if (a.hasValue(styleable.PlayAnimView_playRatioWidth)) {
            this.mRatioWidth = a.getFloat(styleable.PlayAnimView_playRatioWidth, 1 / 3f);
        }

        a.recycle();
    }

    private void init() {
        this.mPaint = new Paint(1);
        this.mPaint.setStrokeWidth(this.mStrokeWidth);
        this.mFillLineLinearGradient = new LinearGradient(0.0F, 0.0F, 0.0F, 1.0F, this.mFullStartColor, this.mFullEndColor, TileMode.CLAMP);
        this.mStrokeLineLinearGradient = new LinearGradient(0.0F, 0.0F, 0.0F, 1.0F, new int[]{this.mStrokeStartColor, this.mStrokeCenterColor, this.mStrokeEndColor}, (float[]) null, TileMode.CLAMP);
        this.mMatrix = new Matrix();

        this.mFillLineLinearGradient.setLocalMatrix(mMatrix);
        this.mStrokeLineLinearGradient.setLocalMatrix(mMatrix);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        int var4 = this.getMeasuredWidth();
        int var5 = this.getMeasuredHeight();
        int size = Math.min(var4, var5);
        this.setMeasuredDimension(size, size);
        this.mLineWidth = (int) ((float) this.getMeasuredWidth() * this.mRatioWidth / (float) (2 * this.mLineCount - 1));
        this.mLinePadding = (int) (((float) this.getMeasuredWidth() * this.mRatioWidth - (float) ((2 * this.mLineCount - 1) * this.mLineWidth)) / (float) 2);
        this.mMaxHeight = (int) ((float) this.getMeasuredHeight() * this.mRatioWidth);
        this.mMinHeight = (int) ((float) this.getMeasuredHeight() * this.mRatioWidth / (float) 5);
        this.mRadius = (float) this.mLineWidth / 2.0F;
        this.computeOriginHeight();
    }

    private void computeOriginHeight() {
        this.mOriginHeightList.clear();
        Random mRandom = new Random();
        for (int i = 0; i < mLineCount; i++) {
            mOriginHeightList.add(mRandom.nextFloat() * (mMaxHeight - mMinHeight) + mMinHeight);
        }
    }

    @SuppressLint({"DrawAllocation"})
    @Override
    protected void onDraw(@NotNull Canvas canvas) {
        super.onDraw(canvas);
        float x = 0.0F;
        float y = 0.0F;
        float x1 = 0.0F;
        float y1 = 0.0F;
        float originHeight = 0.0F;
        float height = 0.0F;
        float realProgress = this.mProgress;

        for (int it = 0; it < mLineCount; it++) {
            originHeight = mOriginHeightList.get(it);
            if (originHeight > (float) ((this.mMaxHeight - this.mMinHeight) / 2 + this.mMinHeight)) {
                realProgress = -realProgress;
            }
            height = (float) ((this.mMaxHeight - this.mMinHeight) * 2) * realProgress + originHeight;
            if (realProgress >= 0) {
                if (height > mMaxHeight) {
                    height -= Math.abs(height - mMaxHeight) * 2;
                }
                if (height < mMinHeight) {
                    height += Math.abs(mMinHeight - height) * 2;
                }
            } else {
                if (height < mMinHeight) {
                    height += Math.abs(mMinHeight - height) * 2;
                }
                if (height > mMaxHeight) {
                    height -= Math.abs(height - mMaxHeight) * 2;
                }
            }

            x = (float) (this.mLinePadding + 2 * it * this.mLineWidth) + (float) this.getMeasuredWidth() * ((float) 1 - this.mRatioWidth) / (float) 2;
            y = (float) (this.getMeasuredHeight() / 2) - height / (float) 2;
            x1 = x + (float) this.mLineWidth;
            y1 = y + height;

            mPaint.setStyle(Style.FILL);
            mMatrix.setTranslate(x, y);
            mMatrix.preScale(mLineWidth, height);
            mFillLineLinearGradient.setLocalMatrix(mMatrix);
            mPaint.setShader(mFillLineLinearGradient);
            canvas.drawRoundRect(
                    x, y, x1, y1, mRadius, mRadius, mPaint
            );

            mPaint.setStyle(Paint.Style.STROKE);
            mStrokeLineLinearGradient.setLocalMatrix(mMatrix);
            mPaint.setShader(mStrokeLineLinearGradient);
            canvas.drawRoundRect(
                    x, y, x1, y1, mRadius, mRadius, mPaint
            );
        }
    }

    // CPU优化：禁用播放动画以降低CPU使用率
    public void startAnimation() {
        // CPU优化：直接返回，不启动动画
        return;
    }

    public final void stopAnimation() {
        if (this.mValueAnimator != null) {
            if (this.mValueAnimator.isRunning()) {
                this.mValueAnimator.cancel();
            }
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        this.stopAnimation();
        if (this.mValueAnimator != null) {
            this.mValueAnimator.removeAllUpdateListeners();
        }
        super.onDetachedFromWindow();
    }

    public final void updateBg(@NotNull Drawable drawable) {
        this.mBgBitmap = drawable;
        this.invalidate();
    }

    public PlayAnimView(@Nullable Context context) {
        this(context, (AttributeSet) null);
    }

    public PlayAnimView(@Nullable Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public PlayAnimView(@Nullable Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.mStrokeWidth = 2.0F;
        this.mRatioWidth = 0.33333334F;
        this.mLineCount = 5;
        this.mOriginHeightList = (List) (new ArrayList());
        this.mFullStartColor = Color.BLACK;
        this.mFullEndColor = Color.BLACK;
        this.mStrokeStartColor = Color.BLACK;
        this.mStrokeCenterColor = Color.BLACK;
        this.mStrokeEndColor = Color.BLACK;
        this.initProperties(context, attrs);
        this.init();
    }

}
