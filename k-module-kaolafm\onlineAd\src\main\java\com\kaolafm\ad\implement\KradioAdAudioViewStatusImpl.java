package com.kaolafm.ad.implement;


import com.kaolafm.ad.view.KradioAdAudioSuperView;

/**
 * 音频广告状态接口
 * <AUTHOR>
 * @date 2020-01-13
 */

public interface KradioAdAudioViewStatusImpl {

    void timerend();

    void playAdOver();

    void interrupt();

    void reset();

    void show();

    void hide();

    void pause();

    void cancel();

    KradioAdAudioSuperView create();

    void updateTime();

    void dismiss();

    void distroy();
}
