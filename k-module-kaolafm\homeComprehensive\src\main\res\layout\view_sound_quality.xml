<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/sound_quality_recycler_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginBottom="@dimen/y30"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintHeight_percent="0.7"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <TextView
        android:id="@+id/sound_quality_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/sound_quality"
        android:textColor="@color/colorWhite"
        android:textSize="@dimen/text_size5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/sound_quality_recycler_view"/>


</androidx.constraintlayout.widget.ConstraintLayout>