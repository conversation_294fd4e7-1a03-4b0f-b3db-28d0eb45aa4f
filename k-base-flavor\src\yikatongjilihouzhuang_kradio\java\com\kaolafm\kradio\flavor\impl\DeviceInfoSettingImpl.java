package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.os.Build;
import android.util.Log;

import com.ecarx.sdk.ECarX;
import com.ecarx.sdk.device.DeviceAPI;
import com.kaolafm.kradio.flavor.utils.DeviceInfoUtil;
import com.kaolafm.kradio.lib.base.flavor.DeviceInfoSetting;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-03-19 20:58
 ******************************************/
public final class DeviceInfoSettingImpl implements DeviceInfoSetting {
    @Override
    public void setInfoForSDK(Context context) {
        String deviceId = null;
        try {
            deviceId = DeviceAPI.get(context).getOpenVIN();
        } catch (Exception e) {
            e.printStackTrace();
        }
        Log.i(ECarX.TAG, "setInfoForSDK: deviceId=" + deviceId);
        Log.i(ECarX.TAG, "getDeviceId: deviceId=" + deviceId);
        if (deviceId != null && deviceId.length() > 32) {
            deviceId = deviceId.substring(0, 32);
        }
        DeviceInfoUtil.setDeviceIdAndCarType(deviceId, Build.DEVICE);
    }

//    @Override
//    public String getCarType(Object... args) {
//        Context context = (Context) args[0];
//        String carType = null;
//        try {
//            carType = DeviceAPI.get(context).getVehicleType();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        Log.i(ECarX.TAG, "getCarType: carType=" + carType);
//        return carType;
//        return null;
//    }
}
