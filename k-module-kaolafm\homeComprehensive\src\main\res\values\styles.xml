<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:app="http://schemas.android.com/apk/res-auto">

<!--    <style name="AppThemeTrans" parent="@style/AppBaseTheme">-->

<!--    </style>-->

    <style name="PlayerTitleTextStyle">
        <item name="android:textStyle">bold</item>
        <item name="android:ellipsize">end</item>
        <item name="android:maxLines">1</item>
        <item name="android:textColor">@color/colorWhite</item>
        <item name="android:textSize">@dimen/text_size7</item>
    </style>

    <style name="ListVerticalScrollBar">
        <item name="android:scrollbarThumbVertical">@drawable/scroll_bar_rect</item>
        <item name="android:scrollbarTrackVertical">@drawable/scroll_bar_bg_rect</item>
        <item name="android:scrollbars">vertical</item>
        <item name="android:fadeScrollbars">true</item>
    </style>

    <style name="PlayListVerticalScrollBar" parent="ListVerticalScrollBar">
        <item name="android:scrollbarThumbVertical">@drawable/scroll_bar_rect_play_list</item>
        <item name="android:scrollbarTrackVertical">@null</item>
    </style>

    <style name="secondary_page_top_guideline">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">0dp</item>
        <item name="android:orientation">horizontal</item>
        <item name="layout_constraintGuide_percent">0.21</item>
    </style>

    <style name="SkeletonAllCategory">
        <item name="android:layout_width">80dp</item>
        <item name="android:layout_height">80dp</item>
        <item name="android:padding">@dimen/m20</item>
        <item name="layout_constraintDimensionRatio">1:1</item>
        <item name="layout_constraintHeight_default">percent</item>
        <item name="layout_constraintHeight_percent">0.11</item>
        <item name="layout_constraintHorizontal_bias">0.043</item>
        <item name="layout_constraintLeft_toLeftOf">parent</item>
        <item name="layout_constraintRight_toRightOf">parent</item>
        <item name="layout_constraintTop_toTopOf">parent</item>
    </style>

    <style name="CustomDialog" parent="android:style/Theme.Dialog">
        <!--背景颜色及透明程度-->
        <item name="android:windowBackground">@color/transparent_color</item>
        <!--是否有标题 -->
        <item name="android:windowNoTitle">true</item>
        <!--是否浮现在activity之上-->
        <item name="android:windowIsFloating">true</item>
        <!--是否模糊-->
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.9</item>
    </style>

    <style name="AppThemeCompat.spempty" parent="android:style/Theme.NoTitleBar">
        <item name="android:windowNoTitle">true</item>//无标题
        <item name="android:windowActionBar">false</item>//无ActionBar
        <item name="android:windowFullscreen">@bool/isWindowFullScreen</item>//全屏即无通知栏
        <item name="android:windowContentOverlay">@null</item>//是否有遮盖
        <item name="android:windowBackground">@null</item>
        <!--<item name="android:windowIsTranslucent">true</item>-->
        <!--<item name="android:windowBackground">@drawable/kradio_splash</item>-->
        <!--<item name="android:windowAnimationStyle">@style/activityDefaultAnimation</item>-->
        <!--<item name="android:windowAnimationStyle">@style/activityAnim</item>-->
    </style>

    <style name="HomeItemGoldenRatio">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
    </style>

    <style name="home_all_ctg_style">
        <item name="android:layout_width">@dimen/home_all_button_width</item>
        <item name="android:layout_height">@dimen/home_all_button_height</item>
        <item name="android:text">@string/all_ctg_str</item>
        <item name="android:contentDescription">@string/all_ctg_str</item>
        <item name="android:background">@drawable/selector_home_all_category_ic</item>
    </style>

    <style name="user_logout_button_style">
        <item name="android:layout_width">@dimen/x224</item>
        <item name="android:layout_height">@dimen/y54</item>
    </style>

    <style name="customer_title_text_style">
        <item name="android:gravity">center</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
    </style>
</resources>
