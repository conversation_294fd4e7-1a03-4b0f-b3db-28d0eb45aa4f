package com.kaolafm.kradio.service;

import android.app.PendingIntent;
import android.app.Service;
import android.appwidget.AppWidgetManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.os.IBinder;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.RemoteViews;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.flavor.R;

import com.kaolafm.kradio.k_kaolafm.categories.history.HistoryDaoManager;
import com.kaolafm.kradio.k_kaolafm.categories.history.HistoryItem;
import com.kaolafm.kradio.k_kaolafm.categories.history.HistoryUtils;
import com.kaolafm.kradio.k_kaolafm.cp.CP;
import com.kaolafm.kradio.k_kaolafm.home.player.PlayerHelper;
import com.kaolafm.kradio.k_kaolafm.home.playerbar.SubscribeModel;

import com.kaolafm.kradio.k_kaolafm.subscribe.SubscribeData;
import com.kaolafm.kradio.k_kaolafm.subscribe.SubscribeManager;
import com.kaolafm.kradio.k_kaolafm.subscribe.data.ErrorInfo;
import com.kaolafm.kradio.k_kaolafm.subscribe.data.ResultCallback;
import com.kaolafm.kradio.k_kaolafm.subscribe.data.SubscribeRepository;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.lib.utils.UrlUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.uitl.PlayerUtil;
import com.kaolafm.kradio.widget.BYDWidgetProvider;
import com.kaolafm.sdk.core.mediaplayer.BroadcastRadioListManager;
import com.kaolafm.sdk.core.mediaplayer.IPlayChangedListener;
import com.kaolafm.sdk.core.mediaplayer.IPlayerStateListener;
import com.kaolafm.sdk.core.mediaplayer.LiveBroadcastPlayerManager;
import com.kaolafm.sdk.core.mediaplayer.OnBroadcastRadioLivingListener;


import com.kaolafm.sdk.core.mediaplayer.PlayerRadioListManager;

import com.kaolafm.sdk.vehicle.BroadcastStatus;
import com.kaolafm.sdk.vehicle.GeneralCallback;
import com.kaolafm.utils.BitmapUtils;
import com.kaolafm.utils.DateFormatUtil;
import com.kaolafm.kradio.lib.utils.IntentUtils;

import java.util.List;

import io.reactivex.Observable;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;


/**
 * <AUTHOR>
 */
public class BYDWidgetService extends Service implements IPlayerStateListener, OnBroadcastRadioLivingListener, SubscribeRepository.SubscribeChangeListener, IPlayChangedListener {

    public static final String WIDGET_ACTION_NEXT = "kl.action.next";
    public static final String WIDGET_ACTION_PREV = "kl.action.prev";
    public static final String WIDGET_ACTION_PAUSE = "kl.action.pause";
    public static final String WIDGET_ACTION_PLAY = "kl.action.play";
    public static final String WIDGET_ACTION_REFRESH = "kl.action.refresh";
    public static final String WIDGET_ACTION_EXIT = "kl.action.exit";
    public static final String WIDGET_ACTION_COLLECTION = "kl.action.collection";

    private Context context = AppDelegate.getInstance().getContext();

    private static final String TAG = "BYDWidgetService";
    //专辑图片
    private Bitmap mCurrentBitmap;

    private Bitmap mBlurBitmap;

    private Bitmap mBlurBitmapByLand;

    private Bitmap mBlurBitmapByPort;

    private boolean subscription = false;

    private String livingTime = "";

    private int landW;
    private int landH;
    private int portW;
    private int portH;

    private SubscribeModel subscribeModel;

    private boolean isPortrait = true;

    @Override
    public void onCreate() {
        super.onCreate();
        boolean init = PlayerManager.getInstance().isPlayerInitSuccess();
        Log.i(TAG, "onCreate init:"+init);
        if (!init) {
            PlayerManager.getInstance().addPlayerInitComplete(new IPlayerInitCompleteListener() {
                @Override
                public void onPlayerInitComplete(boolean b) {
                    if (b) {
                        PlayerManager.getInstance().removePlayerInitComplete(this);
                        init();
                    }
                }
            });
        } else {
            init();
        }

    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent == null) {
            return START_NOT_STICKY;
        }
        executeActionCommand(intent.getAction());
        return START_NOT_STICKY;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mCurrentBitmap = null;
        PlayerManagerHelper.getInstance().removeIPlayChangedListener(this);
        BroadcastRadioListManager.getInstance().removeOnBroadcastRadioLivingListener(this);
        PlayerManager.getInstance().removePlayControlStateCallback(this);
        ((SubscribeRepository) SubscribeManager.getInstance(context, CP.KaoLaFM)).removeSubscribeChangeListener(this);
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            mBlurBitmap = mBlurBitmapByLand;
            isPortrait = false;
        } else {
            mBlurBitmap = mBlurBitmapByPort;
            isPortrait = true;
        }
        setRemoteViews(PlayerManager.getInstance().getCurPlayItem());
        super.onConfigurationChanged(newConfig);
    }

    private void init(){
        subscribeModel = new SubscribeModel(context);
        PlayerManager.getInstance().init(AppDelegate.getInstance().getContext());
        PlayerManager.getInstance().addPlayControlStateCallback(this);
        PlayerManagerHelper.getInstance().addIPlayChangedListener(this);
        ((SubscribeRepository) SubscribeManager.getInstance(context, CP.KaoLaFM)).addSubscribeChangeListener(this);
        BroadcastRadioListManager.getInstance().addOnBroadcastRadioLivingListener(this);
        initWH();
        initPlayInfo();
        if (ResUtil.getOrientation() == Configuration.ORIENTATION_LANDSCAPE) {
            isPortrait = false;
        }
    }

    private void initWH() {
        landW = ScreenUtil.dp2px(239);
        landH =ScreenUtil.dp2px(268 - 50);
        portW =ScreenUtil.dp2px(390);
        portH =ScreenUtil.dp2px(177 - 37);
    }

    private void initPlayInfo() {
        PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
        if (playItem == null) {
            HistoryDaoManager.getInstance().queryHistoryListOrderByTime(1, historyItems -> {
                if (!ListUtil.isEmpty(historyItems)) {
                    HistoryItem historyItem = historyItems.get(0);
                    if (historyItem != null) {
                        PlayItem historyPlayItem = HistoryUtils.turnPlayItem(historyItem);
                        updateBitmap(historyPlayItem);
                    }
                }
            });
        } else {
            updateBitmap(playItem);
        }
    }

    @Override
    public void onIdle(PlayItem playItem) {
        Log.i(TAG, "onIdle");
    }

    @Override
    public void onPlayerPreparing(final PlayItem playItem) {
        Log.i(TAG, "onPlayerPreparing");
        if (playItem == null) {
            Log.i(TAG, "playitem null");
            return;
        }
        updateBitmap(playItem);
    }

    @Override
    public void onPlayerPlaying(final PlayItem playItem) {
        Log.i(TAG, "onPlayerPlaying");
        setRemoteViews(playItem);
    }

    @Override
    public void onPlayerPaused(PlayItem playItem) {
        Log.i(TAG, "onPlayerPaused");
        setRemoteViews(playItem);
    }

    @Override
    public void onProgress(String s, int i, int i1, boolean b) {
        Log.i(TAG, "onProgress isRefresh:" );
        PlayItem playItem = getCurrentPlayItem();
        if (playItem != null && !playItem.isLivingUrl()) {
            setRemoteViews(playItem);
        }
    }

    @Override
    public void onPlayerFailed(PlayItem playItem, int what, int extra) {
        Log.i(TAG, "onPlayerFailed");
    }

    @Override
    public void onPlayerEnd(PlayItem playItem) {
        Log.i(TAG, "onPlayerEnd");
    }

    @Override
    public void onSeekStart(String s) {
        Log.i(TAG, "onSeekStart");
    }

    @Override
    public void onSeekComplete(String s) {
        Log.i(TAG, "onSeekComplete");
    }

    @Override
    public void onBufferingStart(PlayItem playItem) {
        Log.i(TAG, "onBufferingStart");
    }

    @Override
    public void onBufferingEnd(PlayItem playItem) {
        Log.i(TAG, "onBufferingEnd");
    }

    private String getPic(PlayItem playItem,String type) {
        String url = playItem.getPic();

        if (TextUtils.isEmpty(url)) {
            PlayerRadioListItem curPlayerRadioListItem = PlayerRadioListManager.getInstance().getCurRadioItem();
            if (curPlayerRadioListItem != null && !TextUtils.isEmpty(curPlayerRadioListItem.getPicUrl())) {
                url = curPlayerRadioListItem.getPicUrl();
            }
        }

        String picUrl = UrlUtil.getCustomPicUrl(type, url);

        return picUrl;
    }

    private void executeActionCommand(String action) {
        if (TextUtils.isEmpty(action)) {
            return;
        }

        switch (action) {
            case WIDGET_ACTION_NEXT:
                if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
                    return;
                }

                if (PlayerManager.getInstance().getCurPlayItem() == null) {
                    PlayerUtil.playNetOrLocal();
                } else {
                    PlayerManager.getInstance().playNext();
                }
                break;
            case WIDGET_ACTION_PREV:
                if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
                    return;
                }

                if (PlayerManager.getInstance().getCurPlayItem() == null) {
                    PlayerUtil.playNetOrLocal();
                } else {
                    PlayerManager.getInstance().playPre();
                }
                break;
            case WIDGET_ACTION_PLAY:
                if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
                    return;
                }
                if (PlayerManager.getInstance().getCurPlayItem() == null) {
                    PlayerUtil.playNetOrLocal();
                } else {
                    PlayerManagerHelper.getInstance().switchPlayerStatus(true);
                }
                break;
            case WIDGET_ACTION_PAUSE:
                if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
                    return;
                }
                PlayerManagerHelper.getInstance().switchPlayerStatus(true);
                break;
            case WIDGET_ACTION_COLLECTION:
                widgetSubscribe(PlayerHelper.getSubscribeId());
                break;
            case WIDGET_ACTION_REFRESH:
                setRemoteViews(PlayerManager.getInstance().getCurPlayItem());
                break;
            case WIDGET_ACTION_EXIT:
                setRemoteViews(PlayerManager.getInstance().getCurPlayItem());
                break;
        }
    }

    @Override
    public void onSubscribesChanged(List<SubscribeData> subscribes) {
        subscription = false;
        if (!ListUtil.isEmpty(subscribes)) {
            for (SubscribeData s : subscribes) {
                if (s.getId() == PlayerHelper.getSubscribeId()) {
                    subscription = true;
                    break;
                }
            }
        }
        setRemoteViews(PlayerManager.getInstance().getCurPlayItem());
    }

    @Override
    public void onPlayChangeChanged(PlayItem playItem) {
        setRemoteViews(playItem);
        setSubscribeState();
    }

    private static class MyGeneralCallback implements GeneralCallback {
        private String actionType;
        private Context context;

        public MyGeneralCallback(Context context, String actionType) {
            this.context = context;
            this.actionType = actionType;
        }

        @Override
        public void onResult(Object o) {
//            PlayerManager.getInstance().removeGetContentListener(this);
//            if (WIDGET_ACTION_PREV.equals(actionType)) {
//                KLAutoPlayerManager.getInstance(context).playPre();
//            } else {
//                KLAutoPlayerManager.getInstance(context).playNext();
//            }
        }

        @Override
        public void onError(int i) {
            PlayerManager.getInstance().removeGetContentListener(this);
        }

        @Override
        public void onException(Throwable throwable) {
            PlayerManager.getInstance().removeGetContentListener(this);
        }
    }


    private void playHistoryOrDefault(final String actionType) {
//        HistoryItem history = getRecentlyItem();
//        boolean isPlayNextOrPre = WIDGET_ACTION_NEXT.equals(actionType) || WIDGET_ACTION_PREV.equals(actionType);
//        if (WIDGET_ACTION_PLAY.equals(actionType)) {
//
//        } else {
//            if (history != null && !Constants.RESOURCES_TYPE_BROADCAST.equals(history.getType())) {
//                if (isPlayNextOrPre) {
//                    PlayerManager.getInstance().addGetContentListener(new MyGeneralCallback(getApplicationContext(), actionType));
//                }
//            }
//        }
//        if (history != null) {
//            KLAutoPlayerManager.getInstance(getApplicationContext()).playHistory(history, !isPlayNextOrPre);
//        } else {
//            KLAutoPlayerManager.getInstance(getApplicationContext()).playPgc(Constants.DEFAULT_PGC, !isPlayNextOrPre);
//        }
    }

    @Override
    public void onLivingCountDown(String s) {
        PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
        livingTime = s;
        if (playItem != null && playItem.isLivingUrl()) {
            setRemoteViews(playItem);
        }
    }

    private void updateBitmap(PlayItem playItem) {
        String pic = getPic(playItem,UrlUtil.PIC_250_250);
        if (TextUtils.isEmpty(pic)) {
            setRemoteViews(playItem);
        } else {
            ImageLoader.getInstance().getBitmapFromCache(context, pic, bitmap -> {
                PlayItem curPlayItem = getCurrentPlayItem();
                if (curPlayItem != null && curPlayItem.getAudioId() == playItem.getAudioId()) {
                    mCurrentBitmap = bitmap;
                    setRemoteViews(playItem);
                }

            });
        }

        updateblurBitmap(playItem);
    }

    private void updateblurBitmap(PlayItem playItem){
        String pic = getPic(playItem,UrlUtil.PIC_100_100);
        ImageLoader.getInstance().getBlurBitmapFromCache(context, pic, 20, bitmap -> {
            Observable.create((ObservableOnSubscribe<Boolean>) emitter -> {

                Bitmap portB = BitmapUtils.createScaledBitmapRGB565(bitmap, portW, portH, BitmapUtils.ScalingLogic.CROP);
                Bitmap landB = BitmapUtils.createScaledBitmapRGB565(bitmap, landW, landH, BitmapUtils.ScalingLogic.CROP);

                mBlurBitmapByPort = BitmapUtils.setBitmapRoundSize(portB, portW, portH, ScreenUtil.dp2px(10));
                mBlurBitmapByLand = BitmapUtils.setBitmapRoundSize(landB, landW, landH, ScreenUtil.dp2px(10));

                if (ResUtil.getOrientation() == Configuration.ORIENTATION_LANDSCAPE) {
                    mBlurBitmap = mBlurBitmapByLand;
                } else {
                    mBlurBitmap = mBlurBitmapByPort;
                }
                //todo
                mBlurBitmap = mBlurBitmapByLand ;

                emitter.onNext(true);
            }).subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(b -> setRemoteViews(playItem));
        });
    }

    public void setRemoteViews(PlayItem playItem) {
        Log.i(TAG, "setRemoteViews");
        final AppWidgetManager manager = AppWidgetManager.getInstance(context);
        final ComponentName componentName = new ComponentName(context, BYDWidgetProvider.class);
        RemoteViews views = new RemoteViews(context.getPackageName(), R.layout.widget_layout_sport);
        //设置播放暂停按钮
        if (PlayerManager.getInstance().isPlaying()) {
            showPlayBtn(views);
        } else {
            showPauseBtn(views);
        }
        //设置点击封面进入应用
        Intent launchAppIntent = IntentUtils.getInstance().getLauncherIntentUseWidget(context);
        views.setOnClickPendingIntent(R.id.widget_playinfo_layout, PendingIntent.getActivity(context, 0, launchAppIntent, 0));
        //设置上一首
        Intent intent = new Intent(context, BYDWidgetService.class);
        intent.setAction(WIDGET_ACTION_PREV);
        views.setOnClickPendingIntent(R.id.widget_prev, PendingIntent.getService(context, 0, intent, 0));
        //设置下一首
        Intent intent1 = new Intent(context, BYDWidgetService.class);
        intent1.setAction(WIDGET_ACTION_NEXT);
        views.setOnClickPendingIntent(R.id.widget_next, PendingIntent.getService(context, 0, intent1, 0));
        //设置收藏
        Intent collection = new Intent(context, BYDWidgetService.class);
        collection.setAction(WIDGET_ACTION_COLLECTION);
        views.setOnClickPendingIntent(R.id.widget_collection, PendingIntent.getService(context, 0, collection, 0));

        if (playItem != null) {
            updateByPlayItem(playItem, views);
            manager.updateAppWidget(componentName, views);
        } else {
            HistoryDaoManager.getInstance().queryHistoryListOrderByTime(1, historyItems -> {
                if (!ListUtil.isEmpty(historyItems)) {
                    HistoryItem historyItem = historyItems.get(0);
                    PlayItem historyPlayItem = HistoryUtils.turnPlayItem(historyItem);
                    updateByPlayItem(historyPlayItem, views);
                    manager.updateAppWidget(componentName, views);
                }
            });
        }
    }


    private void updateByPlayItem(PlayItem playItem, RemoteViews views) {

        if (playItem != null) {
            Log.i(TAG, "updateByPlayItem："+ playItem.getTitle());
            //设置封面图片
            views.setImageViewBitmap(R.id.widget_cover, mCurrentBitmap);
            //设置高斯模糊图片
            views.setImageViewBitmap(R.id.widget_blur_imageview, mBlurBitmap);

            views.setViewVisibility(R.id.widget_blur_bg, View.VISIBLE);

            //设置碎片名称
            CharSequence widgetText = PlayItemUtils.getPlayItemAudioName(playItem);
            if (!TextUtils.isEmpty(widgetText)) {
                views.setTextViewText(R.id.widget_audio_name, widgetText);
            } else {
                views.setTextViewText(R.id.widget_audio_name, "暂无节目信息");
            }
            //设置专辑名称
            CharSequence albumName = PlayItemUtils.getPlayItemAlbumName(playItem);
            if (!TextUtils.isEmpty(albumName)) {
                views.setTextViewText(R.id.widget_album_name, albumName);
            }

            if (playItem.isLivingUrl()) {
                if (playItem.getFinishTime() <= 0) {
                    views.setViewVisibility(R.id.widget_duration, View.INVISIBLE);
                    views.setViewVisibility(R.id.widget_cur_time, View.INVISIBLE);
                    views.setProgressBar(R.id.widget_progressBar, 0, 0, false);
                } else {
                    views.setViewVisibility(R.id.widget_duration, View.VISIBLE);
                    views.setViewVisibility(R.id.widget_cur_time, View.VISIBLE);
                    views.setTextViewText(R.id.widget_duration, DateFormatUtil.getCurrDate(playItem.getFinishTime()));
                    views.setProgressBar(R.id.widget_progressBar, 0, 0, false);
                    views.setTextViewText(R.id.widget_cur_time, livingTime + " / ");
                }
            } else {
                views.setViewVisibility(R.id.widget_duration, View.VISIBLE);
                views.setViewVisibility(R.id.widget_cur_time, View.VISIBLE);
                int duration = playItem.getDuration();
                Log.i(TAG, "updateByPlayItem----------------->position = " + playItem.getPosition() + "---->duration = " + duration);
                views.setProgressBar(R.id.widget_progressBar, duration, playItem.getPosition(), false);
                views.setTextViewText(R.id.widget_duration, DateFormatUtil.getDescriptiveTime(duration));
                views.setTextViewText(R.id.widget_cur_time, DateFormatUtil.getDescriptiveTime(playItem.getPosition()) + " / ");
            }
        } else {
            views.setViewVisibility(R.id.widget_blur_bg, View.GONE);
        }
        //设置收藏状态
        if (subscription) {
            views.setImageViewResource(R.id.widget_collection, R.drawable.selector_widget_btn_collection);
        } else {
            views.setImageViewResource(R.id.widget_collection, R.drawable.selector_widget_btn_uncollection);
        }

        //设置直播中小标
        updateBroadcastLabel(views);
    }

    private void updateBroadcastLabel(RemoteViews remoteViews) {
        if (remoteViews == null) {
            return;
        }
        PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
        if (playItem == null) {
            return;
        }

        int whichPlayer = PlayerHelper.whichPlayer();

        if (whichPlayer == PlayerHelper.PLAYER_TYPE_BROADCAST) {
            remoteViews.setViewVisibility(R.id.widget_broadcast_label, View.VISIBLE);
            remoteViews.setViewVisibility(R.id.widget_broadcast_label_textview, View.VISIBLE);
            switch (playItem.getStatus()) {
                //直播
                case BroadcastStatus.BROADCAST_STATUS_LIVING:
                    remoteViews.setImageViewResource(R.id.widget_broadcast_label, R.drawable.icon_player_living);
                    remoteViews.setTextViewText(R.id.widget_broadcast_label_textview, "直播中");
                    break;
                //回放
                case BroadcastStatus.BROADCAST_STATUS_PLAYBACK:
                    remoteViews.setImageViewResource(R.id.widget_broadcast_label, R.drawable.icon_player_playback);
                    remoteViews.setTextViewText(R.id.widget_broadcast_label_textview, "回听");
                    break;
            }
        } else {
            remoteViews.setViewVisibility(R.id.widget_broadcast_label, View.GONE);
            remoteViews.setViewVisibility(R.id.widget_broadcast_label_textview, View.GONE);
        }
    }

    private void showPlayBtn(RemoteViews views) {
        views.setImageViewResource(R.id.widget_play_or_pause, R.drawable.selector_widget_btn_play);
        Intent intent = new Intent(context, BYDWidgetService.class);
        intent.setAction(WIDGET_ACTION_PAUSE);
        views.setOnClickPendingIntent(R.id.widget_play_or_pause, PendingIntent.getService(context, 0, intent, 0));
    }

    private void showPauseBtn(RemoteViews views) {
        views.setImageViewResource(R.id.widget_play_or_pause, R.drawable.selector_widget_btn_pause);
        Intent intent = new Intent(context, BYDWidgetService.class);
        intent.setAction(WIDGET_ACTION_PLAY);
        views.setOnClickPendingIntent(R.id.widget_play_or_pause, PendingIntent.getService(context, 0, intent, 0));
    }

    private PlayItem getCurrentPlayItem() {
        PlayItem curPlayItem;
        int whichPlayer = PlayerHelper.whichPlayer();

        if (whichPlayer == PlayerHelper.PLAYER_TYPE_LIVE) {
            curPlayItem = LiveBroadcastPlayerManager.getInstance().getPlayItem();
        } else {
            curPlayItem = PlayerManager.getInstance().getCurPlayItem();
        }

        return curPlayItem;
    }

    public void widgetSubscribe(final long id){
        if (subscribeModel == null) {
            return;
        }
        subscribeModel.isSubscribed(String.valueOf(id), new ResultCallback() {
            @Override
            public void onResult(boolean result, int code) {
                if (result) {
                    unSubscribe(id);
                } else {
                    subscribe(id);
                }
            }

            @Override
            public void onFailure(ErrorInfo errorInfo) {

            }
        });
    }

    private void subscribe(long id){
        if (subscribeModel == null) {
            return;
        }
        SubscribeData subscribeData = new SubscribeData();
        subscribeData.setId(id);
        subscribeModel.subscribe(subscribeData, new ResultCallback() {
            @Override
            public void onResult(boolean result, int code) {
                subscription = result;
                setRemoteViews(PlayerManager.getInstance().getCurPlayItem());
            }

            @Override
            public void onFailure(ErrorInfo errorInfo) {

            }
        });
    }

    private void unSubscribe(long id) {
        if (subscribeModel == null) {
            return;
        }
        SubscribeData sd = new SubscribeData();
        sd.setId(id);
        subscribeModel.unsubscribe(sd, new ResultCallback() {
            @Override
            public void onResult(boolean result, int code) {
                if (result) {
                    subscription = false;
                }
                setRemoteViews(PlayerManager.getInstance().getCurPlayItem());
            }

            @Override
            public void onFailure(ErrorInfo errorInfo) {

            }
        });
    }

    private void setSubscribeState(){
        if (subscribeModel == null) {
            return;
        }
        subscribeModel.isSubscribed(String.valueOf(PlayerHelper.getSubscribeId()), new ResultCallback() {
            @Override
            public void onResult(boolean result, int code) {
                subscription = result;
                setRemoteViews(PlayerManager.getInstance().getCurPlayItem());
            }

            @Override
            public void onFailure(ErrorInfo errorInfo) {

            }
        });
    }

}
