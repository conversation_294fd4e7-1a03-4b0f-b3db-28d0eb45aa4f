package com.kaolafm.kradio.online.player.mvp;

import com.kaolafm.kradio.lib.base.mvp.IView;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import java.util.List;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-04-17 11:34
 ******************************************/

public interface ListenTVProgramListView extends IView {
    void onGetListenTvProgramListDataSuccess(List<PlayItem> playItemArrayList, boolean updateView);

    void onGetListenTvProgramListDataError(int code, String msg);
}
