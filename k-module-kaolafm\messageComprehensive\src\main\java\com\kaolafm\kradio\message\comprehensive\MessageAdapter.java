package com.kaolafm.kradio.message.comprehensive;

import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.utils.DateUtil;
import com.kaolafm.kradio.lib.bean.CrashMessageBean;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;


/**
 * 消息盒子适配器 插播占位
 */
public class MessageAdapter extends BaseAdapter<CrashMessageBean> {
    @Override
    protected BaseHolder<CrashMessageBean> getViewHolder(ViewGroup parent, int viewType) {
        return new MessageViewHolder(inflate(parent, R.layout.msg_item_adapter, viewType));
    }

    public class MessageViewHolder extends BaseHolder<CrashMessageBean> {

        ImageView msgItemPic;
        ImageView arrowIcon;
        TextView msgItemTitle;
        TextView msgItemTime;
        TextView msgItemContent;
        ImageView un_readed_tip;

        public MessageViewHolder(View itemView) {
            super(itemView);
            msgItemPic=itemView.findViewById(R.id.msg_item_pic);
            arrowIcon=itemView.findViewById(R.id.arrowIcon);
            msgItemTitle=itemView.findViewById(R.id.msg_item_title);
            msgItemTime=itemView.findViewById(R.id.msg_item_time);
            msgItemContent=itemView.findViewById(R.id.msg_item_content);

            un_readed_tip = itemView.findViewById(R.id.un_readed_tip);

            msgItemTitle.setMaxWidth(ScreenUtil.getScreenWidth() - ResUtil.getDimen(R.dimen.x600));
        }

        @Override
        public void setupData(CrashMessageBean messageBean, int position) {
            msgItemTitle.setText(messageBean.getTipsTitle());

            try {
                msgItemTime.setText(DateUtil.formatMillis("yyyy-MM-dd HH:mm", Long.parseLong(messageBean.getSendTime())));
            } catch (Exception e) {
                Logger.e("MessageViewHolder",e.getMessage());
            }
            if( !StringUtil.isEmpty(messageBean.getEventDescription())){
                msgItemContent.setText(messageBean.getEventDescription());
            } else {
                if( !StringUtil.isEmpty(messageBean.getEventDescriptionExtract())){
                    msgItemContent.setText(messageBean.getEventDescriptionExtract());
                }else {
                    msgItemTitle.setText("");
                }
            }

//            int resourceId = 0;
//            switch (messageBean.getMsgLevel()) {
////            resourceId = R.drawable.online_message_bubble_warning;黄色预留，一期不做
//                case "1"://情感化问候
//                    resourceId = R.drawable.message_blue_icon;
//                    break;
//                case "2"://节目预约、社交 、礼物等
//                    resourceId = R.drawable.message_green_icon;
//                    break;
//                case "3"://应急广播消息
////                    switch (messageBean.getEventLevel()) {
////                        case "1":
//                    resourceId = R.drawable.message_red_iconr;
////                            break;
////                        case "2":
////                            resourceId = R.drawable.message_bubble_cheng;
////                            break;
////                        case "3":
////                            resourceId = R.drawable.message_bubble_huang;
////                            break;
////                        case "4":
////                            resourceId = R.drawable.message_bubble_lan;
////                            break;
////                    }
//                    break;
//                default:
//                    resourceId = R.drawable.message_green_icon;
//            }

//            if (resourceId != 0) {
//                msgItemPic.setVisibility(View.VISIBLE);
//                msgItemPic.setImageResource(resourceId);
//            }else {
//                msgItemPic.setVisibility(View.GONE);
//            }
            if( !StringUtil.isEmpty(messageBean.getMsgIconUrl())){
                ImageLoader.getInstance().displayImage(msgItemPic.getContext(), messageBean.getMsgIconUrl(), msgItemPic);
            }else {
                if(MessageDialogHelper.isEbPushMessage(messageBean)){ //紧急消息
                    msgItemPic.setImageResource(R.drawable.message_red_iconr);
                } else {
                    msgItemPic.setImageResource(R.drawable.media_default_pic);
                }
            }

            if (messageBean.isLook()) {
                //已读消息
                msgItemTitle.setTextColor(ResUtil.getColor(R.color.message_item_title_color));
                msgItemTime.setTextColor(ResUtil.getColor(R.color.message_item_title_color));
                msgItemContent.setTextColor(ResUtil.getColor(R.color.message_item_title_color));
                arrowIcon.setImageDrawable(ResUtil.getDrawable(R.drawable.message_arrow_right_not));
                un_readed_tip.setVisibility(View.GONE);
            } else {
                //未读消息
                msgItemTitle.setTextColor(ResUtil.getColor(R.color.message_item_title_color2));
                msgItemTime.setTextColor(ResUtil.getColor(R.color.message_item_title_color2));
                msgItemContent.setTextColor(ResUtil.getColor(R.color.message_item_title_color2));
                arrowIcon.setImageDrawable(ResUtil.getDrawable(R.drawable.message_arrow_right));
                un_readed_tip.setVisibility(View.VISIBLE);
            }
        }
    }
}
