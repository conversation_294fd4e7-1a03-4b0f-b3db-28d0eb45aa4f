package com.kaolafm.kradio.live.comprehensive.gift.ui;

import android.graphics.Bitmap;
import android.graphics.drawable.AnimationDrawable;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;

import java.io.InputStream;

/**
 * Created by <PERSON> on 2023/2/9.
 */
public class AnimatedGifDrawable extends AnimationDrawable {

	private int mCurrentIndex = 0;
	private UpdateListener mListener;
	// CPU优化：添加帧计数器，降低GIF更新频率
	private int frameCounter = 0;
	private static final int UPDATE_INTERVAL = 3; // 每3帧更新一次UI

	public AnimatedGifDrawable(InputStream source, UpdateListener listener) {
		mListener = listener;
		GifDecoder decoder = new GifDecoder();
		decoder.read(source);
		// Iterate through the gif frames, add each as animation frame
		for (int i = 0; i < decoder.getFrameCount(); i++) {
			Bitmap bitmap = decoder.getFrame(i);
			BitmapDrawable drawable = new BitmapDrawable(bitmap);
			// Explicitly set the bounds in order for the frames to display
			drawable.setBounds(0, 0, bitmap.getWidth(), bitmap.getHeight());
			addFrame(drawable, decoder.getDelay(i));
			if (i == 0) {
				// Also set the bounds for this container drawable
				setBounds(0, 0, bitmap.getWidth(), bitmap.getHeight());
			}
		}
	}

	/**
	 * Naive method to proceed to next frame. Also notifies listener.
	 * CPU优化：降低UI更新频率，从每帧更新改为每3帧更新一次
	 */
	public void nextFrame() {
		mCurrentIndex = (mCurrentIndex + 1) % getNumberOfFrames();
		// CPU优化：只在特定帧间隔时才触发UI更新，减少MessageQueue压力
		if (mListener != null && (++frameCounter % UPDATE_INTERVAL == 0)) {
			mListener.update();
		}
	}

	/**
	 * Return display duration for current frame
	 */
	public int getFrameDuration() {
		return getDuration(mCurrentIndex);
	}

	/**
	 * Return drawable for current frame
	 */
	public Drawable getDrawable() {
		return getFrame(mCurrentIndex);
	}

	/**
	 * Interface to notify listener to update/redraw Can't figure out how to
	 * invalidate the drawable (or span in which it sits) itself to force redraw
	 */
	public interface UpdateListener {
		void update();
	}

}