package com.kaolafm.gradle.plugin


import com.kaolafm.gradle.plugin.micromodule.MicroModule
import groovy.json.JsonSlurper
import org.gradle.api.Project
import org.w3c.dom.Element

import javax.xml.parsers.DocumentBuilderFactory

class Util {

    static String upperCase(String str) {
        char[] ch = str.toCharArray()
        if (ch[0] >= 'a' && ch[0] <= 'z') {
            ch[0] -= 32
        }
        return String.valueOf(ch)
    }

    static String upperCastFirst(String str) {
        def string = str.substring(0, 1)
        def upperCase = string.toUpperCase()
        return upperCase + str.substring(1, str.length())
    }

    /**
     * 将驼峰命名改成大写+下划线的命名
     */
    static String underscoreName(String name) {
        StringBuilder result = new StringBuilder()
        if (name != null && name.length() > 0) {
            // 将第一个字符处理成大写
            result.append(name.substring(0, 1).toUpperCase())
            // 循环处理其余字符
            for (int i = 1; i < name.length(); i++) {
                String s = name.substring(i, i + 1)
                // 在大写字母前添加下划线
                if (s == s.toUpperCase() && !Character.isDigit(s.charAt(0))) {
                    result.append("_")
                }
                // 其他字符直接转成大写
                result.append(s.toUpperCase())
            }
        }
        return result.toString()
    }


    static String getAndroidManifestPackageName(File androidManifest) {
        def builderFactory = DocumentBuilderFactory.newInstance()
        builderFactory.setNamespaceAware(true)
        Element manifestXml = builderFactory.newDocumentBuilder().parse(androidManifest).documentElement
        return manifestXml.getAttribute("package")
    }

    static MicroModule buildMicroModule(Project project, String microModulePath) {
        String[] pathElements = removeTrailingColon(microModulePath).split(":")
        int pathElementsLen = pathElements.size()
        File parentMicroModuleDir = project.projectDir
        for (int j = 0; j < pathElementsLen; j++) {
            parentMicroModuleDir = new File(parentMicroModuleDir, pathElements[j])
        }
        File microModuleDir = parentMicroModuleDir.canonicalFile
        String microModuleName = microModuleDir.absolutePath.replace(project.projectDir.absolutePath, "")
        if (File.separator == "\\") {
            microModuleName = microModuleName.replaceAll("\\\\", ":")
        } else {
            microModuleName = microModuleName.replaceAll("/", ":")
        }
        if (!microModuleDir.exists()) {
            return null
        }
        MicroModule microModule = new MicroModule()
        microModule.name = microModuleName
        microModule.microModuleDir = microModuleDir
        return microModule
    }

    private static String removeTrailingColon(String microModulePath) {
        return microModulePath.startsWith(":") ? microModulePath.substring(1) : microModulePath
    }

    static def readLocalProperties(Project project, String name) {
        def properties = new Properties()
        File file = project.rootProject.file('local.properties')
        if (file.exists()) {
            def inputStream = file.newDataInputStream()
            properties.load(inputStream)
            if (properties.containsKey(name)) {
                return properties.getProperty(name)
            }
        }
    }

//    /**
//     * 获取版本号
//     * @return
//     */
//    static def getVersionCode(Project project) {
//        def majorVersion = null
//        def patterns = getBuildPattern()
//        if(patterns.contains("comprehensive")){
//            majorVersion = project.rootProject.ext.majorVersionCode_comprehensive
//        }
//        if(patterns.contains("online")){
//            majorVersion = project.rootProject.ext.majorVersionCode_online
//        }
//        return majorVersion
//    }
//
//    /**
//     * 获取版本名称
//     * @return
//     */
//    static def getVersionName(Project project) {
//        def majorVersion = null
//        def patterns = getBuildPattern()
//        if(patterns.contains("comprehensive")){
//            majorVersion = project.rootProject.ext.majorVersionName_comprehensive
//        }
//        if(patterns.contains("online")){
//            majorVersion = project.rootProject.ext.majorVersionName_online
//        }
//        return majorVersion
//    }

    /**
     * 获取模式（综合，在线电台，儿童）
     * @return
     */
    static def getBuildPattern(Project project) {
        def patterns = null
        def singleChannel = readLocalProperties(project.rootProject, "SINGLE_CHANNEL")
        def json = getChannels(project.rootProject)
        json.each {
            if (singleChannel?.length() > 0) {
                if (it.manifestPlaceholders.KL_CHANNEL_VALUE == singleChannel) {
                    patterns = it.patterns
                    return
                }
            }
        }
        return patterns
    }

    /**
     * 获取渠道
     * @param project
     * @return
     */
    static def getChannels(Project project) {
        def rootDir = project.rootDir.path
        def file = new File(rootDir + "/flavor.json")
        return new JsonSlurper().parse(file)
    }

}