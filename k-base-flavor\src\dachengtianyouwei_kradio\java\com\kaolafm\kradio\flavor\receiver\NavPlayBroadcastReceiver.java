package com.kaolafm.kradio.flavor.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;



/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-04-16 09:06
 ******************************************/
public final class NavPlayBroadcastReceiver extends BroadcastReceiver {
    private static final String TAG = "NavPlayBroadcastReceiver";

    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        Log.i(TAG, "onReceive------->action = " + action);
        if ("android.NaviOne.voiceprotocol".equals(action)) {
            String extra = intent.getStringExtra("VOICEPROTOCOL");
            Log.i(TAG, "onReceive------->extra = " + extra);
            // 导航播报
            if ("play".equals(extra)) {
                KLAutoPlayerManager.getInstance().setVolume(0.3F, 0.3F);
            }
            // 导航暂停播报
            else if ("stop".equals(extra)) {
                KLAutoPlayerManager.getInstance().setVolume(1.0F, 1.0F);
            }
        }
    }
}
