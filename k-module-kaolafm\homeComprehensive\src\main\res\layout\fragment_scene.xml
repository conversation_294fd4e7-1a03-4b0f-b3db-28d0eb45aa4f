<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/home_scene_bg">

    <ImageView
        android:id="@+id/scene_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_vertical"
        android:orientation="vertical"
        android:weightSum="100">

        <View
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="30" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/choice_rv"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_gravity="center_vertical"
            android:layout_weight="34" />


        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="36">

            <ImageView
                android:id="@+id/choice_iv"
                android:layout_width="@dimen/m60"
                android:layout_height="@dimen/m60"
                android:layout_gravity="center"
                android:background="@drawable/scene_close"
                android:scaleType="centerCrop" />

        </FrameLayout>
    </LinearLayout>


</RelativeLayout>