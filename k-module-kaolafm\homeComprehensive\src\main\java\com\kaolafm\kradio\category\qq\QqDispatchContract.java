package com.kaolafm.kradio.category.qq;

import androidx.fragment.app.Fragment;

/**
 * <AUTHOR>
 **/
public class QqDispatchContract {

//    public static final int STATE_UNLOAD = 0;
//    public static final int STATE_LOAD = 1;
//
//    @IntDef({STATE_UNLOAD, STATE_LOAD})
//    public @interface State {
//    }

    public interface IPresenter extends com.kaolafm.kradio.lib.base.mvp.IPresenter {
        void loadData(long categoryId);
    }


    public interface IView extends com.kaolafm.kradio.lib.base.mvp.IView {
        void showData(Fragment fragment);
    }


}
