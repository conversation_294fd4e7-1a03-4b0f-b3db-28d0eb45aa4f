<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">


    <com.kaolafm.kradio.lib.widget.tab.SlidingTabLayout
        android:id="@+id/srtl_home_navigation_tab"
        android:layout_width="0dp"
        android:layout_height="@dimen/sliding_tab_layout_height"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/iv_home_nav_allcategray_more"
        app:layout_constraintTop_toTopOf="parent"
        app:tl_indicator_drawable="@drawable/home_nav_arrow"
        app:tl_first_no_padding="true"
        />

    <TextView
        style="@style/home_all_ctg_style"
        android:id="@+id/iv_home_nav_allcategray_more"
        android:layout_marginStart="@dimen/text_size_title4"
        android:gravity="center"
        android:textColor="@color/text_color_white"
        android:textSize="@dimen/text_size_title4"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/srtl_home_navigation_tab"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/iv_home_nav_mongolian_layer"
        android:layout_width="@dimen/m50"
        android:layout_height="0dp"
        android:background="@drawable/home_nav_mongolian_bg_selector"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@id/srtl_home_navigation_tab"
        app:layout_constraintRight_toRightOf="@id/srtl_home_navigation_tab"
        app:layout_constraintTop_toTopOf="@id/srtl_home_navigation_tab" />

</merge>