<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tl="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_subcategory_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/tabLayoutContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:orientation="horizontal">

        <!--二级tab-->
        <!--注意:为了对齐fragment,必须满足:tl_tab_padding + layout_marginLeft == x90-->
        <com.flyco.tablayout.SlidingTabLayout
            android:id="@+id/stb_subcategory_tab_title"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/m56"
            android:background="@drawable/online_tab_category_two_bg"
            android:paddingLeft="@dimen/m10"
            android:paddingRight="@dimen/m10"
            android:visibility="gone"
            app:tl_tab_padding="@dimen/m16"
            app:tl_tab_space_equal="false"
            app:tl_textBold="SELECT"
            tl:tl_indicator_color="@color/transparent"
            tl:tl_textSelectColor="@color/colorWhite"
            tl:tl_textSelectSize="@dimen/online_all_ctg_sub_title_size"
            tl:tl_textUnselectColor="@color/user_info_key_color"
            tl:tl_textsize="@dimen/online_all_ctg_sub_title_size" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/tab_title_ll"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/m56"
            android:layout_toRightOf="@+id/stb_subcategory_tab_title"
            android:visibility="visible"
            tools:visibility="visible">

            <com.kaolafm.kradio.lib.widget.tab.SlidingTabLayout
                android:id="@+id/stb_subcategory_tab_title_two"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/m56"
                android:background="@drawable/online_tab_category_two_bg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tl_indicator_drawable="@drawable/online_tab_indicator_pic"
                app:tl_indicator_gravity="BOTTOM"
                app:tl_tab_padding="@dimen/m20"
                app:tl_textBold="SELECT"
                app:tl_textSelectColor="@color/colorWhite"
                app:kradio_tl_textSelectSize="@dimen/online_all_ctg_sub_title_size"
                app:tl_textSelectSize="@dimen/online_all_ctg_sub_title_size"
                app:tl_textUnselectColor="@color/user_info_key_color"
                app:kradio_tl_textsize="@dimen/online_all_ctg_sub_title_size" />

            <View
                android:id="@+id/slidingTwoGradientLeft"
                android:layout_width="@dimen/m60"
                android:layout_height="@dimen/m56"
                android:layout_gravity="start"
                android:background="@drawable/online_city_tab_gradient_left"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/slidingTwoGradientRight"
                android:layout_width="@dimen/m60"
                android:layout_height="@dimen/m56"
                android:layout_gravity="end"
                android:background="@drawable/online_city_tab_gradient_right"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="@id/stb_subcategory_tab_title_two"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>


        <ImageView
            android:id="@+id/tv_refresh_btn"
            android:layout_width="@dimen/x138"
            android:layout_height="@dimen/y50"
            android:layout_centerInParent="true"
            android:layout_marginEnd="@dimen/y30"
            android:src="@drawable/ic_launcher"
            android:visibility="gone" />

        <com.kaolafm.kradio.common.widget.NotScrollViewPager
            android:id="@+id/vp_subcategory_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/stb_subcategory_tab_title"
            android:overScrollMode="never" />

        <View
            android:id="@+id/tab_title_city_ll"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/stb_subcategory_tab_title"
            android:layout_gravity="right"
            android:layout_marginBottom="@dimen/m20"
            android:background="@color/login_bg"
            android:visibility="gone" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/stb_subcategory_tab_title"
            android:layout_alignLeft="@+id/tab_title_ll">

            <com.kaolafm.kradio.lib.widget.tab.SlidingTabLayout
                android:id="@+id/stb_subcategory_tab_title_city"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/m56"
                android:background="@drawable/online_tab_category_two_bg"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tl_indicator_drawable="@drawable/online_tab_indicator_pic"
                app:tl_textBold="SELECT"
                app:tl_textSelectColor="@color/colorWhite"
                app:kradio_tl_textSelectSize="@dimen/online_all_ctg_sub_title_size"
                app:tl_textSelectSize="@dimen/online_all_ctg_sub_title_size"
                app:tl_textUnselectColor="@color/user_info_key_color"
                app:kradio_tl_textsize="@dimen/online_all_ctg_sub_title_size" />

            <View
                android:id="@+id/slidingCityGradientLeft"
                android:layout_width="@dimen/m60"
                android:layout_height="@dimen/m56"
                android:layout_gravity="start"
                android:background="@drawable/online_city_tab_gradient_left"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/slidingCityGradientRight"
                android:layout_width="@dimen/m60"
                android:layout_height="@dimen/m56"
                android:layout_gravity="end"
                android:background="@drawable/online_city_tab_gradient_right"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="@id/stb_subcategory_tab_title_city"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </RelativeLayout>

    <View
        android:id="@+id/view_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/m1"
        android:background="@color/color_common_line" />


    <com.kaolafm.kradio.common.widget.NotScrollViewPager
        android:id="@+id/vp_subcategory_content2"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:overScrollMode="never"
        android:visibility="gone" />

    <ViewStub
        android:id="@+id/vs_layout_error_page"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />


</LinearLayout>
