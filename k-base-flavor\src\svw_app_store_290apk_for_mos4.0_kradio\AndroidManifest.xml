<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.kaolafm.kradio.flavor">

    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <application
        android:allowBackup="false"
        android:debuggable="false"
        tools:replace="android:allowBackup,android:debuggable">

        <activity
            android:name="com.kaolafm.kradio.flavor.ExitActivity"
            android:launchMode="singleTop"
            android:theme="@android:style/Theme.NoDisplay" />
        <activity
            android:name="com.kaolafm.kradio.flavor.OpenPrivacyActivity"
            android:launchMode="singleTop"
            android:theme="@android:style/Theme.NoDisplay" />
        <receiver android:name=".impl.NetBradcastReceiver" >
            <intent-filter >
                <action android:name="com.cns.android.intent.action.NETWORK_CHANGED" />
            </intent-filter>
        </receiver>
    </application>
</manifest>
