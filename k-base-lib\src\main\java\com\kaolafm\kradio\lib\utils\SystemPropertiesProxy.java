/**
 * Logback: the reliable, generic, fast and flexible logging framework.
 * Copyright (C) 1999-2013, QOS.ch. All rights reserved.
 *
 * This program and the accompanying materials are dual-licensed under
 * either the terms of the Eclipse Public License v1.0 as published by
 * the Eclipse Foundation
 *
 *   or (per the licensee's choosing)
 *
 * under the terms of the GNU Lesser General Public License version 2.1
 * as published by the Free Software Foundation.
 */
package com.kaolafm.kradio.lib.utils;

import android.content.Context;
import android.util.Log;

import java.lang.reflect.Method;

/**
 * A proxy to get Android's global system properties (as opposed
 * to the default process-level system properties). Settings from
 * `adb setprop` can be accessed from this class.
 */
public class SystemPropertiesProxy {

  public static final String TAG = "SystemPropertiesProxy";

  /**
   * 根据给定的Key返回String类型的值
   *
   * @param context 上下文
   * @param key     获取指定信息所需的key
   * @return 返回一个String类型的值，如果不存在该key则返回空字符串
   */
  public static String getString(Context context, String key) {
    String result = "";
    try {
      ClassLoader classLoader = context.getClassLoader();
      @SuppressWarnings("rawtypes")
      Class SystemProperties = classLoader.loadClass("android.os.SystemProperties");
      //参数类型
      @SuppressWarnings("rawtypes")
      Class[] paramTypes = new Class[1];
      paramTypes[0] = String.class;
      Method getString = SystemProperties.getMethod("get", paramTypes);
      //参数
      Object[] params = new Object[1];
      params[0] = key;

      result = (String) getString.invoke(SystemProperties, params);
    } catch (IllegalArgumentException e) {
      //e.printStackTrace();
      //如果key超过32个字符则抛出该异常
      Log.w(TAG, "key超过32个字符");
    } catch (Exception e) {
      result = "";
    }
    return result;
  }
}
