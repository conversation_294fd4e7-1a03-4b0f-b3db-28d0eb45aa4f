package com.kaolafm.kradio.flavor.aop;


import android.provider.Settings;
import android.util.Log;
import android.view.View;

import com.kaolafm.kradio.flavor.carnetwork.api.AuthApiRequest;
import com.kaolafm.kradio.flavor.carnetwork.carnetworkutils.AuthListener;
import com.kaolafm.kradio.flavor.carnetwork.carnetworkutils.CarAuthUtil;
import com.kaolafm.kradio.flavor.carnetwork.carnetworkutils.ChanganAuthManager;
import com.kaolafm.kradio.flavor.carnetwork.carnetworkutils.OuShangAuthConstants;

import com.kaolafm.kradio.k_kaolafm.cp.CP;
import com.kaolafm.kradio.k_kaolafm.home.data.Category;
import com.kaolafm.kradio.k_kaolafm.home.gallery.PageJumper;
import com.kaolafm.kradio.k_kaolafm.home.player.PlayerHelper;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;



import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;

@Aspect
public class AuthAspect {

    private AuthApiRequest mApiRequest;

    public ProceedingJoinPoint mProceedingJoinPoint;

    private boolean isFromTTS = false;//防止语音播报通过后继续调用播放器导致流程走两次


    @Around("execution(* com.kaolafm.sdk.core.mediaplayer.PlayerService.PlayerBinder.start(..))")
    public void checkAuthstart(ProceedingJoinPoint point) throws Throwable {
        Log.i("zsjxxxxxx", "checkAuthstart: .PlayerBinder.start         point = " + point);
        mProceedingJoinPoint = point;
        PlayItem mPlayItem = (PlayItem) point.getArgs()[0];
        checkAction(new AuthListener() {
            @Override
            public void onIsCanUse(boolean isCanUse) {
                Log.i("zsj", "onIsCanUse = " + isCanUse);
                if (isCanUse) {
                    //可以播放
                    try {
                        point.proceed();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                    }
                } else {
                    //不可播放
                    if (mPlayItem != null) {
                        PlayerManager.getInstance().notifyIPlayChangedListener(mPlayItem);
                    }
                }
            }

            @Override
            public void Error(int errCode, String errMsg) {
                //不可播放
                if (mPlayItem != null) {
                    PlayerManager.getInstance().notifyIPlayChangedListener(mPlayItem);
                }
            }
        });

    }


    @Around("execution(* com.kaolafm.sdk.core.mediaplayer.PlayerService.PlayerBinder.play(..))")
    public void checkAuthplay(ProceedingJoinPoint point) throws Throwable {
        Log.i("zsjxxxxxx", "checkAuthplay: .PlayerBinder.play");
        mProceedingJoinPoint = point;
        PlayItem currentPlayItem = PlayerManager.getInstance().getCurPlayItem();
        checkAction(new AuthListener() {
            @Override
            public void onIsCanUse(boolean isCanUse) {
                Log.i("zsj", "onIsCanUse = " + isCanUse);
//                PlayerManager.getInstance().notifyIPlayChangedListener(mPlayItem);
                if (isCanUse) {
                    //可以播放
                    try {
                        point.proceed();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                    }
                } else {
                    //不可播放
                    if (currentPlayItem != null) {
                        PlayerManager.getInstance().notifyIPlayChangedListener(PlayerManager.getInstance().getCurPlayItem());
                    }
                }
            }

            @Override
            public void Error(int errCode, String errMsg) {
                //不可播放
                if (currentPlayItem != null) {
                    PlayerManager.getInstance().notifyIPlayChangedListener(PlayerManager.getInstance().getCurPlayItem());
                }
            }
        });
    }


    @Around("execution(* PlayerManager.start(..))")
    public void checkAuthPlayerManager(ProceedingJoinPoint point) throws Throwable {
        Log.i("zsjxxxxxx", "PlayerManager.start");
        if (CarAuthUtil.AUTH_THROUGH) {
            point.proceed();
        } else {
            PlayItem mPlayItem = (PlayItem) point.getArgs()[0];
            checkAction(new AuthListener() {
                @Override
                public void onIsCanUse(boolean isCanUse) {
                    Log.i("zsj", "onIsCanUse = " + isCanUse);
                    if (isCanUse) {
                        //可以播放
                        try {
                            point.proceed();
                        } catch (Throwable throwable) {
                            throwable.printStackTrace();
                        }
                    } else {
                        //不可播放
                        if (mPlayItem != null) {
                            PlayerManager.getInstance().notifyIPlayChangedListener(mPlayItem);
                        }
                    }
                }

                @Override
                public void Error(int errCode, String errMsg) {
                    //不可播放
                    if (mPlayItem != null) {
                        PlayerManager.getInstance().notifyIPlayChangedListener(mPlayItem);
                    }
                }
            });
        }
    }


    @Around("execution(* com.kaolafm.kradio.k_kaolafm.home.HorizontalHomePlayerFragment.onItemClick(..))")
    public void checkAuthOnItemClickListener(ProceedingJoinPoint point) throws Throwable {
        Log.i("zsjxxxxxx", "HorizontalHomePlayerFragment.onItemClick  CarAuthUtil.AUTH_THROUGH = " + CarAuthUtil.AUTH_THROUGH);
        if (CarAuthUtil.AUTH_THROUGH) {
            point.proceed();
        } else {
            View v = (View) point.getArgs()[0];
            Category.Group group = (Category.Group) point.getArgs()[1];
            Category.Item item = (Category.Item) point.getArgs()[2];
            Log.i("zsjxxxxxx", "HorizontalHomePlayerFragment.onItemClick  item = " + item.toString());


            if (AntiShake.check(1000, v.getId())) {
                return;
            }
            switch (item.operateType) {
                case Category.OPERATE_TYPE_ENTRA:
                    // TODO: 2018/4/25 放到presenter中;
                    PageJumper.getInstance().jumpToAllCategoryPage(item);
                    break;
                case Category.OPERATE_TYPE_LIVE:
                    if (!CarAuthUtil.AUTH_THROUGH) {
                        Log.i("zsj", "onCardClick: mKRadioAuthInter.authStatus() = " + CarAuthUtil.AUTH_THROUGH);
                        //如果流量鉴权不通过，进行流量鉴权的check,不要直接跳转到直播界面
                        checkAction(new AuthListener() {
                            @Override
                            public void onIsCanUse(boolean isCanUse) {
                                Log.i("zsj", "onIsCanUse = " + isCanUse);
                                if (isCanUse) {
                                    //可以播放
                                    try {
                                        PageJumper.getInstance().jumpToLivePage(item);
                                    } catch (Throwable throwable) {
                                        throwable.printStackTrace();
                                    }
                                } else {
                                    //不可播放
                                }
                            }

                            @Override
                            public void Error(int errCode, String errMsg) {
                                //不可播放
                            }
                        });
                    } else {
                        PageJumper.getInstance().jumpToLivePage(item);
                    }
                    break;
                default: {
                    if (!CarAuthUtil.AUTH_THROUGH) {
                        Log.i("zsj", "onCardClick: mKRadioAuthInter.authStatus() = " + CarAuthUtil.AUTH_THROUGH);
                        //如果流量鉴权不通过，也要去触发播放
                        areaItemClickPlay(group, item);
                    } else {
                        if (PlayerHelper.isCurrentAlbum(item.id, item.resType)) {
                            Log.i("zsj", "onCardClick: 当前正在播放,return.");
                            return;
                        }
                        areaItemClickPlay(group, item);
                    }
                }
                break;
            }
        }

    }

    private void areaItemClickPlay(Category.Group group, Category.Item item) {
        if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
            return;
        } else {
            PlayerHelper.PlayParam playParam = PlayerHelper.getPlayParam(group, item);
            if (playParam.cpSource == CP.QQ) {
                PlayerHelper.playQQWithCheckLogin(playParam);
            } else {
                PlayerHelper.play(group, item);
            }
        }
    }

    //开始鉴权
    public void checkAction(final AuthListener mAuthListener) {

        OuShangAuthConstants.BASEURL = OuShangAuthConstants.BASEURL_OFFICIAL;

        if (!CarAuthUtil.checkAuthStatusAndDate()) {
            mAuthListener.onIsCanUse(true);
            return;
        }

        //显示loading状态
        ChanganAuthManager.getInstance().initDialog().runUIshow(OuShangAuthConstants.CODE_LOADING, mProceedingJoinPoint);

        String token = Settings.Global.getString(AppDelegate.getInstance().getContext().getContentResolver(),
                "ca_access_token");

//        String token = Settings.System.getString(AppDelegate.getInstance().getContext().getContentResolver(), "CA_ACCESS_TOKEN");

        ChangAnAuthRequest(mAuthListener, token);

    }


    /**
     * 判断当前网络是否需要鉴权，获取当前的Access token 并 请求联通结果获取流量鉴权结果
     */
    private void ChangAnAuthRequest(AuthListener mListener, String token) {
        String appid = OuShangAuthConstants.APPID;
        String vid = token;
        String itemid = OuShangAuthConstants.ITEMID;
        long timestamp = System.currentTimeMillis();
        Log.i("zsj", "ChangAnAuth  getChangAnAuth  param:  " + "appid = " + appid + ",vid = " + vid + ",itemid = " + itemid + ",timestamp = " + timestamp);
        if (mApiRequest == null) {
            mApiRequest = new AuthApiRequest();
        }
        mApiRequest.getChangAnAuth(appid, vid, itemid, timestamp, new HttpCallback<Integer>() {
            @Override
            public void onSuccess(Integer integer) {
                if (integer <= 0) {
                    ChanganAuthManager.getInstance().initDialog().runUIshow(integer, mProceedingJoinPoint);
                    CarAuthUtil.saveAuthAndDate(String.valueOf(System.currentTimeMillis()), false);
                    mListener.onIsCanUse(false);
                } else if (integer == 1) {
                    ChanganAuthManager.getInstance().initDialog().runUIdismiss();
                    CarAuthUtil.saveAuthAndDate(String.valueOf(System.currentTimeMillis()), true);
                    mListener.onIsCanUse(true);
                }
            }

            @Override
            public void onError(ApiException e) {
                //由于对方接口返回数据与我们的不一致,导致一直走onError.可以通过e.getCode()判断是否成功
                int code = e.getCode();
                Log.i("zsj", "ChangAnAuth getChangAnAuth json :  = " + e.toString());
                if (code <= 0) {
                    ChanganAuthManager.getInstance().initDialog().runUIshow(code, mProceedingJoinPoint);
                    CarAuthUtil.saveAuthAndDate(String.valueOf(System.currentTimeMillis()), false);
                    mListener.onIsCanUse(false);
                } else if (code == 1) {
                    ChanganAuthManager.getInstance().initDialog().runUIdismiss();
                    CarAuthUtil.saveAuthAndDate(String.valueOf(System.currentTimeMillis()), true);
                    mListener.onIsCanUse(true);
                } else if (code == 604 || code == 408) {
                    //超时和其他错误  sdkapi超时errorcode没有暴露  超时状态至为604和408
                    ChanganAuthManager.getInstance().initDialog().runUIshow(OuShangAuthConstants.CODE_TIMEOUT, mProceedingJoinPoint);
                    CarAuthUtil.saveAuthAndDate(String.valueOf(System.currentTimeMillis()), false);
                    mListener.Error(code, e.getMessage());
                } else {
                    ChanganAuthManager.getInstance().initDialog().runUIshow(OuShangAuthConstants.CODE_ERROR, mProceedingJoinPoint);
                    CarAuthUtil.saveAuthAndDate(String.valueOf(System.currentTimeMillis()), false);
                    mListener.Error(code, e.getMessage());
                }
            }
        });

    }

}
