<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:layout_width="@dimen/m604"
        android:layout_height="@dimen/m72"
        android:layout_centerInParent="true"
        android:layout_centerHorizontal="true"
        android:background="@drawable/bg_audio_ad_suspension">

        <com.kaolafm.kradio.common.widget.RoundRectImageView
            android:id="@+id/ad_audio_image"
            android:layout_width="@dimen/m48"
            android:layout_height="@dimen/m48"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/x16"
            app:rriv_radius="@dimen/m12" />

        <LinearLayout
            android:id="@+id/ad_audio_center_view"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/m48"
            android:layout_centerInParent="true"
            android:background="@drawable/bg_ad_audio_suspension_center"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="@dimen/x73"
            android:paddingTop="@dimen/y6"
            android:paddingEnd="@dimen/x68"
            android:paddingBottom="@dimen/y6">

            <!-- CPU优化：删除Lottie动画，使用静态图标 -->
            <ImageView
                android:id="@+id/ad_audio_horn_iv"
                android:layout_width="@dimen/m39"
                android:layout_height="@dimen/m39"
                android:src="@drawable/component_play_icon_2" />

            <TextView
                android:id="@+id/ad_audio_title_tv"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:layout_marginStart="@dimen/m15"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:singleLine="true"
                android:textColor="@color/comprehensive_order_primary_color"
                android:textSize="@dimen/m24"
                tools:text="广告播放中" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_ad_image_label"
            android:layout_width="@dimen/m52"
            android:layout_height="@dimen/m28"
            android:layout_alignParentEnd="true"
            android:layout_margin="@dimen/m4"
            android:background="@drawable/bg_ad_mark2"
            android:gravity="center"
            android:text="@string/ad"
            android:textColor="@color/ad_tag2"
            android:textSize="@dimen/m16"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </RelativeLayout>
</RelativeLayout>