package com.kaolafm.ad.comprehensive;

import android.content.Context;
import android.util.Log;

import com.kaolafm.ad.api.model.AudioAdvert;
import com.kaolafm.ad.comprehensive.control.KradioAdSceneConstants;
import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.ad.comprehensive.implement.AdvertPlayerImpl;
import com.kaolafm.ad.comprehensive.view.KradioAdAudioViewStyleConfig;
import com.kaolafm.ad.comprehensive.view.KradioAudioAdContentView;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.utils.TimerUtil;
import com.kaolafm.kradio.lib.bean.AdPlayItem;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

/**
 * 广告核心管理类，统一管理广告的状态
 *
 * <AUTHOR>
 * @date 2020-01-06
 */
public class KradioAdAudioManager {

    private Context context;

    private static final String TAG = "KradioAdAudioManager";

    public Context getContext() {
        return context;
    }

    public void setContext(Context context) {
        this.context = context;
    }

    private volatile static KradioAdAudioManager instance;

    private KradioAudioAdContentView mKradioAudioAdContentView;

    private KradioAdAudioViewStyleConfig mKradioAdAudioViewStyleConfig;

    AdvertPlayerImpl advertPlayerimpl;

    public AudioAdvert audioAdvert;

    private long jumptimer = 0;

    public AudioAdvert getAudioAdvert() {
        return audioAdvert;
    }

    public void setAudioAdvert(AudioAdvert audioAdvert) {
        this.audioAdvert = audioAdvert;
    }

    public KradioAdAudioManager() {
    }

    public void init() {
        mKradioAdAudioViewStyleConfig = new KradioAdAudioViewStyleConfig();
        advertPlayerimpl = new AdvertPlayerImpl();
        AdvertisingManager.getInstance().setPlayer(advertPlayerimpl);

    }


    public AdvertPlayerImpl getAdvertPlayerimpl() {
        return advertPlayerimpl;
    }

    public void setAdvertPlayerimpl(AdvertPlayerImpl advertPlayerimpl) {
        this.advertPlayerimpl = advertPlayerimpl;
    }

    public static KradioAdAudioManager getInstance() {
        if (instance == null) {
            synchronized (KradioAdAudioManager.class) {
                if (instance == null) {
                    instance = new KradioAdAudioManager();
                }
            }
        }
        return instance;
    }


    /**
     * 重置音频广告，广告时间重置，主要是音频切换，先重置，再请求数据
     */
    public void resetAudioAd() {
        jumptimer = 0;
    }

    public void showAudioAdView(long durition) {
        resetAudioAd();
        setAudioAdDate(durition);
        show(AppManager.getInstance().getMainActivity());
        if (durition != 0) {
            startTimer();
        } else {
            endTimer();
        }
    }

    public void setOnAdViewVisibilityChanged(KradioAudioAdContentView.OnAdViewVisibilityChanged mOnAdViewVisibilityChanged) {
        if (mKradioAudioAdContentView != null)
            mKradioAudioAdContentView.setOnAdViewVisibilityChanged(mOnAdViewVisibilityChanged);
    }

    public boolean isShowing() {
        return mKradioAudioAdContentView != null && mKradioAudioAdContentView.isShowing();
    }

    public void show(Context context) {
        if (context == null) {
            return;
        }
        if (mKradioAudioAdContentView == null) {
            mKradioAudioAdContentView = new KradioAudioAdContentView(context, mKradioAdAudioViewStyleConfig);

//            Glide.with(context).asGif().load(R.drawable.kradio_aduio_ad_horn).into(mKradioAudioAdContentView.getmAudioAdHornImageView());
        }
        mKradioAudioAdContentView.show();
    }

    public String getPlayAudioAd() {
        PlayItem adPlayItem = PlayerManager.getInstance().getCurrentTempTaskPlayItem();
        if (adPlayItem instanceof AdPlayItem) {
            return String.valueOf(((AdPlayItem) adPlayItem).getAdType());
        }
        return null;
    }

    public boolean isNeedReplaceAudioAd(String audio_ad_preplay_type, String audio_ad_playing_type) {
        if (audio_ad_playing_type == null ||
                audio_ad_preplay_type.equals(KradioAdSceneConstants.SPREND_SCENE)) {
            return true;
        }

        if (audio_ad_preplay_type.equals(KradioAdSceneConstants.TIMER_SCENE) &&
                audio_ad_playing_type.equals(KradioAdSceneConstants.SPREND_SCENE)) {
            return false;
        } else {
            return true;
        }
    }

    public void cancel() {
        if (timerUtil != null) {
            timerUtil.cancel();
        }
        Log.i(TAG, ".....cancel = " + mKradioAudioAdContentView);
        if (mKradioAudioAdContentView != null) {
            mKradioAudioAdContentView.cancel();
            mKradioAudioAdContentView = null;
        }
    }


    public void setAudioAdDate(long time) {
        jumptimer = time;
    }

    private TimerUtil timerUtil;
    int timeTemp;

    public void startTimer() {
        if (jumptimer <= 0) {
            return;
        }
        if (timerUtil == null) {
            timerUtil = TimerUtil.newInstance();
        }
        timeTemp = (int) jumptimer / 1000;
        updateTimer(jumptimer);
        Log.i(TAG, "jumptimer = " + jumptimer);
        timerUtil.interval(1000, num -> {
            Log.i(TAG, "timeTemp[0] = " + timeTemp);
            if (--timeTemp <= 0) {
                Log.i(TAG, "已经是非法值了,取消 : " + timeTemp);
                jumptimer = 0;
                timerUtil.cancel();
                endTimer();
                return;
            }
            Log.i(TAG, "timer update = " + timeTemp);
            updateTimer(timeTemp * 1000);
        });
    }

    private void endTimer() {
        if (timerUtil != null) {
            timerUtil.cancel();
        }
        if (mKradioAudioAdContentView != null) {
            mKradioAudioAdContentView.timerend();
        }
    }

    public void mainActivityStart(Context context) {
        if (audioAdvert != null && audioAdvert.getSubtype() == KradioAdSceneConstants.SUB_TYPE_SPRAND_SCREEN) {
            if (advertPlayerimpl.getFlashAudioADShow()) {
                return;
            }
            show(context);
            if (jumptimer <= 0) {
                endTimer();
            }
        }
    }

    private void updateTimer(long timer) {
        if (mKradioAudioAdContentView != null) {
            mKradioAudioAdContentView.updateViewTime(timer);
        }
    }

}
