package com.ecarx.sdk;

/**
 * <AUTHOR>
 **/
public class ECarXTokenManager {
    private String mAccessToken;
    private String mRefreshToken;


    private ECarXTokenManager() {
    }

    private volatile static ECarXTokenManager sInstance;

    public static ECarXTokenManager getInstance() {
        if (sInstance == null) {
            synchronized (ECarXTokenManager.class) {
                if (sInstance == null) {
                    sInstance = new ECarXTokenManager();
                }
            }
        }
        return sInstance;
    }

    public void setToken(AccessTokenResult tokenResult) {
        mAccessToken = tokenResult.getAccessToken();
        mRefreshToken = tokenResult.getRefreshToken();
    }

    public String getAccessToken() {
        return mAccessToken;
    }

    public String getRefreshToken() {
        return mRefreshToken;
    }
}
