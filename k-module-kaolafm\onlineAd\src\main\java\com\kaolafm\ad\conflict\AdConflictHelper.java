package com.kaolafm.ad.conflict;

import android.util.Log;

import com.kaolafm.ad.AdConstant;
import com.kaolafm.ad.api.model.Advert;
import com.kaolafm.ad.api.model.BaseAdvert;
import com.kaolafm.ad.api.model.ImageAdvert;
import com.kaolafm.ad.api.model.InteractionAdvert;
import com.kaolafm.ad.control.KradioAdSceneConstants;
import com.kaolafm.ad.expose.AdvertInterceptor;
import com.kaolafm.ad.expose.AdvertisingLifecycleCallback;
import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;

import java.util.Iterator;
import java.util.concurrent.CopyOnWriteArrayList;

public class AdConflictHelper implements AdvertisingLifecycleCallback, AdvertInterceptor {


    /**
     * 当前正在展示的广告集合
     */
    private CopyOnWriteArrayList<Advert> mRunningAdvert = new CopyOnWriteArrayList<>();

    private static final String TAG = "AdConflictHelper";

    @Override
    public void onCreate(String s, int i) {

    }

    @Override
    public void onStart(Advert advert) {
        addAdvert(advert);
    }

    @Override
    public void intercept(Chain chain) {
        if (TimingSceneGiveUp(chain.advert())) {
            chain.process(null);
        } else {
            chain.process(chain.advert());
        }
    }

    @Override
    public void onClose(Advert advert) {
        remove(advert);
    }

    @Override
    public void onError(String s, int i, Exception e) {

    }

    public void init() {
        Log.i(TAG, "初始化广告冲突处理");
        AdvertisingManager.getInstance().addInterceptor(this);
        AdvertisingManager.getInstance().registerAdvertLifecycleCallback(this);
    }


    private void addAdvert(Advert advert) {

        if (advert == null) {
            return;
        }

        Log.i(TAG, "准备添加广告：" + advert.getSubtype() + " , ad:" + advert);

        if (mRunningAdvert.size() == 0) {
            addRunningAdvert(advert);
        } else {
            checkAdConflict(advert);
        }
    }


    private void addRunningAdvert(Advert advert) {
        Log.i(TAG, "添加展示中的广告：" + advert);
        if (advert == null) {
            return;
        }
        mRunningAdvert.add(advert);
    }

    public void remove(Advert advert) {
        if (advert == null) {
            return;
        }
        Log.i(TAG, "删除展示中的广告：" + advert);
        mRunningAdvert.remove(advert);
    }

    private void checkAdConflict(Advert advert) {

        AdReplaceScene(advert);

        addRunningAdvert(advert);
    }


    /**
     * 通用场景
     * 后者顶掉前者
     *
     * @param advert
     */
    private void AdReplaceScene(Advert advert) {

        if (advert == null) {
            return;
        }

        if (advert.getSubtype() == KradioAdSceneConstants.SUB_TYPE_COLOUM) {
            columnAdCloseInteraction();
            return;
        }

        Iterator iterator = mRunningAdvert.iterator();
        while (iterator.hasNext()) {
            Advert advertsing = (Advert) iterator.next();
            String sessionId = advertsing.getSessionId();
            boolean eq = sessionId.equals(advert.getSessionId());
            Log.i(TAG, "检查是否需要顶替广告 subType：" + advert.getSubtype() + " , sessionId 是否一致:" + eq);
            if (!eq) {
                Log.i(TAG, "删除展示中的广告：" + advertsing + ",sub:" + advertsing.getSubtype());
                if (advertsing instanceof BaseAdvert) {
                    //fix  http://redmine.itings.cn/issues/40023
                    ((BaseAdvert) advertsing).setInteractionAdvert(null);
                }
                skipReport(advertsing);
                //sessionId不一致标示是不同的广告，顶替掉正在展示的ad。
                mRunningAdvert.remove(advertsing);
                AdvertisingManager.getInstance().close(advertsing);
            }
        }

    }

    /**
     * 特别场景
     * 定时舍弃场景
     * 需求：开屏过程中，出现的定时广告需丢弃，如果进入到首页则按照顶替逻辑处理 {@link AdConflictHelper #AdReplaceScene()}或者打电话的过程中丢弃。
     *
     * @param advert
     * @return
     */
    private boolean TimingSceneGiveUp(Advert advert) {

        if (advert.getSubtype() == AdConstant.TYPE_TIMED_ADVERT) {
            if (PlayerManagerHelper.getInstance().isPlayingAd()) {
                //如果正在播放其他广告就移除播放
                Log.d(TAG, "定时广告, 正在播放其他广告就移除播放, 丢弃场景");
                return true;
            }
            //如果是打电话过程中，出现的定时就进行丢弃，否则就检查是不是开屏的过程中遇到定时
            if (PlayerManagerHelper.getInstance().invalidPlayAction()) {
                return true;
            } else {

                if (AdConflict.isShowHome) {
                    return false;
                }

                boolean spreading = false;

                for (Advert ad : mRunningAdvert) {
                    int subType = ad.getSubtype();
                    if (subType == KradioAdSceneConstants.SUB_TYPE_SPRAND_SCREEN) {
                        spreading = true;
                        break;
                    }
                }

                Log.i(TAG, "定时广告丢弃场景：" + spreading);

                return spreading;
            }
        } else {
            return false;
        }
    }

    /**
     * 特别场景
     * 出现某广告时，又出现了首屏连图的情况
     * 需求：无论任何广告展示过程中，出现连图广告（无论是否配置了二次互动），只关闭其广告的二次互动。
     *
     * @return
     */
    public void columnAdCloseInteraction() {
        for (Advert ad : mRunningAdvert) {
            if (ad instanceof InteractionAdvert) {
                Log.i(TAG, "关闭二次互动:" + ad);
                AdvertisingManager.getInstance().close(ad);
            } else if (ad instanceof BaseAdvert) {
                //会出现开屏二次互动比连图后展示，所以产品要求即使后展示也要关闭。
                Log.i(TAG, "关闭base二次互动:" + ad);
                ((BaseAdvert) ad).setInteractionAdvert(null);
            }
        }
    }

    private void skipReport(Advert advert) {

        if (advert instanceof InteractionAdvert) {
            //二次互动只有在点击时候上报
            return;
        }

        if (advert.getType() == KradioAdSceneConstants.AD_TYPE_AUDIO_IMAGE && advert instanceof ImageAdvert) {
            //音图只上报音的跳过
            return;
        }

        AdvertisingManager.getInstance().getReporter().skip(advert);
    }

}
