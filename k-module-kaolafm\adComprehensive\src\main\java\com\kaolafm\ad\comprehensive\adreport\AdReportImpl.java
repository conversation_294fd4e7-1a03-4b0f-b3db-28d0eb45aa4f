package com.kaolafm.ad.comprehensive.adreport;

import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.ad.AdConstant;
import com.kaolafm.ad.api.internal.model.AdvertReportEntity;
import com.kaolafm.ad.api.model.Advert;
import com.kaolafm.ad.expose.AdvertisingReporter;
import com.kaolafm.ad.report.AdReportAgent;
import com.kaolafm.ad.report.bean.AdReportMonitorEvent;
import com.kaolafm.ad.report.bean.AdReportPlayEndEvent;

import java.util.Map;

public class AdReportImpl implements AdvertisingReporter {

    private static final String TAG = "AdReportImpl";

    @Override
    public void display(Advert advert) {
        if (isNull(advert)) {
            return;
        }
        Log.i(TAG,"display");
        AdReportAgent.onEvent(advert.getSessionId(), AdReportAgent.EventType.PV);
        reportMonitorPV(advert);
    }

    @Override
    public void endDisplay(Advert advert) {
        if (isNull(advert)) {
            return;
        }
        Log.i(TAG,"endDisplay");
        AdReportAgent.onEvent(advert.getSessionId(), AdReportAgent.EventType.DISPLAY_END);
    }

    @Override
    public void interruptDisplay(Advert advert) {
        if (isNull(advert)) {
            return;
        }
        Log.i(TAG,"interruptDisplay");
        AdReportAgent.onEvent(advert.getSessionId(), AdReportAgent.EventType.DISPLAY_INTERRUPT);
    }

    @Override
    public void play(Advert advert) {
        if (isNull(advert)) {
            return;
        }
        Log.i(TAG,"play");
        AdReportAgent.onEvent(advert.getSessionId(), AdReportAgent.EventType.PLAY_START);
        reportMonitorPV(advert);
    }

    @Override
    public void endPlay(Advert advert,long playTime) {
        if (isNull(advert)) {
            return;
        }
        Log.i(TAG,"endPlay ："+playTime);
        AdReportPlayEndEvent adReportPlayEndEvent = new AdReportPlayEndEvent();
        adReportPlayEndEvent.setEventType(AdReportAgent.EventType.PLAY_END);
        adReportPlayEndEvent.setSessionId(advert.getSessionId());
        adReportPlayEndEvent.setPlayTime(playTime / 1000);
        AdReportAgent.onEvent(adReportPlayEndEvent);
    }

    @Override
    public void click(Advert advert) {
        if (isNull(advert)) {
            return;
        }
        Log.i(TAG,"click");
        AdReportAgent.onEvent(advert.getSessionId(), AdReportAgent.EventType.CLICK);
        reportMonitorClick(advert);
    }

    @Override
    public void skip(Advert advert) {
        if (isNull(advert)) {
            return;
        }
        Log.i(TAG,"skip");
        AdReportAgent.onEvent(advert.getSessionId(), AdReportAgent.EventType.SKIP);
    }

    @Override
    public void displayInteraction(Advert advert) {
        if (isNull(advert)) {
            return;
        }
        Log.i(TAG,"displayInteraction");
        AdReportAgent.onEvent(advert.getSessionId(), AdReportAgent.EventType.DISPLAY_MORE_INTERACTION);
    }

    @Override
    public void endInteraction(Advert advert) {
        if (isNull(advert)) {
            return;
        }
        Log.i(TAG,"endInteraction");
        AdReportAgent.onEvent(advert.getSessionId(), AdReportAgent.EventType.MODE_INTERACTION_END);
    }

    private boolean isNull(Advert advert){
        Log.i(TAG,"isNull:"+advert);
        return advert == null;
    }

    private AdvertReportEntity getAdvertReportEntity(Advert advert){
        Map map = advert.getExtra();

        if (map == null) {
            return null;
        }

        if (!map.containsKey(AdConstant.KEY_EXTRA_REPORT)) {
            return null;
        }


        AdvertReportEntity advertReportEntity = (AdvertReportEntity) map.get(AdConstant.KEY_EXTRA_REPORT);

        Log.i(TAG,"reportMonitor advertReportEntity:"+advertReportEntity);

        return advertReportEntity;
    }

    private void reportMonitorPV(Advert advert){

        AdvertReportEntity advertReportEntity = getAdvertReportEntity(advert);

        Log.i(TAG,"reportMonitorPV advertReportEntity:"+advertReportEntity);

        if (advertReportEntity == null) {
            return;
        }

        int monitorType = advertReportEntity.getMonitorType();
        Log.i(TAG,"reportMonitorPV monitorType:"+advertReportEntity.getMonitorType());

        if (monitorType == 0) {
            return;
        }

        String pvUrl = advertReportEntity.getPvMonitorUrl();

        Log.i(TAG,"reportMonitorPV pvUrl:"+pvUrl);

        if (!TextUtils.isEmpty(pvUrl)) {
            AdReportMonitorEvent adReportMonitorEvent = new AdReportMonitorEvent(getMonitorType(monitorType));
            adReportMonitorEvent.setCreativeId(advert.getId());
            adReportMonitorEvent.setSessionId(advert.getSessionId());
            adReportMonitorEvent.setMonitorUrl(pvUrl);
            AdReportAgent.onEvent(adReportMonitorEvent);
        }

    }

    private void reportMonitorClick(Advert advert){
        AdvertReportEntity advertReportEntity = getAdvertReportEntity(advert);

        Log.i(TAG,"reportMonitorClick advertReportEntity:"+advertReportEntity);

        if (advertReportEntity == null) {
            return;
        }

        int monitorType = advertReportEntity.getMonitorType();
        Log.i(TAG,"reportMonitorClick monitorType:"+advertReportEntity.getMonitorType());

        if (monitorType == 0) {
            return;
        }

        String clickUrl = advertReportEntity.getClickMonitorUrl();
        Log.i(TAG,"reportMonitorClick clickUrl:"+clickUrl);

        if (!TextUtils.isEmpty(clickUrl)) {
            AdReportMonitorEvent adReportMonitorEvent = new AdReportMonitorEvent(getMonitorType(monitorType));
            adReportMonitorEvent.setCreativeId(advert.getId());
            adReportMonitorEvent.setSessionId(advert.getSessionId());
            adReportMonitorEvent.setMonitorUrl(clickUrl);
            AdReportAgent.onEvent(adReportMonitorEvent);
        }
    }

    private int getMonitorType(int type){
        int monitor;
        switch (type) {
            case 1:
                monitor = AdReportAgent.EventType.MIAOZHEN_MONITOR;
                break;
            case 3:
                monitor = AdReportAgent.EventType.TALKING_DATA_MONITOR;
                break;
            default:
                monitor = -1;
                break;
        }
        return monitor;
    }

}
