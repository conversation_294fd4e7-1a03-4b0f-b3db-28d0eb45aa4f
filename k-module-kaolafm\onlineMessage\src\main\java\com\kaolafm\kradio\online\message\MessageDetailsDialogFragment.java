package com.kaolafm.kradio.online.message;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.PointF;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import androidx.annotation.DrawableRes;
import androidx.annotation.IdRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.transition.Transition;
import com.davemorrissey.labs.subscaleview.ImageSource;
import com.davemorrissey.labs.subscaleview.ImageViewState;
import com.davemorrissey.labs.subscaleview.SubsamplingScaleImageView;
import com.kaolafm.kradio.common.report.ReportUtil;
import com.kaolafm.kradio.basedb.manager.MessageDaoManager;
import com.kaolafm.kradio.basedb.entity.meaasge.CrashMessageBean;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.DateUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.online.common.utils.AppDateUtils;
import com.kaolafm.kradio.online.common.utils.LinearGradientFontSpan;
import com.kaolafm.message.utils.OnlineMessageUtils;
import com.kaolafm.opensdk.api.CrashMessageBaseBean;
import com.kaolafm.opensdk.crash.CrashPlayerHelper;
import com.kaolafm.opensdk.crash.Icrashstate;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonClickReportEvent;
import com.kaolafm.report.event.MessageShowReportEvent;
import com.kaolafm.report.event.PageShowReportEvent;
import com.kaolafm.report.util.ReportConstants;

import java.io.File;
import java.util.List;

/**
 * 消息泡泡详情
 * 调用示例：
 * MessageBubbleDialogFragment dialogFragment = (MessageBubbleDialogFragment) new Dialogs.Builder().setType(Dialogs.TYPE_MESSAGE_BUBBLE).create();
 * dialogFragment.setBubbleType(MessageBubbleDialogFragment.TYPE_DANGER)
 * .setSceneBitmap(BitmapFactory.decodeResource(getResources(), R.drawable.online_message_bubble_thunderbolt))
 * .setTitle("国家应急广播")
 * .setSubTitle("石家庄市发布暴雨雷电红色预警")
 * .setButtons(new ArrayList<MessageBubbleDialogFragment.MessageBubbleButton>() {{
 * add(new MessageBubbleDialogFragment.MessageBubbleButton("查看详情", R.id.online_message_bubble_button_1_id));
 * add(new MessageBubbleDialogFragment.MessageBubbleButton("倒计时", 20L, R.id.online_message_bubble_button_2_id));
 * add(new MessageBubbleDialogFragment.MessageBubbleButton("删除", R.drawable.online_search_icon_delete, R.id.online_message_bubble_button_3_id));
 * add(new MessageBubbleDialogFragment.MessageBubbleButton("确认", R.drawable.online_user_no_login_icon, 15, R.id.online_message_bubble_button_4_id));
 * }}, new View.OnClickListener() {
 *
 * @Override public void onClick(View v) {
 * int id = v.getId();
 * if (id == R.id.online_message_bubble_button_1_id) {
 * Log.e(TAG, "点击按钮：1");
 * } else if (id == R.id.online_message_bubble_button_2_id) {
 * Log.e(TAG, "点击按钮：2");
 * } else if (id == R.id.online_message_bubble_button_3_id) {
 * Log.e(TAG, "点击按钮：3");
 * } else if (id == R.id.online_message_bubble_button_4_id) {
 * Log.e(TAG, "点击按钮：4");
 * }
 * }
 * }).show(getSupportFragmentManager(), "MessageBubbleDialogFragment");
 */
public class MessageDetailsDialogFragment extends Dialog {
    private RelativeLayout root_view;
    private ImageView mXfermodeView;
    private ImageView bubbleIcon;
    private ImageView play_iv;
    private ImageView bubble_qr_iv;
    private ImageView bubble_pic_iv;
    private TextView bubbleTitle;
    private TextView bubbleSubTitle;
    private TextView bubbleContent;
    private LinearLayout bubbleButtonParent;

    private LinearLayout bubble_qr_ll;
    private RelativeLayout bubble_pic_rl;
    private RelativeLayout pic_big_rl;
    private SubsamplingScaleImageView pic_big_iv;
    private ImageView msg_tips_pic_iv;
    private View loading;

    private String mTitle, mSubTitle;
    private String mIconResource;
    private CrashMessageBean crashMessageBean;
    private Bitmap destBitmap;
    private Bitmap srcBitmap;

    private boolean hasSceneBg = false;
    private List<MessageBubbleButton> mButtons;
    private View.OnClickListener mViewClickListener;
    protected long startTime = -1;
    private boolean isPlay;//是否正在播放
    private boolean isMsgList;//是否从消息列表打开的


    public MessageDetailsDialogFragment(@NonNull Context context) {
        super(context, R.style.FullScreenDialogTheme);
    }


//    public static MessageDetailsDialogFragment create() {
//        MessageDetailsDialogFragment fragment = new MessageDetailsDialogFragment();
//        return fragment;
//    }
//
//    @Override
//    public String getPageId() {
//        return Constants.PAGE_ID_MESSAGE_DETAILS;
//    }


    @SuppressLint("LongLogTag")
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.online_layout_message_details);
        isPlay = CrashPlayerHelper.getInstance().isPlayCrash;
        root_view = findViewById(R.id.root_view);
        mXfermodeView = findViewById(R.id.bubbleBg);
        bubbleIcon = findViewById(R.id.bubbleIcon);
        bubbleTitle = findViewById(R.id.bubbleTitle);
        play_iv = findViewById(R.id.play_iv);
        bubbleSubTitle = findViewById(R.id.bubbleSubTitle);
        bubbleContent = findViewById(R.id.bubbleContent);
        bubbleButtonParent = findViewById(R.id.bubbleButtonParent);
        loading = findViewById(R.id.loading);

        bubble_qr_iv = findViewById(R.id.bubble_qr_iv);
        bubble_pic_iv = findViewById(R.id.bubble_pic_iv);
        bubble_qr_ll = findViewById(R.id.bubble_qr_ll);
        bubble_pic_rl = findViewById(R.id.bubble_pic_rl);
        pic_big_iv = findViewById(R.id.pic_big_iv);
        pic_big_rl = findViewById(R.id.pic_big_rl);
        msg_tips_pic_iv = findViewById(R.id.msg_tips_pic_iv);

        Window window = this.getWindow();
        //设置弹出位置
//        window.setGravity(Gravity.BOTTOM | Gravity.START);

        int matchParent = ViewGroup.LayoutParams.MATCH_PARENT;//父布局的宽度

        WindowManager.LayoutParams lp = window.getAttributes();
        lp.width = matchParent;
        lp.height = matchParent;
//        lp.x = matchParent;
//        lp.y = 300;  //设置出现的高度，距离顶部
        window.setAttributes(lp);
        //去除系统自带的margin
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        //背景全透明
        window.setDimAmount(0f);
        //设置弹出动画
        window.setWindowAnimations(R.style.OnlineMessageBubbleAnimation);
        //设置对话框大小
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);

        setBubbleType();
        setTitle(crashMessageBean.getHeadline());
        try {
            setSubTitle(DateUtil.formatMillis("yyyy-MM-dd HH:mm", Long.parseLong(crashMessageBean.getSendTime())));
        } catch (Exception e) {
            Log.e("MessageDetailsDialogFragment", e.getMessage());
        }
        setBubbleContent(crashMessageBean.getEventDescription());
        setSceneBitmap(crashMessageBean.getMsgDetailsBgUrl());
        if (isPlay) {
            play_iv.setImageDrawable(ResUtil.getDrawable(R.drawable.online_msg_play_not_icon));
        } else {
            play_iv.setImageDrawable(ResUtil.getDrawable(R.drawable.online_msg_play_icon));
        }
//        setButtons(mButtons, mViewClickListener);
        loading.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

            }
        });
        if (isPlay) {
            CrashPlayerHelper.getInstance().setIcrashstate(new Icrashstate() {
                @Override
                public void onCrashstate(int i) {
                    //播放完成
                    OnlineMessageUtils.getInstance().setShowMsgDetails(false);
                    isPlay = false;
                    play_iv.setImageDrawable(ResUtil.getDrawable(R.drawable.online_msg_play_icon));
                }

                @Override
                public void onBufferingUpdate(CrashMessageBaseBean crashMessageBaseBean) {
                    //缓冲完成
                }
            });
        }
        root_view.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                MessageDaoManager.getInstance().updateLook(crashMessageBean.getMsgId());
                if (CrashPlayerHelper.getInstance().isPlay()) {
                    CrashPlayerHelper.getInstance().playEnd();
                }
                dismiss();
            }
        });
        play_iv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isPlay) {
                    return;
                }
                if (crashMessageBean != null) {
                    isPlay = true;
                    play_iv.setImageDrawable(ResUtil.getDrawable(R.drawable.online_msg_play_not_icon));
                    loading.setVisibility(View.VISIBLE);
                    CrashPlayerHelper.getInstance().setIcrashstate(new Icrashstate() {
                        @Override
                        public void onCrashstate(int i) {
                            //播放完成
                            OnlineMessageUtils.getInstance().setShowMsgDetails(false);
                            isPlay = false;
                            play_iv.setImageDrawable(ResUtil.getDrawable(R.drawable.online_msg_play_icon));
                        }

                        @Override
                        public void onBufferingUpdate(CrashMessageBaseBean crashMessageBaseBean) {
                            //缓冲完成
                            OnlineMessageUtils.getInstance().setShowMsgDetails(true);
                            loading.setVisibility(View.GONE);
                        }
                    });
                    CrashPlayerHelper.getInstance().addImmediatelyplayDate(AppDateUtils.getInstance().changeDate(crashMessageBean)).startPlay();
                }
                //上报点击
                ReportUtil.addMessageClike(getPageId(), crashMessageBean.getMsgContentType(), crashMessageBean.getMsgId());
                ButtonClickReportEvent event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_MSG_PLAY_ICON);
                ReportHelper.getInstance().addEvent(event);
            }
        });
        mXfermodeView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                @SuppressLint("WrongConstant") MessageDetailsDialogFragment dialogFragment
//                        = (MessageDetailsDialogFragment) new Dialogs.Builder().setType(Dialogs.TYPE_MESSAGE_DETAILS).create();
//                dialogFragment.setCrashMessageBean(crashMessageBean)
//                        .show(getChildFragmentManager(), "MessageDetailsDialogFragment");
////                CrashPlayerHelper.getInstance().addImmediatelyplayDate(AppDateUtils.getInstance().changeDate(messageBeanList.get(position))).startPlay();
//                MessageDaoManager.getInstance().updateLook(crashMessageBean.getMsgId());
//                dismiss();
            }
        });
        if (crashMessageBean.getMsgLevel().equals("3")) {
            //应急广播要显示标题下边的图片
            msg_tips_pic_iv.setVisibility(View.VISIBLE);
            ImageLoader.getInstance().displayImage(getContext(), crashMessageBean.getMsgTipsPicUrl()
                    , msg_tips_pic_iv);
        }
        if (crashMessageBean.getMsgStyleType() == 1 || crashMessageBean.getMsgStyleType() == 3) {
            //有图的模式
            bubble_pic_rl.setVisibility(View.VISIBLE);
            bubble_qr_ll.setVisibility(View.GONE);
            ImageLoader.getInstance().displayImage(getContext(), crashMessageBean.getMsgDetailsQrUrl()
                    , bubble_pic_iv, ResUtil.getDrawable(R.drawable.message_pic_error));
//            ImageLoader.getInstance().displayImage(getContext(), crashMessageBean.getMsgDetailsQrUrl()
//                    , pic_big_iv, ResUtil.getDrawable(R.drawable.message_pic_error));
            loadBigPic(crashMessageBean.getMsgDetailsQrUrl());
            bubble_pic_rl.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    //显示大图
                    pic_big_rl.setVisibility(View.VISIBLE);
                }
            });
            pic_big_rl.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    pic_big_rl.setVisibility(View.GONE);
                }
            });
        } else {
            //无图的模式
            bubble_pic_rl.setVisibility(View.GONE);
            if (!TextUtils.isEmpty(crashMessageBean.getMsgDetailsQrUrl())) {
                //如果是无图模式。并且二维码字段不为空就显示二维码
                bubble_qr_ll.setVisibility(View.VISIBLE);
                ImageLoader.getInstance().displayImage(getContext(), crashMessageBean.getMsgDetailsQrUrl(), bubble_qr_iv);
            }

        }

    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        MessageDaoManager.getInstance().updateLook(crashMessageBean.getMsgId());
        if (CrashPlayerHelper.getInstance().isPlay()) {
            CrashPlayerHelper.getInstance().playEnd();
        }
        dismiss();
    }

    /**
     * 加载大图显示
     */
    private void loadBigPic(String url) {
        Glide.with(getContext()).downloadOnly()
                .load(url)
                .into(new SimpleTarget<File>() {
                    @Override
                    public void onResourceReady(@NonNull File resource, @Nullable Transition<? super File> transition) {
                        pic_big_iv.setMinimumScaleType(SubsamplingScaleImageView.SCALE_TYPE_CENTER_CROP);
                        pic_big_iv.setImage(ImageSource.uri(resource.getAbsolutePath()),
                                new ImageViewState(0f, new PointF(0f, 0f), 0));
                    }
                });
    }

    public MessageDetailsDialogFragment setMsgList(boolean isMsgList) {
        this.isMsgList = isMsgList;
        return this;
    }

    @Override
    public void dismiss() {
        super.dismiss();
        OnlineMessageUtils.getInstance().setShowMsgDetails(false);
        isPlay = false;
        reportPageShowEvent();

    }

    @Override
    public void show() {
        super.show();
        //上报
//        ReportUtil.addMessageShow(getPageId(), crashMessageBean.getMsgContentType(), crashMessageBean.getMsgId());
        MessageShowReportEvent messageShowReportEvent = new MessageShowReportEvent();
        messageShowReportEvent.setRadiotype(crashMessageBean.getMsgContentType());
        messageShowReportEvent.setRemarks2(crashMessageBean.getMsgId());
        messageShowReportEvent.setPageid(getPageId());
        Log.d("1233", "------------" + isMsgList);
        if (isMsgList) {
            messageShowReportEvent.setPage(Constants.PAGE_ID_MESSAGE);
        } else {
            messageShowReportEvent.setPage(Constants.PAGE_ID_MESSAGE_CARD);
        }
        ReportHelper.getInstance().addEvent(messageShowReportEvent);
    }

    public MessageDetailsDialogFragment setTitle(String title) {
        this.mTitle = title;
        if (bubbleTitle != null)
            bubbleTitle.setText(title);
        return this;
    }

    public MessageDetailsDialogFragment setSubTitle(String subtitle) {
        this.mSubTitle = subtitle;
        if (bubbleSubTitle != null)
            bubbleSubTitle.setText(getRadiusGradientSpan(subtitle, 0));
        return this;
    }

    public MessageDetailsDialogFragment setBubbleContent(String content) {

        if (bubbleContent != null)
            bubbleContent.setText(content);
        return this;
    }

    public MessageDetailsDialogFragment setCrashMessageBean(CrashMessageBean messageBean) {
        this.crashMessageBean = messageBean;
        return this;
    }


    private MessageDetailsDialogFragment setSceneBitmap(String url) {
        ImageLoader.getInstance().displayImage(getContext(), url, mXfermodeView);
        return this;
    }

//    public MessageDetailsDialogFragment setSceneBitmap(Bitmap bitmap) {
//        return setSceneBitmap(bitmap, true);
//    }

    private void setDestBitmap(Bitmap bitmap) {
        if (destBitmap != null && !destBitmap.isRecycled()) {
            destBitmap.recycle();
        }
        destBitmap = bitmap;

    }

    /**
     * 灾害等级:1.红色预警 2.橙色预警 3.黄色预警 4.蓝色预警
     */
    public MessageDetailsDialogFragment setBubbleType() {

        int resourceId = 0;
        switch (crashMessageBean.getMsgLevel()) {
//            resourceId = R.drawable.online_message_bubble_warning;黄色预留，一期不做
            case "1"://情感化问候
//                resourceId = R.drawable.online_message_blue_icon;
                break;
            case "2"://节目预约、社交 、礼物等
//                resourceId = R.drawable.online_message_green_icon;
                break;
            case "3"://应急广播消息
//                switch (crashMessageBean.getEventLevel()) {
//                    case "1":
                resourceId = R.drawable.online_message_red_iconr;
//                        break;
//                    case "2":
//                        resourceId = R.drawable.online_message_bubble_cheng;
//                        break;
//                    case "3":
//                        resourceId = R.drawable.online_message_bubble_huang;
//                        break;
//                    case "4":
//                        resourceId = R.drawable.online_message_bubble_lan;
//                        break;
//                }
                break;
        }
        if (resourceId != 0) {
            bubbleIcon.setImageResource(resourceId);
        }
        return this;
    }

    public MessageDetailsDialogFragment setButtons(List<MessageBubbleButton> buttonTexts, View.OnClickListener onClickListener) {
        this.mButtons = buttonTexts;
        this.mViewClickListener = onClickListener;
        if (bubbleButtonParent == null) return this;
        boolean hasButton = bubbleButtonParent.getChildCount() > 0;
        bubbleButtonParent.removeAllViews();
        if (buttonTexts == null || buttonTexts.isEmpty()) {
            if (hasButton) {
                Bitmap resetDestBitmap;
                if (hasSceneBg) {
                    resetDestBitmap = BitmapFactory.decodeResource(getContext().getResources(), R.drawable.online_message_bubble_nonebutton_bg_board);
                } else {
                    resetDestBitmap = BitmapFactory.decodeResource(getContext().getResources(), R.drawable.online_message_bubble_nonebutton_bg);
                }
                setDestBitmap(resetDestBitmap);
            }
            return this;
        }

        int perPadding = ResUtil.getDimen(R.dimen.y6) / (buttonTexts.size() - 1);
        for (int i = 0; i < buttonTexts.size(); i++) {
            MessageBubbleButton bt = buttonTexts.get(i);
            View button = createButtonView(bubbleButtonParent, bt, perPadding * i);
            button.setOnClickListener(onClickListener);
            if (i < buttonTexts.size() - 1)
                createButtonDividerView(bubbleButtonParent, perPadding * (i + 1) / 2);
        }
        if (!hasButton) {
            Bitmap resetDestBitmap;
            if (hasSceneBg) {
                resetDestBitmap = BitmapFactory.decodeResource(getContext().getResources(), R.drawable.online_message_bubble_button_bg);
            } else {
                resetDestBitmap = BitmapFactory.decodeResource(getContext().getResources(), R.drawable.online_message_bubble_button_bg);
            }
            setDestBitmap(resetDestBitmap);
        }
        return this;
    }

    private View createButtonDividerView(ViewGroup parent, int dPadding) {
        View view = new View(getContext());
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(ResUtil.getDimen(R.dimen.x1), LinearLayout.LayoutParams.MATCH_PARENT);
        params.setMargins(0, ResUtil.getDimen(R.dimen.y21) - dPadding, 0, ResUtil.getDimen(R.dimen.y26) + dPadding);
        view.setLayoutParams(params);
        view.setBackgroundResource(R.drawable.online_message_bubble_button_divider);
        parent.addView(view);
        return view;
    }

    /**
     * 创建按钮
     *
     * @param parent
     * @param bt
     * @param dPadding 按钮从左到右依次上移，该值表示当前按钮应上移的距离，px
     * @return
     */
    private View createButtonView(ViewGroup parent, MessageBubbleButton bt, int dPadding) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.online_bubble_button, parent, false);
        view.setPadding(view.getPaddingStart(), view.getPaddingTop() - dPadding, view.getPaddingEnd(), view.getPaddingBottom() + dPadding);

        ImageView iconIv = view.findViewById(R.id.messageBubbleIconIv);
        TextView titleTv = view.findViewById(R.id.messageBubbleTextTv);
        TextView timerTv = view.findViewById(R.id.messageBubbleTimerTv);
        if (bt.icon == -1) {
            iconIv.setVisibility(View.GONE);
        } else {
            iconIv.setImageResource(bt.icon);
        }
        if (bt.timer == -1) {
            timerTv.setVisibility(View.GONE);
        } else {
            timerTv.setText(String.format(ResUtil.getString(R.string.online_message_bubble_timer), bt.timer));
        }
        titleTv.setText(bt.text);
        view.setId(bt.id);
        parent.addView(view);
        return view;
    }

    public static final class MessageBubbleButton {

        private String text;
        @DrawableRes
        private int icon = -1;
        private long timer = -1;
        @IdRes
        private int id;

        public MessageBubbleButton(String text, int id) {
            this.text = text;
            this.id = id;
        }

        public MessageBubbleButton(String text, long timer, int id) {
            this.text = text;
            this.timer = timer;
            this.id = id;
        }

        public MessageBubbleButton(String text, int icon, int id) {
            this.text = text;
            this.icon = icon;
            this.id = id;
        }

        public MessageBubbleButton(String text, int icon, long timer, int id) {
            this.text = text;
            this.icon = icon;
            this.timer = timer;
            this.id = id;
        }
    }

    public String getPageId() {
        return Constants.PAGE_ID_MESSAGE_DETAILS;
    }

    @Override
    protected void onStart() {
        super.onStart();
        String pageId = getPageId();
        if (!StringUtil.isEmpty(pageId)) {
            Log.i(ReportConstants.REPORT_TAG, "设置pageid= " + pageId);
            ReportHelper.getInstance().setPage(pageId);
        }

        startTime = System.currentTimeMillis();
    }
    public SpannableStringBuilder getRadiusGradientSpan(String string, int lineHeight) {
        SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder(string);
        LinearGradientFontSpan span = new LinearGradientFontSpan(
                ResUtil.getColor(R.color.online_activity_dateils_time_text_start_color)
                , ResUtil.getColor(R.color.online_activity_dateils_time_text_end_color));
        spannableStringBuilder.setSpan(span, 0, spannableStringBuilder.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spannableStringBuilder;

    }
    /**
     * 页面曝光事件上报
     */
    private void reportPageShowEvent() {
        long duration = System.currentTimeMillis() - startTime;
        if (startTime < 0 || duration < 300) {
            return;
        }

        PageShowReportEvent event = new PageShowReportEvent();
        event.setPageId(getPageId());
        event.setPageTime(String.valueOf(duration));
        ReportHelper.getInstance().addEvent(event);
        Log.i("BaseDialogFragment", "report=" + duration);
        startTime = -1;
    }
}
