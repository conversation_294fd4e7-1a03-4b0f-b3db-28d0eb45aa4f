package com.kaolafm.kradio.online.player.mvp;

import com.kaolafm.kradio.lib.base.mvp.IView;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import java.util.List;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-04-17 11:34
 ******************************************/

public interface BroadcastProgramListView extends IView {
    void onGetBroadcastProgramListDataSuccess(List<PlayItem> playItemArrayList, boolean updateView);

    void onGetBroadcastProgramListDataError(int code, String msg);
}
