//package com.kaolafm.kradio.lib.bean;
//
//import android.text.TextUtils;
//
//import com.google.gson.annotations.SerializedName;
//import com.kaolafm.opensdk.api.CrashMessageButtonActionBean;
//
//public class CrashMessageDto {
//    @SerializedName("msgId")
//    private String msgId;
//    @SerializedName("headline")
//    private String headline;
//    @SerializedName("eventDescription")
//    private String eventDescription;
//    @SerializedName("headlinePath")
//    private String headlinePath;
//    @SerializedName("eventDescriptionPath")
//    private String eventDescriptionPath;
//    /**
//     * 一句话简介
//     */
//    @SerializedName("eventDescriptionExtract")
//    private String eventDescriptionExtract;
//    @SerializedName("sendTime")
//
//
//    private String sendTime;
//    /**
//     * 插播类型 0-立即插播  1-延时插播 2-非插播消息
//     */
//    @SerializedName("playType")
//    private String playType;
//    /**
//     * 消息紧急类型 一共三级
//     */
//    @SerializedName("msgLevel")
//    private String msgLevel;
//    /**
//     * 消息内容类型
//     */
//    @SerializedName("msgContentType")
//    private String msgContentType ;
//    @SerializedName("tipsTitle")
//    private String tipsTitle;
//    @SerializedName("msgDetailsStartTime")
//    private String msgDetailsStartTime;
//    @SerializedName("msgDetailsEndTime")
//    private String msgDetailsEndTime;
//    @SerializedName("msgDetailsBgUrl")
//    private String msgDetailsBgUrl;
//    @SerializedName("msgDetailsQrUrl")
//    private String msgDetailsQrUrl;
//    @SerializedName("msgDetailsBtbStyleLeft")
//    private int msgDetailsBtbStyleLeft;
//    @SerializedName("msgDetailsBtbStyleRight")
//    private int msgDetailsBtbStyleRight;
//    @SerializedName("msgDetailsBtnTextLeft")
//    private String msgDetailsBtnTextLeft;
//    @SerializedName("msgDetailsBtnTextRight")
//    private String msgDetailsBtnTextRight;
//    @SerializedName("msgDetailsBtnActionLeft")
//    private CrashMessageButtonActionDto msgDetailsBtnActionLeft;
//    @SerializedName("msgDetailsBtnActionRight")
//    private CrashMessageButtonActionDto msgDetailsBtnActionRight;
//    @SerializedName("emergencyId")
//    private String emergencyId;
//    @SerializedName("publishTime")
//    private String publishTime;
//    @SerializedName("sender")
//    private String sender;
//    @SerializedName("eventType")
//    private String eventType;
//    @SerializedName("msgStyleType")
//    private int msgStyleType;//0-文 1-文+图 2-文+按钮 3-文+图+按钮
//    /**
//     * 灾害等级:1.红色预警 2.橙色预警 3.黄色预警 4.蓝色预警
//     */
//    @SerializedName("eventLevel")
//    private String eventLevel;
//    @SerializedName("isLook")
//    private boolean isLook;
//    @SerializedName("msgTipsPicUrl")
//    private String msgTipsPicUrl;
//    /**
//     * 在线电台版消息小卡背景图
//     */
//    @SerializedName("cardBgUrl")
//    private String cardBgUrl;
//    /**
//     * 综合版消息小卡背景图
//     */
//    @SerializedName("comprehensiveCardBgUrl ")
//    private String comprehensiveCardBgUrl ;
//
//    /**
//     * 消息泡泡/卡片处共用的ICON
//     */
//    @SerializedName("msgIconUrl")
//    private String msgIconUrl ;
//
//    private CrashMessageButtonActionDto msgBubbleBtnActionLeft;
//    private CrashMessageButtonActionDto msgBubbleBtnActionRight;
//    private String msgType;
//    private long startTime;
//    private long endTime;
//    private String msgPicUrl;
//    private String msgBubbleBtnTextLeft;
//    private String msgBubbleBtnTextRight;
//    private String msgDetailsPicUrl;
//
//    public String getMsgPicUrl() {
//        return msgPicUrl;
//    }
//
//    public void setMsgPicUrl(String msgPicUrl) {
//        this.msgPicUrl = msgPicUrl;
//    }
//
//    public String getMsgBubbleBtnTextLeft() {
//        return msgBubbleBtnTextLeft;
//    }
//
//    public void setMsgBubbleBtnTextLeft(String msgBubbleBtnTextLeft) {
//        this.msgBubbleBtnTextLeft = msgBubbleBtnTextLeft;
//    }
//
//    public String getMsgBubbleBtnTextRight() {
//        return msgBubbleBtnTextRight;
//    }
//
//    public void setMsgBubbleBtnTextRight(String msgBubbleBtnTextRight) {
//        this.msgBubbleBtnTextRight = msgBubbleBtnTextRight;
//    }
//
//    public String getMsgDetailsPicUrl() {
//        return msgDetailsPicUrl;
//    }
//
//    public void setMsgDetailsPicUrl(String msgDetailsPicUrl) {
//        this.msgDetailsPicUrl = msgDetailsPicUrl;
//    }
//
//    public long getStartTime() {
//        return startTime;
//    }
//
//    public void setStartTime(long startTime) {
//        this.startTime = startTime;
//    }
//
//    public long getEndTime() {
//        return endTime;
//    }
//
//    public void setEndTime(long endTime) {
//        this.endTime = endTime;
//    }
//
//    public String getMsgType(){
//        return this.msgType;
//    }
//
//    public void setMsgType(String msgType){
//        this.msgType = msgType;
//    }
//
//    public void setMsgBubbleBtnActionLeft(CrashMessageButtonActionDto bean){
//        this.msgBubbleBtnActionLeft = bean;
//    }
//    public CrashMessageButtonActionDto getMsgBubbleBtnActionLeft(){
//        return this.msgBubbleBtnActionLeft;
//    }
//    public void setMsgBubbleBtnActionRight(CrashMessageButtonActionDto bean){
//        this.msgBubbleBtnActionRight = bean;
//    }
//    public CrashMessageButtonActionDto getMsgBubbleBtnActionRight(){
//        return this.msgBubbleBtnActionRight;
//    }
//
//    public String getComprehensiveCardBgUrl() {
//        return comprehensiveCardBgUrl;
//    }
//
//    public void setComprehensiveCardBgUrl(String comprehensiveCardBgUrl) {
//        this.comprehensiveCardBgUrl = comprehensiveCardBgUrl;
//    }
//
//    public String getCardBgUrl() {
//        return cardBgUrl;
//    }
//
//    public void setCardBgUrl(String cardBgUrl) {
//        this.cardBgUrl = cardBgUrl;
//    }
//
//    public String getEventDescriptionExtract() {
//        return eventDescriptionExtract;
//    }
//
//    public void setEventDescriptionExtract(String eventDescriptionExtract) {
//        this.eventDescriptionExtract = eventDescriptionExtract;
//    }
//
//    public void setMsgTipsPicUrl(String msgTipsPicUrl) {
//        this.msgTipsPicUrl = msgTipsPicUrl;
//    }
//
//    public String getMsgContentType() {
//        return msgContentType;
//    }
//
//    public void setMsgContentType(String msgContentType) {
//        this.msgContentType = msgContentType;
//    }
//
//    public int getMsgStyleType() {
//        return msgStyleType;
//    }
//
//    public void setMsgStyleType(int msgStyleType) {
//        this.msgStyleType = msgStyleType;
//    }
//
//    public boolean isLook() {
//        return isLook;
//    }
//
//    public void setLook(boolean look) {
//        isLook = look;
//    }
//
//    public String getMsgId() {
//        return this.msgId;
//    }
//
//    public void setMsgId(String msgId) {
//        this.msgId = msgId;
//    }
//
//    public String getHeadline() {
//        return this.headline;
//    }
//
//    public void setHeadline(String headline) {
//        this.headline = headline;
//    }
//
//    public String getEventDescription() {
//        return this.eventDescription;
//    }
//
//    public void setEventDescription(String eventDescription) {
//        this.eventDescription = eventDescription;
//    }
//
//    public String getHeadlinePath() {
//        return this.headlinePath;
//    }
//
//    public void setHeadlinePath(String headlinePath) {
//        this.headlinePath = headlinePath;
//    }
//
//    public String getEventDescriptionPath() {
//        return this.eventDescriptionPath;
//    }
//
//    public void setEventDescriptionPath(String eventDescriptionPath) {
//        this.eventDescriptionPath = eventDescriptionPath;
//    }
//
//    public String getSendTime() {
//        return this.sendTime;
//    }
//
//    public void setSendTime(String sendTime) {
//        this.sendTime = sendTime;
//    }
//
//    public String getPlayType() {
//        return this.playType;
//    }
//
//    public void setPlayType(String playType) {
//        this.playType = playType;
//    }
//
//    public String getMsgLevel() {
//        return this.msgLevel;
//    }
//
//    public void setMsgLevel(String msgLevel) {
//        this.msgLevel = msgLevel;
//    }
//
//    public String getTipsTitle() {
//        return this.tipsTitle;
//    }
//
//    public void setTipsTitle(String tipsTitle) {
//        this.tipsTitle = tipsTitle;
//    }
//
//    public String getMsgDetailsStartTime() {
//        return this.msgDetailsStartTime;
//    }
//
//    public void setMsgDetailsStartTime(String msgDetailsStartTime) {
//        this.msgDetailsStartTime = msgDetailsStartTime;
//    }
//
//    public String getMsgDetailsEndTime() {
//        return this.msgDetailsEndTime;
//    }
//
//    public void setMsgDetailsEndTime(String msgDetailsEndTime) {
//        this.msgDetailsEndTime = msgDetailsEndTime;
//    }
//
//    public String getMsgDetailsBgUrl() {
//        return this.msgDetailsBgUrl;
//    }
//
//    public void setMsgDetailsBgUrl(String msgDetailsBgUrl) {
//        this.msgDetailsBgUrl = msgDetailsBgUrl;
//    }
//
//    public String getMsgDetailsQrUrl() {
//        return this.msgDetailsQrUrl;
//    }
//
//    public void setMsgDetailsQrUrl(String msgDetailsQrUrl) {
//        this.msgDetailsQrUrl = msgDetailsQrUrl;
//    }
//
//    public int getMsgDetailsBtbStyleLeft() {
//        return this.msgDetailsBtbStyleLeft;
//    }
//
//    public void setMsgDetailsBtbStyleLeft(int msgDetailsBtbStyleLeft) {
//        this.msgDetailsBtbStyleLeft = msgDetailsBtbStyleLeft;
//    }
//
//    public int getMsgDetailsBtbStyleRight() {
//        return this.msgDetailsBtbStyleRight;
//    }
//
//    public void setMsgDetailsBtbStyleRight(int msgDetailsBtbStyleRight) {
//        this.msgDetailsBtbStyleRight = msgDetailsBtbStyleRight;
//    }
//
//    public String getMsgDetailsBtnTextLeft() {
//        return this.msgDetailsBtnTextLeft;
//    }
//
//    public void setMsgDetailsBtnTextLeft(String msgDetailsBtnTextLeft) {
//        this.msgDetailsBtnTextLeft = msgDetailsBtnTextLeft;
//    }
//
//    public String getMsgDetailsBtnTextRight() {
//        return this.msgDetailsBtnTextRight;
//    }
//
//    public void setMsgDetailsBtnTextRight(String msgDetailsBtnTextRight) {
//        this.msgDetailsBtnTextRight = msgDetailsBtnTextRight;
//    }
//
//    public CrashMessageButtonActionDto getMsgDetailsBtnActionLeft() {
//        return this.msgDetailsBtnActionLeft;
//    }
//
//    public void setMsgDetailsBtnActionLeft(CrashMessageButtonActionDto msgDetailsBtnActionLeft) {
//        this.msgDetailsBtnActionLeft = msgDetailsBtnActionLeft;
//    }
//
//    public CrashMessageButtonActionDto getMsgDetailsBtnActionRight() {
//        return this.msgDetailsBtnActionRight;
//    }
//
//    public void setMsgDetailsBtnActionRight(CrashMessageButtonActionDto msgDetailsBtnActionRight) {
//        this.msgDetailsBtnActionRight = msgDetailsBtnActionRight;
//    }
//
//    public String getEmergencyId() {
//        return this.emergencyId;
//    }
//
//    public void setEmergencyId(String emergencyId) {
//        this.emergencyId = emergencyId;
//    }
//
//    public String getPublishTime() {
//        return this.publishTime;
//    }
//
//    public void setPublishTime(String publishTime) {
//        this.publishTime = publishTime;
//    }
//
//    public String getSender() {
//        return this.sender;
//    }
//
//    public void setSender(String sender) {
//        this.sender = sender;
//    }
//
//    public String getEventType() {
//        return this.eventType;
//    }
//
//    public void setEventType(String eventType) {
//        this.eventType = eventType;
//    }
//
//    public String getEventLevel() {
//        if (TextUtils.isEmpty(this.eventLevel)) {
//            this.eventLevel = "0";
//        }
//        return this.eventLevel;
//    }
//
//    public void setEventLevel(String eventLevel) {
//        this.eventLevel = eventLevel;
//    }
//
//    public boolean getIsLook() {
//        return this.isLook;
//    }
//
//    public void setIsLook(boolean isLook) {
//        this.isLook = isLook;
//    }
//
//    public String getMsgTipsPicUrl() {
//        return this.msgTipsPicUrl;
//    }
//
//    public void setMsgIconUrl(String msgIconUrl){
//        this.msgIconUrl = msgIconUrl;
//    }
//    public String getMsgIconUrl(){
//        return this.msgIconUrl;
//    }
//
//    @Override
//    public String toString() {
//        return "CrashMessageBean{" +
//                "msgId='" + msgId + '\'' +
//                ", headline='" + headline + '\'' +
//                ", eventDescription='" + eventDescription + '\'' +
//                ", headlinePath='" + headlinePath + '\'' +
//                ", eventDescriptionPath='" + eventDescriptionPath + '\'' +
//                ", eventDescriptionExtract='" + eventDescriptionExtract + '\'' +
//                ", sendTime='" + sendTime + '\'' +
//                ", playType='" + playType + '\'' +
//                ", msgLevel='" + msgLevel + '\'' +
//                ", msgContentType='" + msgContentType + '\'' +
//                ", tipsTitle='" + tipsTitle + '\'' +
//                ", msgDetailsStartTime='" + msgDetailsStartTime + '\'' +
//                ", msgDetailsEndTime='" + msgDetailsEndTime + '\'' +
//                ", msgDetailsBgUrl='" + msgDetailsBgUrl + '\'' +
//                ", msgDetailsQrUrl='" + msgDetailsQrUrl + '\'' +
//                ", msgDetailsBtbStyleLeft=" + msgDetailsBtbStyleLeft +
//                ", msgDetailsBtbStyleRight=" + msgDetailsBtbStyleRight +
//                ", msgDetailsBtnTextLeft='" + msgDetailsBtnTextLeft + '\'' +
//                ", msgDetailsBtnTextRight='" + msgDetailsBtnTextRight + '\'' +
//                ", msgDetailsBtnActionLeft='" + msgDetailsBtnActionLeft + '\'' +
//                ", msgDetailsBtnActionRight='" + msgDetailsBtnActionRight + '\'' +
//                ", emergencyId='" + emergencyId + '\'' +
//                ", publishTime='" + publishTime + '\'' +
//                ", sender='" + sender + '\'' +
//                ", eventType='" + eventType + '\'' +
//                ", msgStyleType=" + msgStyleType +
//                ", eventLevel='" + eventLevel + '\'' +
//                ", isLook=" + isLook +
//                ", msgTipsPicUrl='" + msgTipsPicUrl + '\'' +
//                ", cardBgUrl='" + cardBgUrl + '\'' +
//                ", comprehensiveCardBgUrl='" + comprehensiveCardBgUrl + '\'' +
//                ", msgIconUrl='" + msgIconUrl + '\'' +
//                '}';
//    }
//}
