package com.kaolafm.kradio.home.comprehensive.item;


import com.kaolafm.kradio.component.ui.base.cell.Card;

/**
 * 首页card.
 * <AUTHOR>
 * @date 2019-08-21
 */
public class HomeCard extends Card {

    /**
     * 父数据所在集合的位置。用于定位。
     */
    public int parentPosition;

    @Override
    public String toString() {
        return "HomeCard{" +
                "parentPosition=" + parentPosition +
                ", subtitle='" + subtitle + '\'' +
                ", code='" + code + '\'' +
                '}';
    }
}
