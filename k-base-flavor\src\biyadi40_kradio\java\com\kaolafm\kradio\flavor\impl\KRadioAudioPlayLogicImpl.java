package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.content.Intent;
import android.util.Log;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.common.bean.HistoryItem;
import com.kaolafm.kradio.k_kaolafm.home.player.PlayerHelper;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.k_kaolafm.home.HorizontalHomePlayerFragment;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioPlayLogicInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioBroadcastHistoryInter;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.player.bean.BroadcastRadioSimpleData;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;

import static com.kaolafm.kradio.receiver.BYDMediaModeReceiver.MEDIA_MODE_ACTION;

import java.util.List;

/**
 * <pre>
 *     <AUTHOR> Wenchl
 *     e-mail : <EMAIL>
 *     time   : 2019/07/28
 *     desc   :
 *     version: 1.0
 * </pre>
 */
public class KRadioAudioPlayLogicImpl implements KRadioAudioPlayLogicInter {
    @Override
    public boolean autoPlayAudio(Object... args) {
        return false;
    }

    @Override
    public boolean requestAudioFocus(Object... args) {
        boolean flag = PlayerManager.getInstance().requestAudioFocus();
        return flag;
    }

    @Override
    public boolean resumeAudioPlayLogic(Object... args) {
        if (args != null && args.length > 0) {
//            Activity activity = ((HorizontalHomePlayerFragment) args[0]).getActivity();
            Activity activity = (Activity) args[0];
            if (activity != null) {
                Intent intent = activity.getIntent();

                String mode = null;
                if (intent != null) {
                    mode = intent.getStringExtra(MEDIA_MODE_ACTION);
                }
                Log.i("byd KRadioAudioPlayLogicImpl", "MainActivity  mode : " + mode);

                if (MEDIA_MODE_ACTION.equals(mode)) {
                    //回到考拉FM必须要先强占音频焦点
                    if (PlayerManager.getInstance().getCurrentAudioFocusStatus() < 0) {
                        requestAudioFocus();
                    }

                    PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
                    if (playItem != null && !PlayerManager.getInstance().isPlaying()) {
                        PlayerManagerHelper.getInstance().switchPlayerStatus(true);
                    }
                } else {
                    if (PlayerManager.getInstance().isPauseFromUser()) {
                        Log.i("byd KRadioAudioPlayLogicImpl", "resumeAudioPlayLogic  return pause from user  ");
                        requestAudioFocus();
                        return false;
                    }
                    if (!PlayerManager.getInstance().isPlaying()) {
                        Log.i("byd KRadioAudioPlayLogicImpl", "need resume play ");
                        requestAudioFocus();
                        if (PlayerManager.getInstance().getPlayListControl() != null) {
                            Log.i("byd KRadioAudioPlayLogicImpl", "switchPlayerStatus ");
                            PlayerManagerHelper.getInstance().switchPlayerStatus(true);
                        } else {
                            playNetOrLocal();
                        }
                    }
                }
            }
        }
        return false;
    }

    public void playNetOrLocal() {
        Log.i("byd KRadioAudioPlayLogicImpl", "playNetOrLocal ");
        HistoryManager.getInstance().getHistoryList(new HttpCallback<List<HistoryItem>>() {
            @Override
            public void onSuccess(List<HistoryItem> historyList) {
                if (!ListUtil.isEmpty(historyList)) {
                    Log.d("byd KRadioAudioPlayLogicImpl", "onSuccess-" + historyList.size());
                    playHistory(historyList.get(0));
                } else {
                    Log.d("byd KRadioAudioPlayLogicImpl", "historyList.isEmpty");
                }
            }

            @Override
            public void onError(ApiException e) {
                Log.d("byd KRadioAudioPlayLogicImpl", "ApiException." + e.toString());
            }
        });
    }

    private void playHistory(HistoryItem historyItem) {
        PlayerLogUtil.log(getClass().getSimpleName(), "playHistory.....");
        if (StringUtil.equals(String.valueOf(PlayerConstants.RESOURCES_TYPE_BROADCAST), historyItem.getType())) {
            KRadioBroadcastHistoryInter inter = ClazzImplUtil.getInter("KRadioBroadcastHistoryImpl");
            if (inter != null) {
                long currentId = Long.valueOf(historyItem.getRadioId());
                inter.onBroadcastHistory(currentId);
            } else {
                BroadcastRadioSimpleData data = new BroadcastRadioSimpleData();
                data.setBroadcastId(Long.valueOf(historyItem.getRadioId()));
                data.setImg(historyItem.getPicUrl());
                data.setName(historyItem.getRadioTitle());
                PlayerManagerHelper.getInstance().addBroadcastRadioSimpleItem(data);
            }
        }
        PlayerHelper.play(historyItem);
    }

    @Override
    public boolean restartAudioPlayLogic(Object... args) {
        return false;
    }

    @Override
    public boolean doStartInPlay(Object... args) {
        if (!PlayerManager.getInstance().isPlaying() && AppDelegate.getInstance().isAppForeground()) {
            PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
            PlayerManagerHelper.getInstance().startPlayItemInList(playItem);
            // PlayerManagerHelper.getInstance().switchPlayerStatus(true);
        }
        return true;
    }
}