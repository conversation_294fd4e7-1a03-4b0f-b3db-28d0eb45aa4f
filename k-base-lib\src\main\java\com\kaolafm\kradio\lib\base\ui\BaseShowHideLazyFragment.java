package com.kaolafm.kradio.lib.base.ui;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.util.Log;
import android.view.View;

import com.kaolafm.kradio.lib.base.mvp.IPresenter;

/**
 * 懒加载fragment，结合viewpager使用可以实现页面显示在加载数据，不预加载下一页
 *
 * <AUTHOR>
 * @date 2018/4/27
 */

public abstract class BaseShowHideLazyFragment<P extends IPresenter> extends BaseShowHideFragment<P> {

    /**
     * 是否已经初始化
     */
    private boolean isInit = false;

    /**
     * 是否已经加载过数据
     */
    protected boolean isLoaded = false;
    private boolean isVisibleToUser;

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        if (isVisibleToUser && !isInit) {
            isInit = true;
            isLoaded = true;
            lazyLoad();
        } else {
            isInit = true;
        }
    }

    /**
     * 此函数针对BaseFragment做了空实现，不能去掉
     * @param view
     */
    @Override
    protected void changeViewLayoutForStatusBar(View view) {

    }

    @Override
    protected void addFragmentRootViewPadding(View view) {

    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        this.isVisibleToUser = isVisibleToUser;
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser && isInit && !isLoaded) {
            isLoaded = true;
            lazyLoad();
        }
        Log.d("BaseLazyFragment","isLoaded = "+isLoaded+" isVisibleToUser = "+isVisibleToUser);
        //下边的代码打开后,会导致后一页划出时,是空白的.
        //注释掉后,会导致,横竖屏切换,页面item出现重叠.
        if (getView() != null) {
            getView().setVisibility(isVisibleToUser ? View.VISIBLE : View.GONE);
        }
    }

    /**
     * 懒加载数据
     */
    protected abstract void lazyLoad();

}
