<?xml version="1.0" encoding="utf-8"?>
<com.kaolafm.kradio.lib.widget.ratio.RatioConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rcl_item_home_content"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:wh_ratio="0.719:1">

    <View
        android:id="@+id/bg_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:background="@drawable/card_big_bottom_all_corners" />

    <com.kaolafm.kradio.lib.widget.square.SquareLayout
        android:id="@+id/sv_item_home_place"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.kaolafm.kradio.component.ui.base.view.OvalImageView
            android:id="@+id/iv_item_home_cover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:rid_type="3"
            app:layout_constraintDimensionRatio="w,1:1"
            app:oval_radius="@dimen/default_radius_img"
            android:scaleType="centerCrop" />

        <View
            android:id="@+id/view_item_home_cover_bg"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/card_big_fg" />

        <com.kaolafm.kradio.component.ui.base.view.OvalImageView
            android:id="@+id/vip_icon"
            android:layout_width="@dimen/m62"
            android:layout_height="@dimen/m30"
            android:layout_alignStart="@+id/iv_item_home_cover"
            android:layout_alignTop="@+id/iv_item_home_cover"
            android:scaleType="fitStart"
            app:rid_type="1"
            app:oval_radius="@dimen/default_radius_img"
            tools:src="@drawable/comprehensive_icon_vip" />

        <com.kaolafm.kradio.component.ui.base.view.RateView
            android:id="@+id/vs_layout_playing"
            android:layout_width="@dimen/m23"
            android:layout_height="@dimen/m23"
            android:layout_marginEnd="@dimen/x18"
            android:layout_marginBottom="@dimen/x18"
            android:layout_alignParentBottom="true"
            android:layout_alignParentEnd="true"
            app:lottie_autoPlay="true"
            app:lottie_fileName="lottie/rate.json"
            app:lottie_loop="true"/>
    </com.kaolafm.kradio.lib.widget.square.SquareLayout>

    <TextView
        android:id="@+id/tv_item_home_ad_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_ad_mark"
        android:paddingLeft="@dimen/x10"
        android:paddingTop="@dimen/y5"
        android:paddingRight="@dimen/x10"
        android:paddingBottom="@dimen/y5"
        android:text="@string/ad"
        android:textColor="@color/home_bar_player_living_text_color"
        android:textSize="@dimen/text_size0"
        android:visibility="gone"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/view_item_home_text_bg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@drawable/card_big_bottom"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/sv_item_home_place" />

    <TextView
        android:id="@+id/tv_item_home_title"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:ellipsize="end"
        android:gravity="center"
        android:maxEms="7"
        android:lines="2"
        android:paddingStart="@dimen/home_item_title_padding"
        android:paddingEnd="@dimen/home_item_title_padding"
        android:background="@drawable/card_big_bottom"
        android:textColor="@color/card_big_title_text_color"
        android:textSize="@dimen/text_size4_vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/sv_item_home_place"
        tools:text="二货一箩筐二货一箩筐二货一箩筐一箩二货一箩筐一箩筐" />

    <ImageView
        android:id="@+id/no_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

</com.kaolafm.kradio.lib.widget.ratio.RatioConstraintLayout>
