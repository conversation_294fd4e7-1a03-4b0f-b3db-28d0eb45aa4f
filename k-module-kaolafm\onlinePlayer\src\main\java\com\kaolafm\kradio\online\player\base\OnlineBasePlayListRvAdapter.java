package com.kaolafm.kradio.online.player.base;

import androidx.recyclerview.widget.RecyclerView;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import java.util.List;

public abstract class OnlineBasePlayListRvAdapter extends BaseAdapter<PlayItem> {
    private final String TAG = OnlineBasePlayListRvAdapter.class.getSimpleName();
    //用来重新加载直播项入场动画。开始显示列表时直播项也只显示普通样式，一段时间后重新渲染
    protected boolean needInit = false;
    //当前正在播放的item,为null时，表示正在听直播内容，如果用户手动选择了节目（直播或回放），将会有对应的值
    private PlayItem playingItem = null;
    //记录正在播放的item的position
    protected int selectPosition = -1;
    //选中item的id
    protected long selectId = 0;
    //忽略attach动画的item的position。用于在切换节目播放时移除先一个播放节目的背景时禁止其attach动画。
    private int ignoreAttachAnimationItemPosition = -1;

    public OnlineBasePlayListRvAdapter(List<PlayItem> dataList) {
        super(dataList);
        resetPlayingToNormal();
    }

    public long getSelectId() {
        return selectId;
    }

    public void setSelectId(long selectId) {
        this.selectId = selectId;
    }

    public int getSelectPosition() {
        return selectPosition;
    }

    public PlayItem getPlayingItem() {
        return playingItem;
    }

    @Override
    public void setDataList(List<PlayItem> dataList, boolean notifyDataSetChanged) {
        super.setDataList(dataList, notifyDataSetChanged);
        resetPlayingToNormal();
    }

    /**
     * 重置播放项为普通项样式
     * 渲染时将不会显示背景
     */
    public void resetPlayingToNormal() {
        this.needInit = false;
        //每次都重新计算正在直播的item，防止出现错误
        //如果当前数据中有正在直播的数据，找到下标
        //只有第一次显示列表以及每次全部刷新列表时进行操作
        this.selectPosition = findSelectPosition();
        if (this.selectPosition != -1 && getItemCount() > this.selectPosition)
            selectId = getItemData(this.selectPosition).getAudioId();
    }

    /**
     * 重置播放项为普通项样式
     * 渲染时将不会显示背景
     */
    public void resetPlayingToNormalAndClearBackground() {
        this.needInit = false;
        unSelectItem();
        //每次都重新计算正在直播的item，防止出现错误
        //如果当前数据中有正在直播的数据，找到下标
        //只有第一次显示列表以及每次全部刷新列表时进行操作
        this.selectPosition = findSelectPosition();
        if (this.selectPosition != -1 && getItemCount() > this.selectPosition)
            selectId = getItemData(this.selectPosition).getAudioId();
    }

    /**
     * 查找当前正在播放的PlayItem的位置
     *
     * @return
     */
    public int findSelectPosition() {
        int selectPosition = -1;
        //开始时playingItem=null,这是认为正在收听直播节目
        this.playingItem = PlayerManager.getInstance().getCurPlayItem();
        if (playingItem == null) {
            return selectPosition;
        }
        //用户手动切换节目后，playingItem有值，则拿到用户回听的节目
        for (int i = 0; i < getItemCount(); i++) {
            if (getItemData(i).getAudioId() == playingItem.getAudioId()) {
                selectPosition = i;
                break;
            }
        }
        return selectPosition;
    }

    /**
     * 选中
     *
     * @param position
     */
    public void selectItem(int position) {
        //移除已经选中的item背景
        unSelectItem();
        //渲染最新选中的item背景
        this.selectPosition = position;
        if (this.selectPosition != -1 && getItemCount() > this.selectPosition)
            selectId = getItemData(this.selectPosition).getAudioId();
        reRenderPlayingItem();
    }

    /**
     * 高亮选中的item。
     * 如果当前selectPosition==-1，说明
     */
    public void reRenderPlayingItem() {
        this.needInit = true;
        if (this.selectPosition != -1) {
            notifyItemChanged(selectPosition);
        }
    }

    /**
     * 取消选中
     */
    public void unSelectItem() {
        if (selectId != 0) {
            int selectP = -1;
            for (int i = 0; i < getDataList().size(); i++) {
                if (getItemData(i).getAudioId() == selectId) {
                    selectP = i;
                    break;
                }
            }
            if (this.selectPosition != selectP && selectP != -1) this.selectPosition = selectP;
        }
        ignoreAttachAnimationItemPosition = this.selectPosition;
        this.selectPosition = -1;
        if (ignoreAttachAnimationItemPosition != -1) {
            notifyItemChanged(ignoreAttachAnimationItemPosition);
        }
    }

    public void notifyItemChanged(int position, boolean isShowAnimation) {
        if (isShowAnimation) {
            this.ignoreAttachAnimationItemPosition = -1;
        } else {
            this.ignoreAttachAnimationItemPosition = position;
        }
        notifyItemChanged(position);
    }

    @Override
    public void onViewAttachedToWindow(BaseHolder<PlayItem> holder) {
        super.onViewAttachedToWindow(holder);
        if (this.ignoreAttachAnimationItemPosition == holder.getAdapterPosition()) {
            this.ignoreAttachAnimationItemPosition = -1;
            return;
        }
        addAnimation(holder);
    }


    private void addAnimation(RecyclerView.ViewHolder holder) {
        //  添加入场动画
        Animation animation = AnimationUtils.loadAnimation(holder.itemView.getContext(), R.anim.online_player_anim_item_play_item);
        holder.itemView.setAnimation(animation);
    }

    public abstract void notifyAdapterPlayItemPaused(PlayItem playItem);

    public abstract void notifyAdapterPlayItemResume(PlayItem playItem);
}
