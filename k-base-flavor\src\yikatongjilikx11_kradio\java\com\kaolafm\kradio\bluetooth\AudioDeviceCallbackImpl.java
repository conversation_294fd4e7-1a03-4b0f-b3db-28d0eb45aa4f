package com.kaolafm.kradio.bluetooth;

import android.media.AudioDeviceCallback;
import android.media.AudioDeviceInfo;
import android.os.Build;
import androidx.annotation.RequiresApi;
import android.util.Log;

import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.player.logic.PlayerManager;

@RequiresApi(api = Build.VERSION_CODES.M)
public class AudioDeviceCallbackImpl extends AudioDeviceCallback {
    private static final String TAG = "AudioDeviceCallbackImpl";

    @Override
    public void onAudioDevicesAdded(AudioDeviceInfo[] addedDevices) {
        super.onAudioDevicesAdded(addedDevices);
        Log.i(TAG, "onAudioDevicesAdded");
        bluetoothStateChangedOnPsd(addedDevices, true);
    }

    @Override
    public void onAudioDevicesRemoved(AudioDeviceInfo[] removedDevices) {
        super.onAudioDevicesRemoved(removedDevices);
        Log.i(TAG, "onAudioDevicesRemoved");
        bluetoothStateChangedOnPsd(removedDevices, false);
    }

    //在psd上蓝牙连接状态判断
    private void bluetoothStateChangedOnPsd(AudioDeviceInfo[] deviceInfos, boolean isAdded) {
        VehicleDisplayManager manager = VehicleDisplayManager.getInstance();
        for (AudioDeviceInfo info: deviceInfos) {
            Log.i(TAG, "type=" + info.getType());
            if (info.getType() == AudioDeviceInfo.TYPE_BLUETOOTH_A2DP
                    && manager.isInitSuccess()
                    && manager.getPsdDisplayId() == manager.getCurrentDisplayId()) {
                Log.i(TAG, "bluetooth change");
                BluetoothStateManager.getInstance().setBluetoothAddedOnPsd(isAdded);
                if (PlayerManagerHelper.getInstance().isPlaying()) {
                    PlayerManagerHelper.getInstance().pause(false);
                }
                PlayerManager.getInstance().abandonAudioFocus();
                return;
            }
        }

    }
}
