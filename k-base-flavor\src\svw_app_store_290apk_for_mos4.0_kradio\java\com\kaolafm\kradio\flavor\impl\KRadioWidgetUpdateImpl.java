package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Popup;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;


import com.kaolafm.kradio.flavor.BuildConfig;
import com.kaolafm.kradio.flavor.ExitActivity;
import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.base.flavor.KRadioWidgetUpdateInter;
import com.kaolafm.opensdk.account.token.AccessTokenManager;
import com.kaolafm.opensdk.account.token.KaolaAccessToken;


public class KRadioWidgetUpdateImpl implements KRadioWidgetUpdateInter {

    private static final String TAG = "KRadioWidgetUpdateImpl";

    @Override
    public void updateWidget(Context context) {
        boolean ishow = false;
        if (BuildConfig.DEBUG){
            return;
        }
        try {
            KaolaAccessToken kaolaAccessToken = AccessTokenManager.getInstance().getKaolaAccessToken();
            String openId = kaolaAccessToken.getOpenId();
            if (TextUtils.isEmpty(openId)) {
                ishow = true;
            }
        } catch (Exception e) {
            e.printStackTrace();
            ishow = true;
        }
        if (ishow) {
            showPopup((Activity) context);
        }
    }

    private void showPopup(Activity activity) {
        Popup popup = new Popup(activity.getString(R.string.popu_msg), activity.getString(R.string.popu_noopenid));
        //点击取消后退出应用
        Intent intent = new Intent(activity, ExitActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(activity, 9, intent, PendingIntent.FLAG_UPDATE_CURRENT);
        popup.setActionOne(activity.getString(R.string.popu_ok), pendingIntent);
        NotificationManager notificationManager = (NotificationManager) activity.getSystemService(Context.NOTIFICATION_SERVICE);
        notificationManager.addPopup(9, popup);
    }
}
