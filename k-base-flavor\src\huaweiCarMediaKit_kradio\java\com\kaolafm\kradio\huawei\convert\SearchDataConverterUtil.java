package com.kaolafm.kradio.huawei.convert;

import com.huawei.carmediakit.bean.Compilation;
import com.huawei.carmediakit.bean.MediaElement;
import com.huawei.carmediakit.bean.SearchResult;
import com.kaolafm.opensdk.api.search.model.SearchProgramBean;

import java.util.ArrayList;
import java.util.List;

import static com.kaolafm.kradio.huawei.utils.MediaIdHelper.getMediaId;

public class SearchDataConverterUtil {

    public static SearchResult toSearchResult(List<SearchProgramBean> searchProgramBeanList) {
        SearchResult searchResult = new SearchResult();
        List<Compilation> compilationList = new ArrayList<>();
        Compilation compilation = new Compilation();
        List<MediaElement> mediaElementList = new ArrayList<>();
        MediaElement mediaElement = null;

        if (searchProgramBeanList == null || searchProgramBeanList.isEmpty()) {
            searchResult.setTotalResultNum(0);
            return searchResult;
        }

        int maxSize = Math.min(8, searchProgramBeanList.size());
        for (int i = 0; i < maxSize; i++) {
            SearchProgramBean bean = searchProgramBeanList.get(i);
            mediaElement = new MediaElement();
            mediaElement.setMediaId(getMediaId(bean));
            mediaElement.setCoverUrl(bean.getImg());
            mediaElement.setName(bean.getAlbumName());
            mediaElement.setElementType(MediaElement.ElementType.ALBUM);
            mediaElement.setDesp(bean.getName());
            mediaElementList.add(mediaElement);
        }
        compilation.setTopElements(mediaElementList);
        compilation.setTotalElementsNum(searchProgramBeanList.size());
        compilation.setElementsType(MediaElement.ElementType.ALBUM);
        compilation.setName("结果");
        compilationList.add(compilation);
        searchResult.setResultCompilations(compilationList);
        searchResult.setTotalResultNum(searchProgramBeanList.size());
        return searchResult;
    }
}
