package com.kaolafm.kradio.activity.comprehensive.ui;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.opensdk.api.activity.model.Activity;

public class ActivityAdapter extends BaseAdapter<Activity> {
    @Override
    protected BaseHolder getViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(
                R.layout.item_activity, parent, false);
        return new ActivityViewHolder(view);
    }
}