package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.media.AudioManager;
import android.util.Log;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioRecorderInter;
import com.kaolafm.kradio.lib.base.flavor.live.KradioRecorderInterface;

import static android.media.AudioManager.AUDIOMIC_GAIN;
import static android.media.AudioManager.AUDIO_MIC_NORM;


/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-10-27 13:56
 ******************************************/
public final class KRadioAudioRecorderImpl implements KRadioAudioRecorderInter {
    private static final String TAG = "KRadioAudioRecorderImpl";
    private OnMicFocusStatusListener mOnMicFocusStatusListener;
    private AudioManager mAudioManager;

    @Override
    public boolean initVR(Object... args) {
        return false;
    }

    @Override
    public boolean onAudioRecordStart(Object... args) {
        Context context = AppDelegate.getInstance().getContext();
        mOnMicFocusStatusListener = (OnMicFocusStatusListener) args[0];
        if (mAudioManager == null) {
            mAudioManager = (AudioManager) context.getApplicationContext().getSystemService(Context.AUDIO_SERVICE);
        }
        mAudioManager.requestAudioMicFocus(onAudioMicFocusChangeListener, AUDIO_MIC_NORM);
        Log.i(TAG, "onAudioRecordStart: stopVR");
        return false;
    }

    @Override
    public boolean onAudioRecordStop(Object... args) {
        Log.i(TAG, "onAudioRecordStart: reStartWakeRecord");
        return true;
    }

    @Override
    public boolean onAudioRecordStopAfter(Object... args) {
        if (mAudioManager != null) {
            mAudioManager.abandonAudioMicFocus(onAudioMicFocusChangeListener);
        }
        return true;
    }

    @Override
    public void setVrStatusListener(OnVRStatusListener onVRStatusListener) {

    }

    @Override
    public KradioRecorderInterface getRecorder() {
        return null;
    }

    private AudioManager.OnAudioMicFocusChangeListener onAudioMicFocusChangeListener = new AudioManager.OnAudioMicFocusChangeListener() {

        @Override
        public void onAudioMicFocusChange(int i) {
            if (i == AUDIOMIC_GAIN) {
                Log.i(TAG, "onAudioMicFocusChange = " + i);
                if (mOnMicFocusStatusListener != null) {
                    mOnMicFocusStatusListener.onMicFocusGain();
                }
            }
        }
    };
}
