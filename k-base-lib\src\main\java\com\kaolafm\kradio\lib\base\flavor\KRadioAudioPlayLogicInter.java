package com.kaolafm.kradio.lib.base.flavor;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-07-05 12:15
 ******************************************/
public interface KRadioAudioPlayLogicInter {
    /**
     * @param args (args[0]约定传context)
     * @return
     */
    boolean autoPlayAudio(Object... args);

    /**
     * 申请音频焦点
     *
     * @param args
     * @return
     */
    boolean requestAudioFocus(Object... args);

    /**
     * app回到前端时恢复音频播放逻辑
     *
     * @param args
     * @return
     */
    boolean resumeAudioPlayLogic(Object... args);

    /**
     * app回到前端时恢复音频播放逻辑
     *
     * @param args
     * @return
     */
    boolean restartAudioPlayLogic(Object... args);

    /**
     * 处理播放中启动APP，或者退出APP时，只退出页面播放器不销毁之后启动的情况。
     *
     * @param args
     * @return
     */
    boolean doStartInPlay(Object... args);
}
