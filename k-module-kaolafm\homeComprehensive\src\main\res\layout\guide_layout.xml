<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/guild_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="MissingDefaultResource">
    <com.kaolafm.launcher.GuideBgView
        android:id="@+id/guide_root_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <View
        android:visibility="gone"
        android:id="@+id/guide_root_bg_step3"
        android:background="@color/message_details_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <TextView
        android:id="@+id/guide_close"
        android:layout_marginTop="@dimen/y40"
        android:layout_marginStart="@dimen/x210"
        android:background="@drawable/guide_root_close_bg"
        android:text="关闭引导"
        android:gravity="center"
        android:textSize="@dimen/m28"
        android:layout_width="@dimen/m192"
        android:layout_height="@dimen/m64" />

    <LinearLayout
        android:layout_marginTop="@dimen/m40"
        android:layout_centerHorizontal="true"
        android:gravity="center"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <View
            android:layout_marginStart="@dimen/m80"
            android:background="@drawable/guide_root_title_left_indicator"
            android:layout_width="@dimen/m114"
            android:layout_height="@dimen/m2" />
        <TextView
            android:layout_margin="@dimen/x12"
            android:text="&quot;所见即可说&quot;功能引导"
            android:textSize="@dimen/m28"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <View
            android:background="@drawable/guide_root_title_right_indicator"
            android:layout_width="@dimen/m114"
            android:layout_height="@dimen/m2" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/guide_step1_op_panel"
        android:background="@drawable/guide_op_panel_bg_step1"
        android:orientation="vertical"
        android:gravity="start"
        android:layout_width="@dimen/x712"
        android:layout_height="@dimen/m146">

        <TextView
            android:layout_marginStart="@dimen/m86"
            android:layout_marginTop="@dimen/m16"
            android:layout_marginEnd="@dimen/m28"
            android:id="@+id/guide_step1_op_desc"
            android:text="您可说标题【xxx】来播放内容"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/m26"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <TextView
            android:paddingLeft="@dimen/m16"
            android:paddingRight="@dimen/m16"
            android:paddingTop="@dimen/m8"
            android:paddingBottom="@dimen/m8"
            android:background="@drawable/guide_op_panel_button_bg"
            android:layout_marginStart="@dimen/m86"
            android:layout_marginTop="@dimen/m24"
            android:id="@+id/guide_step1_button"
            android:textColor="@color/guide_op_panel_button_text_color"
            android:gravity="center"
            android:textSize="@dimen/m24"
            android:text="下一步"
            android:layout_width="@dimen/m152"
            android:layout_height="@dimen/m48" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/guide_step2_op_panel"
        android:visibility="visible"
        android:background="@drawable/guide_op_panel_bg_step2"
        android:orientation="vertical"
        android:paddingLeft="@dimen/m28"
        android:paddingRight="0dp"
        android:layout_width="@dimen/x410"
        android:layout_height="@dimen/m204">

        <TextView
            android:layout_marginTop="@dimen/m74"
            android:id="@+id/guide_step2_op_desc"
            android:text="您可说【我的】打开个人中心"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/m26"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <TextView
            android:paddingLeft="@dimen/m16"
            android:paddingRight="@dimen/m16"
            android:paddingTop="@dimen/m8"
            android:paddingBottom="@dimen/m8"
            android:background="@drawable/guide_op_panel_button_bg"
            android:layout_marginTop="@dimen/m24"
            android:layout_marginBottom="@dimen/m20"
            android:id="@+id/guide_step2_button"
            android:gravity="center"
            android:text="下一步"
            android:textSize="@dimen/m24"
            android:textColor="@color/guide_op_panel_button_text_color"
            android:layout_width="@dimen/m176"
            android:layout_height="@dimen/m48" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/guide_step3_op_panel"
        android:layout_width="@dimen/x540"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/guide_op_panel_bg_step3"
        android:gravity="center_horizontal"
        android:orientation="vertical"

        android:paddingLeft="@dimen/m28"
        android:paddingTop="@dimen/m16"
        android:paddingRight="@dimen/m28"
        android:paddingBottom="@dimen/m20"
        android:visibility="visible">

        <TextView
            android:id="@+id/guide_step3_op_desc_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="更多说明请见"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/m26" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/guide_step3_op_desc"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="我的-设置-所见即可说帮助说明"
                    android:textColor="@color/colorWhite"
                    android:textSize="@dimen/m26" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_alignParentBottom="true"
                    android:background="@color/colorWhite" />

            </LinearLayout>


            <TextView
                android:id="@+id/guide_step3_op_desc2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text=" 中查看"
                android:textColor="@color/colorWhite"
                android:textSize="@dimen/m26" />
        </LinearLayout>


        <TextView
            android:id="@+id/guide_step3_button"
            android:layout_width="@dimen/m176"
            android:layout_height="@dimen/m48"
            android:layout_marginTop="@dimen/m26"
            android:background="@drawable/guide_op_panel_button_bg"
            android:gravity="center"
            android:paddingLeft="@dimen/m16"
            android:paddingTop="@dimen/m8"
            android:paddingRight="@dimen/m16"
            android:paddingBottom="@dimen/m8"
            android:text="我知道了"
            android:textColor="@color/guide_op_panel_button_text_color"
            android:textSize="@dimen/m24" />
    </LinearLayout>

</RelativeLayout>


