package com.kaolafm.kradio.huawei.controller;

import com.huawei.carmediakit.bean.MediaPlayLocator;
import com.huawei.carmediakit.bean.OperResult;
import com.huawei.carmediakit.bean.PlayMode;
import com.huawei.carmediakit.bean.PlayRate;
import com.huawei.carmediakit.bean.PlayState;
import com.huawei.carmediakit.controller.IMediaPlayingController;
import com.huawei.carmediakit.reporter.MediaPlayingReporter;
import com.iflytek.cloud.ErrorCode;
import com.kaolafm.kradio.huawei.utils.Constant;
import com.kaolafm.kradio.huawei.utils.MediaIdHelper;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;


public class MediaPlayingController  implements IMediaPlayingController {
    public static final String TAG = Constant.TAG;

    @Override
    public OperResult play(MediaPlayLocator mediaPlayLocator) {
        Logger.i(TAG, "play:" + mediaPlayLocator);

        String id = MediaIdHelper.getPlayId(mediaPlayLocator.getMediaId());
        int type = MediaIdHelper.getContentType(mediaPlayLocator.getMediaId());
        Logger.i(TAG, "id=" + id + ":type=" + type);
        PlayerManagerHelper.getInstance().start(id, type);

        PlayState playState = new PlayState();
        playState.setState(PlayState.State.PLAYING);
        MediaPlayingReporter.reportPlayState(playState);
        return new OperResult(ErrorCode.SUCCESS, "Success");
    }

    @Override
    public OperResult pauseCurrent() {
        Logger.i(TAG, "pauseCurrent");

        PlayerManagerHelper.getInstance().pause(true);
        PlayState playState = new PlayState();
        playState.setState(PlayState.State.PAUSED);
        MediaPlayingReporter.reportPlayState(playState);
        return new OperResult(ErrorCode.SUCCESS, "Success");
    }

    @Override
    public OperResult resumeCurrent() {
        Logger.i(TAG, "resumeCurrent");

        PlayerManagerHelper.getInstance().play(true);
        PlayState playState = new PlayState();
        playState.setState(PlayState.State.PLAYING);
        MediaPlayingReporter.reportPlayState(playState);
        return new OperResult(ErrorCode.SUCCESS, "Success");
    }

    @Override
    public OperResult seekTo(int i) {
        Logger.i(TAG, "seekTo");

        PlayerManagerHelper.getInstance().seek(i);
        return new OperResult(ErrorCode.SUCCESS, "Success");
    }

    @Override
    public OperResult playPrevious() {
        Logger.i(TAG, "playPrevious");

        PlayerManagerHelper.getInstance().playPre(true);
        return new OperResult(ErrorCode.SUCCESS, "Success");
    }

    @Override
    public OperResult playNext() {
        Logger.i(TAG, "playNext");

        PlayerManagerHelper.getInstance().playNext(true);
        return new OperResult(ErrorCode.SUCCESS, "Success");
    }

    @Override
    public OperResult switchPlayMode(PlayMode playMode) {
        Logger.i(TAG, "switchPlayMode");

        OperResult result = new OperResult();
        result.setErrCode(ErrorCode.ERROR_AISOUND_UNSUPPORTED);
        return result;
    }

    @Override
    public OperResult switchPlayRate(PlayRate playRate) {
        Logger.i(TAG, "switchPlayRate");
        OperResult result = new OperResult();
        result.setErrCode(ErrorCode.ERROR_AISOUND_UNSUPPORTED);
        return result;
    }
}
