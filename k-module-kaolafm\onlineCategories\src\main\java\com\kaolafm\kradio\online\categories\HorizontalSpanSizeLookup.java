package com.kaolafm.kradio.online.categories;

import androidx.recyclerview.widget.GridLayoutManager;

import com.kaolafm.kradio.common.SubcategoryItemBean;
import com.kaolafm.kradio.online.categories.adapter.SubcategoryAdapter;

/**
 * 横向布局
 *
 * <AUTHOR>
 **/
public class HorizontalSpanSizeLookup extends GridLayoutManager.SpanSizeLookup {

    private int mSpanCount = 2;

    private final SubcategoryAdapter mSubcategoryAdapter;

    public HorizontalSpanSizeLookup(SubcategoryAdapter mSubcategoryAdapter) {
        this.mSubcategoryAdapter = mSubcategoryAdapter;
    }

    @Override
    public int getSpanSize(int position) {
        if (position > mSubcategoryAdapter.getItemCount()) {
            return 0;
        }

        switch (mSubcategoryAdapter.getItemViewType(position)) {
                //标题、线性布局横向占满格，就是一个item占20份
            case SubcategoryItemBean.TYPE_ITEM_TITLE:
//            case SubcategoryItemBean.TYPE_ITEM_TITLE_BUTTON:
//            case SubcategoryItemBean.TYPE_ITEM_SUBSCRIPTION:
//            case SubcategoryItemBean.TYPE_ITEM_NO_SUBSCRIPTION:
            case SubcategoryItemBean.TYPE_ITEM_BUTTON:
                return CategoryConstant.GRID_TOTAL_SPAN_COUNT;
                //专栏:专辑
            case SubcategoryItemBean.TYPE_ITEM_ALBUM:
                //排行榜等5列的每个item占4份
            case SubcategoryItemBean.TYPE_ITEM_OFFICIAL_CHARTS:
            case SubcategoryItemBean.TYPE_ITEM_CHARTS:
            case SubcategoryItemBean.TYPE_ITEM_RADIO_CHANNEL:
            case SubcategoryItemBean.TYPE_ITEM_TV:
                //广播:本地
            case SubcategoryItemBean.TYPE_ITEM_BROADCAST_LOCAL:
                //广播:分类
            case SubcategoryItemBean.TYPE_ITEM_BROADCAST_CATEGORY:
                return 1;//CategoryConstant.GRID_FIVE_SPAN;
            default:
                return 0;
        }
    }
    public void setSpanCount(int spanCount) {
        mSpanCount = spanCount;
    }
}
