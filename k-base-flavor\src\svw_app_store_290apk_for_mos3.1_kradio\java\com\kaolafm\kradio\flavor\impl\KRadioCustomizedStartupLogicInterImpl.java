package com.kaolafm.kradio.flavor.impl;

import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Popup;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.RemoteException;
import android.util.Log;

import com.cns.android.account.CnsAccountInfo;
import com.cns.android.account.CnsAccountManager;
import com.cns.android.helper.VehicleInfoHelper;
import com.kaolafm.kradio.common.http.api.login.KRadioLoginRequest;
import com.kaolafm.kradio.flavor.ExitActivity;
import com.kaolafm.kradio.basedb.manager.HistoryDaoManager;
import com.kaolafm.kradio.k_kaolafm.search.SearchHistoryManager;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.base.flavor.KRadioCustomizedStartupLogicInter;
import com.kaolafm.kradio.lib.utils.KaolaAppConfigData;
import com.kaolafm.kradio.lib.utils.SharedPreferenceUtil;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.log.Logging;

import static android.content.CustomIntent.ACTION_SVW_ACCOUNT_CHANGED;

public class KRadioCustomizedStartupLogicInterImpl implements KRadioCustomizedStartupLogicInter {

    private static final String TAG = "CustomizedStartupLogic";

    static final int Illegal = -1;
    static final int PrimaryUser = 0;
    static final int SecondaryUser = 1;
    static final int GuestUserOnline = 2;
    static final int AnonymousGuestUser = 3;

    Context mContext;
    BroadcastReceiver mAccountReceiver;
    @Override
    public void checkVWSystemAccount(Context context,AccountCallback cb) {
        mContext = context;
        if (DebugImpl.isDebug()){
//            checkAccountSwitch(context);
            cb.isValid(true);
            return;
        }
        addAccountBroadcast();
        CnsAccountManager.getInstance(context).registerManagerServiceConnection(new VehicleInfoHelper.ManagerServiceConnectionListener() {
            @Override
            public void onManagerServiceConnect() {
                Log.d(TAG,"onManagerServiceConnect in");
                if (checkVWSystemAccountInner(context)) {
                    Log.d(TAG,"onManagerServiceConnect checkVWSystemAccount = true");
                    checkAccountSwitch(context);
                    cb.isValid(true);
                }else{
                    Log.d(TAG,"onManagerServiceConnect checkVWSystemAccount = false");
                    notifyUser(context);
                    Log.d(TAG,"onManagerServiceConnect notifyUser");
                    cb.isValid(false);
                }
            }

            @Override
            public void onManagerServiceDisconnection() {
            }
        });
    }

    @Override
    public void release() {
//        removeBroadcast();
    }

    private void addAccountBroadcast(){
        mAccountReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if (!checkVWSystemAccountInner(context)){
                    //监测到系统账号变化并且是退出状态，直接退出app
                    //System.exit(100);
                    Log.i(TAG, "receiver: account changed");
                    AppManager.getInstance().appExit();
                } else {
                    checkAccountSwitch(context);
                }
            }
        };
        IntentFilter filter=new IntentFilter();
        filter.addAction(ACTION_SVW_ACCOUNT_CHANGED);
        mContext.registerReceiver(mAccountReceiver, filter);
    }

    private void removeBroadcast(){
        mContext.unregisterReceiver(mAccountReceiver);
    }

    public boolean checkVWSystemAccountInner(Context context) {
        Log.d(TAG,"checkVWSystemAccount in");
        CnsAccountInfo cnsAccountInfo = null;
        cnsAccountInfo = getAccountInfo(context);
        boolean hasVWAccount = false;
        if (null == cnsAccountInfo
                || cnsAccountInfo.getType() == Illegal
                || cnsAccountInfo.getType() == GuestUserOnline
                || cnsAccountInfo.getType() == AnonymousGuestUser) {
            if(cnsAccountInfo!=null)
                Log.d(TAG,"checkVWSystemAccount cnsAccountInfo = "+cnsAccountInfo.toString());
            hasVWAccount = false;
        }else{
            hasVWAccount = true;
        }

        Log.d(TAG,"checkVWSystemAccount hasVWAccount = "+hasVWAccount);
        return hasVWAccount;
    }

    static public CnsAccountInfo getAccountInfo(Context context) {
        CnsAccountInfo cnsAccountInfo = null;
        try {
            cnsAccountInfo = CnsAccountManager.getInstance(context).getAccountInfo();
            Log.d(TAG, cnsAccountInfo.toString());
        } catch (RemoteException e) {
            e.printStackTrace();
        } finally {
            return cnsAccountInfo;
        }
    }

    /**
     * 6.2账号切换检测
     * 大众提供了获取车机系统用户账号信息的方法，如下：com.cns.android.account.CnsAccountManager#getAccountInfo
     * 通过该方法可获取车机系统用户账号ID，听伴APP需将该ID作为敏感信息以加密的方式保存于本地。当听伴APP启动时需调用上述方法
     * 重新获取当前车机系统用户账号ID，若当前获取的车机系统用户账号ID与本地存储的车机系统用户账号ID不一致，需完成如下操作：
     * （1）若听伴APP已登录，退出登录。
     * （2）更新本地存储的车机系统用户账号ID
     * @param context
     */
    private void checkAccountSwitch(Context context) {
        Log.d(TAG,"checkAccountWithSystem in");
        SharedPreferenceUtil sharedPreferenceUtil = SharedPreferenceUtil
                .newInstance(AppDelegate.getInstance().getContext(), "cns.account", Context.MODE_PRIVATE);
        String storedAccountId = sharedPreferenceUtil.getString("accountId", null);
        Log.d(TAG,"checkAccountWithSystem storedAccountId = "+storedAccountId);

        CnsAccountInfo cnsAccountInfo = getAccountInfo(context);
        Log.d(TAG,"checkAccountWithSystem cnsAccountInfo.getId() = "+cnsAccountInfo.getId());
        if (null != storedAccountId && !storedAccountId.equals(cnsAccountInfo.getId())) {
            quitAndClear();
        }
        Log.d(TAG,"checkAccountWithSystem save AccountId = "+cnsAccountInfo.getId());
        sharedPreferenceUtil.putString("accountId", cnsAccountInfo.getId());
        Log.d(TAG,"checkAccountWithSystem out");
    }

    private void notifyUser(Context context) {
        Log.d(TAG,"notifyUser in");
        Intent intent = new Intent(context, ExitActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(context, 9, intent, PendingIntent.FLAG_UPDATE_CURRENT);
        Popup popup = new Popup("提示", "用户名或密码错误。");
        popup.setActionCancel("确定", pendingIntent);
        NotificationManager notificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        notificationManager.addPopup(9, popup);
        Log.d(TAG,"notifyUser in");
    }

    private void quitAndClear() {
        Log.d(TAG,"quitAndClear in");
        // 1. 退出听伴账号
        Log.d(TAG, "begin logout because of CNS Account switch");
        UserInfoManager.getInstance().logout();
        new KRadioLoginRequest().logout(KaolaAppConfigData.getInstance().getAppKey(), new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                Log.d(TAG, "CNS Account switch triggered logout success.");
            }

            @Override
            public void onError(ApiException e) {
                Log.d(TAG, "CNS Account switch triggered logout failed.");
            }
        });

        // 2. 清除搜索历史
        SearchHistoryManager.getInstance().clearAllRecentSearchTags();

        // 3. 清除本地未登录时产生的收听历史
        HistoryDaoManager.getInstance().deleteAll();
        Log.d(TAG,"quitAndClear out");
    }
}

