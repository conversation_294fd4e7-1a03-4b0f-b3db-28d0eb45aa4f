package com.kaolafm.kradio.component.ui.brandpage.carownerradio;

import android.animation.Animator;
import android.animation.AnimatorInflater;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.FloatEvaluator;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.content.Intent;

import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.animation.LinearInterpolator;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.bumptech.glide.load.resource.bitmap.FitCenter;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.component.ui.R;
import com.kaolafm.kradio.component.ui.base.cell.Status;
import com.kaolafm.kradio.component.ui.base.view.RateView;
import com.kaolafm.kradio.config.ConfigSettingManager;
import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.component.ui.base.view.KradioTextView;
import com.kaolafm.kradio.component.ui.base.view.OvalImageView;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.UrlUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.lib.widget.tab.LinearGradientFontSpan;
import com.kaolafm.opensdk.api.operation.model.column.ColumnContent;
import com.kaolafm.opensdk.api.operation.model.column.LiveProgramDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.RadioDetailColumnMember;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.LivePlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

import static com.kaolafm.kradio.component.ui.brandpage.carownerradio.CarOwnerRadioEntrance.BROADCAST_RECEIVER_ACTION_CAR_ENTER_ANIMATION_COMPLETE;

/**
 * 品牌主页配置的车主电台组件
 *
 * <AUTHOR> shiqian
 * @date 2023-02-28
 */
public class CarOwnerRadio extends ConstraintLayout {
    private static final String TAG = "CarOwnerRadio";

    private ConstraintLayout carTypeRadioLayout, carTypeCommonLayout, carTypeLiveLayout;

    private ImageView radioLogo, radioCar;
    //    private TextView radioPlayName;
    private TextView radioName;

    private LinearLayout radio_live_tag;
    private RateView radio_live_tag_playing;
    private ImageView card_play_iv;
    private TextView radio_live_tag_tv;

    private OvalImageView radioCommonCover;
    private TextView radioCommonTitle, radioCommonSubTitle;

    private ImageView radioLiveCover;
    private KradioTextView radioLiveTitle, radio_live_btn;

    private ConstraintLayout radioCard;
    private ImageView radioBg,//光圈
            radioRoad,  //路
            playBtn;  //中心播放按钮，只有在品牌电台页显示，否则不显示

    private AnimatorSet enterAnimatorSet;    //进入品牌电台页面的动画
    private OnClickListener mOnClickListener;

    private HomeCell brandPageCell;

    private int liveStatus;

    public void pauseLottie(){
        radio_live_tag_playing.setVisibility(GONE); //隐藏播放动画
        card_play_iv.setVisibility(GONE);
    }

    public void playLottie(){
        radio_live_tag_playing.setVisibility(GONE);
        card_play_iv.setVisibility(GONE);
    }

    public void hideLottie(){
        radio_live_tag_playing.setVisibility(GONE);
        card_play_iv.setVisibility(GONE);
    }

    /**
     * 更新播放状态
     */
    public void updatePlayState() {
        if (brandPageCell == null || brandPageCell.getContentList() == null || brandPageCell.getContentList().size() == 0) {
            return;
        }


        ColumnContent columnContent = brandPageCell.getContentList().get(0);
//        if (isPlayingBrandContent() || columnContent.getCanPlay() == 0) {
//            ViewUtil.setViewVisibility(playBtn, View.GONE);
//        } else {
//            ViewUtil.setViewVisibility(playBtn, View.VISIBLE);
//        }
        ViewUtil.setViewVisibility(playBtn, View.GONE);
    }

    /**
     * 是否正播品牌电台的节目
     *
     * @return
     */
    public boolean isPlayingBrandContent() {
        PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
        boolean isPlaying = PlayerManager.getInstance().isPlaying();
        if (!isPlaying || playItem == null || !haveBrandContent()) {
            return false;
        }
        ColumnContent columnContent = brandPageCell.getContentList().get(0);
        if (!isSameProgram(columnContent.getId())) {
            return false;
        }
        return true;
    }

    /**
     * 是否有品牌电台数据
     *
     * @return
     */
    public boolean haveBrandContent() {
        if (brandPageCell == null || ListUtil.isEmpty(brandPageCell.getContentList())) {
            return false;
        }
        ColumnContent columnContent = brandPageCell.getContentList().get(0);
        if (columnContent == null)
            return false;

        return true;
    }

    public @Nullable LiveProgramDetailColumnMember getLiveProgramDetailColumnMember(){
        if (brandPageCell == null || ListUtil.isEmpty(brandPageCell.getContentList())) {
            return null;
        }

        ColumnContent columnContent = brandPageCell.getContentList().get(0);
        if (columnContent == null){
            return null;
        }

        if(columnContent instanceof LiveProgramDetailColumnMember){
            return (LiveProgramDetailColumnMember)columnContent;
        }

        return null;
    }

    public CarOwnerRadio(Context context) {
        this(context, null);
    }

    public CarOwnerRadio(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CarOwnerRadio(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    @Override
    protected void onDetachedFromWindow() {
        cancelAnimators();
        super.onDetachedFromWindow();
    }

    /**
     * 取消动画
     */
    private void cancelAnimators() {
        if (enterAnimatorSet != null && enterAnimatorSet.isRunning())
            enterAnimatorSet.cancel();
    }

    private void init(Context context) {
        setVisibility(View.GONE);
        LayoutInflater.from(context).inflate(R.layout.car_owner_radio, this);
        radioBg = findViewById(R.id.radio_bg);
        playBtn = findViewById(R.id.playBtn);

        carTypeRadioLayout = findViewById(R.id.car_owner_radio_type_radio);
        radioLogo = findViewById(R.id.radio_logo);
//        radioPlayName = findViewById(R.id.radio_play_name);
        radioName = findViewById(R.id.radio_name);
        radioCar = findViewById(R.id.radio_car);

        carTypeCommonLayout = findViewById(R.id.car_owner_radio_type_common);
        radioCommonCover = findViewById(R.id.radio_common_cover);
        radioCommonTitle = findViewById(R.id.radio_common_title);
        radioCommonSubTitle = findViewById(R.id.radio_common_sub_title);

        carTypeLiveLayout = findViewById(R.id.car_owner_radio_type_live);
        radioLiveCover = findViewById(R.id.radio_live_cover);
        radioLiveTitle = findViewById(R.id.radio_live_title);

        radio_live_tag = findViewById(R.id.radio_live_tag);
        radio_live_tag_playing = findViewById(R.id.radio_live_tag_playing);
        card_play_iv = findViewById(R.id.card_play_iv);
        radio_live_tag_tv = findViewById(R.id.radio_live_tag_tv);
    }

    public void updateDate(HomeCell brandPageCell) {
        if (brandPageCell == null) {
            return;
        }
        this.brandPageCell = brandPageCell;
        if (brandPageCell.getContentList() != null) {
            if (brandPageCell.getContentList().size() > 0) {
                // 根据不同成员类型解析组件要展示的内容
                ColumnContent columnContent = brandPageCell.getContentList().get(0);
                if (columnContent == null) {
                    return;
                }
                View centerView = null;
                if (columnContent instanceof RadioDetailColumnMember) {
                    carTypeRadioLayout.setVisibility(VISIBLE);
                    carTypeCommonLayout.setVisibility(GONE);
                    carTypeLiveLayout.setVisibility(GONE);


                    String logoUrl = UrlUtil.getCardLogoUrl(brandPageCell.getImageFiles());
                    ImageLoader.getInstance().displayCircleImage(getContext(), logoUrl, radioLogo);
//                    radioPlayName.setText(columnContent.getTitle() + "");
                    radioName.setText(columnContent.getTitle() + "");
                    String urlCar = UrlUtil.getCarPicUrl(brandPageCell.getImageFiles());
                    ImageLoader.getInstance().displayImage(getContext(), urlCar, radioCar, new FitCenter());
                    centerView = carTypeRadioLayout;
                } else if (columnContent instanceof LiveProgramDetailColumnMember) {
                    carTypeRadioLayout.setVisibility(GONE);
                    carTypeCommonLayout.setVisibility(GONE);
                    carTypeLiveLayout.setVisibility(VISIBLE);

                    String coverUrl = UrlUtil.getCoverPicUrl(columnContent.getImageFiles());
                    ImageLoader.getInstance().displayCircleImage(getContext(), coverUrl, radioLiveCover);

                    card_play_iv.setVisibility(GONE);
                    liveStatus = columnContent.getLiveStatus();

                     if (columnContent.getLiveStatus() == Status.STATUS_NOT_START || columnContent.getLiveStatus() == Status.STATUS_Delay) {
                        // 直播未开始
                        radio_live_tag_tv.setText("直播待开始");
                        radio_live_tag.setBackground(ResUtil.getDrawable(R.drawable.car_owner_radio_live_tag_end_bg));
                        radio_live_tag_playing.setVisibility(GONE);
                    }  else if (columnContent.getLiveStatus() == Status.STATUS_LIVING) {
                        radio_live_tag_tv.setText("直播中");
                        radio_live_tag.setBackground(ResUtil.getDrawable(R.drawable.car_owner_radio_live_tag_bg));
                        radio_live_tag_playing.setVisibility(GONE);
                    }else{
                         // 直播已结束
                         radio_live_tag_tv.setText("直播已结束");
                         radio_live_tag.setBackground(ResUtil.getDrawable(R.drawable.car_owner_radio_live_tag_end_bg));
                         radio_live_tag_playing.setVisibility(GONE);
                     }
                    radioLiveTitle.setText(columnContent.getTitle() + "");
                    radioLiveTitle.setGradientColor(ResUtil.getColor(R.color.car_owner_radio_name_text_end_color),
                            ResUtil.getColor(R.color.car_owner_radio_name_text_start_color), 90);
                    radioLiveTitle.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            //这里需要延时在重新设置跑马灯效果，否则会获取焦点失败
                            radioLiveTitle.setEllipsize(TextUtils.TruncateAt.MARQUEE);
                            radioLiveTitle.setSingleLine(true);
                            radioLiveTitle.setSelected(true);
                            radioLiveTitle.setFocusable(true);
                            radioLiveTitle.setFocusableInTouchMode(true);
                            radioLiveTitle.requestFocus();
                        }
                    }, 1000);
                    centerView = carTypeLiveLayout;
                } else {
                    carTypeCommonLayout.setVisibility(VISIBLE);
                    carTypeRadioLayout.setVisibility(GONE);
                    carTypeLiveLayout.setVisibility(GONE);

                    String urlCover = UrlUtil.getCoverPicUrl(columnContent.getImageFiles());
                    ImageLoader.getInstance().displayCircleImage(getContext(), urlCover, radioCommonCover);
                    radioCommonTitle.setText(columnContent.getTitle() + "");
                    radioCommonSubTitle.setText(StringUtil.getMaxStringCenterDot(columnContent.getSubtitle() + "", 8));
                    centerView = carTypeCommonLayout;
                }
//                String bgUrl = UrlUtil.getCardBgUrl(brandPageCell.getImageFiles());
//                ImageLoader.getInstance().displayImage(getContext(), bgUrl, radioBg);
                View finalCenterView = centerView;
                updatePlayState();

                ConfigSettingManager.getInstance().isPlayAnimation(new ConfigSettingManager.OnResultCallback<Boolean>() {
                    @Override
                    public void onResult(Boolean aBoolean) {
                        runAnimationOnEnter(aBoolean, finalCenterView);
                    }
                });
            }
        }
    }

    /**
     * 生成渐变文字
     *
     * @param string
     * @return
     */
    public SpannableStringBuilder getRadiusGradientSpan(String string) {
        SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder(string);
        LinearGradientFontSpan span = new LinearGradientFontSpan(ResUtil.getColor(R.color.car_owner_text_color_start)
                , ResUtil.getColor(R.color.car_owner_text_color_end));
        spannableStringBuilder.setSpan(span, 0, spannableStringBuilder.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spannableStringBuilder;

    }

    @Override
    public void setOnClickListener(OnClickListener onClickListener) {
        Log.e(TAG, "");
        mOnClickListener = onClickListener;

        super.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (AntiShake.check(v.getId())) return;
                if (mOnClickListener != null) {
                    //品牌电台页点击
                    mOnClickListener.onClick(CarOwnerRadio.this);
                }
            }
        });
    }

    private void runAnimationOnEnter(boolean showAnimator, View centerView) {
        if (showAnimator) {
//                ViewUtil.setViewVisibility(radioInBtn, View.GONE);
            ObjectAnimator translationY = ObjectAnimator.ofObject(radioCar, "translationY", new FloatEvaluator(), ResUtil.getDimen(R.dimen.m236), 0).setDuration(3000);
            translationY.setDuration(1000);
            AnimatorSet scaleBg = (AnimatorSet) AnimatorInflater.loadAnimator(getContext(), R.animator.car_owner_radio_scale_in);
            scaleBg.setTarget(radioBg);
            AnimatorSet scaleCardCenter = (AnimatorSet) AnimatorInflater.loadAnimator(getContext(), R.animator.car_owner_radio_scale_in);
            scaleCardCenter.setTarget(centerView);
            if (enterAnimatorSet != null && enterAnimatorSet.isRunning()) {
                enterAnimatorSet.cancel();
            }
            enterAnimatorSet = new AnimatorSet();
            enterAnimatorSet.setInterpolator(new LinearInterpolator());
            enterAnimatorSet.playTogether(translationY, scaleBg, scaleCardCenter);
            enterAnimatorSet.addListener(new AnimatorListenerAdapter() {
                @Override
                public void onAnimationStart(Animator animation) {
                    setVisibility(View.VISIBLE);
                    super.onAnimationStart(animation);
                }

                @Override
                public void onAnimationEnd(Animator animation) {
                    super.onAnimationEnd(animation);
                    if (playBtn != null)
                        playBtn.animate().alpha(1).setDuration(300).start();
                    getContext().sendBroadcast(new Intent(BROADCAST_RECEIVER_ACTION_CAR_ENTER_ANIMATION_COMPLETE));
                }
            });
            enterAnimatorSet.start();
            return;
        }
        setVisibility(View.VISIBLE);
        if (radioBg != null)
            radioBg.setAlpha(1f);
        radioCar.setTranslationY(0);
        if (playBtn != null)
            playBtn.setAlpha(1f);
        getContext().sendBroadcast(new Intent(BROADCAST_RECEIVER_ACTION_CAR_ENTER_ANIMATION_COMPLETE));
    }

    /**
     * 是否播放的相同节目
     *
     * @param id
     * @return
     */
    private boolean isSameProgram(String id) {
        PlayItem curPlayItem = PlayerManager.getInstance().getCurPlayItem();
        if (curPlayItem == null) return false;
        switch (curPlayItem.getType()) {
            case PlayerConstants.RESOURCES_TYPE_BROADCAST:
            case PlayerConstants.RESOURCES_TYPE_TV:
            case PlayerConstants.RESOURCES_TYPE_RADIO:
                return curPlayItem.getRadioId() != null && curPlayItem.getRadioId().equals(String.valueOf(id));
            case PlayerConstants.RESOURCES_TYPE_ALBUM:
            case PlayerConstants.RESOURCES_TYPE_FEATURE:
                return curPlayItem.getAlbumId() != null && curPlayItem.getAlbumId().equals(String.valueOf(id));
            case PlayerConstants.RESOURCES_TYPE_LIVING:
                LivePlayItem livePlayItem = (LivePlayItem) curPlayItem;
                return String.valueOf(livePlayItem.getLiveId()).equals(id);
            default:
                return String.valueOf(curPlayItem.getAudioId()).equals(String.valueOf(id));
        }
    }

    public HomeCell getBrandPageCell() {
        return brandPageCell;
    }
}
