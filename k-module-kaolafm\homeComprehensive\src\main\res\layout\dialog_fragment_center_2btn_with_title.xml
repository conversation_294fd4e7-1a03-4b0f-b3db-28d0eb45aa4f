<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/m640"
    android:layout_height="@dimen/y324"
    android:layout_gravity="center"
    android:minWidth="@dimen/m640"
    android:background="@drawable/bg_dialog"
    android:gravity="center"
    android:orientation="vertical"
    android:paddingLeft="@dimen/x20"
    android:paddingTop="@dimen/y40"
    android:paddingRight="@dimen/x20"
    android:paddingBottom="@dimen/y40">

    <TextView
        android:id="@+id/tv_dialog_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textColor="@color/dialog_common_btn_title_text_color"
        android:textSize="@dimen/text_size5"
        tools:text="确定?" />

    <TextView
        android:id="@+id/tv_dialog_bottom_message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y35"
        android:gravity="center"
        android:maxLines="3"
        android:textColor="@color/text_color_5"
        android:textSize="@dimen/text_size4"
        tools:text="确定清空收听历史吗?" />

    <LinearLayout
        android:id="@+id/tv_dialog_button_main_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y40"
        android:gravity="center_horizontal"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_dialog_bottom_define"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/m35"
            android:background="@drawable/selector_dialog_costomize_btn_sure"
            android:gravity="center"
            android:minWidth="@dimen/m156"
            android:paddingStart="@dimen/x40"
            android:paddingTop="@dimen/y14"
            android:paddingEnd="@dimen/x40"
            android:paddingBottom="@dimen/y14"
            android:text="@string/ok"
            android:textColor="@color/dialog_common_btn_sure_text_color"
            android:textSize="@dimen/text_size4" />

        <TextView
            android:id="@+id/tv_dialog_bottom_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/m35"
            android:background="@drawable/selector_dialog_costomize_btn_cancle"
            android:gravity="center"
            android:minWidth="@dimen/m156"
            android:paddingStart="@dimen/x40"
            android:paddingTop="@dimen/y14"
            android:paddingEnd="@dimen/x40"
            android:paddingBottom="@dimen/y14"
            android:text="@string/cancel"
            android:textColor="@color/dialog_common_btn_cancel_text_color"
            android:textSize="@dimen/text_size4" />
    </LinearLayout>
</LinearLayout>