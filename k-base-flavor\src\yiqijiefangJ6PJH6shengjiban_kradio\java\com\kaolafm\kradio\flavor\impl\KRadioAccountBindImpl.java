package com.kaolafm.kradio.flavor.impl;

import com.kaolafm.kradio.lib.base.flavor.KRadioAccountBindInter;
import com.kaolafm.opensdk.api.ex.BindAccountRequest;
import com.kaolafm.opensdk.http.core.HttpCallback;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-08-26 16:23
 ******************************************/
public class KRadioAccountBindImpl implements KRadioAccountBindInter {
    @Override
    public void bindDeviceId(String phone, String nickName, String avatar, HttpCallback callback) {
        new BindAccountRequest().bindDeviceId(phone, nickName, avatar, callback);
    }
}
