package com.kaolafm.kradio.categories.viewholder;

import android.view.View;
import android.widget.TextView;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.bean.SubcategoryItemBean;
import com.kaolafm.kradio.lib.utils.ResUtil;

/**
 * <AUTHOR>
 * @date 2018/4/25
 */

public class TitleViewHolder extends BaseSubcategoryViewHolder {

    public TitleViewHolder(View itemView) {
        super(itemView);
    }

    @Override
    public void setupData(SubcategoryItemBean subcategoryItemBean, int position) {
        int paddingTop;
        if (position == 0) {
            paddingTop = ResUtil.getDimen(R.dimen.subcategory_grid_padding_top_30);
        } else {
            //由于后面的gride有8px的间隔，所有要减去这个间隔
            paddingTop = ResUtil.getDimen(R.dimen.y52);
        }
        itemView.setPadding(0, paddingTop, 0, ResUtil.getDimen(R.dimen.y40));
        ((TextView) itemView.findViewById(R.id.item_subcategory_title)).setText(subcategoryItemBean.getTitle());
    }
}
