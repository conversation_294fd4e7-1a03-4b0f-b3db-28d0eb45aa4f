package com.kaolafm.kradio.flavor.aop;

import androidx.fragment.app.DialogFragment;
import android.view.Gravity;
import com.kaolafm.kradio.common.report.ReportUtil;
import com.kaolafm.kradio.lib.dialog.Dialogs;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.setting.SettingFragment;
import com.kaolafm.kradio.setting.SkinAndQualityAdapter;
import com.kaolafm.opensdk.player.logic.util.ToneQualityHelper;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;


@Aspect
public class SettingAspect {

    SkinAndQualityAdapter qualityAdapter;
    private DialogFragment fullScreenDialogFragment = null;

    @Around("execution(* com.kaolafm.kradio.setting.SettingFragment.showSoundQuality(..))")
    public void showSoundQuality(JoinPoint point) throws Throwable {
        List<SkinAndQualityAdapter.Item> soundQualities = (List<SkinAndQualityAdapter.Item>) point.getArgs()[0];

        qualityAdapter = new SkinAndQualityAdapter();
        ArrayList arrayList = new ArrayList();
        arrayList.add(soundQualities.get(0));
        qualityAdapter.setDataList(arrayList);
        qualityAdapter.selectQuality(ToneQualityHelper.getInstance().getToneQuality());
        qualityAdapter.setOnItemClickListener((view, viewType, item, position) -> {
            ToneQualityHelper.getInstance().removeToneQualityListener(iToneQualityListene);

            qualityAdapter.select(position);
            ToneQualityHelper.getInstance().setToneQuality(item.quality);
            ReportUtil.addToneSelectEvent(item.quality);
            fullScreenDialogFragment.dismiss();
            if (fullScreenDialogFragment != null) {
                fullScreenDialogFragment.dismiss();
            }
            qualityAdapter = null;
        });
//
        fullScreenDialogFragment = new Dialogs.Builder()
                .setType(Dialogs.TYPE_LIST)
                .setGravity(Gravity.CENTER)
                .setTitle(ResUtil.getString(com.kaolafm.kradio.k_kaolafm.R.string.sound_quality))
                .setAdapter(qualityAdapter)
                .create();

        SettingFragment fragment = (SettingFragment) point.getTarget();
        fullScreenDialogFragment.show(fragment.getFragmentManager(), "voice");

        ToneQualityHelper.getInstance().registerToneQualityListener(iToneQualityListene);
    }

    private ToneQualityHelper.IToneQualityListener iToneQualityListene = new ToneQualityHelper.IToneQualityListener(){

        @Override
        public void toneChange(int i) {
            if (qualityAdapter != null) {
                qualityAdapter.selectQuality(i);
            }
        }
    };
}
