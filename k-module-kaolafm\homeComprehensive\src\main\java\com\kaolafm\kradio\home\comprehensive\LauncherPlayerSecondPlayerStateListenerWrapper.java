package com.kaolafm.kradio.home.comprehensive;

import android.annotation.SuppressLint;
import android.util.Log;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.launcher.LauncherActivity;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

import java.lang.ref.WeakReference;

/**
 * 首页播放状态监听。从首页内部类移出来的。
 */
public class LauncherPlayerSecondPlayerStateListenerWrapper extends BasePlayStateListener {

    private static final String TAG = "HomePlayerSecondPlayerStateListenerWrapper";

    private String radioId;

    private int count = 0;

    private boolean showAllError = true;

    private WeakReference<LauncherActivity> weakReference;

    public LauncherPlayerSecondPlayerStateListenerWrapper(LauncherActivity launcherActivity) {
        weakReference = new WeakReference<>(launcherActivity);
    }

    @SuppressLint("LongLogTag")
    @Override
    public void onIdle(PlayItem playItem) {
        super.onIdle(playItem);
        Log.i(TAG, "onIdle start");
        LauncherActivity launcherActivity = weakReference.get();
        if (launcherActivity == null) {
            return;
        }
        if (radioId == null || !radioId.equals(launcherActivity.getRadioId())) {
            //如果radioId发生变化,则重置.
            showAllError = true;
            count = 0;
        }
        if(playItem != null && playItem.isLiving()){
            launcherActivity.setIsMediaPause(true);
        }
        radioId = launcherActivity.getRadioId();
        launcherActivity.setPlayEnd();
        Log.i(TAG, "onIdle: radioId=" + radioId);

    }

    @SuppressLint("LongLogTag")
    @Override
    public void onPlayerPlaying(PlayItem playItem) {
        super.onPlayerPlaying(playItem);
        Log.i(TAG, "onPlayerPlaying start");
        //只要有一个可以播放,就不提示"播放失败".
        showAllError = false;
        LauncherActivity launcherActivity = weakReference.get();
        if (launcherActivity != null) {
            launcherActivity.setIsMediaPause(false);
            if (PlayerManagerHelper.getInstance().getCurPlayItem().getType() == PlayerConstants.RESOURCES_TYPE_RADIO) {
                launcherActivity.registerLiveLifecycleListener();
            }
        } else {
            Log.i(TAG, "onPlayerPlaying horizontalHomePlayerFragment is null");
        }

    }

    @SuppressLint("LongLogTag")
    @Override
    public void onPlayerPreparing(PlayItem playItem) {
        super.onPlayerPreparing(playItem);
        Log.i(TAG, "onPlayerPreparing start");
//        LauncherActivity launcherActivity = weakReference.get();
//        if (launcherActivity != null) {
//            launcherActivity.setSelected();
//        } else {
//            Log.i(TAG, "onPlayerPreparing horizontalHomePlayerFragment is null");
//        }
    }

    @SuppressLint("LongLogTag")
    @Override
    public void onPlayerPaused(PlayItem playItem) {
        Log.i(TAG, "onPlayerPaused start");
        LauncherActivity launcherActivity = weakReference.get();
        if (launcherActivity != null) {
            launcherActivity.setIsMediaPause(true);
            launcherActivity.setPlayEnd();
        } else {
            Log.i(TAG, "onPlayerPaused horizontalHomePlayerFragment is null");
        }
    }

    @SuppressLint("LongLogTag")
    @Override
    public void onPlayerFailed(PlayItem playItem, int i, int i1) {
        super.onPlayerFailed(playItem, i, i1);
        Log.i(TAG, "onPlayerFailed: error = " + i + " , " + i1);
        LauncherActivity launcherActivity = weakReference.get();
        if (launcherActivity != null) {
            launcherActivity.setPlayEnd();
        } else {
            Log.i(TAG, "onPlayerPaused horizontalHomePlayerFragment is null");
        }
    }

    @SuppressLint("LongLogTag")
    @Override
    public void onPlayerEnd(PlayItem playItem) {
        super.onPlayerEnd(playItem);
        LauncherActivity launcherActivity = weakReference.get();
        if (launcherActivity == null) {
            return;
        }
        launcherActivity.setPlayEnd();

        boolean isSameId = radioId != null && radioId.equals(launcherActivity.getRadioId());
        Log.i(TAG, "onPlayerEnd: count=" + count + ",isSameId=" + isSameId + ",showAllError=" + showAllError);
        if (showAllError && isSameId) {
            count++;
            if (PlayerConstants.RESOURCES_TYPE_ALBUM == PlayerManagerHelper.getInstance().getCurPlayItem().getType()) {
                if (!PlayerManagerHelper.getInstance().hasNextItem()) {
                    ToastUtil.showNormal(launcherActivity, R.string.is_not_online);
                }
            }
        }
    }
}
