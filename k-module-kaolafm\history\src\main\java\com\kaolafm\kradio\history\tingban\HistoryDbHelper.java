package com.kaolafm.kradio.history.tingban;

import android.content.Context;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;

/**
 * 听伴车载升级到Kradio 用于读取听伴的历史。
 */
public class HistoryDbHelper extends SQLiteOpenHelper {
    /**
     * 点播存入历史的最大条目数
     */
    public static final int PLAY_HISTORY_LIMIT_SIZE = 100;
    /**
     * Database version
     */
//    public static final int DB_VERSION_1 = 1; /* initial version */
    public static final int DB_VERSION_2 = 2; /* initial version, 3.1 */
    public static final int DB_VERSION_3 = 3;
    public static final int DB_VERSION_4 = 4;
    public static final int DB_VERSION_5 = 5;
    public static final int DB_VERSION_6 = 6; // 4.8.1
    public static final int DB_VERSION_7 = 7; // 4.8.1
    public static final int DB_VERSION = DB_VERSION_7;
    /**
     * Database name
     */
    public static final String DB_NAME = "history.db";
    /**
     * 专辑播放历史表
     */
    public static final String TABLE_PLAY_HISTORY = "play_history";
    /**
     * 收听历史临时表
     */
    private static final String TABLE_PLAY_HISTORY_TEMP = "temp_" + TABLE_PLAY_HISTORY;
    /**
     * 专辑碎片播放历史表
     */
    public static final String TABLE_AUDIO_PLAY_HISTORY = "audio_play_history";
    /**
     * 传统电台播放历史表
     */
    public static final String TABLE_PLAY_BROADCAST_HISTORY = "play_broadcast_history";
    /**
     * 传统电台播放历史临时表
     */
    private static final String TABLE_PLAY_BROADCAST_HISTORY_TEMP = "temp_" + TABLE_PLAY_BROADCAST_HISTORY;

    /**
     * table feild
     */
    public static final String FIELD_TYPE = "type";
    public static final String FIELD_RADIO_ID = "radioId";
    public static final String FIELD_RADIO_TITLE = "radioTitle";
    public static final String FIELD_PIC_URL = "pic_url";
    public static final String FIELD_AUDIO_ID = "audioId";
    public static final String FIELD_AUDIO_TITLE = "audioTitle";
    public static final String FIELD_PLAY_URL = "playUrl";
    public static final String FIELD_OFFLINE_PLAY_URL = "offlinePlayUrl";
    public static final String FIELD_PLAYED_TIME = "playedTime";
    public static final String FIELD_DURATION = "duration";
    public static final String FIELD_IS_OFFLINE = "isOffline";
    public static final String FIELD_ORDER_NUM = "orderNum";
    public static final String FIELD_SHARE_URL = "shareUrl";
    public static final String FIELD_TIMESTAMP = "timestamp";
    public static final String FIELD_CATEGORYID = "categoryId";
    /**
     * 专辑排序（升序或降序 具体参考PlayerRadioListItem.ORDER_MODE_POSITIVE，PlayerRadioListItem.ORDER_MODE_REVERSE）
     */
    public static final String FIELD_ORDER_MODE = "orderMode";
    /**
     * 电台分类类型
     */
    public static final String FIELD_TYPE_ID = "typeId";
    /**
     * 额外预留表字段
     */
    public static final String FIELD_PARAMS_ONE = "paramsOne";
    public static final String FIELD_PARAMS_TWO = "paramsTwo";

    /**
     * 第三方资源，来源url
     */
    public static final String FIELD_SOURCE_URL = "sourceUrl";

    private static final String FIELD_ID = "_id";
    /**
     * Sql statement: query operations
     */
    public static final String SQL_QUERY_PLAY_HISTORY
            = "SELECT * FROM " + TABLE_PLAY_HISTORY + " ORDER BY " + FIELD_TIMESTAMP + " DESC LIMIT " + PLAY_HISTORY_LIMIT_SIZE;
    public static final String SQL_QUERY_PLAY_HISTORY_BY_ONE
            = "SELECT * FROM " + TABLE_PLAY_HISTORY + " ORDER BY " + FIELD_TIMESTAMP + " DESC LIMIT 1";

    /**
     * Sql statement: drop table operations
     */
    public static final String SQL_DROP_TABLE_PLAY_HISTORY = "DROP TABLE IF EXISTS " + TABLE_PLAY_HISTORY;

    public static final String SQL_DROP_TABLE_AUDIO_PLAY_HISTORY = "DROP TABLE IF EXISTS " + TABLE_AUDIO_PLAY_HISTORY;

    public static final String SQL_DROP_TABLE_BROADCAST_PLAY_HISTORY = "DROP TABLE IF EXISTS " + TABLE_PLAY_BROADCAST_HISTORY;

    public HistoryDbHelper(Context context) {
        super(context, DB_NAME, null, DB_VERSION);
    }

    @Override
    public synchronized void onCreate(SQLiteDatabase db) {
        createTablePlayHistory(db);
        createTableAudioPlayHistory(db);
    }

    @Override
    public synchronized void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        // need implement in the future when update database, here is only a sample code
        try {
            db.beginTransaction();
            boolean success = false;
            for (int i = oldVersion; i <= DB_VERSION; i++) {
                switch (i) {
                    case DB_VERSION_2:
                        success = updateToVersion2(db);
                        break;
                    case DB_VERSION_3:
                        success = updateToVersion3(db);
                        break;
                    case DB_VERSION_4:
                        success = updateToVersion4(db);
                        break;
                    case DB_VERSION_5:
                        success = updateToVersion5(db);
                        break;
                    case DB_VERSION_6:
                        success = updateToVersion6(db);
                        break;
                    default:
                        break;
                }
            }
            if (success) {
                db.setVersion(DB_VERSION);
                db.setTransactionSuccessful();
            }
        } catch (Throwable e) {
            e.printStackTrace();
        } finally {
            db.endTransaction();
        }
    }

    @Override
    public synchronized void onDowngrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        dropAllTable(db);
        onCreate(db);
    }

    private boolean updateToVersion2(SQLiteDatabase db) {
        dropAllTable(db);
        onCreate(db);
        return true;
    }

    private boolean updateToVersion3(SQLiteDatabase db) {
        backUpPlayHistoryTableData(db);
        onCreate(db);
        return true;
    }

    private boolean updateToVersion4(SQLiteDatabase db) {
        backUpPlayHistoryTableData(db);
        backUpBroadcastPlayHistoryTableData(db);
        onCreate(db);
        return true;
    }

    private boolean updateToVersion5(SQLiteDatabase db) {
        backUpPlayHistoryTableData(db);
        onCreate(db);
        dropBroadcastHistoryTable(db);
        return true;
    }

    private boolean updateToVersion6(SQLiteDatabase db) {
        backUpPlayHistoryTableData(db);
        return true;
    }

    /**
     * 备份收听历史数据表数据
     *
     * @param db
     */
    private void backUpPlayHistoryTableData(SQLiteDatabase db) {
        boolean bRenameSuc = false;
        try {
            db.execSQL(CREATE_TEMP_PLAY_HISTORY_TABLE);
            bRenameSuc = true;
        } catch (SQLException se) {
        }
        if (bRenameSuc) {
            dropPlayHistoryTable(db);
            createTablePlayHistory(db);
            try {
                db.execSQL(CREATE_NEW_PLAY_HISTORY_NEW_ADD_TABLE);
                db.execSQL(SQL_DROP_TABLE_PLAY_HISTORY_TEMP);
            } catch (SQLException se) {
            }
        }
    }

    /**
     * 创建收听历史临时表SQL语句
     */
    private static final String CREATE_TEMP_PLAY_HISTORY_TABLE = "ALTER TABLE " + TABLE_PLAY_HISTORY +
            " RENAME TO " + TABLE_PLAY_HISTORY_TEMP;
    /**
     * 将收听历史临时表中的数据导入到收听历史新表中
     */
    private static final String CREATE_NEW_PLAY_HISTORY_NEW_ADD_TABLE = "INSERT INTO " + TABLE_PLAY_HISTORY +
            "(" + FIELD_ID + "," + FIELD_TYPE + "," + FIELD_RADIO_ID + "," + FIELD_RADIO_TITLE + "," + FIELD_PIC_URL
            + "," + FIELD_AUDIO_ID + "," + FIELD_AUDIO_TITLE + "," + FIELD_PLAY_URL + "," + FIELD_OFFLINE_PLAY_URL
            + "," + FIELD_PLAYED_TIME + "," + FIELD_DURATION + "," + FIELD_IS_OFFLINE + "," + FIELD_ORDER_NUM + "," + FIELD_SHARE_URL
            + "," + FIELD_TIMESTAMP + "," + FIELD_TYPE_ID + "," + FIELD_CATEGORYID + "," + FIELD_ORDER_MODE + ")"
            + " SELECT " + FIELD_ID + "," + FIELD_TYPE + "," + FIELD_RADIO_ID + "," + FIELD_RADIO_TITLE + "," + FIELD_PIC_URL
            + "," + FIELD_AUDIO_ID + "," + FIELD_AUDIO_TITLE + "," + FIELD_PLAY_URL + "," + FIELD_OFFLINE_PLAY_URL
            + "," + FIELD_PLAYED_TIME + "," + FIELD_DURATION + "," + FIELD_IS_OFFLINE + "," + FIELD_ORDER_NUM + "," + FIELD_SHARE_URL
            + "," + FIELD_TIMESTAMP + "," + "NULL" + "," + "NULL" + "," + "NULL" + " FROM " + TABLE_PLAY_HISTORY_TEMP;
    /**
     * 删除收听历史临时表
     */
    private static final String SQL_DROP_TABLE_PLAY_HISTORY_TEMP = "DROP TABLE IF EXISTS " + TABLE_PLAY_HISTORY_TEMP;


    /**
     * 创建收听历史临时表SQL语句
     */
    private static final String CREATE_TEMP_BROADCAST_PLAY_HISTORY_TABLE = "ALTER TABLE " + TABLE_PLAY_BROADCAST_HISTORY +
            " RENAME TO " + TABLE_PLAY_BROADCAST_HISTORY_TEMP;
    /**
     * 将收听历史临时表中的数据导入到收听历史新表中
     */
    private static final String CREATE_NEW_BROADCAST_PLAY_HISTORY_TABLE = "INSERT INTO " + TABLE_PLAY_BROADCAST_HISTORY +
            " SELECT * FROM " + TABLE_PLAY_BROADCAST_HISTORY_TEMP;
    /**
     * 删除收听历史临时表
     */
    private static final String SQL_DROP_TABLE_BROADCAST_PLAY_HISTORY_TEMP = "DROP TABLE IF EXISTS " + TABLE_PLAY_BROADCAST_HISTORY_TEMP;

    /**
     * 备份传统广播电台收听历史数据
     *
     * @param db
     */
    private void backUpBroadcastPlayHistoryTableData(SQLiteDatabase db) {
        boolean bRenameSuc = false;
        try {
            db.execSQL(CREATE_TEMP_BROADCAST_PLAY_HISTORY_TABLE);
            bRenameSuc = true;
        } catch (SQLException se) {
        }
        if (bRenameSuc) {
            try {
                db.execSQL(CREATE_NEW_BROADCAST_PLAY_HISTORY_TABLE);
                db.execSQL(SQL_DROP_TABLE_BROADCAST_PLAY_HISTORY_TEMP);
            } catch (SQLException se) {
            }
        }
    }

    @Override
    public SQLiteDatabase getWritableDatabase() {
        try {
            return super.getWritableDatabase();
        } catch (Throwable t) {
            t.printStackTrace();
        }
        return null;
    }

    private void dropAllTable(SQLiteDatabase db) {
        dropPlayHistoryTable(db);
        dropBroadcastHistoryTable(db);
        dropAudioPlayHistoryTable(db);
    }

    private void dropPlayHistoryTable(SQLiteDatabase db) {
        db.execSQL(SQL_DROP_TABLE_PLAY_HISTORY);
    }

    private void dropAudioPlayHistoryTable(SQLiteDatabase db) {
        db.execSQL(SQL_DROP_TABLE_AUDIO_PLAY_HISTORY);
    }

    private void dropBroadcastHistoryTable(SQLiteDatabase db) {
        db.execSQL(SQL_DROP_TABLE_BROADCAST_PLAY_HISTORY);
    }

    /**
     * Create Tables.
     */
    private static void createTablePlayHistory(SQLiteDatabase db) {
        StringBuffer sqlPlayHistory = new StringBuffer();
        sqlPlayHistory.append("CREATE TABLE IF NOT EXISTS ").append(TABLE_PLAY_HISTORY)
                .append("(" + FIELD_ID + " INTEGER PRIMARY KEY AUTOINCREMENT,")
                .append(FIELD_TYPE).append(" TEXT,")
                .append(FIELD_RADIO_ID).append(" TEXT,")
                .append(FIELD_RADIO_TITLE).append(" TEXT,")
                .append(FIELD_PIC_URL).append(" TEXT,")
                .append(FIELD_AUDIO_ID).append(" TEXT,")
                .append(FIELD_AUDIO_TITLE).append(" TEXT,")
                .append(FIELD_PLAY_URL).append(" TEXT,")
                .append(FIELD_OFFLINE_PLAY_URL).append(" TEXT,")
                .append(FIELD_PLAYED_TIME).append(" INTEGER,")
                .append(FIELD_DURATION).append(" INTEGER,")
                .append(FIELD_IS_OFFLINE).append(" INTEGER,")
                .append(FIELD_ORDER_NUM).append(" INTEGER,")
                .append(FIELD_SHARE_URL).append(" TEXT,")
                .append(FIELD_TIMESTAMP).append(" TEXT,")
                .append(FIELD_TYPE_ID).append(" INTEGER,")
                .append(FIELD_CATEGORYID).append(" INTEGER,")
                .append(FIELD_ORDER_MODE).append(" INTEGER,")
                .append(FIELD_SOURCE_URL).append(" TEXT)");
        try {
            db.execSQL(sqlPlayHistory.toString());
        } catch (SQLException se) {
            //KL.e(HistoryDbHelper.class, "createTablePlayHistory table failed = {}", se.toString());
        }
    }

    /**
     * 创建播放1000个专辑碎片表
     */
    private static void createTableAudioPlayHistory(SQLiteDatabase db) {
        StringBuffer sqlPlayHistory = new StringBuffer();
        sqlPlayHistory.append("CREATE TABLE IF NOT EXISTS ").append(TABLE_AUDIO_PLAY_HISTORY)
                .append("(" + FIELD_ID + " INTEGER PRIMARY KEY AUTOINCREMENT,")
                .append(FIELD_TYPE).append(" TEXT,")
                .append(FIELD_RADIO_ID).append(" TEXT,")
                .append(FIELD_RADIO_TITLE).append(" TEXT,")
                .append(FIELD_PIC_URL).append(" TEXT,")
                .append(FIELD_AUDIO_ID).append(" TEXT,")
                .append(FIELD_AUDIO_TITLE).append(" TEXT,")
                .append(FIELD_PLAY_URL).append(" TEXT,")
                .append(FIELD_OFFLINE_PLAY_URL).append(" TEXT,")
                .append(FIELD_PLAYED_TIME).append(" INTEGER,")
                .append(FIELD_DURATION).append(" INTEGER,")
                .append(FIELD_IS_OFFLINE).append(" INTEGER,")
                .append(FIELD_ORDER_NUM).append(" INTEGER,")
                .append(FIELD_SHARE_URL).append(" TEXT,")
                .append(FIELD_TIMESTAMP).append(" TEXT,")
                .append(FIELD_ORDER_MODE).append(" INTEGER,")
                .append(FIELD_PARAMS_ONE).append(" TEXT,")
                .append(FIELD_PARAMS_TWO).append(" TEXT)");
        try {
            db.execSQL(sqlPlayHistory.toString());
        } catch (SQLException se) {
            //KL.e(HistoryDbHelper.class, "createTablePlayHistory table failed = {}", se.toString());
        }
    }
}
