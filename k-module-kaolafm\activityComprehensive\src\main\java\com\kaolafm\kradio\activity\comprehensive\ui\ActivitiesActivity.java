package com.kaolafm.kradio.activity.comprehensive.ui;

import android.content.res.Configuration;
import android.os.Bundle;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;
import androidx.constraintlayout.widget.Group;
import androidx.constraintlayout.widget.Guideline;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.kaolafm.kradio.activity.comprehensive.ActivityPresenter;
import com.kaolafm.kradio.common.base.BaseSkinAppCompatActivity;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkManager;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.opensdk.api.activity.model.Activity;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.util.ReportConstants;

import java.lang.ref.WeakReference;
import java.util.List;

/**
 * 活动专区(Activity)显示页面
 */
public class ActivitiesActivity extends BaseSkinAppCompatActivity<ActivityPresenter> implements IActivityView {
    private static final String TAG = ActivitiesActivity.class.getSimpleName();

    Group firstView;
    Group secondView;
    ConstraintLayout mRootLayout;
    RecyclerView rvSub;
    ImageView qrCodeImage3;
    TextView qrCodeTextView3;
    LinearLayout mLoading;
    TextView mNoActivity;
    ViewStub mErrorPage;
    Guideline mGuideline;

    RelativeLayout mErrorLayout;

    StaggeredGridLayoutManager mLayoutManager;
    ActivityAdapter mActivityAdapter;
    NetWorkListener mNetWorkListener;

    @Override
    public int getLayoutId() {
        return R.layout.fragment_activity_ui;
    }

    @Override
    public int getLayoutId_Tow() {
        return 0;
    }

    @Override
    public void initView(Bundle savedInstanceState) {

        firstView=findViewById(R.id.first_view);
        secondView=findViewById(R.id.second_view);
        mRootLayout=findViewById(R.id.activity_root_layout);
        rvSub=findViewById(R.id.rv_sub);
        qrCodeImage3=findViewById(R.id.qrCode_image_3);
        qrCodeTextView3=findViewById(R.id.qrCode_textView_3);
        mLoading=findViewById(R.id.activity_loading);
        mNoActivity=findViewById(R.id.tv_no_activity);
        mErrorPage=findViewById(R.id.vs_layout_error_page);
        mGuideline=findViewById(R.id.activity_top_guideline);
        findViewById(R.id.back_view).setOnClickListener(v -> finish());


        mLayoutManager = new StaggeredGridLayoutManager(1, LinearLayoutManager.VERTICAL);
        rvSub.setLayoutManager(mLayoutManager);
        ((DefaultItemAnimator) rvSub.getItemAnimator()).setSupportsChangeAnimations(false);
        mActivityAdapter = new ActivityAdapter();
        rvSub.setAdapter(mActivityAdapter);
        rvSub.addItemDecoration(new RvItemDecoration());

        NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false);
        // 不管网络状态如何，都需将网络状态监听器注册到网络检测模块，以便在其他场景下引起网络变化时能够自动加载数据。
        mNetWorkListener = new NetWorkListener(this);
        NetworkManager.getInstance().addNetworkReadyListener(mNetWorkListener);

        showAccordingToScreen(ResUtil.getOrientation());
    }

    @Override
    public void initData() {

    }

    @Override
    protected ActivityPresenter createPresenter() {
        return new ActivityPresenter(this);
    }


    @Override
    public void onResume() {
        super.onResume();
        updateData();
    }

    private boolean isNetworkUnavailable() {
        if (!NetworkUtil.isNetworkAvailable(ActivitiesActivity.this, false)) {
            toast(ResUtil.getString(R.string.no_net_work_str));
            return true;
        }
        return false;
    }

    private void toast(String msg) {
        ToastUtil.showOnActivity(ActivitiesActivity.this, msg);
    }

    private void showErrorLayout(String error, boolean clickToRetry) {
        if (mErrorLayout == null) {
            mErrorLayout = (RelativeLayout) mErrorPage.inflate();
            TextView tvNetworkNosign = mErrorLayout.findViewById(R.id.tv_network_nosign);
            tvNetworkNosign.setText(error);
            // 支持点击重试
            if (clickToRetry) {
                ImageView ivNetworkNoSign = mErrorLayout.findViewById(R.id.network_nosigin);
                ivNetworkNoSign.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        ViewUtil.setViewVisibility(mErrorLayout, View.GONE);
                        updateData();
                        TextView tvNetworkNosign = mErrorLayout.findViewById(R.id.tv_network_nosign);
                        String text = null;
                        if (tvNetworkNosign != null) {
                            text = tvNetworkNosign.getText().toString();
                        }
                        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_NETWORK_RETRY, text, getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
                    }
                });
            }
        }
        ViewUtil.setViewVisibility(mErrorLayout, View.VISIBLE);
        if (mErrorLayout != null) {
            TextView tvNetworkNosign = mErrorLayout.findViewById(R.id.tv_network_nosign);
            String text = null;
            if (tvNetworkNosign != null) {
                text = tvNetworkNosign.getText().toString();
            }
            ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_NETWORK_RETRY, text, getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
        }
    }

    @Override
    protected void showAccordingToScreen(int orientation) {
        super.showAccordingToScreen(orientation);

        ViewGroup.MarginLayoutParams layoutParams =
                (ViewGroup.MarginLayoutParams) rvSub.getLayoutParams();
        layoutParams.topMargin = ResUtil.getDimen(R.dimen.m22);

        uploadView(orientation == Configuration.ORIENTATION_LANDSCAPE);
    }

    private void uploadView(boolean isLand) {
        if (isLand) {
            ConstraintSet set = new ConstraintSet();
            set.clone(mRootLayout);
            set.setGuidelinePercent(mGuideline.getId(), 0.25f);
            set.constrainPercentWidth(rvSub.getId(), 0.8f);
            set.applyTo(mRootLayout);
        } else {
            ConstraintSet set = new ConstraintSet();
            set.clone(mRootLayout);
            set.setGuidelinePercent(mGuideline.getId(), 0.11f);
            set.constrainPercentWidth(rvSub.getId(), 0.91f);
            set.applyTo(mRootLayout);
        }
    }

    @Override
    public void showLoading() {
        ViewUtil.setViewVisibility(mLoading, View.VISIBLE);
    }

    @Override
    public void hideLoading() {
        ViewUtil.setViewVisibility(mLoading, View.GONE);
    }

    @Override
    public void onActivityInfo(List<Activity> activityList) {
        ViewUtil.setViewVisibility(secondView, View.GONE);
        ViewUtil.setViewVisibility(firstView, View.VISIBLE);
        mActivityAdapter.setDataList(activityList);
        //initQrData(activityInfo.qrCodeList);
    }

    @Override
    public void showEmpty() {

    }

    @Override
    public void hideEmpty() {

    }

    @Override
    public void showError(String error, boolean clickToRetry) {
        showErrorLayout(error, clickToRetry);
    }

    @Override
    public void changeViewLayoutForStatusBar(View view) {
    }


    public void updateData() {
        if (isNetworkUnavailable()) {
            showError(ResUtil.getString(R.string.network_nosigin), true);
            return;
        }
        if (mPresenter != null) {
            mPresenter.getActivityInfo();
        }
    }
    @Override
    public void onDestroy() {
        super.onDestroy();
        NetworkManager.getInstance().removeNetworkReadyListener(mNetWorkListener);
    }

    public static class NetWorkListener implements NetworkManager.INetworkReady {
        private WeakReference activityFragment;

        public NetWorkListener(ActivitiesActivity fragment) {
            activityFragment = new WeakReference<>(fragment);
        }

        @Override
        public void networkChange(boolean hasNetwork) {
            Log.i("activityFragment", "Network state changed, param [hasNetwork] value is : " + hasNetwork);
            if (!hasNetwork) {
                return;
            }
            ActivitiesActivity fragment = (ActivitiesActivity) this.activityFragment.get();

            if (fragment != null && fragment.mPresenter != null) {
                fragment.mPresenter.getActivityInfo();
            }
        }
    }

    public String getPageId() {
        return Constants.PAGE_ID_ACTIVITY;
    }

    public boolean isReportFragment() {
        return true;
    }
}

