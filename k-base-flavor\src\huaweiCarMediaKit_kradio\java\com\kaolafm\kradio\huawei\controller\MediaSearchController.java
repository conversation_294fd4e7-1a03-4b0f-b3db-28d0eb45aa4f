package com.kaolafm.kradio.huawei.controller;

import com.huawei.carmediakit.bean.OperResult;
import com.huawei.carmediakit.constant.ErrorCode;
import com.huawei.carmediakit.controller.IMediaSearchController;
import com.kaolafm.kradio.huawei.utils.Constant;
import com.kaolafm.kradio.k_kaolafm.search.SearchHistoryManager;
import com.kaolafm.kradio.lib.utils.Logger;

public class MediaSearchController implements IMediaSearchController {
    public static final String TAG = Constant.TAG;

    @Override
    public OperResult clearSearchHistory() {
        Logger.i(TAG, "clearSearchHistory");
        SearchHistoryManager.getInstance().clearAllRecentSearchTags();
        return new OperResult(ErrorCode.SUCCESS, "");
    }
}
