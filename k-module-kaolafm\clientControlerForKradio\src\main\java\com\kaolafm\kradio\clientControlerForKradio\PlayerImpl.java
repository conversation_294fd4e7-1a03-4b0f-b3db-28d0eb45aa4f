package com.kaolafm.kradio.clientControlerForKradio;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.os.Process;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.lib.report.ReportUtil;
import com.kaolafm.kradio.home.HomeDataManager;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.IKLDataChangeInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioPlayLogicInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioClientPlayRetryInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioClientPlayerInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioClientProgressCorrectInter;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.bean.BroadcastRadioSimpleData;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.opensdk.player.core.listener.KLAudioStateChangedByAudioFocusListener;
import com.kaolafm.opensdk.player.core.utils.PlayerCustomizeManager;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.UIThreadUtil;
import com.kaolafm.report.event.PlayerUiControlReportEvent;
import com.kaolafm.report.util.ReportConstants;
import com.kaolafm.sdk.core.ErrorInfo;
import com.kaolafm.sdk.core.Music;
import com.kaolafm.sdk.core.PlayListener;
import com.kaolafm.sdk.core.PlayState;

import java.util.ArrayList;
import java.util.List;

import static com.kaolafm.kradio.clientControlerForKradio.PlayerImpl.PlayerActionPgk.DO_PLAY_ACTION;
import static com.kaolafm.kradio.lib.utils.UrlUtil.getLocalPicUri;

/**
 * <AUTHOR>
 **/
public class PlayerImpl implements ClientPlayer {
    private static final String TAG = "client.impl.play";

    private Music mMusic;
    private Context mContext;
    private IKLDataChangeInter mIklDataChangeInter;
    private Handler mHandler = new Handler(Looper.getMainLooper());
    private KRadioClientPlayerInter mKRadioClientPlayerInter;
    private KRadioAudioPlayLogicInter mKRadioAudioPlayLogicInter;
    private KRadioClientProgressCorrectInter mProgressCorrectInter;
    private KRadioClientPlayRetryInter mKRadioClientPlayRetryInter;
    private PlayerActionPgk mPlayerActionPgk = new PlayerActionPgk();
    //监听因被动失去音频焦点导致音频暂停
    private KLAudioStateChangedByAudioFocusListener mKLAudioStateChangedByAudioFocusListener;


    /**
     * 处理播放器未初始化完成时调用了与播放器相关接口后的业务逻辑对象
     */
    static class PlayerActionPgk {
        static final String DO_PLAY_ACTION = "play";
        String doAction;
    }

    /**
     * 是否在播放器初始化完成后申请音频焦点true为是，false为否
     */
    private boolean isNeedRequestAudioFocusOnPlayerInit;
    private final BasePlayStateListener mListener = new BasePlayStateListener() {

        @Override
        public void onIdle(PlayItem playItem) {

        }

        @Override
        public void onPlayerPreparing(PlayItem playItem) {
            Log.i(TAG, "onPlayerPreparing: ");
            if (mPlayListeners != null && !mPlayListeners.isEmpty() && mMusic != null) {
                for (int i = 0; i < mPlayListeners.size(); i++) {
                    try {
                        PlayListener playListener = mPlayListeners.get(i);
                        if (playListener != null) {
                            playListener.onStartPrepare(mMusic);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }

        @Override
        public void onPlayerPlaying(PlayItem playItem) {
            Log.i(TAG, "onPlayerPlaying: ");
            if (mPlayListeners != null && !mPlayListeners.isEmpty()) {
                Log.i(TAG, "onPlayerPlaying: mPlayListeners.size() = " + mPlayListeners.size());
                String type = String.valueOf(PlayerManagerHelper.getInstance().getCurPlayItem().getType());
                mMusic = DataConverter.toMusic(type, playItem);
                mMusic = changeMusic(mMusic);
                Log.i(TAG, "music pic url = " + mMusic.localPicUri + ", name = " + mMusic.audioName);
                for (int i = 0; i < mPlayListeners.size(); i++) {
                    try {
                        PlayListener playListener = mPlayListeners.get(i);
                        if (playListener != null) {
                            playListener.onPlaying(mMusic);
                            playListener.onPlayMusic(mMusic);
                            playListener.onPlayStateChange(PlayState.PLAYING, 2, mMusic);
                            Log.i(TAG, "onPlayerPlaying, playListener: " + playListener + ", hashCode: " + playListener.hashCode() + ", PlayState: " + PlayState.PLAYING + ", pid: " + Thread.currentThread().getId() + ", processID: " + Process.myPid());
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }

        @Override
        public void onPlayerPaused(PlayItem playItem) {
            Log.i(TAG, "onPlayerPaused: ");
            if (mPlayListeners != null && !mPlayListeners.isEmpty() && mMusic != null) {
                Log.i(TAG, "onPlayerPaused: mPlayListeners.size() = " + mPlayListeners.size());
                for (int i = 0; i < mPlayListeners.size(); i++) {
                    try {
                        PlayListener playListener = mPlayListeners.get(i);
                        if (playListener != null) {
                            playListener.onPause(mMusic);
                            playListener.onPlayStateChange(PlayState.PAUSED, 2, mMusic);
                            Log.i(TAG, "onPlayerPaused, playListener: " + playListener + ", hashCode: " + playListener.hashCode() + ", PlayState: " + PlayState.PAUSED + ", pid: " + Thread.currentThread().getId() + ", processID: " + Process.myPid());
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }

        @Override
        public void onProgress(PlayItem playItem, long progress, long duration) {
//            Log.i(TAG, "onProgress: " + progress);
            if(mProgressCorrectInter != null) {
                progress = mProgressCorrectInter.correctProgress(progress);
                Log.i(TAG, "onProgress correct: " + progress);
            }

            mProgress = progress;

            if (mMusic != null) {
                mMusic.progress = progress;
                mMusic.duration = duration;
            }else {
                return;
            }

            if (mPlayListeners != null && !mPlayListeners.isEmpty()) {

//                PlayItem playItem = mPlayerManager.getCurrentPlayItem();
//                if (playItem == null) {
//                    return;
//                }
//                playItem.setPosition(progress);

                for (int i = 0; i < mPlayListeners.size(); i++) {
                    try {
                        PlayListener playListener = mPlayListeners.get(i);
                        if (playListener != null) {
                            playListener.onProgress(mMusic, progress);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }

        @Override
        public void onPlayerFailed(PlayItem playItem, int a, int a1) {
            Log.i(TAG, "onPlayerFailed: ");
            if (mPlayListeners != null && !mPlayListeners.isEmpty()) {
                for (int i = 0; i < mPlayListeners.size(); i++) {
                    try {
                        mPlayListeners.get(i).onError(new ErrorInfo(-1));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }

        @Override
        public void onPlayerEnd(PlayItem playItem) {
            Log.i(TAG, "onPlayerEnd: ");
            if (mPlayListeners != null && !mPlayListeners.isEmpty()) {
                for (int i = 0; i < mPlayListeners.size(); i++) {
                    try {
                        PlayListener playListener = mPlayListeners.get(i);
                        if (playListener != null) {
                            playListener.onCompleted();
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    };

    /**
     * 音频焦点变化的时候（见com.kaolafm.kradio.clientControlerForKradio.ClientImpl#setAudioFocusChangeListener(com.kaolafm.sdk.client.IAudioFocusChangeListener))
     * 通知外调sdk PlayStateListener的onPlayStateChange方法
     * @return
     */
    protected void onPlayStateChangeInAudioFocusChange(int flag) {
        Log.i(TAG, "onPlayStateChange: listener size：" + mPlayListeners.size());
        if(mMusic == null)return;
        for (int i = 0; i < mPlayListeners.size(); i++) {
            try {
                PlayListener playListener = mPlayListeners.get(i);
                if (playListener != null) {
                    if (flag == 1) {
                        //1. 焦点丢失
                        if (PlayerManagerHelper.getInstance().isPlaying()) {
                            Log.i(TAG, "onPlayStateChange: PLAYING, focus gain");
                            playListener.onPlayStateChange(PlayState.PLAYING, 1, mMusic);
                            Log.i(TAG, "onPlayStateChangeInAudioFocusChange01, playListener: " + playListener + ", hashCode: " + playListener.hashCode() + ", PlayState: " + PlayState.PLAYING + ", pid: " + Thread.currentThread().getId() + ", processID: " + Process.myPid());
                        } else {
                            Log.i(TAG, "onPlayStateChange: PAUSED, focus gain");
                            playListener.onPlayStateChange(PlayState.PAUSED, 1, mMusic);
                            Log.i(TAG, "onPlayStateChangeInAudioFocusChange02, playListener: " + playListener + ", hashCode: " + playListener.hashCode() + ", PlayState: " + PlayState.PAUSED + ", pid: " + Thread.currentThread().getId() + ", processID: " + Process.myPid());
                        }
                    } else {
                        //1. 焦点丢失
                        Log.i(TAG, "onPlayStateChange: PAUSED, focus loss");
                        playListener.onPlayStateChange(PlayState.PAUSED, 1, mMusic);
                        Log.i(TAG, "onPlayStateChangeInAudioFocusChange03, playListener: " + playListener + ", hashCode: " + playListener.hashCode() + ", PlayState: " + PlayState.PAUSED + ", pid: " + Thread.currentThread().getId() + ", processID: " + Process.myPid());
                    }

                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private List<PlayListener> mPlayListeners;
    private long mProgress;

    public PlayerImpl(Context context) {
        mContext = context;
        mPlayListeners = new ArrayList<>();
        // 解决伙伴https://app.huoban.com/tables/2100000007530121/items/2300001011828468?userId=1917386问题
        initPlayer();
        mKRadioClientPlayerInter = ClazzImplUtil.getInter("KRadioClientPlayerImpl");
        mKRadioAudioPlayLogicInter = ClazzImplUtil.getInter("KRadioAudioPlayLogicImpl");
        mProgressCorrectInter = ClazzImplUtil.getInter("KRadioClientProgressCorrectImpl");
        mKRadioClientPlayRetryInter = ClazzImplUtil.getInter("KRadioClientPlayRetryImpl");
    }

    private final IPlayerInitCompleteListener iPlayerInitCompleteListener = new IPlayerInitCompleteListener() {
        @Override
        public void onPlayerInitComplete(boolean b) {
            try {
                PlayerManager.getInstance().removePlayerInitComplete(this);
            } catch (Exception e) {
            }
            PlayerManager.getInstance().addPlayControlStateCallback(mListener);
            // 解决https://app.huoban.com/tables/2100000007530121/items/2300001655437533?userId=1229522问题
            if (isNeedRequestAudioFocusOnPlayerInit) {
                isNeedRequestAudioFocusOnPlayerInit = false;
                PlayerManager.getInstance().requestAudioFocus();
            }
            Log.i(TAG, "onPlayerInitComplete doAction = " + mPlayerActionPgk.doAction);
            if (DO_PLAY_ACTION.equals(mPlayerActionPgk.doAction)) {
                resume();
            }
            mPlayerActionPgk.doAction = null;
        }
    };

    private void initPlayer() {
        PlayerManager playerManager = PlayerManager.getInstance();
        boolean isInitSuccess = playerManager.isPlayerInitSuccess();
        Log.i(TAG, "initPlayer isInitSuccess = " + isInitSuccess);
        if (isInitSuccess) {
            PlayerManager.getInstance().addPlayControlStateCallback(mListener);
        } else {
            PlayerManager.getInstance().addPlayerInitComplete(iPlayerInitCompleteListener);
            PlayerManager.getInstance().init(mContext);
        }
        mIklDataChangeInter = (IKLDataChangeInter) ClazzImplUtil.getInter("IKLDataChangeImpl");
    }

    public void resume() {
        boolean isInitSuccess = PlayerManager.getInstance().isPlayerInitSuccess();
        Log.i(TAG, "resume isInitSuccess = " + isInitSuccess);
        if (isInitSuccess) {
            PlayerManager.getInstance().requestAudioFocus();
        } else {
            mPlayerActionPgk.doAction = DO_PLAY_ACTION;
            isNeedRequestAudioFocusOnPlayerInit = true;
            return;
        }
        ReportUtil.addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_PLAY, PlayerUiControlReportEvent.CONTROL_TYPE_VOICE, null);
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001886958569?userId=1229522问题
        if (PlayerManagerHelper.getInstance().isPlayerResAavalible()) {
            boolean isPlaying = PlayerManagerHelper.getInstance().isPlaying();
            Log.i(TAG, "resume isPlaying = " + isPlaying);
            if (!isPlaying) {
                PlayerManagerHelper.getInstance().switchPlayerStatus(true);
            }
        } else {
            if (mKRadioClientPlayerInter != null && mKRadioClientPlayerInter.doIgnorePlayAction(mContext)) {
                return;
            }
            if (NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
                HomeDataManager.getInstance().autoPlay();
            } else {
                Log.i(TAG, "resume no network!");
                if (mKRadioClientPlayRetryInter != null) {
                    mKRadioClientPlayRetryInter.retry();
                }
            }
        }
    }

    public void play(PlayParam playParam) {
        play(playParam, false);
    }

    public void playSearchData(PlayParam playParam) {
        play(playParam, true);
    }

    private void play(PlayParam playParam, boolean isPlaySearchData) {
        Log.i(TAG, "play: playParam=" + playParam);
        if (ResType.TYPE_BROADCAST == playParam.resType) {
            BroadcastRadioSimpleData data = new BroadcastRadioSimpleData();
            data.setBroadcastId(Long.valueOf(playParam.id));
            data.setImg(playParam.img);
            if (!TextUtils.isEmpty(playParam.fm)) {
                data.setName(playParam.title + "  " + playParam.fm);
            } else {
                data.setName(playParam.title + "  " + " ");
            }
            PlayerManagerHelper.getInstance().addBroadcastRadioSimpleItem(data);
        }else if (ResType.TYPE_TV == playParam.resType) {
            BroadcastRadioSimpleData data = new BroadcastRadioSimpleData();
            data.setBroadcastId(Long.valueOf(playParam.id));
            data.setImg(playParam.img);
            if (!TextUtils.isEmpty(playParam.fm)) {
                data.setName(playParam.title + "  " + playParam.fm);
            } else {
                data.setName(playParam.title + "  " + " ");
            }
            data.setResType(ResType.TYPE_TV);
            PlayerManagerHelper.getInstance().addBroadcastRadioSimpleItem(data);
        }
        ReportUtil.addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_PLAY, PlayerUiControlReportEvent.CONTROL_TYPE_VOICE, null);
        if (isPlaySearchData) {
            PlayerManagerHelper.getInstance().startSearchData(String.valueOf(playParam.id), playParam.resType);
        } else {
            PlayerManagerHelper.getInstance().start(String.valueOf(playParam.id), playParam.resType);
        }
        if(mMusic == null)return;

        for (int i = 0; i < mPlayListeners.size(); i++) {
            try {
                PlayListener playListener = mPlayListeners.get(i);
                if (playListener != null) {
                    Log.i(TAG, "onPlayStateChange: PLAYING, clientsdk operation");
                    playListener.onPlayStateChange(PlayState.PLAYING, 2 ,mMusic);
                    Log.i(TAG, "play, playListener: " + playListener + ", hashCode: " + playListener.hashCode() + ", PlayState: " + PlayState.PLAYING + ", pid: " + Thread.currentThread().getId() + ", processID: " + Process.myPid());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public void play(List<PlayParam> playParams, int beginIndex) {
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001757536741?userId=1229522问题
        if (ListUtil.isEmpty(playParams) || playParams.size() - 1 < beginIndex) {
            return;
        }
        //旧版播放器不支持Audio播单
        PlayParam playParam = playParams.get(beginIndex);
        play(playParam);
        //mPlayerManager.play(playParam.id, String.valueOf(playParam.resType));
    }

    public void pause() {
        ReportUtil.addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_PAUSE, PlayerUiControlReportEvent.CONTROL_TYPE_VOICE, null);
        PlayerManagerHelper.getInstance().pause(true);
        if(mMusic == null)return;
        for (int i = 0; i < mPlayListeners.size(); i++) {
            try {
                PlayListener playListener = mPlayListeners.get(i);
                if (playListener != null) {
                    Log.i(TAG, "onPlayStateChange: PAUSED, clientsdk operation");
                    playListener.onPlayStateChange(PlayState.PAUSED, 2 ,mMusic);
                    Log.i(TAG, "pause, playListener: " + playListener + ", hashCode: " + playListener.hashCode() + ", PlayState: " + PlayState.PAUSED + ", pid: " + Thread.currentThread().getId() + ", processID: " + Process.myPid());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public void jumpTo(int position){
        PlayerManagerHelper.getInstance().seek(position);
    }

    @Override
    public void stop() {
        PlayerCustomizeManager.getInstance().unregisterAudioStateChangedByAudioFocusListener(mKLAudioStateChangedByAudioFocusListener);
        PlayerManager.getInstance().stop();
    }

    @Override
    public void setNeedRequestAudioFocusOnPlayerInit(boolean needRequestAudioFocusOnPlayerInit) {
        isNeedRequestAudioFocusOnPlayerInit = needRequestAudioFocusOnPlayerInit;
    }

    @Override
    public void playNext() {
        if (PlayerManagerHelper.getInstance().hasNextItem()) {
            ReportUtil.addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_NEXT, PlayerUiControlReportEvent.CONTROL_TYPE_VOICE, null);
            ReportUtil.reportEndPlay(ReportConstants.PLAY_CHANGE_BY_OTHER, true);
            PlayerManagerHelper.getInstance().playNext(true);
        } else {
            UIThreadUtil.runUIThread(() -> ToastUtil.showInfo(mContext, R.string.is_last_one_warning_str));
        }
    }

    @Override
    public void playPrev() {
        if (PlayerManagerHelper.getInstance().hasPreItem()) {
            ReportUtil.addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_PREVIOUS, PlayerUiControlReportEvent.CONTROL_TYPE_VOICE, null);
            ReportUtil.reportEndPlay(ReportConstants.PLAY_CHANGE_BY_OTHER, true);
            mHandler.post(new Runnable() {
                @Override
                public void run() {
                    PlayerManagerHelper.getInstance().playPre(true);
                }
            });
        } else {
            UIThreadUtil.runUIThread(() -> ToastUtil.showInfo(mContext, R.string.is_first_one_warning_str));
        }
    }

    @Override
    public int getPlayState() {
        return PlayerManagerHelper.getInstance().isPlaying() ? 1 : 0;
    }

    @Override
    public long getProgress() {
        return mProgress;
    }

    @Override
    public void forward(int second) {
        PlayerManagerHelper.getInstance().fastForward();
    }

    @Override
    public void backward(int second) {
        PlayerManagerHelper.getInstance().fastBackward();
    }

    /**
     * 前进指定的秒数
     * @param seconds
     */
    public void forwardExactSeconds(int seconds){
        PlayerManagerHelper.getInstance().fastForward(seconds);
    }

    /**
     * 后退指定的秒数
     * @param seconds
     */
    public void backwardExactSeconds(int seconds){
        PlayerManagerHelper.getInstance().fastBackward(seconds);
    }

    @Override
    public boolean hasPrev() {
        return PlayerManagerHelper.getInstance().hasPreItem();
    }

    @Override
    public boolean hasNext() {
        return PlayerManagerHelper.getInstance().hasNextItem();
    }

    @Override
    public PlayParam getPlayParam() {
        PlayParam playParam = new PlayParam();
        playParam.id = PlayerManagerHelper.getInstance().getSubscribeId();
        playParam.resType = PlayerManagerHelper.getInstance().getCurPlayItem().getType();

        return playParam;
    }

    @Override
    public void registerPlayListener(PlayListener playListener) {
        Log.i(TAG, "registerPlayListener------>" + playListener);
        if (playListener == null) {
            return;
        }
        for (int i = mPlayListeners.size() - 1; i >= 0; i--) {
            PlayListener tempPlayListener = mPlayListeners.get(i);
            if (playListener.asBinder() == tempPlayListener.asBinder()) {
                Log.i(TAG, "registerPlayListener------>return");
                return;
            }
        }
        mPlayListeners.add(playListener);
        //用于监听用户手动暂停
        PlayerManagerHelper.getInstance().addClientPlayListener(playListener);
        PlayerManager.getInstance().addPlayControlStateCallback(mListener);
        if (PlayerManagerHelper.getInstance().isPlaying()) {
            Music music = getMusicInfo();
            try {
                playListener.onPlayMusic(music);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void unregisterPlayListener(PlayListener playListener) {
        Log.i(TAG, "unregisterPlayListener------>" + playListener);
        mPlayListeners.remove(playListener);
        PlayerManagerHelper.getInstance().removeClientPlayListener(playListener);
    }

    @Override
    public Music getMusicInfo() {

        Music rst = null;

        if (mMusic != null) {
            rst = mMusic;
        } else {
            PlayItem currentPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
            //https://app.huoban.com/tables/2100000007530121/items/2300001631649177?userId=1881599
            if (currentPlayItem != null && currentPlayItem.getType() != -1) {
                rst = new Music();
                rst.audioId = currentPlayItem.getAudioId();
                rst.audioName = currentPlayItem.getTitle();
                rst.albumId = PlayerManagerHelper.getInstance().getSubscribeId();
                rst.albumName = currentPlayItem.getAlbumTitle();
                rst.type = currentPlayItem.getType();
                rst.progress = mProgress;
                rst.duration = currentPlayItem.getDuration();

                rst.picUrl = PlayerManagerHelper.getInstance().getPlayItemPicUrl(currentPlayItem);
                rst.localPicUri = getLocalPicUri(rst.picUrl);
            }
        }
        return rst;
    }

    //渠道需求转换Music
    private Music changeMusic(Music music) {
        if (mIklDataChangeInter != null) {
            music = (Music) (mIklDataChangeInter.changeData(music));
        }
        return music;
    }
}
