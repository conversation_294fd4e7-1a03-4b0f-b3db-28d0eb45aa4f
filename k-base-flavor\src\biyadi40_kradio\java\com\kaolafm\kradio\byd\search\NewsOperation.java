package com.kaolafm.kradio.byd.search;

import android.content.Intent;

import com.kaolafm.kradio.clientControlerForKradio.ClientImpl;

/**
 * 新闻节目搜索
 * 参数定义：
 * 除节目类型以外，其余参数字段可任意组合，三方应用可以根据应用功能选择对接字段，
 * 若不支持按照单独的参数进行搜索，可以将单独参数字段，按照关键词进行搜索，但是智能语音传过来的这些参数必须处理
 *
 * intent.putExtra("IS_BACKGROUND", "TRUE");//字符串类型，该参数非必须项，参数值为"TRUE"时为导航在前台，参数值为"FALSE"时，默认前台
 * intent.putExtra("EXTRA_LOCATION_SEARCH","广东省|深圳市|宝安区");//字符串类型，地点（安徽|北京|广州…），多个标签时用"|"分隔
 * intent.putExtra("EXTRA_DATETIM_SEARCH,""2021-07-12|周一|早上");//字符串类型，时间，多个标签时用"|"分隔
 * intent.putExtra("EXTRA_MEDIA_SEARCH","新浪");//字符串类型，来源媒体（新浪|网易|凤凰|腾讯|搜狐|雅虎|百度|谷歌|环球）
 * intent.putExtra("EXTRA_PROGRAM_TAGS","体育");//字符串类型，类型（热点|体育|科技|房产|军事|经视|娱乐|经济|时政|互联网|汽车|音频|游戏|食品|教育|社会|人物|儿童|女性|航空|健康|环保|天气|图片|城市|旅游|公益|交友|育儿|读书|家居|地产|微博|博客|明星|高考|篮球|足球|基金|数码|电影|音乐|家电|情感|美容|购物|论坛|鲜果|文娱|搞笑|文艺|星座|母婴|城建|政法|国际|奥运|奥运会|奥运会徽|伦敦奥运会|伦敦奥运|奥运金牌榜|奥运火炬手|奥运金牌榜|欧洲杯|股票）
 * intent.putExtra("EXTRA_KEYWORDS_SEARCH","英超|欧洲杯|林书豪");//字符串类型，关键词（英超|欧洲杯|林书豪|小米手机|nba体育|nba…），多个关键词时用"|"分隔
 * intent.putExtra("EXTRA_TYPE_SEARCH","新闻");//字符串类型，节目类型（新闻/在线节目）
 * 返回值定义：
 * 0：执行成功
 * 1：其他执行失败
 * 2：超出可设置的范围
 * 3：当前场景不支持该功能
 * 4：需手动操作用户协议后执行
 * 5：需登录后支持
 * 6：网络异常
 * 7：当前功能中暂无数据信息
 */
public class NewsOperation extends Operation{
    public String category = "116"; //资讯

    public NewsOperation(ClientImpl client, Intent intent) {
        super(client, intent);
    }

    @Override
    public void exe() {
        String keywords = intent.getStringExtra("EXTRA_KEYWORDS_SEARCH");
        String location = intent.getStringExtra("EXTRA_LOCATION_SEARCH");
        String datetime = intent.getStringExtra("EXTRA_DATETIM_SEARCH");
        String media = intent.getStringExtra("EXTRA_MEDIA_SEARCH");
        String programTags = intent.getStringExtra("EXTRA_PROGRAM_TAGS");

        doSearch(keywords, 1, 2, null, null,
                null, category);
    }
}
