<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/pc_select_head_title_height">

    <ImageView
        android:id="@+id/pc_iv_back"
        android:layout_width="@dimen/m80"
        android:layout_height="@dimen/m80"
        android:layout_marginLeft="@dimen/m20"
        android:background="@drawable/color_main_button_click_selector"
        android:scaleType="centerInside"
        android:src="@drawable/globle_arrow_normal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/personCommend_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/personlityrecommendation_title"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/m28"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/pc_jump"
        android:layout_width="@dimen/pc_select_head_button_width"
        android:layout_height="@dimen/pc_select_head_button_height"
        android:layout_centerVertical="true"
        android:layout_marginRight="@dimen/y15"
        android:background="@drawable/pc_jump"
        android:gravity="center"
        android:text="@string/pc_jump_str"
        android:textColor="@color/pc_text_selector"
        android:textSize="@dimen/text_size1"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>