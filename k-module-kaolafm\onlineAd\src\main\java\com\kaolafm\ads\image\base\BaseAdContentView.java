package com.kaolafm.ads.image.base;

import android.content.Context;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import android.util.AttributeSet;
import android.widget.ImageView;

import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageConfigImpl;
import com.kaolafm.kradio.lib.utils.imageloader.ImageConfigImpl.Builder;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.lib.utils.imageloader.listener.OnImageLoaderListener;

public abstract class BaseAdContentView<T> extends ConstraintLayout {
    protected AdImageListener mAdImageListener;

    public BaseAdContentView(Context context) {
        this(context, null);
    }

    public BaseAdContentView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public BaseAdContentView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void setAdImageListener(AdImageListener adImageListener) {
        mAdImageListener = adImageListener;
    }

    public abstract void loadAdContent(T t);

    public void show() {
        this.setVisibility(VISIBLE);
    }

    public void hide() {
        this.setVisibility(INVISIBLE);
    }

    protected void displayImage(ImageView view, String url, OnImageLoaderListener listener) {
        Builder builder = ImageConfigImpl.builder().url(url)
                .onImageLoaderListener(listener)
                .cacheStrategy(null)
                .imgType(ImageConfigImpl.TYPE_UNKNOWN)
                .isCenterCrop(false)
                .imageView(view);
        ImageLoader.getInstance().displayImage(getContext(), builder);
    }

    protected void displayImage(ImageView view, String url, int imageRadius, OnImageLoaderListener listener) {
        Builder builder = ImageConfigImpl.builder().url(url)
                .onImageLoaderListener(listener)
                .cacheStrategy(null)
                .imgType(ImageConfigImpl.TYPE_UNKNOWN)
                .isCenterCrop(false)
                .imageRadius(imageRadius)
                .imageView(view);
        ImageLoader.getInstance().displayImage(getContext(), builder);
    }

    protected void displayLocalImage(ImageView view, String path) {
        ImageLoader.getInstance().displayLocalImage(getContext(), path, view);
    }

    protected void displayLocalImage(ImageView view, String path, int imageRadius) {
        ImageLoader.getInstance().displayLocalIRoundmage(getContext(), path, view, imageRadius);
    }

    public class AdOnImageLoaderListener implements OnImageLoaderListener {

        @Override
        public void onLoadingFailed(String url, ImageView target, Exception exception) {
            if (mAdImageListener != null) {
                mAdImageListener.onAdImageLoadFailed();
            }
        }

        @Override
        public void onLoadingComplete(String url, ImageView target) {
            if (mAdImageListener != null) {
                mAdImageListener.onAdImageLoaded(BaseAdContentView.this);
            }
        }
    }
}
