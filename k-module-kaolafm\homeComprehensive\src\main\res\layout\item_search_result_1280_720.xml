<?xml version="1.0" encoding="utf-8"?>
<com.kaolafm.kradio.component.ui.base.view.ScaleConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_search_result"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/search_guide_line_center"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.5" />

    <com.kaolafm.kradio.component.ui.base.view.OvalImageView
        android:id="@+id/iv_search_cover"
        android:layout_width="@dimen/m114"
        android:layout_height="@dimen/m114"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="H,1:1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_default="percent"
        app:layout_constraintWidth_percent="0.218"
        app:rid_type="4" />

    <!--描边，日间模式有-->
    <!--    <View-->
    <!--        android:layout_width="0dp"-->
    <!--        android:layout_height="0dp"-->
    <!--        android:background="@drawable/sh_bg_r8l"-->
    <!--        app:layout_constraintBottom_toBottomOf="@+id/iv_search_cover"-->
    <!--        app:layout_constraintLeft_toLeftOf="@+id/iv_search_cover"-->
    <!--        app:layout_constraintRight_toRightOf="@+id/iv_search_cover"-->
    <!--        app:layout_constraintTop_toTopOf="@+id/iv_search_cover" />-->

    <com.kaolafm.kradio.component.ui.base.view.OvalImageView
        android:id="@+id/vip_icon"
        android:layout_width="@dimen/m52"
        android:layout_height="@dimen/m24"
        android:layout_alignStart="@+id/iv_search_cover"
        android:layout_alignTop="@+id/iv_search_cover"
        app:rid_type="1"
        tools:src="@drawable/comprehensive_icon_vip" />

    <View
        android:id="@+id/search_bg_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/bg_search_result"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/iv_search_cover"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <com.kaolafm.kradio.component.ui.base.view.RateView
        android:id="@+id/audioAnimationIcon"
        android:layout_width="@dimen/m28"
        android:layout_height="@dimen/m28"
        android:layout_marginStart="@dimen/x33"
        app:layout_constraintBottom_toBottomOf="@id/tv_search_title"
        app:layout_constraintLeft_toRightOf="@id/iv_search_cover"
        app:layout_constraintTop_toTopOf="@id/tv_search_title" />
    <com.kaolafm.kradio.search.KeywordTextView
        android:id="@+id/tv_search_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/x15"
        android:layout_marginEnd="@dimen/x84"
        android:layout_marginBottom="@dimen/y6"
        android:ellipsize="end"
        android:maxEms="@integer/search_result_title_length_1280_720"
        android:singleLine="true"
        android:textColor="@color/search_result_title_text_color"
        android:textSize="@dimen/search_text_size"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@id/tv_search_sub_title"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintLeft_toRightOf="@id/audioAnimationIcon"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_goneMarginStart="@dimen/x30"
        tools:text="标题标题标题标题" />

    <TextView
        android:id="@+id/typeTv"
        android:layout_width="@dimen/x48"
        android:layout_height="@dimen/y26"
        android:layout_marginStart="@dimen/x30"
        android:background="@drawable/comprehensive_search_result_item_type_bg"
        android:gravity="center"
        android:textColor="@color/comprehensive_search_result_flag_color"
        android:textSize="@dimen/m18"
        app:layout_constraintBottom_toBottomOf="@id/tv_search_sub_title"
        app:layout_constraintStart_toEndOf="@id/iv_search_cover"
        app:layout_constraintTop_toTopOf="@id/tv_search_sub_title"
        app:layout_goneMarginStart="0dp"
        tools:text="广播" />

    <TextView
        android:id="@+id/tv_search_sub_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/x6"
        android:layout_marginEnd="@dimen/m78"
        android:ellipsize="end"
        android:maxEms="@integer/search_result_subtitle_length_1280_720"
        android:maxLines="1"
        android:singleLine="true"
        android:textColor="@color/comprehensive_search_subtitle_color"
        android:textSize="@dimen/search_item_content_title_text_size"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@id/typeTv"
        app:layout_constraintTop_toBottomOf="@id/tv_search_title"
        tools:text="小标题小标题小标题小标题小标题小标题小标题小标题小标题小标题小标题小标题小标题小标题小标题小标题小标题小标题小标题小标题" />

</com.kaolafm.kradio.component.ui.base.view.ScaleConstraintLayout>