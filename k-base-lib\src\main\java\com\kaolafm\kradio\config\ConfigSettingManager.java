package com.kaolafm.kradio.config;

import android.util.Log;

import com.kaolafm.opensdk.OpenSDK;
import com.kaolafm.opensdk.api.config.ConfigSettingOption;
import com.kaolafm.opensdk.api.config.ConfigSettingRequest;
import com.kaolafm.opensdk.api.config.IConfigSettingOptionListener;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;


/**
 * config配置信息管理类
 *
 * <AUTHOR>
 * @date 2023-02-28
 */
public class ConfigSettingManager {

    private static final String TAG = "ConfigSettingManager";


    private volatile static ConfigSettingManager mInstance;

    private ConfigSettingManager() {
    }

    public static ConfigSettingManager getInstance() {
        if (mInstance == null) {
            synchronized (ConfigSettingManager.class) {
                if (mInstance == null) {
                    mInstance = new ConfigSettingManager();
                }
            }
        }
        return mInstance;
    }

    private ConfigSettingOption mConfigSettingOption;

    /**
     * 此处设置config的方法为private
     *
     * @param configSettingOption
     */
    private void setConfigSetting(ConfigSettingOption configSettingOption) {
        this.mConfigSettingOption = configSettingOption;
    }

    /**
     * 获取config配置信息
     *
     * @param iConfigSettingOptionGetListener
     */
    public void getConfigSetting(IConfigSettingOptionListener iConfigSettingOptionGetListener) {
        if (mConfigSettingOption == null) {
            if (OpenSDK.getInstance().isActivate()) {
                new ConfigSettingRequest().getConfigSwitchInfos(new HttpCallback<ConfigSettingOption>() {
                    @Override
                    public void onSuccess(ConfigSettingOption configSettingOption) {
                        setConfigSetting(configSettingOption);
                        iConfigSettingOptionGetListener.onGetSuccess(configSettingOption);
                    }

                    @Override
                    public void onError(ApiException e) {
                        Log.d(TAG, "onError: " + e.getMessage());
                        iConfigSettingOptionGetListener.onGetFailure(e);
                    }
                });
            } else {
                Log.d(TAG, "onError: open sdk is not activated");
                iConfigSettingOptionGetListener.onGetFailure(new ApiException(-1, "open sdk is not activated"));
            }
        } else {
            iConfigSettingOptionGetListener.onGetSuccess(mConfigSettingOption);
        }
    }

    /**
     * 是否开启动画
     *
     * @param callback
     */
    public void isPlayAnimation(OnResultCallback<Boolean> callback) {
        getConfigSetting(new IConfigSettingOptionListener() {
            @Override
            public void onGetSuccess(ConfigSettingOption configSettingOption) {
                if (callback == null) return;
                if (configSettingOption == null) {
                    callback.onResult(false);
                    return;
                }
                callback.onResult(configSettingOption.getIsPlayAnim() == 1);
            }

            @Override
            public void onGetFailure(ApiException e) {
                if (callback == null) return;
                callback.onResult(false);
            }
        });
    }

    public interface OnResultCallback<RESULT> {
        void onResult(RESULT result);
    }
}
