package com.kaolafm.kradio.lib.base.flavor;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-12-02 20:59
 ******************************************/
public interface KRadioClientInter {
    /**
     * 是否可忽略唤起听伴APP
     *
     * @param args
     * @return true为是，false为否
     */
    boolean canIgnoreLaunchApp(Object... args);

    /**
     * 是否允许播放
     * @param args
     * @return
     */
    boolean isAllowPlay(Object... args);
}
