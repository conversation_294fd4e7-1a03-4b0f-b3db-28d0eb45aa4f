<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_layout"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:gravity="center">

    <RelativeLayout
        android:layout_width="@dimen/m605"
        android:layout_height="@dimen/m325"
        android:background="@drawable/online_dialog_bg"
        android:orientation="vertical"
        android:paddingTop="@dimen/m56"
        android:paddingBottom="@dimen/m48">

        <TextView
            android:id="@+id/tv_dialog_bottom_message"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="@color/online_dialog_content"
            android:textSize="@dimen/text_size5"
            tools:text="确定清空收听历史吗?" />

        <LinearLayout
            android:id="@+id/tv_dialog_button_main_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:gravity="center_horizontal"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_dialog_bottom_define"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/online_selector_dialog_costomize_btn_sure"
                android:gravity="center"
                android:minWidth="@dimen/m234"
                android:minHeight="@dimen/m64"
                android:text="@string/ok"
                android:textColor="@color/dialog_common_btn_cancel_text_color"
                android:textSize="@dimen/text_size3" />

            <View
                android:layout_width="@dimen/x48"
                android:layout_height="1dp" />

            <TextView
                android:id="@+id/tv_dialog_bottom_cancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/online_selector_dialog_costomize_btn_cancle"
                android:gravity="center"
                android:minWidth="@dimen/m234"
                android:minHeight="@dimen/m64"
                android:text="@string/cancel"
                android:textColor="@color/dialog_common_btn_sure_text_color"
                android:textSize="@dimen/text_size3" />
        </LinearLayout>
    </RelativeLayout>
</RelativeLayout>