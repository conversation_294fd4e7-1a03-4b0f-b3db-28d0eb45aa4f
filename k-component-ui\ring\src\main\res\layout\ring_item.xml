<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center">

    <ImageView
        android:layout_margin="@dimen/m56"
        android:id="@+id/iv_covert"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:scaleType="centerCrop"
        android:alpha="0.4">
    </ImageView>

    <ImageView
        android:layout_margin="@dimen/m56"
        android:id="@+id/iv_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:scaleType="center">
    </ImageView>

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lt_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true"/>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center">

        <com.kaolafm.kradio.component.ui.base.view.KradioTextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:gravity="center"
            android:textSize="@dimen/text_size9" />

        <TextView
            android:id="@+id/tv_sub"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/tv_title"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/y6"
            android:gravity="center"
            tools:text=""
            android:textSize="@dimen/text_size3" />

        <TextView
            android:id="@+id/tv_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_above="@id/tv_title"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="@dimen/y6"
            android:gravity="center"
            tools:text=""
            android:textColor="@color/color_ring_item_text_type"
            android:textSize="@dimen/text_size3" />

        <ImageView
            android:id="@+id/iv_tag"
            android:layout_width="@dimen/x72"
            android:layout_height="@dimen/y36"
            android:layout_below="@id/tv_sub"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/y19"
            android:gravity="center"
            tools:src="@drawable/icon_vip_home"
            android:scaleType="centerInside" />
    </RelativeLayout>

</FrameLayout>