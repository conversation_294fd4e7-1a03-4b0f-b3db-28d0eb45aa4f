<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:visibility="gone"
    android:id="@+id/live_pending_layout"
    app:layout_constraintTop_toTopOf="parent"
    app:layout_constraintLeft_toLeftOf="parent"
    app:layout_constraintRight_toRightOf="parent"
    android:layout_marginTop="@dimen/y209"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/live_pending_img"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_width="@dimen/m360"
        android:layout_height="@dimen/m142"
        android:src="@drawable/comprehensive_live_status_pending"/>

    <TextView
        android:id="@+id/live_pending_text"
        app:layout_constraintTop_toBottomOf="@id/live_pending_img"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="@dimen/m20"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/m54"
        android:textSize="@dimen/m36"
        android:textColor="#FFEEEEEE"
        android:text="主播准备中，请稍等片刻" />


</androidx.constraintlayout.widget.ConstraintLayout>