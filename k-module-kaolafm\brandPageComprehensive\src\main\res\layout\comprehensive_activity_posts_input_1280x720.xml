<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_black_70_transparent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/contentView"
        android:layout_width="@dimen/m1120"
        android:layout_height="@dimen/m482"
        android:background="@drawable/comprehensive_topic_posts_item_bg"
        android:paddingStart="@dimen/m30"
        android:paddingTop="@dimen/m30"
        android:paddingEnd="@dimen/m30"
        android:paddingBottom="@dimen/m60"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <View
            android:id="@+id/inputBg"
            android:layout_width="match_parent"
            android:layout_height="@dimen/m300"
            android:background="@drawable/comprehensive_topic_posts_input_bg"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <EditText
            android:id="@+id/inputEt"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="@dimen/m20"
            android:layout_marginTop="@dimen/m20"
            android:layout_marginEnd="@dimen/m20"
            android:layout_marginBottom="@dimen/m56"
            android:background="@null"
            android:gravity="start"
            android:hint="@string/comprehensive_topic_publish_posts_input_hint_text"
            android:imeOptions="actionSend|flagNoExtractUi"
            android:inputType="textMultiLine"
            android:lineSpacingExtra="@dimen/m6"
            android:maxLength="1000"
            android:textColor="@color/comprehensive_topic_subtitle_text_color"
            android:textColorHint="@color/comprehensive_topic_publish_posts_input_hint_text_color"
            android:textCursorDrawable="@drawable/comprehensive_topic_posts_input_cursor_drawable"
            android:textSize="@dimen/m26"
            app:layout_constraintBottom_toBottomOf="@id/inputBg"
            app:layout_constraintEnd_toEndOf="@id/inputBg"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="@id/inputBg"
            app:layout_constraintTop_toTopOf="@id/inputBg"
            app:layout_constraintVertical_bias="0.0"
            tools:text="这里是帖子内容！！！这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容这里是帖子内容" />

        <TextView
            android:id="@+id/textLengthTip"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/m36"
            android:layout_marginEnd="@dimen/m20"
            android:layout_marginBottom="@dimen/m10"
            android:gravity="center"
            android:text="0字"
            android:textColor="@color/comprehensive_topic_publish_posts_text_length_tip_text_color"
            android:textSize="@dimen/m24"
            app:layout_constraintBottom_toBottomOf="@id/inputBg"
            app:layout_constraintEnd_toEndOf="@id/inputBg" />

        <ImageView
            android:id="@+id/inputClearBtn"
            android:layout_width="@dimen/m76"
            android:layout_height="@dimen/m56"
            android:paddingStart="@dimen/m20"
            android:paddingTop="@dimen/m10"
            android:paddingEnd="@dimen/m20"
            android:paddingBottom="@dimen/m10"
            android:src="@drawable/comprehensive_topic_posts_input_clear"
            app:layout_constraintBottom_toBottomOf="@id/inputBg"
            app:layout_constraintStart_toStartOf="@id/inputBg" />


        <LinearLayout
            android:id="@+id/voiceInput"
            android:layout_width="@dimen/m240"
            android:layout_height="@dimen/m72"
            android:background="@drawable/comprehensive_topic_posts_voice_input_btn_bg"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <ImageView
                android:layout_width="@dimen/m38"
                android:layout_height="@dimen/m38"
                android:scaleType="centerCrop"
                android:src="@drawable/comprehensive_topic_posts_voice_input_mic" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/m4"
                android:text="点击语音输入"
                android:textColor="@color/comprehensive_topic_primary_color"
                android:textSize="@dimen/m26" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/voiceInputting"
            android:layout_width="@dimen/m240"
            android:layout_height="@dimen/m72"
            android:background="@drawable/comprehensive_topic_posts_voice_input_btn_bg"
            android:gravity="center"
            android:orientation="horizontal"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <com.airbnb.lottie.LottieAnimationView
                android:id="@+id/rateView"
                android:layout_width="@dimen/m38"
                android:layout_height="@dimen/m38"
                app:lottie_autoPlay="false"
                app:lottie_fileName="lottie/rate.json"
                app:lottie_loop="true" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/m4"
                android:text="说完了"
                android:textColor="@color/comprehensive_topic_primary_color"
                android:textSize="@dimen/m26" />
        </LinearLayout>

        <com.kaolafm.kradio.component.ui.base.view.KradioTextView
            android:id="@+id/publishBtn"
            android:layout_width="@dimen/m152"
            android:layout_height="@dimen/m72"
            android:layout_marginStart="@dimen/m4"
            android:background="@drawable/comprehensive_topic_posts_publish_btn_bg"
            android:gravity="center"
            android:text="发布"
            android:textColor="@color/comprehensive_topic_posts_publish_btn_text_color"
            android:textSize="@dimen/m26"
            app:kt_font_weight="0.3"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>