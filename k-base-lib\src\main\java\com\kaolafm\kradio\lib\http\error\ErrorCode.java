package com.kaolafm.kradio.lib.http.error;

/**
 * <AUTHOR> on 2018/6/7.
 */

public class ErrorCode {

    /**
     * 服务器错误
     */
    public static final int HTTP_SERVICE_ERROR = 500;

    public static final int HTTP_SERVICE_UNAVAILABLE = 503;

    public static final int HTTP_SERVICE_BAD_GATEWAY = 502;

    /**
     * 请求地址不存在
     */
    public static final int HTTP_HOST_NOT_EXIST = 404;

    /**
     * 请求被服务器拒绝
     */
    public static final int HTTP_SERVICE_REJECTED = 403;

    /**
     * 请求超时
     */
    public static final int HTTP_REQUEST_TIME_OUT = 408;

    /**
     * 请求被重定向
     */
    public static final int HTTP_REQUEST_REDIRECTED = 307;

    /**
     * 未知错误
     */
    public static final int UNKNOWN_ERROR = 600;

    /**
     * json解析异常
     */
    public static final int JSON_PARSE_ERROR = 601;

    /**
     * 无法解析该域名
     */
    public static final int HTTP_UNKNOWN_HOST = 602;

    /**
     * 网络连接异常
     */
    public static final int HTTP_CONNECT_ERROR = 603;

    /**
     * 网络连接超时
     */
    public static final int HTTP_CONNECT_TIMEOUT = 604;

    /**
     * 数据错误
     */
    public static final int HTTP_RESULT_ERROR = 605;

    /**
     * 数据为空
     */
    public static final int HTTP_RESULT_NULL = 606;

    /**
     * TOKEN 失效
     */
    public static final int ERROR_CODE_TOKEN_INVALID = 40102;


}
