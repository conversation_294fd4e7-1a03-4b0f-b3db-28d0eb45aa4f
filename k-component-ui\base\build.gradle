dependencies {
    implementation fileTree(dir: 'base/libs', include: ['*.jar'])

    //换肤（由于BaseSkinAppCompatActivity需要被player和main同时引用，故将换肤所需依赖放到common项目下）
    api 'skin.support:skin-support:4.0.5'                   // skin-support
    api 'skin.support:skin-support-appcompat:4.0.5'         // skin-support 基础控件支持
    api 'skin.support:skin-support-design:4.0.5'            // skin-support-design material design 控件支持[可选]
    api 'skin.support:skin-support-cardview:4.0.5'          // skin-support-cardview CardView 控件支持[可选]
    api 'skin.support:skin-support-constraint-layout:4.0.5' // skin-support-constraint-layout ConstraintLayout 控件支持[可选]


}
