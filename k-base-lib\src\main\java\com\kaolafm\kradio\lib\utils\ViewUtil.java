package com.kaolafm.kradio.lib.utils;

import android.app.Activity;
import android.content.Context;
import android.content.ContextWrapper;
import android.content.res.Configuration;
import android.graphics.Rect;
import android.view.TouchDelegate;
import android.view.View;
import android.view.View.MeasureSpec;
import android.view.ViewGroup;

import com.kaolafm.kradio.lib.sdk.utils.PerformanceSettingMananger;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-04-18 09:25
 ******************************************/

public final class ViewUtil {

    private ViewUtil() {
    }

    /**
     * 设置当前是否需要隐藏
     */
    public static void setViewVisibility(View view, int visibility) {
        if (view == null) {
            return;
        }
        if (view.getVisibility() == visibility) {
            return;
        }
        view.setVisibility(visibility);
    }

    /**
     * 检测当前View是否可见
     *
     * @param view
     * @return
     */
    public static boolean isViewVisibility(View view) {
        if (view == null) {
            return false;
        }
        return view.getVisibility() == View.VISIBLE;
    }

    /**
     * 设置View是否可用
     */
    public static void setEnabled(View view, boolean isEnable) {
        if (view == null) {
            return;
        }
        if (view.isEnabled() == isEnable) {
            return;
        }
        view.setEnabled(isEnable);
    }

    /**
     * 扩大View的触摸和点击响应范围,最大不超过其父View范围
     */
    public static void expandViewTouchDelegate(final View view, final int top,
                                               final int bottom, final int left, final int right) {
        ((View) view.getParent()).post(() -> {
            Rect bounds = new Rect();
            view.setEnabled(true);
            view.getHitRect(bounds);

            bounds.top -= top;
            bounds.bottom += bottom;
            bounds.left -= left;
            bounds.right += right;

            TouchDelegate touchDelegate = new TouchDelegate(bounds, view);

            if (View.class.isInstance(view.getParent())) {
                ((View) view.getParent()).setTouchDelegate(touchDelegate);
            }
        });
    }

    /**
     * try get host activity from view.
     * views hosted on floating window like dialog and toast will sure return null.
     *
     * @return host activity; or null if not available
     */
    public static Activity getActivityFromView(View view) {
        Context context = view.getContext();
        while (context instanceof ContextWrapper) {
            if (context instanceof Activity) {
                return (Activity) context;
            }
            context = ((ContextWrapper) context).getBaseContext();
        }
        return null;
    }

    /**
     * 向View 中追加padding
     */
    public static void addPaddingForView(View view, int left, int top, int right, int bottom) {
        if (view != null) {
            int tempLeft = view.getPaddingLeft() + left;
            int tempTop = view.getPaddingTop() + top;
            int tempRight = view.getPaddingRight() + right;
            int tempBottom = view.getPaddingBottom() + bottom;
            view.setPadding(tempLeft, tempTop, tempRight, tempBottom);
        }
    }

    public static int[] measureByRatio(View view, double widthRatio, double heightRatio, int widthMeasureSpec, int heightMeasureSpec) {
        int[] specs = new int[2];
        if (widthRatio == 0 || heightRatio == 0) {
            specs[0] = widthMeasureSpec;
            specs[1] = heightMeasureSpec;
            return specs;
        }

        int orientation = ResUtil.getOrientation();
        //根据横竖屏来确定宽高
        //横屏
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            int measuredHeight = view.getMeasuredHeight();
            int width = (int) (measuredHeight * (widthRatio / heightRatio));
            specs[0] = MeasureSpec.makeMeasureSpec(width, MeasureSpec.EXACTLY);
            specs[1] = heightMeasureSpec;
        } else {
            int measuredWidth = view.getMeasuredWidth();
            int height = (int) (measuredWidth * (heightRatio / widthRatio));
            specs[0] = widthMeasureSpec;
            specs[1] = MeasureSpec.makeMeasureSpec(height, MeasureSpec.EXACTLY);
        }
        return specs;
    }

    public static void switchWH(ViewGroup.LayoutParams layoutParams, int orientation) {
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            layoutParams.width = ViewGroup.LayoutParams.WRAP_CONTENT;
            layoutParams.height = ViewGroup.LayoutParams.MATCH_PARENT;
        } else {
            layoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT;
            layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT;
        }
    }

    /**
     * 根据配置显示或隐藏蒙层
     *
     * @param view
     */
    public static void setViewVisibilityAccordingToSetting(View view) {
        PerformanceSettingMananger settingManager = PerformanceSettingMananger.getInstance();
        if (settingManager.isShowConfig() && !settingManager.getHomeIsNeedMongolia()) {
            ViewUtil.setViewVisibility(view, View.GONE);
        } else {
            ViewUtil.setViewVisibility(view, View.VISIBLE);
        }
    }

    public static boolean isInTouchZone(View view, int x, int y) {
        Rect rect = new Rect();
        view.getDrawingRect(rect);
        int[] location = new int[2];
        view.getLocationOnScreen(location);
        rect.left = location[0];
        rect.top = location[1];
        rect.right = rect.right + location[0];
        rect.bottom = rect.bottom + location[1];
        return rect.contains(x, y);
    }

    /**
     * 粗略判定View是否具备做动画的条件
     *
     * @param view
     * @return
     */
    public static boolean canMakeAnimation(View view) {
        if (view == null) {
            return false;
        }
        if (view.getVisibility() != View.VISIBLE) {
            return false;
        }
        return view.getWidth() != 0 && view.getHeight() != 0;
    }
}
