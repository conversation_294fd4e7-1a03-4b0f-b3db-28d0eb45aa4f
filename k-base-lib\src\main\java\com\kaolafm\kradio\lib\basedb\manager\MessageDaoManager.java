package com.kaolafm.kradio.lib.basedb.manager;

import com.kaolafm.kradio.lib.basedb.GreenDaoInterface.OnQueryListener;
import com.kaolafm.kradio.lib.basedb.greendao.CrashMessageBeanDao;
import com.kaolafm.kradio.lib.basedb.greendao.CrashMessageBeanDao.Properties;
import com.kaolafm.kradio.lib.bean.CrashMessageBean;

import org.greenrobot.greendao.query.QueryBuilder;

import java.util.List;

/**
 * 消息数据库管理类
 */

public class MessageDaoManager extends BaseDBManager<CrashMessageBean> {


    private final CrashMessageBeanDao messageBeanDao;

    private static final class MessageDaoManagerHolder {

        private static final MessageDaoManager INSTANCE = new MessageDaoManager();
    }

    public static MessageDaoManager getInstance() {
        return MessageDaoManagerHolder.INSTANCE;
    }

    private MessageDaoManager() {
        super();
        messageBeanDao = mDaoSession.getCrashMessageBeanDao();
    }

    /**
     * 查询某个MsgId的对象是否存在
     */
    public boolean isExitObject(long id) {
        QueryBuilder<CrashMessageBean> qb = messageBeanDao.queryBuilder();
        qb.where(Properties.MsgId.eq(id));
        long length = qb.buildCount().count();
        return length > 0;
    }

    public void updateLook(String msgId) {
        runInNewThread(() -> {
            CrashMessageBean bean = messageBeanDao.queryBuilder().where(Properties.MsgId.eq(msgId))
                    .build().unique();
            bean.setIsLook(true);
            messageBeanDao.update(bean);
            return true;
        }, null);
    }

    /**
     * 根据MsgId删除消息。
     */
    public void delete(String msgId) {
        runInNewThread(() -> {
            CrashMessageBean song = messageBeanDao.queryBuilder().where(Properties.MsgId.eq(msgId)).build().unique();
            if (song != null) {
                messageBeanDao.delete(song);
            }
            return true;
        }, null);
    }

    @Override
    public void save(CrashMessageBean messageBean) {
        runInNewThread(() -> {
            if (messageBean == null) {
                return false;
            }
            //数据库是否已经存在该消息，存在就删除重新存储。
            CrashMessageBean crashMessageBean = messageBeanDao.queryBuilder().where(Properties.MsgId.eq(messageBean.getMsgId()))
                    .build().unique();
            if (crashMessageBean != null) {
                messageBean.setMsgId(crashMessageBean.getMsgId());
                messageBeanDao.update(messageBean);
            } else {
                messageBeanDao.insert(messageBean);
            }
            return true;
        }, null);
    }

    @Override
    public List<CrashMessageBean> queryAllSync() {
        List<CrashMessageBean> songList = messageBeanDao.queryBuilder().orderDesc(Properties.SendTime)
                .build().list();
        return songList;
    }

    /**
     * 查询未读消息数据
     */
    public void queryMsgLook(OnQueryListener<List<CrashMessageBean>> listener) {
        runInNewThread(() -> {
            List<CrashMessageBean> songList = messageBeanDao.queryBuilder().where(Properties.IsLook.eq(false))
                    .build().list();
            return songList;
        }, listener);
    }

    public void setAllMessageReadedSync(){
        List<CrashMessageBean> list = messageBeanDao.queryBuilder().where(Properties.IsLook.eq(false)).build().list();
        if(list == null){
            return;
        }
        for( CrashMessageBean bean: list){
            if(bean != null){
                updateLookSync(bean.getMsgId());
            }
        }
    }

    public List<CrashMessageBean> getAllUnReadedSync(){
        return messageBeanDao.queryBuilder().where(Properties.IsLook.eq(false)).build().list();
    }

    public void updateLookSync(String msgId) {
        CrashMessageBean bean = messageBeanDao.queryBuilder().where(Properties.MsgId.eq(msgId)).build().unique();
        if(bean != null){
            bean.setIsLook(true);
            messageBeanDao.update(bean);
        }
    }

    public void insertSync(CrashMessageBean messageBean) {
        messageBeanDao.insert(messageBean);
    }

    public void deleteSync(String msgId) {
        CrashMessageBean song = messageBeanDao.queryBuilder().where(Properties.MsgId.eq(msgId)).build().unique();
        if (song != null) {
            messageBeanDao.delete(song);
        }
    }

}
