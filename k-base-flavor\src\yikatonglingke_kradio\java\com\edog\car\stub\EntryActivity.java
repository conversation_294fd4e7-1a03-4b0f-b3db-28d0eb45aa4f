package com.edog.car.stub;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import android.util.Log;
import android.widget.Toast;

import com.ecarx.sdk.oauth2.IEventHandler;
import com.ecarx.sdk.oauth2.OAuth2API;
import com.ecarx.sdk.oauth2.base.BaseReq;
import com.ecarx.sdk.oauth2.base.BaseResp;
import com.ecarx.sdk.oauth2.base.Opcode;
import com.ecarx.sdk.oauth2.event.SendAuth;
import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.toast.ToastUtil;

public class EntryActivity extends Activity implements IEventHandler {
    private static final String TAG = EntryActivity.class.getSimpleName();

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        handleIntent();
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);
        handleIntent();
    }

    private void handleIntent() {
        //处理回调
        OAuth2API.getInstance().handleIntent(getIntent(), this);
    }

    @Override
    public void onReq(BaseReq baseReq) {
        Log.i(TAG, "onReq: type = " + baseReq.getType());
    }

    @Override
    public void onResp(BaseResp baseResp) {
        Log.i(TAG, "onResp: type = " + baseResp.getType());
        switch (baseResp.getType()) {
            case Opcode.AUTH:
                authResp(baseResp);
                break;
            default:
                Toast.makeText(this, "TYPE ERROR", Toast.LENGTH_SHORT).show();
                break;
        }
        finish();
    }

    private void authResp(BaseResp baseResp) {
        Log.i(TAG, "onResp: errorCode = " + baseResp.errCode);
        switch (baseResp.errCode) {
            case BaseResp.ErrCode.ERR_OK:
                //至此获取到code 授权成功
//                Log.i(TAG, "onResp: token = " + ((SendAuth.Resp) baseResp).code);
//                ToastUtil.showInfo(getApplicationContext(), "授权成功");
                //至此获取到code 授权成功
                String code = ((SendAuth.Resp) baseResp).code;
                Log.i(TAG, "onResp: code = " + code);
                Toast.makeText(this, R.string.authorized_success_str, Toast.LENGTH_SHORT).show();
                //
                Intent intent = new Intent("com.ecarx.action.GetCodeSuccess");
                intent.putExtra("code", code);
                LocalBroadcastManager.getInstance(getApplicationContext()).sendBroadcast(intent);
                break;
            case BaseResp.ErrCode.ERR_USER_CANCEL:
                ToastUtil.showInfo(getApplicationContext(), R.string.authorized_canceled_str);
                LocalBroadcastManager.getInstance(getApplicationContext()).sendBroadcast(new Intent("com.ecarx.action.GetCodeFailure"));
                break;
            case BaseResp.ErrCode.ERR_COMM:
            case BaseResp.ErrCode.ERR_SENT_FAILED:
            case BaseResp.ErrCode.ERR_AUTH_DENIED:
            case BaseResp.ErrCode.ERR_NOT_SUPPORT:
            case BaseResp.ErrCode.ERR_BAN:
                ToastUtil.showInfo(getApplicationContext(), R.string.authorized_failed_str);
                LocalBroadcastManager.getInstance(getApplicationContext()).sendBroadcast(new Intent("com.ecarx.action.GetCodeFailure"));
                break;
            default:
                break;
        }
        finish();
    }
}