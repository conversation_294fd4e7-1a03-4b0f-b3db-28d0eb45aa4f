package com.kaolafm.notify;

import android.app.Activity;
import android.content.Context;
import android.os.Handler;
import android.os.Message;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.base.flavor.KRadioTransStatusBarInter;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;

import java.lang.ref.WeakReference;

/**
 * <AUTHOR>
 **/
public class NotifyWindow {

    private static final String TAG = "k.msg.ui";

    private static final int WHAT_HIDE = 0x111;
    private static final long DELAY_TIME = 12000;
    private static final long DELAY_TIME_AFTER_CLICK = 5000;


    private WeakReference<Context> refContext;

    private View mView;
    private ImageView ivIcon;
    private TextView tvMessage;
    private View vCancle;
    private View vSure;

    private String iconUrl;
    private String msg;
    private NotifyCallback mListener;

    private final Handler mHandler = new MyHandler(NotifyWindow.this);
    private static class  MyHandler extends Handler
    {
        private final WeakReference<NotifyWindow> weakNotify;
        private MyHandler(NotifyWindow notifyWindow)
        {
            weakNotify = new WeakReference<>(notifyWindow);
        }
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            if (msg.what == WHAT_HIDE) {
                NotifyWindow owner = weakNotify.get();
                if (owner == null) {
                    return;
                }
                owner.hide();
                if (owner.mListener != null) {
                    owner.mListener.onHide(owner);
                }
            }
        }
    };

    public NotifyWindow(Context context) {
        refContext = new WeakReference<Context>(context);
        LayoutInflater layoutInflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);

        mView = layoutInflater.inflate(R.layout.notify_window, null);
        ivIcon = mView.findViewById(R.id.flow_iv1);
        tvMessage = mView.findViewById(R.id.flow_text);
        vCancle = mView.findViewById(R.id.btnCancle);
        vSure = mView.findViewById(R.id.btnSure);

        View placeholder = mView.findViewById(R.id.placeholder);
        boolean isWindowFullScreen = ResUtil.getBoolean(R.bool.isWindowFullScreen);
        if (isWindowFullScreen) {
            ViewUtil.setViewVisibility(placeholder, View.GONE);
        } else {
            KRadioTransStatusBarInter kRadioTransStatusBarInter = ClazzImplUtil.getInter("KRadioTransStatusBarImpl");
            // 解决http://redmine.itings.cn/issues/40839问题
            if (kRadioTransStatusBarInter != null) {
                kRadioTransStatusBarInter.changeViewLayoutForStatusBar(mView, 0);
            }
            ViewUtil.setViewVisibility(placeholder, View.VISIBLE);
        }

        mView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //6、当用户点击直播条信息，不做响应；
                // Do-Nothing
            }
        });

        vSure.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //5、当用户点击确定时，跳转进入直播间播放器；
                //消失逻辑在外部调用
                mHandler.removeMessages(WHAT_HIDE);
                mHandler.sendEmptyMessageDelayed(WHAT_HIDE, DELAY_TIME_AFTER_CLICK);
                if (mListener != null) {
                    mListener.onSure(NotifyWindow.this);
                }
            }
        });

        vCancle.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //4、当用户点击取消时，中断语音播报，悬浮窗提示消失；
                //消失逻辑在外部调用
                mHandler.removeMessages(WHAT_HIDE);
                mHandler.sendEmptyMessageDelayed(WHAT_HIDE, DELAY_TIME_AFTER_CLICK);
                if (mListener != null) {
                    mListener.onCancel(NotifyWindow.this);
                }
            }
        });
    }


    public NotifyWindow setIcon(String iconUrl) {
        this.iconUrl = iconUrl;
        return this;
    }

    public NotifyWindow setMessage(String msg) {
        this.msg = msg;
        return this;
    }

    public NotifyWindow setListener(NotifyCallback listener) {
        this.mListener = listener;
        return this;
    }

    public void show() {
        if (refContext.get() != null) {
            mHandler.removeMessages(WHAT_HIDE);

            ImageLoader.getInstance().displayImage(refContext.get(), iconUrl, ivIcon);
            tvMessage.setText(msg);

            Activity currentActivity = AppManager.getInstance().getMainActivity();
            if (currentActivity != null) {
                ToastUtil.showLive(currentActivity, mView);
            }

            //需求改为:不再等待语音播放完成之后的5秒,而是语音播放开始后的12秒
            mHandler.sendEmptyMessageDelayed(WHAT_HIDE, DELAY_TIME);
            if (mListener != null) {
                mListener.onShow(NotifyWindow.this);
            }
        }
    }

    public void hide() {
        mHandler.removeMessages(WHAT_HIDE);
        ToastUtil.dismissNotify();
        Nolley.getInstance().unlock();
    }


    /**
     * @param delay 毫秒
     */
    public void hide(int delay) {
        mHandler.removeMessages(WHAT_HIDE);
        mHandler.sendEmptyMessageDelayed(WHAT_HIDE, delay);
    }


}
