package cmgyunting.vehicleplayer.cnr.widget;

import android.appwidget.AppWidgetManager;
import android.appwidget.AppWidgetProvider;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;

import com.kaolafm.auto.appwidget.KLAppWidget;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.opensdk.player.logic.PlayerManager;

import cmgyunting.vehicleplayer.cnr.YunTingWidgetService;

public class YunBydDophinWidget extends AppWidgetProvider {

    private static final String TAG = "YunBydDophinWidget";

    private PlayerManager playerManager = PlayerManager.getInstance();

    @Override
    public void onReceive(Context context, Intent intent) {
        Log.i(TAG, "onReceive----->" + intent.getAction());
        if (Constants.APPEXIT_ACTION.equals(intent.getAction())) {
            AppWidgetManager appWidgetManager = AppWidgetManager.getInstance(context);
            ComponentName componentName = new ComponentName(context, KLAppWidget.class);
            for (int appWidgetId : appWidgetManager.getAppWidgetIds(componentName)) {
                updateAppWidget(context, appWidgetManager, appWidgetId, Constants.APPEXIT_ACTION);
            }
        } else {
            super.onReceive(context, intent);
        }
    }

    @Override
    public void onUpdate(Context context, AppWidgetManager appWidgetManager, int[] appWidgetIds) {
        Log.i(TAG, "onUpdate ----- appwidgetId.size appWidgetIds size = " + appWidgetIds.length + "-----appWidgetIds = " + appWidgetIds);
        for (int appWidgetId : appWidgetIds) {
            updateAppWidget(context, appWidgetManager, appWidgetId, "android.appwidget.action.APPWIDGET_UPDATE");
        }
    }

    @Override
    public void onEnabled(Context context) {
        // TODO: 4/5/21 参考widgetDemo修改现有Service启动逻辑，解决service启动的各种异常
        Log.i(TAG, "onEnable ----- ");
        Intent intent = new Intent(context, YunTingDophoinWidgetService.class);
        intent.setAction(YunTingDophoinWidgetService.WIDGET_ACTION_REFRESH);
        intent.putExtra("widget_policy", YunTingDophoinWidgetService.WIDET_POLICY_DOPHIN);
//        context.startService(intent);
        startServiceCompat(context, intent);
    }


    @Override
    public void onDisabled(Context context) {
        Log.i(TAG, "onDisabled ----- ");
        Intent intent = new Intent(context, YunTingDophoinWidgetService.class);
        intent.setAction(YunTingDophoinWidgetService.WIDGET_ACTION_REFRESH);
//        context.startService(intent);
        startServiceCompat(context, intent);
    }

    @Override
    public void onDeleted(Context context, int[] appWidgetIds) {
        super.onDeleted(context, appWidgetIds);
    }

    @Override
    public void onRestored(Context context, int[] oldWidgetIds, int[] newWidgetIds) {
        super.onRestored(context, oldWidgetIds, newWidgetIds);
    }


    @Override
    public void onAppWidgetOptionsChanged(Context context, AppWidgetManager appWidgetManager, int appWidgetId, Bundle newOptions) {
        super.onAppWidgetOptionsChanged(context, appWidgetManager, appWidgetId, newOptions);
    }

    void updateAppWidget(final Context pContext, AppWidgetManager appWidgetManager, final int appWidgetId, String action) {
        Log.i(TAG, "updateAppWidget ----- appWidgetId:" + appWidgetId);
        Intent intent = new Intent(pContext, YunTingDophoinWidgetService.class);
        intent.setAction(YunTingDophoinWidgetService.WIDGET_ACTION_REFRESH);
        intent.putExtra("widget_policy", YunTingDophoinWidgetService.WIDET_POLICY_DOPHIN);
//        pContext.startService(intent);
        startServiceCompat(pContext, intent);
    }

    private void startServiceCompat(Context context, Intent intent) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.startForegroundService(intent);
        } else {
            context.startService(intent);
        }
    }

}
