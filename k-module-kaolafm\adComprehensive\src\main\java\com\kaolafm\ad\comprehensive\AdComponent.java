package com.kaolafm.ad.comprehensive;

import com.kaolafm.ad.comprehensive.processor.AdControlProcessor;
import com.kaolafm.ad.comprehensive.processor.AdExposeProcess;
import com.kaolafm.kradio.component.BaseComponent;

public class AdComponent extends BaseComponent {
    @Override
    protected void initProcessors() {
        addProcessor(new AdControlProcessor());
        addProcessor(new AdExposeProcess());
    }
}
