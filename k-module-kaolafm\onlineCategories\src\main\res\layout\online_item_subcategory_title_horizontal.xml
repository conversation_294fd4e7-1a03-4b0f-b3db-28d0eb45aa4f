<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="@dimen/x60"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/online_category_item_title_bg"
    android:padding="@dimen/m15">

    <com.kaolafm.kradio.component.ui.base.view.KradioTextView
        android:id="@+id/item_subcategory_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:clickable="false"
        android:gravity="center"
        android:textColor="@color/online_category_item_title_text_color"
        android:textSize="@dimen/online_all_ctg_item_content_size"
        tool:text="热门" />
</FrameLayout>