package com.kaolafm.kradio.flavor;

import android.os.Bundle;
import android.os.RemoteException;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.cns.android.hmi.HMIManager;
import com.kaolafm.kradio.lib.base.AppManager;

public class OpenPrivacyActivity extends AppCompatActivity {
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //  跳转至账号主页页面:
        try {
            HMIManager.getInstance(this).switchToLinux(HMIManager.ENTRY_POINT_USER_MANAGEMENT_MAIN);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        AppManager.getInstance().appExit();

    }
}
