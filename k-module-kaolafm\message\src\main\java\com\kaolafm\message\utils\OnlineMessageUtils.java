package com.kaolafm.message.utils;

public class OnlineMessageUtils {
    private boolean isShowMsgDetails;//当前是详情点击的播放
    private static OnlineMessageUtils onlineMessageUtils;

    public static OnlineMessageUtils getInstance() {
        if (onlineMessageUtils == null) {
            synchronized (OnlineMessageUtils.class) {
                if (onlineMessageUtils == null) {
                    onlineMessageUtils = new OnlineMessageUtils();
                }
            }
        }
        return onlineMessageUtils;
    }

    public boolean getShowMsgDetails() {
        return isShowMsgDetails;
    }

    public void setShowMsgDetails(boolean isShowMsgDetails) {
        this.isShowMsgDetails = isShowMsgDetails;
    }
}
