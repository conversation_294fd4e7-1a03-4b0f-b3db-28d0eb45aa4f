<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <gradient android:angle="270"
                android:endColor="#ff2e3a4d"
                android:startColor="#ff4d5e99"
                android:type="linear"
                />
            <corners android:radius="@dimen/default_radius_img" />
        </shape>
    </item>

    <item>
        <shape android:shape="rectangle">
            <gradient android:angle="270"
                android:endColor="#ff2e3a4d"
                android:startColor="#ff4d5e99"
                android:type="linear"
                 />
            <corners android:radius="@dimen/default_radius_img" />
        </shape>
    </item>

</selector>