package com.kaolafm.kradio.component;

/**
 * 阻塞线程，等待结果返回。
 * <AUTHOR>
 * @date 2019-07-05
 */
public class WaitForResultInterceptor implements ComponentInterceptor {

    private static final class WaitForResultInterceptorHolder {
        private static final WaitForResultInterceptor INSTANCE = new WaitForResultInterceptor();
    }

    public static WaitForResultInterceptor getInstance() {
        return WaitForResultInterceptorHolder.INSTANCE;
    }

    @Override
    public ComponentResult intercept(ComponentChain chain) {
        RealCaller caller = chain.caller();
        caller.waitForResult();
        return caller.result();
    }
}
