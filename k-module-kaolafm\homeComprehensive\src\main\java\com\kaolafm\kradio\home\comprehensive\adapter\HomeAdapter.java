package com.kaolafm.kradio.home.comprehensive.adapter;

import android.graphics.Bitmap;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.kaolafm.kradio.common.utils.BitmapUtils;
import com.kaolafm.kradio.component.ui.activitycard.ComponentActivityCell;
import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.common.ResType;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.bean.BroadcastRadioSimpleData;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.api.operation.model.ImageFile;
import com.kaolafm.opensdk.api.operation.model.column.ColumnContent;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.LivePlayItem;
import com.kaolafm.opensdk.player.logic.model.item.VideoAlbumPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Yan
 * @date 2019-08-14
 */
public class HomeAdapter extends GroupBasicAdapter<View, HomeCell> {

    private static final String TAG = HomeAdapter.class.getSimpleName();
    private boolean isShowBitmap = false;//是否显示item倒影，荣国显示需要在布局添加对应id控件

    public HomeAdapter() {
        setHasStableIds(true);
    }

    public HomeAdapter(boolean isShowBitmap) {
        this.isShowBitmap = isShowBitmap;
        setHasStableIds(true);
    }

    @Override
    protected BaseHolder<HomeCell> getViewHolder(ViewGroup parent, int layoutId) {
        BaseHolder<HomeCell> holder = super.getViewHolder(parent, layoutId);
        ImageView imageView = holder.itemView.findViewById(R.id.bitmap_iv);
        if (isShowBitmap) {
            if (imageView != null) {
                //这里需要延迟处理，非则item的截图会是空图片
                imageView.setVisibility(View.VISIBLE);
                imageView.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        Bitmap reflectBitmap = BitmapUtils
                                .createReflectionBitmapBrand(BitmapUtils
                                        .createBitmapFromViewNotBg(holder.itemView.findViewById(R.id.root_layout), 1F));
                        imageView.setImageBitmap(reflectBitmap);
                    }
                }, 200);
            }
        } else {
            if (imageView != null) {
                imageView.setVisibility(View.GONE);
            }
        }
        return holder;
    }
    public void updateItem(String id, String img,String title,String desc){
        A:for (int i = 0; i < mDataList.size(); i++) {
            HomeCell bean = mDataList.get(i);
            for (int i1 = 0; i1 < bean.contentList.size(); i1++) {
                ColumnContent cc = bean.contentList.get(i1);
                if(id.equals(cc.getId())){
                    Map<String, ImageFile> map = new HashMap<>();
                    ImageFile vv = new ImageFile();
                    vv.setUrl(img);
                    map.put("cover", vv);
                    cc.setImageFiles(map);
                    cc.setProgramTitle(title);
                    cc.setProgramDesc(desc);
                    notifyItemChanged(i);
                    break A;
                }
            }
        }
    }
    @Override
    public void onBindViewHolder(BaseHolder<HomeCell> holder, int position) {
        super.onBindViewHolder(holder, position);
        HomeCell itemData = getItemData(position);
        if (itemData instanceof ComponentActivityCell) {
            ((ComponentActivityCell) itemData).exposure();
        }
    }

    public void changePlayingState() {
        PlayItem playItem = PlayerManagerHelper.getInstance().getCurPlayItem();
        String playingId = Constants.BLANK_STR;
        int radioType = -1;

        if (playItem != null) {
            playingId = playItem.getRadioId();
            radioType = PlayerManagerHelper.getInstance().getCurPlayItem().getType();
            //如果播放的pgc电台再判断是不是一键收听，一键收听播放的都是专辑类型
            if (radioType == PlayerConstants.RESOURCES_TYPE_ONE_KEY_LISTENER_TYPE) {
                radioType = ResType.ALBUM_TYPE;
                playingId = playItem.getAlbumId();
            }
            if (radioType == PlayerConstants.RESOURCES_TYPE_LIVE_STREAM) {
                radioType = ResType.RADIO_TYPE;
            }
            if (radioType == PlayerConstants.RESOURCES_TYPE_LIVING) {
                radioType = ResType.LIVE_TYPE;
                playingId = ((LivePlayItem) playItem).getLiveId() + "";
            }
            if (radioType == PlayerConstants.RESOURCES_TYPE_ALBUM) {
                // AlbumPlayListControl#getAudioInfo#mAudioRequest.getAudioDetails
                // PlayerConstants.RESOURCES_TYPE_ALBUM -> AlbumPlayItem -> isFromAudio
                // item.getContentList().get(j).getId() -> 1000026437837
                // playItem.getAudioId() -> audioId -> 1000026437837
                // playItem.getRadioId() -> mInfoData.getAlbumId() -> 1100002157353
            }
            if (radioType == PlayerConstants.RESOURCES_TYPE_VIDEO_ALBUM) {
                // VideoAlbumPlayListControl#getAudioInfo#mVideoAudioRequest.getAudioDetails
                // PlayerConstants.RESOURCES_TYPE_VIDEO_ALBUM -> VideoAlbumPlayItem -> isFromAudio
            }
        }

        for (int i = 0, size = getItemCount(); i < size; i++) {
            HomeCell item = mDataList.get(i);
            if (item.selected) {
                item.selected = false;
                notifyItemChanged(i);
            }
            if(item==null|| item.getContentList()==null){
                return;
            }
            for (int j = 0; j < item.getContentList().size(); j++) {
                String itemCode = item.getCode();
                String itemId = String.valueOf(item.getContentList().get(j).getId());
//                Log.i(TAG, "changePlayingState() -> for(j) -> itemCode=" + itemCode + ", itemId=" + itemId + ", playingId=" + playingId);
                String curPlayId = playingId;
                if (radioType == PlayerConstants.RESOURCES_TYPE_VIDEO_ALBUM){
                    if (item.getContentList().get(j).getResType() == ResType.VIDEO_TYPE){
                        curPlayId = String.valueOf(playItem.getAudioId());
                    } else {
                        curPlayId = playItem.getAlbumId();
                    }
                }
                if (TextUtils.equals(itemId, curPlayId)) {
                    item.selected = true;
                    notifyItemChanged(i);
                }
            }

        }
    }

    public void changeBroadcastPlayingState() {
        int position = PlayerManagerHelper.getInstance().getCurrentFrequencyPosition();
        BroadcastRadioSimpleData broadcastRadioSimpleData = PlayerManagerHelper.getInstance().getBroadcastRadioSimpleDataByIndex(position);
        if (broadcastRadioSimpleData == null) {
            return;
        }
        for (int i = 0, size = getItemCount(); i < size; i++) {
            HomeCell item = mDataList.get(i);
            if (item.selected) {
                item.selected = false;
                notifyItemChanged(i);
            }

            if (broadcastRadioSimpleData.getBroadcastId() == item.playId) {
                item.selected = true;
                notifyItemChanged(i);
            }
        }
    }


    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public void onViewRecycled(BaseHolder<HomeCell> holder) {
        super.onViewRecycled(holder);
        // CPU优化：安全地回收ViewHolder资源
        // 由于BaseHolder没有getData方法，我们通过position获取数据
        int position = holder.getAdapterPosition();
        if (mDataList != null && position >= 0 && position < mDataList.size()) {
            HomeCell homeCell = mDataList.get(position);
            if (homeCell != null) {
                Log.i(TAG, "回收ViewHolder资源，位置: " + position);
                homeCell.release();
            }
        } else {
            // 如果position无效，说明ViewHolder已经被移除，直接调用release
            Log.i(TAG, "ViewHolder已被移除，执行通用资源清理");
        }
    }
}
