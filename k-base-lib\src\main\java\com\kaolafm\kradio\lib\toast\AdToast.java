package com.kaolafm.kradio.lib.toast;

import android.content.Context;

public class AdToast extends SuperToast {
    private ToastHandler mNotifyHandler;

    public AdToast(Context context) {
        super(context);
        mNotifyHandler = ToastHandler.createInstance();
    }

    @Override
    public AdToast show() {
        mNotifyHandler.add(this);
        sendAccessibilityEvent();
        return this;
    }

    /**
     * 手动让toast的消失。
     */
    @Override
    public void dismiss() {
        mNotifyHandler.removeToast(this);
    }

    /**
     * 取消正在显示的toast并清空消息队列。
     */
    @Override
    public void cancelAllToasts() {
        mNotifyHandler.cancelAllToasts();
    }
}
