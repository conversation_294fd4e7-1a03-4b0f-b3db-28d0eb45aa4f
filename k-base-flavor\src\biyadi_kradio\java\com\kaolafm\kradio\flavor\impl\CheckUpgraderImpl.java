package com.kaolafm.kradio.flavor.impl;

import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.hardware.bydauto.bodywork.BYDAutoBodyworkDevice;
import android.util.Log;

import com.google.gson.Gson;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.ICheckUpgraderInter;
import com.kaolafm.kradio.upgrader.net.RequestConstants;
import com.kaolafm.kradio.upgrader.net.RequestHelper;
import com.kaolafm.kradio.upgrader.net.model.QueryRequestData;
import com.kaolafm.kradio.upgrader.net.sha.apache.org.Sha256Hash;

import java.util.HashMap;
import java.util.Map;

public class CheckUpgraderImpl implements ICheckUpgraderInter {
    private static final String TAG = "CheckUpgraderImpl";
    private boolean isInit = false;
    private String modelName;
    private String autoVin;
    private QueryRequestData mQueryRequestData;
    private String body;
    private String sign;
    private static boolean isFirstTime = true;

    @Override
    public boolean checkUpgrade(boolean isAccord) {
//        Log.i(TAG, "checkUpgrade : isAccord = " + isAccord + ",isFirstTime = " + isFirstTime);
        //只有不是第一次 并且 不是主动刷新 进行返回
//        if (!isFirstTime && !isAccord) {
//            return true;
//        }

        if (!isInit) {
            isInit = init();
        }
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("modelName", modelName);
        headers.put("vin", autoVin);
        headers.put("sign", sign);

        Log.i(TAG, "checkUpgrade :       body = " + body);
        Log.i(TAG, "checkUpgrade :       head        modelName = " + modelName + "       vin = " + autoVin + "       sign = " + sign);
        if (!RequestHelper.isWork) {
            RequestHelper.getInstance().addQueryVersionRequest(headers, mQueryRequestData, isAccord);
        }
        return true;
    }

    @Override
    public String getVersionName() {
        return null;
    }


    private boolean init() {

        try {
            isFirstTime = false;
            RequestHelper.getInstance().init();
            PackageManager packageManager = AppDelegate.getInstance().getContext().getPackageManager();
            PackageInfo packageInfo = packageManager.getPackageInfo(AppDelegate.getInstance().getContext().getPackageName(), 0);
            modelName = String.valueOf(BYDAutoBodyworkDevice.getInstance(AppDelegate.getInstance().getContext()).getAutoModelName()).trim();
            autoVin = BYDAutoBodyworkDevice.getInstance(AppDelegate.getInstance().getContext()).getAutoVIN().trim();
            mQueryRequestData = new QueryRequestData();
            mQueryRequestData.setPackageName(packageInfo.packageName);
            mQueryRequestData.setVersionCode(packageInfo.versionCode);
            body = new Gson().toJson(mQueryRequestData);
            sign = new Sha256Hash(RequestConstants.SignHeader + body.toString() + RequestConstants.SignEnder, RequestConstants.salt).toString();
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }
}
