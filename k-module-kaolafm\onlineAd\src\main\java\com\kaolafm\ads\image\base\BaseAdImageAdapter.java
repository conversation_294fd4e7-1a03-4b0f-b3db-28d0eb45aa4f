package com.kaolafm.ads.image.base;

import android.content.Context;


public abstract class BaseAdImageAdapter<T> {
    private BaseAdContentView mBaseAdContentView;

    public abstract BaseAdContentView<T> onCreateAdView(Context context);
    public abstract void onBindAdView(BaseAdContentView<T> baseAdContentView, T t);

    public void init(Context context){
        mBaseAdContentView = createAdView(context);
    }

    public BaseAdContentView getBaseAdContentView() {
        return mBaseAdContentView;
    }

    public final BaseAdContentView<T> createAdView(Context context){
        final BaseAdContentView baseAdContentView = onCreateAdView(context);
        return baseAdContentView;
    }

    public final void loadAd(T t, AdImageListener adImageListener){
        mBaseAdContentView.setAdImageListener(adImageListener);
        onBindAdView(mBaseAdContentView, t);
    }
}
