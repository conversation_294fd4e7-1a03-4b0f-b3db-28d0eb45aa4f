package com.kaolafm.kradio.common.view;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.util.AttributeSet;
import android.view.MotionEvent;

public class HorizontalRecyclerview extends RecyclerView {

    public HorizontalRecyclerview(@NonNull Context context) {
        super(context);
    }

    public HorizontalRecyclerview(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    private int startX, startY;

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                startX = (int) ev.getX();
                startY = (int) ev.getY();
                getParent().requestDisallowInterceptTouchEvent(true);//告诉viewgroup不要去拦截我
                break;
            case MotionEvent.ACTION_MOVE:
                int endX = (int) ev.getX();
                int endY = (int) ev.getY();
                int disX = Math.abs(endX - startX);
                int disY = Math.abs(endY - startY);

                LinearLayoutManager manager = (LinearLayoutManager) getLayoutManager();
                int lastItemPosition = manager.findLastCompletelyVisibleItemPosition();//最后一个可见
                int firstItemPosition = manager.findFirstCompletelyVisibleItemPosition();//第一个可见
                int itemCount = manager.getItemCount();//总item数

                //横向滑动
                if (disX > disY) {
                    //向左endX减少,向右endX增加
                    if (lastItemPosition == (itemCount - 1) && startX > endX) {
                        //判断是否滑动到了最后一个Item，并且是向左滑动
                        getParent().requestDisallowInterceptTouchEvent(false);//交给viewgroup
                    } else if (firstItemPosition == 0 && startX < endX) {
                        //判断是否滑动到了第一个Item，并且是向右滑动
                        getParent().requestDisallowInterceptTouchEvent(false);//交给viewgroup
                    } else {
                        getParent().requestDisallowInterceptTouchEvent(true);//recyclerview滑动
                    }
                } else {
                    //竖向滑动
                    getParent().requestDisallowInterceptTouchEvent(false);//交给viewgroup
                }
                break;
        }
        return super.dispatchTouchEvent(ev);
    }

}