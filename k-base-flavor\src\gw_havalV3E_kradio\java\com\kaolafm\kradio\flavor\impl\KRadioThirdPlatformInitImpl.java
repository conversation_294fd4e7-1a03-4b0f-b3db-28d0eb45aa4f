package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.util.Log;

import com.kaolafm.kradio.flavor.customer.utils.TbAptivSrcManager;
import com.kaolafm.kradio.lib.base.flavor.KRadioThirdPlatformInitInter;
import com.kaolafm.opensdk.player.logic.PlayerManager;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-07-03 17:14
 ******************************************/
public class KRadioThirdPlatformInitImpl implements KRadioThirdPlatformInitInter {

    public static final String TAG = "KRadioThirdPlatformInit";

    @Override
    public boolean initThirdPlatform(Object... args) {
        Log.i(TAG, "initThirdPlatform");
        try {
            TbAptivSrcManager.getInstance().init((Context) args[0]);
        } catch (Throwable t) {
            t.printStackTrace();
        }
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001948485727?userId=1397929问题
//        PlayerManager.getInstance().setupPlayer();

        return true;
    }

    @Override
    public boolean destroyThirdPlatform(Object... args) {
        TbAptivSrcManager.getInstance().destroy();
        return true;
    }
}
