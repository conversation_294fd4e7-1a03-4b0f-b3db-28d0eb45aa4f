package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.content.Intent;

import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.base.flavor.KRadioActivityLifecyleInter;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-10-26 17:36
 ******************************************/
public class KRadioActivityLifecyleImpl implements KRadioActivityLifecyleInter {
    @Override
    public boolean onRestart(Object... args) {
        return false;
    }

    @Override
    public boolean onResume(Object... args) {
        Context context = (Context) args[0];
        String title = context.getString(R.string.status_bar_title_str);

        Intent intent = new Intent("status_bar_recv_app");
        intent.putExtra("key_type", 1);
        intent.putExtra("value", title);
        context.getApplicationContext().sendBroadcast(intent);
        return true;
    }

    @Override
    public boolean onPause(Object... args) {
        return false;
    }

    @Override
    public boolean onStop(Object... args) {
        return false;
    }
}
