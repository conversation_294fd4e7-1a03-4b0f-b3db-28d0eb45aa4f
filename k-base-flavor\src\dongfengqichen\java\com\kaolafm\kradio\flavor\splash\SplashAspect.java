package com.kaolafm.kradio.flavor.splash;

import android.util.Log;

import com.hsae.autosdk.util.LogUtil;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;

@Aspect
public class SplashAspect {


    @Around("execution(* com.kaolafm.auto.home.HubActivity.getDelayTime(..))")
    public Object setSplashTime(ProceedingJoinPoint joinPoint) throws Throwable {
//        Log.i("xxxxx", joinPoint.getTarget() + ":::" + joinPoint.getThis());
        return 100;
    }

    @Around("execution(* com.kaolafm.auto.home.HubActivity.setTheme(..))")
    public Object setSplashTheme(ProceedingJoinPoint joinPoint) throws Throwable {
//        Log.i("xxxxx", joinPoint.getTarget() + ":::" + joinPoint.getThis());
        return true;
    }

}
