package com.kaolafm.kradio.lib.basedb.manager;


import com.kaolafm.kradio.lib.basedb.GreenDaoInterface;
import com.kaolafm.kradio.lib.bean.HistoryItem;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.query.WhereCondition;

import java.util.List;

/**
 * 历史本地数据库管理类基类。与历史相关的相同操作的基类。
 * 目前是登录和未登录保存在两个表中，但是操作大部分都是一样的。
 * <AUTHOR>
 * @date 2020/9/4
 */
public abstract class BaseHistoryDaoManager<T, D extends AbstractDao<T, ?>> extends BaseDBManager<T> implements Manager {

    protected static final int MAX_HISTORY_NUM = 100;

    protected D mDao;

    private GreenDaoInterface.OnQueryListener<Boolean> mListener;

    protected BaseHistoryDaoManager() {
        mDao = getDao();
    }

    protected List<HistoryItem> getBroadcastList(WhereCondition condMore) {
        List<T> list = mDao.queryBuilder()
                .where(condMore)
                .orderDesc(getTimestamp())
                .limit(2).build().list();
        return toHistoryItemList(list);
    }

    protected List<HistoryItem> queryHistoryListOrderByTime(int count) {
        List<T> list = mDao.queryBuilder().orderDesc(getTimestamp())
                .limit(count)
                .build().list();
        return toHistoryItemList(list);
    }

    protected void queryLatestHistoryByAlbumId(WhereCondition cond, GreenDaoInterface.OnQueryListener<HistoryItem> listener) {
        runInNewThread(() -> {
            T t = mDao.queryBuilder().orderDesc(getTimestamp())
                    .where(cond)
                    .limit(1)
                    .build().unique();
            return toHistoryItem(t);
        }, listener);
    }

    /**
     * 保存历史
     *
     * @param isUpdatePlayPosition 是否只是更新播放位置, 如果是 就不刷新UI
     *                             true 表示不更新UI
     *                             false 表示不更新UI。
     */
    @Override
    public void saveHistory(PlayItem playItem, boolean isUpdatePlayPosition) {
        if (cannotSave(playItem)) {
            return;
        }
        runInNewThread(() -> {
            T historyItem = toHistoryEntity(playItem);
            mDaoSession.insertOrReplace(historyItem);
            return true;
        }, isUpdatePlayPosition ? null : mListener);
    }

    @Override
    public HistoryItem getLasted() {
        T t = mDao.queryBuilder().orderDesc(getTimestamp()).limit(1).unique();
        return toHistoryItem(t);
    }

    @Override
    public void saveHistoryWhenDestroy(PlayItem playItem) {
        if (cannotSave(playItem)) {
            return;
        }
        T historyItem = toHistoryEntity(playItem);
        insertSynch(historyItem);
    }

    @Override
    public void clear(HttpCallback<Boolean> callback) {
        runInNewThread(() -> {
            mDao.deleteAll();
            return true;
        }, success -> {
            if (callback != null) {
                if (success) {
                    callback.onSuccess(true);
                }else {
                    callback.onError(new ApiException("清空历史出错了"));
                }
            }
        });
    }

    abstract HistoryItem toHistoryItem(T t);

    abstract List<HistoryItem> toHistoryItemList(List<T> list);

    abstract T toHistoryEntity(PlayItem playItem);

    protected abstract D getDao();

    abstract Property getTimestamp();

    @Override
    public void destroy() {
        closeDataBase();
    }

    private boolean cannotSave(PlayItem playItem) {
        if (playItem == null) {
            return true;
        }
        int type = playItem.getType();

        if (type == PlayerConstants.RESOURCES_TYPE_ONE_KEY_LISTENER_TYPE) {
            return true;
        }
        if (type == PlayerConstants.RESOURCES_TYPE_PURCHASE_ONE_KEY_LISTENER_TYPE) {
            return true;
        }
        if (type == PlayerConstants.RESOURCES_TYPE_LIVING) {
            return true;
        }
        if (type == PlayerConstants.RESOURCES_TYPE_LIVE_STREAM) {
            return true;
        }
        if (type == PlayerConstants.RESOURCES_TYPE_TEMP_TASK) {
            return true;
        }
        if (type == PlayerConstants.RESOURCES_TYPE_INVALID) {
            return true;
        }
        return false;
    }

    @Override
    public void setListener(GreenDaoInterface.OnQueryListener<Boolean> listener) {
        mListener = listener;
    }
}
