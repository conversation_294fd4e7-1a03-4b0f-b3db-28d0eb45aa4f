package com.kaolafm.kradio.online.home.mvp;


import android.content.Context;
import android.util.Log;

import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.common.KradioSDKManager;
import com.kaolafm.kradio.common.http.api.volume.VolumeRequest;
import com.kaolafm.kradio.common.report.ReportUtil;
import com.kaolafm.kradio.home.HomeDataManager;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioPlayLogicInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioSystemSourceChangeInter;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.media.MediaSessionUtil;
import com.kaolafm.opensdk.api.operation.model.column.ColumnGrp;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.IPlayerInteractionFiredListener;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;
import com.kaolafm.opensdk.socket.ConnectErrorListener;
import com.kaolafm.opensdk.socket.SocketManager;


import java.util.List;

import javax.net.ssl.SSLHandshakeException;

import io.socket.engineio.client.EngineIOException;

/**
 * Created by v on 2018/4/13.
 */

public class HomePlayerPresenter
        implements HomePlayerContract.Presenter, ConnectErrorListener {

    private static final String TAG = "HomePlayerPresenter";
    /**
     * 是否第一次刷新，第一次刷新出错就显示错误页面，不是第一次不显示错误。
     */
    private boolean isFirstRefresh = true;

    private HomePlayerContract.View iView;

    private ICategoryModel mModel;

    private KRadioAudioPlayLogicInter mKRadioAudioPlayLogicInter;

    private KRadioSystemSourceChangeInter mKRadioSystemSourceChangeInter;

    private IPlayerInteractionFiredListener mPlayerInteractionFiredListener;

    public HomePlayerPresenter(Context mContext, HomePlayerContract.View iView, ICategoryModel model) {
        this.iView = iView;
        this.mModel = model;
        Log.i(TAG, "HomePlayerPresenter");
        SocketManager.getInstance().registerConnectErrorListener(this);
    }

    @Override
    public void init() {
        mKRadioAudioPlayLogicInter = ClazzImplUtil.getInter("KRadioAudioPlayLogicImpl");
        mKRadioSystemSourceChangeInter = ClazzImplUtil.getInter("KRadioSystemSourceChangeImpl");
        initPlayerInteractionFiredListener();
    }

    public void onPlayerInitSuccess() {
        requestAudioFocus();
        checkVolumeBalance();
        registerSourceChanged();
        MediaSessionUtil.getInstance().registerMediaSession();
    }

    private void checkVolumeBalance() {
        new VolumeRequest().checkVolumeBalanceStatus(new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean isOpen) {
                int openValue = isOpen ? 1 : 0;
                PlayerManager.getInstance().setLoudnessNormalization(openValue);
            }

            @Override
            public void onError(ApiException e) {

            }
        });
    }

    /**
     * 注册源切换
     */
    private void registerSourceChanged() {
        if (mKRadioSystemSourceChangeInter != null) {
            mKRadioSystemSourceChangeInter.registerSourceChanged();
        }
    }

    private void requestAudioFocus() {
        if (mKRadioAudioPlayLogicInter != null) {
            boolean flag = mKRadioAudioPlayLogicInter.requestAudioFocus();
            Log.i(TAG, "requestAudioFocus-------->flag = " + flag);
        }
    }

    @Override
    public void start() {
        boolean flag = KradioSDKManager.getInstance().isUsable();
        // 首次启动，在未激活状态下不调用
        Log.i("k.activate", "start: isActivation = " + flag);
        // fixme 暂时容错，需要整理激活逻辑
        if (flag) {
            getData();
        } else {
            KradioSDKManager.getInstance().addUsableObserver(this::getData);
        }
    }

    /**
     * 获取数据
     */
    private void getData() {
        if (iView != null) {
            iView.hideErrorLayout();
            iView.showLoading();
        }
        mModel.getDatas(new HttpCallback<List<ColumnGrp>>() {

            @Override
            public void onSuccess(List<ColumnGrp> columnGrps) {
                if (iView != null) {
                    Log.i(TAG, "trouble shoot, enter method mModel.getDatas.onSuccess iView not null");
                    isFirstRefresh = false;

                    if (!ListUtil.isEmpty(columnGrps)) {
                        Log.i(TAG, "trouble shoot, enter method mModel.getDatas.onSuccess begin show content");
                        iView.showContent(columnGrps);
                    }
                } else {
                    Log.i(TAG, "getData onSuccess iView is null");
                }
                reportRecommendShow(columnGrps);
            }

            @Override
            public void onError(ApiException e) {
                connectError();
                Log.i(TAG, "getData onError e = " + e);
            }
        });

    }

    /**
     * 推荐展示数据上报
     *
     * @param listListPair
     */
    private void reportRecommendShow( List<ColumnGrp> listListPair) {
        if (listListPair == null) {
            return;
        }
        if (ListUtil.isEmpty(listListPair)) {
            return;
        }
        ReportUtil.addRecommendShowEvent(listListPair);
    }

    @Override
    public void destroy() {
        iView = null;
        unregisterSourceChanged();
    }

    public void autoPlay() {
        HomeDataManager.getInstance().autoPlay();
    }

    private void connectError() {
        if (isFirstRefresh) {
            if (iView != null) {
                iView.showError("");
            }
        }
    }

    private void connectError(String error) {
        if (iView != null) {
            iView.showError(error);
        }
    }

    /**
     * 注销源切换
     */
    private void unregisterSourceChanged() {
        if (mKRadioSystemSourceChangeInter != null) {
            mKRadioSystemSourceChangeInter.unregisterSourceChanged();
        }
    }

    private void initPlayerInteractionFiredListener() {
        mPlayerInteractionFiredListener = (playItem, position, id) -> {
            PlayerLogUtil.log(getClass().getSimpleName(), "ijk soft ad callback: position" + position + ", id = " + id);
            exposureAd(String.valueOf(id));
        };
        PlayerManager playerManager = PlayerManager.getInstance();
        if (playerManager.isPlayerInitSuccess()) {
            playerManager.setPlayerInteractionFiredListener(mPlayerInteractionFiredListener);
        } else {
            playerManager.addPlayerInitComplete(b -> playerManager.setPlayerInteractionFiredListener(mPlayerInteractionFiredListener));
        }
    }

    /**
     * 广告曝光
     *
     * @param adId
     */
    public void exposureAd(String adId) {
        AdvertisingManager.getInstance().expose(adId,
                String.valueOf(ScreenUtil.getScreenWidth()),
                String.valueOf(ScreenUtil.getScreenHeight()),
                "4",
                ""
        );
    }

    @Override
    public void onConnectError(Object... args) {
        Log.e(TAG, "Socket Connect Error.");
        if (args.length > 0
                && args[0] instanceof EngineIOException
                && null != ((EngineIOException) args[0]).getCause()
                && ((EngineIOException) args[0]).getCause() instanceof SSLHandshakeException) {
            connectError(ResUtil.getString(R.string.home_network_certificate_error));
        } else {
            connectError();
        }
    }
}

