package com.kaolafm.base.internal;

import android.content.Context;
import android.content.SharedPreferences;

/**
 * <AUTHOR> on 2019-08-20.
 * 听伴升级KRadio读取缓存UDID
 */

public class TingBanToKRadioCacheObtainDeviceId extends BaseObtainDeviceId {
    private static final String UDID_PREFERENCE_NAME = "udid_information_pf";

    private static final String UDID_VALUE = "udid_value";

    @Override
    protected String createUUID(Context context) {
        return getSharedPreferences(context).getString(UDID_VALUE, "");
    }

    /**
     * 获取SharedPreferences
     *
     * @param context
     * @return
     */
    private SharedPreferences getSharedPreferences(Context context) {
        SharedPreferences sharedPreferences = context.getSharedPreferences(UDID_PREFERENCE_NAME, Context.MODE_PRIVATE);
        return sharedPreferences;
    }

}
