<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <RelativeLayout
        android:layout_weight="1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <com.kaolafm.kradio.lib.widget.tab.SlidingTabLayout
            android:id="@+id/srtl_home_navigation_tab"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/home_all_button_height"
            android:minWidth="@dimen/m52"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_default="wrap"
            app:tl_first_no_padding="true"
            app:tl_indicator_anim_enable="false"
            app:tl_indicator_style="NORMAL"
            app:tl_tab_padding="@dimen/m30"
            app:kradio_tl_textSize="@dimen/m30"
            app:kradio_tl_textSelectSize="@dimen/m34"
            app:tl_textSelectColor="@color/sliding_tab_text_selected"
            app:tl_textUnselectColor="@color/sliding_tab_text_unselected" />

        <View
            android:id="@+id/view_zhe"
            android:visibility="gone"
            android:background="@drawable/home_tab_zhe_bg"
            android:layout_alignRight="@+id/srtl_home_navigation_tab"
            android:layout_width="@dimen/m60"
            android:layout_height="@dimen/home_all_button_height" />
    </RelativeLayout>

    <TextView
        android:id="@+id/iv_home_nav_allcategray_more"
        style="@style/home_all_ctg_style"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="@dimen/x20"
        android:gravity="center"
        android:textColor="@color/text_color_7"
        android:textSize="@dimen/text_size5"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/srtl_home_navigation_tab"
        app:layout_constraintTop_toTopOf="parent" />

    <!--    <View-->
    <!--        android:id="@+id/iv_home_nav_mongolian_layer"-->
    <!--        android:layout_width="@dimen/m50"-->
    <!--        android:layout_height="0dp"-->
    <!--        android:background="@drawable/home_nav_mongolian_bg_selector"-->
    <!--        android:visibility="invisible"-->
    <!--        app:layout_constraintBottom_toBottomOf="@id/srtl_home_navigation_tab"-->
    <!--        app:layout_constraintRight_toRightOf="@id/srtl_home_navigation_tab"-->
    <!--        app:layout_constraintTop_toTopOf="@id/srtl_home_navigation_tab" />-->

</LinearLayout>