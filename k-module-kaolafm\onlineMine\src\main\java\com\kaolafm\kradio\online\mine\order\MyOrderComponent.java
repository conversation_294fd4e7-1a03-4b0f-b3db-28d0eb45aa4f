package com.kaolafm.kradio.online.mine.order;

import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.kaolafm.kradio.component.BaseComponent;
import com.kaolafm.kradio.component.RealCaller;
import com.kaolafm.kradio.lib.utils.Constants;

/**
 * BroadcastPlayer组件，此Activity不是安卓系统的Activity
 *
 */
public class MyOrderComponent extends BaseComponent{

    private static final String START_ACTIVITY = "startActivity";

    @Override
    protected void initProcessors() {
    }

    @Override
    public boolean onCall(RealCaller caller) {
        String actionName = caller.actionName();
        if (START_ACTIVITY.equals(actionName)) {
            Log.i(Constants.START_TAG, "startToSearch------------>START_PAGE");
            Context context = caller.getParamValue("context");
            Intent intent = new Intent();
            intent.setClass(context, MyOrderActivity.class);
            context.startActivity(intent);
        }

        return super.onCall(caller);
    }

}
