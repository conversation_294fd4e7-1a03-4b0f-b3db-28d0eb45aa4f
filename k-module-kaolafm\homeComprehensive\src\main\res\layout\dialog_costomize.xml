<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/x783"
    android:layout_height="@dimen/y508"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_gravity="center"
    android:background="@color/color_2">

    <TextView
        android:id="@+id/dialog_tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y60"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        tools:text="定制你的收听偏好"
        app:layout_constraintTop_toTopOf="parent" />
    <TextView
        android:id="@+id/dialog_tv_subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y20"
        android:textColor="@color/text_color_3"
        android:textSize="@dimen/text_size1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/dialog_tv_title"
        tools:text="请选择最多3个兴趣标签"
        />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/dialog_rv"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/y50"
        android:layout_marginBottom="@dimen/y30"
        android:overScrollMode="never"
        app:layout_constraintBottom_toTopOf="@+id/dialog_btn_left"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/dialog_tv_subtitle" />

    <Button
        android:id="@+id/dialog_btn_left"
        android:layout_width="@dimen/x160"
        android:layout_height="@dimen/y60"
        android:layout_marginBottom="@dimen/y40"
        android:background="@drawable/selector_dialog_costomize_btn_sure"
        android:textColor="@color/selector_dialog_btn_text_color"
        android:textSize="@dimen/text_size3"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/dialog_btn_right" />

    <Button
        android:id="@+id/dialog_btn_right"
        android:layout_width="@dimen/x160"
        android:layout_height="@dimen/y60"
        android:layout_marginLeft="@dimen/y70"
        android:layout_marginBottom="@dimen/y40"
        android:background="@drawable/selector_dialog_costomize_btn_cancle"
        android:textColor="@color/selector_dialog_btn_text_color"
        android:textSize="@dimen/text_size3"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/dialog_btn_left"
        app:layout_constraintRight_toRightOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>