<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_view"
    android:background="@color/message_details_bg"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/card_bg_iv"
        android:layout_width="@dimen/m594"
        android:layout_height="@dimen/m397"
        android:layout_marginStart="@dimen/m48"
        android:layout_marginBottom="@dimen/m72"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="567:340"
        app:layout_constraintStart_toStartOf="parent"
        tools:background="@drawable/online_message_bubble_nonebutton_bg" />

    <com.kaolafm.kradio.online.common.view.DoubleLayerView
        android:id="@+id/bubbleBg"
        android:layout_width="@dimen/m594"
        android:layout_height="@dimen/m397"
        android:layout_marginStart="@dimen/m48"
        android:layout_marginBottom="@dimen/m72"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="567:340"
        app:layout_constraintStart_toStartOf="parent"
        tools:background="@drawable/online_message_bubble_nonebutton_bg_board" />

    <ImageView
        android:id="@+id/bubbleIcon"
        android:layout_width="0dp"
        android:layout_height="@dimen/m228"
        android:layout_marginStart="@dimen/m25"
        android:layout_marginBottom="@dimen/m295"
        android:scaleType="fitXY"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="588:684"
        app:layout_constraintStart_toStartOf="@id/bubbleBg"
        tools:src="@drawable/online_message_bubble_warning" />

    <LinearLayout
        android:id="@+id/bubbleTitle_ll"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/m63"
        android:layout_marginTop="@dimen/m129"
        android:orientation="vertical"
        app:layout_constraintStart_toStartOf="@id/bubbleBg"
        app:layout_constraintTop_toTopOf="@id/bubbleBg">

        <ImageView
            android:id="@+id/msg_tips_pic_iv"
            android:layout_width="@dimen/m346"
            android:layout_height="@dimen/m32"
            android:layout_marginBottom="@dimen/m20"
            android:visibility="gone" />

        <TextView
            android:id="@+id/bubbleTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/online_bubble_title_color"
            android:textSize="@dimen/m24"
            tools:text="路况交通" />
    </LinearLayout>

    <TextView
        android:id="@+id/bubbleSubTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/m63"
        android:layout_marginTop="@dimen/m2"
        android:layout_marginEnd="@dimen/m63"
        android:textColor="@color/online_bubble_content_color"
        android:textSize="@dimen/m28"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="@id/bubbleBg"
        app:layout_constraintTop_toBottomOf="@id/bubbleTitle_ll"
        tools:text="前方三公里正在进行道路施工\n车辆行驶缓慢！" />

    <LinearLayout
        android:id="@+id/bubbleButtonParent"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/m17"
        android:layout_marginEnd="@dimen/m80"
        android:layout_marginBottom="@dimen/m26"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="@id/bubbleBg"
        app:layout_constraintEnd_toEndOf="@id/bubbleBg"
        app:layout_constraintStart_toStartOf="@id/bubbleBg">

    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>