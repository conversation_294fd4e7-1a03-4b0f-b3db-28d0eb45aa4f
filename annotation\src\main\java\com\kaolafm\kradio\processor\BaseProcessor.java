package com.kaolafm.kradio.processor;

import com.squareup.javapoet.ClassName;
import com.squareup.javapoet.JavaFile;
import com.squareup.javapoet.MethodSpec;
import com.squareup.javapoet.MethodSpec.Builder;
import com.squareup.javapoet.TypeName;
import com.squareup.javapoet.TypeSpec;

import java.io.IOException;
import java.util.Set;

import javax.lang.model.element.Element;
import javax.lang.model.element.Modifier;
import javax.lang.model.element.TypeElement;
import javax.tools.Diagnostic.Kind;

/**
 * <AUTHOR>
 * @date 2019-09-17
 */
public abstract class BaseProcessor implements IProcessor {

    protected CoreProcessor coreProcessor;

    private String mClassJavaDoc;

    public BaseProcessor(CoreProcessor processor) {
        coreProcessor = processor;
        mClassJavaDoc = "由 " + this.getClass().getSimpleName() + "自动生成。不要修改!\n";
    }

    protected TypeSpec.Builder getTypeSpec(Builder constructorMethod) {
        TypeSpec.Builder builder = TypeSpec.classBuilder(getClassName())
                .superclass(getSuperclass())
                .addModifiers(Modifier.PUBLIC)
                .addJavadoc(mClassJavaDoc);
        if (constructorMethod != null) {
            builder.addMethod(constructorMethod.build());
        }
        return builder;
    }

    @Override
    public void process(Set<? extends Element> elements) {
        Builder constructorMethod = MethodSpec.constructorBuilder().addModifiers(Modifier.PUBLIC);
        insertStatementBefore((Set<TypeElement>) elements, constructorMethod);
        boolean generateOneClass = generateOneClass();
        elements.forEach(element -> {
            if (validateAnnotateElement(element)) {
                processElement((TypeElement) element, constructorMethod);
            }
        });
        if (generateOneClass) {
            writeJavaFile(getTypeSpec(constructorMethod).build());
        }
    }

    protected TypeName getSuperclass() {
        return getTypeName("java.lang.Object");
    }

    protected TypeName getTypeName(String canonicalName) {
        return ClassName.get(coreProcessor.elementUtils.getTypeElement(canonicalName));
    }

    /**
     * 创建构造函数后，其他操作之前添加构造参数或其他。
     */
    protected abstract void insertStatementBefore(Set<TypeElement> elements,
            Builder constructorMethod);

    /**
     * 验证element合法性
     * @param element
     * @return
     */
    protected abstract boolean validateAnnotateElement(Element element);

    protected abstract String getClassName();

    protected abstract String getGeneratedPackageName();

    abstract boolean generateOneClass();

    /**
     * 构造函数中处理element
     * @param element
     * @param constructorMethod
     */
    protected abstract void processElement(TypeElement element, Builder constructorMethod);

    protected void writeJavaFile(TypeSpec type) {
        try {
            JavaFile.builder(getGeneratedPackageName(), type).build().writeTo(coreProcessor.getProcessingEnv().getFiler());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    protected void log(String msg) {
        coreProcessor.messager.printMessage(Kind.NOTE, msg);
    }

}
