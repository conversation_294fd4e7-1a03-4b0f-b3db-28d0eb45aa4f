package com.kaolafm.kradio.activity.comprehensive;

import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.kaolafm.kradio.activity.comprehensive.ui.ActivitiesActivity;
import com.kaolafm.kradio.component.BaseComponent;
import com.kaolafm.kradio.component.RealCaller;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.YTLogUtil;

/**
 * 活动组件，此Activity不是安卓系统的Activity
 *
 */
public class ActivityComponent extends BaseComponent{

    private static final String START_ACTIVITY = "startActivity";

    @Override
    protected void initProcessors() {
    }

    @Override
    public boolean onCall(RealCaller caller) {
        String actionName = caller.actionName();
        if (START_ACTIVITY.equals(actionName)) {
            YTLogUtil.logStart("ActivityComponent", "onCall", "startToActivities");
            Context context = caller.getParamValue("context");
            Intent intent = new Intent();
            intent.setClass(context, ActivitiesActivity.class);
            context.startActivity(intent);
        }

        return super.onCall(caller);
    }

}
