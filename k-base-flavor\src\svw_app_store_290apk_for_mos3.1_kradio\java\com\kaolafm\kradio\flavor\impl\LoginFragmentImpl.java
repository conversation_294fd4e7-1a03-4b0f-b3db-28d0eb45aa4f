package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.text.InputFilter;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.base.flavor.LoginFragmentInter;
import com.kaolafm.kradio.lib.utils.IntentUtils;

public class LoginFragmentImpl implements LoginFragmentInter {

    private static final String TAG = LoginFragment.class.getSimpleName();

    private static final String ACTION_IME_START = "com.hanvon.inputmethod.IME_START";
    private static final String ACTION_IME_HIDE = "com.hanvon.inputmethod.IME_HIDE";

    int mType; //1：手机号   2：验证码
    View mEditSnapshot;
    EditText viewSnapshot;
    BroadcastReceiver mBroadcastReceiver;

    Activity context;

    EditText mEtPhoneNum;
    EditText mEtVerificationCode;

    @Override
    public void initView(Activity context, EditText phoneNumber, EditText verifyCode) {
        this.context = context;
        mEtPhoneNum = phoneNumber;
        mEtPhoneNum.setCursorVisible(false);
        mEtVerificationCode = verifyCode;
        mEtVerificationCode.setCursorVisible(false);
        mEditSnapshot = View.inflate(context, R.layout.phone_login_snapshot,
                null);
        viewSnapshot = mEditSnapshot.findViewById(R.id.input_view);
        viewSnapshot.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                if (actionId == EditorInfo.IME_ACTION_DONE) {
                    if (mType == 1) {
                        mEtPhoneNum.setText(viewSnapshot.getText().toString());
                    } else if (mType == 2) {
                        mEtVerificationCode.setText(viewSnapshot.getText().toString());
                    }
                }
                return false;
            }
        });

        mEditSnapshot.findViewById(R.id.back_img).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                removeSnapshot();
            }
        });

        mEditSnapshot.findViewById(R.id.tv_ok).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mType == 1) {
                    mEtPhoneNum.setText(viewSnapshot.getText().toString());
                } else if (mType == 2) {
                    mEtVerificationCode.setText(viewSnapshot.getText().toString());
                }

                removeSnapshot();
            }
        });

        mBroadcastReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                receiveBroadcast(context, intent);
            }
        };
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(ACTION_IME_START);
        intentFilter.addAction(ACTION_IME_HIDE);
        context.registerReceiver(mBroadcastReceiver, intentFilter);
    }

    private void receiveBroadcast(Context context, Intent intent) {
        if (ACTION_IME_START.equals(intent.getAction())&&!isFromBack) {
            showEditTextSnapshot();
            setSnapshotText();
        } else if (ACTION_IME_HIDE.equals(intent.getAction())) {
            //removeSnapshot();
        }
    }

    private void showEditTextSnapshot() {
        try {
            FrameLayout contentView = (FrameLayout) context.getWindow().getDecorView()
                    .findViewById(android.R.id.content);
            ViewGroup viewGroup = contentView.findViewById(R.id.phone_root_view);
            if (viewGroup == null) {
                contentView.addView(mEditSnapshot);
            }
        } catch (Exception e) {
            Log.i(TAG, "showEditTextSnapshot error");
        }
    }

    private void removeSnapshot() {
        FrameLayout contentView = (FrameLayout) ((Activity) context).getWindow().getDecorView()
                .findViewById(android.R.id.content);
        contentView.removeView(mEditSnapshot);
    }

    private void setSnapshotText() {
        if (mEtPhoneNum.isFocused()) {
            viewSnapshot.setHint(context.getResources().getString(com.kaolafm.kradio.k_kaolafm.R.string.user_input_phone_num));
            viewSnapshot.setFilters(new InputFilter[]{new InputFilter.LengthFilter(11)});
            viewSnapshot.setText(mEtPhoneNum.getText());
            mType = 1;
        } else if (mEtVerificationCode.isFocused()) {
            viewSnapshot.setHint(context.getResources().getString(
                    com.kaolafm.kradio.k_kaolafm.R.string.user_input_verification_code));
            viewSnapshot.setFilters(new InputFilter[]{new InputFilter.LengthFilter(6)});
            viewSnapshot.setText(mEtVerificationCode.getText());
            mType = 2;
        }
        viewSnapshot.setSelection(viewSnapshot.getText().length());
        viewSnapshot.requestFocus();
    }

    @Override
    public void onDestroy() {
        if (context != null) {
            context.unregisterReceiver(mBroadcastReceiver);
        }
    }

    boolean isFromBack;

    @Override
    public void onPause() {
        isFromBack = IntentUtils.getInstance().isAppOnForeground();
        if (isFromBack && mEtPhoneNum != null) {
            mEtPhoneNum.setText("");
            mEtVerificationCode.setText("");
            InputMethodManager imm = (InputMethodManager)context.getSystemService(Context.INPUT_METHOD_SERVICE);
            imm.hideSoftInputFromWindow(mEtVerificationCode.getWindowToken(),0);

        }
    }

    @Override
    public void onResume() {
        isFromBack = IntentUtils.getInstance().isAppOnForeground();
    }
}
