package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import androidx.annotation.RequiresApi;
import android.util.Log;

import com.kaolafm.kradio.k_kaolafm.home.HomeDataManager;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioClientPlayRetryInter;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.opensdk.player.logic.PlayerManager;

public class KRadioClientPlayRetryImpl implements KRadioClientPlayRetryInter {
    private static final String TAG = "client.impl.p";

    @Override
    public void retry() {
        Log.i(TAG, "resume retry");
        new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
            @RequiresApi(api = Build.VERSION_CODES.M)
            @Override
            public void run() {
                Log.i(TAG, "resume again");
                ConnectivityManager connectivity = (ConnectivityManager) AppDelegate.getInstance()
                        .getContext().getSystemService(Context.CONNECTIVITY_SERVICE);
                Network network = connectivity.getActiveNetwork();
                NetworkCapabilities capabilities = connectivity.getNetworkCapabilities(network);
                if (capabilities != null && capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)){
                    Log.i(TAG, "network available");
                    int currentFocus = PlayerManager.getInstance().getCurrentAudioFocusStatus();
                    Log.i(TAG, "checkFocusStatus: currentFocus = " + currentFocus);
                    boolean isPlaying = PlayerManager.getInstance().isPlaying();
                    Log.i(TAG, "player isPlaying = " + isPlaying);
                    if (currentFocus > 0 && isPlaying == false) {
                        HomeDataManager.getInstance().playNetOrLocal();
                    }
                } else {
                    Log.i(TAG, "network not available");
                    retry();
                }
            }
        }, 1000);
    }
}
