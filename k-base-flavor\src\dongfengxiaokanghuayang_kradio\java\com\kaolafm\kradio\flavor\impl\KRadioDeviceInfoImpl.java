package com.kaolafm.kradio.flavor.impl;


import android.provider.Settings;
import android.util.Log;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.StringUtil;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-05-22 20:50
 ******************************************/
public final class KRadioDeviceInfoImpl implements KRadioDeviceInfoInter {
    private static final String TAG = "KRadioDeviceInfoImpl";
    public static final String VIN = "dfsk_vin";

    @Override
    public String getDeviceId(Object... args) {
        String vin = Settings.System.getString(AppDelegate.getInstance().getContext().getContentResolver(), VIN);
        Log.i(TAG, "getDeviceId: " + vin);
        return StringUtil.makeAsciiOnly(vin);
    }

    @Override
    public String getCarType(Object... args) {
        return null;
    }
}
