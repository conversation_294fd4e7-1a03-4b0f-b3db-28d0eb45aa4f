package com.kaolafm.kradio.common.widget;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.graphics.Color;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.DecelerateInterpolator;
import android.widget.RelativeLayout;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.ViewUtil;

import java.util.ArrayList;


public class RadioPlayerImageAnimLayout extends RelativeLayout {

    private static final String TAG = "RadioPlayerAnimLayout";
    private static final int MAGIC_CARD_DURATION = 500;

    View mMagicOneView;
    View mMagicTwoView;
    View mMagicThreeView;
    View mMagicFourView;

    private boolean bPreIsComplete = true;
    private boolean bNextIsComplete = true;

    private int mMagicUnit;
    private int mHalfMagicUnit;

    private float mFourToThreeScalePre;
    private float mThreeToTwoScalePre;
    private float mTwoToOneScalePre;

    private float mOneToTwoScaleNext;
    private float mTwoToThreeScaleNext;
    private float mThreeToFourNext;

    /**
     * 是否可以执行切换上一首和下一首动画效果 true为是，false为否
     */
    private boolean canMakeMagicLayerAnimation;

    public RadioPlayerImageAnimLayout(Context context) {
        this(context, null);
    }

    public RadioPlayerImageAnimLayout(Context context, AttributeSet attrs) {
        this(context, attrs, -1);
    }

    public RadioPlayerImageAnimLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context);
    }

    private void initView(Context context) {
        View view= LayoutInflater.from(context).inflate(R.layout.radio_player_image_anim_layout, this, true);

        mMagicOneView =view.findViewById(R.id.player_card_magic_one);
        mMagicTwoView =view.findViewById(R.id.player_card_magic_two);
        mMagicThreeView =view.findViewById(R.id.player_card_magic_three);
        mMagicFourView =view.findViewById(R.id.player_card_magic_four);


    }

    public void showMagicLayer(int width) {
        if (width == 0) {
            return;
        }
        canMakeMagicLayerAnimation = true;
        ViewUtil.setViewVisibility(mMagicOneView, View.VISIBLE);
        ViewUtil.setViewVisibility(mMagicTwoView, View.VISIBLE);
        ViewUtil.setViewVisibility(mMagicThreeView, View.VISIBLE);
        ViewUtil.setViewVisibility(mMagicFourView, View.GONE);

        mMagicUnit = getResources().getDimensionPixelOffset(R.dimen.player_magic_card_margin_unit);
        mHalfMagicUnit = mMagicUnit / 2;

        LayoutParams layoutParams1 = (LayoutParams) mMagicOneView.getLayoutParams();
        layoutParams1.width = width - 3 * mMagicUnit;

        LayoutParams layoutParams2 = (LayoutParams) mMagicTwoView.getLayoutParams();
        layoutParams2.width = width - 2 * mMagicUnit;
//        Log.i(TAG, "layoutParams2.width="+layoutParams2.width);
        mTwoToOneScalePre = (layoutParams2.width - mMagicUnit) / (float) layoutParams2.width;
//        Log.i(TAG, "layoutParams1.width="+layoutParams1.width);
        mOneToTwoScaleNext = layoutParams2.width / (float) layoutParams1.width;

        LayoutParams layoutParams3 = (LayoutParams) mMagicThreeView.getLayoutParams();
        layoutParams3.width = width - mMagicUnit;
//        Log.i(TAG, "layoutParams3.width="+layoutParams3.width);
        mThreeToTwoScalePre = (layoutParams3.width - mMagicUnit) / (float) layoutParams3.width;
//        Log.i(TAG, "layoutParams2.width="+layoutParams2.width);
        mTwoToThreeScaleNext = layoutParams3.width / (float) layoutParams2.width;

        LayoutParams layoutParams4 = (LayoutParams) mMagicFourView.getLayoutParams();
        layoutParams4.width = width;
//        Log.i(TAG, "layoutParams3.width="+layoutParams3.width);
        mThreeToFourNext = layoutParams4.width / (float) layoutParams3.width;
//        Log.i(TAG, "width="+width);
        mFourToThreeScalePre = (width - mMagicUnit) / (float) width;
    }

    private void resetPreAnimation() {
        mMagicFourView.clearAnimation();
        mMagicThreeView.clearAnimation();
        mMagicTwoView.clearAnimation();
        try {
            ViewUtil.setViewVisibility(mMagicFourView, View.GONE);
            AnimatorSet animatorSet = new AnimatorSet();
            animatorSet.playTogether(
                    ObjectAnimator.ofFloat(mMagicFourView, "translationY", -mHalfMagicUnit, 0),
                    ObjectAnimator.ofFloat(mMagicFourView, "scaleX", mFourToThreeScalePre, 1.0F),

                    ObjectAnimator.ofFloat(mMagicThreeView, "translationY", -mHalfMagicUnit, 0),
                    ObjectAnimator.ofFloat(mMagicThreeView, "scaleX", mThreeToTwoScalePre, 1.0F),

                    ObjectAnimator.ofFloat(mMagicTwoView, "translationY", -mHalfMagicUnit, 0),
                    ObjectAnimator.ofFloat(mMagicTwoView, "alpha", 0F, 1.0F),
                    ObjectAnimator.ofFloat(mMagicTwoView, "scaleX", mTwoToOneScalePre, 1.0F)
            );
            animatorSet.addListener(new Animator.AnimatorListener() {
                @Override
                public void onAnimationStart(Animator animation) {
                }

                @Override
                public void onAnimationEnd(Animator animation) {
                    bPreIsComplete = true;
                    mMagicFourView.clearAnimation();
                    mMagicThreeView.clearAnimation();
                    mMagicTwoView.clearAnimation();
                }

                @Override
                public void onAnimationCancel(Animator animation) {
                    bPreIsComplete = true;
                    mMagicFourView.clearAnimation();
                    mMagicThreeView.clearAnimation();
                    mMagicTwoView.clearAnimation();
                }

                @Override
                public void onAnimationRepeat(Animator animation) {

                }
            });
//        Log.i(TAG, "mHalfMagicUnit="+mHalfMagicUnit);
//        Log.i(TAG, "mFourToThreeScalePre="+mFourToThreeScalePre);
//        Log.i(TAG, "mThreeToTwoScalePre="+mThreeToTwoScalePre);
//        Log.i(TAG, "mTwoToOneScalePre="+mTwoToOneScalePre);
            animatorSet.setDuration(0).start();
        } catch (Exception e) {
            Log.w(TAG, e.toString());
        }
    }

    public void managePreAnimation() {
        if (!bPreIsComplete || !bNextIsComplete) {
            return;
        }
        ViewUtil.setViewVisibility(mMagicFourView, View.VISIBLE);
        bPreIsComplete = false;
        AnimatorSet animatorSet = new AnimatorSet();
        ArrayList<Animator> animatorArrayList = new ArrayList<>();

        if (ViewUtil.canMakeAnimation(mMagicFourView)) {
            animatorArrayList.add(ObjectAnimator.ofFloat(mMagicFourView, "translationY", mHalfMagicUnit, 0F));
            animatorArrayList.add(ObjectAnimator.ofFloat(mMagicFourView, "scaleX", 1.0F, mFourToThreeScalePre));
        }

        if (ViewUtil.canMakeAnimation(mMagicThreeView)) {
            animatorArrayList.add(ObjectAnimator.ofFloat(mMagicThreeView, "translationY", mHalfMagicUnit, 0F));
            animatorArrayList.add(ObjectAnimator.ofFloat(mMagicThreeView, "scaleX", 1.0F, mThreeToTwoScalePre));
        }

        if (ViewUtil.canMakeAnimation(mMagicTwoView)) {
            animatorArrayList.add(ObjectAnimator.ofFloat(mMagicTwoView, "translationY", mHalfMagicUnit, 0F));
            animatorArrayList.add(ObjectAnimator.ofFloat(mMagicTwoView, "alpha", 0.2F, 0F));
            animatorArrayList.add(ObjectAnimator.ofFloat(mMagicTwoView, "scaleX", 1.0F, mTwoToOneScalePre));
        }

        animatorSet.playTogether(/*
                ObjectAnimator.ofFloat(mMagicFourView, "translationY", mHalfMagicUnit, 0F),
                ObjectAnimator.ofFloat(mMagicFourView, "scaleX", 1.0F, mFourToThreeScalePre),

                ObjectAnimator.ofFloat(mMagicThreeView, "translationY", mHalfMagicUnit, 0F),
                ObjectAnimator.ofFloat(mMagicThreeView, "scaleX", 1.0F, mThreeToTwoScalePre),

                ObjectAnimator.ofFloat(mMagicTwoView, "translationY", mHalfMagicUnit, 0F),
                ObjectAnimator.ofFloat(mMagicTwoView, "alpha", 0.2F, 0F),
                ObjectAnimator.ofFloat(mMagicTwoView, "scaleX", 1.0F, mTwoToOneScalePre)*/
                animatorArrayList);
        animatorSet.setInterpolator(new DecelerateInterpolator());
        animatorSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                resetPreAnimation();
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                resetPreAnimation();
            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        animatorSet.setDuration(MAGIC_CARD_DURATION).start();
    }

    private void resetNextAnimation() {
        mMagicOneView.clearAnimation();
        mMagicTwoView.clearAnimation();
        mMagicThreeView.clearAnimation();

        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.playTogether(
                ObjectAnimator.ofFloat(mMagicOneView, "translationY", mHalfMagicUnit, 0),
                ObjectAnimator.ofFloat(mMagicOneView, "alpha", 1.0F, 0.0F),
                ObjectAnimator.ofFloat(mMagicOneView, "scaleX", mOneToTwoScaleNext, 1.0F),

                ObjectAnimator.ofFloat(mMagicTwoView, "translationY", mHalfMagicUnit, 0),
                ObjectAnimator.ofFloat(mMagicTwoView, "scaleX", mTwoToThreeScaleNext, 1.0F),

                ObjectAnimator.ofFloat(mMagicThreeView, "translationY", mHalfMagicUnit, 0),
                ObjectAnimator.ofFloat(mMagicThreeView, "alpha", 0.0F, 1.0F),
                ObjectAnimator.ofFloat(mMagicThreeView, "scaleX", mThreeToFourNext, 1.0F)
        );
        animatorSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
                mMagicOneView.setBackgroundColor(Color.TRANSPARENT);
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                bNextIsComplete = true;
                mMagicOneView.clearAnimation();
                mMagicTwoView.clearAnimation();
                mMagicThreeView.clearAnimation();
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                bNextIsComplete = true;
                mMagicOneView.clearAnimation();
                mMagicTwoView.clearAnimation();
                mMagicThreeView.clearAnimation();
            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        animatorSet.setDuration(0).start();
    }

    public void manageNextAnimation() {
        if (!bNextIsComplete || !bPreIsComplete) {
            return;
        }
        bNextIsComplete = false;
        final AnimatorSet animatorSet = new AnimatorSet();

        animatorSet.playTogether(
                ObjectAnimator.ofFloat(mMagicOneView, "translationY", 0, mHalfMagicUnit),
                ObjectAnimator.ofFloat(mMagicOneView, "alpha", 0, 1.0F),
                ObjectAnimator.ofFloat(mMagicOneView, "scaleX", 1.0F, mOneToTwoScaleNext),

                ObjectAnimator.ofFloat(mMagicTwoView, "translationY", 0, mHalfMagicUnit),
                ObjectAnimator.ofFloat(mMagicTwoView, "scaleX", 1.0F, mTwoToThreeScaleNext),

                ObjectAnimator.ofFloat(mMagicThreeView, "translationY", 0, mHalfMagicUnit),
                ObjectAnimator.ofFloat(mMagicThreeView, "alpha", 1.0F, 0.0F),
                ObjectAnimator.ofFloat(mMagicThreeView, "scaleX", 1.0F, mThreeToFourNext)
        );
        animatorSet.setInterpolator(new AccelerateInterpolator());
        animatorSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
                mMagicOneView.setBackgroundResource(R.drawable.shape_white50_top_round_rectangle);
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                resetNextAnimation();
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                resetNextAnimation();
            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        animatorSet.setDuration(MAGIC_CARD_DURATION).start();
    }
}
