package com.kaolafm.kradio.common.utils;

import com.kaolafm.kradio.lib.base.AppDelegate;

/**
 * <AUTHOR> on 2019-09-06.
 * 权限管理工具类
 */

public class PermissionManagerUtil {
    PermissionUtils mPermissionUtils;

    private static final class PermissionManagerUtilHolder {
        private static final PermissionManagerUtil INSTANCE = new PermissionManagerUtil();
    }

    public static PermissionManagerUtil getInstance() {
        return PermissionManagerUtilHolder.INSTANCE;
    }

    private PermissionManagerUtil() {
    }

    public boolean isGrant() {
        if (mPermissionUtils == null) {
            mPermissionUtils = new PermissionUtils(AppDelegate.getInstance().getContext());
        }
        return mPermissionUtils.isGrant();
    }
}
