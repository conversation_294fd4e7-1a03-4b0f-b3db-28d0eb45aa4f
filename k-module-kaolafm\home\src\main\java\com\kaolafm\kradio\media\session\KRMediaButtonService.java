//package com.kaolafm.media.session;
//
//import android.app.Service;
//import android.content.Intent;
//import android.os.IBinder;
//import android.support.annotation.Nullable;
//import android.support.v4.media.session.MediaButtonReceiver;
//
//import com.kaolafm.kradio.lib.utils.IntentUtils;
//import com.kaolafm.utils.MediaSessionUtil;
//
///******************************************
// * 类描述:
// *
// * @version: V1.0
// * @author: yangshaoning
// * @time: 2019-12-26 16:57
// ******************************************/
//public class KRMediaButtonService extends Service {
//
//    @Override
//    public void onCreate() {
//        super.onCreate();
//        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001772006790?userId=1229522问题
//        new IntentUtils().startForeground(this);
//    }
//
//    @Nullable
//    @Override
//    public IBinder onBind(Intent intent) {
//        return null;
//    }
//
//    public int onStartCommand(Intent intent, int flags, int startId) {
//        MediaButtonReceiver.handleIntent(MediaSessionUtil.getInstance().getMediaSession(), intent);
//        return super.onStartCommand(intent, flags, startId);
//    }
//}
