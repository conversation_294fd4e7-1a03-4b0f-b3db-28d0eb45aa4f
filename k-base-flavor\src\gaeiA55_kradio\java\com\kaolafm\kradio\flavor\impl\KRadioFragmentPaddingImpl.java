package com.kaolafm.kradio.flavor.impl;

import android.view.View;

import com.kaolafm.kradio.lib.base.flavor.KRadioFragmentPaddingInter;

import static com.kaolafm.kradio.lib.utils.ViewUtil.addPaddingForView;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-12-10 10:54
 ******************************************/
public class KRadioFragmentPaddingImpl implements KRadioFragmentPaddingInter {
    @Override
    public boolean doFragmentPadding(Object... args) {
        View view = (View) args[0];
        addPaddingForView(view, 180, 0, 0, 0);
        return true;
    }
}
