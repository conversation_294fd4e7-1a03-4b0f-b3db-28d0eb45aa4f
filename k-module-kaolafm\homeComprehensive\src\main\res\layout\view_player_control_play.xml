<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical"
    tool:background="@color/color_2">

    <ImageView
        android:id="@+id/play_switch_playState_mode"
        android:layout_width="@dimen/player_page_play_and_pause_button_size"
        android:layout_height="@dimen/player_page_play_and_pause_button_size"
        android:layout_centerInParent="true"
        android:layout_gravity="center"
        android:background="@drawable/color_main_button_click_selector"
        android:padding="@dimen/m20"
        android:scaleType="fitXY"
        android:src="@drawable/ic_player_bar_pause_normal" />

    <fr.castorflex.android.circularprogressbar.CircularProgressBar
        android:id="@+id/play_play_loading"
        style="@style/CustomerCircularProgressBar"
        android:layout_width="@dimen/m82"
        android:layout_height="@dimen/m82"
        android:layout_centerInParent="true"
        android:layout_gravity="center"
        android:visibility="gone"
        app:cpb_color="@color/circular_progress_color"
        app:cpb_stroke_width="@dimen/loading_progress_width" />

</merge>