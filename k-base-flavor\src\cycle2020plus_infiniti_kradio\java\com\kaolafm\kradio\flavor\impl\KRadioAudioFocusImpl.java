package com.kaolafm.kradio.flavor.impl;

import android.annotation.TargetApi;
import android.content.Context;
import android.media.AudioAttributes;
import android.media.AudioFocusRequest;
import android.media.AudioManager;
import android.provider.Settings;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioFocusInter;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;

import static android.media.AudioManager.AUDIOFOCUS_GAIN_TRANSIENT;
import static android.media.AudioManager.AUDIOFOCUS_REQUEST_GRANTED;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-03-23 15:49
 ******************************************/
public class KRadioAudioFocusImpl implements KRadioAudioFocusInter {
    private int mAudioSource_Mic = 0;
    private AudioManager mAudioManager = null;

    public KRadioAudioFocusImpl() {
        mAudioSource_Mic = Settings.Global.getInt(AppDelegate.getInstance().getContext().getContentResolver(), "ivi.media.streamtype.defmic", 0);
    }


    @TargetApi(26)
    @Override
    public boolean requestAudioFocus(Object... args) {
        AudioManager.OnAudioFocusChangeListener audioFocusChangeListener = (AudioManager.OnAudioFocusChangeListener) args[0];
        AudioAttributes aa = new AudioAttributes.Builder().setLegacyStreamType(mAudioSource_Mic).build();

        int durationHint = AUDIOFOCUS_GAIN_TRANSIENT;

        AudioFocusRequest afr = new AudioFocusRequest.Builder(durationHint)
                .setAudioAttributes(aa)
                .setAcceptsDelayedFocusGain(false)
                .setOnAudioFocusChangeListener(audioFocusChangeListener) //handler 根据项目需求传入
                .build();
        //申请audio focus
        if (mAudioManager == null) {
            mAudioManager = (AudioManager) AppDelegate.getInstance().getContext().getSystemService(Context.AUDIO_SERVICE);
        }
        int nRet = mAudioManager.requestAudioFocus(afr);
        boolean status = nRet == AUDIOFOCUS_REQUEST_GRANTED;
        if (status) {
            PlayerManagerHelper.getInstance().pause(false);
        }
        return status;
    }

    @Override
    public boolean abandonAudioFocus(Object... args) {
//        if (mAudioManager != null) {
//            mAudioManager.abandonAudioFocus((AudioManager.OnAudioFocusChangeListener) args[0]);
//            return true;
//        }
        //https://app.huoban.com/tables/2100000007530121/items/2300001651947599?userId=1545533
        boolean playing = PlayerManagerHelper.getInstance().isPlaying();
        if (!playing) {
            PlayerManagerHelper.getInstance().play(false);
        }
        return false;
    }
}
