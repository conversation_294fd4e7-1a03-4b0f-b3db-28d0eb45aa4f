package com.kaolafm.kradio.search;

import android.content.Context;
import android.content.res.Configuration;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;
import androidx.constraintlayout.widget.Guideline;
import androidx.fragment.app.DialogFragment;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.util.TypedValue;
import android.view.ActionMode;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewStub;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.google.android.flexbox.FlexboxLayoutManager;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.common.ViewConstants;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.flavor.KRadioMultiWindowInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioSearchInter;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.lib.dialog.Dialogs;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.MultiUtil;
import com.kaolafm.kradio.lib.utils.NetworkMonitor;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.RecyclerViewExposeUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.bean.BroadcastRadioSimpleData;
import com.kaolafm.kradio.home.comprehensive.playerbar.ComprehensivePlayerHelper;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.api.personalise.model.HotRecommend;
import com.kaolafm.opensdk.api.search.model.SearchProgramBean;
import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.listener.IGeneralListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.trello.rxlifecycle3.android.FragmentEvent;

import java.util.ArrayList;
import java.util.List;

/**
 * 搜索页面
 */
public class SearchFragment extends BaseFragment<SearchPresenter>
        implements ISearchView, RecyclerViewExposeUtil.OnItemExposeListener {
    private static final String TAG = "SearchFragment";

    EditText mEtSearchWord;
    ImageView mIvSearchWordDelete;
    TextView mTvSearch;
    ImageView mTvClearSearchHistory;
    RecyclerView mRvAssociateList;
    RecyclerView mRvSearchHistoryTags;
    RecyclerView mRvSearchResults;
    ViewStub mVsSearchException;
    ViewStub mVsSearchNetworkError;
    View mSearchLoadingView;
    Guideline mTopGuideLine;
    ConstraintLayout mRootLayout;
    ConstraintLayout mClSearchView;
    ImageView mIvSearchBack;
    TextView mTvSearchHistory;
    TextView mTvHotSearchWords;
    RecyclerView mRvHotSearchWords;
    TypeSpinner mTsSearchType;

    private TextView mTvException;
    private View mExceptionView;
    private View mNetworkErrorView;

    private static final int STATE_SEARCH_HISTORY = 0;
    private static final int STATE_SEARCH_ASSOCIATE = 1;
    private static final int STATE_SEARCH_RESULT = 2;
    private static final int STATE_SEARCH_EXCEPTION = 3;
    private static final int STATE_SEARCH_LOADING = 4;

    private int mState = STATE_SEARCH_HISTORY;

    private boolean isSearchStarted = false;

    private boolean isClick = false;

    private volatile boolean isGetHotWordsFailed;

    private BasePlayStateListener mPlayerStateListener;

    private List<String> mHotWords = new ArrayList<>();

    SearchResultAdapter searchResultAdapter;

    private int lineCount = 2;
    GridLayoutManager layoutManager;

    KRadioMultiWindowInter kRadioMultiWindowInter;
    List<SearchProgramBeanResult> mSearchProgramBeans;

    private IGeneralListener generalListener;


    private boolean isShowWindow = false;

    @Override
    protected int getLayoutId() {
        return R.layout.fragment_search;
    }

    @Override
    protected int getLayoutId_Tow() {
        return 0;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        kRadioMultiWindowInter = ClazzImplUtil.getInter("KradioMultiWindowImpl");
        getActivity().getWindow().setSoftInputMode(
                WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN);
    }

    @Override
    protected SearchPresenter createPresenter() {
        return new SearchPresenter(this);
    }

    @Override
    public void initView(View view) {
        mEtSearchWord=view.findViewById(R.id.et_search_word);
        mIvSearchWordDelete=view.findViewById(R.id.iv_search_word_delete);
        mTvSearch=view.findViewById(R.id.tv_search);
        mTvClearSearchHistory=view.findViewById(R.id.tv_clear_search_history);
        mRvAssociateList=view.findViewById(R.id.rv_associate_list);
        mRvSearchHistoryTags=view.findViewById(R.id.rv_search_history_tags);
        mRvSearchResults=view.findViewById(R.id.rv_search_results);
        mVsSearchException=view.findViewById(R.id.vs_search_exception);
        mVsSearchNetworkError=view.findViewById(R.id.vs_search_network_error);
        mSearchLoadingView=view.findViewById(R.id.search_loading);
        mTopGuideLine=view.findViewById(R.id.search_top_guideline);
        mRootLayout=view.findViewById(R.id.search_main_layout);
        mClSearchView=view.findViewById(R.id.cl_search_view);
        mIvSearchBack=view.findViewById(R.id.backView);
        mTvSearchHistory=view.findViewById(R.id.tv_search_history);
        mTvHotSearchWords=view.findViewById(R.id.tv_hot_search_words);
        mRvHotSearchWords=view.findViewById(R.id.rv_hot_search_words);
        mTsSearchType=view.findViewById(R.id.ts_search_type);

        mTvSearch.setOnClickListener(v -> onViewClick(v));
        mIvSearchWordDelete.setOnClickListener(v -> onViewClick(v));
        mTvClearSearchHistory.setOnClickListener(v -> onViewClick(v));

        int ori = ResUtil.getOrientation(); //获取屏幕方向
        if (ori == Configuration.ORIENTATION_LANDSCAPE) {
            //横屏
            lineCount = 2;
        } else if (ori == Configuration.ORIENTATION_PORTRAIT) {
            //竖屏
            lineCount = 1;
        }

        mEtSearchWord.addTextChangedListener(mTextWatcher);
        mEtSearchWord.setOnEditorActionListener(mEditActionListener);
        mEtSearchWord.setLongClickable(false);
        mEtSearchWord.setTextIsSelectable(false);
        mEtSearchWord.setCustomSelectionActionModeCallback(new ActionMode.Callback() {
            public boolean onCreateActionMode(ActionMode mode, Menu menu) {
                return false;
            }

            public boolean onPrepareActionMode(ActionMode mode, Menu menu) {
                return false;
            }

            public boolean onActionItemClicked(ActionMode mode, MenuItem item) {
                return false;
            }

            public void onDestroyActionMode(ActionMode mode) {
            }
        });

        NetworkMonitor.getInstance(getContext()).registerNetworkStatusChangeListener(mNetworkStatusChangedListener);


        addPlayerOB();
        PlayerManager.getInstance().addPlayControlStateCallback(mPlayerStateListener);
        PlayerManager.getInstance().addGeneralListener(generalListener);

        initSearchHistoryView();
        refreshSearchHistoryView();
        initHotSearchWordsView();
        getHotSearchWords();
        initSearchAssociateView();
        initSearchResultView();
        initSearchTypeView();

        showOrHideHistoryView(true);
        initViewInner();
        KRadioSearchInter kRadioSearchInter = ClazzImplUtil.getInter("KRadioSearchImpl");
        if (kRadioSearchInter != null) {
            kRadioSearchInter.configInputSoft(mEtSearchWord);
        }
    }

    private void addPlayerOB() {
        mPlayerStateListener = new BasePlayStateListener() {
            @Override
            public void onPlayerPreparing(PlayItem playItem) {
                super.onPlayerPreparing(playItem);
                Log.d("mPlayerStateListener", "onPlayerPreparing");
                ComprehensivePlayerHelper.printPlayitem("mPlayerStateListener", playItem);
//                updateItemPlayingStateByPlayer(playItem);
            }

            @Override
            public void onPlayerEnd(PlayItem playItem) {
                super.onPlayerEnd(playItem);
                Logging.d("mPlayerStateListener", "onPlayerEnd");
                Log.d("mPlayerStateListener", "playItem getTitle = " + playItem.getTitle());
                if (isValidStatus()) {
                }
            }

            @Override
            public void onPlayerPlaying(PlayItem playItem) {
                Log.d("mPlayerStateListener", "onPlayerPlaying");
                Log.d("mPlayerStateListener", "playItem getTitle = " + playItem.getTitle());
                super.onPlayerPlaying(playItem);
                if (isValidStatus()) {

                }
                if (mState == STATE_SEARCH_RESULT) {
                    searchResultAdapter = (SearchResultAdapter) mRvSearchResults.getAdapter();
                    if (searchResultAdapter != null) {
                        if (!isClick) {
                            searchResultAdapter.setType(0);
                        }
                        searchResultAdapter.notifyDataSetChanged();
                        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001629835652?userId=1229522问题
                        //  searchResultAdapter.refreshPlaying(PlayerManager.getInstance().getCurPlayItem().getRadioId());
                    }
                    isClick = false;
                }
            }

            @Override
            public void onPlayerPaused(PlayItem playItem) {
                Log.d("mPlayerStateListener", "onPlayerPaused");
                super.onPlayerPaused(playItem);
                if (isValidStatus()) {

                }
                super.onPlayerPaused(playItem);
                if (mState == STATE_SEARCH_RESULT) {
                    searchResultAdapter = (SearchResultAdapter) mRvSearchResults.getAdapter();
                    if (searchResultAdapter != null) {
//                        searchResultAdapter.notifyDataSetChanged();
                        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001629835652?userId=1229522问题
                        searchResultAdapter.refreshPlaying(PlayerManagerHelper.getInstance().getCurPlayItem().getRadioId());
                        //searchResultAdapter.refreshPlaying(KLAutoPlayerManager.getInstance().getRadioId());
                    }
                }
            }

            @Override
            public void onPlayerFailed(PlayItem playItem, int i, int i1) {
                super.onPlayerFailed(playItem, i, i1);
                Log.d("mPlayerStateListener", "onPlayerFailed");
                Log.d("mPlayerStateListener", "playItem getTitle = " + playItem.getTitle());
                updateItemPlayingStateByPlayer(PlayerManagerHelper.getInstance().getCurPlayItem());
            }

            private boolean isValidStatus() {
                if (mState == STATE_SEARCH_RESULT) {
                    searchResultAdapter = (SearchResultAdapter) mRvSearchResults.getAdapter();
                    if (searchResultAdapter != null) {
                        return true;
                    }
                }
                return false;
            }
        };

        //for play fail
        generalListener = new IGeneralListener() {

            @Override
            public void getPlayListError(PlayItem playItem, int code, int i1) {
                if (code != PlayerConstants.ERROR_CODE_PLAY_LIST_IS_LAST_ONE) {
//                UIThreadUtil.runUIThread(() -> ToastUtil.showError(getContext(), R.string.play_failed_str));
                    updateItemPlayingStateByPlayer(PlayerManagerHelper.getInstance().getCurPlayItem());
                }
            }

            @Override
            public void playUrlError(int code) {
                if (!NetworkUtil.isNetworkAvailable(getContext(), false)) {
                    return;
                }
//            UIThreadUtil.runUIThread(() -> ToastUtil.showError(getContext(), R.string.play_failed_str));
                updateItemPlayingStateByPlayer(PlayerManagerHelper.getInstance().getCurPlayItem());
            }
        };
    }

    private void updateItemPlayingStateByPlayer(PlayItem playItem) {
        if (mSearchProgramBeans == null) {
            return;
        }
        //输入当前播放playitem，判断需要高亮的列表item。
        //playitem的type不能用来判断，因为碎片和电台，专辑都是0
        boolean isFindAudio = false;
        for (int i = 0; i < mSearchProgramBeans.size(); i++) {
            SearchProgramBeanResult item = mSearchProgramBeans.get(i);
            //列表item播放完毕切换下一个item
            if (playItem.getAudioId() == item.getId()) {
                if (!item.isPlaying) {
                    Log.d("mPlayerStateListener", " item id = " + item.getId() + " item type = " + item.getType());
                    updateItemPlayingState(i);
                    isFindAudio = true;
                }
            }
        }
        if (!isFindAudio) {
            for (int i = 0; i < mSearchProgramBeans.size(); i++) {
                SearchProgramBeanResult item = mSearchProgramBeans.get(i);
                long id = Long.parseLong(playItem.getRadioId());
                if (id == item.getId()) {
                    if (!item.isPlaying) {
                        Log.d("mPlayerStateListener", " item id = " + item.getId() + " item type = " + item.getType());
                        updateItemPlayingState(i);
                        isFindAudio = true;
                    }
                }
            }
        }
    }

    /**
     * 切换playitem时候，判断当前是否是碎片，如果是碎片，那么切换搜索列表下一个item，否则因为电台和专辑都有自己列表，所以不管
     */
    private void useCustomPlayList() {
        for (int i = 0; i < mSearchProgramBeans.size(); i++) {
            SearchProgramBeanResult item = mSearchProgramBeans.get(i);
            if (item.isPlaying && item.getType() == 1) {
                Log.d("mPlayerStateListener", "item type = " + item.getType());
                if ((i + 1) < mSearchProgramBeans.size()) {
                    SearchProgramBeanResult itemToPlay = mSearchProgramBeans.get(i + 1);
                    updateItemPlayingState(i + 1);
                    PlayerManagerHelper.getInstance().start(String.valueOf(itemToPlay.getId()), itemToPlay.getType());
                }
            }
        }
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mPresenter.searchClassifyAll();
    }

    @Override
    public void onResume() {
        super.onResume();
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001757409503?userId=1229522问题
//        showSoftKeyboard();

        if (isShowWindow) {
            isShowWindow = false;
            mTsSearchType.expand();
        }
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        mEtSearchWord.setTextSize(TypedValue.COMPLEX_UNIT_PX, ResUtil.getDimen(R.dimen.search_text_size));
        if (ResUtil.getOrientation() == Configuration.ORIENTATION_LANDSCAPE) {
            lineCount = 2;
            uploadView(true);
        } else {
            lineCount = 1;
            uploadView(false);
        }
        if (layoutManager != null && searchResultAdapter != null) {
            layoutManager.setSpanCount(lineCount);
//            mRvSearchResults.setLayoutManager(layoutManager);
            if (mRvSearchResults != null) {
                RecyclerView.Adapter adapter = mRvSearchResults.getAdapter();
                RecyclerView.LayoutManager manager = mRvSearchResults.getLayoutManager();
                mRvSearchResults.setAdapter(null);
                mRvSearchResults.setLayoutManager(null);
                mRvSearchResults.getRecycledViewPool().clear();
                mRvSearchResults.setLayoutManager(manager);
                mRvSearchResults.setAdapter(adapter);
            }
        }
        mTsSearchType.showAccordingToScreen(ResUtil.getOrientation());
    }

    @Override
    public void onDestroy() {
        if (searchResultAdapter != null) {
            searchResultAdapter.setLifeCycleNull();
        }
        super.onDestroy();
        NetworkMonitor.getInstance(getContext()).removeNetworkStatusChangeListener(mNetworkStatusChangedListener);
        PlayerManager.getInstance().removePlayControlStateCallback(mPlayerStateListener);
        PlayerManager.getInstance().removeGeneralListener(generalListener);
    }

    private TextView.OnEditorActionListener mEditActionListener = new TextView.OnEditorActionListener() {
        @Override
        public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
            String keyword = v.getText().toString();
            // 解决https://app.huoban.com/tables/2100000007530121/items/2300001665111238?userId=1229522问题
            //todo
            if (!TextUtils.isEmpty(keyword)) {
                hideSoftKeyboard();
            }
            searchByKeyword("1", keyword);
            return true;
        }
    };

    private TextWatcher mTextWatcher = new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {

        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            if (s.length() == 0) {
                ViewUtil.setViewVisibility(mIvSearchWordDelete, View.GONE);
            } else {
                ViewUtil.setViewVisibility(mIvSearchWordDelete, View.VISIBLE);
            }
        }

        @Override
        public void afterTextChanged(Editable s) {
            String word = s.toString();
            if (word.length() == 0) {
                mPresenter.cancelRequest();
                if (mState == STATE_SEARCH_RESULT || mState == STATE_SEARCH_EXCEPTION || mState == STATE_SEARCH_LOADING) {
                    refreshSearchHistoryView();
                }
                switchState(STATE_SEARCH_HISTORY);
                return;
            }
            if (isSearchStarted) {
                isSearchStarted = false;
                return;
            }
            if (mPresenter != null) {
                mPresenter.getAssociateWords(word);
            }
        }
    };

    private NetworkMonitor.OnNetworkStatusChangedListener mNetworkStatusChangedListener = new NetworkMonitor.OnNetworkStatusChangedListener() {
        @Override
        public void onStatusChanged(int newStatus, int oldStatus) {
            if (oldStatus == NetworkMonitor.STATUS_NO_NETWORK) {
                if (mState == STATE_SEARCH_EXCEPTION) {
                    searchByKeyword("1", mEtSearchWord.getText().toString());
                }
                if (mTsSearchType.getState() == TypeSpinner.STATE_LOAD_FAILED) {
                    mPresenter.searchClassifyAll();
                    ;
                }
                if (isGetHotWordsFailed) {
                    getHotSearchWords();
                }
            }
        }
    };

    public void onViewClick(View view) {
        int id = view.getId();
        if (id == R.id.tv_search) {
            searchByKeyword("1", mEtSearchWord.getText().toString());
            return;
        }
        if (id == R.id.iv_search_word_delete) {
            mEtSearchWord.setText(Constants.BLANK_STR);
            return;
        }
        if (id == R.id.tv_clear_search_history) {
            if (!AntiShake.check(id)) {
                clearSearchHistory();
            }
            return;
        }
    }

    @Override
    public boolean onBackPressedSupport() {
        return manageBackEvent();
    }

    @Override
    public String getPageId() {
        return Constants.PAGE_ID_SEARCH;
    }

    @Override
    public void showAssociateWordsView(List<AssociateInfo> associateInfos) {
        AssociateAdapter associateAdapter = (AssociateAdapter) mRvAssociateList.getAdapter();
        if (associateAdapter == null) {
            associateAdapter = new AssociateAdapter();
            associateAdapter.setOnItemClickListener((View v, int viewType, AssociateInfo associateInfo, int position) -> {
                String keyword = associateInfo.getAssociateWord();
                isSearchStarted = true;
                if (!TextUtils.isEmpty(keyword)) {
                    mEtSearchWord.setText(keyword);
                    mEtSearchWord.setSelection(mEtSearchWord.getText().toString().trim().length());
                }
                searchByKeyword("3", keyword);
            });
            mRvAssociateList.setAdapter(associateAdapter);
        }
        associateAdapter.setDataList(associateInfos);
        switchState(STATE_SEARCH_ASSOCIATE);
    }

    @Override
    public void showSearchResultView(List<SearchProgramBean> searchProgramBeansOri) {
        mSearchProgramBeans = new ArrayList<>();
        for (SearchProgramBean result : searchProgramBeansOri) {
            SearchProgramBeanResult resultNew = new SearchProgramBeanResult(result);
            mSearchProgramBeans.add(resultNew);
        }
        int ori = ResUtil.getOrientation(); //获取屏幕方向
        if (ori == Configuration.ORIENTATION_LANDSCAPE) {
            //横屏
            lineCount = 2;
        } else if (ori == Configuration.ORIENTATION_PORTRAIT) {
            //竖屏
            lineCount = 1;
        }
        layoutManager.setSpanCount(lineCount);
        mRvSearchResults.setLayoutManager(layoutManager);
        searchResultAdapter = (SearchResultAdapter) mRvSearchResults.getAdapter();
        if (searchResultAdapter == null) {
            searchResultAdapter = new SearchResultAdapter(bindUntilEvent(FragmentEvent.DESTROY));
            finalSearchResultAdapter = searchResultAdapter;
            searchResultAdapter.setOnItemClickListener((View view, int viewType, SearchProgramBeanResult searchProgramBean, int position) -> {
                if (mSearchProgramBeans.get(position).isPlaying) {
                    return;
                }
                if (!NetworkUtil.isNetworkAvailable(getContext(), true)) {
                    return;
                }

                updateItemPlayingState(position);

                handleBroadcast(searchProgramBean);

                PlayerManagerHelper.getInstance().start(String.valueOf(searchProgramBean.getId()), searchProgramBean.getType());
                mPresenter.reportSearchResultPlayEvent(searchProgramBean, position);
                mPresenter.reportContentClickEvent(searchProgramBean, position);
            });
        }
        mRvSearchResults.setAdapter(searchResultAdapter);
        searchResultAdapter.setDataList(mSearchProgramBeans);
        RecyclerViewExposeUtil exposeUtil = new RecyclerViewExposeUtil();
        exposeUtil.setRecyclerItemExposeListener(mRvSearchResults, this);
        switchState(STATE_SEARCH_RESULT);
        isSearchStarted = false;
        updateItemPlayingStateByPlayer(PlayerManagerHelper.getInstance().getCurPlayItem());
    }

    private void handleBroadcast(SearchProgramBeanResult searchProgramBean) {
        if (PlayerConstants.RESOURCES_TYPE_BROADCAST == searchProgramBean.getType()) {
            BroadcastRadioSimpleData data = new BroadcastRadioSimpleData();
            data.setBroadcastId(searchProgramBean.getId());
            data.setImg(searchProgramBean.getImg());
            String name = searchProgramBean.getAlbumName();
            data.setResType(PlayerConstants.RESOURCES_TYPE_BROADCAST);
            data.setName(name);
            PlayerManagerHelper.getInstance().addBroadcastRadioSimpleItem(data);
        }else if (PlayerConstants.RESOURCES_TYPE_TV == searchProgramBean.getType()){
            BroadcastRadioSimpleData data = new BroadcastRadioSimpleData();
            data.setBroadcastId(searchProgramBean.getId());
            data.setImg(searchProgramBean.getImg());
            String name = searchProgramBean.getAlbumName();
            data.setName(name);
            data.setResType(PlayerConstants.RESOURCES_TYPE_TV);
            PlayerManagerHelper.getInstance().addBroadcastRadioSimpleItem(data);
        }
    }

    private void updateItemPlayingState(int position) {
        if (position >= 0 && position < mSearchProgramBeans.size()) {
            //update playing state
            //为了提高效率，只更新发生播放状态变化的两个item
            for (int i = 0; i < mSearchProgramBeans.size(); i++) {
                SearchProgramBeanResult result = mSearchProgramBeans.get(i);
                if (result.isPlaying) {
                    searchResultAdapter.notifyItemChanged(i);
                }
                result.isPlaying = false;
            }
        }
        mRvSearchResults.setAdapter(searchResultAdapter);
        searchResultAdapter.setDataList(mSearchProgramBeans);
        switchState(STATE_SEARCH_RESULT);
        isSearchStarted = false;
        mSearchProgramBeans.get(position).isPlaying = true;
        searchResultAdapter.notifyItemChanged(position);
    }

    SearchResultAdapter finalSearchResultAdapter;

    @Override
    public void showNoNetView() {
        showExceptionView(false, ResUtil.getString(R.string.no_net_work_str));
    }

    @Override
    public void showNoResultView() {
        showExceptionView(false, ResUtil.getString(R.string.search_no_result));
    }

    @Override
    public void showErrorView(int errorCode) {
        if (errorCode == 604) {
            showExceptionView(true, ResUtil.getString(R.string.home_network_timerout));
        } else {
            showExceptionView(true, ResUtil.getString(R.string.home_network_failed));
        }
    }

    @Override
    public void showHotSearchWordsView(List<String> hotWords) {
        if (hotWords == null) {
            isGetHotWordsFailed = true;
        }
        mHotWords = hotWords;
        if (mState == STATE_SEARCH_HISTORY) {
            ViewUtil.setViewVisibility(mTvHotSearchWords, View.VISIBLE);
        }
        SearchHistoryAdapter searchHistoryAdapter = (SearchHistoryAdapter) mRvHotSearchWords.getAdapter();
        if (searchHistoryAdapter == null) {
            searchHistoryAdapter = new SearchHistoryAdapter(ButtonExposureOrClickReportEvent.BUTTON_ID_SEARCH_HOT_KEYWORDS_ITEM);
            searchHistoryAdapter.setOnItemClickListener((View v, int viewType, String s, int position) -> {
                isSearchStarted = true;
                if (!TextUtils.isEmpty(s) && mEtSearchWord != null) {
                    mEtSearchWord.setText(s);
                    mEtSearchWord.setSelection(mEtSearchWord.getText().toString().trim().length());
                }
                searchByKeyword("2", s);
            });
            mRvHotSearchWords.setAdapter(searchHistoryAdapter);
        }
        searchHistoryAdapter.setDataList(hotWords);
    }

    @Override
    public void setTypeData(List<SearchType> searchTypes) {
        mTsSearchType.setData(searchTypes);
        if (searchTypes == null) {
            mTsSearchType.setState(TypeSpinner.STATE_LOAD_FAILED);
            return;
        }
    }

    @Override
    public void showSearchTypeNetworkError(String error) {
        mTsSearchType.showNetworkError(error);
    }

    @Override
    public void showNetworkError(String error, boolean clickToRetry) {
        isSearchStarted = false;
        if (mNetworkErrorView == null) {
            mNetworkErrorView = mVsSearchNetworkError.inflate();
        }
        ViewUtil.setViewVisibility(mNetworkErrorView, View.VISIBLE);

        // 改变默认的错误信息文本
        TextView tvNetworkNoSign = mNetworkErrorView.findViewById(R.id.tv_network_nosign);
        tvNetworkNoSign.setText(error);

        // 支持点击图标重试
        ImageView ivNetworkNoSign = mNetworkErrorView.findViewById(R.id.network_nosigin);
        if (clickToRetry) {
            ivNetworkNoSign.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    ViewUtil.setViewVisibility(mNetworkErrorView, View.GONE);
                    searchByKeyword("1", mEtSearchWord.getText().toString());
                }
            });
        } else {
            ivNetworkNoSign.setOnClickListener(null);
        }
        switchState(STATE_SEARCH_EXCEPTION);
    }

    @Override
    public void showHotRecommend(HotRecommend hotRecommend) {

    }

    @Override
    public void showLoadingView() {
        switchState(STATE_SEARCH_LOADING);
    }

    public void showExceptionView(boolean isNeedRefresh, String exceptionString) {
        isSearchStarted = false;
        if (mExceptionView == null) {
            mExceptionView = mVsSearchException.inflate();
            mTvException = mExceptionView.findViewById(R.id.tv_search_exception_message);
        }
        mTvException.setText(exceptionString);
        if (isNeedRefresh) {
            mTvException.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    searchByKeyword("1", mEtSearchWord.getText().toString());
                }
            });
        } else {
            mTvException.setOnClickListener(null);
        }
        switchState(STATE_SEARCH_EXCEPTION);
    }

    private void getHotSearchWords() {
        mPresenter.getHotSearchWords();
    }

    private void initSearchHistoryView() {
        FlexboxLayoutManager flexboxLayoutManager = new FlexboxLayoutManager(getContext());
        mRvSearchHistoryTags.setLayoutManager(flexboxLayoutManager);
        mRvSearchHistoryTags.addItemDecoration(new SearchItemDecoration(0, ResUtil.getDimen(R.dimen.x37),
                0, ResUtil.getDimen(R.dimen.y30)));
    }

    private void initHotSearchWordsView() {
        FlexboxLayoutManager flexboxLayoutManager = new FlexboxLayoutManager(getContext());
        mRvHotSearchWords.setLayoutManager(flexboxLayoutManager);
        mRvHotSearchWords.addItemDecoration(new SearchItemDecoration(0, ResUtil.getDimen(R.dimen.x37),
                0, ResUtil.getDimen(R.dimen.y30)));
    }

    private void initSearchAssociateView() {
        mRvAssociateList.setLayoutManager(new FlexboxLayoutManager(getContext()));
        mRvAssociateList.addItemDecoration(new SearchItemDecoration(0, ResUtil.getDimen(R.dimen.x60),
                0, ResUtil.getDimen(R.dimen.y39)));
    }

    private void initSearchResultView() {
        layoutManager = new GridLayoutManager(getContext(), lineCount, LinearLayoutManager.VERTICAL, false);
        mRvSearchResults.setLayoutManager(layoutManager);
        mRvSearchResults.addItemDecoration(new SearchGridItemDecoration(ResUtil.getDimen(R.dimen.x25), ResUtil.getDimen(R.dimen.y21)));
    }

    private void initSearchTypeView() {
        //todo
        mTsSearchType.setOnExpandListener(() -> {
            if (mTsSearchType.getState() == TypeSpinner.STATE_LOAD_FAILED) {
                mPresenter.searchClassifyAll();
            }
        });
        getActivity().getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING);
    }

    /**
     * 搜索
     *
     * @param searchWay 1.手动输入；2.点击历史记录；3.点击联想词；4.语音搜索
     * @param keyword   关键词
     */
    private void searchByKeyword(String searchWay, String keyword) {
        if (mPresenter == null) {
            return;
        }
        mPresenter.cancelRequest();
        if (TextUtils.isEmpty(keyword) || TextUtils.isEmpty(keyword.trim())) {
            ToastUtil.showInfo(getContext(), R.string.toast_plz_input_keyword);
            return;
        }
        String s = keyword.trim();
        if (null != mNetworkErrorView) {
            ViewUtil.setViewVisibility(mNetworkErrorView, View.GONE);
        }
        mPresenter.searchByKeyword(mTsSearchType.getType(), searchWay, s);
    }

    private void switchState(int state) {
        if (mState == state) {
            return;
        }
        showOrHideStateView(mState, false);
        mState = state;
        showOrHideStateView(mState, true);
    }

    private void showOrHideStateView(int state, boolean isShow) {
        switch (state) {
            case STATE_SEARCH_HISTORY:
                showOrHideHistoryView(isShow);
                break;
            case STATE_SEARCH_ASSOCIATE:
                showOrHideAssociateView(isShow);
                break;
            case STATE_SEARCH_RESULT:
                showOrHideResultView(isShow);
                break;
            case STATE_SEARCH_EXCEPTION:
                showOrHideExceptionView(isShow);
                break;
            case STATE_SEARCH_LOADING:
                showOrHideLoadingView(isShow);
                break;
            default:
        }
    }

    private void showOrHideHistoryView(boolean isShow) {
        //ViewUtil.setViewVisibility(mSearchHistoryGroup, isShow ? View.VISIBLE : View.GONE);
        showOrHideHotSearchView(isShow);
        List<String> tags = SearchHistoryManager.getInstance().getRecentSearchTags();
        if (ListUtil.isEmpty(tags) || !isShow) {
            ViewUtil.setViewVisibility(mTvClearSearchHistory, View.GONE);
            ViewUtil.setViewVisibility(mTvSearchHistory, View.GONE);
            ViewUtil.setViewVisibility(mRvSearchHistoryTags, View.GONE);
        } else {
            ViewUtil.setViewVisibility(mTvClearSearchHistory, View.VISIBLE);
            ViewUtil.setViewVisibility(mTvSearchHistory, View.VISIBLE);
            ViewUtil.setViewVisibility(mRvSearchHistoryTags, View.VISIBLE);
        }
        reportSearchPage(isShow);
    }

    private void showOrHideHotSearchView(boolean isShow) {
        if (isShow) {
            ViewUtil.setViewVisibility(mTvHotSearchWords, View.VISIBLE);
            ViewUtil.setViewVisibility(mRvHotSearchWords, View.VISIBLE);
        } else {
            ViewUtil.setViewVisibility(mTvHotSearchWords, View.GONE);
            ViewUtil.setViewVisibility(mRvHotSearchWords, View.GONE);
        }
    }

    private void showOrHideAssociateView(boolean isShow) {
        ViewUtil.setViewVisibility(mRvAssociateList, isShow ? View.VISIBLE : View.GONE);
    }

    private void showOrHideResultView(boolean isShow) {
        ViewUtil.setViewVisibility(mRvSearchResults, isShow ? View.VISIBLE : View.GONE);
        reportSearchResultPage(isShow);
    }

    private void showOrHideExceptionView(boolean isShow) {
        ViewUtil.setViewVisibility(mExceptionView, isShow ? View.VISIBLE : View.GONE);
    }

    private void showOrHideLoadingView(boolean isShow) {
        ViewUtil.setViewVisibility(mSearchLoadingView, isShow ? View.VISIBLE : View.GONE);
    }

    private void reportSearchPage(boolean isShow) {
        if (isShow) {
            ReportHelper.getInstance().setPage(Constants.PAGE_ID_SEARCH);
        }
    }

    private void reportSearchResultPage(boolean isShow) {
        if (isShow) {
            ReportHelper.getInstance().setPage(Constants.PAGE_ID_SEARCH_RESULT);
        }
    }

    private void showSoftKeyboard() {
        mEtSearchWord.setFocusable(true);
        mEtSearchWord.setFocusableInTouchMode(true);
        mEtSearchWord.requestFocus();
        mEtSearchWord.setCursorVisible(true);
        InputMethodManager imm = (InputMethodManager) getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
        imm.showSoftInput(mEtSearchWord, 0);
    }

    private void hideSoftKeyboard() {
        InputMethodManager imm = (InputMethodManager) getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
        imm.hideSoftInputFromWindow(mEtSearchWord.getWindowToken(), InputMethodManager.HIDE_NOT_ALWAYS);
    }

    private Boolean manageBackEvent() {
        if (mState == STATE_SEARCH_HISTORY || mState == STATE_SEARCH_EXCEPTION) {
            if (null != mNetworkErrorView) {
                ViewUtil.setViewVisibility(mNetworkErrorView, View.GONE);
            }
            pop();
        } else {
            mEtSearchWord.setText(Constants.BLANK_STR);
        }
        return true;
    }

    private void refreshSearchHistoryView() {
        SearchHistoryAdapter searchHistoryAdapter = (SearchHistoryAdapter) mRvSearchHistoryTags.getAdapter();
        if (searchHistoryAdapter == null) {
            searchHistoryAdapter = new SearchHistoryAdapter(ButtonExposureOrClickReportEvent.BUTTON_ID_SEARCH_PAGE_HISTORY_ITEM);
            searchHistoryAdapter.setOnItemClickListener((View v, int viewType, String s, int position) -> {
                isSearchStarted = true;
                if (!TextUtils.isEmpty(s) && mEtSearchWord != null) {
                    mEtSearchWord.setText(s);
                    mEtSearchWord.setSelection(mEtSearchWord.getText().toString().trim().length());
                }
                searchByKeyword("2", s);
            });
            mRvSearchHistoryTags.setAdapter(searchHistoryAdapter);
        }
        List<String> tags = SearchHistoryManager.getInstance().getRecentSearchTags();
        searchHistoryAdapter.setDataList(tags);
    }

    private void clearSearchHistory() {
        // 解决[DI4.0_3.5UI][功能缺陷] [规划院] 【云听】[概率100%】云听主界面---搜索，同时点击搜索框中的节目类型下拉按钮和清空记录按钮，节目类型弹框和清空记录弹框能同时存在
        boolean expanded = mTsSearchType.isExpanded();
        Log.i(TAG, "expanded:" + expanded);
        if (expanded) {
            return;
        }
        DialogFragment dialogFragment = new Dialogs.Builder()
                .setType(Dialogs.TYPE_2BTN)
                .setGravity(Gravity.CENTER)
                .setMessage(ResUtil.getString(R.string.are_you_sure_to_clear_your_search_history))
                .setOnPositiveListener(dialog -> {
                    SearchHistoryManager.getInstance().clearAllRecentSearchTags();
                    refreshSearchHistoryView();
                    showOrHideHistoryView(true);
                    dialog.dismiss();
                })
                .create();
        dialogFragment.show(getFragmentManager(), "clear_search");

    }

    private void uploadView(boolean isLand) {
        mEtSearchWord.setTextSize(TypedValue.COMPLEX_UNIT_PX, ResUtil.getDimen(R.dimen.search_text_size));
        ConstraintLayout.LayoutParams tsLayoutParams = (ConstraintLayout.LayoutParams) mTsSearchType.getLayoutParams();
        boolean isMutiWindow = MultiUtil.getMultiStatus();
        if (isMutiWindow && kRadioMultiWindowInter != null) {
            kRadioMultiWindowInter.doMutiSearchView(mTsSearchType, mEtSearchWord);
        }
        mTsSearchType.setLayoutParams(tsLayoutParams);
        mEtSearchWord.setHint(ResUtil.getString(R.string.search_hint));
        ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) mClSearchView.getLayoutParams();
        layoutParams.setMarginStart(ResUtil.getDimen(R.dimen.search_edit_left_margin));
        layoutParams.width = ResUtil.getDimen(R.dimen.search_edit_width);
        layoutParams.height = ResUtil.getDimen(R.dimen.comprehensive_search_edit_height);
        //更改分屏状态下宽度,所以需要在setLayoutParams方法之前调用
        if (isLand && isMutiWindow) {
            if (kRadioMultiWindowInter != null) {
                kRadioMultiWindowInter.doMultiSearchFragment(layoutParams);
            }
        }
        mClSearchView.setLayoutParams(layoutParams);

        ConstraintLayout.LayoutParams lp = (ConstraintLayout.LayoutParams) mTvSearch.getLayoutParams();
        lp.setMarginStart(ResUtil.getDimen(R.dimen.search_btn_left_margin));
        lp.width = ResUtil.getDimen(R.dimen.search_btn_width);
        lp.height = ResUtil.getDimen(R.dimen.search_btn_height);
        mTvSearch.setLayoutParams(lp);

        ConstraintLayout.LayoutParams backLp = (ConstraintLayout.LayoutParams) mIvSearchBack.getLayoutParams();
        backLp.setMarginStart(ResUtil.getDimen(R.dimen.back_left_margin));

        if (!isLand) {
            ConstraintLayout.LayoutParams histroyLp = (ConstraintLayout.LayoutParams) mTvSearchHistory.getLayoutParams();
            histroyLp.setMarginStart(ResUtil.getDimen(R.dimen.m24));
            ConstraintLayout.LayoutParams associateLp = (ConstraintLayout.LayoutParams) mRvAssociateList.getLayoutParams();
            associateLp.setMarginStart(ResUtil.getDimen(R.dimen.m24));
            ConstraintLayout.LayoutParams hotSearchLp = (ConstraintLayout.LayoutParams) mTvHotSearchWords.getLayoutParams();
            hotSearchLp.setMarginStart(ResUtil.getDimen(R.dimen.m24));
        } else {
            ConstraintLayout.LayoutParams histroyLp = (ConstraintLayout.LayoutParams) mTvSearchHistory.getLayoutParams();
            histroyLp.setMarginStart(1);
            ConstraintLayout.LayoutParams associateLp = (ConstraintLayout.LayoutParams) mRvAssociateList.getLayoutParams();
            associateLp.setMarginStart(1);
            ConstraintLayout.LayoutParams hotSearchLp = (ConstraintLayout.LayoutParams) mTvHotSearchWords.getLayoutParams();
            hotSearchLp.setMarginStart(1);
        }

        ConstraintSet set = new ConstraintSet();
        set.clone(mRootLayout);
        if (isLand) {
            setGuideline(set, mTopGuideLine, isLand);
            set.connect(mTvSearchHistory.getId(), ConstraintSet.LEFT, mClSearchView.getId(), ConstraintSet.LEFT);
            set.connect(mRvSearchHistoryTags.getId(), ConstraintSet.LEFT, mClSearchView.getId(), ConstraintSet.LEFT);
            set.connect(mRvAssociateList.getId(), ConstraintSet.LEFT, mClSearchView.getId(), ConstraintSet.LEFT);
            set.connect(mTvHotSearchWords.getId(), ConstraintSet.LEFT, mClSearchView.getId(), ConstraintSet.LEFT);
        } else {
            setGuideline(set, mTopGuideLine, isLand);
            set.connect(mTvSearchHistory.getId(), ConstraintSet.LEFT, mIvSearchBack.getId(), ConstraintSet.LEFT);
            set.connect(mRvSearchHistoryTags.getId(), ConstraintSet.LEFT, mTvSearchHistory.getId(), ConstraintSet.LEFT);
            set.connect(mRvAssociateList.getId(), ConstraintSet.LEFT, mIvSearchBack.getId(), ConstraintSet.LEFT);
            set.connect(mTvHotSearchWords.getId(), ConstraintSet.LEFT, mIvSearchBack.getId(), ConstraintSet.LEFT);
        }
        set.applyTo(mRootLayout);
    }

    public void setGuideline(ConstraintSet set, Guideline guideLine, boolean isLand) {
        if (isLand) {
            set.setGuidelinePercent(guideLine.getId(), ViewConstants.TITLE_LAND_PERCENT);
        } else {
            set.setGuidelinePercent(guideLine.getId(), 0.133f);
        }
    }

    private void initViewInner() {
        int mCurrentOrientation = ResUtil.getOrientation();
        if (mCurrentOrientation == Configuration.ORIENTATION_PORTRAIT) {
            uploadView(false);
        } else if (mCurrentOrientation == Configuration.ORIENTATION_LANDSCAPE) {
            uploadView(true);
        }
    }

    @Override
    public void onPause() {
        super.onPause();
//        hideSoftKeyboard();

        if (mTsSearchType.isWindowShow()) {
            isShowWindow = true;
            mTsSearchType.collapse();
        }
    }

    public boolean isReportFragment() {
        return true;
    }

    @Override
    public void onItemViewVisible(boolean visible, int position) {
        if (visible && searchResultAdapter != null) {
            SearchProgramBean bean = searchResultAdapter.getItemData(position);
            mPresenter.reportContentShowEvent(bean, position);
        }
    }
}
