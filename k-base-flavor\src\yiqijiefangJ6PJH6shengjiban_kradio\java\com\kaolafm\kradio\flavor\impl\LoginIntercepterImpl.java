package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.base.flavor.LoginIntercepter;
import com.kaolafm.kradio.lib.utils.ResUtil;

import java.lang.ref.WeakReference;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-09-01 11:27
 ******************************************/
public class LoginIntercepterImpl implements LoginIntercepter {
    private static final String TAG = "LoginIntercepterImpl";
    private MyRunnable myRunnable;

    public LoginIntercepterImpl() {
        myRunnable = new MyRunnable(this);
    }

    @Override
    public boolean intercept(Object... args) {
        boolean flag = !UserInfoManager.getInstance().isUserLogin();
        if (flag) {
            new Handler().postDelayed(myRunnable, 2000);
        }
        return flag;
    }

    @Override
    public View getInterceptView(Object... args) {
        Context context = (Context) args[0];
        LayoutInflater layoutInflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View parentView = layoutInflater.inflate(R.layout.faw_user_login_warning_layout, ((ViewGroup) args[1]), true);

        String content = ResUtil.getString(R.string.login_jump_warning_str);
        int index = content.lastIndexOf('\n');
        SpannableString spannableString = new SpannableString(content);

        int len = spannableString.length();
        spannableString.setSpan(new AbsoluteSizeSpan(ResUtil.getDimen(R.dimen.text_size5)), 0, index, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(ResUtil.getColor(R.color.colorWhite)), 0, index, Spannable.SPAN_INCLUSIVE_INCLUSIVE);

        spannableString.setSpan(new AbsoluteSizeSpan(ResUtil.getDimen(R.dimen.text_size1)), index, len, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(ResUtil.getColor(R.color.gray_c1_color)), index, len, Spannable.SPAN_INCLUSIVE_INCLUSIVE);

        ((TextView) parentView.findViewById(R.id.faw_user_login_textView)).setText(spannableString);
        return parentView;
    }

    private static class MyRunnable implements Runnable {
        private WeakReference<LoginIntercepterImpl> weakReference;

        public MyRunnable(LoginIntercepterImpl loginIntercepterImpl) {
            weakReference = new WeakReference<>(loginIntercepterImpl);
        }

        @Override
        public void run() {
            LoginIntercepterImpl loginIntercepterImpl = weakReference.get();
            Log.i(TAG, "run start loginIntercepterImpl = " + loginIntercepterImpl);
            if (loginIntercepterImpl != null) {
                loginIntercepterImpl.gotoLoginPage();
            }
        }
    }

    private void gotoLoginPage() {
        if (UserInfoManager.getInstance().isUserLogin()) {
            return;
        }
        Activity activity = AppManager.getInstance().getCurrentActivity();
        if (activity == null) {
            return;
        }
        Intent intent = new Intent();
        ComponentName component = new ComponentName(
                "com.aerozhonghuan.changchun_largescreen", // 解放行包名
                "com.aerozhonghuan.changchun_largescreen.LargeScreenActivity"); // 解放行要跳转到的页面
        intent.setComponent(component);
        intent.putExtra("key", "authorize");
        try {
            activity.startActivity(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
