package com.kaolafm.kradio.online.mine.login;

import android.content.res.Resources;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.kaolafm.kradio.common.base.BaseSkinAppCompatActivity;
import com.kaolafm.kradio.k_kaolafm.R; 
import com.kaolafm.kradio.lib.base.ui.BaseActivity;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.user.UserInfoManager;
import com.kaolafm.kradio.user.ui.LoginPresenter;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.LoginReportEvent;
 

/**
 * 登录页面
 * 蔡佳彬
 */
@Route(path = RouterConstance.ACTIVITY_URL_LOGIN)
public class OnlineLoginActivity extends BaseSkinAppCompatActivity {
    
    ImageView login_close_iv; 
    LinearLayout login_fl_ll; 
    LinearLayout login_fl_ll2; 
    LinearLayout login_fl_ll3; 
    RelativeLayout rootView;

    private OnlineAccountLoginFragment onlineAccountLoginFragment;
    private String remarks1 = "";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        remarks1 = getIntent().getExtras().getString("type");
    }

    @Override
    public void setTheme(@Nullable Resources.Theme theme) {
        super.setTheme(R.style.TranslucentTheme);
    }

    @Override
    public void setTheme(int resid) {
        super.setTheme(R.style.TranslucentTheme);
    }

    @Override
    protected LoginPresenter createPresenter() {
        return null;
    }

    @Override
    public int getLayoutId() {
        return R.layout.online_activity_login;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        login_close_iv=view.findViewById(R.id.login_close_iv);
        login_fl_ll=view.findViewById(R.id.login_fl_ll);
        login_fl_ll2=view.findViewById(R.id.login_fl_ll2);
        login_fl_ll3=view.findViewById(R.id.login_fl_ll3);
        rootView=view.findViewById(R.id.rootView);
        
        
        if (UserInfoManager.getInstance().isUserLogin()) {
            //退出登录
            login_close_iv.setVisibility(View.GONE);
            login_fl_ll.setVisibility(View.GONE);
            login_fl_ll3.setVisibility(View.VISIBLE);
            OnlineLoginOutFragment outFragment = new OnlineLoginOutFragment();
            loadRootFragment(R.id.login_out_fl,
                    outFragment,
                    false, false);
        } else {
            login_close_iv.setVisibility(View.VISIBLE);
            login_close_iv.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    UserInfoManager.getInstance().setQrCodeStatus(-1);
                    finish();
                }
            });
            //登录
            onlineAccountLoginFragment = new OnlineAccountLoginFragment();
            loadRootFragment(R.id.login_qr_fl,
                    onlineAccountLoginFragment,
                    false, false);

            loadRootFragment(R.id.login_fl,
                    new OnlineLoginFragment(),
                    false, false);
        }
//        rootView.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                finish();
//            }
//        });

    }

    @Override
    public void initData() {

    }

    public void showBindSuccess(String avatar, String name) {
        loadRootFragment(R.id.login_qr_confirm_fl,
                OnlineUserConfirmFragment.newInstance(avatar, name),
                false, false);
        login_fl_ll.setVisibility(View.INVISIBLE);
        login_fl_ll2.setVisibility(View.VISIBLE);
    }

    public void hideBindSuccess() {
        UserInfoManager.getInstance().setQrCodeStatus(-1);
        if (onlineAccountLoginFragment != null) {
            onlineAccountLoginFragment.onSupportInvisible();
        }
        login_fl_ll.setVisibility(View.VISIBLE);
        login_fl_ll2.setVisibility(View.GONE);
        if (onlineAccountLoginFragment != null) {
            onlineAccountLoginFragment.onSupportVisible();
        }
    }

//    @Override
//    public String getPageId() {
//        return Constants.ONLINE_PAGE_ID_LOGIN;
//    }

    public void reportLoginEvent() {
        LoginReportEvent event = new LoginReportEvent();
        event.setType(UserInfoManager.getInstance().getLoginType());
        event.setRemarks1(remarks1);
        if (!TextUtils.isEmpty(remarks1)) {
            event.setPage(remarks1);
        }

        ReportHelper.getInstance().addEvent(event);
    }
}