package com.kaolafm.kradio.flavor.impl;

import android.util.Log;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioIsKaolaRunningInter;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-12-10 16:53
 ******************************************/
public class KRadioIsKaolaRunningImpl implements KRadioIsKaolaRunningInter {
    private static final String TAG = "KRadioIsKaolaRunningImpl";

    @Override
    public boolean setIsKaolaRunning(Object... args) {
        Boolean flag = (Boolean) args[0];
        Log.i(TAG, "setIsKaolaRunning--->flag = " + flag);
        AppDelegate.getInstance().setKaolaRuning(flag);
        return true;
    }
}
