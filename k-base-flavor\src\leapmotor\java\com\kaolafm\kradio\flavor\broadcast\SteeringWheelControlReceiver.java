package com.kaolafm.kradio.flavor.broadcast;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.kaolafm.kradio.player.helper.PlayerManagerHelper;

/**
 * 广播action：" com.leapmotor.action.METER.CTRL"
 * 方向盘控制指令StringExtraName：" value"
 * <p>
 * 方向盘控制指令value中的值：“1”：上一曲；“2”：下一曲
 * 注意事项:
 * 对接方在接收到方向盘上一曲或者下一曲按键事件广播之后，需要先判断自身媒体是否正在播放中，仅有当前媒体处于播放中的时候才需要处理方向盘上一曲或者下一曲的事件逻辑，否则不做任何处理。
 */
public class SteeringWheelControlReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        if ("com.leapmotor.action.METER.CTRL".equals(action)) {
            String extra = intent.getStringExtra("value");
            if (PlayerManagerHelper.getInstance().isPlaying()) {
                if ("1".equals(extra)) {
                    PlayerManagerHelper.getInstance().playPre(true);
                } else if ("2".equals(extra)) {
                    PlayerManagerHelper.getInstance().playPre(false);
                }
            }
        }
    }
}
