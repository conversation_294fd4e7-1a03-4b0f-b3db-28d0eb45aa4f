package com.kaolafm.kradio.lib.basedb.manager;

import android.os.SystemClock;

import com.kaolafm.kradio.lib.basedb.GreenDaoInterface;
import com.kaolafm.kradio.lib.basedb.greendao.HistoryItemDao;
import com.kaolafm.kradio.lib.basedb.greendao.HistoryItemDao.Properties;
import com.kaolafm.kradio.lib.bean.HistoryItem;
import com.kaolafm.kradio.lib.utils.HistoryBeanUtil;
import com.kaolafm.kradio.lib.utils.YTLogUtil;
import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import java.util.List;
import org.greenrobot.greendao.Property;

/**
 * 未登录时。收听历史数据库管理类。
 * Created by kaolafm on 2019/1/8.
 */

public class HistoryDaoManager extends BaseHistoryDaoManager<HistoryItem, HistoryItemDao> {

    /**
     * 数据最大数量
     */
    private static final int MAX_HISTORY_NUM = 99;

    private static final class HistoryDaoManagerHolder {

        private static final HistoryDaoManager INSTANCE = new HistoryDaoManager();
    }

    public static HistoryDaoManager getInstance() {
        return HistoryDaoManagerHolder.INSTANCE;
    }

    private HistoryDaoManager() {
        super();
    }

    @Override
    public void progress(PlayItem playItem, long progress) {
        //目前未登录时本地没有更新进度需求。
//        KRadioSaveHistoryInter inter = ClazzImplUtil.getInter("KRadioSaveHistoryImpl");
//        if(inter != null && inter.isSaveProgressHistory()) {
        if (needSaveHistory()) {
            saveHistory(playItem, false);
        }
//        }
    }

    private long lastSaveHistoryTime = -1;

    private boolean needSaveHistory() {
        long curT = SystemClock.elapsedRealtime();
        if (curT - lastSaveHistoryTime >= 3000) {
            lastSaveHistoryTime = curT;
            return true;
        }
        return false;
    }

    @Override
    public void getHistoryList(HttpCallback<List<HistoryItem>> callback) {
        YTLogUtil.logStart("HistoryDaoManager", "getHistoryList", "");
        queryHistoryListOrderByTime(historyItemList -> {
            if (callback != null){
                if (historyItemList != null) {
                    callback.onSuccess(historyItemList);
                }else {
                    callback.onError(new ApiException("历史为空"));
                }
            }
        });
    }

    public void queryHistoryListOrderByTime(GreenDaoInterface.OnQueryListener<List<HistoryItem>> listener) {
        queryHistoryListOrderByTime(MAX_HISTORY_NUM, listener);
    }

    public void queryHistoryListOrderByTime(int count, GreenDaoInterface.OnQueryListener<List<HistoryItem>> listener) {
        runInNewThread(() -> queryHistoryListOrderByTime(count), listener);
    }

    @Override
    public List<HistoryItem> getBroadcastList() {
        return getBroadcastList(Properties.Type.eq(ResType.TYPE_BROADCAST));
    }

    @Override
    public void queryLatestHistoryByAlbumId(String albumId, GreenDaoInterface.OnQueryListener<HistoryItem> listener) {
        queryLatestHistoryByAlbumId(Properties.RadioId.eq(albumId), listener);
    }

    @Override
    public void destroy() {

    }

    @Override
    HistoryItem toHistoryItem(HistoryItem historyItem) {
        return historyItem;
    }

    @Override
    List<HistoryItem> toHistoryItemList(List<HistoryItem> list) {
        return list;
    }

    @Override
    HistoryItem toHistoryEntity(PlayItem playItem) {
        HistoryItem historyItem = new HistoryItem();
        HistoryBeanUtil.translateToHistoryItem(playItem, historyItem);
        return historyItem;
    }

    @Override
    protected HistoryItemDao getDao() {
        return mDaoSession.getHistoryItemDao();
    }

    @Override
    Property getTimestamp() {
        return Properties.TimeStamp;
    }

    public List<HistoryItem> queryHistoryListSync() {
        return mDao.queryBuilder().orderDesc(getTimestamp())
                .where(Properties.Type.notEq(String.valueOf(PlayerConstants.RESOURCES_TYPE_BROADCAST)))
                .limit(MAX_HISTORY_NUM)
                .build().list();
    }

}
