package com.kaolafm.kradio.online.player.mvp;

import com.kaolafm.kradio.common.ErrorInfo;
import com.kaolafm.kradio.common.ResultCallback;

import java.lang.ref.WeakReference;

/**
 * <AUTHOR>
 */
public abstract class OnlineSubscribeCallback implements ResultCallback {
    protected WeakReference<OnlinePlayerBasePresenter> weakReference;

    public OnlineSubscribeCallback(OnlinePlayerBasePresenter playerPresenter) {
        weakReference = new WeakReference<>(playerPresenter);
    }

    @Override
    public abstract void onResult(boolean result, int status);

    @Override
    public abstract void onFailure(ErrorInfo errorInfo);
}
