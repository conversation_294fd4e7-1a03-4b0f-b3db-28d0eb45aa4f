package com.kaolafm.kradio.component.ui.ring.view;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.os.Handler;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.view.GestureDetector;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;

import com.kaolafm.kradio.component.ui.R;
import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.lib.BuildConfig;
import com.kaolafm.kradio.lib.common.ResType;
import com.kaolafm.kradio.lib.report.ReportParamUtil;
import com.kaolafm.kradio.lib.report.ReportUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.lib.utils.ScrollTouchHelper;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.InvalidPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.TempTaskPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.HomeScrollReportEvent;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> shiqian
 * @date 2022-07-14
 */
public class Ring extends RelativeLayout implements GestureDetector.OnGestureListener, View.OnClickListener, View.OnTouchListener {
    private String TAG = "Ring";

    private GestureDetector detector;
    private RingItem point0, point1, point2;
    private Map<Integer, RingItem> maps;

    /**
     * RingItem选中的监听
     */
    private OnSelectListener mOnSelectListener;

    /**
     * 光圈对播放器播放状态的监听
     */
    private RingPlayerStateListener mRingPlayerStateListener;
    private RingPlayerListStateListener mRingPlayerListStateListener;

    private Handler mHandler = new Handler();

    /**
     * 选中RingItem的position（0，1，2）
     */
    private int selectedPosition = 0;

    /**
     * 上一次选中RingItem的position（0，1，2）
     */
    private int oldSelectedPosition = -1;

    /**
     * 手势滑动动画是否在执行
     */
    private boolean isSlideAnimPlaying = false;

    /**
     * 动画缩放比例
     */
    private float mAnimScaleValue = 0.72f;

    /**
     * 动画透明度
     */
    private float mAnimAlphaValue = 1.0f;

    /**
     * 动画X轴平移的距离
     */
    private float mAnimTranslationX = 0;

    /**
     * 开屏动画持续的时长
     */
    private long mSplashAnimDuration = BuildConfig.IS_PLAY_ANIM ? 1250 : 0;

    /**
     * 手势滑动动画持续的时长
     */
    private long mSlideAnimDuration = 1000;

    /**
     * 滑动开始后，延后多长时间进行数据替换
     */
    private long mDataDelayDuration = mSlideAnimDuration / 2;

    /**
     * 光圈直径
     */
    private int mRingItemDiameter = 0;

    /**
     * 光圈是否可以响应手势事件
     */
    private boolean mTouchEnable = true;

    /**
     * 光圈是否在更新数据
     */
    private boolean mIsUpdating = true;

    /**
     * 是否首次执行onLayout()方法
     */
    private boolean isFirstLayout = true;
    private boolean isFirstExecute = true;

    private int slideMode = -1;//(slideLeft：1，slideRight：2)
    private ScrollTouchHelper.TouchEvent touchEvent;
    private long oldIndex = -1;//上次的index
    private long oldTime = -1;//上次的上报时间戳
    private String pageId = Constants.PAGE_ID_HOME_LIKE;


    public Ring(Context context) {
        this(context, null);
    }

    public Ring(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public Ring(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        ScrollTouchHelper touchHelper = new ScrollTouchHelper();
        touchEvent = touchHelper.new TouchEvent();
        touchHelper.setEvent(touchEvent);
        init(context);
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent event) {
        // 处理拦截事件，此处会把事件向下分发，如果子view对事件进行了消费，那么此处将不会收到move和up事件
        int sw = ScreenUtil.getScreenWidth();
        int sh = ScreenUtil.getScreenHeight();
        touchEvent.setScreenWidth(sw);
        touchEvent.setScreenHeight(sh);
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                float rawX = event.getRawX();
                float rawY = event.getRawY();
                Log.i(TAG, "ACTION_DOWN: (" + rawX + "," + rawY + ")  / ( " + sw + " ," + sh + ")");
                touchEvent.setDownX((int) rawX);
                touchEvent.setDownY((int) rawY);
                break;
        }
        return super.onInterceptTouchEvent(event);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        // 获取宽-测量规则的模式和大小
        int widthMode = MeasureSpec.getMode(widthMeasureSpec);
        int widthSize = MeasureSpec.getSize(widthMeasureSpec);

        // 获取高-测量规则的模式和大小
        int heightMode = MeasureSpec.getMode(heightMeasureSpec);
        int heightSize = MeasureSpec.getSize(heightMeasureSpec);

        mRingItemDiameter = heightSize;
        mAnimTranslationX = (float) (heightSize * 0.6);
    }

    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        super.onLayout(changed, l, t, r, b);

        if (isFirstLayout) {
            Set set = maps.keySet();
            for (Object o : set) {
                ViewGroup.LayoutParams layoutParams = ((RingItem) maps.get(o)).getLayoutParams();
                layoutParams.width = mRingItemDiameter;
                layoutParams.height = mRingItemDiameter;
                ((RingItem) maps.get(o)).setLayoutParams(layoutParams);
            }
            isFirstLayout = false;
        }
    }

    public boolean isAnimPlaying() {
        return isSlideAnimPlaying;
    }

    /**
     * 设置是否可以滑动
     *
     * @param touchEnable
     */
    public void setTouchEnable(boolean touchEnable) {
        mTouchEnable = touchEnable;
    }

    public boolean getTouchEnable() {
        return mTouchEnable;
    }


    public void onSelectChild(int position) {
        Set set = maps.keySet();
        for (Object o : set) {
            ((RingItem) maps.get(o)).onSelectChanged(((Integer) o).intValue() == position);
        }
    }

    /**
     * 执行动画
     */
    public void playAnim() {
        Set set = maps.keySet();
        for (Object o : set) {
            ((RingItem) maps.get(o)).playAnim();
        }
    }

    /**
     * 取消动画
     */
    public void pauseAnim() {
        Set set = maps.keySet();
        for (Object o : set) {
            ((RingItem) maps.get(o)).pauseAnim();
        }
    }

    public void setPageId(String pageId) {
        this.pageId = pageId;
    }

    /**
     * 初始化view
     *
     * @param context
     */
    void init(Context context) {
        // 加载布局
        LayoutInflater.from(context).inflate(R.layout.ring_layout, this);
        point0 = findViewById(R.id.point_0);
        point1 = findViewById(R.id.point_1);
        point2 = findViewById(R.id.point_2);
        maps = new HashMap<>();
        maps.put(0, point0);
        maps.put(1, point1);
        maps.put(2, point2);
        onViewSelectd(0);
//        maps.get(0).setDataIndex(0);
        maps.get(0).setOnClickListener(this);
        maps.get(1).setOnClickListener(this);
        maps.get(2).setOnClickListener(this);
        maps.get(0).setOnTouchListener(this);
        maps.get(1).setOnTouchListener(this);
        maps.get(2).setOnTouchListener(this);
        // 创建手势检测器
        detector = new GestureDetector(getContext(), this);
        mRingPlayerStateListener = new RingPlayerStateListener(this);
        mRingPlayerListStateListener = new RingPlayerListStateListener(this);
        PlayerManager.getInstance().addPlayControlStateCallback(mRingPlayerStateListener);
        PlayerManager.getInstance().addPlayListControlStateCallback(mRingPlayerListStateListener);
    }

    /**
     * 展开开屏动画
     */
    public void unFoldSplashAnim() {
        AnimatorSet mAnimatorSet = new AnimatorSet();
        ObjectAnimator mObjectAnimatorX1 = ObjectAnimator.ofFloat(maps.get(1), "translationX", 0, mAnimTranslationX);
        ObjectAnimator mObjectAnimatorScaleX1 = ObjectAnimator.ofFloat(maps.get(1), "scaleX", 1.0f, mAnimScaleValue);
        ObjectAnimator mObjectAnimatorScaleY1 = ObjectAnimator.ofFloat(maps.get(1), "scaleY", 1.0f, mAnimScaleValue);
        ObjectAnimator mObjectAnimatorAlpha1 = ObjectAnimator.ofFloat(maps.get(1), "alpha", 1.0f, mAnimAlphaValue);

        ObjectAnimator mObjectAnimatorX2 = ObjectAnimator.ofFloat(maps.get(2), "translationX", 0, -mAnimTranslationX);
        ObjectAnimator mObjectAnimatorScaleX2 = ObjectAnimator.ofFloat(maps.get(2), "scaleX", 1.0f, mAnimScaleValue);
        ObjectAnimator mObjectAnimatorScaleY2 = ObjectAnimator.ofFloat(maps.get(2), "scaleY", 1.0f, mAnimScaleValue);
        ObjectAnimator mObjectAnimatorAlpha2 = ObjectAnimator.ofFloat(maps.get(2), "alpha", 1.0f, mAnimAlphaValue);


        mAnimatorSet.playTogether(mObjectAnimatorX1, mObjectAnimatorScaleX1, mObjectAnimatorScaleY1, mObjectAnimatorAlpha1,
                mObjectAnimatorX2, mObjectAnimatorScaleX2, mObjectAnimatorScaleY2, mObjectAnimatorAlpha2);
        mAnimatorSet.setDuration(mSplashAnimDuration);
        mAnimatorSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
                isSlideAnimPlaying = true;
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                isSlideAnimPlaying = false;
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                isSlideAnimPlaying = false;
            }

            @Override
            public void onAnimationRepeat(Animator animation) {
                isSlideAnimPlaying = true;
            }
        });
        mAnimatorSet.start();
//        mHandler.postDelayed(new Runnable() {
//            @Override
//            public void run() {
//                setTouchEnable(originTouchEnable);
//                isSlideAnimPlaying = false;
//            }
//        }, mSplashAnimDuration);
    }

    /**
     * 收起开屏动画
     */
    public void foldSplashAnim() {
        AnimatorSet mAnimatorSet = new AnimatorSet();
        ObjectAnimator mObjectAnimatorX1 = ObjectAnimator.ofFloat(maps.get(1), "translationX", 0, mAnimTranslationX);
        ObjectAnimator mObjectAnimatorScaleX1 = ObjectAnimator.ofFloat(maps.get(1), "scaleX", 1.0f, mAnimScaleValue);
        ObjectAnimator mObjectAnimatorScaleY1 = ObjectAnimator.ofFloat(maps.get(1), "scaleY", 1.0f, mAnimScaleValue);
        ObjectAnimator mObjectAnimatorAlpha1 = ObjectAnimator.ofFloat(maps.get(1), "alpha", 1.0f, mAnimAlphaValue);

        ObjectAnimator mObjectAnimatorX2 = ObjectAnimator.ofFloat(maps.get(2), "translationX", 0, -mAnimTranslationX);
        ObjectAnimator mObjectAnimatorScaleX2 = ObjectAnimator.ofFloat(maps.get(2), "scaleX", 1.0f, mAnimScaleValue);
        ObjectAnimator mObjectAnimatorScaleY2 = ObjectAnimator.ofFloat(maps.get(2), "scaleY", 1.0f, mAnimScaleValue);
        ObjectAnimator mObjectAnimatorAlpha2 = ObjectAnimator.ofFloat(maps.get(2), "alpha", 1.0f, mAnimAlphaValue);


        mAnimatorSet.playTogether(mObjectAnimatorX1, mObjectAnimatorScaleX1, mObjectAnimatorScaleY1, mObjectAnimatorAlpha1,
                mObjectAnimatorX2, mObjectAnimatorScaleX2, mObjectAnimatorScaleY2, mObjectAnimatorAlpha2);
        mAnimatorSet.setDuration(mSplashAnimDuration);
        mAnimatorSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
                isSlideAnimPlaying = true;
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                isSlideAnimPlaying = false;
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                isSlideAnimPlaying = false;
            }

            @Override
            public void onAnimationRepeat(Animator animation) {
                isSlideAnimPlaying = true;
            }
        });
        mAnimatorSet.start();
    }

    public void setTranslationX(float translationX) {
        maps.get(0).setTranslationX(translationX);
        maps.get(1).setTranslationX(translationX);
        maps.get(2).setTranslationX(translationX);
    }

    /**
     * 平移动画
     */
    public void translationAnim(float translationX, int duration) {
        AnimatorSet mAnimatorSet = new AnimatorSet();
        ObjectAnimator mObjectAnimatorX1 = ObjectAnimator.ofFloat(maps.get(0), "translationX", 0, translationX);
        ObjectAnimator mObjectAnimatorX2 = ObjectAnimator.ofFloat(maps.get(1), "translationX", 0, translationX);
        ObjectAnimator mObjectAnimatorX3 = ObjectAnimator.ofFloat(maps.get(2), "translationX", 0, translationX);
        mAnimatorSet.playTogether(mObjectAnimatorX1, mObjectAnimatorX2, mObjectAnimatorX3);
        mAnimatorSet.setDuration(duration);
        mAnimatorSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
                isSlideAnimPlaying = true;
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                isSlideAnimPlaying = false;
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                isSlideAnimPlaying = false;
            }

            @Override
            public void onAnimationRepeat(Animator animation) {
                isSlideAnimPlaying = true;
            }
        });
        mAnimatorSet.start();
    }

    /**
     * 隐藏非选中光圈
     */
    public void hideUnselectItem() {
        maps.get((selectedPosition + 2) % 3).setVisibility(GONE);
        maps.get((selectedPosition + 1) % 3).setVisibility(GONE);
    }

    /**
     * 展示非选中光圈
     */
    public void showUnselectItem() {
        maps.get((selectedPosition + 2) % 3).setVisibility(VISIBLE);
        maps.get((selectedPosition + 1) % 3).setVisibility(VISIBLE);
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == maps.get((selectedPosition + 1) % 3).getId()) {// 点击右边view
            slideLeft();
        } else if (v.getId() == maps.get((selectedPosition + 2) % 3).getId()) {// 点击左边view
            slideRight();
        }
    }

    @Override
    public boolean onTouch(View v, MotionEvent event) {
        // Toast.makeText(this, "onTouchEvent", Toast.LENGTH_SHORT).show();
        return detector.onTouchEvent(event);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        // Toast.makeText(this, "onTouchEvent", Toast.LENGTH_SHORT).show();
        return detector.onTouchEvent(event);
    }

    @Override
    public boolean onDown(MotionEvent e) {
        return false;
    }

    @Override
    public void onShowPress(MotionEvent e) {

    }

    @Override
    public boolean onSingleTapUp(MotionEvent e) {
        return false;
    }

    @Override
    public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) {
        return false;
    }

    @Override
    public void onLongPress(MotionEvent e) {

    }

    @Override
    public boolean onFling(MotionEvent e1, MotionEvent e2, float velocityX, float velocityY) {
        // Toast.makeText(this, "onFling", Toast.LENGTH_SHORT).show();
        float minMove = 120; // 最小滑动距离
        float minVelocity = 0; // 最小滑动速度
        float beginX = e1.getX();
        float endX = e2.getX();
        float beginY = e1.getY();
        float endY = e2.getY();

        if (beginX - endX > minMove && Math.abs(velocityX) > minVelocity) { // 左滑
//                slideLeft(velocityX);
            slideLeft();
        } else if (endX - beginX > minMove && Math.abs(velocityX) > minVelocity) { // 右滑
//                slideRight(velocityX);
            slideRight();
        } else if (beginY - endY > minMove && Math.abs(velocityY) > minVelocity) { // 上滑
            slideUp(velocityY);
        } else if (endY - beginY > minMove && Math.abs(velocityY) > minVelocity) { // 下滑
            slideDown(velocityY);
        }
        float rawX = endX;
        float rawY = endY;
        touchEvent.setUpX((int) rawX);
        touchEvent.setUpY((int) rawY);
        //至此，一个上报事件产生
        HomeScrollReportEvent homeScrollReportEvent = new HomeScrollReportEvent();
        homeScrollReportEvent.setDownPointer(touchEvent.getDownX(), touchEvent.getDownY());
        homeScrollReportEvent.setUpPointer(touchEvent.getUpX(), touchEvent.getUpY());
        homeScrollReportEvent.setScreenSize(touchEvent.getScreenWidth(), touchEvent.getScreenHeight());
        Log.i(TAG, "generate HomeScrollReportEvent:" + homeScrollReportEvent.toString());
        ReportHelper.getInstance().addEvent(homeScrollReportEvent);
        // Toast.makeText(this, "selectedPosition is "+ selectedPosition +"\noldSelectedPosition is "+ oldSelectedPosition, // Toast.LENGTH_SHORT).show();
        return false;
    }

    /**
     * 向左滑动，或者点击右侧view
     */
    void slideLeft() {
        if (isSlideAnimPlaying || mIsUpdating || !mTouchEnable) {
            return;
        }
        slideMode = 1;

        // Toast.makeText(this, velocityX + "左滑", // Toast.LENGTH_SHORT).show();
        // 最左侧view数据+1
        oldSelectedPosition = selectedPosition;
        selectedPosition = (selectedPosition + 1) % 3;

        onViewSelectd(selectedPosition);

        AnimatorSet mAnimatorSet = new AnimatorSet();
        ObjectAnimator mObjectAnimatorX1 = ObjectAnimator.ofFloat(maps.get(oldSelectedPosition), "x", maps.get(oldSelectedPosition).getX(), maps.get((oldSelectedPosition + 2) % 3).getX());
        ObjectAnimator mObjectAnimatorY1 = ObjectAnimator.ofFloat(maps.get(oldSelectedPosition), "y", maps.get(oldSelectedPosition).getY(), maps.get((oldSelectedPosition + 2) % 3).getY());
        ObjectAnimator mObjectAnimatorScaleX1 = ObjectAnimator.ofFloat(maps.get(oldSelectedPosition), "scaleX", 1.0f, mAnimScaleValue);
        ObjectAnimator mObjectAnimatorScaleY1 = ObjectAnimator.ofFloat(maps.get(oldSelectedPosition), "scaleY", 1.0f, mAnimScaleValue);
        ObjectAnimator mObjectAnimatorAloha1 = ObjectAnimator.ofFloat(maps.get(oldSelectedPosition), "alpha", 1.0f, mAnimAlphaValue);

        ObjectAnimator mObjectAnimatorX2 = ObjectAnimator.ofFloat(maps.get((oldSelectedPosition + 2) % 3), "x", maps.get((oldSelectedPosition + 2) % 3).getX(), maps.get((oldSelectedPosition + 1) % 3).getX());
        ObjectAnimator mObjectAnimatorY2 = ObjectAnimator.ofFloat(maps.get((oldSelectedPosition + 2) % 3), "y", maps.get((oldSelectedPosition + 2) % 3).getY(), maps.get((oldSelectedPosition + 1) % 3).getY());

        ObjectAnimator mObjectAnimatorX3 = ObjectAnimator.ofFloat(maps.get((oldSelectedPosition + 1) % 3), "x", maps.get((oldSelectedPosition + 1) % 3).getX(), maps.get(oldSelectedPosition).getX());
        ObjectAnimator mObjectAnimatorY3 = ObjectAnimator.ofFloat(maps.get((oldSelectedPosition + 1) % 3), "y", maps.get((oldSelectedPosition + 1) % 3).getY(), maps.get(oldSelectedPosition).getY());
        ObjectAnimator mObjectAnimatorScaleX3 = ObjectAnimator.ofFloat(maps.get((oldSelectedPosition + 1) % 3), "scaleX", mAnimScaleValue, 1.0f);
        ObjectAnimator mObjectAnimatorScaleY3 = ObjectAnimator.ofFloat(maps.get((oldSelectedPosition + 1) % 3), "scaleY", mAnimScaleValue, 1.0f);
        ObjectAnimator mObjectAnimatorAlpha3 = ObjectAnimator.ofFloat(maps.get((oldSelectedPosition + 1) % 3), "alpha", mAnimAlphaValue, 1.0f);

        mAnimatorSet.playTogether(mObjectAnimatorX1, mObjectAnimatorY1, mObjectAnimatorScaleX1, mObjectAnimatorScaleY1, mObjectAnimatorAloha1,
                mObjectAnimatorX2, mObjectAnimatorY2,
                mObjectAnimatorX3, mObjectAnimatorY3, mObjectAnimatorScaleX3, mObjectAnimatorScaleY3, mObjectAnimatorAlpha3);
        mAnimatorSet.setDuration(mSlideAnimDuration);
        mAnimatorSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
                isSlideAnimPlaying = true;
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                isSlideAnimPlaying = false;
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                isSlideAnimPlaying = false;
            }

            @Override
            public void onAnimationRepeat(Animator animation) {
                isSlideAnimPlaying = true;
            }
        });
        mAnimatorSet.start();

//        RingManager.getInstance().deleteTempData();
        maps.get((oldSelectedPosition + 2) % 3).postDelayed(new Runnable() {
            @Override
            public void run() {
                maps.get((oldSelectedPosition + 2) % 3).setDataIndex((maps.get((oldSelectedPosition + 1) % 3).getDataIndex() + 1) % RingManager.getInstance().getDatas().size());
                maps.get((oldSelectedPosition + 2) % 3).setItem(RingManager.getInstance().getDatas().get(maps.get((oldSelectedPosition + 2) % 3).getDataIndex()));
            }
        }, mDataDelayDuration);
    }

    /**
     * 向右滑动，或者点击左侧view
     */
    void slideRight() {
        if (isSlideAnimPlaying || mIsUpdating || !mTouchEnable) {
            return;
        }
        slideMode = 2;

        // Toast.makeText(this, velocityX + "右滑", // Toast.LENGTH_SHORT).show();
        // 最右侧view数据-1
        oldSelectedPosition = selectedPosition;
        selectedPosition = (selectedPosition + 2) % 3;

        onViewSelectd(selectedPosition);

        AnimatorSet mAnimatorSet = new AnimatorSet();
        ObjectAnimator mObjectAnimatorX1 = ObjectAnimator.ofFloat(maps.get(oldSelectedPosition), "x", maps.get(oldSelectedPosition).getX(), maps.get((oldSelectedPosition + 1) % 3).getX());
        ObjectAnimator mObjectAnimatorY1 = ObjectAnimator.ofFloat(maps.get(oldSelectedPosition), "y", maps.get(oldSelectedPosition).getY(), maps.get((oldSelectedPosition + 1) % 3).getY());
        ObjectAnimator mObjectAnimatorScaleX1 = ObjectAnimator.ofFloat(maps.get(oldSelectedPosition), "scaleX", 1.0f, mAnimScaleValue);
        ObjectAnimator mObjectAnimatorScaleY1 = ObjectAnimator.ofFloat(maps.get(oldSelectedPosition), "scaleY", 1.0f, mAnimScaleValue);
        ObjectAnimator mObjectAnimatorAlpha1 = ObjectAnimator.ofFloat(maps.get(oldSelectedPosition), "alpha", 1.0f, mAnimAlphaValue);

        ObjectAnimator mObjectAnimatorX2 = ObjectAnimator.ofFloat(maps.get((oldSelectedPosition + 1) % 3), "x", maps.get((oldSelectedPosition + 1) % 3).getX(), maps.get((oldSelectedPosition + 2) % 3).getX());
        ObjectAnimator mObjectAnimatorY2 = ObjectAnimator.ofFloat(maps.get((oldSelectedPosition + 1) % 3), "y", maps.get((oldSelectedPosition + 1) % 3).getY(), maps.get((oldSelectedPosition + 2) % 3).getY());

        ObjectAnimator mObjectAnimatorX3 = ObjectAnimator.ofFloat(maps.get((oldSelectedPosition + 2) % 3), "x", maps.get((oldSelectedPosition + 2) % 3).getX(), maps.get(oldSelectedPosition).getX());
        ObjectAnimator mObjectAnimatorY3 = ObjectAnimator.ofFloat(maps.get((oldSelectedPosition + 2) % 3), "y", maps.get((oldSelectedPosition + 2) % 3).getY(), maps.get(oldSelectedPosition).getY());
        ObjectAnimator mObjectAnimatorScaleX3 = ObjectAnimator.ofFloat(maps.get((oldSelectedPosition + 2) % 3), "scaleX", mAnimScaleValue, 1.0f);
        ObjectAnimator mObjectAnimatorScaleY3 = ObjectAnimator.ofFloat(maps.get((oldSelectedPosition + 2) % 3), "scaleY", mAnimScaleValue, 1.0f);
        ObjectAnimator mObjectAnimatorAlpha3 = ObjectAnimator.ofFloat(maps.get((oldSelectedPosition + 2) % 3), "alpha", mAnimAlphaValue, 1.0f);

        mAnimatorSet.playTogether(mObjectAnimatorX1, mObjectAnimatorY1, mObjectAnimatorScaleX1, mObjectAnimatorScaleY1, mObjectAnimatorAlpha1,
                mObjectAnimatorX2, mObjectAnimatorY2,
                mObjectAnimatorX3, mObjectAnimatorY3, mObjectAnimatorScaleX3, mObjectAnimatorScaleY3, mObjectAnimatorAlpha3);
        mAnimatorSet.setDuration(mSlideAnimDuration);
        mAnimatorSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
                isSlideAnimPlaying = true;
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                isSlideAnimPlaying = false;
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                isSlideAnimPlaying = false;
            }

            @Override
            public void onAnimationRepeat(Animator animation) {
                isSlideAnimPlaying = true;
            }
        });
        mAnimatorSet.start();

//        RingManager.getInstance().deleteTempData();
        maps.get((oldSelectedPosition + 1) % 3).postDelayed(new Runnable() {
            @Override
            public void run() {
                maps.get((oldSelectedPosition + 1) % 3).setDataIndex((maps.get((oldSelectedPosition + 2) % 3).getDataIndex() + RingManager.getInstance().getDatas().size() - 1) % RingManager.getInstance().getDatas().size());
                maps.get((oldSelectedPosition + 1) % 3).setItem(RingManager.getInstance().getDatas().get(maps.get((oldSelectedPosition + 1) % 3).getDataIndex()));
            }
        }, mDataDelayDuration);
    }

    void slideUp(float velocityY) {
        // Toast.makeText(this, velocityY + "上滑", // Toast.LENGTH_SHORT).show();
    }

    void slideDown(float velocityY) {
        // Toast.makeText(this, velocityY + "下滑", // Toast.LENGTH_SHORT).show();
    }

    /**
     * view被选中
     *
     * @param selectedPosition
     */
    private void onViewSelectd(int selectedPosition) {
//        maps.get(selectedPosition).bringToFront();

        onSelectChild(selectedPosition);

        if (mOnSelectListener != null) {
            if (RingManager.getInstance().getDatas() == null
                    || RingManager.getInstance().getDatas().size() <= 0
                    || maps.get(selectedPosition) == null
                    || maps.get(selectedPosition).getDataIndex() < 0
                    || maps.get(selectedPosition).getDataIndex() >= RingManager.getInstance().getDatas().size()) {
                return;
            }
            mOnSelectListener.onSelect(maps.get(selectedPosition).getDataIndex());
            Log.d(TAG, "select dataIndex----" + maps.get(selectedPosition).getDataIndex());
            Log.d(TAG, "select homecell----" + RingManager.getInstance().getDatas().get(maps.get(selectedPosition).getDataIndex()).toString());
            Log.d(TAG, "select playId----" + RingManager.getInstance().getDatas().get(maps.get(selectedPosition).getDataIndex()).playId);
        }
    }

    public void setOnSelectListener(OnSelectListener onSelectLiistener) {
        mOnSelectListener = onSelectLiistener;
    }

    public interface OnSelectListener {
        void onSelect(int postion);
    }

    /**
     * view更新数据前先校验合法性
     *
     * @return
     */
    private boolean checkLegitimacy() {
        if (RingManager.getInstance().getDatas().size() < 3) {
            Log.d(TAG, "checkLegitimacy false");
            return false;
        }
        return true;
    }

    /**
     *
     */
    public void refreshData() {
        Log.d(TAG, "notifyDataSetChanged start");
        if (!checkLegitimacy()) {
            return;
        }

        PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
        Log.d(TAG, "playItem = " + playItem.toString());

        // 获取光圈当前选中数据的position
        int curDataIndex = RingManager.getInstance().getCurIndex();
        Log.d(TAG, "curDataIndex = " + curDataIndex);

        notifyDataOnSelectChanged(curDataIndex);
    }

    /**
     * 根据当前播放节目矫正光圈显示数据
     */
    public void notifyDataSetChanged() {
        Log.d(TAG, "notifyDataSetChanged start");
        if (!checkLegitimacy()) {
            return;
        }

        // 获取当前播放节目
        PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
        // 获取光圈当前选中数据的position
        int curDataIndex = RingManager.getInstance().getCurIndex();
        Log.d(TAG, "curDataIndex = " + curDataIndex);

        // 当前没有播放内容
        if (playItem == null) {
            Log.d(TAG, "playItem == null");
            notifyDataOnSelectChanged(curDataIndex);
            return;
        }
        Log.d(TAG, "playItem = " + playItem.toString());
        Log.d(TAG, "playItem.title = " + playItem.getTitle());

        // 当前正在播放
        // 获取当前播放内容的belongId和type
        String curBelongId = "";
        int radioType = playItem.getType();
        if (radioType == ResType.NOACTION_TYPE) {
            curBelongId = playItem.getAlbumId() + "";
        } else if (radioType == ResType.ALBUM_TYPE) {
            curBelongId = playItem.getAlbumId() + "";
        } else if (radioType == ResType.RADIO_TYPE) {
            curBelongId = playItem.getRadioId() + "";
        } else if (radioType == ResType.BROADCAST_TYPE) {
            curBelongId = playItem.getAlbumId() + "";
        } else if (radioType == ResType.TV_TYPE) {
            curBelongId = playItem.getAlbumId() + "";
        } else if (radioType == ResType.LIVE_TYPE) {
            curBelongId = playItem.getAudioId() + "";
        } else {
            curBelongId = playItem.getAlbumId() + "";
        }
        Log.d(TAG, "playingId = " + curBelongId);
        Log.d(TAG, "radioType = " + radioType);

        // 如果当前播放内容是报时等临时条目，不响应
        if (playItem instanceof TempTaskPlayItem
                || playItem instanceof InvalidPlayItem) {
            Log.d(TAG, "playItem is TempTaskPlayItem");
            if (PlayerManager.getInstance().isPlaying()) {
                Log.d(TAG, "is playing");
            } else {
                Log.d(TAG, "un playing");
            }
            return;
        }

        // 将播放PlayItem对象转化为HomeCell对象
        HomeCell playCell = RingManager.getInstance().transPlayItemToHomeCell(playItem);
        Log.d(TAG, "playCell.belongingId == " + playCell.belongingId);

        // 获取光圈当前选中的数据
        HomeCell curCell = RingManager.getInstance().getCurItem();


        // 当前光圈没有选中的数据（首次进入）
        if (curCell == null) {
            Log.d(TAG, "curItem == null");
            Log.d(TAG, "RingManage.get(0).belongingId == " + RingManager.getInstance().getDatas().get(0).belongingId);
            Log.d(TAG, "RingManager.get(size-1).belongingId == " + RingManager.getInstance().getDatas().get(RingManager.getInstance().getDatas().size() - 1).belongingId);
            if (playCell.belongingId == RingManager.getInstance().getDatas().get(0).belongingId) {
                Log.d(TAG, "notifyDataOnSelectChanged == 0");
                notifyDataOnSelectChanged(0);
            } else if (playCell.belongingId == RingManager.getInstance().getDatas().get(RingManager.getInstance().getDatas().size() - 1).belongingId) {
                Log.d(TAG, "notifyDataOnSelectChanged == RingManager.get(size-1)");
                notifyDataOnSelectChanged(RingManager.getInstance().getDatas().size() - 1);
            } else {
                Log.d(TAG, "addTempData == 0");
                RingManager.getInstance().addTempData(0, playCell);
                notifyDataOnSelectChanged(0);
            }
            return;
        }
        Log.d(TAG, "curCell = " + curCell.toString());
        Log.d(TAG, "curCell.belongingId = " + curCell.belongingId);
        Log.d(TAG, "curCell.name = " + curCell.name);

        // 当前存在播放内容，且光圈选中数据不为空
        // 光圈选中节目和当前播放节目一致
        if (TextUtils.equals(String.valueOf(curCell.belongingId), curBelongId)) {
            Log.d(TAG, "playId == playingId");
            // 此处本来可以直接不响应，但是因为有多个页面共用一个播单的情况，所以更新一下
//            notifyDataOnSelectChanged(curDataIndex);
            return;
        }

        Log.d(TAG, "playId != playingId");
        // 光圈选中节目和当前播放节目不一致,需要进行矫正更新UI
        // 如果光圈中存在临时数据，删除，并更新光圈选中的position为上一个
        if (RingManager.getInstance().getTempData() != null) {
            Log.d(TAG, "getTempData != null");
            RingManager.getInstance().deleteTempData();
            curDataIndex = (curDataIndex + RingManager.getInstance().getDatas().size() - 1) % RingManager.getInstance().getDatas().size();
        }
        Log.d(TAG, "delete后，curDataIndex = " + curDataIndex);

        // 判断播放器当前播放PlayItem是否属于光圈当前可见HomeCell
        int curPlayIndexOfHome = -1;
        HomeCell rightCell = RingManager.getInstance().getDatas().get((curDataIndex + 1) % RingManager.getInstance().getDatas().size());
        if (TextUtils.equals(String.valueOf(rightCell.belongingId), curBelongId)) {
            curPlayIndexOfHome = (curDataIndex + 1) % RingManager.getInstance().getDatas().size();
            Log.d(TAG, "curPlayIndexOfHome is " + curPlayIndexOfHome);
            notifyDataOnSelectChanged(curPlayIndexOfHome);
            return;
        }
        HomeCell centerCell = RingManager.getInstance().getDatas().get(curDataIndex % RingManager.getInstance().getDatas().size());
        if (TextUtils.equals(String.valueOf(centerCell.belongingId), curBelongId)) {
            curPlayIndexOfHome = curDataIndex % RingManager.getInstance().getDatas().size();
            Log.d(TAG, "curPlayIndexOfHome is " + curPlayIndexOfHome);
            notifyDataOnSelectChanged(curPlayIndexOfHome);
            return;
        }
        HomeCell leftCell = RingManager.getInstance().getDatas().get((curDataIndex + RingManager.getInstance().getDatas().size() - 1) % RingManager.getInstance().getDatas().size());
        if (TextUtils.equals(String.valueOf(leftCell.belongingId), curBelongId)) {
            curPlayIndexOfHome = (curDataIndex + RingManager.getInstance().getDatas().size() - 1) % RingManager.getInstance().getDatas().size();
            Log.d(TAG, "curPlayIndexOfHome is " + curPlayIndexOfHome);
            notifyDataOnSelectChanged(curPlayIndexOfHome);
            return;
        }

        curDataIndex = (curDataIndex + 1) % RingManager.getInstance().getDatas().size();
        Log.d(TAG, "curPlayIndexOfHome is " + curPlayIndexOfHome);
        // 将播放内容加入光圈列表
        RingManager.getInstance().addTempData(curDataIndex, playCell);
        notifyDataOnSelectChanged(curDataIndex);
    }

    /**
     * 根据当前播放节目矫正光圈显示数据
     */
//    public void notifyDataSetChanged(){
//        Log.d("123456", "notifyDataSetChanged start");
//        Log.d(TAG, "notifyDataSetChanged start");
//        if(!checkLegitimacy()){
//            return;
//        }
//
//        // 获取当前播放节目
//        PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
//
//        // 获取光圈选中的数据
//        HomeCell curItem = RingManager.getInstance().getCurItem();
//        // 获取当前选中数据的position
//        int curDataIndex = RingManager.getInstance().getCurIndex();
//        Log.d(TAG, "curDataIndex 1 = "+curDataIndex);
//
//        // 当前没有播放内容
//        if(playItem == null){
//            Log.d(TAG, "playItem == null");
//            notifyDataOnSelectChanged(curDataIndex);
//            return;
//        }
//        Log.d(TAG, "playItem = "+playItem.toString());
//
//        // 获取当前播放内容的belongId和type
//        String curBelongId = "";
//        int radioType = playItem.getType();
//        if(radioType == ResType.ALBUM_TYPE){
//            curBelongId = playItem.getAlbumId()+"";
//        }else if(radioType == ResType.RADIO_TYPE){
//            curBelongId = playItem.getRadioId()+"";
//        }else if(radioType == ResType.BROADCAST_TYPE){
//            curBelongId = playItem.getAlbumId()+"";
//        }else if(radioType == ResType.LIVE_TYPE){
//            curBelongId = playItem.getAudioId()+"";
//        }
//        Log.d(TAG, "playingId = "+curBelongId);
//        Log.d(TAG, "radioType = "+radioType);
//
//        // 如果当前播放内容是报时等临时条目，不响应
//        if(radioType == -1 || playItem instanceof TempTaskPlayItem){
//            Log.d(TAG, "playItem is TempTaskPlayItem");
//            if(PlayerManager.getInstance().isPlaying()){
//                Log.d(TAG, "notifyDataSetChanged end 2");
//            }else {
//                notifyDataOnSelectChanged(curDataIndex);
//            }
//            return;
//        }
//
//        // 将播放PlayItem对象转化为HomeCell对象
//        HomeCell homeCell = transPlayItemToHomeCell(playItem);
//        Log.d(TAG, "homeCell.belongingId == "+homeCell.belongingId);
//        // 当前光圈没有选中数据
//        if(curItem == null){
//            Log.d(TAG, "curItem == null");
//            Log.d(TAG, "RingManage.get(0).belongingId == "+RingManager.getInstance().getDatas().get(0).belongingId);
//            Log.d(TAG, "RingManager.get(size-1).belongingId == "+RingManager.getInstance().getDatas().get(RingManager.getInstance().getDatas().size()-1).belongingId);
//            if(homeCell.belongingId == RingManager.getInstance().getDatas().get(0).belongingId){
//                notifyDataOnSelectChanged(0);
//            }else if(homeCell.belongingId == RingManager.getInstance().getDatas().get(RingManager.getInstance().getDatas().size()-1).belongingId){
//                notifyDataOnSelectChanged(RingManager.getInstance().getDatas().size()-1);
//            }else {
//                RingManager.getInstance().addTempData(curDataIndex, homeCell);
//                notifyDataOnSelectChanged(curDataIndex);
//            }
//            return;
//        }
//        Log.d(TAG, "curItem = "+curItem.toString());
//        Log.d(TAG, "curItem.belongingId = "+curItem.belongingId);
//
//        // 当前存在播放内容，且光圈选中数据不为空
//        // 光圈选中节目和当前播放节目一致
//        if(TextUtils.equals(String.valueOf(curItem.belongingId), curBelongId)){
//            Log.d(TAG, "playId == playingId");
//            // 此处本来可以直接不响应，但是因为有多个页面共用一个播单的情况，所以更新一下
//            notifyDataOnSelectChanged(curDataIndex);
//            return;
//        }
//
//        Log.d(TAG, "playId != playingId");
//        // 光圈选中节目和当前播放节目不一致,需要进行矫正更新UI
//        // 如果光圈中存在临时数据，删除，并更新光圈选中的position为上一个
//        if(RingManager.getInstance().getTempData()!=null){
//            Log.d(TAG, "getTempData != null");
//            RingManager.getInstance().deleteTempData();
//            curDataIndex = (curDataIndex+ RingManager.getInstance().getDatas().size()-1)% RingManager.getInstance().getDatas().size();
//        }
//        Log.d(TAG, "curDataIndex 2 = "+curDataIndex);
//
//        // 判断播放器当前播放PlayItem是否存在于光圈数据中
//        int curPlayItemOfHomeIndex = -1;
//        for (int i = 0; i < RingManager.getInstance().getDatas().size(); i++) {
//            HomeCell item = RingManager.getInstance().getDatas().get(i);
//            if (TextUtils.equals(String.valueOf(item.belongingId), curBelongId)) {
//                curPlayItemOfHomeIndex = i;
//                break;
//            }
//        }
//        // curPlayItemOfHomeIndex!=-1，说明播放器当前播放PlayItem存在于光圈数据中
//        if(curPlayItemOfHomeIndex != -1){// 如果需要改需求，每次都插，不管在不在，则添加判断  && (Math.abs(curDataIndex-curPlayItemOfHomeIndex)<3 || Math.abs(curDataIndex-curPlayItemOfHomeIndex)>RingManager.getInstance().getDatas().size()-3)
//            Log.d(TAG, "curPlayItemOfHomeIndex is "+curPlayItemOfHomeIndex);
//            notifyDataOnSelectChanged(curPlayItemOfHomeIndex);
//            return;
//        }
//
//        // curPlayItemOfHomeIndex==-1，说明光圈数据没有播放器当前播放PlayItem
//        Log.d(TAG, "curPlayItemOfHomeIndex == -1");
//        curDataIndex = (curDataIndex+ 1)% RingManager.getInstance().getDatas().size();
//        // 将播放内容加入光圈列表
//        RingManager.getInstance().addTempData(curDataIndex, homeCell);
//        notifyDataOnSelectChanged(curDataIndex);
//
////        // 如果之前光圈选中的数据是临时插入的播放数据
////        if(hasTemp){
////            Log.d(TAG, "tempData != null");
////            curDataIndex = (curDataIndex+ 1)% RingManager.getInstance().getDatas().size();
////            RingManager.getInstance().addTempData(curDataIndex, homeCell);
////            notifyDataOnSelectChanged(curDataIndex);
////            return;
////        }
////
////        // 如果之前光圈选中的数据不是临时插入的播放数据
////        Log.d(TAG, "tempData == null");
////        curDataIndex = (curDataIndex+ 1)% RingManager.getInstance().getDatas().size();
////        RingManager.getInstance().addTempData(curDataIndex, homeCell);
////        notifyDataOnSelectChanged(curDataIndex);
//    }


    /**
     * 根据选中数据刷新光圈UI
     *
     * @param dataSelect
     */
    private void notifyDataOnSelectChanged(int dataSelect) {
        Log.d(TAG, "notifyDataOnSelectChanged start");
        Log.d(TAG, "dataSelect = " + dataSelect);
        mIsUpdating = true;
        RingManager.getInstance().setCurIndex(dataSelect);

        maps.get(selectedPosition).setDataIndex(dataSelect);

        HomeCell homeCell = RingManager.getInstance().getDatas().get(dataSelect);
        maps.get(selectedPosition).setItem(homeCell);


        HomeCell homeCell2 = RingManager.getInstance().getDatas().get((dataSelect + RingManager.getInstance().getDatas().size() - 1) % RingManager.getInstance().getDatas().size());
        maps.get((selectedPosition + 2) % 3).setDataIndex((dataSelect + RingManager.getInstance().getDatas().size() - 1) % RingManager.getInstance().getDatas().size());
        maps.get((selectedPosition + 2) % 3).setItem(homeCell2);


        HomeCell homeCell3 = RingManager.getInstance().getDatas().get((dataSelect + 1) % RingManager.getInstance().getDatas().size());
        maps.get((selectedPosition + 1) % 3).setDataIndex((dataSelect + 1) % RingManager.getInstance().getDatas().size());
        maps.get((selectedPosition + 1) % 3).setItem(homeCell3);

        if (this.isShown()&&(homeCell.getPlayId() != oldIndex || System.currentTimeMillis() - oldTime > 1000)) {
            //因为光圈会在短时间内多次刷新相同数据，所以1秒之内只取一次上报
            oldTime = System.currentTimeMillis();
            //上报内容曝光
            ReportUtil.addContentShowEvent("", ReportParamUtil.getRadioType(homeCell.getResType()),
                    "", String.valueOf(homeCell.getPlayId()),
                    ReportParamUtil.getEventTag(homeCell.isVip(), homeCell.isFine()),
                    pageId, pageId, "" + homeCell.getPositionInParent());
            ReportUtil.addContentShowEvent("", ReportParamUtil.getRadioType(homeCell2.getResType()),
                    "", String.valueOf(homeCell2.getPlayId()),
                    ReportParamUtil.getEventTag(homeCell2.isVip(), homeCell2.isFine()),
                    pageId, pageId, "" + homeCell2.getPositionInParent());
            ReportUtil.addContentShowEvent("", ReportParamUtil.getRadioType(homeCell3.getResType()),
                    "", String.valueOf(homeCell3.getPlayId()),
                    ReportParamUtil.getEventTag(homeCell3.isVip(), homeCell3.isFine()),
                    pageId, pageId, "" + homeCell3.getPositionInParent());

        }

        if (isFirstExecute) {
            unFoldSplashAnim();
            isFirstExecute = false;
        }
        oldIndex = homeCell.getPlayId();
        Log.d(TAG, "notifyDataOnSelectChanged end");
        Log.d(TAG, "notifyDataSetChanged end");
        mIsUpdating = false;

    }

    @Override
    protected void onDetachedFromWindow() {
        PlayerManager.getInstance().removePlayControlStateCallback(mRingPlayerStateListener);
        PlayerManager.getInstance().removePlayListControlStateCallback(mRingPlayerListStateListener);
        super.onDetachedFromWindow();
    }
}
