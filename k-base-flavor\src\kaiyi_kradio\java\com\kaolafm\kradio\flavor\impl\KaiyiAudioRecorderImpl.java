package com.kaolafm.kradio.flavor.impl;

import android.media.AudioManager;
import android.util.Log;

import com.kaolafm.kradio.kaiyi.KaiyiAudioManagerProxy;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.live.KradioRecorderInterface;
import com.kaolafm.kradio.live1.KradioRecorder;

/**
 * <pre>
 *     <AUTHOR> Wenchl
 *     e-mail : <EMAIL>
 *     time   : 2019/09/03
 *     desc   :
 *     version: 1.0
 * </pre>
 */
public class KaiyiAudioRecorderImpl implements KradioRecorderInterface, KaiyiAudioManagerProxy.OnAudioMicFocusChangeListener {

    private KradioRecorder kradioRecorder = new KradioRecorder();
    private static final String TAG = "KaiyiRecorderImpl";

    @Override
    public void startRecord() {

        int mic = KaiyiAudioManagerProxy.getInstance().requestAudioMicFocus(AppDelegate.getInstance().getContext(),this);
        if (mic == AudioManager.AUDIOFOCUS_REQUEST_GRANTED) {
            kradioRecorder.startRecord();
        }
        Log.i(TAG,"startRecord mic:"+mic);
    }

    @Override
    public boolean stopRecord() {
        Log.i(TAG,"stopRecord");
        boolean b = kradioRecorder.stopRecord();
        int abandon = KaiyiAudioManagerProxy.getInstance().abandonAudioMicFocus(AppDelegate.getInstance().getContext(),this);
        Log.i(TAG,"stopRecord abandon:"+abandon);
        return b;
    }

    @Override
    public String getFilePath() {
        return kradioRecorder.getFilePath();
    }

    @Override
    public void onAudioMicFocusChange(int i) {
        Log.i(TAG,"onAudioMicFocusChange:"+i);
        if (i == KaiyiAudioManagerProxy.AUDIOMIC_DELIVER) {
            kradioRecorder.stopRecord();
        }
    }
}
