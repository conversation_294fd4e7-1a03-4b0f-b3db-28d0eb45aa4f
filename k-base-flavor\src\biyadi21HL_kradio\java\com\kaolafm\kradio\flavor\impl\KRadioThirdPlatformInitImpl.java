package com.kaolafm.kradio.flavor.impl;

import android.content.Intent;
import android.os.Build;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioThirdPlatformInitInter;
import com.kaolafm.kradio.service.BYDWidgetService;


/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-04-15 10:29
 ******************************************/
public class KRadioThirdPlatformInitImpl implements KRadioThirdPlatformInitInter {
    @Override
    public boolean initThirdPlatform(Object... args) {
//        AudioStatusManager.getInstance().setNeedPlayServiceForeground(false);
        Intent intent = new Intent(AppDelegate.getInstance().getContext(), BYDWidgetService.class);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            AppDelegate.getInstance().getContext().startForegroundService(intent);
        } else {
            AppDelegate.getInstance().getContext().startService(intent);
        }
        return true;
    }

    @Override
    public boolean destroyThirdPlatform(Object... args) {
        return false;
    }
}
