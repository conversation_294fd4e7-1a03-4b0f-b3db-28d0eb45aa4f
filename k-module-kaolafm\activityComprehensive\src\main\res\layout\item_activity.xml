<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:fitsSystemWindows="true"
    android:id="@+id/item_root"
    android:layout_height="wrap_content"
    >

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/activity_item_guideline"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"/>

    <ImageView
        android:id="@+id/qrCode_image"
        android:layout_width="@dimen/m160"
        android:layout_height="@dimen/m160"
        android:layout_marginEnd="@dimen/activities_item_margin"
        android:padding="@dimen/m6"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/activity_item_guideline"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@drawable/activity_item_img_bg"
        tools:ignore="MissingConstraints" />

    <TextView
        android:id="@+id/qrCode_textView"
        android:layout_width="@dimen/x200"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y10"
        android:paddingLeft="@dimen/x5"
        android:paddingRight="@dimen/x5"
        android:gravity="center_horizontal"
        android:lineSpacingMultiplier="1.25"
        android:maxLines="2"
        android:textSize="@dimen/m24"
        android:textColor="@color/text_color_2"
        app:layout_constraintTop_toBottomOf="@+id/qrCode_image"
        app:layout_constraintLeft_toLeftOf="@+id/qrCode_image"
        app:layout_constraintRight_toRightOf="@+id/qrCode_image"
        tools:text="扫码扫码扫码扫码扫码扫码"
        tools:ignore="MissingConstraints" />

    <TextView
        android:id="@+id/title_activity"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/activities_item_padding_start"
        android:paddingEnd="@dimen/activities_item_padding_end"
        android:paddingTop="@dimen/m20"
        android:paddingBottom="@dimen/m20"
        android:textSize="@dimen/m26"
        android:maxLines="2"
        android:layout_marginEnd="@dimen/x6"
        android:lineSpacingMultiplier="1.20"
        android:textColor="@color/text_color_1"
        android:gravity="center_vertical"
        android:background="@drawable/activity_item_title_bg"
        app:layout_constraintLeft_toRightOf="@+id/activity_item_guideline"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="test"
        />
    <TextView
        android:id="@+id/des_activity"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/activities_item_padding_start"
        android:paddingEnd="@dimen/activities_item_padding_end"
        android:paddingTop="@dimen/y30"
        android:paddingBottom="@dimen/y30"
        android:lineSpacingMultiplier="1.25"
        android:layout_marginEnd="@dimen/x6"
        android:textColor="@color/text_color_3"
        app:layout_constraintLeft_toLeftOf="@+id/title_activity"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title_activity"
        android:textSize="@dimen/m26"
        android:background="@drawable/activity_item_bottom_bg"
        tools:text="新年活动新年活动新年活动新年活动新年活动新年活动新年活动新年活动新年活动新年活动新年活动"
        tools:ignore="MissingConstraints" />

    <View
        android:id="@+id/qr_view_expire"
        android:layout_width="@dimen/m160"
        android:layout_height="@dimen/m160"
        android:background="@color/color_black_70_transparent"
        app:layout_constraintLeft_toLeftOf="@+id/qrCode_image"
        app:layout_constraintRight_toRightOf="@+id/qrCode_image"
        app:layout_constraintTop_toTopOf="@+id/qrCode_image"
        app:layout_constraintBottom_toBottomOf="@+id/qrCode_image"
        android:visibility="gone"
        tools:visibility="visible"
        tools:ignore="MissingConstraints" />
    <com.kaolafm.kradio.component.ui.base.view.OvalImageView
        android:id="@+id/qr_expire_icon"
        android:layout_width="@dimen/m108"
        android:layout_height="@dimen/m70"
        android:src="@drawable/qr_expire_icon"
        app:layout_constraintTop_toTopOf="@+id/qr_view_expire"
        app:layout_constraintBottom_toBottomOf="@+id/qr_view_expire"
        app:layout_constraintLeft_toLeftOf="@+id/qr_view_expire"
        app:layout_constraintRight_toRightOf="@+id/qr_view_expire"
        tools:ignore="MissingConstraints" />
</androidx.constraintlayout.widget.ConstraintLayout>