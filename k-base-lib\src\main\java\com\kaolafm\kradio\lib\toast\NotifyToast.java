package com.kaolafm.kradio.lib.toast;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import android.view.Gravity;
import android.view.ViewGroup;
import android.view.WindowManager;

import com.kaolafm.kradio.lib.utils.ScreenUtil;

/**
 * <AUTHOR>
 **/
public class NotifyToast extends SuperToast {

    private ToastHandler mNotifyHandler;

    public NotifyToast(Context context) {
        super(context);

        mNotifyHandler = ToastHandler.createInstance();

        IntentFilter filter = new IntentFilter();
        filter.addAction("kaolafm.action.onOrientationChanged");
        LocalBroadcastManager.getInstance(context).registerReceiver(new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                int orientation = intent.getIntExtra("orientation", -1);

                if (orientation == Configuration.ORIENTATION_PORTRAIT || orientation == Configuration.ORIENTATION_LANDSCAPE) {
                    if (getView() != null) {
                        //只有顶部toast才响应横竖屏切换
                        final int verticalGravity = getGravity() & Gravity.VERTICAL_GRAVITY_MASK;
                        if (verticalGravity == Gravity.TOP) {

                            if (getDisplayLevel() == ToastStyle.LEVEL_ACTIVITY) {
                                ViewGroup.LayoutParams layoutParams = getView().getLayoutParams();
                                layoutParams.width = ScreenUtil.getGlobalNotifyWindowWidth(orientation);
                            } else {
                                WindowManager wm = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
                                if (wm != null) {
                                    try {
                                        ViewGroup.LayoutParams layoutParams = getView().getLayoutParams();
                                        layoutParams.width = ScreenUtil.getGlobalNotifyWindowWidth(orientation);
                                        wm.updateViewLayout(getView(), layoutParams);
                                    } catch (RuntimeException re) {
                                        re.printStackTrace();
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }, filter);
    }


    @Override
    public NotifyToast show() {
        mNotifyHandler.add(this);
        sendAccessibilityEvent();
        return this;
    }

    /**
     * 手动让toast的消失。
     */
    @Override
    public void dismiss() {
        mNotifyHandler.removeToast(this);
    }

    /**
     * 取消正在显示的toast并清空消息队列。
     */
    @Override
    public void cancelAllToasts() {
        mNotifyHandler.cancelAllToasts();
    }
}
