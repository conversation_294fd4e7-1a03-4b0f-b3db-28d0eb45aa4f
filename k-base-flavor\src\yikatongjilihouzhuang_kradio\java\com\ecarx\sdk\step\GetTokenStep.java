package com.ecarx.sdk.step;

import android.text.TextUtils;
import android.util.Log;

import com.ecarx.sdk.AccessTokenResult;
import com.ecarx.sdk.ECarX;
import com.ecarx.sdk.ECarXSdk;
import com.ecarx.sdk.TokenInfo;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

/**
 * <AUTHOR>
 **/
public class GetTokenStep extends Step {
    private final ECarX ecarx;

    public GetTokenStep(ECarX eCarX) {
        super();
        this.ecarx = eCarX;
    }

    @Override
    public void exe() {
        Log.i(ECarX.TAG, "exe:" + getClass().getSimpleName());
        String code = ecarx.getEcarxCode();
        if (TextUtils.isEmpty(code)) {
            ecarx.updateStep();
            ecarx.nextStep();
        } else {
            ECarXSdk.getInstance().getAccessToken(ECarXSdk.APP_ID, ECarXSdk.PACKAGE_NAME, ECarXSdk.SHA1, code, new HttpCallback<AccessTokenResult>() {
                @Override
                public void onSuccess(AccessTokenResult accessTokenResult) {
                    TokenInfo tokenInfo = new TokenInfo();
                    tokenInfo.setAccessTokenResult(accessTokenResult);
                    tokenInfo.setAccessTokenTime(System.currentTimeMillis());
                    tokenInfo.setRefreshTokenTime(System.currentTimeMillis());
                    ecarx.updateEcarxTokenInfo(tokenInfo);
                    ecarx.updateStep();
                    ecarx.nextStep();
                }

                @Override
                public void onError(ApiException e) {
                    Log.i(ECarX.TAG, "   onError: error=" + e);
                    ecarx.error(e);
                }
            });
        }
    }
}
