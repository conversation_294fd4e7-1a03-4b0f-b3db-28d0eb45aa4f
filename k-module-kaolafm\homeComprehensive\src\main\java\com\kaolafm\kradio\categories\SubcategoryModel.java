package com.kaolafm.kradio.categories;

import android.text.TextUtils;
import android.util.Log;
import android.util.LongSparseArray;

import com.flyco.tablayout.listener.CustomTabEntity;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.kaolafm.base.utils.FileUtil;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.categories.util.DataConverter;
import com.kaolafm.kradio.category.ErrorCode;
import com.kaolafm.kradio.category.TabEntity;
import com.kaolafm.kradio.category.all.AllCategoriesItem;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.mvp.BaseModel;
import com.kaolafm.kradio.lib.bean.BroadcastRadioSimpleData;
import com.kaolafm.kradio.lib.bean.SubcategoryItemBean;
import com.kaolafm.kradio.lib.common.ResType;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.KaolaAppConfigData;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.broadcast.BroadcastRequest;
import com.kaolafm.opensdk.api.broadcast.ProgramDetails;
import com.kaolafm.opensdk.api.operation.OperationRequest;
import com.kaolafm.opensdk.api.operation.model.ImageFile;
import com.kaolafm.opensdk.api.operation.model.category.AlbumCategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.BroadcastCategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.Category;
import com.kaolafm.opensdk.api.operation.model.category.CategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.LeafCategory;
import com.kaolafm.opensdk.api.operation.model.category.LiveProgramCategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.RadioCategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.RadioQQMusicCategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.TVCategoryMember;
import com.kaolafm.opensdk.api.operation.model.column.BroadcastDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.Column;
import com.kaolafm.opensdk.api.operation.model.column.ColumnGrp;
import com.kaolafm.opensdk.api.operation.model.column.ColumnMember;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.core.RuntimeTypeAdapterFactory;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.utils.operation.OperationAssister;
import com.trello.rxlifecycle3.LifecycleTransformer;

import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.Timer;
import java.util.TimerTask;

/**
 * 子分类Model。这里之所以做了数据转换，主要原因是原来接口数据不统一，后面改了接口，数据统一了，没有修改，还是用的原来的逻辑。
 *
 * <AUTHOR>
 */

public class SubcategoryModel extends BaseModel {

    private static final String KEY_CACHE_SONG_CHARTS = "song_charts";

    private static final String KEY_CACHE_LOCAL_BROADCAST = CategoryConstant.MEDIA_TYPE_BROADCAST + "-"
            + CategoryConstant.TAB_CID_LOCAL_BROADCAST;

    private static final String KEY_CACHE_CATEGORY_BROADCAST = CategoryConstant.MEDIA_TYPE_BROADCAST + "-"
            + CategoryConstant.TAB_CID_CATEGORY_BROADCAST;

    private static final String KEY_CACHE_RADIO_MINE = CategoryConstant.MEDIA_TYPE_RADIO + "-"
            + CategoryConstant.TAB_CID_MINE;

    private static final String KEY_CACHE_MUSIC_MINE = CategoryConstant.MEDIA_TYPE_MUSIC + "-"
            + CategoryConstant.TAB_CID_MINE;

    private static final int PAGE_SIZE = 20;
    /**
     * 1:表示上线
     */
    private static final int ONLINE = 1;
    private final BroadcastRequest mBroadcastRequest;

    /**
     * 地区code，服务器获取
     */
    private int areaCode = -1;

    /**
     * 是否有下一页
     */
    private boolean isHaveNext = false;

    private long mCategoryCode;

    private long mCategoryId;

    private HashMap<String, List<SubcategoryItemBean>> mContentCache;

    private final LifecycleTransformer mLifecycleTransformer;

    private int mNextPage = 2;

    protected OperationRequest mOperationRequest;

    private static boolean sNeedReLoad;
    private Queue<SubcategoryItemBean> broadcastDataList = new LinkedList<>();
    private Queue<String> requestTimeStr = new LinkedList<>();
    private Timer mTime;
    private long priod = 1000;//间隔时间

    public SubcategoryModel(LifecycleTransformer lifecycleTransformer) {
        mContentCache = new HashMap<>();
        mLifecycleTransformer = lifecycleTransformer;
        mOperationRequest = new OperationRequest().bindLifecycle(mLifecycleTransformer);
        mBroadcastRequest = new BroadcastRequest().bindLifecycle(mLifecycleTransformer);
        mTime = new Timer();
    }

    public SubcategoryModel(LifecycleTransformer lifecycleTransformer, long categoryId) {
        this(lifecycleTransformer);
        mCategoryId = categoryId;
        Log.i("SubcategoryModel", "SubcategoryModel: mCategoryId=" + mCategoryId);
    }

    @Override
    public void destroy() {
        mContentCache.clear();
        mContentCache = null;
    }

    /**
     * 获取次级分类，包括tab标题和item内容。
     *
     * @param categoryId
     * @param callback
     */
    public void getSubcategory(long categoryId, HttpCallback<Subcategory> callback) {
        mOperationRequest.getCategoryList(categoryId + "", true, 0, true, 1, 20, new HttpCallback<List<Category>>() {
            @Override
            public void onSuccess(List<Category> categories) {
                Subcategory subcategory = new Subcategory();
                if (!ListUtil.isEmpty(categories)) {
                    //应该只有一个
                    for (Category category : categories) {
                        List<Category> childCategories = category.getChildCategories();
                        if (!ListUtil.isEmpty(childCategories)) {
                            ArrayList<SubcategoryItemBean> itemBeans = new ArrayList<>();
                            ArrayList<CustomTabEntity> tabs = new ArrayList<>();
                            for (Category childCategory : childCategories) {
                                tabs.add(new TabEntity(childCategory.getName(), childCategory.getCode()));
                                List<SubcategoryItemBean> subcategoryItemBeans = parseCategory(childCategory);
                                if (!ListUtil.isEmpty(subcategoryItemBeans)) {
                                    itemBeans.addAll(subcategoryItemBeans);
                                }
                            }
                            subcategory.tabs = tabs;
                            subcategory.itemBeans = itemBeans;
                        }
                    }
                }
                callback.onSuccess(subcategory);
            }

            @Override
            public void onError(ApiException e) {
                if (callback != null) {
                    callback.onError(e);
                }
            }
        });
    }

    /**
     * 如已经加载过了，就不在重新加载刷新页面
     */
    public boolean isLoaded(int categoryType, long categoryId) {
        return !ListUtil.isEmpty(mContentCache.get(categoryType + "-" + categoryId));
    }

    public void setHaveNext(boolean isHaveNext) {
        this.isHaveNext = isHaveNext;
    }


    /**
     * 获取[ai电台]下的子分类
     *
     * @param type
     * @param code
     * @param callback
     * @param cid
     */
    private void getCategoryDataForAiRadio(int type, String code, HttpCallback<List<SubcategoryItemBean>> callback, int cid) {
        String zone = "classPage";
        mOperationRequest.getColumnList(code, true, zone, new HttpCallback<List<ColumnGrp>>() {
            @Override
            public void onSuccess(List<ColumnGrp> cgs) {
                if (callback != null) {
                    if (cgs == null || cgs.isEmpty()) {
                        callback.onSuccess(null);
                    } else {
                        List<SubcategoryItemBean> rst = new ArrayList<>();
                        DataConverter.toSubcategoryItemBeans(rst, cgs.get(0), false);
                        //缓存数据
                        cache(type + "-" + cid, rst);
                        callback.onSuccess(rst);
                    }
                }

            }

            @Override
            public void onError(ApiException e) {
                if (callback != null) {
                    callback.onError(e);
                }
            }
        });
    }

    /**
     * 获取广播分类数据
     */
    public void getCategoryBroadcast(HttpCallback<List<SubcategoryItemBean>> callback) {
        mOperationRequest.getCategoryList("0", true, 2,
                new HttpCallback<List<Category>>() {
                    @Override
                    public void onSuccess(List<Category> categoryList) {
                        List<SubcategoryItemBean> categoryBroadcastItem = null;
                        if (!ListUtil.isEmpty(categoryList)) {
                            List<Category> childCategories = categoryList.get(0).getChildCategories();
                            if (!ListUtil.isEmpty(childCategories)) {
                                categoryBroadcastItem = broadcastCategoryToItemBean(
                                        childCategories);
                                cache(KEY_CACHE_CATEGORY_BROADCAST, categoryBroadcastItem);
                                if (callback != null) {
                                    callback.onSuccess(categoryBroadcastItem);
                                }
                            } else {
                                if (callback != null) {
                                    callback.onSuccess(categoryBroadcastItem);
                                }
                            }
                        } else {
                            if (callback != null) {
                                callback.onSuccess(categoryBroadcastItem);
                            }
                        }
                    }

                    @Override
                    public void onError(ApiException e) {
                        if (callback != null) {
                            callback.onError(e);
                        }
                    }
                });

    }

    /**
     * 获取本地广播数据，分页
     */
    public void getLocalBroadcast(int pageNum, HttpCallback<List<SubcategoryItemBean>> callback) {
        mOperationRequest.getColumnList("0", true,
                KaolaAppConfigData.getInstance().getLng(), KaolaAppConfigData.getInstance().getLat(),
                "broadcastPage", null,
                new HttpCallback<List<ColumnGrp>>() {
                    @Override
                    public void onSuccess(List<ColumnGrp> columnGrps) {
                        List<SubcategoryItemBean> broadcastToItemBean = null;
                        if (!ListUtil.isEmpty(columnGrps)) {
                            ColumnGrp columnGrp = columnGrps.get(0);
                            if (columnGrp instanceof Column) {
                                List<? extends ColumnMember> columnMembers = ((Column) columnGrp).getColumnMembers();
                                if (!ListUtil.isEmpty(columnMembers)) {
                                    broadcastToItemBean = broadcastToItemBean(columnMembers);
                                    cache(KEY_CACHE_LOCAL_BROADCAST, broadcastToItemBean);
                                    if (callback != null) {
                                        callback.onSuccess(broadcastToItemBean);
                                    }
                                } else {
                                    if (callback != null) {
                                        callback.onSuccess(broadcastToItemBean);
                                    }
                                }

                            } else {
                                if (callback != null) {
                                    callback.onSuccess(broadcastToItemBean);
                                }
                            }
                        } else {
                            if (callback != null) {
                                callback.onSuccess(broadcastToItemBean);
                            }
                        }
                    }

                    @Override
                    public void onError(ApiException e) {
                        if (callback != null) {
                            callback.onError(e);
                        }
                    }
                });
    }

    /**
     * 广播播放时要将所有列表数据转为BroadcastRadioSimpleData数据用于播放器列表展示。
     */
    public ArrayList<BroadcastRadioSimpleData> itemBean2Simple(List<SubcategoryItemBean> dataList) {
        ArrayList<BroadcastRadioSimpleData> list = new ArrayList<>();
        if (!ListUtil.isEmpty(dataList)) {
            for (int i = 0, size = dataList.size(); i < size; i++) {
                SubcategoryItemBean itemBean = dataList.get(i);
                BroadcastRadioSimpleData simpleData = new BroadcastRadioSimpleData();
                simpleData.setBroadcastId(itemBean.getId());
                simpleData.setName(itemBean.getTitle() + "  " + itemBean.getName());
                simpleData.setImg(itemBean.getCoverUrl());
                simpleData.setResType(itemBean.getResType());
                list.add(simpleData);
            }
        }
        return list;
    }

    /**
     * 本地广播加载更多
     */
    public void loadMore(HttpCallback<List<SubcategoryItemBean>> callback) {
        if (isHaveNext) {
            getLocalBroadcast(mNextPage, callback);
        } else {
            callback.onError(new ApiException("没有下一页了"));
        }
    }


    /**
     * 广播转item数据
     */
    private List<SubcategoryItemBean> broadcastToItemBean(List<? extends ColumnMember> dataList) {
        if (dataList != null && dataList.size() > 0) {
            int size = dataList.size();
            ArrayList<SubcategoryItemBean> itemBeanList = new ArrayList<>();
            String radioId = PlayerManagerHelper.getInstance().getCurPlayItem().getRadioId();
            for (int i = 0; i < size; i++) {
                ColumnMember columnMember = dataList.get(i);
                if (columnMember instanceof BroadcastDetailColumnMember) {
                    BroadcastDetailColumnMember broadcastColumn = (BroadcastDetailColumnMember) columnMember;
                    SubcategoryItemBean itemBean = new SubcategoryItemBean();
                    itemBean.setItemType(SubcategoryItemBean.TYPE_ITEM_BROADCAST_LOCAL);
                    itemBean.setCoverUrl(OperationAssister.getImage(broadcastColumn));
                    itemBean.setName(columnMember.getSubtitle() == null ? "" : columnMember.getSubtitle());
                    itemBean.setTitle(broadcastColumn.getTitle());
                    long broadcastId = broadcastColumn.getBroadcastId();
                    itemBean.setSelected(TextUtils.equals(radioId, String.valueOf(broadcastId))
                            && PlayerManagerHelper.getInstance().isBroadcastPlayer());
                    itemBean.setResType(ResType.BROADCAST_TYPE);
                    itemBean.setId(broadcastId);
                    itemBeanList.add(itemBean);
                }
            }
            return itemBeanList;
        }
        return null;
    }

    /**
     * 广播分类转item数据
     */
    private List<SubcategoryItemBean> broadcastCategoryToItemBean(List<Category> list) {
        if (!ListUtil.isEmpty(list)) {
            int size = list.size();
            ArrayList<SubcategoryItemBean> itemBeanList = new ArrayList<>();
            for (int i = 0; i < size; i++) {
                Category broadcast = list.get(i);
                SubcategoryItemBean itemBean = new SubcategoryItemBean();
                itemBean.setName(broadcast.getName());
                itemBean.setResType(ResType.BROADCAST_TYPE);
                itemBean.setItemType(SubcategoryItemBean.TYPE_ITEM_BROADCAST_CATEGORY);
                itemBean.setId(Long.parseLong(broadcast.getCode()));
                Map<String, ImageFile> imageFiles = broadcast.getImageFiles();
                if (imageFiles != null) {
                    ImageFile imageFile = imageFiles.get(ImageFile.KEY_ICON);
                    if (imageFile != null) {
                        itemBean.setCoverUrl(imageFile.getUrl());
                    }
                }
                itemBeanList.add(itemBean);
            }

            return itemBeanList;
        }
        return null;
    }

    public boolean isHaveNext() {
        return isHaveNext;
    }

    /**
     * 缓存数据到内存
     */
    public void cache(String key, List<SubcategoryItemBean> dataList) {
        if (mContentCache != null) {
            mContentCache.put(key, dataList);
        }
    }

    /***************************************************************************************************************/
    //全部数据
    private static LongSparseArray<Category> mData = new LongSparseArray<>();

    private LongSparseArray<AllCategoriesItem> mItemList = new LongSparseArray<>();

    private void parseCategory(Category cate, int level) {
        mData.append(Long.valueOf(cate.getCode()), cate);
        List<Category> childCategories = cate.getChildCategories();
        if (childCategories != null) {
            //childCategories不为null,表示有子类
            for (int i = 0; i < childCategories.size(); i++) {
                parseCategory(childCategories.get(i), level + 1);
            }
        }
    }

    /**
     * 是否为'LeafCategory',且成员'CategoryMember'为广播类型
     *
     * @param cate
     * @return
     */
    private static boolean containsBroadcastMember(Category cate) {
        boolean rst = false;
        if (cate instanceof LeafCategory) {
            List<CategoryMember> members = ((LeafCategory) cate).getCategoryMembers();
            if (members != null && !members.isEmpty()) {
                CategoryMember cm = members.get(0);
                if (cm instanceof BroadcastCategoryMember) {
                    rst = true;
                }
            }
        }
        return rst;
    }

    private static List<AllCategoriesItem> rst = new ArrayList<>();

    /**
     * @param callback
     */
    public void getAllCategoryList(HttpCallback<List<AllCategoriesItem>> callback) {
        if (mItemList.size() == 0 || sNeedReLoad) {
            //先显示缓存
            //loadFromFile(callback);
//
            loadFromNet(callback);
        } else {
            callback.onSuccess(rst);
        }
    }

    public void refresh() {
        mData.clear();
        rst.clear();
    }

    private void loadFromNet(HttpCallback<List<AllCategoriesItem>> listHttpCallback) {
        String parentCode = "0";
        int type = 0;//分类页面,从根节点开始,传0
        mOperationRequest.getCategoryList(parentCode, true, type, 1, AllCategoriesFragment.MAX_REQUEST_NUMBER,
                new HttpCallback<List<Category>>() {
                    @Override
                    public void onSuccess(List<Category> categoryList) {
                        sNeedReLoad = false;

                        mData.clear();
                        rst.clear();
                        mItemList.clear();

                        for (int i = 0; i < categoryList.size(); i++) {
                            Category category = categoryList.get(i);
                            parseCategory(category, 0);
                            //
                            AllCategoriesItem item = new AllCategoriesItem();
                            item.id = Long.valueOf(category.getCode());
                            item.title = category.getName();
                            item.type = getMemberContentType(category);
                            rst.add(item);
                        }


                        if (listHttpCallback != null) {
                            listHttpCallback.onSuccess(rst);
                        }
                    }

                    @Override
                    public void onError(ApiException e) {
                        sNeedReLoad = true;
                        Log.i("SubcategoryModel", "onError: " + e);
                        if (listHttpCallback != null) {
                            listHttpCallback.onError(e);
                        }
                    }
                });
    }

    public void getCategoryList(String parentCode, HttpCallback<List<AllCategoriesItem>> callback) {
        int type = 0;//分类页面,从根节点开始,传0
        mOperationRequest.getCategoryList(parentCode, true, type, false, 1, AllCategoriesFragment.MAX_REQUEST_NUMBER,
                new HttpCallback<List<Category>>() {
                    @Override
                    public void onSuccess(List<Category> categoryList) {
                        sNeedReLoad = false;
                        mData.clear();
                        rst.clear();
                        mItemList.clear();

                        for (int i = 0; i < categoryList.size(); i++) {
                            Category category = categoryList.get(i);
                            AllCategoriesItem allCategoriesItem = translateToTabAndItem(category);
                            mItemList.put(Long.valueOf(category.getCode()), allCategoriesItem);
                            rst.add(allCategoriesItem);
                        }

                        if (callback != null) {
                            callback.onSuccess(rst);
                        }
                    }

                    @Override
                    public void onError(ApiException e) {
                        sNeedReLoad = true;
                        Log.i("SubcategoryModel", "onError: " + e);
                        if (callback != null) {
                            callback.onError(e);
                        }
                    }
                });
    }

    private AllCategoriesItem translateToTabAndItem(Category category) {
        AllCategoriesItem item = new AllCategoriesItem();
        item.id = Long.valueOf(category.getCode());
        item.title = category.getName();
        List<Category> childCategories = category.getChildCategories();
        if (!ListUtil.isEmpty(childCategories)) {
            ArrayList<CustomTabEntity> tabList = new ArrayList<>();
            ArrayList<SubcategoryItemBean> itemBeanList = new ArrayList<>();
            for (Category childCategory : childCategories) {
                tabList.add(new TabEntity(childCategory.getName(), childCategory.getCode()));
                List<Category> childOfChilds = childCategory.getChildCategories();
                //不等于空说明是三级分类
                if (!ListUtil.isEmpty(childOfChilds)) {
                    for (Category childOfChild : childOfChilds) {
                        //不是叶子分类说明是四级分类
                        List<SubcategoryItemBean> beanList = memberToItem(category);
                        if (!ListUtil.isEmpty(beanList)) {
                            itemBeanList.addAll(beanList);
                        } else {
                            List<Category> fourCategories = childOfChild.getChildCategories();
                            if (!ListUtil.isEmpty(fourCategories)) {
                                for (Category fourCategory : fourCategories) {
                                    SubcategoryItemBean subcategoryItemBean = new SubcategoryItemBean();
                                    subcategoryItemBean.setName(fourCategory.getName());
                                    subcategoryItemBean.setTitle(fourCategory.getName());
                                    subcategoryItemBean.setId(Long.parseLong(fourCategory.getCode()));
                                    subcategoryItemBean.setItemType(SubcategoryItemBean.TYPE_ITEM_TITLE);
                                    itemBeanList.add(subcategoryItemBean);
                                    List<SubcategoryItemBean> fourItemList = memberToItem(fourCategory);
                                    if (!ListUtil.isEmpty(fourItemList)) {
                                        itemBeanList.addAll(fourItemList);
                                    }
                                }
                            }
                        }
                    }
                } else {
                    List<SubcategoryItemBean> beanList = memberToItem(childCategory);
                    if (!ListUtil.isEmpty(beanList)) {
                        itemBeanList.addAll(beanList);
                    }
                }
            }
            item.tabList = tabList;
            item.itemBeanList = itemBeanList;
        } else {

        }
        return item;
    }

    private List<SubcategoryItemBean> memberToItem(Category fourCategory) {
        if (fourCategory instanceof LeafCategory) {
            ArrayList<SubcategoryItemBean> itemList = new ArrayList<>();
            List<CategoryMember> categoryMembers = ((LeafCategory) fourCategory).getCategoryMembers();
            if (!ListUtil.isEmpty(categoryMembers)) {
                for (CategoryMember categoryMember : categoryMembers) {
                    itemList.add(parseCategoryMember(categoryMember));
                }
            }
            return itemList;
        }
        return null;
    }

    public static int getTreeDepth(long id) {
        int rst = 0;
        Category category = mData.get(id);
        rst = getDepth(category, 0);
        return rst;
    }

    private static int getDepth(Category category, int d) {
        int depth = d;
        if (category == null || category instanceof LeafCategory) {
            return depth;
        } else {
            List<Category> childCategories = category.getChildCategories();
            if (childCategories != null) {
                depth = getDepth(childCategories.get(0), depth + 1);
            }
        }
        return depth;
    }


    /**
     * 获取分类下的标签数据
     *
     * @param parentId
     * @param httpCallback
     */
    public void getCategoriesByParentId(long parentId, HttpCallback<List<Category>> httpCallback) {
        Category category = mData.get(parentId);
        if (category != null) {
            List<Category> childCategories = category.getChildCategories();
            if (!ListUtil.isEmpty(childCategories)) {

                httpCallback.onSuccess(childCategories);
            } else {
                httpCallback.onError(new ApiException(ErrorCode.NO_SUBCATEGORY, "[" + parentId + "]该分类下无数据!"));
            }
        } else {
            httpCallback.onError(new ApiException(ErrorCode.TYPE_ERROR, "[" + parentId + "]该分类类型错误!"));
        }
    }

    public void getSubcategoryList(String parentId, HttpCallback<List<Category>> callback) {
        mOperationRequest.getSubcategoryListForMoreLevels(parentId, null, callback);
    }

    public void getCategoryMemberList(String parentCode, HttpCallback<List<SubcategoryItemBean>> callback) {
        mCategoryCode = Long.parseLong(parentCode);
        mOperationRequest.getCategoryMemberList(parentCode, 1, AllCategoriesFragment.MAX_REQUEST_NUMBER,
                new HttpCallback<BasePageResult<List<CategoryMember>>>() {
                    @Override
                    public void onSuccess(BasePageResult<List<CategoryMember>> basePageResult) {
                        mNextPage = basePageResult.getNextPage();
                        isHaveNext = basePageResult.getHaveNext() == Constants.HAVE_PAGE;
                        List<CategoryMember> dataList = basePageResult.getDataList();
                        if (!ListUtil.isEmpty(dataList)) {
                            ArrayList<SubcategoryItemBean> itemBeans = new ArrayList<>();
                            for (CategoryMember categoryMember : dataList) {
                                SubcategoryItemBean subcategoryItemBean = parseCategoryMember(categoryMember);
                                itemBeans.add(subcategoryItemBean);
                                broadcastDataList.add(subcategoryItemBean);
                            }
                            Log.i("broadcastDataList:size:",broadcastDataList.size()+"");
                            callback.onSuccess(itemBeans);
                        } else {
                            ArrayList<SubcategoryItemBean> itemBeans = new ArrayList<>();
                            callback.onSuccess(itemBeans);
                        }
                    }

                    @Override
                    public void onError(ApiException e) {
                        callback.onError(e);
                    }
                });
    }

    /**
     * 获取分类对应的数据
     *
     * @param cid         parent id
     * @param isRecursive 是否解析下一级
     * @param callback
     */
    public void getSubcategoryItemBeanByParentId(long cid, boolean isRecursive, HttpCallback<List<SubcategoryItemBean>> callback) {
        List<SubcategoryItemBean> rst = new ArrayList<>();
        mCategoryCode = cid;
        Category category = mData.get(cid);
        Log.i("SubcategoryModel", "getSubcategoryItemBeanByParentId: " + cid + ", " + mData);
        if (category != null) {
            List<Category> childCategories = category.getChildCategories();

            if (childCategories == null) {
                //无下级分类
                parseLeafCategory(callback, rst, category);
            } else {
                //有下级分类
                for (int i = 0; i < childCategories.size(); i++) {
                    Category c = childCategories.get(i);

                    boolean isBroadcastCategory = containsBroadcastMember(c);
                    //解析分类
                    SubcategoryItemBean sib = new SubcategoryItemBean();
                    sib.setId(Long.valueOf(c.getCode()));
                    sib.setTitle(c.getName());
                    sib.setName(c.getName());
                    sib.setResType(c.getContentType());
                    sib.setItemType(getItemType(c));
                    rst.add(sib);


                    if (!isBroadcastCategory) {
                        parseLeafCategory(callback, rst, c);
                    }
                }

                callback.onSuccess(rst);
            }
        } else {
            callback.onError(new ApiException(cid + ":不存在该id的分类."));
        }
    }

    private void parseLeafCategory
            (HttpCallback<List<SubcategoryItemBean>> callback, List<SubcategoryItemBean> rst, Category
                    category) {
        if (category instanceof LeafCategory) {
            List<CategoryMember> categoryMembers = ((LeafCategory) category).getCategoryMembers();

            if (categoryMembers != null) {

                for (int i = 0; i < categoryMembers.size(); i++) {
                    CategoryMember cm = categoryMembers.get(i);

                    SubcategoryItemBean sib = parseCategoryMember(cm);

                    rst.add(sib);
                }

                callback.onSuccess(rst);
            } else {
                callback.onError(new ApiException("getSubcategoryItemBeanByParentId categoryMembers is NULL!"));
            }
        } else {
            //do-nothing
        }
    }

    public List<SubcategoryItemBean> parseCategory(Category category) {
        ArrayList<SubcategoryItemBean> itemBeans = new ArrayList<>();
        //三级分类
        if (category instanceof LeafCategory) {
            parseCategoryMemberList((LeafCategory) category, itemBeans);
            //四级分类
        } else {
            List<Category> childCategories = category.getChildCategories();
            if (!ListUtil.isEmpty(childCategories)) {
                for (Category childCategory : childCategories) {
                    //解析分类
                    SubcategoryItemBean sib = new SubcategoryItemBean();
                    sib.setId(Long.valueOf(childCategory.getCode()));
                    sib.setTitle(childCategory.getName());
                    sib.setName(childCategory.getName());
                    sib.setResType(childCategory.getContentType());
                    int itemType = getItemType(childCategory);
                    sib.setItemType(itemType);
                    itemBeans.add(sib);
                    //标题类型后面需要继续展示成员
                    if (itemType == SubcategoryItemBean.TYPE_ITEM_TITLE && childCategory instanceof LeafCategory) {
                        parseCategoryMemberList((LeafCategory) childCategory, itemBeans);
                    }
                }
            }
        }
        return itemBeans;
    }

    private void parseCategoryMemberList(LeafCategory category, ArrayList<SubcategoryItemBean> itemBeans) {
        List<CategoryMember> categoryMembers = category.getCategoryMembers();
        if (categoryMembers != null) {
            for (int i = 0; i < categoryMembers.size(); i++) {
                CategoryMember cm = categoryMembers.get(i);
                SubcategoryItemBean sib = parseCategoryMember(cm);
                //添加上一级分类的code。
                sib.setParentCode(category.getCode());
                itemBeans.add(sib);
            }
        }
    }


    private static int getItemType(Object cOrMember) {
        int rst;
        if (cOrMember instanceof Category) {
            if (containsBroadcastMember((Category) cOrMember)) {
                rst = SubcategoryItemBean.TYPE_ITEM_BROADCAST_CATEGORY;
            } else {
                rst = SubcategoryItemBean.TYPE_ITEM_TITLE;
            }

        } else if (cOrMember instanceof RadioQQMusicCategoryMember) {
            rst = SubcategoryItemBean.TYPE_ITEM_RADIO_CHANNEL;

        } else if (cOrMember instanceof RadioCategoryMember) {
            rst = SubcategoryItemBean.TYPE_ITEM_RADIO_CHANNEL;

        } else if (cOrMember instanceof BroadcastCategoryMember) {
            rst = SubcategoryItemBean.TYPE_ITEM_BROADCAST_LOCAL;

        } else if (cOrMember instanceof LiveProgramCategoryMember) {
            rst = SubcategoryItemBean.TYPE_ITEM_BROADCAST_LOCAL;

        } else if (cOrMember instanceof AlbumCategoryMember) {
            rst = SubcategoryItemBean.TYPE_ITEM_ALBUM;

        } else if (cOrMember instanceof TVCategoryMember) {
            rst = SubcategoryItemBean.TYPE_ITEM_TV;

        } else {
            rst = SubcategoryItemBean.TYPE_ITEM_RADIO_CHANNEL;
        }
        return rst;
    }


    private SubcategoryItemBean parseCategoryMember(CategoryMember cm) {
        SubcategoryItemBean sib = null;
        if (cm instanceof BroadcastCategoryMember) {
            sib = new SubcategoryItemBean();

            BroadcastCategoryMember broadcastColumn = (BroadcastCategoryMember) cm;
            sib.setCoverUrl(OperationAssister.getImage(broadcastColumn));
            sib.setName(cm.getSubtitle() == null ? "" : cm.getSubtitle());
            sib.setTitle(broadcastColumn.getTitle());
            sib.setResType(ResType.BROADCAST_TYPE);
            sib.setId(broadcastColumn.getBroadcastId());

        } else if (cm instanceof TVCategoryMember) {
            sib = new SubcategoryItemBean();

            TVCategoryMember broadcastColumn = (TVCategoryMember) cm;
            sib.setParentCode(cm.getCode());
            sib.setCoverUrl(OperationAssister.getImage(broadcastColumn));
            sib.setName(cm.getSubtitle() == null ? "" : cm.getSubtitle());
            sib.setTitle(broadcastColumn.getTitle());
            sib.setResType(ResType.TV_TYPE);
            sib.setId(broadcastColumn.getListenTVid());
        } else if (cm instanceof AlbumCategoryMember) {
            AlbumCategoryMember albumCategoryMember = (AlbumCategoryMember) cm;
            sib = new SubcategoryItemBean();
            sib.setId(OperationAssister.getId(cm));
            sib.setName(cm.getTitle());
            sib.setResType(OperationAssister.getType(cm));
            sib.setCoverUrl(OperationAssister.getImage(cm));
            sib.setFine(albumCategoryMember.getFine());
            sib.setVip(albumCategoryMember.getVip());
        } else {
            sib = new SubcategoryItemBean();
            sib.setId(OperationAssister.getId(cm));
            sib.setName(cm.getTitle());
            sib.setResType(OperationAssister.getType(cm));
            sib.setCoverUrl(OperationAssister.getImage(cm));
        }

        sib.setItemType(getItemType(cm));

        return sib;
    }


    private static Integer getMemberContentType(Category category) {
        int rst = CategoryConstant.MEDIA_TYPE_RADIO;
        if (category != null) {
            List<Category> childCategories = category.getChildCategories();
            if (childCategories != null) {
                rst = getMemberContentType(childCategories.get(0));
            } else {
                if (category instanceof LeafCategory) {
                    List<CategoryMember> categoryMembers = ((LeafCategory) category).getCategoryMembers();
                    if (!ListUtil.isEmpty(categoryMembers)) {
                        CategoryMember cm = categoryMembers.get(0);
                        rst = getContentType(cm);
                    }
                }
            }
        }
        return rst;
    }

    private static int getContentType(CategoryMember cm) {
        int rst;
        if (cm instanceof RadioQQMusicCategoryMember) {
            rst = CategoryConstant.MEDIA_TYPE_MUSIC;

        } else if (cm instanceof RadioCategoryMember) {
            rst = CategoryConstant.MEDIA_TYPE_RADIO;

        } else if (cm instanceof AlbumCategoryMember) {
            rst = CategoryConstant.MEDIA_TYPE_RADIO;

        } else if (cm instanceof BroadcastCategoryMember) {
            rst = CategoryConstant.MEDIA_TYPE_BROADCAST;

        } else if (cm instanceof LiveProgramCategoryMember) {
            rst = CategoryConstant.MEDIA_TYPE_BROADCAST;

        } else {
            rst = CategoryConstant.MEDIA_TYPE_RADIO;
        }
        return rst;
    }


    /**
     * 获取本地分类数据
     *
     * @return
     */
    public List<SubcategoryItemBean> getLocalBroadcastMembers(long itemId) {
        // TODO: 2019/3/11 有问题,有可能加入广播分类下的成员
        int index = 0;
        int size = mData.size();
        c:
        for (int i = 0; i < size; i++) {

            Category category = mData.valueAt(i);

            if (category instanceof LeafCategory) {
                List<CategoryMember> categoryMembers = ((LeafCategory) category).getCategoryMembers();
                if (!ListUtil.isEmpty(categoryMembers)) {
                    for (int j = 0; j < categoryMembers.size(); j++) {
                        CategoryMember categoryMember = categoryMembers.get(j);
                        if (categoryMember instanceof BroadcastCategoryMember) {
                            if (((BroadcastCategoryMember) categoryMember).getBroadcastId() == itemId) {
                                index = i;
                                break c;
                            }

                        }
                    }
                }
            }
        }

        //
        List<SubcategoryItemBean> rst = new ArrayList<>();
        Category category = mData.valueAt(index);

        if (category != null && category instanceof LeafCategory) {
            List<CategoryMember> categoryMembers = ((LeafCategory) category).getCategoryMembers();
            for (int j = 0; j < categoryMembers.size(); j++) {
                CategoryMember categoryMember = categoryMembers.get(j);

                if (categoryMember instanceof BroadcastCategoryMember) {
                    rst.add(parseCategoryMember(categoryMember));
                }
            }
        }

        return rst;
    }

    private void loadFromFile(HttpCallback<List<AllCategoriesItem>> listHttpCallback) {
        RuntimeTypeAdapterFactory<CategoryMember> factory = RuntimeTypeAdapterFactory
                .of(CategoryMember.class)
                .registerSubtype(AlbumCategoryMember.class, "AlbumDetailTbCategoryMember")
                .registerSubtype(BroadcastCategoryMember.class, "BroadcastDetailTbCategoryMember")
                .registerSubtype(LiveProgramCategoryMember.class, "LiveProgramDetailTbCategoryMember")
                .registerSubtype(RadioCategoryMember.class, "RadioDetailTbCategoryMember");

        RuntimeTypeAdapterFactory<Category> factoryCate = RuntimeTypeAdapterFactory
                .of(Category.class)
                .registerSubtype(Category.class)
                .registerSubtype(LeafCategory.class);

        Gson gson = new GsonBuilder()
                //支持将序列化key为object的map,默认只能序列化key为string的map
                .enableComplexMapKeySerialization()
                .registerTypeAdapterFactory(factory)
                .registerTypeAdapterFactory(factoryCate)
                .create();

        String json = FileUtil.readAssetFile(AppDelegate.getInstance().getContext(), "all_category.json", "UTF-8");
        List<Category> cache = gson.fromJson(json, new TypeToken<List<Category>>() {
        }.getType());

        mData.clear();
        rst.clear();

        for (int i = 0; i < cache.size(); i++) {
            Category category = cache.get(i);
            parseCategory(category, 0);
            //
            AllCategoriesItem item = new AllCategoriesItem();
            item.id = Long.valueOf(category.getCode());
            item.title = category.getName();
            item.type = getMemberContentType(category);
            rst.add(item);
        }


        listHttpCallback.onSuccess(rst);
    }


    /**
     * 获取广播电台列表数据
     */
    public void requestAlbumListData(String code, int nextPage, HttpCallback<BasePageResult<List<CategoryMember>>> callback) {
        mOperationRequest.getCategoryMemberList(code, nextPage, AllCategoriesFragment.MAX_REQUEST_NUMBER, callback);
    }

    /**
     * 加载更多分类成员
     */
    public void loadMoreCategoryMember(HttpCallback<List<SubcategoryItemBean>> callback) {
        mOperationRequest.getCategoryMemberList(String.valueOf(mCategoryCode), mNextPage,
                AllCategoriesFragment.MAX_REQUEST_NUMBER, new HttpCallback<BasePageResult<List<CategoryMember>>>() {
                    @Override
                    public void onSuccess(BasePageResult<List<CategoryMember>> basePageResult) {
                        if (basePageResult == null) {
                            if (callback != null) {
                                callback.onError(new ApiException("requestAlbumListData categoryMembers is NULL!"));
                            }
                            return;
                        }
                        isHaveNext = basePageResult.getHaveNext() == 1;
                        mNextPage = basePageResult.getNextPage();
                        List<CategoryMember> categoryMembers = basePageResult.getDataList();

                        if (ListUtil.isEmpty(categoryMembers)) {
                            if (callback != null) {
                                callback.onError(new ApiException("requestAlbumListData categoryMembers is NULL!"));
                            }
                            return;
                        }
                        List<SubcategoryItemBean> rst = new ArrayList<>();
                        for (int i = 0; i < categoryMembers.size(); i++) {
                            CategoryMember cm = categoryMembers.get(i);
                            rst.add(parseCategoryMember(cm));
                        }
                        if (callback != null) {
                            callback.onSuccess(rst);
                        }
                    }

                    @Override
                    public void onError(ApiException e) {
                        if (callback != null) {
                            callback.onError(e);
                        }
                    }
                });
    }
    public void getBroadcastProgramList(long broadcastId, String data, HttpCallback<List<ProgramDetails>> callback) {
        mBroadcastRequest.getBroadcastProgramList(broadcastId, data,new HttpCallback<List<ProgramDetails>>() {
            @Override
            public void onSuccess(List<ProgramDetails> list) {
                callback.onSuccess(list);
            }

            @Override
            public void onError(ApiException e) {
                callback.onError(e);
            }
        });
    }
    public void schedulProgramListRequest(int delay,HttpCallback<List<ProgramDetails>> callback){
        if (mTime == null) {
            Log.w("SubcategoryModel", "Timer is null, cannot schedule program list request. Timer may have been cancelled.");
            return;
        }

        TimerTask mTimeTask = new TimerTask(){
            @Override
            public void run() {
                if(broadcastDataList != null){
                    Log.i("broadcastDataList:",broadcastDataList.size()+"");
                    if(broadcastDataList.size() > 0 ){
                        SubcategoryItemBean subcategoryItemBean = broadcastDataList.poll();
                        if(subcategoryItemBean != null){
                            getBroadcastProgramList(subcategoryItemBean.getId(),null, callback);
                        }
                    }
                }
            }
        };

        try {
            mTime.schedule(mTimeTask,delay * 1000,priod);
        } catch (IllegalStateException e) {
            Log.w("SubcategoryModel", "Timer was cancelled, cannot schedule task", e);
        } catch (Exception e) {
            Log.e("SubcategoryModel", "Error scheduling timer task", e);
        }
    }

    public void cancelSchedule(){
        mTime.cancel();
        mTime = null;
    }
}
