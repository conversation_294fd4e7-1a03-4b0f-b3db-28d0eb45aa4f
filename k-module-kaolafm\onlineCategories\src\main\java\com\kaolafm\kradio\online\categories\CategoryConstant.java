package com.kaolafm.kradio.online.categories;

import androidx.annotation.IntDef;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.ResUtil;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 分类相关常量
 *
 * <AUTHOR>
 * @date 2018/4/26
 */

public interface CategoryConstant {

    /**
     * 未定义
     */
    public static final int TAB_TYPE_UNDEFINED = 20181021;

    /**
     * 音乐 我的 tab
     */
    public static final int TAB_TYPE_MUSIC_MINE = 20181022;

    /**
     * radio 我的 tab
     */
    public static final int TAB_TYPE_RADIO_MINE = 20181023;

    /**
     * 广播,本地
     */
    public static final int TAB_TYPE_BRAODCAST_LOCAL = 20181024;

    /**
     * 广播,分类
     */
    public static final int TAB_TYPE_BRAODCAST_CAEGORY = 20181025;

    /**
     * 限制传入媒体类型参数，音乐、电台、广播
     */
    @Target(ElementType.PARAMETER)
    @Retention(RetentionPolicy.SOURCE)
    @IntDef({CategoryConstant.MEDIA_TYPE_MUSIC, CategoryConstant.MEDIA_TYPE_RADIO,
            CategoryConstant.MEDIA_TYPE_BROADCAST})
    @interface MediaType {

    }

    @Target({ElementType.PARAMETER, ElementType.FIELD, ElementType.METHOD})
    @Retention(RetentionPolicy.SOURCE)
    @IntDef({LOGIN_TYPE_TICKET, LOGIN_TYPE_QQ, LOGIN_TYPE_WECHAT, LOGIN_TYPE_DEVICE, LOGIN_TYPE_QQMUSIC})
    @interface LoginType {

    }

    /**
     * 媒资类型 音乐、电台、广播
     */
    String MEDIA_TYPE = "category_type";

    String PAGE_ID = "page_id";

    /**
     * 一级分类Id
     */
    String CATEGORY_ID = "category_id";
    /**
     * 二级分类id
     */
    String SUBCATEGORY_ID = "subcategory_id";

    /**
     * 用于判断是否是过来登录的
     */
    String COME_TO_LOGIN = "come_to_login";

    /**
     * 次级分类下是否有成员，用于判断下个层级是获取
     */
    String HAVE_MEMBERS = "haveMembers";

    /**
     * 是否是本地电台
     */
    String IS_LOCAL_RADIO = "isLocalRadio";

    /**
     * 音乐类型
     */
    int MEDIA_TYPE_MUSIC = /*10*/5;
    /**
     * 电台类型
     */
    int MEDIA_TYPE_RADIO = /*9*/4;
    /**
     * 广播类型
     */
    int MEDIA_TYPE_BROADCAST = /*30*/2;
    /**
     * 听电视
     */
    int MEDIA_TYPE_TV = /*30*/7;
    /**************************账号、收听等自定义item的区分点击事件********************************/
    /**
     * 考拉fm账号
     */
    int RESOURCE_TYPE_RADIO_ACCOUNT_KAOLA = 1031;

    /**
     * QQ音乐账号
     */
    int RESOURCE_TYPE_MUSIC_ACCOUNT_QQ = 1032;

    int RESOURCE_TYPE_RADIO_NO_SUBSCRIPTION = 1033;


    /**********************************************************/

    /**
     * grid4列
     */
    int GRID_FOUR_SPAN = 4;
    /**
     * grid5列
     */
    int GRID_FIVE_SPAN = 5;
    /**
     * 一行的列数
     */
    int GRID_TOTAL_SPAN_COUNT = 8;//GRID_FOUR_SPAN * GRID_FIVE_SPAN;

    /**
     * 数据来源，QQ音乐排行
     */
    int SOURCES_TYPE_QQMUSIC_CHARTS = 1;

    String TAB_NAME_MINE = ResUtil.getString(R.string.online_mine);

    /**
     * 分类排序，我的  本地在线广播
     */
    int CATEGRAY_SORT_MINE_LOACLBRODCAST = 0;

    int CATEGRAY_SORT_MINE_LINERODCAST = 1;

    public static final String TYPE_CATEGORY = "Category";
    /**
     * <我的>自定义id
     */
    String TAB_CID_MINE = "-20";
    /**
     * <本地广播>自定义id
     */
    int TAB_CID_LOCAL_BROADCAST = 21;

    /**
     * <分类广播>自定义id
     */
    int TAB_CID_CATEGORY_BROADCAST = 22;

//======================账号登录相关=============================
    /**
     * 账号登录类型
     */
    String LOGIN_TYPE = "login_type";

    /**
     * QQ音乐相关票据登录验证
     */
    int LOGIN_TYPE_TICKET = 1;
    /**
     * qq登录账号类型
     */
    int LOGIN_TYPE_QQ = 2;
    /**
     * 微信登录账号类型
     */
    int LOGIN_TYPE_WECHAT = 3;
    /**
     * 硬件登录
     */
    int LOGIN_TYPE_DEVICE = 4;

    /**
     * 用户（QQ或微信）与硬件联合登录, 此种登录类型包函了login_type =0，1，2，3，4几种情况（单账号登录)
     */
    int LOGIN_TYPE_QQMUSIC = 5;

    /**
     * 考拉fm登录类型
     */
    int LOGIN_TYPE_KAOLAFM = 6;


    /**
     * 登录requestCode
     */
    int LOGIN_REQUEST_CODE = 100;
    /**
     * 登录成功返回code
     */
    int LOGIN_RESULT_SUCCESS_CODE = 10;
    /**
     * 退出登录返回code
     */
    int LOGOUT_RESULT_CODE = 11;
    /**
     * 取消登录
     */
    int LOGIN_RESULT_CANCEL_CODE = 12;

    //===========================================网络请求地址========================================================

    /**
     * 分类最多请求叶子数量.
     * 使用老接口. 没有分页信息. 客户端不知道是否需要加载下一页数据.只能设置这个值. 如果请求下来的叶子数等于这个值, 那么就加载下一页, 反之亦然.
     */


}
