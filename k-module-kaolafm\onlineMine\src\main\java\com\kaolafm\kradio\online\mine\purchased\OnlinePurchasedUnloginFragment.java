package com.kaolafm.kradio.online.mine.purchased;

import androidx.annotation.NonNull;
import android.view.View;
import android.widget.TextView;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.mvp.IPresenter;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.lib.base.ui.online.BaseShowHideFragment;
import com.trello.rxlifecycle3.LifecycleTransformer;

public class OnlinePurchasedUnloginFragment extends BaseShowHideFragment {

    private TextView tv_unlogin;

    @Override
    protected int getLayoutId() {
        return R.layout.online_fragment_purchased_empty;
    }

    @Override
    protected IPresenter createPresenter() {
        return null;
    }

    @Override
    public void initView(View view) {
        tv_unlogin = view.findViewById(R.id.tv_unlogin);
        tv_unlogin.setText(getString(R.string.mine_purchased_un_login_show));
    }

    @NonNull
    @Override
    public LifecycleTransformer bindUntilEvent(@NonNull Object event) {
        return null;
    }

    @Override
    protected void changeViewLayoutForStatusBar(View view) {
    }

    @Override
    protected void addFragmentRootViewPadding(View view) {
    }

    @Override
    protected void showAccordingToScreen(int orientation) {
        super.showAccordingToScreen(orientation);
    }
}
