package com.kaolafm.kradio.common.widget;

import android.content.Context;
import androidx.annotation.DrawableRes;
import androidx.constraintlayout.widget.ConstraintLayout;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.SeekBar;
import android.widget.TextView;
import com.kaolafm.kradio.lib.utils.date.TimeUtil;
import com.kaolafm.kradio.k_kaolafm.R;
import java.util.Formatter;
import java.util.Locale;

/**
 * 简单封装了一个SeekBar和两个显示时间的文本框
 *
 * <AUTHOR>
 */
public class TimerSeekBar extends ConstraintLayout {
    private static final int ELAPSED_MIX = 1;
    protected SeekBarView mSeekBar;
    protected TextView mElapsedTimeText;
    protected TextView mTotalTimeText;

    protected StringBuilder formatBuilder;
    protected Formatter formatter;

    protected long mElapsedTime;
    protected long mTotalTime;

    private int mTouchExpand;

    private SeekBar.OnSeekBarChangeListener mSeekBarListener;

    public TimerSeekBar(Context context) {
        this(context, null);
    }

    public TimerSeekBar(Context context, AttributeSet attrs) {
        this(context, attrs, -1);
    }

    public TimerSeekBar(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context);
    }

    protected void initView(Context context) {
        LayoutInflater.from(context).inflate(R.layout.timer_seek_bar, this, true);
        mSeekBar = findViewById(R.id.horizontal_seek_bar);
        mTouchExpand = getResources().getDimensionPixelOffset(R.dimen.m25);
        mElapsedTimeText = findViewById(R.id.seek_bar_elapsed_time);
        mTotalTimeText = findViewById(R.id.seek_bar_total_time);

        formatBuilder = new StringBuilder();
        formatter = new Formatter(formatBuilder, Locale.getDefault());
        mSeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (mSeekBarListener != null) {
                    mSeekBarListener.onProgressChanged(seekBar, progress, fromUser);
                }
                if (fromUser) {
                    setElapsedTime(progress);
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                if (mSeekBarListener != null) {
                    mSeekBarListener.onStartTrackingTouch(seekBar);
                }
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                if (mSeekBarListener != null) {
                    mSeekBarListener.onStopTrackingTouch(seekBar);
                }
            }
        });
    }

    public void setOnSeekBarChangeListener(SeekBar.OnSeekBarChangeListener listener) {
        if (listener != null) {
            mSeekBarListener = listener;
        }
    }

    public void setElapsedTime(long millis) {
        if (millis == -1) {
            mElapsedTimeText.setText("");
            mElapsedTime = millis;
            return;
        }
        if (mTotalTime <= 0) {
            millis = 0;
        }
        if (mElapsedTime == 0 || millis != mElapsedTime) {
            String str = TimeUtil.getStringForTime(formatBuilder, formatter, millis);
            mElapsedTimeText.setText(str);
            mElapsedTime = millis;
        }
    }

    public void setTotalTime(long millis) {
        if (millis == -1) {
            mTotalTimeText.setText("");
            mTotalTime = millis;
            return;
        }
        if (millis != mTotalTime) {
            String str = TimeUtil.getStringForTime(formatBuilder, formatter, millis);
            mTotalTimeText.setText(str);
            mTotalTime = millis;
        }
    }

    public void setProgress(int progress) {
        mSeekBar.setProgress(progress);
    }

    public void setMaxProgress(int duration) {
        mSeekBar.setMax(duration);
    }

    @Override
    public void setEnabled(boolean enabled) {
        mSeekBar.setEnabled(enabled);
    }

    public void setThumb(@DrawableRes int thumbId) {
        mSeekBar.setThumb(getResources().getDrawable(thumbId));
    }

    public void setCanTouch(boolean isCanTouch) {
        mSeekBar.setCanTouch(isCanTouch);
    }

}
