package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Handler;
import android.os.Message;
import android.util.Log;

import com.kaolafm.kradio.lib.base.flavor.NetworkStatusInter;
import com.kaolafm.kradio.lib.utils.NetworkManager;

import okhttp3.OkHttpClient;
import okhttp3.Request;

import static com.kaolafm.kradio.lib.utils.NetworkManager.NETWORK_CONNECT;
import static com.kaolafm.kradio.lib.utils.NetworkManager.NETWORK_DEFAULT;
import static com.kaolafm.kradio.lib.utils.NetworkManager.NETWORK_NO;

public class NetworkStatusInterImpl implements NetworkStatusInter {
    @Override
    public boolean getNetStatus(Context context) {
        try {
            ConnectivityManager connectivity = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            if (connectivity != null) {
                boolean isNetOn = connectivity.getNetworkStatus();
                Log.d("NetworkStatusInterImpl", "status = " + isNetOn);
                if (isNetOn) {
                    if (NetworkManager.getInstance().getNetworkState() == NETWORK_DEFAULT) {//刚启动还未启动网络轮询
                        FakeNetworkCheckImpl impl = new FakeNetworkCheckImpl();
                        isNetOn = impl.isInternetAvailable(context);
                    } else {
                        isNetOn = !NetworkManager.getInstance().isNotHasNetwork();
                    }

                    Log.d("NetworkStatusInterImpl", "getNetworkState = " + isNetOn + "-getNetworkState-" + NetworkManager.getInstance().getNetworkState());
                }
                if (DebugImpl.hasNet()) {
                    isNetOn = true;
                    Log.d("NetworkStatusInterImpl", "status isNetOn= " + isNetOn);
                }
                return isNetOn;
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
        if (DebugImpl.isDebug()) {
            return true;
        } else {
            return false;
        }

    }

    @Override
    public String[] netAction() {
        return new String[]{/*ConnectivityManager.ACTION_NETWORK_CHANGED*/};
    }


}
