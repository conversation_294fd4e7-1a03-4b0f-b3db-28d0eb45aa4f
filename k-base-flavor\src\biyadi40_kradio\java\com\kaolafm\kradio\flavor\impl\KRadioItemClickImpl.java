package com.kaolafm.kradio.flavor.impl;

import com.kaolafm.kradio.lib.base.flavor.KRadioItemClickInter;
import com.kaolafm.opensdk.player.logic.PlayerManager;

public class KRadioItemClickImpl implements KRadioItemClickInter {
    @Override
    public void doItemClick(Object... args) {
        if (!PlayerManager.getInstance().isPlaying()) {
            PlayerManager.getInstance().play(true);
        }
    }
}
