package com.kaolafm.kradio.online.player.mvp;

import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.kradio.lib.base.mvp.IModel;
import com.kaolafm.kradio.player.http.PlayerRequest;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-04-17 12:40
 ******************************************/

public final class BroadcastDateListPresenter extends BasePresenter<IModel, BroadcastDateListView> {

    public BroadcastDateListPresenter(BroadcastDateListView view) {
        super(view);
    }

    /**
     * 获取广播日期分类
     */
    public void getBroadcastDate() {
        new PlayerRequest().getBroadcastDate(new HttpCallback<BroadcastDateData>() {
            @Override
            public void onSuccess(BroadcastDateData broadcastDateData) {
                if (broadcastDateData == null) {
                    onGetBroadcastDateError(-1, null);
                } else {
                    onGetBroadcastDateSuccess(broadcastDateData);
                }
            }

            @Override
            public void onError(ApiException e) {
                onGetBroadcastDateError(e.getCode(), e.getMessage());
            }
        });
    }

    private void onGetBroadcastDateSuccess(BroadcastDateData broadcastDateData) {
        if (mView == null) {
            return;
        }
        mView.onGetBroadcastDateSuccess(broadcastDateData);
    }

    private void onGetBroadcastDateError(int code, String msg) {
        if (mView == null) {
            return;
        }
        mView.onGetBroadcastDateError(code, msg);
    }
}
