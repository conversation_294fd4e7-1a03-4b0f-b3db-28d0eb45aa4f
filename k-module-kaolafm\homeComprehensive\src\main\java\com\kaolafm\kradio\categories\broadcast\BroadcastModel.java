package com.kaolafm.kradio.categories.broadcast;

import android.text.TextUtils;
import android.util.Log;


import com.kaolafm.kradio.lib.base.mvp.BaseModel;
import com.kaolafm.kradio.lib.bean.SubcategoryItemBean;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.broadcast.BroadcastRequest;
import com.kaolafm.opensdk.api.broadcast.ProgramDetails;
import com.kaolafm.opensdk.api.operation.OperationRequest;
import com.kaolafm.opensdk.api.operation.model.category.AlbumCategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.AudioDetailTbCategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.BroadcastCategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.CategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.FeatureCategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.InfoFragmentCategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.LiveProgramCategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.RadioCategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.RadioQQMusicCategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.TVCategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.VideoAlbumCategoryMember;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.utils.operation.OperationAssister;
import com.kaolafm.kradio.lib.bean.BroadcastRadioDetailData;
import com.trello.rxlifecycle3.LifecycleTransformer;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Queue;
import java.util.Timer;
import java.util.TimerTask;


/**
 * Created by kaolafm on 2018/4/25.
 */

public class BroadcastModel extends BaseModel {

    /**
     * 默认每页条数
     */
    private static final int PAGE_SIZE = 20;
    private final BroadcastRequest mBroadcastRequest;

    private String mCategoryId;

    private int mNextPageNum = 1;

    private int mHaveNext;

    private Queue<BroadcastRadioDetailData> broadcastDataList = new LinkedList<>();
    private Queue<String> requestTimeStr = new LinkedList<>();
    private Timer mTime;
    private long priod = 1000;//间隔时间
    public BroadcastModel() {
        mBroadcastRequest = new BroadcastRequest();
        mTime = new Timer();
    }

    public boolean haveNext() {
        return mHaveNext == Constants.HAVE_PAGE;
    }

    /**
     * 设置广播类型
     */
    public void setBroadcastCategoryType(int categoryId) {
        this.mCategoryId = String.valueOf(categoryId);
    }

    /**
     * 获取广播电台列表数据
     */
    public void requestBroadcastListData(LifecycleTransformer lifecycleTransformer, HttpCallback<List<BroadcastRadioDetailData>> callback) {
        OperationRequest operationRequest = new OperationRequest().bindLifecycle(lifecycleTransformer);
        operationRequest.getCategoryMemberList(mCategoryId, mNextPageNum, PAGE_SIZE,
                new HttpCallback<BasePageResult<List<CategoryMember>>>() {
                    @Override
                    public void onSuccess(BasePageResult<List<CategoryMember>> basePageResult) {
                        mHaveNext = basePageResult.getHaveNext();
                        mNextPageNum = basePageResult.getNextPage();
                        List<CategoryMember> dataList = basePageResult.getDataList();
                        List<BroadcastRadioDetailData> itemList = categoryMemberToBroadcastItem(dataList);
                        broadcastDataList.addAll(itemList);
                        if (callback != null) {
                            callback.onSuccess(itemList);
                        }
                    }

                    @Override
                    public void onError(ApiException e) {
                        if (callback != null) {
                            callback.onError(e);
                        }
                    }
                });
    }

    public void setPageNum(int pageNum) {
        mNextPageNum = pageNum;
    }

    private List<BroadcastRadioDetailData> categoryMemberToBroadcastItem(List<CategoryMember> categoryMemberList) {
        ArrayList<BroadcastRadioDetailData> list = new ArrayList<>();
        if (categoryMemberList != null) {
            String playingId = PlayerManagerHelper.getInstance().getCurPlayItem().getRadioId();
            for (int i = 0, size = categoryMemberList.size(); i < size; i++) {
                CategoryMember categoryMember = categoryMemberList.get(i);
                BroadcastRadioDetailData broadcastRadioDetailData = new BroadcastRadioDetailData();
                broadcastRadioDetailData.setName(categoryMember.getTitle());
                String freq = categoryMember.getSubtitle();
                broadcastRadioDetailData.setFreq(!TextUtils.isEmpty(freq) ? freq : "");
                broadcastRadioDetailData.setIcon(OperationAssister.getImage(categoryMember));
                if (categoryMember instanceof AlbumCategoryMember) {
                    // 人工运营分类成员:专辑。
                    broadcastRadioDetailData.setResourcesType(PlayerConstants.RESOURCES_TYPE_ALBUM);
                    AlbumCategoryMember albumMember = (AlbumCategoryMember) categoryMember;
                    Long broadcastId = albumMember.getAlbumId();
                    broadcastRadioDetailData.setBroadcastId(broadcastId);
                    broadcastRadioDetailData.setPlayTimes(albumMember.getPlayTimes());
                    broadcastRadioDetailData.setPlaying(TextUtils.equals(playingId, String.valueOf(broadcastId)));
                    list.add(broadcastRadioDetailData);
                } else if (categoryMember instanceof BroadcastCategoryMember) {
                    // 人工运营分类成员：在线广播
                    broadcastRadioDetailData.setResourcesType(PlayerConstants.RESOURCES_TYPE_BROADCAST);
                    BroadcastCategoryMember broadcastMember = (BroadcastCategoryMember) categoryMember;
                    Long broadcastId = broadcastMember.getBroadcastId();
                    broadcastRadioDetailData.setBroadcastId(broadcastId);
                    broadcastRadioDetailData.setPlayTimes(broadcastMember.getPlayTimes());
                    broadcastRadioDetailData.setPlaying(TextUtils.equals(playingId, String.valueOf(broadcastId)));
                    list.add(broadcastRadioDetailData);
                } else if (categoryMember instanceof FeatureCategoryMember) {
                    // 人工运营分类成员:专题。
                    broadcastRadioDetailData.setResourcesType(PlayerConstants.RESOURCES_TYPE_FEATURE);
                    FeatureCategoryMember featureMember = (FeatureCategoryMember) categoryMember;
                    Long broadcastId = featureMember.getFeatureId();
                    broadcastRadioDetailData.setBroadcastId(broadcastId);
                    broadcastRadioDetailData.setPlayTimes(featureMember.getPlayTimes());
                    broadcastRadioDetailData.setPlaying(TextUtils.equals(playingId, String.valueOf(broadcastId)));
                    list.add(broadcastRadioDetailData);
                } else if (categoryMember instanceof LiveProgramCategoryMember) {
                    // 人工运营分类成员:直播节目
                    broadcastRadioDetailData.setResourcesType(PlayerConstants.RESOURCES_TYPE_LIVING);
                    LiveProgramCategoryMember liveProgramMember = (LiveProgramCategoryMember) categoryMember;
                    Long broadcastId = liveProgramMember.getLiveProgramId();
                    broadcastRadioDetailData.setBroadcastId(broadcastId);
                    broadcastRadioDetailData.setPlayTimes(0);// 此类型无 playTimes 字段
                    broadcastRadioDetailData.setPlaying(TextUtils.equals(playingId, String.valueOf(broadcastId)));
                    list.add(broadcastRadioDetailData);
                } else if (categoryMember instanceof RadioCategoryMember) {
                    // 人工运营分类成员:AI电台.
                    broadcastRadioDetailData.setResourcesType(PlayerConstants.RESOURCES_TYPE_RADIO);
                    RadioCategoryMember radioMember = (RadioCategoryMember) categoryMember;
                    Long broadcastId = radioMember.getRadioId();
                    broadcastRadioDetailData.setBroadcastId(broadcastId);
                    broadcastRadioDetailData.setPlayTimes(radioMember.getPlayTimes());
                    broadcastRadioDetailData.setPlaying(TextUtils.equals(playingId, String.valueOf(broadcastId)));
                    list.add(broadcastRadioDetailData);
                } else if (categoryMember instanceof TVCategoryMember) {
                    // 人工运营分类成员：听电视
                    broadcastRadioDetailData.setResourcesType(PlayerConstants.RESOURCES_TYPE_TV);
                    TVCategoryMember tvMember = (TVCategoryMember) categoryMember;
                    Long broadcastId = tvMember.getListenTVid();
                    broadcastRadioDetailData.setBroadcastId(broadcastId);
                    broadcastRadioDetailData.setPlayTimes(tvMember.getPlayTimes());
                    broadcastRadioDetailData.setPlaying(TextUtils.equals(playingId, String.valueOf(broadcastId)));
                    list.add(broadcastRadioDetailData);
                } else if (categoryMember instanceof AudioDetailTbCategoryMember) {
                    // 人工运营分类成员:碎片。
                    broadcastRadioDetailData.setResourcesType(PlayerConstants.RESOURCES_TYPE_AUDIO);
                    AudioDetailTbCategoryMember audioDetailTbMember = (AudioDetailTbCategoryMember) categoryMember;
                    Long broadcastId = audioDetailTbMember.getAudioId();
                    broadcastRadioDetailData.setBroadcastId(broadcastId);
                    broadcastRadioDetailData.setPlayTimes(audioDetailTbMember.getPlayTimes().intValue());
                    broadcastRadioDetailData.setPlaying(TextUtils.equals(playingId, String.valueOf(broadcastId)));
                    list.add(broadcastRadioDetailData);
                }
//                else if (categoryMember instanceof RadioQQMusicCategoryMember) {
//                    // 人工运营分类成员:QQ音乐电台
//                    broadcastRadioDetailData.setResourcesType(PlayerConstants.RESOURCES_TYPE_QQ_MUSIC);
//                    RadioQQMusicCategoryMember radioQQMusicMember = (RadioQQMusicCategoryMember) categoryMember;
//                    Long broadcastId = radioQQMusicMember.getRadioQQMusicId();
//                    broadcastRadioDetailData.setBroadcastId(broadcastId);
//                    broadcastRadioDetailData.setPlayTimes(0);// 此类型无 playTimes 字段
//                    broadcastRadioDetailData.setPlaying(TextUtils.equals(playingId, String.valueOf(broadcastId)));
//                    list.add(broadcastRadioDetailData);
//                }
//                else if (categoryMember instanceof InfoFragmentCategoryMember) {
//                    // 人工运营分类成员:资讯碎片。
//                    broadcastRadioDetailData.setResourcesType(PlayerConstants.RESOURCES_TYPE_INFO_FRAGMENT);
//                    InfoFragmentCategoryMember infoFragmentMember = (InfoFragmentCategoryMember) categoryMember;
//                    Long broadcastId = infoFragmentMember.getAlbumId();
//                    broadcastRadioDetailData.setBroadcastId(broadcastId);
//                    broadcastRadioDetailData.setPlayTimes(infoFragmentMember.getPlayTimes());
//                    broadcastRadioDetailData.setPlaying(TextUtils.equals(playingId, String.valueOf(broadcastId)));
//                    list.add(broadcastRadioDetailData);
//                }
//                else if (categoryMember instanceof VideoAlbumCategoryMember) {
//                    // 人工运营分类成员:视频专辑。
//                    broadcastRadioDetailData.setResourcesType(PlayerConstants.RESOURCES_TYPE_VIDEO_ALBUM);
//                    VideoAlbumCategoryMember videoAlbumMember = (VideoAlbumCategoryMember) categoryMember;
//                    Long broadcastId = videoAlbumMember.getAlbumId();
//                    broadcastRadioDetailData.setBroadcastId(broadcastId);
//                    broadcastRadioDetailData.setPlayTimes(videoAlbumMember.getPlayTimes());
//                    broadcastRadioDetailData.setPlaying(TextUtils.equals(playingId, String.valueOf(broadcastId)));
//                    list.add(broadcastRadioDetailData);
//                }
//                else if (categoryMember instanceof VideoFragmentCategoryMember) {
//                    // 人工运营分类成员:视频碎片。
//                    // ? RESOURCES_TYPE_INFO_FRAGMENT
//                    broadcastRadioDetailData.setResourcesType(PlayerConstants.RESOURCES_TYPE_INFO_FRAGMENT);
//                    VideoFragmentCategoryMember videoFragmentMember = (VideoFragmentCategoryMember) categoryMember;
//                    Long broadcastId = videoFragmentMember.getAlbumId();
//                    broadcastRadioDetailData.setBroadcastId(broadcastId);
//                    broadcastRadioDetailData.setPlayTimes(videoFragmentMember.getPlayTimes());
//                    broadcastRadioDetailData.setPlaying(TextUtils.equals(playingId, String.valueOf(broadcastId)));
//                    list.add(broadcastRadioDetailData);
//                }
            }
        }
        return list;
    }

    @Override
    public void destroy() {

    }
    public void getBroadcastProgramList(long broadcastId, String data, HttpCallback<List<ProgramDetails>> callback) {
        mBroadcastRequest.getBroadcastProgramList(broadcastId, data,new HttpCallback<List<ProgramDetails>>() {
            @Override
            public void onSuccess(List<ProgramDetails> list) {
                callback.onSuccess(list);
            }

            @Override
            public void onError(ApiException e) {
                callback.onError(e);
            }
        });
    }
    public void schedulProgramListRequest(int delay,HttpCallback<List<ProgramDetails>> callback){
        // 检查Timer是否可用，这是导致空指针异常的根本原因
        if (mTime == null) {
            Log.w("BroadcastModel", "Timer is null, cannot schedule program list request. Timer may have been cancelled.");
            return;
        }

        TimerTask mTimeTask = new TimerTask(){
            @Override
            public void run() {
                if(broadcastDataList != null){
                    Log.i("broadcastDataList:",broadcastDataList.size()+"");
                    if(broadcastDataList.size() > 0 ){
                        BroadcastRadioDetailData broadcastRadioDetailData = broadcastDataList.poll();
                        if(broadcastRadioDetailData != null){
                            getBroadcastProgramList(broadcastRadioDetailData.getBroadcastId(),null, callback);
                        }
                    }
                }
            }
        };

        try {
            mTime.schedule(mTimeTask,delay * 1000,priod);
        } catch (IllegalStateException e) {
            Log.w("BroadcastModel", "Timer was cancelled, cannot schedule task", e);
        } catch (Exception e) {
            Log.e("BroadcastModel", "Error scheduling timer task", e);
        }
    }

    public void cancelSchedule(){
        mTime.cancel();
        mTime = null;
    }
}
