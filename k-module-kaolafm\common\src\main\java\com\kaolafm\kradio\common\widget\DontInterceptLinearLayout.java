package com.kaolafm.kradio.common.widget;

import android.content.Context;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.widget.LinearLayout;

/**
 * <AUTHOR>
 * @date 2018/5/7
 */

public class DontInterceptLinearLayout extends LinearLayout {

    public DontInterceptLinearLayout(Context context) {
        super(context);
    }

    public DontInterceptLinearLayout(Context context,
            @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public DontInterceptLinearLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        Log.i("DontInterceptLinear", "onInterceptTouchEvent: "+ev.getAction()+", onInterceptTouchEvent="+super.onInterceptTouchEvent(ev));
        return super.onInterceptTouchEvent(ev);
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        if (ev.getAction() == MotionEvent.ACTION_UP){
            return false;
        }
        Log.i("DontInterceptLinear", "dispatchTouchEvent: ev="+ev.getAction()+", super.dispatchTouchEvent(ev)="+super.dispatchTouchEvent(ev));
        return super.dispatchTouchEvent(ev);
    }
}
