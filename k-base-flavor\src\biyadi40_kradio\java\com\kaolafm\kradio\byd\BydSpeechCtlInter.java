package com.kaolafm.kradio.byd;

import android.content.Intent;

public interface BydSpeechCtlInter {

    //----------------------支持的功能--------------------

    void book();    //订阅

    void unBook();  //取消订阅

    void jumpTo(int secondsPosition);  //跳转

    void forward(int seconds); //快进

    void rewind(int seconds);  //快退

    void quit(); //退出

    void quality(String quality); //音质

    void search(Intent intent); //搜索

    void playRecommend(); //播放推荐

    void playLately();  //播放最近

    void playBook(); //播放订阅

    //----------------------暂不支持的功能--------------------

    void collect(); //收藏

    void unCollect(); //取消收藏

    void playFavorite(); //播放收藏

    void speedAdjust(); //倍速

    void playLike(); //播放喜欢的节目

    void download(); //下载

    void playMode(); //播放模式

    //---------------------无此功能----------------------

    void openLyric(); //打开歌词

    void closeLyric();//关闭歌词









    void sendCmdResult(String action, String code, String msg); //发送语音指令执行的结果

}
