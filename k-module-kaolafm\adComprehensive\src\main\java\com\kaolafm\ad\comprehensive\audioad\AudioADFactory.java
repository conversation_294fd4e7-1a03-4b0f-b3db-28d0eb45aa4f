package com.kaolafm.ad.comprehensive.audioad;

import android.util.SparseArray;

import com.kaolafm.ad.AdConstant;
import com.kaolafm.ad.comprehensive.control.KradioAdSceneConstants;

/**
 * @ClassName AudioADFactory
 * @Description 音频广告类型工厂
 * <AUTHOR>
 * @Date 2020-03-12 10:43
 * @Version 1.0
 */
public class AudioADFactory {

    private static SparseArray<BaseAudioAD> AD_PLAYER_ARRAY = new SparseArray<>();

    static {
        AD_PLAYER_ARRAY.put(KradioAdSceneConstants.SUB_TYPE_SWITCH_PROGROM, new AudioStartAudioAD());
        AD_PLAYER_ARRAY.put(KradioAdSceneConstants.AD_TYPE_SWITCH_RADIO_AUDIO, new AudioEndAudioAD());
        AD_PLAYER_ARRAY.put(KradioAdSceneConstants.SUB_TYPE_SPRAND_SCREEN, new FlashAudioAD());
        AD_PLAYER_ARRAY.put(AdConstant.TYPE_TIMED_ADVERT, new TimerAudioAD());
    }

    private AudioADFactory() {
    }

    public static BaseAudioAD getAdPlayer(int type) {
        BaseAudioAD baseAudioAD = AD_PLAYER_ARRAY.get(type);
        if (baseAudioAD == null) {
            baseAudioAD = new TimerAudioAD();
        }
        return baseAudioAD;
    }
}
