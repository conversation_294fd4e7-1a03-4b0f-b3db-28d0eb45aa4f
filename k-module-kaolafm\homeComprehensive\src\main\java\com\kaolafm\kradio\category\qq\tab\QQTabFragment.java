package com.kaolafm.kradio.category.qq.tab;

import com.kaolafm.kradio.category.base.BaseTabFragment;
import com.kaolafm.kradio.category.base.TabContract;
import com.kaolafm.kradio.lib.bean.SubcategoryItemBean;
import java.util.List;

/**
 * <AUTHOR>
 **/
public class QQTabFragment extends BaseTabFragment {

    @Override
    public void showContent(String[] titles, List<SubcategoryItemBean> itemBeans, int showIndex) {

    }

    @Override
    protected int getLayoutId_Tow() {
        return 0;
    }

    @Override
    protected TabContract.IPresenter createPresenter() {
        return new QQTabPresenter(this);
    }

}
