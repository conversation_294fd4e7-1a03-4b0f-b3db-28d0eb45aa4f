package com.kaolafm.ad.comprehensive.control;

/**
 * 广告曝光场景id设置
 * 应在综合版和在线电台版分别进行场景id的设置
 */
public class AdExposeScene {
    protected static int mSprendScene = KradioAdSceneConstants.SPREND_SCENE;
    protected static int mAlbumScene = KradioAdSceneConstants.ALBUM_SCENE;
    protected static int mRadioScene = KradioAdSceneConstants.RADIO_SCENE;
    protected static int mBrandScene = KradioAdSceneConstants.BRAND_SCENE;
    protected static int mTimerScene = KradioAdSceneConstants.TIMER_SCENE;


    public static int getSprendScene() {
        return mSprendScene;
    }

    public static void setSprendScene(int sprendScene) {
        mSprendScene = sprendScene;
    }

    public static int getAlbumScene() {
        return mAlbumScene;
    }

    public static void setAlbumScene(int albumScene) {
        mAlbumScene = albumScene;
    }

    public static int getRadioScene() {
        return mRadioScene;
    }

    public static void setRadioScene(int radioScene) {
        mRadioScene = radioScene;
    }

    public static int getBrandScene() {
        return mBrandScene;
    }

    public static void setBrandScene(int brandScene) {
        mBrandScene = brandScene;
    }

    public static int getTimerScene() {
        return mTimerScene;
    }

    public static void setTimerScene(int timerScene) {
        mTimerScene = timerScene;
    }
}
