package com.kaolafm.kradio.flavor.impl;

import android.annotation.SuppressLint;
import android.content.Context;
import android.media.AudioManager;
import android.util.Log;

import com.kaolafm.kradio.lib.base.flavor.KRadioAddPlayerListenerInter;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

/**
 * author : wxb
 * date   : 2021/10/27
 * desc   :同步首页播放状态 上层对播放状态有单独处理
 */
public class KRadioAddPlayerListenerImpl implements KRadioAddPlayerListenerInter {
    private static final String TAG = "KRadioAddPlayerListenerImpl";
    AudioManager mAudioManager;

    @SuppressLint("LongLogTag")
    @Override
    public void initPlayerListener(Context context) {
        if (null == mAudioManager) {
            mAudioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
        }
        if (null == stateListener) {
            stateListener = new BasePlayStateListener() {
                @Override
                public void onIdle(PlayItem playItem) {
                    super.onIdle(playItem);
                    setIdle();
                }

                @Override
                public void onPlayerPreparing(PlayItem playItem) {
                    super.onPlayerPreparing(playItem);
                    setIdle();
                }

                @Override
                public void onPlayerPlaying(PlayItem playItem) {
                    super.onPlayerPlaying(playItem);
                    setUnmute();
                }

                @Override
                public void onPlayerPaused(PlayItem playItem) {
                    super.onPlayerPaused(playItem);
                    setUnmute();
                }

                @Override
                public void onPlayerFailed(PlayItem playItem, int what, int extra) {
                    super.onPlayerFailed(playItem, what, extra);
                    setUnmute();
                }

                @Override
                public void onPlayerEnd(PlayItem playItem) {
                    super.onPlayerEnd(playItem);
                    setUnmute();
                }
            };
            Log.i(TAG, "addPlayControlStateCallback start");
            PlayerManager.getInstance().addPlayControlStateCallback(stateListener);
        }
    }

    BasePlayStateListener stateListener;

    @SuppressLint("LongLogTag")
    @Override
    public void removePlayerListener(Context context) {
        if (null != stateListener) {
            Log.i(TAG, "removePlayerListener start");
            PlayerManager.getInstance().removePlayControlStateCallback(stateListener);
        }
    }

    @SuppressLint("LongLogTag")
    public void setUnmute() {
        if (null != mAudioManager) {
            Log.i(TAG, "setUnmute start");
            mAudioManager.setParameters("jpcc.music=unmute");
        }
    }

    @SuppressLint("LongLogTag")
    public void setIdle() {
        if (null != mAudioManager) {
            Log.i(TAG, "setIdle start");
            mAudioManager.setParameters("jpcc.music=setPlayStatus.idle");
        }
    }
}
