package com.kaolafm.kradio.flavor.carnetwork.api;

import com.kaolafm.kradio.flavor.carnetwork.carnetworkutils.AuthConstants;
import com.kaolafm.opensdk.api.BaseResult;

import io.reactivex.Single;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.Path;

interface AuthApiServices {

    //    @GET("/tfsPlatform-INF/service/authbyvin2/{json}")
    @Headers({"Domain-Name:dongfengxiaokang"})
    @GET(AuthConstants.REQUEST_GET_AUTH)
    Single<BaseResult> getAuthInfo(@Path("json") String json);
}
