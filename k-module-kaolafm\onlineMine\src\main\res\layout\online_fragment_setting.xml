<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:scrollbars="none"
    tools:context="com.kaolafm.kradio.online.mine.page.OnlineSettingFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:paddingLeft="@dimen/m94"
        android:paddingRight="@dimen/m50"
        android:paddingTop="@dimen/m61"
        android:paddingBottom="@dimen/m50"
        >

        <androidx.viewpager.widget.ViewPager
            android:id="@+id/view_page"
            android:layout_width="0dp"
            android:layout_height="0dp" />

        <androidx.viewpager.widget.ViewPager
            android:id="@+id/view_page2"
            android:layout_width="0dp"
            android:layout_height="0dp" />

        <LinearLayout
            android:id="@+id/toneQualityTitle"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="@dimen/m28"
                android:layout_height="@dimen/m28"
                android:layout_gravity="center_vertical"
                android:src="@drawable/setting_acoustics_icon" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/x12"
                android:text="@string/mine_setting_acoustics_text"
                android:textStyle="bold"
                android:textColor="@color/online_setting_title_text_color"
                android:textSize="@dimen/m24" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/soundQualitiesParentView"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/m88"
            android:layout_marginTop="@dimen/m20"
            android:background="@drawable/tab_setting_bg"
            android:gravity="center"
            android:orientation="horizontal"
            android:visibility="gone">

        </LinearLayout>


        <LinearLayout
            android:id="@+id/skinTitleView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/m60"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="@dimen/m28"
                android:layout_height="@dimen/m28"
                android:layout_gravity="center_vertical"
                android:src="@drawable/setting_theme_icon" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/m12"
                android:text="@string/mine_setting_theme_text"
                android:textStyle="bold"
                android:textColor="@color/online_setting_title_text_color"
                android:textSize="@dimen/m24" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/mine_setting_theme_ll"
            android:layout_width="@dimen/m296"
            android:layout_height="@dimen/m60"
            android:layout_marginTop="@dimen/m20"
            android:background="@drawable/tab_setting_bg"
            android:gravity="center"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/mine_setting_theme_a_ll"
                android:layout_width="@dimen/m147"
                android:layout_height="@dimen/m59"
                tools:background="@drawable/tab_setting_indicator_bg"
                android:gravity="center"
                android:orientation="vertical"
               >

                <TextView
                    android:id="@+id/mine_setting_theme_a_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/mine_setting_theme_text3"
                    android:textStyle="bold"
                    android:textColor="@color/online_setting_tab_text_color"
                    android:textSize="@dimen/m24" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/mine_setting_theme_b_ll"
                android:layout_width="@dimen/m147"
                android:layout_height="@dimen/m59"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/mine_setting_theme_b_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/mine_setting_theme_text4"
                    android:textStyle="bold"
                    android:textColor="@color/online_setting_tab_text_color_not"
                    android:textSize="@dimen/m24" />
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/m60"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="@dimen/m28"
                android:layout_height="@dimen/m28"
                android:layout_gravity="center_vertical"
                android:src="@drawable/setting_message_icon" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/m12"
                android:text="@string/mine_setting_message_text"
                android:textStyle="bold"
                android:textColor="@color/online_setting_title_text_color"
                android:textSize="@dimen/m24" />
        </LinearLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/m22"
            android:layout_marginRight="@dimen/m170">

            <TextView
                android:id="@+id/rl_a_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/mine_setting_message_play_text"
                android:textStyle="bold"
                android:textColor="@color/online_setting_msg_text_color"
                android:textSize="@dimen/m24" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/rl_a_tv"
                android:text=""
                android:textColor="@color/online_setting_msg_text_color"
                android:textSize="@dimen/m24" />

            <Switch
                android:id="@+id/setting_msg_switch"
                android:layout_width="@dimen/m80"
                android:layout_height="@dimen/m40"
                android:layout_alignParentRight="true"
                android:thumb="@drawable/setting_switch_thumb"
                android:track="@drawable/setting_switch_selector" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/m22"
            android:layout_marginRight="@dimen/m170">

            <TextView
                android:id="@+id/rl_b_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/mine_setting_message_helper_text"
                android:textStyle="bold"
                android:textColor="@color/online_setting_msg_text_color"
                android:textSize="@dimen/m24" />

            <TextView
                android:id="@+id/rl_b_tips_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/rl_b_tv"
                android:text="@string/mine_setting_message_helper_tips_text"
                android:textColor="@color/online_setting_msg_tips_text_color"
                android:textSize="@dimen/m20" />

            <Switch
                android:id="@+id/setting_msg_helper_switch"
                android:layout_width="@dimen/m80"
                android:layout_height="@dimen/m40"
                android:layout_alignParentRight="true"
                android:thumb="@drawable/setting_switch_thumb"
                android:track="@drawable/setting_switch_selector" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/m22"
            android:layout_marginRight="@dimen/m170">

            <TextView
                android:id="@+id/rl_c_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/mine_setting_message_travel_text"
                android:textStyle="bold"
                android:textColor="@color/online_setting_msg_text_color"
                android:textSize="@dimen/m24" />

            <TextView
                android:id="@+id/rl_c_tips_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/rl_c_tv"
                android:text="@string/mine_setting_message_travel_tips_text"
                android:textColor="@color/online_setting_msg_tips_text_color"
                android:textSize="@dimen/m20" />

            <Switch
                android:id="@+id/setting_msg_travel_switch"
                android:layout_width="@dimen/m80"
                android:layout_height="@dimen/m40"
                android:layout_alignParentRight="true"
                android:thumb="@drawable/setting_switch_thumb"
                android:track="@drawable/setting_switch_selector" />
        </RelativeLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/m54"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="@dimen/m28"
                android:layout_height="@dimen/m28"
                android:layout_gravity="center_vertical"
                android:src="@drawable/setting_back_msg_icon" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/x12"
                android:text="@string/mine_setting_back_msg_text"
                android:textStyle="bold"
                android:textColor="@color/online_setting_title_text_color"
                android:textSize="@dimen/m24" />
        </LinearLayout>


        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/m25"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="@dimen/m120"
                android:layout_height="@dimen/m120"
                android:src="@drawable/settong_msg_code_pic" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:layout_marginLeft="@dimen/m23"
                android:text="@string/mine_setting_back_msg_text2"
                android:textColor="@color/online_setting_title_text_color"
                android:textSize="@dimen/m20" />
        </LinearLayout>
    </LinearLayout>
</ScrollView>