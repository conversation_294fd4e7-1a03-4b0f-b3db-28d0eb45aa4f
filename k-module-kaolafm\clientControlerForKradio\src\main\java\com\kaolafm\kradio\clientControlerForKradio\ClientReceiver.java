package com.kaolafm.kradio.clientControlerForKradio;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.kaolafm.kradio.common.event.StopAudioEBData;
import com.kaolafm.kradio.lib.base.AppManager;

import org.greenrobot.eventbus.EventBus;

/**
 * Created by mengxn on 16-12-23.
 */
public class ClientReceiver extends BroadcastReceiver {
    private static final String TAG = "client.receiver";

    @Override
    public void onReceive(Context context, Intent intent) {
        if (intent == null) {
            return;
        }
        Log.i(TAG, "onReceive=" + intent.getAction());
        if ("com.kaolafm.sdk.client".equals(intent.getAction())) {
            if ("extra_command_exit_app".equals(intent.getStringExtra("extra_key_command"))) {
                //ExitCommand.create().execute(context);
                // TODO 需处理只退出APP不停止音频逻辑
                boolean canStopAudio = intent.getBooleanExtra("extra_stop_audio_command_exit_app", true);
                StopAudioEBData stopAudioEBData = new StopAudioEBData();
                stopAudioEBData.canStopAudio = canStopAudio;
                // 发送EventBus消息如果延迟比较严重，可能会存在canStopAudio不能起到应有的作用
                EventBus.getDefault().post(stopAudioEBData);
//                if (canStopAudio) {
//                    PlayerManager.getInstance().destroy();
//                }
                AppManager.getInstance().killAll();
                Log.i(TAG, "onReceive: exit app.");
            }
        }
    }
}
