package com.kaolafm.kradio.lib.utils;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-12-11 12:05
 ******************************************/

public final class ClazzUtil {

    private static Map<String, Class> mClasses = new HashMap<>();

    private static Map<String, Object> mClassesInstance = new HashMap<>();

    /**
     * 实例化一个类
     *
     * @param clazzName
     * @return
     */
    public static <T> T getClazzInstance(String clazzName) {
        Object obj = null;
        try {
            Class clazz = mClasses.get(clazzName);
            if (clazz == null) {
                clazz = Class.forName(clazzName);
                mClasses.put(clazzName, clazz);
            }
            obj = clazz.newInstance();
        } catch (ClassNotFoundException ignored) {
        } catch (IllegalAccessException ignored) {
        } catch (InstantiationException ignored) {
        } catch (NoClassDefFoundError ignored) {
        }
        return (T) obj;
    }

    /**
     * 创建一个单例的实例 注意销毁
     * @param clazzName
     * @param <T>
     * @return
     */
    public synchronized static <T> T getClazzSingleInstance(String clazzName) {
        Object obj = null;
        if (mClassesInstance.containsKey(clazzName)) {
            obj = mClassesInstance.get(clazzName);
        }
        if (obj == null) {
            obj = getClazzInstance(clazzName);
            mClassesInstance.put(clazzName, obj);
        }
        return (T) obj;
    }

    @SuppressWarnings("unchecked")
    public static Object invoke(Class clazz, Object target, String name, Object... args)
            throws Exception {
        Class[] parameterTypes = null;
        if (args != null) {
            parameterTypes = new Class[args.length];
            for (int i = 0; i < args.length; i++) {
                parameterTypes[i] = args[i].getClass();
            }
        }

        Method method = clazz.getDeclaredMethod(name, parameterTypes);
        method.setAccessible(true);
        return method.invoke(target, args);
    }
}
