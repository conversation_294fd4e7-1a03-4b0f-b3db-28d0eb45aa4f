package com.kaolafm.kradio.home.comprehensive.playerbar;

import androidx.annotation.StringRes;
import android.util.Log;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.lib.bean.BroadcastRadioSimpleData;
import com.kaolafm.kradio.lib.player.contant.BroadcastStatus;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.home.comprehensive.item.BroadcastCell;
import com.kaolafm.kradio.lib.common.ResType;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.UrlUtil;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.api.media.model.AlbumDetails;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.api.operation.model.column.BroadcastDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.ColumnContent;
import com.kaolafm.opensdk.api.operation.model.column.TVDetailColumnMember;
import com.kaolafm.opensdk.player.logic.model.item.RadioPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

import java.util.ArrayList;


/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: ComprehensivePlayerHelper.java
 *                                                                  *
 * Created in 2018/4/24 下午6:09
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
public class ComprehensivePlayerHelper {
    /**
     * 电台标签最长
     */
    private static final int MAX_SUB_TAG_LEN = 8;
    private static final String SUB_TAG_REPLACE_STRING = "...";

    public static final String TAG = "player.helper";

    /**
     * 判断是否直播中
     *
     * @return
     */
    public static boolean isLiving() {
        boolean playerEnable = PlayerManagerHelper.getInstance().isLivingPlayer();
        return playerEnable;
    }

    public static String getPlayFlagType(PlayItem playItem) {
        String flagType = null;
        int type = playItem.getType();
        @StringRes int stringRes = -1;
        switch (type) {
            case PlayerConstants.RESOURCES_TYPE_LIVING: {
                stringRes = -1;
            }
            break;
            case PlayerConstants.RESOURCES_TYPE_LIVE_STREAM: {
                if (playItem.isLiving()) {
                    stringRes = R.string.comprehensive_playerbar_state_living;
                } else {
                    stringRes = R.string.comprehensive_playerbar_state_playback;
                }
            }
            break;
            case PlayerConstants.RESOURCES_TYPE_BROADCAST:
            case PlayerConstants.RESOURCES_TYPE_TV: {
                if (playItem.isLiving()) {
                    stringRes = R.string.comprehensive_playerbar_state_living;
                } else {
                    if (BroadcastStatus.BROADCAST_STATUS_PLAYBACK == playItem.getStatus()) {
                        stringRes = R.string.comprehensive_playerbar_state_playback;
                    }
                }
            }
            break;
            case PlayerConstants.RESOURCES_TYPE_ALBUM:
                if (playItem.getBuyType() == AudioDetails.BUY_TYPE_AUDIO && playItem.getBuyStatus() == AlbumDetails.BUY_STATUS_PURCHASE) {
                    stringRes = R.string.comprehensive_playerbar_state_purchased;
                } else if (playItem.getBuyType() == AudioDetails.BUY_TYPE_AUDITION && playItem.getBuyStatus() == AlbumDetails.BUY_STATUS_NOT_PURCHASE) {
                    stringRes = R.string.comprehensive_playerbar_state_audition;
                }
                break;
            default:
                break;
        }
        if (stringRes != -1) {
            flagType = ResUtil.getString(stringRes);
            return flagType;
        }
        if (playItem.getType() == PlayerConstants.RESOURCES_TYPE_RADIO) {
            //智能电台
            RadioPlayItem radioPlayItem = (RadioPlayItem) playItem;
            String subTag = radioPlayItem.getRadioInfoData().getRadioSubTag();
            if (!StringUtil.isEmpty(subTag)) {
                String subTagTemp = formatSubTag(subTag);
                if (!StringUtil.isEmpty(subTagTemp)) {
                    flagType = subTagTemp;
                }
            }
        }
        return flagType;
    }

    private static String formatSubTag(String subTag) {
        /**
         * 去掉半角空格
         */
        String subTemp = subTag.replace(" ", "");
        /**
         * 去掉全角空格
         */
        String subTemp1 = subTemp.replace("　", "").trim();

        if (StringUtil.isEmpty(subTemp1)) {
            return null;
        }
        int len = subTemp1.length();
        if (len <= MAX_SUB_TAG_LEN) {
            return subTemp1;
        }
        String tag = subTemp1.substring(0, MAX_SUB_TAG_LEN);
        return StringUtil.join(tag, SUB_TAG_REPLACE_STRING);
    }

    public static void play(HomeCell homeCell) {
        Log.i(TAG, "clickToPlay homeCell = " + homeCell);
        if (homeCell == null) {
            return;
        }
        if (homeCell.resType== ResType.BROADCAST_TYPE||homeCell.resType== ResType.TV_TYPE) {
            handBroadcasts(homeCell);
        }
        Log.i(TAG, "homeCell id = " + homeCell.playId + "   " + homeCell.resType);
        PlayerManagerHelper.getInstance().start(String.valueOf(homeCell.playId), homeCell.resType);
    }

    private static void handBroadcasts(HomeCell homeCell) {
        if (homeCell.getContentList() != null && homeCell.getContentList().size()>1){
            ArrayList<BroadcastRadioSimpleData> broadcastList = new ArrayList<>();
            for (int i = 0, size = homeCell.getContentList().size(); i < size; i++) {
                BroadcastRadioSimpleData brsd = cellToBroadcastItem(homeCell.contentList.get(i));
                if (brsd != null) {
                    broadcastList.add(brsd);
                }
            }
            PlayerManagerHelper.getInstance().addBroadcastRadioSimpleItems(broadcastList);
        }



//        Card parent = homeCell.parent;
//        if (parent != null) {
//            List<? extends Cell> childes = parent.childes;
//            if (!ListUtil.isEmpty(childes)) {
//                ArrayList<BroadcastRadioSimpleData> broadcastList = new ArrayList<>();
//                for (int i = 0, size = childes.size(); i < size; i++) {
//                    BroadcastRadioSimpleData brsd = cellToBroadcastItem((HomeCell) childes.get(i));
//                    if (brsd != null) {
//                        broadcastList.add(brsd);
//                    }
//                }
//                PlayerManagerHelper.getInstance().addBroadcastRadioSimpleItems(broadcastList);
//            }
//        }
    }

    private static BroadcastRadioSimpleData cellToBroadcastItem(ColumnContent content) {
        BroadcastRadioSimpleData brsd = null;
        if (content instanceof BroadcastDetailColumnMember) {
            brsd = new BroadcastRadioSimpleData();
            brsd.setBroadcastId(Long.parseLong(content.getId()));
            brsd.setImg(UrlUtil.getCardPicUrl(content.getImageFiles()));
//            String freq = content.getFreq() == null ? "" : content.getFreq();
//            brsd.setName(content.getTitle() + "  " + freq);
            brsd.setResType(ResType.BROADCAST_TYPE);
        } else if (content instanceof TVDetailColumnMember) {
            brsd = new BroadcastRadioSimpleData();
            brsd.setBroadcastId(Long.parseLong(content.getId()));
            brsd.setImg(UrlUtil.getCardPicUrl(content.getImageFiles()));
//            String freq = content.getFreq() == null ? "" : content.getFreq();
//            brsd.setName(content.getTitle() + "  " + freq);
            brsd.setResType(ResType.TV_TYPE);
        }

        return brsd;
    }
    private static BroadcastRadioSimpleData cellToBroadcastItem(HomeCell cell) {
        BroadcastRadioSimpleData brsd = null;
        if (cell instanceof BroadcastCell) {
            brsd = new BroadcastRadioSimpleData();
            brsd.setBroadcastId(cell.playId);
            brsd.setImg(cell.imageUrl);
            String freq = ((BroadcastCell) cell).freq == null ? "" : ((BroadcastCell) cell).freq;
            brsd.setName(cell.name + "  " + freq);
        }
        return brsd;
    }

    static public void printPlayitem(String aTAG, PlayItem item) {
        Log.d(aTAG, "playitem name=" + item.getTitle()
                + " type=" + item.getType()
                + " radioId=" + item.getRadioId()
                + " audioId=" + item.getAudioId());
    }

}
