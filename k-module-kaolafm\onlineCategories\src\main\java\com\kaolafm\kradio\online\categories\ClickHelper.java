package com.kaolafm.kradio.online.categories;

import com.kaolafm.kradio.common.SubcategoryItemBean;
import com.kaolafm.kradio.common.report.ReportParamUtil;
import com.kaolafm.kradio.common.report.ReportUtil;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

/**
 * <AUTHOR>
 **/
public class ClickHelper {


    public static void onClick(SubcategoryItemBean subcategoryItemBean, int position) {
        onClickBroadcast(subcategoryItemBean, position);
    }

    private static void onClickBroadcast(SubcategoryItemBean subcategoryItemBean, int position) {
        if (PlayerManagerHelper.getInstance().isPlayCurrentRadio(String.valueOf(subcategoryItemBean.getId()))) {
            return;
        }
        switch (subcategoryItemBean.getItemType()) {
            case SubcategoryItemBean.TYPE_ITEM_BROADCAST_CATEGORY:
//                reportContentClickEvent(subcategoryItemBean);
//                Bundle bundle = new Bundle();
//                bundle.putInt("id", (int) subcategoryItemBean.getId());
//                bundle.putString("name", subcategoryItemBean.getName());
//                EventBus.getDefault().post(new PagerJumpEvent(PagerJumpEvent.PAGE_BROADCAST_LIST, bundle));
//                break;

            case SubcategoryItemBean.TYPE_ITEM_BROADCAST_LOCAL:
                reportContentClickEvent(subcategoryItemBean, Constants.PAGE_ID_CATEGORIES_BROADCAST,position);
                PlayerManagerHelper.getInstance().start(String.valueOf(subcategoryItemBean.getId()), PlayerConstants.RESOURCES_TYPE_BROADCAST);
                break;
            case SubcategoryItemBean.TYPE_ITEM_TV:
                reportContentClickEvent(subcategoryItemBean, Constants.PAGE_ID_CATEGORIES_TV,position);
                PlayerManagerHelper.getInstance().start(String.valueOf(subcategoryItemBean.getId()), PlayerConstants.RESOURCES_TYPE_TV);
                break;
            case SubcategoryItemBean.TYPE_ITEM_ALBUM: {
                //订阅的item,点击会走到这里.
                //判断是否下线,并提示.
                reportContentClickEvent(subcategoryItemBean, Constants.PAGE_ID_CATEGORIES_ALBUM,position);
                if (subcategoryItemBean.isOnline()) {
                    PlayerManagerHelper.getInstance().start(String.valueOf(subcategoryItemBean.getId()), PlayerConstants.RESOURCES_TYPE_ALBUM);
                } else {
                    ToastUtil.showNormal(AppDelegate.getInstance().getContext(), "该资源暂不支持播放，试一试其他的吧");
                }
            }
            break;
            case SubcategoryItemBean.TYPE_ITEM_RADIO_CHANNEL:
                reportContentClickEvent(subcategoryItemBean, Constants.PAGE_ID_CATEGORIES_AI,position);
                PlayerManagerHelper.getInstance().start(String.valueOf(subcategoryItemBean.getId()), PlayerConstants.RESOURCES_TYPE_RADIO);
                break;
            case SubcategoryItemBean.TYPE_ITEM_TITLE:
            case SubcategoryItemBean.TYPE_ITEM_BUTTON:
                // Do-Nothing
                break;
            default:
        }
    }

    private static void reportContentClickEvent(SubcategoryItemBean bean, String pageId, int position) {
        ReportUtil.addContentClickEvent("", ReportParamUtil.getRadioType(bean), "",
                String.valueOf(bean.getId()), ReportParamUtil.getEventTag(bean.getVip() == 1, bean.getFine() == 1),
                pageId, bean.getItemType()+"", ""+position);
    }


}
