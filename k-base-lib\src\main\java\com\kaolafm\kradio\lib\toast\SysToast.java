package com.kaolafm.kradio.lib.toast;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.kaolafm.kradio.lib.R;
import com.kaolafm.kradio.lib.base.flavor.SystemToastInter;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;

import java.lang.ref.WeakReference;

/**
 * @Description:
 * @Author: Maclay
 * @Date: 2022/1/5 3:00 下午
 */
public class SysToast {
    private static Toast toast;
    private static String lastMsg;
    static long shoutime;
    private WeakReference<Context> refContext;

    public View show(Context context, String msg, int lengthShort) {
        if (System.currentTimeMillis() - shoutime < 3000 && TextUtils.equals(lastMsg, msg)) {
            return null;
        }
        refContext = new WeakReference<Context>(context);
        View view = ((LayoutInflater) refContext.get().getSystemService(Context.LAYOUT_INFLATER_SERVICE)).inflate(R.layout.sys_toast_layout, null, false);
        TextView text = (TextView) view.findViewById(R.id.toast_msg);
        text.setText(msg);
//        text.setPadding();
        ((View) text.getParent()).setLayoutParams(new FrameLayout.LayoutParams((int) (ScreenUtil.getScreenWidth() * 0.8), ViewGroup.LayoutParams.WRAP_CONTENT));
        //解决https://app.huoban.com/tables/2100000007530121/items/2300001672042057?userId=1229522问题
        if (toast == null) {
            toast = new Toast(refContext.get());
        } else {
            toast.cancel();
            toast = null;
            toast = new Toast(refContext.get());
        }

        toast.setGravity(Gravity.CENTER, 0, -ResUtil.getDimen(R.dimen.m12));
        toast.setDuration(Toast.LENGTH_SHORT);
        SystemToastInter inter = ClazzImplUtil.getInter("SystemToastImpl");
        if (inter != null && inter.getSystemToast()) {
            toast.setText(msg);
            Log.d("SysToast", "getSystemToast");
        } else {
            Log.d("SysToast", "setView");
            toast.setView(view);
        }
        toast.show();
        shoutime = System.currentTimeMillis();
        lastMsg = msg;
        return view;
    }

    public static void release() {
        toast = null;
    }
}