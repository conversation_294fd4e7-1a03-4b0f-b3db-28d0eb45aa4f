package com.kaolafm.kradio.online.common.location;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.HostConstant;

import java.util.Map;

import io.reactivex.Single;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.QueryMap;

/**
 * 位置服务
 */
public interface OnlineLocationService {
    @Headers({HostConstant.DOMAIN_HEADER_OPEN_KAOLA, HostConstant.DOMAIN_HEADER_HTTPS_PROTOCOL})
    @GET(OnlineLocationHttpConstants.REQUEST_GET_ADRESS_URL)
    Single<BaseResult<OnlineLocationGetAderssBean>> getAdress(@QueryMap Map<String, Object> map);
}
