<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:layout_gravity="center_vertical"
    android:orientation="horizontal">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/user_center_top_guideline"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.2" />

    <TextView
        android:id="@+id/tv_dialog_cancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y40"
        android:background="@drawable/globle_round_bg"
        android:paddingStart="@dimen/x30"
        android:paddingTop="@dimen/y14"
        android:paddingEnd="@dimen/x30"
        android:paddingBottom="@dimen/y14"
        android:text="@string/cancel"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size5"
        app:layout_constraintHorizontal_bias="0.8"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_dialog_message" />

    <TextView
        android:id="@+id/tv_dialog_define"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y40"
        android:background="@drawable/globle_round_bg"
        android:paddingStart="@dimen/x30"
        android:paddingTop="@dimen/y14"
        android:paddingEnd="@dimen/x30"
        android:paddingBottom="@dimen/y14"
        android:text="@string/ok"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size5"
        app:layout_constraintHorizontal_bias="0.205"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_dialog_message" />

    <TextView
        android:id="@+id/tv_dialog_message"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size7"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/user_center_top_guideline"
        tools:text="@string/are_you_sure_to_clear_your_listening_history" />
</androidx.constraintlayout.widget.ConstraintLayout>
