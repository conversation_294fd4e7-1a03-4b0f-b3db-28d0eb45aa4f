package com.kaolafm.kradio.lib.utils;

import android.content.Context;
import android.util.Log;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-08-27 15:50
 ******************************************/
public class ActivationUtils {
    private static final String TAG = "ActivationUtils";
    /**
     * 存储激活页激活按钮被触发逻辑xml文件名称
     */
    private static final String ACTIVATION_XML_NAME = "activation";
    /**
     * 存储激活页激活按钮被触发逻辑标记
     */
    private static final String ACTIVATION_FLAG_NAME = "activation_flag";

    public boolean isActivation(Context context) {
        SharedPreferenceUtil sharedPreferenceUtil = SharedPreferenceUtil.getInstance(context, ACTIVATION_XML_NAME);
        boolean flag = sharedPreferenceUtil.getBoolean(ACTIVATION_FLAG_NAME, false);
        Log.i(TAG, "isActivation-----> flag = " + flag);
        return flag;
    }

    public void setActivation(Context context, boolean flag) {
        SharedPreferenceUtil sharedPreferenceUtil = SharedPreferenceUtil.getInstance(context, ACTIVATION_XML_NAME);
        sharedPreferenceUtil.putBoolean(ACTIVATION_FLAG_NAME, flag);
        Log.i(TAG, "setActivation-----> flag = " + flag);
    }
}
