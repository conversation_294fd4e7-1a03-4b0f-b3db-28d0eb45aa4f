<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_height="match_parent">

    <com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout
        android:id="@+id/trfl_broadcast_list_refresh"
        android:layout_width="match_parent"

        android:layout_height="match_parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_broadcast_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never" />
    </com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout>

    <include
        android:id="@+id/loadingView"
        layout="@layout/online_refresh_center"
        tools:visibility="visible"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />
</FrameLayout>
