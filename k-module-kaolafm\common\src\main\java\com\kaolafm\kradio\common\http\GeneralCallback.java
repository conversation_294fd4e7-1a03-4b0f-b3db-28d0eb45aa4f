package com.kaolafm.kradio.common.http;

/******************************************
 * 类描述： 通用回调接口模版 类名称：GeneralCallback
 *
 * @version: 1.0
 * @author: shaoning<PERSON>ang
 * @time: 2016-7-28 10:15
 ******************************************/
public interface GeneralCallback<T> {
    /**
     * 得到正确的结果回调（备注 此结果可能为null，注意判断）
     *
     * @param t
     */
    void onResult(T t);

    /**
     * 未得到任何结果，但会给出一个错误码
     *
     * @param code
     */
    void onError(int code);

    /**
     * 业务逻辑处理出现异常
     *
     * @param throwable
     */
    void onException(Throwable throwable);
}
