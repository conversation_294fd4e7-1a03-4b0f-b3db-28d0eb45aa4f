package com.kaolafm.kradio.online.mine.purchased;

import android.content.res.Configuration;
import android.graphics.Color;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.common.SkinStateManager;
import com.kaolafm.kradio.common.bean.PurchasedItemBean;
import com.kaolafm.kradio.common.report.ReportParamUtil;
import com.kaolafm.kradio.common.report.ReportUtil;
import com.kaolafm.kradio.common.utils.TextLineUtil;
import com.kaolafm.kradio.common.widget.layoutmanager.SafetyStaggeredGridLayoutManager;
import com.kaolafm.kradio.common.widget.refresh.KradioRefreshFooterHorizontal;
import com.kaolafm.kradio.common.widget.refresh.KradioRefreshHeaderHorizontal;
import com.kaolafm.kradio.common.widget.refresh.KradioSmartRefreshHorizontal;
import com.kaolafm.kradio.k_kaolafm.R; 
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioMultiWindowInter;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.base.ui.BaseLazyFragment;
import com.kaolafm.kradio.lib.base.ui.online.BaseShowHideLazyFragment;
import com.kaolafm.kradio.lib.base.ui.online.BaseViewPagerLazyFragment;
import com.kaolafm.kradio.lib.common.ResType;
import com.kaolafm.kradio.lib.toast.SuperToast;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkManager;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.RecyclerViewExposeUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.UrlUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.common.bean.BroadcastRadioSimpleData;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;


import com.kaolafm.kradio.purchase.purchased.IPurchasedView;
import com.kaolafm.kradio.purchase.purchased.PurchasedPresenter;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants; 

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;
 
import me.yokeyword.fragmentation.SupportFragment;

/**
 * <pre>
 *     <AUTHOR> Wenchl
 *     e-mail : <EMAIL>
 *     time   : 2019/03/06
 *     desc   :
 *     version: 1.0
 * </pre>
 */
public class OnlinePurchasedLoginedFragment extends BaseShowHideLazyFragment<PurchasedPresenter>
        implements IPurchasedView, RecyclerViewExposeUtil.OnItemExposeListener {

    private static final String TAG = "OnlinePurchased";
 
    RecyclerView rvSub; 
    ViewStub vsLayoutErrorPage; 
    TextView tvPurchasedCount; 
    TextView tvNoPurchased; 
    LinearLayout mRootLayout; 
    KradioSmartRefreshHorizontal refreshLayout; 
    View mPurchasedLoading;

    List<PurchasedItemBean> purchasedItemBeans;

    private PurchasedAdapter purchasedAdapter;

    private OnKeyListenListener onKeyListenListener;

    private SkinStateManager.ILoadSkinListener mILoadSkinListener;
    private boolean showToast = false;
    StaggeredGridLayoutManager layoutManager;
    private int lineCount = 2;// 默认是两列，在初始化时会自动修改
    Configuration mConfiguration;

    RelativeLayout mErrorLayout;
    NetWorkListener mNetWorkListener;
    private static SuperToast mToast;

    private BasePlayStateListener mPlayerStateListenerWrapper;

    @Override
    protected int getLayoutId() {
        return R.layout.online_fragment_purchased_list;
    }

    @Override
    protected PurchasedPresenter createPresenter() {
        return new PurchasedPresenter(this);
    }


    private KRadioMultiWindowInter mKRadioMultiWindowInter;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mKRadioMultiWindowInter = ClazzImplUtil.getInter("KradioMultiWindowImpl");
    }

    @Override
    public void initView(View view) {

        rvSub=view.findViewById(R.id.rv_sub);
        vsLayoutErrorPage=view.findViewById(R.id.vs_layout_error_page);
        tvPurchasedCount=view.findViewById(R.id.tv_purchased_count);
        tvNoPurchased=view.findViewById(R.id.tv_no_purchased);
        mRootLayout=view.findViewById(R.id.sub_main_layout);
        refreshLayout=view.findViewById(R.id.refreshLayout);
        mPurchasedLoading=view.findViewById(R.id.purchased_loading);
 
        
        layoutManager = new SafetyStaggeredGridLayoutManager(lineCount, LinearLayoutManager.HORIZONTAL);
        layoutManager.setSpanCount(lineCount);
        rvSub.setLayoutManager(layoutManager);
        ((DefaultItemAnimator) rvSub.getItemAnimator()).setSupportsChangeAnimations(false);
        purchasedAdapter = new PurchasedAdapter();
        purchasedAdapter.setOnItemClickListener((view1, viewType, purchasedItemBean, position) -> {

            if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
                return;
            }
            reportContentClickEvent(purchasedItemBean, position);
            if (!purchasedItemBean.isOnLine()) {
                ToastUtil.showOnActivity(getActivity(), getString(R.string.online_res_drop_off));
                return;
            }

            if (PlayerManagerHelper.getInstance().isPlayCurrentRadio(String.valueOf(purchasedItemBean.getId()))) {
                return;
            }
            if (purchasedItemBean.getType() == ResType.ALBUM_TYPE) {
                PlayerManagerHelper.getInstance().start(String.valueOf(purchasedItemBean.getId()), PlayerConstants.RESOURCES_TYPE_ALBUM);
            } else if (purchasedItemBean.getType() == ResType.AUDIO_TYPE) {
                PlayerManagerHelper.getInstance().start(String.valueOf(purchasedItemBean.getId()), PlayerConstants.RESOURCES_TYPE_AUDIO);
            } else if (purchasedItemBean.getType() == ResType.RADIO_TYPE) {
                PlayerManagerHelper.getInstance().start(String.valueOf(purchasedItemBean.getId()), PlayerConstants.RESOURCES_TYPE_RADIO);
            } else if (purchasedItemBean.getType() == ResType.BROADCAST_TYPE) {
                BroadcastRadioSimpleData data = new BroadcastRadioSimpleData();
                data.setBroadcastId(Long.valueOf(purchasedItemBean.getId()));
                data.setImg(purchasedItemBean.getImg());
                data.setName(purchasedItemBean.getName());
                PlayerManagerHelper.getInstance().addBroadcastRadioSimpleItem(data);
                PlayerManagerHelper.getInstance().start(String.valueOf(purchasedItemBean.getId()), PlayerConstants.RESOURCES_TYPE_BROADCAST);
            }
        });
//        setTextViewSpannable();
        rvSub.setAdapter(purchasedAdapter);
        rvSub.addItemDecoration(new PurchasedItemDecoration());

        RecyclerViewExposeUtil exposeUtil = new RecyclerViewExposeUtil();
        exposeUtil.setRecyclerItemExposeListener(rvSub, this);

        mPlayerStateListenerWrapper = new BasePlayStateListener() {
            @Override
            public void onPlayerPlaying(PlayItem playItem) {
                super.onPlayerPlaying(playItem);
                if (purchasedAdapter != null) {
                    purchasedAdapter.setPlayState();
                }
            }

            @Override
            public void onPlayerPaused(PlayItem playItem) {
                super.onPlayerPaused(playItem);
                if (purchasedAdapter != null) {
                    purchasedAdapter.setPlayState();
                }
            }
        };
        PlayerManager.getInstance().addPlayControlStateCallback(mPlayerStateListenerWrapper);
        if (isLoaded) {
            if (!ListUtil.isEmpty(purchasedItemBeans)) {
                showPurchasedData(purchasedItemBeans, false);
            } else {
                showPurchasedData(null, false);
            }
        }
        initSkinListener();
        NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false);

        // 不管网络状态如何，都需将网络状态监听器注册到网络检测模块，以便在其他场景下引起网络变化时能够自动加载数据。
        mNetWorkListener = new NetWorkListener(this);
        NetworkManager.getInstance().addNetworkReadyListener(mNetWorkListener);
        refreshLayout.setEnableRefresh(true);
        refreshLayout.setEnableLoadMore(true);
        refreshLayout.setEnableAutoLoadMore(false);//使上拉加载具有弹性效果
        refreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(getContext())) {
                    refreshLayout.finishLoadMore();
                    return;
                }
                mPresenter.getNextPageList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(getContext())) {
                    refreshLayout.finishRefresh();
                    return;
                }
                mPresenter.fetchPurchasedData();
            }
        });
        addHeaderAndBottom();
    }

    /**
     * 点击item事件上报
     *
     * @param item
     */
    private void reportContentClickEvent(PurchasedItemBean item, int position) {
        ReportUtil.addContentClickEvent("", ReportParamUtil.getRadioType(item.getType()),
                "", String.valueOf(item.getId()),
                ReportParamUtil.getEventTag(item.getVip() == 1, item.getFine() == 1),
                getPageId(), getPageId(), "" + position);
    }

    private void reportContentShowEvent(PurchasedItemBean item, int position) {
        ReportUtil.addContentShowEvent("", ReportParamUtil.getRadioType(item.getType()),
                "", String.valueOf(item.getId()),
                ReportParamUtil.getEventTag(item.getVip() == 1, item.getFine() == 1),
                getPageId(), getPageId(), "" + position);
    }

    @Override
    public String getPageId() {
        return Constants.ONLINE_PAGE_ID_MINE_PURCHASED;
    }

    public void updateData() {
        Log.d(TAG, "Enter method updateData()");
        if (mPresenter != null) {
            mPresenter.fetchPurchasedData();
        }
    }

    private void addHeaderAndBottom() {
        refreshLayout.setRefreshHeader(new KradioRefreshHeaderHorizontal(getContext()));
        refreshLayout.setRefreshFooter(new KradioRefreshFooterHorizontal(getContext()));
//        CustomerRefreshBottom mRefreshBottom = new CustomerRefreshBottom(getContext());
//        refreshLayout.setBottomView(mRefreshBottom);
//        RefreshLayoutInitialize.initDefaultRefreshLayoutHeaderStyle(getContext(), refreshLayout);
    }

    @Override
    public void showPurchasedData(List<PurchasedItemBean> list, boolean loadMore) {
        hideErrorLayout();
        if (!loadMore && ListUtil.isEmpty(list)) {
            purchasedItemBeans = null;
            tvNoPurchased.setVisibility(View.VISIBLE);
            tvPurchasedCount.setVisibility(View.GONE);
            purchasedAdapter.setDataList(list);
        } else {
            tvNoPurchased.setVisibility(View.GONE);
            if (loadMore) {
                if (ListUtil.isEmpty(list)) {
                    list = new ArrayList<>();
                }
                if (purchasedItemBeans != null) {
                    purchasedItemBeans.addAll(list);
                } else {
                    purchasedItemBeans = list;
                }
                List<PurchasedItemBean> resultList = getPurDiffList(purchasedAdapter.getDataList(), list);

                purchasedAdapter.addDataList(resultList);
            } else {
                purchasedItemBeans = list;
                purchasedAdapter.setDataList(list);
            }


        }
        refreshLayout.finishLoadMore();
        refreshLayout.finishRefresh();
        boolean hasNext = mPresenter.hasNext;
        refreshLayout.setEnableLoadMore(hasNext);
        refreshLayout.setEnableOverScrollBounce(hasNext);
        purchasedAdapter.setPlayState();
    }

    //去重
    public static List<PurchasedItemBean> getPurDiffList(List<PurchasedItemBean> fromList,
                                                         List<PurchasedItemBean> onList) {
        List<PurchasedItemBean> result = new ArrayList<>();
        for (PurchasedItemBean bean : onList) {
            boolean hasValue = false;
            for (PurchasedItemBean item : fromList) {
                if (bean.getId() == item.getId()
                        && (bean.getName() != null && bean.getName().equals(item.getName()))) {
                    hasValue = true;
                    break;
                }
            }
            if (!hasValue) {
                result.add(bean);
            }
        }
        return result;
    }

    @Override
    public void showPurchasedDataNum(int count) {
        if (tvNoPurchased.getVisibility() == View.GONE) {
            tvPurchasedCount.setVisibility(View.VISIBLE);
            String format = getContext().getString(R.string.online_purchased_count);
            tvPurchasedCount.setText(String.format(format, count));

        }

    }


    @Override
    public void showError() {

        // 因切换TAB页时需要重新加载订阅数据，可能会导致网络异常与订阅数据列表同时显示，所以显示错误时先清空其他控件
        tvNoPurchased.setVisibility(View.GONE);
        tvPurchasedCount.setVisibility(View.GONE);
        purchasedItemBeans = null;
        purchasedAdapter.setDataList(null);

        showErrorLayout(ResUtil.getString(R.string.network_nosigin), true);
        if (onKeyListenListener != null) {
            onKeyListenListener.onShow(false);
        }
    }

    @Override
    public void showError(String error, boolean clickToRetry) {
        showErrorLayout(error, clickToRetry);
        if (onKeyListenListener != null) {
            onKeyListenListener.onShow(false);
        }
    }

    private void showErrorLayout(String error, boolean clickToRetry) {
        if (mErrorLayout == null) {
            mErrorLayout = (RelativeLayout) vsLayoutErrorPage.inflate();
            TextView tvNetworkNosign = mErrorLayout.findViewById(R.id.tv_error);
            tvNetworkNosign.setText(error);
            ImageView ivNetworkNoSign = mErrorLayout.findViewById(R.id.iv_error);
            // 支持点击重试
            if (clickToRetry) {
                mErrorLayout.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        hideErrorLayout();
                        updateData();
                    }
                });
            } else {
                ivNetworkNoSign.setImageResource(R.drawable.online_error_empty);
            }
        }
        ViewUtil.setViewVisibility(mErrorLayout, View.VISIBLE);
        ViewUtil.setViewVisibility(refreshLayout, View.GONE);
    }

    public void hideErrorLayout() {
        if (mErrorLayout == null) {
            return;
        }
        ViewUtil.setViewVisibility(mErrorLayout, View.GONE);
    }

    @Override
    public void onShowChange(boolean show) {
        if (onKeyListenListener != null) {
            onKeyListenListener.onShow(show);
        }
    }

    @Override
    public void prepareFragmentStateForShowLoading() {
        purchasedItemBeans = null;
        purchasedAdapter.setDataList(null);
        purchasedAdapter.notifyDataSetChanged();
        ViewUtil.setViewVisibility(mErrorLayout, View.GONE);
        ViewUtil.setViewVisibility(tvNoPurchased, View.GONE);
        ViewUtil.setViewVisibility(tvPurchasedCount, View.GONE);
    }

    @Override
    public void showLoading() {
        ViewUtil.setViewVisibility(mPurchasedLoading, View.VISIBLE);
    }

    @Override
    public void hideLoading() {
        ViewUtil.setViewVisibility(mPurchasedLoading, View.GONE);
    }

    @Override
    public void toast(int id) {
        ToastUtil.showError(getContext(), id);
    }

    public void setOnKeyListenListener(OnKeyListenListener onKeyListenListener) {
        this.onKeyListenListener = onKeyListenListener;
    }

    @Override
    public void onMultiWindowModeChanged(boolean isInMultiWindowMode) {
        super.onMultiWindowModeChanged(isInMultiWindowMode);
        //fixme 此种处理方式会导致StaggeredGridLayoutManager索引越界异常产生crash
        Log.i(TAG, "onMultiWindowModeChanged " + isInMultiWindowMode);
        if (rvSub != null) {
            RecyclerView.Adapter adapter = rvSub.getAdapter();
            RecyclerView.LayoutManager manager = rvSub.getLayoutManager();
//            StaggeredGridLayoutManager manager = new StaggeredGridLayoutManager(lineCount, LinearLayoutManager.VERTICAL);
            rvSub.setAdapter(null);
            rvSub.setLayoutManager(null);
            rvSub.getRecycledViewPool().clear();
            rvSub.setLayoutManager(manager);
            rvSub.setAdapter(adapter);
        }
    }

    @Override
    protected void lazyLoad() {
        updateData();
    }

    @Override
    public void onItemViewVisible(boolean visible, int position) {
        if (visible && purchasedAdapter != null) {
            PurchasedItemBean bean = purchasedAdapter.getItemData(position);
            reportContentShowEvent(bean, position);
        }
    }

    public interface OnKeyListenListener {
        void onShow(boolean show);
    }

    public class PurchasedAdapter extends BaseAdapter<PurchasedItemBean> {


        @Override
        protected BaseHolder getViewHolder(ViewGroup parent, int viewType) {
            BaseHolder subcategoryItemBeanBaseHolder;
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.online_item_purchased, parent, false);
            subcategoryItemBeanBaseHolder = new PurchasedViewHolder(view);
            return subcategoryItemBeanBaseHolder;
        }

        public class PurchasedViewHolder extends BaseHolder<PurchasedItemBean> {
 
            View backgroundIv; 
            ImageView ivPlayCover; 
            TextView userTabTitle; 
            ImageView ivPurchaseType;

            public PurchasedViewHolder(View itemView) {
                super(itemView);

                backgroundIv=itemView.findViewById(R.id.backgroundIv);
                ivPlayCover=itemView.findViewById(R.id.iv_play_cover);
                userTabTitle=itemView.findViewById(R.id.user_tab_title);
                ivPurchaseType=itemView.findViewById(R.id.iv_purchase_type);
            }

            @Override
            public void setupData(PurchasedItemBean purchasedData, int position) {
                boolean online = purchasedData.isOnLine();
                if (!online) {
                    itemView.setAlpha(0.6f);
                }
                boolean playing = purchasedData.isPlaying();
                if (playing) {
                    backgroundIv.setVisibility(View.VISIBLE);
                } else {
                    backgroundIv.setVisibility(View.GONE);
                }
//                backgroundIv.setBackground(playing ? getResources().getDrawable(R.drawable.online_ring_selected) : getResources().getDrawable(R.drawable.online_ring_unselected));
                userTabTitle.setSelected(playing);
                userTabTitle.setText(StringUtil.getMaxString(purchasedData.getName(), 6));
                ImageLoader.getInstance().displayImage(getContext(), UrlUtil.getCustomPicUrl(UrlUtil.PIC_250_250, purchasedData.getImg()), ivPlayCover);
                if (purchasedData.getVip() == 1) {
                    ivPurchaseType.setBackground(getResources().getDrawable(R.drawable.online_ring_ic_vip));
                } else if (purchasedData.getFine() == 1) {
                    ivPurchaseType.setBackground(getResources().getDrawable(R.drawable.online_ring_ic_supreme));
                }
                if (purchasedData.getType() == ResType.LIVE_TYPE) {
                    ivPurchaseType.setImageResource(R.drawable.online_ring_ic_live);
                }
            }
        }

        void setPlayState() {
            long playingId = PlayerManagerHelper.getInstance().getSubscribeId();
            for (int i = 0; i < getItemCount(); i++) {
                PurchasedItemBean purchasedItemBean = getItemData(i);
                if (purchasedItemBean != null) {

                    if (purchasedItemBean.isPlaying()) {
                        purchasedItemBean.setPlaying(false);
                        notifyItemChanged(i);
                    }

                    if (purchasedItemBean.getId() == playingId) {
                        purchasedItemBean.setPlaying(true);
                        notifyItemChanged(i);
                    }

                }
            }
        }

    }

    public void fetchPurchasedData() {
        Log.d(TAG, "Enter method fetchPurchasedData()");
        showToast = true;
        if (mPresenter == null) return;
        mPresenter.fetchPurchasedData();
    }


    private void setTextViewSpannable() {
        if (tvNoPurchased == null) return;
        tvNoPurchased.setText(R.string.online_no_purchased);
        if (true) {
            //先去掉了变色处理，等UI走查
            return;
        }
        SpannableString spannableString = new SpannableString(ResUtil.getString(R.string.online_no_purchased));
        int color = ResUtil.getColor(R.color.online_spannable_text_color);
        ForegroundColorSpan colorSpan = new ForegroundColorSpan(color);
        spannableString.setSpan(colorSpan, spannableString.length() - 4, spannableString.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ClickableSpan() {
            @Override
            public void onClick(View widget) {
                if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
                    return;
                }
                PlayerManagerHelper.getInstance().start("1200000000099", PlayerConstants.RESOURCES_TYPE_RADIO);

                // TODO: 2019-11-01 是否有问题
                //PageJumper.getInstance().back();
                ((SupportFragment) (getParentFragment().getParentFragment())).pop();
            }

            @Override
            public void updateDrawState(TextPaint ds) {
                ds.setColor(ds.linkColor);
                //不显示下划线
                ds.setUnderlineText(false);
            }
        }, spannableString.length() - 4, spannableString.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        tvNoPurchased.setMovementMethod(LinkMovementMethod.getInstance());
        tvNoPurchased.setHighlightColor(Color.TRANSPARENT);
        int color1 = ResUtil.getColor(R.color.online_spannable_text_color);
        tvNoPurchased.setLinkTextColor(color1);
        tvNoPurchased.setText(spannableString);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        tvNoPurchased.setText(null);
        tvNoPurchased.clearComposingText();
        SkinStateManager.getInstance().removeLoadSkinListener(mILoadSkinListener);
        NetworkManager.getInstance().removeNetworkReadyListener(mNetWorkListener);
        PlayerManager.getInstance().removePlayControlStateCallback(mPlayerStateListenerWrapper);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        TextLineUtil.clearTextLineCache();
    }

//    private void updateView(boolean isLand) {
//        ConstraintSet set = new ConstraintSet();
//        set.clone(mRootLayout);
//        if (isLand) {
//            set.setVerticalBias(tvNoPurchased.getId(), 0.5f);
//        } else {
//            set.setVerticalBias(tvNoPurchased.getId(), 0.36f);
//        }
//        set.applyTo(mRootLayout);
//    }

    @Override
    protected void showAccordingToScreen(int orientation) {
        int paddingLeft = ScreenUtil.getGlobalPaddingLeft(orientation);
        int paddingRight = ScreenUtil.getGlobalPaddingRight(orientation);

        mRootLayout.setPadding(paddingLeft, 0, paddingRight, 0);
        //
        //        切换的时候设置列数
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            lineCount = 2;
//            updateView(true);
        } else {
            lineCount = 1;
//            updateView(false);
        }

        boolean multi = false;

        if (mKRadioMultiWindowInter != null && mKRadioMultiWindowInter.setSubscriptionGridLayoutManager(layoutManager)) {
            multi = true;
        }

        if (!multi) {
            layoutManager.setSpanCount(lineCount);
            // 解决横竖屏切换后adapter item位置错乱的问题
            rvSub.setAdapter(purchasedAdapter);
        }
        rvSub.setLayoutManager(layoutManager);
        purchasedAdapter.notifyDataSetChanged();
    }

    private void initSkinListener() {
        mILoadSkinListener = this::setTextViewSpannable;
        SkinStateManager.getInstance().addLoadSkinListener(mILoadSkinListener);
    }

    public static class NetWorkListener implements NetworkManager.INetworkReady {
        private WeakReference purchasedFragmentWeakReference;

        public NetWorkListener(OnlinePurchasedLoginedFragment onlinePurchasedLoginedFragment) {
            purchasedFragmentWeakReference = new WeakReference<>(onlinePurchasedLoginedFragment);
        }

        @Override
        public void networkChange(boolean hasNetwork) {
            Log.i(TAG, "Network state changed, param [hasNetwork] value is : " + hasNetwork);
            if (!hasNetwork) {
                return;
            }
            OnlinePurchasedLoginedFragment onlinePurchasedLoginedFragment = (OnlinePurchasedLoginedFragment) purchasedFragmentWeakReference.get();

            if (onlinePurchasedLoginedFragment != null && onlinePurchasedLoginedFragment.mPresenter != null) {
                onlinePurchasedLoginedFragment.mPresenter.fetchPurchasedData();
            }
        }
    }
}
