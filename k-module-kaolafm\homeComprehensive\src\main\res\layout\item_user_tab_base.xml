<?xml version="1.0" encoding="utf-8"?>
<com.kaolafm.kradio.component.ui.base.view.ScaleConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_broadcast_list_item">

    <LinearLayout
        android:id="@+id/content_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintTop_toTopOf="parent">

        <com.kaolafm.kradio.lib.widget.SquareFrameLayout
            android:layout_width="@dimen/m114"
            android:layout_height="@dimen/m114"
            app:canScale="false">

            <com.kaolafm.kradio.component.ui.base.view.OvalImageView
                android:id="@+id/iv_play_cover"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:contentDescription="@null"
                android:scaleType="fitXY"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:rid_type="4"
                tools:src="@drawable/ic_launcher" />

            <!--描边，日间模式有-->
            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/sh_bg_r8l"
                android:visibility="gone" />

            <com.kaolafm.kradio.component.ui.base.view.OvalImageView
                android:id="@+id/vip_icon"
                android:layout_width="@dimen/m51"
                android:layout_height="@dimen/m25"
                android:scaleType="centerCrop"
                app:rid_type="1"
                tools:src="@drawable/ic_live_s" />
        </com.kaolafm.kradio.lib.widget.SquareFrameLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:orientation="vertical"
            android:paddingStart="@dimen/x30"
            android:paddingEnd="@dimen/x80">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/x50"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <com.kaolafm.kradio.component.ui.base.view.RateView
                    android:id="@+id/user_layout_playing"
                    android:layout_width="@dimen/m28"
                    android:layout_height="@dimen/m28"
                    android:layout_marginRight="@dimen/x13"
                    app:lottie_autoPlay="true"
                    app:lottie_fileName="lottie/rate.json"
                    app:lottie_loop="true" />

                <TextView
                    android:id="@+id/user_tab_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:singleLine="true"
                    android:textColor="@color/user_item_title_text_color"
                    android:textSize="@dimen/text_size_title7"
                    tools:text="行动派" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/user_item_tag_tv"
                    android:layout_width="@dimen/m48"
                    android:layout_height="@dimen/m26"
                    android:layout_marginTop="@dimen/y2"
                    android:layout_marginRight="@dimen/m8"
                    android:background="@drawable/user_item_tag_bg"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:singleLine="true"
                    android:textColor="@color/history_item_tag_text_color"
                    android:textSize="@dimen/m18"
                    tools:text="专辑" />

                <TextView
                    android:id="@+id/user_tab_content"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/y2"
                    android:ellipsize="end"
                    android:singleLine="true"
                    android:textColor="@color/sl_item_subcategory_broadcast_subtxt"
                    android:textSize="@dimen/text_size_title8"
                    tools:text="1天前更新" />
            </LinearLayout>
        </LinearLayout>

    </LinearLayout>


    <com.kaolafm.kradio.component.ui.base.view.OvalImageView
        android:id="@+id/cover_offline"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_centerInParent="true"
        android:background="@drawable/offline_layer"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/content_ll"
        app:layout_constraintTop_toTopOf="parent"
        app:rid_type="4"
        tools:visibility="visible" />


</com.kaolafm.kradio.component.ui.base.view.ScaleConstraintLayout>