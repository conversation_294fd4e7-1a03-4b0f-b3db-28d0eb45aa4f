package com.kaolafm.kradio.home.comprehensive.item;

import android.content.res.Configuration;
import androidx.annotation.NonNull;
import android.util.Log;
import android.util.TypedValue;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.component.ui.base.view.RateView;
import com.kaolafm.kradio.lib.widget.square.SquareLayout;
import com.kaolafm.kradio.component.ui.base.CellBinder;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.flavor.KRadioHomeItemFontSizeInter;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.lib.widget.ratio.RatioConstraintLayout;
/**
 * 首页广播类型
 * <AUTHOR> Yan
 * @date 2019-08-16
 */
public class BroadcastCell extends HomeCell implements CellBinder<View, BroadcastCell> {

    public String freq;

    private static final int TITILE_LINE_MAX = 8;

    ImageView mIvItemHomeCover;

    SquareLayout mSquareLayout;

    RatioConstraintLayout mRclItemHomeContent;

    TextView mTvItemHomeTitle;

    View mViewItemHomeCoverBg;

    View mViewItemHomeTextBg;
    TextView mTvItemHomeAdLabel;
    TextView tvFreq;

    RateView mPlayingIcon;
    View llFreq;


    public void mountView(@NonNull BroadcastCell data, @NonNull View view, int position) {
        mIvItemHomeCover=view.findViewById(R.id.iv_item_home_cover);
        mSquareLayout=view.findViewById(R.id.sv_item_home_place);
        mRclItemHomeContent=view.findViewById(R.id.rcl_item_home_content);
        mTvItemHomeTitle=view.findViewById(R.id.tv_item_home_title);
        mViewItemHomeCoverBg=view.findViewById(R.id.view_item_home_cover_bg);
        mViewItemHomeTextBg=view.findViewById(R.id.view_item_home_text_bg);
        mTvItemHomeAdLabel=view.findViewById(R.id.tv_item_home_ad_label);
        tvFreq=view.findViewById(R.id.tvFreq);
        mPlayingIcon=view.findViewById(R.id.vs_layout_playing);
        llFreq=view.findViewById(R.id.llFreq);


        Log.i("GoldenRatioCell", "mountView dataad:" + data.iImageAd + " , position:" + position + ",url:" + data.imageUrl);
        //因连图的上报属性和二次互动后台配置了全部的图片，所以这里只需要标记一个连图图片，曝光一次即可，对应 首页的GoldenRatioCell 类
        ImageLoader.getInstance().displayImage(view.getContext(), data.imageUrl, mIvItemHomeCover);
        String tmpValue = data.name;
        if (tmpValue.length() <= TITILE_LINE_MAX) {
            tmpValue += "\n";
        }
        mTvItemHomeTitle.setText(tmpValue);
        Log.i("AdTag", data.toString());
        if (data.iImageAd >= 0) {
            ViewUtil.setViewVisibility(mTvItemHomeAdLabel, View.VISIBLE);
        } else {
            ViewUtil.setViewVisibility(mTvItemHomeAdLabel, View.GONE);
        }

        onOrientationChanged();
//        mSquareLayout.setPlayState(data.selected);
//        mIvItemHomeCover.setPlayState(data.selected);
        mPlayingIcon.setVisibility(data.selected ? View.VISIBLE : View.GONE);
        mViewItemHomeCoverBg.setSelected(data.selected);
        mTvItemHomeTitle.setSelected(data.selected);

        KRadioHomeItemFontSizeInter homeItemFontSizeInter = ClazzImplUtil.getInter("KRadioHomeItemFontSizeImpl");
        if (homeItemFontSizeInter != null) {
            changeHomeTitleSize(ResUtil.getDimen(homeItemFontSizeInter.getSizeId()));
        } else {
            changeHomeTitleSize(ResUtil.getDimen(R.dimen.home_item_golden_text_size));
        }
        boolean hasFreq = false;
        if (!StringUtil.isEmpty(freq)) {
            if (freq.startsWith("FM")) {
                freq = freq.replace("FM", "");
            }
            if (freq.contains("/")){
                freq = freq.substring(0,freq.indexOf("/"));
            }
            tvFreq.setText(freq);
            hasFreq = true;
        }
        llFreq.setVisibility(hasFreq ? View.VISIBLE : View.GONE);
        ViewUtil.setViewVisibilityAccordingToSetting(mViewItemHomeCoverBg);
    }

    private void changeHomeTitleSize(int size) {
        mTvItemHomeTitle.setTextSize(TypedValue.COMPLEX_UNIT_PX, size);
    }

    @Override
    public int getItemType() {
        return R.layout.item_home_broadcast_img;
    }
    @Override
    public int spanSize() {
        return ResUtil.getInt(R.integer.home_item_radio_spans);
    }

    private void onOrientationChanged() {
        int orientation = ResUtil.getOrientation();
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            mRclItemHomeContent.setRatio("0.719:1");
        } else {
            mRclItemHomeContent.setRatio("0.66:1");
        }
    }
}
