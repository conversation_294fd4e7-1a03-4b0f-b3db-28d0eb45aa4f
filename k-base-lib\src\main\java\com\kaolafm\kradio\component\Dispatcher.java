package com.kaolafm.kradio.component;

import android.os.Handler;
import android.os.Looper;
import com.kaolafm.kradio.component.RealCaller.AsyncRunnable;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Deque;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2019-07-03
 */
class Dispatcher {

    private static volatile Dispatcher mInstance;

    private static final int MAX_REQUESTS = Runtime.getRuntime().availableProcessors() * 2 + 10;

    /**
     * 准备运行的异步请求的双向队列。
     */
    private final Deque<AsyncRunnable> readyAsyncCalls = new ArrayDeque<>();

    /**
     * 正在运行的异步请求的双向队列。
     */
    private final Deque<AsyncRunnable> runningAsyncCalls = new ArrayDeque<>();

    /**
     * 正在运行同步请求的双向队列。同时只能有一个正在运行的同步
     */
    private final Deque<RealCaller> runningSyncCalls = new ArrayDeque<>();

    /**
     * 责任链处理类的线程池
     */
    private ExecutorService mExecutorService;

    private static final Handler MAIN_THREAD_HEADER = new Handler(Looper.getMainLooper());

    public static Dispatcher getInstance() {
        if (mInstance == null) {
            synchronized (Dispatcher.class) {
                if (mInstance == null) {
                    mInstance = new Dispatcher();
                }
            }
        }
        return mInstance;
    }

    private Dispatcher() {
    }


    public synchronized ExecutorService executorService() {
        if (mExecutorService == null) {
            // CPU优化：调整线程池配置以降低CPU使用率
            int corePoolSize = Math.max(1, Runtime.getRuntime().availableProcessors() / 2); // 减少核心线程数
            mExecutorService = new ThreadPoolExecutor(
                    corePoolSize, // 核心线程数减半
                    MAX_REQUESTS,
                    120, // 增加空闲线程存活时间 60s -> 120s
                    TimeUnit.SECONDS,
                    new SynchronousQueue<>(),
                    r -> {
                        Thread thread = new Thread(r);
                        thread.setName("processor-pool-" + thread.getId());
                        thread.setPriority(Thread.NORM_PRIORITY - 1); // 降低线程优先级
                        return thread;
                    });
        }
        return mExecutorService;
    }

    public void executed(RealCaller caller) {
        runningSyncCalls.add(caller);

    }

    public void executed(AsyncRunnable asyncRunnable) {
        synchronized (this) {
            readyAsyncCalls.add(asyncRunnable);
        }
        promoteAndExecute();
    }

    public void mainThread(Runnable runnable) {
        MAIN_THREAD_HEADER.post(runnable);
    }

    public void finished(RealCaller caller) {
        finished(runningSyncCalls, caller);
    }

    public void finished(AsyncRunnable runnable) {
        finished(runningAsyncCalls, runnable);
    }

    private <T> void finished(Deque<T> calls, T call) {
        synchronized (this) {
            if (!calls.remove(call)) {
                return;
            }
        }
        promoteAndExecute();
    }

    void threadPool(Runnable runnable) {
        if (runnable != null) {
            executorService().execute(runnable);
        }
    }

    private boolean promoteAndExecute() {
        assert (!Thread.holdsLock(this));

        List<AsyncRunnable> executableCalls = new ArrayList<>();
        boolean isRunning;
        synchronized (this) {
            for (Iterator<AsyncRunnable> i = readyAsyncCalls.iterator(); i.hasNext(); ) {
                AsyncRunnable asyncCall = i.next();
                //超过最大运行数时就不在往运行队列中添加
                if (runningAsyncCalls.size() >= MAX_REQUESTS){
                    break;
                }
                i.remove();
                executableCalls.add(asyncCall);
                runningAsyncCalls.add(asyncCall);
            }
            isRunning = runningCallsCount() > 0;
        }

        for (int i = 0, size = executableCalls.size(); i < size; i++) {
            AsyncRunnable asyncCall = executableCalls.get(i);
            asyncCall.executeOn(executorService());
        }
        return isRunning;
    }

    public synchronized int runningCallsCount() {
        return runningAsyncCalls.size() + runningSyncCalls.size();
    }
}
