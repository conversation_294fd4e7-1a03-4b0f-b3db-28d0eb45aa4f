package cmgyunting.vehicleplayer.cnr;

import android.appwidget.AppWidgetManager;
import android.appwidget.AppWidgetProvider;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;

import com.kaolafm.auto.appwidget.KLAppWidget;
import com.kaolafm.kradio.lib.utils.Constants;

public class YunBydWidget extends AppWidgetProvider {

    private static final String TAG = "YunBydWidget";

    @Override
    public void onReceive(Context context, Intent intent) {
        Log.i(TAG, "onReceive----->" + intent.getAction());
        if (Constants.APPEXIT_ACTION.equals(intent.getAction())) {
            AppWidgetManager appWidgetManager = AppWidgetManager.getInstance(context);
            ComponentName componentName = new ComponentName(context, KLAppWidget.class);
            for (int appWidgetId : appWidgetManager.getAppWidgetIds(componentName)) {
                updateAppWidget(context, appWidgetManager, appWidgetId, Constants.APPEXIT_ACTION);
            }
        } else {
            super.onReceive(context, intent);
        }
    }

    @Override
    public void onEnabled(Context context) {
        Log.i(TAG, "onEnable ----- ");
        Intent intent = new Intent(context, YunTingWidgetService.class);
        intent.setAction(YunTingWidgetService.WIDGET_ACTION_REFRESH);
        intent.putExtra("widget_policy",YunTingWidgetService.WIDET_POLICY_NORAML);
        intent.putExtra("startServer", true);
//        context.startService(intent);
        startServiceCompat(context,intent);
    }

    private void startServiceCompat(Context context, Intent intent) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.startForegroundService(intent);
        }else {
            context.startService(intent);
        }
    }

    @Override
    public void onUpdate(Context context, AppWidgetManager appWidgetManager, int[] appWidgetIds) {
        Log.i(TAG, "onUpdate ----- appwidgetId.size appWidgetIds size = " + appWidgetIds.length + "-----appWidgetIds = " + appWidgetIds);
        for (int appWidgetId : appWidgetIds) {
            updateAppWidget(context, appWidgetManager, appWidgetId, "android.appwidget.action.APPWIDGET_UPDATE");
        }
    }

    @Override
    public void onDisabled(Context context) {
        Log.i(TAG, "onDisabled ----- ");
        Intent intent = new Intent(context, YunTingWidgetService.class);
        intent.setAction(YunTingWidgetService.WIDGET_ACTION_REFRESH);
        intent.putExtra("startServer", true);
//        context.startService(intent);
        startServiceCompat(context,intent);
    }


    @Override
    public void onAppWidgetOptionsChanged(Context context, AppWidgetManager appWidgetManager, int appWidgetId, Bundle newOptions) {
        super.onAppWidgetOptionsChanged(context, appWidgetManager, appWidgetId, newOptions);
    }

    void updateAppWidget(final Context pContext, AppWidgetManager appWidgetManager, final int appWidgetId, String action) {
        Log.i(TAG, "updateAppWidget ----- appWidgetId:" + appWidgetId);
        Intent intent = new Intent(pContext, YunTingWidgetService.class);
        intent.setAction(YunTingWidgetService.WIDGET_ACTION_REFRESH);
        intent.putExtra("widget_policy",YunTingWidgetService.WIDET_POLICY_NORAML);
        intent.putExtra("startServer", true);
//        pContext.startService(intent);
        startServiceCompat(pContext,intent);
    }

}
