package com.kaolafm.kradio.lib.location.model;

import android.content.Context;
import android.util.Log;

import com.amap.api.location.AMapLocationClient;
import com.amap.api.location.AMapLocationClientOption;
import com.amap.api.location.AMapLocationListener;
import com.kaolafm.kradio.lib.base.AppDelegate;

/**
 * @ClassName GaoDeLocation
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/5/26 14:58
 * @Version 1.0
 */
public class GaoDeLocation implements IKaoLaLocation {
    private static final String TAG = "GaoDeLocation";
    LocationModel mLocation = new LocationModel();
    private AMapLocationClient mLocationClient;
    private KaoLaLocationListener mLaLocationListener;

    public GaoDeLocation() {
        init();
    }

    private void init() {
        mLaLocationListener = new KaoLaLocationListener();
        start(AppDelegate.getInstance().getContext());
    }

    @Override
    public LocationModel getLocation() {
        return mLocation;
    }

    @Override
    public void addLocationListener(IKaoLaLocationListener listener) {
        mLaLocationListener.addLocationListener(listener);
    }

    @Override
    public void removeLocationListener(IKaoLaLocationListener listener) {
        mLaLocationListener.removeLocationListener(listener);
    }

    private AMapLocationListener mLocationListener = location -> {
        if (location == null) {
            return;
        }
        if (location.getErrorCode() != 0) {
            Log.i(TAG, "location_error:" + location.getErrorCode() + " info:" + location.getErrorInfo());
            return;
        }
        mLocation.setLatitude(location.getLatitude());
        mLocation.setLongitude(location.getLongitude());
        mLocation.setCityName(location.getCity());
        notifyLocation();
    };

    private void notifyLocation() {
        Log.i(TAG, "notifyLocation:" + mLocation.getLatitude() + "/" + mLocation.getLongitude());
        mLaLocationListener.locationChange(mLocation);
    }

    /**
     * 开启高德定位服务
     *
     * @param context
     */
    public void start(Context context) {
        //声明AMapLocationClient类对象
        mLocationClient = new AMapLocationClient(context);
        //设置定位回调监听
        mLocationClient.setLocationListener(mLocationListener);
        AMapLocationClientOption mLocationOption = new AMapLocationClientOption();
        //设置定位模式为高精度模式，Battery_Saving为低功耗模式，Device_Sensors是仅设备模式
        mLocationOption.setLocationMode(AMapLocationClientOption.AMapLocationMode.Hight_Accuracy);
        //设置是否返回地址信息（默认返回地址信息）
        mLocationOption.setNeedAddress(true);
        //设置是否只定位一次,默认为false
        mLocationOption.setOnceLocation(false);
        //设置是否强制刷新WIFI，默认为强制刷新
        mLocationOption.setWifiActiveScan(true);
        //设置是否允许模拟位置,默认为false，不允许模拟位置
        mLocationOption.setMockEnable(false);
        //设置定位间隔,单位毫秒,默认为2000ms
        mLocationOption.setInterval(TIME_INTERVAL);
        //给定位客户端对象设置定位参数
        mLocationClient.setLocationOption(mLocationOption);
        //启动定位
        mLocationClient.startLocation();
    }

    @Override
    public void destroy() {
        mLocationClient.onDestroy();
    }

}
