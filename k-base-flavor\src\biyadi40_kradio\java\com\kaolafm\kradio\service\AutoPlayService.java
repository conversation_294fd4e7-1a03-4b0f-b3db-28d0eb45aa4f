package com.kaolafm.kradio.service;

import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.IBinder;
import androidx.annotation.Nullable;

import com.kaolafm.base.utils.NetworkMonitor;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.flavor.utils.PlayerUtil;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.opensdk.player.logic.PlayerManager;

import java.lang.ref.WeakReference;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-06-20 09:49
 ******************************************/
public final class AutoPlayService extends Service {
    private MyOnNetworkStatusChangedListener myOnNetworkStatusChangedListener;
    private MyOnPlayerInitCompleteListener myOnPlayerInitCompleteListener;

    @Override
    public void onCreate() {
        super.onCreate();
        myOnNetworkStatusChangedListener = new MyOnNetworkStatusChangedListener(this);
        myOnPlayerInitCompleteListener = new MyOnPlayerInitCompleteListener();
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    private static class MyOnNetworkStatusChangedListener implements NetworkMonitor.OnNetworkStatusChangedListener {

        private WeakReference<AutoPlayService> weakReference;

        public MyOnNetworkStatusChangedListener(AutoPlayService autoPlayService) {
            weakReference = new WeakReference<>(autoPlayService);
        }

        @Override
        public void onStatusChanged(int i, int i1) {
            AutoPlayService autoPlayService = weakReference.get();
            if (autoPlayService == null) {
                return;
            }
            NetworkMonitor.getInstance(autoPlayService.getApplicationContext()).removeNetworkStatusChangeListener(this);
            if (i == NetworkMonitor.STATUS_MOBILE || i == NetworkMonitor.STATUS_WIFI) {
                autoPlayService.playInit();
            }
        }
    }


    private static class MyOnPlayerInitCompleteListener implements IPlayerInitCompleteListener {
        @Override
        public void onPlayerInitComplete(boolean b) {
            PlayerManager.getInstance().removePlayerInitComplete(this);
            playAudio();
        }
    }

    private void playInit() {
        PlayerManager.getInstance().addPlayerInitComplete(myOnPlayerInitCompleteListener);
    }


    private static void playAudio() {
        //走首页的播放逻辑
        PlayerUtil.playNetOrLocal();
    }


    @Override
    public void onTaskRemoved(Intent rootIntent) {
        super.onTaskRemoved(rootIntent);
    }


    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {

        Context context = getApplicationContext();
        if (NetworkUtil.isNetworkAvailable(context)) {
            playInit();
        } else {
            NetworkMonitor.getInstance(context).registerNetworkStatusChangeListener(myOnNetworkStatusChangedListener);
        }
        return super.onStartCommand(intent, flags, startId);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }
}
