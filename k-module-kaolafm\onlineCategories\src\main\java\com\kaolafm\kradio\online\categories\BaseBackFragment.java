package com.kaolafm.kradio.online.categories;

import androidx.annotation.CallSuper;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.mvp.IPresenter;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.lib.base.ui.online.BaseShowHideFragment;
import com.kaolafm.kradio.lib.utils.ResUtil;


/**
 * <p>不可重写getLayoutId,将顶部箭头所在布局从原布局中删去,保留下边内容的布局</p>
 * <p>
 * <p>如果箭头布局包含标题,使用bbf_title_center_textview,并调用addTitleCenterView()添加</p>
 * <p>
 * <p>如果箭头布局包含Tab,使用bbf_title_center_tablayout,并调用addTitleCenterView()添加</p>
 * <p>
 * <p>通过addContentView(),添加内容布局</p>
 * <p>
 * <p>在initView中做view的初始化</p>
 * <p>
 * <p>目前不支持ButterKnife</p>
 * <p>
 * <p>示例:</p>
 *
 * <p>super.initView(view);
 *
 * <p>View titleView = View.inflate(getContext(), R.layout.bbf_title_center_tablayout, null);
 * <p>
 * <p>this.addTitleCenterView(titleView);
 * <p>
 * <p>mContentView = View.inflate(getContext(), R.layout.fragment_all_categories, null);
 * <p>this.addContentView(mContentView);
 * <p>
 * <p>mTabLayout = (SlidingTabLayout) titleView.findViewById(R.id.stb_all_category_title_name);
 * <p>vRootCate = mContentView.findViewById(R.id.vRootCate);
 * <p>mViewPager = mContentView.findViewById(R.id.vp_all_category_content);
 *
 * <AUTHOR>
 **/
public abstract class BaseBackFragment<P extends IPresenter> extends BaseShowHideFragment<P> {

    ViewGroup bbfTitleLayout;
    LinearLayout bbfCenter;
    FrameLayout bbfRight;
    TextView text_line;
    FrameLayout bbfContent;

    @Override
    protected int getLayoutId() {
        return R.layout.online_fragment_base_back;
    }

    @Override
    @CallSuper
    public void initView(View view) {
        bbfTitleLayout=view.findViewById(R.id.bbf_title);
        bbfCenter=view.findViewById(R.id.bbf_center);
        bbfRight=view.findViewById(R.id.bbf_right);
        text_line=view.findViewById(R.id.text_line);
        bbfContent=view.findViewById(R.id.bbf_content);


    }

    protected void addTitleCenterView(View view) {
        bbfCenter.addView(view);
    }

    protected void addTitleRightView(View view) {
        bbfRight.addView(view);
    }

    protected void addContentView(View view) {
        bbfContent.addView(view);
    }

    protected void setTextlineVisible() {
        text_line.setVisibility(View.VISIBLE);
    }

    protected void setContentMaginTop(){
     LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) bbfContent.getLayoutParams();
        layoutParams.setMargins(0, ResUtil.getDimen(R.dimen.y30),0,0);
    }

    @Override
    @CallSuper
    protected void showAccordingToScreen(int orientation) {
//        ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) bbfBack.getLayoutParams();
//        layoutParams.leftMargin = ScreenUtil.getGlobalBackMarginLeft(bbfBack, orientation);
    }


}
