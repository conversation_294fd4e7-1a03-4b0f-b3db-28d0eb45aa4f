<?xml version="1.0" encoding="utf-8"?>
<!-- padding不能去掉，否则隐藏控件会重叠导致无法正常点击 -->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingBottom="@dimen/m10"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout
        android:id="@+id/trfl_broadcast_list_refresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_broadcast_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never" />
    </com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout>

    <!-- 隐藏控件，用于所见即可说语音执行滑动操作 -->
    <TextView
        style="@style/ContentDescriptionScrollUp"
        android:layout_gravity="top|center_horizontal" />

    <TextView
        style="@style/ContentDescriptionScrollDown"
        android:layout_gravity="bottom|center_horizontal" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/y20"
        android:layout_gravity="bottom"
        android:layout_marginStart="@dimen/m80"
        android:layout_marginEnd="@dimen/m80"
        android:background="@drawable/app_bottom_bg" />

    <include
        android:id="@+id/loadingView"
        layout="@layout/refresh_center"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        tools:visibility="visible" />
</FrameLayout>
