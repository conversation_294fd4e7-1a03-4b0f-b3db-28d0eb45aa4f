<?xml version="1.0" encoding="utf-8"?>
<resources>
    <attr name="canScale" format="boolean" />
    <declare-styleable name="PagerSlidingTabStrip">
        <attr name="pstsIndicatorColor" format="color" />
        <attr name="pstsIndicatorStartColor" format="color" />
        <attr name="pstsIndicatorEndColor" format="color" />
        <attr name="pstsUnderlineColor" format="color" />
        <attr name="pstsDividerColor" format="color" />
        <attr name="pstsTabPaddingTopBottom" format="dimension" />
        <attr name="pstsIndicatorHeight" format="dimension" />
        <attr name="pstsUnderlineHeight" format="dimension" />
        <attr name="pstsDividerPaddingTopBottom" format="dimension" />
        <attr name="pstsTabPaddingLeftRight" format="dimension" />
        <attr name="pstsScrollOffset" format="dimension" />
        <attr name="pstsTabBackground" format="reference" />
        <!--该属性表示里面的TAB是否均分整个PagerSlidingTabStrip控件的宽,true是,false不均分,从左到右排列,默认false-->
        <attr name="pstsShouldExpand" format="boolean" />
        <attr name="pstsTextAllCaps" format="boolean" />
        <attr name="pstsTextSelectedColor" format="color" />
        <attr name="pstsTextSelectedBold" format="boolean" />
        <!--缩放的最大值,0.3表示放大后最大是原来的0.3倍,默认未0.3-->
        <attr name="pstsScaleZoomMax" format="float" />
        <attr name="pstsSmoothScrollWhenClickTab" format="boolean" />
        <!-- 宽为match_parent ，指示器的宽需手动计算的问题,这个属性设置为true,则指示器的位置和字体的宽一样-->
        <attr name="pstsIndicatorWrap" format="boolean" />
    </declare-styleable>

    <declare-styleable name="PlayingIndicator">
        <attr name="bar_num" format="integer" />
        <attr name="duration" format="integer" />
        <attr name="bar_color" format="color|reference" />
        <attr name="step_num" format="integer" />
        <attr name="is_two_way" format="boolean" />
        <attr name="min_height" format="integer|reference" />
    </declare-styleable>

    <declare-styleable name="AreaLayout_Layout">
        <attr name="type" format="enum">
            <enum name="BIG" value="1" />
            <enum name="SMALL" value="0" />
        </attr>
    </declare-styleable>

    <!--<declare-styleable name="SegmentTabLayout">-->
    <!--&lt;!&ndash; indicator &ndash;&gt;-->
    <!--&lt;!&ndash; 设置显示器颜色 &ndash;&gt;-->
    <!--<attr name="tl_indicator_color" format="color" />-->
    <!--&lt;!&ndash; 设置显示器高度 &ndash;&gt;-->
    <!--<attr name="tl_indicator_height" format="dimension" />-->
    <!--&lt;!&ndash; 设置显示器固定宽度 &ndash;&gt;-->
    <!--<attr name="tl_indicator_width" format="dimension" />-->
    <!--&lt;!&ndash; 设置显示器margin,当indicator_width大于0,无效 &ndash;&gt;-->
    <!--<attr name="tl_indicator_margin_left" format="dimension" />-->
    <!--<attr name="tl_indicator_margin_top" format="dimension" />-->
    <!--<attr name="tl_indicator_margin_right" format="dimension" />-->
    <!--<attr name="tl_indicator_margin_bottom" format="dimension" />-->
    <!--&lt;!&ndash; 设置显示器圆角弧度&ndash;&gt;-->
    <!--&lt;!&ndash;<attr name="tl_indicator_corner_radius" format="dimension" />&ndash;&gt;-->
    <!--&lt;!&ndash; 设置显示器上方还是下方,只对圆角矩形有用&ndash;&gt;-->
    <!--&lt;!&ndash;<attr name="tl_indicator_gravity" format="enum">&ndash;&gt;-->
    <!--&lt;!&ndash;<enum name="TOP" value="48" />&ndash;&gt;-->
    <!--&lt;!&ndash;<enum name="BOTTOM" value="80" />&ndash;&gt;-->
    <!--&lt;!&ndash;</attr>&ndash;&gt;-->
    <!--&lt;!&ndash; 设置显示器为常规|三角形|背景色块|&ndash;&gt;-->
    <!--<attr name="tl_indicator_style" format="enum">-->
    <!--<enum name="NORMAL" value="0" />-->
    <!--<enum name="TRIANGLE" value="1" />-->
    <!--<enum name="BLOCK" value="2" />-->
    <!--</attr>-->
    <!--&lt;!&ndash; 设置显示器长度与title一样长,只有在STYLE_NORMAL并且indicatorWidth小于零有效&ndash;&gt;-->
    <!--<attr name="tl_indicator_width_equal_title" format="boolean" />-->
    <!--&lt;!&ndash; 设置显示器支持动画&ndash;&gt;-->
    <!--<attr name="tl_indicator_anim_enable" format="boolean" />-->
    <!--&lt;!&ndash; 设置显示器动画时间&ndash;&gt;-->
    <!--<attr name="tl_indicator_anim_duration" format="integer" />-->
    <!--&lt;!&ndash; 设置显示器支持动画回弹效果&ndash;&gt;-->
    <!--<attr name="tl_indicator_bounce_enable" format="boolean" />-->

    <!--&lt;!&ndash; underline &ndash;&gt;-->
    <!--&lt;!&ndash; 设置下划线颜色 &ndash;&gt;-->
    <!--<attr name="tl_underline_color" format="color" />-->
    <!--&lt;!&ndash; 设置下划线高度 &ndash;&gt;-->
    <!--<attr name="tl_underline_height" format="dimension" />-->
    <!--&lt;!&ndash; 设置下划线上方还是下方&ndash;&gt;-->
    <!--<attr name="tl_underline_gravity" format="enum">-->
    <!--<enum name="TOP" value="48" />-->
    <!--<enum name="BOTTOM" value="80" />-->
    <!--</attr>-->

    <!--&lt;!&ndash; divider &ndash;&gt;-->
    <!--&lt;!&ndash; 设置分割线颜色 &ndash;&gt;-->
    <!--<attr name="tl_divider_color" format="color" />-->
    <!--&lt;!&ndash; 设置分割线宽度 &ndash;&gt;-->
    <!--<attr name="tl_divider_width" format="dimension" />-->
    <!--&lt;!&ndash; 设置分割线的paddingTop和paddingBottom &ndash;&gt;-->
    <!--<attr name="tl_divider_padding" format="dimension" />-->

    <!--&lt;!&ndash; tab &ndash;&gt;-->
    <!--&lt;!&ndash; 设置tab的paddingLeft和paddingRight &ndash;&gt;-->
    <!--<attr name="tl_tab_padding" format="dimension" />-->
    <!--&lt;!&ndash; 设置tab大小等分 &ndash;&gt;-->
    <!--<attr name="tl_tab_space_equal" format="boolean" />-->
    <!--&lt;!&ndash; 设置tab固定宽度大小 &ndash;&gt;-->
    <!--<attr name="tl_tab_width" format="dimension" />-->
    <!--&lt;!&ndash; 设置tab固定高度大小 &ndash;&gt;-->
    <!--<attr name="tl_tab_height" format="dimension|reference" />-->

    <!--&lt;!&ndash; title &ndash;&gt;-->
    <!--&lt;!&ndash; 设置字体大小 &ndash;&gt;-->
    <!--<attr name="tl_textsize" format="dimension" />-->
    <!--&lt;!&ndash; 设置字体选中颜色 &ndash;&gt;-->
    <!--<attr name="tl_textSelectColor" format="color" />-->
    <!--&lt;!&ndash; 设置字体未选中颜色 &ndash;&gt;-->
    <!--<attr name="tl_textUnselectColor" format="color" />-->
    <!--&lt;!&ndash; 设置字体加粗 &ndash;&gt;-->
    <!--<attr name="tl_textBold" format="enum">-->
    <!--<enum name="NONE" value="0" />-->
    <!--<enum name="SELECT" value="1" />-->
    <!--<enum name="BOTH" value="2" />-->
    <!--</attr>-->
    <!--&lt;!&ndash; 设置字体全大写 &ndash;&gt;-->
    <!--<attr name="tl_textAllCaps" format="boolean" />-->

    <!--<attr name="tl_bar_color" format="color" />-->
    <!--<attr name="tl_bar_stroke_color" format="color" />-->
    <!--<attr name="tl_bar_stroke_width" format="dimension" />-->

    <!--<attr name="tl_background_color" format="color" />-->
    <!--<attr name="tl_background_padding" format="dimension" />-->
    <!--&lt;!&ndash;tab的排列方式，水平或垂直&ndash;&gt;-->
    <!--<attr name="tl_tab_orientation" format="enum">-->
    <!--<enum name="horizontal" value="0" />-->
    <!--<enum name="vertical" value="1" />-->
    <!--</attr>-->

    <!--</declare-styleable>-->

    <declare-styleable name="MsgView">
        <!-- 圆角矩形背景色 -->
        <attr name="mv_backgroundColor" format="color" />
        <!-- 圆角弧度,单位dp-->
        <attr name="mv_cornerRadius" format="dimension" />
        <!-- 圆角弧度,单位dp-->
        <attr name="mv_strokeWidth" format="dimension" />
        <!-- 圆角边框颜色-->
        <attr name="mv_strokeColor" format="color" />
        <!-- 圆角弧度是高度一半-->
        <attr name="mv_isRadiusHalfHeight" format="boolean" />
        <!-- 圆角矩形宽高相等,取较宽高中大值-->
        <attr name="mv_isWidthHeightEqual" format="boolean" />
    </declare-styleable>
    <declare-styleable name="CircleProgressImageView">
        <attr name="progress_color" format="color|reference"></attr>
        <attr name="back_color" format="color|reference"></attr>
        <attr name="progress_width" format="dimension|reference"></attr>
    </declare-styleable>

    <declare-styleable name="DiscreteScrollView">
        <attr name="dsv_orientation" format="enum">
            <enum name="horizontal" value="0" />
            <enum name="vertical" value="1" />
        </attr>
    </declare-styleable>

    <!--自定义渐变色进度条-->
    <declare-styleable name="GradientProgressBar">
        <attr name="progressDrawable" format="color|reference"></attr>
        <attr name="progress" format="integer"></attr>
    </declare-styleable>

    <!--带时间显示的seekbar-->
    <declare-styleable name="IndicatorSeekBar">
        <attr name="seek_color" format="color" />
        <attr name="seek_bg_color" format="color" />
        <attr name="seek_txt_size" format="dimension" />
        <!--圆角半径-->
        <attr name="seek_bg_radius" format="dimension" />
        <attr name="txt_offset" format="dimension" />
    </declare-styleable>

    <declare-styleable name="IndicatorV212SeekBar">
        <!--    背景圆角：左上    -->
        <attr name="radius_top_left" format="dimension" />
        <!--    背景圆角：右上    -->
        <attr name="radius_top_right" format="dimension" />
        <!--    背景圆角：左下    -->
        <attr name="radius_bottom_left" format="dimension" />
        <!--    背景圆角：右下    -->
        <attr name="radius_bottom_right" format="dimension" />
        <!--    背景渐变色：上    -->
        <attr name="start_color_bg" format="color" />
        <!--    背景渐变色：下    -->
        <attr name="end_color_bg" format="color" />
        <!--    进度条已到部分背景：上    -->
        <attr name="position_start_color_bg" format="color" />
        <!--    进度条已到部分背景：下    -->
        <attr name="position_end_color_bg" format="color" />
        <!--    上侧进度细条渐变色：左    -->
        <attr name="start_color_seek" format="color" />
        <!--    上侧进度细条渐变色：90%处    -->
        <attr name="center_color_seek" format="color" />
        <!--    上侧进度细条渐变色：右    -->
        <attr name="end_color_seek" format="color" />
        <!--    进度显示滑块颜色：上    -->
        <attr name="start_color_thumb" format="color" />
        <!--    进度显示滑块颜色：下    -->
        <attr name="end_color_thumb" format="color" />
        <!--    进度显示滑块颜色：字体颜色    -->
        <attr name="text_color_thumb" format="color" />
        <!--    进度显示滑块：字体大小    -->
        <attr name="text_size_thumb" format="dimension" />
        <!--    是否使用自身的thumb    -->
        <attr name="need_inner_thumb_view" format="boolean" />
    </declare-styleable>

    <!--负反馈-->
    <declare-styleable name="AIRadioMinusFeedbackView">
        <attr name="minus_location" format="integer"></attr>
    </declare-styleable>

    <!--播放-->
    <declare-styleable name="PlayerControlViewPlay">
        <attr name="play_location" format="integer"></attr>
    </declare-styleable>

    <!--下一首-->
    <declare-styleable name="PlayerControlViewNext">
        <attr name="next_location" format="integer"></attr>
        <attr name="next_icon" format="reference"></attr>
    </declare-styleable>

    <!--上一首-->
    <declare-styleable name="PlayerControlViewPrevious">
        <attr name="previous_location" format="integer"></attr>
        <attr name="previous_icon" format="reference"></attr>
    </declare-styleable>




    <declare-styleable name="RoundRectImageView">
        <attr name="rriv_radius" format="dimension" />
    </declare-styleable>

    <declare-styleable name="PlayAnimView">
        <attr name="playLineCount" format="integer" />
        <attr name="playRatioWidth" format="float" />
        <attr name="playFullStartColor" format="color|reference" />
        <attr name="playFullEndColor" format="color|reference" />
        <attr name="playStrokeWidth" format="dimension|reference" />
        <attr name="playStrokeStartColor" format="color|reference" />
        <attr name="playStrokeCenterColor" format="color|reference" />
        <attr name="playStrokeEndColor" format="color|reference" />
    </declare-styleable>

    <declare-styleable name="YunTingMusicPlayAnimationView">
        <attr name="strokeWidth" format="dimension|reference" />
        <attr name="fullStartColor" format="color|reference" />
        <attr name="fullEndColor" format="color|reference" />
        <attr name="strokeStartColor" format="color|reference" />
        <attr name="strokeCenterColor" format="color|reference" />
        <attr name="strokeEndColor" format="color|reference" />
        <attr name="ratioWidth" format="float" />
        <attr name="lineCount" format="integer" />
    </declare-styleable>

    <attr name="srlTextColorTitle" format="color|reference" />
    <attr name="srlTextColorTime" format="color|reference" />
    <attr name="srlProgressColor" format="color|reference" />
    <attr name="srlArrowColor" format="color|reference" />
    <declare-styleable name="ColorSettableClassicsHeader">
        <attr name="srlTextColorTitle" />
        <attr name="srlTextColorTime" />
        <attr name="srlProgressColor" />
        <attr name="srlArrowColor" />
    </declare-styleable>
    <declare-styleable name="ColorSettableClassicsFooter">
        <attr name="srlTextColorTitle" />
        <attr name="srlProgressColor" />
        <attr name="srlArrowColor" />
    </declare-styleable>

    <declare-styleable name="KradioBannerViewStyle">
        <!--      切换时间间隔  -->
        <attr name="setInterval" format="integer|reference" />
        <!--      动画时间  -->
        <attr name="setAnimDuration" format="integer|reference" />
        <!--      自动开始  -->
        <attr name="autoStart" format="boolean" />
        <!--      当只有一个数据时是否轮播  -->
        <attr name="playWhenSingleData" format="boolean" />
        <!--      方向  -->
        <attr name="setDirection">
            <enum name="BOTTOM_TO_TOP" value="0" />
            <enum name="TOP_TO_BOTTOM" value="1" />
            <enum name="RIGHT_TO_LEFT" value="2" />
            <enum name="LEFT_TO_RIGHT" value="3" />
        </attr>
    </declare-styleable>
    <declare-styleable name="CursorSkinEditText">
        <attr name="cset_cursorDrawable" format="reference" />
    </declare-styleable>
</resources>