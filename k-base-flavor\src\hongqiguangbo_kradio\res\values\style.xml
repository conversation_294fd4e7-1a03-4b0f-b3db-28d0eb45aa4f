<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="AppThemeCompat.splash" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowNoTitle">true</item>//无标题
        <item name="android:windowActionBar">false</item>//无ActionBar
        <item name="android:windowFullscreen">true</item>//全屏即无通知栏
        <!--        <item name="android:windowFullscreen">true</item>//全屏即无通知栏-->
        <item name="android:windowContentOverlay">@null</item>//是否有遮盖
        <!--<item name="android:windowIsTranslucent">true</item>-->
        <!--        <item name="android:windowBackground">@drawable/splash_yunting</item>-->
        <item name="android:windowBackground">@drawable/background_splash</item>
        <!--        <item name="android:windowBackground">@drawable/splash_yt_tmp</item>-->
        <!--        <item name="android:windowBackground">@drawable/ic_launcher_yt</item>-->
        <!--<item name="android:windowAnimationStyle">@style/activityDefaultAnimation</item>-->
        <!--<item name="android:windowAnimationStyle">@style/activityAnim</item>-->
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
    </style>
</resources>