package com.kaolafm.kradio.online.common.view;

import android.content.res.Configuration;
import android.graphics.Rect;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;

import com.kaolafm.kradio.common.helper.SkinHelper;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.flavor.KRadioDialogFragmentInter;
import com.kaolafm.kradio.lib.dialog.BaseDialogFragment;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;

/**
 * <AUTHOR>
 **/
public class FullScreenDialogFragment extends BaseDialogFragment {
    private static final int GRID_TOTAL_SPAN_COUNT = 4;

    private RecyclerView mRecyclerView;
    private GridLayoutManager mLayoutManager;
    private SSL mSpanSizeLookup;
    private RecycleViewSpaceItemDecoration mSpaceDecoration;
    private RecyclerView.Adapter mAdapter;
    private CharSequence mTitle;

    public static FullScreenDialogFragment create() {
        FullScreenDialogFragment fragment = new FullScreenDialogFragment();
        fragment.setGravity(Gravity.TOP);
        KRadioDialogFragmentInter kRadioDialogFragmentInter = (KRadioDialogFragmentInter) ClazzImplUtil
                .getInter("KRadioDialogFragmentImpl");
        if (kRadioDialogFragmentInter == null) {
            fragment.setWidth(WindowManager.LayoutParams.MATCH_PARENT);
            fragment.setHeight(ScreenUtil.getScreenHeightWithoutStatus());
        } else {
            kRadioDialogFragmentInter.setContentWidth(fragment);
            kRadioDialogFragmentInter.setContentHeight(fragment);
        }
        return fragment;
    }

    public void setAdapter(RecyclerView.Adapter adapter) {
        mAdapter = adapter;
    }

    @Override
    public void onStart() {
        super.onStart();
        Window window = getDialog().getWindow();
        WindowManager.LayoutParams params = window.getAttributes();
        if (SkinHelper.isNightMode()) {
            params.dimAmount = 0.9f;
        } else {
            params.dimAmount = 0.7f;
        }
        window.setAttributes(params);
    }

    @Override
    protected View getContentView() {
        FragmentActivity activity = getActivity();
        mLayoutManager = new GridLayoutManager(activity, GRID_TOTAL_SPAN_COUNT, GridLayoutManager.HORIZONTAL, false);
        mSpanSizeLookup = new SSL();
        mSpaceDecoration = new RecycleViewSpaceItemDecoration(ResUtil.getDimen(R.dimen.x10));

        View inflate = View.inflate(activity, R.layout.online_dialog_fullscreen, null);
        mRecyclerView = inflate.findViewById(R.id.dialog_fullscreen_rv);
        mLayoutManager.setSpanSizeLookup(mSpanSizeLookup);
        mRecyclerView.setLayoutManager(mLayoutManager);

        mRecyclerView.addItemDecoration(mSpaceDecoration);

        if (mAdapter != null) {
            mRecyclerView.setAdapter(mAdapter);
        }
        return inflate;
    }

    @Override
    protected void showAccordingToScreen(int orientation) {
        KRadioDialogFragmentInter kRadioDialogFragmentInter = (KRadioDialogFragmentInter) ClazzImplUtil
                .getInter("KRadioDialogFragmentImpl");
        if (kRadioDialogFragmentInter == null) {
            setWidth(WindowManager.LayoutParams.MATCH_PARENT);
            setHeight(ScreenUtil.getScreenHeightWithoutStatus());
        } else {
            kRadioDialogFragmentInter.setContentHeight(FullScreenDialogFragment.this);
            kRadioDialogFragmentInter.setContentWidth(FullScreenDialogFragment.this);
        }
        super.showAccordingToScreen(orientation);
        switch (orientation) {
            case Configuration.ORIENTATION_LANDSCAPE:
                mLayoutManager.setOrientation(GridLayoutManager.HORIZONTAL);
                if (mAdapter != null && mAdapter.getItemCount() < 4) {
                    mLayoutManager.setSpanCount(mAdapter.getItemCount());
                    mSpanSizeLookup.setSpanSize(mAdapter.getItemCount());
                } else {
                    mLayoutManager.setSpanCount(4);
                    mSpanSizeLookup.setSpanSize(4);
                }
                changeLandViewTopMargin();
                break;
            default:
                mLayoutManager.setOrientation(GridLayoutManager.VERTICAL);
                if (mAdapter != null && mAdapter.getItemCount() < 3) {
                    mLayoutManager.setSpanCount(mAdapter.getItemCount());
                } else {
                    mLayoutManager.setSpanCount(3);
                }
                mSpanSizeLookup.setSpanSize(1);
                changePortraitViewTopMargin();
                break;
        }
    }

    //横屏显示
    private void changeLandViewTopMargin() {
        ViewGroup.MarginLayoutParams rootViewLayoutParams =
                (ViewGroup.MarginLayoutParams) mRecyclerView.getLayoutParams();
        rootViewLayoutParams.topMargin = ResUtil.getDimen(R.dimen.y240);
        mRecyclerView.setLayoutParams(rootViewLayoutParams);
    }

    //竖屏显示
    private void changePortraitViewTopMargin() {
        ViewGroup.MarginLayoutParams rootViewLayoutParams =
                (ViewGroup.MarginLayoutParams) mRecyclerView.getLayoutParams();
        rootViewLayoutParams.topMargin = ResUtil.getDimen(R.dimen.y320);
        mRecyclerView.setLayoutParams(rootViewLayoutParams);
    }

    public void setTitle(CharSequence title) {
        mTitle = title;
    }

    /***************************************************************************************************************/
    private class RecycleViewSpaceItemDecoration extends RecyclerView.ItemDecoration {
        private int space;

        public RecycleViewSpaceItemDecoration(int space) {
            this.space = space;
        }

        @Override
        public void getItemOffsets(Rect outRect, View view,
                                   RecyclerView parent, RecyclerView.State state) {
            outRect.right = space;
            outRect.bottom = space;
            outRect.top = space;
            outRect.left = space;
        }
    }

    class SSL extends GridLayoutManager.SpanSizeLookup {
        @Override
        public int getSpanSize(int position) {
            return mSpanSize;
        }

        private int mSpanSize = 4;

        public int getSpanSize() {
            return mSpanSize;
        }

        public void setSpanSize(int spanSize) {
            this.mSpanSize = spanSize;
        }
    }
}
