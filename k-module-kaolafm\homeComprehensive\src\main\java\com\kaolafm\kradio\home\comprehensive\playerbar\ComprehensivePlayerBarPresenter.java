package com.kaolafm.kradio.home.comprehensive.playerbar;

import androidx.lifecycle.Lifecycle;

import android.content.Intent;
import android.util.Log;
import android.view.View;

import com.kaolafm.kradio.common.CP;
import com.kaolafm.kradio.common.ErrorInfo;
import com.kaolafm.kradio.common.ResultCallback;
import com.kaolafm.kradio.lib.bean.SubscribeData;
import com.kaolafm.kradio.common.event.PlayerCheckPreOrNextButtonEBData;
import com.kaolafm.kradio.common.event.PlayerListDelAllEBData;
import com.kaolafm.kradio.common.helper.SubscribeChangeListenerComponent;
import com.kaolafm.kradio.lib.report.ReportUtil;
import com.kaolafm.kradio.common.utils.ThreadUtil;
import com.kaolafm.kradio.component.DynamicComponent;
import com.kaolafm.kradio.component.RealCaller;
import com.kaolafm.kradio.constant.UserComponentConst;
import com.kaolafm.kradio.constant.UserStateObserverProcessorConst;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioPlayerStateInter;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.ComponentUtil;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.player.comprehensive.broadcast.BroadcastPlayListFragment;
import com.kaolafm.kradio.player.event.PlayerChangedEBData;
import com.kaolafm.kradio.player.event.PlayerListChangedEBData;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.player.helper.intercept.OverhaulInterceptManager;
import com.kaolafm.kradio.subscribe.RemoteSubscribeDataSource;
import com.kaolafm.kradio.subscribe.SubscribeChangeListener;
import com.kaolafm.kradio.subscribe.SubscribeHelper;
import com.kaolafm.kradio.subscribe.SubscribeModel;
import com.kaolafm.kradio.user.LoginManager;
import com.kaolafm.kradio.user.UserInfoManager;
import com.kaolafm.opensdk.api.live.model.LiveInfoDetail;
import com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.listener.IGeneralListener;
import com.kaolafm.opensdk.player.logic.model.item.BroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.event.LoginReportEvent;
import com.kaolafm.report.event.PlayerUiControlReportEvent;
import com.kaolafm.report.event.SubscibeReportEvent;
import com.kaolafm.report.util.ReportConstants;
import com.kaolafm.report.util.ReportParameterManager;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.List;

import tv.danmaku.ijk.media.player.IjkMediaPlayer;

import static com.kaolafm.opensdk.api.live.model.LiveInfoDetail.STATUS_LIVING;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: PlayerBarPresenterOnline.java
 *                                                                  *
 * Created in 2018/4/16 下午6:12                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
public class ComprehensivePlayerBarPresenter implements ComprehensivePlayerbarContract.IPlayerPresenter
        , SubscribeChangeListener, LoginManager.LoginListener {

    public static final String TAG = "kradio.playerbar";

    public static final double MAX_PROGRESS = 100;

    private ComprehensivePlayerbarContract.IPlayerView iPlayerView;

    private SubscribeModel mModel;

    private PlayItem mCurrentPlayItem;

    private DynamicComponent mUserStateObserver;

    private final int STATE_NORMAL = 1;
    private final int STATE_SWITCH_LOGIN = 2;
    private int mState = STATE_NORMAL;

    KRadioPlayerStateInter kRadioPlayerStateInter;

    private SubscribeChangeListenerComponent mKaolaSubscribeListener = new SubscribeChangeListenerComponent() {
        @Override
        protected void onSubscribesChanged(List<SubscribeData> subscribes) {
            ComprehensivePlayerBarPresenter.this.onSubscribesChanged(subscribes);
        }
    };

//    private int audioFocusPermits = Integer.MIN_VALUE;

    public ComprehensivePlayerBarPresenter(ComprehensivePlayerbarContract.IPlayerView iPlayerView) {
        super();
        this.iPlayerView = iPlayerView;
        //监听登录状态变化
        LoginManager.getInstance().registerLoginListener(this);
        //
        EventBus.getDefault().register(this);

        addLogStatusOB();
        //
        iPlayerView.setCollectClickListener(v -> {
            // do 底部播放栏订阅按钮点击
            int id = v.getId();
            if (AntiShake.check(id)) {
                return;
            }
            if (checkNetwork()) {
                return;
            }

            if (!LoginManager.getInstance().checkLogin()) {
                mState = STATE_SWITCH_LOGIN;
            } else {
                toggleSubscribeState(false);
            }

//            boolean userBound = UserInfoManager.getInstance().isUserBound();
//            Logging.d("检查是否登录=%s，未登录显示登录页面", userBound);
//            if (!userBound) {
//                SupportActivity sa = (SupportActivity) iPlayerView.getRootActivity();
//                //todo 原方式会从list中取最后一个元素作为栈顶元素，但由于某些fragment未正常移出，导致栈顶fragment错误
//                //todo 此处暂时取第一个元素作为跳转的fromFragment，解决应用崩溃问题
//                FragmentManager supportFragmentManager = sa.getSupportFragmentManager();
//                List<Fragment> fragments = supportFragmentManager.getFragments();
//                Fragment topFragment = fragments.get(fragments.size() - 1);
//                ComponentClient.obtainBuilder(LoginComponentConst.NAME)
//                        .setActionName(LoginProcessorConst.SWITH_TO_LOGIN_FRAG)
//                        .addParam(LoginProcessorConst.TOPFRAG, topFragment)
//                        .addParam(LoginProcessorConst.BACKTYPE, LoginProcessorConst.BACKTYPE_POP)
//                        .build().call();
//                mState = STATE_SWITCH_LOGIN;
//            } else {
//                toggleSubscribeState(false);
//            }

            PlayItem curPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
            String radioId = null, audioId = null;
            if (curPlayItem != null) {
                radioId = curPlayItem.getRadioId();
                audioId = String.valueOf(curPlayItem.getAudioId());
            }
            ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_PLAYER_SUBSCRIBE_BUTTON, "", ReportParameterManager.getInstance().getPage(), ReportConstants.CONTROL_TYPE_SCREEN, null, radioId, audioId, null));
        });

//        PlayerManager.getInstance().addAudioFocusListener(new OnAudioFocusChangeInter() {
//            @Override
//            public void onAudioFocusChange(int i) {
//                Log.d("PENDING_PLAY", "onAudioFocusChange i="+i +" audioFocusPermits: "+audioFocusPermits);
//                if(i == 1 && audioFocusPermits == 0){
//                    PlayerManagerHelper.getInstance().play(true);
//                    Log.d("PENDING_PLAY", "play");
//                }
////                Log.d("PENDING_PLAY", "222 onAudioFocusChange i="+i +" audioFocusPermits: "+audioFocusPermits);
//                audioFocusPermits--;
//            }
//        });
        iPlayerView.setNextClickListener(v -> {
            int id = v.getId();
            if (AntiShake.check(id)) {
                return;
            }
            if (checkNetwork()) {
                return;
            }
            OverhaulInterceptManager.getInstance().userClickNext();
//            audioFocusPermits = 2;
//            Log.d("PENDING_PLAY", "OnNextClickListener audioFocusPermits: "+audioFocusPermits);
            if (PlayerManagerHelper.getInstance().getCurrentPlayType() == PlayerConstants.RESOURCES_TYPE_BROADCAST ||
                    PlayerManagerHelper.getInstance().getCurrentPlayType() == PlayerConstants.RESOURCES_TYPE_TV) {
                if (PlayerManagerHelper.getInstance().isInProgramPlayerPage()) {
                    //播放详情页
                    //若获取不到节目单，详情页展示图标及暂无节目单文案，图标参照UI设计图，底部播放器上一个/下一个置灰处理，点击可toast提示：暂不支持节目切换
                    if (!PlayerManagerHelper.getInstance().isHasBroadcastPlayList()) {
                        ToastUtil.showInfo(v.getContext(), R.string.comprehensive_unsupport_change_program);
                        return;
                    }
                    if (!PlayerManagerHelper.getInstance().hasNextProgramItem()
                            || PlayerManagerHelper.getInstance().isNotStartProgram(PlayerManagerHelper.getInstance().getNextPlayItem())) {
                        ToastUtil.showOnly(v.getContext(), R.string.no_next_program);
                        return;
                    }
                    OverhaulInterceptManager.getInstance().intercept(v.getContext(), PlayerManagerHelper.getInstance().getNextPlayItem(), true);
                } else if (!PlayerManagerHelper.getInstance().hasNextItem()) {
                    ToastUtil.showInfo(v.getContext(), R.string.no_next_broadcast);
                    return;
                }
            } else if (PlayerManagerHelper.getInstance().getCurrentPlayType() == PlayerConstants.RESOURCES_TYPE_LIVING) {
                ToastUtil.showInfo(v.getContext(), R.string.comprehensive_live_cannot_switch_program);
                return;
            } else if (PlayerManager.getInstance().getCustomType() == PlayerConstants.RESOURCES_TYPE_VIDEO_ALBUM ||
                            PlayerManager.getInstance().getCustomType() == PlayerConstants.RESOURCES_TYPE_VIDEO_AUDIO) {
                if (!PlayerManagerHelper.getInstance().hasNextItem()) {
                    if (PlayerManager.getInstance().getPlayList().size() >= 1) {
                        ToastUtil.showInfo(v.getContext(), R.string.video_no_more_next);
                    }
                    return;
                }
            } else if (!PlayerManagerHelper.getInstance().hasNextItem()) {
                ToastUtil.showInfo(v.getContext(), R.string.no_next_audio);
                return;
            }
            String position = PlayerUiControlReportEvent.POSITION_PLAY_BAR;
//            if (PlayerManagerHelper.getInstance().isInProgramPlayerPage()) {
//                position = PlayerUiControlReportEvent.POSITION_PLAY_FRAGEMNT;
//            }
            ReportUtil.addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_NEXT, PlayerUiControlReportEvent.CONTROL_TYPE_CLICK, position);
            PlayerManagerHelper.getInstance().playNext(true);
        });

        iPlayerView.setPreClickListener(v -> {
            int id = v.getId();
            if (AntiShake.check(id)) {
                return;
            }
            if (checkNetwork()) {
                return;
            }
            OverhaulInterceptManager.getInstance().userClickPre();
//            audioFocusPermits = 2;
//            Log.d("PENDING_PLAY", "OnNextClickListener audioFocusPermits: "+audioFocusPermits);
            if (PlayerManagerHelper.getInstance().getCurrentPlayType() == PlayerConstants.RESOURCES_TYPE_BROADCAST ||
                    PlayerManagerHelper.getInstance().getCurrentPlayType() == PlayerConstants.RESOURCES_TYPE_TV) {
                if (PlayerManagerHelper.getInstance().isInProgramPlayerPage()) {
                    //播放详情页
                    //若获取不到节目单，详情页展示图标及暂无节目单文案，图标参照UI设计图，底部播放器上一个/下一个置灰处理，点击可toast提示：暂不支持节目切换
                    if (!PlayerManagerHelper.getInstance().isHasBroadcastPlayList()) {
                        ToastUtil.showInfo(v.getContext(), R.string.comprehensive_unsupport_change_program);
                        return;
                    }
                    if (!PlayerManagerHelper.getInstance().hasPreProgramItem()) {
                        ToastUtil.showOnly(v.getContext(), R.string.no_pre_program);
                        return;
                    }
                    OverhaulInterceptManager.getInstance().intercept(v.getContext(), PlayerManagerHelper.getInstance().getPrevPlayItem(), true);
                } else if (!PlayerManagerHelper.getInstance().hasPreItem()) {
                    ToastUtil.showInfo(v.getContext(), R.string.no_pre_broadcast);
                    return;
                }
            } else if (PlayerManagerHelper.getInstance().getCurrentPlayType() == PlayerConstants.RESOURCES_TYPE_LIVING) {
                ToastUtil.showInfo(v.getContext(), R.string.comprehensive_live_cannot_switch_program);
                return;
            } else if (PlayerManager.getInstance().getCustomType() == PlayerConstants.RESOURCES_TYPE_VIDEO_ALBUM ||
                            PlayerManager.getInstance().getCustomType() == PlayerConstants.RESOURCES_TYPE_VIDEO_AUDIO) {
                if (!PlayerManagerHelper.getInstance().hasPreItem()) {
                    if (PlayerManager.getInstance().getPlayList().size() >= 1) {
                        ToastUtil.showInfo(v.getContext(), R.string.video_no_more_pre);
                    }
                    return;
                }
            } else if (!PlayerManagerHelper.getInstance().hasPreItem()) {
                ToastUtil.showInfo(v.getContext(), R.string.no_pre_audio);
                return;
            }
            String position = PlayerUiControlReportEvent.POSITION_PLAY_BAR;
//            if (PlayerManagerHelper.getInstance().isInProgramPlayerPage()) {
//                position = PlayerUiControlReportEvent.POSITION_PLAY_FRAGEMNT;
//            }
            ReportUtil.addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_PREVIOUS, PlayerUiControlReportEvent.CONTROL_TYPE_CLICK, position);
            PlayerManagerHelper.getInstance().playPre(true);
        });

        iPlayerView.setPlayOrPauseClickListener(v -> {
            int id = v.getId();
            if (AntiShake.check(id)) {
                return;
            }

            String position = PlayerUiControlReportEvent.POSITION_PLAY_BAR;
//            if (PlayerManagerHelper.getInstance().isInProgramPlayerPage()) {
//                position = PlayerUiControlReportEvent.POSITION_PLAY_FRAGEMNT;
//            }
            if (PlayerManagerHelper.getInstance().isPlaying()) {
                //可以暂停
                ReportUtil.addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_PAUSE, PlayerUiControlReportEvent.CONTROL_TYPE_CLICK, position);
                PlayerManagerHelper.getInstance().switchPlayerStatus(true);
            } else {
                //如果是暂停状态,判断是否有网络;
                if (checkNetwork()) {
                    return;
                }
                PlayItem playItem = PlayerManagerHelper.getInstance().getCurPlayItem();
                if (playItem != null && playItem.getType() == PlayerConstants.RESOURCES_TYPE_LIVING) {
                    switch (playItem.getStatus()) {
                        case LiveInfoDetail.STATUS_FINISHED:
                            ToastUtil.showNormal(v.getContext().getApplicationContext(), ResUtil.getString(R.string.player_failed_live_finished));
                            return;
                        case LiveInfoDetail.STATUS_COMING:
                        case LiveInfoDetail.STATUS_START_TODAY:
                        case LiveInfoDetail.STATUS_START_TOMORROW:
                        case LiveInfoDetail.STATUS_START_AFTER_TOMORROW:
                        case LiveInfoDetail.STATUS_NOT_START:
                        case LiveInfoDetail.STATUS_DELAYED:
                            ToastUtil.showNormal(v.getContext().getApplicationContext(), ResUtil.getString(R.string.player_failed_live_not_start));
                            return;
                    }
                }
                ReportUtil.addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_PLAY, PlayerUiControlReportEvent.CONTROL_TYPE_CLICK, position);
                PlayerManagerHelper.getInstance().switchPlayerStatus(true);
            }
        });

        kRadioPlayerStateInter = ClazzImplUtil.getInter("KRadioPlayerStateImpl");
    }

    @Override
    public void attachPlayer() {
        //解决某些渠道中播放器不销毁，socket数据不清空的情况下，再次进入app中状态没有刷新的问题
        mCurrentPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
        if (mCurrentPlayItem != null && iPlayerView != null) {
            ThreadUtil.runOnUI(() -> {
                iPlayerView.setEnabled(true);
                updateBtnsState();
                iPlayerView.showLoading(false);
                iPlayerView.updateInfo(getCP(), mCurrentPlayItem);
            });
        }
        PlayerManager.getInstance().addPlayControlStateCallback(basePlayStateListener);
        PlayerManager.getInstance().addGeneralListener(mGeneralListener);
        if (mCurrentPlayItem != null) {
            iPlayerView.updateInfo(getCP(), mCurrentPlayItem);
            if (PlayerManagerHelper.getInstance().isPlayingClock()) {
                iPlayerView.showPauseState();
            }
//            int progroess;
            // 获取 当前的条目算出来百分比
//            if (mCurrentPlayItem.getDuration() != 0) {
//                progroess = (mCurrentPlayItem.getPosition() * 100) / mCurrentPlayItem.getDuration();
//            } else {
//                progroess = 0;
//            }
//            iPlayerView.updateProgress(progroess);
            iPlayerView.updateProgress(mCurrentPlayItem.getPosition(), mCurrentPlayItem.getDuration());
        }
        mState = STATE_NORMAL;
    }


    @Override
    public void detachPlayer() {
        PlayerManager.getInstance().removePlayControlStateCallback(basePlayStateListener);
        PlayerManager.getInstance().removeGeneralListener(mGeneralListener);
        EventBus.getDefault().unregister(this);
        LoginManager.getInstance().unregisterLoginListener(this);
        ComponentUtil.removeObserver(UserComponentConst.NAME, mUserStateObserver);
        iPlayerView = null;
    }

    @Override
    public void attachSubscribeModel(SubscribeModel subscribeModel) {
        this.mModel = subscribeModel;
        this.mModel.addSubscribeChangeListener(mKaolaSubscribeListener);
    }

    @Override
    public void detachSubscribeModel() {
        if (mModel != null) {
            this.mModel.removeSubscribeChangeListener(mKaolaSubscribeListener);
            this.mModel = null;
        }
    }

    /**
     * 考拉资源的订阅
     */
    private void setKaolafmSubscribeState() {
        long id = PlayerManagerHelper.getInstance().getSubscribeId(true);
        if (-1 == id) {
            if (iPlayerView != null) {
                iPlayerView.setCollectState(false);
            }
        } else {
            if (mModel != null)
                mModel.isSubscribed(String.valueOf(id), new ResultCallback() {
                    @Override
                    public void onResult(boolean result, int status) {
                        if (iPlayerView != null) {
                            iPlayerView.setCollectState(result);
                            boolean canSubscribe = PlayerManager.getInstance().getPlayListInfo().getNoSubscribe() != 1;
                            Intent intent = new Intent().setAction("com.yunting.subscribe");
                            intent.putExtra("canSubscribe", canSubscribe);
                            intent.putExtra("isSubscribe", result);
                            AppDelegate.getInstance().getContext().sendBroadcast(intent);
                        }
                    }

                    @Override
                    public void onFailure(ErrorInfo errorInfo) {
                        if (iPlayerView != null) {
                            iPlayerView.setCollectState(false);
                        }
                    }
                });
        }
    }


    /**
     * 订阅或上一首是否可用
     *
     * @return
     */
    private boolean isSubscribeEnable() {
        boolean isSubscribeEnable = false;

        int type = PlayerManagerHelper.getInstance().getCurPlayItem().getType();
        switch (type) {
            case PlayerConstants.RESOURCES_TYPE_RADIO:
            case PlayerConstants.RESOURCES_TYPE_ALBUM:
            case PlayerConstants.RESOURCES_TYPE_ONE_KEY_LISTENER_TYPE:
            case PlayerConstants.RESOURCES_TYPE_PURCHASE_ONE_KEY_LISTENER_TYPE:
            case PlayerConstants.RESOURCES_TYPE_LIVE_STREAM:
                isSubscribeEnable = true;
                break;
            case PlayerConstants.RESOURCES_TYPE_BROADCAST:
                isSubscribeEnable = PlayerManagerHelper.getInstance().hasPreItem();
                break;
            case PlayerConstants.RESOURCES_TYPE_LIVING:
                isSubscribeEnable = false;
                break;
            default:
                break;
        }
        return isSubscribeEnable;
    }


    private void toggleSubscribeState(boolean isLogin) {
        long id = PlayerManagerHelper.getInstance().getSubscribeId(true);
        if (-1 == id) {
            if (iPlayerView != null) {
                iPlayerView.setCollectState(false);
            }
        } else {
            if (mCurrentPlayItem == null || mModel == null) {
                return;
            } else {
                Log.i(TAG, "    toggleSubscribeState: " + id);
                if (iPlayerView != null && iPlayerView.getCollectState()) {
                    kaolaUnsubscribe(id);
                } else {
                    toggleKaolafmSubscribeState(id, isLogin);
                }
            }
        }
    }

    private boolean checkNetwork() {
        // 暂无网络
        if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
            return true;
        }
        return false;
    }


    /**
     * 切换考拉订阅状态
     *
     * @param id
     */
    private void toggleKaolafmSubscribeState(final long id, final boolean isLogin) {
        Log.i(TAG, "订阅" + id);

        if (mModel != null)
            mModel.isSubscribed(String.valueOf(id), new ResultCallback() {
                @Override
                public void onResult(boolean result, int status) {
                    Log.i(TAG, "onResult:" + id + ":z 从model获取订阅状态:" + result);
                    if (result && !isLogin) {
                        kaolaUnsubscribe(id);
                    } else {
                        // 如果服务端返回已订阅，那么只更新界面，为修复缺陷： #40918，增加了代码理解难度
                        if (result && null != iPlayerView) {
                            // //更改订阅状态;不弹toast;
                            iPlayerView.setCollectState(true);
                        } else {
                            kaolaSubscribe(id);
                        }
                    }
                }

                @Override
                public void onFailure(ErrorInfo errorInfo) {
                    ToastUtil.showError(AppDelegate.getInstance().getContext(), R.string.subscribe_failed_str);
                    Log.i(TAG, "onResult:" + id + " 从model获取订阅状态,出错:" + errorInfo.code);
                    if (iPlayerView != null) {
                        iPlayerView.setCollectState(false);
                    }
                }
            });
    }

    private SubscribeData getSubscribeData(long id) {
        if (mCurrentPlayItem == null) {
            return null;
        }

        SubscribeData sd = new SubscribeData();
        sd.setId(id);
        sd.setName(mCurrentPlayItem.getAlbumTitle());
        sd.setType(SubscribeHelper.convertSubscriptionType(mCurrentPlayItem.getType()));
        sd.setImg(mCurrentPlayItem.getPicUrl());
        sd.setUpdateTime(mCurrentPlayItem.getUpdateTime());
        sd.setLocation(SubscibeReportEvent.POSITION_PLAY_BAR);
        return sd;
    }

    private void kaolaSubscribe(long id) {
        SubscribeData sd = getSubscribeData(id);
        if (sd == null || mModel == null) {
            return;
        }
        mModel.subscribe(sd, new ResultCallback() {
            @Override
            public void onResult(boolean result, int status) {
                Log.i(TAG, "    onResult:" + id + ": 订阅:" + result + ",status=" + status);

                //如果订阅成功,则设置为true;否则,不操作.
                if (iPlayerView != null) {
                    //
                    if (status == RemoteSubscribeDataSource.STATUS_SUCCESS) {
                        //更改订阅状态;弹toast;
                        iPlayerView.setCollectState(true);
                        iPlayerView.showToast("订阅成功");
                        boolean canSubscribe = PlayerManager.getInstance().getPlayListInfo().getNoSubscribe() != 1;
                        Intent intent = new Intent().setAction("com.yunting.subscribe");
                        intent.putExtra("canSubscribe", canSubscribe);
                        intent.putExtra("isSubscribe", true);
                        AppDelegate.getInstance().getContext().sendBroadcast(intent);
                    } else if (RemoteSubscribeDataSource.SUBSCRIBED_STATUS == status) {
                        // //更改订阅状态;不弹toast;
                        iPlayerView.setCollectState(true);
                    } else {
                        //更改订阅状态;弹toast;
                        iPlayerView.setCollectState(false);
                        iPlayerView.showToast("订阅失败");
                    }
                }
            }

            @Override
            public void onFailure(ErrorInfo errorInfo) {
                Log.i(TAG, "    onResult:" + id + " 订阅出错:" + errorInfo.code);
                if (iPlayerView != null) {
                    //恢复未订阅状态
                    iPlayerView.setCollectState(false);
                    iPlayerView.showToast("订阅失败");
                }
            }
        });

        //现在UI上显示
        if (iPlayerView != null) {
            iPlayerView.setCollectState(true);
        }
    }

    private void kaolaUnsubscribe(long id) {
        SubscribeData sd = getSubscribeData(id);
        if (sd == null || mModel == null) {
            return;
        }
        mModel.unsubscribe(sd, new ResultCallback() {
            @Override
            public void onResult(boolean result, int status) {
                Log.i(TAG, "    onResult:" + sd.getId() + ": model取消订阅:" + result);

                //如果取消成功,则设置为false;否则,不操作.
                if (iPlayerView != null) {
                    if (result) {
                        iPlayerView.setCollectState(false);
                        iPlayerView.showToast("取消订阅成功");
                        boolean canSubscribe = PlayerManager.getInstance().getPlayListInfo().getNoSubscribe() != 1;
                        Intent intent = new Intent().setAction("com.yunting.subscribe");
                        intent.putExtra("canSubscribe", canSubscribe);
                        intent.putExtra("isSubscribe", false);
                        AppDelegate.getInstance().getContext().sendBroadcast(intent);
                    } else {
                        //恢复订阅状态
                        iPlayerView.setCollectState(true);
                        iPlayerView.showToast("取消订阅失败");
                    }
                }
            }

            @Override
            public void onFailure(ErrorInfo errorInfo) {
                //Log.i(TAG, "    onResult:" + sd.getId()+ ": model取消订阅出错:" + errorInfo.code);
                if (iPlayerView != null) {
                    //恢复订阅状态
                    iPlayerView.setCollectState(true);
                    iPlayerView.showToast("取消收藏失败");
                }
            }
        });

        //现在UI上显示
        if (iPlayerView != null) {
            iPlayerView.setCollectState(false);
        }
    }


    @Override
    public void onSubscribesChanged(List<SubscribeData> subscribes) {
        boolean subscribed = false;
        long id = PlayerManagerHelper.getInstance().getSubscribeId(true);

        if (-1 == id) {
            if (iPlayerView != null) {
                iPlayerView.setCollectState(false);
            }
        } else {
            if (subscribes != null && !subscribes.isEmpty()) {
                for (SubscribeData s : subscribes) {
                    if (s.getId() == id) {
                        subscribed = true;
                        break;
                    }
                }
            }
            if (iPlayerView != null) {
                iPlayerView.setCollectState(subscribed);
            }
        }
    }

    private BasePlayStateListener basePlayStateListener = new BasePlayStateListener() {
        @Override
        public void onIdle(PlayItem playItem) {
            Log.d(TAG, "onIdle");
            // 解决从可播放专辑切换到未购买付费专辑时进度条不变动的问题
            if (iPlayerView.getLifecycle().getCurrentState().isAtLeast(Lifecycle.State.CREATED)) {
                if (playItem.getType() != PlayerConstants.RESOURCES_TYPE_LIVING) {
                    iPlayerView.updateProgress(-1); // 以-1为标志位，以便和正常进度为0的情况作区分，防止进度条闪动
                }
            }
        }

        @Override
        public void onPlayerPreparing(PlayItem playItem) {
            Log.i(TAG, "onPlayerPreparing");
            mCurrentPlayItem = playItem;
            if (iPlayerView != null) {
                iPlayerView.setEnabled(true);
                updateBtnsState();
                iPlayerView.updateInfo(getCP(), playItem);
                boolean flag = iPlayerView.getLifecycle().getCurrentState().isAtLeast(Lifecycle.State.CREATED);
                if (flag) {
                    iPlayerView.showLoading(true);
                }
            }
            setKaolafmSubscribeState();
        }

        @Override
        public void onPlayerPlaying(PlayItem playItem) {
            Log.i(TAG, "onPlayerPlaying --- getPlaybackRate()=" + PlayerManager.getInstance().getPlaybackRate());

            if (iPlayerView == null) {
                return;
            }
            boolean flag = iPlayerView.getLifecycle().getCurrentState().isAtLeast(Lifecycle.State.CREATED);
            if (flag && (kRadioPlayerStateInter == null || kRadioPlayerStateInter.isPlaying())) {
                iPlayerView.showLoading(false);
                iPlayerView.showPlayState();
                iPlayerView.showPlayBarType();
                //   iPlayerView.updateInfo(getCP(), playItem);
            }
            setKaolafmSubscribeState();
        }

        @Override
        public void onPlayerPaused(PlayItem playItem) {
            Log.i(TAG, " onPlayerPaused=iPlayerView=" + iPlayerView);
            if (iPlayerView == null) {
                return;
            }
            if (iPlayerView.getLifecycle().getCurrentState().isAtLeast(Lifecycle.State.CREATED)) {
                iPlayerView.showLoading(false);
                Log.i(TAG, " iPlayerView.showPauseState()");
                iPlayerView.showPauseState();
            }
        }

        @Override
        public void onProgress(PlayItem playItem, long progress, long duration) {
            if (iPlayerView == null || playItem == null) {
                return;
            }
            if (!needUpdateProgress(playItem)) {
//                return;
            }
            if (progress == 0) {
                return;
            }
//            iPlayerView.setProgressEnabled(true);
            if (iPlayerView.getLifecycle().getCurrentState().isAtLeast(Lifecycle.State.CREATED)) {
                iPlayerView.updateProgress((int) progress, (int) duration);
            }
        }

        @Override
        public void onPlayerFailed(PlayItem playItem, int what, int extra) {
            Log.i(TAG, " onPlayerFailed");
            // 由于ijkplayer内部错误导致的播放失败
            super.onPlayerFailed(playItem, what, extra);
            if (iPlayerView == null) {
                return;
            }
            if (iPlayerView.getLifecycle().getCurrentState().isAtLeast(Lifecycle.State.CREATED)) {
                iPlayerView.showPauseState();
                iPlayerView.showLoading(false);
                // fixed 此处在播放失败时进行提示，虽然提前判断了网络状态，但在网络不稳定时，
                //  ijkplayer播放时由于网络或服务器异常反馈失败，但此时的网络状态可能是可用状态，也会出现提示错误的情况
                //todo
                Log.i(TAG, "onPlayerFailed: what=" + what + " extra=" + extra);
                if (what == IjkMediaPlayer.MEDIA_ERROR_IJK_PLAYER && extra == IjkMediaPlayer.MEDIA_ERROR_NETWORK_FAILED || extra == IjkMediaPlayer.MEDIA_ERROR_IMMEDIATE_EXIT) {
                    iPlayerView.showError(R.string.ijkplayer_network_failed);
                } else if (what == PlayerConstants.ERROR_CODE_RADIO_COPYRIGHT_LITE) {
                    iPlayerView.showError(R.string.comprehensive_radio_is_lite);
                } else if (what == PlayerConstants.ERROR_CODE_PLAY_LIST_URL_NULL && playItem instanceof BroadcastPlayItem
                        && ((BroadcastPlayItem) playItem).getProgramEnable() == PlayerConstants.BROADCAST_PROGRAM_SHOW) {
                    //产品需求：如果后台配置了展示播单且可以播放，但是播放地址为null，则认为正在转码中，提示会听生成中
                    iPlayerView.showError(R.string.comprehensive_broadcast_backplay_error);
//                    EventBus.getDefault().post(new BroadcastPlayListFragment.BroadcastGeneratingEven());
//                    iPlayerView.showPlayState();
//                    iPlayerView.showLoading(false);

                } else if (what == PlayerConstants.ERROR_CODE_NO_COPYRIGHT) {
                    iPlayerView.showError(R.string.no_copyright);
                } else {
                    //特殊场景：主站直播如果是未开播状态，那么播放地址会返回null，这时候需要进行判断
                    if (playItem.getType() == PlayerConstants.RESOURCES_TYPE_LIVING) {
//                        if (playItem.getStatus() != STATUS_LIVING && what == PlayerConstants.ERROR_CODE_PLAY_LIST_URL_NULL) {
//                        }
                        return;
                    }
                    iPlayerView.showError(R.string.is_not_online);
                }
            }
        }

        @Override
        public void onPlayerEnd(PlayItem playItem) {
            Log.i(TAG, " onPlayerEnd");

            super.onPlayerEnd(playItem);
            if (iPlayerView == null) {
                return;
            }
            if (iPlayerView.getLifecycle().getCurrentState().isAtLeast(Lifecycle.State.CREATED)) {
                iPlayerView.showPauseState();
                iPlayerView.showLoading(false);
            }
        }

        @Override
        public void onSeekStart(PlayItem playItem) {
            Log.i(TAG, " onSeekStart");

            super.onSeekStart(playItem);
        }

        @Override
        public void onSeekComplete(PlayItem playItem) {
            Log.i(TAG, " onSeekComplete");

            super.onSeekComplete(playItem);
            String eventPosition = PlayerUiControlReportEvent.POSITION_PLAY_BAR;
//            if (PlayerManagerHelper.getInstance().isInProgramPlayerPage()) {
//                eventPosition = PlayerUiControlReportEvent.POSITION_PLAY_FRAGEMNT;
//            }
            ReportUtil.addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_CHANGE_PROGRESS, PlayerUiControlReportEvent.CONTROL_TYPE_SLIDE, eventPosition);
        }

        @Override
        public void onBufferingStart(PlayItem playItem) {
            Log.i(TAG, " onBufferingStart");

            if (iPlayerView == null) {
                return;
            }
            iPlayerView.showLoading(true);

            if (!needUpdateProgress(playItem)) {
                return;
            }


            if (iPlayerView.getLifecycle().getCurrentState().isAtLeast(Lifecycle.State.CREATED)) {
                if (playItem.getPosition() != 0) {
//                    iPlayerView.updateProgress(getProgress(playItem.getPosition(), playItem.getDuration()));
                    iPlayerView.updateProgress(playItem.getPosition(), playItem.getDuration());
                }
            }
        }

        @Override
        public void onBufferingEnd(PlayItem playItem) {
            Log.i(TAG, " onBufferingEnd");
            if (iPlayerView != null)
                iPlayerView.showLoading(false);
        }

        @Override
        public void onDownloadProgress(PlayItem playItem, long progress, long l) {
            super.onDownloadProgress(playItem, progress, l);
        }
    };

    private boolean needUpdateProgress(PlayItem playItem) {
        if (iPlayerView == null) return false;
        if (playItem == null || playItem.isLiving()) {
            iPlayerView.setProgressEnabled(false);
            return false;
        }
        iPlayerView.setProgressEnabled(true);
        return true;
    }


    private int getProgress(int progress, int duration) {
        return (int) (((float) progress) / duration * MAX_PROGRESS);
    }

    protected @CP.CpType
    int getCP() {
        return CP.KaoLaFM;
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPreOrNextChanged(PlayerCheckPreOrNextButtonEBData event) {
        //排序发生变化,下一首按钮是否置灰
        if (iPlayerView != null) {
            iPlayerView.setNextState(PlayerManagerHelper.getInstance().hasNextItem());
            iPlayerView.setPrevState(PlayerManagerHelper.getInstance().hasPreItem());
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onDeleteAllChanged(PlayerListDelAllEBData event) {
        if (iPlayerView != null) {
            iPlayerView.setEnabled(false);
            iPlayerView.setTitle("QQ音乐 听我想听");
        }

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPlayerChanged(PlayerChangedEBData playerChangedEBData) {
//
//        int whichPlayer = PlayerHelper.whichPlayer();
//        switch (whichPlayer) {
//            case PlayerHelper.PLAYER_TYPE_LIVE: {
//            }
//            break;
//            case PlayerHelper.PLAYER_TYPE_BROADCAST: {
//                Log.i(TAG, "onPlayerChanged: 播放器切换为广播播放器");
//                if (mModel != null) {
//                    mModel.changeSubscribeManager(CP.KaoLaFM);
//                }
//            }
//            break;
//            default: {
//                Log.i(TAG, "onPlayerChanged: 播放器切换为正常播放器");
//                if (mModel != null) {
//                    mModel.changeSubscribeManager(CP.KaoLaFM);
//                }
//            }
//            break;
//        }

    }


//    /**
//     * <p>如果是考拉的资源,只要判断是否登录了kradio;
//     * <p>如果是QQ的资源,先判断是否登录了kradio,然后再判断是否登录了qq.
//     *
//     * @param loginListener
//     * @return
//     */
//    private boolean checkLogin(int cp, LoginManager.LoginListener loginListener) {
//
//        boolean rst = false;
//
//        rst = LoginManager.getInstance().checkLoginOrJumpLoginPage(cp, loginListener);
//
//        Log.i("showLoginInfo", "checkLoginOrJumpLoginPage: " + rst);
//        return rst;
//
//    }

    /***********************************************************************************************/
    /************************************** 登录 ****************************************/
    /***********************************************************************************************/
    @Override
    public void onLoginStateChange(int cp, boolean isLogin) {
        updateBtnsState();
    }


    @Override
    public void onCancel() {
    }

    public void updateBtnsState() {
//        boolean isSubscribeEnable = isSubscribeEnable();
//
//        if (isSubscribeEnable) {
//            //订阅状态
//            if (mModel == null) {
//                if (iPlayerView != null) {
//                    iPlayerView.setCollectState(false);
//                }
//            } else {
//                setKaolafmSubscribeState();
//            }
//        }

        //设置上一首/收藏,下一首按钮是否可用
        if (iPlayerView != null) {
            //除了直播,其他播放器都有下一首的按钮;判断是否点
//            iPlayerView.setPrevState(isSubscribeEnable);
            iPlayerView.setPrevState(PlayerManagerHelper.getInstance().hasPreItem());
            iPlayerView.setNextState(PlayerManagerHelper.getInstance().hasNextItem());
        }
    }

    /**
     * #36337
     *
     * @param playerListChangedEBData
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPlayerListChange(PlayerListChangedEBData playerListChangedEBData) {
        if (iPlayerView == null) {
            return;
        }
        updateBtnsState();
        iPlayerView.updateInfo(getCP(), PlayerManagerHelper.getInstance().getCurPlayItem());
    }


    private IGeneralListener mGeneralListener = new IGeneralListener() {

        @Override
        public void getPlayListError(PlayItem playItem, int i, int i1) {
            if (isPlayBroadcastError()) {
                if (iPlayerView == null) {
                    return;
                }
                iPlayerView.updateBroadcastErrorInfo();
                updateBtnsState();
                iPlayerView.setPrevState(PlayerManagerHelper.getInstance().hasPreItem());
            }
        }

        @Override
        public void playUrlError(int code) {

        }
    };

    private boolean isPlayBroadcastError() {
        PlayItem playItemTemp = PlayerManagerHelper.getInstance().getCurPlayItem();
        if (playItemTemp.getType() != PlayerConstants.RESOURCES_TYPE_INVALID) {
            return false;
        }
        if (PlayerManagerHelper.getInstance().getCurrentPlayType() != PlayerConstants.RESOURCES_TYPE_BROADCAST) {
            return false;
        }
        return true;
    }

    private void addLogStatusOB() {
        mUserStateObserver = new DynamicComponent() {
            @Override
            public String getName() {
                return "PlayerBarPresenterOnline-DynamicComponent";
            }

            @Override
            public boolean onCall(RealCaller caller) {
                switch (caller.actionName()) {
                    case UserStateObserverProcessorConst.USER_LOGIN: {
                        if (mState == STATE_SWITCH_LOGIN) {
                            reportLoginEvent(UserInfoManager.getInstance().getLoginType(),
                                    LoginReportEvent.REMARKS1_MINI_PLAYER_SUBSCRIBE);
                            mState = STATE_NORMAL;
                            toggleSubscribeState(true);
                        }
                        break;
                    }
                    case UserStateObserverProcessorConst.USER_LOGOUT: {
                        break;
                    }
                }
                return false;
            }
        };
        ComponentUtil.addObserverNoRepeat(UserComponentConst.NAME, mUserStateObserver);
    }

    private void reportLoginEvent(String type, String remarks1) {
        LoginReportEvent event = new LoginReportEvent();
        event.setType(type);
        event.setRemarks1(remarks1);
        ReportHelper.getInstance().addEvent(event);
    }
}
