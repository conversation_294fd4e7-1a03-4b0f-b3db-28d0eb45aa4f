<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/m24"
    tools:background="#2D3242">

    <TextView
        android:id="@+id/head_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/m28"
        android:ellipsize="end"
        android:gravity="center"
        android:lines="1"
        android:textColor="@color/text_color_7"
        android:textSize="@dimen/m26"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="唐山台" />

    <View
        android:id="@+id/divider"
        android:layout_width="0dp"
        android:layout_height="@dimen/m1"
        android:layout_marginLeft="@dimen/m20"
        android:background="@drawable/bg_item_broadcast_content_head_divider"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/head_title"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>