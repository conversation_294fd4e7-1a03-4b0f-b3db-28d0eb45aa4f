package com.kaolafm.kradio.flavor.view;

import androidx.annotation.NonNull;
import android.text.TextPaint;
import android.text.style.ClickableSpan;
import android.view.View;

import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.base.AppDelegate;

public class ClickSpan extends ClickableSpan {
    private final View.OnClickListener mListener;
    private int mTextColor;
    public ClickSpan(View.OnClickListener mListener) {
        this.mListener = mListener;
    }

    public ClickSpan(View.OnClickListener mListener, int mTextColor) {
        this.mListener = mListener;
        this.mTextColor = mTextColor;
    }

    @Override
    public void onClick(@NonNull View widget) {
        mListener.onClick(widget);
    }

    @Override
    public void updateDrawState(@NonNull TextPaint ds) {
        if (mTextColor == 0){
            ds.setColor(AppDelegate.getInstance().getContext().getResources().getColor(R.color.colorWhite));
        }else {
            ds.setColor(mTextColor);
        }
        ds.setUnderlineText(false);
    }
}
