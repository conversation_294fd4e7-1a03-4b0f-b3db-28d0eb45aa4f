package com.kaolafm.kradio.home.utils;

import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.view.View;

import com.google.gson.Gson;
import com.kaolafm.base.utils.SpUtil;
import com.kaolafm.kradio.lib.bean.CrashMessageBean;
import com.kaolafm.kradio.common.utils.OnlineConstants;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.api.CrashMessageBaseBean;
import com.kaolafm.opensdk.api.personalise.model.HotRecommend;

public class AppDateUtils {
    private static AppDateUtils appDateUtils;

    public static AppDateUtils getInstance() {
        if (appDateUtils == null) {
            synchronized (AppDateUtils.class) {
                if (appDateUtils == null) {
                    appDateUtils = new AppDateUtils();
                }
            }
        }
        return appDateUtils;
    }

    /**
     * 把数据库数据装换成bean
     *
     * @param crashMessageBean
     * @return
     */
    public CrashMessageBaseBean changeDate(CrashMessageBean crashMessageBean) {
        CrashMessageBaseBean baseBean = null;
        String json = new Gson().toJson(crashMessageBean);
        baseBean = new Gson().fromJson(json, CrashMessageBaseBean.class);
        return baseBean;

    }

    /**
     * 通过设置开关确认是否可以播放泡泡消息
     *
     * @param bean
     * @return
     */
    public boolean isConfirmPlayMsg(CrashMessageBean bean) {
        boolean b = false;
        boolean voiceSpeakState = SpUtil.getInt(OnlineConstants.VOICE_SPEAK, 0) > 1;
        boolean voiceAssistantState = SpUtil.getInt(OnlineConstants.VOICE_ASSISTANT, 0) > 1;
        boolean travelServiceState = SpUtil.getInt(OnlineConstants.TRAVEL_SERVICE, 0) > 1;
        //消息等级 1-情感化问候、车载服务类消息 2-节目预约、社群等主动交互类消息 3-云听应急广播消息
//        switch (bean.getMsgLevel()) {
//            case "1"://出行
//                //出行按单独开关为准
//                b = travelServiceState;
//                break;
//            case "2"://小助手
//                b = voiceAssistantState;
//                break;
//            default:
//                b = voiceSpeakState;
//                break;
//        }
        return voiceSpeakState;
    }

    /**
     * 收听时长格式化
     * 不足0.5小时按0.5小时 超过0.5小时不足1小时按1小时
     *
     * @return
     */
    public String userDurationFormat(long duration) {
        if (duration == 0) {
            return "0";
        }
        String time = "0";
        double cumPlaytime = (duration / 1000d / 60d) / 60;
        int o = ((int) cumPlaytime);//整数部分
        double p = cumPlaytime - o;//小数部分
        if (p <= 0.5) {
            time = (o + 0.5) + "";
        } else {
            time = (o + 1) + "";
        }
        return time;
    }

    /**
     * 生成无内容文案
     *
     * @param hotRecommend
     * @return
     */
    public SpannableString getTextViewSpannable(HotRecommend hotRecommend) {
        String str = ResUtil.getString(R.string.comprehensive_hot_recommend);

        SpannableString spannableString = new SpannableString(String.format(str,
                hotRecommend.getName()));

        spannableString.setSpan(new ForegroundColorSpan(ResUtil.getColor(R.color.comprehensive_search_hot_recommend_color)),
                4, spannableString.length(), SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ClickableSpan() {
            @Override
            public void onClick(View widget) {
                if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
                    return;
                }
                PlayerManagerHelper.getInstance().start(hotRecommend.getContentId() + "", hotRecommend.getType());

                // TODO: 2019-11-01 是否有问题
                //PageJumper.getInstance().back();
//                ((SupportFragment) (context.getParentFragment().getParentFragment())).pop();
            }

            @Override
            public void updateDrawState(TextPaint ds) {
                //不显示下划线
                ds.setUnderlineText(false);
            }
        }, spannableString.length() - 4, spannableString.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        return spannableString;

    }

    // 为了适配所见即可说，把推荐和热词拆成了两个TextView
    public SpannableString getTextViewSpannable2(HotRecommend hotRecommend) {
        SpannableString spannableString = new SpannableString(hotRecommend.getName());

        spannableString.setSpan(new ForegroundColorSpan(ResUtil.getColor(R.color.comprehensive_search_hot_recommend_color)),
                0, spannableString.length(), SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ClickableSpan() {
            @Override
            public void onClick(View widget) {
                if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
                    return;
                }
                PlayerManagerHelper.getInstance().start(hotRecommend.getContentId() + "", hotRecommend.getType());

                // TODO: 2019-11-01 是否有问题
                //PageJumper.getInstance().back();
//                ((SupportFragment) (context.getParentFragment().getParentFragment())).pop();
            }

            @Override
            public void updateDrawState(TextPaint ds) {
                //不显示下划线
                ds.setUnderlineText(false);
            }
        }, 0, spannableString.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        return spannableString;
    }

    /**
     * 生成渐变文字
     * @param context
     * @param string
     * @return
     */
//    public SpannableStringBuilder getRadiusGradientSpan(Context context,String string) {
//        SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder(string);
//        LinearGradientFontSpan span = new LinearGradientFontSpan(ResUtil.getColor(R.color.online_tab_text_start)
//                , ResUtil.getColor(R.color.online_tab_text_end));
//        spannableStringBuilder.setSpan(span, 0, spannableStringBuilder.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
//        return spannableStringBuilder;
//
//    }


}
