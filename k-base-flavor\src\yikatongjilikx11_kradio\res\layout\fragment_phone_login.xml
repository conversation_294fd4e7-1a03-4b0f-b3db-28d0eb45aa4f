<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:id="@+id/login_fragment_layout"
    android:fitsSystemWindows="true"
    android:layout_height="match_parent">

    <EditText
        android:id="@+id/et_input_num"
        android:layout_width="@dimen/x375"
        android:layout_height="@dimen/y50"
        android:background="@null"
        android:hint="@string/user_input_phone_num"
        android:textSize="@dimen/text_size_title4"
        android:inputType="number"
        android:maxLength="11"
        android:maxLines="1"
        android:textColor="@color/text_color_1"
        android:textColorHint="@color/text_color_2"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <View
        android:id="@+id/num_line"
        android:layout_width="0dp"
        android:layout_height="@dimen/y1"
        android:background="@color/user_login_line_color"
        app:layout_constraintLeft_toLeftOf="@id/et_input_num"
        app:layout_constraintRight_toRightOf="@id/et_input_num"
        app:layout_constraintTop_toBottomOf="@id/et_input_num"/>

    <EditText
        android:id="@+id/et_input_code"
        android:layout_width="@dimen/x208"
        android:layout_height="@dimen/y50"
        android:background="@null"
        android:hint="@string/user_input_verification_code"
        android:textSize="@dimen/text_size_title4"
        android:textColor="@color/text_color_1"
        android:textColorHint="@color/text_color_2"
        android:inputType="number"
        android:maxLength="6"
        android:maxLines="1"
        android:layout_marginTop="@dimen/y24"
        app:layout_constraintLeft_toLeftOf="@id/num_line"
        app:layout_constraintTop_toBottomOf="@id/num_line"/>

    <View
        android:id="@+id/code_line"
        android:layout_width="0dp"
        android:layout_height="@dimen/y1"
        android:background="@color/user_login_line_color"
        app:layout_constraintLeft_toLeftOf="@id/et_input_code"
        app:layout_constraintRight_toRightOf="@id/et_input_code"
        app:layout_constraintTop_toBottomOf="@id/et_input_code"/>

    <Button
        android:id="@+id/btn_get_code"
        android:layout_width="0dp"
        android:layout_height="@dimen/y50"
        android:text="@string/user_get_verification_code"
        android:textSize="@dimen/text_size_title7"
        android:background="@drawable/selector_code_btn_bg"
        android:textAllCaps="false"
        android:layout_marginLeft="@dimen/x12"
        app:layout_constraintLeft_toRightOf="@id/et_input_code"
        app:layout_constraintRight_toRightOf="@id/et_input_num"
        app:layout_constraintBottom_toBottomOf="@id/et_input_code"/>

    <Button
        android:id="@+id/btn_login"
        android:layout_width="@dimen/x375"
        android:layout_height="@dimen/y60"
        android:text="@string/user_login"
        android:textSize="@dimen/text_size_title4"
        android:background="@drawable/bg_login_btn"
        android:layout_marginLeft="@dimen/x12"
        android:layout_marginTop="@dimen/y46"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_no_login_ps"/>
    
    <TextView
        android:id="@+id/tv_no_login_ps"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/user_notice_string"
        android:textSize="@dimen/text_size_title6"
        android:textColor="@color/text_color_2"
        android:layout_marginTop="@dimen/y20"
        app:layout_constraintLeft_toLeftOf="@id/et_input_code"
        app:layout_constraintTop_toBottomOf="@id/btn_get_code"/>

    <CheckBox
        android:id="@+id/cb_account_interwork"
        android:layout_marginTop="@dimen/y10"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/m40"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="@id/et_input_code"
        app:layout_constraintTop_toBottomOf="@id/btn_get_code"/>
</androidx.constraintlayout.widget.ConstraintLayout>