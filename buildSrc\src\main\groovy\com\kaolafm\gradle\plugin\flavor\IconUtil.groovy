package com.kaolafm.gradle.plugin.flavor

import org.gradle.api.Project

import javax.imageio.ImageIO
import javax.swing.*
import java.awt.*
import java.awt.image.BufferedImage

/**
 *
 * <AUTHOR>
 * @date 2019-09-23
 */
class IconUtil {

    static createWatermark(Project project, String channel, String flavorName) {
        def path = project.parent.projectDir.path + "/k-base-lib/src/main/res"
        def flavorPath = project.projectDir.path + "/src/" + channel + "/res/"

        new Thread(new Runnable() {

            @Override
            void run() {
                def icons = findIcon(path, flavorPath, flavorName)
            }
        }).start()
    }

    static findIcon(String path, String flavorPath, String channel) {
        def file = new File(path)
        def iconFiles = new ArrayList<File>()
        file.listFiles(new FileFilter() {

            @Override
            boolean accept(File resFile) {
                return resFile.isDirectory() && resFile.name.startsWith("mipmap")
            }
        }).each {
            it.listFiles(new FileFilter() {

                @Override
                boolean accept(File mipmapFile) {
                    return mipmapFile.name == "ic_launcher.png"
                }
            }) each {
                iconFiles.add(it)
                addWatermark(it.path, flavorPath + File.separator + it.getParentFile().name + "/ic_launcher.png", channel, "png")
            }

        }
        return iconFiles
    }

    /**
     * @description
     * @param sourceImgPath 源图片路径
     * @param tarImgPath 保存的图片路径
     * @param waterMarkContent 水印内容
     * @param fileExt 图片格式
     * @return void
     */
    static addWatermark(String sourceImgPath, String tarImgPath, String waterMarkContent, String fileExt) {
        Font font = new Font("宋体", Font.BOLD, 16)//水印字体，大小
        Color markContentColor = Color.BLUE//水印颜色
        Integer degree = -45//设置水印文字的旋转角度
        float alpha = 1.0f//设置水印透明度 默认为1.0  值越小颜色越浅
        OutputStream outImgStream = null
        try {
            File srcImgFile = new File(sourceImgPath)//得到文件
            Image srcImg = ImageIO.read(srcImgFile)//文件转化为图片
            int srcImgWidth = srcImg.getWidth(null)//获取图片的宽
            int srcImgHeight = srcImg.getHeight(null)//获取图片的高
            // 加水印
            BufferedImage bufImg = new BufferedImage(srcImgWidth, srcImgHeight, BufferedImage.TYPE_INT_RGB)
            Graphics2D g = bufImg.createGraphics()//得到画笔
            g.drawImage(srcImg, 0, 0, srcImgWidth, srcImgHeight, null)
            g.setColor(markContentColor)//设置水印颜色
            g.setFont(font)             //设置字体
            g.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_ATOP, alpha))//设置水印文字透明度
            if (null != degree) {
                g.rotate(Math.toRadians(degree), (double) bufImg.getWidth(), (double) bufImg.getHeight())//设置水印旋转
            }
            JLabel label = new JLabel(waterMarkContent)
            FontMetrics metrics = label.getFontMetrics(font)
            int width = metrics.stringWidth(label.getText())//文字水印的宽
            int rowsNumber = srcImgHeight / width + srcImgHeight % width// 图片的高  除以  文字水印的宽  打印的行数(以文字水印的宽为间隔)
            int columnsNumber = srcImgWidth / width + srcImgWidth % width//图片的宽 除以 文字水印的宽  每行打印的列数(以文字水印的宽为间隔)
            //防止图片太小而文字水印太长，所以至少打印一次
            if (rowsNumber < 1) {
                rowsNumber = 1
            }
            if (columnsNumber < 1) {
                columnsNumber = 1
            }
            for (int j = 0; j < rowsNumber; j++) {
                for (int i = 0; i < columnsNumber; i++) {
                    g.drawString(waterMarkContent, i * width + j * width, -i * width + j * width)//画出水印,并设置水印位置
                }
            }
            g.dispose()// 释放资源
            // 输出图片
            def file = new File(tarImgPath)
            def parentFile = file.getParentFile()
            if (!parentFile.exists() || !parentFile.isDirectory()) {
                parentFile.mkdirs()
            }
            if (!file.exists()) {
                file.createNewFile()
            }
            outImgStream = new FileOutputStream(file)
            ImageIO.write(bufImg, fileExt, outImgStream)
        } catch (Exception e) {
            e.printStackTrace()
            e.getMessage()
        } finally {
            try {
                if (outImgStream != null) {
                    outImgStream.flush()
                    outImgStream.close()
                }
            } catch (Exception e) {
                e.printStackTrace()
                e.getMessage()
            }
        }
    }
}