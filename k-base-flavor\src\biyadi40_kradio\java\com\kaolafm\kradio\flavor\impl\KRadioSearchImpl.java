package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.text.InputFilter;
import android.text.Spanned;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.TextView;

import com.kaolafm.kradio.lib.base.flavor.KRadioSearchInter;

/**
 * @Package: com.kaolafm.kradio.flavor.impl
 * @Description:
 * @Author: Maclay
 * @Date: 10:12
 */
public class KRadioSearchImpl implements KRadioSearchInter {
    @Override
    public void configSearchEditText(EditText editText) {
        Log.d("KRadioSearchImpl", "configSearchEditText");
    }

    @Override
    public void configSearchHisItem(TextView textView) {
        Log.d("KRadioSearchImpl", "configSearchHisItem");
        if (textView != null) {
            textView.setMaxEms(10);
        }
    }

    @Override
    public void configInputSoft(EditText view) {
        if (view != null) {
            view.post(() -> {
                view.setFocusable(true);
                view.setFocusableInTouchMode(true);
                view.requestFocus();
                view.setCursorVisible(true);
                InputMethodManager imm = (InputMethodManager) view.getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
                imm.showSoftInput(view, 0);
            });
        }
    }

//    private class LengthFilter implements InputFilter {
//        private final int maxLength;
//
//        public LengthFilter(int max) {
//            maxLength = max;
//        }
//
//        public CharSequence filter(CharSequence source, int start, int end, Spanned dest,
//                                   int dstart, int dend) {
//            //限定字符数量
//            int dindex = 0;
//            int count = 0;
//            //计算文本框中已经存在的字符长度
//            while (count <= maxLength && dindex < dest.length()) {
//                char c = dest.charAt(dindex++);
//                //这里是根据ACSII值进行判定的中英文，其中中文及中文符号的ACSII值都是大于128的
//                if ((int) c <= 128) {
//                    count += 1;
//                } else {
//                    count += 2;
//                }
//            }
//
//            if (count > maxLength) {
//                return dest.subSequence(0, dindex - 1);
//            }
//            //计算输入的字符长度
//            int sindex = 0;
//            while (count <= maxLength && sindex < source.length()) {
//                char c = source.charAt(sindex++);
//                if ((int) c <= 128) {
//                    count += 1;
//                } else {
//                    count += 2;
//                }
//            }
//            if (count > maxLength) {
//                sindex--;
//            }
//            return source.subSequence(0, sindex);
//        }
//    }
}
