package com.kaolafm.kradio.home.comprehensive.test;

import android.app.Activity;
import android.content.Context;
import android.view.View;
import android.widget.Toast;

import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.notify.Nolley;
import com.kaolafm.notify.NotifyCallback;
import com.kaolafm.notify.NotifyParam;
import com.kaolafm.notify.NotifyWindow;

public class TestUtils {

    //显示直播提示
    static public void testLiveNotify(Context ctx) {
        //测试模拟直播入流推送通知;
        Activity mainActivity = AppManager.getInstance().getMainActivity();
        String voiceUrl = "http://image.kaolafm.net/mz/mp3_32/201906/489ad986-33ba-49af-a8de-5e30d5bf9c6d.mp3";
        NotifyParam param = new NotifyParam.Builder(Constants.NOTIFY_TYPE_LIVE)
                .activity(mainActivity)
                .icon("https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1575017844779&di=b8a9cc7505834e3900f005e4cb15126c&imgtype=0&src=http%3A%2F%2Fimg.zcool.cn%2Fcommunity%2F01e4e2554556880000019ae905646c.jpg%401280w_1l_2o_100sh.jpg")
                .msg("测试")
                .callback(new NotifyCallback() {

                    @Override
                    public void onSure(NotifyWindow notifyWindow) {
                        notifyWindow.hide();
                        Toast.makeText(ctx, "[NotifyCallback]Sure", Toast.LENGTH_SHORT).show();
                    }

                    @Override
                    public void onCancel(NotifyWindow notifyWindow) {

                    }

                    @Override
                    public void onShow(NotifyWindow notifyWindow) {

                    }

                    @Override
                    public void onHide(NotifyWindow notifyWindow) {

                    }
                })

                .build();
        Nolley.getInstance().addNotify(param);
    }
}
