<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/car_owner_radio_card"
    android:layout_width="@dimen/m572"
    android:layout_height="@dimen/m614">

    <ImageView
        android:id="@+id/radio_road"
        android:layout_width="@dimen/m521"
        android:layout_height="@dimen/m244"
        android:src="@drawable/car_owner_radio_road"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />


    <ImageView
        android:id="@+id/radio_bg"
        android:layout_width="@dimen/m572"
        android:layout_height="@dimen/m572"
        android:layout_marginBottom="@dimen/m54"
        android:src="@drawable/car_owner_radio_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include layout="@layout/car_owner_radio_type_radio" />

    <include layout="@layout/car_owner_radio_type_common" />

    <include layout="@layout/car_owner_radio_type_live" />

    <ImageView
        android:id="@+id/playBtn"
        android:layout_width="@dimen/m72"
        android:layout_height="@dimen/m72"
        android:layout_marginTop="@dimen/m250"
        android:alpha="0"
        android:src="@drawable/car_owner_radio_pause"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:alpha="1" />

</androidx.constraintlayout.widget.ConstraintLayout>