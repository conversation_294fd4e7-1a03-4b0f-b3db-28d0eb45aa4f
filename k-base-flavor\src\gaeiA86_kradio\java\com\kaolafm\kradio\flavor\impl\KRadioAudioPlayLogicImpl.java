package com.kaolafm.kradio.flavor.impl;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.Log;

import com.kaolafm.kradio.flavor.common.SystemBootUtil;
import com.kaolafm.kradio.flavor.utils.PlayerUtil;

import com.kaolafm.kradio.lib.base.flavor.KRadioAudioPlayLogicInter;
import com.kaolafm.kradio.lib.utils.IntentUtils;


import com.kaolafm.opensdk.player.core.listener.OnPlayLogicListener;
import com.kaolafm.opensdk.player.core.utils.PlayerCustomizeManager;


/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-12-17 20:36
 ******************************************/
public final class KRadioAudioPlayLogicImpl implements KRadioAudioPlayLogicInter {
    private final static String TAG = "KRadioAudioPlayLogicImpl";

    public KRadioAudioPlayLogicImpl() {
        PlayerCustomizeManager.getInstance().setPlayLogicListener(new OnPlayLogicListener() {
            @Override
            public boolean onPlayLogicDispose() {
                int currentFocus = PlayerManager.getInstance().getCurrentAudioFocusStatus();
                Log.i(TAG, "isAppOnForeground--------->currentFocus = " + currentFocus);
                if ((currentFocus < 0)) {
                    boolean isAppOnForeground = IntentUtils.getInstance().isAppOnForeground();
                    Log.i(TAG, "isAppOnForeground--------->isAppOnForeground = " + isAppOnForeground);
                    return !isAppOnForeground;
                }
                return false;
            }
        });
    }

    @SuppressLint("LongLogTag")
    @Override
    public boolean autoPlayAudio(Object... args) {
        Context context = (Context) args[0];
        SystemBootUtil systemBootUtil = new SystemBootUtil();
        boolean flag = systemBootUtil.isFirstBoot(context);
        Log.i(TAG, "autoPlayAudio:   flag = " + flag);
        if (flag) {
            PlayerUtil.playDefaultMediaForChannel();
            systemBootUtil.updateFirstBoot(context, false);
            return true;
        }
        return false;
    }

    @Override
    public boolean requestAudioFocus(Object... args) {
        boolean flag = PlayerManager.getInstance().requestAudioFocus();
        return flag;
    }

    @Override
    public boolean resumeAudioPlayLogic(Object... args) {
        recoverPlay();
        return true;
    }

    @Override
    public boolean restartAudioPlayLogic(Object... args) {
        return false;
    }

    @Override
    public boolean doStartInPlay(Object... args) {
        return false;
    }

    private void recoverPlay() {
        PlayerManager playerManager = PlayerManager.getInstance();

        Log.i(TAG, "recoverPlay---------->PlayerManager.isPausedFromUser() = " + playerManager.isPauseFromUser()
                + "          PlayerManager.getCurrentAudioFocusStatus() = " + playerManager.getCurrentAudioFocusStatus());
        if (playerManager.isPauseFromUser()) {
            if (playerManager.getCurrentAudioFocusStatus() < 0) {
                requestAudioFocus();
            }
            return;
        }
        if (playerManager.getCurrentAudioFocusStatus() >0) {
            return;
        }
        requestAudioFocus();
        if (!playerManager.isPlaying()) {
            PlayerManagerHelper.getInstance().switchPlayerStatus(false);
        }
    }
}