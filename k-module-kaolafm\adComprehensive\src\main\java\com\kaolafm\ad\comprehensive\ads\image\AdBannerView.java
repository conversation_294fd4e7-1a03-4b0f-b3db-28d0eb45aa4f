package com.kaolafm.ad.comprehensive.ads.image;

import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.drawable.BitmapDrawable;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.ImageView;

import com.kaolafm.ad.comprehensive.ads.image.base.BaseAdContentView;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.AnimUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.util.ReportConstants;
import com.kaolafm.report.util.ReportParameterManager;

import java.io.File;

public class AdBannerView extends BaseAdContentView<AdContentInfo> {

    private OnViewHideListener mHideListener;

    private ImageView mIvAdImage;

//    private ConstraintLayout mClAdBanner;

    private ImageView mIvAdCollapse;

    private String mUrl;
    private String mLocalPath;
    private int mWidth;
    private int mHeight;

    private AdSize mCurrAdSize;
    private AdContentInfo adContentInfo;

    public AdBannerView(Context context, String url, String localPath, int width, int height) {
        super(context);
        mUrl = url;
        mLocalPath = localPath;
        mWidth = width;
        mHeight = height;
        init(context);
    }

    public AdBannerView(Context context) {
        super(context);
        init(context);
    }

    public AdBannerView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public AdBannerView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        LayoutInflater.from(context).inflate(R.layout.ad_interact_image_view_layout, this, true);
        mIvAdImage = findViewById(R.id.iv_ad_banner);
//        AdSize adSize = SizeUtil.getAdSize(getContext(), mWidth, mHeight);
//        mCurrAdSize =new AdSize();
//        mCurrAdSize.height
//        setImageSize();
        mIvAdCollapse = findViewById(R.id.iv_ad_collapse);
        Bitmap bitmap = BitmapFactory.decodeResource(getResources(), R.drawable.collapse);
        BitmapDrawable drawable = new BitmapDrawable(getResources(), bitmap);
        drawable.setAntiAlias(true);
        mIvAdCollapse.setImageDrawable(drawable);
        mIvAdCollapse.setOnClickListener((v) -> {
            hide();
            ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_AD_PIC_CLOSE, "", ReportParameterManager.getInstance().getPage()
                    , ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_ADVERT, null, null, null, adContentInfo == null ? "" : adContentInfo.getId()));
        });
//        mClAdBanner = findViewById(R.id.cl_ad_banner);
    }

//    private void invalidateImageView(){
//        AdSize adSize = SizeUtil.getAdSize(getContext(), mWidth, mHeight);
//        if(!mCurrAdSize.equals(adSize)){
//            mCurrAdSize = adSize;
//            setImageSize();
//            mIvAdImage.requestLayout();
//        }
//    }

    private void setImageSize() {
        LayoutParams layoutParams = (LayoutParams) mIvAdImage.getLayoutParams();
        layoutParams.width = mCurrAdSize.width;
        layoutParams.height = mCurrAdSize.height;
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        mIvAdImage.setScaleType(ImageView.ScaleType.CENTER_CROP);

        if (!TextUtils.isEmpty(mLocalPath)) {
            File file = new File(mLocalPath);
            if (file.exists()) {
                displayLocalImage(mIvAdImage, mLocalPath, ResUtil.getDimen(R.dimen.m20));
                return;
            }
        }
        displayImage(mIvAdImage, mUrl, ResUtil.getDimen(R.dimen.m20), null);
    }

    @Override
    public void loadAdContent(AdContentInfo adContentInfo) {
        this.adContentInfo = adContentInfo;

        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_AD_PIC_CLOSE, "", ReportParameterManager.getInstance().getPage()
                , ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_ADVERT, null, null, null, adContentInfo.getId()));
    }

    @Override
    public void show() {
//        invalidateImageView();
        AnimUtil.startTranslateY(this, -mIvAdImage.getLayoutParams().height, 0);
        super.show();
    }

    @Override
    public void hide() {
        super.hide();
        notifyHide();
    }

    @Override
    protected void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (getVisibility() == VISIBLE) {
            hide();
        }
    }

    private void notifyHide() {
        if (mHideListener != null) {
            mHideListener.onHide();
        }
    }

    public void setOnViewHideListener(OnViewHideListener listener) {
        mHideListener = listener;
    }

    /**
     * view隐藏监听
     */
    public interface OnViewHideListener {

        /**
         * view隐藏的时候回调
         */
        void onHide();
    }
}
