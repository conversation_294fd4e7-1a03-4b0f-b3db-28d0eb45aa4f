<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:state_activated="true" android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#80000000" />
            <corners android:bottomLeftRadius="@dimen/big_corner_size" android:bottomRightRadius="@dimen/big_corner_size" android:radius="@dimen/big_corner_size" android:topLeftRadius="@dimen/big_corner_size" android:topRightRadius="@dimen/big_corner_size" />
        </shape>
    </item>

    <item android:state_activated="true" android:state_pressed="false">
        <shape android:shape="rectangle">
            <stroke android:width="@dimen/m2" android:color="@color/radio_fragment_subscribe_activate" />
            <corners android:bottomLeftRadius="@dimen/big_corner_size" android:bottomRightRadius="@dimen/big_corner_size" android:radius="@dimen/big_corner_size" android:topLeftRadius="@dimen/big_corner_size" android:topRightRadius="@dimen/big_corner_size" />
        </shape>
    </item>

    <item android:state_activated="false" android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#00ACEB" />
            <corners android:bottomLeftRadius="@dimen/big_corner_size" android:bottomRightRadius="@dimen/big_corner_size" android:radius="@dimen/big_corner_size" android:topLeftRadius="@dimen/big_corner_size" android:topRightRadius="@dimen/big_corner_size" />
        </shape>
    </item>

    <item android:state_activated="false" android:state_pressed="false">
        <shape android:shape="rectangle">
            <solid android:color="#00ACEB" />
            <corners android:bottomLeftRadius="@dimen/big_corner_size" android:bottomRightRadius="@dimen/big_corner_size" android:radius="@dimen/big_corner_size" android:topLeftRadius="@dimen/big_corner_size" android:topRightRadius="@dimen/big_corner_size" />
        </shape>
    </item>


</selector>
