package com.kaolafm.kradio.online.mine.login;

import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import com.kaolafm.kradio.common.widget.OvalImageView;
import com.kaolafm.kradio.k_kaolafm.R; 
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.user.ui.IUserLoginView;
import com.kaolafm.kradio.user.ui.UserLoginPresenter;
import com.kaolafm.opensdk.api.login.model.UserInfo;
 

/**
 * 扫码登录确认页面
 * 蔡佳彬
 */
public class OnlineUserConfirmFragment extends BaseFragment<UserLoginPresenter> implements IUserLoginView {
 
    OvalImageView scanned_user_avatar; 
    TextView scanned_user_name; 
    TextView scanned_btn_back;

    private String avatar = "", name = "";
    static OnlineUserConfirmFragment fragment;

    public OnlineUserConfirmFragment() {
        // Required empty public constructor
    }

    public static OnlineUserConfirmFragment newInstance(String avatar, String name) {
        if (fragment == null)
            fragment = new OnlineUserConfirmFragment();
        Bundle bundle = new Bundle();
        bundle.putString("avatar", avatar);
        bundle.putString("name", name);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        avatar = getArguments().getString("avatar");
        name = getArguments().getString("name");
    }


    @Override
    protected int getLayoutId() {
        return R.layout.online_user_confirm_login;
    }

    @Override
    protected UserLoginPresenter createPresenter() {
        return new UserLoginPresenter(this);
    }

    @Override
    public void initView(View view) {
        scanned_user_avatar=view.findViewById(R.id.scanned_user_avatar);
        scanned_user_name=view.findViewById(R.id.scanned_user_name);
        scanned_btn_back=view.findViewById(R.id.scanned_btn_back);
    
        
        scanned_user_name.setText(name);
        ImageLoader.getInstance().displayImage(getContext(), avatar, scanned_user_avatar);
        scanned_btn_back.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (getActivity() instanceof OnlineLoginActivity) {
                    ((OnlineLoginActivity) getActivity()).hideBindSuccess();
                }
            }
        });
    }

    @Override
    public void loginSuccess(UserInfo userInfo) {

    }

    @Override
    public void loginFailed() {

    }

    @Override
    public void logoutSuccess() {

    }

    @Override
    public void logoutFailed() {

    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        fragment = null;
    }
}