package com.kaolafm.kradio.online.common.view;

import android.content.Context;
import android.graphics.Paint;
import android.graphics.Rect;
import android.text.TextPaint;
import android.util.AttributeSet;
import android.util.TypedValue;

import com.kaolafm.kradio.common.widget.KradioTextView;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.ResUtil;

public class AutoSizeTextView extends KradioTextView {
    private CharSequence text;
    private Rect mTextBound;
    private int width;

    public AutoSizeTextView(Context context) {
        this(context, null);
    }

    public AutoSizeTextView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public AutoSizeTextView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mTextBound = new Rect();
        text = getText();
        resetTextSize();
    }

    @Override
    public void setText(CharSequence text, BufferType type) {
        super.setText(text, type);
        resetTextSize();
    }

    @Override
    public void setTextSize(float size) {
        resetTextSize();
    }

//    @Override
//    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
//        super.onSizeChanged(w, h, oldw, oldh);
//        if (w == oldw) return;
//        resetTextSize();
//    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        if (getMeasuredWidth() != width) {
            width = getMeasuredWidth();
            resetTextSize();
        }
    }

    private void resetTextSize() {
        TextPaint paint = new TextPaint(Paint.ANTI_ALIAS_FLAG);
        if (paint == null || text == null || getMeasuredWidth() == 0) return;
        paint.setTextSize(ResUtil.getDimen(R.dimen.m56));
        paint.getTextBounds(text, 0, text.length(), mTextBound);
        if (mTextBound.right - mTextBound.left + getPaddingStart() + getPaddingEnd() > getMeasuredWidth()) {
            setTextSize(TypedValue.COMPLEX_UNIT_PX, ResUtil.getDimen(R.dimen.m36));
        } else {
            setTextSize(TypedValue.COMPLEX_UNIT_PX, ResUtil.getDimen(R.dimen.m56));
        }
    }
}
