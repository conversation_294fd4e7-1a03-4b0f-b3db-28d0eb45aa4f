<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/activity_root_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent"
    android:layout_marginBottom="@dimen/online_fragment_categories_bottom"
    android:paddingTop="@dimen/home_page_root_view_padding_top">


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_sub"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/y48"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        />

    <TextView
        android:visibility="gone"
        android:id="@+id/tv_more_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:text="@string/activity_more_title"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        />

    <ImageView
        android:id="@+id/qrCode_image_3"
        android:layout_width="@dimen/m160"
        android:layout_height="@dimen/m160"
        android:layout_marginTop="@dimen/y30"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_more_title"
        tools:ignore="MissingConstraints" />

    <TextView
        android:id="@+id/qrCode_textView_3"
        android:layout_width="@dimen/x200"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y10"
        android:maxLines="2"
        app:layout_constraintLeft_toLeftOf="@+id/qrCode_image_3"
        app:layout_constraintRight_toRightOf="@+id/qrCode_image_3"
        app:layout_constraintTop_toBottomOf="@+id/qrCode_image_3"
        tools:ignore="MissingConstraints" />

    <ViewStub
        android:id="@+id/vs_layout_error_page"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout="@layout/online_error_layout" />

    <TextView
        android:id="@+id/tv_no_activity"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/user_purchased_no_sub_text_color"
        android:textSize="@dimen/text_size5"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ViewStub
        android:id="@+id/activity_loading"
        android:layout="@layout/online_refresh_center"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>