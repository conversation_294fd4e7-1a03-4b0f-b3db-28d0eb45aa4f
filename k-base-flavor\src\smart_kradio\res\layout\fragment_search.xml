<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/search_main_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_home"
    android:paddingTop="@dimen/search_page_padding_top"
    android:clickable="false">

    <androidx.constraintlayout.widget.Group
        android:id="@+id/search_history_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:constraint_referenced_ids="tv_search_history,rv_search_history_tags,tv_hot_search_words,rv_hot_search_words" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/search_top_guideline"
        style="@style/secondary_page_top_guideline" />

    <ImageView
        android:id="@+id/iv_search_back"
        style="@style/FragmentBackButton"
        app:layout_constraintBottom_toTopOf="@id/search_top_guideline"
        tools:ignore="MissingConstraints" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_search_view"
        android:layout_width="@dimen/search_edit_width"
        android:layout_height="@dimen/search_edit_height"
        android:layout_marginLeft="@dimen/x52"
        android:background="@drawable/bg_search_edit_text"
        app:layout_constraintBottom_toTopOf="@id/search_top_guideline"
        app:layout_constraintLeft_toRightOf="@id/iv_search_back"
        app:layout_constraintTop_toTopOf="parent">

        <com.kaolafm.kradio.search.TypeSpinner
            android:id="@+id/ts_search_type"
            android:layout_width="@dimen/x168"
            android:layout_height="0dp"
            android:gravity="center"
            android:paddingRight="@dimen/x31"
            android:paddingLeft="@dimen/x31"
            android:layout_margin="@dimen/m1"
            android:text="综合"
            android:textColor="@color/search_typespinner_text_color"
            android:textSize="@dimen/text_size_title4"
            android:background="@drawable/bg_type_spinner"
            android:includeFontPadding="false"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"/>

        <EditText
            android:id="@+id/et_search_word"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@null"
            android:hint="@string/search_hint"
            android:imeOptions="actionSearch|flagNoExtractUi"
            android:paddingRight="@dimen/x10"
            android:singleLine="true"
            android:textSize="@dimen/text_size_title4"
            android:textColor="@color/search_edit_text_color"
            android:textColorHint="@color/search_text_hint_color"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/ts_search_type"
            app:layout_constraintRight_toLeftOf="@id/iv_search_word_delete"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_search_word_delete"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/y10"
            android:layout_marginBottom="@dimen/y10"
            android:layout_marginRight="@dimen/x20"
            android:background="@drawable/ic_search_cancel"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="W,1:1"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_search"
        android:layout_width="@dimen/x130"
        android:layout_height="@dimen/y68"
        android:layout_marginLeft="@dimen/x34"
        android:background="@drawable/bg_search_btn"
        android:gravity="center"
        android:text="@string/search"
        android:textColor="@color/search_btn_text_color"
        android:textSize="@dimen/text_size_title4"
        app:layout_constraintBottom_toTopOf="@id/search_top_guideline"
        app:layout_constraintLeft_toRightOf="@id/cl_search_view"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_search_history"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/search_history"
        android:textColor="@color/search_history_text_color"
        android:textSize="@dimen/text_size_title4"
        app:layout_constraintLeft_toLeftOf="@id/cl_search_view"
        app:layout_constraintTop_toBottomOf="@id/search_top_guideline" />

    <TextView
        android:id="@+id/tv_clear_search_history"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawablePadding="@dimen/x4"
        android:text="@string/clear_search_history"
        android:textColor="@color/search_clear_history_text_color"
        android:textSize="@dimen/text_size_title4"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="@id/tv_search_history"
        app:layout_constraintRight_toRightOf="@id/tv_search" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_search_history_tags"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y25"
        android:overScrollMode="never"
        app:layout_constraintLeft_toLeftOf="@id/cl_search_view"
        app:layout_constraintRight_toRightOf="@id/tv_search"
        app:layout_constraintTop_toBottomOf="@id/tv_search_history" />

    <TextView
        android:id="@+id/tv_hot_search_words"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y50"
        android:text="@string/hot_search_words"
        android:textColor="@color/search_history_text_color"
        android:textSize="@dimen/text_size_title4"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/rv_search_history_tags"
        app:layout_constraintLeft_toLeftOf="@id/rv_search_history_tags"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_hot_search_words"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/y25"
        android:overScrollMode="never"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@id/rv_search_history_tags"
        app:layout_constraintRight_toRightOf="@id/rv_search_history_tags"
        app:layout_constraintTop_toBottomOf="@id/tv_hot_search_words" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_associate_list"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:overScrollMode="never"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@id/cl_search_view"
        app:layout_constraintRight_toRightOf="@id/tv_search"
        app:layout_constraintTop_toBottomOf="@id/search_top_guideline" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_search_results"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:overScrollMode="never"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/search_top_guideline"
        app:layout_constraintWidth_default="percent"
        app:layout_constraintWidth_percent="0.856" />

    <include
        android:id="@+id/search_loading"
        layout="@layout/refresh_bottom"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <ViewStub
        android:id="@+id/vs_search_exception"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout="@layout/search_result_exception_layout"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ViewStub
        android:id="@+id/vs_search_network_error"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout="@layout/home_no_network_rl" />

</androidx.constraintlayout.widget.ConstraintLayout>