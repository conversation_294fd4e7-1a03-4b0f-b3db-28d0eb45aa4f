package com.kaolafm.kradio.user.channel;

import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 * @date 2019/4/19
 */
public class Channel {

    @SerializedName("androidClienPackage")
    private String packageName;

    @SerializedName("androidClienSign")
    private String androidClientSign;

    @SerializedName("appDescription")
    private String appDescription;

    @SerializedName("appId")
    private String appId;

    @SerializedName("appLogo")
    private Object appLogo;

    @SerializedName("appName")
    private String appName;

    @SerializedName("appType")
    private int appType;

    @SerializedName("applicant")
    private Object applicant;

    @SerializedName("authorizeCallbackUrl")
    private String authorizeCallbackUrl;

    @SerializedName("companyName")
    private Object companyName;

    @SerializedName("cooperationMode")
    private int cooperationMode;

    @SerializedName("createTime")
    private long createTime;

    /**
     * id : 579
     * openId : 179
     * uid : 4326191
     * appName : K-radio（apk）横屏
     * appDescription : K-radio（apk）横屏
     * cooperationMode : 3
     * packageName : com.kaolafm.kradio.k_radio_horizontal
     * androidClientSign : 21F1E4C2CA6156223DDA87F2B38D80A9C6BFE7B4
     * authorizeCallbackUrl : http://api.kradio.top/user/kaolacallback
     * createTime : 1537867741000
     * updateTime : 1556276910000
     * appId : ye8192
     * appKey : f6dff42133bf06810a52a1d392b9906b
     * status : 1
     * applicant : null
     * companyName : null
     * appType : 1
     * secType : 40
     * appLogo : null
     * onclickAppLogo : null
     * productId : 1
     */

    @SerializedName("id")
    private int id;

    @SerializedName("onclickAppLogo")
    private Object onclickAppLogo;

    @SerializedName("openId")
    private int openId;

    @SerializedName("productId")
    private int productId;

    @SerializedName("secType")
    private int secType;

    @SerializedName("secretKey")
    private String appKey;

    @SerializedName("status")
    private int status;

    @SerializedName("uid")
    private String uid;

    @SerializedName("updateTime")
    private long updateTime;

    private String channel;

    public String getPackageName() {
        return packageName;
    }

    public String getAndroidClientSign() {
        return androidClientSign;
    }

    public String getAppDescription() {
        return appDescription;
    }

    public String getAppId() {
        return appId;
    }

    public Object getAppLogo() {
        return appLogo;
    }

    public String getAppName() {
        return appName;
    }

    public int getAppType() {
        return appType;
    }

    public Object getApplicant() {
        return applicant;
    }

    public String getAuthorizeCallbackUrl() {
        return authorizeCallbackUrl;
    }

    public Object getCompanyName() {
        return companyName;
    }

    public int getCooperationMode() {
        return cooperationMode;
    }

    public long getCreateTime() {
        return createTime;
    }

    public int getId() {
        return id;
    }

    public Object getOnclickAppLogo() {
        return onclickAppLogo;
    }

    public int getOpenId() {
        return openId;
    }

    public int getProductId() {
        return productId;
    }

    public int getSecType() {
        return secType;
    }

    public String getAppKey() {
        return appKey;
    }

    public int getStatus() {
        return status;
    }

    public String getUid() {
        return uid;
    }

    public long getUpdateTime() {
        return updateTime;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public void setAndroidClientSign(String androidClientSign) {
        this.androidClientSign = androidClientSign;
    }

    public void setAppDescription(String appDescription) {
        this.appDescription = appDescription;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public void setAppLogo(Object appLogo) {
        this.appLogo = appLogo;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public void setAppType(int appType) {
        this.appType = appType;
    }

    public void setApplicant(Object applicant) {
        this.applicant = applicant;
    }

    public void setAuthorizeCallbackUrl(String authorizeCallbackUrl) {
        this.authorizeCallbackUrl = authorizeCallbackUrl;
    }

    public void setCompanyName(Object companyName) {
        this.companyName = companyName;
    }

    public void setCooperationMode(int cooperationMode) {
        this.cooperationMode = cooperationMode;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public void setId(int id) {
        this.id = id;
    }

    public void setOnclickAppLogo(Object onclickAppLogo) {
        this.onclickAppLogo = onclickAppLogo;
    }

    public void setOpenId(int openId) {
        this.openId = openId;
    }

    public void setProductId(int productId) {
        this.productId = productId;
    }

    public void setSecType(int secType) {
        this.secType = secType;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public void setUpdateTime(long updateTime) {
        this.updateTime = updateTime;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    @Override
    public String toString() {
        return "Channel{" +
                "packageName='" + packageName + '\'' +
                ", androidClientSign='" + androidClientSign + '\'' +
                ", appDescription='" + appDescription + '\'' +
                ", appId='" + appId + '\'' +
                ", appLogo=" + appLogo +
                ", appName='" + appName + '\'' +
                ", appType=" + appType +
                ", applicant=" + applicant +
                ", authorizeCallbackUrl='" + authorizeCallbackUrl + '\'' +
                ", companyName=" + companyName +
                ", cooperationMode=" + cooperationMode +
                ", createTime=" + createTime +
                ", id=" + id +
                ", onclickAppLogo=" + onclickAppLogo +
                ", openId=" + openId +
                ", productId=" + productId +
                ", secType=" + secType +
                ", appKey='" + appKey + '\'' +
                ", status=" + status +
                ", uid='" + uid + '\'' +
                ", updateTime=" + updateTime +
                ", channel=" + channel +
                '}';
    }
}
