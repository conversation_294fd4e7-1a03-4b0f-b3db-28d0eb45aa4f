<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="match_parent">

    <com.kaolafm.kradio.common.widget.GridRecyclerView
        android:id="@+id/rv_home_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 隐藏控件，用于所见即可说语音执行滑动操作 -->
    <TextView
        android:visibility="gone"
        style="@style/ContentDescriptionScrollLeft"
        app:layout_constraintTop_toTopOf="@id/rv_home_content"
        app:layout_constraintLeft_toLeftOf="@id/rv_home_content"
        />

    <TextView
        android:visibility="gone"
        style="@style/ContentDescriptionScrollRight"
        app:layout_constraintTop_toTopOf="@id/rv_home_content"
        app:layout_constraintRight_toRightOf="@id/rv_home_content"
        tools:ignore="UnknownId" />
</androidx.constraintlayout.widget.ConstraintLayout>