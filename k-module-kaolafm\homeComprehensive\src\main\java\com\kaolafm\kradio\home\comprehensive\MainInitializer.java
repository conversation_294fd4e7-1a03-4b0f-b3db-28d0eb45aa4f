package com.kaolafm.kradio.home.comprehensive;

import android.app.Application;
import android.util.Log;

import com.kaolafm.flavor.AccountInterworkInter;
import com.kaolafm.kradio.common.http.vehicle.KlSdkVehicle;
import com.kaolafm.kradio.lib.report.ReportUtil;
import com.kaolafm.kradio.lib.utils.YTLogUtil;
import com.kaolafm.kradio.scene.launcher.InitService;
import com.kaolafm.kradio.lib.init.AppInit;
import com.kaolafm.kradio.lib.init.BaseAppInitializer;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.ReportManager;


/**
 * <AUTHOR>
 * @date 2019-11-12
 */
@AppInit(description = "首页初始化")
public class MainInitializer extends BaseAppInitializer {

    @Override
    public void onCreate(Application application) {
        YTLogUtil.logStart("MainInitializer", "onCreate", "start");
        ReportHelper.getInstance().setCarType(KlSdkVehicle.getInstance().getCarType());
        ReportUtil.report(ReportManager.getInstance());
        ReportHelper.getInstance().initByApk(); //使用apk自己上报v
        InitService.start(application);

        //  应用启动，账号打通相关初始化
        AccountInterworkInter mAccountInterworkInter = ClazzImplUtil.getInter("AccountInterworkImpl");
        if(mAccountInterworkInter != null && mAccountInterworkInter.isOpenThirdPartyAccount()){
            mAccountInterworkInter.init();
        }
        YTLogUtil.logStart("MainInitializer", "onCreate", "end");
    }
}
