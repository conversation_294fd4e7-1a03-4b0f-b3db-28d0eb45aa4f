package com.kaolafm.kradio.byd;

import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.IBinder;
import android.os.RemoteException;
import android.util.Log;

import com.kaolafm.kradio.byd.search.AlbumOperation;
import com.kaolafm.kradio.byd.search.NewsOperation;
import com.kaolafm.kradio.byd.search.Operation;
import com.kaolafm.kradio.byd.search.RadioOperation;
import com.kaolafm.kradio.clientControlerForKradio.ClientImpl;
import com.kaolafm.kradio.common.CP;
import com.kaolafm.kradio.common.SubscribeData;
import com.kaolafm.kradio.common.bean.HistoryItem;
import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.k_kaolafm.home.HomeDataManager;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.receiver.BydSpeechCtlReceiver;
import com.kaolafm.kradio.subscribe.LoadCallback;
import com.kaolafm.kradio.subscribe.SubscribeManager;
import com.kaolafm.kradio.uitl.SpeechUtil;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.sdk.client.ErrorInfo;
import com.kaolafm.sdk.client.ISubscribeResult;

import java.util.List;

import static com.kaolafm.kradio.byd.BydSpeechCtlActionConstant.AUTOVOICE_QUALITY;
import static com.kaolafm.kradio.byd.BydSpeechCtlActionConstant.RESULT_FAILED;
import static com.kaolafm.kradio.byd.BydSpeechCtlActionConstant.RESULT_NEED_LOGIN;
import static com.kaolafm.kradio.byd.BydSpeechCtlActionConstant.RESULT_NETWORK_ERROR;
import static com.kaolafm.kradio.byd.BydSpeechCtlActionConstant.RESULT_SUCCESS;

/**
 * 供语音控制调用的功能实现
 */
public class BydSpeechCtlManager implements BydSpeechCtlInter {

    private ClientImpl client;

    private static final String TAG = "BydSpeechCtlManager";

    private static volatile BydSpeechCtlManager manager;

    public static BydSpeechCtlManager getInstance() {
        if (manager == null) {
            synchronized (BydSpeechCtlManager.class) {
                if (manager == null) {
                    manager = new BydSpeechCtlManager();
                }
            }
        }
        return manager;
    }

    private BydSpeechCtlManager() {
        //fixmed 错误: 无法访问IClientAPI
        //           找不到com.kaolafm.sdk.client.IClientAPI的类文件
        //通过compileOnly的方式在flavor中引入ClientSdk，解决编译问题
        client = new ClientImpl(AppDelegate.getInstance().getContext());
    }


    public void registerSpeechCtlReceiver(Context context) {
        IntentFilter filter = new IntentFilter();
        filter.addAction(BydSpeechCtlActionConstant.AUTOVOICE_BOOK);
        filter.addAction(BydSpeechCtlActionConstant.AUTOVOICE_UNBOOK);
        filter.addAction(BydSpeechCtlActionConstant.AUTOVOICE_JUMP_TO);
        filter.addAction(BydSpeechCtlActionConstant.AUTOVOICE_FORWARD);
        filter.addAction(BydSpeechCtlActionConstant.AUTOVOICE_PLAY_LATELY);
        filter.addAction(BydSpeechCtlActionConstant.AUTOVOICE_PLAY_BOOK);
        filter.addAction(BydSpeechCtlActionConstant.AUTOVOICE_REWIND);
        filter.addAction(BydSpeechCtlActionConstant.AUTOVOICE_QUALITY);
        filter.addAction(BydSpeechCtlActionConstant.AUTOVOICE_QUIT);
        filter.addAction(BydSpeechCtlActionConstant.FUNCTION_UPDATE_RESULT);
        filter.addAction(BydSpeechCtlActionConstant.AUTOVOICE_SEARCH);
        filter.addAction(BydSpeechCtlActionConstant.AUTOVOICE_RECOMMEND);
        filter.addAction(BydSpeechCtlActionConstant.AUTOVOICE_ACTIVECARE);
        context.registerReceiver(new BydSpeechCtlReceiver(), filter);
    }

    /**
     * am broadcast -a com.byd.action.AUTOVOICE_BOOK
     */
    @Override
    public void book() {
        if (!isUserLogin(BydSpeechCtlActionConstant.AUTOVOICE_BOOK,
                ResUtil.getString(R.string.speech_login_tips))) {
            return;
        }

        long subscribedId = PlayerManagerHelper.getInstance().getSubscribeId();
        try {
            client.subscribe(subscribedId, new ISubscribeResult() {
                @Override
                public void onSuccuss() throws RemoteException {
                    sendCmdResult(BydSpeechCtlActionConstant.AUTOVOICE_BOOK, RESULT_SUCCESS,
                            ResUtil.getString(R.string.speech_book_success));
                }

                @Override
                public void onFailure(ErrorInfo errorInfo) throws RemoteException {
                    sendCmdResult(BydSpeechCtlActionConstant.AUTOVOICE_BOOK, RESULT_FAILED,
                            ResUtil.getString(R.string.speech_book_fail));
                }

                @Override
                public IBinder asBinder() {
                    return null;
                }
            });
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * am broadcast -a com.byd.action.AUTOVOICE_UNBOOK
     */
    @Override
    public void unBook() {
        if (!isUserLogin(BydSpeechCtlActionConstant.AUTOVOICE_UNBOOK,
                ResUtil.getString(R.string.speech_login_tips))) {
            return;
        }

        long subscribeId = PlayerManagerHelper.getInstance().getSubscribeId();
        try {
            client.unsubscribe(subscribeId, new ISubscribeResult() {
                @Override
                public void onSuccuss() throws RemoteException {
                    sendCmdResult(BydSpeechCtlActionConstant.AUTOVOICE_UNBOOK, RESULT_SUCCESS,
                            ResUtil.getString(R.string.speech_unbook_success));
                }

                @Override
                public void onFailure(ErrorInfo errorInfo) throws RemoteException {
                    sendCmdResult(BydSpeechCtlActionConstant.AUTOVOICE_UNBOOK, RESULT_FAILED,
                            ResUtil.getString(R.string.speech_unbook_fail));
                }

                @Override
                public IBinder asBinder() {
                    return null;
                }
            });
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void jumpTo(int secondsPosition) {
        int millsPosition = secondsPosition * 1000;
        try {
            client.jumpTo(millsPosition);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void forward(int seconds) {
        try {
            if (seconds != -1) {//有参数时执行指定的快进秒数（seconds）
                client.forward(seconds);
            } else { //无参数时执行默认快近秒数（30s）
                client.forward();
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void rewind(int seconds) {
        try {
            if (seconds != -1) {//有参数时执行指定的快退秒数（seconds）
                client.backward(seconds);
            } else { //无参数时执行默认快退秒数（30s）
                client.backward();
            }
            sendCmdResult(BydSpeechCtlActionConstant.AUTOVOICE_QUIT, RESULT_SUCCESS, "");
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void speedAdjust() {
        sendUnSupportResult(BydSpeechCtlActionConstant.AUTOVOICE_SPEED_ADJUST);
    }

    @Override
    public void playLike() {
        sendUnSupportResult(BydSpeechCtlActionConstant.AUTOVOICE_PLAY_LIKE);
    }

    @Override
    public void download() {
        sendUnSupportResult(BydSpeechCtlActionConstant.AUTOVOICE_PLAY_DOWNLOAD);
    }

    @Override
    public void playMode() {
        sendUnSupportResult(BydSpeechCtlActionConstant.AUTOVOICE_PLAY_MODE);
    }

    @Override
    public void openLyric() {
        sendUnSupportResult(BydSpeechCtlActionConstant.AUTOVOICE_CLOSE_LYRIC);
    }

    @Override
    public void closeLyric() {
        sendUnSupportResult(BydSpeechCtlActionConstant.AUTOVOICE_CLOSE_LYRIC);
    }

    @Override
    public void quit() {
        try {
            client.exitApp();
            sendCmdResult(BydSpeechCtlActionConstant.AUTOVOICE_QUIT, RESULT_SUCCESS,
                    ResUtil.getString(R.string.speech_quit_tips_success));
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void quality(String quality) {
        int qualityCode = 0;
        switch (quality) {
            case "高品质":
                qualityCode = PlayerConstants.ToneQuality.HIGH_TONE_QUALITY;
                break;
            case "无损品质":
                qualityCode = PlayerConstants.ToneQuality.HIGHER_TONE_QUALITY;
                break;
            case "标准":
                qualityCode = PlayerConstants.ToneQuality.MIDDLE_TONE_QUALITY;
            default:
                break;
        }
        try {
            client.changeVoiceQuality(qualityCode);
            sendCmdResult(AUTOVOICE_QUALITY, RESULT_SUCCESS, "");
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void search(Intent intent) {
        Operation operation;
        String type = intent.getStringExtra("EXTRA_TYPE_SEARCH");
        if ("新闻".equals(type)) {
            operation = new NewsOperation(client, intent);
        } else if ("在线节目".equals(type)) {
            operation = new RadioOperation(client, intent);
        } else {
            operation = new AlbumOperation(client, intent);
        }
        operation.exe();
    }

    @Override
    public void playRecommend() {
        //播放首页的第一个条目
        HomeDataManager.getInstance().playFirstItemWithCallBack(new HomeDataManager.PlayFirstItemCallback() {
            @Override
            public void success() {
                sendCmdResult(BydSpeechCtlActionConstant.AUTOVOICE_QUIT, RESULT_SUCCESS, "");
            }

            @Override
            public void failed() {
                sendCmdResult(BydSpeechCtlActionConstant.AUTOVOICE_QUIT, RESULT_FAILED, "");
            }
        });
    }

    @Override
    public void playLately() {
        HistoryManager.getInstance().getHistoryList(new HttpCallback<List<HistoryItem>>() {
            @Override
            public void onSuccess(List<HistoryItem> historyItems) {
                if (historyItems != null && historyItems.size() > 0)
                    PlayerManagerHelper.getInstance().startHistory(historyItems.get(0));
            }

            @Override
            public void onError(ApiException e) {
                e.printStackTrace();
            }
        });
    }

    @Override
    public void playBook() {
        if (!isUserLogin(BydSpeechCtlActionConstant.AUTOVOICE_BOOK,
                ResUtil.getString(R.string.speech_login_tips))) {
            return;
        }
        SubscribeManager.getInstance(AppDelegate.getInstance().getContext(), CP.KaoLaFM).getAllSubscribes(new LoadCallback() {
            @Override
            public void onSuccess(List<SubscribeData> subscribes) {
                //播放订阅节目
                SubscribeData subscribeData = subscribes.get(0);
                if (subscribeData.getIsOnline() == 1) {
                    PlayerManagerHelper.getInstance().startSubscribe(subscribeData);
                    sendCmdResult(BydSpeechCtlActionConstant.AUTOVOICE_QUIT, RESULT_SUCCESS, "");
                } else {
                    com.kaolafm.kradio.common.ErrorInfo errorInfo = new com.kaolafm.kradio.common.ErrorInfo(400);
                    errorInfo.des = "该订阅资源已下线";
                    onFailure(errorInfo);
                }
            }

            @Override
            public void onFailure(com.kaolafm.kradio.common.ErrorInfo errorInfo) {
                Log.i(TAG, errorInfo.des);
                sendCmdResult(BydSpeechCtlActionConstant.AUTOVOICE_QUIT, RESULT_FAILED, errorInfo.des);
            }
        });
    }

    @Override
    public void collect() {
        sendUnSupportResult(BydSpeechCtlActionConstant.AUTOVOICE_COLLECT);
    }

    @Override
    public void unCollect() {
        sendUnSupportResult(BydSpeechCtlActionConstant.AUTOVOICE_UNCOLLECT);
    }

    @Override
    public void playFavorite() {
        sendUnSupportResult(BydSpeechCtlActionConstant.AUTOVOICE_PLAY_FAVORITE);
    }

    @Override
    public void sendCmdResult(String action, String code, String msg) {
        SpeechUtil.sendCmdResult(action, code, msg);
    }

    private boolean isUserLogin(String action, String msg) {
        boolean isLogin = UserInfoManager.getInstance().isUserBound();
        if (!isLogin) {
            //发送没有登录提示
            sendCmdResult(action, RESULT_NEED_LOGIN, msg);
        }

        return isLogin;
    }

    private void sendUnSupportResult(String action) {
        sendCmdResult(action, RESULT_FAILED,
                ResUtil.getString(R.string.speech_unsupport_fail));
    }

    /**
     * 当传入参数为“场景code=0” 时，需要继续播放当前播放的节目（若当前没有播放的内容，生态内
     * 容可播放猜你喜欢类的节目/专辑）。除此之外，其他场景code值输入均为播放该code对应的场景
     * 数值为"TRUE"时为导航在前台，参数值为"FALSE"时，
     * 默认前台 intent.putExtra("EXTRA_SCENE_ID","1031");
     * 字符串类型，场景code类型（0|1031|1051）
     * 返回值定义： 0：执行成功 1：其他执行失败 2：超出可设置的范围 3：当前场景不支持该功能
     * 4：需手动操作用户协议后执行 5：需登录后支持 6：网络异常 7：当前功能中暂无数据信息
     *
     * @param code
     * @param isBackGround
     */
    public void playScene(String code, boolean isBackGround) {
//        返回值定义： 0：执行成功 1：其他执行失败 2：超出可设置的范围 3：当前场景不支持该功能 4：需手动操作用户协议后执行 5：需登录后支持 6：网络异常 7：当前功能中暂无数据信息
        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext())) {
            sendCmdResult(BydSpeechCtlActionConstant.AUTOVOICE_ACTIVECARE, RESULT_NETWORK_ERROR,
                    "");
            return;
        }
    }
}
