package com.kaolafm.kradio.lib.base.flavor.player;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-02-09 11:04
 ******************************************/

public interface KLOnPlayerPlayLogicListener {
    /**
     * 如果全部播单已经播放完毕，是否可以循环至播单第一个继续播放
     *
     * @return true为是，false为否
     */
    boolean canCycleToFirstAudio();

    /**
     * 是否忽略检查当前播单是否还有下一个音频
     *
     * @return true为是，false为否
     */
    boolean canIgnoreCheckHasNextAudio();

    /**
     * 是否检查音频准备中状态逻辑
     *
     * @return true为执行检查流程不走后续流程，false为忽略检查结果继续走后续流程
     */
    boolean doCheckPlayerPreparingLogic();
}
