<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    tools:background="@color/Blue"
    android:paddingTop="@dimen/y23"
    android:paddingBottom="@dimen/y19">

    <RelativeLayout
        android:id="@+id/titleLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:id="@+id/msg_item_pic"
            android:layout_width="@dimen/m32"
            android:layout_height="@dimen/m32"
            android:scaleType="centerInside"
            android:layout_marginEnd="@dimen/x10"
            tools:src="@drawable/message_green_icon" />

        <ImageView
            android:layout_centerVertical="true"
            android:layout_alignParentEnd="true"
            android:id="@+id/un_readed_tip"
            android:layout_marginStart="@dimen/m16"
            android:layout_marginEnd="@dimen/m16"
            android:src="@drawable/small_red_oval"
            android:layout_width="@dimen/m16"
            android:layout_height="@dimen/m16" />

        <TextView
            android:maxWidth="@dimen/x800"
            android:layout_toEndOf="@id/msg_item_pic"
            android:id="@+id/msg_item_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:textColor="@color/message_item_title_color2"
            android:textSize="@dimen/m24"
            android:maxLines="1"
            tools:text="sk进突破aassk进突破aask进突破ask进突sk进突破aask进突破ask进突破ask破askk进突破ask进突破ask" />

        <TextView
            android:layout_alignParentEnd="true"
            android:layout_toEndOf="@id/msg_item_title"
            android:id="@+id/msg_item_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/x32"
            android:textColor="@color/message_item_title_color2"
            android:textSize="@dimen/m24"
            tools:text="时间 1231321" />

    </RelativeLayout>

    <TextView
        android:id="@+id/msg_item_content"
        android:layout_width="0dp"
        android:lineSpacingExtra="@dimen/m8"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y20"
        android:layout_marginEnd="@dimen/x32"
        android:textColor="@color/message_item_content_color"
        android:textSize="@dimen/m26"
        android:maxLines="3"
        android:ellipsize="end"
        app:layout_constraintEnd_toStartOf="@id/arrowIcon"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titleLayout"
        tools:text="前方3公里正在进行道路施工车辆行驶缓慢，" />

    <ImageView
        android:id="@+id/arrowIcon"
        android:layout_width="@dimen/m36"
        android:layout_height="@dimen/m36"
        android:src="@drawable/message_arrow_right"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>