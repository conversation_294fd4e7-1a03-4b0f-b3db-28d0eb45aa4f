<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/comprehensive_topic_posts_item_bg">

    <com.kaolafm.kradio.component.ui.base.view.RoundCircleImageView
        android:id="@+id/userIcon"
        android:layout_width="@dimen/m48"
        android:layout_height="@dimen/m48"
        android:layout_marginStart="@dimen/m20"
        android:layout_marginTop="@dimen/m23"
        app:isCircle="true"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/userName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/m10"
        android:layout_marginTop="@dimen/m23"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/comprehensive_topic_primary_color"
        android:textSize="@dimen/m20"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toStartOf="@id/likeParentView"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@id/userIcon"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="用户昵称用户昵称用户昵称用户昵称用户昵称用户昵称用户昵称用户昵称用户昵称用户昵称用户昵称用户昵称" />

    <TextView
        android:id="@+id/updateTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/m10"
        android:layout_marginTop="@dimen/m3"
        android:layout_marginBottom="@dimen/m1"
        android:textColor="@color/comprehensive_topic_subtitle_text_color"
        android:textSize="@dimen/m16"
        app:layout_constraintBottom_toBottomOf="@id/userIcon"
        app:layout_constraintStart_toEndOf="@id/userIcon"
        tools:text="更新时间" />


    <com.kaolafm.kradio.component.ui.base.view.ScaleConstraintLayout
        android:id="@+id/likeParentView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="0dp"
        android:paddingTop="@dimen/m43"
        android:paddingEnd="@dimen/m28"
        android:paddingBottom="@dimen/m40"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/likeCount"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/m26"
            android:gravity="center"
            android:textColor="@color/comprehensive_topic_subtitle_text_color"
            android:textSize="@dimen/m20"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="3660" />

        <ImageView
            android:id="@+id/likeIcon"
            android:layout_width="@dimen/m26"
            android:layout_height="@dimen/m26"
            android:layout_marginEnd="@dimen/m10"
            android:src="@drawable/comprehensive_topic_posts_unlike"
            app:layout_constraintEnd_toStartOf="@id/likeCount"
            app:layout_constraintTop_toTopOf="@id/likeCount" />

    </com.kaolafm.kradio.component.ui.base.view.ScaleConstraintLayout>

    <TextView
        android:id="@+id/postsContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/m20"
        android:layout_marginTop="@dimen/m13"
        android:layout_marginEnd="@dimen/m20"
        android:layout_marginBottom="@dimen/m20"
        android:lineSpacingExtra="@dimen/m6"
        android:textSize="@dimen/m20"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/userIcon" />
</androidx.constraintlayout.widget.ConstraintLayout>