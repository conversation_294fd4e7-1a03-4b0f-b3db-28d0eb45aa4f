<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <permission android:name="com.huawei.hmsauto.permission.intelligence.SERVICE_PROVIDER" />
    <uses-permission android:name="com.huawei.hicar.HICAR_PERMISSION_SYSTEM" />
    <uses-permission android:name="com.huawei.ohos.carmediaui.CARMEDIAUI_PERMISSION" />
    <uses-permission android:name="com.huawei.ohos.carmediaui.CARMEDIAUI_PERMISSION_SYSTEM" />

    <application
        android:allowBackup="false"
        tools:replace="android:allowBackup">
        <!--设置品牌色-->
        <!--
        <meta-data
            android:name="carmediaui_logo_color"
            android:value="\#6C7190" />
            -->
        <activity
            android:name="com.kaolafm.kradio.huawei.activity.AboutActivity"
            android:exported="true">
            <intent-filter>
                <action
                    android:name="com.yunting.car.ABOUT" />
                <category
                    android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity android:name="com.kaolafm.kradio.huawei.activity.FeedbackActivity"
            android:theme="@style/dialogActivityTheme">
            <intent-filter>
                <action
                    android:name="com.yunting.car.FEEDBACK"/>
                <category
                    android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </activity>
    </application>

</manifest>