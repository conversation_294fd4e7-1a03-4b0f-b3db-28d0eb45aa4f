package com.kaolafm.kradio.history;

import android.app.Application;
import android.util.Log;

import com.kaolafm.kradio.common.KradioSDKManager;
import com.kaolafm.kradio.history.utils.TingBanToKRadioUtil;
import com.kaolafm.kradio.lib.init.AppInit;
import com.kaolafm.kradio.lib.init.BaseAppInitializer;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.YTDataCache;
import com.kaolafm.kradio.lib.utils.YTLogUtil;

/**
 * <AUTHOR>
 * @date 2019-11-14
 */
@AppInit(priority = 40, description = "收听历史初始化",isAsync = true)
public class HistoryInitializer extends BaseAppInitializer {

    @Override
    public void onCreate(Application application) {
        YTLogUtil.logStart("HistoryInitializer", "onCreate", "start");
        KradioSDKManager.getInstance().addUsableObserver(() -> {
            HistoryManager.getInstance().init();
            YTDataCache.prepareHistoryData(HistoryManager.getInstance());
            TingBanToKRadioUtil.init(application);
        });
        YTLogUtil.logStart("HistoryInitializer", "onCreate", "end");
    }

    @Override
    public void asyncCreate(Application application) {
        super.asyncCreate(application);
    }

    @Override
    public void onTerminate() {
        super.onTerminate();
        HistoryManager.getInstance().destroy();
    }
}
