package com.kaolafm.kradio.flavor.impl;


import android.app.Application;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.provider.Settings;
import android.util.Log;

import com.kaolafm.kradio.flavor.carnetwork.carnetworkutils.CarAuthUtil;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioAuthInter;
import com.kaolafm.kradio.lib.sdk.utils.UserInfoManager;
import com.kaolafm.kradio.lib.utils.NetworkMonitor;

import com.kaolafm.sdk.core.mediaplayer.IPlayerStateListener;



public class KRadioAuthImpl implements KRadioAuthInter, NetworkMonitor.OnNetworkStatusChangedListener, IPlayerStateListener {

    private ConnectivityManager mConnectivityManager;
    private int oldStatus;
    private int newStatus;
    private Application context;
    private NetworkInfo info;
    private static final String TYPE_MUSIC = "MUSIC";
    //正在播放(1) 暂停(0) 停止(-1)
    private static final int PLAY_STATUS_PLAY = 1;
    private static final int PLAY_STATUS_PAUSE = 0;
    private static final int PLAY_STATUS_STOP = -1;

    private static class InstanceHolder {
        private final static KRadioAuthImpl sInstance = new KRadioAuthImpl();
    }

    public static KRadioAuthImpl getInstance() {
        return InstanceHolder.sInstance;
    }

    @Override
    public boolean doInitCheckCanPlayInter() {
        PlayerCustomizeManager.getInstance().injectKLCheckCanPlayListener("com.kaolafm.kradio.flavor.impl.CheckCanPlayImpl");
//        NetworkMonitor.getInstance(AppDelegate.getInstance().getContext()).registerNetworkStatusChangeListener(this);
        context = AppDelegate.getInstance().getContext();
        IntentFilter filter = new IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION);
        if (AppDelegate.getInstance().getContext() != null) {
            AppDelegate.getInstance().getContext().registerReceiver(mReceiver, filter);
            mConnectivityManager = (ConnectivityManager) AppDelegate.getInstance().getContext().getSystemService(Context.CONNECTIVITY_SERVICE);
            info = mConnectivityManager.getActiveNetworkInfo();
            newStatus = oldStatus = info.getType();
        }
        PlayerManager.getInstance().addIPlayerStateListener(this);
        return true;
    }

    @Override
    public boolean unInitCheckCanPlayInter() {
        if (AppDelegate.getInstance().getContext() != null) {
            AppDelegate.getInstance().getContext().unregisterReceiver(mReceiver);
        }
        PlayerManager.getInstance().removeIPlayerStateListener(this);
        return true;
    }

    @Override
    public boolean doCheckAuth(Object... args) {
        if (PlayerCustomizeManager.getInstance().getCheckCanPlayInter() != null) {
            PlayerCustomizeManager.getInstance().getCheckCanPlayInter().checkPlay(args);
        }
        return true;
    }

    @Override
    public boolean authStatus(Object... args) {
        //一个本地保存的鉴权状态  一个是网络是否需要去鉴权
//        !CarAuthUtil.needAuthByNetWork() || CarAuthUtil.AUTH_THROUGH;
        return CarAuthUtil.AUTH_THROUGH;
    }

    @Override
    public boolean netWorkStatus(Object... args) {
        return CarAuthUtil.needAuthByNetWork();
    }

    @Override
    public void onStatusChanged(int newStatus, int oldStatus) {
        Log.i("zsj", "onStatusChanged: newStatus = " + newStatus + ",oldStatus = " + oldStatus);
        Log.i("zsj", "onStatusChanged: CarAuthUtil.needAuthByNetWork() = " + CarAuthUtil.needAuthByNetWork());
        if (!CarAuthUtil.needAuthByNetWork()) {
            return;
        }
        CarAuthUtil.saveAuthAndDate(String.valueOf(System.currentTimeMillis()), false);
        doCheckAuth(PlayerManager.getInstance().getCurPlayItem(), KRadioAuthInter.METHOD_NETCHANGE, false);
    }


    private BroadcastReceiver mReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent.getAction().equals(ConnectivityManager.CONNECTIVITY_ACTION)) {
                if (!UserInfoManager.getInstance().isUserActivation()) {
                    return;
                }
                Log.i("zsj", "mReceiver: CarAuthUtil.needAuthByNetWork() = " + CarAuthUtil.needAuthByNetWork());
//
                if (mConnectivityManager != null) {
                    info = mConnectivityManager.getActiveNetworkInfo();
                }
                if (info != null && info.isConnected()) {
                    newStatus = info.getType();
                    Log.i("zsj", "mReceiver: newStatus = " + newStatus + ",oldStatus = " + oldStatus);
                    if (oldStatus != newStatus) {
                        //网络变化
                        oldStatus = newStatus;
                    } else {
                        //网络无变化
                        return;
                    }
                } else {
                    //无网络
                    return;
                }

                if (CarAuthUtil.ischecking) {
                    Log.i("zsj", "mReceiver: CarAuthUtil.ischecking = " + CarAuthUtil.ischecking);
                    return;
                }

                if (!CarAuthUtil.needAuthByNetWork()) {
                    return;
                }
                CarAuthUtil.saveAuthAndDate(String.valueOf(System.currentTimeMillis()), false);
                doCheckAuth(PlayerManager.getInstance().getCurPlayItem(), KRadioAuthInter.METHOD_NETCHANGE, false);

            }
        }
    };


    @Override
    public void onIdle(PlayItem playItem) {
        if (playItem != null) {
            setPlayingTitle(playItem.getTitle(), TYPE_MUSIC, PLAY_STATUS_STOP);
            stopPlaying();
        }
    }

    @Override
    public void onPlayerPreparing(PlayItem playItem) {

    }

    @Override
    public void onPlayerPlaying(PlayItem playItem) {
        if (playItem != null) {
            setPlayingTitle(playItem.getTitle(), TYPE_MUSIC, PLAY_STATUS_PLAY);
        }
    }

    @Override
    public void onPlayerPaused(PlayItem playItem) {
        if (playItem != null) {
            setPlayingTitle(playItem.getTitle(), TYPE_MUSIC, PLAY_STATUS_PAUSE);
        }
    }

    @Override
    public void onProgress(String s, int i, int i1, boolean b) {
    }

    @Override
    public void onPlayerFailed(PlayItem playItem, int i, int i1) {

    }

    @Override
    public void onPlayerEnd(PlayItem playItem) {

    }

    @Override
    public void onSeekStart(String s) {

    }

    @Override
    public void onSeekComplete(String s) {

    }

    @Override
    public void onBufferingStart(PlayItem playItem) {

    }

    @Override
    public void onBufferingEnd(PlayItem playItem) {

    }

    /**
     * 设置正在播放信息
     *
     * @param playname  正在播放的信息
     * @param playtype  正在播放类型 视频("MOVIE") 收音机("RADIO") 音乐("MUSIC")
     * @param playstate 正在播放(1) 暂停(0) 停止(-1)
     */
    public void setPlayingTitle(String playname, String playtype, int playstate) {
        if (playname != null && playtype != null) {
            Settings.System.putString(context.getContentResolver(),
                    KRadioAuthImpl.NAME, playname);
            Settings.System.putString(context.getContentResolver(),
                    KRadioAuthImpl.TYPE, playtype);
            Settings.System.putInt(context.getContentResolver(),
                    KRadioAuthImpl.STATE, playstate);

            Intent intent = new Intent();
            intent.setAction(KRadioAuthImpl.ACTION_PLAY_CHANGE);
            context.sendBroadcast(intent);
        }
    }

    /**
     * 停止播放显示
     */
    public void stopPlaying() {
        Settings.System.putString(context.getContentResolver(),
                KRadioAuthImpl.NAME, "");
        Settings.System.putString(context.getContentResolver(),
                KRadioAuthImpl.TYPE, "");
        Settings.System.putInt(context.getContentResolver(),
                KRadioAuthImpl.STATE, KRadioAuthImpl.STATE_STOP);

        Intent intent = new Intent();
        intent.setAction(KRadioAuthImpl.ACTION_PLAY_CHANGE);
        context.sendBroadcast(intent);
    }

    /**
     * 存入系统的播放名称
     */
    public static final String NAME = "PLAYNAME";
    /**
     * 存入系统的播放类型
     */
    public static final String TYPE = "PLAYTYPE";
    /**
     * 存入系统的播放状态
     */
    public static final String STATE = "PLAYSTATE";
    /**
     * 播放状态播放中
     */
    public static final int STATE_PLAYING = 1;
    /**
     * 播放状态暂停
     */
    public static final int STATE_PAUSE = 0;
    /**
     * 播放状态停止
     */
    public static final int STATE_STOP = -1;
    /**
     * 媒体变化广播 acition
     */
    public static final String ACTION_PLAY_CHANGE = "com.incall.apps.playing.merge";


}
