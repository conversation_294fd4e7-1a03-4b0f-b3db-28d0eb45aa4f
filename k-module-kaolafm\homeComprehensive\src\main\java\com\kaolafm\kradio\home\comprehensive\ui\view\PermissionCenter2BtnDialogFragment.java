package com.kaolafm.kradio.home.comprehensive.ui.view;

import android.content.res.Configuration;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentActivity;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.kaolafm.kradio.component.ui.base.skin.SkinHelper;
import com.kaolafm.kradio.common.utils.CommonUtils;
import com.kaolafm.kradio.common.utils.FlavorUtil;
import com.kaolafm.kradio.common.comprehensive.web.WebViewActivity;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.dialog.BaseDialogFragment;
import com.kaolafm.kradio.lib.dialog.DialogListener;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.event.DialogExposureEvent;
import com.kaolafm.report.util.ReportConstants;
import com.kaolafm.report.util.ReportParameterManager;

/**
 * <AUTHOR>
 **/
public class PermissionCenter2BtnDialogFragment extends BaseDialogFragment {

    private View mTvDialogButtonMainLayout;
    private TextView mTvDialogBottomCancel;
    private TextView mTvDialogBottomDefine;
    private TextView mTvDialogBottomMessage;
    private TextView mTvCb;
    private LinearLayout check_ll;
    private CheckBox check_box;
    private View mRootView;
    private DialogListener.OnNativeListener<DialogFragment> mNativeListener;
    private DialogListener.OnPositiveListener<DialogFragment> mPositiveListener;
    private CharSequence message;
    private CharSequence leftBtnText;
    private CharSequence rightBtnText;
    private boolean canShowButton = true;
    private boolean canShowCheckBox = false;//是否显示checkBox
    private boolean checkBoxSelect = false;//是否选中

    public static PermissionCenter2BtnDialogFragment create() {
        PermissionCenter2BtnDialogFragment fragment = new PermissionCenter2BtnDialogFragment();
        fragment.setGravity(Gravity.TOP);
        fragment.setWidth(WindowManager.LayoutParams.WRAP_CONTENT);
//        fragment.setHeight((int) (ScreenUtil.getScreenHeight() * 0.14f));
        fragment.setHeight(WindowManager.LayoutParams.WRAP_CONTENT);
        return fragment;
    }

    @Override
    public void onStart() {
        super.onStart();
        Window window = getDialog().getWindow();
        WindowManager.LayoutParams params = window.getAttributes();
        if (SkinHelper.isNightMode()) {
            params.dimAmount = 0.9f;
        } else {
            params.dimAmount = 0.7f;
        }
        window.setAttributes(params);
        CommonUtils.getInstance().initGreyStyle(window);
    }

    @Override
    protected View getContentView() {
        FragmentActivity activity = getActivity();
        View inflate = View.inflate(activity, R.layout.dialog_fragment_permission_center_2btn, null);
        mTvDialogButtonMainLayout = inflate.findViewById(R.id.tv_dialog_button_main_layout);
        mTvDialogBottomCancel = inflate.findViewById(R.id.tv_dialog_bottom_cancel);
        mTvDialogBottomDefine = inflate.findViewById(R.id.tv_dialog_bottom_define);
        mTvDialogBottomMessage = inflate.findViewById(R.id.tv_dialog_bottom_message);
        check_ll = inflate.findViewById(R.id.check_ll);
        check_box = inflate.findViewById(R.id.check_box);
        mTvCb = inflate.findViewById(R.id.tvCb);
        mRootView = inflate.findViewById(R.id.root_layout);
        mRootView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

            }
        });
        mTvCb.setOnClickListener(v -> check_box.performClick());
        check_box.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                checkBoxSelect = isChecked;
            }
        });
        mTvDialogBottomCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!AntiShake.check(v.getId())) {
                    ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_PRIVACY_POLICY_DISAGREE, mTvDialogBottomCancel.getText().toString(),
                            getPageId(), ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_PRIVACY_POLICY));

                    if (mNativeListener != null) {
                        mNativeListener.onClick(PermissionCenter2BtnDialogFragment.this);
                    } else {
                        dismiss();
                    }
                }
            }
        });

        mTvDialogBottomDefine.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!AntiShake.check(v.getId())) {
                    ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_PRIVACY_POLICY_AGREE, mTvDialogBottomDefine.getText().toString(),
                            getPageId(), ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_PRIVACY_POLICY));
                    if (canShowCheckBox) {
                        if (!checkBoxSelect) {
                            ToastUtil.showInfo(getContext(), R.string.launcher_agreement_first_toast);
                        } else {
                            if (mPositiveListener != null) {
                                mPositiveListener.onClick(PermissionCenter2BtnDialogFragment.this);
                            }
                            dismiss();
                        }
                    } else {
                        if (mPositiveListener != null) {
                            mPositiveListener.onClick(PermissionCenter2BtnDialogFragment.this);
                        }
                        dismiss();
                    }

                }
            }
        });
        mTvDialogBottomMessage.setText(CommonUtils.getInstance().initAgreementTextAndClick(message.toString()
                , new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (!NetworkUtil.isNetworkAvailable(getContext(), true)) {
                            return;
                        }
                        String theme = "dark";
                        if (SkinHelper.isDayMode()) {
                            theme = "light";
                        }
                        String web = ResUtil.getString(R.string.http_url_server_agreement)
                                + "?theme=" + theme + "&bgColor=transparent&contentSize="
                                + (int) ResUtil.getDimension(R.dimen.m22)
                                + "&showTitle=1"
                                + "&marginL=0"
                                + "&unit=1"
                                + "&marginR=" + ResUtil.getDimen(R.dimen.n33)
                                + "&textIndent=0";
                        WebViewActivity.start(getActivity(),
                                FlavorUtil.getHttp443Url(web), getResources().getString(R.string.launcher_agreement0)
                                , Constants.PAGE_ID_ACCOUNT_PROTOCOL);
//                                  group1.setVisibility(View.VISIBLE);
//                                  group.setVisibility(View.GONE);
//                                  tvTitle.setText("");
//                                  String web = FlavorUtil.getHttp443Url(ResUtil.getString(R.string.http_url_server_agreement));
//                                  showWebView(webView, web);
//                                  initNoticeHolder().web = 1;
                    }
                }, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (!NetworkUtil.isNetworkAvailable(getContext(), true)) {
                            return;
                        }
                        String theme = "dark";
                        if (SkinHelper.isDayMode()) {
                            theme = "light";
                        }
                        String CUR_PAGE = ResUtil.getString(R.string.http_url_policy)
                                + "?theme=" + theme + "&bgColor=transparent&contentSize="
                                + (int) ResUtil.getDimension(R.dimen.m22)
                                + "&showTitle=1"
                                + "&marginL=0"
                                + "&unit=1"
                                + "&marginR=" + ResUtil.getDimen(R.dimen.n33)
                                + "&textIndent=0";
                        WebViewActivity.start(getContext(),
                                FlavorUtil.getHttp443Url(CUR_PAGE), getResources().getString(R.string.launcher_agreement1)
                                , Constants.PAGE_ID_ACCOUNT_POLICY);

//                                  group1.setVisibility(View.VISIBLE);
//                                  group.setVisibility(View.GONE);
//                                  tvTitle.setText("");
//                                  showWebView(webView, FlavorUtil.getHttp443Url(ResUtil.getString(R.string.http_url_policy)));
//                                  initNoticeHolder().web = 2;
                    }
                }));
        mTvDialogBottomMessage.setHighlightColor(0);
        mTvDialogBottomMessage.setMovementMethod(LinkMovementMethod.getInstance());
        ViewUtil.setViewVisibility(mTvDialogButtonMainLayout, canShowButton ? View.VISIBLE : View.GONE);
        ViewUtil.setViewVisibility(check_ll, canShowCheckBox ? View.VISIBLE : View.GONE);
        if (!TextUtils.isEmpty(leftBtnText)) {
            mTvDialogBottomDefine.setText(leftBtnText);
        }

        if (!TextUtils.isEmpty(rightBtnText)) {
            mTvDialogBottomCancel.setText(rightBtnText);
        }

        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_PRIVACY_POLICY_AGREE, mTvDialogBottomDefine.getText().toString(),
                getPageId(), ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_PRIVACY_POLICY));
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_PRIVACY_POLICY_DISAGREE, mTvDialogBottomCancel.getText().toString(),
                getPageId(), ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_PRIVACY_POLICY));
        return inflate;
    }

    public void setMessage(CharSequence message) {
        this.message = message;
    }

    public void setLeftButton(CharSequence leftBtnText) {
        this.leftBtnText = leftBtnText;
    }

    public void setRightButton(CharSequence rightBtnText) {
        this.rightBtnText = rightBtnText;
    }

    public void setOnNativeListener(DialogListener.OnNativeListener<DialogFragment> nativeListener) {
        mNativeListener = nativeListener;
    }

    public void setOnPositiveListener(DialogListener.OnPositiveListener<DialogFragment> positiveListener) {
        mPositiveListener = positiveListener;
    }

    public void setCanShowButton(boolean canShowButton) {
        this.canShowButton = canShowButton;
    }

    public void setCanShowCheckBox(boolean canShowCheckBox) {
        this.canShowCheckBox = canShowCheckBox;
    }

    @Override
    protected void showAccordingToScreen(int orientation) {
        super.showAccordingToScreen(orientation);

        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            changeLandView();
        } else {
            changePortraitView();
        }
    }

    //横屏显示
    private void changeLandView() {
        ViewGroup.MarginLayoutParams rootViewLayoutParams =
                (ViewGroup.MarginLayoutParams) mRootView.getLayoutParams();
        rootViewLayoutParams.topMargin = ResUtil.getDimen(R.dimen.y198);
    }

    //竖屏显示
    private void changePortraitView() {
        ViewGroup.MarginLayoutParams rootViewLayoutParams =
                (ViewGroup.MarginLayoutParams) mRootView.getLayoutParams();
        rootViewLayoutParams.topMargin = ResUtil.getDimen(R.dimen.y294);
    }


    @Override
    protected void reportDialogExposureEvent(long duration) {
        super.reportDialogExposureEvent(duration);
        ReportHelper.getInstance().addEvent(new DialogExposureEvent(ReportConstants.DIALOG_ID_PRIVACY_POLICY, ReportParameterManager.getInstance().getPage(),duration,null));
    }
}
