package com.kaolafm.kradio.online.categories.holder;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.kradio.common.SubcategoryItemBean;
import com.kaolafm.kradio.k_kaolafm.R; 
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
 
/**
 * 本地广播
 * <AUTHOR>
 * @date 2018/5/9
 */

public class BroadcastLocalViewHolder extends BaseHolder<SubcategoryItemBean> {
 
    ImageView mIvBroadcastCover;
 
    TextView mTvBroadcastName;

    public BroadcastLocalViewHolder(View itemView) {
        super(itemView);
        mIvBroadcastCover=itemView.findViewById(R.id.iv_broadcast_cover);
        mTvBroadcastName=itemView.findViewById(R.id.tv_broadcast_name);
    }

    @Override
    public void setupData(SubcategoryItemBean subcategoryItemBean, int position) {
        ImageLoader.getInstance().displayImage(itemView.getContext(), subcategoryItemBean.getCoverUrl(), mIvBroadcastCover);
        mTvBroadcastName.setText(subcategoryItemBean.getName());
        if (subcategoryItemBean.isSelected()){
//            mPiBroadcastPlayingIndicator.setVisibility(View.VISIBLE);
            mTvBroadcastName.setSelected(true);
        }else {
//            mPiBroadcastPlayingIndicator.setVisibility(View.GONE);
            mTvBroadcastName.setSelected(false);
        }
    }
}
