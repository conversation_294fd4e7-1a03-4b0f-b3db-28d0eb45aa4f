package com.kaolafm.kradio.report.data;

import com.kaolafm.report.event.StartReportEvent;

import java.util.UUID;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-03-04 17:35
 ******************************************/
public class SceneBaseReportData extends StartReportEvent {
    /**
     * 场景推送session
     */
    private String cjsession = UUID.randomUUID().toString();

    /**
     * 场景内容code
     */
    private String cjcode;
    /**
     * 场景推送类型
     */
    private String cjtype;

    public String getCjsession() {
        return cjsession;
    }

    public void setCjsession(String cjsession) {
        this.cjsession = cjsession;
    }

    public String getCjcode() {
        return cjcode;
    }

    public void setCjcode(String cjcode) {
        this.cjcode = cjcode;
    }

    public String getCjtype() {
        return cjtype;
    }

    public void setCjtype(String cjtype) {
        this.cjtype = cjtype;
    }
}
