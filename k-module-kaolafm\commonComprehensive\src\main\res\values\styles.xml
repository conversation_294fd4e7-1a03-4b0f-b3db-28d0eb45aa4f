<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!-- Comprehensive Radio Base Theme. -->
    <style name="ComprehensiveBaseThemeCompat" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowNoTitle">true</item>//无标题
        <item name="android:windowActionBar">false</item>//无ActionBar
        <item name="android:windowFullscreen">@bool/isWindowFullScreen</item>//全屏即无通知栏
        <item name="android:windowContentOverlay">@null</item>//是否有遮盖

        <item name="android:windowDisablePreview">true</item>
        <!-- 沉浸式状态栏与导航栏 start-->
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
    </style>

    <!-- Comprehensive Radio Base Application Theme. -->
    <style name="ComprehensiveAppThemeCompat" parent="ComprehensiveBaseThemeCompat">
        <item name="android:windowBackground">@drawable/splash_bg_home</item>
        <item name="android:windowEnableSplitTouch">false</item>
        <item name="android:splitMotionEvents">false</item>
    </style>
    <style name="ComprehensiveAppThemeCompatTranslucent" parent="ComprehensiveAppThemeCompat">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Translucent</item>
    </style>

    <!-- Comprehensive Radio Splash Theme. -->
    <style name="ComprehensiveAppThemeCompat.Splash" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowBackground">@drawable/background_splash_comprehensive</item>
        <item name="android:windowActionBar">false</item>//无ActionBar
        <item name="android:windowFullscreen">@bool/isWindowFullScreen</item>//全屏即无通知栏
        <item name="android:windowContentOverlay">@null</item>//是否有遮盖
    </style>

    <style name="Comprehensive_FragmentBackButton_white" parent="FragmentBackButton_white">
        <item name="android:src">@drawable/comprehensive_player_ic_back</item>
    </style>

    <style name="ComprehensiveFragmentBackButton">
        <item name="android:id">@id/backView</item>
        <item name="android:layout_width">@dimen/m80</item>
        <item name="android:layout_height">@dimen/m80</item>
        <item name="android:layout_marginStart">@dimen/m48</item>
        <item name="android:layout_marginTop">@dimen/m33</item>
        <item name="android:padding">@dimen/m22</item>
        <item name="android:scaleType">centerInside</item>
        <item name="android:src">@drawable/base_back</item>
        <item name="android:contentDescription">@string/content_desc_back</item>
    </style>

    <style name="FullScreenDialogTheme" parent="BaseDialogTheme">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:background">@color/transparent</item>
    </style>
    <style name="NormalDialogTheme" parent="Theme.AppCompat.Dialog">
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
    </style>
    <style name="MessageBubbleAnimation">
        <item name="android:windowEnterAnimation">@anim/message_bubble_show</item>
        <item name="android:windowExitAnimation">@anim/message_bubble_dismiss</item>
    </style>
</resources>
