package com.kaolafm.kradio.flavor.impl;

import android.Manifest;

import com.kaolafm.kradio.lib.base.flavor.PermissionApplyInter;

public class PermissionApplyImpl implements PermissionApplyInter {

    @Override
    public String[] getPermission() {
        return new String[]{
                Manifest.permission.CHANGE_WIFI_STATE,
                Manifest.permission.WRITE_EXTERNAL_STORAGE,
                Manifest.permission.READ_EXTERNAL_STORAGE,
                Manifest.permission.READ_PHONE_STATE,
                Manifest.permission.RECORD_AUDIO,
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION,
        };
    }
}
