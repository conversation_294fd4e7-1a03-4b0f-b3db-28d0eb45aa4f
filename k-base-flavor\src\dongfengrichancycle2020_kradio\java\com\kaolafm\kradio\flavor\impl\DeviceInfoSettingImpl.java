package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.os.Build;
import android.provider.Settings;

import com.kaolafm.kradio.common.http.vehicle.KlSdkVehicle;
import com.kaolafm.kradio.flavor.utils.DeviceInfoUtil;
import com.kaolafm.kradio.lib.base.flavor.DeviceInfoSetting;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-03-22 11:46
 ******************************************/
public final class DeviceInfoSettingImpl implements DeviceInfoSetting {
    @Override
    public void setInfoForSDK(Context context) {
        String deviceId = Settings.Global.getString(context.getContentResolver(), "ivi.system.vehicle.daid");
        if (deviceId == null || deviceId.length() < 6) {
            DeviceInfoUtil.setDeviceIdAndCarType(deviceId, null);
        } else {
            DeviceInfoUtil.setDeviceIdAndCarType(deviceId, deviceId.substring(0, 6));
        }

    }
}
