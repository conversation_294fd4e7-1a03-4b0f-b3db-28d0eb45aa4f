package com.kaolafm.kradio.flavor.impl;

import android.util.Log;

import com.hsae.autosdk.voice.VoiceProxy;
import com.kaolafm.kradio.flavor.recorder.DongfengRichanRecorder;

import com.kaolafm.kradio.lib.base.flavor.KRadioAudioRecorderInter;
import com.kaolafm.kradio.lib.base.flavor.live.KradioRecorderInterface;


/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-07-05 12:16
 ******************************************/
public final class KRadioAudioRecorderImpl implements KRadioAudioRecorderInter {

    @Override
    public boolean initVR(Object... args) {
        return false;
    }

    @Override
    public boolean onAudioRecordStart(Object... args) {
        VoiceProxy.getInstance().stopVR();
        Log.i("DongfengRichanRecorder", "onAudioRecordStart: stopVR" );
        return true;
    }

    @Override
    public boolean onAudioRecordStop(Object... args) {
        VoiceProxy.getInstance().reStartWakeRecord();
        Log.i("DongfengRichanRecorder", "onAudioRecordStart: reStartWakeRecord" );
        return true;
    }

    @Override
    public boolean onAudioRecordStopAfter(Object... args) {
        return false;
    }

    @Override
    public void setVrStatusListener(OnVRStatusListener onVRStatusListener) {

    }

    @Override
    public KradioRecorderInterface getRecorder() {
        return new DongfengRichanRecorder();
    }

}
