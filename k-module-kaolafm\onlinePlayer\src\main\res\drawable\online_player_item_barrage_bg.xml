<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_selected="true">
        <layer-list>
            <item android:right="@dimen/x100">
                <shape>
                    <gradient android:angle="0" android:endColor="#00979797" android:startColor="#95E0FF" />
                    <corners android:bottomLeftRadius="@dimen/m22" android:topLeftRadius="@dimen/m22" />
                </shape>
            </item>
            <item android:bottom="@dimen/m2" android:left="@dimen/m2" android:top="@dimen/m2">
                <shape>
                    <gradient android:angle="0" android:endColor="#006890FF" android:startColor="#617DE1" />
                    <corners android:bottomLeftRadius="@dimen/m20" android:topLeftRadius="@dimen/m20" />
                </shape>
            </item>
        </layer-list>
    </item>
    <item android:drawable="@color/transparent" android:state_selected="false" />
    <item android:drawable="@color/transparent" />

</selector>