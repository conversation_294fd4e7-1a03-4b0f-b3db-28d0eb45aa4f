package com.kaolafm.kradio.demo;

import android.content.Context;
import android.graphics.Color;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.FrameLayout.LayoutParams;
import android.widget.TextView;
import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.base.ui.BaseActivity;
import com.kaolafm.kradio.lib.utils.ResUtil;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;

/**
 * 用于添加水印
 *
 * <AUTHOR>
 * @date 2019-06-25
 */
@Aspect
public class WatermarkAspect {

    @After("execution(* com.kaolafm.kradio.lib.base.ui.BaseActivity.initView(..))")
    public void addWatermark(JoinPoint point) throws Throwable {
        BaseActivity activity = (BaseActivity) point.getTarget();
        FrameLayout contentView = activity.findViewById(android.R.id.content);
        if (contentView != null) {
            contentView.addView(getWatermarkView(activity));
        }
    }

    private View getWatermarkView(Context context) {
        TextView textView = new TextView(context);
        FrameLayout.LayoutParams params = new LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT);
        params.gravity = Gravity.BOTTOM | Gravity.END;
        textView.setLayoutParams(params);
        textView.setBackgroundColor(Color.parseColor("#336c7190"));
        textView.setGravity(Gravity.CENTER);
        int dimen = ResUtil.getDimen(R.dimen.m16);
        textView.setPadding(dimen, dimen, dimen, dimen);
        textView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 22);
        textView.setTextColor(Color.parseColor("#b3000000"));
        textView.setText("K-radio测试专用版");
        textView.setClickable(false);
        return textView;
    }
}
