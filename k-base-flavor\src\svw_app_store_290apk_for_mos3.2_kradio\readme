上汽大众车机调试说明
1）车机的连接
step 1：
通过车机USB转网线连接电脑
step 2:
修改电脑IP为*************
step 3:
输入命令：ssh root@***********，密码为root，进入车机Linux侧
输入如下命令：
openFW.sh
iptables -F
iptables -P FORWARD ACCEPT
exit
此时，车机WLAN热点可连接。
车机WLAN热点名称为MY VW***，WLAN的密码为：dayxBE9VBiRs
step 4:
电脑连接上述WIFI后
adb connect ***********可连接车机Android系统，进行应用调试

2）关于SDK
必须使用大众提供的Android SDK进行开发，因为部分定制功能依赖了大众定制的接口
大众定制SDK下载地址：http://************/svn/Project/NEW1.项目管理/A-上汽大众-kradio apk-李瑞奇

需要用大众sdk里面的android.jar替换系统sdk里面的android.jar,否则编译出错

3）重签名命令
jarsigner -verbose -keystore edog-car-clinent-key.keystore -signedjar K-radio_shangqidazhong37w_release_207020007_resign.apk K-radio_shangqidazhong37w_release_207020007_out1.apk edog-car

因大众需要进行27版本编译
特在local.properties文件中进行标识 #local.sdkVersion=27