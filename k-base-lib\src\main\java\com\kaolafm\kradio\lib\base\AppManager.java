package com.kaolafm.kradio.lib.base;

import android.app.Activity;
import android.os.Handler;
import android.util.Log;

import com.kaolafm.opensdk.log.Logging;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;

/**
 * Activity管理类，使用linklist容器mActivityList类管理activity
 * <p>
 * Tips: mActivityList 容器中的顺序仅仅是 Activity 的创建顺序, 并不能保证和 Activity 任务栈顺序一致
 *
 * <AUTHOR>
 * @date 2018/4/13
 */

public class AppManager {

    private volatile static AppManager mInstance;

    private List<Activity> mActivityList;

    private Activity mCurrentActivity;

    private static final String TAG = "AppManager";

    private AppManager() {
        if (mActivityList == null) {
            mActivityList = new LinkedList<>();
        }
    }

    public static AppManager getInstance() {
        if (mInstance == null) {
            synchronized (AppManager.class) {
                if (mInstance == null) {
                    mInstance = new AppManager();
                }
            }
        }
        return mInstance;
    }

    public static final String IS_NOT_ADD_ACTIVITY_LIST = "is_not_add_activity_list";

    /**
     * 将activity添加到集合，统一管理。
     */
    public void addActivity(Activity activity) {
        synchronized (AppManager.class) {
            if (!mActivityList.contains(activity)) {
                mActivityList.add(activity);
            }
        }
    }

    /**
     * 将activity从集合中移除
     */
    public void removeActivity(Activity activity) {
        if (mActivityList == null) {
            return;
        }
        synchronized (AppManager.class) {
            if (mActivityList.contains(activity)) {
                mActivityList.remove(activity);
            }
        }
    }

    /**
     * 获取最近启动的activity，并不一定是可见的。
     */
    public Activity getTopActivity() {
        if (mActivityList == null) {
            return null;
        }
        return mActivityList.size() > 0 ? mActivityList.get(mActivityList.size() - 1) : null;
    }

    /**
     * 获取当前显示的activity，跟方法获取的activity是保证可见的，即生命周期处于onResume()和onStop()之间的。没有执行onStop之前的。
     */
    public Activity getCurrentActivity() {
        return mCurrentActivity;
    }

    /**
     * 设置当前显示的activity
     */
    public void setCurrentActivity(Activity activity) {
        mCurrentActivity = activity;
    }

    /**
     * 删除集合里的指定位置的 {@link Activity}
     */
    public Activity removeActivity(int location) {
        if (mActivityList == null) {
            return null;
        }
        synchronized (AppManager.class) {
            if (location > 0 && location < mActivityList.size()) {
                return mActivityList.remove(location);
            }
        }
        return null;
    }

    /**
     * 关闭指定的 {@link Activity} class 的所有的实例
     */
    public void killActivity(Class<?> activityClass) {
        if (mActivityList == null) {
            return;
        }
        for (Activity activity : mActivityList) {
            if (activity.getClass().equals(activityClass)) {
                activity.finish();
            }
        }
    }


    /**
     * 指定的 {@link Activity} 实例是否存活
     *
     * @param {@link Activity}
     */
    public boolean activityInstanceIsLive(Activity activity) {
        if (mActivityList == null) {
            return false;
        }
        return mActivityList.contains(activity);
    }


    /**
     * 指定的 {@link Activity} class 是否存活(同一个 {@link Activity} class 可能有多个实例)
     */
    public boolean activityClassIsLive(Class<?> activityClass) {
        if (mActivityList == null) {
            return false;
        }
        for (Activity activity : mActivityList) {
            if (activity.getClass().equals(activityClass)) {
                return true;
            }
        }
        return false;
    }


    /**
     * 获取指定 {@link Activity} class 的实例,没有则返回 null(同一个 {@link Activity} class 有多个实例,则返回最早创建的实例)
     */
    public Activity findActivity(Class<?> activityClass) {
        if (mActivityList == null) {
            return null;
        }
        for (Activity activity : mActivityList) {
            if (activity.getClass().equals(activityClass)) {
                return activity;
            }
        }
        return null;
    }

    /**
     * 返回一个存储所有未销毁的 {@link Activity} 的集合
     */
    public List<Activity> getActivityList() {
        if (mActivityList == null) {
            mActivityList = new LinkedList<>();
        }
        return mActivityList;
    }


    /**
     * 关闭所有 {@link Activity}
     */
    public void killAll() {
//        while (getActivityList().size() != 0) { //此方法只能兼容LinkedList
//            getActivityList().remove(0).finish();
//        }
        Log.i(TAG, "killAll");
        Iterator<Activity> iterator = getActivityList().iterator();
        while (iterator.hasNext()) {
            Activity next = iterator.next();
            Log.i(TAG, "killAll , "+next);
            iterator.remove();
            next.finish();
        }
    }

    /**
     * 关闭所有 {@link Activity},排除指定的 {@link Activity}
     *
     * @param excludeActivityClasses activity class
     */
    public void killAll(Class<?>... excludeActivityClasses) {
        List<Class<?>> excludeList = Arrays.asList(excludeActivityClasses);
        Iterator<Activity> iterator = getActivityList().iterator();
        while (iterator.hasNext()) {
            Activity next = iterator.next();

            if (excludeList.contains(next.getClass())) {
                continue;
            }

            iterator.remove();
            next.finish();
        }
    }

    /**
     * 关闭所有 {@link Activity},排除指定的 {@link Activity}
     *
     * @param excludeActivityName {@link Activity} 的完整全路径
     */
    public void killAll(String... excludeActivityName) {
        List<String> excludeList = Arrays.asList(excludeActivityName);
        Iterator<Activity> iterator = getActivityList().iterator();
        while (iterator.hasNext()) {
            Activity next = iterator.next();

            if (excludeList.contains(next.getClass().getName())) {
                continue;
            }

            iterator.remove();
            next.finish();
        }
    }

    /**
     * 退出应用程序
     */
    public void appExit() {
        try {
            Logging.i(TAG, "appExit");
            if (mExitListeners != null && !mExitListeners.isEmpty()) {
                for (ExitListener el : mExitListeners) {
                    el.onAppExit();
                }

                mExitListeners.clear();
                mExitListeners = null;
            }
            // 仅关闭所有 Activity，避免主动杀进程
            killAll();

            // 保留行为：如确需“真退出”，请改走显式受控路径（例如 AppExitHard.exit()），避免在生命周期回调中触发
            android.util.Log.i("AppManager", "appExit: finished all activities without killing process");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void appExitKillDelay() {
        try {
            Logging.i(TAG, "appExit");
            if (mExitListeners != null && !mExitListeners.isEmpty()) {
                for (ExitListener el : mExitListeners) {
                    el.onAppExit();
                }

                mExitListeners.clear();
                mExitListeners = null;
            }
            killAll();
            new Handler().postDelayed(() -> {
                android.util.Log.i("AppManager", "appExitKillDelay: finished all activities without killing process");
            }, 400);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    public void appExitNoKill() {
        try {
            Logging.i(TAG, "appExit");
            if (mExitListeners != null && !mExitListeners.isEmpty()) {
                for (ExitListener el : mExitListeners) {
                    el.onAppExit();
                }

                mExitListeners.clear();
                mExitListeners = null;
            }
            killAll();
//            android.os.Process.killProcess(android.os.Process.myPid());
//            System.exit(0);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public interface ExitListener {
        void onAppExit();
    }

    private List<ExitListener> mExitListeners;

    public void registerExitListener(ExitListener l) {
        if (mExitListeners == null) {
            mExitListeners = new ArrayList<>();
        }
        mExitListeners.add(l);
    }


    private WeakReference<Activity> mMainActivity;

    public void setMainActivity(Activity activity) {
        this.mMainActivity = new WeakReference<>(activity);
    }

    public Activity getMainActivity() {
        return mMainActivity == null ? null : mMainActivity.get();
    }

}
