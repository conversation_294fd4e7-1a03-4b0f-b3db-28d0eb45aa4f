package com.kaolafm.kradio.online.home.location;

import com.kaolafm.kradio.lib.base.mvp.IView;
import com.kaolafm.kradio.online.home.location.bean.OnlineRecomandCityBean;

import java.util.List;

public interface ILocationSelectView extends IView {

    void showNoNetView();
    void showNoResultView();
    void showLoadingView();
    void showAssociateWordsView(List<OnlineRecomandCityBean> resultBean);
    void showErrorView(int errorCode);
    void showHotSearchWordsView(List<OnlineRecomandCityBean> hotWords);
    void showNetworkError(String error, boolean clickToRetry);
}
