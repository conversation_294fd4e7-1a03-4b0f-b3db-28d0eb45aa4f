<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/message_details_bg">

    <RelativeLayout
        android:id="@+id/info_ll"
        android:layout_width="@dimen/m1050"
        android:layout_height="@dimen/m520"
        android:background="@drawable/msg_dialog_bg"
        tools:background="#9D000000"
        android:layout_centerInParent="true">

        <com.kaolafm.kradio.component.ui.base.view.OvalImageView
            android:id="@+id/bubbleBg"
            android:layout_width="@dimen/m1050"
            android:layout_height="@dimen/m520"
            android:scaleType="centerCrop"
            app:oval_radius="@dimen/m8" />

        <com.kaolafm.kradio.component.ui.base.view.OvalImageView
            android:id="@+id/dialog_right_half_bg"
            android:layout_width="@dimen/m520"
            android:layout_height="@dimen/m520"
            android:layout_alignParentEnd="true"
            android:scaleType="centerInside"
            app:oval_radius="@dimen/m16" />

        <com.kaolafm.kradio.component.ui.base.view.OvalImageView
            android:id="@+id/dialog_right_half_bg_mask"
            android:src="@drawable/dialog_right_half_bg_mask"
            android:layout_width="@dimen/m520"
            android:layout_height="@dimen/m520"
            android:layout_alignParentEnd="true"
            android:scaleType="centerCrop"
            app:rid_type="5"
            app:oval_radius="@dimen/m16"/>

<!--   内容主体     -->
        <LinearLayout
            android:id="@+id/front_ground_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="@dimen/m16"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/content_ll"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:paddingLeft="@dimen/m24"
                android:paddingRight="@dimen/m24"
                android:orientation="horizontal" >

                <LinearLayout
                    android:id="@+id/content_left_panel"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:layout_marginEnd="@dimen/m32"
                    android:orientation="vertical">

                    <LinearLayout
                        android:id="@+id/bubbleTitle_ll"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/m58"
                        android:gravity="center_vertical">

                        <TextView
                            android:id="@+id/bubbleTitle"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:maxLines="1"
                            android:textColor="@color/text_color_7"
                            android:textSize="@dimen/m32"
                            tools:text="路况交通路况交通路况交通路况交通路况交通路况交通路况交通路况交通" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/m26"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/activity_time_tip"
                            android:text="活动时间："
                            android:textSize="@dimen/m24"
                            android:textColor="@color/colorWhite"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content" />

                        <TextView
                            android:id="@+id/bubbleSubTitle"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/m10"
                            android:lineSpacingMultiplier="1.3"
                            android:textColor="@color/colorWhite"
                            android:textSize="@dimen/m24"
                            tools:text="2022.05.31 14:08" />
                    </LinearLayout>

                    <ScrollView
                        android:layout_marginTop="@dimen/m26"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginBottom="@dimen/m16"
                        android:scrollbarStyle="outsideOverlay"
                        android:scrollbarThumbVertical="@drawable/sl_sh_sb_thumb"
                        android:scrollbarTrackVertical="@drawable/sl_sh_sb_track"
                        android:scrollbars="vertical">

                        <TextView
                            android:id="@+id/bubbleContent"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:textColor="@color/text_color_7"
                            android:textSize="@dimen/m24"
                            tools:text="前方3公里正在进行道路施工车辆行驶公里正在进行道路施工车辆行驶公里正在进行道路施工车辆行驶缓慢，请谨慎驾驶谨慎驾驶谨慎驾驶重要事情说请谨慎驾驶谨慎驾驶谨慎驾驶重要事情说" />
                    </ScrollView>
                </LinearLayout>
                <RelativeLayout
                    android:id="@+id/content_lift_rl"
                    android:layout_width="@dimen/m224"
                    android:layout_height="match_parent"
                    android:visibility="visible"
                    tools:visibility="visible">

                    <View
                        android:layout_marginTop="@dimen/m91"
                        android:background="#7fffffff"
                        android:layout_width="@dimen/m1"
                        android:layout_height="@dimen/m257" />

                    <FrameLayout
                        android:layout_alignParentTop="true"
                        android:layout_alignParentStart="true"
                        android:id="@+id/video_pic_wrapper"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content">
                        <RelativeLayout
                            android:id="@+id/activity_video_rl"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/m33"
                            android:gravity="center"
                            android:layout_marginTop="@dimen/m93"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <RelativeLayout
                                android:id="@+id/viedo_iv_iv2_wrapper"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content" >
<!--                                <ImageView-->
<!--                                    android:id="@+id/viedo_iv"-->
<!--                                    android:layout_width="@dimen/m192"-->
<!--                                    android:layout_height="@dimen/m192"-->
<!--                                    android:layout_margin="@dimen/m8"-->
<!--                                    android:contentDescription="@string/content_desc_play"-->
<!--                                    android:scaleType="centerCrop" />-->


                                <com.kaolafm.kradio.component.ui.base.view.OvalImageView
                                    android:id="@+id/viedo_iv"
                                    android:layout_width="@dimen/m192"
                                    android:layout_height="@dimen/m192"
                                    android:contentDescription="@string/content_desc_play"
                                    android:scaleType="centerCrop"
                                    app:oval_radius="@dimen/m15"
                                    app:has_boder="true"
                                    app:color_edge="@color/colorWhite"
                                    app:width_edge="@dimen/m3"
                                    android:background="@drawable/message_qr_bg_frame"
                                    tools:src="@drawable/message_green_icon" />

                                <ImageView
                                    android:layout_centerInParent="true"
                                    android:id="@+id/viedo_iv2"
                                    android:layout_width="@dimen/m60"
                                    android:layout_height="@dimen/m60"
                                    android:src="@drawable/video_icon" />

                            </RelativeLayout>

                            <TextView
                                android:layout_centerHorizontal="true"
                                android:id="@+id/bubble_video_desc_tv"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_below="@id/viedo_iv_iv2_wrapper"
                                android:layout_marginTop="@dimen/m16"
                                android:text="点击查看视频"
                                android:textColor="#cccccc"
                                android:textSize="@dimen/m20" />
                        </RelativeLayout>

                        <RelativeLayout
                            android:id="@+id/bubble_pic_rl"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/m33"
                            android:gravity="center"
                            android:layout_marginTop="@dimen/m93"
                            android:orientation="vertical"
                            android:visibility="visible">

                            <com.kaolafm.kradio.component.ui.base.view.OvalImageView
                                android:contentDescription="@string/content_desc_message_detail_big_image"
                                android:id="@+id/bubble_pic_iv"
                                android:layout_width="@dimen/m192"
                                android:layout_height="@dimen/m192"
                                android:scaleType="centerInside"
                                app:oval_radius="@dimen/m15"
                                app:has_boder="true"
                                app:color_edge="@color/colorWhite"
                                app:width_edge="@dimen/m3"
                                android:background="@drawable/message_qr_bg_frame"
                                tools:src="@drawable/message_green_icon" />

                            <ImageView
                                android:id="@+id/bubble_pic_detils_iv"
                                android:layout_alignRight="@id/bubble_pic_iv"
                                android:layout_alignBottom="@id/bubble_pic_iv"
                                android:layout_width="@dimen/m30"
                                android:layout_height="@dimen/m30"
                                android:layout_marginRight="@dimen/m10"
                                android:layout_marginBottom="@dimen/m10"
                                android:scaleType="centerInside"
                                android:src="@drawable/msg_pic_icon" />

                            <TextView
                                android:layout_centerHorizontal="true"
                                android:id="@+id/bubble_qr_desc_tv"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_below="@id/bubble_pic_detils_iv"
                                android:layout_marginTop="@dimen/m16"
                                android:text="扫描二维码 参加活动"
                                android:textColor="#cccccc"
                                android:textSize="@dimen/m20" />
                        </RelativeLayout>
                    </FrameLayout>


                </RelativeLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/bubbleButtonParent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="@dimen/m20"
                android:layout_marginBottom="@dimen/m20"
                android:paddingLeft="@dimen/m24"
                android:paddingRight="@dimen/m24"
                android:gravity="center" />

        </LinearLayout>

<!--   重播图标     -->
        <LinearLayout
            android:layout_marginTop="@dimen/m34"
            android:layout_marginEnd="@dimen/m40"
            android:layout_alignParentEnd="true"
            android:layout_alignParentTop="true"
            android:id="@+id/replay_panel"
            android:background="@drawable/message_detail_replay_panel_bg"
            android:layout_width="@dimen/m124"
            android:enabled="false"
            android:gravity="center"
            android:layout_height="@dimen/m38" >
            <View
                android:enabled="false"
                android:id="@+id/replay_panel_state_icon"
                android:background="@drawable/message_detail_replay_state_icon"
                android:layout_width="@dimen/m24"
                android:layout_height="@dimen/m24" />

            <TextView
                android:layout_marginStart="@dimen/m6"
                android:id="@+id/play_iv"
                android:enabled="false"
                android:contentDescription="@string/message_bubble_replay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/message_replay_btn_text_color"
                android:textSize="@dimen/m20"
                android:text="播报" />
        </LinearLayout>
    </RelativeLayout>
<!--视频展开-->
    <RelativeLayout
        android:id="@+id/activity_play_video_big_rl"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone">
        <RelativeLayout
        android:layout_margin="@dimen/m8"
        android:id="@+id/video_view_rl"
        android:layout_width="@dimen/m1034"
        android:layout_height="@dimen/m504"
        android:layout_centerInParent="true"
        android:background="@color/colorBlack">
        <!-- 隐藏控件，用于所见即可说语音执行关闭操作 -->
        <TextView
            android:id="@+id/cd_close_video"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@color/transparent"
            android:contentDescription="@string/content_desc_activity_close_video"
            android:text="@string/content_desc_activity_close_video"
            android:textColor="@color/transparent"
            android:textSize="1sp"
            />
        <VideoView
            android:id="@+id/video_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignParentLeft="true"
            android:layout_alignParentTop="true"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true" />

        <ImageView
            android:id="@+id/viedo_replay"
            android:layout_width="@dimen/m100"
            android:layout_height="@dimen/m100"
            android:contentDescription="@string/content_desc_replay"
            android:layout_centerInParent="true"
            android:scaleType="centerInside"
            android:src="@drawable/video_icon"
            android:visibility="gone" />
    </RelativeLayout>
    </RelativeLayout>
<!--  查看大图  -->
    <RelativeLayout
        android:id="@+id/pic_big_rl"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/message_details_bg"
        android:visibility="gone">
        <!-- 隐藏控件，用于所见即可说语音执行关闭操作 -->
        <TextView
            android:id="@+id/cd_close_big_image"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@color/transparent"
            android:contentDescription="@string/content_desc_big_image_close"
            android:text="@string/content_desc_big_image_close"
            android:textColor="@color/transparent"
            android:textSize="1sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            />

        <ImageView
            android:id="@+id/pic_big_iv"
            android:scaleType="fitCenter"
            android:layout_width="@dimen/m760"
            android:layout_height="@dimen/m428"
            android:layout_centerInParent="true" />
    </RelativeLayout>

<!--  活动报名  -->
    <RelativeLayout
        android:id="@+id/activity_register_big_rl"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone">

        <RelativeLayout
            android:background="@drawable/msg_dialog_bg"
            android:id="@+id/ok_ll"
            android:layout_width="@dimen/m504"
            android:layout_height="@dimen/m504"
            android:layout_centerInParent="true">
            <LinearLayout
                android:layout_centerInParent="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/m35"
                android:layout_marginBottom="@dimen/m63"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="visible">

                <com.kaolafm.kradio.component.ui.base.view.OvalImageView
                    android:padding="@dimen/m16"
                    android:id="@+id/ok_qr_iv"
                    android:layout_width="@dimen/m280"
                    android:layout_height="@dimen/m280"
                    android:background="@drawable/message_qr_bg"
                    android:scaleType="centerInside"
                    tools:src="@drawable/message_green_icon" />

                <TextView
                    android:id="@+id/tv_qrcode_desc"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/m20"
                    android:gravity="center"
                    android:text="请使用手机扫码参与\n活动并查看详情"
                    android:textColor="@color/text_color_7"
                    android:textSize="@dimen/m24" />
            </LinearLayout>
        </RelativeLayout>

    </RelativeLayout>

    <include
        android:id="@+id/loading"
        layout="@layout/refresh_center"
        android:layout_width="@dimen/m1000"
        android:layout_height="@dimen/m365"
        android:layout_centerInParent="true"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 隐藏控件，用于所见即可说语音执行关闭操作 -->
    <TextView
        android:id="@+id/cd_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@color/transparent"
        android:contentDescription="@string/content_desc_close_dialog"
        android:text="@string/content_desc_close_dialog"
        android:textColor="@color/transparent"
        android:textSize="1sp"
        android:layout_alignStart="@id/info_ll"
        android:layout_alignTop="@id/info_ll"
        tools:ignore="SmallSp" />
</RelativeLayout>
