package com.kaolafm.launcher;

import static com.kaolafm.kradio.lib.common.ResType.LIVE_TYPE;
import static com.kaolafm.kradio.lib.common.ResType.VIDEO_ALBUM_TYPE;
import static com.kaolafm.kradio.lib.common.ResType.VIDEO_TYPE;
import static com.kaolafm.kradio.lib.utils.Constants.CLIENT_EXTRA_TYPE;
import static com.kaolafm.kradio.lib.utils.Constants.INVALID_NUM;
import static com.kaolafm.kradio.lib.utils.Constants.SEARCH_BY_KEYWORDS_EXTRA_TYPE;
import static com.kaolafm.kradio.lib.utils.Constants.START_PAGE;
import static com.kaolafm.kradio.lib.utils.Constants.USER_PROMPT_AGREE;
import static com.kaolafm.kradio.lib.utils.Constants.USER_PROMPT_AGREE_SP_FILE_NAME;
import static com.kaolafm.kradio.lib.utils.Constants.USER_PROMPT_FIRST_SP_FILE_NAME;
import static com.kaolafm.kradio.lib.utils.Constants.USER_PROMPT_FIRST_SP_ITEM_VALUE;
import static com.kaolafm.kradio.player.helper.intercept.HintVoiceChainIntercept.TYPE_ALBUM;
import static com.kaolafm.kradio.player.helper.intercept.HintVoiceChainIntercept.TYPE_AUDIO;
import static com.kaolafm.kradio.player.helper.intercept.HintVoiceChainIntercept.TYPE_VIP;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Dialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.ContextWrapper;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.ActivityInfo;
import android.content.res.Configuration;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.StrictMode;
import android.os.StrictMode.ThreadPolicy.Builder;
import android.os.StrictMode.VmPolicy;
import android.telephony.PhoneStateListener;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewStub;
import android.view.Window;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.core.app.ActivityCompat;
import androidx.fragment.app.DialogFragment;
import androidx.lifecycle.Lifecycle;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.google.gson.Gson;
import com.kaolafm.ad.AdConstant;
import com.kaolafm.ad.api.model.Advert;
import com.kaolafm.ad.api.model.AudioAdvert;
import com.kaolafm.ad.comprehensive.KradioAdAudioManager;
import com.kaolafm.ad.comprehensive.ads.image.AdvertisingImagerImpl;
import com.kaolafm.ad.comprehensive.base.ComprehensiveBaseAdInterceptorActivity;
import com.kaolafm.ad.comprehensive.control.KradioAdSceneConstants;
import com.kaolafm.ad.comprehensive.implement.AdvertPlayerImpl;
import com.kaolafm.ad.comprehensive.listener.IFlashScreenAdPlayerListener;
import com.kaolafm.ad.expose.AdvertisingImager;
import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.auto.home.HubActivity;
import com.kaolafm.base.utils.SpUtil;
import com.kaolafm.flavor.AccountInterworkInter;
import com.kaolafm.kradio.activity.comprehensive.ui.ActivitysDetailsDialogFragment;
import com.kaolafm.kradio.categories.AllCategoriesFragment;
import com.kaolafm.kradio.common.KradioSDKManager;
import com.kaolafm.kradio.common.base.BaseApplication;
import com.kaolafm.kradio.common.comprehensive.event.ChangeBadgeViewEvent;
import com.kaolafm.kradio.common.event.BroadcastPlayerChangedData;
import com.kaolafm.kradio.common.event.HideShowEBData;
import com.kaolafm.kradio.common.event.StopAudioEBData;
import com.kaolafm.kradio.common.router.IRouterConsumer;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.kradio.common.utils.CommonUtils;
import com.kaolafm.kradio.common.utils.DiskUtil;
import com.kaolafm.kradio.common.utils.PermissionUtils;
import com.kaolafm.kradio.common.utils.ThreadUtil;
import com.kaolafm.kradio.component.ComponentClient;
import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.config.ConfigSettingManager;
import com.kaolafm.kradio.constant.LiveComponentConst;
import com.kaolafm.kradio.constant.LoginProcessorConst;
import com.kaolafm.kradio.history.HistoryManager;
import com.kaolafm.kradio.history.utils.TingBanToKRadioUtil;
import com.kaolafm.kradio.home.comprehensive.ComperhensiveHomeDateFragment;
import com.kaolafm.kradio.home.comprehensive.HorizontalHomePlayerFragment;
import com.kaolafm.kradio.home.comprehensive.HorizontalPersonlityRecommendationFragment;
import com.kaolafm.kradio.home.comprehensive.LauncherPlayerSecondPlayerStateListenerWrapper;
import com.kaolafm.kradio.home.comprehensive.data.EventOrientationChangeData;
import com.kaolafm.kradio.home.comprehensive.gallery.PageJumper;
import com.kaolafm.kradio.home.comprehensive.playerbar.ComprehensivePlayerBar;
import com.kaolafm.kradio.home.comprehensive.playerbar.ComprehensivePlayerHelper;
import com.kaolafm.kradio.home.comprehensive.widget.HomeBackBar;
import com.kaolafm.kradio.home.comprehensive.widget.HomeBackBar.OnBackListener;
import com.kaolafm.kradio.home.utils.AppDateUtils;
import com.kaolafm.kradio.k_kaolafm.BuildConfig;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.base.arouter.ARouterBaseFragment;
import com.kaolafm.kradio.lib.base.flavor.ConfigChangeInter;
import com.kaolafm.kradio.lib.base.flavor.IActivationInter;
import com.kaolafm.kradio.lib.base.flavor.ICheckUpgraderInter;
import com.kaolafm.kradio.lib.base.flavor.InterestRecommentInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioAddPlayerListenerInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioAppTitleInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioPlayLogicInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioBackKeyInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioFullScreenInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioIsKaolaRunningInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioMediaKeyEventInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioOperateClickCellInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioSetupPlayerInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioStopAudioInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioThirdPlatformInitInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioUserPromptCtrlInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioWidgetUpdateInter;
import com.kaolafm.kradio.lib.base.flavor.ThirdPlatformLoginer;
import com.kaolafm.kradio.lib.base.ui.BaseShowHideFragment;
import com.kaolafm.kradio.lib.basedb.GreenDaoInterface;
import com.kaolafm.kradio.lib.basedb.manager.MessageDaoManager;
import com.kaolafm.kradio.lib.bean.CrashMessageBean;
import com.kaolafm.kradio.lib.bean.HistoryItem;
import com.kaolafm.kradio.lib.common.ModelConstant;
import com.kaolafm.kradio.lib.common.ResType;
import com.kaolafm.kradio.lib.dialog.Dialogs;
import com.kaolafm.kradio.lib.event.PopFragmentEBData;
import com.kaolafm.kradio.lib.init.ModelManager;
import com.kaolafm.kradio.lib.report.ReportUtil;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.ActivationUtils;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.ClientConnectControl;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.IntentUtils;
import com.kaolafm.kradio.lib.utils.KaolaAppConfigData;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.kradio.lib.utils.MultiUtil;
import com.kaolafm.kradio.lib.utils.NetworkMonitor;
import com.kaolafm.kradio.lib.utils.NetworkMonitor.OnNetworkStatusChangedListener;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.lib.utils.SharedPreferenceUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.YTDataCache;
import com.kaolafm.kradio.lib.utils.YTLogUtil;
import com.kaolafm.kradio.live.comprehensive.ui.ComprehensiveLiveActivity;
import com.kaolafm.kradio.live.player.HomeLiveManager;
import com.kaolafm.kradio.media.MediaSessionUtil;
import com.kaolafm.kradio.message.comprehensive.MessageBubbleDialogFragment;
import com.kaolafm.kradio.message.comprehensive.MessageDetailsDialogFragment;
import com.kaolafm.kradio.message.comprehensive.MessageRateLimit;
import com.kaolafm.kradio.message.comprehensive.MessageSocketHelper;
import com.kaolafm.kradio.mine.MineUtil;
import com.kaolafm.kradio.player.comprehensive.audio.RadioPlayerFragment;
import com.kaolafm.kradio.player.comprehensive.broadcast.BroadcastPlayerFragment;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.player.helper.intercept.HintInterceptManager;
import com.kaolafm.kradio.player.helper.intercept.PlayInterceptFactory;
import com.kaolafm.kradio.player.radiolive.LiveLifecycleListener;
import com.kaolafm.kradio.player.radiolive.LiveStateManager;
import com.kaolafm.kradio.player.radiolive.RadioLiveInfo;
import com.kaolafm.kradio.purchase.PayManager;
import com.kaolafm.kradio.purchase.constant.PayConst;
import com.kaolafm.kradio.purchase.model.PayResult;
import com.kaolafm.kradio.purchase.observer.AlbumPayListener;
import com.kaolafm.kradio.purchase.observer.AudiosPayListener;
import com.kaolafm.kradio.purchase.observer.VipPayListener;
import com.kaolafm.kradio.scene.launcher.event.LocationEvent;
import com.kaolafm.kradio.subscribe.SubscribeModel;
import com.kaolafm.kradio.user.BackUserFragment;
import com.kaolafm.kradio.user.LoginManager;
import com.kaolafm.kradio.user.UserInfoManager;
import com.kaolafm.kradio.user.comprehensive.ui.UserLoginActivity;
import com.kaolafm.kradio.user.comprehensive.ui.prompt.UserPromptFragment;
import com.kaolafm.kradio.user.ui.LauncherInter;
import com.kaolafm.message.utils.OnlineMessageUtils;
import com.kaolafm.opensdk.OpenSDK;
import com.kaolafm.opensdk.api.CrashMessageBaseBean;
import com.kaolafm.opensdk.api.CrashMessageButtonActionBean;
import com.kaolafm.opensdk.api.config.ConfigSettingOption;
import com.kaolafm.opensdk.api.config.IConfigSettingOptionListener;
import com.kaolafm.opensdk.api.purchase.model.PurchaseSucess;
import com.kaolafm.opensdk.crash.CrashPlayerHelper;
import com.kaolafm.opensdk.crash.Icrashstate;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.IGeneralListener;
import com.kaolafm.opensdk.player.logic.model.item.LiveStreamPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.UIThreadUtil;
import com.kaolafm.opensdk.socket.SocketManager;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonClickReportEvent;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.event.LoginReportEvent;
import com.kaolafm.report.util.ReportConstants;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.Single;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;
import io.reactivex.schedulers.Schedulers;
import me.yokeyword.fragmentation.ISupportFragment;
import me.yokeyword.fragmentation.SupportFragment;
import me.yokeyword.fragmentation.SupportHelper;

@Route(path = RouterConstance.MAIN_COMPREHENSIVE_URL)
public class LauncherActivity extends ComprehensiveBaseAdInterceptorActivity<LauncherPresenter>
        implements OnNetworkStatusChangedListener, IRouterConsumer {

    public static final String TAG = "LauncherActivity";
    private ConfigChangeInter configChangeInter;
    /**
     * 是否已经初始化播放器
     */
    private boolean isInitPlayerContext = false;

    HomeBackBar mHbrHomeBack;
    ComprehensivePlayerBar mPlayerBar;
    FrameLayout mFlLauncherRootContent;
    ImageView badgeView;
    /**
     * 娱乐fragment
     */
    ARouterBaseFragment fragmentKFm;

    /**
     * 个性推荐界面fragment
     */
    SupportFragment fragmentPersonalityRecommendation;

    SupportFragment mUserPromptFragment;

    private KRadioMediaKeyEventInter mKRadioMediaKeyEventInter;

    // TODO 此接口中resumeAudioPlayLogic接口独立出来可能更好后期可考虑完善一下
    private KRadioAudioPlayLogicInter mKRadioAudioPlayLogicInter;

    private LauncherInter mLauncherInter;

    /**
     * 是否在Activity销毁时停止音频播放 true 为是，false 为否
     */
    private boolean canStopAudio = true;

    private boolean canCheckAutoPlayOnResume = false;

    private Bundle savedInstanceStateOnCreate = null;

    static {
        AppCompatDelegate.setCompatVectorFromResourcesEnabled(true);
    }

    private IntentFilter intentFilter;
    private LocalBroadcastManager localBroadcastManager;
    private BroadcastReceiver localReceiver;

    private static final String SAVED_MEDIA_PAUSE = "is_media_pause";
    private static final String SAVED_CURRENT_TAB_INDEX = "current_tab_index";
    private boolean isMediaPause;
    private PlayItem mLastPlayItem;
    private AudiosPayListener mAudiosPayListener;
    private boolean needComeBack = false;
    //大众需要对audiomanger 做处理
    KRadioAddPlayerListenerInter kRadioAddPlayerListenerInter;
    LauncherPlayerSecondPlayerStateListenerWrapper playerSecondPlayerStateListenerWrapper;
    private PhoneStateListener phoneStateListener;
    private MessageBubbleDialogFragment dialogFragment;
    private boolean isFront = false;

    private int currentScreenOrientation;
    private Handler mainHandler;

    private static final int PERMISSION_REQUEST_CODE = 10000;

    private void addMsg(String json) {
        Log.d("插播json：", json);
        changeMessagePlay(json);
    }

    private final IPlayerInitCompleteListener mPlayerInitListener = b -> initPlayerContext();



    @SuppressLint("MissingSuperCall")
    @Override
    protected void onSaveInstanceState(Bundle outState) {
        isMediaPause = !PlayerManagerHelper.getInstance().isPlaying();
        outState.putBoolean(SAVED_MEDIA_PAUSE, isMediaPause);

        // 保存当前选中的tab index，解决多屏切换时状态丢失问题
        int currentTabIndex = YTDataCache.getSelectPage();
        outState.putInt(SAVED_CURRENT_TAB_INDEX, currentTabIndex);
        Log.i(TAG, "onSaveInstanceState: saving currentTabIndex = " + currentTabIndex);

        //todo 暂时对有问题的渠道进行处理，修复了再次进入app时socket数据加载不出来的问题
        super.onSaveInstanceState(outState);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        YTLogUtil.logStart(TAG, "onCreate", "start");

        if (!OpenSDK.getInstance().isActivate()) {
            KradioSDKManager.getInstance().initAndActivate();
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }

        // 闪屏广告
        mPresenter = new LauncherPresenter();
        mPresenter.init();
        initFlashScreenAdListener();

        getWindow().setBackgroundDrawable(ResUtil.getDrawable(R.drawable.bg_home));
        if (savedInstanceState != null) {
            isMediaPause = savedInstanceState.getBoolean(SAVED_MEDIA_PAUSE);

            // 恢复当前选中的tab index，解决多屏切换时状态丢失问题
            int savedTabIndex = savedInstanceState.getInt(SAVED_CURRENT_TAB_INDEX, -1);
            if (savedTabIndex >= 0) {
                YTDataCache.setSelectPage(savedTabIndex);
                Log.i(TAG, "onCreate: restored currentTabIndex = " + savedTabIndex);
            }

            // 优雅恢复：不再通过自我重启(CLEAR_TASK/CLEAR_TOP)来恢复，避免多显示清栈引发的杀进程
            // 仅记录日志并在当前实例内恢复所需状态
            YTLogUtil.logStart(TAG, "onCreate", "savedInstanceState!=null, skip self-relaunch to avoid clear-task-all");
        }
        ModelManager.getInstance().setModel(ModelConstant.MODEL_COMPREHENSIVE);
        currentScreenOrientation = getResources().getConfiguration().orientation;
        CommonUtils.getInstance().initGreyStyle(getWindow());
        getWindow().setBackgroundDrawable(null);
        savedInstanceStateOnCreate = savedInstanceState;
        RouterManager.getInstance().addRouterConsumer(this);
        //remove title bar  即隐藏标题栏
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        super.onCreate(savedInstanceState);
        if (SpUtil.getBoolean(Constants.SP_IS_CLICK_HUB_AGREE, false)){
            SpUtil.putBoolean(Constants.SP_IS_CLICK_HUB_AGREE, false);
            showNotice = false;
        }
        mainHandler = new Handler(Looper.getMainLooper());
        ((BaseApplication)getApplication()).addActivity(this);

        Intent intent = new Intent("com.kaolafm.client.ACTION_ONCREATE");
        sendBroadcast(intent);

        AppManager.getInstance().setMainActivity(this);

        ConfigSettingManager.getInstance().getConfigSetting(new IConfigSettingOptionListener() {
            @Override
            public void onGetSuccess(ConfigSettingOption configSettingOption) {
                if (configSettingOption.getIsSilent() != null) {
                    checkCodeUpdate();
                    Constants.switchGreyStyle = configSettingOption.getIsSilent() == 1;
                }
            }

            @Override
            public void onGetFailure(ApiException e) {

            }
        });

        // 从HubActivity挪过来的权限请求
        requestPermission();

        handleNetwork();

        mLauncherInter = ClazzImplUtil.getInter("LauncherImpl");
        if (mLauncherInter != null) {
            mLauncherInter.onCreate(this);
        }

        final KRadioFullScreenInter kRadioFullScreenInter = ClazzImplUtil.getInter("KRadioFullScreenImpl");
        if (kRadioFullScreenInter != null) {
            kRadioFullScreenInter.initFullScreen(this);
        }

        final KRadioSetupPlayerInter kRadioSetupPlayerInter = ClazzImplUtil.getInter("KRadioSetupPlayerImpl");
        if (kRadioSetupPlayerInter != null) {
            // 解决https://app.huoban.com/tables/2100000007530121/items/2300001344736559?userId=1229522问题
            kRadioSetupPlayerInter.setupPlayer();
        }
        mKRadioAudioPlayLogicInter = ClazzImplUtil.getInter("KRadioAudioPlayLogicImpl");

        if (BuildConfig.DEBUG) {
            StrictMode.setThreadPolicy(new Builder()
                    .detectDiskReads()
                    .detectDiskWrites()
                    .detectNetwork()
                    .detectCustomSlowCalls()
                    .penaltyLog()
                    .build());
            StrictMode.setVmPolicy(new VmPolicy.Builder()
                    .detectLeakedSqlLiteObjects()
                    .detectLeakedClosableObjects()
                    .detectActivityLeaks()
                    .detectLeakedRegistrationObjects()
                    .penaltyLog()
                    .build());
        }
        boolean isShowWaterMark = ResUtil.getBoolean(R.bool.isShowWaterMark);
        if (isShowWaterMark) {
            ViewStub waterMarkViewStub = findViewById(R.id.water_mark_viewStub);
            waterMarkViewStub.inflate();
        }
        mKRadioMediaKeyEventInter = ClazzImplUtil.getInter("KRadioMediaKeyEventImpl");

        KRadioIsKaolaRunningInter kRadioIsKaolaRunningInter = ClazzImplUtil.getInter("KRadioIsKaolaRunningImpl");
        if (kRadioIsKaolaRunningInter != null) {
            kRadioIsKaolaRunningInter.setIsKaolaRunning(true);
        }

        KRadioAppTitleInter kRadioAppTitleInter = ClazzImplUtil.getInter("KRadioAppTitleImpl");
        if (kRadioAppTitleInter != null) {
            ViewStub appTitleViewStub = findViewById(R.id.app_title_viewStub);
            kRadioAppTitleInter.initAppTitle(this, appTitleViewStub);
        }

        KRadioWidgetUpdateInter kRadioWidgetUpdateInter = ClazzImplUtil.getInter("KRadioWidgetUpdateImpl");
        if (kRadioWidgetUpdateInter != null) kRadioWidgetUpdateInter.updateWidget(this);
        AntiShake.setClickDelayTime(ResUtil.getInt(R.integer.click_interval_value));
        // 账号打通，三方应用查询车机账号信息
        AccountInterworkInter mAccountInterworkInter = ClazzImplUtil.getInter("AccountInterworkImpl");
        if (mAccountInterworkInter != null && mAccountInterworkInter.isOpenThirdPartyAccount()) {
            mAccountInterworkInter.getAccountBindState();
        }

        // 付费提示音
        initHintVoiceIntercept();

        initPlayerListener();

        startUploadLoc();

        handleOnLocationEventTimeout();

        onClickHandleFocus(null);
        YTLogUtil.logStart(TAG, "onCreate", "end");
    }

    private Disposable uploadLocJob;
    private void disposeUploadLocJob(){
        if(uploadLocJob != null && !uploadLocJob.isDisposed()){
            uploadLocJob.dispose();
        }
    }

    private void requestPermission() {
        boolean flag = SharedPreferenceUtil.getInstance(this).getBoolean(SharedPreferenceUtil.PERMISSION_REQUEST, false);
        Log.i(TAG, "requestPermission start flag = " + flag);
        if (flag) {
            appEntryCheck();
        } else {
            PermissionUtils mPermissionUtils = new PermissionUtils(this);
            ActivityCompat.requestPermissions(this, mPermissionUtils.getPerms(), PERMISSION_REQUEST_CODE);
            SharedPreferenceUtil.getInstance(this).putBoolean(SharedPreferenceUtil.PERMISSION_REQUEST, true);
        }
    }

    /**
     * 所有需要改变启动正常初始化流程的逻辑放在这里
     */
    private void appEntryCheck() {
        //空间不足判断
        if (!DiskUtil.checkFreeSpace(this)) {
            //弹出空间不足的提示
            DiskUtil.createDialog(this);
        }
    }


    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        Log.d(TAG, "onRequestPermissionsResult: " + Arrays.toString(grantResults));
    }

    private void startUploadLoc(){
        uploadLocJob = Observable.interval(10, TimeUnit.MINUTES)
                .subscribeOn(Schedulers.io())
                .doOnNext( it -> {
                    MessageSocketHelper.uploadLoc();
                })
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<Long>() {
                    @Override
                    public void accept(Long aLong) throws Exception {
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                    }
                });
    }

    /**
     * 首页广告
     *
     * @return
     */
    @Override
    protected boolean isNeedAdvertInterceptor() {
        return true;
    }

    /**
     * 首页广告
     * 拦截器用于图片广告只在首页和播放器页面显示
     *
     * @param advert
     * @return
     */
    @Override
    protected boolean isIgnoreAdvert(Advert advert) {
        return !getLifecycle().getCurrentState().isAtLeast(Lifecycle.State.RESUMED)
                || !(getTopFragment() instanceof HorizontalHomePlayerFragment
                || getTopFragment() instanceof RadioPlayerFragment
                || getTopFragment() instanceof BroadcastPlayerFragment
                || getTopFragment() instanceof ComperhensiveHomeDateFragment) &&
                advert.getSubtype() != KradioAdSceneConstants.SUB_TYPE_SPRAND_SCREEN;
    }

    private boolean isCodeUpdate;
    private boolean showNotice = true;

    /**
     * 验证是否有协议更新
     */
    private void checkCodeUpdate() {
        if (isCodeUpdate)
            return;
        ConfigSettingManager.getInstance().getConfigSetting(new IConfigSettingOptionListener() {
            @Override
            public void onGetSuccess(ConfigSettingOption configSettingOption) {
                int code = SpUtil.getInt(Constants.SP_POLICY_VERSION, -1);
                int code2 = SpUtil.getInt(Constants.SP_AGREEMENT_VERSION, -1);

                if (code > 0 && showNotice) {
                    if (code < configSettingOption.getPrivacyPolicyVersion() || code2 < configSettingOption.getAgreementVersion()) {
                        isCodeUpdate = true;
                        String contentMsg = "";
                        if (!TextUtils.isEmpty(configSettingOption.getPromptCopy())) {
                            contentMsg = configSettingOption.getPromptCopy();
                        }
                        DialogFragment dialogFragment = new Dialogs.Builder()
                                .setType(Dialogs.TYPE_2BTN_PERMISSION)
                                .setGravity(Gravity.CENTER)
                                .setLeftBtnText("继续使用")
                                .setRightBtnText("不同意并退出")
                                .setCanShowCheckBox(true)
                                .setOutCancel(false)
                                .setMessage(contentMsg)
                                .setOnNativeListener(dialog -> {
                                    dialog.dismiss();
                                    //不同意就退出
                                    ClientConnectControl.instance.notifyProtocolRejected();
                                    showNotice = false;
                                    AppManager.getInstance().appExit();
                                })
                                .setOnPositiveListener(dialog -> {
                                    //同意就进应用
                                    showNotice = false;
                                    SpUtil.putInt(Constants.SP_POLICY_VERSION, configSettingOption.getPrivacyPolicyVersion());
                                    SpUtil.putInt(Constants.SP_AGREEMENT_VERSION, configSettingOption.getAgreementVersion());
                                    ClientConnectControl.instance.notifyProtocolReceived();
                                    dialog.dismiss();
                                })
                                .create();
                        dialogFragment.show(getSupportFragmentManager(), "permission_setting");
                    }
                } else {
                    SpUtil.putInt(Constants.SP_POLICY_VERSION, configSettingOption.getPrivacyPolicyVersion());
                    SpUtil.putInt(Constants.SP_AGREEMENT_VERSION, configSettingOption.getAgreementVersion());
                }
            }

            @Override
            public void onGetFailure(ApiException e) {

            }
        });
    }

    @Override
    public boolean isLauncherActivity() {
        return true;
    }

    /**
     * 首页是否首次走onStart true为是，false为否
     */
    private boolean isFirstStart = true;

    private boolean isFirstResume = true;

    @Override
    protected void onStart() {
        Log.i(TAG, "onStart start");
        isFront = true;
        if (mLauncherInter != null) {
            mLauncherInter.onStart(this);
        }
        super.onStart();
        if (isFirstStart) {
            isFirstStart = false;
            return;
        }
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001478605104?userId=1229522问题
        if (mKRadioAudioPlayLogicInter != null) {
            mKRadioAudioPlayLogicInter.restartAudioPlayLogic(this);
        }
    }
    /*
     * 获取sdk过来的播放id与类型
     * */
    private void jumpByIntent(){
        Intent intent = getIntent();
        if(intent != null){
            Long id = intent.getLongExtra("id",0);
            int resType = intent.getIntExtra("resType",0);
            intent.removeExtra("id");
            intent.removeExtra("resType");
            Log.i(TAG,"=========id:"+id+"restype:"+resType);
            if(id != 0) {
                PayManager.getInstance().closePay();
                canCheckAutoPlayOnResume = false;
                jumpToPageByID(id, resType);
            }
        }
    }

    private void jumpToPageByID(Long id, int resType) {
        if(resType == LIVE_TYPE){
            PlayerManagerHelper.getInstance().start(String.valueOf(id),resType);
            PageJumper.getInstance().jumpToLivePageWithoutContext(id,LauncherActivity.this);
        }else if(resType == VIDEO_TYPE){
            if(PlayerManager.getInstance().getSpeedLimitState()){
                PlayerManagerHelper.getInstance().start(String.valueOf(id), ResType.VIDEO_TYPE, true);
            } else {
                Map params = new HashMap();
                params.put(Constants.ROUTER_PARAMS_KEY_RESOURCE_ID, id.toString());
                params.put(Constants.ROUTER_PARAMS_KEY_RESOURCE_TYPE, String.valueOf(ResType.VIDEO_TYPE));
                RouterManager.getInstance().navigateToPage(LauncherActivity.this, Constants.PAGE_ID_VIDEO, params);
            }
        }else if(resType == VIDEO_ALBUM_TYPE){
            PageJumper.getInstance().init(LauncherActivity.this,(HorizontalHomePlayerFragment)fragmentKFm);
            PageJumper.getInstance().jumpToVideoAlbumFragment(id.toString());
        }else {
            if (NetworkUtil.isNetworkAvailableWidthDefaultToast(LauncherActivity.this)) {
                HomeCell homeCell = new HomeCell();
                homeCell.playId = id;
                homeCell.resType = resType;
                ComprehensivePlayerHelper.play(homeCell);
            } else {
                Log.i(TAG, "clickToPlay no Network");
            }
        }
    }

    private void handleNetwork() {
        NetworkMonitor.getInstance(AppDelegate.getInstance().getContext()).registerNetworkStatusChangeListener(this);

        if (!NetworkUtil.isNetworkAvailable(getApplicationContext(), false)) {
            ToastUtil.showError(getApplicationContext(), R.string.no_net_work_str);
        }
    }

    @Override
    public boolean onKeyUp(int keyCode, KeyEvent event) {
        boolean flag = super.onKeyUp(keyCode, event);
        if (mKRadioMediaKeyEventInter != null) {
            flag = mKRadioMediaKeyEventInter.onKeyUp(keyCode, event);
        }
        return flag;
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        boolean flag = false;
        if (mKRadioMediaKeyEventInter != null) {
            flag = mKRadioMediaKeyEventInter.dispatchKeyEvent(event);
        }
        Log.i(TAG, "dispatchKeyEvent flag = " + flag);
        if (flag) {
            return flag;
        }
        return super.dispatchKeyEvent(event);
    }

    @Override
    protected void showAccordingToScreen(int orientation) {
        Log.e("orientation", orientation + "");
        Log.e("isPortrait", ScreenUtil.isPortrait() + "");
        super.showAccordingToScreen(orientation);
        setHomeBackVisibility(orientation);
    }

    private Disposable updateCrashMessageJob = null;
    private void updateCrashMessage(){
        disposeUpdateCrashMessageJob();
        updateCrashMessageJob = Observable.just(1)
                .subscribeOn(Schedulers.io())
                .doOnNext( it -> {
                    List<CrashMessageBean> messageBeanList = MessageDaoManager.getInstance().queryAllSync();
                    if (messageBeanList != null && messageBeanList.size() > 0) {
                        try {
                            for (CrashMessageBean messageBean : messageBeanList) {
                                if ((System.currentTimeMillis() - Long.parseLong(messageBean.getSendTime())) >= (1000 * 60 * 60 * 24 * 2)) {
                                    //如果消息超过24小时就要删除0
                                    MessageDaoManager.getInstance().deleteSync(messageBean.getMsgId());
                                    Log.d(TAG, "-----插播消息超过48小时删除----" + messageBean.getHeadline());
                                }
                            }
                        } catch (Exception e) {
                            Logger.e(TAG, e.getMessage());
                        }
                    }
                })
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(integer -> {
                    EventBus.getDefault().post(new ChangeBadgeViewEvent());
                }, throwable -> {
                });
    }

    private void disposeUpdateCrashMessageJob(){
        if(updateCrashMessageJob != null && !updateCrashMessageJob.isDisposed()){
            updateCrashMessageJob.dispose();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        Log.i(TAG, "onResume start");
        AppManager.getInstance().setMainActivity(this);

        updateCrashMessage();

        if (mLauncherInter != null) {
            mLauncherInter.onResume(this);
        }
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001579294377?userId=1545533问题
        if (mKRadioAudioPlayLogicInter != null) {
            mKRadioAudioPlayLogicInter.resumeAudioPlayLogic(this);
        }

        sendOrientationChangeMsg(ResUtil.getOrientation());
        IntentUtils.getInstance().notifyAppOnResume(this);
        if (isFirstResume) {
            isFirstResume = false;
        }

        if (canCheckAutoPlayOnResume) {
            checkOnNewIntentLogic(getIntent());
            canCheckAutoPlayOnResume = false;
        }
        if (configChangeInter == null) {
            configChangeInter = ClazzImplUtil
                    .getInter("KRadioConfigChangeImpl");
        }
        if (configChangeInter != null) {
            String theme = getCurrentThemeFromSettings();
            if (!android.text.TextUtils.isEmpty(theme)) {
                configChangeInter.saveConfiguration(theme);
            }
        }

        saveConfiguration(false);
        dealPageJump();
        refreshSpeedText();
    }

    @Override
    protected void onPause() {
        Log.i(TAG, "onPause start");
        if (mLauncherInter != null) {
            mLauncherInter.onPause(this);
        }
        super.onPause();
        IntentUtils.getInstance().notifyAppOnPause(this);
        ToastUtil.dismiss();
    }

    @Override
    public void onStatusChanged(int newStatus, int oldStatus) {
        Log.i(TAG, "onStatusChanged----->newStatus = " + newStatus + "--->isActivate = " + OpenSDK.getInstance().isActivate());
        if (newStatus == NetworkMonitor.STATUS_MOBILE || newStatus == NetworkMonitor.STATUS_WIFI) {
            String str = ResUtil.getString(R.string.network_has_linked);
            if (!TextUtils.isEmpty(str)) {
                ToastUtil.showInfo(getApplicationContext(), str);
            }
        } else if (newStatus == NetworkMonitor.STATUS_NO_NETWORK) {
            ToastUtil.showError(getApplicationContext(), R.string.no_net_work_str);
        }
    }


    @Override
    public void initView(Bundle savedInstanceState) {
        YTLogUtil.logStart(TAG, "initView", "");
        mHbrHomeBack = findViewById(R.id.hbr_home_back);
        mPlayerBar = findViewById(R.id.pb_home_play);
        mFlLauncherRootContent = findViewById(R.id.fl_launcher_root_content);
        badgeView = findViewById(R.id.bar_message_badge_view);
        speedLimitText = findViewById(R.id.video_speed);

        mHbrHomeBack.setOnBackListener(new OnBackListener() {
            @Override
            public void backToHome() {
                moveTaskToBack(true);
            }

            @Override
            public void exitApp() {
                finish();
            }
        });

        mPlayerBar.setPlayerBarHomeListener(v -> {
            Log.i(TAG, "mPlayerBar home onClick");
            onBackPressed();
        });

        attachPlayer();

        initModuleView();

        KRadioStopAudioInter mKRadioStopAudioInter = ClazzImplUtil
                .getInter("KRadioStopAudioImpl");
        if (mKRadioStopAudioInter != null) {
            canStopAudio = mKRadioStopAudioInter.isStopAudio(this);
        }
        jumpByIntent();
    }

    @Override
    public void initData() {
    }

    /**
     * 主要为了解决Monkey测试出现的空指针问题
     */
//    private void checkPresenterIsNull() {
//        if (mPresenter == null) {
//            mPresenter = createPresenter();
//        }
//    }

    private void attachPlayer() {
        YTLogUtil.logStart(TAG, "attachPlayer", "start");
        mPlayerBar.attachPlayer();
        mPlayerBar.attachSubscribeModel(new SubscribeModel());

//        checkPresenterIsNull();
//        if (PlayerManagerHelper.getInstance().isPlayerInitComplete()) {
//            mPresenter.onPlayerInitSuccess();
//        } else {
//            PlayerManagerHelper.getInstance().addPlayerInitCompleteListener(mPlayerInitListener);
//        }
    }

    @Deprecated
    public void onPlayerInitComplete(boolean isSuccess) {
//        YTLogUtil.logStart(TAG, "onPlayerInitComplete", "");
//        if (isSuccess) {
//            checkPresenterIsNull();
//            mPresenter.onPlayerInitSuccess();
//            if (mPresenter != null) {
//                mPresenter.onPlayerInitSuccess();
//                KRadioInitPlayerInter inter = ClazzImplUtil.getInter("KRadioInitPlayerImpl");
//                if (inter != null && inter.isInit() && KradioAdAudioManager.getInstance().getAdvertPlayerimpl() == null) {
//                    initPlayerContext();
//                }
//            }
//        }
    }

    private boolean hasPlayExtra(){
        boolean hasPlayExtra = false;
        Intent intent = getIntent();
        int extraType = INVALID_NUM;
        try {
            extraType = intent.getIntExtra(CLIENT_EXTRA_TYPE, INVALID_NUM);
        } catch (Exception e) {
        }
        if (extraType == SEARCH_BY_KEYWORDS_EXTRA_TYPE) {
            hasPlayExtra = true;
        }

        return hasPlayExtra;
    }

    private void doThirdPlatformLogin(){
        ThirdPlatformLoginer thirdPlatformLoginer = ClazzImplUtil.getInter("ThirdPlatformLoginerImpl");
        if (thirdPlatformLoginer != null) {
            thirdPlatformLoginer.login(AppDelegate.getInstance().getContext().getApplicationContext(),
                    new ThirdPlatformLoginer.Callback() {
                        @Override
                        public void onSuccess() {
                        }

                        @Override
                        public void onFailure() {
                        }
                    });
        }
    }

    public void initPlayerContext() {
        YTLogUtil.logStart(TAG, "initPlayerContext", "start");
        if (isInitPlayerContext) {
            return;
        }
        isInitPlayerContext = true;
        mPresenter.onPlayerInitSuccess();

        //此处逻辑跟120版本差距较大，后期需要合并120新增逻辑
        PlayItem playItem = PlayerManagerHelper.getInstance().getCurPlayItem();
        YTLogUtil.logStart(TAG, "initPlayerContext", "playItem = " + playItem);
        if (hasPlayExtra() || (playItem != null && playItem.getType() != PlayerConstants.RESOURCES_TYPE_INVALID)) {
            // 播放器 还在播放,启动考拉
            doThirdPlatformLogin();
            if (mKRadioAudioPlayLogicInter != null) {
                mKRadioAudioPlayLogicInter.doStartInPlay();
            }
        } else {
            // 正常的app 退出逻辑
            if (mKRadioAudioPlayLogicInter != null) {
                //播放指定渠道中的播放逻辑
                boolean status = mKRadioAudioPlayLogicInter.autoPlayAudio(AppDelegate.getInstance().getContext());
                if (!status) {
                    mPresenter.playNetOrLocal();
                } else {
                    doThirdPlatformLogin();
                }
            } else {
                mPresenter.playNetOrLocal();
            }

            ICheckUpgraderInter mICheckUpgraderInter = ClazzImplUtil.getInter("CheckUpgraderImpl");
            if (mICheckUpgraderInter != null) {
                mICheckUpgraderInter.checkUpgrade(false);
            }
        }
    }

    private void setHomeBackVisibility(int orientation) {
        if (!KaolaAppConfigData.getInstance().isShowMainBackHomeSwitch()) {
            return;
        }
        if (orientation == Configuration.ORIENTATION_PORTRAIT) {
            mHbrHomeBack.setVisibility(View.GONE);
        } else {
            mHbrHomeBack.setVisibility(View.VISIBLE);
        }
    }


    /**
     * 是否正在通话中
     *
     * @return
     */
    private boolean isCalling() {
        boolean b = false;
        TelephonyManager telephonyManager = (TelephonyManager) getSystemService(Context.TELEPHONY_SERVICE);
        if (TelephonyManager.CALL_STATE_OFFHOOK == telephonyManager.getCallState()
                || TelephonyManager.CALL_STATE_RINGING == telephonyManager.getCallState()) {
            //正在通话
            b = true;
            callingInMessage(telephonyManager);
        }
        return b;
    }

    /**
     * 来插播消息时，验证有没有在直播间并且在录音，有的话就要结束录音
     */
    private void isLiveForme() {
        if (AppManager.getInstance().getCurrentActivity() instanceof ComprehensiveLiveActivity &&
                HomeLiveManager.getInstance().isRecording()) {
            //如果当前的页面是直播间，并且正在录音就要取消录音
            ComprehensiveLiveActivity activity = (ComprehensiveLiveActivity) AppManager.getInstance().getCurrentActivity();
            activity.closeRecord();
        }
    }
    private void onCrashMessageArrived(String json) {
        if (Looper.getMainLooper().isCurrentThread()) {
            onCrashMessageArrivedInner(json);
        } else {
            mainHandler.post(new Runnable() {
                @Override
                public void run() {
                    onCrashMessageArrived(json);
                }
            });
        }
    }

    private void onCrashMessageArrivedInner(String json) {

        EventBus.getDefault().post(new ChangeBadgeViewEvent());

        ConfigSettingManager.getInstance().getConfigSetting(new IConfigSettingOptionListener() {
            @Override
            public void onGetSuccess(ConfigSettingOption config) {

                if(config == null){
                    insertMessageToDB(json);
                } else {
                    int range = config.getMsgTimeArrange();
                    int limit = config.getMsgLimit();

                    //只有在range > 0 且 limit >0 时（合法可理解的值）进行流量控制；
                    //其他的（不合法不可理解的值）都是直接插入不弹出（其他情况包括 range <= 0 或 limit <= 0 ）
                    if(range > 0 && limit > 0){
                        Context context = LauncherActivity.this;
                        MessageRateLimit.updateRateLimit(context, range, limit);

                        if(MessageRateLimit.getLeftTokenCount(context) > 0){
                            changeMessagePlay(json);
                        }  else {
                            //直接插入
                            insertMessageToDB(json);
                        }
                    } else {
                        //直接插入不弹出
                        insertMessageToDB(json);
                    }
                }
            }

            @Override
            public void onGetFailure(ApiException e) {

            }
        });
    }

    private void insertMessageToDB(String json){
        CrashMessageBean crashMessageBean = new Gson().fromJson(json, CrashMessageBean.class);
        MessageDaoManager.getInstance().save(crashMessageBean);
    }

    /**3级消息不关广告*/
    private void closeAd(CrashMessageBean bean){
        if("2".equals(bean.getPlayType())){
            return;
        }
        //插播进入，如果在播放节目切换广告，则关闭
        AudioAdvert audioAdvert = KradioAdAudioManager.getInstance().getAudioAdvert();
        if (audioAdvert != null &&
                (audioAdvert.getSubtype() == KradioAdSceneConstants.SUB_TYPE_SWITCH_PROGROM ||
                        audioAdvert.getSubtype() == AdConstant.TYPE_TIMED_ADVERT)) {
            AdvertisingManager.getInstance().close();
        }
    }

    /**
     * 处理接受插播消息的逻辑
     */
    private synchronized void changeMessagePlay(String json) {
        CrashMessageBean crashMessageBean = new Gson().fromJson(json, CrashMessageBean.class);
        MessageDaoManager.getInstance().save(crashMessageBean);

        closeAd(crashMessageBean);

        Log.i("CrashMessageChainInt","changeMessagePlay PlayType=" + (crashMessageBean == null ? "-10086" : crashMessageBean.getPlayType()));

        switch (crashMessageBean.getPlayType()) {
            case "0"://立即插播

                mPresenter.videoPlayerLogicWhenCrashMsgShow();
                if(StringUtil.isEmpty(crashMessageBean.getEventDescriptionPath())){
                    showMessageDialog(crashMessageBean);
                } else {
                    if (AppDateUtils.getInstance().isConfirmPlayMsg(crashMessageBean)) {
                        PlayerManager.getInstance().justStopTempTask(PlayInterceptFactory.TEMP_TASK_TYPE_CLOCK);
                        if (isCalling()) {
                            //正在通话
                            CrashPlayerHelper.getInstance()
                                    .addImmediatelyplayDate(new Gson().fromJson(new Gson().toJson(crashMessageBean), CrashMessageBaseBean.class));
                        } else {
                            if (CrashPlayerHelper.getInstance().isPlayCrash) {
                                CrashPlayerHelper.getInstance()
                                        .addImmediatelyplayDate(new Gson().fromJson(new Gson().toJson(crashMessageBean), CrashMessageBaseBean.class));
                            } else {
//                                isLiveForme();

                                CrashPlayerHelper.getInstance()
                                        .addImmediatelyplayDate(new Gson().fromJson(new Gson().toJson(crashMessageBean), CrashMessageBaseBean.class))
                                        .setIcrashstate(new Icrashstate() {
                                            @Override
                                            public void onCrashstate(int i) {

                                            }

                                            @Override
                                            public void onBufferingUpdate(CrashMessageBaseBean crashMessageBaseBean) {
                                                if (OnlineMessageUtils.getInstance().getShowMsgDetails() || crashMessageBaseBean == null) {
                                                    //如果是从消息详情点击的播放就不展示弹窗
                                                    return;
                                                }
                                                String json = new Gson().toJson(crashMessageBaseBean);
                                                CrashMessageBean crashMessageBean = new Gson().fromJson(json, CrashMessageBean.class);
                                                showMessageDialog(crashMessageBean);
                                            }

                                            @Override
                                            public void onPlayerFailed(PlayerFailedType playerFailedType) {

                                            }
                                        })
                                        .startPlay();
                            }
                        }
                    } else {
//                        isLiveForme();
                        //语音播报开关被关闭需要播放提示音
                        CrashPlayerHelper.getInstance()
                                .setIcrashstate(new Icrashstate() {
                                    @Override
                                    public void onCrashstate(int i) {

                                    }

                                    @Override
                                    public void onBufferingUpdate(CrashMessageBaseBean crashMessageBaseBean) {
//                                    if (crashMessageBaseBean == null) {
//                                        return;
//                                    }
//                                    String json = new Gson().toJson(crashMessageBaseBean);
//                                    CrashMessageBean crashMessageBean = new Gson().fromJson(json, CrashMessageBean.class);
//                                    showMessageDialog(crashMessageBean);
                                    }

                                    @Override
                                    public void onPlayerFailed(PlayerFailedType playerFailedType) {

                                    }
                                })
                                .playTips(getString(R.string.online_message_voice_url));
                        showMessageDialog(crashMessageBean);
                    }
                }


                break;
            case "1"://延时插播
                CrashPlayerHelper.getInstance()
                        .addPlayDate(new Gson().fromJson(new Gson().toJson(crashMessageBean), CrashMessageBaseBean.class));
                if (AppDateUtils.getInstance().isConfirmPlayMsg(crashMessageBean)) {
                    if (!isCalling()) {
                        //未在通话
                        if (!PlayerManagerHelper.getInstance().isPlaying()) {
                            //如果没有在播放中，并且插播未在播放中，就直接播放延时插播
                            if (!CrashPlayerHelper.getInstance().isPlayCrash) {
//                                isLiveForme();
                                CrashPlayerHelper.getInstance().startPlay();
                                showMessageDialog(crashMessageBean);
                            }
                        }
                    }
                } else {
                    //语音播报开关被关闭需要播放提示音
                    if (!PlayerManagerHelper.getInstance().isPlaying()) {
//                        isLiveForme();
                        CrashPlayerHelper.getInstance().playTips(getString(R.string.online_message_voice_url));
                        showMessageDialog(crashMessageBean);
                    }
                }

                break;
            case "2"://普通消息
                if (!PlayerManagerHelper.getInstance().isPlaying()) {
//                    if (AppDateUtils.getInstance().isConfirmPlayMsg(crashMessageBean)) {
                        CrashPlayerHelper.getInstance().playTips(getString(R.string.online_message_voice_url));

//                    }
                    showMessageDialog(crashMessageBean);
                }else {
                    CrashPlayerHelper.getInstance().addPlayDate(new Gson().fromJson(new Gson().toJson(crashMessageBean), CrashMessageBaseBean.class));
                }

//                isLiveForme();
//                showMessageDialog(crashMessageBean);
//                if ( !AppDateUtils.getInstance().isConfirmPlayMsg(crashMessageBean) ) {
                    //语音播报开关被关闭需要播放提示音
//                if (!PlayerManager.getInstance().isPlaying()) {
//                    isLiveForme();
//                    CrashPlayerHelper.getInstance().playTips(getString(R.string.online_message_voice_url));
////                    showMessageDialog(crashMessageBean);
//                }
//                }
                break;
        }

    }

    @Subscribe
    public void changeBadgeView(ChangeBadgeViewEvent event) {
        changeBadgeView();
    }

    @Subscribe
    public void onFirstDataLoadedEvent(FirstDataLoadedEvent event){
        GuideHelper.handleGuide(this);
    }
    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void onLocationEvent(LocationEvent event){
        MessageSocketHelper.handleMessageSocket(this, this::onCrashMessageArrived, this::showMessageDialog);
    }

    private Disposable locationEventTimeoutJob;
    private void handleOnLocationEventTimeout(){
        locationEventTimeoutJob = Single.just(1).delay(10, TimeUnit.SECONDS, AndroidSchedulers.mainThread()).subscribe(integer -> {
            MessageSocketHelper.handleMessageSocket(this, this::onCrashMessageArrived, this::showMessageDialog);
        }, throwable -> {

        });
    }
    private void disposeLocationEventTimeoutJob(){
        if(locationEventTimeoutJob != null && !locationEventTimeoutJob.isDisposed()){
            locationEventTimeoutJob.dispose();
        }
    }

    /**
     * 更新小红点
     */
    private void changeBadgeView() {
        badgeView.postDelayed(new Runnable() {
            @Override
            public void run() {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        MessageDaoManager.getInstance().queryMsgLook(new GreenDaoInterface.OnQueryListener<List<CrashMessageBean>>() {
                            @Override
                            public void onQuery(List<CrashMessageBean> crashMessageBeans) {
                                if (crashMessageBeans != null && crashMessageBeans.size() > 0) {
                                    badgeView.setImageResource(R.drawable.comprehensive_message_badge_view_red_point);
                                } else {
                                    badgeView.setImageResource(R.drawable.comprehensive_message_badge_view);
                                }
                            }
                        });
                    }
                });
            }
        }, 300);
    }

    /**
     * 电话状态监听
     */
    private void callingInMessage(TelephonyManager telephonyManager) {
        if (phoneStateListener == null) {
            phoneStateListener = new PhoneStateListener() {
                @Override
                public void onCallStateChanged(int state, String phoneNumber) {
                    super.onCallStateChanged(state, phoneNumber);
                    switch (state) {
                        case TelephonyManager.CALL_STATE_IDLE: {
                            Log.e(TAG, "CALL_STATE_IDLE");
                            if (CrashPlayerHelper.getInstance().isImmediatePlayDate()) {
                                CrashPlayerHelper.getInstance()
                                        .setIcrashstate(new Icrashstate() {
                                            @Override
                                            public void onCrashstate(int i) {

                                            }

                                            @Override
                                            public void onBufferingUpdate(CrashMessageBaseBean crashMessageBaseBean) {
                                                if (crashMessageBaseBean == null) {
                                                    return;
                                                }
                                                String json = new Gson().toJson(CrashPlayerHelper.getInstance().getCrashMessageBaseBean());
                                                Log.i("sendMessageSocket", "--------通话结束，插播播放-----" + json);
                                                CrashMessageBean crashMessageBean = new Gson().fromJson(json, CrashMessageBean.class);
                                                showMessageDialog(crashMessageBean);
                                            }

                                            @Override
                                            public void onPlayerFailed(PlayerFailedType playerFailedType) {

                                            }

                                        })
                                        .startPlay();

                            }
                            break;
                        }
                        case TelephonyManager.CALL_STATE_OFFHOOK: {
                            Log.e(TAG, "CALL_STATE_OFFHOOK");

                            break;
                        }
                        case TelephonyManager.CALL_STATE_RINGING: {
                            Log.e(TAG, "CALL_STATE_RINGING");
                            break;
                        }
                        default:
                            break;
                    }
                }
            };
            telephonyManager.listen(phoneStateListener, PhoneStateListener.LISTEN_CALL_STATE);
        }
    }


    /**
     * 显示消息弹窗
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void showMessageDialog(CrashMessageBean crashMessageBean) {
        Log.d("messageUI", "showMessageDialog");
        if (Looper.getMainLooper().isCurrentThread()) {
            showMessageDialogInner(crashMessageBean);
        } else {
            new Handler(Looper.getMainLooper()).post(new Runnable() {
                @Override
                public void run() {
                    showMessageDialogInner(crashMessageBean);
                }
            });
        }

    }

    private void showMessageDialogInner(CrashMessageBean crashMessageBean) {
        if (dialogFragment != null && dialogFragment.isShowing()) {
            Log.d("messageUI", "dialogFragment is not null");
            if (dialogFragment.getCrashMessageBean() != null
                    && dialogFragment.getCrashMessageBean().getMsgId().equals(crashMessageBean.getMsgId())) {
                Log.d("showMessageDialog", "-------------插播-----已经在展示当前的消息");
                return;
            } else {
                dismissDialog(dialogFragment);
                dialogFragment = null;
            }
        }
        if (AppManager.getInstance().getCurrentActivity() == null || crashMessageBean == null) {
            return;
        }

        createMessageBubbleDialogFragment(crashMessageBean);

    }

    private void dismissDialog(Dialog dialog){

        Context context = ((ContextWrapper) dialog.getContext()).getBaseContext();
        if(context instanceof Activity) {
            if(!((Activity)context).isFinishing() && !((Activity)context).isDestroyed()){
                dialog.dismiss();
                return;
            }
        }

        try {
            dialog.dismiss();
        } catch (Exception e){
            Log.d(TAG, "dismissDialog", e);
        }
    }
    /**
     * 创建消息泡泡弹窗Fragment
     */
    private void createMessageBubbleDialogFragment(CrashMessageBean crashMessageBean) {
        // 重置录音状态
        isLiveForme();
        dialogFragment = new MessageBubbleDialogFragment(AppManager.getInstance().getCurrentActivity())
                .setMDimAmount(0.5f)
                .setCrashMessageBean(crashMessageBean)
                .showDialogBg(isShowDialogBg())
                .setButtonsListener(new MessageBubbleDialogFragment.OnButtonClickListener() {
                    @Override
                    public void onButtonClick(MessageBubbleDialogFragment fragment, View v) {
                        int id = v.getId();
                        if (id == R.id.message_bubble_button_1_id) {

                            handleLeftButton(crashMessageBean);

                        } else if (id == R.id.message_bubble_button_2_id) {

                            handleRightButton(crashMessageBean);

                        } else if (id == R.id.message_bubble_button_3_id) {
                            Log.e(TAG, "点击按钮：3");
                        } else if (id == R.id.message_bubble_button_4_id) {
                            Log.e(TAG, "点击按钮：4");
                        }
                        //上报点击
                        ReportUtil.addMessageClike(Constants.PAGE_ID_MESSAGE_CARD, crashMessageBean.getMsgContentType(), crashMessageBean.getMsgId());
                    }
                }).setOnShowListener(new MessageBubbleDialogFragment.OnShowListener() {
                    @Override
                    public void onShow() {
                    }
                }).setOnDisMissListener(new MessageBubbleDialogFragment.OnDismissListener() {
                    @Override
                    public void onDisMiss() {
                    }
                });
        dialogFragment.show();
    }
    private void handleRightButton(CrashMessageBean bean){
        //关闭
        //数据上报
        ButtonClickReportEvent event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_MSG_CLOSE);
        event.setPage(Constants.PAGE_ID_MESSAGE_CARD);
        ReportHelper.getInstance().addEvent(event);
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_MESSAGE_BUBBLE_CLOSE, bean.getMsgDetailsBtnTextRight(), Constants.PAGE_ID_MESSAGE_CARD, ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_MESSAGE_BUBBLE));

        dialogFragment.dismiss();
        if (CrashPlayerHelper.getInstance().isPlay()) {
            CrashPlayerHelper.getInstance().playEnd();
        }

        CrashMessageButtonActionBean action = bean.getMsgBubbleBtnActionRight();
        if(action == null) {
            return;
        }

        MessageButtonActionHelper.handleButtonAction(this, action);
    }
    private void handleLeftButton(CrashMessageBean bean){
        //数据上报
        ButtonClickReportEvent event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_MSG_DETAILS_SHOW);
        event.setPage(Constants.PAGE_ID_MESSAGE_CARD);
        ReportHelper.getInstance().addEvent(event);
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_MESSAGE_BUBBLE_SEE_DETAILS, bean.getMsgDetailsBtnTextLeft(), Constants.PAGE_ID_MESSAGE_CARD, ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_MESSAGE_BUBBLE));

        dialogFragment.dismiss();

        CrashMessageButtonActionBean action = bean.getMsgBubbleBtnActionLeft();

        if(action == null){
            //查看详情
            showMessageBubbleDetailFragment(bean);
            return;
        }

        if (CrashPlayerHelper.getInstance().isPlay()) {
            CrashPlayerHelper.getInstance().playEnd();
        }
        MessageButtonActionHelper.handleButtonAction(this, action);
    }

    /**
     * 展示消息泡泡详情
     */
    private void showMessageBubbleDetailFragment(CrashMessageBean crashMessageBean) {
        MessageDetailsDialogFragment detailsDialogFragment
                = new MessageDetailsDialogFragment(AppManager.getInstance().getCurrentActivity());
        detailsDialogFragment.setCrashMessageBean(crashMessageBean)
                .show();
        MessageDaoManager.getInstance().updateLook(crashMessageBean.getMsgId());
    }

    /**
     * 判断是否显示消息泡泡为透明背景
     *
     * @return
     */
    private boolean isShowDialogBg() {
        return !isFront;
    }

    /**
     * 初始化模块view.
     */
    private void initModuleView() {
        //initNormalView();
        boolean activation = new ActivationUtils().isActivation(this);
        if (activation) {
            initUserPromptViewData(HideShowEBData.HIDE_RECOMMEND);
        } else {
            IActivationInter mIActivationInter = ClazzImplUtil.getInter("ActivationImpl");
            if (mIActivationInter != null) {
                initActivationView(mIActivationInter);
            } else {
                //设置为已经激活`
                new ActivationUtils().setActivation(getApplicationContext(), true);
                //
                HideShowEBData eventData = new HideShowEBData();
                eventData.setType(HideShowEBData.HIDE_ACTIVATE);
                hideShowView(eventData);
            }
        }
    }

    /**
     * 初始化 激活view
     */
    private void initActivationView(IActivationInter mIActivationInter) {
        Log.i(TAG, "initActivationView: mIActivationInter = " + mIActivationInter);
        if (mIActivationInter != null) {
            ARouterBaseFragment fragmentKActivation = (ARouterBaseFragment) mIActivationInter.getActivationFragment();
            loadRootFragment(R.id.fl_launcher_root_content, fragmentKActivation, false, false);
        }
    }

    @Override
    protected LauncherPresenter createPresenter() {
        return mPresenter;
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void setCanStopAudio(StopAudioEBData stopAudioEBData) {
        canStopAudio = stopAudioEBData.canStopAudio;
        Log.i(TAG, "setCanStopAudio---------->canStopAudio = " + canStopAudio);
    }

    @Override
    protected void onStop() {
        Log.i(TAG, "onStop start");
        if (mLauncherInter != null) {
            mLauncherInter.onStop(this);
        }
        HistoryManager.getInstance().saveBroadcastList(PlayerManagerHelper.getInstance().getBroadcastRadioSimpleItems());
        super.onStop();
        isFront = false;
    }

    @Override
    protected void onDestroy() {
        Log.i(TAG, "onDestroy start");
        PlayerManagerHelper.getInstance().removePlayerInitCompleteListener(mPlayerInitListener);

        disposeLocationEventTimeoutJob();
        disposeUpdateCrashMessageJob();
        disposeUploadLocJob();

        if (mPlayerBar != null) {
            mPlayerBar.detachPlayer();
            mPlayerBar.detachSubscribeModel();
        }
        try {
            long radioId = Long.parseLong(getRadioId());
            LiveStateManager.getInstance().unregisterLiveLifecycleListener(radioId, mLiveLifecycleListener);
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        //如果不加canStopAudio,会导致通过ClientSDK发送退出App但不停止播放命令,再次回到App中,点击电台不能播放
        if (canStopAudio) {
            SocketManager.getInstance().close();
            LiveStateManager.getInstance().exitRadio();
        }
        try {
            if (null != kRadioAddPlayerListenerInter) {
                kRadioAddPlayerListenerInter.removePlayerListener(this);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        destroyPlayer();
        PlayerManager.getInstance().removePlayControlStateCallback(playerSecondPlayerStateListenerWrapper);
        PlayerManager.getInstance().removeGeneralListener(generalListener);
        if (mLauncherInter != null) {
            mLauncherInter.onDestory(this);
        }
        NetworkMonitor.getInstance(AppDelegate.getInstance().getContext()).removeNetworkStatusChangeListener(this);
        AdvertisingManager.getInstance().close();
        AdvertisingImager advertisingImager = AdvertisingManager.getInstance().getImager();
        if (advertisingImager != null && advertisingImager instanceof AdvertisingImagerImpl) {
            ((AdvertisingImagerImpl) advertisingImager).destroyAdView();
        }
        ((BaseApplication)getApplication()).removeActivity(this);
        if (fragmentKFm != null) {
            fragmentKFm.onDestroyApp();
        }

        KRadioThirdPlatformInitInter kRadioThirdPlatformInitInter = ClazzImplUtil
                .getInter("KRadioThirdPlatformInitImpl");
        if (kRadioThirdPlatformInitInter != null) {
            kRadioThirdPlatformInitInter.destroyThirdPlatform(getApplicationContext());
        }
        AppManager.getInstance().setMainActivity(null);
        if (canStopAudio) {
            PlayerManagerHelper.getInstance().pause(false);
            Log.i(TAG, "Skip destroy() in onDestroy; rely on explicit AppManager.appExit for real exits");
            PlayerManagerHelper.getInstance().resetVariable();
        }
        super.onDestroy();
    }

    /**
     * 这里修改了fragmentation底层对onBackPressedSupport的实现，fragCount退出判断从1修改为0，这样更符合项目的场景
     */
    @Override
    public void onBackPressedSupport() {
        handlePopFrag();
    }

    private void handlePopFrag() {
        ARouterBaseFragment frag = (ARouterBaseFragment) SupportHelper
                .getBackStackTopFragment(getSupportFragmentManager());
        if (frag != null && frag.onBackPressedSupport()) {
            return;
        }
        Log.i(TAG, "handlePopFrag------>start");
        int fragCount = getSupportFragmentManager().getBackStackEntryCount();
        if (fragCount > 0) {
            pop();
        } else {
            KRadioBackKeyInter kRadioBackKeyInter = ClazzImplUtil.getInter("KRadioBackKeyImpl");
            Log.i(TAG, "handlePopFrag------>end kRadioBackKeyInter = " + kRadioBackKeyInter);
            if (kRadioBackKeyInter != null) {
                kRadioBackKeyInter.onBackPressed(this);
            } else {
                finish();
            }
        }
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_launcher;
    }

    @Override
    public int getLayoutId_Tow() {
        return 0;
    }


    @Override
    public boolean useEventBus() {
        return true;
    }


    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        if (ev.getAction() == MotionEvent.ACTION_DOWN) {
            View v = getCurrentFocus();
            if (isShouldHideKeyboard(v, ev)) {
                InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
                imm.hideSoftInputFromWindow(v.getWindowToken(), InputMethodManager.HIDE_NOT_ALWAYS);
            }
        }
        return super.dispatchTouchEvent(ev);
    }

    /**
     * 根据EditText所在坐标和用户点击的坐标相对比，来判断是否隐藏键盘
     */
    private boolean isShouldHideKeyboard(View v, MotionEvent event) {
        if ((v instanceof EditText)) {
            int[] l = {0, 0};
            v.getLocationInWindow(l);
            int left = l[0],
                    top = l[1],
                    bottom = top + v.getHeight(),
                    right = left + v.getWidth();
            return !(event.getX() > left && event.getX() < right
                    && event.getY() > top && event.getY() < bottom);
        }
        return false;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void hideShowView(HideShowEBData hideShowEBData) {
        if (hideShowEBData == null) {
            return;
        }
        initUserPromptViewData(hideShowEBData.getType());
    }

    private void initPage(int type) {
        if (type == HideShowEBData.HIDE_ACTIVATE) {
            boolean isShowInterestRecomment = false;
            InterestRecommentInter mInterestRecommentInter = (InterestRecommentInter) ClazzImplUtil
                    .getInter("InterestRecommentApplyImpl");
            if (mInterestRecommentInter != null) {
                isShowInterestRecomment = mInterestRecommentInter.isShowInterestRecomment();
            }
            if (isShowInterestRecomment) {
                initPersonalityRecommendationView();
            } else {
                initNormalView();
            }
        } else if (type == HideShowEBData.HIDE_RECOMMEND) {
            if (fragmentPersonalityRecommendation != null) {
                fragmentPersonalityRecommendation.onBackPressedSupport();
            }
            initNormalView();
        }
        getSupportFragmentManager().executePendingTransactions();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void popFragment(PopFragmentEBData popFragmentEBData) {
        handlePopFrag();
    }

    private void setReportLandOrPortrait(int orientation) {
        if (orientation == Configuration.ORIENTATION_PORTRAIT) {
            ReportHelper.getInstance().setLandOrPortrait(ReportConstants.ORIENTATION_PORTRAIT);
        } else {
            ReportHelper.getInstance().setLandOrPortrait(ReportConstants.ORIENTATION_LANDSCAPE);
        }
    }

    /**
     * 重启app
     */
    private void restartApp() {
        Logging.d("通过重启HubActivity来重启App");
        Context currentActivity = this;
        Intent intent = new Intent(currentActivity, HubActivity.class);
        Intent restartIntent = Intent.makeRestartActivityTask(intent.getComponent());
        currentActivity.startActivity(restartIntent);

        // 添加详细的退出日志
        android.util.Log.e("LauncherActivity", "=== 应用退出被触发 ===");
        android.util.Log.e("LauncherActivity", "方法: LauncherActivity.restartApp()");
        android.util.Log.e("LauncherActivity", "原因: 通过HubActivity重启应用");
        android.util.Log.e("LauncherActivity", "调用栈:", new Exception("退出调用栈"));
        android.util.Log.e("LauncherActivity", "=== 即将调用 System.exit(0) ===");

        System.exit(0);
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        AdvertisingImager advertisingImager = AdvertisingManager.getInstance().getImager();
        // 使用Settings中的主题，如果没有就不处理主题切换
        String settingsTheme = getCurrentThemeFromSettings();
        if (!android.text.TextUtils.isEmpty(settingsTheme)) {
            switchNightMode(settingsTheme);
        }
        Log.i(TAG, "onConfigurationChanged..." + newConfig);

        // 修复：强制刷新UI以适配新的屏幕配置
        // 当从主屏切换回副屏时，确保UI正确适配新的屏幕尺寸
        forceRefreshUIForConfigurationChange(newConfig);

        if (advertisingImager != null) {
            advertisingImager.skip(null);
        }
        sendOrientationChangeMsg(ResUtil.getOrientation());
        int newOrientation = getResources().getConfiguration().orientation;
        if (currentScreenOrientation != newOrientation){
            currentScreenOrientation = newOrientation;
            EventBus.getDefault().post(new EventOrientationChangeData());
        }
    }

    private void switchNightMode(String theme) {
        //使用换肤框架，放在flavor中处理
        if (configChangeInter == null) {
            configChangeInter = ClazzImplUtil
                    .getInter("KRadioConfigChangeImpl");
        }
        if (configChangeInter != null) {
            configChangeInter.onConfigChanged(theme);
        }
    }

    /**
     * 强制刷新UI以适配新的屏幕配置
     * 修复副主副屏切换时分辨率异常的问题
     */
    private void forceRefreshUIForConfigurationChange(Configuration newConfig) {
        try {
            Log.i(TAG, "forceRefreshUIForConfigurationChange: start, newConfig=" + newConfig);

            // 1. 强制刷新根视图，确保布局重新计算
            View rootView = findViewById(android.R.id.content);
            if (rootView != null) {
                rootView.post(() -> {
                    rootView.requestLayout();
                    rootView.invalidate();
                });
            }

            // 2. 通知所有Fragment重新适配屏幕
            if (fragmentKFm != null && fragmentKFm instanceof HorizontalHomePlayerFragment) {
                View fragmentView = fragmentKFm.getView();
                if (fragmentView != null) {
                    fragmentView.post(() -> {
                        // 强制Fragment重新执行showAccordingToScreen
                        fragmentView.requestLayout();
                    });
                }
            }

            // 3. 延迟执行，确保配置变化完全生效后再刷新
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                try {
                    // 强制重新获取屏幕尺寸相关的资源
                    getResources().getConfiguration();

                    // 通知EventBus，让所有监听者重新适配
                    EventBus.getDefault().post(new EventOrientationChangeData());

                    Log.i(TAG, "forceRefreshUIForConfigurationChange: completed");
                } catch (Exception e) {
                    Log.e(TAG, "forceRefreshUIForConfigurationChange: delayed refresh error", e);
                }
            }, 100); // 延迟100ms确保配置变化完全生效

        } catch (Exception e) {
            Log.e(TAG, "forceRefreshUIForConfigurationChange: error", e);
        }
    }


    @Override
    public void onMultiWindowModeChanged(boolean isInMultiWindowMode) {
        MultiUtil.isInMultiWindowMode = isInMultiWindowMode;
        super.onMultiWindowModeChanged(isInMultiWindowMode);
    }


    //  广播暂无广播资源，接受广播，并在此刷新播放条信息
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void playNextIsNotLiving(BroadcastPlayerChangedData broadcastPlayerChangedData) {
        //          判断当前播放器的状态刷新播放条数据，同时重置一下状态
        if (mPlayerBar == null) {
            return;
        }
        mPlayerBar.updateInfo(1, PlayerManagerHelper.getInstance().getCurPlayItem());
        resetPlayerBarState();
    }

    private void resetPlayerBarState() {
        PlayerManagerHelper playerManagerHelper = PlayerManagerHelper.getInstance();
        Log.i(TAG, "Player: isPlaying = " + playerManagerHelper.isPlaying());
        Log.i(TAG, "Player: isPlayingClock = " + playerManagerHelper.isPlayingClock());
        if (playerManagerHelper.isPlaying() && !playerManagerHelper.isPlayingClock()) {
            mPlayerBar.showPlayState();
        } else {
            mPlayerBar.showPauseState();
        }
    }

    private void checkOnNewIntentLogic(Intent intent) {
        // 解决http://redmine.itings.cn/issues/40774问题
        boolean autoPlay = IntentUtils.getInstance().isAutoPlay(intent) /*intent.getBooleanExtra("auto_play", false)*/;
        YTLogUtil.logStart("LauncherActivity", "checkOnNewIntentLogic", "autoPlay = " + autoPlay);
        if (autoPlay) {
            PlayerManagerHelper playerManagerHelper = PlayerManagerHelper.getInstance();

            if (!getkRadioOperateClickCellInter(intent)) {
                if (!playerManagerHelper.isPlaying()) {
                    playerManagerHelper.switchPlayerStatus(true);
                }
            }

        }
    }

    public boolean getkRadioOperateClickCellInter(Intent intent) {
        //亿咖通 kx11此处加入运营位点击操作
        KRadioOperateClickCellInter settingModelInter = ClazzImplUtil.getInter("KRadioOperateClickImpl");
        if (settingModelInter != null && settingModelInter.getIntentUri(intent) != null && settingModelInter.isOperate(intent)) {


            return true;
        }
        return false;
    }

    /**
     * 发送广播,通知toast横竖屏切换
     */
    private void sendOrientationChangeMsg(int orientation) {
        Log.i("kradio.flow", "onConfigurationChanged: orientation=" + orientation);
        Intent intent = new Intent();
        intent.setAction("kaolafm.action.onOrientationChanged");
        intent.putExtra("orientation", orientation);
        LocalBroadcastManager.getInstance(getApplicationContext()).sendBroadcast(intent);
    }

    private void initUserPromptView(int type) {
        mUserPromptFragment = UserPromptFragment.getInstance();
        ((UserPromptFragment) mUserPromptFragment).setAgreeListener(new UserPromptFragment.IUserAgreePrompt() {
            @Override
            public void agree() {
                SharedPreferenceUtil sharedPreferenceUtil = SharedPreferenceUtil
                        .getInstance(AppDelegate.getInstance().getContext(), USER_PROMPT_AGREE_SP_FILE_NAME);
                sharedPreferenceUtil.putBoolean(USER_PROMPT_AGREE, true);
                initPage(type);
                if (mUserPromptFragment != null) {
                    mUserPromptFragment.onBackPressedSupport();
                }
            }
        });
        loadRootFragment(R.id.fl_launcher_root_content, mUserPromptFragment, false, false);
    }

    private void initUserPromptViewData(int type) {
        SharedPreferenceUtil sharedPreferenceUtil = SharedPreferenceUtil
                .getInstance(AppDelegate.getInstance().getContext(), USER_PROMPT_FIRST_SP_FILE_NAME);
        boolean isFirst = sharedPreferenceUtil.getBoolean(USER_PROMPT_FIRST_SP_ITEM_VALUE, true);

        KRadioUserPromptCtrlInter kRadioUserPromptCtrlInter = ClazzImplUtil.getInter("KRadioUserPromptCtrlInterImpl");
        if (isFirst && null != kRadioUserPromptCtrlInter && kRadioUserPromptCtrlInter.showUserPrompt()) {
            initUserPromptView(type);
            return;
        }
        initPage(type);
    }

    /**
     * 首页
     */
    private void initNormalView() {
        mPresenter.reportAppStart(getIntent());
        List<HistoryItem> historyItems = TingBanToKRadioUtil.loadHistoryFromTingban();
        if (historyItems != null && !historyItems.isEmpty()) {
            TingBanToKRadioUtil.copyHistoryFromTingban(historyItems);
            if (NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
                TingBanToKRadioUtil.uploadHistoryOfTingbanToSever(historyItems);
                TingBanToKRadioUtil.clearHistroyOfTingban();
            }
        }
        if (null == savedInstanceStateOnCreate) {
            fragmentKFm = (ARouterBaseFragment) RouterManager.getInstance().getRouterFragment(RouterConstance.HOME_LIKE_COMPREHENSIVE_URL);
            loadRootFragment(R.id.fl_launcher_root_content, fragmentKFm, false, false);
        }
    }

    /**
     * 进入个性推荐界面
     */
    private void initPersonalityRecommendationView() {
        fragmentPersonalityRecommendation = HorizontalPersonlityRecommendationFragment.getInstance();
        loadRootFragment(R.id.fl_launcher_root_content, fragmentPersonalityRecommendation, false, false);
    }

    /**
     * 闪屏页广告
     */
    private void initFlashScreenAdListener() {
        AdvertPlayerImpl advertPlayer = KradioAdAudioManager.getInstance().getAdvertPlayerimpl();
        YTLogUtil.logStart(TAG, "initFlashScreenAdListener", "advertPlayer = " + advertPlayer);
        if (flashScreenAdPlayerListener != null && advertPlayer != null) {
            advertPlayer.setListener(flashScreenAdPlayerListener);
        } else {
            preparePlayerAction();
        }
    }

    private final IFlashScreenAdPlayerListener flashScreenAdPlayerListener = this::preparePlayerAction;

    private void preparePlayerAction() {
        YTLogUtil.logStart(TAG, "preparePlayerAction", "preparePlayerAction");
        if (!NetworkUtil.isNetworkAvailable(this, false)) {
            // TODO：Wangyi 如果没网直接不播了,可以优化
            return;
        }
        if (PlayerManagerHelper.getInstance().isPlayerInitComplete()){
            initPlayerContext();
        } else {
            PlayerManagerHelper.getInstance().addPlayerInitCompleteListener(mPlayerInitListener);
        }
    }


    private void initHintVoiceIntercept() {
        HintInterceptManager hintInterceptManager = HintInterceptManager.getInstance();
        hintInterceptManager.addOnCurrentPlayItemIntercept(new HintInterceptManager.OnCurrentPlayItemIntercept() {
            @Override
            public void getHintInterceptState(PlayItem playItem, int hintType, int buyType, int buyStatus, boolean isUserLogin) {
                Log.i(TAG, "getHintInterceptState :" + playItem.getBuyStatus());
                HistoryManager.getInstance().saveHistory(playItem, true);
                if (!isUserLogin && topFragmentIsLoginPage()) {
                    return;
                }
                if (!isUserLogin) {
//                    if (getTopFragment() instanceof BackUserFragment) {
//                        ((BackUserFragment) getTopFragment()).pop();
//                    }
                    switch (buyType) {
                        case TYPE_ALBUM://专辑购买
                            LoginManager.getInstance().setLoginInTo(LoginReportEvent.REMARKS1_ALUBM_PAY_BTN);
                            break;
                        case TYPE_AUDIO: //碎片购买
                            LoginManager.getInstance().setLoginInTo(LoginReportEvent.REMARKS1_PLAY_ITEM);
                            break;
                        case TYPE_VIP: //vip购买
                            LoginManager.getInstance().setLoginInTo(LoginReportEvent.REMARKS1_PLAY_VIP_BUTTON);
                            break;
                        default:
                            LoginManager.getInstance().setLoginInTo("");
                    }
                    PageJumper.getInstance().jumpToPlayerFragment(mPlayerBar);
                    PageJumper.getInstance().jumpToNewLoginPageAndComeBack(true);
                } else {
                    PageJumper.getInstance().jumpToPlayerFragment(mPlayerBar);
                    new Handler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            // 调用支付相关弹窗逻辑，并监听支付回调
                            switch (buyType) {
                                case TYPE_ALBUM: //专辑购买
                                    final boolean[] disposeAlbumPayResult = {false};
                                    PayManager.getInstance()
                                            .pay(PayConst.PAY_TYPE_ALBUM, playItem)
                                            .addPayListener(playItem, new AlbumPayListener() {
                                                @Override
                                                public void payResponse(PayResult payResult, PlayItem resultItem) {
                                                    hintInterceptManager.notifyPaySuccess();
                                                    if (playItem != resultItem) {
                                                        PayManager.getInstance().removeItemListeners(playItem);
                                                    }
                                                    //专辑购买需要刷新专辑列表，并播放当前条目
                                                    PlayerManagerHelper.getInstance().restart(playItem.getAlbumId(), playItem.getType());
                                                }
                                            });
                                    break;
                                case TYPE_AUDIO: //碎片购买
                                    if (mLastPlayItem != null && mAudiosPayListener != null) {
                                        PayManager.getInstance().removeItemSingleListener(mLastPlayItem, mAudiosPayListener);
                                    }
                                    mLastPlayItem = playItem;
                                    //每次都添加新的listener但旧的listener没有移除，会导致重复回调
                                    final boolean[] disposePayResult = {false};
                                    PayManager.getInstance()
                                            .pay(PayConst.PAY_TYPE_AUDIOS, playItem)
                                            .addPayListener(playItem, mAudiosPayListener = new AudiosPayListener() {
                                                @Override
                                                public void payResponse(PayResult payResult, PlayItem resultItem, String audioIds) {
                                                    if (!disposePayResult[0]) {
                                                        hintInterceptManager.notifyPaySuccess();
                                                        Integer status = payResult.getPurchaseSucess().getStatus();
                                                        if (status == PurchaseSucess.STATUS_SUCCESS) {
                                                            onAudioPayed(audioIds);
                                                        }
                                                        disposePayResult[0] = true; //更新为已执行的状态
                                                        PayManager.getInstance().removeItemSingleListener(playItem, this); //回调完成移除回调
                                                    }
                                                }
                                            });
                                    break;
                                case TYPE_VIP: //vip购买
                                    PayManager.getInstance()
                                            .pay(PayConst.PAY_TYPE_VIP, playItem)
                                            .addPayListener(null, new VipPayListener() {
                                                @Override
                                                public void payResponse(PayResult payResult, PlayItem resultItem, Long vipTime) {
                                                    Log.i(TAG, "VIP PAY SUCCESS !");
                                                    hintInterceptManager.notifyPaySuccess();
                                                    PlayerManagerHelper.getInstance().refreshPlayList();
                                                }
                                            });
                                    break;
                                default:
                                    break;
                            }
                        }
                    }, 1000);
                }
            }
        });
    }

    private boolean topFragmentIsLoginPage() {
//        boolean ret = false;
//        ISupportFragment topFragment = getTopFragment();
//        if (topFragment instanceof BackUserFragment) {
//            ret = ((BackUserFragment) topFragment).isUserLoginFragment();
//            Log.i(TAG, "loginPageIsShowing:" + ret);
//        }
        return AppManager.getInstance().getCurrentActivity() instanceof UserLoginActivity;
    }

    public void jumpToNewLoginPageAndComeBack(boolean needComeBack) {
        this.needComeBack = needComeBack;
        if (needComeBack) {
            jumpToNewLoginPage();
        }
    }

    private void jumpToNewLoginPage() {
        //fixed 由于添加了"已购"标题，"账号登陆的索引需要变成3"
        HintInterceptManager.getInstance();
        BackUserFragment backUserFragment = BackUserFragment.newInstance(3);
        if (needComeBack) {
            needComeBack = false;
            if (PlayerManagerHelper.getInstance().isActionFromUser()) {
                backUserFragment.setBackData(LoginProcessorConst.BACKTYPE_POP, Constants.PLAYER_ITEM_CLICK);
            } else {
                backUserFragment.setBackData(LoginProcessorConst.BACKTYPE_POP, Constants.PLAYER_ITEM_CAROUSEL);
            }
        }
        extraTransaction().start(backUserFragment);
//        loadRootFragment(R.id.fl_launcher_root_content, backUserFragment, false, false);
    }

    /**
     * 碎片支付成功后的播放处理
     *
     * @param audioIds
     */
    private void onAudioPayed(String audioIds) {// 支付成功后的播放处理
        Log.i(TAG, "onAudioPayed audioIds:" + audioIds);
        List<Long> idList = PlayerManagerHelper.getInstance().convertAudioIdsToItemIdsList(audioIds);
        if (idList != null && idList.size() > 0) {
            long firstPayedAudioId = idList.get(0);
            PlayerManager.getInstance().getPlayItemFromAudioId(firstPayedAudioId, new PlayerManager.GetPlayItemListener() {
                @Override
                public void success(PlayItem playItem) {
                    Log.i(TAG, "onAudioPayed save history for next play:" + playItem.getRadioName());
                    HistoryManager.getInstance().saveHistory(playItem, true);
                    PlayerManagerHelper.getInstance().restart(playItem.getAlbumId(), playItem.getType());
                }

                @Override
                public void error(ApiException e) {
                    e.printStackTrace();
                }
            });
        }
    }

    private void initPlayerListener() {

        /**
         * 全部/部分播放地址为空的处理
         */
        if (playerSecondPlayerStateListenerWrapper == null) {
            playerSecondPlayerStateListenerWrapper = new LauncherPlayerSecondPlayerStateListenerWrapper(this);
        }
        //大众需要对audiomanger 做处理
        kRadioAddPlayerListenerInter = ClazzImplUtil.getInter("KRadioAddPlayerListenerImpl");
        if (null != kRadioAddPlayerListenerInter) {
            kRadioAddPlayerListenerInter.initPlayerListener(this);
        }
        PlayerManager.getInstance().addPlayControlStateCallback(playerSecondPlayerStateListenerWrapper);
        //对专辑播单做的特殊处理:但播单为空时,提示toast.
//        if (playlistInfoListener == null) {
//            playlistInfoListener = new OnPlaylistInfoListener() {
//                @Override
//                public void onPlaylistReady(List<PlayItem> playlist, int index, boolean canAutoPlay) {
//
//                }
//
//                @Override
//                public void onPlaylistError(int error) {
//                    Log.e(TAG, "onPlaylistError: error=" + error);
//                    ToastUtil.showInfo(AppDelegate.getInstance().getContext(), R.string.is_not_online);
//                    mPlayerBar.showLoading(false);
//                }
//            };
//        }
//        PlayerRadioListManager.getInstance().setOnPlaylistInfoListener(playlistInfoListener);

        PlayerManager.getInstance().addGeneralListener(generalListener);
    }

    public ComprehensivePlayerBar getPlayerBar() {
        return mPlayerBar;
    }

    private IGeneralListener generalListener = new IGeneralListener() {

        @Override
        public void getPlayListError(PlayItem playItem, int code, int i1) {
            Logging.i(TAG, "getPlayListError:" + code);
            switch (code) {
//                case PlayerConstants.ERROR_CODE_NO_COPYRIGHT:// do not need 增加无版权提示
//                    UIThreadUtil.runUIThread(() -> ToastUtil.showError(LauncherActivity.this, R.string.no_copyright));
//                    break;
                case PlayerConstants.ERROR_CODE_PLAY_LIST_IS_LAST_ONE:
                    break;
                case PlayerConstants.ERROR_CODE_RADIO_COPYRIGHT_LITE:
                    UIThreadUtil.runUIThread(() -> ToastUtil.showError(LauncherActivity.this, R.string.comprehensive_radio_is_lite));
                    break;
                default:
                    if (playItem != null && playItem.getType() == PlayerConstants.RESOURCES_TYPE_LIVING)
                        return;
                    UIThreadUtil.runUIThread(() -> ToastUtil.showError(LauncherActivity.this, R.string.play_failed_str));
                    break;
            }
            if (isPlayBroadcastError()) {
                if (fragmentKFm != null && fragmentKFm instanceof HorizontalHomePlayerFragment) {
                    if (((HorizontalHomePlayerFragment) fragmentKFm).homeDateFragments != null
                            && ((HorizontalHomePlayerFragment) fragmentKFm).homeDateFragments.size() > 0) {
                        ((HorizontalHomePlayerFragment) fragmentKFm).setSelected();
                    }
                }
            }
        }

        @Override
        public void playUrlError(int code) {
            Logging.i(TAG, "playUrlError code = " + code);
            if (!NetworkUtil.isNetworkAvailable(LauncherActivity.this, false)) {
                UIThreadUtil.runUIThread(() -> ToastUtil.showError(LauncherActivity.this, com.kaolafm.kradio.lib.R.string.no_net_work_str));
                return;
            }
            //fixed 修复最后一条播放完毕后，提醒"资源不支持的问题"
            if (code != PlayerConstants.ERROR_CODE_PLAY_LIST_IS_LAST_ONE) {
                UIThreadUtil.runUIThread(() -> ToastUtil.showError(LauncherActivity.this, R.string.play_failed_str));
            }
        }
    };

    private LiveLifecycleListener mLiveLifecycleListener = new LiveLifecycleListener() {
        @Override
        public void onState(int state, RadioLiveInfo radioLiveInfo) {
            if (state == LiveStreamPlayItem.Living) {
                Bundle extend = getExtend();
                String action = extend.getString("operating");
                if (TextUtils.equals(action, "yes")) {
                    ISupportFragment topFragment = getTopFragment();
                    ComponentClient.obtainBuilder(LiveComponentConst.NAME)
                            .setActionName(LiveComponentConst.START_FRAGMENT)
                            .addParam("liveInfo", radioLiveInfo.getLiveDetails())
                            .addParam("context", topFragment)
                            .addParam("containerId", R.id.launcher_main_layout)
                            .build().callAsync();
                }
            }
        }
    };

    public void registerLiveLifecycleListener() {
        LiveStateManager.getInstance()
                .registerLiveLifecycleListener(Long.parseLong(getRadioId()), mLiveLifecycleListener);
    }

    public String getRadioId() {
        String radioId = Constants.BLANK_STR;
        PlayItem playItem = PlayerManagerHelper.getInstance().getCurPlayItem();
        if (playItem != null) {
            radioId = playItem.getRadioId();
        }
        return radioId;
    }

    public void setIsMediaPause(boolean flag) {
        isMediaPause = flag;
        setSelected();
    }

    /**
     * 设置播放异常
     */
    public void setPlayEnd() {
        if (fragmentKFm != null && fragmentKFm instanceof HorizontalHomePlayerFragment) {
            if (((HorizontalHomePlayerFragment) fragmentKFm).homeDateFragments != null
                    && ((HorizontalHomePlayerFragment) fragmentKFm).homeDateFragments.size() > 0) {
                //https://app.huoban.com/tables/2100000007530121/items/2300001503899968?userId=1874548
                ThreadUtil.runOnUI(() -> ((HorizontalHomePlayerFragment) fragmentKFm).setPlayEnd());
            }
        }
    }

    /**
     * 设置播放状态
     */
    public void setSelected() {
        if (fragmentKFm != null && fragmentKFm instanceof HorizontalHomePlayerFragment) {
            if (((HorizontalHomePlayerFragment) fragmentKFm).homeDateFragments != null
                    && ((HorizontalHomePlayerFragment) fragmentKFm).homeDateFragments.size() > 0) {
                //https://app.huoban.com/tables/2100000007530121/items/2300001503899968?userId=1874548
                ThreadUtil.runOnUI(() -> ((HorizontalHomePlayerFragment) fragmentKFm).setSelected());
            }
        }
    }

    private boolean isPlayBroadcastError() {
        PlayItem playItemTemp = PlayerManagerHelper.getInstance().getCurPlayItem();
        if (playItemTemp.getType() != PlayerConstants.RESOURCES_TYPE_INVALID) {
            return false;
        }
        if (PlayerManagerHelper.getInstance().getCurrentPlayType() != PlayerConstants.RESOURCES_TYPE_BROADCAST) {
            return false;
        }
        return true;
    }

    /**
     * 销毁播放器
     */
    private void destroyPlayer() {
        Log.i(TAG, "destroyPlayer: canStopAudio = " + canStopAudio);
        if (canStopAudio) {
            MediaSessionUtil.getInstance().unregisterMediaSession();
            MediaSessionUtil.getInstance().release();
        } else {
            PlayerManagerHelper.getInstance().resetVariable();
        }
    }

    private void dealPageJump() {
        Intent intent = getIntent();
        int page = -1;
        try {
            page = intent.getIntExtra(START_PAGE, -1);
        } catch (Exception e) {
        }
        Log.i(TAG, "dealPageJump-------->page = " + page);
        switch (page) {
            case Constants.START_PAGE_LOGIN:
                PageJumper.getInstance().jumpToLogin();
                break;
        }
    }

    // todo fix V3.0路演临时方案，丢失焦点不影响音频播放，测试通过后考虑把代码删除或按键隐藏
    // since 1103，逻辑改回最初
    private boolean isHandleLossFocus = false;
    public void onClickHandleFocus(View view) {
//        if (isHandleLossFocus) {
//            PlayerManager.getInstance().setHandleLossFocus(false);
//            ToastUtil.showInfo(this, "当音频焦点丢失时，音频播放不受影响");
//            isHandleLossFocus = false;
//        } else {
//            PlayerManager.getInstance().setHandleLossFocus(true);
//            ToastUtil.showInfo(this, "当音频焦点丢失时，音频会暂停播放");
//            isHandleLossFocus = true;
//        }
//        PlayerManager.getInstance().setHandleLossFocus(true);
    }

    private TextView speedLimitText;
    public void onSpeedChange(View view) {
        if (PlayerManager.getInstance().getSpeedLimitState()) {
            speedLimitText.setText("未超过限速");
            PlayerManager.getInstance().setSpeedLimitState(false);
        } else {
            speedLimitText.setText("超过限速");
            PlayerManager.getInstance().setSpeedLimitState(true);
        }
    }
    private void refreshSpeedText(){
        if (PlayerManager.getInstance().getSpeedLimitState()) {
            speedLimitText.setText("超过限速");
        } else {
            speedLimitText.setText("未超过限速");
        }
    }

    public void onShowGuide(View view){
        GuideHelper.setGuideShowed(this, false);
        GuideHelper.handleGuide(this);
    }
    /**
     * 测试消息泡泡
     *
     * @param view
     */
    public void onMsgTest(View view) {
        Log.i("CrashMessageChainInt","onMsgTest");
        if (false){
            if (getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT){
                setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
            } else {
                setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
            }
            return;
        }
        String json = "{" +
                "\"eventDescription\":\"3个月云听VIP会员权益免费送啦！云听VIP会员权益免费送啦！您在8月4日至8月5日，微信扫码即可参加活动。畅听有声好书，出行轻松陪伴！\"" +
                ",\"eventDescriptionExtract\":\"3个月云听VIP会员权益免费送啦！3个月云听VIP会员权益免费送啦！上汽大众ID.车主，您在8月4日至8月5日，微信扫码即可参加活动。\"" +
                ",\"eventDescriptionPath\":\"https://image.kaolafm.net/mz/audios/202206/2ce8192b-f795-40e9-8e72-3d7778fe05de.mp3\"" +
                ",\"headline\":\"埃里克森点击阿斯利康点击\"" +
                ",\"headlinePath\":\"https://image.kaolafm.net/mz/audios/202206/987efb5c-9f7c-4289-b242-1a21d1b2cdc3.mp3\"" +
                ",\"msgContentType\":\"0\"" +
//                ",\"cardBgUrl\":\"http://img.kaolafm.net/mz/images/202302/0d0e9d01-e7c7-4f01-8837-66b8e01f8fe9/default.png\"" +
                ",\"cardBgUrl\":\"https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/8797abaf41e54e9caa2425f8f4633de9_mergeImage.png\"" +

//                ",\"msgTipsPicUrl\":\"http://img.kaolafm.net/mz/images/202210/1b60b1bb-d3d3-4c0c-ae54-8ce27c9db8b7/default.png\"" +

                ",\"msgPicUrl\":\"http://img.kaolafm.net/mz/images/202210/1b60b1bb-d3d3-4c0c-ae54-8ce27c9db8b7/default.png\"" +

                ",\"msgDetailsBgUrl\":\"https://beijingoptbbs.oss-cn-beijing.aliyuncs.com/zh/521637-dc87db9415ebc70735c8f2ff9b66700e.jpg\"" +
                ",\"msgDetailsBtbStyleLeft\":1" +
                ",\"msgDetailsBtbStyleRight\":1" +
                ",\"msgDetailsBtnTextLeft\":\"查看\"" +
                ",\"msgDetailsBtnTextRight\":\"关闭\"" +
                ",\"msgBubbleBtnTextLeft\":\"查看\"" +
                ",\"msgBubbleBtnTextRight\":\"关闭\"" +
                ",\"msgDetailsEndTime\":\"2022-10-26 14:58:35\"" +
                ",\"msgBubbleBtnActionLeft\":{\"actionType\":2,\"resId\":\"88\",\"resType\":0,\"url\":\"http://www.baidu.com\"}" +
//                ",\"msgBubbleBtnActionLeft\":{\"actionType\":3,\"resId\":\"88\",\"resType\":0,\"url\":\"\"}" +
//                ",\"msgBubbleBtnActionLeft\":{\"actionType\":1,\"resId\":\"1700000000084\",\"resType\":12,\"url\":\"\"}" +
//                ",\"msgBubbleBtnActionLeft\":{\"actionType\":1,\"resId\":\"1600000000459\",\"resType\":11,\"url\":\"\"}" +
                ",\"msgBubbleBtnActionLeft\":{\"actionType\":1,\"resId\":\"2333\",\"resType\":5,\"url\":\"\"}" +
//                ",\"msgBubbleBtnActionRight\":{\"actionType\":1,\"resId\":\"1600000000459\",\"resType\":11,\"url\":\"\"}" +
                ",\"msgDetailsBtnActionLeft\":{\"actionType\":2,\"resId\":\"2\",\"resType\":2,\"url\":\"\"}" +
                ",\"msgDetailsBtnActionRight\":{\"actionType\":3,\"resId\":\"3\",\"resType\":3,\"url\":\"\"}" +

                ",\"typeData\":{\"emergencyId\":\"1111111111111111122\",\"eventType\":\"1\",\"eventLevel\":\"1\"}" +

                ",\"msgDetailsQrUrl\":\"http://img.kaolafm.net/mz/images/202209/61bfd9d7-4a93-4b6f-b24f-15dd52e84b65/default.png\"" +

                ",\"msgDetailsPicTips\":\"扫二维码参加活动\"" +
                ",\"msgDetailsStartTime\":\"2022-08-26 14:58:35\"" +
                ",\"msgId\":\"" + System.currentTimeMillis()+"\""+
                ",\"msgLevel\":\"1\"" +
                ",\"msgStyleType\":2" +
                ",\"msgType\":\"02\"" +
                ",\"msgTime\":\"2023-09-18 14:58:35\"" +
                ",\"playType\":\"0\"" +
                ",\"publishTime\":\"1695048435739\"" +
                ",\"sendTime\":\"" + System.currentTimeMillis()+"\""+
                ",\"msgIconUrl\":\"http://img.kaolafm.net/mz/images/202209/61bfd9d7-4a93-4b6f-b24f-15dd52e84b65/default.png\"" +
                ",\"sender\":\"正安县气象台\"" +
                ",\"tipsTitle\":\"我是一个特别长的标题试试能不能显示呢全\"}";
//        https://img.zcool.cn/community/01c7565e5a7977a801216518113c7e.jpg@1280w_1l_2o_100sh.jpg
//        addMsg(json);
//                addMsg(json2);

//        testButtonActionO2O(json);

        badgeView.postDelayed(new Runnable() {
            @Override
            public void run() {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        addMsg(json);
                    }
                });
            }
        }, 1000);
    }

    private void testButtonActionO2O(String json){
        Gson gson = new Gson();

        CrashMessageBean crashMessageBean = gson.fromJson(json, CrashMessageBean.class);

        CrashMessageBaseBean baseBean = gson.fromJson(gson.toJson(crashMessageBean), CrashMessageBaseBean.class);

        MessageDaoManager.getInstance().save(crashMessageBean);
        List<CrashMessageBean> list = MessageDaoManager.getInstance().queryAllSync();

        for(int i = 0; i < list.size(); i++){
            CrashMessageBean bean = list.get(i);
            Log.d("GEENDAO", bean.toString());
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);
        Log.i(TAG, "onNewIntent: ");
        canCheckAutoPlayOnResume = true;
        jumpByIntent();
        interceptApplicationJumpEvent(intent);
    }

    private void interceptApplicationJumpEvent(Intent intent) {
        if (intent == null) intent = getIntent();
        String pageId = intent.getStringExtra(Constants.ROUTER_PARAMS_KEY_PAGE_ID);
        String date = intent.getStringExtra(Constants.ROUTER_PARAMS_KEY_EXTRA);
        if (StringUtil.isEmpty(pageId)) return;
        if (PlayerManagerHelper.getInstance().isAudioAdPlayLockOver()) {
            //如果在广告的锁定期就不响应跳转
            return;
        }

        if (StringUtil.isNotEmpty(pageId)) {
            consumeRoute(pageId, date);
        }
    }

    @Override
    public String consumeRoute(String pageId, Object extra) {
        Log.i(TAG, "consumeRoute() pageId=" + pageId + ", extra=" + extra);
        if (fragmentKFm == null) {
            fragmentKFm = (ARouterBaseFragment) RouterManager.getInstance().getRouterFragment(RouterConstance.HOME_LIKE_COMPREHENSIVE_URL);
        }
        switch (pageId) {
            case Constants.PAGE_ID_CATEGORY://综合版-分类
                Log.i(TAG, "consumeRoute() Constants.PAGE_ID_CATEGORY 综合版-分类");
                long fatherCode;// 指定一级Tab
                long sonCode;// 指定二级Tab
                if (extra != null && !TextUtils.isEmpty(extra.toString())) {
                    if (extra.toString().contains(Constants.ROUTER_PARAMS_VALUE_SPLIT)){
                        try {
                            String[] valueCodes = extra.toString().split(Constants.ROUTER_PARAMS_VALUE_SPLIT);
                            if (valueCodes.length > 1){
                                fatherCode = Long.parseLong(valueCodes[0]);
                                sonCode = Long.parseLong(valueCodes[1]);
                            } else if (valueCodes.length > 0){
                                fatherCode = Long.parseLong(valueCodes[0]);
                                sonCode = 0;
                            } else {
                                fatherCode = 0;
                                sonCode = 0;
                            }
                        } catch (Exception e) {
                            Log.e(TAG, "consumeRoute() Constants.PAGE_ID_CATEGORY error1=" + e.getMessage());
                            fatherCode = 0;
                            sonCode = 0;
                        }
                    } else {
                        try {
                            fatherCode = Long.parseLong(extra.toString());
                            sonCode = 0;
                        } catch (Exception e) {
                            Log.e(TAG, "consumeRoute() Constants.PAGE_ID_CATEGORY error2=" + e.getMessage());
                            fatherCode = 0;
                            sonCode = 0;
                        }
                    }
                } else {
                    fatherCode = 0;
                    sonCode = 0;
                }
                Log.i(TAG, "consumeRoute() Constants.PAGE_ID_CATEGORY fatherCode=" + fatherCode + ", sonCode=" + sonCode);

                AllCategoriesFragment fragment;
                fragment = (AllCategoriesFragment) RouterManager.getInstance().getRouterFragment(RouterConstance.CATEGORIES_COMPREHENSIVE_URL);
                // setCode(fatherCode, sonCode): 第一个参数指定选择的一级Tab，第二个参数指定选择的二级Tab
                fragment.setCode(fatherCode, sonCode);
                if (fragmentKFm != null && validTransactionDelegate() && fragmentKFm.extraTransaction() != null)
                    fragmentKFm.extraTransaction().start((SupportFragment) fragment);
                break;
            case Constants.PAGE_ID_MINE_SUBSCRIBE:   //我的-我的订阅
                BaseShowHideFragment loginFragment = MineUtil.INSTANCE.newMineFragment(0, false);
                if (loginFragment instanceof BackUserFragment) {
                    ((BackUserFragment) loginFragment).setBackData(LoginProcessorConst.BACKTYPE_NONE,
                            Constants.HOME_TO_LOGIN);
                }
                UserInfoManager.getInstance().setLoginType("");
                if (fragmentKFm != null && validTransactionDelegate() && fragmentKFm.extraTransaction() != null)
                    fragmentKFm.extraTransaction().start((SupportFragment) loginFragment);
                break;
            case Constants.PAGE_ID_MINE_HISTRORY:   //我的-收听历史
                BaseShowHideFragment loginFragment1 = MineUtil.INSTANCE.newMineFragment(1, false);
                if (loginFragment1 instanceof BackUserFragment) {
                    ((BackUserFragment) loginFragment1).setBackData(LoginProcessorConst.BACKTYPE_NONE,
                            Constants.HOME_TO_LOGIN);
                }
                UserInfoManager.getInstance().setLoginType("");
                if (fragmentKFm != null && validTransactionDelegate() && fragmentKFm.extraTransaction() != null)
                    fragmentKFm.extraTransaction().start((SupportFragment) loginFragment1);
                break;
            case Constants.PAGE_ID_MINE_PURCHASED:   //我的-已购
                BaseShowHideFragment loginFragment2 = MineUtil.INSTANCE.newMineFragment(2, false);
                if (loginFragment2 instanceof BackUserFragment) {
                    ((BackUserFragment) loginFragment2).setBackData(LoginProcessorConst.BACKTYPE_NONE,
                            Constants.HOME_TO_LOGIN);
                }
                UserInfoManager.getInstance().setLoginType("");
                if (fragmentKFm != null && validTransactionDelegate() && fragmentKFm.extraTransaction() != null)
                    fragmentKFm.extraTransaction().start((SupportFragment) loginFragment2);
                break;
            case Constants.PAGE_ID_MINE_USER:   //我的-个人中心
                BaseShowHideFragment loginFragment3 = MineUtil.INSTANCE.newMineFragment(3, false);
                if (loginFragment3 instanceof BackUserFragment) {
                    ((BackUserFragment) loginFragment3).setBackData(LoginProcessorConst.BACKTYPE_NONE,
                            Constants.HOME_TO_LOGIN);
                }
                UserInfoManager.getInstance().setLoginType("");
                if (fragmentKFm != null && validTransactionDelegate() && fragmentKFm.extraTransaction() != null)
                    fragmentKFm.extraTransaction().start((SupportFragment) loginFragment3);
                break;
            case Constants.PAGE_ID_ACCOUNT_MAIN://综合版-我的
                BaseShowHideFragment loginFragment4 = MineUtil.INSTANCE.showLoginFragment();
                if (loginFragment4 instanceof BackUserFragment) {
                    ((BackUserFragment) loginFragment4).setBackData(LoginProcessorConst.BACKTYPE_NONE,
                            Constants.HOME_TO_LOGIN);
                }
                UserInfoManager.getInstance().setLoginType("");
                if (fragmentKFm != null && validTransactionDelegate() && fragmentKFm.extraTransaction() != null)
                    fragmentKFm.extraTransaction().start((SupportFragment) loginFragment4);
                break;
            case Constants.PAGE_ID_ACTIVITY://综合版-活动详情
                //不是常驻的活动才可以打开详情
                String actId = "";
                if (extra != null && !TextUtils.isEmpty(extra.toString())) {
                    actId = extra.toString();
                }
                if (TextUtils.isEmpty(actId)) {
                    ToastUtil.showInfo(LauncherActivity.this, "数据错误！");
                    return IRouterConsumer.ROUTER_CONSUME_FULLY;
                }
                ActivitysDetailsDialogFragment dialogFragment
                        = (ActivitysDetailsDialogFragment) new ActivitysDetailsDialogFragment(AppManager.getInstance().getCurrentActivity(), actId);
                dialogFragment.show();
                break;
            case Constants.PAGE_ID_PLAYER_MUSIC_LIST:
            case Constants.PAGE_ID_BROADCAST_MAIN:
            case Constants.PAGE_ID_PLAYER_LIVING:
            case Constants.PAGE_ID_PLAYER_ALBUM_MAIN:
                PageJumper.getInstance().jumpToPlayerFragment(null);
                break;
            case Constants.PAGE_ID_LIVE_ROOM:
                String liveId = "";
                if (extra != null && !TextUtils.isEmpty(extra.toString())) {
                    liveId = extra.toString();
                }
                if (TextUtils.isEmpty(liveId)) {
                    ToastUtil.showInfo(LauncherActivity.this, "数据错误！");
                    return IRouterConsumer.ROUTER_CONSUME_FULLY;
                }
                PageJumper.getInstance().jumpToLivePage(Long.parseLong(liveId));
                break;
        }
        return IRouterConsumer.ROUTER_CONSUME_FULLY;
    }

    private boolean validTransactionDelegate() {
        return LauncherActivity.this.getSupportDelegate().getTransactionDelegate() != null;
    }
}
