package com.kaolafm.kradio.categories.broadcast;

import android.util.Log;

import com.kaolafm.kradio.lib.base.mvp.BaseModel;
import com.kaolafm.kradio.lib.bean.BroadcastTabContainerData;
import com.kaolafm.kradio.lib.bean.BroadcastTabData;
import com.kaolafm.opensdk.api.operation.OperationRequest;
import com.kaolafm.opensdk.api.operation.model.category.Category;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.trello.rxlifecycle3.LifecycleTransformer;

import java.util.ArrayList;
import java.util.List;

public class TabBroadcastModel extends BaseModel {

    private static final String TAG = "TabBroadcastModel";

    public void requestBroadcastData(int categoryId, LifecycleTransformer lifecycleTransformer, HttpCallback<List<Category>> callback) {
        OperationRequest operationRequest = new OperationRequest().bindLifecycle(lifecycleTransformer);
        operationRequest.getSubcategoryListForMoreLevels(String.valueOf(categoryId), null, callback);
    }

    @Override
    public void destroy() {
    }

    /**
     * 生成本地调试用的假数据
     */
    public BroadcastTabContainerData generateTestData() {
        BroadcastTabContainerData containerData = new BroadcastTabContainerData();
        containerData.tabsList = new ArrayList<>();
        containerData.fragments = new ArrayList<>();
        for (int i = 0; i < 15; i++) {
            BroadcastTabData tab = new BroadcastTabData();
            tab.id = i;
            tab.name = "城市" + i;
            tab.isSelected = i == 0;
            containerData.tabsList.add(tab);
//            containerData.fragments.add(BroadcastListFragment.newInstance(10073509, tab.name));
            // 测试环境 - 辽宁
            containerData.fragments.add(BroadcastTabContentFragment.newInstance(10073509, tab.name));
//            // 正式环境 - 辽宁
//            containerData.fragments.add(BroadcastTabContentFragment.newInstance(10079449, tab.name));
        }
        Log.d(TAG, "generateTestData() -> containerData:" + containerData);
        return containerData;
    }
}
