package com.kaolafm.kradio.lib.base.flavor;

import android.app.Activity;
import android.content.Context;
import android.view.View;
import android.webkit.WebView;

public interface UserCenterInter {

    String getFlavorServiceAgreementUrl();

    String getFlavorHiddenPolicyUrl();

    boolean supportDarkLightSwitch();

    String getTheme(Context context);
    boolean isShowThemeEvent(View view);
    WebView getWebView(Activity context, String url);

    class ThemeChangeEvent {

        private String theme;

        public String getTheme() {
            return theme;
        }

        public void setTheme(String theme) {
            this.theme = theme;
        }
    }

    void setWebViewLongClick(WebView webView);

}
