<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <com.kaolafm.kradio.IndicatorV212SeekBar
        android:id="@+id/player_bar_progress"
        android:layout_width="0dp"
        android:layout_height="@dimen/m111"
        android:background="@null"
        android:clipChildren="false"
        android:max="100"
        android:thumb="@null"
        android:thumbOffset="100dp"
        app:need_inner_thumb_view="false"
        app:center_color_seek="@color/playerbar_center_color_seek"
        app:end_color_bg="@color/playerbar_end_color_bg"
        app:end_color_seek="@color/playerbar_end_color_seek"
        app:end_color_thumb="@color/playerbar_end_color_thumb"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/infoPlayer"
        app:layout_constraintStart_toStartOf="@id/infoPlayer"
        app:position_end_color_bg="@color/playerbar_position_end_color_bg"
        app:position_start_color_bg="@color/playerbar_position_start_color_bg"
        app:start_color_bg="@color/playerbar_start_color_bg"
        app:start_color_seek="@color/playerbar_start_color_seek"
        app:start_color_thumb="@color/playerbar_start_color_thumb"
        app:text_color_thumb="@color/playerbar_text_color_thumb"
        tools:max="100"
        tools:progress="10"
        tools:visibility="visible" />
    <!--            android:progressDrawable="@drawable/ll_play_bar_seekbar"
    -->

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/infoPlayer"
        android:layout_width="@dimen/m1120"
        android:layout_height="@dimen/m100"
        android:layout_marginStart="@dimen/m80"
        android:layout_marginEnd="@dimen/m80"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="parent">

        <include
            layout="@layout/bar_message_badge_view"
            android:layout_width="@dimen/m80"
            android:layout_height="@dimen/m80"
            android:layout_marginStart="@dimen/m60"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/player_bar_previous"
            android:layout_width="@dimen/m80"
            android:layout_height="@dimen/m80"
            android:layout_gravity="center"
            android:layout_marginStart="@dimen/m56"
            android:layout_marginEnd="@dimen/m16"
            android:background="@drawable/color_main_button_click_selector"
            android:contentDescription="@string/content_desc_pre"
            android:padding="@dimen/m24"
            android:scaleType="fitCenter"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/player_bar_play_ll"
            app:layout_constraintStart_toEndOf="@id/bar_message_badge_view"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/comprehensive_playerbar_prev" />

        <fr.castorflex.android.circularprogressbar.CircularProgressBar
            android:id="@+id/player_bar_loading"
            style="@style/CustomerCircularProgressBar"
            android:layout_width="@dimen/m80"
            android:layout_height="@dimen/m80"
            android:visibility="invisible"
            app:cpb_color="@color/circular_progress_color"
            app:cpb_stroke_width="@dimen/loading_progress_width"
            app:layout_constraintBottom_toBottomOf="@id/player_bar_play_ll"
            app:layout_constraintEnd_toEndOf="@id/player_bar_play_ll"
            app:layout_constraintStart_toStartOf="@id/player_bar_play_ll"
            app:layout_constraintTop_toTopOf="@id/player_bar_play_ll" />

        <LinearLayout
            android:id="@+id/player_bar_play_ll"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/rl_previous_next"
            app:layout_constraintStart_toEndOf="@id/player_bar_previous"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/player_bar_play"
                android:layout_width="@dimen/m80"
                android:layout_height="@dimen/m80"
                android:layout_marginLeft="@dimen/m16"
                android:layout_marginRight="@dimen/m16"
                android:background="@drawable/color_main_button_click_selector"
                android:padding="@dimen/m24"
                android:scaleType="centerCrop"
                app:srcCompat="@drawable/comprehensive_playerbar_play" />

        </LinearLayout>

        <!--为了兼容 直播 ，广播状态下，播放前一个按钮的出现和隐藏，引发位置变动-->
        <LinearLayout
            android:id="@+id/rl_previous_next"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/player_bar_play_ll"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/player_bar_next"
                android:layout_width="@dimen/m80"
                android:layout_height="@dimen/m80"
                android:layout_gravity="center"
                android:layout_marginLeft="@dimen/m16"
                android:layout_marginRight="@dimen/m16"
                android:background="@drawable/color_main_button_click_selector"
                android:contentDescription="@string/content_desc_next"
                android:padding="@dimen/m24"
                android:scaleType="centerCrop"
                app:srcCompat="@drawable/comprehensive_playerbar_next" />

            <ImageView
                android:id="@+id/player_bar_collect"
                android:layout_width="@dimen/m80"
                android:layout_height="@dimen/m80"
                android:layout_gravity="center"
                android:layout_marginLeft="@dimen/m16"
                android:layout_marginRight="@dimen/m16"
                android:background="@drawable/color_main_button_click_selector"
                android:contentDescription="@string/content_desc_collect"
                android:padding="@dimen/m24"
                android:scaleType="centerCrop"
                android:visibility="gone"
                app:srcCompat="@drawable/playerbar_collect" />

            <ImageView
                android:id="@+id/player_bar_video_minimum"
                android:layout_width="@dimen/m80"
                android:layout_height="@dimen/m80"
                android:layout_gravity="center"
                android:layout_marginLeft="@dimen/m16"
                android:layout_marginRight="@dimen/m16"
                android:src="@drawable/ic_player_bar_video_maximum"
                android:padding="@dimen/m24"
                android:scaleType="centerCrop"
                android:visibility="gone"/>

            <com.kaolafm.kradio.player.comprehensive.play.view.AIRadioMinusFeedbackView
                android:id="@+id/ai_radio_minus_feed_back_view"
                android:layout_width="@dimen/m75"
                android:layout_height="@dimen/m75"
                android:layout_gravity="center"
                android:layout_marginRight="@dimen/m65"
                android:background="@drawable/color_main_button_click_selector"
                android:visibility="gone"
                app:minus_location="1" />
        </LinearLayout>

        <com.kaolafm.kradio.component.ui.base.view.OvalImageView
            android:id="@+id/player_bar_cover"
            android:layout_width="@dimen/m72"
            android:layout_height="@dimen/m72"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/m40"
            android:scaleType="fitXY"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/rl_previous_next"
            app:layout_constraintTop_toTopOf="parent"
            app:oval_radius="@dimen/m8"
            app:srcCompat="@drawable/media_default_pic" />

        <LinearLayout
            android:id="@+id/player_bar_constrantlayout_rl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/y17"
            android:layout_marginBottom="@dimen/y17"
            android:gravity="center_vertical"
            android:orientation="vertical"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/player_bar_cover">

            <include
                layout="@layout/bar_player_horizontal_inner_v212"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="@dimen/m20"
                android:layout_marginEnd="@dimen/m60" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/thumb_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 隐藏控件，用于所见即可说语音执行滑动操作 -->
    <TextView
        android:id="@+id/cd_forward"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@color/transparent"
        android:contentDescription="@string/content_desc_forward"
        android:text="@string/content_desc_forward"
        android:textColor="@color/transparent"
        android:textSize="1sp"
        app:layout_constraintRight_toRightOf="@id/player_bar_progress"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="SmallSp" />

    <TextView
        android:id="@+id/cd_backward"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@color/transparent"
        android:contentDescription="@string/content_desc_backward"
        android:text="@string/content_desc_backward"
        android:textColor="@color/transparent"
        android:textSize="1sp"
        app:layout_constraintLeft_toLeftOf="@id/player_bar_progress"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="SmallSp" />

    <TextView
        android:id="@+id/cd_replay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@color/transparent"
        android:contentDescription="@string/content_desc_replay"
        android:text="@string/content_desc_replay"
        android:textColor="@color/transparent"
        android:textSize="1sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="SmallSp" />
</androidx.constraintlayout.widget.ConstraintLayout>
