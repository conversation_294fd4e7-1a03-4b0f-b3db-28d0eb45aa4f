<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:paddingTop="@dimen/m42"
    android:background="@color/order_bg_color">

    <include
        android:id="@+id/multis_list"
        layout="@layout/order_audios_multis_list"
        android:layout_width="match_parent"
        android:layout_height="@dimen/order_content_has_tab_height"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        />

    <include
        android:id="@+id/multis_pay"
        android:visibility="gone"
        layout="@layout/order_audios_multis_pay"
        android:layout_width="match_parent"
        android:layout_height="@dimen/order_content_has_tab_height"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        />

</androidx.constraintlayout.widget.ConstraintLayout>