package com.kaolafm.kradio.common;

import android.app.Application;
import android.content.Context;
import android.content.SharedPreferences;
import android.os.Build;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;

import com.kaolafm.ad.AdvertOptions;
import com.kaolafm.base.internal.DeviceIdUtil;
import com.kaolafm.base.utils.DeviceUtil;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.base.utils.NetworkMonitor;
import com.kaolafm.kradio.common.http.RequestInterceptManager;
import com.kaolafm.kradio.common.http.vehicle.KlSdkVehicle;
import com.kaolafm.kradio.common.utils.FileUtil;
import com.kaolafm.kradio.k_kaolafm.BuildConfig;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.DeviceInfoSetting;
import com.kaolafm.kradio.lib.base.flavor.ObtainDeviceID;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.KaolaAppConfigData;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.SPUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.YTDataCache;
import com.kaolafm.kradio.lib.utils.YTLogUtil;
import com.kaolafm.opensdk.OpenSDK;
import com.kaolafm.opensdk.Options;
import com.kaolafm.opensdk.account.token.AccessTokenManager;
import com.kaolafm.opensdk.crash.InitSDKException;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.log.LogLevel.RequestLevel;
import com.kaolafm.opensdk.log.Logging;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Kradio SDK 初始化管理类。
 *
 * <AUTHOR> Yan
 * @date 2019-11-08
 */
public final class KradioSDKManager {

    private static final String TAG = "KradioSDKManager";
    /**
     * SDK是否已经初始化。true-是
     */
    private volatile boolean initialized = false;

    /**
     * 是否正在激活，true-是
     */
    private volatile boolean activating = false;

    private volatile boolean activated = false;

    /**
     * 是否需要清空token, 为了解决 厂商适配中, 如果刷机后, deviceId变化了. 导致获取接口出现参数错误问题
     */
    private volatile boolean isNeedClearToken = false;

    private final List<OnUsableObserver> mUsableObservers = new ArrayList<>();

//    private DeviceInfoSetting mDeviceInfoSetting;

    private static final class ManagerHolder {
        private static final KradioSDKManager INSTANCE = new KradioSDKManager();
    }

    public static KradioSDKManager getInstance() {
        return ManagerHolder.INSTANCE;
    }

    private KradioSDKManager() {
        Logging.setDebug(BuildConfig.DEBUG);
        Logging.setRequestLevel(RequestLevel.ALL);

    }

    private boolean checkCertificate() {
        Application context = AppDelegate.getInstance().getContext();
        String rawStr = FileUtil.readRawFile(context, R.raw.certificate);
        String time = rawStr.substring(0,10);
        String MD5 = rawStr.substring(10).trim();
        String str = FileUtil.md5(context.getString(R.string.certificates_str));
        LocalDate givenDate = LocalDate.parse(time);
        // 当前日期时间
        LocalDateTime now = LocalDateTime.now();
        if(str.equals(MD5)){
            if(givenDate.isAfter(now.toLocalDate())){
                return true;
            }else {
                throw new RuntimeException("需更新certificate.cer中的时间");
            }
        }else {
            throw new RuntimeException("证书校验失败");
        }

    }
    /**
     * 初始化并激活SDK
     */
    public void initAndActivate() {
        YTLogUtil.logStart(TAG, "initAndActivate", "");
        KradioSDKManager.getInstance().addUsableObserver(() -> {
            YTLogUtil.logStart(TAG, "initAndActivate", "activte done");
            YTDataCache.prepareHomeData();
        });

        Application context = AppDelegate.getInstance().getContext();
        boolean nullContext = context == null;
        Log.i(TAG, "initAndActivate context is null: " + nullContext);
        if (nullContext) {
            context = AppDelegate.getInstance().getContext();
        }
        boolean activate = OpenSDK.getInstance().isActivate();
        YTLogUtil.logStart(TAG, "initAndActivate", "activate:" + activate + ", initialized:" + initialized);
        if (initialized && !activate) {
            // 如果初始化并且没有激活，通过网络激活
            activateIfNetworkAvailable(context);
            return;
        }

        SharedPreferences sp = SPUtil.getSharedPreferences(context, null);
        String onlyId = sp.getString(SPUtil.SP_ONLY_ID, "");
        // 基础设备ID
        if (TextUtils.isEmpty(onlyId)) {
            onlyId = obtainDeviceIdFromBase(context);//TODO:直接替换车厂的获取方法。
            if (!TextUtils.isEmpty(onlyId)) {
                SPUtil.putString(context, sp, SPUtil.SP_ONLY_ID, onlyId);
            }
        }
        if (!TextUtils.isEmpty(onlyId)) {
            KaolaAppConfigData.getInstance().setUdid(onlyId);
            // 设置设备ID和车型到共享偏好设置
            DeviceIdUtil.setDeviceId(onlyId);
            YTLogUtil.logStart(TAG, "initAndActivate", "initSDK start onlyId = " + onlyId);
        }

        Options options = RequestInterceptManager.getInstance().getSDKOptions();
        options.setDeviceId(onlyId);
        try {
            OpenSDK.getInstance().initSDK(context, (AdvertOptions) options, null);
        } catch (InitSDKException e) {
            YTLogUtil.logStart(TAG, "initAndActivate", "initSDK exception:" + e.getMessage());
            ToastUtil.showError(context,"initSDK exception:" + e.getMessage());
        }
        YTLogUtil.logStart(TAG, "initAndActivate", "initSDK done");
        initialized = true;
        if (isNeedClearToken) {
            AccessTokenManager.getInstance().getKaolaAccessToken().clear();
        }
        activateIfNetworkAvailable(context);
        // 下面是以前的激活方法先保留一下
    }

    private String obtainDeviceIdFromBase(Context context){
        String deviceID = null;
        ObtainDeviceID obtainDeviceID = ClazzImplUtil.getInter("ObtainDeviceIDImpl");
        if (obtainDeviceID != null) {
            deviceID = obtainDeviceID.obtainDeviceID();
        }
        if (StringUtil.isEmpty(deviceID)) {
            deviceID = DeviceUtil.getImei(context);
            if(TextUtils.isEmpty(deviceID) && checkCertificate()){
                deviceID = DeviceUtil.getAndroidID(context);
//                Toast.makeText(context, "AndroidID:"+deviceID, Toast.LENGTH_SHORT).show();
            }
        }
        return deviceID;
    }

    public void release() {
        OpenSDK.getInstance().release();
    }

    /**
     * 网络可用就直接激活，不可用就注册网络监听，可用时再激活。
     *
     * @param context
     */
    private void activateIfNetworkAvailable(Context context) {
        if (NetworkUtil.isNetworkAvailable(context, false)) {
            activate();
        } else {
            NetworkMonitor networkMonitor = NetworkMonitor.getInstance(context);
            networkMonitor.registerNetworkStatusChangeListener(new NetworkStatusChangedListener());
        }
    }

    /**
     * 调用SDK接口进行激活
     */
    private void activate() {
        YTLogUtil.logStart(TAG, "activate", "");
        OpenSDK openSDK = OpenSDK.getInstance();
        if (!openSDK.isActivate()) {
            if (activating) {
                return;
            }
            activating = true;
            openSDK.activate(new HttpCallback<Boolean>() {
                @Override
                public void onSuccess(Boolean aBoolean) {
                    YTLogUtil.logStart(TAG, "activate", "success");

                    activated = true;
                    activating = false;
                    notifyObservers();
                }

                @Override
                public void onError(ApiException e) {
                    Log.i(TAG, "activate error: " + e.getMessage());
                    activating = false;
                }
            });
        } else {
            Log.i(TAG, "already activate success");

            activated = true;
            notifyObservers();
        }
    }

    private void notifyObservers() {
        if (!ListUtil.isEmpty(mUsableObservers)) {
            for (OnUsableObserver observer : mUsableObservers) {
                observer.activate();
            }
        }
        mUsableObservers.clear();
    }


    public boolean isInitialized() {
        return initialized;
    }

    /**
     * SDK接口是否可用 true-可用
     */
    public boolean isUsable() {
        return isInitialized() && OpenSDK.getInstance().isActivate();
    }

    /**
     * 添加监听
     */
    public void addUsableObserver(OnUsableObserver observer) {
        if (observer != null) {
            if (activated) {
                observer.activate();
            } else {
                mUsableObservers.add(observer);
            }
        }
    }

    /**
     * 移除监听
     */
    public void removeUsableObserver(OnUsableObserver observer) {
        if (observer != null) {
            mUsableObservers.remove(observer);
        }
    }


    public interface OnUsableObserver {
        void activate();
    }

    private class NetworkStatusChangedListener implements NetworkMonitor.OnNetworkStatusChangedListener {

        @Override
        public void onStatusChanged(int newStatus, int oldStatus) {
            if (newStatus == NetworkMonitor.STATUS_MOBILE || newStatus == NetworkMonitor.STATUS_WIFI) {
                Logging.d("OpenSDK初始化网络监听，网络状态=%s::%s", newStatus, oldStatus);
                activate();
                NetworkMonitor.getInstance(AppDelegate.getInstance().getContext()).removeNetworkStatusChangeListener(this);
            }
        }
    }

    /**
     * 是否需要清空token
     */
    public void setNeedClearToken(boolean needClearToken) {
        isNeedClearToken = needClearToken;
    }

    private void setCarType() {
        DeviceInfoSetting deviceInfoSetting = ClazzImplUtil.getInter("DeviceInfoSettingImpl");
        if (deviceInfoSetting != null) {
            deviceInfoSetting.setInfoForSDK(AppDelegate.getInstance().getContext());
        } else {
            KlSdkVehicle.getInstance().setCarType(Build.DEVICE);
        }
    }
}
