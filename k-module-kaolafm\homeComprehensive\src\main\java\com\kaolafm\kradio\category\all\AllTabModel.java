package com.kaolafm.kradio.category.all;

import com.kaolafm.kradio.categories.CategoryConstant;
import com.kaolafm.kradio.categories.SubcategoryModel;
import com.kaolafm.opensdk.http.core.HttpCallback;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 **/
public class AllTabModel extends SubcategoryModel {

    public AllTabModel() {
        super(null);
    }

    public void getAllCategoryData(HttpCallback<List<AllCategoriesItem>> listHttpCallback) {
        if (listHttpCallback != null) {
            List<AllCategoriesItem> data = new ArrayList<>();

            AllCategoriesItem aciQQ = new AllCategoriesItem();
            aciQQ.title = "QQ音乐";
            aciQQ.type = CategoryConstant.MEDIA_TYPE_MUSIC;
            data.add(aciQQ);

            AllCategoriesItem aciAi = new AllCategoriesItem();
            aciAi.title = "AI电台";
            aciAi.type = CategoryConstant.MEDIA_TYPE_RADIO;
            data.add(aciAi);

            AllCategoriesItem aciBroadcast = new AllCategoriesItem();
            aciBroadcast.title = "在线广播";
            aciBroadcast.type = CategoryConstant.MEDIA_TYPE_BROADCAST;
            data.add(aciBroadcast);

            listHttpCallback.onSuccess(data);
        }
    }
}
