<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_home">

    <ImageView
        android:id="@+id/iv_switch_channel_back"
        android:layout_width="@dimen/m80"
        android:layout_height="@dimen/m80"
        android:layout_marginLeft="@dimen/x40"
        android:layout_marginTop="@dimen/y20"
        android:background="@drawable/color_main_button_click_selector"
        android:padding="@dimen/m22"
        android:src="@drawable/user_ic_left_back"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_channel_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:text="@string/settings_channel_back_door"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/m32"
        app:layout_constraintBottom_toBottomOf="@id/iv_switch_channel_back"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_switch_channel_back" />

    <View
        android:id="@+id/view_channel_title_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/y1"
        android:layout_marginTop="@dimen/y20"
        android:background="#19FFFFFF"
        app:layout_constraintTop_toBottomOf="@id/iv_switch_channel_back" />

    <TextView
        android:id="@+id/tv_channel_current"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/x77"
        android:layout_marginTop="@dimen/y72"
        android:text="渠道分支:"
        android:textColor="@color/text_color_3"
        android:textSize="@dimen/text_size5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/view_channel_title_divider" />

    <TextView
        android:id="@+id/tv_channel_current_name"
        android:layout_width="0dp"
        android:layout_height="@dimen/y56"
        android:layout_marginEnd="@dimen/x50"
        android:layout_marginStart="@dimen/x20"
        android:background="@drawable/shape_channel_rectangle"
        android:drawableEnd="@drawable/bg_switch_channel"
        android:drawablePadding="@dimen/x30"
        android:gravity="center_vertical"
        android:singleLine="true"
        android:paddingStart="@dimen/x36"
        android:textColor="@color/text_color_3"
        android:ellipsize="marquee"
        android:textSize="@dimen/text_size5"
        app:layout_constraintBottom_toBottomOf="@id/tv_channel_current"
        app:layout_constraintLeft_toRightOf="@id/tv_channel_current"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_channel_current"
        tools:text="测试专用" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_channel_list"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/tv_channel_commit"
        app:layout_constraintLeft_toLeftOf="@id/tv_channel_current_name"
        app:layout_constraintRight_toRightOf="@id/tv_channel_current_name"
        app:layout_constraintTop_toBottomOf="@id/tv_channel_current_name"
        android:visibility="invisible"
        android:layout_marginBottom="@dimen/y20"
        />

    <TextView
        android:id="@+id/tv_channel_commit"
        android:layout_width="@dimen/x360"
        android:layout_height="@dimen/y70"
        android:layout_marginBottom="@dimen/y110"
        android:background="@drawable/selector_home_all_category_ic"
        android:gravity="center"
        android:text="确认"
        android:textColor="@color/text_color_white"
        android:textSize="@dimen/text_size5"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>