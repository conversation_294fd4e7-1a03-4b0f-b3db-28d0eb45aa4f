package com.kaolafm.kradio.flavor.impl;

import android.app.ActivityManager;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Build;
import androidx.annotation.RequiresApi;
import android.view.Gravity;

import com.kaolafm.auto.home.HubActivity;
import com.kaolafm.kradio.flavor.view.CustomAlertDialog;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.base.flavor.KRadioPermissionInter;

import java.util.List;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-10-13 18:21
 ******************************************/
public class KRadioPermissionImpl implements KRadioPermissionInter {
    @Override
    public boolean onPermissionDenied(Object... args) {
        Context context = (Context)args[0];
        CustomAlertDialog.Builder builder = new CustomAlertDialog.Builder(context);
        builder.setCancelable(false);
        builder.setCanceledOnTouchOutside(false);
        builder.setTitle(context.getResources().getString(com.kaolafm.kradio.k_kaolafm.R.string.permissions_tip));
        builder.setMessage(context.getResources().getString(com.kaolafm.kradio.k_kaolafm.R.string.permissions_app_tip));
        builder.setGravity(Gravity.CENTER);
        builder.setNegativeButton("知道了",
                new android.content.DialogInterface.OnClickListener() {
                    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
                    public void onClick(DialogInterface dialog, int which) {
//                        AppManager.getInstance().appExit();
                        //销毁任务栈 解决权限关闭，启动应用拒绝权限，还被调起的问题
                        ActivityManager activityManager = (ActivityManager) context.getApplicationContext().getSystemService(Context.ACTIVITY_SERVICE);
                        List<ActivityManager.AppTask> appTaskList = activityManager.getAppTasks();
                        for (ActivityManager.AppTask appTask : appTaskList) {
                            appTask.finishAndRemoveTask();
                        }
                    }
                });

        builder.create().show();

        return true;
    }
}
