# 多屏幕跳转修复方案（K-Radio）

## 背景
- 现象：设置页（SettingActivity）与搜索页（SearchActivity）在副屏触发跳转时，页面在主屏打开。
- 预期：在副屏触发的页面，应在副屏打开；主屏触发的页面，在主屏打开。

## 核心问题与根因
- 路由跳转时未显式指定目标 Display（屏幕）
- Android 默认行为：未指定时，新 Activity 可能落在主屏
- 代码层面：RouterManager 的跳转统一走 `ARouter.getInstance().build(...).navigation()`，未携带 `ActivityOptions.setLaunchDisplayId(...)`

## 解决方案概述
- 在路由层（RouterManager）集中处理：
  - 自动获取“当前 Activity 所在屏幕”的 `displayId`
  - 若在副屏，则给新启动的 Activity 注入相同的 `displayId`
  - 调用方（各业务页面）零改动，即可自动保持与当前屏幕一致
- 为便于与 ARouter 对接，新增了 `Postcard.withOptionsBundle(Bundle)` 接口，直接传入 `ActivityOptions` 生成的 `Bundle`

## 代码变更清单
1) Router 跳转层：
- 文件：`k-module-kaolafm/common/src/main/java/com/kaolafm/kradio/common/router/RouterManager.java`
- 主要改动：
  - 方法 `jumpPage(String path)`：
    - 获取 `displayId`，副屏时：
      - `ActivityOptions.makeBasic().setLaunchDisplayId(displayId)`
      - `ARouter.getInstance().build(path).withOptionsBundle(options.toBundle()).navigation()`
    - 主屏：保留原逻辑
  - 方法 `jumpPage(String path, Bundle bundle)`：同上，增加 `with(bundle)`
  - 新/改：`getCurrentDisplayId(Activity)` 使用 `activity.getWindowManager().getDefaultDisplay().getDisplayId()`

2) ARouter Postcard 扩展：
- 文件：`k-base-lib/src/main/java/com/alibaba/android/arouter/facade/Postcard.java`
- 新增方法：
  - `public Postcard withOptionsBundle(Bundle options)`
  - 作用：直接设置 `optionsCompat`（ARouter 内部会用它作为 `startActivity` 的 options）

## 关键实现细节
- 获取屏幕 ID：
  - `activity.getWindowManager().getDefaultDisplay().getDisplayId()`
  - 满足本项目配置（minSdk=26、target/compile=29）
- 设置目标屏幕：
  - `ActivityOptions options = ActivityOptions.makeBasic();`
  - `options.setLaunchDisplayId(displayId);`
- 传给 ARouter：
  - 直接 `withOptionsBundle(options.toBundle())`（避免不必要的 `ActivityOptionsCompat` 包装）

## 验证用例（建议）
- 场景一：在副屏进入个人中心，点击“设置”
  - 期望：SettingActivity 在副屏打开
- 场景二：在副屏进入“搜索”
  - 期望：SearchActivity 在副屏打开
- 场景三：上述操作在主屏触发
  - 期望：页面保持在主屏
- 场景四：其他通过 RouterManager 的通用跳转
  - 期望：均保持与当前屏幕一致

## 回归影响范围
- 所有通过 `RouterManager.jumpPage(...)` 系列触发的页面跳转
- 调用方无需修改，行为将自动与当前屏幕保持一致

## 风险与兼容性
- 现配置（minSdk=26、target/compile=29）下：
  - `getDefaultDisplay().getDisplayId()`、`setLaunchDisplayId` 均可用
- 若未来将 `compileSdk` 升至 31+：
  - `getDefaultDisplay()` 会被标记废弃但仍可用；也可切换为 `activity.getDisplay()` 获取 ID

## 为什么不使用 ActivityOptionsCompat？
- `ActivityOptionsCompat` 本身不提供 `setLaunchDisplayId`；真正需要的是 `ActivityOptions` 的 `Bundle`
- 新增 `withOptionsBundle(Bundle)` 后，直接传递 `ActivityOptions.toBundle()` 更直观简单

## FAQ
- Q：为何放在 `RouterManager` 层统一处理？
  - A：一次修改，覆盖所有路由跳转，避免调用方零散改动和遗漏
- Q：currentActivity 为空怎么办？
  - A：`getCurrentDisplayId` 返回默认（0，主屏），行为与历史一致

## 后续建议
- 若有少量页面需要“强制在主屏/副屏”打开，可在 RouterManager 扩展一个显式参数接口，例如：
  - `jumpPageOnDisplay(String path, Bundle bundle, int displayId)`
- 若升级到 compileSdk 31+，可将获取屏幕 ID 的实现切换为：`activity.getDisplay().getDisplayId()`

---
如需我补充测试脚本或进一步自动化校验，请告诉我。
