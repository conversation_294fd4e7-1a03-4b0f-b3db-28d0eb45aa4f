package com.kaolafm.kradio.lib.base.ui;

import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.view.View.OnClickListener;


import com.kaolafm.kradio.lib.utils.AntiShake;

/**
 * ViewHolder 基类，只需要实现setupData()就可以填充数据。
 *
 * <AUTHOR>
 * @date 2017/12/27
 */

public abstract class BaseHolder<T> extends RecyclerView.ViewHolder implements OnClickListener {

    protected OnViewClickListener mViewClickListener;
    protected OnViewCOtherlickListener mViewCOtherlickListener;

    public BaseHolder(final View itemView) {
        super(itemView);
        itemView.setOnClickListener(this);
    }

    @Override
    public void onClick(final View v) {
        if ((!AntiShake.check(v.getId())) && mViewClickListener != null) {
            mViewClickListener.onViewClick(v, getAdapterPosition());
        }
    }

    /**
     * 释放holder中的资源
     */
    public void onRelease() {

    }

    void setOnViewHolderClickListener(OnViewClickListener listener) {
        mViewClickListener = listener;
    }
    void setOnViewHolderOtherClickListener(OnViewCOtherlickListener listener) {
        mViewCOtherlickListener = listener;
    }

    public interface OnViewClickListener {

        /**
         * 点击事件
         *
         * @param position 可能会返回-1，当改条目被remove后会返回-1.
         */
        void onViewClick(View view, int position);
    }

    public interface OnViewCOtherlickListener {

        /**
         * 点击事件
         *
         * @param view
         * @param position
         * @param o
         */
        void onViewOtherClick(View view, int position, Object o);
    }

    /**
     * 填充数据
     */
    public abstract void setupData(T t, int position);

    private int imgSize;

    public int getImgSize() {
        return imgSize;
    }

    public void setImgSize(int imgSize) {
        this.imgSize = imgSize;
    }
}
