package com.kaolafm.kradio.common;

import android.app.Application;
import android.util.Log;

import com.alibaba.android.arouter.launcher.ARouter;
import com.kaolafm.base.utils.SpUtil;
import com.kaolafm.kradio.common.utils.OnlineConstants;
import com.kaolafm.kradio.lib.BuildConfig;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.DeviceInfoSettingASync;
import com.kaolafm.kradio.lib.base.flavor.KRadioLazyInitInter;
import com.kaolafm.kradio.lib.base.flavor.NetPingOptions;
import com.kaolafm.kradio.lib.init.AppInit;
import com.kaolafm.kradio.lib.init.BaseAppInitializer;
import com.kaolafm.kradio.lib.sdk.utils.PerformanceSettingMananger;
import com.kaolafm.kradio.lib.utils.AppUtil;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkManager;
import com.kaolafm.kradio.lib.utils.YTLogUtil;

import static com.kaolafm.kradio.common.utils.OnlineConstants.TRAVEL_SERVICE_DEFAULT_SWITCH;
import static com.kaolafm.kradio.common.utils.OnlineConstants.VOICE_ASSISTANT;
import static com.kaolafm.kradio.common.utils.OnlineConstants.VOICE_ASSISTANT_DEFAULT_SWITCH;
import static com.kaolafm.kradio.common.utils.OnlineConstants.VOICE_SPEAK_DEFAULT_SWITCH;

/**
 * <AUTHOR> Yan
 * @date 2019-09-18
 */
@AppInit(priority = 80, description = "通用初始化")
public final class CommonInitializer extends BaseAppInitializer {

    private static final String TAG = "CommonInitializer";

    private AppDelegate mAppDelegate;

    private Application mApplication;

    @Override
    public void onCreate(Application application) {
        YTLogUtil.logStart(TAG, "onCreate", "start");

        mApplication = application;
//        String processName = AppUtil.getCurrentProcessName(application);
//        //如果获取进程名为空，重启进程 fix #35094
//        if (processName == null) {
//            AppUtil.restartProcess(application);
//        }
        mAppDelegate = AppDelegate.getInstance();

        NetPingOptions netPingOptions = ClazzImplUtil.getInter("NetPingOptionsImp");
        if (netPingOptions != null) {
            if (netPingOptions.isNeedPing()) {
                NetworkManager.getInstance().init();
            }
        } else {
            NetworkManager.getInstance().init();
        }

        if (AppDelegate.getInstance().getContext() == null) {
            mAppDelegate.onCreate(application);
        }
//        DeviceInfoSettingASync deviceInfoSettingASync = ClazzImplUtil.getInter("DeviceInfoSettingASyncImpl");
//        KRadioLazyInitInter lazyInitInter = ClazzImplUtil.getInter("KRadioLazyInitImpl");
        //有异步获取did的车机，这里不初始化sdk，防止生成默认的did激活，
//        if (deviceInfoSettingASync == null && (lazyInitInter == null || !lazyInitInter.isEnableLazyInit())) {
            KradioSDKManager.getInstance().initAndActivate();
//        }
        if (BuildConfig.DEBUG) {
            // 这两行必须写在init之前，否则这些配置在init过程中将无效
            ARouter.openLog();     // 打印日志
            ARouter.openDebug();   // 开启调试模式(如果在InstantRun模式下运行，必须开启调试模式！线上版本需要关闭,否则有安全风险)
        }
        ARouter.init(application);
        SpUtil.init(application.getApplicationContext());
        //设置图片加载分辨率
        switch (BuildConfig.IMAGE_SIZE) {
            case 0:
                PerformanceSettingMananger.getInstance().setHomeImageSize(PerformanceSettingMananger.LOW_SIZE);
                break;
            case 1:
                PerformanceSettingMananger.getInstance().setHomeImageSize(PerformanceSettingMananger.MEDIUM_SIZE);
                break;
            case 2:
                PerformanceSettingMananger.getInstance().setHomeImageSize(PerformanceSettingMananger.HIGH_SIZE);
                break;
        }
        initMessageSwitchState();
        YTLogUtil.logStart(TAG, "onCreate", "end");
    }

    @Override
    public void onTerminate() {
        super.onTerminate();
        mAppDelegate.onTerminate(mApplication);
        KradioSDKManager.getInstance().release();
    }

    /**
     * 初始化消息按钮状态
     */
    private void initMessageSwitchState() {
        int voiceSpeakState = SpUtil.getInt(OnlineConstants.VOICE_SPEAK, 0);
        int voiceAssistantState = SpUtil.getInt(OnlineConstants.VOICE_ASSISTANT, 0);
        int travelServiceState = SpUtil.getInt(OnlineConstants.TRAVEL_SERVICE, 0);
        if (voiceSpeakState == 0) {
            SpUtil.putInt(OnlineConstants.VOICE_SPEAK, VOICE_SPEAK_DEFAULT_SWITCH);
        }
        if (voiceAssistantState == 0) {
            SpUtil.putInt(VOICE_ASSISTANT, VOICE_ASSISTANT_DEFAULT_SWITCH);
        }
        if (travelServiceState == 0) {
            SpUtil.putInt(OnlineConstants.TRAVEL_SERVICE, TRAVEL_SERVICE_DEFAULT_SWITCH);
        }
    }

}
