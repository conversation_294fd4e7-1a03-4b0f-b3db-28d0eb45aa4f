package com.kaolafm.kradio.flavor.carnetwork.api;

import com.kaolafm.kradio.flavor.carnetwork.carnetworkutils.AuthConstants;
import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.http.core.HttpCallback;

import java.util.HashMap;

import io.reactivex.functions.Function;


public class AuthApiRequest extends BaseRequest {

    private final AuthApiServices mChangAnApiServices;

    public AuthApiRequest() {
        mUrlManager.putDomain("dongfengxiaokang", AuthConstants.BASEURL);
        mChangAnApiServices = obtainRetrofitService(AuthApiServices.class);
    }

    public void requestAuth(String appid, String vid, String itemid,String usercode,
                            long timestamp,int netsource,int cartype, HttpCallback<Integer> callback) {
        HashMap<String, Object> param = new HashMap<>();
        param.put("appid", appid);
        param.put("vid", vid);
        param.put("itemid", itemid);
        param.put("usercode", usercode);
        param.put("timestamp", timestamp);
        param.put("netsource", netsource);
        param.put("cartype", cartype);
        String toJson = mGsonLazy.get().toJson(param);
        doHttpDeal(mChangAnApiServices.getAuthInfo(toJson), new Function<BaseResult, Integer>() {
            @Override
            public Integer apply(BaseResult baseResult) throws Exception {
                return baseResult.getCode();
            }
        }, callback);
    }

}
