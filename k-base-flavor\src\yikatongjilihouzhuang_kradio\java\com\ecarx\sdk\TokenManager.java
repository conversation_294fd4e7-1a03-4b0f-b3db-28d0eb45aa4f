package com.ecarx.sdk;

/**
 * <AUTHOR>
 **/
public class TokenManager {
    private String mAccessToken;
    private String mRefreshToken;


    private TokenManager() {
    }

    private volatile static TokenManager sInstance;

    public static TokenManager getInstance() {
        if (sInstance == null) {
            synchronized (TokenManager.class) {
                if (sInstance == null) {
                    sInstance = new TokenManager();
                }
            }
        }
        return sInstance;
    }

    public void setToken(AccessTokenResult tokenResult) {
        mAccessToken = tokenResult.getAccessToken();
        mRefreshToken = tokenResult.getRefreshToken();
    }

    public String getAccessToken() {
        return mAccessToken;
    }

    public String getRefreshToken() {
        return mRefreshToken;
    }
}
