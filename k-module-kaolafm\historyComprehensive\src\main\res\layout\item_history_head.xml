<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:paddingTop="@dimen/m57"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_history_login_tip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/sync_history_after_login"
        android:textColor="@color/text_color_7"
        android:textSize="@dimen/history_login_tip" />

    <TextView
        android:id="@+id/tv_history_login"
        style="?android:attr/borderlessButtonStyle"
        android:layout_width="@dimen/m370"
        android:layout_height="@dimen/m64"
        android:layout_marginTop="@dimen/m22"
        android:background="@drawable/bg_login_btn"
        android:gravity="center"
        android:text="@string/no_login_buttom_str"
        android:textColor="@color/text_color_7"
        android:textSize="@dimen/text_size_title7" />


</LinearLayout>
