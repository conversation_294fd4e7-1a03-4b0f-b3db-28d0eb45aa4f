package com.kaolafm.ad.comprehensive.base;

import android.os.Bundle;
import androidx.annotation.Nullable;

import com.kaolafm.ad.api.model.Advert;
import com.kaolafm.ad.api.model.AudioImageAdvert;
import com.kaolafm.ad.api.model.ImageAdvert;
import com.kaolafm.ad.api.model.InteractionAdvert;
import com.kaolafm.ad.comprehensive.control.KradioAdSceneConstants;
import com.kaolafm.ad.expose.AdvertInterceptor;
import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.kradio.common.base.BaseSkinAppCompatActivity;
import com.kaolafm.kradio.lib.base.mvp.IPresenter;
import com.kaolafm.kradio.lib.utils.IntentUtils;

public abstract class ComprehensiveBaseAdInterceptorActivity<P extends IPresenter> extends BaseSkinAppCompatActivity<P> {
    private AdvertInterceptor advertInterceptor;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (isNeedAdvertInterceptor()) {
            addAdvertInterceptor();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (advertInterceptor != null) {
            AdvertisingManager.getInstance().removeInterceptor(advertInterceptor);
        }
    }

    /**
     * =================================广告拦截器========================================
     */

    protected void addAdvertInterceptor() {
        /**
         * 广告拦截
         * 如果由品牌电台页面跳转到了播放页，是可以正常显示广告的
         */
        advertInterceptor = new AdvertInterceptor() {
            @Override
            public void intercept(Chain chain) {
                Advert advert = chain.advert();
                IntentUtils intentUtils = IntentUtils.getInstance();
                //处于后台
                if (!intentUtils.isAppOnForeground()) {
                    if (advert instanceof ImageAdvert || advert instanceof InteractionAdvert) {
                        advert.setId(-1);
                        chain.process(advert);
                        return;
                    }
                    if (advert instanceof AudioImageAdvert) {
                        AudioImageAdvert audioImageAdvert = (AudioImageAdvert) advert;
                        audioImageAdvert.setImageAdvert(null);
                        chain.process(audioImageAdvert);
                        return;
                    }
                }

                if (isIgnoreAdvert(advert)) {
                    //没有在播放详情页
                    if (advert instanceof ImageAdvert) {
//                        advert.setId(-1);
                        chain.process(advert);
                        return;
                    }
                    if (advert instanceof AudioImageAdvert) {
                        AudioImageAdvert audioImageAdvert = (AudioImageAdvert) advert;
//                        audioImageAdvert.setImageAdvert(null);
                        chain.process(audioImageAdvert);
                        InteractionAdvert interactionAdvert = ((AudioImageAdvert) advert).getInteractionAdvert();
                        if (interactionAdvert != null && interactionAdvert.getOpportunity()
                                == KradioAdSceneConstants.INTERACT_AD_OPPORTUNITY_BEFORE) {
                            AdvertisingManager.getInstance().expose(interactionAdvert);
                        }
                        return;
                    }
                }
                chain.process(advert);
            }
        };
        AdvertisingManager.getInstance().addInterceptor(advertInterceptor);
    }

    /**
     * 是否需要广告拦截器
     *
     * @return
     */
    protected boolean isNeedAdvertInterceptor() {
        return false;
    }

    /**
     * 是否忽略广告，当{@link ComprehensiveBaseAdInterceptorActivity#isNeedAdvertInterceptor()}返回true才有效
     *
     * @param advert
     * @return
     */
    protected boolean isIgnoreAdvert(Advert advert) {
        return true;
    }
}
