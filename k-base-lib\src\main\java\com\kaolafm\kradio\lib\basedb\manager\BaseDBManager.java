package com.kaolafm.kradio.lib.basedb.manager;

import android.util.Log;

import com.kaolafm.kradio.lib.basedb.GreenDaoInterface.OnQueryListener;
import com.kaolafm.kradio.lib.basedb.greendao.DaoSession;

import io.reactivex.Single;
import io.reactivex.SingleObserver;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;
import java.lang.reflect.ParameterizedType;
import java.util.List;
import java.util.concurrent.Callable;
import org.greenrobot.greendao.async.AsyncSession;
import org.greenrobot.greendao.database.Database;

/**
 * 将所有创建的表格相同的部分封装到这个BaseDBManager中
 *
 * <AUTHOR>
 * @date 2018/5/14
 */

public class BaseDBManager<T> {

    protected final DaoSession mDaoSession;

    protected final DaoManager mManager;

    protected final AsyncSession mAsyncSession;

    private final Class mEntityClass;

    protected BaseDBManager() {
        mManager = DaoManager.getInstance();
        mDaoSession = mManager.getDaoSession();
        mEntityClass = (Class) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0];
        mAsyncSession = mDaoSession.startAsyncSession();
    }

    public void updateTable() {
        Database database = mDaoSession.getDatabase();
        mManager.getHelper().onUpgrade(database, 1, 1);
    }
//======================插入==========================

    /**
     * 插入单个对象
     */
    public void insert(T t) {
        try {
            runInNewThread(() -> {
                mDaoSession.insertOrReplace(t);
                return true;
            }, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 插入一个对象集合
     */
    public void insert(final List<T> list) {
        if (list == null || list.isEmpty()) {
            return;
        }
        try {
            runInNewThread(() -> {
                for (T t : list) {
                    mDaoSession.insertOrReplace(t);
                }
                return true;
            }, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 同步插入单个对象
     */
    public boolean insertSynch(T t) {
        try {
            return mDaoSession.insertOrReplace(t) != -1;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 同步插入一个对象集合
     */
    public boolean insertSynch(final List<T> list) {
        if (list == null || list.isEmpty()) {
            return false;
        }
        try {
            return mDaoSession.insertOrReplace(list) != -1;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

//======================保存==========================

    /**
     * 如果主键相同就更新，不相同就插入
     */
    public void save(T t) {
        save(mEntityClass, t);
    }

    /**
     * 如果主键相同就更新，不相同就插入
     */
    public void save(Class clazz, T t) {
        runInNewThread(() -> {
            mDaoSession.getDao(clazz).save(t);
            return true;
        }, null);
    }

    /**
     * 保存一个对象集合。如果主键相同就更新，不相同就插入
     */
    public void save(Class clazz, List<T> list) {
        runInNewThread(() -> {
            mDaoSession.getDao(clazz).saveInTx(list);
            return true;
        }, null);
    }

    public void saveSync(Class clazz, List<T> list) {
        mDaoSession.getDao(clazz).saveInTx(list);
    }

    public void saveSync(Class clazz, T t) {
        mDaoSession.getDao(clazz).save(t);
    }

    public void saveSync(T t) {
        saveSync(mEntityClass, t);
    }

    /**
     * 保存一个对象集合。如果主键相同就更新，不相同就插入
     */
    public void save(List<T> list) {
        save(mEntityClass, list);
    }
//======================更新==========================

    /**
     * 以对象形式进行数据修改
     * 其中必须要知道对象的主键ID
     */
    public void update(T t) {
        if (t == null) {
            return;
        }
        try {
            mDaoSession.update(t);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 批量更新数据
     */
    public void update(final List<T> list, Class clazz) {
        try {
            mAsyncSession.updateInTx(clazz, list);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 批量更新数据
     */
    public void update(final List<T> list) {
        update(list, mEntityClass);
    }

//======================删除==========================

    /**
     * 删除所有数据，指定类class
     */
    public void deleteAll(Class<T> clazz) {
        try {
            mAsyncSession.deleteAll(clazz);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 删除所有数据，反射获取当前泛型的class
     */
    public void deleteAll() {
        deleteAll(mEntityClass);
    }

    /**
     * 删除某个数据
     */
    public void delete(T t) {
        try {
            mAsyncSession.delete(t);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 批量删除数据
     */
    public void delete(final List<T> list, Class clazz) {
        if (null == list || list.isEmpty()) {
            return;
        }
        try {
            mAsyncSession.deleteInTx(clazz, list);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 批量删除数据
     */
    public void delete(final List<T> list) {
        delete(list, mEntityClass);
    }

//======================查询==========================

    /**
     * 获得某个表名
     */
    public String getTableName(Class clazz) {
        return mDaoSession.getDao(clazz).getTablename();
    }

    /**
     * 获得某个表名
     */
    public String getTableName() {
        return getTableName(mEntityClass);
    }

    /**
     * 根据主键ID来查询，结果以回调方式显示
     */
    public void queryById(long id, Class clazz, OnQueryListener<T> listener) {
        runInNewThread(() -> (T) mDaoSession.getDao(clazz).load(id), listener);
    }

    /**
     * 根据主键ID来查询,结果以回调方式显示
     */
    public void queryById(long id, OnQueryListener<T> listener) {
        queryById(id, mEntityClass, listener);
    }

    /**
     * 查询某条件下的对象，结果以回调方式显示
     */
    public void queryRaw(Class clazz, String where, OnQueryListener<List<T>> listener, Object... params) {
        runInNewThread(() -> (List<T>) mDaoSession.getDao(clazz).queryRawCreate(where, params), listener);
    }

    /**
     * 查询某条件下的对象，结果以回调方式显示
     */
    public void queryRaw(String where, OnQueryListener<List<T>> listener, Object... params) {
        queryRaw(mEntityClass, where, listener, params);
    }

    /**
     * 同步查询某条件下的对象
     */
    public List<T> queryRawSync(Class clazz, String where, String... params) {
        List<T> objects = null;
        try {
            objects = mDaoSession.getDao(clazz).queryRaw(where, params);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return objects;
    }

    /**
     * 同步查询某条件下的对象
     */
    public List<T> queryRawSync(String where, String... params) {
        return queryRawSync(mEntityClass, where, params);
    }

    //-------------查询所有-----------

    /**
     * 查询所有对象
     */
    public Single<List<T>> queryAll(Class clazz) {
        return Single.fromCallable((Callable<List<T>>) () -> mDaoSession.getDao(clazz).loadAll())
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 查询所有对象， 结果以回调方式显示。
     */
    public void queryAll(Class clazz, OnQueryListener<List<T>> listener) {
        try {
            runInNewThread(() -> mDaoSession.getDao(clazz).loadAll(), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * 查询数据库数量
     */
    public void queryAllCount(Class clazz, OnQueryListener<Long> listener) {
        runInNewThread(() -> mDaoSession.getDao(clazz).queryBuilder().buildCount().count(), listener);
    }

    /**
     * 查询数据库数量
     */
    public void queryAllCount(OnQueryListener<Long> listener) {
        queryAllCount(mEntityClass, listener);
    }

    /**
     * 同步查询所有对象
     */
    public List<T> queryAllSync(Class clazz) {
        List<T> list = null;
        try {
            list = mDaoSession.loadAll(clazz);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list;
    }

    /**
     * 查询所有对象
     */
    public Single<List<T>> queryAll() {
        return queryAll(mEntityClass);
    }

    public void queryAll(OnQueryListener<List<T>> listener) {
        queryAll(mEntityClass, listener);
    }

    /**
     * 同步查询所有对象
     */
    public List<T> queryAllSync() {
        return queryAllSync(mEntityClass);
    }

    /**
     * 关闭数据库一般在OnDestroy中使用
     */
    public void closeDataBase() {
        mManager.closeDataBase();
    }

    protected <C> void runInNewThread(final Callable<? extends C> callable, OnQueryListener<C> listener) {
        Single.fromCallable(callable)
                .subscribeOn(Schedulers.io())
                .unsubscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new SingleObserver<C>() {
                    private Disposable mDisposable;

                    @Override
                    public void onSubscribe(Disposable d) {
                        mDisposable = d;
                    }

                    @Override
                    public void onSuccess(C c) {
                        if (listener != null) {
                            listener.onQuery(c);
                        }
                        dispose(mDisposable);
                    }

                    @Override
                    public void onError(Throwable e) {
                        Log.i("BaseDBManager", "onError: " + e);
                        if (listener != null) {
                            listener.onQuery(null);
                        }
                        dispose(mDisposable);
                    }
                });
    }

    /**
     * 注销rxjava，防止内存泄露
     */
    private void dispose(Disposable disposable) {
        if (disposable != null && !disposable.isDisposed()) {
            disposable.dispose();
        }
    }

}
