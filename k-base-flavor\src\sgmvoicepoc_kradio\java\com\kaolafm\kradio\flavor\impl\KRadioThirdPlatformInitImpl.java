package com.kaolafm.kradio.flavor.impl;

import android.annotation.SuppressLint;
import android.util.Log;

import com.kaolafm.kradio.lib.base.flavor.KRadioSystemSourceChangeInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioThirdPlatformInitInter;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.opensdk.player.logic.PlayerManager;

/**
 * <AUTHOR>
 **/
public class KRadioThirdPlatformInitImpl implements KRadioThirdPlatformInitInter {
    private static final String TAG = "KRadioThirdPlatformInitImpl";

    @Override
    public boolean initThirdPlatform(Object... args) {
        initPlayer();
        return true;
    }

    private KRadioSystemSourceChangeInter mKRadioSystemSourceChangeInter;

    private void initPlayer() {
        mKRadioSystemSourceChangeInter = ClazzImplUtil.getInter("KRadioSystemSourceChangeImpl");
        PlayerManager playerManager = PlayerManager.getInstance();
        boolean isInitSuccess = playerManager.isPlayerInitSuccess();
        if (isInitSuccess) {
            if (mKRadioSystemSourceChangeInter != null) {
                mKRadioSystemSourceChangeInter.registerSourceChanged();
            }
        } else {
            playerManager.addPlayerInitComplete(onPlayerInitCompleteListener);
        }
    }

    @Override
    public boolean destroyThirdPlatform(Object... args) {
        return false;
    }

    private final IPlayerInitCompleteListener onPlayerInitCompleteListener = new IPlayerInitCompleteListener() {
        @SuppressLint("LongLogTag")
        @Override
        public void onPlayerInitComplete(boolean b) {
            Log.i(TAG, "onPlayerInitComplete init " + b);
            try {
                if (mKRadioSystemSourceChangeInter != null) {
                    mKRadioSystemSourceChangeInter.registerSourceChanged();
                }
                PlayerManager.getInstance().removePlayerInitComplete(onPlayerInitCompleteListener);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    };
}