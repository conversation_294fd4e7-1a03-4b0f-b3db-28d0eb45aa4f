package com.kaolafm.kradio.uitl;

import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.kradio.common.bean.HistoryItem;
import com.kaolafm.sdk.core.mediaplayer.PlayItem;

/**
 * <pre>
 *     <AUTHOR> Wenchl
 *     e-mail : <EMAIL>
 *     time   : 2019/07/26
 *     desc   :
 *     version: 1.0
 * </pre>
 */
public class HistoryUtils {

    /**
     * 通过HistoryItem创建一个 PlayItem
     *
     * @param item
     * @return
     */
    public static PlayItem turnPlayItem(HistoryItem item) {
        PlayItem playItem = new PlayItem();

        if (!TextUtils.isEmpty(item.getRadioId())) {
            playItem.setAlbumId(Long.valueOf(item.getRadioId()));
        }
        String radioTitle = item.getRadioTitle();
        if (TextUtils.isEmpty(radioTitle)) {
            radioTitle = "暂无节目信息";
        }
        playItem.setAlbumName(radioTitle);
        playItem.setAlbumPic(item.getPicUrl());

        if (!TextUtils.isEmpty(item.getAudioId())) {
            playItem.setAudioId(Long.valueOf(item.getAudioId()));
        }
        playItem.setTitle(item.getAudioTitle());
        playItem.setPosition((int) item.getPlayedTime());
        playItem.setDuration((int) item.getDuration());
        playItem.setPlayUrl(item.getPlayUrl());
        playItem.setOrderNum((int) item.getOrderNum());

        playItem.setIsOffline(item.getIsOffline());
        playItem.setOfflinePlayUrl(item.getOfflinePlayUrl());
        playItem.setCategoryId(item.getCategoryId());
        Log.i("HistoryUtils", "turnPlayItem:"+ playItem.toString());
        return playItem;
    }


}
