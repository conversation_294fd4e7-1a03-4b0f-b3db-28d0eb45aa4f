package com.kaolafm.kradio.online.categories.tab;

import androidx.fragment.app.Fragment;

import com.kaolafm.kradio.common.SubcategoryItemBean;

import java.util.List;

/**
 * <AUTHOR>
 **/
public class TabContract {

    public static class TabItem {
        public String title;
        public long tabId;

        @Override
        public String toString() {
            return "TabItem{" +
                    "title='" + title + '\'' +
                    ", tabId=" + tabId +
                    '}';
        }
    }

    public interface IPresenter extends com.kaolafm.kradio.lib.base.mvp.IPresenter {

        void loadAIData(long showTabId);
        void loaCiData(long showTabId);
        void loadData(long showTabId);
    }

    public interface IView extends com.kaolafm.kradio.lib.base.mvp.IView {
        void showData(String[] titles,String[] ids, List<Fragment> fragments, int showIndex);

        void showContent(String[] titles, List<SubcategoryItemBean> itemBeans, int showIndex);

        void showError(Exception exception);
    }
}
