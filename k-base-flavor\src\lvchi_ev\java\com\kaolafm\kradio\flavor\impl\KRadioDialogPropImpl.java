package com.kaolafm.kradio.flavor.impl;

import android.view.Window;
import android.view.WindowManager;

import com.kaolafm.kradio.lib.base.flavor.KRadioDialogPropInter;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-08-22 12:07
 ******************************************/
public final class KRadioDialogPropImpl implements KRadioDialogPropInter {
    @Override
    public boolean addDialogWindowFlags(Object... args) {
        Window window = (Window) args[0];
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001222957580?userId=2169710问题
        window.setFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);
        return true;
    }

    @Override
    public boolean hideNavigationBar(Object... args) {
        return false;
    }
}
