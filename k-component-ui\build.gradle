import com.kaolafm.gradle.plugin.Util

apply plugin: 'micro-module'
apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'product-flavor'
//apply plugin: 'org.greenrobot.greendao'
apply from: '../aop.gradle'
apply from: '../test.gradle'

def and = rootProject.ext.android
def dependent = rootProject.ext.dependencies
def jpush = rootProject.ext.jpush
ext.alwaysLib = true

// 获取当前的git的commit_id
def getGitRevision() {
    return "git rev-parse --short HEAD".execute().text.trim()
}
// 给gradle.properties中的GITEST_COMMIT_ID赋值
GIT_COMMIT_ID = getGitRevision()

android {
    compileSdkVersion and.compileSdkVersion
    buildToolsVersion and.buildToolsVersion


    defaultConfig {
        renderscriptTargetApi 27 //must match target sdk and build tools
        renderscriptSupportModeEnabled true
        minSdkVersion and.minSdkVersion
        targetSdkVersion and.targetSdkVersion
        versionCode 1
        versionName "1.0"
        multiDexEnabled true
        manifestPlaceholders = [
                JPUSH_PKGNAME: jpush["jpush-pkgname"],
                JPUSH_APPKEY : jpush["jpush-appkey"], //JPush上注册的包名对应的appkey�?*换成你的*�?
                JPUSH_CHANNEL: jpush["jpush-channel"], //暂时填写默认值即�?.
        ]

       // testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
        ndk {
            abiFilters "armeabi"
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            buildConfigField "String", "BUILD_TIME", "\"${getBuildTime()}\""
            buildConfigField "String", "GIT_COMMIT_ID", "\"${GIT_COMMIT_ID}\""
        }
        debug {
            buildConfigField "String", "BUILD_TIME", "\"${getBuildTime()}\""
            buildConfigField "String", "GIT_COMMIT_ID", "\"${GIT_COMMIT_ID}\""
        }
        prelease.initWith(android.buildTypes.release)
    }
    compileOptions {
        sourceCompatibility and.javaSourceVersion
        targetCompatibility and.javaTargetVersion
    }
//    sourceSets {
//        main {
//            jniLibs.srcDirs = ['main/libs']
//        }
//    }
    dexOptions {
        javaMaxHeapSize "4g"
    }

}

def getBuildTime() {
    return new Date().format("yyMMddHHmm")
}

microModule {

    include ':base'

    def pattern = Util.getBuildPattern(rootProject)
    if (pattern.contains("comprehensive")) {
        include ':bigCard'
        include ':topicCard'
        include ':rotationCard'
        include ':activityCard'
        include ':2x3Card'
        include ':2x1Card'
        include ':1x1Card'
        include ':brandPageHomeCard'
    }

    if (pattern.contains("online")) {
        include ':ring'
    }
}


dependencies {
    annotationProcessor project(":annotation")
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    api project(':k-base-lib')


    api dependent["mediaCompat"]
    api dependent["lifecycle-runtime"]
    api dependent["lifecycle-extensions"]
    api dependent["persistence-room-runtime"]
    annotationProcessor dependent["persistence-room-compiler"]
    api dependent["persistence-room-rxjava2"]

    annotationProcessor dependent["glide-compiler"]

    api dependent["tablayout"]
//    api dependent["xpopup"]
    api ('com.google.android.flexbox:flexbox:3.0.0'){
        exclude group: 'org.jetbrains.kotlin'
    }

    api dependent["bugly_crashreport"]
    api dependent["bugly_nativecrashreport"]
    api dependent["skeleton"]
    api dependent["shimmerlayout"]
    api dependent['lottie']
    //换肤（由于BaseSkinAppCompatActivity需要被player和main同时引用，故将换肤所需依赖放到common项目下）
    api dependent['skin-support']
    api dependent['skin-support-appcompat']
    api dependent['skin-support-design']
    api dependent['skin-support-cardview']
    api dependent['skin-support-constraint']
}

