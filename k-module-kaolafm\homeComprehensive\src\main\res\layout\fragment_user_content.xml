<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_home"
    android:paddingTop="@dimen/m33">

    <LinearLayout
        android:id="@+id/fuc_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintTop_toTopOf="parent">

        <!--注意:为了对齐fragment,必须满足:tl_tab_padding + layout_marginLeft == x90-->
        <com.kaolafm.kradio.lib.widget.tab.SlidingTabLayout
            android:id="@+id/user_tab_title"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/y70"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/m80"
            app:kradio_tl_textSelectSize="@dimen/text_size5"
            app:kradio_tl_textSize="@dimen/text_size5"
            app:tl_textSelectSize="@dimen/text_size5"
            app:tl_textSize="@dimen/text_size5"
            app:tl_first_no_padding="true"
            app:tl_indicator_anim_enable="false"
            app:tl_indicator_color="@color/tab_indicator_underline_color"
            app:tl_indicator_height="@dimen/tab_indicator_height"
            app:tl_indicator_style="NORMAL"
            app:tl_indicator_width_equal_title="true"
            app:tl_tab_padding="@dimen/x10"
            app:tl_textSelectColor="@color/tab_indicator_select_color"
            app:tl_textUnselectColor="@color/tab_indicator_unselect_color" />

        <!--一键删除按钮-->
        <!--        <ImageView-->
        <!--            android:id="@+id/user_right_btn"-->
        <!--            android:layout_width="@dimen/x142"-->
        <!--            android:layout_height="@dimen/y52"-->
        <!--            android:layout_gravity="center_vertical"-->
        <!--            android:layout_marginRight="@dimen/history_right_btn_padding"-->
        <!--            android:visibility="invisible" />-->
        <View
            android:layout_width="@dimen/x1"
            android:layout_height="@dimen/y26"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="@dimen/x16"
            android:layout_marginRight="@dimen/x46"
            android:background="@color/tab_indicator_unselect_color" />
        <!--设置按钮-->
        <LinearLayout
            android:id="@+id/user_setting"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:contentDescription="@string/content_desc_setting"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="@dimen/m36"
                android:layout_height="@dimen/m36"
                android:background="@drawable/ic_user_setting" />

            <TextView
                android:id="@+id/settingBtn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="设置"
                android:textColor="@color/tab_indicator_unselect_color"
                android:textSize="@dimen/m28" />
        </LinearLayout>
    </LinearLayout>

    <!--遮罩,否则SlidingTabLayout的第一个条目会滑动超过左基准线-->
    <View
        android:id="@+id/zhezhao"
        android:layout_width="@dimen/m50"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/fuc_layout"
        app:layout_constraintLeft_toLeftOf="@+id/fuc_layout"
        app:layout_constraintTop_toTopOf="@+id/fuc_layout" />

    <View
        android:id="@+id/view_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/y1"
        android:layout_alignParentTop="true"
        android:background="@color/user_setting_view_divider"
        app:layout_constraintTop_toBottomOf="@+id/fuc_layout" />

    <com.kaolafm.kradio.common.widget.NotScrollViewPager
        android:id="@+id/user_viewpager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:overScrollMode="never"
        android:paddingLeft="@dimen/x1"
        android:paddingRight="@dimen/x1"
        android:layout_marginBottom="@dimen/m10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/view_divider" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/y20"
        android:layout_gravity="bottom"
        android:layout_marginStart="@dimen/m80"
        android:layout_marginEnd="@dimen/m80"
        android:background="@drawable/app_bottom_bg"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>