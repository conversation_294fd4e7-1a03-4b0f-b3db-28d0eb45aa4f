<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.kaolafm.kradio.lib.widget.square.SquareImageView
        android:id="@+id/iv_home_exit"
        android:layout_width="@dimen/home_back_bar_size"
        android:layout_height="@dimen/home_back_bar_size"
        android:background="@drawable/color_main_button_click_selector"
        android:contentDescription="@null"
        android:padding="@dimen/m23"
        android:scaleType="centerInside"
        android:src="@drawable/kradio_back"
        app:canScale="false"
        app:layout_constraintBottom_toTopOf="@id/space_home_back"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="spread" />

    <Space
        android:id="@+id/space_home_back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toTopOf="@id/iv_home_back"
        app:layout_constraintTop_toBottomOf="@id/iv_home_exit" />

    <com.kaolafm.kradio.lib.widget.square.SquareImageView
        android:id="@+id/iv_home_back"
        android:layout_width="@dimen/home_back_bar_size"
        android:layout_height="@dimen/home_back_bar_size"
        android:background="@drawable/color_main_button_click_selector"
        android:contentDescription="@null"
        android:padding="@dimen/m23"
        android:scaleType="centerInside"
        android:src="@drawable/kradio_home"
        app:canScale="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/space_home_back"
        app:layout_constraintVertical_chainStyle="spread" />
</merge>
