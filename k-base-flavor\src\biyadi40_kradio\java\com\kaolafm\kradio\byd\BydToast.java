package com.kaolafm.kradio.byd;

import android.content.Context;
import android.content.res.Configuration;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.utils.ResUtil;

/**
 * <AUTHOR>
 **/
public class BydToast {
    private static Toast toast;

    public static void show(Context context, String msg, int lengthShort) {
        View view = ((LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE)).inflate(R.layout.hw_toast_layout, null, false);

        TextView text = (TextView) view.findViewById(R.id.toast_msg);
        text.setText(msg);

        //解决https://app.huoban.com/tables/2100000007530121/items/2300001672042057?userId=1229522问题
        if (toast == null) {
            toast = new Toast(context);
        } else {
            toast.cancel();
            toast = null;
            toast = new Toast(context);
        }

        boolean isPortrait = ResUtil.getOrientation() == Configuration.ORIENTATION_PORTRAIT;
        if (isPortrait) {
            toast.setGravity(Gravity.TOP, 0, 1276);
        } else {
            toast.setGravity(Gravity.TOP, 0, 686);
        }
        toast.setDuration(Toast.LENGTH_SHORT);
        toast.setView(view);
        toast.show();
    }

    public static void release() {
        toast = null;
    }
}
