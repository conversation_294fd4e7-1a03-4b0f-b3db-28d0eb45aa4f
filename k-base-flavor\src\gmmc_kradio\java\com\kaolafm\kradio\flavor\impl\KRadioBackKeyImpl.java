package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;

import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.base.flavor.KRadioBackKeyInter;
import com.kaolafm.kradio.lib.dialog.CommonBottomDialog;
import com.kaolafm.kradio.lib.dialog.DialogListener;

import static com.kaolafm.kradio.lib.utils.ViewUtil.getActivityFromView;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-04-24 16:02
 ******************************************/
public final class KRadioBackKeyImpl implements KRadioBackKeyInter {
    @Override
    public boolean onBackPressed(Object... args) {
        final Activity activity = (Activity) args[0];
        activity.moveTaskToBack(true);
        return true;
    }
}
