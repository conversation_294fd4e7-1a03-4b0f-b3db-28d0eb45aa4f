package com.kaolafm.kradio.flavor.carnetwork.carnetworkutils;

public class ChanganAuthConstants {

    public static String BASEURL = "http://auth.tboss.cu-sc.com:10010";
    public final static String BASEURL_TEST = "http://auth.test-tboss.cu-sc.com:10010";
    public final static String BASEURL_OFFICIAL = "http://auth.tboss.cu-sc.com:10010";

    public final static String REQUEST_GET_AUTH = "/changan/service/authv2/{json}";

    public final static String CHANGAN_APPID = "CUCAKAOLA1";
    public final static String CHANGAN_ITEMID = "CUCKAOLA";

    //长安返回的code码值
    /**
     * code	message	说明
     * 1   成功
     * 0
     * -1	access_token+”:”+vid	需订购产品
     * -2	“access_token:”+聚合平台失败原因	获取access_token失败
     * -3	“access_token”字符串	获取access_token返回时间大于30秒
     * -4	access_token字段内容	access_token过期
     * -5	“vid”字符串	鉴权请求中vid字段为空
     * -6	“vid”字符串	通过vid获取vin返回时间大于30秒
     * -7	vid字段内容	鉴权请求中vid字段过期
     * -8	vid字段内容+”:”+聚合平台失败原因	通过vid获取vin返回程序错误
     * -9	message	其它原因
     */
//    public final static int CODE_SUCCESS = 1;
//    public final static int CODE_ZERO = 0;
    public final static int CODE_MINUS_ONE = -1;
    public final static int CODE_MINUS_TWO = -2;
    public final static int CODE_MINUS_THREE = -3;
    public final static int CODE_MINUS_FOUR = -4;
    public final static int CODE_MINUS_FIVE = -5;
    public final static int CODE_MINUS_SIX = -6;
    public final static int CODE_MINUS_SEVEN = -7;
    public final static int CODE_MINUS_EIGHT = -8;
    public final static int CODE_MINUS_NINE = -9;

    //自定义的code
    public final static int CODE_LOADING = 98;//loading状态
    public final static int CODE_TIMEOUT = 97;//超时状态
    public final static int CODE_ERROR = 96;//错误状态
    public final static int CODE_CONNECTFAILED = 95;//车机系统与平台连接失败。

    //长安提示语内容
//    public final static String DIALOG_TEXT_SHOPPING = "您暂未购买该产品，或产品已过有效期，请至服务商城购买。";
//    public final static String DIALOG_TEXT_NETWORKFAILED = "网络超时，请稍后重试。";
//    public final static String DIALOG_TEXT_NOCARINFO = "车辆信息不完整，服务认证失败，请联系购车经销商处理或拔打热线4008886677";


}
