package com.kaolafm.kradio.flavor.impl;

import android.util.Log;

import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.base.flavor.KRadioConvertMediaKeyCodeInter;
import com.kaolafm.kradio.lib.utils.AntiShake;

public class KRadioConvertMediaKeyCodeImpl implements KRadioConvertMediaKeyCodeInter {

    public static final String TAG = "ConvertMediaKeyCode";

    @Override
    public int convertKeyCode(int originKeycode) {
        if (AntiShake.check(1000, AppManager.getInstance())) { //见KRadioMediaKeyEventImpl
            Log.w(TAG, "1000ms内 执行过了onKeyUp, return");
            return -1; //如果已经执行过了，就不再执行了
        } else {
            Log.w(TAG, "mediaButton执行" + originKeycode);
            return originKeycode;
        }
    }
}
