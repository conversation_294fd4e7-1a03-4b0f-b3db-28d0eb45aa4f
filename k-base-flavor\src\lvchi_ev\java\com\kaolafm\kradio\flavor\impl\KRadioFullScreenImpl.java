package com.kaolafm.kradio.flavor.impl;

import com.kaolafm.kradio.lib.base.flavor.KRadioFullScreenInter;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-08-01 16:08
 ******************************************/
public final class KRadioFullScreenImpl implements KRadioFullScreenInter {

    @Override
    public boolean initFullScreen(Object... args) {
//        Activity activity = (Activity) args[0];
//        if (Build.VERSION.SDK_INT > 11 && Build.VERSION.SDK_INT < 19) { // lower api
//            View v = activity.getWindow().getDecorView();
//            v.setSystemUiVisibility(View.GONE);
//        } else if (Build.VERSION.SDK_INT >= 19) {
//            View decorView = activity.getWindow().getDecorView();
//            int uiOptions = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
//                    | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY;
//            decorView.setSystemUiVisibility(uiOptions);
//        }
        return true;
    }

    @Override
    public boolean hideNavBarOnResume(Object... args) {
//        boolean flag = initFullScreen(args);
        return true;
    }
}
