package com.kaolafm.kradio.online.common.playbar;

import android.app.Activity;
import androidx.lifecycle.LifecycleOwner;
import android.graphics.drawable.Drawable;
import androidx.annotation.StringRes;
import android.view.View;

import com.kaolafm.kradio.subscribe.SubscribeModel;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: PlayerbarContractOnline.java
 *                                                                  *
 * Created in 2018/4/16 下午6:14                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
public class OnlinePlayerbarContract {


    public interface IPlayerView extends LifecycleOwner {
        void setCoverImageDrawable(Drawable drawable);


        void setCoverImageUrl(String coverUrl);


        void setTitle(CharSequence title);


        void setSubtitle(CharSequence title);

        /**
         * @param progress 1~100
         */
        @Deprecated
        void updateProgress(int progress);

        void updateProgress(int position, int duration);

        void showLoading(boolean show);


        void showPauseState();


        void showPlayState();

        void showBroadcastState();

        void showNormalState();

        /**
         * 收藏
         *
         * @param isCollect
         */
        void setCollectState(boolean isCollect);


        void setCollectClickListener(View.OnClickListener listener);


        void setNextClickListener(View.OnClickListener listener);

        void setPreClickListener(View.OnClickListener listener);

        void setPlayOrPauseClickListener(View.OnClickListener listener);


        void setPrevState(boolean hasPre);

        void setNextState(boolean hasNext);

        void showToast(String info);

        void showLiveState();

        void updateInfo(int cp, PlayItem playItem);

        void showError(@StringRes int player_failure);

        void setEnabled(boolean b);

        void setProgressEnabled(boolean isEnabled);
        void setProgressVisibility(boolean visible);

        void updateBroadcastErrorInfo();

        void showPlayBarType();

        Activity getRootActivity();

        boolean getCollectState();

    }

    public interface IPlayerPresenter {

        void attachPlayer();

        void detachPlayer();

        void attachSubscribeModel(SubscribeModel subscribeModel);

        void detachSubscribeModel();

    }
}
