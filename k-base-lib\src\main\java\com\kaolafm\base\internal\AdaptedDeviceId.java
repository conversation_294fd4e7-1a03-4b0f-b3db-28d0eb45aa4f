package com.kaolafm.base.internal;

/**
 * <AUTHOR>
 * @date 2020/9/1
 */
public class AdaptedDeviceId extends CacheObtainDeviceId {


    private static volatile AdaptedDeviceId mInstance;

    public static AdaptedDeviceId getInstance() {
        if (mInstance == null) {
            synchronized (AdaptedDeviceId.class) {
                if (mInstance == null) {
                    mInstance = new AdaptedDeviceId();
                }
            }
        }
        return mInstance;
    }

    private AdaptedDeviceId() {
    }
}
