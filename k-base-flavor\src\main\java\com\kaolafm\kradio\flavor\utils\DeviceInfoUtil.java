package com.kaolafm.kradio.flavor.utils;

import android.os.Build;
import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.base.internal.DeviceIdUtil;
import com.kaolafm.kradio.common.KradioSDKManager;
import com.kaolafm.kradio.common.http.vehicle.KlSdkVehicle;
import com.kaolafm.kradio.lib.base.AppDelegate;

/**
 * <AUTHOR> @date 2019-11-15
 */
public class DeviceInfoUtil {
    private static final String TAG = "DeviceInfoUtil";

    public static void setDeviceIdAndCarType(String deviceId, String carType) {
        Log.i(TAG, "setDeviceIdAndCarType: deviceid = " + deviceId + ", carType = " + carType);
        if (TextUtils.isEmpty(carType)) {
            KlSdkVehicle.getInstance().setCarType(Build.DEVICE);
        } else {
            KlSdkVehicle.getInstance().setCarType(carType);
        }
      if (!TextUtils.isEmpty(deviceId)) {
            try {
                String oldDeviceId = DeviceIdUtil.getDeviceId(AppDelegate.getInstance().getContext());
                Log.i(TAG, "setDeviceIdAndCarType: oldDeviceId = " + oldDeviceId + " nowDeviceId = " + deviceId);
                if (!oldDeviceId.equals(deviceId)) {
                    KradioSDKManager.getInstance().setNeedClearToken(true);
                }
                DeviceIdUtil.setDeviceId(deviceId);
            } catch (Exception e) {
                Log.e(TAG, " " + e);
            }
        }
    }
}
