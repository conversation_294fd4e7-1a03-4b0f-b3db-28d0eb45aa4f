package com.kaolafm.kradio.flavor.impl;

import android.annotation.SuppressLint;
import android.util.Log;


import com.kaolafm.kradio.lib.base.flavor.KRadioAudioPlayLogicInter;

import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-08-28 16:17
 ******************************************/
public final class KRadioAudioPlayLogicImpl implements KRadioAudioPlayLogicInter {
    private static final String TAG = "KRadioAudioPlayLogicImpl";

    @SuppressLint("LongLogTag")
    @Override
    public boolean autoPlayAudio(Object... args) {
//        Context context = (Context) args[0];
//        SystemBootUtil systemBootUtil = new SystemBootUtil();
//        boolean flag = systemBootUtil.isFirstBoot(context);
//        Log.i(TAG, "autoPlayAudio:   flag = " + flag);
//        if (flag) {
//            systemBootUtil.updateFirstBoot(context, false);
//        return true;
//        }
        PlayerUtil.playDefaultMediaForChannel();
        return true;
    }

    @Override
    public boolean requestAudioFocus(Object... args) {
        boolean flag = PlayerManager.getInstance().requestAudioFocus();
        return flag;
    }

    @Override
    public boolean resumeAudioPlayLogic(Object... args) {
        recoverPlay();
        return true;
    }

    @Override
    public boolean restartAudioPlayLogic(Object... args) {
        return false;
    }

    @Override
    public boolean doStartInPlay(Object... args) {
        return false;
    }

    @SuppressLint("LongLogTag")
    private void recoverPlay() {
        PlayerManager playerManager = PlayerManager.getInstance();

        Log.i(TAG, "recoverPlay---------->PlayerManager.isPausedFromUser() = " + playerManager.isPauseFromUser()
                + "          PlayerManager.getCurrentAudioFocusStatus() = " + playerManager.getCurrentAudioFocusStatus());
        if (playerManager.isPauseFromUser()) {
            if (playerManager.getCurrentAudioFocusStatus() < 0) {
                requestAudioFocus();
            }
            return;
        }
        if (playerManager.getCurrentAudioFocusStatus() >0) {
            return;
        }
        requestAudioFocus();
        if (!playerManager.isPlaying()) {
            PlayerManagerHelper.getInstance().switchPlayerStatus(false);
        }
    }
}
