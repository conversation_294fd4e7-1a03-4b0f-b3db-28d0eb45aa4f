<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/search_main_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/online_search_fragment_bg">
    <ImageView
        android:id="@+id/backViewBt"
        android:layout_width="@dimen/m76"
        android:layout_height="@dimen/m76"
        android:layout_marginStart="@dimen/x50"
        android:padding="@dimen/m20"
        android:src="@drawable/online_player_ic_back"
        app:layout_constraintBottom_toBottomOf="@id/cl_search_view"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/cl_search_view"
        tools:ignore="MissingConstraints" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_search_view"
        android:layout_width="0dp"
        android:layout_height="@dimen/online_search_edit_height"

        android:layout_marginTop="@dimen/y32"
        android:layout_marginEnd="@dimen/x114"
        android:background="@drawable/online_search_edittext_bg"
        app:layout_constraintEnd_toStartOf="@id/tv_search"
        app:layout_constraintStart_toEndOf="@id/backViewBt"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="NotSibling">

        <ImageView
            android:id="@+id/ts_search_type"
            android:layout_width="@dimen/m28"
            android:layout_height="@dimen/m28"
            android:layout_marginStart="@dimen/m20"
            android:src="@drawable/online_top_search_icon_1"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <EditText
            android:id="@+id/et_search_word"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@null"
            android:hint="@string/online_location_search_hint"
            android:imeOptions="actionSearch|flagNoExtractUi"
            android:paddingStart="@dimen/x68"
            android:paddingEnd="@dimen/x10"
            android:selectAllOnFocus="true"

            android:singleLine="true"
            android:textColor="@color/online_search_edittext_color"
            android:textColorHint="@color/online_search_text_hint_color"
            android:textCursorDrawable="@drawable/online_search_cursor_drawable"
            android:textSize="@dimen/text_size4"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/iv_search_word_delete"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.0" />

        <ImageView
            android:id="@+id/iv_search_word_delete"
            android:layout_width="@dimen/m36"
            android:layout_height="@dimen/m36"
            android:layout_marginTop="@dimen/y10"
            android:layout_marginEnd="@dimen/x20"
            android:layout_marginBottom="@dimen/y10"
            android:src="@drawable/online_search_icon_delete_search"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="W,1:1"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_search"
        android:layout_width="@dimen/x140"
        android:layout_height="@dimen/y56"
        android:layout_marginStart="@dimen/x28"
        android:layout_marginTop="@dimen/y32"
        android:layout_marginEnd="@dimen/x56"
        android:background="@drawable/online_search_button_bg"
        android:gravity="center"
        android:text="@string/online_search"
        android:textColor="@color/colorBlack"
        android:textSize="@dimen/m24"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/cl_search_view"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/cl_search_view"
        app:layout_constraintTop_toTopOf="parent" />

    <ScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/m36"
        android:scrollbars="none"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@+id/cl_search_view"
        app:layout_constraintRight_toRightOf="@+id/cl_search_view"
        app:layout_constraintTop_toBottomOf="@id/cl_search_view">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:id="@+id/tv_search_ding_ll"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.kaolafm.kradio.component.ui.base.view.KradioTextView
                    android:id="@+id/tv_search_ding"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/online_location_ding"
                    android:textColor="@color/online_search_history_text_color"
                    android:textSize="@dimen/text_size5"
                    app:kt_font_weight="0.7"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/tv_ding_name"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/y48"
                    android:layout_marginTop="@dimen/m24"
                    android:background="@drawable/online_bg_search_history"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:minWidth="@dimen/x70"
                    android:paddingLeft="@dimen/x28"
                    android:paddingRight="@dimen/x28"
                    android:singleLine="true"
                    android:textColor="@color/online_search_associate_text_color"
                    android:textSize="@dimen/m24"
                    tools:text="云听资讯" />
            </LinearLayout>

            <com.kaolafm.kradio.component.ui.base.view.KradioTextView
                android:id="@+id/tv_search_history"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/m40"
                android:text="@string/online_location_search_history"
                android:textColor="@color/online_search_history_text_color"
                android:textSize="@dimen/text_size5"
                app:kt_font_weight="0.7"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_search_ding_ll"
                tools:visibility="visible" />

            <ImageView
                android:id="@+id/tv_clear_search_history"
                android:layout_width="@dimen/m138"
                android:layout_height="@dimen/m48"
                android:src="@drawable/online_ic_search_clear_selector"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/tv_search_history"
                app:layout_constraintRight_toRightOf="parent" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_search_history_tags"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/m24"
                android:overScrollMode="never"
                android:visibility="gone"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_search_history"
                tools:visibility="visible" />

            <com.kaolafm.kradio.component.ui.base.view.KradioTextView
                android:id="@+id/tv_hot_search_words"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/m20"
                android:text="@string/online_location_hot_search_words"
                android:textColor="@color/online_search_history_text_color"
                android:textSize="@dimen/text_size5"
                app:kt_font_weight="0.7"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/rv_search_history_tags"
                tools:visibility="visible" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_hot_search_words"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/m24"
                android:overScrollMode="never"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_hot_search_words" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_associate_list"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:overScrollMode="never"
        android:paddingTop="@dimen/y7"
        android:paddingBottom="@dimen/y7"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@id/cl_search_view"
        app:layout_constraintRight_toRightOf="@id/cl_search_view"
        app:layout_constraintTop_toBottomOf="@id/cl_search_view" />

    <include
        android:id="@+id/search_loading"
        layout="@layout/online_refresh_center"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ViewStub
        android:id="@+id/vs_search_exception"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout="@layout/online_search_result_exception_layout"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ViewStub
        android:id="@+id/vs_search_network_error"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout="@layout/online_error_layout" />

</androidx.constraintlayout.widget.ConstraintLayout>