package com.kaolafm.kradio.common.widget;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;

import com.kaolafm.kradio.k_kaolafm.R;
import com.lcodecore.tkrefreshlayout.IBottomView;
import com.lcodecore.tkrefreshlayout.IHeaderView;
import com.lcodecore.tkrefreshlayout.OnAnimEndListener;

import java.lang.ref.WeakReference;
import java.util.concurrent.atomic.AtomicBoolean;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2017-09-07 08:27
 ******************************************/

public final class CustomerRefreshHeader implements IHeaderView {
    //    private TextView mRefreshTextView;
    private WeakReference<Context> mWeakReference;

    private CustomerRefreshBottom.OnVisibilityChanged mOnVisibilityChanged;
    private AtomicBoolean isVisibility = new AtomicBoolean(false);

    public CustomerRefreshHeader(Context context) {
        mWeakReference = new WeakReference<>(context);
    }

    @Override
    public View getView() {
        Context context = mWeakReference.get();
        if (context == null) {
            return null;
        }
        LayoutInflater layoutInflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View refreshLayout = layoutInflater.inflate(R.layout.refresh_bottom, null);
//        mRefreshTextView = (TextView) refreshLayout.findViewById(R.id.refresh_bottom_textView);
        return refreshLayout;
    }

    @Override
    public void onPullingDown(float v, float v1, float v2) {
        if (isVisibility.compareAndSet(false, true)) {
            if (mOnVisibilityChanged != null) {
                mOnVisibilityChanged.onVisibilityChanged(true);
            }
        }
    }

    @Override
    public void startAnim(float maxBottomHeight, float bottomHeight) {
    }

    @Override
    public void onFinish(OnAnimEndListener onAnimEndListener) {
        onAnimEndListener.onAnimEnd();
        if (isVisibility.compareAndSet(true, false)) {
            if (mOnVisibilityChanged != null) {
                mOnVisibilityChanged.onVisibilityChanged(false);
            }
        }
    }

    @Override
    public void onPullReleasing(float fraction, float maxBottomHeight, float bottomHeight) {
        if (fraction == 0 && isVisibility.compareAndSet(true, false) ) {
            if (mOnVisibilityChanged != null) {
                mOnVisibilityChanged.onVisibilityChanged(false);
            }
        }
    }

    @Override
    public void reset() {
    }

    public CustomerRefreshBottom.OnVisibilityChanged getOnVisibilityChanged() {
        return mOnVisibilityChanged;
    }

    public void setOnVisibilityChanged(CustomerRefreshBottom.OnVisibilityChanged mOnVisibilityChanged) {
        this.mOnVisibilityChanged = mOnVisibilityChanged;
    }

    interface OnVisibilityChanged {
        void onVisibilityChanged(boolean visible);
    }

}
