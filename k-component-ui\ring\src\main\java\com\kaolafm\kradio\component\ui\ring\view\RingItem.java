package com.kaolafm.kradio.component.ui.ring.view;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.airbnb.lottie.LottieAnimationView;
import com.kaolafm.kradio.component.ui.R;
import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.component.ui.base.skin.SkinHelper;
import com.kaolafm.kradio.lib.BuildConfig;
import com.kaolafm.kradio.lib.common.ContentType;
import com.kaolafm.kradio.lib.common.ResType;
import com.kaolafm.kradio.lib.player.contant.BroadcastStatus;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.component.ui.base.view.KradioTextView;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

/**
 * <AUTHOR> shiqian
 * @date 2022-07-14
 */
public class RingItem extends FrameLayout {
    private LottieAnimationView lt_bg;
    private ImageView iv_covert, iv_bg, iv_tag;
    private KradioTextView tv_title;
    private TextView tv_sub, tv_type;
    private int mDataIndex = -1;
    private boolean mIsSelect = false;
    private int mResType = ResType.NOACTION_TYPE;
    private int mContentType = ContentType.NOACTION_TYPE;
    private String mSkinName = "";
    /**
     * 是否开启光圈动画
     */
    private boolean mAnimEnable = BuildConfig.IS_PLAY_ANIM;

    private HomeCell mHomeCell;

    public RingItem(Context context) {
        this(context, null);
    }

    public RingItem(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public RingItem(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    void init(Context context) {
        // 加载布局
        LayoutInflater.from(context).inflate(R.layout.ring_item, this);
        iv_covert = findViewById(R.id.iv_covert);
        lt_bg = findViewById(R.id.lt_bg);
        iv_bg = findViewById(R.id.iv_bg);
        tv_title = findViewById(R.id.tv_title);
        iv_tag = findViewById(R.id.iv_tag);
        tv_sub = findViewById(R.id.tv_sub);
        tv_type = findViewById(R.id.tv_type);
        if (mAnimEnable) {
            lt_bg.setImageAssetsFolder("lottie/ring/images");
            lt_bg.loop(true);
        }
    }

    void setDataIndex(int dataIndex) {
        mDataIndex = dataIndex;
    }

    int getDataIndex() {
        return mDataIndex;
    }

    public void setItem(HomeCell homeCell) {
        mHomeCell = homeCell;
        showData(homeCell);
    }

    public HomeCell getItem() {
        return mHomeCell;
    }

    private void showData(HomeCell homeCell) {
        if (homeCell == null) {
            return;
        }
        setTextStytle();
        tv_title.setText(StringUtil.getMaxString(StringUtil.replaceBlank(homeCell.name), 6));
        if (!TextUtils.isEmpty(homeCell.recommendReason))
            tv_sub.setText(StringUtil.getMaxString(homeCell.recommendReason, 12));
        else tv_sub.setText("");
        if (homeCell.resType == ResType.ALBUM_TYPE) {
            tv_type.setText("音频集");
        } else if (homeCell.resType == ResType.RADIO_TYPE) {
            tv_type.setText("AI电台");
        } else if (homeCell.resType == ResType.TV_TYPE) {
            tv_type.setText("听电视");
        } else if (homeCell.resType == ResType.LIVE_TYPE) {
            tv_type.setText("直播间");
            if (!StringUtil.isEmpty(homeCell.getAnchor()))
                tv_sub.setText(StringUtil.getMaxString("主播：" + homeCell.getAnchor(), 12));
            else tv_sub.setText("");
        } else if (homeCell.resType == ResType.BROADCAST_TYPE) {
            tv_type.setText(homeCell.getFreq());
        } else {
            tv_type.setText("");
        }
        if (homeCell.resType == ResType.ALBUM_TYPE
                || homeCell.resType == ResType.RADIO_TYPE) {
            ImageLoader.getInstance().displayCircleImage(getContext(), homeCell.imageUrl, iv_covert);
            iv_covert.setVisibility(VISIBLE);
        } else {
            iv_covert.setVisibility(GONE);
        }

        setVipCorner(homeCell);
        setRingBg(homeCell);
    }

    private void setRingBg(HomeCell homeCell) {
        if (mResType == homeCell.resType
                && mContentType == homeCell.contentType
                && mSkinName.equals(SkinHelper.getCurSkinName())) {
            return;
        }

        if (mAnimEnable) {
            if (homeCell.resType == ResType.ALBUM_TYPE) {
                iv_bg.setBackground(ResUtil.getDrawable(R.drawable.ring_bg_audio_set_unselected));
                lt_bg.setAnimation("lottie/ring/demo.json");
            } else if (homeCell.resType == ResType.RADIO_TYPE) {
                iv_bg.setBackground(ResUtil.getDrawable(R.drawable.ring_bg_radio_unselected));
                lt_bg.setAnimation("lottie/ring/demo.json");
            } else if (homeCell.resType == ResType.BROADCAST_TYPE) {
                if (homeCell.contentType == ContentType.BROADCAST_NEWS_TYPE) {
                    iv_bg.setBackground(ResUtil.getDrawable(R.drawable.ring_bg_broadcast_news_unselected));
                    lt_bg.setAnimation("lottie/ring/demo.json");
                } else if (homeCell.contentType == ContentType.BROADCAST_TRAFFIC_TYPE) {
                    iv_bg.setBackground(ResUtil.getDrawable(R.drawable.ring_bg_broadcast_traffic_unselected));
                    lt_bg.setAnimation("lottie/ring/demo.json");
                } else if (homeCell.contentType == ContentType.BROADCAST_MUSSIC_TYPE) {
                    iv_bg.setBackground(ResUtil.getDrawable(R.drawable.ring_bg_broadcast_music_unselected));
                    lt_bg.setAnimation("lottie/ring/demo.json");
                } else {
                    iv_bg.setBackground(ResUtil.getDrawable(R.drawable.ring_bg_broadcast_common_unselected));
                    lt_bg.setAnimation("lottie/ring/demo.json");
                }
            } else if (homeCell.resType == ResType.TV_TYPE) {
                iv_bg.setBackground(ResUtil.getDrawable(R.drawable.ring_bg_tv_listen_unselected));
                lt_bg.setAnimation("lottie/ring/demo.json");
            } else if (homeCell.resType == ResType.LIVE_TYPE) {
                iv_bg.setBackground(ResUtil.getDrawable(R.drawable.ring_bg_tv_listen_unselected));
                lt_bg.setAnimation("lottie/ring/demo.json");
            } else {
                iv_bg.setBackground(ResUtil.getDrawable(R.drawable.ring_bg_broadcast_common_unselected));
                lt_bg.setAnimation("lottie/ring/demo.json");
            }
        } else {
            if (homeCell.resType == ResType.ALBUM_TYPE) {
                iv_bg.setBackground(ResUtil.getDrawable(R.drawable.ring_bg_audio_set_unselected));
                lt_bg.setBackground(ResUtil.getDrawable(R.drawable.ring_bg_audio_set_selected));
            } else if (homeCell.resType == ResType.RADIO_TYPE) {
                iv_bg.setBackground(ResUtil.getDrawable(R.drawable.ring_bg_radio_unselected));
                lt_bg.setBackground(ResUtil.getDrawable(R.drawable.ring_bg_radio_selected));
            } else if (homeCell.resType == ResType.BROADCAST_TYPE) {
                if (homeCell.contentType == ContentType.BROADCAST_NEWS_TYPE) {
                    iv_bg.setBackground(ResUtil.getDrawable(R.drawable.ring_bg_broadcast_news_unselected));
                    lt_bg.setBackground(ResUtil.getDrawable(R.drawable.ring_bg_broadcast_news_selected));
                } else if (homeCell.contentType == ContentType.BROADCAST_TRAFFIC_TYPE) {
                    iv_bg.setBackground(ResUtil.getDrawable(R.drawable.ring_bg_broadcast_traffic_unselected));
                    lt_bg.setBackground(ResUtil.getDrawable(R.drawable.ring_bg_broadcast_traffic_selected));
                } else if (homeCell.contentType == ContentType.BROADCAST_MUSSIC_TYPE) {
                    iv_bg.setBackground(ResUtil.getDrawable(R.drawable.ring_bg_broadcast_music_unselected));
                    lt_bg.setBackground(ResUtil.getDrawable(R.drawable.ring_bg_broadcast_music_selected));
                } else {
                    iv_bg.setBackground(ResUtil.getDrawable(R.drawable.ring_bg_broadcast_common_unselected));
                    lt_bg.setBackground(ResUtil.getDrawable(R.drawable.ring_bg_broadcast_common_selected));
                }
            } else if (homeCell.resType == ResType.TV_TYPE) {
                iv_bg.setBackground(ResUtil.getDrawable(R.drawable.ring_bg_tv_listen_unselected));
                lt_bg.setBackground(ResUtil.getDrawable(R.drawable.ring_bg_tv_listen_selected));
            } else if (homeCell.resType == ResType.LIVE_TYPE) {
                iv_bg.setBackground(ResUtil.getDrawable(R.drawable.ring_bg_tv_listen_unselected));
                lt_bg.setBackground(ResUtil.getDrawable(R.drawable.ring_bg_tv_listen_selected));
            } else {
                iv_bg.setBackground(ResUtil.getDrawable(R.drawable.ring_bg_broadcast_common_unselected));
                lt_bg.setBackground(ResUtil.getDrawable(R.drawable.ring_bg_broadcast_common_selected));
            }
        }
        mResType = homeCell.resType;
        mContentType = homeCell.contentType;
        mSkinName = SkinHelper.getCurSkinName();

    }

    /**
     * 设置显示的角标，显示精品或者vip
     */
    public void setVipCorner(HomeCell homeCell) {
        if (iv_tag == null) {
            return;
        }
        int icon = 0;
        //fine==1代表精品
        if (1 == homeCell.fine) {
            icon = R.drawable.icon_supreme_home;
        } else if (1 == homeCell.vip) {
            icon = R.drawable.icon_vip_home;
        }
        if (icon != 0) {
            iv_tag.setVisibility(View.VISIBLE);
            iv_tag.setImageResource(icon);
        } else {
            iv_tag.setImageDrawable(null);
            iv_tag.setVisibility(View.INVISIBLE);
            getPlayFlagType(homeCell);
        }
    }

    public void getPlayFlagType(HomeCell homeCell) {
        int type = homeCell.resType;
        switch (type) {
            case PlayerConstants.RESOURCES_TYPE_LIVING:
            case PlayerConstants.RESOURCES_TYPE_LIVE_STREAM:
            case PlayerConstants.RESOURCES_TYPE_TV:
            case PlayerConstants.RESOURCES_TYPE_BROADCAST: {
                PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
                if (playItem.getAlbumId().equals(homeCell.playId + "")) {
                    if (playItem.isLiving()) {
                        setLivingFlag();
                    } else {
                        if (playItem.getStatus() == BroadcastStatus.BROADCAST_STATUS_PLAYBACK) {
                            setPlaybackFlag();
                        } else {
                            iv_tag.setVisibility(View.INVISIBLE);
                        }
                    }
                } else {
                    setLivingFlag();
                }
            }
            break;
            default:
                iv_tag.setVisibility(View.INVISIBLE);
                break;
        }
    }

    private void setLivingFlag() {
        //显示直播角标
        iv_tag.setImageResource(R.drawable.ic_live_s_home);
        if (iv_tag.getVisibility() != View.VISIBLE) {
            iv_tag.setVisibility(View.VISIBLE);
        }
    }

    private void setPlaybackFlag() {
        //显示回放角标
        iv_tag.setImageResource(R.drawable.ic_playback_s_home);
        if (iv_tag.getVisibility() != View.VISIBLE) {
            iv_tag.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 执行动画 - CPU优化：已禁用动画以提升性能
     */
    void playAnim() {
        // CPU优化：禁用动画以降低CPU使用率
        // if (mAnimEnable && mIsSelect) {
        //     lt_bg.playAnimation();
        // }
    }

    /**
     * 暂停动画 - CPU优化：已禁用动画以提升性能
     */
    void pauseAnim() {
        // CPU优化：禁用动画以降低CPU使用率
        // lt_bg.pauseAnimation();
    }

    /**
     * 设置当前RingItem是否被选中
     *
     * @param isSelect
     */
    void onSelectChanged(boolean isSelect) {
        mIsSelect = isSelect;
        if (isSelect) {
            bringToFront();
            visibiletyAnim(true);
        } else {
            visibiletyAnim(false);
        }
    }

    private void visibiletyAnim(boolean visibility) {
        float endAlphaValue = 0.0f;
        float endAlphaValue2 = 1.0f;//背景光圈和选中光圈的显示是反着的
        if (visibility) {
            endAlphaValue = 1.0f;
            endAlphaValue2 = 0.0f;
        }
        AnimatorSet mAnimatorSet = new AnimatorSet();
        ObjectAnimator mObjectAnimatorAlpha1 = ObjectAnimator.ofFloat(this.lt_bg, "alpha", this.lt_bg.getAlpha(), endAlphaValue);
        ObjectAnimator mObjectAnimatorAlpha2 = ObjectAnimator.ofFloat(this.tv_sub, "alpha", this.tv_sub.getAlpha(), endAlphaValue);
        ObjectAnimator mObjectAnimatorAlpha3 = ObjectAnimator.ofFloat(this.iv_bg, "alpha", this.iv_bg.getAlpha(), endAlphaValue2);
        mAnimatorSet.playTogether(mObjectAnimatorAlpha1, mObjectAnimatorAlpha2,mObjectAnimatorAlpha3);
        mAnimatorSet.setDuration(1000);
        mAnimatorSet.start();
    }

    private void setTextStytle() {
        if (mIsSelect) {
            tv_title.setGradientColor(ResUtil.getColor(R.color.color_ring_item_text_title_selected_start), ResUtil.getColor(R.color.color_ring_item_text_title_selected_end), 270);
            tv_sub.setTextColor(ResUtil.getColor(R.color.color_ring_item_text_sub_selected));
        } else {
            tv_title.setGradientColor(ResUtil.getColor(R.color.color_ring_item_text_title_normal_start), ResUtil.getColor(R.color.color_ring_item_text_title_normal_end), 270);
            tv_sub.setTextColor(ResUtil.getColor(R.color.color_ring_item_text_sub_normal));
        }
    }

    @Override
    public void invalidate() {
        super.invalidate();
        setItem(mHomeCell);
    }
}
