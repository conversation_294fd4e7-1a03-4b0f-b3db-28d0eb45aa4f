package com.kaolafm.kradio.search;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.Configuration;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;
import androidx.fragment.app.DialogFragment;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.text.Editable;
import android.text.SpannableString;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.style.ClickableSpan;
import android.util.Log;
import android.util.TypedValue;
import android.view.ActionMode;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewStub;
import android.view.ViewTreeObserver;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.google.android.flexbox.FlexboxLayoutManager;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.common.base.BaseSkinAppCompatActivity;
import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.kradio.lib.bean.BroadcastRadioSimpleData;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.utils.CommonUtils;
import com.kaolafm.kradio.common.widget.refresh.KradioSmartRefreshLayout;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.home.comprehensive.playerbar.ComprehensivePlayerHelper;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioMultiWindowInter;
import com.kaolafm.kradio.lib.base.flavor.UserCenterInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioSearchHideInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioSearchInter;
import com.kaolafm.kradio.lib.dialog.Dialogs;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkMonitor;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.RecyclerViewExposeUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.SoftKeyboardHelper;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.home.comprehensive.ui.view.MaxCountPerLineFlexboxLayoutManager;
import com.kaolafm.kradio.player.helper.intercept.OverhaulInterceptManager;
import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.personalise.model.HotRecommend;
import com.kaolafm.opensdk.api.search.model.SearchProgramBean;
import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.listener.ICheckCopyrightListener;
import com.kaolafm.opensdk.player.logic.listener.IGeneralListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.event.PageShowReportEvent;
import com.kaolafm.report.util.ReportConstants;
import com.kaolafm.report.util.ReportParameterManager;
import com.lcodecore.tkrefreshlayout.utils.ScrollingUtil;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshLoadMoreListener;
import com.trello.rxlifecycle3.android.ActivityEvent;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 搜索页面
 */
@Route(path = RouterConstance.SEARCH_COMPREHENSIVE_URL)
public class SearchActivity extends BaseSkinAppCompatActivity<SearchPresenter>
        implements ISearchView, RecyclerViewExposeUtil.OnItemExposeListener , View.OnTouchListener {
    private static final String TAG = "SearchFragment";

    EditText mEtSearchWord;
    ImageView mIvSearchWordDelete;
    TextView mTvSearch;
    ImageView mTvClearSearchHistory;
    RecyclerView mRvAssociateList;
    RecyclerView mRvSearchHistoryTags;
    RecyclerView mRvSearchResults;
    View mSearchResultParentLayout;
    ViewStub mVsSearchException;
    ViewStub mVsSearchNetworkError;
    View mSearchLoadingView;
    View mTopGuideLine;
    ConstraintLayout mRootLayout;
    ConstraintLayout mClSearchView;
    TextView mTvSearchHistory;
    TextView mTvHotSearchWords;
    RecyclerView mRvHotSearchWords;
    TypeSpinner mTsSearchType;
    KradioSmartRefreshLayout mSmartRefreshLayout;
    TextView mAssociateScrollUp, mAssociateScrollDown;
    TextView mResultScrollUp, mResultScrollDown;

    //最多输入字符数
    private final int EDITETEXT_INPUT_MAX_LENGTH = 100;

    public static final String PARAM_SEARCH_HINT = "PARAM_SEARCH_HINT";

    private boolean isRefresh = false, isLoadMore = false;
    private int mCurrPage = 1;//当前搜索页数
    private String mCurrSearchWay, mCurrKeyword;//当前搜索的方式和关键词
    //加载数据时传入的type
    private String loadDataType = "0";

    private ImageView mIvSearchExceptionPic;
    private TextView mTvException;
    private TextView mTvSubMessageException;
    private TextView mTvHotWord;
    private View mHotWordView;
    private View mExceptionView;
    private View mNetworkErrorView;

    //推荐内容
    private HotRecommend mHotRecommend;

    private static final int STATE_SEARCH_HISTORY = 0;
    private static final int STATE_SEARCH_ASSOCIATE = 1;
    private static final int STATE_SEARCH_RESULT = 2;
    private static final int STATE_SEARCH_EXCEPTION = 3;
    private static final int STATE_SEARCH_LOADING = 4;

    private int mState = STATE_SEARCH_HISTORY;

    private boolean isSearchStarted = false;

    private boolean isClick = false;

    private volatile boolean isGetHotWordsFailed;

    private BasePlayStateListener mPlayerStateListener;

    private List<String> mHotWords = new ArrayList<>();

    SearchResultAdapter searchResultAdapter;

    private int lineCount = 2;
    GridLayoutManager layoutManager;

    KRadioMultiWindowInter kRadioMultiWindowInter;
    List<SearchProgramBeanResult> mSearchProgramBeans;

    private IGeneralListener generalListener;


    private boolean isShowWindow = false;
    private long startTime;

    private SoftKeyboardHelper softKeyboardHelper;

    @Override
    public int getLayoutId() {
        return R.layout.fragment_search;
    }

    @Override
    public int getLayoutId_Tow() {
        return R.layout.fragment_search_1280_720;
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public void initView(Bundle savedInstanceState) {
        mEtSearchWord = findViewById(R.id.et_search_word);
        mIvSearchWordDelete = findViewById(R.id.iv_search_word_delete);
        mTvSearch = findViewById(R.id.tv_search);
        mTvClearSearchHistory = findViewById(R.id.tv_clear_search_history);
        mRvAssociateList = findViewById(R.id.rv_associate_list);
        mRvSearchHistoryTags = findViewById(R.id.rv_search_history_tags);
        mRvSearchResults = findViewById(R.id.rv_search_results);
        mSearchResultParentLayout = findViewById(R.id.search_result_parent_layout);
        mVsSearchException = findViewById(R.id.vs_search_exception);
        mVsSearchNetworkError = findViewById(R.id.vs_search_network_error);
        mSearchLoadingView = findViewById(R.id.search_loading);
        mTopGuideLine = findViewById(R.id.search_top_guideline);
        mRootLayout = findViewById(R.id.search_main_layout);
        mClSearchView = findViewById(R.id.cl_search_view);
        mTvSearchHistory = findViewById(R.id.tv_search_history);
        mTvHotSearchWords = findViewById(R.id.tv_hot_search_words);
        mRvHotSearchWords = findViewById(R.id.rv_hot_search_words);
        mTsSearchType = findViewById(R.id.ts_search_type);
        mSmartRefreshLayout = findViewById(R.id.smartRefreshLayout);

        mRvHotSearchWords.setOnTouchListener(this);
        mRvSearchHistoryTags.setOnTouchListener(this);
        mRvSearchResults.setOnTouchListener(this);


        mTvSearch.setOnClickListener(this::onViewClick);
        mIvSearchWordDelete.setOnClickListener(this::onViewClick);
        mTvClearSearchHistory.setOnClickListener(this::onViewClick);
        mRootLayout.setOnClickListener(v -> hideSoftKeyboard());

        int ori = ResUtil.getOrientation(); //获取屏幕方向
        if (ori == Configuration.ORIENTATION_LANDSCAPE) {
            //横屏
            lineCount = 2;
        } else if (ori == Configuration.ORIENTATION_PORTRAIT) {
            //竖屏
            lineCount = 1;
        }

        if (StringUtil.isNotEmpty(mSearchHint)) {
            mEtSearchWord.setHint(mSearchHint);
        }
        mEtSearchWord.addTextChangedListener(mTextWatcher);
        mEtSearchWord.setOnEditorActionListener(mEditActionListener);
        mEtSearchWord.setLongClickable(false);
        mEtSearchWord.setTextIsSelectable(false);
        mEtSearchWord.setCustomSelectionActionModeCallback(new ActionMode.Callback() {
            public boolean onCreateActionMode(ActionMode mode, Menu menu) {
                return false;
            }

            public boolean onPrepareActionMode(ActionMode mode, Menu menu) {
                return false;
            }

            public boolean onActionItemClicked(ActionMode mode, MenuItem item) {
                return false;
            }

            public void onDestroyActionMode(ActionMode mode) {
            }
        });

        mAssociateScrollUp = findViewById(R.id.associate_cd_up);
        mAssociateScrollDown = findViewById(R.id.associate_cd_down);
        mAssociateScrollUp.setOnClickListener((v) -> ScrollingUtil.scrollListByVoice(mRvAssociateList, -1));
        mAssociateScrollDown.setOnClickListener((v) -> ScrollingUtil.scrollListByVoice(mRvAssociateList, 1));
        mResultScrollUp = findViewById(R.id.result_cd_up);
        mResultScrollDown = findViewById(R.id.result_cd_down);
        mResultScrollUp.setOnClickListener((v) -> ScrollingUtil.scrollRrefreshListByVoice(mRvSearchResults, mSmartRefreshLayout, -1));
        mResultScrollDown.setOnClickListener((v) -> ScrollingUtil.scrollRrefreshListByVoice(mRvSearchResults, mSmartRefreshLayout, 1));

        NetworkMonitor.getInstance(SearchActivity.this).registerNetworkStatusChangeListener(mNetworkStatusChangedListener);


        addPlayerOB();
        PlayerManager.getInstance().addPlayControlStateCallback(mPlayerStateListener);
        PlayerManager.getInstance().addGeneralListener(generalListener);

        // 注册EventBus监听主题变化
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }

        initSearchHistoryView();
        refreshSearchHistoryView();
        initHotSearchWordsView();
        getHotSearchWords();
        initSearchAssociateView();
        initSearchResultView();
        initSearchTypeView();

        showOrHideHistoryView(true);
        initViewInner();
        KRadioSearchInter kRadioSearchInter = ClazzImplUtil.getInter("KRadioSearchImpl");
        if (kRadioSearchInter != null) {
            kRadioSearchInter.configInputSoft(mEtSearchWord);
        }

        mPresenter.searchClassifyAll();
        mClSearchView.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                if (mTsSearchType != null) {
                    mTsSearchType.setPopupWidth(mClSearchView.getMeasuredWidth());
                }
            }
        });

        mSmartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                isLoadMore = true;
                mCurrPage++;
                searchByKeyword(mCurrSearchWay, mCurrKeyword, mCurrPage, true);
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                isRefresh = true;
                mCurrPage = 1;
                searchByKeyword(mCurrSearchWay, mCurrKeyword, true);
            }
        });
        mTsSearchType.setPageId(getPageId());

        softKeyboardHelper = new SoftKeyboardHelper(this);
        softKeyboardHelper.registerKeyboardListener(mRootLayout, new SoftKeyboardHelper.SoftKeyboardListener() {
            @Override
            public void onSoftKeyboardShow(int softKeyboardHeight) {

            }

            @Override
            public void onSoftKeyboardHide(int softKeyboardHeight) {
                if (mRvAssociateList.getAdapter() != null) {
                    mRvAssociateList.getAdapter().notifyDataSetChanged();
                }
            }
        });
    }

    private String mSearchHint;

    @Override
    public void initData() {
        mSearchHint = getIntent().getStringExtra(PARAM_SEARCH_HINT);
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        CommonUtils.getInstance().initGreyStyle(getWindow());
        kRadioMultiWindowInter = ClazzImplUtil.getInter("KradioMultiWindowImpl");
    }

    @Override
    protected SearchPresenter createPresenter() {
        return new SearchPresenter(this);
    }

    private void addPlayerOB() {
        mPlayerStateListener = new BasePlayStateListener() {
            @Override
            public void onPlayerPreparing(PlayItem playItem) {
                super.onPlayerPreparing(playItem);
                Log.d("mPlayerStateListener", "onPlayerPreparing");
                ComprehensivePlayerHelper.printPlayitem("mPlayerStateListener", playItem);
                updateItemPlayingStateByPlayer(playItem);
            }

            @Override
            public void onPlayerEnd(PlayItem playItem) {
                super.onPlayerEnd(playItem);
                Logging.d("mPlayerStateListener", "onPlayerEnd");
                Log.d("mPlayerStateListener", "playItem getTitle = " + playItem.getTitle());
                if (isValidStatus()) {
                }
            }

            @Override
            public void onPlayerPlaying(PlayItem playItem) {
                Log.d("mPlayerStateListener", "onPlayerPlaying");
                if (playItem != null) {
                    Log.d("mPlayerStateListener", "playItem getTitle = " + playItem.getTitle());
                }
                super.onPlayerPlaying(playItem);
                if (isValidStatus()) {

                }
                if (mState == STATE_SEARCH_RESULT) {
                    searchResultAdapter = (SearchResultAdapter) mRvSearchResults.getAdapter();
                    if (searchResultAdapter != null) {
                        if (!isClick) {
                            searchResultAdapter.setType(0);
                        }
                        searchResultAdapter.notifyDataSetChanged();
                        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001629835652?userId=1229522问题
                        searchResultAdapter.refreshPlaying(PlayerManagerHelper.getInstance().getCurPlayItem().getRadioId());
                    }
                    isClick = false;
                }
            }

            @Override
            public void onPlayerPaused(PlayItem playItem) {
                Log.d("mPlayerStateListener", "onPlayerPaused");
                super.onPlayerPaused(playItem);
                if (isValidStatus()) {

                }
                super.onPlayerPaused(playItem);
                if (mState == STATE_SEARCH_RESULT) {
                    searchResultAdapter = (SearchResultAdapter) mRvSearchResults.getAdapter();
                    if (searchResultAdapter != null) {
//                        searchResultAdapter.notifyDataSetChanged();
                        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001629835652?userId=1229522问题
                        searchResultAdapter.refreshPlaying(PlayerManagerHelper.getInstance().getCurPlayItem().getRadioId());
                        //searchResultAdapter.refreshPlaying(KLAutoPlayerManager.getInstance().getRadioId());
                    }
                }
            }

            @Override
            public void onPlayerFailed(PlayItem playItem, int i, int i1) {
                super.onPlayerFailed(playItem, i, i1);
                Log.d("mPlayerStateListener", "onPlayerFailed");
                Log.d("mPlayerStateListener", "playItem getTitle = " + playItem.getTitle());
                updateItemPlayingStateByPlayer(PlayerManagerHelper.getInstance().getCurPlayItem());
            }

            private boolean isValidStatus() {
                if (mState == STATE_SEARCH_RESULT) {
                    searchResultAdapter = (SearchResultAdapter) mRvSearchResults.getAdapter();
                    if (searchResultAdapter != null) {
                        return true;
                    }
                }
                return false;
            }
        };

        //for play fail
        generalListener = new IGeneralListener() {

            @Override
            public void getPlayListError(PlayItem playItem, int code, int i1) {
                if (code != PlayerConstants.ERROR_CODE_PLAY_LIST_IS_LAST_ONE) {
//                UIThreadUtil.runUIThread(() -> ToastUtil.showError(RingAnimActivity.this, R.string.play_failed_str));
                    updateItemPlayingStateByPlayer(PlayerManagerHelper.getInstance().getCurPlayItem());
                }
            }

            @Override
            public void playUrlError(int code) {
                if (!NetworkUtil.isNetworkAvailable(SearchActivity.this, false)) {
                    return;
                }
//            UIThreadUtil.runUIThread(() -> ToastUtil.showError(RingAnimActivity.this, R.string.play_failed_str));
                updateItemPlayingStateByPlayer(PlayerManagerHelper.getInstance().getCurPlayItem());
            }
        };
    }

    private void updateItemPlayingStateByPlayer(PlayItem playItem) {
        if (mSearchProgramBeans == null) {
            return;
        }
        //输入当前播放playitem，判断需要高亮的列表item。
        //playitem的type不能用来判断，因为碎片和电台，专辑都是0
        boolean isFindAudio = false;
        for (int i = 0; i < mSearchProgramBeans.size(); i++) {
            SearchProgramBeanResult item = mSearchProgramBeans.get(i);
            //列表item播放完毕切换下一个item
            if ((!isVideo(item.getType()) && playItem.getAudioId() == item.getId()) ||
                    (isVideo(item.getType()) && playItem.getAlbumId().equals(String.valueOf(item.getId())))) {
                if (!item.isPlaying) {
                    Log.d("mPlayerStateListener", " item id = " + item.getId() + " item type = " + item.getType());
                    updateItemPlayingState(i);
                }
                isFindAudio = true;
            }
        }
        if (!isFindAudio) {
            for (int i = 0; i < mSearchProgramBeans.size(); i++) {
                SearchProgramBeanResult item = mSearchProgramBeans.get(i);
                long id = Long.parseLong(playItem.getRadioId());
                if (id == item.getId()) {
                    if (!item.isPlaying) {
                        Log.d("mPlayerStateListener", " item id = " + item.getId() + " item type = " + item.getType());
                        updateItemPlayingState(i, !isAudio(item.getType()));
                    }
                    isFindAudio = true;
                }
            }
        }
        if (!isFindAudio) {
            for (int i = 0; i < mSearchProgramBeans.size(); i++) {
                SearchProgramBeanResult item = mSearchProgramBeans.get(i);
                if (item.isPlaying) {
                    if (isVideo(item.getType())) {
                        updateItemPlayingState(i);
                    } else {
                        updateItemPlayingState(i, false);
                    }
                }
            }
        }
    }

    private boolean isVideo(int type) {
        return type == PlayerConstants.RESOURCES_TYPE_VIDEO_ALBUM || type == PlayerConstants.RESOURCES_TYPE_VIDEO_AUDIO;
    }

    private boolean isAudio(Integer type) {
        return SearchResultAdapter.TYPE_AUDIO == type || SearchResultAdapter.TYPE_QQ_SCENE == type || SearchResultAdapter.TYPE_QQ_TAG == type;
    }

    /**
     * 切换playitem时候，判断当前是否是碎片，如果是碎片，那么切换搜索列表下一个item，否则因为电台和专辑都有自己列表，所以不管
     */
    private void useCustomPlayList() {
        for (int i = 0; i < mSearchProgramBeans.size(); i++) {
            SearchProgramBeanResult item = mSearchProgramBeans.get(i);
            if (item.isPlaying && item.getType() == 1) {
                Log.d("mPlayerStateListener", "item type = " + item.getType());
                if ((i + 1) < mSearchProgramBeans.size()) {
                    SearchProgramBeanResult itemToPlay = mSearchProgramBeans.get(i + 1);
                    updateItemPlayingState(i + 1);
                    PlayerManagerHelper.getInstance().start(String.valueOf(itemToPlay.getId()), itemToPlay.getType());
                }
            }
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        startTime = System.currentTimeMillis();
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001757409503?userId=1229522问题
//        showSoftKeyboard();

        if (isShowWindow) {
            isShowWindow = false;
            mTsSearchType.expand();
        }

        applySkins();

        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_SEARCH_BUTTON, "搜索", getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
    }

    private void applySkins() {
        if (mTvSubMessageException != null)
            applyHotRecommandSpannableString(mTvSubMessageException.getText().toString());
    }

    private void applyHotRecommandSpannableString(String text) {
        if (mTvHotWord != null) {
            SpannableString spannableString = new SpannableString(text);
            spannableString.setSpan(new ClickableSpan() {
                @Override
                public void onClick(@NonNull View widget) {
                    PlayerManagerHelper.getInstance().start(String.valueOf(mHotRecommend.getContentId()), mHotRecommend.getType());
                }

                @Override
                public void updateDrawState(@NonNull TextPaint ds) {
//                        super.updateDrawState(ds);
                    ds.setColor(ResUtil.getColor(R.color.comprehensive_search_hot_recommend_color));
                    ds.setUnderlineText(false);
                }
            }, 0, spannableString.length(), SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE);
            mTvHotWord.setText(spannableString);
            mHotWordView.setContentDescription(spannableString);
            mTvHotWord.setHighlightColor(ResUtil.getColor(R.color.transparent));
        }
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        mEtSearchWord.setTextSize(TypedValue.COMPLEX_UNIT_PX, ResUtil.getDimen(R.dimen.search_text_size));

        if (layoutManager != null && searchResultAdapter != null) {
            layoutManager.setSpanCount(lineCount);
            if (mRvSearchResults != null) {
                mRvSearchResults.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        RecyclerView.Adapter adapter = mRvSearchResults.getAdapter();
                        RecyclerView.LayoutManager manager = mRvSearchResults.getLayoutManager();
                        mRvSearchResults.setAdapter(null);
                        mRvSearchResults.setLayoutManager(null);
                        mRvSearchResults.getRecycledViewPool().clear();
                        mRvSearchResults.setLayoutManager(manager);
                        mRvSearchResults.setAdapter(adapter);
                    }
                }, 300);
            }
        }

        saveConfiguration();
    }

    @Override
    public void onDestroy() {
        if (searchResultAdapter != null) {
            searchResultAdapter.setLifeCycleNull();
        }
        // CPU优化：清理图片内存缓存
        ImageLoader.getInstance().clearMemoryCache(this);
        super.onDestroy();
        NetworkMonitor.getInstance(SearchActivity.this).removeNetworkStatusChangeListener(mNetworkStatusChangedListener);
        PlayerManager.getInstance().removePlayControlStateCallback(mPlayerStateListener);
        PlayerManager.getInstance().removeGeneralListener(generalListener);
        if (softKeyboardHelper != null) {
            softKeyboardHelper.unregisterKeyboardListener(mRootLayout);
        }

        // 注销EventBus
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }

    private TextView.OnEditorActionListener mEditActionListener = new TextView.OnEditorActionListener() {
        @Override
        public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
            String keyword = v.getText().toString();
            // 解决https://app.huoban.com/tables/2100000007530121/items/2300001665111238?userId=1229522问题
            //todo
            if (!TextUtils.isEmpty(keyword)) {
                hideSoftKeyboard();
            }
            searchByKeyword("1", keyword);
            return true;
        }
    };

    private TextWatcher mTextWatcher = new TextWatcher() {
        boolean isShowDelete = false;

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {

        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            if (s.length() == 0) {
                ViewUtil.setViewVisibility(mIvSearchWordDelete, View.GONE);
                isShowDelete = false;
            } else {
                ViewUtil.setViewVisibility(mIvSearchWordDelete, View.VISIBLE);
                if (!isShowDelete) {
                    ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_SEARCH_INPUT_KEYWORD_CLEAR, "搜索清空", getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
                }
                isShowDelete = true;
            }
        }

        @Override
        public void afterTextChanged(Editable s) {
            String word = s.toString();
            if (word.length() == 0) {
                mPresenter.cancelRequest();
                if (mState == STATE_SEARCH_RESULT || mState == STATE_SEARCH_EXCEPTION || mState == STATE_SEARCH_LOADING) {
                    refreshSearchHistoryView();
                }
                switchState(STATE_SEARCH_HISTORY);
                return;
            }
            if (word.length() > EDITETEXT_INPUT_MAX_LENGTH) {
                ToastUtil.showNormal(getApplicationContext(), ResUtil.getString(R.string.comprehensive_search_input_more_toast));
                mEtSearchWord.setText(word.substring(0, 100));
                mEtSearchWord.setSelection(EDITETEXT_INPUT_MAX_LENGTH);
            }
            if (isSearchStarted) {
                isSearchStarted = false;
                return;
            }
            if (mPresenter != null) {
                mPresenter.getAssociateWords(word);
            }
        }
    };

    private NetworkMonitor.OnNetworkStatusChangedListener mNetworkStatusChangedListener = new NetworkMonitor.OnNetworkStatusChangedListener() {
        @Override
        public void onStatusChanged(int newStatus, int oldStatus) {
            if (oldStatus == NetworkMonitor.STATUS_NO_NETWORK) {
                if (mState == STATE_SEARCH_EXCEPTION) {
                    searchByKeyword("1", mEtSearchWord.getText().toString());
                }
                if (mTsSearchType.getState() == TypeSpinner.STATE_LOAD_FAILED) {
                    mPresenter.searchClassifyAll();
                    ;
                }
                if (isGetHotWordsFailed) {
                    getHotSearchWords();
                }
            }
        }
    };

    public void onViewClick(View view) {
        int id = view.getId();
        if (id == R.id.tv_search) {
            String keyword = mEtSearchWord.getText().toString();
            if (StringUtil.isEmpty(keyword) && StringUtil.isNotEmpty(mSearchHint)) {
                keyword = mSearchHint;
                mEtSearchWord.setText(keyword);
                mEtSearchWord.setSelection(keyword.length());
            }
            if (softKeyboardHelper != null) {
                softKeyboardHelper.hideSoftKeyboard(mEtSearchWord);
            }
            searchByKeyword("1", keyword);
            ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_SEARCH_BUTTON, "搜索", getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
            return;
        }
        if (id == R.id.iv_search_word_delete) {
            mEtSearchWord.setText(Constants.BLANK_STR);
            ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_SEARCH_INPUT_KEYWORD_CLEAR, "搜索清空", getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
            return;
        }
        if (id == R.id.tv_clear_search_history) {
            if (!AntiShake.check(id)) {
                clearSearchHistory();
                ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_SEARCH_PAGE_HISTORY_CLEAR, null, getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
            }
            return;
        }
    }

    @Override
    public String getPageId() {
        return Constants.PAGE_ID_SEARCH;
    }

    @Override
    public void showAssociateWordsView(List<AssociateInfo> associateInfos) {
        AssociateAdapter associateAdapter = (AssociateAdapter) mRvAssociateList.getAdapter();
        if (associateAdapter == null) {
            associateAdapter = new AssociateAdapter();
            associateAdapter.setPageId(getPageId());
            associateAdapter.setOnItemClickListener((View v, int viewType, AssociateInfo associateInfo, int position) -> {
                String keyword = associateInfo.getAssociateWord();
                isSearchStarted = true;
                if (!TextUtils.isEmpty(keyword)) {
                    mEtSearchWord.setText(keyword);
                    mEtSearchWord.setSelection(mEtSearchWord.getText().toString().trim().length());
                }
                searchByKeyword("3", keyword);
                ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_SEARCH_ASSOCIATIONAL_WORD, keyword, getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
            });
            mRvAssociateList.setAdapter(associateAdapter);
        }
        associateAdapter.setDataList(associateInfos);
        switchState(STATE_SEARCH_ASSOCIATE);
    }

    @Override
    public void showSearchResultView(List<SearchProgramBean> searchProgramBeansOri) {
        isRefresh = false;
        isLoadMore = false;
        if (mSearchProgramBeans == null)
            mSearchProgramBeans = new ArrayList<>();
        if (mCurrPage == 1) {
            //刷新，清空节目
            mSearchProgramBeans.clear();
        }

        if (searchProgramBeansOri == null || searchProgramBeansOri.size() < 20) {
            //获取到的数据不足一页,当前页码减1,下次加载更多的时候可以补充上空位
            mCurrPage--;
        }
        List<SearchProgramBeanResult> newProgramList = new ArrayList<SearchProgramBeanResult>();
        for (SearchProgramBean result : searchProgramBeansOri) {
            if (!hasAddedProgram(result)) {
                SearchProgramBeanResult resultNew = new SearchProgramBeanResult(result);
                newProgramList.add(resultNew);
            }
        }
        int ori = ResUtil.getOrientation(); //获取屏幕方向
        int newLineCount = 0;
        if (ori == Configuration.ORIENTATION_LANDSCAPE) {
            //横屏
            newLineCount = 2;
        } else if (ori == Configuration.ORIENTATION_PORTRAIT) {
            //竖屏
            newLineCount = 1;
        }
        if (newLineCount != lineCount) {
            lineCount = newLineCount;
            layoutManager.setSpanCount(lineCount);
            if (mRvSearchResults.getLayoutManager() != layoutManager)
                mRvSearchResults.setLayoutManager(layoutManager);
        }

        searchResultAdapter = (SearchResultAdapter) mRvSearchResults.getAdapter();
        if (searchResultAdapter == null) {
            searchResultAdapter = new SearchResultAdapter(bindUntilEvent(ActivityEvent.DESTROY));

            searchResultAdapter.setOnItemClickListener((View view, int viewType, SearchProgramBeanResult searchProgramBean, int position) -> {
                if (mSearchProgramBeans.get(position).isPlaying) {
                    return;
                }
                if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), true)) {
                    return;
                }

                PlayerManagerHelper.getInstance().checkCopyright(String.valueOf(searchProgramBean.getId()), searchProgramBean.getType(), new ICheckCopyrightListener() {
                    @Override
                    public void onGranted() {
                        updateItemPlayingState(position);
                        handleBroadcast(searchProgramBean);
                        OverhaulInterceptManager.getInstance().setNeedOverhaulSwitch(false);
                        if (searchProgramBean.getType() == ResType.TYPE_VIDEO_ALBUM || searchProgramBean.getType() == ResType.TYPE_VIDEO_AUDIO) {
                            Map<String, String> map = new HashMap<>();
                            if (PlayerManager.getInstance().getSpeedLimitState()) {
                                ToastUtil.showInfo(view.getContext(), R.string.video_player_overspeed_toast);
                                PlayerManagerHelper.getInstance().start(String.valueOf(searchProgramBean.getId()), searchProgramBean.getType(), true);
                            } else {
                                map.put(Constants.ROUTER_PARAMS_KEY_RESOURCE_TYPE, String.valueOf(searchProgramBean.getType()));
                                map.put(Constants.ROUTER_PARAMS_KEY_RESOURCE_ID, String.valueOf(searchProgramBean.getId()));
                                RouterManager.getInstance().navigateToPage(view.getContext(), Constants.PAGE_ID_VIDEO, map);
                            }
                        } else {
                            PlayerManagerHelper.getInstance().start(String.valueOf(searchProgramBean.getId()), searchProgramBean.getType());
                        }
                    }

                    @Override
                    public void onError(PlayItem playItem, int i, int i1) {
                        Log.e(TAG, "Failed to check copyright when clicking on search results.errorCode=" + i + " ,extra=" + i1);
                        if (i == PlayerConstants.ERROR_CODE_RADIO_COPYRIGHT_LITE) {
                            ToastUtil.showError(SearchActivity.this, R.string.comprehensive_radio_is_lite);
                        } else {
                            //只要不是没有版权，都按原先的逻辑处理
                            updateItemPlayingState(position);
                            handleBroadcast(searchProgramBean);
                            OverhaulInterceptManager.getInstance().setNeedOverhaulSwitch(false);
                            PlayerManagerHelper.getInstance().start(String.valueOf(searchProgramBean.getId()), searchProgramBean.getType());
                        }
                    }
                });

                mPresenter.reportSearchResultPlayEvent(searchProgramBean, position);
                mPresenter.reportContentClickEvent(searchProgramBean, position);
            });
            mRvSearchResults.setAdapter(searchResultAdapter);
        }
        if (ListUtil.isEmpty(mSearchProgramBeans)) {
            mSearchProgramBeans.addAll(newProgramList);
            searchResultAdapter.setDataList(mSearchProgramBeans);
        } else {
            if (newProgramList.size() > 0) {
                int startPosition = mSearchProgramBeans.size();
                mSearchProgramBeans.addAll(newProgramList);
                searchResultAdapter.notifyItemRangeInserted(startPosition, newProgramList.size());
            } else {
                showNoResultView();
            }
        }

        if (mState != STATE_SEARCH_RESULT)
            switchState(STATE_SEARCH_RESULT);
        isSearchStarted = false;
        updateItemPlayingStateByPlayer(PlayerManagerHelper.getInstance().getCurPlayItem());

        mSmartRefreshLayout.finishRefresh();
        mSmartRefreshLayout.finishLoadMore();
    }

    /**
     * 检查是否已经包含了该节目
     *
     * @param result
     * @return
     */
    private boolean hasAddedProgram(SearchProgramBean result) {
        for (SearchProgramBeanResult mSearchProgramBean : mSearchProgramBeans) {
            if (mSearchProgramBean != null && result != null && mSearchProgramBean.getId().equals(result.getId())) {
                return true;
            }
        }
        return false;
    }

    private void handleBroadcast(SearchProgramBeanResult searchProgramBean) {
        if (PlayerConstants.RESOURCES_TYPE_BROADCAST == searchProgramBean.getType()) {
            BroadcastRadioSimpleData data = new BroadcastRadioSimpleData();
            data.setBroadcastId(searchProgramBean.getId());
            data.setImg(searchProgramBean.getImg());
            String name = searchProgramBean.getAlbumName();
            data.setName(name);
            data.setResType(PlayerConstants.RESOURCES_TYPE_BROADCAST);
            PlayerManagerHelper.getInstance().addBroadcastRadioSimpleItem(data);
        } else if (PlayerConstants.RESOURCES_TYPE_TV == searchProgramBean.getType()) {
            BroadcastRadioSimpleData data = new BroadcastRadioSimpleData();
            data.setBroadcastId(searchProgramBean.getId());
            data.setImg(searchProgramBean.getImg());
            String name = searchProgramBean.getAlbumName();
            data.setName(name);
            data.setResType(PlayerConstants.RESOURCES_TYPE_TV);
            PlayerManagerHelper.getInstance().addBroadcastRadioSimpleItem(data);
        }
    }

    private void updateItemPlayingState(int position) {
        updateItemPlayingState(position, true);
    }

    private void updateItemPlayingState(int position, boolean isPlaying) {
        if (position >= 0 && position < mSearchProgramBeans.size()) {
            //update playing state
            //为了提高效率，只更新发生播放状态变化的两个item
            for (int i = 0; i < mSearchProgramBeans.size(); i++) {
                SearchProgramBeanResult result = mSearchProgramBeans.get(i);
                if (result.isPlaying) {
                    searchResultAdapter.notifyItemChanged(i);
                }
                result.isPlaying = false;
            }
        }
        if (mRvSearchResults.getAdapter() != searchResultAdapter) {
            mRvSearchResults.setAdapter(searchResultAdapter);
            searchResultAdapter.setDataList(mSearchProgramBeans);
        } else {
            searchResultAdapter.setDataList(mSearchProgramBeans, false);
        }
        switchState(STATE_SEARCH_RESULT);
        isSearchStarted = false;
        mSearchProgramBeans.get(position).isPlaying = isPlaying;
        searchResultAdapter.notifyItemChanged(position);
    }


    SearchResultAdapter finalSearchResultAdapter;

    @Override
    public void showNoNetView() {
        showExceptionView(false, ResUtil.getString(R.string.no_net_work_str), false);
    }

    @Override
    public void showNoResultView() {
        isLoadMore = false;
        isRefresh = false;
        mSmartRefreshLayout.finishRefresh();
        mSmartRefreshLayout.finishLoadMore();
        if (mCurrPage == 1) {   //刷新时才要显示空内容
            showExceptionView(false, ResUtil.getString(R.string.search_no_result), true);
            return;
        }
        mCurrPage--;
    }

    @Override
    public void showErrorView(int errorCode) {
        if (mCurrPage == 1) {
            mSmartRefreshLayout.finishRefresh(false);
            isRefresh = false;
        } else {
            mSmartRefreshLayout.finishLoadMore(false);
            isLoadMore = false;
        }
        if (mCurrPage == 1) {
            if (errorCode == 604) {
                showExceptionView(true, ResUtil.getString(R.string.home_network_timerout), false);
            } else {
                showExceptionView(true, ResUtil.getString(R.string.home_network_failed), false);
            }
            return;
        }
        if (errorCode == 604) {
            ToastUtil.showError(getApplicationContext(), ResUtil.getString(R.string.home_network_timerout_toast));
        } else {
            ToastUtil.showError(getApplicationContext(), ResUtil.getString(R.string.home_network_failed_toast));
        }
        mCurrPage--;
    }

    @Override
    public void showHotSearchWordsView(List<String> hotWords) {
        if (hotWords == null) {
            isGetHotWordsFailed = true;
        }
        mHotWords = hotWords;
        if (mState == STATE_SEARCH_HISTORY) {
            ViewUtil.setViewVisibility(mTvHotSearchWords, ListUtil.isEmpty(hotWords) ? View.GONE : View.VISIBLE);
            ViewUtil.setViewVisibility(mRvHotSearchWords, ListUtil.isEmpty(hotWords) ? View.GONE : View.VISIBLE);
        }
        SearchHistoryAdapter searchHistoryAdapter = (SearchHistoryAdapter) mRvHotSearchWords.getAdapter();
        if (searchHistoryAdapter == null) {
            searchHistoryAdapter = new SearchHistoryAdapter(ButtonExposureOrClickReportEvent.BUTTON_ID_SEARCH_HOT_KEYWORDS_ITEM);
            searchHistoryAdapter.setPageId(getPageId());
            searchHistoryAdapter.setOnItemClickListener((View v, int viewType, String s, int position) -> {
                isSearchStarted = true;
                if (!TextUtils.isEmpty(s) && mEtSearchWord != null) {
                    mEtSearchWord.setText(s);
                    mEtSearchWord.setSelection(mEtSearchWord.getText().toString().trim().length());
                }
                searchByKeyword("5", s);
                ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_SEARCH_HOT_KEYWORDS_ITEM, s, getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
            });
            mRvHotSearchWords.setAdapter(searchHistoryAdapter);
        }
        searchHistoryAdapter.setDataList(hotWords);
    }

    @Override
    public void setTypeData(List<SearchType> searchTypes) {
        mTsSearchType.setData(searchTypes);
        if (ListUtil.isEmpty(searchTypes)) {
            mTsSearchType.setState(TypeSpinner.STATE_LOAD_FAILED);
            return;
        }
        SearchType searchType = searchTypes.get(0);
        if (searchType == null) return;
        loadDataType = searchType.getType();
        mTsSearchType.setText(searchType.getTypeName());
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_SEARCH_CATEGORY_TAGS_LABEL
                , searchType.getTypeName(), getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
    }

    @Override
    public void showSearchTypeNetworkError(String error) {
        mTsSearchType.showNetworkError(error);
    }

    @Override
    public void showNetworkError(String error, boolean clickToRetry) {
        if (mCurrPage == 1) {
            mSmartRefreshLayout.finishRefresh(false);
            isRefresh = false;
        } else {
            mSmartRefreshLayout.finishLoadMore(false);
            isLoadMore = false;
        }
        isSearchStarted = false;
        if (mCurrPage > 1) {
            //加载过程中的错误
            mCurrPage--;
            ToastUtil.showError(getApplicationContext(), error);
            return;
        }

        if (mNetworkErrorView == null) {
            mNetworkErrorView = mVsSearchNetworkError.inflate();
        }
        ViewUtil.setViewVisibility(mNetworkErrorView, View.VISIBLE);

        // 改变默认的错误信息文本
        TextView tvNetworkNoSign = mNetworkErrorView.findViewById(R.id.tv_network_nosign);
        tvNetworkNoSign.setText(error);

        // 支持点击图标重试
        ImageView ivNetworkNoSign = mNetworkErrorView.findViewById(R.id.network_nosigin);
        if (clickToRetry) {
            ivNetworkNoSign.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    ViewUtil.setViewVisibility(mNetworkErrorView, View.GONE);
                    searchByKeyword("1", mEtSearchWord.getText().toString());
                    String text = tvNetworkNoSign.getText().toString();
                    ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_NETWORK_RETRY, text, getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
                }
            });
        } else {
            ivNetworkNoSign.setOnClickListener(null);
        }
        switchState(STATE_SEARCH_EXCEPTION);

        TextView tvNetworkNosign = mNetworkErrorView.findViewById(R.id.tv_network_nosign);
        String text = null;
        if (tvNetworkNosign != null) {
            text = tvNetworkNosign.getText().toString();
        }
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_NETWORK_RETRY, text, getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
    }


    @Override
    public void showLoadingView() {
        switchState(STATE_SEARCH_LOADING);
    }

    public void showExceptionView(boolean isNeedRefresh, String exceptionString, boolean showHoyRecommand) {
        isSearchStarted = false;
        if (mExceptionView == null) {
            mExceptionView = mVsSearchException.inflate();
            mIvSearchExceptionPic = mExceptionView.findViewById(R.id.iv_search_exception_pic);
            mTvException = mExceptionView.findViewById(R.id.tv_search_exception_message);
            mTvSubMessageException = mExceptionView.findViewById(R.id.tv_search_exception_sub_message);
            mHotWordView = mExceptionView.findViewById(R.id.hot_word_layout);
            mTvHotWord = mExceptionView.findViewById(R.id.tv_hot_word);
        }
        mTvException.setText(exceptionString);
        if (showHoyRecommand) {
            mIvSearchExceptionPic.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_search_exception_pic));
            //每次显示都调用获取最新推荐内容
            mPresenter.getHotRecommend();
        } else {
            mIvSearchExceptionPic.setImageDrawable(ResUtil.getDrawable(R.drawable.ic_network_error));
            mTvSubMessageException.setVisibility(View.GONE);
            mHotWordView.setVisibility(View.GONE);
        }
        if (isNeedRefresh) {
            mTvException.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    searchByKeyword("1", mEtSearchWord.getText().toString());
                }
            });
        } else {
            mTvException.setOnClickListener(null);
        }
        switchState(STATE_SEARCH_EXCEPTION);
    }

    @Override
    public void showHotRecommend(HotRecommend hotRecommend) {
        this.mHotRecommend = hotRecommend;
        if (mTvSubMessageException != null && mTvHotWord != null && mHotWordView != null) {
            if (hotRecommend != null && StringUtil.isNotEmpty(hotRecommend.getName())) {
                mHotWordView.setContentDescription("");
                ViewUtil.setViewVisibility(mTvSubMessageException, View.VISIBLE);
                ViewUtil.setViewVisibility(mHotWordView, View.VISIBLE);
                applyHotRecommandSpannableString(hotRecommend.getName());
                mTvHotWord.setMovementMethod(new ClickLinkMovementMethod());
            } else {
                ViewUtil.setViewVisibility(mTvSubMessageException, View.GONE);
                ViewUtil.setViewVisibility(mHotWordView, View.GONE);
            }
        }
    }

    private void getHotSearchWords() {
        mPresenter.getHotSearchWords();
    }

    private void initSearchHistoryView() {
        FlexboxLayoutManager flexboxLayoutManager = new MaxCountPerLineFlexboxLayoutManager(SearchActivity.this, 5);
        mRvSearchHistoryTags.setLayoutManager(flexboxLayoutManager);
        mRvSearchHistoryTags.addItemDecoration(new SearchItemDecoration(0, ResUtil.getDimen(R.dimen.x30),
                0, ResUtil.getDimen(R.dimen.y30)));
    }

    private void initHotSearchWordsView() {
        FlexboxLayoutManager flexboxLayoutManager = new MaxCountPerLineFlexboxLayoutManager(SearchActivity.this, 5);
        mRvHotSearchWords.setLayoutManager(flexboxLayoutManager);
        mRvHotSearchWords.addItemDecoration(new SearchItemDecoration(0, ResUtil.getDimen(R.dimen.x30),
                0, ResUtil.getDimen(R.dimen.y30)));
    }

    private void initSearchAssociateView() {
        mRvAssociateList.setLayoutManager(new LinearLayoutManager(SearchActivity.this));
        mRvAssociateList.addItemDecoration(new SearchItemDecoration(0, 0, ResUtil.getDimen(R.dimen.y20),
                0, false, true, true, true));
    }

    private void initSearchResultView() {
        layoutManager = new GridLayoutManager(SearchActivity.this, lineCount, LinearLayoutManager.VERTICAL, false);
        mRvSearchResults.setLayoutManager(layoutManager);
        mRvSearchResults.addItemDecoration(new SearchGridItemDecoration(ResUtil.getDimen(R.dimen.x40), ResUtil.getDimen(R.dimen.y24)));
    }

    private void initSearchTypeView() {
        //todo
        mTsSearchType.setText("综合");
        mTsSearchType.setOnExpandListener(() -> {
            if (mTsSearchType.getState() == TypeSpinner.STATE_LOAD_FAILED) {
                mPresenter.searchClassifyAll();
            }
        });
        getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING);

        mTsSearchType.setOnTypeChangedListener(searchType -> {
            Log.d(TAG, "TypeChanged " + searchType.getTypeName());
            loadDataType = searchType.getType();
            if (mTsSearchType != null) {
                mTsSearchType.setText(searchType.getTypeName());
            }
//            if (StringUtil.isNotEmpty(mCurrKeyword))
//                searchByKeyword(mCurrSearchWay, mCurrKeyword);
        });
    }

    /**
     * 搜索
     *
     * @param searchWay 1.手动输入；2.点击历史记录；3.点击联想词；4.语音搜索
     * @param keyword   关键词
     */
    private void searchByKeyword(String searchWay, String keyword, boolean fromRefresh) {
        mCurrPage = 1;
        if (softKeyboardHelper != null) {
            softKeyboardHelper.hideSoftKeyboard(mEtSearchWord);
        }
        searchByKeyword(searchWay, keyword, mCurrPage, fromRefresh);
    }

    private void searchByKeyword(String searchWay, String keyword) {
        searchByKeyword(searchWay, keyword, false);
    }

    private void searchByKeyword(String searchWay, String keyword, int pagenum) {
        searchByKeyword(searchWay, keyword, pagenum, false);
    }

    /**
     * 搜索
     * 用于刷新和加载
     *
     * @param searchWay 1.手动输入；2.点击历史记录；3.点击联想词；4.语音搜索；5.热门搜索
     * @param keyword   关键词
     * @param pagenum   页码
     */
    private void searchByKeyword(String searchWay, String keyword, int pagenum, boolean fromRefresh) {
        if (mPresenter == null) {
            return;
        }

        mCurrSearchWay = searchWay;
        mCurrKeyword = keyword;
        KRadioSearchHideInter inter = ClazzImplUtil.getInter("KRadioSearchHideImpl");
        if (inter != null && inter.isHideAfterClick()) {
            hideSoftKeyboard();
        }

        mCurrSearchWay = searchWay;
        mCurrKeyword = keyword;
        mPresenter.cancelRequest();
        if (TextUtils.isEmpty(keyword) || TextUtils.isEmpty(keyword.trim())) {
            ToastUtil.showInfo(getApplicationContext(), R.string.toast_plz_input_keyword);
            return;
        }
        String s = keyword.trim();
        if (null != mNetworkErrorView) {
            ViewUtil.setViewVisibility(mNetworkErrorView, View.GONE);
        }
        mPresenter.searchByKeyword(loadDataType, searchWay, s, pagenum, 20, fromRefresh);
    }

    private void switchState(int state) {
        if (mState == state) {
            if (mState == STATE_SEARCH_ASSOCIATE) {
                //当联想词发生变化时要重新修改联想词列表显隐状态，如果没有联想词就隐藏
                showOrHideStateView(mState, true);
            } else {
                return;
            }
        }
        showOrHideStateView(mState, false);
        mState = state;
        showOrHideStateView(mState, true);
    }

    private void showOrHideStateView(int state, boolean isShow) {
        switch (state) {
            case STATE_SEARCH_HISTORY:
                showOrHideHistoryView(isShow);
                break;
            case STATE_SEARCH_ASSOCIATE:
                showOrHideAssociateView(isShow);
                break;
            case STATE_SEARCH_RESULT:
                showOrHideResultView(isShow);
                break;
            case STATE_SEARCH_EXCEPTION:
                showOrHideExceptionView(isShow);
                break;
            case STATE_SEARCH_LOADING:
                showOrHideLoadingView(isShow);
                break;
            default:
        }
    }

    private void showOrHideHistoryView(boolean isShow) {
        //ViewUtil.setViewVisibility(mSearchHistoryGroup, isShow ? View.VISIBLE : View.GONE);
        showOrHideHotSearchView(isShow);
        List<String> tags = SearchHistoryManager.getInstance().getRecentSearchTags();
        if (ListUtil.isEmpty(tags) || !isShow) {
            ViewUtil.setViewVisibility(mTvClearSearchHistory, View.GONE);
            ViewUtil.setViewVisibility(mTvSearchHistory, View.GONE);
            ViewUtil.setViewVisibility(mRvSearchHistoryTags, View.GONE);
        } else {
            if (mTvClearSearchHistory.getVisibility() == View.VISIBLE) {
                //上报数据
                ButtonExposureOrClickReportEvent reportEventBean = new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_SEARCH_PAGE_HISTORY_CLEAR, null, getPageId(), ReportConstants.CONTROL_TYPE_SCREEN);
                reportEventBean.setPage(getPageId());
                ReportHelper.getInstance().addEvent(reportEventBean);
            }
            ViewUtil.setViewVisibility(mTvClearSearchHistory, View.VISIBLE);
            ViewUtil.setViewVisibility(mTvSearchHistory, View.VISIBLE);
            ViewUtil.setViewVisibility(mRvSearchHistoryTags, View.VISIBLE);
        }
        reportSearchPage(isShow);
    }

    private void showOrHideHotSearchView(boolean isShow) {
        if (isShow && !ListUtil.isEmpty(mHotWords)) {
            ViewUtil.setViewVisibility(mTvHotSearchWords, View.VISIBLE);
            ViewUtil.setViewVisibility(mRvHotSearchWords, View.VISIBLE);
        } else {
            ViewUtil.setViewVisibility(mTvHotSearchWords, View.GONE);
            ViewUtil.setViewVisibility(mRvHotSearchWords, View.GONE);
        }
    }

    private void showOrHideAssociateView(boolean isShow) {
        isShow = isShow && (mRvAssociateList != null && mRvAssociateList.getAdapter() != null && mRvAssociateList.getAdapter().getItemCount() > 0);
        ViewUtil.setViewVisibility(mRvAssociateList, isShow ? View.VISIBLE : View.GONE);
        ViewUtil.setViewVisibility(mAssociateScrollUp, isShow ? View.VISIBLE : View.GONE);
        ViewUtil.setViewVisibility(mAssociateScrollDown, isShow ? View.VISIBLE : View.GONE);
    }

    private void showOrHideResultView(boolean isShow) {
        if (!isShow && (isLoadMore || isRefresh)) {
            return;
        }
        if (isShow) {
            showOrHideHistoryView(false);
            showOrHideExceptionView(false);
        }
        ViewUtil.setViewVisibility(mRvSearchResults, isShow ? View.VISIBLE : View.GONE);
        ViewUtil.setViewVisibility(mResultScrollUp, isShow ? View.VISIBLE : View.GONE);
        ViewUtil.setViewVisibility(mResultScrollDown, isShow ? View.VISIBLE : View.GONE);
        ViewUtil.setViewVisibility(mSearchResultParentLayout, isShow ? View.VISIBLE : View.GONE);
        reportSearchResultPage(isShow);
    }

    private void showOrHideExceptionView(boolean isShow) {
        ViewUtil.setViewVisibility(mExceptionView, isShow ? View.VISIBLE : View.GONE);
    }

    private void showOrHideLoadingView(boolean isShow) {
        ViewUtil.setViewVisibility(mSearchLoadingView, isShow ? View.VISIBLE : View.GONE);
        showOrHideExceptionView(!isShow);
    }

    private void reportSearchPage(boolean isShow) {
        if (isShow) {
            reportPageShowEventSelf();
            startTime = System.currentTimeMillis();
            ReportHelper.getInstance().setPage(Constants.PAGE_ID_SEARCH);
        }
    }

    private void reportSearchResultPage(boolean isShow) {
        if (isShow) {
            reportPageShowEventSelf();
            startTime = System.currentTimeMillis();
            ReportHelper.getInstance().setPage(Constants.PAGE_ID_SEARCH_RESULT);
        }
    }

    private void showSoftKeyboard() {
        mEtSearchWord.setFocusable(true);
        mEtSearchWord.setFocusableInTouchMode(true);
        mEtSearchWord.requestFocus();
        mEtSearchWord.setCursorVisible(true);
        InputMethodManager imm = (InputMethodManager) SearchActivity.this.getSystemService(Context.INPUT_METHOD_SERVICE);
        imm.showSoftInput(mEtSearchWord, 0);
    }

    private void hideSoftKeyboard() {
        InputMethodManager imm = (InputMethodManager) SearchActivity.this.getSystemService(Context.INPUT_METHOD_SERVICE);
        imm.hideSoftInputFromWindow(mEtSearchWord.getWindowToken(), InputMethodManager.HIDE_NOT_ALWAYS);
    }


    public void clickBack(View v) {
        if (mState == STATE_SEARCH_HISTORY || mState == STATE_SEARCH_EXCEPTION) {
            if (null != mNetworkErrorView) {
                ViewUtil.setViewVisibility(mNetworkErrorView, View.GONE);
            }
            finish();
        } else if (mState == STATE_SEARCH_LOADING) {
            showOrHideHistoryView(false);
            showOrHideLoadingView(false);
        } else {
            mEtSearchWord.setText(Constants.BLANK_STR);
            ViewUtil.setViewVisibility(mSearchResultParentLayout, View.GONE);
        }
    }

    private void refreshSearchHistoryView() {
        SearchHistoryAdapter searchHistoryAdapter = (SearchHistoryAdapter) mRvSearchHistoryTags.getAdapter();
        if (searchHistoryAdapter == null) {
            searchHistoryAdapter = new SearchHistoryAdapter(ButtonExposureOrClickReportEvent.BUTTON_ID_SEARCH_PAGE_HISTORY_ITEM);
            searchHistoryAdapter.setPageId(getPageId());
            searchHistoryAdapter.setOnItemClickListener((View v, int viewType, String s, int position) -> {
                isSearchStarted = true;
                if (!TextUtils.isEmpty(s) && mEtSearchWord != null) {
                    mEtSearchWord.setText(s);
                    mEtSearchWord.setSelection(mEtSearchWord.getText().toString().trim().length());
                }
                searchByKeyword("2", s);
                ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_SEARCH_PAGE_HISTORY_ITEM, s, getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
            });
            mRvSearchHistoryTags.setAdapter(searchHistoryAdapter);
        }
        List<String> tags = SearchHistoryManager.getInstance().getRecentSearchTags();
        searchHistoryAdapter.setDataList(tags);
    }

    private void clearSearchHistory() {
        // 解决[DI4.0_3.5UI][功能缺陷] [规划院] 【云听】[概率100%】云听主界面---搜索，同时点击搜索框中的节目类型下拉按钮和清空记录按钮，节目类型弹框和清空记录弹框能同时存在
        boolean expanded = mTsSearchType.isExpanded();
        Log.i(TAG, "expanded:" + expanded);
        if (expanded) {
            return;
        }
        DialogFragment dialogFragment = new Dialogs.Builder()
                .setType(Dialogs.TYPE_2BTN)
                .setGravity(Gravity.CENTER)
                .setMessage(ResUtil.getString(R.string.are_you_sure_to_clear_your_search_history))
                .setOnPositiveListener(dialog -> {
                    SearchHistoryManager.getInstance().clearAllRecentSearchTags();
                    refreshSearchHistoryView();
                    showOrHideHistoryView(true);
                    dialog.dismiss();
                })
                .create();
        dialogFragment.show(getSupportFragmentManager(), "clear_search");

    }

    private void uploadView(boolean isLand) {
//        mEtSearchWord.setTextSize(TypedValue.COMPLEX_UNIT_PX, ResUtil.getDimen(R.dimen.search_text_size));
//        mTsSearchType.setTextSize(TypedValue.COMPLEX_UNIT_PX, ResUtil.getDimen(R.dimen.search_text_size));
//        ConstraintLayout.LayoutParams tsLayoutParams = (ConstraintLayout.LayoutParams) mTsSearchType.getLayoutParams();
//        boolean isMutiWindow = MultiUtil.getMultiStatus();
//        if (isMutiWindow && kRadioMultiWindowInter != null) {
//            kRadioMultiWindowInter.doMutiSearchView(mTsSearchType, mEtSearchWord);
//        }
//        mTsSearchType.setLayoutParams(tsLayoutParams);
//        mEtSearchWord.setHint(ResUtil.getString(R.string.search_hint));
//        ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) mClSearchView.getLayoutParams();
//        KRadioC211ViewSizeInter inter = ClazzImplUtil.getInter("KRadioC211ViewSizeImpl");
//        if (inter == null || !inter.isNeedReset()) { //为空或非C211不执行
//            layoutParams.setMarginStart(ResUtil.getDimen(R.dimen.search_edit_left_margin));
////              layoutParams.width = ResUtil.getDimen(R.dimen.search_edit_width);
//            layoutParams.height = ResUtil.getDimen(R.dimen.comprehensive_search_edit_height);
//        }
//        //更改分屏状态下宽度,所以需要在setLayoutParams方法之前调用
//        if (isLand && isMutiWindow) {
//            if (kRadioMultiWindowInter != null) {
//                kRadioMultiWindowInter.doMultiSearchFragment(layoutParams);
//            }
//        }
//        mClSearchView.setLayoutParams(layoutParams);
//
//        ConstraintLayout.LayoutParams lp = (ConstraintLayout.LayoutParams) mTvSearch.getLayoutParams();
//        lp.setMarginStart(ResUtil.getDimen(R.dimen.search_btn_left_margin));
//        lp.width = ResUtil.getDimen(R.dimen.search_btn_width);
//        lp.height = ResUtil.getDimen(R.dimen.search_btn_height);
//        mTvSearch.setLayoutParams(lp);
//
//        if (!isLand) {
//            ConstraintLayout.LayoutParams histroyLp = (ConstraintLayout.LayoutParams) mTvSearchHistory.getLayoutParams();
//            histroyLp.setMarginStart(ResUtil.getDimen(R.dimen.m24));
//            ConstraintLayout.LayoutParams associateLp = (ConstraintLayout.LayoutParams) mRvAssociateList.getLayoutParams();
//            associateLp.setMarginStart(ResUtil.getDimen(R.dimen.m24));
//            ConstraintLayout.LayoutParams hotSearchLp = (ConstraintLayout.LayoutParams) mTvHotSearchWords.getLayoutParams();
//            hotSearchLp.setMarginStart(ResUtil.getDimen(R.dimen.m24));
//            ((ConstraintLayout.LayoutParams) mTopGuideLine.getLayoutParams()).setMargins(0, ResUtil.getDimen(R.dimen.y30), 0, 0);
//        } else {
//            ConstraintLayout.LayoutParams histroyLp = (ConstraintLayout.LayoutParams) mTvSearchHistory.getLayoutParams();
//            histroyLp.setMarginStart(1);
//            ConstraintLayout.LayoutParams associateLp = (ConstraintLayout.LayoutParams) mRvAssociateList.getLayoutParams();
//            associateLp.setMarginStart(1);
//            ConstraintLayout.LayoutParams hotSearchLp = (ConstraintLayout.LayoutParams) mTvHotSearchWords.getLayoutParams();
//            hotSearchLp.setMarginStart(1);
//            ((ConstraintLayout.LayoutParams) mTopGuideLine.getLayoutParams()).setMargins(0, ResUtil.getDimen(R.dimen.y40), 0, 0);
//        }
//        mRootLayout.setPadding(0, ResUtil.getDimen(R.dimen.default_top_padding_search), 0, 0);
//        ConstraintSet set = new ConstraintSet();
//        set.clone(mRootLayout);
//        if (isLand) {
////            setGuideline(set, mTopGuideLine, isLand);
//            set.connect(mTvSearchHistory.getId(), ConstraintSet.LEFT, mClSearchView.getId(), ConstraintSet.LEFT);
//            set.connect(mRvSearchHistoryTags.getId(), ConstraintSet.LEFT, mClSearchView.getId(), ConstraintSet.LEFT);
//            set.connect(mRvAssociateList.getId(), ConstraintSet.LEFT, mClSearchView.getId(), ConstraintSet.LEFT);
//            set.connect(mTvHotSearchWords.getId(), ConstraintSet.LEFT, mClSearchView.getId(), ConstraintSet.LEFT);
//        } else {
////            setGuideline(set, mTopGuideLine, isLand);
//            set.connect(mTvSearchHistory.getId(), ConstraintSet.LEFT, R.id.backView, ConstraintSet.LEFT);
//            set.connect(mRvSearchHistoryTags.getId(), ConstraintSet.LEFT, mTvSearchHistory.getId(), ConstraintSet.LEFT);
//            set.connect(mRvAssociateList.getId(), ConstraintSet.LEFT, R.id.backView, ConstraintSet.LEFT);
//            set.connect(mTvHotSearchWords.getId(), ConstraintSet.LEFT, R.id.backView, ConstraintSet.LEFT);
//        }
//        set.applyTo(mRootLayout);
    }

    public void setGuideline(ConstraintSet set, View guideLine, boolean isLand) {
        if (isLand) {
            set.setGuidelinePercent(guideLine.getId(), 0.200f);
        } else {
            set.setGuidelinePercent(guideLine.getId(), 0.133f);
        }
    }

    private void initViewInner() {
        int mCurrentOrientation = ResUtil.getOrientation();
        if (mCurrentOrientation == Configuration.ORIENTATION_PORTRAIT) {
            uploadView(false);
        } else if (mCurrentOrientation == Configuration.ORIENTATION_LANDSCAPE) {
            uploadView(true);
        }
    }

    @Override
    public void onPause() {
        super.onPause();
//        hideSoftKeyboard();

        if (mTsSearchType.isWindowShow()) {
            isShowWindow = true;
            mTsSearchType.collapse();
        }

        reportPageShowEventSelf();
    }

    private void reportPageShowEventSelf() {
        long duration = System.currentTimeMillis() - startTime;
        if (!isReportFragment() || TextUtils.isEmpty(ReportParameterManager.getInstance().getPage()) || startTime < 0 || duration < 300) {
            return;
        }
        PageShowReportEvent event = new PageShowReportEvent();
        event.setPage(ReportParameterManager.getInstance().getPage());
        event.setPageId(ReportParameterManager.getInstance().getPage());
        event.setPageTime(String.valueOf(duration));
        ReportHelper.getInstance().addEvent(event);
    }

    public boolean isReportFragment() {
        return true;
    }

    @Override
    public void onItemViewVisible(boolean visible, int position) {
        if (visible && searchResultAdapter != null) {
            SearchProgramBean bean = searchResultAdapter.getItemData(position);
            mPresenter.reportContentShowEvent(bean, position);
        }
    }

    @Override
    protected boolean autoSetBackViewMarginLeft() {
        return false;
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public boolean onTouch(View v, MotionEvent event) {
        int id = v.getId();
        if (id == R.id.rv_search_history_tags || id == R.id.rv_hot_search_words || id == R.id.rv_search_results){
            if (event.getAction() == MotionEvent.ACTION_UP){
                hideSoftKeyboard();
            }
        }
        return false;
    }

    /**
     * 监听主题变化事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onThemeChangeEvent(UserCenterInter.ThemeChangeEvent event) {
        if ("isSameTheme".equals(event.getTheme())) {
            updateThemeFromSettings();
            return;
        }

        // 延迟刷新，确保主题资源已经更新
        new Handler(Looper.getMainLooper()).postDelayed(this::refreshAdaptersForThemeChange, 100);
    }

    /**
     * 从Settings读取当前主题并更新
     */
    private void updateThemeFromSettings() {
        try {
            String settingsTheme = android.provider.Settings.System.getString(
                getContentResolver(),
                "android.car.THEME_TYPE"
            );

            // 延迟刷新适配器，确保主题资源已经更新
            new Handler(Looper.getMainLooper()).postDelayed(this::refreshAdaptersForThemeChange, 300);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 刷新适配器以适应主题变化
     */
    private void refreshAdaptersForThemeChange() {
        // 主题变化时刷新搜索结果适配器
        if (searchResultAdapter != null) {
            searchResultAdapter.notifyDataSetChanged();
        }

        // 同时刷新联想词适配器
        if (mRvAssociateList != null && mRvAssociateList.getAdapter() != null) {
            mRvAssociateList.getAdapter().notifyDataSetChanged();
        }
    }
}
