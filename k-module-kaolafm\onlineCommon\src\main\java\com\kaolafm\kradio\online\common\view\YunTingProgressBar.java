package com.kaolafm.kradio.common.online.view;

import android.animation.ValueAnimator;
import android.animation.ValueAnimator.AnimatorUpdateListener;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Paint.Style;
import android.graphics.Path;
import android.graphics.PathMeasure;
import android.graphics.PointF;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.Region;
import android.graphics.Shader;
import android.graphics.SweepGradient;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewParent;

import com.kaolafm.kradio.k_kaolafm.R.drawable;
import com.kaolafm.kradio.k_kaolafm.R.styleable;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.HashMap;

/**
 * 云听车载异形进度条
 */
public final class YunTingProgressBar extends View {
    private final String TAG;
    //进度条前景是否使用完全的渐变
    private final boolean LINEAR_GRADIENT = false;

    private float touchSlop;

    //滑块图片
    private Drawable mThumbBitmap;
    //滑块大小
    private float mThumpSize;
    private float mThumbScaleMultipleWhenTouch;
    //滑块显示区域
    private Rect thumbRect;

    //进度条的完整path
    private final Path mProgressBarPath;
    //滑块坐标信息
    private float[] mThumbPos;
    //滑块在进度条上的切点的信息
    private float[] mThumbTan;
    //进度条对应的PathMeasure
    private PathMeasure mPathMeasure;
    //进度条的总长度
    private float mProgressBarLength;
    //进度条上第一个点的位置（x,y）
    private float[] mFirstPointLocation;
    //进度条上最后一个点的位置（x,y）
    private float[] mLastPointLocation;

    //可点击区域的path
    private Path mTouchablePath;
    //100%进度时完整的高亮path
    private Path mHighLightTotalPath;
    //可点击区域Path构成的Region
    private Region mTouchablePathRegion;
    //可点击区域Path Region的裁剪区域
    private Region mTouchablePathClipRegion;

    //非进度条区域的颜色色值
    private int unProgressColor;
    //非进度条区域的颜色渐变到透明的色值
    private int unProgressTransparentColor;
    //进度条的颜色色值
    private int progressColor;
    //进度条的颜色色值
    private int progressColorWithAlpha;

    private Paint mPaint;

    //高亮部分的细线
    private SweepGradient mProgressLineSweepGradient;
    private SweepGradient mProgressForegroundSweepGradient;
    private SweepGradient mNotProgressSweepGradient;

    //50%中间位置进度条最大宽度
    private float mMaxWidth;
    //绘图时用的矩形区域
    private final RectF mRectF;
    //高亮区域需要通过裁剪来达到跟随当前进度显示的目的，以此保存裁剪区域
    private final RectF mHighLightClipRectF;
    //开始位置的度数，取140度对应UI中进度部分左侧的位置
    private float startAngle;
    //进度条与非进度条之间的间距
    private float gapAngle;
    //椭圆短轴长度
    private float ovalHeight;
    //渐变使用的矩阵
    private final Matrix mMatrix;
    //音频总时长
    private float mMaxProgress;
    //音频播放进度
    private float mProgress;
    //当前播放进度与总进度的比值
    private float mProgressRatio;
    //用于计算是否滑动超过了最大进度分之一
    private int progressInner;
    //是否使用平滑滑动
    private boolean mUseSmoothTouch;
    //两边的非进度条使用短的长度
    private boolean mShortUnProgress;
    //是否允许点击调整进度
    private boolean isEnableClickToSeek;

    private YunTingProgressBar.YunTingProgressBarCallback mYunTingProgressBarCallback;

    /**----------------------- 点击事件 --------------------------*/
    private float downX;
    private boolean isDownThumb;
    private boolean isMoveThumb;
    //用于计算点击进度的临时点
    private float[] tempPoint;
    private ValueAnimator thumbScaleAnimation;
    //滑块缩放参数，从1到28/16
    private float mThumbScale;

    public YunTingProgressBar(@Nullable Context context) {
        this(context, (AttributeSet) null);
    }

    public YunTingProgressBar(@Nullable Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public YunTingProgressBar(@Nullable Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        String var10001 = YunTingProgressBar.class.getSimpleName();
        this.TAG = var10001;
        this.mThumbScaleMultipleWhenTouch = 1.0F;
        this.thumbRect = new Rect();
        this.mProgressBarPath = new Path();
        this.mThumbPos = new float[2];
        this.mThumbTan = new float[2];
        this.mPathMeasure = new PathMeasure(this.mProgressBarPath, false);
        this.mFirstPointLocation = new float[2];
        this.mLastPointLocation = new float[2];
        this.mTouchablePath = new Path();
        this.mHighLightTotalPath = new Path();
        this.mTouchablePathRegion = new Region();
        this.mTouchablePathClipRegion = new Region();
        this.unProgressColor = Color.parseColor("#4D0084FF");
        this.unProgressTransparentColor = Color.parseColor("#00005AFF");
        this.progressColor = Color.parseColor("#00BAFF");
        this.progressColorWithAlpha = Color.parseColor("#CD00BAFF");
        Paint var4 = new Paint(1);
        var4.setStyle(Style.FILL);
        var4.setColor(this.unProgressColor);
        this.mPaint = var4;
        this.mRectF = new RectF();
        this.mHighLightClipRectF = new RectF();
        this.startAngle = 140.0F;
        this.gapAngle = 5.0F;
        this.mMatrix = new Matrix();
        this.mThumbScale = 1.0F;
        this.getValues(attrs);
        this.init();
    }

    private final void getValues(AttributeSet attrs) {
        TypedArray var10000 = this.getContext().obtainStyledAttributes(attrs, styleable.YunTingProgressBar);
        TypedArray ta = var10000;
        this.mProgress = ta.getFloat(styleable.YunTingProgressBar_ytpbProgress, 0.0F);
        this.mMaxProgress = ta.getFloat(styleable.YunTingProgressBar_ytpbMaxProgress, 100.0F);
        float startAngleToLeftHorizontal = ta.getFloat(styleable.YunTingProgressBar_ytpbStartAngleToLeftHorizontal, 40.0F);
        this.startAngle = 180.0F - startAngleToLeftHorizontal;
        this.gapAngle = ta.getFloat(styleable.YunTingProgressBar_ytpbGapAngle, 5.0F);
        this.mThumbBitmap = ta.getDrawable(styleable.YunTingProgressBar_ytpbThumbSrc);
        if (this.mThumbBitmap == null) {
            this.mThumbBitmap = (Drawable) (new BitmapDrawable(this.getResources(), BitmapFactory.decodeResource(this.getResources(), drawable.online_progress_thumb_icon)));
        }

        this.mThumpSize = ta.getDimension(styleable.YunTingProgressBar_ytpbThumbSize, 50.0F);
        this.mThumbScaleMultipleWhenTouch = ta.getFloat(styleable.YunTingProgressBar_ytpbThumbScaleMultipleWhenTouch, 1.0F);
        this.mUseSmoothTouch = ta.getBoolean(styleable.YunTingProgressBar_ytpbUseSmoothTouch, false);
        this.mShortUnProgress = ta.getBoolean(styleable.YunTingProgressBar_ytpbShortUnProgress, false);
        this.isEnableClickToSeek = ta.getBoolean(styleable.YunTingProgressBar_ytpbEnableClickToSeek, false);
        ta.recycle();
    }

    public final void setMaxProgress(float duration) {
        this.mMaxProgress = duration < (float) 0 ? 0.0F : duration;
        if (this.mMaxProgress != 0.0F) {
            this.touchSlop = (this.mLastPointLocation[0] - this.mFirstPointLocation[0]) / this.mMaxProgress;
        }

    }

    public final void setProgress(float position) {
        this.setProgress(position, true);
    }

    public final void setProgress(float position, boolean needCallback) {
        this.setProgress(position, needCallback, true);
    }

    public final void setProgress(float position, boolean needCallback, boolean needInvalidate) {
        if (!this.isMoveThumb) {
            this.setProgressInner(position);
            if (needCallback) {
                YunTingProgressBar.YunTingProgressBarCallback var10000 = this.mYunTingProgressBarCallback;
                if (var10000 != null) {
                    var10000.onProgressChanged(this, this.mProgress, false);
                }
            }

            if (needInvalidate) {
                this.invalidate();
            }

        }
    }

    public final float getProgress() {
        return this.mProgress;
    }

    public final float getMaxProgress() {
        return this.mMaxProgress;
    }

    /**
     * 是否使用平滑触摸进度调整
     * 如果平滑触摸，手势MOVE的时候将不进行任何判断，总会调整进度，此时计算量会增大，性能可能会降低
     * 如果禁用平滑触摸，只有MOVE达到 1/maxProgress 后才会更新进度，减少MOVE时的计算量，但也会导致进度发生细微变化时不能实时更新
     * 注意：如果maxProgress值很大，则禁用与否在视觉效果上没有什么差异。
     */
    public final void setUseSmoothTouch(boolean useSmoothTouch) {
        this.mUseSmoothTouch = useSmoothTouch;
    }

    public final void setShortUnProgress(boolean shortUnProgress) {
        this.mShortUnProgress = shortUnProgress;
        if (this.mShortUnProgress) {
            this.mNotProgressSweepGradient = new SweepGradient(0.0F, 0.0F, new int[]{this.unProgressColor, this.unProgressTransparentColor}, new float[]{0.491F, 0.508F});
        } else {
            this.mNotProgressSweepGradient = new SweepGradient(0.0F, 0.0F, new int[]{this.unProgressColor, this.unProgressTransparentColor}, new float[]{0.491F, 0.513F});
        }

        SweepGradient var10000 = this.mNotProgressSweepGradient;
        var10000.setLocalMatrix(this.mMatrix);
        this.invalidate();
    }

    public final boolean isShortUnProgress() {
        return this.mShortUnProgress;
    }

    /**
     * 内部用于设置进度的方法
     */
    private final void setProgressInner(float position) {
        this.mProgress = position < (float) 0 ? 0.0F : position;
        this.mProgressRatio = this.mMaxProgress == 0.0F ? 0.0F : this.mProgress / this.mMaxProgress;
        float currLength = 1.0F;
        float var3 = this.mProgressRatio;
        this.mProgressRatio = Math.min(currLength, var3);
        currLength = this.mProgressRatio * this.mProgressBarLength;
        this.mPathMeasure.getPosTan(currLength, this.mThumbPos, (float[]) null);
    }

    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        this.setProgress(this.mProgress, false, false);
    }

    private final void init() {
        this.mProgressLineSweepGradient = new SweepGradient(0.0F, 0.0F, new int[]{this.unProgressColor, this.unProgressColor, this.unProgressColor, this.unProgressColor}, new float[]{0.01F, 0.015F, 0.485F, 0.49F});
        int[] colors = this.LINEAR_GRADIENT ? new int[]{0, this.progressColor, 0} : new int[]{0, this.progressColorWithAlpha, this.progressColor, this.progressColorWithAlpha, 0};
        float[] locations = this.LINEAR_GRADIENT ? new float[]{0.005F, 0.25F, 0.495F} : new float[]{0.005F, 0.06F, 0.25F, 0.44F, 0.495F};
        this.mProgressForegroundSweepGradient = new SweepGradient(0.0F, 0.0F, colors, locations);
        this.mNotProgressSweepGradient = new SweepGradient(0.0F, 0.0F, new int[]{this.unProgressColor, this.unProgressTransparentColor}, new float[]{0.491F, 0.513F});
        this.post((Runnable) (new Runnable() {
            public final void run() {
                YunTingProgressBar.this.setMaxProgress(YunTingProgressBar.this.mMaxProgress);
                YunTingProgressBar.this.setProgress(YunTingProgressBar.this.mProgress);
            }
        }));
    }

    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        this.setMeasuredDimension(this.getMeasuredWidth(), (int) ((float) this.getMeasuredWidth() * 106.0F / 1100.0F));
        this.mMaxWidth = (float) this.getMeasuredHeight() * 8.0F / 106.0F;
        this.mPaint.setStrokeWidth((float) this.getMeasuredHeight() * 2.0F / 106.0F);
        this.ovalHeight = (float) this.getMeasuredHeight() * 71.0F / 106.0F;
        this.mRectF.set(0.0F, this.mMaxWidth / (float) 2, (float) this.getMeasuredWidth(), this.ovalHeight - this.mMaxWidth / 2.0F);
        //设置高亮进度条的上半段
        this.mHighLightTotalPath.reset();
        this.mHighLightTotalPath.addArc(this.mRectF, this.startAngle, 180.0F - (float) 2 * this.startAngle);

        //设置高亮进度条的下半段
        this.mRectF.top = -this.mMaxWidth / (float) 2;
        this.mRectF.bottom = this.ovalHeight + this.mMaxWidth / 2.0F;
        this.mHighLightTotalPath.arcTo(this.mRectF, (float) 180 - this.startAngle, (float) 2 * this.startAngle - (float) 180);

        //设置进度条可点击部分的path上半段
        this.mRectF.top = -this.mMaxWidth / (float) 2;
        this.mRectF.bottom = this.ovalHeight - this.mMaxWidth / 2.0F;
        this.mTouchablePath.reset();
        this.mTouchablePath.addArc(this.mRectF, this.startAngle, 180.0F - (float) 2 * this.startAngle);
        //设置进度条可点击部分的path下半段
        this.mRectF.top = this.mMaxWidth / (float) 2;
        this.mRectF.bottom = this.ovalHeight + this.mMaxWidth / 2.0F;
        this.mTouchablePath.arcTo(this.mRectF, (float) 180 - this.startAngle, (float) 2 * this.startAngle - (float) 180);
        // 设置进度条可点击部分的Region
        this.mTouchablePathClipRegion.set((int) this.mRectF.left, (int) this.mRectF.top, (int) this.mRectF.right, (int) this.mRectF.bottom);
        this.mTouchablePathRegion.setPath(this.mTouchablePath, this.mTouchablePathClipRegion);

        //设置进度条正常时的Path和PathMeasure、计算渐变Shader的矩阵和进度条总长度
        this.mRectF.set(0.0F, 0.0F, (float) this.getMeasuredWidth(), this.ovalHeight);
        this.mProgressBarPath.reset();
        this.mProgressBarPath.addArc(this.mRectF, this.startAngle, 180.0F - (float) 2 * this.startAngle);
        this.mPathMeasure.setPath(this.mProgressBarPath, false);
        this.mProgressBarLength = this.mPathMeasure.getLength();
        this.mPathMeasure.getPosTan(0.0F, this.mFirstPointLocation, (float[]) null);
        this.mPathMeasure.getPosTan(this.mProgressBarLength, this.mLastPointLocation, (float[]) null);
        this.mMatrix.setTranslate((this.mRectF.right + this.mRectF.left) / 2.0F, (this.mRectF.bottom + this.mRectF.top) / 2.0F);
        mProgressLineSweepGradient.setLocalMatrix(this.mMatrix);
        mProgressForegroundSweepGradient.setLocalMatrix(this.mMatrix);
        mNotProgressSweepGradient.setLocalMatrix(this.mMatrix);
    }

    protected void onDraw(@NotNull Canvas canvas) {
        super.onDraw(canvas);
        this.mHighLightClipRectF.set(0.0F, 0.0F, this.mThumbPos[0], (float) this.getMeasuredHeight());

        //绘制进度条高亮部分的细线
        mPaint.setShader((Shader) mProgressLineSweepGradient);
        this.mPaint.setColor(this.progressColor);
        this.mPaint.setStyle(Style.STROKE);
        canvas.save();
        canvas.clipRect(this.mHighLightClipRectF);
        canvas.drawPath(this.mProgressBarPath, this.mPaint);
        canvas.restore();
        //绘制进度条高亮部分的前景
        canvas.save();
        mPaint.setShader((Shader) mProgressForegroundSweepGradient);
        this.mPaint.setStyle(Style.FILL);
        canvas.clipRect(this.mHighLightClipRectF);
        canvas.drawPath(this.mHighLightTotalPath, this.mPaint);
        canvas.restore();
        //绘制进度条未到达的进度部分的细线
        this.mHighLightClipRectF.set(this.mThumbPos[0], 0.0F, (float) this.getMeasuredWidth(), (float) this.getMeasuredHeight());
        this.mPaint.setColor(this.unProgressColor);
        this.mPaint.setShader((Shader) null);
        this.mPaint.setStyle(Style.STROKE);
        canvas.save();
        canvas.clipRect(this.mHighLightClipRectF);
        canvas.drawPath(this.mProgressBarPath, this.mPaint);
        canvas.restore();

        //绘制两边的非进度条部分
        this.mPaint.setColor(Color.parseColor("#0084FF"));
        mPaint.setShader((Shader) mNotProgressSweepGradient);
        float notProgressSweepAngle = ((float) 180 - this.startAngle) * 2.2F;
        canvas.drawArc(this.mRectF, this.startAngle + this.gapAngle, notProgressSweepAngle, false, this.mPaint);
        //镜像反转，用于绘制右侧渐变的非进度条部分
        canvas.save();
        canvas.scale(-1.0F, 1.0F, (this.mRectF.right + this.mRectF.left) / 2.0F, (this.mRectF.bottom + this.mRectF.top) / (float) 2);
        canvas.drawArc(this.mRectF, this.startAngle + this.gapAngle, notProgressSweepAngle, false, this.mPaint);
        canvas.restore();

        //绘制滑块位置
        float thumbScaledSize = this.mThumpSize / (float) 2 * this.mThumbScale;
        this.thumbRect.set((int) (this.mThumbPos[0] - thumbScaledSize), (int) (this.mThumbPos[1] - thumbScaledSize), (int) (this.mThumbPos[0] + thumbScaledSize), (int) (this.mThumbPos[1] + thumbScaledSize));
        Drawable var4 = this.mThumbBitmap;
        if (var4 != null) {
            var4.setBounds(this.thumbRect);
        }

        var4 = this.mThumbBitmap;
        if (var4 != null) {
            var4.draw(canvas);
        }

    }

    public boolean onTouchEvent(@Nullable MotionEvent event) {
        if (!this.isEnabled()) {
            return super.onTouchEvent(event);
        } else {
            YunTingProgressBar.YunTingProgressBarCallback var10000;
            ViewParent var5;
            switch (event.getAction()) {
                case  MotionEvent.ACTION_DOWN:
                    this.downX = event.getX();
                    if (this.isTouchThumb(event)) {
                        var5 = this.getParent();
                        if (var5 != null) {
                            var5.requestDisallowInterceptTouchEvent(true);
                        }

                        this.isDownThumb = true;
                        this.startSetThumbScaleAnimation();
                        this.getTouchProgress(event);
                        if (this.mYunTingProgressBarCallback != null) {
                            var10000 = this.mYunTingProgressBarCallback;
                            var10000.onStartTrackingTouch(this);
                            var10000 = this.mYunTingProgressBarCallback;
                            var10000.onProgressChanged(this, this.mProgress, true);
                        }

                        return true;
                    }

                    if (this.isTouchSeek(event)) {
                        return true;
                    }
                    break;
                case MotionEvent.ACTION_UP:
                case MotionEvent.ACTION_CANCEL:
                    boolean result = false;
                    if (this.isMoveThumb) {
                        if (this.mYunTingProgressBarCallback != null) {
                            this.getTouchProgress(event);
                            var10000 = this.mYunTingProgressBarCallback;
                            var10000.onStopTrackingTouch(this);
                            var10000 = this.mYunTingProgressBarCallback;
                            var10000.onProgressChanged(this, this.mProgress, true);
                        }

                        result = true;
                    } else if (this.isEnableClickToSeek && this.isTouchSeek(event)) {
                        Log.d("cai", "-----onTouchEvent----点击了进度条");
                        this.getTouchProgress(event);
                        if (this.mYunTingProgressBarCallback != null) {
                            var10000 = this.mYunTingProgressBarCallback;
                            var10000.onSingleClick(this);
                            var10000 = this.mYunTingProgressBarCallback;
                            var10000.onProgressChanged(this, this.mProgress, true);
                        }

                        this.invalidate();
                        result = true;
                    }

                    this.downX = 0.0F;
                    this.isMoveThumb = false;
                    this.isDownThumb = false;
                    this.stopSetThumbScaleAnimation();
                    var5 = this.getParent();
                    if (var5 != null) {
                        var5.requestDisallowInterceptTouchEvent(true);
                    }

                    if (result) {
                        return true;
                    }
                    break;
                case MotionEvent.ACTION_MOVE:
                    if (this.isDownThumb) {
                        this.isMoveThumb = true;
                        if (!this.mUseSmoothTouch) {
                            float var3 = event.getX() - this.downX;
                            int quotient = (int) (Math.abs(var3) / this.touchSlop);
                            if (quotient == this.progressInner) {
                                Log.e(this.TAG, "move时事件判断条件不满足：touchSlop = " + this.touchSlop + ",dX=" + (event.getRawX() - this.downX) + ",quotient=" + quotient + ",this.progressInner=" + this.progressInner);
                                return true;
                            }

                            this.progressInner = quotient;
                        }

                        this.getTouchProgress(event);
                        if (this.mYunTingProgressBarCallback != null) {
                            var10000 = this.mYunTingProgressBarCallback;
                            var10000.onProgressChanged(this, this.mProgress, true);
                        }

                        this.invalidate();
                        return true;
                    }
            }

            return super.onTouchEvent(event);
        }
    }

    private final void getTouchProgress(MotionEvent event) {
        boolean small = true;
        float var10000;
        float ratioX;
        float var5;
        if (event.getX() < (float) this.getMeasuredWidth() / 2.0F) {
            small = true;
            ratioX = event.getX();
            var5 = this.mFirstPointLocation[0];
            var10000 = Math.max(ratioX, var5);
        } else {
            small = false;
            ratioX = event.getX();
            var5 = this.mLastPointLocation[0];
            var10000 = Math.min(ratioX, var5);
        }

        float x = var10000;
        ratioX = (x - this.mFirstPointLocation[0]) / (this.mLastPointLocation[0] - this.mFirstPointLocation[0]);
        Log.e(this.TAG, "getTouchProgress::ratioX=" + ratioX + ",event.x=" + event.getX() + ",x=" + x + ",smallest=" + small);
        this.setProgressInner(ratioX * this.mMaxProgress);
    }
    /**
     * 计算电机的点与椭圆的交点
     * 根据点击的x求y
     */
    private final PointF calcIntersection(MotionEvent event) {
        //椭圆公式为：x^2/a^2+y^2/b^2=1
        PointF intersection = new PointF(0.0F, 0.0F);
        double a = (double) this.getMeasuredWidth() / 2.0D;
        double b = (double) this.ovalHeight / 2.0D;
        float var7;
        float var8;
        float var10001;
        if ((double) event.getX() < a) {
            var7 = event.getX();
            var8 = this.mFirstPointLocation[0];
            var10001 = Math.max(var7, var8);
        } else {
            var7 = event.getX();
            var8 = this.mLastPointLocation[0];
            var10001 = Math.min(var7, var8);
        }

        intersection.x = var10001;
        double var11 = 2.0D;
        double var10000 = Math.pow(b, var11);
        double var13 = (double) 1;
        double var9 = (double) intersection.x - a;
        var9 = Math.abs(var9);
        var11 = 2.0D;
        double var10002 = Math.pow(var9, var11);
        var11 = 2.0D;
        var9 = var10000 * (var13 - var10002 / Math.pow(a, var11));
        double y = Math.sqrt(var9);
        //必须半个高度减，因为计算的点在椭圆下半部分
        intersection.y = (float) (b + y);
        Log.e(this.TAG, "calcIntersection::x--->" + intersection.x + " ,y--->" + intersection.y + ",tempY--->" + y);
        return intersection;
    }
    /**
     * 是否点在进度条上
     *
     * @return
     */
    private final boolean isTouchSeek(MotionEvent event) {
        return this.mTouchablePathRegion.contains((int) event.getX(), (int) event.getY());
    }
    /**
     * 是否点在滑块上
     */
    private final boolean isTouchThumb(MotionEvent event) {
        return this.thumbRect.contains((int) event.getX(), (int) event.getY());
    }

    private final void startSetThumbScaleAnimation() {
        ValueAnimator var10000;
        if (this.thumbScaleAnimation == null) {
            this.thumbScaleAnimation = ValueAnimator.ofFloat(new float[]{1.0F, this.mThumbScaleMultipleWhenTouch}).setDuration(50L);
            var10000 = this.thumbScaleAnimation;
            var10000.addUpdateListener((AnimatorUpdateListener) (new AnimatorUpdateListener() {
                public final void onAnimationUpdate(ValueAnimator animation) {
                    YunTingProgressBar var10000 = YunTingProgressBar.this;
                    Object var10001 = animation.getAnimatedValue();
                    if (var10001 == null) {
                    } else {
                        var10000.mThumbScale = (Float) var10001;
                        YunTingProgressBar.this.invalidate();
                    }
                }
            }));
        }

        thumbScaleAnimation.setFloatValues(new float[]{this.mThumbScale, this.mThumbScaleMultipleWhenTouch});
        if (!thumbScaleAnimation.isRunning()) {
            thumbScaleAnimation.start();
        }

    }

    private final void stopSetThumbScaleAnimation() {
        if (this.thumbScaleAnimation != null) {
            if (thumbScaleAnimation.isRunning()) {
                thumbScaleAnimation.cancel();
            }
            thumbScaleAnimation.setFloatValues(new float[]{this.mThumbScale, 1.0F});
            thumbScaleAnimation.start();
        }
    }
    /**
     * 设置监听
     *
     * @param yunTingProgressBarCallback
     */
    public final void setYunTingProgressBarCallback(@NotNull YunTingProgressBar.YunTingProgressBarCallback yunTingProgressBarCallback) {
        this.mYunTingProgressBarCallback = yunTingProgressBarCallback;
    }



    public interface YunTingProgressBarCallback {
        /**
         * 进度发生变化
         *
         * @param seekBar  拖动条
         * @param progress 当前进度数值
         * @param isUser   是否是用户操作, true 表示用户拖动, false 表示通过代码设置
         */
        void onProgressChanged(@Nullable YunTingProgressBar seekBar, float progress, boolean isUser);
        /**
         * 用户开始拖动
         *
         * @param seekBar 拖动条
         */
        void onStartTrackingTouch(@Nullable YunTingProgressBar seekBar);
        /**
         * 用户结束拖动
         *
         * @param seekBar 拖动条
         */
        void onStopTrackingTouch(@Nullable YunTingProgressBar seekBar);
        /**
         * 点击调整进度
         *
         * @param seekBar
         */
        void onSingleClick(@Nullable YunTingProgressBar seekBar);
    }
}
