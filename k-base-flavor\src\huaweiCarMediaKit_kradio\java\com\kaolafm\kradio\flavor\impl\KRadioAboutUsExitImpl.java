package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.util.Log;

import com.kaolafm.kradio.huawei.utils.Constant;
import com.kaolafm.kradio.lib.base.flavor.KRadioAboutUsExitInter;

public class KRadioAboutUsExitImpl implements KRadioAboutUsExitInter {
    private static final String TAG = Constant.TAG;

    @Override
    public void onExit(Activity activity) {
        Log.i(TAG, "onExit");
        activity.finish();
    }
}
