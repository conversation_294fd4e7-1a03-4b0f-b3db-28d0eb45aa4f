package com.kaolafm.kradio.online.categories.tab;

import android.Manifest;
import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Canvas;
import android.graphics.Rect;
import android.location.LocationManager;
import android.os.Bundle;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewStub;
import android.view.animation.AnimationUtils;
import android.view.animation.GridLayoutAnimationController;
import android.view.animation.LayoutAnimationController;
import android.widget.TextView;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.common.SubcategoryItemBean;
import com.kaolafm.kradio.common.bean.BroadcastRadioDetailData;
import com.kaolafm.kradio.common.report.ReportParamUtil;
import com.kaolafm.kradio.common.report.ReportUtil;
import com.kaolafm.kradio.common.utils.PermissionUtils;
import com.kaolafm.kradio.common.widget.GridRecyclerView;
import com.kaolafm.kradio.common.widget.UserSubItemDecoration;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.ui.BaseLazyFragment;
import com.kaolafm.kradio.lib.base.ui.online.BaseShowHideLazyFragment;
import com.kaolafm.kradio.lib.base.ui.online.BaseViewPagerLazyFragment;
import com.kaolafm.kradio.lib.common.ResType;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.KaolaAppConfigData;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.RecyclerViewExposeUtil;
import com.kaolafm.kradio.lib.utils.RecyclerViewUtils;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.online.categories.CategoryConstant;
import com.kaolafm.kradio.online.categories.HorizontalCategoryItemSpace;
import com.kaolafm.kradio.online.categories.HorizontalSpanSizeLookup;
import com.kaolafm.kradio.online.categories.adapter.HorizontalSubcategoryAdapter;
import com.kaolafm.kradio.online.categories.broadcast.BroadcastPresent;
import com.kaolafm.kradio.online.categories.broadcast.IBroadcastView;
import com.kaolafm.kradio.online.categories.event.SubcategoryItemEvent;
import com.kaolafm.kradio.online.common.event.CancelJumpToOnlinePlayerEvent;
import com.kaolafm.kradio.online.common.event.OnlinePlayerFragmentJumpActionEvent;
import com.kaolafm.kradio.scene.launcher.InitService;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

/**
 * 具体每一页二级分类的Fragment页面
 *
 * <AUTHOR>
 */
public class TabItemFragment extends BaseViewPagerLazyFragment<TabItemContract.IPresenter>
        implements TabItemContract.IView, RecyclerViewExposeUtil.OnItemExposeListener, IBroadcastView {
    public static final String TAG = "TabItemFragment";

    private GridLayoutManager mGridLayoutManager;

    private GridRecyclerView mRvSubcategoryContent;
    private View mErrorView;

    private HorizontalSpanSizeLookup mSpanSizeLookup;

    private ViewStub mVsLayoutErrorPage;
    private View mLoadingView;
    private View clPermissionTip;

    private HorizontalSubcategoryAdapter mSubcategoryAdapter;
    private GridLayoutAnimationController mGridController;
    private RecyclerViewScrollListener mRecyclerViewScrollListener;
    private int spaceResId = R.integer.online_subcategory_item_span_count;

    private long categoryId;
    private boolean isLocalBroadcast = false;
    //    private HorizontalCategoryItemSpace mHorizontalCategoryItemSpace = new HorizontalCategoryItemSpace();
    private RecyclerViewExposeUtil exposeUtil;

    @Override
    protected void changeViewLayoutForStatusBar(View view) {
        // Do-Nothing
        //因为是子fragment,所以不需要设置pading.
    }

    @Override
    protected void lazyLoad() {
        if (isLocalBroadcast) {
            if (!hasGpsPermission() || !hasLocationInfo()) {
                showGpsPermissionTip();
                return;
            }
        }
        if (mPresenter != null) {
            showLoading();
            mPresenter.loadData();
        }
    }

    public void update() {
        if (mPresenter != null) {
            showLoading();
            mPresenter.loadData();
        }
    }


    private void showGpsPermissionTip() {
        clPermissionTip.setVisibility(View.VISIBLE);
    }

    private void hideGpsPermissionTip() {
        clPermissionTip.setVisibility(View.GONE);
    }

    private boolean hasGpsPermission() {
        String per[] = new String[]{
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION
        };
        return PermissionUtils.checkPermission(getContext(), per);
    }

    private boolean isGpsOpen() {
        LocationManager locationManager
                = (LocationManager) getContext().getApplicationContext().getSystemService(Context.LOCATION_SERVICE);
        // 通过GPS卫星定位，定位级别可以精确到街（通过24颗卫星定位，在室外和空旷的地方定位准确、速度快）
        boolean gps = locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER);
        // 通过WLAN或移动网络(3G/2G)确定的位置（也称作AGPS，辅助GPS定位。主要用于在室内或遮盖物（建筑群或茂密的深林等）密集的地方定位）
        boolean network = locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER);
        boolean passive = locationManager.isProviderEnabled(LocationManager.PASSIVE_PROVIDER);
        if (gps || network || passive) {
            return true;
        }
        return false;
    }

    private boolean hasLocationInfo() {
        return !TextUtils.isEmpty(KaolaAppConfigData.getInstance().getLat())
                && !TextUtils.isEmpty(KaolaAppConfigData.getInstance().getLng());
    }

    @Override
    public boolean useEventBus() {
        return true;
    }

    private boolean isJumpingToPlayerFragment = false;

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void observeEvent(OnlinePlayerFragmentJumpActionEvent event) {
        isJumpingToPlayerFragment = event.getAction() == OnlinePlayerFragmentJumpActionEvent.ACTION_START;
    }

    @Override
    public void onResume() {
        super.onResume();
        checkLoadData();
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser) {
            checkLoadData();
        }
    }

    private void checkLoadData() {
        if (!isLocalBroadcast) {
            return;
        }
//        if (clPermissionTip.getVisibility() == View.GONE) {
//            return;
//        }
//        if (hasGpsPermission() && isGpsOpen() && !hasLocationInfo()) {
//            InitService.getLocation();//速度很慢
//        }
        // 没有定位权限的，返回界面重新检查下
//        if (hasGpsPermission() && hasLocationInfo()) {
//            hideGpsPermissionTip();
//            if (mPresenter != null) {
//                showLoading();
//                mPresenter.loadData();
//
//            }
//        }
    }

    @Override
    public void setSelected() {
        if (mSubcategoryAdapter != null) {
            mSubcategoryAdapter.setSelected();
        }
    }

    @Override
    public void showData(List<SubcategoryItemBean> data) {
        setData(data);
        setSelected();
    }


    @Override
    public void showMoreData(List<SubcategoryItemBean> data) {
        hideLoading();
        if (ListUtil.isEmpty(data)) {
            return;
        }
        mSubcategoryAdapter.addDataList(data);
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001697153594?userId=1229522问题
        setSelected();
    }

    @Override
    public void showError(Exception exception) {
        hideLoading();
        Log.i(TAG, getMTag() + "showError: error = " + exception.getMessage());
        if (mVsLayoutErrorPage != null) {
            mErrorView = mVsLayoutErrorPage.inflate();
            TextView tvNetworkError = mErrorView.findViewById(R.id.tv_status_page_network_error);
            mVsLayoutErrorPage = null;
            if (tvNetworkError != null) {
                tvNetworkError.setOnClickListener(v -> {
                    if (!AntiShake.check(v.getId())) {
                        if (!NetworkUtil.isNetworkAvailable(getContext(), true)) {
                            return;
                        }
                        mSubcategoryAdapter.clear();
                        if (mPresenter != null) {
                            showLoading();
                            mPresenter.loadData();
                        }
                    }
                });
            }
        }

        isLoaded = false;
        ViewUtil.setViewVisibility(mErrorView, View.VISIBLE);
    }

    @Override
    public void showMoreDataError(Exception exception) {
        hideLoading();
    }

    @Override
    protected int getLayoutId() {
        return R.layout.online_fragment_subcategory_item;
    }

    @Override
    protected TabItemContract.IPresenter createPresenter() {
        Bundle arg = getArguments();
        boolean haveMembers = true;
        if (arg != null) {
            haveMembers = arg.getBoolean(CategoryConstant.HAVE_MEMBERS, true);
        }
        isLocalBroadcast = arg.getBoolean(CategoryConstant.IS_LOCAL_RADIO, false);
        categoryId = getArguments().getLong(CategoryConstant.CATEGORY_ID);
        return new TabItemPresenter(this, categoryId, haveMembers);
    }

    @Override
    public void initView(View view) {
        assignViews(view);
        setup();
    }


    private void assignViews(View view) {
        mRvSubcategoryContent = view.findViewById(R.id.rv_subcategory_content);
        mVsLayoutErrorPage = view.findViewById(R.id.vs_layout_error_page);
        mLoadingView = view.findViewById(R.id.category_loading_view);
        clPermissionTip = view.findViewById(R.id.clPermissionTip);
    }

    private void setup() {
        mSubcategoryAdapter = new HorizontalSubcategoryAdapter();
        mSubcategoryAdapter.setOnItemClickListener((itemView, viewType, subcategoryItemBean, position) -> {
            //在所有播放事件开始前发送一次取消延时跳转播放页的逻辑，防止加载过程中跳转到了播放页导致播放页内容与光圈数据不一致
            EventBus.getDefault().post(new CancelJumpToOnlinePlayerEvent());
            if (isJumpingToPlayerFragment) return;
            if (NetworkUtil.isNetworkAvailable(getContext(), true)) {
                if (mPresenter != null) {
                    // TODO: 2019/3/2 后续修改
                    mPresenter.onClick(subcategoryItemBean, mSubcategoryAdapter, position);
                }
            }
        });
        mGridLayoutManager = new GridLayoutManager(getContext(), CategoryConstant.GRID_TOTAL_SPAN_COUNT, GridLayoutManager.HORIZONTAL, false);
        mSpanSizeLookup = new HorizontalSpanSizeLookup(mSubcategoryAdapter);
        mGridLayoutManager.setSpanSizeLookup(mSpanSizeLookup);

        mRvSubcategoryContent.setLayoutManager(mGridLayoutManager);
        //加载动画
        mGridController = (GridLayoutAnimationController) AnimationUtils.loadLayoutAnimation(getContext(), R.anim.online_layout_subcategory_item_load);

        mRvSubcategoryContent.setAdapter(mSubcategoryAdapter);

        //间隔
//        mHorizontalCategoryItemSpace.setPorFirstRowTop(ResUtil.getDimen(R.dimen.online_subcategory_content_top_duration));
//        mRvSubcategoryContent.addItemDecoration(mHorizontalCategoryItemSpace);
        mRvSubcategoryContent.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                final int spanCount = getSpanCount(parent);
                final int childCount = parent.getAdapter().getItemCount();
                final int adapterPosition = parent.getChildAdapterPosition(view);
                if (isFirst(adapterPosition, spanCount, childCount)) {
                    outRect.left = ResUtil.getDimen(R.dimen.online_subscriptions_padding_left);
                } else {
                    outRect.left = 0;
                }
                outRect.right = ResUtil.getDimen(R.dimen.x40);
            }

            private boolean isFirst(int position, int spanCount, int childCount) {
                Log.e("isFirstColumn", "position is " + position);
                Log.e("isFirstColumn", "spanCount is " + spanCount);
                Log.e("isFirstColumn", "childCount is " + childCount);
                return position < spanCount;
            }

            private int getSpanCount(RecyclerView parent) {
                RecyclerView.LayoutManager layoutManager = parent.getLayoutManager();

                if (layoutManager instanceof GridLayoutManager) {
                    return ((GridLayoutManager) layoutManager).getSpanCount();
                } else if (layoutManager instanceof StaggeredGridLayoutManager) {
                    return ((StaggeredGridLayoutManager) layoutManager).getSpanCount();
                } else {
                    throw new UnsupportedOperationException("the GridDividerItemDecoration can only be used in " +
                            "the RecyclerView which use a GridLayoutManager or StaggeredGridLayoutManager");
                }
            }
        });
        exposeUtil = new RecyclerViewExposeUtil();
        exposeUtil.setRecyclerItemExposeListener(mRvSubcategoryContent, this);
    }

    BroadcastPresent broadcastPresent;

    /**
     * 切换省
     *
     * @param id
     */
    public void changeTabItemDate(int id) {
        if (broadcastPresent == null)
            broadcastPresent = new BroadcastPresent(this);
        broadcastPresent.loadFirstData(id);
    }

    private void setData(List<SubcategoryItemBean> data) {
        hideLoading();
        ViewUtil.setViewVisibility(mErrorView, View.GONE);

        if (data != null && !data.isEmpty()) {
            if (data.get(0).getItemType() == SubcategoryItemBean.TYPE_ITEM_BROADCAST_CATEGORY) {
//                changeRvGridManagerCategory();
                EventBus.getDefault().post(new SubcategoryItemEvent(data));
                changeTabItemDate((int) data.get(0).getId());
                return;
            }
            mRvSubcategoryContent.scrollToPosition(0);
            mSubcategoryAdapter.setDataList(data);
            mRvSubcategoryContent.scheduleLayoutAnimation();
            initLayoutAnimationAndHead(data.get(0).getItemType());
            if (mPresenter.isHasNextPage()) {
                mRecyclerViewScrollListener = new RecyclerViewScrollListener(TabItemFragment.this);
                mRvSubcategoryContent.addOnScrollListener(mRecyclerViewScrollListener);
            }

        }

    }

    private void changeRvGridManagerCategory() {
        if (spaceResId == R.integer.online_subcategory_categroy_item_span_count) {
            return;
        }
        spaceResId = R.integer.online_subcategory_categroy_item_span_count;
        int spanCount = ResUtil.getInt(spaceResId);
        mSpanSizeLookup.setSpanCount(spanCount);
        mGridLayoutManager.setSpanCount(spanCount);
        mSubcategoryAdapter.notifyDataSetChanged();
    }


    /**
     * 设置加载动画和更新列表头布局。
     * 垂直布局用mLayoutController， gride布局用mGridController。
     * 切换的时候，判断是否需要更换。
     */
    private void initLayoutAnimationAndHead(int itemType) {
        LayoutAnimationController layoutAnimation = mRvSubcategoryContent.getLayoutAnimation();
        if (!(layoutAnimation instanceof GridLayoutAnimationController)) {
            mRvSubcategoryContent.setLayoutAnimation(mGridController);
        }
    }

    @Override
    public void onItemViewVisible(boolean visible, int position) {
        if (visible && mSubcategoryAdapter != null) {
            SubcategoryItemBean bean = mSubcategoryAdapter.getItemData(position);
            ReportUtil.addContentShowEvent("", ReportParamUtil.getRadioType(bean), "",
                    String.valueOf(bean.getId()), ReportParamUtil.getEventTag(bean.getVip() == 1, bean.getFine() == 1),
                    getPageId(), categoryId + "", "" + position);
        }
    }

    @Override
    public String getPageId() {
        Bundle arg = getArguments();
        if (arg != null) {
            return arg.getString(CategoryConstant.PAGE_ID);
        }
        return super.getPageId();
    }

    /**
     * 省市台广播；列表
     *
     * @param resultList
     * @param isLoadMore 是否是加载更多，true是
     */
    @Override
    public void notifyDataChange(List<BroadcastRadioDetailData> resultList, boolean isLoadMore) {
        List<SubcategoryItemBean> data = new ArrayList<>();
        SubcategoryItemBean subcategoryItemBean;
        for (BroadcastRadioDetailData broadcastRadioDetailData : resultList) {
            subcategoryItemBean = new SubcategoryItemBean();
            subcategoryItemBean.setName(broadcastRadioDetailData.getName());
            subcategoryItemBean.setId(broadcastRadioDetailData.getBroadcastId());
            subcategoryItemBean.setCoverUrl(broadcastRadioDetailData.getIcon());
            subcategoryItemBean.setFreq(broadcastRadioDetailData.getFreq());
            subcategoryItemBean.setItemType(SubcategoryItemBean.TYPE_ITEM_BROADCAST_CATEGORY);
            subcategoryItemBean.setResType(ResType.BROADCAST_TYPE);
            data.add(subcategoryItemBean);
        }
        mRvSubcategoryContent.scrollToPosition(0);
        mSubcategoryAdapter.setDataList(data);
        mRvSubcategoryContent.scheduleLayoutAnimation();
        if (data.size() > 0)
            initLayoutAnimationAndHead(data.get(0).getItemType());
        if (mPresenter.isHasNextPage()) {
            mRecyclerViewScrollListener = new RecyclerViewScrollListener(TabItemFragment.this);
            mRvSubcategoryContent.addOnScrollListener(mRecyclerViewScrollListener);
        }
        setSelected();
    }

    /**
     * 省市台广播；列表
     */
    @Override
    public void showError() {

    }

    private static class RecyclerViewScrollListener extends RecyclerView.OnScrollListener {
        WeakReference<TabItemFragment> tabItemFragmentWeakReference;

        public RecyclerViewScrollListener(TabItemFragment tabItemFragment) {
            tabItemFragmentWeakReference = new WeakReference<>(tabItemFragment);
        }

        @Override
        public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
            super.onScrollStateChanged(recyclerView, newState);
            //防止重复加载，只有松开手后再加载
            if (newState != RecyclerView.SCROLL_STATE_IDLE) {
                return;
            }
            if (tabItemFragmentWeakReference == null) {
                return;
            }
            TabItemFragment tabItemFragment = tabItemFragmentWeakReference.get();
            if (tabItemFragment == null) {
                return;
            }
            if (tabItemFragment.mRvSubcategoryContent == null) {
                return;
            }
            GridLayoutManager gridLayoutManager = (GridLayoutManager) tabItemFragment.mRvSubcategoryContent.getLayoutManager();
            if (gridLayoutManager == null) {
                return;
            }
            int adapterSize = tabItemFragment.mSubcategoryAdapter.getItemCount();

            if (gridLayoutManager.findLastVisibleItemPosition() + 1 < adapterSize) {
                return;
            }
            Log.i(TAG, "滑动到了加载下一页数据");

            if (!tabItemFragment.isHasNextPage()) {
                return;
            }
            Log.i(TAG, "有下一页数据.加载数据");
            tabItemFragment.loadMore();
        }

        @Override
        public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
            super.onScrolled(recyclerView, dx, dy);
        }
    }

    private boolean isHasNextPage() {
        if (mPresenter == null) {
            return false;
        }
        return mPresenter.isHasNextPage();
    }

    private void loadMore() {
        if (mPresenter == null) {
            return;
        }
        if (!NetworkUtil.isNetworkAvailable(getContext(), true)) {
            return;
        }
        showLoading();
        mPresenter.loadMore();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mRvSubcategoryContent != null && mRecyclerViewScrollListener != null) {
            mRvSubcategoryContent.removeOnScrollListener(mRecyclerViewScrollListener);
        }
    }

    @Override
    protected void showLoading() {
        super.showLoading();
        ViewUtil.setViewVisibility(mLoadingView, View.VISIBLE);

    }

    @Override
    protected void hideLoading() {
        super.hideLoading();
        ViewUtil.setViewVisibility(mLoadingView, View.GONE);
    }

    @Override
    protected void showAccordingToScreen(int orientation) {
        int firstVisibleItemPosition = mGridLayoutManager.findFirstVisibleItemPosition();
        int spanCount = ResUtil.getInt(spaceResId);
        mSpanSizeLookup.setSpanCount(spanCount);
        int left, top, right, bottom;
        mGridLayoutManager.setSpanCount(spanCount);
        //横屏
//        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
        mGridLayoutManager.setOrientation(GridLayoutManager.HORIZONTAL);
        //竖屏
//        } else {
//            mGridLayoutManager.setOrientation(GridLayoutManager.VERTICAL);
//        }

        left = 0;
        top = orientation == Configuration.ORIENTATION_LANDSCAPE ? ResUtil.getDimen(R.dimen.online_subcategory_content_top_duration) : 0;
        right = ResUtil.getDimen(R.dimen.online_subcategory_content_right_duration);
        bottom = ResUtil.getDimen(R.dimen.online_subcategory_content_bottom_duration);
        mRvSubcategoryContent.setPadding(left, top, right, bottom);

        mSubcategoryAdapter.notifyDataSetChanged();

        //自动滚动到上一个位置。
        //offset至少要有一个像素的便宜，否则某些手机和车机会不滚动
        mGridLayoutManager.scrollToPositionWithOffset(firstVisibleItemPosition, -1);
        RecyclerViewUtils.resetRecyclerView(mRvSubcategoryContent);

        ConstraintLayout.LayoutParams lp = (ConstraintLayout.LayoutParams) mLoadingView.findViewById(R.id.llRefresh).getLayoutParams();
        lp.verticalBias = ResUtil.getFloat(R.dimen.online_loading_vertical_bias);
        mLoadingView.findViewById(R.id.llRefresh).setLayoutParams(lp);
    }

    @Override
    public void onMultiWindowModeChanged(boolean isInMultiWindowMode) {
        super.onMultiWindowModeChanged(isInMultiWindowMode);
        Log.i(TAG, "onMultiWindowModeChanged " + isInMultiWindowMode);
//        if (mRvSubcategoryContent != null) {
//            RecyclerView.Adapter adapter = mRvSubcategoryContent.getAdapter();
//            RecyclerView.LayoutManager manager = mRvSubcategoryContent.getLayoutManager();
//            mRvSubcategoryContent.setAdapter(null);
//            mRvSubcategoryContent.setLayoutManager(null);
//            mRvSubcategoryContent.getRecycledViewPool().clear();
//            mRvSubcategoryContent.setLayoutManager(manager);
//            mRvSubcategoryContent.setAdapter(adapter);
//        }
    }
}
