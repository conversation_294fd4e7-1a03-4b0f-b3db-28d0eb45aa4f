package com.kaolafm.kradio.online.common.view;

import android.content.Context;
import android.util.AttributeSet;

import skin.support.widget.SkinCompatTextView;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-07-12 17:22
 ******************************************/
public final class OnlineAutoMarqueenTextView extends SkinCompatTextView {
    private boolean isFocusable = true;

    public OnlineAutoMarqueenTextView(Context context) {
        super(context);
    }

    public OnlineAutoMarqueenTextView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public OnlineAutoMarqueenTextView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public boolean isFocused() {
        return isFocusable;
    }

    public void setFocusable(boolean isFocusable) {
        this.isFocusable = isFocusable;
    }
}
