package com.kaolafm.kradio.lib.sdk.utils;

import androidx.annotation.IntDef;
import android.util.Log;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.SharedPreferenceUtil;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.HashMap;

public class PerformanceSettingMananger {

    private static final String SP_PERFORMANCE_SETTING = "performanceSetting";

    private static final String SP_KEY_IS_NEED_BLUR = "isNeedBlur";

    private static final String SP_KEY_SHOW_CONFIG = "showConfig";

    private static final String SP_KEY_HOME_IMAGE_SIZE = "homeImageSize";

    private static final String SP_KEY_CATEGORIES_IMAGE_SIZE = "categoriesImageSize";

    private static final String SP_KEY_HOME_IS_NEED_MONGOLIA = "homeIsNeedMongolia";

    private static final String SP_KEY_HOME_IS_JPG_WEBP = "homeIsJpgOrWebp";

    private static final String SP_KEY_HOME_IS_START_SPLASH = "homeIsStartSplash";

    private static final String SP_KEY_PLAYER_IS_NEED_MONGOLIA = "playerIsNeedMongolia";

    private static final String SP_KEY_IS_NEED_ANIMATION = "isNeedAnimation";

    public static final int HIGH_SIZE = 2;

    public static final int MEDIUM_SIZE = 1;

    public static final int LOW_SIZE = 0;

    public static final int IMAGE_SIZE_DEFAULT = LOW_SIZE;

    private int mCategoriesImageSize = 0;

    private int mHomeImageSize = 0;

    private SharedPreferenceUtil mSPUtil;

    @IntDef({HIGH_SIZE, MEDIUM_SIZE, LOW_SIZE})
    @Retention(RetentionPolicy.SOURCE)
    public @interface Size {
    }

    private static final class PerformanceSettingManangerHolder {
        private static final PerformanceSettingMananger INSTANCE = new PerformanceSettingMananger();
    }

    public static PerformanceSettingMananger getInstance() {
        return PerformanceSettingManangerHolder.INSTANCE;
    }

    private PerformanceSettingMananger() {
        init();
    }

    private void init() {
        mSPUtil = SharedPreferenceUtil
                .getInstance(AppDelegate.getInstance().getContext(), SP_PERFORMANCE_SETTING);
    }


    public void showConfig() {
        mSPUtil.putBoolean(SP_KEY_SHOW_CONFIG, false);
    }

    /**
     * 是否显示配置页面
     */
    public boolean isShowConfig() {
        return mSPUtil.getBoolean(SP_KEY_SHOW_CONFIG, false);
    }

    public void setHomeImageSize(@Size int type) {
        mHomeImageSize = type;
        mSPUtil.putInt(SP_KEY_HOME_IMAGE_SIZE, type);
    }

    public @Size int getHomeImageSize() {
        if (mHomeImageSize == 0) {
//            默认是低配 LOW_SIZE
            mHomeImageSize = mSPUtil.getInt(SP_KEY_HOME_IMAGE_SIZE, LOW_SIZE);
        }
        return mHomeImageSize;
    }

    public void setCategoriesImageSize(@Size int type) {
        mCategoriesImageSize = type;
        mSPUtil.putInt(SP_KEY_CATEGORIES_IMAGE_SIZE, type);
    }

    public @Size int getCategoriesImageSize() {
        if (mCategoriesImageSize == 0) {
            mCategoriesImageSize = mSPUtil.getInt(SP_KEY_CATEGORIES_IMAGE_SIZE, IMAGE_SIZE_DEFAULT);
        }
        return mCategoriesImageSize;
    }

    public void setIsNeedBlur(boolean isNeedBlur) {
        mSPUtil.putBoolean(SP_KEY_IS_NEED_BLUR, isNeedBlur);
    }

    public boolean getIsNeedBlur() {
        return mSPUtil.getBoolean(SP_KEY_IS_NEED_BLUR, true);
    }



    public void setHomeIsNeedMongolia(boolean isNeedMongolia) {
        mSPUtil.putBoolean(SP_KEY_HOME_IS_NEED_MONGOLIA, isNeedMongolia);
    }

    public boolean getHomeIsNeedMongolia() {
        return mSPUtil.getBoolean(SP_KEY_HOME_IS_NEED_MONGOLIA, true);
    }

    public void setHomeIsJpgOrWebp(boolean isJpgOrWebp) {
        mSPUtil.putBoolean(SP_KEY_HOME_IS_JPG_WEBP, isJpgOrWebp);
    }

    public boolean getHomeIsJpgOrWebp() {
        return mSPUtil.getBoolean(SP_KEY_HOME_IS_JPG_WEBP, true);
    }
    public void setHomeIsStartSplash(boolean isStartSplash) {
        mSPUtil.putBoolean(SP_KEY_HOME_IS_START_SPLASH, isStartSplash);
    }

    public boolean getHomeIsStartSplash() {
        return mSPUtil.getBoolean(SP_KEY_HOME_IS_START_SPLASH, false);
    }


    public void setPlayerIsNeedMongolia(boolean isNeedMongolia) {
        mSPUtil.putBoolean(SP_KEY_PLAYER_IS_NEED_MONGOLIA, isNeedMongolia);
    }

    public boolean getPlayerIsNeedMongolia() {
        return mSPUtil.getBoolean(SP_KEY_PLAYER_IS_NEED_MONGOLIA, true);
    }

    public void setIsNeedAnimation(boolean isNeedAnimation) {
        mSPUtil.putBoolean(SP_KEY_IS_NEED_ANIMATION, isNeedAnimation);
    }

    public boolean getIsNeedAnimation() {
        return mSPUtil.getBoolean(SP_KEY_IS_NEED_ANIMATION, false);
    }

    public void clearInfo() {
        mSPUtil.remove(SP_KEY_CATEGORIES_IMAGE_SIZE, SP_KEY_HOME_IMAGE_SIZE,
                SP_KEY_HOME_IS_NEED_MONGOLIA, SP_KEY_IS_NEED_ANIMATION, SP_KEY_IS_NEED_BLUR,
                SP_KEY_PLAYER_IS_NEED_MONGOLIA);
    }

    public final static HashMap<Integer,Integer> BigCardImgSizeMap = new HashMap<>();
    static {
        // CPU优化：极致降低图片尺寸以减少内存占用和解码时间
        BigCardImgSizeMap.put(PerformanceSettingMananger.LOW_SIZE,100);  // 150 -> 100
        BigCardImgSizeMap.put(PerformanceSettingMananger.MEDIUM_SIZE,150); // 200 -> 150
        BigCardImgSizeMap.put(PerformanceSettingMananger.HIGH_SIZE,200);   // 300 -> 200
    }

    public final static HashMap<Integer,Integer> catImgSizeMap = new HashMap<>();
    static {
        // CPU优化：极致降低专栏图片尺寸
        catImgSizeMap.put(PerformanceSettingMananger.LOW_SIZE,60);  // 80 -> 60
        catImgSizeMap.put(PerformanceSettingMananger.MEDIUM_SIZE,80); // 100 -> 80
        catImgSizeMap.put(PerformanceSettingMananger.HIGH_SIZE,120);   // 150 -> 120
    }

    public int getImgSize(HashMap<Integer,Integer> map){
        int homeImageSize = getHomeImageSize();

        int imgSize = 10;
        if(map.containsKey(homeImageSize)){
            imgSize = map.get(homeImageSize);
        }
        Log.i("PerformanceSetting"," imgSize = "+imgSize);
        return imgSize;
    }
}
