<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/m220"
    android:layout_gravity="center">


    <!--        <com.kaolafm.kradio.onlineCommon.view.OvalFlipSeekBar-->
    <!--            android:id="@+id/online_main_seekbar"-->
    <!--            android:layout_width="match_parent"-->
    <!--            android:layout_height="@dimen/m170"-->
    <!--            android:layout_alignParentBottom="true"-->
    <!--            android:paddingBottom="@dimen/m50"-->
    <!--            app:of_progress="0"-->
    <!--            app:of_progress_width="@dimen/m4"-->
    <!--            app:of_sweep_angle="240"-->
    <!--            app:of_thumb="@drawable/online_progress_thumb_icon" />-->
    <com.kaolafm.kradio.online.common.view.YunTingProgressBar
        android:id="@+id/online_main_seekbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="@dimen/m22"
        app:ytpbGapAngle="5"
        app:ytpbMaxProgress="100"
        app:ytpbProgress="0"
        app:ytpbStartAngleToLeftHorizontal="40"
        app:ytpbThumbScaleMultipleWhenTouch="1.75"
        app:ytpbThumbSize="@dimen/m70"
        app:ytpbThumbSrc="@drawable/online_progress_thumb_icon" />

    <LinearLayout
        android:id="@+id/rl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <RelativeLayout
            android:id="@+id/player_bar_constrantlayout_rl"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/m50"
            android:layout_marginTop="@dimen/m10">

            <com.kaolafm.kradio.component.ui.base.view.OvalImageView
                android:id="@+id/player_bar_cover"
                android:layout_width="@dimen/m44"
                android:layout_height="@dimen/m44"
                android:layout_centerVertical="true"
                android:layout_marginLeft="@dimen/m20"
                android:scaleType="fitXY"
                app:oval_radius="@dimen/m6"
                app:srcCompat="@drawable/media_default_pic" />

            <!--文字和标题-->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginStart="@dimen/m20"
                android:layout_toRightOf="@id/player_bar_cover"
                android:gravity="center_vertical"
                android:orientation="vertical">

                <com.kaolafm.kradio.online.common.view.OnlineAutoMarqueenTextView
                    android:id="@+id/player_bar_sub_title_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="marquee"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:maxEms="9"
                    android:singleLine="true"
                    android:textColor="@color/online_playerbar_title"
                    android:textSize="@dimen/m24"
                    tools:text="新石门客栈" />
            </LinearLayout>

        </RelativeLayout>

        <com.kaolafm.kradio.component.ui.base.view.KradioTextView
            android:id="@+id/playerBarPositionTv"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/m70"
            android:gravity="center_vertical"
            android:textColor="@color/online_playerbar_position"
            android:textSize="@dimen/m32"
            android:textStyle="bold"
            android:visibility="gone"
            app:kt_font_weight="0.6"
            tools:text="21:14/01:15:30" />
        <!--直播布局按钮-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/player_bar_play_rl"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/m19">

            <ImageView
                android:id="@+id/player_bar_fun"
                android:layout_width="@dimen/m60"
                android:layout_height="@dimen/m60"
                android:layout_gravity="center"
                android:layout_marginStart="@dimen/m60"
                android:layout_marginRight="@dimen/m60"
                android:background="@drawable/color_main_button_click_selector"
                android:padding="@dimen/m16"
                android:scaleType="centerCrop"
                android:src="@drawable/online_playerbar_tv_icon"
                android:visibility="invisible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/player_bar_previous"
                android:layout_width="@dimen/m60"
                android:layout_height="@dimen/m60"
                android:layout_gravity="center"
                android:layout_marginStart="@dimen/m60"
                android:layout_marginRight="@dimen/m60"
                android:background="@drawable/color_main_button_click_selector"
                android:padding="@dimen/m16"
                android:scaleType="centerCrop"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@+id/player_bar_fun"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/online_playerbar_prev" />

            <fr.castorflex.android.circularprogressbar.CircularProgressBar
                android:id="@+id/player_bar_loading"
                style="@style/CustomerCircularProgressBar"
                android:layout_width="@dimen/m72"
                android:layout_height="@dimen/m72"
                android:visibility="invisible"
                app:cpb_color="@color/online_circular_progress_color"
                app:cpb_stroke_width="@dimen/online_loading_progress_width"
                app:layout_constraintBottom_toBottomOf="@id/player_bar_play"
                app:layout_constraintEnd_toEndOf="@id/player_bar_play"
                app:layout_constraintStart_toStartOf="@id/player_bar_play"
                app:layout_constraintTop_toTopOf="@id/player_bar_play" />

            <ImageView
                android:id="@+id/player_bar_play"
                android:layout_width="@dimen/m60"
                android:layout_height="@dimen/m60"
                android:layout_centerVertical="true"
                android:layout_marginLeft="@dimen/m64"
                android:layout_marginRight="@dimen/m64"
                android:background="@drawable/color_main_button_click_selector"
                android:padding="@dimen/m16"
                android:scaleType="centerCrop"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/player_bar_next"
                app:layout_constraintStart_toEndOf="@id/player_bar_previous"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/online_playerbar_play" />

            <ImageView
                android:id="@+id/player_bar_next"
                android:layout_width="@dimen/m60"
                android:layout_height="@dimen/m60"
                android:layout_gravity="center"
                android:layout_marginRight="@dimen/m62"
                android:background="@drawable/color_main_button_click_selector"
                android:padding="@dimen/m16"
                android:scaleType="centerCrop"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/player_bar_collect"
                app:layout_constraintStart_toEndOf="@id/player_bar_play"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/online_playerbar_next" />

            <ImageView
                android:id="@+id/player_bar_collect"
                android:layout_width="@dimen/m60"
                android:layout_height="@dimen/m60"
                android:layout_gravity="center"
                android:layout_marginRight="@dimen/m58"
                android:background="@drawable/color_main_button_click_selector"
                android:padding="@dimen/m16"
                android:scaleType="centerCrop"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/player_bar_next"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/playerbar_collect_album"
                tools:visibility="visible" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>

</merge>