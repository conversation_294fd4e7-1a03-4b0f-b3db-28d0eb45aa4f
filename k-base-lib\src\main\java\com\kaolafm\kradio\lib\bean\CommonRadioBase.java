/*
 * Copyright (C) 2015 AutoRadio
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.kaolafm.kradio.lib.bean;

import com.kaolafm.kradio.lib.utils.Constants;

import java.util.ArrayList;


/******************************************
 * 类描述： 专辑或电台通用对象基类
 *
 * @version: 1.0
 * @author: shaoningYang
 * @time: 2015-5-28 上午11:02:57
 ******************************************/
public class CommonRadioBase {
    private int sortType;
    private int countNum;
    private int hasCopyright;
    private long followedNum;
    private int isOnline;
    private long listenNum;
    private long id;
    private String img;
    private String name;
    private String desc;
    private String ctgName;
    /**
     * 是否订阅或收藏 0为否，1为是
     */
    private int isSubscribe;
    private ArrayList<Host> host;

    private String type;

    /**
     * V4.8.1新加字段 在线人数 喜欢人数
     */
    private int onLineNum;
    private long likedNum;
    /**
     * PGC电台分类
     */
    private int typeId = Constants.INVALID_NUM;

    private String freq;

    public int getCountNum() {
        return countNum;
    }

    public void setCountNum(int countNum) {
        this.countNum = countNum;
    }

    public String getFreq() {
        return freq;
    }

    public void setFreq(String freq) {
        this.freq = freq;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getFollowedNum() {
        return followedNum;
    }

    public void setFollowedNum(long followedNum) {
        this.followedNum = followedNum;
    }

    public int getIsOnline() {
        return isOnline;
    }

    public void setIsOnline(int isOnline) {
        this.isOnline = isOnline;
    }

    public int getHasCopyright() {
        return hasCopyright;
    }

    public void setHasCopyright(int hasCopyright) {
        this.hasCopyright = hasCopyright;
    }

    public long getListenNum() {
        return listenNum;
    }

    public void setListenNum(long listenNum) {
        this.listenNum = listenNum;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setHost(ArrayList<Host> host) {
        this.host = host;
    }

    public ArrayList<Host> getHost() {
        return host;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public int getTypeId() {
        return typeId;
    }

    public void setTypeId(int typeId) {
        this.typeId = typeId;
    }

    public int getOnLineNum() {
        return onLineNum;
    }

    public void setOnLineNum(int onLineNum) {
        this.onLineNum = onLineNum;
    }

    public long getLikedNum() {
        return likedNum;
    }

    public void setLikedNum(long likedNum) {
        this.likedNum = likedNum;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getCtgName() {
        return ctgName;
    }

    public void setCtgName(String ctgName) {
        this.ctgName = ctgName;
    }

    public int getIsSubscribe() {
        return isSubscribe;
    }

    public void setIsSubscribe(int isSubscribe) {
        this.isSubscribe = isSubscribe;
    }

    public boolean isSubscribe() {
        boolean flag = isSubscribe == 1;
        return flag;
    }

    public int getSortType() {
        return sortType;
    }

    public void setSortType(int sortType) {
        if (sortType == Constants.ORDER_MODE_REVERSE) {
            this.sortType = sortType;
        } else {
            this.sortType = Constants.ORDER_MODE_POSITIVE;
        }
    }
}
