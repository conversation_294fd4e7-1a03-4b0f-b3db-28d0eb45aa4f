package com.kaolafm.kradio.media.session;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.media.ThumbnailUtils;
import android.os.Handler;
import android.os.Looper;
import android.os.SystemClock;
import android.support.v4.media.MediaMetadataCompat;
import android.support.v4.media.RatingCompat;
import android.support.v4.media.session.MediaSessionCompat;
import android.support.v4.media.session.PlaybackStateCompat;
import android.util.Log;
import android.view.KeyEvent;

import com.kaolafm.kradio.common.CP;
import com.kaolafm.kradio.common.ErrorInfo;
import com.kaolafm.kradio.common.ResultCallback;
import com.kaolafm.kradio.lib.bean.SubscribeData;
import com.kaolafm.kradio.k_kaolafm.R;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.media.MediaButtonManagerUtil;
import com.kaolafm.kradio.subscribe.SubscribeChangeListener;
import com.kaolafm.kradio.subscribe.SubscribeManager;
import com.kaolafm.kradio.subscribe.SubscribeRepository;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import java.lang.ref.WeakReference;
import java.util.List;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-04-09 17:47
 ******************************************/
public final class KRMediaSessionUpLP implements KRMediaSessionInter, SubscribeChangeListener {
    private static final String TAG = "KRMediaSessionUpLP";
    private MediaSessionCompat mMediaSession;
    private MediaSessionIPlayerStateListener mMediaSessionIPlayerStateListener;
    private static SubscribeData mSubscribeData;

    public KRMediaSessionUpLP() {
        Log.i(TAG, "初始化");
        Context context = AppDelegate.getInstance().getContext();
        mMediaSession = new MediaSessionCompat(context, context.getPackageName());
    }

    @Override
    public void registerMediaSession(Context context) {
        Log.i(TAG, "registerMediaSession");

        // 解决36322问题
        if (mMediaSession == null) {
            mMediaSession = new MediaSessionCompat(context, context.getPackageName());
        }
        ((SubscribeRepository) SubscribeManager.getInstance(AppDelegate.getInstance().getContext(), CP.KaoLaFM)).addSubscribeChangeListener(this);
        MediaSessionCallback mediaSessionCallback = new MediaSessionCallback(this);
        mMediaSession.setCallback(mediaSessionCallback);
        mMediaSession.setFlags(MediaSessionCompat.FLAG_HANDLES_MEDIA_BUTTONS | MediaSessionCompat.FLAG_HANDLES_TRANSPORT_CONTROLS);
        mMediaSession.setActive(true);
        boolean isMetadataAvailable = context.getResources().getBoolean(R.bool.isMetadataAvailable);
        Log.i(TAG, "registerMediaSession-------->isMetadataAvailable = " + isMetadataAvailable);
        if (isMetadataAvailable) {
            mMediaSessionIPlayerStateListener = new MediaSessionIPlayerStateListener(this);
            PlayerManager.getInstance().addPlayControlStateCallback(mMediaSessionIPlayerStateListener);
        }
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001295650582?userId=2188646问题
        updateMetaData();
    }

    @Override
    public void unregisterMediaSession(Context context) {
        Log.i(TAG, "unregisterMediaSession-------->mMediaSession = " + mMediaSession);
        if (mMediaSession != null) {
            mMediaSession.setActive(false);
            mMediaSession.release();
            mMediaSession = null;
            ((SubscribeRepository) SubscribeManager.getInstance(AppDelegate.getInstance().getContext(), CP.KaoLaFM)).removeSubscribeChangeListener(this);
            PlayerManager.getInstance().removePlayControlStateCallback(mMediaSessionIPlayerStateListener);
            mMediaSessionIPlayerStateListener = null;
        }
    }

    @Override
    public MediaSessionCompat getMediaSession() {
        return mMediaSession;
    }

    @Override
    public void onSubscribesChanged(List<SubscribeData> subscribes) {
        boolean subscribed = false;
        long sId = PlayerManagerHelper.getInstance().getSubscribeId();
        if (sId == -1) {
            Log.i(TAG, "    onSubscribesChanged:" + sId);
            updateMetaData(false);
            return;
        }

        if (subscribes != null && !subscribes.isEmpty()) {
            for (SubscribeData s : subscribes) {
                if (s.getId() == sId) {
                    subscribed = true;
                    break;
                }
            }
        }
        Log.i(TAG, "    onSubscribesChanged:" + sId + ":订阅状态改变:" + subscribed);
        updateMetaData(subscribed);
    }

    private static class MediaSessionCallback extends MediaSessionCompat.Callback {
        private MediaButtonManagerUtil mMediaButtonManagerUtil = new MediaButtonManagerUtil();
        private WeakReference<KRMediaSessionUpLP> mWeakReference;

        public MediaSessionCallback(KRMediaSessionUpLP krMediaSessionUpLP) {
            mWeakReference = new WeakReference<>(krMediaSessionUpLP);
        }

        @Override
        public boolean onMediaButtonEvent(final Intent mediaButtonIntent) {
            Log.i(TAG, "onMediaButtonEvent: " + mediaButtonIntent.getAction());

            if (!Intent.ACTION_MEDIA_BUTTON.equals(mediaButtonIntent.getAction())) {
                return super.onMediaButtonEvent(mediaButtonIntent);
            }
            KeyEvent key = mediaButtonIntent.getParcelableExtra(Intent.EXTRA_KEY_EVENT);
            // 解决https://app.huoban.com/tables/2100000007530121/items/2300001928514035?userId=1229522问题
            if (key == null || key.getRepeatCount() > 0) {
                return super.onMediaButtonEvent(mediaButtonIntent);
            }
            int keyAction = key.getAction();
            if (keyAction == KeyEvent.ACTION_UP) {
                return super.onMediaButtonEvent(mediaButtonIntent);
            }
            int keyCode = key.getKeyCode();
            mMediaButtonManagerUtil.manageMediaButtonClick(keyCode);
            return true;
        }

        @Override
        public void onPause() {
            Log.i(TAG, "onPause: ");

            super.onPause();
            PlayerManagerHelper.getInstance().pause(true);
        }

        @Override
        public void onPlay() {
            Log.i(TAG, "onPlay: ");

            super.onPlay();
            PlayerManagerHelper.getInstance().play(true);
        }

        @Override
        public void onSkipToNext() {
            Log.i(TAG, "onSkipToNext: ");

            super.onSkipToNext();
            PlayerManagerHelper.getInstance().playNext(true);
        }

        @Override
        public void onSkipToPrevious() {
            Log.i(TAG, "onSkipToPrevious: ");

            super.onSkipToPrevious();
            PlayerManagerHelper.getInstance().playPre(true);
        }

        @Override
        public void onSetRating(RatingCompat rating) {
            Log.i(TAG, "onSetRating: ");

            super.onSetRating(rating);
            KRMediaSessionUpLP krMediaSessionUpLP = mWeakReference.get();
            Log.i(TAG, "onSetRating: krMediaSessionUpLP = " + krMediaSessionUpLP);
            if (krMediaSessionUpLP != null) {
                boolean isFavorite = rating.hasHeart();
                Log.i(TAG, "onSetRating: isFavorite = " + isFavorite);
                if (mSubscribeData == null) {
                    mSubscribeData = new SubscribeData();
                }
                mSubscribeData.setId(PlayerManagerHelper.getInstance().getSubscribeId());
                if (isFavorite) {
                    SubscribeManager.getInstance(AppDelegate.getInstance().getContext(), CP.KRadio).subscribe(mSubscribeData, null);
                } else {
                    SubscribeManager.getInstance(AppDelegate.getInstance().getContext(), CP.KRadio).unsubscribe(mSubscribeData, null);
                }
            }
        }
    }

    private void updateMetaData() {
        long sId = PlayerManagerHelper.getInstance().getSubscribeId();
        if (sId == -1) {
            updateMetaData(false);
            return;
        }
        SubscribeManager.getInstance(AppDelegate.getInstance().getContext(), CP.KRadio).isSubscribed(String.valueOf(sId), new ResultCallback() {
            @Override
            public void onResult(boolean result, int code) {
                Log.i(TAG, "onPlayerPreparing check isSubscribed onResult: result = " + result);
                updateMetaData(result);
            }

            @Override
            public void onFailure(ErrorInfo errorInfo) {
                Log.i(TAG, "onPlayerPreparing check isSubscribed onFailure ");
                updateMetaData(false);
            }
        });
    }

    private static class MediaSessionIPlayerStateListener extends BasePlayStateListener {
        private WeakReference<KRMediaSessionUpLP> mWeakReference;

        public MediaSessionIPlayerStateListener(KRMediaSessionUpLP krMediaSessionUpLP) {
            mWeakReference = new WeakReference<>(krMediaSessionUpLP);
        }

        @Override
        public void onIdle(PlayItem playItem) {
            KRMediaSessionUpLP krMediaSessionUpLP = mWeakReference.get();
            if (krMediaSessionUpLP != null) {
                krMediaSessionUpLP.updatePlaybackState(PlaybackStateCompat.STATE_STOPPED);
            }
        }

        @Override
        public void onPlayerPreparing(PlayItem playItem) {
            KRMediaSessionUpLP krMediaSessionUpLP = mWeakReference.get();
            if (krMediaSessionUpLP != null) {
                krMediaSessionUpLP.updateMetaData();
            }
        }

        @Override
        public void onPlayerPlaying(PlayItem playItem) {
            KRMediaSessionUpLP krMediaSessionUpLP = mWeakReference.get();
            if (krMediaSessionUpLP != null) {
                krMediaSessionUpLP.updatePlaybackState(PlaybackStateCompat.STATE_PLAYING);
            }
        }

        @Override
        public void onPlayerPaused(PlayItem playItem) {
            KRMediaSessionUpLP krMediaSessionUpLP = mWeakReference.get();
            if (krMediaSessionUpLP != null) {
                krMediaSessionUpLP.updatePlaybackState(PlaybackStateCompat.STATE_PAUSED);
            }
        }

        @Override
        public void onProgress(PlayItem playItem, long i, long i1) {
//            KRMediaSessionUpLP krMediaSessionUpLP = mWeakReference.get();
//            if (krMediaSessionUpLP != null) {
//                krMediaSessionUpLP.updatePlaybackState(PlaybackStateCompat.STATE_PLAYING);
//            }
        }

        @Override
        public void onPlayerFailed(PlayItem playItem, int i, int i1) {
            KRMediaSessionUpLP krMediaSessionUpLP = mWeakReference.get();
            if (krMediaSessionUpLP != null) {
                krMediaSessionUpLP.updatePlaybackState(PlaybackStateCompat.STATE_ERROR);
            }
        }

        @Override
        public void onSeekComplete(PlayItem playItem) {
            KRMediaSessionUpLP krMediaSessionUpLP = mWeakReference.get();
            if (krMediaSessionUpLP != null) {
                krMediaSessionUpLP.updatePlaybackState(PlaybackStateCompat.STATE_PLAYING);
            }
        }
    }

    private void updateMetaData(boolean isFavorite) {
        if (mMediaSession == null) {
            return;
        }

        PlayItem playItem = PlayerManagerHelper.getInstance().getCurPlayItem();
        if (playItem == null) {
            mMediaSession.setMetadata(null);
        } else {
            String radioName = playItem.getAlbumTitle();
            String audioPic = PlayerManagerHelper.getInstance().getPlayItemPicUrl(playItem);
            MediaMetadataCompat.Builder metaData = new MediaMetadataCompat.Builder()
                    .putString(MediaMetadataCompat.METADATA_KEY_ARTIST, playItem.getHost())
                    .putString(MediaMetadataCompat.METADATA_KEY_ALBUM, radioName)
                    .putString(MediaMetadataCompat.METADATA_KEY_TITLE, playItem.getTitle())
                    .putLong(MediaMetadataCompat.METADATA_KEY_DURATION, playItem.getDuration())
                    .putRating(MediaMetadataCompat.METADATA_KEY_USER_RATING, RatingCompat.newHeartRating(isFavorite));
            new Handler(Looper.getMainLooper()).post(new Runnable() {
                @Override
                public void run() {
                    ImageLoader.getInstance().getBitmapFromCache(AppDelegate.getInstance().getContext(), audioPic, bitmap -> {
                        Bitmap thumbnailBitmap = ThumbnailUtils.extractThumbnail(bitmap, 100, 100);
                        metaData.putBitmap(MediaMetadataCompat.METADATA_KEY_DISPLAY_ICON, thumbnailBitmap);
                        //异步导致出现了mMediaSession为null的情况
                        if (mMediaSession != null) {
                            mMediaSession.setMetadata(metaData.build());
                        }
                    });
                }
            });
        }
    }

    public void updatePlaybackState(@PlaybackStateCompat.State int playbackState) {
        if (mMediaSession == null) {
            return;
        }
        PlayItem playItem = PlayerManagerHelper.getInstance().getCurPlayItem();
        PlaybackStateCompat state = new PlaybackStateCompat.Builder()
                .setActions(PlaybackStateCompat.ACTION_PAUSE |
                        PlaybackStateCompat.ACTION_PLAY |
                        PlaybackStateCompat.ACTION_SKIP_TO_PREVIOUS |
                        PlaybackStateCompat.ACTION_SKIP_TO_NEXT |
                        PlaybackStateCompat.ACTION_PLAY_PAUSE | PlaybackStateCompat.ACTION_SET_RATING)
                .setState(playbackState, playItem == null ? 0 : playItem.getPosition(), 0, SystemClock.elapsedRealtime())
                .build();
        mMediaSession.setPlaybackState(state);
    }
}