package com.kaolafm.kradio.flavor.service;

import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.IBinder;
import androidx.annotation.Nullable;


import com.kaolafm.kradio.flavor.utils.PlayerUtil;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.NetworkMonitor;
import com.kaolafm.kradio.lib.utils.NetworkUtil;


import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.utils.MediaSessionUtil;

import java.lang.ref.WeakReference;

import static com.kaolafm.kradio.flavor.utils.FlavorConstants.SERVICE_START_COMMAND;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-06-20 09:49
 ******************************************/
public final class AutoPlayService extends Service {
    @Override
    public void onCreate() {
        super.onCreate();
        Context context = getApplicationContext();
        if (NetworkUtil.isNetworkAvailable(context)) {
            playDefaultPgc();
        } else {
            NetworkMonitor.getInstance(context).registerNetworkStatusChangeListener(new MyOnNetworkStatusChangedListener(this));
        }
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        return SERVICE_START_COMMAND;
    }

    private static class MyOnNetworkStatusChangedListener implements NetworkMonitor.OnNetworkStatusChangedListener {

        private WeakReference<AutoPlayService> weakReference;

        public MyOnNetworkStatusChangedListener(AutoPlayService autoPlayService) {
            weakReference = new WeakReference<>(autoPlayService);
        }

        @Override
        public void onStatusChanged(int i, int i1) {
            AutoPlayService autoPlayService = weakReference.get();
            if (autoPlayService == null) {
                return;
            }
            NetworkMonitor.getInstance(autoPlayService.getApplicationContext()).removeNetworkStatusChangeListener(this);
            if (i == NetworkMonitor.STATUS_MOBILE || i == NetworkMonitor.STATUS_WIFI) {
                autoPlayService.playDefaultPgc();
            }
        }
    }

    private void playDefaultPgc() {
        final PlayerManager playerManager = PlayerManager.getInstance();
        playerManager.addPlayerInitComplete(new MyOnPlayerInitCompleteListener());
    }


    private static class MyOnPlayerInitCompleteListener implements IPlayerInitCompleteListener {
        @Override
        public void onPlayerInitComplete(boolean b) {
            MediaSessionUtil.getInstance().registerMediaSession();
            final PlayerManager playerManager = PlayerManager.getInstance(null);
            playerManager.removePlayerInitComplete(this);
            PlayerUtil.playDefaultMediaForChannel();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        MediaSessionUtil.getInstance().unregisterMediaSession();
    }
}
