<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:id="@+id/root">

    <LinearLayout
        android:id="@+id/ll_history_login"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center"
        >
        <com.kaolafm.kradio.component.ui.base.view.KradioTextView
            android:id="@+id/tv_history_login_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:gravity="center"
            app:kt_font_weight="0.7"
            android:textColor="@color/online_history_login_notice_text"
            android:textSize="@dimen/history_login_tip"
            android:text="@string/online_sync_history_after_login"
            />

        <com.kaolafm.kradio.component.ui.base.view.KradioTextView
            android:id="@+id/tv_history_login"
            android:layout_marginTop="@dimen/y47"
            android:layout_marginBottom="@dimen/y10"
            android:layout_width="@dimen/online_history_empty_btn_width"
            android:layout_height="@dimen/online_history_empty_btn_height"
            android:background="@drawable/online_history_empty_login_btn_bg"
            android:layout_gravity="center_horizontal"
            android:gravity="center"
            app:kt_font_weight="0.7"
            android:textColor="@color/online_history_login_btn_text"
            android:textSize="@dimen/history_login_tip"
            android:text="@string/online_no_login_buttom_str"
            />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
