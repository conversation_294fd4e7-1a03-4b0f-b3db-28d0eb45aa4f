package com.kaolafm.kradio.lib.base.flavor;

import android.view.View;

import io.reactivex.annotations.Nullable;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: lidan
 * @time: 2021-05-27 15:06
 ******************************************/
public interface KRadioHomeExitInter {
    /**
     * 首頁按鈕是否展示
     *
     * @param show show = true/false
     * @return
     */
    boolean isShowHomeExitBtn(View view, boolean show, @Nullable Object... args);

}
