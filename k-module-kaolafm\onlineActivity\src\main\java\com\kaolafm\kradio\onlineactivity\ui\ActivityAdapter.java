package com.kaolafm.kradio.onlineactivity.ui;

import android.view.LayoutInflater;
import android.view.ViewGroup;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.opensdk.api.activity.model.Activity;

public class ActivityAdapter extends BaseAdapter<Activity> {
    public ActivityAdapter() {
        super();
    }

    @Override
    protected BaseHolder getViewHolder(ViewGroup parent, int viewType) {
        BaseHolder baseHolder;
        switch (viewType) {
            case 0://无按钮的类型
                baseHolder = new ActivityViewHolder(LayoutInflater.from(parent.getContext()).inflate(
                        R.layout.item_activity, parent, false));
                break;
            case 1://有按钮的活动类型
                baseHolder = new ActivityNewViewHolder(LayoutInflater.from(parent.getContext()).inflate(
                        R.layout.item_activity_new, parent, false));
                break;
            default:
                baseHolder = new ActivityViewHolder(LayoutInflater.from(parent.getContext()).inflate(
                        R.layout.item_activity, parent, false));
                break;
        }
        return baseHolder;
    }

    @Override
    public int getItemViewType(int position) {
        return mDataList.get(position).getActivityType();
    }
}