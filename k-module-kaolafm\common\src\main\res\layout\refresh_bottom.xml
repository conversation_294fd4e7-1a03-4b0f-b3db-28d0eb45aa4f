<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/refresh_bottom_linearlayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="horizontal">

    <fr.castorflex.android.circularprogressbar.CircularProgressBar xmlns:app="http://schemas.android.com/apk/res-auto"
        android:id="@+id/refresh_bottom_progressBar"
        style="@style/CustomerCircularProgressBar"
        android:layout_width="@dimen/m36"
        android:layout_height="@dimen/m36"
        app:cpb_color="@color/player_order_multi_loading_color"
        app:cpb_stroke_width="@dimen/loading_progress_width" />

    <!--在搜索界面 使用 ConstraintLayout 必须在布局的每一个子view 添加id-->
    <com.kaolafm.kradio.common.widget.CTextView
        android:id="@+id/kradio_widget_ctextview"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingLeft="@dimen/x30"
        android:text="@string/loading_more_str"
        android:textColor="@color/player_order_multi_loading_color"
        android:textSize="@dimen/text_size3" />
</LinearLayout>