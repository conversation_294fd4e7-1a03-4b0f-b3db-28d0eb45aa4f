//package com.kaolafm.kradio.flavor.service;
//
//import android.app.Service;
//import android.content.Context;
//import android.content.Intent;
//import android.os.IBinder;
//import android.support.annotation.Nullable;
//import android.util.Log;
//
//import com.kaolafm.kradio.lib.base.AppDelegate;
//import com.kaolafm.kradio.lib.utils.NetworkMonitor;
//import com.kaolafm.kradio.lib.utils.NetworkUtil;
//
//
//import com.kaolafm.utils.PlayerUtil;
//
//import java.lang.ref.WeakReference;
//
//import static com.kaolafm.kradio.flavor.utils.FlavorConstants.SERVICE_START_COMMAND;
//
///******************************************
// * 类描述:
// *
// * @version: V1.0
// * @author: yangshaoning
// * @time: 2018-06-20 09:49
// ******************************************/
//public final class AutoPlayService extends Service {
//    private static final String TAG = "AutoPlayService";
//    @Override
//    public void onCreate() {
//        super.onCreate();
//        Context context = getApplicationContext();
//        Log.i(TAG, "onCreate start");
//        if (NetworkUtil.isNetworkAvailable(context)) {
//            playDefaultPgc();
//        } else {
//            NetworkMonitor.getInstance(context).registerNetworkStatusChangeListener(new MyOnNetworkStatusChangedListener(this));
//        }
//    }
//
//    @Override
//    public int onStartCommand(Intent intent, int flags, int startId) {
//        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001189368354?userId=1545533问题
//        return SERVICE_START_COMMAND;
//    }
//
//    @Nullable
//    @Override
//    public IBinder onBind(Intent intent) {
//        return null;
//    }
//
//    private static class MyOnNetworkStatusChangedListener implements NetworkMonitor.OnNetworkStatusChangedListener {
//
//        private WeakReference<AutoPlayService> weakReference;
//
//        public MyOnNetworkStatusChangedListener(AutoPlayService autoPlayService) {
//            weakReference = new WeakReference<>(autoPlayService);
//        }
//
//        @Override
//        public void onStatusChanged(int i, int i1) {
//            AutoPlayService autoPlayService = weakReference.get();
//            if (autoPlayService == null) {
//                return;
//            }
//            NetworkMonitor.getInstance(autoPlayService.getApplicationContext()).removeNetworkStatusChangeListener(this);
//            if (i == NetworkMonitor.STATUS_MOBILE || i == NetworkMonitor.STATUS_WIFI) {
//                autoPlayService.playDefaultPgc();
//            }
//        }
//    }
//
//    private void playDefaultPgc() {
//        final PlayerManager playerManager = PlayerManager.getInstance(AppDelegate.getInstance().getContext());
//        playerManager.setupPlayer();
//        playerManager.addPlayerInitCompleteListener(new MyOnPlayerInitCompleteListener());
//    }
//
//
//    private static class MyOnPlayerInitCompleteListener implements VLCMediaPlayClient.OnPlayerInitCompleteListener {
//        @Override
//        public void onPlayerInitComplete(boolean b) {
//            final PlayerManager playerManager = PlayerManager.getInstance(null);
//            playerManager.removePlayerInitCompleteListener(this);
////            KLAutoPlayerManager.getInstance().playDefaultMediaForChannel();
//            PlayerUtil.playNetOrLocal();
//        }
//    }
//}
