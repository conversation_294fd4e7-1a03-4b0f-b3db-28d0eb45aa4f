package com.kaolafm.kradio.flavor.impl;

import com.kaolafm.kradio.flavor.utils.PlayerUtil;
import com.kaolafm.kradio.lib.base.flavor.KRadioHistoryInter;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-10-16 18:31
 ******************************************/
public class KRadioHistoryImpl implements KRadioHistoryInter {
    @Override
    public boolean doNoHistoryData(Object... args) {
        PlayerUtil.playDefaultMediaForChannel();
        return true;
    }
}
