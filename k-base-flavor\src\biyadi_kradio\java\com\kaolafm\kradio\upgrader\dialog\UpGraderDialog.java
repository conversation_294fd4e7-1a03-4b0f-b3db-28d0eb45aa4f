package com.kaolafm.kradio.upgrader.dialog;

import android.content.Context;
import android.util.Log;
import android.view.View;
import android.widget.TextView;

import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.dialog.BaseCenterDelayDialog;
import com.kaolafm.kradio.upgrader.net.model.UpdateData;
import com.kaolafm.kradio.lib.toast.ToastUtil;

public class UpGraderDialog extends BaseCenterDelayDialog {
    public static final int UpGraderDialogAlert = 0;
    public static final int UpGraderDialogProgress = 1;
    private int mCode;//code值返回布局的类型 0为提示是否升级  1为显示进度

    private TextView update_sure;
    private TextView update_cancel;

    private TextView tv_progress;
    private TextView tv_update_info;

    private int mProgress = 0;

    public UpGraderDialog(Context context) {
        super(context, false, 1F, 1F, true);
    }

    public void setmCode(int mCode) {
        this.mCode = mCode;
    }

    public void setInfo(UpdateData info) {
        tv_update_info.setText("最新版本：" + info.getUpdateVersion() + "\n版本号：" + info.getVersionCode());
    }

    @Override
    protected void returnContentView(View mContentView) {
        if (mCode == UpGraderDialogAlert) {
            update_sure = mContentView.findViewById(R.id.update_sure);
            update_cancel = mContentView.findViewById(R.id.update_cancel);
            update_sure.setOnClickListener(v -> mPositiveListener.onClick(null));
            update_cancel.setOnClickListener(v -> mNativeListener.onClick(null));
        } else {
            tv_progress = mContentView.findViewById(R.id.tv_progress);
            tv_update_info = mContentView.findViewById(R.id.tv_update_info);
        }
    }

    @Override
    protected int delayTimeDimiss() {
        if (mCode == UpGraderDialogAlert) {
            return 30000;
        } else {
            return 0;
        }
    }

    @Override
    protected int getLayoutId() {
        return getLayout();
    }


    //获取相应布局
    private int getLayout() {
        Log.i("zsj", "getLayout: mCode = " + mCode);
        switch (mCode) {
            case 0:
                return R.layout.update_select;
            case 1:
                return R.layout.update_progress;
            default:
                return 0;
        }
    }

    public void upProgress(int progress) {
        if (tv_progress != null) {
            mProgress = progress;
            tv_progress.setText(String.valueOf(progress) + "%");
        }
    }

    @Override
    public void delayDismissAction(Long aLong) {
        //如果当前正在下载，切换至后台给与提示
        Log.i("UpGraderDialog", "delayDismissAction: mCode = " + mCode + ", isShowing = " + isShowing() + ", mProgress = " + mProgress);
        if (mCode == UpGraderDialogProgress && isShowing() && mProgress < 95) {
            ToastUtil.showOnly(AppDelegate.getInstance().getContext(), AppDelegate.getInstance().getContext().getResources().getString(R.string.update_onbackground));
        }
    }
}
