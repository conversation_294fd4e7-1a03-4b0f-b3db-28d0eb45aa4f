package com.kaolafm.ads.image;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import android.text.Html;
import android.text.TextUtils;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.ad.api.model.ImageAdvert;
import com.kaolafm.ad.control.KradioAdSceneConstants;
import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.lib.utils.imageloader.listener.OnImageLoaderListener;

import io.reactivex.Observable;
import io.reactivex.Observer;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Function;
import io.reactivex.schedulers.Schedulers;

import java.io.File;
import java.util.concurrent.TimeUnit;

public class SplashAdActivity extends Activity {
    private ConstraintLayout mView;
    private ImageView mIvAd;
    private TextView mTvSkip;

    private ImageAdvert mAdvert;
    private int mSkipTime;
    protected long mDuration;

    protected boolean paused = false;

    protected Disposable mCloseDisposable;
    private Disposable mSkipDisposable;

    public static final String CLOSE_SPLASH_AD = "com.edog.car.close.spash";
    private LocalBroadcastManager mLocalBroadcastManager;
    private BroadcastReceiver mBroadcastReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            cancelClose();
            AdvertisingManager.getInstance().getReporter().skip(mAdvert);
            goNextPage(true);
        }
    };


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.i(Constants.START_TAG, "SplashAdActivity oncreate...");

        initView();
        loadAdContent();
        mLocalBroadcastManager = LocalBroadcastManager.getInstance(this);
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(CLOSE_SPLASH_AD);
        mLocalBroadcastManager.registerReceiver(mBroadcastReceiver, intentFilter);
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (paused) {
            goNextPage(false);
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        paused = true;
    }

    private void initView() {
        setContentView(R.layout.ad_image_view_layout_splash);
        mView = findViewById(R.id.cl_ad_view);
        mIvAd = findViewById(R.id.iv_ad);
        mTvSkip = findViewById(R.id.tv_ad_skip);
        mView.setVisibility(View.INVISIBLE);

        mView.setBackgroundColor(getResources().getColor(R.color.colorBlack));
    }


    public void loadAdContent() {
        try {
            mAdvert = getIntent().getParcelableExtra("advert");
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (mAdvert == null) {
            return;
        }

        mSkipTime = mAdvert.getJumpSeconds();
        mDuration = mAdvert.getExposeDuration();
        String path = mAdvert.getLocalPath();
        File file = null;
        try {
            file = new File(path);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (file == null || !file.exists()) {
            goNextPage(true);
            return;
        }

        ImageLoader.getInstance().displayLocalImage(this, path, mIvAd, new SplashAdImageLoaderListener());
        if (mAdvert.getType() != KradioAdSceneConstants.AD_TYPE_AUDIO_IMAGE && mDuration > 0) {
            AdvertisingManager.getInstance().getReporter().display(mAdvert);
        }
//        int ori = ResUtil.getOrientation(); //获取屏幕方向
//        if (ori == Configuration.ORIENTATION_LANDSCAPE) {
//            //横屏
//            ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) mIvAd.getLayoutParams();
//            layoutParams.width = (ScreenUtil.getScreenHeight() / 9 * 16);
////        layoutParams.width = 720;
//            mIvAd.setLayoutParams(layoutParams);
//        } else if (ori == Configuration.ORIENTATION_PORTRAIT) {
//            //竖屏
//        }

    }

    private void showAd() {
        boolean isJump = mAdvert.isJump();
        if (isJump) {
            setSkipTimeView();
        } else {
            mTvSkip.setVisibility(View.GONE);
        }
        mView.setVisibility(View.VISIBLE);
        countdownToCloseAd();
    }

    public void countdownToCloseAd() {
        Observable.timer(mDuration, TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<Long>() {
                    @Override
                    public void onSubscribe(Disposable d) {
                        mCloseDisposable = d;
                    }

                    @Override
                    public void onNext(Long aLong) {
                        if (!paused) {
                            goNextPage(false);
                        }
                    }

                    @Override
                    public void onError(Throwable e) {
                        cancelClose();
                    }

                    @Override
                    public void onComplete() {
                        if (mAdvert.getType() != KradioAdSceneConstants.AD_TYPE_AUDIO_IMAGE && TextUtils.isEmpty(mAdvert.getAttachImage().getUrl())) {
                            if (mDuration > 0) {
                                AdvertisingManager.getInstance().getReporter().endDisplay(mAdvert);
                            }
                        }
                        cancelClose();
                    }
                });
    }

    public void setSkipTimeView() {
        if (mSkipTime <= 0) {
            mTvSkip.setText("跳过广告");
            mTvSkip.setOnClickListener((v) -> {
                skipAd();
            });
            return;
        }
        Observable.interval(0, 1, TimeUnit.SECONDS)
                .take(mSkipTime + 1)
                .onTerminateDetach()
                .map(new Function<Long, Long>() {
                    @Override
                    public Long apply(Long aLong) throws Exception {
                        return mSkipTime - aLong;
                    }
                })
                .observeOn(AndroidSchedulers.mainThread())
                .onTerminateDetach()
                .subscribe(new Observer<Long>() {
                    @Override
                    public void onSubscribe(Disposable d) {
                        mSkipDisposable = d;
                    }

                    @Override
                    public void onNext(Long aLong) {
//                        mTvSkip.setText(aLong + "s 后可跳过广告");
                        mTvSkip.setText(Html.fromHtml("<html><font color=\"#D7A448\">" + aLong + "s</font> " +
                                "<font color=\"#ffffff\"> 后可跳过广告</font></html>"));
                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onComplete() {
                        mTvSkip.setText("跳过广告");
                        mTvSkip.setOnClickListener((v) -> {
                            skipAd();
                        });
                        cancelSkip();
                    }
                });
    }


    private void skipAd() {
        cancelClose();
        AdvertisingManager.getInstance().close();
    }

    private void goNextPage(boolean isSkip) {
        goNextPage(isSkip, false);

    }

    private void goNextPage(boolean isSkip, boolean isImageSkip) {
        Intent intent = new Intent(this, SplashAdHelper.getTargetActivity());
        intent.putExtra("isSkip", isSkip);
        intent.putExtra("isImageSkip", isImageSkip);
        this.startActivity(intent);
        finish();
    }

    private void cancelClose() {
        if (mCloseDisposable != null && !mCloseDisposable.isDisposed()) {
            mCloseDisposable.dispose();
            mCloseDisposable = null;
        }
    }

    private void cancelSkip() {
        if (mSkipDisposable != null && !mSkipDisposable.isDisposed()) {
            mSkipDisposable.dispose();
            mSkipDisposable = null;
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (TextUtils.isEmpty(mAdvert.getAttachImage().getUrl())) {
                AdvertisingManager.getInstance().close(mAdvert);
            }
            this.finish();
            AppManager.getInstance().appExit();
            ((AdvertisingImagerImpl) AdvertisingManager.getInstance().getImager()).destroyAdView();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        goNextPage(false, true);
        if (mAdvert != null && mDuration > 0) {
            if (mAdvert.getType() != KradioAdSceneConstants.AD_TYPE_AUDIO_IMAGE) {
                AdvertisingManager.getInstance().getReporter().skip(mAdvert);
            }
        }
    }

    @Override
    protected void onDestroy() {
        mLocalBroadcastManager.unregisterReceiver(mBroadcastReceiver);
        super.onDestroy();
        cancelSkip();
        cancelClose();
    }


    public class SplashAdImageLoaderListener implements OnImageLoaderListener {

        @Override
        public void onLoadingFailed(String url, ImageView target, Exception exception) {
        }

        @Override
        public void onLoadingComplete(String url, ImageView target) {
            showAd();
        }
    }
}
