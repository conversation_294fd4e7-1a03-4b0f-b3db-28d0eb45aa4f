package com.kaolafm.kradio.common;

import org.greenrobot.greendao.annotation.NotNull;

/**
 * 子分类item的bean类
 *
 * <AUTHOR>
 * @date 2018/4/25
 */

public class PubcategoryItemBean {

    /**
     * 纯文字标题
     */
    public static final int TYPE_ITEM_TITLE = 101;

    /**
     * 歌单,专辑
     */
    public static final int TYPE_ITEM_ALBUM = 102;

    /**
     * 排行榜
     */
    public static final int TYPE_ITEM_CHARTS = 103;

    /**
     * 排行榜官方榜
     */
    public static final int TYPE_ITEM_OFFICIAL_CHARTS = 104;

    /**
     * 订阅
     */
    public static final int TYPE_ITEM_SUBSCRIPTION = 105;

    /**
     * 账号的item
     */
//    public static final int TYPE_ITEM_ACCOUNT = 106;

    /**
     * 收听历史、我喜欢等item
     */
//    public static final int TYPE_ITEM_LISTEN = 107;

    /**
     * 带按钮的标题，如订阅标题
     */
//    public static final int TYPE_ITEM_TITLE_BUTTON = 108;

    /**
     * 频道、电台、分类、场景
     */
    public static final int TYPE_ITEM_RADIO_CHANNEL = 109;

    /**
     * 无订阅内容
     */
    public static final int TYPE_ITEM_NO_SUBSCRIPTION = 110;

    /**
     * 本地广播
     */
    public static final int TYPE_ITEM_BROADCAST_LOCAL = 111;

    /**
     * 分类广播
     */
    public static final int TYPE_ITEM_BROADCAST_CATEGORY = 112;

    /**
     * 只有一个按钮
     */
    public static final int TYPE_ITEM_BUTTON = 113;

    /**
     * item类型，根据不同类型，显示不同布局
     */
    @NotNull
    private int itemType;

    /**
     * 节目名、按钮名
     */
    private String name;
    /**
     * 封面url与{@link #resId}互斥
     */
    private String coverUrl;
    /**
     * 标题
     */
    private String title;
    /**
     * 收听数
     */
    private long listenNum;
    /**
     * 资源图片id，与{@link #coverUrl}互斥
     */
    private int resId;
    /**
     * id
     */
    private long id;
    /**
     * 登录方式
     */
    private int loginType;
    /**
     * 描述
     */
    private String des;
    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 是否上线
     */
    private boolean isOnline = true;

    /**
     * 是否被选中正在播放，true正在播放
     */
    private boolean isSelected;

    /**
     * 上一级的code
     */
    private String parentCode;

    public int getLoginType() {
        return loginType;
    }

    public void setLoginType(int loginType) {
        this.loginType = loginType;
    }

    /**
     * 资源类型 {@link com.kaolafm.kradio.lib.common.ResType}
     */
    private int resType;

    public int getResType() {
        return resType;
    }

    public void setResType(int resType) {
        this.resType = resType;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCoverUrl() {
        return coverUrl;
    }

    public void setCoverUrl(String coverUrl) {
        this.coverUrl = coverUrl;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public long getListenNum() {
        return listenNum;
    }

    public void setListenNum(long listenNum) {
        this.listenNum = listenNum;
    }

    public int getResId() {
        return resId;
    }

    public void setResId(int resId) {
        this.resId = resId;
    }

    public int getItemType() {
        return itemType;
    }

    public void setItemType(int itemType) {
        this.itemType = itemType;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public boolean isSelected() {
        return isSelected;
    }

    public void setSelected(boolean selected) {
        isSelected = selected;
    }

    public boolean isOnline() {
        return isOnline;
    }

    public void setOnline(boolean online) {
        isOnline = online;
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }
}
