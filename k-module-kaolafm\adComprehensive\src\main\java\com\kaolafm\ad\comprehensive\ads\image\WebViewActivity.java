package com.kaolafm.ad.comprehensive.ads.image;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.drawable.BitmapDrawable;
import android.net.http.SslError;
import android.os.Build;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.util.Log;
import android.view.ViewGroup;
import android.webkit.SslErrorHandler;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ImageView;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.kaolafm.kradio.common.base.BaseSkinAppCompatActivity;
import com.kaolafm.kradio.common.utils.CommonUtils;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.mvp.IPresenter;

@Route(path = RouterConstance.AD_WEBVIEW_COMPREHENSIVE_URL)
public class WebViewActivity extends BaseSkinAppCompatActivity {

    private static final String WEB_URL = "web_url";

    private WebView mWebView;
    private ImageView mCloseView;

    public static void start(Context context, String url) {
        Intent intent = new Intent(context, WebViewActivity.class);
        intent.putExtra(WEB_URL, url);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        CommonUtils.getInstance().initGreyStyle(getWindow());
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_ad_webview;
    }

    @Override
    public int getLayoutId_Tow() {
        return 0;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        String url = getIntent().getStringExtra(WEB_URL);

        Log.i("WebViewActivity","url:"+url);

        mWebView = findViewById(R.id.ad_webview);
        CommonUtils.getInstance().initGreyStyle(mWebView);
        mWebView.getSettings().setSavePassword(false);
        if(Build.VERSION.SDK_INT < 19 && mWebView != null) {
            mWebView.removeJavascriptInterface("searchBoxJavaBridge_");
            mWebView.removeJavascriptInterface("accessibility");
            mWebView.removeJavascriptInterface("accessibilityTraversal");
        }

        mCloseView = findViewById(R.id.ad_webview_close);
        Bitmap bitmap = BitmapFactory.decodeResource(getResources(), R.drawable.collapse);
        BitmapDrawable drawable = new BitmapDrawable(getResources(), bitmap);
        drawable.setAntiAlias(true);
        mCloseView.setImageDrawable(drawable);
        mCloseView.setOnClickListener(view1 -> {
            finish();
        });

        if (TextUtils.isEmpty(url)) {
            return;
        }

        setWebViewSetting();

        mWebView.loadUrl(url);
    }

    @Override
    public void initData() {

    }
    @Override
    public void onResume() {
        if (mWebView != null) {
            mWebView.onResume();
        }
        super.onResume();
    }

    @Override
    public void onPause() {
        super.onPause();
        if (mWebView != null)  {
            mWebView.onPause();
        }
    }

    @Override
    protected IPresenter createPresenter() {
        return null;
    }

    private void setWebViewSetting(){
        WebSettings settings = mWebView.getSettings();


        settings.setUseWideViewPort(true);

        settings.setJavaScriptEnabled(false);
        settings.setSavePassword(false);
        //自动加载图片
        settings.setLoadsImagesAutomatically(true);

        settings.setCacheMode(WebSettings.LOAD_NO_CACHE);//不使用缓存，只从网络获取数据.


        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            // 5.0以上允许加载http和https混合的页面(5.0以下默认允许，5.0+默认禁止)
            settings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        }

        mWebView.setWebViewClient(new WebViewClient(){

            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                //可处理loading开始
                super.onPageStarted(view, url, favicon);
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                //可处理loading结束
                super.onPageFinished(view, url);
            }

            @Override
            public void onReceivedSslError(WebView view, SslErrorHandler handler, SslError error) {
                handler.cancel();
            }
        });
    }

    @Override
    protected void onDestroy() {
        if( mWebView!=null) {
            ViewGroup parent = (ViewGroup) mWebView.getParent();
            if (parent != null) {
                parent.removeView(mWebView);
            }

            mWebView.stopLoading();
            // 退出时调用此方法，移除绑定的服务，否则某些特定系统会报错
            mWebView.getSettings().setJavaScriptEnabled(false);
            mWebView.clearHistory();
            mWebView.clearCache(true);
            mWebView.clearView();
            mWebView.removeAllViews();
            mWebView.destroy();

            mWebView = null;
        }
        super.onDestroy();
    }
}
