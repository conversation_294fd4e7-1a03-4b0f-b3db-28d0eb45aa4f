package com.kaolafm.kradio.flavor.impl;

import android.annotation.SuppressLint;
import android.cluster.ClusterManager;
import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Bundle;
import androidx.annotation.IntDef;
import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.kradio.flavor.common.DataUtil;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.SyncInstrumentInter;
import com.kaolafm.kradio.player.manager.BroadcastRadioListManager;

import com.kaolafm.kradio.player.manager.OnBroadcastRadioListChangedListener;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.sdk.core.mediaplayer.IPlayerStateListener;
import com.kaolafm.sdk.core.mediaplayer.OnBroadcastRadioListChangedListener;


import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.utils.DateFormatUtil;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

@SuppressLint("WrongConstant")
public class SyncInstrumentImpl implements SyncInstrumentInter, IPlayerStateListener, OnBroadcastRadioListChangedListener {
    private static final String TAG = "SyncInstrumentImpl";

    private ClusterManager mClusterManager;
    private Context mContext;
    private Date ddate = new Date();
    private Date pdate = new Date();
    private String appName;
    private KLAutoPlayerManager mPlayerManager;
    private IPlayerInitCompleteListener onPlayerInitCompleteListener = new IPlayerInitCompleteListener() {
        @Override
        public void onPlayerInitComplete(boolean b) {
            try {
                PlayerManager.getInstance().removePlayerInitComplete(onPlayerInitCompleteListener);
                mPlayerManager.addIPlayerStateListener(SyncInstrumentImpl.this);
                BroadcastRadioListManager.getInstance().addOnBroadcastRadioChangedListener(SyncInstrumentImpl.this);
//                mClusterManager = (ClusterManager) mContext.getSystemService("cluster");
                mClusterManager = ClusterManager.getInstance(AppDelegate.getInstance().getContext());
            } catch (Exception e) {
                e.printStackTrace();
            } catch (NoClassDefFoundError ndf) {
                ndf.printStackTrace();
            }
        }
    };


    private void initPlayer() {

        PlayerManager playerManager = PlayerManager.getInstance();
        boolean isInitSuccess = playerManager.isPlayerInitSuccess();
        if (isInitSuccess) {
            try {
                mPlayerManager.addIPlayerStateListener(this);
                BroadcastRadioListManager.getInstance().addOnBroadcastRadioChangedListener(SyncInstrumentImpl.this);
//                mClusterManager = (ClusterManager) mContext.getSystemService("cluster");
                mClusterManager = ClusterManager.getInstance(AppDelegate.getInstance().getContext());
            } catch (Exception e) {
                e.printStackTrace();
            } catch (NoClassDefFoundError ndf) {
                ndf.printStackTrace();
            }
        } else {
            playerManager.addPlayerInitComplete(onPlayerInitCompleteListener);
        }
    }

    @Override
    public boolean initSyncInstrument() {
        //初始化注册监听接口
        Log.i(TAG, "initSyncInstrument: ");
        mContext = AppDelegate.getInstance().getContext();
        mPlayerManager = KLAutoPlayerManager.getInstance();
        try {
            mClusterManager = ClusterManager.getInstance(AppDelegate.getInstance().getContext());
        } catch (NoClassDefFoundError ndf) {
            ndf.printStackTrace();
        }
        initPlayer();
        return true;
    }

    @Override
    public boolean releaseSyncInstrument() {
        //反注册监听接口
//        Log.i(TAG, "releaseSyncInstrument: ");
//        KLAutoPlayerManager.getInstance().removeIPlayerStateListener(this);
//        BroadcastRadioListManager.getInstance().removeOnPlayingRadioChangedListener(this);
        return true;
    }

    @Override
    public void onIdle(PlayItem playItem) {

    }

    @Override
    public void onPlayerPreparing(PlayItem playItem) {
        Log.i(TAG, "onPlayerPreparing: " + playItem.getAlbumName() + "      " + playItem.getTitle());
        sendTitle(playItem.getTitle());
        if (!TextUtils.isEmpty(playItem.getAudioPic())) {
            sendImg(playItem.getAudioPic());
        } else {
            sendImg(playItem.getAlbumPic());
        }
        sendAppName();
    }

    @Override
    public void onPlayerPlaying(PlayItem playItem) {
        Log.i(TAG, "onPlayerPlaying: MEDAI_STATE_START ");
        sendStatus(ClusterManager.State.MEDAI_STATE_START);
    }

    @Override
    public void onPlayerPaused(PlayItem playItem) {
        Log.i(TAG, "onPlayerPaused: MEDAI_STATE_PAUSE ");
        sendStatus(ClusterManager.State.MEDAI_STATE_PAUSE);
    }

    @Override
    public void onProgress(String s, int mProgress, int mDuration, boolean b) {
        PlayItem item = PlayerManager.getInstance().getCurPlayItem();
        if (item != null && !item.isLivingUrl()) {
            long progressTalSeconds = (mProgress + 500) / 1000;
            long durationTalSeconds = (mDuration + 500) / 1000;

            int progressHours = (int) progressTalSeconds / 3600;
            int progressMinutes = (int) (progressTalSeconds / 60) % 60;
            int progressSeconds = (int) progressTalSeconds % 60;

            int durationHours = (int) durationTalSeconds / 3600;
            int durationMinutes = (int) (durationTalSeconds / 60) % 60;
            int durationSeconds = (int) durationTalSeconds % 60;

//            Log.i(TAG, "onProgress: progress =: " + progressHours + ":" + progressMinutes + ":" + progressSeconds);
//            Log.i(TAG, "onProgress: duration =: " + durationHours + ":" + durationMinutes + ":" + durationSeconds);
            sendTime(progressHours, progressMinutes, progressSeconds, durationHours, durationMinutes, durationSeconds);
        }

    }

    @Override
    public void onPlayerFailed(PlayItem playItem, int i, int i1) {

    }

    @Override
    public void onPlayerEnd(PlayItem playItem) {

    }

    @Override
    public void onSeekStart(String s) {

    }

    @Override
    public void onSeekComplete(String s) {

    }

    @Override
    public void onBufferingStart(PlayItem playItem) {

    }

    @Override
    public void onBufferingEnd(PlayItem playItem) {

    }

    @Override
    public void onBroadcastRadioListInitiated(int i) {

    }

    @Override
    public void onBroadcastRadioListUpdated(int i) {

    }

    @Override
    public void onProgramUpdated(int i) {

    }

    @Override
    public void onLivingCountDown(String time) {
        PlayItem item = PlayerManager.getInstance().getCurPlayItem();
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss", Locale.US);

        try {
            pdate = sdf.parse(time);
            ddate = sdf.parse(DateFormatUtil.getCurrDate(item.getFinishTime()));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        int progressHours = DataUtil.getHour(pdate);
        int progressMinutes = DataUtil.getMinute(pdate);
        int progressSeconds = DataUtil.getSecond(pdate);


        int durationHours = DataUtil.getHour(ddate);
        int durationMinutes = DataUtil.getMinute(ddate);
        int durationSeconds = DataUtil.getSecond(ddate);

//        Log.i(TAG, "onLivingProgress: " + progressHours + ":" + progressMinutes + ":" + progressSeconds);
//        Log.i(TAG, "onLivingDuration: " + durationHours + ":" + durationMinutes + ":" + durationSeconds);
        sendTime(progressHours, progressMinutes, progressSeconds, durationHours, durationMinutes, durationSeconds);

    }


    private String getAppName(Context mContext) {
        String appName = null;
        try {
            PackageManager packageManager = mContext.getPackageManager();
            PackageInfo packageInfo = packageManager.getPackageInfo(
                    mContext.getPackageName(), 0);
            appName = mContext.getResources().getString(packageInfo.applicationInfo.labelRes);
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        return appName;
    }

    //设置歌曲名称
    private void sendTitle(String title) {
        if (mClusterManager == null) {
            Log.i(TAG, "sendTitle: mClusterManager = " + mClusterManager);
            return;
        }
        Log.i(TAG, "sendTitle: " + title);
        Bundle data = new Bundle();
        data.putString(ClusterManager.Key.MEDIA_NAME, title);
        mClusterManager.sendCommand(ClusterManager.Cmd.ACU_MEDIA_NAME, data);
    }

    //通知歌曲进度及时长
    private void sendTime(int curHour, int curMin, int curSec, int totalHour, int totalMin, int totalSec) {
        if (mClusterManager == null) {
            Log.i(TAG, "sendTime: mClusterManager = " + mClusterManager);
            return;
        }
        Bundle data = new Bundle();
        data.putInt(ClusterManager.Key.CLUSTER_CUR_HOUR, curHour);
        data.putInt(ClusterManager.Key.CLUSTER_CUR_MINUTE, curMin);
        data.putInt(ClusterManager.Key.CLUSTER_CUR_SECOND, curSec);
        data.putInt(ClusterManager.Key.CLUSTER_TOTAL_HOUR, totalHour);
        data.putInt(ClusterManager.Key.CLUSTER_TOTAL_MINUTE, totalMin);
        data.putInt(ClusterManager.Key.CLUSTER_TOTAL_SECOND, totalSec);
        mClusterManager.sendCommand(ClusterManager.Cmd.ACU_MEDIA_TIME, data);
    }

    //通知封面图片
    private void sendImg(String img) {
        if (mClusterManager == null) {
            Log.i(TAG, "sendImg: mClusterManager = " + mClusterManager);
            return;
        }
        Log.i(TAG, "sendImg: " + img);
        Bundle data = new Bundle();
        data.putString(ClusterManager.Key.MEDIA_ALBUM_PATH, img);
        mClusterManager.sendCommand(ClusterManager.Cmd.ACU_MEDIA_ALBUM, data);
    }

    //通知播放状态
    private void sendStatus(@GaeiPlayStauts int status) {
        if (mClusterManager == null) {
            Log.i(TAG, "sendStatus: mClusterManager = " + mClusterManager);
            return;
        }
        Log.i(TAG, "sendStatus: " + status);
        mClusterManager.sendCommand(ClusterManager.Cmd.ACU_MEDIA_STATE, status);
    }

    //通知应用名称
    private void sendAppName() {
        if (mClusterManager == null) {
            return;
        }
        if (appName == null) {
            appName = getAppName(mContext);
        }
        Log.i(TAG, "sendAppName: " + appName);
        Bundle data = new Bundle();
        data.putString(ClusterManager.Key.MEDIA_APP_NAME, appName);
        mClusterManager.sendCommand(ClusterManager.Cmd.ACU_MEDIA_APP_NAME, data);
    }


    @Retention(RetentionPolicy.SOURCE)
    @IntDef({ClusterManager.State.MEDAI_STATE_START, ClusterManager.State.MEDAI_STATE_PAUSE})
    public @interface GaeiPlayStauts {
    }

}