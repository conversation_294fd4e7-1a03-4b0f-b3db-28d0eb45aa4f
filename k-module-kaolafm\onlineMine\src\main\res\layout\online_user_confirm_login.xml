<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/scanned_user_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    tools:ignore="MissingDefaultResource">

    <RelativeLayout
        android:id="@+id/scanned_user_rl"
        android:layout_width="@dimen/m140"
        android:layout_height="@dimen/m140"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:background="@drawable/login_confirm_user_pic_bg"
        app:layout_constraintTop_toTopOf="parent">

        <com.kaolafm.kradio.component.ui.base.view.OvalImageView
            android:id="@+id/scanned_user_avatar"
            android:layout_width="@dimen/m128"
            android:layout_height="@dimen/m128"
            android:layout_centerInParent="true"
            android:scaleType="centerInside"
            app:circle="true" />
    </RelativeLayout>

    <TextView
        android:id="@+id/scanned_user_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y8"
        android:textColor="@color/colorWhite_60"
        android:textSize="@dimen/m24"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/scanned_user_rl" />

    <TextView
        android:id="@+id/scanned_qr_msg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y20"
        android:text="@string/user_qr_scanned_sure"
        android:textColor="@color/colorWhite"
        android:textSize="@dimen/m24"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/scanned_user_name" />

    <Button
        android:id="@+id/scanned_btn_back"
        android:layout_width="@dimen/x200"
        android:layout_height="@dimen/y48"
        android:layout_marginTop="@dimen/y30"
        android:background="@drawable/online_selector_code_btn_bg"
        android:gravity="center"
        android:text="@string/user_qr_back_bt_msg"
        android:textColor="@color/user_qr_back_relogin_color"
        android:textSize="@dimen/m20"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/scanned_qr_msg" />

</androidx.constraintlayout.widget.ConstraintLayout>