//package com.kaolafm.kradio.flavor.impl;
//
//import android.annotation.SuppressLint;
//import android.app.Activity;
//import android.content.Context;
//import android.content.Intent;
//import android.util.Log;
//
//import com.kaolafm.kradio.k_kaolafm.KLAutoPlayerManager;
//import com.kaolafm.kradio.lib.base.AppDelegate;
//import com.kaolafm.kradio.lib.base.flavor.KRadioAudioPlayLogicInter;
//import com.kaolafm.sdk.core.mediaplayer.AudioStatusManager;
//import com.kaolafm.sdk.core.mediaplayer.OnPlayLogicListener;
//
//
///******************************************
// * 类描述:
// *
// * @version: V1.0
// * @author: yangshaoning
// * @time: 2020-10-14 10:42
// ******************************************/
//public final class KRadioAudioPlayLogicImpl implements KRadioAudioPlayLogicInter {
//    private final static String TAG = "KRadioAudioPlayLogicImpl";
//
//    public KRadioAudioPlayLogicImpl() {
//        AudioStatusManager.getInstance().setOnPlayLogicListener(new OnPlayLogicListener() {
//            @SuppressLint("LongLogTag")
//            @Override
//            public boolean onPlayLogicDispose() {
//                int currentFocus = AudioStatusManager.getInstance().getCurrentFocusChange();
//                Log.i(TAG, "isAppOnForeground--------->currentFocus = " + currentFocus);
//                if ((currentFocus < 0)) {
//                    boolean isAppOnForeground = AppDelegate.getInstance().isAppForeground();
//                    Log.i(TAG, "isAppOnForeground--------->isAppOnForeground = " + isAppOnForeground);
//                    return !isAppOnForeground;
//                }
//                return false;
//            }
//        });
//    }
//
//    @SuppressLint("LongLogTag")
//    @Override
//    public boolean autoPlayAudio(Object... args) {
//        return false;
//    }
//
//    @Override
//    public boolean requestAudioFocus(Object... args) {
//        AudioStatusManager audioStatusManager = AudioStatusManager.getInstance();
//        boolean flag = false;
//        if (audioStatusManager.getCurrentFocusChange() < 1) {
//            flag = audioStatusManager.requestAudioFocus();
//        }
//        return flag;
//    }
//
//    @Override
//    public boolean resumeAudioPlayLogic(Object... args) {
//        recoverPlay();
//        return true;
//    }
//
//    @SuppressLint("LongLogTag")
//    @Override
//    public boolean restartAudioPlayLogic(Object... args) {
//        Context context = (Context) args[0];
//        if (context instanceof Activity) {
//            Intent intent = ((Activity) context).getIntent();
//            // 解决https://app.huoban.com/tables/2100000007530121/items/2300001668075066?userId=1229522问题
//            if (intent != null) {
//                boolean autoPlay = intent.getBooleanExtra("auto_play", false);
//                Log.i(TAG, "restartAudioPlayLogic autoPlay = " + autoPlay);
//                if (autoPlay) {
//                    KLAutoPlayerManager klAutoPlayerManager = KLAutoPlayerManager.getInstance();
//                    if (!klAutoPlayerManager.isPlaying()) {
//                        klAutoPlayerManager.switchPlayerStatus(true);
//                        return true;
//                    }
//                }
//            }
//        }
//        return false;
//    }
//
//    @SuppressLint("LongLogTag")
//    private void recoverPlay() {
//        KLAutoPlayerManager klAutoPlayerManager = KLAutoPlayerManager.getInstance();
//        AudioStatusManager audioStatusManager = AudioStatusManager.getInstance();
//        Log.i(TAG, "recoverPlay---------->audioStatusManager.isPausedFromUser() = " + audioStatusManager.isPausedFromUser()
//                + "          audioStatusManager.getCurrentFocusChange() = " + audioStatusManager.getCurrentFocusChange());
////        if (audioStatusManager.getCurrentFocusChange() >= 0) {
////            return;
////        }
//        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001671449799?userId=1229522问题
//        audioStatusManager.requestAudioFocus();
//
//        if (!audioStatusManager.isPausedFromUser()) {
//            if (!klAutoPlayerManager.isPlaying()) {
//                klAutoPlayerManager.switchPlayerStatus(false);
//            }
//        }
//    }
//
//    @SuppressLint("LongLogTag")
//    @Override
//    public boolean doStartInPlay(Object... args) {
//        int audioFocus = AudioStatusManager.getInstance().getCurrentFocusChange();
//        boolean isAppOnForeground = AppDelegate.getInstance().isAppForeground();
//        Log.i(TAG, "doStartInPlay start focus = " + audioFocus + " isAppOnForeground = " + isAppOnForeground);
//        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001592906358?userId=1229522问题
//        if (audioFocus < 0 && !isAppOnForeground) {
//            return false;
//        }
//        if (!KLAutoPlayerManager.getInstance().isPlaying()) {
//            KLAutoPlayerManager.getInstance().switchPlayerStatus(true);
//        }
//        return true;
//    }
//}