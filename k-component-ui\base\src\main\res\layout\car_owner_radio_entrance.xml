<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/car_owner_radio_card"
    android:layout_width="@dimen/m475"
    android:layout_height="@dimen/m476">

    <ImageView
        android:id="@+id/radio_road"
        android:layout_width="@dimen/m288"
        android:layout_height="@dimen/m126"
        android:src="@drawable/car_owner_radio_entrance_road"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/car_owner_radio_card_center"
        android:layout_width="@dimen/m340"
        android:layout_height="@dimen/m340"
        android:layout_marginTop="@dimen/m66"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <include layout="@layout/car_owner_radio_entrance_back" />

        <include layout="@layout/car_owner_radio_entrance_front" />

    </androidx.constraintlayout.widget.ConstraintLayout>



    <ImageView
        android:id="@+id/radio_bg"
        android:layout_width="@dimen/m475"
        android:layout_height="@dimen/m422"
        android:layout_marginBottom="@dimen/m54"
        android:scaleType="centerInside"
        android:src="@drawable/car_owner_radio_entrance_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.kaolafm.kradio.component.ui.base.view.KradioTextView
        android:id="@+id/radio_in_btn"
        android:layout_width="@dimen/m260"
        android:layout_height="@dimen/m92"
        android:layout_marginBottom="@dimen/m16"
        android:background="@drawable/car_owner_radio_entrance_btn"
        android:gravity="center_horizontal"
        android:paddingTop="@dimen/m23"
        android:text="进入听友社区"
        android:textColor="#FFFFFFFF"
        android:textSize="@dimen/m26"
        app:kt_font_weight="0.7"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>