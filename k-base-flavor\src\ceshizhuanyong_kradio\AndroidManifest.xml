<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.kaolafm.kradio.flavor">

    <application>
<!--        <meta-data-->
<!--            android:name="com.baidu.lbsapi.API_KEY"-->
<!--            android:value="PHYyfkAiXC7PKG8Hq16xRjhMkjyUBQHn" />-->

        <!--云听自己的签名-->
        <!--        <meta-data-->
        <!--            android:name="com.baidu.lbsapi.API_KEY"-->
        <!--            android:value="q6dQZ5bWsUFq8NalZOKNhWIBAbcatuaG" >-->
        <!--        </meta-data>-->

<!--        <service-->
<!--            android:name="com.baidu.location.f"-->
<!--            android:enabled="true"-->
<!--            android:process=":remote" />-->

    </application>
</manifest>