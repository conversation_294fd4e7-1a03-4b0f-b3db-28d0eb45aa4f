package com.kaolafm.kradio.online.home.mvp;


import com.kaolafm.kradio.lib.base.mvp.IPresenter;
import com.kaolafm.kradio.lib.base.mvp.IView;
import com.kaolafm.opensdk.api.operation.model.column.ColumnGrp;

import java.util.List;

/*************************************
 * 类名称：HomePlayerContract
 * 类描述：
 *
 * @version: 0.0.1
 * @author: 刘云龙
 **********************************/
public class HomePlayerContract {

    public interface View extends IView {


        void showContent(List<ColumnGrp> cells);

        void showLoading();

        void hideLoading();

        void showError(String error);

        void hideErrorLayout();

    }

    public interface Presenter extends IPresenter {
        void init();
    }
}
