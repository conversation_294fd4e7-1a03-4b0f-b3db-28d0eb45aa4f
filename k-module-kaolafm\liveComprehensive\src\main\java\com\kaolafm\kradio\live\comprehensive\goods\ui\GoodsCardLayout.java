package com.kaolafm.kradio.live.comprehensive.goods.ui;

import android.content.Context;
import android.graphics.Paint;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.ScaleAnimation;
import android.widget.FrameLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.kaolafm.kradio.component.ui.base.view.OvalImageView;
import com.kaolafm.kradio.component.ui.base.view.RateView;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.purchase.util.MoneyUtils;
import com.kaolafm.opensdk.api.yunxin.model.GoodsCardMsg;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.util.ReportConstants;

/**
 * <AUTHOR> shiqian
 * @date 2023-02-15
 */
public class GoodsCardLayout extends FrameLayout {
    private final String TAG = "ShopCardLayout";

    public static final int CARD_SHOWING_STATUS = 1;
    public static final int CARD_SHOW_STATUS = 2;
    public static final int CARD_DISMISSING_STATUS = 3;
    public static final int CARD_DISMISS_STATUS = 4;
    private int mShowStatus = CARD_DISMISS_STATUS;
    private Context mContext;
    private LayoutInflater mInflater;
    private View rootView, mBaseView;
    OvalImageView item_img;
    RelativeLayout item_explain;
    RateView item_explain_sign;
    TextView item_position;
    TextView item_title;
    TextView item_desc;
    TextView item_origin_price;
    TextView item_discount_price;
    TextView item_btn;
    TextView item_off_shelf;
    private OnShopPurchaseListener mOnShopPurchaseListener;
    private OnGetLocationInWindowListener mOnGetLocationInWindowListener;
    private Animation mInDurationAnimation, mOutDurationAnimation;
    private int popupWidth, popupHeight;

    private GoodsCardMsg mData;
    private int mPosition = -1;
    private String pageId;

    public GoodsCardLayout(Context context, View baseView, OnGetLocationInWindowListener onGetLocationInWindowListener) {
        this(context, null, baseView, onGetLocationInWindowListener);
    }

    public GoodsCardLayout(Context context, AttributeSet attrs, View baseView, OnGetLocationInWindowListener onGetLocationInWindowListener) {
        this(context, attrs, 0, baseView, onGetLocationInWindowListener);
    }

    public GoodsCardLayout(Context context, AttributeSet attrs, int defStyleAttr, View baseView, OnGetLocationInWindowListener onGetLocationInWindowListener) {
        super(context, attrs, defStyleAttr);

        mInflater = LayoutInflater.from(context);
        mContext = context;
        mBaseView = baseView;
        mOnGetLocationInWindowListener = onGetLocationInWindowListener;
        initView();
    }

    private void initView() {
        rootView = mInflater.inflate(R.layout.comprehensive_goods_card, null);
        item_img = rootView.findViewById(R.id.item_img);
        item_explain = rootView.findViewById(R.id.item_explain);
        item_explain_sign = rootView.findViewById(R.id.item_explain_sign);
        item_position = rootView.findViewById(R.id.item_position);
        item_title = rootView.findViewById(R.id.item_title);
        item_desc = rootView.findViewById(R.id.item_desc);
        item_origin_price = rootView.findViewById(R.id.item_origin_price);
        item_discount_price = rootView.findViewById(R.id.item_discount_price);
        item_off_shelf = rootView.findViewById(R.id.item_off_shelf);
        item_btn = rootView.findViewById(R.id.item_btn);
        item_btn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mOnShopPurchaseListener != null) {
                    mOnShopPurchaseListener.onPurchase(mData, mPosition);
                }
                String radioId = null, audioId = null;

                PlayItem curPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
                if (curPlayItem != null) {
                    radioId = curPlayItem.getRadioId();
                    audioId = String.valueOf(curPlayItem.getAudioId());
                }

                ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_LIVE_ROOM_MERCHANDISE_PUSH_PURCHASE, item_btn.getText().toString(), pageId
                        , ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_LIVE_ROOM_MERCHANDISE, radioId, audioId, audioId, null));
            }
        });
        mInDurationAnimation = AnimationUtils.loadAnimation(getContext(), R.anim.comprehensive_shop_card_in_duration_anim);
        mInDurationAnimation.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {
                mShowStatus = CARD_SHOWING_STATUS;
            }

            @Override
            public void onAnimationEnd(Animation animation) {
                mShowStatus = CARD_SHOW_STATUS;
            }

            @Override
            public void onAnimationRepeat(Animation animation) {
                mShowStatus = CARD_SHOWING_STATUS;
            }
        });
        mOutDurationAnimation = AnimationUtils.loadAnimation(getContext(), R.anim.comprehensive_shop_card_out_duration_anim);
        mOutDurationAnimation.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {
                mShowStatus = CARD_DISMISSING_STATUS;
            }

            @Override
            public void onAnimationEnd(Animation animation) {
                mShowStatus = CARD_DISMISS_STATUS;
            }

            @Override
            public void onAnimationRepeat(Animation animation) {
                mShowStatus = CARD_DISMISSING_STATUS;
            }
        });

        this.addView(rootView);

        post(new Runnable() {
            @Override
            public void run() {
                if (mBaseView == null) {
                    return;
                }
                /** 获取参照物控件（直播间底部功能区的购物按钮）的位置 */
                int[] location = new int[2];
                mBaseView.getLocationOnScreen(location);
                Log.i(TAG, "location[0]" + location[0]);
                Log.i(TAG, "location[1]" + location[1]);
                /** 这个很重要 ,获取view的宽高 */
                getRootView().measure(View.MeasureSpec.UNSPECIFIED, View.MeasureSpec.UNSPECIFIED);
                popupWidth = getRootView().getMeasuredWidth();
                popupHeight = getRootView().getMeasuredHeight();
                int popupX = location[0] + mBaseView.getWidth() / 2 - popupWidth / 2;
                int popupY = location[1] - popupHeight - ResUtil.getDimen(R.dimen.y12);
                mOnGetLocationInWindowListener.onGetLocation(popupX, popupY);
//                setX(location[0] + mBaseView.getWidth()/2 - popupWidth/2);
            }
        });
    }

    public void show(GoodsCardMsg goodsCardMsg, int position) {
        setVisibility(VISIBLE);
        if (mShowStatus == CARD_SHOWING_STATUS) {

        } else if (mShowStatus == CARD_SHOW_STATUS) {
            startAnimation(mInDurationAnimation);
        } else if (mShowStatus == CARD_DISMISSING_STATUS) {
            startAnimation(mInDurationAnimation);
        } else if (mShowStatus == CARD_DISMISS_STATUS) {
            startAnimation(mInDurationAnimation);
        }

        setData(goodsCardMsg, position);
        String radioId = null, audioId = null;

        PlayItem curPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
        if (curPlayItem != null) {
            radioId = curPlayItem.getRadioId();
            audioId = String.valueOf(curPlayItem.getAudioId());
        }
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_LIVE_ROOM_MERCHANDISE_PUSH_PURCHASE, item_btn.getText().toString(), pageId
                , ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_LIVE_ROOM_MERCHANDISE, radioId, audioId, audioId, null));
    }

    public void dismiss() {
        if (mShowStatus == CARD_SHOWING_STATUS) {
            setVisibility(GONE);
        } else if (mShowStatus == CARD_SHOW_STATUS) {
            startAnimation(mOutDurationAnimation);
        } else if (mShowStatus == CARD_DISMISSING_STATUS) {

        } else if (mShowStatus == CARD_DISMISS_STATUS) {

        }
    }

    public void setPageId(String pageId) {
        this.pageId = pageId;
    }

    public interface OnGetLocationInWindowListener {
        void onGetLocation(int width, int height);
    }

    public interface OnShopPurchaseListener {
        void onPurchase(GoodsCardMsg goodsCardMsgm, int position);
    }

    public void setOnShopPurchaseListener(OnShopPurchaseListener onShopPurchaseListener) {
        mOnShopPurchaseListener = onShopPurchaseListener;
    }

    private void setData(GoodsCardMsg goodsCardMsg, int position) {
        mData = goodsCardMsg;
        mPosition = position;
        updateData();
    }

    /**
     * 展示UI
     */
    private void updateData() {
        int p = mPosition + 1;
        if (p > 0) {
            item_position.setText(p + "");
            item_position.setVisibility(VISIBLE);
        } else {
            item_position.setVisibility(GONE);
        }
        if (!TextUtils.isEmpty(mData.getName())) {
            item_title.setText(mData.getName());
        }
        if (!TextUtils.isEmpty(mData.getSellPoint())) {
            item_desc.setText(mData.getSellPoint());
        }
        if (!TextUtils.isEmpty(mData.getPicUrl())) {
            ImageLoader.getInstance().displayImage(mContext, mData.getPicUrl(), item_img);
        }
        item_origin_price.setText(MoneyUtils.changeF2Y(mData.getMarketPrice()) + "");
        item_origin_price.getPaint().setFlags(Paint.STRIKE_THRU_TEXT_FLAG);
        item_discount_price.setText(MoneyUtils.changeF2Y(mData.getSalesPrice()) + "");
        item_off_shelf.setVisibility(View.GONE);
        if (mData.getPushType() != null && mData.getPushType().equals(1)) {
            item_explain_sign.startAnimation();
            item_explain.setVisibility(View.VISIBLE);
        } else {
            item_explain.setVisibility(View.GONE);
        }
        if (mData.getShelf() != null && mData.getShelf() == 1) {
            if (mData.getStock() != null && mData.getStock() > 0) {
                Log.d(TAG, mData.getStock() + "");
                item_off_shelf.setVisibility(View.GONE);
                item_btn.setClickable(true);
                item_btn.setBackground(mContext.getResources().getDrawable(R.drawable.comprehensive_goods_item_btn_normal_bg));
            } else {
                item_off_shelf.setVisibility(View.GONE);
                item_btn.setClickable(false);
                item_btn.setBackground(mContext.getResources().getDrawable(R.drawable.comprehensive_goods_item_btn_off_bg));
            }
        } else {
            item_off_shelf.setVisibility(View.VISIBLE);
            item_btn.setClickable(false);
            item_btn.setBackground(mContext.getResources().getDrawable(R.drawable.comprehensive_goods_item_btn_off_bg));
        }
//        invalidate();
    }
}
