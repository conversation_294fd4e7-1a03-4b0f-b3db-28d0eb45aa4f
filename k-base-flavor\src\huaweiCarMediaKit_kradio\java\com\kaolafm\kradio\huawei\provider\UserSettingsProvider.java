package com.kaolafm.kradio.huawei.provider;


import android.content.Context;

import com.huawei.carmediakit.bean.SettingItem;
import com.huawei.carmediakit.bean.SettingValue;
import com.huawei.carmediakit.bean.UserInfo;
import com.huawei.carmediakit.callback.BasicCallback;
import com.huawei.carmediakit.constant.ErrorCode;
import com.huawei.carmediakit.provider.IUserSettingsProvider;
import com.kaolafm.kradio.huawei.utils.Constant;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.kradio.lib.utils.SharedPreferenceUtil;
import com.kaolafm.utils.AppInfoUtil;

import java.util.ArrayList;
import java.util.List;

public class UserSettingsProvider implements IUserSettingsProvider {
    public static final String TAG = Constant.TAG;

    @Override
    public void asyncQueryUserInfo(BasicCallback<UserInfo> basicCallback) {
        Logger.i(TAG, "queryUserInfo");

        UserInfo userInfo = new UserInfo();
        userInfo.setNickName(UserInfoManager.getInstance().getUserNickName());
        userInfo.setProfilePicUrl(UserInfoManager.getInstance().getUserFavicon());
        userInfo.setUserId(UserInfoManager.getInstance().getUserId());
        userInfo.setOnline(UserInfoManager.getInstance().isUserBound());

        basicCallback.callback(userInfo, ErrorCode.SUCCESS, "success");
    }

    @Override
    public List<SettingItem> querySettingItems() {
        Logger.i(TAG, "querySettingItems");

        SharedPreferenceUtil sp = SharedPreferenceUtil.getInstance(
                AppDelegate.getInstance().getContext(),
                "USER_SETTING_INFO",
                Context.MODE_PRIVATE);

        List<SettingItem> arrayList = new ArrayList<SettingItem>();
        arrayList.add(getPlayOptionItem(sp));
        arrayList.add(getAboutItem(sp));
        arrayList.add(getFeedbackItem(sp));

        return arrayList;
    }

    private SettingItem getPlayOptionItem(SharedPreferenceUtil sp) {
        SettingItem.SettingItemBuilder settingItemBuilder1 = new SettingItem.SettingItemBuilder(SettingItem.SettingType.OPTION);
        SettingValue.OptionSettingValue optionSettingValue2 = new SettingValue.OptionSettingValue();
        List<SettingValue.SettingOption> arrayList1 = new ArrayList();
        SettingValue.SettingOption settingOption1 = new SettingValue.SettingOption();

        int i = sp.getInt("auto_option", 1);
        settingOption1.setSelected(i == 0);
        settingOption1.setValue("自动");
        arrayList1.add(settingOption1);

        SettingValue.SettingOption settingOption2 = new SettingValue.SettingOption();
        settingOption2.setSelected(i == 1);
        settingOption2.setValue("高品质");
        arrayList1.add(settingOption2);

        SettingValue.SettingOption settingOption3 = new SettingValue.SettingOption();
        settingOption3.setSelected(i == 2);
        settingOption3.setValue("中品质");
        arrayList1.add(settingOption3);

        SettingValue.SettingOption settingOption4 = new SettingValue.SettingOption();
        settingOption4.setSelected(i == 3);
        settingOption4.setValue("低品质");
        arrayList1.add(settingOption4);

        optionSettingValue2.setOptions(arrayList1);
        settingItemBuilder1.setId(1)
                .setTitle("播放音质")
                .setValue((SettingValue)optionSettingValue2);

        return settingItemBuilder1.build();
    }

    private SettingItem getAboutItem(SharedPreferenceUtil sp) {
        SettingItem.SettingItemBuilder settingItemBuilder1 = new SettingItem.SettingItemBuilder(SettingItem.SettingType.OPTION);
        SettingValue.JumpSettingValue jumpSettingValue = new SettingValue.JumpSettingValue();
        jumpSettingValue.setValue("com.yunting.car.ABOUT");
        SettingItem.SettingItemBuilder settingItemBuilder2 = settingItemBuilder1.setType(SettingItem.SettingType.JUMP).setId(2);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("关于我们");
        stringBuilder.append(AppInfoUtil.getVersionName(AppDelegate.getInstance().getContext()));
        settingItemBuilder2.setDesp(stringBuilder.toString())
                .setTitle("关于我们")
                .setValue((SettingValue)jumpSettingValue);

        return settingItemBuilder2.build();
    }

    private SettingItem getFeedbackItem(SharedPreferenceUtil sp) {
        SettingItem.SettingItemBuilder settingItemBuilder1 = new SettingItem.SettingItemBuilder(SettingItem.SettingType.OPTION);
        SettingValue.JumpSettingValue jumpSettingValue = new SettingValue.JumpSettingValue();
        jumpSettingValue.setValue("com.yunting.car.FEEDBACK");
        SettingItem.SettingItemBuilder settingItemBuilder2 = settingItemBuilder1.setType(SettingItem.SettingType.JUMP).setId(3);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("意见反馈");
        stringBuilder.append(AppInfoUtil.getVersionName(AppDelegate.getInstance().getContext()));
        settingItemBuilder2.setDesp(stringBuilder.toString())
                .setTitle("意见反馈")
                .setValue((SettingValue)jumpSettingValue);

        return settingItemBuilder2.build();
    }
}
