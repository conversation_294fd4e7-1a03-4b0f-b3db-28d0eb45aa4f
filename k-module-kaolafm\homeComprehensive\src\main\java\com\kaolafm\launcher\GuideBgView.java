package com.kaolafm.launcher;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.Rect;
import android.graphics.Xfermode;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;

import androidx.annotation.IntDef;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.ResUtil;

/**
 * Created by 63062 on 2017/8/2.
 */

public class GuideBgView extends View {

    private static final String TAG = "GuideBgView";
    public static final int HOLE_TYPE_RECT = 0;
    public static final int HOLE_TYPE_CIRCLE = 1;

    @IntDef({ HOLE_TYPE_RECT, HOLE_TYPE_CIRCLE })
    @interface GuideBgType{ }

    @GuideBgType
    int type;
    private Paint paint;
    private View targetView;

    public void setHoleType(@GuideBgType int holeType) {
        this.type = holeType;
    }
    public void setTargetView(View targetView) {
        if(targetView == null){
            return;
        }
        this.targetView = targetView;
    }

    public GuideBgView(Context context) {
        super(context);
        init();
    }

    public GuideBgView(Context context, AttributeSet attributes){
        super(context, attributes);
        init();
    }

    public GuideBgView(Context context, AttributeSet attrs, int defStyleAttr){
        super(context, attrs, defStyleAttr);
        init();
    }

    public GuideBgView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init();
    }
    interface TargetRectFocusedListener{
        void onTargetFocused(Rect rect);
    }

    public void setTargetRectFocuesdListener(TargetRectFocusedListener listener) {
        this.listener = listener;
    }

    private TargetRectFocusedListener listener;



    private void init(){
        paint = new Paint();
        paint.setAntiAlias(true);
    }

    private Xfermode xfermode = new PorterDuffXfermode(PorterDuff.Mode.CLEAR);
    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        if(targetView == null){
            return;
        }

        int[] locations = new int[2];
        targetView.getLocationOnScreen(locations);
        Log.i(TAG, "onDraw --- targetView --- getLocationOnScreen --- locations=[" + locations[0] + ", " + locations[1] + "]");

//        int[] locationsInWindow = new int[2];
//        targetView.getLocationInWindow(locationsInWindow);
//        Log.i(TAG, "onDraw --- targetView --- getLocationInWindow --- locations=[" + locationsInWindow[0] + ", " + locationsInWindow[1] + "]");

        int[] locationsGuide = new int[2];
        this.getLocationOnScreen(locationsGuide);
        Log.i(TAG, "onDraw --- GuideBgView --- getLocationOnScreen ---locations=[" + locationsGuide[0] + ", " + locationsGuide[1] + "]");

//        int[] locationsGuideInWindow = new int[2];
//        this.getLocationInWindow(locationsGuideInWindow);
//        Log.i(TAG, "onDraw --- GuideBgView --- getLocationInWindow ---locations=[" + locationsGuideInWindow[0] + ", " + locationsGuideInWindow[1] + "]");

//        int extraX = locationsGuide[0] - locationsGuideInWindow[0];
//        int extraY = locationsGuide[1] - locationsGuideInWindow[1];
//        Log.i(TAG, "onDraw --- GuideBgView --- [extraX,extraY]=[" + extraX + ", " + extraY + "]");

        int finalX = locations[0] - locationsGuide[0];
        int finalY = locations[1] - locationsGuide[1];
        Log.i(TAG, "onDraw --- GuideBgView --- [finalX,finalY]=[" + finalX + ", " + finalY + "]");


        Rect rect = new Rect();
        rect.left = finalX;
        rect.top = finalY;
        rect.right = rect.left + targetView.getMeasuredWidth();
        rect.bottom = rect.top + targetView.getMeasuredHeight();

        int canvasWidth = canvas.getWidth();
        int canvasHeight = canvas.getHeight();
        //绘制背景
        int layerId = canvas.saveLayer(0, 0, canvasWidth, canvasHeight, null, Canvas.ALL_SAVE_FLAG);
        paint.setColor(ResUtil.getColor(R.color.message_details_bg));
        canvas.drawRect(0, 0, canvasWidth, canvasHeight, paint);

//        getBackground().draw(canvas);
        //挖洞
        paint.setXfermode(xfermode);

        // 使用 Path 绘制带圆角的矩形
        @SuppressLint("DrawAllocation")
        Path path = new Path();
        float radius = 12;

        // 构建圆角矩形路径
        path.addRoundRect(
                rect.left,
                rect.top,
                rect.right,
                rect.bottom,
                radius,
                radius,
                Path.Direction.CW
        );

        if(type == HOLE_TYPE_RECT){
            canvas.drawPath(path, paint);
        } else if(type == HOLE_TYPE_CIRCLE){
            canvas.drawCircle((rect.left + rect.right) / 2
                    , (rect.top + rect.bottom) / 2
                    , (int) Math.sqrt((rect.right - rect.left) * (rect.right - rect.left) + (rect.bottom - rect.top) * (rect.bottom - rect.top)) / 2
                    , paint);
        }
        paint.setXfermode(null);

        if(listener != null){
            listener.onTargetFocused(rect);
        }

        canvas.restoreToCount(layerId);
    }
}
