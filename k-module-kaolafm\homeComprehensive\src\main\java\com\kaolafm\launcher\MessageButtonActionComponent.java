package com.kaolafm.launcher;

import android.content.Context;

import com.kaolafm.kradio.component.BaseComponent;
import com.kaolafm.kradio.component.RealCaller;
import com.kaolafm.kradio.home.comprehensive.gallery.PageJumper;
import com.kaolafm.opensdk.api.CrashMessageButtonActionBean;

public class MessageButtonActionComponent extends BaseComponent {

    private static final String EXEC_MESSAGE_BUTTON_ACTION = "ExecMessageButtonAction";
    private static final String JUMP_TO_PLAYER = "JumpToPlayer";
    private static final String JUMP_TO_LIVE = "JumpToLive";

    @Override
    protected void initProcessors() {
    }

    @Override
    public boolean onCall(RealCaller caller) {

        if(EXEC_MESSAGE_BUTTON_ACTION.equals(caller.actionName())){
            Context context = caller.getParamValue("context");
            CrashMessageButtonActionBean buttonAction = caller.getParamValue("buttonAction");
            MessageButtonActionHelper.handleButtonAction(context, buttonAction);
        } else if(JUMP_TO_PLAYER.equals(caller.actionName())){
            PageJumper.getInstance().jumpToPlayerFragment(null);
        } else if(JUMP_TO_LIVE.equals(caller.actionName())){
            long id = caller.getParamValue("id");
            PageJumper.getInstance().jumpToLivePage(id);
        }

        return super.onCall(caller);
    }
}
