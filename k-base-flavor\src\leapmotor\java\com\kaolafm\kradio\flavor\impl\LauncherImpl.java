package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.view.WindowManager;

import com.kaolafm.kradio.lib.base.AppManager;

public class LauncherImpl implements LauncherInter {
    @Override
    public void onStart(Object... args) {

    }

    @Override
    public void onCreate(Object... args) {
        Activity activity = (Activity) args[0];
        SystemBootUtil systemBootUtil = new SystemBootUtil();
        if (systemBootUtil.isFirstBoot(activity)) {
            ThemeChangeUtil.getInstance().changeTheme();
        }

        ThemeChangeUtil.getInstance().addThemeListener(activity);

//        Activity activity = (Activity)args[0];
//        WindowManager.LayoutParams attributes = activity.getWindow().getAttributes();
//        attributes.dimAmount = 0.0f; //设置窗口之外部分透明程度
//        attributes.x = 500;
//        attributes.y = -20;
//        attributes.width = 1640;
//        attributes.height = 880;
//        activity.getWindow().setAttributes(attributes);
//        activity.getWindow().setFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL, WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL);
    }

    @Override
    public void onResume(Object... args) {

    }

    @Override
    public void onPause(Object... args) {

    }

    @Override
    public void onStop(Object... args) {

    }

    @Override
    public void onRestart(Object... args) {

    }

    @Override
    public void onDestory(Object... args) {

    }
}
