package com.kaolafm.kradio.lib.utils;


public class ScrollTouchHelper {

    private TouchEvent event;

    public ScrollTouchHelper(){
    }

    public TouchEvent getEvent() {
        return event;
    }

    public void setEvent(TouchEvent event) {
        this.event = event;
    }

    public class TouchEvent{
        private int downX;
        private int downY;
        private int upX;
        private int upY;
        private int screenWidth;
        private int screenHeight;

        public TouchEvent() {
        }

        public int getDownX() {
            return downX;
        }

        public void setDownX(int downX) {
            this.downX = downX;
        }

        public int getDownY() {
            return downY;
        }

        public void setDownY(int downY) {
            this.downY = downY;
        }

        public int getUpX() {
            return upX;
        }

        public void setUpX(int upX) {
            this.upX = upX;
        }

        public int getUpY() {
            return upY;
        }

        public void setUpY(int upY) {
            this.upY = upY;
        }

        public int getScreenWidth() {
            return screenWidth;
        }

        public void setScreenWidth(int screenWidth) {
            this.screenWidth = screenWidth;
        }

        public int getScreenHeight() {
            return screenHeight;
        }

        public void setScreenHeight(int screenHeight) {
            this.screenHeight = screenHeight;
        }

        @Override
        public String toString() {
            return "TouchEvent{" +
                    "downX=" + downX +
                    ", downY=" + downY +
                    ", upX=" + upX +
                    ", upY=" + upY +
                    ", screenWidth=" + screenWidth +
                    ", screenHeight=" + screenHeight +
                    '}';
        }
    }
}
