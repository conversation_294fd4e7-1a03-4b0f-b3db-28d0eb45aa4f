<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="@dimen/y26"
    android:id="@+id/bg">

    <View
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="@dimen/y74"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv"
        android:layout_marginStart="@dimen/x55"
        android:scaleType="centerInside"
        android:layout_width="@dimen/m36"
        android:layout_height="@dimen/m36"
        app:layout_constraintBottom_toBottomOf="@id/header"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/header" />

    <TextView
        android:id="@+id/tvName"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/x10"
        android:layout_marginEnd="@dimen/x30"
        android:drawablePadding="@dimen/x10"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:textColor="@color/user_info_value_color"
        android:textSize="@dimen/text_size5"
        app:layout_constraintBottom_toBottomOf="@id/header"
        app:layout_constraintEnd_toStartOf="@id/tvState"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@id/iv"
        app:layout_constraintTop_toTopOf="@id/header"
        app:layout_goneMarginStart="@dimen/x55"
        tools:text="" />


    <TextView
        android:id="@+id/tvState"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginEnd="@dimen/x54"
        android:gravity="center_vertical"
        android:textColor="@color/user_info_value_color"
        android:textSize="@dimen/text_size4"
        app:layout_constraintBottom_toBottomOf="@id/header"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/header"
        tools:text="" />

    <LinearLayout
        android:id="@+id/llContent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingTop="@dimen/y11"
        android:layout_marginStart="@dimen/x55"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/header" />
</androidx.constraintlayout.widget.ConstraintLayout>