[{"code": "10000044", "title": "推荐", "subtitle": "", "description": "", "imageFiles": {"icon": {"url": "http://img.kaolafm.net/mz/images/201809/154a59dc-f11d-4d5b-a43f-69233c92b2c1/default.png", "width": 60, "height": 60}}, "extInfo": {}, "type": "ColumnGrp", "childColumns": [{"type": "Column", "code": "10000050", "title": "午后", "subtitle": "推荐", "description": "", "imageFiles": {}, "extInfo": {"appControlType": "FunctionModule"}, "childColumns": null, "forwardToMore": 0, "moreColumnMember": null, "columnMembers": [{"type": "RadioDetailColumnMember", "code": "10000057", "title": "蔚来电台", "subtitle": "", "description": "蔚来汽车个性化电台", "cornerMark": 0, "imageFiles": {"cover": {"url": "http://img.kaolafm.net/mz/images/201806/4e765d88-1289-499e-ac2c-e65835c48fe9/550_550.jpg", "width": 550, "height": 550}}, "extInfo": {"categoryPath": "", "isLeaf": "0"}, "radioId": 1200000000363, "playTimes": 3356}, {"type": "RadioDetailColumnMember", "code": null, "title": "搞笑电台", "subtitle": null, "description": null, "cornerMark": null, "imageFiles": {"icon": {"url": "http://img.kaolafm.net/mz/images/201806/ce809597-bcc7-4f05-868f-12aedbf29354/default.jpg", "width": null, "height": null}}, "extInfo": null, "radioId": 1200000000162, "playTimes": 8620711}, {"type": "RadioDetailColumnMember", "code": null, "title": "乐听头条", "subtitle": null, "description": null, "cornerMark": null, "imageFiles": {"icon": {"url": "http://img.kaolafm.net/mz/images/201805/be9b1689-1a6f-4837-a84f-7362aadfa19e/default.jpg", "width": null, "height": null}}, "extInfo": null, "radioId": 1200000000374, "playTimes": null}]}, {"type": "Column", "code": "10000051", "title": "音乐", "subtitle": "随心", "description": "", "imageFiles": {}, "extInfo": {}, "childColumns": null, "forwardToMore": 0, "moreColumnMember": null, "columnMembers": [{"type": "RadioQQMusicDetailColumnMember", "code": "10000058", "title": "唤醒心灵", "subtitle": "", "description": "", "cornerMark": 0, "imageFiles": {"cover": {"url": "http://img.kaolafm.net/mz/images/201804/f3ba7832-3bdb-11e8-9b86-c81f66bca8d3/550_550.jpg", "width": 550, "height": 550}}, "extInfo": {"categoryPath": "", "isLeaf": "0"}, "radioQQMusicId": 1700000000040, "oldId": 199, "radioQQMusicType": 0}, {"type": "RadioQQMusicDetailColumnMember", "code": "10000059", "title": "随心听", "subtitle": "", "description": "", "cornerMark": 0, "imageFiles": {"cover": {"url": "http://img.kaolafm.net/mz/images/201804/a2a2cc24-3bdb-11e8-9b86-c81f66bca8d3/550_550.jpg", "width": 550, "height": 550}}, "extInfo": {"categoryPath": "", "isLeaf": "0"}, "radioQQMusicId": 1700000000004, "oldId": 101, "radioQQMusicType": 0}, {"type": "RadioQQMusicDetailColumnMember", "code": "10000060", "title": "咖啡馆", "subtitle": "", "description": "", "cornerMark": 0, "imageFiles": {"cover": {"url": "http://img.kaolafm.net/mz/images/201804/cee6b3f4-3bdb-11e8-9b86-c81f66bca8d3/550_550.jpg", "width": 550, "height": 550}}, "extInfo": {"categoryPath": "", "isLeaf": "0"}, "radioQQMusicId": 1700000000024, "oldId": 141, "radioQQMusicType": 0}]}, {"type": "Column", "code": "10000052", "title": "精品", "subtitle": "优选", "description": "", "imageFiles": {}, "extInfo": {}, "childColumns": null, "forwardToMore": 0, "moreColumnMember": null, "columnMembers": [{"type": "LiveProgramDetailColumnMember", "code": "10000061", "title": "朝圣之旅", "subtitle": "", "description": "奥迪朝圣之旅", "cornerMark": 0, "imageFiles": {"cover": {"url": "http://img.kaolafm.net/mz/images/201809/90beb4d8-2929-415b-b624-48e9675f80a1/550_550.jpg", "width": 550, "height": 550}}, "extInfo": {"categoryPath": "", "isLeaf": "0"}, "liveProgramId": 1555836869}, {"type": "AlbumDetailColumnMember", "code": "10000062", "title": "童话剧场", "subtitle": "", "description": "童话剧场近距离听故事", "cornerMark": 0, "imageFiles": {"cover": {"url": "http://img.kaolafm.net/mz/images/201809/a2d9de94-1b8b-4171-993b-2382e95d467d/550_550.jpg", "width": 550, "height": 550}}, "extInfo": {"categoryPath": "", "isLeaf": "0"}, "albumId": 1100002118071, "playTimes": 5215}, {"type": "AlbumDetailColumnMember", "code": "10000063", "title": "晓说 2018", "subtitle": "", "description": "倾听高晓松针对时下热点话题和舆论事件进行解说和点评，让你足不出户就能知晓天下事，喜欢脱口秀的朋友记得收听哦！", "cornerMark": 0, "imageFiles": {"cover": {"url": "http://img.kaolafm.net/mz/images/201802/24cd3973-25d1-4d7a-9bb2-f4eddce7fb2e/550_550.jpg", "width": 550, "height": 550}}, "extInfo": {"categoryPath": "", "isLeaf": "0"}, "albumId": 1100001736709, "playTimes": 457215867}]}]}, {"code": "10000045", "title": "头条", "subtitle": "", "description": "", "imageFiles": {"icon": {"url": "http://img.kaolafm.net/mz/images/201809/68bb6652-2f81-44e3-9029-5d8492a5eeaa/default.png", "width": 60, "height": 60}}, "extInfo": {}, "type": "ColumnGrp", "childColumns": [{"type": "Column", "code": "10000053", "title": "聚焦", "subtitle": "热点", "description": "", "imageFiles": {}, "extInfo": {}, "childColumns": null, "forwardToMore": 0, "moreColumnMember": null, "columnMembers": [{"type": "RadioDetailColumnMember", "code": "10000064", "title": "凤凰新闻", "subtitle": "", "description": "凤凰卫视、凤凰新闻", "cornerMark": 0, "imageFiles": {"cover": {"url": "http://img.kaolafm.net/mz/images/201807/f97baf0d-a761-47f8-a7fa-a8ea98cc54f6/550_550.jpg", "width": 550, "height": 550}}, "extInfo": {"categoryPath": "", "isLeaf": "0"}, "radioId": 1200000000507, "playTimes": null}, {"type": "RadioDetailColumnMember", "code": "10000065", "title": "社会新鲜事", "subtitle": "", "description": "资讯（娱乐,时政,财经,热点,体育）,健康,互联网,股票,音乐", "cornerMark": 0, "imageFiles": {"cover": {"url": "http://img.kaolafm.net/mz/images/201806/57d7c546-2fa3-46dd-98c3-0d00b2e9b7f3/550_550.jpg", "width": 550, "height": 550}}, "extInfo": {"categoryPath": "", "isLeaf": "0"}, "radioId": 1200000000501, "playTimes": null}, {"type": "RadioDetailColumnMember", "code": "10000066", "title": "环球时政", "subtitle": "", "description": "时政", "cornerMark": 0, "imageFiles": {"cover": {"url": "http://img.kaolafm.net/mz/images/201806/372c06f3-b497-412d-a5f8-2f66690c9ead/550_550.jpg", "width": 550, "height": 550}}, "extInfo": {"categoryPath": "", "isLeaf": "0"}, "radioId": 1200000000443, "playTimes": null}]}, {"type": "Column", "code": "10000054", "title": "资讯", "subtitle": "速递", "description": "", "imageFiles": {}, "extInfo": {}, "childColumns": null, "forwardToMore": 0, "moreColumnMember": null, "columnMembers": [{"type": "RadioDetailColumnMember", "code": "10000067", "title": "互联网焦点", "subtitle": "", "description": "互联网", "cornerMark": 0, "imageFiles": {"cover": {"url": "http://img.kaolafm.net/mz/images/201806/2376c005-7442-416c-8fc3-e418ffc92f92/550_550.jpg", "width": 550, "height": 550}}, "extInfo": {"categoryPath": "", "isLeaf": "0"}, "radioId": 1200000000441, "playTimes": 2}, {"type": "RadioDetailColumnMember", "code": "10000068", "title": "财经要闻", "subtitle": "", "description": "财经", "cornerMark": 0, "imageFiles": {"cover": {"url": "http://img.kaolafm.net/mz/images/201806/2068ff0a-b63a-46d3-8e7f-490ce41e8a68/550_550.jpg", "width": 550, "height": 550}}, "extInfo": {"categoryPath": "", "isLeaf": "0"}, "radioId": 1200000000442, "playTimes": null}, {"type": "RadioDetailColumnMember", "code": "10000069", "title": "汽车快讯", "subtitle": "", "description": "汽车类资讯", "cornerMark": 0, "imageFiles": {"cover": {"url": "http://img.kaolafm.net/mz/images/201806/ebd8fae9-925f-445d-b6c4-a86988fdeb35/550_550.jpg", "width": 550, "height": 550}}, "extInfo": {"categoryPath": "", "isLeaf": "0"}, "radioId": 1200000000445, "playTimes": null}]}, {"type": "Column", "code": "10000055", "title": "职场", "subtitle": "宝典", "description": "", "imageFiles": {}, "extInfo": {}, "childColumns": null, "forwardToMore": 0, "moreColumnMember": null, "columnMembers": [{"type": "RadioDetailColumnMember", "code": "10000070", "title": "知识风向标", "subtitle": "", "description": "热点,财经,互联网,创投,AI,时政,走近科学,英语,区块链", "cornerMark": 0, "imageFiles": {"cover": {"url": "http://img.kaolafm.net/mz/images/201806/0b98fa76-0437-4b2d-ae59-efb7f30a67dc/550_550.jpg", "width": 550, "height": 550}}, "extInfo": {"categoryPath": "", "isLeaf": "0"}, "radioId": 1200000000506, "playTimes": null}, {"type": "RadioDetailColumnMember", "code": "10000071", "title": "职场加油包", "subtitle": "", "description": "靠近梦想，成就自己", "cornerMark": 0, "imageFiles": {"cover": {"url": "http://img.kaolafm.net/mz/images/201806/7b3f2205-dde4-4879-9b0a-aca079dde0a2/550_550.jpg", "width": 550, "height": 550}}, "extInfo": {"categoryPath": "", "isLeaf": "0"}, "radioId": 1200000000472, "playTimes": null}, {"type": "RadioDetailColumnMember", "code": "10000072", "title": "英语show", "subtitle": "", "description": "英语教育", "cornerMark": 0, "imageFiles": {"cover": {"url": "http://img.kaolafm.net/mz/images/201806/e569fb1f-741f-4ffd-9ca5-cfcdbb3e16be/550_550.jpg", "width": 550, "height": 550}}, "extInfo": {"categoryPath": "", "isLeaf": "0"}, "radioId": 1200000000449, "playTimes": 1}]}]}, {"code": "10000046", "title": "音乐", "subtitle": "", "description": "", "imageFiles": {"icon": {"url": "http://img.kaolafm.net/mz/images/201809/f0bd4ddd-4871-44ea-a7aa-fa7d93d5b798/default.png", "width": 60, "height": 60}}, "extInfo": {}, "type": "ColumnGrp", "childColumns": [{"type": "Column", "code": "10000062", "title": "音乐", "subtitle": "我的", "description": "", "imageFiles": {}, "extInfo": {"appControlType": "MyMusic"}, "childColumns": null, "forwardToMore": 0, "moreColumnMember": null, "columnMembers": []}, {"type": "Column", "code": "10000063", "title": "最热", "subtitle": "榜单", "description": "", "imageFiles": {}, "extInfo": {}, "childColumns": null, "forwardToMore": 1, "moreColumnMember": {"type": "CategoryColumnMember", "code": "10000141", "title": "更多跳转", "subtitle": "", "description": "", "cornerMark": 0, "imageFiles": {"cover": {"url": "http://img.kaolafm.net/mz/images/201809/83534adc-1515-4d42-a6e8-1aac8a5f08f1/default.jpg", "width": 550, "height": 550}}, "extInfo": {"categoryPath": "10000327/10000328", "isLeaf": "1"}, "categoryCode": "10000328", "contentType": 5}, "columnMembers": [{"type": "RadioQQMusicDetailColumnMember", "code": "10000087", "title": "华语新锐榜", "subtitle": "", "description": "", "cornerMark": 0, "imageFiles": {"cover": {"url": "http://img.kaolafm.net/mz/images/201804/a4831c92-3bdb-11e8-9b86-c81f66bca8d3/550_550.jpg", "width": 550, "height": 550}}, "extInfo": {"categoryPath": "", "isLeaf": "0"}, "radioQQMusicId": 1700000000005, "oldId": 118, "radioQQMusicType": 0}, {"type": "RadioQQMusicDetailColumnMember", "code": "10000088", "title": "新歌抢先听", "subtitle": "", "description": "", "cornerMark": 0, "imageFiles": {"cover": {"url": "http://img.kaolafm.net/mz/images/201805/202bbd92-5359-11e8-b680-c81f66bca8d3/550_550.jpg", "width": 550, "height": 550}}, "extInfo": {"categoryPath": "", "isLeaf": "0"}, "radioQQMusicId": 1700000000192, "oldId": 3225, "radioQQMusicType": 1}, {"type": "RadioQQMusicDetailColumnMember", "code": "10000089", "title": "流行in乐圈", "subtitle": "", "description": "", "cornerMark": 0, "imageFiles": {"cover": {"url": "http://img.kaolafm.net/mz/images/201807/947970cc-8428-11e8-b764-c81f66bca8d3/550_550.jpg", "width": 550, "height": 550}}, "extInfo": {"categoryPath": "", "isLeaf": "0"}, "radioQQMusicId": 1700000000284, "oldId": 3152, "radioQQMusicType": 1}]}, {"type": "Column", "code": "10000064", "title": "复古", "subtitle": "经典", "description": "", "imageFiles": {}, "extInfo": {}, "childColumns": null, "forwardToMore": 0, "moreColumnMember": null, "columnMembers": [{"type": "RadioQQMusicDetailColumnMember", "code": "10000090", "title": "一人一首招牌歌", "subtitle": "", "description": "", "cornerMark": 0, "imageFiles": {"cover": {"url": "http://img.kaolafm.net/mz/images/201804/1d370e5a-3bdc-11e8-9b86-c81f66bca8d3/550_550.jpg", "width": 550, "height": 550}}, "extInfo": {"categoryPath": "", "isLeaf": "0"}, "radioQQMusicId": 1700000000059, "oldId": 307, "radioQQMusicType": 0}, {"type": "RadioQQMusicDetailColumnMember", "code": "10000091", "title": "历年经典", "subtitle": "", "description": "", "cornerMark": 0, "imageFiles": {"cover": {"url": "http://img.kaolafm.net/mz/images/201805/7c27cdf2-5354-11e8-9ce0-c81f66bca8d3/550_550.jpg", "width": 550, "height": 550}}, "extInfo": {"categoryPath": "", "isLeaf": "0"}, "radioQQMusicId": 1700000000123, "oldId": 21, "radioQQMusicType": 1}, {"type": "RadioQQMusicDetailColumnMember", "code": "10000092", "title": "KTV必点歌", "subtitle": "", "description": "", "cornerMark": 0, "imageFiles": {"cover": {"url": "http://img.kaolafm.net/mz/images/201804/0fe8011e-3bdc-11e8-9b86-c81f66bca8d3/550_550.jpg", "width": 550, "height": 550}}, "extInfo": {"categoryPath": "", "isLeaf": "0"}, "radioQQMusicId": 1700000000053, "oldId": 270, "radioQQMusicType": 0}]}]}, {"code": "10000047", "title": "综艺", "subtitle": "", "description": "", "imageFiles": {"icon": {"url": "http://img.kaolafm.net/mz/images/201809/cded9cf9-cc92-46eb-9e65-9ca9944a6d8b/default.png", "width": 60, "height": 60}}, "extInfo": {}, "type": "ColumnGrp", "childColumns": [{"type": "Column", "code": "10000056", "title": "搞笑", "subtitle": "娱乐", "description": "", "imageFiles": {}, "extInfo": {}, "childColumns": null, "forwardToMore": 0, "moreColumnMember": null, "columnMembers": [{"type": "RadioDetailColumnMember", "code": "10000073", "title": "涩会段子", "subtitle": "", "description": "涩会段子", "cornerMark": 0, "imageFiles": {"cover": {"url": "http://img.kaolafm.net/mz/images/201806/0223e153-496f-4cfb-9364-39c40ca6d9dd/550_550.jpg", "width": 550, "height": 550}}, "extInfo": {"categoryPath": "", "isLeaf": "0"}, "radioId": 1200000000451, "playTimes": null}, {"type": "RadioDetailColumnMember", "code": "10000074", "title": "娱乐八卦", "subtitle": "", "description": "娱乐", "cornerMark": 0, "imageFiles": {"cover": {"url": "http://img.kaolafm.net/mz/images/201806/4dfa50b4-c184-4d48-9577-b16e4e67052d/550_550.jpg", "width": 550, "height": 550}}, "extInfo": {"categoryPath": "", "isLeaf": "0"}, "radioId": 1200000000444, "playTimes": 1}, {"type": "RadioDetailColumnMember", "code": "10000075", "title": "传统相声", "subtitle": "", "description": "相声", "cornerMark": 0, "imageFiles": {"cover": {"url": "http://img.kaolafm.net/mz/images/201806/32b330ca-9a4b-4d07-b1c5-c90d14a8d28f/550_550.jpg", "width": 550, "height": 550}}, "extInfo": {"categoryPath": "", "isLeaf": "0"}, "radioId": 1200000000457, "playTimes": 1}]}, {"type": "Column", "code": "10000057", "title": "博闻", "subtitle": "天下", "description": "", "imageFiles": {}, "extInfo": {}, "childColumns": null, "forwardToMore": 0, "moreColumnMember": null, "columnMembers": [{"type": "RadioDetailColumnMember", "code": "10000076", "title": "历史漫谈", "subtitle": "", "description": "历史", "cornerMark": 0, "imageFiles": {"cover": {"url": "http://img.kaolafm.net/mz/images/201806/d8641b11-7c64-4109-8299-9e5426236726/550_550.jpg", "width": 550, "height": 550}}, "extInfo": {"categoryPath": "", "isLeaf": "0"}, "radioId": 1200000000454, "playTimes": null}, {"type": "RadioDetailColumnMember", "code": "10000077", "title": "带你看世界", "subtitle": "", "description": "旅游美食,生活,音乐", "cornerMark": 0, "imageFiles": {"cover": {"url": "http://img.kaolafm.net/mz/images/201806/ae5193e3-2970-4980-a338-5aba47deaec8/550_550.jpg", "width": 550, "height": 550}}, "extInfo": {"categoryPath": "", "isLeaf": "0"}, "radioId": 1200000000504, "playTimes": 6}, {"type": "RadioDetailColumnMember", "code": "10000078", "title": "诗词歌赋", "subtitle": "", "description": "诗词歌赋", "cornerMark": 0, "imageFiles": {"cover": {"url": "http://img.kaolafm.net/mz/images/201806/c313d365-d778-432b-be2b-a07e20907c5b/550_550.jpg", "width": 550, "height": 550}}, "extInfo": {"categoryPath": "", "isLeaf": "0"}, "radioId": 1200000000458, "playTimes": null}]}]}, {"code": "10000048", "title": "专栏", "subtitle": "", "description": "", "imageFiles": {"icon": {"url": "http://img.kaolafm.net/mz/images/201809/ba5bb317-2021-4dcb-be0c-3b296cc3337c/default.png", "width": 60, "height": 60}}, "extInfo": {}, "type": "ColumnGrp", "childColumns": [{"type": "Column", "code": "10000059", "title": "深度", "subtitle": "聆听", "description": "", "imageFiles": {}, "extInfo": {}, "childColumns": null, "forwardToMore": 0, "moreColumnMember": null, "columnMembers": [{"type": "AlbumDetailColumnMember", "code": "10000082", "title": "老梁寻根", "subtitle": "", "description": "铁嘴老梁隐匿数月重装上阵", "cornerMark": 0, "imageFiles": {"cover": {"url": "http://img.kaolafm.net/mz/images/201708/22964593-2a93-4704-b33c-09f334854c55/550_550.jpg", "width": 550, "height": 550}}, "extInfo": {"categoryPath": "", "isLeaf": "0"}, "albumId": 1100000510636, "playTimes": 13679768}, {"type": "AlbumDetailColumnMember", "code": "10000083", "title": "历史回声", "subtitle": "", "description": "用今天的视角讲述陌生的故事", "cornerMark": 0, "imageFiles": {"cover": {"url": "http://img.kaolafm.net/mz/images/201708/9797c5af-9363-47db-8990-fd1caf36db98/550_550.jpg", "width": 550, "height": 550}}, "extInfo": {"categoryPath": "", "isLeaf": "0"}, "albumId": 1100000517498, "playTimes": 113566880}, {"type": "AlbumDetailColumnMember", "code": "10000084", "title": "追根溯源", "subtitle": "", "description": "探求一切事物根源，刨根问底", "cornerMark": 0, "imageFiles": {"cover": {"url": "http://img.kaolafm.net/mz/images/201804/bda840fd-8b1a-469e-8cf1-695d7886135e/550_550.jpg", "width": 550, "height": 550}}, "extInfo": {"categoryPath": "", "isLeaf": "0"}, "albumId": 1100001981791, "playTimes": 1147068}, {"type": "AlbumDetailColumnMember", "code": "10000085", "title": "品读论语", "subtitle": "", "description": "闻圣人言教，悟百态人生，享幸福生活。", "cornerMark": 0, "imageFiles": {"cover": {"url": "http://img.kaolafm.net/mz/images/201807/1874378f-0522-4a5d-871b-fbb80a264ccd/550_550.jpg", "width": 550, "height": 550}}, "extInfo": {"categoryPath": "", "isLeaf": "0"}, "albumId": 1100002107939, "playTimes": 153}, {"type": "AlbumDetailColumnMember", "code": "10000086", "title": "色彩心理学", "subtitle": "", "description": "了解色彩心理学，点亮生活", "cornerMark": 0, "imageFiles": {"cover": {"url": "http://img.kaolafm.net/mz/images/201806/647dea96-633a-4513-a8a0-bc432a60d122/550_550.jpg", "width": 550, "height": 550}}, "extInfo": {"categoryPath": "", "isLeaf": "0"}, "albumId": 1100002107436, "playTimes": 169}]}]}, {"code": "10000049", "title": "广播", "subtitle": "", "description": "", "imageFiles": {"icon": {"url": "http://img.kaolafm.net/mz/images/201809/93724d9c-5435-4228-9dc7-ed625c77df4a/default.png", "width": 60, "height": 60}}, "extInfo": {}, "type": "ColumnGrp", "childColumns": [{"type": "Column", "code": "10000060", "title": "最近", "subtitle": "常听", "description": "", "imageFiles": {}, "extInfo": {"appControlType": "LatestBroadcast"}, "childColumns": null, "forwardToMore": 0, "moreColumnMember": null, "columnMembers": []}, {"type": "Column", "code": "10000061", "title": "广播", "subtitle": "优选", "description": "", "imageFiles": {}, "extInfo": {}, "childColumns": null, "forwardToMore": 0, "moreColumnMember": null, "columnMembers": [{"type": "BroadcastDetailColumnMember", "code": null, "title": "惠州新闻综合广播阳光100", "subtitle": null, "description": null, "cornerMark": null, "imageFiles": {"icon": {"url": "http://img.kaolafm.net/mz/images/201512/e7573b13-524f-4265-8c9e-2dda18a0d14d/default.jpg", "width": null, "height": null}}, "extInfo": null, "broadcastId": 1600000000489, "playTimes": 28942}, {"type": "BroadcastDetailColumnMember", "code": null, "title": "安徽新闻综合广播 安徽之声", "subtitle": null, "description": null, "cornerMark": null, "imageFiles": {"icon": {"url": "http://img.kaolafm.net/mz/images/201604/8f5abf71-5e6e-48b8-a5f4-09b6274d0010/default.jpg", "width": null, "height": null}}, "extInfo": null, "broadcastId": 1600000000298, "playTimes": 55661}, {"type": "BroadcastDetailColumnMember", "code": null, "title": "郑州新闻广播", "subtitle": null, "description": null, "cornerMark": null, "imageFiles": {"icon": {"url": "http://img.kaolafm.net/mz/images/201512/2642de25-faaf-4443-b2ae-ffefb7ab3011/default.jpg", "width": null, "height": null}}, "extInfo": null, "broadcastId": 1600000000062, "playTimes": 54954}, {"type": "BroadcastDetailColumnMember", "code": null, "title": "央广中国乡村广播", "subtitle": null, "description": null, "cornerMark": null, "imageFiles": {"icon": {"url": "http://img.kaolafm.net/mz/images/201612/a38f414b-346e-4c18-9d3c-569aa97c8d71/default.jpg", "width": null, "height": null}}, "extInfo": null, "broadcastId": 1600000000329, "playTimes": 36286}, {"type": "BroadcastDetailColumnMember", "code": null, "title": "央广中国之声", "subtitle": null, "description": null, "cornerMark": null, "imageFiles": {"icon": {"url": "http://img.kaolafm.net/mz/images/201608/617b7348-1d42-4fc1-820b-b8ce30f26999/default.jpg", "width": null, "height": null}}, "extInfo": null, "broadcastId": 1600000000510, "playTimes": 3972743}, {"type": "BroadcastDetailColumnMember", "code": null, "title": "河北新闻广播", "subtitle": null, "description": null, "cornerMark": null, "imageFiles": {"icon": {"url": "http://img.kaolafm.net/mz/images/201512/5cf47c2b-8376-4c39-be4b-dd2304230e38/default.jpg", "width": null, "height": null}}, "extInfo": null, "broadcastId": 1600000000359, "playTimes": 205732}, {"type": "BroadcastDetailColumnMember", "code": null, "title": "山东新闻综合广播", "subtitle": null, "description": null, "cornerMark": null, "imageFiles": {"icon": {"url": "http://img.kaolafm.net/mz/images/201512/dd93f5b0-91c2-462f-a543-45163ecbfad5/default.jpg", "width": null, "height": null}}, "extInfo": null, "broadcastId": 1600000000095, "playTimes": 82150}, {"type": "BroadcastDetailColumnMember", "code": null, "title": "江苏新闻广播", "subtitle": null, "description": null, "cornerMark": null, "imageFiles": {"icon": {"url": "http://img.kaolafm.net/mz/images/201512/32e178a0-4f3d-40cd-a535-37fd6663d6e4/default.jpg", "width": null, "height": null}}, "extInfo": null, "broadcastId": 1600000000170, "playTimes": 128768}, {"type": "BroadcastDetailColumnMember", "code": null, "title": "北京新闻广播", "subtitle": null, "description": null, "cornerMark": null, "imageFiles": {"icon": {"url": "http://img.kaolafm.net/mz/images/201607/ddfac5d6-9d41-4eba-9c6e-6c3f8606528c/default.jpg", "width": null, "height": null}}, "extInfo": null, "broadcastId": 1600000000451, "playTimes": 224111}, {"type": "BroadcastDetailColumnMember", "code": null, "title": "陕西新闻广播", "subtitle": null, "description": null, "cornerMark": null, "imageFiles": {"icon": {"url": "http://img.kaolafm.net/mz/images/201607/7551463d-7f18-4fec-bdf9-35a1e6add2b4/default.jpg", "width": null, "height": null}}, "extInfo": null, "broadcastId": 1600000000479, "playTimes": 40112}, {"type": "BroadcastDetailColumnMember", "code": null, "title": "四川新闻广播", "subtitle": null, "description": null, "cornerMark": null, "imageFiles": {"icon": {"url": "http://img.kaolafm.net/mz/images/201612/fc41fa77-e127-4643-a85c-3b866b8f3d5b/default.jpg", "width": null, "height": null}}, "extInfo": null, "broadcastId": 1600000000756, "playTimes": 33078}, {"type": "BroadcastDetailColumnMember", "code": null, "title": "济南新闻广播", "subtitle": null, "description": null, "cornerMark": null, "imageFiles": {"icon": {"url": "http://img.kaolafm.net/mz/images/201511/58936d1d-1c3f-41e8-8916-db84a1b052e9/default.jpg", "width": null, "height": null}}, "extInfo": null, "broadcastId": 1600000000055, "playTimes": 107532}]}]}]