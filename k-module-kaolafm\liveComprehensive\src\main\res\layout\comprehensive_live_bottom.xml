<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/live_bottom"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/message_badge_view"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginLeft="@dimen/m180"
        android:layout_marginBottom="@dimen/comprehensive_live_bottom_msg_margin_bottom"
        android:layout_width="@dimen/comprehensive_live_bottom_msg_badge_width"
        android:layout_height="@dimen/comprehensive_live_bottom_msg_badge_width"
        android:contentDescription="@string/content_desc_message"
        android:src="@drawable/comprehensive_message_badge_view" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/live_bottom_ability"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginHorizontal="@dimen/m328"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/comprehensive_live_bottom_height"
        android:orientation="horizontal"/>

<!--    <RelativeLayout-->
<!--        android:id="@+id/live_bottom_gift"-->
<!--        app:layout_constraintTop_toTopOf="parent"-->
<!--        app:layout_constraintLeft_toLeftOf="parent"-->
<!--        app:layout_constraintRight_toRightOf="parent"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        android:layout_marginTop="@dimen/comprehensive_live_bottom_margin_top"-->
<!--        android:layout_marginBottom="@dimen/comprehensive_live_bottom_margin_bottom"-->
<!--        android:layout_width="@dimen/comprehensive_live_bottom_item_width"-->
<!--        android:layout_height="@dimen/comprehensive_live_bottom_item_width"-->
<!--        android:gravity="center"-->
<!--        android:background="@drawable/comprehensive_live_bottom_btn_bg">-->

<!--        <ImageView-->
<!--            android:layout_width="@dimen/comprehensive_live_bottom_item_img_width"-->
<!--            android:layout_height="@dimen/comprehensive_live_bottom_item_img_width"-->
<!--            android:scaleType="fitXY"-->
<!--            android:src="@drawable/comprehensive_live_bottom_gift_img"/>-->

<!--    </RelativeLayout>-->

<!--    <RelativeLayout-->
<!--        android:id="@+id/live_bottom_record"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:layout_constraintRight_toLeftOf="@id/live_bottom_gift"-->
<!--        android:layout_marginBottom="@dimen/comprehensive_live_bottom_margin_bottom"-->
<!--        android:layout_marginRight="@dimen/x68"-->
<!--        android:layout_width="@dimen/comprehensive_live_bottom_item_width"-->
<!--        android:layout_height="@dimen/comprehensive_live_bottom_item_width"-->
<!--        android:gravity="center"-->
<!--        android:background="@drawable/comprehensive_live_bottom_btn_bg">-->

<!--        <ImageView-->
<!--            android:layout_width="@dimen/comprehensive_live_bottom_item_img_width"-->
<!--            android:layout_height="@dimen/comprehensive_live_bottom_item_img_width"-->
<!--            android:scaleType="fitXY"-->
<!--            android:src="@drawable/comprehensive_live_bottom_record_img"/>-->

<!--    </RelativeLayout>-->

<!--    <RelativeLayout-->
<!--        android:id="@+id/live_bottom_shop"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:layout_constraintLeft_toRightOf="@id/live_bottom_gift"-->
<!--        android:layout_marginBottom="@dimen/comprehensive_live_bottom_margin_bottom"-->
<!--        android:layout_marginLeft="@dimen/x68"-->
<!--        android:layout_width="@dimen/comprehensive_live_bottom_item_width"-->
<!--        android:layout_height="@dimen/comprehensive_live_bottom_item_width"-->
<!--        android:gravity="center"-->
<!--        android:background="@drawable/comprehensive_live_bottom_btn_bg">-->

<!--        <ImageView-->
<!--            android:layout_width="@dimen/comprehensive_live_bottom_item_img_width"-->
<!--            android:layout_height="@dimen/comprehensive_live_bottom_item_img_width"-->
<!--            android:scaleType="fitXY"-->
<!--            android:src="@drawable/comprehensive_live_bottom_goods_img"/>-->

<!--    </RelativeLayout>-->

</androidx.constraintlayout.widget.ConstraintLayout>