package com.kaolafm.kradio.search;

import com.kaolafm.kradio.lib.base.mvp.IView;
import com.kaolafm.opensdk.api.personalise.model.HotRecommend;
import com.kaolafm.opensdk.api.search.model.SearchProgramBean;

import java.util.List;

public interface ISearchView extends IView {
    void showAssociateWordsView(List<AssociateInfo> associateInfos);
    void showSearchResultView(List<SearchProgramBean> searchProgramBeans);
    void showNoNetView();
    void showNoResultView();
    void showLoadingView();
    void showErrorView(int errorCode);
    void showHotSearchWordsView(List<String> hotWords);
    void setTypeData(List<SearchType> searchTypes);
    void showSearchTypeNetworkError(String error);
    void showNetworkError(String error, boolean clickToRetry);
    void showHotRecommend(HotRecommend hotRecommend);
}
