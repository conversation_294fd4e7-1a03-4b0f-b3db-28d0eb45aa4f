<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/broadcast_play_list_main_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/colorBlack">

    <com.kaolafm.kradio.lib.widget.tab.SlidingTabLayout
        android:id="@+id/broadcast_play_list_indicator"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/comprehensive_player_broadcast_slidingtab_height"
        android:layout_marginStart="@dimen/m30"
        app:kradio_tl_textSelectSize="@dimen/text_size5"
        app:kradio_tl_textSize="@dimen/text_size5"
        app:tl_textSelectSize="@dimen/text_size5"
        app:tl_textSize="@dimen/text_size5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tl_first_no_padding="true"
        app:tl_indicator_color="@color/sliding_tab_text_selected"
        app:tl_indicator_height="@dimen/m2"
        app:tl_indicator_margin_bottom="0dp"
        app:tl_indicator_style="NORMAL"
        app:tl_indicator_width_equal_title="true"
        app:tl_tab_padding="@dimen/x60"
        app:tl_textBold="SELECT"
        app:tl_textSelectColor="@color/sliding_tab_text_selected"
        app:tl_textUnselectColor="@color/sliding_tab_text_unselected" />

    <skin.support.widget.SkinCompatView
        android:layout_width="0dp"
        android:layout_height="@dimen/y1"
        android:layout_marginStart="@dimen/x30"
        android:layout_marginEnd="@dimen/x30"
        android:background="@color/comprehensive_playlist_divider"
        app:layout_constraintEnd_toEndOf="@id/broadcast_play_list_viewPager"
        app:layout_constraintStart_toStartOf="@id/broadcast_play_list_viewPager"
        app:layout_constraintTop_toBottomOf="@id/broadcast_play_list_indicator" />

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/broadcast_play_list_viewPager"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/y101"
        android:descendantFocusability="beforeDescendants"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>