package com.kaolafm.kradio.lib.base.adapter;

import androidx.recyclerview.widget.RecyclerView;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-04-11 11:18
 ******************************************/

public interface BaseViewHolderUpdateListener<T> {
    void updateData(T t, int position);

    void onViewRecycled(RecyclerView.ViewHolder holder);
}
