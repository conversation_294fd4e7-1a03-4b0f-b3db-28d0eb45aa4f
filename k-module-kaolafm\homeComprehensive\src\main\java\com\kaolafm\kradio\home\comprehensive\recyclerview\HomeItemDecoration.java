package com.kaolafm.kradio.home.comprehensive.recyclerview;

import android.content.res.Configuration;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import androidx.legacy.widget.Space;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.Adapter;
import androidx.recyclerview.widget.RecyclerView.State;
import android.text.TextPaint;
import android.view.View;

import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.home.comprehensive.adapter.HomeAdapter;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.home.comprehensive.item.SpaceCell;
import com.kaolafm.kradio.lib.common.ResType;
import com.kaolafm.kradio.lib.utils.ResUtil;

/**
 * 这里做了item的间隙，还在card的第一个cell上面画了标题。
 *
 * <AUTHOR>
 * @date 2019-08-18
 */
public class HomeItemDecoration extends RecyclerView.ItemDecoration {

    private String TAG = "HomeItemDecoration";

    private TextPaint textPaint;

    public HomeItemDecoration() {
        textPaint = new TextPaint();
        textPaint.setAntiAlias(true);
        textPaint.setTextAlign(Paint.Align.LEFT);
    }

    @Override
    public void onDraw(Canvas c, RecyclerView parent, State state) {
        super.onDraw(c, parent, state);
        HomeAdapter adapter = (HomeAdapter) parent.getAdapter();
        for (int i = 0; i < parent.getChildCount(); i++) {
            View view = parent.getChildAt(i);
            int position = parent.getChildAdapterPosition(view);
            HomeCell cell = adapter.getItemData(position);
            if (cell instanceof SpaceCell) {
                continue;
            }
            //212版本需求去掉列表的标题
//            if (cell != null && cell.positionInParent == 0) {
//                RecyclerView.LayoutParams layoutParams = (RecyclerView.LayoutParams) view.getLayoutParams();
//                int left = view.getLeft();
//                int bottom = view.getTop() - layoutParams.topMargin;
//                textPaint.setTextSize(ResUtil.getDimen(R.dimen.home_item_title_size));
////                textPaint.setColor(ResUtil.getColor(R.color.text_color_white));
//                textPaint.setColor(ResUtil.getColor(R.color.home_item_title_text_color));
//                Rect rect = new Rect();
//                String title = cell.parent.title;
//                String subtitle = cell.parent.subtitle;
//                title = title + subtitle;
//                textPaint.getTextBounds(title, 0, title.length(), rect);
//                int top = bottom - ResUtil.getDimen(R.dimen.home_item_title_space_v);
//                c.drawText(title, left, top, textPaint);
//
////                textPaint.setColor(ResUtil.getColor(R.color.home_item_sub_title_text_color));
////                textPaint.setTextSize(ResUtil.getDimen(R.dimen.home_item_subtitle_size));
////                left = left + rect.width() + ResUtil.getDimen(R.dimen.home_item_title_space_h);
////                textPaint.getTextBounds(subtitle, 0, subtitle.length(), rect);
////                c.drawText(subtitle, left, top, textPaint);
//            }
        }
    }

    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, State state) {
        super.getItemOffsets(outRect, view, parent, state);
        if (view instanceof Space) {
            return;
        }
        int orientation = ResUtil.getOrientation();
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            //outRect.bottom = ResUtil.getDimen(R.dimen.x6);
            outRect.right = ResUtil.getDimen(R.dimen.m30);
            int childAdapterPosition = parent.getChildAdapterPosition(view);
            Adapter adapter = parent.getAdapter();
            if (adapter instanceof HomeAdapter) {
                HomeCell itemData = ((HomeAdapter) adapter).getItemData(childAdapterPosition);
                if (itemData != null && itemData.isLastInParent) {
                    outRect.right = ResUtil.getDimen(R.dimen.m80);
                }
//                if (childAdapterPosition==0){
//                    outRect.left = ResUtil.getDimen(R.dimen.m80);
//                }
                if (childAdapterPosition == 0 && (itemData.itemType == ResType.HOME_ITEM_TYPE_BRAND ||
                        itemData.itemType == ResType.HOME_ITEM_TYPE_BRAND_HOME)) {
                    outRect.right = ResUtil.getDimen(R.dimen.m64);
                    outRect.left = ResUtil.getDimen(R.dimen.m40);
                }
            }
        } else {
            outRect.right = ResUtil.getDimen(R.dimen.x15);
            outRect.top = ResUtil.getDimen(R.dimen.home_item_space_top);
        }
    }
}
