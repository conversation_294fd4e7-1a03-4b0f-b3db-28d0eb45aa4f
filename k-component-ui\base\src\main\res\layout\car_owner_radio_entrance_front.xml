<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/car_owner_radio_card_front"
    android:layout_width="@dimen/m340"
    android:layout_height="@dimen/m340"
    android:layout_gravity="center"
    android:background="@drawable/car_owner_radio_center_bg">

    <com.kaolafm.kradio.component.ui.base.view.OvalImageView
        android:id="@+id/radio_front_cover"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:circle="true"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/radio_mask"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@drawable/car_owner_radio_entrance_shade"/>

    <com.kaolafm.kradio.component.ui.base.view.OvalImageView
        android:id="@+id/radio_logo"
        app:layout_constraintBottom_toTopOf="@id/radio_name"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginBottom="@dimen/m36"
        android:layout_width="@dimen/m74"
        android:layout_height="@dimen/m74"
        app:circle="true"
        android:src="@drawable/car_owner_radio_entrance_logo"/>

    <com.kaolafm.kradio.component.ui.base.view.KradioTextView
        android:id="@+id/radio_name"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textSize="@dimen/m32"
        android:textStyle="bold"
        app:kt_gradient_angle="90"
        app:kt_start_color="@color/car_owner_radio_name_text_start_color"
        app:kt_end_color="@color/car_owner_radio_name_text_end_color"
        android:text="- 车主电台名称 -"/>

    <ImageView
        android:id="@+id/radio_car"
        android:layout_width="@dimen/m180"
        android:layout_height="@dimen/m180"
        android:layout_marginTop="@dimen/m213"
        android:src="@drawable/car_owner_radio_entrance_car"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>