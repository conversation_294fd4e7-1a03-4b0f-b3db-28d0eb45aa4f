package com.kaolafm.kradio.common.widget;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;

import com.kaolafm.kradio.k_kaolafm.R;
import com.lcodecore.tkrefreshlayout.IBottomView;

import java.lang.ref.WeakReference;
import java.util.concurrent.atomic.AtomicBoolean;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2017-09-07 08:27
 ******************************************/

public final class CustomerRefreshBottom implements IBottomView {
    //    private TextView mRefreshTextView;
    private WeakReference<Context> mWeakReference;

    private OnVisibilityChanged mOnVisibilityChanged;
    private AtomicBoolean isVisibility = new AtomicBoolean(false);

    public CustomerRefreshBottom(Context context) {
        mWeakReference = new WeakReference<>(context);
    }

    @Override
    public View getView() {
        Context context = mWeakReference.get();
        if (context == null) {
            return null;
        }
        LayoutInflater layoutInflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View refreshLayout = layoutInflater.inflate(R.layout.refresh_bottom, null);
//        mRefreshTextView = (TextView) refreshLayout.findViewById(R.id.refresh_bottom_textView);
        return refreshLayout;
    }

    @Override
    public void onPullingUp(float fraction, float maxBottomHeight, float bottomHeight) {
        if (isVisibility.compareAndSet(false, true)) {
            if (mOnVisibilityChanged != null) {
                mOnVisibilityChanged.onVisibilityChanged(true);
            }
        }
    }

    @Override
    public void startAnim(float maxBottomHeight, float bottomHeight) {
    }

    @Override
    public void onPullReleasing(float fraction, float maxBottomHeight, float bottomHeight) {
        if (fraction == 0 && isVisibility.compareAndSet(true, false)) {
            if (mOnVisibilityChanged != null) {
                mOnVisibilityChanged.onVisibilityChanged(false);
            }
        }
    }

    @Override
    public void onFinish() {
        if (isVisibility.compareAndSet(true, false)) {
            if (mOnVisibilityChanged != null) {
                mOnVisibilityChanged.onVisibilityChanged(false);
            }
        }
    }

    @Override
    public void reset() {

    }

    public void setRefreshText(String text) {
//        mRefreshTextView.setText(text);
    }

    public void setRefreshText(int resId) {
//        mRefreshTextView.setText(resId);
    }

    public OnVisibilityChanged getOnVisibilityChanged() {
        return mOnVisibilityChanged;
    }

    public void setOnVisibilityChanged(OnVisibilityChanged mOnVisibilityChanged) {
        this.mOnVisibilityChanged = mOnVisibilityChanged;
    }

    public interface OnVisibilityChanged {
        void onVisibilityChanged(boolean visible);
    }

}
