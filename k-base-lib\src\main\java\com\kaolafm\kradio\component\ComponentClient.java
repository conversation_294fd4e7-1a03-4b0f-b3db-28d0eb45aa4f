package com.kaolafm.kradio.component;

import android.app.Activity;
import android.app.Application;
import androidx.fragment.app.Fragment;
import com.kaolafm.kradio.lib.utils.ObjPool;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 组件调用中心
 * <AUTHOR>
 * @date 2019-06-28
 */
public class ComponentClient {

    /**
     * 使用对象池复用Builder对象。
     */
    private static final ObjPool<Builder, String> BUILDER_POOL = new ObjPool<Builder, String>() {
        @Override
        protected Builder newInstance(String componentName) {
            return new Builder();
        }
    };

    private static Application mApplication;

    /**
     * 传递的参数
     */
    private final Map<String, Object> params = new HashMap<>();

    private String mComponentName;

    private String actionName;

    private RealCaller mCaller = new RealCaller(this);

    private Dispatcher mDispatcher = Dispatcher.getInstance();

    private boolean isCallbackOnMainThread;

    private List<ComponentInterceptor> interceptors = new ArrayList<>();

    /**
     * 在Activity销毁时取消
     */
    private WeakReference<Activity> cancelOnDestroyActivity;

    /**
     * 在fragment销毁时取消
     */
    private WeakReference<Fragment> cancelOnDestroyFragment;

    /**
     * 超时时间。默认值(同步：2000ms, 异步：0ms)
     */
    private long mTimeout = -1;

    private ComponentClient(String componentName) {
        mComponentName = componentName;
    }


    public static synchronized void init(Application application) {
        mApplication = application;
    }


    public static Builder obtainBuilder(String componentName) {
        return BUILDER_POOL.get(componentName);
    }

    boolean isCallbackOnMainThread() {
        return isCallbackOnMainThread;
    }

    /**
     * 拦截器
     * @return
     */
    List<ComponentInterceptor> interceptors() {
        return interceptors;
    }

    /**
     * 超时时间
     * @return
     */
    long timeout() {
        return mTimeout;
    }

    WeakReference<Activity> cancelOnDestroyActivity() {
        return cancelOnDestroyActivity;
    }

    WeakReference<Fragment> cancelOnDestroyFragment() {
        return cancelOnDestroyFragment;
    }

    public void callAsync() {
        callAsync(null);
    }
    public void callAsync(ComponentCallback callback) {
        mCaller.call(callback);
    }

    public ComponentResult call() {
        return mCaller.call();
    }

    private void setCallId(String callId) {
        mCaller.setCallId(callId);
    }

    public String componentName() {
        return mComponentName;
    }

    public Application application() {
        return mApplication;
    }

    public String actionName() {
        return actionName;
    }

    public Dispatcher dispatcher() {
        return mDispatcher;
    }

    public Map<String, Object> getParams() {
        return params;
    }

    /**
     * 取消请求。
     */
    public void cancel() {
        mCaller.cancelOnDestroy();
    }

    public static void sendResult(String callId, ComponentResult result) {
        RealCaller.sendResult(callId, result);
    }

    /**
     * 手动注册Component
     * @param component
     */
    public static void registerComponent(DynamicComponent component) {
        if (component != null) {
            ComponentManager.registerComponent(component);
        }
    }

    /**
     * 注销手动注册的Component
     * @param component
     */
    public static void unregisterComponent(DynamicComponent component) {
        if (component != null) {
            ComponentManager.unregisterComponent(component);
        }
    }

    public static class Builder implements ObjPool.Resetable, ObjPool.Initable<String>{

        private ComponentClient mClient;

        /**
         * 设置请求动作名，该名用于区分被请求的组件方执行的逻辑。
         * @param actionName 名称
         * @return Builder自身
         */
        public Builder setActionName(String actionName) {
            mClient.actionName = actionName;
            return this;
        }

        /**
         * 设置请求参数，Map集合。
         * @param params 参数集合
         * @return Builder自身
         */
        public Builder setParams(Map<String, Object> params) {
            mClient.params.clear();
            return addParams(params);
        }

        /**
         * 添加请求参数，Map集合
         * @param params 参数集合
         * @return Builder自身
         */
        public Builder addParams(Map<String, Object> params) {
            if (params != null) {
                mClient.params.putAll(params);
            }
            return this;
        }

        /**
         * 添加请求参数，键值对
         * @param key 键
         * @param value 值
         * @return Builder自身
         */
        public Builder addParam(String key, Object value) {
            mClient.params.put(key, value);
            return this;
        }

        /**
         * 设置Caller的id，如果不设置会自动生成一个。
         * @param callId
         * @return Builder自身
         */
        public Builder setCallId(String callId) {
            mClient.setCallId(callId);
            return this;
        }

        /**
         * 回调函数是否在主线程中执行
         * @param isMainThread
         * @return
         */
        public Builder isCallbackOnMainThread(boolean isMainThread) {
            mClient.isCallbackOnMainThread = isMainThread;
            return this;
        }

        /**
         * 添加组件调用前的拦截器
         * @param interceptor 拦截器
         * @return Builder自身
         */
        public Builder addInterceptor(ComponentInterceptor interceptor) {
            if (interceptor != null) {
                mClient.interceptors.add(interceptor);
            }
            return this;
        }
        /**
         * 设置activity.onDestroy时自动cancel
         * @param activity 监控此activity的生命周期，在onDestroy方法被调用后若cc未执行完则自动cancel
         * @return Builder自身
         */
        public Builder cancelOnDestroyWith(Activity activity) {
            if (activity != null) {
                mClient.cancelOnDestroyActivity = new WeakReference<>(activity);
            }
            return this;
        }

        /**
         * 设置fragment.onDestroy时自动cancel
         * @param fragment 监控此fragment的生命周期，在onDestroy方法被调用后若cc未执行完则自动cancel
         * @return Builder自身
         */
        public Builder cancelOnDestroyWith(Fragment fragment) {
            if (fragment != null) {
                mClient.cancelOnDestroyFragment = new WeakReference<>(fragment);
            }
            return this;
        }

        /**
         * 设置超时时间
         * @param timeout 超时时间限制(ms) 需大于等于0
         * @return Builder自身
         */
        public Builder setTimeout(long timeout) {
            if (timeout >= 0) {
                mClient.mTimeout = timeout;
            }
            return this;
        }


        public ComponentClient build() {
            ComponentClient client = this.mClient;
            //复用Builder
            BUILDER_POOL.put(this);
            return client;
        }

        @Override
        public void init(String s) {
            mClient = new ComponentClient(s);
        }

        @Override
        public void reset() {
            mClient = null;
        }
    }
}
