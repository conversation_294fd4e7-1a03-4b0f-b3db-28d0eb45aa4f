<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="dialogActivityTheme" parent="Theme.AppCompat.Light.Dialog">
        <!--设置dialog的背景-->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!--设置Dialog的windowFrame框为无-->
        <item name="android:windowFrame">@null</item>
        <!--设置无标题-->
        <item name="windowNoTitle">true</item>
        <!--是否浮现在activity之上，设置成true在部分机型上高度可能显示不全-->
        <item name="android:windowIsFloating">false</item>
        <!--是否半透明-->
        <item name="android:windowIsTranslucent">true</item>
        <!--设置窗口内容不覆盖-->
        <item name="android:windowContentOverlay">@null</item>
        <!--设置动画-->
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <!--背景是否模糊显示-->
        <item name="android:backgroundDimEnabled">true</item>
    </style>
</resources>