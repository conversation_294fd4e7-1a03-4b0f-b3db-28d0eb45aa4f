package com.kaolafm.kradio.brand.comprehensive;

import android.animation.FloatEvaluator;
import android.animation.ObjectAnimator;
import android.annotation.SuppressLint;

import androidx.lifecycle.Lifecycle;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.viewpager.widget.ViewPager;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.view.animation.DecelerateInterpolator;
import android.view.animation.Interpolator;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.Scroller;
import android.widget.TextView;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.bumptech.glide.Glide;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.transition.Transition;
import com.kaolafm.ad.api.model.Advert;
import com.kaolafm.ad.comprehensive.ads.image.AdvertisingImagerImpl;
import com.kaolafm.ad.comprehensive.base.ComprehensiveBaseAdInterceptorActivity;
import com.kaolafm.ad.expose.AdvertisingImager;
import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.activity.comprehensive.ui.ActivitysDetailsDialogFragment;
import com.kaolafm.kradio.brand.mvp.BrandPagePresenter;
import com.kaolafm.kradio.brand.mvp.IBrandPageView;
import com.kaolafm.kradio.common.router.IRouterConsumer;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.kradio.common.utils.CommonUtils;
import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.component.ui.brandpage.carownerradio.CarOwnerRadioEntrance;
import com.kaolafm.kradio.config.ConfigSettingManager;
import com.kaolafm.kradio.home.comprehensive.adapter.HomeAdapter;
import com.kaolafm.kradio.home.comprehensive.playerbar.ComprehensiveMiniPlayerBar;
import com.kaolafm.kradio.home.comprehensive.playerbar.ComprehensivePlayerHelper;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.base.flavor.KRadioClickRetryInter;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.RecyclerViewExposeUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.UrlUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.lib.widget.tab.OnTabSelectListener;
import com.kaolafm.kradio.lib.widget.tab.SlidingTabLayout;
import com.kaolafm.kradio.lib.widget.tab.Tab;
import com.kaolafm.kradio.lib.widget.viewpager.VerticalViewPager;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.api.brandpage.model.BrandPageListBean;
import com.kaolafm.opensdk.api.maintab.model.MainTabBean;
import com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.LivePlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.util.ReportConstants;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> shiqian
 * @date 2023-03-01
 */
@Route(path = RouterConstance.BRAND_COMPREHENSIVE_URL)
public class ComprehensiveBrandPageActivity extends ComprehensiveBaseAdInterceptorActivity<BrandPagePresenter>
        implements IBrandPageView, RecyclerViewExposeUtil.OnItemExposeListener, View.OnClickListener, IRouterConsumer {

    SlidingTabLayout brandPageNavigation;
    ImageView home_room_bg_iv;
    VerticalViewPager brand_view_page;
    ViewStub mVsHomeNoNetwork;
    View mPcLoadingView;
    ComprehensiveMiniPlayerBar miniPlayer;
    ImageView brand_car_logo_iv;
    TextView brand_page_name;
    View exitBtn;
    View titleRootView;
    ImageView pagerArrow;
    TextView cdUp;
    TextView cdDown;

    View mHomeNoNetWorkRl;

    public HomeAdapter mHomeAdapter;
    //ViewPager状态监听
    private ViewPager.OnPageChangeListener mViewPagerPageChangeListener;
    private ObjectAnimator arrowAnimation; //箭头动画
    private boolean needArrowAnimation = false; //是否需要箭头动画，直到最后一页显示后，应当置为false，以后不需要再引导用户

    private Handler mHandler = new Handler(Looper.getMainLooper());


    private final String TAG = getClass().getSimpleName();

    private List<MainTabBean> tabBeanList;
    private int selectPage = 0;
    private String brandPageId;//品牌id
    private BrandPageListBean brandPageListBean;
    private List<Fragment> fragmentList = new ArrayList<>();
    private BroadcastReceiver mReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (CarOwnerRadioEntrance.BROADCAST_RECEIVER_ACTION_CAR_ENTER_ANIMATION_COMPLETE.equals(intent.getAction())) {
                //进入动画完成
                ConfigSettingManager.getInstance().isPlayAnimation(new ConfigSettingManager.OnResultCallback<Boolean>() {
                    @Override
                    public void onResult(Boolean aBoolean) {
                        if (aBoolean) {
                            titleRootView.animate().alpha(1).setDuration(300).start();
                            brandPageNavigation.animate().alpha(1).setDuration(300).start();
                        } else {
                            titleRootView.setAlpha(1);
                            brandPageNavigation.setAlpha(1);
                        }
                    }
                });

            }
        }
    };
    private final String PLAYER_FRAGMENT = "PLAYER_FRAGMENT";

    @Override
    protected boolean isReportPage() {
        return true;
    }

    @Override
    public String getPageId() {
        return Constants.PAGE_ID_BRAND_PAGE;
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        AppManager.getInstance().setMainActivity(this);
        CommonUtils.getInstance().initGreyStyle(getWindow());
        AdvertisingImagerImpl advertisingImager = (AdvertisingImagerImpl) AdvertisingManager.getInstance().getImager();
        if (advertisingImager != null) {
            advertisingImager.displayAttachedImageInBrand(this, false, true);
//            advertisingImager.displayAttachedImage(this, false, true);
        }
        brandPageId = getIntent().getStringExtra("brandPageId");
        if (!NetworkUtil.isNetworkAvailable(getApplicationContext(), true)) {
//                    Log.i(TAG, "fetchSubscriptionData no network");
            ToastUtil.showInfo(getApplicationContext(), ResUtil.getString(R.string.no_net_work_str));
            showNoNetWorkView("");
            return;
        }
        mPresenter.getBrandPageList(brandPageId);
    }

    @Override
    protected BrandPagePresenter createPresenter() {
        return new BrandPagePresenter(this);
    }

    @Override
    public int getLayoutId() {
        return R.layout.comprehensive_brand_page_activity;
    }

    @Override
    public int getLayoutId_Tow() {
        return R.layout.comprehensive_brand_page_activity_two;
    }


    @Override
    public void onEnterAnimationComplete() {
        super.onEnterAnimationComplete();
        ViewUtil.setViewVisibility(exitBtn, View.VISIBLE);
        ViewUtil.setViewVisibility(miniPlayer, View.VISIBLE);
    }

    @Override
    public void initView(Bundle savedInstanceState) {

        brandPageNavigation=findViewById(R.id.brand_page_navigation);
        home_room_bg_iv=findViewById(R.id.home_room_bg_iv);
        brand_view_page=findViewById(R.id.brand_view_page);
        mVsHomeNoNetwork=findViewById(R.id.vs_home_no_network);
        mPcLoadingView=findViewById(R.id.pc_loading);
        miniPlayer=findViewById(R.id.miniPlayer);
        brand_car_logo_iv=findViewById(R.id.brand_car_logo_iv);
        brand_page_name=findViewById(R.id.brand_page_name);
        exitBtn=findViewById(R.id.iv_home_exit_btn);
        titleRootView=findViewById(R.id.titleRootView);
        pagerArrow=findViewById(R.id.pagerArrow);

        handleVoiceScroll();
//        handlePlayPause();

        exitBtn.setOnClickListener(v -> onClickCloseBtn());


        RouterManager.getInstance().addRouterConsumer(this);
        miniPlayer.setCoverClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                jumpToPlayerPage();
            }
        });

        RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) pagerArrow.getLayoutParams();
        layoutParams.setMarginStart((getResources().getDisplayMetrics().widthPixels - layoutParams.width) / 2);
        pagerArrow.setLayoutParams(layoutParams);

        VerticalPageTransformer mVerticalPageTransformer = new VerticalPageTransformer();
        mVerticalPageTransformer.setMaxOffset(ResUtil.getDimen(R.dimen.m150));
        brand_view_page.setPageTransformer(false, mVerticalPageTransformer);
        ConfigSettingManager.getInstance().isPlayAnimation(new ConfigSettingManager.OnResultCallback<Boolean>() {
            @Override
            public void onResult(Boolean aBoolean) {
                if (aBoolean) {
                    //开启动画时才要设置动画时长
                    setViewPagerScroller();
                }
            }
        });

        pagerArrow.setOnClickListener(this);
        findViewById(R.id.pager_arrow_wrapper).setOnClickListener(this);
    }

//    private boolean toBePaused = false;
//    private void handlePlayPause(){
//        TextView pauseView = findViewById(R.id.cd_pause);
//        PlayerManager.getInstance().addAudioFocusListener(new OnAudioFocusChangeInter() {
//            @Override
//            public void onAudioFocusChange(int i) {
//                if(i == 1 && toBePaused){
//                    toBePaused = false;
////                    miniPlayer.postDelayed(() -> miniPlayer.performPlayerBarClick(), 1000);
//                    miniPlayer.performPlayerBarClick();
//                }
//
//                if(PlayerManager.getInstance().isPlaying()){
//                    pauseView.setVisibility(View.VISIBLE);
//                }
//            }
//        });
//
//        TextView playView = findViewById(R.id.cd_play);
//        if(playView != null){
//            playView.setOnClickListener(v ->{
//                if(miniPlayer != null){
//                    miniPlayer.postDelayed(() -> miniPlayer.performPlayerBarClick(), 1000);
//                }
//            });
//        }
//
//        if(pauseView != null){
//            pauseView.setOnClickListener(v ->{
//
//                pauseView.setVisibility(View.GONE);
//
//                if(miniPlayer != null){
//                    toBePaused = true;
//                }
//            });
//        }
//    }

    private void handleVoiceScroll(){

//        TextView prePageView = findViewById(R.id.pre_page);
//        if(prePageView != null){
//            prePageView.setOnClickListener(v -> {
//                if(brand_view_page.getCurrentItem() > 0){
//                    brand_view_page.setCurrentItem(brand_view_page.getCurrentItem()-1);
//                    ToastUtil.showNormal(AppDelegate.getInstance().getContext(), "转到了上一页");
//                }
//            });
//        }
//
//        TextView nextPageView = findViewById(R.id.next_page);
//        if(nextPageView != null){
//            nextPageView.setOnClickListener(v ->{
//                if(brand_view_page.getCurrentItem() + 1 < brand_view_page.getAdapter().getCount()){
//                    brand_view_page.setCurrentItem(brand_view_page.getCurrentItem()+1);
//                    ToastUtil.showNormal(AppDelegate.getInstance().getContext(), "转到了下一页");
//                }
//            });
//        }
    }

    /**
     * 跳转到播放详情页
     */
    public void jumpToPlayerPage() {
        String routerPath = null;
        if (ComprehensivePlayerHelper.isLiving()) {
            routerPath = RouterConstance.PLAY_LIVE_COMPREHENSIVE_URL;
            PlayItem curPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
            if (curPlayItem == null || curPlayItem.getType() != PlayerConstants.RESOURCES_TYPE_LIVING) {
                return;
            }

            Bundle bundle = new Bundle();
            bundle.putLong("liveId", ((LivePlayItem) curPlayItem).getLiveId());
            RouterManager.getInstance().jumpPage(routerPath, bundle);
        } else {
            int type = PlayerManagerHelper.getInstance().getCurrentPlayType();
            if (type == PlayerConstants.RESOURCES_TYPE_BROADCAST || type == PlayerConstants.RESOURCES_TYPE_TV) {
                routerPath = RouterConstance.PLAY_BROADCAST_COMPREHENSIVE_URL;
            } else {
                List<PlayItem> playList = PlayerManager.getInstance().getPlayList();
                if (ListUtil.isEmpty(playList)) {
                    Log.i(TAG, "jumpToPlayerFragment empty list");
                    return;
                }
                routerPath = RouterConstance.PLAY_RADIO_COMPREHENSIVE_URL;
            }
            Fragment routerFragment = RouterManager.getInstance().getRouterFragment(routerPath);
            Bundle args = new Bundle();
            args.putBoolean("hasPlayerBar",true);
            routerFragment.setArguments(args);
            getSupportFragmentManager().beginTransaction().replace(R.id.frameLayout, routerFragment, PLAYER_FRAGMENT).commitNowAllowingStateLoss();
            exitBtn.setEnabled(false);
            exitBtn.setVisibility(View.GONE);
        }

    }

    @Override
    public void initData() {
        registerReceiver(mReceiver, new IntentFilter(CarOwnerRadioEntrance.BROADCAST_RECEIVER_ACTION_CAR_ENTER_ANIMATION_COMPLETE));
    }

    @Override
    protected void onDestroy() {
        unregisterReceiver(mReceiver);
//        if (mViewPagerPageChangeListener != null)
//            brand_view_page.removeOnPageChangeListener(mViewPagerPageChangeListener);
        mHandler.removeCallbacksAndMessages(null);
        super.onDestroy();
        AdvertisingManager.getInstance().close();
        AdvertisingImager advertisingImager = AdvertisingManager.getInstance().getImager();
        if (advertisingImager != null) {
            ((AdvertisingImagerImpl) advertisingImager).destroyAdViewInBrand();
        }
        RouterManager.getInstance().removeRouterConsumer(this);

    }

    @Override
    public void onLoaing() {
        mPcLoadingView.setVisibility(View.VISIBLE);
    }

    @Override
    public void onLoadFinish() {
        mPcLoadingView.setVisibility(View.GONE);
    }

    @SuppressLint("CheckResult")
    @Override
    public void showTabAndList(BrandPageListBean brandPageListBean) {
//        ToastUtil.showInfo(this, "---:" + brandPageListBean.getBrandName());
        this.brandPageListBean = brandPageListBean;

        brand_page_name.setText(brandPageListBean.getBrandName() + "");
        String url = "";
        if (brandPageListBean.getImageFiles() != null) {
            url = UrlUtil.getCardLogoUrl(brandPageListBean.getImageFiles());
        }
        ImageLoader.getInstance().displayCircleImage(this, url, brand_car_logo_iv);
        if (brandPageListBean.getImageFiles() != null) {
            url = UrlUtil.getCardBgUrl(brandPageListBean.getImageFiles());
        }
        if (!TextUtils.isEmpty(url)) {
            Glide.with(this).asDrawable().load(url).into(new SimpleTarget<Drawable>() {
                @Override
                public void onResourceReady(@NonNull Drawable resource, @Nullable Transition<? super Drawable> transition) {
                    home_room_bg_iv.setImageDrawable(resource);
                }
            });
        }
        ComperhensiveBrandPageDateFragment comperhensiveHomeDateFragment;
        fragmentList.clear();
        if (brandPageListBean.getBrandSections() != null && brandPageListBean.getBrandSections().size() > 0) {
            List<Tab> tabList = new ArrayList<>();
            for (int i = 0; i < brandPageListBean.getBrandSections().size(); i++) {
                Tab tab = new Tab();
                tab.title = brandPageListBean.getBrandSections().get(i).getName();
                tab.position = i;
                tab.code = brandPageListBean.getBrandSections().get(i).getSectionId() + "";
                tabList.add(tab);

                comperhensiveHomeDateFragment = ComperhensiveBrandPageDateFragment.newInstance();
                comperhensiveHomeDateFragment.loadDate("BrandPage", i == 0);
                comperhensiveHomeDateFragment.setBrandPageInfo(brandPageListBean.getBrandSections().get(i).getSectionId() + "");
                fragmentList.add(comperhensiveHomeDateFragment);
            }
            brandPageNavigation.setTabs(tabList);
            brandPageNavigation.setCurrentTab(0);
            brandPageNavigation.setOnTabSelectListener(new OnTabSelectListener() {
                @Override
                public void onTabSelect(int position) {
                    brand_view_page.setCurrentItem(position);
                    Tab tab = brandPageNavigation.getTab(position);
                    ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_BRAND_PAGE_NAVIGATION, tab.title, getPageId()
                            , ReportConstants.CONTROL_TYPE_SCREEN));
                }

                @Override
                public void onTabReselect(int position) {

                }
            });
            ConfigSettingManager.getInstance().isPlayAnimation(new ConfigSettingManager.OnResultCallback<Boolean>() {
                @Override
                public void onResult(Boolean aBoolean) {
                    needArrowAnimation = aBoolean;
                    if (brandPageNavigation != null)
                        brandPageNavigation.setIndicatorAnimEnable(aBoolean);
                }
            });
            brand_view_page.setOffscreenPageLimit(fragmentList.size());
            brand_view_page.setAdapter(new FragmentPagerAdapter(getSupportFragmentManager()) {
                @Override
                public Fragment getItem(int position) {
                    return fragmentList.get(position);
                }

                @Override
                public int getCount() {
                    return fragmentList.size();
                }

                @Override
                public void destroyItem(ViewGroup container, int position, Object object) {
//                    if (position == 0) return;
//                    super.destroyItem(container, position, object);
                }
            });
            if (mViewPagerPageChangeListener == null) {
                initPageChangeListener();
                brand_view_page.setOnPageChangeListener(mViewPagerPageChangeListener);
            }
            brand_view_page.setCurrentItem(0);

            if (fragmentList.size() > 1) {
                mHandler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (pagerArrow == null) return;
                        pagerArrow.setVisibility(View.VISIBLE);
                        runPageArrowPageAnimation();
                        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_BRAND_PAGE_ARROW_BUTTON, null, getPageId()
                                , ReportConstants.CONTROL_TYPE_SCREEN, null, null, null, null, null));
                    }
                }, 1000);
            }
            for (Tab tab : tabList) {
                ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_BRAND_PAGE_NAVIGATION, tab.title, getPageId()
                        , ReportConstants.CONTROL_TYPE_SCREEN));
            }
        }
    }

    /**
     * 初始化ViewPager监听器
     */
    private void initPageChangeListener() {
        mViewPagerPageChangeListener = new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                brandPageNavigation.setCurrentTab(position);
            }

            @Override
            public void onPageScrollStateChanged(int state) {
                if (state == ViewPager.SCROLL_STATE_IDLE) {
                    if (brand_view_page == null || brand_view_page.getAdapter() == null) return;
                    boolean isLastPage = brand_view_page.getCurrentItem() == brand_view_page.getAdapter().getCount() - 1;
                    if (isLastPage) {
                        pagerArrow.setRotation(180);
                    } else {
                        pagerArrow.setRotation(0);
                    }
                    runPageArrowPageAnimation();
                    if (isLastPage)
                        needArrowAnimation = false;
                }
            }
        };
    }

    /**
     * 播放第一页选中后箭头上下跳动的动画
     */
    private void runPageArrowPageAnimation() {
        if (arrowAnimation != null && arrowAnimation.isRunning()) arrowAnimation.cancel();
        if (!needArrowAnimation || pagerArrow.getVisibility() != View.VISIBLE)
            return;    //不需要动画了，返回
        int dimen = ResUtil.getDimen(R.dimen.m20);
        if (pagerArrow.getRotation() == 180) dimen = -dimen;    //向上跳动
        if (arrowAnimation == null) {
            arrowAnimation = ObjectAnimator.ofObject(pagerArrow, "translationY", new FloatEvaluator(), 0, dimen)
                    .setDuration(300);
            arrowAnimation.setInterpolator(new DecelerateInterpolator());
            arrowAnimation.setRepeatCount(3);
            arrowAnimation.setRepeatMode(ObjectAnimator.REVERSE);
        } else {
            arrowAnimation.setFloatValues(0, dimen);
        }
        arrowAnimation.start();
    }

    @Override
    public void showTabAndFailure(Exception e) {
        showNoNetWorkView("");
    }

    @Override
    public void onShowContent(List<HomeCell> cells) {

    }

    @Override
    public void onUpdateLiveStatus(List<HomeCell> newCells) {

    }

    @Override
    public void onShowContentFailure(Exception e) {
        showNoNetWorkView("");
    }

    private void showOrHideLoadingView(boolean isShow) {
        Log.i(TAG, "showOrHideLoadingView: " + isShow);
        ViewUtil.setViewVisibility(mPcLoadingView, isShow ? View.VISIBLE : View.GONE);
    }

    private void showNoNetWorkView(String error) {
        showOrHideLoadingView(false);
        if (mVsHomeNoNetwork != null) {
            if (null == mHomeNoNetWorkRl) {
                mHomeNoNetWorkRl = mVsHomeNoNetwork.inflate();
            }
            if (StringUtil.isNotEmpty(error)) {
                TextView tvNetworkNosign = mHomeNoNetWorkRl.findViewById(R.id.tv_network_nosign);
                tvNetworkNosign.setText(error);
            }
            // 支持点击重试
            KRadioClickRetryInter mKRadioClickRetryInter = ClazzImplUtil.getInter(
                    "KRadioClickRetryInterImpl");
            if (mKRadioClickRetryInter == null || mKRadioClickRetryInter.canRetry()) {
                ImageView ivNetworkNoSign = mHomeNoNetWorkRl.findViewById(R.id.network_nosigin);
                ivNetworkNoSign.setOnClickListener(v -> {
                    hideErrorLayout();
                    mPresenter.getBrandPageList(brandPageId);
                });
            }
        }
        ViewUtil.setViewVisibility(mHomeNoNetWorkRl, View.VISIBLE);
    }

    private void hideErrorLayout() {
//        ViewUtil.setViewVisibility(mHomeRecyclerView, View.VISIBLE);
        if (mHomeNoNetWorkRl != null) {
            //这里要把无网的view移除，是因为横竖屏转换的时候设置GONE不起作用，依然会显示出来。
            //homeRoom.removeView(mHomeNoNetWorkRl);
            //mHomeNoNetWorkRl = null;
            ViewUtil.setViewVisibility(mHomeNoNetWorkRl, View.GONE);
        }
    }

    public void onClickCloseBtn() {
        finish();
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_BRAND_PAGE_BACK_BUTTON, null, getPageId()
                , ReportConstants.CONTROL_TYPE_SCREEN, null, null, null, null, null));
//        overridePendingTransition(R.anim.anim_jump_brand_enter,R.anim.anim_jump_brand_exit);

    }

    @Override
    public void onItemViewVisible(boolean visible, int position) {

    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.pagerArrow || v.getId() == R.id.pager_arrow_wrapper) {
            //切页
            if (AntiShake.check(v.getId()) || brand_view_page == null || brand_view_page.getAdapter() == null)
                return;
            int count = brand_view_page.getAdapter().getCount();
            if (count <= 0) return;
            int currentItem = brand_view_page.getCurrentItem();
            if (currentItem == count - 1) {
                brand_view_page.setCurrentItem(0);
            } else {
                int target = Math.min(count - 1, currentItem + 1);
                brand_view_page.setCurrentItem(target);
            }

            ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_BRAND_PAGE_ARROW_BUTTON, null, getPageId()
                    , ReportConstants.CONTROL_TYPE_SCREEN, null, null, null, null, null));
        }
    }


    public static class VerticalPageTransformer implements ViewPager.PageTransformer {
        /**
         * 最大偏移
         */
        private float maxOffset = 0f;

        public float getMaxOffset() {
            return maxOffset;
        }

        public void setMaxOffset(float maxOffset) {
            this.maxOffset = maxOffset;
        }

        @Override
        public void transformPage(View view, float position) {
            if (position < -1.0f) {
                // [-Infinity,-1)
                // This page is way off-screen to the left.
                handleInvisiblePage(view, position);
            } else if (position <= 0.0f) {
                // [-1,0]
                // Use the default slide transition when moving to the left page
                handleLeftPage(view, position);
            } else if (position <= 1.0f) {
                // (0,1]
                handleRightPage(view, position);
            } else {
                // (1,+Infinity]
                // This page is way off-screen to the right.
                handleInvisiblePage(view, position);
            }
        }

        private void handleRightPage(View view, float position) {
        }

        private void handleLeftPage(View view, float position) {
            view.setTranslationY(maxOffset * position);
        }

        private void handleInvisiblePage(View view, float position) {
        }
    }

    /**
     * 设置滚动时长
     */
    private void setViewPagerScroller() {
        try {
            Field scrollerField = VerticalViewPager.class.getDeclaredField("mScroller");
            scrollerField.setAccessible(true);
            Field interpolator = VerticalViewPager.class.getDeclaredField("sInterpolator");
            interpolator.setAccessible(true);

            Scroller scroller = new Scroller(this, (Interpolator) interpolator.get(brand_view_page)) {
                @Override
                public void startScroll(int startX, int startY, int dx, int dy, int duration) {
                    super.startScroll(startX, startY, dx, dy, 1000);    // 这里是关键，将duration变长或变短
                }

                @Override
                public void startScroll(int startX, int startY, int dx, int dy) {
                    super.startScroll(startX, startY, dx, dy, 1000);
                }
            };
            scrollerField.set(brand_view_page, scroller);
        } catch (NoSuchFieldException e) {
            // Do nothing.
        } catch (IllegalAccessException e) {
            // Do nothing.
        }
    }

    @Override
    public void onBackPressedSupport() {
        exitBtn.setEnabled(true);
        exitBtn.setVisibility(View.VISIBLE);
        Fragment fragmentByTag = getSupportFragmentManager().findFragmentByTag(PLAYER_FRAGMENT);
        if (fragmentByTag != null) {
            getSupportFragmentManager().beginTransaction().remove(fragmentByTag).commitNowAllowingStateLoss();
            updateFragmentLiveStatus();
            return;
        }

        super.onBackPressedSupport();

    }

    /**
     * 更新 Fragment 圆形直播间组件状态
     */
    private void updateFragmentLiveStatus(){
        if (fragmentList == null || fragmentList.isEmpty()){
            return;
        }
        for (Fragment fragment : fragmentList){
            if (fragment instanceof ComperhensiveBrandPageDateFragment){
                ((ComperhensiveBrandPageDateFragment) fragment).updateFragmentLiveStatus("ComprehensiveBrandPageActivity -> onBackPressedSupport -> updateFragmentLiveStatus -> ComperhensiveBrandPageDateFragment");
            }
        }
    }

    @Override
    protected boolean isNeedAdvertInterceptor() {
        return false;
    }

    @Override
    protected boolean isIgnoreAdvert(Advert advert) {
        Fragment fragmentByTag = getSupportFragmentManager().findFragmentByTag(PLAYER_FRAGMENT);
        return !getLifecycle().getCurrentState().isAtLeast(Lifecycle.State.RESUMED) || fragmentByTag == null;
    }


    @Override
    protected void onResume() {
        super.onResume();
        //返回按钮数据上报
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_BRAND_PAGE_BACK_BUTTON, null, getPageId()
                , ReportConstants.CONTROL_TYPE_SCREEN, null, null, null, null, null));
        //翻页箭头数据上报
        if (pagerArrow.getVisibility() == View.VISIBLE)
            ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_BRAND_PAGE_ARROW_BUTTON, null, getPageId()
                    , ReportConstants.CONTROL_TYPE_SCREEN, null, null, null, null, null));
    }

    @Override
    public String consumeRoute(String pageId, Object extra) {
        switch (pageId) {
            case Constants.PAGE_ID_ACTIVITY://综合版-活动详情
                //不是常驻的活动才可以打开详情
                String actId = "";
                if (extra != null && !TextUtils.isEmpty(extra.toString())) {
                    actId = extra.toString();
                }
                if (TextUtils.isEmpty(actId)) {
                    ToastUtil.showInfo(getApplicationContext(), "数据错误！");
                    return IRouterConsumer.ROUTER_CONSUME_FULLY;
                }
                ActivitysDetailsDialogFragment dialogFragment
                        = (ActivitysDetailsDialogFragment) new ActivitysDetailsDialogFragment(AppManager.getInstance().getCurrentActivity(), actId);
                dialogFragment.show();
                break;
            case Constants.PAGE_ID_PLAYER_MUSIC_LIST:
            case Constants.PAGE_ID_BROADCAST_MAIN:
            case Constants.PAGE_ID_PLAYER_LIVING:
            case Constants.PAGE_ID_PLAYER_ALBUM_MAIN:
                jumpToPlayerPage();
                break;
        }
        return IRouterConsumer.ROUTER_CONSUME_FULLY;
    }
}
