package com.kaolafm.kradio.category.qq.tab;

import androidx.fragment.app.Fragment;
import android.text.TextUtils;

import com.kaolafm.kradio.category.FragmentFactory;
import com.kaolafm.kradio.category.base.TabContract;
import com.kaolafm.kradio.categories.CategoryConstant;
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.opensdk.api.operation.model.category.Category;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 **/
public class QQTabPresenter extends BasePresenter<QQTabModel, TabContract.IView> implements TabContract.IPresenter {

    public QQTabPresenter(TabContract.IView view) {
        super(view);
    }

    @Override
    protected QQTabModel createModel() {
        return new QQTabModel();
    }

    @Override
    public void loadAIData(long showTabId) {

    }

    @Override
    public void loadData(long showTabId) {

        mModel.getCategoriesByParentId(CategoryConstant.MEDIA_TYPE_MUSIC, new com.kaolafm.opensdk.http.core.HttpCallback<List<Category>>() {
            @Override
            public void onSuccess(List<Category> categories) {
                //数据处理:返回的没有<我的>，就添加<我的>
                if (categories != null && categories.size() > 0) {
                    if (!TextUtils.equals(CategoryConstant.TAB_NAME_MINE, categories.get(0).getName())) {
                        Category category = new Category();
                        category.setName(CategoryConstant.TAB_NAME_MINE);
                        category.setCode(CategoryConstant.TAB_CID_MINE);
                        categories.add(0, category);
                    }
                }
                //显示
                List<TabContract.TabItem> tabs = new ArrayList<>();
                //我的
                TabContract.TabItem tabMine = new TabContract.TabItem();
                tabMine.title = CategoryConstant.TAB_NAME_MINE;
                tabMine.tabId = Integer.valueOf(CategoryConstant.TAB_CID_MINE);
                tabs.add(tabMine);
                //遍历接口
                if (categories != null && categories.size() > 0) {
                    for (int i = 0; i < categories.size(); i++) {
                        Category category = categories.get(i);
                        if (TextUtils.equals(CategoryConstant.TAB_NAME_MINE, category.getName())) {
                            continue;
                        }

                        TabContract.TabItem tab = new TabContract.TabItem();
                        tab.title = category.getName();
                        tab.tabId = Integer.valueOf(category.getCode());
                        tabs.add(tab);
                    }
                }
                //
                String[] titles = new String[tabs.size()];
                for (int i = 0; i < titles.length; i++) {
                    titles[i] = tabs.get(i).title;
                }
                //
                List<Fragment> fs = new ArrayList<>();
                for (int i = 0; i < tabs.size(); i++) {
                    TabContract.TabItem tabItem = tabs.get(i);
                    if (CategoryConstant.TAB_NAME_MINE.equals(tabItem.title)) {
                        //fs.add(FragmentFactory.createQqTabMineFragment());
                    } else {
                        fs.add(FragmentFactory.createTabItemFragment(/*CategoryConstant.MEDIA_TYPE_MUSIC,*/ tabItem.tabId));
                    }
                }
                //
                mView.showData(titles, fs, getIndexById(tabs, showTabId));
            }


            @Override
            public void onError(com.kaolafm.opensdk.http.error.ApiException exception) {
                mView.showError(exception);
            }
        });
    }


    /**
     * 根据id,选择tab index
     *
     * @param tabItems
     * @param showTabId
     * @return
     */
    private int getIndexById(List<TabContract.TabItem> tabItems, long showTabId) {
        int index = 0;
        for (int i = 0; i < tabItems.size(); i++) {
            if (tabItems.get(i).tabId == showTabId) {
                index = i;
                break;
            }
        }
        return index;
    }


}
