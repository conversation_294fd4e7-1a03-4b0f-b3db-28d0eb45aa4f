package com.kaolafm.kradio.common.widget;

import android.content.Context;
import android.content.res.TypedArray;
import androidx.annotation.DrawableRes;
import android.util.AttributeSet;
import android.widget.EditText;

import com.kaolafm.kradio.k_kaolafm.R;

import skin.support.widget.SkinCompatBackgroundHelper;
import skin.support.widget.SkinCompatSupportable;
import skin.support.widget.SkinCompatTextHelper;

import static skin.support.widget.SkinCompatHelper.INVALID_ID;
import static skin.support.widget.SkinCompatHelper.checkResourceId;

public class CursorSkinEditText extends EditText implements SkinCompatSupportable {
    private int mTextCursorDrawableResId = INVALID_ID;

    private SkinCompatTextHelper mTextHelper;
    private SkinCompatBackgroundHelper mBackgroundTintHelper;

    public CursorSkinEditText(Context context) {
        this(context, null);
    }

    public CursorSkinEditText(Context context, AttributeSet attrs) {
        this(context, attrs, R.attr.editTextStyle);
    }

    public CursorSkinEditText(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mBackgroundTintHelper = new SkinCompatBackgroundHelper(this);
        mBackgroundTintHelper.loadFromAttributes(attrs, defStyleAttr);
        mTextHelper = SkinCompatTextHelper.create(this);
        mTextHelper.loadFromAttributes(attrs, defStyleAttr);

        TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.CursorSkinEditText);
        if (a.hasValue(R.styleable.CursorSkinEditText_cset_cursorDrawable)) {
            mTextCursorDrawableResId = a.getResourceId(R.styleable.CursorSkinEditText_cset_cursorDrawable, INVALID_ID);
        }
        a.recycle();
        applySelf();
    }

    @Override
    public void setBackgroundResource(@DrawableRes int resId) {
        super.setBackgroundResource(resId);
        if (mBackgroundTintHelper != null) {
            mBackgroundTintHelper.onSetBackgroundResource(resId);
        }
    }

    @Override
    public void setTextAppearance(int resId) {
        setTextAppearance(getContext(), resId);
    }

    @Override
    public void setTextAppearance(Context context, int resId) {
        super.setTextAppearance(context, resId);
        if (mTextHelper != null) {
            mTextHelper.onSetTextAppearance(context, resId);
        }
    }

    public int getTextColorResId() {
        return mTextHelper != null ? mTextHelper.getTextColorResId() : INVALID_ID;
    }

    @Override
    public void setCompoundDrawablesRelativeWithIntrinsicBounds(
            @DrawableRes int start, @DrawableRes int top, @DrawableRes int end, @DrawableRes int bottom) {
        super.setCompoundDrawablesRelativeWithIntrinsicBounds(start, top, end, bottom);
        if (mTextHelper != null) {
            mTextHelper.onSetCompoundDrawablesRelativeWithIntrinsicBounds(start, top, end, bottom);
        }
    }

    @Override
    public void setCompoundDrawablesWithIntrinsicBounds(
            @DrawableRes int left, @DrawableRes int top, @DrawableRes int right, @DrawableRes int bottom) {
        super.setCompoundDrawablesWithIntrinsicBounds(left, top, right, bottom);
        if (mTextHelper != null) {
            mTextHelper.onSetCompoundDrawablesWithIntrinsicBounds(left, top, right, bottom);
        }
    }

    @Override
    public void applySkin() {
        if (mBackgroundTintHelper != null) {
            mBackgroundTintHelper.applySkin();
        }
        if (mTextHelper != null) {
            mTextHelper.applySkin();
        }
        applySelf();
    }

    private void applySelf() {
        mTextCursorDrawableResId = checkResourceId(mTextCursorDrawableResId);
        if (mTextCursorDrawableResId != INVALID_ID) {
            try {
                setTextCursorDrawable(mTextCursorDrawableResId);
                invalidate();
//                Field mEditor = getClass().getDeclaredField("mEditor");
//                mEditor.setAccessible(true);
//                Field mDrawableForCursor = mEditor.getClass().getDeclaredField("mDrawableForCursor");
//                mDrawableForCursor.setAccessible(true);
//                mDrawableForCursor.set(mEditor.get(this), ResUtil.getDrawable(mTextCursorDrawableResId));
            } catch (Throwable e) {
                e.printStackTrace();
            }
        }
    }
}
