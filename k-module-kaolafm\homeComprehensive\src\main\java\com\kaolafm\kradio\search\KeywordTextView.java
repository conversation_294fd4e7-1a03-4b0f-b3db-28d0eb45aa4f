package com.kaolafm.kradio.search;

import android.content.Context;
import android.text.SpannableString;
import android.util.AttributeSet;

import com.kaolafm.kradio.lib.utils.KeywordsUtils;
import com.kaolafm.kradio.lib.utils.StringUtil;

import java.util.List;

import skin.support.widget.SkinCompatTextView;

public class KeywordTextView extends SkinCompatTextView {
    private SpannableString highlightContent;

    public KeywordTextView(Context context) {
        super(context);
    }

    public KeywordTextView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public KeywordTextView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void setKeywordsAndColor(String content, List<String> keywords, int color) {
        if (StringUtil.isEmpty(content)) return;
        setKeywordsAndColor(content, keywords, color, 0, content.length());
    }

    public void setKeywordsAndColor(String content, List<String> keywords, int color, int start, int count) {
        if (StringUtil.isEmpty(content)) return;
        highlightContent =
                KeywordsUtils.highlightKeywords(content, keywords, color);
        if (start < content.length()) {
            int newCount = count;
            if (start + count >= content.length()) {
                newCount = content.length() - start;
            }
            CharSequence newContent = highlightContent.subSequence(start, newCount);
            setText(newContent);
            return;
        }
        setText(highlightContent);
    }

    public SpannableString getHighlightContent() {
        return highlightContent;
    }

    public CharSequence getHighlightContent(int startOffset) {
        return highlightContent.subSequence(startOffset, highlightContent.length());
    }
}
