package com.kaolafm.kradio.lib.exit;

import android.util.Log;

import com.kaolafm.kradio.lib.init.AppInitTask;

import java.util.ArrayList;
import java.util.List;

/**
 * 结束任务{@link AppExitTask}集合。内部逻辑处理使用，外部不需要关心
 * <AUTHOR>
 * @date 2019-09-16
 */
public class AppExitTaskContainer {

    public List<AppExitTask> appExitTasks;

    public List<AppExitTask> asyncExitTasks;

    public AppExitTaskContainer() {
        appExitTasks = new ArrayList<>();
        asyncExitTasks = new ArrayList<>();
    }

    public void add(AppExitTask task) {
        Log.i("logx"," 添加任务: "+ task.exitreatment.getClass());
        appExitTasks.add(task);
        if (task.isAsync) {
            asyncExitTasks.add(task);
        }
    }
}
