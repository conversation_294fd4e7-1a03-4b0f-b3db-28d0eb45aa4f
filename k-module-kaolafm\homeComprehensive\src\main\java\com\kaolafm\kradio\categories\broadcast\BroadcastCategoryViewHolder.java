package com.kaolafm.kradio.categories.broadcast;

import android.util.TypedValue;
import android.view.View;
import android.widget.TextView;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.bean.SubcategoryItemBean;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.utils.ResUtil;

/**
 * 分类广播
 *
 * <AUTHOR>
 * @date 2018/5/9
 */

public class BroadcastCategoryViewHolder extends BaseHolder<SubcategoryItemBean> {
    TextView mTvBroadcastCategoryName;
    View mRootView;

    public BroadcastCategoryViewHolder(View itemView) {
        super(itemView);
        mTvBroadcastCategoryName=itemView.findViewById(R.id.tv_broadcast_category_name);
        mRootView=itemView.findViewById(R.id.root_view);
    }

    @Override
    public void setupData(SubcategoryItemBean subcategoryItemBean, int position) {
        mTvBroadcastCategoryName.setText(subcategoryItemBean.getName());
        mRootView.setContentDescription(subcategoryItemBean.getName());
        mTvBroadcastCategoryName.setTextSize(TypedValue.COMPLEX_UNIT_PX, ResUtil.getDimen(R.dimen.subcategory_item_broadcast_category_text_size));
    }
}
