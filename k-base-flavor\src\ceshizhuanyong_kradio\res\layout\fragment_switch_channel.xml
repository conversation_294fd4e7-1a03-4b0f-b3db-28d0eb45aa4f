<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_home">

    <ImageView
        android:id="@+id/iv_switch_channel_back"
        android:layout_width="@dimen/m80"
        android:layout_height="@dimen/m80"
        android:layout_marginLeft="@dimen/x40"
        android:layout_marginTop="@dimen/y20"
        android:background="@drawable/color_main_button_click_selector"
        android:padding="@dimen/m22"
        android:src="@drawable/player_ic_back"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_channel_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:text="@string/settings_channel_back_door"
        android:textColor="@color/text_color_7"
        android:textSize="@dimen/m30"
        app:layout_constraintBottom_toBottomOf="@id/iv_switch_channel_back"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_switch_channel_back" />

    <View
        android:id="@+id/view_channel_title_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/y1"
        android:layout_marginTop="@dimen/y20"
        android:background="#19FFFFFF"
        app:layout_constraintTop_toBottomOf="@id/iv_switch_channel_back" />

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/envLL"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/m40"
        android:layout_marginLeft="@dimen/x77"
        android:layout_marginEnd="@dimen/x50"
        android:contentDescription="@string/person_center_env_str"
        android:orientation="horizontal"
        android:visibility="visible"
        app:layout_constraintTop_toBottomOf="@id/view_channel_title_divider"
        tools:ignore="MissingConstraints">

        <ImageView
            android:layout_width="@dimen/m28"
            android:layout_height="@dimen/m28"
            android:layout_gravity="center_vertical"
            android:src="@drawable/settings_aboutus" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/m10"
            android:layout_weight="1"
            android:text="@string/person_center_env_str"
            android:textColor="@color/setting_title_text_color"
            android:textSize="@dimen/m26" />

        <Switch
            android:id="@+id/env_switch"
            android:layout_width="@dimen/m80"
            android:layout_height="@dimen/m40"
            android:layout_alignParentRight="true"
            android:contentDescription="@string/person_center_env_str"
            android:focusable="true"
            android:thumb="@drawable/setting_switch_thumb"
            android:track="@drawable/setting_switch_selector" />
    </androidx.appcompat.widget.LinearLayoutCompat>
    <TextView
        android:id="@+id/tv_channel_current"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/x77"
        android:layout_marginTop="@dimen/y72"
        android:text="渠道分支:"
        android:textColor="@color/text_color_7"
        android:textSize="@dimen/text_size4"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/envLL"/>

    <TextView
        android:id="@+id/tv_channel_current_name"
        android:layout_width="@dimen/m737"
        android:layout_height="@dimen/m60"
        android:layout_marginStart="@dimen/x30"
        android:layout_marginEnd="@dimen/x50"
        android:background="@drawable/shape_channel_rectangle"
        android:ellipsize="marquee"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/x36"
        android:singleLine="true"
        android:textColor="@color/text_color_7"
        android:textSize="@dimen/text_size5"
        app:layout_constraintBottom_toBottomOf="@id/tv_channel_current"
        app:layout_constraintLeft_toRightOf="@id/tv_channel_current"
        app:layout_constraintTop_toTopOf="@id/tv_channel_current"
        tools:text="测试专用" />

    <ImageView
        android:id="@+id/comprehensive_switch_channel_arrow_view"
        android:layout_width="@dimen/m16"
        android:layout_height="@dimen/m16"
        android:layout_marginRight="@dimen/x60"
        android:src="@drawable/comprehensive_bg_switch_channel"
        app:layout_constraintBottom_toBottomOf="@+id/tv_channel_current_name"
        app:layout_constraintEnd_toEndOf="@+id/tv_channel_current_name"
        app:layout_constraintTop_toTopOf="@+id/tv_channel_current_name" />

    <RelativeLayout
        android:id="@+id/rl_channel_list"
        android:layout_width="0dp"
        android:layout_height="@dimen/m370"
        android:layout_marginTop="@dimen/y8"
        android:background="@drawable/bg_switch_list"
        android:maxHeight="@dimen/m370"
        android:paddingLeft="@dimen/x30"
        android:paddingRight="@dimen/x40"
        android:paddingTop="@dimen/y20"
        android:visibility="invisible"
        android:paddingBottom="@dimen/y26"
        app:layout_constraintEnd_toEndOf="@id/tv_channel_current_name"
        app:layout_constraintStart_toStartOf="@id/tv_channel_current_name"
        app:layout_constraintTop_toBottomOf="@id/tv_channel_current_name">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_channel_list_1"
            style="@style/recyclerview_bar_style"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
    </RelativeLayout>

    <TextView
        android:id="@+id/tv_channel_commit"
        android:layout_width="@dimen/m160"
        android:layout_height="@dimen/m60"
        android:layout_marginLeft="@dimen/x30"
        android:background="@drawable/switch_btn_bg"
        android:gravity="center"
        android:text="确认"
        android:textColor="@color/text_color_7"
        android:textSize="@dimen/text_size5"
        app:layout_constraintBottom_toBottomOf="@+id/tv_channel_current_name"
        app:layout_constraintStart_toEndOf="@+id/tv_channel_current_name"
        app:layout_constraintTop_toTopOf="@+id/tv_channel_current_name" />

</androidx.constraintlayout.widget.ConstraintLayout>