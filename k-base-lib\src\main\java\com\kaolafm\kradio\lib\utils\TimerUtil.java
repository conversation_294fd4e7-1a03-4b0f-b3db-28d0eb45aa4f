package com.kaolafm.kradio.lib.utils;

import io.reactivex.Flowable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Action;
import io.reactivex.schedulers.Schedulers;
import java.util.concurrent.TimeUnit;

/**
 * 定时相关的工具类
 * Created by donald on 2017/11/3.
 */
public class TimerUtil {

    private  Disposable mDisposable;

    private TimerUtil() {
    }

    /**
     * 获取一个工具类实例，<b color="#fff000">不是单例</b>。
     */
    public static TimerUtil newInstance() {
        return new TimerUtil();
    }

    /**
     * milliseconds 毫秒后执行。执行完成后会自动取消
     */
    public void timer(long milliseconds, final INext next) {
        cancel();
        mDisposable = Flowable.timer(milliseconds, TimeUnit.MILLISECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeOn(Schedulers.io())
                .subscribe(aLong -> {
                    if (next != null) {
                        next.doNext(aLong);
                    }
                    cancel();
                });
    }

    /**
     * 每隔milliseconds毫秒后执行一次
     */
    public void interval(long milliseconds, final INext iNext) {
        cancel();
        mDisposable = Flowable.interval(milliseconds, TimeUnit.MILLISECONDS)
                //加上背压策略
                .onBackpressureDrop()
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(aLong -> {
                    if (iNext != null) {
                        iNext.doNext(aLong);
                    }
                });
    }

    /**
     * 从指定的时间的秒数倒计时，每次减一秒
     * @param count 倒计时的时长
     * @param next  倒计时中执行
     * @param complete 计时完成
     */
    public void countdown(int count, INext next, Action complete) {
        mDisposable = Flowable.interval(0, 1, TimeUnit.SECONDS)
                .take(count)
                .map(aLong -> count - aLong)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(time -> {
                    if (next != null) {
                        next.doNext(time);
                    }
                }, throwable -> cancel(), () -> {
                    if (complete != null) {
                        complete.run();
                    }
                    cancel();
                });
    }

    /**
     * 取消定时器
     */
    public void cancel() {
        if (mDisposable != null && !mDisposable.isDisposed()) {
            mDisposable.dispose();
            mDisposable = null;
        }
    }

    public boolean isCountDownInProgress() {
        return mDisposable != null && !mDisposable.isDisposed();
    }

    public interface INext {

        void doNext(long num);
    }
}
