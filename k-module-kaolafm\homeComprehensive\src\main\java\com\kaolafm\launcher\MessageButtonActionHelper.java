package com.kaolafm.launcher;

import android.app.Dialog;
import android.app.ProgressDialog;
import android.content.Context;
import android.os.Bundle;
import android.view.Window;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.kaolafm.kradio.activity.comprehensive.ui.ActivitysDetailsDialogFragment;
import com.kaolafm.kradio.common.comprehensive.web.WebViewActivity;
import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.kradio.common.widget.ProgressImageView;
import com.kaolafm.kradio.home.comprehensive.gallery.PageJumper;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.CrashMessageButtonActionBean;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

import java.util.Map;

public class MessageButtonActionHelper {

    private static boolean waitPlayerPlaying = false;

    public static void handleButtonAction(Context context, CrashMessageButtonActionBean action){
        switch (action.getActionType()){

            case 4: //内部页面跳转
                Map<String, String> params = RouterManager.getInstance().parseUrlParams(action.getUrl());
                String pageId = params.get(Constants.ROUTER_PARAMS_KEY_PAGE_ID);
                if( !StringUtil.isEmpty(pageId) ){
                    RouterManager.getInstance().navigateToPage(context, pageId, params);
                }

                break;
            case 3: //跳活动页面

                ActivitysDetailsDialogFragment dialogFragment = new ActivitysDetailsDialogFragment(context, action.getResId());
                dialogFragment.show();

                break;
            case 2: //跳H5页面
                WebViewActivity.start(context, action.getUrl());
                break;
            case 1: //根据<resId,resType>跳转播放或活动页面

                if(action.getResType() == PlayerConstants.RESOURCES_TYPE_LIVING){
                    PlayItem playItem = PlayerManagerHelper.getInstance().getCurPlayItem();
                    if (PlayerManagerHelper.getInstance().isSameProgram(playItem, action.getResId())) {
                        //相同 只跳转到直播间
                        PageJumper.getInstance().jumpToLivePage(Long.parseLong(action.getResId()));
                        return;
                    }
                    PlayerManagerHelper.getInstance().start(action.getResId(), action.getResType());
                    PageJumper.getInstance().jumpToLivePage(Long.parseLong(action.getResId()));
                } else if(action.getResType() == ResType.TYPE_AUDIO){
                    PlayerManagerHelper.getInstance().start(action.getResId(), action.getResType());
                } else {
                    PlayerManagerHelper.getInstance().start(action.getResId(), action.getResType());

                    waitPlayerPlaying = true;
                    LoadingDialog.showLoading();
                   PlayerManager.getInstance().addPlayControlStateCallback(new BasePlayStateListener(){
                       @Override
                       public void onProgress(PlayItem playItem, long progress, long total) {
                           if(waitPlayerPlaying){
                               int type = PlayerManagerHelper.getInstance().getCurrentPlayType();
                               if(type == action.getResType()){
                                   PageJumper.getInstance().jumpToPlayerFragment(null);
                                   waitPlayerPlaying = false;
                                   LoadingDialog.hideLoading();
                               }
                           }
                       }
                   });
                }

                break;
        }
    }

//    private static ProgressDialog loading = null;
//    private static void showLoading(){
//        loading = ProgressDialog.show(AppManager.getInstance().getTopActivity(), "加载中...", "");
//        loading.setContentView(R.layout.loading_layout);
//        loading.setCancelable(true);
//    }
//
//    private static void hideLoading(){
//        if(loading != null){
//            loading.dismiss();
//            loading = null;
//        }
//    }

    public static class LoadingDialog extends Dialog{

        public LoadingDialog(@NonNull Context context) {
            super(context, R.style.FullScreenDialogTheme);
        }

        @Override
        public void onCreate(@Nullable Bundle savedInstanceState) {
            super.onCreate(savedInstanceState);
            setContentView(R.layout.refresh_center);

            setCancelable(true);
        }

        private static LoadingDialog loadingDialog;
        public static void showLoading(){
            if(loadingDialog != null){
                loadingDialog.dismiss();
                loadingDialog = null;
            }
            loadingDialog = new LoadingDialog(AppManager.getInstance().getCurrentActivity());
            loadingDialog.show();
        }

        private static void hideLoading(){
            if(loadingDialog != null){
                loadingDialog.dismiss();
                loadingDialog = null;
            }
        }
    }
}
