package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.content.Context;
import android.util.Log;

import com.autosee.vr.sdk.VRClientManager;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioRecorderInter;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-03-12 11:59
 ******************************************/
public final class KRadioAudioRecorderImpl implements KRadioAudioRecorderInter {

    private KRadioAudioRecorderInter.OnVRStatusListener mOnVRStatusListener;

    public KRadioAudioRecorderImpl() {
        Log.i("KRadioAudioRecorderImpl", "KRadioAudioRecorderImpl------->start");
        VRClientManager.getInstance().setTtsCallback(new VRClientManager.TTSCallback() {
            @Override
            public void onSpeakBegin() {
                if (mOnVRStatusListener != null) {
                    mOnVRStatusListener.onSpeakBegin();
                }
            }

            @Override
            public void onSpeakPaused() {

            }

            @Override
            public void onSpeakResumed() {

            }

            @Override
            public void onCompleted() {
                if (mOnVRStatusListener != null) {
                    mOnVRStatusListener.onSpeakCompleted();
                }
            }
        });
    }

    @Override
    public boolean initVR(Object... args) {
        Context context = (Context) args[0];
        if (context == null) {
            return false;
        }
        VRClientManager.getInstance().init(context instanceof Activity ? context.getApplicationContext() : context, VRClientManager.MSG_APP_ID_ONLINE_RADIO);
        return true;
    }

    @Override
    public boolean onAudioRecordStart(Object... args) {
        VRClientManager.getInstance().stopRecord();
        return true;
    }

    @Override
    public boolean onAudioRecordStop(Object... args) {
        VRClientManager.getInstance().startRecord();
        return true;
    }

    @Override
    public void setVrStatusListener(OnVRStatusListener onVRStatusListener) {
        mOnVRStatusListener = onVRStatusListener;
    }

    @Override
    public KradioRecorderInterface getRecorder() {
        return null;
    }
}
