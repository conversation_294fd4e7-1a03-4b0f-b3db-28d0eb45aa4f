package com.kaolafm.kradio.coin;

import com.kaolafm.kradio.lib.utils.KaolaAppConfigData;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.opensdk.account.token.AccessTokenManager;
import com.kaolafm.opensdk.account.token.KaolaAccessToken;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.socket.SocketListener;
import java.util.Map;
import kotlin.jvm.internal.Intrinsics;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;


public final class CoinSocketListener implements SocketListener {

    private String KEY_OPEN_UID;
    private String KEY_APP_ID;
    private HttpCallback callback;

    public CoinSocketListener(@NotNull HttpCallback callback) {
        super();
        this.callback = callback;
        this.KEY_OPEN_UID = "open_uid";
        this.KEY_APP_ID = "appid";
    }

    @NotNull
    public final String getKEY_OPEN_UID() {
        return this.KEY_OPEN_UID;
    }

    @NotNull
    public final String getKEY_APP_ID() {
        return this.KEY_APP_ID;
    }

    public void onSuccess(@Nullable CoinBean coinBean) {
        Logger.i("coin", "coinbean:" + coinBean + ' ');
        AccessTokenManager var10000 = AccessTokenManager.getInstance();
        Intrinsics.checkExpressionValueIsNotNull(var10000, "AccessTokenManager.getInstance()");
        KaolaAccessToken var3 = var10000.getKaolaAccessToken();
        Intrinsics.checkExpressionValueIsNotNull(var3, "AccessTokenManager.getInstance().kaolaAccessToken");
        String userId = var3.getUserId();
        if (Intrinsics.areEqual(userId, coinBean != null ? coinBean.getUid() : null)) {
            this.callback.onSuccess(coinBean != null ? coinBean.getIntegral() : null);
        } else {
            this.callback.onSuccess(0);
        }

    }

    // $FF: synthetic method
    // $FF: bridge method
    public void onSuccess(Object var1) {
        this.onSuccess((CoinBean) var1);
    }

    @NotNull
    public String getEvent() {
        return "integralRefresh";
    }

    @NotNull
    public Map getParams(@Nullable Map params) {
        AccessTokenManager var10000 = AccessTokenManager.getInstance();
        Intrinsics.checkExpressionValueIsNotNull(var10000, "AccessTokenManager.getInstance()");
        KaolaAccessToken var3 = var10000.getKaolaAccessToken();
        Intrinsics.checkExpressionValueIsNotNull(var3, "AccessTokenManager.getInstance().kaolaAccessToken");
        String userId = var3.getUserId();
        if (params == null) {
            Intrinsics.throwNpe();
        }

        String var10001 = this.KEY_APP_ID;
        KaolaAppConfigData var10002 = KaolaAppConfigData.getInstance();
        Intrinsics.checkExpressionValueIsNotNull(var10002, "KaolaAppConfigData.getInstance()");
        String var4 = var10002.getAppId();
        Intrinsics.checkExpressionValueIsNotNull(var4, "KaolaAppConfigData.getInstance().appId");
        params.put(var10001, var4);
        if (!StringUtil.isEmpty(userId)) {
            var10001 = this.KEY_OPEN_UID;
            Intrinsics.checkExpressionValueIsNotNull(userId, "userId");
            params.put(var10001, userId);
        }

        return params;
    }

    public boolean isNeedParams() {
        return false;
    }

    public boolean isNeedRequest() {
        return true;
    }

    public void onError(@Nullable ApiException error) {
        Logger.i("coin", "error:" + error + ' ');
        this.callback.onError(error);
    }


}
