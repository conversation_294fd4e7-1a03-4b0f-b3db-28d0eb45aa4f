package com.kaolafm.kradio.home.comprehensive.mvp;


import com.kaolafm.kradio.lib.base.mvp.IView;
import com.kaolafm.opensdk.api.personalise.model.InterestTag;

import com.kaolafm.opensdk.http.error.ApiException;
import java.util.ArrayList;
import java.util.List;

public class HorizontalPersonlityRecommendationPresenterContact {

    public interface View extends IView {

        void saveError(ApiException exception);

        void saveSuccess(String msg);

        void showFirst(List<InterestTag> interestTags);

        void hideLoading();

        void showError(String s);

    }

    public interface Presenter extends IPresenter {
    }

    public interface IPresenter {

        void saveInterestTags(ArrayList<String> tagList);
    }
}
