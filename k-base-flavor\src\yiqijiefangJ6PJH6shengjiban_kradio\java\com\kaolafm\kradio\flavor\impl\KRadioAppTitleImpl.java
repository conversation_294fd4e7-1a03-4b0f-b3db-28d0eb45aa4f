package com.kaolafm.kradio.flavor.impl;

import android.view.ViewStub;

import com.kaolafm.kradio.lib.base.flavor.KRadioAppTitleInter;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-11-20 11:03
 ******************************************/
public class KRadioAppTitleImpl implements KRadioAppTitleInter {
    @Override
    public boolean initAppTitle(Object... args) {
//        Activity activity = (Activity) args[0];
        ViewStub viewStub = (ViewStub) args[1];
        viewStub.inflate();
//        TextView titleTextView = activity.findViewById(R.id.common_textView);
//        titleTextView.setTextAppearance(activity, R.style.customer_title_text_style);
        return true;
    }
}