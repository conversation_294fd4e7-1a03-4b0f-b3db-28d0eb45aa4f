package com.kaolafm.kradio.lib.base.flavor;

/******************************************
 * 类描述:
 * 大众
 *当“隐私模式”处于开启状态下，用户打开“云听”App，“云听”App将处于无网络、不可用状态，且在前端弹出popup，提示“抱歉，位置/语音服务开关已关闭，如需使用请前往设置中打开”，后续执行方案A-2相关步骤。(详情请见PPT《location data_最终方案_V05》)
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-11-20 11:03
 ******************************************/
public interface KRadioHubInitInter {
    boolean hasPrivacyMode(Object... args);
}
