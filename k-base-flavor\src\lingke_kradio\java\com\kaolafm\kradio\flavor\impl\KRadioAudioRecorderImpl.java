package com.kaolafm.kradio.flavor.impl;

import android.util.Log;

import com.ecarx.sdk.openapi.ECarXApiClient;
import com.ecarx.sdk.policy.IAudioPolicy;
import com.ecarx.sdk.policy.IMicResourceCallback;
import com.ecarx.sdk.policy.PolicyAPI;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioRecorderInter;
import com.kaolafm.kradio.lib.base.flavor.live.KradioRecorderInterface;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: Jacklee
 * @time: 2021-03-15 14:28
 ******************************************/
public final class KRadioAudioRecorderImpl implements KRadioAudioRecorderInter {
    private static final String TAG = "KRadioAudioRecorderImpl";
    private OnVRStatusListener mOnVRStatusListener;
    private IAudioPolicy mIAudioPolicy;

    private MyIMicResourceCallback myIMicResourceCallback;

    public KRadioAudioRecorderImpl() {
        try {
            final PolicyAPI policyAPI = PolicyAPI.createPolicyAPI(AppDelegate.getInstance().getContext());
            policyAPI.init(AppDelegate.getInstance().getContext(), new ECarXApiClient.Callback() {
                @Override
                public void onAPIReady(boolean b) {
                    if (b){
                        Log.i(TAG, "PolicyAPI  初始化成功");
                        if (policyAPI != null) {
                            mIAudioPolicy = policyAPI.getAudioPolicy();
                        }
                    }else {
                        Log.i(TAG, "PolicyAPI  初始化失败");
                    }
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
        myIMicResourceCallback = new MyIMicResourceCallback();
    }

    @Override
    public boolean initVR(Object... args) {
        return false;
    }

    @Override
    public boolean onAudioRecordStart(Object... args) {
        if (mIAudioPolicy == null) {
            Log.i(TAG, "onAudioRecordStart------->mIAudioPolicy = null");
            return false;
        }
        // 向车机系统申请使用MIC
        boolean flag = false;
        try {
            flag = mIAudioPolicy.requestMic(IAudioPolicy.REQUEST_TYPE_RECORD, myIMicResourceCallback);
        } catch (Exception e) {
            e.printStackTrace();
        }
        Log.i(TAG, "onAudioRecordStart------->flag = " + flag);
        return flag;
    }

    @Override
    public boolean onAudioRecordStop(Object... args) {
        if (mIAudioPolicy == null) {
            Log.i(TAG, "onAudioRecordStop------->mIAudioPolicy = null");
            return false;
        }
        // 向车机系统申请释放MIC
        boolean flag = false;
        try {
            flag = mIAudioPolicy.releaseMic(myIMicResourceCallback);
        } catch (Exception e) {
            e.printStackTrace();
        }
        Log.i(TAG, "onAudioRecordStop------->flag = " + flag);
        return flag;
    }

    @Override
    public boolean onAudioRecordStopAfter(Object... args) {
        return false;
    }

    @Override
    public void setVrStatusListener(OnVRStatusListener onVRStatusListener) {
        mOnVRStatusListener = onVRStatusListener;
    }

    @Override
    public KradioRecorderInterface getRecorder() {
        return null;
    }

    private class MyIMicResourceCallback implements IMicResourceCallback {

        @Override
        public void onAbort(int i) {
            Log.i(TAG, "onAbort------->i = " + i);
        }

        @Override
        public void onResume() {
            Log.i(TAG, "onResume------->");
        }
    }
}
