package com.kaolafm.kradio.profile;

import android.annotation.SuppressLint;
import android.app.Application;
import android.content.Context;
import android.os.Environment;
import android.text.TextUtils;
import com.google.gson.Gson;
import com.kaolafm.base.utils.FileUtil;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.KaolaAppConfigData;
import com.kaolafm.kradio.user.channel.Channel;
import com.kaolafm.kradio.user.channel.SwitchChannelFragment;
import com.kaolafm.opensdk.account.profile.KaolaProfileManager;
import io.reactivex.Single;
import io.reactivex.SingleObserver;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;
import java.io.File;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;

/**
 * AOP方法修改各种信息。
 * <AUTHOR>
 * @date 2019-05-27
 */
@Aspect
public class AmendProfileAspect {

    private Channel mChannel;

    /**
     * 在指定的SDK方法执行完之后执行该方法
     * 加载完AppId等相关信息后重新赋值。
     * @param point
     * @throws Throwable
     */
    @After("execution(* com.kaolafm.opensdk.account.profile.KaolaProfileManager.loadProfile(..))")
    public void amend(JoinPoint point) throws Throwable {
        KaolaProfileManager profileManager = (KaolaProfileManager) point.getTarget();
        if (profileManager != null) {
            if (mChannel != null) {
                String packageName = mChannel.getPackageName();
                String appId = mChannel.getAppId();
                String appKey = mChannel.getAppKey();
                String channel = mChannel.getChannel();
                channel = channel == null ? "" : channel;
                profileManager.setPackageName(packageName);
                profileManager.setAppId(appId);
                profileManager.setAppKey(appKey);
                profileManager.setChannel(channel);
                KaolaAppConfigData configData = KaolaAppConfigData.getInstance();
                configData.setAppId(appId);
                configData.setAppKey(appKey);
                configData.setChannel(channel);
                configData.setPackageName(packageName);
            }
        }
    }

    /**
     * 在指定有返回的方法后执行该方法。
     * 将返回值赋值到该方法的参数中。
     * @param processName
     * @param point
     * @throws Throwable
     */
    @AfterReturning(pointcut = "execution(* com.kaolafm.kradio.lib.utils.AppUtil.getCurrentProcessName(..))", returning = "processName")
    public void readChannel(String processName, JoinPoint point) throws Throwable {
        Context context = (Context) point.getArgs()[0];
        if (context != null) {
            String packageName = context.getPackageName();
            //只在主进程
            if (TextUtils.equals(packageName, processName)) {
                initChannel();
            }
        }
    }

    @SuppressLint("MissingPermission")
    private void initChannel() {
        Single.fromCallable(() -> {
                String path = "/data/data/com.edog.car";
                String dataPath = path + File.separator + SwitchChannelFragment.KAOLA_AUTO_BASE + File.separator
                        + SwitchChannelFragment.UDID_PATH_KAOLA_API
                        + File.separator + "channel.txt";
                String channelStr = FileUtil.readFileFromSDCard(dataPath).trim();
                Channel channel = new Gson().fromJson(channelStr, Channel.class);
                if (channel != null) {
                    Application context = AppDelegate.getInstance().getContext();
                    String androidClientPackage = channel.getPackageName();
                    String packageName = context.getPackageName();
                    if (!TextUtils.equals(androidClientPackage, packageName)) {
                        mChannel = channel;
                    }
                }

            return "";
        }).observeOn(AndroidSchedulers.mainThread())
                .subscribeOn(Schedulers.io())
                .subscribe(new SingleObserver<String>() {

                    private Disposable mDisposable;

                    @Override
                    public void onSubscribe(Disposable d) {
                        mDisposable = d;
                    }

                    @Override
                    public void onSuccess(String s) {
                        if (mDisposable != null) {
                            mDisposable.dispose();
                        }
                    }

                    @Override
                    public void onError(Throwable e) {
                        if (mDisposable != null) {
                            mDisposable.dispose();
                        }
                    }
                });

    }
}
