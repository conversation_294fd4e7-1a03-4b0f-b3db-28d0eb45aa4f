package com.kaolafm.kradio.auto.appwidget.remoteviews;

import android.graphics.Bitmap;

import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;


public class RemoteData {
    public PlayItem mPlayItem;
    public Bitmap mCurrentBitmap;
    public Bitmap mBlurBitmap;
    public String livingTime = "";
    public boolean subscription = false;
    private  int appWidgetId;

    public int getAppWidgetId() {
        return appWidgetId;
    }

    public void setAppWidgetId(int appWidgetId) {
        this.appWidgetId = appWidgetId;
    }

    private RemoteData(Builder builder) {
        mPlayItem = builder.mPlayItem;
        mCurrentBitmap = builder.mCurrentBitmap;
        mBlurBitmap = builder.mBlurBitmap;
        livingTime = builder.livingTime;
        subscription = builder.subscription;
    }

    public static final class Builder {
        private PlayItem mPlayItem;
        private Bitmap mCurrentBitmap;
        private Bitmap mBlurBitmap;
        private String livingTime;
        private boolean subscription;

        public Builder() {
        }

        public Builder mPlayItem(PlayItem val) {
            mPlayItem = val;
            return this;
        }

        public Builder mCurrentBitmap(Bitmap val) {
            mCurrentBitmap = val;
            return this;
        }

        public Builder mBlurBitmap(Bitmap val) {
            mBlurBitmap = val;
            return this;
        }

        public Builder livingTime(String val) {
            livingTime = val;
            return this;
        }

        public Builder subscription(boolean val) {
            subscription = val;
            return this;
        }

        public RemoteData build() {
            return new RemoteData(this);
        }
    }
}
