package com.kaolafm.kradio.common.base;

import android.app.Activity;
import android.app.Application;
import android.content.Context;

import java.util.LinkedList;

/**
 * <AUTHOR>
 * @date 2019-07-10
 */
public class BaseApplication extends Application {
    public static LinkedList<Activity> mList = new LinkedList<>();

    protected BaseApplication() {
    }

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
    }

    @Override
    public void onCreate() {
        super.onCreate();
    }

    @Override
    public void onTerminate() {
        super.onTerminate();
    }

    @Override
    public void onLowMemory() {
        super.onLowMemory();
    }

    @Override
    public void onTrimMemory(int level) {
        super.onTrimMemory(level);
    }


    public void exitApplication() {
        for (int i = 0; i < mList.size(); ++i) {
            if (mList.get(i) != null) {
                mList.get(i).finish();
            }
        }

        mList.clear();
    }

    public void addActivity(Activity activity) {
        if (null != activity) {
            mList.add(activity);
        }

    }

    public void removeActivity(Activity activity) {
        mList.remove(activity);
    }
}
