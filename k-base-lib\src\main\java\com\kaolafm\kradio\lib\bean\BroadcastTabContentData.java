package com.kaolafm.kradio.lib.bean;

public class BroadcastTabContentData {
    private int type;
    private String name;
    private int tabId;

    /**
     * 电台Id
     */
    private long broadcastId;
    /**
     * 是否正在播放
     */
    private boolean isPlaying;
    /**
     * 播放数
     */
    private int playTimes;
    /**
     * 频率
     */
    private String freq;
    /**
     * 来源icon
     */
    private String icon;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getName() {
        return name == null ? "" : name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getTabId() {
        return tabId;
    }

    public void setTabId(int tabId) {
        this.tabId = tabId;
    }

    public long getBroadcastId() {
        return broadcastId;
    }

    public void setBroadcastId(long broadcastId) {
        this.broadcastId = broadcastId;
    }

    public boolean isPlaying() {
        return isPlaying;
    }

    public void setPlaying(boolean playing) {
        isPlaying = playing;
    }

    public int getPlayTimes() {
        return playTimes;
    }

    public void setPlayTimes(int playTimes) {
        this.playTimes = playTimes;
    }

    public String getFreq() {
        return freq == null ? "" : freq;
    }

    public void setFreq(String freq) {
        this.freq = freq;
    }

    public String getIcon() {
        return icon == null ? "" : icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }
}
