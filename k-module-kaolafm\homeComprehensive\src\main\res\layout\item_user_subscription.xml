<?xml version="1.0" encoding="utf-8"?>
<com.kaolafm.kradio.common.widget.CScaleRelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_broadcast_list_item">

    <LinearLayout xmlns:sfl="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <com.kaolafm.kradio.lib.widget.SquareFrameLayout
            android:layout_width="@dimen/m114"
            android:layout_height="@dimen/m114"
            sfl:canScale="false">

            <View
                android:layout_width="@dimen/m114"
                android:layout_height="@dimen/m114"
                android:background="@drawable/sh_bg_r8l_fff" />

            <com.kaolafm.kradio.component.ui.base.view.OvalImageView
                android:id="@+id/iv_play_cover"
                android:layout_width="@dimen/m114"
                android:layout_height="@dimen/m114"
                android:contentDescription="@null"
                android:scaleType="fitXY"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:rid_type="4"
                tools:src="@drawable/ic_launcher" />

            <com.kaolafm.kradio.component.ui.base.view.OvalImageView
                android:id="@+id/user_subscription_offline"
                android:layout_width="@dimen/m114"
                android:layout_height="@dimen/m114"
                android:layout_centerInParent="true"
                android:background="@drawable/offline_layer"
                android:src="@drawable/offline"
                app:rid_type="4" />
            <!--描边，日间模式有-->
            <View
                android:layout_width="@dimen/m114"
                android:layout_height="@dimen/m114"
                android:background="@drawable/sh_bg_r8l"
                android:visibility="gone" />
        </com.kaolafm.kradio.lib.widget.SquareFrameLayout>

        <FrameLayout
            android:id="@+id/fl_item_user_subscription_info_root"
            android:layout_width="match_parent"
            android:layout_height="@dimen/m114"
            android:gravity="center_vertical"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginLeft="@dimen/x30"
                android:layout_marginRight="@dimen/x30"
                android:gravity="center_vertical"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="@dimen/x50"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <com.kaolafm.kradio.component.ui.base.view.RateView
                        android:id="@+id/user_layout_playing"
                        android:layout_width="@dimen/m28"
                        android:layout_height="@dimen/m28"
                        app:lottie_autoPlay="true"
                        android:layout_marginRight="@dimen/x13"
                        app:lottie_fileName="lottie/rate.json"
                        app:lottie_loop="true" />

                    <TextView
                        android:id="@+id/user_tab_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:singleLine="true"
                        android:textColor="@color/user_item_title_text_color"
                        android:textSize="@dimen/text_size_title7"
                        tools:text="垃圾堆里附近阿里" />
                </LinearLayout>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/x6"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/ivPlay"
                        android:layout_width="@dimen/m13"
                        android:layout_height="@dimen/m18"
                        android:layout_marginEnd="@dimen/m10"
                        android:src="@drawable/sl_item_subcategory_broadcast_subimg" />

                    <TextView
                        android:id="@+id/user_tab_content"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:singleLine="true"
                        android:textColor="@color/sl_item_subcategory_broadcast_subtxt"
                        android:textSize="@dimen/text_size2"
                        tools:text="lajdfljaldfjl" />
                </LinearLayout>


            </LinearLayout>

            <ImageView
                android:id="@+id/iv_subscription_subscribe"
                android:layout_width="@dimen/x82"
                android:layout_height="match_parent"
                android:layout_gravity="end"
                android:contentDescription="@null"
                android:padding="@dimen/x24"
                android:scaleType="centerInside"
                android:src="@drawable/playerbar_collect" />

        </FrameLayout>


    </LinearLayout>

    <com.kaolafm.kradio.component.ui.base.view.OvalImageView
        android:id="@+id/vip_icon"
        android:layout_width="@dimen/m51"
        android:layout_height="@dimen/m25"
        app:rid_type="1"
        tools:src="@drawable/comprehensive_icon_vip" />
</com.kaolafm.kradio.common.widget.CScaleRelativeLayout>