package com.kaolafm.kradio.lib.bean;

/**
 * 历史item接口。为了统一登录和未登录item，方便转换
 *
 * <AUTHOR>
 * @date 2020/9/7
 */
public interface Itemable {

    long getRadioUpdateTime();

    void setRadioUpdateTime(long radioUpdateTime);

    String getCurrentProgramName();

    void setCurrentProgramName(String currentProgramName);

    String getType();

    void setType(String type);

    String getRadioId();

    void setRadioId(String radioId);

    int getTypeId();

    void setTypeId(int typeId);

    String getRadioTitle();

    void setRadioTitle(String radioTitle);

    String getPicUrl();

    void setPicUrl(String picUrl);

    String getAudioId();

    void setAudioId(String audioId);

    String getAudioTitle();

    void setAudioTitle(String audioTitle);

    String getPlayUrl();

    void setPlayUrl(String playUrl);

    long getPlayedTime();

    void setPlayedTime(long playedTime);

    long getDuration();

    void setDuration(long duration);

    void setSourceUrl(String sourceUrl);

    boolean isOffline();

    void setOffline(boolean isOffline);

    long getTimeStamp();

    void setTimeStamp(long timeStamp);

    long getOrderNum();

    void setOrderNum(long orderNum);

    String getOfflinePlayUrl();

    void setOfflinePlayUrl(String offlinePlayUrl);

    String getShareUrl();

    void setShareUrl(String shareUrl);

    int getCategoryId();

    void setCategoryId(int categoryId);

    String getParamTwo();

    void setParamTwo(String paramTwo);

    String getParamOne();

    void setParamOne(String paramOne);

    String getSourceUrl();

    int getOrderMode();

    void setOrderMode(int orderMode);

    boolean isPlaying();

    void setPlaying(boolean isPlaying);

    int getFine();

    void setFine(int fine);

    int getVip();

    void setVip(int vip);

    String getFreq();

    void setFreq(String freq);

    long getListenCount();

    void setListenCount(long listenCount);

    int getBroadcastSort();

    void setBroadcastSort(int broadcastSort);
}
