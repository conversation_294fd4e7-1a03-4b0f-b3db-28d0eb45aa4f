package com.kaolafm.kradio.http;//package com.kaolafm.kradio.http;
//
//import android.view.View;
//import com.kaolafm.base.utils.SpUtil;
//import com.kaolafm.kradio.flavor.R;
//import com.kaolafm.kradio.lib.base.AppDelegate;
//import com.kaolafm.kradio.lib.toast.ToastUtil;
//import org.aspectj.lang.JoinPoint;
//import org.aspectj.lang.ProceedingJoinPoint;
//import org.aspectj.lang.annotation.After;
//import org.aspectj.lang.annotation.Around;
//import org.aspectj.lang.annotation.Aspect;
//
///**
// * 用于切换sdk的http和https
// *
// * <AUTHOR> <PERSON>
// * @date 2019-06-18
// */
//@Aspect
//public class HttpAspect {
//
////    @Around("execution(* com.kaolafm.opensdk.Options.isUseHttps(..))")
////    public boolean changeToHttp(ProceedingJoinPoint point) throws Throwable {
////        SpUtil.init(AppDelegate.getInstance().getContext());
////        return SpUtil.getBoolean("useHttps", true);
////    }
//
//    @After("execution(* PerformanceSettingFragment.initView(..))")
//    public void onClick(JoinPoint point) throws Throwable {
//        View view = (View) point.getArgs()[0];
//        if (view != null) {
//            view.findViewById(R.id.login_title).setOnClickListener(v -> {
//                boolean useHttps = SpUtil.getBoolean("useHttps", true);
//                ToastUtil.showInfo(AppDelegate.getInstance().getContext(), "切换到" + (useHttps ? "Http" : "Https"));
//                SpUtil.putBoolean("useHttps", !useHttps);
//            });
//        }
//    }
//}
