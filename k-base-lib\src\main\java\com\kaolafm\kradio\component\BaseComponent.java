package com.kaolafm.kradio.component;

import android.text.TextUtils;
import java.util.HashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 继承该类的实现接口
 * <AUTHOR>
 * @date 2019-10-23
 */
@SharedConst
public abstract class BaseComponent implements Component {

    private AtomicBoolean initialized = new AtomicBoolean(false);

    protected final HashMap<String, ActionProcessor> processors = new HashMap<>();

    /**
     * 在该方法中调用addProcessor方法添加ActionProcessor的实现类。
     */
    protected abstract void initProcessors();

    /**
     * 添加action处理类。该方法会在initProcessors()中调用。使用{@link ActionProcessor#actionName()}做key。
     * @param processor
     */
    protected void addProcessor(ActionProcessor processor) {
        if (processor != null) {
            processors.put(processor.actionName(), processor);
        }
    }

    /**
     * 添加action处理类。使用自定义的actionName做key。
     * @param actionName
     * @param processor
     */
    protected void addProcessor(String actionName, ActionProcessor processor) {
        if (processor != null && !TextUtils.isEmpty(actionName)) {
            processors.put(actionName, processor);
        }
    }

    @Override
    public boolean onCall(RealCaller caller) {
        if (initialized.compareAndSet(false, true)) {
            synchronized (processors) {
                initProcessors();
            }
        }
        ActionProcessor actionProcessor = processors.get(caller.actionName());
        if (actionProcessor != null) {
            return actionProcessor.onAction(caller);
        }
        return false;
    }
}
