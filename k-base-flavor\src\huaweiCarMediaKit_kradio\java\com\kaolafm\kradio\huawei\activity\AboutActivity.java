package com.kaolafm.kradio.huawei.activity;

import android.os.Bundle;

import com.kaolafm.BaseSkinAppCompatActivity;
import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.huawei.utils.Constant;
import com.kaolafm.kradio.lib.base.mvp.IPresenter;
import com.kaolafm.kradio.setting.UserCenterAboutUsFragment;
import com.trello.rxlifecycle3.LifecycleTransformer;

public class AboutActivity extends BaseSkinAppCompatActivity {
    public static final String TAG = Constant.TAG;

    @Override
    protected IPresenter createPresenter() {
        return null;
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_about;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        getSupportFragmentManager()    //
                .beginTransaction()
                .add(R.id.fragment_container,new UserCenterAboutUsFragment())
                .commit();
    }

    @Override
    public void initData() {

    }

    @Override
    public LifecycleTransformer bindUntilEvent(Object event) {
        return null;
    }
}