package com.kaolafm.kradio.aop;

import android.util.Log;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;

/**
 * @Package: com.kaolafm.kradio.aop
 * @Description:
 * @Author: Maclay
 * @Date: 17:51
 */
@Aspect
public class DeviceUtilAop {
    private static final String TAG = "DeviceUtilAop";

    @Around("execution(* com.kaolafm.base.utils.DeviceUtil.getImei(..))")
    public String DeviceUtil_getImei(ProceedingJoinPoint point) throws Throwable {
        Log.d(TAG, "DeviceUtil_getImei");
        return "";
    }
}
