package com.kaolafm.kradio.flavor.receiver;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;
import android.view.KeyEvent;

import com.incall.proxy.constant.SettingsConstantsDef;
import com.kaolafm.utils.MediaButtonManagerUtil;

import static com.incall.proxy.constant.SettingsConstantsDef.COAGENT_ACTION_KEY_CHANGED;
import static com.incall.proxy.constant.SettingsConstantsDef.EXTRA_KEY_CODE;
import static com.incall.proxy.constant.SettingsConstantsDef.EXTRA_KEY_STATE;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-05-22 21:01
 ******************************************/
public final class KRMediaButtonBroadcastReceiver extends BroadcastReceiver {
    private static final String TAG = "KRMediaButtonBroadcastReceiver";

    private MediaButtonManagerUtil mMediaButtonManagerUtil = new MediaButtonManagerUtil();

    @SuppressLint("LongLogTag")
    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();

        if (COAGENT_ACTION_KEY_CHANGED.equals(action)) {
            // 多媒体按键事件码
            SettingsConstantsDef.KeyCode keyCode;
            SettingsConstantsDef.KeyState keyState;
            try {
                keyCode = SettingsConstantsDef.KeyCode.valueOf(intent.getStringExtra(EXTRA_KEY_CODE));

                keyState = SettingsConstantsDef.KeyState.valueOf(intent.getStringExtra(EXTRA_KEY_STATE));
            } catch (Exception e) {
                e.printStackTrace();
                Log.i(TAG, "onReceive----->keyCode error！");
                return;
            }

            Log.i(TAG, "onReceive----->keyCode = " + keyCode + "--keyState = " + keyState);
            if (keyState == SettingsConstantsDef.KeyState.UP) {
                int tempKeyCode = -1;
                if (keyCode == SettingsConstantsDef.KeyCode.PAUSE) {
                    tempKeyCode = KeyEvent.KEYCODE_MEDIA_PAUSE;
                } else if (keyCode == SettingsConstantsDef.KeyCode.PLAY) {
                    tempKeyCode = KeyEvent.KEYCODE_MEDIA_PLAY;
                } else if (keyCode == SettingsConstantsDef.KeyCode.PLAYPAUSE) {
                    tempKeyCode = KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE;
                } else if (keyCode == SettingsConstantsDef.KeyCode.STOP) {
                    tempKeyCode = KeyEvent.KEYCODE_MEDIA_STOP;
                } else if (keyCode == SettingsConstantsDef.KeyCode.NEXT) {
                    tempKeyCode = KeyEvent.KEYCODE_MEDIA_NEXT;
                } else if (keyCode == SettingsConstantsDef.KeyCode.PRE) {
                    tempKeyCode = KeyEvent.KEYCODE_MEDIA_PREVIOUS;
                }
                Log.i(TAG, "onReceive----->tempKeyCode = " + tempKeyCode);
                mMediaButtonManagerUtil.manageMediaButtonClick(tempKeyCode);
            }
        }
    }
}
