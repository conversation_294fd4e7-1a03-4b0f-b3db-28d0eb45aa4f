<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/online_page_bg_vague"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical">

        <ImageView
            android:id="@+id/message_back_mine"
            android:layout_width="@dimen/m48"
            android:layout_height="@dimen/m48"
            android:layout_marginStart="@dimen/x64"
            android:layout_marginTop="@dimen/y36"
            android:background="@color/transparent"
            android:clickable="true"
            android:focusable="true"
            android:padding="@dimen/m6"
            android:scaleType="centerInside"
            android:src="@drawable/online_player_ic_back"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/y40"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/online_message_activity_title_text"
            android:textColor="@color/online_message_title_text_color"
            android:textSize="@dimen/m28"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatImageButton
            android:id="@+id/msg_tips_ib"
            android:layout_width="@dimen/m44"
            android:layout_height="@dimen/m44"
            android:layout_marginTop="@dimen/m38"
            android:layout_marginEnd="@dimen/m66"
            android:background="@color/transparent"
            android:padding="@dimen/m6"
            android:scaleType="fitCenter"
            android:src="@drawable/msg_tips_icon"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/y1"
        android:layout_marginTop="@dimen/y32"
        android:background="@color/online_message_line_color" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/online_msg_rv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingLeft="@dimen/x80"
        android:paddingRight="@dimen/x80"
        android:scrollbars="none" />
</LinearLayout>