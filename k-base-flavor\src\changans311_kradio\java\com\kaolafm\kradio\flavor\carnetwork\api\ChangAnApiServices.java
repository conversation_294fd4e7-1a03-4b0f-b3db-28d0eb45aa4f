package com.kaolafm.kradio.flavor.carnetwork.api;

import com.kaolafm.kradio.flavor.carnetwork.carnetworkutils.ChanganAuthConstants;
import com.kaolafm.opensdk.api.BaseResult;

import io.reactivex.Single;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.Path;

interface ChangAnApiServices {
    //    @GET("/changan/service/authv2/{json}")
    @Headers({"Domain-Name:changan"})
    @GET(ChanganAuthConstants.REQUEST_GET_AUTH)
    Single<BaseResult> getChangAnAuthInfo(@Path("json") String json);
}
