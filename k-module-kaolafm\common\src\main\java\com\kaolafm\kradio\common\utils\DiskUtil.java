package com.kaolafm.kradio.common.utils;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Environment;
import android.util.Log;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.flavor.KRadioFreeSpace;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;

public class DiskUtil {
    public static String TAG = "freeSpace";
    private static boolean isEnoughSpace = false;
    /**
     * Check free space boolean.
     *
     * @return the boolean
     */
    public static boolean checkFreeSpace(Context ctx) {
        final KRadioFreeSpace KRadioFreeSpaceInter = ClazzImplUtil.getInter("KRadioFreeSpaceImpl");
        if(KRadioFreeSpaceInter!=null){
            isEnoughSpace = KRadioFreeSpaceInter.checkFreeSpace(ctx);
        }else{
            int minimum = 20; //要求sd卡最少可用空间已M为单位
            isEnoughSpace = checkFreeSpaceDefault(minimum);
        }
        return isEnoughSpace;
    }

    /**
     * Check free space default boolean.
     *
     * @return the boolean
     */
    public static boolean checkFreeSpaceDefault(int minimum) {
        long freeSpace = getSDFreeSpace();
        Log.i(TAG,"freeSpace = "+freeSpace);
        if (freeSpace > minimum) {
            return true;
        } else {
            return false;
        }
    }

    public static long getSDFreeSpace() {
        long freeSpace = Environment.getExternalStorageDirectory().getFreeSpace();
        long usableSpace = Environment.getExternalStorageDirectory().getUsableSpace();
        long totalSpace = Environment.getExternalStorageDirectory().getTotalSpace();
        Log.i(TAG, "onCreate: 剩余空间大小："+freeSpace/1024/1024+" 可用大小:"+usableSpace/1024/1024+" 总空间大小:"+totalSpace/1024/1024 );
        return usableSpace/1024/1024;
    }

    public static void createDialog(Activity act){
        AlertDialog.Builder builder = new AlertDialog.Builder(act);
        builder.setTitle(act.getResources().getString(R.string.no_free_space_dialog_title));
        builder.setMessage(act.getResources().getString(R.string.no_free_space_dialog_content));
        builder.setNeutralButton(act.getResources().getString(R.string.no_free_space_dialog_btn), new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                // 添加详细的退出日志
                android.util.Log.e("DiskUtil", "=== 应用退出被触发 ===");
                android.util.Log.e("DiskUtil", "方法: DiskUtil.showNoFreeSpaceDialog()");
                android.util.Log.e("DiskUtil", "原因: 用户点击磁盘空间不足对话框按钮");
                android.util.Log.e("DiskUtil", "调用栈:", new Exception("退出调用栈"));
                android.util.Log.e("DiskUtil", "=== 即将调用 System.exit(0) ===");

                System.exit(0);
            }
        });
        builder.create().show();
    }
}
