package com.kaolafm.kradio.lib.bean;

import com.google.gson.Gson;
import com.kaolafm.opensdk.api.CrashMessageButtonActionBean;

import org.greenrobot.greendao.converter.PropertyConverter;

public class CrashMessageButtonActionBeanConverter implements PropertyConverter<CrashMessageButtonActionBean, String> {

    private static Gson gson = new Gson();
    @Override
    public CrashMessageButtonActionBean convertToEntityProperty(String databaseValue) {
        return gson.fromJson(databaseValue, CrashMessageButtonActionBean.class);
    }

    @Override
    public String convertToDatabaseValue(CrashMessageButtonActionBean entityProperty) {
        return gson.toJson(entityProperty);
    }
}
