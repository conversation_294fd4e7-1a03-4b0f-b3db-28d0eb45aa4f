<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/item_root"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <com.kaolafm.kradio.component.ui.base.view.OvalImageView
        android:id="@+id/item_activitys_bg_iv"
        android:layout_width="@dimen/m360"
        android:layout_height="@dimen/m360"
        android:src="@drawable/item_activitys_bg_pic"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="MissingConstraints" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="@dimen/m360"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true">

        <RelativeLayout
            android:id="@+id/pic_rl"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/pic_image"
                android:layout_width="@dimen/m100"
                android:layout_height="@dimen/m100"
                android:layout_centerInParent="true"
                android:background="@drawable/activity_item_img_bg"
                tools:background="@color/red01"
                tools:ignore="MissingConstraints" />

        </RelativeLayout>

        <TextView
            android:id="@+id/title_activity"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/y10"
            android:ellipsize="end"
            android:gravity="center"
            android:maxEms="8"
            android:maxLines="1"
            android:paddingLeft="@dimen/x5"
            android:paddingRight="@dimen/x5"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/m38"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/pic_rl"
            tools:ignore="MissingConstraints"
            tools:text="活动标题" />

        <TextView
            android:id="@+id/des_activity"
            android:layout_width="@dimen/x300"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="center"
            android:maxEms="12"
            android:layout_marginTop="@dimen/m5"
            android:maxLines="2"
            android:textColor="@color/color_2"
            android:textSize="@dimen/m20"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title_activity"
            tools:text="新年活动新年活动新年活动新年活动新年活动新年活动新年活动新年活动新年活动新年活动新年活动" />

        <TextView
            android:id="@+id/date_activity"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="center"
            android:maxEms="12"
            android:maxLines="1"
            android:layout_marginTop="@dimen/m6"
            android:textColor="@color/color_2"
            android:textSize="@dimen/m24"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/des_activity"
            tools:ignore="MissingConstraints"
            tools:text="2022.03.12" />

        <TextView
            android:layout_width="@dimen/x160"
            android:layout_height="@dimen/y48"
            android:text="查看详情"
            android:gravity="center"
            android:layout_marginTop="@dimen/m11"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/m22"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/date_activity" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/qr_view_expire"
        android:layout_width="@dimen/m360"
        android:layout_height="@dimen/m360"
        android:background="@drawable/item_expire_bg"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="MissingConstraints" />

    <com.kaolafm.kradio.component.ui.base.view.OvalImageView
        android:id="@+id/qr_expire_icon"
        android:layout_width="@dimen/m138"
        android:layout_height="@dimen/m100"
        android:layout_centerInParent="true"
        android:src="@drawable/qr_expire_icon"
        android:visibility="gone"
        tools:ignore="MissingConstraints" />
</RelativeLayout>