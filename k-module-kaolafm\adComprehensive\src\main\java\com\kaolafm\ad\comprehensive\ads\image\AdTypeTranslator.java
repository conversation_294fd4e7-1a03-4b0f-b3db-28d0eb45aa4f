package com.kaolafm.ad.comprehensive.ads.image;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.kaolafm.ad.api.model.Advert;
import com.kaolafm.ad.api.model.InteractionAdvert;

public class AdTypeTranslator {
    public enum AdType {
        COLLAPSEDIMAGE("collapsed", "com.kaolafm.ad.comprehensive.ads.image.AdCollapsedImageAdapter"),
        SKIPIMAGE("skip", "com.kaolafm.ad.comprehensive.ads.image.AdImageAdapter"),
        INTERACT_WITH_BANNER("banner", "com.kaolafm.ad.comprehensive.ads.image.AdInteractWithBannerAdapter"),
        INTERACT_WITH_WEBVIEW("webview", "com.kaolafm.ad.comprehensive.ads.image.AdInteractWithWebviewAdapter"),
        NTERACT_QR_CODE("qrcode", "com.kaolafm.ad.comprehensive.ads.image.AdQrCodeAdapter");

        @NonNull
        private final String mKey;
        @Nullable
        private final String mClassName;

        private AdType(String key, String className) {
            mKey = key;
            mClassName = className;
        }

        @Override
        public String toString() {
            return mClassName;
        }
    }

    private static final int AD_IMAGE = 4;
    private static final int AD_IMAGE_AUDIO = 5;

    public static String getAdImageTypeName(Advert advert) {
        if (advert instanceof InteractionAdvert) {
            return getInteractTypeName(((InteractionAdvert) advert).getInteractionType());
        }
        int type = advert.getType();
        return getSuspendTypeName(type);
    }

    private static String getSuspendTypeName(int adType) {
        if (adType == AD_IMAGE) {
            return AdType.SKIPIMAGE.toString();
        }
        return AdType.COLLAPSEDIMAGE.toString();
    }

    private static String getInteractTypeName(int interactionType) {
        if (interactionType == 1) {
            return AdType.INTERACT_WITH_BANNER.toString();
        }
        if (interactionType == 2) {
            return AdType.INTERACT_WITH_WEBVIEW.toString();
        }
        return AdType.NTERACT_QR_CODE.toString();
    }
}
