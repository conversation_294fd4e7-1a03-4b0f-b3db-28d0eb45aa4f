dependencies {
    implementation fileTree(dir: 'onlineHome/libs', include: ['*.jar'])
    implementation microModule(':common')
    implementation microModule(':scene')
    implementation microModule(':onlineUser')
    implementation microModule(':home')
    implementation microModule(':purchase')
    implementation microModule(':onlineCommon')
    implementation microModule(':onlinePlayer')
    implementation microModule(':onlineActivity')
    implementation microModule(':onlineCategories')
    implementation microModule(':onlineSearch')
    implementation microModule(':onlineSubscriptions')
    implementation microModule(':onlineHistory')
    implementation microModule(':onlineMine')
    implementation microModule(':message')
    implementation microModule(':player')
    implementation microModule(':mainTab')
    implementation microModule(':onlineAd')
}
