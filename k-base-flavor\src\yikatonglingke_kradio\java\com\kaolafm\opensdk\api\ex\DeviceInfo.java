package com.kaolafm.opensdk.api.ex;

import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 **/
public class DeviceInfo {

    /**
     * access_token : ff5dde1a-d6ac-4642-b594-ea42ffb45c17
     * refresh_token : a0959dbc-a063-4f77-9337-27e708668e1f
     * open_uid : Sj0unV27lfg
     * expires_in : 7200
     * scope : basis
     */

    @SerializedName("access_token")
    private String accessToken;
    @SerializedName("refresh_token")
    private String refreshToken;
    @SerializedName("open_uid")
    private String userId;
    @SerializedName("expires_in")
    private long refreshTime;
    @SerializedName("scope")
    private String scope;

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public long getRefreshTime() {
        return refreshTime;
    }

    public void setRefreshTime(long refreshTime) {
        this.refreshTime = refreshTime;
    }

    public String getScope() {
        return scope;
    }

    public void setScope(String scope) {
        this.scope = scope;
    }

    @Override
    public String toString() {
        return "DeviceInfo{" +
                "accessToken='" + accessToken + '\'' +
                ", refreshToken='" + refreshToken + '\'' +
                ", userId='" + userId + '\'' +
                ", refreshTime=" + refreshTime +
                ", scope='" + scope + '\'' +
                '}';
    }
}
