package com.kaolafm.kradio.common.web;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * 应用内跳转WebViewActivity的参数
 */
public class WebViewBean implements Parcelable {
    private String url;
    private String pageId;
    private String title;

    public WebViewBean(Parcel in) {
        url = in.readString();
        pageId = in.readString();
        title = in.readString();
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(url);
        dest.writeString(pageId);
        dest.writeString(title);
    }

    public static final Creator<WebViewBean> CREATOR = new Creator<WebViewBean>() {
        public WebViewBean createFromParcel(Parcel var1) {
            return new WebViewBean(var1);
        }

        public WebViewBean[] newArray(int var1) {
            return new WebViewBean[var1];
        }
    };

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getPageId() {
        return pageId;
    }

    public void setPageId(String pageId) {
        this.pageId = pageId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    @Override
    public String toString() {
        return "WebViewBean{" +
                "url='" + url + '\'' +
                ", pageId='" + pageId + '\'' +
                ", title='" + title + '\'' +
                '}';
    }
}
