package com.kaolafm.kradio.home.data;

import androidx.annotation.IntDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.List;
import java.util.Map;

/**
 * Created by v on 2018/4/10.
 */
public class Category {

    /**
     * 附加:类型
     */
    public static final int CONTROL_TYPE_NONE = 0;   //"LatestBroadcast";//这个栏目下你可以添加广播的最近收听历史。
    public static final int LATEST_BROADCAST = 1;   //"LatestBroadcast";//这个栏目下你可以添加广播的最近收听历史。
    public static final int FUNCTION_MODULE = 2;    //"FunctionModule";//这个栏目下你可以添加音乐下面的4个模块
    public static final int MY_MUSIC = 3;           //"MyMusic";//这个栏目下你可以添加功能入口；

    /**
     * 附加:类型
     */
    @Retention(RetentionPolicy.SOURCE)
    @IntDef({LATEST_BROADCAST, FUNCTION_MODULE, MY_MUSIC})
    public @interface AppControlType {
    }


    /**
     * 操作类型
     */
    public static final int OPERATE_TYPE_PLAY = 0;//即点即播
    public static final int OPERATE_TYPE_ENTRA = 1;// 功能入口
    public static final int OPERATE_TYPE_LIVE = 2;// 直播入口

    /**
     * 操作类型
     */
    @Retention(RetentionPolicy.SOURCE)
    @IntDef({Category.OPERATE_TYPE_ENTRA, Category.OPERATE_TYPE_PLAY, Category.OPERATE_TYPE_LIVE})
    public @interface OperateType {
    }


    /**
     * 卡片类型
     */
    public static final int CARD_TYPE_BIG = 1;//大卡片
    public static final int CARD_TYPE_SMALL_ALBUM = 2;//专辑,电台的小卡片
    public static final int CARD_TYPE_SMALL_FUNCTION = 3;//功能入口的小卡片
    public static final int CARD_TYPE_SMALL_BROADCAST = 4;//广播小卡片

    /**
     * 卡片类型
     */
    @Retention(RetentionPolicy.SOURCE)
    @IntDef({CARD_TYPE_SMALL_FUNCTION,
            CARD_TYPE_BIG,
            CARD_TYPE_SMALL_BROADCAST,
            CARD_TYPE_SMALL_ALBUM})
    public @interface CardType {
    }

    /**
     * 首页数据运营区域
     */
    public static final String HOME_DATE_ZONE_MAIN_PAGE = "mainPage";
    public static final String HOME_DATE_ZONE_NEWS_PAGE = "newsPage";
    public static final String HOME_DATE_ZONE_ALBUM_PAGE = "albumPage";
    public static final String HOME_DATE_ZONE_RADIO_PAGE = "radioPage";
    public static final String HOME_DATE_ZONE_TV_PAGE = "TVPage";
    public static final String HOME_DATE_ZONE_ACTIVITY_PAGE = "activityPage";


    /**
     * 广播分类
     */
    public static final String ID_RECOMMEND = "108";//推荐
    public static final String ID_HEAD = "109";//头条
    public static final String ID_MUSIC = "110";//音乐
    public static final String ID_ZONGYI = "111";//综艺
    public static final String ID_ZUANLAN = "112";//专栏
    public static final int ID_BROADCAST = 113;//广播
    public long id;
    public String title;
    public String icon;
    public long redirectId;

    public int targetPosition;

    @Override
    public String toString() {
        return "[" + id + ":" + title + ":" + redirectId + "]";
    }

    /**
     * 对应Gallery里的一个大区块
     */
    public static class Group {
        public long id;
        public String title = "";
        public String subtitle = "";
        public List<Item> items;
        /**
         * <p>栏目扩展属性中，增加了 key 为 appControlType 的属性。
         * <p>若key值为appControlType的属性存在且值为FunctionModule，这个栏目下你可以添加功能入口；
         * <p>若key值为appControlType的属性存在且值为MyMusic，这个栏目下你可以添加音乐下面的4个模块；
         * <p>若key值为appControlType的属性存在且值为LatestBroadcast，这个栏目下你可以添加广播的最近收听历史。
         */
        public @AppControlType
        int appControlType;

        /**
         * view的显示类型，默认0，ID_BROADCAST表示广播的正方形卡片，这个主要是为了竖屏广播换行。
         */
        public int viewType = 0;

        public long parentCode;

        @Override
        public String toString() {
            return "[" + title + ", " + parentCode + "]";
        }
    }


    /**
     * 对应Gallery里大区块中的大小卡片:可能为功能入口,可能为专辑,电台,歌单 etc.
     */
    public static class Item {

        public long id;
        public String title;
        public String freq;
        /**
         * 资源类型
         */
        public int resType;
        public String coverImgUrl;
        public long cFirstId;
        public long cSecondId;

        public int displayForm;
        public int subrtype;
        public int qqCategoryType;
        /*------------------------*/
        public @CardType
        int cardType = Category.CARD_TYPE_BIG;//卡片类型
        public @OperateType
        int operateType = Category.OPERATE_TYPE_PLAY;
        /**
         * 推荐类型
         */
        public String outputMode;
        /**
         * 推荐 callback
         */
        public String callBack;

        public boolean isSelected;

        public Group parent;
        public Item secondItem;
        //额外信息
        public Map<String, String> extInfo;

        /**
         * 组的第一个
         */
        public boolean firstInGroup = false;

        @Override
        public String toString() {
            return "[" + title + ":freq" + freq + ":id=" + id + ":resType=" + resType + ":" + cFirstId + "_" + cSecondId + ":img=" + coverImgUrl + "]";
        }


        public static class Builder {

            private long id;
            private String title;
            private String freq;
            private int resType;
            private String coverImgUrl;
            private int cardType = CARD_TYPE_BIG;
            private int operateType = OPERATE_TYPE_PLAY;

            public Builder id(long id) {
                this.id = id;
                return this;
            }

            public Builder title(String title) {
                this.title = title;
                return this;
            }

            public Builder freq(String freq) {
                this.freq = freq;
                return this;
            }

            public Builder resType(int resType) {
                this.resType = resType;
                return this;
            }

            public Builder coverImgUrl(String coverImgUrl) {
                this.coverImgUrl = coverImgUrl;
                return this;
            }

            public Builder cardType(@CardType int cardType) {
                this.cardType = cardType;
                return this;
            }

            public Builder operateType(@OperateType int operateType) {
                this.operateType = operateType;
                return this;
            }

            public Item build() {
                Category.Item item = new Category.Item();
                item.id = this.id;
                item.title = this.title;
                item.resType = this.resType;
                item.coverImgUrl = this.coverImgUrl;
                item.cardType = this.cardType;
                item.operateType = this.operateType;
                item.freq = this.freq;
                return item;
            }


        }
    }

}