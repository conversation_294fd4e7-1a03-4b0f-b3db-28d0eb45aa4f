package com.kaolafm.kradio.live.mvp;

import android.content.Context;
import android.util.Log;

import com.alibaba.sdk.android.oss.ClientException;
import com.alibaba.sdk.android.oss.ServiceException;
import com.alibaba.sdk.android.oss.callback.OSSCompletedCallback;
import com.alibaba.sdk.android.oss.callback.OSSProgressCallback;
import com.alibaba.sdk.android.oss.model.PutObjectRequest;
import com.alibaba.sdk.android.oss.model.PutObjectResult;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.NetworkManager;
import com.kaolafm.kradio.live.player.HomeLiveManager;
import com.kaolafm.kradio.live.player.LiveManager;
import com.kaolafm.kradio.live.player.NimManager;
import com.kaolafm.kradio.live.player.RecordUploadHelper;
import com.kaolafm.kradio.live.player.RecorderStatus;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.live.LiveApiConstant;
import com.kaolafm.opensdk.api.live.LiveRequest;
import com.kaolafm.opensdk.api.live.model.ChatUserInfo;
import com.kaolafm.opensdk.api.live.model.LiveChatRoomMemberInfoResult;
import com.kaolafm.opensdk.api.live.model.MessageBean;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.trello.rxlifecycle3.android.FragmentEvent;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;

import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.functions.Action;
import io.reactivex.functions.Consumer;
import io.reactivex.schedulers.Schedulers;

/**
 * 处理Live相关的数据获取，缓存，与聊天室的交互，通知UI进行更新
 *
 * <AUTHOR> Huangui
 */
public class LivePresenter extends BasePresenter<LiveModel, LiveView> {


    public static final String TAG = "LivePresenter";

    public static final boolean DEBUG_LIVE = true;

    private NimManager.RoomMemberChangedObserver mMemberChangedObserver;

    private NimManager.OnChatMessageReceivedListener mMessageReceivedListener;

    private NimManager.EnterChatRoomListener mEnterChatRoomListener;

    public LivePresenter(LiveView view) {
        super(view);
    }

    @Override
    public void start() {
        super.start();
    }

    public void initNim() {
        NimManager.getInstance().initNim();
        LiveManager.getInstance().registerResponseRecord();
    }

    @Override
    protected LiveModel createModel() {
        return new LiveModel(((BaseFragment) mView).bindUntilEvent(FragmentEvent.DESTROY_VIEW));
    }

    @Override
    public void destroy() {
        super.destroy();
        LiveManager.getInstance().unRegisterResponseRecord();
    }

    public String getNickName() {
        return NimManager.getInstance().getNickName();
    }

    /**
     * 获取收听人数
     */
    public void getListenerNumber() {
        String roomId = NimManager.getInstance().getRoomId();
        if (DEBUG_LIVE) {
            Log.i(TAG, "getListenerNumber roomId: " + roomId);
        }
        // do live 直播新接口逻辑
        long roomIdLong;
        try {
            roomIdLong = Long.parseLong(roomId);
        } catch (NumberFormatException e) {
            roomIdLong = 0L;
        }
        fetchRoomOnLineCount(roomIdLong);
    }

    private class RecordUploadProgressCallback implements OSSProgressCallback<PutObjectRequest> {

        private ObservableEmitter<Integer> mEmitter;

        private RecordUploadProgressCallback(ObservableEmitter<Integer> mEmitter) {
            this.mEmitter = mEmitter;
        }

        @Override
        public void onProgress(PutObjectRequest request, long currentSize, long totalSize) {
            float progress = (totalSize > 0) ? ((float) currentSize / totalSize) * 100 : -1;
            if (DEBUG_LIVE) {
                Log.i(TAG, String.format("onProgress Progress %d from %d (%f)",
                        currentSize, totalSize, progress));
            }
            mEmitter.onNext((int) progress);
        }
    }

    private class RecordUploadCompletedCallback implements OSSCompletedCallback<PutObjectRequest, PutObjectResult> {

        private ObservableEmitter<Integer> mEmitter;

        public RecordUploadCompletedCallback(ObservableEmitter<Integer> mEmitter) {
            this.mEmitter = mEmitter;
        }

        @Override
        public void onSuccess(PutObjectRequest request, PutObjectResult result) {
            if (DEBUG_LIVE) {
                Log.i(TAG, "onSuccess responseJson : " + result.toString());
            }
            mEmitter.onComplete();

        }

        @Override
        public void onFailure(PutObjectRequest request, ClientException clientException, ServiceException serviceException) {
            if (DEBUG_LIVE) {
                Log.i(TAG, "onFailure client errorMessage : " + clientException.getMessage() + " ; onFailure server errorMessage :" + serviceException.getMessage());
            }
            mEmitter.tryOnError(new RuntimeException(clientException.getMessage()));
            mEmitter.tryOnError(new RuntimeException(serviceException.getMessage()));
        }
    }

    /**
     * 进入聊天室
     *
     * @param roomId 聊天室id
     */
    public void enterChatRoom(Context context, String roomId) {
        boolean entered = NimManager.getInstance().isChatRoomEntered();
        if (DEBUG_LIVE) {
            Log.i(TAG, "enterChatRoom roomId: " + roomId + ", entered: " + entered);
        }
        NimManager.getInstance().addRoomMemberChangedObserver(createRoomMemberChangedObserver());
        NimManager.getInstance().addChatMessageReceivedListener(createChatMessageReceivedListener());
        NimManager.getInstance().enterChatRoom(context, roomId, createEnterChatRoomListener());
    }

    /**
     * 退出聊天室
     */
    public void exitChatRoom() {
        if (DEBUG_LIVE) {
            Log.i(TAG, "exitChatRoom");
        }
        NimManager.getInstance().exitChatRoom();
    }

    private NimManager.RoomMemberChangedObserver createRoomMemberChangedObserver() {
        if (mMemberChangedObserver != null) {
            return mMemberChangedObserver;
        }
        mMemberChangedObserver = new NimManager.RoomMemberChangedObserver() {
            @Override
            public void onRoomMemberIn(ChatUserInfo chatUserInfo) {
                if (mView != null) {
                    mView.showRoomMemberEnter(chatUserInfo);
                }
            }

            @Override
            public void onRoomMemberExit(ChatUserInfo chatUserInfo) {

            }
        };
        return mMemberChangedObserver;
    }

    private NimManager.OnChatMessageReceivedListener createChatMessageReceivedListener() {
        if (mMessageReceivedListener != null) {
            return mMessageReceivedListener;
        }
        mMessageReceivedListener = new NimManager.OnChatMessageReceivedListener() {
            @Override
            public void onChatMessageReceived(ArrayList<MessageBean> messageData) {
                if (mView != null) {
                    mView.showChatMessageReceived(messageData);
                }
            }
        };
        return mMessageReceivedListener;
    }

    private NimManager.EnterChatRoomListener createEnterChatRoomListener() {
        if (mEnterChatRoomListener == null) {
            mEnterChatRoomListener = new NimManager.EnterChatRoomListener() {
                @Override
                public void onException(Throwable throwable) {
                    if (LivePresenter.DEBUG_LIVE) {
                        Log.i(TAG, "enterChatRoom onException: ", throwable);
                    }
                    NimManager.getInstance().setChatRoomEntered(false);
                }

                @Override
                public void loginFailed(int code) {
                    if (LivePresenter.DEBUG_LIVE) {
                        Log.i(TAG, "enterChatRoom loginFailed code: " + code);
                    }
                    NimManager.getInstance().setChatRoomEntered(false);
                }

                @Override
                public void loginSuccess(String account) {
                    if (LivePresenter.DEBUG_LIVE) {
                        Log.i(TAG, "enterChatRoom loginSuccess account: " + account);
                    }
                    getListenerNumber();
                    //此处登录成功，进入聊天室是否成功还需要进一步确认
                }

                @Override
                public void enterChatRoomSuccess() {
                    if (LivePresenter.DEBUG_LIVE) {
                        Log.i(TAG, "enterChatRoom enterChatRoomSuccess account");
                    }
                    NimManager.getInstance().setChatRoomEntered(true);
                }

                @Override
                public void enterChatRoomFailed(int code) {
                    if (LivePresenter.DEBUG_LIVE) {
                        Log.i(TAG, "enterChatRoom enterChatRoomFailed code: " + code);
                    }
                }
            };
        }
        return mEnterChatRoomListener;
    }

    /**
     * 通过接口获取聊天室在线人数
     */
    public void fetchRoomOnLineCount(Long liveId){
        Log.d(TAG, "live--- fetchRoomOnLineCount() liveId=" + liveId);
        if (mView == null){
            return;
        }
        mModel.fetchRoomMembers(liveId, new HttpCallback<LiveChatRoomMemberInfoResult>() {
            @Override
            public void onSuccess(LiveChatRoomMemberInfoResult memberInfoResult) {
                Log.d(TAG, "live--- fetchRoomOnLineCount() onSuccess memberInfoResult=" + memberInfoResult);
                if (mView == null || memberInfoResult == null){
                    return;
                }
                try {
                    int count = Integer.parseInt(memberInfoResult.getOnlineUserCount());
                    mView.showListenerNumber(count);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onError(ApiException e) {
                Log.d(TAG, "live--- fetchRoomOnLineCount() onError ApiException=" + e);
            }
        });
    }

    /**
     * 发送语音消息，同时向网宿和云信发送，以网宿的上传进度作为进度更新回调，但成功与否取决于两者的共同
     * 结果。
     *
     * @param context  上下文，网宿的SDK使用
     * @param program  上传到网宿时的附加信息
     * @param fileName 文件地址
     */
    public void sendAudioMessageToServer(Context context, RecordUploadHelper.UploadParam program,
                                         String fileName) {
        // 调用的此方法发送语音消息
        if(NetworkManager.getInstance().isNotHasNetwork()){
            ToastUtil.showInfo(context, R.string.no_net_work_str);
            return;
        }
        String path = HomeLiveManager.getInstance().getFilePath();
        if (path == null) {
            if (mView != null) {
                mView.showFileNotExist();
            }
            return;
        }
        File file = new File(path);
        if (!file.exists()) {
            if (mView != null) {
                mView.showFileNotExist();
            }
            return;
        }
        HomeLiveManager.getInstance().setRecorderStatus(RecorderStatus.UPLOADING);

        final String room = NimManager.getInstance().getRoomId();

        Observable wangsu = Observable.create(new ObservableOnSubscribe<Boolean>() {
            @Override
            public void subscribe(ObservableEmitter emitter) throws Exception {
                // path -> /storage/emulated/0/Android/data/com.edog.car/files/Music/K-Radio-Record/Live-Leave-message.aac
                RecordUploadHelper.uploadToAliyunOSS(context, path, program, fileName
                        , new RecordUploadCompletedCallback2(emitter)
                        , new RecordUploadProgressCallback2(emitter));
            }
        });

        Observable yunxin = Observable.create(new ObservableOnSubscribe<Boolean>() {
            @Override
            public void subscribe(ObservableEmitter<Boolean> emitter) {
                String path = LiveApiConstant.SEND_MESSAGE_BASE_URL + generateFileUploadedPath(fileName);
                // path -> https://iovimage.radio.cn/kradio_live_radio/rhTjbBXufN4-1696820752594.aac
                sendAudioMessageToServer(program, path, emitter);
            }
        });

        wangsu
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer() {
                    @Override
                    public void accept(Object o) throws Exception {
                        if (mView != null) {
                            if (o instanceof Integer) {
                                Log.i(TAG, "sendAudioMessage showRecordUploadProgress: " + o);
                                mView.showRecordUploadProgress((int) o);
                            }
                        }
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        if (mView != null) {
                            mView.showRecordUploadFailure();
                        }
                    }
                }, new Action() {
                    @Override
                    public void run() throws Exception {
                        yunxin.subscribeOn(Schedulers.io())
                                .observeOn(AndroidSchedulers.mainThread())
                                .subscribe(new Consumer() {
                                    @Override
                                    public void accept(Object o) throws Exception {
                                        if (o instanceof Boolean && ((Boolean) o)) {

                                        }
                                    }
                                }, new Consumer<Throwable>() {
                                    @Override
                                    public void accept(Throwable throwable) throws Exception {
                                        if (mView != null) {
                                            mView.showRecordUploadFailure();
                                        }
                                    }
                                }, new Action() {
                                    @Override
                                    public void run() throws Exception {
                                        if (mView != null) {
                                            mView.showRecordUploadSuccess();
                                        }
                                    }
                                });
                    }
                });
    }

    private void sendAudioMessageToServer(RecordUploadHelper.UploadParam program, String path, ObservableEmitter<Boolean> emitter) {
        String encode = null;
        try {
            encode = URLEncoder.encode(path, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        new LiveRequest().sendMessageToServer(program.getUid(), program.getAppid(), encode, program.getLiveProgramId(), new HttpCallback<BaseResult<String>>() {
            @Override
            public void onSuccess(BaseResult<String> result) {
                if (result.getCode() == 10000) {
                    emitter.onNext(true);
                    emitter.onComplete();
                    return;
                }
                onError(new ApiException("发送失败"));
            }

            @Override
            public void onError(ApiException e) {
                emitter.tryOnError(new RuntimeException(e.getMessage()));
            }
        });
    }

    private class RecordUploadCompletedCallback2 implements OSSCompletedCallback<PutObjectRequest, PutObjectResult> {

        private ObservableEmitter<Integer> mEmitter;

        public RecordUploadCompletedCallback2(ObservableEmitter<Integer> mEmitter) {
            this.mEmitter = mEmitter;
        }

        @Override
        public void onSuccess(PutObjectRequest request, PutObjectResult result) {
            if (DEBUG_LIVE) {
                Log.i(TAG, "onSuccess responseJson : " + result.toString());
            }
            mEmitter.onComplete();

        }

        @Override
        public void onFailure(PutObjectRequest request, ClientException clientException, ServiceException serviceException) {
            String clientMessage = "", serviceMessage = "";
            if (clientException != null) {
                clientMessage = clientException.toString();
            }
            if (serviceException != null) {
                serviceMessage = serviceException.toString();
            }
            if (DEBUG_LIVE) {
                Log.i(TAG, "onFailure client errorMessage : " + clientMessage + " ; onFailure server errorMessage :" + serviceMessage);
            }
            mEmitter.tryOnError(new RuntimeException(clientMessage));
            mEmitter.tryOnError(new RuntimeException(serviceMessage));
        }
    }

    private class RecordUploadProgressCallback2 implements OSSProgressCallback<PutObjectRequest> {

        private ObservableEmitter<Integer> mEmitter;

        private RecordUploadProgressCallback2(ObservableEmitter<Integer> mEmitter) {
            this.mEmitter = mEmitter;
        }

        @Override
        public void onProgress(PutObjectRequest request, long currentSize, long totalSize) {
            float progress = (totalSize > 0) ? ((float) currentSize / totalSize) * 100 : -1;
            if (DEBUG_LIVE) {
                Log.i(TAG, String.format("onProgress Progress %d from %d (%f)",
                        currentSize, totalSize, progress));
            }
            mEmitter.onNext((int) progress);
        }
    }

    private static String generateFileUploadedPath(String fileName) {
        return "kradio_live_radio/" + fileName;
    }
}
