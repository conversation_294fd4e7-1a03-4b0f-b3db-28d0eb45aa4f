package com.kaolafm.kradio.home.comprehensive.data;

import android.util.Log;
import android.util.LongSparseArray;

import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.home.HomeDataManager;
import com.kaolafm.kradio.home.data.Category;
import com.kaolafm.kradio.home.comprehensive.mvp.ICategoryModel;
import com.kaolafm.kradio.home.comprehensive.mvp.IGalleryModel;
import com.kaolafm.kradio.lib.base.mvp.IModel;
import com.kaolafm.kradio.lib.utils.YTDataCache;
import com.kaolafm.opensdk.api.broadcast.ProgramDetails;
import com.kaolafm.opensdk.api.operation.model.column.ColumnGrp;
import com.kaolafm.opensdk.api.search.SearchRequest;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 **/
public class HomePlayerModel implements IGalleryModel, ICategoryModel, IModel {
    private static final String TAG = "home.model";
    private List<Category> mCategories = new ArrayList<>();
    private LongSparseArray<List<Category.Group>> mGroups = new LongSparseArray();

    private volatile static HomePlayerModel mInstance;
    private SearchRequest mSearchRequest;

    private HomePlayerModel() {

    }

    public static HomePlayerModel getInstance() {
        if (mInstance == null) {
            synchronized (HomePlayerModel.class) {
                if (mInstance == null) {
                    mInstance = new HomePlayerModel();
                }
            }
        }
        return mInstance;
    }

    @Override
    @Deprecated
    public void getDatas(HttpCallback<List<ColumnGrp>> callback) {
        Log.i(TAG, "刷新首页数据....");
        HomeDataManager.getInstance().request(callback);
    }

    @Override
    public void getDatas(String zone, HttpCallback<List<ColumnGrp>> callback) {
        if (callback == null) {
            return;
        }
        // 优化：先尝试从缓存获取数据，失败时降级到直接网络请求
        YTDataCache.fetchHomePageZone(zone).thenAccept(result -> {
            if (result != null && !result.isEmpty()) {
                HomeDataManager.getInstance().updateFirstHomeCell(result);
                callback.onSuccess(result);
            } else {
                // 缓存获取失败或数据为空，降级到直接网络请求
                HomeDataManager.getInstance().request(zone, callback);
            }
        }).exceptionally(throwable -> {
            // 缓存获取异常，降级到直接网络请求
            HomeDataManager.getInstance().request(zone, callback);
            return null;
        });
    }

    public void schedulProgramListRequest(int delay,HttpCallback<List<ProgramDetails>> callback){
        HomeDataManager.getInstance().schedulProgramListRequest(delay,callback);
    }
    public void getHotSearchWords(HttpCallback<List<String>> callback) {
        if(mSearchRequest == null) mSearchRequest = new SearchRequest().setTag(this.toString());
        mSearchRequest.getHotWords(callback);
    }

    @Override
    public void refresh() {
        mCategories.clear();
        mGroups.clear();
    }

    @Override
    public void destroy() {
        cancelSchedule();
    }
    public void cancelSchedule(){
        HomeDataManager.getInstance().cancelSchedule();
    }
    public void addBroadcastList(List<HomeCell> cells){
        HomeDataManager.getInstance().addBroadcastList(cells);
    }
    public void cancelRequest() {
        mSearchRequest.cancel(this.toString());
    }
}
