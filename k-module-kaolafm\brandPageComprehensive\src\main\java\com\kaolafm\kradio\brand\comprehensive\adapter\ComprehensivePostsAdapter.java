package com.kaolafm.kradio.brand.comprehensive.adapter;

import android.view.View;
import android.view.ViewGroup;
import android.view.View.OnClickListener;
import android.widget.ImageView;
import android.widget.TextView;
import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.kaolafm.kradio.k_kaolafm.R.drawable;
import com.kaolafm.kradio.k_kaolafm.R.id;
import com.kaolafm.kradio.k_kaolafm.R.layout;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.date.TimeUtil;
import com.kaolafm.opensdk.api.topic.model.TopicPosts;
import java.util.Arrays;
import java.util.List;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public final class ComprehensivePostsAdapter extends BaseAdapter {
    @Nullable
    private ComprehensivePostsAdapter.OnItemLikeIconClickedListener listener;

    private final String formatNumber(long count, int decimalPlaces) {
        if (count < (long)10000) {
            return String.valueOf(count);
        } else {
            String var5 = "%." + decimalPlaces + "fw";
            Object[] var6 = new Object[]{(float)count / 10000.0F};
            String var10000 = String.format(var5, Arrays.copyOf(var6, var6.length));
            Intrinsics.checkExpressionValueIsNotNull(var10000, "java.lang.String.format(format, *args)");
            return var10000;
        }
    }

    @NotNull
    protected BaseHolder getViewHolder(@Nullable ViewGroup parent, int viewType) {
        return (BaseHolder)(new ComprehensivePostsAdapter.RelatedContentViewHolder(this.inflate(parent, layout.comprehensive_rv_item_topic_posts, 0)));
    }

    @Nullable
    public final ComprehensivePostsAdapter.OnItemLikeIconClickedListener getListener() {
        return this.listener;
    }

    public final void setListener(@Nullable ComprehensivePostsAdapter.OnItemLikeIconClickedListener var1) {
        this.listener = var1;
    }

    public ComprehensivePostsAdapter(@Nullable List dataList, @Nullable ComprehensivePostsAdapter.OnItemLikeIconClickedListener listener) {
        super(dataList);
        this.listener = listener;
    }

    public final class RelatedContentViewHolder extends BaseHolder {
        private final ImageView userImage;
        private final TextView userName;
        private final TextView updateTime;
        private final TextView likeCount;
        private final ImageView likeIcon;
        private final TextView postsContent;
        private final View likeParentView;
        private final RequestOptions option;

        public void setupData(@Nullable TopicPosts t, int position) {
            if (t != null) {
                this.userImage.setImageResource(drawable.user_no_login_icon);
                TextView var10000 = this.userName;
                Intrinsics.checkExpressionValueIsNotNull(var10000, "userName");
                var10000.setText((CharSequence)t.getNickname());
                var10000 = this.likeCount;
                Intrinsics.checkExpressionValueIsNotNull(var10000, "likeCount");
                var10000.setText((CharSequence)ComprehensivePostsAdapter.this.formatNumber(t.getLikeCount(), 1));
                this.likeIcon.setImageResource(t.getLikeStatus() == 1 ? drawable.comprehensive_topic_posts_liked : drawable.comprehensive_topic_posts_unlike);
                var10000 = this.postsContent;
                Intrinsics.checkExpressionValueIsNotNull(var10000, "postsContent");
                var10000.setText((CharSequence)t.getContent());
                var10000 = this.updateTime;
                Intrinsics.checkExpressionValueIsNotNull(var10000, "updateTime");
                TextView var10001 = this.updateTime;
                Intrinsics.checkExpressionValueIsNotNull(var10001, "updateTime");
                var10000.setText((CharSequence)TimeUtil.getTopicPostsTime(var10001.getContext(), t.getPublishTime()));
                ImageView var3 = this.userImage;
                Intrinsics.checkExpressionValueIsNotNull(var3, "userImage");
                Glide.with(var3.getContext()).load(t.getAvatar()).apply(this.option).into(this.userImage);
            }
        }

        // $FF: synthetic method
        // $FF: bridge method
        public void setupData(Object var1, int var2) {
            this.setupData((TopicPosts)var1, var2);
        }

        public RelatedContentViewHolder(@Nullable View itemView) {
            super(itemView);
            if (itemView == null) {
                Intrinsics.throwNpe();
            }

            this.userImage = (ImageView)itemView.findViewById(id.userIcon);
            this.userName = (TextView)itemView.findViewById(id.userName);
            this.updateTime = (TextView)itemView.findViewById(id.updateTime);
            this.likeCount = (TextView)itemView.findViewById(id.likeCount);
            this.likeIcon = (ImageView)itemView.findViewById(id.likeIcon);
            this.postsContent = (TextView)itemView.findViewById(id.postsContent);
            this.likeParentView = itemView.findViewById(id.likeParentView);
            RequestOptions var3 = new RequestOptions();
            var3.error(drawable.user_no_login_icon);
            var3.placeholder(drawable.user_no_login_icon);
            this.option = var3;
            this.likeParentView.setOnClickListener((OnClickListener)(new OnClickListener() {
                public final void onClick(View it) {
                    if (!AntiShake.check(it)) {
                        ComprehensivePostsAdapter.OnItemLikeIconClickedListener var10000 = ComprehensivePostsAdapter.this.getListener();
                        if (var10000 != null) {
                            Object var10001 = ComprehensivePostsAdapter.this.getItemData(RelatedContentViewHolder.this.getAdapterPosition());
                            Intrinsics.checkExpressionValueIsNotNull(var10001, "getItemData(adapterPosition)");
                            var10000.onItemLikeClicked((TopicPosts)var10001, RelatedContentViewHolder.this.getAdapterPosition());
                        }

                    }
                }
            }));
        }
    }

    public interface OnItemLikeIconClickedListener {
        void onItemLikeClicked(@NotNull TopicPosts var1, int var2);
    }
}
