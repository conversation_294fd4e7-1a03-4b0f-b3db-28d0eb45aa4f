<?xml version="1.0" encoding="utf-8"?>
<com.kaolafm.kradio.component.ui.base.view.ScaleConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:sfl="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <View

        android:layout_width="@dimen/m206"
        android:layout_height="@dimen/m206"
        android:background="@drawable/online_class_item_unselect_bg"
        app:layout_constraintDimensionRatio="1:1" />

    <com.kaolafm.kradio.component.ui.base.view.OvalImageView
        android:id="@+id/iv_purchase_type"
        android:layout_width="@dimen/m80"
        android:layout_height="@dimen/m36"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <View
        android:id="@+id/backgroundIv"
        android:layout_width="@dimen/m206"
        android:layout_height="@dimen/m206"
        android:background="@drawable/online_class_item_select_bg"
        android:visibility="gone" />

    <com.kaolafm.kradio.component.ui.base.view.OvalImageView
        android:id="@+id/iv_play_cover"
        android:layout_width="@dimen/m64"
        android:layout_height="@dimen/m64"
        android:layout_marginTop="@dimen/y29"
        android:scaleType="centerCrop"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:oval_radius="@dimen/m32"
        app:rid_type="0"
        tools:src="@drawable/media_default_pic" />

    <TextView
        android:id="@+id/user_tab_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/y12"
        android:ellipsize="end"
        android:gravity="center"
        android:singleLine="true"
        android:textColor="@color/online_user_item_title_text_color"
        android:textSize="@dimen/text_size_title7"
        app:layout_constraintBottom_toTopOf="@id/iv_purchase_type"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

</com.kaolafm.kradio.component.ui.base.view.ScaleConstraintLayout>
