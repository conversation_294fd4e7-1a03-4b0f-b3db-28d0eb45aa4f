<?xml version="1.0" encoding="utf-8"?>
<com.kaolafm.kradio.lib.widget.ratio.RatioConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:canScale="false"
    app:wh_ratio="1:1"
    tools:ignore="MissingDefaultResource">
<LinearLayout
    android:orientation="horizontal"
    android:layout_width="wrap_content"
    android:layout_height="match_parent">
    <include
        android:id="@+id/acticity_item"
        layout="@layout/component_item_activity"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <include
        android:id="@+id/acticity_btn_item"
        layout="@layout/component_item_activity_new"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
</LinearLayout>
</com.kaolafm.kradio.lib.widget.ratio.RatioConstraintLayout>