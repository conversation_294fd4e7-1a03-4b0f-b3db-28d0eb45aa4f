package com.kaolafm.kradio.live.comprehensive.gift;

import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Color;
import android.graphics.Point;
import android.graphics.drawable.ColorDrawable;
import android.os.Build;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.viewpager.widget.ViewPager;
import androidx.recyclerview.widget.RecyclerView;

import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Display;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.kaolafm.kradio.common.utils.CommonUtils;
import com.kaolafm.kradio.k_kaolafm.BuildConfig;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.purchase.util.MoneyUtils;
import com.kaolafm.opensdk.api.live.model.Gift;
import com.kaolafm.opensdk.api.live.model.GiftsResult;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.DialogExposureEvent;
import com.kaolafm.report.util.ReportConstants;

/**
 * <AUTHOR> shiqian
 * @date 2023-02-21
 */
public class GiftPannelDialog extends DialogFragment {

    private static final String TAG = "GiftPannelDialog";

    private TextView giftHeaderBalance;
    private GiftPanelControl giftPanelControl;
    private LinearLayout gift_viewpager, gift_recyclerview;
    private RecyclerView giftRecyclerView;
    private ViewPager giftViewpager;
    private LinearLayout giftDotsLayout;

    private GiftsResult mDatas;

    private static final String PAGE_ID = "PAGE_ID";
    private static final String GIF_RESULT = "GIF_RESULT";
    private String pageId;

    public static GiftPannelDialog getInstance(GiftsResult giftsResult, String pageId) {
        GiftPannelDialog giftPannelDialog = new GiftPannelDialog();
        Bundle bundle = new Bundle();
        bundle.putParcelable(GIF_RESULT, giftsResult);
        bundle.putString(PAGE_ID, pageId);
        giftPannelDialog.setArguments(bundle);
        return giftPannelDialog;
    }

    @Override
    public void onStart() {
        super.onStart();
        CommonUtils.getInstance().initGreyStyle(getDialog().getWindow());
        getDialog().getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

//        getDialog().getWindow().setDimAmount(0f);
        WindowManager.LayoutParams layoutParams = getDialog().getWindow().getAttributes();
        layoutParams.gravity = Gravity.BOTTOM; // 位置
        layoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT;//宽度满屏
//        layoutParams.horizontalMargin = ResUtil.getDimen(R.dimen.x48);
        layoutParams.dimAmount = 0f;
        getDialog().getWindow().setAttributes(layoutParams);
        int padding = BuildConfig.LAYOUT_TYPE == 0 ? ResUtil.getDimen(R.dimen.m48) : ResUtil.getDimen(R.dimen.m40);
        getDialog().getWindow().getDecorView().setPadding(padding,0, padding,0);
//        getDialog().getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
    }

    /**
     * 设置页面为透明
     */
    private void initWindow(Window window) {
        if (window == null) {
            return;
        }
        window.requestFeature(Window.FEATURE_NO_TITLE);
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
//        window.setWindowAnimations(android.R.style.Animation_Translucent);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION);
            window.setStatusBarColor(Color.TRANSPARENT);
        } else {
            window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        }
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        if (false && getDialog() != null && need_DRAWS_SYSTEM_BAR_BACKGROUNDS(getContext())) {
            initWindow(getDialog().getWindow());
        }
        Bundle arguments = getArguments();
        if (arguments != null) {
            this.mDatas = arguments.getParcelable(GIF_RESULT);
            this.pageId = arguments.getString(PAGE_ID, "");
        }
        View view = inflater.inflate(R.layout.comprehensive_gift_box, container, false);
        giftHeaderBalance = (TextView) view.findViewById(R.id.gift_header_balance);
        gift_viewpager = (LinearLayout) view.findViewById(R.id.gift_viewpager);
        gift_recyclerview = (LinearLayout) view.findViewById(R.id.gift_recyclerview);
        giftRecyclerView = (RecyclerView) view.findViewById(R.id.rv_gift);
        giftViewpager = (ViewPager) view.findViewById(R.id.gift_pagers);
        giftDotsLayout = (LinearLayout) view.findViewById(R.id.dots_container);
        giftPanelControl = new GiftPanelControl(getContext(), giftViewpager, giftRecyclerView, giftDotsLayout);
        view.findViewById(R.id.cd_close).setOnClickListener((v)->dismiss());
//        giftPanelControl.setData(mDatas);
        giftPanelControl.setOnGiftSelectedListener(new GiftPanelControl.OnGiftSelectedListener() {
            @Override
            public void onSelected(Gift gift) {

            }
        });
        giftPanelControl.setOnGiftGivingListener(new GiftPanelControl.OnGiftGivingListener() {
            @Override
            public void onGiving(Gift gift) {
                if (mOnGivingGiftListener != null) {
                    mOnGivingGiftListener.onGiving(gift);
                }
            }
        });
        gift_viewpager.setVisibility(View.GONE);
        gift_recyclerview.setVisibility(View.VISIBLE);

        return view;
    }

    @Override
    public void onDismiss(DialogInterface dialog) {
        super.onDismiss(dialog);
        reportPageShowEvent();
        if (mOnDismissListener != null) {
            mOnDismissListener.onDismiss();
        }
    }

    public GiftPannelDialog setData(GiftsResult giftsResult) {
        if (giftsResult != null) {
            mDatas = giftsResult;
            if (giftHeaderBalance != null)
                giftHeaderBalance.setText(MoneyUtils.changeF2Y(mDatas.getBalance()) + "");
            if (giftPanelControl != null)
                giftPanelControl.setData(mDatas);
        }
        return this;
    }

    public interface OnGivingGiftListener {
        void onGiving(Gift gift);
    }

    OnGivingGiftListener mOnGivingGiftListener;

    public GiftPannelDialog setOnGivingGiftListener(OnGivingGiftListener onGivingGiftListener) {
        mOnGivingGiftListener = onGivingGiftListener;
        return this;
    }

    public interface OnDismissListener {
        void onDismiss();
    }

    OnDismissListener mOnDismissListener;

    public GiftPannelDialog setOnDismissListener(OnDismissListener onDismissListener) {
        mOnDismissListener = onDismissListener;
        return this;
    }

    protected long startTime = -1;

    @Override
    public void onResume() {
        super.onResume();
        startTime = System.currentTimeMillis();
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
    }

    /**
     * 页面曝光事件上报
     */
    private void reportPageShowEvent() {
        long duration = System.currentTimeMillis() - startTime;
        if (startTime < 0 || duration < 300) {
            return;
        }
        ReportHelper.getInstance().addEvent(new DialogExposureEvent(ReportConstants.DIALOG_ID_LIVE_ROOM_GIFT_LIST, pageId, duration, null));
        Log.i("BaseDialogFragment", "report=" + duration);
        startTime = -1;
    }

    private boolean need_DRAWS_SYSTEM_BAR_BACKGROUNDS(Context context){
        if (context == null){
            Log.i(TAG, "need_DRAWS_SYSTEM_BAR_BACKGROUNDS --- context is null -> result = false");
            return false;
        }
        Point screenSizePoint = getScreenSize(context);
        Point realScreenSizePoint = getRealScreenSize(context);
        int navigationBarHeight = getNavigationBarHeight(context);
        Log.i(TAG, "need_DRAWS_SYSTEM_BAR_BACKGROUNDS --- getScreenSize          = " + screenSizePoint);
        Log.i(TAG, "need_DRAWS_SYSTEM_BAR_BACKGROUNDS --- getRealScreenSize      = " + realScreenSizePoint);
        Log.i(TAG, "need_DRAWS_SYSTEM_BAR_BACKGROUNDS --- getNavigationBarHeight = " + navigationBarHeight);
        boolean result = screenSizePoint.y + navigationBarHeight == realScreenSizePoint.y;
        Log.i(TAG, "need_DRAWS_SYSTEM_BAR_BACKGROUNDS --- result = " + result);
        return result;
    }

    /**
     * 获取屏幕宽高等参数，此宽高是去掉状态栏或导航栏的，并非屏幕的实际宽高
     */
    private Point getScreenSize(Context context){
        Point point = new Point();
        if (context == null){
            return point;
        }
        DisplayMetrics displayMetrics = new DisplayMetrics();
        ((WindowManager) context.getSystemService(Context.WINDOW_SERVICE)).getDefaultDisplay().getMetrics(displayMetrics);
        point.x = displayMetrics.widthPixels;
        point.y = displayMetrics.heightPixels;
        return point;
    }

    /**
     * 获取屏幕的实际宽高
     */
    private Point getRealScreenSize(Context context){
        Point point = new Point();
        if (context == null){
            return point;
        }
        WindowManager wm = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        Display display = wm.getDefaultDisplay();
        display.getRealSize(point);
        return point;
    }

    /**
     * 获取导航栏高度
     */
    private int getNavigationBarHeight(Context context) {
        int result = 0;
        if (context == null){
            return result;
        }
        int resourceId = context.getResources().getIdentifier("navigation_bar_height", "dimen", "android");
        if (resourceId > 0) {
            result = context.getResources().getDimensionPixelSize(resourceId);
        }
        return result;
    }
}
