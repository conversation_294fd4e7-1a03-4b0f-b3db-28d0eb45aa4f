<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/fuc_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintTop_toTopOf="parent">

        <!--注意:为了对齐fragment,必须满足:tl_tab_padding + layout_marginLeft == x90-->
        <com.flyco.tablayout.SlidingTabLayout
            android:id="@+id/user_tab_title"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/y70"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="@dimen/x50"
            android:layout_marginRight="@dimen/x10"
            android:layout_weight="1"
            app:tl_indicator_anim_enable="true"
            app:tl_indicator_color="@color/tab_indicator_underline_color"
            app:tl_indicator_height="@dimen/tab_indicator_height"
            app:tl_indicator_width_equal_title="true"
            app:tl_tab_padding="@dimen/x10"
            app:tl_tab_space_equal="false"
            app:tl_textBold="SELECT"
            app:tl_textSelectColor="@color/tab_indicator_select_color"
            app:tl_textSelectSize="@dimen/text_size4"
            app:tl_textUnselectColor="@color/tab_indicator_unselect_color"
            app:tl_textsize="@dimen/text_size4" />

        <!--一键删除按钮-->
        <TextView
            android:id="@+id/user_right_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="@dimen/m50"
            android:background="@drawable/globle_round_bg"
            android:drawablePadding="@dimen/x5"
            android:gravity="center"
            android:paddingLeft="@dimen/x16"
            android:paddingTop="@dimen/y10"
            android:paddingRight="@dimen/x16"
            android:paddingBottom="@dimen/y10"
            android:textColor="@color/user_right_textview_color"
            android:textSize="@dimen/text_size_title4"
            android:visibility="invisible" />

    </LinearLayout>

    <!--遮罩,否则SlidingTabLayout的第一个条目会滑动超过左基准线-->
    <View
        android:id="@+id/zhezhao"
        android:layout_width="@dimen/m50"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/fuc_layout"
        app:layout_constraintLeft_toLeftOf="@+id/fuc_layout"
        app:layout_constraintTop_toTopOf="@+id/fuc_layout" />

    <View
        android:id="@+id/view_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/y1"
        android:layout_alignParentTop="true"
        android:background="@color/user_setting_view_divider"
        app:layout_constraintTop_toBottomOf="@+id/fuc_layout" />

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/user_viewpager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:overScrollMode="never"
        android:paddingLeft="@dimen/x1"
        android:paddingRight="@dimen/x1"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/view_divider" />
</androidx.constraintlayout.widget.ConstraintLayout>