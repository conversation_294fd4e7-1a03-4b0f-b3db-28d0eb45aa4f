package com.kaolafm.kradio.flavor.view;

import android.content.Context;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;

import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.flavor.impl.RiChanHelper;

/**
 * <pre>
 *     <AUTHOR> Wenchl
 *     e-mail : <EMAIL>
 *     time   : 2019/08/07
 *     desc   :
 *     version: 1.0
 * </pre>
 */
public class AccountLoginView extends LinearLayout {

    public AccountLoginView(Context context) {
        super(context);
        init();
    }

    public AccountLoginView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public AccountLoginView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        View view = inflate(getContext(), R.layout.layout_account_login, this);
        Button button = view.findViewById(R.id.richan_account_btn);
        button.setOnClickListener(v -> RiChanHelper.getInstance(getContext()).showLoginPage());
    }

}
