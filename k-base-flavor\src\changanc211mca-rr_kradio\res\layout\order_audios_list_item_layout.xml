<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/m80"
    android:layout_gravity="center_vertical"
    android:background="@android:color/transparent"
    tools:background="@color/colorWhite">


    <TextView
        android:id="@+id/tvBuyType"
        android:layout_width="@dimen/m54"
        android:layout_height="@dimen/m24"
        android:layout_marginEnd="@dimen/x16"
        android:background="@drawable/color_medium_line_radio_sub_title_tag_round_bg"
        android:gravity="center"
        android:text="@string/audition"
        android:textColor="@color/player_radio_item_subtitle_text_color"
        android:textSize="@dimen/m18"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/item_num"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/item_num"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="021"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size4"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/item_title"
        app:layout_constraintStart_toEndOf="@id/ivBuyType"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/item_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/m10"
        android:text="高规格"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size4"
        android:maxLines="2"
        android:ellipsize="end"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/item_check"
        app:layout_constraintStart_toEndOf="@id/item_num"
        app:layout_constraintTop_toTopOf="parent" />

    <CheckBox
        android:id="@+id/item_check"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:button="@drawable/order_pay_multi_checkbox_selector"
        android:scaleX="0.5"
        android:scaleY="0.5"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/item_title"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>