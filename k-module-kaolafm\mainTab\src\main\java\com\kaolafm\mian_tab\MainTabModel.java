package com.kaolafm.mian_tab;

import com.kaolafm.kradio.lib.base.mvp.BaseModel;
import com.kaolafm.kradio.lib.utils.YTDataCache;
import com.kaolafm.opensdk.api.maintab.model.MainTabBean;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import java.util.List;

public class MainTabModel extends BaseModel {

    public MainTabModel() {
    }

    public void getTabList(HttpCallback<List<MainTabBean>> callback) {
        if (callback == null) {
            return;
        }
        YTDataCache.fetchTabList().thenAccept(result -> {
            if (result != null) {
                callback.onSuccess(result);
            } else {
                callback.onError(new ApiException("no data"));
            }
        }).exceptionally(throwable -> {
            callback.onError(new ApiException(throwable));
            return null;
        });
    }

    @Override
    public void destroy() {

    }
}
