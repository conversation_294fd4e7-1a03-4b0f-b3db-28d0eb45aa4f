<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/contentView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitXY"
        tools:src="@drawable/comprehensive_topic_backpic" />

    <ImageView
        android:id="@+id/backview"
        android:theme="@style/FragmentBackButton"
        android:layout_width="@dimen/m80"
        android:layout_height="@dimen/m80"
        android:layout_marginStart="@dimen/m44"
        android:layout_marginTop="@dimen/m24"
        android:padding="@dimen/m16"
        android:scaleType="centerCrop"
        android:src="@drawable/base_back_brand"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.kaolafm.kradio.component.ui.base.view.KradioTextView
        android:id="@+id/topicNameTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/m20"
        android:text="话题"
        android:textColor="@color/comprehensive_topic_primary_color"
        android:textSize="@dimen/m30"
        app:kt_font_weight="0.3"
        app:layout_constraintBottom_toBottomOf="@+id/backview"
        app:layout_constraintStart_toEndOf="@+id/backview"
        app:layout_constraintTop_toTopOf="@+id/backview" />

    <ImageView
        android:id="@+id/voiceBroadcastBt"
        android:layout_width="@dimen/m154"
        android:layout_height="@dimen/m48"
        android:layout_marginStart="@dimen/m20"
        android:src="@drawable/comprehensive_brand_topic_voice_broadcast"
        android:visibility="gone"
        android:contentDescription="@string/content_desc_topic_detail_voice_broadcast"
        app:layout_constraintBottom_toBottomOf="@+id/backview"
        app:layout_constraintStart_toEndOf="@+id/topicNameTv"
        app:layout_constraintTop_toTopOf="@+id/backview"
        tools:visibility="visible" />


    <View
        android:id="@+id/centerDivider"
        android:layout_width="@dimen/m4"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/x580"
        android:layout_marginTop="@dimen/y159"
        android:layout_marginBottom="@dimen/y81"
        android:background="@drawable/comprehensive_brand_gradient_divider"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/left_panel"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/x60"
        android:layout_marginTop="@dimen/m128"
        android:layout_marginEnd="@dimen/x40"
        android:layout_marginBottom="@dimen/m40"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/centerDivider"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginBottom="@dimen/m24"
            app:layout_constraintBottom_toTopOf="@id/publishBtn"
            app:layout_constraintTop_toTopOf="parent">

            <com.kaolafm.kradio.component.ui.base.view.RoundCircleImageView
                android:id="@+id/topicPic"
                android:layout_width="@dimen/m120"
                android:layout_height="@dimen/m120"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:round_radius="@dimen/m9" />

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/comprehensive_brand_topic_detail_cover_stroke"
                app:layout_constraintBottom_toBottomOf="@id/topicPic"
                app:layout_constraintEnd_toEndOf="@id/topicPic"
                app:layout_constraintStart_toStartOf="@id/topicPic"
                app:layout_constraintTop_toTopOf="@id/topicPic" />

            <com.kaolafm.kradio.component.ui.base.view.KradioTextView
                android:id="@+id/topicTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/m20"
                android:lineSpacingExtra="@dimen/m12"
                android:maxLines="2"
                android:textColor="@color/comprehensive_topic_primary_color"
                android:textSize="@dimen/m26"
                app:kt_font_weight="0.7"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toTopOf="@id/topicJoinPeopleCount"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toEndOf="@+id/topicPic"
                app:layout_constraintTop_toTopOf="@+id/topicPic"
                app:layout_constraintVertical_chainStyle="packed"
                tools:text="TextViewTextViewTextViewTextViewTextViewTextViewTextViewTextViewTextViewTextViewTextViewTextViewTextViewTextViewTextViewTextViewTextViewTextViewTextViewTextViewTextViewTextView" />

            <TextView
                android:id="@+id/topicJoinPeopleCount"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/m36"
                android:textColor="@color/comprehensive_topic_subtitle_text_color"
                android:textSize="@dimen/m24"
                app:layout_constraintBottom_toBottomOf="@id/topicPic"
                app:layout_constraintEnd_toStartOf="@id/topicJoinPeopleCountTip"
                app:layout_constraintStart_toStartOf="@id/topicTitle"
                app:layout_constraintTop_toBottomOf="@id/topicTitle"
                tools:text="6321" />

            <TextView
                android:id="@+id/topicJoinPeopleCountTip"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/m36"
                android:text="人参与"
                android:textColor="@color/comprehensive_topic_subtitle_text_color"
                android:textSize="@dimen/m18"
                app:layout_constraintBaseline_toBaselineOf="@id/topicJoinPeopleCount"
                app:layout_constraintEnd_toStartOf="@id/topicReadCount"
                app:layout_constraintStart_toEndOf="@id/topicJoinPeopleCount" />

            <TextView
                android:id="@+id/topicReadCount"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/m36"
                android:layout_marginStart="@dimen/x20"
                android:textColor="@color/comprehensive_topic_subtitle_text_color"
                android:textSize="@dimen/m24"
                app:layout_constraintBaseline_toBaselineOf="@id/topicJoinPeopleCountTip"
                app:layout_constraintEnd_toStartOf="@id/topicReadCountTip"
                app:layout_constraintStart_toEndOf="@id/topicJoinPeopleCountTip"
                tools:text="6321" />

            <TextView
                android:id="@+id/topicReadCountTip"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/m36"
                android:text="阅读量"
                android:textColor="@color/comprehensive_topic_subtitle_text_color"
                android:textSize="@dimen/m18"
                app:layout_constraintBaseline_toBaselineOf="@id/topicJoinPeopleCountTip"
                app:layout_constraintStart_toEndOf="@id/topicReadCount" />

            <TextView
                android:id="@+id/topicIntroduceTv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:minHeight="@dimen/m108"
                android:layout_marginTop="@dimen/m10"
                android:ellipsize="end"
                android:lineSpacingExtra="@dimen/m12"
                android:maxLines="3"
                android:textColor="@color/comprehensive_topic_subtitle_text_color"
                android:textSize="@dimen/m24"
                app:layout_constrainedWidth="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toStartOf="@id/topicPic"
                app:layout_constraintTop_toBottomOf="@id/topicPic"
                tools:text="介绍介绍介绍介" />

            <com.kaolafm.kradio.component.ui.base.view.KradioTextView
                android:id="@+id/topicAboutTip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/y10"
                android:layout_marginEnd="@dimen/x40"
                android:text="相关内容"
                android:textColor="@color/comprehensive_topic_primary_color"
                android:textSize="@dimen/m26"
                android:visibility="gone"
                app:kt_font_weight="0.3"
                app:layout_constrainedWidth="true"
                app:layout_constraintStart_toStartOf="@id/topicPic"
                app:layout_constraintTop_toBottomOf="@id/topicIntroduceTv"
                tools:visibility="visible" />


            <com.kaolafm.kradio.common.widget.banner.KradioBannerView
                android:id="@+id/topicAboutRv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/m10"
                app:autoStart="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@id/topicAboutTip"
                app:layout_constraintTop_toBottomOf="@id/topicAboutTip"
                app:playWhenSingleData="false"
                app:setDirection="RIGHT_TO_LEFT"
                app:setInterval="5000" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <ImageView
            android:id="@+id/publishBtn"
            android:contentDescription="@string/content_desc_topic_detail_publish"
            android:layout_width="@dimen/m320"
            android:layout_height="@dimen/m72"
            android:layout_marginStart="@dimen/x80"
            android:src="@drawable/comprehensive_brand_topic_baseinfo_publish_btn"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/right_panel"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/m60"
        android:layout_marginTop="@dimen/m107"
        android:layout_marginEnd="@dimen/m30"
        android:layout_marginBottom="@dimen/m40"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/centerDivider"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/postsCountTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/m10"
            android:layout_marginTop="@dimen/m5"
            android:textColor="@color/comprehensive_topic_primary_color"
            android:textSize="@dimen/m26"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="发帖3214" />

        <TextView
            android:id="@+id/postsUpdateTimeTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/m20"
            android:textColor="@color/comprehensive_topic_primary_color"
            android:textSize="@dimen/m26"
            android:visibility="gone"
            app:layout_constraintStart_toEndOf="@id/postsCountTv"
            app:layout_constraintTop_toTopOf="@id/postsCountTv"
            tools:text="10:30更新" />

        <RadioGroup
            android:id="@+id/commentListSortGroup"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/m30"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <RadioButton
                android:id="@+id/rbSortTime"
                android:layout_width="@dimen/m90"
                android:layout_height="@dimen/m48"
                android:background="@drawable/comprehensive_topic_sort_type_rb_bg"
                android:button="@null"
                android:gravity="center"
                android:text="时间"
                android:textColor="@color/comprehensive_topic_sort_type_rb_text_color"
                android:textSize="@dimen/m26" />

            <RadioButton
                android:id="@+id/rbSortHot"
                android:layout_width="@dimen/m90"
                android:layout_height="@dimen/m48"
                android:layout_marginStart="@dimen/m30"
                android:background="@drawable/comprehensive_topic_sort_type_rb_bg"
                android:button="@null"
                android:checked="true"
                android:gravity="center"
                android:text="热度"
                android:textColor="@color/comprehensive_topic_sort_type_rb_text_color"
                android:textSize="@dimen/m26" />

        </RadioGroup>

        <ViewStub
            android:id="@+id/postsEmptyViewStub"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/m25"
            android:layout="@layout/search_result_exception_layout"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/postsCountTv" />

        <com.kaolafm.kradio.common.widget.refresh.KradioSmartRefreshLayout
            android:id="@+id/postsListSrl"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/m25"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/postsCountTv">

            <com.kaolafm.kradio.online.common.view.ColorSettableClassicsHeader
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/m30"
                app:srlArrowColor="@color/comprehensive_search_srl_color"
                app:srlDrawableMarginRight="@dimen/m12"
                app:srlDrawableSize="@dimen/m40"
                app:srlEnableLastTime="false"
                app:srlProgressColor="@color/comprehensive_search_srl_color"
                app:srlTextColorTime="@color/comprehensive_search_srl_color"
                app:srlTextColorTitle="@color/comprehensive_search_srl_color"
                app:srlTextRefreshing="@string/comprehensive_hot_search_refreshing_text"
                app:srlTextSizeTitle="@dimen/m24" />

            <com.kaolafm.kradio.common.widget.KLRecyclerView
                android:id="@+id/postsRv"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />


            <com.kaolafm.kradio.online.common.view.ColorSettableClassicsFooter
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/m30"
                app:srlArrowColor="@color/comprehensive_search_srl_color"
                app:srlDrawableMarginRight="@dimen/m12"
                app:srlDrawableSize="@dimen/m40"
                app:srlProgressColor="@color/comprehensive_search_srl_color"
                app:srlTextColorTitle="@color/comprehensive_search_srl_color"
                app:srlTextLoading="@string/comprehensive_hot_search_loading_text"
                app:srlTextSizeTitle="@dimen/m24" />
        </com.kaolafm.kradio.common.widget.refresh.KradioSmartRefreshLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 隐藏控件，用于所见即可说语音执行滑动操作 -->
    <TextView
        android:id="@+id/cd_up"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/transparent"
        android:contentDescription="@string/content_desc_up_scroll"
        android:text="@string/content_desc_up_scroll"
        android:textColor="@color/transparent"
        android:textSize="1sp"
        app:layout_constraintEnd_toEndOf="@id/right_panel"
        app:layout_constraintStart_toStartOf="@id/right_panel"
        app:layout_constraintBottom_toTopOf="@id/right_panel"
        tools:ignore="SmallSp" />

    <TextView
        android:id="@+id/cd_down"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/transparent"
        android:contentDescription="@string/content_desc_down_scroll"
        android:text="@string/content_desc_down_scroll"
        android:textColor="@color/transparent"
        android:textSize="1sp"
        app:layout_constraintEnd_toEndOf="@id/right_panel"
        app:layout_constraintStart_toStartOf="@id/right_panel"
        app:layout_constraintTop_toBottomOf="@id/right_panel"
        tools:ignore="SmallSp" />
</androidx.constraintlayout.widget.ConstraintLayout>