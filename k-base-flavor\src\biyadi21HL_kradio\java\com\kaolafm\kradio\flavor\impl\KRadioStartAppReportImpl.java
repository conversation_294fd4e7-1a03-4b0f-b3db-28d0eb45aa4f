//package com.kaolafm.kradio.flavor.impl;
//
//import com.kaolafm.kradio.lib.base.flavor.KRadioStartAppReportInter;
//import com.kaolafm.report.event.StartReportEvent;
//
//import static com.kaolafm.kradio.uitl.Constants.APP_START_EVENT_CODE;
//
///******************************************
// * 类描述:
// *
// * @version: V1.0
// * @author: yangshaoning
// * @time: 2020-03-04 17:07
// ******************************************/
//public class KRadioStartAppReportImpl implements KRadioStartAppReportInter {
//    @Override
//    public boolean reportStartApp(Object... args) {
//        StartReportEvent startReportEvent = new StartReportEvent();
//
//        startReportEvent.setEventcode(APP_START_EVENT_CODE);
//        startReportEvent.setType((String) args[0]);
//
//        return true;
//    }
//}
