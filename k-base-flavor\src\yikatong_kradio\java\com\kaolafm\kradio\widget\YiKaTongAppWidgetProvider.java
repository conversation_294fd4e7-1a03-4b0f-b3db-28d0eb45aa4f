package com.kaolafm.kradio.widget;

import android.app.PendingIntent;
import android.appwidget.AppWidgetManager;
import android.appwidget.AppWidgetProvider;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.os.Process;
import android.util.Log;
import android.widget.RemoteViews;


import com.kaolafm.kradio.k_kaolafm.cp.CP;
import com.kaolafm.kradio.k_kaolafm.home.player.PlayerHelper;
import com.kaolafm.kradio.k_kaolafm.subscribe.SubscribeManager;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.NetworkUtil;


/**
 * <pre>
 *     <AUTHOR> Wenchl
 *     e-mail : <EMAIL>
 *     time   : 2019/04/03
 *     desc   :
 *     version: 1.0
 * </pre>
 */
public class YiKaTongAppWidgetProvider extends AppWidgetProvider {

    private static final String TAG = "YiKaTongWidget";

    //播放
    public static final String PLAY_ACTION = "kradio.widget.yikatong.play";
    //暂停
    public static final String PAUSE_ACTION = "kradio.widget.yikatong.pause";
    //上一首
    public static final String PREV_ACTION = "kradio.widget.yikatong.prev";
    //下一首
    public static final String NEXT_ACTION = "kradio.widget.yikatong.next";
    //收藏
    public static final String SUBSCRIPTION_ACTION = "kradio.widget.yikatong.subscription";

    public static final String EXIT_ACTION ="com.kradio.yikatong.widget.exit";
    @Override
    public void onReceive(Context context, Intent intent) {
        super.onReceive(context, intent);
        Log.i(TAG,"onReceive action:"+intent.getAction() +" myPid:"+ Process.myPid());
        dealAction(context,intent);
    }

    @Override
    public void onUpdate(Context context, AppWidgetManager appWidgetManager, int[] appWidgetIds) {

        for (int appWidgetId : appWidgetIds) {
            Log.i(TAG,"onUpdate："+appWidgetId);
        }

    }

    @Override
    public void onDeleted(Context context, int[] appWidgetIds) {
        super.onDeleted(context, appWidgetIds);
        Log.i(TAG,"onDeleted");
    }

    @Override
    public void onDisabled(Context context) {
        super.onDisabled(context);
        Log.i(TAG,"onDisabled");
    }

    @Override
    public void onEnabled(Context context) {
        super.onEnabled(context);
        Log.i(TAG,"onEnabled");
//        WidgetUpdateManager.getInstance().init();
        WidgetUpdateManager.getInstance().updateWidgetInfo(PlayerManager.getInstance().getCurPlayItem(),null);
        WidgetUpdateManager.getInstance().getBitmap(PlayerManager.getInstance().getCurPlayItem());
    }

    private void dealAction(Context context,Intent intent){
        Log.i(TAG,"dealAction："+NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext()));
        PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
        switch (intent.getAction()) {
            case PLAY_ACTION:
                if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
                    return;
                }
                if (WidgetUpdateManager.getInstance().isShowRadio() && playItem != null){
                    PlayerManagerHelper.getInstance().switchPlayerStatus(true);
                } else {
                    WidgetUpdateManager.getInstance().playBrandRadio();
                }
                break;
            case PAUSE_ACTION:
                if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
                    return;
                }
                PlayerManagerHelper.getInstance().switchPlayerStatus(true);
                break;
            case PREV_ACTION:
                if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
                    return;
                }
                PlayerManager.getInstance().playPre();
                break;
            case NEXT_ACTION:
                if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
                    return;
                }
                PlayerManager.getInstance().playNext();
                break;
            case SUBSCRIPTION_ACTION:
                if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
                    return;
                }
                WidgetUpdateManager.getInstance().widgetSubscribe(PlayerHelper.getSubscribeId());
                break;
            case EXIT_ACTION:
                WidgetUpdateManager.getInstance().destroy();
                WidgetUpdateManager.getInstance().updateWidgetInfo(null,null);
                break;
        }
    }

}
