package com.kaolafm.kradio.brand.comprehensive.topic;

import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Rect;
import android.os.Bundle;

import androidx.annotation.IdRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewStub;
import android.widget.ImageView;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.brand.comprehensive.adapter.ComprehensivePostsAdapter;
import com.kaolafm.kradio.brand.comprehensive.adapter.TopicRelatedContentAdapter;
import com.kaolafm.kradio.brand.mvp.ITopicDetailView;
import com.kaolafm.kradio.brand.mvp.TopicPresenter;
import com.kaolafm.kradio.common.base.BaseSkinAppCompatActivity;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.kradio.common.utils.CommonUtils;
import com.kaolafm.kradio.common.widget.banner.KradioBannerView;
import com.kaolafm.kradio.component.ui.base.utils.ComponentUtils;
import com.kaolafm.kradio.k_kaolafm.BuildConfig;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.report.ReportUtil;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.RecyclerViewExposeUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.utils.date.TimeUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.lib.utils.imageloader.listener.OnGetBitmapListener;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.player.router.RouterDesturlPlayCallbackImpl;
import com.kaolafm.kradio.user.UserInfoManager;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.topic.TopicRequest;
import com.kaolafm.opensdk.api.topic.model.TopicDetail;
import com.kaolafm.opensdk.api.topic.model.TopicPosts;
import com.kaolafm.opensdk.api.topic.model.TopicRelatedContent;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.TempTaskPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.event.LoginReportEvent;
import com.kaolafm.report.util.ReportConstants;
import com.kaolafm.report.util.ReportParameterManager;
import com.lcodecore.tkrefreshlayout.utils.ScrollingUtil;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnLoadMoreListener;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import org.jetbrains.annotations.NotNull;

import java.net.URLDecoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;


@Route(path = RouterConstance.BRAND_TOPIC_DETAIL_URL_COMPREHENSIVE)
public class ComprehensiveTopicDetailActivity extends BaseSkinAppCompatActivity<TopicPresenter>
        implements ITopicDetailView, RecyclerViewExposeUtil.OnItemExposeListener {

    private long mTopicId = 0;
    private TopicRelatedContentAdapter mAdapter;
    private ComprehensivePostsAdapter mPostsAdapter;

    private final int mPageSize = 10;
    private int mPageNum = 1;


    ImageView topicPic;
    TextView topicTitle;
    ImageView backview;
    ImageView voiceBroadcastBt;
    TextView topicJoinPeopleCount;
    TextView topicReadCount;
    TextView topicIntroduceTv;
    TextView topicAboutTip;
    KradioBannerView<TopicRelatedContent> topicAboutRv;
    ImageView publishBtn;
    TextView postsCountTv;
    TextView postsUpdateTimeTv;
    RadioGroup commentListSortGroup;
    SmartRefreshLayout postsListSrl;
    RecyclerView postsRv;
    ViewStub postsEmptyViewStub;
    RadioButton rbSortHot;
    RadioButton rbSortTime;
    ImageView contentView;

    private TopicDetail mTopicDetail;   //话题详情
    private BasePlayStateListener basePlayStateListener;    //语音播报监听
    private PlayItem curPlayItemWhenPlayTempPlayItem;    //播放语音播报之前播放的音频
    private AtomicBoolean isPlayingTemp = new AtomicBoolean(false);
    private TopicPosts mWannerLikePosts;    //需要点赞的帖子
    private boolean needJumpPublishPage = false;    //需要跳转到发布页
    private int mWannerLikePostsPosition;    //需要点赞的帖子在列表中的位置
    private View mErrorLayout;  //错误提示
    private RecyclerViewExposeUtil mExposeRvUtil;

    private RouterDesturlPlayCallbackImpl routerDesturlPlayCallback = new RouterDesturlPlayCallbackImpl();

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        CommonUtils.getInstance().initGreyStyle(getWindow());
    }

    @Override
    protected void onPostCreate(@Nullable Bundle savedInstanceState) {
        super.onPostCreate(savedInstanceState);
        RequestOptions drawableOption = new RequestOptions()
                .override(ScreenUtil.getScreenWidth(), ScreenUtil.getScreenHeight());
        Glide.with(this).asDrawable().load(R.drawable.comprehensive_topic_backpic).apply(drawableOption).into(contentView);
    }

    @Override
    protected TopicPresenter createPresenter() {
        return new TopicPresenter(this);
    }

    @Override
    public void onGetTopicDetailSuccess(TopicDetail topicDetail) {
        this.mTopicDetail = topicDetail;
        if (!StringUtil.isEmpty(topicDetail.getVoiceUrl())) {
            ViewUtil.setViewVisibility(voiceBroadcastBt, View.VISIBLE);
            //语音播报按钮
            ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_TOPIC_PAGE_VOICE_BROADCAST, "语音播报", getPageId()
                    , ReportConstants.CONTROL_TYPE_SCREEN));
        }
        ImageLoader.getInstance().getBitmapFromCache(this, topicDetail.getCover(), new OnGetBitmapListener() {
            @Override
            public void onBitmap(Bitmap srcBitmap) {
                if (srcBitmap.isRecycled()) {
                    return;
                }
                if (srcBitmap != null && topicPic != null) {
                    topicPic.setImageBitmap(srcBitmap);
                }
            }
        });

        topicTitle.setText(formatTitle(topicDetail.getTitle()));
        topicJoinPeopleCount.setText(ComponentUtils.getInstance().formatNumber(topicDetail.getUserCount(), 1));
        topicReadCount.setText(ComponentUtils.getInstance().formatNumber(topicDetail.getReadCount(), 1));
        topicIntroduceTv.setText(topicDetail.getDes());

        topicTitle.post(new Runnable() {
            @Override
            public void run() {
                if (topicJoinPeopleCount != null) {
                    ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) topicJoinPeopleCount.getLayoutParams();
                    if (topicTitle.getLineCount() > 1) layoutParams.topMargin = 0;
                    else layoutParams.topMargin = ResUtil.getDimen(R.dimen.m10);
                    topicJoinPeopleCount.setLayoutParams(layoutParams);
                }
            }
        });

        if (mAdapter == null) {
            mAdapter = new TopicRelatedContentAdapter();
            topicAboutRv.setAdapter(mAdapter);
            topicAboutRv.setItemShowListener(new KradioBannerView.IKradioBannerItemShowListener<TopicRelatedContent>() {
                @Override
                public void onItemShow(TopicRelatedContent o, int position) {
                    Map<String, String> stringStringMap = RouterManager.getInstance().parseUrlParams(o.getLink());
                    if (stringStringMap.containsKey(Constants.ROUTER_PARAMS_KEY_RESOURCE_TYPE)) {
                        String type = stringStringMap.get(Constants.ROUTER_PARAMS_KEY_RESOURCE_TYPE);
                        reportContentShowClickEvent(o, position, false, String.valueOf(PlayerConstants.RESOURCES_TYPE_AUDIO).equals(type));
                    }
                }
            });
            topicAboutRv.setItemOnClickListener(new KradioBannerView.IKradioBannerItemClickListener<TopicRelatedContent>() {
                @Override
                public void onItemClick(TopicRelatedContent topicRelatedContent, int position) {
                    if (AntiShake.check(topicRelatedContent)) return;

                    if (StringUtil.isEmpty(topicRelatedContent.getLink())) return;
                    Map<String, String> stringStringMap = RouterManager.getInstance().parseUrlParams(topicRelatedContent.getLink());
                    if (stringStringMap.containsKey(Constants.ROUTER_PARAMS_KEY_RESOURCE_TYPE)) {
                        //有type字段
                        String type = stringStringMap.get(Constants.ROUTER_PARAMS_KEY_RESOURCE_TYPE);
                        reportContentShowClickEvent(topicRelatedContent, position, true, String.valueOf(PlayerConstants.RESOURCES_TYPE_AUDIO).equals(type));
                        if (String.valueOf(PlayerConstants.RESOURCES_TYPE_ALBUM).equals(type)
                                || String.valueOf(PlayerConstants.RESOURCES_TYPE_AUDIO).equals(type)
                                || String.valueOf(PlayerConstants.RESOURCES_TYPE_RADIO).equals(type)
                                || String.valueOf(PlayerConstants.RESOURCES_TYPE_BROADCAST).equals(type)
                                || String.valueOf(PlayerConstants.RESOURCES_TYPE_TV).equals(type)
                                || String.valueOf(PlayerConstants.RESOURCES_TYPE_LIVING).equals(type)
                                || String.valueOf(PlayerConstants.RESOURCES_TYPE_LIVE_STREAM).equals(type)
                                || String.valueOf(PlayerConstants.RESOURCES_TYPE_FEATURE).equals(type)
                                || String.valueOf(PlayerConstants.RESOURCES_TYPE_ONE_KEY_LISTENER_TYPE).equals(type)
                                || String.valueOf(PlayerConstants.RESOURCES_TYPE_PURCHASE_ONE_KEY_LISTENER_TYPE).equals(type)
                                || String.valueOf(PlayerConstants.RESOURCES_TYPE_QQ_MUSIC).equals(type)
                        ) {
                            //播放
                            if (!stringStringMap.containsKey(Constants.ROUTER_PARAMS_KEY_RESOURCE_ID))
                                return;
                            String id = stringStringMap.get(Constants.ROUTER_PARAMS_KEY_RESOURCE_ID);
                            if (PlayerManagerHelper.getInstance().isSameProgram(id)) return;
                            PlayerManagerHelper.getInstance().start(id, Integer.parseInt(type));
                        } else {
                            // 站内跳转
                            RouterManager.getInstance().interceptApplicationJumpEvent(ComprehensiveTopicDetailActivity.this, topicRelatedContent.getLink(), routerDesturlPlayCallback);
                        }
                        return;
                    }
                    // 站内跳转
                    RouterManager.getInstance().interceptApplicationJumpEvent(ComprehensiveTopicDetailActivity.this, topicRelatedContent.getLink(), routerDesturlPlayCallback);
                }
            });
        }
        topicAboutRv.setDatas(topicDetail.getRelatedContent());
        if (ListUtil.isEmpty(mTopicDetail.getRelatedContent())) {
            ViewUtil.setViewVisibility(topicAboutTip, View.GONE);
        } else {
            ViewUtil.setViewVisibility(topicAboutTip, View.VISIBLE);
        }

        postsCountTv.setText(String.format(ResUtil.getString(R.string.comprehensive_topic_posts_count_tip), ComponentUtils.getInstance().formatNumber(topicDetail.getPostsCount(), 1)));
        if (topicDetail.getUpdateTime() == 0) {
            postsUpdateTimeTv.setVisibility(View.GONE);
        } else {
            postsUpdateTimeTv.setText(String.format(ResUtil.getString(R.string.comprehensive_topic_posts_update_time_tip), TimeUtil.getTopicPostsTime(this, topicDetail.getUpdateTime())));
            postsUpdateTimeTv.setVisibility(View.VISIBLE);
        }

    }

    /**
     * 格式化标题
     *
     * @param title
     * @return
     */
    private String formatTitle(String title) {
        if (StringUtil.isEmpty(title)) {
            return "##";
        }
        StringBuilder sb = new StringBuilder(String.format("#%s", title));
        if (sb.length() > 23) {
            sb.delete(22, sb.length());
            sb.append("...").append("#");
            return sb.toString();
        }
        return sb.append("#").toString();
    }

    @Override
    public String getPageId() {
        return Constants.PAGE_ID_TOPIC_DATEILS;
    }

    /**
     * 左侧推荐
     *
     * @param topicRelatedContent
     * @param position
     */
    private void reportContentShowClickEvent(TopicRelatedContent topicRelatedContent, int position, boolean b, boolean isAudio) {
        String id = "";
        if (!TextUtils.isEmpty(topicRelatedContent.getLink())) {
            id = parseUrlParams(topicRelatedContent.getLink());
        }
        if (isAudio) {
            ReportUtil.addComponentShowAndClickEvent(id,
                    b, "18"
                    , 2, "", position + "",
                    0 + "", ""
                    , "无", getPageId(), "无");
        } else {
            ReportUtil.addComponentShowAndClickEvent("",
                    b, "18"
                    , 2, "", position + "",
                    0 + "", id
                    , "无", getPageId(), "无");
        }
    }

    /**
     * 右侧列表
     *
     * @param topicPosts
     * @param position
     */
    private void reportContentShowEvent(TopicPosts topicPosts, int position) {

        ReportUtil.addComponentShowAndClickEvent(topicPosts.getPostId() + "",
                false, "17"
                , 2, "", position + "",
                0 + "", topicPosts.getPostId() + ""
                , "无", getPageId(), "无");
    }

    /**
     * 通过url解析出参数
     *
     * @param url
     * @return
     */
    private String parseUrlParams(String url) {
        Map<String, String> map = new HashMap<>();
        String id = "";
        try {
            final String charset = "utf-8";
            url = URLDecoder.decode(url, charset);
            if (url.indexOf('?') != -1 && !url.endsWith("?")) {
                final String contents = url.substring(url.indexOf('?') + 1);
                String[] keyValues = contents.split("&");
                for (int i = 0; i < keyValues.length; i++) {
                    String key = keyValues[i].substring(0, keyValues[i].indexOf("="));
                    String value = keyValues[i].substring(keyValues[i].indexOf("=") + 1);
                    map.put(key, value);
                }
            }
            id = map.get(Constants.ROUTER_PARAMS_KEY_RESOURCE_ID);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return id;
    }


    @Override
    public void onGetTopicDetailFailure() {
        if (mPageNum == 1)
            postsListSrl.finishRefresh();
        else
            postsListSrl.finishLoadMore();

        ToastUtil.showNormal(getApplicationContext(), "当前网络异常");
    }

    @Override
    public void onGetPostsListSuccess(BasePageResult<List<TopicPosts>> result) {
        if (mPageNum == 1)
            postsListSrl.finishRefresh();
        else
            postsListSrl.finishLoadMore();
        if (result == null) return;
        postsListSrl.setEnableRefresh(true);
//        postsListSrl.setEnableLoadMore(result.getHaveNext() == 1);
        if (ListUtil.isEmpty(result.getDataList()) && (mPageNum == 1 || mPostsAdapter == null || ListUtil.isEmpty(mPostsAdapter.getDataList()))) {
            showErrorLayout(false);
            return;
        }
        if (mErrorLayout != null) {
            mErrorLayout.setVisibility(View.GONE);
        }
        if (mPostsAdapter == null) {
            mExposeRvUtil = new RecyclerViewExposeUtil();
            mExposeRvUtil.setRecyclerItemExposeListener(postsRv, this);
            mPostsAdapter = new ComprehensivePostsAdapter(result.getDataList(), new ComprehensivePostsAdapter.OnItemLikeIconClickedListener() {
                @Override
                public void onItemLikeClicked(@NotNull TopicPosts mTopicPosts, int position) {
                    if (AntiShake.check(mTopicPosts.getPostId())) return;
                    boolean isLiked = mTopicPosts.getLikeStatus() == TopicPosts.STATUS_LIKED;
                    String buttonId = ButtonExposureOrClickReportEvent.BUTTON_ID_TOPIC_PAGE_POSTS_LIKE;
                    if (isLiked) {
                        buttonId = ButtonExposureOrClickReportEvent.BUTTON_ID_TOPIC_PAGE_POSTS_UNLIKE;
                    }
                    ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(
                            ButtonExposureOrClickReportEvent.MODE_CLICK, buttonId, null,
                            ReportParameterManager.getInstance().getPage(), ReportConstants.CONTROL_TYPE_SCREEN));
                    if (!UserInfoManager.getInstance().isUserBound()) {
                        ComprehensiveTopicDetailActivity.this.mWannerLikePosts = mTopicPosts;
                        ComprehensiveTopicDetailActivity.this.mWannerLikePostsPosition = position;
                        RouterManager.getInstance().jumpPage(RouterConstance.LOGIN_COMPREHENSIVE_URL);
                        return;
                    }
                    mPresenter.toggleLikePosts(mTopicPosts, position);
                }
            });
            postsRv.setAdapter(mPostsAdapter);
            return;
        }
        if (mPageNum == 1) {
            mPostsAdapter.setDataList(result.getDataList());
        } else {
            mPostsAdapter.addDataList(result.getDataList());
        }
    }

    /**
     * 展示错误
     *
     * @param isNetError
     */
    private void showErrorLayout(boolean isNetError) {
        if (mErrorLayout == null) {
            mErrorLayout = postsEmptyViewStub.inflate();
        }
        mErrorLayout.setVisibility(View.VISIBLE);
        ImageView picImage = mErrorLayout.findViewById(R.id.iv_search_exception_pic);
        TextView errorMsgView = mErrorLayout.findViewById(R.id.tv_search_exception_message);
        if (isNetError) {
            picImage.setImageDrawable(ResUtil.getDrawable(R.drawable.ic_network_error));
            errorMsgView.setText(ResUtil.getString(R.string.network_error_toast));
            mErrorLayout.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (postsListSrl != null) {
                        postsListSrl.autoRefresh();
                    }
                }
            });
            return;
        }
        picImage.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_search_exception_pic));
        errorMsgView.setText(ResUtil.getString(R.string.search_no_result));
        mErrorLayout.setOnClickListener(null);
    }

    @Override
    public void onGetPostsListFailure() {
        if (mPageNum == 1)
            postsListSrl.finishRefresh();
        else
            postsListSrl.finishLoadMore();
        ToastUtil.showNormal(getApplicationContext(), "当前网络异常");
        if (mPageNum == 1 || mPostsAdapter == null || ListUtil.isEmpty(mPostsAdapter.getDataList())) {
            showErrorLayout(true);
        }
    }

    @Override
    public void onOperatePostsSuccess(TopicPosts mTopicPosts, int position) {
        if (mPostsAdapter == null || position >= mPostsAdapter.getDataList().size()) return;
        mPostsAdapter.notifyItemChanged(position);
    }

    @Override
    public void onOperatePostsFailure(int operationType) {
        if (operationType == TopicRequest.POSTS_OPERATE_LIKE) {
            ToastUtil.showNormal(this.getApplicationContext(), "点赞失败");
        } else {
            ToastUtil.showNormal(this.getApplicationContext(), "取消点赞失败");
        }
    }


    @Override
    public void onPublishPostsSuccess() {

    }

    @Override
    public void onPublishPostsFailure() {

    }

    @Override
    public int getLayoutId() {
        return R.layout.comprehensive_activity_topic_detail_1920x720;
    }

    @Override
    public int getLayoutId_Tow() {
        return 0;
    }

    @Override
    public void initView(Bundle savedInstanceState) {

        topicPic=findViewById(R.id.topicPic);
        topicTitle=findViewById(R.id.topicTitle);
        backview=findViewById(R.id.backview);
        voiceBroadcastBt=findViewById(R.id.voiceBroadcastBt);
        topicJoinPeopleCount=findViewById(R.id.topicJoinPeopleCount);
        topicReadCount=findViewById(R.id.topicReadCount);
        topicIntroduceTv=findViewById(R.id.topicIntroduceTv);
        topicAboutTip=findViewById(R.id.topicAboutTip);
        topicAboutRv=findViewById(R.id.topicAboutRv);
        publishBtn=findViewById(R.id.publishBtn);
        postsCountTv=findViewById(R.id.postsCountTv);
        postsUpdateTimeTv=findViewById(R.id.postsUpdateTimeTv);
        commentListSortGroup=findViewById(R.id.commentListSortGroup);
        postsListSrl=findViewById(R.id.postsListSrl);
        postsRv=findViewById(R.id.postsRv);
        postsEmptyViewStub=findViewById(R.id.postsEmptyViewStub);
        rbSortHot=findViewById(R.id.rbSortHot);
        rbSortTime=findViewById(R.id.rbSortTime);
        contentView=findViewById(R.id.contentView);

        publishBtn.setOnClickListener(v -> jumpToInputPage());
        voiceBroadcastBt.setOnClickListener(v -> onClickVoiceBroadcast(v) );
        backview.setOnClickListener(v -> onClickBack(v) );

        topicPic.setImageResource(R.drawable.media_default_pic);

        initRelatedContentRv();
        initPostsRv();
        initPostsRg();

        handleVoiceScroll(R.id.cd_up, R.id.cd_down, postsRv);
    }

    private void handleVoiceScroll(@IdRes int upViewId, @IdRes int downViewId, RecyclerView recyclerView){
        View.OnClickListener listener = v -> {
            if(upViewId == v.getId()){
                ScrollingUtil.scrollListByVoice(recyclerView, -1);
//                ToastUtil.showNormal(AppDelegate.getInstance().getContext(), "向上滑动");
                recyclerView.postDelayed(()->{
                    if(recyclerView.canScrollVertically(-1)){
                        postsListSrl.autoLoadMore();
                    }
                }, 1000);

            } else if(downViewId == v.getId()){
                ScrollingUtil.scrollListByVoice(recyclerView, 1);
//                ToastUtil.showNormal(AppDelegate.getInstance().getContext(), "向下滑动");
                recyclerView.postDelayed(()->{
                    if(recyclerView.canScrollVertically(1)){
                        postsListSrl.autoRefresh();
                    }
                }, 1000);
            }
        };
        // 所见即可说
        TextView upScroll = findViewById(R.id.cd_up);
        if(upScroll != null){
            upScroll.setOnClickListener(listener);
        }
        TextView downScroll = findViewById(R.id.cd_down);
        if(downScroll != null){
            downScroll.setOnClickListener(listener);
        }
    }

    private void initRelatedContentRv() {
//        topicAboutRv.addItemDecoration(new TopicRelatedContentItemDecoration(ResUtil.getDimen(R.dimen.m10)));
    }

    private void initPostsRg() {
        commentListSortGroup.check(R.id.rbSortHot);
        commentListSortGroup.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup group, int checkedId) {
                if (postsListSrl != null && mPostsAdapter != null) {
                    mPostsAdapter.setDataList(null);
                    postsListSrl.autoRefresh();
                }

                if (checkedId == R.id.rbSortHot) {
                    ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_TOPIC_PAGE_SORT_WAY_BUTTON_POPULARITY, rbSortHot.getText().toString(), getPageId()
                            , ReportConstants.CONTROL_TYPE_SCREEN));
                } else if (checkedId == R.id.rbSortTime) {
                    ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_TOPIC_PAGE_SORT_WAY_BUTTON_TIME, rbSortTime.getText().toString(), getPageId()
                            , ReportConstants.CONTROL_TYPE_SCREEN));
                }
            }
        });
    }

    private void initPostsRv() {
        int span = 2;
        if (BuildConfig.LAYOUT_TYPE == 1) {
            span = 1;
        }
        StaggeredGridLayoutManager layoutManager = new StaggeredGridLayoutManager(span, StaggeredGridLayoutManager.VERTICAL);
        layoutManager.setGapStrategy(StaggeredGridLayoutManager.GAP_HANDLING_NONE);
        postsRv.setLayoutManager(layoutManager);
        postsRv.addItemDecoration(new PostsListRvItemDecoration(ResUtil.getDimen(R.dimen.m30), ResUtil.getDimen(R.dimen.m30)));
        postsRv.setOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                layoutManager.invalidateSpanAssignments();
            }
        });

        postsListSrl.setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                ViewUtil.setViewVisibility(mErrorLayout, View.GONE);
                int orderWay;
                if (commentListSortGroup.getCheckedRadioButtonId() == R.id.rbSortTime)
                    orderWay = TopicRequest.ORDER_WAY_BY_TIME;
                else orderWay = TopicRequest.ORDER_WAY_BY_POPULARITY;
                refreshPostsList(orderWay);
                mPresenter.getTopicDetail(mTopicId);
            }
        });
        postsListSrl.setOnLoadMoreListener(new OnLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                ViewUtil.setViewVisibility(mErrorLayout, View.GONE);
                mPageNum++;
                int orderWay;
                if (commentListSortGroup.getCheckedRadioButtonId() == R.id.rbSortTime)
                    orderWay = TopicRequest.ORDER_WAY_BY_TIME;
                else orderWay = TopicRequest.ORDER_WAY_BY_POPULARITY;
                mPresenter.loadPostsList(mTopicId, orderWay, mPageNum, mPageSize);
            }
        });
    }

    private void reportLoginEvent() {
        String type = UserInfoManager.getInstance().getLoginType();
        if (TextUtils.isEmpty(type)) {
            return;
        }
        LoginReportEvent event = new LoginReportEvent();
        event.setType(type);
        event.setRemarks1(LoginReportEvent.REMARKS1_TOPIC_SEND);
        ReportHelper.getInstance().addEvent(event);
    }

    @Override
    protected void onResume() {
        super.onResume();
        postsListSrl.autoRefresh();
        if (UserInfoManager.getInstance().isLoginStateChange() && UserInfoManager.getInstance().isUserBound()) {
            if (mWannerLikePosts != null) {
                mPresenter.operatePosts(mWannerLikePosts, mWannerLikePostsPosition, true);
                mWannerLikePosts = null;
            }
            if (needJumpPublishPage) {
                needJumpPublishPage = false;
                jumpToInputPage();
                reportLoginEvent();
            }
        }
        //数据上报
        //返回按钮
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_TOPIC_PAGE_BACK_BUTTON, null, getPageId()
                , ReportConstants.CONTROL_TYPE_SCREEN));
        //发布按钮
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_TOPIC_PAGE_PUBLISH, "发布", getPageId()
                , ReportConstants.CONTROL_TYPE_SCREEN));
        //热度排序按钮
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_TOPIC_PAGE_SORT_WAY_BUTTON_POPULARITY, rbSortHot.getText().toString(), getPageId()
                , ReportConstants.CONTROL_TYPE_SCREEN));
        //时间排序按钮
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_TOPIC_PAGE_SORT_WAY_BUTTON_TIME, rbSortTime.getText().toString(), getPageId()
                , ReportConstants.CONTROL_TYPE_SCREEN));
    }

    /**
     * 刷新数据
     *
     * @param orderWay
     */
    private void refreshPostsList(int orderWay) {
        mPageNum = 1;
        mPresenter.loadPostsList(mTopicId, orderWay, mPageNum, mPageSize);
    }

    @Override
    public void initData() {
        Intent intent = getIntent();
        if (intent == null) return;
        mTopicId = intent.getLongExtra(Constants.ARGUMENT_TOPIC_ID, 0L);
    }

    public void onClickBack(View view) {
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_TOPIC_PAGE_BACK_BUTTON, null, getPageId()
                , ReportConstants.CONTROL_TYPE_SCREEN));
        finish();
    }

    public void onClickVoiceBroadcast(View view) {
        if (mTopicDetail == null || StringUtil.isEmpty(mTopicDetail.getVoiceUrl())) {
            ToastUtil.showNormal(getApplicationContext(), ResUtil.getString(R.string.comprehensive_topic_voice_broadcast_url_empty));
            return;
        }
        if (isPlayingTemp.compareAndSet(false, true)) {
            if (StringUtil.isEmpty(mTopicDetail.getVoiceUrl())) {
                isPlayingTemp.set(false);
                ToastUtil.showNormal(getApplicationContext(), ResUtil.getString(R.string.comprehensive_topic_voice_broadcast_failure));
                return;
            }
            TempTaskPlayItem tempTaskPlayItem = getTempTaskPlayItem(mTopicDetail.getVoiceUrl());
            PlayerManager.getInstance().pause();
            curPlayItemWhenPlayTempPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
            PlayerManager.getInstance().startTempTask(tempTaskPlayItem);
        }
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_TOPIC_PAGE_VOICE_BROADCAST, "语音播报", getPageId()
                , ReportConstants.CONTROL_TYPE_SCREEN));
    }

    /**
     * 创建语音播报PlayItem
     *
     * @param audioUrl
     * @return
     */
    private TempTaskPlayItem getTempTaskPlayItem(String audioUrl) {
        TempTaskPlayItem tempTaskPlayItem = new TempTaskPlayItem();
        tempTaskPlayItem.setNeedPlayStateCallBack(true);
        tempTaskPlayItem.setTempTaskType(PlayerConstants.TEMP_TASK_TYPE_HINT);
        initBaseStateListener();
        tempTaskPlayItem.setPlayStateListener(basePlayStateListener);
        tempTaskPlayItem.setNeedNextInnerAction(true);
        tempTaskPlayItem.setPlayUrl(audioUrl);
        return tempTaskPlayItem;
    }

    /**
     * 初始化监听器
     */
    private void initBaseStateListener() {
        if (basePlayStateListener == null)
            basePlayStateListener = new BasePlayStateListener() {
                @Override
                public void onPlayerFailed(PlayItem playItem, int what, int extra) {
                    super.onPlayerFailed(playItem, what, extra);
                    isPlayingTemp.set(false);
                    ToastUtil.showNormal(getApplicationContext(), ResUtil.getString(R.string.comprehensive_topic_voice_broadcast_failure));
                    if (!PlayerManagerHelper.getInstance().isPlaying()) {
                        //如果是暂停状态,判断是否有网络;
                        if (checkNetwork()) {
                            return;
                        }
                        PlayerManagerHelper.getInstance().switchPlayerStatus(false);
                    }
                }

                @Override
                public void onPlayerEnd(PlayItem playItem) {
                    super.onPlayerEnd(playItem);
                    isPlayingTemp.set(false);
                }
            };
    }

    private boolean checkNetwork() {
        // 暂无网络
        if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(getApplicationContext())) {
            return true;
        }
        return false;
    }

    /**
     * 跳转到内容输入页
     */
    private void jumpToInputPage() {
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_TOPIC_PAGE_PUBLISH, "发布", getPageId()
                , ReportConstants.CONTROL_TYPE_SCREEN));

        if (!UserInfoManager.getInstance().isUserBound()) {
            needJumpPublishPage = true;
            ToastUtil.showNormal(getApplicationContext(), ResUtil.getString(R.string.comprehensive_topic_publish_posts_no_login));
            RouterManager.getInstance().jumpPage(RouterConstance.LOGIN_COMPREHENSIVE_URL);
            return;
        }
        if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(getApplicationContext())) return;
        Bundle bundle = new Bundle();
        bundle.putLong(ComprehensivePostsInputActivity.ARGUMENT_TOPIC_ID, mTopicId);
        bundle.putString(ComprehensivePostsInputActivity.ARGUMENT_PAGE_ID, getPageId());
        RouterManager.getInstance().jumpPage(RouterConstance.BRAND_POSTS_PUBLISH_URL_COMPREHENSIVE, bundle);
    }

    @Override
    protected void onDestroy() {
        if (isPlayingTemp.compareAndSet(true, false)) {
            PlayerManager.getInstance().stopTempTask();
        }
        super.onDestroy();
    }

    @Override
    public void onItemViewVisible(boolean visible, int position) {
        TopicPosts topicPosts = (TopicPosts) mPostsAdapter.getDataList().get(position);
        reportContentShowEvent(topicPosts, position);

        boolean isLiked = topicPosts.getLikeStatus() == TopicPosts.STATUS_LIKED;
        String buttonId = ButtonExposureOrClickReportEvent.BUTTON_ID_TOPIC_PAGE_POSTS_LIKE;
        if (!isLiked) {
            buttonId = ButtonExposureOrClickReportEvent.BUTTON_ID_TOPIC_PAGE_POSTS_UNLIKE;
        }
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(
                ButtonExposureOrClickReportEvent.MODE_EXPOSURE, buttonId, null,
                ReportParameterManager.getInstance().getPage(), ReportConstants.CONTROL_TYPE_SCREEN));
    }

    /**
     * 话题相关内容的分割线
     */
    private static class TopicRelatedContentItemDecoration extends RecyclerView.ItemDecoration {
        private final int spaceHeight;

        public TopicRelatedContentItemDecoration(int spaceHeight) {
            this.spaceHeight = spaceHeight;
        }

        @Override
        public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
            final int childCount = parent.getAdapter().getItemCount();
            final int adapterPosition = parent.getChildAdapterPosition(view);
            if (isLastRow(adapterPosition, childCount)) {
                outRect.bottom = 0;
            } else {
                outRect.bottom = spaceHeight;
            }
        }

        private boolean isLastRow(int position, int childCount) {
            return position == childCount - 1;
        }
    }

    /**
     * 帖子列表的分割线
     */
    private static class PostsListRvItemDecoration extends RecyclerView.ItemDecoration {
        private final int spaceW, spaceH;

        public PostsListRvItemDecoration(int spaceW, int spaceH) {
            this.spaceW = spaceW;
            this.spaceH = spaceH;
        }

        @Override
        public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
            outRect.right = spaceW;
            outRect.bottom = spaceH;
        }
    }
}
