package com.kaolafm.kradio.upgrader.dialog;

import android.content.Context;
import android.util.Log;
import android.view.View;
import android.widget.TextView;

import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.dialog.BaseCenterDelayDialog;

public class UpGraderDialog extends BaseCenterDelayDialog {
    public static final int UpGraderDialogAlert = 0;
    public static final int UpGraderDialogProgress = 1;
    private int mCode;//code值返回布局的类型 0为提示是否升级  1为显示进度

    private TextView update_sure;
    private TextView update_cancel;

    private TextView  tv_progress;

    public UpGraderDialog(Context context) {
        super(context);
    }

    public void setmCode(int mCode) {
        this.mCode = mCode;
    }

    @Override
    protected void returnContentView(View mContentView) {
        if (mCode==UpGraderDialogAlert){
            update_sure = mContentView.findViewById(R.id.update_sure);
            update_cancel = mContentView.findViewById(R.id.update_cancel);
            update_sure.setOnClickListener(v -> mPositiveListener.onClick(null));
            update_cancel.setOnClickListener(v -> mNativeListener.onClick(null));
        }else {
            tv_progress =  mContentView.findViewById(R.id.tv_progress);
        }
    }

    @Override
    protected int delayTimeDimiss() {
        return 30000;
    }

    @Override
    protected int getLayoutId() {
        return getLayout();
    }


    //获取相应布局
    private int getLayout() {
        Log.i("zsj", "getLayout: mCode = " + mCode);
        switch (mCode) {
            case 0:
                return R.layout.update_select;
            case 1:
                return R.layout.update_progress;
            default:
                return 0;
        }
    }

    public  void upProgress(int progress){
        if (tv_progress!=null){
            tv_progress.setText(String.valueOf(progress));
        }
    }
}
