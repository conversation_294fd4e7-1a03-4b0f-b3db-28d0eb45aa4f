package com.ecarx.sdk;

import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;
import android.util.Log;
import com.ecarx.sdk.step.GetCodeStep;
import com.ecarx.sdk.step.GetOpenUidStep;
import com.ecarx.sdk.step.GetTokenStep;
import com.ecarx.sdk.step.GetUserInfoStep;
import com.ecarx.sdk.step.LoginCompleteStep;
import com.ecarx.sdk.step.LoginStep;
import com.ecarx.sdk.step.RefreshTokenStep;
import com.ecarx.sdk.step.Step;
import com.google.gson.Gson;
import com.kaolafm.kradio.lib.event.KaoLaCountEvent;

import org.greenrobot.eventbus.EventBus;

/**
 * <AUTHOR>
 **/
public class ECarX {
    public static final String TAG = "kradio.ecarx";

    private static final int STATE_LOGINED = 0;
    private static final int STATE_OPENUID_AVAILABLE = 6;
    private static final int STATE_PHONE_AVAILABLE = 1;
    private static final int STATE_ACCESS_TOKEN_AVAILABLE = 2;
    private static final int STATE_REFRESH_TOKEN_AVAILABLE = 3;
    private static final int STATE_CODE_AVAILABLE = 4;
    private static final int STATE_CODE_UNAVAILABLE = 5;

    private static final String SP_KEY_USER_INFO = "user_info";
    private static final String SP_KEY_TOKEN_INFO = "token_info";


    private final SharedPreferences sp;

    private Context mContext;
    private Step step;
    private Gson gson = new Gson();
    private String mEcarxCode;
    private String mOpenUid;

    private ECarX(Context context) {
        this.mContext = context;
        sp = mContext.getSharedPreferences("ecarx", Context.MODE_PRIVATE);
    }

    private volatile static ECarX sInstance;

    public static ECarX getInstance(Context context) {
        if (sInstance == null) {
            synchronized (ECarX.class) {
                if (sInstance == null) {
                    sInstance = new ECarX(context);
                }
            }
        }
        return sInstance;
    }

    private LoginListener mLoginListener;

    public void setLoginListener(LoginListener l) {
        mLoginListener = l;
    }

    public void login() {
        updateStep();
        nextStep();
    }

    public void logout() {
        updateEcarxTokenInfo(null);
        updateEcarxUserInfo(null);
        updateUserInfoManager(null);
    }

    public void updateStep() {
        int state = assessState();
        switch (state) {
            case STATE_LOGINED:
                step = new LoginCompleteStep(ECarX.this);
                break;
            case STATE_OPENUID_AVAILABLE:
                step = new LoginStep(ECarX.this);
                break;
            case STATE_PHONE_AVAILABLE:
                step = new GetOpenUidStep(ECarX.this);
                break;
            case STATE_ACCESS_TOKEN_AVAILABLE:
                step = new GetUserInfoStep(ECarX.this);
                break;
            case STATE_REFRESH_TOKEN_AVAILABLE:
                step = new RefreshTokenStep(ECarX.this);
                break;
            case STATE_CODE_AVAILABLE:
                step = new GetTokenStep(ECarX.this);
                break;
            case STATE_CODE_UNAVAILABLE:
                step = new GetCodeStep(ECarX.this);
                break;
            default:
        }
    }

    public void nextStep() {
        step.exe();
    }

    public void success() {
        if (mLoginListener != null) {
            mLoginListener.onSuccess();
        }
    }

    public void error(Exception e) {
        if (mLoginListener != null) {
            mLoginListener.onError(e);
        }
    }


    private int assessState() {
        int state;
        boolean userBound = UserInfoManager.getInstance().isUserBound();
        if (userBound) {
            state = STATE_LOGINED;
        } else {
            String openUid = getOpenUid();
            if (!TextUtils.isEmpty(openUid)) {
                state = STATE_OPENUID_AVAILABLE;
            } else {
                UserInfo userInfo = getEcarxUserInfo();
                if (userInfo != null && !TextUtils.isEmpty(userInfo.getMobile())) {
                    state = STATE_PHONE_AVAILABLE;
                } else {
                    TokenInfo tokenInfo = getEcarxTokenInfo();
                    if (!isExpiredAccessToken(tokenInfo)) {
                        state = STATE_ACCESS_TOKEN_AVAILABLE;
                    } else {
                        if (!isExpiredRefreshToken(tokenInfo)) {
                            state = STATE_REFRESH_TOKEN_AVAILABLE;
                        } else {
                            String ecarxCode = getEcarxCode();
                            Log.i(TAG, "assessState------->ecarxCode = " + ecarxCode);
                            if (!TextUtils.isEmpty(ecarxCode)) {
                                state = STATE_CODE_AVAILABLE;
                            } else {
                                state = STATE_CODE_UNAVAILABLE;
                            }
                        }
                    }
                }
            }
        }
        return state;
    }


    /***************************************************************************************************************/
    public Context getContext() {
        return mContext;
    }

    public UserInfo getEcarxUserInfo() {
        String json = sp.getString(SP_KEY_USER_INFO, "");
        if (TextUtils.isEmpty(json)) {
            return null;
        } else {
            return gson.fromJson(json, UserInfo.class);
        }

//        UserInfo userInfo  = new UserInfo();
//        userInfo.setMobile("15110197026");
//        userInfo.setNickName("V");
//        userInfo.setUserIco("https://ss1.bdstatic.com/70cFvXSh_Q1YnxGkpoWK1HF6hhy/it/u=2085918456,607512301&fm=26&gp=0.jpg");
//        return userInfo;
    }

    public TokenInfo getEcarxTokenInfo() {
        String json = sp.getString(SP_KEY_TOKEN_INFO, "");
        if (TextUtils.isEmpty(json)) {
            return null;
        } else {
            return gson.fromJson(json, TokenInfo.class);
        }
    }

    public void updateEcarxUserInfo(UserInfo userInfo) {
        String s;
        if (userInfo == null) {
            s = "";
        } else {
            s = gson.toJson(userInfo);
        }
        sp.edit().putString(SP_KEY_USER_INFO, s).commit();
    }

    public void updateEcarxTokenInfo(TokenInfo tokenInfo) {
        String s;
        if (tokenInfo == null) {
            s = "";
        } else {
            s = gson.toJson(tokenInfo);
        }
        sp.edit().putString(SP_KEY_TOKEN_INFO, s).commit();
    }

    public void setEcarxCode(String code) {
        mEcarxCode = code;
    }

    public String getEcarxCode() {
        return mEcarxCode;
    }

    public void setOpenUid(String openUid) {
        mOpenUid = openUid;
    }

    public String getOpenUid() {
        return mOpenUid;
    }

    public void updateUserInfoManager(com.kaolafm.opensdk.api.login.model.UserInfo userInfo) {
        if (userInfo == null) {
            UserInfoManager userInfoManager = UserInfoManager.getInstance();
            userInfoManager.setUserFavicon("");
            userInfoManager.setUserNickName("");
//            userInfoManager.setUserCancleBound();
        } else {
            UserInfoManager infoManager = UserInfoManager.getInstance();
            infoManager.setUserNickName(userInfo.getNickName());
            infoManager.setUserFavicon(userInfo.getAvatar());
            infoManager.localLogin();
            EventBus.getDefault().post(new KaoLaCountEvent(KaoLaCountEvent.LOGIN_SUCESS_EVENT));
        }
    }


    /***************************************************************************************************************/
    private boolean isExpiredAccessToken(TokenInfo tokenInfo) {
        boolean rst = true;
        if (tokenInfo == null || tokenInfo.getAccessTokenResult() == null) {
            rst = true;
        } else {
            String accessToken = tokenInfo.getAccessTokenResult().getAccessToken();
            if (TextUtils.isEmpty(accessToken)) {
                rst = true;
            } else {
                long accessTokenTime = tokenInfo.getAccessTokenTime();
                long expiresIn = tokenInfo.getAccessTokenResult().getExpiresIn() * 60 * 1000;//亿咖通的单位为分钟
                //如果当前时间<保存时的时间+token的有效时长,说明未过期
                if (System.currentTimeMillis() < (accessTokenTime + expiresIn)) {
                    rst = false;
                } else {
                    rst = true;
                }
            }
        }
        Log.i(TAG, "isExpiredAccessToken: tokenInfo=" + tokenInfo);
        Log.i(TAG, "         : 是否过期=" + rst);
        return rst;
    }

    private boolean isExpiredRefreshToken(TokenInfo tokenInfo) {
        boolean rst = true;
        if (tokenInfo == null || tokenInfo.getAccessTokenResult() == null) {
            rst = true;
        } else {
            String token = tokenInfo.getAccessTokenResult().getRefreshToken();
            if (TextUtils.isEmpty(token)) {
                rst = true;
            } else {
                long tokenTime = tokenInfo.getRefreshTokenTime();
                long expiresIn = 30 * 24 * 60 * 60 * 1000;//亿咖通的单位为分钟,refreshToken有效期为30天
                //如果当前时间<保存时的时间+token的有效时长,说明未过期
                if (System.currentTimeMillis() < (tokenTime + expiresIn)) {
                    rst = false;
                } else {
                    rst = true;
                }
            }
        }
        Log.i(TAG, "isExpiredRefreshToken: tokenInfo=" + tokenInfo);
        Log.i(TAG, "         : 是否过期=" + rst);
        return rst;
    }

    public interface LoginListener {
        void onSuccess();

        void onError(Exception e);
    }

}
