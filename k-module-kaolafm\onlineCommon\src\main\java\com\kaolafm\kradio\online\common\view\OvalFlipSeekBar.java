package com.kaolafm.kradio.online.common.view;

import android.animation.ValueAnimator;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PathMeasure;
import android.graphics.RectF;
import android.graphics.Shader;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.os.Message;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;

import com.kaolafm.kradio.k_kaolafm.R;

/**
 * 圆形翻转Seekbar
 * 蔡佳彬
 */
public class OvalFlipSeekBar extends View {

    private float progress;//外部设置的进度
    private float progressWidth;//进度条的线条宽度
    private int maxProgress = 100;//总进度
    private int minMeasureHeight = 245;//进度条的最小高度
    private int startAngle;//背景在圆上的开始角度（从那个角度开始画）
    private int seekStartAngle;//进度在圆上的开始角度（从那个角度开始画）
    private int sweepAngle;//背景椭圆的角度（会画出来的线，其余监督不会画出来）
    private int progressSweepAngle;//下半圆进度条的角度
    private int gapAngle;//下半圆左右缺口的角度（360度为一圈）
    private float thumbSize;//滑块的大小
    private boolean showAnim = false;

    private int mMeasureHeight;
    private int mMeasureWidth;

    private Paint bgPaint = new Paint(Paint.ANTI_ALIAS_FLAG | Paint.DITHER_FLAG);
    private Paint bgPaint2 = new Paint(Paint.ANTI_ALIAS_FLAG | Paint.DITHER_FLAG);
    private Paint progressPaint = new Paint(Paint.ANTI_ALIAS_FLAG | Paint.DITHER_FLAG);

    private RectF pRectF;//背景
    private RectF thumbRectF;//滑块参数
    private Drawable mThumb;//滑块Drawable对象
    private Bitmap bm;//滑块Bitmap对象

    //bitmap设置想要的大小
    private int newWidth = 100;
    private int newHeight = 100;

    private float[] mTempPos;//背景椭圆的数据
    private float[] mTempTan;
    private float[] mTempPosSeek;//进度条背景的数据
    private float[] mTempTanSeek;
    private PathMeasure mSeekPathMeasure;
    private Path mSeekPath;//进度条轨迹
    private Path mSeekBgPath;//进度条背景轨迹

    private float downX;  //按下时的X
    private float mThumbX;         // 拖动按钮  X坐标
    private float mThumbY;         // 拖动按钮  Y坐标
    private float mSeekStartX;         // 进度条开始点坐标X
    private float mSeekStartY;         // 进度条开始点坐标Y
    private float mSeekEndX;         // 进度条结束点坐标 X

    private float unitAngleProgress;//进度条分成一百份。每份占多少度

    private float curProgress = 0;//当前进度
    private boolean isDownThumb;//是否点击了滑块
    private boolean isMoveThumb;//是否在移动滑块
    private OnOvalFlipSeekBarChangeListener ovalFlipSeekBarChangeListener;

    private int touchSlop;
    private boolean canInvalidate = true;
    private float mThumbScale = 1f; //滑块缩放参数，从1到28/16
    private final float THUMB_SCALE_MAX = 28f / 16f; //滑块缩放的最大比例


    public OvalFlipSeekBar(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.OvalFlipSeekBar);
        progress = ta.getInt(R.styleable.OvalFlipSeekBar_of_progress, 0);
        progressWidth = ta.getDimension(R.styleable.OvalFlipSeekBar_of_progress_width, 8f);
        startAngle = ta.getInt(R.styleable.OvalFlipSeekBar_of_start_angle, 150);
        sweepAngle = ta.getInt(R.styleable.OvalFlipSeekBar_of_sweep_angle, 240);
        progressSweepAngle = ta.getInt(R.styleable.OvalFlipSeekBar_of_progress_sweep_angle, 110);
        gapAngle = ta.getInt(R.styleable.OvalFlipSeekBar_of_gap_angle, 5);
        seekStartAngle = ta.getInt(R.styleable.OvalFlipSeekBar_of_seek_start_angle, 35);
//        showAnim = ta.getBoolean(R.styleable.OvalFlipSeekBar_of_show_anim, true);
        mThumb = ta.getDrawable(R.styleable.OvalFlipSeekBar_of_thumb);
        thumbSize = ta.getDimension(R.styleable.OvalFlipSeekBar_of_thumb_size, 50);
        ta.recycle();

        touchSlop = ViewConfiguration.get(getContext()).getScaledTouchSlop();

        unitAngleProgress = (float) (progressSweepAngle / 100.0);

        mSeekPathMeasure = new PathMeasure();
        mTempPos = new float[2];
        mTempTan = new float[2];
        mTempPosSeek = new float[2];
        mTempTanSeek = new float[2];
        mSeekPath = new Path();
        mSeekBgPath = new Path();

        BitmapDrawable bd = (BitmapDrawable) mThumb;
        bm = bd.getBitmap();
        bm = changeBitmapSize(bm);

        bgPaint.setStyle(Paint.Style.STROKE);
        bgPaint.setStrokeCap(Paint.Cap.ROUND);
        bgPaint.setAntiAlias(true);//抗锯齿
        bgPaint.setDither(true);//抖动处理
        bgPaint.setStrokeWidth(progressWidth);

        bgPaint2.setStyle(Paint.Style.STROKE);
        bgPaint2.setStrokeCap(Paint.Cap.ROUND);
        bgPaint2.setAntiAlias(true);//抗锯齿
        bgPaint2.setDither(true);//抖动处理
        bgPaint2.setStrokeWidth(progressWidth);

        progressPaint.setStyle(Paint.Style.STROKE);
        progressPaint.setStrokeCap(Paint.Cap.ROUND);
        progressPaint.setAntiAlias(true);//抗锯齿
        progressPaint.setDither(true);//抖动处理
        progressPaint.setStrokeWidth(progressWidth + 4);

    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        int ws = MeasureSpec.getSize(widthMeasureSpec);     //取出宽度的确切数值
        int wm = MeasureSpec.getMode(widthMeasureSpec);     //取出宽度的测量模式
        int hs = MeasureSpec.getSize(heightMeasureSpec);    //取出高度的确切数值
        int specMode = MeasureSpec.getMode(heightMeasureSpec);    //取出高度的测量模

        mMeasureWidth = getMeasuredWidth();
        mMeasureHeight = Math.max(ws / 8, minMeasureHeight);

//        Log.d("cai3", "--------" + mMeasureHeight + "-----" + mMeasureWidth + "---" + hs + "----" + ws);
        setMeasuredDimension(MeasureSpec.makeMeasureSpec(ws, wm), MeasureSpec.makeMeasureSpec(mMeasureHeight, specMode));
    }

    //获取状态栏的高度
    private int getStatusBarHeight() {
        int resourceId = getResources().getIdentifier("status_bar_height", "dimen", "android");
        if (resourceId > 0) {
            return getResources().getDimensionPixelSize(resourceId);
        }
        return 0;
    }

    Handler handler = new Handler() {
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            if (msg.what == 111) {
                if (curProgress > 0) {

                    curProgress -= 2;
                    postInvalidate();
                    handler.sendEmptyMessageDelayed(111, 20);
                } else {
                    handler.sendEmptyMessageDelayed(222, 20);
                }
            } else {
                if (curProgress < progress) {
                    curProgress += 2;
                    postInvalidate();
                    handler.sendEmptyMessageDelayed(222, 20);
                } else {
                    handler.removeMessages(111);
                    handler.removeMessages(222);
                    showAnim = false;
                }
            }
        }
    };

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        if (!showAnim) {
            curProgress = progress;
        }

        drawBg(canvas);
        drawProgress(canvas);
        if (mSeekEndX <= 0) {
            curProgress = 100;
            invalidate();
        }
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        mSeekPathMeasure.setPath(mSeekPath, false);
//        Log.d("cai3", "----onSizeChanged-----" + w + "---" + h + "@@@@@@" + getStatusBarHeight());
        float halfProgressWidth = progressWidth / 2;
        pRectF = new RectF(halfProgressWidth + getPaddingLeft(),
                halfProgressWidth + getPaddingTop(),
                w - halfProgressWidth - getPaddingRight(),
                h - halfProgressWidth - getPaddingBottom());

    }


    /**
     * 绘制椭圆背景
     *
     * @param canvas
     */
    private void drawBg(Canvas canvas) {
//        绘制上半部分圆
        Shader mShader = new LinearGradient(0, 0, mMeasureWidth, mMeasureHeight,
                new int[]{Color.parseColor("#4d0084FF"), Color.parseColor("#4d0084FF"), Color.TRANSPARENT, Color.TRANSPARENT, Color.TRANSPARENT
                        , Color.TRANSPARENT, Color.TRANSPARENT, Color.parseColor("#4d0084FF"), Color.parseColor("#4d0084FF")}, null, Shader.TileMode.REPEAT); // 一个材质,打造出一个线性梯度沿著一条线。
        bgPaint.setShader(mShader);
        canvas.drawArc(pRectF,
                startAngle,
                sweepAngle,
                false,
                bgPaint);

        //绘制下半部分进度条背景
        bgPaint2.setColor(Color.parseColor("#4d0084FF"));
//        pRectFSeek = pRectF;
        canvas.drawArc(pRectF,
                seekStartAngle,
                progressSweepAngle,
                false,
                bgPaint2);
        //保存进度的轨迹
        //根据轨迹获取seek的结束坐标
        PathMeasure mSeekPathMeasure2 = new PathMeasure();
        mSeekBgPath.reset();
        mSeekBgPath.addArc(pRectF, seekStartAngle, progressSweepAngle);
        mSeekPathMeasure2.setPath(mSeekBgPath, false);
        if (null == mSeekPathMeasure2) return;
        mSeekPathMeasure2.getPosTan(1, mTempPosSeek, mTempTanSeek);
        mSeekEndX = mTempPosSeek[0];
    }

    /**
     * 绘制弧形进度条
     *
     * @param canvas
     */
    private void drawProgress(Canvas canvas) {
        Shader mShader = new LinearGradient(0, 0, mMeasureWidth, mMeasureHeight,
                new int[]{Color.TRANSPARENT, Color.parseColor("#0084FF")
                        , Color.parseColor("#0084FF")
                        , Color.parseColor("#00BAFF")
                        , Color.parseColor("#00BAFF")
                        , Color.parseColor("#00BAFF")}, null, Shader.TileMode.MIRROR); // 一个材质,打造出一个线性梯度沿著一条线。
        progressPaint.setShader(mShader);
        for (int i = 0, end = (int) (curProgress * unitAngleProgress); i <= end; i++) {
//            Log.d("cai", "drawProgress-----" + ((startAngle - gapAngle) - i) + "-----------end---" + end + "---curProgress--" + curProgress);
//            if (curProgress < 50) {
//                progressPaint.setStrokeWidth(progressWidth + ((16 - progressWidth) / 50)*curProgress);
//            } else {
//                progressPaint.setStrokeWidth(16);
//            }
            canvas.drawArc(pRectF,
                    (startAngle - gapAngle) - i,
                    4f,
                    false,
                    progressPaint);

            //保存进度的轨迹
            mSeekPath.reset();
            mSeekPath.addArc(pRectF, (startAngle - gapAngle) - i, progressSweepAngle);
            mSeekPathMeasure.setPath(mSeekPath, false);
            if (null == mSeekPathMeasure) return;
            mSeekPathMeasure.getPosTan(1, mTempPos, mTempTan);
            mThumbX = mTempPos[0];
            mThumbY = mTempPos[1];
            if (curProgress == 0) {
                mSeekStartX = mTempPos[0];
                mSeekStartY = mTempPos[1];
            }
        }


        //绘制滑块
        float thumbScaledSize = thumbSize * mThumbScale;

        thumbRectF = new RectF(mThumbX - thumbScaledSize, mThumbY - thumbScaledSize, mThumbX + thumbScaledSize, mThumbY + thumbScaledSize);
//        Log.d("cai2", "-----" + thumbRectF);
        canvas.drawBitmap(bm, null, thumbRectF, progressPaint);
    }

    private Bitmap changeBitmapSize(Bitmap bitmap) {
        int width = bitmap.getWidth();
        int height = bitmap.getHeight();


        //计算压缩的比率
        float scaleWidth = ((float) newWidth) / width;
        float scaleHeight = ((float) newHeight) / height;

        //获取想要缩放的matrix
        Matrix matrix = new Matrix();
        matrix.postScale(scaleWidth, scaleHeight);

        //获取新的bitmap
        bitmap = Bitmap.createBitmap(bitmap, 0, 0, width, height, matrix, true);
//        Log.e("newWidth", "newWidth" + bitmap.getWidth());
//        Log.e("newHeight", "newHeight" + bitmap.getHeight());
        return bitmap;
    }


    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (!isEnabled()) return true;
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN://手指按下
                downX = event.getX();
                //判断是否点在滑块上
//                    Log.d("cai", "-----onTouchEvent----点击了滑块");
                if (isDownThumb(event)) {
                    isDownThumb = true;
                    startSetThumbScaleAnimation();
                    getTouchProgress(event.getX());
                    if (ovalFlipSeekBarChangeListener != null) {
                        ovalFlipSeekBarChangeListener.onStartTrackingTouch(this);
                        ovalFlipSeekBarChangeListener.onProgressChanged(this, progress, true);
                    }
                }
                break;
            case MotionEvent.ACTION_MOVE://按下移动
                if (Math.abs(event.getX() - downX) > touchSlop) {
//                    Log.d("cai", "-----onTouchEvent----移动--X:" + event.getX() + "--Y--" + event.getY() + "-----" + (int) ((event.getX() / unitAngleProgress)) / 10);
                    if (isDownThumb) {
                        isMoveThumb = true;
                        //整个进度条的X轴像素值
                        getTouchProgress(event.getX());
                        if (ovalFlipSeekBarChangeListener != null) {
                            ovalFlipSeekBarChangeListener.onProgressChanged(this, progress, true);
                        }
                        invalidate();
                    }
                }
                break;
            case MotionEvent.ACTION_UP://手指抬起
            case MotionEvent.ACTION_CANCEL://超出区域
                if (isMoveThumb) {
                    isMoveThumb = false;
                    isDownThumb = false;
                    if (ovalFlipSeekBarChangeListener != null) {
                        getTouchProgress(event.getX());
                        ovalFlipSeekBarChangeListener.onStopTrackingTouch(this);
                        ovalFlipSeekBarChangeListener.onProgressChanged(this, progress, true);
                    }
                } else if (isDownSeek(event)) {
                    Log.d("cai", "-----onTouchEvent----点击了进度条");
                    getTouchProgress(event.getX());
                    if (ovalFlipSeekBarChangeListener != null) {
                        ovalFlipSeekBarChangeListener.onSingleClick(this);
                        ovalFlipSeekBarChangeListener.onProgressChanged(this, progress, true);
                    }
                    invalidate();
                }
                isMoveThumb = false;
                isDownThumb = false;
                stopSetThumbScaleAnimation();
                break;
//            case MotionEvent.ACTION_CANCEL://超出区域
//                isMoveThumb = false;
//                isDownThumb = false;
//                break;
        }
        return true;
    }

    private void stopSetThumbScaleAnimation() {
        if (thumbScaleAnimation == null) return;
        if (thumbScaleAnimation.isRunning()) {
            thumbScaleAnimation.cancel();
        }
        thumbScaleAnimation.setFloatValues(mThumbScale,1f);
        thumbScaleAnimation.start();
    }

    //    private AtomicBoolean isThumbScaleAnimationRunning = new AtomicBoolean(false);
    private ValueAnimator thumbScaleAnimation;

    private void startSetThumbScaleAnimation() {
        if (thumbScaleAnimation == null) {
            thumbScaleAnimation = ValueAnimator.ofFloat(1f, THUMB_SCALE_MAX).setDuration(50);
            thumbScaleAnimation.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                @Override
                public void onAnimationUpdate(ValueAnimator animation) {
                    mThumbScale = (float) animation.getAnimatedValue();
                    invalidate();
                }
            });
        }
        thumbScaleAnimation.setFloatValues(mThumbScale, THUMB_SCALE_MAX);
        if (!thumbScaleAnimation.isRunning()) {
            thumbScaleAnimation.start();
        }
    }

    private void getTouchProgress(float getX) {
        float seekbarX = mSeekEndX - mSeekStartX;
        float touchX = (getX - mSeekStartX);
        //因为是圆弧所以在进度加一个补偿值
        if (touchX > seekbarX / 2) {
            touchX -= 30;
        } else {
            touchX += 30;
        }
        float p = Math.round((touchX / (seekbarX) * 100));
//        Log.d("cai", "-----onTouchEvent----移动--X:" + getX + "--r--" + pRectF.right + "----" + p);
        if (p < 100 && p > 0) {
            progress = p;
        } else {
            if (p <= 0) {
                p = 0;
            } else {
                p = 100;
            }
            progress = p;
        }
    }

    /**
     * 是否点在进度条上
     *
     * @return
     */
    private boolean isDownSeek(MotionEvent event) {
        return pRectF.contains(event.getX(), event.getY()) && event.getY() > mSeekStartY - 10 && !showAnim;
    }

    /**
     * 是否点在滑块上
     */
    private boolean isDownThumb(MotionEvent event) {
        return thumbRectF.contains(event.getX(), event.getY()) && !showAnim;
    }

    public void setMaxProgress(int maxProgress) {
        this.maxProgress = maxProgress;
    }

    public int getMaxProgress() {
        return this.maxProgress;
    }

    public void setProgressCanInvalidate(boolean canInvalidate) {
        this.canInvalidate = canInvalidate;
    }

    /**
     * 设置进度
     *
     * @param progress
     */
    public void setProgress(int progress) {
        if (maxProgress != 0 && !isMoveThumb && canInvalidate) {
            //产品要求：如果未获取到开始和结束时间，则进度条应显示最右边
            if (getMaxProgress() == 0) {
                this.progress = 100;
            } else {
                this.progress = (int) ((Float.parseFloat(progress + "") / maxProgress) * 100);
            }
            invalidate();
            if (ovalFlipSeekBarChangeListener != null) {
                ovalFlipSeekBarChangeListener.onProgressChanged(this, this.progress, false);
            }
        }
    }


    /**
     * 设置进度
     *
     * @param progress
     */
    public void setProgress(int progress, boolean showAnim) {
        if (showAnim && canInvalidate) {
            this.showAnim = showAnim;
            //产品要求：如果未获取到开始和结束时间，则进度条应显示最右边
            if (getMaxProgress() == 0) {
                this.progress = 100;
            } else {
                this.progress = (int) ((Float.parseFloat(progress + "") / maxProgress) * 100);
            }
            handler.sendEmptyMessageAtTime(111, 100);
            if (ovalFlipSeekBarChangeListener != null) {
                ovalFlipSeekBarChangeListener.onProgressChanged(this, this.progress, false);
            }
        }

    }

    /**
     * 获取当前进度
     *
     * @return
     */
    public float getProgress() {
        return progress;
    }

    /**
     * 是否在执行动画
     *
     * @return
     */
    public boolean isShowAnim() {
        return showAnim;
    }

    /**
     * 设置监听
     *
     * @param ovalFlipSeekBarChangeListener
     */
    public void setOvalFlipSeekBarChangeListener(OnOvalFlipSeekBarChangeListener ovalFlipSeekBarChangeListener) {
        this.ovalFlipSeekBarChangeListener = ovalFlipSeekBarChangeListener;
    }

    public interface OnOvalFlipSeekBarChangeListener {
        /**
         * 进度发生变化
         *
         * @param seekBar  拖动条
         * @param progress 当前进度数值
         * @param isUser   是否是用户操作, true 表示用户拖动, false 表示通过代码设置
         */
        void onProgressChanged(OvalFlipSeekBar seekBar, float progress, boolean isUser);

        /**
         * 用户开始拖动
         *
         * @param seekBar 拖动条
         */
        void onStartTrackingTouch(OvalFlipSeekBar seekBar);

        /**
         * 用户结束拖动
         *
         * @param seekBar 拖动条
         */
        void onStopTrackingTouch(OvalFlipSeekBar seekBar);

        /**
         * 点击调整进度
         *
         * @param seekBar
         */
        void onSingleClick(OvalFlipSeekBar seekBar);
    }


//    public int getGradient(float fraction, int startColor, int endColor) {
//        if (fraction > 1) fraction = 1;
//        int alphaStart = Color.alpha(startColor);
//        int redStart = Color.red(startColor);
//        int blueStart = Color.blue(startColor);
//        int greenStart = Color.green(startColor);
//        int alphaEnd = Color.alpha(endColor);
//        int redEnd = Color.red(endColor);
//        int blueEnd = Color.blue(endColor);
//        int greenEnd = Color.green(endColor);
//        int alphaDifference = alphaEnd - alphaStart;
//        int redDifference = redEnd - redStart;
//        int blueDifference = blueEnd - blueStart;
//        int greenDifference = greenEnd - greenStart;
//        int alphaCurrent = (int) (alphaStart + fraction * alphaDifference);
//        int redCurrent = (int) (redStart + fraction * redDifference);
//        int blueCurrent = (int) (blueStart + fraction * blueDifference);
//        int greenCurrent = (int) (greenStart + fraction * greenDifference);
//        return Color.argb(alphaCurrent, redCurrent, greenCurrent, blueCurrent);
//    }
}