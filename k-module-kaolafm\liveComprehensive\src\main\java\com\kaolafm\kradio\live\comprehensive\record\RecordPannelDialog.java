package com.kaolafm.kradio.live.comprehensive.record;

import android.Manifest;
import android.content.Context;
import android.content.DialogInterface;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.media.AudioManager;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.SystemClock;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.fragment.app.DialogFragment;
import androidx.core.content.ContextCompat;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;

import com.kaolafm.kradio.common.utils.CommonUtils;
import com.kaolafm.kradio.common.utils.PermissionUtils;
import com.kaolafm.kradio.component.ComponentClient;
import com.kaolafm.kradio.constant.UserComponentConst;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioFocusInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioSpeakImageInter;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.opensdk.api.live.model.MessageBean;
import com.kaolafm.kradio.live.mvp.LivePresenter;
import com.kaolafm.kradio.live.player.HomeLiveManager;
import com.kaolafm.kradio.live.player.LiveStatus;
import com.kaolafm.kradio.live.player.NimManager;
import com.kaolafm.kradio.live.player.RecordUploadHelper;
import com.kaolafm.kradio.live.player.RecorderStatus;
import com.kaolafm.opensdk.api.live.model.SendChatMsgData;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.user.LoginManager;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.event.DialogExposureEvent;
import com.kaolafm.report.event.PlayerUiControlReportEvent;
import com.kaolafm.report.util.ReportConstants;

import org.greenrobot.eventbus.EventBus;
import org.jetbrains.annotations.NotNull;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Observable;
import java.util.Observer;

import static android.media.AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK;

/**
 * <AUTHOR> shiqian
 * @date 2023-02-21
 */
public class RecordPannelDialog extends DialogFragment implements View.OnClickListener, LoginManager.LoginListener, RecordBoxLayout.OnButtonExposedListener {

    private static final String TAG = "RecordPannelDialog";
    RecordBoxLayout recordBoxLayout;

    private KRadioSpeakImageInter mKRadioSpeakImageInter;
    private KRadioAudioFocusInter mKRadioAudioFocusInter;

    private long mProgramId; //节目id

    public static final int CODE_PERMISSION_REQUEST = 1;
    private static final int DURATION_LONG = 1500;
    private static final int RECORD_DURATION = 20 * 1000;
    private static final int RECORD_COUNTDOWN = 1000;
    private long mLastRecordTime;
    private long mLastListenTime;

    private int mSystemVolume;

    private CountDownTimer mRecordTimer;

    private static final String PAGE_ID = "PAGE_ID";
    private String pageId;

    public static RecordPannelDialog getInstance(String pageId) {
        RecordPannelDialog recordPannelDialog = new RecordPannelDialog();
        Bundle bundle = new Bundle();
        bundle.putString(PAGE_ID, pageId);
        recordPannelDialog.setArguments(bundle);
        return recordPannelDialog;
    }

    @Override
    public void onStart() {
        super.onStart();
        CommonUtils.getInstance().initGreyStyle(getDialog().getWindow());
        getDialog().getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        WindowManager.LayoutParams layoutParams = getDialog().getWindow().getAttributes();
        layoutParams.gravity = Gravity.CENTER; // 位置
        layoutParams.dimAmount = 0f;
        getDialog().getWindow().setAttributes(layoutParams);
    }


    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        this.pageId = getArguments().getString(PAGE_ID, "");
        View view = inflater.inflate(R.layout.comprehensive_record_box, container, false);
        recordBoxLayout = view.findViewById(R.id.recordButtonBoxLayout);
        recordBoxLayout.setOnRecordButtonClickListener(this::onClick);
        recordBoxLayout.setOnListenButtonClickListener(this::onClick);
        recordBoxLayout.setOnCancelButtonClickListener(this::onClick);
        recordBoxLayout.setOnSendButtonClickListener(this::onClick);
        recordBoxLayout.setOnButtonExposedListener(this);

        view.findViewById(R.id.cd_close).setOnClickListener((v)->dismiss());

        recordBoxLayout.setVisibility(View.VISIBLE);
        HomeLiveManager.getInstance().addRecorderStatusObserver(mRecorderStatusObserver);
        mKRadioSpeakImageInter = ClazzImplUtil.getInter("KRadioSpeakImageImpl");

        boolean login = LoginManager.getInstance().isLogin();
        recordBoxLayout.notifyUserLoginStatus(login);
        if (!login) {
            reportUnLoginButton();
        }
        LoginManager.getInstance().registerLoginListener(this);

        return view;
    }

    @Override
    public void onDestroy() {
        HomeLiveManager.getInstance().removeRecorderStatusObserver(mRecorderStatusObserver);
        LoginManager.getInstance().unregisterLoginListener(this);
        stopRecordTimer();
        recordBoxLayout = null;
        mOnDismissListener = null;
        super.onDestroy();
    }

    @Override
    public void onDismiss(DialogInterface dialog) {
        super.onDismiss(dialog);
        cancel();
        reportPageShowEvent();
        if (mOnDismissListener != null) {
            mOnDismissListener.onDismiss();
        }
    }

    @Override
    public void onLoginStateChange(int cp, boolean isLogin) {
        recordBoxLayout.notifyUserLoginStatus(isLogin);
        if (!isLogin) {
            reportUnLoginButton();
        }
    }

    @Override
    public void onCancel() {

    }

    private void reportUnLoginButton() {
        reportButton(ButtonExposureOrClickReportEvent.MODE_EXPOSURE,
                ButtonExposureOrClickReportEvent.BUTTON_ID_LIVE_PAGE_SEND_MESSAGE_UNLOGIN,
                ResUtil.getString(R.string.voice_message_please_login));
    }

    @Override
    public void onRecordButtonExposed(@NotNull String name) {
        reportButton(ButtonExposureOrClickReportEvent.MODE_EXPOSURE,
                ButtonExposureOrClickReportEvent.BUTTON_ID_LIVE_PAGE_SEND_MESSAGE_LOGIN, name);
    }


    @Override
    public void onCancelButtonExposed(@NotNull String name) {
        reportButton(ButtonExposureOrClickReportEvent.MODE_EXPOSURE,
                ButtonExposureOrClickReportEvent.BUTTON_ID_LIVE_PAGE_CANCEL_SEND_MESSAGE, name);
    }

    @Override
    public void onListenButtonExposed(@NotNull String name) {
        reportButton(ButtonExposureOrClickReportEvent.MODE_EXPOSURE,
                ButtonExposureOrClickReportEvent.BUTTON_ID_LIVE_PAGE_LISTEN, name);
    }

    @Override
    public void onSendButtonExposed(@NotNull String name) {
        reportButton(ButtonExposureOrClickReportEvent.MODE_EXPOSURE,
                ButtonExposureOrClickReportEvent.BUTTON_ID_LIVE_PAGE_SEND_BUTTON, name);
    }

    @Override
    public void onRecordingButtonExposed(@NotNull String name) {
    }

    @Override
    public void onListeningButtonExposed(@NotNull String name) {

    }

    @Override
    public void onSendingButtonExposed(@NotNull String name) {

    }

    @Override
    public void onSendSucceedButtonExposed(@NotNull String name) {

    }

    public interface OnDismissListener {
        void onDismiss();
    }

    OnDismissListener mOnDismissListener;

    public RecordPannelDialog setOnDismissListener(OnDismissListener onDismissListener) {
        mOnDismissListener = onDismissListener;
        return this;
    }

    public interface OnSendAudioMsgToServerListener {
        void onSendAudioMsgToServer(RecordUploadHelper.UploadParam program, String fileName);
    }

    OnSendAudioMsgToServerListener mOnSendAudioMsgToServerListener;

    public RecordPannelDialog setOnSendAudioMsgToServerListener(OnSendAudioMsgToServerListener onDismissListener) {
        mOnSendAudioMsgToServerListener = onDismissListener;
        return this;
    }

    public interface OnSendAudioMsgSuccessListener {
        void OnSendAudioMsgSuccess();
    }

    OnSendAudioMsgSuccessListener mOnSendAudioMsgSuccessListener;

    public RecordPannelDialog setOnSendAudioMsgSuccessListener(OnSendAudioMsgSuccessListener onSendAudioMsgSuccessListener) {
        mOnSendAudioMsgSuccessListener = onSendAudioMsgSuccessListener;
        return this;
    }

    public RecordPannelDialog setData(long programId) {
        mProgramId = programId;

        return this;
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.live_cancel_button_layout) {
            cancel();
            reportButton(ButtonExposureOrClickReportEvent.MODE_CLICK,
                    ButtonExposureOrClickReportEvent.BUTTON_ID_LIVE_PAGE_CANCEL_SEND_MESSAGE,
                    ResUtil.getString(R.string.voice_message_cancel));
        } else if (id == R.id.live_listen_button_layout) {
            if (HomeLiveManager.getInstance().isPlaying()) {
                stopListen();
            } else if (HomeLiveManager.getInstance().isFinished()) {
                startListen();
                reportButton(ButtonExposureOrClickReportEvent.MODE_CLICK,
                        ButtonExposureOrClickReportEvent.BUTTON_ID_LIVE_PAGE_LISTEN,
                        ResUtil.getString(R.string.voice_message_audition));
            } else if (HomeLiveManager.getInstance().isListened() || HomeLiveManager.getInstance().isFailure()) {
                startListen();
                reportButton(ButtonExposureOrClickReportEvent.MODE_CLICK,
                        ButtonExposureOrClickReportEvent.BUTTON_ID_LIVE_PAGE_LISTEN,
                        ResUtil.getString(R.string.voice_message_audition));
            }
        } else if (id == R.id.micIv || id == R.id.recordLayout) {
//            boolean isUserLogin = ComprehensiveLiveUtil.showLoginIfNotLogin();
            if (LoginManager.getInstance().checkLogin()) {
                if (HomeLiveManager.getInstance().isRecording()) {
                    stopRecord();
                } else if (HomeLiveManager.getInstance().isIdle()) {
                    reportButton(ButtonExposureOrClickReportEvent.MODE_CLICK,
                            ButtonExposureOrClickReportEvent.BUTTON_ID_LIVE_PAGE_SEND_MESSAGE_LOGIN,
                            ResUtil.getString(R.string.voice_message_click_to_start));
                    startRecordWithPermissionCheck();
                } else if (HomeLiveManager.getInstance().isUploading()) {
                    //
                } else if (HomeLiveManager.getInstance().isUploaded()) {
                    HomeLiveManager.getInstance().setRecorderStatus(RecorderStatus.IDLE);
                } else {
                    sendMessage();
                    reportButton(ButtonExposureOrClickReportEvent.MODE_CLICK,
                            ButtonExposureOrClickReportEvent.BUTTON_ID_LIVE_PAGE_SEND_BUTTON,
                            ResUtil.getString(R.string.voice_message_send));
                }
            } else {
                reportButton(ButtonExposureOrClickReportEvent.MODE_CLICK,
                        ButtonExposureOrClickReportEvent.BUTTON_ID_LIVE_PAGE_SEND_MESSAGE_UNLOGIN,
                        ResUtil.getString(R.string.voice_message_please_login));
            }
        }
    }


    private void startRecordWithPermissionCheck() {
        List<String> needPermission = new ArrayList<String>();
        boolean needRecordAudio = ContextCompat.checkSelfPermission(getContext(),
                Manifest.permission.RECORD_AUDIO) != PackageManager.PERMISSION_GRANTED;
        if (needRecordAudio) {
            needPermission.add(Manifest.permission.RECORD_AUDIO);
        }
        boolean needReadExternal = ContextCompat.checkSelfPermission(getContext(),
                Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED;
        if (needReadExternal) {
            needPermission.add(Manifest.permission.READ_EXTERNAL_STORAGE);
        }
        boolean needWriteExternal = ContextCompat.checkSelfPermission(getContext(),
                Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED;
        if (needWriteExternal) {
            needPermission.add(Manifest.permission.WRITE_EXTERNAL_STORAGE);
        }
        if (needPermission.size() > 0) {
//            String[] requestPermissions = new String[needPermission.size()];
//            for (int i = 0; i < needPermission.size(); i++) {
//                requestPermissions[i] = needPermission.get(i);
//            }
//            ActivityCompat.requestPermissions(getActivity(), requestPermissions,
//                    CODE_PERMISSION_REQUEST);
            PermissionUtils permissionUtils = new PermissionUtils(getContext());
            permissionUtils.getDialog();
        } else {
            startRecord();
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
                                           @NonNull int[] grantResults) {
        switch (requestCode) {
            case CODE_PERMISSION_REQUEST:
                if (grantResults.length > 0
                        && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    startRecord();
                } else {

                }
                break;
        }
    }


    private void startRecord() {
        long delta = SystemClock.elapsedRealtime() - mLastRecordTime;
        if (delta < DURATION_LONG) {
            if (LivePresenter.DEBUG_LIVE) {
                Log.i(TAG, "startRecord but period to last recording is too short");
            }
            return;
        }
        //recordAndSetSystemVolume();
        //解除语音助手对麦克风的占用
//        IASRControl ias = (IASRControl) ARouter.getInstance()
//                .build(RouterFragmentConstants.FRAG_KAOLA_VOICE_ASSISTANT_CONTROL).navigation();
//        ias.stopASR();
        HomeLiveManager.getInstance().startRecordDeal();
    }

    /**
     * 录音倒计时，到时间后自动结束录音
     */
    private void startCountdownTimer() {
        if (mRecordTimer != null) {
            mRecordTimer.cancel();
            mRecordTimer = null;
        }
        String s = getResources().getString(R.string.live_second);

        recordBoxLayout.updateRecordText(RECORD_DURATION / 1000 + s);

        mRecordTimer = new CountDownTimer(RECORD_DURATION, RECORD_COUNTDOWN) {
            @Override
            public void onTick(long millisUntilFinished) {
                recordBoxLayout.updateRecordText((millisUntilFinished / 1000) + s);
            }

            @Override
            public void onFinish() {
                stopRecord();
            }
        };
        mRecordTimer.start();
    }

    private void startListenTimer() {
        recordBoxLayout.startListenTimer();
    }

    private void stopListenTimer() {
        recordBoxLayout.stopListenTimer();
    }

    private void stopRecord() {
        String path = HomeLiveManager.getInstance().stopRecord();
        String recordFile = HomeLiveManager.getInstance().getFilePath();
        //restoreSystemVolume();
        //让语音助手继续监听麦克风
//        IASRControl ias = (IASRControl) ARouter.getInstance()
//                .build(RouterFragmentConstants.FRAG_KAOLA_VOICE_ASSISTANT_CONTROL).navigation();
//        ias.startASR();
        stopRecordTimer();
        if (recordFile != null && recordFile.equals(path)) {
            startRecordFinishAnim();
        } else if (HomeLiveManager.RECORD_TIME_TOO_SHORT.equals(path)) {
            ToastUtil.showNormal(getContext(), R.string.live_speak_too_short);
        }

        PlayerManagerHelper.getInstance().play(true);
    }

    private void cancelRecord() {
        if (!HomeLiveManager.getInstance().isRecording()) return;
        stopRecordTimer();
        stopRecordingAnim(false);
//        mRecordButtonText.setVisibility(View.INVISIBLE);
        HomeLiveManager.getInstance().stopRecord(true);
//        IASRControl ias = (IASRControl) ARouter.getInstance()
//                .build(RouterFragmentConstants.FRAG_KAOLA_VOICE_ASSISTANT_CONTROL).navigation();
//        ias.startASR();
    }

    /**
     * 录音正在进行动画，是一个圆在不断地放大-缩小
     */
    private void startRecordFinishAnim() {
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "startRecordFinishAnim");
        }
        if (HomeLiveManager.getInstance().getLiveStatus() != LiveStatus.LIVING) {
            if (LivePresenter.DEBUG_LIVE) {
                Log.i(TAG, "startRecordFinishAnim but live status is not live, return");
            }
            return;
        }
        recordBoxLayout.startRecordFinishAnim();
    }

    private void sendMessage() {
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "sendMessage");
        }
        //可上传状态有：录音完成FINISHED，试听完成LISTENED，正在试听LISTENING, 上传失败FAILURE
        //其他状态：录音中RECORDING，空闲IDLE，正在上传UPLOADING，上传完成UPLOADED则不能上传
        //以此避免无效上传, 但这个判断在按钮点击的时候已经处理了，这里留个备忘
//        if (HomeLiveManager.getInstance().isListened() || HomeLiveManager.getInstance().isPlaying() ||
//                HomeLiveManager.getInstance().isFinished() || HomeLiveManager.getInstance().isFailure()) {
//        }
        stopListen();
        String uid = (String) ComponentClient.obtainBuilder(UserComponentConst.NAME)
                .setActionName(UserComponentConst.GET_USER_ID)
                .build().call().getData().get(UserComponentConst.GET_USER_ID);

        int timeLength = HomeLiveManager.getInstance().getRecordDuration();
        String uploadFileName = RecordUploadHelper.generateFileUploadName();
        String nickName = NimManager.getInstance().getNickName() == null ? "" : NimManager.getInstance().getNickName();
        try {
            nickName = URLEncoder.encode(nickName, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        RecordUploadHelper.UploadParam param = new RecordUploadHelper.UploadParam(mProgramId, uid, nickName
                , timeLength, uploadFileName);
        mOnSendAudioMsgToServerListener.onSendAudioMsgToServer(param, uploadFileName);
//        mRecordButtonImage.setProgress(0);
    }

    private void recordAndSetSystemVolume() {
        //音量设为0
        AudioManager am = (AudioManager) getContext().getSystemService(Context.AUDIO_SERVICE);
        mSystemVolume = am.getStreamVolume(AudioManager.STREAM_MUSIC);
        am.setStreamVolume(AudioManager.STREAM_MUSIC, 0, AudioManager.FLAG_PLAY_SOUND);
    }

    private void restoreSystemVolume() {
        //还原系统音量
        AudioManager am = (AudioManager) getContext().getSystemService(Context.AUDIO_SERVICE);
        am.setStreamVolume(AudioManager.STREAM_MUSIC, mSystemVolume, AudioManager.FLAG_PLAY_SOUND);
    }

    private boolean requestAudioFocus() {
        if (mKRadioAudioFocusInter == null) {
            mKRadioAudioFocusInter = ClazzImplUtil.getInter("KRadioAudioFocusImpl");
        }

        if (mKRadioAudioFocusInter != null) {
            // 解决https://app.huoban.com/tables/2100000007530121/items/2300001158191064?userId=1881599问题
            boolean flag = mKRadioAudioFocusInter.requestAudioFocus(mAudioFocusListener);
            return flag;
        }
        AudioManager am = (AudioManager) getContext().getSystemService(Context.AUDIO_SERVICE);
        boolean status = AudioManager.AUDIOFOCUS_REQUEST_GRANTED ==
                am.requestAudioFocus(mAudioFocusListener, AudioManager.STREAM_MUSIC,
                        AudioManager.AUDIOFOCUS_GAIN);
        Log.i(TAG, "live requestAudioFocus status:" + status);
        return status;
    }

    private boolean abandonAudioFocus() {
        if (mKRadioAudioFocusInter != null) {
            boolean flag = mKRadioAudioFocusInter.abandonAudioFocus(mAudioFocusListener);
            return flag;
        }
        AudioManager am = (AudioManager) getContext().getSystemService(Context.AUDIO_SERVICE);
        boolean status = AudioManager.AUDIOFOCUS_REQUEST_GRANTED ==
                am.abandonAudioFocus(mAudioFocusListener);
        Log.i(TAG, "live abandonAudioFocus status:" + status);
        return status;
    }

    private AudioManager.OnAudioFocusChangeListener mAudioFocusListener
            = new AudioManager.OnAudioFocusChangeListener() {
        @Override
        public void onAudioFocusChange(int focusChange) {
            if (focusChange == AudioManager.AUDIOFOCUS_LOSS_TRANSIENT) {
                Log.i(TAG, "OnAudioFocusChangeListener:AUDIOFOCUS_LOSS_TRANSIENT");
            } else if (focusChange == AudioManager.AUDIOFOCUS_LOSS) {
                Log.i(TAG, "OnAudioFocusChangeListener:AUDIOFOCUS_LOSS");
            } else if (focusChange == AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK) {
                Log.i(TAG, "OnAudioFocusChangeListener:AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK");
            } else if (focusChange == AudioManager.AUDIOFOCUS_GAIN) {
                Log.i(TAG, "OnAudioFocusChangeListener:AUDIOFOCUS_GAIN");
            }
        }
    };

    private void stopRecordTimer() {
        if (mRecordTimer != null) {
            mRecordTimer.cancel();
            mRecordTimer = null;
        }
    }


    private void startListen() {
        long delta = SystemClock.elapsedRealtime() - mLastListenTime;
        if (delta < DURATION_LONG) {
            if (LivePresenter.DEBUG_LIVE) {
                Log.i(TAG, "startRecord but period to last listening is too short");
            }
            return;
        }
        if (requestAudioFocus()) {
            HomeLiveManager.getInstance().startListen();
        }
    }

    private void stopListen() {
        HomeLiveManager.getInstance().stopListen();
        stopListenTimer();
    }

    public void cancel() {
        cancelRecord();
        HomeLiveManager.getInstance().cancel();
        recordBoxLayout.notifyCancel();
    }

    private void startRecordingAnim() {
        recordBoxLayout.notifyStartRecord();
    }

    private void stopRecordingAnim(boolean isRecordFinish) {
        recordBoxLayout.stopRecordingAnim(isRecordFinish);
    }

    private void updateLastRecordTime() {
        mLastRecordTime = SystemClock.elapsedRealtime();
    }

    private void updateLastListenTime() {
        mLastListenTime = SystemClock.elapsedRealtime();
    }

    /**
     * 在录音-上传过程中，随着状态的变化面更新界面
     */
    private Observer mRecorderStatusObserver = new Observer() {
        @Override
        public void update(Observable o, Object arg) {
            RecorderStatus status = (RecorderStatus) arg;
            if (LivePresenter.DEBUG_LIVE) {
                Log.i(TAG, "RecorderStatusObserver update: " + status);
            }

            //录音相关状态的变化只有在直播过程中体现
//            if (HomeLiveManager.getInstance().getLiveStatus() != LiveStatus.LIVING) {
//                if (LivePresenter.DEBUG_LIVE) {
//                    Log.i(TAG, "mRecorderStatusObserver update but live status is not live");
//                }
//                if (status != RecorderStatus.FAILURE) {
//                    // fixBug 直播间语音发送过程中操作断网，概率出现一直显示发送中
//                    return;
//                }
//            }

            switch (status) {
                case CANCEL:
                    updateLastRecordTime();
                    break;
                case IDLE:
                    showRecordIdle();
                    break;
                case RECORDING:
                    startCountdownTimer();
                    showRecording();
                    break;
                case LISTENING:
                    requestAudioFocus();
                    startListenTimer();
                    break;
                case LISTENED:
                    stopListenTimer();
                    abandonAudioFocus();
                    updateLastListenTime();
                    break;
                case FINISHED:
                    showRecordFinished();
                    break;
                case UPLOADING:
                    showRecordUploading();
                    break;
                case UPLOADED:
                    showRecordUploaded();
                    break;
                case FAILURE:
                    showRecordUploadAgainAsFailure();
                    break;

            }
        }
    };

    private void showRecordIdle() {
        stopRecordingAnim(true);
        recordBoxLayout.showRecordIdle();
//        mRecordButtonImage.setDrawProgress(false);
//        mRecordButtonImage.setImageDrawable(SkinCompatResources.getDrawableCompat(this, R.drawable.live_leave_a_message));
//        mSpeakImage.setVisibility(View.VISIBLE);
//        mLoginPromptText.setVisibility(View.VISIBLE);
//        mRecordButtonText.setVisibility(View.INVISIBLE);
//        mLoginPromptText.setText(R.string.live_click_to_speak);
        abandonAudioFocus();
    }

    private void showRecordFinished() {
//        mRecordButtonText.setText(R.string.live_send_message);
//        mLoginPromptText.setVisibility(View.INVISIBLE);
//        mRecordButtonImage.setImageDrawable(SkinCompatResources.getDrawableCompat(this, R.drawable.live_send_message_selector));
//        mRecordButtonImage.setVisibility(View.VISIBLE);
        recordBoxLayout.updateRecordText("");
        stopRecordingAnim(true);
        abandonAudioFocus();
        updateLastRecordTime();
    }

    private void showRecording() {
//        mRecordButtonText.setVisibility(View.VISIBLE);
//        mSpeakImage.setVisibility(View.GONE);
//        mLoginPromptText.setText(R.string.live_click_to_finish);
//        // mRecordButtonImage.setImageResource(R.drawable.live_leave_message_recording);
//        mRecordButtonImage.setImageDrawable(SkinCompatResources.getDrawableCompat(this, R.drawable.live_leave_message_recording));
        startRecordingAnim();
        requestAudioFocus();
    }

    private void showRecordUploading() {
        recordBoxLayout.showRecordUploading();
//        mRecordButtonText.setText(R.string.live_uploading);
//        mLoginPromptText.setVisibility(View.GONE);
//        mRecordButtonImage.setDrawProgress(true);
//        mRecordButtonImage.setVisibility(View.VISIBLE);
    }

    private void showRecordUploaded() {
//        mRecordButtonImage.setDrawProgress(false);
////        mRecordButtonImage.setImageResource(R.drawable.live_message_send_success);
//        mRecordButtonImage.setImageDrawable(SkinCompatResources.getDrawableCompat(this, R.drawable.live_message_send_success));
//        mRecordButtonImage.setVisibility(View.VISIBLE);
//        mRecordButtonText.setVisibility(View.INVISIBLE);
        recordBoxLayout.showRecordUploaded();
        recordBoxLayout.postDelayed(new Runnable() {
            @Override
            public void run() {
                EventBus.getDefault().post(createMineMessage());
                if (mOnSendAudioMsgSuccessListener != null) {
                    mOnSendAudioMsgSuccessListener.OnSendAudioMsgSuccess();
                }
            }
        }, 1000);

//        showChatMessageSent(createMineMessage());
    }

    private void showRecordUploadAgainAsFailure() {
//        mRecordButtonText.setText(R.string.live_upload_again);
//        mLoginPromptText.setVisibility(View.GONE);
//        mRecordButtonImage.setDrawProgress(false);
////        mRecordButtonImage.setImageResource(R.drawable.live_send_failure_selector);
//        mRecordButtonImage.setImageDrawable(SkinCompatResources.getDrawableCompat(this, R.drawable.live_send_failure_selector));
//        mRecordButtonImage.setVisibility(View.VISIBLE);
        recordBoxLayout.showRecordUploadAgainAsFailure();
    }

    private ArrayList<MessageBean> createMineMessage() {
        MessageBean mb = new MessageBean();
        String nickName = NimManager.getInstance().getNickName();
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "createMineMessage nickName: " + nickName);
        }
        if (nickName == null || "null".equals(nickName)) {
            nickName = getResources().getString(R.string.live_nickname_me);
        }
        mb.nickName = nickName;
        mb.sendChatMsgData = new SendChatMsgData();
        mb.sendChatMsgData.file = HomeLiveManager.getInstance().getFilePath();
        mb.sendChatMsgData.sessionId = NimManager.getInstance().getRoomId();
        mb.sendChatMsgData.duration = HomeLiveManager.getInstance().getRecordDuration();
        ArrayList<MessageBean> list = new ArrayList<>(1);
        list.add(mb);
        return list;
    }

    protected long startTime = -1;

    @Override
    public void onResume() {
        super.onResume();
        startTime = System.currentTimeMillis();
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
    }

    /**
     * 页面曝光事件上报
     */
    private void reportPageShowEvent() {
        long duration = System.currentTimeMillis() - startTime;
        if (startTime < 0 || duration < 300) {
            return;
        }
        ReportHelper.getInstance().addEvent(new DialogExposureEvent(getDialogId(), pageId, duration, null));
        Log.i("BaseDialogFragment", "report=" + duration);
        startTime = -1;
    }

    private String getDialogId() {
        return ReportConstants.DIALOG_ID_LIVE_ROOM_SEND_MESSAGE;
    }

    private void reportButton(String mode, String buttonid, String buttonname) {
        PlayItem curPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
        String radioId = null, audioId = null;
        if (curPlayItem != null) {
            radioId = curPlayItem.getRadioId();
            audioId = String.valueOf(curPlayItem.getAudioId());
        }
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(mode,
                buttonid, buttonname, pageId,
                PlayerUiControlReportEvent.CONTROL_TYPE_CLICK, getDialogId(), radioId,
                audioId, audioId, null));
    }
}
