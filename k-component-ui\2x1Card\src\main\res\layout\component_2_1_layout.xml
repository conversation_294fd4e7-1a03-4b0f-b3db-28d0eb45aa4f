<?xml version="1.0" encoding="utf-8"?>
<com.kaolafm.kradio.lib.widget.ratio.RatioConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    app:canScale="false"
    app:wh_ratio="0.98:1"
    tools:background="@drawable/bg_home">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="2"
            android:orientation="horizontal">

            <include
                android:id="@+id/layout_2_1_top_lift"
                layout="@layout/component_mini_card_layout"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1" />

            <include
                android:id="@+id/layout_2_1_top_right"
                layout="@layout/component_mini_card_layout"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginLeft="@dimen/m30"
                android:layout_weight="1" />
        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:layout_weight="3"
            android:id="@+id/layout_2_1_bottom"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/m30"
            tools:background="#a0a0"
            android:scrollbars="none"
            app:layout_constraintTop_toBottomOf="@+id/layout_2_1_top_lift" />
    </LinearLayout>
    <!--    <include-->
    <!--        android:id="@+id/layout_2_1_bottom"-->
    <!--        layout="@layout/component_content_big_card_layout"-->
    <!--        android:layout_width="@dimen/m410"-->
    <!--        android:layout_height="@dimen/m242"-->
    <!--        android:layout_below="@+id/layout_2_1_top_lift"-->
    <!--        android:layout_marginTop="@dimen/m30" />-->
</com.kaolafm.kradio.lib.widget.ratio.RatioConstraintLayout>