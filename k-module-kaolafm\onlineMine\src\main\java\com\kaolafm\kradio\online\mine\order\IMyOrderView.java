package com.kaolafm.kradio.online.mine.order;

import com.kaolafm.kradio.lib.base.mvp.IView;
import com.kaolafm.opensdk.api.purchase.model.Order;

import java.util.List;

/**
 * @Package: com.kaolafm.kradio.comprehensive.user.order
 * @Description:
 * @Author: Maclay
 * @Date: 15:39
 */
public interface IMyOrderView extends IView {

    void hideError();

    void showList(List<Order> list, int currentPage);

    void showError();

    void showError(String errMsg, boolean retry);
}
