package com.kaolafm.kradio.flavor.impl;

import android.annotation.TargetApi;
import android.content.Context;
import android.media.AudioAttributes;
import android.media.AudioFocusRequest;
import android.media.AudioManager;
import android.provider.Settings;


import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioFocusInter;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.player.logic.PlayerManager;

import static android.media.AudioManager.AUDIOFOCUS_GAIN_TRANSIENT;
import static android.media.AudioManager.AUDIOFOCUS_REQUEST_GRANTED;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-03-23 15:49
 ******************************************/
public class KRadioAudioFocusImpl implements KRadioAudioFocusInter {
    private int mAudioSource_Mic = 0;
    private AudioManager mAudioManager = null;

    public KRadioAudioFocusImpl() {
        mAudioSource_Mic = Settings.Global.getInt(AppDelegate.getInstance().getContext().getContentResolver(), "ivi.media.streamtype.defmic", 0);
    }


    @TargetApi(26)
    @Override
    public boolean requestAudioFocus(Object... args) {
        boolean playing = PlayerManager.getInstance().isPlaying();
        if (playing) {
            PlayerManagerHelper.getInstance().pause(false);
        }
        return true;
    }

    @Override
    public boolean abandonAudioFocus(Object... args) {
//        if (mAudioManager != null) {
//            mAudioManager.abandonAudioFocus((AudioManager.OnAudioFocusChangeListener) args[0]);
//            return true;
//        }
        //https://app.huoban.com/tables/2100000007530121/items/2300001651947599?userId=1545533
        boolean playing = PlayerManager.getInstance().isPlaying();
        if (!playing){
            PlayerManagerHelper.getInstance().play(false);
        }
        return false;
    }
}
