package com.kaolafm.kradio.search;

import android.animation.ObjectAnimator;
import android.content.Context;
import android.content.res.Configuration;
import android.graphics.drawable.Drawable;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.kradio.common.utils.CommonUtils;
import com.kaolafm.kradio.component.ui.base.skin.SkinHelper;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.UserCenterInter;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.util.ReportConstants;

import java.util.List;

import static android.view.ViewGroup.LayoutParams.MATCH_PARENT;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

public class TypeSpinner extends FrameLayout {
    public static final int STATE_LOADING = 0;
    public static final int STATE_LOADED = 1;
    public static final int STATE_LOAD_FAILED = 2;
    private int state = 0;//默认加载中

    private TextView mTextView;
    public ImageView mDrawable;

    private String type = "0";

    private TypePopupWindow mPopupWindow;
    private Drawable arrowDrawableSelect;
    private Drawable arrowDrawableUnSelect;

    private OnExpandListener mOnExpandListener;
    private boolean isExpanded = false; //是否为展开状态
    private static final String TAG = "TypeSpinner";

    private boolean isSupportAnimate = true;

    private OnTypeChangedListener mOnTypeChangedListener;
    private String pageId = Constants.PAGE_ID_SEARCH;

    public TypeSpinner(Context context) {
        super(context);
        init(context);
    }

    public TypeSpinner(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public TypeSpinner(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        mTextView = new TextView(context);
        mTextView.setTextColor(ResUtil.getColor(R.color.comprehensive_search_typespinner_text_color));
        mTextView.setTextSize(TypedValue.COMPLEX_UNIT_PX, ResUtil.getDimen(R.dimen.m20));
        mTextView.setGravity(Gravity.CENTER);
        addView(mTextView, new FrameLayout.LayoutParams(MATCH_PARENT, MATCH_PARENT));
        mDrawable = new ImageView(context);
        int dimenW = ResUtil.getDimen(R.dimen.m16);
        int dimenH = ResUtil.getDimen(R.dimen.m10);
        LayoutParams params = new LayoutParams(dimenW, dimenH);
        params.gravity = Gravity.CENTER_VERTICAL | Gravity.END;
        addView(mDrawable, params);
        arrowDrawableSelect = ResUtil.getDrawable(R.drawable.type_arrow_select);
        arrowDrawableUnSelect = ResUtil.getDrawable(R.drawable.type_arrow_unselect);

        arrowDrawableSelect.setBounds(0, 0, dimenW, dimenH);
        arrowDrawableUnSelect.setBounds(0, 0, dimenW, dimenH);
        mDrawable.setImageDrawable(arrowDrawableUnSelect);
        mPopupWindow = new TypePopupWindow(context);
        CommonUtils.getInstance().initGreyStyle(mPopupWindow.getContentView());
        mPopupWindow.addOnClickListener(searchType -> {
            setType(searchType.getType());
            setText(searchType.getTypeName());
            if (mOnTypeChangedListener != null) {
                mOnTypeChangedListener.onTypeChanged(searchType);
            }
        });
        mPopupWindow.setOnDismissListener(() -> {
            isExpanded = false;
            Log.i(TAG, "isExpanded:" + isExpanded);
            setSelected(false);
            animateArrow(false);
        });
        setClickable(true);
        setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                // 添加空的Click是因为修复日产https://app.huoban.com/tables/2100000007530121/items/2300002015992736问题
                // Android系统只有在开发者实现了onClick事件时才会触发触感反馈
                // 这是个配合Android系统解决的通用问题，所以在主干开发版本也提交，避免后续升级时，再次出现该问题。
            }
        });
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        registerEventBus(); // 注册 EventBus
    }


    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        unregisterEventBus(); // 注销 EventBus
    }

    public void registerEventBus() {
        EventBus.getDefault().register(this);
    }

    public void unregisterEventBus() {
        EventBus.getDefault().unregister(this);
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onThemeEvent(UserCenterInter.ThemeChangeEvent themeChangeEvent) {
        String theme = themeChangeEvent == null ? null : themeChangeEvent.getTheme();
        Log.i(TAG, "onThemeEvent() --- theme = " + theme);
        if (TextUtils.isEmpty(theme)){
            return;
        }
        if (TextUtils.equals(SkinHelper.IS_SAME_THEME, theme)){
            return;
        }
        if(isNightMode()){
            arrowDrawableSelect = ResUtil.getDrawable(R.drawable.type_arrow_select);
            arrowDrawableUnSelect = ResUtil.getDrawable(R.drawable.type_arrow_unselect);
            mTextView.setTextColor(ResUtil.getColor(R.color.comprehensive_search_typespinner_text_color));
            animateArrow(false);
        }else{
            arrowDrawableSelect = ResUtil.getDrawable(R.drawable.type_arrow_select_night);
            arrowDrawableUnSelect = ResUtil.getDrawable(R.drawable.type_arrow_unselect_night);
            mTextView.setTextColor(ResUtil.getColor(R.color.comprehensive_search_typespinner_text_color_night));
            animateArrow(false);
        }

    }

    public boolean isNightMode() {
        try {
            String settingsTheme = android.provider.Settings.System.getString(
                getContext().getContentResolver(),
                "android.car.THEME_TYPE"
            );

            if (!android.text.TextUtils.isEmpty(settingsTheme)) {
                return "theme.night".equals(settingsTheme);
            } else {
                // 回退到系统Configuration
                int currentNightMode = getResources().getConfiguration().uiMode
                        & Configuration.UI_MODE_NIGHT_MASK;
                return currentNightMode == Configuration.UI_MODE_NIGHT_YES;
            }
        } catch (Exception e) {
            return false; // 默认白天模式
        }
    }

    public void setData(List<SearchType> searchTypes) {
        setState(STATE_LOADED);
        mPopupWindow.setData(searchTypes);
    }

    public void showNetworkError(String error) {
        setState(STATE_LOAD_FAILED);
        mPopupWindow.showNetworkError(error);
    }

    @Override
    public boolean onTouchEvent(@NonNull MotionEvent event) {
        if (event.getAction() == MotionEvent.ACTION_UP) {
            if (isEnabled() && isClickable()) {
                if (!mPopupWindow.isShowing()) {
                    if (!NetworkUtil.isNetworkAvailable(getContext(), false)) {
                        ToastUtil.showError(AppDelegate.getInstance().getContext(), R.string.no_net_work_str);
                    } else {
                        expand();
                    }
                } else {
                    collapse();
                }
            }
            ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_SEARCH_CATEGORY_TAGS_LABEL
                    , mTextView.getText().toString(), getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
        }
        return super.onTouchEvent(event);
    }

    public boolean isWindowShow() {
        return mPopupWindow.isShowing();
    }

    public void collapse() {
        isExpanded = false;
        Log.i(TAG, "isExpanded:" + isExpanded);
        setSelected(false);
        animateArrow(false);
        mPopupWindow.dismiss();
    }

    public void expand() {
        isExpanded = true;
        Log.i(TAG, "isExpanded:" + isExpanded);
        if (mOnExpandListener != null) {
            mOnExpandListener.onExpand();
        }
        setSelected(true);
        animateArrow(true);
        mPopupWindow.showAsDropDown(this, 0, ResUtil.getDimen(R.dimen.y8));
    }

    public void setSupportAnimate(boolean isSupport) {
        isSupportAnimate = isSupport;
    }

    private void animateArrow(boolean shouldRotateUp) {
        if (!isSupportAnimate) {
            return;
        }

        int start = shouldRotateUp ? 0 : 10000;
        int end = shouldRotateUp ? 10000 : 0;
        Drawable drawable;
        if (shouldRotateUp) {
            drawable = arrowDrawableSelect;
        } else {
            drawable = arrowDrawableUnSelect;
        }
        mDrawable.setImageDrawable(drawable);
        ObjectAnimator animator = ObjectAnimator.ofInt(drawable, "level", start, end);
        animator.start();
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    /**
     * Sets the listener to be called when the window is expand.
     *
     * @param onExpandListener The listener.
     */
    public void setOnExpandListener(OnExpandListener onExpandListener) {
        mOnExpandListener = onExpandListener;
    }

    public void showAccordingToScreen(int orientation) {
        if (mPopupWindow != null) {

            if (mPopupWindow.isShowing()) {
                mPopupWindow.dismiss();
            }

            //目前没有找到很好的方式，在pop showing的情况下进行动态切换，暂时只能在切换横竖屏时dismiss，由用户自己打开
            mPopupWindow.changePopupView(orientation);
        }
    }

    public void setPopupWidth(int width) {
        if (mPopupWindow != null) {
            mPopupWindow.setWidth(width);
        }
    }

    public void setText(String typeName) {
        mTextView.setText(typeName);
    }

    public void setPageId(String pageId) {
        this.pageId = pageId;
        mPopupWindow.setPageId(pageId);
    }

    public String getPageId(){
        return pageId;
    }

    public interface OnExpandListener {
        public void onExpand();
    }

    public boolean isExpanded() {
        return isExpanded;
    }


    public OnTypeChangedListener getOnTypeChangedListener() {
        return mOnTypeChangedListener;
    }

    public void setOnTypeChangedListener(OnTypeChangedListener mOnTypeChangedListener) {
        this.mOnTypeChangedListener = mOnTypeChangedListener;
    }

    interface OnTypeChangedListener {
        public void onTypeChanged(SearchType newType);
    }
}
