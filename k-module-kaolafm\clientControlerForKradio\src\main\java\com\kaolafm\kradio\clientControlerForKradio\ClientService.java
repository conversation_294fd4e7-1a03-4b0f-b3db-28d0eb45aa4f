package com.kaolafm.kradio.clientControlerForKradio;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;
import android.os.RemoteCallbackList;
import android.os.RemoteException;
import android.util.Log;

import com.kaolafm.sdk.core.IClientAPI;


public class ClientService extends Service {

    private static final String TAG = "client.ClientService";
    private IClientAPI.Stub mClientApi;
    RemoteCallbackList<IClientAPI> callbackList;

    @Override
    public void onCreate() {
        Log.i(TAG, "onCreate");
        super.onCreate();
        mClientApi = new ClientImpl(this.getApplicationContext());
        callbackList = new RemoteCallbackList<IClientAPI>() {
            @Override
            public void onCallbackDied(IClientAPI callback, Object cookie) {
                Log.i(TAG, "onCallbackDied: "+callback+" cookie "+cookie);
                try {
                    callback.clientDied();
                } catch (RemoteException e) {
                    Log.i(TAG, "onCallbackDied: "+e);
                    e.printStackTrace();
                }
            }
        };
    }
    @Override
    public IBinder onBind(Intent intent) {
        Log.i(TAG, "onBind: intent=" + intent);
        return mClientApi;
    }
}