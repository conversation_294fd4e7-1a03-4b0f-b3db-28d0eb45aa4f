package com.kaolafm.kradio.live.comprehensive.ui.adapters;

import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.api.live.model.LiveAbility;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.event.PlayerUiControlReportEvent;

import java.util.List;

public class LiveAbilityAdapter extends BaseAdapter<LiveAbility> {

    public LiveAbilityAdapter(List<LiveAbility> dataList) {
        super(dataList);
    }

    @Override
    public int getItemViewType(int position) {
        return 0;
    }

    @Override
    protected BaseHolder<LiveAbility> getViewHolder(ViewGroup parent, int viewType) {
        return new AbilityHolder(inflate(parent, R.layout.comprehensive_live_ability_item, viewType));
    }

    private class AbilityHolder extends BaseHolder<LiveAbility> {

        private RelativeLayout liveAbility;
        private ImageView liveAbilityImg;


        public AbilityHolder(View itemView) {
            super(itemView);

            liveAbility = itemView.findViewById(R.id.live_ability);
            liveAbilityImg = itemView.findViewById(R.id.live_ability_img);

            liveAbility.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if(mOnAbilityStatListener!=null){
                        mOnAbilityStatListener.onAbilityStart(v, mDataList.get(getAdapterPosition()).getType());
                    }
                }
            });
        }

        @Override
        public void setupData(LiveAbility data, int position) {
            if(mDataList.get(position).getType()==1){
                ImageLoader.getInstance().displayImage(itemView.getContext(),
                        R.drawable.comprehensive_live_bottom_record_img, liveAbilityImg);
                itemView.setContentDescription(itemView.getContext().getString(R.string.content_desc_live_send_voice));
            }
            if(mDataList.get(position).getType()==2){
                ImageLoader.getInstance().displayImage(itemView.getContext(),
                        R.drawable.comprehensive_live_bottom_gift_img, liveAbilityImg);
                itemView.setContentDescription(itemView.getContext().getString(R.string.content_desc_live_send_gift));
            }
            if(mDataList.get(position).getType()==3){
                ImageLoader.getInstance().displayImage(itemView.getContext(),
                        R.drawable.comprehensive_live_bottom_goods_img, liveAbilityImg);
                itemView.setContentDescription(itemView.getContext().getString(R.string.content_desc_live_buy));
                if(mOnAbilityStatListener!=null){
                    mOnAbilityStatListener.onGoodsInit(itemView);
                }
            }

        }
    }


    @Override
    public void onViewAttachedToWindow(BaseHolder<LiveAbility> holder) {
        super.onViewAttachedToWindow(holder);
        String buttonid = null;
        switch (getItemData(holder.getAdapterPosition()).getType()) {
            case LiveAbility.LIVE_ABILITY_SEND_MESSAGE:
                buttonid = ButtonExposureOrClickReportEvent.BUTTON_ID_LIVE_ROOM_START_RECORD_MESSAGE;
                break;
            case LiveAbility.LIVE_ABILITY_REWARD:
                buttonid = ButtonExposureOrClickReportEvent.BUTTON_ID_LIVE_ROOM_GIFT_SPAN_CONTROLLER;
                break;
            case LiveAbility.LIVE_ABILITY_SHOPPING:
                buttonid = ButtonExposureOrClickReportEvent.BUTTON_ID_LIVE_ROOM_SHOPPING_CART_SPAN_CONTROLLER;
                break;
        }
        PlayItem curPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
        String radioId = null, audioId = null;
        if (curPlayItem != null) {
            radioId = curPlayItem.getRadioId();
            audioId = String.valueOf(curPlayItem.getAudioId());
        }
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE,
                buttonid, null, Constants.PAGE_ID_LIVE_ROOM, PlayerUiControlReportEvent.CONTROL_TYPE_CLICK, null, radioId, audioId, audioId, null));
    }

    private OnAbilityStatListener mOnAbilityStatListener;

    public interface OnAbilityStatListener{
        void onAbilityStart(View view, Integer type);
        void onGoodsInit(View view);
    }

    public void setOnAbilityStatListener(OnAbilityStatListener onAbilityStatListener){
        mOnAbilityStatListener = onAbilityStatListener;
    }

}
