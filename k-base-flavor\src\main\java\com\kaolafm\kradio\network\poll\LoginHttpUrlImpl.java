package com.kaolafm.kradio.network.poll;

import com.kaolafm.kradio.lib.base.flavor.LoginHttpUrl;
import com.kaolafm.opensdk.api.login.KRadioApiConstants;

import okhttp3.HttpUrl;
import okhttp3.HttpUrl.Builder;

/**
 * <AUTHOR>
 * @date 2020/6/16
 */
public class LoginHttpUrlImpl implements LoginHttpUrl {

    @Override
    public void newBuild(Builder builder, HttpUrl oldUrl) {
        //如果是轮询需要在url中添加sso=silent
        boolean addParameter = oldUrl.toString().contains(KRadioApiConstants.REQUEST_LOGIN_URL)
                || oldUrl.toString().contains(KRadioApiConstants.REQUEST_TOKEN_URL);
        if (addParameter) {
            builder.addQueryParameter("sso", "silent");
        }
    }
}
