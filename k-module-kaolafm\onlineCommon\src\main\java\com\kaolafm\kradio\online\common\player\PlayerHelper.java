package com.kaolafm.kradio.online.common.player;

import androidx.annotation.IntDef;
import android.util.Log;

import com.kaolafm.kradio.common.CP;
import com.kaolafm.kradio.common.bean.BroadcastStatus;
import com.kaolafm.kradio.common.bean.HistoryItem;
import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.OneKeyPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: PlayerHelper.java                                               
 *                                                                  *
 * Created in 2018/4/24 下午6:09                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
public class PlayerHelper {

    public static final String TAG = "player.helper";

    /**
     * 播放参数
     */
    public static class PlayParam {
        @CP.CpType
        public int cpSource;
        public long id;
        public int resType;
        public String from;

        @Override
        public String toString() {
            return "[" + cpSource + ":" + id + ":" + resType + ":" + from + "]";
        }
    }


    public static void play(HistoryItem historyItem) {
        PlayerManagerHelper.getInstance().startHistory(historyItem);
    }


    /**
     * 播放总入口
     *
     * @param pp
     */
    public static void play(PlayParam pp) {
        if (pp == null) {
            return;
        }
        PlayerManagerHelper.getInstance().start(String.valueOf(pp.id), pp.resType);
    }

    /**
     * 判断是否直播中
     *
     * @return
     */
    public static boolean isLiving() {
        boolean playerEnable = PlayerManagerHelper.getInstance().isLivingPlayer();
        return playerEnable;
    }


    public static final int FLAG_TYPE_NON = 0;
    public static final int FLAG_TYPE_LIVE = 1;
    public static final int FLAG_TYPE_PLAYBACK = 2;

    @IntDef({FLAG_TYPE_NON, FLAG_TYPE_LIVE, FLAG_TYPE_PLAYBACK})
    public @interface FlagType {
    }

    public static @FlagType
    int getPlayFlagType(PlayItem playItem) {
        int flagType = FLAG_TYPE_NON;
        int type = playItem.getType();
        switch (type) {
            case PlayerConstants.RESOURCES_TYPE_LIVING: {
                flagType = FLAG_TYPE_LIVE;
            }
            break;
            case PlayerConstants.RESOURCES_TYPE_LIVE_STREAM: {
                if (playItem.isLiving()) {
                    flagType = FLAG_TYPE_LIVE;
                } else {
                    flagType = FLAG_TYPE_PLAYBACK;
                }
            }
            break;
            case PlayerConstants.RESOURCES_TYPE_BROADCAST: {
                if (playItem.isLiving()) {
                    flagType = FLAG_TYPE_LIVE;
                } else {
                    if (BroadcastStatus.BROADCAST_STATUS_PLAYBACK == playItem.getStatus()) {
                        flagType = FLAG_TYPE_PLAYBACK;
                    } else {
                        flagType = FLAG_TYPE_NON;
                    }
                }
            }

            default:
                break;
        }
        return flagType;
    }


    public static final int PLAYER_TYPE_ALBUM = 0;
    public static final int PLAYER_TYPE_LIVE = 1;
    public static final int PLAYER_TYPE_QQ = 2;
    public static final int PLAYER_TYPE_BROADCAST = 3;
    public static final int PLAYER_TYPE_TV = 4;

    @IntDef({PLAYER_TYPE_ALBUM,
            PLAYER_TYPE_LIVE,
            PLAYER_TYPE_QQ,
            PLAYER_TYPE_BROADCAST,
            PLAYER_TYPE_TV})
    public @interface PlayerType {
    }

    public static @PlayerType
    int whichPlayer() {

        int which;
        if (isLiving()) {
            which = PLAYER_TYPE_LIVE;
        } else if (PlayerManagerHelper.getInstance().isBroadcastPlayer()) {
            which = PLAYER_TYPE_BROADCAST;
        } else if (PlayerManagerHelper.getInstance().isTvPlayer()) {
            which = PLAYER_TYPE_TV;
        } else {
            which = PLAYER_TYPE_ALBUM;
        }

        return which;
    }

    /**
     * 统一获取订阅id的方法,因为一键收听的订阅id与众不同
     *
     * @return 如果不是有效的id, 则返回-1;
     */
    public static long getSubscribeId() {
        long subId = -1;

        PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
        if (playItem instanceof OneKeyPlayItem) {
            subId = ((OneKeyPlayItem) playItem).getInfoData().getAlbumId();
        } else {
            try {
                subId = Long.parseLong(playItem.getRadioId());
            } catch (Exception e) {

            }
        }
        return subId;
    }

    public static void play(HomeCell homeCell) {
        Log.i(TAG, "clickToPlay homeCell = " + homeCell);
        if (homeCell == null) {
            return;
        }
//        if (homeCell instanceof BroadcastCell) {
//            handBroadcasts((BroadcastCell) homeCell);
//        }
        Log.i(TAG, "homeCell id = " + homeCell.playId + "   " + homeCell.resType);
        PlayerManagerHelper.getInstance().start(String.valueOf(homeCell.playId), homeCell.resType);
    }

//    private static void handBroadcasts(BroadcastCell homeCell) {
//        Card parent = homeCell.parent;
//        if (parent != null) {
//            List<? extends Cell> childes = parent.childes;
//            if (!ListUtil.isEmpty(childes)) {
//                ArrayList<BroadcastRadioSimpleData> broadcastList = new ArrayList<>();
//                for (int i = 0, size = childes.size(); i < size; i++) {
//                    BroadcastRadioSimpleData brsd = cellToBroadcastItem((HomeCell) childes.get(i));
//                    if (brsd != null) {
//                        broadcastList.add(brsd);
//                    }
//                }
//                PlayerManagerHelper.getInstance().addBroadcastRadioSimpleItems(broadcastList);
//            }
//        }
//    }
//
//    private static BroadcastRadioSimpleData cellToBroadcastItem(HomeCell cell) {
//        BroadcastRadioSimpleData brsd = null;
//        if (cell instanceof BroadcastCell) {
//            brsd = new BroadcastRadioSimpleData();
//            brsd.setBroadcastId(cell.playId);
//            brsd.setImg(cell.imageUrl);
//            String freq = ((BroadcastCell) cell).freq == null ? "" : ((BroadcastCell) cell).freq;
//            brsd.setName(cell.name + "  " + freq);
//        }
//        return brsd;
//    }

    static public void printPlayitem(String aTAG, PlayItem item) {
        Log.d(aTAG, "playitem name=" + item.getTitle()
                + " type=" + item.getType()
                + " radioId=" + item.getRadioId()
                + " audioId=" + item.getAudioId());
    }

}
