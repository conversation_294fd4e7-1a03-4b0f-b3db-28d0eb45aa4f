package com.kaolafm.ad.comprehensive.implement;

import static android.media.AudioManager.AUDIOFOCUS_LOSS;
import static android.media.AudioManager.AUDIOFOCUS_LOSS_TRANSIENT;

import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.ad.AdConstant;
import com.kaolafm.ad.api.model.Advert;
import com.kaolafm.ad.api.model.AudioAdvert;
import com.kaolafm.ad.api.model.ImageAdvert;
import com.kaolafm.ad.comprehensive.KradioAdAudioManager;
import com.kaolafm.ad.comprehensive.ads.image.AdvertisingImagerImpl;
import com.kaolafm.ad.comprehensive.conflict.AdConflict;
import com.kaolafm.ad.comprehensive.control.KradioAdSceneConstants;
import com.kaolafm.ad.comprehensive.listener.IFlashScreenAdPlayerListener;
import com.kaolafm.ad.expose.AdvertisingLifecycleCallback;
import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.ad.expose.AdvertisingPlayer;
import com.kaolafm.kradio.lib.bean.AdPlayItem;
import com.kaolafm.kradio.lib.tts.TTSSpeaker;
import com.kaolafm.kradio.lib.utils.IntentUtils;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.YTLogUtil;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.player.helper.intercept.AdPlayChainIntercept;
import com.kaolafm.kradio.player.helper.intercept.PlayInterceptFactory;
import com.kaolafm.notify.Nolley;
import com.kaolafm.opensdk.api.CrashMessageBaseBean;
import com.kaolafm.opensdk.crash.CrashPlayerHelper;
import com.kaolafm.opensdk.crash.Icrashstate;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.TempTaskPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.VideoAlbumPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.UIThreadUtil;

import java.io.File;

/**
 * 1. 播放广告时, 播放按钮要显示暂停(记录当前播放状态, 暂停播放, 然后插播广告, 控制广告不状态回调)
 * 2. 播放广告时, 如果过了锁定期, 点击播放要跳过广告, 播放当前的碎片 (停止播放的广告, 手动回调playend. 在playend中处理接下来的逻辑, 关闭所有广告包括后置的二次互动)
 * 3. 播放广告时, 如果过了锁定期, 点击下一首,要跳过广告, 播放下一个碎片 (停止当前播放的广告, 关闭所有广告, 但不处理playend之后的动作, 直接获取下一首数据播放)
 * 4. 播放广告时, 如果被抢焦点, 关闭所有广告, 然后等待焦点恢复, 继续播放当前碎片(上层可以设置一个焦点被抢的监听, 执行跳过广告, 之后的逻辑)
 * 5. 播放定时广告, 结束后, 要保持原来的播放状态(记录播放前的播放状态, 播放结束后, 恢复)
 **/
public class AdvertPlayerImpl implements AdvertisingPlayer {
    private static final String TAG = "AdPlayer:" + PlayerConstants.LOG_TAG;
    private static final int MILLISECONDS = 1000;
    private static final String SPRAND_SIKP_AD = "sprand_skip_ad";

    /**
     * 闪屏广告是否已经展示过了
     */
    private boolean isFlashAudioADShow = false;

    /**
     * 上一个广告类型.
     */
    private int mPreAdType = 0;
    private boolean isloadCrash = false;
    private AudioAdvert audioAdvert;

    public AdvertPlayerImpl() {
        initAdvertLifecycle();
    }

    @Override
    public void play(AudioAdvert audioAdvert) {
        YTLogUtil.logStart(TAG, "play", "调用广告曝光.........");

        //隐藏二次互动的Banner大图
        AdvertisingImagerImpl imager = (AdvertisingImagerImpl) AdvertisingManager.getInstance().getImager();
        imager.hideInteractionBannerView();

        if (audioAdvert != null) {
            int type = audioAdvert.getSubtype();
            KradioAdAudioManager.getInstance().setAudioAdvert(audioAdvert);
            PlayerManagerHelper.getInstance().setAudioAdvert(audioAdvert);

            exposeInteractionAdvert(audioAdvert);

            switch (type) {
                case AdConstant.TYPE_TIMED_ADVERT: {
                    Log.i(TAG, "定时广告");
                    if ((PlayInterceptFactory.getInstance().clockChainIntercept != null && PlayInterceptFactory.getInstance().clockChainIntercept.isPlay)
                            || PlayerManagerHelper.getInstance().invalidPlayAction()) {
                        Log.i(TAG, "定时广告, 获取到当前播放时间播报或已经失去音频焦点或者正在播放插播, 拦截不在播放");
                        AdvertisingManager.getInstance().close();
                        return;
                    }

                    int focusChange = PlayerManager.getInstance().getCurrentAudioFocusStatus();
                    if (focusChange == AUDIOFOCUS_LOSS || focusChange == AUDIOFOCUS_LOSS_TRANSIENT) {
                        if (!isAppOnForeground()) {
                            AdvertisingManager.getInstance().close();
                            return;
                        }
                    }
                    if (KradioAdAudioManager.getInstance().isNeedReplaceAudioAd(
                            String.valueOf(KradioAdSceneConstants.TIMER_SCENE),
                            KradioAdAudioManager.getInstance().getPlayAudioAd())) {
                        boolean isCrashPlaying = CrashPlayerHelper.getInstance().isPlay();
                        Log.i(TAG, "定时广告 --- isCrashPlaying=" + isCrashPlaying);
                        if (!isCrashPlaying) {//如果正在播放插播，等待插播播放完成在播放广告 插播占位
                            Log.i(TAG, "定时广告 --- startTo playTimeAd() isCrashPlaying=" + false);
                            playTimeAd(getPlayUrl(audioAdvert.getLocalPath(), audioAdvert.getUrl()));
                            showAudioAdView(audioAdvert.getJumpSeconds() * MILLISECONDS);
                        } else {
                            CrashPlayerHelper.getInstance().setIcrashstate(new Icrashstate() {
                                @Override
                                public void onCrashstate(int i) {
                                    Log.i(TAG, "定时广告 --- startTo playTimeAd() isCrashPlaying=" + true);
                                    playTimeAd(getPlayUrl(audioAdvert.getLocalPath(), audioAdvert.getUrl()));
                                    showAudioAdView(audioAdvert.getJumpSeconds() * MILLISECONDS);
                                }

                                @Override
                                public void onBufferingUpdate(CrashMessageBaseBean crashMessageBaseBean) {

                                }

                                @Override
                                public void onPlayerFailed(PlayerFailedType playerFailedType) {

                                }

                            });
                        }
                    }
                }

                break;
                case KradioAdSceneConstants.SUB_TYPE_SWITCH_PROGROM: {
                    Log.i(TAG, "节目切换");
                    if (CrashPlayerHelper.getInstance().isPlay()) {
                        Log.i(TAG, "节目切换, 获取到当前正在播放插播, 拦截不在播放");
                        AdvertisingManager.getInstance().close();
                        return;
                    }
                    if (KradioAdAudioManager.getInstance().isNeedReplaceAudioAd(
                            String.valueOf(KradioAdSceneConstants.TIMER_SCENE),
                            KradioAdAudioManager.getInstance().getPlayAudioAd())) {
                        if (!CrashPlayerHelper.getInstance().isPlay()) {//如果正在播放插播，等待插播播放完成在播放广告  插播占位
                            Log.i(TAG, "is_need " + "isjump:" + audioAdvert.isJump() + audioAdvert.getJumpSeconds());
                            playSwitchProgramAd(getPlayUrl(audioAdvert.getLocalPath(), audioAdvert.getUrl()));
                            showAudioAdView(audioAdvert.getJumpSeconds() * MILLISECONDS);
                        } else {
                            CrashPlayerHelper.getInstance().setIcrashstate(new Icrashstate() {
                                @Override
                                public void onCrashstate(int i) {
                                    Log.i(TAG, "is_need " + "isjump:" + audioAdvert.isJump() + audioAdvert.getJumpSeconds());
                                    playSwitchProgramAd(getPlayUrl(audioAdvert.getLocalPath(), audioAdvert.getUrl()));
                                    showAudioAdView(audioAdvert.getJumpSeconds() * MILLISECONDS);
                                }

                                @Override
                                public void onBufferingUpdate(CrashMessageBaseBean crashMessageBaseBean) {

                                }

                                @Override
                                public void onPlayerFailed(PlayerFailedType playerFailedType) {

                                }
                            });

                        }
                    }
                }
                break;
                case KradioAdSceneConstants.SUB_TYPE_SPRAND_SCREEN:
                    if (StringUtil.isNotEmpty(audioAdvert.getLocalPath())) {
                        Log.i(TAG, "闪屏广告 路径:" + audioAdvert.getLocalPath());
                        playFlashAd(getPlayUrl(audioAdvert.getLocalPath(), audioAdvert.getUrl()));
                        showAudioAdView(audioAdvert.getJumpSeconds() * MILLISECONDS);
                    } else {
                        Log.i(TAG, "闪屏广告 数据为空");
                        notifyFlashScreenAdPlayEnd();
                    }
                    break;
                case KradioAdSceneConstants.AD_TYPE_SWITCH_RADIO_AUDIO: {
                    Log.i(TAG, "排位音频广告: 是否允许跳过" + audioAdvert.isJump());
                    playSwitchRadioAudio(getPlayUrl(audioAdvert.getLocalPath(), audioAdvert.getUrl()));
                    showAudioAdView(audioAdvert.getJumpSeconds() * MILLISECONDS);
                }
                break;
                default:
                    break;
            }
            mPreAdType = type;
        }
    }

    private boolean isAppOnForeground() {
        IntentUtils intentUtils = IntentUtils.getInstance();
        return intentUtils.isAppOnForeground();
    }

    private String getPlayUrl(String localPath, String url) {
        if (StringUtil.isEmpty(localPath)) {
            return url;
        }
        File adFile = new File(localPath);
        if (adFile.exists()) {
            return localPath;
        }

        return url;
    }

    @Override
    public void stop(AudioAdvert audioAdvert) {
        Log.i(TAG, "调用 stop");
        AudioAdvert tempAudioAdvert = audioAdvert;
        if (tempAudioAdvert != null) {
            PlayerManagerHelper.getInstance().removelockPlayerForAudioAd();
            AdConflict.removeAdvert(tempAudioAdvert);
        }
        if (tempAudioAdvert == null) {
            tempAudioAdvert = KradioAdAudioManager.getInstance().getAudioAdvert();
        }
        if (tempAudioAdvert == null) {
            return;
        }
        KradioAdAudioManager.getInstance().cancel();

        if (PlayerManagerHelper.getInstance().isPlayingAd()) {
            PlayItem playItem = PlayerManager.getInstance().getCurrentTempTaskPlayItem();
            if (playItem instanceof AdPlayItem) {
                ((AdPlayItem) playItem).setNeedNextInnerAction(true);
            }

            if (tempAudioAdvert.getSubtype() == KradioAdSceneConstants.SUB_TYPE_SPRAND_SCREEN) {
                playItem.addMapCacheData("sprand_skip_ad", "true");
            }
            PlayerManagerHelper.getInstance().stopAudioAd(!PlayerManagerHelper.getInstance().getCurPlayItem().isLiving());
        }
    }

    @Override
    public void pause(AudioAdvert audioAdvert) {
//      音频广告播放暂停
        Log.i(TAG, "....... pause");
        PlayerManager.getInstance().pause();
    }


    @Override
    public void error(String adZoneId, int subtype, ApiException e) {
        Log.e(TAG, "广告曝光 error" + subtype);
        notifyPlayEnd(subtype);
    }


    public void exposeInteractionAdvert(AudioAdvert audioAdvert) {
        if (audioAdvert != null && audioAdvert.getInteractionAdvert() != null) {
            int exposeInteractionOpportunity = audioAdvert.getInteractionAdvert().getOpportunity();
//            * 展示时机。1，广告开始曝光时；2，广告曝光结束；
            if (audioAdvert.getType() == 3 && exposeInteractionOpportunity == 1 && audioAdvert.getSubtype() != KradioAdSceneConstants.SUB_TYPE_SPRAND_SCREEN) {
                AdvertisingManager.getInstance().expose(audioAdvert.getInteractionAdvert());
            }
        }
    }

    public void showAudioAdView(long duration) {
        KradioAdAudioManager.getInstance().showAudioAdView(duration);
    }

    private AdPlayItem makeAdPlayItem(String url, int type) {
        AdPlayItem adPlayItem = new AdPlayItem();
        adPlayItem.setPlayUrl(url);
        adPlayItem.setAdType(type);
        adPlayItem.setPlayerIsPlaying(PlayerManagerHelper.getInstance().isPlaying());
        adPlayItem.setNeedPlayStateCallBack(true);
        adPlayItem.setNeedNextInnerAction(true);
        return adPlayItem;
    }

    /**
     * 播放定时广告, 需要差别对待, 如果之前播放的是其他音频广告, 那么结束后, 要处理其他的音频广告播放完成的逻辑
     */
    private void playTimeAd(String url) {
        Log.i(TAG, "播放 定时广告: " + url);

        AdvertisingManager.getInstance().getReporter().play(KradioAdAudioManager.getInstance().getAudioAdvert());
        PlayerManagerHelper.getInstance().lockPlayerForAudioAd();


        AdPlayItem adPlayItem = makeAdPlayItem(url, AdConstant.TYPE_TIMED_ADVERT);
        PlayItem tempTaskPlayItem = PlayerManager.getInstance().getCurrentTempTaskPlayItem();

        int status = PlayerManager.getInstance().getPlayStatus();
        boolean isPlaying = status != PlayerConstants.TYPE_PLAYER_PAUSED;

        if (mPreAdType != AdConstant.TYPE_TIMED_ADVERT) {
            adPlayItem.setPlayerIsPlaying(isPlaying);
        }

        if (tempTaskPlayItem instanceof TempTaskPlayItem) {
            if (((TempTaskPlayItem) tempTaskPlayItem).getTempTaskType() == PlayerConstants.TEMP_TASK_TYPE_CLOCK) {
                adPlayItem.setPlayerIsPlaying(true);
            } else {
                adPlayItem.setPlayerIsPlaying(((TempTaskPlayItem) tempTaskPlayItem).getPlayerIsPlaying());
            }
        } else {
            PlayerManager.getInstance().pause();
        }
        Log.i(TAG, "播放 定时广告: 之前是正在播放吗: " + adPlayItem.getPlayerIsPlaying());

        adPlayItem.setPlayStateListener(new BasePlayStateListener() {
            @Override
            public void onPlayerEnd(PlayItem playItem) {
                super.onPlayerEnd(playItem);
                Log.i(TAG, "onPlayerEnd 播放 定时广告完成");
                playAdEnd(true, playItem);
                if (adPlayItem.getPlayerIsPlaying()) {
                    if (PlayerManagerHelper.getInstance().getCurPlayItem() instanceof VideoAlbumPlayItem) {
                        PlayerManager.getInstance().justStopTempTask(0);
                    }
                    PlayerManager.getInstance().switchPlayerStatus();
                }
            }
        });

        PlayerManager.getInstance().startTempTask(adPlayItem);
    }


    /**
     * 播放闪屏广告
     *
     * @param url
     */
    private void playFlashAd(String url) {
        Log.i(TAG, "播放 开屏广告");

        AdvertisingManager.getInstance().getReporter().play(KradioAdAudioManager.getInstance().getAudioAdvert());
        PlayerManagerHelper.getInstance().lockPlayerForAudioAd();

        AdPlayItem adPlayItem = makeAdPlayItem(url, KradioAdSceneConstants.SUB_TYPE_SPRAND_SCREEN);
        adPlayItem.setPlayStateListener(new BasePlayStateListener() {
            @Override
            public void onPlayerEnd(PlayItem playItem) {
                super.onPlayerEnd(playItem);
                Log.i(TAG, "播放 闪屏广告完成");
                playAdEnd(true, playItem);
                notifyFlashScreenAdPlayEnd();
            }
        });

        PlayerManager playerManager = PlayerManager.getInstance();
        if (playerManager.isPlayerInitSuccess()) {
            playerManager.pause();
            playerManager.startTempTask(adPlayItem);
        } else {
            playerManager.addPlayerInitComplete(b -> {
                playerManager.pause();
                playerManager.startTempTask(adPlayItem);
            });
        }
    }


    /**
     * 播放节目切换广告
     */
    private void playSwitchProgramAd(String url) {
        Log.i(TAG, "播放 节目切换广告");
        AdvertisingManager.getInstance().getReporter().play(KradioAdAudioManager.getInstance().getAudioAdvert());
        PlayerManagerHelper.getInstance().lockPlayerForAudioAd();

        AdPlayItem adPlayItem = makeAdPlayItem(url, KradioAdSceneConstants.SUB_TYPE_SPRAND_SCREEN);
        adPlayItem.setNeedNextInnerAction(false);
        adPlayItem.setPlayerIsPlaying(true);
        adPlayItem.setPlayStateListener(new BasePlayStateListener() {
            @Override
            public void onPlayerEnd(PlayItem playItem) {
                super.onPlayerEnd(playItem);
                Log.i(TAG, "播放 切换节目广告完成");
                playAdEnd(true, playItem);
                notifySwitchAudioAdEnd();
            }
        });
        PlayerManager.getInstance().startTempTask(adPlayItem);
        //FIXME SDK注释掉了，后面可能再开
//        if (PlayerManager.getInstance().isAsyncStartExecuting()) {
//            PlayerManager.getInstance().setPlayerAsncStartExecutingListener(new IPlayerAsncStartExecutingListener() {
//                @Override
//                public void onAsyncStartExecuteFinished() {
//                    PlayerManager.getInstance().setPlayerAsncStartExecutingListener(null);
//                    PlayerManager.getInstance().startTempTask(adPlayItem);
//                }
//            });
//        } else {
//            PlayerManager.getInstance().startTempTask(adPlayItem);
//        }
    }

    /**
     * 播放编排位广告
     *
     * @param url
     */
    private void playSwitchRadioAudio(String url) {
        Log.i(TAG, "播放 切换编排位广告 " + ", url = " + url);
        if (StringUtil.isEmpty(url)) {
            notifySwitchAudioAdEnd();
            return;
        }
        AdvertisingManager.getInstance().getReporter().play(KradioAdAudioManager.getInstance().getAudioAdvert());
        PlayerManagerHelper.getInstance().lockPlayerForAudioAd();
        AdPlayItem adPlayItem = makeAdPlayItem(url, KradioAdSceneConstants.AD_TYPE_SWITCH_RADIO_AUDIO);
        adPlayItem.setNeedNextInnerAction(false);
        adPlayItem.setPlayerIsPlaying(true);
        adPlayItem.setPlayStateListener(new BasePlayStateListener() {
            @Override
            public void onPlayerEnd(PlayItem playItem) {
                super.onPlayerEnd(playItem);
                Log.i(TAG, "播放 编排位广告完成");
                playAdEnd(true, playItem);
                notifySwitchAudioAdEnd();
            }
        });
        PlayerManager.getInstance().startTempTask(adPlayItem);
    }

    private void playAdEnd(boolean isRealPlayEnd, PlayItem playItem) {
        String v = playItem.getMapCacheData(SPRAND_SIKP_AD);
        if (TextUtils.isEmpty(v)) {
            AdvertisingManager.getInstance().getReporter().endPlay(KradioAdAudioManager.getInstance().audioAdvert, playItem.getPosition());
        }
        PlayerManagerHelper.getInstance().removelockPlayerForAudioAd();
        //这个地方引起播放完成循环调用
        //PlayerManagerHelper.getInstance().audioAdPlayOver(playItem.getPosition());
        PlayerManagerHelper.getInstance().playAudioAdOver(true);
        KradioAdAudioManager.getInstance().cancel();
        if (isRealPlayEnd) {
            showSecondInteraction();
        }
    }

    private void notifyPlayEnd(int type) {
        switch (type) {
            case KradioAdSceneConstants.SUB_TYPE_SWITCH_PROGROM:
            case KradioAdSceneConstants.AD_TYPE_SWITCH_RADIO_AUDIO:

                notifySwitchAudioAdEnd();
                break;
            case KradioAdSceneConstants.SUB_TYPE_SPRAND_SCREEN: {
                notifyFlashScreenAdPlayEnd();
            }
            break;
            default:
                break;
        }
    }


    public boolean getFlashAudioADShow() {
        return isFlashAudioADShow;
    }

    /**
     * 展示后置二次互动
     */
    private void showSecondInteraction() {
        AudioAdvert audioAdvert = KradioAdAudioManager.getInstance().getAudioAdvert();
        Log.i(TAG, "showSecondInteraction audioAdvert= " + audioAdvert);
        if (audioAdvert != null && audioAdvert.getInteractionAdvert() != null
                && audioAdvert.getInteractionAdvert().getOpportunity() == KradioAdSceneConstants.INTERACT_AD_OPPORTUNITY_AFTER) {
            UIThreadUtil.runUIThread(() -> AdvertisingManager.getInstance().expose(audioAdvert.getInteractionAdvert()));
        }
    }

    /**
     * 闪屏广告处理完成callback
     */
    private IFlashScreenAdPlayerListener mFlashScreenAdPlayerListener;

    public void setListener(IFlashScreenAdPlayerListener iFlashScreenAdPlayerListener) {
        iFlashScreenAdPlayerListener.onPlayEnd();
        if (isFlashAudioADShow) {
            return;
        }
        mFlashScreenAdPlayerListener = iFlashScreenAdPlayerListener;
    }

    /**
     * 节目广告处理完成callback
     */
    private AdPlayChainIntercept.IPlayAdEndCallback mPlayAdEndCallback;

    public void setPlayAdEndCallback(AdPlayChainIntercept.IPlayAdEndCallback iPlayAdEndCallback) {
        mPlayAdEndCallback = iPlayAdEndCallback;
    }

    /**
     * 通知节目切换广告播放完成
     */
    private void notifySwitchAudioAdEnd() {
        AdConflict.removeAdvert(KradioAdAudioManager.getInstance().getAudioAdvert());
        if (mPlayAdEndCallback != null) {
            mPlayAdEndCallback.playEnd();
        }
    }

    /**
     * 通知闪屏广告播放完成
     */
    private void notifyFlashScreenAdPlayEnd() {
        if (isFlashAudioADShow) {
            return;
        }
        AdConflict.removeAdvert(KradioAdAudioManager.getInstance().getAudioAdvert());
        isFlashAudioADShow = true;

        if (mFlashScreenAdPlayerListener != null) {
            mFlashScreenAdPlayerListener.onPlayEnd();
        }
    }

    /**
     * 注册广告生命周期监听
     */
    private void initAdvertLifecycle() {
        AdvertisingManager.getInstance().registerAdvertLifecycleCallback(new AdvertisingLifecycleCallback() {
            @Override
            public void onCreate(String s, int i) {
            }

            @Override
            public void onStart(Advert advert) {
                if (advert == null) {
                    return;
                }
                Nolley.closeAllToast();
                /**
                 * 解决 #40193
                 */
                if (!PlayerManagerHelper.getInstance().isPlayingAd()) {
                    TTSSpeaker.getInstance().stop();
//                    PlayItem playItem = PlayerManager.getInstance().getCurrentTempTaskPlayItem();
//                    if (playItem != null && playItem instanceof TempTaskPlayItem) {
//                        PlayerManager.getInstance().stopTempTask();
//                    }
                }
                int type = advert.getSubtype();
                Log.i(TAG, " 广告 onStart type = " + type + " 所属类型: " + advert.getClass().toString());
                if (advert instanceof ImageAdvert) {
                    switch (type) {
                        case KradioAdSceneConstants.SUB_TYPE_SPRAND_SCREEN: {
                            notifyFlashScreenAdPlayEnd();
                        }
                        break;
                        case KradioAdSceneConstants.SUB_TYPE_SWITCH_PROGROM:
                        case KradioAdSceneConstants.AD_TYPE_SWITCH_RADIO_AUDIO: {
                            notifySwitchAudioAdEnd();
                        }
                        break;
                        default:
                            break;
                    }
                }
            }

            @Override
            public void onClose(Advert advert) {
            }

            @Override
            public void onError(String s, int i, Exception e) {

            }
        });
    }
}
