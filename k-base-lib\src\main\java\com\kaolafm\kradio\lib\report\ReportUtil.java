package com.kaolafm.kradio.lib.report;

import org.jetbrains.annotations.NotNull;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-11-01
 */
public final class ReportUtil {
    private static Reporter mReporter;


    public static void report(Reporter reporter) {
        mReporter = reporter;
        initPlayerListener();
    }

    /**
     * 初始化播放器lintener;
     */
    public static void initPlayerListener() {
        mReporter.initPlayerListener();
    }

    public static void reportStartPlay(Object bean) {
        mReporter.reportStartPlay(bean);
    }

    public static void reportLivingPlay(Object bean) {
        mReporter.reportLivingPlay(bean);
    }

    public static void reportStartOtherPlay(Object bean) {
        mReporter.reportStartOtherPlay(bean);
    }


    /**
     * 上报播放结束事件
     */
    public static void reportEndPlay(String reason) {
        mReporter.reportEndPlay(reason);
    }

    /**
     * 上报播放结束事件
     */
    public static void reportEndPlay(String reason, boolean isNeedReport) {
        mReporter.reportEndPlay(reason, isNeedReport);
    }

    /**
     * 上报卡顿开始
     *
     * @param bean
     * @param isSeek
     */
    public static void reportBufferingStart(Object bean, boolean isSeek) {
        mReporter.reportBufferingStart(bean, isSeek);
    }

    /**
     * 上报卡顿结束
     *
     * @param bean
     * @param isSeek
     * @param bufferStartTime 卡顿开始时间
     */
    public static void reportBufferingEnd(Object bean, boolean isSeek, long bufferStartTime) {
        mReporter.reportBufferingEnd(bean, isSeek, bufferStartTime);
    }


    /**
     * 上报请求错误
     *
     * @param bean
     * @param what
     * @param extra
     */
    public static void reportRequestError(Object bean, int what, int extra) {
        mReporter.reportRequestError(bean, what, extra);
    }

    /**
     * 设置页面id
     *
     * @param pageId
     */
    public static void setPageId(String pageId) {
        mReporter.setPageId(pageId);
    }


    /**
     * 搜索结果 播放
     *
     * @param playType
     * @param callback
     */
    public static void reportSearchToPlay(String playType, String callback) {
        mReporter.reportSearchToPlay(playType, callback);
    }

    /**
     * @param way      选择方式	1.语音选择；2.手动选择；3.未知
     * @param radioId  radioId，碎片则为专辑id，ai电台为电台id，专辑为专辑id
     * @param id       内容id 对应选择的结果内容id：碎片id，专辑id、ai电台id、广播id（至少一个）
     * @param position 搜索结果索引号 被选择的结果索引号：1，2，3，4
     * @param result   搜索结果追踪号	搜索服务端透传的数据
     */
    public static void reportSearchSelectResult(String way, String radioId, String id, String position, String result) {
        mReporter.reportSearchSelectResult(way, radioId, id, position, result);
    }

    /**
     * @param requestAgent 用户原始文本，比如 ‘我要听刘德华的冰雨
     * @param type         1.手动输入；2.点击历史记录；3.点击联想词；4.语音搜索
     * @param result       0：超时；1：参数有误；2：无结果；3：正常
     * @param playType     0:否，1：是
     * @param keyword      语义理解的结果，比如‘刘德华，冰雨’
     */
    public static void reportSearchResult(String requestAgent, String type, String result, String playType, String keyword, String contentType) {
        mReporter.reportSearchResult(requestAgent, type, result, playType, keyword, contentType);
    }


    /**
     * 音质选择
     *
     * @param type
     */
    public static void addToneSelectEvent(int type) {
        mReporter.addToneSelectEvent(type);
    }

    /**
     * 消息语音播报开关
     *
     * @param type
     */
    public static void addSettingVoiceSwiych(int type) {
        mReporter.addSettingVoiceSwiych(type);
    }

    /**
     * 消息泡泡曝光
     */
    public static void addMessageShow(String pageId, String radiotype, String remarks2) {
        mReporter.addMessageShow(pageId, radiotype, remarks2);
    }

    /**
     * 消息泡泡点击曝光
     */
    public static void addMessageClike(String pageId, String radiotype, String remarks2) {
        mReporter.addMessageClike(pageId, radiotype, remarks2);
    }

    /**
     * 负反馈
     *
     * @param audioId
     * @param radioId
     * @param albumId
     * @param callback
     * @param position
     */
    public static void addMinusFeedbackEvent(String audioId, String radioId, String albumId, String callback, String position) {
        mReporter.addMinusFeedbackEvent(audioId, radioId, albumId, callback, position);
    }

    /**
     * 正反馈
     *
     * @param audioId
     * @param radioId
     * @param albumId
     * @param callback
     */
    public static void addPlusFeedbackEvent(String audioId, String radioId, String albumId, String callback) {
        mReporter.addPlusFeedbackEvent(audioId, radioId, albumId, callback);
    }

    /**
     * 登录
     *
     * @param type     登录类型, 1 扫码  2 手机号登录
     * @param remarks1 122113.我的——个人中心——进入登录流程
     *                 122112.已购页面——点击立即登录——进入登录流程
     *                 122111.收听历史——点击立即登录——进入登录流程
     *                 122110.我的订阅——点击立即登录——进入登录流程
     *                 141200.试听碎片轮播至需付费碎片——判断登录
     *                 141201.播放详情页专辑封面下方点击“VIP会员 免费听”——判断登录
     *                 141202.播放详情页内点击需付费碎片——判断登录
     */
    public static void addLoginEvent(String type, String remarks1) {
        mReporter.addLoginEvent(type, remarks1);
    }

    /**
     * 直播留言
     *
     * @param id
     * @param liveId
     * @param compereId
     * @param radioId
     */
    public static void addLivingLeaveMessageEvent(String id, String liveId, String compereId, String radioId) {
        mReporter.addLivingLeaveMessageEvent(id, liveId, compereId, radioId);
    }

    /**
     * 订阅与取消订阅
     *
     * @param type          0：取消订阅；1：订阅
     * @param subscribeType 1：专辑；2：电台；3：广播；4：音乐电台（第三方内容源）
     * @param position      1：播放条；2：全屏播放器；3：widget
     * @param radioId       专辑取专辑id，电台取电台id，广播取广播id，音乐取音乐电台id
     */
    public static void addSubscribeEvent(String type, String subscribeType, String position, String radioId) {
        mReporter.addSubscribeEvent(type, subscribeType, position, radioId);
    }


    /**
     * 播放条控制
     *
     * @param type        1：播放后暂停；2：暂停后播放；3：上一首；4：下一首；5：列表
     * @param controlType 1：点击；2：语音；3：方控；4：其他
     * @param position    1：播放条；2：全屏播放器；3：widget
     */
    public static void addPlayerUiControlEvent(String type, String controlType, String position) {
        mReporter.addPlayerUiControlEvent(type, controlType, position);
    }

    /**
     * 推荐展示
     */
    public static void addRecommendShowEvent(List<?> beanList) {
        mReporter.addRecommendShowEvent(beanList);
    }

    /**
     * 播放列表推荐展示
     */
    public static void addPlayListRecommendShowEvent(int type, String callBack) {
        mReporter.addPlayListRecommendShowEvent(type, callBack);
    }

    /**
     * 推荐点击
     *
     * @param type
     * @param callBack 推荐服务端透传的数据
     */
    public static void addRecommendSelectEvent(String type, String callBack) {
        mReporter.addRecommendSelectEvent(type, callBack);
    }

    /**
     * 添加激活结果上报.
     */
    public static void tingBanToKRadioUpdate() {
        mReporter.tingBanToKRadioUpdate();
    }

    public static void addContentClickEvent(String audioId, String radioType,
                                            String audioType, String radioId, String tag,
                                            String pageId, String remarks1, String remarks2) {
        mReporter.addContentClickEvent(audioId, radioType, audioType, radioId, tag,
                pageId, remarks1, remarks2);
    }

    public static void addComponentShowAndClickEvent(String audioId, boolean mold,
                                                     String cardid, int action,
                                                     String column_code, String memberorder,
                                                     String placeorder, String radioId,
                                                     String tag, String pageId,
                                                     String paytype) {
        mReporter.addComponentShowAndClickEvent(audioId, mold, cardid, action, column_code,
                memberorder, placeorder, radioId, tag, pageId, paytype);
    }

    public static void addPageShowEvent(long startTime, String pageId) {
        mReporter.addPageShowEvent(startTime, pageId);
    }

    public static void addContentShowEvent(String audioId, String radioType,
                                           String audioType, String radioId, String tag,
                                           String pageId, String remarks1, String remarks2) {
        mReporter.addContentShowEvent(audioId, radioType, audioType, radioId, tag,
                pageId, remarks1, remarks2);
    }
}
