package com.kaolafm.kradio.lib.exit;

import androidx.annotation.NonNull;

import com.kaolafm.kradio.lib.init.Process;

/**
 * 初始化任务。内部逻辑处理使用，外部不需要关心
 * <AUTHOR>
 * @date 2019-09-10
 */
public class AppExitTask implements Comparable<AppExitTask> {

    public int priority;

    public int process;

    public String description;

    public boolean isAsync;

    public AppExitreatment exitreatment;

    public AppExitTask(int priority, int process, String description, boolean isAsync, AppExitreatment exitreatment) {
        this.priority = priority;
        this.process = process;
        this.description = description;
        this.isAsync = isAsync;
        this.exitreatment = exitreatment;
    }

    @Override
    public int compareTo(@NonNull AppExitTask item) {
        return Integer.compare(this.priority, item.priority);
    }

    public boolean inAllProcess() {
        return process == Process.ALL;
    }

    public boolean inMainProcess() {
        return process == Process.MAIN || inAllProcess();
    }

    public boolean inOtherProcess() {
        return process == Process.OTHER || inAllProcess();
    }
}
