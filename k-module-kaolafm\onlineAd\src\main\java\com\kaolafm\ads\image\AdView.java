package com.kaolafm.ads.image;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.View;

import com.kaolafm.ad.api.model.ImageAdvert;
import com.kaolafm.ad.api.model.InteractionAdvert;
import com.kaolafm.ad.conflict.AdConflict;
import com.kaolafm.ad.control.KradioAdSceneConstants;
import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.ads.image.base.AdImageListener;
import com.kaolafm.ads.image.base.BaseAdImageAdapter;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.flavor.KRadioTransStatusBarInter;
import com.kaolafm.kradio.lib.toast.AdToast;
import com.kaolafm.kradio.lib.toast.SuperToast;
import com.kaolafm.kradio.lib.toast.ToastStyle;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;

public class AdView {
    private static final String TAG = "AdView";

    private BaseAdImageAdapter baseAdImageAdapter;

    private Context mContext;

    private SuperToast mAdToast;

    private ImageAdvert mAdvert;

    private int mYOffset;

    public AdView(Context context) {
        mContext = context;
        boolean isWindowFullScreen = context.getResources().getBoolean(R.bool.isWindowFullScreen);
        if (!isWindowFullScreen) {
            KRadioTransStatusBarInter kRadioTransStatusBarInter = ClazzImplUtil.getInter("KRadioTransStatusBarImpl");
            if (kRadioTransStatusBarInter != null) {
                mYOffset = kRadioTransStatusBarInter.getStatusBarHeight();
            }
        }
    }

    public void setContext(Context context) {
        mContext = context;
    }

    private AdImageListener mAdImageListener = new AdImageListener() {
        @Override
        public void onAdImageLoaded(View view) {
            reportDisplay();
        }

        @Override
        public void onAdImageLoadFailed() {
            mAdToast.dismiss();
        }

        @Override
        public void onAdImageCollapsed() {
            AdvertisingManager.getInstance().close(mAdvert);
            AdvertisingManager.getInstance().getReporter().interruptDisplay(mAdvert);
        }

        @Override
        public void onAdSkip() {
            Log.i("audio_ad_close_flow", "AdView->onAdSkip-->close");
            AdvertisingManager.getInstance().close(mAdvert);
            AdvertisingManager.getInstance().getReporter().skip(mAdvert);
        }

        @Override
        public void onAdComplete() {
            AdvertisingManager.getInstance().close(mAdvert);
            showAfterInteractView(mAdvert);
            if (mAdvert != null && mAdvert.getType() != KradioAdSceneConstants.AD_TYPE_AUDIO_IMAGE) {
                AdvertisingManager.getInstance().getReporter().endDisplay(mAdvert);
            }
        }
    };

    public void loadAd(ImageAdvert advert) {
        loadAd(advert, true);
    }

    public void loadAd(ImageAdvert advert, boolean isShowAdMainImage) {
        if (advert.getExposeDuration() > 0) {
            mAdvert = advert;
            String className = AdTypeTranslator.getAdImageTypeName(advert);
            try {
                baseAdImageAdapter = AdImageAdapterFactory.create(className);
            } catch (Exception e) {
                e.printStackTrace();
            }
            baseAdImageAdapter.init(mContext);
            AdSize adSize = SizeUtil.getAdSize(mContext, advert.getWidth(), advert.getHeight());
            int width = adSize.width;
            int height = adSize.height;
            if (mAdToast != null) {
                mAdToast.dismiss();
            }
            if (isShowAdMainImage) {
                //显示图片广告
                mAdToast = new AdToast(mContext)
                        .setCanTouch(true)
                        .setDisplayLevel(ToastStyle.LEVEL_ACTIVITY)
                        .setView(baseAdImageAdapter.getBaseAdContentView())
                        .setWidth(width)
                        .setGravity(Gravity.TOP)
                        .setXOffset(0)
                        .setYOffset(mYOffset + ResUtil.getDimen(R.dimen.y12))
                        .setRadius(ResUtil.getDimen(R.dimen.m16))
                        .setBackground(ResUtil.getColor(R.color.transparent_color))
                        .setDuration(-1)
                        .setHeight(height)
                        .create()
                        .show();
            }
            baseAdImageAdapter.loadAd(createAdContentInfo(), mAdImageListener);
            Log.i(TAG, "load Adview----->" + mAdvert);
            showBeforeInteractView(advert);
            return;
        }
        showBeforeInteractView(advert);
        //http://redmine.itings.cn/issues/40170  时长为0时，需要删除掉冲突里的advert
        AdConflict.removeAdvert(advert);
        showAfterInteractView(advert);
    }

    private AdContentInfo createAdContentInfo() {
        AdContentInfo adContentInfo = new AdContentInfo();
        adContentInfo.setImageDuration(mAdvert.getExposeDuration());
        adContentInfo.setSkipTime(mAdvert.getJumpSeconds());
        adContentInfo.setJump(mAdvert.isJump());
        if (!TextUtils.isEmpty(mAdvert.getLocalPath())) {
            adContentInfo.setLocalPath(mAdvert.getLocalPath());
            adContentInfo.setPreload(true);
        } else {
            adContentInfo.setImageUrl(mAdvert.getUrl());
            adContentInfo.setPreload(false);
        }
        return adContentInfo;
    }

    /**
     * 显示后置二次互动
     *
     * @param advert
     */
    private void showAfterInteractView(ImageAdvert advert) {
        InteractionAdvert interactionAdvert = advert.getInteractionAdvert();
        if (advert.getType() != KradioAdSceneConstants.AD_TYPE_AUDIO_IMAGE
                && interactionAdvert != null && interactionAdvert.getOpportunity() == KradioAdSceneConstants.INTERACT_AD_OPPORTUNITY_AFTER) {
            AdvertisingManager.getInstance().expose(interactionAdvert);
        }
    }

    private void showBeforeInteractView(ImageAdvert advert) {
        //曝光前置二次互动
        InteractionAdvert interactionAdvert = advert.getInteractionAdvert();
        if (interactionAdvert != null && interactionAdvert.getOpportunity() == KradioAdSceneConstants.INTERACT_AD_OPPORTUNITY_BEFORE) {
            AdvertisingManager.getInstance().expose(interactionAdvert);
        }
    }

    public void hide() {
        if (mAdToast != null) {
            Log.i(TAG, "hide Adview----->" + mAdvert);
            mAdToast.dismiss();
        }
    }

    public boolean isShow() {
        return mAdToast != null && mAdToast.isShowing();
    }

    public void destroy() {
        hide();
        if (mAdvert != null) {
            AdvertisingManager.getInstance().close(mAdvert);
        }
    }

    private void reportDisplay() {
        if (mAdvert != null) {
            //如果是音图广告或者当前是开屏的附图不上报（开屏附图的情况下 主图已上报display）
            if (mAdvert.getType() == KradioAdSceneConstants.AD_TYPE_AUDIO_IMAGE || mAdvert.getSubtype() == KradioAdSceneConstants.SUB_TYPE_SPRAND_SCREEN) {
                return;
            }
            AdvertisingManager.getInstance().getReporter().display(mAdvert);
        }
    }
}
