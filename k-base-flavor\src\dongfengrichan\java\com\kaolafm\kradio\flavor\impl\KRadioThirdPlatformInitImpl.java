package com.kaolafm.kradio.flavor.impl;

import android.content.Context;

import com.kaolafm.kradio.lib.base.flavor.KRadioThirdPlatformInitInter;

/**
 * <AUTHOR>
 **/
public class KRadioThirdPlatformInitImpl implements KRadioThirdPlatformInitInter {


    @Override
    public boolean initThirdPlatform(Object... args) {
        RiChanHelper.getInstance((Context) args[0]).init();
        return true;
    }

    @Override
    public boolean destroyThirdPlatform(Object... args) {
        return false;
    }
}
