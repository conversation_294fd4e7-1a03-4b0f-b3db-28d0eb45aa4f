package com.kaolafm.kradio.flavor.impl;

import android.os.Handler;
import android.os.IBinder;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.aptiv.sdssemanticmanager.SdsSemanticDispatcher;
import com.google.gson.Gson;
import com.kaolafm.kradio.clientControlerForKradio.ClientImpl;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.sdk.client.ErrorInfo;
import com.kaolafm.sdk.client.IPlayResult;

/**
 * <AUTHOR>
 *
 * <p>
 * OPEN	        	打开节目
 * </p>
 * <p>
 * CLOSE	        	关闭节目
 * </p>
 * <p>
 * PLAY	        	播放节目
 * </p>
 * <p>
 * DISLIKE	        	不喜欢
 * </p>
 * <p>
 * PAUSE	        	暂停
 * </p>
 * <p>
 * PAST	        	上一个
 * </p>
 * <p>
 * REPEAT	        	重听
 * </p>
 * <p>
 * NEXT	        	下一个
 * </p>
 * <p>
 * RANDOM	        	随机播放
 * </p>
 * <p>
 * CYCLE	        	单曲循环
 * </p>
 * <p>
 * ORDER	        	顺序播放
 * </p>
 * <p>
 * LOOP	        	循环播放
 * </p>
 * <p>
 * REVERSE	        	倒序播放
 * </p>
 * <p>
 * ORDER_TRANS		    换个顺序
 * </p>
 * <p>
 * SEARCH_PRESENTER	这是谁的节目
 * </p>
 * <p>
 * SEARCH_PROGRAM		节目名是什么
 * </p>
 * <p>
 * COLLECT		        收藏
 * </p>
 * <p>
 * CANCEL_COLLECT		取消收藏
 * </p>
 * <p>
 * LIKE		        喜欢
 * </p>
 * <p>
 * CANCEL_BOOK		    取消订阅
 * </p>
 * <p>
 * RECOGNIZE_SONG		听歌识曲
 * </p>
 * <p>
 * RECOGNIZE_SINGER	这首歌是什么歌
 * </p>
 * <p>
 * CLOSE_LIST		    关闭播放列表
 * </p>
 * <p>
 * OPEN_LIST		    打开播放列表
 * </p>
 **/
abstract class Operation {
    private static final String TAG = "kradio.Oper";
    private static final Gson mGson = new Gson();
    protected final SpeechResponse.SemanticBean mSemanticBean;
    protected final ClientImpl mClient;

    @Override
    public String toString() {
        return "Operation{" +
                "mSemanticBean=" + mSemanticBean +
                '}';
    }

    public Operation(SpeechResponse.SemanticBean semanticBean) {
        mSemanticBean = semanticBean;
        mClient = new ClientImpl(AppDelegate.getInstance().getContext());
    }


    public static final Operation parse(String jsonString) {
        Log.i(TAG, "parse jsonString = " + jsonString);
        SpeechResponse response = mGson.fromJson(jsonString, SpeechResponse.class);
        SpeechResponse.SemanticBean semanticBean = response.getSemantic();
        String operation = /*TextUtils.isEmpty(semanticBean.operation) ? semanticBean.insType :*/ semanticBean.operation;
        if (operation == null) {
            return new EmptyOperation(semanticBean);
        }

        //处理operation，app根据operation，执行相关的操作
        switch (operation.toUpperCase()) {
            case "OPEN":
                return new OpenOperation(semanticBean);
            case "CLOSE":
                return new CloseOperation(semanticBean);
//            case "VOLUME_UNMUTE":
            case "PLAY":
                //KLAutoPlayerManager.getInstance().
                return new PlayOperation(semanticBean);
//            case "VOLUME_MUTE":
            case "PAUSE":
                return new PauseOperation(semanticBean);
            case "PAST":
                return new PrevOperation(semanticBean);
            case "NEXT":
                return new NextOperation(semanticBean);
            case "SEARCH_PRESENTER":
            case "SEARCH_PROGRAM":
            case "QUERY":
                return new SearchOperation(semanticBean);
            case "RANDOM":
            case "CYCLE":
            case "ORDER":
            case "LOOP":
            case "REVERSE":
            case "ORDER_TRANS":
            case "REPEAT":
            case "COLLECT":
            case "CANCEL_COLLECT":
            case "LIKE":
            case "DISLIKE":
            case "CANCEL_BOOK":
            case "CLOSE_LIST":
            case "OPEN_LIST":
            case "RECOGNIZE_SONG":
            case "RECOGNIZE_SINGER":
            default:
                return new EmptyOperation(semanticBean);
        }


    }

    public abstract void exe();


    /**
     * 空实现
     */
    static class EmptyOperation extends Operation {

        public EmptyOperation(SpeechResponse.SemanticBean semanticBean) {
            super(semanticBean);
        }

        @Override
        public void exe() {
            Log.i(TAG, "operation is null");
            // Do-Nothing
        }
    }

    /**
     * 打开
     */
    static class OpenOperation extends Operation {
        public OpenOperation(SpeechResponse.SemanticBean semanticBean) {
            super(semanticBean);
        }

        @Override
        public void exe() {
            Log.i(TAG, "operation is null");
            // Do-Nothing
            try {
                mClient.launchApp(true);
            } catch (RemoteException e) {
                Log.e(TAG, e.toString());
            }
        }
    }

    /**
     * 下一首
     */
    static class NextOperation extends Operation {
        public NextOperation(SpeechResponse.SemanticBean semanticBean) {
            super(semanticBean);
        }

        @Override
        public void exe() {
            try {
                mClient.playNext();
            } catch (RemoteException e) {
                Log.e(TAG, e.toString());
            }
        }
    }

    /**
     * 上一首
     */
    static class PrevOperation extends Operation {
        public PrevOperation(SpeechResponse.SemanticBean semanticBean) {
            super(semanticBean);
        }

        @Override
        public void exe() {
            try {
                mClient.playPre();
            } catch (RemoteException e) {
                Log.e(TAG, e.toString());
            }

        }
    }

    /**
     * 暂停
     */
    static class PauseOperation extends Operation {
        public PauseOperation(SpeechResponse.SemanticBean semanticBean) {
            super(semanticBean);
        }

        @Override
        public void exe() {
            try {
                mClient.pause();
            } catch (RemoteException e) {
                Log.e(TAG, e.toString());
            }
        }
    }

    /**
     * 播放
     */
    static class PlayOperation extends Operation {
        public PlayOperation(SpeechResponse.SemanticBean semanticBean) {
            super(semanticBean);
        }

        @Override
        public void exe() {
            try {
                String presenter = mSemanticBean.presenter;
                String program = mSemanticBean.program;
                String keyword = program;
                if (TextUtils.isEmpty(keyword)) {
                    keyword = presenter;
                }
                if (TextUtils.isEmpty(keyword)) {
                    //如果presenter与program都为空,则调用播放
                    Thread.sleep(500);
                    mClient.play();
                } else {
                    //如果presenter或program不为空,则调用搜索播放
                    mClient.playByKeywords(keyword, new IPlayResult() {
                        @Override
                        public void onSuccuss() throws RemoteException {
                            Log.i(TAG, this + "\nonSuccuss");
                        }

                        @Override
                        public void onFailure(ErrorInfo errorInfo) throws RemoteException {
                            Log.i(TAG, this + "\nonFailure:errorInfo=" + errorInfo);
                        }

                        @Override
                        public IBinder asBinder() {
                            return null;
                        }
                    });
                }
            } catch (Exception e) {
                Log.e(TAG, e.toString());
            }

        }
    }

    /**
     * 关闭
     */
    static class CloseOperation extends Operation {
        public CloseOperation(SpeechResponse.SemanticBean semanticBean) {
            super(semanticBean);
        }

        @Override
        public void exe() {
            try {
                Log.i(TAG, "When CloseOperation, SdsSemanticDispatcher.reportMediaStatus with : activeStatus [" + SdsSemanticDispatcher.SEMANTIC_CLIENT_ACTIVE_STATUS_BACKGROUND + "], playbackStatus [" + SdsSemanticDispatcher.SEMANTIC_CLIENT_PLAYBACK_STATUS_PAUSED + "].");
                SdsSemanticDispatcher.getInstance()
                        .reportMediaStatus(SdsSemanticDispatcher.SEMANTIC_CLIENT_TYPE_ONLINE_RADIO, SdsSemanticDispatcher.SEMANTIC_CLIENT_ACTIVE_STATUS_BACKGROUND, SdsSemanticDispatcher.SEMANTIC_CLIENT_PLAYBACK_STATUS_PAUSED);
                mClient.exitApp();
            } catch (RemoteException e) {
                Log.e(TAG, e.toString());
            }
        }
    }

    /**
     * 搜索播放
     */
    private static class SearchOperation extends Operation {

        public SearchOperation(SpeechResponse.SemanticBean semanticBean) {
            super(semanticBean);
        }

        @Override
        public void exe() {
            try {
                String presenter = mSemanticBean.presenter;
                String program = mSemanticBean.program;
                String keyword = program;
                if (TextUtils.isEmpty(program)) {
                    keyword = presenter;
                }
                mClient.playByKeywords(keyword, new IPlayResult() {
                    @Override
                    public void onSuccuss() throws RemoteException {
                        Log.i(TAG, this + "\nonSuccuss");
                    }

                    @Override
                    public void onFailure(ErrorInfo errorInfo) throws RemoteException {
                        Log.i(TAG, this + "\nonFailure:errorInfo=" + errorInfo);
                    }

                    @Override
                    public IBinder asBinder() {
                        return null;
                    }
                });
            } catch (RemoteException e) {
                Log.e(TAG, e.toString());
            }
        }
    }
}
