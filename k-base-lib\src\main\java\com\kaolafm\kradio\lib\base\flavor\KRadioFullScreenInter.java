package com.kaolafm.kradio.lib.base.flavor;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-04-25 16:12
 ******************************************/
public interface KRadioFullScreenInter {
    boolean initFullScreen(Object... args);

    /**
     * 在Activity onResume时隐藏底部导航栏
     *
     * @param args
     * @return true已处理， false未处理
     */
    boolean hideNavBarOnResume(Object... args);
}
