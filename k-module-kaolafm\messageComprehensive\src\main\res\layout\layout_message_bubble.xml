<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:contentDescription="@string/content_desc_message_popup_go_detail"
    android:background="@color/message_details_bg">

    <ImageView
        android:id="@+id/card_bg_iv"
        android:background="@drawable/msg_dialog_bg"
        android:layout_width="@dimen/m720"
        android:layout_height="@dimen/m340"
        android:layout_marginStart="@dimen/m80"
        android:layout_marginBottom="@dimen/m136"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <com.kaolafm.kradio.component.ui.base.view.OvalImageView
        android:id="@+id/dialog_right_half_bg"
        android:layout_width="@dimen/m340"
        android:layout_height="@dimen/m340"
        android:scaleType="centerCrop"
        app:oval_radius="@dimen/m16"
        app:layout_constraintBottom_toBottomOf="@id/card_bg_iv"
        app:layout_constraintEnd_toEndOf="@id/card_bg_iv"
        app:layout_constraintTop_toTopOf="@id/card_bg_iv" />
    <com.kaolafm.kradio.component.ui.base.view.OvalImageView
        android:id="@+id/dialog_right_half_bg_mask"
        android:src="@drawable/dialog_right_half_bg_mask"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:scaleType="centerCrop"
        app:rid_type="5"
        app:oval_radius="@dimen/m16"
        app:layout_constraintBottom_toBottomOf="@id/dialog_right_half_bg"
        app:layout_constraintEnd_toEndOf="@id/dialog_right_half_bg"
        app:layout_constraintStart_toStartOf="@id/dialog_right_half_bg"
        app:layout_constraintTop_toTopOf="@id/dialog_right_half_bg" />

    <LinearLayout
        android:id="@+id/bubbleTitle_ll"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/m40"
        android:layout_marginTop="@dimen/m30"
        android:layout_marginEnd="@dimen/m40"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="@+id/card_bg_iv"
        app:layout_constraintStart_toStartOf="@id/card_bg_iv"
        app:layout_constraintTop_toTopOf="@id/card_bg_iv">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/m20"
            android:gravity="top">

            <ImageView
                android:id="@+id/bubbleIcon"
                android:layout_width="@dimen/m48"
                android:layout_height="@dimen/m48"
                android:scaleType="fitXY"
                tools:src="@drawable/message_green_icon" />

            <com.kaolafm.kradio.component.ui.base.view.KradioTextView
                android:id="@+id/bubbleTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/m20"
                android:maxLines="2"
                android:ellipsize="end"
                android:textColor="@color/text_color_7"
                android:textSize="@dimen/m28"
                app:kt_font_weight="0.3"
                tools:text="路况交通路况交通路况交通路况交通" />
        </LinearLayout>

        <ImageView
            android:id="@+id/msg_tips_pic_iv"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/m28"
            android:adjustViewBounds="true"
            android:layout_marginBottom="@dimen/m10"
            android:visibility="gone"
            tools:src="@color/blue01"
            tools:visibility="visible" />
    </LinearLayout>

    <com.kaolafm.kradio.component.ui.base.view.KradioTextView
        android:id="@+id/bubbleSubTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/m40"
        android:layout_marginEnd="@dimen/m40"
        android:lineSpacingExtra="@dimen/m6"
        android:maxLines="1"
        android:textColor="@color/msg_desc_text_color"
        android:textSize="@dimen/m24"
        android:ellipsize="end"
        app:layout_constraintEnd_toEndOf="@id/card_bg_iv"
        app:layout_constraintStart_toStartOf="@id/card_bg_iv"
        app:layout_constraintTop_toBottomOf="@id/bubbleTitle_ll"
        tools:text="前方三公里正在进行道路施工n车辆行驶缓慢n车辆行驶缓慢n车辆行驶缓慢车辆行驶缓慢！" />

    <LinearLayout
        android:id="@+id/bubbleButtonParent"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/m40"
        android:layout_marginEnd="@dimen/m40"
        android:layout_marginBottom="@dimen/m20"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="@id/card_bg_iv"
        app:layout_constraintEnd_toEndOf="@id/card_bg_iv"
        app:layout_constraintStart_toStartOf="@id/card_bg_iv"
        tools:layout_height="20dp" >

        <LinearLayout
            android:id="@+id/col1"
            android:layout_height="wrap_content"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center" >
            <TextView
                android:gravity="center"
                android:id="@+id/message_bubble_button_1_id"
                android:paddingTop="@dimen/m8"
                android:paddingBottom="@dimen/m8"
                android:contentDescription="@string/message_bubble_replay"
                android:background="@drawable/message_details_btn_bg"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/message_details_btn_text_color"
                android:textSize="@dimen/m22"
                android:text="查看详情" />
        </LinearLayout>

        <View
            android:id="@+id/col2"
            android:layout_width="@dimen/m28"
            android:layout_height="match_parent" />

        <LinearLayout
            android:id="@+id/col3"
            android:layout_height="wrap_content"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center" >
            <TextView
                android:id="@+id/bubble_replay"
                android:gravity="center"
                android:enabled="true"
                android:paddingTop="@dimen/m8"
                android:paddingBottom="@dimen/m8"
                android:contentDescription="@string/message_bubble_replay"
                android:background="@drawable/message_replay_btn_bg"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/message_replay_btn_text_color"
                android:textSize="@dimen/m22"
                android:text="@string/message_bubble_replay" />
        </LinearLayout>

        <View
            android:id="@+id/col4"
            android:layout_width="@dimen/m28"
            android:layout_height="match_parent" />

        <LinearLayout
            android:id="@+id/col5"
            android:layout_height="wrap_content"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center">
            <LinearLayout
                android:background="@drawable/message_details_btn_bg"
                android:paddingTop="@dimen/m8"
                android:paddingBottom="@dimen/m8"
                android:id="@+id/message_bubble_button_2_id"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center">

                <TextView
                    android:id="@+id/messageBubbleTextTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/message_details_btn_text_color"
                    android:textSize="@dimen/m22"
                    android:text="关闭" />

                <TextView
                    android:id="@+id/messageBubbleTimerTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/m2"
                    android:textColor="@color/bubble_timer_color"
                    android:textSize="@dimen/m20"
                    tools:text="(20s)" />

            </LinearLayout>
        </LinearLayout>

<!--     col6, col7 纯占位   -->
        <View
            android:visibility="gone"
            android:id="@+id/col6"
            android:layout_width="@dimen/m28"
            android:layout_height="match_parent" />

        <LinearLayout
            android:visibility="gone"
            android:id="@+id/col7"
            android:layout_height="wrap_content"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center">
            <LinearLayout
                android:background="@drawable/message_details_btn_bg"
                android:paddingTop="@dimen/m8"
                android:paddingBottom="@dimen/m8"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/bubble_btn_text_color"
                    android:textSize="@dimen/m22"
                    android:text="" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/m2"
                    android:textColor="@color/bubble_timer_color"
                    android:textSize="@dimen/m20"
                    tools:text="(20s)" />

            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

    <!-- 隐藏控件，用于所见即可说语音执行关闭操作 -->
    <TextView
        android:id="@+id/cd_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        android:contentDescription="@string/content_desc_close_dialog"
        android:text="@string/content_desc_close_dialog"
        android:textColor="@android:color/transparent"
        app:layout_constraintLeft_toRightOf="@id/card_bg_iv"
        app:layout_constraintTop_toTopOf="@id/card_bg_iv"
        android:textSize="1sp"
        />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/replay_panel"
        android:layout_width="@dimen/m720"
        android:layout_height="@dimen/m340"
        android:layout_marginStart="@dimen/m80"
        android:layout_marginBottom="@dimen/m136"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        >

        <include
            android:id="@+id/loading"
            layout="@layout/refresh_center"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>