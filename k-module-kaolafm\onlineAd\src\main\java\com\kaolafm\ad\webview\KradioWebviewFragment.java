package com.kaolafm.ad.webview;

import android.graphics.Bitmap;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ImageView;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.mvp.IPresenter;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;

public class KradioWebviewFragment extends BaseFragment {

    private static final String WEBVIEW_URL = "webview_url";

    private static final String TAG = "KradioWebviewFragment";

    private WebView mWebView;

    private ImageView mCloseView;


    public static KradioWebviewFragment newInstance(String url){
        KradioWebviewFragment kradioWebviewFragment = new KradioWebviewFragment();
        Bundle bundle = new Bundle();
        bundle.putString(WEBVIEW_URL, url);
        kradioWebviewFragment.setArguments(bundle);
        return kradioWebviewFragment;
    }

    @Override
    protected int getLayoutId() {
        return R.layout.fragment_ad_webview;
    }

    @Override
    protected IPresenter createPresenter() {
        return null;
    }


    @Override
    public void initView(View view) {

        Bundle bundle = getArguments();
        String url = null;
        if (bundle != null) {
            url = bundle.getString(WEBVIEW_URL);
        }

        if (TextUtils.isEmpty(url)) {
            Log.i(TAG,"webview loadurl is null");
            return;
        }


        mWebView = view.findViewById(R.id.ad_webview);
        if(Build.VERSION.SDK_INT < 19 && mWebView != null) {
            mWebView.removeJavascriptInterface("searchBoxJavaBridge_");
            mWebView.removeJavascriptInterface("accessibility");
            mWebView.removeJavascriptInterface("accessibilityTraversal");
        }

        mCloseView = view.findViewById(R.id.ad_webview_close);

        mCloseView.setOnClickListener(view1 -> {
            pop();
        });

        setWebViewSetting();

        mWebView.loadUrl(url);

    }

    private void setWebViewSetting(){
        WebSettings settings = mWebView.getSettings();


        settings.setUseWideViewPort(true);

        settings.setJavaScriptEnabled(false);
        settings.setSavePassword(false);

        //自动加载图片
        settings.setLoadsImagesAutomatically(true);

        settings.setCacheMode(WebSettings.LOAD_NO_CACHE);//不使用缓存，只从网络获取数据.


        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            // 5.0以上允许加载http和https混合的页面(5.0以下默认允许，5.0+默认禁止)
            settings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        }

        mWebView.setWebViewClient(new WebViewClient(){

            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                //可处理loading开始
                super.onPageStarted(view, url, favicon);
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                //可处理loading结束
                super.onPageFinished(view, url);
            }
        });
    }



}
