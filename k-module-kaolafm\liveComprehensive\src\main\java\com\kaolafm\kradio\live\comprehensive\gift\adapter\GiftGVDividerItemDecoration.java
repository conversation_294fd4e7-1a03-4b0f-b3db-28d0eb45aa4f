package com.kaolafm.kradio.live.comprehensive.gift.adapter;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.ResUtil;

public class GiftGVDividerItemDecoration extends RecyclerView.ItemDecoration {
    /*
     * RecyclerView的布局方向，默认先赋值 为纵向布局
     * RecyclerView 布局可横向，也可纵向
     * 横向和纵向对应的分割线画法不一样
     * */
    private int mOrientation = LinearLayoutManager.VERTICAL;

    private int mItemSize = ResUtil.getDimen(R.dimen.m52);//item之间分割线的size，默认为1

    private Paint mPaint;//绘制item分割线的画笔，和设置其属性

    public GiftGVDividerItemDecoration(Context context) {
        this(context,LinearLayoutManager.HORIZONTAL, R.color.transparent_color);
    }

    public GiftGVDividerItemDecoration(Context context, int orientation) {
        this(context,orientation, R.color.transparent_color);
    }

    public GiftGVDividerItemDecoration(Context context, int orientation, int dividerColor){
        this(context,orientation,dividerColor,20);
    }

    /**
     * @param context
     * @param orientation 绘制方向
     * @param dividerColor 分割线颜色 颜色资源id
     * @param mItemSize 分割线宽度 传入dp值就行
     */
    public GiftGVDividerItemDecoration(Context context, int orientation, int dividerColor, int mItemSize){
        this.mOrientation = orientation;
        if(orientation != LinearLayoutManager.VERTICAL && orientation != LinearLayoutManager.HORIZONTAL){
            throw new IllegalArgumentException("请传入正确的参数") ;
        }
        mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);

        mPaint.setColor(context.getResources().getColor(dividerColor));
    }


    @Override
    public void onDraw(Canvas c, RecyclerView parent, RecyclerView.State state) {
//        if(mOrientation == LinearLayoutManager.VERTICAL){
//            drawVertical(c,parent) ;
//        }else {
//            drawHorizontal(c,parent) ;
//        }
    }

    /**
     * 绘制纵向 item 分割线
     * @param canvas
     * @param parent
     */
    private void drawVertical(Canvas canvas,RecyclerView parent){
        final int left = parent.getPaddingLeft() ;
        final int right = parent.getMeasuredWidth() - parent.getPaddingRight();
        final int childSize = parent.getChildCount() ;
        for(int i = 0 ; i < childSize ; i ++){
            final View child = parent.getChildAt( i ) ;
            RecyclerView.LayoutParams layoutParams = (RecyclerView.LayoutParams) child.getLayoutParams();
            final int top = child.getBottom() + layoutParams.bottomMargin ;
            final int bottom = top + mItemSize ;
            canvas.drawRect(left,top,right,bottom,mPaint);
        }
    }

    /**
     * 绘制横向 item 分割线
     * @param canvas
     * @param parent
     */
    private void drawHorizontal(Canvas canvas, RecyclerView parent){
        final int top = parent.getPaddingTop() ;
        final int bottom = parent.getMeasuredHeight() - parent.getPaddingBottom() ;
        final int childSize = parent.getChildCount() ;
        for(int i = 0 ; i < childSize ; i ++){
            final View child = parent.getChildAt( i ) ;
            RecyclerView.LayoutParams layoutParams = (RecyclerView.LayoutParams) child.getLayoutParams();
            final int left = child.getRight() + layoutParams.rightMargin ;
            final int right = left + mItemSize ;
            canvas.drawRect(left,top,right,bottom,mPaint);
        }
    }

    /**
     * 设置item分割线的size
     * @param outRect
     * @param view
     * @param parent
     * @param state
     */
    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
        if(mOrientation == LinearLayoutManager.VERTICAL){
            outRect.set(0,0,0,mItemSize);//垂直排列 底部偏移
        }else {
            if(0 == parent.getChildLayoutPosition(view)){
                outRect.set(ResUtil.getDimen(R.dimen.m32),0,mItemSize,0);
            }else {
                outRect.set(0,0,mItemSize,0);
            }

        }
    }
}
