//package com.kaolafm.kradio.receiver;
//
//import android.content.BroadcastReceiver;
//import android.content.Context;
//import android.content.Intent;
//import android.os.Bundle;
//import android.os.Process;
//import android.util.Log;
//
//import com.kaolafm.kradio.lib.base.flavor.KRadioBackKeyInter;
//import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
//import com.kaolafm.kradio.service.HanWidgetService;
//
//import java.lang.reflect.Field;
//
///**
// * <AUTHOR>
// **/
//public class BydThemeBroadcastReceiver extends BroadcastReceiver {
//
//    private static final String TAG = "k.byd.btbr";
//
//    @Override
//    public void onReceive(Context context, Intent intent) {
//        try {
//            String action = intent.getAction();
//            Log.i(TAG, "onReceive action = " + action);
//            if ("com.byd.changebydtheme".equals(action)) {
//                String themeType = intent.getStringExtra("themeType");
//                String eco = intent.getStringExtra("eco");
//                Log.i(TAG, "onReceive themeType =" + themeType);
//                Log.i(TAG, "onReceive eco =" + eco);
//                //切换主题后，需要重新走初始化逻辑，否则会导致初始化异常的一系列问题
//                KRadioBackKeyInter kRadioBackKeyInter = ClazzImplUtil.getInter("KRadioBackKeyImpl");
//                if (kRadioBackKeyInter != null) {
//                    kRadioBackKeyInter.appExit(this);
//                }
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
////        String action = intent.getAction();
////        Log.i(TAG, "onReceive action = " + action);
////        if ("com.byd.changebydtheme".equals(action)) {
////            String themeType = intent.getStringExtra("themeType");
////            boolean isSport;
////            if ("eco".equals(themeType)) {
////                //当前为经济模式
////                Log.i(TAG, "onReceive: 【经济】模式");
////                isSport = false;
////            } else if ("sport".equals(themeType)) {
////                //当前为运动模式
////                Log.i(TAG, "onReceive: 【运动】模式");
////                isSport = true;
////            } else {
////                Log.i(TAG, "onReceive: 【无】模式");
////                //发送默认经济模式
////                isSport = false;
////                //切换主题后，需要重新走初始化逻辑，否则会导致初始化异常的一系列问题
////                KRadioBackKeyInter kRadioBackKeyInter = ClazzImplUtil.getInter("KRadioBackKeyImpl");
////                if (kRadioBackKeyInter != null) {
////                    kRadioBackKeyInter.appExit(this);
////                }
////            }
////            Log.i(TAG, "onReceive themeType =" + themeType);
////            Log.i(TAG, "onReceive isSport =" + isSport);
////        }
//    }
//}
