package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.app.PendingIntent;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.media.AudioManager;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.ecarx.sdk.mediacenter.MediaCenterAPI;
import com.ecarx.sdk.mediacenter.MediaInfo;
import com.ecarx.sdk.mediacenter.MediaListInfo;
import com.ecarx.sdk.mediacenter.MusicClient;
import com.ecarx.sdk.mediacenter.MusicPlaybackInfo;
import com.ecarx.sdk.mediacenter.RecommendInfo;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.flavor.R;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.base.flavor.KRadioMediaSessionInter;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;

import com.kaolafm.sdk.core.mediaplayer.IPlayChangedListener;
import com.kaolafm.sdk.core.mediaplayer.IPlayerStateListener;
import com.kaolafm.sdk.core.mediaplayer.OnAudioFocusChangeInter;

import com.kaolafm.sdk.core.mediaplayer.PlayItemType;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

import static com.ecarx.sdk.mediacenter.AbstractMusicPlaybackInfo.PLAYBACK_STATUS_INTERRUPT;
import static com.ecarx.sdk.mediacenter.AbstractMusicPlaybackInfo.PLAYBACK_STATUS_PAUSED;
import static com.ecarx.sdk.mediacenter.AbstractMusicPlaybackInfo.PLAYBACK_STATUS_PLAYING;
import static com.ecarx.sdk.mediacenter.AbstractMusicPlaybackInfo.PLAYBACK_STATUS_PREPARE;
import static com.ecarx.sdk.mediacenter.SourceType.SOURCE_TYPE_ONLINE;
import static com.kaolafm.kradio.lib.utils.Constants.RESOURCES_TYPE_BROADCAST;
import static com.kaolafm.kradio.lib.utils.UrlUtil.PIC_100_100;
import static com.kaolafm.kradio.lib.utils.UrlUtil.PIC_250_250;
import static com.kaolafm.kradio.lib.utils.UrlUtil.getDefaultConfigPicUrl;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-08-23 20:45
 ******************************************/
public final class KRadioMediaSessionImpl implements KRadioMediaSessionInter {
    private static final String TAG = "KRadioMediaSessionImpl";
    private MyOnAudioFocusChangeInter myOnAudioFocusChangeInter;
    private MediaSessionIPlayerStateListener mMediaSessionIPlayerStateListener;
    private MyIPlayChangedListener myIPlayChangedListener;

    private MyMusicClient myMusicClient;

    private boolean isRelease;

    private Object mToken;

    private String mAppName;

    public KRadioMediaSessionImpl() {
        initAppName();
        myOnAudioFocusChangeInter = new MyOnAudioFocusChangeInter(this);
        mMediaSessionIPlayerStateListener = new MediaSessionIPlayerStateListener(this);
        myIPlayChangedListener = new MyIPlayChangedListener(this);
        Log.i(TAG, "KRadioMediaSessionImpl------>" + KLAutoPlayerManager.getInstance().checkNull());
        PlayerManager.getInstance().addPlayControlStateCallback(mMediaSessionIPlayerStateListener);
        KLAutoPlayerManager.getInstance().addIPlayChangedListener(myIPlayChangedListener);
        PlayerManager.getInstance().addAudioFocusListener(myOnAudioFocusChangeInter);
    }

    private void initAppName() {
        Context context = AppDelegate.getInstance().getContext();
        try {
            PackageManager packageManager = context.getPackageManager();
            PackageInfo packageInfo = packageManager.getPackageInfo(
                    context.getPackageName(), 0);
            int labelRes = packageInfo.applicationInfo.labelRes;
            mAppName = context.getResources().getString(labelRes);
            Log.i(TAG, "initAppName-------->" + mAppName);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void registerMediaSession(Object... args) {
        Context context = (Context) args[0];
        MediaCenterAPI mediaCenterAPI = MediaCenterAPI.get(context);
        if (myMusicClient == null) {
            myMusicClient = new MyMusicClient();
            initToken();
        }

        Log.i(TAG, "registerMediaSession-------->mToken = " + mToken);

        if (isTokenAvailable()) {
            mediaCenterAPI.requestPlay(mToken);
            mediaCenterAPI.updateCurrentSourceType(mToken, SOURCE_TYPE_ONLINE);
//            resetMediaSessionResource();
        }

        Log.i(TAG, "registerMediaSession-------->updateCurrentSourceType = " + SOURCE_TYPE_ONLINE);
    }

    @Override
    public void unregisterMediaSession(Object... args) {
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001270547010?userId=1229522问题
        updatePausedStatus();
        Log.i(TAG, "unregisterMediaSession-------->" + myMediaListInfo);
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001297677193?userId=1229522问题
        if (myMediaListInfo != null) {
            myMediaListInfo.setPlayItems(null);
        }
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001336934199?userId=1229522问题
        unregisterToken();
    }

    @Override
    public void release(Object... args) {
        if (isRelease) {
            return;
        }

        PlayerManager.getInstance().removePlayControlStateCallback(mMediaSessionIPlayerStateListener);
        PlayerManager.getInstance().removeIPlayChangedListener(myIPlayChangedListener);

        isRelease = true;
        PlayerManager.getInstance().removeAudioFocusListener(myOnAudioFocusChangeInter);
        resetMediaSessionResource();
    }

    private void unregisterToken() {
        if (mToken != null) {
            MediaCenterAPI mediaCenterAPI = MediaCenterAPI.get(AppDelegate.getInstance().getContext());
            mediaCenterAPI.unregister(mToken);
            myMusicClient = null;
        }
    }

    private void resetMediaSessionResource() {
        MediaCenterAPI mediaCenterAPI = MediaCenterAPI.get(AppDelegate.getInstance().getContext());
        if (mediaCenterAPI != null && isTokenAvailable()) {
            mediaCenterAPI.updateCurrentSourceType(mToken, -1);
            mediaCenterAPI.updateMusicPlaybackState(mToken, new MusicPlaybackInfo() {
                @Override
                public int getSourceType() {
                    return -1;
                }
            });
        }
    }

    private static class MyOnAudioFocusChangeInter implements OnAudioFocusChangeInter {
        private WeakReference<KRadioMediaSessionImpl> weakReference;

        public MyOnAudioFocusChangeInter(KRadioMediaSessionImpl kRadioMediaSessionImpl) {
            weakReference = new WeakReference<>(kRadioMediaSessionImpl);
        }

        @Override
        public void onAudioFocusChange(int i) {
            KRadioMediaSessionImpl kRadioMediaSessionImpl = weakReference.get();
            if (kRadioMediaSessionImpl == null) {
                return;
            }
            Log.i(TAG, "onAudioFocusChange--------->focus = " + i);
            if (i == AudioManager.AUDIOFOCUS_GAIN) {
                kRadioMediaSessionImpl.registerMediaSession(AppDelegate.getInstance().getContext());
            } else if (i == AudioManager.AUDIOFOCUS_LOSS || i == AudioManager.AUDIOFOCUS_LOSS_TRANSIENT) {
                kRadioMediaSessionImpl.unregisterMediaSession(AppDelegate.getInstance().getContext());
            }
        }
    }

    private void updatePausedStatus() {
        int focusStatus = PlayerManager.getInstance().getCurrentAudioFocusStatus();
        Log.i(TAG, "onPlayerPaused----------->focusStatus = " + focusStatus);
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001276965757?userId=1545533问题
        updatePlayStatus(focusStatus < 0 ? PLAYBACK_STATUS_INTERRUPT : PLAYBACK_STATUS_PAUSED);
    }

    private void updatePlayStatus(int playBackStatus) {
        if (isTokenAvailable()) {
            MediaCenterAPI.get(AppDelegate.getInstance().getContext()).updateMusicPlaybackState(mToken, new MusicPlaybackInfo() {
                @Override
                public String getTitle() {
                    PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
                    String title;
                    if (playItem != null) {
                        title = playItem.getTitle();
                        if (TextUtils.isEmpty(title)) {
                            title = KLAutoPlayerManager.getInstance().getRadioName();
                        }
                    } else {
                        title = KLAutoPlayerManager.getInstance().getRadioName();
                    }
                    Log.i(TAG, "getTitle------->title = " + title);
                    return title;
                }

                @Override
                public String getAppName() {
                    return mAppName;
                }

                @Override
                public String getAlbum() {
                    String radioName = KLAutoPlayerManager.getInstance().getRadioName();
                    Log.i(TAG, "getAlbum------->radioName = " + radioName);
                    return radioName;
                }

                @Override
                public String getArtist() {
                    String radioName = KLAutoPlayerManager.getInstance().getRadioName();
                    Log.i(TAG, "getArtist------->radioName = " + radioName);
                    return radioName;
                }

                @Override
                public long getDuration() {
                    int duration = mMediaSessionIPlayerStateListener.getDuration();
                    if (duration > 0) {
                        return duration;
                    }

                    PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
                    if (playItem != null) {
                        return playItem.getDuration();
                    }
                    return super.getDuration();
                }

                @Override
                public int getSourceType() {
                    Log.i(TAG, "getSourceType------->src = " + SOURCE_TYPE_ONLINE);
                    return SOURCE_TYPE_ONLINE;
                }

                @Override
                public PendingIntent getLaunchIntent() {
                    Intent intent;
                    Context context = AppDelegate.getInstance().getContext();
                    Activity activity = AppManager.getInstance().getTopActivity();
                    if (activity == null) {
                        intent = context.getPackageManager().getLaunchIntentForPackage(context.getPackageName());
                    } else {
                        intent = new Intent();
                        ComponentName componentName = new ComponentName(AppDelegate.getInstance().getContext().getPackageName(), activity.getLocalClassName());
                        intent.setComponent(componentName);
//                        Log.i(TAG, "getLaunchIntent------->start = " + componentName);
                    }
                    return PendingIntent.getActivity(AppDelegate.getInstance().getContext(), 101, intent, PendingIntent.FLAG_UPDATE_CURRENT);
                }

                @Override
                public int getPlaybackStatus() {
                    return playBackStatus;
                }

                @Override
                public Uri getArtwork() {
                    PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
                    if (playItem != null) {
                        String picUrl = getPic(playItem, PIC_250_250);
                        return Uri.parse(picUrl);
                    }
                    return super.getArtwork();
                }

                // 解决https://app.huoban.com/tables/2100000007530121/items/2300001285486601?userId=1229522问题
                @Override
                public String getUuid() {
                    KLAutoPlayerManager klAutoPlayerManager = KLAutoPlayerManager.getInstance();
                    ArrayList<PlayItem> playItemArrayList = klAutoPlayerManager.getPlayList(true);
                    PlayItem playItem = null;
                    if (ListUtil.isEmpty(playItemArrayList)) {
                        playItem = klAutoPlayerManager.getCurrentPlayItem();
                    } else {
                        int position = PlayerManager.getInstance().getPlayListCurrentPosition();
                        int size = playItemArrayList.size();
                        if (position < size && position >= 0) {
                            playItem = playItemArrayList.get(position);
                        }
                    }
                    if (playItem != null) {
                        long audioId = playItem.getAudioId();
                        Log.i(TAG, "getUuid------->audioId = " + audioId);
                        return String.valueOf(audioId);
                    }
                    return super.getUuid();
                }

                @Override
                public int getPlayingItemPositionInQueue() {
                    int position = PlayerManager.getInstance().getPlayListCurrentPosition();
                    Log.i(TAG, "getPlayingItemPositionInQueue------>" + position);
                    // 解决https://app.huoban.com/tables/2100000007530121/items/2300001336321361?userId=1545533问题
                    return position;
                }
            });
        }
    }

    private void updateProgress(int progress) {
        if (isTokenAvailable()) {
            MediaCenterAPI.get(AppDelegate.getInstance().getContext()).updateCurrentProgress(mToken, progress);
        }
    }

    private static class MediaSessionIPlayerStateListener implements IPlayerStateListener {
        private WeakReference<KRadioMediaSessionImpl> mWeakReference;
        private int mDuration;

        public MediaSessionIPlayerStateListener(KRadioMediaSessionImpl kRadioMediaSessionImpl) {
            mWeakReference = new WeakReference<>(kRadioMediaSessionImpl);
        }

        public int getDuration() {
            return mDuration;
        }

        @Override
        public void onIdle(PlayItem playItem) {
            Log.i(TAG, "onIdle----------->");
            mDuration = 0;
        }

        @Override
        public void onPlayerPreparing(PlayItem playItem) {
            Log.i(TAG, "onPlayerPreparing----------->");
            KRadioMediaSessionImpl kRadioMediaSessionImpl = mWeakReference.get();
            if (kRadioMediaSessionImpl != null) {
                kRadioMediaSessionImpl.updatePlayStatus(PLAYBACK_STATUS_PREPARE);
            }
        }

        @Override
        public void onPlayerPlaying(PlayItem playItem) {
            Log.i(TAG, "onPlayerPlaying----------->");
            mDuration = 0;
            KRadioMediaSessionImpl kRadioMediaSessionImpl = mWeakReference.get();
            if (kRadioMediaSessionImpl != null) {
                kRadioMediaSessionImpl.updatePlayList();
                kRadioMediaSessionImpl.updatePlayStatus(PLAYBACK_STATUS_PLAYING);
            }
        }

        @Override
        public void onPlayerPaused(PlayItem playItem) {
            Log.i(TAG, "onPlayerPaused----------->");
            KRadioMediaSessionImpl kRadioMediaSessionImpl = mWeakReference.get();
            if (kRadioMediaSessionImpl != null) {
                kRadioMediaSessionImpl.updatePausedStatus();
            }
        }

        @Override
        public void onProgress(String s, int progress, int duration, boolean b) {
            mDuration = duration;
            KRadioMediaSessionImpl kRadioMediaSessionImpl = mWeakReference.get();
            if (kRadioMediaSessionImpl != null) {
                kRadioMediaSessionImpl.updateProgress(progress);
            }
        }

        @Override
        public void onPlayerFailed(PlayItem playItem, int i, int i1) {
            Log.i(TAG, "onPlayerFailed----------->");
            KRadioMediaSessionImpl kRadioMediaSessionImpl = mWeakReference.get();
            if (kRadioMediaSessionImpl != null) {
                kRadioMediaSessionImpl.updatePausedStatus();
            }
        }

        @Override
        public void onPlayerEnd(PlayItem playItem) {
            Log.i(TAG, "onPlayerEnd----------->");
            // 解决https://app.huoban.com/tables/2100000007530121/items/2300001315861232?userId=1229522问题
            KRadioMediaSessionImpl kRadioMediaSessionImpl = mWeakReference.get();
            if (kRadioMediaSessionImpl != null) {
                kRadioMediaSessionImpl.updatePausedStatus();
            }
        }

        @Override
        public void onSeekStart(String s) {
            Log.i(TAG, "onSeekStart----------->");
        }

        @Override
        public void onSeekComplete(String s) {
            Log.i(TAG, "onSeekComplete----------->");
        }

        @Override
        public void onBufferingStart(PlayItem playItem) {
            Log.i(TAG, "onBufferingStart----------->");
        }

        @Override
        public void onBufferingEnd(PlayItem playItem) {
            Log.i(TAG, "onBufferingEnd----------->");
        }
    }

    private static class MyMusicClient extends MusicClient {

        @Override
        public boolean onPlay() {
            new Handler(Looper.getMainLooper()).post(new Runnable() {
                @Override
                public void run() {
                    Log.i(TAG, "onPlay--------->start");
                    PlayerManager.getInstance().play(true);
                }
            });
            return true;
        }

        @Override
        public boolean onPause() {
            new Handler(Looper.getMainLooper()).post(new Runnable() {
                @Override
                public void run() {
                    Log.i(TAG, "onPause--------->start");
                    PlayerManager.getInstance().pause(true);
                }
            });
            return true;
        }

        @Override
        public boolean onNext() {
            new Handler(Looper.getMainLooper()).post(new Runnable() {
                @Override
                public void run() {
                    Log.i(TAG, "onNext--------->start");
                    KLAutoPlayerManager klAutoPlayerManager = KLAutoPlayerManager.getInstance();
                    if (klAutoPlayerManager.hasNext()) {
                        klAutoPlayerManager.playNext();
                    } else {
                        Context context = AppDelegate.getInstance().getContext();
                        ToastUtil.showError(context, R.string.is_last_one_warning_str);
                    }
                }
            });
            return true;
        }

        @Override
        public boolean onPrevious() {
            new Handler(Looper.getMainLooper()).post(new Runnable() {
                @Override
                public void run() {
                    Log.i(TAG, "onPrevious--------->start");
                    KLAutoPlayerManager klAutoPlayerManager = KLAutoPlayerManager.getInstance();
                    if (klAutoPlayerManager.hasPre()) {
                        klAutoPlayerManager.playPre();
                    } else {
                        Context context = AppDelegate.getInstance().getContext();
                        ToastUtil.showError(context, R.string.is_first_one_warning_str);
                    }
                }
            });
            return true;
        }

        @Override
        public boolean onForward() {
            new Handler(Looper.getMainLooper()).post(new Runnable() {
                @Override
                public void run() {
                    Log.i(TAG, "onForward--------->start");
                    PlayerManagerHelper.getInstance().fastForward();
                }
            });
            return true;
        }

        @Override
        public boolean onRewind() {
            new Handler(Looper.getMainLooper()).post(new Runnable() {
                @Override
                public void run() {
                    Log.i(TAG, "onRewind--------->start");
                    PlayerManagerHelper.getInstance().fastBackward();
                }
            });
            return true;
        }

        @Override
        public boolean onCollect(int i, boolean b) {
            return false;
        }

        @Override
        public boolean onDownload(int i, boolean b) {
            return false;
        }

        @Override
        public boolean onLoopModeChange(int i) {
            return false;
        }

        @Override
        public boolean onSourceSelected(int i) {
            return false;
        }

        @Override
        public boolean onSourceChanged(int i, String s) {
            return false;
        }

        @Override
        public boolean onMediaSelected(MediaInfo mediaInfo) {
            return false;
        }

        @Override
        public MusicPlaybackInfo getMusicPlaybackInfo() {
            return null;
        }

        @Override
        public int[] getMediaSourceTypeList() {
            return new int[0];
        }

        @Override
        public int getCurrentSourceType() {
            return 0;
        }

        @Override
        public long getCurrentProgress() {
            PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
            if (playItem != null) {
                return playItem.getPosition();
            }
            return 0;
        }

        @Override
        public List<MediaInfo> getPlaylist(int i) {
            return null;
        }

        @Override
        public boolean onReplay() {
            return false;
        }

        @Override
        public boolean onPlayRecommend(RecommendInfo recommendInfo) {
            return false;
        }

        @Override
        public boolean onCancelRecommend(RecommendInfo recommendInfo) {
            return false;
        }

        @Override
        public boolean onMediaSelected(int index, String s) {
            ArrayList<PlayItem> playItems = KLAutoPlayerManager.getInstance().getPlayList(true);
            if (ListUtil.isEmpty(playItems)) {
                return false;
            }
            for (int i = 0, size = playItems.size(); i < size; i++) {
                PlayItem playItem = playItems.get(i);
                if (playItem == null) {
                    continue;
                }
                long audioId = playItem.getAudioId();
                if (StringUtil.equals(String.valueOf(audioId), s)) {
                    new Handler(Looper.getMainLooper()).post(new Runnable() {
                        @Override
                        public void run() {
                            if (playItem.getType() == PlayItemType.BROADCAST_LIVING) {
                                PlayerManager.getInstance().play(audioId, RESOURCES_TYPE_BROADCAST);
                            } else {
                                PlayerManager.getInstance().play(playItem);
                            }
                        }
                    });
                    break;
                }
            }
            return false;
        }

        @Override
        public boolean onMediaForward(boolean b) {
            return false;
        }

        @Override
        public boolean onMediaRewind(boolean b) {
            return false;
        }

        @Override
        public boolean onMediaQualityChange(int i) {
            return false;
        }

        @Override
        public void onMediaCenterFocusChanged(String s) {

        }
    }

    private boolean isTokenAvailable() {
        boolean flag = mToken != null;
        if (!flag) {
            initToken();
        }
        flag = mToken != null;
        return flag;
    }

    private void initToken() {
        Context context = AppDelegate.getInstance().getContext();
        MediaCenterAPI mediaCenterAPI = MediaCenterAPI.get(context);
        if (mediaCenterAPI != null) {
            mToken = mediaCenterAPI.registerMusic(context.getPackageName(), myMusicClient);
        }
        if (mToken != null) {
            mediaCenterAPI.requestPlay(mToken);
            mediaCenterAPI.updateCurrentSourceType(mToken, SOURCE_TYPE_ONLINE);
        }
    }

    private static class MyIPlayChangedListener implements IPlayChangedListener {
        private WeakReference<KRadioMediaSessionImpl> mWeakReference;

        public MyIPlayChangedListener(KRadioMediaSessionImpl kRadioMediaSessionImpl) {
            mWeakReference = new WeakReference<>(kRadioMediaSessionImpl);
        }

        @Override
        public void onPlayChangeChanged(PlayItem playItem) {
            KRadioMediaSessionImpl kRadioMediaSessionImpl = mWeakReference.get();
            Log.i(TAG, "onPlayChangeChanged-------->" + kRadioMediaSessionImpl);
            if (kRadioMediaSessionImpl != null) {
                kRadioMediaSessionImpl.updatePlayList();
            }
        }
    }

    private class MyMediaListInfo extends MediaListInfo {
        private ArrayList<PlayItem> mPlayItems;

        public ArrayList<PlayItem> getPlayItems() {
            return mPlayItems;
        }

        public void setPlayItems(ArrayList<PlayItem> mPlayItems) {
            if (ListUtil.isEmpty(mPlayItems)) {
                this.mPlayItems = null;
                return;
            }
            this.mPlayItems = (ArrayList<PlayItem>) mPlayItems.clone();
        }

        @Override
        public int getMediaListType() {
            // 解决https://app.huoban.com/tables/2100000007530121/items/2300001337039189?userId=1229522问题
            return 0;
        }

        @Override
        public int getSourceType() {
            return SOURCE_TYPE_ONLINE;
        }

        @Override
        public List<MediaInfo> getMediaList() {
            Log.i(TAG, "getMediaList--------->");
            ArrayList<PlayItem> playItemArrayList = KLAutoPlayerManager.getInstance().getPlayList(true);
            if (!ListUtil.isEmpty(playItemArrayList)) {
                int size = playItemArrayList.size();
                List<MediaInfo> mediaInfoList = new ArrayList<>(size);
                for (int i = 0; i < size; i++) {
                    PlayItem playItem = playItemArrayList.get(i);
                    if (playItem == null) {
                        continue;
                    }
                    MyMediaInfo mediaInfo = new MyMediaInfo(playItem, i);
                    mediaInfoList.add(mediaInfo);
                }
                Log.i(TAG, "getMediaList--------->mediaInfoList size = " + mediaInfoList.size());
                return mediaInfoList;
            }
            return super.getMediaList();
        }
    }


    private MyMediaListInfo myMediaListInfo;

    private void updatePlayList() {
        Log.i(TAG, "updatePlayList-------->mToken = " + mToken);
        if (isTokenAvailable()) {
            if (myMediaListInfo == null) {
                myMediaListInfo = new MyMediaListInfo();
            }

//            ArrayList<PlayItem> prePlayItems = myMediaListInfo.getPlayItems();
//            ArrayList<PlayItem> curPlayItems = KLAutoPlayerManager.getInstance().getPlayList(true);

//            boolean canUpdate = false;
//            int preSize = prePlayItems == null ? 0 : prePlayItems.size();
//            int curSize = curPlayItems == null ? 0 : curPlayItems.size();
//            if (preSize != curSize) {
//                canUpdate = true;
//            } else {
//                for (int i = 0; i < curSize; i++) {
//                    PlayItem prePlayItem = prePlayItems.get(i);
//                    PlayItem curPlayItem = curPlayItems.get(i);
//                    if (prePlayItem == null || curPlayItem == null) {
//                        continue;
//                    }
//                    if (prePlayItem.getAudioId() != curPlayItem.getAudioId()) {
//                        canUpdate = true;
//                        break;
//                    }
//                }
//            }
            Log.i(TAG, "updatePlayList-------->");
//            Log.i(TAG, "updatePlayList-------->canUpdate = " + canUpdate + "----->preSize = " + preSize + "---->curSize = " + curSize);
            // 解决https://app.huoban.com/tables/2100000007530121/items/2300001315926793?userId=1229522问题
//            if (canUpdate) {
            MediaCenterAPI.get(AppDelegate.getInstance().getContext()).updateMediaList(mToken, myMediaListInfo);
//            }
//            myMediaListInfo.setPlayItems(curPlayItems);
        }
    }

    private class MyMediaInfo extends MediaInfo {
        private PlayItem mPlayItem;
        private int mIndex;

        public MyMediaInfo(PlayItem playItem, int index) {
            mPlayItem = playItem;
            mIndex = index;
        }

        @Override
        public String getTitle() {
            String title;
            if (mPlayItem.getCategoryId() == PlayItem.CTG_TYPE_TX) {
                title = mPlayItem.getAlbumName();
            } else {
                title = mPlayItem.getTitle();
            }
            return title;
        }

        @Override
        public String getUuid() {
            return String.valueOf(mPlayItem.getAudioId());
        }

        @Override
        public String getArtist() {
            return KLAutoPlayerManager.getInstance().getRadioName();
        }

        @Override
        public Uri getArtwork() {
            String picUrl = getPic(mPlayItem, PIC_100_100);
            return Uri.parse(picUrl);
        }

        @Override
        public int getSourceType() {
            return SOURCE_TYPE_ONLINE;
        }

        @Override
        public int getAlbumIndex() {
            return mIndex;
        }

        @Override
        public int getPlayingItemPositionInQueue() {
            int position = PlayerManager.getInstance().getPlayListCurrentPosition();
            Log.i(TAG, "MediaInfo getPlayingItemPositionInQueue------>" + position);
            // 解决https://app.huoban.com/tables/2100000007530121/items/2300001336321361?userId=1545533问题
            return position;
        }
    }

    private String getPic(PlayItem playItem, String type) {
        String picUrl = playItem.getAudioPic();
        picUrl = TextUtils.isEmpty(picUrl) ? playItem.getAlbumPic() : picUrl;

        return getDefaultConfigPicUrl(picUrl, type);
    }
}