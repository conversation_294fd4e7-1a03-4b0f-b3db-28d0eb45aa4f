package com.kaolafm.kradio.player.online.utils;

import android.support.v4.util.LruCache;

import java.nio.charset.Charset;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.List;

public class MemoryCacheManager {
    private LruCache<String, List<?>> mMediaFileBitmapMemoryCache;
    // 获取该应用虚拟机允许的最大内存，超过该内存量，会OOM。用kb单位保存。
    private int maxMemory = (int) (Runtime.getRuntime().maxMemory() / 1024);
    // 获取最大内存的1/8.
    private int cacheSize = maxMemory / 8;
    private volatile static MemoryCacheManager instance;

    private MemoryCacheManager() {
        mMediaFileBitmapMemoryCache = new LruCache<String, List<?>>(cacheSize);
    }

    public static MemoryCacheManager newInstance() {
        if (MemoryCacheManager.instance == null) {
            synchronized (MemoryCacheManager.class) {
                if (MemoryCacheManager.instance == null) {
                    MemoryCacheManager.instance = new MemoryCacheManager();
                }
            }
        }
        return MemoryCacheManager.instance;
    }

    public void addListDataToMemoryCache(String key, List<?> listData) {
        if (getListDataFromMemCache(key) == null) {
            mMediaFileBitmapMemoryCache.put(hashKeyForDisk(key), listData);
        }
    }

    private List<?> getListDataFromMemCache(String key) {
        return mMediaFileBitmapMemoryCache.get(hashKeyForDisk(key));
    }

    public void clearAllFromMemCache() {
        mMediaFileBitmapMemoryCache.evictAll();
    }

    /**
     * String MD5加密
     *
     * @param key
     * @return
     */
    private String hashKeyForDisk(String key) {
        String cacheKey;
        try {
            MessageDigest mDigest = MessageDigest.getInstance("MD5");
            mDigest.update(key.getBytes(Charset.forName("UTF-8")));
            cacheKey = bytesToHexString(mDigest.digest());
        } catch (NoSuchAlgorithmException e) {
            cacheKey = String.valueOf(key.hashCode());
        }
        return cacheKey.toLowerCase();
    }


    private String bytesToHexString(byte[] var0) {
        StringBuffer var1 = new StringBuffer(var0.length);
        for (byte var3 : var0) {
            String var2 = Integer.toHexString(255 & (int) var0[var3]);
            if (var2.length() < 2) {
                var1.append(0);
            }
            var1.append(var2.toUpperCase());
        }

        return var1.toString();
    }
}
