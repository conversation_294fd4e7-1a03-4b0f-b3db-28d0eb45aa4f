package com.kaolafm.kradio.common.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import android.util.AttributeSet;
import android.view.View;
import android.widget.LinearLayout;
import com.kaolafm.kradio.k_kaolafm.R;

/**
 * <AUTHOR>
 **/
public class GradientProgressBar extends ConstraintLayout {
    private View mPlayerBarProgress;
    private Drawable mProgressDrawable;

    public GradientProgressBar(Context context) {
        this(context, null);
    }

    public GradientProgressBar(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public GradientProgressBar(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();

        final TypedArray a = context.obtainStyledAttributes(
                attrs, R.styleable.GradientProgressBar, defStyleAttr, 0);

        final Drawable progressDrawable = a.getDrawable(R.styleable.GradientProgressBar_progressDrawable);
        final int progress = a.getInt(R.styleable.GradientProgressBar_progress, 0);
        if (progressDrawable != null) {
            setProgressDrawable(progressDrawable);
        }

        updateProgress(progress);
    }


    private void init() {
        mPlayerBarProgress = new View(getContext());
        addView(mPlayerBarProgress);
    }


    public void updateProgress(int progress) {
        int parentWidth = getWidth();
        updateProgress(progress, parentWidth);
    }

    public void updateProgress(int progress, int parentWidth) {
        float progressPercent = (float) progress / 100.f;

        ConstraintLayout.LayoutParams lparams = (ConstraintLayout.LayoutParams) mPlayerBarProgress.getLayoutParams();
        lparams.width = 0;
        lparams.height = LayoutParams.MATCH_PARENT;
        lparams.matchConstraintPercentWidth = progressPercent;
        lparams.leftToLeft = ConstraintLayout.LayoutParams.PARENT_ID;
        lparams.topToTop = ConstraintLayout.LayoutParams.PARENT_ID;
        lparams.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID;
        mPlayerBarProgress.setLayoutParams(lparams);
    }

    public void resetProgress() {
        LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.MATCH_PARENT);
        mPlayerBarProgress.setLayoutParams(lp);
    }


    public Drawable getProgressDrawable() {
        return mProgressDrawable;
    }

    public void setProgressDrawable(Drawable d) {
        this.mProgressDrawable = d;
        mPlayerBarProgress.setBackground(d);
    }

}
