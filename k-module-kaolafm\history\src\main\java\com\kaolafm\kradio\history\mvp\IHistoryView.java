package com.kaolafm.kradio.history.mvp;

import androidx.annotation.StringRes;
import com.kaolafm.kradio.lib.bean.HistoryItem;
import com.kaolafm.kradio.lib.base.mvp.IView;
import com.kaolafm.opensdk.api.personalise.model.HotRecommend;

import java.util.List;

/**
 * Created by kaolafm on 2018/5/2.
 */

public interface IHistoryView extends IView{

    /**
     * 显示一键清空按钮
     * @param show true表示显示
     */
    void showButton(boolean show);

    void showHistory(List<HistoryItem> historyItems);
    /**
     * 显示空页面
     */
    void showEmpty();

    void finishRefresh(boolean success);

    /**
     * 准备显示Loading状态前的界面状态
     */
    void prepareFragmentStateForShowLoading();

    void showLoading();

    void hideLoading();

    void hideErrorLayout();

    void showError(String error, boolean clickToRetry);

    void showToast(@StringRes int resId);

    /**
     * 推荐内容
     * @param hotRecommend
     */
    void showHotRecommend(HotRecommend hotRecommend);

    void loginStatusChanged();

}
