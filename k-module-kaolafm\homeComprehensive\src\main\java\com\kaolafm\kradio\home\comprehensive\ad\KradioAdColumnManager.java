package com.kaolafm.kradio.home.comprehensive.ad;


import android.text.TextUtils;
import android.util.Log;
import android.util.SparseArray;
import com.kaolafm.ad.comprehensive.ADDataHandle.GetAdDataMap;
import com.kaolafm.ad.api.AdvertisingRequest;
import com.kaolafm.ad.api.model.ImageAdvert;
import com.kaolafm.ad.comprehensive.control.KradioAdSceneConstants;
import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.home.comprehensive.recyclerview.HomeRecyclerViewHelper;
import com.kaolafm.opensdk.api.operation.model.column.ColumnGrp;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import java.util.List;
import java.util.Map;

/**
 * 栏目成员连图广告管理类
 */

public class KradioAdColumnManager {

    private volatile static KradioAdColumnManager instance;

    private HomeRecyclerViewHelper mHomeRecyclerViewHelper;

    private static final String TAG = "KradioAdColumnManager";

    private SparseArray adverts = new SparseArray<ImageAdvert>();

    private KradioAdColumnManager() {
    }

    public static KradioAdColumnManager getInstance() {
        if (instance == null) {
            synchronized (KradioAdColumnManager.class) {
                if (instance == null) {
                    instance = new KradioAdColumnManager();
                }
            }
        }
        return instance;
    }

    public void bindHomeRecyclerViewHelper(HomeRecyclerViewHelper homeRecyclerViewHelper){
        mHomeRecyclerViewHelper = homeRecyclerViewHelper;
    }

    public void unBindHomeRecyclerViewHelper(){
        mHomeRecyclerViewHelper = null;
    }

    public void checkColumnAd(ColumnGrp childColumnGrp, List<HomeCell> cells, int index){
        //连图运营位是动态的，所以如果曝光场景id是-1 才进行后续逻辑
        Long sceneId = GetAdDataMap.getLongADSpaceBySceneID(KradioAdSceneConstants.HOME_PAGE_COLOUM_SCENE);

        if (sceneId == null || sceneId != -1) {
            Log.i(TAG,"checkColumnAd ,sceneId not eq -1");
            return;
        }

        Map<String,String> extMap = childColumnGrp.getExtInfo();
        if (extMap == null) {
            Log.i(TAG,"checkColumnAd ,extMap is null");
            return;
        }

        String value = extMap.get("AD_ZONE_ID");

        if (TextUtils.isEmpty(value)) {
            Log.i(TAG,"checkColumnAd ,AD_ZONE_ID is null");
            return;
        }


        //请求栏目封面连图
        requestColumnAd(value,cells,index);
    }

    private void requestColumnAd(String adZoneId, List<HomeCell> cells,int index) {

        new AdvertisingRequest().getImageAdvertList(adZoneId, null, null, new HttpCallback<List<ImageAdvert>>() {
            @Override
            public void onSuccess(List<ImageAdvert> imageAdverts) {
                replaceAdImageUrl(cells,imageAdverts,index);
            }

            @Override
            public void onError(ApiException e) {

            }
        });

    }

    private void replaceAdImageUrl(List<HomeCell> cells, List<ImageAdvert> imageAdverts, int index){

        int coverSize = imageAdverts.size();

        int cellSzie = cells.size();

        Log.i(TAG,"replaceAdImageUrl coverSize:"+coverSize +",cellSize:"+cellSzie +",index:"+index);

        if (coverSize > cellSzie) {
            return;
        }

        int j = cells.size() - coverSize;
        int columnPosition = index - coverSize;

        for (int i = 0; i < coverSize; i++) {

            if (j >= cellSzie) {
                break;
            }
            //替换url
            ImageAdvert imageAdvert = imageAdverts.get(i);
            String url = imageAdvert.getUrl();
            Log.i(TAG,"replaceAdImageUrl:"+url + ", column index:"+columnPosition);
            HomeCell cell = cells.get(j);
            cell.imageUrl = url;
            cell.iImageAd = i;

            //储存creativeId，上报用
            setCreativeIds(columnPosition,imageAdvert);
            j++;
            columnPosition++;
        }

        int positionStart = index - coverSize;

        if (mHomeRecyclerViewHelper != null) {
            Log.i(TAG,"notifyItemRangeChanged start:"+positionStart+" ,itemCount:"+coverSize);
            mHomeRecyclerViewHelper.notifyItemRangeChanged(positionStart,coverSize);
        }

    }


    private void setCreativeIds(int key,ImageAdvert advert){
        Log.i(TAG,"setCreativeIds:"+key+",id:"+advert.toString());
        adverts.put(key,advert);
    }

    public ImageAdvert getAdvert(int key){
        Object o = adverts.get(key);
        return (ImageAdvert) o;
    }

    public void removeAdvert(int key){
        adverts.remove(key);
    }

}
