<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <ImageView
        android:id="@+id/hostPic"
        android:layout_width="@dimen/m380"
        android:layout_height="@dimen/m380"
        android:layout_marginTop="@dimen/y118"
        android:layout_marginEnd="@dimen/x156"
        android:scaleType="fitCenter"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.kaolafm.kradio.component.ui.base.view.KradioTextView
        android:id="@+id/hostNameTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y32"
        android:textSize="@dimen/m32"
        android:textStyle="bold"
        app:kt_end_color="@color/online_player_host_name_end"
        app:kt_gradient_angle="270"
        app:kt_start_color="@color/online_player_host_name_start"
        app:kt_text_color_gradient="true"
        app:layout_constraintEnd_toEndOf="@id/hostPic"
        app:layout_constraintStart_toStartOf="@id/hostPic"
        app:layout_constraintTop_toBottomOf="@id/hostPic"
        tools:text="主播：董乐、王尚" />

    <TextView
        android:id="@+id/liveTitleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y8"
        android:textColor="@color/online_player_live_title"
        android:textSize="@dimen/m24"
        app:layout_constraintEnd_toEndOf="@id/hostPic"
        app:layout_constraintStart_toStartOf="@id/hostPic"
        app:layout_constraintTop_toBottomOf="@id/hostNameTv"
        tools:text="好车在路上" />

</merge>