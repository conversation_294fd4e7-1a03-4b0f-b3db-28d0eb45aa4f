package com.kaolafm.kradio.coin;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

/**
 * <AUTHOR>
 **/
public class CoinBean implements Serializable {
    /**
     * integral : 262
     * uid : HD3Kqm-FmkA
     */

    @SerializedName("integral")
    private int integral;

    @SerializedName("uid")
    private String uid;

    public int getIntegral() {
        return integral;
    }

    public void setIntegral(int integral) {
        this.integral = integral;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    @Override
    public String toString() {
        return "CoinBean{" +
                "integral=" + integral +
                ", uid='" + uid + '\'' +
                '}';
    }
}
