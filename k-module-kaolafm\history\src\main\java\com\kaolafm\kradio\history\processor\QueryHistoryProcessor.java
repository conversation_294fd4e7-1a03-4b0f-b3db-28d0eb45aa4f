package com.kaolafm.kradio.history.processor;

import android.text.TextUtils;
import android.util.Log;
import com.kaolafm.kradio.component.ActionProcessor;
import com.kaolafm.kradio.component.ComponentClient;
import com.kaolafm.kradio.component.ComponentResult;
import com.kaolafm.kradio.component.RealCaller;
import com.kaolafm.kradio.component.SharedConst;
import com.kaolafm.kradio.history.HistoryManager;

/**
 * 查询历史
 * <AUTHOR>
 * @date 2019-10-24
 */
@SharedConst
public class QueryHistoryProcessor implements ActionProcessor {

    private static final String ACTION_QUERY = "QueryHistory";

    private static final String KEY_ALBUM_ID = "key_album_id_history";

    private static final String KEY_RESULT_ALBUM_HISTORY = "key_result_album_history";

    @Override
    public String actionName() {
        return ACTION_QUERY;
    }

    @Override
    public boolean onAction(RealCaller caller) {
        String albumId = caller.getParamValue(KEY_ALBUM_ID);
        Log.i("QueryHistoryProcessor", "onAction: "+albumId);
        if (!TextUtils.isEmpty(albumId)) {
            HistoryManager.getInstance().queryLatestHistoryByAlbumId(albumId,
                    historyItem -> ComponentClient.sendResult(caller.getCallId(), ComponentResult.success(KEY_RESULT_ALBUM_HISTORY, historyItem)));
        }
        return true;
    }
}
