package com.kaolafm.kradio.service;

import android.app.PendingIntent;
import android.app.Service;
import android.appwidget.AppWidgetManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.os.IBinder;
import android.os.SystemProperties;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.RemoteViews;
import android.widget.Toast;

import com.kaolafm.auto.appwidget.KLAppWidget;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.byd.BydToast;
import com.kaolafm.kradio.common.CP;
import com.kaolafm.kradio.common.ErrorInfo;
import com.kaolafm.kradio.common.ResultCallback;
import com.kaolafm.kradio.common.SubscribeData;
import com.kaolafm.kradio.common.bean.HistoryItem;
import com.kaolafm.kradio.common.http.GeneralCallback;
import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.flavor.utils.PlayerUtil;
import com.kaolafm.kradio.basedb.manager.HistoryDaoManager;
import com.kaolafm.kradio.k_kaolafm.home.player.PlayerHelper;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.IntentUtils;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.lib.utils.UrlUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.report.ReportManager;
import com.kaolafm.kradio.subscribe.SubscribeChangeListener;
import com.kaolafm.kradio.subscribe.SubscribeManager;
import com.kaolafm.kradio.subscribe.SubscribeModel;
import com.kaolafm.kradio.subscribe.SubscribeRepository;
import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.report.event.PlayerUiControlReportEvent;
import com.kaolafm.utils.BitmapUtils;

import java.util.List;

import cmgyunting.vehicleplayer.cnr.WidgetUtils;
import io.reactivex.Observable;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;

/**
 * <AUTHOR>
 */
public class HanWidgetService extends Service {

    public static final String WIDGET_ACTION_NEXT = "kl.action.next";
    public static final String WIDGET_ACTION_PREV = "kl.action.prev";
    public static final String WIDGET_ACTION_PAUSE = "kl.action.pause";
    public static final String WIDGET_ACTION_PLAY = "kl.action.play";
    public static final String WIDGET_ACTION_REFRESH = "kl.action.refresh";
    public static final String WIDGET_ACTION_REFRESH_DEFAULT = "kl.action.refresh.default";
    public static final String WIDGET_ACTION_EXIT = "kl.action.exit";
    public static final String WIDGET_ACTION_COLLECTION = "kl.action.collection";
    public static final String WIDGET_ACTION_CHANGE_THEME = "kl.action.change.theme";
    public static final String EXTRA_IS_SPORT = "extra_is_sport";
    /**
     * 比亚迪widget eco模式
     */
    public static final int BYD_THEME_ECO = 1;
    /**
     * 比亚迪widget sport模式
     */
    public static final int BYD_THEME_SPORT = 2;

    private Context context = AppDelegate.getInstance().getContext();

    private static final String TAG = "k.byd.hws";
    //专辑图片
    private Bitmap mCurrentBitmap;

    private Bitmap mBlurBitmap;

    private Bitmap mBlurBitmapByLand;

    private Bitmap mBlurBitmapByPort;

    private boolean subscription = false;

    private String livingTime = "";

    private int landW;
    private int landH;
    private int portW;
    private int portH;

    private SubscribeModel subscribeModel;

    private boolean isPortrait = true;

    private boolean isPauseStatus = false;
    private AppWidgetManager appWidgetManager;
    private ComponentName componentName;

    private ProgressView progressView;

    @Override
    public void onCreate() {
        Log.i(TAG, "onCreate: ");
        super.onCreate();

        progressView = new ProgressView(this);
        appWidgetManager = AppWidgetManager.getInstance(this);
        componentName = new ComponentName(this, KLAppWidget.class);

        mThemeIsSport = getWidgetTheme();
        setWidgetTheme(mThemeIsSport);

        boolean init = PlayerManager.getInstance().isPlayerInitSuccess();
        Log.i(TAG, "onCreate init:" + init);
        if (!init) {
            PlayerManager.getInstance().addPlayerInitComplete(new IPlayerInitCompleteListener() {
                @Override
                public void onPlayerInitComplete(boolean b) {
                    if (b) {
                        PlayerManager.getInstance().removePlayerInitComplete(this);
                        init();
                    }
                }
            });
        } else {
            init();
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.i(TAG, "onStartCommand: ");
        Log.i(TAG, "onHandleIntent: intent=" + intent);
        executeActionCommand(intent);
        return START_NOT_STICKY;
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onDestroy() {
        Log.i(TAG, "hws:onDestroy: ");
        super.onDestroy();
        BydToast.release();
        mCurrentBitmap = null;
        PlayerManager.getInstance().removePlayControlStateCallback(playStateListener);
//        BroadcastRadioListManager.getInstance().removeOnBroadcastRadioLivingListener(this);
        ((SubscribeRepository) SubscribeManager.getInstance(context, CP.KaoLaFM)).removeSubscribeChangeListener(subscribeChangeListener);
    }


    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            mBlurBitmap = mBlurBitmapByLand;
            isPortrait = false;
        } else {
            mBlurBitmap = mBlurBitmapByPort;
            isPortrait = true;
        }
        setRemoteViews(PlayerManager.getInstance().getCurPlayItem());
        super.onConfigurationChanged(newConfig);
    }

    private void init() {
        subscribeModel = new SubscribeModel();
        PlayerManager.getInstance().init(AppDelegate.getInstance().getContext());
        PlayerManager.getInstance().addPlayControlStateCallback(playStateListener);
//        PlayerManager.getInstance().addIPlayChangedListener(this);
//        AudioStatusManager.getInstance().registerOnAudioFocusChangeListener(this);
        ((SubscribeRepository) SubscribeManager.getInstance(context, CP.KaoLaFM)).addSubscribeChangeListener(subscribeChangeListener);
//        BroadcastRadioListManager.getInstance().addOnBroadcastRadioLivingListener(this);
        initWH();
        initPlayInfo();
        if (ResUtil.getOrientation() == Configuration.ORIENTATION_LANDSCAPE) {
            isPortrait = false;
        }
    }

    private void initWH() {
        landW = ScreenUtil.dp2px(312);
        landH = ScreenUtil.dp2px(248);
        portW = ScreenUtil.dp2px(540);
        portH = ScreenUtil.dp2px(200);
    }

    private void initPlayInfo() {
        PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
        if (playItem == null) {
            HistoryDaoManager.getInstance().queryHistoryListOrderByTime(1, historyItems -> {
                if (!ListUtil.isEmpty(historyItems)) {
                    HistoryItem historyItem = historyItems.get(0);
                    if (historyItem != null) {
                        //todo
//                        PlayItem historyPlayItem = HistoryUtils.turnPlayItem(historyItem);
//                        updateBitmap(historyPlayItem);
                    }
                }
            });
        } else {
            updateBitmap(playItem);
        }
    }

//    @Override
//    public void onIdle(PlayItem playItem) {
//        Log.i(TAG, "onIdle");
//        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001652540031?userId=1229522问题
//        isPauseStatus = true;
//        setRemoteViews(playItem);
//    }
//
//    @Override
//    public void onPlayerPreparing(final PlayItem playItem) {
//        Log.i(TAG, "onPlayerPreparing");
//        if (playItem == null) {
//            Log.i(TAG, "playitem null");
//            return;
//        }
//        updateBitmap(playItem);
//    }
//
//    @Override
//    public void onPlayerPlaying(final PlayItem playItem) {
//        Log.i(TAG, "onPlayerPlaying");
//        isPauseStatus = false;
//        setRemoteViews(playItem);
//    }
//
//    @Override
//    public void onPlayerPaused(PlayItem playItem) {
//        Log.i(TAG, "onPlayerPaused");
//        isPauseStatus = true;
//        setRemoteViews(playItem);
//    }
//
//    @Override
//    public void onProgress(String s, int i, int i1, boolean b) {
//        Log.i(TAG, "onProgress isRefresh:");
//        PlayItem playItem = getCurrentPlayItem();
//        if (playItem != null && !playItem.isLivingUrl()) {
//            setRemoteViews(playItem);
//        }
//    }
//
//    @Override
//    public void onPlayerFailed(PlayItem playItem, int what, int extra) {
//        Log.i(TAG, "onPlayerFailed");
//        isPauseStatus = true;
//        setRemoteViews(playItem);
//    }
//
//    @Override
//    public void onPlayerEnd(PlayItem playItem) {
//        Log.i(TAG, "onPlayerEnd");
//        isPauseStatus = true;
//        setRemoteViews(playItem);
//    }
//
//    @Override
//    public void onSeekStart(String s) {
//        Log.i(TAG, "onSeekStart");
//    }
//
//    @Override
//    public void onSeekComplete(String s) {
//        Log.i(TAG, "onSeekComplete");
//    }
//
//    @Override
//    public void onBufferingStart(PlayItem playItem) {
//        Log.i(TAG, "onBufferingStart");
//    }
//
//    @Override
//    public void onBufferingEnd(PlayItem playItem) {
//        Log.i(TAG, "onBufferingEnd");
//    }

//    private String getPic(PlayItem playItem, String type) {
//        String url = playItem.getPicUrl();
//
//        if (TextUtils.isEmpty(url)) {
//            PlayerRadioListItem curPlayerRadioListItem = PlayerRadioListManager.getInstance().getCurRadioItem();
//            if (curPlayerRadioListItem != null && !TextUtils.isEmpty(curPlayerRadioListItem.getPicUrl())) {
//                url = curPlayerRadioListItem.getPicUrl();
//            }
//        }
//
//        String picUrl = UrlUtil.getCustomPicUrl(type, url);
//
//        return picUrl;
//    }

    private void executeActionCommand(Intent intent) {
        if (intent == null) {
            return;
        }
        String action = intent.getAction();
        if (TextUtils.isEmpty(action)) {
            return;
        }

        Log.i(TAG, "executeActionCommand:" + action);

        switch (action) {
            case WIDGET_ACTION_NEXT:
                if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
                    BydToast.show(context, "无网络", Toast.LENGTH_SHORT);
                    return;
                }

//                if (TempTaskPlayerManager.getInstance().isPlayerEnable()) {
//                    PlayerManager.getInstance().playStart();
//                    return;
//                }
                if (PlayerManager.getInstance().getCurPlayItem() == null) {
                    PlayerUtil.playNetOrLocal();
                } else {
                    boolean hasNext = PlayerManager.getInstance().hasNext();
                    if (hasNext) {
                        WidgetUtils.tryPlayNext(this);
                    } else {
                        BydToast.show(context, "已经是最后一首了", Toast.LENGTH_SHORT);
                    }
                }
                ReportManager.getInstance().addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_NEXT, PlayerUiControlReportEvent.CONTROL_TYPE_CLICK, PlayerUiControlReportEvent.POSITION_WIDGET);
                break;
            case WIDGET_ACTION_PREV:
                if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
                    BydToast.show(context, "无网络", Toast.LENGTH_SHORT);
                    return;
                }

                if (PlayerManager.getInstance().getCurPlayItem() == null) {
                    PlayerUtil.playNetOrLocal();
                } else {
                    boolean hasPre = PlayerManager.getInstance().hasPre();
                    if (hasPre) {
                        PlayerManager.getInstance().playPre();
                    } else {
                        BydToast.show(context, "已经是第一首了", Toast.LENGTH_SHORT);
                    }
                }
                ReportManager.getInstance().addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_PREVIOUS, PlayerUiControlReportEvent.CONTROL_TYPE_CLICK, PlayerUiControlReportEvent.POSITION_WIDGET);

                break;
            case WIDGET_ACTION_PLAY:
                if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
                    BydToast.show(context, "无网络", Toast.LENGTH_SHORT);
                    return;
                }
                if (PlayerManager.getInstance().getCurPlayItem() == null) {
                    PlayerUtil.playNetOrLocal();
                } else {
                    PlayerManager.getInstance().switchPlayerStatus(true);
                }
                ReportManager.getInstance().addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_PLAY, PlayerUiControlReportEvent.CONTROL_TYPE_CLICK, PlayerUiControlReportEvent.POSITION_WIDGET);
                break;
            case WIDGET_ACTION_PAUSE:
//                if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
//                    BydToast.show(context, "无网络", Toast.LENGTH_SHORT);
//                    return;
//                }
                PlayerManager.getInstance().pause(true);
                ReportManager.getInstance().addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_PAUSE, PlayerUiControlReportEvent.CONTROL_TYPE_CLICK, PlayerUiControlReportEvent.POSITION_WIDGET);
                break;
            case WIDGET_ACTION_COLLECTION:
                if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
                    BydToast.show(context, "无网络", Toast.LENGTH_SHORT);
                    return;
                }
                widgetSubscribe(PlayerHelper.getSubscribeId());
                break;
            case WIDGET_ACTION_REFRESH:
                // 解决https://app.huoban.com/tables/2100000007530121/items/2300001686584800?userId=1545533问题
                mThemeIsSport = getWidgetTheme();
//                mThemeIsSport = intent.getBooleanExtra(EXTRA_IS_SPORT, false);
//                Log.d(TAG, "executeActionCommand: refresh ,mThemeIsSport=" + mThemeIsSport + ",sdkInit:" + KRadioApplication.SDKInit);
//                if (!PlayerManager.SDKInit) {
                // TODO: 4/2/21
                //如果没初始化sdk就设置默认点击view。如果走else 用到了播放器的东西，没初始化会崩溃
//                    setDefaultRemoteViews();
//                } else {
                setRemoteViews(PlayerManager.getInstance().getCurPlayItem());
//                }
                break;
            case WIDGET_ACTION_CHANGE_THEME:
                mThemeIsSport = intent.getBooleanExtra(EXTRA_IS_SPORT, false);
                Log.i(TAG, "executeActionCommand: CHANGE_THEME ,mThemeIsSport=" + mThemeIsSport);
                setRemoteViews(PlayerManager.getInstance().getCurPlayItem());
                break;
            case WIDGET_ACTION_EXIT:
                isPauseStatus = false;
                setRemoteViews(PlayerManager.getInstance().getCurPlayItem());
                break;
            case WIDGET_ACTION_REFRESH_DEFAULT:
                setDefaultRemoteViews();
                break;
        }
    }

    public void onSubscribesChanged(List<SubscribeData> subscribes) {
        subscription = false;
        if (!ListUtil.isEmpty(subscribes)) {
            for (SubscribeData s : subscribes) {
                if (s.getId() == PlayerHelper.getSubscribeId()) {
                    subscription = true;
                    break;
                }
            }
        }
        setRemoteViews(PlayerManager.getInstance().getCurPlayItem());
    }

    public void onPlayChangeChanged(PlayItem playItem) {
        setRemoteViews(playItem);
        setSubscribeState();
    }

    SubscribeChangeListener subscribeChangeListener = subscribes -> {
        subscription = false;
        if (!ListUtil.isEmpty(subscribes)) {
            for (SubscribeData s : subscribes) {
                if (s.getId() == PlayerHelper.getSubscribeId()) {
                    subscription = true;
                    break;
                }
            }
        }
        setRemoteViews(PlayerManager.getInstance().getCurPlayItem());
    };

    public void onAudioFocusChange(int i) {
        Log.i(TAG, "onAudioFocusChange: i = " + i);
//        if (i >= AudioManager.AUDIOFOCUS_GAIN) {
//            isPauseStatus = true;
//        } else {
//            isPauseStatus = false;
//        }
    }

//    private static class MyGeneralCallback implements GeneralCallback {
//        private String actionType;
//        private Context context;
//
//        public MyGeneralCallback(Context context, String actionType) {
//            this.context = context;
//            this.actionType = actionType;
//        }
//
//        @Override
//        public void onResult(Object o) {
////            PlayerManager.getInstance().removeGetContentListener(this);
////            if (WIDGET_ACTION_PREV.equals(actionType)) {
////                KLAutoPlayerManager.getInstance(context).playPre();
////            } else {
////                KLAutoPlayerManager.getInstance(context).playNext();
////            }
//        }
//
//        @Override
//        public void onError(int i) {
//            PlayerManager.getInstance().removeGetContentListener(this);
//        }
//
//        @Override
//        public void onException(Throwable throwable) {
//            PlayerManager.getInstance().removeGetContentListener(this);
//        }
//    }


    private void playHistoryOrDefault(final String actionType) {
//        HistoryItem history = getRecentlyItem();
//        boolean isPlayNextOrPre = WIDGET_ACTION_NEXT.equals(actionType) || WIDGET_ACTION_PREV.equals(actionType);
//        if (WIDGET_ACTION_PLAY.equals(actionType)) {
//
//        } else {
//            if (history != null && !Constants.RESOURCES_TYPE_BROADCAST.equals(history.getType())) {
//                if (isPlayNextOrPre) {
//                    PlayerManager.getInstance().addGetContentListener(new MyGeneralCallback(getApplicationContext(), actionType));
//                }
//            }
//        }
//        if (history != null) {
//            KLAutoPlayerManager.getInstance(getApplicationContext()).playHistory(history, !isPlayNextOrPre);
//        } else {
//            KLAutoPlayerManager.getInstance(getApplicationContext()).playPgc(Constants.DEFAULT_PGC, !isPlayNextOrPre);
//        }
    }

//    @Override
//    public void onLivingCountDown(String s) {
//        PlayItem playItem = PlayerManager.getInstance().getCurrentPlayItem();
//        livingTime = s;
//        if (playItem != null && playItem.isLivingUrl()) {
//            setRemoteViews(playItem);
//        }
//    }

    private void updateBitmap(PlayItem playItem) {
        String pic = playItem.getPicUrl();
        Log.i(TAG, "updateBitmap:" + pic);
        if (TextUtils.isEmpty(pic)) {
            setRemoteViews(playItem);
        } else {
            if (mCurrentBitmap != null && !mCurrentBitmap.isRecycled()) {
                mCurrentBitmap.recycle();
                mCurrentBitmap = null;
            }
            ImageLoader.getInstance().getBitmapFromCache(context, pic, bitmap -> {
                PlayItem curPlayItem = PlayerManager.getInstance().getCurPlayItem();
                if (curPlayItem != null && curPlayItem.getAudioId() == playItem.getAudioId()) {
                    mCurrentBitmap = bitmap;
                    setRemoteViews(playItem);
                } else if (curPlayItem == null) {
                    mCurrentBitmap = bitmap;
                    setRemoteViews(playItem);
                }

            });
        }

        updateblurBitmap(playItem);
    }

    private void updateblurBitmap(PlayItem playItem) {
        String pic = playItem.getPicUrl();
        ImageLoader.getInstance().getBlurBitmapFromCache(context, pic, 20, bitmap -> {
            Observable.create((ObservableOnSubscribe<Boolean>) emitter -> {

                Bitmap portB = BitmapUtils.createScaledBitmapRGB565(bitmap, portW, portH, BitmapUtils.ScalingLogic.CROP);
                Bitmap landB = BitmapUtils.createScaledBitmapRGB565(bitmap, landW, landH, BitmapUtils.ScalingLogic.CROP);

                mBlurBitmapByPort = BitmapUtils.setBitmapRoundSize(portB, portW, portH, ScreenUtil.dp2px(10));
                mBlurBitmapByLand = BitmapUtils.setBitmapRoundSize(landB, landW, landH, ScreenUtil.dp2px(10));

                if (ResUtil.getOrientation() == Configuration.ORIENTATION_LANDSCAPE) {
                    mBlurBitmap = mBlurBitmapByLand;
                } else {
                    mBlurBitmap = mBlurBitmapByPort;
                }
                emitter.onNext(true);
            }).subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(b -> setRemoteViews(playItem));
        });
    }

    private Bitmap getBitmap(Context context) {
        PackageManager packageManager = null;
        ApplicationInfo applicationInfo;
        try {
            packageManager = context.getApplicationContext()
                    .getPackageManager();
            applicationInfo = packageManager.getApplicationInfo(
                    context.getPackageName(), 0);
        } catch (PackageManager.NameNotFoundException e) {
            applicationInfo = null;
        }
        Drawable d = packageManager.getApplicationIcon(applicationInfo); //xxx根据自己的情况获取drawable
        BitmapDrawable bd = (BitmapDrawable) d;
        Bitmap bm = bd.getBitmap();
        return bm;
    }

    public void setRemoteViews(PlayItem playItem) {
        Log.i(TAG, "setRemoteViews :playItem=" + playItem);
        setWidgetTheme(mThemeIsSport);
        RemoteViews views = new RemoteViews(context.getPackageName(), mLayoutRid);
        //设置播放暂停按钮
        if (PlayerManager.getInstance().isPlaying()) {
            showPlayBtn(views);
        } else {
            showPauseBtn(views);
        }
        //设置点击封面进入应用
        Intent launchAppIntent = new IntentUtils().getLauncherIntentUseWidget(context);
        views.setOnClickPendingIntent(R.id.widget_facade, PendingIntent.getActivity(context, 0, launchAppIntent, 0));
        //设置上一首
        Intent intent = new Intent(context, HanWidgetService.class);
        intent.setAction(WIDGET_ACTION_PREV);
        views.setOnClickPendingIntent(R.id.widget_prev, PendingIntent.getService(context, 0, intent, 0));
        //设置下一首
        Intent intent1 = new Intent(context, HanWidgetService.class);
        intent1.setAction(WIDGET_ACTION_NEXT);
        views.setOnClickPendingIntent(R.id.widget_next, PendingIntent.getService(context, 0, intent1, 0));
//        //设置收藏
//        Intent collection = new Intent(context, HanWidgetService.class);
//        collection.setAction(WIDGET_ACTION_COLLECTION);
//        views.setOnClickPendingIntent(R.id.widget_collection, PendingIntent.getService(context, 0, collection, 0));
        Bitmap bitmap = getBitmap(context);
        if (bitmap != null) {
            views.setImageViewBitmap(R.id.title_tb_logo_imageView, bitmap);
        }

        if (playItem != null) {
            updateByPlayItem(playItem, views);
            Log.i(TAG, "setRemoteViews: <1>[playItem != null] ");
            appWidgetManager.updateAppWidget(componentName, views);
        } else {
            HistoryDaoManager.getInstance().queryHistoryListOrderByTime(1, historyItems -> {
                if (!ListUtil.isEmpty(historyItems)) {
                    HistoryItem historyItem = historyItems.get(0);
                    // TODO: 4/2/21
//                    PlayItem historyPlayItem = HistoryUtils.turnPlayItem(historyItem);
//                    updateByPlayItem(historyPlayItem, views);
                }
                Log.i(TAG, "setRemoteViews: <2>[playItem == null] ");
                appWidgetManager.updateAppWidget(componentName, views);
            });
        }
    }

    private void setDefaultRemoteViews() {
        Log.d(TAG, "setDefaultRemoteViews");
        setWidgetTheme(mThemeIsSport);
        RemoteViews views = new RemoteViews(context.getPackageName(), mLayoutRid);
        //设置暂停按钮
        showPauseBtn(views);

        //设置点击封面进入应用
        Intent launchAppIntent = new IntentUtils().getLauncherIntentUseWidget(context);
        views.setOnClickPendingIntent(R.id.widget_facade, PendingIntent.getActivity(context, 0, launchAppIntent, 0));
        appWidgetManager.updateAppWidget(componentName, views);
    }


    private void updateByPlayItem(PlayItem playItem, RemoteViews views) {

        boolean isPlaying = PlayerManager.getInstance().isPlaying();
        Log.i(TAG, "updateByPlayItem isPlaying = " + isPlaying);
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001665869713?userId=1545533问题
        views.setViewVisibility(R.id.iv_play_status, isPlaying ? View.VISIBLE : View.GONE);
        if (playItem != null) {
            Log.i(TAG, "updateByPlayItem：" + playItem.getTitle());
            if (mCurrentBitmap != null && !mCurrentBitmap.isRecycled()) {
                //设置封面图片
                views.setImageViewBitmap(R.id.widget_cover, mCurrentBitmap);
            }
//            //设置高斯模糊图片
//            views.setImageViewBitmap(R.id.widget_blur_imageview, mBlurBitmap);
//
//            views.setViewVisibility(R.id.widget_blur_bg, View.VISIBLE);
//
//            //设置碎片名称
//            CharSequence widgetText = PlayItemUtils.getPlayItemAudioName(playItem);
//            if (!TextUtils.isEmpty(widgetText)) {
//                views.setTextViewText(R.id.widget_audio_name, widgetText);
//            } else {
//                views.setTextViewText(R.id.widget_audio_name, "暂无节目信息");
//            }
//            //设置专辑名称
//            CharSequence albumName = PlayItemUtils.getPlayItemAlbumName(playItem);
//            if (!TextUtils.isEmpty(albumName)) {
//                views.setTextViewText(R.id.widget_album_name, albumName);
//            }
//
            if (playItem.isLiving()) {
//                if (playItem.getFinishTime() <= 0) {
//                    views.setViewVisibility(R.id.widget_duration, View.INVISIBLE);
//                    views.setViewVisibility(R.id.widget_cur_time, View.INVISIBLE);
//                views.setProgressBar(R.id.widget_progressBar, 0, 0, false);
//                } else {
//                    views.setViewVisibility(R.id.widget_duration, View.VISIBLE);
//                    views.setViewVisibility(R.id.widget_cur_time, View.VISIBLE);
//                    views.setTextViewText(R.id.widget_duration, DateFormatUtil.getCurrDate(playItem.getFinishTime()));
//                views.setProgressBar(R.id.widget_progressBar, 0, 0, false);
//                views.setProgressBar(R.id.widget_progressBar, 0, 0, false);
                setProgressView(views, R.id.widget_progressBar, 0);
//                    views.setTextViewText(R.id.widget_cur_time, livingTime + " / ");
//                }
            } else {
//                views.setViewVisibility(R.id.widget_duration, View.VISIBLE);
//                views.setViewVisibility(R.id.widget_cur_time, View.VISIBLE);
                int duration = playItem.getDuration();
                int position = playItem.getPosition();
//                views.setViewVisibility(R.id.iv_play_status, KLAutoPlayerManager.getInstance().isPlaying() ? View.VISIBLE : View.GONE);
                Log.i(TAG, "updateByPlayItem----------------->position = " + playItem.getPosition() + "---->duration = " + duration);
//                views.setProgressBar(R.id.widget_progressBar, duration, position, false);
                setProgressView(views, R.id.widget_progressBar, (float) position / duration);
//                views.setTextViewText(R.id.widget_duration, DateFormatUtil.getDescriptiveTime(duration));
//                views.setTextViewText(R.id.widget_cur_time, DateFormatUtil.getDescriptiveTime(playItem.getPosition()) + " / ");
            }
        } else {
//            views.setViewVisibility(R.id.widget_blur_bg, View.GONE);
        }
//        //设置收藏状态
//        if (subscription) {
//            views.setImageViewResource(R.id.widget_collection, R.drawable.selector_widget_btn_collection);
//        } else {
//            views.setImageViewResource(R.id.widget_collection, R.drawable.selector_widget_btn_uncollection);
//        }

        //设置直播中小标
        updateBroadcastLabel(views);

//        if (mCurrentBitmap != null) {
//            mCurrentBitmap.recycle();
//            mCurrentBitmap = null;
//        }
    }


    private void setProgressView(RemoteViews views, int appwidget_iv, float progress) {
//        if ("eco".equals(SystemProperties.get("persist.sys.ecosport"))) {
//            //当前为经济模式
//            Log.i(TAG, "onAppWidgetOptionsChanged: 当前为[经济]模式.");
//            //paint.setColor(Color.parseColor("#00DCFF"));
//            progressView.setTheme(this, false);
//        } else if ("sport".equals(SystemProperties.get("persist.sys.ecosport"))) {
//            //当前为运动模式
//            Log.i(TAG, "onAppWidgetOptionsChanged: 当前为[运动]模式.");
//            //paint.setColor(Color.RED);
//            progressView.setTheme(this, true);
//        }

        progressView.drawToRemoteViews(views, appwidget_iv, progress);
        views.setViewVisibility(appwidget_iv, View.VISIBLE);
        appWidgetManager.updateAppWidget(componentName, views);

    }

    private void updateBroadcastLabel(RemoteViews remoteViews) {
//        if (remoteViews == null) {
//            return;
//        }
//        PlayItem playItem = KLAutoPlayerManager.getInstance().getCurrentPlayItem();
//        if (playItem == null) {
//            return;
//        }
//
//        int whichPlayer = ComprehensivePlayerHelper.whichPlayer();
//
//        if (whichPlayer == ComprehensivePlayerHelper.PLAYER_TYPE_BROADCAST) {
//            remoteViews.setViewVisibility(R.id.widget_broadcast_label, View.VISIBLE);
//            remoteViews.setViewVisibility(R.id.widget_broadcast_label_textview, View.VISIBLE);
//            switch (playItem.getStatus()) {
//                //直播
//                case BroadcastStatus.BROADCAST_STATUS_LIVING:
//                    remoteViews.setImageViewResource(R.id.widget_broadcast_label, R.drawable.icon_player_living);
//                    remoteViews.setTextViewText(R.id.widget_broadcast_label_textview, "直播中");
//                    break;
//                //回放
//                case BroadcastStatus.BROADCAST_STATUS_PLAYBACK:
//                    remoteViews.setImageViewResource(R.id.widget_broadcast_label, R.drawable.icon_player_playback);
//                    remoteViews.setTextViewText(R.id.widget_broadcast_label_textview, "回听");
//                    break;
//            }
//        } else {
//            remoteViews.setViewVisibility(R.id.widget_broadcast_label, View.GONE);
//            remoteViews.setViewVisibility(R.id.widget_broadcast_label_textview, View.GONE);
//        }
    }

    private void showPlayBtn(RemoteViews views) {
        views.setImageViewResource(R.id.widget_play_or_pause, R.drawable.selector_widget_btn_play);
        Intent intent = new Intent(context, HanWidgetService.class);
        intent.setAction(WIDGET_ACTION_PAUSE);
        views.setOnClickPendingIntent(R.id.widget_play_or_pause, PendingIntent.getService(context, 0, intent, 0));
    }

    private void showPauseBtn(RemoteViews views) {
        views.setImageViewResource(R.id.widget_play_or_pause, R.drawable.selector_widget_btn_pause);
        Intent intent = new Intent(context, HanWidgetService.class);
        intent.setAction(WIDGET_ACTION_PLAY);
        views.setOnClickPendingIntent(R.id.widget_play_or_pause, PendingIntent.getService(context, 0, intent, 0));
    }

//    private PlayItem getCurrentPlayItem() {
//        PlayItem curPlayItem = null;
//        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001687054950?userId=1229522问题
////        try {
//
//        if (TempTaskPlayerManager.getInstance().isPlayerEnable()) {
//            curPlayItem = TempTaskPlayerManager.getInstance().getCurrentPlayItem();
//            return curPlayItem;
//        }
//
//        int whichPlayer = ComprehensivePlayerHelper.whichPlayer();
//
//        if (whichPlayer == ComprehensivePlayerHelper.PLAYER_TYPE_LIVE) {
//            curPlayItem = LiveBroadcastPlayerManager.getInstance().getPlayItem();
//        } else {
//            curPlayItem = KLAutoPlayerManager.getInstance().getCurrentPlayItem();
//        }
////        } catch (Throwable t) {
////        }
//        return curPlayItem;
//    }

    public void widgetSubscribe(final long id) {
        if (subscribeModel == null) {
            return;
        }
        subscribeModel.isSubscribed(String.valueOf(id), new ResultCallback() {
            @Override
            public void onResult(boolean result, int code) {
                if (result) {
                    unSubscribe(id);
                } else {
                    subscribe(id);
                }
            }

            @Override
            public void onFailure(ErrorInfo errorInfo) {

            }
        });
    }

    private void subscribe(long id) {
        if (subscribeModel == null) {
            return;
        }
        SubscribeData subscribeData = new SubscribeData();
        subscribeData.setId(id);
        subscribeModel.subscribe(subscribeData, new ResultCallback() {
            @Override
            public void onResult(boolean result, int code) {
                subscription = result;
                setRemoteViews(PlayerManager.getInstance().getCurPlayItem());
            }

            @Override
            public void onFailure(ErrorInfo errorInfo) {

            }
        });
    }

    private void unSubscribe(long id) {
        if (subscribeModel == null) {
            return;
        }
        SubscribeData sd = new SubscribeData();
        sd.setId(id);
        subscribeModel.unsubscribe(sd, new ResultCallback() {
            @Override
            public void onResult(boolean result, int code) {
                if (result) {
                    subscription = false;
                }
                setRemoteViews(PlayerManager.getInstance().getCurPlayItem());
            }

            @Override
            public void onFailure(ErrorInfo errorInfo) {

            }
        });
    }

    private void setSubscribeState() {
        if (subscribeModel == null) {
            return;
        }
        subscribeModel.isSubscribed(String.valueOf(PlayerHelper.getSubscribeId()), new ResultCallback() {
            @Override
            public void onResult(boolean result, int code) {
                subscription = result;
                setRemoteViews(PlayerManager.getInstance().getCurPlayItem());
            }

            @Override
            public void onFailure(ErrorInfo errorInfo) {

            }
        });
    }

    private boolean mThemeIsSport = false;

    /**
     * 返回widget remoteviews layoutid
     * （比亚迪有两种模式，不同的模式有不同的布局。 目前默认的布局为 widget_layout_sport）
     *
     * @return
     */
    private boolean getWidgetTheme() {
        boolean isSport = false;

        if ("eco".equals(SystemProperties.get("persist.sys.ecosport"))) {
            //当前为经济模式
            Log.i(TAG, "onAppWidgetOptionsChanged: 当前为[经济]模式.");
            isSport = false;
        } else if ("sport".equals(SystemProperties.get("persist.sys.ecosport"))) {
            //当前为运动模式
            Log.i(TAG, "onAppWidgetOptionsChanged: 当前为[运动]模式.");
            isSport = true;
        } else {
            Log.i(TAG, "onAppWidgetOptionsChanged: 当前为[空]模式,默认使用[经济]模式.");
            Log.i(TAG, "                         : 当前为:" + SystemProperties.get("persist.sys.ecosport"));
        }

        return isSport;
    }

    private int mLayoutRid;

    private void setWidgetTheme(boolean isSport) {
        Log.i(TAG, "setWidgetTheme: theme=" + (isSport ? "sport" : "eco"));
        mLayoutRid = isSport ? R.layout.widget_layout_han_sport : R.layout.widget_layout_han_eco;
        progressView.setTheme(this, isSport);
    }

    BasePlayStateListener playStateListener = new BasePlayStateListener() {
        @Override
        public void onPlayerPreparing(final PlayItem playItem) {
            Log.i(TAG, "onPlayerPreparing");
            if (playItem == null) {
                Log.i(TAG, "playitem null");
                return;
            }
            setRemoteViews(playItem);
            setSubscribeState();
            updateBitmap(playItem);
        }

        @Override
        public void onPlayerPlaying(final PlayItem playItem) {
            Log.i(TAG, "onPlayerPlaying");
            setRemoteViews(playItem);
        }

        @Override
        public void onPlayerPaused(PlayItem playItem) {
            Log.i(TAG, "onPlayerPaused");
            setRemoteViews(playItem);
        }

        @Override
        public void onProgress(PlayItem playItem, long progress, long l) {
            Log.i(TAG, "onProgress isRefresh:");
            if (!playItem.isLiving()) {
                setRemoteViews(playItem);
            }
        }
    };

}
