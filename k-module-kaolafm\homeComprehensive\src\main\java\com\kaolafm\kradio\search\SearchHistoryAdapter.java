package com.kaolafm.kradio.search;

import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.flavor.KRadioSearchInter;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.util.ReportConstants;


public class SearchHistoryAdapter extends BaseAdapter<String> {

    private String pageId = Constants.PAGE_ID_SEARCH;
    private String mButtonId;

    public SearchHistoryAdapter(String mButtonId) {
        this.mButtonId = mButtonId;
    }

    @Override
    protected BaseHolder<String> getViewHolder(ViewGroup parent, int viewType) {
        return new SearchHistoryHolder(inflate(parent, R.layout.item_search_history, viewType));
    }

    public class SearchHistoryHolder extends BaseHolder<String> {
        TextView mTvSearchHistoryTag;
        View mRootView;

        public SearchHistoryHolder(View itemView) {
            super(itemView);
            mTvSearchHistoryTag=itemView.findViewById(R.id.tv_search_history_tag);
            mRootView=itemView.findViewById(R.id.root_view);
            KRadioSearchInter kRadioSearchInter = ClazzImplUtil.getInter("KRadioSearchImpl");
            if (kRadioSearchInter != null) {
                kRadioSearchInter.configSearchHisItem(mTvSearchHistoryTag);
            }
        }

        @Override
        public void setupData(String s, int position) {
            mTvSearchHistoryTag.setText(s);
            mRootView.setContentDescription(s);
        }
    }

    public void setPageId(String pageId) {
        this.pageId = pageId;
    }

    public String getPageId() {
        return pageId;
    }

    @Override
    public void onViewAttachedToWindow(BaseHolder<String> holder) {
        super.onViewAttachedToWindow(holder);
        if (holder instanceof SearchHistoryHolder) {
            ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, mButtonId, ((SearchHistoryHolder) holder).mTvSearchHistoryTag.getText().toString(), getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
        }
    }
}
