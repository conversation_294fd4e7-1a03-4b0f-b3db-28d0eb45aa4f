package com.kaolafm.kradio.flavor.impl;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.Log;

import com.kaolafm.kradio.flavor.common.SystemBootUtil;
import com.kaolafm.kradio.flavor.utils.PlayerUtil;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioPlayLogicInter;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.player.core.listener.OnPlayLogicListener;
import com.kaolafm.opensdk.player.core.utils.PlayerCustomizeManager;
import com.kaolafm.opensdk.player.logic.PlayerManager;


/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-07-05 12:16
 ******************************************/
public final class KRadioAudioPlayLogicImpl implements KRadioAudioPlayLogicInter {
    private final static String TAG = "KRadioAudioPlayLogicImpl";

    public KRadioAudioPlayLogicImpl() {
        PlayerCustomizeManager.getInstance().setPlayLogicListener(new OnPlayLogicListener() {
            @SuppressLint("LongLogTag")
            @Override
            public boolean onPlayLogicDispose() {
                int currentFocus = PlayerManager.getInstance().getCurrentAudioFocusStatus();
                Log.i(TAG, "isAppOnForeground--------->currentFocus = " + currentFocus);
                if ((currentFocus < 0)) {
                    boolean isAppOnForeground = AppDelegate.getInstance().isAppForeground();
                    Log.i(TAG, "isAppOnForeground--------->isAppOnForeground = " + isAppOnForeground);
                    return !isAppOnForeground;
                }
                return false;
            }
        });
    }

    @SuppressLint("LongLogTag")
    @Override
    public boolean autoPlayAudio(Object... args) {
        Context context = (Context) args[0];
        SystemBootUtil systemBootUtil = new SystemBootUtil();
        boolean flag = systemBootUtil.isFirstBoot(context);
        Log.i(TAG, "autoPlayAudio:   flag = " + flag);
        if (flag) {
            PlayerUtil.playDefaultMediaForChannel();
            systemBootUtil.updateFirstBoot(context, false);
            return true;
        }
        return false;
    }

    @Override
    public boolean requestAudioFocus(Object... args) {
        boolean flag = PlayerManager.getInstance().requestAudioFocus();
        return flag;
    }

    @Override
    public boolean resumeAudioPlayLogic(Object... args) {
        return false;
    }

    @Override
    public boolean restartAudioPlayLogic(Object... args) {
        recoverPlay();
        return true;
    }

    @Override
    public boolean doStartInPlay(Object... args) {
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001405023057?userId=1229522问题
        if (!PlayerManager.getInstance().isPlaying()) {
            PlayerManagerHelper.getInstance().switchPlayerStatus(true);
        }
        return true;
    }

    private void recoverPlay() {
//        KLAutoPlayerManager klAutoPlayerManager = KLAutoPlayerManager.getInstance();
//        AudioStatusManager audioStatusManager = AudioStatusManager.getInstance();
//        Log.i(TAG, "recoverPlay---------->audioStatusManager.isPausedFromUser() = " + audioStatusManager.isPausedFromUser()
//                + "          audioStatusManager.getCurrentFocusChange() = " + audioStatusManager.getCurrentFocusChange());
//        if (audioStatusManager.isPausedFromUser()) {
//            if (audioStatusManager.getCurrentFocusChange() < 0) {
//                requestAudioFocus();
//            }
//            return;
//        }
//        if (audioStatusManager.getCurrentFocusChange() > 0) {
//            return;
//        }
        requestAudioFocus();
        if (!PlayerManager.getInstance().isPlaying()) {
            PlayerManagerHelper.getInstance().switchPlayerStatus(false);
        }
    }
}