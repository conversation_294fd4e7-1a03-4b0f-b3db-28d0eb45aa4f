package com.kaolafm.kradio.byd;

public interface BydSpeechCtlActionConstant {

    //---------------------接收广播aciton------------------

    //支持的功能
    String FUNCTION_UPDATE_RESULT = "com.byd.action.FUNCTION_UPDATE_RESULT"; //默认应用添加结果
    String AUTOVOICE_BOOK = "com.byd.action.AUTOVOICE_BOOK"; //订阅
    String AUTOVOICE_UNBOOK = "com.byd.action.AUTOVOICE_UNBOOK"; //取消订阅
    String AUTOVOICE_QUIT = "com.byd.action.AUTOVOICE_QUIT"; //退出播放
    String AUTOVOICE_SEARCH = "com.byd.action.AUTOVOICE_SEARCH"; //节目搜索
    String AUTOVOICE_RECOMMEND = "com.byd.action.AUTOVOICE_RECOMMEND"; //播放今日推荐
    String AUTOVOICE_PLAY_LATELY = "com.byd.action.AUTOVOICE_PLAY_LATELY"; //播放最近播放的节目
    String AUTOVOICE_PLAY_BOOK = "com.byd.action.AUTOVOICE_PLAY_BOOK"; //播放订阅的节目单
    String AUTOVOICE_QUALITY = "com.byd.action.AUTOVOICE_QUALITY"; //音乐品质设置
    String AUTOVOICE_JUMP_TO = "com.byd.action.AUTOVOICE_JUMP_TO"; //跳转至xx播放
    String AUTOVOICE_FORWARD = "com.byd.action.AUTOVOICE_FORWARD"; //快进（xx分钟）
    String AUTOVOICE_REWIND = "com.byd.action.AUTOVOICE_REWIND"; //快退（xx分钟）

    String AUTOVOICE_ACTIVECARE = "com.byd.action.AUTOVOICE_ACTIVECARE"; //开机问候场景—拉起生态应用播放节


    //不支持的功能
    String AUTOVOICE_COLLECT = "com.byd.action.AUTOVOICE_COLLECT"; //收藏
    String AUTOVOICE_UNCOLLECT = "com.byd.action.AUTOVOICE_UNCOLLECT"; //取消收藏
    String AUTOVOICE_OPEN_LYRIC = "com.byd.action.AUTOVOICE_OPEN_LYRIC"; //查看歌词
    String AUTOVOICE_CLOSE_LYRIC = "com.byd.action.AUTOVOICE_CLOSE_LYRIC"; //关闭歌词
    String AUTOVOICE_PLAY_FAVORITE = "com.byd.action.AUTOVOICE_PLAY_FAVORITE"; //播放收藏的节目单
    String AUTOVOICE_PLAY_LIKE = "com.byd.action.AUTOVOICE_PLAY_LIKE"; //播放我喜欢的节目单
    String AUTOVOICE_PLAY_DOWNLOAD = "com.byd.action.AUTOVOICE_PLAY_DOWNLOAD"; //播放下载的节目
    String AUTOVOICE_PLAY_MODE = "com.byd.action.AUTOVOICE_PLAY_MODE"; //播放模式设置
    String AUTOVOICE_SPEED_ADJUST = "com.byd.action.AUTOVOICE_SPEED_ADJUST"; //x倍速播放


    //----------------------执行结果code------------------------

    //todo 目前各aciton的返回码不统一，需沟通统一后修改

    String RESULT_SUCCESS = "0";

    String RESULT_FAILED = "1";

    String RESULT_OUTOF_BOUNDARY = "2";

    String RESULT_UNSUPPORT = "3";

    String RESULT_NEED_USER_AGRREMENT_ALLOW_FIRST = "4";

    String RESULT_NEED_LOGIN = "5";

    String RESULT_NETWORK_ERROR = "6";

    String RESULT_HAVE_NO_DATA = "7";

    String RESULT_VIP_ONLY = "10";

    String RESULT_NEED_PAY_TO_SUPPORT = "11";

}
