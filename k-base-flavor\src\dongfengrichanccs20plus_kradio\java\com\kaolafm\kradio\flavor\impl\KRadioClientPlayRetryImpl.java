package com.kaolafm.kradio.flavor.impl;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.kaolafm.kradio.k_kaolafm.home.HomeDataManager;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioClientPlayRetryInter;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.opensdk.player.logic.PlayerManager;

public class KRadioClientPlayRetryImpl implements KRadioClientPlayRetryInter {
    private static final String TAG = " ";

    @Override
    public void retry() {
        Log.i(TAG, "resume retry");
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            Log.i(TAG, "resume again");
            if (NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())){
                Log.i(TAG, "network available");
                int currentFocus = PlayerManager.getInstance().getCurrentAudioFocusStatus();
                Log.i(TAG, "checkFocusStatus: currentFocus = " + currentFocus);
                boolean isPlaying = PlayerManager.getInstance().isPlaying();
                Log.i(TAG, "player isPlaying = " + isPlaying);
                if (currentFocus > 0 && !isPlaying) {
                    HomeDataManager.getInstance().playNetOrLocal();
                }
            } else {
                Log.i(TAG, "network not available");
                retry();
            }
        }, 1000);
    }
}
