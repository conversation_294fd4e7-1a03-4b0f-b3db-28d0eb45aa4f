package com.kaolafm.kradio.home.comprehensive.data;

import android.text.TextUtils;
import android.util.Log;
import android.util.Pair;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.component.ui.activitycard.ComponentActivityCell;
import com.kaolafm.kradio.component.ui.base.cell.Card;
import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.component.ui.bigcard.ComponentBigCardCell;
import com.kaolafm.kradio.component.ui.bigcard.ComponentBrandPageBigCardCell;
import com.kaolafm.kradio.component.ui.brandpage.ComponentBrandPageCardCell;
import com.kaolafm.kradio.component.ui.brandpage.ComponentBrandPageHomeCardCell;
import com.kaolafm.kradio.component.ui.k_1x1card.Component1And1Cell;
import com.kaolafm.kradio.component.ui.k_1x1card.ComponentBrandPage1And1Cell;
import com.kaolafm.kradio.component.ui.k_2x1card.Component2And1Cell;
import com.kaolafm.kradio.component.ui.k_2x3card.Component2And3Cell;
import com.kaolafm.kradio.component.ui.rotationcard.ComponentRotationCell;
import com.kaolafm.kradio.component.ui.topiccard.ComponentTopicCardCell;
import com.kaolafm.kradio.history.HistoryManager;
import com.kaolafm.kradio.home.comprehensive.ad.KradioAdColumnManager;
import com.kaolafm.kradio.home.comprehensive.item.BroadcastCell;
import com.kaolafm.kradio.home.comprehensive.item.FunctionBigCell;
import com.kaolafm.kradio.home.comprehensive.item.FunctionPairCell;
import com.kaolafm.kradio.home.comprehensive.item.GoldenRatioCell;
import com.kaolafm.kradio.home.comprehensive.item.HomeCard;
import com.kaolafm.kradio.home.comprehensive.util.HomeUtil;
import com.kaolafm.kradio.home.data.Category;
import com.kaolafm.kradio.lib.base.flavor.KRadioHomeCellInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioPicSettingInter;
import com.kaolafm.kradio.lib.bean.HistoryItem;
import com.kaolafm.kradio.lib.bean.SubscribeData;
import com.kaolafm.kradio.lib.common.ResType;
import com.kaolafm.kradio.lib.utils.ChannelSetingUtil;
import com.kaolafm.kradio.lib.utils.FlavorImplName;
import com.kaolafm.kradio.lib.utils.UrlUtil;
import com.kaolafm.kradio.lib.widget.tab.Tab;
import com.kaolafm.opensdk.api.operation.model.ImageFile;
import com.kaolafm.opensdk.api.operation.model.column.ActivityDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.AlbumDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.AudioDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.BroadcastDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.CategoryColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.Column;
import com.kaolafm.opensdk.api.operation.model.column.ColumnContent;
import com.kaolafm.opensdk.api.operation.model.column.ColumnGrp;
import com.kaolafm.opensdk.api.operation.model.column.ColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.FeatureDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.LiveProgramDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.PageRedirectionColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.RadioDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.RadioQQMusicDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.SearchResultColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.TVDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.TopicDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.VideoAlbumDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.VideoDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.WebViewColumnMember;
import com.kaolafm.opensdk.api.subscribe.SubscribeInfo;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.utils.operation.OperationAssister;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: DataConverter.java
 *                                                                  *
 * Created in 2018/4/24 上午11:06
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
public class DataConverter {


    private static final String TAG = "sdk";

//    public static SubscribeData toSubscribeData(SongMenu sm) {
//        SubscribeData sd = new SubscribeData();
//        sd.setId(sm.getDissId());
//        sd.setImg(sm.getPicUrl());
//        sd.setName(sm.getDissName());
//        return sd;
//    }


//    public static List<SubscribeData> toSubscribeDataList(List<SongMenu> songMenus) {
//        List<SubscribeData> sms = new LinkedList<>();
//        if (songMenus != null && !songMenus.isEmpty()) {
//            for (int i = 0; i < songMenus.size(); i++) {
//                SongMenu sm = songMenus.get(i);
//
//                SubscribeData sd = DataConverter.toSubscribeData(sm);
//
//                sms.add(sd);
//            }
//        }
//        return sms;
//    }

    public static Category.Group toGroup(Column column) {
        if (column == null) {
            return null;
        }
        Category.Group group = new Category.Group();
        group.id = Long.valueOf(column.getCode());
        group.title = column.getTitle();
        group.subtitle = column.getSubtitle();
        group.appControlType = getAppControlType(column);

        group.items = new ArrayList<>();

        List<? extends ColumnMember> columnMembers = column.getColumnMembers();

        if (columnMembers != null && !columnMembers.isEmpty()) {
            for (int i = 0; i < columnMembers.size(); i++) {
                Category.Item item = toItem(columnMembers.get(i), group);
                if (item != null) {
                    group.items.add(item);
                }
            }
        }
        if (!ListUtil.isEmpty(group.items)) {
            Category.Item item = group.items.get(0);
            if (item != null) {
                item.firstInGroup = true;
                if (item.resType == ResType.BROADCAST_TYPE) {
                    group.viewType = Category.ID_BROADCAST;
                }
            }
        }
        return group;
    }


    private static Category.Item toItem(ColumnMember cm, Category.Group group) {
        if (cm == null) {
            return null;
        }
        Category.Item item = new Category.Item();
//        Log.i("tag", "toItem: " + cm.getRid() + "," + cm.getRname());
        item.id = OperationAssister.getId(cm);

        if (cm instanceof BroadcastDetailColumnMember) {
            item.freq = ((BroadcastDetailColumnMember) cm).getFreq();
        }
        //有该成员说明有功能模块
        if (cm instanceof CategoryColumnMember) {
            group.appControlType = Category.FUNCTION_MODULE;
        }

        item.title = cm.getTitle();
        Map tempMap = cm.getImageFiles();
        if (tempMap != null) {
            ImageFile cover = cm.getImageFiles().get("cover");
            if (cover == null) {
                cover = cm.getImageFiles().get("icon");
            }
            if (cover != null) {
                item.coverImgUrl = cover.getUrl();
            }
        }
        item.coverImgUrl = OperationAssister.getImage(cm);

        item.resType = getResType(cm);
        item.cardType = getCardType(cm);
        item.operateType = getOperateType(cm);
        item.callBack = cm.getCallBack();
        item.outputMode = cm.getOutputMode();
        item.isSelected = TextUtils.equals(PlayerManager.getInstance().getCurPlayItem().getRadioId(), String.valueOf(item.id));
        item.extInfo = cm.getExtInfo();
        item.parent = group;
        return item;
    }


    public static Category toCategory(ColumnGrp cg) {
        if (cg == null) {
            return null;
        }
        Category cat = new Category();
        cat.id = HomeUtil.getLongMaybeError(cg.getCode());
        cat.title = cg.getTitle();
        if (cg.getImageFiles() != null) {
            if (cg.getImageFiles().get("icon") != null) {
                cat.icon = cg.getImageFiles().get("icon").getUrl();
            }
        }

        cat.redirectId = HomeUtil.getLongMaybeError(cg.getCode());

        return cat;
    }


    /**
     * 获取播放类型
     */
    private static int getResType(ColumnMember cm) {
        int restype = ResType.ALBUM_TYPE;
        if (cm instanceof LiveProgramDetailColumnMember) {
            restype = ResType.LIVE_TYPE;
        } else if (cm instanceof SearchResultColumnMember) {
            restype = ResType.NOACTION_TYPE;
        } else if (cm instanceof BroadcastDetailColumnMember) {
            restype = ResType.BROADCAST_TYPE;
        } else if (cm instanceof AlbumDetailColumnMember) {
            restype = ResType.ALBUM_TYPE;
        } else if (cm instanceof AudioDetailColumnMember) {
            restype = ResType.AUDIO_TYPE;
        } else if (cm instanceof RadioDetailColumnMember) {
            restype = ResType.RADIO_TYPE;
        } else if (cm instanceof RadioQQMusicDetailColumnMember) {
            RadioQQMusicDetailColumnMember qqcm = (RadioQQMusicDetailColumnMember) cm;
//            0,场景电台 ;1,标签电台
            switch (qqcm.getRadioQQMusicType()) {
                case 1:
                    restype = ResType.RESOURCES_TYPE_MUSIC_RADIO_LABEL;
                    break;
                case 0:
                    restype = ResType.RESOURCES_TYPE_MUSIC_RADIO_SCENE;
                    break;
                default:
                    restype = ResType.RESOURCES_TYPE_QQ_MUSIC;
            }
        } else if (cm instanceof WebViewColumnMember) {
            //error
            restype = ResType.NOACTION_TYPE;
        } else if (cm instanceof CategoryColumnMember) {
//            1:专辑；2:广播；3:直播；4:智能电台；5:QQ音乐电台
            //功能入口，跳转，需要额外信息
            restype = ResType.FUNCTION_ENTER_SMALL;

        } else if (cm instanceof ActivityDetailColumnMember) {
            restype = ResType.ACTIVITY_TYPE;
        } else if (cm instanceof FeatureDetailColumnMember) {
            restype = ResType.FEATURE_TYPE;
        } else if (cm instanceof TVDetailColumnMember) {
            restype = ResType.TV_TYPE;
        } else if (cm instanceof TopicDetailColumnMember) {
            restype = ResType.TOPIC_TYPE;
        } else if (cm instanceof PageRedirectionColumnMember) {// ResType.NOACTION_TYPE 导致无法跳转
            restype = ResType.NOACTION_TYPE;
        } else if (cm instanceof VideoAlbumDetailColumnMember) {
            restype = ResType.VIDEO_ALBUM_TYPE;
        } else if (cm instanceof VideoDetailColumnMember) {
            restype = ResType.VIDEO_TYPE;
        }
        Map<String, String> extInfo = cm.getExtInfo();
        return restype;
    }

    private static List<ColumnContent> getHomeCardContentList(List<ColumnContent> contentList) {
        if (contentList == null) return new ArrayList<>();
        for (int i = 0; i < contentList.size(); i++) {
            contentList.get(i).setResType(getResType(contentList.get(i)));
        }
        return contentList;
    }

    /**
     * 212 通过成员的type转换成ResType
     *
     * @param type
     * @return
     */
    private static int getHomeCardChildResType(String type) {
        int resType = ResType.NOACTION_TYPE;

        switch (type) {
            case "WebViewColumnMember"://web页面
                break;
            case "SearchResultColumnMember"://搜索类型
                break;
            case "AlbumDetailColumnMember"://专辑类型
                resType = ResType.ALBUM_TYPE;
                break;
            case "PageRedirectionColumnMember"://跳转页面类型
                break;
            case "FeatureDetailColumnMember"://专题类型
                resType = ResType.FEATURE_TYPE;
                break;
            case "ActivityDetailColumnMember"://活动类型
                resType = ResType.ACTIVITY_TYPE;
                break;
            case "TVDetailColumnMember"://听电视类型
                resType = ResType.TV_TYPE;
                break;
            case "LiveProgramDetailColumnMember"://直播类型
                resType = ResType.LIVE_TYPE;
                break;
            case "CategoryColumnMember"://分类内容类型
                resType = ResType.CATEGORY_TYPE;
                break;
            case "BroadcastDetailColumnMember"://广播类型
                resType = ResType.BROADCAST_TYPE;
                break;
            case "RadioDetailColumnMember"://电台类型
                resType = ResType.RADIO_TYPE;
                break;
            case "AudioDetailColumnMember"://单曲类型
                resType = ResType.AUDIO_TYPE;
                break;
        }
        return resType;
    }

    /**
     * 获取卡片类型
     */
    private static int getCardType(ColumnMember cm) {
        @Category.CardType int cardType = Category.CARD_TYPE_BIG;
        if (cm instanceof BroadcastDetailColumnMember) {
            cardType = Category.CARD_TYPE_SMALL_BROADCAST;
        } else if (cm instanceof CategoryColumnMember) {
            cardType = Category.CARD_TYPE_SMALL_FUNCTION;
        }
        return cardType;
    }

    /**
     * 是否vip 1:是,0:否
     */
    private static int getVip(ColumnMember cm) {
        int vipType = 0;
        if (cm instanceof AlbumDetailColumnMember) {
            vipType = ((AlbumDetailColumnMember) cm).getVip();
        }
        return vipType;
    }

    /**
     * 是否精品  1:是,0:否
     */
    private static int getFine(ColumnMember cm) {
        int fine = 0;
        if (cm instanceof AlbumDetailColumnMember) {
            fine = ((AlbumDetailColumnMember) cm).getFine();
        }
        return fine;
    }


    /**
     * 操作类型
     */
    private static int getOperateType(ColumnMember cm) {
        @Category.OperateType int operateType = Category.OPERATE_TYPE_PLAY;

        if (cm instanceof LiveProgramDetailColumnMember) {
            operateType = Category.OPERATE_TYPE_LIVE;
        } else if (cm instanceof CategoryColumnMember) {
            operateType = Category.OPERATE_TYPE_ENTRA;
        }
        return operateType;
    }

    /**
     * 获取AppControlType
     */
    private static int getAppControlType(Column column) {
        @Category.AppControlType int act;
        String appControlType = column.getExtInfo().get("appControlType");
        if ("LatestBroadcast".equals(appControlType)) {
            act = Category.LATEST_BROADCAST;
        } else if ("FunctionModule".equals(appControlType)) {
            act = Category.FUNCTION_MODULE;
        } else if ("MyMusic".equals(appControlType)) {
            act = Category.MY_MUSIC;
        } else {
            act = Category.CONTROL_TYPE_NONE;
        }

        return act;
    }


    /**
     *
     */
    public static SubscribeData toSubscribeData(SubscribeInfo subscribeInfo) {
        if (subscribeInfo == null) {
            return null;
        }
        SubscribeData sd = new SubscribeData();
        sd.setId(subscribeInfo.getId());
        sd.setName(subscribeInfo.getName());
        sd.setType(subscribeInfo.getType());
        sd.setDesc(subscribeInfo.getDesc());
        sd.setImg(subscribeInfo.getImg());
        sd.setUpdateTime(String.valueOf(subscribeInfo.getUpdateTime()));
        sd.setUpdateNum(subscribeInfo.getUpdateNum());
        sd.setCountNum(subscribeInfo.getCountNum());
        sd.setHasCopyright(subscribeInfo.getHasCopyright());
        sd.setIsOnline(subscribeInfo.getIsOnline());
        return sd;
    }

    private static int getTotalMemberNum(List<ColumnGrp> grps){
        if (ListUtil.isEmpty(grps)) {
            return -1;
        }
        int totalNum = 0;
        for (ColumnGrp columnGrp : grps){
            if (columnGrp instanceof Column) {
                try {
                    List<? extends ColumnMember> columnMembers = ((Column) columnGrp).getColumnMembers();
                    totalNum += columnMembers.size();
                } catch (Exception e) {
                    return -1;
                }
            } else {
                List<? extends ColumnGrp> childColumnGrps = columnGrp.getChildColumns();
                for (ColumnGrp childGroup : childColumnGrps){
                    try {
                        List<? extends ColumnMember> columnMembers = ((Column) childGroup).getColumnMembers();
                        totalNum += columnMembers.size();
                    } catch (Exception e) {
                        return -1;
                    }
                }
            }
        }
        return totalNum;
    }

    public static List<HomeCell> toHomeCells(List<ColumnGrp> grps) {
        // ColumnGrp - 栏目组，下级依然是栏目
        // Column    - 栏目，下级是成员列表
        if (ListUtil.isEmpty(grps)) {
            return null;
        }
        int totalMemberNum = getTotalMemberNum(grps);
        ArrayList<HomeCell> homeCells = new ArrayList<>();
        Iterator<ColumnGrp> iterator = grps.iterator();
        KRadioHomeCellInter homeCellImpl = (KRadioHomeCellInter) ChannelSetingUtil.getChannelImpl("KRadioHomeCellImpl");
        int index = 0;
        while (iterator.hasNext()) {
            ColumnGrp columnGrp = iterator.next();
            //转化为Card。tab不为空说明有子栏目
            if (columnGrp instanceof Column) {
                Card card = groupToCard((Column) columnGrp, index);
                List<? extends ColumnMember> columnMembers = ((Column) columnGrp).getColumnMembers();
                List<HomeCell> cells = memberToCell(homeCells.size(),columnMembers, columnGrp.getCode(), totalMemberNum);
                // todo ? 是否需要添加广播历史
                //  蔚来项目暂时先不添加
//                List<HomeCell> broadcastHistory = getBroadcastHistory((Column) columnGrp, card);
//                if (!ListUtil.isEmpty(broadcastHistory)) {
//                    cells.addAll(broadcastHistory);
//                }
                card.childCount = cells.size();
                card.childes = cells;
                homeCells.addAll(cells);
                // todo ? 是否需要连图运营位是动态的
                //  蔚来项目暂时先不添加
//                KradioAdColumnManager.getInstance().checkColumnAd((Column) columnGrp, cells, homeCells.size());
            } else {
                List<? extends ColumnGrp> childColumnGrps = columnGrp.getChildColumns();
                for (int j = 0, childSize = childColumnGrps.size(); j < childSize; j++) {
                    ColumnGrp childGroup = childColumnGrps.get(j);
                    Card card = groupToCard(childGroup, index);
                    if (childGroup instanceof Column) {
                        List<? extends ColumnMember> columnMembers = ((Column) childGroup).getColumnMembers();
                        List<HomeCell> cells = memberToCell(homeCells.size(),columnMembers, columnGrp.getCode(), totalMemberNum);
                        // todo ? 是否需要添加广播历史
                        //  蔚来项目暂时先不添加
//                        List<HomeCell> broadcastHistory = getBroadcastHistory(childGroup, card);
//                        if (!ListUtil.isEmpty(broadcastHistory)) {
//                            cells.addAll(broadcastHistory);
//                        }
                        if (homeCellImpl != null) {
                            homeCellImpl.computeCellSpace(j, cells);
                        }
                        card.childCount = cells.size();
                        card.childes = cells;
                        homeCells.addAll(cells);
                        KradioAdColumnManager.getInstance().checkColumnAd(childGroup, cells, homeCells.size());
                    }
                }
            }
            index++;
        }
        return homeCells;
    }

    private void getHomeCellList(ColumnGrp columnGrp, ArrayList<HomeCell> homeCells) {
        List<? extends ColumnMember> columnMembers = ((Column) columnGrp).getColumnMembers();
        Iterator<? extends ColumnMember> iterator2 = columnMembers.iterator();

        while (iterator2.hasNext()) {
            ColumnMember columnMember = iterator2.next();
            HomeCell homeCell = new HomeCell();
            if (columnMember instanceof CategoryColumnMember) {
                Map<String, String> extInfo = columnMember.getExtInfo();
                //功能入口，小卡片
                if (extInfo != null && ("SMALL".equals(extInfo.get("displayStyle"))
                        || "BIG".equals(extInfo.get("displayStyle")))) {

                    String[] paths = getPaths(extInfo);
                    homeCell.firstCode = paths[0];
                    homeCell.secondCode = paths[1];
                }
            }
            String image = OperationAssister.getImage(columnMember);
            homeCell.imageUrl = UrlUtil.getDefaultConfigPicUrl(image);
            homeCell.code = columnMember.getCode();
            homeCell.playId = OperationAssister.getId(columnMember);
            if (columnMember instanceof AlbumDetailColumnMember) {
                homeCell.belongingId = ((AlbumDetailColumnMember) columnMember).getAlbumId();
            } else if (columnMember instanceof RadioDetailColumnMember) {
                homeCell.belongingId = ((RadioDetailColumnMember) columnMember).getRadioId();
            } else if (columnMember instanceof BroadcastDetailColumnMember) {
                homeCell.belongingId = ((BroadcastDetailColumnMember) columnMember).getBroadcastId();
                homeCell.contentType = ((BroadcastDetailColumnMember) columnMember).getBroadcastSort();
                homeCell.freq = ((BroadcastDetailColumnMember) columnMember).getFreq();
            } else if (columnMember instanceof TVDetailColumnMember) {
                homeCell.belongingId = ((TVDetailColumnMember) columnMember).getListenTVid();
            } else if (columnMember instanceof LiveProgramDetailColumnMember) {
                LiveProgramDetailColumnMember liveColumnMember = (LiveProgramDetailColumnMember) columnMember;
                homeCell.belongingId = liveColumnMember.getLiveProgramId();
                homeCell.anchor = liveColumnMember.getAnchor();
            }

            homeCell.name = columnMember.getTitle();
            homeCell.resType = getResType(columnMember);
            homeCell.recommendReason = columnMember.getRecommendReason();
            homeCell.outputMode = columnMember.getOutputMode();
            homeCell.callBack = columnMember.getCallBack();
            homeCell.vip = getVip(columnMember);
            homeCell.fine = getFine(columnMember);
            homeCells.add(homeCell);
        }

    }

    public static Pair<List<Tab>, List<HomeCell>> toTabsAndCells(List<ColumnGrp> grps) {
        if (ListUtil.isEmpty(grps)) {
            return null;
        }
        ArrayList<HomeCell> homeCells = new ArrayList<>();
        ArrayList<Tab> tabs = new ArrayList<>();
        Iterator<ColumnGrp> iterator = grps.iterator();
        KRadioHomeCellInter homeCellImpl = (KRadioHomeCellInter) ChannelSetingUtil.getChannelImpl("KRadioHomeCellImpl");
        int index = 0;
        while (iterator.hasNext()) {
            ColumnGrp columnGrp = iterator.next();
            Tab tab = toTab(columnGrp);
            if (tab != null) {
                tabs.add(tab);
                //转化为Card。tab不为空说明有子栏目
                List<? extends ColumnGrp> childColumnGrps = columnGrp.getChildColumns();
                for (int j = 0, childSize = childColumnGrps.size(); j < childSize; j++) {
                    ColumnGrp childGroup = childColumnGrps.get(j);
                    Card card = groupToCard(childGroup, index);
                    if (childGroup instanceof Column) {
                        List<? extends ColumnMember> columnMembers = ((Column) childGroup).getColumnMembers();
                        List<HomeCell> cells = memberToCell(homeCells.size(),columnMembers, columnGrp.getCode(), -1);
                        List<HomeCell> broadcastHistory = getBroadcastHistory(childGroup, card);
                        if (!ListUtil.isEmpty(broadcastHistory)) {
                            cells.addAll(broadcastHistory);
                        }
                        if (homeCellImpl != null) {
                            homeCellImpl.computeCellSpace(j, cells);
                        }
                        card.childCount = cells.size();
                        card.childes = cells;
                        homeCells.addAll(cells);
                        KradioAdColumnManager.getInstance().checkColumnAd(childGroup, cells, homeCells.size());
                    }
                }
                index++;
            }
        }
        return Pair.create(tabs, homeCells);
    }

    private static Tab toTab(ColumnGrp columnGrp) {
        if (columnGrp != null && !ListUtil.isEmpty(columnGrp.getChildColumns())) {
            Tab homeTab = new Tab();
            homeTab.code = columnGrp.getCode();
            homeTab.title = columnGrp.getTitle();
            return homeTab;
        }
        return null;
    }


    /**
     * 获取广播的收听历史。如果配置了扩展属性"appControlType: LatestBroadcast"就需要查询本地广播历史
     */
    private static List<HomeCell> getBroadcastHistory(ColumnGrp childGroup, Card card) {
        Map<String, String> extInfo = childGroup.getExtInfo();
        if (extInfo != null) {
            String appControlType = extInfo.get("appControlType");
            //有广播历史
            if ("LatestBroadcast".equals(appControlType)) {
                List<HistoryItem> historyItems = HistoryManager.getInstance().getBroadcastList();
                if (!ListUtil.isEmpty(historyItems)) {
                    ArrayList<HomeCell> homeCells = new ArrayList<>();

                    for (int i = 0, size = historyItems.size(); i < size; i++) {
                        HistoryItem historyItem = historyItems.get(i);
                        HomeCell homeCell = toBroadcastCell(historyItem);
                        if (homeCell != null) {
                            homeCell.parent = card;
                            homeCell.positionInParent = i;
                            homeCell.isLastInParent = i == size - 1;
                            homeCells.add(homeCell);
                        }
                    }
//                    if (!ListUtil.isEmpty(homeCells)) {
//                        //两头添加空cell，用于换行
//                        homeCells.add(0, new SpaceCell());
//                        return homeCells;
//                    }
                }
            }
        }
        return null;
    }

    private static HomeCell toBroadcastCell(HistoryItem historyItem) {
        if (historyItem != null) {
            BroadcastCell BroadcastCell = new BroadcastCell();
            BroadcastCell.code = historyItem.getRadioId();
            BroadcastCell.playId = Long.parseLong(historyItem.getRadioId());
            BroadcastCell.imageUrl = historyItem.getPicUrl();
            BroadcastCell.resType = ResType.BROADCAST_TYPE;
            String[] temp = historyItem.getRadioTitle().split("  ");
            int size = temp.length;
            String title = temp[0];
            String freq = "";
            if (size > 1) {
                freq = temp[1];
            }
            BroadcastCell.name = title;
            BroadcastCell.freq = freq;
            return BroadcastCell;
        }
        return null;
    }

    private static Card groupToCard(ColumnGrp columnGrp, int position) {
        HomeCard card = new HomeCard();
        if (columnGrp != null) {
            card.title = columnGrp.getTitle();
            card.subtitle = columnGrp.getSubtitle();
            card.code = columnGrp.getCode();
            card.parentPosition = position;
        }
        return card;
    }

    public static List<HomeCell> memberToCell(int indexFrom, List<? extends ColumnMember> columnMembers,
                                              String columnId, int totalMemberNum) {
//        return memberToCellInNio(indexFrom, columnMembers, columnId, totalMemberNum);
        ArrayList<HomeCell> homeCells = new ArrayList<>();
//      避免在while 里面多次反射
        KRadioPicSettingInter picSetting = (KRadioPicSettingInter) ChannelSetingUtil.getChannelImpl(FlavorImplName.QICHENRICHANSETTINGPIC);

        if (!ListUtil.isEmpty(columnMembers)) {
            FunctionPairCell functionPair = new FunctionPairCell();
            Iterator<? extends ColumnMember> iterator = columnMembers.iterator();
            int index = indexFrom;
            while (iterator.hasNext()) {
                ColumnMember columnMember = iterator.next();
                HomeCell homeCell = getCellAccordingToMember(columnMember);
                if (columnMember instanceof FeatureDetailColumnMember) {
                    //拦截专题类型
                    // todo ? 蔚来项目暂时先不执行这个额外的转换
//                    homeCell = new GoldenRatioCell();
                    homeCell.belongingId = ((FeatureDetailColumnMember) columnMember).getFeatureId();
                } else if (columnMember instanceof AlbumDetailColumnMember) {
                    homeCell.belongingId = ((AlbumDetailColumnMember) columnMember).getAlbumId();
                } else if (columnMember instanceof RadioDetailColumnMember) {
                    homeCell.belongingId = ((RadioDetailColumnMember) columnMember).getRadioId();
                } else if (columnMember instanceof BroadcastDetailColumnMember) {
                    homeCell.belongingId = ((BroadcastDetailColumnMember) columnMember).getBroadcastId();
                    homeCell.contentType = ((BroadcastDetailColumnMember) columnMember).getBroadcastSort();
                    homeCell.freq = ((BroadcastDetailColumnMember) columnMember).getFreq();
                } else if (columnMember instanceof TVDetailColumnMember) {
                    homeCell.belongingId = ((TVDetailColumnMember) columnMember).getListenTVid();
                } else if (columnMember instanceof LiveProgramDetailColumnMember) {
                    LiveProgramDetailColumnMember liveColumnMember = (LiveProgramDetailColumnMember) columnMember;
                    homeCell.belongingId = liveColumnMember.getLiveProgramId();
                    homeCell.anchor = liveColumnMember.getAnchor();
                } else if (columnMember instanceof ActivityDetailColumnMember) {
                    ActivityDetailColumnMember activityDetailColumnMember = (ActivityDetailColumnMember) columnMember;
                    homeCell.belongingId = Long.parseLong(activityDetailColumnMember.getActivityId());
                } else if (columnMember instanceof TopicDetailColumnMember) {
                    TopicDetailColumnMember topicDetailColumnMember = (TopicDetailColumnMember) columnMember;
                    homeCell.belongingId = topicDetailColumnMember.getTopicId();
                }
                String image = OperationAssister.getImage(columnMember);
                if (picSetting != null) {
                    homeCell.imageUrl = picSetting.getHomePicUrl(image);
                } else {
                    homeCell.imageUrl = UrlUtil.getDefaultConfigPicUrl(image);
                }
                homeCell.columnId = columnId;
                homeCell.imageFiles = columnMember.getImageFiles();
                homeCell.memberTag = columnMember.getMemberTag();
                homeCell.componentType = columnMember.getComponentType();
                // flag 将 columnMember.getContentList() 设置给 homeCell.contentList
                homeCell.contentList = getHomeCardContentList(columnMember.getContentList());
                homeCell.code = columnMember.getCode();
                homeCell.playId = OperationAssister.getId(columnMember);
                homeCell.name = columnMember.getTitle();
//                homeCell.parent = card;
                homeCell.resType = getResType(columnMember);
                homeCell.outputMode = columnMember.getOutputMode();
                homeCell.callBack = columnMember.getCallBack();
                homeCell.vip = getVip(columnMember);
                homeCell.fine = getFine(columnMember);
                //将功能列表作为一个卡片添加到数据中。
//                homeCell = checkFunctionCell(card, functionPair, homeCell);
                if (homeCell != null) {
                    homeCell.positionInParent = index++;
                    if (totalMemberNum < 0) {
                        homeCell.isLastInParent = index == columnMembers.size();
                    } else {
                        homeCell.isLastInParent = index == totalMemberNum;
                    }
                    homeCells.add(homeCell);
                } else {
                    iterator.remove();
                }
            }
            //广播需要换行
//            if (columnMembers.size() > 0 && columnMembers.get(0) instanceof BroadcastDetailColumnMember) {
//                homeCells.add(0, new SpaceCell());
//                homeCells.add(homeCells.size(), new SpaceCell());
//            }
        }
        return homeCells;
    }

    public static List<HomeCell> memberToCellInNio(int indexFrom, List<? extends ColumnMember> columnMembers,
                                                   String columnId, int totalMemberNum) {
        ArrayList<HomeCell> homeCells = new ArrayList<>();

        // 避免在while 里面多次反射
        KRadioPicSettingInter picSetting = (KRadioPicSettingInter) ChannelSetingUtil.getChannelImpl(FlavorImplName.QICHENRICHANSETTINGPIC);

        if (!ListUtil.isEmpty(columnMembers)) {
            Iterator<? extends ColumnMember> iterator = columnMembers.iterator();
            while (iterator.hasNext()) {
                ColumnMember columnMember = iterator.next();
                List<HomeCell> homeCellList = generateHomeCellInNio(columnId, columnMember, columnMember.getContentList(), picSetting);
                if (!homeCellList.isEmpty()) {
                    homeCells.addAll(homeCellList);
                }
         }
        }
        Log.i("DataConvert", "memberToCellInNio.homeCells is "+homeCells.toString());
        return homeCells;
    }

    private static List<HomeCell> generateHomeCellInNio(String columnId, ColumnMember columnMember, List<ColumnContent> columnContentList, KRadioPicSettingInter picSetting){
        List<HomeCell> homeCellList = new ArrayList<>();
        if (columnContentList == null || columnContentList.isEmpty()){
            return homeCellList;
        }
        for (ColumnContent columnContent : columnContentList){
            // 统一处理成 [大卡 - ComponentBigCardCell]
            HomeCell homeCell = new ComponentBigCardCell();
            homeCell.itemType = ResType.HOME_ITEM_TYPE_BIG;

            if (columnMember instanceof FeatureDetailColumnMember) {
                //拦截专题类型
                // todo ? 蔚来项目暂时先不执行这个额外的转换
//                homeCell = new GoldenRatioCell();
                homeCell.belongingId = ((FeatureDetailColumnMember) columnMember).getFeatureId();
            } else if (columnMember instanceof AlbumDetailColumnMember) {
                homeCell.belongingId = ((AlbumDetailColumnMember) columnMember).getAlbumId();
            } else if (columnMember instanceof RadioDetailColumnMember) {
                homeCell.belongingId = ((RadioDetailColumnMember) columnMember).getRadioId();
            } else if (columnMember instanceof BroadcastDetailColumnMember) {
                homeCell.belongingId = ((BroadcastDetailColumnMember) columnMember).getBroadcastId();
                homeCell.contentType = ((BroadcastDetailColumnMember) columnMember).getBroadcastSort();
                homeCell.freq = ((BroadcastDetailColumnMember) columnMember).getFreq();
            } else if (columnMember instanceof TVDetailColumnMember) {
                homeCell.belongingId = ((TVDetailColumnMember) columnMember).getListenTVid();
            } else if (columnMember instanceof LiveProgramDetailColumnMember) {
                LiveProgramDetailColumnMember liveColumnMember = (LiveProgramDetailColumnMember) columnMember;
                homeCell.belongingId = liveColumnMember.getLiveProgramId();
                homeCell.anchor = liveColumnMember.getAnchor();
            } else if (columnMember instanceof ActivityDetailColumnMember) {
                ActivityDetailColumnMember activityDetailColumnMember = (ActivityDetailColumnMember) columnMember;
                homeCell.belongingId = Long.parseLong(activityDetailColumnMember.getActivityId());
            } else if (columnMember instanceof TopicDetailColumnMember) {
                TopicDetailColumnMember topicDetailColumnMember = (TopicDetailColumnMember) columnMember;
                homeCell.belongingId = topicDetailColumnMember.getTopicId();
            }
            String image = OperationAssister.getImage(columnMember);
            if (picSetting != null) {
                homeCell.imageUrl = picSetting.getHomePicUrl(image);
            } else {
                homeCell.imageUrl = UrlUtil.getDefaultConfigPicUrl(image);
            }
            homeCell.columnId = columnId;
            homeCell.imageFiles = columnMember.getImageFiles();
            homeCell.memberTag = columnMember.getMemberTag();
            homeCell.componentType = columnMember.getComponentType();

            // flag 蔚来项目中 将 columnMember.getContentList() 铺平为 大卡组件
            ArrayList<ColumnContent> contentList = new ArrayList<>(1);
            contentList.add(columnContent);

            homeCell.contentList = contentList;
            homeCell.code = columnMember.getCode();
            homeCell.playId = OperationAssister.getId(columnMember);
            homeCell.name = columnMember.getTitle();
//                homeCell.parent = card;
            homeCell.resType = getResType(columnMember);
            homeCell.outputMode = columnMember.getOutputMode();
            homeCell.callBack = columnMember.getCallBack();
            homeCell.vip = getVip(columnMember);
            homeCell.fine = getFine(columnMember);

            homeCellList.add(homeCell);
        }
        Log.i("DataConvert", "generateHomeCellInNio.homeCellList is "+homeCellList.toString());
        return homeCellList;
    }

    private HomeCell initDate(HomeCell homeCell, ColumnMember columnMember) {
        if (columnMember instanceof FeatureDetailColumnMember) {
            //拦截专题类型
            homeCell = new GoldenRatioCell();
            homeCell.belongingId = ((FeatureDetailColumnMember) columnMember).getFeatureId();
            homeCell.itemType = ResType.FEATURE_TYPE;
            homeCell.resType = ResType.FEATURE_TYPE;
        } else if (columnMember instanceof AlbumDetailColumnMember) {
            homeCell.belongingId = ((AlbumDetailColumnMember) columnMember).getAlbumId();
        } else if (columnMember instanceof RadioDetailColumnMember) {
            homeCell.belongingId = ((RadioDetailColumnMember) columnMember).getRadioId();
        } else if (columnMember instanceof BroadcastDetailColumnMember) {
            homeCell.belongingId = ((BroadcastDetailColumnMember) columnMember).getBroadcastId();
            homeCell.contentType = ((BroadcastDetailColumnMember) columnMember).getBroadcastSort();
            homeCell.freq = ((BroadcastDetailColumnMember) columnMember).getFreq();
        } else if (columnMember instanceof TVDetailColumnMember) {
            homeCell.belongingId = ((TVDetailColumnMember) columnMember).getListenTVid();
        } else if (columnMember instanceof LiveProgramDetailColumnMember) {
            LiveProgramDetailColumnMember liveColumnMember = (LiveProgramDetailColumnMember) columnMember;
            homeCell.belongingId = liveColumnMember.getLiveProgramId();
            homeCell.anchor = liveColumnMember.getAnchor();
        } else if (columnMember instanceof ActivityDetailColumnMember) {
            ActivityDetailColumnMember activityDetailColumnMember = (ActivityDetailColumnMember) columnMember;
            homeCell.belongingId = Long.parseLong(activityDetailColumnMember.getActivityId());
        } else if (columnMember instanceof TopicDetailColumnMember) {
            TopicDetailColumnMember topicDetailColumnMember = (TopicDetailColumnMember) columnMember;
            homeCell.belongingId = topicDetailColumnMember.getTopicId();
        }
        return homeCell;
    }

    /**
     * 检查是不是功能入口，如果是就将两个数据合并成一个对然后作为一个item。
     *
     * @param card         父
     * @param functionPair 功能入口对
     * @param homeCell     单个item
     * @return 返回功能入口对或原数据cell，返回空说明已经将单个cell保存到cell对的第一个中。
     */
    private static HomeCell checkFunctionCell(Card card, FunctionPairCell functionPair, HomeCell homeCell) {
        if (homeCell instanceof FunctionPairCell) {
            if (functionPair.first == null) {
                functionPair.first = (FunctionPairCell) homeCell;
            } else if (functionPair.second == null) {
                functionPair.second = (FunctionPairCell) homeCell;
            }
            if (functionPair.second != null || (homeCell instanceof FunctionBigCell)) {
                FunctionPairCell functionPairCell;
                if ((homeCell instanceof FunctionBigCell)) {
                    functionPairCell = (FunctionBigCell) homeCell;
                } else {
                    functionPairCell = new FunctionPairCell(functionPair);
                    functionPairCell.positionInParent = functionPair.first.positionInParent;
                }

                functionPairCell.parent = card;
                functionPair.reset();
                return functionPairCell;
            }
            return null;
        } else {
            return homeCell;
        }
    }

    private static HomeCell getCellAccordingToMember(ColumnMember columnMember) {
        HomeCell homeCell = null;
        switch (columnMember.getComponentType()) {
            case 1://上2下1组件
                homeCell = new Component2And1Cell();
                homeCell.itemType = ResType.HOME_ITEM_TYPE_2_1;
                break;
            case 2://轮播组件
                homeCell = new ComponentRotationCell();
                homeCell.itemType = ResType.HOME_ITEM_TYPE_ROTATUON;
                break;
            case 3://上2下3组件
                homeCell = new Component2And3Cell();
                homeCell.itemType = ResType.HOME_ITEM_TYPE_2_3;
                break;
            case 4://上1下1组件
                homeCell = new Component1And1Cell();
                homeCell.itemType = ResType.HOME_ITEM_TYPE_1_1;
                break;
            case 5://单内容大卡组件
                homeCell = new ComponentBigCardCell();
                homeCell.itemType = ResType.HOME_ITEM_TYPE_BIG;
                break;
            case 6://活动类型
                homeCell = new ComponentActivityCell();
                homeCell.itemType = ResType.ACTIVITY_TYPE;
                break;
            case 7://品牌入口组件
                homeCell = new ComponentBrandPageHomeCardCell();
                homeCell.itemType = ResType.HOME_ITEM_TYPE_BRAND_HOME;
                break;
            case 10:// 品牌电台圆形组件 do
                homeCell = new ComponentBrandPageCardCell();
                homeCell.itemType = ResType.HOME_ITEM_TYPE_BRAND;
                break;
            case 11://话题大卡组件
            case 12://话题小卡组件
                homeCell = new ComponentTopicCardCell();
                homeCell.itemType = ResType.HOME_ITEM_TYPE_TOPIC;
                break;
            case 13://品牌主页大卡
            case 15://车主活动类型组件
                homeCell = new ComponentBrandPageBigCardCell();
                homeCell.itemType = ResType.HOME_ITEM_TYPE_BRAND_PAGE_BIG;
                break;
            case 14://品牌主页 1+1
                homeCell = new ComponentBrandPage1And1Cell();
                homeCell.itemType = ResType.HOME_ITEM_TYPE_BRAND_PAGE_1_1;
                break;
            default:
                homeCell = getCellAccordingToMemberOld(columnMember);
        }

        return homeCell;
    }

    /**
     * 212之前的逻辑
     *
     * @param columnMember
     * @return
     */
    private static HomeCell getCellAccordingToMemberOld(ColumnMember columnMember) {
        HomeCell homeCell = null;
        int itemType;
        if (columnMember instanceof BroadcastDetailColumnMember) {
            homeCell = new BroadcastCell();
            itemType = ResType.BROADCAST_TYPE;
            ((BroadcastCell) homeCell).freq = ((BroadcastDetailColumnMember) columnMember).getFreq();
        } else if (columnMember instanceof CategoryColumnMember) {
            Map<String, String> extInfo = columnMember.getExtInfo();
            //功能入口，小卡片
            if (extInfo != null && ("SMALL".equals(extInfo.get("displayStyle"))
                    || "BIG".equals(extInfo.get("displayStyle")))) {
                if ("SMALL".equals(extInfo.get("displayStyle"))) {
                    homeCell = new FunctionPairCell();
                } else {
                    homeCell = new FunctionBigCell();
                }
                String[] paths = getPaths(extInfo);
                ((FunctionPairCell) homeCell).firstCode = paths[0];
                ((FunctionPairCell) homeCell).secondCode = paths[1];
            } else {
                homeCell = new GoldenRatioCell();
            }
            itemType = ResType.FUNCTION_ENTER_SMALL;
        } else {
            homeCell = new GoldenRatioCell();
            itemType = ResType.RADIO_TYPE;
        }
        homeCell.itemType = itemType;
        return homeCell;
    }

    private static String[] getPaths(Map<String, String> extInfo) {
        String[] codes = new String[]{"0", "0"};
        String categoryPath = extInfo.get("categoryPath");
        if (!TextUtils.isEmpty(categoryPath)) {
            String[] paths = categoryPath.split("\\/");
            if (paths.length > 0) {
                codes[0] = paths[0];
            }
            if (paths.length > 1) {
                codes[1] = paths[1];
            }
        }
        return codes;
    }

    public static int getItemType(ColumnMember columnMember) {
        int itemType = ResType.RADIO_TYPE;
        if (columnMember instanceof BroadcastDetailColumnMember) {
            itemType = ResType.BROADCAST_TYPE;
        } else if (columnMember instanceof CategoryColumnMember) {
            itemType = ResType.FUNCTION_ENTER_SMALL;
        }
        return itemType;
    }

}
