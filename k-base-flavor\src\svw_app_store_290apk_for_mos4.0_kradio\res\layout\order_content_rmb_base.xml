<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/x960"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:background="@color/order_bg_color">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:background="@color/order_info_bg_color"
        android:id="@+id/ll_order_top"
        android:layout_marginLeft="@dimen/order_pay_margin_left"
        android:layout_marginRight="@dimen/order_pay_margin_right"
        android:layout_width="match_parent"
        android:layout_height="@dimen/order_content_top_height"
        android:orientation="horizontal"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toTopOf="@id/ll_order_bottom">

        <RelativeLayout
            android:id="@+id/rl_order_content"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:paddingTop="@dimen/m51"
            android:paddingLeft="@dimen/m44"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/fl_order_qrcode"
            app:layout_constraintBottom_toBottomOf="parent">

            <FrameLayout
                android:id="@+id/fl_image"
                android:layout_width="@dimen/m200"
                android:layout_height="@dimen/m200">

                <ImageView
                    android:id="@+id/iv_order_info_image"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/Blue">
                </ImageView>

                <TextView
                    android:visibility="gone"
                    android:id="@+id/tv_tag"
                    android:layout_width="@dimen/m76"
                    android:layout_height="@dimen/m38"
                    android:gravity="center"
                    android:text=""
                    android:textSize="@dimen/text_size3"
                    android:shadowColor="#80000000"
                    android:shadowDx="0"
                    android:shadowDy="1"
                    android:shadowRadius="3"
                    android:background="@drawable/order_album_image_tag_bg">
                </TextView>
                <ImageView
                    android:visibility="gone"
                    android:id="@+id/iv_tag"
                    android:layout_width="@dimen/x76"
                    android:layout_height="@dimen/y38"
                    android:layout_alignStart="@+id/iv_item_home_cover"
                    android:layout_alignTop="@+id/iv_item_home_cover"
                    android:scaleType="centerCrop"
                    android:src="@drawable/icon_vip" />
            </FrameLayout>

            <RelativeLayout
                android:id="@+id/rl_info_rmb"
                android:layout_toRightOf="@id/fl_image"
                android:layout_marginLeft="@dimen/m60"
                android:paddingTop="@dimen/m16"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <LinearLayout
                    android:id="@+id/ll_order_rmb_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">
                    <TextView
                        android:id="@+id/tv_order_rmb_title"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:maxLines="2"
                        android:text=""
                        android:textColor="@color/text_color_white"
                        android:textSize="@dimen/text_size5"/>

                    <TextView
                        android:visibility="gone"
                        android:id="@+id/tv_order_rmb_tag"
                        android:layout_marginLeft="@dimen/m10"
                        android:padding="@dimen/m5"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:text=""
                        android:textColor="@color/text_color_white"
                        android:textSize="@dimen/text_size1"
                        android:background="@drawable/order_price_tag"/>

                </LinearLayout>

                <LinearLayout
                    android:layout_below="@id/ll_order_rmb_title"
                    android:layout_marginTop="@dimen/m8"
                    android:id="@+id/ll_order_rmb_origin_price"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/m41"
                    android:orientation="horizontal">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:text="价格"
                        android:textColor="@color/text_color_white"
                        android:textSize="@dimen/text_size3"/>
                    <TextView
                        android:id="@+id/tv_order_rmb_origin_price"
                        android:layout_marginLeft="@dimen/m10"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="    "
                        android:textColor="@color/order_money_delete_color"
                        android:textSize="@dimen/text_size3"/>
                </LinearLayout>

                <LinearLayout
                    android:layout_below="@id/ll_order_rmb_origin_price"
                    android:id="@+id/ll_order_rmb_current_price"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="horizontal">
                    <TextView
                        android:id="@+id/tv_order_rmb_current"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="折扣价"
                        android:textColor="@color/text_color_white"
                        android:textSize="@dimen/text_size3"/>
                    <TextView
                        android:id="@+id/tv_order_rmb_current_price"
                        android:layout_marginLeft="@dimen/m10"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="    "
                        android:textColor="@color/order_money_color"
                        android:textSize="50px"
                        android:textStyle="bold"/>
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="元"
                        android:textColor="@color/order_money_color"
                        android:textSize="@dimen/text_size1"/>
                </LinearLayout>
            </RelativeLayout>

        </RelativeLayout>

        <FrameLayout
            android:id="@+id/fl_order_qrcode"
            android:layout_width="@dimen/x196"
            android:layout_height="match_parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toRightOf="@id/rl_order_content"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <LinearLayout
                android:id="@+id/ll_order_rmb"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center">

                <FrameLayout
                    android:layout_width="@dimen/m160"
                    android:layout_height="@dimen/m160"
                    android:background="@color/order_qrcode_bg_color">

                    <LinearLayout
                        android:id="@+id/ll_qrcode_failed"
                        android:layout_margin="@dimen/m14"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:background="@drawable/order_qrcode_failed_bg">
                        <ImageView
                            android:id="@+id/iv_qrcode_refresh"
                            android:layout_width="@dimen/m32"
                            android:layout_height="@dimen/m32"
                            android:src="@drawable/order_loading"/>
                        <TextView
                            android:id="@+id/tv_qrcode_failed"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/text_color_white"
                            android:textSize="@dimen/text_size1"
                            android:text=""
                            android:gravity="center"/>
                    </LinearLayout>

                    <ImageView
                        android:id="@+id/iv_qrcode"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent" />
                </FrameLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingTop="@dimen/m17"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="@string/pay_open_third_method"
                        android:textColor="@color/order_qrcode_sub_color"
                        android:textSize="@dimen/text_size3" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="@string/pay_scan_to_pay"
                        android:textColor="@color/order_qrcode_sub_color"
                        android:textSize="@dimen/text_size3" />
                </LinearLayout>
            </LinearLayout>

        </FrameLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ll_order_bottom"
        android:layout_marginLeft="@dimen/order_pay_margin_left"
        android:layout_marginRight="@dimen/order_pay_margin_right"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/m12"
        android:paddingBottom="@dimen/m23"
        android:orientation="vertical"
        app:layout_constraintTop_toBottomOf="@id/ll_order_top"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <LinearLayout
            android:id="@+id/ll_notice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent">

            <TextView
                android:id="@+id/tv_rmb_buy_notice"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/y30"
                android:gravity="center"
                android:text="购买须知"
                android:textColor="@color/order_qrcode_title_color"
                android:textSize="@dimen/text_size3"/>

            <TextView
                android:id="@+id/tv_buy_notice_1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="1.数字音频作品购买成功后不可退换，解释权归央广新媒体文化传媒（北京）有限公司所有。"
                android:textColor="@color/order_desc_txt_color"
                android:textSize="@dimen/text_size0"/>

            <TextView
                android:id="@+id/tv_buy_notice_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="2.云听车载仅支持人民币支付，若您的账号内有云币余额可前往手机云听APP购买相关数字音频作品。"
                android:textColor="@color/order_desc_txt_color"
                android:textSize="@dimen/text_size0"/>

            <TextView
                android:id="@+id/tv_buy_notice_3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="3.兑换关系：1云币=1元。"
                android:textColor="@color/order_desc_txt_color"
                android:textSize="@dimen/text_size0"/>
            <TextView
                android:id="@+id/tv_rmb_buy_notice_4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="4.云听车载应用内使用云币支付为免密支付，数字音频作品购买成功后不可退款。"
                android:textColor="@color/order_desc_txt_color"
                android:textSize="@dimen/text_size0"/>
            <TextView
                android:id="@+id/tv_buy_notice_5"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="5.云币仅限在云听平台内消费使用，充值后不可提现。"
                android:textColor="@color/order_desc_txt_color"
                android:textSize="@dimen/text_size0"/>
            <TextView
                android:id="@+id/tv_buy_notice_6"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="6.根据苹果公司政策规定，其他平台与ios设备上充值的云听币不能相互通用。"
                android:textColor="@color/order_desc_txt_color"
                android:textSize="@dimen/text_size0"/>
            <TextView
                android:id="@+id/tv_buy_notice_7"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="7.可添加云听客服微信：yuntingkefu，咨询开发票相关事宜。"
                android:textColor="@color/order_desc_txt_color"
                android:textSize="@dimen/text_size0"/>
        </LinearLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_count_down"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textSize="@dimen/text_size3"
        android:textColor="@color/order_title_color"
        android:layout_marginRight="@dimen/m25"
        android:layout_marginBottom="@dimen/m28"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>