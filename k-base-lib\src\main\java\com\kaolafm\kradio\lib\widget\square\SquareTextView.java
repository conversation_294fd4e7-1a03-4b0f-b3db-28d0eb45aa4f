package com.kaolafm.kradio.lib.widget.square;

import android.content.Context;
import android.content.res.TypedArray;
import androidx.appcompat.widget.AppCompatTextView;
import android.util.AttributeSet;
import android.view.MotionEvent;

import com.kaolafm.kradio.lib.R;
import com.kaolafm.kradio.lib.utils.AnimUtil;

/**
 * Created by kaolafm on 2018/4/25.
 */

public class SquareTextView extends AppCompatTextView {

    private boolean canScale;

    public SquareTextView(Context context) {
        this(context, null);
    }

    public SquareTextView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SquareTextView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        setMeasuredDimension(getDefaultSize(0, widthMeasureSpec), getDefaultSize(0, heightMeasureSpec));
        int childWidthSize = getMeasuredWidth();
        //高度和宽度一样
        heightMeasureSpec = widthMeasureSpec = MeasureSpec.makeMeasureSpec(childWidthSize, MeasureSpec.EXACTLY);
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }

    private void init(Context context, AttributeSet attrs) {
        TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.SquareTextView);
        canScale = ta.getBoolean(R.styleable.SquareTextView_canScale, true);
        ta.recycle();
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (canScale) {
            int action = event.getAction();
            switch (action) {
                case MotionEvent.ACTION_DOWN:
                    AnimUtil.startScalePress(this);
                    break;
                case MotionEvent.ACTION_OUTSIDE:
                case MotionEvent.ACTION_CANCEL:
                case MotionEvent.ACTION_UP:
                    AnimUtil.startScaleRelease(this);
                    break;
                default:
                    break;
            }
        }
        return super.onTouchEvent(event);
    }

}
