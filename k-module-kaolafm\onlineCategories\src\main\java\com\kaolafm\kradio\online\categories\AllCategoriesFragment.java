package com.kaolafm.kradio.online.categories;

import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.os.Handler;
import androidx.annotation.CallSuper;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;
import android.util.Log;
import android.view.View;
import android.view.ViewStub;
import android.widget.ImageView;
import android.widget.TextView;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.common.widget.NotScrollViewPager;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.flavor.AllCategoriesCustomTitleInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioC211ViewSizeInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioClickRetryInter;
import com.kaolafm.kradio.lib.base.flavor.SkeletonInter;
import com.kaolafm.kradio.lib.base.ui.online.BaseViewPagerFragment;
import com.kaolafm.kradio.lib.event.PagerJumpEvent;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkManager;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.widget.tab.OnTabSelectListener;
import com.kaolafm.kradio.lib.widget.tab.SlidingTabLayout;
import com.kaolafm.kradio.lib.widget.tab.Tab;
import com.kaolafm.kradio.online.categories.radio.RadioTabFragment;
import com.kaolafm.kradio.online.common.event.OnlineShowCityTabEvent;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.online.categories.adapter.CategoriesFragmentAdapter;
import com.kaolafm.kradio.online.categories.broadcast.BroadcastListFragment;
import com.kaolafm.opensdk.http.error.ApiException;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

/**
 * 所有分类页面，包括音乐、电台、收音机等。
 *
 * <AUTHOR>
 * @date 2018/4/16
 */
@Route(path = RouterConstance.ONLINE_URL_CATEGORIES)
public class AllCategoriesFragment extends BaseBackFragment<AllCategoriesPresenter> implements IAllCategoriesView {
    final String TAG = AllCategoriesFragment.class.getSimpleName();
    public static int MAX_REQUEST_NUMBER = 28;

    View vRootCate;
    ViewStub mNoNetwork;
    View mNoNetWorkRl;
    View loading;
    boolean isDataReceived;
    boolean loadDataError;
    SlidingTabLayout mTabLayout;
    NotScrollViewPager mViewPager;
    private CategoriesFragmentAdapter mFragmentAdapter;
    private View mContentView;
    /**
     * 要显示的一级codeId
     */
    private long mFatherCode = 0;
    /**
     * 要显示的二级codeId
     */
    private long mSonCode = 0;
    private NetWorkListener mNetWorkListener;
    private int tabPosition = 0;


    private String mTargetChildFragmentPageId;//子Fragment中要求选中的子Fragment的PageId

    /**
     * 得到全局分类ui
     *
     * @param fatherCode 要显示的一级codeId
     * @param sonCode    要显示的二级codeId
     * @return AllCategoriesFragment
     */
    public static AllCategoriesFragment getInstance(long fatherCode, long sonCode) {
        AllCategoriesFragment allCategoriesFragment = new AllCategoriesFragment();
        allCategoriesFragment.mFatherCode = fatherCode;
        allCategoriesFragment.mSonCode = sonCode;
        return allCategoriesFragment;
    }

    public void setCode(long fatherCode, long sonCode) {
        mFatherCode = fatherCode;
        mSonCode = sonCode;
    }

    @Override
    protected void onVisibleChanged(boolean isVisible) {
        super.onVisibleChanged(isVisible);
        if (mFragmentAdapter == null || mTabLayout == null) return;
        if (mFragmentAdapter.getItem(mTabLayout.getCurrentPosition()) instanceof RadioTabFragment) {
            ((RadioTabFragment) mFragmentAdapter.getItem(mTabLayout.getCurrentPosition())).setUserVisibleHint(isVisible);
        }
    }

    @Override
    public void initView(View view) {
        Log.i("cate", "initView: ");
        super.initView(view);

        View titleView = View.inflate(getContext(), R.layout.online_bbf_title_center_tablayout, null);
        this.addTitleCenterView(titleView);

        mContentView = View.inflate(getContext(), R.layout.online_fragment_all_categories, null);
        this.addContentView(mContentView);

        mTabLayout = (SlidingTabLayout) titleView.findViewById(R.id.stb_all_category_title_name);
        KRadioC211ViewSizeInter inter = ClazzImplUtil.getInter("KRadioC211ViewSizeImpl");
//        if (inter != null && inter.isNeedReset()) {
//            mTabLayout.getLayoutParams().height = ResUtil.getDimen(R.dimen.y80);
//        }
        vRootCate = mContentView.findViewById(R.id.vRootCate);
        loading = mContentView.findViewById(R.id.loading);
        mViewPager = mContentView.findViewById(R.id.vp_all_category_content);


        //配合lazyfragment,避免预加载
        /**
         * 在全部页听ai电台下的电台，再听在线广播，两个都高亮显示
         */
        mViewPager.setOffscreenPageLimit(0);
        mViewPager.setScanScroll(false);
        mViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int i, float v, int i1) {

            }

            @Override
            public void onPageSelected(int i) {
                mTabLayout.setCurrentTab(i);
            }

            @Override
            public void onPageScrollStateChanged(int i) {

            }
        });
        mTabLayout.setOnTabSelectListener(new OnTabSelectListener() {
            @Override
            public void onTabSelect(int position) {
                NetworkUtil.isNetworkAvailable(getContext(), true);
                mViewPager.setCurrentItem(position, true);
                mTabLayout.getTab(position).tabView.setBackground(ResUtil.getDrawable(R.drawable.online_tab_class_all_indicator_bg));
                mTabLayout.getTab(position).tabView.getPaint().setFakeBoldText(true);

                mTabLayout.getTab(tabPosition).tabView.getPaint().setFakeBoldText(false);
                mTabLayout.getTab(tabPosition).tabView.setBackground(new ColorDrawable());
                tabPosition = position;
                EventBus.getDefault().post(new OnlineShowCityTabEvent(false));
//                //点击上报
//                ButtonClickReportEvent event;
//                switch (mTabLayout.getTab(position).tabView.getText().toString()) {
//                    case "AI电台":
//                        event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_CLASS_AI);
//                        ReportHelper.getInstance().addEvent(event);
//                        break;
//                    case "专栏节目":
//                        event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_CLASS_ALBUM);
//                        ReportHelper.getInstance().addEvent(event);
//                        break;
//                    case "在线广播":
//                        event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_CLASS_GB);
//                        ReportHelper.getInstance().addEvent(event);
//                        break;
//                    case "听电视":
//                        event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_CALSS_TV);
//                        ReportHelper.getInstance().addEvent(event);
//                        break;
//                }
            }

            @Override
            public void onTabReselect(int position) {
            }
        });

    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
//        showLoading();
        mPresenter.loadData();
//        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
        mNetWorkListener = new NetWorkListener(this);
        NetworkManager.getInstance().addNetworkReadyListener(mNetWorkListener);
//        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!NetworkUtil.isNetworkAvailable(getContext(), true)) {
            showNoNetWorkView();
        }
    }

    @Override
    public void onEnterAnimationEnd(Bundle savedInstanceState) {
        Log.i("cate", "onEnterAnimationEnd:");
        super.onEnterAnimationEnd(savedInstanceState);
//        mPresenter.getData(mCategoryId, mSubcategoryId);
//        mPresenter.loadData();

//        int expand = (int) getResources().getDimension(R.dimen.m60);
//        ViewUtil.expandViewTouchDelegate(vClose, expand, expand, expand, expand);
    }


    @Override
    protected AllCategoriesPresenter createPresenter() {
        return new AllCategoriesPresenter(this, mFatherCode, mSonCode);
    }

    @Override
    public void showData(String[] titles, String[] code, List<Fragment> fragments, int showIndex) {
        //下边的方法会导致,从广播进入,切到资讯,然后资讯下边的最后几个子tab的数据不能显示出来
//        for (int i = 0; i < titles.length; i++) {
//            Log.i(TAG, "showData: titles = " + titles[i]);
//        }
        //如果存在要显示的子Fragment的pageId，则设置显示
        if (mTargetChildFragmentPageId != null && !ListUtil.isEmpty(fragments)) {
            for (int i = 0; i < fragments.size(); i++) {
                Fragment fragment = fragments.get(i);
                if (fragment instanceof BaseViewPagerFragment) {
                    if (((BaseViewPagerFragment) fragment).getPageId().equals(mTargetChildFragmentPageId)) {
                        showIndex = i;
                        mTargetChildFragmentPageId = null;
                        break;
                    }
                }
            }
        }

        //记录获取到data的状态
        isDataReceived = true;
        hideLoading();
        mFragmentAdapter = new CategoriesFragmentAdapter(getChildFragmentManager(), fragments, titles);
        if (titles != null && titles.length > 0) {
            List<Tab> tabs = new ArrayList<>();
            AllCategoriesCustomTitleInter inter = ClazzImplUtil.getInter("AllCategoriesCustomTitleImpl");

            for (int i = 0; i < titles.length; i++) {
                Tab tab = new Tab();
                if (null != inter) {
                    tab.title = inter.TitleCustom(titles[i]);
                } else {
                    tab.title = titles[i];
                }
                tab.position = i;
                tab.select = i == showIndex;
                tabs.add(tab);
            }
            mTabLayout.setTabs(tabs);
        }
        for (int i = 0; i < code.length; i++) {
            if (code[i].equals(mFatherCode)) {
                showIndex = i;
                break;
            }
        }

        mViewPager.setAdapter(mFragmentAdapter);
        mFragmentAdapter.notifyDataSetChanged();
//        mTabLayout.setViewPager(mViewPager);
        mTabLayout.setCurrentTab(showIndex);
        mViewPager.setCurrentItem(showIndex);
        //刷新一下tab，不然断网再重连会出现字体高亮不随着位置显示。
        mTabLayout.notifyDataSetChanged();
        tabPosition = showIndex;
        mTabLayout.getTab(tabPosition).tabView.setBackground(ResUtil.getDrawable(R.drawable.online_tab_class_all_indicator_bg));
    }

    @Override
    public void showError(Exception e) {
        Log.i(TAG, "showError: error = " + e.getMessage());
        //todo 网络异常时，记录当前状态
        loadDataError = true;
        hideLoading();
        String str = null;
        if (e instanceof ApiException) {
            switch (((ApiException) e).getCode()) {
                case ErrorCode.NO_NET:
                case ErrorCode.NET_TIME_OUT:
                    //str = ResUtil.getString(R.string.no_net_work_str);
                    // 解决https://app.huoban.com/tables/2100000007530121/items/2300001944559937?userId=1229522问题
                    if (mFragmentAdapter == null || mFragmentAdapter.getCount() == 0) {
                        showNoNetWorkView();
                    }
                    break;
                case com.kaolafm.opensdk.http.error.ErrorCode.HTTPS_CERTIFICATE_ERROR:
                    showNoNetWorkView(ResUtil.getString(R.string.home_network_certificate_error), true);
                    break;
                case ErrorCode.NO_SUBCATEGORY:
                case ErrorCode.TYPE_ERROR:
                    str = ResUtil.getString(R.string.online_error_subcategory_is_null);
                    break;
                default:
            }
        }

        if (str != null) {
            ToastUtil.showError(getContext(), str);
        }
    }


    @Override
    public boolean useEventBus() {
        return true;
    }

    @Subscribe
    public void onEvent(PagerJumpEvent event) {
        if (event != null) {
            if (event.page == PagerJumpEvent.PAGE_BROADCAST_LIST && event.bundle != null) {
                int id = event.bundle.getInt("id");
                String name = event.bundle.getString("name");
                extraTransaction().start(BroadcastListFragment.newInstance(id, name));
            }
        }
    }

    /***************************************************************************************************************/
//    private ViewSkeletonScreen mSkeleton;
    @Override
    protected void hideLoading() {
        super.hideLoading();
        ViewUtil.setViewVisibility(vRootCate, View.GONE);
//        if (mSkeleton != null) {
//            mSkeleton.hide();
//        }
        loading.setVisibility(View.GONE);
    }

    @Override
    protected void showLoading() {
        super.showLoading();
        SkeletonInter skeletonInter = ClazzImplUtil.getInter("SkeletonInterImpl");
        if (skeletonInter != null) {
            //延时显示skeleton框架，避免网络好的情况下出现一闪而过的加载页面
            //如果在此时间内数据已经加载出来，就取消显示
            if (skeletonInter.showSkeleton()) {
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        //如果在延迟时间后，数据仍没有显示出来，并且没有其他异常情况，则显示加载框架
                        if (!isDataReceived && !loadDataError) {
                            showSkeleton();
                        }
                    }
                }, skeletonInter.getDelayInterval());
            } else {
                Log.i(TAG, "dispose show skeleton");
            }
        } else {
            showSkeleton();
        }
    }

    private void showSkeleton() {
        loading.setVisibility(View.VISIBLE);
//        ViewUtil.setViewVisibility(vRootCate, View.VISIBLE);
//        if (mSkeleton == null) {
//            //骨架
//            mSkeleton = Skeleton.bind(vRootCate)
//                    .load(R.layout.layout_cate_skeleton)
//                    .shimmer(true)
//                    .show();
//        } else {
//            mSkeleton.show();
//        }
    }

    /***************************************************************************************************************/

    @Override
    @CallSuper
    protected void showAccordingToScreen(int orientation) {
        super.showAccordingToScreen(orientation);
//        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
//            mTabLayout.setTabPadding(ScreenUtil.px2dp(ResUtil.getDimen(R.dimen.x35)));
//        } else {
//            mTabLayout.setTabPadding(ScreenUtil.px2dp(ResUtil.getDimen(R.dimen.x20)));
//        }
        changeTabTextSize(true);
    }

    /**
     * 修改"所有"页面中的一级标题字体大小
     *
     * @param needChange boolean值
     */
    public void changeTabTextSize(boolean needChange) {
        if (needChange) {
            int textSize = ResUtil.getDimen(R.dimen.online_nav_bar_tv_unselected);
            int textSizeSelected = ResUtil.getDimen(R.dimen.online_category_nav_bar_tv_selected);
            mTabLayout.setTextSize(textSize);
            mTabLayout.setTextSelectedSize(textSizeSelected);
            Log.i(TAG, "changeTabTextSize:" + textSize + " " + textSizeSelected);
        }
    }

    public void showNoNetWorkView() {
        if (mContentView == null) {
            return;
        }
        if (mNoNetwork == null) {
            mNoNetwork = mContentView.findViewById(R.id.all_category_no_network);
            mNoNetWorkRl = mNoNetwork.inflate();
        }
        TextView tvNetworkNosign = mNoNetWorkRl.findViewById(R.id.tv_error);
        tvNetworkNosign.setText(ResUtil.getString(R.string.network_nosigin));
        ViewUtil.setViewVisibility(mNoNetWorkRl, View.VISIBLE);
        ViewUtil.setViewVisibility(mViewPager, View.GONE);
        ViewUtil.setViewVisibility(mTabLayout, View.GONE);
    }

    public void showNoNetWorkView(String error, boolean clickToRetry) {
        if (mContentView == null) {
            return;
        }

        if (mNoNetwork == null) {
            mNoNetwork = mContentView.findViewById(R.id.all_category_no_network);
            mNoNetWorkRl = mNoNetwork.inflate();
        }
        TextView tvNetworkNosign = mNoNetWorkRl.findViewById(R.id.tv_error);
        tvNetworkNosign.setText(error);
        // 支持点击重试
        KRadioClickRetryInter mKRadioClickRetryInter = ClazzImplUtil.getInter(
                "KRadioClickRetryInterImpl");
        if (clickToRetry || (mKRadioClickRetryInter != null
                && mKRadioClickRetryInter.canRetry())) {
            ImageView ivNetworkNoSign = mNoNetWorkRl.findViewById(R.id.iv_error);
            ivNetworkNoSign.setOnClickListener(v -> {
                hideErrorLayout();
//                showLoading();
                new Handler().postDelayed(() -> mPresenter.loadData(), 100);
            });
        }
        ViewUtil.setViewVisibility(mNoNetWorkRl, View.VISIBLE);
        ViewUtil.setViewVisibility(mViewPager, View.GONE);
        ViewUtil.setViewVisibility(mTabLayout, View.GONE);
    }

    private void hideErrorLayout() {
        ViewUtil.setViewVisibility(mNoNetWorkRl, View.GONE);
        ViewUtil.setViewVisibility(mViewPager, View.VISIBLE);
        ViewUtil.setViewVisibility(mTabLayout, View.VISIBLE);
    }

    public class NetWorkListener implements NetworkManager.INetworkReady {
        private WeakReference allCategoriesFragmentWeakReference;

        public NetWorkListener(AllCategoriesFragment allCategoriesFragment) {
            allCategoriesFragmentWeakReference = new WeakReference<>(allCategoriesFragment);
        }

        @Override
        public void networkChange(boolean hasNetwork) {
            Log.i("networkChange", "Network state changed, param [hasNetwork] value is : " + hasNetwork);
            if (!hasNetwork) {
                showNoNetWorkView(ResUtil.getString(R.string.network_nosigin), true);
                return;
            }

            if (mPresenter != null) {
                hideErrorLayout();
//                showLoading();
                mPresenter.loadData();
            }
//            NetworkManager.getInstance().removeNetworkReadyListener(mNetWorkListener);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        isDataReceived = false;
        NetworkManager.getInstance().removeNetworkReadyListener(mNetWorkListener);
    }

    public boolean isReportFragment() {
        return true;
    }

    /**
     * 切换子Fragment
     *
     * @param pageId
     */
    public void selectChildFragment(String pageId) {
        switch (pageId) {
            case Constants.PAGE_ID_CATEGORIES_BROADCAST:    //分类--广播tab页面
            case Constants.PAGE_ID_CATEGORIES_TV:   //分类--电视tab页面
            case Constants.PAGE_ID_CATEGORIES_AI:   //分类--AI电台tab页面
            case Constants.PAGE_ID_CATEGORIES_ALBUM:    //分类--专辑tab页面
                mTargetChildFragmentPageId = pageId;
                break;
            default:
                mTargetChildFragmentPageId = null;
                break;
        }
        if (mTargetChildFragmentPageId == null) return;
        if (mFragmentAdapter != null) {
            List<Fragment> fragments = mFragmentAdapter.getFragments();
            if (fragments != null) {
                for (int i = 0; i < fragments.size(); i++) {
                    Fragment fragment = fragments.get(i);
                    if (fragment instanceof BaseViewPagerFragment) {
                        if (((BaseViewPagerFragment) fragment).getPageId().equals(String.valueOf(pageId))) {
                            mTabLayout.getTab(i).tabView.performClick();
                            mTargetChildFragmentPageId = null;
                        }
                    }
                }
            }
        }
    }
}
