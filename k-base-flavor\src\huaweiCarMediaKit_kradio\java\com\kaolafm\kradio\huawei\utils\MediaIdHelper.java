package com.kaolafm.kradio.huawei.utils;

import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.kradio.common.SubscribeData;
import com.kaolafm.kradio.common.bean.HistoryItem;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.opensdk.api.broadcast.ProgramDetails;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.api.search.model.SearchProgramBean;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

public class MediaIdHelper {
    private static final String TAG = MediaIdHelper.class.getSimpleName();

    //由于华为sdk只支持存放一个id，不支持type，所以将id和content type拼在一起存放
    public static String getIdAndType(String id, int contentType) {
        String mediaId = id + "_" + contentType;
        Logger.i(TAG, mediaId);
        return mediaId;
    }

    public static String getIdAndType(String id, int contentType, String pId) {
        String mediaId = id + "_" + contentType + "_" + pId;
        Logger.i(TAG, mediaId);
        return mediaId;
    }

    //
    public static String getPlayId(String mediaId) {
        if (TextUtils.isEmpty(mediaId)) {
            return "";
        }
        String[] results = mediaId.split("_");
        if (results.length > 0) {
            return results[0];
        }
        return mediaId;
    }

    public static int getContentType(String mediaId) {
        String[] results = mediaId.split("_");
        if (results.length > 1) {
            try {
                return Integer.parseInt(results[1]);
            } catch (NumberFormatException e) {
                Log.e(TAG, e.toString());
            }
        }
        return -1;
    }

    public static String getMediaId(PlayItem item) {
        return getIdAndType(String.valueOf(item.getAudioId()), item.getType());
    }

    public static String getMediaId(PlayItem item, boolean isAlbum) {
        String mediaId;
        if (isAlbum) {
            mediaId = getIdAndType(String.valueOf(item.getRadioId()), item.getType());
        } else {
            mediaId = getIdAndType(String.valueOf(item.getAudioId()), item.getType());
        }

        return mediaId;
    }

    public static String getMediaId(HistoryItem item) {
        String mediaId;
        mediaId = getIdAndType(item.getRadioId(), Integer.parseInt(item.getType()));

        return mediaId;
    }

    public static String getMediaId(SubscribeData item) {
        String mediaId;
        mediaId = getIdAndType(String.valueOf(item.getId()), item.getType());

        return mediaId;
    }

    public static String getMediaId(ProgramDetails item) {
        String mediaId;
        mediaId = getIdAndType(String.valueOf(item.getProgramId()), PlayerConstants.RESOURCES_TYPE_BROADCAST);

        return mediaId;
    }

    public static String getMediaId(SearchProgramBean bean) {
        return getIdAndType(String.valueOf(bean.getId()), bean.getType());
    }

    public static String getMediaId(AudioDetails audioDetails) {
        return getIdAndType(String.valueOf(audioDetails.getAudioId()), PlayerConstants.RESOURCES_TYPE_AUDIO);
    }
}
