package com.kaolafm.kradio.history.utils;

import android.content.Context;
import android.os.Looper;

import com.kaolafm.kradio.history.UploadListenHistoryManager;
import com.kaolafm.kradio.history.HistoryManager;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.opensdk.api.history.SaveHistoriesRequest;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.RadioPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

/**
 * 用于保存历史的播放器监听。
 * <AUTHOR>
 * @date 2020/8/20
 */
public class SaveHistoryPlayStateListener extends BasePlayStateListener {

    private HistoryManager mHistoryManager;

    private SaveHistoriesRequest mSaveHistoriesRequest;

    private PlayItem prePlayItem;

    private PlayItem mPlayItem;

    public SaveHistoryPlayStateListener(HistoryManager historyManager) {
        mHistoryManager = historyManager;
        mSaveHistoriesRequest = new SaveHistoriesRequest();
    }

    @Override
    public void onIdle(PlayItem playItem) {
        super.onIdle(playItem);
    }

    private void reportPlayAudio() {
        //登录状态下不需要上报碎片状态
        if (mHistoryManager.isLogin()) {
            return;
        }
        if (mPlayItem == null) {
            return;
        }
        Context context = AppDelegate.getInstance().getContext();
        if (!NetworkUtil.isNetworkAvailable(context,false)) {
            return;
        }
        boolean isRadio = mPlayItem instanceof RadioPlayItem;
        mSaveHistoriesRequest.updateAudioState(mPlayItem.getAudioId(), isRadio ? 1 : 0, 0, null);
    }

    private void saveHistory(PlayItem playItem, boolean donotUpdateUI) {
        mHistoryManager.saveHistory(playItem, donotUpdateUI);
    }

    @Override
    public void onProgress(PlayItem playItem, long progress, long total) {
        super.onProgress(playItem, progress, total);
        mHistoryManager.progress(playItem, progress);
    }

    @Override
    public void onSeekStart(PlayItem playItem) {
        super.onSeekStart(playItem);
        UploadListenHistoryManager.getInstance().setCurrentPlayInvalid();
    }

    @Override
    public void onBufferingStart(PlayItem playItem) {
        super.onBufferingStart(playItem);
        UploadListenHistoryManager.getInstance().setCurrentPlayInvalid();
    }

    @Override
    public void onBufferingEnd(PlayItem playItem) {
        super.onBufferingEnd(playItem);
        UploadListenHistoryManager.getInstance().setInvalidTime(false);
    }

    @Override
    public void onPlayerPlaying(PlayItem playItem) {
        super.onPlayerPlaying(playItem);
        UploadListenHistoryManager.getInstance().setInvalidTime(false);
        mPlayItem = playItem;
        if (prePlayItem == null) {
            prePlayItem = playItem;
            saveHistory(playItem, false);
        } else {
            saveHistory(prePlayItem, true);
            // 同时保存两条历史记录会出现插入时间相同的问题，导致查询的时候当前播放的item不会跑到前面
            new android.os.Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                @Override
                public void run() {
                    saveHistory(playItem, false);
                }
            }, 20);
            prePlayItem = playItem;
        }
        reportPlayAudio();
    }
}
