package com.kaolafm.kradio.flavor.impl;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.media.AudioAttributes;
import android.media.AudioFocusRequest;
import android.media.AudioManager;
import android.os.Build;
import android.util.Log;


import com.ecarx.sdk.openapi.ECarXApiClient;
import com.ecarx.sdk.policy.IAudioAttributes;
import com.ecarx.sdk.policy.PolicyAPI;
import com.kaolafm.kradio.bluetooth.BluetoothStateManager;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.opensdk.player.core.model.AAudioFocus;
import com.kaolafm.opensdk.player.core.utils.AudioFocusManager;
import com.kaolafm.opensdk.player.core.utils.PlayerCustomizeManager;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;

/**
 * 音源管理类 （替代AudioFocusManager功能）
 */
public class AudioSourceManager extends AAudioFocus {
    private IAudioAttributes mAudioAttributes;
    private final static String TAG = "AudioSourceManager";
    public static boolean mIsInSource = false;
    private AudioManager mAudioManager;
    private AudioManager.OnAudioFocusChangeListener mOnAudioFocusChangeListener = (focusChange) -> {
        this.notifyAudioFocusChange(true, focusChange);
    };

    public AudioSourceManager(Context context) {
        mAudioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
        if (android.os.Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            try {
                PolicyAPI policyAPI = PolicyAPI.createPolicyAPI(AppDelegate.getInstance().getContext());
                policyAPI.init(AppDelegate.getInstance().getContext(), new ECarXApiClient.Callback() {
                    @SuppressLint("LongLogTag")
                    @Override
                    public void onAPIReady(boolean b) {
                        if (b) {
                            Log.i(TAG, "PolicyAPI  初始化成功");
                            if (policyAPI != null) {
                                //获取音频属性策略实例
                                mAudioAttributes = policyAPI.getAudioAttributes();
                            }
                        } else {
                            Log.i(TAG, "PolicyAPI  初始化失败");
                        }
                    }
                });
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    AudioFocusRequest mAudioFocusRequest;

    @Override
    public boolean requestAudioFocus() {
        if (this.mAudioManager == null) {
            return false;
        }
        if (android.os.Build.VERSION.SDK_INT >= Build.VERSION_CODES.O && mAudioAttributes != null) {
            //此功能接口适用于大于android.os.Build.VERSION_CODES.O的安卓系统版本，android.os.Build.VERSION_CODES.O以下的版本可以使用原生AOSP接口
            int mUsage, mContent;
            Log.i(TAG, "bluetooth state=" + BluetoothStateManager.getInstance().isBluetoothAddedOnPsd());
            if (BluetoothStateManager.getInstance().isBluetoothAddedOnPsd()) {
                mUsage = mAudioAttributes.getAudioAttributesUsage(IAudioAttributes.USAGE_MD_BT_AUDIO);
                mContent = mAudioAttributes.getAudioAttributesContentType(IAudioAttributes.CONTENT_TYPE_MD_BT_AUDIO);
            } else {
                mUsage = mAudioAttributes.getAudioAttributesUsage(IAudioAttributes.USAGE_MD_MEDIA);
                mContent = mAudioAttributes.getAudioAttributesContentType(IAudioAttributes.CONTENT_TYPE_MD_MUSIC);
            }
            AudioAttributes mAudioAttributes = new AudioAttributes.Builder()
                    //通过PolicyAPI的getAudioAttributesUsage接口获取到的mUsage
                    .setUsage(mUsage)
                    //通过PolicyAPI的getAudioAttributesContentType接口获取到的mContent
                    .setContentType(mContent)
                    .build();
            mAudioFocusRequest = new AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN)
                    .setAudioAttributes(mAudioAttributes)
                    .setOnAudioFocusChangeListener(mOnAudioFocusChangeListener)
                    .build();
            Log.d(getClass().getSimpleName(), "mAudioAttributes=" + mAudioAttributes.toString());
            int result = mAudioManager.requestAudioFocus(mAudioFocusRequest);
            return result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED;
        } else {
            if (mAudioManager == null) {
                return false;
            }

            PlayerCustomizeManager.getInstance().beforeRequestAudioFocus(mAudioManager);
            int result = mAudioManager.requestAudioFocus(mOnAudioFocusChangeListener, AudioManager.STREAM_MUSIC,
                    AudioManager.AUDIOFOCUS_GAIN);
            PlayerLogUtil.log(getClass().getSimpleName(), "requestAudioFocus", "result = " + result);
            notifyAudioFocusChange(false, result);
            return result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED;
        }

    }

    @Override
    public boolean abandonAudioFocus() {
        if (mAudioManager == null) {
            return false;
        }
        int rect;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O && mAudioFocusRequest != null) {
            rect = mAudioManager.abandonAudioFocusRequest(mAudioFocusRequest);
        } else {
            rect = mAudioManager.abandonAudioFocus(mOnAudioFocusChangeListener);
        }

        PlayerLogUtil.log(getClass().getSimpleName(), "abandonAudioFocus", "result = " + rect);
//        notifyAudioFocusChange(false, rect);
        return rect == AudioManager.AUDIOFOCUS_REQUEST_GRANTED;
    }


}
