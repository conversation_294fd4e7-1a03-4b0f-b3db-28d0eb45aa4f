package com.kaolafm.kradio.categories;

import android.content.res.Configuration;
import android.graphics.Rect;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.State;
import android.util.Log;
import android.view.View;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.bean.SubcategoryItemBean;
import com.kaolafm.kradio.lib.utils.ResUtil;

/**
 * 分类页面各个item之间的间隔。根据类型显示不同的间距
 *
 * <AUTHOR>
 * @date 2018/4/29
 */

public class HorizontalCategoryItemSpace extends CategoryItemSpace {

    int itemGap = 0;

    int firstPorRowTop = 0;

    /**
     * 设置竖屏的顶部间距
     * @param top
     * @return
     */
    public HorizontalCategoryItemSpace setPorFirstRowTop(int top) {
        this.firstPorRowTop = top;
        return this;
    }

    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, State state) {
        final GridLayoutManager gridLayoutManager = (GridLayoutManager) parent.getLayoutManager();
        int itemCount = gridLayoutManager.getItemCount();
        if (itemCount == 0) {
            return;
        }
        int spanCount = gridLayoutManager.getSpanCount();// 每行的列数
        int position = parent.getChildLayoutPosition(view);
        int column = position % spanCount;//列 从0开始
        int itemViewType = parent.getAdapter().getItemViewType(position);
        int right = 0, bottom = 0, left = 0, top = 0;
        switch (itemViewType) {
            //标题
            case SubcategoryItemBean.TYPE_ITEM_TITLE:
                if (position == 0) {
                    left = top = 0;
                } else {
                    top = ResUtil.getDimen(R.dimen.subcategory_item_title_space_top);
                    left = ResUtil.getDimen(R.dimen.subcategory_item_title_space_left);
                }
                right = ResUtil.getDimen(R.dimen.subcategory_item_title_space_right);
                bottom = ResUtil.getDimen(R.dimen.subcategory_item_title_space_bottom);
                break;
            //广播:本地
            case SubcategoryItemBean.TYPE_ITEM_BROADCAST_LOCAL:
                right = ResUtil.getDimen(R.dimen.subcategory_item_broadcast_space_right);
                top = ResUtil.getDimen(R.dimen.subcategory_item_broadcast_space_bottom);
                break;
            //广播:分类
            case SubcategoryItemBean.TYPE_ITEM_BROADCAST_CATEGORY:
                right = ResUtil.getDimen(R.dimen.subcategory_item_broadcast_space_category_right);
                top = ResUtil.getDimen(R.dimen.subcategory_item_broadcast_space_category_bottom);
                break;
            //Ai电台-新闻财经-新闻频道
            case SubcategoryItemBean.TYPE_ITEM_RADIO_CHANNEL:
            case SubcategoryItemBean.TYPE_ITEM_ALBUM:
            case SubcategoryItemBean.TYPE_ITEM_TV:
                right = ResUtil.getDimen(R.dimen.subcategory_item_album_space_right);
                top = ResUtil.getDimen(R.dimen.subcategory_item_album_space_bottom);
                break;
            //榜单
            case SubcategoryItemBean.TYPE_ITEM_CHARTS:
                //QQ-排行榜-官方
            case SubcategoryItemBean.TYPE_ITEM_OFFICIAL_CHARTS:
                right = ResUtil.getDimen(R.dimen.subcategory_item_radio_space_right);
                bottom = ResUtil.getDimen(R.dimen.subcategory_item_radio_space_bottom);
                break;

            default:
                //如果子类没有该类型,调用父类方法
                Log.i("kradio.HCIS", "getItemOffsets: 子类没有该类型:" + itemViewType);
                super.getItemOffsets(outRect, view, parent, state);
        }
        if (ResUtil.getOrientation() == Configuration.ORIENTATION_PORTRAIT) {//竖屏的时候保持左右边距相同，item宽度相同，左右item紧贴recyclerview内边
            int dividerW = right;//以上都是只有右间距的，有变化请修改此处。
            left = column * dividerW / spanCount;
            right = (spanCount - column - 1) * dividerW / spanCount;
        }
        if ((ResUtil.getOrientation() == Configuration.ORIENTATION_PORTRAIT && position / spanCount == 0)) {
            top += firstPorRowTop;
        }
        outRect.set(Math.round(left), top, Math.round(right), Math.round(bottom));

    }

    public void setGap(int orientation) {
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            itemGap = ResUtil.getDimen(R.dimen.m6);
        } else {
            itemGap = ResUtil.getDimen(R.dimen.m30);
        }
    }
}
