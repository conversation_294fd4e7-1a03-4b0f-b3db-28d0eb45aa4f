package com.kaolafm.kradio.component.ui.k_1x1card;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;

import com.kaolafm.kradio.component.ui.R;
import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.component.ui.base.utils.ComponentUtils;
import com.kaolafm.kradio.component.ui.base.utils.VipCornerUtil;
import com.kaolafm.kradio.component.ui.base.view.OvalImageView;
import com.kaolafm.kradio.component.ui.base.CellBinder;
import com.kaolafm.kradio.component.ui.base.ItemClickSupport;
import com.kaolafm.kradio.component.ui.base.view.RateView;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.UrlUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.operation.model.column.ActivityDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.AlbumDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.PageRedirectionColumnMember;

/**
 * 首页组件1+1
 */
public class Component1And1Cell extends HomeCell implements CellBinder<View, Component1And1Cell>, ItemClickSupport {

    View layout_1_1_top_lift;
    View layout_1_1_top_right;

    private OvalImageView card_bg_iv_lift, card_pic_iv_lift;
    private TextView card_title_tv_lift;
    private ImageView card_play_iv_lift;
    private ImageView vip_icon_lift;
    private RateView card_layout_playing_lift;

    private OvalImageView card_bg_iv_right, card_pic_iv_right;
    private TextView card_title_tv_right;
    private ImageView card_play_iv_right;
    private ImageView vip_icon_right;
    private RateView card_layout_playing_right;

    private static final String TAG = "Component1And1Cell";

    // 防止重复应用约束的简单标志
    private boolean mLeftConstraintApplied = false;
    private boolean mRightConstraintApplied = false;


    @Override
    public void mountView(@NonNull Component1And1Cell data, @NonNull View view, int position) {
        // 重置约束应用状态
        mLeftConstraintApplied = false;
        mRightConstraintApplied = false;

        layout_1_1_top_lift=view.findViewById(R.id.layout_1_1_top_lift);
        layout_1_1_top_right=view.findViewById(R.id.layout_1_1_top_right);
        initView();
        setTopViewDate(data);
        setBottomViewDate(data);
    }

    @Override
    public int getItemType() {
        return R.layout.component_1_1_layout;
    }

    private void initView() {
        //上边区域
        card_bg_iv_lift = layout_1_1_top_lift.findViewById(R.id.card_bg_iv);
        card_pic_iv_lift = layout_1_1_top_lift.findViewById(R.id.card_pic_iv);
        card_title_tv_lift = layout_1_1_top_lift.findViewById(R.id.card_title_tv);
        card_play_iv_lift = layout_1_1_top_lift.findViewById(R.id.card_play_iv);
        vip_icon_lift = layout_1_1_top_lift.findViewById(R.id.vip_icon);
        card_layout_playing_lift = layout_1_1_top_lift.findViewById(R.id.card_layout_playing);
        //下边区域
        card_bg_iv_right = layout_1_1_top_right.findViewById(R.id.card_bg_iv);
        card_pic_iv_right = layout_1_1_top_right.findViewById(R.id.card_pic_iv);
        card_title_tv_right = layout_1_1_top_right.findViewById(R.id.card_title_tv);
        card_play_iv_right = layout_1_1_top_right.findViewById(R.id.card_play_iv);
        vip_icon_right = layout_1_1_top_right.findViewById(R.id.vip_icon);
        card_layout_playing_right = layout_1_1_top_right.findViewById(R.id.card_layout_playing);

        // 删除硬编码背景设置，让皮肤框架自动处理
        // layout_1_1_top_lift.setBackground(ResUtil.getDrawable(R.drawable.component_card_bg_1));
        // layout_1_1_top_right.setBackground(ResUtil.getDrawable(R.drawable.component_card_bg_1));


        ViewGroup.LayoutParams para;
        para = card_title_tv_lift.getLayoutParams();
        para.width = ResUtil.getDimen(R.dimen.m166);
        para.height = ViewGroup.LayoutParams.MATCH_PARENT;
        card_title_tv_lift.setLayoutParams(para);

        para = card_title_tv_right.getLayoutParams();
        para.width = ResUtil.getDimen(R.dimen.m166);
        para.height = ViewGroup.LayoutParams.MATCH_PARENT;
        card_title_tv_lift.setLayoutParams(para);


        card_title_tv_lift.setMaxLines(3);
        card_title_tv_right.setMaxLines(3);
        card_title_tv_lift.setEllipsize(TextUtils.TruncateAt.END);
        card_title_tv_right.setEllipsize(TextUtils.TruncateAt.END);
    }

    /**
     * 设置上方区域数据
     *
     * @param data
     */
    private void setTopViewDate(Component1And1Cell data) {
        if (data.contentList != null && data.contentList.size() > 0) {
            String title = "";
            if (!TextUtils.isEmpty(data.getContentList().get(0).getTitle())) {
                title = data.getContentList().get(0).getTitle();
            } else if(!TextUtils.isEmpty(data.getContentList().get(0).getProgramDesc())){
                title = data.getContentList().get(0).getProgramDesc();
            }else if(!TextUtils.isEmpty(data.getContentList().get(0).getProgramTitle())){
                title = data.getContentList().get(0).getProgramTitle();
            }else {
                title = data.getContentList().get(0).getDescription();
            }
            card_title_tv_lift.setText(title);
            if (!TextUtils.isEmpty(UrlUtil.getCardBgUrl(data.contentList.get(0).getImageFiles())))
                ImageLoader.getInstance().displayImage(card_pic_iv_lift.getContext(),
                        UrlUtil.getCardBgUrl(data.contentList.get(0).getImageFiles()), card_bg_iv_lift);
            boolean showPlayBtn = true;
            if (false/*data.getContentList().get(0) instanceof PageRedirectionColumnMember
                    || data.getContentList().get(0) instanceof ActivityDetailColumnMember*/) {
                //页面跳转类型
                card_pic_iv_lift.setVisibility(View.GONE);
                card_play_iv_lift.setVisibility(View.GONE);
                showPlayBtn = false;
            } else {
                card_pic_iv_lift.setVisibility(View.VISIBLE);
                card_play_iv_lift.setVisibility(View.VISIBLE);
                if (!TextUtils.isEmpty(UrlUtil.getCardPicUrl(data.contentList.get(0).getImageFiles()))) {
                    // 避免重复应用约束
                    if (!mLeftConstraintApplied) {
                        ConstraintLayout pCl = (ConstraintLayout) card_pic_iv_lift.getParent();
                        ConstraintSet cs = new ConstraintSet();
                        cs.clone(pCl);
                        cs.clear(card_pic_iv_lift.getId());
                        cs.connect(card_pic_iv_lift.getId(), ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP, 0);
                        cs.connect(card_pic_iv_lift.getId(), ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, 0);
                        cs.connect(card_pic_iv_lift.getId(), ConstraintSet.RIGHT, ConstraintSet.PARENT_ID, ConstraintSet.RIGHT, ResUtil.getDimen(R.dimen.m20));
                        cs.constrainWidth(card_pic_iv_lift.getId(), ResUtil.getDimen(R.dimen.y138));
                        cs.constrainHeight(card_pic_iv_lift.getId(), ResUtil.getDimen(R.dimen.y138));
                        cs.applyTo(pCl);
                        mLeftConstraintApplied = true;
                    }
//                    ViewGroup.LayoutParams para;
//                    para = card_pic_iv_lift.getLayoutParams();
//                    para.height = ResUtil.getDimen(R.dimen.y130);
//                    para.width = ResUtil.getDimen(R.dimen.y130);;
//                    card_pic_iv_lift.setLayoutParams(para);
                    card_pic_iv_lift.setVisibility(View.VISIBLE);

                    vip_icon_lift.setVisibility(View.VISIBLE);
                    ImageLoader.getInstance().displayImageFixedSize(card_pic_iv_lift.getContext(),
                            UrlUtil.getCardPicUrl(data.contentList.get(0).getImageFiles()), card_pic_iv_lift, ResUtil.getDimen(R.dimen.m280));
                    if (data.contentList.get(0).getResType() == ResType.TYPE_BROADCAST
                            || data.contentList.get(0).getResType() == ResType.TYPE_TV) {
                        vip_icon_lift.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_icon_live_class));
                    }  else if (data.getContentList().get(0).getResType() == ResType.TYPE_LIVE) {
                        if (data.getContentList().get(0).getLiveStatus()==0||data.getContentList().get(0).getLiveStatus()==6){
                            vip_icon_lift.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_live_icon_reday));
                        }else if (data.getContentList().get(0).getLiveStatus()==1){
                            vip_icon_lift.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_live_icon_playing));
                        }
                    } else if (data.getContentList().get(0).getResType() == ResType.TYPE_VIDEO_AUDIO ||
                            data.getContentList().get(0).getResType() == ResType.TYPE_VIDEO_ALBUM) {
                        vip_icon_lift.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_icon_video));
                    } else {
                        if (data.contentList.get(0) instanceof AlbumDetailColumnMember) {
                            AlbumDetailColumnMember albumDetailColumnMember= (AlbumDetailColumnMember) data.contentList.get(0);
                            VipCornerUtil.setVipCorner(vip_icon_lift,albumDetailColumnMember.getVip(), albumDetailColumnMember.getFine(), false);
                        }else {
                            vip_icon_lift.setVisibility(View.GONE);
                        }
                    }
                }else {
                    card_pic_iv_lift.setVisibility(View.GONE);
                    vip_icon_lift.setVisibility(View.GONE);
                }
                if (data.contentList.get(0).getCanPlay() == 1) {
                    card_play_iv_lift.setVisibility(View.VISIBLE);
                } else {
                    card_play_iv_lift.setVisibility(View.GONE);
                    showPlayBtn = false;
                }
            }
            layout_1_1_top_lift.setContentDescription(card_title_tv_lift.getText());
            layout_1_1_top_lift.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onViewClickListener != null) {
                        v.setTag(0);
                        onViewClickListener.onViewClick(v, getPositionInParent());
                    }
                }
            });

            boolean notShowPlaying = data.getContentList().get(0) instanceof PageRedirectionColumnMember
                    || data.getContentList().get(0) instanceof ActivityDetailColumnMember;
            if (!notShowPlaying) {
                card_layout_playing_lift.setVisibility(data.selected
                        && ComponentUtils.getInstance().getCardHomePlayIdIsPlaying(data.contentList.get(0).getId(), data.contentList.get(0).getCanPlay()) ?
                        View.VISIBLE : View.GONE);
                card_play_iv_lift.setVisibility((showPlayBtn && card_layout_playing_lift.getVisibility() == View.GONE) ? View.VISIBLE : View.GONE);
            } else {
                card_layout_playing_lift.setVisibility(View.GONE);
                card_play_iv_lift.setVisibility((View.GONE));
            }


        }


    }

    /**
     * 设置下方区域数据
     *
     * @param data
     */
    private void setBottomViewDate(Component1And1Cell data) {
        if (data.contentList != null && data.contentList.size() > 1) {
            String title = "";
            if (!TextUtils.isEmpty(data.getContentList().get(1).getTitle())) {
                title = data.getContentList().get(1).getTitle();
            } else if(!TextUtils.isEmpty(data.getContentList().get(1).getProgramDesc())){
                title = data.getContentList().get(1).getProgramDesc();
            }else if(!TextUtils.isEmpty(data.getContentList().get(1).getProgramTitle())){
                title = data.getContentList().get(1).getProgramTitle();
            }else {
                title = data.getContentList().get(1).getDescription();
            }
            card_title_tv_right.setText(title);
            if (!TextUtils.isEmpty(UrlUtil.getCardBgUrl(data.contentList.get(1).getImageFiles())))
                ImageLoader.getInstance().displayImage(card_pic_iv_right.getContext(),
                        UrlUtil.getCardBgUrl(data.contentList.get(1).getImageFiles()), card_bg_iv_right);
            boolean showPlayBtn = true;
            if (false/*data.getContentList().get(1) instanceof PageRedirectionColumnMember
                    || data.getContentList().get(1) instanceof ActivityDetailColumnMember*/) {
                card_pic_iv_right.setVisibility(View.GONE);
                card_play_iv_right.setVisibility(View.GONE);
                showPlayBtn = false;
            } else {
                // 避免重复应用约束
                if (!mRightConstraintApplied) {
                    ConstraintLayout pCl = (ConstraintLayout) card_pic_iv_right.getParent();
                    ConstraintSet cs = new ConstraintSet();
                    cs.clone(pCl);
                    cs.clear(card_pic_iv_right.getId());
                    cs.connect(card_pic_iv_right.getId(), ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP, 0);
                    cs.connect(card_pic_iv_right.getId(), ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, 0);
                    cs.connect(card_pic_iv_right.getId(), ConstraintSet.RIGHT, ConstraintSet.PARENT_ID, ConstraintSet.RIGHT, ResUtil.getDimen(R.dimen.m20));
                    cs.constrainWidth(card_pic_iv_right.getId(), ResUtil.getDimen(R.dimen.y138));
                    cs.constrainHeight(card_pic_iv_right.getId(), ResUtil.getDimen(R.dimen.y138));
                    cs.applyTo(pCl);
                    mRightConstraintApplied = true;
                }
//                ViewGroup.LayoutParams para;
//                para = card_pic_iv_right.getLayoutParams();
//                para.height = ResUtil.getDimen(R.dimen.m130);
//                para.width = ResUtil.getDimen(R.dimen.m130);;
//                card_pic_iv_right.setLayoutParams(para);
                card_pic_iv_right.setVisibility(View.VISIBLE);

                card_play_iv_right.setVisibility(View.VISIBLE);
                if (!TextUtils.isEmpty(UrlUtil.getCardPicUrl(data.contentList.get(1).getImageFiles()))) {
                    card_pic_iv_right.setVisibility(View.VISIBLE);
                    vip_icon_right.setVisibility(View.VISIBLE);
                    ImageLoader.getInstance().displayImageFixedSize(card_pic_iv_right.getContext(),
                            UrlUtil.getCardPicUrl(data.contentList.get(1).getImageFiles()), card_pic_iv_right, ResUtil.getDimen(R.dimen.m280));
                    if (data.contentList.get(1).getResType() == ResType.TYPE_BROADCAST
                            || data.contentList.get(1).getResType() == ResType.TYPE_TV) {
                        vip_icon_right.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_icon_live_class));
                    }  else if (data.getContentList().get(1).getResType() == ResType.TYPE_LIVE) {
                        if (data.getContentList().get(1).getLiveStatus()==0||data.getContentList().get(1).getLiveStatus()==6){
                            vip_icon_right.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_live_icon_reday));
                        }else if (data.getContentList().get(1).getLiveStatus()==1){
                            vip_icon_right.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_live_icon_playing));
                        }
                    } else if (data.getContentList().get(1).getResType() == ResType.TYPE_VIDEO_AUDIO ||
                            data.getContentList().get(1).getResType() == ResType.TYPE_VIDEO_ALBUM) {
                        vip_icon_right.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_icon_video));
                    } else {
//                        VipCornerUtil.setVipCorner(vip_icon_right, data.contentList.get(1).getVip(), data.contentList.get(1).getFine(), true);
                        if (data.contentList.get(1) instanceof AlbumDetailColumnMember) { // AudioDetailColumnMember
                            AlbumDetailColumnMember albumDetailColumnMember= (AlbumDetailColumnMember) data.contentList.get(1);
                            VipCornerUtil.setVipCorner(vip_icon_right,albumDetailColumnMember.getVip(), albumDetailColumnMember.getFine(), false);
                        }else {
                            vip_icon_right.setVisibility(View.GONE);
                        }
                    }
                }else {
                    card_pic_iv_right.setVisibility(View.GONE);
                    vip_icon_right.setVisibility(View.GONE);
                }
                if (data.contentList.get(1).getCanPlay() == 1) {
                    card_play_iv_right.setVisibility(View.VISIBLE);
                } else {
                    card_play_iv_right.setVisibility(View.GONE);
                    showPlayBtn = false;
                }
            }
            layout_1_1_top_right.setContentDescription(card_title_tv_right.getText());
            layout_1_1_top_right.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onViewClickListener != null) {
                        v.setTag(1);
                        onViewClickListener.onViewClick(v, getPositionInParent());
                    }
                }
            });

            boolean notShowPlaying = data.getContentList().get(1) instanceof PageRedirectionColumnMember
                    || data.getContentList().get(1) instanceof ActivityDetailColumnMember;
            if (!notShowPlaying) {
                card_layout_playing_right.setVisibility(data.selected
                        && ComponentUtils.getInstance().getCardHomePlayIdIsPlaying(data.contentList.get(1).getId(), data.contentList.get(1).getCanPlay()) ?
                        View.VISIBLE : View.GONE);
                card_play_iv_right.setVisibility((showPlayBtn && card_layout_playing_right.getVisibility() == View.GONE) ? View.VISIBLE : View.GONE);
            } else {
                card_layout_playing_right.setVisibility(View.GONE);
                card_play_iv_right.setVisibility(View.GONE);
            }

        }

    }

    private BaseHolder.OnViewClickListener onViewClickListener;

    @Override
    public void setOnItemClickListener(BaseHolder.OnViewClickListener listener) {
        onViewClickListener = listener;
    }

    @Override
    public void release() {
        super.release();
        // CPU优化：安全地清理Glide图片资源
        if (card_pic_iv_lift != null) {
            Context context = card_pic_iv_lift.getContext();
            if (context != null && !isContextDestroyed(context)) {
                Logger.i(TAG, "清理card_pic_iv_lift图片资源");
                Glide.with(context).clear(card_pic_iv_lift);
            }
        }
        if (card_pic_iv_right != null) {
            Context context = card_pic_iv_right.getContext();
            if (context != null && !isContextDestroyed(context)) {
                Logger.i(TAG, "清理card_pic_iv_right图片资源");
                Glide.with(context).clear(card_pic_iv_right);
            }
        }
    }

    /**
     * 检查Context是否已被销毁
     */
    private boolean isContextDestroyed(Context context) {
        if (context instanceof Activity) {
            return ((Activity) context).isDestroyed();
        }
        return false;
    }
}
