package com.kaolafm.kradio.flavor.impl;

import android.media.AudioManager;

import com.kaolafm.kradio.lib.base.flavor.MediaSessionHelperInter;

public class MediaSessionHelperInterImpl implements MediaSessionHelperInter {
    @Override
    public boolean needUnregisterMediaSession(int focusState) {
        boolean needUnregister = false;
        if (focusState == AudioManager.AUDIOFOCUS_LOSS || focusState == AudioManager.AUDIOFOCUS_LOSS_TRANSIENT) {
            needUnregister = true;
        }
        return needUnregister;
    }
}
