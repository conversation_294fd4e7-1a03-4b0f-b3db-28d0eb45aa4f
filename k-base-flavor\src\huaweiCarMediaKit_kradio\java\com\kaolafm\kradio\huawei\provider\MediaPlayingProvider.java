package com.kaolafm.kradio.huawei.provider;


import android.util.Log;

import com.huawei.carmediakit.bean.Lyrics;
import com.huawei.carmediakit.bean.MediaElement;
import com.huawei.carmediakit.bean.PagedMediaElements;
import com.huawei.carmediakit.bean.PagenationInfo;
import com.huawei.carmediakit.bean.PlayMode;
import com.huawei.carmediakit.bean.PlayRate;
import com.huawei.carmediakit.bean.PlayState;
import com.huawei.carmediakit.bean.Single;
import com.huawei.carmediakit.callback.BasicCallback;
import com.huawei.carmediakit.constant.ErrorCode;
import com.huawei.carmediakit.provider.IMediaPlayingProvider;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.common.bean.HistoryItem;
import com.kaolafm.kradio.flavor.utils.PlayerUtil;
import com.kaolafm.kradio.huawei.convert.DataConverterUtil;
import com.kaolafm.kradio.huawei.convert.PlayDataConverterUtil;
import com.kaolafm.kradio.huawei.utils.Constant;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.PlaylistInfo;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import java.util.List;

public class MediaPlayingProvider implements IMediaPlayingProvider {
    public static final String TAG = Constant.TAG;

    @Override
    public Single queryCurrentSingle() {
        Logger.i(TAG, "queryCurrentSingle");

        HistoryItem historyItem = HistoryManager.getInstance().getLasted();
        if (historyItem != null) {
            PlayerUtil.playNetOrLocal();
        }
        return DataConverterUtil.toSingle(historyItem);
    }

    @Override
    public void asyncQueryPlayQueue(int i, BasicCallback<PagedMediaElements> basicCallback) {
        Logger.i(TAG, "asyncQueryPlayQueue");

        List<PlayItem> playItems = PlayerManagerHelper.getInstance().getPlayList(false);
        PagedMediaElements pagedMediaElements = new PagedMediaElements();
        List<MediaElement> mediaElementList = PlayDataConverterUtil.toMediaElementList(playItems);
        pagedMediaElements.setElements(mediaElementList);
        PagenationInfo pagenationInfo = new PagenationInfo();
        pagenationInfo.setPageIndex(i);
        pagenationInfo.setPageSize(15);
        pagenationInfo.setTotalNum(playItems.size());
        pagedMediaElements.setPagenationInfo(pagenationInfo);
        basicCallback.callback(pagedMediaElements, ErrorCode.SUCCESS, "Success");
        /*
        PlayerManager.getInstance().loadNextPage(new IPlayListGetListener() {
            @Override
            public void onDataGet(PlayItem playItem, List<PlayItem> arrayList) {
                PlayerManager.getInstance().getPlayList();
            }

            @Override
            public void onDataGetError(int i) {
            }
        });
         */
    }

    @Override
    public PlayState queryPlayState() {
        Logger.i(TAG, "queryPlayState");

        PlayState playState = new PlayState();
        PlayerManagerHelper helper = PlayerManagerHelper.getInstance();
        if (helper.isPlaying()) {
            playState.setState(PlayState.State.PLAYING);
        } else if (helper.isPlayerBuffering()) {
            playState.setState(PlayState.State.BUFFERING);
        } else {
            playState.setState(PlayState.State.PAUSED);
        }

        return playState;
    }

    @Override
    public void asyncQueryLyrics(BasicCallback<Lyrics> basicCallback) {
        Logger.i(TAG, "asyncQueryLyrics");
    }

    @Override
    public PlayMode queryPlayMode() {
        Logger.i(TAG, "queryPlayMode");
        PlayMode playMode = new PlayMode();
        playMode.setMode(PlayMode.Mode.ORDER);
        return playMode;
    }

    @Override
    public List<PlayRate> queryPlayRates() {
        Logger.i(TAG, "queryPlayRates");
        return null;
    }
}
