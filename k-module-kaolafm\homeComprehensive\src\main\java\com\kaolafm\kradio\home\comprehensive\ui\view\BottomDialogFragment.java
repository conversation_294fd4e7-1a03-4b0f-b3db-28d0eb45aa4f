package com.kaolafm.kradio.home.comprehensive.ui.view;

import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentActivity;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.kaolafm.kradio.component.ui.base.skin.SkinHelper;
import com.kaolafm.kradio.common.utils.CommonUtils;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.dialog.BaseDialogFragment;
import com.kaolafm.kradio.lib.dialog.DialogListener;
import com.kaolafm.kradio.lib.utils.AntiShake;

/**
 * <AUTHOR>
 **/
public class BottomDialogFragment extends BaseDialogFragment {

    private TextView mTvDialogBottomCancel;
    private TextView mTvDialogBottomDefine;
    private TextView mTvDialogBottomMessage;
    private DialogListener.OnNativeListener<DialogFragment> mNativeListener;
    private DialogListener.OnPositiveListener<DialogFragment> mPositiveListener;
    private CharSequence message;
    private CharSequence leftBtnText;
    private CharSequence rightBtnText;

    public static BottomDialogFragment create() {
        BottomDialogFragment fragment = new BottomDialogFragment();
        fragment.setGravity(Gravity.BOTTOM);
        fragment.setWidth(WindowManager.LayoutParams.MATCH_PARENT);
//        fragment.setHeight((int) (ScreenUtil.getScreenHeight() * 0.14f));
        fragment.setHeight(WindowManager.LayoutParams.WRAP_CONTENT);
        return fragment;
    }

    @Override
    public void onStart() {
        super.onStart();
        Window window = getDialog().getWindow();
        WindowManager.LayoutParams params = window.getAttributes();
        if (SkinHelper.isNightMode()) {
            params.dimAmount = 0.9f;
        } else {
            params.dimAmount = 0.7f;
        }
        window.setAttributes(params);
        CommonUtils.getInstance().initGreyStyle(window);
    }

    @Override
    protected View getContentView() {
        FragmentActivity activity = getActivity();
        View inflate = View.inflate(activity, R.layout.dialog_fragment_bottom, null);
        mTvDialogBottomCancel = inflate.findViewById(R.id.tv_dialog_bottom_cancel);
        mTvDialogBottomDefine = inflate.findViewById(R.id.tv_dialog_bottom_define);
        mTvDialogBottomMessage = inflate.findViewById(R.id.tv_dialog_bottom_message);

        mTvDialogBottomCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!AntiShake.check(v.getId())) {
                    if (mNativeListener != null) {
                        mNativeListener.onClick(BottomDialogFragment.this);
                    } else {
                        dismiss();
                    }
                }
            }
        });

        mTvDialogBottomDefine.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!AntiShake.check(v.getId())) {
                    if (mPositiveListener != null) {
                        mPositiveListener.onClick(BottomDialogFragment.this);
                    }
                }
            }
        });

        mTvDialogBottomMessage.setText(message);
        if (!TextUtils.isEmpty(leftBtnText)) {
            mTvDialogBottomCancel.setText(leftBtnText);
        }
        if (!TextUtils.isEmpty(rightBtnText)) {
            mTvDialogBottomDefine.setText(rightBtnText);
        }
        return inflate;
    }


    public void setMessage(CharSequence message) {
        this.message = message;
    }

    public void setLeftButton(CharSequence leftBtnText) {
        this.leftBtnText = leftBtnText;
    }

    public void setRightButton(CharSequence rightBtnText) {
        this.rightBtnText = rightBtnText;
    }

    public void setOnNativeListener(DialogListener.OnNativeListener<DialogFragment> nativeListener) {
        mNativeListener = nativeListener;
    }

    public void setOnPositiveListener(DialogListener.OnPositiveListener<DialogFragment> positiveListener) {
        mPositiveListener = positiveListener;
    }

    @Override
    protected void showAccordingToScreen(int orientation) {
        setWidth(WindowManager.LayoutParams.MATCH_PARENT);
        setHeight(/*(int) (ScreenUtil.getScreenHeight() * 0.14f)*/WindowManager.LayoutParams.WRAP_CONTENT);
        super.showAccordingToScreen(orientation);

        //nexus 6上字体太大,不知道原因在哪儿
//        int dimensionPixelSize;
//        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
//            dimensionPixelSize = getResources().getDimensionPixelSize(R.dimen.text_size5);
//        } else {
//            dimensionPixelSize = getResources().getDimensionPixelSize(R.dimen.text_size3);
//        }
//        Log.i("novelot", "showAccordingToScreen: dimensionPixelSize=" + dimensionPixelSize);
//        mTvDialogBottomMessage.setTextSize(dimensionPixelSize);
    }
}
