package com.kaolafm.kradio.flavor.impl;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.Log;

import com.kaolafm.kradio.flavor.common.SystemBootUtil;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioPlayLogicInter;

import com.kaolafm.opensdk.player.core.listener.OnPlayLogicListener;


/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-07-05 12:16
 ******************************************/
public final class KRadioAudioPlayLogicImpl implements KRadioAudioPlayLogicInter {
    private final static String TAG = "KRadioAudioPlayLogicImpl";

    public KRadioAudioPlayLogicImpl() {

    }

    @SuppressLint("LongLogTag")
    @Override
    public boolean autoPlayAudio(Object... args) {
        Context context = AppDelegate.getInstance().getContext();
        SystemBootUtil systemBootUtil = new SystemBootUtil();
        boolean flag = systemBootUtil.isFirstBoot(context);
        Log.i(TAG, "autoPlayAudio:   flag = " + flag);
        if (flag) {
            PlayerUtil.playDefaultMediaForChannel();
            systemBootUtil.updateFirstBoot(context, false);
            return true;
        }
        return false;
    }

    @Override
    public boolean requestAudioFocus(Object... args) {
        return false;
    }

    @Override
    public boolean resumeAudioPlayLogic(Object... args) {
        return false;
    }

    @Override
    public boolean restartAudioPlayLogic(Object... args) {
        return false;
    }

    @Override
    public boolean doStartInPlay(Object... args) {
        Context context = AppDelegate.getInstance().getContext();
        SystemBootUtil systemBootUtil = new SystemBootUtil();
        boolean flag = systemBootUtil.isFirstBoot(context);
        Log.i(TAG, "doStartInPlay:   flag = " + flag);
        if (flag) {
            PlayerUtil.playDefaultMediaForChannel();
            systemBootUtil.updateFirstBoot(context, false);
            return true;
        }
        return false;
    }
}