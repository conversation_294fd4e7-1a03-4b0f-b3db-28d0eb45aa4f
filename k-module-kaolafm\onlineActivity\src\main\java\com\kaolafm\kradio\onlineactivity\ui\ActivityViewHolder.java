package com.kaolafm.kradio.onlineactivity.ui;

import android.annotation.SuppressLint;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.kradio.common.widget.KradioTextView;
import com.kaolafm.kradio.k_kaolafm.R; 
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.UrlUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.opensdk.api.activity.model.Activity;
 

public class ActivityViewHolder extends BaseHolder<Activity> {
 
    KradioTextView titleTextView; 
    TextView desTextView; 
    ImageView qrCodeImage; 
    ImageView item_activitys_bg_iv;
 
    View qrExpireView; 
    View qrExpireIcon;

    public ActivityViewHolder(View itemView) {
        super(itemView);

        titleTextView = itemView.findViewById(R.id.title_activity);
        desTextView = itemView.findViewById(R.id.des_activity);
        qrCodeImage = itemView.findViewById(R.id.qrCode_image);
        item_activitys_bg_iv = itemView.findViewById(R.id.item_activitys_bg_iv);
        qrExpireView = itemView.findViewById(R.id.qr_view_expire);
        qrExpireIcon = itemView.findViewById(R.id.qr_expire_icon);
        
    }

    @Override
    public void setupData(Activity activity, int position) {
        titleTextView.setFontWeight(0.7f);
//        titleTextView.setGradientColor(ResUtil.getColor(R.color.online_activity_item_title_start_color_2)
//                , ResUtil.getColor(R.color.online_activity_item_title_end_color_2), 0);
        titleTextView.setTextColor(ResUtil.getColor(R.color.online_activity_item_title_start_color_2));
        titleTextView.setText(StringUtil.getMaxString(activity.getName(),8));
        desTextView.setText(StringUtil.getMaxString(activity.getDescription(),22));
        if (!TextUtils.isEmpty(activity.getBackgroundUrl()))
            ImageLoader.getInstance().displayImage(AppDelegate.getInstance().getContext(),
                    activity.getBackgroundUrl(), item_activitys_bg_iv);

        ImageLoader.getInstance().displayImage(AppDelegate.getInstance().getContext(),
                UrlUtil.getCustomPicUrl(UrlUtil.PIC_250_250, activity.getQrCodeUrl()), qrCodeImage);

        if (activity.getStatus() == 1) {
            qrExpireView.setVisibility(View.GONE);
            qrExpireIcon.setVisibility(View.GONE);
//            titleTextView.setTextColor(ResUtil.getColor(R.color.color_2));
//            desTextView.setTextColor(ResUtil.getColor(R.color.color_2));
        } else {
            qrExpireView.setVisibility(View.VISIBLE);
            qrExpireIcon.setVisibility(View.VISIBLE);
//            titleTextView.setTextColor(ResUtil.getColor(R.color.color_2));
//            desTextView.setTextColor(ResUtil.getColor(R.color.color_2));
        }
    }
}
