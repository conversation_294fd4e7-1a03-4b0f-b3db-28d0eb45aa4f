package com.kaolafm.kradio.flavor.impl;

import com.kaolafm.kradio.lib.base.flavor.KRadioThirdPlatformInitInter;


/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-07-05 12:25
 ******************************************/
public final class KRadioThirdPlatformInitImpl implements KRadioThirdPlatformInitInter {
    @Override
    public boolean initThirdPlatform(Object... args) {
        AudioStatusManager.getInstance().setNeedPlayServiceForeground(false);
        return true;
    }

    @Override
    public boolean destroyThirdPlatform(Object... args) {
        return false;
    }
}
