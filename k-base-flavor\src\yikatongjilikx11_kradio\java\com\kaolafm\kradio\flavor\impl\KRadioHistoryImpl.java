package com.kaolafm.kradio.flavor.impl;

import com.kaolafm.kradio.history.ui.IHistoryView;
import com.kaolafm.kradio.lib.base.flavor.KRadioHistoryInter;

/**
 * @Package: com.kaolafm.kradio.flavor.impl
 * @Description:
 * @Author: lidan
 * @Date: 2021/6/7 16:29
 */
public class KRadioHistoryImpl implements KRadioHistoryInter {
    @Override
    public boolean doNoHistoryData(Object... args) {
        if (args.length > 0) {
            Object o = args[0];
            if (o instanceof IHistoryView) {
                ((IHistoryView) o).showEmpty();
            }
        }
        return true;
    }
}
