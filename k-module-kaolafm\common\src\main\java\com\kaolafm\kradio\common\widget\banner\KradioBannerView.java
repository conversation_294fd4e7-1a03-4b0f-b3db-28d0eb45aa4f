package com.kaolafm.kradio.common.widget.banner;

import android.content.Context;
import android.content.res.TypedArray;
import androidx.annotation.AnimRes;
import androidx.annotation.NonNull;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.RelativeLayout;
import android.widget.ViewFlipper;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.k_kaolafm.R;

import java.util.List;

/**
 * 轮播组件
 * <p>
 * 使用是需要设置Adapter，Adapter只用来创建轮播Item的View以及绑定数据
 * 如果需要设置点击事件，需要调用{@link KradioBannerView#setItemOnClickListener(IKradioBannerItemClickListener)}方法
 */
public class KradioBannerView<DATA> extends RelativeLayout {
    private final int measureSpec = MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED);
    private ViewFlipper mViewFlipper;
    /**
     * 文字切换时间间隔,默认5s
     */
    private long mInterval = 5000;
    /**
     * 自动开始 - CPU优化：禁用轮播功能以降低CPU使用率
     */
    private boolean autoStart = false; // 原值为true，现改为false以优化性能
    /**
     * 当只有一个数据时是否轮播
     */
    private boolean playWhenSingleData = false;

    private boolean hasSetDirection = false;
    private int direction = DIRECTION_BOTTOM_TO_TOP;
    private static final int DIRECTION_BOTTOM_TO_TOP = 0;
    private static final int DIRECTION_TOP_TO_BOTTOM = 1;
    private static final int DIRECTION_RIGHT_TO_LEFT = 2;
    private static final int DIRECTION_LEFT_TO_RIGHT = 3;
    @AnimRes
    private int inAnimResId = R.anim.text_banner_anim_right_in;
    @AnimRes
    private int outAnimResId = R.anim.text_banner_anim_left_out;
    private boolean hasSetAnimDuration = false;
    /**
     * 默认0.5s
     */
    private int animDuration = 500;

    /**
     * 适配器
     */
    private KradioBannerAdapter<DATA> mAdapter;

    private List<DATA> mDatas;
    private IKradioBannerItemClickListener<DATA> mListener;
    private IKradioBannerItemShowListener<DATA> mShowListener;
    private boolean isStarted;  //是否正在轮播
    private boolean isDetachedFromWindow;


    public KradioBannerView(Context context) {
        this(context, null);
    }

    public KradioBannerView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs, 0);
    }

    /**
     * 初始化控件
     */
    private void init(Context context, AttributeSet attrs, int defStyleAttr) {
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.KradioBannerViewStyle, defStyleAttr, 0);
        mInterval = (long) typedArray.getFloat(R.styleable.KradioBannerViewStyle_setInterval, mInterval);//文字切换时间间隔
        autoStart = typedArray.getBoolean(R.styleable.KradioBannerViewStyle_autoStart, autoStart);//是否自动轮播
        playWhenSingleData = typedArray.getBoolean(R.styleable.KradioBannerViewStyle_playWhenSingleData, playWhenSingleData);//当只有一个数据时是否轮播
        hasSetAnimDuration = typedArray.hasValue(R.styleable.KradioBannerViewStyle_setAnimDuration);
        animDuration = typedArray.getInt(R.styleable.KradioBannerViewStyle_setAnimDuration, animDuration);//动画时间
        hasSetDirection = typedArray.hasValue(R.styleable.KradioBannerViewStyle_setDirection);
        direction = typedArray.getInt(R.styleable.KradioBannerViewStyle_setDirection, direction);//方向
        if (hasSetDirection) {
            switch (direction) {
                case DIRECTION_BOTTOM_TO_TOP:
                    inAnimResId = R.anim.text_banner_anim_bottom_in;
                    outAnimResId = R.anim.text_banner_anim_top_out;
                    break;
                case DIRECTION_TOP_TO_BOTTOM:
                    inAnimResId = R.anim.text_banner_anim_top_in;
                    outAnimResId = R.anim.text_banner_anim_bottom_out;
                    break;
                case DIRECTION_RIGHT_TO_LEFT:
                    inAnimResId = R.anim.text_banner_anim_right_in;
                    outAnimResId = R.anim.text_banner_anim_left_out;
                    break;
                case DIRECTION_LEFT_TO_RIGHT:
                    inAnimResId = R.anim.text_banner_anim_left_in;
                    outAnimResId = R.anim.text_banner_anim_right_out;
                    break;
            }
        } else {
            inAnimResId = R.anim.text_banner_anim_right_in;
            outAnimResId = R.anim.text_banner_anim_left_out;
        }
        mViewFlipper = new ViewFlipper(getContext());//new 一个ViewAnimator
        mViewFlipper.setLayoutParams(new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        addView(mViewFlipper);
        typedArray.recycle();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, measureSpec);

        int height = 0;
        for (int i = 0; i < getChildCount(); i++) {
            View child = getChildAt(i);
            if (child != null)
                height = Math.max(height, child.getMeasuredHeight());
        }

        setMeasuredDimension(getMeasuredWidth(), height);
    }

    /**
     * 暂停动画
     */
    public void stopViewAnimator() {
        if (isStarted) {
            removeCallbacks(mRunnable);
            isStarted = false;
        }
    }

    /**
     * 开始动画 - CPU优化：已禁用轮播功能
     */
    public void startViewAnimator() {
        // CPU优化：轮播功能已禁用，直接返回
        if (!autoStart) {
            return;
        }
        if (!isStarted) {
            if (!isDetachedFromWindow) {
                isStarted = true;
                postDelayed(mRunnable, mInterval);
            }
        }
    }

    /**
     * 设置延时间隔
     */
    private AnimRunnable mRunnable = new AnimRunnable();

    private class AnimRunnable implements Runnable {

        @Override
        public void run() {
            if (isStarted) {
                setInAndOutAnimation(inAnimResId, outAnimResId);
                mViewFlipper.showNext();//手动显示下一个子view。
                if (mShowListener != null)
                    mShowListener.onItemShow(getCurrentData(), mViewFlipper.getDisplayedChild());
                postDelayed(this, mInterval + animDuration);
            } else {
                stopViewAnimator();
            }

        }
    }


    /**
     * 设置进入动画和离开动画
     *
     * @param inAnimResId  进入动画的resID
     * @param outAnimResID 离开动画的resID
     */
    private void setInAndOutAnimation(@AnimRes int inAnimResId, @AnimRes int outAnimResID) {
        Animation inAnim = AnimationUtils.loadAnimation(getContext(), inAnimResId);
        inAnim.setDuration(animDuration);
        mViewFlipper.setInAnimation(inAnim);

        Animation outAnim = AnimationUtils.loadAnimation(getContext(), outAnimResID);
        outAnim.setDuration(animDuration);
        mViewFlipper.setOutAnimation(outAnim);
    }


    /**
     * 设置数据集合
     */
    public void setDatas(List<DATA> datas) {
        stopViewAnimator();
        this.mDatas = datas;
        if (!ListUtil.isEmpty(mDatas)) {
            mViewFlipper.removeAllViews();
            if (mAdapter == null) return;
            for (int i = 0; i < mDatas.size(); i++) {
                View itemView = mAdapter.onCreateView(this);
                if (itemView != null) {
                    DATA data = mDatas.get(i);
                    setItemViewClickListener(itemView, data, i);
                    mAdapter.onBindView(itemView, data, i);
                    mViewFlipper.addView(itemView, i);//添加子view,并标识子view位置
                    if (i == 0 && mShowListener != null) {
                        mShowListener.onItemShow(getCurrentData(), this.mViewFlipper.getDisplayedChild());
                    }
                }
            }
            //只有多于1个才轮播，否则静止不动
            if (autoStart && (playWhenSingleData || mDatas.size() > 1))
                startViewAnimator();
        }

    }

    /**
     * 设置子view的点击事件
     *
     * @param itemView
     * @param data
     * @param position
     */
    private void setItemViewClickListener(View itemView, DATA data, int position) {
        if (mListener != null) {
            //添加点击监听器
            itemView.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mListener != null)
                        mListener.onItemClick(data, position);
                }
            });
        } else {
            //如果没有设置点击监听器，则设置itemView不可点击，防止itemView抢先消费手势事件
            itemView.setClickable(false);
        }
    }

    public KradioBannerAdapter<DATA> getAdapter() {
        return mAdapter;
    }

    public void setAdapter(KradioBannerAdapter<DATA> mAdapter) {
        this.mAdapter = mAdapter;
    }

    public long getInterval() {
        return mInterval;
    }

    public void setInterval(long mInterval) {
        this.mInterval = mInterval;
    }

    /**
     * 获取当前正在显示的
     *
     * @return
     */
    public DATA getCurrentData() {
        int displayedChild = this.mViewFlipper.getDisplayedChild();
        if (mDatas != null && displayedChild < mDatas.size()) {
            return mDatas.get(displayedChild);
        }
        return null;
    }

    public int getAnimDuration() {
        return animDuration;
    }

    public void setAnimDuration(int animDuration) {
        this.animDuration = animDuration;
    }

    /**
     * 设置点击监听事件回调
     */
    public void setItemOnClickListener(IKradioBannerItemClickListener<DATA> listener) {
        if (this.mListener == listener) {
            return;
        }
        this.mListener = listener;
        if (ListUtil.isEmpty(mDatas)) {
            Log.e(getLogTag(), "数据为空，忽略设置点击监听");
            return;
        }
        if (mViewFlipper == null) {
            Log.e(getLogTag(), "mViewFlipper为空，忽略设置点击监听");
            return;
        }
        if (mDatas.size() != mViewFlipper.getChildCount()) {
            Log.e(getLogTag(), "数据长度与子View数量不符，忽略设置点击监听");
            setDatas(mDatas);
            return;
        }
        DATA data;
        View itemView;
        for (int i = 0; i < mViewFlipper.getChildCount(); i++) {
            itemView = mViewFlipper.getChildAt(i);
            data = mDatas.get(i);
            setItemViewClickListener(itemView, data, i);
        }
    }

    /**
     * 设置点击监听事件回调
     */
    public void setItemShowListener(IKradioBannerItemShowListener<DATA> listener) {
        this.mShowListener = listener;
    }


    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        isDetachedFromWindow = true;
        stopViewAnimator();
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        isDetachedFromWindow = false;
        if (autoStart && mDatas != null && (playWhenSingleData || mDatas.size() > 1))
            startViewAnimator();
    }

    /**
     * 是否开始轮播了
     *
     * @return
     */
    public boolean isStarted() {
        return isStarted;
    }

    public static class Item<ITEM_DATA, ITEM_VIEW extends View> {

    }


    public abstract static class KradioBannerAdapter<DATA> {
        public KradioBannerAdapter() {
        }

        public abstract View onCreateView(ViewGroup container);

        public abstract void onBindView(@NonNull View itemView, DATA data, int position);
    }

    /**
     * 点击监听回调接口
     */
    public interface IKradioBannerItemClickListener<DATA> {
        void onItemClick(DATA data, int position);
    }

    /**
     * 切换监听回调接口
     */
    public interface IKradioBannerItemShowListener<DATA> {
        void onItemShow(DATA data, int position);
    }

    private String getLogTag() {
        return getClass().getSimpleName();
    }
}