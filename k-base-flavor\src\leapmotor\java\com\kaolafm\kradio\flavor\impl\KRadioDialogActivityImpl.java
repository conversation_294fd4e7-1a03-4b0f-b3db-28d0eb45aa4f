package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.view.WindowManager;

import com.kaolafm.kradio.lib.base.flavor.KRadioDialogActivityInter;

public class KRadioDialogActivityImpl implements KRadioDialogActivityInter {

    @Override
    public void handleHubActivity(Activity activity) {
        activity.setTitle(null);
        activity.setTheme(com.kaolafm.kradio.k_kaolafm.R.style.AppTheme);
        WindowManager.LayoutParams attributes = activity.getWindow().getAttributes();
        attributes.x = 500;
        attributes.y = -20;
        attributes.width = 1640;
        attributes.height = 880;
        attributes.dimAmount = 0.0f; //设置窗口之外部分透明程度
        activity.getWindow().setAttributes(attributes);
        activity.getWindow().setFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL, WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL);
    }

    @Override
    public void handleBaseActivity(Activity activity) {
        WindowManager.LayoutParams attributes = activity.getWindow().getAttributes();
        attributes.x = 500;
        attributes.y = -20;
        attributes.width = 1640;
        attributes.height = 880;
        attributes.dimAmount = 0.0f; //设置窗口之外部分透明程度
        activity.getWindow().setAttributes(attributes);
        activity.getWindow().setFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL, WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL);
        activity.getWindow().setBackgroundDrawableResource(com.kaolafm.kradio.lib.R.color.transparent_color);
    }
}
