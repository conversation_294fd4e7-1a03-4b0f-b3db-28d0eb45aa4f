package com.kaolafm.kradio.flavor.impl;

import android.view.WindowManager;

import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.base.flavor.KRadioDialogFragmentInter;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.view.FullScreenDialogFragment;

/**
 * @ClassName KRadioDialogFragmentImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/6/10 11:10
 * @Version 1.0
 */
public class KRadioDialogFragmentImpl implements KRadioDialogFragmentInter {

    @Override
    public void setContentHeight(Object... args) {
        FullScreenDialogFragment fullScreenDialogFragment = (FullScreenDialogFragment) args[0];
        if (fullScreenDialogFragment != null) {
            fullScreenDialogFragment.setWidth(WindowManager.LayoutParams.WRAP_CONTENT);
        }
    }

    @Override
    public void setContentWidth(Object... args) {
        FullScreenDialogFragment fullScreenDialogFragment = (FullScreenDialogFragment) args[0];
        if (fullScreenDialogFragment != null) {
            fullScreenDialogFragment.setWidth(WindowManager.LayoutParams.MATCH_PARENT);
        }
    }

    @Override
    public int getContentWidth() {
        return ResUtil.getDimen(R.dimen.m605);
    }

    @Override
    public int getContentHeight() {
        return ResUtil.getDimen(R.dimen.m605);
    }
}
