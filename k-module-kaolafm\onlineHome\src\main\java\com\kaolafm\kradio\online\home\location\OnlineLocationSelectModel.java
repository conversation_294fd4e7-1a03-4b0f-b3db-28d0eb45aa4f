package com.kaolafm.kradio.online.home.location;

import com.kaolafm.kradio.lib.base.mvp.BaseModel;
import com.kaolafm.kradio.online.home.location.bean.OnlineRecomandCityBean;
import com.kaolafm.kradio.online.home.location.request.OnlineLocationSelectRequest;
import com.kaolafm.opensdk.http.core.HttpCallback;

import java.util.List;

public class OnlineLocationSelectModel extends BaseModel {

    private OnlineLocationSelectRequest mSearchRequest;

    public OnlineLocationSelectModel(){
        mSearchRequest = new OnlineLocationSelectRequest().setTag(this.toString());

    }

    @Override
    public void destroy() {

    }

    /**
     * 根据结果联想
     * @param keyword
     * @param callback
     */
    public void getAssociateWords(String keyword, HttpCallback<List<OnlineRecomandCityBean>> callback){
        mSearchRequest.getSuggestedWords(keyword,callback);
    }

    /**
     * 获取热门搜索
     * @param callback
     */
    public void getHotSearchWords(HttpCallback<List<OnlineRecomandCityBean>> callback){
        mSearchRequest.getHotWords(callback);
    }


    public void cancelRequest(){
        mSearchRequest.cancel(this.toString());
    }
}
