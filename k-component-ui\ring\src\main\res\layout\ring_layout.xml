<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center">

    <com.kaolafm.kradio.component.ui.ring.view.RingItem
        android:id="@+id/point_0"
        android:layout_centerInParent="true"
        android:layout_width="@dimen/m445"
        android:layout_height="@dimen/m445"
        android:gravity="center"
        android:textSize="@dimen/text_size_title1"/>

    <com.kaolafm.kradio.component.ui.ring.view.RingItem
        android:id="@+id/point_1"
        android:layout_centerInParent="true"
        android:layout_width="@dimen/m445"
        android:layout_height="@dimen/m445"
        android:gravity="center"
        android:textSize="@dimen/text_size_title1"/>

    <com.kaolafm.kradio.component.ui.ring.view.RingItem
        android:id="@+id/point_2"
        android:layout_centerInParent="true"
        android:layout_width="@dimen/m445"
        android:layout_height="@dimen/m445"
        android:gravity="center"
        android:textSize="@dimen/text_size_title1"/>
</RelativeLayout>