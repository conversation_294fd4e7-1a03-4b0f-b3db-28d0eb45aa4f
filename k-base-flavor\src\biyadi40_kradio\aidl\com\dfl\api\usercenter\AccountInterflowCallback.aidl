// AccountInterflowCallback.aidl
package com.dfl.api.usercenter;

/**
 * 账号互通，提供给第三方CPSP的 aidl
 * */
interface AccountInterflowCallback {

    /**
     * 个人中心登录成功后，返回 CP 应用界面接口。
     * 两个场景，1、登录成功之后跳转回去，token为个人中心登录token。2、登录页面点击跳过按钮跳转回去，token为空字符串。
     *
     * @param token 个人中心登录 TOKEN，CP 供应商 可以用此 TOKEN 进行身份校验
     * @param sourceName 跳转到 CP 应用界面的来源，个人 中心固定传”icar”
     */
    void backCp(String token, String sourceName);

    /**
     * 个人中心通知 CP 应用退登接口
     *
     * @param sourceName 通知 CP 应用退登的来源发起应 用，个人中心固定传”icar”
     */
    void logoutCp(String sourceName);

    /**
     * 个人中心通知 CP 应用登录接口
     *
     * @param sourceName 通知 CP 应用退登的来源发起应 用，个人中心固定传”icar”
     */
    void loginCp(String sourceName);

    /**
     * 用户在个人中心设置页面点击登录并绑定按钮，登录并绑定cp
     */
    void loginAndBind();

    /**
     * 用户在个人中心设置页面点击退出并解绑按钮，退出并解绑cp
     */
    void logoutAndUnbind();

    /**
     * cp跳转过来购买 会员/流量套餐，购买成功之后跳转回去
     */
    void onBuyBackCp();

    /**
     * 个人中心跳转到cp登录页面，登录成功之后要跳转回来个人中心。此类场景一律使用此方法从个人中心跳转到cp
     *
     * @param pageType 页面类型，从个人中心哪个页面跳转过来的，要跳转回去。1 代表从个人中心首页跳转过去, 2 代表从会员购买界面跳转过去, 3 代表从车联服务-组合套餐页面跳转过去, 4 代表从超级服务包激活页面跳转过去
     *                  cp在登录成功之后，调用backToIcar(int cpType, int type)方法，将pageType传进type参数即可
     */
    void onloginAndBindBack(int pageType);

    /**
     * 给GUI商城使用的，用户点击个人中心超级服务包激活页面去使用按钮，需要跳转到GUI商城；
     * 如果此时GUI商城未登录，则需要先登录；
     * 皮肤领取完之后，需要跳转回个人中心
     */
    void onUseSkin();

    /**
     * 个人中心获取到cp的token和uid之后，会通过此方法回调通知cp。个人中心如果获取token失败，则此接口返回的cpToken和cpUid都为空字符串
     *
     * @param json json字符串，例：{"cpToken":"abcddf","cpUid":"123"}
     */
    void onAccessCpToken(String json);

    /**
     * 个人中心同意隐私政策之后通知cp
     * aidl版本V1.7弃用
     */
//    void onConsentAgreement();

    /**
     * 个人中心获取到实名认证状态之后，调用此方法通知cpsp
     *
     * @param accountType 0: 测试账号 1: 正式账号
     * @param status 0: 获取不到实名认证状态 1:已实名 2:未实名
    */
    void onAccessCerStatus(int accountType, int status);

}
