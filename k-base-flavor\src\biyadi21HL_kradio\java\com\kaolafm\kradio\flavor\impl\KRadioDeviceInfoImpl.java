//package com.kaolafm.kradio.flavor.impl;
//
//import android.content.Context;
//import android.hardware.bydauto.bodywork.BYDAutoBodyworkDevice;
//import android.util.Log;
//
//import com.kaolafm.kradio.lib.base.flavor.KRadioDeviceInfoInter;
//
//
///******************************************
// * 类描述:
// *
// * @version: V1.0
// * @author: yangshaoning
// * @time: 2019-03-22 11:46
// ******************************************/
//public final class KRadioDeviceInfoImpl implements KRadioDeviceInfoInter {
//    @Override
//    public String getDeviceId(Object... args) {
//        try {
//            Context context = (Context) args[0];
//            return BYDAutoBodyworkDevice.getInstance(context).getAutoVIN();
//        } catch (Exception e) {
//            e.printStackTrace();
//            return null;
//        }
//    }
//
//    @Override
//    public String getCarType(Object... args) {
//        try {
//            Context context = (Context) args[0];
//            String carType = String.valueOf(BYDAutoBodyworkDevice.getInstance(context).getAutoModelName());
//            Log.i("KRadioDeviceInfoImpl", "getCarType: carType = " + carType);
//            return carType;
//        } catch (Exception e) {
//            e.printStackTrace();
//            return null;
//        }
//    }
//}
