<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/splash_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

<!--    <ImageView-->
<!--        android:id="@+id/splash_iv"-->
<!--        app:layout_constraintTop_toTopOf="parent"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:layout_constraintLeft_toLeftOf="parent"-->
<!--        app:layout_constraintRight_toRightOf="parent"-->
<!--        android:src="@drawable/kradio_splash"-->
<!--        android:scaleType="fitXY"-->

<!--        android:layout_width="0dp"-->
<!--        android:layout_height="0dp" />-->

</androidx.constraintlayout.widget.ConstraintLayout>