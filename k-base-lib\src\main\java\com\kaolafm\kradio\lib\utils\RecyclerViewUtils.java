package com.kaolafm.kradio.lib.utils;

import androidx.recyclerview.widget.RecyclerView;

/**
 * @Description:
 * @Author: Maclay
 * @Date: 2021/11/19 3:36 下午
 */
public class RecyclerViewUtils {

    public static void postResetRecyclerView(RecyclerView recyclerView) {
        if (recyclerView != null) {
            recyclerView.post(() -> {
                resetRecyclerView(recyclerView);
            });
        }
    }

    public static void resetRecyclerView(RecyclerView recyclerView) {
        if (recyclerView != null) {
            RecyclerView.Adapter adapter = recyclerView.getAdapter();
            RecyclerView.LayoutManager manager = recyclerView.getLayoutManager();
            recyclerView.setAdapter(null);
            recyclerView.setLayoutManager(null);
            recyclerView.getRecycledViewPool().clear();
            recyclerView.setLayoutManager(manager);
            recyclerView.setAdapter(adapter);
        }
    }
}