package com.kaolafm.kradio.flavor.impl;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.SystemClock;
import android.util.Log;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.AppManager;

import com.kaolafm.kradio.lib.base.flavor.KRadioBackKeyInter;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.service.BYDWidgetService;
import com.kaolafm.opensdk.player.logic.PlayerManager;

import cmgyunting.vehicleplayer.cnr.YunTingWidgetService;


/**
 * 比亚迪车机在近期任务列表 kill 掉考拉fm进程发出通知
 * <p>
 * Created by <PERSON><PERSON><PERSON> on 2018/3/29.
 */

public class KillBroadcastReceiver extends BroadcastReceiver {

    private static final String KILL_EDOG_CAR = "byd.intent.action.KILL_EDOG_CAR";
    private static final String KILL_YUNTING = "byd.intent.action.KILL_YUNTING";
    private static final String TAG = "YunTing_KillBCReceiver";

    @Override
    public void onReceive(Context context, Intent intent) {
        // TODO: 4/15/21 静态广播在某些情况下会接收不到（targetSDK 24+ ），可以尝试动态广播注册
        String action = intent.getAction();
        Log.i(TAG, "action:" + action + " intent " + intent.toString());
        Log.i(TAG, "action" + action + "...receive startTime:" + System.currentTimeMillis());
        if (KILL_YUNTING.equalsIgnoreCase(action) || KILL_EDOG_CAR.equalsIgnoreCase(action)) {
//            context.sendBroadcast(new Intent(ABANDON_AUDIOFOCUS_ACTION));
//            PlayerManager.getInstance().reset();
//            Intent appIntent = new Intent(context, YunTingWidgetService.class);
//            appIntent.setAction(YunTingWidgetService.WIDGET_ACTION_EXIT);
//            context.startService(appIntent);
//            AppManager.getInstance().killAll();
            KRadioBackKeyInter kRadioBackKeyInter = ClazzImplUtil.getInter("KRadioBackKeyImpl");
            if (kRadioBackKeyInter != null) {
                int activitySize = AppManager.getInstance().getActivityList().size();
                long time = SystemClock.elapsedRealtime() - Constants.HubActivityStartTime;
                Log.i(TAG, "action" + action + "...activitySize : " + activitySize + " , time : " + time);
                boolean needFinish = !(activitySize == 0 || time < 1000);
                kRadioBackKeyInter.dealKillYunTingReceiver(this, needFinish);
            }
        }
    }
}
