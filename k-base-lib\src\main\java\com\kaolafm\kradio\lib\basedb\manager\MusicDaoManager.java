package com.kaolafm.kradio.lib.basedb.manager;

import android.text.TextUtils;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.lib.basedb.GreenDaoInterface.OnQueryListener;
import com.kaolafm.kradio.lib.basedb.greendao.SongHistoryDao;
import com.kaolafm.kradio.lib.basedb.greendao.SongHistoryDao.Properties;
import com.kaolafm.kradio.lib.bean.SongHistory;
import com.kaolafm.opensdk.api.music.qq.QQMusicRequest;
import com.kaolafm.opensdk.api.music.qq.model.BaseMusicResult;
import com.kaolafm.opensdk.api.music.qq.model.Song;
import java.util.ArrayList;
import java.util.List;
import org.greenrobot.greendao.query.QueryBuilder;

/**
 * 音乐数据库管理类
 *
 * <AUTHOR>
 * @date 2018/5/14
 */

public class MusicDaoManager extends BaseDBManager<SongHistory> {

    /**
     * 数据最大数量
     */
    private static final int MAX_HISTORY_NUM = 200;

    private static final int MAX_PAGE_SIZE = 50;

    private final SongHistoryDao mSongDao;

    private static final class MusicDaoManagerHolder {

        private static final MusicDaoManager INSTANCE = new MusicDaoManager();
    }

    public static MusicDaoManager getInstance() {
        return MusicDaoManagerHolder.INSTANCE;
    }

    private MusicDaoManager() {
        super();
        mSongDao = mDaoSession.getSongHistoryDao();
    }

    /**
     * 查询某个songId的对象是否存在
     */
    public boolean isExitObject(long id) {
        QueryBuilder<SongHistory> qb = mSongDao.queryBuilder();
        qb.where(Properties.SongId.eq(id));
        long length = qb.buildCount().count();
        return length > 0;
    }

    /**
     * 根据songId删除歌曲。
     */
    public void delete(long songId) {
        runInNewThread(() -> {
            SongHistory song = mSongDao.queryBuilder().where(Properties.SongId.eq(songId)).build().unique();
            if (song != null) {
                mSongDao.delete(song);
            }
            return true;
        }, null);
    }

    public void save(Song song) {
        if (song != null) {
            SongHistory songHistory = new SongHistory();
            translate(song, songHistory, true);
            save(songHistory);
        }
    }

    @Override
    public void save(SongHistory songHistory) {
        runInNewThread(() -> {
            if (songHistory == null) {
                return false;
            }
            songHistory.setTimestamps(System.currentTimeMillis());
            //数据库是否已经存在该单曲，存在就删除重新存储。
            SongHistory oldSongHistory = mSongDao.queryBuilder().where(Properties.SongId.eq(songHistory.getSongId()))
                    .build().unique();
            if (oldSongHistory != null) {
                songHistory.setId(oldSongHistory.getId());
                mSongDao.update(songHistory);
            } else {
                mSongDao.save(songHistory);
            }

            //判断总数量是否超过最大限制， 超过就删除超出数量的最早的数据
            long count = mSongDao.count();
            if (count > MAX_HISTORY_NUM) {
                List<SongHistory> list = mSongDao.queryBuilder().orderAsc(Properties.Timestamps)
                        .limit((int) (count - MAX_HISTORY_NUM)).build().list();
                mSongDao.deleteInTx(list);
            }
            return true;
        }, null);
    }

    /**
     * 查询前200条数据
     */
    public void querySpecifiedCount(OnQueryListener<List<Song>> listener) {
        runInNewThread(() -> {
            List<SongHistory> songList = mSongDao.queryBuilder().orderDesc(Properties.Timestamps)
                    .limit(MAX_HISTORY_NUM)
                    .build().list();
            //QQ音乐批量查询一次最多只能50条，所以超过50条需要分页查询。
            StringBuilder sb = new StringBuilder();
            int size = songList.size();
            //页数
            int num = (int) Math.ceil(size / Float.valueOf(MAX_PAGE_SIZE));
            ArrayList<Song> allSongList = new ArrayList<>();
            QQMusicRequest qqMusicRequest = new QQMusicRequest();
            for (int i = 0; i < num; i++) {
                sb.setLength(0);
                //每页条数，只有最后一页可能不足50条，其他页数都是50条。
                int pageSize = MAX_PAGE_SIZE;
                if (i == num - 1) {
                    pageSize = size % MAX_PAGE_SIZE;
                }
                //第i页的再总数据中的起始索引
                int startIndex = i * MAX_PAGE_SIZE;
                for (int j = 0; j < pageSize; j++) {
                    SongHistory song = songList.get(j + startIndex);
                    String songMid = song.getSongMid();
                    if (!TextUtils.isEmpty(songMid)) {
                        sb.append(songMid).append(",");
                    }
                }
                if (!TextUtils.isEmpty(sb)) {
                    sb.deleteCharAt(sb.length() - 1);
                    BaseMusicResult<List<Song>> songListResult = qqMusicRequest.getSongListBatch(sb.toString());
                    if (songListResult != null) {
                        List<Song> result = songListResult.getResult();
                        if (!ListUtil.isEmpty(result)) {
                            allSongList.addAll(result);
                        }
                    }
                }
            }
            return allSongList;
        }, listener);
    }

    /**
     * 查询最近一条数据
     */
    public void queryLatest(OnQueryListener<Song> listener) {
        runInNewThread(this::queryLatest, listener);
    }

    public Song queryLatest() {
        SongHistory songHistory = mSongDao.queryBuilder().orderDesc(Properties.Timestamps).limit(1).unique();
        Song song = new Song();
        boolean success = translate(song, songHistory, false);
        return success ? song : null;
    }

    public void update(Song song) {
        if (song != null) {
            SongHistory songHistory = new SongHistory();
            boolean translateSuccess = translate(song, songHistory, true);
            if (translateSuccess) {
                update(songHistory);
            }
        }
    }

    private boolean translate(Song song, SongHistory songHistory, boolean isReverse) {
        if (song == null || songHistory == null) {
            return false;
        }
        if (isReverse) {
            songHistory.setAlbumId(song.getAlbumId());
            songHistory.setAlbumMid(song.getAlbumMid());
            songHistory.setAlbumName(song.getAlbumName());
            songHistory.setAlbumPic(song.getAlbumPic());
            songHistory.setGenre(song.getGenre());
            songHistory.setHot(song.getHot());
            songHistory.setIsOnly(song.getIsOnly());
            songHistory.setKSongId(song.getkSongId());
            songHistory.setKSongMid(song.getSongMid());
            songHistory.setLanguage(song.getLanguage());
            songHistory.setPingpong(song.getPingpong());
            songHistory.setPlayable(song.getPlayable());
            songHistory.setPublicTime(song.getPublicTime());
            songHistory.setRecommendReason(song.getRecommendReason());
            songHistory.setSingerId(song.getSingerId());
            songHistory.setSingerMid(song.getSingerMid());
            songHistory.setSingerName(song.getSingerName());
            songHistory.setSingerPic(song.getSingerPic());
            songHistory.setSizeTry(song.getSizeTry());
            songHistory.setSongH5Url(song.getSongH5Url());
            songHistory.setSongId(song.getSongId());
            songHistory.setSongMid(song.getSongMid());
            songHistory.setSongName(song.getSongName());
            songHistory.setSongPlayTime(song.getSongPlayTime());
            songHistory.setSongPlayUrl(song.getSongPlayUrl());
            songHistory.setSongPlayUrlHq(song.getSongPlayUrlHq());
            songHistory.setSongPlayUrlSq(song.getSongPlayUrlSq());
            songHistory.setSongPlayUrlStandard(song.getSongPlayUrlStandard());
            songHistory.setSongSize(song.getSongSize());
            songHistory.setSongSizeHq(song.getSongSizeHq());
            songHistory.setSongSizeSq(song.getSongSizeSq());
            songHistory.setSongSizeStandard(song.getSongSizeStandard());
            songHistory.setTimestamps(song.getTimestamps());
            songHistory.setTryBegin(song.getTryBegin());
            songHistory.setTryEnd(song.getTryEnd());
            songHistory.setUnplayableCode(song.getUnplayableCode());
            songHistory.setUnplayableMsg(song.getUnplayableMsg());
            songHistory.setUserOwnRule(song.getUserOwnRule());
        } else {
            song.setAlbumId(songHistory.getAlbumId());
            song.setAlbumMid(songHistory.getAlbumMid());
            song.setAlbumName(songHistory.getAlbumName());
            song.setAlbumPic(songHistory.getAlbumPic());
            song.setGenre(songHistory.getGenre());
            song.setHot(songHistory.getHot());
            song.setIsOnly(songHistory.getIsOnly());
            song.setKSongId(songHistory.getkSongId());
            song.setKSongMid(songHistory.getSongMid());
            song.setLanguage(songHistory.getLanguage());
            song.setPingpong(songHistory.getPingpong());
            song.setPlayable(songHistory.getPlayable());
            song.setPublicTime(songHistory.getPublicTime());
            song.setRecommendReason(songHistory.getRecommendReason());
            song.setSingerId(songHistory.getSingerId());
            song.setSingerMid(songHistory.getSingerMid());
            song.setSingerName(songHistory.getSingerName());
            song.setSingerPic(songHistory.getSingerPic());
            song.setSizeTry(songHistory.getSizeTry());
            song.setSongH5Url(songHistory.getSongH5Url());
            song.setSongId(songHistory.getSongId());
            song.setSongMid(songHistory.getSongMid());
            song.setSongName(songHistory.getSongName());
            song.setSongPlayTime(songHistory.getSongPlayTime());
            song.setSongPlayUrl(songHistory.getSongPlayUrl());
            song.setSongPlayUrlHq(songHistory.getSongPlayUrlHq());
            song.setSongPlayUrlSq(songHistory.getSongPlayUrlSq());
            song.setSongPlayUrlStandard(songHistory.getSongPlayUrlStandard());
            song.setSongSize(songHistory.getSongSize());
            song.setSongSizeHq(songHistory.getSongSizeHq());
            song.setSongSizeSq(songHistory.getSongSizeSq());
            song.setSongSizeStandard(songHistory.getSongSizeStandard());
            song.setTimestamps(songHistory.getTimestamps());
            song.setTryBegin(songHistory.getTryBegin());
            song.setTryEnd(songHistory.getTryEnd());
            song.setUnplayableCode(songHistory.getUnplayableCode());
            song.setUnplayableMsg(songHistory.getUnplayableMsg());
            song.setUserOwnRule(songHistory.getUserOwnRule());
        }
        return true;
    }

}
