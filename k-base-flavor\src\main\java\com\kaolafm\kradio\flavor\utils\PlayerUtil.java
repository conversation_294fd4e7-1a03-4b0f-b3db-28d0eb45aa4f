//package com.kaolafm.kradio.flavor.utils;
//
//import android.util.Log;
//
//import com.kaolafm.kradio.player.home.HomeDataManager;
//import com.kaolafm.kradio.network.FlavorApiRequest;
//import com.kaolafm.kradio.network.model.DefaultPlayData;
//import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
//import com.kaolafm.opensdk.http.core.HttpCallback;
//import com.kaolafm.opensdk.http.error.ApiException;
//
//public class PlayerUtil {
//    public static final String TAG = "PlayerUtil";
//
//    private PlayerUtil() {
//    }
//
//    /**
//     * 播放网络或者本地
//     * 首页使用的启动播放逻辑
//     */
//    public static void playNetOrLocal() {
//        HomeDataManager.getInstance().playNetOrLocal();
//    }
//
//    /**
//     * 播放渠道电台（例：日产电台、启辰电台）
//     *
//     * @return
//     */
//    public static boolean playDefaultMediaForChannel() {
//        FlavorApiRequest.getInstance().getDefaultPlayInfo(new HttpCallback<DefaultPlayData>() {
//            @Override
//            public void onSuccess(DefaultPlayData defaultPlayData) {
//                if (defaultPlayData != null) {
//                    Log.i("DefaultMediaData", "getDefaultPlay onSuccess: " + defaultPlayData.getName() + "        " + defaultPlayData.getId() + "        " + defaultPlayData.getType());
//                    PlayerManagerHelper.getInstance().start(String.valueOf(defaultPlayData.getId()), defaultPlayData.getType());
//                } else {
//                    playNetOrLocal();
//                }
//            }
//
//            @Override
//            public void onError(ApiException exception) {
//                Log.i("DefaultMediaData", "getDefaultPlay onError: exception = " + exception.toString());
//                playNetOrLocal();
//            }
//        });
//        return true;
//    }
//}
