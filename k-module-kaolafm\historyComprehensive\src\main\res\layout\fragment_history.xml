<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/history_main_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorTransparent">

    <!--    <TextView-->
    <!--        android:id="@+id/tv_history_count"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:textColor="@color/text_count_color"-->
    <!--        android:textSize="@dimen/m22"-->
    <!--        android:layout_marginBottom="@dimen/y15"-->
    <!--        android:paddingTop="@dimen/y15"-->
    <!--        app:layout_constraintLeft_toLeftOf="parent"-->
    <!--        app:layout_constraintTop_toTopOf="parent"-->
    <!--        tools:text="有10条收听历史"-->
    <!--        android:visibility="gone"/>-->

    <com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout
        android:id="@+id/refreshLayout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/historyCount"
        tools:visibility="visible">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_history_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never"
            tools:background="#a0a0"
            android:paddingTop="@dimen/y15"
            android:paddingBottom="@dimen/y15"/>
    </com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout>

    <include
        android:id="@+id/historyCount"
        layout="@layout/item_history_count"
        android:visibility="gone"
        tools:visibility="visible" />

    <ViewStub
        android:id="@+id/vs_history_net_error"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout="@layout/home_no_network_rl" />

    <include
        android:layout_marginTop="@dimen/m100"
        layout="@layout/comprehensive_no_content_layout"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible"
        android:layout_height="wrap_content"
        android:layout_width="match_parent" />

    <include
        android:id="@+id/history_loading"
        layout="@layout/refresh_center"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 隐藏控件，用于所见即可说语音执行滑动操作 -->
    <TextView
        style="@style/ContentDescriptionScrollUp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/refreshLayout" />

    <TextView
        style="@style/ContentDescriptionScrollDown"
        app:layout_constraintBottom_toBottomOf="@id/refreshLayout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
