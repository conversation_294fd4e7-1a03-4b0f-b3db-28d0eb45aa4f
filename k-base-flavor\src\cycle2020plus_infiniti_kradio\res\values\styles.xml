<resources>

    <style name="scroll_view_bar_style">
        <item name="android:scrollbarThumbVertical">@drawable/shape_scroll_bar</item>
        <item name="android:scrollbars">vertical</item>
        <item name="android:scrollbarTrackVertical">@drawable/shape_scroll_bar_track_bg</item>
    </style>

<!--    https://app.huoban.com/tables/2100000007530121/items/2300001678199825?userId=1545533-->
    <style name="Animation">
        <item name="android:activityOpenEnterAnimation">@null</item>
        <item name="android:activityOpenExitAnimation">@null</item>
        <item name="android:activityCloseEnterAnimation">@null</item>
        <item name="android:activityCloseExitAnimation">@null</item>
        <item name="android:taskOpenEnterAnimation">@null</item>
        <item name="android:taskOpenExitAnimation">@null</item>
        <item name="android:taskCloseEnterAnimation">@null</item>
        <item name="android:taskCloseExitAnimation">@null</item>
        <item name="android:taskToFrontEnterAnimation">@null</item>
        <item name="android:taskToFrontExitAnimation">@null</item>
        <item name="android:taskToBackEnterAnimation">@null</item>
        <item name="android:taskToBackExitAnimation">@null</item>
    </style>

    <style name="AppTheme" parent="Theme.AppCompat.NoActionBar">
        <!-- 设置无标题-->
        <!--<item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>//无ActionBar
        <item name="android:windowFullscreen">@bool/isWindowFullScreen</item>//全屏即无通知栏
        <item name="android:windowContentOverlay">@null</item>//是否有遮盖
        <item name="android:windowDisablePreview">false</item>-->
        <item name="android:windowAnimationStyle">@style/Animation</item>
    </style>

    <style name="AppThemeCompat.splash" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowNoTitle">true</item>//无标题
        <item name="android:windowActionBar">false</item>//无ActionBar
        <item name="android:windowFullscreen">@bool/isWindowFullScreen</item>//全屏即无通知栏
        <item name="android:windowContentOverlay">@null</item>//是否有遮盖
        <item name="android:windowBackground">@drawable/kradio_splash</item>
        <item name="android:windowAnimationStyle">@style/Animation</item>
    </style>
</resources>