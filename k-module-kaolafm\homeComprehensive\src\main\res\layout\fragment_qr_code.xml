<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:sfl="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <include layout="@layout/layout_title"
        android:visibility="gone"/>

    <TextView
        android:id="@+id/tv_qr_code_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y70"
        android:textColor="@color/text_color_2"
        android:textSize="@dimen/text_size5"
        tool:text="用QQ扫一扫进行扫码登录" />

    <WebView
        android:id="@+id/wv_qr_code_show"
        android:layout_width="@dimen/m280"
        android:layout_height="@dimen/m280"
        android:minHeight="@dimen/m280"
        android:layout_marginTop="@dimen/y30"
        />
    <ImageView
        android:id="@+id/iv_qr_code_image"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="@null"
        android:scaleType="centerInside"
        android:visibility="gone"
        />

    <TextView
        android:id="@+id/tv_qr_code_teams_of_service"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y50"
        android:gravity="center"
        android:lineSpacingExtra="@dimen/y10"
        android:textColor="@color/text_color_2"
        android:textColorLink="@color/selector_today_recommend_text"
        tool:text="@string/read_and_agree_to_the_terms_of_service_single_line" />

</LinearLayout>
