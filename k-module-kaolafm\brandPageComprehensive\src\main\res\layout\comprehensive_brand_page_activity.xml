<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/home_room"
    android:background="@drawable/bg_home"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    >

    <ImageView
        android:scaleType="fitXY"
        tools:src="@drawable/comprehensive_brand_bg"
        android:id="@+id/home_room_bg_iv"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <LinearLayout
        android:id="@+id/titleRootView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/m60"
        android:layout_marginTop="@dimen/m42"
        android:alpha="0"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:alpha="1">

        <ImageView
            android:id="@+id/brand_car_logo_iv"
            android:layout_width="@dimen/m48"
            android:layout_height="@dimen/m48"
            android:src="@drawable/splash_yunting" />

        <TextView
            android:id="@+id/brand_page_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/m20"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="极限字符极限字符极限字符极限字符极限字符"
            android:textColor="@color/comprehensive_topic_primary_color"
            android:textSize="@dimen/m30"
            app:layout_constraintStart_toEndOf="@+id/brand_car_logo_iv"
            app:layout_constraintTop_toTopOf="@+id/brand_car_logo_iv" />
    </LinearLayout>

    <com.kaolafm.kradio.lib.widget.viewpager.VerticalViewPager
        android:id="@+id/brand_view_page"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbarFadeDuration="1000"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <RelativeLayout
        android:id="@+id/pager_arrow_wrapper"
        android:contentDescription="@string/content_desc_brand_next"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" >

        <ImageView
            android:layout_centerHorizontal="true"
            android:id="@+id/pagerArrow"
            android:layout_width="@dimen/m48"
            android:layout_height="@dimen/m48"
            android:layout_marginBottom="@dimen/m20"
            android:src="@drawable/comprehensive_brand_arrow"
            android:translationY="0dp"
            android:visibility="gone"
            tools:visibility="visible" />
    </RelativeLayout>


<!--    <TextView-->
<!--        android:id="@+id/pre_page"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:background="@color/transparent"-->
<!--        android:contentDescription="@string/content_desc_brand_pre"-->
<!--        android:text="@string/content_desc_brand_pre"-->
<!--        android:textColor="@color/transparent"-->
<!--        android:textSize="1sp"-->
<!--        app:layout_constraintEnd_toStartOf="@id/pagerArrow"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintTop_toTopOf="@id/pagerArrow"-->
<!--        tools:ignore="SmallSp" />-->

<!--    <TextView-->
<!--        android:id="@+id/next_page"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:background="@color/transparent"-->
<!--        android:contentDescription="@string/content_desc_brand_next"-->
<!--        android:text="@string/content_desc_brand_next"-->
<!--        android:textColor="@color/transparent"-->
<!--        android:textSize="1sp"-->
<!--        app:layout_constraintStart_toEndOf="@id/pagerArrow"-->
<!--        app:layout_constraintTop_toTopOf="@id/pagerArrow"-->
<!--        tools:ignore="SmallSp" />-->


    <RelativeLayout
        android:layout_width="0dp"
        android:layout_height="@dimen/m52"
        android:layout_marginStart="@dimen/m172"
        android:layout_marginTop="@dimen/m40"
        android:layout_marginEnd="@dimen/m60"
        app:layout_constraintStart_toEndOf="@id/titleRootView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.kaolafm.kradio.lib.widget.tab.SlidingTabLayout
            android:id="@+id/brand_page_navigation"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:alpha="0"
            android:minWidth="@dimen/m80"
            app:kradio_tl_textSelectSize="@dimen/m28"
            app:kradio_tl_textSize="@dimen/m28"
            app:tl_first_no_padding="false"
            app:tl_indicator_anim_enable="true"
            app:tl_indicator_drawable="@drawable/brabd_page_tab_indicator_drawable"
            app:tl_tab_padding="@dimen/m47"
            app:tl_textBold="SELECT"
            app:tl_textSelectColor="@color/comprehensive_topic_primary_color"
            app:tl_textUnselectColor="@color/comprehensive_topic_primary_color"
            tools:alpha="1" />
    </RelativeLayout>

    <com.kaolafm.kradio.home.comprehensive.playerbar.ComprehensiveMiniPlayerBar
        android:id="@+id/miniPlayer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/m4"
        android:layout_marginBottom="@dimen/m26"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:visibility="visible" />

    <ViewStub
        android:id="@+id/vs_home_no_network"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout="@layout/home_no_network_rl"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        android:id="@+id/pc_loading"
        layout="@layout/refresh_center"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_home_exit_btn"
        android:contentDescription="@string/content_desc_back"
        android:layout_width="@dimen/m84"
        android:layout_height="@dimen/m180"
        android:clickable="true"
        android:focusable="true"
        android:scaleType="fitCenter"
        android:src="@drawable/comprehensive_brand_main_back_icon"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <FrameLayout
        android:id="@+id/frameLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
