package com.kaolafm.kradio.lib.utils.glide;

import com.bumptech.glide.load.model.GlideUrl;
import com.bumptech.glide.signature.EmptySignature;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.imageloader.GlideApp;
import com.kaolafm.kradio.lib.utils.imageloader.GlideModuleConfiguration;

import java.io.File;

/**
 * <AUTHOR>
 **/
public class ImageCacheUtils {


    //    picUrl = UrlUtil.getCustomPicUrl(UrlUtil.PIC_250_250, picUrl);
    File file = new File(AppDelegate.getInstance().getContext().getExternalCacheDir(), GlideModuleConfiguration.IMAGE_DISK_CACHE_NAME);
    //localPicUrl = MultipleImageLoader.getInstance().getCachePathForUri(picUrl);


    public static String getCacheFile2(String url) {
        String rst = null;

        File photoCacheDir = GlideApp.getPhotoCacheDir(AppDelegate.getInstance().getContext());
        //Log.i("novelot", "getCacheFile2: photoCacheDir=" + photoCacheDir);

        File file = new File(AppDelegate.getInstance().getContext().getExternalCacheDir(), GlideModuleConfiguration.IMAGE_DISK_CACHE_NAME);
        //Log.i("novelot", "      : getAbsolutePath=" + file.getAbsolutePath());

        DataCacheKey dataCacheKey = new DataCacheKey(new GlideUrl(url), EmptySignature.obtain());
        SafeKeyGenerator safeKeyGenerator = new SafeKeyGenerator();
        String safeKey = safeKeyGenerator.getSafeKey(dataCacheKey);
        //Log.i("novelot", "      : safeKey=" + safeKey);

        rst = file.getAbsolutePath() + File.pathSeparator + safeKey;
        //Log.i("novelot", "      : rst=" + rst);

//        try {
//            int cacheSize = 100 * 1000 * 1000;
//            DiskLruCache diskLruCache = DiskLruCache.open(new File(getCacheDir(), DiskCache.Factory.DEFAULT_DISK_CACHE_DIR), 1, 1, cacheSize);
//            DiskLruCache.Value value = diskLruCache.get(safeKey);
//            if (value != null) {
//                return value.getFile(0).getAbsolutePath();
//            }
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
        return rst;
    }
}
