package com.kaolafm.kradio.onlineactivity.ui;

import android.content.res.Configuration;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.kaolafm.kradio.lib.base.ui.online.BaseShowHideFragment;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.onlineactivity.ActivityPresenter;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkManager;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.opensdk.api.activity.model.Activity;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonClickReportEvent;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

/**
 * 活动专区(Activity)显示页面
 */
@Route(path = RouterConstance.ONLINE_URL_ACTIVITYS)
public class ActivitiesFragment extends BaseShowHideFragment<ActivityPresenter> implements IActivityView {
    private static final String TAG = ActivitiesFragment.class.getSimpleName();

    ConstraintLayout mRootLayout;
    RecyclerView rvSub;
    ImageView qrCodeImage3;
    TextView qrCodeTextView3;
    ViewStub mLoading;
    TextView mNoActivity;
    ViewStub mErrorPage;

    RelativeLayout mErrorLayout;
    public View mLoadingCL;

    GridLayoutManager mLayoutManager;
    ActivityAdapter mActivityAdapter;
    NetWorkListener mNetWorkListener;
    private List<Activity> activityList = new ArrayList<>();
    private RecyclerViewScrollListener mRecyclerViewScrollListener;

    private int pageNum = 1;
    private int pageSize = 5;

    @Override
    public int getLayoutId() {
        return R.layout.fragment_activity_ui;
    }


    @Override
    protected ActivityPresenter createPresenter() {
        return new ActivityPresenter(this);
    }


    @Override
    public void onResume() {
        super.onResume();
        updateData();
    }

    private boolean isNetworkUnavailable() {
        if (!NetworkUtil.isNetworkAvailable(getContext(), false)) {
            toast(ResUtil.getString(R.string.no_net_work_str));
            return true;
        }
        return false;
    }

    private void toast(String msg) {
        ToastUtil.showOnActivity(getContext(), msg);
    }

    private void showErrorLayout(String error, boolean clickToRetry) {
        if (mErrorLayout == null) {
            mErrorLayout = (RelativeLayout) mErrorPage.inflate();
            TextView tvNetworkNosign = mErrorLayout.findViewById(R.id.tv_error);
            tvNetworkNosign.setText(error);
            // 支持点击重试
            if (clickToRetry) {
                ImageView ivNetworkNoSign = mErrorLayout.findViewById(R.id.iv_error);
                ivNetworkNoSign.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        ViewUtil.setViewVisibility(mErrorLayout, View.GONE);
                        ViewUtil.setViewVisibility(rvSub, View.VISIBLE);
                        updateData();
                    }
                });
            }
        }
        ViewUtil.setViewVisibility(mErrorLayout, View.VISIBLE);
        ViewUtil.setViewVisibility(rvSub, View.GONE);
    }

    @Override
    protected void showAccordingToScreen(int orientation) {
        super.showAccordingToScreen(orientation);

        ViewGroup.MarginLayoutParams layoutParams =
                (ViewGroup.MarginLayoutParams) rvSub.getLayoutParams();
        layoutParams.topMargin = ResUtil.getDimen(R.dimen.m22);

        uploadView(orientation == Configuration.ORIENTATION_LANDSCAPE);

    }

    private void uploadView(boolean isLand) {
//        if (isLand) {
//            ConstraintSet set = new ConstraintSet();
//            set.clone(mRootLayout);
//
//            set.constrainPercentWidth(rvSub.getId(), 0.8f);
//            set.applyTo(mRootLayout);
//        } else {
//            ConstraintSet set = new ConstraintSet();
//            set.clone(mRootLayout);
//            set.constrainPercentWidth(rvSub.getId(), 0.91f);
//            set.applyTo(mRootLayout);
//        }
    }

    @Override
    public void showLoading() {
        if (mLoading != null) {
            if (mLoadingCL == null) {
                mLoadingCL = mLoading.inflate();
            }
        }
        ViewUtil.setViewVisibility(mLoadingCL, View.VISIBLE);
    }

    @Override
    public void hideLoading() {
        ViewUtil.setViewVisibility(mLoadingCL, View.GONE);
    }

    @Override
    public void onActivityInfo(List<Activity> activityList) {
        if (mErrorLayout != null) {
            ViewUtil.setViewVisibility(mErrorLayout, View.GONE);
        }
        ViewUtil.setViewVisibility(rvSub, View.VISIBLE);
//        this.activityList.clear();
        for (Activity activity : activityList) {
            this.activityList.add(activity);
        }

        mActivityAdapter.setDataList(this.activityList);
        //initQrData(activityInfo.qrCodeList);
        if (mPresenter.isHaveNext()) {
            mRecyclerViewScrollListener = new RecyclerViewScrollListener(ActivitiesFragment.this);
            rvSub.addOnScrollListener(mRecyclerViewScrollListener);
        }
    }

    @Override
    public void showEmpty() {

    }

    @Override
    public void hideEmpty() {

    }

    @Override
    public void showError(String error, boolean clickToRetry) {
        showErrorLayout(error, clickToRetry);
    }

    public void updateData() {
        if (isNetworkUnavailable()) {
            showError(ResUtil.getString(R.string.network_nosigin), true);
            return;
        }
        if (mPresenter != null) {
            if (this.activityList != null)
                this.activityList.clear();
            mPresenter.getActivityInfo(pageNum=1, pageSize);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        NetworkManager.getInstance().removeNetworkReadyListener(mNetWorkListener);
    }

    @Override
    public void initView(View view) {
        mRootLayout=view.findViewById(R.id.activity_root_layout);
        rvSub=view.findViewById(R.id.rv_sub);
        qrCodeImage3=view.findViewById(R.id.qrCode_image_3);
        qrCodeTextView3=view.findViewById(R.id.qrCode_textView_3);
        mLoading=view.findViewById(R.id.activity_loading);
        mNoActivity=view.findViewById(R.id.tv_no_activity);
        mErrorPage=view.findViewById(R.id.vs_layout_error_page);



        mLayoutManager = new GridLayoutManager(getContext(), 1, GridLayoutManager.HORIZONTAL, false);
        rvSub.setLayoutManager(mLayoutManager);
        ((DefaultItemAnimator) rvSub.getItemAnimator()).setSupportsChangeAnimations(false);
        mActivityAdapter = new ActivityAdapter();
        rvSub.setAdapter(mActivityAdapter);
        rvSub.addItemDecoration(new RvItemDecoration());
        mActivityAdapter.setOnItemClickListener(new BaseAdapter.OnItemClickListener<Activity>() {
            @Override
            public void onItemClick(View view, int viewType, Activity activity, int position) {

                if (activityList.get(position).getStatus() == 1 && activityList.get(position).getActivityType() == 1) {
                    //未结束并且不是常驻的活动才可以打开详情
                    ButtonClickReportEvent event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_ACTIVITY_PAGE_DATAILS);
                    ReportHelper.getInstance().addEvent(event);
                    ActivitysDetailsDialogFragment dialogFragment
                            = (ActivitysDetailsDialogFragment) new ActivitysDetailsDialogFragment(getActivity());
                    dialogFragment.setActivityBean(activityList.get(position));
                    dialogFragment.show();
//                    CrashPlayerHelper.getInstance().addImmediatelyplayDate(AppDateUtils.getInstance().changeDate(messageBeanList.get(position))).startPlay();
//                    MessageDaoManager.getInstance().updateLook(messageBeanList.get(position).getMsgId());
                }
            }
        });

        NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false);
        // 不管网络状态如何，都需将网络状态监听器注册到网络检测模块，以便在其他场景下引起网络变化时能够自动加载数据。
        mNetWorkListener = new NetWorkListener(this);
        NetworkManager.getInstance().addNetworkReadyListener(mNetWorkListener);

        showAccordingToScreen(ResUtil.getOrientation());
        changeViewLayoutForStatusBar(mRootLayout);
    }

    public class NetWorkListener implements NetworkManager.INetworkReady {
        private WeakReference activityFragment;

        public NetWorkListener(ActivitiesFragment fragment) {
            activityFragment = new WeakReference<>(fragment);
        }

        @Override
        public void networkChange(boolean hasNetwork) {
            Log.i("activityFragment", "Network state changed, param [hasNetwork] value is : " + hasNetwork);
            if (!hasNetwork) {
                return;
            }
            ActivitiesFragment fragment = (ActivitiesFragment) this.activityFragment.get();

            if (fragment != null && fragment.mPresenter != null) {
                if (activityList != null)
                    activityList.clear();
                fragment.mPresenter.getActivityInfo(pageNum=1, pageSize);
            }
        }
    }

    private class RecyclerViewScrollListener extends RecyclerView.OnScrollListener {
        WeakReference<ActivitiesFragment> activitiesFragmentWeakReference;

        public RecyclerViewScrollListener(ActivitiesFragment tabItemFragment) {
            activitiesFragmentWeakReference = new WeakReference<>(tabItemFragment);
        }

        @Override
        public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
            super.onScrollStateChanged(recyclerView, newState);
            //防止重复加载，只有松开手后再加载
            if (newState != RecyclerView.SCROLL_STATE_IDLE) {
                return;
            }
            if (activitiesFragmentWeakReference == null) {
                return;
            }

            GridLayoutManager staggeredGridLayoutManager = (GridLayoutManager) rvSub.getLayoutManager();
            if (staggeredGridLayoutManager == null) {
                return;
            }
            int adapterSize = mActivityAdapter.getItemCount();

            if (staggeredGridLayoutManager.findLastVisibleItemPosition() + 1 < adapterSize) {
                return;
            }
            Log.i(TAG, "滑动到了加载下一页数据");

            if (!mPresenter.isHaveNext()) {
                return;
            }
            Log.i(TAG, "有下一页数据.加载数据");
            mPresenter.getActivityInfo(pageNum += 1, pageSize);
        }

        @Override
        public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
            super.onScrolled(recyclerView, dx, dy);
        }
    }

    public String getPageId() {
        return Constants.ONLINE_PAGE_ID_ACTIVITY;
    }

    public boolean isReportFragment() {
        return true;
    }
}
