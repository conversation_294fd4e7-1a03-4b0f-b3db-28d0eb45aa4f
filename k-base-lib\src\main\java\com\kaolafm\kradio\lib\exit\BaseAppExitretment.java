package com.kaolafm.kradio.lib.exit;

import android.app.Application;

import com.kaolafm.kradio.lib.init.AppInit;
import com.kaolafm.kradio.lib.init.AppInitManager;
import com.kaolafm.kradio.lib.init.AppInitializable;

/**
 * 继承该类并添加注解{@link AppExit}的类会被自动添加到管理中{@link AppExitManager}根据其配置进行退出应用相关接口的回调。
 * 重载了部分方法，用于简化继承类。
 * <AUTHOR>
 * @date 2019-09-10
 */
public abstract class BaseAppExitretment implements AppExitreatment {

    @Override
    public void asyncExit(Application application) {

    }
}
