package com.kaolafm.kradio.home.comprehensive.widget;

import android.content.Context;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.flavor.KRadioBackKeyInter;
import com.kaolafm.kradio.lib.base.flavor.KradioBackToHomeSettingInter;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.KaolaAppConfigData;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.widget.square.SquareImageView;


/**
 * 首页返回桌面view。返回桌面或退出应用。
 *
 * <AUTHOR>
 * @date 2019-09-01
 */
public class HomeBackBar extends ConstraintLayout {

    private OnBackListener mListener;

    public HomeBackBar(Context context) {
        this(context, null);
    }

    public HomeBackBar(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public HomeBackBar(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }
    public void onViewClicked(View view) {
        int id = view.getId();
        if (!AntiShake.check(id)) {
            //退出应用
            if (id == R.id.iv_home_exit) {
                KRadioBackKeyInter kRadioBackKeyInter = ClazzImplUtil.getInter("KRadioBackKeyImpl");
                if (kRadioBackKeyInter != null) {
                    // 解决https://app.huoban.com/tables/2100000007530121/items/2300001338869018?userId=1229522问题
                    kRadioBackKeyInter.onBackPressed(ViewUtil.getActivityFromView(view));
                } else {
                    if (mListener != null) {
                        mListener.exitApp();
                    }
                }
            } else //返回桌面
                if (id == R.id.iv_home_back) {
                    KradioBackToHomeSettingInter guideSettingsInter = ClazzImplUtil.getInter("KradioBackToHomeSettingImpl");
                    if (guideSettingsInter != null) {
                        // 适配车联天下 ，if-else 的问题等改完车联天下 统一进行修改
                        guideSettingsInter.backToHome(getContext());
                    } else {
                        if (mListener != null) {
                            mListener.backToHome();
                        }
                    }

                }
        }
    }

    private void init() {
        setBackgroundColor(ResUtil.getColor(R.color.bg_back_bar));
        View view = LayoutInflater.from(getContext()).inflate(R.layout.view_home_back_btn, this, true);
        SquareImageView btnexit = view.findViewById(R.id.iv_home_exit);
        btnexit.setOnClickListener(v -> onViewClicked(v));
        SquareImageView btnBack = view.findViewById(R.id.iv_home_back);
        btnBack.setOnClickListener(v -> onViewClicked(v));

        // 根据渠道判断是否打开左侧导航栏
        setVisibility(KaolaAppConfigData.getInstance().isShowMainBackHomeSwitch() ? View.VISIBLE : View.GONE);
    }

    public void setOnBackListener(OnBackListener listener) {
        mListener = listener;
    }

    public interface OnBackListener {
        void backToHome();

        void exitApp();
    }

}
