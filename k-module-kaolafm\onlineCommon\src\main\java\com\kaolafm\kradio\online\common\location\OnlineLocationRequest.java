package com.kaolafm.kradio.online.common.location;

import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.http.core.HttpCallback;

import java.util.Map;

public class OnlineLocationRequest extends BaseRequest {
    OnlineLocationService locationService;

    public OnlineLocationRequest() {
        locationService = obtainRetrofitService(OnlineLocationService.class);
    }
    public void getAdress(Map<String, Object> map,HttpCallback<OnlineLocationGetAderssBean> httpCallback){
        doHttpDeal(locationService.getAdress(map), baseResult -> {
            OnlineLocationGetAderssBean success = baseResult.getResult();
            return success;
        }, httpCallback);
    }
}
