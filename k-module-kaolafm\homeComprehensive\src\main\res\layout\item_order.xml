<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/user_order_item_corner"
    android:paddingBottom="@dimen/y26">

    <View
        android:id="@+id/vTop"
        android:layout_width="match_parent"
        android:layout_height="@dimen/y74"
        android:background="@drawable/sh_order_header"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ivVip"
        android:layout_width="@dimen/m36"
        android:layout_height="@dimen/m36"
        android:layout_marginStart="@dimen/x55"
        android:layout_marginTop="@dimen/y1"
        android:scaleType="centerInside"
        android:src="@drawable/comprehensive_ic_vip"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="@id/vTop"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/vTop" />

    <TextView
        android:id="@+id/tvName"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/x12"
        android:layout_marginEnd="@dimen/x30"
        android:drawablePadding="@dimen/x10"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:textColor="@color/text_color_7"
        android:textSize="@dimen/text_size5"
        app:layout_constraintBottom_toBottomOf="@id/vTop"
        app:layout_constraintEnd_toStartOf="@id/tvState"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@id/ivVip"
        app:layout_constraintTop_toTopOf="@id/vTop"
        app:layout_goneMarginStart="@dimen/x55"
        tools:text="VIPVIP" />


    <TextView
        android:id="@+id/tvState"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginEnd="@dimen/x54"
        android:gravity="center_vertical"
        android:textColor="@color/text_color_7"
        android:textSize="@dimen/text_size4"
        app:layout_constraintBottom_toBottomOf="@id/vTop"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/vTop"
        tools:text="已完成" />

    <ImageView
        android:id="@+id/order_item_pay_iv"
        android:layout_width="@dimen/m32"
        android:layout_height="@dimen/m32"
        android:layout_marginRight="@dimen/x8"
        android:src="@drawable/comprehensive_my_order_item_pay_icon"
        app:layout_constraintBottom_toBottomOf="@+id/tvName"
        app:layout_constraintEnd_toStartOf="@+id/tvState"
        app:layout_constraintTop_toTopOf="@+id/tvName" />

    <LinearLayout
        android:id="@+id/llContent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/x55"
        android:orientation="vertical"
        android:paddingTop="@dimen/y11"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/vTop" />
</androidx.constraintlayout.widget.ConstraintLayout>