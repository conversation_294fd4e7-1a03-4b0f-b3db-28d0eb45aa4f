package com.kaolafm.ad.comprehensive.view;

import android.content.Context;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;


/**
 * 音频广告自定义View
 *
 * <AUTHOR>
 * @date 2020-01-06
 */
public class KradioAudioAdContentView extends KradioAdAudioSuperView {
    private OnAdViewVisibilityChanged mOnAdViewVisibilityChanged;

    public KradioAudioAdContentView(Context mContext, KradioAdAudioViewStyleConfig mKradioAdAudioViewStyleConfig) {
        super(mContext, mKradioAdAudioViewStyleConfig);
        initAudioAdView(KradioAudioAdContentView.this);
    }

    @Override
    public void timerend() {
        super.timerend();
    }

    @Override
    public void playAdOver() {
        super.playAdOver();
    }

    @Override
    public void interrupt() {
        super.interrupt();
    }

    @Override
    public void reset() {
        super.reset();
    }

    @Override
    public void show() {
        super.show();
        if (getmAudioAdHornImageView() != null) {
            getmAudioAdHornImageView().setVisibility(View.VISIBLE);
            getmAudioAdHornImageView().startAnimation();
        }
        displayAudioAdView(KradioAudioAdContentView.this);
    }

    @Override
    public void hide() {
        super.hide();
    }

    @Override
    public void updateTime() {
        super.updateTime();
    }

    @Override
    public void pause() {
        super.pause();
    }

    @Override
    public void cancel() {
        cancelAudioAdView(KradioAudioAdContentView.this);
        if (getmAudioAdHornImageView() != null) {
            getmAudioAdHornImageView().setVisibility(View.GONE);
            getmAudioAdHornImageView().stopAnimation();
        }
    }

    @Override
    public KradioAdAudioSuperView create() {
        super.create();
        return null;
    }

    @Override
    public void dismiss() {
        super.dismiss();
    }

    @Override
    public void distroy() {
        super.distroy();
    }

    private void cancelAudioAdView(KradioAdAudioSuperView mKradioAdAudioSuperView) {
        if (mKradioAdAudioSuperView == null || mKradioAdAudioSuperView.getmView() == null) {
            return;
        }
        if (mKradioAdAudioSuperView.getViewGroup() == null) {
            return;
        }
        ViewUtil.setViewVisibility(mKradioAdAudioSuperView.getmView(), View.GONE);
        if (mOnAdViewVisibilityChanged != null)
            mOnAdViewVisibilityChanged.onnAdViewVisibilityChanged(false);

    }

    private void displayAudioAdView(KradioAdAudioSuperView mKradioAdAudioSuperView) {
        if (mKradioAdAudioSuperView.isShowing()) {
            return;
        }
        if (mKradioAdAudioSuperView.getViewGroup() == null) {
            return;
        }
        ViewUtil.setViewVisibility(mKradioAdAudioSuperView.getmView(), View.VISIBLE);
        if (mOnAdViewVisibilityChanged != null)
            mOnAdViewVisibilityChanged.onnAdViewVisibilityChanged(true);
    }

    private void initAudioAdView(KradioAdAudioSuperView mKradioAdAudioSuperView) {
        if (mKradioAdAudioSuperView.isShowing()) {
            return;
        }
        if (mKradioAdAudioSuperView.getViewGroup() == null) {
            return;
        }
        try {
            RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT);
            layoutParams.setMargins(layoutParams.getMarginStart(), ResUtil.getDimen(R.dimen.y20), layoutParams.getMarginEnd(), layoutParams.bottomMargin);
            mKradioAdAudioSuperView.getViewGroup().addView(mKradioAdAudioSuperView.getmView(),
                    layoutParams
            );
        } catch (IllegalStateException e) {
            e.printStackTrace();
        }
    }

    public OnAdViewVisibilityChanged getOnAdViewVisibilityChanged() {
        return mOnAdViewVisibilityChanged;
    }

    public void setOnAdViewVisibilityChanged(OnAdViewVisibilityChanged mOnAdViewVisibilityChanged) {
        this.mOnAdViewVisibilityChanged = mOnAdViewVisibilityChanged;
    }

    public interface OnAdViewVisibilityChanged {
        public void onnAdViewVisibilityChanged(boolean show);
    }
}
