package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.provider.Settings;

import com.kaolafm.kradio.flavor.utils.DeviceInfoUtil;
import com.kaolafm.kradio.lib.base.flavor.DeviceInfoSetting;


/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-10-26 13:59
 ******************************************/
public final class DeviceInfoSettingImpl implements DeviceInfoSetting {

    @Override
    public void setInfoForSDK(Context context) {
        try {
            DeviceInfoUtil.setDeviceIdAndCarType(getDeviceId(), getCarType());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private String getDeviceId(Object... args) {
        Context context = (Context) args[0];
        String deviceId = Settings.Global.getString(context.getContentResolver(), "ivi.system.vehicle.daid");
        return deviceId;
    }

    private String getCarType(Object... args) {
        Context context = (Context) args[0];
        String deviceId = Settings.Global.getString(context.getContentResolver(), "ivi.system.vehicle.daid");
        if (deviceId == null || deviceId.length() < 6) {
            return null;
        }
        return deviceId.substring(0, 6);
    }
}
