package com.kaolafm.kradio.brand.mvp;

import com.kaolafm.kradio.lib.base.mvp.BaseModel;
import com.kaolafm.opensdk.api.brandpage.BrandPageRequest;
import com.kaolafm.opensdk.api.brandpage.model.BrandPageListBean;
import com.kaolafm.opensdk.api.operation.model.column.Column;
import com.kaolafm.opensdk.http.core.HttpCallback;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-03-01
 */
public class BrandPageModel extends BaseModel {

    private BrandPageRequest mBrandPageRequest;

    public BrandPageModel() {
        mBrandPageRequest = new BrandPageRequest().setTag(this.toString());
    }

    /**
     * 获取品牌主页列表
     *
     * @param brandPageId
     * @param callback
     */
    public void getBrandPageList(String brandPageId, HttpCallback<BrandPageListBean> callback) {
        mBrandPageRequest.getBrandPageList(brandPageId, callback);
    }

    /**
     * 获取品牌主页列表内容
     *
     * @param sectionId
     * @param open_uid
     * @param access_token
     * @param callback
     */
    public void getBrandPageContent(String sectionId, String open_uid, String access_token, HttpCallback<List<Column>> callback) {
        mBrandPageRequest.getBrandPageContent(sectionId, open_uid, access_token, callback);
    }

    @Override
    public void destroy() {
        mBrandPageRequest = null;
    }
}
