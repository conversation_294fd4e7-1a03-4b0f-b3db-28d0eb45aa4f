package com.kaolafm.kradio.online.categories.event;

import com.kaolafm.kradio.common.SubcategoryItemBean;

import java.util.List;

public class SubcategoryItemEvent {
    private int type;//0-省数据  1-市数据
    private List<SubcategoryItemBean> data;

    public SubcategoryItemEvent(List<SubcategoryItemBean> data) {
        this.data = data;
        this.type = 0;
    }
    public SubcategoryItemEvent(int type,List<SubcategoryItemBean> data) {
        this.data = data;
        this.type = type;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public List<SubcategoryItemBean> getData() {
        return data;
    }

    public void setData(List<SubcategoryItemBean> data) {
        this.data = data;
    }
}
