## Add project specific ProGuard rules here.
## You can control the set of applied configuration files using the
## proguardFiles setting in build.gradle.
##
## For more details, see
##   http://developer.android.com/guide/developing/tools/proguard.html
#
## If your project uses WebView with J<PERSON>, uncomment the following
## and specify the fully qualified class name to the JavaScript interface
## class:
##-keepclassmembers class fqcn.of.javascript.interface.for.webview {
##   public *;
##}
#
## Uncomment this to preserve the line number information for
## debugging stack traces.
##-keepattributes SourceFile,LineNumberTable
#
## If you keep the line number information, uncomment this to
## hide the original source file name.
##-renamesourcefileattribute SourceFile
## copyright zhonghanwen
##-------------------------------------------基本不用动区域--------------------------------------------
##---------------------------------基本指令区----------------------------------
#-optimizationpasses 5
#-dontskipnonpubliclibraryclassmembers
#-printmapping proguardMapping.txt
#-optimizations !code/simplification/cast,!field/*,!class/merging/*
#-keepattributes *Annotation*,InnerClasses
#-keepattributes Signature
#-keepattributes SourceFile,LineNumberTable
##----------------------------------------------------------------------------
#
##---------------------------------默认保留区---------------------------------
##继承activity,application,service,broadcastReceiver,contentprovider....不进行混淆
#-keep public class * extends android.app.Activity
#-keep public class * extends android.app.Application
#-keep public class * extends android.support.multidex.MultiDexApplication
#-keep public class * extends android.app.Service
#-keep public class * extends android.content.BroadcastReceiver
#-keep public class * extends android.content.ContentProvider
#-keep public class * extends android.app.backup.BackupAgentHelper
#-keep public class * extends android.preference.Preference
#-keep public class * extends android.view.View
#-keep public class com.android.vending.licensing.ILicensingService
#-keep class android.support.** {*;}
#
#-keep public class * extends android.view.View{
#    *** get*();
#    void set*(***);
#    public <init>(android.content.Context);
#    public <init>(android.content.Context, android.util.AttributeSet);
#    public <init>(android.content.Context, android.util.AttributeSet, int);
#}
#-keepclasseswithmembers class * {
#    public <init>(android.content.Context, android.util.AttributeSet);
#    public <init>(android.content.Context, android.util.AttributeSet, int);
#}
##这个主要是在layout 中写的onclick方法android:onclick="onClick"，不进行混淆
#-keepclassmembers class * extends android.app.Activity {
#   public void *(android.view.View);
#}
#
#-keepclassmembers class * implements java.io.Serializable {
#    static final long serialVersionUID;
#    private static final java.io.ObjectStreamField[] serialPersistentFields;
#    private void writeObject(java.io.ObjectOutputStream);
#    private void readObject(java.io.ObjectInputStream);
#    java.lang.Object writeReplace();
#    java.lang.Object readResolve();
#}
#-keep class **.R$* {
# *;
#}
#
#-keepclassmembers class * {
#    void *(*Event);
#}
#
#-keepclassmembers enum * {
#    public static **[] values();
#    public static ** valueOf(java.lang.String);
#}
#-keep class * implements android.os.Parcelable {
#  public static final android.os.Parcelable$Creator *;
#}
##// natvie 方法不混淆
#-keepclasseswithmembernames class * {
#    native <methods>;
#}
#
##保持 Parcelable 不被混淆
#-keep class * implements android.os.Parcelable {
#  public static final android.os.Parcelable$Creator *;
#}
#
##----------------------------------------------------------------------------
#
##---------------------------------webview------------------------------------
#-keepclassmembers class fqcn.of.javascript.interface.for.Webview {
#   public *;
#}
#-keepclassmembers class * extends android.webkit.WebViewClient {
#    public void *(android.webkit.WebView, java.lang.String, android.graphics.Bitmap);
#    public boolean *(android.webkit.WebView, java.lang.String);
#}
#-keepclassmembers class * extends android.webkit.WebViewClient {
#    public void *(android.webkit.WebView, jav.lang.String);
#}
##----------------------------------------------------------------------------
##---------------------------------------------------------------------------------------------------
##---------------------------------实体类---------------------------------
#
##---------------------------------第三方包-------------------------------
#
##支付宝支付
#-keep class com.alipay.android.app.IAlixPay{*;}
#-keep class com.alipay.android.app.IAlixPay$Stub{*;}
#-keep class com.alipay.android.app.IRemoteServiceCallback{*;}
#-keep class com.alipay.android.app.IRemoteServiceCallback$Stub{*;}
#-keep class com.alipay.sdk.app.PayTask{ public *;}
#-keep class com.alipay.sdk.app.AuthTask{ public *;}
#-keep public class * extends android.os.IInterface
##微信支付
#-keep class com.tencent.mm.sdk.openapi.WXMediaMessage {*;}
#-keep class com.tencent.mm.sdk.openapi.** implements com.tencent.mm.sdk.openapi.WXMediaMessage$IMediaObject {*;}
#-keep class com.tencent.wxop.** { *; }
#-dontwarn com.tencent.mm.**
#-keep class com.tencent.mm.**{*;}
#
#-keep class sun.misc.Unsafe { *; }
#
#-keep class com.taobao.** {*;}
#-keep class com.alibaba.** {*;}
#-keep class com.alipay.** {*;}
#-dontwarn com.taobao.**
#-dontwarn com.alibaba.**
#-dontwarn com.alipay.**
#
#-keep class com.ut.** {*;}
#-dontwarn com.ut.**
#
#-keep class com.ta.** {*;}
#-dontwarn com.ta.**
#
#-keep class anet.**{*;}
#-keep class org.android.spdy.**{*;}
#-keep class org.android.agoo.**{*;}
#-dontwarn anet.**
#-dontwarn org.android.spdy.**
#-dontwarn org.android.agoo.**
#
#-keepclasseswithmembernames class com.xiaomi.**{*;}
#-keep public class * extends com.xiaomi.mipush.sdk.PushMessageReceiver
#
#-dontwarn com.xiaomi.push.service.b
#
#-keep class org.apache.http.**
#-keep interface org.apache.http.**
#-dontwarn org.apache.**
#
##okhttp3.x
#-dontwarn com.squareup.okhttp3.**
#-keep class com.squareup.okhttp3.** { *;}
#-dontwarn okio.**
##sharesdk
#-keep class cn.sharesdk.**{*;}
#-keep class com.sina.**{*;}
#-keep class **.R$* {*;}
#-keep class **.R{*;}
#
#-keep class com.mob.**{*;}
#-dontwarn com.mob.**
#-dontwarn cn.sharesdk.**
#-dontwarn **.R$*
#
### nineoldandroids-2.4.0.jar
#-keep public class com.nineoldandroids.** {*;}
#
#####################zxing#####################
#-keep class com.google.zxing.** {*;}
#-dontwarn com.google.zxing.**
###百度定位
#-keep class com.baidu.** {*;}
#-keep class vi.com.** {*;}
#-dontwarn com.baidu.**
#
### okhttp
#-dontwarn com.squareup.okhttp.**
#-keep class com.squareup.okhttp.{*;}
##retrofit
#-dontwarn retrofit.**
#-keep class retrofit.** { *; }
#-keepattributes Signature
#-keepattributes Exceptions
#-dontwarn okio.**
#
##recyclerview-animators
#-keep class jp.wasabeef.** {*;}
#-dontwarn jp.wasabeef.*
#
##multistateview
#-keep class com.kennyc.view.** { *; }
#-dontwarn com.kennyc.view.*
#
## universal-image-loader 混淆
#-dontwarn com.nostra13.universalimageloader.**
#-keep class com.nostra13.universalimageloader.** { *; }
#
##ormlite
#-keep class com.j256.**
#-keepclassmembers class com.j256.** { *; }
#-keep enum com.j256.**
#-keepclassmembers enum com.j256.** { *; }
#-keep interface com.j256.**
#-keepclassmembers interface com.j256.** { *; }
##umeng
## ========= 友盟 =================
#-dontshrink
#-dontoptimize
#-dontwarn com.google.android.maps.**
#-dontwarn android.webkit.WebView
#-dontwarn com.umeng.**
#-dontwarn com.tencent.weibo.sdk.**
#-dontwarn com.facebook.**
#
#
#-keep enum com.facebook.**
#-keepattributes Exceptions,InnerClasses,Signature
#-keepattributes *Annotation*
#-keepattributes SourceFile,LineNumberTable
#
#-keep public interface com.facebook.**
#-keep public interface com.tencent.**
#-keep public interface com.umeng.socialize.**
#-keep public interface com.umeng.socialize.sensor.**
#-keep public interface com.umeng.scrshot.**
#
#-keep public class com.umeng.socialize.* {*;}
#-keep public class javax.**
#-keep public class android.webkit.**
#
#-keep class com.facebook.**
#-keep class com.umeng.scrshot.**
#-keep public class com.tencent.** {*;}
#-keep class com.umeng.socialize.sensor.**
#
#-keep class com.tencent.mm.sdk.modelmsg.WXMediaMessage {*;}
#
#-keep class com.tencent.mm.sdk.modelmsg.** implements com.tencent.mm.sdk.modelmsg.WXMediaMessage$IMediaObject {*;}
#
#-keep class im.yixin.sdk.api.YXMessage {*;}
#-keep class im.yixin.sdk.api.** implements im.yixin.sdk.api.YXMessage$YXMessageData{*;}
#-keepclassmembers class * {
#   public <init> (org.json.JSONObject);
#}
#-keepclassmembers enum * {
#    public static **[] values();
#    public static ** valueOf(java.lang.String);
#}
#
##友盟自动更新
#-keep public class com.umeng.fb.ui.ThreadView {
#}
#-keep public class * extends com.umeng.**
## 以下包不进行过滤
#-keep class com.umeng.** { *; }
#
#

#
#
##AndFix
#-keep class * extends java.lang.annotation.Annotation
#-keepclasseswithmembernames class * {
#    native <methods>;
#}
#
##eventbus 3.0
#-keepattributes *Annotation*
#-keepclassmembers class ** {
#    @org.greenrobot.eventbus.Subscribe <methods>;
#}
#-keep enum org.greenrobot.eventbus.ThreadMode { *; }
#-keepclassmembers class * extends org.greenrobot.eventbus.util.ThrowableFailureEvent {
#    <init>(java.lang.Throwable);
#}
#
#
##EventBus
#-keepclassmembers class ** {
#    public void onEvent*(**);
#}
#-keepclassmembers class ** {
#public void xxxxxx(**);
#}
#
#
#
## for DexGuard only
#-keepresourcexmlelements manifest/application/meta-data@value=GlideModule
#
## #  ######## greenDao混淆  ##########
## # -------------------------------------------
#-keep class de.greenrobot.dao.** {*;}
#-keepclassmembers class * extends de.greenrobot.dao.AbstractDao {
#    public static Java.lang.String TABLENAME;
#}
##jpush极光推送
#-dontwarn cn.jpush.**
#-keep class cn.jpush.** { *; }
#
##activeandroid
#-keep class com.activeandroid.** { *; }
#-dontwarn com.ikoding.app.biz.dataobject.**
#-keep public class com.ikoding.app.biz.dataobject.** { *;}
#-keepattributes *Annotation*
#
##log4j
#-dontwarn org.apache.log4j.**
#-keep class  org.apache.log4j.** { *;}
##下面几行 是环信即时通信的代码混淆
#-keep class com.easemob.** {*;}
#-keep class org.jivesoftware.** {*;}
#-dontwarn  com.easemob.**
#
##融云
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
# public *;
#}
#
#-keepattributes Exceptions,InnerClasses
#
#-keep class io.rong.** {*;}
#
#-keep class * implements io.rong.imlib.model.MessageContent{*;}
#
#-keepattributes Signature
#
#-keepattributes *Annotation*
#
#-keep class sun.misc.Unsafe { *; }
#
#-keep class com.google.gson.examples.android.model.** { *; }
#
#-keepclassmembers class * extends com.sea_monster.dao.AbstractDao {
# public static java.lang.String TABLENAME;
#}
#-keep class **$Properties
#-dontwarn org.eclipse.jdt.annotation.**
#
#-keep class com.ultrapower.** {*;}
##高徳地图
#-dontwarn com.amap.api.**
#-dontwarn com.a.a.**
#-dontwarn com.autonavi.**
#-keep class com.amap.api.**  {*;}
#-keep class com.autonavi.**  {*;}
#-keep class com.a.a.**  {*;}
##---------------------------------反射相关的类和方法-----------------------
#
##---------------------------------与js互相调用的类------------------------
#
##---------------------------------自定义View的类------------------------
#
##SuperID
##由*郭宇翔*贡献混淆代码
##作者Github地址：https://github.com/yourtion
#-keep class **.R$* {*;}
#-keep class com.isnc.facesdk.aty.**{*;}
#-keep class com.isnc.facesdk.**{*;}
#-keep class com.isnc.facesdk.common.**{*;}
#-keep class com.isnc.facesdk.net.**{*;}
#-keep class com.isnc.facesdk.view.**{*;}
#-keep class com.isnc.facesdk.viewmodel.**{*;}
#-keep class com.matrixcv.androidapi.face.**{*;}
#
##retrofit2.x
#-dontwarn retrofit2.**
#-keep class retrofit2.** { *; }
#-keepattributes Signature
#-keepattributes Exceptions
#
##Rxjava RxAndroid
#-dontwarn rx.*
#-dontwarn sun.misc.**
#
#-keepclassmembers class rx.internal.util.unsafe.*ArrayQueue*Field* {
#   long producerIndex;
#   long consumerIndex;
#}
#
#-keepclassmembers class rx.internal.util.unsafe.BaseLinkedQueueProducerNodeRef {
#    rx.internal.util.atomic.LinkedQueueNode producerNode;
#}
#
#-keepclassmembers class rx.internal.util.unsafe.BaseLinkedQueueConsumerNodeRef {
#    rx.internal.util.atomic.LinkedQueueNode consumerNode;
#}
#
##litepal
#-dontwarn org.litepal.
#-keep class org.litepal.* { ; }
#-keep enum org.litepal.*
#-keep interface org.litepal. { ; }
#-keep public class  extends org.litepal.
#-keepattributes Annotation
#-keepclassmembers class * extends org.litepal.crud.DataSupport{*;}
#
##fastJson
#-dontwarn com.alibaba.fastjson.**
#-keep class com.alibaba.fastjson.** { *; }
#
## Okio
#-dontwarn com.squareup.**
#-dontwarn okio.**
#-keep public class org.codehaus.* { *; }
#-keep public class java.nio.* { *; }
## Retrolambda
#-dontwarn java.lang.invoke.*
#
##小米push
#-keepclasseswithmembernames class com.xiaomi.**{*;}
#-keep public class * extends com.xiaomi.mipush.sdk.PushMessageReceiver
#
#
##fresco
## Do not strip any method/class that is annotated with @DoNotStrip
#-keep @com.facebook.common.internal.DoNotStrip class *
#-keepclassmembers class * {
#    @com.facebook.common.internal.DoNotStrip *;
#}
#
#
#
##科大讯飞
##由*	jp1017*贡献混淆代码
##作者Github地址：hhttps://github.com/jp1017
#-keep class com.iflytek.**{*;}
#-keep interface * implements com.alibaba.android.arouter.facade.template.IProvider
#-keep class * implements com.alibaba.android.arouter.facade.template.IProvider
#-keep class * implements com.kaolafm.kradio.lib.base
#
