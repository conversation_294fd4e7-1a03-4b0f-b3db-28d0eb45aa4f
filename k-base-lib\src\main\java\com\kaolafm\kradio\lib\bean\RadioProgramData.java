package com.kaolafm.kradio.lib.bean;

import android.os.Parcel;
import android.os.Parcelable;
import android.view.View;

/**
 * Created by pika<PERSON> on 2015/11/11.
 */
public class RadioProgramData implements Parcelable {

    private int id;                //101,
    private long broadcastId;      //电台ID
    private long nextProgramId;    //下一期节目ID 默认是-1
    private long preProgramId;     //上一期节目ID 默认是-1
    private String title;          //"星星梦之声"
    private String backLiveUrl;    //回放地址
    private String playUrl;        //播放流地址
    private String comperes;       //主播
    private String beginTime;
    private String endTime;
    private long startTime;
    private long finishTime;
    private int status;            // 1:直播中 2:回放 3 未开播
    private int isSubscribe;       //是否预定节目 1预定 0 未预定
    private String desc;//"描述"
    private String broadcastDesc;  //电台描述
    private View view;
    private int isPlaying = 0;  //0 不是正在直播  1 正在直播

    public static int IS_PLAYING_Y = 1;
    public static int IS_PLAYING_N = 0;


    public RadioProgramData() {
    }

    public static final Creator<RadioProgramData> CREATOR = new Creator<RadioProgramData>() {
        @Override
        public RadioProgramData createFromParcel(Parcel in) {
            return new RadioProgramData(in);
        }

        @Override
        public RadioProgramData[] newArray(int size) {
            return new RadioProgramData[size];
        }
    };

    public void setViewTag(View view){
        this.view = view;
    }

    public View getViewTag(){
        return this.view;
    }

    public int getIsPlaying() {
        return isPlaying;
    }

    public void setIsPlaying(int isPlaying) {
        this.isPlaying = isPlaying;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public long getBroadcastId() {
        return broadcastId;
    }

    public void setBroadcastId(long broadcastId) {
        this.broadcastId = broadcastId;
    }

    public long getNextProgramId() {
        return nextProgramId;
    }

    public void setNextProgramId(long nextProgramId) {
        this.nextProgramId = nextProgramId;
    }

    public long getPreProgramId() {
        return preProgramId;
    }

    public void setPreProgramId(long preProgramId) {
        this.preProgramId = preProgramId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getBackLiveUrl() {
        return backLiveUrl;
    }

    public void setBackLiveUrl(String backLiveUrl) {
        this.backLiveUrl = backLiveUrl;
    }

    public String getPlayUrl() {
        return playUrl;
    }

    public void setPlayUrl(String playUrl) {
        this.playUrl = playUrl;
    }

    public String getComperes() {
        return comperes;
    }

    public void setComperes(String comperes) {
        this.comperes = comperes;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(long finishTime) {
        this.finishTime = finishTime;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getIsSubscribe() {
        return isSubscribe;
    }

    public void setIsSubscribe(int isSubscribe) {
        this.isSubscribe = isSubscribe;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getBroadcastDesc() {
        return broadcastDesc;
    }

    public void setBroadcastDesc(String broadcastDesc) {
        this.broadcastDesc = broadcastDesc;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.id);
        dest.writeLong(this.broadcastId);
        dest.writeLong(this.nextProgramId);
        dest.writeLong(this.preProgramId);
        dest.writeString(this.title);
        dest.writeString(this.backLiveUrl);
        dest.writeString(this.playUrl);
        dest.writeString(this.comperes);
        dest.writeString(this.beginTime);
        dest.writeString(this.endTime);
        dest.writeString(this.desc);
        dest.writeString(this.broadcastDesc);
        dest.writeInt(this.status);
        dest.writeInt(this.isSubscribe);
        dest.writeLong(this.startTime);
        dest.writeLong(this.finishTime);
        dest.writeInt(this.isPlaying);
    }

    private RadioProgramData(Parcel in) {
        this.id = in.readInt();
        this.broadcastId = in.readLong();
        this.nextProgramId = in.readLong();
        this.preProgramId = in.readLong();
        this.title = in.readString();
        this.backLiveUrl = in.readString();
        this.playUrl = in.readString();
        this.comperes = in.readString();
        this.beginTime = in.readString();
        this.endTime = in.readString();
        this.desc = in.readString();
        this.broadcastDesc = in.readString();
        this.status = in.readInt();
        this.isSubscribe = in.readInt();
        this.startTime = in.readLong();
        this.finishTime = in.readLong();
        this.isPlaying = in.readInt();
    }
}
