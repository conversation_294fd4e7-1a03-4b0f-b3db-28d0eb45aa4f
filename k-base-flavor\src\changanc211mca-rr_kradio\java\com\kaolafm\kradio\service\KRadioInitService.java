package com.kaolafm.kradio.service;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;
import androidx.annotation.Nullable;
import android.util.Log;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.IntentUtils;
import com.kaolafm.opensdk.player.logic.PlayerManager;

public class KRadioInitService extends Service {

    public static final String TAG = "KRadioInitService";
    public boolean isNeedInitForeground = true;

    @Override
    public void onCreate() {
        super.onCreate();
        if (isNeedInitForeground) {
            int id = (int) System.currentTimeMillis() % 100;
            Log.i(TAG, "onStartCommand startForeground:" + id);
            IntentUtils.getInstance().startForeground(id, this);
            isNeedInitForeground = false;
        }
        Log.i(TAG, "onCreate");
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        Log.i(TAG, "onBind");
        if (!PlayerManager.getInstance().isPlayerInitSuccess()) {
            PlayerManager.getInstance().init(AppDelegate.getInstance().getContext());
        }
        return null;
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.i(TAG, "onStartCommand");
        if (isNeedInitForeground) {
            int id = (int) System.currentTimeMillis() % 100;
            Log.i(TAG, "onStartCommand startForeground:" + id);
            IntentUtils.getInstance().startForeground(id, this);
            isNeedInitForeground = false;
        }
        if (!PlayerManager.getInstance().isPlayerInitSuccess()) {
            PlayerManager.getInstance().init(AppDelegate.getInstance().getContext());
        }
        return START_NOT_STICKY;
    }

    @Override
    public void onDestroy() {
        Log.i(TAG, "onDestroy");
        super.onDestroy();
        isNeedInitForeground = true;
    }

    @Override
    public boolean onUnbind(Intent intent) {
        Log.i(TAG, "onUnbind");
        return super.onUnbind(intent);
    }
}
