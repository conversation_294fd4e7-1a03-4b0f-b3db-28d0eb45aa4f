<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
<LinearLayout
    android:id="@+id/playerbar_activity_ll"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintEnd_toStartOf="@id/rl"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    <include
        layout="@layout/layout_msg_float_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/playerbar_activity"
        android:layout_width="@dimen/default_play_bar_activity_w"
        android:layout_height="@dimen/default_play_bar_height"
        android:layout_gravity="center"
        android:background="@color/playbar_activity_bg"
        android:gravity="center"
        android:orientation="vertical"

        tools:visibility="visible">

        <ImageView
            android:layout_width="@dimen/m33"
            android:layout_height="@dimen/m29"
            android:layout_marginTop="@dimen/y5"
            android:layout_marginBottom="@dimen/y2"
            android:scaleType="fitXY"
            android:src="@drawable/home_activity_icon" />

        <TextView
            android:id="@+id/player_bar_activty_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/y2"
            android:gravity="center_horizontal"
            android:includeFontPadding="false"
            android:lineSpacingExtra="0dp"
            android:text=""
            tools:text="dddd"
            android:textColor="@color/home_activity_txt"
            android:textSize="@dimen/text_size3" />
    </LinearLayout>
</LinearLayout>
    <!-- CPU优化：减少布局层级，将RelativeLayout+LinearLayout合并为ConstraintLayout -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/rl"
        android:layout_width="0dp"
        android:layout_height="@dimen/default_play_bar_height"
        android:background="@color/playerbar_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/playerbar_activity_ll">

        <!--播放按钮 - CPU优化：减少嵌套层级-->
        <LinearLayout
            android:id="@+id/playerbar_live_rl"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:orientation="horizontal"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="gone">

            <ImageView
                android:id="@+id/player_bar_live_playorPause_iv"
                android:layout_width="@dimen/m80"
                android:layout_height="@dimen/m80"
                android:layout_gravity="center"
                android:layout_marginLeft="@dimen/x60"
                android:layout_marginRight="@dimen/x60"
                android:layout_weight="1"
                android:background="@drawable/color_main_button_click_selector"
                android:padding="@dimen/m20"
                android:scaleType="centerCrop"
                app:srcCompat="@drawable/ic_player_bar_pause_normal" />

            <ImageView
                android:id="@+id/player_bar_live_iv"
                android:layout_width="@dimen/m80"
                android:layout_height="@dimen/m80"
                android:layout_gravity="center"
                android:layout_marginRight="@dimen/x60"
                android:layout_weight="1"
                android:background="@drawable/color_main_button_click_selector"
                android:padding="@dimen/m20"
                android:scaleType="centerCrop"
                android:visibility="gone"
                app:srcCompat="@drawable/player_bar_live" />
        </LinearLayout>
        <!--直播布局按钮 - CPU优化：减少嵌套层级-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/player_bar_play_rl"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/playerbar_live_rl"
            app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/player_bar_previous"
                    android:layout_width="@dimen/m80"
                    android:layout_height="@dimen/m80"
                    android:layout_gravity="center"
                    android:layout_marginStart="@dimen/x60"
                    android:layout_marginRight="@dimen/x60"
                    android:background="@drawable/color_main_button_click_selector"
                    android:padding="@dimen/m20"
                    android:scaleType="centerCrop"
                    android:visibility="visible"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/comprehensive_playerbar_prev" />

                <fr.castorflex.android.circularprogressbar.CircularProgressBar
                    android:id="@+id/player_bar_loading"
                    style="@style/CustomerCircularProgressBar"
                    android:layout_width="@dimen/m80"
                    android:layout_height="@dimen/m80"
                    android:visibility="invisible"
                    app:cpb_color="@color/circular_progress_color"
                    app:cpb_stroke_width="@dimen/loading_progress_width"
                    app:layout_constraintBottom_toBottomOf="@id/player_bar_play_ll"
                    app:layout_constraintEnd_toEndOf="@id/player_bar_play_ll"
                    app:layout_constraintStart_toStartOf="@id/player_bar_play_ll"
                    app:layout_constraintTop_toTopOf="@id/player_bar_play_ll" />

                <LinearLayout
                    android:id="@+id/player_bar_play_ll"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/player_bar_previous"
                    app:layout_constraintTop_toTopOf="parent">

                    <ImageView
                        android:id="@+id/player_bar_play"
                        android:layout_width="@dimen/m80"
                        android:layout_height="@dimen/m80"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="@dimen/x60"
                        android:layout_marginRight="@dimen/x60"
                        android:background="@drawable/color_main_button_click_selector"
                        android:padding="@dimen/m20"
                        android:scaleType="centerCrop"
                        app:srcCompat="@drawable/comprehensive_playerbar_play" />

                </LinearLayout>

                <!--为了兼容 直播 ，广播状态下，播放前一个按钮的出现和隐藏，引发位置变动-->
                <LinearLayout
                    android:id="@+id/rl_previous_next"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_centerInParent="true"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/player_bar_play_ll"
                    app:layout_constraintTop_toTopOf="parent">

                    <ImageView
                        android:id="@+id/player_bar_next"
                        android:layout_width="@dimen/m80"
                        android:layout_height="@dimen/m80"
                        android:layout_gravity="center"
                        android:layout_marginRight="@dimen/x60"
                        android:background="@drawable/color_main_button_click_selector"
                        android:padding="@dimen/m20"
                        android:scaleType="centerCrop"
                        app:srcCompat="@drawable/comprehensive_playerbar_next" />

                    <ImageView
                        android:id="@+id/player_bar_collect"
                        android:layout_width="@dimen/m80"
                        android:layout_height="@dimen/m80"
                        android:layout_gravity="center"
                        android:layout_marginRight="@dimen/x60"
                        android:background="@drawable/color_main_button_click_selector"
                        android:padding="@dimen/m20"
                        android:scaleType="centerCrop"
                        android:visibility="gone"
                        app:srcCompat="@drawable/playerbar_collect" />

                    <com.kaolafm.kradio.player.comprehensive.play.view.AIRadioMinusFeedbackView
                        android:id="@+id/ai_radio_minus_feed_back_view"
                        android:layout_width="@dimen/m75"
                        android:layout_height="@dimen/m75"
                        android:layout_gravity="center"
                        android:layout_marginRight="@dimen/x65"
                        android:background="@drawable/color_main_button_click_selector"
                        android:visibility="gone"
                        app:minus_location="1" />
                </LinearLayout>

                <!--为了兼容 直播 ，广播状态下，播放前一个按钮的出现和隐藏，引发位置变动-->
                <!--                <fr.castorflex.android.circularprogressbar.CircularProgressBar xmlns:app="http://schemas.android.com/apk/res-auto"-->
                <!--                    android:id="@+id/player_bar_loading"-->
                <!--                    style="@style/CustomerCircularProgressBar"-->
                <!--                    android:layout_width="@dimen/m80"-->
                <!--                    android:layout_height="@dimen/m80"-->
                <!--                    android:layout_alignLeft="@+id/player_bar_play"-->
                <!--                    android:layout_alignTop="@+id/player_bar_play"-->
                <!--                    android:layout_alignRight="@+id/player_bar_play"-->
                <!--                    android:layout_alignBottom="@+id/player_bar_play"-->
                <!--                    android:visibility="invisible"-->
                <!--                    app:cpb_color="@color/circular_progress_color"-->
                <!--                    app:cpb_stroke_width="@dimen/loading_progress_width" />-->

                <!--<ImageView-->
                <!--android:id="@+id/player_bar_collect"-->
                <!--android:layout_width="@dimen/m80"-->
                <!--android:layout_height="@dimen/m80"-->
                <!--android:layout_centerVertical="true"-->
                <!--android:layout_marginRight="@dimen/x60"-->
                <!--android:layout_toRightOf="@id/rl_previous_next"-->
                <!--android:background="@drawable/color_main_button_click_selector"-->
                <!--android:padding="@dimen/m20"-->
                <!--android:scaleType="centerCrop"-->
                <!--app:srcCompat="@drawable/playerbar_collect" />-->

            </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- CPU优化：内容区域布局 -->
        <RelativeLayout
            android:id="@+id/player_bar_constrantlayout_rl"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/player_bar_play_rl"
            app:layout_constraintTop_toTopOf="parent">

                <View
                    android:layout_width="@dimen/player_bar_audio_cover_size"
                    android:layout_height="@dimen/player_bar_audio_cover_size"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="@dimen/x20"
                    android:background="@drawable/sh_bg_r8l_fff" />

                <com.kaolafm.kradio.component.ui.base.view.OvalImageView
                    android:id="@+id/player_bar_cover"
                    android:layout_width="@dimen/player_bar_audio_cover_size"
                    android:layout_height="@dimen/player_bar_audio_cover_size"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="@dimen/x20"
                    android:scaleType="fitXY"
                    app:srcCompat="@drawable/media_default_pic" />

                <!--文字和标题-->
                <com.kaolafm.kradio.component.ui.base.view.OvalImageView
                    android:id="@+id/player_bar_live_flag"
                    android:layout_width="@dimen/m34"
                    android:layout_height="@dimen/m18"
                    android:layout_alignStart="@+id/player_bar_cover"
                    android:layout_alignTop="@+id/player_bar_cover"
                    android:scaleType="centerCrop"
                    android:src="@drawable/ic_live_s"
                    android:visibility="visible"
                    app:rid_type="1" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_toRightOf="@id/player_bar_cover"
                    android:gravity="center_vertical"
                    android:orientation="vertical">

                    <include layout="@layout/bar_player_horizontal_inner" />
                </LinearLayout>

                <com.kaolafm.kradio.component.ui.base.view.OvalImageView
                    android:id="@+id/vip_icon"
                    android:layout_width="@dimen/x34"
                    android:layout_height="@dimen/x18"
                    android:layout_alignStart="@+id/player_bar_cover"
                    android:layout_alignTop="@+id/player_bar_cover"
                    android:visibility="gone"
                    app:rid_type="1"
                    tools:src="@drawable/comprehensive_icon_vip" />

            <ViewStub
                android:id="@id/nav_home_viewStub"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_centerVertical="true"
                android:layout="@layout/home_bar_layout" />
        </RelativeLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.kaolafm.kradio.common.widget.IndicatorSeekBar
        android:id="@+id/player_bar_progress"
        android:layout_width="0dp"
        android:layout_height="@dimen/m36"
        android:layout_marginBottom="@dimen/m99"
        android:background="@null"
        android:clipChildren="false"
        android:max="0"
        android:maxHeight="@dimen/y6"
        android:minHeight="@dimen/y6"
        android:paddingStart="0dp"
        android:paddingEnd="0dp"
        android:progressDrawable="@drawable/ll_play_bar_seekbar"
        android:thumb="@null"
        android:thumbOffset="100dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/playerbar_activity_ll"
        app:seek_bg_radius="@dimen/y14"
        app:seek_color="#181C22"
        app:seek_txt_size="@dimen/text_size1"
        app:txt_offset="@dimen/x14"
        tools:max="100"
        tools:progress="10" />
</androidx.constraintlayout.widget.ConstraintLayout>
