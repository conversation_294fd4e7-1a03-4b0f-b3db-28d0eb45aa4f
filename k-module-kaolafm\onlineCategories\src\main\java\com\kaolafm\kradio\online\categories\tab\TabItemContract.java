package com.kaolafm.kradio.online.categories.tab;

import com.kaolafm.kradio.common.SubcategoryItemBean;
import com.kaolafm.kradio.online.categories.adapter.HorizontalSubcategoryAdapter;

import java.util.List;

/**
 * <AUTHOR>
 **/
public class TabItemContract {
    public interface IPresenter extends com.kaolafm.kradio.lib.base.mvp.IPresenter {
        void loadData();

        void loadMore();

        boolean isHasNextPage();

        void onClick(SubcategoryItemBean subcategoryItemBean, int position);

        void onClick(SubcategoryItemBean subcategoryItemBean, HorizontalSubcategoryAdapter adapter, int position);

        void registerListener();

        void unregisterListener();
    }

    public interface IView extends com.kaolafm.kradio.lib.base.mvp.IView {
        void showData(List<SubcategoryItemBean> data);

        void showMoreData(List<SubcategoryItemBean> data);

        void showError(Exception exception);

        void showMoreDataError(Exception exception);

        void setSelected();
    }
}
