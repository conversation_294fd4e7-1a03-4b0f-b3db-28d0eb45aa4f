package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.view.View;

import com.kaolafm.kradio.lib.base.flavor.KRadioHomeExitInter;

import io.reactivex.annotations.Nullable;

public class KRadioHomeExitImpl implements KRadioHomeExitInter {
    @Override
    public boolean isShowHomeExitBtn(View view, boolean show, @Nullable Object... args) {
        view.setVisibility(show ? View.VISIBLE : View.GONE);
        view.setOnClickListener(v -> {
            if (args != null && args[0] instanceof Activity) {
                ((Activity) args[0]).finish();
            }
        });
        return false;

    }
}
