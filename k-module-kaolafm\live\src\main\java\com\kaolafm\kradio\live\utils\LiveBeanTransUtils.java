package com.kaolafm.kradio.live.utils;

import android.text.TextUtils;

import com.kaolafm.kradio.live.player.NimManager;
import com.kaolafm.opensdk.api.live.model.ChatRoomMessageInfo;
import com.kaolafm.opensdk.api.live.model.ChatUserInfo;
import com.kaolafm.opensdk.api.live.model.MessageBean;
import com.kaolafm.opensdk.api.yunxin.model.GiftRankUser;
import com.kaolafm.opensdk.api.yunxin.model.LiveCommentStatusMsg;
import com.kaolafm.opensdk.api.yunxin.model.LiveOtherUserEnterExitMsg;
import com.kaolafm.opensdk.api.yunxin.model.LiveTextMsg;

public class LiveBeanTransUtils {

    /**
     * 加入聊天室使用的扩展字段（头像）key值
     */
    public static final String AVATAR_KEY = "avatar";

    /**
     * 加入聊天室使用的扩展字段（昵称）key值
     */
    public static final String NICK_NAME_KEY = "nickName";

    /**
     * 转换通过接口获取的聊天室成员信息
     */
    public static ChatUserInfo chatRoomMember2ChatUserInfo(LiveOtherUserEnterExitMsg liveOtherUserEnterExitMsg) {
        ChatUserInfo userInfo = new ChatUserInfo();

        if (liveOtherUserEnterExitMsg != null) {
            userInfo.setUserName(liveOtherUserEnterExitMsg.getAccount());
            userInfo.setAvatar(liveOtherUserEnterExitMsg.getAvatar());
            userInfo.setNickName(liveOtherUserEnterExitMsg.getNickName());
            userInfo.setUid(liveOtherUserEnterExitMsg.getDeviceId());
        }
        return userInfo;
    }

    /**
     * 转换通过接口获取的聊天室成员信息
     */
    public static ChatUserInfo chatRoomMember2ChatUserInfo(GiftRankUser giftRankUser) {
        ChatUserInfo userInfo = new ChatUserInfo();

        if (giftRankUser != null) {
            userInfo.setUserName(giftRankUser.getAccount());
            userInfo.setAvatar(giftRankUser.getAvatar());
            userInfo.setNickName(giftRankUser.getNickname());
            userInfo.setUid(giftRankUser.getOpenUid());
        }
        return userInfo;
    }

    /**
     * 转换通过接口获取的聊天室历史消息数据
     */
    public static MessageBean createMessageBean(ChatRoomMessageInfo msg) {
        MessageBean bean = new MessageBean();
        bean.account = msg.getSenderAccount();
        bean.contentString = msg.getMsgType() == 0 ? msg.getMsgContent() : msg.getMsgAudioContent();
        bean.chatTime = String.valueOf(msg.getSendTime());
        bean.type = NimManager.MSG_TYPE_RECEIVE;
        bean.userIconUrl = msg.getSenderAvatar();
        bean.nickName = msg.getSenderNickName();
        return bean;
    }

    public static MessageBean createMessageBeanFromSocketTextMsg(LiveTextMsg liveTextMsg){
        MessageBean bean = new MessageBean();
        bean.id = liveTextMsg.getId();
        bean.type = NimManager.MSG_TYPE_RECEIVE;
        bean.account = liveTextMsg.getSenderAccount();
        bean.contentString = liveTextMsg.getMsgText();
        bean.chatTime = String.valueOf(liveTextMsg.getSendTime());
        bean.userIconUrl = liveTextMsg.getSenderAvatar();
        bean.nickName = liveTextMsg.getSenderNickName();
        return bean;
    }

    public static boolean isSameMsgData(MessageBean messageBean, LiveCommentStatusMsg liveCommentStatusMsg){
        if (messageBean == null || TextUtils.isEmpty(messageBean.id)){
            return false;
        }
        if (liveCommentStatusMsg == null || TextUtils.isEmpty(liveCommentStatusMsg.getId())){
            return false;
        }
        return TextUtils.equals(messageBean.id, liveCommentStatusMsg.getId());
    }
}
