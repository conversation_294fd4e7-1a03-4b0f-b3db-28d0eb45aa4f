package com.kaolafm.kradio.upgrader.net.api;

import com.kaolafm.kradio.upgrader.net.model.QueryVersionData;

import java.util.Map;

import okhttp3.RequestBody;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.HeaderMap;
import retrofit2.http.POST;
import retrofit2.http.Streaming;
import retrofit2.http.Url;

/**
 * <AUTHOR> on 2019/4/1.
 */

public interface RequestService {
    @POST("thirdParty/queryVersion")
    Call<QueryVersionData> requestQueryVersion(@HeaderMap Map<String, String> headers, @Body RequestBody body);

}
