package com.kaolafm.kradio.online.categories.radio;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.View;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.online.categories.BaseTabFragment;
import com.kaolafm.kradio.online.categories.CategoryConstant;
import com.kaolafm.kradio.online.categories.ErrorCode;
import com.kaolafm.kradio.online.categories.tab.TabContract;
import com.kaolafm.opensdk.http.error.ApiException;

/**
 * 专栏节目、在线广播的二级页面
 *
 * <AUTHOR>
 **/
public class RadioTabFragment extends BaseTabFragment {
    private long mSonCode;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    protected boolean isReportFragment() {
        return true;
    }

    @Override
    public String getPageId() {
        Bundle arg = getArguments();
        if (arg != null) {
            return arg.getString(CategoryConstant.PAGE_ID);
        }
        return super.getPageId();
    }

    @Override
    public void initView(View view) {
        super.initView(view);
        mStbSubcategoryTabTitle.setSnapOnTabClick(true);
    }

    @Override
    public void initArgs() {
        super.initArgs();
        Bundle arg = getArguments();
        if (arg != null) {
            mSonCode = arg.getLong(CategoryConstant.SUBCATEGORY_ID);
        }
    }

    @Override
    public void showError(Exception e) {
        //  Log.i(TAG, "showError: error = " + e.getMessage());
        String str = null;
        if (e instanceof ApiException) {
            switch (((ApiException) e).getCode()) {
                case ErrorCode.NO_NET:
                    isLoaded = false;
                    //str = ResUtil.getString(R.string.no_net_work_str);
                    break;
                case ErrorCode.NO_SUBCATEGORY:
                case ErrorCode.TYPE_ERROR:
                    str = ResUtil.getString(R.string.online_error_subcategory_is_null);
                    break;
                default:
            }
        }
        if (str != null) {
            ToastUtil.showError(getContext(), str);
        }

    }


    @Override
    protected TabContract.IPresenter createPresenter() {
        return new RadioTabPresenter(this, mSonCode,getPageId());
    }

    @Override
    protected void showAccordingToScreen(int orientation) {
        super.showAccordingToScreen(orientation);
//        int textSize = ResUtil.getDimen(R.dimen.subtitle_tab_title_size);
//        changeTabTitleTextSize(textSize,textSize);
    }

    public void changeTabTitleTextSize(int noraml, int selected) {
        mStbSubcategoryTabTitle.setTextsize(ScreenUtil.px2dp(noraml));
        mStbSubcategoryTabTitle.setTextSelectSize(ScreenUtil.px2dp(selected));
    }

}
