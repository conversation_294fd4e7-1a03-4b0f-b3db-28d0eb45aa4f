package com.kaolafm.kradio.flavor.impl;

import android.app.Application;
import android.content.Context;
import android.util.Log;

import com.byd.diLinkAccount.BindStateCallBack;
import com.byd.diLinkAccount.DiLinkAccountSDK;
import com.byd.diLinkAccount.DiLinkAccountService;

/**
 * <AUTHOR>
 **/
public class BydAccountHelper {

    private static final String TAG = "BydAccountHelper";
    private static BydAccountHelper mInstance;
    private Context mContext;

    private BydAccountHelper(Context context) {
        mContext = context;
    }

    /**
     * 获取实例
     * @param context
     * @return
     */
    public static BydAccountHelper getInstance(Context context) {
        if (mInstance == null) {
            synchronized (BydAccountHelper.class) {
                if (mInstance == null) {
                    mInstance = new BydAccountHelper(context);
                }
            }
        }
        return mInstance;
    }


    /**
     * 比亚迪账号互通sdk初始化
     */
    public void init() {
        Log.i(TAG, "init start");
        if(isBYDAccountAppInstalled()){
            DiLinkAccountSDK.initSDK((Application) mContext);
        }
    }

    /**
     * 判断本地是否已安装比亚迪车机账号
     */
    public boolean isBYDAccountAppInstalled(){
        return DiLinkAccountSDK.isBYDAccountAppInstalled(mContext);
    }

    /**
     * 获取三方账号绑定比亚迪账号状态接口
     * @param userId 三方账号 id，三方未登录则传空字符串
     * @param callback 回调接口
     */
    public void getAccountBindState(String userId, String token, String nickName, String avatar, BindStateCallBack callback){
        if(isBYDAccountAppInstalled()) {
            Log.i(TAG, "getAccountBindState");
            try {
                DiLinkAccountService.getInstance().getAccountBindState(userId==null?"":userId,
                        token==null?"":token,
                        nickName==null?"":nickName,
                        avatar==null?"":avatar,
                        callback);
            }catch (Exception e){
            Log.e(TAG, e.getMessage());
        }

        }
    }

    /**
     * 三方账号操作比亚迪账号绑定行为接口
     * @param action 操作类型(
     *               1：回传三方账号信息，收到比亚迪账号发起绑定三方账号查询广播时调用
     *               2：三方账号注销，三方生态应用启动时自动登录流程中删除绑定关系时调用
     *               3：三方更新当前三方已登录的三方账号信息，在三方登录账号、刷新账号信息时（包含由于比亚迪账号 联动而登录、刷新）调用，即三方生态应用启动时自动登录流程中登录成功回传登录的账号信息时调用； 比亚迪车辆账号与三方生态应用账号绑定实现快速登录流程中登录成功时调用)
     * @param userId
     * @param token
     * @param nickName
     * @param avatar
     */
    public void setBYDAccountBind(int action,String userId,String token,String nickName,String avatar){
        if(isBYDAccountAppInstalled()) {
            Log.i(TAG, "getAccountBindState");
            try {
                DiLinkAccountService.getInstance().setBYDAccountBind(action,
                        userId==null?"":userId,
                        token==null?"":token,
                        nickName==null?"":nickName,
                        avatar==null?"":avatar);
            }catch (Exception e){
                Log.e(TAG, e.getMessage());
            }

        }
    }

}
