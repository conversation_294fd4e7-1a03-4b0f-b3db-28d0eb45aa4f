package com.kaolafm.kradio.flavor.impl;

import android.annotation.SuppressLint;
import android.app.ActivityManager;
import android.content.Context;
import android.util.Log;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioPlayLogicInter;

import com.kaolafm.opensdk.player.core.listener.OnPlayLogicListener;

import java.util.List;


/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-07-05 12:16
 ******************************************/
@SuppressLint("LongLogTag")
public final class KRadioAudioPlayLogicImpl implements KRadioAudioPlayLogicInter {
    private final static String TAG = "KRadioAudioPlayLogicImpl";

    public KRadioAudioPlayLogicImpl() {
        PlayerCustomizeManager.getInstance().setPlayLogicListener(new OnPlayLogicListener() {
            @Override
            public boolean onPlayLogicDispose() {
                int currentFocus = PlayerManager.getInstance().getCurrentAudioFocusStatus();
                Log.i(TAG, "isAppOnForeground--------->currentFocus = " + currentFocus);
                if ((currentFocus < 0)) {
                    boolean isAppOnForeground = isAppOnForeground();
                    Log.i(TAG, "isAppOnForeground--------->isAppOnForeground = " + isAppOnForeground);
                    return !isAppOnForeground;
                }
                return false;
            }
        });
    }

    @Override
    public boolean autoPlayAudio(Object... args) {
        return false;
    }

    @Override
    public boolean requestAudioFocus(Object... args) {
        return false;
    }

    @Override
    public boolean resumeAudioPlayLogic(Object... args) {
        return false;
    }

    @Override
    public boolean restartAudioPlayLogic(Object... args) {
        return false;
    }
    /**
     * 监测APP是否在前台运行
     *
     * @return true为是，false为否
     */
    private boolean isAppOnForeground() {
        Context context = AppDelegate.getInstance().getContext();
        ActivityManager activityManager = (ActivityManager) context.getSystemService(
                Context.ACTIVITY_SERVICE);
        String packageName = context.getPackageName();
        List<ActivityManager.RunningAppProcessInfo> appProcesses = activityManager.getRunningAppProcesses();
        if (appProcesses == null) {
            return false;
        }
        for (ActivityManager.RunningAppProcessInfo appProcess : appProcesses) {
            if (appProcess.processName.equals(packageName)
                    && appProcess.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND) {
                return true;
            }
        }
        return false;
    }
}
