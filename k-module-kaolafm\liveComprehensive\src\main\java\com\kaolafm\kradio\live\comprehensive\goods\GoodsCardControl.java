package com.kaolafm.kradio.live.comprehensive.goods;

import android.content.Context;
import androidx.constraintlayout.widget.ConstraintLayout;
import android.util.Log;
import android.view.View;
import android.widget.LinearLayout;

import com.kaolafm.kradio.live.comprehensive.goods.ui.GoodsCardLayout;
import com.kaolafm.opensdk.api.yunxin.model.GoodsCardMsg;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.DialogExposureEvent;
import com.kaolafm.report.util.ReportConstants;

/**
 * <AUTHOR>
 * @date 2023-02-14
 */
public class GoodsCardControl implements GoodsCardLayout.OnGetLocationInWindowListener {
    private final String TAG = "GoodsCardControl";
    private final String pageId;

    private LinearLayout mShopCardParentLayout;
    private GoodsCardLayout mGoodsCardLayout;
    private GoodsCardMsg mGoodsCardMsg;
    private boolean isShowing;

    private GoodsCardLayout.OnShopPurchaseListener mOnShopPurchaseListener;

    public GoodsCardControl(Context context, LinearLayout parentLayout, View baseView, String pageId) {
        mShopCardParentLayout = parentLayout;
        mGoodsCardLayout = new GoodsCardLayout(context, baseView, this);
        this.pageId = pageId;
        mGoodsCardLayout.setPageId(pageId);
    }

    public void show(GoodsCardMsg goodsCardMsg, int position) {
        if (mGoodsCardLayout == null) {
            return;
        }
        if (mShopCardParentLayout.indexOfChild(mGoodsCardLayout) == -1) {
            mShopCardParentLayout.addView(mGoodsCardLayout);

        }
        mGoodsCardLayout.show(goodsCardMsg, position);
        mGoodsCardMsg = goodsCardMsg;
        isShowing = true;
        startTime = System.currentTimeMillis();

    }

    public boolean isShowing() {
        return isShowing;
    }

    public void dismiss() {
        if(mGoodsCardLayout!=null){
            mGoodsCardLayout.dismiss();
        }
        reportPageShowEvent();
        isShowing = false;
    }

    public void destory(){
        if(mGoodsCardLayout!=null && mShopCardParentLayout!=null){
            if (mShopCardParentLayout.indexOfChild(mGoodsCardLayout) != -1) {
                mShopCardParentLayout.removeView(mGoodsCardLayout);
                mGoodsCardLayout = null;
            }
        }
    }


    public void setOnShopPurchaseListener(GoodsCardLayout.OnShopPurchaseListener onShopPurchaseListener) {
        if (onShopPurchaseListener != null) {
            mGoodsCardLayout.setOnShopPurchaseListener(onShopPurchaseListener);
        }
    }

    @Override
    public void onGetLocation(int width, int height) {
        Log.i(TAG, "onGetLocation: width=" + width + "; height=" + height);
        ConstraintLayout.LayoutParams params = (ConstraintLayout.LayoutParams) mShopCardParentLayout.getLayoutParams();
        params.setMargins(width, 0, 0, 0);
        mShopCardParentLayout.setLayoutParams(params);
    }


    protected long startTime = -1;

    /**
     * 页面曝光事件上报
     */
    private void reportPageShowEvent() {
        long duration = System.currentTimeMillis() - startTime;
        if (startTime < 0 || duration < 300) {
            return;
        }
        Long id = 0L;
        id = mGoodsCardMsg.getId();
        DialogExposureEvent reportEventBean = new DialogExposureEvent(ReportConstants.DIALOG_ID_LIVE_ROOM_MERCHANDISE, pageId, duration, String.valueOf(id));
        reportEventBean.setPage(pageId);
        ReportHelper.getInstance().addEvent(reportEventBean);
        Log.i("BaseDialogFragment", "report=" + duration);
        startTime = -1;
    }
}
