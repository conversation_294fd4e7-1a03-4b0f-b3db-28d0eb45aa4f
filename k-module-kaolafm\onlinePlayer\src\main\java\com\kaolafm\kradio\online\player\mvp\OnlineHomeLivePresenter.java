package com.kaolafm.kradio.online.player.mvp;

import android.content.Context;
import android.util.Log;

import com.alibaba.sdk.android.oss.ClientException;
import com.alibaba.sdk.android.oss.ServiceException;
import com.alibaba.sdk.android.oss.callback.OSSCompletedCallback;
import com.alibaba.sdk.android.oss.callback.OSSProgressCallback;
import com.alibaba.sdk.android.oss.model.PutObjectRequest;
import com.alibaba.sdk.android.oss.model.PutObjectResult;
import com.kaolafm.kradio.live1.model.MessageBean;
import com.kaolafm.kradio.live1.mvp.HomeLivePresenter;
import com.kaolafm.kradio.live1.mvp.HomeLiveView;
import com.kaolafm.kradio.live1.mvp.LivePresenter;
import com.kaolafm.kradio.live1.player.HomeLiveManager;
import com.kaolafm.kradio.live1.player.NimManager;
import com.kaolafm.kradio.live1.player.RecordUploadHelper;
import com.kaolafm.kradio.live1.player.RecorderStatus;
import com.kaolafm.kradio.online.player.http.OnlineLiveApiConstant;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.live.LiveRequest;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.netease.nimlib.sdk.chatroom.model.EnterChatRoomResultData;
import com.netease.nimlib.sdk.msg.model.QueryDirectionEnum;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;

import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.functions.Action;
import io.reactivex.functions.Consumer;
import io.reactivex.schedulers.Schedulers;

public class OnlineHomeLivePresenter extends HomeLivePresenter {
    public OnlineHomeLivePresenter(HomeLiveView view) {
        super(view);
    }

    private NimManager.EnterChatRoomListener mEnterChatRoomListener;

    @Override
    public void enterChatRoom(Context context, String roomId) {
        enterChatRoom(false, context, roomId);
    }
    
    public boolean isChatRoomEntered(){
        return NimManager.getInstance().isChatRoomEntered();
    }

    public void enterChatRoom(boolean ignoreLoginState, Context context, String roomId) {
        boolean entered = NimManager.getInstance().isChatRoomEntered();
        if (DEBUG_LIVE) {
            Log.i(TAG, "enterChatRoom roomId: " + roomId + ", entered: " + entered);
        }
        if (entered) {
            return;
        }
        NimManager.getInstance().addRoomMemberChangedObserver(createRoomMemberChangedObserver());
        NimManager.getInstance().addChatMessageReceivedListener(createChatMessageReceivedListener());
        NimManager.getInstance().enterChatRoom(ignoreLoginState, context, roomId, createEnterChatRoomListener());
    }

    public void logoutIm(){
        NimManager.getInstance().logoutNim();
    }

    private NimManager.EnterChatRoomListener createEnterChatRoomListener() {
        if (mEnterChatRoomListener == null) {
            mEnterChatRoomListener = new NimManager.EnterChatRoomListener() {
                @Override
                public void onException(Throwable throwable) {
                    if (LivePresenter.DEBUG_LIVE) {
                        Log.i(TAG, "enterChatRoom onException: ", throwable);
                    }
                    NimManager.getInstance().setChatRoomEntered(false);
                    if (mView != null && mView instanceof OnlineHomeLiveView) {
                        ((OnlineHomeLiveView) mView).enterChatRoomFailed();
                    }
                }

                @Override
                public void loginFailed(int code) {
                    if (LivePresenter.DEBUG_LIVE) {
                        Log.i(TAG, "enterChatRoom loginFailed code: " + code);
                    }
                    NimManager.getInstance().setChatRoomEntered(false);
                    if (mView != null && mView instanceof OnlineHomeLiveView) {
                        ((OnlineHomeLiveView) mView).enterChatRoomFailed();
                    }
                }

                @Override
                public void loginSuccess(String account) {
                    if (LivePresenter.DEBUG_LIVE) {
                        Log.i(TAG, "enterChatRoom loginSuccess account: " + account);
                    }
                    //此处登录成功，进入聊天室是否成功还需要进一步确认
                }

                @Override
                public void enterChatRoomSuccess(EnterChatRoomResultData data) {
                    if (LivePresenter.DEBUG_LIVE) {
                        Log.i(TAG, "enterChatRoom enterChatRoomSuccess account: " + data);
                    }
                    NimManager.getInstance().setChatRoomEntered(true);
                    if (mView != null && mView instanceof OnlineHomeLiveView) {
                        ((OnlineHomeLiveView) mView).enterChatRoomSuccess();
                    }
                }

                @Override
                public void enterChatRoomFailed(int code) {
                    if (LivePresenter.DEBUG_LIVE) {
                        Log.i(TAG, "enterChatRoom enterChatRoomFailed code: " + code);
                    }
                }

                @Override
                public void getAccountFailed(int code) {
                    if (LivePresenter.DEBUG_LIVE) {
                        Log.i(TAG, "enterChatRoom getAccountFailed code: " + code);
                    }
                    NimManager.getInstance().setChatRoomEntered(false);
                    if (mView != null && mView instanceof OnlineHomeLiveView) {
                        ((OnlineHomeLiveView) mView).enterChatRoomFailed();
                    }
                }
            };
        }
        return mEnterChatRoomListener;
    }

    /**
     * 获取历史消息,可选择给定时间往前或者往后查询，若方向往前，则结果排序按时间逆序，反之则结果排序按时间顺序。
     *
     * @return InvocationFuture 可以设置回调函数。回调中返回历史消息列表
     */
    public void getHistoryMessageList(String roomId, long startTime, int limit, QueryDirectionEnum direction) {
        NimManager.getInstance().getHistoryMessageList(roomId, startTime, limit, direction, new NimManager.HistoryRequestCallback() {
            @Override
            public void onHistoryMessageReceived(List<MessageBean> messages, boolean isOld) {
                if (mView != null && mView instanceof OnlineHomeLiveView) {
                    ((OnlineHomeLiveView) mView).onHistoryMessageReceived(messages, isOld);
                }
            }

            @Override
            public void onHistoryMessageQueryFailed(int code, Throwable exception) {
                if (mView != null && mView instanceof OnlineHomeLiveView) {
                    ((OnlineHomeLiveView) mView).onHistoryMessageQueryFailed(code, exception);
                }
            }
        });
    }

    /**
     * 发送语音消息，同时向网宿和云信发送，以网宿的上传进度作为进度更新回调，但成功与否取决于两者的共同
     * 结果。
     *
     * @param context  上下文，网宿的SDK使用
     * @param program  上传到网宿时的附加信息
     * @param fileName 文件地址
     */
    public void sendAudioMessageToServer(Context context, RecordUploadHelper.UploadParam program,
                                         String fileName) {
        String path = HomeLiveManager.getInstance().getFilePath();
        if (path == null) {
            if (mView != null) {
                mView.showFileNotExist();
            }
            return;
        }
        File file = new File(path);
        if (!file.exists()) {
            if (mView != null) {
                mView.showFileNotExist();
            }
            return;
        }
        HomeLiveManager.getInstance().setRecorderStatus(RecorderStatus.UPLOADING);

        final String room = NimManager.getInstance().getRoomId();

        Observable wangsu = Observable.create(new ObservableOnSubscribe<Boolean>() {
            @Override
            public void subscribe(ObservableEmitter emitter) throws Exception {
                RecordUploadHelper.uploadToAliyunOSS(context, path, program, fileName
                        , new RecordUploadCompletedCallback(emitter)
                        , new RecordUploadProgressCallback(emitter));
            }
        });

        Observable yunxin = Observable.create(new ObservableOnSubscribe<Boolean>() {
            @Override
            public void subscribe(ObservableEmitter<Boolean> emitter) {
                String path = OnlineLiveApiConstant.SEND_MESSAGE_BASE_URL + generateFileUploadedPath(fileName);
                sendAudioMessageToServer(program, path, emitter);
            }
        });

        wangsu
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer() {
                    @Override
                    public void accept(Object o) throws Exception {
                        if (mView != null) {
                            if (o instanceof Integer) {
                                Log.i(TAG, "sendAudioMessage showRecordUploadProgress: " + o);
                                mView.showRecordUploadProgress((int) o);
                            }
                        }
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        if (mView != null) {
                            mView.showRecordUploadFailure();
                        }
                    }
                }, new Action() {
                    @Override
                    public void run() throws Exception {
                        yunxin.subscribeOn(Schedulers.io())
                                .observeOn(AndroidSchedulers.mainThread())
                                .subscribe(new Consumer() {
                                    @Override
                                    public void accept(Object o) throws Exception {
                                        if (o instanceof Boolean && ((Boolean) o)) {

                                        }
                                    }
                                }, new Consumer<Throwable>() {
                                    @Override
                                    public void accept(Throwable throwable) throws Exception {
                                        if (mView != null) {
                                            mView.showRecordUploadFailure();
                                        }
                                    }
                                }, new Action() {
                                    @Override
                                    public void run() throws Exception {
                                        if (mView != null) {
                                            mView.showRecordUploadSuccess();
                                        }
                                    }
                                });
                    }
                });
    }

    private void sendAudioMessageToServer(RecordUploadHelper.UploadParam program, String path, ObservableEmitter<Boolean> emitter) {
        String encode = null;
        try {
            encode = URLEncoder.encode(path, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        new LiveRequest().sendMessageToServer(program.getUid(), program.getAppid(), encode, program.getLiveProgramId(), new HttpCallback<BaseResult<String>>() {
            @Override
            public void onSuccess(BaseResult<String> result) {
                if (result.getCode() == 10000) {
                    emitter.onNext(true);
                    emitter.onComplete();
                    return;
                }
                onError(new ApiException("发送失败"));
            }

            @Override
            public void onError(ApiException e) {
                emitter.tryOnError(new RuntimeException(e.getMessage()));
            }
        });
    }

    private class RecordUploadCompletedCallback implements OSSCompletedCallback<PutObjectRequest, PutObjectResult> {

        private ObservableEmitter<Integer> mEmitter;

        public RecordUploadCompletedCallback(ObservableEmitter<Integer> mEmitter) {
            this.mEmitter = mEmitter;
        }

        @Override
        public void onSuccess(PutObjectRequest request, PutObjectResult result) {
            if (DEBUG_LIVE) {
                Log.i(TAG, "onSuccess responseJson : " + result.toString());
            }
            mEmitter.onComplete();

        }

        @Override
        public void onFailure(PutObjectRequest request, ClientException clientException, ServiceException serviceException) {
            String clientMessage = "", serviceMessage = "";
            if (clientException != null) {
                clientMessage = clientException.toString();
            }
            if (serviceException != null) {
                serviceMessage = serviceException.toString();
            }
            if (DEBUG_LIVE) {
                Log.i(TAG, "onFailure client errorMessage : " + clientMessage + " ; onFailure server errorMessage :" + serviceMessage);
            }
            mEmitter.tryOnError(new RuntimeException(clientMessage));
            mEmitter.tryOnError(new RuntimeException(serviceMessage));
        }
    }

    private class RecordUploadProgressCallback implements OSSProgressCallback<PutObjectRequest> {

        private ObservableEmitter<Integer> mEmitter;

        private RecordUploadProgressCallback(ObservableEmitter<Integer> mEmitter) {
            this.mEmitter = mEmitter;
        }

        @Override
        public void onProgress(PutObjectRequest request, long currentSize, long totalSize) {
            float progress = (totalSize > 0) ? ((float) currentSize / totalSize) * 100 : -1;
            if (DEBUG_LIVE) {
                Log.i(TAG, String.format("onProgress Progress %d from %d (%f)",
                        currentSize, totalSize, progress));
            }
            mEmitter.onNext((int) progress);
        }
    }

    private static String generateFileUploadedPath(String fileName) {
        return "kradio_live_radio/" + fileName;
    }
}
