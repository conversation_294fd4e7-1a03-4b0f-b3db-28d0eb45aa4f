<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="390dp"
    android:layout_height="180dp"
    >

    <RelativeLayout
        android:id="@+id/widget_playinfo_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/widget_play_operation_layout"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/widget_blur_imageview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/widget_default_bg_port"
            />

        <ImageView
            android:id="@+id/widget_blur_bg"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone"
            android:background="@drawable/shape_biyadi_widget_blur_bg"
            />

        <ImageView
            android:id="@+id/widget_kaolafm_log"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/widget_logo_port"
            android:layout_marginTop="@dimen/m14"
            android:layout_marginLeft="@dimen/m17"
            />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:gravity="center_vertical"
            android:layout_below="@+id/widget_kaolafm_log"
            android:layout_above="@+id/widget_time_layout"
            android:layout_marginLeft="42dp"
            android:layout_marginTop="9dp">


            <FrameLayout
                android:id="@+id/widget_audiocover_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true">

                <ImageView
                    android:id="@+id/widget_cover"
                    android:layout_width="58dp"
                    android:layout_height="58dp"
                    android:layout_marginTop="2dp"
                    />

                <ImageView
                    android:id="@+id/widget_broadcast_label"
                    android:layout_width="50dp"
                    android:layout_height="17dp"
                    android:visibility="gone"
                    />

                <TextView
                    android:id="@+id/widget_broadcast_label_textview"
                    android:layout_width="55dp"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:textSize="11sp"
                    android:textColor="@color/colorWhite"
                    android:visibility="gone"
                    />

            </FrameLayout>

            <TextView
                android:id="@+id/widget_audio_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/m24"
                android:layout_marginTop="@dimen/m15"
                android:layout_toRightOf="@+id/widget_audiocover_layout"
                android:ellipsize="end"
                android:lines="1"
                android:maxLines="1"
                android:textColor="@color/colorWhite"
                android:textSize="@dimen/text_size4" />

            <TextView
                android:id="@+id/widget_album_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignBottom="@+id/widget_audiocover_layout"
                android:layout_marginBottom="@dimen/m15"
                android:layout_marginLeft="@dimen/m24"
                android:layout_toRightOf="@+id/widget_audiocover_layout"
                android:ellipsize="end"
                android:lines="1"
                android:maxLines="1"
                android:textColor="@color/colorWhite"
                android:textSize="@dimen/text_size3" />

        </RelativeLayout>


        <LinearLayout
            android:id="@+id/widget_time_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_above="@+id/widget_progressBar"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="@dimen/m9"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/widget_cur_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/colorWhite"
                android:textSize="@dimen/text_size4" />

            <TextView
                android:id="@+id/widget_duration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/colorWhite"
                android:textSize="@dimen/text_size4" />

        </LinearLayout>

        <ProgressBar
            android:id="@+id/widget_progressBar"
            style="@style/Widget.AppCompat.ProgressBar.Horizontal"
            android:layout_width="match_parent"
            android:layout_height="@dimen/m4"
            android:layout_alignParentBottom="true"
            android:progressDrawable="@drawable/widget_seekbar_bar"
            android:scrollbarStyle="insideInset" />

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/widget_play_operation_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:gravity="center"
        android:background="@drawable/shape_biyadi_eco_radius_bg"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/widget_prev"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:src="@drawable/selector_widget_btn_play_prev" />

        <ImageView
            android:id="@+id/widget_play_or_pause"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:src="@drawable/selector_widget_btn_pause" />

        <ImageView
            android:id="@+id/widget_next"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:src="@drawable/selector_widget_btn_play_next" />

        <ImageView
            android:id="@+id/widget_collection"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:src="@drawable/selector_widget_btn_uncollection" />
    </LinearLayout>

</RelativeLayout>