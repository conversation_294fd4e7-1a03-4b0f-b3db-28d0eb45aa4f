package com.kaolafm.kradio.lib.widget;

import android.content.Context;
import android.content.res.TypedArray;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatTextView;
import android.util.AttributeSet;
import android.view.MotionEvent;
import com.kaolafm.kradio.lib.R;
import com.kaolafm.kradio.lib.utils.AnimUtil;

/**
 * <AUTHOR>
 * @date 2019-08-05
 */
public class ScaleTextView extends AppCompatTextView {

    /**
     * 是否有点击播放效果，默认true有。
     */
    private boolean canScale;


    public ScaleTextView(Context context) {
        this(context, null);
    }

    public ScaleTextView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ScaleTextView(Context context, @Nullable AttributeSet attrs,
            int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.ScaleTextView);
        canScale = ta.getBoolean(R.styleable.ScaleTextView_canScale, true);
        ta.recycle();
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (canScale) {
            AnimUtil.onTouchEvent(this, event);
        }
        return super.onTouchEvent(event);
    }
}
