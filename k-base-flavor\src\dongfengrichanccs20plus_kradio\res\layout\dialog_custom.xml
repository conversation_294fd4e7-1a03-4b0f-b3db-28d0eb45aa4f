<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="1000dp"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/dialog_custom_bg">

    <!--   title     -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:minWidth="210dp"
        android:orientation="horizontal"
        android:paddingLeft="40dp"
        android:paddingRight="40dp"
        android:paddingTop="20dp">

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:textSize="24sp"
            android:text="标题"/>
    </LinearLayout>

    <!--   message     -->
    <LinearLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:minWidth="210dp"
        android:paddingLeft="40dp"
        android:paddingRight="40dp"
        android:paddingTop="30dp"
        android:paddingBottom="30dp">

        <TextView
            android:id="@+id/message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:clickable="true"
            android:focusable="true"
            android:textSize="24sp"
            android:text="消息"/>
    </LinearLayout>

    <!--   Button     -->
    <RelativeLayout
        android:id="@+id/rl_btn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minWidth="210dp"
        android:minHeight="40dp"
        android:padding="20dp"
        android:gravity="center">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <Button
                android:id="@+id/positiveButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingHorizontal="20dp"
                android:background="@drawable/dialog_custom_btn"
                android:textAllCaps="false"
                android:textColor="@android:color/white"
                android:textSize="24sp"
                android:text="@string/sure"/>

            <View
                android:id="@+id/divider"
                android:layout_width="15dp"
                android:layout_height="match_parent"/>

            <Button
                android:id="@+id/negativeButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingHorizontal="20dp"
                android:background="@drawable/dialog_custom_btn"
                android:textAllCaps="false"
                android:textColor="@android:color/white"
                android:textSize="24sp"
                android:text="@string/cancel"/>
        </LinearLayout>
    </RelativeLayout>
</LinearLayout>