package com.kaolafm.kradio.flavor.media;

import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.media.session.PlaybackState;
import android.os.Build;
import android.os.Handler;
import android.os.Parcel;
import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bumptech.glide.Glide;
import com.bumptech.glide.request.FutureTarget;
import com.huawei.hmsauto.intelligence.appmanager.IntelligenceApiManager;
import com.huawei.hmsauto.intelligence.appmanager.MediaPlayerCallback;
import com.huawei.hmsauto.intelligence.appmanager.MediaPlayerManager;
import com.huawei.hmsauto.intelligence.exception.DteRemoteException;
import com.huawei.hmsauto.intelligence.utils.EventSourceType;
import com.huawei.hmsauto.intelligence.utils.MediaInfo;
import com.huawei.hmsauto.intelligence.utils.PlayStatusType;
import com.huawei.hmsauto.intelligence.utils.TargetType;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.kradio.clientControlerForKradio.ClientImpl;
import com.kaolafm.kradio.clientControlerForKradio.ClientPlayer;
import com.kaolafm.kradio.clientControlerForKradio.DataConverter;
import com.kaolafm.kradio.common.ResultCallback;
import com.kaolafm.kradio.common.SubscribeData;
import com.kaolafm.kradio.common.report.ReportUtil;
import com.kaolafm.kradio.k_kaolafm.home.HomeDataManager;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.lib.utils.imageloader.listener.OnGetBitmapListener;
import com.kaolafm.kradio.player.bean.BroadcastRadioSimpleData;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.subscribe.SubscribeModel;
import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.search.VoiceSearchRequest;
import com.kaolafm.opensdk.api.search.model.SearchProgramBean;
import com.kaolafm.opensdk.api.search.model.VoiceSearchResult;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.report.event.PlayerUiControlReportEvent;
import com.kaolafm.report.util.ReportConstants;
import com.kaolafm.sdk.client.ErrorInfo;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Package: com.kaolafm.kradio.flavor.media
 * @Description:
 * @Author: Maclay
 * @Date: 16:07
 */
public class HWMediaManager {
    private static final String TAG = "HWMediaManager";
    private String bundleName;
    MediaPlayerManager mediaPlayerManager;
    MediaCallback mediaCallback;
    private Bitmap appBitmap;
    private PlayStatusType mPlayStatusType;

    public HWMediaManager() {
        new Thread(new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "子线程初始化");
                init(AppDelegate.getInstance().getContext());
            }
        }).start();
    }

    private void init(@NonNull Context context) {
        Log.d(TAG, "init");
        bundleName = getPackageName(context);
        Log.d(TAG, "init bundleName " + bundleName);
        try {
            IntelligenceApiManager.init(context);
            mediaPlayerManager = IntelligenceApiManager.getMediaPlayerManager();
            registerMediaCallback();
            PlayerManager.getInstance().addPlayControlStateCallback(playStateListener);
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    public void release() {
        Log.d(TAG, " release ");
        PlayerManager.getInstance().removePlayControlStateCallback(playStateListener);
        if (appBitmap != null) {
            appBitmap.recycle();
            appBitmap = null;
        }
    }


    private String getPackageName(Context context) {
        try {
            PackageManager packageManager = context.getPackageManager();
            PackageInfo packageInfo = packageManager.getPackageInfo(
                    context.getPackageName(), 0);
            return packageInfo.packageName;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "com.edog.car";
    }

    private void registerMediaCallback() {
        Log.d(TAG, "registerMediaCallback");
        try {
            if (mediaPlayerManager != null) {
                mediaPlayerManager.registerMediaPlayerCallback(mediaCallback = new MediaCallback(), bundleName);
            }
        } catch (DteRemoteException e) {
            e.printStackTrace();
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    BasePlayStateListener playStateListener = new BasePlayStateListener() {
        @Override
        public void onPlayerPreparing(final PlayItem playItem) {
            Log.i(TAG, "onPlayerPreparing");
            lastBitmapUrl = null;
            if (playItem == null) {
                Log.i(TAG, "playitem null");
                return;
            }
        }

        @Override
        public void onPlayerPlaying(final PlayItem playItem) {
            Log.i(TAG, "onPlayerPlaying");
            MyMediaSessionManager.getInstance().updateMediaSession(PlaybackState.STATE_PLAYING);
            notifyPlayStatusInfo(PlayStatusType.PLAYING, bundleName);
            notifyMediaInfo(playItem);
        }

        @Override
        public void onPlayerPaused(PlayItem playItem) {
            Log.i(TAG, "onPlayerPaused");
            lastBitmapUrl = null;
            MyMediaSessionManager.getInstance().updateMediaSession(PlaybackState.STATE_PAUSED);
            notifyPlayStatusInfo(PlayStatusType.STOP, bundleName);
        }

        @Override
        public void onPlayerEnd(PlayItem playItem) {
            super.onPlayerEnd(playItem);
            Log.i(TAG, "onPlayerEnd");
            MyMediaSessionManager.getInstance().updateMediaSession(PlaybackState.STATE_STOPPED);
            notifyPlayStatusInfo(PlayStatusType.STOP, bundleName);
        }

        @Override
        public void onProgress(PlayItem playItem, long progress, long l) {
            Log.i(TAG, "onProgress isRefresh:");
            if (!playItem.isLiving()) {
            }
        }
    };

    private void notifyPlayStatusInfo(PlayStatusType playStatusType, String bundleName) {
        if (playStatusType == mPlayStatusType) {
            Log.i(TAG, "now:" + playStatusType + " last:" + mPlayStatusType + "  return");
            return;
        } else {
            Log.i(TAG, "now:" + playStatusType + " last:" + mPlayStatusType + "  continue");
            mPlayStatusType = playStatusType;
        }
        try {
            if (mediaPlayerManager != null) {
                try {
                    Log.d(TAG, "notifyPlayStatusInfo ,  playStatusType = " + playStatusType + " , bundleName " + bundleName);
                    int result = mediaPlayerManager.notifyPlayStatusInfo(playStatusType, bundleName);
                    Log.d(TAG, "notifyPlayStatusInfo ,  result = " + result);
                } catch (DteRemoteException e) {
                    e.printStackTrace();
                }
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    private String lastBitmapUrl = "";

    private void notifyMediaInfo(PlayItem playItem) {
        if (AntiShake.check(playItem)) {
            Log.d(TAG, "AntiShake " + playItem.toString());
            return;
        }
        Log.d(TAG, "notifyPlayStatusInfo " + playItem.toString());
        try {
            if (mediaPlayerManager != null && playItem != null) {
                //先刷新一次信息再说图片
                MediaInfo mediaInfo = toMediaInfoAos(playItem, null);
                int result = mediaPlayerManager.notifyMediaInfo(mediaInfo, bundleName);
                Log.d(TAG, "notifyMediaInfo1 ,  result = " + result);
                final String url = playItem.getPicUrl();
                if (TextUtils.equals(lastBitmapUrl, url)) {
                    Log.d(TAG, "notifyPlayStatusInfo , same bitmapUrl return");
                    return;
                }
                ImageLoader.getInstance().getBlurBitmapFromCache(AppDelegate.getInstance().getContext(), url, 0,
                        new OnGetBitmapListener() {
                            @Override
                            public void onBitmap(Bitmap bitmap) {
                                bitmap = compressImage_bitmap(bitmap, 200, false);
                                try {
                                    Log.d(TAG, "notifyPlayStatusInfo ,  PlayItem = " + playItem.toString());
                                    MediaInfo mediaInfo = toMediaInfoAos(playItem, bitmap);
                                    int result = mediaPlayerManager.notifyMediaInfo(mediaInfo, bundleName);
                                    if (result == 0) {
                                        lastBitmapUrl = url;
                                    }
                                    Log.d(TAG, "notifyMediaInfo ,  result = " + result);
                                } catch (DteRemoteException e) {
                                    e.printStackTrace();
                                }
                            }
                        });
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    /**
     * 压缩图片
     *
     * @param image
     * @param size        最大尺寸k；
     * @param needRecycle
     * @return Bitmap
     */
    private Bitmap compressImage_bitmap(Bitmap image, int size, boolean needRecycle) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        image.compress(Bitmap.CompressFormat.JPEG, 100, baos);// 质量压缩方法，这里100表示不压缩，把压缩后的数据存放到baos中
        int options = 100;
        while (baos.toByteArray().length / 1024 > size) { // 循环判断如果压缩后图片是否大于size
            // kb,大于继续压缩
            baos.reset();// 重置baos即清空baos
            image.compress(Bitmap.CompressFormat.JPEG, options, baos);// 这里压缩options%，把压缩后的数据存放到baos中
            options -= 10;// 每次都减少10
        }
        if (needRecycle) {
            image.recycle();
        }
        ByteArrayInputStream isBm = new ByteArrayInputStream(baos.toByteArray());// 把压缩后的数据baos存放到ByteArrayInputStream中
        Bitmap bitmap = BitmapFactory.decodeStream(isBm, null, null);// 把ByteArrayInputStream数据生成图片
        return bitmap;
    }

    /**
     * String NowPlayingInfoPropertyAPPName; 当前播放音乐播放器的名称
     * PixelMap
     * NowPlayingInfoPropertyAPPIcon;
     * 当前播放音乐播放器的图标，大小需要
     * 小于 200KB
     * String NowPlayingInfoPropertyArtist; 当前播放音乐的作家名称
     * String NowPlayingInfoPropertyItemName; 当前播放音乐名称
     * PixelMap
     * NowPlayingInfoPropertyArtwork;
     * 当前播放音乐的专辑背景图片，大小需
     * 要小于 200KB
     *
     * @param playItem
     * @param bitmap
     * @return
     */
    private MediaInfo toMediaInfoAos(PlayItem playItem, Bitmap bitmap) {
        MediaInfo mediaInfoAos = new MediaInfo(Parcel.obtain());
        mediaInfoAos.setNowPlayingInfoPropertyAPPIcon(getAppBitmap());
        mediaInfoAos.setNowPlayingInfoPropertyAPPName(getAppName());
        mediaInfoAos.setNowPlayingInfoPropertyArtist(playItem.getSourceName());
        String title = playItem.getRadioName();
        if (StringUtil.isEmpty(title)) {
            title = playItem.getAlbumTitle();
        }
        mediaInfoAos.setNowPlayingInfoPropertyItemName(title);
        if (bitmap != null) {
            mediaInfoAos.setNowPlayingInfoPropertyArtwork(bitmap);
        }

        return mediaInfoAos;
    }

    private String getAppName() {
        try {
            PackageManager packageManager = AppDelegate.getInstance().getContext().getPackageManager();
            PackageInfo packageInfo = packageManager.getPackageInfo(
                    AppDelegate.getInstance().getContext().getPackageName(), 0);
            int labelRes = packageInfo.applicationInfo.labelRes;
            return AppDelegate.getInstance().getContext().getResources().getString(labelRes);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "云听";
    }

    private Bitmap getAppBitmap() {
        if (appBitmap != null) {
            return appBitmap;
        }
        PackageManager packageManager = null;
        ApplicationInfo applicationInfo = null;
        try {
            packageManager = AppDelegate.getInstance().getContext().getApplicationContext()
                    .getPackageManager();
            applicationInfo = packageManager.getApplicationInfo(bundleName, 0);
        } catch (PackageManager.NameNotFoundException e) {
            applicationInfo = null;
        }
        Drawable d = packageManager.getApplicationIcon(applicationInfo);
        BitmapDrawable bd = (BitmapDrawable) d;
        appBitmap = bd.getBitmap();
        appBitmap = compressImage_bitmap(appBitmap, 200, false);
        return appBitmap;

    }

    class MediaCallback implements MediaPlayerCallback {

        /**
         * targetType 目标媒体类型，枚举类，
         * 当前有音乐、视频、电台，当前注册
         * 媒体应用根据目标媒体类型来判断此
         * 回调是否需要执行。详见表 5
         * eventSourceType 产生回调信息的来源
         * 处，枚举类，根据不同的消息来源可
         * 有不同交互逻辑，例如当消息来自语
         * 音，可以回调 TTS 接口进行语音回
         * 复，详见表 6
         * 返回参数：
         *  0：执行成功
         *  1：执行失败
         *
         * @param targetType
         * @param eventSourceType
         * @return
         */
        @Override
        public int onPlay(TargetType targetType, EventSourceType eventSourceType) {
            Log.d(TAG, "MediaCallback onPlay , targetType " + targetType + " , eventSourceType " + eventSourceType);
            if (!PlayerManagerHelper.getInstance().isPlaying()) {
                PlayerManagerHelper.getInstance().play(true);
            }
            return 0;
        }

        @Override
        public int onPause(TargetType targetType, EventSourceType eventSourceType) {
            Log.d(TAG, "MediaCallback onPause , targetType " + targetType + " , eventSourceType " + eventSourceType);
//            if (PlayerManagerHelper.getInstance().isPlaying()) {
            PlayerManagerHelper.getInstance().pause(true);
//            }
            return 0;
        }

        @Override
        public int onPrevious(TargetType targetType, EventSourceType eventSourceType) {
            Log.d(TAG, "MediaCallback onPrevious , targetType " + targetType + " , eventSourceType " + eventSourceType);
            PlayerManagerHelper.getInstance().playPre(true);
            return 0;
        }

        @Override
        public int onNext(TargetType targetType, EventSourceType eventSourceType) {
            Log.d(TAG, "MediaCallback onNext , targetType " + targetType + " , eventSourceType " + eventSourceType);
            PlayerManagerHelper.getInstance().playNext(true);
            return 0;
        }

        @Override
        public PlayStatusType onGetPlayingStatusInfo(EventSourceType eventSourceType) {
            Log.d(TAG, "MediaCallback onGetPlayingStatusInfo ,  " + "eventSourceType " + eventSourceType);
            //涉及到音乐卡片，返回播放状态
            if (PlayerManagerHelper.getInstance().isPlaying()) {
                return PlayStatusType.PLAYING;
            } else {
                return PlayStatusType.STOP;
            }
        }

        @Override
        public MediaInfo onGetMediaInfo(EventSourceType eventSourceType) {
            PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
            if (playItem != null) {
                String imgUrl = playItem.getPicUrl();
                Bitmap bitmap = ImageLoader.getInstance().getBitmapFromCache(AppDelegate.getInstance().getContext(), imgUrl);
                return toMediaInfoAos(playItem, compressImage_bitmap(bitmap, 200, false));
            }
            return null;
        }


        @Override
        public int onChange(EventSourceType eventSourceType) {
            Log.d(TAG, "MediaCallback onChange ,  " + "eventSourceType " + eventSourceType);
            PlayerManager.getInstance().playNext();
            return 0;
        }

        @Override
        public int onFavorite(boolean b, EventSourceType eventSourceType) {
            Log.d(TAG, "MediaCallback onFavorite ,  " + "eventSourceType " + eventSourceType + " , " + b);
            if (!UserInfoManager.getInstance().isUserLogin()) {//未登录返回12
                return 12;
            }
            SubscribeData sd = new SubscribeData();
            sd.setType(PlayerManager.getInstance().getCurPlayItem().getType());
            sd.setId(PlayerManagerHelper.getInstance().getSubscribeId());
            if (b) {
                new SubscribeModel().subscribe(sd, null);
            } else {
                new SubscribeModel().unsubscribe(sd, null);
            }
            return 0;
        }

        @Override
        public int onPlayWithContent(Map<String, String> map) {
            Log.d(TAG, "MediaCallback onPlayWithContent ,  " + "map " + map.toString());
            String keyword = getRemoteKeyword(map);

            if (TextUtils.isEmpty(keyword)) {
                return 1;
            }
            new VoiceSearchRequest().searchBySemantics("kaola", 0, null,
                    2, 1, null, null, null, null,
                    keyword, keyword, new HttpCallback<VoiceSearchResult>() {
                        @Override
                        public void onSuccess(VoiceSearchResult ssd) {
                            Log.i(TAG, "playByKeywords----->onSuccess = " + ssd);
                            try {
                                List<SearchProgramBean> dataList = ssd.getProgramList();
                                ArrayList<ClientPlayer.PlayParam> result = new ArrayList<>();
                                if (dataList != null && !dataList.isEmpty()) {
                                    for (int i = 0; i < dataList.size(); i++) {
                                        SearchProgramBean dlb = dataList.get(i);
                                        ClientPlayer.PlayParam music = DataConverter.toPlayParam(dlb);
                                        if (music == null) {
                                            continue;
                                        }
                                        result.add(music);
                                    }
                                }
                                if (!ListUtil.isEmpty(result)) {
                                    ClientPlayer.PlayParam playParam = result.get(0);
                                    if (ResType.TYPE_BROADCAST == playParam.resType) {
                                        BroadcastRadioSimpleData data = new BroadcastRadioSimpleData();
                                        data.setBroadcastId(playParam.id);
                                        data.setImg(playParam.img);
                                        if (!TextUtils.isEmpty(playParam.fm)) {
                                            data.setName(playParam.title + "  " + playParam.fm);
                                        } else {
                                            data.setName(playParam.title + "  " + " ");
                                        }
                                        PlayerManagerHelper.getInstance().addBroadcastRadioSimpleItem(data);
                                    }
                                    ReportUtil.addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_PLAY, PlayerUiControlReportEvent.CONTROL_TYPE_VOICE, null);
                                    PlayerManagerHelper.getInstance().start(String.valueOf(playParam.id), playParam.resType);
                                } else {
                                    goToPlayDesktopFirst();
                                }

                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }

                        @Override
                        public void onError(ApiException e) {
                            Log.i(TAG, "playByKeywords----->onError = " + e);
                            goToPlayDesktopFirst();
                        }
                    });
            return 0;
        }

        private void goToPlayDesktopFirst() {
            HomeDataManager.getInstance().playFirstItemWithCallBack(new HomeDataManager.PlayFirstItemCallback() {
                @Override
                public void success() {
                    new Handler().postDelayed(postPlay, 2000);
                }

                @Override
                public void failed() {
                }
            });
        }

        private Runnable postPlay = new Runnable() {
            @Override
            public void run() {
                PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
                if (playItem != null) {
                    String content = "未搜索到对应资源，已为您推荐" + (playItem.getAlbumTitle() == null ?
                            playItem.getRadioName() == null ? playItem.getSourceName() : playItem.getRadioName()
                            : playItem.getAlbumTitle());
                    Log.i(TAG, content);
                    Toast.makeText(AppDelegate.getInstance().getContext(), content, Toast.LENGTH_SHORT).show();
                } else {
                    new Handler().postDelayed(this, 1000);
                }
            }
        };

        //////////////////////////////
        @Override
        public int onPlayMode(int i, EventSourceType eventSourceType) {
            Log.d(TAG, "MediaCallback onPlayMode , targetType " + i + " , eventSourceType " + eventSourceType);
            return 1;
        }

        @Override
        public int onSeek(int i, int i1, EventSourceType eventSourceType) {
            Log.d(TAG, "MediaCallback onSeek , targetType " + i + " i1 " + i1 + " , eventSourceType " + eventSourceType);
            return 1;
        }

        @Override
        public int onPlayPause(EventSourceType eventSourceType) {
            Log.d(TAG, "MediaCallback onPlayPause ,  " + "eventSourceType " + eventSourceType);
            return 1;
        }

        @Override
        public int onFullScreen(boolean b, EventSourceType eventSourceType) {
            Log.d(TAG, "MediaCallback onFullScreen ,  " + "eventSourceType " + eventSourceType + " , " + b);
            return 1;
        }

        @Override
        public int onDownload(EventSourceType eventSourceType) {
            Log.d(TAG, "MediaCallback onDownload ,  " + "eventSourceType " + eventSourceType);
            return 1;
        }

        @Override
        public int onFollow(boolean b, EventSourceType eventSourceType) {
            Log.d(TAG, "MediaCallback onFollow ,  " + "eventSourceType " + eventSourceType + " , " + b);
            return 1;
        }

        /**
         * @param action action 操作类型，
         * @param para   para 操作参数
         *               返回值：
         *                0：执行成功
         *                1：执行失败
         */
        @Override
        public int onMediaCustomAction(String action, String para) {
            Log.d(TAG, "onMediaCustomAction  action=" + action + "-para-" + para);
            switch (action) {
                case "hivoice.media.general.fastForwardProgress"://部分可实现，快进30s秒可以实现，进度不建议做。（在线广播不支持跳转）
                    PlayerManagerHelper.getInstance().fastForward();
                    break;
                case "hivoice.media.general.fastBackwardProgress"://部分可实现，快退30s秒可以实现，进度不建议做。（在线广播不支持跳转）
                    PlayerManagerHelper.getInstance().fastBackward();
                    break;
                case "hivoice.media.jumpToProgress"://部分可实现，跳转XX秒可以实现，跳转百分比不建议做，在线广播不支持跳转
                    //time 时间
                    //unit
                    // 1：跳转时长
                    // 2：跳转百分比
                    JSONObject jsonParams = JSON.parseObject(para);
                    String time = jsonParams.getString("time");
                    int unit = jsonParams.getInteger("unit");
                    if (unit == 1 && !TextUtils.isEmpty(time)) {
                        try {
                            int pos = Integer.parseInt(time);
                            PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
                            if (pos < playItem.getPosition()) {
                                PlayerManagerHelper.getInstance().seek(pos);
                            } else {
                                Log.d(TAG, "onMediaCustomAction jumpToProgress return=1");

                                return 1;
                            }

                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    break;
                default: {
                    Log.d(TAG, "onMediaCustomAction default return=1");

                    return 1;
                }
            }
            Log.d(TAG, "onMediaCustomAction  return=0");
            return 0;
        }
    }

    //过滤动作名词做关键词提取
    private String getRemoteKeyword(Map<String, String> map) {
        //专辑
//            String category =;
//            String tags =;
//            String programmeName =;
//            String albumName =;
//            String sequence =;
//            String artist =;
//            String work =;
//电台
//            String name =;
//            String frequency =;
//            String city =;
//            String province =;
//            String type =;
//            String programme =;
        if (map != null) {
            for (Map.Entry<String, String> entry : map.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                if (!TextUtils.isEmpty(value)) {
                    Log.d(TAG, "getRemoteKeyword ,key " + key + " , value " + value);
                    if (value.startsWith("播放")) {
                        value = value.replaceFirst("播放", "");
                    } else if (value.startsWith("播放专辑")) {
                        value = value.replaceFirst("播放专辑", "");
                    } else if (value.endsWith("我想听")) {
                        value = value.replaceFirst("我想听", "");
                    } else if (value.endsWith("来首")) {
                        value = value.replaceFirst("来首", "");
                    } else if (value.endsWith("来点")) {
                        value = value.replaceFirst("来点", "");
                    } else if (value.endsWith("放首")) {
                        value = value.replaceFirst("放首", "");
                    } else if (value.endsWith("我要听")) {
                        value = value.replaceFirst("我要听", "");
                    } else if (value.endsWith("搜索")) {
                        value = value.replaceFirst("搜索", "");
                    } else if (value.endsWith("收听")) {
                        value = value.replaceFirst("收听", "");
                    }


                    if (!TextUtils.isEmpty(value)) {
                        return value;
                    }
                }
            }
        }
        return "";
    }
}
