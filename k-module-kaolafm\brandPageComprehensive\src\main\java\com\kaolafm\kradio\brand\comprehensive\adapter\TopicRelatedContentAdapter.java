package com.kaolafm.kradio.brand.comprehensive.adapter;

import android.graphics.Bitmap;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import com.kaolafm.kradio.common.widget.banner.KradioBannerView.KradioBannerAdapter;
import com.kaolafm.kradio.k_kaolafm.R.drawable;
import com.kaolafm.kradio.k_kaolafm.R.id;
import com.kaolafm.kradio.k_kaolafm.R.layout;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.lib.utils.imageloader.listener.OnGetBitmapListener;
import com.kaolafm.opensdk.api.topic.model.TopicRelatedContent;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public final class TopicRelatedContentAdapter extends KradioBannerAdapter {
   @NotNull
   @Override
   public View onCreateView(@NotNull ViewGroup container) {
      View itemView = LayoutInflater.from(container.getContext()).inflate(layout.comprehensive_rv_item_related_content, container, false);
      return itemView;
   }

   public void onBindView(@NotNull View itemView, @Nullable TopicRelatedContent data, int position) {
      final ImageView coverPic = (ImageView)itemView.findViewById(id.coverPic);
      TextView title = (TextView)itemView.findViewById(id.title);
      TextView tagView = (TextView)itemView.findViewById(id.tagView);
      if (data != null) {
         coverPic.setImageResource(drawable.media_default_pic);
         byte var10001;
         if (StringUtil.isEmpty(data.getTag())) {
            var10001 = 8;
         } else {
            tagView.setText((CharSequence)data.getTag());
            var10001 = 0;
         }

         tagView.setVisibility(var10001);
         title.setText((CharSequence)data.getTitle());
         ImageLoader var10000 = ImageLoader.getInstance();
         var10000.getBitmapFromCache(coverPic.getContext(), data.getImg(), (OnGetBitmapListener)(new OnGetBitmapListener() {
            @Override
            public final void onBitmap(Bitmap it) {
               if (it != null && !it.isRecycled()) {
                  coverPic.setImageBitmap(it);
               }

            }
         }));
      }
   }

   // $FF: synthetic method
   // $FF: bridge method
   @Override
   public void onBindView(View var1, Object var2, int var3) {
      this.onBindView(var1, (TopicRelatedContent)var2, var3);
   }
}
