package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.media.AudioAttributes;
import android.media.AudioFocusRequest;
import android.media.AudioManager;
import android.os.Build;
import android.util.Log;

import com.ecarx.sdk.openapi.ECarXApiClient;
import com.ecarx.sdk.policy.IAudioAttributes;
import com.ecarx.sdk.policy.IAudioPolicy;
import com.ecarx.sdk.policy.PolicyAPI;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioFocusInter;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: Jacklee
 * @time: 2021-03-15 11:23
 ******************************************/
public final class KRadioAudioFocusImpl implements KRadioAudioFocusInter {
    private static final String TAG = "KRadioAudioFocusImpl";
    public static final int ECARX_AUDIO_STREAM_TYPE = 15;
    //    private IAudioPolicy mIAudioPolicy;
    private IAudioAttributes mAudioAttributes;

    public KRadioAudioFocusImpl() {
        //此功能接口适用于大于android.os.Build.VERSION_CODES.O的安卓系统版本，android.os.Build.VERSION_CODES.O以下的版本可以使用原生AOSP接口
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            try {
                final PolicyAPI policyAPI = PolicyAPI.createPolicyAPI(AppDelegate.getInstance().getContext());
                policyAPI.init(AppDelegate.getInstance().getContext(), new ECarXApiClient.Callback() {
                    @Override
                    public void onAPIReady(boolean b) {
                        if (b) {
                            Log.i(TAG, "PolicyAPI  初始化成功");
                            if (policyAPI != null) {
                                //获取音频属性策略实例
                                mAudioAttributes = policyAPI.getAudioAttributes();
                            }
                        } else {
                            Log.i(TAG, "PolicyAPI  初始化失败");
                        }
                    }
                });
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public boolean requestAudioFocus(Object... args) {
        AudioManager am = (AudioManager) AppDelegate.getInstance().getContext().getSystemService(Context.AUDIO_SERVICE);
        if (am==null)return false;
        boolean status;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O ) {
            //此功能接口适用于大于android.os.Build.VERSION_CODES.O的安卓系统版本，android.os.Build.VERSION_CODES.O以下的版本可以使用原生AOSP接口
            int mUsage = mAudioAttributes.getAudioAttributesUsage(IAudioAttributes.USAGE_MD_MEDIA);
            int mContent = mAudioAttributes.getAudioAttributesContentType(IAudioAttributes.CONTENT_TYPE_MD_MUSIC);
            AudioAttributes mAudioAttributes = new AudioAttributes.Builder()
                    //通过PolicyAPI的getAudioAttributesUsage接口获取到的mUsage
                    .setUsage(mUsage)
                    //通过PolicyAPI的getAudioAttributesContentType接口获取到的mContent
                    .setContentType(mContent)
                    .build();
            AudioFocusRequest mAudioFocusRequest = new AudioFocusRequest.Builder(ECARX_AUDIO_STREAM_TYPE)
                    .setAudioAttributes(mAudioAttributes)
                    .build();
            status = AudioManager.AUDIOFOCUS_REQUEST_GRANTED == am.requestAudioFocus(mAudioFocusRequest);
        } else {
            status = AudioManager.AUDIOFOCUS_REQUEST_GRANTED ==
                    am.requestAudioFocus((AudioManager.OnAudioFocusChangeListener) args[0], ECARX_AUDIO_STREAM_TYPE,
                            AudioManager.AUDIOFOCUS_GAIN);
            Log.i(TAG, "live requestAudioFocus status:" + status);
        }
        return status;
    }

    @Override
    public boolean abandonAudioFocus(Object... args) {
        AudioManager am = (AudioManager) AppDelegate.getInstance().getContext().getSystemService(Context.AUDIO_SERVICE);
        if (am==null)return false;
        boolean status = AudioManager.AUDIOFOCUS_REQUEST_GRANTED ==
                am.abandonAudioFocus((AudioManager.OnAudioFocusChangeListener) args[0]);
        Log.i(TAG, "live abandonAudioFocus status:" + status);
        return status;
    }
}
