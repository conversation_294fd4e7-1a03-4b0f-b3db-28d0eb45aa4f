<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center">

        <TextView
            android:id="@+id/toast_msg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_sys_toast_layout_bg"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="2"
            android:minHeight="@dimen/y120"
            android:paddingLeft="@dimen/x60"
            android:paddingTop="@dimen/y39"
            android:paddingRight="@dimen/x60"
            android:paddingBottom="@dimen/y39"
            android:textColor="@color/text_color_toast"
            android:textSize="@dimen/text_size5"
            tools:text="我是Toast" />
    </LinearLayout>
</FrameLayout>
