package com.kaolafm.kradio.lib.bean;


import android.util.Log;

import com.kaolafm.kradio.lib.utils.Constants;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Generated;
import org.greenrobot.greendao.annotation.Unique;

@Entity
public class HistoryItem implements Itemable {
    /**
     * 电台类型
     */
    private String type;
    /**
     * 电台id或专辑id
     */
    @Unique
    private String radioId;
    /**
     * PGC所属类型
     */
    private int typeId = Constants.INVALID_NUM;
    /**
     * 电台名称
     */
    private String radioTitle;
    /**
     * 电台图片
     */
    private String picUrl;
    /**
     * 上次收听的碎片id
     */
    private String audioId;
    /**
     * 碎片名称
     */
    private String audioTitle;
    /**
     * 播放地址
     */
    private String playUrl;
    /**
     * 上次播放位置
     */
    private long playedTime;
    /**
     * 时长
     */
    private long duration;
    /**
     * 是否为离线
     */
    private boolean isOffline;
    /**
     * 产生历史记录的时间戳
     */
    private long timeStamp;
    private long orderNum;
    /**
     * 离线地址
     */
    private String offlinePlayUrl;
    private String shareUrl;
    /**
     * 专辑分类ID
     */
    private int categoryId;
    /**
     * 额外追加参数
     */
    private String paramOne;
    private String paramTwo;

    /**
     * 第三方资源，来源url
     */
    private String sourceUrl;
    /**
     * 专辑排序（升序或降序 具体参考PlayerRadioListItem.ORDER_MODE_POSITIVE，PlayerRadioListItem.ORDER_MODE_REVERSE）
     */
    private int orderMode = Constants.ORDER_MODE_POSITIVE;

    /**
     * 是否正在播放
     */
    private boolean isPlaying;

    /**
     * 是否需要付费
     */
    private int fine;

    /**
     * 专辑是否vip
     */
    private int vip;

    /**
     * 广播频率
     */
    private String freq;

    /**
     * 广播内容类型（音乐，交通，新闻等）
     */
    private int broadcastSort;

    /**
     * 播放量
     */
    private long listenCount;
    /**
     * 更新时间
     */
    private long radioUpdateTime;
    /**
     * 广播|听电视当前节目名
     */
    private String currentProgramName;


    @Generated(hash = 1287935827)
    public HistoryItem(String type, String radioId, int typeId, String radioTitle, String picUrl,
            String audioId, String audioTitle, String playUrl, long playedTime, long duration,
            boolean isOffline, long timeStamp, long orderNum, String offlinePlayUrl, String shareUrl,
            int categoryId, String paramOne, String paramTwo, String sourceUrl, int orderMode,
            boolean isPlaying, int fine, int vip, String freq, int broadcastSort, long listenCount,
            long radioUpdateTime, String currentProgramName) {
        this.type = type;
        this.radioId = radioId;
        this.typeId = typeId;
        this.radioTitle = radioTitle;
        this.picUrl = picUrl;
        this.audioId = audioId;
        this.audioTitle = audioTitle;
        this.playUrl = playUrl;
        this.playedTime = playedTime;
        this.duration = duration;
        this.isOffline = isOffline;
        this.timeStamp = timeStamp;
        this.orderNum = orderNum;
        this.offlinePlayUrl = offlinePlayUrl;
        this.shareUrl = shareUrl;
        this.categoryId = categoryId;
        this.paramOne = paramOne;
        this.paramTwo = paramTwo;
        this.sourceUrl = sourceUrl;
        this.orderMode = orderMode;
        this.isPlaying = isPlaying;
        this.fine = fine;
        this.vip = vip;
        this.freq = freq;
        this.broadcastSort = broadcastSort;
        this.listenCount = listenCount;
        this.radioUpdateTime = radioUpdateTime;
        this.currentProgramName = currentProgramName;
    }

    @Generated(hash = 1930117983)
    public HistoryItem() {
    }

    @Override
    public int getOrderMode() {
        return orderMode;
    }

    @Override
    public void setOrderMode(int orderMode) {
        this.orderMode = orderMode;
    }

    @Override
    public String getParamTwo() {
        return paramTwo;
    }

    @Override
    public void setParamTwo(String paramTwo) {
        this.paramTwo = paramTwo;
    }

    @Override
    public String getParamOne() {
        return paramOne;
    }

    @Override
    public void setParamOne(String paramOne) {
        this.paramOne = paramOne;
    }

    @Override
    public long getRadioUpdateTime() {
        return radioUpdateTime;
    }

    @Override
    public void setRadioUpdateTime(long radioUpdateTime) {
        this.radioUpdateTime = radioUpdateTime;
    }

    @Override
    public String getCurrentProgramName() {
        return currentProgramName;
    }

    @Override
    public void setCurrentProgramName(String currentProgramName) {
        this.currentProgramName = currentProgramName;
    }

    @Override
    public String getType() {
        return type;
    }

    @Override
    public void setType(String type) {
        this.type = type;
    }

    @Override
    public String getRadioId() {
        return radioId;
    }

    @Override
    public void setRadioId(String radioId) {
        this.radioId = radioId;
    }

    @Override
    public int getTypeId() {
        return typeId;
    }

    @Override
    public void setTypeId(int typeId) {
        this.typeId = typeId;
    }

    @Override
    public String getRadioTitle() {
        return radioTitle;
    }

    @Override
    public void setRadioTitle(String radioTitle) {
        this.radioTitle = radioTitle;
    }

    @Override
    public String getPicUrl() {
        return picUrl;
    }

    @Override
    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    @Override
    public String getAudioId() {
        return audioId;
    }

    @Override
    public void setAudioId(String audioId) {
        this.audioId = audioId;
    }

    @Override
    public String getAudioTitle() {
        return audioTitle;
    }

    @Override
    public void setAudioTitle(String audioTitle) {
        this.audioTitle = audioTitle;
    }

    @Override
    public String getPlayUrl() {
        return playUrl;
    }

    @Override
    public void setPlayUrl(String playUrl) {
        this.playUrl = playUrl;
    }

    @Override
    public long getPlayedTime() {
        return playedTime;
    }

    @Override
    public void setPlayedTime(long playedTime) {
        this.playedTime = playedTime;
    }

    @Override
    public long getDuration() {
        return duration;
    }

    @Override
    public void setDuration(long duration) {
        this.duration = duration;
    }

    @Override
    public long getTimeStamp() {
        return timeStamp;
    }

    @Override
    public void setTimeStamp(long timeStamp) {
        Log.i("HistoryItem", "timeStamp = " + timeStamp);
        this.timeStamp = timeStamp;
    }

    @Override
    public long getOrderNum() {
        return orderNum;
    }

    @Override
    public void setOrderNum(long orderNum) {
        this.orderNum = orderNum;
    }

    @Override
    public String getOfflinePlayUrl() {
        return offlinePlayUrl;
    }

    @Override
    public void setOfflinePlayUrl(String offlinePlayUrl) {
        this.offlinePlayUrl = offlinePlayUrl;
    }

    @Override
    public String getShareUrl() {
        return shareUrl;
    }

    @Override
    public void setShareUrl(String shareUrl) {
        this.shareUrl = shareUrl;
    }

    @Override
    public int getCategoryId() {
        return categoryId;
    }

    @Override
    public void setCategoryId(int categoryId) {
        this.categoryId = categoryId;
    }

    @Override
    public String getSourceUrl() {
        return sourceUrl;
    }

    @Override
    public void setSourceUrl(String sourceUrl) {
        this.sourceUrl = sourceUrl;
    }

    @Override
    public boolean isOffline() {
        return this.isOffline;
    }

    @Override
    public void setOffline(boolean isOffline) {
        this.isOffline = isOffline;
    }

    @Override
    public boolean isPlaying() {
        return this.isPlaying;
    }

    @Override
    public void setPlaying(boolean isPlaying) {
        this.isPlaying = isPlaying;
    }

    public int getFine() {
        return fine;
    }

    public void setFine(int fine) {
        this.fine = fine;
    }

    public int getVip() {
        return vip;
    }

    public void setVip(int vip) {
        this.vip = vip;
    }

    public boolean isFine() {
        //1为精品
        return 1 == fine;
    }

    public boolean isVIP() {
        //1为VIP
        return 1 == fine;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof HistoryItem)) return false;

        HistoryItem that = (HistoryItem) o;

        return radioId.equals(that.radioId);

    }

    @Override
    public int hashCode() {
        return radioId.hashCode();
    }

    public boolean getIsOffline() {
        return this.isOffline;
    }

    public void setIsOffline(boolean isOffline) {
        this.isOffline = isOffline;
    }

    public boolean getIsPlaying() {
        return this.isPlaying;
    }

    public void setIsPlaying(boolean isPlaying) {
        this.isPlaying = isPlaying;
    }

    @Override
    public String getFreq() {
        return freq;
    }

    @Override
    public void setFreq(String freq) {
        this.freq = freq;
    }

    public long getListenCount() {
        return listenCount;
    }

    public void setListenCount(long listenCount) {
        this.listenCount = listenCount;
    }

    @Override
    public int getBroadcastSort() {
        return broadcastSort;
    }

    @Override
    public void setBroadcastSort(int broadcastSort) {
        this.broadcastSort = broadcastSort;
    }

    @Override
    public String toString() {
        return "HistoryItem{" +
                "type='" + type + '\'' +
                ", radioId='" + radioId + '\'' +
                ", typeId=" + typeId +
                ", radioTitle='" + radioTitle + '\'' +
                ", picUrl='" + picUrl + '\'' +
                ", audioId='" + audioId + '\'' +
                ", audioTitle='" + audioTitle + '\'' +
                ", playUrl='" + playUrl + '\'' +
                ", playedTime=" + playedTime +
                ", duration=" + duration +
                ", isOffline=" + isOffline +
                ", timeStamp=" + timeStamp +
                ", orderNum=" + orderNum +
                ", offlinePlayUrl='" + offlinePlayUrl + '\'' +
                ", shareUrl='" + shareUrl + '\'' +
                ", categoryId=" + categoryId +
                ", paramOne='" + paramOne + '\'' +
                ", paramTwo='" + paramTwo + '\'' +
                ", sourceUrl='" + sourceUrl + '\'' +
                ", orderMode=" + orderMode +
                ", isPlaying=" + isPlaying +
                ", fine=" + fine +
                ", vip=" + vip +
                ", freq='" + freq + '\'' +
                ", broadcastSort='" + broadcastSort + '\'' +
                ", listenCount=" + listenCount +
                '}';
    }
}
