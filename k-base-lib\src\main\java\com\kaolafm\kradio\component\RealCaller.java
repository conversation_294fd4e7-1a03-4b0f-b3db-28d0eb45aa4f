package com.kaolafm.kradio.component;

import android.os.Looper;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.Log;
import com.kaolafm.kradio.component.ComponentResult.Code;
import com.kaolafm.kradio.lib.utils.AppUtil;
import com.kaolafm.opensdk.log.Logging;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import okhttp3.internal.NamedRunnable;

/**
 * <AUTHOR>
 * @date 2019-07-02
 */
public class RealCaller implements Caller {

    /**
     * 默认超时时间为2秒
     */
    private static final long DEFAULT_TIMEOUT = 2000;

    /**
     * 是否异步
     */
    private boolean mAsync = false;

    private final ComponentClient mClient;

    private ComponentResult mResult;

    /**
     * 同步锁守护变量。
     */
    private boolean executed = false;

    /**
     * 是否已经完成
     */
    private AtomicBoolean finished = new AtomicBoolean(false);

    private long timeoutAt;

    /**
     * 是否超时。true表示超时，false未超时。
     */
    private boolean timeoutStatus = false;

    private final byte[] wait4resultLock = new byte[0];

    private boolean waiting = false;

    private String mCallId;

    /**
     * 是否已经取消
     */
    private boolean mCanceled = false;


    RealCaller(ComponentClient client) {
        this.mClient = client;
    }

    @Override
    public ComponentResult call() {
        try {
            mAsync = false;
            setTimeoutAt();
            mCanceled = false;
            mCallId = nextCallId();
            mClient.dispatcher().executed(this);
            return getResultWithInterceptorChain();
        } catch (Exception e) {
            return ComponentResult.defaultExceptionResult(e);
        } finally {
            mClient.dispatcher().finished(this);
        }
    }

    @Override
    public void call(ComponentCallback callback) {
        synchronized (this) {
            if (executed) {
                return;
            }
            executed = true;
        }
        mAsync = true;
        setTimeoutAt();
        mCanceled = false;
        mCallId = nextCallId();
        mClient.dispatcher().executed(new AsyncRunnable(callback));
    }

    private static String prefix;

    private static AtomicInteger index = new AtomicInteger(1);

    void cancelOnDestroy() {
        if (!isFinished()) {
            Logging.d("取消组件请求");
            cancel();
        }else {
            Logging.d("取消组件请求，但请求已经完成");
        }
    }

    void timeout() {
        if (markFinished()) {
            timeoutStatus = true;
            setResultForWaiting(ComponentResult.error(Code.ERROR_TIMEOUT));
        }
    }

    private String nextCallId() {
        //如果已经设置了callId，就不在自动生成。
        if (!TextUtils.isEmpty(mCallId)) {
            return mCallId;
        }
        if (TextUtils.isEmpty(prefix)) {
            String currentProcessName = AppUtil.getCurrentProcessName(mClient.application());
            if (!TextUtils.isEmpty(currentProcessName)) {
                prefix = currentProcessName + ":";
            } else {
                return ":::" + index.getAndIncrement();
            }
        }
        return prefix + index.getAndIncrement();
    }

    @Override
    public void cancel() {
        if (markFinished()) {
            mCanceled = true;
            setResultForWaiting(ComponentResult.error(Code.ERROR_CANCELED));
            Log.i("RealCaller", "已经取消，CallId=" + mCallId);
        } else {
            Log.i("RealCaller", "取消，但是组件调用已经完成");
        }
    }

    @Override
    public ComponentClient client() {
        return mClient;
    }

    @Override
    public ComponentResult result() {
        return mResult;
    }

    @Override
    public boolean isCanceled() {
        return mCanceled;
    }

    @Override
    public boolean isStopped() {
        return mCanceled || timeoutStatus;
    }

    @Override
    public boolean isFinished() {
        return finished.get();
    }

    public boolean isTimeout() {
        return timeoutStatus;
    }

    /**
     * 设置超时截止时间。
     */
    private void setTimeoutAt() {
        long timeout = mClient.timeout();
        //主线程下的同步调用必须设置超时时间，默认为2秒
        if (!mAsync) {
            boolean mainThreadCallWithNoTimeout = mClient.timeout() == 0 && Looper.getMainLooper() == Looper
                    .myLooper();
            if (mainThreadCallWithNoTimeout || mClient.timeout() < 0) {
                timeout = DEFAULT_TIMEOUT;
            }
        } else {
            timeout = timeout < 0 ? 0 : timeout;
        }
        if (timeout > 0) {
            timeoutAt = SystemClock.elapsedRealtime() + timeout;
        } else {
            timeoutAt = 0;
        }
        timeoutStatus = false;
    }

    long timeoutAt() {
        return timeoutAt;
    }

    /**
     * 获取操作名称。被调用的组件根据不同的名称做对应的逻辑。
     * @return
     */
    public String actionName() {
        return mClient.actionName();
    }

    @Override
    public String getCallId() {
        return mCallId;
    }

    public void setCallId(String callId) {
        if (!TextUtils.isEmpty(callId)) {
            mCallId = callId;
        }
    }

    /**
     * 获取参数集合
     * @return
     */
    public Map<String, Object> getParams() {
        return mClient.getParams();
    }

    /**
     * 根据key获取对应的参数
     * @param key
     * @param <T>
     * @return
     */
    public <T> T getParamValue(String key) {
        try {
            return (T) mClient.getParams().get(key);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 阻塞等待结果。
     */
    void waitForResult() {
        try {
            synchronized (wait4resultLock) {
                if (!isFinished()) {
                    waiting = true;
                    wait4resultLock.wait();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 发送结果。
     */
    static void sendResult(String callId, ComponentResult result) {
        RealCaller caller = ComponentMonitor.getCallerById(callId);
        if (caller != null) {
            if (caller.markFinished()) {
                if (result == null) {
                    result = ComponentResult.defaultNullResult();
                }
                caller.setResultForWaiting(result);
            }
        }
    }

    /**
     * 为阻塞的线程设置结果，并唤醒阻塞的线程。
     */
    void setResultForWaiting(ComponentResult result) {
        try {
            synchronized (wait4resultLock) {
                setResult(result);
                if (waiting) {
                    waiting = false;
                    wait4resultLock.notifyAll();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 设置结果
     */
    void setResult(ComponentResult result) {
        finished.set(true);
        mResult = result;
    }

    private boolean markFinished() {
        return finished.compareAndSet(false, true);
    }

    private Dispatcher dispatcher() {
        return mClient.dispatcher();
    }

    private ComponentResult getResultWithInterceptorChain() {
        ComponentChain chain = new ComponentChain(this);

        //添加自定义的拦截器
        chain.addInterceptors(mClient.interceptors());

        chain.addInterceptor(VerifyInterceptor.getInstance());
        //添加监控
        ComponentMonitor.addMonitor(this);
        ComponentResult result;
        try {
            //如果取消了就不执行拦截器
            if (isFinished()) {
                result = mResult;
            } else {
                result = chain.proceed();
            }
        } catch (Exception e) {
            result = ComponentResult.defaultExceptionResult(e);
        } finally {
            ComponentMonitor.removeMonitor(mCallId);
        }
        if (result == null) {
            result = ComponentResult.defaultNullResult();
        }
        mResult = null;
        return result;
    }

    public boolean isAsync() {
        return mAsync;
    }


    final class AsyncRunnable extends NamedRunnable {

        private ComponentCallback mCallback;

        public AsyncRunnable(ComponentCallback callback) {
            super("Component-AsyncRunnable -> %s-%s", mCallId, RealCaller.this.mClient.componentName());
            mCallback = callback;
        }

        void executeOn(ExecutorService executorService) {
            boolean success = false;
            try {
                executorService.execute(this);
                success = true;
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                //如果execute()没有执行成功也要结束该任务
                if (!success) {
                    dispatcher().finished(this);
                }
            }
        }

        @Override
        protected void execute() {
            try {
                ComponentResult result = getResultWithInterceptorChain();
                if (mClient.isCallbackOnMainThread()) {
                    dispatcher().mainThread(new CallbackRunnable(mCallback, RealCaller.this, result));
                } else {
                    if (mCallback != null) {
                        mCallback.onResult(RealCaller.this, result);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                dispatcher().finished(this);
            }
        }
    }

    private final class CallbackRunnable implements Runnable {

        private final Caller mCaller;

        private ComponentCallback mCallback;

        private ComponentResult mResult;

        CallbackRunnable(ComponentCallback callback, Caller caller, ComponentResult result) {
            this.mCaller = caller;
            this.mCallback = callback;
            this.mResult = result;
        }

        @Override
        public void run() {
            try {
                if (mCallback != null) {
                    mCallback.onResult(mCaller, mResult);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


}
