package com.kaolafm.gradle.plugin.component

import org.objectweb.asm.*

import java.util.jar.JarEntry
import java.util.jar.JarFile
import java.util.regex.Pattern
/**
 * 代码扫描类。用到了ASM框架
 * <AUTHOR>
 * @since 17/3/20 11:48
 */
class CodeScanner {

    ArrayList<RegisterInfo> infoList
    Map<String, ScanJarHarvest> cacheMap
    Set<String> cachedJarContainsInitClass = new HashSet<>()

    CodeScanner(ArrayList<RegisterInfo> infoList, Map<String, ScanJarHarvest> cacheMap) {
        this.infoList = infoList
        this.cacheMap = cacheMap
    }

    /**
     * 扫描jar包
     * @param jarFile 来源jar包文件
     * @param destFile transform后的目标jar包文件
     */
    boolean scanJar(File jarFile, File destFile) {
        //检查是否存在缓存，有就添加class list 和 设置fileContainsInitClass
        if (!jarFile || hitCache(jarFile, destFile))
            return false

        def srcFilePath = jarFile.absolutePath
        def file = new JarFile(jarFile)
        Enumeration enumeration = file.entries()

        while (enumeration.hasMoreElements()) {
            JarEntry jarEntry = (JarEntry) enumeration.nextElement()
            String entryName = jarEntry.getName()
            //support包不扫描
            if (entryName.startsWith("android/support"))
                break
            checkInitClass(entryName, destFile, srcFilePath)
            //是否要过滤这个类，这个可配置
            if (shouldProcessClass(entryName)) {
                InputStream inputStream = file.getInputStream(jarEntry)
                scanClass(inputStream, jarFile.absolutePath)
                inputStream.close()
            }
        }
        if (null != file) {
            file.close()
        }
        //加入缓存
        addToCacheMap(null, null, srcFilePath)
        return true
    }
    /**
     * 检查此entryName是不是被注入注册代码的类，如果是则记录此文件（class文件或jar文件）用于后续的注册代码注入
     * @param entryName
     * @param destFile
     */
    boolean checkInitClass(String entryName, File destFile) {
        checkInitClass(entryName, destFile, "")
    }

    boolean checkInitClass(String entryName, File destFile, String srcFilePath) {
        if (entryName == null || !entryName.endsWith(".class"))
            return
        entryName = entryName.substring(0, entryName.lastIndexOf('.'))
        def found = false
        infoList.each { ext ->
            if (ext.initClassName == entryName) {
                ext.fileContainsInitClass = destFile
                if (destFile.name.endsWith(".jar")) {
                    addToCacheMap(null, entryName, srcFilePath)
                    found = true
                }
            }
        }
        return found
    }

    boolean shouldProcessClass(String entryName) {
        if (entryName == null || !entryName.endsWith(".class"))
            return false
        entryName = entryName.substring(0, entryName.lastIndexOf('.'))
        def length = infoList.size()
        for (int i = 0; i < length; i++) {
            if (shouldProcessThisClassForRegister(infoList.get(i), entryName))
                return true
        }
        return false
    }

    /**
     * 过滤器进行过滤，获取的包含的类，排除的不需要包含的类。
     * @param info
     * @param entryName
     * @return
     */
    private static boolean shouldProcessThisClassForRegister(RegisterInfo info, String entryName) {
        if (info != null) {
            def list = info.includePatterns
            if (list) {
                def exlist = info.excludePatterns
                Pattern pattern, p
                for (int i = 0; i < list.size(); i++) {
                    pattern = list.get(i)
                    if (pattern.matcher(entryName).matches()) {
                        if (exlist) {
                            for (int j = 0; j < exlist.size(); j++) {
                                p = exlist.get(j)
                                if (p.matcher(entryName).matches())
                                    return false
                            }
                        }
                        return true
                    }
                }
            }
        }
        return false
    }

    /**
     * 处理class的注入
     * @param file class文件
     * @return 修改后的字节码文件内容
     */
    boolean scanClass(File file) {
        return scanClass(file.newInputStream(), file.absolutePath)
    }

    //refer hack class when object init
    boolean scanClass(InputStream inputStream, String filePath) {
        ClassReader cr = new ClassReader(inputStream)
        ClassWriter cw = new ClassWriter(cr, 0)
        ScanClassVisitor cv = new ScanClassVisitor(Opcodes.ASM6, cw, filePath)
        cr.accept(cv, ClassReader.EXPAND_FRAMES)
        inputStream.close()

        return cv.found
    }

    class ScanClassVisitor extends ClassVisitor {
        private String filePath
        private def found = false

        private String className

        private List<String> classList = new ArrayList<>()

        private List<ConstantInfo> infos


        ScanClassVisitor(int api, ClassVisitor cv, String filePath) {
            super(api, cv)
            this.filePath = filePath

        }

        boolean is(int access, int flag) {
            return (access & flag) == flag
        }

        boolean isFound() {
            return found
        }

        void visit(int version, int access, String name, String signature,
                   String superName, String[] interfaces) {
            super.visit(version, access, name, signature, superName, interfaces)
            className = name
            //抽象类、接口、非public等类无法调用其无参构造方法
            if (is(access, Opcodes.ACC_ABSTRACT)
                    || is(access, Opcodes.ACC_INTERFACE)
                    || !is(access, Opcodes.ACC_PUBLIC)
            ) {
                return
            }
            infoList.each { ext ->
                if (shouldProcessThisClassForRegister(ext, name)) {
                    if (superName != 'java/lang/Object' && !ext.superClassNames.isEmpty()) {
                        for (int i = 0; i < ext.superClassNames.size(); i++) {
                            if (ext.superClassNames.get(i) == superName) {
                                addClass(ext, superName, name)
                                return
                            }
                        }
                    }
                    if (ext.interfaceName && interfaces != null) {
                        interfaces.each { itName ->
                            if (itName == ext.interfaceName) {
                                addClass(ext, itName, name)
                            }
                        }
                    }
                }
            }
        }
        private void addClass(RegisterInfo info, String superName, String className) {
            info.classList.add(className)//需要把对象注入到管理类  就是fileContainsInitClass
            addToCacheMap(superName, className, filePath)
            found = true
            //如果需要共享常量给其他模块使用，就添加到集合中。以便后面查询对应类中的常量
            if (info.isShareConst) {
                classList.add(className)
                initInfoList()
            }
        }

        @Override
        AnnotationVisitor visitAnnotation(String desc, boolean visible) {
            //如果是非自动注册的类想要暴露常量，添加该注解也可以生成常量
            if ("Lcom/kaolafm/kradio/component/SharedConst;".equals(desc)) {
                if (!classList.contains(className)) {
                    initInfoList()
                    classList.add(className)
                }
            }
            return super.visitAnnotation(desc, visible)
        }

        @Override
        FieldVisitor visitField(int access, String name, String desc, String signature, Object value) {
            if (classList.contains(className)) {
                if (is(access, Opcodes.ACC_STATIC) && is(access, Opcodes.ACC_FINAL)) {
                    def info = new ConstantInfo(name, value, desc)
                    if (!infos.contains(info)) {
                        infos.add(info)
                    }
                }
            }
            return super.visitField(access, name, desc, signature, value)
        }

        @Override
        void visitEnd() {
            super.visitEnd()
            if (className != null && infos != null) {
//                ConstantFactory.addConstInfos(className, infos)
            }
        }

        void initInfoList() {
            //第一次时从容器里取
            if (!infos) {
//                infos = ConstantFactory.getInfoList(className)
            }
            //容器里没有就new一个
            if (!infos) {
                infos = new ArrayList<ConstantInfo>()
            }
        }
    }
    /**
     * 扫描到的类添加到map
     * @param interfaceName
     * @param name
     * @param srcFilePath
     */
    private void addToCacheMap(String interfaceName, String name, String srcFilePath) {
        if (!srcFilePath.endsWith(".jar") || cacheMap == null) return
        def jarHarvest = cacheMap.get(srcFilePath)
        if (!jarHarvest) {
            jarHarvest = new ScanJarHarvest()
            cacheMap.put(srcFilePath, jarHarvest)
        }
        if (name) {
            ScanJarHarvest.Harvest classInfo = new ScanJarHarvest.Harvest()
            classInfo.setIsInitClass(interfaceName == null)
            classInfo.setInterfaceName(interfaceName)
            classInfo.setClassName(name)
            jarHarvest.harvestList.add(classInfo)
        }
    }

    boolean isCachedJarContainsInitClass(String filePath) {
        return cachedJarContainsInitClass.contains(filePath)
    }

    /**
     * 检查是否存在缓存，有就添加class list 和 设置fileContainsInitClass
     * @param jarFile
     * @param destFile
     * @return 是否存在缓存
     */
    boolean hitCache(File jarFile, File destFile) {
        def jarFilePath = jarFile.absolutePath
        if (cacheMap != null) {
            ScanJarHarvest scanJarHarvest = cacheMap.get(jarFilePath)
            if (scanJarHarvest) {
                infoList.each { info ->
                    scanJarHarvest.harvestList.each { harvest ->
                        //       println("----harvest-------"+harvest.className)
                        if (harvest.isInitClass) {
                            if (info.initClassName == harvest.className) {
                                info.fileContainsInitClass = destFile
                                cachedJarContainsInitClass.add(jarFilePath)
                            }
                        } else if (info.interfaceName == harvest.interfaceName) {
                            info.classList.add(harvest.className)
                        }

                        for (int i = 0; i < info.superClassNames.size(); i++) {
                            if (info.superClassNames.get(i) == harvest.interfaceName) {
                                info.classList.add(harvest.className)
                            }
                        }
                    }
                }
                return true
            }
        }
        return false
    }
}