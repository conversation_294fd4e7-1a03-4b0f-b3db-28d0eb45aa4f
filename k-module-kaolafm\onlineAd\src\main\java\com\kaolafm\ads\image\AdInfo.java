package com.kaolafm.ads.image;

public class AdInfo {
    private String imageUrl;
    private int skipTime;
    private String attachImageUrl;
    private int adType;
    private int imageDuration;
    private int attachImageDuration;
    private int duration;
    private String adExposureScene;

    public AdInfo() {
    }

    public AdInfo(String imageUrl, int skipTime, String attachImageUrl, int adType, int imageDuration, int attachImageDuration, int duration, String adExposureScene) {
        this.imageUrl = imageUrl;
        this.skipTime = skipTime;
        this.attachImageUrl = attachImageUrl;
        this.adType = adType;
        this.imageDuration = imageDuration;
        this.attachImageDuration = attachImageDuration;
        this.duration = duration;
        this.adExposureScene = adExposureScene;
    }

    public String getImageUrl() {
        return this.imageUrl;
    }

    public String getAttachImageUrl() {
        return attachImageUrl;
    }

    public void setAttachImageUrl(String attachImageUrl) {
        this.attachImageUrl = attachImageUrl;
    }

    public int getAdType() {
        return adType;
    }

    public void setAdType(int adType) {
        this.adType = adType;
    }

    public int getImageDuration() {
        return imageDuration;
    }

    public void setImageDuration(int imageDuration) {
        this.imageDuration = imageDuration;
    }

    public int getAttachImageDuration() {
        return attachImageDuration;
    }

    public void setAttachImageDuration(int attachImageDuration) {
        this.attachImageDuration = attachImageDuration;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public int getSkipTime() {
        return skipTime;
    }

    public void setSkipTime(int skipTime) {
        this.skipTime = skipTime;
    }

    public String getAdExposureScene() {
        return adExposureScene;
    }

    public void setAdExposureScene(String adExposureScene) {
        this.adExposureScene = adExposureScene;
    }
}
