<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:showIn="@layout/fragment_broadcast_player_horizontal"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/left"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.15" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/right"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.85" />

    <!--android:paddingLeft="16dip"  android:paddingRight="16dip" 是系统主题使用的值,这里不做适配-->
    <com.kaolafm.kradio.common.widget.SeekBarView
        android:id="@+id/horizontal_seek_bar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:maxHeight="@dimen/y3"
        android:minHeight="@dimen/y3"
        android:paddingLeft="16dip"
        tools:progress="0"
        tools:max="100"
        android:paddingRight="16dip"
        android:progressDrawable="@drawable/player_progressbar_background"
        android:splitTrack="false"
        android:thumb="@drawable/player_progressbar_thumb"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintLeft_toRightOf="@id/left"
        app:layout_constraintRight_toLeftOf="@id/right"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/seek_bar_elapsed_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/broadcast_player_timer_seek_bar_time_color"
        android:textSize="@dimen/text_size3"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toLeftOf="@id/left"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/seek_bar_total_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/broadcast_player_timer_seek_bar_time_color"
        android:textSize="@dimen/text_size3"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/right"
        app:layout_constraintTop_toTopOf="parent" />

</merge>
