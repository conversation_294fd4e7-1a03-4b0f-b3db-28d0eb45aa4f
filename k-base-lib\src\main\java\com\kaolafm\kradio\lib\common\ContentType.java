package com.kaolafm.kradio.lib.common;

/**
 * 内容类型:服务器和客户端共用一套
 */
public interface ContentType {

    /**
     * "id"	"name"
     * "6"	"交通台"
     * "7"	"经济台"
     * "8"	"新闻台"
     * "9"	"音乐台"
     * "10"	"校园台"
     * "11"	"娱乐台"
     * "12"	"方言台"
     * "13"	"曲艺台"
     * "14"	"外语台"
     * "15"	"文艺台"
     * "16"	"旅游台"
     * "17"	"体育台"
     * "18"	"生活台"
     * "19"	"都市台"
     * "20"	"综合台"
     * "21"	"民族台"
     */
    int NOACTION_TYPE = -1;//无
    int BROADCAST_TRAFFIC_TYPE = 6;//广播交通台
    int BROADCAST_NEWS_TYPE = 8;//广播新闻台
    int BROADCAST_MUSSIC_TYPE = 9;//广播音乐台
}