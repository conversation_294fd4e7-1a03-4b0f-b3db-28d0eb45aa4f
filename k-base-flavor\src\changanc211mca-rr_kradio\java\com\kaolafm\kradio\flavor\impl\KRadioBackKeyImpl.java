package com.kaolafm.kradio.flavor.impl;

import androidx.appcompat.app.AppCompatActivity;

import com.kaolafm.kradio.lib.base.flavor.KRadioBackKeyInter;

/**
 */
public class KRadioBackKeyImpl implements KRadioBackKeyInter {
    @Override
    public boolean onBackPressed(Object... args) {
        AppCompatActivity activity = (AppCompatActivity) args[0];
        activity.moveTaskToBack(true);
        return true;
    }

    @Override
    public boolean appExit(Object... args) {
        return false;
    }

    @Override
    public void dealKillYunTingReceiver(Object... args) {

    }
}
