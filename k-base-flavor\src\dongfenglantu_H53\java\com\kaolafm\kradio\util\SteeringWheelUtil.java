package com.kaolafm.kradio.util;

import android.app.Activity;
import android.content.Context;
import android.util.Log;
import android.view.KeyEvent;

import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;

import mega.car.MegaCar;
import mega.car.config.KeyInput;
import mega.car.input.CarInputManager;
import mega.car.input.CustomInputEvent;

public class SteeringWheelUtil {

    private static final String TAG = "SteeringWheelUtil";

    public static void init(Activity activity) {
        try {
            Log.w(TAG, "begin init");

            MegaCar car = MegaCar.createCar(activity, null);
            CarInputManager manager = car.getCarManager("KeyInput");
            if (manager == null) {
                return;
            }
            manager.requestInputEventCapture(CarInputManager.INPUT_TYPE_CUSTOM_INPUT_EVENT, new CarInputManager.CarInputCaptureCallback() {
                @Override
                public boolean onInterceptTouchEvent(CustomInputEvent customInputEvent) {
                    return false;
                }

                @Override
                public void onCustomInputEvent(CustomInputEvent customInputEvent) {
                    int inputCode = customInputEvent.getInputCode();
                    Log.w(TAG, "handle keycode:" + inputCode);
                    if (inputCode == KeyInput.KeyCodeEnum.RIGHT_RIGHT) {
                        //下一首
                        if (PlayerManagerHelper.getInstance().hasNextItem()) {
                            PlayerManagerHelper.getInstance().playNext(true);
                        } else {
                            Context context = AppDelegate.getInstance().getContext();
                            if (context != null) {
                                ToastUtil.showOnly(context, context.getString(R.string.is_last_one_warning_str));
                            }
                        }
                    } else if (inputCode == KeyInput.KeyCodeEnum.RIGHT_LEFT) {
                        //上一首
                        if (PlayerManagerHelper.getInstance().hasPreItem()) {
                            PlayerManagerHelper.getInstance().playPre(true);
                        } else {
                            Context context = AppDelegate.getInstance().getContext();
                            if (context != null) {
                                ToastUtil.showOnly(context, context.getString(R.string.is_first_one_warning_str));
                            }
                        }
                    } else if (inputCode == KeyInput.KeyCodeEnum.RIGHT_SELECT) {
                        //暂停、播放
                        PlayerManagerHelper.getInstance().switchPlayerStatus(true);
                    }
                }

                @Override
                public void onCustomerKeyLongPress(CustomInputEvent customInputEvent) {
                }

                @Override
                public void onKeyEvent(KeyEvent keyEvent) {
                } //end onKeyEvent
            });
            Log.w(TAG, "end init");
        } catch (Exception | Error e) {
            Log.e(TAG, e.toString());
        }
    }
}
