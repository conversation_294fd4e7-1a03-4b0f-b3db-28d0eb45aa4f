package com.kaolafm.kradio.online.player.pages;

import android.Manifest;
import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.media.AudioManager;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.SystemClock;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.app.ActivityCompat;
import androidx.fragment.app.Fragment;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.DecelerateInterpolator;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.google.gson.Gson;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.common.report.ReportParamUtil;
import com.kaolafm.kradio.common.report.ReportUtil;
import com.kaolafm.kradio.component.ComponentClient;
import com.kaolafm.kradio.component.DynamicComponent;
import com.kaolafm.kradio.component.RealCaller;
import com.kaolafm.kradio.constant.UserComponentConst;
import com.kaolafm.kradio.constant.UserStateObserverProcessorConst;
import com.kaolafm.kradio.k_kaolafm.R; 
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioFocusInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioFragmentTransStatusBarAdapterInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioSpeakImageInter;
import com.kaolafm.kradio.lib.common.ResType;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.ComponentUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.TimerUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.live1.model.ChatUserInfo;
import com.kaolafm.kradio.live1.model.MessageBean;
import com.kaolafm.kradio.live1.mvp.LivePresenter;
import com.kaolafm.kradio.live1.player.ErrorStatus;
import com.kaolafm.kradio.live1.player.HomeLiveManager;
import com.kaolafm.kradio.live1.player.LiveStatus;
import com.kaolafm.kradio.live1.player.NimManager;
import com.kaolafm.kradio.live1.player.RecordUploadHelper;
import com.kaolafm.kradio.live1.player.RecorderStatus;
import com.kaolafm.kradio.live1.player.SendChatMsgData;
import com.kaolafm.kradio.live1.comprehensive.utils.LiveUtil;
import com.kaolafm.kradio.online.common.base.MBaseShowHideFragment;
import com.kaolafm.kradio.online.common.view.RoundImageView;
import com.kaolafm.kradio.online.player.adapters.LiveMessageAdapter;
import com.kaolafm.kradio.online.player.models.BusEventPlayingProgramIdChanged;
import com.kaolafm.kradio.online.player.models.SystemMessage;
import com.kaolafm.kradio.online.player.mvp.BroadcastDetailPresenter;
import com.kaolafm.kradio.online.player.mvp.OnlineHomeLivePresenter;
import com.kaolafm.kradio.online.player.mvp.OnlineHomeLiveView;
import com.kaolafm.kradio.online.player.utils.LowSpeedLayoutManager;
import com.kaolafm.kradio.online.player.utils.OnlinePlayerLiveUtil;
import com.kaolafm.kradio.online.player.utils.VerticalSpaceDividerDecoration;
import com.kaolafm.kradio.online.player.views.RecordButtonBoxLayout;
import com.kaolafm.kradio.common.view.StrokeRoundImageView;
import com.kaolafm.kradio.player.event.PlayerChangedEBData;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.api.broadcast.BroadcastRequest;
import com.kaolafm.opensdk.api.broadcast.ProgramDetails;
import com.kaolafm.opensdk.api.live.model.LiveInfoDetail;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.LivePlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Observable;
import java.util.Observer;
import java.util.concurrent.atomic.AtomicBoolean;
 
 
import skin.support.content.res.SkinCompatResources;

import static android.media.AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK;

/**
 * 直播弹幕
 */
public class LiveBarrageFragment extends MBaseShowHideFragment<OnlineHomeLivePresenter> implements OnlineHomeLiveView, View.OnClickListener {

    private static final String TAG = LiveBarrageFragment.class.getSimpleName();

    private static final int CODE_PERMISSION_REQUEST = 1;
    private static final int PLAY_ERROR_TOLERANCE_TIME = 60 * 1000;

    public static final int DURATION_LONG = 1500;

    private static final int RECORD_DURATION = 20 * 1000;
    private static final int RECORD_COUNTDOWN = 1000;
    private static String PROGRAM_ID = "PROGRAM_ID";
 
    ImageView iconLiving; 
    TextView mListenerNumberText; 
    FrameLayout mScreenBulletContainer; 
    ConstraintLayout mLiveNotStartLayout; 
    TextView mForecastText; 
    ConstraintLayout mLiveAlreadyFinishLayout; 
    ConstraintLayout mErrorLayout; 
    ConstraintLayout mComingLayout; 
    RecyclerView recyclerView; 
    View liveBarrageRootView; 
    RecordButtonBoxLayout recordButtonBoxLayout; 
    RelativeLayout userHeaderParentView; 
    TextView refreshStatusLayout;


    private boolean isFront = false;
    private boolean gotoLogin = false;  //是否打开过登录页面

    private TextView mLastMessageText;
    private boolean isLoadingHistory = false;  //是否正在加载历史数据

    private BroadcastDetailPresenter mBroadcastDetailPresenter;

    private long mProgramId; //节目id

    private long mLastRecordTime;
    private long mLastListenTime;

    private String mForecastString;

    private RecyclerView.OnScrollListener mScrollListener;
    private AtomicBoolean isFirstLoadHistory = new AtomicBoolean(true); //第一次加载
    private AtomicBoolean checkLiveInfoByUser = new AtomicBoolean(false);   //用户点击“立即收听”按钮查询状态

    private KRadioSpeakImageInter mKRadioSpeakImageInter;
    private KRadioAudioFocusInter mKRadioAudioFocusInter;
    private KRadioFragmentTransStatusBarAdapterInter mKRadioFragmentTransStatusBarAdapterInter;
    private UserLoginComponent mUserLoginComponent;

    private boolean bPlayerEnabled;
    private PlayItem mPlayItem;

    private BasePlayStateListener mPlayerStateListener;

    private CountDownTimer mRecordTimer;
    private CountDownTimer mForecastTimer;

    private int mSystemVolume;

    private LinkedList<ChatUserInfo> mMemberEnterQueue = new LinkedList<>();
    private LinkedList<MessageBean> mMessageReceiveQueue = new LinkedList<>();

    //在线成员列表，仅保存用于展示头像的几个成员
    private List<ChatUserInfo> mOnlineMembers = new ArrayList<>();

    private boolean bDoMemberEnterAnim;
    private boolean bDoMessageReceiveAnim;

    //系统消息列表，临时将系统消息放置到列表中展示
    private List<SystemMessage> mMessages = new ArrayList() {{
        MessageBean messageBean = new MessageBean();
        messageBean.contentString = ResUtil.getString(R.string.online_player_live_system_tip);
        messageBean.chatTime = String.valueOf(System.currentTimeMillis());
        add(new SystemMessage(SystemMessage.MessageType.SYSTEM_MESSAGE, null, messageBean));
    }};
    private LiveMessageAdapter mMessageAdapter;
    private int mineHeaderViewPosition = -1;    //未登录时自己的头像在头像父级View中的位置
    private int listenerCount = 0;//当前收听总人数，会被定时更新
    private boolean isShow = false;

    public LiveBarrageFragment() {
        // Required empty public constructor
    }

    public static LiveBarrageFragment newInstance(long programId, String mTag) {
        LiveBarrageFragment fragment = new LiveBarrageFragment();
        Bundle args = new Bundle();
        args.putLong(PROGRAM_ID, programId);
        args.putString(ARGUMENT_TAG, mTag);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected int getLayoutId() {
        return R.layout.online_player_fragment_live_barrage;
    }

    @Override
    protected OnlineHomeLivePresenter createPresenter() {
        OnlineHomeLivePresenter presenter = new OnlineHomeLivePresenter(this);
        HomeLiveManager.getInstance().setPresenter(presenter);
        return presenter;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mForecastString = getString(R.string.online_player_live_hour_minute_second);
        mKRadioSpeakImageInter = ClazzImplUtil.getInter("KRadioSpeakImageImpl");
        mKRadioFragmentTransStatusBarAdapterInter = ClazzImplUtil.getInter("KRadioFragmentTransStatusBarAdapterImpl");

        mUserLoginComponent = new UserLoginComponent();
        ComponentUtil.addObserver(UserComponentConst.NAME, mUserLoginComponent);
    }

    @Override
    public void onResume() {
        super.onResume();
        isFront = true;
        TimerUtil.newInstance().timer(250, num -> {
            //36568 【monkey】FATAL EXCEPTION: main，io.reactivex.exceptions.OnErrorNotImplementedException
            if (mPresenter != null) {
                mPresenter.initNim();
                initHomeLiveManager();
            }
        });
    }

    @Override
    public void onPause() {
        isFront = false;
        isShow = false;
        super.onPause();
    }

    private void initHomeLiveManager() {
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "initHomeLiveManager");
        }
        HomeLiveManager.getInstance().addRecorderStatusObserver(mRecorderStatusObserver);
        if (!LiveUtil.isUserBound()) {
//            mLoginPromptText.setText(R.string.live_leave_message_after_login);
            recordButtonBoxLayout.notifyUserLoginStatus(false);
        } else {
            recordButtonBoxLayout.notifyUserLoginStatus(true);
//            mLoginPromptText.setText(R.string.live_click_to_speak);
        }

        if (mProgramId > 0) {
            HomeLiveManager.getInstance().loopLiveStatus(mProgramId);
        } else {
            Log.i(TAG, "initHomeLiveManager, null programid, no old data, show error");
            showErrorInfo(ErrorStatus.ERROR_ARGUMENT);
        }
    }

    @Override
    public String getPageId() {
        return Constants.PAGE_ID_LIVE_ROOM;
    }

    @Override
    protected boolean isReportFragment() {
        return true;
    }

    @Override
    public void initView(View view) {

        iconLiving=view.findViewById(R.id.icon_living);
        mListenerNumberText=view.findViewById(R.id.live_listening_number);
        mScreenBulletContainer=view.findViewById(R.id.live_screen_bullet_layout);
        mLiveNotStartLayout=view.findViewById(R.id.live_not_start_layout);
        mForecastText=view.findViewById(R.id.live_not_start_text);
        mLiveAlreadyFinishLayout=view.findViewById(R.id.live_finish_layout);
        mErrorLayout=view.findViewById(R.id.live_error_layout);
        mComingLayout=view.findViewById(R.id.live_coming_layout);
        recyclerView=view.findViewById(R.id.recyclerView);
        liveBarrageRootView=view.findViewById(R.id.liveBarrageRootView);
        recordButtonBoxLayout=view.findViewById(R.id.recordButtonBoxLayout);
        userHeaderParentView=view.findViewById(R.id.userHeaderParentView);
        refreshStatusLayout=view.findViewById(R.id.refresh_status_layout);
        refreshStatusLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                checkLiveInfoByUser.set(true);
                mPresenter.getLiveInfo(mProgramId);
            }
        });
     

        LinearLayoutManager layout = new LowSpeedLayoutManager(getContext(), LinearLayoutManager.VERTICAL, false);
        layout.setStackFromEnd(true);
        recyclerView.setLayoutManager(layout);
        mMessageAdapter = new LiveMessageAdapter(mMessages);
        recyclerView.setAdapter(mMessageAdapter);

        recyclerView.getItemAnimator().setAddDuration(0);
        recyclerView.addItemDecoration(new VerticalSpaceDividerDecoration(recyclerView.getContext(), ResUtil.getDimen(R.dimen.y32)));
        initViewInner();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPlayerChanged(PlayerChangedEBData playerChangedEBData) {
        if (bPlayerEnabled) {
            HomeLiveManager.getInstance().onLiveExit();
            this.pop();
            exitChatRoom();
        }
    }

    @Override
    public void showFileNotExist() {
        String msg = getResources().getString(R.string.online_player_live_upload_failed) + ": "
                + getResources().getString(R.string.online_player_live_file_not_exist);
        showErrorToast(msg);
        HomeLiveManager.getInstance().setRecorderStatus(RecorderStatus.IDLE);
    }

    @Override
    public void showRecordUploadProgress(int progress) {
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "showRecordUploadProgress: " + progress);
        }
//        mRecordButtonImage.setProgress(progress);
    }

    @Override
    public void showRecordUploadSuccess() {
        //语音消息发送成功后才上报发送事件
        LiveInfoDetail info = HomeLiveManager.getInstance().getPlayingInfo();
        ReportUtil.addLivingLeaveMessageEvent(String.valueOf(info.getLiveId()), String.valueOf(info.getProgramId()), info.getComperes(), null);
        HomeLiveManager.getInstance().setRecorderStatus(RecorderStatus.UPLOADED);
        HomeLiveManager.getInstance().deleteFile();
    }

    @Override
    public void showRecordUploadFailure() {
        //由于文件上传的回调是在子线程进行的，所以用post的方式让它在UI线程更新UI
        post(new Runnable() {
            @Override
            public void run() {
                ToastUtil.showError(getContext(), R.string.online_player_live_upload_failed);
                HomeLiveManager.getInstance().setRecorderStatus(RecorderStatus.FAILURE);
            }
        });
    }

    @Override
    public void showLiveInfo(LiveInfoDetail info) {
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "showLiveInfo");
        }
        if (info == null) {
            showErrorInfo(ErrorStatus.ERROR_REQUEST);
        } else {
            String roomId = info.getRoomId();
            // FIXME: 2022/7/5 1721206561这个roomId是测试专用的新直播间的id
//            String roomId = "373127209";
            int status = info.getStatus();
            if (LivePresenter.DEBUG_LIVE) {
                Log.i(TAG, "showLiveInfo roomId: " + roomId + ", STATUS: " + status);
            }
            if (!TextUtils.isEmpty(roomId) && info.getStatus() == LiveInfoDetail.STATUS_LIVING) {
                if (LiveUtil.isUserBound()) {
                    mPresenter.enterChatRoom(getContext(), roomId);
//                    recordButtonBoxLayout.notifyUserLoginStatus(true);
                } else {
                    mPresenter.enterChatRoom(true, getContext(), roomId);
//                    recordButtonBoxLayout.notifyUserLoginStatus(false);
                    if (isFront && !gotoLogin) {
                        gotoLogin = true;
                    }
                }
            }
            setContent(info);
        }
    }

    @Override
    public void showErrorInfo(ErrorStatus status) {
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "getShowingInfo onError msg: " + status);
        }
        showError(ErrorStatus.ERROR_REQUEST);
    }

    private void setContent(LiveInfoDetail info) {
        HomeLiveManager.getInstance().setShowingInfo(info);
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "setContent: " + info.getStatus());
        }
        showCommonInfo(info);
        mPresenter.refreshChatRoomInfo();
        int status = info.getStatus();

        PlayItem item = PlayerManager.getInstance().getCurPlayItem();
        if (item instanceof LivePlayItem) {
            if (info.getProgramId() == ((LivePlayItem) item).getLiveId()) {
                ((LivePlayItem) item).setStatus(status);
            }
        }

        if (status == LiveInfoDetail.STATUS_NOT_START) {
            if (HomeLiveManager.getInstance().getLiveStatus() != LiveStatus.NOT_START
                    && !mLiveNotStartLayout.isShown()) {
                showForecast(info);
            }
            if (checkLiveInfoByUser.compareAndSet(true, false)) {
                ToastUtil.showNormal(getContext(), R.string.online_player_live_coming_1);
            }
            stopLive();
            iconLiving.setVisibility(View.GONE);
        } else if (status == LiveInfoDetail.STATUS_FINISHED) {
            if (HomeLiveManager.getInstance().getLiveStatus() != LiveStatus.FINISHED
                    && !mLiveAlreadyFinishLayout.isShown()) {
                showFinish();
            }
            stopLive();
            iconLiving.setVisibility(View.GONE);
        } else if (status == LiveInfoDetail.STATUS_LIVING) {
            iconLiving.setVisibility(View.VISIBLE);
            if (HomeLiveManager.getInstance().getLiveStatus() != LiveStatus.LIVING) {
                showLive(info);
            }
        } else if (status == LiveInfoDetail.STATUS_COMING) {
            if (HomeLiveManager.getInstance().getLiveStatus() != LiveStatus.COMING
                    && !mComingLayout.isShown()) {
                showComing();
            }
            iconLiving.setVisibility(View.GONE);
        } else if (status == LiveInfoDetail.STATUS_DELAYED) {
            if (HomeLiveManager.getInstance().getLiveStatus() != LiveStatus.NOT_START
                    && !mLiveNotStartLayout.isShown()) {
                showForecast(info);
            }
            iconLiving.setVisibility(View.GONE);
        } else {
            //未知状态
            hideRecordButton();
        }
        //report
        if (!isShow)
            reportContentShowEvent(info);
    }

    private void reportContentShowEvent(LiveInfoDetail info) {
        isShow = true;
        ReportUtil.addContentShowEvent(String.valueOf(info.getProgramId()),
                ReportParamUtil.getRadioType(ResType.LIVE_TYPE),
                "0",
                String.valueOf(info.getLiveId()), "无",
                getPageId(), getPageId(), "0");

    }

    private void showCommonInfo(LiveInfoDetail result) {
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "showCommonInfo");
        }
        Fragment parentFragment = getParentFragment();
        if (parentFragment instanceof OnlinePlayerFragment) {
            ((OnlinePlayerFragment) parentFragment).setComperes(result.getComperes());
        }
//        loadImage(result.getProgramPic(), false);
//        mLiveNameText.setText(result.getProgramName());
//        mLivePlayerText.setText(String.format(getString(R.string.live_player), result.getComperes()));
    }


    /**
     * 收到消息动画，在左下部向上弹起
     *
     * @param message
     */
    private void showMessageReceived(MessageBean message) {
        final TextView textView = (TextView)
                LayoutInflater.from(getContext()).inflate(R.layout.online_player_live_screen_bullet_text, null);
        textView.setText(String.format(getString(R.string.online_player_live_message_received), message.nickName));
//        textView.setBackgroundResource(R.drawable.live_message_received_bg);
        textView.setBackgroundDrawable(SkinCompatResources.getDrawableCompat(getContext(), R.drawable.online_player_live_message_received_bg));
        final FrameLayout.LayoutParams lp = new FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.WRAP_CONTENT,
                FrameLayout.LayoutParams.WRAP_CONTENT);
        lp.leftMargin = iconLiving.getLeft();
        mScreenBulletContainer.addView(textView, lp);

        int bottom = mScreenBulletContainer.getBottom() - mScreenBulletContainer.getTop();

        if (mLastMessageText != null) {
            int s = (int) mLastMessageText.getY();
            int e = (int) (bottom * 0.60);

            ObjectAnimator oan = ObjectAnimator.ofFloat(mLastMessageText, "y", s, e);
            oan.setDuration(300);

            ObjectAnimator oaa = ObjectAnimator.ofFloat(mLastMessageText, "alpha", 1, 0);
            oaa.setDuration(300);

            AnimatorSet as = new AnimatorSet();
            as.playTogether(oan, oaa);
            as.start();
        }

        int width = View.MeasureSpec.makeMeasureSpec((1 << 30) - 1, View.MeasureSpec.AT_MOST);
        int height = View.MeasureSpec.makeMeasureSpec((1 << 30) - 1, View.MeasureSpec.AT_MOST);
        textView.measure(width, height);

        int middle = bottom - (textView.getMeasuredHeight() * 2);
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001576707636?userId=2169710问题
        ObjectAnimator oan = ObjectAnimator.ofFloat(textView, "y", bottom, middle - ResUtil.getInt(R.integer.online_player_live_msg_y_d_value));
        oan.setDuration(500);
        oan.setInterpolator(new DecelerateInterpolator());
        AnimatorSet animSet = new AnimatorSet();
        animSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
                bDoMessageReceiveAnim = true;
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                bDoMessageReceiveAnim = false;
                if (mScreenBulletContainer != null && mLastMessageText != null) {
                    mScreenBulletContainer.removeView(mLastMessageText);
                }
                mLastMessageText = textView;
                if (mMessageReceiveQueue.size() > 0) {
                    MessageBean mb = mMessageReceiveQueue.removeFirst();
                    showMessageReceived(mb);
                }
                mLastMessageText.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (mLastMessageText != null && mScreenBulletContainer != null) {
                            mScreenBulletContainer.removeView(mLastMessageText);
                        }
                    }
                }, 4000);
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                bDoMessageReceiveAnim = false;
            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        animSet.play(oan);
        animSet.start();
    }

    private void hideRecordButton() {
        cancelRecord();
        recordButtonBoxLayout.setVisibility(View.INVISIBLE);
    }

    private void showImageButton() {
        recordButtonBoxLayout.setOnRecordButtonClickListener(this::onClick);
        recordButtonBoxLayout.setOnListenButtonClickListener(this::onClick);
        recordButtonBoxLayout.setOnCancelButtonClickListener(this::onClick);
        recordButtonBoxLayout.setOnSendButtonClickListener(this::onClick);

        if (mKRadioSpeakImageInter != null && mKRadioSpeakImageInter.hideSpeakImage()) {
            ViewUtil.setViewVisibility(recordButtonBoxLayout, View.GONE);
        } else {
            ViewUtil.setViewVisibility(recordButtonBoxLayout, View.VISIBLE);
        }
    }

    private void showForecast(LiveInfoDetail result) {
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "showForecast");
        }
        HomeLiveManager.getInstance().setLiveStatus(LiveStatus.NOT_START);
        hideRecordButton();
        recyclerView.setVisibility(View.GONE);
        userHeaderParentView.setVisibility(View.GONE);
        mListenerNumberText.setVisibility(View.GONE);
        mLiveAlreadyFinishLayout.setVisibility(View.GONE);
        mErrorLayout.setVisibility(View.GONE);
        mComingLayout.setVisibility(View.GONE);
        startForecastTimer(result.getStartTime(), result.getServerTime());
    }

    private void showFinish() {
        //轮询到结束并且播放器也没有播放，就不在轮询了。
        HomeLiveManager.getInstance().stopLoopLiveStatus();
        HomeLiveManager.getInstance().setLiveStatus(LiveStatus.FINISHED);
        refreshStatusLayout.setVisibility(View.GONE);
        recyclerView.setVisibility(View.GONE);
        mLiveAlreadyFinishLayout.setVisibility(View.VISIBLE);
        mListenerNumberText.setVisibility(View.GONE);
        userHeaderParentView.setVisibility(View.GONE);
        mLiveNotStartLayout.setVisibility(View.GONE);
        mComingLayout.setVisibility(View.GONE);
        mErrorLayout.setVisibility(View.GONE);
        hideRecordButton();
        if (mPlayItem != null && bPlayerEnabled
                && HomeLiveManager.getInstance().getLiveStatus() == LiveStatus.LIVING) {
            bPlayerEnabled = false;
            PlayerManager.getInstance().reset();
        }
    }

    private void showLive(LiveInfoDetail info) {
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "showLive result: " + info.getProgramName());
        }
        boolean isNeedRestart = HomeLiveManager.getInstance().getLiveStatus() != LiveStatus.LIVING;
        HomeLiveManager.getInstance().setLiveStatus(LiveStatus.LIVING);
        userHeaderParentView.setVisibility(View.VISIBLE);
        recyclerView.setVisibility(View.VISIBLE);
        refreshStatusLayout.setVisibility(View.GONE);
        mLiveNotStartLayout.setVisibility(View.GONE);
        mLiveAlreadyFinishLayout.setVisibility(View.GONE);
        mErrorLayout.setVisibility(View.GONE);
        mComingLayout.setVisibility(View.GONE);
        showImageButton();
        mPlayItem = HomeLiveManager.getInstance().toPlayItem(info);
        String liveUrl = mPlayItem.getPlayUrl();
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "showLive play liveUrl: " + liveUrl);
        }
        if (liveUrl == null) {
            showComing();
        } else {
            boolean isPlaying = PlayerManager.getInstance().isPlaying();

            bPlayerEnabled = true;

            mPlayerStateListener = new BasePlayStateListener() {
                @Override
                public void onPlayerFailed(PlayItem playItem, int i1, int i) {
                    if (getView() != null) {
                        showError(ErrorStatus.ERROR_PLAY);
                    }
                }
            };

            PlayerManager.getInstance().addPlayControlStateCallback(mPlayerStateListener);
            HomeLiveManager.getInstance().setPlayingInfo(info);
            PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
            if (playItem instanceof LivePlayItem) {
                if (isPlaying && ((LivePlayItem) mPlayItem).getLiveId() == ((LivePlayItem) playItem).getLiveId()) {
                    if (LivePresenter.DEBUG_LIVE) {
                        Log.i(TAG, "showLive play but is playing and have same id, return");
                    }
                    return;
                }
            }
            //原本的逻辑是进入直播间后自动播放，现在要求不自动播放
            if (isNeedRestart && PlayerManagerHelper.getInstance().isPlaying()) {
                PlayerManagerHelper.getInstance().start(((LivePlayItem) mPlayItem).getLiveId() + "", PlayerConstants.RESOURCES_TYPE_LIVING);
                initHomeLiveManager();
            }
        }

    }

    private void showComing() {
        HomeLiveManager.getInstance().setLiveStatus(LiveStatus.COMING);
        mLiveNotStartLayout.setVisibility(View.GONE);
        mLiveAlreadyFinishLayout.setVisibility(View.GONE);
        refreshStatusLayout.setVisibility(View.GONE);
        recyclerView.setVisibility(View.GONE);
        mErrorLayout.setVisibility(View.GONE);
        mListenerNumberText.setVisibility(View.GONE);
        userHeaderParentView.setVisibility(View.GONE);
        mComingLayout.setVisibility(View.VISIBLE);
        hideRecordButton();
    }

    private void showError(ErrorStatus status) {
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "showError reason: " + status);
        }
        if (status == ErrorStatus.ERROR_PLAY) {
            //播放错误，如果状态是结束，则显示结束
            LiveInfoDetail info = HomeLiveManager.getInstance().getPlayingInfo();
            if (info == null) {
                HomeLiveManager.getInstance().setErrorStatus(status);
                showCannotPlayError();
                return;
            }
            if (info.getStatus() == LiveInfoDetail.STATUS_FINISHED) {
                showFinish();
                return;
            } else if (info.getStatus() == LiveInfoDetail.STATUS_LIVING) {
                //直播倒计时结束，开始播放，由于流的延迟性，很可能播放失败，所以在直播开始后的1分钟内的
                //播放失败，当做预告处理
                long serverTime = info.getServerTime();
                long startTime = info.getStartTime();
                if (LivePresenter.DEBUG_LIVE) {
                    Log.i(TAG, "showError living serverTime: " + serverTime
                            + ", startTime: " + startTime);
                }
                if (serverTime - startTime <= PLAY_ERROR_TOLERANCE_TIME) {
                    showComing();
                    return;
                }
            }
        }
        HomeLiveManager.getInstance().setErrorStatus(status);
        showCannotPlayError();
    }

    private void showCannotPlayError() {
        hideRecordButton();
        HomeLiveManager.getInstance().setLiveStatus(LiveStatus.ERROR);
        PlayerManager.getInstance().removePlayControlStateCallback(mPlayerStateListener);
        PlayerManager.getInstance().reset();
        bPlayerEnabled = false;
        mComingLayout.setVisibility(View.GONE);
        mLiveNotStartLayout.setVisibility(View.GONE);
        mLiveAlreadyFinishLayout.setVisibility(View.GONE);
        recyclerView.setVisibility(View.GONE);
        refreshStatusLayout.setVisibility(View.GONE);
        mErrorLayout.setVisibility(View.VISIBLE);
    }

    /**
     * 显示收听人数
     *
     * @param number
     */
    @Override
    public void showListenerNumber(int number) {
        this.listenerCount = number;
        mListenerNumberText.setText(formatNumber(this.listenerCount));
        LiveInfoDetail liveInfo = HomeLiveManager.getInstance().getShowingInfo();
        if (liveInfo != null && liveInfo.getStatus() == LiveInfoDetail.STATUS_LIVING) {
            ViewUtil.setViewVisibility(mListenerNumberText, View.VISIBLE);
        } else {
            ViewUtil.setViewVisibility(mListenerNumberText, View.GONE);
        }
    }

    private String formatNumber(int number) {
        if (number < 10000) {
            return String.format(getString(R.string.online_player_live_listening_number_low), number);
        }
        float result = number / 10000f;
        return String.format(getString(R.string.online_player_live_listening_number_high), result);
    }

    /**
     * 显示成员进入，如果当前正在执行{@link #showMemberEnter(ChatUserInfo)}中的动画,则这个消息进入队列，
     * 等动画完成后再进行
     *
     * @param member
     */
    @Override
    public void showRoomMemberEnter(ChatUserInfo member) {
        mPresenter.getListenerNumber();
        if (member != null) {
            // TODO: 2022/8/24
            if (LivePresenter.DEBUG_LIVE) {
                Log.i(TAG, "showRoomMemberEnter: " + member.getNickName());
            }
            if (userHeaderParentView.getChildCount() > 5) return;
            if (userHeaderParentView.getChildCount() == 5) {
                addMoreHeaderTpParent();
            }
            if (!containsMember(member)) {
                mOnlineMembers.add(member);
                addHeaderToParent(member, userHeaderParentView.getChildCount() * ResUtil.getDimen(R.dimen.x24));
            }
            if (mOnlineMembers.size() >= 5) {
                addMoreHeaderTpParent();
            }
            //如果自己没登陆而且没有获取到昵称
            if (!LiveUtil.isUserBound() && StringUtil.isEmpty(member.getNickName())) return;

            /*
            //当前版本不再显示成员进入的漂浮提示
            //如果正在做成员进入动画，或者成员进入队列中有元素在排队，添加到队尾，否则展示此条进入提示
            if (mMemberEnterQueue.size() > 0 || bDoMemberEnterAnim) {
                mMemberEnterQueue.addLast(member);
            } else {
                showMemberEnter(member);
            }
             */

        }

    }

    @Override
    public void showRoomMemberExit(ChatUserInfo member) {
        mPresenter.getListenerNumber();
        if (!containsMember(member)) return;
        if (mPresenter.isChatRoomEntered())
            mPresenter.fetchRoomMembers();
    }

    /**
     * 成员进入动画，在顶部自右往左飞过
     *
     * @param member
     */
    private void showMemberEnter(ChatUserInfo member) {
        // TODO: 2022/6/30
        Context context = getContext();
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001107580293问题
        if (context == null) {
            return;
        }

        // 将进入直播间的系统消息添加到列表
        if (mMessageAdapter != null) {
            mMessages.add(new SystemMessage(SystemMessage.MessageType.ENTER_ROOM, member, null));
            mMessageAdapter.notifyItemInserted(mMessages.size() - 1);
            if (!recyclerView.canScrollVertically(1)) {
                recyclerView.smoothScrollToPosition(mMessages.size() - 1);
            }
        }


        final TextView textView = (TextView)
                LayoutInflater.from(context).inflate(R.layout.online_player_live_screen_bullet_text, null);
        textView.setText(String.format(getString(R.string.online_player_live_member_enter), member.getNickName()));

        textView.setBackgroundDrawable(SkinCompatResources.getDrawableCompat(getContext(), R.drawable.online_player_live_screen_bullet_bg));
        textView.setGravity(Gravity.CENTER_VERTICAL);
        FrameLayout.LayoutParams lp = new FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.WRAP_CONTENT,
                FrameLayout.LayoutParams.WRAP_CONTENT);
        lp.topMargin = (int) (mScreenBulletContainer.getHeight() * 0.2);
        mScreenBulletContainer.addView(textView, lp);

        int start = mScreenBulletContainer.getRight();
        //直播中的图标右边50px慢下来
        int middle = iconLiving.getRight() + 50;
        int left = iconLiving.getLeft();

        ObjectAnimator oar = ObjectAnimator.ofFloat(textView, "x", start, middle);
        oar.setDuration(500);
        oar.setInterpolator(new DecelerateInterpolator());

        ObjectAnimator oam = ObjectAnimator.ofFloat(textView, "x", middle, left);
        oam.setDuration(3000);

        //滑出屏幕，需要一个负值,由于View刚Add，还没有宽度，所以写一个参考值
        ObjectAnimator oal = ObjectAnimator.ofFloat(textView, "x", left, -300);
        oal.setDuration(300);
        oal.setInterpolator(new AccelerateInterpolator());

        AnimatorSet animSet = new AnimatorSet();
        animSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
                bDoMemberEnterAnim = true;
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                bDoMemberEnterAnim = false;
                if (mScreenBulletContainer != null && textView != null) {
                    mScreenBulletContainer.removeView(textView);
                }
                if (mMemberEnterQueue.size() > 0) {
                    ChatUserInfo member = mMemberEnterQueue.removeFirst();
                    showMemberEnter(member);
                }
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                bDoMemberEnterAnim = false;
            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        animSet.play(oal).after(oam).after(oar);
        animSet.start();
    }

    public void showChatMessageSent(ArrayList<MessageBean> messageList) {
        if (messageList == null || messageList.size() <= 0) {
            if (LivePresenter.DEBUG_LIVE) {
                Log.i(TAG, "showChatMessageReceived empty message list");
            }
            return;
        }
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "showChatMessageReceived message count: " + messageList.size());
        }
        int startPosition = mMessages.size();
        for (MessageBean messageBean : messageList) {
            mMessages.add(new SystemMessage(SystemMessage.MessageType.SEND, null, messageBean));
        }
        mMessageAdapter.notifyItemRangeInserted(startPosition, 1);
        if (!recyclerView.canScrollVertically(1)) {
            recyclerView.smoothScrollToPosition(mMessages.size() - 1);
        }

//        showMessageReceivedAnimation(messageList);
    }

    /**
     * 显示收到消息，如果当前正在执行{@link #showMessageReceived(MessageBean)}中的动画,
     * 则这个消息进入队列，等动画完成后再进行。如果是多条消息，则逐条进入队列
     *
     * @param messageList
     */
    @Override
    public void showChatMessageReceived(ArrayList<MessageBean> messageList) {
        if (messageList == null || messageList.size() <= 0) {
            if (LivePresenter.DEBUG_LIVE) {
                Log.i(TAG, "showChatMessageReceived empty message list");
            }
            return;
        }
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "showChatMessageReceived message count: " + messageList.size());
        }

        int startPosition = mMessages.size();
        for (MessageBean messageBean : messageList) {
            mMessages.add(new SystemMessage(SystemMessage.MessageType.RECEIVE, null, messageBean));
        }
        mMessageAdapter.notifyItemRangeInserted(startPosition, messageList.size());
        if (!recyclerView.canScrollVertically(1)) {
            recyclerView.smoothScrollToPosition(mMessages.size() - 1);
        }
//        showMessageReceivedAnimation(messageList);
    }

    private void showMessageReceivedAnimation(ArrayList<MessageBean> messageList) {
        //消息以列表的方式组织，所以如果消息队列中有消息或者正在进行消息提示的动画，把所有
        //新来的消息添加到队列，
        if (mMessageReceiveQueue.size() > 0 || bDoMessageReceiveAnim) {
            mMessageReceiveQueue.addAll(messageList);
        } else {
            //否则，取列表中的第一条展示，如果还有剩下的，进入消息队列
            MessageBean message = messageList.remove(0);
            showMessageReceived(message);
            if (messageList.size() > 0) {
                mMessageReceiveQueue.addAll(messageList);
            }
        }
    }

    @Override
    public void onDestroyView() {
        releaseOnDestroyView();
        super.onDestroyView();
    }

    public void releaseOnDestroyView() {
        if (mScrollListener != null)
            recyclerView.removeOnScrollListener(mScrollListener);
        PlayerManager.getInstance().removePlayControlStateCallback(mPlayerStateListener);
        clearNimStatus();
    }

    /**
     * 退出直播间并退出云信
     */
    private void clearNimStatus() {
        if (PlayerManagerHelper.getInstance().isLivingPlayer()) {
            HomeLiveManager.getInstance().stopLoopLiveStatus();
            exitChatRoom();
            mPresenter.logoutIm();
            bPlayerEnabled = false;
        }
    }

    @Override
    public void onDestroy() {
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "onDestroy");
        }
        releaseOnDestroy();
        super.onDestroy();
    }

    public void releaseOnDestroy() {
        if (HomeLiveManager.getInstance().isPlaying()) {
            HomeLiveManager.getInstance().stopListen();
        }
        if (HomeLiveManager.getInstance().isRecording()) {
            HomeLiveManager.getInstance().stopRecord(true);
        }
        if (NetworkUtil.isNetworkAvailableWidthDefaultToast(getContext())) {
            stopLoopLiveStatus();
        }
        HomeLiveManager.getInstance().removeRecorderStatusObserver(mRecorderStatusObserver);

        stopRecordTimer();
        stopForecastTimer();
        HomeLiveManager.getInstance().resetStatus();
        HomeLiveManager.getInstance().setPresenter(null);
        ComponentUtil.removeObserver(UserComponentConst.NAME, mUserLoginComponent);
    }

    private void exitChatRoom() {
        mPresenter.exitChatRoom();
        clearChatRoomQueue();
    }

    private void clearChatRoomQueue() {
        mMemberEnterQueue.clear();
        mMessageReceiveQueue.clear();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
                                           @NonNull int[] grantResults) {
        switch (requestCode) {
            case CODE_PERMISSION_REQUEST:
                if (grantResults.length > 0
                        && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    startRecord();
                } else {

                }
                break;
        }
    }

    private void startRecordWithPermissionCheck() {
        List<String> needPermission = new ArrayList<String>();
        boolean needRecordAudio = ContextCompat.checkSelfPermission(getContext(),
                Manifest.permission.RECORD_AUDIO) != PackageManager.PERMISSION_GRANTED;
        if (needRecordAudio) {
            needPermission.add(Manifest.permission.RECORD_AUDIO);
        }
        boolean needReadExternal = ContextCompat.checkSelfPermission(getContext(),
                Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED;
        if (needReadExternal) {
            needPermission.add(Manifest.permission.READ_EXTERNAL_STORAGE);
        }
        boolean needWriteExternal = ContextCompat.checkSelfPermission(getContext(),
                Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED;
        if (needWriteExternal) {
            needPermission.add(Manifest.permission.WRITE_EXTERNAL_STORAGE);
        }
        if (needPermission.size() > 0) {
            String[] requestPermissions = new String[needPermission.size()];
            for (int i = 0; i < needPermission.size(); i++) {
                requestPermissions[i] = needPermission.get(i);
            }
            ActivityCompat.requestPermissions(getActivity(), requestPermissions,
                    CODE_PERMISSION_REQUEST);
        } else {
            startRecord();
        }
    }

    private void startRecord() {
        long delta = SystemClock.elapsedRealtime() - mLastRecordTime;
        if (delta < DURATION_LONG) {
            if (LivePresenter.DEBUG_LIVE) {
                Log.i(TAG, "startRecord but period to last recording is too short");
            }
            return;
        }
        //recordAndSetSystemVolume();
        //解除语音助手对麦克风的占用
//        IASRControl ias = (IASRControl) ARouter.getInstance()
//                .build(RouterFragmentConstants.FRAG_KAOLA_VOICE_ASSISTANT_CONTROL).navigation();
//        ias.stopASR();
        HomeLiveManager.getInstance().startRecordDeal();
    }

    private void startForecastTimer(long startTime, long serverTime) {
        if (mForecastTimer != null) {
            mForecastTimer.cancel();
            mForecastTimer = null;
        }
        long millisInFuture = startTime - serverTime;
        if (millisInFuture > 0) {
            refreshStatusLayout.setVisibility(View.GONE);
            mLiveNotStartLayout.setVisibility(View.VISIBLE);
            mForecastTimer = new CountDownTimer(millisInFuture, 1000) {
                @Override
                public void onTick(long millisUntilFinished) {
                    mForecastText.setText(generateForestString(millisUntilFinished));
                }

                @Override
                public void onFinish() {
                    mLiveNotStartLayout.setVisibility(View.GONE);
                    refreshStatusLayout.setVisibility(View.VISIBLE);
                    mPresenter.getLiveInfo(mProgramId);
                }
            };
            mForecastTimer.start();
        } else {
            mLiveNotStartLayout.setVisibility(View.GONE);
            //此状态下主站正在对直播进行审核，所以status为直播中但是服务器时间>直播开始时间
            refreshStatusLayout.setVisibility(View.VISIBLE);
        }
    }

    private String generateForestString(long period) {
        int seconds = (int) (period / 1000);
        int hour = seconds / (60 * 60);
        int left = seconds % (60 * 60);
        int minute = left / 60;
        int second = left % 60;
        String minuteStr = null;
        if (minute < 10) {
            minuteStr = "0" + minute;
        } else {
            minuteStr = String.valueOf(minute);
        }
        String secondStr = null;
        if (second < 10) {
            secondStr = "0" + second;
        } else {
            secondStr = String.valueOf(second);
        }
        String ret = String.format(mForecastString, hour, minuteStr, secondStr);
        return ret;
    }

    /**
     * 录音倒计时，到时间后自动结束录音
     */
    private void startCountdownTimer() {
        if (mRecordTimer != null) {
            mRecordTimer.cancel();
            mRecordTimer = null;
        }
        String s = getResources().getString(R.string.online_player_live_second);

        recordButtonBoxLayout.updateRecordText(RECORD_DURATION / 1000 + s);

        mRecordTimer = new CountDownTimer(RECORD_DURATION, RECORD_COUNTDOWN) {
            @Override
            public void onTick(long millisUntilFinished) {
                recordButtonBoxLayout.updateRecordText((millisUntilFinished / 1000) + s);
            }

            @Override
            public void onFinish() {
                stopRecord();
            }
        };
        mRecordTimer.start();
    }

    private void startListenTimer() {
        recordButtonBoxLayout.startListenTimer();
    }

    private void stopListenTimer() {
        recordButtonBoxLayout.stopListenTimer();
    }

    private void stopRecord() {
        String path = HomeLiveManager.getInstance().stopRecord();
        String recordFile = HomeLiveManager.getInstance().getFilePath();
        //restoreSystemVolume();
        //让语音助手继续监听麦克风
//        IASRControl ias = (IASRControl) ARouter.getInstance()
//                .build(RouterFragmentConstants.FRAG_KAOLA_VOICE_ASSISTANT_CONTROL).navigation();
//        ias.startASR();
        stopRecordTimer();
        if (recordFile != null && recordFile.equals(path)) {
            startRecordFinishAnim();
        } else if (HomeLiveManager.RECORD_TIME_TOO_SHORT.equals(path)) {
            ToastUtil.showNormal(getContext(), R.string.online_player_live_speak_too_short);
        }

        PlayerManagerHelper.getInstance().play(true);
    }

    private void cancelRecord() {
        if (!HomeLiveManager.getInstance().isRecording()) return;
        stopRecordTimer();
        stopRecordingAnim(false);
//        mRecordButtonText.setVisibility(View.INVISIBLE);
        HomeLiveManager.getInstance().stopRecord(true);
//        IASRControl ias = (IASRControl) ARouter.getInstance()
//                .build(RouterFragmentConstants.FRAG_KAOLA_VOICE_ASSISTANT_CONTROL).navigation();
//        ias.startASR();
    }

    /**
     * 录音正在进行动画，是一个圆在不断地放大-缩小
     */
    private void startRecordFinishAnim() {
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "startRecordFinishAnim");
        }
        if (HomeLiveManager.getInstance().getLiveStatus() != LiveStatus.LIVING) {
            if (LivePresenter.DEBUG_LIVE) {
                Log.i(TAG, "startRecordFinishAnim but live status is not live, return");
            }
            return;
        }
        recordButtonBoxLayout.startRecordFinishAnim();
    }

    private void sendMessage() {
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "sendMessage");
        }
        //可上传状态有：录音完成FINISHED，试听完成LISTENED，正在试听LISTENING, 上传失败FAILURE
        //其他状态：录音中RECORDING，空闲IDLE，正在上传UPLOADING，上传完成UPLOADED则不能上传
        //以此避免无效上传, 但这个判断在按钮点击的时候已经处理了，这里留个备忘
//        if (HomeLiveManager.getInstance().isListened() || HomeLiveManager.getInstance().isPlaying() ||
//                HomeLiveManager.getInstance().isFinished() || HomeLiveManager.getInstance().isFailure()) {
//        }
        stopListen();
        String uid = (String) ComponentClient.obtainBuilder(UserComponentConst.NAME)
                .setActionName(UserComponentConst.GET_USER_ID)
                .build().call().getData().get(UserComponentConst.GET_USER_ID);

        int timeLength = HomeLiveManager.getInstance().getRecordDuration();
        String uploadFileName = RecordUploadHelper.generateFileUploadName();
        String nickName = mPresenter.getNickName();
        try {
            nickName = URLEncoder.encode(nickName, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        RecordUploadHelper.UploadParam param = new RecordUploadHelper.UploadParam(mProgramId, uid, nickName
                , timeLength, uploadFileName);
        mPresenter.sendAudioMessageToServer(getContext(), param, uploadFileName);
//        mRecordButtonImage.setProgress(0);
    }

    private void recordAndSetSystemVolume() {
        //音量设为0
        AudioManager am = (AudioManager) getContext().getSystemService(Context.AUDIO_SERVICE);
        mSystemVolume = am.getStreamVolume(AudioManager.STREAM_MUSIC);
        am.setStreamVolume(AudioManager.STREAM_MUSIC, 0, AudioManager.FLAG_PLAY_SOUND);
    }

    private void restoreSystemVolume() {
        //还原系统音量
        AudioManager am = (AudioManager) getContext().getSystemService(Context.AUDIO_SERVICE);
        am.setStreamVolume(AudioManager.STREAM_MUSIC, mSystemVolume, AudioManager.FLAG_PLAY_SOUND);
    }

    private boolean requestAudioFocus() {
        if (mKRadioAudioFocusInter == null) {
            mKRadioAudioFocusInter = ClazzImplUtil.getInter("KRadioAudioFocusImpl");
        }

        if (mKRadioAudioFocusInter != null) {
            // 解决https://app.huoban.com/tables/2100000007530121/items/2300001158191064?userId=1881599问题
            boolean flag = mKRadioAudioFocusInter.requestAudioFocus(mAudioFocusListener);
            return flag;
        }
        AudioManager am = (AudioManager) getContext().getSystemService(Context.AUDIO_SERVICE);
        boolean status = AudioManager.AUDIOFOCUS_REQUEST_GRANTED ==
                am.requestAudioFocus(mAudioFocusListener, AudioManager.STREAM_MUSIC,
                        AudioManager.AUDIOFOCUS_GAIN);
        Log.i(TAG, "live requestAudioFocus status:" + status);
        return status;
    }

    private boolean abandonAudioFocus() {
        if (mKRadioAudioFocusInter != null) {
            boolean flag = mKRadioAudioFocusInter.abandonAudioFocus(mAudioFocusListener);
            return flag;
        }
        AudioManager am = (AudioManager) getContext().getSystemService(Context.AUDIO_SERVICE);
        boolean status = AudioManager.AUDIOFOCUS_REQUEST_GRANTED ==
                am.abandonAudioFocus(mAudioFocusListener);
        Log.i(TAG, "live abandonAudioFocus status:" + status);
        return status;
    }

    private AudioManager.OnAudioFocusChangeListener mAudioFocusListener
            = new AudioManager.OnAudioFocusChangeListener() {
        @Override
        public void onAudioFocusChange(int focusChange) {
            if (focusChange == AudioManager.AUDIOFOCUS_LOSS_TRANSIENT) {
                Log.i(TAG, "OnAudioFocusChangeListener:AUDIOFOCUS_LOSS_TRANSIENT");
            } else if (focusChange == AudioManager.AUDIOFOCUS_LOSS) {
                Log.i(TAG, "OnAudioFocusChangeListener:AUDIOFOCUS_LOSS");
            } else if (focusChange == AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK) {
                Log.i(TAG, "OnAudioFocusChangeListener:AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK");
            } else if (focusChange == AudioManager.AUDIOFOCUS_GAIN) {
                Log.i(TAG, "OnAudioFocusChangeListener:AUDIOFOCUS_GAIN");
            }
        }
    };

    private void stopRecordTimer() {
        if (mRecordTimer != null) {
            mRecordTimer.cancel();
            mRecordTimer = null;
        }
    }


    private void stopForecastTimer() {
        if (mForecastTimer != null) {
            mForecastTimer.cancel();
            mForecastTimer = null;
        }
    }

    private void startListen() {
        long delta = SystemClock.elapsedRealtime() - mLastListenTime;
        if (delta < DURATION_LONG) {
            if (LivePresenter.DEBUG_LIVE) {
                Log.i(TAG, "startRecord but period to last listening is too short");
            }
            return;
        }
        if (requestAudioFocus()) {
            HomeLiveManager.getInstance().startListen();
        }
    }

    private void stopListen() {
        HomeLiveManager.getInstance().stopListen();
        stopListenTimer();
    }

    private void cancel() {
        HomeLiveManager.getInstance().cancel();
        recordButtonBoxLayout.notifyCancel();
    }

    private void startRecordingAnim() {
        recordButtonBoxLayout.notifyStartRecord();
    }

    private void stopRecordingAnim(boolean isRecordFinish) {
        recordButtonBoxLayout.stopRecordingAnim(isRecordFinish);
    }

    private void updateLastRecordTime() {
        mLastRecordTime = SystemClock.elapsedRealtime();
    }

    private void updateLastListenTime() {
        mLastListenTime = SystemClock.elapsedRealtime();
    }

    /**
     * 在录音-上传过程中，随着状态的变化面更新界面
     */
    private Observer mRecorderStatusObserver = new Observer() {
        @Override
        public void update(Observable o, Object arg) {
            //录音相关状态的变化只有在直播过程中体现
            if (HomeLiveManager.getInstance().getLiveStatus() != LiveStatus.LIVING) {
                if (LivePresenter.DEBUG_LIVE) {
                    Log.i(TAG, "mRecorderStatusObserver update but live status is not live");
                }
                return;
            }
            RecorderStatus status = (RecorderStatus) arg;
            if (LivePresenter.DEBUG_LIVE) {
                Log.i(TAG, "RecorderStatusObserver update: " + status);
            }
            switch (status) {
                case CANCEL:
                    updateLastRecordTime();
                    break;
                case IDLE:
                    showRecordIdle();
                    break;
                case RECORDING:
                    startCountdownTimer();
                    showRecording();
                    break;
                case LISTENING:
                    requestAudioFocus();
                    startListenTimer();
                    break;
                case LISTENED:
                    stopListenTimer();
                    abandonAudioFocus();
                    updateLastListenTime();
                    break;
                case FINISHED:
                    showRecordFinished();
                    break;
                case UPLOADING:
                    showRecordUploading();
                    break;
                case UPLOADED:
                    showRecordUploaded();
                    break;
                case FAILURE:
                    showRecordUploadAgainAsFailure();
                    break;

            }
        }
    };

    private void showRecordIdle() {
        stopRecordingAnim(true);
        recordButtonBoxLayout.showRecordIdle();
//        mRecordButtonImage.setDrawProgress(false);
//        mRecordButtonImage.setImageDrawable(SkinCompatResources.getDrawableCompat(getContext(), R.drawable.live_leave_a_message));
//        mSpeakImage.setVisibility(View.VISIBLE);
//        mLoginPromptText.setVisibility(View.VISIBLE);
//        mRecordButtonText.setVisibility(View.INVISIBLE);
//        mLoginPromptText.setText(R.string.live_click_to_speak);
        abandonAudioFocus();
    }

    private void showRecordFinished() {
//        mRecordButtonText.setText(R.string.live_send_message);
//        mLoginPromptText.setVisibility(View.INVISIBLE);
//        mRecordButtonImage.setImageDrawable(SkinCompatResources.getDrawableCompat(getContext(), R.drawable.live_send_message_selector));
//        mRecordButtonImage.setVisibility(View.VISIBLE);
        recordButtonBoxLayout.updateRecordText("");
        stopRecordingAnim(true);
        abandonAudioFocus();
        updateLastRecordTime();
    }

    private void showRecording() {
//        mRecordButtonText.setVisibility(View.VISIBLE);
//        mSpeakImage.setVisibility(View.GONE);
//        mLoginPromptText.setText(R.string.live_click_to_finish);
//        // mRecordButtonImage.setImageResource(R.drawable.live_leave_message_recording);
//        mRecordButtonImage.setImageDrawable(SkinCompatResources.getDrawableCompat(getContext(), R.drawable.live_leave_message_recording));
        startRecordingAnim();
        requestAudioFocus();
    }

    private void showRecordUploading() {
        recordButtonBoxLayout.showRecordUploading();
//        mRecordButtonText.setText(R.string.live_uploading);
//        mLoginPromptText.setVisibility(View.GONE);
//        mRecordButtonImage.setDrawProgress(true);
//        mRecordButtonImage.setVisibility(View.VISIBLE);
    }

    private void showRecordUploaded() {
//        mRecordButtonImage.setDrawProgress(false);
////        mRecordButtonImage.setImageResource(R.drawable.live_message_send_success);
//        mRecordButtonImage.setImageDrawable(SkinCompatResources.getDrawableCompat(getContext(), R.drawable.live_message_send_success));
//        mRecordButtonImage.setVisibility(View.VISIBLE);
//        mRecordButtonText.setVisibility(View.INVISIBLE);
        recordButtonBoxLayout.showRecordUploaded();
        showChatMessageSent(createMineMessage());
    }

    private void showRecordUploadAgainAsFailure() {
//        mRecordButtonText.setText(R.string.live_upload_again);
//        mLoginPromptText.setVisibility(View.GONE);
//        mRecordButtonImage.setDrawProgress(false);
////        mRecordButtonImage.setImageResource(R.drawable.live_send_failure_selector);
//        mRecordButtonImage.setImageDrawable(SkinCompatResources.getDrawableCompat(getContext(), R.drawable.live_send_failure_selector));
//        mRecordButtonImage.setVisibility(View.VISIBLE);
        recordButtonBoxLayout.showRecordUploadAgainAsFailure();
    }


    private ArrayList<MessageBean> createMineMessage() {
        MessageBean mb = new MessageBean();
        String nickName = mPresenter.getNickName();
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "createMineMessage nickName: " + nickName);
        }
        if (nickName == null || "null".equals(nickName)) {
            nickName = getResources().getString(R.string.online_player_live_nickname_me);
        }
        mb.nickName = nickName;
        mb.sendChatMsgData = new SendChatMsgData();
        mb.sendChatMsgData.file = HomeLiveManager.getInstance().getFilePath();
        mb.sendChatMsgData.sessionId = NimManager.getInstance().getRoomId();
        mb.sendChatMsgData.duration = HomeLiveManager.getInstance().getRecordDuration();
        ArrayList<MessageBean> list = new ArrayList<>(1);
        list.add(mb);
        return list;
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.live_cancel_button_layout) {
            cancel();
        } else if (id == R.id.live_listen_button_layout) {
            if (HomeLiveManager.getInstance().isPlaying()) {
                stopListen();
            } else if (HomeLiveManager.getInstance().isFinished()) {
                startListen();
            } else if (HomeLiveManager.getInstance().isListened() || HomeLiveManager.getInstance().isFailure()) {
                startListen();
            }
        } else if (id == R.id.recordIv || id == R.id.recordTextViewParent) {
            boolean isUserLogin = OnlinePlayerLiveUtil.showLoginIfNotLogin(this);
            if (isUserLogin) {
                if (HomeLiveManager.getInstance().isRecording()) {
                    stopRecord();
                } else if (HomeLiveManager.getInstance().isIdle()) {
                    startRecordWithPermissionCheck();
                } else if (HomeLiveManager.getInstance().isUploading()) {
                    //
                } else if (HomeLiveManager.getInstance().isUploaded()) {
                    HomeLiveManager.getInstance().setRecorderStatus(RecorderStatus.IDLE);
                } else {
                    sendMessage();
                }
            }
        }
    }

    @Override
    public void onHistoryMessageReceived(List<MessageBean> param, boolean isOld) {
        isLoadingHistory = false;
        if (mMessageAdapter != null) {
            int startPosition = 0;
            if (!isOld) {
                startPosition = mMessages.size();
            }
            MessageBean messageBean;
            for (int i = 0; i < param.size(); i++) {
                messageBean = param.get(i);
                mMessages.add(startPosition + i, new SystemMessage(SystemMessage.MessageType.RECEIVE, null, messageBean));
            }
            mMessageAdapter.notifyItemRangeInserted(startPosition, param.size());
            if (isOld && isFirstLoadHistory.compareAndSet(true, false)) {
                recyclerView.scrollToPosition(mMessages.size() - 1);
                initScrollListener();
                recyclerView.addOnScrollListener(mScrollListener);
            }
        }
    }

    private void initScrollListener() {
        if (mScrollListener == null) {
            mScrollListener = new RecyclerView.OnScrollListener() {
                @Override
                public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
                    super.onScrollStateChanged(recyclerView, newState);
                }

                @Override
                public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                    super.onScrolled(recyclerView, dx, dy);
                    if (!recyclerView.canScrollVertically(-1) && !ListUtil.isEmpty(mMessages)) {
                        for (SystemMessage mMessage : mMessages) {
                            MessageBean message = mMessage.getMessage();
                            if (message == null) {
                                continue;
                            }
                            if (isLoadingHistory) return;
                            isLoadingHistory = true;
                            return;
                        }
                    }
                }
            };
        }
    }

    @Override
    public void onHistoryMessageQueryFailed(int code, Throwable exception) {
        Log.e(TAG, "查询历史消息失败：code=" + code + ",exception=" + exception);
        isLoadingHistory = false;
    }

    @Override
    public void onChatRoomMemberReceived(List<ChatUserInfo> users) {
        userHeaderParentView.removeAllViews();
        mOnlineMembers.clear();
        ChatUserInfo userInfo;
        if (!ListUtil.isEmpty(users)) {
            for (int i = 0; i < users.size(); i++) {
                if (mOnlineMembers.size() >= 5) break;
                userInfo = users.get(i);
                if (!containsMember(userInfo)) {
                    addHeaderToParent(userInfo, i * ResUtil.getDimen(R.dimen.x24));
                    mOnlineMembers.add(userInfo);
                }
            }
            if (mOnlineMembers.size() >= 5) {
                addMoreHeaderTpParent();
            }
        }
    }

    private boolean containsMember(ChatUserInfo userInfo) {
        for (ChatUserInfo mOnlineMember : mOnlineMembers) {
            if (mOnlineMember.getUid() != null && mOnlineMember.getUid().equals(userInfo.getUid()))
                return true;
        }
        return false;
    }

    /**
     * 添加用户头像
     *
     * @param userInfo
     * @param marginStart
     */
    private void addHeaderToParent(ChatUserInfo userInfo, int marginStart) {
        StrokeRoundImageView circleImageView = new StrokeRoundImageView(getContext());
        RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(ResUtil.getDimen(R.dimen.m40), ResUtil.getDimen(R.dimen.m40));
        params.setMarginStart(marginStart);
        userHeaderParentView.addView(circleImageView, params);
        RequestOptions options = new RequestOptions()
                .placeholder(R.drawable.online_player_default_header)                //加载成功之前占位图
                .error(R.drawable.online_player_default_header);                //加载错误之后的错误图
        Glide.with(this).load(userInfo.getAvatar()).apply(options).into(circleImageView);
        if (!LiveUtil.isUserBound() && StringUtil.isEmpty(userInfo.getUserName())) {
            //如果没有登录，则应记录游客头像的位置，登录的时候根据位置删除该头像
            this.mineHeaderViewPosition = userHeaderParentView.getChildCount() - 1;
        }
    }

    private void addMoreHeaderTpParent() {
        RoundImageView circleImageView = new RoundImageView(getContext());
        RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(ResUtil.getDimen(R.dimen.x40), ResUtil.getDimen(R.dimen.y40));
        params.setMarginStart(5 * ResUtil.getDimen(R.dimen.x24));
        userHeaderParentView.addView(circleImageView, params);
        Glide.with(this).load(R.drawable.online_player_more_member).into(circleImageView);
    }


    private void removeMineHeaderView() {
        if (this.mineHeaderViewPosition >= 0 && userHeaderParentView.getChildCount() > this.mineHeaderViewPosition) {
            userHeaderParentView.removeViewAt(this.mineHeaderViewPosition);
            this.mineHeaderViewPosition = -1;
            if (mPresenter.isChatRoomEntered()) {
                mPresenter.fetchRoomMembers();
            }
        }
    }

    @Override
    public void onChatRoomMemberQueryFailed(int code, Throwable exception) {

    }

    @Override
    public void enterChatRoomSuccess() {
        mPresenter.fetchRoomMembers();
    }

    @Override
    public void enterChatRoomFailed() {

    }

    private class UserLoginComponent implements DynamicComponent {

        @Override
        public String getName() {
            return "UserLoginComponent$LiveBarrageFragment";
        }

        @Override
        public boolean onCall(RealCaller caller) {
            String actionName = caller.actionName();
            if (UserStateObserverProcessorConst.USER_LOGIN.equals(actionName)) {
                if (mPresenter.isChatRoomEntered()) {
                    clearNimStatus();
                    removeMineHeaderView();
                }
            }
            if (UserStateObserverProcessorConst.USER_LOGOUT.equals(actionName) || UserStateObserverProcessorConst.USER_LOGIN.equals(actionName)) {
                initHomeLiveManager();
            }
            return false;
        }
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (ResUtil.getOrientation() == Configuration.ORIENTATION_LANDSCAPE) {
//            uploadView(true);
        } else {
//            uploadView(false);
        }
    }

    private void initViewInner() {
        int mCurrentOrientation = ResUtil.getOrientation();
        if (mCurrentOrientation == Configuration.ORIENTATION_PORTRAIT) {
//            uploadView(false);
        } else if (mCurrentOrientation == Configuration.ORIENTATION_LANDSCAPE) {
//            uploadView(true);
        }
    }

    // 解决https://app.huoban.com/tables/2100000007530121/items/2300001291113548?userId=1229522问题
    @Override
    protected void changeViewLayoutForStatusBar(View view) {
        if (mKRadioFragmentTransStatusBarAdapterInter != null && mKRadioFragmentTransStatusBarAdapterInter.doFragmentTransStatusBar()) {
            // 解决https://app.huoban.com/tables/2100000007530121/items/2300001416647090?userId=1229522问题
            super.changeViewLayoutForStatusBar(view);
        } else {
//            int height = ScreenUtil.getStatusBarHeight();
//            if (height <= 0) {
//                height = ResUtil.getDimen(R.dimen.y24);
//            }
//            if (height > 0) {
//                ConstraintLayout.LayoutParams lp = (ConstraintLayout.LayoutParams) mMinimumImage.getLayoutParams();
//                lp.topMargin = height;
//                mMinimumImage.setLayoutParams(lp);
//                lp = (ConstraintLayout.LayoutParams) mLiveNameText.getLayoutParams();
//                lp.topMargin = height;
//                mLiveNameText.setLayoutParams(lp);
//                lp = (ConstraintLayout.LayoutParams) mStopLiveImage.getLayoutParams();
//                lp.topMargin = height;
//                mStopLiveImage.setLayoutParams(lp);
//            }
        }
    }

    private void stopLive() {
        if (PlayerManagerHelper.getInstance().isLivingPlayer()) {
            PlayerManagerHelper.getInstance().pause(false);
            PlayerManager.getInstance().stop(false);
            PlayerManager.getInstance().reset();
            HomeLiveManager.getInstance().onLiveExit(false);
//            exitChatRoom();
            bPlayerEnabled = false;
        }
    }

    private void stopLoopLiveStatus() {
        if (PlayerManagerHelper.getInstance().isLivingPlayer()) {
//            PlayerManager.getInstance().reset();
//            HomeLiveManager.getInstance().onLiveExit(true);
            HomeLiveManager.getInstance().stopLoopLiveStatus();
            exitChatRoom();
            bPlayerEnabled = false;
        }
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        if (!hidden) {
            PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
            if (playItem.getType() != PlayerConstants.RESOURCES_TYPE_LIVING ||
                    playItem.getType() != PlayerConstants.RESOURCES_TYPE_BROADCAST) {
                pop();
            }
        }
    }

    @Override
    public void initArgs() {
        super.initArgs();
        if (getArguments() != null) {
            mProgramId = getArguments().getLong(PROGRAM_ID, 0);
        }
    }

    /**
     * 获取在线广播节目详情
     */
    public void getBroadcastProgramDetails(long programid) {
        new BroadcastRequest().getBroadcastProgramDetails(programid, new HttpCallback<ProgramDetails>() {

            @Override
            public void onSuccess(ProgramDetails programDetails) {
//                tvDetails.setText(mGson.toJson(programDetails));
                Log.e(TAG, "获取在线广播节目详情\n" + new Gson().toJson(programDetails));
            }

            @Override
            public void onError(ApiException exception) {
//                tvDetails.setText(mGson.toJson(exception));
                Log.e(TAG, "获取在线广播节目详情失败\n" + new Gson().toJson(exception));
            }
        });

    }

    @Override
    public boolean useEventBus() {
        return true;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void updateLivingProgramId(BusEventPlayingProgramIdChanged playingProgramIdChanged) {
//        this.mProgramId = playingProgramIdChanged.getProgramId();
    }
 
}