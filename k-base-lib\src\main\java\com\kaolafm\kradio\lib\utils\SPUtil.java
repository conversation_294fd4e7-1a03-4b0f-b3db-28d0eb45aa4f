package com.kaolafm.kradio.lib.utils;

import android.content.Context;
import android.content.SharedPreferences;

/**
 * 这个是用来替换SharedPreferenceUtil，因为那个类有一定的逻辑问题
 */
public class SPUtil {
    public static final String DEFAULT_FILE = "sp_default";
    public static final String SP_ONLY_ID = "onlyId";

    public static void putBoolean(Context context, SharedPreferences sp, String key, boolean value) {
        if (sp == null) {
            sp = getSharedPreferences(context, DEFAULT_FILE);
        }
        sp.edit().putBoolean(key, value).apply();
    }

    public static boolean getBoolean(Context context, SharedPreferences sp, String key, boolean defValue) {
        if (sp == null) {
            sp = getSharedPreferences(context, DEFAULT_FILE);
        }
        return sp.getBoolean(key, defValue);
    }

    public static void putString(Context context, SharedPreferences sp, String key, String value) {
        if (sp == null) {
            sp = getSharedPreferences(context, DEFAULT_FILE);
        }
        sp.edit().putString(key, value).apply();
    }

    public static String getString(Context context, SharedPreferences sp, String key, String defValue) {
        if (sp == null) {
            sp = getSharedPreferences(context, DEFAULT_FILE);
        }
        return sp.getString(key, defValue);
    }

    public static SharedPreferences getSharedPreferences(Context context, String spName) {
        if (spName == null){
            spName = DEFAULT_FILE;
        }
        return context.getSharedPreferences(spName, Context.MODE_PRIVATE);
    }
}
