<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="OvalFlipSeekBar">

        <!--进度值 介于0-100-->
        <attr name="of_progress" format="integer" />
        <!--线条宽度-->
        <attr name="of_progress_width" format="dimension" />
        <!--起始角度-->
        <attr name="of_start_angle" format="integer" />
        <!--进度在圆上的起始角度-->
        <attr name="of_seek_start_angle" format="integer" />
        <!--扫过的角度-->
        <attr name="of_sweep_angle" format="integer" />
        <!--下半圆进度条的角度-->
        <attr name="of_progress_sweep_angle" format="integer" />
        <!--下半圆缺口的角度-->
        <attr name="of_gap_angle" format="integer" />
        <!--滑块的资源-->
        <attr name="of_thumb" format="reference" />
        <!--滑块的大小-->
        <attr name="of_thumb_size" format="dimension" />
    </declare-styleable>

    <declare-styleable name="YunTingProgressBar">
        <attr name="ytpbMaxProgress" format="float" />
        <attr name="ytpbProgress" format="float" />
        <attr name="ytpbStartAngleToLeftHorizontal" format="float" />
        <attr name="ytpbGapAngle" format="float" />
        <attr name="ytpbThumbSrc" format="reference" />
        <attr name="ytpbThumbSize" format="dimension" />
        <attr name="ytpbThumbScaleMultipleWhenTouch" format="float" />
        <attr name="ytpbUseSmoothTouch" format="boolean" />
        <attr name="ytpbShortUnProgress" format="boolean" />
        <attr name="ytpbEnableClickToSeek" format="boolean" />
    </declare-styleable>

    <attr name="srlTextColorTitle" format="color|reference" />
    <attr name="srlTextColorTime" format="color|reference" />
    <attr name="srlProgressColor" format="color|reference" />
    <attr name="srlArrowColor" format="color|reference" />
    <declare-styleable name="ColorSettableClassicsHeader">
        <attr name="srlTextColorTitle" />
        <attr name="srlTextColorTime" />
        <attr name="srlProgressColor" />
        <attr name="srlArrowColor" />
    </declare-styleable>
    <declare-styleable name="ColorSettableClassicsFooter">
        <attr name="srlTextColorTitle" />
        <attr name="srlProgressColor" />
        <attr name="srlArrowColor" />
    </declare-styleable>
</resources>