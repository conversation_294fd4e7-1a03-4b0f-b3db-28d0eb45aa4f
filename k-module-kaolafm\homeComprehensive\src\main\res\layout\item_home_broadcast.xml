<?xml version="1.0" encoding="utf-8"?>
<com.kaolafm.kradio.lib.widget.ratio.RatioConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:canScale="true"
    app:wh_ratio="1:1">

<!--    必须有一个显示的子view是match，不然会出现设置空隙(offset)不起作用-->

    <View
        android:id="@+id/view_item_home_playing"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_item_corner"
        />
    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/gl_item_guide"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintGuide_percent="0.43"
        android:orientation="horizontal"
        />

    <TextView
        android:id="@+id/tv_item_home_broadcast_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:lines="2"
        android:maxEms="6"
        android:gravity="center_horizontal"
        android:textColor="@color/red01"
        android:textSize="@dimen/home_item_broadcast_text_size"
        android:layout_marginLeft="@dimen/x14"
        android:layout_marginRight="@dimen/x14"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/gl_item_guide"
        tools:text="北京音乐广播北京音乐广播" />

</com.kaolafm.kradio.lib.widget.ratio.RatioConstraintLayout>


