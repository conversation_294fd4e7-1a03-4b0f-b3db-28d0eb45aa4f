package com.kaolafm.kradio.lib.bean;

import com.google.gson.Gson;
import com.kaolafm.opensdk.api.CrashMessageTypeDataBean;

import org.greenrobot.greendao.converter.PropertyConverter;

public class CrashMessageTypeDataBeanConverter implements PropertyConverter<CrashMessageTypeDataBean, String> {

    private static Gson gson = new Gson();
    @Override
    public CrashMessageTypeDataBean convertToEntityProperty(String databaseValue) {
        return gson.fromJson(databaseValue, CrashMessageTypeDataBean.class);
    }

    @Override
    public String convertToDatabaseValue(CrashMessageTypeDataBean entityProperty) {
        return gson.toJson(entityProperty);
    }
}
