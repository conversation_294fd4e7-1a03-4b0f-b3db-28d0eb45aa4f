package com.kaolafm.kradio.lib.utils.imageloader;

import android.content.Context;
import android.graphics.drawable.Drawable;
import androidx.fragment.app.Fragment;
import android.widget.ImageView;
import com.bumptech.glide.load.resource.bitmap.BitmapTransformation;
import com.kaolafm.kradio.lib.utils.imageloader.listener.OnGetBitmapListener;
import com.kaolafm.kradio.lib.utils.imageloader.listener.OnGetDrawableListener;
import com.kaolafm.kradio.lib.utils.imageloader.listener.OnImageLoaderListener;

/**
 * <AUTHOR>
 * @date 2018/4/15
 */

public interface BaseImageLoader extends BaseInterface{

    void displayImage(Context context, ImageConfigImpl.Builder builder);

    void displayImage(Context context, int resId, ImageView imageView);

    void displayImage(Context context, int resId, ImageView imageView, int size);

    void displayImage(Context context, int resId, ImageView imageView, boolean isCenterCrop);

    void displayImageAsBitmap(Context context, int resId, ImageView imageView);

    void displayImageAsGif(Context context, int resId, ImageView imageView);

    void displayImage(Context context, String url, ImageView imageView);

    void displayImageNotCenterCrop(Context context, String url, ImageView imageView);

    void displayImage(Context context, String url, ImageView imageView, boolean isCache);

    void displayImage(Fragment fragment, String url, ImageView imageView);

    void displayImage(Context context, String url, ImageView imageView, Drawable defRes);

    void displayImage(Fragment fragment, String url, ImageView imageView, Drawable defRes);

    void displayImage(Context context, String url, ImageView imageView, BitmapTransformation... transformations);

    void displayImage(Context context, String url, ImageView imageView, OnImageLoaderListener listener, BitmapTransformation... transformations);

    void displayImage(Fragment fragment, String url, ImageView imageView, BitmapTransformation... transformations);

    void displayImage(Context context, String url, ImageView imageView, Drawable defRes, BitmapTransformation... transformations);

    void displayImage(Fragment fragment, String url, ImageView imageView, Drawable defRes, BitmapTransformation... transformations);

    void displayImage(Context context, String url, ImageView imageView, Drawable defRes, int size);

    void displayImage(Fragment fragment, String url, ImageView imageView, Drawable defRes, int size);

    void displayImage(Context context, String url, ImageView imageView, Drawable defRes, boolean cacheInMemory);

    void displayImage(Fragment fragment, String url, ImageView imageView, Drawable defRes, boolean cacheInMemory);

    void displayImage(Context context, String url, ImageView imageView, OnImageLoaderListener listener);

    void displayImage(Context context, String url, ImageView imageView, OnImageLoaderListener listener, boolean isCenterCrop);

    void displayImage(Fragment fragment, String url, ImageView imageView, OnImageLoaderListener listener);

    void displayImage(Context context, String url, ImageView imageView, Drawable defRes, OnImageLoaderListener listener);

    void displayImage(Fragment fragment, String url, ImageView imageView, Drawable defRes, OnImageLoaderListener listener);

    void getBlurBitmapFromCache(Context context, String url, int blurRadius, OnGetBitmapListener listener);

    /**
     * 显示固定的大小，正方形
     * @param context
     * @param url
     * @param imageView
     * @param size
     */
    void displayImageFixedSize(Context context, String url, ImageView imageView, int size);

    /**
     * 显示固定的大小，正方形
     * @param fragment
     * @param url
     * @param imageView
     * @param size
     */
    void displayImageFixedSize(Fragment fragment, String url, ImageView imageView, int size);
    /**
     * 显示固定的大小，正方形
     * @param context
     * @param resId
     * @param imageView
     * @param size
     */
    void displayImageFixedSize(Context context, int resId, ImageView imageView, int size);

    void displayCircleImage(Context context, int resId, ImageView imageView);

    void displayCircleImage(Context context, String url, ImageView imageView);

    void displayCircleImage(Context context, String url, ImageView imageView, OnImageLoaderListener onImageLoaderListener);

    void displayCircleImageAsGif(Context context, String url, ImageView imageView, OnImageLoaderListener onImageLoaderListener);

    void displayCircleImage(Context context, String url, ImageView imageView, int borderWidth, int borderColor);

    void displayCircleImage(Context context, String url, ImageView imageView, int borderWidth, int borderColor, int size);

    void displayCircleImage(Context context, String url, ImageView imageView, int borderWidth, int borderColor, int size, Drawable def);

    void displayCircleImage(Context context, String url, ImageView imageView, Drawable defRes);

    void displayCircleImage(Fragment fragment, String url, ImageView imageView, Drawable defRes);

    void displayRoundImage(Context context, String url, ImageView imageView, int radius);

    void displayRoundImage(Context context, String url, ImageView imageView, Drawable defRes, int radius);

    void displayRoundImage(Fragment fragment, String url, ImageView imageView, Drawable defRes, int radius);

    void displayBlurImage(Context context, String url, int blurRadius, OnGetDrawableListener listener);

    void displayBlurImage(Context context, String url, ImageView imageView, Drawable defRes, int blurRadius);

    void displayBlurImage(Context context, int resId, ImageView imageView, int blurRadius);

    void displayBlurImage(Fragment fragment, String url, ImageView imageView, Drawable defRes, int blurRadius);

    void displayBlurImage(Context context, String url, ImageView imageView);

    void displayImageInResource(Context context, int resId, ImageView imageView);

    void displayImageInResource(Fragment fragment, int resId, ImageView imageView);

    void displayImageInResource(Context context, int resId, ImageView imageView, BitmapTransformation... transformations);

    void displayImageInResource(Fragment fragment, int resId, ImageView imageView, BitmapTransformation... transformations);

    void displayImageInResource(Context context, int resId, ImageView imageView, Drawable defRes);

    void displayImageInResource(Fragment fragment, int resId, ImageView imageView, Drawable defRes);

    void displayImageInResource(Context context, int resId, ImageView imageView, Drawable defRes, BitmapTransformation... transformations);

    void displayImageInResource(Fragment fragment, int resId, ImageView imageView, Drawable defRes, BitmapTransformation... transformations);

    void displayImage(Context context, String url, ImageView imageView,  boolean isCache, boolean isCenterCrop);
}
