package com.kaolafm.kradio.flavor.impl;

import android.view.KeyEvent;

import com.kaolafm.kradio.lib.base.flavor.KRadioConvertMediaKeyCodeInter;
import com.kaolafm.opensdk.player.logic.PlayerManager;

public class KRadioConvertMediaKeyCodeImpl implements KRadioConvertMediaKeyCodeInter {
    @Override
    public int convertKeyCode(int originKeycode) {
        if(originKeycode == KeyEvent.KEYCODE_MUHENKAN) {
            if(PlayerManager.getInstance().isPlaying()) {
                return KeyEvent.KEYCODE_MEDIA_PAUSE;
            } else {
                return KeyEvent.KEYCODE_MEDIA_PLAY;
            }
        }
        return originKeycode;
    }
}
