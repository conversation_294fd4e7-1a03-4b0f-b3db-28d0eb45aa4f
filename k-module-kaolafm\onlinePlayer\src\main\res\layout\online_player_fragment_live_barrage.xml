<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/liveBarrageRootView"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/icon_living"
        android:layout_width="0dp"
        android:layout_height="@dimen/y54"
        android:layout_marginStart="@dimen/x89"
        android:layout_marginTop="@dimen/y29"
        android:src="@drawable/online_player_living"
        android:visibility="gone"
        app:layout_constrainedWidth="true"
        app:layout_constraintDimensionRatio="140:54"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <RelativeLayout
        android:id="@+id/userHeaderParentView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/x13"
        app:layout_constraintBottom_toBottomOf="@id/icon_living"
        app:layout_constraintStart_toEndOf="@id/icon_living"
        app:layout_constraintTop_toTopOf="@id/icon_living"></RelativeLayout>

    <TextView
        android:id="@+id/live_listening_number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/x24"
        android:background="@drawable/online_player_live_barrage_listen_count"
        android:paddingStart="@dimen/x20"
        android:paddingTop="@dimen/y6"
        android:paddingEnd="@dimen/x20"
        android:paddingBottom="@dimen/y6"
        android:textColor="@color/online_player_live_text_color"
        android:textSize="@dimen/m20"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/icon_living"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintStart_toEndOf="@id/userHeaderParentView"
        app:layout_constraintTop_toTopOf="@id/icon_living"
        tools:text="123.5w" />

    <com.kaolafm.kradio.common.widget.KLRecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/y23"
        android:layout_marginBottom="@dimen/y28"
        android:fadingEdgeLength="@dimen/y72"
        android:requiresFadingEdge="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/recordButtonBoxLayout"
        app:layout_constraintTop_toBottomOf="@id/icon_living" />

    <com.kaolafm.kradio.online.player.views.RecordButtonBoxLayout
        android:id="@+id/recordButtonBoxLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/x114"
        android:layout_marginBottom="@dimen/y42"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="parent" />


    <FrameLayout
        android:id="@+id/live_screen_bullet_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/y20"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/icon_living" />


    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/live_image_third_guideline"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.65" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/live_not_start_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/x48"
        android:layout_marginEnd="@dimen/x48"
        android:background="@drawable/online_live_not_start_bg"
        android:paddingTop="@dimen/y20"
        android:paddingBottom="@dimen/y20"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/icon_living">

        <TextView
            android:id="@+id/live_not_start_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:text="@string/online_player_live_start_interval"
            android:textColor="@color/online_player_no_start_tip_color"
            android:textSize="@dimen/m24"
            app:layout_constraintBottom_toTopOf="@id/live_not_start_text"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/live_not_start_watch_icon"
            android:layout_width="@dimen/x32"
            android:layout_height="@dimen/y32"
            android:src="@drawable/online_living_watch"
            app:layout_constraintBottom_toBottomOf="@id/live_not_start_text"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/live_not_start_text"
            app:layout_constraintTop_toTopOf="@id/live_not_start_text" />

        <TextView
            android:id="@+id/live_not_start_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/x16"
            android:layout_marginTop="@dimen/y20"
            android:textColor="@color/online_player_live_text_color"
            android:textSize="@dimen/m36"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@id/live_not_start_bottom_tip"
            app:layout_constraintLeft_toRightOf="@id/live_not_start_watch_icon"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/live_not_start_tip"
            tools:text="10小时" />

        <TextView
            android:id="@+id/live_not_start_bottom_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/y20"
            android:text="@string/online_player_live_start_interval_bottom"
            android:textColor="@color/online_player_no_start_tip_color"
            android:textSize="@dimen/m24"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/live_not_start_text" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.kaolafm.kradio.component.ui.base.view.KradioTextView
        android:id="@+id/refresh_status_layout"
        android:layout_width="@dimen/m260"
        android:layout_height="@dimen/m72"
        android:layout_marginStart="@dimen/m192"
        android:background="@drawable/online_bg_login_btn"
        android:gravity="center"
        android:text="@string/online_player_live_listen_now"
        android:textColor="@color/online_player_live_text_color"
        android:textSize="@dimen/m24"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/icon_living">

    </com.kaolafm.kradio.component.ui.base.view.KradioTextView>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/live_finish_layout"
        android:layout_width="@dimen/x488"
        android:layout_height="@dimen/y120"
        android:layout_marginStart="@dimen/x48"
        android:layout_marginEnd="@dimen/x48"
        android:background="@drawable/online_live_not_start_bg"
        android:paddingTop="@dimen/y33"
        android:paddingBottom="@dimen/y33"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/icon_living">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/online_player_live_finished"
            android:textColor="@color/online_player_live_text_color"
            android:textSize="@dimen/m36"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/live_error_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/icon_living">

        <TextView
            android:id="@+id/live_error_tip1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/online_player_live_load_error"
            android:textColor="@color/online_player_live_text_color"
            android:textSize="@dimen/m28"
            app:layout_constraintBottom_toTopOf="@id/live_error_tip2"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/live_error_tip2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/y40"
            android:text="@string/online_player_live_go_listen_other"
            android:textColor="@color/online_player_listen_other_color"
            android:textSize="@dimen/m20"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/live_error_tip1" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/live_coming_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/live_image_third_guideline">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:text="@string/online_player_live_coming"
            android:textColor="@color/online_player_live_text_color"
            android:textSize="@dimen/m28"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.2789" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:text="@string/online_player_live_go_listen_other"
            android:textColor="@color/online_player_listen_other_color"
            android:textSize="@dimen/m20"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.4842" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>