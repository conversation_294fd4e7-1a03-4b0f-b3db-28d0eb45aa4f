package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.util.Log;

import com.ecarx.sdk.ECarX;
import com.ecarx.sdk.device.DeviceAPI;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-03-19 20:58
 ******************************************/
public final class DeviceInfoSettingImpl implements DeviceInfoSetting {
    @Override
    public void setInfoForSDK(Context context) {
        String deviceId = null;
        try {
            deviceId = DeviceAPI.get(context).getOpenVIN();
        } catch (Exception e) {
            e.printStackTrace();
        }
        Log.i(ECarX.TAG, "setInfoForSDK: deviceId=" + deviceId);
        DeviceInfoUtil.setDeviceIdAndCarType(deviceId, null);
    }
}
