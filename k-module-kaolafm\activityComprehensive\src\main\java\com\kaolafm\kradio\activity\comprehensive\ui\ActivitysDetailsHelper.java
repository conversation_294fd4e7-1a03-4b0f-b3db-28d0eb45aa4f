package com.kaolafm.kradio.activity.comprehensive.ui;

import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.opensdk.api.activity.model.ActivityInfo;

import java.util.ArrayList;
import java.util.List;

public class ActivitysDetailsHelper {
    interface ButtonClickListener{
        void onButtonClick(ActivityInfo.ButtonList bean);
    }

    static void addSep(LayoutInflater inflater, LinearLayout bubbleButtonParent){
        View sep = inflater.inflate(R.layout.activity_button_ll_sep, bubbleButtonParent, false);
        bubbleButtonParent.addView(sep, 0);
    }

    static void addButton(LayoutInflater inflater, LinearLayout bubbleButtonParent, ActivityInfo.ButtonList bean, ButtonClickListener listener){
        TextView buttonView = (TextView)inflater.inflate(R.layout.activity_button_ll_item, bubbleButtonParent, false);
        buttonView.setTag(bean);
        buttonView.setText(bean.getContent());
        buttonView.setOnClickListener(v ->{
            if(listener != null){
                ActivityInfo.ButtonList tmp = (ActivityInfo.ButtonList) v.getTag();
                listener.onButtonClick(tmp);
            }
        });
        bubbleButtonParent.addView(buttonView, 0);
    }
    static List<ActivityInfo.ButtonList> getValidButtons(ActivityInfo activityBean){
        if(activityBean == null || activityBean.getButtonList() == null || activityBean.getButtonList().size() == 0){
            return new ArrayList<>();
        }

        List<ActivityInfo.ButtonList> beans = new ArrayList<>();

        for(int i = 0; i < activityBean.getButtonList().size(); i++){
            ActivityInfo.ButtonList buttonBean = activityBean.getButtonList().get(i);
            if(buttonBean.getAction() != 3){
                beans.add(buttonBean);
            }
        }

        return beans;
    }
}
