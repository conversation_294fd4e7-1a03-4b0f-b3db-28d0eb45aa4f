//package com.kaolafm.kradio.flavor.impl;
//
//import android.app.Activity;
//import android.content.res.Resources;
//import android.graphics.BitmapFactory;
//import android.view.View;
//import android.view.ViewStub;
//
//import com.kaolafm.kradio.flavor.R;
//import com.kaolafm.kradio.lib.base.flavor.KRadioNavBarInter;
//import com.kaolafm.kradio.lib.utils.IntentUtils;
//
//import static com.kaolafm.kradio.lib.utils.ViewUtil.getActivityFromView;
//
///******************************************
// * 类描述:
// *
// * @version: V1.0
// * @author: yangshaoning
// * @time: 2019-09-02 19:44
// ******************************************/
//public final class KRadioNavBarImpl implements KRadioNavBarInter {
//    @Override
//    public boolean initNavBarHomeUI(Object... args) {
//        View view = (View) args[0];
//        ViewStub homeViewStub = view.findViewById(R.id.nav_home_viewStub);
//        if (homeViewStub == null) {
//            return false;
//        }
//        homeViewStub.inflate();
//        final View childView = view.findViewById(R.id.nav_home_main_layout);
//        if (childView == null) {
//            return false;
//        }
//        childView.post(new Runnable() {
//            @Override
//            public void run() {
//                int height = childView.getHeight();
//
//                BitmapFactory.Options options = new BitmapFactory.Options();
//                options.inJustDecodeBounds = true;
//                Resources res = childView.getResources();
//                BitmapFactory.decodeResource(res, R.drawable.home_bar_icon, options);
//
//                int hPadding = (height - options.outHeight - 2 * res.getDimensionPixelOffset(R.dimen.player_bar_home_padding)) / 2;
//                childView.setPadding(hPadding, 0, hPadding, 0);
//                childView.setOnClickListener(new View.OnClickListener() {
//                    @Override
//                    public void onClick(View v) {
//                        Activity activity = getActivityFromView(v);
//                        new IntentUtils().startLauncher(activity);
//                    }
//                });
//            }
//        });
//        return true;
//    }
//
//    @Override
//    public boolean initActivationHomeUI(Object... args) {
//        return false;
//    }
//}
