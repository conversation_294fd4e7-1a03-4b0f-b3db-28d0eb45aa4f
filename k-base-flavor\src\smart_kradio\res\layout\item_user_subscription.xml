<?xml version="1.0" encoding="utf-8"?>
<com.kaolafm.kradio.common.widget.CScaleLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:sfl="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <com.kaolafm.kradio.lib.widget.SquareFrameLayout
        android:layout_width="@dimen/m114"
        android:layout_height="@dimen/m114"
        sfl:canScale="false">

        <ImageView
            android:id="@+id/iv_play_cover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="@dimen/m1"
            android:scaleType="fitCenter" />

        <ImageView
            android:id="@+id/user_subscription_offline"
            android:layout_width="@dimen/m114"
            android:layout_height="@dimen/m114"
            android:layout_centerInParent="true"
            android:background="@drawable/offline_layer"
            android:src="@drawable/online_offline" />

    </com.kaolafm.kradio.lib.widget.SquareFrameLayout>

    <FrameLayout
        android:id="@+id/fl_item_user_subscription_info_root"
        android:layout_width="match_parent"
        android:layout_height="@dimen/m114"
        android:background="@drawable/selector_user_list_item_bg"
        android:gravity="center_vertical"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginLeft="@dimen/x30"
            android:gravity="center_vertical"
            android:orientation="vertical">

            <TextView
                android:id="@+id/user_tab_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/x50"
                android:ellipsize="end"
                android:singleLine="true"
                android:textColor="@color/user_item_title_text_color"
                android:textSize="@dimen/text_size_title4" />

            <TextView
                android:id="@+id/user_tab_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/x15"
                android:ellipsize="end"
                android:singleLine="true"
                android:textColor="@color/user_item_subtitle_text_color"
                android:textSize="@dimen/text_size_title5" />

        </LinearLayout>

        <ImageView
            android:id="@+id/iv_subscription_subscribe"
            android:layout_width="@dimen/x82"
            android:layout_height="match_parent"
            android:layout_gravity="end"
            android:contentDescription="@null"
            android:padding="@dimen/x24"
            android:scaleType="centerInside"
            android:src="@drawable/playerbar_collect" />

    </FrameLayout>


</com.kaolafm.kradio.common.widget.CScaleLinearLayout>
