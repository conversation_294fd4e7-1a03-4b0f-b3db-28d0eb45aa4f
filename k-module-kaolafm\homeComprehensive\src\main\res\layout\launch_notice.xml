<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/launch_notice_id"
    android:background="@drawable/bg_home">

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="@dimen/y51"
        android:text="@string/launch_notice"
        android:textSize="@dimen/text_size6"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvStart"
        android:layout_width="@dimen/m210"
        android:layout_height="@dimen/y60"
        android:layout_marginEnd="@dimen/notice_btn_distance"
        android:layout_marginBottom="@dimen/notice_btn_bottom"
        android:background="@drawable/message_details_btn_bg"
        android:gravity="center"
        android:text="@string/start_using"
        android:textSize="@dimen/text_size4"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tvExit"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/tvExit"
        android:layout_width="@dimen/m210"
        android:layout_height="@dimen/y60"
        android:layout_marginStart="@dimen/notice_btn_distance"
        android:background="@drawable/message_details_btn_bg2"
        android:gravity="center"
        android:text="@string/disagree_exit"
        android:textSize="@dimen/text_size4"
        app:layout_constraintBottom_toBottomOf="@id/tvStart"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvStart" />

    <CheckBox
        android:id="@+id/cb"
        android:layout_width="@dimen/m32"
        android:layout_height="@dimen/m32"
        android:layout_marginStart="@dimen/x2"
        android:layout_marginBottom="@dimen/notice_cb_bottom"
        android:background="@drawable/sl_cb"
        android:button="@null"
        android:checked="false"
        android:paddingStart="@dimen/x10"
        android:textColor="#ffffffff"
        android:textSize="@dimen/text_size3"
        app:layout_constraintBottom_toTopOf="@id/tvStart"
        app:layout_constraintStart_toStartOf="@id/scrollView" />

    <TextView
        android:id="@+id/tvCb"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/y47"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/x22"
        android:text="@string/launch_agreement_tip"
        android:textSize="@dimen/text_size3"
        app:layout_constraintBottom_toBottomOf="@id/cb"
        app:layout_constraintStart_toEndOf="@id/cb"
        app:layout_constraintTop_toTopOf="@id/cb" />

    <ScrollView
        android:id="@+id/scrollView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/notice_content_start"
        android:layout_marginTop="@dimen/notice_content_top"
        android:layout_marginEnd="@dimen/notice_content_end"
        android:layout_marginBottom="@dimen/y13"
        android:scrollbarSize="@dimen/x4"
        android:scrollbarStyle="insideInset"
        android:scrollbarThumbVertical="@drawable/sh_sb_thumb"
        android:scrollbarTrackVertical="@drawable/sh_sb_track"
        android:scrollbars="vertical"
        app:layout_constraintBottom_toTopOf="@id/cb"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvTitle">

        <TextView
            android:id="@+id/tvContent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:letterSpacing="0.08"
           android:lineSpacingExtra="@dimen/m10"
            android:text="@string/launch_notice_content"
            android:textSize="@dimen/text_size3" />
    </ScrollView>

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="cb,tvCb,scrollView,tvExit,tvStart"
        tools:visibility="gone" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="ivBack,webView"
        tools:visibility="visible" />

    <WebView
        android:id="@+id/webView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/default_edge_start"
        android:layout_marginEnd="@dimen/default_edge_end"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="gone" />

    <ImageView
        android:id="@+id/ivBack"
        style="@style/FragmentBackButton"
        app:layout_constraintBottom_toBottomOf="@id/tvTitle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvTitle" />
</androidx.constraintlayout.widget.ConstraintLayout>