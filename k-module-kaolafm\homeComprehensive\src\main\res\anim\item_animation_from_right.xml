<?xml version="1.0" encoding="utf-8"?>
<set xmlns:android="http://schemas.android.com/apk/res/android"
    android:duration="@integer/anim_duration_medium">

    <translate
        android:fromXDelta="100%p"
        android:interpolator="@android:anim/decelerate_interpolator"
        android:pivotX="50%p"
        android:toXDelta="0%p" />




</set>

<!--<scale-->
<!--android:fromXScale="0%"-->
<!--android:fromYScale="0%"-->
<!--android:interpolator="@android:anim/decelerate_interpolator"-->
<!--android:pivotX="50%"-->
<!--android:pivotY="50%p"-->
<!--android:toXScale="100%"-->
<!--android:toYScale="100%" />-->

    <!--<alpha--><!--android:fromAlpha="0.5"--><!--android:interpolator="@android:anim/accelerate_decelerate_interpolator"--><!--android:toAlpha="1" />-->
