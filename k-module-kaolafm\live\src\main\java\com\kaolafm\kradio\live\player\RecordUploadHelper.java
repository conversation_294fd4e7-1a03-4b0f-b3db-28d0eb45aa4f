package com.kaolafm.kradio.live.player;

import android.content.Context;
import android.util.Log;

import com.alibaba.sdk.android.oss.ClientConfiguration;
import com.alibaba.sdk.android.oss.OSS;
import com.alibaba.sdk.android.oss.OSSClient;
import com.alibaba.sdk.android.oss.callback.OSSCompletedCallback;
import com.alibaba.sdk.android.oss.callback.OSSProgressCallback;
import com.alibaba.sdk.android.oss.common.OSSLog;
import com.alibaba.sdk.android.oss.common.auth.OSSAuthCredentialsProvider;
import com.alibaba.sdk.android.oss.common.auth.OSSCredentialProvider;
import com.alibaba.sdk.android.oss.model.OSSRequest;
import com.alibaba.sdk.android.oss.model.PutObjectRequest;
import com.alibaba.sdk.android.oss.model.PutObjectResult;
import com.kaolafm.kradio.lib.utils.KaolaAppConfigData;
import com.kaolafm.opensdk.api.live.LiveApiConstant;
import com.kaolafm.kradio.live.mvp.LivePresenter;
import com.kaolafm.kradio.live.utils.LiveUtil;

import org.jetbrains.annotations.NotNull;

import java.util.HashMap;

public class RecordUploadHelper {

    private static final String TAG = "LivePresenter";


    private static String generateFileUploadedPath(String fileName) {
        return "kradio_live_radio/" + fileName;
    }

    public static String generateFileUploadName() {
        String userId = LiveUtil.getUserId();
        return userId + "-"
                + System.currentTimeMillis() + LiveManager.EXTENSION;
    }

    //上传到网速的云节点
    public static void uploadToAliyunOSS(Context context, String path, UploadParam program, String fileName,
                                         OSSCompletedCallback<PutObjectRequest, PutObjectResult> ossCompletedCallback,
                                         OSSProgressCallback<PutObjectRequest> ossProgressCallback) {
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "uploadToAliyunOSS  start! path : " + path);
        }
        OSSCredentialProvider credentialProvider = new OSSAuthCredentialsProvider(LiveApiConstant.STS_SERVER_URL + program.getAppid());
        ClientConfiguration conf = new ClientConfiguration();
        conf.setConnectionTimeout(15 * 1000); // 连接超时，默认15秒
        conf.setSocketTimeout(15 * 1000); // socket超时，默认15秒
        conf.setMaxConcurrentRequest(5); // 最大并发请求书，默认5个
        conf.setMaxErrorRetry(2); // 失败后最大重试次数，默认2次
        OSS oss = new OSSClient(context, LiveApiConstant.OSS_ENDPOINT, credentialProvider, conf);
        OSSLog.enableLog();
        PutObjectRequest put = new PutObjectRequest(LiveApiConstant.BUCKET_NAME, generateFileUploadedPath(fileName), path);
        put.setCRC64(OSSRequest.CRC64Config.YES);
        put.setCallbackParam(new HashMap<String, String>() {
            {
                put("callbackUrl", LiveApiConstant.UPLOAD_CALLBACK_URL + program);
                //callbackBody可以自定义传入的信息
                put("callbackBody", "filename=${object}");
            }
        });
        put.setProgressCallback(ossProgressCallback);
        oss.asyncPutObject(put, ossCompletedCallback);
    }


    public static class UploadParam {

        long liveProgramId;

        String uid;

        String nickName;

        int timeLength;

        String voiceFileUrl;

        String appid;

        public long getLiveProgramId() {
            return liveProgramId;
        }

        public String getUid() {
            return uid;
        }

        public String getNickName() {
            return nickName;
        }

        public int getTimeLength() {
            return timeLength;
        }

        public String getVoiceFileUrl() {
            return voiceFileUrl;
        }

        public String getAppid() {
            return appid;
        }

        public UploadParam(long liveProgramId, String uid, String nickName, int timeLength,
                           String voiceFileUrl) {
            this.liveProgramId = liveProgramId;
            this.uid = uid;
            this.nickName = nickName;
            this.timeLength = timeLength;
            this.voiceFileUrl = voiceFileUrl;

            KaolaAppConfigData kaolaAppConfigData = KaolaAppConfigData.getInstance();
            this.appid = kaolaAppConfigData.getAppId();
        }

        @NotNull
        @Override
        public String toString() {
            return "?liveProgramId=" + liveProgramId + "&uid=" + uid + "&nickName="
                    + nickName + "&timeLength=" + timeLength + "&voiceFileUrl=" + voiceFileUrl;
        }
    }


}
