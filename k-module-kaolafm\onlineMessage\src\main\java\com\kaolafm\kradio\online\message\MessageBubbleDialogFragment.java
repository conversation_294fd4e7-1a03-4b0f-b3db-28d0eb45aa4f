package com.kaolafm.kradio.online.message;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import androidx.annotation.DrawableRes;
import androidx.annotation.IdRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.kaolafm.kradio.common.report.ReportUtil;
import com.kaolafm.kradio.basedb.manager.MessageDaoManager;
import com.kaolafm.kradio.online.common.view.DoubleLayerView;
import com.kaolafm.kradio.basedb.entity.meaasge.CrashMessageBean;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.online.common.event.OlineChangeBadgeViewEvent;
import com.kaolafm.opensdk.api.CrashMessageBaseBean;
import com.kaolafm.opensdk.crash.CrashPlayerHelper;
import com.kaolafm.opensdk.crash.Icrashstate;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.PageShowReportEvent;

import org.greenrobot.eventbus.EventBus;

import java.util.List;

/**
 * 消息泡泡
 * 调用示例：
 * MessageBubbleDialogFragment dialogFragment = (MessageBubbleDialogFragment) new Dialogs.Builder().setType(Dialogs.TYPE_MESSAGE_BUBBLE).create();
 * dialogFragment.setBubbleType(MessageBubbleDialogFragment.TYPE_DANGER)
 * .setSceneBitmap(BitmapFactory.decodeResource(getResources(), R.drawable.online_message_bubble_thunderbolt))
 * .setTitle("国家应急广播")
 * .setSubTitle("石家庄市发布暴雨雷电红色预警")
 * .setButtons(new ArrayList<MessageBubbleDialogFragment.MessageBubbleButton>() {{
 * add(new MessageBubbleDialogFragment.MessageBubbleButton("查看详情", R.id.online_message_bubble_button_1_id));
 * add(new MessageBubbleDialogFragment.MessageBubbleButton("倒计时", 20L, R.id.online_message_bubble_button_2_id));
 * add(new MessageBubbleDialogFragment.MessageBubbleButton("删除", R.drawable.online_search_icon_delete, R.id.online_message_bubble_button_3_id));
 * add(new MessageBubbleDialogFragment.MessageBubbleButton("确认", R.drawable.online_user_no_login_icon, 15, R.id.online_message_bubble_button_4_id));
 * }}, new View.OnClickListener() {
 *
 * @Override public void onClick(View v) {
 * int id = v.getId();
 * if (id == R.id.online_message_bubble_button_1_id) {
 * Log.e(TAG, "点击按钮：1");
 * } else if (id == R.id.online_message_bubble_button_2_id) {
 * Log.e(TAG, "点击按钮：2");
 * } else if (id == R.id.online_message_bubble_button_3_id) {
 * Log.e(TAG, "点击按钮：3");
 * } else if (id == R.id.online_message_bubble_button_4_id) {
 * Log.e(TAG, "点击按钮：4");
 * }
 * }
 * }).show(getSupportFragmentManager(), "MessageBubbleDialogFragment");
 */
public class MessageBubbleDialogFragment extends Dialog {
    private ConstraintLayout root_view;
    private DoubleLayerView mXfermodeView;
    private ImageView bubbleIcon, msg_tips_pic_iv, card_bg_iv;
    private TextView bubbleTitle;
    private TextView bubbleSubTitle;
    private LinearLayout bubbleButtonParent;


    //消息类型

    private String mTitle, mSubTitle;
    private String mIconResource;

    private OnShowListener mOnShowListener;
    private OnDismissListener mOnDismissListener;
    private TextView timerTv;
    private boolean hasSceneBg = false;
    private List<MessageBubbleButton> mButtons;
    private OnButtonClickListener mViewClickListener;
    private CrashMessageBean crashMessageBean;
    private int closeTime = 8;
    protected long startTime = -1;
    private boolean isShowDialogBg;//是否显示dialog半透明背景
    private boolean isCloseTime = false;//是否读秒结束

    private Handler handler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            if (closeTime > 0) {
                if (timerTv != null) {
                    timerTv.setText(String.format(ResUtil.getString(R.string.online_message_bubble_timer), closeTime));
                }
                closeTime--;
                handler.sendEmptyMessageDelayed(1, 1000);
            } else {
                isCloseTime = true;
                dismiss();
            }
        }
    };
    private float dimAmount = 0f;

    public MessageBubbleDialogFragment(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    public MessageBubbleDialogFragment(@NonNull Context context) {
        super(context, R.style.FullScreenDialogTheme);
    }


    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.online_layout_message_bubble);
        root_view = findViewById(R.id.root_view);
        mXfermodeView = findViewById(R.id.bubbleBg);
        bubbleIcon = findViewById(R.id.bubbleIcon);
        bubbleTitle = findViewById(R.id.bubbleTitle);
        bubbleSubTitle = findViewById(R.id.bubbleSubTitle);
        bubbleButtonParent = findViewById(R.id.bubbleButtonParent);
        msg_tips_pic_iv = findViewById(R.id.msg_tips_pic_iv);
        card_bg_iv = findViewById(R.id.card_bg_iv);
        Window window = this.getWindow();
        //设置弹出位置
//        window.setGravity(Gravity.BOTTOM | Gravity.START);

        int matchParent = ViewGroup.LayoutParams.MATCH_PARENT;//父布局的宽度

        Window dialogWindow = this.getWindow();
//        dialogWindow.setGravity(Gravity.TOP | Gravity.RIGHT);
        WindowManager.LayoutParams lp = dialogWindow.getAttributes();
        lp.width = matchParent;
        lp.height = matchParent;
//        lp.x = matchParent;
//        lp.y = 300;  //设置出现的高度，距离顶部
        window.setAttributes(lp);
        //去除系统自带的margin
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        //背景全透明
        window.setDimAmount(0f);
        //设置弹出动画
        window.setWindowAnimations(R.style.OnlineMessageBubbleAnimation);
        //设置对话框大小
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        if (isShowDialogBg) {
            root_view.setBackgroundColor(ResUtil.getColor(R.color.message_details_bg));
        } else {
            root_view.setBackgroundColor(Color.TRANSPARENT);
        }
        setBubbleType();
        setTitle(mTitle);
        setSubTitle(mSubTitle);
        setSceneBitmap();
        setButtons(mButtons, mViewClickListener);
        root_view.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (CrashPlayerHelper.getInstance().isPlay()) {
                    CrashPlayerHelper.getInstance().playEnd();
                }
                dismiss();
            }
        });
        mXfermodeView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (crashMessageBean == null) {
                    return;
                }
                dismiss();
                MessageDetailsDialogFragment dialogFragment
                        = new MessageDetailsDialogFragment(getContext());
                dialogFragment.setCrashMessageBean(crashMessageBean)
                        .show();
                MessageDaoManager.getInstance().updateLook(crashMessageBean.getMsgId());
                //上报点击
                ReportUtil.addMessageClike(getPageId(), crashMessageBean.getMsgContentType(), crashMessageBean.getMsgId());
            }
        });
        if (crashMessageBean.getMsgLevel().equals("3")) {
            //应急广播要显示标题下边的图片
            msg_tips_pic_iv.setVisibility(View.VISIBLE);
            ImageLoader.getInstance().displayImage(getContext(), crashMessageBean.getMsgTipsPicUrl()
                    , msg_tips_pic_iv);
        }
//        if (CrashPlayerHelper.getInstance().isPlay()) {
        CrashPlayerHelper.getInstance().setIcrashstate(new Icrashstate() {
            @Override
            public void onCrashstate(int i) {
                if (i == 0) {
                    if (handler != null)
                        handler.sendEmptyMessage(1);
                }
            }

            @Override
            public void onBufferingUpdate(CrashMessageBaseBean crashMessageBaseBean) {

            }
        });
//        }
    }

    public MessageBubbleDialogFragment showDialogBg(boolean isShowDialogBg) {
        this.isShowDialogBg = isShowDialogBg;
        return this;
    }

    public CrashMessageBean getCrashMessageBean() {
        return crashMessageBean;
    }

    @Override
    public void show() {
        Log.d("messageUI", "dialogFragment show");
        super.show();
        if (mOnShowListener != null) {
            mOnShowListener.onShow();
        }
        //上报展示
        ReportUtil.addMessageShow(getPageId(), crashMessageBean.getMsgContentType(), crashMessageBean.getMsgId());
    }

    @Override
    public void dismiss() {
        Log.d("messageUI", "dialogFragment dismiss");
        if (handler != null) {
            handler.removeMessages(1);
            handler = null;
        }
        if (crashMessageBean.getMsgStyleType() == 0 || crashMessageBean.getMsgStyleType() == 1) {
            //无按钮
            MessageDaoManager.getInstance().updateLook(crashMessageBean.getMsgId());
            EventBus.getDefault().post(new OlineChangeBadgeViewEvent());
        } else if (crashMessageBean.getMsgStyleType() == 2 || crashMessageBean.getMsgStyleType() == 3) {
            //有按钮计时结束的关闭不会已读
            if (!isCloseTime) {
                MessageDaoManager.getInstance().updateLook(crashMessageBean.getMsgId());
                EventBus.getDefault().post(new OlineChangeBadgeViewEvent());
            }
        }
        super.dismiss();
        reportPageShowEvent();
        if (mOnDismissListener != null) {
            mOnDismissListener.onDisMiss();
        }
    }


    public MessageBubbleDialogFragment setMDimAmount(float dimAmount) {
        this.dimAmount = dimAmount;
        return this;
    }


    public MessageBubbleDialogFragment setTitle(String title) {
        this.mTitle = title;
        if (bubbleTitle != null)
            bubbleTitle.setText(title);
        return this;
    }

    public MessageBubbleDialogFragment setCrashMessageBean(CrashMessageBean messageBean) {
        this.crashMessageBean = messageBean;
        return this;
    }

    public MessageBubbleDialogFragment setSubTitle(String subtitle) {
        this.mSubTitle = subtitle;
        if (bubbleSubTitle != null)
            bubbleSubTitle.setText(getMaxString(subtitle));
        return this;
    }

    /**
     * 每行最多17个字符，最多显示两行，长于34个字符则用“...”省略
     *
     * @param subTitle
     * @return
     */
    private String getMaxString(String subTitle) {
        if (StringUtil.isEmpty(subTitle)) return null;
        if (subTitle.length() <= 17) return subTitle;
        StringBuilder newSubTitle = new StringBuilder();
        newSubTitle.append(subTitle.substring(0, 17)).append("\n");
        if (subTitle.length() > 34) {
            newSubTitle.append(subTitle.substring(17, 33)).append("...");
        } else {
            newSubTitle.append(subTitle.substring(17));
        }
        return newSubTitle.toString();
    }


    private MessageBubbleDialogFragment setSceneBitmap() {

        if (bubbleButtonParent == null || mXfermodeView == null) return this;
        boolean hasButton = bubbleButtonParent.getChildCount() > 0;

        Bitmap destBitmap;
        if (hasButton) {
            destBitmap = BitmapFactory.decodeResource(getContext().getResources(), R.drawable.online_message_bubble_button_bg_board);
        } else {
            destBitmap = BitmapFactory.decodeResource(getContext().getResources(), R.drawable.online_message_bubble_nonebutton_bg_board);
        }
        if (TextUtils.isEmpty(crashMessageBean.getCardBgUrl())) {
            card_bg_iv.setImageResource(R.drawable.online_message_bubble_nonebutton_bg);
        } else {
            ImageLoader.getInstance().displayImage(getContext(), crashMessageBean.getCardBgUrl(), card_bg_iv);
        }
        if (mXfermodeView != null)
            mXfermodeView.setDestBitmap(destBitmap, 0, 0, DoubleLayerView.ALPHA_NO_SET);
        return this;
    }


    public MessageBubbleDialogFragment setBubbleType() {
        if (bubbleIcon == null) return this;
        int resourceId = 0;
        switch (crashMessageBean.getMsgLevel()) {
//            resourceId = R.drawable.online_message_bubble_warning;黄色预留，一期不做
            case "1"://情感化问候
                resourceId = R.drawable.online_message_bubble_message;
                break;
            case "2"://节目预约、社交 、礼物等
                resourceId = R.drawable.online_message_bubble_gift;
                break;
            case "3"://应急广播消息
                switch (crashMessageBean.getEventLevel()) {
                    case "1":
                        resourceId = R.drawable.online_message_bubble_danger;
                        break;
                    case "2":
                        resourceId = R.drawable.online_message_bubble_cheng;
                        break;
                    case "3":
                        resourceId = R.drawable.online_message_bubble_huang;
                        break;
                    case "4":
                        resourceId = R.drawable.online_message_bubble_lan;
                        break;
                }
                break;
        }
        if (resourceId != 0) {
            bubbleIcon.setImageResource(resourceId);
        }
        return this;
    }

    public MessageBubbleDialogFragment setButtons(List<MessageBubbleButton> buttonTexts, OnButtonClickListener onClickListener) {
        if (crashMessageBean.getMsgStyleType() == 2 || crashMessageBean.getMsgStyleType() == 3) {
            this.mButtons = buttonTexts;
            this.mViewClickListener = onClickListener;
            if (bubbleButtonParent == null) return this;
            boolean hasButton = bubbleButtonParent.getChildCount() > 0;
            bubbleButtonParent.removeAllViews();
            if (buttonTexts == null || buttonTexts.isEmpty()) {
                if (hasButton) {
                    setSceneBitmap();
                }
                return this;
            }

            int perPadding = ResUtil.getDimen(R.dimen.y6) / (buttonTexts.size() - 1);
            for (int i = 0; i < buttonTexts.size(); i++) {
                MessageBubbleButton bt = buttonTexts.get(i);
                View button = createButtonView(bubbleButtonParent, bt, perPadding * i);
                button.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (onClickListener != null) {
                            onClickListener.onButtonClick(MessageBubbleDialogFragment.this, v);
                        }
                    }
                });
                if (i < buttonTexts.size() - 1)
                    createButtonDividerView(bubbleButtonParent, perPadding * (i + 1) / 2);
            }
            if (!hasButton) {
                setSceneBitmap();
            }
        } else {
            if (bubbleButtonParent != null) {
                bubbleButtonParent.removeAllViews();
            }
        }
        return this;
    }

    private View createButtonDividerView(ViewGroup parent, int dPadding) {
        View view = new View(getContext());
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(ResUtil.getDimen(R.dimen.x1), LinearLayout.LayoutParams.MATCH_PARENT);
        params.setMargins(0, ResUtil.getDimen(R.dimen.y21) - dPadding, 0, ResUtil.getDimen(R.dimen.y26) + dPadding);
        view.setLayoutParams(params);
        view.setBackgroundResource(R.drawable.online_message_bubble_button_divider);
        parent.addView(view);
        return view;
    }

    /**
     * 创建按钮
     *
     * @param parent
     * @param bt
     * @param dPadding 按钮从左到右依次上移，该值表示当前按钮应上移的距离，px
     * @return
     */
    private View createButtonView(ViewGroup parent, MessageBubbleButton bt, int dPadding) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.online_bubble_button, parent, false);
        view.setPadding(view.getPaddingStart(), view.getPaddingTop() - dPadding, view.getPaddingEnd(), view.getPaddingBottom() + dPadding);

        ImageView iconIv = view.findViewById(R.id.messageBubbleIconIv);
        TextView titleTv = view.findViewById(R.id.messageBubbleTextTv);
        timerTv = view.findViewById(R.id.messageBubbleTimerTv);
        if (bt.icon == -1) {
            iconIv.setVisibility(View.GONE);
        } else {
            iconIv.setImageResource(bt.icon);
        }
        if (bt.timer == -1) {
            timerTv.setVisibility(View.GONE);
        } else {
            timerTv.setText("");
        }
        titleTv.setText(bt.text);
        view.setId(bt.id);
        parent.addView(view);
        return view;
    }

    public static final class MessageBubbleButton {

        private String text;
        @DrawableRes
        private int icon = -1;
        private long timer = -1;
        @IdRes
        private int id;

        public MessageBubbleButton(String text, int id) {
            this.text = text;
            this.id = id;
        }

        public MessageBubbleButton(String text, long timer, int id) {
            this.text = text;
            this.timer = timer;
            this.id = id;
        }

        public MessageBubbleButton(String text, int icon, int id) {
            this.text = text;
            this.icon = icon;
            this.id = id;
        }

        public MessageBubbleButton(String text, int icon, long timer, int id) {
            this.text = text;
            this.icon = icon;
            this.timer = timer;
            this.id = id;
        }
    }

    public MessageBubbleDialogFragment setOnShowListener(OnShowListener onShowListener) {
        mOnShowListener = onShowListener;
        return this;
    }

    public interface OnShowListener {
        void onShow();
    }

    public MessageBubbleDialogFragment setOnDisMissListener(OnDismissListener onDismissListener) {
        mOnDismissListener = onDismissListener;
        return this;
    }

    public interface OnButtonClickListener {
        void onButtonClick(MessageBubbleDialogFragment fragment, View btn);
    }

    public interface OnDismissListener {
        void onDisMiss();
    }


    public String getPageId() {
        return Constants.PAGE_ID_MESSAGE_CARD;
    }

    @Override
    protected void onStart() {
        super.onStart();
//        String pageId = getPageId();
//        if (!StringUtil.isEmpty(pageId)) {
//            Log.i(ReportConstants.REPORT_TAG, "设置pageid= " + pageId);
//            ReportHelper.getInstance().setPage(pageId);
//        }

        startTime = System.currentTimeMillis();
    }

    /**
     * 页面曝光事件上报
     */
    private void reportPageShowEvent() {
        long duration = System.currentTimeMillis() - startTime;
        if (startTime < 0 || duration < 300) {
            return;
        }

        PageShowReportEvent event = new PageShowReportEvent();
        event.setPage(getPageId());
        event.setPageId(getPageId());
        event.setPageTime(String.valueOf(duration));
        ReportHelper.getInstance().addEvent(event);
        Log.i("BaseDialogFragment", "report=" + duration);
        startTime = -1;
    }
}
