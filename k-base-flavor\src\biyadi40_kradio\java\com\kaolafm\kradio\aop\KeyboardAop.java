package com.kaolafm.kradio.aop;

import android.content.Context;
import androidx.fragment.app.Fragment;
import android.util.Log;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;

/**
 * @Package: com.kaolafm.kradio.aop
 * @Description:
 * @Author: Maclay
 * @Date: 16:51
 */
@Aspect
public class KeyboardAop {
    private static final String TAG = "KeyboardAop";
    private boolean needResumeKeyboard = false;

    @Around("execution(* com.kaolafm.kradio.user.ui.LoginFragment.onPause(..))")
    public void loginFragmentOnPause(ProceedingJoinPoint point) throws Throwable {
        Log.d(TAG, "loginFragmentOnPause");
        point.proceed();
        try {
            Fragment fragment = (Fragment) point.getThis();
            InputMethodManager imm = (InputMethodManager) fragment.getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
            View view = fragment.getActivity().getWindow().getDecorView().findFocus();
            if (view instanceof EditText) {
                Log.d(TAG, "loginFragmentOnPause focus on edit , "+imm.isActive());
                needResumeKeyboard = true;
                imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

//    @Around("execution(* com.kaolafm.kradio.user.ui.LoginFragment.onResume(..))")
//    public void loginFragmentOnResume(ProceedingJoinPoint point) throws Throwable {
//        Log.d(TAG, "loginFragmentOnResume");
//        point.proceed();
//        if (needResumeKeyboard) {
//            try {
//                Fragment fragment = (Fragment) point.getThis();
//                InputMethodManager imm = (InputMethodManager) fragment.getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
//                View view = fragment.getActivity().getWindow().getDecorView().findFocus();
//                if (view instanceof EditText) {
//                    Log.d(TAG, "loginFragmentOnResume focus on edit , show keyboard");
//                    needResumeKeyboard = false;
//                    view.postDelayed(()->{
//                        imm.showSoftInput(view,0);
//                        Log.d(TAG, "loginFragmentOnResume  show keyboard");
//                    },250);
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
//    }
}
