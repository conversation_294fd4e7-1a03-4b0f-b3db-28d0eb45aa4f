<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/broadcast_player_root_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_home"
    android:paddingTop="@dimen/activity_title_top_padding">

    <ImageView
        android:id="@+id/player_title_line"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginTop="@dimen/y12"
        android:background="@color/radio_fragment_title_line_bg_color"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/backView" />

    <ImageView
        style="@style/FragmentBackButton"
        tools:ignore="MissingConstraints" />

    <TextView
        android:id="@+id/broadcast_player_horizontal_title_text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:textColor="@color/global_title_text_color"
        android:textSize="@dimen/text_size7"
        app:layout_constraintBottom_toTopOf="@id/player_title_line"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.5" />

    <TextView
        android:id="@+id/player_broadcast_subscribe_text"
        android:layout_width="@dimen/player_subscribe_button_width"
        android:layout_height="@dimen/player_subscribe_button_height"
        android:layout_marginRight="@dimen/all_view_left_right_distance"
        android:background="@drawable/pc_round_bg"
        android:gravity="center"
        android:textColor="@color/player_subscribe_text_selector"
        android:textSize="@dimen/player_subscribe_text_size"
        app:layout_constraintBottom_toTopOf="@id/player_title_line"
        app:layout_constraintHorizontal_bias="0.501"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.542" />

    <!--    <View-->
    <!--        android:id="@+id/layer_meng"-->
    <!--        android:layout_width="0dp"-->
    <!--        android:layout_height="0dp"-->
    <!--        android:background="@drawable/broadcast_list_shadow"-->
    <!--        android:visibility="gone"-->
    <!--        app:layout_constraintBottom_toBottomOf="parent"-->
    <!--        app:layout_constraintHeight_default="percent"-->
    <!--        app:layout_constraintHeight_percent="0.426"-->
    <!--        app:layout_constraintLeft_toLeftOf="parent"-->
    <!--        app:layout_constraintRight_toRightOf="parent" />-->

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/broadcast_player_horizontal_controller_bar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/x20"
        android:layout_marginRight="@dimen/x20"
        android:layout_marginBottom="@dimen/y45"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintLeft_toLeftOf="@id/broadcast_player_horizontal_seek_bar"
        app:layout_constraintRight_toRightOf="@id/broadcast_player_horizontal_seek_bar">

        <include layout="@layout/view_broadcast_player_play_bar" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.Group
        android:id="@+id/broadcast_player_show_hide_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:constraint_referenced_ids="broadcast_player_horizontal_title_text,broadcast_player_horizontal_sv" />


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/broadcast_player_horizontal_sv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.462"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/player_title_line"
        app:layout_constraintVertical_bias="0.21" />


    <com.kaolafm.kradio.player.comprehensive.play.widget.BroadcastPlayListContent
        android:id="@+id/broadcast_player_horizontal_play_list"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.0" />

    <com.kaolafm.kradio.common.widget.TimerSeekBar
        android:id="@+id/broadcast_player_horizontal_seek_bar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/y30"
        app:layout_constraintBottom_toTopOf="@id/broadcast_player_horizontal_controller_bar"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintWidth_percent="0.6" />


</androidx.constraintlayout.widget.ConstraintLayout>
