package com.kaolafm.kradio.lib.basedb.manager;

import android.app.Application;

import com.kaolafm.kradio.lib.BuildConfig;
import com.kaolafm.kradio.lib.basedb.greendao.DaoMaster;
import com.kaolafm.kradio.lib.basedb.greendao.DaoSession;
import com.kaolafm.kradio.lib.basedb.helper.SQLiteUpdateOpenHelper;
import com.kaolafm.kradio.lib.base.AppDelegate;

import org.greenrobot.greendao.query.QueryBuilder;

/**
 * 进行数据库的管理
 * 1.创建数据库
 * 2.创建数据库表
 * 3.对数据库进行增删查改
 * 4.对数据库进行升级
 * <AUTHOR>
 * @date 2018/5/14
 */

public class DaoManager {

    private static final String DB_NAME = "kradio.db";

    private final Application mContext;

    private DaoMaster mDaoMaster;

    private DaoSession mDaoSession;

    private DaoMaster.OpenHelper mHelper;

    private DaoManager() {
        mContext = AppDelegate.getInstance().getContext();
        QueryBuilder.LOG_SQL = BuildConfig.DEBUG;
        QueryBuilder.LOG_VALUES = BuildConfig.DEBUG;
    }

    private static final class DaoManagerHolder{
        private static final DaoManager INSTANCE = new DaoManager();
    }

    public static DaoManager getInstance(){
        return DaoManagerHolder.INSTANCE;
    }
    public DaoMaster getDaoMaster(){
        if (mDaoMaster == null){
            mHelper = new SQLiteUpdateOpenHelper(mContext, DB_NAME);
            mDaoMaster = new DaoMaster(mHelper.getWritableDatabase());
        }
        return mDaoMaster;
    }
    public DaoMaster.OpenHelper getHelper(){
        if (mHelper == null){
            mHelper = new SQLiteUpdateOpenHelper(mContext, DB_NAME);
        }
        return mHelper;
    }


    public DaoSession getDaoSession(){
        if (mDaoSession == null){
            if (mDaoMaster == null){
                mDaoMaster = getDaoMaster();
            }
            mDaoSession = mDaoMaster.newSession();
        }
        return mDaoSession;
    }

    public void closeDataBase(){
        closeHelper();
        closeDaoSession();
    }

    public void closeDaoSession() {
        if (mDaoSession != null){
            mDaoSession.clear();
            mDaoSession = null;
        }
    }

    public void closeHelper() {
        if (mHelper != null){
            mHelper.close();
            mHelper = null;
        }
    }


}
