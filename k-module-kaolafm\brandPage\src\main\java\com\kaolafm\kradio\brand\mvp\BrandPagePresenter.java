package com.kaolafm.kradio.brand.mvp;

import android.util.Log;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.home.comprehensive.data.DataConverter;
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.kradio.lib.common.ResType;
import com.kaolafm.opensdk.api.brandpage.model.BrandPageListBean;
import com.kaolafm.opensdk.api.operation.model.ImageFile;
import com.kaolafm.opensdk.api.operation.model.column.Column;
import com.kaolafm.opensdk.api.operation.model.column.ColumnContent;
import com.kaolafm.opensdk.api.operation.model.column.ColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.LiveProgramDetailColumnMember;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> shiqian
 * @date 2023-03-01
 */
public class BrandPagePresenter extends BasePresenter<BrandPageModel, IBrandPageView> {
    private final String TAG = "BrandPagePresenter";

    /**
     * 开关 - 生成品牌主页圆形卡片直播节目假数据
     * 调试结束后记得关闭
     */
    private static final boolean DEBUG_LIVE_SWITCH = false;

    public BrandPagePresenter(IBrandPageView view) {
        super(view);
    }

    @Override
    protected BrandPageModel createModel() {
        return mModel = new BrandPageModel();
    }

    /**
     * 获取品牌主页列表
     *
     * @param brandPageId
     */
    public void getBrandPageList(String brandPageId) {
        if (mView != null) {
            mView.onLoaing();
        }
        mModel.getBrandPageList(brandPageId, new HttpCallback<BrandPageListBean>() {
            @Override
            public void onSuccess(BrandPageListBean brandPageListBean) {
                if (mView != null) {
                    mView.showTabAndList(brandPageListBean);
                }
                if (mView != null) {
                    mView.onLoadFinish();
                }
            }

            @Override
            public void onError(ApiException e) {
                if (mView != null) {
                    mView.showTabAndFailure(e);
                }
                if (mView != null) {
                    mView.onLoadFinish();
                }
            }
        });
    }

    /**
     * 获取品牌主页列表内容 do 品牌主页请求接口获取数据
     *
     * @param sectionId
     * @param open_uid
     * @param access_token
     */
    public void getBrandPageContent(String sectionId, String open_uid, String access_token) {
        Log.i(TAG, "getBrandPageContent");
        if (mView != null) {
            mView.onLoaing();
        }
        mModel.getBrandPageContent(sectionId, open_uid, access_token, new HttpCallback<List<Column>>() {
            @Override
            public void onSuccess(List<Column> columnGrps) {
                List<HomeCell> cells = new ArrayList<>();
                if (columnGrps != null && columnGrps.size() > 0) {
                    List<? extends ColumnMember> columnMembers = ((Column) columnGrps.get(0)).getColumnMembers();
                    cells = DataConverter.memberToCell(cells.size(), columnMembers, ((Column) columnGrps.get(0)).getCode(), -1);
                }
                if (mView != null) {
                    if (!ListUtil.isEmpty(cells)) {
                        modifyToTestData(cells);
                        mView.onShowContent(cells);
                    }
                } else {
                    Log.i(TAG, "getBrandPageContent onSuccess mView is null");
                }
                if (mView != null) {
                    mView.onLoadFinish();
                }
            }

            @Override
            public void onError(ApiException e) {
                if (mView != null) {
                    mView.onShowContentFailure(e);
                }
                if (mView != null) {
                    mView.onLoadFinish();
                }
            }
        });
    }

    /**
     * 获取品牌主页列表内容 -> 更新直播类型的圆形组件直播间状态
     *
     * @param sectionId
     * @param open_uid
     * @param access_token
     */
    public void getBrandPageLiveStatusData(String sectionId, String open_uid, String access_token) {
        Log.i(TAG, "getBrandPageLiveStatusData");
        if (mView != null) {
            mView.onLoaing();
        }
        mModel.getBrandPageContent(sectionId, open_uid, access_token, new HttpCallback<List<Column>>() {
            @Override
            public void onSuccess(List<Column> columnGrps) {
                Log.i(TAG, "getBrandPageLiveStatusData onSuccess");
                List<HomeCell> cells = new ArrayList<>();
                if (columnGrps != null && columnGrps.size() > 0) {
                    List<? extends ColumnMember> columnMembers = ((Column) columnGrps.get(0)).getColumnMembers();
                    cells = DataConverter.memberToCell(cells.size(), columnMembers, ((Column) columnGrps.get(0)).getCode(), -1);
                }
                if (mView != null) {
                    if (!ListUtil.isEmpty(cells)) {
                        modifyToTestData(cells);
                        mView.onUpdateLiveStatus(cells);
                    }
                } else {
                    Log.i(TAG, "getBrandPageLiveStatusData onSuccess mView is null");
                }
                if (mView != null) {
                    mView.onLoadFinish();
                }
            }

            @Override
            public void onError(ApiException e) {
                Log.e(TAG, "getBrandPageLiveStatusData error:" + e);
                if (mView != null) {
                    mView.onLoadFinish();
                }
            }
        });
    }

    private int testFlag = 1;

    /**
     * 生成品牌主页圆形卡片直播节目假数据
     */
    private void modifyToTestData(List<HomeCell> cells){
        if (!DEBUG_LIVE_SWITCH){
            return;
        }

        if (!isBrandPageCell(cells)){
            return;
        }

        List<ColumnContent> columnContentList = cells.get(0).getContentList();
        if (columnContentList == null || columnContentList.isEmpty()){
            return;
        }

        columnContentList.remove(0);
        LiveProgramDetailColumnMember liveProgramDetailColumnMember = new LiveProgramDetailColumnMember();
        Map<String, ImageFile> map = new HashMap<>();
        ImageFile imageFile = new ImageFile();
        imageFile.setUrl("https://img2.baidu.com/it/u=2286064893,2631559078&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=313");
        map.put("cover", imageFile);
        liveProgramDetailColumnMember.setImageFiles(map);
        int liveStatus = testFlag++ % 3;
        liveProgramDetailColumnMember.setLiveStatus(liveStatus);
        liveProgramDetailColumnMember.setTitle("传测试001");
        liveProgramDetailColumnMember.setId("1595057541");
        liveProgramDetailColumnMember.setResType(PlayerConstants.RESOURCES_TYPE_LIVING);
        columnContentList.add(0, liveProgramDetailColumnMember);
        Log.i("ComperhensiveBrandPageDateFragment", "modifyToTestData --- liveStatus=" + liveStatus);
    }

    private boolean isBrandPageCell(List<HomeCell> cells) {
        if (cells != null && cells.size() > 0) {
            if (cells.get(0).itemType == ResType.HOME_ITEM_TYPE_BRAND) {
                return true;
            }
        }
        return false;
    }
}
