package com.kaolafm.kradio.lib.dialog;

import android.content.Context;
import androidx.annotation.StringRes;
import android.view.View;
import android.widget.TextView;

import com.kaolafm.kradio.lib.R;
import com.kaolafm.kradio.lib.dialog.DialogListener.OnNativeListener;
import com.kaolafm.kradio.lib.dialog.DialogListener.OnPositiveListener;
import com.kaolafm.kradio.lib.utils.AntiShake;

public class CommonCenterDialog extends BaseCenterDelayDialog {
    TextView mTvDialogCenterCancel;
    TextView mTvDialogCenterDefine;
    TextView mTvDialogCenterMessage;

    private OnNativeListener<CommonCenterDialog> mNativeListener;

    private OnPositiveListener<CommonCenterDialog> mPositiveListener;


    public CommonCenterDialog(Context context) {
        super(context);
    }


    public CommonCenterDialog(Context context,boolean cancelOutside,float widthScale,float hightScale) {
        super(context,cancelOutside,widthScale,hightScale);
    }

    @Override
    public void onCreateView() {
        super.onCreateView();
    }

    @Override
    protected void initView(View view) {
        mTvDialogCenterCancel = view.findViewById(R.id.tv_dialog_center_cancel);
        mTvDialogCenterDefine = view.findViewById(R.id.tv_dialog_center_define);
        mTvDialogCenterMessage = view.findViewById(R.id.tv_dialog_center_message);
        mTvDialogCenterCancel.setOnClickListener(v -> onViewClicked(v));
        mTvDialogCenterDefine.setOnClickListener(v -> onViewClicked(v));
    }


    @Override
    protected int delayTimeDimiss() {
        return 0;
    }

    @Override
    protected int getLayoutId() {
        return R.layout.common_center_dialog;
    }

    public void onViewClicked(View view) {
        int id = view.getId();
        if (!AntiShake.check(id)) {
            if (id == R.id.tv_dialog_center_cancel) {
                if (mNativeListener != null) {
                    mNativeListener.onClick(this);
                } else {
                    dismiss();
                }
            } else if (id == R.id.tv_dialog_center_define) {
                if (mPositiveListener != null) {
                    mPositiveListener.onClick(this);
                }
            }
        }
    }

    public CommonCenterDialog setMessage(String message) {
        mTvDialogCenterMessage.setText(message);
        return this;
    }

    public CommonCenterDialog setMessage(@StringRes int resId) {
        mTvDialogCenterMessage.setText(resId);
        return this;
    }

    public CommonCenterDialog setLeftButton(CharSequence text) {
        mTvDialogCenterDefine.setText(text);
        return this;
    }

    public CommonCenterDialog setLeftButton(@StringRes int resId) {
        mTvDialogCenterDefine.setText(resId);
        return this;
    }

    public CommonCenterDialog setRightButton(CharSequence text) {
        mTvDialogCenterCancel.setText(text);
        return this;
    }

    public CommonCenterDialog setRightButton(@StringRes int resId) {
        mTvDialogCenterCancel.setText(resId);
        return this;
    }

    public CommonCenterDialog setMyOnNativeListener(OnNativeListener<CommonCenterDialog> nativeListener) {
        mNativeListener = nativeListener;
        return this;
    }

    public CommonCenterDialog setMyOnPositiveListener(OnPositiveListener<CommonCenterDialog> positiveListener) {
        mPositiveListener = positiveListener;
        return this;
    }


}
