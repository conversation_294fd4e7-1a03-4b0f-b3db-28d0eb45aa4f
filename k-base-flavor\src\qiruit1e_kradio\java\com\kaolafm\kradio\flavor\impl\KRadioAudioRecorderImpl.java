package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.util.Log;

import com.kaolafm.kradio.lib.base.flavor.KRadioAudioRecorderInter;
import com.kaolafm.kradio.lib.base.flavor.live.KradioRecorderInterface;
import com.oem.frameworks.mic.MicManager;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-08-15 15:06
 ******************************************/
public final class KRadioAudioRecorderImpl implements KRadioAudioRecorderInter {
    private static final String TAG = "KRadioAudioRecorderImpl";
    private MicManager mMicManager; //申请mic管理对象

    @Override
    public boolean initVR(Object... args) {
        mMicManager = new MicManager((Context) args[0]);//实例化管理对象
        return true;
    }

    @Override
    public boolean onAudioRecordStart(Object... args) {
        int status = mMicManager.requestMicFocus(listener, MicManager.MIC_COMMON_LEVEL, MicManager.MICFOCUS_FLAG_DELAY_OK);
        Log.i(TAG, "onAudioRecordStart status = " + status);
        return status == MicManager.MICFOCUS_REQUEST_GRANTED;
    }

    @Override
    public boolean onAudioRecordStop(Object... args) {
        int status = mMicManager.abandonMicFocus(listener);
        Log.i(TAG, "onAudioRecordStop status = " + status);
        return status == MicManager.MICFOCUS_REQUEST_GRANTED;
    }

    @Override
    public KradioRecorderInterface getRecorder() {
        return null;
    }

    @Override
    public void setVrStatusListener(OnVRStatusListener onVRStatusListener) {
    }

    private MicManager.OnMicFocusChangeListener listener = new MicManager.OnMicFocusChangeListener() {
        @Override
        public void onMicFocusChange(int focusChange) {
            Log.i(TAG, "onMicFocusChange focusChange = " + focusChange);
        }
    };
}
