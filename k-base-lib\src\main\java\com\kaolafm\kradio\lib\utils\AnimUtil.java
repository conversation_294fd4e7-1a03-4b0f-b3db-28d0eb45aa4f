package com.kaolafm.kradio.lib.utils;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.annotation.SuppressLint;
import android.os.Build;
import androidx.annotation.RequiresApi;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.TranslateAnimation;

/**
 * 动画相关工具类
 *
 * <AUTHOR>
 * @date 2018/5/7
 */

public class AnimUtil {

    public static void startScale(View view, float scaleValue, float defaultScale, int duration) {
        AnimatorSet animatorSet = new AnimatorSet();
        ObjectAnimator animationScaleX = ObjectAnimator.ofFloat(view, "scaleX", scaleValue, defaultScale);
        ObjectAnimator animationScaleY = ObjectAnimator.ofFloat(view, "scaleY", scaleValue, defaultScale);
        animationScaleX.setDuration(duration);
        animationScaleY.setDuration(duration);
        animatorSet.playTogether(animationScaleX, animationScaleY);
        animatorSet.start();
    }

    @SuppressLint("WrongConstant")
    public static ObjectAnimator startRotation(View view, int duration) {
        ObjectAnimator animationScale = ObjectAnimator.ofFloat(view, "rotation", 0f, 360f);
        animationScale.setDuration(duration);
        animationScale.setRepeatCount(ValueAnimator.INFINITE);
        animationScale.setRepeatMode(ValueAnimator.RESTART);
        animationScale.start();
        return animationScale;
    }

    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    public static void stopAnimation(ObjectAnimator animator) {
        if (animator != null) {
            animator.pause();
        }
    }

    /**
     * 是否启用缩放动画
     * true = 启用缩放动画（默认）
     * false = 禁用缩放动画以优化性能
     */
    private static final boolean ENABLE_SCALE_ANIMATION = false;

    public static void startScalePress(View view) {
        if (!ENABLE_SCALE_ANIMATION) {
            return;
        }
        startScale(view, 1F, 0.92F, 250);
    }

    public static void startScaleRelease(View view) {
        if (!ENABLE_SCALE_ANIMATION) {
            return;
        }
        startScale(view, 0.92F, 1F, 250);
    }

    public static void onTouchEvent(View view, MotionEvent event) {
        if (!ENABLE_SCALE_ANIMATION) {
            return;
        }

        int action = event.getAction();
        switch (action) {
            case MotionEvent.ACTION_DOWN:
                startScalePress(view);
                break;
            case MotionEvent.ACTION_OUTSIDE:
            case MotionEvent.ACTION_CANCEL:
            case MotionEvent.ACTION_UP:
                startScaleRelease(view);
                break;
            default:
                break;
        }
    }

    /**
     * 开始指定view的x轴偏移动画。
     * @param view
     * @param translateX 相对于view的x的偏移量
     */
    public static void startTranslateX(View view, float translateX) {
        view.animate()
                .setDuration(400)
                .translationX(translateX)
                .start();
    }

    public static void startTranslateY(View view, float start, float end) {
        TranslateAnimation animation = new TranslateAnimation(0, 0, start, end);
        animation.setDuration(400);
        view.startAnimation(animation);
    }
}
