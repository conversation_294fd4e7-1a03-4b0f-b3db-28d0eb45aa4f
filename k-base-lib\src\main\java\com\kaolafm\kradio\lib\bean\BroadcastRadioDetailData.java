package com.kaolafm.kradio.lib.bean;

/**
 * Created by GuoPei on 2015/11/16.
 * 广播电台详情对象
 */
public class BroadcastRadioDetailData {
    /**
     * 电台Id
     */
    private long broadcastId;
    /**
     * 电台名称
     */
    private String name;
    /**
     * 封面url
     */
    private String img;

    /**
     * 电台描述
     */
    private String desc;
    /**
     * 分类名称
     */
    private String classifyName;
    /**
     * 分类ID
     */
    private int classifyId;
    /**
     * 是否订阅
     */
    private int isSubscribe;
    /**
     * 播放url
     */
    private String playUrl;
    /**
     * 分享url
     */
    private String shareUrl;
    /**
     * 在线人数
     */
    private int onLineNum;
    /**
     * 喜欢人数
     */
    private long likedNum;
    /**
     * 当前状态
     */
    private int status;
    /**
     * 房间ID
     */
    private int roomId;

    /**
     * 当前节目名
     */
    private String currentProgramTitle;

    /**
     * 来源icon
     */
    private String icon;

    /**
     * 频率
     */
    private String freq;

    /**
     * 播放数
     */
    private int playTimes;

    /**
     * 是否正在播放
     */
    private boolean isPlaying;

    /**
     * 资源类型
     */
    private int resourcesType;

    public int getPlayTimes() {
        return playTimes;
    }

    public void setPlayTimes(int playTimes) {
        this.playTimes = playTimes;
    }

    public String getFreq() {
        return freq;
    }

    public void setFreq(String freq) {
        this.freq = freq;
    }

    public long getBroadcastId() {
        return broadcastId;
    }

    public void setBroadcastId(long broadcastId) {
        this.broadcastId = broadcastId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getClassifyName() {
        return classifyName;
    }

    public void setClassifyName(String classifyName) {
        this.classifyName = classifyName;
    }

    public int getClassifyId() {
        return classifyId;
    }

    public void setClassifyId(int classifyId) {
        this.classifyId = classifyId;
    }

    public boolean getIsSubscribe() {
        boolean flag = isSubscribe == 1;
        return flag;
    }

    public void setIsSubscribe(int isSubscribe) {
        this.isSubscribe = isSubscribe;
    }

    public String getPlayUrl() {
        return playUrl;
    }

    public void setPlayUrl(String playUrl) {
        this.playUrl = playUrl;
    }

    public String getShareUrl() {
        return shareUrl;
    }

    public void setShareUrl(String shareUrl) {
        this.shareUrl = shareUrl;
    }

    public int getOnLineNum() {
        return onLineNum;
    }

    public void setOnLineNum(int onLineNum) {
        this.onLineNum = onLineNum;
    }

    public long getLikedNum() {
        return likedNum;
    }

    public void setLikedNum(long likedNum) {
        this.likedNum = likedNum;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getRoomId() {
        return roomId;
    }

    public void setRoomId(int roomId) {
        this.roomId = roomId;
    }

    public String getCurrentProgramTitle() {
        return currentProgramTitle;
    }

    public void setCurrentProgramTitle(String currentProgramTitle) {
        this.currentProgramTitle = currentProgramTitle;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getIcon() {
        return icon;
    }

    public boolean isPlaying() {
        return isPlaying;
    }

    public void setPlaying(boolean playing) {
        isPlaying = playing;
    }

    public int getResourcesType() {
        return resourcesType;
    }

    public void setResourcesType(int resourcesType) {
        this.resourcesType = resourcesType;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
