package com.kaolafm.kradio.live.mvp;

import com.kaolafm.kradio.lib.base.mvp.IView;
import com.kaolafm.kradio.live.player.ErrorStatus;
import com.kaolafm.opensdk.api.goods.model.GoodsResult;
import com.kaolafm.opensdk.api.live.model.ChatUserInfo;
import com.kaolafm.opensdk.api.live.model.Gift;
import com.kaolafm.opensdk.api.live.model.GiftGivingResult;
import com.kaolafm.opensdk.api.live.model.GiftsResult;
import com.kaolafm.opensdk.api.live.model.LiveInfoDetail;
import com.kaolafm.opensdk.api.live.model.MessageBean;
import com.kaolafm.opensdk.api.yunxin.model.LiveCommentStatusMsg;
import com.kaolafm.opensdk.http.error.ApiException;

import java.util.ArrayList;
import java.util.List;

public interface HomeLiveView extends IView {
    void showLiveInfo(LiveInfoDetail info);

    void showErrorInfo(ErrorStatus status);

    void showListenerNumber(int number);

    void showFileNotExist();

    void showRecordUploadProgress(int progress);

    void showRecordUploadSuccess();

    void showRecordUploadFailure();

    void showRoomMemberEnter(ChatUserInfo member);

    void showRoomMemberExit(ChatUserInfo chatUserInfo);

    void showChatMessageReceived(ArrayList<MessageBean> messageData);

    /**
     * 从消息列表中删除指定的消息
     */
    void onChatMessageReceivedToRemove(LiveCommentStatusMsg liveCommentStatusMsg);


    void onChatRoomMemberReceived(List<ChatUserInfo> users);

    void onChatRoomMemberQueryFailed(int code, Throwable exception);

    void onHistoryMessageReceived(List<MessageBean> param, boolean isOld);

    void onHistoryMessageQueryFailed(int code, Throwable exception);

    void enterChatRoomSuccess();

    void enterChatRoomFailed();

    void onGiftsSuccess(GiftsResult giftsResult);

    void onGiftsFailed();

    void onLoading();

    void onLoadFinish();

    void onGiveGiftSuccess(GiftGivingResult giftGivingResult, Gift gift);

    void onGiveGiftFailed(ApiException e);

    void onGoodsSuccess(GoodsResult goodsResult);

    void onGoodsFailed();
}
