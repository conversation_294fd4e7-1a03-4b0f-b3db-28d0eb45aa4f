<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingTop="@dimen/y44">

    <com.kaolafm.kradio.common.widget.CTextView
        android:id="@+id/media_detail_similar_re_left_textView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/similar_re_str"
        android:textColor="@color/colorWhite"
        android:textSize="@dimen/text_size5" />

    <com.kaolafm.kradio.common.widget.CTextView
        android:id="@+id/media_detail_similar_re_right_textView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:background="@drawable/normal_big_round_common_stroke_line_selector"
        android:gravity="center"
        android:paddingBottom="@dimen/media_detail_batch_v_padding"
        android:paddingLeft="@dimen/media_detail_batch_h_padding"
        android:paddingRight="@dimen/media_detail_batch_h_padding"
        android:paddingTop="@dimen/media_detail_batch_v_padding"
        android:text="@string/another_batch_str"
        android:textColor="@color/colorWhite"
        android:textSize="@dimen/text_size3" />

    <com.kaolafm.kradio.common.widget.KLRecyclerView
        android:id="@+id/media_detail_similar_re_recyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/media_detail_similar_re_left_textView"
        android:layout_marginTop="@dimen/y34" />
</RelativeLayout>