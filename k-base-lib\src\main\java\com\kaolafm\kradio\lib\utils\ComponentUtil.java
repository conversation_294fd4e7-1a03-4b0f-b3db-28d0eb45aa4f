package com.kaolafm.kradio.lib.utils;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.component.ComponentCallback;
import com.kaolafm.kradio.component.ComponentClient;
import com.kaolafm.kradio.component.ComponentResult;
import com.kaolafm.kradio.component.DynamicComponent;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 组件调用的工具类。该工具是为了简化调用步骤。
 *
 * <AUTHOR>
 * @date 2019-10-29
 */
public class ComponentUtil {

    public static final String ADD_OBSERVER = "addObserver";

    public static final String REMOVE_OBSERVER = "removeObserver";

    public static final String COMPONENT_NAME = "addObserverComponentName";

    /**
     * 组件名-动态组件名集合 键值对集合。key是组件名，即其他组件需要监听的组件的名称，如UserComponent; value是动态组件名，即监听的组件发生改变需要回调的动态组件名。
     */
    private static final ConcurrentHashMap<String, List<String>> COMPONENT_OBSERVERS = new ConcurrentHashMap<>();

    private ComponentUtil() {
    }

    /**
     * 同步调用指定组件
     *
     * @param componentName 调用的组件名
     * @param actionName    调用组件的操作名
     * @param params        传递给调用组件的参数
     * @return 组件返回的结果。
     */
    public static ComponentResult call(String componentName, String actionName, Map<String, Object> params) {
        return ComponentClient.obtainBuilder(componentName)
                .setActionName(actionName)
                .setParams(params)
                .build().call();
    }

    /**
     * 同步调用指定组件
     *
     * @param componentName 调用的组件名
     * @param actionName    调用组件的操作名
     * @return 组件返回的结果。
     */
    public static ComponentResult call(String componentName, String actionName) {
        return ComponentClient.obtainBuilder(componentName)
                .setActionName(actionName)
                .build().call();
    }

    /**
     * 同步调用指定组件。带有一个参数
     *
     * @param componentName 调用的组件名
     * @param actionName    调用组件的操作名
     * @return 组件返回的结果。
     */
    public static ComponentResult call(String componentName, String actionName, String key, Object value) {
        return ComponentClient.obtainBuilder(componentName)
                .setActionName(actionName)
                .addParam(key, value)
                .build().call();
    }


    /**
     * 异步调用指定组件，需要回调。
     *
     * @param componentName 调用的组件名
     * @param actionName    调用组件的操作名
     * @param params        传递给调用组件的参数
     * @param callback      回调
     */
    public static void callAsync(String componentName, String actionName, Map<String, Object> params,
            ComponentCallback callback) {
        ComponentClient.obtainBuilder(componentName)
                .setActionName(actionName)
                .setParams(params)
                .build().callAsync(callback);
    }

    /**
     * 异步调用指定组件，需要回调。
     *
     * @param componentName 调用的组件名
     * @param actionName    调用组件的操作名
     * @param callback      回调
     */
    public static void callAsync(String componentName, String actionName, ComponentCallback callback) {
        ComponentClient.obtainBuilder(componentName)
                .setActionName(actionName)
                .build().callAsync(callback);
    }

    /**
     * 异步调用指定组件，不需要回调
     *
     * @param componentName 调用的组件名
     * @param actionName    调用组件的操作名
     * @param params        传递给调用组件的参数
     */
    public static void callAsync(String componentName, String actionName, Map<String, Object> params) {
        ComponentClient.obtainBuilder(componentName)
                .setActionName(actionName)
                .setParams(params)
                .build().callAsync();
    }

    /**
     * 异步调用指定组件，不需要回调
     *
     * @param componentName 调用的组件名
     * @param actionName    调用组件的操作名
     */
    public static void callAsync(String componentName, String actionName) {
        ComponentClient.obtainBuilder(componentName)
                .setActionName(actionName)
                .build().callAsync();
    }

    /**
     * 获取调用指定组件的接口的返回值，Map集合
     *
     * @param componentName 调用的组件名
     * @param actionName    调用组件的操作名
     * @param params        传递给调用组件的参数
     * @return 返回的结果
     */
    public static Map<String, Object> getResult(String componentName, String actionName, Map<String, Object> params) {
        return call(componentName, actionName, params).getData();
    }

    /**
     * 获取调用指定组件的接口的返回值，Map集合
     *
     * @param componentName 调用的组件名
     * @param actionName    调用组件的操作名
     * @return 返回的结果
     */
    public static Map<String, Object> getResult(String componentName, String actionName) {
        return call(componentName, actionName).getData();
    }

    /**
     * 根据key获取调用指定组件的接口的返回值。
     *
     * @param componentName 调用的组件名
     * @param actionName    调用组件的操作名
     * @param params        传递给调用组件的参数
     * @param key           键
     * @return 返回的结果
     */
    public static <T> T getResultValue(String componentName, String actionName, Map<String, Object> params,
            String key) {
        return (T) getResult(componentName, actionName, params).get(key);
    }

    /**
     * 根据key获取调用指定组件的接口的返回值。
     *
     * @param componentName 调用的组件名
     * @param actionName    调用组件的操作名
     * @param key           键
     * @return 返回的结果
     */
    public static <T> T getResultValue(String componentName, String actionName, String key) {
        return (T) getResult(componentName, actionName).get(key);
    }

    /**
     * 根据key获取调用指定组件的接口的返回值。 这里的key如果与actionName一样，可以直接调用该方法。
     *
     * @param componentName 调用的组件名
     * @param actionName    调用组件的操作名
     * @return 返回的结果
     */
    public static <T> T getResultValue(String componentName, String actionName) {
        return (T) getResult(componentName, actionName).get(actionName);
    }

    /**
     * 调用模块添加监听，如其他模块需要监听用户的登录状态。
     *
     * @param componentName 需要监听的模块的名称，如用户中心
     * @param component     动态组件，当监听的模块有回调时会回调到该组件的onCall方法中，不同的状态可以根据actionName区分。
     */
    public static void addObserver(String componentName, DynamicComponent component) {
        if (component != null) {
            addObserver(componentName, component, null);
        }
    }

    public static void addObserverNoRepeat(String componentName, DynamicComponent component) {
        if (component != null) {
            if(!checkDynComponentName(componentName,component.getName())){
                addObserver(componentName, component, null);
            }
        }
    }

    /**
     * 调用模块添加监听，如其他模块需要监听用户的登录状态。可以添加一些自定义的action，也可以不用，直接使用{@link #addObserver(String, DynamicComponent)}
     *
     * @param componentName    被监听的模块的名称，如用户中心
     * @param dynamicComponent 动态组件，当监听的模块有回调时会回调到该组件的onCall方法中，不同的状态可以根据actionName区分。
     * @param params           参数，可以自定义一系列的动作名
     */
    public static void addObserver(String componentName, DynamicComponent dynamicComponent,
            Map<String, Object> params) {
        if (dynamicComponent != null) {
            ComponentClient.registerComponent(dynamicComponent);

            String dynamicComponentName = dynamicComponent.getName();
            ComponentClient.obtainBuilder(componentName)
                    .setActionName(ADD_OBSERVER)
                    .addParam(COMPONENT_NAME, dynamicComponentName)
                    .addParams(params)
                    .build().call();

            List<String> dynamicComponents = COMPONENT_OBSERVERS.get(componentName);
            if (dynamicComponents == null) {
                dynamicComponents = new ArrayList<>();
                COMPONENT_OBSERVERS.put(componentName, dynamicComponents);
            }
            dynamicComponents.add(dynamicComponentName);

        }
    }

    public static boolean checkDynComponentName(String componentName,String name){
        List<String> dynamicComponents = COMPONENT_OBSERVERS.get(componentName);
        if(dynamicComponents != null){
            for(String aName:dynamicComponents){
                if(aName.equals(name))
                    return true;
            }
        }
        return false;
    }

    /**
     * 移除监听，通知调用模块移除其他模块添加的监听
     *
     * @param componentName    被监听的模块名称
     * @param dynamicComponent 需要移除的动态组件
     */
    public static void removeObserver(String componentName, DynamicComponent dynamicComponent) {
        if (dynamicComponent != null) {
            ComponentClient.unregisterComponent(dynamicComponent);

            String dynamicComponentName = dynamicComponent.getName();
            ComponentClient.obtainBuilder(componentName)
                    .setActionName(REMOVE_OBSERVER)
                    .addParam(COMPONENT_NAME, dynamicComponentName)
                    .build().call();

            List<String> dynamicComponentNames = COMPONENT_OBSERVERS.get(componentName);
            if (!ListUtil.isEmpty(dynamicComponentNames)) {
                dynamicComponentNames.remove(dynamicComponentName);
            }
        }
    }

    /**
     * 通知对应的观察者。
     *
     * @param componentName 被监听的组件名，如UserComponent
     * @param actionName    被监听的组件名的操作名。如login
     */
    public static void notifyObserver(String componentName, String actionName) {
        notifyObserver(componentName, actionName, null, false);
    }

    /**
     * 通知对应的观察者，可以设置是否在主线程回调。
     *
     * @param componentName          被监听的组件名，如UserComponent
     * @param actionName             被监听的组件名的操作名。如login
     * @param isCallbackOnMainThread 是否在主线程回调，true在主线程，false 表示原来在什么线程，回调也在什么线程
     */
    public static void notifyObserver(String componentName, String actionName, boolean isCallbackOnMainThread) {
        notifyObserver(componentName, actionName, null, isCallbackOnMainThread);
    }

    /**
     * 通知对应的观察者，可以设置参数和是否在主线程回调
     *
     * @param componentName          被监听的组件名，如UserComponent
     * @param actionName             被监听的组件名的操作名。如login
     * @param params                 传给动态组件的参数
     * @param isCallbackOnMainThread 是否在主线程回调，true在主线程，false
     */
    public static void notifyObserver(String componentName, String actionName, Map<String, Object> params,
            boolean isCallbackOnMainThread) {
        notifyObserver(componentName, actionName, params, isCallbackOnMainThread, null);
    }

    /**
     * 通知对应的观察者，可以设置参数和是否在主线程回调，回调结果
     *
     * @param componentName          被监听的组件名，如UserComponent
     * @param actionName             被监听的组件名的操作名。如login
     * @param params                 传给动态组件的参数
     * @param isCallbackOnMainThread 是否在主线程回调，true在主线程，false
     * @param callback               动态注册的组件返回的结果
     */
    public static void notifyObserver(String componentName, String actionName, Map<String, Object> params,
            boolean isCallbackOnMainThread, ComponentCallback callback) {
        List<String> dynamicComponents = COMPONENT_OBSERVERS.get(componentName);
        if (!ListUtil.isEmpty(dynamicComponents)) {
            for (String dynamicComponentName : dynamicComponents) {
                ComponentClient.obtainBuilder(dynamicComponentName)
                        .setActionName(actionName)
                        .addParams(params)
                        .isCallbackOnMainThread(isCallbackOnMainThread)
                        .build().callAsync(callback);
            }
        }
    }


}
