package com.kaolafm.kradio.clientControlerForKradio;

import static com.kaolafm.kradio.lib.utils.Constants.CLIENT_EXTRA_TYPE;
import static com.kaolafm.kradio.lib.utils.Constants.SEARCH_BY_KEYWORDS_EXTRA_TYPE;

import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import android.os.Looper;
import android.os.Process;
import android.os.RemoteException;
import android.util.Log;
import android.widget.Toast;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.clientControlerForKradio.ex.ExecuteProxy;
import com.kaolafm.kradio.common.CP;
import com.kaolafm.kradio.common.KradioSDKManager;
import com.kaolafm.kradio.common.ResultCallback;
import com.kaolafm.kradio.common.utils.PermissionUtils;
import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.base.flavor.KRadioClientInter;
import com.kaolafm.kradio.lib.bean.SubscribeData;
import com.kaolafm.kradio.lib.common.ResType;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.ClientConnectControl;
import com.kaolafm.kradio.lib.utils.IntentUtils;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.SharedPreferenceUtil;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.player.helper.intercept.HintInterceptManager;
import com.kaolafm.kradio.subscribe.RemoteSubscribeDataSource;
import com.kaolafm.kradio.subscribe.SubscribeModel;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.media.model.ToneQuality;
import com.kaolafm.opensdk.api.operation.OperationRequest;
import com.kaolafm.opensdk.api.operation.model.column.ColumnGrp;
import com.kaolafm.opensdk.api.search.SearchRequest;
import com.kaolafm.opensdk.api.search.VoiceSearchRequest;
import com.kaolafm.opensdk.api.search.model.SearchProgramBean;
import com.kaolafm.opensdk.api.search.model.VoiceSearchProgramBean;
import com.kaolafm.opensdk.api.search.model.VoiceSearchResult;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.core.listener.KLAudioStateChangedByAudioFocusListener;
import com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter;
import com.kaolafm.opensdk.player.core.utils.PlayerCustomizeManager;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.IGeneralListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.ToneQualityHelper;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.AudioSearchReportEvent;
import com.kaolafm.report.event.StartReportEvent;
import com.kaolafm.report.util.ReportConstants;
import com.kaolafm.sdk.core.ErrorInfo;
import com.kaolafm.sdk.core.IAudioFocusChangeListener;
import com.kaolafm.sdk.core.IAudioStateChangedByAudioFocusListener;
import com.kaolafm.sdk.core.IClientAPI;
import com.kaolafm.sdk.core.IExecuteResult;
import com.kaolafm.sdk.core.IPlayResult;
import com.kaolafm.sdk.core.IRadioResult;
import com.kaolafm.sdk.core.IRequestCallback;
import com.kaolafm.sdk.core.ISearchResult;
import com.kaolafm.sdk.core.ISearchResultV2;
import com.kaolafm.sdk.core.ISubscribeResult;
import com.kaolafm.sdk.core.Music;
import com.kaolafm.sdk.core.PlayListener;
import com.kaolafm.sdk.core.Radio;
import com.kaolafm.sdk.core.SearchData;
import com.kaolafm.sdk.core.ex.ErrorCode;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 **/
public class ClientImpl extends IClientAPI.Stub {
    private static final String TAG = "client.impl";
    private static final String START_TYPE = "start_type";

    private final PlayerImpl mPlayer;
    private final ExecuteProxy mExecuteProxy;

    private WeakReference<Context> refContext;
    private SubscribeModel subscribeModel;

    private OnAudioFocusChangeInter mAudioFocusListener;
    private KRadioClientInter mKRadioClientInter;

    /**
     * KRadio未激活
     */
    private static final int NO_ACTIVE_APP = -1;

    private KLAudioStateChangedByAudioFocusListener mKLAudioStateChangedByAudioFocusListener;

    public ClientImpl(Context context) {
        this.refContext = new WeakReference<Context>(context);
        mPlayer = new PlayerImpl(context);
        subscribeModel = new SubscribeModel(/*refContext.get()*/);
        mExecuteProxy = new ExecuteProxy(context);
        mKRadioClientInter = ClazzImplUtil.getInter("KRadioClientImpl");
    }

    private void startApp() {
        if (mKRadioClientInter != null && mKRadioClientInter.canIgnoreLaunchApp()) {
            Log.i(TAG, "startApp be ignored!");
            return;
        }
        Context context = refContext.get();
        if (context == null) {
            return;
        }
        Intent intent = context.getPackageManager().getLaunchIntentForPackage(context.getPackageName());
        if (intent != null) {
            intent.putExtra(CLIENT_EXTRA_TYPE, SEARCH_BY_KEYWORDS_EXTRA_TYPE);
            context.startActivity(intent);
        }
    }

    @Override
    public void launchApp(boolean b) throws RemoteException {
        Log.i(TAG, Process.myPid() + ":" + "ClientSDKService.ClientSDKImpl launchApp.");

        Context context = refContext.get();
        Intent intent = context.getPackageManager().getLaunchIntentForPackage(context.getPackageName());
        if (intent != null) {
            intent.putExtra(IntentUtils.START_TYPE, StartReportEvent.TYPE_AUDIO);
            context.startActivity(intent);
        }

        Toast.makeText(context, "launchApp start", Toast.LENGTH_LONG).show();
    }

    @Override
    public void exitApp() throws RemoteException {
        Log.i(TAG, "exitApp");
        mPlayer.stop();
        AppManager.getInstance().killAll();
    }

    @Override
    public void play() throws RemoteException {
        Log.i(TAG, "play() called");
        if (mKRadioClientInter != null) {
            if (!mKRadioClientInter.isAllowPlay()) {
                Log.i(TAG, "isAllowPlay = false");
                return;
            }
        }
        if (!checkUserServiceStatus()) {
            HintInterceptManager.getInstance().playHint2AgreeService();
            return;
        }
        if (!checkSDKStatus()) {
            // 解决https://app.huoban.com/tables/2100000007530121/items/2300001350093747?userId=1229522问题
//            launchApp(false);
            return;
        }
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                mPlayer.resume();
            }
        });

    }


    @Override
    public void playMusic(Music music) throws RemoteException {
        Log.i(TAG, "playMusic: " + music);
        if (mKRadioClientInter != null) {
            if (!mKRadioClientInter.isAllowPlay()) {
                return;
            }
        }
        if (!checkUserServiceStatus()) {
            HintInterceptManager.getInstance().playHint2AgreeService();
            return;
        }
        if (!checkSDKStatus()) {
//            launchApp(false);
            return;
        }
        if (music != null) {
            reportSearchToPlay(ReportConstants.COTENT_BY_SEARCH, music.searchCallBack);
            new Handler(Looper.getMainLooper()).post(new Runnable() {
                @Override
                public void run() {
                    checkFocusStatus();
                    mPlayer.play(DataConverter.toPlayParam(music));
                }
            });
        }
        startApp();
    }

    @Override
    public void playMusicList(List<Music> list, int i) throws RemoteException {
        Log.i(TAG, "playMusicList: " + list);
        if (mKRadioClientInter != null) {
            if (!mKRadioClientInter.isAllowPlay()) {
                return;
            }
        }
        if (!checkUserServiceStatus()) {
            HintInterceptManager.getInstance().playHint2AgreeService();
            return;
        }
        if (!checkSDKStatus()) {
//            launchApp(false);
            return;
        }
        List<ClientPlayer.PlayParam> playParams = new ArrayList<>();
        for (int j = 0; j < list.size(); j++) {
            playParams.add(DataConverter.toPlayParam(list.get(j)));
        }
        if (!ListUtil.isEmpty(list)) {
            if (list.size() > i && list.get(i) != null) {
                reportSearchToPlay(ReportConstants.COTENT_BY_SEARCH, list.get(i).searchCallBack);
            }
        }
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                checkFocusStatus();
                mPlayer.play(playParams, i);
            }
        });
        startApp();
    }

    @Override
    public void pause() throws RemoteException {
        Log.i(TAG, "pause: ");
        if (!checkUserServiceStatus()) {
            HintInterceptManager.getInstance().playHint2AgreeService();
            return;
        }
        if (!checkSDKStatus()) {
//            launchApp(false);
            return;
        }
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                mPlayer.pause();
                // 外接sdk调用暂停，按照用户点击暂停处理
                PlayerManagerHelper.getInstance().pause(true);
            }
        });
    }

    @Override
    public void playNext() throws RemoteException {
        if (mKRadioClientInter != null) {
            if (!mKRadioClientInter.isAllowPlay()) {
                return;
            }
        }
        Log.i(TAG, "playNext: ");
        if (!checkUserServiceStatus()) {
            HintInterceptManager.getInstance().playHint2AgreeService();
            return;
        }
        if (!checkSDKStatus() || !NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
//            launchApp(false);
            return;
        }
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                Log.i(TAG, "playNext run: " + Thread.currentThread().getName());
//                checkFocusStatus();
                mPlayer.playNext();
            }
        });
    }

    @Override
    public void playPre() throws RemoteException {
        if (mKRadioClientInter != null) {
            if (!mKRadioClientInter.isAllowPlay()) {
                return;
            }
        }
        Log.i(TAG, "playPre: ");
        if (!checkUserServiceStatus()) {
            HintInterceptManager.getInstance().playHint2AgreeService();
            return;
        }
        if (!checkSDKStatus() || !NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
//            launchApp(false);
            return;
        }
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001251463570?userId=1229522问题
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                Log.i(TAG, "playPre run: " + Thread.currentThread().getName());
//                checkFocusStatus();
                mPlayer.playPrev();
            }
        });
    }

    @Override
    public void setPlayListener(PlayListener playListener) throws RemoteException {
        Log.i(TAG, "setPlayListener: ");
        mPlayer.registerPlayListener(playListener);
    }

    @Override
    public void removePlayListener(PlayListener playListener) throws RemoteException {
        Log.i(TAG, "removePlayListener: ");
        mPlayer.unregisterPlayListener(playListener);
    }

    @Override
    public int getPlayState() throws RemoteException {
        int playState = mPlayer.getPlayState();
        Log.i(TAG, "getPlayState: playState = " + playState);
        return playState;
    }

    @Override
    public long getProgress() throws RemoteException {
        //Log.i(TAG, "getProgress: ");
        return mPlayer.getProgress();
    }

    @Override
    public Music getCurrentMusicInfo() throws RemoteException {
//        ClientPlayer.PlayParam playParam = mPlayer.getPlayParam();
//        Music music = DataConverter.toMusic(playParam);
        Music music = mPlayer.getMusicInfo();
        Log.i(TAG, "getCurrentMusicInfo:  music is null " + (music == null));
        return music;
    }

    @Override
    public void forward() throws RemoteException {
        Log.i(TAG, "forward: ");
        mPlayer.forward(30);
    }

    //todo 在clientSdk中添加支持
    public void forward(int seconds) throws RemoteException {
        mPlayer.forwardExactSeconds(seconds);
    }

    @Override
    public void backward() throws RemoteException {
        mPlayer.backward(30);
    }

    //todo 在clientSdk中添加支持
    public void backward(int seconds) throws RemoteException {
        mPlayer.backwardExactSeconds(seconds);
    }

    public void changeVoiceQuality(ToneQuality quality) throws RemoteException {
        ToneQualityHelper.getInstance().setToneQuality(quality);
    }

    public void jumpTo(int position) throws RemoteException {
        mPlayer.jumpTo(position);
    }

    @Override
    public void back() throws RemoteException {

    }

    @Override
    public void download() throws RemoteException {

    }

    @Override
    public void getRadioList(IRadioResult iRadioResult) throws RemoteException {
        ErrorInfo errInfo = new ErrorInfo(-111);
        errInfo.info = "KRadio不支持该方法";
        iRadioResult.onFailure(errInfo);
    }

    @Override
    public void switchChannel(Radio radio) throws RemoteException {

    }

    @Override
    public void playSearchData(SearchData searchData) throws RemoteException {
        Log.i(TAG, Process.myPid() + ":" + "ClientSDKService.ClientSDKImpl playSearchData is called");
        Log.i(TAG, Process.myPid() + ":" + "ClientSDKService.ClientSDKImpl playSearchData.searchData.getIsShowUI()" + searchData.getIsShowUI());
        checkFocusStatus();
        mPlayer.playSearchData(DataConverter.toPlayParam(searchData));
        Log.i(TAG, Process.myPid() + ":" + "ClientSDKService.ClientSDKImpl playSearchData execute");
        if (searchData.getIsShowUI() != 1) {// 1不唤起电台界面,其他值都唤起（老版本没有这个字段，json解析失败默认值会为0，兼容老版本）
            Log.i(TAG, Process.myPid() + ":" + "ClientSDKService.ClientSDKImpl playSearchData.startApp");
            startApp();
        }
    }

    @Override
    public boolean hasPre() throws RemoteException {
        Log.i(TAG, "hasPre: " + mPlayer.hasPrev());
        return mPlayer.hasPrev();
    }

    @Override
    public boolean hasNext() throws RemoteException {
        Log.i(TAG, "hasNext: " + mPlayer.hasNext());
        return mPlayer.hasNext();
    }

    @Override
    public void playByKeywords(String s, IPlayResult iPlayResult) throws RemoteException {
        Log.i(TAG, "playByKeywords: keyword=" + s);
        if (!checkUserServiceStatus()) {
            HintInterceptManager.getInstance().playHint2AgreeService();
            return;
        }
        if (!checkSDKStatus()) {
            if (iPlayResult != null) {
                ErrorInfo errorInfo = new ErrorInfo(-2);
                iPlayResult.onFailure(errorInfo);
            }
//            launchApp(false);
            return;
        }
        new VoiceSearchRequest().searchBySemantics("kaola", 0, null,
                2, 1, null, null, null, null,
                s, s, new HttpCallback<VoiceSearchResult>() {
                    @Override
                    public void onSuccess(VoiceSearchResult ssd) {
                        Log.i(TAG, "playByKeywords----->onSuccess = " + ssd);
                        if (iPlayResult != null) {
                            try {
                                List<VoiceSearchProgramBean> dataList = ssd.getProgramList();
                                ArrayList<ClientPlayer.PlayParam> result = new ArrayList<>();
                                if (dataList != null && !dataList.isEmpty()) {
                                    for (int i = 0; i < dataList.size(); i++) {
                                        VoiceSearchProgramBean dlb = dataList.get(i);
                                        ClientPlayer.PlayParam music = DataConverter.toPlayParam(dlb);
                                        if (music == null) {
                                            continue;
                                        }
                                        result.add(music);
                                    }
                                }
                                if (!ListUtil.isEmpty(dataList)) {
                                    reportSearchToPlay(ReportConstants.COTENT_BY_AUDIO, dataList.get(0).getCallback());
                                }
                                if (result.isEmpty()) {
                                    Log.w(TAG, "playByKeywords search result empty");
                                    iPlayResult.onFailure(new ErrorInfo(ErrorCode.DataIsNull));
                                } else {
                                    checkFocusStatus();
                                    mPlayer.play(result, 0);
                                    iPlayResult.onSuccuss();
                                    startApp();
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    }

                    @Override
                    public void onError(ApiException e) {
                        Log.i(TAG, "playByKeywords----->onError = " + e);
                        if (iPlayResult != null) {
                            try {
                                iPlayResult.onFailure(new ErrorInfo(e.getCode()));
                            } catch (Exception e1) {
                                e1.printStackTrace();
                            }
                        }
                    }
                });
    }

    @Override
    public void search(String s, ISearchResult iSearchResult) throws RemoteException {
        Log.i(TAG, "search: keyword=" + s);
        if (!checkSDKStatus()) {
//            launchApp(false);
            if (iSearchResult != null) {
                ErrorInfo errorInfo = new ErrorInfo(-2);
                iSearchResult.onFailure(errorInfo);
            }
            return;
        }
        new VoiceSearchRequest().searchBySemantics(
                "kaola",
                0,
                null,
                2,
                1,
                null,
                null,
                null,
                null,
                s,
                s,
                new HttpCallback<VoiceSearchResult>() {
                    @Override
                    public void onSuccess(VoiceSearchResult ssd) {
                        if (iSearchResult != null) {
                            try {
                                List<VoiceSearchProgramBean> dataList = ssd.getProgramList();
                                ArrayList<Music> result = new ArrayList<>();
                                if (dataList != null && !dataList.isEmpty()) {
                                    for (int i = 0; i < dataList.size(); i++) {
                                        VoiceSearchProgramBean dlb = dataList.get(i);
                                        Music music = DataConverter.toMusic(dlb);
                                        if (music == null) {
                                            continue;
                                        }
                                        //Log.e("novelot", String.format("%d[%s,%s,%d]", i, music.audioName, music.albumName, music.albumId == 0 ? music.audioId : music.audioId));
                                        result.add(music);
                                    }
                                }
                                iSearchResult.onSuccess(result);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    }

                    @Override
                    public void onError(ApiException e) {
                        if (iSearchResult != null) {
                            try {
                                iSearchResult.onFailure(new ErrorInfo(e.getCode()));
                            } catch (Exception e1) {
                                e1.printStackTrace();
                            }
                        }
                    }
                });
    }

    @Override
    public void showSearchResult(List<Music> list, String s) throws RemoteException {

    }

    @Override
    public void searchByType(int type, final String keywords, int page, int count, ISearchResultV2 iSearchResultV2) throws RemoteException {
        Log.i(TAG, Process.myPid() + ":" + "ClientSDKService.ClientSDKImpl searchByType.");
        if (!checkSDKStatus()) {
            if (iSearchResultV2 != null) {
                ErrorInfo errorInfo = new ErrorInfo(-2);
                iSearchResultV2.onFailure(errorInfo);
            }
//            launchApp(false);
            return;
        }
        new SearchRequest().searchByType(
                keywords, type, page, count,
                new HttpCallback<BasePageResult<List<SearchProgramBean>>>() {
                    @Override
                    public void onSuccess(BasePageResult<List<SearchProgramBean>> ssd) {
                        if (iSearchResultV2 != null) {
                            try {
                                List<SearchProgramBean> dataList = ssd.getDataList();
                                ArrayList<SearchData> result = new ArrayList<>();
                                if (dataList != null && !dataList.isEmpty()) {
                                    for (int i = 0; i < dataList.size(); i++) {
                                        SearchProgramBean dlb = dataList.get(i);
                                        SearchData music = DataConverter.toSearchData(dlb);
                                        if (music == null) {
                                            continue;
                                        }
                                        result.add(music);
                                    }
                                }
                                iSearchResultV2.onSuccess(result);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    }

                    @Override
                    public void onError(ApiException e) {
                        if (iSearchResultV2 != null) {
                            try {
                                iSearchResultV2.onFailure(new ErrorInfo(e.getCode()));
                            } catch (Exception e1) {
                                e1.printStackTrace();
                            }
                        }
                    }
                });
    }

    @Override
    public void search2(int qualityType, int field, int credibility,
                        String artist,
                        String audioName,
                        String albumName,
                        String category,
                        String keywords,
                        String text, ISearchResult iSearchResult) throws RemoteException {
        Log.i(TAG, "search2: ");
        search3("kaola", qualityType, field,
                credibility, artist, audioName, albumName, category, keywords, text, iSearchResult);
    }

    @Override
    public void search3(String voiceSource, int qualityType, int field, int credibility,
                        String artist,
                        String audioName,
                        String albumName,
                        String category,
                        String keywords,
                        String text, ISearchResult iSearchResult) throws RemoteException {
        Log.i(TAG, "search3: ");
        if (!checkSDKStatus()) {
            if (iSearchResult != null) {
                ErrorInfo errorInfo = new ErrorInfo(-2);
                iSearchResult.onFailure(errorInfo);
            }
//            launchApp(false);
            return;
        }
        new VoiceSearchRequest().searchBySemantics(voiceSource, qualityType, null,
                field, credibility, artist, audioName, albumName, category,
                keywords, text, new HttpCallback<VoiceSearchResult>() {
                    @Override
                    public void onSuccess(VoiceSearchResult ssd) {
                        if (iSearchResult != null) {
                            try {
                                List<VoiceSearchProgramBean> dataList = ssd.getProgramList();
                                ArrayList<Music> result = new ArrayList<>();
                                if (dataList != null && !dataList.isEmpty()) {
                                    for (int i = 0; i < dataList.size(); i++) {
                                        VoiceSearchProgramBean dlb = dataList.get(i);
                                        Music music = DataConverter.toMusic(dlb);
                                        if (music == null) {
                                            continue;
                                        }
                                        //Log.e("novelot", String.format("%d[%s,%s,%d]", i, music.audioName, music.albumName, music.albumId == 0 ? music.audioId : music.audioId));
                                        result.add(music);
                                    }
                                }
                                iSearchResult.onSuccess(result);
                                String searchResult = AudioSearchReportEvent.RESULT_EMPTY;
                                if (!ListUtil.isEmpty(result)) {
                                    searchResult = AudioSearchReportEvent.RESULT_SUCCESS;
                                }
                                reportSearchResult(searchResult, text, keywords, "0");
                            } catch (Exception e) {
                                e.printStackTrace();
                                reportSearchResult(AudioSearchReportEvent.RESULT_EMPTY, text, keywords, "0");
                            }
                        }
                    }

                    @Override
                    public void onError(ApiException e) {
                        if (iSearchResult != null) {
                            try {
                                iSearchResult.onFailure(new ErrorInfo(e.getCode()));
                            } catch (Exception e1) {
                                e1.printStackTrace();
                            }
                        }
                        reportSearchResult(AudioSearchReportEvent.RESULT_ERROR, text, keywords, "0");
                    }
                });
    }

    /**
     * copied from playByKeywords()，use SearchRequest to replace VoiceSearchRequest
     *
     * @param voiceSource 必填 语音来源 公司标识,考拉:kaola;同行者:txzing;思必驰:sibichi;问问:wenwen;蓦然:moran;科大 讯飞:kedaxunfei;
     * @param qualityType 可填 音频质量要求,0:低;1:高;
     * @param field       必填 场景类别 1：音乐，2：综合; 6: 传统广播
     * @param credibility 可填 参数可信标识 0：不可信，1：可信；为1时，表示场景分类和其他字段等信息可信度高。
     * @param artist      可填 艺术家
     * @param audioName   可填 音频名称
     * @param albumName   可填 专辑名称
     * @param category    可填 分类
     * @param keywords    必填 关键词 多个关键词以英文逗号“,”分隔
     * @param text        必填 用户声控的原始串
     * @throws RemoteException
     */
    public void search4(String voiceSource, int qualityType, int field, int credibility,
                        String artist,
                        String audioName,
                        String albumName,
                        String category,
                        String keywords,
                        String text, IPlayResult iPlayResult) throws RemoteException {
        Log.i(TAG, "search4: ");
        if (!checkSDKStatus()) {
            if (iPlayResult != null) {
                ErrorInfo errorInfo = new ErrorInfo(-2);
                iPlayResult.onFailure(errorInfo);
            }
            return;
        }
        new SearchRequest().searchBySemantics(voiceSource, qualityType, null,
                field, credibility, artist, audioName, albumName, category,
                keywords, text, null, null, null, new HttpCallback<VoiceSearchResult>() {
                    @Override
                    public void onSuccess(VoiceSearchResult ssd) {
                        if (iPlayResult != null) {
                            try {
                                List<VoiceSearchProgramBean> dataList = ssd.getProgramList();
                                ArrayList<ClientPlayer.PlayParam> result = new ArrayList<>();
                                if (dataList != null && !dataList.isEmpty()) {
                                    for (int i = 0; i < dataList.size(); i++) {
                                        VoiceSearchProgramBean dlb = dataList.get(i);
                                        ClientPlayer.PlayParam music = DataConverter.toPlayParam(dlb);
                                        if (music == null) {
                                            continue;
                                        }
                                        result.add(music);
                                    }
                                }
                                if (!ListUtil.isEmpty(dataList)) {
                                    reportSearchToPlay(ReportConstants.COTENT_BY_AUDIO, dataList.get(0).getCallback());
                                }
                                checkFocusStatus();
                                mPlayer.play(result, 0);
                                iPlayResult.onSuccuss();
                            } catch (Exception e) {
                                e.printStackTrace();
                                reportSearchResult(AudioSearchReportEvent.RESULT_EMPTY, text, keywords, "0");
                            }
                        }
                    }

                    @Override
                    public void onError(ApiException e) {
                        if (iPlayResult != null) {
                            try {
                                iPlayResult.onFailure(new ErrorInfo(e.getCode()));
                            } catch (Exception e1) {
                                e1.printStackTrace();
                            }
                        }
                    }
                });
    }

    /**
     * copied from playByKeywords()，use SearchRequest to replace VoiceSearchRequest
     *
     * @param voiceSource 必填 语音来源 公司标识,考拉:kaola;同行者:txzing;思必驰:sibichi;问问:wenwen;蓦然:moran;科大 讯飞:kedaxunfei;
     * @param qualityType 可填 音频质量要求,0:低;1:高;
     * @param field       必填 场景类别 1：音乐，2：综合; 6: 传统广播
     * @param credibility 可填 参数可信标识 0：不可信，1：可信；为1时，表示场景分类和其他字段等信息可信度高。
     * @param artist      可填 艺术家
     * @param audioName   可填 音频名称
     * @param albumName   可填 专辑名称
     * @param category    可填 分类
     * @param keywords    必填 关键词 多个关键词以英文逗号“,”分隔
     * @param text        必填 用户声控的原始串
     * @throws RemoteException
     */
    public void search5(String voiceSource, int qualityType, int field, int credibility,
                        String artist,
                        String audioName,
                        String albumName,
                        String category,
                        String keywords,
                        String text, IPlayResult iPlayResult) throws RemoteException {
        Log.i(TAG, "search5: ");
        final ErrorInfo errorInfo = new ErrorInfo(-2);
        final IGeneralListener errorListener = new IGeneralListener() {

            @Override
            public void getPlayListError(PlayItem playItem, int i, int i1) {
                Log.i(TAG, "getPlayListError, 该资源暂不支持播放");
                errorInfo.info = "该资源暂不支持播放";
                try {
                    iPlayResult.onFailure(errorInfo);
                } catch (RemoteException remoteException) {
                    remoteException.printStackTrace();
                }
            }

            @Override
            public void playUrlError(int i) {
                Log.i(TAG, "playUrlError, 该资源暂不支持播放");
                errorInfo.info = "该资源暂不支持播放";
                try {
                    iPlayResult.onFailure(errorInfo);
                } catch (RemoteException remoteException) {
                    remoteException.printStackTrace();
                }
            }
        };
        if (!checkSDKStatus()) {
            errorInfo.info = "初始化失败";
            iPlayResult.onFailure(errorInfo);
            return;
        }
        new SearchRequest().searchBySemantics(voiceSource, qualityType, null,
                field, credibility, artist, audioName, albumName, category,
                keywords, text, null, null, null, new HttpCallback<VoiceSearchResult>() {
                    @Override
                    public void onSuccess(VoiceSearchResult ssd) {
                        try {
                            List<VoiceSearchProgramBean> dataList = ssd.getProgramList();
                            ArrayList<ClientPlayer.PlayParam> result = new ArrayList<>();
                            if (dataList != null && !dataList.isEmpty()) {
                                for (int i = 0; i < dataList.size(); i++) {
                                    VoiceSearchProgramBean dlb = dataList.get(i);
                                    ClientPlayer.PlayParam music = DataConverter.toPlayParam(dlb);
                                    if (music == null) {
                                        continue;
                                    }
                                    result.add(music);
                                }
                            }
                            if (!ListUtil.isEmpty(dataList)) {
                                reportSearchToPlay(ReportConstants.COTENT_BY_AUDIO, dataList.get(0).getCallback());

                                checkFocusStatus();
                                PlayerManager.getInstance().addGeneralListener(errorListener);
                                mPlayer.play(result, 0);
                                new Handler().postDelayed(new Runnable() {
                                    @Override
                                    public void run() {
                                        PlayerManager.getInstance().removeGeneralListener(errorListener);
                                    }
                                }, 1000);
                            } else {
                                Log.i(TAG, "search5 未搜到结果1");
                                errorInfo.info = "未搜到结果";
                                iPlayResult.onFailure(errorInfo);
                            }

                        } catch (Exception e) {
                            e.printStackTrace();
                            reportSearchResult(AudioSearchReportEvent.RESULT_EMPTY, text, keywords, "0");
                            Log.i(TAG, "search5 未搜到结果2");
                            errorInfo.info = "未搜到结果";
                            try {
                                iPlayResult.onFailure(errorInfo);
                            } catch (RemoteException remoteException) {
                                remoteException.printStackTrace();
                            }
                        }
                    }

                    @Override
                    public void onError(ApiException e) {
                        if (iPlayResult != null) {
                            try {
                                Log.i(TAG, "search5 网络异常");
                                errorInfo.info = "网络异常";
                                iPlayResult.onFailure(errorInfo);
                            } catch (Exception e1) {
                                e1.printStackTrace();
                            }
                        }
                    }
                });
    }

    @Override
    public void registerAudioStateChangedByAudioFocusListener(IAudioStateChangedByAudioFocusListener iAudioStateChangedByAudioFocusListener) throws RemoteException {
        Log.i(TAG, "registerAudioStateChangedByAudioFocusListener:" + iAudioStateChangedByAudioFocusListener);
        PlayerCustomizeManager playerCustomizeManager = PlayerCustomizeManager.getInstance();
        if (iAudioStateChangedByAudioFocusListener != null) {
            if (mKLAudioStateChangedByAudioFocusListener == null) {
                mKLAudioStateChangedByAudioFocusListener = new KLAudioStateChangedByAudioFocusListener() {
                    @Override
                    public void onAudioStatePausedByLossAudioFocus() {
                        Log.i(TAG, "onAudioStatePausedByLossAudioFocus start");
                        try {
                            iAudioStateChangedByAudioFocusListener.onAudioStatePausedByLossAudioFocus();
                        } catch (RemoteException e) {
                            e.printStackTrace();
                        }
                    }

                    @Override
                    public void onAudioStatePlayingByGainAudioFocus() {
                        Log.i(TAG, "onAudioStatePlayingByGainAudioFocus start");
                        try {
                            iAudioStateChangedByAudioFocusListener.onAudioStatePlayingByGainAudioFocus();
                        } catch (RemoteException e) {
                            e.printStackTrace();
                        }
                    }
                };
            } else {
                playerCustomizeManager.unregisterAudioStateChangedByAudioFocusListener(mKLAudioStateChangedByAudioFocusListener);
            }
            playerCustomizeManager.registerAudioStateChangedByAudioFocusListener(mKLAudioStateChangedByAudioFocusListener);
        } else {
            playerCustomizeManager.unregisterAudioStateChangedByAudioFocusListener(mKLAudioStateChangedByAudioFocusListener);
        }
    }

    @Override
    public void unregisterAudioStateChangedByAudioFocusListener(IAudioStateChangedByAudioFocusListener iAudioStateChangedByAudioFocusListener) throws RemoteException {
        // TODO: 2020-04-28 zc 这块合并代码新出现的, 等和少宁商量
        // PlayerManager PlayerManager = PlayerManager.getInstance();
        // PlayerManager.unregisterAudioStateChangedByAudioFocusListener(mKLAudioStateChangedByAudioFocusListener);
    }

    @Override
    public void clientDied() throws RemoteException {
        Log.i(TAG,"Client died");
    }

    @Override
    public void requestAuthorization() throws RemoteException {
        Context context = refContext.get();
        //检查权限
        boolean flag = new PermissionUtils(context).isGrant();
        //检查授权
        boolean isShow = !ClientConnectControl.instance.isProtocolReceived();
        if(!isShow && flag){
            //发送广播
            ClientConnectControl.instance.notifyProtocolReceived();
        }else {
            ClientConnectControl.instance.notifyProtocolRejected();
        }
    }

    @Override
    public void subscribe(long id, ISubscribeResult iSubscribeResult) throws RemoteException {
        Log.i(TAG, "subscribe: id=" + id);
        if (!checkSDKStatus()) {
            if (iSubscribeResult != null) {
                ErrorInfo errorInfo = new ErrorInfo(-2);
                iSubscribeResult.onFailure(errorInfo);
            }
//            launchApp(false);
            return;
        }

        ClientPlayer.PlayParam playParam = mPlayer.getPlayParam();

        if (id < 0) {
            id = playParam.id;
            Log.i(TAG, "    : subscribe.id=" + id);
        }

        if (playParam.resType == ResType.ALBUM_TYPE
                || playParam.resType == ResType.RADIO_TYPE
                || playParam.resType == ResType.BROADCAST_TYPE
                || playParam.resType == ResType.AUDIO_TYPE) {

            subscribeModel.changeSubscribeManager(CP.KaoLaFM);
            SubscribeData sd = new SubscribeData();
            sd.setType(PlayerManagerHelper.getInstance().getCurPlayItem().getType());
            sd.setId(id);


            subscribeModel.subscribe(sd, new ResultCallback() {
                @Override
                public void onResult(boolean result, int status) {
                    //如果订阅成功,则设置为true;否则,不操作.
                    if (iSubscribeResult != null) {
                        //
                        if (status == RemoteSubscribeDataSource.STATUS_SUCCESS) {
                            //更改订阅状态;弹toast;
                            try {
                                iSubscribeResult.onSuccuss();
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        } else if (RemoteSubscribeDataSource.SUBSCRIBED_STATUS == status) {
                            try {
                                iSubscribeResult.onSuccuss();
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        } else {
                            ErrorInfo ei = new ErrorInfo(-2);
                            ei.info = "收藏失败";
                            try {
                                iSubscribeResult.onFailure(ei);
                            } catch (Exception e) {
                                Log.i(TAG, Process.myPid() + ":" + "ClientSDKService.subscribe: onError=" + ei);
                            }
                        }
                    }
                }

                @Override
                public void onFailure(com.kaolafm.kradio.common.ErrorInfo errorInfo) {
                    if (iSubscribeResult != null) {
                        ErrorInfo ei = new ErrorInfo(errorInfo.code);
                        ei.info = "收藏失败";
                        try {
                            iSubscribeResult.onFailure(ei);
                        } catch (Exception e) {
                            Log.i(TAG, Process.myPid() + ":" + "ClientSDKService.subscribe: onError=" + ei);
                        }
                    }
                }
            });
        }

    }

    @Override
    public void unsubscribe(long id, ISubscribeResult iSubscribeResult) throws RemoteException {
        Log.i(TAG, "unsubscribe: id=" + id);
        if (!checkSDKStatus()) {
            if (iSubscribeResult != null) {
                ErrorInfo errorInfo = new ErrorInfo(-2);
                iSubscribeResult.onFailure(errorInfo);
            }
            return;
        }
        ClientPlayer.PlayParam playParam = mPlayer.getPlayParam();

        if (id <= 0) {
            id = playParam.id;
            Log.i(TAG, "    unsubscribe: id=" + id);
        }

        if (playParam.resType == ResType.ALBUM_TYPE
                || playParam.resType == ResType.RADIO_TYPE
                || playParam.resType == ResType.BROADCAST_TYPE
                || playParam.resType == ResType.AUDIO_TYPE) {
            //subscribeModel.changeSubscribeManager(CP.KaoLaFM);
            SubscribeData sd = new SubscribeData();
            sd.setType(PlayerManagerHelper.getInstance().getCurPlayItem().getType());
            sd.setId(id);


            subscribeModel.unsubscribe(sd, new ResultCallback() {
                @Override
                public void onResult(boolean result, int status) {
                    //如果订阅成功,则设置为true;否则,不操作.
                    if (iSubscribeResult != null) {
                        //
                        if (status == RemoteSubscribeDataSource.STATUS_SUCCESS) {
                            //更改订阅状态;弹toast;
                            try {
                                iSubscribeResult.onSuccuss();
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        } else if (RemoteSubscribeDataSource.SUBSCRIBED_STATUS == status) {
                            try {
                                iSubscribeResult.onSuccuss();
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        } else {
                            ErrorInfo ei = new ErrorInfo(-2);
                            ei.info = "收藏失败";
                            try {
                                iSubscribeResult.onFailure(ei);
                            } catch (Exception e) {
                                Log.i(TAG, Process.myPid() + ":" + "ClientSDKService.subscribe: onError=" + e);
                            }
                        }
                    }
                }

                @Override
                public void onFailure(com.kaolafm.kradio.common.ErrorInfo errorInfo) {
                    if (iSubscribeResult != null) {
                        ErrorInfo ei = new ErrorInfo(errorInfo.code);
                        ei.info = "收藏失败";
                        try {
                            iSubscribeResult.onFailure(ei);
                        } catch (Exception e) {
                            Log.i(TAG, Process.myPid() + ":" + "ClientSDKService.subscribe: onError=" + e);
                        }
                    }
                }
            });

        }
    }

    @Override
    public int getCurrentFocusChangeState() throws RemoteException {
        return PlayerManager.getInstance().getCurrentAudioFocusStatus();
    }

    @Override
    public void setAudioFocusChangeListener(IAudioFocusChangeListener iAudioFocusChangeListener) throws RemoteException {
        Log.i(TAG, "setAudioFocusChangeListener:" + iAudioFocusChangeListener);
        if (iAudioFocusChangeListener == null) {
            return;
        }

        if (mAudioFocusListener == null) {
            mAudioFocusListener = i -> {
                try {
                    iAudioFocusChangeListener.onAudioFocusChange(i);
                    mPlayer.onPlayStateChangeInAudioFocusChange(i);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            };
        } else {
            PlayerManager.getInstance().removeAudioFocusListener(mAudioFocusListener);
        }
        PlayerManager.getInstance().addAudioFocusListener(mAudioFocusListener);
    }


    @Override
    public void executeWithCallback(String s, final IExecuteResult iExecuteResult) throws RemoteException {
        if (mExecuteProxy != null) {
            mExecuteProxy.execute(s, iExecuteResult);
        }

    }

    @Override
    public String execute(String s) throws RemoteException {
//        if (!checkSDKStatus()) {
////            launchApp(false);
//            return null;
//        }
        if (mExecuteProxy != null) {
            return mExecuteProxy.execute(s);
        } else {
            return null;
        }
    }

    private void reportSearchResult(String result, String text, String query, String playType) {
        AudioSearchReportEvent reportEvent = new AudioSearchReportEvent();
        reportEvent.setText(text);
        reportEvent.setResult(result);
        reportEvent.setRemarks1(query);
        reportEvent.setPlaytype(playType);
        ReportHelper.getInstance().addEvent(reportEvent, false);
    }

    private void reportSearchToPlay(String playType, String callback) {
        ReportHelper.getInstance().setSearchAudioPlayCallBack(playType, callback);
    }

    private boolean checkSDKStatus() {
//        return KRadioApplication.SDKInit && OpenSDK.getInstance().isActivate();
        boolean flag = KradioSDKManager.getInstance().isUsable();
        Log.i(TAG, "checkSDKStatus isUsable = " + flag);
        return flag;
    }

    /**
     * 判断是否已同意用户协议
     *
     * @return agree
     */
    private boolean checkUserServiceStatus() {
        return ClientConnectControl.instance.isProtocolReceived();
    }

    private void checkFocusStatus() {
        int currentFocus = PlayerManager.getInstance().getCurrentAudioFocusStatus();
        Log.i(TAG, "checkFocusStatus: currentFocus = " + currentFocus);
        if (currentFocus <= 0) {// currentFocus为0时没有应用持有音频焦点，也需要申请
            PlayerManager.getInstance().requestAudioFocus();
        }
    }

    @Override
    public void getCells(String zone, IRequestCallback callback) throws RemoteException {
        new OperationRequest().getColumnTree(true, zone, new HttpCallback<List<ColumnGrp>>() {
            @Override
            public void onSuccess(List<ColumnGrp> columnGrps) {
                if (callback != null) {
                    try {
                        List<HomeCell> dataList = com.kaolafm.kradio.home.comprehensive.data.DataConverter.toHomeCells(columnGrps);
                        ArrayList<SearchData> result = new ArrayList<SearchData>();
                        if (dataList != null && !dataList.isEmpty()) {
                            for (int i = 0; i < dataList.size(); i++) {
                                HomeCell dlb = dataList.get(i);
                                SearchData music = DataConverter.cellToSearchData(dlb);
                                if (music == null) {
                                    continue;
                                }
                                result.add(music);
                            }
                        }
                        callback.onSuccess(result);
                    } catch (Exception e1) {
                        e1.printStackTrace();
                    }
                }
            }

            @Override
            public void onError(ApiException e) {
                if (callback != null) {
                    try {
                        callback.onFailure(new ErrorInfo(e.getCode()));
                    } catch (Exception e1) {
                        e1.printStackTrace();
                    }
                }
            }
        });
    }
}
