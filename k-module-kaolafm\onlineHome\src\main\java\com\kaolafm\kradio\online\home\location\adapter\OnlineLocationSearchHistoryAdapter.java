package com.kaolafm.kradio.online.home.location.adapter;

import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.kaolafm.kradio.k_kaolafm.R; 
import com.kaolafm.kradio.lib.base.flavor.KRadioSearchInter;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.online.home.location.bean.OnlineRecomandCityBean;
 

public class OnlineLocationSearchHistoryAdapter extends BaseAdapter<OnlineRecomandCityBean> {

    @Override
    protected BaseHolder<OnlineRecomandCityBean> getViewHolder(ViewGroup parent, int viewType) {
        return new LoactionSearchHistoryHolder(inflate(parent, R.layout.online_item_loaction_search_history, viewType));
    }

    public class LoactionSearchHistoryHolder extends BaseHolder<OnlineRecomandCityBean> {
      
        TextView mTvSearchHistoryTag;

        public LoactionSearchHistoryHolder(View itemView) {
            super(itemView);
            mTvSearchHistoryTag = itemView.findViewById(R.id.tv_search_history_tag);
            
            KRadioSearchInter kRadioSearchInter = ClazzImplUtil.getInter("KRadioSearchImpl");
            if (kRadioSearchInter != null) {
                kRadioSearchInter.configSearchHisItem(mTvSearchHistoryTag);
            }
        }

        @Override
        public void setupData(OnlineRecomandCityBean onlineRecomandCityBean, int position) {
//            if (!TextUtils.isEmpty(onlineRecomandCityBean.name)) {
                mTvSearchHistoryTag.setText(onlineRecomandCityBean.name);
//            }
        }
    }
}
