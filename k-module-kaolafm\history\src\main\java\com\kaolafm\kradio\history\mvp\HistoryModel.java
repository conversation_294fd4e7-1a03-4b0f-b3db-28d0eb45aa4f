package com.kaolafm.kradio.history.mvp;

import com.kaolafm.kradio.lib.bean.HistoryItem;
import com.kaolafm.kradio.history.HistoryManager;
import com.kaolafm.kradio.lib.basedb.GreenDaoInterface.OnQueryListener;
import com.kaolafm.kradio.lib.base.mvp.BaseModel;
import com.kaolafm.opensdk.api.personalise.PersonalizedRequest;
import com.kaolafm.opensdk.api.personalise.model.HotRecommend;
import com.kaolafm.opensdk.http.core.HttpCallback;
import java.util.List;

/**
 * Created by kaolafm on 2018/5/2.
 */

public class HistoryModel extends BaseModel {

    private static final int MAX_NUM_UNLOGIN = 100;

    private HistoryManager mHistoryManager;
    private PersonalizedRequest mPersonalizedRequest;

    public HistoryModel() {
        mHistoryManager = HistoryManager.getInstance();
        mPersonalizedRequest = new PersonalizedRequest().setTag(this.toString());
    }

    public void clearHistory(HttpCallback<Boolean> callback) {
        mHistoryManager.clear(callback);
    }

    @Override
    public void destroy() {
    }
    public void getHotRecommend(HttpCallback<HotRecommend> callback) {
        mPersonalizedRequest.getHotRecommend(callback);
    }
    public void getHistoryList(boolean login, HttpCallback<List<HistoryItem>> callback) {
        mHistoryManager.getHistoryListWithLogin(login, callback);
    }

    public void setListener(OnQueryListener<Boolean> listener) {
        mHistoryManager.setListener(listener);
    }
}
