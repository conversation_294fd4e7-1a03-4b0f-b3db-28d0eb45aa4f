package com.kaolafm.kradio.auto.appwidget.remoteviews;

import android.app.PendingIntent;
import android.appwidget.AppWidgetManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.text.TextUtils;
import android.util.Log;
import android.widget.RemoteViews;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.auto.appwidget.KLAppWidgetOne;
import com.kaolafm.kradio.common.bean.HistoryItem;
import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.basedb.manager.HistoryDaoManager;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.IntentUtils;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

public class RemoteView_One implements IRemoteViews {
    public PlayItem playItem;
    public Bitmap mCurrentBitmap;
    public Bitmap mBlurBitmap;
    public String livingTime = "";
    public boolean subscription = false;
    private Context context = AppDelegate.getInstance().getContext();

    @Override
    public void setRemoteViews(RemoteData remoteData) {
        playItem = remoteData.mPlayItem;
        mCurrentBitmap = remoteData.mCurrentBitmap;
        mBlurBitmap = remoteData.mBlurBitmap;
        livingTime = remoteData.livingTime;
        subscription = remoteData.subscription;

        final AppWidgetManager manager = AppWidgetManager.getInstance(context);
        final ComponentName componentName = new ComponentName(context, KLAppWidgetOne.class);
        RemoteViews views = new RemoteViews(context.getPackageName(), R.layout.widget_layout_one);

        //设置点击封面进入应用
        Intent launchAppIntent = new IntentUtils().getLauncherIntentUseWidget(context);
        views.setOnClickPendingIntent(R.id.widget_layout_one_main_layout, PendingIntent.getActivity(context, 0, launchAppIntent, PendingIntent.FLAG_CANCEL_CURRENT));
        Log.i(TAG, "RemoteView_One playItem = " + playItem);
        if (playItem != null) {
            updateByPlayItem(playItem, views);
            if (remoteData.getAppWidgetId() == AppWidgetManager.INVALID_APPWIDGET_ID) {
                manager.updateAppWidget(  componentName  , views);
            } else {
                manager.updateAppWidget(  remoteData.getAppWidgetId() , views);
            }

        }
//        else {
//            HistoryDaoManager.getInstance().queryHistoryListOrderByTime(1, historyItems -> {
//                if (!ListUtil.isEmpty(historyItems)) {
//                    HistoryItem historyItem = historyItems.get(0);
//                    PlayItem historyPlayItem = HistoryUtils.turnPlayItem(historyItem);
//                    updateByPlayItem(historyPlayItem, views);
//                }
//                if (remoteData.getAppWidgetId() == AppWidgetManager.INVALID_APPWIDGET_ID) {
//                    manager.updateAppWidget(  componentName  , views);
//                } else {
//                    manager.updateAppWidget(  remoteData.getAppWidgetId() , views);
//                }
//            });
//        }
    }


    private void updateByPlayItem(PlayItem playItem, RemoteViews views) {

        if (playItem != null) {
            Log.i(TAG, "updateByPlayItem：" + playItem.getTitle());
            //设置碎片名称
            CharSequence widgetText = TextUtils.isEmpty(playItem.getSourceName())?playItem.getTitle():playItem.getSourceName();
            if (!TextUtils.isEmpty(widgetText)) {
                views.setTextViewText(R.id.widget_audio_name, widgetText);
            } else {
                views.setTextViewText(R.id.widget_audio_name, "暂无节目信息");
            }
            //设置专辑名称
            CharSequence albumName = playItem.getAlbumTitle();
            if (!TextUtils.isEmpty(albumName)) {
                views.setTextViewText(R.id.widget_album_name, albumName);
            }
        } else {

        }
    }
}
