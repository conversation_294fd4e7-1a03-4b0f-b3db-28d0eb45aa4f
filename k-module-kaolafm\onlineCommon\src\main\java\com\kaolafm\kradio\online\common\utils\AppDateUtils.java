package com.kaolafm.kradio.online.common.utils;

import android.content.Context;
import android.text.SpannableStringBuilder;
import android.text.Spanned;

import com.google.gson.Gson;
import com.kaolafm.base.utils.SpUtil;
import com.kaolafm.kradio.basedb.entity.meaasge.CrashMessageBean;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.common.utils.OnlineConstants;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.opensdk.api.CrashMessageBaseBean;

public class AppDateUtils {
    private static AppDateUtils appDateUtils;

    public static AppDateUtils getInstance() {
        if (appDateUtils == null) {
            synchronized (AppDateUtils.class) {
                if (appDateUtils == null) {
                    appDateUtils = new AppDateUtils();
                }
            }
        }
        return appDateUtils;
    }

    /**
     * 把数据库数据装换成bean
     *
     * @param crashMessageBean
     * @return
     */
    public CrashMessageBaseBean changeDate(CrashMessageBean crashMessageBean) {
        CrashMessageBaseBean baseBean = null;
        String json = new Gson().toJson(crashMessageBean);
        baseBean = new Gson().fromJson(json, CrashMessageBaseBean.class);
        return baseBean;

    }

    /**
     * 通过设置开关确认是否可以播放泡泡消息
     *
     * @param bean
     * @return
     */
    public boolean isConfirmPlayMsg(CrashMessageBean bean) {
        boolean b = false;
        boolean voiceSpeakState = SpUtil.getInt(OnlineConstants.VOICE_SPEAK, 0) > 1;
        boolean voiceAssistantState = SpUtil.getInt(OnlineConstants.VOICE_ASSISTANT, 0) > 1;
        boolean travelServiceState = SpUtil.getInt(OnlineConstants.TRAVEL_SERVICE, 0) > 1;
        //消息等级 1-情感化问候、车载服务类消息 2-节目预约、社群等主动交互类消息 3-云听应急广播消息
        switch (bean.getMsgLevel()) {
            case "1"://出行
                //出行按单独开关为准
                b = travelServiceState;
                break;
            case "2"://小助手
                b = voiceAssistantState;
                break;
            default:
                b = voiceSpeakState;
                break;
        }
        return b;
    }

    /**
     * 收听时长格式化
     * 不足0.5小时按0.5小时 超过0.5小时不足1小时按1小时
     *
     * @return
     */
    public String userDurationFormat(long duration) {
        if (duration == 0) {
            return "0";
        }
        String time="0";
        double cumPlaytime = (duration / 1000d / 60d) / 60;
        int o = ((int) cumPlaytime);//整数部分
        double p = cumPlaytime - o;//小数部分
        if (p <= 0.5) {
            time = (o + 0.5)+"";
        } else {
            time = (o + 1)+"";
        }
        return time;
    }

    /**
     * 生成渐变文字
     * @param context
     * @param string
     * @return
     */
    public SpannableStringBuilder getRadiusGradientSpan(Context context,String string) {
        SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder(string);
        LinearGradientFontSpan span = new LinearGradientFontSpan(ResUtil.getColor(R.color.online_tab_text_start)
                , ResUtil.getColor(R.color.online_tab_text_end));
        spannableStringBuilder.setSpan(span, 0, spannableStringBuilder.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spannableStringBuilder;

    }
}
