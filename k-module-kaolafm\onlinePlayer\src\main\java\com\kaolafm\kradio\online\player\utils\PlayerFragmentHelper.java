package com.kaolafm.kradio.online.player.utils;

import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import com.kaolafm.kradio.common.CP;
import com.kaolafm.kradio.common.ErrorInfo;
import com.kaolafm.kradio.common.ResultCallback;
import com.kaolafm.kradio.common.SubscribeData;
import com.kaolafm.kradio.common.event.PlayerSubscribeResultEBData;
import com.kaolafm.kradio.component.DynamicComponent;
import com.kaolafm.kradio.component.RealCaller;
import com.kaolafm.kradio.constant.UserComponentConst;
import com.kaolafm.kradio.constant.UserStateObserverProcessorConst;
import com.kaolafm.kradio.lib.utils.ComponentUtil;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.kradio.online.player.mvp.OnlinePlayerBasePresenter;
import com.kaolafm.kradio.online.player.mvp.OnlineRadioPlayerPresenter;
import com.kaolafm.kradio.online.player.pages.OnlineAlbumPlayListRebuildFragment;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.subscribe.SubscribeHelper;
import com.kaolafm.kradio.user.UserInfoManager;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.LoginReportEvent;
import com.kaolafm.report.event.SubscibeReportEvent;

import org.greenrobot.eventbus.EventBus;

import me.yokeyword.fragmentation.SupportFragment;

/**
 * 从{@link OnlineAlbumPlayListRebuildFragment} 提取出来了一些辅助工具方法
 *
 * <AUTHOR> Huangui
 */
public class PlayerFragmentHelper {

    public static final String TAG = "kradio.player";

    DynamicComponent mUserStateObserver;
    private final int STATE_NORMAL = 1;
    private final int STATE_SWITCH_LOGIN = 2;
    private final int STATE_SWITCH_LOGIN_BUY = 3;
    private int mState = STATE_NORMAL;

    View mSubView;
    OnlinePlayerBasePresenter mPresenter;

    public PlayerFragmentHelper() {
        addLogStatusOB();
    }

//    /**
//     * 订阅或取消订阅专辑
//     *
//     * @param v
//     */
//    public void subscribeOrUnSubscribeMedia(View v, OnlinePlayerBasePresenter presenter, SupportFragment frag) {
//        Log.i("subscribe", "subscribeOrUnSubscribeMedia: ");
//        mSubView = v;
//        mPresenter = presenter;
//        if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(v.getContext())) {
//            return;
//        }
//        boolean userBound = UserInfoManager.getInstance().isUserBound();
//        Logging.d("检查是否登录=%s，未登录显示登录页面", userBound);
//        if (!userBound) {
//            ComponentClient.obtainBuilder(LoginComponentConst.NAME)
//                    .setActionName(LoginProcessorConst.SWITH_TO_LOGIN_FRAG)
//                    .addParam(LoginProcessorConst.TOPFRAG, frag)
//                    .addParam(LoginProcessorConst.BACKTYPE, LoginProcessorConst.BACKTYPE_POP)
//                    .build().call();
//            mState = STATE_SWITCH_LOGIN;
//        } else {
//            toggleSubscribeState(v, presenter);
//        }
//    }

//    /**
//     * 订阅或取消订阅广播
//     *
//     * @param v
//     */
//    public void subscribeOrUnSubscribeBroadcast(View v, OnlinePlayerBasePresenter presenter, BroadcastPlayerActivity frag) {
//        Log.i("subscribe", "subscribeOrUnSubscribeMedia: ");
//        mSubView = v;
//        mPresenter = presenter;
//        if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(v.getContext())) {
//            return;
//        }
//        boolean userBound = UserInfoManager.getInstance().isUserBound();
//        Logging.d("检查是否登录=%s，未登录显示登录页面", userBound);
//        if (!userBound) {
//            ComponentClient.obtainBuilder(LoginComponentConst.NAME)
//                    .setActionName(LoginProcessorConst.SWITH_TO_LOGIN_FRAG)
//                    .addParam(LoginProcessorConst.TOP2FRAG, frag)
//                    .addParam(LoginProcessorConst.BACKTYPE, LoginProcessorConst.BACKTYPE_POP)
//                    .build().call();
//            mState = STATE_SWITCH_LOGIN;
//        } else {
//            toggleSubscribeState(v, presenter);
//        }
//    }

    public void setPresenter(OnlinePlayerBasePresenter mPresenter) {
        this.mPresenter = mPresenter;
    }

    /**
     * 登录后去购买
     */
    public void login2Pay(OnlinePlayerBasePresenter presenter, SupportFragment frag) {
        mPresenter = presenter;
        Bundle bundle=new Bundle();
        bundle.putString("type", LoginReportEvent.ONLINE_REMARKS1_PLAY_CAROUSEL);
        RouterManager.getInstance().jumpPage(RouterConstance.ACTIVITY_URL_LOGIN,bundle);
//        ComponentClient.obtainBuilder(LoginComponentConst.NAME)
//                .setActionName(LoginProcessorConst.SWITH_TO_LOGIN_FRAG)
//                .addParam(LoginProcessorConst.TOPFRAG, frag)
//                .addParam(LoginProcessorConst.BACKTYPE, LoginProcessorConst.BACKTYPE_POP)
//                .build().call();
        mState = STATE_SWITCH_LOGIN_BUY;
    }

    private void toggleSubscribeState(View v, OnlinePlayerBasePresenter presenter) {
        if (v == null || presenter == null) {
            return;
        }
        Log.i(TAG, "subscribeOrUnSubscribeMedia: ");
        long subscribeId = PlayerManagerHelper.getInstance().getSubscribeId();
        if (subscribeId == -1) {
            Log.i(TAG, "    subscribeOrUnSubscribeMedia: 无效的订阅id[-1].");
        } else {
            SubscribeData subscribeData = makeSubscribeData(subscribeId);
            Log.i("subscribe", "subscribeOrUnSubscribeMedia: subscribeData=" + subscribeData.getId());
            if (v.isActivated()) {
                presenter.unSubscribeMedia(v.getContext().getApplicationContext(), subscribeData);
            } else {
                // 272版本改为强账号之后，对于需要执行订阅动作的场景，首先判断该内容是否已订阅；若未订阅才执行订阅操作，否则只更新界面订阅按钮状态
                SubscribeHelper.isSubscribed(CP.KaoLaFM, String.valueOf(subscribeId), new ResultCallback() {
                    @Override
                    public void onResult(boolean result, int code) {
                        if (result) {
                            presenter.updateSubscribe(true);
                        } else {
                            subscribeData.setType(PlayerManager.getInstance().getCurPlayItem().getType());
                            presenter.subscribeMedia(v.getContext().getApplicationContext(), subscribeData);
                        }
                    }

                    @Override
                    public void onFailure(ErrorInfo errorInfo) {
                        subscribeData.setType(PlayerManager.getInstance().getCurPlayItem().getType());
                        presenter.subscribeMedia(v.getContext().getApplicationContext(), subscribeData);
                    }
                });
            }
        }
    }

    private SubscribeData makeSubscribeData(long subscribeId) {
        SubscribeData subscribeData = new SubscribeData();
        subscribeData.setLocation(SubscibeReportEvent.POSITION_PLAY_FRAGEMNT);
        subscribeData.setId(subscribeId);
        subscribeData.setType(PlayerManager.getInstance().getCurPlayItem().getType());
        return subscribeData;
    }

//    public void updateSubscribeText(TextView textView, boolean activated) {
//        if (textView == null) {
//            return;
//        }
//        if (activated) {
//            textView.setActivated(true);
//            textView.setText(R.string.subscribed_str);
//        } else {
//            textView.setActivated(false);
//            Context context = textView.getContext();
//            String quarterStr = context.getString(R.string.quarter_str);
//            textView.setText(context.getString(R.string.subscribe_str, quarterStr, quarterStr));
//        }
//    }

    public void postSubscribeMsg(boolean isSubscribed) {
        PlayerSubscribeResultEBData playerSubscribeResultEBData = new PlayerSubscribeResultEBData();
        playerSubscribeResultEBData.isSubscribed = isSubscribed;
        EventBus.getDefault().post(playerSubscribeResultEBData);
    }

//    public void switchPlayListShowOrHide(BasePlayListInter playList,
//                                         boolean isShow) {
//        if (isShow) {
//            ViewUtil.setViewVisibility((ViewGroup) playList, View.GONE);
//        } else {
//            ViewUtil.setViewVisibility((ViewGroup) playList, View.VISIBLE);
//        }
//        playList.switchPlayListShowOrHide();
//    }

    public boolean checkIsDiffRadio(long id) {
        long albumId = getCurrentAlbumId();
        boolean isDiffRadio = id != albumId;
        return isDiffRadio;
    }

    public long getCurrentAlbumId() {
        String rId = PlayerManager.getInstance().getCurPlayItem().getRadioId();
        long albumId = 0L;
        if (!TextUtils.isEmpty(rId) && TextUtils.isDigitsOnly(rId)) {
            try {
                albumId = Long.parseLong(rId);
            } catch (NumberFormatException nfe) {
                nfe.printStackTrace();
            }
        }
        return albumId;
    }

    private void addLogStatusOB() {
        mUserStateObserver = new DynamicComponent() {
            @Override
            public String getName() {
                return "PlayerFragmentHelper-DynamicComponent";
            }

            @Override
            public boolean onCall(RealCaller caller) {
                Log.d(TAG, "login onCall " + caller.actionName() + " , mState " + mState+" ,action "+caller.actionName());
                switch (caller.actionName()) {
                    case UserStateObserverProcessorConst.USER_LOGIN: {
                        if (mState == STATE_SWITCH_LOGIN) {
//                            reportLoginEvent(UserInfoManager.getInstance().getLoginType(),
//                                    LoginReportEvent.REMARKS1_PLAY_SUBSCRIBE);
                            mState = STATE_NORMAL;
                            toggleSubscribeState(mSubView, mPresenter);
                        } else if (mState == STATE_SWITCH_LOGIN_BUY) {
                            if (mPresenter != null && mPresenter instanceof OnlineRadioPlayerPresenter) {
                                OnlineRadioPlayerPresenter playerPresenter = (OnlineRadioPlayerPresenter) mPresenter;
                                long id = Long.parseLong(PlayerManager.getInstance().getCurPlayItem().getAlbumId());
                                playerPresenter.getPayInfo(id, true);
//                                reportLoginEvent(UserInfoManager.getInstance().getLoginType(),
//                                        LoginReportEvent.REMARKS1_PLAY_VIP_BUTTON);
                                playerPresenter.checkSubscribeMedia();//登录后检测下订阅状态
                            }
                        }else{
                            if (mPresenter != null && mPresenter instanceof OnlineRadioPlayerPresenter) {
                                OnlineRadioPlayerPresenter playerPresenter = (OnlineRadioPlayerPresenter) mPresenter;
                                long id = Long.parseLong(PlayerManager.getInstance().getCurPlayItem().getAlbumId());
                                playerPresenter.getPayInfo(id, false);
                                playerPresenter.checkSubscribeMedia();//登录后检测下订阅状态
                            }
                        }
                        break;
                    }
                    case UserStateObserverProcessorConst.USER_LOGOUT: {
                        break;
                    }
                }
                return false;
            }
        };
        ComponentUtil.addObserverNoRepeat(UserComponentConst.NAME, mUserStateObserver);
    }

    public void onDestroyPlayerFragmentHelper() {
        ComponentUtil.removeObserver(UserComponentConst.NAME, mUserStateObserver);
    }

    private void reportLoginEvent(String type, String remarks1) {
        LoginReportEvent event = new LoginReportEvent();
        event.setType(type);
        event.setRemarks1(remarks1);
        ReportHelper.getInstance().addEvent(event);
    }
}