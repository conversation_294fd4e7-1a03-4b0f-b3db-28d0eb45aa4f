package com.kaolafm.kradio.flavor.impl;

import android.provider.Settings;
import android.util.Log;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.StringUtil;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-05-22 20:50
 ******************************************/
public final class DeviceInfoSettingImpl implements DeviceInfoSetting {
    //    public static final String VEHICLE_VIN = "VEHICLE_VIN";
    public static final String VEHICLE_VIN = "vehicle_vin";
    public static final String KEY_TUID = "KEY_TUID";
    public static final String KEY_CARTYPE = "KEY_CARTYPE";

    @Override
    public void setInfoForSDK(Context context) {
        //此方式获取到的vin是乱码
//        String vin = Settings.System.getString(AppDelegate.getInstance().getContext().getContentResolver(),
//                VEHICLE_VIN);
//        Log.i("zsj", "setInfoForSDK: " + vin);
//        return vin;
        String vin = Settings.Global.getString(AppDelegate.getInstance().getContext().getContentResolver(),
                "vehicle_vin");
        Log.i("zsj", "setInfoForSDK: " + vin);
        String strCarType = Settings.System.getString(AppDelegate.getInstance().getContext().getContentResolver(), KEY_CARTYPE);
        DeviceInfoUtil.setDeviceIdAndCarType(StringUtil.makeAsciiOnly(vin), strCarType);
//        if (StringUtil.makeAsciiOnly(vin)) {
//            Log.i("zsj", "setInfoForSDK: allAscii ");
//            return vin;
//        } else {
//            Log.i("zsj", "setInfoForSDK: not allAscii ");
//            return null;
//        }
    }
}
