package com.kaolafm.kradio.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;

/**
 * 比亚迪账号发生状态变更（登录、退出、切换均会发送该广播）
 */
public class BydAccountStateReceiver extends BroadcastReceiver {
    public static final String ACCOUNT_STATE_ACTION =  "com.byd.action.byd_account_state_change";

    private String TAG = "BydAccountStateReceiver";

    @Override
    public void onReceive(Context context, Intent intent) {
        if (ACCOUNT_STATE_ACTION.equals(intent.getAction())){
            Log.i(TAG,"onReceive");
            Log.i(TAG,"isAppForeground is "+AppDelegate.getInstance().isAppForeground());
            AccountInterworkInter mAccountInterworkInter = ClazzImplUtil.getInter("AccountInterworkImpl");
            if(mAccountInterworkInter != null && mAccountInterworkInter.isOpenThirdPartyAccount()){
                mAccountInterworkInter.getAccountBindState();
            }
        }
    }
}
