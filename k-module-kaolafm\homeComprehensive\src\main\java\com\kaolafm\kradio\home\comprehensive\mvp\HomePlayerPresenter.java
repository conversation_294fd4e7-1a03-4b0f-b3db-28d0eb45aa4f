package com.kaolafm.kradio.home.comprehensive.mvp;


import android.content.Context;
import android.util.Log;

import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.base.utils.NetworkMonitor;
import com.kaolafm.kradio.home.HomeDataManager;
import com.kaolafm.kradio.home.comprehensive.data.HomePlayerModel;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioPlayLogicInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioSystemSourceChangeInter;
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.media.MediaSessionUtil;
import com.kaolafm.opensdk.api.volume.VolumeRequest;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.IPlayerInteractionFiredListener;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;

import java.util.List;

/**
 * Created by v on 2018/4/13.
 */

public class HomePlayerPresenter extends BasePresenter<HomePlayerModel, HomePlayerContract.View>
        implements HomePlayerContract.Presenter, NetworkMonitor.OnNetworkStatusChangedListener {

    private static final String TAG = "HomePlayerPresenter";
    /**
     * 是否第一次刷新，第一次刷新出错就显示错误页面，不是第一次不显示错误。
     */
    private boolean isFirstRefresh = true;

    private HomePlayerModel mModel;

    private KRadioAudioPlayLogicInter mKRadioAudioPlayLogicInter;

    private KRadioSystemSourceChangeInter mKRadioSystemSourceChangeInter;

    private IPlayerInteractionFiredListener mPlayerInteractionFiredListener;

    public HomePlayerPresenter(Context context, HomePlayerContract.View view) {
        super(view);
        mModel = createModel();
        Log.i(TAG, "HomePlayerPresenter: registerNetworkStatusChangeListener");
        NetworkMonitor.getInstance(context).registerNetworkStatusChangeListener(this);

    }

    @Override
    protected HomePlayerModel createModel() {
        return HomePlayerModel.getInstance();
    }

    @Override
    public void init() {
        mKRadioAudioPlayLogicInter = ClazzImplUtil.getInter("KRadioAudioPlayLogicImpl");
        mKRadioSystemSourceChangeInter = ClazzImplUtil.getInter("KRadioSystemSourceChangeImpl");
        initPlayerInteractionFiredListener();
    }

    public void onPlayerInitSuccess() {
        requestAudioFocus();
        checkVolumeBalance();
        registerSourceChanged();
        MediaSessionUtil.getInstance().registerMediaSession();
    }

    private void checkVolumeBalance() {
        new VolumeRequest().checkVolumeBalanceStatus(new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean isOpen) {
                int openValue = isOpen ? 1 : 0;
                PlayerManager.getInstance().setLoudnessNormalization(openValue);
            }

            @Override
            public void onError(ApiException e) {

            }
        });
    }

    /**
     * 注册源切换
     */
    private void registerSourceChanged() {
        if (mKRadioSystemSourceChangeInter != null) {
            mKRadioSystemSourceChangeInter.registerSourceChanged();
        }
    }

    private void requestAudioFocus() {
        if (mKRadioAudioPlayLogicInter != null) {
            boolean flag = mKRadioAudioPlayLogicInter.requestAudioFocus();
            Log.i(TAG, "requestAudioFocus-------->flag = " + flag);
        }
    }

    public void initData(){
//        boolean flag = KradioSDKManager.getInstance().isUsable();
//        // 首次启动，在未激活状态下不调用
//        Log.i("k.activate", "start: isActivation = " + flag);
//        // fixme 暂时容错，需要整理激活逻辑
//        if (flag) {
//            getData();
//        } else {
//            KradioSDKManager.getInstance().addUsableObserver(this::getData);
//        }
    }
    @Override
    public void start() {
        super.start();
    }

    /**
     * 获取热词
     */
    public void getHotSearchWords() {
        mModel.getHotSearchWords(new HttpCallback<List<String>>() {
            @Override
            public void onSuccess(List<String> hotWords) {
                if (ListUtil.isEmpty(hotWords)) {
                    return;
                }
                int size = hotWords.size();
                if (size > 10) {
                    hotWords = hotWords.subList(size - 10, size);
                }
                mView.showHotSearchWordsView(hotWords);
            }

            @Override
            public void onError(ApiException e) {
                mView.showHotSearchWordsView(null);
            }
        });
    }

    @Override
    public void destroy() {
        mModel.cancelRequest();
        mView = null;
        unregisterSourceChanged();
    }

    @Override
    public void onStatusChanged(int newStatus, int oldStatus) {
        Log.i(TAG, "onStatusChanged: : " + newStatus);
        if (newStatus == NetworkMonitor.STATUS_MOBILE || newStatus == NetworkMonitor.STATUS_WIFI) {
            start();
        }
    }

    public void playNetOrLocal() {
        HomeDataManager.getInstance().autoPlay();
    }


    /**
     * 注销源切换
     */
    private void unregisterSourceChanged() {
        if (mKRadioSystemSourceChangeInter != null) {
            mKRadioSystemSourceChangeInter.unregisterSourceChanged();
        }
    }

    private void initPlayerInteractionFiredListener() {
        mPlayerInteractionFiredListener = (playItem, position, id) -> {
            PlayerLogUtil.log(getClass().getSimpleName(), "ijk soft ad callback: position" + position + ", id = " + id);
            exposureAd(String.valueOf(id));
        };
        PlayerManager playerManager = PlayerManager.getInstance();
        if (playerManager.isPlayerInitSuccess()) {
            playerManager.setPlayerInteractionFiredListener(mPlayerInteractionFiredListener);
        } else {
            playerManager.addPlayerInitComplete(b -> playerManager.setPlayerInteractionFiredListener(mPlayerInteractionFiredListener));
        }
    }

    /**
     * 广告曝光
     *
     * @param adId
     */
    public void exposureAd(String adId) {
        AdvertisingManager.getInstance().expose(adId,
                String.valueOf(ScreenUtil.getScreenWidth()),
                String.valueOf(ScreenUtil.getScreenHeight()),
                "4",
                ""
        );
    }

}

