<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_ad_interact_root"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@color/transparent_color">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingRight="@dimen/m15"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:id="@+id/ll_ad_interact_msg_and_close"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/m76"
                android:layout_marginLeft="@dimen/m44"
                android:layout_marginTop="@dimen/m15"
                android:layout_marginEnd="@dimen/m15"
                android:background="@drawable/comprehensive_ad_qrcode_bg"
                android:paddingLeft="@dimen/m58"
                android:paddingRight="@dimen/m24"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/tv_ad_msg"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:ellipsize="end"
                    android:includeFontPadding="false"
                    android:maxWidth="@dimen/m176"
                    android:maxLines="2"
                    android:textColor="@color/ad_text_color"
                    android:textSize="@dimen/text_size2"
                    tools:text="瑞幸咖啡真好喝！瑞幸咖啡真好喝！！！" />


            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <ImageView
            android:id="@+id/iv_ad_interact"
            android:layout_width="@dimen/m88"
            android:layout_height="@dimen/m88"
            android:layout_marginTop="@dimen/m15"
            android:background="@drawable/ad_interact_image_bg"
            android:padding="@dimen/m6"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/home_scene_bg" />
        <!--        这个view的作用是在没有标题的时候把图片区域撑大一点，这个样关闭按钮才能往右靠-->
        <View
            android:layout_width="@dimen/m103"
            android:layout_height="1dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="@dimen/m44"
            android:layout_height="@dimen/m14"
            android:layout_margin="@dimen/m6"
            android:background="@drawable/bg_ad_label_oval"
            android:gravity="center"
            android:text="@string/ad"
            android:textColor="#eeeeee"
            android:textSize="@dimen/m10"
            app:layout_constraintLeft_toLeftOf="@id/iv_ad_interact"
            app:layout_constraintRight_toRightOf="@id/iv_ad_interact"
            app:layout_constraintTop_toTopOf="@id/iv_ad_interact" />

        <ImageView
            android:id="@+id/iv_ad_close"
            android:layout_width="@dimen/m48"
            android:layout_height="@dimen/m48"
            android:layout_gravity="center_vertical"
            android:contentDescription="@string/content_desc_close_advertise"
            android:src="@drawable/comprehensive_ad_close"
            android:visibility="visible"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</merge>
