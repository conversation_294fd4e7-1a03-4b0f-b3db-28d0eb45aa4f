<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    tools:background="@drawable/bg_home">

    <TextView
        android:id="@+id/item_title_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:lineHeight="@dimen/y36"
        android:maxLines="2"
        android:lineSpacingExtra="@dimen/m6"
        android:text="我是标题啊"
        android:textColor="@color/text_color_7"
        android:textSize="@dimen/m24" />

    <TextView
        android:id="@+id/item_time_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/m5"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="1小时前"
        android:textColor="@color/text_color_8"
        android:textSize="@dimen/m20" />

</LinearLayout>