package com.kaolafm.kradio.flavor.carnetwork.carnetworkutils;

import android.content.Context;
import android.util.Log;

import com.incall.vehicle.proxy.VehicleNetworkManager;
import com.incall.vehicle.proxy.define.CommonTypeDefine;
import com.kaolafm.kradio.flavor.carnetwork.api.ChangAnApiRequest;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.SharedPreferenceUtil;

import java.text.SimpleDateFormat;
import java.util.Date;

public class CarAuthUtil {
    private SharedPreferenceUtil mSPUtil;
    private ChangAnApiRequest mChangAnApiRequest;
    private static final String AUTH_DATE_STATUS = "authDateStatus";
    private static final String SP_KEY_TIME_INFO = "timeInfo";
    private static final String SP_KEY_STATUS_INFO = "statusInfo";
    public static boolean AUTH_THROUGH = false;//鉴权状态是否通过
    public static volatile boolean ischecking = false;

    /**
     * 网络判断是否需要去鉴权(长安jar包中提供了方法)
     *
     * @return true 继续响应鉴权  false 不做鉴权
     */
    public static boolean needAuthByNetWork() {
        try {
            CommonTypeDefine.NetWorkState netWorkState = VehicleNetworkManager.getInstance().getNetWorkState();
            switch (netWorkState) {
                case INTERNAL_TBOX:
                case EXTERNAL_TBOX:
                    return true;
                default:
                    return false;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return true;
        }
    }


    /**
     * 根据日期和状态判断是否需要去鉴权
     *
     * @return true 继续响应鉴权  false 不做鉴权
     */
    public static boolean checkAuthStatusAndDate() {
        SharedPreferenceUtil mSPUtil = SharedPreferenceUtil.newInstance(AppDelegate.getInstance().getContext(), AUTH_DATE_STATUS, Context.MODE_PRIVATE);
        String time = mSPUtil.getString(SP_KEY_TIME_INFO, "0");
        boolean status = AUTH_THROUGH = mSPUtil.getBoolean(SP_KEY_STATUS_INFO, false);
        Log.i("zsj", "checkAuthStatusAndDate: isSameDate(time) = " + isSameDate(time) + ",status = " + status + ",time = " + time);
        if (status == false || !isSameDate(time)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 对比是否是同一天
     *
     * @param time String类型-当前的时间戳
     * @return
     */
    public static boolean isSameDate(String time) {
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
            long longTime = new Long(time);
            Date date = new Date(longTime);
//        String res = simpleDateFormat.format(date);
            //当前时间
            Date now = new Date();
            SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
            //获取今天的日期
            String nowDay = sf.format(now);
            //对比的时间
            String day = sf.format(date);
            return day.equals(nowDay);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 保存时间和鉴权状态
     *
     * @param time
     * @param status
     */
    public static void saveAuthAndDate(String time, boolean status) {
        SharedPreferenceUtil mSPUtil = SharedPreferenceUtil.newInstance(AppDelegate.getInstance().getContext(), AUTH_DATE_STATUS, Context.MODE_PRIVATE);
        mSPUtil.putString(SP_KEY_TIME_INFO, time);
        mSPUtil.putBoolean(SP_KEY_STATUS_INFO, status);
        AUTH_THROUGH = status;
        Log.i("zsj", "saveAuthAndDate: time = " + time + ",status = " + status);
    }
}
