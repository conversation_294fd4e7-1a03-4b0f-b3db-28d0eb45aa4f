package com.kaolafm.kradio.lib.basedb.helper;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;

import com.kaolafm.kradio.lib.basedb.greendao.CrashMessageBeanDao;
import com.kaolafm.kradio.lib.basedb.greendao.DaoMaster;
import com.kaolafm.kradio.lib.basedb.greendao.HistoryItemDao;
import com.kaolafm.kradio.lib.basedb.greendao.LoginedHistoryItemDao;
import com.kaolafm.kradio.lib.basedb.greendao.SongHistoryDao;

import org.greenrobot.greendao.database.Database;

/******************************************
 * 类描述： 数据库升级帮助类 类名称：SQLiteUpdateOpenHelper
 *
 * @version: 1.0
 * @author: shaoningYang
 * @time: 2017-11-28 14:49
 ******************************************/

public final class SQLiteUpdateOpenHelper extends DaoMaster.OpenHelper {

    public SQLiteUpdateOpenHelper(Context context, String name) {
        super(context, name);
    }

    public SQLiteUpdateOpenHelper(Context context, String name, SQLiteDatabase.CursorFactory factory) {
        super(context, name, factory);
    }

    @Override
    public void onUpgrade(Database db, int oldVersion, int newVersion) {
        MigrationHelper.migrate(db, SongHistoryDao.class, HistoryItemDao.class, LoginedHistoryItemDao.class, CrashMessageBeanDao.class);
    }
}
