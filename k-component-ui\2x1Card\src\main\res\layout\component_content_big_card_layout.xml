<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@drawable/component_card_bg">

    <com.kaolafm.kradio.component.ui.base.view.OvalImageView
        android:id="@+id/card_big_bg_iv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:oval_radius="@dimen/m8" />


    <com.kaolafm.kradio.component.ui.base.view.OvalImageView
        android:id="@+id/card_big_pic_iv"
        android:layout_width="@dimen/y132"
        android:layout_height="@dimen/y132"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:layout_marginRight="@dimen/m20"
        android:layout_marginBottom="@dimen/m30"
        app:oval_radius="@dimen/m8"
        tools:src="@drawable/splash_yunting" />

    <ImageView
        android:id="@+id/vip_icon"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/m20"
        android:layout_alignLeft="@+id/card_big_pic_iv"
        android:layout_alignTop="@+id/card_big_pic_iv"
        android:scaleType="fitStart"
        android:adjustViewBounds="true"
        tools:src="@drawable/comprehensive_icon_vip" />

    <com.kaolafm.kradio.component.ui.base.view.KradioTextView
        android:id="@+id/card_big_title_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/m20"
        android:layout_marginRight="@dimen/m26"
        android:layout_marginTop="@dimen/m30"
        app:kt_font_weight="0.3"
        android:textColor="@color/text_color_7"
        android:textSize="@dimen/m26"
        tools:text="我是标题啊" />

    <TextView
        android:id="@+id/card_big_des_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/card_big_title_tv"
        android:layout_marginLeft="@dimen/m20"
        android:layout_marginTop="@dimen/m15"
        android:layout_marginRight="@dimen/m28"
        android:layout_toStartOf="@+id/card_big_pic_iv"
        android:maxLines="3"
        android:maxWidth="@dimen/m342"
        android:lineSpacingExtra="@dimen/m7"
        android:textColor="@color/text_color_7"
        android:textSize="@dimen/m24"
        tools:text="我是描述啊我是描述啊我是描述啊我是描述啊我是描述啊我是描述啊我是描述啊" />

    <ImageView
        android:id="@+id/card_big_play_iv"
        android:layout_width="@dimen/m38"
        android:layout_height="@dimen/m38"
        android:layout_alignRight="@+id/card_big_pic_iv"
        android:layout_alignBottom="@+id/card_big_pic_iv"
        android:layout_marginRight="@dimen/m10"
        android:layout_marginBottom="@dimen/m10"
        android:src="@drawable/component_play_icon_2"
        android:visibility="gone"
        tools:visibility="visible" />

    <com.kaolafm.kradio.component.ui.base.view.RateView
        android:id="@+id/card_layout_playing"
        android:layout_width="@dimen/m38"
        android:layout_height="@dimen/m38"
        android:layout_alignRight="@+id/card_big_pic_iv"
        android:layout_alignBottom="@+id/card_big_pic_iv"
        android:layout_marginRight="@dimen/m10"
        android:layout_marginBottom="@dimen/m10"
        android:visibility="gone"
        app:lottie_autoPlay="true"
        app:lottie_fileName="lottie/rate.json"
        app:lottie_loop="true" />
</RelativeLayout>