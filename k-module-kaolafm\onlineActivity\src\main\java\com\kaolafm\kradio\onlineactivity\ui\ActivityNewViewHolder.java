package com.kaolafm.kradio.onlineactivity.ui;

import android.annotation.SuppressLint;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.base.utils.DateUtil;
import com.kaolafm.kradio.common.widget.KradioTextView;
import com.kaolafm.kradio.k_kaolafm.R; 
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.UrlUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.opensdk.api.activity.model.Activity;
 
public class ActivityNewViewHolder extends BaseHolder<Activity> {
 
    KradioTextView titleTextView;
    TextView desTextView; 
    TextView activity_details_tv;
    ImageView item_activitys_bg_iv;
    ImageView pic_image; 
    KradioTextView date_activity;
    View qrExpireView; 
    View qrExpireIcon;

    public ActivityNewViewHolder(View itemView) {
        super(itemView);
        qrExpireIcon = view.findViewById(R.id.qr_expire_icon);
        qrExpireView = view.findViewById(R.id.qr_view_expire);
        date_activity = view.findViewById(R.id.date_activity);
        pic_image = view.findViewById(R.id.pic_image);
        item_activitys_bg_iv = view.findViewById(R.id.item_activitys_bg_iv);
        activity_details_tv = view.findViewById(R.id.activity_details_tv);
        desTextView = view.findViewById(R.id.des_activity);
        titleTextView = view.findViewById(R.id.title_activity);
    }

    @Override
    public void setupData(Activity activity, int position) {
//        if (position == 0) {
//            activity.setStyleType("1");
//        }
//        if (position == 3) {
//            activity.setStyleType("4");
//        }
        //设置主题
        setItemStyle(activity.getStyleType());
        titleTextView.setText(StringUtil.getMaxString(activity.getName(),8));
        desTextView.setText(StringUtil.getMaxString(activity.getDescription(),12));
//        if (!TextUtils.isEmpty(activity.getBackgroundUrl()))
//            ImageLoader.getInstance().displayImage(AppDelegate.getInstance().getContext(),
//                    activity.getBackgroundUrl(), item_activitys_bg_iv);

        ImageLoader.getInstance().displayCircleImage(AppDelegate.getInstance().getContext(),
                UrlUtil.getCustomPicUrl(UrlUtil.PIC_250_250, activity.getQrCodeUrl()), pic_image);

        String start = DateUtil.formatMillis("MM.dd", Long.parseLong(activity.getStartTime()));
        String end = DateUtil.formatMillis("MM.dd", Long.parseLong(activity.getEndTime()));
        date_activity.setText(start + " - " + end);
        date_activity.setVisibility(View.VISIBLE);
        pic_image.setVisibility(View.VISIBLE);


        if (activity.getStatus() == 1) {
            qrExpireView.setVisibility(View.GONE);
            qrExpireIcon.setVisibility(View.GONE);
//            titleTextView.setTextColor(ResUtil.getColor(R.color.color_2));
//            desTextView.setTextColor(ResUtil.getColor(R.color.color_2));
        } else {
            qrExpireView.setVisibility(View.VISIBLE);
            qrExpireIcon.setVisibility(View.VISIBLE);
//            titleTextView.setTextColor(ResUtil.getColor(R.color.color_2));
//            desTextView.setTextColor(ResUtil.getColor(R.color.color_2));
        }
    }

    /**
     * 设置item主题样式
     *
     * @param type
     */
    private void setItemStyle(String type) {
        switch (type) {
            case "1":
//                titleTextView.setGradientColor(ResUtil.getColor(R.color.online_activity_item_title_start_color_1)
//                        , ResUtil.getColor(R.color.online_activity_item_title_end_color_1), 0);
//                date_activity.setGradientColor(ResUtil.getColor(R.color.online_activity_item_title_start_color_1)
//                        , ResUtil.getColor(R.color.online_activity_item_title_end_color_1), 0);

                titleTextView.setTextColor(ResUtil.getColor(R.color.online_activity_item_title_start_color_1));
                date_activity.setTextColor(ResUtil.getColor(R.color.online_activity_item_title_start_color_1));
                desTextView.setTextColor(ResUtil.getColor(R.color.online_activity_item_subtitle_color_1));
                activity_details_tv.setTextColor(ResUtil.getColor(R.color.online_activity_item_btn_text_color_1));
                activity_details_tv.setBackgroundResource(R.drawable.online_activity_item_btn_bg_green);
                item_activitys_bg_iv.setImageResource(R.drawable.online_activity_item_green);
                break;
            case "2":
//                titleTextView.setGradientColor(ResUtil.getColor(R.color.online_activity_item_title_start_color_2)
//                        , ResUtil.getColor(R.color.online_activity_item_title_end_color_2), 0);
//                date_activity.setGradientColor(ResUtil.getColor(R.color.online_activity_item_title_start_color_2)
//                        , ResUtil.getColor(R.color.online_activity_item_title_end_color_2), 0);

                titleTextView.setTextColor(ResUtil.getColor(R.color.online_activity_item_title_start_color_2));
                date_activity.setTextColor(ResUtil.getColor(R.color.online_activity_item_title_start_color_2));
                desTextView.setTextColor(ResUtil.getColor(R.color.online_activity_item_subtitle_color_2));
                activity_details_tv.setTextColor(ResUtil.getColor(R.color.online_activity_item_btn_text_color_2));
                activity_details_tv.setBackgroundResource(R.drawable.online_activity_item_btn_bg_blue);
                item_activitys_bg_iv.setImageResource(R.drawable.online_activity_item_blue);
                break;
            case "3":
//                titleTextView.setGradientColor(ResUtil.getColor(R.color.online_activity_item_title_start_color_3)
//                        , ResUtil.getColor(R.color.online_activity_item_title_end_color_3), 0);
//                date_activity.setGradientColor(ResUtil.getColor(R.color.online_activity_item_title_start_color_3)
//                        , ResUtil.getColor(R.color.online_activity_item_title_end_color_3), 0);

                titleTextView.setTextColor(ResUtil.getColor(R.color.online_activity_item_title_start_color_3));
                date_activity.setTextColor(ResUtil.getColor(R.color.online_activity_item_title_start_color_3));
                desTextView.setTextColor(ResUtil.getColor(R.color.online_activity_item_subtitle_color_3));
                activity_details_tv.setTextColor(ResUtil.getColor(R.color.online_activity_item_btn_text_color_3));
                activity_details_tv.setBackgroundResource(R.drawable.online_activity_item_btn_bg_red);
                item_activitys_bg_iv.setImageResource(R.drawable.online_activity_item_red);
                break;
            case "4":
//                titleTextView.setGradientColor(ResUtil.getColor(R.color.online_activity_item_title_start_color_4)
//                        , ResUtil.getColor(R.color.online_activity_item_title_end_color_4), 0);
//                date_activity.setGradientColor(ResUtil.getColor(R.color.online_activity_item_title_start_color_4)
//                        , ResUtil.getColor(R.color.online_activity_item_title_end_color_4), 0);

                titleTextView.setTextColor(ResUtil.getColor(R.color.online_activity_item_title_start_color_4));
                date_activity.setTextColor(ResUtil.getColor(R.color.online_activity_item_title_start_color_4));
                desTextView.setTextColor(ResUtil.getColor(R.color.online_activity_item_subtitle_color_4));
                activity_details_tv.setTextColor(ResUtil.getColor(R.color.online_activity_item_btn_text_color_4));
                activity_details_tv.setBackgroundResource(R.drawable.online_activity_item_btn_bg_yellow);
                item_activitys_bg_iv.setImageResource(R.drawable.online_activity_item_yellow);
                break;
            case "5":
//                titleTextView.setGradientColor(ResUtil.getColor(R.color.online_activity_item_title_start_color_5)
//                        , ResUtil.getColor(R.color.online_activity_item_title_end_color_5), 0);
//                date_activity.setGradientColor(ResUtil.getColor(R.color.online_activity_item_title_start_color_5)
//                        , ResUtil.getColor(R.color.online_activity_item_title_end_color_5), 0);

                titleTextView.setTextColor(ResUtil.getColor(R.color.online_activity_item_title_start_color_5));
                date_activity.setTextColor(ResUtil.getColor(R.color.online_activity_item_title_start_color_5));
                desTextView.setTextColor(ResUtil.getColor(R.color.online_activity_item_subtitle_color_5));
                activity_details_tv.setTextColor(ResUtil.getColor(R.color.online_activity_item_btn_text_color_5));
                activity_details_tv.setBackgroundResource(R.drawable.online_activity_item_btn_bg_pink);
                item_activitys_bg_iv.setImageResource(R.drawable.online_activity_item_pink);
                break;
            case "6":
//                titleTextView.setGradientColor(ResUtil.getColor(R.color.online_activity_item_title_start_color_6)
//                        , ResUtil.getColor(R.color.online_activity_item_title_end_color_6), 0);
//                date_activity.setGradientColor(ResUtil.getColor(R.color.online_activity_item_title_start_color_6)
//                        , ResUtil.getColor(R.color.online_activity_item_title_end_color_6), 0);

                titleTextView.setTextColor(ResUtil.getColor(R.color.online_activity_item_title_start_color_6));
                date_activity.setTextColor(ResUtil.getColor(R.color.online_activity_item_title_start_color_6));
                desTextView.setTextColor(ResUtil.getColor(R.color.online_activity_item_subtitle_color_6));
                activity_details_tv.setTextColor(ResUtil.getColor(R.color.online_activity_item_btn_text_color_6));
                activity_details_tv.setBackgroundResource(R.drawable.online_activity_item_btn_bg_purple);
                item_activitys_bg_iv.setImageResource(R.drawable.online_activity_item_purple);
                break;
        }
    }
}
