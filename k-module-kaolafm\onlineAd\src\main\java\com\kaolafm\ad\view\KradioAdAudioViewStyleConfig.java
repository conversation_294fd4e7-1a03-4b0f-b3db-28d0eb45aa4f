package com.kaolafm.ad.view;

import android.graphics.drawable.Drawable;
import androidx.annotation.LayoutRes;
import android.view.Gravity;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.ResUtil;

/**
 * 音频广告显示配置，包括 在屏幕中的位置等等一系列的
 *
 * <AUTHOR>
 * @date 2020-01-13
 */
public class KradioAdAudioViewStyleConfig {

    @LayoutRes
    public int layoutId;

    public int style;

    public int height;

    public int width;

    public int animationId;

    public int gravity;

    public int backgroundColor;

    public Drawable background;

    public float radius;

    public KradioAdAudioViewStyleConfig() {
        layoutId = R.layout.audio_ad_suspension_layout;
        width = ResUtil.getDimen(R.dimen.x604);
        height = ResUtil.getDimen(R.dimen.y72);
        animationId = android.R.style.Animation_Toast;
        gravity = Gravity.CENTER_HORIZONTAL|Gravity.TOP;
        radius = ResUtil.getDimen(R.dimen.m16);
    }

}
