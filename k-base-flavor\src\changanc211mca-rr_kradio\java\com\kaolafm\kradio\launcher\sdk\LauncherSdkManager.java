package com.kaolafm.kradio.launcher.sdk;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Handler;
import android.os.Looper;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;

import com.incall.apps.commoninterface.c211launcher.ILauncherListener;
import com.incall.apps.commoninterface.c211launcher.LauncherManager;
import com.incall.apps.commoninterface.c211launcher.LauncherSdkConstant;
import com.incall.apps.commoninterface.c211launcher.bean.PlayBean;
import com.kaolafm.kradio.flavor.utils.PlayerUtil;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.IntentUtils;
import com.kaolafm.kradio.lib.utils.NetworkManager;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.UrlUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.player.bean.BroadcastStatus;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.IPlayerStateListener;
import com.kaolafm.opensdk.player.logic.model.item.InvalidPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

import java.util.ArrayList;
import java.util.List;

import io.reactivex.Observable;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;

public class LauncherSdkManager implements IPlayerStateListener {
    private static final String TAG = "LauncherSdkManager";

    private static LauncherSdkManager instance;

    private LauncherSdkManager() {
    }

    public static LauncherSdkManager getInstance() {
        if (instance == null) {
            instance = new LauncherSdkManager();
        }

        return instance;
    }

    private long mCacheAudioId;

    public void init() {
        Log.i(TAG, "init");
        LauncherManager.getInstance().init(AppDelegate.getInstance().getContext());
    }

    public void registerListener() {
        Log.i(TAG, "registerListener");
        LauncherManager.getInstance().registerListener(
                LauncherSdkConstant.MediaType.YUNTING, iLauncherListener);

        PlayerManager.getInstance().addPlayControlStateCallback(this);
    }

    public void unRegisterListener() {
        Log.i(TAG, "unRegisterListener");
        LauncherManager.getInstance().unregisterListener(
                LauncherSdkConstant.MediaType.YUNTING
        );
        PlayerManager.getInstance().removePlayControlStateCallback(this);
    }

    private final ILauncherListener iLauncherListener = new ILauncherListener.Stub() {
        @Override
        public void basicTypes(int i, long l, boolean b, float v, double v1, String s) throws RemoteException {

        }

        @Override
        public void switchPlay() throws RemoteException {
            Log.i(TAG, "switchPlay");
            if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext())) {
                new Handler(Looper.getMainLooper()).post(new Runnable() {
                    @Override
                    public void run() {
                        Toast.makeText(AppDelegate.getInstance().getContext(), "网络有问题，请稍候重试", Toast.LENGTH_SHORT).show();
                    }
                });
                return;
            }
            if (PlayerManager.getInstance().getCurPlayItem() instanceof InvalidPlayItem ||
                    PlayerManager.getInstance().getCurPlayItem() == null) {
                PlayerUtil.playNetOrLocal();
            } else {
                PlayerManagerHelper.getInstance().play(true);
            }
        }

        @Override
        public void switchPause() throws RemoteException {
            Log.i(TAG, "switchPause");
            PlayerManagerHelper.getInstance().pause(true);
        }

        @Override
        public void clickPre() throws RemoteException {
            Log.i(TAG, "clickPre");
            PlayerManagerHelper.getInstance().playPre(true);
        }

        @Override
        public void clickNext() throws RemoteException {
            Log.i(TAG, "clickNext");
            PlayerManagerHelper.getInstance().playNext(true);
        }

        @Override
        public void switchNextPlayMode() throws RemoteException {
            Log.i(TAG, "switchNextPlayMode");
        }

        @Override
        public int getCurPlayMode() throws RemoteException {
            Log.i(TAG, "getCurPlayMode");
            return 0;
        }

        @Override
        public void clickPlayList(int i) throws RemoteException {
            Log.i(TAG, "clickPlayList=" + i);
            try {
                List<PlayItem> playItems = PlayerManager.getInstance().getPlayList();
                if (i >= 0 && i < playItems.size()) {
                    PlayItem item = playItems.get(i);
                    PlayerManagerHelper.getInstance().startPlayItemInList(item);
                }
            } catch (Exception e) {
                Log.i(TAG, "clickPlayList=" + e.toString());
            }
        }

        @Override
        public void seekToProgress(int i) throws RemoteException {
            Log.i(TAG, "seekToProgress=" + i);
            try {
                PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
                Log.i(TAG, "type=" + playItem.getType() + ":status=" + playItem.getStatus());
                if (playItem.getType() == PlayerConstants.RESOURCES_TYPE_BROADCAST
                        && playItem.getStatus() == BroadcastStatus.BROADCAST_STATUS_LIVING) {
                    Log.i(TAG, "seek return");
                    return;
                }
                int duration = playItem.getDuration();
                int progress = (int) ((float) i / (float) 100 * duration);
                PlayerManagerHelper.getInstance().seek(progress);
            } catch (Exception e) {
                Log.i(TAG, "seekToProgress=" + e.toString());
            }
        }

        @Override
        public int getCurPlayIndex() throws RemoteException {
            try {
                Log.i(TAG, "getCurPlayIndex");
                List<PlayItem> playItems = PlayerManager.getInstance().getPlayList();
                PlayItem item = PlayerManager.getInstance().getCurPlayItem();
                for (int i = 0; i < playItems.size(); i++) {
                    if (item.getAudioId() == playItems.get(i).getAudioId()) {
                        return i;
                    }
                }
            } catch (Exception e) {
                Log.i(TAG, "getCurPlayIndex=" + e.toString());
            }
            return 0;
        }

        @Override
        public List<PlayBean> getPlayList() throws RemoteException {
            Log.i(TAG, "getPlayList");
            try {
                List<PlayItem> playItems = PlayerManager.getInstance().getPlayList();
                return toPlayBeanList(playItems);
            } catch (Exception e) {
                Log.i(TAG, "getPlayList=" + e.toString());
                return new ArrayList<>();
            }
        }

        @Override
        public void startMediaApp() throws RemoteException {
            Log.i(TAG, "startMediaApp");
            try {
                Context context = AppDelegate.getInstance().getContext();
                Intent launchAppIntent = IntentUtils.getInstance().getLauncherIntentUseWidget(context);
                launchAppIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                context.startActivity(launchAppIntent);
            } catch (Exception e) {
                Log.i(TAG, "startMediaApp=" + e.toString());
            }
        }

        @Override
        public void freshManagerMediaType(int i) throws RemoteException {
            Log.i(TAG, "freshManagerMediaType=" + i);
        }
    };

    private List<PlayBean> toPlayBeanList(List<PlayItem> playItemList) {
        Log.i(TAG, "toPlayBeanList");
        List<PlayBean> playBeanList = new ArrayList<>();
        for (PlayItem item : playItemList) {
            playBeanList.add(toPlayBean(item));
        }
        return playBeanList;
    }

    private PlayBean toPlayBean(PlayItem playItem) {
        PlayBean bean = new PlayBean();
        bean.setmId(String.valueOf(playItem.getAudioId()));
        bean.setTitle(playItem.getTitle());
        bean.setSubTitle(playItem.getAlbumTitle());
        bean.setLrcPath("");

        return bean;
    }

    @Override
    public void onIdle(PlayItem playItem) {
        Log.i(TAG, "onIdle=" + playItem);
    }

    @Override
    public void onPlayerPreparing(PlayItem playItem) {
        Log.i(TAG, "onPlayerPreparing=" + playItem);
        notifyLauncherInfo(playItem);
        LauncherManager.getInstance().notifyPlay();
    }

    @Override
    public void onPlayerPlaying(PlayItem playItem) {
        Log.i(TAG, "onPlayerPlaying=" + playItem);
        notifyLauncherInfo(playItem);
        LauncherManager.getInstance().notifyPlay();
    }

    private void notifyLauncherInfo(PlayItem playItem) {
        Log.i(TAG, "notifyLauncherInfo=" + playItem);
        if (playItem == null) {
            Log.e(TAG, "playItem is null");
            return;
        }

        LauncherManager.getInstance().notifyMediaTypeChange(LauncherSdkConstant.MediaType.YUNTING);

        if (playItem.getType() == PlayerConstants.RESOURCES_TYPE_BROADCAST
                && playItem.getStatus() == BroadcastStatus.BROADCAST_STATUS_LIVING) {
            LauncherManager.getInstance().setupProgressBar(true, false);
        } else {
            LauncherManager.getInstance().setupProgressBar(true, true);
        }

        if (!TextUtils.isEmpty(playItem.getPicUrl())) {
            Log.e(TAG, "pic url is have");
            notifyPlayInfo(playItem, null);
            String url = UrlUtil.getCustomPicUrl(UrlUtil.PIC_340_340, playItem.getPicUrl());
            ImageLoader.getInstance().getBitmapFromCache(
                    AppDelegate.getInstance().getContext(), url, bitmap -> {
                    /*
                    Observable.create((ObservableOnSubscribe<Bitmap>) emitter -> {
                        Bitmap bitmap1 = DrawableUtil.getReSizeBitmap(bitmap, 400, 400);
                        emitter.onNext(bitmap1);
                    }).subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribe(b -> {
                                PlayBean music = new PlayBean();
                                music.setTitle(playItem.getTitle());
                                music.setSubTitle(playItem.getAlbumTitle());
                                music.setLrcPath("");
                                LauncherManager.getInstance().notifyPlayInfo(music, b);
                            });
                     */
                        Log.e(TAG, "bitmap is done");
                        notifyPlayInfo(playItem, bitmap);
                    });
        } else {
            Log.e(TAG, "pic url is null");
            notifyPlayInfo(playItem, null);
        }

    }

    private void notifyPlayInfo(PlayItem playItem, Bitmap bitmap) {
        Log.e(TAG, "notifyPlayInfo  id:" + playItem.getAudioId());
        if (playItem.getAudioId() == mCacheAudioId && bitmap == null) {
            return; //当前playitem上传过带图内容，不再传了
        }
        if (bitmap != null) {
            mCacheAudioId = playItem.getAudioId();
        }
        PlayBean music = new PlayBean();
        music.setTitle(playItem.getTitle());
        music.setSubTitle(playItem.getAlbumTitle());
        music.setLrcPath("");
        Log.e(TAG, "notifyPlayInfo  title:" + playItem.getTitle() + "  albumTitle:" + playItem.getAlbumTitle() + " radioName" + playItem.getRadioName());
        LauncherManager.getInstance().notifyPlayInfo(music, bitmap);

    }

    @Override
    public void onPlayerPaused(PlayItem playItem) {
        Log.i(TAG, "onPlayerPaused=" + playItem);

        if (playItem == null) {
            Log.e(TAG, "playItem is null");
            return;
        }

        try {
            if (AntiShake.check(this)) {
                Log.e(TAG, "AntiShake");
            }
            LauncherManager.getInstance().notifyPause();
        } catch (Exception e) {
            Log.e(TAG, e.toString());
        }
    }

    @Override
    public void onProgress(PlayItem playItem, long progress, long total) {
        Log.i(TAG, "onProgress=" + playItem);
        int seekPos = (int) ((float) progress / (float) total * 100);
        LauncherManager.getInstance().notifyFreshProgress(seekPos);
    }

    @Override
    public void onPlayerFailed(PlayItem playItem, int i, int i1) {
        Log.i(TAG, "onPlayerFailed=" + playItem);
    }

    @Override
    public void onPlayerEnd(PlayItem playItem) {
        Log.i(TAG, "onPlayerEnd=" + playItem);
    }

    @Override
    public void onSeekStart(PlayItem playItem) {

    }

    @Override
    public void onSeekComplete(PlayItem playItem) {

    }

    @Override
    public void onBufferingStart(PlayItem playItem) {

    }

    @Override
    public void onBufferingEnd(PlayItem playItem) {

    }

    @Override
    public void onDownloadProgress(PlayItem playItem, long l, long l1) {

    }
}
