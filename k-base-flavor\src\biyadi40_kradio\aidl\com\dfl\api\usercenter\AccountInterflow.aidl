package com.dfl.api.usercenter;
import com.dfl.api.usercenter.AccountInterflowCallback;

/**
 * 账号互通，提供给第三方CPSP的 aidl
 * */
interface AccountInterflow {

    /*
     * 获取个人中心登录状态
     *
     * @return true/false，true 代表个人中心已登录；false 代表未登录
     * */
    boolean getLoginStatus();

    /*
     * 跳转个人中心对应界面
     *
     * @param cpType 固定值，由个人中心提供，用来 区分鉴定 CP 类型；例如高德是”1”，日产智行电 台是”2”
     * @param type 固定值，1 代表跳转登录界面，2 代表跳转会员购买界面，3 代表跳转车联服务-组合套餐页面，4 代表跳转超级服务包激活页，5 代表跳转设置页面
     */
    void openIcarPages(int cpType, int type);

    /**
     * 获取个人中心 TOKEN
     *
     * @param cpType 固定值，由个人中心提供，用来 区分鉴定 CP 类型
     * @return json字符串 {"uid": "uuid","token": "daToken","daid": "daId"}"
     */
    String getIcarToken(int cpType);

    /**
     * CP 应用登录成功通知个人中心
     *
     * @param cpUid CP 应用用户唯一标识
     * @param cpUsrNick CP 应用用户昵称
     * @param cpToken CP 用户 TOKEN，可不传
     * @param cpType 固定值，由个人中心提供，用来 区分鉴定 CP 类型
     */
    void bindCp(String cpUid, String cpUsrNick, String cpToken, int cpType);

    /**
     * CP 应用退登成功通知个人中心
     *
     * @param cpType 固定值，由个人中心提供，用来 区分鉴定 CP 类型
     */
    void unBindCp(int cpType);

    /**
     * 注册回调监听
     *
     * @param cpType 固定值，由个人中心提供，用来 区分鉴定 CP 类型
     * @param callback AccountInterflowCallback
     */
    void registerCallback(int cpType, AccountInterflowCallback callback);

    /**
     * 解除注册回调监听
     * @param cpType 固定值，由个人中心提供，用来 区分鉴定 CP 类型
     * @param callback AccountInterflowCallback
     */
    void unRegisterCallback(int cpType, AccountInterflowCallback callback);

    /**
     * 个人中心跳转cp登录之后，cp跳转回来个人中心的方法。此方法对应 AccountInterflowCallback 的 onloginAndBindBack() 方法
     *
     * @param cpType 固定值，由个人中心提供，用来 区分鉴定 CP 类型；例如高德是”1”，日产智行电 台是”2”
     * @param type 固定值，1 代表跳转个人中心首页，2 代表跳转会员购买界面，3 代表跳转车联服务-组合套餐页面, 4 代表跳转超级服务包激活页面
     */
    void backToIcar(int cpType, int type);

    /**
     * cp调用此方法获取cp的token和uid
     *
     * @param cpType 固定值，由个人中心提供，用来 区分鉴定 CP 类型
     */
    void reqCpToken(int cpType);

    /**
     * cp 调用此方法通知个人中心查询剩余流量
     *
     * @param cpType 固定值，由个人中心提供，用来 区分鉴定 CP 类型
     */
    void queryFlowResidue(int cpType);

    /**
     * 酷狗登录之后将openId传递给个人中心，用于购买会员时验证
     *
     * @param cpType 固定值，由个人中心提供，用来 区分鉴定 CP 类型
     * @param openId 酷狗的openId
     */
    void noticeOpenId(int cpType, String openId);

    /**
     * 获取实名认证状态
     *
     * @param cpType cp类型
     */
    void reqCerStatus(int cpType);

}
