package com.kaolafm.kradio.flavor.impl;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.Log;

import com.kaolafm.kradio.lib.base.flavor.KRadioThirdPlatformInitInter;

import com.kaolafm.opensdk.player.core.utils.PlayerCustomizeManager;


/**
 * <AUTHOR>
 **/
public class KRadioThirdPlatformInitImpl implements KRadioThirdPlatformInitInter {
    private static final String TAG = "KRadioThirdPlatformInitImpl";

    @SuppressLint("LongLogTag")
    @Override
    public boolean initThirdPlatform(Object... args) {
        RiChanHelper.getInstance((Context) args[0]).init();
        PlayerCustomizeManager.getInstance().setNeedRequestAudioFocus(false);
        return true;
    }

    @Override
    public boolean destroyThirdPlatform(Object... args) {
        return false;
    }
}
