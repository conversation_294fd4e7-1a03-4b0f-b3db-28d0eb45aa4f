package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.view.View;
import android.view.Window;

import com.kaolafm.kradio.lib.base.flavor.KRadioTransStatusBarInter;
import com.kaolafm.kradio.lib.utils.ScreenUtil;

import static com.kaolafm.kradio.lib.utils.ScreenUtil.setStatusBar;
import static com.kaolafm.kradio.lib.utils.ViewUtil.addPaddingForView;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-03-12 10:06
 ******************************************/
public final class KRadioTransStatusBarImpl implements KRadioTransStatusBarInter {
    private int mStatusBarHeight;

    public KRadioTransStatusBarImpl() {
        mStatusBarHeight = ScreenUtil.getStatusBarHeight();
    }

    @Override
    public boolean changeStatusBarColor(Activity activity, int colorRes) {
        Window window = activity.getWindow();
        setStatusBar(window, false);
        return true;
    }

    @Override
    public boolean changeViewLayoutForStatusBar(View view, int id) {
//        view.setPadding(0, mStatusBarHeight, 0, 0);
        addPaddingForView(view, 0, mStatusBarHeight, 0, 0);
        return true;
    }

    @Override
    public boolean canChangeViewLayoutForStatusBar(Object... args) {
        return false;
    }

    @Override
    public int getStatusBarHeight(Object... args) {
        return mStatusBarHeight;
    }
}
