package com.kaolafm.kradio.online.home.data;

import android.util.Log;
import android.util.LongSparseArray;

import com.kaolafm.kradio.home.HomeDataManager;
import com.kaolafm.kradio.home.data.Category;
import com.kaolafm.kradio.online.home.mvp.ICategoryModel;
import com.kaolafm.kradio.online.home.mvp.IGalleryModel;
import com.kaolafm.opensdk.api.operation.model.column.ColumnGrp;
import com.kaolafm.opensdk.http.core.HttpCallback;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 **/
public class HomePlayerModel implements IGalleryModel, ICategoryModel {
    private static final String TAG = "home.model";
    private List<Category> mCategories = new ArrayList<>();
    private LongSparseArray<List<Category.Group>> mGroups = new LongSparseArray();

    private volatile static HomePlayerModel mInstance;

    private HomePlayerModel() {

    }

    public static HomePlayerModel getInstance() {
        if (mInstance == null) {
            synchronized (HomePlayerModel.class) {
                if (mInstance == null) {
                    mInstance = new HomePlayerModel();
                }
            }
        }
        return mInstance;
    }




    @Override
    public void getDatas(HttpCallback<List<ColumnGrp>> callback) {
        Log.i(TAG, "刷新首页数据....");
        HomeDataManager.getInstance().request(callback);
    }

    @Override
    public void refresh() {
        mCategories.clear();
        mGroups.clear();
    }
}
