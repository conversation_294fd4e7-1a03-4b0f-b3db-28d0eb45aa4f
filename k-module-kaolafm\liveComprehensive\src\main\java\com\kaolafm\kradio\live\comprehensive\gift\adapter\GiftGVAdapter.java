package com.kaolafm.kradio.live.comprehensive.gift.adapter;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.kaolafm.kradio.live.comprehensive.gift.utils.RecyclerViewUtil;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.purchase.util.MoneyUtils;
import com.kaolafm.opensdk.api.live.model.Gift;
import com.kaolafm.opensdk.api.live.model.GiftsResult;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.util.ReportConstants;
import com.kaolafm.report.util.ReportParameterManager;

import static com.kaolafm.report.util.ReportConstants.DIALOG_ID_LIVE_ROOM_GIFT_LIST;

/**
 * Created by Ren on 2023/2/9.
 */
public class GiftGVAdapter extends RecyclerView.Adapter<GiftGVAdapter.ViewHodler> {
    private GiftsResult mGiftsResult;
    private RecyclerView mRecyclerView;
    private Context mContext;
    private boolean isNetData;

    private ViewHodler mHolder;
    private int clickTemp = -1;
    private RecyclerViewUtil recyclerViewUtil;

    //标识选择的Item
    public void setSeclection(int position) {
        clickTemp = position;
    }

    public int getSecletion() {
        return clickTemp;
    }

    public GiftGVAdapter(RecyclerView recyclerView, GiftsResult giftsResult, Context mContext) {
        super();
        this.mRecyclerView = recyclerView;
        this.mGiftsResult = giftsResult;
        this.mContext = mContext;
    }


    public void clear() {
        this.mContext = null;
    }

    @Override
    public ViewHodler onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.comprehensive_gift_item, parent, false);
        view.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mOnItemClickListener != null) {
                    final int position = ((RecyclerView) parent).getChildAdapterPosition(view);
                    final Gift giftModel = mGiftsResult.getGiftList().get(position);
                    mOnItemClickListener.onItemClick(view, giftModel, position);
                    clickTemp = position;
                    notifyDataSetChanged();
                }
            }
        });
        view.findViewById(R.id.item_btn).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                final int position = ((RecyclerView) parent).getChildAdapterPosition(view);
                final Gift giftModel = mGiftsResult.getGiftList().get(position);
                if (mOnGiftGiveListener != null) {
                    mOnGiftGiveListener.onGiftGive(v, giftModel, position);
                }

                String contentId = null, radioId = null, audioId = null;
                if (giftModel != null) {
                    contentId = String.valueOf(giftModel.getGiftId());
                }
                PlayItem curPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
                if (curPlayItem != null) {
                    radioId = curPlayItem.getRadioId();
                    audioId = String.valueOf(curPlayItem.getAudioId());
                }
                ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_LIVE_ROOM_GIFT_ITEM_SEND, ((TextView) v).getText().toString(), ReportParameterManager.getInstance().getPage()
                        , ReportConstants.CONTROL_TYPE_SCREEN, DIALOG_ID_LIVE_ROOM_GIFT_LIST, radioId, audioId, audioId,null, contentId));
            }
        });
        return new ViewHodler(view);
    }

    @Override
    public void onBindViewHolder(final ViewHodler holder, final int position) {
        final Gift giftModel = mGiftsResult.getGiftList().get(position);

        ImageLoader.getInstance().displayImage(mContext,
                giftModel.getGiftImg(),
                holder.itemImgNormal);
        ImageLoader.getInstance().displayImage(mContext,
                giftModel.getGiftImg(),
                holder.itemImgSelected);
        holder.itemName.setText(giftModel.getGiftName());
        String cost;
        if (giftModel.getGiftCost() != null && giftModel.getGiftCost() > 0) {
            cost = MoneyUtils.changeF2Y(giftModel.getGiftCost()) + "云币";
            holder.itemPriceNormal.setTextColor(mContext.getResources().getColor(R.color.comprehensive_gift_item_price_normal_color));
            holder.itemPriceSelected.setTextColor(mContext.getResources().getColor(R.color.comprehensive_gift_item_price_selected_color));
            holder.itemPriceSelected.getPaint().setFakeBoldText(false);
        } else {
            cost = "免费";
            holder.itemPriceNormal.setTextColor(mContext.getResources().getColor(R.color.comprehensive_gift_item_price_free_color));
            holder.itemPriceSelected.setTextColor(mContext.getResources().getColor(R.color.comprehensive_gift_item_price_free_color));
            holder.itemPriceSelected.getPaint().setFakeBoldText(true);
        }
        holder.itemPriceNormal.setText(cost);
        holder.itemPriceSelected.setText(cost);
        if (clickTemp == position) {
//            holder.llroot.setBackgroundResource(R.drawable.gift_shape_chose);
            onItemSelected(holder, true);
            mHolder = holder;
        } else {
//            holder.llroot.setBackgroundResource(R.drawable.gift_shape_tran);
            onItemSelected(holder, false);
        }
        if (giftModel.getRewardIntegral() != null
                && mGiftsResult.getShowRewardContent() != null
                && mGiftsResult.getShowRewardContent().equals(1)) {
            holder.itemPoints.setText("+" + giftModel.getRewardIntegral() + "积分");
            holder.itemPoints.setVisibility(View.VISIBLE);
        } else {
            holder.itemPoints.setVisibility(View.INVISIBLE);
        }
    }

    @Override
    public void onViewAttachedToWindow(ViewHodler holder) {
        super.onViewAttachedToWindow(holder);
        int adapterPosition = holder.getAdapterPosition();
        Gift giftModel = mGiftsResult.getGiftList().get(adapterPosition);
        String contentId = null, radioId = null, audioId = null;
        if (giftModel != null) {
            contentId = String.valueOf(giftModel.getGiftId());
        }
        PlayItem curPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
        if (curPlayItem != null) {
            radioId = curPlayItem.getRadioId();
            audioId = String.valueOf(curPlayItem.getAudioId());
        }
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_LIVE_ROOM_GIFT_ITEM_SEND, holder.itemBtn.getText().toString(), ReportParameterManager.getInstance().getPage()
                , ReportConstants.CONTROL_TYPE_SCREEN, DIALOG_ID_LIVE_ROOM_GIFT_LIST, radioId, audioId, audioId, null, contentId));
    }

    @Override
    public int getItemCount() {
        return mGiftsResult.getGiftList().size();
    }

    public void clearSelection() {
        if (mHolder != null) {
//            mHolder.llroot.setBackgroundResource(R.drawable.gift_shape_tran);
            onItemSelected(mHolder, false);
            mHolder = null;
        }
    }

    class ViewHodler extends RecyclerView.ViewHolder {
        //        ConstraintLayout llroot;
        LinearLayout itemNormal, itemSelected;
        ImageView itemImgNormal, itemImgSelected;
        TextView itemName, itemPriceNormal, itemPriceSelected, itemPoints, itemBtn;

        public ViewHodler(View view) {
            super(view);
//            llroot = (ConstraintLayout) view.findViewById(R.id.ll_gift_root);
            itemNormal = (LinearLayout) view.findViewById(R.id.item_normal);
            itemSelected = (LinearLayout) view.findViewById(R.id.item_selected);
            itemImgNormal = (ImageView) view.findViewById(R.id.item_img_normal);
            itemImgSelected = (ImageView) view.findViewById(R.id.item_img_selected);
            itemName = (TextView) view.findViewById(R.id.item_name);
            itemPriceNormal = (TextView) view.findViewById(R.id.item_price_normal);
            itemPriceSelected = (TextView) view.findViewById(R.id.item_price_selected);
            itemPoints = (TextView) view.findViewById(R.id.item_points);
            itemBtn = (TextView) view.findViewById(R.id.item_btn);
        }
    }

    public interface OnItemClickListener {
        void onItemClick(View view, Gift giftModel, int position);
    }

    public OnItemClickListener mOnItemClickListener;

    public void setOnItemClickListener(OnItemClickListener listener) {
        mOnItemClickListener = listener;
    }

    public interface OnGiftGiveListener {
        void onGiftGive(View view, Gift giftModel, int position);
    }

    public OnGiftGiveListener mOnGiftGiveListener;

    public void setOnGiftGiveListener(OnGiftGiveListener listener) {
        mOnGiftGiveListener = listener;
    }


    private void onItemSelected(ViewHodler holder, boolean isSelected) {
        if (isSelected) {
            holder.itemSelected.setVisibility(View.VISIBLE);
            holder.itemNormal.setVisibility(View.GONE);
        } else {
            holder.itemSelected.setVisibility(View.GONE);
            holder.itemNormal.setVisibility(View.VISIBLE);
        }

    }
}
