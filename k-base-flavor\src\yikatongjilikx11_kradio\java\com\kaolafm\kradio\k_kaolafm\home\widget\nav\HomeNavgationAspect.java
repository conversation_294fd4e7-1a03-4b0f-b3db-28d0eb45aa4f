package com.kaolafm.kradio.k_kaolafm.home.widget.nav;

import android.util.Log;

import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.utils.ResUtil;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

@Aspect
public class HomeNavgationAspect {

    private String TAG = "HomeNavgationAspect";

//    @Around("execution(* HomeNavigationLayout.changeHomeNavigationTabSize(..))")
//    public void changeNaviTabSize(ProceedingJoinPoint point) throws Throwable {
//        Log.i(TAG,"changeNaviTabSize...");
//        HomeNavigationLayout navigationLayout  = (HomeNavigationLayout) point.getThis();
//        Object[] args = point.getArgs();
//        Log.i(TAG,"origin params::"+args[0]+" "+args[1]);
//        args[0] = ResUtil.getDimen(R.dimen.text_size_title2);
//        args[1] = ResUtil.getDimen(R.dimen.text_size_title1);
//        Log.i(TAG,"changed params::"+args[0]+" "+args[1]);
//        point.proceed(args);
//    }
}
