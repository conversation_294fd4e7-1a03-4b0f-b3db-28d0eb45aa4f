package com.kaolafm.kradio.lib.base.mvp;

import android.app.Activity;
import androidx.lifecycle.LifecycleObserver;

/**
 * Presenter接口。
 *
 * <AUTHOR>
 * @date 2018/4/13
 * @see BasePresenter
 */

public interface IPresenter extends LifecycleObserver {

    /**
     * 主要做一些初始化的操作.
     * 在activity中是在{@link Activity#onStart()}中调用，fragment中没有调用，需要自己手动调用。
     */
    void start();

    /**
     * 主要做释放内存，注销操作。
     */
    void destroy();
}
