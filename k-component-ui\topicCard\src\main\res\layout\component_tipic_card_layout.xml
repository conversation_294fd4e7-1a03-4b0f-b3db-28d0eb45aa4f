<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/component_card_bg_7">


    <com.kaolafm.kradio.component.ui.base.view.OvalImageView
        android:id="@+id/card_bg_iv"
        android:layout_width="@dimen/m900"
        android:layout_height="@dimen/m432"
        android:minWidth="@dimen/m440"
        app:oval_radius="@dimen/m8" />

    <RelativeLayout
        android:id="@+id/radio_topic_rl"
        android:layout_width="@dimen/m900"
        android:layout_height="@dimen/m432"
        android:layout_centerInParent="true"
        android:minWidth="@dimen/m440"
        android:paddingLeft="@dimen/m30"
        android:paddingTop="@dimen/m32"
        android:paddingRight="@dimen/m30"
        android:paddingBottom="@dimen/m30">

        <LinearLayout
            android:id="@+id/radio_topic_ll"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/m47"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent">

            <TextView
                android:id="@+id/radio_topic_tag_tv"
                android:layout_width="@dimen/m80"
                android:layout_height="@dimen/m42"
                android:background="@drawable/car_owner_radio_back_topic_btn_bg"
                android:gravity="center"
                android:text="话题"
                android:textColor="#FFFFFCFB"
                android:textSize="@dimen/m20" />

            <TextView
                android:id="@+id/radio_topic_name_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/m10"
                android:layout_weight="1"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="1"
                android:text="#我的保养日记我的保养日记我的保养日记"
                android:textColor="#FFFFFCFB"
                android:textSize="@dimen/m32"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/radio_num_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/m20"
                android:gravity="center"
                tools:text="0人参与 0阅读量"
                android:textColor="#FFCFD6E6"
                android:textSize="@dimen/m24"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent" />
        </LinearLayout>

        <com.kaolafm.kradio.component.ui.base.view.KradioTextView
            android:id="@+id/radio_topic_des_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/radio_topic_ll"
            android:layout_marginTop="@dimen/m11"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:textColor="#FFFFFCFB"
            android:textSize="@dimen/m26"
            app:kt_font_weight="0.3"
            tools:text="每台车的特点其实都不一样，但总有最吸引你的一点，你为什么买这个你最不喜欢的车" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/tipic_min_card_rv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/radio_topic_des_tv"
            android:layout_marginTop="@dimen/m10"
            android:scrollbars="none" />
    </RelativeLayout>
</RelativeLayout>