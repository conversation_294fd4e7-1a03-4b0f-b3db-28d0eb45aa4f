package com.kaolafm.kradio.media.session;


import android.content.ComponentName;
import android.content.Context;
import android.media.AudioManager;
import android.support.v4.media.session.MediaSessionCompat;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-04-09 18:12
 ******************************************/
public final class KRMediaSessionDownLP implements KRMediaSessionInter {
    private ComponentName mbCN;
    private AudioManager mAudioManager;

    @Override
    public void registerMediaSession(Context context) {
        if (mbCN == null) {
            mbCN = new ComponentName(context, context.getPackageName());
        }
        if (mAudioManager == null) {
            mAudioManager = (AudioManager) context.getApplicationContext().getSystemService(Context.AUDIO_SERVICE);
        }
        mAudioManager.registerMediaButtonEventReceiver(mbCN);
    }

    @Override
    public void unregisterMediaSession(Context context) {
        if (mAudioManager == null || mbCN == null) {
            return;
        }
        mAudioManager.unregisterMediaButtonEventReceiver(mbCN);
    }

    @Override
    public MediaSessionCompat getMediaSession() {
        return null;
    }
}

