// MicroModule build file where you can declare MicroModule dependencies.
dependencies {
    implementation fileTree(dir: 'live/libs', include: ['*.jar'])
    implementation microModule(':common')
    implementation microModule(':player')
    implementation microModule(':history')
    implementation microModule(':user')


    //阿里云的对象存储
    implementation 'com.aliyun.dpa:oss-android-sdk:2.9.9'


//    //腾讯云关于视频直播的 https://cloud.tencent.com/document/product/454/7886
//    implementation 'com.tencent.liteavsdk:LiteAVSDK_Smart:8.3.9878'
}
