package com.kaolafm.kradio.flavor.impl;

import android.content.Context;

import com.kaolafm.kradio.byd.BydToast;
import com.kaolafm.kradio.lib.base.flavor.KRadioToastInter;
import com.kaolafm.kradio.lib.toast.ToastStyle;
import com.kaolafm.kradio.lib.toast.ToastUtil;

/**
 * @Package: com.kaolafm.kradio.flavor.impl
 * @Description:
 * @Author: Maclay
 * @Date: 15:21
 */
public class KRadioToastImpl implements KRadioToastInter {

    @Override
    public void showToast(Context context, String msg, int dur) {
        BydToast.show(context, msg, dur);
    }

    @Override
    public void showToast(int from, Context context, String msg) {
        if (from == 0) {
            ToastUtil.showOnly(context, msg);
        }
    }

    @Override
    public int displayLevel(Object... args) {
        return ToastStyle.LEVEL_ACTIVITY;
    }
}
