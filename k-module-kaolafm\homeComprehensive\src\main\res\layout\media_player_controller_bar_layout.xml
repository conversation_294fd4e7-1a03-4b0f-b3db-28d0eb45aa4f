<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="0.8"
        android:gravity="center">

        <com.kaolafm.kradio.common.widget.CImageButton
            android:id="@id/pcb_switch_playBack_mode"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/color_main_button_click_selector"
            android:padding="@dimen/stand_oval_click_button_padding"
            android:src="@drawable/player_all_mode_selector"
            android:visibility="invisible" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="0.8"
        android:gravity="center">

        <com.kaolafm.kradio.common.widget.CImageButton
            android:id="@id/pcb_like"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/color_main_button_click_selector"
            android:padding="@dimen/stand_oval_click_button_padding"
            android:src="@drawable/player_switch_like_selector"
            android:visibility="invisible" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="center">

        <com.kaolafm.kradio.common.widget.CImageButton
            android:id="@id/pcb_play_pre_audio"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/color_main_button_click_selector"
            android:padding="@dimen/stand_oval_click_button_padding"
            android:src="@drawable/player_pre_selector" />
    </LinearLayout>

    <RelativeLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1.2"
        android:gravity="center">

        <com.kaolafm.kradio.common.widget.CImageButton
            android:id="@id/pcb_switch_playState_mode"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:background="@null"
            android:src="@drawable/player_switch_play_selector" />

        <fr.castorflex.android.circularprogressbar.CircularProgressBar xmlns:app="http://schemas.android.com/apk/res-auto"
            android:id="@id/pcb_play_loading"
            style="@style/CustomerCircularProgressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:visibility="gone"
            app:cpb_color="@color/circular_progress_color"
            app:cpb_stroke_width="@dimen/loading_progress_width" />
    </RelativeLayout>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="center">

        <com.kaolafm.kradio.common.widget.CImageButton
            android:id="@id/pcb_play_next_audio"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/color_main_button_click_selector"
            android:padding="@dimen/stand_oval_click_button_padding"
            android:src="@drawable/player_next_selector" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="0.8"
        android:gravity="center">

        <com.kaolafm.kradio.common.widget.CImageButton
            android:id="@id/pcb_lrc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/color_main_button_click_selector"
            android:padding="@dimen/stand_oval_click_button_padding"
            android:src="@drawable/player_switch_lrc_selector"
            android:visibility="invisible" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="0.8"
        android:gravity="center">

        <com.kaolafm.kradio.common.widget.CImageButton
            android:id="@id/pcb_playlist"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/color_main_button_click_selector"
            android:padding="@dimen/stand_oval_click_button_padding"
            android:src="@drawable/player_playlist_selector" />
    </LinearLayout>
</merge>