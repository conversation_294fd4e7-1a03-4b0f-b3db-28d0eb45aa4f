package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.app.Dialog;
import android.content.Intent;
import android.os.Handler;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentActivity;

import android.util.Log;
import android.view.Gravity;

import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.base.flavor.KRadioBackKeyInter;
import com.kaolafm.kradio.lib.dialog.Dialogs;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.opensdk.player.logic.PlayerManager;

import me.yokeyword.fragmentation.ISupportFragment;
import me.yokeyword.fragmentation.SupportHelper;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-06-17 12:10
 ******************************************/
public final class KRadioBackKeyImpl implements KRadioBackKeyInter {
    private static final String TAG = "KRadioBackKeyImpl";

    @Override
    public boolean onBackPressed(Object... args) {

        final Activity activity = (Activity) args[0];

        DialogFragment dialogFragment = new Dialogs.Builder()
                .setType(Dialogs.TYPE_2BTN)
                .setGravity(Gravity.CENTER_VERTICAL)
                .setMessage("是否要退出云听?")
                .setLeftBtnText("退到后台")
                .setRightBtnText(ResUtil.getString(R.string.ok))
                .setOnPositiveListener(dialog -> {
                    PlayerManager.getInstance().pause(false);
                    activity.finish();
                    dialog.dismiss();
                }).setOnNativeListener(dialog -> {
                    Dialog childDialog = dialog.getDialog();
                    dialog.dismiss();
                    if (childDialog != null) {
                        childDialog.cancel();
                    }
                    new Handler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            Intent homeIntent = new Intent(Intent.ACTION_MAIN);
                            homeIntent.addCategory(Intent.CATEGORY_HOME);
                            homeIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED);
                            activity.startActivity(homeIntent);
                        }
                    }, 500);
                })
                .create();
        dialogFragment.show(((FragmentActivity) activity).getSupportFragmentManager(), "exit_app");
        return true;
    }

    @Override
    public boolean appExit(Object... args) {
        return false;
    }

    @Override
    public void dealKillYunTingReceiver(Object... args) {
    }
}
