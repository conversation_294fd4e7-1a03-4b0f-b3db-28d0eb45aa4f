package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.kaolafm.kradio.lib.base.flavor.KRadioWidgetUpdateInter;

import cmgyunting.vehicleplayer.cnr.YunTingWidgetService;
import cmgyunting.vehicleplayer.cnr.widget.YunTingDophoinWidgetService;

public class KRadioWidgetUpdateImpl implements KRadioWidgetUpdateInter {

    private static final String TAG = "KRadioWidgetUpdateImpl";

    @Override
    public void updateWidget(Context context) {
        //重启普通服务
        Log.i(TAG, "onEnable ----- ");
        Intent intent = new Intent(context, YunTingWidgetService.class);
        intent.setAction(YunTingWidgetService.WIDGET_ACTION_REFRESH);
        intent.putExtra("widget_policy", YunTingWidgetService.WIDET_POLICY_NORAML);
        intent.putExtra("startServer", true);
        context.startService(intent);
        //重启海豚服务
        Intent dophinIntent = new Intent(context, YunTingDophoinWidgetService.class);
        intent.setAction(YunTingWidgetService.WIDGET_ACTION_REFRESH);
        intent.putExtra("widget_policy", YunTingDophoinWidgetService.WIDET_POLICY_NORAML);
        intent.putExtra("startServer", true);
        context.startService(dophinIntent);
    }
}
