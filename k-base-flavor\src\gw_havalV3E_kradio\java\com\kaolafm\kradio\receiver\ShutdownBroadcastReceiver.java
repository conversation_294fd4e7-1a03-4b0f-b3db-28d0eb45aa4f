package com.kaolafm.kradio.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.kaolafm.kradio.lib.base.flavor.ShutdownLogicListener;
import com.kaolafm.kradio.lib.utils.InjectManager;


/**
 * Created by Wenchl on 2018/3/5.
 */

public class ShutdownBroadcastReceiver extends BroadcastReceiver {

    @Override
    public void onReceive(Context context, Intent intent) {
        if (Intent.ACTION_SHUTDOWN.equals(intent.getAction())) {
            ShutdownLogicListener shutdownLogicListener = InjectManager.getInstance().getShutdownLogicListener();
            if (shutdownLogicListener != null) {
                shutdownLogicListener.onShutdownLogic(context, intent);
            }
        }
    }
}
