package com.kaolafm.ad.comprehensive.ads.image;

public class AdContentInfo {
    private String id;
    private String imageUrl;
    private int skipTime;
    private long imageDuration;
    private String localPath;
    private boolean isPreload;
    private boolean isJump;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public int getSkipTime() {
        return skipTime;
    }

    public void setSkipTime(int skipTime) {
        this.skipTime = skipTime;
    }

    public long getImageDuration() {
        return imageDuration;
    }

    public void setImageDuration(long imageDuration) {
        this.imageDuration = imageDuration;
    }

    public String getLocalPath() {
        return localPath;
    }

    public void setLocalPath(String localPath) {
        this.localPath = localPath;
    }

    public boolean isPreload() {
        return isPreload;
    }

    public void setPreload(boolean preload) {
        isPreload = preload;
    }

    public boolean isJump() {
        return isJump;
    }

    public void setJump(boolean jump) {
        isJump = jump;
    }
}
