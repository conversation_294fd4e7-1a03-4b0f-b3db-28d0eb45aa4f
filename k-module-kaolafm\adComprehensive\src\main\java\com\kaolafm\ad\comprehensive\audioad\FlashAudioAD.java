package com.kaolafm.ad.comprehensive.audioad;

import com.kaolafm.ad.comprehensive.control.KradioAdSceneConstants;
import com.kaolafm.ad.comprehensive.listener.IFlashScreenAdPlayerListener;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.TempTaskPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

/**
 * @ClassName FlashAudioAD
 * @Description 闪屏广告(闪屏广告)
 * <AUTHOR>
 * @Date 2020-03-11 17:36
 * @Version 1.0
 */
public class FlashAudioAD extends BaseAudioAD {

    /**
     * 处理完闪屏音频广告 回调
     */
    private IFlashScreenAdPlayerListener mFlashScreenAdPlayerListener;
    private boolean isFlashAudioADShow;

    @Override
    public void onGetData() {
        startPlay(makeAudioAdPlayItem(mAudioAdvert.getLocalPath(), KradioAdSceneConstants.SUB_TYPE_SPRAND_SCREEN));
    }

    @Override
    public void onError() {
        isFlashAudioADShow = true;
        notifyFlashScreenAdPlayEnd();
    }

    @Override
    protected TempTaskPlayItem makeAudioAdPlayItem(String url, int type) {
        TempTaskPlayItem adPlayItem = super.makeAudioAdPlayItem(url, type);
        adPlayItem.setNeedNextInnerAction(false);
        adPlayItem.setPlayStateListener(new BasePlayStateListener() {
            @Override
            public void onPlayerEnd(PlayItem playItem) {
                notifyFlashScreenAdPlayEnd();
                playAudioAdEnd();
            }
        });
        return adPlayItem;
    }


    private void notifyFlashScreenAdPlayEnd() {
        if (mFlashScreenAdPlayerListener != null) {
            mFlashScreenAdPlayerListener.onPlayEnd();
        }
    }

    @Override
    public void setCallback(Object o) {
        if (o instanceof IFlashScreenAdPlayerListener) {
            if (isFlashAudioADShow) {
                ((IFlashScreenAdPlayerListener) o).onPlayEnd();
                return;
            }
            mFlashScreenAdPlayerListener = (IFlashScreenAdPlayerListener) o;
        }
    }
}
