package com.kaolafm.mian_tab;

import com.kaolafm.kradio.lib.base.mvp.IView;
import com.kaolafm.mian_tab.bean.MainBean;
import com.kaolafm.opensdk.api.maintab.model.MainTabBean;
import com.kaolafm.opensdk.http.error.ApiException;

import java.util.List;

public interface IMainTabView extends IView {
    void onLoading();
    void onLoadFinish();
    void onTabDate(List<MainTabBean> bean);
    void onTabDateError(String error);

}
