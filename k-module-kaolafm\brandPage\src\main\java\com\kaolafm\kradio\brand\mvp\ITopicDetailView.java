package com.kaolafm.kradio.brand.mvp;


import com.kaolafm.kradio.lib.base.mvp.IView;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.topic.model.TopicDetail;
import com.kaolafm.opensdk.api.topic.model.TopicPosts;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-03-01
 */
public interface ITopicDetailView extends IView {
    void onGetTopicDetailSuccess(TopicDetail topicDetails);

    void onGetTopicDetailFailure();

    void onGetPostsListSuccess(BasePageResult<List<TopicPosts>> result);

    void onGetPostsListFailure();

    void onOperatePostsSuccess(TopicPosts mTopicPosts, int position);

    void onOperatePostsFailure(int operationType);

    void onPublishPostsSuccess();

    void onPublishPostsFailure();
}
