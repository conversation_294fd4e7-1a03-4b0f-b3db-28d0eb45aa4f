package com.kaolafm.kradio.lib.utils;

import android.util.Log;


import com.kaolafm.kradio.lib.BuildConfig;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 **/
public class Logger {

    public static void print(String tag, List list) {
        if (!BuildConfig.DEBUG) {
            return;
        }
        if (list == null) {
            Log.i(tag, "printList:list is null.");
        } else {
            Log.i(tag, "printList: ");
            print(tag, list.iterator());
        }
    }

    public static <T> void print(String tag, Map<Long, List<T>> map) {
        if (!BuildConfig.DEBUG) {
            return;
        }
        if (map == null || map.isEmpty()) {
            Log.i(tag, "printMap:empty.");
        } else {
            Log.i(tag, "printMap: ");
            Set<Map.Entry<Long, List<T>>> entries = map.entrySet();
            Iterator<Map.Entry<Long, List<T>>> iterator = entries.iterator();
            while (iterator.hasNext()) {
                Map.Entry<Long, List<T>> item = iterator.next();
                List<T> value = item.getValue();
                Log.i(tag, "   :key=" + item.getKey());
                print(tag, value);
            }
        }
    }

    public static void print(String tag, Set set) {
        if (!BuildConfig.DEBUG) {
            return;
        }
        if (set == null) {
            Log.i(tag, "printSet:set is null.");
        } else {
            Log.i(tag, "printSet: ");
            print(tag, set.iterator());
        }
    }

    public static void print(String tag, Iterator iterator) {
        if (!BuildConfig.DEBUG) {
            return;
        }
        if (iterator == null) {
            Log.i(tag, "print:array is null.");
        } else {
            Log.i(tag, "printIterator: ");
            while (iterator.hasNext()) {
                Log.i(tag, "" + iterator.next());
            }
        }
    }

    public static void v(String tag, String s) {
        if (!BuildConfig.DEBUG) {
            return;
        }
        Log.v(tag, s);
    }

    public static void d(String tag, String s) {
        if (!BuildConfig.DEBUG) {
            return;
        }
        Log.i(tag, s);
    }

    public static void i(String tag, String s) {
        if (!BuildConfig.DEBUG) {
            return;
        }
        Log.i(tag, s);
    }

    public static void w(String tag, String s) {
        if (!BuildConfig.DEBUG) {
            return;
        }
        Log.w(tag, s);
    }

    public static void e(String tag, String s) {
        if (!BuildConfig.DEBUG) {
            return;
        }
        Log.e(tag, s);
    }


}
