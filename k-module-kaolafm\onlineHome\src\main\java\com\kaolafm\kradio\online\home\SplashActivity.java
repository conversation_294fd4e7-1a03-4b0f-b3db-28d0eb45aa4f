package com.kaolafm.kradio.online.home;

import static com.kaolafm.kradio.lib.utils.Constants.CLIENT_EXTRA_TYPE;
import static com.kaolafm.kradio.lib.utils.Constants.INVALID_NUM;
import static com.kaolafm.kradio.lib.utils.Constants.START_PAGE;
import static com.kaolafm.kradio.lib.utils.Constants.START_TAG;
import static com.kaolafm.report.event.ButtonClickReportEvent.ONLINE_BUTTON_DISAGREE;
import static com.kaolafm.report.event.ButtonClickReportEvent.ONLINE_BUTTON_START;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.ActivityManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.net.http.SslError;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.os.SystemClock;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.constraintlayout.widget.Group;
import androidx.core.app.ActivityCompat;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.webkit.SslErrorHandler;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.ad.control.KradioAdSceneConstants;
import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.ad.timer.TimedAdvertManager;
import com.kaolafm.flavor.AccountInterworkInter;
import com.kaolafm.ad.ADDataHandle.ADMapCallback;
import com.kaolafm.ad.ADDataHandle.GetAdDataMap;
import com.kaolafm.ad.KradioAdAudioManager;
import com.kaolafm.ad.adreport.AdReportImpl;
import com.kaolafm.ad.conflict.AdConflict;
import com.kaolafm.ad.utils.ADUtils;
import com.kaolafm.ads.image.AdvertisingImagerImpl;
import com.kaolafm.ads.image.SplashAdHelper;
import com.kaolafm.kradio.common.KradioSDKManager;
import com.kaolafm.kradio.common.SkinStateManager;
import com.kaolafm.kradio.common.base.BaseApplication;
import com.kaolafm.kradio.common.base.BaseSkinAppCompatActivityLight;
import com.kaolafm.kradio.common.helper.SkinHelper;
import com.kaolafm.kradio.common.report.ReportUtil;
import com.kaolafm.kradio.common.utils.DiskUtil;
import com.kaolafm.kradio.common.utils.FlavorUtil;
import com.kaolafm.kradio.common.utils.PermissionUtils;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.base.flavor.DeviceInfoSettingASync;
import com.kaolafm.kradio.lib.base.flavor.KRadioApplicationRequestAudioFocusInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioCustomizedStartupLogicInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioDialogActivityInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioFullScreenInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioHubInitInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioHubOnStop;
import com.kaolafm.kradio.lib.base.flavor.KRadioHubUserPromptInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioOperateClickCellInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioPermissionInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioRequestAudioFocusInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioTransStatusBarInter;
import com.kaolafm.kradio.lib.base.flavor.UserCenterInter;
import com.kaolafm.kradio.lib.common.ModelConstant;
import com.kaolafm.kradio.lib.init.ModelManager;
import com.kaolafm.kradio.lib.sdk.utils.PerformanceSettingMananger;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.IntentUtils;
import com.kaolafm.kradio.lib.utils.KaolaAppConfigData;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.lib.utils.SharedPreferenceUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.online.common.web.OnlineWebViewActivity;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.scene.launcher.InitService;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonClickReportEvent;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;

/**
 * 在线电台-启动页
 */
public class SplashActivity extends BaseSkinAppCompatActivityLight {
    private String TAG = SplashActivity.class.getSimpleName();

    // 解决https://app.huoban.com/tables/2100000007530121/items/2300001182976364问题
    private static int delayTime = 1000; // 延迟加载时间
    private boolean isGoLauncher = true;
    PermissionUtils mPermissionUtils;
    GetAdDataMap mGetAdDataMap;

    private KradioSDKManager.OnUsableObserver mUsableObserver;
    private AdvertisingImagerImpl mAdvertisingImager;

    MyHandler mMyHandler;
    private Group group;
    private Group group1;
    private ImageView ivBack;
    private boolean SplashActivityInBackground = false;

    private boolean canGoNextPage = false;

    private boolean isFirstOnResume = true;

    public static final int STATE_NONE = 100;
    public static final int STATE_NORMAL_INIT = 101;
    public static final int STATE_NO_INIT = 102;
    public static final int STATE_REQUEST_PERMISSION = 103;
    public static final int STATE_GET_ADDATA = 104;
    public static final int STATE_GOTO_NEXTPAGE = 105;
    public static final int STATE_GETDEVICEID_ASYNC = 106;

    private static final int PERMISSION_REQUEST_CODE = 10000;

    private int mState = STATE_NONE;
    Map<Integer, Integer> mStateMap = new HashMap<>();

    private boolean isExecute = false;
    boolean showNotice;

    //是否在权限拒绝退出的时候自我释放音频焦点
    private boolean isAbandonAudioFocusSelf = false;

    //统计tips显示时常
    protected long startTime = -1;
    private String pageId = "";

    private TextView tvTitle;
    private View view_line;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        Log.i(Constants.START_TAG, "SplashActivity oncreate...");
        Log.i(Constants.START_TAG, "SplashActivity oncreate..." + SkinHelper.isDayMode());
        super.onCreate(savedInstanceState);
        ModelManager.getInstance().setModel(ModelConstant.MODEL_ONLINE);
        Constants.HubActivityStartTime = SystemClock.elapsedRealtime();
        mPermissionUtils = new PermissionUtils(this);
        KRadioHubUserPromptInter kRadioHubUserPromptInter = ClazzImplUtil.getInter("KRadioHubUserPromptImpl");
        if (kRadioHubUserPromptInter != null && kRadioHubUserPromptInter.showUserPrompt(this)) {
            kRadioHubUserPromptInter.showPrompt(this);
        } else {
            normalOnCreate();
        }


        KRadioDialogActivityInter inter = ClazzImplUtil.getInter("KRadioDialogActivityImpl");
        if (inter != null) inter.handleHubActivity(this);

        ReportHelper.getInstance().initBySdk();
    }

    public void normalOnCreate() {
        initState();
        appEntryCheck();
        init();
    }

    private void init() {
        BaseApplication.getInstance().addActivity(this);

        //此定制类存在,是需要在权限申请前，申请音频焦点
        KRadioApplicationRequestAudioFocusInter kRadioRequestAudioFocusInter = ClazzImplUtil.getInter("KRadioApplicationRequestAudioFocusImpl");
        if (kRadioRequestAudioFocusInter != null) {
            kRadioRequestAudioFocusInter.requestAudioFocusBySelf();
        }
        Log.i("PlayerInitializer", "application init RequestAudioFocus ------>kRadioRequestAudioFocusInter = " + kRadioRequestAudioFocusInter);

        mPermissionUtils = new PermissionUtils(this);
        requestPermission();

        boolean isWindowFullScreen = getResources().getBoolean(com.kaolafm.kradio.lib.R.bool.isWindowFullScreen);
        if (!isWindowFullScreen) {
            KRadioTransStatusBarInter kRadioTransStatusBarInter = ClazzImplUtil.getInter("KRadioTransStatusBarImpl");
            if (kRadioTransStatusBarInter != null) {
                kRadioTransStatusBarInter.changeStatusBarColor(this, com.kaolafm.kradio.lib.R.color.transparent_color);
            }
        }
    }

    private class NoticeHolder {
        boolean isChecked = false;
        int web = 0;
    }

    private NoticeHolder noticeHolder;

    private NoticeHolder initNoticeHolder() {
        if (noticeHolder == null) {
            noticeHolder = new NoticeHolder();
        }
        return noticeHolder;
    }


    private void showNotice() {
        Log.i(Constants.START_TAG, "SplashActivity showNotice..." + SkinHelper.isDayMode());
        setContentView(R.layout.online_launch_notice);
//        if (noticeHolder == null) {
//            ToastUtil.showInfo(this, R.string.launcher_agreement_first_toast);
//        }
        showNotice = true;
        startTime = System.currentTimeMillis();
        TextView tvContent = findViewById(R.id.tvContent);
        tvTitle = findViewById(R.id.tvTitle);
        view_line = findViewById(R.id.view_line);
        String content = getString(R.string.launch_notice_content);
        int index_start = content.indexOf("《", 0);
        int index_end = content.indexOf("》", index_start) + 1;
        int index_start1 = content.indexOf("《", index_end);
        int index_end1 = content.indexOf("》", index_start1) + 1;
        group = findViewById(R.id.group);
        group1 = findViewById(R.id.group1);
        ivBack = findViewById(R.id.ivBack);
        int expand = (int) getResources().getDimension(R.dimen.m60);
        ViewUtil.expandViewTouchDelegate(ivBack, expand, expand, expand, expand);
        ivBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                group1.setVisibility(View.GONE);
                group.setVisibility(View.VISIBLE);
                tvTitle.setText(R.string.launch_notice);
                view_line.setVisibility(View.INVISIBLE);
                initNoticeHolder().web = 0;
//                startTime = System.currentTimeMillis() - startTime;
//                ReportUtil.addPageShowEvent(startTime, pageId);
//                startTime = -1;
            }
        });
        WebView webView = findViewById(R.id.webView);
        if (Build.VERSION.SDK_INT < 19 && webView != null) {
            webView.removeJavascriptInterface("searchBoxJavaBridge_");
            webView.removeJavascriptInterface("accessibility");
            webView.removeJavascriptInterface("accessibilityTraversal");
        }

        SpannableStringBuilder spannable = new SpannableStringBuilder(content);
        spannable.setSpan(new ClickableSpan() {
                              @Override
                              public void onClick(@NonNull View widget) {
                                  if (!NetworkUtil.isNetworkAvailable(SplashActivity.this, true)) {
                                      return;
                                  }
//                                  group1.setVisibility(View.VISIBLE);
//                                  group.setVisibility(View.GONE);
                                  String theme = "dark";
                                  if (SkinHelper.isDayMode()) {
                                      theme = "light";
                                  }
                                  String web = ResUtil.getString(R.string.http_url_server_agreement)
                                          + "?theme=" + theme + "&bgColor=transparent&contentSize="
                                          + 13
                                          + "&showTitle=1";
                                  OnlineWebViewActivity.start(SplashActivity.this,
                                          FlavorUtil.getHttp443Url(web), getResources().getString(R.string.launcher_agreement0)
                                          , Constants.ONLINE_PAGE_ID_ACCOUNT_PROTOCOL);
//                                  if (tvTitle != null)
//                                      tvTitle.setText(getResources().getString(R.string.launcher_agreement0));
//                                  startTime = System.currentTimeMillis();
                                  pageId = Constants.ONLINE_PAGE_ID_ACCOUNT_PROTOCOL;
//                                  initNoticeHolder().web = 1;
//                                  OnlineWebViewActivity.start(SplashActivity.this,web);
                              }

                              @Override
                              public void updateDrawState(@NonNull TextPaint ds) {
                                  super.updateDrawState(ds);
                                  ds.setUnderlineText(false);
                              }
                          }, index_start, index_end
                , Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannable.setSpan(new ForegroundColorSpan(ResUtil.getColor(R.color.agreement_content_jump_txt)), index_start, index_end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannable.setSpan(new ClickableSpan() {
                              @Override
                              public void onClick(@NonNull View widget) {
                                  if (!NetworkUtil.isNetworkAvailable(SplashActivity.this, true)) {
                                      return;
                                  }
                                  String theme = "dark";
                                  if (SkinHelper.isDayMode()) {
                                      theme = "light";
                                  }
//                                  group1.setVisibility(View.VISIBLE);
//                                  group.setVisibility(View.GONE);
                                  String CUR_PAGE = ResUtil.getString(R.string.http_url_policy)
                                          + "?theme=" + theme + "&bgColor=transparent&contentSize="
                                          + 13
                                          + "&showTitle=1";
                                  OnlineWebViewActivity.start(SplashActivity.this,
                                          FlavorUtil.getHttp443Url(CUR_PAGE), getResources().getString(R.string.launcher_agreement1)
                                          , Constants.ONLINE_PAGE_ID_ACCOUNT_PRIVATE);
//                                  showWebView(webView, );
//                                  view_line.setVisibility(View.VISIBLE);
//                                  if (tvTitle != null)
//                                      tvTitle.setText(getResources().getString(R.string.launcher_agreement1));
//                                  initNoticeHolder().web = 2;

                                  pageId = Constants.ONLINE_PAGE_ID_ACCOUNT_PRIVATE;
                                  startTime = System.currentTimeMillis();

                              }

                              @Override
                              public void updateDrawState(@NonNull TextPaint ds) {
                                  super.updateDrawState(ds);
                                  ds.setUnderlineText(false);
                              }
                          }, index_start1, index_end1
                , Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannable.setSpan(new ForegroundColorSpan(ResUtil.getColor(R.color.agreement_content_jump_txt)), index_start1, index_end1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        tvContent.setText(spannable);
        tvContent.setMovementMethod(LinkMovementMethod.getInstance());
        CheckBox cb = findViewById(R.id.cb);
        TextView tvCb = findViewById(R.id.tvCb);
        tvCb.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                cb.performClick();
            }
        });
        cb.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                initNoticeHolder().isChecked = isChecked;
            }
        });
        ((TextView) findViewById(R.id.tvStart)).setTextColor(ResUtil.getColor(R.color.online_splash_tips_btn_text_color));
        findViewById(R.id.tvStart).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //点击事件上报
                ButtonClickReportEvent event = new ButtonClickReportEvent(ONLINE_BUTTON_START);
                ReportHelper.getInstance().addEvent(event);

                boolean agree = cb.isChecked();
                if (agree) {
                    showNotice = false;
                    startTime = System.currentTimeMillis();
                    ReportUtil.addPageShowEvent(startTime, getPageId());
                    startTime = -1;
                    SharedPreferenceUtil.getInstance(v.getContext(), "k_notice", Context.MODE_PRIVATE).putBoolean("show", false);
                    normalInit();
                    onResume();
                } else {
                    ToastUtil.showInfo(SplashActivity.this, R.string.launcher_agreement_first_toast);
                }
            }
        });
        ((TextView) findViewById(R.id.tvExit)).setTextColor(ResUtil.getColor(R.color.online_splash_tips_btn_text_color));
        findViewById(R.id.tvExit).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //点击事件上报
                ButtonClickReportEvent event = new ButtonClickReportEvent(ONLINE_BUTTON_DISAGREE);
                ReportHelper.getInstance().addEvent(event);
                startTime = System.currentTimeMillis();
                ReportUtil.addPageShowEvent(startTime, getPageId());
                startTime = -1;
                finish();
            }
        });
        if (noticeHolder != null) {
            cb.setChecked(noticeHolder.isChecked);
            switch (noticeHolder.web) {
                case 0:
                    //nothing
                    break;
                case 1:
                    group1.setVisibility(View.VISIBLE);
                    group.setVisibility(View.GONE);
                    tvTitle.setText("");
//                    String web = FlavorUtil.getHttp443Url(ResUtil.getString(R.string.http_url_server_agreement));
//                    showWebView(webView, web);
                    break;
                case 2:
                    group1.setVisibility(View.VISIBLE);
                    group.setVisibility(View.GONE);
//                    tvTitle.setText("");
//                    showWebView(webView, FlavorUtil.getHttp443Url(ResUtil.getString(R.string.http_url_policy)));
                    break;
            }
        }
    }

    private void showWebView(WebView webView, String url) {
        KaolaAppConfigData kaolaAppConfigData = KaolaAppConfigData.getInstance();
        StringBuilder sb = new StringBuilder(url);
        String appId = kaolaAppConfigData.getAppId();
        sb.append(appId);
        //支持黑夜模式和白天模式
        UserCenterInter userCenterInter = ClazzImplUtil.getInter("UserCenterInterImpl");
//        if (userCenterInter != null && userCenterInter.supportDarkLightSwitch()) {
//            sb.append("&theme=");
//            sb.append(userCenterInter.getTheme(SplashActivity.this));
////            int skin = getContext().getResources().getConfiguration().uiMode;
////            if (TextUtils.equals(skin, "night")) {
////                sb.append("&theme=dark");
////            } else {
////                sb.append("&theme=light");
////            }
//            webView = userCenterInter.getWebView(SplashActivity.this, sb.toString());
//        } else {
//            sb.append("&theme=");
//            if (SkinHelper.isNightMode()) {
//                sb.append("dark");
//            } else {
//                sb.append("light");
//            }
//        }
//        sb.append("&version=").append(AppInfoUtil.getVersionName(this));
        setWebViewSetting(webView);
        webView.clearView();
        String httpUrl = sb.toString();
        webView.setVisibility(View.INVISIBLE);
        webView.loadUrl(url);
    }

    @SuppressLint("SetJavaScriptEnabled")
    private void setWebViewSetting(WebView webViewContent) {
        WebSettings settings = webViewContent.getSettings();
        settings.setUseWideViewPort(true);
        settings.setJavaScriptEnabled(true);
        settings.setSavePassword(false);
//        settings.setTextZoom(ResUtil.getInt(R.integer.online_web_view_zoom_size));
        //自动加载图片
        settings.setLoadsImagesAutomatically(true);
//        settings.setAppCacheEnabled(true);
        settings.setCacheMode(WebSettings.LOAD_NO_CACHE);//不使用缓存，只从网络获取数据.
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            // 5.0以上允许加载http和https混合的页面(5.0以下默认允许，5.0+默认禁止)
            settings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        }
        webViewContent.setBackgroundColor(Color.TRANSPARENT);
        webViewContent.getBackground().setAlpha(1);
//        webViewContent.setDrawingCacheEnabled(false);
        webViewContent.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                //可处理loading开始
                super.onPageStarted(view, url, favicon);
            }

            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
//                return super.shouldOverrideUrlLoading(view, url);
                view.loadUrl(url);
                return true;
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                //可处理loading结束
                super.onPageFinished(view, url);
                webViewContent.setVisibility(View.VISIBLE);

            }

            @Override
            public void onReceivedSslError(WebView view, SslErrorHandler handler, SslError error) {
                handler.cancel();
            }
        });
        webViewContent.setWebChromeClient(new WebChromeClient());


    }

    private void initState() {
        mState = STATE_NONE;
        //这里记录不能进行状态跳转的状态组合
        mStateMap.put(STATE_NO_INIT, STATE_NORMAL_INIT);
    }

    private void updateState(int aState) {
        mState = aState;
        updateState();
    }

    private void updateNextState(int toState) {
        //查找当前状态是否可以跳转到下一个状态
        if (mStateMap.get(mState) != null) {
            return;
        }
        updateState(toState);
    }

    private void updateState() {
        Log.i("updateState", "mState = " + mState);
        switch (mState) {
            case STATE_NO_INIT: {
                break;
            }
            case STATE_NORMAL_INIT: {
                initAndActivateSDK();
                //是否显示云听使用提示
                boolean shouldShow = SharedPreferenceUtil.getInstance(this, "k_notice", Context.MODE_PRIVATE).getBoolean("show", true);
                Log.d("SplashActivity", "show notice " + shouldShow);
                if (shouldShow) {
                    showNotice();
                    return;
                }
                normalInit();
//                updateState(STATE_REQUEST_PERMISSION);
                break;
            }
            case STATE_REQUEST_PERMISSION: {
                requestPermission();
                break;
            }
            case STATE_GET_ADDATA: {
                goLauncher();
                break;
            }
            case STATE_GOTO_NEXTPAGE: {
                mGetAdDataMap.clearAllCB();
                goNextPage();
                break;
            }
            case STATE_GETDEVICEID_ASYNC: {
                break;
            }
            default:
                break;
        }
    }

    private void initLocation() {
        InitService.getLocation();
    }

//    @RequiresApi(api = Build.VERSION_CODES.Q)
//    int debug() {
//        Log.e("rsq", "KradioApplication.mList.size()" + KradioApplication.mList.size());
//        for (int i = 0; i < KradioApplication.mList.size(); ++i) {
//            Activity activity = (Activity) KradioApplication.mList.get(i);
//            Log.e("rsq", activity.getClass().getName());
//        }
//
//        int activityCount = 0;
//        ActivityManager activityManager = (ActivityManager) getSystemService(ACTIVITY_SERVICE);
//        // 获取任务栈列表
//        List<ActivityManager.AppTask> tasks = activityManager.getAppTasks();
//        for (ActivityManager.AppTask task : tasks) {
//            ActivityManager.RecentTaskInfo info = task.getTaskInfo();
//            activityCount = info.numActivities; //栈内Activity数量
//            String topActivity = info.topActivity.getClassName(); //栈顶Activity
//            String baseActivity = info.baseActivity.getClassName(); //栈底Activity
//            Log.e("rsq", "topActivity is " + topActivity);
//            Log.e("rsq", "baseActivity is " + baseActivity);
//        }
//        return activityCount;
//    }

    public void normalInit() {
        initTheme();
        initFullScreen();
        setContentView(R.layout.online_splash_layout);

        mGetAdDataMap = GetAdDataMap.getInstance();
        mMyHandler = new MyHandler(this);


        initAudioFocus();
        handleOutCallAudio();
        notifyAppOpen();
        KRadioHubInitInter kRadioHubInitInter = ClazzImplUtil.getInter("KRadioHubInitImpl");
        if (kRadioHubInitInter != null && kRadioHubInitInter.hasPrivacyMode(this)) {
            return;
        }
        updateState(STATE_GET_ADDATA);
    }

    private void notifyAppOpen() {
        IntentUtils.getInstance().notifyAppOpen(AppDelegate.getInstance().getContext());
    }

    /**
     * 所有需要改变启动正常初始化流程的逻辑放在这里
     */
    private void appEntryCheck() {
        //空间不足判断
        if (!DiskUtil.checkFreeSpace(this)) {
            //弹出空间不足的提示
            DiskUtil.createDialog(this);
            updateState(STATE_NO_INIT);
        }

//        //大众账号需求
        KRadioCustomizedStartupLogicInter kRadioCustomizedStartupLogicInter
                = ClazzImplUtil.getInter("KRadioCustomizedStartupLogicInterImpl");
        if (null != kRadioCustomizedStartupLogicInter) {
            kRadioCustomizedStartupLogicInter.checkVWSystemAccount(
                    AppDelegate.getInstance().getContext(),
                    valid -> {
                        if (valid) {
                            updateState(STATE_NORMAL_INIT);
                        }
                    });
            updateState(STATE_NO_INIT);
        }

        //gmmc渠道需求
//        if (!this.isTaskRoot() && getIntent() != null) {
//            String action = getIntent().getAction();
//            if (getIntent().hasCategory(Intent.CATEGORY_LAUNCHER) && Intent.ACTION_MAIN.equals(action)) {
//                Log.i(Constants.START_TAG, "action = " + action);
//                startToLauncher();
//                finish();
//                updateState(STATE_NO_INIT);
//            }
//        }

        DeviceInfoSettingASync deviceInfoSettingASync = ClazzImplUtil.getInter("DeviceInfoSettingASyncImpl");
        //有异步获取did的车机，这里不初始化sdk，防止生成默认的did激活，
        if (deviceInfoSettingASync != null) {
            deviceInfoSettingASync.setInfoForSDK(this, isSuccess -> {
                Log.d(START_TAG, "deviceInfoSettingASync.setInfoForSDK =" + isSuccess);
                if (isSuccess) {
                    updateNextState(STATE_NORMAL_INIT);
                }
            });
        } else {
            updateNextState(STATE_NORMAL_INIT);
        }
    }

    /**
     * 新增语音外调SDK launchApp自动播放逻辑兼容
     */
    private void handleOutCallAudio() {
        Intent intent = getIntent();
        if (intent != null) {
            boolean autoPlay = IntentUtils.getInstance().isAutoPlay(intent)/*intent.getBooleanExtra("auto_play", false)*/;
            Log.i(Constants.START_TAG, "onCreate------>start autoPlay = " + autoPlay);
            if (autoPlay) {
                if (!PlayerManager.getInstance().isPlaying()) {
                    PlayerManagerHelper.getInstance().switchPlayerStatus(false);
                }
            }
        }
    }

    private void initAudioFocus() {
        //KRadioApplicationRequestAudioFocusImpl 定制实现类存在则代码在权限申请前就请求音频焦点了（见com.kaolafm.auto.home.SplashActivity.onCreate），
        // 不存在，再使用原有音频焦点申请逻辑。
        KRadioApplicationRequestAudioFocusInter kRadioApplicationRequestAudioFocusInter = ClazzImplUtil.getInter("KRadioApplicationRequestAudioFocusImpl");
        KRadioRequestAudioFocusInter kRadioRequestAudioFocusInter = null;
        if (kRadioApplicationRequestAudioFocusInter == null) {
            kRadioRequestAudioFocusInter = ClazzImplUtil.getInter("KRadioRequestAudioFocusImpl");
            if (kRadioRequestAudioFocusInter != null) {
                kRadioRequestAudioFocusInter.requestAudioFocusBySelf();
            }
        }
        Log.i(Constants.START_TAG, "onCreate------>kRadioRequestAudioFocusInter = " + kRadioRequestAudioFocusInter);
    }

    private void initTheme() {
        if (setTheme()) {
            setTheme(R.style.AppThemeCompat_spempty);
        }
    }

    private void initFullScreen() {
        final KRadioFullScreenInter kRadioFullScreenInter = ClazzImplUtil.getInter("KRadioFullScreenImpl");
        if (kRadioFullScreenInter != null) {
            kRadioFullScreenInter.initFullScreen(this);
        }
    }

    public void exposeSprendAd() {
        int id = GetAdDataMap.getADSpaceBySceneID(KradioAdSceneConstants.SPREND_SCENE);

        String acceptedAdTypes = ADUtils.getADTypeStr(KradioAdSceneConstants.AD_TYPE_AUDIO,
                KradioAdSceneConstants.AD_TYPE_IMAGE,
                KradioAdSceneConstants.AD_TYPE_AUDIO_IMAGE);

        AdvertisingManager.getInstance().exposePreloading(
                String.valueOf(id),
                KradioAdSceneConstants.SUB_TYPE_SPRAND_SCREEN,
                String.valueOf(ScreenUtil.getScreenWidth()),
                String.valueOf(ScreenUtil.getScreenHeight()),
                acceptedAdTypes,
                ""
        );
    }

    private void initGetAdData() {
        Log.e("SplashActivity", "initGetAdData: ");

        mGetAdDataMap.registerCallBack(new ADMapCallback() {
            @Override
            public void updateADMapData() {
                updateState(STATE_GOTO_NEXTPAGE);
                mGetAdDataMap.unregisterCallBack(this);
            }

            @Override
            public void handleFail() {
                updateState(STATE_GOTO_NEXTPAGE);
                mGetAdDataMap.unregisterCallBack(this);
            }
        });
        mGetAdDataMap.getAdData();
    }

    private void exposeAd() {
        Log.d(Constants.START_TAG, "exposeAd");
        if (mPermissionUtils.isShowDialog()) {
            return;
        }
        mAdvertisingImager = new AdvertisingImagerImpl();
        mAdvertisingImager.createSplashAdView(this);
        mAdvertisingImager.registAdListener(adListener);

        AdvertisingManager.getInstance().setImager(mAdvertisingImager);
        AdvertisingManager.getInstance().setReporter(new AdReportImpl());
        SplashAdHelper.setTargetActivity(MainActivity.class);

        KradioAdAudioManager.getInstance().init();
        int timedAdvertId = GetAdDataMap.getADSpaceBySceneID(KradioAdSceneConstants.TIMER_SCENE);
        if (timedAdvertId > 0) {
            TimedAdvertManager.getInstance().start(timedAdvertId, String.valueOf(ScreenUtil.getScreenWidth()), String.valueOf(ScreenUtil.getScreenHeight()));
        }
        AdConflict.init();
        exposeSprendAd();
    }

    private AdvertisingImagerImpl.AdListener adListener = new AdvertisingImagerImpl.AdListener() {
        @Override
        public void onAdError() {
            Log.d(Constants.START_TAG, "adListener onAdError");
            startToLauncher();
            finish();
        }
    };

    // AOP 会侵入修改这个方法，在启辰和日产上会切换主题
    public boolean setTheme() {
        return false;
    }

    private void requestPermission() {
        boolean flag = mPermissionUtils.isGrant();
        Log.i(Constants.START_TAG, "requestPermission start flag = " + flag);

        if (flag) {
            appEntryCheck();
            updateState(STATE_NORMAL_INIT);
        } else {
            ActivityCompat.requestPermissions(this, mPermissionUtils.getPerms(), PERMISSION_REQUEST_CODE);
        }
    }

//    @RequiresApi(api = Build.VERSION_CODES.Q)
//    void clearTasks() {
////        ActivityManager activityManager = getSystemService(ActivityManager.class);
//
//        ActivityManager activityManager = (ActivityManager) getSystemService(ACTIVITY_SERVICE);
//        ActivityManager manager = (ActivityManager) getSystemService(ACTIVITY_SERVICE);
//        List<ActivityManager.RunningTaskInfo> runningTaskInfos = manager.getRunningTasks(1);
//        if (runningTaskInfos != null) {
//            try {
//                Method forceStopPackage = manager.getClass().getDeclaredMethod("forceStopPackage", String.class);
//                forceStopPackage.setAccessible(true);
//                forceStopPackage.invoke(manager, (runningTaskInfos.get(0).topActivity).getPackageName());
//            } catch (Exception e) {
//
//            }
//        }
//    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (grantResults == null || grantResults.length == 0) {
//            requestPermission();
//            return;
            if (isExecute) {
                return;
            }
            isExecute = true;
//            mPermissionUtils.getDialog();
//            Toast.makeText(this.getApplicationContext(), getString(R.string.permissions_app_tip), Toast.LENGTH_SHORT).show();
            TimerTask task = new TimerTask() {
                @Override
                public void run() {
                    //销毁任务栈 解决权限关闭，启动应用拒绝权限，还被调起的问题
                    ActivityManager activityManager = (ActivityManager) SplashActivity.this.getApplicationContext().getSystemService(Context.ACTIVITY_SERVICE);
                    List<ActivityManager.AppTask> appTaskList = activityManager.getAppTasks();
                    for (ActivityManager.AppTask appTask : appTaskList) {
                        appTask.finishAndRemoveTask();
                    }
                }
            };
            Timer timer = new Timer();
            timer.schedule(task, 500);//0.5秒后执行TimeTask的run方法
        } else {
            int deniedCount = 0;
            for (int i : grantResults) {
                if (i == PackageManager.PERMISSION_DENIED) {
                    deniedCount++;
                }
            }
            if (deniedCount == 0) {
                if (mState != STATE_NO_INIT) {
                    updateState(STATE_NORMAL_INIT);
                }
            } else {
                boolean showSettingDialog = false;
                for (int i = 0; i < permissions.length; i++) {
                    if (grantResults[i] == PackageManager.PERMISSION_DENIED &&
                            !ActivityCompat.shouldShowRequestPermissionRationale(this, permissions[i])) {
                        showSettingDialog = true;
                    }
                }
                if (showSettingDialog) {
                    mPermissionUtils.getDialog();
                } else {
                    //拒绝权限推出应用前，判断一下播放状态，播放中就暂停。
                    boolean isPlaying = PlayerManager.getInstance().isPlaying();
                    Log.i(Constants.START_TAG, "Permission denied isPlaying = " + isPlaying);
                    if (isPlaying) {
                        PlayerManagerHelper.getInstance().switchPlayerStatus(false);
                    }
                    //释放音频焦点
                    PlayerManager.getInstance().abandonAudioFocus();
                    isAbandonAudioFocusSelf = true;
                    Log.i(Constants.START_TAG, "Permission denied abandonAudioFocus");
                    //销毁任务栈 解决权限关闭，启动应用拒绝权限，还被调起的问题
                    ActivityManager activityManager = (ActivityManager) this.getApplicationContext().getSystemService(Context.ACTIVITY_SERVICE);
                    List<ActivityManager.AppTask> appTaskList = activityManager.getAppTasks();
                    for (ActivityManager.AppTask appTask : appTaskList) {
                        appTask.finishAndRemoveTask();
                    }
                }
            }
        }
    }

    private void initSDK() {
        //这里如果sdk首次启动，等待初始化完成再调用广告接口，否则直接调用
        mUsableObserver = this::initGetAdData;
        KradioSDKManager.getInstance().addUsableObserver(mUsableObserver);

        // 注册登录接口
        AccountInterworkInter mAccountInterworkInter = ClazzImplUtil.getInter("AccountInterworkImpl");
        if (mAccountInterworkInter != null && mAccountInterworkInter.isOpenThirdPartyAccount()) {
            mAccountInterworkInter.bindAccount();
        }

        //2秒接口没有返回直接当作失败处理
        mMyHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                Message msg = new Message();
                msg.what = 1;
                mMyHandler.sendMessage(msg);
            }
        }, 2 * 1000);
    }

    private void initAndActivateSDK() {
        KradioSDKManager.getInstance().initAndActivate();
    }

    private void goLauncher() {
//      这块是为了确保在第一次启动的情况下，同意权限，haspermission 和 onPermissionsGranted 会走两遍，避免重复启动。
        Log.i(Constants.START_TAG, "goLauncher start");
        //fixed 初次初始化时还没有定位权限,所以会导致定位失败，需要在获取权限后重新定位
        initLocation();
        if (isGoLauncher) {
            isGoLauncher = false;
        } else {
            return;
        }
        initSDK();
        //      默认不开启
        if (PerformanceSettingMananger.getInstance().getHomeIsStartSplash()) {
            delayTime = 2000;
        }
    }

    protected void startToLauncher() {
        Intent intent = getIntent();
        if (intent == null) {
            intent = new Intent();
        }
        intent.setClass(SplashActivity.this, MainActivity.class);
//        intent.setFlags(intent.FLAG_ACTIVITY_CLEAR_TASK | intent.FLAG_ACTIVITY_NEW_TASK);// 每次打开LauncherActivity时都清除栈
        KRadioOperateClickCellInter settingModelInter = ClazzImplUtil.getInter("KRadioOperateClickImpl");
        if (settingModelInter != null && settingModelInter.isOperate(intent)) {
            settingModelInter.setIntent(intent);
        }
        intent.putExtra(CLIENT_EXTRA_TYPE, getIntent().getIntExtra(CLIENT_EXTRA_TYPE, INVALID_NUM));
        intent.putExtra(START_PAGE, intent.getIntExtra(START_PAGE, INVALID_NUM));
        Log.i(Constants.START_TAG, "startToLauncher------------>START_PAGE：" + intent.getIntExtra(START_PAGE, INVALID_NUM));
        startActivity(intent);
    }

    // 此函数实现解决https://app.huoban.com/tables/2100000007544849/items/2300001234591322?userId=1545533需求
    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (showNotice) {
            showNotice();
        }
    }

    private void goNextPage() {
        // 此处与onResume配合，保证SplashActivity在前台时才跳转，在后台时不跳转；直到其回到前台时再跳转
        Log.d(Constants.START_TAG, "goNextPage SplashActivityInBackground : " + SplashActivityInBackground);
        canGoNextPage = true;
        if (!SplashActivityInBackground) {
            exposeAd();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        SplashActivityInBackground = true;
        Log.i(Constants.START_TAG, "onPause start");
    }

    @Override
    protected void onStart() {
        super.onStart();
        Log.i(Constants.START_TAG, "onStart start");
    }

    @Override
    protected void onStop() {
        super.onStop();
        KRadioHubOnStop onStopImpl = ClazzImplUtil.getInter("KRadioHubOnStopImpl");
        if (onStopImpl != null) {
            onStopImpl.onStop();
        } else {
            boolean isGrant = mPermissionUtils.isGrant();
            Log.i(Constants.START_TAG, "onStop start isGrant = " + isGrant);
            if (!isGrant) {
                KRadioPermissionInter kRadioPermissionInter = ClazzImplUtil.getInter("KRadioPermissionImpl");
                if (kRadioPermissionInter != null && kRadioPermissionInter.onPermissionDenied()) {
                    return;
                }
                AppManager.getInstance().appExit();
            }
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        Log.i(Constants.START_TAG, "onNewIntent ");
        KRadioOperateClickCellInter settingModelInter = ClazzImplUtil.getInter("KRadioOperateClickImpl");
        if (settingModelInter != null && settingModelInter.startToLauncher() && settingModelInter.isOperate(intent)) {
            startToLauncher();
        }

    }

    @Override
    public void onResume() {
        super.onResume();
        Log.i(Constants.START_TAG, "onResume start , canGoNextPage : " + canGoNextPage);
        SplashActivityInBackground = false;
        Log.i(Constants.START_TAG, "onResume start");
        if (showNotice) {
            return;
        }
        if (canGoNextPage) {
            exposeAd();
        }
        if (!isFirstOnResume && !isAbandonAudioFocusSelf) {
            initAudioFocus();
        }
        isFirstOnResume = false;
    }

    @Override
    public void finish() {
        super.finish();
        Log.i(Constants.START_TAG, "SplashActivity finish");
    }

    @Override
    protected void onRestart() {
        super.onRestart();
        Log.i(Constants.START_TAG, "onRestart start");
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && !SplashActivityInBackground) {
//            AppManager.getInstance().appExit();
            if (group != null && !group.isShown() && ivBack != null) {
                ivBack.callOnClick();
            }
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        BaseApplication.getInstance().removeActivity(this);
//        LeaksManager.getInstance().leak(SplashActivity.this);
        if (mAdvertisingImager != null && adListener != null) {
            mAdvertisingImager.unregirstAdListener(adListener);
        }
        KradioSDKManager.getInstance().removeUsableObserver(mUsableObserver);

        //大众账号需求
        KRadioCustomizedStartupLogicInter kRadioCustomizedStartupLogicInter = ClazzImplUtil.getInter("KRadioCustomizedStartupLogicInterImpl");
        if (null != kRadioCustomizedStartupLogicInter) {
            kRadioCustomizedStartupLogicInter.release();
        }
    }

    //防止内存泄漏
    static class MyHandler extends Handler {
        private WeakReference<Activity> mWeakReference;

        public MyHandler(Activity act) {
            mWeakReference = new WeakReference<>(act);
        }

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            SplashActivity mainActivity = (SplashActivity) mWeakReference.get();
            switch (msg.what) {
                case 1:
                    if (mainActivity != null) {
                        handleADGetFail(mainActivity);
                    }
                    break;
                default:
                    break;
            }
            mWeakReference.clear();
        }
    }

    private static void handleADGetFail(SplashActivity mainActivity) {
        Log.i(Constants.START_TAG, "2秒接口没有返回直接当作失败处理,mGetAdDataMap.mState = " + mainActivity.mGetAdDataMap.mState);
        if (mainActivity.mGetAdDataMap.mState != GetAdDataMap.STATE_ERROR && mainActivity.mGetAdDataMap.mState != GetAdDataMap.STATE_SUCCESS) {
            Log.i(Constants.START_TAG, "2秒接口没有返回直接当作失败处理");
            mainActivity.mGetAdDataMap.mState = GetAdDataMap.STATE_NONE;

            mainActivity.updateState(mainActivity.STATE_GOTO_NEXTPAGE);
        }
    }

    public String getPageId() {
        return Constants.ONLINE_PAGE_ID_USE_TIPS;
    }

}
