package com.kaolafm.kradio.flavor.impl;

import android.app.Dialog;
import android.view.View;
import android.view.Window;

import com.kaolafm.kradio.lib.base.flavor.KRadioDialogAttrInter;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-03-02 17:40
 ******************************************/
public class KRadioDialogAttrImpl implements KRadioDialogAttrInter {
    @Override
    public boolean beforeDialogShow(Object... args) {
        return false;
    }

    @Override
    public boolean afterDialogShow(Object... args) {
        Dialog dialog = (Dialog) args[0];
        if (dialog == null) {
            return false;
        }
        Window window = dialog.getWindow();
        if (window == null) {
            return false;
        }
        window.getDecorView().setSystemUiVisibility(
                View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                        | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                        | View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
        return true;
    }
}