package com.kaolafm.kradio.component.ui.k_1x1card;

import androidx.annotation.NonNull;
import android.text.Html;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.kradio.component.ui.R;
import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.component.ui.base.utils.ComponentUtils;
import com.kaolafm.kradio.component.ui.base.utils.VipCornerUtil;
import com.kaolafm.kradio.component.ui.base.view.OvalImageView;
import com.kaolafm.kradio.component.ui.base.view.RateView;
import com.kaolafm.kradio.component.ui.base.CellBinder;
import com.kaolafm.kradio.component.ui.base.ItemClickSupport;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.UrlUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.operation.model.column.ActivityDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.AlbumDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.PageRedirectionColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.TopicDetailColumnMember;

/**
 * 首页组件1+1
 */
public class ComponentBrandPage1And1Cell extends HomeCell implements CellBinder<View, ComponentBrandPage1And1Cell>, ItemClickSupport {

    View layout_1_1_top_lift;
    View layout_1_1_top_right;

    private OvalImageView card_bg_iv_lift, card_pic_iv_lift;
    private TextView card_title_tv_lift;
    private TextView card_tag_tv_lift;
    private TextView card_des_tv_lift;
    private View des_hide_view_lift;
    private ImageView card_play_iv_lift;
    private ImageView vip_icon_lift;
    private RateView card_layout_playing_lift;

    private OvalImageView card_bg_iv_right, card_pic_iv_right;
    private TextView card_title_tv_right;
    private TextView card_tag_tv_right;
    private View des_hide_view_right;
    private ImageView card_play_iv_right;
    private ImageView vip_icon_right;
    private TextView card_des_tv_right;
    private RateView card_layout_playing_right;


    @Override
    public void mountView(@NonNull ComponentBrandPage1And1Cell data, @NonNull View view, int position) {
        layout_1_1_top_lift=view.findViewById(R.id.layout_1_1_top_lift);
        layout_1_1_top_right=view.findViewById(R.id.layout_1_1_top_right);

        initView();
        setTopViewDate(data);
        setBottomViewDate(data);
    }

    @Override
    public int getItemType() {
        return R.layout.component_brand_page_1_1_layout;
    }

    private void initView() {
        //上边区域
        card_bg_iv_lift = layout_1_1_top_lift.findViewById(R.id.card_bg_iv);
        card_pic_iv_lift = layout_1_1_top_lift.findViewById(R.id.card_pic_iv);
        card_title_tv_lift = layout_1_1_top_lift.findViewById(R.id.card_title_tv);
        card_play_iv_lift = layout_1_1_top_lift.findViewById(R.id.card_play_iv);
        vip_icon_lift = layout_1_1_top_lift.findViewById(R.id.vip_icon);
        card_layout_playing_lift = layout_1_1_top_lift.findViewById(R.id.card_layout_playing);
        card_tag_tv_lift = layout_1_1_top_lift.findViewById(R.id.card_tag_tv);
        card_des_tv_lift = layout_1_1_top_lift.findViewById(R.id.card_des_tv);
        des_hide_view_lift = layout_1_1_top_lift.findViewById(R.id.des_hide_view);
        //下边区域
        card_bg_iv_right = layout_1_1_top_right.findViewById(R.id.card_bg_iv);
        card_pic_iv_right = layout_1_1_top_right.findViewById(R.id.card_pic_iv);
        card_title_tv_right = layout_1_1_top_right.findViewById(R.id.card_title_tv);
        card_play_iv_right = layout_1_1_top_right.findViewById(R.id.card_play_iv);
        vip_icon_right = layout_1_1_top_right.findViewById(R.id.vip_icon);
        card_layout_playing_right = layout_1_1_top_right.findViewById(R.id.card_layout_playing);
        card_tag_tv_right = layout_1_1_top_right.findViewById(R.id.card_tag_tv);
        card_des_tv_right = layout_1_1_top_right.findViewById(R.id.card_des_tv);
        des_hide_view_right = layout_1_1_top_right.findViewById(R.id.des_hide_view);


        layout_1_1_top_lift.setBackground(ResUtil.getDrawable(R.drawable.component_card_bg_7));
        layout_1_1_top_right.setBackground(ResUtil.getDrawable(R.drawable.component_card_bg_7));

//        ViewGroup.LayoutParams para;
//        para = card_title_tv_lift.getLayoutParams();
//        para.width = ResUtil.getDimen(R.dimen.m234);;
//        card_title_tv_lift.setLayoutParams(para);

//        para = card_title_tv_right.getLayoutParams();
//        para.width = ResUtil.getDimen(R.dimen.m234);;
//        card_title_tv_lift.setLayoutParams(para);


//        card_title_tv_lift.setMaxLines(3);
//        card_title_tv_right.setMaxLines(3);
//        card_title_tv_lift.setEllipsize(TextUtils.TruncateAt.END);
//        card_title_tv_right.setEllipsize(TextUtils.TruncateAt.END);
    }

    /**
     * 设置上方区域数据
     *
     * @param data
     */
    private void setTopViewDate(ComponentBrandPage1And1Cell data) {
        if (data.contentList != null && data.contentList.size() > 0) {
            card_title_tv_lift.setText(data.contentList.get(0).getTitle() + "");
            if (data.getContentList().get(0) instanceof TopicDetailColumnMember) {
                des_hide_view_lift.setVisibility(View.GONE);
                String numText = ComponentUtils.getInstance().formatNumber(data.getContentList().get(0).getUserCount(), 1)
                        + "<font size=" + ResUtil.getDimen(R.dimen.m18) + ">人参与</font> " +
                        ComponentUtils.getInstance().formatNumber(data.getContentList().get(0).getReadCount(), 1) +
                        "<font size=" + ResUtil.getDimen(R.dimen.m18) + ">阅读量</font>";
                card_des_tv_lift.setText(Html.fromHtml(numText));
            } else {
                des_hide_view_lift.setVisibility(View.VISIBLE);
                card_des_tv_lift.setText(data.contentList.get(0).getSubtitle() + "");
            }

            if (!TextUtils.isEmpty(UrlUtil.getCardBgUrl(data.contentList.get(0).getImageFiles())))
                ImageLoader.getInstance().displayImage(card_pic_iv_lift.getContext(),
                        UrlUtil.getCardBgUrl(data.contentList.get(0).getImageFiles()), card_bg_iv_lift);
            if (!TextUtils.isEmpty(data.contentList.get(0).getTag())) {
                card_tag_tv_lift.setText(data.contentList.get(0).getTag());
                if (!TextUtils.isEmpty(data.contentList.get(0).getTagColor())) {
                    ComponentUtils.getInstance().setTagStyle(card_tag_tv_lift, data.contentList.get(0).getTagColor());
                }
                card_tag_tv_lift.setVisibility(View.VISIBLE);
            } else {
                card_tag_tv_lift.setVisibility(View.INVISIBLE);
            }
//            if (data.getContentList().get(0) instanceof PageRedirectionColumnMember) {
//                //页面跳转类型
//                card_pic_iv_lift.setVisibility(View.GONE);
//                card_play_iv_lift.setVisibility(View.GONE);
//                card_play_iv_lift.setVisibility(View.GONE);
//            } else {
            card_pic_iv_lift.setVisibility(View.VISIBLE);
            card_play_iv_lift.setVisibility(View.VISIBLE);
            if (!TextUtils.isEmpty(UrlUtil.getCardPicUrl(data.contentList.get(0).getImageFiles()))) {
                ViewGroup.LayoutParams para;
                para = card_pic_iv_lift.getLayoutParams();
                para.height = ResUtil.getDimen(R.dimen.m160);
                para.width = ResUtil.getDimen(R.dimen.m160);
                ;
                card_pic_iv_lift.setLayoutParams(para);
                card_pic_iv_lift.setVisibility(View.VISIBLE);

                vip_icon_lift.setVisibility(View.VISIBLE);
                ImageLoader.getInstance().displayImage(card_pic_iv_lift.getContext(),
                        UrlUtil.getCardPicUrl(data.contentList.get(0).getImageFiles()), card_pic_iv_lift);
                if (data.contentList.get(0).getResType() == ResType.TYPE_BROADCAST
                        || data.contentList.get(0).getResType() == ResType.TYPE_TV) {
                    vip_icon_lift.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_icon_live_class));
                } else if (data.getContentList().get(0).getResType() == ResType.TYPE_LIVE) {
                    if (data.getContentList().get(0).getLiveStatus() == 0 || data.getContentList().get(0).getLiveStatus() == 2) {
                        vip_icon_lift.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_live_icon_reday));
                    } else if (data.getContentList().get(0).getLiveStatus() == 1) {
                        vip_icon_lift.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_live_icon_playing));
                    }
                } else if (data.getContentList().get(0).getResType() == ResType.TYPE_VIDEO_AUDIO ||
                        data.getContentList().get(0).getResType() == ResType.TYPE_VIDEO_ALBUM) {
                    vip_icon_lift.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_icon_video));
                } else {
//                        VipCornerUtil.setVipCorner(vip_icon_lift, data.contentList.get(0).getVip(), data.contentList.get(0).getFine(), true);
                    if (data.contentList.get(0) instanceof AlbumDetailColumnMember) {
                        AlbumDetailColumnMember albumDetailColumnMember = (AlbumDetailColumnMember) data.contentList.get(0);
                        VipCornerUtil.setVipCorner(vip_icon_lift, albumDetailColumnMember.getVip(), albumDetailColumnMember.getFine(), false);
                    }
                }
            } else {
                card_pic_iv_lift.setVisibility(View.GONE);
                vip_icon_lift.setVisibility(View.GONE);
            }
            if (data.contentList.get(0).getCanPlay() == 1) {
                card_play_iv_lift.setVisibility(View.VISIBLE);
            } else {
                card_play_iv_lift.setVisibility(View.GONE);
            }
//            }
            layout_1_1_top_lift.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onViewClickListener != null) {
                        v.setTag(0);
                        onViewClickListener.onViewClick(v, getPositionInParent());
                    }
                }
            });

            boolean notShowPlaying = data.getContentList().get(0) instanceof PageRedirectionColumnMember
                    || data.getContentList().get(0) instanceof ActivityDetailColumnMember;
            if (!notShowPlaying) {
                boolean b = data.selected
                        && ComponentUtils.getInstance().getCardHomePlayIdIsPlaying(data.contentList.get(0).getId(), data.contentList.get(0).getCanPlay());
                card_layout_playing_lift.setVisibility(b ?
                        View.VISIBLE : View.GONE);
                if (b) {
                    card_play_iv_lift.setVisibility(View.GONE);
                } else {
                    if (data.contentList.get(0).getCanPlay() == 1) {
                        card_play_iv_lift.setVisibility(View.VISIBLE);
                    } else {
                        card_play_iv_lift.setVisibility(View.GONE);
                    }
                }
            } else {
                card_layout_playing_lift.setVisibility(View.GONE);
                card_play_iv_lift.setVisibility(View.GONE);
            }

        }


    }

    /**
     * 设置下方区域数据
     *
     * @param data
     */
    private void setBottomViewDate(ComponentBrandPage1And1Cell data) {
        if (data.contentList != null && data.contentList.size() > 1) {
            card_title_tv_right.setText(data.contentList.get(1).getTitle() + "");
            if (data.getContentList().get(1) instanceof TopicDetailColumnMember) {
                String numText = ComponentUtils.getInstance().formatNumber(data.getContentList().get(1).getUserCount(), 1)
                        + "<font size=" + ResUtil.getDimen(R.dimen.m18) + ">人参与</font> " +
                        ComponentUtils.getInstance().formatNumber(data.getContentList().get(1).getReadCount(), 1) +
                        "<font size=" + ResUtil.getDimen(R.dimen.m18) + ">阅读量</font>";
                card_des_tv_right.setText(Html.fromHtml(numText));
            } else {
                card_des_tv_right.setText(data.contentList.get(1).getSubtitle() + "");
            }
            if (!TextUtils.isEmpty(UrlUtil.getCardBgUrl(data.contentList.get(1).getImageFiles())))
                ImageLoader.getInstance().displayImage(card_pic_iv_right.getContext(),
                        UrlUtil.getCardBgUrl(data.contentList.get(1).getImageFiles()), card_bg_iv_right);
            if (!TextUtils.isEmpty(data.contentList.get(1).getTag())) {
                card_tag_tv_right.setText(data.contentList.get(1).getTag());
                if (!TextUtils.isEmpty(data.contentList.get(1).getTagColor())) {
                    ComponentUtils.getInstance().setTagStyle(card_tag_tv_right, data.contentList.get(1).getTagColor());
                }
                card_tag_tv_right.setVisibility(View.VISIBLE);
            } else {
                card_tag_tv_right.setVisibility(View.INVISIBLE);
            }
            if (data.getContentList().get(1) instanceof PageRedirectionColumnMember) {
                card_pic_iv_right.setVisibility(View.GONE);
                card_play_iv_right.setVisibility(View.GONE);
                card_play_iv_right.setVisibility(View.GONE);
            } else {
                ViewGroup.LayoutParams para;
                para = card_pic_iv_right.getLayoutParams();
                para.height = ResUtil.getDimen(R.dimen.m160);
                para.width = ResUtil.getDimen(R.dimen.m160);
                ;
                card_pic_iv_right.setLayoutParams(para);
                card_pic_iv_right.setVisibility(View.VISIBLE);

                card_play_iv_right.setVisibility(View.VISIBLE);
                if (!TextUtils.isEmpty(UrlUtil.getCardPicUrl(data.contentList.get(1).getImageFiles()))) {
                    card_pic_iv_right.setVisibility(View.VISIBLE);
                    vip_icon_right.setVisibility(View.VISIBLE);
                    ImageLoader.getInstance().displayImage(card_pic_iv_right.getContext(),
                            UrlUtil.getCardPicUrl(data.contentList.get(1).getImageFiles()), card_pic_iv_right);
                    if (data.contentList.get(1).getResType() == ResType.TYPE_BROADCAST
                            || data.contentList.get(1).getResType() == ResType.TYPE_TV) {
                        vip_icon_right.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_icon_live_class));
                    } else if (data.getContentList().get(1).getResType() == ResType.TYPE_LIVE) {
                        if (data.getContentList().get(1).getLiveStatus() == 0 || data.getContentList().get(1).getLiveStatus() == 2) {
                            vip_icon_right.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_live_icon_reday));
                        } else if (data.getContentList().get(1).getLiveStatus() == 1) {
                            vip_icon_right.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_live_icon_playing));
                        }
                    } else if (data.getContentList().get(1).getResType() == ResType.TYPE_VIDEO_AUDIO ||
                            data.getContentList().get(1).getResType() == ResType.TYPE_VIDEO_ALBUM) {
                        vip_icon_right.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_icon_video));
                    } else {
//                        VipCornerUtil.setVipCorner(vip_icon_right, data.contentList.get(1).getVip(), data.contentList.get(1).getFine(), true);
                        if (data.contentList.get(1) instanceof AlbumDetailColumnMember) {
                            AlbumDetailColumnMember albumDetailColumnMember = (AlbumDetailColumnMember) data.contentList.get(1);
                            VipCornerUtil.setVipCorner(vip_icon_right, albumDetailColumnMember.getVip(), albumDetailColumnMember.getFine(), false);
                        }
                    }
                } else {
                    card_pic_iv_right.setVisibility(View.GONE);
                    vip_icon_right.setVisibility(View.GONE);
                }
                if (data.contentList.get(1).getCanPlay() == 1) {
                    card_play_iv_right.setVisibility(View.VISIBLE);
                } else {
                    card_play_iv_right.setVisibility(View.GONE);
                }
            }
            layout_1_1_top_right.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onViewClickListener != null) {
                        v.setTag(1);
                        onViewClickListener.onViewClick(v, getPositionInParent());
                    }
                }
            });

            boolean notShowPlaying = data.getContentList().get(1) instanceof PageRedirectionColumnMember
                    || data.getContentList().get(1) instanceof ActivityDetailColumnMember;
            if (!notShowPlaying) {
                boolean b = data.selected
                        && ComponentUtils.getInstance().getCardHomePlayIdIsPlaying(data.contentList.get(1).getId(), data.contentList.get(1).getCanPlay());
                card_layout_playing_right.setVisibility(b ?
                        View.VISIBLE : View.GONE);
                if (b) {
                    card_play_iv_right.setVisibility(View.GONE);
                } else {
                    if (data.contentList.get(1).getCanPlay() == 1) {
                        card_play_iv_right.setVisibility(View.VISIBLE);
                    } else {
                        card_play_iv_right.setVisibility(View.GONE);
                    }
                }
            } else {
                card_layout_playing_right.setVisibility(View.GONE);
                card_play_iv_right.setVisibility(View.GONE);
            }

        }

    }

    private BaseHolder.OnViewClickListener onViewClickListener;

    @Override
    public void setOnItemClickListener(BaseHolder.OnViewClickListener listener) {
        onViewClickListener = listener;
    }
}
