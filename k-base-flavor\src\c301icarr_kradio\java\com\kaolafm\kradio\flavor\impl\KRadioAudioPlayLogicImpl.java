package com.kaolafm.kradio.flavor.impl;

import android.annotation.SuppressLint;
import android.util.Log;


import com.kaolafm.kradio.lib.base.flavor.KRadioAudioPlayLogicInter;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.player.logic.PlayerManager;


/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-08-28 16:17
 ******************************************/
public final class KRadioAudioPlayLogicImpl implements KRadioAudioPlayLogicInter {
    private static final String TAG = "KRadioAudioPlayLogicImpl";

    @SuppressLint("LongLogTag")
    @Override
    public boolean autoPlayAudio(Object... args) {
        return false;
    }

    @Override
    public boolean requestAudioFocus(Object... args) {
        return PlayerManager.getInstance().requestAudioFocus();
    }

    @Override
    public boolean resumeAudioPlayLogic(Object... args) {
        recoverPlay();
        return true;
    }

    @Override
    public boolean restartAudioPlayLogic(Object... args) {
        return false;
    }

    @SuppressLint("LongLogTag")
    private void recoverPlay() {
        PlayerManager playerManager = PlayerManager.getInstance();

        Log.i(TAG, "recoverPlay---------->PlayerManager.isPausedFromUser() = " + playerManager.isPauseFromUser()
                + "          PlayerManager.getCurrentAudioFocusStatus() = " + playerManager.getCurrentAudioFocusStatus());
        if (playerManager.isPauseFromUser()) {
            if (playerManager.getCurrentAudioFocusStatus() < 0) {
                requestAudioFocus();
            }
            return;
        }
        if (playerManager.getCurrentAudioFocusStatus() < 0) {
            requestAudioFocus();
        }

        Log.i(TAG, "is playing?=" + playerManager.isPlaying());
        playerManager.play(false);
    }

    @Override
    public boolean doStartInPlay(Object... args) {
        return false;
    }
}
