/*
 * Copyright 2017 Jess<PERSON>an
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.kaolafm.kradio.lib.utils.imageloader;

import android.content.Context;
import androidx.fragment.app.Fragment;

/**
 * 图片加载策略,实现 {@link BaseImageLoaderStrategy}<br/>
 * 默认是使用的glide。<br/>
 * 如果想要更换图片加载库，需要实现该接口，并通过{@link ImageLoader#setLoadImageStrategy(BaseImageLoaderStrategy)}添加，才能生效。
 */
public interface BaseImageLoaderStrategy<T extends ImageConfig> extends BaseInterface {

    /**
     * 加载图片
     */
    void loadImage(Context ctx, T config);

    /**
     * 加载图片
     */
    void loadImage(Fragment fragment, T config);

    /**
     * 停止加载并清除所有缓存
     */
    void clear(Context ctx, T config);

    void destroy(Context context);
}
