package com.kaolafm.kradio.home.comprehensive;

import com.kaolafm.kradio.user.LoginManager;
import com.kaolafm.opensdk.log.Logging;

public class LoginListenerProxy implements LoginManager.LoginListener {
    public static final String TAG = "login.proxy";
    private LoginManager.LoginListener listener;

    private volatile static LoginListenerProxy mInstance;

    private LoginListenerProxy() {
    }

    public static LoginListenerProxy getInstance() {
        if (mInstance == null) {
            synchronized (LoginListenerProxy.class) {
                if (mInstance == null) {
                    mInstance = new LoginListenerProxy();
                }
            }
        }
        return mInstance;
    }

    public void setListener(LoginManager.LoginListener listener) {
        this.listener = listener;
    }

    @Override
    public void onLoginStateChange(int cp, boolean isLogin) {
        Logging.i(TAG, "onLoginStateChange: cp=" + cp + ",isLogin=" + isLogin);
        LoginManager.getInstance().unregisterLoginListener(this);
        if (listener != null) {
            listener.onLoginStateChange(cp, isLogin);
        }
    }

    @Override
    public void onCancel() {
        Logging.i(TAG, "onCancel: ");
        listener = null;
        LoginManager.getInstance().unregisterLoginListener(this);
    }
}