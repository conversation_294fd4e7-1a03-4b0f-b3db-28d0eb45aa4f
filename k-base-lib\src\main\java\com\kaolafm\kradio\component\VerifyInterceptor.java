package com.kaolafm.kradio.component;

import android.text.TextUtils;
import com.kaolafm.kradio.component.ComponentResult.Code;

/**
 * <AUTHOR>
 * @date 2019-07-01
 */
class VerifyInterceptor implements ComponentInterceptor {

    private static class VerifyInterceptorHolder {
        private final static VerifyInterceptor INSTANCE = new VerifyInterceptor();
    }

    public static VerifyInterceptor getInstance() {
        return VerifyInterceptorHolder.INSTANCE;
    }

    private VerifyInterceptor() {
    }

    @Override
    public ComponentResult intercept(ComponentChain chain) {
        ComponentClient client = chain.client();
        String componentName = client.componentName();
        int code = 0;
        if (TextUtils.isEmpty(componentName)) {
            code = Code.ERROR_COMPONENT_NAME_EMPTY;
        }else if (client.application() == null) {
            code = Code.ERROR_CONTEXT_NULL;
        }else {
            //当前进程没有该组件
            if (!ComponentManager.hasComponent(componentName)) {
                code = Code.ERROR_NO_COMPONENT_FOUND;
            }
        }
        if (code != Code.SUCCESS) {
            return ComponentResult.error(code);
        }
        if (ComponentManager.hasComponent(componentName)) {
            chain.addInterceptor(CallServiceInterceptor.getInstance());
        }

        chain.addInterceptor(WaitForResultInterceptor.getInstance());
        return chain.proceed();
    }
}
