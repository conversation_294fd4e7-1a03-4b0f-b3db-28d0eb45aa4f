package com.kaolafm.ads.image.base;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Point;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;

import com.kaolafm.kradio.lib.utils.AnimUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.Map;

public abstract class BaseDragAdContentView<T> extends BaseAdWithDurationContentView<T> {

    static final String TAG = "BaseDragAdContentView";

    private int parentWidth;

    private int parentHeight;

    private int lastX;

    private int lastY;

    private int downX;

    private int downY;

    private boolean isDrag;

    Map<Integer, Point> mInitPointMap = new HashMap<>();

    public BaseDragAdContentView(Context context) {
        super(context);
        setClickable(true);
    }

    public BaseDragAdContentView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        setClickable(true);
    }

    public BaseDragAdContentView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        setClickable(true);
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        int rawX = (int) event.getRawX();
        int rawY = (int) event.getRawY();
        switch (event.getAction() & MotionEvent.ACTION_MASK) {
            case MotionEvent.ACTION_DOWN:
                setPressed(true);
                isDrag = false;
                getParent().requestDisallowInterceptTouchEvent(true);
                lastX = rawX;
                lastY = rawY;
                downX = rawX;
                downY = rawY;
                ViewGroup parent;
                if (getParent() != null) {
                    parent = (ViewGroup) getParent();
                    parentHeight = parent.getHeight();
                    parentWidth = parent.getWidth();
                }
                break;
            case MotionEvent.ACTION_MOVE:
                int dx = rawX - lastX;
                int dy = rawY - lastY;
                if (parentHeight <= 0 || parentWidth == 0) {
                    isDrag = false;
                    break;
                } else {
                    isDrag = true;
                }
                int distance = (int) Math.sqrt(dx * dx + dy * dy);
                if (distance <= 10) {
                    isDrag = false;
                    break;
                }

                float x = getX() + dx;
                float y = getY() + dy;
                //检测是否到达边缘 左上右下
                x = x < 0 ? 0 : x > parentWidth - getWidth() ? parentWidth - getWidth() : x;
                y = getY() < 0 ? 0 : getY() + getHeight() > parentHeight ? parentHeight - getHeight() : y;
                setX(x);
                setY(y);
                lastX = rawX;
                lastY = rawY;

                break;
            case MotionEvent.ACTION_UP:
                int totalDx = rawX - downX;
                int totalDy = rawY - downY;

                int totalDistance = (int) Math.sqrt(totalDx * totalDx + totalDy * totalDy);
                if (totalDistance > 10) {
                    isDrag = true;
                }
                if (isDrag) {
                    setPressed(false);
                }
                break;
        }
        //如果是拖拽则消s耗事件，否则正常传递即可。
        return !isNotDrag() || super.onTouchEvent(event);
    }

    private boolean isNotDrag() {
        return !isDrag;
    }

    @Override
    public void show() {
        super.show();
        //先将view置于屏幕右侧外
        setX(-200);
        animate().setListener(new ShowAnimationListener(this));
        AnimUtil.startTranslateX(this, 0);
    }


    @Override
    public void hide() {
        hide(null);
    }

    public void hide(HideAnimationListener listener) {
        if (listener != null) {
            animate().setListener(listener);
        } else {
            super.hide();
        }
        AnimUtil.startTranslateX(this, -(getX() + getWidth()));
    }

    public void hideWithAnim() {
        hide(new HideAnimationListener(this));
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        animate().cancel();
        clearAnimation();
    }

    @Override
    protected void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);

        updateFloatView();
    }

    private void updateFloatView() {
        this.postDelayed(() -> {
            Point point = mInitPointMap.get(ResUtil.getOrientation());
            if (point != null) {
                setX(point.x);
                setY(point.y);
            }
        }, 100);
    }

    public class HideAnimationListener extends AnimatorListenerAdapter {

        private WeakReference<View> mViewRef;

        public HideAnimationListener(View view) {
            mViewRef = new WeakReference<>(view);
        }

        @Override
        public void onAnimationEnd(Animator animator) {
            BaseDragAdContentView.super.hide();
            clear(animator);
        }

        @Override
        public void onAnimationCancel(Animator animator) {
            clear(animator);
        }

        public void clear(Animator animator) {
            View view = mViewRef.get();
            if (view != null) {
                view.animate().setListener(null);
            }
            if (animator != null) {
                animator.removeAllListeners();
            }
        }
    }

    public static class ShowAnimationListener extends AnimatorListenerAdapter {
        private WeakReference<BaseDragAdContentView> mViewReference;

        public ShowAnimationListener(BaseDragAdContentView view) {
            mViewReference = new WeakReference<>(view);
        }

        @Override
        public void onAnimationEnd(Animator animation) {
            super.onAnimationEnd(animation);
            BaseDragAdContentView view = mViewReference.get();
            if (view != null) {
                view.saveCurrentCoordinate();
            }
        }
    }

    public void saveCurrentCoordinate() {
        //存储当前屏幕分辨率，旋转时候用来计算百分比
        if (mInitPointMap.size() == 0) {
            Point p1 = new Point();
            Point p2 = new Point();

            p1.x = (int) getX();
            p1.y = (int) getY();

            p2.x = ScreenUtil.getScreenHeight() - (ScreenUtil.getScreenWidth() - (int) getX());
            p2.y = ScreenUtil.getScreenWidth() - (ScreenUtil.getScreenHeight() - (int) getY());

            if (ResUtil.getOrientation() == Configuration.ORIENTATION_LANDSCAPE) {
                p2.y -= ScreenUtil.getNavigationBarHeight();
                mInitPointMap.put(Configuration.ORIENTATION_LANDSCAPE, p1);
                mInitPointMap.put(Configuration.ORIENTATION_PORTRAIT, p2);
            } else {
                p2.x -= ScreenUtil.getNavigationBarHeight();
                mInitPointMap.put(Configuration.ORIENTATION_LANDSCAPE, p2);
                mInitPointMap.put(Configuration.ORIENTATION_PORTRAIT, p1);
            }
        }
    }
}
