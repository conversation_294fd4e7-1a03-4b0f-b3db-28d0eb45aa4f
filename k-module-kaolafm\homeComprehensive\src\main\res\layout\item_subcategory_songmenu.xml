<?xml version="1.0" encoding="utf-8"?>
<com.kaolafm.kradio.common.widget.CScaleLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:sfl="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools"
    android:id="@+id/ll_songmenu_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <com.kaolafm.kradio.lib.widget.SquareFrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        sfl:canScale="false">

        <ImageView
            android:id="@+id/iv_subcategory_songmenu_cover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:contentDescription="@null"
            tool:src="@drawable/online_what_i_like" />

        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/shape_songmenu_item_bg"
            />
        <ViewStub
            android:id="@+id/vs_layout_playing"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout="@layout/layout_playing_square_item" />

        <TextView
            android:id="@+id/tv_subcategory_songmenu_listen_num"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="end|bottom"
            android:paddingBottom="@dimen/y10"
            android:paddingEnd="@dimen/x10"
            android:paddingTop="@dimen/y10"
            android:textColor="@color/text_color_1"
            android:gravity="end"
            android:textSize="@dimen/text_size1"
            tool:text="129.1万" />
    </com.kaolafm.kradio.lib.widget.SquareFrameLayout>

    <TextView
        android:id="@+id/tv_subcategory_songmenu_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/x14"
        android:ellipsize="end"
        android:maxLines="2"
        android:maxEms="8"
        android:textColor="@color/text_color_1"
        tool:text="一生太长，我只想对你说五个字"
        android:textSize="@dimen/text_size2" />
</com.kaolafm.kradio.common.widget.CScaleLinearLayout>
