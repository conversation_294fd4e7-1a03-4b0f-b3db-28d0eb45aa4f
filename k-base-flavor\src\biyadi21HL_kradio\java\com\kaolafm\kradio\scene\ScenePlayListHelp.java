//package com.kaolafm.kradio.scene;
//
//import android.os.RemoteException;
//import android.util.Log;
//
//import com.google.gson.Gson;
//import com.kaolafm.base.utils.ListUtil;
//import com.kaolafm.kradio.lib.utils.Constants;
//import com.kaolafm.kradio.lib.utils.Logger;
//import com.kaolafm.opensdk.http.core.HttpCallback;
//import com.kaolafm.opensdk.http.error.ApiException;
//import com.kaolafm.sdk.client.IExecuteResult;
//import com.kaolafm.sdk.client.ex.cmd.SceneRadioCmd;
//
//
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * 比亚迪场景推荐播放处理类
// * （需求：以播放方面来讲分两个情况
// * 1.获取到一个专辑和电台，将专辑的第一个碎片组合到电台的播单列表第一位以电台类型去播放，并在第一首播放完毕
// * 通知比亚迪告知用户是否需要继续播放，如继续进行后续播放，否则停止播放不动。
// * 2.获取到电台或者专辑，直接播放。）
// */
//public class ScenePlayListHelp {
//
//    private static final String TAG = "k.byd.splh";
//
//    private static final int SCENE_ALBUM = 1;
//    private static final int SCENE_RADIO = 4;
//    private static final int BAN_AD_AUDIOID_INDEX = 3;
//
//    private SceneDataList.SceneData addAlbumInfo = null;
//    private SceneDataList.SceneData radioSceneInfo = null;
//    private long albumAudioId;
//
//    private static class PlayListHelpInstance {
//        private static ScenePlayListHelp INSTANCE = new ScenePlayListHelp();
//    }
//
//    public static ScenePlayListHelp getInstance() {
//        return PlayListHelpInstance.INSTANCE;
//    }
//
//    /**
//     * 播放场景电台
//     *
//     * @param sceneMultiData
//     */
//    public void playSceneRadio(SceneMultiData sceneMultiData) {
//        Logger.v(TAG, "playSceneRadio: " + sceneMultiData);
//        new RecRequest().getSceneRadioList(sceneMultiData.getCode(), "10", new HttpCallback<BaseSceneListData<List<SceneDataList>>>() {
//            @Override
//            public void onSuccess(BaseSceneListData<List<SceneDataList>> listBaseSceneListData) {
//                if (listBaseSceneListData != null && listBaseSceneListData.getResult().size() > 0) {
//                    SceneDataList sceneDataList = listBaseSceneListData.getResult().get(0);
//                    if (sceneDataList == null) {
//                        Log.e(TAG, "sceneDataList is null ");
//                        return;
//                    }
//                    List<SceneDataList.SceneData> sceneList = sceneDataList.getDataList();
//                    if (ListUtil.isEmpty(sceneList)) {
//                        Log.e(TAG, "sceneList is null ");
//                        return;
//                    }
//                    if (sceneMultiData.isAdd() && sceneList.size() > 1) {
//                        playAddAlbumRadio(sceneList, sceneMultiData);
//                    } else {
//                        playNormalRadio(sceneList, sceneMultiData);
//                    }
//                } else {
//                    Log.e(TAG, "listBaseSceneListData is null ");
//                }
//            }
//
//            @Override
//            public void onError(ApiException e) {
//                Log.e(TAG, "playSceneRadio ApiException:" + e.getMessage());
//            }
//        });
//    }
//
//    public long getAlbumAudioId() {
//        return albumAudioId;
//    }
//
//    public void setAlbumAudioId(long id) {
//        albumAudioId = id;
//    }
//
//    /**
//     * 播放组合电台
//     *
//     * @param dataList
//     */
//    private void playAddAlbumRadio(List<SceneDataList.SceneData> dataList, SceneMultiData sceneMultiData) {
//        Logger.v(TAG, "playAddAlbumRadio: sceneMultiData=" + sceneMultiData);
//        SceneDataList.SceneData albumInfo = null;
//        SceneDataList.SceneData radioInfo = null;
//        for (int i = 0; i < dataList.size(); i++) {
//            SceneDataList.SceneData sceneData = dataList.get(i);
//            if (sceneData.getContentType() == SCENE_ALBUM) {
//                albumInfo = sceneData;
//            } else {
//                radioInfo = sceneData;
//            }
//            if (albumInfo != null && radioInfo != null) {
//                break;
//            }
//        }
//
//        addAlbumInfo = albumInfo;
//        radioSceneInfo = radioInfo;
//
//        if (addAlbumInfo == null) {
//            Log.e(TAG, "addAlbumInfo null");
//            playNormalRadio(dataList, sceneMultiData);
//            return;
//        }
//
//        getAlbumAudioList(sceneMultiData);
//    }
//
//    private void getAlbumAudioList(SceneMultiData sceneMultiData) {
//        Logger.v(TAG, "getAlbumAudioList: " + sceneMultiData);
//        new AudioDao(TAG).getAlbumPlaylist(addAlbumInfo.getId(), 1, Constants.PAGE_SIZE, 1, 0, new JsonResultCallback<CommonResponse<AlbumAudioListData>>() {
//            @Override
//            public void onResult(Object obj) {
//                if (obj instanceof AlbumAudioListData) {
//                    if (ListUtil.isEmpty(((AlbumAudioListData) obj).getDataList())) {
//                        Logger.v(TAG, "getAlbumAudioList: datalist is Empty!");
//                        return;
//                    }
//                    ArrayList<AudioInfo> dataList = ((AlbumAudioListData) obj).getDataList();
//                    sendSceneRadioCmd(sceneMultiData.getExecuteResult(), dataList.get(0).getAudioId());
//                    getRadioAudioList(dataList.get(0), sceneMultiData);
//                }
//            }
//
//            @Override
//            public void onError(int error, String errorMsg) {
//                Log.e(TAG, "getAlbumAudioList onError :" + error + " msg:" + errorMsg);
//            }
//        });
//    }
//
//    public void sendSceneRadioCmd(IExecuteResult executeResult, long id) {
//        Logger.v(TAG, "sendSceneRadioCmd: id=" + id);
//        SceneRadioCmd.Result result = new SceneRadioCmd.Result();
//        result.setSceneAlbumAudioId(id);
//        String json = new Gson().toJson(result);
//        try {
//            albumAudioId = id;
//            Log.e(TAG, "sendSceneRadioCmd json :" + json);
//            executeResult.onResult(json);
//        } catch (RemoteException e) {
//            e.printStackTrace();
//        }
//    }
//
//    private void getRadioAudioList(AudioInfo audioInfo, SceneMultiData sceneMultiData) {
//        Logger.v(TAG, "getRadioAudioList: audioInfo=" + audioInfo);
//        Logger.v(TAG, "getRadioAudioList: sceneMultiData=" + sceneMultiData);
//        new AudioDao(TAG).getPGCPlaylist(radioSceneInfo.getId(), Constants.BLANK_STR, false, new JsonResultCallback() {
//
//            @Override
//            public void onResult(Object obj) {
//                if (obj instanceof ArrayList) {
//                    ArrayList<AudioInfo> dataList = (ArrayList<AudioInfo>) obj;
//                    if (ListUtil.isEmpty(dataList)) {
//                        Logger.v(TAG, "getRadioAudioList: datalist is Empty!");
//                        return;
//                    }
//                    composePlayListAndPlay(audioInfo, dataList, sceneMultiData);
//                }
//            }
//
//            @Override
//            public void onError(int error, String errorMsg) {
//                Log.e(TAG, "getRadioAudioList onError :" + error + " msg:" + errorMsg);
//            }
//        });
//    }
//
//    private void composePlayListAndPlay(AudioInfo audioInfoAlbum, ArrayList<AudioInfo> dataList, SceneMultiData sceneMultiData) {
//        Log.v(TAG, "start composePlayListAndPlay: audioInfoAlbum=" + audioInfoAlbum);
//        Log.v(TAG, "                            :sceneMultiData=" + sceneMultiData);
//        ArrayList<AudioInfo> playlist = new ArrayList<>();
//        audioInfoAlbum.setMainTitleName(audioInfoAlbum.getAudioName());
//        audioInfoAlbum.setContentType(1);
//        audioInfoAlbum.setContentTypeName(audioInfoAlbum.getAlbumName());
//        playlist.add(audioInfoAlbum);
//        int size = dataList.size();
//        for (int i = 0; i < size; i++) {
//            AudioInfo audioInfo = dataList.get(i);
//            if (audioInfoAlbum.getAudioId() != audioInfo.getAudioId()) {
//                playlist.add(audioInfo);
//            } else {
//                Log.e(TAG, "composePlayListAndPlay repeating data :" + audioInfo.getAudioName());
//            }
//        }
//        RadioBean radioBean = new RadioBean();
//        radioBean.setAudioInfos(playlist);
//        radioBean.setRadioType(Constants.RESOURCES_TYPE_PGC);
//        radioBean.setRadioId(radioSceneInfo.getId());
//        radioBean.setRadioName(radioSceneInfo.getName());
//        radioBean.setRadioPic(radioSceneInfo.getImg());
//        radioBean.setClockId(dataList.get(size - 1).getClockId());
//        radioBean.setSceneMultiData(sceneMultiData);
//        PlayerManager.getInstance().playRadio(radioBean);
//    }
//
//    /**
//     * 正常播放电台
//     *
//     * @param dataList
//     */
//    private void playNormalRadio(List<SceneDataList.SceneData> dataList, SceneMultiData sceneMultiData) {
//        Log.v(TAG, "playNormalRadio:" + dataList.get(0).getName());
//        Log.v(TAG, "               : sceneMultiData=" + sceneMultiData);
//        radioSceneInfo = dataList.get(0);
//        if (radioSceneInfo.getContentType() == SCENE_ALBUM) {
//            PlayerManager.getInstance().playAlbum(radioSceneInfo.getId(), sceneMultiData);
//        } else if (radioSceneInfo.getContentType() == SCENE_RADIO) {
//            PlayerManager.getInstance().playPgc(radioSceneInfo.getId(), sceneMultiData);
//        }
//    }
//
//    public long getSceneRadioId() {
//        if (radioSceneInfo != null) {
//            return radioSceneInfo.getId();
//        }
//        return 0;
//    }
//}
