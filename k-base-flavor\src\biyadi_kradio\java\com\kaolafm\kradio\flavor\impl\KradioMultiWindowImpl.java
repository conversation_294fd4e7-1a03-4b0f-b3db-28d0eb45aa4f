package com.kaolafm.kradio.flavor.impl;

import android.os.Build;
import androidx.annotation.RequiresApi;
import androidx.constraintlayout.widget.ConstraintSet;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;
import android.view.View;
import com.kaolafm.kradio.flavor.R;

import android.view.ViewGroup;
import android.widget.ImageView;

import com.kaolafm.kradio.lib.base.flavor.KRadioMultiWindowInter;
import com.kaolafm.kradio.lib.utils.MultiUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.player.ui.holder.PlayerListBroadcastViewHolder;
import com.kaolafm.kradio.player.ui.horizontal.BroadcastPlayerFragment;
import com.kaolafm.kradio.player.ui.horizontal.RadioPlayerFragment;

/**
 * <pre>
 *     <AUTHOR> Wenchl
 *     e-mail : <EMAIL>
 *     time   : 2019/09/17
 *     desc   :
 *     version: 1.0
 * </pre>
 */
public class KradioMultiWindowImpl implements KRadioMultiWindowInter {

    @RequiresApi(api = Build.VERSION_CODES.N)
    @Override
    public boolean setSettingGridLayoutManager(Object... objects) {
        if (!getMultiStatus()) {
            return false;
        }
        Object o = objects[0];
        if (o instanceof GridLayoutManager) {
            GridLayoutManager gridLayoutManagers = (GridLayoutManager) o;
            gridLayoutManagers.setSpanCount(3);
        }
        return true;
    }

    @RequiresApi(api = Build.VERSION_CODES.N)
    @Override
    public boolean setHistoryGridLayoutManager(Object... objects) {
        if (!getMultiStatus()) {
            return false;
        }

        Object o = objects[0];
        if (o instanceof StaggeredGridLayoutManager) {
            StaggeredGridLayoutManager gridLayoutManagers = (StaggeredGridLayoutManager) o;
            gridLayoutManagers.setSpanCount(1);
        }
        return true;
    }

    @RequiresApi(api = Build.VERSION_CODES.N)
    @Override
    public boolean setSubscriptionGridLayoutManager(Object... objects) {
        try {
            if (!getMultiStatus()) {
                return false;
            }

            Object o = objects[0];
            if (o instanceof StaggeredGridLayoutManager) {
                StaggeredGridLayoutManager gridLayoutManagers = (StaggeredGridLayoutManager) o;
                gridLayoutManagers.setSpanCount(1);
            }

            return true;
        }catch (NoSuchMethodError e){
            return false;
        }
    }

    @Override
    public void doFunctionsEndMargin(Object... objects) {
        ConstraintSet mConstraintSet = (ConstraintSet) objects[0];
        View functions = (View) objects[1];
        mConstraintSet.setMargin(functions.getId(), ConstraintSet.END, ResUtil.getDimen(R.dimen.x20));
    }

    @Override
    public void doFunctionsWidth(Object... objects) {
        ViewGroup viewGroup = (ViewGroup) objects[0];
        if (getMultiStatus()) {
            int count = viewGroup.getChildCount();
            int padding = ResUtil.getDimen(R.dimen.m14);
            int w = ResUtil.getDimen(R.dimen.m56);
            for (int i = 0; i < count; i++) {
                ImageView imageView = (ImageView) viewGroup.getChildAt(i);
                imageView.setPadding(padding,padding,padding,padding);
                ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) imageView.getLayoutParams();
                params.width = w;
                imageView.setLayoutParams(params);
            }
        } else {
            int count = viewGroup.getChildCount();
            int padding = ResUtil.getDimen(R.dimen.home_function_icon_padding);
            int w = ResUtil.getDimen(R.dimen.home_function_icon_width);
            for (int i = 0; i < count; i++) {
                ImageView imageView = (ImageView) viewGroup.getChildAt(i);
                imageView.setPadding(padding,padding,padding,padding);
                ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) imageView.getLayoutParams();
                params.width = w;
                imageView.setLayoutParams(params);
            }
        }
    }

    @Override
    public boolean getMultiStatus(Object... objects) {
        return MultiUtil.getMultiStatus();
    }

    @Override
    public void doMultiSearchFragment(Object... objects) {
        ViewGroup.MarginLayoutParams marginLayoutParams = (ViewGroup.MarginLayoutParams) objects[0];
        marginLayoutParams.width = ResUtil.getDimen(com.kaolafm.kradio.k_kaolafm.R.dimen.x320);
    }

    @Override
    public void doMultiPlayerFragmentRadioPlayList(Object... objects) {
        RadioPlayerFragment radioPlayerFragment = (RadioPlayerFragment) objects[0];
        ConstraintSet set = (ConstraintSet) objects[1];
        set.setGuidelinePercent(radioPlayerFragment.mLandGuideLine.getId(), 0.45f);
    }

    @Override
    public void doMultiPlayerFragmentRadioControllerBar(Object... objects) {
        RadioPlayerFragment radioPlayerFragment = (RadioPlayerFragment) objects[0];
        ConstraintSet set = (ConstraintSet) objects[1];
        set.connect(radioPlayerFragment.mPlayerRadioControllerBar.getId(), ConstraintSet.LEFT, ConstraintSet.PARENT_ID, ConstraintSet.LEFT);
        set.connect(radioPlayerFragment.mPlayerRadioControllerBar.getId(), ConstraintSet.RIGHT, radioPlayerFragment.mPlayListView.getId(), ConstraintSet.LEFT);
    }

    @Override
    public void doMultiPlayerFragmentViewShow(Object... objects) {
//        RadioPlayerFragment radioPlayerFragment = (RadioPlayerFragment) objects[0];
//        Log.i("zsj", "doMultiPlayerFragmentViewShow: MultiUtil.getMultiStatus() = " + MultiUtil.getMultiStatus());
//        if (MultiUtil.getMultiStatus()) {
//            Log.i("zsj", "mTotalNumberText: GONE" + radioPlayerFragment.mTotalNumberText.getText().toString());
//            radioPlayerFragment.mTotalNumberText.setVisibility(View.GONE);
//        } else {
//            Log.i("zsj", "mTotalNumberText: VISIBLE" + radioPlayerFragment.mTotalNumberText.getText().toString());
//            radioPlayerFragment.mTotalNumberText.setVisibility(View.VISIBLE);
//        }
//        radioPlayerFragment.mTotalNumberText.requestLayout();
    }

    @Override
    public void doMultiPlayerFragmentTitleLine(Object... objects) {
        RadioPlayerFragment radioPlayerFragment = (RadioPlayerFragment) objects[0];
        ConstraintSet set = (ConstraintSet) objects[1];
        set.setVerticalBias(radioPlayerFragment.mPlayerTitleLine.getId(), 0.2f);
    }

    @Override
    public void doMultiBroadcastPlayerFragmentList(Object... objects) {
        BroadcastPlayerFragment broadcastPlayerFragment = (BroadcastPlayerFragment) objects[0];
        ConstraintSet set = (ConstraintSet) objects[1];
        set.constrainPercentWidth(broadcastPlayerFragment.mPlayListView.getId(), 1f);
    }

    @Override
    public void doMultiBroadcastPlayerFragmentListItem(Object... objects) {
        PlayerListBroadcastViewHolder playerListBroadcastViewHolder = (PlayerListBroadcastViewHolder) objects[0];
        ConstraintSet mConstraintSet = (ConstraintSet) objects[1];
        mConstraintSet.setGuidelinePercent(playerListBroadcastViewHolder.guideline.getId(), 0.25f);
    }

    @Override
    public void doMultiBroadcastPlayerFragmentPlayIcon(Object... objects) {
        BroadcastPlayerFragment broadcastPlayerFragment = (BroadcastPlayerFragment) objects[0];
        ConstraintSet set = (ConstraintSet) objects[1];
        set.constrainPercentHeight(broadcastPlayerFragment.mPlayerControllerBar.getId(), 0.20f);
        set.setVerticalBias(broadcastPlayerFragment.mPlayerControllerBar.getId(), 0.98f);
    }

}
