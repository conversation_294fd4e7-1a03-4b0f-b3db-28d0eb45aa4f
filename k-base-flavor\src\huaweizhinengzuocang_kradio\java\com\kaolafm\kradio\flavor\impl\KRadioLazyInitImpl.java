package com.kaolafm.kradio.flavor.impl;

import android.app.IntentService;
import android.util.Log;

import com.kaolafm.kradio.common.KradioSDKManager;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioLazyInitInter;
import com.kaolafm.kradio.lib.utils.IntentUtils;
import com.kaolafm.launcher.InitService;

public class KRadioLazyInitImpl implements KRadioLazyInitInter {

    private static String TAG = "KRadioLazyInitImpl";
    private volatile static boolean isInit = false;

    @Override
    public boolean isEnableLazyInit() {
        return true;
    }

    @Override
    public synchronized void afterPrivacyEnable() {
        Log.i(TAG, "afterPrivacyEnable" + isInit);
        if (isInit) {
            return;
        }
        isInit = true;
        InitService.start(AppDelegate.getInstance().getContext());
        KradioSDKManager.getInstance().initAndActivate();
    }
}
