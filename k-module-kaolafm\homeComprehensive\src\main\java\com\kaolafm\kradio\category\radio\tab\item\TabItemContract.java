package com.kaolafm.kradio.category.radio.tab.item;

import com.kaolafm.kradio.categories.HorizontalSubcategoryAdapter;
import com.kaolafm.kradio.lib.bean.SubcategoryItemBean;
import com.kaolafm.opensdk.api.personalise.model.HotRecommend;

import java.util.List;

/**
 * <AUTHOR>
 **/
public class TabItemContract {
    public interface IPresenter extends com.kaolafm.kradio.lib.base.mvp.IPresenter {
        void loadData();

        void loadMore();

        boolean isHasNextPage();

        void onClick(SubcategoryItemBean subcategoryItemBean, int position);

        void onClick(SubcategoryItemBean subcategoryItemBean, HorizontalSubcategoryAdapter adapter,int position);

        void registerListener();

        void unregisterListener();

        void schedulProgramListRequest(int delay);

        void cancelSchedule();
    }

    public interface IView extends com.kaolafm.kradio.lib.base.mvp.IView {
        void showData(List<SubcategoryItemBean> data);

        void showHotRecommend(HotRecommend hotRecommend);

        void showMoreData(List<SubcategoryItemBean> data);

        void showError(Exception exception);

        void showMoreDataError(Exception exception);

        void setSelected();

        void showImage(long id,String imgUrl,String desc);
    }
}
