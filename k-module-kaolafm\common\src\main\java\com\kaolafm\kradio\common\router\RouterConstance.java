package com.kaolafm.kradio.common.router;

/**
 * 阿里路由配置表
 */
public final class RouterConstance {

    private static final String BASE_URL = "/kaolafm";
    private static final String COMPREHENSIVE_URL = "/comprehensive";
    private static final String ONLINE_URL = "/online";

    // 首页
    private static final String BASE_MAIN_URL         = "/main";
    public static final String MAIN_COMPREHENSIVE_URL = BASE_URL + BASE_MAIN_URL + COMPREHENSIVE_URL;
    public static final String MAIN_ONLINE_URL        = BASE_URL + BASE_MAIN_URL + ONLINE_URL;

    // 我的
    private static final String BASE_MINE_URL         = "/mine";
    public static final String MINE_COMPREHENSIVE_URL = BASE_URL + BASE_MINE_URL + COMPREHENSIVE_URL;
    public static final String MINE_ONLINE_URL        = BASE_URL + BASE_MINE_URL + ONLINE_URL;

    // 订单
    private static final String BASE_ORDER_URL         = "/order";
    public static final String ORDER_COMPREHENSIVE_URL = BASE_URL + BASE_ORDER_URL + COMPREHENSIVE_URL;
    public static final String ORDER_ONLINE_URL        = BASE_URL + BASE_ORDER_URL + ONLINE_URL;//

    // 登录
    private static final String BASE_LOGIN_URL         = "/login";
    public static final String LOGIN_COMPREHENSIVE_URL = BASE_URL + BASE_LOGIN_URL + COMPREHENSIVE_URL;
    public static final String LOGIN_ONLINE_URL        = BASE_URL + BASE_LOGIN_URL + ONLINE_URL;

    // 播放详情页
    private static final String BASE_PLAY_URL                   = "/play";
    public static final String PLAY_BROADCAST_COMPREHENSIVE_URL = BASE_URL + BASE_PLAY_URL + "/broadcast" + COMPREHENSIVE_URL;//综合版广播播放页
    public static final String PLAY_RADIO_COMPREHENSIVE_URL     = BASE_URL + BASE_PLAY_URL + "/radio" + COMPREHENSIVE_URL;//综合版专辑播放页
    public static final String PLAY_LIVE_COMPREHENSIVE_URL      = BASE_URL + BASE_PLAY_URL + "/live" + COMPREHENSIVE_URL;//综合版直播间播放页
    public static final String PLAY_LONG_VIDEO_COMPREHENSIVE_URL      = BASE_URL + BASE_PLAY_URL + "/videoLong" + COMPREHENSIVE_URL;//综合版视频播放页
    public static final String PLAY_SHORT_VIDEO_COMPREHENSIVE_URL      = BASE_URL + BASE_PLAY_URL + "/videoShort" + COMPREHENSIVE_URL;//综合版视频播放页
    public static final String PLAY_SHORT_VIDEO_COMPREHENSIVE_URL2      = BASE_URL + BASE_PLAY_URL + "/videoShort2" + COMPREHENSIVE_URL;//综合版视频播放页
    public static final String PLAY_VIDEO_COMPREHENSIVE_URL         = BASE_URL + BASE_PLAY_URL + "/video" + COMPREHENSIVE_URL;//视频列表页
    public static final String PLAY_ONLINE_URL                  = BASE_URL + BASE_PLAY_URL + ONLINE_URL;//在线电台播放页

    // 消息盒子
    private static final String BASE_MESSAGE_URL         = "/message";
    public static final String MESSAGE_COMPREHENSIVE_URL = BASE_URL + BASE_MESSAGE_URL + COMPREHENSIVE_URL;
    public static final String MESSAGE_ONLINE_URL        = BASE_URL + BASE_MESSAGE_URL + ONLINE_URL;

    // webview
    private static final String BASE_WEBVIEW_URL         = "/webview";
    public static final String WEBVIEW_COMPREHENSIVE_URL = BASE_URL + BASE_WEBVIEW_URL + COMPREHENSIVE_URL;
    public static final String WEBVIEW_ONLINE_URL        = BASE_URL + BASE_WEBVIEW_URL + ONLINE_URL;


    // 广告
    private static final String BASE_AD_URL                 = "/ad";
    private static final String BASE_AD_WEBVIEW_URL         = BASE_AD_URL + "/webview";
    public static final String AD_WEBVIEW_COMPREHENSIVE_URL = BASE_URL + BASE_AD_WEBVIEW_URL + COMPREHENSIVE_URL;
    public static final String ONLINE_AD_WEBVIEW_URL        = BASE_URL + BASE_AD_WEBVIEW_URL + ONLINE_URL;

    // 推荐
    private static final String BASE_HOME_LIKE_URL         = "/homelike";
    public static final String HOME_LIKE_COMPREHENSIVE_URL = BASE_URL + BASE_HOME_LIKE_URL + COMPREHENSIVE_URL;
    public static final String HOME_LIKE_ONLINE_URL        = BASE_URL + BASE_HOME_LIKE_URL + ONLINE_URL;

    // 分类
    private static final String BASE_CATEGORIES_URL         = "/categories";
    public static final String CATEGORIES_COMPREHENSIVE_URL = BASE_URL + BASE_CATEGORIES_URL + COMPREHENSIVE_URL;
    public static final String CATEGORIES_ONLINE_URL        = BASE_URL + BASE_CATEGORIES_URL + ONLINE_URL;

    // 活动
    private static final String BASE_ACTIVITIES_URL         = "/activities";
    public static final String ACTIVITIES_COMPREHENSIVE_URL = BASE_URL + BASE_ACTIVITIES_URL + COMPREHENSIVE_URL;
    public static final String ACTIVITIES_ONLINE_URL        = BASE_URL + BASE_ACTIVITIES_URL + ONLINE_URL;

    // 设置
    private static final String BASE_SETTING_URL         = "/setting";
    public static final String SETTING_COMPREHENSIVE_URL = BASE_URL + BASE_SETTING_URL + COMPREHENSIVE_URL;

    // 关于我们
    private static final String BASE_ABOUT_URL         = "/about";
    public static final String ABOUT_COMPREHENSIVE_URL = BASE_URL + BASE_ABOUT_URL + COMPREHENSIVE_URL;

    // 可见即可说
    private static final String BASE_CAN_SEE_CAN_SAY_URL = "/canseecansay";
    public static final String CAN_SEE_CAN_SAY_COMPREHENSIVE_URL = BASE_URL + BASE_CAN_SEE_CAN_SAY_URL + COMPREHENSIVE_URL;

    // 搜索
    private static final String BASE_SEARCH_URL         = "/search";
    public static final String SEARCH_COMPREHENSIVE_URL = BASE_URL + BASE_SEARCH_URL + COMPREHENSIVE_URL;

    // 订阅
    private static final String BASE_SUBSCRIPTIONS_URL         = "/subscriptions";
    public static final String SUBSCRIPTIONS_COMPREHENSIVE_URL = BASE_URL + BASE_SUBSCRIPTIONS_URL + COMPREHENSIVE_URL;
    public static final String SUBSCRIPTIONS_ONLINE_URL        = BASE_URL + BASE_SUBSCRIPTIONS_URL + ONLINE_URL;

    // 收听历史
    private static final String BASE_HISTORY_URL         = "/history";
    public static final String HISTORY_COMPREHENSIVE_URL = BASE_URL + BASE_HISTORY_URL + COMPREHENSIVE_URL;
    public static final String HISTORY_ONLINE_URL        = BASE_URL + BASE_HISTORY_URL + ONLINE_URL;//听迹

    // 品牌主页
    private static final String BASE_BRAND_URL         = "/brand";
    public static final String BRAND_COMPREHENSIVE_URL =  BASE_URL + BASE_BRAND_URL + COMPREHENSIVE_URL;

    // 话题详情
    private static final String BASE_BRAND_TOPIC_DETAIL_URL         = "/topic/detail";
    public static final String BRAND_TOPIC_DETAIL_URL_COMPREHENSIVE = BASE_URL + BASE_BRAND_TOPIC_DETAIL_URL + COMPREHENSIVE_URL;

    // 贴子发布
    private static final String BASE_BRAND_POSTS_URL         = "/posts/publish";
    public static final String BRAND_POSTS_PUBLISH_URL_COMPREHENSIVE = BASE_URL + BASE_BRAND_POSTS_URL + COMPREHENSIVE_URL;


}
