<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.kaolafm.kradio.k_kaolafm">

    <!-- 使用考拉FM车载SDK所需权限 start -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<!--    <uses-permission android:name="android.permission.READ_PHONE_STATE" />-->
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <!-- 使用考拉FM车载SDK所需权限 end -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <!-- SD卡权限 -->
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />

    <!--网易聊天室需要-->
    <uses-permission android:name="android.permission.FLASHLIGHT" />
    <uses-permission android:name="android.permission.VIBRATE" />
       
    <uses-permission android:name="android.permission.BLUETOOTH" />
       
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <!--JobIntentService的权限-->
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <!--android:resizeableActivity="true" 不添加此配置会导致APP在Android9.0系统上显示不全问题，具体bug可参考https://app.huoban.com/tables/2100000007530121/items/2300000932317385?userId=1917386-->
    <application
        android:name="com.kaolafm.KradioApplication"
        android:icon="@drawable/ic_launcher_yt"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:resizeableActivity="@bool/supportMutiWindow"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        tools:replace="android:icon">
        <!--方便windows抓https包-->
        <!--android:networkSecurityConfig="@xml/network_security_config"-->

        <!--程序入口，启页-->
        <activity
            android:name="${TB_MAIN_ACTIVITY}"
            android:configChanges="orientation|screenSize|keyboardHidden|locale|layoutDirection|keyboard|uiMode|fontScale"
            android:launchMode="${MAIN_ACTIVITY_LAUNCH_MODE}"
            android:screenOrientation="landscape"
            android:theme="${TB_MAIN_ACTIVITY_THEME}">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.MONKEY" />
            </intent-filter>
            <meta-data
                android:name="distractionOptimized"
                android:value="true" />
        </activity>

        <!--        <service-->
        <!--            android:name="cn.jpush.android.service.PushService"-->
        <!--            android:exported="false"-->
        <!--            android:process=":pushcore">-->
        <!--            <intent-filter>-->
        <!--                <action android:name="cn.jpush.android.intent.REGISTER" />-->
        <!--                <action android:name="cn.jpush.android.intent.REPORT" />-->
        <!--                <action android:name="cn.jpush.android.intent.PushService" />-->
        <!--                <action android:name="cn.jpush.android.intent.PUSH_TIME" />-->
        <!--            </intent-filter>-->
        <!--        </service>-->
        <!-- User defined.  For test only  用户自定义的广播接收器 -->
        <!--        <receiver-->
        <!--            android:name="com.kaolafm.notification.PushReceiver"-->
        <!--            android:enabled="true"-->
        <!--            android:exported="false">-->
        <!--            <intent-filter>-->
        <!--                <action android:name="cn.jpush.android.intent.REGISTRATION" /> &lt;!&ndash; Required  用户注册SDK的intent &ndash;&gt;-->
        <!--                <action android:name="cn.jpush.android.intent.MESSAGE_RECEIVED" /> &lt;!&ndash; Required  用户接收SDK消息的intent &ndash;&gt;-->
        <!--                <action android:name="cn.jpush.android.intent.NOTIFICATION_RECEIVED" /> &lt;!&ndash; Required  用户接收SDK通知栏信息的intent &ndash;&gt;-->
        <!--                <action android:name="cn.jpush.android.intent.NOTIFICATION_OPENED" /> &lt;!&ndash; Required  用户打开自定义通知栏的intent &ndash;&gt;-->
        <!--                <action android:name="cn.jpush.android.intent.CONNECTION" /> &lt;!&ndash; 接收网络变化 连接/断开 since 1.6.3 &ndash;&gt;-->
        <!--                <action android:name="cn.jpush.android.intent.NOTIFICATION_CLICK_ACTION" />-->
        <!--                <category android:name="${JPUSH_PKGNAME}" />-->
        <!--            </intent-filter>-->
        <!--        </receiver>-->

        <service android:name="com.kaolafm.opensdk.player.core.PlayerService" />

        <!--此Receiver不能删掉否则在android5.0以下系统版本会报 java.lang.IllegalArgumentException: MediaButtonReceiver component may not be null. 异常-->
        <receiver android:name="androidx.media.session.MediaButtonReceiver">
            <intent-filter>
                <action android:name="android.intent.action.MEDIA_BUTTON" />
            </intent-filter>
        </receiver>
        <!--此Service不能删除否则会出现java.lang.RuntimeException: Unable to start receiver android.support.v4.media.session.MediaButtonReceiver:
        java.lang.IllegalStateException: Could not find any Service that handles android.intent.action.MEDIA_BUTTON or implements a media browser service
        错误-->
        <!--<service android:name="com.kaolafm.media.session.KRMediaButtonService">
            <intent-filter>
                <action android:name="android.intent.action.MEDIA_BUTTON" />
            </intent-filter>
        </service>-->

        <meta-data
            android:name="kl_ad_app_id"
            android:value="b4d122db7bd845be9dc5e6456a8b7388" />

        <!-- 配置APP ID -->
        <meta-data
            android:name="BUGLY_APPID"
            android:value="7b3b3d9ec6" />

        <!-- 配置Bugly调试模式（true或者false） -->
        <meta-data
            android:name="BUGLY_ENABLE_DEBUG"
            android:value="false" />
        <meta-data
            android:name="com.kaolafm.open.sdk.qqmusic.AppId"
            android:value="12345700" />
        <meta-data
            android:name="com.kaolafm.open.sdk.qqmusic.AppKey"
            android:value="UyZaTlMrnqSJKNLsoy" />

        <!-- 申请开发所需APP_KEY配置:横版 -->
        <meta-data
            android:name="com.kaolafm.open.sdk.AppKey"
            android:value="${KL_APP_KEY}" />

        <!-- 申请开发所需APP_ID配置 -->
        <meta-data
            android:name="com.kaolafm.open.sdk.AppId"
            android:value="${KL_APP_ID}" />

        <!-- 开发者所需要发布的渠道名称,例如（"K-radio"） -->
        <meta-data
            android:name="com.kaolafm.open.sdk.Channel"
            android:value="${KL_CHANNEL_VALUE}" />

        <meta-data
            android:name="com.kaolafm.open.sdk.Suffix"
            android:value="${IS_APPEND_SUFFIX}" />
        <!--        广告appId-->
        <meta-data
            android:name="com.kaolafm.ad.AppId"
            android:value="${KL_AD_APPID_VALUE}" />

        <meta-data
            android:name="android.max_aspect"
            android:value="2.2" />

        <!--        <service-->
        <!--            android:name="com.kaolafm.kradio.clientControlerForKradio.ClientService"-->
        <!--            android:enabled="true"-->
        <!--            android:exported="${HAS_EXPORTED}">-->
        <!--            <intent-filter>-->
        <!--                <action android:name="com.kaolafm.sdk.client" />-->
        <!--            </intent-filter>-->
        <!--        </service>-->

        <!--        <receiver android:name="com.kaolafm.kradio.clientControlerForKradio.ClientReceiver">-->
        <!--            <intent-filter>-->
        <!--                <action android:name="com.edog.car.server.ACTION" />-->
        <!--                <action android:name="com.kaolafm.sdk.client" />-->
        <!--            </intent-filter>-->
        <!--        </receiver>-->

        <!--此Receiver不能删掉否则在android5.0以下系统版本会报 java.lang.IllegalArgumentException: MediaButtonReceiver component may not be null. 异常-->
        <receiver android:name="androidx.media.session.MediaButtonReceiver">
            <intent-filter>
                <action android:name="android.intent.action.MEDIA_BUTTON" />
            </intent-filter>
        </receiver>

    </application>
</manifest>
