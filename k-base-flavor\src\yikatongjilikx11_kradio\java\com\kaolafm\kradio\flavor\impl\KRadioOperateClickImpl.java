package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.util.Log;

import com.kaolafm.kradio.history.ui.IHistoryView;
import com.kaolafm.kradio.k_kaolafm.home.HomeDataManager;
import com.kaolafm.kradio.lib.base.flavor.KRadioHistoryInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioOperateClickCellInter;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.IntentUtils;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.report.ReportHelper;

/**
 * @Package: com.kaolafm.kradio.flavor.impl
 * @Description: kx11实现运营位跳转
 * //调用示例
 * //   String uriString = "kradio://start_page?pageid=110000&radioid=1200000000099&radiotype=3";
 * //    Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(uriString));
 * //    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
 * //     startActivity(intent);
 * <p>
 * PAGEID 为固定id 目前需求是点击后 进入到首页 进行播放
 * public static final String PAGE_ID_MAIN = "110000";
 * radioid 为播放数据的专辑id
 * radiotype 对应是播放类型
 * //            public static final int RESOURCES_TYPE_ALBUM = 0;
 * //            public static final int RESOURCES_TYPE_AUDIO = 1;
 * //            public static final int RESOURCES_TYPE_RADIO = 3;
 * //            public static final int RESOURCES_TYPE_LIVING = 5;
 */
public class KRadioOperateClickImpl implements KRadioOperateClickCellInter {
    /**
     * 运营位外调SDK启动APP携带的extra类型
     */
    public static final String OPERATE_EXTRA_TYPE = "operate_extra_type";
    public static Intent dataIntent = null;

    @Override
    public Intent getDataIntent() {
        Log.d(getClass().getSimpleName(), "getDataIntent=" + dataIntent);

        return dataIntent;
    }

    @Override
    public void setDataIntent(Intent intent) {
        dataIntent = intent;
    }

    @Override
    public void toPlay() {
        //亿咖通 kx11此处加入运营位点击操作
        KRadioOperateClickCellInter operateModelInter = ClazzImplUtil.getInter("KRadioOperateClickImpl");
        if (operateModelInter != null && operateModelInter.getDataIntent() != null && operateModelInter.getIntentUri(operateModelInter.getDataIntent()) != null) {
            Uri uri = operateModelInter.getIntentUri(operateModelInter.getDataIntent());
            if (uri == null) {
                return;
            }
            try {
                String radioId = uri.getQueryParameter(operateModelInter.RADIOID);
                int radioType = -1;
                if (!StringUtil.isEmpty(uri.getQueryParameter(operateModelInter.RADIOTYPE))) {
                    radioType = Integer.parseInt(uri.getQueryParameter(operateModelInter.RADIOTYPE));
                }
                Log.d(getClass().getSimpleName(), "autoPlayAudio  radioId=" + radioId);
                dataIntent = null;
                if (!StringUtil.isEmpty(radioId) && -1 != radioType) {
                    //设置播放类型，上报数据用，kx11定制，调用下面的方法修改playType
                    ReportHelper.getInstance().setSearchAudioPlayCallBack("3", null);
                    PlayerManagerHelper.getInstance().start(String.valueOf(radioId), radioType);

                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            HomeDataManager.getInstance().playNetOrLocal();
        }
    }

    @Override
    public boolean isOperate(Intent intent) {
        if (intent == null) {
            return false;
        }
        Uri uri = intent.getData();
        if (uri == null) {
            return false;
        }
        if (uri.getScheme().equals("kradio")) {
            Log.d(getClass().getSimpleName(), "-isOperate getScheme true-");

            return true;
        }
        Log.d(getClass().getSimpleName(), "-isOperate getScheme false-");

        return false;
    }


    @Override
    public void setIntent(Intent intent) {
        if (null != intent) {
            Uri uri = intent.getData();
            if (uri == null) {
                return;
            }


            String pageid = uri.getQueryParameter(PAGEID);
            String radioid = uri.getQueryParameter(RADIOID);
            String radiotype = uri.getQueryParameter(RADIOTYPE);
            Log.d(getClass().getSimpleName(), "pageid --" + pageid + "-radioid-" + radioid + "-radiotype-" + radiotype);
            intent.putExtra(OPERATE_EXTRA_TYPE, uri);
            intent.putExtra(IntentUtils.AUTO_PLAY_EXTRA, true);//自动播放
            intent.putExtra(IntentUtils.START_TYPE, "3");//用于日志上报，仅kx11定制需求
            dataIntent = intent;
            Log.d(getClass().getSimpleName(), "-dataIntent  uri-" + dataIntent.getParcelableExtra(OPERATE_EXTRA_TYPE).toString());

        }
    }

    @Override
    public Uri getIntentUri(Intent intent) {
//        intent.getData()
        if (intent == null)
            return null;
        Uri uri = intent.getParcelableExtra(OPERATE_EXTRA_TYPE);
        if (uri != null) {
            Log.d(getClass().getSimpleName(), "getIntentUri --" + uri.toString());

        }
        return uri;
    }

    @Override
    public boolean startToLauncher() {
        return true;
    }


}
