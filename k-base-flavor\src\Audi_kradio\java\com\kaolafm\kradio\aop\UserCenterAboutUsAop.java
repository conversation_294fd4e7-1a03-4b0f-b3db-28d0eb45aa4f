package com.kaolafm.kradio.aop;

import android.content.Context;
import android.text.Html;
import android.text.TextUtils;
import android.text.method.ScrollingMovementMethod;
import android.util.Log;
import android.view.View;
import android.widget.ScrollView;
import android.widget.TextView;

import com.kaolafm.kradio.common.utils.FlavorUtil;
import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.setting.UserCenterAboutUsFragment;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

@Aspect
public class UserCenterAboutUsAop {
    private static final String TAG = "UserCenterAboutUsAop";

    @Around("execution(* com.kaolafm.kradio.setting.UserCenterAboutUsFragment.showWebView(..))")
    public void showWebView(ProceedingJoinPoint point) throws Throwable {
        Log.d(TAG, "showWebView");
        UserCenterAboutUsFragment fragment = (UserCenterAboutUsFragment) point.getThis();
        if (fragment.getContext() == null){
            return;
        }
        TextView textView = fragment.getView().findViewById(R.id.tvInfo);
        if (textView != null) {
            String url = (String) point.getArgs()[0];
            if (TextUtils.equals(FlavorUtil.getHttp443Url(Constants.HTTP_URL_SERVICE_AGREEMENT), url)) {
                textView.setText(Html.fromHtml(getHtml(fragment.getContext(),"help_service_agreement")));
            } else {
                textView.setText(Html.fromHtml(getHtml(fragment.getContext(),"help_privacy_policy")));
            }
        }
        ScrollView scrollView = fragment.getView().findViewById(R.id.scrollView);
        scrollView.scrollTo(0,0);
        fragment.getView().findViewById(R.id.web_view_layout).setVisibility(View.VISIBLE);
    }

    @Around("execution(* com.kaolafm.kradio.setting.UserCenterAboutUsFragment.setWebViewSetting(..))")
    public void setWebViewSetting(ProceedingJoinPoint point) throws Throwable {
        Log.d(TAG, "setWebViewSetting");
    }

    public String getHtml(Context context, String file) {
        String result = "";
        InputStreamReader inputReader = null;
        BufferedReader bufReader = null;
        try {
            inputReader = new InputStreamReader(context.getResources()
                    .getAssets().open(file), "UTF-8");
            bufReader = new BufferedReader(inputReader);
            String line = "";
            while ((line = bufReader.readLine()) != null)
                result += line;
            bufReader.close();
            inputReader.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }
}
