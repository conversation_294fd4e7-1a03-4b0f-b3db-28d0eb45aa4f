package com.kaolafm.kradio.coin;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;
import android.text.TextUtils;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.kradio.user.UserInfoManager;
import com.kaolafm.kradio.user.UserInfoManager.IUserInfoStateListener;
import com.kaolafm.opensdk.account.token.AccessTokenManager;
import com.kaolafm.opensdk.account.token.KaolaAccessToken;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import kotlin.jvm.JvmStatic;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;


public final class CoinViewModel extends ViewModel {
    private final CoinRepository repository = new CoinRepository();
    @NotNull
    private final MutableLiveData coinCount = new MutableLiveData();
    @NotNull
    private final MutableLiveData qrCode = new MutableLiveData();
    @NotNull
    private final MutableLiveData isLogined = new MutableLiveData();
    @NotNull
    public static final String TAG = "coin";
    private static int oldCoinCount = -1;
    @NotNull
    public static final CoinViewModel.Companion Companion = new CoinViewModel.Companion();

    @NotNull
    public final MutableLiveData getCoinCount() {
        return this.coinCount;
    }

    @NotNull
    public final MutableLiveData getQrCode() {
        return this.qrCode;
    }

    @NotNull
    public final MutableLiveData isLogined() {
        return this.isLogined;
    }

    public final void queryCoinCount() {
        AccessTokenManager var10000 = AccessTokenManager.getInstance();
        Intrinsics.checkExpressionValueIsNotNull(var10000, "AccessTokenManager.getInstance()");
        KaolaAccessToken var2 = var10000.getKaolaAccessToken();
        Intrinsics.checkExpressionValueIsNotNull(var2, "AccessTokenManager.getInstance().kaolaAccessToken");
        String userId = var2.getUserId();
        Logger.e("coin", "userId: " + userId + " ,hashCode:" + this.hashCode());
        if (TextUtils.isEmpty((CharSequence)userId)) {
            this.coinCount.setValue(0);
        } else {
            this.repository.getCoinCount((HttpCallback)(new HttpCallback() {
                public void onSuccess(@Nullable Integer coinC) {
                    CoinViewModel.this.getCoinCount().setValue(coinC);
                    Logger.d("coin", "getCoinCount: " + coinC);
                    CoinViewModel.Companion var10000 = CoinViewModel.Companion;
                    if (coinC == null) {
                        Intrinsics.throwNpe();
                    }

                    var10000.setOldCoinCount(coinC);
                }

                // $FF: synthetic method
                // $FF: bridge method
                public void onSuccess(Object var1) {
                    this.onSuccess((Integer)var1);
                }

                public void onError(@Nullable ApiException error) {
                    CoinViewModel.this.getCoinCount().setValue(0);
                }
            }));
        }

    }

    protected void onCleared() {
        super.onCleared();
        this.repository.release();
    }

    public CoinViewModel() {
        this.repository.getQrCode((HttpCallback)(new HttpCallback() {
            public void onSuccess(@Nullable String qrUrl) {
                Logger.i("coin", "qr url: " + qrUrl);
                CoinViewModel.this.getQrCode().setValue(qrUrl);
            }

            // $FF: synthetic method
            // $FF: bridge method
            public void onSuccess(Object var1) {
                this.onSuccess((String)var1);
            }

            public void onError(@Nullable ApiException e) {
                Logger.e("coin", "qr : " + e);
            }
        }));
        MutableLiveData var10000 = this.isLogined;
        UserInfoManager var10001 = UserInfoManager.getInstance();
        Intrinsics.checkExpressionValueIsNotNull(var10001, "UserInfoManager.getInstance()");
        var10000.setValue(var10001.isUserBound());
        StringBuilder var1 = (new StringBuilder()).append("is login: ");
        UserInfoManager var10002 = UserInfoManager.getInstance();
        Intrinsics.checkExpressionValueIsNotNull(var10002, "UserInfoManager.getInstance()");
        Logger.w("coin", var1.append(var10002.isUserBound()).toString());
        UserInfoManager.getInstance().addUserInfoStateListener((IUserInfoStateListener)(new IUserInfoStateListener() {
            public void userLogin() {
                Logger.d("coin", "userLogin: ");
                CoinViewModel.this.isLogined().setValue(true);
            }

            public void userLogout() {
                Logger.d("coin", "userLogout: ");
                CoinViewModel.this.isLogined().setValue(false);
                CoinViewModel.this.getCoinCount().setValue(0);
            }

            public void userCancel() {
                Logger.d("coin", "userCancel: ");
                CoinViewModel.this.isLogined().setValue(false);
                CoinViewModel.this.getCoinCount().setValue(0);
            }
        }));
    }

    public static final int getOldCoinCount() {
        CoinViewModel.Companion var10000 = Companion;
        return oldCoinCount;
    }

    public static final void setOldCoinCount(int var0) {
        CoinViewModel.Companion var10000 = Companion;
        oldCoinCount = var0;
    }
    
    public static final class Companion {
        /** @deprecated */
        // $FF: synthetic method
        @JvmStatic
        public static void oldCoinCount$annotations() {
        }

        public final int getOldCoinCount() {
            return CoinViewModel.oldCoinCount;
        }

        public final void setOldCoinCount(int var1) {
            CoinViewModel.oldCoinCount = var1;
        }

        // $FF: synthetic method
        public Companion() {

        }
    }
}
