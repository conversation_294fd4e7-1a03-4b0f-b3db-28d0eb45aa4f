package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.content.Intent;
import android.util.Log;

import com.kaolafm.kradio.k_kaolafm.home.HorizontalHomePlayerFragment;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioPlayLogicInter;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import static com.kaolafm.kradio.receiver.BYDMediaModeReceiver.MEDIA_MODE_ACTION;

/**
 * <pre>
 *     <AUTHOR> Wenchl
 *     e-mail : <EMAIL>
 *     time   : 2019/07/28
 *     desc   :
 *     version: 1.0
 * </pre>
 */
public class KRadioAudioPlayLogicImpl implements KRadioAudioPlayLogicInter {
    @Override
    public boolean autoPlayAudio(Object... args) {
        return false;
    }

    @Override
    public boolean requestAudioFocus(Object... args) {
        boolean flag = PlayerManager.getInstance().requestAudioFocus();
        return flag;
    }

    @Override
    public boolean resumeAudioPlayLogic(Object... args) {
        if (args != null && args.length > 0) {
//            Activity activity = ((HorizontalHomePlayerFragment) args[0]).getActivity();
            Activity activity = (Activity) args[0];
            if (activity != null) {
                Intent intent = activity.getIntent();

                String mode = null;
                if (intent != null) {
                    mode = intent.getStringExtra(MEDIA_MODE_ACTION);
                }
                Log.i("byd KRadioAudioPlayLogicImpl", "MainActivity  mode : " + mode);

                if (MEDIA_MODE_ACTION.equals(mode)) {
                    //回到考拉FM必须要先强占音频焦点
                    if (PlayerManager.getInstance().getCurrentAudioFocusStatus() < 0) {
                        requestAudioFocus();
                    }

                    PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
                    if (playItem != null && !PlayerManager.getInstance().isPlaying()) {
                        PlayerManagerHelper.getInstance().switchPlayerStatus(true);
                    }
                } else {
                    if (PlayerManager.getInstance().isPauseFromUser()) {
                        requestAudioFocus();
                        return false;
                    }
                    if (!PlayerManager.getInstance().isPlaying() && PlayerManager.getInstance().getCurrentAudioFocusStatus() < 0) {
                        requestAudioFocus();
                        PlayerManagerHelper.getInstance().switchPlayerStatus(true);
                    }
                }
            }
        }
        return false;
    }

    @Override
    public boolean restartAudioPlayLogic(Object... args) {
        return false;
    }

    @Override
    public boolean doStartInPlay(Object... args) {
        if (!PlayerManager.getInstance().isPlaying()) {
            PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
            PlayerManagerHelper.getInstance().startPlayItemInList(playItem);
           // PlayerManagerHelper.getInstance().switchPlayerStatus(true);
        }
        return true;
    }
}