package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.incall.vehicle.proxy.IVehicleNetworkCallback;
import com.incall.vehicle.proxy.IVehicleNetworkConfirmCallback;
import com.incall.vehicle.proxy.VehicleNetworkManager;
import com.kaolafm.kradio.flavor.carnetwork.api.ChangAnApiRequest;
import com.kaolafm.kradio.flavor.carnetwork.carnetworkutils.CarAuthUtil;
import com.kaolafm.kradio.flavor.carnetwork.carnetworkutils.ChangAnAuthListener;
import com.kaolafm.kradio.flavor.carnetwork.carnetworkutils.ChanganAuthConstants;
import com.kaolafm.kradio.flavor.carnetwork.dialog.ChangAnDialog;
import com.kaolafm.kradio.k_kaolafm.home.data.Category;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.base.flavor.KRadioAuthInter;
import com.kaolafm.kradio.lib.utils.SharedPreferenceUtil;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import com.kaolafm.opensdk.player.core.listener.ICheckCanPlayInter;
import com.kaolafm.opensdk.player.core.listener.ICheckCanPlayResult;
import com.kaolafm.opensdk.player.core.utils.PlayerCustomizeManager;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;


public class CheckCanPlayImpl implements ICheckCanPlayInter {
    private Category.Item mCategoryItem;
    private PlayItem mPlayItem;
    private boolean fromUser;
    private String mType;
    private ICheckCanPlayResult mICheckCanPlayResult;

    private SharedPreferenceUtil mSPUtil;
    private ChangAnApiRequest mChangAnApiRequest;
    private ChangAnDialog mChangAnDialog;

    private Activity preActivity;
    private Activity curActivity;
    private Handler mainHandler;

    private boolean isFromTTS = false;//防止语音播报通过后继续调用播放器导致流程走两次

    private static class InstanceHolder {
        private final static ICheckCanPlayInter CheckCanPlay = PlayerCustomizeManager.getInstance().getCheckCanPlayInter();
    }

    public static ICheckCanPlayInter getInstance() {
        return InstanceHolder.CheckCanPlay;
    }


    @Override
    public boolean checkPlay(Object... args) {
        mType = (String) args[1];
        //如果是直播获取到的item为Category.Item类型
        if (mType.equals(KRadioAuthInter.METHOD_LIVING)) {
            mCategoryItem = (Category.Item) args[0];
        } else {
            mPlayItem = (PlayItem) args[0];
        }
        fromUser = (boolean) args[2];

        if (args[3] != null) {
            mICheckCanPlayResult = (ICheckCanPlayResult) args[3];
        }
        if (mainHandler == null) {
            mainHandler = new Handler(Looper.getMainLooper());
        }

//        if (isFromTTS) {
//            isFromTTS = false;
//            doPlay(mType);
//            return true;
//        }

        CarAuthUtil.ischecking = true;
        checkAction(new ChangAnAuthListener() {
            @Override
            public void onIsCanUse(boolean isCanUse) {
                Log.i("zsj", "onIsCanUse = " + isCanUse + ",mType = " + mType);
//                PlayerManager.getInstance().notifyIPlayChangedListener(mPlayItem);
//                if (isCanUse) {
//                    //可以播放
//                    runUIdismiss();
//                    doPlay(mType);
//                } else {
//                    //不可播放
//                    doPause(mType);
//                }
                mICheckCanPlayResult.onResult(isCanUse);
                CarAuthUtil.ischecking = false;
            }

            @Override
            public void Error(int errCode, String errMsg) {
                //不可播放
//                doPause(mType);
                mICheckCanPlayResult.onResult(false);
                CarAuthUtil.ischecking = false;
            }
        });


        return true;
    }

    @Override
    public void addListener(Object... objects) {

    }

    @Override
    public void removeListener(Object... objects) {

    }


    //执行播放操作
//    private void doPlay(String type) {
//        if (mPlayItem != null) {
//            PlayerManager.getInstance().notifyIPlayChangedListener(mPlayItem);
//        }
//        switch (type) {
//            case ICheckCanPlayInter.METHOD_PLAY:
//                try {
//                    if (playerBinder != null) {
//                        playerBinder.playItem(fromUser);
//                    }
//                } catch (RemoteException e) {
//                    e.printStackTrace();
//                }
//                break;
//
//            case ICheckCanPlayInter.METHOD_START:
//                if (playerBinder != null) {
//                    playerBinder.startItem(mPlayItem);
//                }
//                break;
//
//            case KRadioAuthInter.METHOD_TTS:
////                isFromTTS = true;
////                boolean isIntercept = ClockPlayer.getInstance(AppDelegate.getInstance().getContext()).intercept();
////                String radioType = PlayItemUtils.getRadioType();
////                if (isIntercept && String.valueOf(ResType.RADIO_TYPE).equals(radioType)) {
////                    PlayerManager.getInstance().reset();
////                    ClockPlayer.getInstance(AppDelegate.getInstance().getContext())
////                            .setClockPlayDoneListener(new ClockPlayer.ClockPlayDoneListener() {
////                                @Override
////                                public void onClockPlayDone() {
////                                    ClockPlayer.getInstance(AppDelegate.getInstance().getContext())
////                                            .setClockPlayDoneListener(null);
////                                    PlayerManager.getInstance().originStart(mPlayItem);
////                                }
////                            });
////                    ClockPlayer.getInstance(AppDelegate.getInstance().getContext()).play();
////                    PlayerManager.getInstance().notifyIPlayChangedListener(mPlayItem);
////                } else {
////                    PlayerManager.getInstance().originStart(mPlayItem);
////                }
//                break;
//            case KRadioAuthInter.METHOD_NETCHANGE:
//                //如果是网络切换不进行播放动作的执行，防止正在播放中的重复播放
//                break;
//            case KRadioAuthInter.METHOD_LIVING:
//                //跳转至直播界面
//                PageJumper.getInstance().jumpToLivePage(mCategoryItem);
//                break;
//            default:
//        }
//    }
//
//    //执行暂停操作
//    private void doPause(String type) {
//        //鉴权不通过通知卡片切换
//        if (mainHandler != null) {
//            mainHandler.post(new Runnable() {
//                @Override
//                public void run() {
//                    //已在主线程中，可以更新UI
//                    if (mPlayItem != null) {
//                        PlayerManager.getInstance().notifyIPlayChangedListener(mPlayItem);
//                    }
//                }
//            });
//        }
//
//        if (playerBinder != null && playerBinder.isPlaying()) {
//            try {
//                playerBinder.pause(false);
//            } catch (RemoteException e) {
//                e.printStackTrace();
//            }
//        }
//    }


    public void checkAction(final ChangAnAuthListener mChangAnAuthListener) {
        VehicleNetworkManager.getInstance().getUCSHost(new IVehicleNetworkCallback() {
            @Override
            public void onException(int arg0, String arg1) {
                Log.i("zsj", "getUCSHost  onException=: arg0 = " + arg0 + ",arg1 = " + arg1);
            }

            @Override
            public void onCompleted(String arg0) {
                if ("incall.changan.com.cn".equals(arg0)) {
                    //正式环境
                    ChanganAuthConstants.BASEURL = ChanganAuthConstants.BASEURL_OFFICIAL;
                    Log.i("zsj", "正式环境  ChanganAuthConstants.BASEURL = " + ChanganAuthConstants.BASEURL_OFFICIAL);
                } else {
                    //测试环境
                    ChanganAuthConstants.BASEURL = ChanganAuthConstants.BASEURL_TEST;
                    Log.i("zsj", "测试环境  ChanganAuthConstants.BASEURL = " + ChanganAuthConstants.BASEURL_TEST);
                }
            }
        });


//        Log.i("zsj", "checkAction: checkAuthStatusAndDate = " + CarAuthUtil.checkAuthStatusAndDate());
//        CarAuthUtil.saveAuthAndDate("0", false);
        if (!CarAuthUtil.checkAuthStatusAndDate()) {
            mChangAnAuthListener.onIsCanUse(true);
            return;
        }
        curActivity = AppManager.getInstance().getCurrentActivity();
        if (preActivity != curActivity && curActivity != null) {
            preActivity = curActivity;
            if (curActivity != null) mChangAnDialog = new ChangAnDialog((Context) curActivity);
        } else {
            if (mChangAnDialog == null && curActivity != null) {
                mChangAnDialog = new ChangAnDialog((Context) curActivity);
            }
        }

        //显示loading状态
        runUIshow(ChanganAuthConstants.CODE_LOADING);
        ChangAnAuth(mChangAnAuthListener);

    }


    /**
     * 获取当前车机激活状态
     */
    public void ChangAnAuth(final ChangAnAuthListener mChangAnAuthListener) {
        //基础鉴权是否通过
        VehicleNetworkManager.getInstance().authentication(new IVehicleNetworkCallback() {
            @Override
            public void onCompleted(String s) {
                Log.i("zsj", "authentication onCompleted: s = " + s);
                //1、inactive 未激活：车联网基础服务未激活，请到4S店注册或者下载INCALL APP注册。
                // 2、active 已激活：无提示
                // 3、expire 已过期：车联网基础服务无法使用，请致电呼叫中心4000-888-6677。
                // 4、retired 已失效：车联网基础服务已无法使用，请到4S店更换设备。
                //需要打开
                if (("active").equals(s)) {
                    ChangAnAuthRequest(mChangAnAuthListener);
                } else {
                    mChangAnAuthListener.onIsCanUse(false);
                    VehicleNetworkManager.getInstance().showConfirmDialogWithStatus(s, new IVehicleNetworkConfirmCallback() {
                        @Override
                        public void onClick(int i) {
//                            AppManager.getInstance().appExit();
                        }
                    });
                    runUIshow(ChanganAuthConstants.CODE_ERROR);
                    CarAuthUtil.saveAuthAndDate(String.valueOf(System.currentTimeMillis()), false);
                }
//                ChangAnAuthRequest(mChangAnAuthListener);
            }

            @Override
            public void onException(int i, String s) {
                Log.i("zsj", "authentication onException: i = " + i + ",s = " + s);
                VehicleNetworkManager.getInstance().showConfirmDialogWithStatus(s, new IVehicleNetworkConfirmCallback() {
                    @Override
                    public void onClick(int i) {

                    }
                });
                runUIshow(ChanganAuthConstants.CODE_CONNECTFAILED);
                CarAuthUtil.saveAuthAndDate(String.valueOf(System.currentTimeMillis()), false);
                mChangAnAuthListener.Error(i, s);
            }
        });
    }

    private void runUIshow(final Integer code) {
        try {
            if (mainHandler != null) {
                mainHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        //已在主线程中，可以更新UI
                        if (mChangAnDialog != null) {
                            mChangAnDialog.setCode(code);
                            mChangAnDialog.show();
                        }
                    }
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
            runUIshow(ChanganAuthConstants.CODE_ERROR);
        }
    }

    private void runUIdismiss() {
        try {
            if (mainHandler != null) {
                mainHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        //已在主线程中，可以更新UI
                        if (mChangAnDialog != null) {
                            mChangAnDialog.dismiss();
                        }
                    }
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 判断当前网络是否需要鉴权，获取当前的Access token 并 请求联通结果获取流量鉴权结果
     */
    private void ChangAnAuthRequest(final ChangAnAuthListener mChangAnAuthListener) {

        Log.i("zsj", "checkAction: needAuthByNetWork = " + CarAuthUtil.needAuthByNetWork());
        if (!CarAuthUtil.needAuthByNetWork()) {
            mChangAnAuthListener.onIsCanUse(true);
            return;
        }

        //获取当前的Access token
        VehicleNetworkManager.getInstance().getToken(new IVehicleNetworkCallback() {
            @Override
            public void onCompleted(String token) {
                Log.i("zsj", "getToken onCompleted: " + token);
                String appid = ChanganAuthConstants.CHANGAN_APPID;
                String vid = token;
                String itemid = ChanganAuthConstants.CHANGAN_ITEMID;
                long timestamp = System.currentTimeMillis();
                Log.i("zsj", "ChangAnAuth  getChangAnAuth  param:  " + "appid = " + appid + ",vid = " + vid + ",itemid = " + itemid + ",timestamp = " + timestamp);
                if (mChangAnApiRequest == null) {
                    mChangAnApiRequest = new ChangAnApiRequest();
                }
                mChangAnApiRequest.getChangAnAuth(appid, vid, itemid, timestamp, new HttpCallback<Integer>() {
                    @Override
                    public void onSuccess(Integer integer) {
                        if (integer <= 0) {
                            runUIshow(integer);
                            CarAuthUtil.saveAuthAndDate(String.valueOf(System.currentTimeMillis()), false);
                            mChangAnAuthListener.onIsCanUse(false);
                        } else if (integer == 1) {
                            runUIdismiss();
                            CarAuthUtil.saveAuthAndDate(String.valueOf(System.currentTimeMillis()), true);
                            mChangAnAuthListener.onIsCanUse(true);
                        }
                    }

                    @Override
                    public void onError(ApiException e) {
                        //由于对方接口返回数据与我们的不一致,导致一直走onError.可以通过e.getCode()判断是否成功
                        int code = e.getCode();
                        Log.i("zsj", "ChangAnAuth getChangAnAuth json :  = " + e.toString());
                        if (code <= 0) {
                            runUIshow(code);
                            CarAuthUtil.saveAuthAndDate(String.valueOf(System.currentTimeMillis()), false);
                            mChangAnAuthListener.onIsCanUse(false);
                        } else if (code == 1) {
                            runUIdismiss();
                            CarAuthUtil.saveAuthAndDate(String.valueOf(System.currentTimeMillis()), true);
                            mChangAnAuthListener.onIsCanUse(true);
                        } else if (code == 604 || code == 408) {
                            //超时和其他错误  sdkapi超时errorcode没有暴露  超时状态至为604和408
                            runUIshow(ChanganAuthConstants.CODE_TIMEOUT);
                            CarAuthUtil.saveAuthAndDate(String.valueOf(System.currentTimeMillis()), false);
                            mChangAnAuthListener.Error(code, e.getMessage());
                        } else {
                            runUIshow(ChanganAuthConstants.CODE_ERROR);
                            CarAuthUtil.saveAuthAndDate(String.valueOf(System.currentTimeMillis()), false);
                            mChangAnAuthListener.Error(code, e.getMessage());
                        }
                    }
                });
            }

            @Override
            public void onException(int i, String s) {
                Log.i("zsj", "getToken onCompleted:  i = " + i + ",s = " + s);
                runUIshow(ChanganAuthConstants.CODE_CONNECTFAILED);
                CarAuthUtil.saveAuthAndDate(String.valueOf(System.currentTimeMillis()), false);
                mChangAnAuthListener.Error(i, s);
            }
        });
    }

}
