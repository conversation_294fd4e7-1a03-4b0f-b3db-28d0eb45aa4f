package com.kaolafm.kradio.media.session;

import android.content.Context;
import android.support.v4.media.session.MediaSessionCompat;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-04-09 18:00
 ******************************************/
public interface KRMediaSessionInter {
    /**
     * 注册多媒体按键
     *
     * @param context
     */
    void registerMediaSession(Context context);

    /**
     * 注销多媒体按键
     *
     * @param context
     */
    void unregisterMediaSession(Context context);

    /**
     * 获取MediaSession对象
     *
     * @return
     */
    MediaSessionCompat getMediaSession();
}
