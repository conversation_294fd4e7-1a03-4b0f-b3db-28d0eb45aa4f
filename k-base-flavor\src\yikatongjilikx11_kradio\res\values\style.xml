<?xml version="1.0" encoding="utf-8"?>
<resources>

<!--    <style name="CustomerCircularProgressBar" parent="CPB">-->
<!--        <item name="cpb_color">@color/cpb_default_color</item>-->
<!--        <item name="cpb_stroke_width">@dimen/cpb_default_stroke_width</item>-->
<!--        <item name="cpb_min_sweep_angle">60</item>-->
<!--        <item name="cpb_max_sweep_angle">150</item>-->
<!--        <item name="cpb_sweep_speed">@string/cpb_default_sweep_speed</item>-->
<!--        <item name="cpb_rotation_speed">@string/cpb_default_rotation_speed</item>-->
<!--    </style>-->

<!--    &lt;!&ndash;dialogFragment 二次进场会重走动画，暂时先去掉&ndash;&gt;-->
<!--    <style name="DefaultAnimation"/>-->

    <!--    <style name="BydFragmentBackButton">-->
    <!--        <item name="android:layout_width">@dimen/m80</item>-->
    <!--        <item name="android:layout_height">@dimen/m80</item>-->
    <!--        <item name="android:background">@drawable/color_main_button_click_selector</item>-->
    <!--        <item name="android:padding">@dimen/m22</item>-->
    <!--        <item name="android:scaleType">centerInside</item>-->
    <!--        <item name="android:src">@drawable/byd_globle_arrow_normal</item>-->
    <!--        <item name="android:layout_marginLeft">@dimen/x18</item>-->
    <!--        <item name="layout_constraintLeft_toLeftOf">parent</item>-->
    <!--        <item name="layout_constraintTop_toTopOf">parent</item>-->
    <!--    </style>-->

    <style name="AppThemeCompat.splash" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowNoTitle">true</item>//无标题
        <item name="android:windowActionBar">false</item>//无ActionBar
        <item name="android:windowFullscreen">true</item>//全屏即无通知栏
<!--        <item name="android:windowFullscreen">true</item>//全屏即无通知栏-->
        <item name="android:windowContentOverlay">@null</item>//是否有遮盖
        <!--<item name="android:windowIsTranslucent">true</item>-->
        <!--        <item name="android:windowBackground">@drawable/splash_yunting</item>-->
        <item name="android:windowBackground">@drawable/background_splash_comprehensive</item>
<!--        <item name="android:windowBackground">@drawable/splash_yt_tmp</item>-->
        <!--        <item name="android:windowBackground">@drawable/ic_launcher_yt</item>-->
        <!--<item name="android:windowAnimationStyle">@style/activityDefaultAnimation</item>-->
        <!--<item name="android:windowAnimationStyle">@style/activityAnim</item>-->
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
    </style>
<!--    <style name="CustomDialog" parent="android:style/Theme.Dialog">-->
<!--        &lt;!&ndash;背景颜色及透明程度&ndash;&gt;-->
<!--        <item name="android:windowBackground">@android:color/transparent</item>-->
<!--        &lt;!&ndash;是否有标题 &ndash;&gt;-->
<!--        <item name="android:windowNoTitle">true</item>-->
<!--        &lt;!&ndash;是否浮现在activity之上&ndash;&gt;-->
<!--        <item name="android:windowIsFloating">true</item>-->
<!--        &lt;!&ndash;是否模糊&ndash;&gt;-->
<!--        <item name="android:backgroundDimEnabled">true</item>-->
<!--    </style>-->
</resources>