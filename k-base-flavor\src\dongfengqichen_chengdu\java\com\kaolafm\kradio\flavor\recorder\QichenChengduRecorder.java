package com.kaolafm.kradio.flavor.recorder;

import android.content.Context;
import android.media.AudioFormat;
import android.media.AudioRecord;
import android.media.MediaRecorder;
import android.util.Log;

import com.kaolafm.kradio.live1.player.LiveManager;

import com.kaolafm.kradio.lib.base.flavor.live.KradioRecorderInterface;
import com.kaolafm.kradio.lib.utils.recorder.PcmToAacUtil;

import java.io.BufferedOutputStream;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;

import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.schedulers.Schedulers;

/**
 * <pre>
 *     <AUTHOR> Wenchl
 *     e-mail : <EMAIL>
 *     time   : 2019/05/28
 *     desc   :
 *     version: 1.0
 * </pre>
 */
public class QichenChengduRecorder implements KradioRecorderInterface {

    private static final String TAG = "KradioQichenChengDuRecorder";

    private static final String FILE_PCM = "Live-Leave-message-qichenchengdu.aac";

    private AudioRecord mAudioRecord;

    private int bufferSize;

    private short[] mAudioRecordData;

    private String filePath;

    private PcmToAacUtil pcmToAacUtil;

    public QichenChengduRecorder() {
        int samplerate = 64000;
        bufferSize = AudioRecord.getMinBufferSize(samplerate, AudioFormat.CHANNEL_IN_STEREO, AudioFormat.ENCODING_PCM_16BIT);
        mAudioRecord = new AudioRecord(MediaRecorder.AudioSource.MIC,
                64000, AudioFormat.CHANNEL_IN_STEREO,
                AudioFormat.ENCODING_PCM_16BIT, bufferSize);
        mAudioRecordData = new short[bufferSize];
    }

    private File createNewAudioFile() {
        if (!LiveManager.RECORDINGS_DIR.exists()) {
            LiveManager.RECORDINGS_DIR.mkdirs();
        }
        File file = new File(LiveManager.RECORDINGS_DIR, FILE_PCM);
        return file;
    }

    @Override
    public void startRecord() {

        final File file = createNewAudioFile();

        if (file.exists()) {
            file.delete();
        }

        filePath = file.getAbsolutePath();

        Observable.create(new ObservableOnSubscribe<Boolean>() {
            @Override
            public void subscribe(ObservableEmitter<Boolean> emitter) throws Exception {
                try {

                    pcmToAacUtil = new PcmToAacUtil(44100, 1);

                    DataOutputStream dos = new DataOutputStream(
                            new BufferedOutputStream(
                                    new FileOutputStream(file)));

                    mAudioRecord.startRecording();

                    while (mAudioRecord.getRecordingState() == AudioRecord.RECORDSTATE_RECORDING) {
                        int number = mAudioRecord.read(mAudioRecordData, 0, bufferSize);

                        short[] mic2Data = new short[number / 4];
                        int j = 0;
                        //启辰成都是4ch的数据，测试发现只有mic2的数据可用。
                        for (int i = 0; i < number; i = i + 4) {
                            mic2Data[j] = mAudioRecordData[i + 1];
                            j++;
                        }

                        byte[] btData = pcmToAacUtil.offerEncoder(shortToByteArray(mic2Data));

                        dos.write(btData);
                    }
                    dos.flush();
                    dos.close();
                } catch (FileNotFoundException e) {
                    e.printStackTrace();
                } catch (IOException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        }).subscribeOn(Schedulers.io()).subscribe();

    }

    @Override
    public boolean stopRecord() {
        if (mAudioRecord == null) {
            return false;
        }

        Log.i(TAG, "stopRecord");
        try {
            mAudioRecord.stop();
            if (pcmToAacUtil != null) {
                pcmToAacUtil.close();
            }
        } catch (IllegalStateException e) {
            Log.i(TAG, "stopRecord error:" + e.getMessage());
            return false;
        }

        return true;
    }

    @Override
    public String getFilePath() {
        return filePath;
    }

    private byte[] shortToByteArray(short[] sData) {
        int shortArrsize = sData.length;
        byte[] bytes = new byte[shortArrsize * 2];
        for (int i = 0; i < shortArrsize; i++) {
            bytes[i * 2] = (byte) (sData[i] & 0x00FF);
            bytes[(i * 2) + 1] = (byte) (sData[i] >> 8);
            sData[i] = 0;
        }
        return bytes;
    }

}
