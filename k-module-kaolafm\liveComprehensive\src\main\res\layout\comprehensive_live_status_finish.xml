<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:visibility="gone"
    android:id="@+id/live_finish_layout"
    app:layout_constraintTop_toTopOf="parent"
    app:layout_constraintLeft_toLeftOf="parent"
    app:layout_constraintRight_toRightOf="parent"
    android:layout_marginTop="@dimen/y270"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/live_finish_num_layout"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/m57"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/m36"
            android:textColor="#FFEEEEEE"
            android:text="本场直播参与人数" />
        <com.kaolafm.kradio.component.ui.base.view.KradioTextView
            android:id="@+id/live_finish_num"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/m48"
            android:textColor="#FFEEEEEE"
            android:text=""
            android:layout_weight="0.7"/>
        <TextView
            android:id="@+id/live_finish_num_ten_thousand"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/m36"
            android:textColor="#FFEEEEEE"
            android:visibility="gone"
            android:text="万" />
    </LinearLayout>

    <TextView
        app:layout_constraintTop_toBottomOf="@id/live_finish_num_layout"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="@dimen/m40"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/m54"
        android:textSize="@dimen/m36"
        android:textColor="#FFEEEEEE"
        android:text="- 直播已结束 -" />
</androidx.constraintlayout.widget.ConstraintLayout>