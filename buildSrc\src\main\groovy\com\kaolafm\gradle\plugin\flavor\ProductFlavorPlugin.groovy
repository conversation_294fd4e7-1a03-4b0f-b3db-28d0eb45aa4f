package com.kaolafm.gradle.plugin.flavor

import com.android.build.gradle.AppPlugin
import com.android.build.gradle.BaseExtension
import com.android.build.gradle.internal.dsl.ProductFlavor
import com.android.build.gradle.internal.dsl.SigningConfig
import com.android.builder.internal.ClassFieldImpl
import com.android.builder.model.ClassField
import com.kaolafm.gradle.plugin.Util
import groovy.json.JsonSlurper
import org.gradle.api.NamedDomainObjectContainer
import org.gradle.api.Plugin
import org.gradle.api.Project

/**
 * 生成渠道对应的分辨率values的插件，基于1280x720
 * <AUTHOR>
 * @date 2019/1/9
 */
class ProductFlavorPlugin implements Plugin<Project> {

    private Project mProject

    private boolean shouldSetApplicationId = false

    private BaseExtension mAndroid

    private static final String DEFAULT_DIMENSION = "K-radio"

    @Override
    void apply(Project project) {
        mProject = project
        mAndroid = project.extensions.findByName("android")
        shouldSetApplicationId = isApplication()
        def singleChannel = Util.readLocalProperties(mProject, "SINGLE_CHANNEL")
        def multiChannel = Util.readLocalProperties(mProject, "MULTI_CHANNEL")
        mAndroid?.flavorDimensions(DEFAULT_DIMENSION)
        mAndroid?.productFlavors { container ->
            def json = getChannels(project)
            json.each {
                if (singleChannel?.length() > 0) {
                    if (it.manifestPlaceholders.KL_CHANNEL_VALUE == singleChannel) {
                        createFlavor(container, it)
                    }
                } else if (multiChannel?.length() > 0) {
                    def multiChannels = multiChannel.split(",")
                    multiChannels.each {
                        channel ->
                            if (channel == it.manifestPlaceholders.KL_CHANNEL_VALUE) {
                                createFlavor(container, it)
                            }
                    }
                } else {
                    createFlavor(container, it)
                }
            }
        }

    }

    def getChannels(Project project) {
        def rootDir = project.rootDir.path
        def file = new File(rootDir + "/flavor.json")
        return new JsonSlurper().parse(file)
    }

    /**
     * 创建渠道
     * @param container
     * @param flavorInfo
     */
    def createFlavor(NamedDomainObjectContainer<ProductFlavor> container, Object flavorInfo) {
        def channel = flavorInfo.manifestPlaceholders.KL_CHANNEL_VALUE
        if (channel == null || channel == "") {
            channel = flavorInfo.name
        }
        //创建渠道
        ProductFlavor flavor = container.create(channel)
        //lib中不能设置ApplicationId
        if (shouldSetApplicationId) {
            setApplicationId(flavor, flavorInfo)
        }
        addNdk(flavor, flavorInfo)

        //设置dimension，目前只有一个，flavor.json文件中可以设置，使用默认的。如果需要多个，一定要在mAndroid.flavorDimensions(DEFAULT_DIMENSION)中添加多个。
        def dimension = flavorInfo.dimension
        flavor.dimension = dimension ? dimension : DEFAULT_DIMENSION

        //设置小版本号
        String versionSuffix = flavorInfo.minorVersion
        if (versionSuffix?.trim()?.length() > 0) {
            flavor.versionNameSuffix = versionSuffix
        }

        GenerateValueFilesUtil.updateBaseWH(flavorInfo.buildConfigFields.layoutType)
        GenerateSkinValueFilesUtil.updateBaseWH(flavorInfo.buildConfigFields.layoutType)

        //设置自定义BuildConfig属性
        flavor.addBuildConfigFields(getConfigFields(flavorInfo.buildConfigFields))

        //清单文件配置
        def placeholders = flavorInfo.manifestPlaceholders
        if (placeholders) {
            flavor.manifestPlaceholders = placeholders
        }
        //剔除不需要资源
        def res = flavorInfo.resConfig
        if (res?.size() > 0) {
            flavor.resConfigs(res)
        }
        System.out.println(">>>>>>mProject.name == " + mProject.name)
        //渠道项目单独适配
        if ("k-base-flavor" == mProject.name) {
            def projectPath = mProject.projectDir.path
            //渠道分辨率
            GenerateValueFilesUtil.generateDensity(projectPath, channel, flavorInfo.density)
            //添加渠道依赖包
            addFlavorDependencies(channel, projectPath)

            //添加代码目录
//            addFlavorSrcDir(channel, projectPath, flavorInfo)
//            IconUtil.createWatermark(mProject, channel, flavor.name)

        } else if ("skin_day_comprehensive" == mProject.name) {
            // 综合版白天模式分辨率适配
            def projectPath = mProject.projectDir.path
            //渠道分辨率
            GenerateSkinValueFilesUtil.generateDensity(projectPath, channel, flavorInfo.density)
        } else if ("skin_night" == mProject.name) {
            // 神色模式分辨率适配
            def projectPath = mProject.projectDir.path
            //渠道分辨率
            GenerateSkinValueFilesUtil.generateDensity(projectPath, channel, flavorInfo.density)
        } else if ("online_skin_day" == mProject.name) {
            // 在线电台白天module分辨率适配
            def projectPath = mProject.projectDir.path
            //渠道分辨率
            GenerateSkinValueFilesUtil.generateDensity(projectPath, channel, flavorInfo.density)
        } else if ("k-base-lib" == mProject.name) {
            def projectPath = mProject.projectDir.path
            //k-base-lib下的分辨率
            GenerateValueFilesUtil.generateDensity(projectPath,flavorInfo.density)
        }
        if (isApplication()) {
            //添加渠道签名
            addSigningConfig(flavor, flavorInfo)
            addProguardRules(flavor)
        }
    }

    def addNdk(ProductFlavor flavor, Object flavorInfo) {
        def abis = flavorInfo.abiFilters
        if (abis == null || abis.size() == 0) {
            abis = new ArrayList<String>(1)
            abis.add("armeabi")
        }
        // 特定渠道配置需要加入的Abi 例如armeabi，armeabi-v7a，arm64-v8a，x86，x86_64，mips，mips64
        flavor.ndk.setAbiFilters(abis)
    }

    def isApplication() {
        return mProject.plugins.hasPlugin(AppPlugin)
    }

    def setApplicationId(ProductFlavor flavor, Object flavorInfo) {
        //设置ApplicationId(包名)
        String applicationId = flavorInfo.applicationId
        if (applicationId?.trim()?.length() > 0) {
            flavor.applicationId = applicationId
        }
        //设置ApplicationId后缀，
        String idSuffix = flavorInfo.applicationidSuffix
        if (idSuffix?.trim()?.length() > 0) {
            flavor.applicationIdSuffix = idSuffix
        }
    }

    /**
     * 获取BuildConfigField集合
     */
    def getConfigFields(Map<String, Object> fields) {
        Map<String, ClassField> map = new HashMap<>()
        if (fields != null && !fields.isEmpty()) {
            fields.each {
                key, value ->
                    if (value != null) {
                        ClassField classField = new ClassFieldImpl(value.getClass().typeName, Util.underscoreName(key),
                                String.valueOf(value))
                        map.put(key, classField)
                    }
            }
        }
        return map
    }


    def addFlavorSrcDir(String channel, String projectPath, Object flavorInfo) {
        def obj = mAndroid.sourceSets.findByName(channel)
        if (obj == null) {
            obj = mAndroid.sourceSets.create(type)
        }
//        if (flavorInfo.patterns.size() > 0) {
//            obj.java.srcDirs(new Object()[]);
//        }
        for (String dir : flavorInfo.patterns) {
            obj.java.srcDir(projectPath + "/src/${channel}/${dir}/java");
            obj.resources.srcDirs(projectPath + "/src/${channel}/${dir}/res");
        }

    }

    /**
     * 添加渠道依赖jar、aar
     * @param channel
     * @param projectPath
     * @return
     */
    def addFlavorDependencies(String channel, String projectPath) {
        def file = mProject.file(projectPath + "/src/" + channel + "/libs")
        if (file.exists()) {
            def files = file.listFiles()
            if (files && files.size() > 0) {
                def imple = mProject.configurations.getByName("${channel}Implementation")
                imple.dependencies.add(mProject.dependencies.create(mProject.files(files)))
            }
        }


        def fileCompileOnly = mProject.file(projectPath + "/src/" + channel + "/onlylibs")
        if (fileCompileOnly.exists()) {
            def files = fileCompileOnly.listFiles()
            if (files && files.size() > 0) {
                def imple = mProject.configurations.getByName("compileOnly")
                imple.dependencies.add(mProject.dependencies.create(mProject.files(files)))
            }
        }

        def fileProvided = mProject.file(projectPath + "/src/" + channel + "/provided")
        if (fileProvided.exists()) {
            def files = fileProvided.listFiles()
            if (files && files.size() > 0) {
                def imple = mProject.configurations.getByName("provided")
                imple.dependencies.add(mProject.dependencies.create(mProject.files(files)))
            }
        }

    }

    def addSigningConfig(ProductFlavor flavor, Object flavorInfo) {
        String channel = flavor.name
        def projectPath = mProject.rootDir.path + "/k-base-flavor"
        def storePath = projectPath + "/src/" + channel
        def signStores = mProject.file(storePath + "/sign_store.xml")
        if (!signStores.exists()) {
            storePath = mProject.rootDir.path + "/k-router"
            def signStoresFileName = "sign_store.xml"
            for (String pattern : flavorInfo.patterns) {
                if (pattern.equals("online")) {
                    signStoresFileName = "sign_store_yunting.xml"
                }
            }
            signStores = mProject.file(storePath + "/" + signStoresFileName)
            if (!signStores.exists()) {
                throw new FileNotFoundException("找不到签名配置文件sign_store.xml")
            }
        }
        flavor.signingConfig = getSignConfig(signStores, channel, storePath)
    }

    SigningConfig getSignConfig(File signStores, String channel, String channelPath) {
        def store = new XmlSlurper().parse(signStores)
        def storeName = store.getProperty("storeName").text()
        def signingConfig = mAndroid.signingConfigs.maybeCreate(channel + "_" + storeName)
        signingConfig.setStoreFile(new File(adjustPath(store.getProperty("storeFile").text(), channelPath)))
        signingConfig.setStorePassword(store.getProperty("storePassword").text())
        signingConfig.setKeyAlias(store.getProperty("keyAlias").text())
        signingConfig.setKeyPassword(store.getProperty("keyPassword").text())
        def v1SigningEnabled = store.getProperty("v1SigningEnabled")
        signingConfig.setV1SigningEnabled(v1SigningEnabled != null ? v1SigningEnabled.toBoolean() : true)
        def v2SigningEnabled = store.getProperty("v2SigningEnabled")
        signingConfig.setV2SigningEnabled(v2SigningEnabled != null ? v2SigningEnabled.toBoolean() : true)
        return signingConfig
    }

    def addProguardRules(ProductFlavor flavor) {
        mProject.afterEvaluate {
            def path = mProject.rootDir.path + "/k-base-flavor/src/" + flavor.name + "/proguard-rules.pro"
            def proguardFile = mProject.file(path)
            if (proguardFile.exists()) {
                mAndroid.buildTypes.getByName("release").proguardFile(proguardFile)
            }
        }
    }

    def adjustPath(String path, String channelPath) {
        if (path.startsWith('~/')) {
            path = path.replaceFirst('~', System.getProperty('user.home'))
            return path
        } else {
            return channelPath + "/" + path
        }
    }
    /**
     * 检查是否是jar或aar
     * @param fileName
     * @return
     */
    def isLibs(String fileName) {
        return fileName != null && (fileName.endsWith(".jar") || fileName.endsWith(".aar"))
    }

}