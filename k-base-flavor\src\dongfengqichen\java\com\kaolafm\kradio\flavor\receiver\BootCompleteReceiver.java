package com.kaolafm.kradio.flavor.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.kaolafm.kradio.flavor.common.SystemBootUtil;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-07-05 11:54
 ******************************************/
public final class BootCompleteReceiver extends BroadcastReceiver {
    private static final String TAG = "BootCompleteReceiver";

    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        Log.i(TAG, "onReceive----------------->" + action);
        if ("com.hsae.auto.ACTION_AUTO_CORE".equals(action)) {
            SystemBootUtil systemBootUtil = new SystemBootUtil();
            systemBootUtil.updateFirstBoot(context, true);
        }
    }
}
