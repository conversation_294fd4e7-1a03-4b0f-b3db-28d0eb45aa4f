<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_layout"
    android:layout_width="@dimen/m605"
    android:layout_height="@dimen/m309"
    android:layout_gravity="center_horizontal"
    android:background="@drawable/bg_dialog"
    android:gravity="center"
    android:minWidth="@dimen/m605"
    android:orientation="vertical"
    android:paddingLeft="@dimen/x60"
    android:paddingTop="@dimen/y50"
    android:paddingRight="@dimen/x60"
    android:paddingBottom="@dimen/y50">

    <TextView
        android:id="@+id/tv_dialog_bottom_message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textColor="@color/dialog_common_btn_title_text_color"
        android:textSize="@dimen/text_size4"
        tools:text="确定清空收听历史吗?" />

    <LinearLayout
        android:id="@+id/tv_dialog_button_main_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y30"
        android:gravity="center_horizontal"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_dialog_bottom_define"
            android:layout_width="@dimen/m156"
            android:layout_height="@dimen/m50"
            android:background="@drawable/selector_dialog_costomize_btn_sure"
            android:gravity="center"
            android:text="@string/ok"
            android:textColor="@color/dialog_common_btn_cancel_text_color"
            android:textSize="@dimen/text_size4" />

        <TextView
            android:id="@+id/tv_dialog_bottom_cancel"
            android:layout_width="@dimen/m156"
            android:layout_height="@dimen/m50"
            android:layout_marginStart="@dimen/x110"
            android:background="@drawable/selector_dialog_costomize_btn_cancle"
            android:gravity="center"
            android:minWidth="@dimen/m156"
            android:text="@string/cancel"
            android:textColor="@color/dialog_common_btn_sure_text_color"
            android:textSize="@dimen/text_size4" />
    </LinearLayout>
</LinearLayout>