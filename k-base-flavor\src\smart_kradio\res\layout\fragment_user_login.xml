<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/user_login_main_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/user_login_customer_view_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/login_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="stl_login_tab,fl_container" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/login_success_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="user_avatar,user_name,btn_logout" />

    <com.flyco.tablayout.SegmentTabLayout
        android:id="@+id/stl_login_tab"
        android:layout_width="@dimen/x390"
        android:layout_height="@dimen/y68"
        android:layout_marginTop="@dimen/user_login_tab_layout_margin"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tl_bar_color="@color/user_login_tab_title_bg_color"
        app:tl_bar_stroke_color="@color/transparent_color"
        app:tl_indicator_anim_enable="true"
        app:tl_indicator_color="@color/user_tab_select_color"
        app:tl_textSelectColor="@color/color2"
        app:tl_textUnselectColor="@color/user_login_tab_title_text_un_select_color"
        app:tl_textsize="@dimen/text_size_title4" />

    <FrameLayout
        android:id="@+id/fl_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/user_login_tab_layout_margin"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/stl_login_tab" />

    <ImageView
        android:id="@+id/user_avatar"
        android:layout_width="@dimen/m104"
        android:layout_height="@dimen/m104"
        android:layout_marginTop="@dimen/user_login_avatar_margin"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/user_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y16"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size_title4"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/user_avatar" />

    <Button
        android:id="@+id/btn_logout"
        style="@style/user_logout_button_style"
        android:layout_marginTop="@dimen/y50"
        android:background="@drawable/bg_login_btn"
        android:text="@string/user_logout"
        android:textSize="@dimen/text_size_title4"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/user_name" />

</androidx.constraintlayout.widget.ConstraintLayout>