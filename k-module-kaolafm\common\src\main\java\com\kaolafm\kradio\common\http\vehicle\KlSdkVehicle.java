package com.kaolafm.kradio.common.http.vehicle;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Build;
import android.text.TextUtils;

import com.kaolafm.base.utils.DeviceUtil;
import com.kaolafm.base.utils.NetworkMonitor;
import com.kaolafm.kradio.common.http.GeneralCallback;
import com.kaolafm.kradio.constant.UserComponentConst;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioAuthInter;
import com.kaolafm.kradio.lib.base.flavor.player.KLOnInitDataListener;
import com.kaolafm.kradio.lib.base.flavor.player.KLOnPlayerPlayLogicListener;
import com.kaolafm.kradio.lib.base.flavor.player.KLOnPlayerPlayLogicPreparingInnerImplListener;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.ClazzUtil;
import com.kaolafm.kradio.lib.utils.ComponentUtil;
import com.kaolafm.kradio.lib.utils.KaolaAppConfigData;
import com.kaolafm.kradio.lib.utils.StringUtil;

import java.lang.ref.WeakReference;

import static com.kaolafm.kradio.lib.utils.ClazzUtil.getClazzInstance;


/******************************************
 * 类描述： 考拉FM车载电台SDK初始化类 类名称：KlSdkVehicle
 *
 * @version: 1.0
 * @author: shaoningYang
 * @time: 2016-7-20 16:54
 ******************************************/
public class KlSdkVehicle {
//    private static final String TAG = "KlSdkVehicle";
    /**
     * APP ID
     */
    private String mAppId;
    /**
     * APP KEY
     */
    private String mAppKey;
    /**
     * 经度
     */
    private String lon;
    /**
     * 纬度
     */
    private String lat;

    private String mAutoId;

    /**
     * 渠道号
     */
    private String mChannel;
    /**
     * 子渠道
     */
    private String mChildChannel;
    /**
     * 包名
     */
    private String mPackageName;
    /**
     * app版本号
     */
    private String mVersion; // appversion;
    /**
     * 系统版本号
     */
    private String mOsVersion; // osversion;
    /**
     * 用户id
     */
    private String mUid;
    /**
     * 1000460(竖屏) 1000400（横屏）
     */
    private String mAppType;

    /**
     * 车型（比如：比亚迪的T18、T15）
     */
    private String mCarType;

    /**
     * 设备唯一标识
     */
    private String mUdid;

    /**
     * SDK 数据相关逻辑回调
     */
    private KLOnInitDataListener mKLOnInitDataListener;

    private KLOnPlayerPlayLogicListener mKLOnPlayerPlayLogicListener;

    /**
     * 当前app是否可以在后台响应播放操作 true为是，false为否
     */
    private boolean isCanContinuePlayInBackGround = true;

//    private WeakReference<IjkMediaPlayer> mIjkMediaPlayer;
//
//    private WeakReference<VLCMediaPlayService> mVlcMediaPlayServiceWeakReference;

    private WeakReference<KLOnPlayerPlayLogicPreparingInnerImplListener> mKLOnPlayerPlayLogicPreparingInnerImplListenerWeakReference;

    private KlSdkVehicle() {
    }

    private static class KL_SDK_VEHICLE_CLASS {
        private static KlSdkVehicle KL_SDK_VEHICLE_INSTANCE = new KlSdkVehicle();
    }

    public static KlSdkVehicle getInstance() {
        return KL_SDK_VEHICLE_CLASS.KL_SDK_VEHICLE_INSTANCE;
    }
//
//    public void setIjkMediaPlayer(IjkMediaPlayer ijkMediaPlayer) {
//        mIjkMediaPlayer = new WeakReference<>(ijkMediaPlayer);
//    }
//
//    public void setVlcMediaPlayService(VLCMediaPlayService vlcMediaPlayService) {
//        mVlcMediaPlayServiceWeakReference = new WeakReference<>(vlcMediaPlayService);
//    }
//
//    public void setPlayerService(KLOnPlayerPlayLogicPreparingInnerImplListener klOnPlayerPlayLogicPreparingInnerImplListener) {
//        mKLOnPlayerPlayLogicPreparingInnerImplListenerWeakReference = new WeakReference<>(klOnPlayerPlayLogicPreparingInnerImplListener);
//    }

    /**
     * 初始化车载基线SDK基础数据
     *
     * @param generalCallback 初始化回调
     */
    public void initKlSdkVehicle(GeneralCallback<Boolean> generalCallback) {
        Context context = AppDelegate.getInstance().getContext();
        mVersion = getVersionName(context);
        mOsVersion = Build.VERSION.RELEASE;
        initAppKeyAndId();
        NetworkMonitor.getInstance(context).registerNetworkStatusChangeListener(mOnNetworkStatusChangedListener);
    }

    private void initAppKeyAndId() {
        if (!TextUtils.isEmpty(mAppId)) {
            return;
        }
        KaolaAppConfigData kaolaAppConfigData = KaolaAppConfigData.getInstance();
        mAppKey = kaolaAppConfigData.getAppKey();
        mAppId = kaolaAppConfigData.getAppId();
        mChannel = kaolaAppConfigData.getChannel();
        mPackageName = kaolaAppConfigData.getPackageName();
        if (mKLOnInitDataListener != null) {
            mUdid = mKLOnInitDataListener.initUdid();
        }

    }

    NetworkMonitor.OnNetworkStatusChangedListener mOnNetworkStatusChangedListener = new NetworkMonitor.OnNetworkStatusChangedListener() {
        @Override
        public void onStatusChanged(int newStatus, int oldStatus) {

            //todo yangsn
//            // 解决https://app.huoban.com/tables/2100000007530121/items/2300001377241864?userId=1229522问题
//            if (!KRadioApplication.SDKInit) {
//                return;
//            }

            String id = ComponentUtil
                    .getResultValue(UserComponentConst.NAME, UserComponentConst.GET_OPEN_ID);
            if (!TextUtils.isEmpty(id)) {
                return;
            }
            if (newStatus == NetworkMonitor.STATUS_MOBILE || newStatus == NetworkMonitor.STATUS_WIFI) {
                //initOpenId(null);
            }
//            if (mIjkMediaPlayer == null) {
//                return;
//            }
//            IjkMediaPlayer ijkMediaPlayer = mIjkMediaPlayer.get();
//            if (ijkMediaPlayer != null) {
//                ijkMediaPlayer.clearProxyAddress();
//            }
        }
    };

    /**
     * 获取APP配置在AndroidManifest.xml中的appId
     *
     * @return
     */
    public String getAppId() {
        return mAppId;
    }

    /**
     * 获取APP传入的AppKey
     *
     * @return
     */
    public String getAppKey() {
        return mAppKey;
    }

    public void setAppIdAndKey(String appId, String appKey) {
        mAppId = appId;
        mAppKey = appKey;
    }

    /**
     * 获取Context
     *
     * @return
     */
    public Context getContext() {
        return AppDelegate.getInstance().getContext();
    }

    public String getLon() {
        return lon;
    }

    public void setLon(String lon) {
        this.lon = lon;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getChannel() {
        return mChannel;
    }

    public String getPackageName() {
        if (mPackageName == null) {
            mPackageName = getContext().getPackageName();
        }
        return mPackageName;
    }

    public void setPackageName(String packageName) {
        this.mPackageName = packageName;
    }

    /**
     * 获取用户id
     *
     * @return
     */
    public String getUid() {
        return mUid;
    }

    public void setUid(String mUid) {
        this.mUid = mUid;
    }

    public String getUdid() {
        if (mUdid == null) {
            mUdid = DeviceUtil.getDeviceId(getContext());
        }
        return mUdid;
    }

    public void setUdid(String mUdid) {
        this.mUdid = mUdid;
    }

    public void setChildChannel(String childChannel) {
        this.mChildChannel = childChannel;
    }

    public String getChildChannel() {
        return mChildChannel;
    }

    /**
     * 设置横竖屏产品线
     *
     * @param appType
     */
    public void setAppType(String appType) {
        this.mAppType = appType;
    }

    public String getAppType() {
        return this.mAppType;
    }

    public void setCarType(String carType) {
        this.mCarType = carType;
    }

    public String getCarType() {
        return mCarType;
    }

    /**
     * 释放资源:网络监听
     */
    public void release() {
        //之前没有释放,会导致内存泄漏
        NetworkMonitor.getInstance(AppDelegate.getInstance().getContext()).removeNetworkStatusChangeListener(mOnNetworkStatusChangedListener);
        try {
            KRadioAuthInter mKRadioAuthInter = (KRadioAuthInter) ClazzUtil.invoke(Class.forName(StringUtil.join(ClazzImplUtil.CLASS_FATHER_PACKAGE, "KRadioAuthImpl")), null, "getInstance", new Object[]{});
            if (mKRadioAuthInter != null) {
                mKRadioAuthInter.unInitCheckCanPlayInter();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 注册一个处理播放流程/逻辑的具体实现
     *
     * @param clazzName
     */
    public void injectKLOnPlayerPlayLogicListener(String clazzName) {
        if (this.mKLOnPlayerPlayLogicListener != null) {
            return;
        }
        Object obj = getClazzInstance(clazzName);
        if (obj == null) {
            return;
        }

        this.mKLOnPlayerPlayLogicListener = (KLOnPlayerPlayLogicListener) obj;
    }

    public KLOnPlayerPlayLogicListener getKLOnPlayerPlayLogicListener() {
        return mKLOnPlayerPlayLogicListener;
    }

    public void setCanContinuePlayInBackGround(boolean canContinuePlayInBackGround) {
        isCanContinuePlayInBackGround = canContinuePlayInBackGround;
    }

    public boolean isCanContinuePlay() {
        return isCanContinuePlayInBackGround;
    }

    public void setChannel(String channel) {
        mChannel = channel;
    }

    public String getVersionName(Context context) {
        if (!TextUtils.isEmpty(mVersion)) {
            return mVersion;
        }
        try {
            PackageManager packageManager = context.getPackageManager();
            PackageInfo packInfo = packageManager.getPackageInfo(context.getPackageName(), 0);
            mVersion = packInfo.versionName;
        } catch (Throwable t) {
            t.printStackTrace();
        }
        return mVersion;
    }


}
