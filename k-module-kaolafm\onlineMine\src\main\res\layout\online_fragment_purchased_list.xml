<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/sub_main_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_marginTop="@dimen/y40"
        android:layout_width="match_parent"
        android:layout_height="@dimen/y36">
        <TextView
            android:id="@+id/tv_purchased_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/online_purchased_text_count_color"
            android:textSize="@dimen/text_size3"
            android:text="123" />
    </RelativeLayout>

    <com.kaolafm.kradio.common.widget.refresh.KradioSmartRefreshHorizontal
        android:id="@+id/refreshLayout"
        android:layout_marginTop="@dimen/y35"
        android:layout_marginBottom="@dimen/y20"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_sub"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never"/>
    </com.kaolafm.kradio.common.widget.refresh.KradioSmartRefreshHorizontal>

    <ViewStub
        android:id="@+id/vs_layout_error_page"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout="@layout/online_error_layout" />


    <TextView
        android:id="@+id/tv_no_purchased"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/online_tv_no_purchased_top_margin"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size4"
        android:visibility="gone"
        android:layout_gravity="center" />

    <include
        android:id="@+id/purchased_loading"
        layout="@layout/online_refresh_center"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:layout_gravity="center" />

</LinearLayout>