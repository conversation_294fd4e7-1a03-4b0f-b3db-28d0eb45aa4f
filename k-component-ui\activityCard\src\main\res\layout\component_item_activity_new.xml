<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/item_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/item_activitys_bg_iv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/comprehensive_activity_item_blue" />

    <LinearLayout
        android:orientation="vertical"
        android:layout_centerInParent="true"
        android:id="@+id/pic_rl"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center" >

        <RelativeLayout
            android:id="@+id/row_icon"
            android:layout_width="match_parent"
            android:gravity="center"
            android:layout_height="0dp"
            android:layout_weight="66" >

            <com.kaolafm.kradio.component.ui.base.view.OvalImageView
                android:id="@+id/pic_image"
                app:circle="true"
                android:layout_width="@dimen/m100"
                android:layout_height="@dimen/m100"
                android:layout_centerHorizontal="true"
                android:background="@drawable/activity_item_img_bg"
                />

        </RelativeLayout>

        <LinearLayout
            android:id="@+id/row_title"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:gravity="center_horizontal|bottom"
            android:layout_height="0dp"
            android:layout_weight="24" >

            <com.kaolafm.kradio.component.ui.base.view.KradioTextView
                android:id="@+id/title_activity"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/m10"
                android:layout_marginRight="@dimen/m10"
                android:gravity="center"
                android:maxLines="1"
                android:paddingLeft="@dimen/x5"
                android:paddingRight="@dimen/x5"
                android:textColor="#E9EDFF"
                android:textSize="@dimen/m26"
                android:textStyle="bold"
                tools:ignore="MissingConstraints"
                tools:text="活动标题活动标题活动标题" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/row_time"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:gravity="center_horizontal|bottom"
            android:layout_height="0dp"
            android:layout_weight="18" >
            <com.kaolafm.kradio.component.ui.base.view.KradioTextView
                android:id="@+id/date_activity"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/m8"
                android:gravity="center"
                android:maxEms="12"
                android:maxLines="1"
                android:textColor="#ffffff"
                android:textSize="@dimen/m24"
                tools:text="2022.03.12" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/row_content"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:gravity="start|top"
            android:layout_height="0dp"
            android:layout_weight="46" >

            <TextView
                android:id="@+id/des_activity"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/y20"
                android:maxLines="2"
                android:lineSpacingExtra="@dimen/m4"
                android:layout_marginStart="@dimen/m28"
                android:layout_marginEnd="@dimen/m28"
                android:textColor="#ccffffff"
                android:textSize="@dimen/m24"
                tools:text="新年活动新年活动新年活动新年活动新年活动新年活动新年活动新年活动新年活动新年活动新年活动" />

        </LinearLayout>
        <LinearLayout
            android:id="@+id/row_button"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:gravity="center"
            android:layout_weight="56" >
            <TextView
                android:id="@+id/activity_details_tv"
                android:layout_width="@dimen/m200"
                android:layout_height="@dimen/m52"
                android:gravity="center"
                android:text="查看详情"
                android:textColor="#FF3E5FA1"
                android:textSize="@dimen/m20"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:background="@drawable/activity_item_detail_button_bg" />
        </LinearLayout>

    </LinearLayout>

</RelativeLayout>