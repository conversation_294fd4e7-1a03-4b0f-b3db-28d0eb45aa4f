package com.kaolafm.kradio.aop;

import androidx.constraintlayout.widget.ConstraintSet;
import androidx.constraintlayout.widget.Guideline;
import android.util.Log;

import com.kaolafm.kradio.lib.toast.SuperToast;
import com.kaolafm.kradio.lib.utils.IntentUtils;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;


@Aspect
public class GuideLineAop {

    private static final String TAG = "GuideLineAop";

    @After("execution(* SearchFragment.setGuideline(..))")
    public void setGuidelinebyBegin(JoinPoint point) throws Throwable {
        boolean isAppOnForeground = IntentUtils.getInstance().isAppOnForeground();
        try {
            Log.i(TAG, "setGuidelinebyBegin: ");
            boolean isLand = (boolean) point.getArgs()[2];
            ((ConstraintSet) point.getArgs()[0]).setGuidelineBegin(((Guideline) point.getArgs()[1]).getId(), 120);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
