package com.kaolafm.kradio.processor;

import com.google.auto.service.AutoService;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.annotation.processing.AbstractProcessor;
import javax.annotation.processing.Messager;
import javax.annotation.processing.ProcessingEnvironment;
import javax.annotation.processing.RoundEnvironment;
import javax.lang.model.SourceVersion;
import javax.lang.model.element.Element;
import javax.lang.model.element.TypeElement;
import javax.lang.model.util.Elements;
import javax.tools.Diagnostic.Kind;

/**
 * <AUTHOR>
 * @date 2019-08-13
 */
@AutoService(javax.annotation.processing.Processor.class)
public class CoreProcessor extends AbstractProcessor {

    public Elements elementUtils;

    public Messager messager;

    private List<IProcessor> mProcessors = new ArrayList<>();

    @Override
    public synchronized void init(ProcessingEnvironment processingEnvironment) {
        super.init(processingEnvironment);
        elementUtils = processingEnvironment.getElementUtils();
        messager = processingEnvironment.getMessager();
        mProcessors.add(new AppInitProcessor(this));
        mProcessors.add(new ModelInitProcessor(this));
        mProcessors.add(new ConstantProcessor(this));
        messager.printMessage(Kind.NOTE, "初始化");
    }

    @Override
    public boolean process(Set<? extends TypeElement> set, RoundEnvironment roundEnvironment) {
        messager.printMessage(Kind.NOTE, "处理set="+set);
        mProcessors.forEach(processor -> {
            Set<? extends Element> elements = roundEnvironment
                    .getElementsAnnotatedWith(elementUtils.getTypeElement(processor.getAnnotationName()));
            if (Util.isNotEmpty(elements)) {
                processor.process(elements);
            }
        });
        return true;
    }

    @Override
    public Set<String> getSupportedAnnotationTypes() {
        HashSet<String> supportAnnotationTypes = new HashSet<>();
        mProcessors.forEach(processor -> supportAnnotationTypes.add(processor.getAnnotationName()));
        return supportAnnotationTypes;
    }

    @Override
    public SourceVersion getSupportedSourceVersion() {
        return SourceVersion.latestSupported();
    }

    public ProcessingEnvironment getProcessingEnv() {
        return processingEnv;
    }

}
