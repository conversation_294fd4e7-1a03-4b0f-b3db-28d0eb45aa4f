<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    tools:background="@drawable/component_card_bg">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/card_pic_iv_rl"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_weight="1">

            <com.kaolafm.kradio.lib.widget.ratio.RatioConstraintLayout
                android:id="@+id/card_pic_cv"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                app:canScale="false"
                app:wh_ratio="1:1">

                <com.kaolafm.kradio.component.ui.base.view.OvalImageView
                    android:id="@+id/card_pic_iv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:oval_radius="@dimen/m8"
                    tools:src="@drawable/splash_yunting" />
            </com.kaolafm.kradio.lib.widget.ratio.RatioConstraintLayout>

            <ImageView
                android:id="@+id/vip_icon"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/m30"
                android:layout_alignLeft="@+id/card_pic_cv"
                android:layout_alignTop="@+id/card_pic_cv"
                android:adjustViewBounds="true"
                android:scaleType="fitStart"
                tools:src="@drawable/comprehensive_icon_vip" />

            <ImageView
                android:id="@+id/card_tag_iv"
                android:layout_width="@dimen/m60"
                android:layout_height="@dimen/m30"
                android:visibility="gone"
                tools:src="@drawable/icon_vip_home" />

            <ImageView
                android:id="@+id/card_play_iv"
                android:layout_width="@dimen/m38"
                android:layout_height="@dimen/m38"
                android:layout_alignEnd="@+id/card_pic_cv"
                android:layout_alignBottom="@+id/card_pic_cv"
                android:layout_marginRight="@dimen/m20"
                android:layout_marginBottom="@dimen/m20"
                android:src="@drawable/component_play_icon_2" />

            <com.kaolafm.kradio.component.ui.base.view.RateView
                android:id="@+id/card_layout_playing"
                android:layout_width="@dimen/m38"
                android:layout_height="@dimen/m38"
                android:layout_alignEnd="@+id/card_pic_cv"
                android:layout_alignBottom="@+id/card_pic_cv"
                android:layout_marginRight="@dimen/m20"
                android:layout_marginBottom="@dimen/m20"
                app:lottie_autoPlay="true"
                app:lottie_fileName="lottie/rate.json"
                app:lottie_loop="true" />
        </RelativeLayout>


        <TextView
            android:id="@+id/card_title_tv"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/m41"
            android:layout_below="@+id/card_pic_iv_rl"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/m6"
            android:ellipsize="end"
            android:maxWidth="@dimen/m196"
            android:maxLines="2"
            android:text="我是标题啊我是标题啊我是标题啊我是标题啊我是标题啊我是标题啊我是标题啊我是标题啊"
            android:textColor="@color/text_color_7"
            android:textSize="@dimen/m28" />

    </LinearLayout>
</RelativeLayout>