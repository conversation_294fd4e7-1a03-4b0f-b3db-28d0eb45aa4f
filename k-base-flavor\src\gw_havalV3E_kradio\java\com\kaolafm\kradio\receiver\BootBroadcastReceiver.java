package com.kaolafm.kradio.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.kaolafm.kradio.auto.appwidget.WidgetService;
import com.kaolafm.opensdk.player.logic.PlayerManager;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-05-28 12:20
 ******************************************/
public class BootBroadcastReceiver extends BroadcastReceiver {
    private static final String TAG = "BootBroadcastReceiver";

    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        Log.i(TAG, "onReceive:" + action);
        if ("aptiv.intent.action.stage.first".equals(action)) {
            PlayerManager.getInstance().init(context.getApplicationContext());
            Intent tempIntent = new Intent(context, WidgetService.class);
            tempIntent.setAction(WidgetService.WIDGET_ACTION_REFRESH);
            context.startService(tempIntent);
        }
    }
}
