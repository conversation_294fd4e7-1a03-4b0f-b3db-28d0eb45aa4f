package com.kaolafm.kradio.flavor.impl;

import android.app.Application;
import android.content.Intent;
import android.hardware.bydauto.AbsBYDAutoDevice;
import android.hardware.bydauto.audio.BYDAutoAudioDevice;
import android.hardware.bydauto.bodywork.AbsBYDAutoBodyworkListener;
import android.hardware.bydauto.bodywork.BYDAutoBodyworkDevice;
import android.hardware.bydauto.instrument.BYDAutoInstrumentDevice;
import android.util.Log;

import com.kaolafm.kradio.byd.BydSpeechCtlManager;
import com.kaolafm.kradio.common.utils.TimeUtil;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.SyncInstrumentInter;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayerStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerListenerHelper;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.concurrent.CopyOnWriteArrayList;

public class SyncInstrumentImpl implements SyncInstrumentInter {
    private static final String TAG = "SyncInstrumentImpl";
    private BYDAutoInstrumentDevice mBYDAutoInstrumentDevice;
    private BYDAutoAudioDevice mBYDAutoAudioDevice;
    private final int MUSIC_SOURCE_YUNTING = 14;
    public static boolean isFormRelease = false;
    public static boolean isFormShutDown = false;
    private YuntingBydAutoBodyListener autoBodyListener;
    private BYDAutoBodyworkDevice autoBodyworkDevice;
    private static final int OFF = 0, ACC = 1, ON = 2, OK = 3;

    private void initPlayer() {
        PlayerManager playerManager = PlayerManager.getInstance();
        boolean isInitSuccess = playerManager.isPlayerInitSuccess();
        if (isInitSuccess) {
            try {
                playerManager.addPlayControlStateCallback(basePlayStateListener);
                Log.i(TAG, "initPlayer init mBYDAutoInstrumentDevice and mBYDAutoAudioDevice ");
                mBYDAutoInstrumentDevice = BYDAutoInstrumentDevice.getInstance(AppDelegate.getInstance().getContext());
                mBYDAutoAudioDevice = BYDAutoAudioDevice.getInstance(AppDelegate.getInstance().getContext());
                Log.i(TAG, "initPlayer mBYDAutoInstrumentDevice is null " + (mBYDAutoInstrumentDevice == null) + " ,mBYDAutoAudioDevice is null " + (mBYDAutoAudioDevice == null));
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            playerManager.addPlayerInitComplete(onPlayerInitCompleteListener);
        }
    }

    @Override
    public boolean initSyncInstrument() {
        Log.i(TAG, "initSyncInstrument isFormRelease " + isFormRelease+ " ,this " + this);
        //初始化注册监听接口
        isFormRelease = false;
        initPlayer();
        PlayerManager.getInstance().addAudioFocusListener(onAudioFocusChangeInter);
        try {
            // 添加系统关机监听，解决熄火后仪表播放状态没有更新的问题
            BYDAutoBodyworkDevice autoBodyworkDevice = BYDAutoBodyworkDevice.getInstance(AppDelegate.getInstance().getContext());
            if (autoBodyworkDevice != null && autoBodyListener == null) {
                autoBodyListener = new YuntingBydAutoBodyListener();
                autoBodyworkDevice.registerListener(autoBodyListener);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        updateAppIcon();
        //todo 语音控制深度定制（功能开发中，为了避免影响当前版本，需要开发完成后打开）
//        BydSpeechCtlManager.getInstance().registerSpeechCtlReceiver(AppDelegate.getInstance().getContext());//注册语音控制回调消息
//        requestBydMusicControl();//申请加入语音默认应用列表
        return true;
    }

    private void requestBydMusicControl() {

        String action = "com.byd.action.FUNCTION_UPDATE";
        String EXTRA_PACKAGE_NAME = AppDelegate.getInstance().getContext().getPackageName(); //应用包名
        String EXTRA_APPLICATION_TYPE = "MUSIC"; //"MUSIC" / "NETRADIO" / "VIDEO"//应用类型
        String ISCONTROL = "TRUE"; //"TRUE"/"FALSE"; //应用是否支持智能语音控制，true支持，false不支持
        Intent intent = new Intent(action);
        intent.putExtra("EXTRA_PACKAGE_NAME", EXTRA_PACKAGE_NAME);//发给智能语音的定向广播intent.putExtra("EXTRA_RESULT_ACTION","com.byd.action.AUTOVOICE_UNBOOK"); intent.putExtra("RESULT_CODE","3"); intent.putExtra("RESULT_MESSAGE","当前场景不支持该功能");sendBroadcast(intent);
        intent.putExtra("EXTRA_APPLICATION_TYPE", EXTRA_APPLICATION_TYPE);//类型为字符串
        intent.putExtra("ISCONTROL", ISCONTROL);//类型为字符串
        AppDelegate.getInstance().getContext().sendBroadcast(intent);
    }

    private void updateAppIcon() {
        try {
            // Step1. 更新资源
            Class<?> classes = Application.class;
            Method method = classes.getMethod("updateResources");
            method.invoke(AppDelegate.getInstance().getContext());
            Log.i(TAG, "updateAppIcon success!");
        } catch (Exception e) {
            e.printStackTrace();
            Log.i(TAG, "updateAppIcon error:" + e.toString());
        }
    }

    private class YuntingBydAutoBodyListener extends AbsBYDAutoBodyworkListener {
        @Override
        public void onPowerLevelChanged(int level) {
            super.onPowerLevelChanged(level);
            //level (0:OFF, 1:ACC, 2:ON, 3:OK)
            Log.i(TAG, "YuntingBydAutoBodyListener onPowerLevelChanged:" + level);
            switch (level) {
                case OFF:
                    releaseSyncInstrument();
                    break;
                case ON:
                    break;
                case ACC:
                    break;
                default:
                    break;
            }
        }
    }


    @Override
    public boolean releaseSyncInstrument() {
        //反注册监听接口
        if (mBYDAutoInstrumentDevice == null) {
            Application context = AppDelegate.getInstance().getContext();
            if (context != null) {
                try {
                    isFormRelease = true;
                    mBYDAutoInstrumentDevice = BYDAutoInstrumentDevice.getInstance(context);
                    if (mBYDAutoInstrumentDevice != null) {
                        Log.i(TAG, "releaseSyncInstrument: BYDAutoInstrumentDevice.MUSIC_STOP");
                        mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_STOP);
                    } else {
                        Log.i(TAG, "releaseSyncInstrument: BYDAutoInstrumentDevice is null");
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } else {
            Log.i(TAG, "releaseSyncInstrument: BYDAutoInstrumentDevice.MUSIC_STOP");
            isFormRelease = true;
            mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_STOP);
        }

        if (autoBodyworkDevice != null) {
            autoBodyworkDevice.unregisterListener(autoBodyListener);
            autoBodyListener = null;
        }
        Log.i(TAG, "releaseSyncInstrument: isFormRelease = " + isFormRelease);
        Log.i(TAG, "releaseSyncInstrument: isFormShutDown = " + isFormShutDown);
        PlayerManager.getInstance().removePlayControlStateCallback(basePlayStateListener);
        PlayerManager.getInstance().removeAudioFocusListener(onAudioFocusChangeInter);
        return true;
    }

//    private void logSize(PlayerManager instance, String des) {
//        try {
//            Field mPlayerListenerHelper = instance.getClass().getDeclaredField("mPlayerListenerHelper");
//            mPlayerListenerHelper.setAccessible(true);
//            PlayerListenerHelper playerListenerHelper = (PlayerListenerHelper) mPlayerListenerHelper.get(instance);
//            Field listeners = playerListenerHelper.getClass().getDeclaredField("mPlayControlListenerArrayList");
//            listeners.setAccessible(true);
//            CopyOnWriteArrayList<IPlayerStateListener> mPlayControlListenerArrayList = (CopyOnWriteArrayList<IPlayerStateListener>) listeners.get(playerListenerHelper);
//            Log.d(TAG, des + " logSize " + mPlayControlListenerArrayList.size() + "--" + (mPlayControlListenerArrayList.indexOf(basePlayStateListener)));
//            for (int i = 0; i < mPlayControlListenerArrayList.size(); i++) {
//                Log.d(TAG, " listener == " + mPlayControlListenerArrayList.get(i));
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }

    OnAudioFocusChangeInter onAudioFocusChangeInter = new OnAudioFocusChangeInter() {
        @Override
        public void onAudioFocusChange(int i) {
            Log.i(TAG, "onAudioFocusChange: AudioFocus = " + i);
            if (i < 0) {
                try {
                    if (mBYDAutoInstrumentDevice != null) {
                        Log.i(TAG, "onAudioFocusChange: BYDAutoInstrumentDevice.MUSIC_STOP");
                        mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_STOP);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else {
                try {
                    if (mBYDAutoInstrumentDevice != null) {
                        Log.i(TAG, "onAudioFocusChange: BYDAutoInstrumentDevice.MUSIC_PAUSE");
                        mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_PAUSE);
                        mBYDAutoInstrumentDevice.sendMusicSource(MUSIC_SOURCE_YUNTING);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    };

    private IPlayerInitCompleteListener onPlayerInitCompleteListener = new IPlayerInitCompleteListener() {
        @Override
        public void onPlayerInitComplete(boolean b) {
            Log.i(TAG, "onPlayerInitComplete init " + isFormRelease + ", " + isFormShutDown);
            try {
                PlayerManager.getInstance().removePlayerInitComplete(onPlayerInitCompleteListener);
                PlayerManager.getInstance().addPlayControlStateCallback(basePlayStateListener);
                Log.i(TAG, "onPlayerInitComplete init mBYDAutoInstrumentDevice");
                mBYDAutoInstrumentDevice = BYDAutoInstrumentDevice.getInstance(AppDelegate.getInstance().getContext());
                Log.i(TAG, "onPlayerInitComplete init mBYDAutoInstrumentDevice result " + (mBYDAutoInstrumentDevice == null));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    };


    private BasePlayStateListener basePlayStateListener = new BasePlayStateListener() {

        @Override
        public void onPlayerPreparing(PlayItem playItem) {
            try {
                Log.i(TAG, "onPlayerPreparing: playItem.getTitle() = " + playItem.getTitle());
                if (mBYDAutoInstrumentDevice != null) {
                    mBYDAutoInstrumentDevice.sendMusicName(playItem.getTitle());
                    mBYDAutoInstrumentDevice.sendMusicPlaybackProgress(0);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onPlayerPlaying(PlayItem playItem) {
            try {
                Log.i(TAG, "onPlayerPlaying: playItem.getTitle() = " + playItem.getTitle());
                Log.i(TAG, "onPlayerPlaying: BYDAutoInstrumentDevice.MUSIC_PLAY ");
                if (mBYDAutoInstrumentDevice != null) {
                    mBYDAutoInstrumentDevice.sendMusicName(playItem.getTitle());
                    mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_PLAY);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onPlayerPaused(PlayItem playItem) {
            Log.i(TAG, "onPlayerPaused isFormRelease : " + isFormRelease);
            if (isFormRelease) {
                return;
            }
            try {
                if (PlayerManager.getInstance().getCurrentAudioFocusStatus() < 0) {
                    Log.i(TAG, "onPlayerPaused: isFormRelease = " + isFormRelease + " , mBYDAutoInstrumentDevice is null ： " + (mBYDAutoInstrumentDevice == null));
                    if (!isFormRelease && mBYDAutoInstrumentDevice != null) {
                        Log.i(TAG, "onPlayerPaused: BYDAutoInstrumentDevice.MUSIC_STOP ");
                        mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_STOP);
                        return;
                    }
                    isFormRelease = false;
                } else {
                    if (isFormShutDown) {
                        if (mBYDAutoInstrumentDevice != null) {
                            Log.i(TAG, "onPlayerPaused: BYDAutoInstrumentDevice.MUSIC_STOP ");
                            mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_STOP);
                        }
                        isFormShutDown = false;
                    } else {
                        if (mBYDAutoInstrumentDevice != null) {
                            Log.i(TAG, "onPlayerPaused: BYDAutoInstrumentDevice.MUSIC_PAUSE ");
                            mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_PAUSE);
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onProgress(PlayItem playItem, long progress, long total) {
            try {
                Log.i(TAG, "onProgress: " + progress / 1000);
                if (mBYDAutoInstrumentDevice != null) {
                    mBYDAutoInstrumentDevice.sendMusicPlaybackProgress((int) progress / 1000);
                }

                if (mBYDAutoAudioDevice != null) {
                    mBYDAutoAudioDevice.sendAudioPlayingTime(
                            TimeUtil.getHours((int) progress),
                            TimeUtil.getMinutes((int) progress),
                            TimeUtil.getSeconds((int) progress));

                    mBYDAutoAudioDevice.sendAudioTotalTime(
                            TimeUtil.getHours((int) total),
                            TimeUtil.getMinutes((int) total),
                            TimeUtil.getSeconds((int) total));
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onPlayerFailed(PlayItem playItem, int i, int i1) {
            try {
                Log.i(TAG, "onPlayerFailed: BYDAutoInstrumentDevice.MUSIC_PAUSE " + i + " , " + i1);
                if (mBYDAutoInstrumentDevice != null)
                    mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_PAUSE);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onPlayerEnd(PlayItem playItem) {
            try {
                Log.i(TAG, "onPlayerEnd: BYDAutoInstrumentDevice.MUSIC_PAUSE");
                if (mBYDAutoInstrumentDevice != null) {
                    mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_PAUSE);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    };
}