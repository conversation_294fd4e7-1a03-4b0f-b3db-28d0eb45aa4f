<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/playerbar_bg">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_toRightOf="@id/nav_home_main_layout"
        android:orientation="horizontal">
        <!--播放按钮-->
        <LinearLayout
            android:id="@+id/playerbar_live_rl"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:background="@color/playerbar_button_background"
            android:orientation="horizontal"
            android:visibility="gone">

            <ImageView
                android:id="@+id/player_bar_live_playorPause_iv"
                android:layout_width="@dimen/m80"
                android:layout_height="@dimen/m80"
                android:layout_gravity="center"
                android:layout_marginLeft="@dimen/x60"
                android:layout_marginRight="@dimen/x60"
                android:layout_weight="1"
                android:background="@drawable/color_main_button_click_selector"
                android:padding="@dimen/m20"
                android:scaleType="centerCrop"
                app:srcCompat="@drawable/ic_player_bar_pause_normal" />

            <ImageView
                android:id="@+id/player_bar_live_iv"
                android:layout_width="@dimen/m80"
                android:layout_height="@dimen/m80"
                android:layout_gravity="center"
                android:layout_marginRight="@dimen/x60"
                android:layout_weight="1"
                android:background="@drawable/color_main_button_click_selector"
                android:padding="@dimen/m20"
                android:scaleType="centerCrop"
                app:srcCompat="@drawable/player_bar_live" />
        </LinearLayout>
        <!--直播布局按钮-->
        <RelativeLayout
            android:id="@+id/player_bar_play_rl"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_toLeftOf="@id/player_bar_constrantlayout_rl"
            android:background="@color/playerbar_button_background">

            <fr.castorflex.android.circularprogressbar.CircularProgressBar
                android:id="@+id/player_bar_loading"
                style="@style/CustomerCircularProgressBar"
                android:layout_width="@dimen/m80"
                android:layout_height="@dimen/m80"
                android:layout_alignLeft="@+id/player_bar_play_ll"
                android:layout_alignTop="@+id/player_bar_play_ll"
                android:layout_alignRight="@+id/player_bar_play_ll"
                android:layout_alignBottom="@+id/player_bar_play_ll"
                android:visibility="invisible"
                app:cpb_color="@color/circular_progress_color"
                app:cpb_stroke_width="@dimen/loading_progress_width" />


            <LinearLayout
                android:id="@+id/player_bar_home_ll"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true">

                <ImageView
                    android:id="@+id/player_bar_home"
                    android:layout_width="@dimen/m80"
                    android:layout_height="@dimen/m80"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="@dimen/x60"
                    android:background="@drawable/color_main_button_click_selector"
                    android:scaleType="centerCrop"
                    app:srcCompat="@drawable/icon_home" />

            </LinearLayout>


            <LinearLayout
                android:id="@+id/player_bar_play_ll"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toRightOf="@id/player_bar_home_ll"
                android:layout_centerVertical="true">

                <ImageView
                    android:id="@+id/player_bar_play"
                    android:layout_width="@dimen/m80"
                    android:layout_height="@dimen/m80"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="@dimen/x60"
                    android:layout_marginRight="@dimen/x60"
                    android:background="@drawable/color_main_button_click_selector"
                    android:padding="@dimen/m20"
                    android:scaleType="centerCrop"
                    app:srcCompat="@drawable/playerbar_play" />
            </LinearLayout>




            <!--为了兼容 直播 ，广播状态下，播放前一个按钮的出现和隐藏，引发位置变动-->
            <LinearLayout
                android:id="@+id/rl_previous_next"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_centerInParent="true"
                android:layout_toRightOf="@id/player_bar_play_ll">

                <ImageView
                    android:id="@+id/player_bar_previous"
                    android:layout_width="@dimen/m80"
                    android:layout_height="@dimen/m80"
                    android:layout_gravity="center"
                    android:layout_marginRight="@dimen/x60"
                    android:background="@drawable/color_main_button_click_selector"
                    android:padding="@dimen/m20"
                    android:scaleType="centerCrop"
                    android:visibility="gone"
                    app:srcCompat="@drawable/playerbar_prev" />

                <ImageView
                    android:id="@+id/player_bar_next"
                    android:layout_width="@dimen/m80"
                    android:layout_height="@dimen/m80"
                    android:layout_gravity="center"
                    android:layout_marginRight="@dimen/x60"
                    android:background="@drawable/color_main_button_click_selector"
                    android:padding="@dimen/m20"
                    android:scaleType="centerCrop"
                    app:srcCompat="@drawable/playerbar_next" />

                <ImageView
                    android:id="@+id/player_bar_collect"
                    android:layout_width="@dimen/m80"
                    android:layout_height="@dimen/m80"
                    android:layout_gravity="center"
                    android:layout_marginRight="@dimen/x60"
                    android:background="@drawable/color_main_button_click_selector"
                    android:padding="@dimen/m20"
                    android:scaleType="centerCrop"
                    android:visibility="gone"
                    app:srcCompat="@drawable/playerbar_collect" />

                <com.kaolafm.kradio.player.ui.horizontal.widget.AIRadioMinusFeedbackView
                    android:id="@+id/ai_radio_minus_feed_back_view"
                    android:layout_width="@dimen/m75"
                    android:layout_height="@dimen/m75"
                    android:layout_gravity="center"
                    android:layout_marginRight="@dimen/x65"
                    android:background="@drawable/color_main_button_click_selector"
                    android:visibility="gone"
                    app:minus_location="1" />
            </LinearLayout>

            <!--为了兼容 直播 ，广播状态下，播放前一个按钮的出现和隐藏，引发位置变动-->
            <fr.castorflex.android.circularprogressbar.CircularProgressBar xmlns:app="http://schemas.android.com/apk/res-auto"
                android:id="@+id/player_bar_loading"
                style="@style/CustomerCircularProgressBar"
                android:layout_width="@dimen/m80"
                android:layout_height="@dimen/m80"
                android:layout_alignLeft="@+id/player_bar_play"
                android:layout_alignTop="@+id/player_bar_play"
                android:layout_alignRight="@+id/player_bar_play"
                android:layout_alignBottom="@+id/player_bar_play"
                android:visibility="invisible"
                app:cpb_color="@color/circular_progress_color"
                app:cpb_stroke_width="@dimen/loading_progress_width" />

            <!--<ImageView-->
            <!--android:id="@+id/player_bar_collect"-->
            <!--android:layout_width="@dimen/m80"-->
            <!--android:layout_height="@dimen/m80"-->
            <!--android:layout_centerVertical="true"-->
            <!--android:layout_marginRight="@dimen/x60"-->
            <!--android:layout_toRightOf="@id/rl_previous_next"-->
            <!--android:background="@drawable/color_main_button_click_selector"-->
            <!--android:padding="@dimen/m20"-->
            <!--android:scaleType="centerCrop"-->
            <!--app:srcCompat="@drawable/playerbar_collect" />-->

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/player_bar_constrantlayout_rl"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <com.kaolafm.kradio.common.widget.GradientProgressBar
                android:id="@+id/player_bar_progress"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:progressDrawable="@drawable/player_bar_progress_bg" />

            <com.kaolafm.kradio.comprehensiveCommon.home.widget.NoSkinImageView
                android:id="@+id/player_bar_cover"
                android:layout_width="@dimen/player_bar_audio_cover_size"
                android:layout_height="@dimen/player_bar_audio_cover_size"
                android:layout_centerVertical="true"
                android:layout_marginLeft="@dimen/x20"
                android:scaleType="fitXY"
                app:srcCompat="@drawable/media_default_pic" />

            <!--文字和标题-->
            <TextView
                android:id="@+id/player_bar_live_flag"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/y25"
                android:layout_alignLeft="@+id/player_bar_cover"
                android:layout_alignTop="@+id/player_bar_cover"
                android:background="@drawable/flag_live_bg"
                android:gravity="center"
                android:paddingLeft="@dimen/x10"
                android:paddingTop="@dimen/y3"
                android:paddingRight="@dimen/x10"
                android:paddingBottom="@dimen/y5"
                android:text="@string/live"
                android:textColor="@color/home_bar_player_living_text_color"
                android:textSize="@dimen/text_size_ext_1"
                android:visibility="gone" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_toRightOf="@id/player_bar_cover"
                android:gravity="center_vertical"
                android:orientation="vertical">

                <include layout="@layout/bar_player_horizontal_inner" />
            </LinearLayout>

        </RelativeLayout>
    </LinearLayout>

    <ViewStub
        android:id="@id/nav_home_viewStub"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_centerVertical="true"
        android:layout="@layout/home_bar_layout" />
</RelativeLayout>
