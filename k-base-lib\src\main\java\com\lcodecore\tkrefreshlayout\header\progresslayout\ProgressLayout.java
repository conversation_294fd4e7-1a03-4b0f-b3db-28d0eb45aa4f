//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//

package com.lcodecore.tkrefreshlayout.header.progresslayout;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.FrameLayout.LayoutParams;

import androidx.annotation.ColorInt;
import androidx.annotation.ColorRes;
import androidx.core.view.ViewCompat;

import com.lcodecore.tkrefreshlayout.IHeaderView;
import com.lcodecore.tkrefreshlayout.OnAnimEndListener;

public class ProgressLayout extends FrameLayout implements IHeaderView {
    private int mCircleWidth;
    private int mCircleHeight;
    private static final int CIRCLE_DIAMETER = 40;
    private static final int CIRCLE_DIAMETER_LARGE = 56;
    public static final int LARGE = 0;
    public static final int DEFAULT = 1;
    private static final int CIRCLE_BG_LIGHT = -328966;
    private static final int DEFAULT_CIRCLE_TARGET = 64;
    private static final float MAX_PROGRESS_ANGLE = 0.8F;
    private static final int MAX_ALPHA = 255;
    private static final int STARTING_PROGRESS_ALPHA = 76;
    private CircleImageView mCircleView;
    private MaterialProgressDrawable mProgress;
    private boolean mIsBeingDragged;

    public ProgressLayout(Context context) {
        this(context, (AttributeSet)null);
    }

    public ProgressLayout(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ProgressLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.mIsBeingDragged = false;
        DisplayMetrics metrics = this.getResources().getDisplayMetrics();
        this.mCircleWidth = (int)(40.0F * metrics.density);
        this.mCircleHeight = (int)(40.0F * metrics.density);
        this.createProgressView();
        ViewCompat.setChildrenDrawingOrderEnabled(this, true);
    }

    private void createProgressView() {
        this.mCircleView = new CircleImageView(this.getContext(), -328966, 20.0F);
        this.mProgress = new MaterialProgressDrawable(this.getContext(), this);
        this.mProgress.setBackgroundColor(-328966);
        this.mCircleView.setImageDrawable(this.mProgress);
        this.mCircleView.setVisibility(8);
        LayoutParams params = new LayoutParams(-2, -2, 17);
        this.mCircleView.setLayoutParams(params);
        this.addView(this.mCircleView);
    }

    public void showArrow(boolean show) {
        this.mProgress.showArrow(show);
    }

    public void setProgressBackgroundColorSchemeResource(@ColorRes int colorRes) {
        this.setProgressBackgroundColorSchemeColor(this.getResources().getColor(colorRes));
    }

    public void setProgressBackgroundColorSchemeColor(@ColorInt int color) {
        this.mCircleView.setBackgroundColor(color);
        this.mProgress.setBackgroundColor(color);
    }

    public void setColorSchemeResources(@ColorRes int... colorResIds) {
        Resources res = this.getResources();
        int[] colorRes = new int[colorResIds.length];

        for(int i = 0; i < colorResIds.length; ++i) {
            colorRes[i] = res.getColor(colorResIds[i]);
        }

        this.setColorSchemeColors(colorRes);
    }

    public void setColorSchemeColors(int... colors) {
        this.mProgress.setColorSchemeColors(colors);
    }

    public void setSize(int size) {
        if (size == 0 || size == 1) {
            DisplayMetrics metrics = this.getResources().getDisplayMetrics();
            if (size == 0) {
                this.mCircleHeight = this.mCircleWidth = (int)(56.0F * metrics.density);
            } else {
                this.mCircleHeight = this.mCircleWidth = (int)(40.0F * metrics.density);
            }

            this.mCircleView.setImageDrawable((Drawable)null);
            this.mProgress.updateSizes(size);
            this.mCircleView.setImageDrawable(this.mProgress);
        }
    }

    public void reset() {
        this.mCircleView.clearAnimation();
        this.mProgress.stop();
        this.mCircleView.setVisibility(8);
        this.mCircleView.getBackground().setAlpha(255);
        this.mProgress.setAlpha(255);
        ViewCompat.setScaleX(this.mCircleView, 0.0F);
        ViewCompat.setScaleY(this.mCircleView, 0.0F);
        ViewCompat.setAlpha(this.mCircleView, 1.0F);
    }

    public View getView() {
        return this;
    }

    public void onPullingDown(float fraction, float maxHeadHeight, float headHeight) {
        if (!this.mIsBeingDragged) {
            this.mIsBeingDragged = true;
            this.mProgress.setAlpha(76);
        }

        this.mProgress.setIsBeingDragged(this.mIsBeingDragged);
        if (this.mCircleView.getVisibility() != 0) {
            this.mCircleView.setVisibility(0);
        }

        if (fraction >= 1.0F) {
            ViewCompat.setScaleX(this.mCircleView, 1.0F);
            ViewCompat.setScaleY(this.mCircleView, 1.0F);
        } else {
            ViewCompat.setScaleX(this.mCircleView, fraction);
            ViewCompat.setScaleY(this.mCircleView, fraction);
        }

        if (fraction <= 1.0F) {
            this.mProgress.setAlpha((int)(76.0F + 179.0F * fraction));
        }

        float adjustedPercent = (float)Math.max((double)fraction - 0.4D, 0.0D) * 5.0F / 3.0F;
        float strokeStart = adjustedPercent * 0.8F;
        this.mProgress.setStartEndTrim(0.0F, Math.min(0.8F, strokeStart));
        this.mProgress.setArrowScale(Math.min(1.0F, adjustedPercent));
        float rotation = (-0.25F + 0.4F * adjustedPercent) * 0.5F;
        this.mProgress.setProgressRotation(rotation);
        this.showArrow(true);
    }

    public void onPullReleasing(float fraction, float maxHeadHeight, float headHeight) {
        this.mIsBeingDragged = false;
        this.mProgress.setIsBeingDragged(this.mIsBeingDragged);
        if (fraction >= 1.0F) {
            ViewCompat.setScaleX(this.mCircleView, 1.0F);
            ViewCompat.setScaleY(this.mCircleView, 1.0F);
        } else {
            ViewCompat.setScaleX(this.mCircleView, fraction);
            ViewCompat.setScaleY(this.mCircleView, fraction);
        }

        this.showArrow(false);
    }

    public void startAnim(float maxHeadHeight, float headHeight) {
        this.mCircleView.setVisibility(0);
        this.mCircleView.getBackground().setAlpha(255);
        this.mProgress.setAlpha(255);
        ViewCompat.setScaleX(this.mCircleView, 1.0F);
        ViewCompat.setScaleY(this.mCircleView, 1.0F);
        this.mProgress.setArrowScale(1.0F);
        this.mProgress.start();
    }

    public void onFinish(final OnAnimEndListener animEndListener) {
        this.mCircleView.animate().scaleX(0.0F).scaleY(0.0F).alpha(0.0F).setListener(new AnimatorListenerAdapter() {
            public void onAnimationEnd(Animator animation) {
                ProgressLayout.this.reset();
                animEndListener.onAnimEnd();
            }
        }).start();
    }
}
