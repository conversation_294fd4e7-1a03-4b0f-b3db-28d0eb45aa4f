<?xml version="1.0" encoding="utf-8"?>
<com.kaolafm.kradio.lib.widget.square.SquareHeightFrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/category_item_broadcast_local_bg">

        <ViewStub
            android:id="@+id/vs_layout_playing"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout="@layout/layout_playing_square_item" />

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center">


            <ImageView
                android:id="@+id/twoLine"
                android:layout_width="@dimen/y24"
                android:layout_height="wrap_content"
                android:layout_alignTop="@+id/layoutFm"
                android:layout_alignBottom="@+id/layoutFm"
                android:layout_centerHorizontal="true"
                android:src="@drawable/two_line"
                android:visibility="gone" />

            <LinearLayout
                android:id="@+id/layoutFm"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:gravity="center_horizontal"
                android:orientation="horizontal"
                android:paddingLeft="@dimen/x10"
                android:paddingRight="@dimen/x10">

                <TextView
                    android:id="@+id/tv_broadcast_frequency_type"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:lines="1"
                    android:singleLine="true"
                    tools:text="FM"
                    android:textColor="@color/category_item_broadcast_local_title_text_color"
                    android:textSize="@dimen/text_size0" />

                <TextView
                    android:id="@+id/tv_broadcast_frequency"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/x6"
                    android:ellipsize="end"
                    android:lines="1"
                    android:singleLine="true"
                    android:textColor="@color/category_item_broadcast_local_title_text_color"
                    android:textSize="@dimen/text_size3"
                    tools:text="97.4" />
            </LinearLayout>

            <TextView
                android:id="@+id/tv_broadcast_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/layoutFm"
                android:layout_marginTop="@dimen/y10"
                android:lines="1"
                android:paddingLeft="@dimen/x10"
                android:paddingRight="@dimen/x10"
                android:singleLine="true"
                tools:text="北京交通广播"
                android:textColor="@color/category_item_broadcast_local_subtitle_text_color"
                android:textSize="@dimen/text_size1" />

        </RelativeLayout>


    </RelativeLayout>


</com.kaolafm.kradio.lib.widget.square.SquareHeightFrameLayout>
