package com.kaolafm.kradio.flavor.impl;

import android.bluetooth.BluetoothClass;
import android.bluetooth.BluetoothDevice;
import android.content.Intent;
import android.util.Log;

import com.kaolafm.kradio.lib.base.flavor.KRadioAclDisConnectedInter;

public class KRadioAclDisConnectedImpl implements KRadioAclDisConnectedInter {

    private static final String TAG = "KRadioAclDisConnectedImpl";

    @Override
    public boolean doAclDisConnected(Object... args) {
        //获取断开设备的类型
        if (args != null) {
            Intent intent = (Intent) args[0];
            BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
            if (device != null) {
                BluetoothClass bluetoothClass = device.getBluetoothClass();
                int type = bluetoothClass.getMajorDeviceClass();
                Log.i(TAG, "majorDeviceClass:" + type);
                if (type == BluetoothClass.Device.Major.AUDIO_VIDEO) {
                    //判断设备类型是AUDIO_VIDEO时返回false，为需要暂停播放
                    return false;
                }else {
                    //判断设备类型非AUDIO_VIDEO时返回true，不需要暂停播放
                    return true;
                }
            }
        }
        return false;
    }
}