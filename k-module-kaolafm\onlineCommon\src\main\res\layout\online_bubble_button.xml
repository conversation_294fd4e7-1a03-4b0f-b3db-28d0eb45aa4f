<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="0dp"
    android:layout_weight="1"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="horizontal"
    android:paddingTop="@dimen/y15"
    android:paddingBottom="@dimen/y30">

    <!--    android:layout_width="0dp"-->
    <!--    android:layout_weight="1"-->

    <ImageView
        android:id="@+id/messageBubbleIconIv"
        android:layout_width="@dimen/x28"
        android:layout_height="@dimen/y28"
        tools:src="@drawable/online_search_icon_delete" />

    <TextView
        android:id="@+id/messageBubbleTextTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/online_bubble_btn_text_color"
        android:textSize="@dimen/m24"
        tools:text="重播" />

    <TextView
        android:id="@+id/messageBubbleTimerTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/x8"
        android:textColor="@color/online_bubble_timer_color"
        android:textSize="@dimen/m22"
        tools:text="(20s)" />
</LinearLayout>