//package com.kaolafm.kradio.flavor.impl;
//
//import android.Manifest;
//import android.content.Context;
//import android.content.pm.PackageManager;
//import android.os.Build;
//import android.os.Environment;
//import android.os.StatFs;
//import android.support.annotation.RequiresApi;
//import android.util.Log;
//
//import com.kaolafm.kradio.lib.base.flavor.KRadioFreeSpace;
//import com.kaolafm.kradio.common.utils.DiskUtil;
//
//import java.io.File;
//import java.io.FileNotFoundException;
//import java.io.FileOutputStream;
//import java.io.IOException;
//
//
///**
// * The type K radio free space.
// * 解决比亚迪车机在剩余空间还有1g就不让app写入数据的问题，这里用尝试写一个5m文件的方法，对性能有一定影响，但是比亚迪车机性能都比较好，实测可以接受
// */
//public class KRadioFreeSpaceImpl implements KRadioFreeSpace {
//    @Override
//    public boolean checkFreeSpace(Object... args) {
//        Context ctx =  (Context) args[0];
//
//        int minimum = 1200; //比亚迪车机1000m以上禁止app写入数据
//        return DiskUtil.checkFreeSpaceDefault(minimum);
//    }
//
//
//    private boolean writeFileTest(Context ctx){
//        //读写权限检查，安装第一次启动时候不能进行文件写入磁盘空间测试
//        PackageManager pm = ctx.getPackageManager();
//        String pacName = ctx.getApplicationInfo().packageName;
//        boolean permission1 = (PackageManager.PERMISSION_GRANTED ==
//                pm.checkPermission(Manifest.permission.WRITE_EXTERNAL_STORAGE, pacName));
//
//        boolean permission2 = (PackageManager.PERMISSION_GRANTED ==
//                pm.checkPermission(Manifest.permission.READ_EXTERNAL_STORAGE, pacName));
//
//        if(!permission1 || !permission2){
//            Log.i("freeSpace","没有读写权限，不进行磁盘空间文件检查");
//            return true;
//        }
//
//        int minimum = 5; //要求sd卡最少可用空间已M为单位
//        int size = minimum * 1024 * 1024;
//        try {
//            byte[] buffer = new byte[size];
//            StringBuilder builder = new StringBuilder();
//            builder.append(new String(buffer, 0, size));
//
//            File fs = new File(Environment.getExternalStorageDirectory()+"/msc/" + "testDiskSpace.data");
//            FileOutputStream outputStream =new FileOutputStream(fs);
//            outputStream.write(buffer);
//            outputStream.flush();
//            outputStream.close();
//            return true;
//        } catch (FileNotFoundException e) {
//            e.printStackTrace();
//            return false;
//        } catch (IOException e) {
//            e.printStackTrace();
//            return false;
//        }
//    }
//
//}
package com.kaolafm.kradio.flavor.impl;

import android.Manifest;
import android.content.Context;

import com.kaolafm.kradio.common.utils.PermissionUtils;
import com.kaolafm.kradio.lib.base.flavor.KRadioFreeSpace;
import com.kaolafm.utils.DiskUtil;

public class KRadioFreeSpaceImpl implements KRadioFreeSpace {
    @Override
    public boolean checkFreeSpace(Object... args) {
//        Context ctx = (Context) args[0];
//        boolean isHasPermission = PermissionUtils.checkPermission(ctx, Manifest.permission.WRITE_EXTERNAL_STORAGE);
//        if (isHasPermission) {
//            return DiskUtil.checkFreeSpaceDefault(20); //20MB
//        } else {
            return true;
//        }
    }
}