package com.kaolafm.kradio.online.player.utils

import android.content.Context
import android.graphics.Rect
import android.view.View
import com.kaolafm.kradio.k_kaolafm.R

class VerticalSpaceDividerDecoration : RecyclerView.ItemDecoration {
    private var dividerHeight: Int = 1
    private lateinit var mContext: Context

    constructor(mContext: Context) {
        this.mContext = mContext
        dividerHeight = mContext.resources.getDimensionPixelSize(R.dimen.m2)
    }

    constructor(mContext: Context, dividerHeight: Int) {
        this.mContext = mContext
        this.dividerHeight = dividerHeight
    }


    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        val count = parent.adapter?.itemCount ?: 0
        super.getItemOffsets(outRect, view, parent, state)
        outRect.set(0, dividerHeight, 0, 0)
//        if (parent.indexOfChild(view) < count - 1) {
//            outRect.set(0, 0, 0, dividerHeight)
//        } else {
//            outRect.set(0, 0, 0, 0)
//        }
    }
}