package com.kaolafm.kradio.flavor.impl;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.kaolafm.kradio.flavor.common.SystemBootUtil;
import com.kaolafm.kradio.flavor.utils.PlayerUtil;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioPlayLogicInter;
import com.kaolafm.kradio.lib.utils.IntentUtils;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.player.core.listener.OnPlayLogicListener;
import com.kaolafm.opensdk.player.core.utils.PlayerCustomizeManager;
import com.kaolafm.opensdk.player.logic.PlayerManager;


/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-07-05 12:16
 ******************************************/
public final class KRadioAudioPlayLogicImpl implements KRadioAudioPlayLogicInter {
    private final static String TAG = "KRadioAudioPlayLogicImpl";

    public KRadioAudioPlayLogicImpl() {
        PlayerCustomizeManager.getInstance().setPlayLogicListener(new OnPlayLogicListener() {
            @SuppressLint("LongLogTag")
            @Override
            public boolean onPlayLogicDispose() {
                int currentFocus = PlayerManager.getInstance().getCurrentAudioFocusStatus();
                Logging.i(TAG, "isAppOnForeground--------->currentFocus = " + currentFocus);
                if ((currentFocus < 0)) {
                    boolean isAppOnForeground = AppDelegate.getInstance().isAppForeground();
                    Log.i(TAG, "isAppOnForeground--------->isAppOnForeground = " + isAppOnForeground);
                    return !isAppOnForeground;
                }
                return false;
            }
        });
    }

    @SuppressLint("LongLogTag")
    @Override
    public boolean autoPlayAudio(Object... args) {
        Context context = (Context) args[0];
        SystemBootUtil systemBootUtil = new SystemBootUtil();
        boolean flag = systemBootUtil.isFirstBoot(context);
        Log.i(TAG, "autoPlayAudio:   flag = " + flag);
        if (flag) {
            PlayerUtil.playDefaultMediaForChannel();
            systemBootUtil.updateFirstBoot(context, false);
            return true;
        }
        return false;
    }

    @Override
    public boolean requestAudioFocus(Object... args) {
        PlayerManager playerManager = PlayerManager.getInstance();
        boolean flag = false;
        if (playerManager.getCurrentAudioFocusStatus() < 1) {
            flag = playerManager.requestAudioFocus();
        }
        return flag;
    }

    @Override
    public boolean resumeAudioPlayLogic(Object... args) {
        Context context = (Context) args[0];
        boolean autoPlay = false;
        if (context instanceof Activity) {
            Intent intent = ((Activity) context).getIntent();
            // 解决https://app.huoban.com/tables/2100000007530121/items/2300001668075066?userId=1229522问题
            if (intent != null) {
                autoPlay = IntentUtils.getInstance().isAutoPlay(intent);
            }
        }
        recoverPlay(autoPlay);
        return true;
    }

    @SuppressLint("LongLogTag")
    @Override
    public boolean restartAudioPlayLogic(Object... args) {
        return false;
    }

    @SuppressLint("LongLogTag")
    private void recoverPlay(boolean autoPlay) {
        Log.i(TAG, "recoverPlay start");
        PlayerManager playerManager = PlayerManager.getInstance();
        playerManager.requestAudioFocus();
        boolean isPausedFromUser = playerManager.isPauseFromUser();
        Log.i(TAG, "recoverPlay isPausedFromUser = " + isPausedFromUser);
        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
            Log.i(TAG, "network is unavailable");
            return;
        }
        if (!isPausedFromUser || autoPlay) {
            boolean isPlaying = playerManager.isPlaying();
            Log.i(TAG, "recoverPlay isPlaying = " + isPlaying);
            if (!playerManager.isPlaying()) {
                PlayerManagerHelper.getInstance().switchPlayerStatus(false);
            }
        }
    }

    @SuppressLint("LongLogTag")
    @Override
    public boolean doStartInPlay(Object... args) {
        int audioFocus = PlayerManager.getInstance().getCurrentAudioFocusStatus();
        boolean isAppOnForeground = AppDelegate.getInstance().isAppForeground();
        Log.i(TAG, "doStartInPlay start focus = " + audioFocus + " isAppOnForeground = " + isAppOnForeground);
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001592906358?userId=1229522问题
        if (audioFocus < 0 && !isAppOnForeground) {
            return false;
        }
        if (!PlayerManager.getInstance().isPlaying()) {
            PlayerManagerHelper.getInstance().switchPlayerStatus(true);
        }
        return true;
    }
}