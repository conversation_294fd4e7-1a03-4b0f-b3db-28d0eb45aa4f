package com.kaolafm.kradio.user.setting;

import com.kaolafm.kradio.setting.SettingItem;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;

import java.util.List;

/**
 * 添加换肤功能
 *
 * <AUTHOR>
 * @date 2019-06-25
 */
@Aspect
public class SettingAspect {

    @Around("execution(* com.kaolafm.kradio.k_kaolafm.user.setting.SettingModel.getItemList(..))")
    public List<SettingItem> addChangeSkinItem(ProceedingJoinPoint point) throws Throwable {
        List<SettingItem> items = (List<SettingItem>) point.proceed();
        if (items != null) {
            for (int i = 0; i < items.size(); i++) {
                if (("意见反馈").equals(items.get(i).getName())) items.remove(i);
            }
        }
        return items;
    }
}
