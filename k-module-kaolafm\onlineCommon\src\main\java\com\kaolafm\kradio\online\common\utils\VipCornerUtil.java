package com.kaolafm.kradio.online.common.utils;

import android.view.View;
import android.widget.ImageView;

import com.kaolafm.kradio.k_kaolafm.R;

//精品或者vip角标util
public class VipCornerUtil {

    /**
     * 设置imageview的左上角显示的角标，显示精品或者vip
     * @param vipView
     * @param vip
     * @param fine
     */
    public static void setVipCorner(ImageView vipView, int vip, int fine) {
        setVipCorner(vipView, vip, fine, false);
    }

    /**
     * 设置imageview的左上角显示的角标，显示精品或者vip
     * @param vipView 需要设置的imageview
     * @param vip vip的值
     * @param fine 精品的值
     * @param isMin 是否显示小图片
     */
    public static void setVipCorner(ImageView vipView, int vip, int fine, boolean isMin) {
        if (vipView == null) {
            return;
        }
        int icon = 0;
        //fine==1代表精品
        if (1 == fine) {
//            icon = isMin? R.drawable.icon_supreme_min: R.drawable.icon_supreme;
            icon =  R.drawable.icon_supreme;
        } else if (1 == vip) {
//            icon = isMin? R.drawable.icon_vip_min: R.drawable.icon_vip;
            icon =  R.drawable.online_icon_vip;
        }

        if (icon != 0) {
            vipView.setVisibility(View.VISIBLE);
            vipView.setImageResource(icon);
        } else {
            vipView.setImageDrawable(null);
            vipView.setVisibility(View.INVISIBLE);
        }
    }
}
