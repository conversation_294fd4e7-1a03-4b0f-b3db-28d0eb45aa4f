package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.content.Context;
import android.os.Build;
import android.text.TextUtils;
import android.util.Log;

import com.ecarx.sdk.ECarXAPIBase;
import com.ecarx.sdk.device.DeviceAPI;
import com.ecarx.sdk.device.IEcarxDeviceIdWatcher;
import com.ecarx.sdk.openapi.ECarXApiClient;
import com.kaolafm.kradio.common.KradioSDKManager;
import com.kaolafm.kradio.common.http.vehicle.KlSdkVehicle;
import com.kaolafm.kradio.flavor.utils.DeviceInfoUtil;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.base.flavor.DeviceInfoSettingASync;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.report.ReportHelper;

import javax.security.auth.callback.Callback;

/**
 * author : wxb
 * date   : 2021/12/24
 * desc   :
 */
public class DeviceInfoSettingASyncImpl implements DeviceInfoSettingASync {
    String TAG = getClass().getSimpleName();

    /**
     * OpenAPI的Device模块
     */
    public static DeviceAPI mDeviceAPI;
    String deviceId = null;
    String ecarType = null;
    CallBack mCallBack;

    @Override
    public void setInfoForSDK(Context context, CallBack cb) {
        mCallBack = cb;
        Log.d(TAG, "setInfoForSDK");
        String carType = KlSdkVehicle.getInstance().getCarType();
        Log.d(TAG, "setInfoForSDK carType=" + carType);

        if (TextUtils.isEmpty(carType) || carType.equals(Build.DEVICE)) {
            try {
                mDeviceAPI = DeviceAPI.get(AppDelegate.getInstance().getContext());
                //异步初始化模块服务
                mDeviceAPI.init(AppDelegate.getInstance().getContext(), new ECarXApiClient.Callback() {
                    @Override
                    public void onAPIReady(boolean ready) {
                        Log.d(TAG, "onAPIReady=" + ready);
                        //客户端只需要根据ready来判断是否可以调用OpenAPI接口
                        if (ready) {
                            /**i
                             * 获取车型信息 Vehicle Type (类PCODE)
                             * @return 车型信息，比如 NL-3A
                             */
                            ecarType = DeviceInfoSettingASyncImpl.mDeviceAPI.getVehicleType();
                            saveDevices(context);
                            try {
                                Log.d(TAG, "setInfoForSDK init attachEcarxDeviceIdWatcher ECarXAPIBase.VERSION_INT=" + ECarXAPIBase.VERSION_INT);
                                if (ECarXAPIBase.VERSION_INT >= 335) {
                                    // 1. 创建EDID监听接口
                                    IEcarxDeviceIdWatcher watch = new IEcarxDeviceIdWatcher() {

                                        /**
                                         * 获取 EDID 回调
                                         * @param edid  ECARX Device ID
                                         *  @param code 返回编码，具体参考 {@link Code}
                                         *                         1.等于{@link IEcarxDeviceIdWatcher#CODE_SUCCESS} 代表返回EDID成功
                                         *                            2.等于{@link IEcarxDeviceIdWatcher#CODE_ERROR_PARAMS} 由于缺少必要的车辆基础信息导致EDID获取失败
                                         *                            3.等于{@link IEcarxDeviceIdWatcher#CODE_ERROR_PARAMS} 网络异常
                                         */
                                        public void onChanged(String edid, int code) {
                                            Log.d(TAG, "IEcarxDeviceIdWatcher edid=" + edid + "==code=" + code);
                                            if (code == CODE_SUCCESS) {
                                                deviceId = edid;
                                                saveDevices(context);
                                            }
                                        }
                                    };
                                    // 2. 注册监听
                                    mDeviceAPI.attachEcarxDeviceIdWatcher(watch);
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        } else {
                            mDeviceAPI = null;
                            AppManager.getInstance().appExit();
                        }
                    }
                });
                Log.d(TAG, "setInfoForSDK init  ECarXAPIBase.VERSION_INT=" + ECarXAPIBase.VERSION_INT);

            } catch (Throwable ignore) {
                //ignore
                ignore.printStackTrace();
                AppManager.getInstance().appExit();
            }
        } else {
            Log.d(TAG, "setInfoForSDK has");
            ((Activity) context).runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    cb.onCall(true);
                }
            });
        }
    }

    private void saveDevices(Context context) {
        Log.d(TAG, "setInfoForSDK: saveDevices==" + deviceId + ":===type====" + ecarType);
        if (TextUtils.isEmpty(deviceId) || TextUtils.isEmpty(ecarType)) {
            return;
        }
        Log.d(TAG, "setInfoForSDK: EDID==" + deviceId + ":===type====" + ecarType);
        DeviceInfoUtil.setDeviceIdAndCarType(deviceId, ecarType);
        ReportHelper.getInstance().setCarType(KlSdkVehicle.getInstance().getCarType());
        KradioSDKManager.getInstance().initAndActivate();

        ((Activity) context).runOnUiThread(new Runnable() {
            @Override
            public void run() {
                mCallBack.onCall(true);
            }
        });
    }


    /**
     * 模块初始化的回调接口
     */
//    private ECarXApiClient.Callback ecarCallback = new ECarXApiClient.Callback() {
//        @Override
//        public void onAPIReady(boolean ready) {
//
//        }
//};
}
