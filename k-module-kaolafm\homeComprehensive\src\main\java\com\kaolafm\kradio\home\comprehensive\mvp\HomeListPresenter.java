package com.kaolafm.kradio.home.comprehensive.mvp;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.base.utils.NetworkMonitor;
import com.kaolafm.kradio.common.KradioSDKManager;
import com.kaolafm.kradio.common.utils.TimeUtil;
import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.home.comprehensive.data.DataConverter;
import com.kaolafm.kradio.home.comprehensive.data.HomePlayerModel;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.kradio.lib.report.ReportUtil;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.YTLogUtil;
import com.kaolafm.opensdk.api.broadcast.ProgramDetails;
import com.kaolafm.opensdk.api.operation.model.column.ColumnGrp;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.socket.ConnectErrorListener;
import com.kaolafm.opensdk.socket.SocketManager;

import java.util.List;

import javax.net.ssl.SSLHandshakeException;

import io.socket.engineio.client.EngineIOException;

public class HomeListPresenter extends BasePresenter<HomePlayerModel, IHomeListView> implements NetworkMonitor.OnNetworkStatusChangedListener, ConnectErrorListener {
    private static final String TAG = "HomeListPresenter";
    private final HomePlayerModel mModel;
    /**
     * 是否第一次刷新，第一次刷新出错就显示错误页面，不是第一次不显示错误。
     */
    private boolean isFirstRefresh = true;

//    private Timer mTime;

    public HomeListPresenter(IHomeListView view) {
        super(view);
        mModel = createModel();
        SocketManager.getInstance().registerConnectErrorListener(this);
//        mTime = new Timer();
    }

    @Override
    protected HomePlayerModel createModel() {
        return HomePlayerModel.getInstance();
    }

    public void initData(String zone) {
        if (KradioSDKManager.getInstance().isUsable()) {
            getData(zone);
        } else {
            KradioSDKManager.getInstance().addUsableObserver(() -> getData(zone));
        }
    }

    /**
     * 获取数据
     */
    private void getData(String zone) {
        YTLogUtil.logStart(TAG, "getData", "zone:" + zone);
        if (NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), true)){
            if (mView != null){
                mView.hideErrorLayout();
                mView.showLoading();
            }
        } else {
            if (mView != null){
                mView.showError(ResUtil.getString(R.string.network_nosigin));
                mView.hideLoading();
            }
            return;
        }
        //主页面的等待，其他页面不等待
        YTLogUtil.logStart(TAG, "getData", "enter");

//        CompletableFuture<List<ColumnGrp>> cachedDataFuture = CompletableFuture.supplyAsync(this::waitForCachedDataWithTimeout);
//        CompletableFuture<List<ColumnGrp>> serverDataFuture = CompletableFuture.supplyAsync(() -> fetchFromServer(zone));
//
//        CompletableFuture.anyOf(cachedDataFuture, serverDataFuture).thenAccept(result -> {
//            new Handler(Looper.getMainLooper()).post(() -> {
//                if (result != null) {
//                    processData((List<ColumnGrp>) result);
//                } else {
//                    connectError();
//                }
//            });
//        });
        mModel.getDatas(zone, new HttpCallback<List<ColumnGrp>>() {
            @Override
            public void onSuccess(List<ColumnGrp> columnGrps) {
                processData(columnGrps);
            }

            @Override
            public void onError(ApiException e) {
                connectError();
            }
        });
//        YTDataCache.fetchHomePageZone(zone).thenAccept(result -> {
//            new Handler(Looper.getMainLooper()).post(() -> {
//                if (result != null) {
//                    processData(result);
//                } else {
//                    connectError();
//                }
//            });
//        });
    }

//    private List<ColumnGrp> fetchFromServer(String zone) {
//        YTLogUtil.logStart(TAG, "fetchFromServer", "");
//        CompletableFuture<List<ColumnGrp>> future = new CompletableFuture<>();
//        mModel.getDatas(zone, new HttpCallback<List<ColumnGrp>>() {
//            @Override
//            public void onSuccess(List<ColumnGrp> columnGrps) {
//                YTLogUtil.logStart(TAG, "fetchFromServer", "success");
//                future.complete(columnGrps);
//            }
//
//            @Override
//            public void onError(ApiException e) {
//                YTLogUtil.logStart(TAG, "fetchFromServer", "onError:" + e.getCode() + " " + e.getMessage());
//                future.complete(null);
//            }
//        });
//        return future.join();
//    }
//
//    //等待最多 10x50 毫秒
//    private List<ColumnGrp> waitForCachedDataWithTimeout() {
//        YTLogUtil.logStart(TAG, "waitForCachedDataWithTimeout", "");
//        for (int tryCount = 0; tryCount < 10; tryCount++) {
//            List<ColumnGrp> columnGrps = HomeCache.getCachedColumnGrpList();
//            if (!ListUtil.isEmpty(columnGrps)) {
//                YTLogUtil.logStart(TAG, "waitForCachedDataWithTimeout", "success");
//                return columnGrps; // 找到了缓存数据
//            }
//            try {
//                TimeUnit.MILLISECONDS.sleep(50); // 休眠50毫秒
//            } catch (InterruptedException e) {
//                Thread.currentThread().interrupt(); // 处理异常
//                break;
//            }
//        }
//        return null; // 超过重试次数后返回null
//    }

    // 处理数据的逻辑
    private void processData(List<ColumnGrp> columnGrps) {
        YTLogUtil.logStart(TAG, "processData", "");
        if (mView != null) {
            List<HomeCell> cells = DataConverter.toHomeCells(columnGrps);
            mModel.addBroadcastList(cells);
            isFirstRefresh = false;
            mView.showContent(cells);
            reportRecommendShownew(cells);
        }
    }

    /**
     * 推荐展示数据上报
     *
     * @param listListPair
     */
    private void reportRecommendShownew(List<HomeCell> listListPair) {
        if (listListPair == null) {
            return;
        }
        if (ListUtil.isEmpty(listListPair)) {
            return;
        }
        ReportUtil.addRecommendShowEvent(listListPair);
    }

    @Override
    public void onStatusChanged(int newStatus, int oldStatus) {
        Log.i(TAG, "onStatusChanged: : " + newStatus);
        if (newStatus == NetworkMonitor.STATUS_MOBILE || newStatus == NetworkMonitor.STATUS_WIFI) {
            start();
        }
    }

    private void connectError() {
        if (isFirstRefresh) {
            if (mView != null) {
                mView.showError("");
            }
        }
    }

    private void connectError(String error) {
        if (mView != null) {
            mView.showError(error);
        }
    }

    @Override
    public void onConnectError(Object... args) {
        Log.e(TAG, "Socket Connect Error.");
        if (args.length > 0 && args[0] instanceof EngineIOException && null != ((EngineIOException) args[0]).getCause() && ((EngineIOException) args[0]).getCause() instanceof SSLHandshakeException) {
            connectError(ResUtil.getString(R.string.home_network_certificate_error));
        } else {
            connectError();
        }
    }

    public void schedulProgramListRequest(int delay) {
        mModel.schedulProgramListRequest(1, new HttpCallback<List<ProgramDetails>>() {
            @Override
            public void onSuccess(List<ProgramDetails> list) {
                if (mView != null) {
                    for (int i = 0; i < list.size(); i++) {
                        if (TimeUtil.isWithinTime(list.get(i).getBeginTime(), list.get(i).getEndTime())) {
                            mView.showImage(list.get(i).getBroadcastId(), list.get(i).getImage(), list.get(i).getTitle(), list.get(i).getDesc());
                        } else {
                            if (!TimeUtil.isAfterTime(list.get(i).getBeginTime())) {
                                schedulProgramList(list.get(i).getBeginTime(), list.get(i).getEndTime(), list.get(i).getBroadcastId(), list.get(i).getImage(), list.get(i).getTitle(), list.get(i).getDesc());
                            }
                        }
                    }
                }
            }

            @Override
            public void onError(ApiException e) {

            }
        });
    }

    // CPU优化：使用静态Handler避免频繁创建Handler对象
    private static final Handler sScheduleHandler = new Handler(Looper.getMainLooper());

    public void schedulProgramList(String beginTime, String endTime, long broadcastId, String img, String title, String desc) {
        int delay = TimeUtil.getDelay(beginTime);
        // CPU优化：复用Handler，避免频繁创建新对象
        sScheduleHandler.postDelayed(() -> {
            if (mView != null) {
                mView.showImage(broadcastId, img, title, desc);
            }
        }, delay * 1000L);
    }

    public void cancelSchedule() {
//        mTime.cancel();
//        mTime = null;
    }

}
