<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_subcategory_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorTransparent">


    <com.kaolafm.kradio.common.widget.GridTouchInterceptRecyclerView
        android:id="@+id/rv_subcategory_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/tv_refresh_btn"
        android:overScrollMode="never" />

    <!-- 隐藏控件，用于所见即可说语音执行滑动操作 -->
    <TextView
        style="@style/ContentDescriptionScrollLeft"
        android:layout_gravity="center_vertical|start" />

    <TextView
        style="@style/ContentDescriptionScrollRight"
        android:layout_gravity="center_vertical|end" />

    <ViewStub
        android:id="@+id/vs_layout_error_page"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout="@layout/layout_each_status_page" />

    <include
        android:id="@+id/category_loading_view"
        layout="@layout/refresh_center"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        tools:visibility="visible" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clPermissionTip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone">

        <ImageView
            app:layout_constraintVertical_bias="0"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:id="@+id/permission_tip_pic_iv"
            android:src="@drawable/permission_tip_pic"
            android:layout_width="@dimen/m360"
            android:layout_height="@dimen/m142" />

        <TextView
            android:id="@+id/tv_permission_tip"
            android:layout_marginTop="@dimen/y32"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            tools:text="@string/local_broadcast_no_permission"
            android:textColor="@color/text_color_7"
            android:textSize="@dimen/text_size4"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/permission_tip_pic_iv"
            />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <include
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="gone"
        layout="@layout/comprehensive_no_content_layout" />
</FrameLayout>
