package com.kaolafm.kradio.flavor.impl;

import android.annotation.SuppressLint;
import android.content.Context;
import android.media.AudioManager;
import android.util.Log;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioRequestAudioFocusInter;
import com.kaolafm.opensdk.player.logic.PlayerManager;


/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-08-13 10:33
 ******************************************/
public class KRadioRequestAudioFocusImpl implements KRadioRequestAudioFocusInter {
    private static final String TAG = "KRadioRequestAudioFocusImpl";

    private int mCurrentAudioFocus = -1;

    @SuppressLint("LongLogTag")
    @Override
    public boolean requestAudioFocusBySelf(Object... args) {
        if (mCurrentAudioFocus < 0) {
            boolean flag = PlayerManager.getInstance().isPlayerInitSuccess();
            if (flag) {
                PlayerManager.getInstance().requestAudioFocus();
            } else {
                AudioManager audioManager = (AudioManager) AppDelegate.getInstance().getContext().getSystemService(Context.AUDIO_SERVICE);
                int result = audioManager.requestAudioFocus(mOnAudioFocusChangeListener, AudioManager.STREAM_MUSIC,
                        AudioManager.AUDIOFOCUS_GAIN);
                Log.i(TAG, "requestAudioFocusBySelf result = " + result);
                if (result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED) {
                    PlayerManager.getInstance().notifyAudioFocus(AudioManager.AUDIOFOCUS_GAIN);
                    mCurrentAudioFocus = 1;
                }
            }
        }
        return true;
    }

    private AudioManager.OnAudioFocusChangeListener mOnAudioFocusChangeListener = new AudioManager.OnAudioFocusChangeListener() {
        @SuppressLint("LongLogTag")
        @Override
        public void onAudioFocusChange(int focusChange) {
            mCurrentAudioFocus = focusChange;
            Log.i(TAG, "onAudioFocusChange----focusChange = " + focusChange);
        }
    };
}