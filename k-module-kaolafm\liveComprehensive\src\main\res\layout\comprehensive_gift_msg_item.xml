<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">


    <RelativeLayout
        android:id="@+id/infoRl"
        android:layout_marginTop="@dimen/y17"
        android:layout_width="@dimen/x372"
        android:layout_height="@dimen/y90"
        android:gravity="center_vertical"
        android:background="@drawable/comprehensive_gift_item_layout_bg">

        <ImageView
            android:id="@+id/headIv"
            android:layout_marginTop="@dimen/m8"
            android:layout_marginLeft="@dimen/m8"
            android:layout_width="@dimen/m74"
            android:layout_height="@dimen/m74"
            android:scaleType="fitXY" />

        <com.kaolafm.kradio.component.ui.base.view.KradioTextView
            android:id="@+id/nickNameTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/m16"
            android:layout_marginTop="@dimen/m6"
            android:layout_toRightOf="@id/headIv"
            app:kt_font_weight="0.7"
            android:text="nickName"
            android:textColor="#FF99DCF7"
            android:textSize="@dimen/m26" />

        <TextView
            android:id="@+id/infoTv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignLeft="@id/nickNameTv"
            android:layout_toLeftOf="@id/giftIv"
            android:layout_below="@id/nickNameTv"
            android:layout_marginTop="@dimen/m4"
            android:layout_marginRight="@dimen/m20"
            android:maxLines="1"
            android:ellipsize="end"
            android:text="送出了棒棒糖"
            android:textColor="#FFEEEEEE"
            android:textSize="@dimen/m24" />

        <ImageView
            android:id="@+id/giftIv"
            android:layout_alignParentRight="true"
            android:layout_marginRight="@dimen/m24"
            android:layout_width="@dimen/m86"
            android:layout_height="@dimen/m86"
            android:layout_centerVertical="true"/>
    </RelativeLayout>

    <ImageView
        android:id="@+id/light"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/x120" />


    <com.kaolafm.kradio.live.comprehensive.gift.ui.StrokeTextView
        android:id="@+id/animation_num"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/x388"
        android:layout_gravity="bottom"
        android:text="x 1"
        android:textColor="#FFEEEEEE"
        android:textSize="@dimen/m56"
         />

</FrameLayout>