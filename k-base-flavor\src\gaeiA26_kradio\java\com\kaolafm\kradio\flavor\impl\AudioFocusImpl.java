package com.kaolafm.kradio.flavor.impl;

import android.media.AudioManager;

import com.kaolafm.kradio.lib.base.flavor.player.KLOnAudioFocusChangeListener;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-08-14 11:50
 ******************************************/
public final class AudioFocusImpl implements KLOnAudioFocusChangeListener {
    @Override
    public boolean onAudioFocusDuck(int i) {
//        PlayerManager.getInstance(MyApplication.mContext).setVolume(0.5F, 0.5F);
        return true;
    }

    @Override
    public boolean beforeOnAudioFocusGain(int i) {
        return false;
    }

    @Override
    public boolean onAudioFocusGain(int i) {
        if (i == AudioManager.AUDIOFOCUS_LOSS) {
            return true;
        }
        return false;
    }

    @Override
    public boolean onAudioFocusLoss(int i) {
        return false;
    }

    @Override
    public boolean onRequestAudioFocusFailed() {
        return false;
    }
}
