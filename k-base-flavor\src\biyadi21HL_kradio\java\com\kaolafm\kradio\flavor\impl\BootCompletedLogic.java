package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.BootCompletedLogicListener;
import com.kaolafm.kradio.service.BYDWidgetService;
import com.kaolafm.opensdk.player.logic.PlayerManager;


/**
 * Created by <PERSON><PERSON><PERSON> on 2018/3/12.
 */

public class BootCompletedLogic implements BootCompletedLogicListener {
    @Override
    public boolean onBootCompletedLogic(Context context, Intent i) {
        PlayerManager.getInstance().init(AppDelegate.getInstance().getContext());
        Intent intent = new Intent(context, BYDWidgetService.class);
        Log.d("BootCompletedLogic","BootCompletedLogic bydstartTest : ");
        if (i != null && i.getBooleanExtra("from_quickboot",false)) {
            intent.setAction(BYDWidgetService.WIDGET_ACTION_REFRESH);
        } else {
            intent.setAction(BYDWidgetService.WIDGET_ACTION_REFRESH);
        }
        context.startService(intent);
        return true;
    }
}
