package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.kaolafm.kradio.lib.base.flavor.BootCompletedLogicListener;
import com.kaolafm.kradio.service.BYDWidgetService;


/**
 * Created by Wenchl on 2018/3/12.
 */

public class BootCompletedLogic implements BootCompletedLogicListener {
    @Override
    public boolean onBootCompletedLogic(Context context, Intent i) {
        PlayerManager.getInstance().init(context);
        Intent intent = new Intent(context, BYDWidgetService.class);
        Log.i("BootCompletedLogic", "BootCompletedLogic bydstartTest : ");
        if (i != null && i.getBooleanExtra("from_quickboot", false)) {
            intent.setAction(BYDWidgetService.WIDGET_ACTION_REFRESH);
        } else {
            intent.setAction(BYDWidgetService.WIDGET_ACTION_REFRESH);
        }
        context.startService(intent);
        return true;
    }
}
