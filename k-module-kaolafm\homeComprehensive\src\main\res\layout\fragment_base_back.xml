<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_home"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/root_layout_back"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/bbf_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <RelativeLayout
                android:id="@+id/bbf_back"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/m70"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <ImageView
                    android:layout_centerInParent="true"
                    style="@style/FragmentBackButton"/>
            </RelativeLayout>


            <LinearLayout
                android:id="@+id/bbf_center"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_marginStart="@dimen/m130"
                android:layout_marginEnd="@dimen/m130"
                android:gravity="center"
                android:orientation="horizontal" />

            <FrameLayout
                android:id="@+id/bbf_right"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="@dimen/m50" />


        </RelativeLayout>

        <TextView
            android:id="@+id/text_line"
            android:layout_width="match_parent"
            android:layout_height="@dimen/m1"
            android:layout_marginTop="@dimen/m30"
            android:background="@color/color_common_line"
            android:visibility="gone" />

        <FrameLayout
            android:id="@+id/bbf_content"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />
    </LinearLayout>

</LinearLayout>
