package com.kaolafm.kradio.flavor.recorder;


import android.media.AudioFormat;
import android.media.AudioRecord;
import android.media.MediaRecorder;
import android.os.Looper;
import android.util.Log;

import com.kaolafm.kradio.lib.base.flavor.live.KradioRecorderInterface;
import com.kaolafm.kradio.lib.utils.recorder.PcmToAacUtil;
import com.kaolafm.kradio.live1.player.HomeLiveManager;
import com.kaolafm.kradio.live1.player.LiveManager;

import java.io.BufferedOutputStream;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;

/**
 * <pre>
 *     <AUTHOR> Wenchl
 *     e-mail : <EMAIL>
 *     time   : 2019/05/28
 *     desc   :
 *     version: 1.0
 * </pre>
 */
public class DongfengRichanRecorder implements KradioRecorderInterface {

    private static final String TAG = "DongfengRichanRecorder";

//    private static final String FILE_PCM = "Live-Leave-message-dongfengrichan.aac";

    private AudioRecord mAudioRecord;

    private int bufferSize;

    private byte[] mAudioRecordData;

    private String filePath;

    private DongfengRichanPcmToAacUtil pcmToAacUtil;

    public DongfengRichanRecorder() {
        int samplerate = 44100;
        bufferSize = AudioRecord.getMinBufferSize(samplerate, AudioFormat.CHANNEL_IN_STEREO, AudioFormat.ENCODING_PCM_16BIT);
        mAudioRecord = new AudioRecord(MediaRecorder.AudioSource.MIC,
                44100, AudioFormat.CHANNEL_IN_MONO,
                AudioFormat.ENCODING_PCM_16BIT, bufferSize);
        mAudioRecordData = new byte[bufferSize];

        File file = createNewAudioFile();
        filePath = file.getAbsolutePath();
    }

    private File createNewAudioFile() {
        if (!HomeLiveManager.RECORDINGS_DIR.exists()) {
            HomeLiveManager.RECORDINGS_DIR.mkdirs();
        }
        File file = new File(LiveManager.RECORDINGS_DIR, "Live-Leave-message" + LiveManager.EXTENSION);
        return file;
    }


    @Override
    public void startRecord() {
        Observable.timer(1000, TimeUnit.MILLISECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(Schedulers.io())
                .subscribe(aLong -> startRecordAction());

    }

    private void startRecordAction() {
        try {
            Log.i("DongfengRichanRecorder", "startRecord");
            final File file = createNewAudioFile();

            if (file.exists() ) {
                file.delete();
            }

            filePath = file.getAbsolutePath();
            pcmToAacUtil = new DongfengRichanPcmToAacUtil(44100, 1);

            DataOutputStream dos = new DataOutputStream(
                    new BufferedOutputStream(
                            new FileOutputStream(file)));

            mAudioRecord.startRecording();
            Log.i("DongfengRichanRecorder", "mAudioRecord.getRecordingState() = " + (mAudioRecord.getRecordingState()));
            while (mAudioRecord.getRecordingState() == AudioRecord.RECORDSTATE_RECORDING) {
                int number = mAudioRecord.read(mAudioRecordData, 0, bufferSize);
                if (AudioRecord.ERROR_INVALID_OPERATION != number) {
                    byte[] btData = pcmToAacUtil.offerEncoder(mAudioRecordData);
                    if (btData.length > 0) {
                        dos.write(btData);
                    }
                }
//                        short[] mic2Data = new short[number / 4];
//                        int j = 0;
//                        //启辰成都是4ch的数据，测试发现只有mic2的数据可用。
//                        for (int i = 0; i < number; i = i + 4) {
//                            mic2Data[j] = mAudioRecordData[i + 1];
//                            j++;
//                        }

            }

            dos.flush();
            dos.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Override
    public boolean stopRecord() {
        if (mAudioRecord == null) {
            return false;
        }

        Log.i(TAG, "stopRecord");
        try {
            mAudioRecord.stop();
            if (pcmToAacUtil != null) {
                pcmToAacUtil.close();
            }
        } catch (IllegalStateException e) {
            Log.i(TAG, "stopRecord error:" + e.getMessage());
            return false;
        }

        return true;
    }

    @Override
    public String getFilePath() {
        return filePath;
    }

    private byte[] shortToByteArray(short[] sData) {
        int shortArrsize = sData.length;
        byte[] bytes = new byte[shortArrsize * 2];
        for (int i = 0; i < shortArrsize; i++) {
            bytes[i * 2] = (byte) (sData[i] & 0x00FF);
            bytes[(i * 2) + 1] = (byte) (sData[i] >> 8);
            sData[i] = 0;
        }
        return bytes;
    }

}
