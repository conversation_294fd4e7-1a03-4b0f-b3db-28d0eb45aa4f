package com.kaolafm.kradio.brand.comprehensive.topic;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.drawable.ColorDrawable;
import android.media.AudioManager;
import android.os.Build;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.ActionMode;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.airbnb.lottie.LottieAnimationView;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.kaolafm.kradio.brand.mvp.IRecordAudioView;
import com.kaolafm.kradio.brand.mvp.ITopicDetailView;
import com.kaolafm.kradio.brand.mvp.SpeechToTextPresenter;
import com.kaolafm.kradio.brand.mvp.TopicPresenter;
import com.kaolafm.kradio.common.base.BaseSkinAppCompatActivity;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.kradio.common.utils.CommonUtils;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioFocusInter;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.SharedPreferenceUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.user.UserInfoManager;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.speech2text.model.SpeechToTextError;
import com.kaolafm.opensdk.api.speech2text.model.SpeechToTextResult;
import com.kaolafm.opensdk.api.topic.model.TopicDetail;
import com.kaolafm.opensdk.api.topic.model.TopicPosts;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.event.DialogExposureEvent;
import com.kaolafm.report.util.ReportConstants;
import com.kaolafm.report.util.ReportParameterManager;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;


@Route(path = RouterConstance.BRAND_POSTS_PUBLISH_URL_COMPREHENSIVE)
public class ComprehensivePostsInputActivity extends BaseSkinAppCompatActivity<TopicPresenter> implements View.OnClickListener, ITopicDetailView, IRecordAudioView {
    private EditText inputEt;
    private TextView textLengthTip;
    private ImageView inputClearBtn;
    private ViewGroup voiceInput, voiceInputting;
    private TextView publishBtn;
    private View contentView;
    private LottieAnimationView rateView;

    private final String TAG = getClass().getSimpleName();

    private long mTopicId = 0L;
    private long mLastRecordTime;   //上次录音完成或取消的时间戳
    public static final int DURATION_LONG = 1000;

    public static final String ARGUMENT_TOPIC_ID = "ARGUMENT_TOPIC_ID";
    public static final String ARGUMENT_PAGE_ID = "PAGE_ID";

    private static final int CODE_PERMISSION_REQUEST = 1;

    private SpeechToTextPresenter mSpeechToTextPresenter;
    private String speedResultLastSentence = null;    //语音识别的上一句话

    private KRadioAudioFocusInter mKRadioAudioFocusInter;

    //输入字符数限制
    private static final int INPUT_NUMBER_LIMIT = 1000;
    /**
     * 帖子内容监听
     */
    private TextWatcher mTextChangedListener = new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {

        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {

        }

        @Override
        public void afterTextChanged(Editable s) {
            if (s == null || s.length() == 0) {
                ViewUtil.setViewVisibility(inputClearBtn, View.GONE);
            } else {
                ViewUtil.setViewVisibility(inputClearBtn, View.VISIBLE);
            }
            if ((s == null || s.length() == 0) && publishBtn.isSelected()) {
                publishBtn.setSelected(false);
            } else if (s != null && s.length() > 0 && !publishBtn.isSelected()) {
                publishBtn.setSelected(true);
            }
            textLengthTip.setText(String.format(ResUtil.getString(R.string.comprehensive_topic_publish_posts_input_text_count), s == null ? 0 : s.length()));
            if (s != null && s.length() >= INPUT_NUMBER_LIMIT) {
                ToastUtil.showNormal(ComprehensivePostsInputActivity.this, String.format(ResUtil.getString(R.string.comprehensive_posts_publish_input_limit_notice), INPUT_NUMBER_LIMIT));
            }
        }
    };
    private TextView.OnEditorActionListener mEditActionListener = new TextView.OnEditorActionListener() {
        @Override
        public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
            if (event != null && event.getAction() == KeyEvent.ACTION_UP) {
                publishBtn.performClick();
            }
            return true;
        }
    };
    private long startTime;
    private String pageId, inputContent;
    private AtomicBoolean gotAudioFocus = new AtomicBoolean(false);

    @Override
    protected TopicPresenter createPresenter() {
        return new TopicPresenter(this);
    }

    @Override
    public int getLayoutId() {
        return R.layout.comprehensive_activity_posts_input_1920x720;
    }

    @Override
    public int getLayoutId_Tow() {
        return R.layout.comprehensive_activity_posts_input_1280x720;
    }


    @Override
    protected void onCreate(@androidx.annotation.Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().setBackgroundDrawable(new ColorDrawable(ResUtil.getColor(R.color.transparent_color)));
        CommonUtils.getInstance().initGreyStyle(getWindow());
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        contentView = findViewById(R.id.contentView);
        contentView.setOnClickListener(this);
        startTime = System.currentTimeMillis();
        inputEt = findViewById(R.id.inputEt);
        rateView = findViewById(R.id.rateView);
        textLengthTip = findViewById(R.id.textLengthTip);
        inputClearBtn = findViewById(R.id.inputClearBtn);
        voiceInput = findViewById(R.id.voiceInput);
        voiceInputting = findViewById(R.id.voiceInputting);
        voiceInputting.setVisibility(View.GONE);
        publishBtn = findViewById(R.id.publishBtn);
        inputEt.addTextChangedListener(mTextChangedListener);
        inputClearBtn.setOnClickListener(this);
        publishBtn.setOnClickListener(this);
        voiceInput.setOnClickListener(this);
        voiceInputting.setOnClickListener(this);
        findViewById(R.id.rootView).setOnClickListener(this);
        inputEt.setImeActionLabel("发布", EditorInfo.IME_ACTION_SEND);
//        inputEt.setOnEditorActionListener(mEditActionListener);
        inputEt.setCustomSelectionActionModeCallback(new ActionMode.Callback() {
            public boolean onCreateActionMode(ActionMode mode, Menu menu) {
                return false;
            }

            public boolean onPrepareActionMode(ActionMode mode, Menu menu) {
                return false;
            }

            public boolean onActionItemClicked(ActionMode mode, MenuItem item) {
                return false;
            }

            public void onDestroyActionMode(ActionMode mode) {
            }
        });
        inputEt.setText(inputContent);
        inputEt.setSelection(inputContent.length());
    }

    @Override
    public void initData() {
        Intent arguments = getIntent();
        if (arguments != null) {
            mTopicId = arguments.getLongExtra(ARGUMENT_TOPIC_ID, 0L);
            pageId = arguments.getStringExtra(ARGUMENT_PAGE_ID);
        }
        inputContent = SharedPreferenceUtil.getInstance(this, POSTS_SP_NAME).getString(POSTS_SP_KEY, "");
    }

    @Override
    protected void onPause() {
        super.onPause();
        saveInputContent(inputEt.getText().toString());
    }

    @Override
    protected void onDestroy() {
        inputEt.removeTextChangedListener(mTextChangedListener);
        if (mSpeechToTextPresenter != null) {
            mSpeechToTextPresenter.destroy();
            if (gotAudioFocus.compareAndSet(true, false)) {
                abandonAudioFocus();
            }
        }
        reportPageShowEvent();
        super.onDestroy();
    }

    private static final String POSTS_SP_NAME = "posts_cache";
    private static final String POSTS_SP_KEY = "POSTS_SP_KEY";

    private void saveInputContent(String content) {
        SharedPreferenceUtil sp = SharedPreferenceUtil.getInstance(this, POSTS_SP_NAME);
        sp.putString(POSTS_SP_KEY, content);
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.publishBtn) {
            ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(
                    ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_POSTS_PUBLISH_PAGE_PUBLISH, null,
                    ReportParameterManager.getInstance().getPage(), ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_POSTS_PUBLISH));
            //发布话题
            if (inputEt.getText().length() == 0) {
                return;
            }
            if (mSpeechToTextPresenter != null && (mSpeechToTextPresenter.isRecording() || gotAudioFocus.get())) {
                mSpeechToTextPresenter.stopRecording(false);
            }
            if (!UserInfoManager.getInstance().isUserBound()) {
                ToastUtil.showNormal(getApplicationContext(), ResUtil.getString(R.string.comprehensive_topic_publish_posts_no_login));
                RouterManager.getInstance().jumpPage(RouterConstance.LOGIN_COMPREHENSIVE_URL);
                return;
            }
            mPresenter.publishPosts(mTopicId, inputEt.getText().toString());
        } else if (v.getId() == R.id.inputClearBtn) {
            inputEt.setText("");
        } else if (v.getId() == R.id.rootView) {
            finish();
        } else if (v.getId() == R.id.voiceInput) {
            if (AntiShake.check(v.getId())) {
                return;
            }
            v.postDelayed(() -> {
                if (NetworkUtil.isNetworkAvailableWidthDefaultToast(getApplicationContext())) {
                    //重置句子结果相关的统计
                    speedResultLastSentence = inputEt.getText().toString();

                    startRecordWithPermissionCheck();
                }
            }, 300);

        } else if (v.getId() == R.id.voiceInputting) {
            if (mSpeechToTextPresenter == null) return;
            mSpeechToTextPresenter.stopRecording(true);
        }
    }


    private void startRecordWithPermissionCheck() {
        List<String> needPermission = new ArrayList<String>();
        boolean needRecordAudio = ContextCompat.checkSelfPermission(this,
                Manifest.permission.RECORD_AUDIO) != PackageManager.PERMISSION_GRANTED;
        if (needRecordAudio) {
            needPermission.add(Manifest.permission.RECORD_AUDIO);
        }
        boolean needReadExternal = ContextCompat.checkSelfPermission(this,
                Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED;
        if (needReadExternal) {
            needPermission.add(Manifest.permission.READ_EXTERNAL_STORAGE);
        }
        boolean needWriteExternal = ContextCompat.checkSelfPermission(this,
                Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED;
        if (needWriteExternal) {
            needPermission.add(Manifest.permission.WRITE_EXTERNAL_STORAGE);
        }
        if (needPermission.size() > 0) {
            String[] requestPermissions = new String[needPermission.size()];
            for (int i = 0; i < needPermission.size(); i++) {
                requestPermissions[i] = needPermission.get(i);
            }
            ActivityCompat.requestPermissions(this, requestPermissions,
                    CODE_PERMISSION_REQUEST);
        } else {
            startRecord();
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
                                           @NonNull int[] grantResults) {
        switch (requestCode) {
            case CODE_PERMISSION_REQUEST:
                if (grantResults.length > 0
                        && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    startRecord();
                }
                break;
        }
    }


    @Override
    protected void onResume() {
        super.onResume();
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(
                ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_POSTS_PUBLISH_PAGE_PUBLISH, null,
                ReportParameterManager.getInstance().getPage(), ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_POSTS_PUBLISH));
    }

    private void startRecord() {
        long delta = System.currentTimeMillis() - mLastRecordTime;
        if (delta < DURATION_LONG) {
            Log.i(TAG, "startRecord but period to last recording is too short");
            return;
        }
        if (mSpeechToTextPresenter == null)
            mSpeechToTextPresenter = new SpeechToTextPresenter(this);

        if (mSpeechToTextPresenter.isRecording()) {
            Log.i(TAG, "recording already running");
            return;
        }
        mSpeechToTextPresenter.startRecord();
    }

    @Override
    public void onGetTopicDetailSuccess(TopicDetail topicDetails) {

    }

    @Override
    public void onGetTopicDetailFailure() {

    }

    @Override
    public void onGetPostsListSuccess(BasePageResult<List<TopicPosts>> result) {

    }

    @Override
    public void onGetPostsListFailure() {

    }

    @Override
    public void onOperatePostsSuccess(TopicPosts mTopicPosts, int position) {

    }

    @Override
    public void onOperatePostsFailure(int operationType) {

    }

    @Override
    public void onPublishPostsSuccess() {
        inputEt.setText("");
        ToastUtil.showNormal(getApplicationContext(), ResUtil.getString(R.string.comprehensive_topic_publish_posts_success));
        finish();
    }

    @Override
    public void onPublishPostsFailure() {
        ToastUtil.showNormal(getApplicationContext(), ResUtil.getString(R.string.comprehensive_topic_publish_posts_failure));
    }

    @Override
    public void onRecordingStarted() {
        voiceInput.setVisibility(View.GONE);
        voiceInputting.setVisibility(View.VISIBLE);
        rateView.playAnimation();
        requestAudioFocus();
        if (!inputEt.hasFocus()) inputEt.requestFocus();
    }

    @Override
    public void onRecordingStopped() {
        voiceInputting.setVisibility(View.GONE);
        voiceInput.setVisibility(View.VISIBLE);
        rateView.pauseAnimation();
        abandonAudioFocus();
        mLastRecordTime = System.currentTimeMillis();
    }

    @Override
    public void onRecordDurationTooShort() {
        ToastUtil.showNormal(getApplicationContext(), "录音时间太短");
        onRecordingStopped();
        mLastRecordTime = System.currentTimeMillis();
    }

    @Override
    public void onRecordingError(@NotNull SpeechToTextError error) {
        abandonAudioFocus();
        mLastRecordTime = System.currentTimeMillis();
        ToastUtil.showNormal(getApplicationContext(), "语音识别出错");
    }

    @Override
    public void onSpeechResultReceived(@NotNull SpeechToTextResult result) {
        //必须放在判断Text之前，因为当state为0时，text为null
        if (result.getState() == SpeechToTextResult.SENTENCE_START) {
            speedResultLastSentence = inputEt.getText().toString();
        }
        if (StringUtil.isEmpty(result.getText())) {
            return;
        }
        String speedResult = speedResultLastSentence + result.getText();
        inputEt.setText(speedResult);
        inputEt.setSelection(inputEt.getText().length());
        if (!inputEt.hasFocus()) {
            inputEt.requestFocus();
        }
    }

    private AudioManager.OnAudioFocusChangeListener mAudioFocusListener
            = new AudioManager.OnAudioFocusChangeListener() {
        /**
         * focusChange == AudioManager.AUDIOFOCUS_LOSS_TRANSIENT):
         *      短暂性丢失焦点，当其他应用申请AUDIOFOCUS_GAIN_TRANSIENT或AUDIOFOCUS_GAIN_TRANSIENT_EXCLUSIVE时，会触发此回调事件
         *      例如播放短视频，拨打电话等。
         *      通常需要暂停音乐播放
         *
         * focusChange == AudioManager.AUDIOFOCUS_LOSS
         *      长时间丢失焦点,当其他应用申请的焦点为AUDIOFOCUS_GAIN时，会触发此回调事件
         *      例如播放QQ音乐，网易云音乐等
         *      此时应当暂停音频并释放音频相关的资源。
         * focusChange == AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK
         *      短暂性丢失焦点并作降音处理，当其他应用申请AUDIOFOCUS_GAIN_TRANSIENT_MAY_DUCK时，会触发此回调事件
         *      通常需要降低音量
         * focusChange == AudioManager.AUDIOFOCUS_GAIN
         *      当其他应用申请焦点之后又释放焦点会触发此回调
         *      可重新播放音乐
         * @param focusChange
         */
        @Override
        public void onAudioFocusChange(int focusChange) {
            if (mSpeechToTextPresenter != null)
                mSpeechToTextPresenter.stopRecording(false);
        }
    };

    /**
     * 请求音频焦点
     *
     * @return
     */
    private boolean requestAudioFocus() {
        if (mKRadioAudioFocusInter == null) {
            mKRadioAudioFocusInter = ClazzImplUtil.getInter("KRadioAudioFocusImpl");
        }

        if (mKRadioAudioFocusInter != null) {
            // 解决https://app.huoban.com/tables/2100000007530121/items/2300001158191064?userId=1881599问题
            boolean flag = mKRadioAudioFocusInter.requestAudioFocus(mAudioFocusListener);
            if (flag) {
                gotAudioFocus.set(true);
            }
            return flag;
        }
        AudioManager am = (AudioManager) this.getSystemService(Context.AUDIO_SERVICE);
        int durationHint = AudioManager.AUDIOFOCUS_GAIN;
        if (Build.VERSION.SDK_INT >= 19) {
            durationHint = AudioManager.AUDIOFOCUS_GAIN_TRANSIENT_EXCLUSIVE;
        }
        boolean status = AudioManager.AUDIOFOCUS_REQUEST_GRANTED ==
                am.requestAudioFocus(mAudioFocusListener, AudioManager.STREAM_MUSIC,
                        durationHint);
        Log.i(TAG, "live requestAudioFocus status:" + status);
        if (status) gotAudioFocus.set(true);
        return status;
    }

    /**
     * 丢弃音频焦点
     *
     * @return
     */
    private boolean abandonAudioFocus() {
        if (mKRadioAudioFocusInter != null) {
            boolean flag = mKRadioAudioFocusInter.abandonAudioFocus(mAudioFocusListener);
            return flag;
        }
        AudioManager am = (AudioManager) this.getSystemService(Context.AUDIO_SERVICE);
        boolean status = AudioManager.AUDIOFOCUS_REQUEST_GRANTED ==
                am.abandonAudioFocus(mAudioFocusListener);
        Log.i(TAG, "live abandonAudioFocus status:" + status);
        if (status) gotAudioFocus.set(false);
        return status;
    }


    /**
     * 页面曝光事件上报
     */
    private void reportPageShowEvent() {
        long duration = System.currentTimeMillis() - startTime;
        if (startTime < 0 || duration < 300) {
            return;
        }
        ReportHelper.getInstance().addEvent(new DialogExposureEvent(ReportConstants.DIALOG_ID_POSTS_PUBLISH, pageId, duration, null));
        Log.i("BaseDialogFragment", "report=" + duration);
        startTime = -1;
    }

    @Override
    public void onNetWordError(@Nullable Object[] args) {
        ToastUtil.showInfo(this, "当前网络异常");
        if (mSpeechToTextPresenter != null) {
            mSpeechToTextPresenter.stopRecording(false);
        }
    }
}
