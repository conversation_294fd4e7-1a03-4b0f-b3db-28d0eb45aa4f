package com.kaolafm.kradio.online.player.base;

import androidx.lifecycle.Lifecycle;
import android.content.res.Configuration;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.RecyclerView;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.LayoutAnimationController;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.kradio.common.bean.ProgramDateData;
import com.kaolafm.kradio.common.widget.refresh.KradioSmartRefreshLayout;
import com.kaolafm.kradio.k_kaolafm.R; 
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.widget.tab.OnTabSelectListener;
import com.kaolafm.kradio.lib.widget.tab.SlidingTabLayout;
import com.kaolafm.kradio.lib.widget.tab.Tab;
import com.kaolafm.kradio.online.common.base.MBaseShowHideFragment;
import com.kaolafm.kradio.online.player.adapters.OnlinePlayItemDiffCallback;
import com.kaolafm.kradio.online.player.models.BusEventPlayingLiving;
import com.kaolafm.kradio.online.player.models.BusEventPlayingProgramIdChanged;
import com.kaolafm.kradio.online.player.models.OnlineTabData;
import com.kaolafm.kradio.online.player.utils.CenterLayoutManager;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.BroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.TVPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonClickReportEvent; 

import org.greenrobot.eventbus.EventBus;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
 

public abstract class OnlineBasePlayListFragment extends MBaseShowHideFragment<BasePresenter> {
    protected String TAG = OnlineBasePlayListFragment.class.getSimpleName();
    private final int DELAY_SCROLL_TIME_MS = 500;   //渲染延时，用于列表显示后延迟滚动到播放项、滚动完毕后延迟重渲染
    private final int DELAY_RENDER_TIME_MS = 200;   //渲染延时，用于滚动完毕后延迟重渲染
 
    protected TextView albumTitleTv; 
    protected TextView playItemCountTv; 
    protected TextView albumFlagTv; 
    protected ImageView sortIcon; 
    protected TextView SortTipTv; 
    protected View divider; 
    protected KradioSmartRefreshLayout refreshLayout; 
    protected RecyclerView recyclerView; 
    protected SlidingTabLayout tabLayout;

    protected CenterLayoutManager layoutManager;

    private OnlineBasePlayListRvAdapter mAdapter;

    private com.kaolafm.kradio.lib.widget.tab.OnTabSelectListener mOnTabSelectedListener;
    protected List<OnlineTabData> onlineTabDataList;

    public OnlineBasePlayListRvAdapter getAdapter() {
        return mAdapter;
    }

    private final RecyclerView.OnScrollListener mRenderScrollListener = new RecyclerView.OnScrollListener() {
        @Override
        public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
            super.onScrollStateChanged(recyclerView, newState);
            if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                mHandler.sendEmptyMessageDelayed(HANDLER_RENDER_ITEM_REAL, DELAY_RENDER_TIME_MS);
            }
        }
    };
    private final Handler mHandler = new Handler(Looper.getMainLooper(), new Handler.Callback() {
        @Override
        public boolean handleMessage(@NonNull Message msg) {
            if (msg.what == HANDLER_SCROLL_TO_PLAYING) {
                if (mAdapter == null) return false;
                int newPosition = mAdapter.findSelectPosition();
                if (newPosition == -1) {
                    return false;
                }
                //查找当前播放项并自动滑动到此
                for (int i = 0; i < playItems.size(); i++) {
                    if (playItems.get(i).getAudioId() == mAdapter.getPlayingItem().getAudioId()) {
                        //判断目标Item是否已经在RecyclerView中间位置
                        View viewByPosition = layoutManager.findViewByPosition(i);
                        if (viewByPosition != null) {
                            final RecyclerView.LayoutParams params = (RecyclerView.LayoutParams)
                                    viewByPosition.getLayoutParams();
                            final int top = layoutManager.getDecoratedTop(viewByPosition) - params.topMargin;
                            final int bottom = layoutManager.getDecoratedBottom(viewByPosition) + params.bottomMargin;
                            final int start = layoutManager.getPaddingTop();
                            final int end = layoutManager.getHeight() - layoutManager.getPaddingBottom();
                            int offset = (start + (end - start) / 2) - (top + (bottom - top) / 2);
                            if (offset == 0) {
                                //已经在中间，不需要滚动，直接渲染即可
                                return false;
                            }
                        }
                        //将会开始平滑滑动
                        recyclerView.smoothScrollToPosition(i);
                        break;
                    }
                }
            } else if (msg.what == HANDLER_RENDER_ITEM) {
                if (mAdapter == null) return false;
                boolean needReRender = true;
                int newPosition = mAdapter.findSelectPosition();
                if (newPosition == -1) {
                    return false;
                }
                if (newPosition != mAdapter.getSelectPosition()) {
                    mAdapter.selectItem(newPosition);
                    needReRender = false;
                }

                //REFRESH_PROGRAM_CHANGE_PLAYITEM_UNSCROLL时只是更新数据，并不滚动到播放项
                if (mRefreshProgramFlag == REFRESH_PROGRAM_CHANGE_PLAYITEM_UNSCROLL) {
                    if (needReRender)
                        mHandler.sendEmptyMessageDelayed(HANDLER_RENDER_ITEM_REAL, DELAY_RENDER_TIME_MS);
                    return false;
                }

                //查找当前播放项并自动滑动到此
                for (int i = 0; i < playItems.size(); i++) {
                    if (playItems.get(i).getAudioId() == mAdapter.getPlayingItem().getAudioId()) {
                        //判断目标Item是否已经在RecyclerView中间位置
                        View viewByPosition = layoutManager.findViewByPosition(i);
                        if (viewByPosition != null) {
                            final RecyclerView.LayoutParams params = (RecyclerView.LayoutParams)
                                    viewByPosition.getLayoutParams();
                            final int top = layoutManager.getDecoratedTop(viewByPosition) - params.topMargin;
                            final int bottom = layoutManager.getDecoratedBottom(viewByPosition) + params.bottomMargin;
                            final int start = layoutManager.getPaddingTop();
                            final int end = layoutManager.getHeight() - layoutManager.getPaddingBottom();
                            int offset = (start + (end - start) / 2) - (top + (bottom - top) / 2);
                            if (offset == 0) {
                                //已经在中间，不需要滚动，直接渲染即可
                                if (needReRender)
                                    mHandler.sendEmptyMessageDelayed(HANDLER_RENDER_ITEM_REAL, DELAY_RENDER_TIME_MS);
                                return false;
                            }
                        }
                        //添加滚动监听，当停止滚动时，重新渲染播放项
                        if (needReRender) {
                            recyclerView.removeOnScrollListener(OnlineBasePlayListFragment.this.mRenderScrollListener);
                            recyclerView.addOnScrollListener(OnlineBasePlayListFragment.this.mRenderScrollListener);
                        }
                        //将会开始平滑滑动
                        recyclerView.smoothScrollToPosition(i);
                        break;
                    }
                }
            } else if (msg.what == HANDLER_RENDER_ITEM_REAL) {
                recyclerView.removeOnScrollListener(mRenderScrollListener);
                //重新渲染正在直播的item
                if (mAdapter != null) {
                    mAdapter.reRenderPlayingItem();
                }
            }
            return false;
        }
    });

    /**
     * 处理一秒钟后的重新渲染item
     * 此处只处理平滑滚动到当前position
     */
    private static final int HANDLER_RENDER_ITEM = 1000;
    /**
     * 真正开始重新渲染的地方。
     */
    private static final int HANDLER_RENDER_ITEM_REAL = 1001;
    /**
     * 仅滚动
     */
    private static final int HANDLER_SCROLL_TO_PLAYING = 1002;
    /**
     * 调用getProgramFromServer()方法刷新列表时，如果是切换日期需要刷新，则使用该值
     */
    protected static final int REFRESH_PROGRAM_CHANGE_DATE = 0;
    /**
     * 调用getProgramFromServer()方法刷新列表时，如果是播放器播放新PlayItem需要刷新，则使用该值
     */
    protected static final int REFRESH_PROGRAM_CHANGE_PLAYITEM = 1;
    /**
     * 调用getProgramFromServer()方法刷新列表时，如果是只是更新列表，但不滚动到播放项，则使用该值
     */
    protected static final int REFRESH_PROGRAM_CHANGE_PLAYITEM_UNSCROLL = 2;
    private int mRefreshProgramFlag = REFRESH_PROGRAM_CHANGE_PLAYITEM;

    //设置数据刷新标识
    public void setRefreshProgramFlag(int mRefreshProgramFlag) {
        this.mRefreshProgramFlag = mRefreshProgramFlag;
    }

    //播单
    protected List<PlayItem> playItems;

    //第一次加载页面标识，用于跳过播放器监听器里面刷新播单的操作，防止第一次打开页面进行两次刷新
    //目前已知：设置监听器时就会触发onPlayingItem回调的有广播。不会触发的有专辑
    private AtomicBoolean isFirstLoadDate = new AtomicBoolean(true);

    private boolean isFront = false;
    private boolean isGotoBack = false;
    /**
     * 播放器状态监听
     */
    private final BasePlayStateListener stateListener = new BasePlayStateListener() {
        @Override
        public void onPlayerEnd(PlayItem playItem) {
            Log.e(TAG, "onPlayerEnd:playItem=" + playItem);
            onPlayItemEnd(playItem);
        }

        @Override
        public void onPlayerFailed(PlayItem playItem, int what, int extra) {
            Log.e(TAG, "onPlayerFailed:playItem=" + playItem + " ,what=" + what + " ,extra=" + extra);
            onPlayItemFailed(playItem);
        }

        @Override
        public void onPlayerPaused(PlayItem playItem) {
            super.onPlayerPaused(playItem);
            onPlayItemPause(playItem);
        }

        @Override
        public void onPlayerPlaying(PlayItem playItem) {
            Log.e(TAG, "onPlayerPlaying:playItem=" + playItem);
            if (!getLifecycle().getCurrentState().isAtLeast(Lifecycle.State.CREATED)) return;
            if (mAdapter != null) mAdapter.notifyAdapterPlayItemResume(playItem);
        }

        @Override
        public void onPlayerPreparing(PlayItem playItem) {
            Log.e(TAG, "onPlayerPreparing:playItem=" + playItem);
//            updatePlayingItem(false);
            if (playItem.getType() == PlayerConstants.RESOURCES_TYPE_BROADCAST || playItem.getType() == PlayerConstants.RESOURCES_TYPE_TV) {
                //如果正在播放直播单曲，通知下去，以便禁用或启用左滑
                EventBus.getDefault().post(new BusEventPlayingLiving(playItem != null && playItem.getStatus() == PlayerConstants.BROADCAST_STATUS_LIVING));
                if (playItem != null)
                    EventBus.getDefault().post(new BusEventPlayingProgramIdChanged(playItem.getAudioId()));
            }
            if (!getLifecycle().getCurrentState().isAtLeast(Lifecycle.State.CREATED)) return;
            //如果是相同的节目，不做处理
            if (mAdapter != null && playItem != null && mAdapter.getPlayingItem() != null && playItem.getAudioId() == mAdapter.getPlayingItem().getAudioId())
                return;
            int index = -1;
            switch (playItem.getType()) {
                case PlayerConstants.RESOURCES_TYPE_BROADCAST:
                case PlayerConstants.RESOURCES_TYPE_TV:
                    if (playItems == null) return;
                    for (int position = 0; position < playItems.size(); position++) {
                        if (playItems.get(position).getAudioId() == playItem.getAudioId()) {
                            index = position;
                            break;
                        }
                    }
                    if (index != -1) {
                        //当前列表找到了新的播放项
                        mRefreshProgramFlag = REFRESH_PROGRAM_CHANGE_PLAYITEM;
                        if (useTabLayout()) {
                            getProgramFromServer(onlineTabDataList.get(tabLayout.getCurrentPosition()));
                        }
                    } else {
                        //不在当前列表中
                        // 2022/6/21 重新计算playItem日期与当前tabLayout日期的关系，切换tabLayout到正确的日期
                        queryDateList();
                    }
                    break;
                case PlayerConstants.RESOURCES_TYPE_ALBUM:
                case PlayerConstants.RESOURCES_TYPE_RADIO:
                    if (playItems == null) return;
                    for (int position = 0; position < playItems.size(); position++) {
                        if (playItems.get(position).getAudioId() == playItem.getAudioId()) {
                            index = position;
                            break;
                        }
                    }

                    if (index == -1) {
                        refreshPlayListAndUpdateItemOnPosition(0);
                    } else {
                        mRefreshProgramFlag = REFRESH_PROGRAM_CHANGE_PLAYITEM;
                        if (mHandler.hasMessages(HANDLER_RENDER_ITEM)) {
                            mHandler.removeMessages(HANDLER_RENDER_ITEM);
                        }
                        mHandler.sendEmptyMessage(HANDLER_RENDER_ITEM);
                    }
                    break;
            }
        }

        @Override
        public void onIdle(PlayItem playItem) {
            Log.e(TAG, "onIdle:playItem=" + playItem);
//            updatePlayingItem(true);
        }
    };

    protected void onPlayItemPause(PlayItem playItem) {
        if (mAdapter != null)
            mAdapter.notifyAdapterPlayItemPaused(playItem);
    }

    protected void onPlayItemFailed(PlayItem playItem) {
        if (mAdapter != null)
            mAdapter.notifyAdapterPlayItemPaused(playItem);
    }

    protected void onPlayItemEnd(PlayItem playItem) {
        if (mAdapter != null)
            mAdapter.notifyAdapterPlayItemPaused(playItem);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.online_player_fragment_boradcast_list;
    }

    @Override
    public void initView(View view) {

        albumTitleTv=view.findViewById(R.id.albumTitleTv);
        playItemCountTv=view.findViewById(R.id.playItemCountTv);
        albumFlagTv=view.findViewById(R.id.albumFlagTv);
        sortIcon=view.findViewById(R.id.sortIcon);
        SortTipTv=view.findViewById(R.id.SortTipTv);
        divider=view.findViewById(R.id.divider);
        refreshLayout=view.findViewById(R.id.refreshLayout);
        recyclerView=view.findViewById(R.id.recyclerView);
        tabLayout=view.findViewById(R.id.tabLayout);
         
        
        
        PlayItem curPlayItem = PlayerManager.getInstance().getCurPlayItem();
        Log.e(TAG, curPlayItem.toString());
        if (curPlayItem == null) return;

        layoutManager = new CenterLayoutManager(getContext());
        recyclerView.setLayoutManager(layoutManager);
        if (curPlayItem.getType() == PlayerConstants.RESOURCES_TYPE_BROADCAST || curPlayItem.getType() == PlayerConstants.RESOURCES_TYPE_TV) {
            layoutManager.setSpeedFast(false);
        }
        if (curPlayItem.getType() == PlayerConstants.RESOURCES_TYPE_ALBUM) {
            ViewGroup.LayoutParams layoutParams = refreshLayout.getLayoutParams();
            if (layoutParams instanceof ConstraintLayout.LayoutParams) {
                ((ConstraintLayout.LayoutParams) layoutParams).topMargin = ResUtil.getDimen(R.dimen.y169);
                refreshLayout.setLayoutParams(layoutParams);
            }
        }

        if (isRefreshLayoutEnabled()) {
            refreshLayout.setEnableRefresh(true);
            refreshLayout.setEnableLoadMore(true);
            refreshLayout.setOnRefreshListener(new OnRefreshListener() {
                @Override
                public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                    loadPrevPage();
                }
            });
            refreshLayout.setOnLoadMoreListener(new OnLoadMoreListener() {
                @Override
                public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                    loadNextPager();
                }
            });
        }

        PlayerManager.getInstance().addPlayControlStateCallback(this.stateListener);

        if (useTabLayout()) {
            queryDateList();
        } else {
            tabLayout.setVisibility(View.GONE);
            mRefreshProgramFlag = REFRESH_PROGRAM_CHANGE_DATE;
            getProgramFromServer(null);
        }

    }

    @Override
    public void onDestroyView() {
        recyclerView.removeOnScrollListener(mRenderScrollListener);
        mHandler.removeCallbacksAndMessages(null);
        PlayerManager.getInstance().removePlayControlStateCallback(this.stateListener);
        if (useTabLayout() && mOnTabSelectedListener != null) {
            tabLayout.setOnTabSelectListener(null);
            mOnTabSelectedListener = null;
        }
        super.onDestroyView();
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        RecyclerView.Adapter adapter = recyclerView.getAdapter();
        if (adapter != null) {
            recyclerView.getAdapter().notifyDataSetChanged();
        }

        recyclerView.setAdapter(null);
        recyclerView.setAdapter(adapter);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        if (useTabLayout()) {
            initOnTabSelectedListener();
            tabLayout.setOnTabSelectListener(mOnTabSelectedListener);
        }
    }

    private void initOnTabSelectedListener() {
        if (mOnTabSelectedListener == null) {
            mOnTabSelectedListener = new OnTabSelectListener() {
                @Override
                public void onTabSelect(int position) {
                    mRefreshProgramFlag = REFRESH_PROGRAM_CHANGE_DATE;
                    getProgramFromServer(onlineTabDataList.get(position));
                    ButtonClickReportEvent event = null;
                    if (position == 0) {
                        event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_PLAY_YESTERDAY);
                        ReportHelper.getInstance().addEvent(event);
                    } else if (position == 1) {
                        event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_PLAY_TODAY);
                        ReportHelper.getInstance().addEvent(event);
                    } else if (position == 2) {
                        event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_PLAY_TOMORROW);
                        ReportHelper.getInstance().addEvent(event);
                    }
                }

                @Override
                public void onTabReselect(int position) {
                    if (!playItems.isEmpty()) {
                        recyclerView.scrollToPosition(0);
                    }
                }
            };
        }
    }

    public boolean useTabLayout() {
        return false;
    }

    public boolean isRefreshLayoutEnabled() {
        return false;
    }

    public void setEnableRefresh(boolean enable) {
        refreshLayout.setEnableRefresh(enable);
    }

    public void setEnableLoadMore(boolean enable) {
        refreshLayout.setEnableLoadMore(enable);
    }

    public void queryDateList() {

    }

    /**
     * 查询播单
     *
     * @param onlineTabData 如果使用TabLayout则应传入OnlineTabData，否则可传入null
     */
    public abstract void getProgramFromServer(@Nullable OnlineTabData onlineTabData);

    public void setTabs(List<OnlineTabData> onlineTabDataList) {
        this.onlineTabDataList = onlineTabDataList;
        List<Tab> tabs = new ArrayList<>();
        PlayItem curPlayItem = PlayerManager.getInstance().getCurPlayItem();
        for (int i = 0; i < onlineTabDataList.size(); i++) {
            Tab tab = new Tab();
            tab.title = onlineTabDataList.get(i).getTitle();
            tab.position = i;
            tabs.add(tab);
        }
        int currentTabPosition = findTabSelectedPosition(curPlayItem, onlineTabDataList);
        //没找到播放节目的下标，就通过PlayItem的日期进行匹配
        if (currentTabPosition == -1) {
            long playItemStartTime = 0;
            if (curPlayItem instanceof BroadcastPlayItem) {
                playItemStartTime = ((BroadcastPlayItem) curPlayItem).getTimeInfoData().getStartTime();
            } else if (curPlayItem instanceof TVPlayItem) {
                playItemStartTime = ((TVPlayItem) curPlayItem).getTimeInfoData().getStartTime();
            }
            if (playItemStartTime != 0) {
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                String curPlayItemDate = format.format(new Date(playItemStartTime));
                for (int i = 0; i < onlineTabDataList.size(); i++) {
                    Object data = onlineTabDataList.get(i).getData();
                    if (data instanceof ProgramDateData) {
                        if (((ProgramDateData) data).getDateTime().equals(curPlayItemDate)) {
                            currentTabPosition = i;
                        }
                    }
                }
            }
        }

        tabLayout.setTabs(tabs);
        tabLayout.setCurrentTab(currentTabPosition);
        if (mOnTabSelectedListener != null && currentTabPosition != -1)
            mOnTabSelectedListener.onTabSelect(currentTabPosition);
    }

    public int findTabSelectedPosition(PlayItem curPlayItem, List<OnlineTabData> onlineTabDataList) {
        return -1;
    }

    public void notifyPlayListChanged(List<PlayItem> playItemArrayList) {
        notifyPlayListChanged(playItemArrayList, false, false, -1);
    }

    public void notifyPlayListChangedWithDiffUtil(List<PlayItem> originList, List<PlayItem> newList) {
        //如果是全量更新，对比两数组差异，进行更新
        //利用DiffUtil.calculateDiff()方法，传入一个规则DiffUtil.Callback对象，和是否检测移动item的 boolean变量，得到DiffUtil.DiffResult 的对象
        DiffUtil.DiffResult diffResult = DiffUtil.calculateDiff(new OnlinePlayItemDiffCallback(originList, newList), true);
        playItems = newList;
        mAdapter.setDataList(playItems, false);
        //利用DiffUtil.DiffResult对象的dispatchUpdatesTo（）方法，传入RecyclerView的Adapter
        diffResult.dispatchUpdatesTo(mAdapter);
        addScrollEvent();
    }

    private void addScrollEvent() {
        addScrollEvent(true);
    }

    protected void addScrollEvent(boolean needRender) {
        addScrollEvent(needRender, true);
    }

    protected void addScrollEvent(boolean needRender, boolean needDelay) {
        long delay = 0;
        if (needDelay) {
            delay = DELAY_SCROLL_TIME_MS;
        }

        if (needRender) {
            //检查是否已经存在一个重新渲染的请求，如果存在则应删除。防止快速切换时出现重新渲染多次的情况
            if (mHandler.hasMessages(HANDLER_RENDER_ITEM)) {
                mHandler.removeMessages(HANDLER_RENDER_ITEM);
            }

            if (mRefreshProgramFlag == REFRESH_PROGRAM_CHANGE_PLAYITEM) {
                mHandler.sendEmptyMessage(HANDLER_RENDER_ITEM);
            } else {
                //按照视频里面的效果，先展示普通列表，过一段时间后再高亮直播项
                mHandler.sendEmptyMessageDelayed(HANDLER_RENDER_ITEM, delay);
            }

            //处理下播放器的节目单，让他能支持跨天
            addPlayItemToPlayList(playItems);
            return;
        }
        int msg = HANDLER_SCROLL_TO_PLAYING;
        //检查是否已经存在一个重新渲染的请求，如果存在则应删除。防止快速切换时出现重新渲染多次的情况
        if (mHandler.hasMessages(msg)) {
            mHandler.removeMessages(msg);
        }

        //按照视频里面的效果，先展示普通列表，过一段时间后再高亮直播项
        mHandler.sendEmptyMessageDelayed(msg, delay);
    }

    /**
     * 更新列表
     *
     * @param playItemArrayList   新数据数组，如果非增量更新，则该数组就是全部数据
     * @param isIncrementalUpdate 是否增量更新，如果false，后面的needAddAll、startPosition都没有意义
     *                            1. 当播放广播时，每天的播单数据会一次性获取到，因此不需要增量更新
     *                            2. 当播放专辑时，播单数据来源于PlayerControl，会分页返回数据，因此会增量更新。
     * @param needAddAll          是否需要添加playItemArrayList到当前维护的playItems里面。只有isIncrementalUpdate为true时有意义。
     *                            1. 当播放专辑时，列表数据来源于PlayerControl，数据每次加载或刷新都是有PlayerControl完成，
     *                            此时更新列表时，PlayerControl已经完成了数据的添加，所以无需再次添加，否则出现item重复
     * @param startPosition       新数组插入的位置。只有isIncrementalUpdate为true时有意义
     */
    public void notifyPlayListChanged(List<PlayItem> playItemArrayList, boolean isIncrementalUpdate, boolean needAddAll, int startPosition) {
        if (mAdapter == null) {
            playItems = playItemArrayList;

            //setLayoutAnimation来实现第一次显示的时候item的延迟加载动画
            Animation animation = AnimationUtils.loadAnimation(getContext(), R.anim.online_player_anim_item_play_item);
            LayoutAnimationController controller = new LayoutAnimationController(animation);
            controller.setDelay(0.1f);
            controller.setOrder(LayoutAnimationController.ORDER_NORMAL);
            recyclerView.setLayoutAnimation(controller);

            mAdapter = getPlayListAdapter(playItems);
            recyclerView.setAdapter(mAdapter);
            mAdapter.setOnItemClickListener(new BaseAdapter.OnItemClickListener<PlayItem>() {
                @Override
                public void onItemClick(View view, int viewType, PlayItem playItem, int position) {
                    onPlayListItemClick(view, viewType, playItem, position);
                }
            });
            setUnsubscribeToRvAdapter(mAdapter);
        } else if (mRefreshProgramFlag == REFRESH_PROGRAM_CHANGE_PLAYITEM ||
                mRefreshProgramFlag == REFRESH_PROGRAM_CHANGE_PLAYITEM_UNSCROLL) {
            if (isIncrementalUpdate) {
                //增量更新
                if (needAddAll) {
                    this.playItems.addAll(startPosition, playItemArrayList);
                }
                mAdapter.notifyItemRangeInserted(startPosition, playItemArrayList.size());
            } else {
                //如果是全量更新，对比两数组差异，进行更新
                //利用DiffUtil.calculateDiff()方法，传入一个规则DiffUtil.Callback对象，和是否检测移动item的 boolean变量，得到DiffUtil.DiffResult 的对象
                DiffUtil.DiffResult diffResult = DiffUtil.calculateDiff(new OnlinePlayItemDiffCallback(playItems, playItemArrayList), true);
                playItems = playItemArrayList;
                mAdapter.setDataList(playItems, false);
                //利用DiffUtil.DiffResult对象的dispatchUpdatesTo（）方法，传入RecyclerView的Adapter
                diffResult.dispatchUpdatesTo(mAdapter);
            }
        } else {
            //换页操作查询到的新数据
            playItems = playItemArrayList;
            mAdapter.resetPlayingToNormal();
            mAdapter.setDataList(playItems);
            recyclerView.scheduleLayoutAnimation();
        }

        addScrollEvent();
    }

    /**
     * 添加播单到播放器，支持跨天
     *
     * @param playItems
     */
    protected abstract void addPlayItemToPlayList(List<PlayItem> playItems);


    public void setUnsubscribeToRvAdapter(OnlineBasePlayListRvAdapter mAdapter) {

    }

//    protected abstract BaseAdapter.OnItemClickListener<PlayItem> getItemClickListener();

    protected abstract OnlineBasePlayListRvAdapter getPlayListAdapter(List<PlayItem> playItems);

    /**
     * 刷新播单并播放指定节目
     *
     * @param playItem
     */
    public abstract void onPlayListItemClick(View view, int viewType, PlayItem playItem, int position);

    public boolean isContainsPlayItem(List<PlayItem> itemList, PlayItem playItem) {
        boolean flag = false;
        if (playItem == null || playItem.getPlayUrl() == null) {
            return false;
        }
        for (PlayItem item : itemList) {
            if (playItem.getAudioId() == item.getAudioId()) {
                flag = true;
                break;
            }
        }
        return flag;
    }

    public void refreshPlayListAndUpdateItemOnPosition(int position) {
        mRefreshProgramFlag = REFRESH_PROGRAM_CHANGE_DATE;
    }

    /**
     * 加载上一页数据
     */
    public void loadPrevPage() {
        mRefreshProgramFlag = REFRESH_PROGRAM_CHANGE_PLAYITEM_UNSCROLL;
    }

    /**
     * 加载下一页数据
     */
    public void loadNextPager() {
        mRefreshProgramFlag = REFRESH_PROGRAM_CHANGE_PLAYITEM_UNSCROLL;
    }

    public void resetSmartRefreshLayoutNormal(boolean succeed) {
        refreshLayout.finishLoadMore(succeed);
        refreshLayout.finishRefresh(succeed);
    }

    @Override
    public void onResume() {
        super.onResume();
        isFront = true;
    }

    @Override
    public void onPause() {
        isFront = false;
        super.onPause();
    }

    @Override
    public boolean onBackPressedSupport() {
        isGotoBack = true;
        return super.onBackPressedSupport();
    }

    public boolean isGotoBack() {
        return isGotoBack;
    }

    /**
     * 是否在前台
     *
     * @return
     */
    public boolean isFront() {
        return isFront;
    }


    public void setItemCountViewGroupVisibility(int visibility) {
        ViewUtil.setViewVisibility(albumTitleTv, visibility);
        ViewUtil.setViewVisibility(playItemCountTv, visibility);
        ViewUtil.setViewVisibility(albumFlagTv, visibility);
        ViewUtil.setViewVisibility(sortIcon, visibility);
        ViewUtil.setViewVisibility(SortTipTv, visibility);
        ViewUtil.setViewVisibility(divider, visibility);
    }

    public void setItemCountViewGroupVisibilityWithoutTitleView(int visibility) {
        ViewUtil.setViewVisibility(playItemCountTv, visibility);
        ViewUtil.setViewVisibility(albumFlagTv, visibility);
        ViewUtil.setViewVisibility(sortIcon, visibility);
        ViewUtil.setViewVisibility(SortTipTv, visibility);
        ViewUtil.setViewVisibility(divider, visibility);
    }
}
