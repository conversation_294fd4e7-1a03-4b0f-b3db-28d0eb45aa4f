/*
 * Copyright 2017 Jess<PERSON>an
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.kaolafm.kradio.lib.utils.imageloader;

import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.widget.ImageView;

/**
 * ================================================
 * 这里是图片加载配置信息的基类,定义一些所有图片加载框架都可以用的通用参数
 * 每个 {@link BaseImageLoaderStrategy} 应该对应一个 {@link ImageConfig} 实现类
 * <p>
 * Created by <PERSON><PERSON><PERSON> on 8/5/16 15:19
 * <a href="mailto:<EMAIL>">Contact me</a>
 * <a href="https://github.com/JessYanCoding">Follow me</a>
 * ================================================
 */
public class ImageConfig {
    protected Bitmap bitmap;
    protected String url;
    protected ImageView imageView;

    /**
     * 占位符 由于皮肤不支持设置 id方式.
     */
    //protected int placeHolder;
    /**
     * 错误占位符
     */
    //protected int errorPic;

    /**
     * 占位符
     */
    protected Drawable placeHolderDrawale;

    /**
     * 错误占位符
     */
    protected Drawable errorPicDrawable;

    public String getUrl() {
        return url;
    }

    public ImageView getImageView() {
        return imageView;
    }

    public Drawable getPlaceHolder() {
        return placeHolderDrawale;
    }

    public Drawable getErrorPic() {
        return errorPicDrawable;
    }

    public Bitmap getBitmap() {
        return bitmap;
    }

    public void setBitmap(Bitmap bitmap) {
        this.bitmap = bitmap;
    }
}
