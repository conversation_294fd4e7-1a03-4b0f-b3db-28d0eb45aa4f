package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.content.IntentFilter;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;

import com.alibaba.fastjson.JSON;
import com.kaolafm.base.utils.SpUtil;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.player.bean.BroadcastRadioSimpleData;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.receiver.BydAccountStateReceiver;
import com.kaolafm.kradio.uitl.PlayStateListenerWrapper;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import java.util.ArrayList;

public class LauncherImpl implements LauncherInter {

    private static final String TAG = "LauncherImpl";
    public static final String BROADCAST_LIST = "BROADCAST_LIST";

    private PlayStateListenerWrapper wrapper = new PlayStateListenerWrapper() {
        @Override
        public void onPlayerPlaying(PlayItem playItem) {
            super.onPlayerPlaying(playItem);
            Log.i(TAG, "onPlayerPlaying");
            saveBroadcastList();
        }
    };

    BydAccountStateReceiver mLocatiopnBroadcast;

    @Override
    public void onStart(Object... args) {
        Log.i(TAG, "onStart");
    }

    @Override
    public void onCreate(Object... args) {
        Log.i(TAG, "onCreate");
        // 1. 实例化BroadcastReceiver子类 & IntentFilter
        mLocatiopnBroadcast = new BydAccountStateReceiver();
        IntentFilter intentFilter = new IntentFilter();
        // 2. 设置接收广播的类型
        intentFilter.addAction(BydAccountStateReceiver.ACCOUNT_STATE_ACTION);// 只有持有相同的action的接受者才能接收此广播
        // 3. 动态注册：调用Context的registerReceiver（）方法
        ((Context) args[0]).registerReceiver(mLocatiopnBroadcast, intentFilter);
        Log.i(TAG, "BydAccountStateReceiver,registerReceiver");


        /**
         *  @see KRadioBroadcastHistoryImpl
         */
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                PlayerManager.getInstance().addPlayControlStateCallback(wrapper);
            }
        }, 500);
        try {
            SpUtil.init(AppDelegate.getInstance().getContext());
            String json = SpUtil.getString(BROADCAST_LIST, "");
            if (TextUtils.isEmpty(json)) {
                Log.i(TAG, "onCreate：empty");
                return;
            }
            SpUtil.putString(BROADCAST_LIST, "");
            Log.i(TAG, "onCreate：" + json);
            ArrayList<BroadcastRadioSimpleData> arrayList = (ArrayList<BroadcastRadioSimpleData>) JSON.parseArray(json, BroadcastRadioSimpleData.class);
            Log.i(TAG, "onCreate：" + arrayList.toString());
            PlayerManagerHelper.getInstance().addBroadcastRadioSimpleItems(arrayList);
        } catch (Exception e) {
            Log.i(TAG, "onCreate" + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public void onResume(Object... args) {
        Log.i(TAG, "onResume");
    }

    @Override
    public void onPause(Object... args) {
        Log.i(TAG, "onPause");
    }

    @Override
    public void onStop(Object... args) {
        Log.i(TAG, "onStop");
        saveBroadcastList();
    }

    @Override
    public void onRestart(Object... args) {
        Log.i(TAG, "onStart");
    }

    @Override
    public void onDestory(Object... args) {
        Log.i(TAG, "onDestory");
        ((Context) args[0]).unregisterReceiver(mLocatiopnBroadcast);
        Log.i(TAG, "BydAccountStateReceiver,unregisterReceiver");
    }

    private void saveBroadcastList() {
        /**
         *  @see KRadioBroadcastHistoryImpl
         */
        ArrayList<BroadcastRadioSimpleData> arrayList = PlayerManagerHelper.getInstance().getBroadcastRadioSimpleItems();
        SpUtil.init(AppDelegate.getInstance().getContext());
        try {
            if (arrayList != null && arrayList.size() > 0) {
                String json = JSON.toJSONString(arrayList);
                Log.i(TAG, "save：" + json);
                SpUtil.putString(BROADCAST_LIST, json);
            }
        } catch (Exception e) {
            Log.i(TAG, "onStop：" + e.toString());
            e.printStackTrace();
        }
    }
}
