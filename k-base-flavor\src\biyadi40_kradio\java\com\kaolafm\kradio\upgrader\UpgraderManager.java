package com.kaolafm.kradio.upgrader;

import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import androidx.core.content.FileProvider;
import android.util.Log;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.upgrader.net.model.UpdateData;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;

import okhttp3.Call;
import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

public class UpgraderManager {

    public final static String TAG = "UpgraderManager";
    public final static String UPGRADE_APK_NAME = "Kradio_upgrade.apk";

    public String downloadPath;

    private String apkurl;

    private int progress;

    private boolean interceptFlag = false;

    private DownloadApkListener mydownloadListener;


    private static class SingletonHoler {
        private static UpgraderManager instance = new UpgraderManager();
    }

    public UpgraderManager() {
    }

    public static UpgraderManager getInstance() {
        return UpgraderManager.SingletonHoler.instance;
    }

    public interface DownloadApkListener {
        void onStart();

        void onProgress(int p);

        void onFinish(String path);

        void onError(String msg);
    }

    /**
     * 下载apk
     *
     * @param downloadApkListener 下载回调
     */
    public void downApkFile(UpdateData mUpdateData, DownloadApkListener downloadApkListener) {
        downloadPath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).getAbsolutePath();
        init(mUpdateData, downloadApkListener);
        downloadAPK(mUpdateData.getUpdateUrl());
//        new Thread(mdownApkRunnable).start();
    }

    private void init(UpdateData mUpdateData, DownloadApkListener downloadApkListener) {
        apkurl = mUpdateData.getUpdateUrl();
        mydownloadListener = downloadApkListener;
    }


    /**
     * get请求
     *
     * @param address
     * @param callback
     */

    public void get(String address, okhttp3.Callback callback) {
        OkHttpClient client = new OkHttpClient();
        FormBody.Builder builder = new FormBody.Builder();
        FormBody body = builder.build();
        Request request = new Request.Builder()
                .url(address)
                .build();
        client.newCall(request).enqueue(callback);
    }

    private void downloadAPK(String apkurl) {
        get(apkurl, new okhttp3.Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.i(TAG, "onFailure: e = " + e);
                mydownloadListener.onError(e.getMessage());
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (mydownloadListener != null) {
                    mydownloadListener.onStart();
                }
                // 改成自己需要的存储位置
                File file = new File(downloadPath + File.separator + UPGRADE_APK_NAME);
                Log.i(TAG, "mdownApkRunnable() file=" + file.getPath());
                if (file.exists()) {
                    file.delete();
                }

                InputStream is = null;//输入流
                FileOutputStream fos = null;//输出流
                try {
                    is = response.body().byteStream();//获取输入流
                    long total = response.body().contentLength();//获取文件大小
                    if (is != null) {
                        Log.i("SettingPresenter", "onResponse: 不为空");
                        fos = new FileOutputStream(file);
                        byte[] buf = new byte[1024];
                        int ch = -1;
                        int process = 0;
                        while ((ch = is.read(buf)) != -1) {
                            fos.write(buf, 0, ch);
                            process += ch;
                            progress = (int) (((float) process / total) * 100);
                            if (mydownloadListener != null) {
                                mydownloadListener.onProgress(progress);//这里就是关键的实时更新进度了！
                            }
                        }
                    }
                    fos.flush();
                    // 下载完成
                    if (fos != null) {
                        fos.close();
                    }
                    if (mydownloadListener != null) mydownloadListener.onFinish(file.getPath());
                } catch (Exception e) {
                    if (mydownloadListener != null) mydownloadListener.onError(e.getMessage());
                    Log.i("SettingPresenter", e.toString());
                } finally {
                    try {
                        if (is != null)
                            is.close();
                    } catch (IOException e) {
                    }
                    try {
                        if (fos != null)
                            fos.close();
                    } catch (IOException e) {
                    }
                }

            }
        });
    }

    private Runnable mdownApkRunnable = new Runnable() {
        @Override
        public void run() {
            if (mydownloadListener != null) {
                mydownloadListener.onStart();
            }
            // 改成自己需要的存储位置
            File file = new File(downloadPath + File.separator + UPGRADE_APK_NAME);
            Log.i(TAG, "mdownApkRunnable() file=" + file.getPath());
            if (file.exists()) {
                file.delete();
            }

            InputStream inputStream = null;
            FileOutputStream outputStream = null;

            try {
                URL url = new URL(apkurl);
                HttpURLConnection conn = (HttpURLConnection) url
                        .openConnection();
                conn.connect();
                int length = conn.getContentLength();
                inputStream = conn.getInputStream();
                outputStream = new FileOutputStream(file);

                int count = 0;
                byte buf[] = new byte[1024];

                do {
                    int numread = inputStream.read(buf);
                    count += numread;
                    progress = (int) (((float) count / length) * 100);
                    // 更新进度
                    if (mydownloadListener != null)
                        mydownloadListener.onProgress(progress);
                    Log.i(TAG, "file download: " + progress + "");

                    if (numread <= 0) {
                        // 下载完成通知安装
                        if (mydownloadListener != null)
                            mydownloadListener.onFinish(file.getPath());
                        break;
                    }
                    outputStream.write(buf, 0, numread);
                    outputStream.flush();
                } while (!interceptFlag);// 点击取消就停止下载.

            } catch (Exception e) {
                if (mydownloadListener != null)
                    mydownloadListener.onError("" + e.getMessage());
                e.printStackTrace();
            } finally {
                try {
                    if (inputStream != null) {
                        inputStream.close();
                    }

                    if (outputStream != null) {
                        outputStream.close();
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

        }
    };

    public void installApk(String filePath) {
        try {
            Log.i(TAG, "开始执行安装: " + filePath);
            File apkFile = new File(filePath);
            Intent intent = new Intent(Intent.ACTION_VIEW);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                Log.w(TAG, "版本大于 N ，开始使用 fileProvider 进行安装");
                intent.setFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                Uri contentUri = FileProvider.getUriForFile(
                        AppDelegate.getInstance().getContext()
                        , "com.kaolafm.kradio.k_radio_horizontal.fileprovider"
                        , apkFile);
                intent.setDataAndType(contentUri, "application/vnd.android.package-archive");
            } else {
                Log.w(TAG, "正常进行安装");
                intent.setDataAndType(Uri.fromFile(apkFile), "application/vnd.android.package-archive");
            }
            AppDelegate.getInstance().getContext().startActivity(intent);
        } catch (Exception e) {
            // todo 安装失败的操作
        }

    }
}
