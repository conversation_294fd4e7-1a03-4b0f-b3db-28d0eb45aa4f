<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/component_card_bg_1"
    tools:background="@drawable/component_card_bg">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.kaolafm.kradio.component.ui.base.view.OvalImageView
            android:id="@+id/card_bg_iv"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:oval_radius="@dimen/m8" />


        <com.kaolafm.kradio.component.ui.base.view.OvalImageView
            android:id="@+id/card_pic_iv"
            android:layout_width="@dimen/m72"
            android:layout_height="@dimen/m72"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:layout_marginRight="@dimen/m15"
            android:layout_marginBottom="@dimen/m15"
            app:oval_radius="@dimen/m8"
            tools:src="@drawable/splash_yunting" />

        <ImageView
            android:id="@+id/vip_icon"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/m20"
            app:layout_constraintTop_toTopOf="@+id/card_pic_iv"
            app:layout_constraintLeft_toLeftOf="@+id/card_pic_iv"
            android:layout_alignLeft="@+id/card_pic_iv"
            android:layout_alignTop="@+id/card_pic_iv"
            android:scaleType="fitStart"
            tools:src="@drawable/comprehensive_icon_vip" />

        <com.kaolafm.kradio.component.ui.base.view.KradioTextView
            android:id="@+id/card_title_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            android:layout_marginLeft="@dimen/m20"
            android:layout_marginTop="@dimen/m20"
            android:lineSpacingExtra="@dimen/m7"
            android:textColor="@color/text_color_7"
            android:textSize="@dimen/m26"
            app:kt_font_weight="0.3"
            tools:text="我是标题啊" />

        <ImageView
            android:id="@+id/card_play_iv"
            android:layout_width="@dimen/m38"
            android:layout_height="@dimen/m38"
            app:layout_constraintRight_toRightOf="@+id/card_pic_iv"
            app:layout_constraintBottom_toBottomOf="@+id/card_pic_iv"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:layout_marginRight="@dimen/m7"
            android:layout_marginBottom="@dimen/m7"
            android:src="@drawable/component_play_icon_2" />

        <com.kaolafm.kradio.component.ui.base.view.RateView
            android:id="@+id/card_layout_playing"
            android:layout_width="@dimen/m38"
            android:layout_height="@dimen/m38"
            app:layout_constraintRight_toRightOf="@+id/card_pic_iv"
            app:layout_constraintBottom_toBottomOf="@+id/card_pic_iv"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:layout_marginRight="@dimen/m7"
            android:layout_marginBottom="@dimen/m7"
            app:lottie_autoPlay="true"
            app:lottie_fileName="lottie/rate.json"
            app:lottie_loop="true" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</RelativeLayout>