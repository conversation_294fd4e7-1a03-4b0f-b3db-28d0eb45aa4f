<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/record_box"
    android:layout_width="@dimen/m640"
    android:layout_height="wrap_content"
    android:paddingTop="@dimen/m28"
    android:paddingBottom="@dimen/m24"
    android:clipToPadding="false"
    android:background="@drawable/comprehensive_record_bg">

    <!--  中间区域  -->
    <ImageButton
        android:id="@+id/micIv"
        android:layout_width="@dimen/m130"
        android:layout_height="@dimen/m130"
        android:background="@color/transparent"
        android:scaleType="fitXY"
        android:src="@drawable/voice_record_mic_comprehensive"
        app:layout_constraintBottom_toTopOf="@id/recordTipText"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/recordTipText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/m10"
        android:textSize="@dimen/m26"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/micIv"
        app:layout_constraintBottom_toBottomOf="parent"
        android:textColor="@color/record_text_color"
        tools:text="@string/voice_message_click_to_start" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/recordLayout"
        android:layout_width="@dimen/m130"
        android:layout_height="@dimen/m130"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@id/micIv"
        app:layout_constraintEnd_toEndOf="@id/micIv"
        app:layout_constraintStart_toStartOf="@id/micIv"
        app:layout_constraintTop_toTopOf="@id/micIv">

        <ImageView
            android:id="@+id/recordIv"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY"
            android:src="@drawable/voice_button_empty_bg_comprehensive" />

        <com.kaolafm.kradio.common.widget.PlayAnimView
            android:id="@+id/live_record_anim_image"
            android:visibility="gone"
            android:layout_width="@dimen/m44"
            android:layout_height="@dimen/m44"
            android:layout_marginBottom="@dimen/m4"
            app:layout_constraintBottom_toTopOf="@id/recordTextView"
            app:layout_constraintLeft_toLeftOf="@id/recordIv"
            app:layout_constraintRight_toRightOf="@id/recordIv"
            app:playLineCount="4"
            app:playStrokeCenterColor="#FFFFFFFF"
            app:playStrokeEndColor="#26FFFFFF"
            app:playStrokeStartColor="#26FFFFFF"
            app:playStrokeWidth="@dimen/x1"
            app:playFullEndColor="#FFFFFFFF"
            app:playFullStartColor="#FFFFFFFF"
            app:playRatioWidth="1"/>

        <ImageView
            android:id="@+id/live_send_anim"
            android:visibility="gone"
            android:layout_width="@dimen/m106"
            android:layout_height="@dimen/m106"
            android:src="@drawable/voice_send_loading_comprehensive"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/recordTextView"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/m12"
            android:textSize="@dimen/m26"
            android:textColor="@color/record_text_color"
            />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <!-- 试听 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/live_listen_button_layout"
        android:layout_width="@dimen/m104"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/m55"
        android:visibility="invisible"
        tools:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toLeftOf="@id/micIv">

        <ImageView
            android:id="@+id/live_listen_message_image"
            android:layout_width="@dimen/m104"
            android:layout_height="@dimen/m104"
            android:layout_marginBottom="@dimen/m24"
            android:scaleType="fitXY"
            android:src="@drawable/voice_listen_comprehensive"
            app:layout_constraintBottom_toTopOf="@id/live_listen_message_text"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />

        <TextView
            android:id="@+id/live_listen_message_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/m24"
            android:layout_marginTop="@dimen/m10"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:textColor="@color/record_text_color"
            android:text="@string/voice_message_audition" />

        <com.kaolafm.kradio.common.widget.PlayAnimView
            android:id="@+id/live_listen_anim_image"
            android:layout_width="@dimen/m104"
            android:layout_height="@dimen/m104"
            android:visibility="gone"
            app:layout_constraintDimensionRatio="W,1:1"
            app:layout_constraintTop_toTopOf="@id/live_listen_message_image"
            app:layout_constraintBottom_toBottomOf="@id/live_listen_message_image"
            app:layout_constraintLeft_toLeftOf="@id/live_listen_message_image"
            app:layout_constraintRight_toRightOf="@id/live_listen_message_image"
            app:playLineCount="4"
            app:playStrokeCenterColor="#FFFFFFFF"
            app:playStrokeEndColor="#26FFFFFF"
            app:playStrokeStartColor="#26FFFFFF"
            app:playStrokeWidth="@dimen/x1"
            app:playFullEndColor="#FFFFFFFF"
            app:playFullStartColor="#FFFFFFFF"
            app:playRatioWidth="0.4"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 取消 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/live_cancel_button_layout"
        android:layout_width="@dimen/m104"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/m55"
        android:visibility="invisible"
        tools:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/micIv">

        <ImageView
            android:id="@+id/live_cancel_message_image"
            app:layout_constraintBottom_toTopOf="@id/live_cancel_message_text"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_marginBottom="@dimen/m24"
            android:layout_width="@dimen/m104"
            android:layout_height="@dimen/m104"
            android:scaleType="fitXY"
            android:src="@drawable/voice_cancel_comprehensive" />

        <TextView
            android:id="@+id/live_cancel_message_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/m24"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:textColor="@color/record_text_color"
            android:text="@string/voice_message_cancel" />
    </androidx.constraintlayout.widget.ConstraintLayout>



</androidx.constraintlayout.widget.ConstraintLayout>