package com.kaolafm.kradio.common.utils;

import java.time.LocalTime;

public class TimeUtil {

    /**
     * @param beginTimeStr "08:00"开始时间
     * @param endTimeStr "17:00" 结束时间
     * @return
     */
    public static boolean isWithinTime(String beginTimeStr,String endTimeStr){
        LocalTime startTime = LocalTime.parse(beginTimeStr);
        LocalTime endTime = LocalTime.parse(endTimeStr);
        LocalTime now = LocalTime.now();
        return !now.isBefore(startTime) && !now.isAfter(endTime);
    }

    /**
     * @param timeStr 给定时间 11:20,现在时间 11;40,返回true
     * @return 现在时间在给定之后吗？
     */
    public static boolean isAfterTime(String timeStr){
        LocalTime endTime = LocalTime.parse(timeStr);
        LocalTime now = LocalTime.now();
        return now.isAfter(endTime);
    }

    public static int getDelay(String timeStr) {
        LocalTime compareTime = LocalTime.parse(timeStr);
        LocalTime now = LocalTime.now();
        int delay = ((compareTime.getHour()-now.getHour())*60+compareTime.getMinute()-now.getMinute())*60+compareTime.getSecond()-now.getSecond();
        return delay;
    }

    public static int getDelay(LocalTime compareTime, LocalTime now) {
        int delay = ((compareTime.getHour()-now.getHour())*60+compareTime.getMinute()-now.getMinute())*60+compareTime.getSecond()-now.getSecond();
        return delay;
    }
}
