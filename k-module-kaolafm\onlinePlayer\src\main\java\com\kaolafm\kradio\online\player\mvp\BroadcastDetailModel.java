package com.kaolafm.kradio.online.player.mvp;

import com.kaolafm.kradio.common.bean.BroadcastRadioDetailData;
import com.kaolafm.kradio.lib.base.mvp.BaseModel;
import com.kaolafm.opensdk.api.broadcast.BroadcastDetails;
import com.kaolafm.opensdk.api.broadcast.BroadcastRequest;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.trello.rxlifecycle3.LifecycleTransformer;

public class BroadcastDetailModel extends BaseModel {

    private final LifecycleTransformer mLifecycleTransformer;

    BroadcastDetailModel(LifecycleTransformer lifecycleTransformer) {
        mLifecycleTransformer = lifecycleTransformer;
    }

    public void getBroadcastDetail(long id, HttpCallback<BroadcastRadioDetailData> httpCallback) {
        BroadcastRequest broadcastRequest = new BroadcastRequest().bindLifecycle(mLifecycleTransformer);

        broadcastRequest.getBroadcastDetails(id, new HttpCallback<BroadcastDetails>() {
            @Override
            public void onSuccess(BroadcastDetails broadcastDetails) {
                try {
                    if (broadcastDetails != null) {
                        httpCallback.onSuccess(toClientBroadcastRadioDetailData(broadcastDetails));
                    } else {
                        httpCallback.onError(new ApiException(-1, "数据为空"));
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    httpCallback.onError(new ApiException(-1, e.getMessage() == null ? "数据为空" : e.getMessage()));
                }
            }

            @Override
            public void onError(ApiException e) {
                httpCallback.onError(e);
            }
        });

    }

    private BroadcastRadioDetailData toClientBroadcastRadioDetailData(BroadcastDetails broadcastDetails) {
        BroadcastRadioDetailData brdd = new BroadcastRadioDetailData();
        brdd.setBroadcastId(broadcastDetails.getBroadcastId());
        brdd.setClassifyId(broadcastDetails.getClassifyId());
        brdd.setClassifyName(broadcastDetails.getClassifyName());
        brdd.setFreq(broadcastDetails.getFreq());
        brdd.setIcon(broadcastDetails.getIcon());
        brdd.setImg(broadcastDetails.getImg());
        brdd.setIsSubscribe(broadcastDetails.getIsSubscribe());
        brdd.setName(broadcastDetails.getName());
        brdd.setLikedNum(broadcastDetails.getLikedNum());
        brdd.setOnLineNum(broadcastDetails.getOnLineNum());
        brdd.setPlayUrl(broadcastDetails.getPlayUrl());
        brdd.setRoomId(broadcastDetails.getRoomId());
        brdd.setStatus(broadcastDetails.getStatus());
        return brdd;
    }

    @Override
    public void destroy() {

    }
}
