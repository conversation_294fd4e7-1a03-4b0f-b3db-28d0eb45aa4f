package com.kaolafm.kradio.util;
 
import android.content.Context;
import android.util.Log;
import android.view.MotionEvent;
import android.view.VelocityTracker;
import android.widget.Toast;

import com.incall.serversdk.interactive.DoubleInteractiveProxy;
import com.kaolafm.kradio.flavor.BuildConfig;

/**
 * Multi Finger Screenshot Gesture Monitor
 *
 */
public class MultiGestureMonitor {
    private static final String TAG = MultiGestureMonitor.class.getSimpleName();
 
    private boolean DBG = true;
 
    private final int SUPPORT_FINGER_COUNTS_3 = 3;
    private final int SUPPORT_FINGER_COUNTS_4 = 4;

    private final int HORIZONTAL_SLIDE_OFFSET_DISTANCEY = 300;
    private final int HORIZONTAL_SLIDE_OFFSET_DISTANCEX = 160;

    private final float BASEVELOCOTY = 45;
 
    private int mode;
    private float mOldx0;
    private float mOldy0;
    private float mOldx1;
    private float mOldy1;
    private float mOldx2;
    private float mOldy2;
    private float mVelocityX0;
    private float mVelocityY0;
 
    private Context mContext;
    private VelocityTracker mVelocityTracker;
 
    private static MultiGestureMonitor sInstance;

    public static MultiGestureMonitor getInstance(Context context) {
        if (sInstance == null) {
           sInstance = new MultiGestureMonitor(context);
        }
        return sInstance;
    }
 
    private MultiGestureMonitor(Context context) {
        mContext = context;
    }
 
    public void onTouchEvent(MotionEvent event) {
        int action = event.getAction();
        switch (action & MotionEvent.ACTION_MASK) {
        case MotionEvent.ACTION_DOWN:
            if (mVelocityTracker == null) {
                mVelocityTracker = VelocityTracker.obtain();
            } else {
                mVelocityTracker.clear();
            }
            mVelocityTracker.addMovement(event);
            mode = 1;
            break;
        case MotionEvent.ACTION_MOVE:
            if (mVelocityTracker != null) {
                mVelocityTracker.addMovement(event);
                mVelocityTracker.computeCurrentVelocity(1000);
                mVelocityX0 = mVelocityTracker.getXVelocity();
                mVelocityY0 = mVelocityTracker.getYVelocity();
            }
            break;
        case MotionEvent.ACTION_POINTER_DOWN:
            mode += 1;
            if (mode == SUPPORT_FINGER_COUNTS_3 || mode == SUPPORT_FINGER_COUNTS_4) {
                try {
                    mOldx0 = event.getX(event.getPointerId(0));
                    mOldy0 = event.getY(event.getPointerId(0));
                    mOldx1 = event.getX(event.getPointerId(1));
                    mOldy1 = event.getY(event.getPointerId(1));
                    mOldx2 = event.getX(event.getPointerId(2));
                    mOldy2 = event.getY(event.getPointerId(2));
 
                    if (DBG) {
                        Log.v(TAG, "oldx0:" + mOldx0 + ",oldy0:" + mOldy0 + ",oldx1:" + mOldx1 + ",oldy1:"
                                + mOldy1 + ",mOldx2=" + mOldx2 + ",mOldy2=" + mOldy2);
                    }
                } catch (IllegalArgumentException e) {
                    e.printStackTrace();
                }
            }
            break;
        case MotionEvent.ACTION_POINTER_UP:
            if (mode == SUPPORT_FINGER_COUNTS_3 || mode == SUPPORT_FINGER_COUNTS_4) {
                try {
                    float newx0 = event.getX(event.getPointerId(0));
                    float newy0 = event.getY(event.getPointerId(0));
                    float newx1 = event.getX(event.getPointerId(1));
                    float newy1 = event.getY(event.getPointerId(1));
                    float newx2 = event.getX(event.getPointerId(2));
                    float newy2 = event.getY(event.getPointerId(2));
 
                    if (DBG) {
                        Log.d(TAG,
                                "newx0:" + (Math.abs(newx0 - mOldx0) > HORIZONTAL_SLIDE_OFFSET_DISTANCEX) + ",newy0:"
                                        + (Math.abs(newy0 - mOldy0) < HORIZONTAL_SLIDE_OFFSET_DISTANCEY) + ",newx1:"
                                        + (Math.abs(mVelocityX0) > BASEVELOCOTY));
 
                        Log.d(TAG, "mVelocityX0=" + mVelocityX0 + ",mVelocityY0=" + mVelocityY0);
                    }

                    if (Math.abs(newx0 - mOldx0) > HORIZONTAL_SLIDE_OFFSET_DISTANCEX
                            && Math.abs(newy0 - mOldy0) < HORIZONTAL_SLIDE_OFFSET_DISTANCEY
                            && Math.abs(mVelocityX0) > BASEVELOCOTY) {
                        if (newx0 - mOldx0 > HORIZONTAL_SLIDE_OFFSET_DISTANCEX) {
                            sendSlideEvent(1);
                        } else if (mOldx0 - newx0 > HORIZONTAL_SLIDE_OFFSET_DISTANCEX) {
                            sendSlideEvent(0);
                        }
                    }
                } catch (IllegalArgumentException e) {
                    e.printStackTrace();
                }
            }
            mode -= 1;
            break;
        case MotionEvent.ACTION_UP:
        case MotionEvent.ACTION_CANCEL:
            Log.v(TAG, "ACTION_UP or ACTION_CANCEL");
            mode = 0;
            if (mVelocityTracker != null) {
                mVelocityTracker.recycle();
                mVelocityTracker = null;
            }
            resetToInitValue();
            break;
        }
    }

    /**
     *
     * @param direction 0:三指左划操作 1:三指右划操作
     */
    private void sendSlideEvent(int direction) {
        if (BuildConfig.DEBUG) {//测试用
            Toast.makeText(mContext, "direction=" + direction, Toast.LENGTH_LONG).show();
        }
        // 5代表在线音乐
        DoubleInteractiveProxy.getInstance().sendSlidingEvent(5, direction);
    }
 
    private void resetToInitValue() {
        mOldx0 = 0;
        mOldy0 = 0;
        mOldx1 = 0;
        mOldy1 = 0;
        mOldx2 = 0;
        mOldy2 = 0;
    }
}