package com.kaolafm.kradio.auto.appwidget.remoteviews;

import android.content.Context;
import androidx.annotation.IntDef;
import android.util.Log;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.SharedPreferenceUtil;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

public class RemoteViewFactory {

    public static final int TYPE_ONE = 1;
    public static final int TYPE_TWO = 2;
    public static final int TYPE_THREE = 3;
    public static final int TYPE_FOUR = 4;
    private static final String TAG = "KLAppWidgetViewFactory";
    private IRemoteViews mIRemoteViews;
    private static final String SP_NAME = "widget.sp";
    private static final String KEY_WIDGET_NAME = "widget_name";
    RemoteView_One mRemoteView_One = new RemoteView_One();
    RemoteView_Two mRemoteView_Two = new RemoteView_Two();
    RemoteView_Three mRemoteView_Three = new RemoteView_Three();
    RemoteView_Four mRemoteView_Four = new RemoteView_Four();
    private static SharedPreferenceUtil mSpUtil;

    private RemoteViewFactory() {
    }

    private static class SingletonInstance {
        private static final RemoteViewFactory INSTANCE = new RemoteViewFactory();
    }

    public static RemoteViewFactory getInstance() {
        return SingletonInstance.INSTANCE;
    }

    public IRemoteViews getIRemoteViewsByDefault() {
        Log.i(TAG, "getIRemoteViewsByDefault");
        if (mIRemoteViews == null) {
            Log.i(TAG, "getIRemoteViewsByDefault mIRemoteViews is null");
            initDefaultRemoteView();
        }
        return mIRemoteViews;
    }

    private IRemoteViews initDefaultRemoteView() {
        mSpUtil = SharedPreferenceUtil
                .newInstance(AppDelegate.getInstance().getContext(), SP_NAME, Context.MODE_PRIVATE);
        int type = mSpUtil.getInt(KEY_WIDGET_NAME, TYPE_TWO);
        return getRemoteView(type);
    }

    public IRemoteViews getRemoteView(@ViewType int type) {
        Log.i(TAG, "getRemoteView : " + type);
        switch (type) {
            case TYPE_ONE:
                mIRemoteViews = mRemoteView_One;
                break;
            case TYPE_TWO:
                mIRemoteViews = mRemoteView_Two;
                break;
            case TYPE_THREE:
                mIRemoteViews = mRemoteView_Three;
                break;
            case TYPE_FOUR:
                mIRemoteViews = mRemoteView_Four;
                break;
        }
        saveWidgetType(type);
        return mIRemoteViews;
    }

    private void saveWidgetType(int type) {
        if (mSpUtil == null) {
            mSpUtil = SharedPreferenceUtil
                    .newInstance(AppDelegate.getInstance().getContext(), SP_NAME, Context.MODE_PRIVATE);
        }
        mSpUtil.putInt(KEY_WIDGET_NAME, type);
    }

    @Retention(RetentionPolicy.SOURCE)
    @IntDef({RemoteViewFactory.TYPE_ONE, RemoteViewFactory.TYPE_TWO, RemoteViewFactory.TYPE_THREE, RemoteViewFactory.TYPE_FOUR})
    public @interface ViewType {
    }
}