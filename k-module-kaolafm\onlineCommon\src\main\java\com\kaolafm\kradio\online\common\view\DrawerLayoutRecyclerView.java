package com.kaolafm.kradio.online.common.view;

import android.content.Context;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.ViewConfiguration;
import android.view.ViewParent;

public class DrawerLayoutRecyclerView extends RecyclerView {
    float lastX = 0;
    private int mTouchSlop;

    public DrawerLayoutRecyclerView(Context context) {
        this(context, null);
    }

    public DrawerLayoutRecyclerView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public DrawerLayoutRecyclerView(Context context, @Nullable AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        final ViewConfiguration configuration = ViewConfiguration.get(getContext());
        mTouchSlop = configuration.getScaledTouchSlop();
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        final int action = ev.getActionMasked();
        ViewParent parent = getParent();
        switch (action) {
            case MotionEvent.ACTION_DOWN:
                if (parent != null) {
                    parent.requestDisallowInterceptTouchEvent(true);
                }
                break;
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                if (parent != null) {
                    parent.requestDisallowInterceptTouchEvent(false);
                }
                break;
        }
        return super.onInterceptTouchEvent(ev);
    }

    @Override
    public boolean onTouchEvent(MotionEvent ev) {
        final int action = ev.getActionMasked();
        boolean unIntercept = true;    //不拦截
        boolean needNotify = false;
        switch (action) {
            case MotionEvent.ACTION_DOWN:
                break;
            case MotionEvent.ACTION_MOVE:
                if (ev.getX() - lastX > mTouchSlop) {
                    //左滑
                    if (!canScrollHorizontally(-1)) {
                        //已经滑动到最左边，无法再往左滑
                        unIntercept = false;
                        needNotify = true;
                    }
                } else if (lastX - ev.getX() > mTouchSlop) {
                    //向右滑动
                    if (!canScrollHorizontally(1)) {
                        //已经滑动到最右边，无法再往右划
                        unIntercept = false;
                        needNotify = true;
                    }
                } else {
                    unIntercept = false;
                    needNotify = true;
                }
                break;
        }
        lastX = ev.getX();
        if (needNotify) {
            ViewParent parent = getParent();
            if (parent != null) {
                parent.requestDisallowInterceptTouchEvent(unIntercept);
            }
        }
        return super.onTouchEvent(ev);
    }
}
