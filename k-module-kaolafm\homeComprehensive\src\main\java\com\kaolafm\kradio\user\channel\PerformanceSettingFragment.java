package com.kaolafm.kradio.user.channel;


import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;
import com.kaolafm.kradio.k_kaolafm.R;
import me.yokeyword.fragmentation.SupportFragment;
import com.kaolafm.kradio.lib.base.mvp.IPresenter;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.lib.utils.ResUtil;
import java.util.ArrayList;
import java.util.List;

public class PerformanceSettingFragment extends BaseFragment implements BaseAdapter.OnItemClickListener {

    RecyclerView settingRecyclerview;
    FrameLayout settingFragmentLayout;

    LinearLayoutManager mLinearLayoutManager;

    PerformanceSettingAdapter performanceSettingAdapter;
    ImageView loginCloseBtn;

    @Override
    public int getLayoutId() {
        return R.layout.user_fragment_performance_setting;
    }

    @Override
    protected int getLayoutId_Tow() {
        return 0;
    }

    @Override
    protected IPresenter createPresenter() {
        return null;
    }


    @Override
    public void initView(View view) {

        settingRecyclerview=view.findViewById(R.id.setting_recyclerview);
        settingFragmentLayout=view.findViewById(R.id.setting_fragment_layout);
        loginCloseBtn=view.findViewById(R.id.login_close_btn);

        String quarterStr = getString(R.string.quarter_str);
        ((TextView) view.findViewById(R.id.login_title)).setText(getString(R.string.person_center_performance_setting, quarterStr, quarterStr, quarterStr));
        mLinearLayoutManager = new LinearLayoutManager(getContext(), LinearLayoutManager.VERTICAL, false);
        settingRecyclerview.setLayoutManager(mLinearLayoutManager);
        performanceSettingAdapter = new PerformanceSettingAdapter();
        settingRecyclerview.setAdapter(performanceSettingAdapter);
        performanceSettingAdapter.setDataList(getDataList());

        performanceSettingAdapter.setOnItemClickListener(this);
        performanceSettingAdapter.setSelected(0);

        loginCloseBtn.setOnClickListener(v -> PerformanceSettingFragment.this.pop());

        SupportFragment fragment = new AnimationSettingFragment();
        loadRootFragment(R.id.setting_fragment_layout, fragment);
    }

    private List<PerformanceSettingAdapter.PerformanceItemBean> getDataList() {
        String[] strings = ResUtil.getStringArray(R.array.performance_setting_title);
        List<PerformanceSettingAdapter.PerformanceItemBean> list = new ArrayList<>();
        for (int i = 0; i < strings.length; i++) {
            PerformanceSettingAdapter.PerformanceItemBean performanceItemBean = new PerformanceSettingAdapter.PerformanceItemBean();
            performanceItemBean.setTitle(strings[i]);
            performanceItemBean.setSelected(false);
            list.add(performanceItemBean);
        }
        return list;
    }

    @Override
    public void onItemClick(View view, int viewType, Object o, int position) {
        switch (position) {
            case 0:
                SupportFragment fragment = new AnimationSettingFragment();
                loadRootFragment(R.id.setting_fragment_layout, fragment);
                break;
            case 1:
                SupportFragment fragment2 = new ImageSettingFragment();
                loadRootFragment(R.id.setting_fragment_layout, fragment2);
                break;
            case 2:
                SupportFragment fragment3 = new InterfaceSettingFragment();
                loadRootFragment(R.id.setting_fragment_layout, fragment3);
                break;
            default:
                break;
        }
        performanceSettingAdapter.setSelected(position);
    }

}
