package com.kaolafm.kradio.service;

import android.content.Context;
import android.content.Intent;
import androidx.annotation.Nullable;
import androidx.core.app.JobIntentService;
import android.util.Log;

import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.utils.IntentUtils;

/**
 * 为了增大oom_score_adj的值，防止背系统kill
 */
public class EmptyService extends JobIntentService {

    private static final String TAG = "kradio.empty";

    public static void start(Context context) {
        Intent intent = new Intent(context, EmptyService.class);
        IntentUtils.getInstance().startService(context, intent);
    }

    @Override
    protected void onHandleWork(@Nullable Intent intent) {
        AppManager.getInstance().registerExitListener(new AppManager.ExitListener() {
            @Override
            public void onAppExit() {
                EmptyService.this.stopSelf();
            }
        });
    }

    @Override
    public void onCreate() {
        super.onCreate();
        Log.i(TAG, "onCreate");
        IntentUtils.getInstance().startForeground(this);
    }

    @Override
    public void onDestroy() {
        Log.i(TAG, "onDestroy");
        super.onDestroy();
    }
}
