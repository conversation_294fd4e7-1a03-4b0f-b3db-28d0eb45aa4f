<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/home_no_network_rel"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@drawable/bg_home">

    <RelativeLayout
        android:layout_centerInParent="true"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/network_nosigin"
            android:layout_width="@dimen/m360"
            android:layout_height="@dimen/m142"
            android:layout_centerHorizontal="true"
            android:contentDescription="@string/content_desc_refresh_no_network"
            android:src="@drawable/ic_network_error" />

        <TextView
            android:id="@+id/tv_network_nosign"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/network_nosigin"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/m32"
            android:text="@string/network_nosigin"
            android:textColor="@color/text_color_7"
            android:textSize="@dimen/m26" />


        <TextView
            android:visibility="gone"
            android:id="@+id/tv_network_nosign_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/tv_network_nosign"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/m40"
            android:text="@string/network_nosigin_tips"
            android:textColor="@color/text_color_7"
            android:textSize="@dimen/m26" />
    </RelativeLayout>
</RelativeLayout>