<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/colorTransparent">

    <TextView
        android:id="@+id/dialog_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingTop="@dimen/y54"
        android:paddingBottom="@dimen/y18"
        android:text="@string/one_key_change_skin"
        android:textColor="#FFFFFF"
        android:textSize="@dimen/text_size5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ListView
        android:id="@android:id/list"
        android:layout_width="match_parent"
        android:layout_height="@dimen/y272"
        android:paddingBottom="@dimen/y30"
        app:layout_constraintTop_toBottomOf="@+id/dialog_title" />

</androidx.constraintlayout.widget.ConstraintLayout>