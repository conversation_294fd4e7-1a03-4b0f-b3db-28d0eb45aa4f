<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:background="@color/transparent"
    android:layout_height="match_parent">
    <!--必须保留 LinearLayout,否则SlidingTabLayout的下三角与文字太靠近-->
    <com.kaolafm.kradio.lib.widget.tab.SlidingTabLayout
        android:id="@+id/stb_all_category_title_name"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/y44"
        android:layout_marginTop="@dimen/m20"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:background="@drawable/online_tab_class_all_bg"
        android:padding="0dp"
        app:tl_first_no_padding="false"
        app:tl_indicator_style="CUSTOM_DRAWABLE"
        app:tl_indicator_drawable="@drawable/online_tab_class_all_indicator_bg"
        app:tl_indicator_height="0dp"
        app:tl_indicator_width_equal_title="true"
        app:tl_indicator_width="0dp"
        app:tl_tab_width="@dimen/x128"
        app:tl_textSelectColor="@color/user_info_value_color"
        app:tl_textUnselectColor="@color/user_info_value_color"
        app:kradio_tl_textsize="@dimen/online_nav_bar_tv_unselected" />
    <com.kaolafm.kradio.common.widget.NotScrollViewPager
        android:id="@+id/vp_all_category_content"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginBottom="@dimen/online_fragment_categories_bottom"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/stb_all_category_title_name" />

    <FrameLayout
        android:id="@+id/vRootCate"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ViewStub
        android:id="@+id/all_category_no_network"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout="@layout/online_error_layout"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        android:id="@+id/loading"
        layout="@layout/online_refresh_center"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>