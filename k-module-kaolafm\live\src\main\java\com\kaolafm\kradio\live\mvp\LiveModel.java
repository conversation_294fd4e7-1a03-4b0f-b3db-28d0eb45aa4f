package com.kaolafm.kradio.live.mvp;

import com.kaolafm.kradio.lib.base.mvp.BaseModel;
import com.kaolafm.opensdk.api.goods.model.GoodsResult;
import com.kaolafm.opensdk.api.live.LiveRequest;
import com.kaolafm.opensdk.api.live.model.ChatRoomMessageInfoResult;
import com.kaolafm.opensdk.api.live.model.ChatRoomTokenDetail;
import com.kaolafm.opensdk.api.live.model.GiftGivingResult;
import com.kaolafm.opensdk.api.live.model.GiftsResult;
import com.kaolafm.opensdk.api.live.model.LiveChatRoomMemberInfoResult;
import com.kaolafm.opensdk.api.live.model.LiveInfoDetail;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.trello.rxlifecycle3.LifecycleTransformer;

/**
 * <AUTHOR>
 * @date 2019/3/6
 */
public class LiveModel extends BaseModel {

    private LiveRequest mLiveRequest;

    LiveModel(LifecycleTransformer lifecycleTransformer) {
        mLiveRequest = new LiveRequest().bindLifecycle(lifecycleTransformer);
    }

    public void chatRoomToken(String uid, String phone, HttpCallback callback) {
        mLiveRequest.getChatRoomToken(uid, phone, callback);
    }

    public void chatRoomToken(String uid, String avatar, String nickName, HttpCallback<ChatRoomTokenDetail> callback){
        mLiveRequest.getChatRoomToken(uid, avatar, nickName, callback);
    }

    @Override
    public void destroy() {

    }

    void getLiveInfo(String id, HttpCallback<LiveInfoDetail> callback) {
        mLiveRequest.getLiveInfo(id, callback);
    }

    void getGifts(Integer liveId, HttpCallback<GiftsResult> callback){
        mLiveRequest.getGifts(liveId, callback);
    }

    void givingGifts(Long giftId,
                  Long liveId,
                  Integer count,
                  HttpCallback<GiftGivingResult> callback){
        mLiveRequest.givingGifts(giftId, liveId, count, callback);
    }

    void getGoods(Integer liveId, HttpCallback<GoodsResult> callback) {
        mLiveRequest.getGoods(liveId, callback);
    }

    void fetchRoomMembers(Long liveId, HttpCallback<LiveChatRoomMemberInfoResult> callback) {
        mLiveRequest.fetchRoomMembers(liveId, callback);
    }

    void getHistoryMessages(Long liveId, Long roomId, Long msgCount, HttpCallback<ChatRoomMessageInfoResult> callback) {
        mLiveRequest.getHistoryMessages(liveId, roomId, msgCount, callback);
    }
}
