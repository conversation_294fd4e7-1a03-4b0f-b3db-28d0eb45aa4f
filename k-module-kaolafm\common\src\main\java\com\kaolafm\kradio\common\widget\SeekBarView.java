package com.kaolafm.kradio.common.widget;

import android.content.Context;
import androidx.appcompat.widget.AppCompatSeekBar;
import android.util.AttributeSet;
import android.view.MotionEvent;

/**
 * <AUTHOR> on 2019-05-23.
 */

public class SeekBarView extends AppCompatSeekBar {
    private boolean isCanTouch = true;

    public SeekBarView(Context context) {
        this(context,null);
    }

    public SeekBarView(Context context, AttributeSet attrs) {
        this(context, attrs,-1);
    }

    public SeekBarView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void setCanTouch(boolean canTouch) {
        isCanTouch = canTouch;
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (isCanTouch) {
            return super.onTouchEvent(event);
        } else {
            return true;
        }
    }

}
