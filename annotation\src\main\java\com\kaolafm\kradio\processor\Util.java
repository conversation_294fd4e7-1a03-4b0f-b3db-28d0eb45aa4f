package com.kaolafm.kradio.processor;

import java.util.Collection;
import java.util.HashSet;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.lang.model.element.AnnotationMirror;
import javax.lang.model.element.Element;
import javax.lang.model.element.TypeElement;

/**
 * <AUTHOR>
 * @date 2019-09-02
 */
public class Util {

    public static Set<TypeElement> getTypeElementsToProcess(Set<? extends Element> elements,
            Set<? extends Element> supportedAnnotations) {
        Set<TypeElement> typeElements = new HashSet<>();
        for (Element element : elements) {
            if (element instanceof TypeElement) {
                boolean found = false;
                for (Element subElement : element.getEnclosedElements()) {
                    for (AnnotationMirror mirror : subElement.getAnnotationMirrors()) {
                        for (Element annotation : supportedAnnotations) {
                            if (mirror.getAnnotationType().asElement().equals(annotation)) {
                                typeElements.add((TypeElement) element);
                                found = true;
                                break;
                            }
                        }
                        if (found) {
                            break;
                        }
                    }
                    if (found) {
                        break;
                    }
                }
            }
        }
        return typeElements;
    }

    private static final Pattern PATTERN = Pattern.compile("_");

    public static String humpName(String name) {
        StringBuilder nameSb = new StringBuilder(name);
        Matcher matcher = PATTERN.matcher(name);
        int i = 0;
        while (matcher.find()) {
            int position = matcher.end() - (i++);
            nameSb.replace(position - 1, position + 1,
                    nameSb.substring(position, position + 1).toUpperCase());
        }
        return nameSb.toString();
    }

    public static boolean isNotEmpty(Collection collection) {
        return collection != null && !collection.isEmpty();
    }

    public static boolean isNotEmpty(Object[] collection) {
        return collection != null && collection.length > 0;
    }

}
