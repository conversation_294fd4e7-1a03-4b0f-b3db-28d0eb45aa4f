package com.kaolafm.kradio.common.widget.refresh;

import android.content.Context;
import androidx.annotation.NonNull;
import android.util.AttributeSet;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.kradio.k_kaolafm.R;
import com.scwang.smartrefresh.horizontal.HorizontalFooter;
import com.scwang.smartrefresh.layout.api.RefreshKernel;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.constant.RefreshState;
import com.scwang.smartrefresh.layout.constant.SpinnerStyle;

/**
 * <AUTHOR>
 * @date 2022-10-13
 */
public class KradioRefreshFooterHorizontal extends HorizontalFooter {

    private ImageView mProgressImage;
    private TextView mTitleText;
    private Animation mAnimation;
    private boolean mNoMoreData = false;

    public KradioRefreshFooterHorizontal(Context context) {
        this(context, null);
    }

    public KradioRefreshFooterHorizontal(Context context, AttributeSet attrs) {
        super(context, attrs);
        initView(context);
    }

    private void initView(Context context) {
        View.inflate(context, R.layout.kradio_refresh_header_horizontal, this);
        mTitleText = findViewById(R.id.kradio_title);
        mProgressImage = findViewById(R.id.kradio_progress);
        mAnimation = AnimationUtils.loadAnimation(context, R.anim.rotate);
    }

    @NonNull
    @Override
    public View getView() {
        return this;
    }

    @NonNull
    @Override
    public SpinnerStyle getSpinnerStyle() {
        return SpinnerStyle.Translate;
    }

    @Override
    public void setPrimaryColors(int... colors) {

    }

    @Override
    public void onInitialized(@NonNull RefreshKernel kernel, int height, int maxDragHeight) {

    }

    @Override
    public void onMoving(boolean isDragging, float percent, int offset, int height, int maxDragHeight) {

    }

    @Override
    public void onReleased(@NonNull RefreshLayout refreshLayout, int height, int maxDragHeight) {
        onStartAnimator(refreshLayout, height, maxDragHeight);
    }

    @Override
    public void onStartAnimator(@NonNull RefreshLayout refreshLayout, int height, int maxDragHeight) {
        mProgressImage.startAnimation(mAnimation);
    }

    @Override
    public int onFinish(@NonNull RefreshLayout refreshLayout, boolean success) {
        if(mNoMoreData){
            return 0;
        }else {
            if (success) {
                mTitleText.setText("加载成功");
            } else {
                mTitleText.setText("加载失败");
            }
            mProgressImage.clearAnimation();
            return 500;//延迟500毫秒之后再弹回
        }
    }

    @Override
    public void onHorizontalDrag(float percentX, int offsetX, int offsetMax) {

    }

    @Override
    public boolean isSupportHorizontalDrag() {
        return false;
    }

    @Override
    public void onStateChanged(@NonNull RefreshLayout refreshLayout, @NonNull RefreshState oldState, @NonNull RefreshState newState) {
        if (!mNoMoreData) {
            switch (newState) {
                case None:
                case PullUpToLoad:
                    mTitleText.setText("左拉加载更多");
                    break;
                case Loading:
                case LoadReleased:
                    mTitleText.setText("正在加载");
                    break;
                case ReleaseToLoad:
                    mTitleText.setText("释放加载更多");
                    break;
            }
        }
    }

    @Override
    public boolean setNoMoreData(boolean noMoreData) {
        return false;
    }
}
