package com.kaolafm.kradio.lib.bean;

import com.kaolafm.kradio.lib.bean.HistoryItem;

/**
 * 用于显示历史条目数的item数据
 * <AUTHOR>
 * @date 2020/8/19
 */
public class HeadTitleItem extends HistoryItem {

    /**
     * 历史条目数
     */
    private int count = 0;

//    /**
//     * 去登录条目高度，没有历史时全屏，有历史时123px
//     */
//    private int itemHeight = LayoutParams.MATCH_PARENT;

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

//    public int getItemHeight() {
//        return itemHeight;
//    }
//
//    public void setItemHeight(int itemHeight) {
//        this.itemHeight = itemHeight;
//    }
}
