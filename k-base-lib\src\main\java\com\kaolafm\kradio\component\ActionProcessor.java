package com.kaolafm.kradio.component;

/**
 * action的处理接口，是用来处理action多的情况下，实现该接口，不用再在component中进行if-else判断
 * <AUTHOR>
 * @date 2019-10-23
 */
public interface ActionProcessor {

    /**
     * 操作名
     */
    String actionName();

    /**
     * 处理操作
     * @return 返回值表示是同步还是异步返回结果，同{@link Component#onCall(RealCaller)}的返回值
     *         true 异步，false 同步
     * @param caller
     */
    boolean onAction(RealCaller caller);
}
