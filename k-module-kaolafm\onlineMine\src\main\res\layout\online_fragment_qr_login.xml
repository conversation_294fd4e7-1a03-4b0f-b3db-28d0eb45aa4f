<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:siv="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/user_account_main_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/blue01"
    android:background="@color/colorTransparent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true">

        <LinearLayout
            android:id="@+id/user_qr_layout"
            android:layout_width="@dimen/m180"
            android:layout_height="@dimen/m180"
            android:layout_gravity="center_horizontal"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="visible"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible">

            <TextView
                android:id="@+id/tv_login_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/login_by_scan_qr_with_kaolafm"
                android:textColor="@color/text_color_1"
                android:textSize="@dimen/text_size4"
                android:visibility="gone"
                tools:visibility="gone" />

            <com.kaolafm.kradio.component.ui.base.view.OvalImageView
                android:id="@+id/iv_login_qr"
                android:layout_width="@dimen/m180"
                android:layout_height="@dimen/m180"
                app:oval_radius="@dimen/m15"
                android:scaleType="centerCrop" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_login_download_app"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/y14"
            android:autoLink="all"
            android:gravity="center"
            android:linksClickable="true"
            android:text="@string/download_kaolafm_app"
            android:textColor="@color/user_info_value_color"
            android:textSize="@dimen/m26"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/user_qr_layout" />

        <LinearLayout
            android:id="@+id/user_third_platform_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone">

        </LinearLayout>

        <RelativeLayout
            android:id="@+id/user_no_network_rel"
            android:layout_width="@dimen/m188"
            android:layout_height="@dimen/m188"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="MissingConstraints"
            tools:visibility="visible">

            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/online_bg_item_error_corner" />

            <ImageView
                android:id="@+id/network_nosigin"
                android:layout_width="@dimen/m32"
                android:layout_height="@dimen/m32"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/m53"
                android:src="@drawable/user_qr_error_refresh" />

            <TextView
                android:id="@+id/tv_error_msg"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/network_nosigin"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/m12"
                android:gravity="center_horizontal"
                android:text="@string/user_qr_error_no_network_msg"
                android:textColor="@color/user_qr_tip_color"
                android:textSize="@dimen/m20" />

        </RelativeLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</RelativeLayout>