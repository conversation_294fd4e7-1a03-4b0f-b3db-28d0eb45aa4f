<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_ad_interact_image_root"
        android:layout_width="match_parent"
        android:layout_height="match_parent"

        android:background="@color/transparent_color"
        android:clickable="true">

        <ImageView
            android:id="@+id/iv_ad_banner"
            android:layout_width="@dimen/m880"
            android:layout_height="@dimen/m320"
            android:layout_marginTop="@dimen/m12"
            android:scaleType="centerCrop"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:background="@color/information_main_color" />

        <ImageView
            android:id="@+id/iv_ad_collapse"
            android:layout_width="@dimen/m56"
            android:layout_height="@dimen/m56"
            android:layout_margin="@dimen/m12"
            android:scaleType="centerInside"
            android:src="@drawable/collapse"
            app:layout_constraintLeft_toLeftOf="@id/iv_ad_banner"
            app:layout_constraintTop_toTopOf="@id/iv_ad_banner" />

        <TextView
            android:layout_width="@dimen/x52"
            android:layout_height="@dimen/y28"
            android:layout_margin="@dimen/m12"
            android:background="@drawable/bg_ad_mark"
            android:gravity="center"
            android:text="@string/ad"
            android:textColor="@color/colorWhite_60"
            android:textSize="@dimen/m16"
            app:layout_constraintRight_toRightOf="@id/iv_ad_banner"
            app:layout_constraintTop_toTopOf="@id/iv_ad_banner" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</merge>
