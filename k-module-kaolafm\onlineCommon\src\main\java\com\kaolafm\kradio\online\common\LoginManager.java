package com.kaolafm.kradio.online.common;


import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleObserver;
import androidx.lifecycle.OnLifecycleEvent;
import android.os.Bundle;

import com.kaolafm.kradio.common.CP;
import com.kaolafm.kradio.component.DynamicComponent;
import com.kaolafm.kradio.component.RealCaller;
import com.kaolafm.kradio.constant.UserComponentConst;
import com.kaolafm.kradio.constant.UserStateObserverProcessorConst;
import com.kaolafm.kradio.lib.utils.ComponentUtil;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.opensdk.log.Logging.Log;

import java.util.ArrayList;
import java.util.List;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: LoginManager.java                                               
 *                                                                  *
 * Created in 2018/5/10 上午10:16                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
public class LoginManager implements LifecycleObserver {

    private static final String TAG = "kradio.login";

//    public static final int KRADIO_STATE_LOGINED     = 1;//kradio已经登录
//    public static final int KRADIO_STATE_UNLOGINED   = -KRADIO_STATE_LOGINED;//kradio未登录
//
//    public static final int KAOLAFM_STATE_LOGINED    = 2;//考拉已经登录
//    public static final int KAOLAFM_STATE_UNLOGINED  = -KAOLAFM_STATE_LOGINED;//考拉未登录
//
//    public static final int QQ_STATE_LOGINED         = 3;//QQ已经登录
//    public static final int QQ_STATE_UNLOGINED       = -QQ_STATE_LOGINED;//qq未登录
//
//    public int getLoginState() {
//        @LoginManager.LoginState int rst = LoginManager.KRADIO_STATE_UNLOGINED;
//
//        //kradio是否登录
//        if (UserInfoManager.getInstance().isUserLogin()) {
//            //QQ是否登录
//            boolean b = LoginManager.getInstance().isLoginQQ();
//            if (b) {
//                rst = LoginManager.QQ_STATE_LOGINED;
//            } else {
//                rst = LoginManager.QQ_STATE_UNLOGINED;
//            }
//
//            rst = LoginManager.KRADIO_STATE_LOGINED;
//        }else{
//            rst = LoginManager.KRADIO_STATE_UNLOGINED;
//        }
//
//
//        return rst;
//    }
//
//    @IntDef({KRADIO_STATE_LOGINED,
//            KRADIO_STATE_UNLOGINED,
//            KAOLAFM_STATE_LOGINED,
//            KAOLAFM_STATE_UNLOGINED,
//            QQ_STATE_LOGINED,
//            QQ_STATE_UNLOGINED     })
//    public @interface LoginState {
//
//    }


    public interface LoginListener {
        void onLoginStateChange(@CP.CpType int cp, boolean isLogin);

        void onCancel();
    }

    private volatile static LoginManager mInstance;

    private LoginManager() {
    }

    public static LoginManager getInstance() {
        if (mInstance == null) {
            synchronized (LoginManager.class) {
                if (mInstance == null) {
                    mInstance = new LoginManager();
                }
            }
        }
        return mInstance;
    }

    private List<LoginListener> mListeners;


    /**
     * 注册监听
     *
     * @param loginListener
     */
    public void registerLoginListener(LoginListener loginListener) {
        Log.w(TAG, "registerLoginListener: " + loginListener);
        if (mListeners == null) {
            mListeners = new ArrayList<>();
        }

        if (!mListeners.contains(loginListener)) {
            mListeners.add(loginListener);
        }
    }

    /**
     * 清空监听
     */
    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    public void clearListeners() {
        Log.i(TAG, "LoginManager清空监听.");
        if (mListeners != null) {
            {
                mListeners.clear();
            }
        }
    }

    /**
     * 注销监听
     *
     * @param loginListener
     */
    public void unregisterLoginListener(LoginListener loginListener) {
        Log.i(TAG, "unregisterLoginListener: " + loginListener);
        if (mListeners != null) {
            if (mListeners.contains(loginListener)) {
                mListeners.remove(loginListener);
            }
        }
    }

    private void changeLoginState(int cp, boolean state) {
        Log.i(TAG, "changeLoginState: " + mListeners);
        if (mListeners != null && !mListeners.isEmpty()) {
            for (int i = 0; i < mListeners.size(); i++) {
                mListeners.get(i).onLoginStateChange(cp, state);
            }
        }
    }

    public static class HomeLoginComponent implements DynamicComponent {
        private LoginManager mLoginManager;

        @Override
        public String getName() {
            return getClass().getSimpleName()+"$"+TAG;
        }

        private void attachLoginManager(LoginManager loginManager) {
            this.mLoginManager = loginManager;
        }

        public final void change(@CP.CpType int cp, boolean isLogin) {
            Log.i("login", "merge: cp=" + cp + ",state=" + isLogin);
            if (mLoginManager != null) {
                mLoginManager.changeLoginState(cp, isLogin);
            }
        }

        @Override
        public boolean onCall(RealCaller caller) {
            String actionName = caller.actionName();
            if (UserStateObserverProcessorConst.USER_LOGIN.equals(actionName)) {
                change(CP.KRadio, true);
            }else if (UserStateObserverProcessorConst.USER_LOGOUT.equals(actionName)){
                change(CP.KRadio, false);
            }else if (UserStateObserverProcessorConst.USER_CANCEL.equals(actionName)){
                if (mLoginManager != null) {
                    mLoginManager.cancel();
                }
            }
            return false;
        }
    }

    public void cancel() {
        Log.i(TAG, "cancel: mListeners=" + mListeners);
        if (mListeners != null && !mListeners.isEmpty()) {
            for (int i = 0; i < mListeners.size(); i++) {
                mListeners.get(i).onCancel();
            }
        }
    }


    public void addCpSource(HomeLoginComponent homeLoginComponent) {
        homeLoginComponent.attachLoginManager(this);
    }


    /**
     * 临时添加
     *
     * @return
     */

    public boolean isLoginQQ() {
        boolean login = QQMusicLoginInfoManager.getInstance().isLogin();
        //Log.i("showLoginInfo", "登录QQ: " + login);
        return login;
    }

    /**
     * 是否登录了考拉
     *
     * @return
     */
    public boolean isLoginKaola() {
        return ComponentUtil.getResultValue(UserComponentConst.NAME, UserComponentConst.IS_USER_BOUND);
    }


    /**
     * <p>如果是考拉的资源,只要判断是否登录了kradio;
     * <p>如果是QQ的资源,先判断是否登录了kradio,然后再判断是否登录了qq.
     *
     * @param loginListener
     * @return
     */
    public boolean checkLoginOrJumpLoginPage(@CP.CpType int cp, LoginManager.
            LoginListener loginListener) {

        boolean rst = false;

        if (cp == CP.QQ) {
            //QQ是否登录
            boolean b = isLoginQQ();
            if (b) {
                rst = true;
            } else {
                Log.i("login", "checkLoginOrJumpLoginPage: QQ未登录,登录qq,并添加登录监听.");
                rst = false;
                //注册登录监听
                LoginManager.getInstance().registerLoginListener(loginListener);
//                PageJumper.getInstance().jumpToLoginPage(cp);

            }
        } else {
            //kradio是否登录
            if (isLoginKaola()) {
                rst = true;
            } else {
                Log.i("login", "checkLoginOrJumpLoginPage: Kradio未登录,登录kradio,并添加登录监听.");
                //如果没有登录kradio,则登录kradio;
                //注册登录监听
                LoginManager.getInstance().registerLoginListener(loginListener);
                Bundle bundle=new Bundle();
                bundle.putString("type", "0");
                RouterManager.getInstance().jumpPage(RouterConstance.ACTIVITY_URL_LOGIN,bundle);
                rst = false;
            }
        }

        Log.i("login", "checkLoginOrJumpLoginPage: " + rst);
        return rst;

    }
}
