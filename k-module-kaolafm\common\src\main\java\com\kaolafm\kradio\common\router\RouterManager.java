package com.kaolafm.kradio.common.router;

import android.app.Activity;
import android.app.ActivityOptions;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Display;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.core.app.ActivityOptionsCompat;
import androidx.fragment.app.Fragment;

import com.alibaba.android.arouter.launcher.ARouter;
import com.alibaba.fastjson.JSON;
import com.kaolafm.kradio.common.utils.FlavorUtil;
import com.kaolafm.kradio.component.ui.base.skin.SkinHelper;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.report.util.ReportParameterManager;

import java.net.URLDecoder;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;

/**
 * 阿里路由跳转管理类
 * 蔡佳彬
 */
public class RouterManager {
    public final String TAG = getClass().getSimpleName();

    //路由消费责任链
    private List<IRouterConsumer> mRouterConsumerList;

    private static final class RouterManagerHolder {
        static final RouterManager routerManager = new RouterManager();
    }

    public static RouterManager getInstance() {
        return RouterManagerHolder.routerManager;
    }

    /**
     * 普通跳转页面
     *
     * @param path 跳转路径
     */
    public void jumpPage(String path) {
        waitFromARouterInit();
        // 获取当前屏幕ID，如果在副屏则在副屏打开新Activity
        Activity currentActivity = AppManager.getInstance().getCurrentActivity();
        int displayId = getCurrentDisplayId(currentActivity);
        Log.i(TAG, "jumpPage() path=" + path + ", displayId=" + displayId + (displayId != Display.DEFAULT_DISPLAY ? " -> 在目标屏上启动" : " -> 在默认屏启动"));

        if (displayId != Display.DEFAULT_DISPLAY) {
            // 在副屏打开
            ActivityOptions options = ActivityOptions.makeBasic();
            options.setLaunchDisplayId(displayId);
            ARouter.getInstance().build(path)
                .withOptionsBundle(options.toBundle())
                .navigation();
        } else {
            // 在主屏打开（默认）
            ARouter.getInstance().build(path).navigation();
        }
    }

    /**
     * 获取当前Activity的Display ID
     * @param activity 当前Activity
     * @return Display ID，如果获取失败返回默认值0
     */
    private int getCurrentDisplayId(Activity activity) {
        if (activity == null) {
            return Display.DEFAULT_DISPLAY;
        }

        try {
            // 使用WindowManager获取Display ID
            Display display = activity.getWindowManager().getDefaultDisplay();
            return display.getDisplayId();
        } catch (Exception e) {
            Log.e(TAG, "无法获取Display ID: " + e.getMessage());
            return Display.DEFAULT_DISPLAY;
        }
    }

    private void waitFromARouterInit(){
        if (ARouter.hasInit){
            return;
        }
        CountDownLatch latch = new CountDownLatch(1);
        ARouter.addInitCompleteListener(latch::countDown);
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            // Handle the interruption
        }
    }

    /**
     * 普通跳转页面
     *
     * @param path 跳转路径
     */
    public void jumpPageWithTransition(Activity activity, String path, int enterAnim, int exitAnim) {
        ARouter.getInstance().build(path).withTransition(enterAnim, exitAnim).navigation(activity);
    }


    /**
     * 普通跳转页面
     *
     * @param path 跳转路径
     */
    public void jumpPagePlay(Activity mActivity, String path, Bundle bundle, ActivityOptionsCompat compat) {
        //  ARouter.getInstance().build(path).with(bundle).withOptionsCompat(compat).navigation(mActivity);
    }

    /**
     * 普通跳转页面
     *
     * @param path 跳转路径
     */
    public void jumpPagePlay(Activity mActivity, String path, ActivityOptionsCompat compat) {
        // ARouter.getInstance().build(path).withOptionsCompat(compat).navigation(mActivity);
    }

    /**
     * 跳转页面带参数
     *
     * @param path
     * @param bundle
     */
    public void jumpPage(String path, Bundle bundle) {
        waitFromARouterInit();

        // 获取当前屏幕ID，如果在副屏则在副屏打开新Activity
        Activity currentActivity = AppManager.getInstance().getCurrentActivity();
        int displayId = getCurrentDisplayId(currentActivity);
        Log.i(TAG, "jumpPage() path=" + path + ", displayId=" + displayId + (displayId != Display.DEFAULT_DISPLAY ? " -> 在目标屏上启动" : " -> 在默认屏启动"));

        if (displayId != Display.DEFAULT_DISPLAY) {
            // 在副屏打开
            ActivityOptions options = ActivityOptions.makeBasic();
            options.setLaunchDisplayId(displayId);
            ARouter.getInstance().build(path).with(bundle)
                .withOptionsBundle(options.toBundle())
                .navigation();
        } else {
            // 在主屏打开（默认）
            ARouter.getInstance().build(path).with(bundle).navigation();
        }
    }

    /**
     * 跳转页面带参数
     *
     * @param path
     * @param bundle
     */
    public void jumpPageWithTransition(Activity activity, String path, Bundle bundle, int enterAnim, int exitAnim) {
        waitFromARouterInit();
        ARouter.getInstance().build(path).withTransition(enterAnim, exitAnim).with(bundle).navigation(activity);
    }

    /**
     * 跳转页面带参数
     *
     * @param path
     */
    public void jumpPageForResult(Activity activity, String path, int requestCode) {
        ARouter.getInstance().build(path).navigation(activity, requestCode);
    }

    /**
     * 跳转页面带参数
     *
     * @param path
     * @param bundle
     */
    public void jumpPageForResult(Activity activity, String path, Bundle bundle, int requestCode) {
        ARouter.getInstance().build(path).with(bundle).navigation(activity, requestCode);
    }

    /**
     * 通过路由获取Fragment
     *
     * @param path
     * @return
     */
    public Fragment getRouterFragment(String path) {
        // 获取Fragment
        waitFromARouterInit();
        return (Fragment) ARouter.getInstance().build(path).navigation();
    }

    public Fragment getRouterFragment(String path, Bundle bundle) {
        return (Fragment) ARouter.getInstance().build(path).with(bundle).navigation();
    }

    /**
     * 添加路由消费者到责任链，逆序为的是新添加的要优先处理
     *
     * @param consumer
     */
    public void addRouterConsumer(IRouterConsumer consumer) {
        if (mRouterConsumerList == null) mRouterConsumerList = new LinkedList<>();
        if (consumer == null || mRouterConsumerList.contains(consumer)) return;
        mRouterConsumerList.add(0, consumer);
    }

    /**
     * 移除一个路由消费者
     *
     * @param consumer
     */
    public void removeRouterConsumer(IRouterConsumer consumer) {
        if (mRouterConsumerList == null || consumer == null) return;
        mRouterConsumerList.remove(consumer);
    }

    /**
     * 应用内跳转页面
     *
     * @param pageId
     */
    public void navigateToPage(Context context, String pageId) {
        navigateToPage(context, pageId, null);
    }

    /**
     * 应用内跳转页面
     *
     * @param pageId
     * @param extra  额外数据。例如：活动详情页，应传入{@link com.kaolafm.opensdk.api.activity.model.Activity}类型的数据
     */
    public void consumeRoute(String pageId, Object extra) {
        if (mRouterConsumerList == null) return;
        Iterator<IRouterConsumer> iterator = mRouterConsumerList.iterator();
        while (iterator.hasNext()) {
            IRouterConsumer routerConsumer = iterator.next();
            String nextPageId = routerConsumer.consumeRoute(pageId, extra);
            if (IRouterConsumer.ROUTER_CONSUME_FULLY.equals(nextPageId)) {
                return;
            }
        }
    }

    /**
     * 应用内跳转页面
     *
     * @param pageId
     * @param params 参数。
     */
    public void navigateToPage(Context context, @NonNull String pageId, @NonNull Map<String, String> params) {
        waitFromARouterInit();
        Bundle bundle;
        switch (pageId) {
//            case Constants.PAGE_ID_MAIN://综合版-首页
            case Constants.PAGE_ID_CATEGORY://综合版-分类
                ARouter.getInstance().build(RouterConstance.MAIN_COMPREHENSIVE_URL)
                        .withString(Constants.ROUTER_PARAMS_KEY_EXTRA, params.get(Constants.ROUTER_PARAMS_KEY_SELECTED_CODES))
                        .withString(Constants.ROUTER_PARAMS_KEY_PAGE_ID, pageId).navigation();
                break;
            case Constants.PAGE_ID_ACCOUNT_MAIN://综合版-我的
                ARouter.getInstance().build(RouterConstance.MAIN_COMPREHENSIVE_URL)
                        .withString(Constants.ROUTER_PARAMS_KEY_EXTRA, params.get(Constants.ROUTER_PARAMS_KEY_EXTRA))
                        .withString(Constants.ROUTER_PARAMS_KEY_PAGE_ID, pageId).navigation();
                break;
            case Constants.PAGE_ID_SEARCH://综合版-搜索
                bundle = new Bundle();
                bundle.putString("PARAM_SEARCH_HINT", params.get(Constants.ROUTER_PARAMS_KEY_EXTRA));
                jumpPage(RouterConstance.SEARCH_COMPREHENSIVE_URL, bundle);
                break;
            case Constants.PAGE_ID_ACCOUNT_LOGIN://综合版-登录
                jumpPage(RouterConstance.LOGIN_COMPREHENSIVE_URL);
                break;
            case Constants.PAGE_ID_MINE_SETTING://综合版-我的
                jumpPage(RouterConstance.SETTING_COMPREHENSIVE_URL);
                break;
            case Constants.PAGE_ID_ACCOUNT_ABOUT_US://综合版-关于我们
                jumpPage(RouterConstance.ABOUT_COMPREHENSIVE_URL);
                break;
            case Constants.PAGE_ID_MESSAGE://综合版-消息盒子
                jumpPage(RouterConstance.MESSAGE_COMPREHENSIVE_URL);
                break;
            case Constants.PAGE_ID_PLAYER_MUSIC_LIST:
            case Constants.PAGE_ID_BROADCAST_MAIN:
            case Constants.PAGE_ID_PLAYER_LIVING:
            case Constants.PAGE_ID_PLAYER_ALBUM_MAIN:
                //综合版播放器
                consumeRoute(pageId, params.get(Constants.ROUTER_PARAMS_KEY_RESOURCE_ID));
                break;
            case Constants.PAGE_ID_VIDEO:
                int videoType = Integer.parseInt(params.get(Constants.ROUTER_PARAMS_KEY_RESOURCE_TYPE));
                bundle = new Bundle();
                bundle.putInt("VIDEO_TYPE_KEY", videoType);
                if(videoType == PlayerConstants.RESOURCES_TYPE_TEMP_TASK){
                    bundle.putInt("VIDEO_TYPE_KEY", PlayerConstants.RESOURCES_TYPE_TEMP_TASK);
                    bundle.putString("VIDEO_URL_KEY", params.get(Constants.ROUTER_PARAMS_KEY_RESOURCE_URL));
                }else {
                    bundle.putInt("VIDEO_TYPE_KEY", videoType);
                    bundle.putLong("VIDEO_ID_KEY", Long.parseLong(params.get(Constants.ROUTER_PARAMS_KEY_RESOURCE_ID)));
                }
//                jumpPage(RouterConstance.PLAY_SHORT_VIDEO_COMPREHENSIVE_URL2, bundle);
                jumpPage(RouterConstance.PLAY_SHORT_VIDEO_COMPREHENSIVE_URL, bundle);
//                jumpPage(RouterConstance.PLAY_LONG_VIDEO_COMPREHENSIVE_URL, bundle);
                break;
            case Constants.PAGE_ID_ACTIVITY://综合版-活动详情
//                ARouter.getInstance().build(RouterConstance.MAIN_COMPREHENSIVE_URL)
//                        .withString(Constants.ROUTER_PARAMS_KEY_EXTRA, params == null ? "" : params.get(Constants.ROUTER_PARAMS_KEY_RESOURCE_ID))
//                        .withString(Constants.ROUTER_PARAMS_KEY_PAGE_ID, pageId).navigation();
                consumeRoute(pageId, params.get(Constants.ROUTER_PARAMS_KEY_RESOURCE_ID));
                break;
            case Constants.PAGE_ID_BRAND_PAGE://综合版-品牌主页
                if (params.get(Constants.ROUTER_PARAMS_KEY_RESOURCE_ID) == null) break;
                bundle = new Bundle();
                bundle.putString("brandPageId", params.get(Constants.ROUTER_PARAMS_KEY_RESOURCE_ID) + "");
                jumpPage(RouterConstance.BRAND_COMPREHENSIVE_URL, bundle);
                break;
            case Constants.PAGE_ID_TOPIC_DATEILS://综合版-话题详情
                if (params.get(Constants.ROUTER_PARAMS_KEY_RESOURCE_ID) == null) break;
                bundle = new Bundle();
                bundle.putLong(Constants.ARGUMENT_TOPIC_ID,
                        Long.parseLong(params.get(Constants.ROUTER_PARAMS_KEY_RESOURCE_ID)));
                RouterManager.getInstance().jumpPage(RouterConstance.BRAND_TOPIC_DETAIL_URL_COMPREHENSIVE, bundle);
                break;
            case Constants.PAGE_ID_ACCOUNT_PROTOCOL: //综合版-服务协议
                String theme = "dark";
                if (SkinHelper.isDayMode()) {
                    theme = "light";
                }
                String web = ResUtil.getString(R.string.http_url_server_agreement)
                        + "?theme=" + theme + "&bgColor=transparent&contentSize="
                        + (int) ResUtil.getDimension(R.dimen.m22)
                        + "&showTitle=1"
                        + "&marginL=0"
                        + "&unit=1"
                        + "&marginR=" + ResUtil.getDimen(R.dimen.n33)
                        + "&textIndent=0";
                bundle = new Bundle();
                bundle.putString(Constants.ROUTER_PARAMS_KEY_WEB_URL, FlavorUtil.getHttp443Url(web));
                bundle.putString(Constants.ROUTER_PARAMS_KEY_WEB_TITLE, context.getResources().getString(R.string.launcher_agreement0));
                bundle.putString(Constants.ROUTER_PARAMS_KEY_WEB_PAGE_ID, Constants.ONLINE_PAGE_ID_ACCOUNT_PROTOCOL);
                RouterManager.getInstance().jumpPage(RouterConstance.WEBVIEW_COMPREHENSIVE_URL, bundle);
                break;
            case Constants.PAGE_ID_ACCOUNT_POLICY:  //综合版-隐私政策
                String theme1 = "dark";
                if (SkinHelper.isDayMode()) {
                    theme1 = "light";
                }
                String web2 = ResUtil.getString(R.string.http_url_policy)
                        + "?theme=" + theme1 + "&bgColor=transparent&contentSize="
                        + (int) ResUtil.getDimension(R.dimen.m22)
                        + "&showTitle=1"
                        + "&marginL=0"
                        + "&unit=1"
                        + "&marginR=" + ResUtil.getDimen(R.dimen.n33)
                        + "&textIndent=0";
                bundle = new Bundle();
                bundle.putString(Constants.ROUTER_PARAMS_KEY_WEB_URL, FlavorUtil.getHttp443Url(web2));
                bundle.putString(Constants.ROUTER_PARAMS_KEY_WEB_TITLE, context.getResources().getString(R.string.launcher_agreement1));
                bundle.putString(Constants.ROUTER_PARAMS_KEY_WEB_PAGE_ID, Constants.PAGE_ID_ACCOUNT_POLICY);
                RouterManager.getInstance().jumpPage(RouterConstance.WEBVIEW_COMPREHENSIVE_URL, bundle);
                break;


            case Constants.PAGE_ID_HOME_LIKE:   //猜你喜欢
            case Constants.PAGE_ID_CATEGORIES_BROADCAST:    //分类--广播tab页面
            case Constants.PAGE_ID_CATEGORIES_TV:   //分类--电视tab页面
            case Constants.PAGE_ID_CATEGORIES_AI:   //分类--AI电台tab页面
            case Constants.PAGE_ID_CATEGORIES_ALBUM:    //分类--专辑tab页面
            case Constants.PAGE_ID_LISTENING_TRACE_SUBSCRIBE_ALBUM: //听迹--我的订阅--专辑/AI电台tab页面
            case Constants.PAGE_ID_LISTENING_TRACE_SUBSCRIBE_SONG:  //听迹--我的订阅--单曲tab页面
            case Constants.PAGE_ID_LISTENING_TRACE_SUBSCRIBE_TV:    //听迹--我的订阅--广播/电视tab页面
            case Constants.PAGE_ID_LISTENING_TRACE_HISTORY: //听迹--收听历史页面
            case Constants.ONLINE_PAGE_ID_ACTIVITY: //活动-列表
            case Constants.ONLINE_PAGE_ID_SEARCH:   //搜索页
            case Constants.PAGE_ID_LOCATION:    //城市选择页
                ARouter.getInstance().build(RouterConstance.MAIN_ONLINE_URL)
                        .withString(Constants.ROUTER_PARAMS_KEY_PAGE_ID, pageId).navigation();
                break;
//            case Constants.ONLINE_PAGE_ID_ROUTER_DATEILS: //活动-详情，需要切换到活动-列表页面
            // FIXME: 2022/11/11 活动详情应获取活动id后请求接口获取到活动详情，然后再跳转
//                            ARouter.getInstance().build(RouterConstance.ROUTER_URL_MAIN).withString(Constants.ROUTER_PARAMS_KEY_PAGE_ID, pageId).withParcelable(Constants.ROUTER_PARAMS_KEY_EXTRA, extra).navigation();
//                break;
            case Constants.ONLINE_PAGE_ID_SEARCH_RESULT:  //搜索结果页
                // FIXME: 2022/11/11 需要传入搜索关键词
                if (params.containsKey(Constants.ROUTER_PARAMS_KEY_SEARCH_KEYWORD)) {
                    String keyWord = params.get(Constants.ROUTER_PARAMS_KEY_SEARCH_KEYWORD);
                    int searchWay = 1;
                    if (params.containsKey(Constants.ROUTER_PARAMS_KEY_SEARCH_WAY)) {
                        String way = params.get(Constants.ROUTER_PARAMS_KEY_SEARCH_WAY);
                        try {
                            searchWay = Integer.parseInt(way);
                        } catch (NumberFormatException e) {
                            Logger.e(TAG, "应用内跳转参数异常（搜索方式有误）：参数如下" + JSON.toJSONString(params));
                            e.printStackTrace();
                        }
                    }
                    ARouter.getInstance().build(RouterConstance.MAIN_ONLINE_URL)
                            .withString(Constants.ROUTER_PARAMS_KEY_PAGE_ID, pageId)
                            .withString(Constants.ROUTER_PARAMS_KEY_SEARCH_KEYWORD, keyWord)
                            .withInt(Constants.ROUTER_PARAMS_KEY_SEARCH_WAY, searchWay).navigation();
                } else {
                    Logger.e(TAG, "应用内跳转参数异常（未找到keyword）：参数如下" + JSON.toJSONString(params));
                }
                break;

            case Constants.PAGE_ID_PLAY_TV: //广播详情页
            case Constants.PAGE_ID_LIVE_ROOM: //直播详情页
                if (params.containsKey(Constants.ROUTER_PARAMS_KEY_RESOURCE_ID)) {
                    String id = params.get(Constants.ROUTER_PARAMS_KEY_RESOURCE_ID);
                    if (StringUtil.isNotEmpty(id)) {
                        //默认直播
                        int type = PlayerConstants.RESOURCES_TYPE_LIVING;
                        if (Constants.ONLINE_PAGE_ID_PLAYER_ALBUM.equals(pageId) || Constants.PAGE_ID_PLAY_TV.equals(pageId)) {
                            //专辑/AI电台、广播/听电视
                            String typeStr = params.get(Constants.ROUTER_PARAMS_KEY_RESOURCE_TYPE);
                            if (StringUtil.isEmpty(typeStr)) {
                                Logger.e(TAG, "应用内跳转参数异常（未找到type）：参数如下" + JSON.toJSONString(params));
                                return;
                            }
                            try {
                                type = Integer.parseInt(typeStr);
                            } catch (NumberFormatException e) {
                                Logger.e(TAG, "应用内跳转参数异常（type错误）：参数如下" + JSON.toJSONString(params));
                                return;
                            }
                        }
                        consumeRoute(pageId,id);
                    } else {
                        Logger.e(TAG, "应用内跳转参数异常（未找到id）：参数如下" + JSON.toJSONString(params));
                    }
                }
                break;
            case Constants.ONLINE_PAGE_ID_PLAYER_ALBUM: //专辑详情页
            case Constants.ONLINE_PAGE_ID_PAY_ALBUM: //专辑购买
            case Constants.ONLINE_PAGE_ID_PAY_AUDIO: //碎片购买页面-单集购买
            case Constants.ONLINE_PAGE_ID_PAY_AUDIO_LIST: //碎片购买页面-多集购买
                if (params.containsKey(Constants.ROUTER_PARAMS_KEY_RESOURCE_ID)) {
                    String id = params.get(Constants.ROUTER_PARAMS_KEY_RESOURCE_ID);
                    if (StringUtil.isNotEmpty(id)) {
                        //默认直播
                        int type = PlayerConstants.RESOURCES_TYPE_LIVING;
                        if (Constants.ONLINE_PAGE_ID_PLAYER_ALBUM.equals(pageId) || Constants.PAGE_ID_PLAY_TV.equals(pageId)) {
                            //专辑/AI电台、广播/听电视
                            String typeStr = params.get(Constants.ROUTER_PARAMS_KEY_RESOURCE_TYPE);
                            if (StringUtil.isEmpty(typeStr)) {
                                Logger.e(TAG, "应用内跳转参数异常（未找到type）：参数如下" + JSON.toJSONString(params));
                                return;
                            }
                            try {
                                type = Integer.parseInt(typeStr);
                            } catch (NumberFormatException e) {
                                Logger.e(TAG, "应用内跳转参数异常（type错误）：参数如下" + JSON.toJSONString(params));
                                return;
                            }
                        }
                        ARouter.getInstance().build(RouterConstance.MAIN_ONLINE_URL)
                                .withString(Constants.ROUTER_PARAMS_KEY_PAGE_ID, pageId)
                                .withString(Constants.ROUTER_PARAMS_KEY_RESOURCE_ID, id)
                                .withInt(Constants.ROUTER_PARAMS_KEY_RESOURCE_TYPE, type)
                                .navigation();
                    } else {
                        Logger.e(TAG, "应用内跳转参数异常（未找到id）：参数如下" + JSON.toJSONString(params));
                    }
                }
                break;
            case Constants.ONLINE_PAGE_ID_PAY_VIP: //VIP购买
                //vip购买不需要跳转到详情页，也不需要跳转到首页,交由可以消费的消费者进行消费即可。
                consumeRoute(Constants.ONLINE_PAGE_ID_PAY_VIP, null);
                break;
            case Constants.PAGE_ID_MESSAGE_CARD:    //消息泡泡插播
            case Constants.PAGE_ID_MESSAGE_DETAILS:    //消息泡泡插播详情
                //todo 已跟诗乾沟通，不需要做
//                            ARouter.getInstance().build(RouterConstance.ROUTER_URL_MAIN).withInt(Constants.ROUTER_PARAMS_KEY_PAGE_ID, pageId).withParcelable(Constants.ROUTER_PARAMS_KEY_EXTRA, extra).navigation();
                break;
            case Constants.PAGE_ID_MINE_MEMBERS:    //用户中心--会员中心
            case Constants.PAGE_ID_MINE_DURATION:   //用户中心--收听时长页面
            case Constants.ONLINE_PAGE_ID_MINE_PURCHASED:   //用户中心-已购
            case Constants.ONLINE_PAGE_ID_MINE_SETTING: //用户中心-设置
            case Constants.ONLINE_PAGE_ID_MINE_ABOUT:   //用户中心-关于
            case Constants.ONLINE_PAGE_ID_MINE_PURCHASED_MY:   //用户中心—已购—我的订单
                ARouter.getInstance().build(RouterConstance.MINE_ONLINE_URL).withString(Constants.ROUTER_PARAMS_KEY_PAGE_ID, pageId).navigation();
                break;
            case Constants.PAGE_ID_MINE_SUBSCRIBE:   //我的-我的订阅
                ARouter.getInstance().build(RouterConstance.MAIN_COMPREHENSIVE_URL)
                        .withString(Constants.ROUTER_PARAMS_KEY_EXTRA, "0")
                        .withString(Constants.ROUTER_PARAMS_KEY_PAGE_ID, pageId).navigation();
                break;
            case Constants.PAGE_ID_MINE_HISTRORY:   //我的-收听历史
                ARouter.getInstance().build(RouterConstance.MAIN_COMPREHENSIVE_URL)
                        .withString(Constants.ROUTER_PARAMS_KEY_EXTRA, "1")
                        .withString(Constants.ROUTER_PARAMS_KEY_PAGE_ID, pageId).navigation();
                break;
            case Constants.PAGE_ID_MINE_PURCHASED:   //我的-已购
                ARouter.getInstance().build(RouterConstance.MAIN_COMPREHENSIVE_URL)
                        .withString(Constants.ROUTER_PARAMS_KEY_EXTRA, "2")
                        .withString(Constants.ROUTER_PARAMS_KEY_PAGE_ID, pageId).navigation();
                break;
            case Constants.PAGE_ID_MINE_USER:   //我的-个人中心
                ARouter.getInstance().build(RouterConstance.MAIN_COMPREHENSIVE_URL)
                        .withString(Constants.ROUTER_PARAMS_KEY_EXTRA, "3")
                        .withString(Constants.ROUTER_PARAMS_KEY_PAGE_ID, pageId).navigation();
                break;
            case Constants.ONLINE_PAGE_ID_LOGIN:   //登录页面
                bundle = new Bundle();
                bundle.putString("type", ReportParameterManager.getInstance().getPage());
                RouterManager.getInstance().jumpPage(RouterConstance.LOGIN_ONLINE_URL, bundle);
                break;
            case Constants.PAGE_ID_LOGIN:   //登录页面
                bundle = new Bundle();
                bundle.putString("type", ReportParameterManager.getInstance().getPage());
                RouterManager.getInstance().jumpPage(RouterConstance.LOGIN_COMPREHENSIVE_URL, bundle);
                break;

            case Constants.ONLINE_PAGE_ID_MESSAGE: //消息盒子
                RouterManager.getInstance().jumpPage(RouterConstance.MESSAGE_ONLINE_URL);
                break;

            case Constants.ONLINE_PAGE_ID_USE_TIPS: //云听使用提示页面
                //todo 已跟诗乾沟通，不需要做
                break;
            case Constants.ONLINE_PAGE_ID_ACCOUNT_PROTOCOL: //服务协议
                theme = "dark";
                if (SkinHelper.isDayMode()) {
                    theme = "light";
                }
                web = ResUtil.getString(R.string.http_url_server_agreement)
                        + "?theme=" + theme + "&bgColor=transparent&contentSize="
                        + 13
                        + "&showTitle=1";
                bundle = new Bundle();
                bundle.putString(Constants.ROUTER_PARAMS_KEY_WEB_URL, FlavorUtil.getHttp443Url(web));
                bundle.putString(Constants.ROUTER_PARAMS_KEY_WEB_TITLE, context.getResources().getString(R.string.launcher_agreement0));
                bundle.putString(Constants.ROUTER_PARAMS_KEY_WEB_PAGE_ID, Constants.ONLINE_PAGE_ID_ACCOUNT_PROTOCOL);
                RouterManager.getInstance().jumpPage(RouterConstance.WEBVIEW_ONLINE_URL, bundle);

                break;
            case Constants.ONLINE_PAGE_ID_ACCOUNT_PRIVATE:  //隐私政策

                theme = "dark";
                if (SkinHelper.isDayMode()) {
                    theme = "light";
                }
                web = ResUtil.getString(R.string.http_url_policy)
                        + "?theme=" + theme + "&bgColor=transparent&contentSize="
                        + 13
                        + "&showTitle=1";
                bundle = new Bundle();
                bundle.putString(Constants.ROUTER_PARAMS_KEY_WEB_URL, FlavorUtil.getHttp443Url(web));
                bundle.putString(Constants.ROUTER_PARAMS_KEY_WEB_TITLE, context.getResources().getString(R.string.launcher_agreement1));
                bundle.putString(Constants.ROUTER_PARAMS_KEY_WEB_PAGE_ID, Constants.ONLINE_PAGE_ID_ACCOUNT_PRIVATE);
                RouterManager.getInstance().jumpPage(RouterConstance.WEBVIEW_ONLINE_URL, bundle);

                break;
        }
    }

    /**
     * 拦截应用内跳转事件
     *
     * @param context
     * @param mDestUrl
     * @return
     */
    public boolean interceptApplicationJumpEvent(Context context, String mDestUrl) {
        return interceptApplicationJumpEvent(context, mDestUrl, null);
    }

    /**
     * 拦截应用内跳转事件
     *
     * @param context
     * @param mDestUrl
     * @return
     */
    public boolean interceptApplicationJumpEvent(Context context, String mDestUrl, DesturlPlayCallback desturlPlayCallback) {
        if (StringUtil.isNotEmpty(mDestUrl)) {
            if (mDestUrl.startsWith(Constants.ROUTER_IDENTIFICATION)) {
                Map<String, String> params = parseUrlParams(mDestUrl);
                if (desturlPlayCallback != null
                        && params.containsKey(Constants.ROUTER_PARAMS_KEY_RESOURCE_ID)
                        && params.containsKey(Constants.ROUTER_PARAMS_KEY_RESOURCE_TYPE)){
                    String id = params.get(Constants.ROUTER_PARAMS_KEY_RESOURCE_ID);
                    String type = params.get(Constants.ROUTER_PARAMS_KEY_RESOURCE_TYPE);
                    if (!TextUtils.isEmpty(id) && !TextUtils.isEmpty(type)){
                        desturlPlayCallback.handleJumpPlayer(id, type);
                        return true;
                    }
                }
                if (params.containsKey(Constants.ROUTER_PARAMS_KEY_PAGE_ID)) {
                    String pageId = params.get(Constants.ROUTER_PARAMS_KEY_PAGE_ID);
                    RouterManager.getInstance().navigateToPage(context, pageId, params);
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 通过url解析出参数
     *
     * @param url
     * @return
     */
    public Map<String, String> parseUrlParams(String url) {
        Map<String, String> map = new HashMap<>();
        try {
            final String charset = "utf-8";
            url = URLDecoder.decode(url, charset);
            if (url.indexOf('?') != -1 && !url.endsWith("?")) {
                final String contents = url.substring(url.indexOf('?') + 1);
                String[] keyValues = contents.split("&");
                for (int i = 0; i < keyValues.length; i++) {
                    String key = keyValues[i].substring(0, keyValues[i].indexOf("="));
                    String value = keyValues[i].substring(keyValues[i].indexOf("=") + 1);
                    map.put(key, value);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return map;
    }

    public interface DesturlPlayCallback{
        void handleJumpPlayer(String id, String typeStr);
    }
}
