package com.kaolafm.kradio.live.utils;

import com.kaolafm.kradio.constant.UserComponentConst;
import com.kaolafm.kradio.lib.utils.ComponentUtil;

/**
 * 直播模块的工具类
 * <AUTHOR>
 * @date 2019-10-30
 */
public class LiveUtil {

    /**
     * 检查用户是否登录。
     * @return
     */
    public static boolean isUserBound() {
        try {
            return ComponentUtil.getResultValue(UserComponentConst.NAME, UserComponentConst.IS_USER_BOUND);
        }catch (Exception e){
            return false;
        }

    }

    /**
     * 获取uid
     * @return
     */
    public static String getUserId() {
        return ComponentUtil.getResultValue(UserComponentConst.NAME, UserComponentConst.GET_USER_ID);
    }

    /**
     * 获取用户昵称
     * @return
     */
    public static String getUserNickname() {
        return ComponentUtil.getResultValue(UserComponentConst.NAME, UserComponentConst.GET_USER_NICKNAME);
    }

    /**
     * 获取用户头像
     * @return
     */
    public static String getUserFavicon() {
        return ComponentUtil.getResultValue(UserComponentConst.NAME, UserComponentConst.GET_USER_FAVICON);
    }


}
