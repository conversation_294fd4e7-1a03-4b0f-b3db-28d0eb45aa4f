<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/car_owner_radio_type_common"
    android:layout_width="@dimen/m572"
    android:layout_height="@dimen/m614"
    android:layout_gravity="center"
    android:visibility="visible">

    <com.kaolafm.kradio.component.ui.base.view.OvalImageView
        android:id="@+id/radio_common_cover"
        android:layout_width="@dimen/car_owner_radio_center_width"
        android:layout_height="@dimen/car_owner_radio_center_width"
        android:layout_marginTop="@dimen/m116"
        android:src="@drawable/car_owner_radio_entrance_cover_rectangle"
        app:circle="true"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:layout_width="@dimen/car_owner_radio_center_width"
        android:layout_height="@dimen/car_owner_radio_center_width"
        android:layout_marginTop="@dimen/m116"
        android:src="@drawable/car_owner_radio_entrance_shade"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/radio_common_sub_title"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/m36"
        android:layout_marginBottom="@dimen/m145"
        android:ellipsize="end"
        android:maxLines="1"
        android:gravity="center"
        android:text="副标题"
        android:textColor="#FFCFD6E6"
        android:textSize="@dimen/m24"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:id="@+id/radio_common_title"
        android:layout_width="@dimen/m250"
        android:maxLines="1"
        android:ellipsize="end"
        android:layout_height="@dimen/m47"
        android:layout_marginBottom="@dimen/m6"
        android:text="标题"
        android:gravity="center"
        android:textColor="#FFFFFFFF"
        android:textSize="@dimen/m32"
        app:layout_constraintBottom_toTopOf="@id/radio_common_sub_title"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <!--    <ImageView-->
    <!--        android:id="@+id/radio_player"-->
    <!--        app:layout_constraintTop_toTopOf="@id/radio_mask"-->
    <!--        app:layout_constraintLeft_toLeftOf="parent"-->
    <!--        app:layout_constraintRight_toRightOf="parent"-->
    <!--        android:layout_marginTop="@dimen/m134"-->
    <!--        android:layout_width="@dimen/m72"-->
    <!--        android:layout_height="@dimen/m72"-->
    <!--        android:src="@drawable/car_owner_radio_pause"/>-->

</androidx.constraintlayout.widget.ConstraintLayout>