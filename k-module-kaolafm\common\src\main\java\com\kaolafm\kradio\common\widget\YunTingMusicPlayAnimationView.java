package com.kaolafm.kradio.common.widget;

import android.animation.TimeInterpolator;
import android.animation.ValueAnimator;
import android.animation.ValueAnimator.AnimatorUpdateListener;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Paint.Style;
import android.graphics.Shader;
import android.graphics.Shader.TileMode;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.View;
import android.view.animation.LinearInterpolator;

import com.kaolafm.kradio.k_kaolafm.R.drawable;
import com.kaolafm.kradio.k_kaolafm.R.styleable;
import com.kaolafm.kradio.lib.utils.ResUtil;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

public class YunTingMusicPlayAnimationView extends View {
    private Paint mPaint;
    //边框线宽
    private float mStrokeWidth;
    //内容部分占据组件宽度的比例
    private float mRatioWidth;
    //线最大高,自动计算
    private int mMaxHeight;
    //线最小高，自动计算
    private int mMinHeight;
    //每条线的宽度，自动计算
    private int mLineWidth;
    //线的总条数
    private int mLineCount;
    //两边线距离左右两侧的额外padding，自动计算，防止出现组件宽度无法整除线数导致的偏移
    private int mLinePadding;
    //圆角半径，自动计算
    private float mRadius;
    //每条线的初始高度,自动计算
    private final List<Float> mOriginHeightList;
    //线条的开始颜色
    private int mFullStartColor;
    //线条的结束颜色
    private int mFullEndColor;
    //边框的开始颜色
    private int mStrokeStartColor;
    //边框的中间颜色
    private int mStrokeCenterColor;
    //边框的结束颜色
    private int mStrokeEndColor;

    private LinearGradient mFillLineLinearGradient;
    private LinearGradient mStrokeLineLinearGradient;
    private Matrix mMatrix;

    private float mProgress;

    private Drawable mBgBitmap;

    private ValueAnimator mValueAnimator;


    public YunTingMusicPlayAnimationView(@Nullable Context context) {
        this(context, (AttributeSet) null);
    }

    public YunTingMusicPlayAnimationView(@Nullable Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public YunTingMusicPlayAnimationView(@Nullable Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.mStrokeWidth = 2.0F;
        this.mRatioWidth = 0.33333334F;
        this.mLineCount = 5;
        boolean var4 = false;
        this.mOriginHeightList = (List) (new ArrayList());
        this.mFullStartColor = Color.BLACK;
        this.mFullEndColor = Color.BLACK;
        this.mStrokeStartColor = Color.BLACK;
        this.mStrokeCenterColor = Color.BLACK;
        this.mStrokeEndColor = Color.BLACK;
        this.initProperties(context, attrs);
        this.init();
    }

    private final void initProperties(Context context, AttributeSet attrs) {
        TypedArray a = context.obtainStyledAttributes(attrs, styleable.YunTingMusicPlayAnimationView);
        if (a.hasValue(styleable.YunTingMusicPlayAnimationView_strokeWidth)) {
            this.mStrokeWidth = a.getDimension(styleable.YunTingMusicPlayAnimationView_strokeWidth, 2.0F);
        }

        if (a.hasValue(styleable.YunTingMusicPlayAnimationView_fullStartColor)) {
            this.mFullStartColor = a.getColor(styleable.YunTingMusicPlayAnimationView_fullStartColor, Color.BLACK);
        }

        if (a.hasValue(styleable.YunTingMusicPlayAnimationView_fullEndColor)) {
            this.mFullEndColor = a.getColor(styleable.YunTingMusicPlayAnimationView_fullEndColor, Color.BLACK);
        }

        if (a.hasValue(styleable.YunTingMusicPlayAnimationView_strokeStartColor)) {
            this.mStrokeStartColor = a.getColor(styleable.YunTingMusicPlayAnimationView_strokeStartColor, Color.BLACK);
        }

        if (a.hasValue(styleable.YunTingMusicPlayAnimationView_strokeCenterColor)) {
            this.mStrokeCenterColor = a.getColor(styleable.YunTingMusicPlayAnimationView_strokeCenterColor, Color.BLACK);
        }

        if (a.hasValue(styleable.YunTingMusicPlayAnimationView_strokeEndColor)) {
            this.mStrokeEndColor = a.getColor(styleable.YunTingMusicPlayAnimationView_strokeEndColor, Color.BLACK);
        }

        if (a.hasValue(styleable.YunTingMusicPlayAnimationView_lineCount)) {
            this.mLineCount = a.getInteger(styleable.YunTingMusicPlayAnimationView_lineCount, 5);
        }

        if (a.hasValue(styleable.YunTingMusicPlayAnimationView_ratioWidth)) {
            this.mRatioWidth = a.getFloat(styleable.YunTingMusicPlayAnimationView_ratioWidth, 1 / 3f);
        }

        a.recycle();
    }

    private final void init() {
        Paint var1 = new Paint(1);
        var1.setStrokeWidth(this.mStrokeWidth);
        this.mPaint = var1;
        this.mFillLineLinearGradient = new LinearGradient(0.0F, 0.0F, 0.0F, 1.0F, this.mFullStartColor, this.mFullEndColor, TileMode.CLAMP);
        this.mStrokeLineLinearGradient = new LinearGradient(0.0F, 0.0F, 0.0F, 1.0F, new int[]{this.mStrokeStartColor, this.mStrokeCenterColor, this.mStrokeEndColor}, (float[]) null, TileMode.CLAMP);
        this.mMatrix = new Matrix();
        mFillLineLinearGradient.setLocalMatrix(this.mMatrix);
        mStrokeLineLinearGradient.setLocalMatrix(this.mMatrix);
        Drawable var6 = ResUtil.getDrawable(drawable.online_player_button_empty_bg);
        this.mBgBitmap = var6;
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        int var4 = this.getMeasuredWidth();
        int var5 = this.getMeasuredHeight();
        int size = Math.min(var4, var5);
        this.setMeasuredDimension(size, size);
        this.mLineWidth = (int) ((float) this.getMeasuredWidth() * this.mRatioWidth / (float) (2 * this.mLineCount - 1));
        this.mLinePadding = (int) (((float) this.getMeasuredWidth() * this.mRatioWidth - (float) ((2 * this.mLineCount - 1) * this.mLineWidth)) / (float) 2);
        this.mMaxHeight = (int) ((float) this.getMeasuredHeight() * this.mRatioWidth);
        this.mMinHeight = (int) ((float) this.getMeasuredHeight() * this.mRatioWidth / (float) 5);
        this.mRadius = (float) this.mLineWidth / 2.0F;
        this.computeOriginHeight();
    }

    private final void computeOriginHeight() {
        this.mOriginHeightList.clear();
        Random mRandom = new Random();
        for (int i = 0; i < mLineCount; i++) {
            mOriginHeightList.add(mRandom.nextFloat() * (mMaxHeight - mMinHeight) + mMinHeight);
        }
    }

    @SuppressLint({"DrawAllocation"})
    @Override
    protected void onDraw(@NotNull Canvas canvas) {
        super.onDraw(canvas);
        Drawable var10000 = this.mBgBitmap;
        var10000.setBounds(0, 0, this.getMeasuredWidth(), this.getMeasuredHeight());
        var10000 = this.mBgBitmap;
        var10000.draw(canvas);
        float x = 0.0F;
        float y = 0.0F;
        float x1 = 0.0F;
        float y1 = 0.0F;
        float originHeight = 0.0F;
        float height = 0.0F;
        float realProgress = this.mProgress;
        for (int i = 0; i < mLineCount; i++) {
            originHeight = ((Number) this.mOriginHeightList.get(i)).floatValue();
            if (originHeight > (float) ((this.mMaxHeight - this.mMinHeight) / 2 + this.mMinHeight)) {
                realProgress = -realProgress;
            }

            height = (float) ((this.mMaxHeight - this.mMinHeight) * 2) * realProgress + originHeight;
            float var14;
            if (realProgress >= (float) 0) {
                if (height > (float) this.mMaxHeight) {
                    var14 = height - (float) this.mMaxHeight;
                    height -= Math.abs(var14) * (float) 2;
                }

                if (height < (float) this.mMinHeight) {
                    var14 = (float) this.mMinHeight - height;
                    height += Math.abs(var14) * (float) 2;
                }
            } else {
                if (height < (float) this.mMinHeight) {
                    var14 = (float) this.mMinHeight - height;
                    height += Math.abs(var14) * (float) 2;
                }

                if (height > (float) this.mMaxHeight) {
                    var14 = height - (float) this.mMaxHeight;
                    height -= Math.abs(var14) * (float) 2;
                }
            }

            x = (float) (this.mLinePadding + 2 * i * this.mLineWidth) + (float) this.getMeasuredWidth() * ((float) 1 - this.mRatioWidth) / (float) 2;
            y = (float) (this.getMeasuredHeight() / 2) - height / (float) 2;
            x1 = x + (float) this.mLineWidth;
            y1 = y + height;
            mPaint.setStyle(Style.FILL);
            mMatrix.setTranslate(x, y);
            mMatrix.preScale((float) this.mLineWidth, height);
            mFillLineLinearGradient.setLocalMatrix(mMatrix);
            mPaint.setShader((Shader) mFillLineLinearGradient);
            canvas.drawRoundRect(x, y, x1, y1, mRadius, mRadius, mPaint);

            mPaint.setStyle(Style.STROKE);
            mStrokeLineLinearGradient.setLocalMatrix(mMatrix);
            mPaint.setShader((Shader) mStrokeLineLinearGradient);
            canvas.drawRoundRect(
                    x, y, x1, y1, mRadius, mRadius, mPaint
            );
        }

    }

    // CPU优化：禁用播放动画以降低CPU使用率
    public final void startAnimation() {
        // CPU优化：直接返回，不启动动画
        return;
    }

    public final void stopAnimation() {
        if (mValueAnimator != null) {
            if (mValueAnimator.isRunning()) {
                mValueAnimator.cancel();
            }
        }

    }
@Override
    protected void onDetachedFromWindow() {
        this.stopAnimation();
        ValueAnimator var10000 = this.mValueAnimator;
        if (var10000 != null) {
            var10000.removeAllUpdateListeners();
        }

        super.onDetachedFromWindow();
    }

    public final void updateBg() {
        Drawable var10001 = ResUtil.getDrawable(drawable.online_player_button_empty_bg);
        this.mBgBitmap = var10001;
        this.invalidate();
    }
}
