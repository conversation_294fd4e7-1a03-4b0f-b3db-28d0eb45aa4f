package com.kaolafm.kradio.flavor.impl;

import com.kaolafm.kradio.lib.base.flavor.ObtainDeviceID;

import java.lang.reflect.Method;

public class ObtainDeviceIDImpl implements ObtainDeviceID {
    @Override
    public String obtainDeviceID() {
        String vin="";
        try {
            Class clazz = Class.forName("android.os.SystemProperties");
            Method method = clazz.getDeclaredMethod("get", String.class, String.class);
            vin = (String) method.invoke(clazz, "ro.vehicle.daid", null);
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return vin;
    }
}
