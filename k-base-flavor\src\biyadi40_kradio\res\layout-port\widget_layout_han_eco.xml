<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/widget_facade"
    android:layout_width="@dimen/widget_with_size"
    android:layout_height="@dimen/widget_height_size"
    android:background="@drawable/bg_widget_han"
    android:paddingLeft="@dimen/widget_padding_left"
    android:paddingTop="@dimen/widget_padding_top"
    android:paddingRight="@dimen/widget_padding_right"
    android:paddingBottom="@dimen/widget_padding_bottom">

    <LinearLayout
        android:id="@+id/title_tb_main_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/title_tb_logo_imageView"
            android:layout_width="@dimen/widget_tb_logo_size"
            android:layout_height="@dimen/widget_tb_logo_size"
            android:layout_marginRight="12dp"
            android:src="@drawable/ic_widget" />

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/app_name"
            android:textColor="#FFFFFF"
            android:textSize="@dimen/widget_tb_logo_text_size" />
    </LinearLayout>

    <ImageView
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/widget_top_line_height"
        android:layout_below="@+id/title_tb_main_layout"
        android:layout_marginTop="@dimen/widget_line_margin_top"
        android:layout_marginRight="13dp"
        android:layout_toLeftOf="@+id/widget_playinfo_layout"
        android:background="@color/byd_widget_line_color_eco" />

    <RelativeLayout
        android:id="@+id/widget_playinfo_layout"
        android:layout_width="@dimen/widget_center_pic_size"
        android:layout_height="@dimen/widget_center_pic_size"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="20dp"
        android:background="@drawable/bg_cover_eco">

        <ImageView
            android:id="@+id/iv_play_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_alignParentTop="true"
            android:background="@drawable/cover_eco"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/widget_cover"
            android:layout_width="@dimen/widget_radio_cover_size"
            android:layout_height="@dimen/widget_radio_cover_size"
            android:layout_centerInParent="true"
            android:background="@drawable/widget_cover_default" />

        <ImageView
            android:id="@+id/widget_progressBar"
            style="@style/Widget.AppCompat.ProgressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:visibility="gone" />

    </RelativeLayout>


    <LinearLayout
        android:id="@+id/widget_play_operation_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/widget_playinfo_layout"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="@dimen/widget_options_h_margin"
        android:layout_marginRight="@dimen/widget_options_h_margin"
        android:gravity="bottom|center_horizontal"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/widget_prev"
            android:layout_width="@dimen/widget_btn_size"
            android:layout_height="@dimen/widget_btn_size"
            android:layout_weight="1"
            android:src="@drawable/selector_widget_btn_play_prev" />

        <ImageView
            android:id="@+id/widget_play_or_pause"
            android:layout_width="@dimen/widget_btn_size"
            android:layout_height="@dimen/widget_btn_size"
            android:layout_weight="1"
            android:src="@drawable/selector_widget_btn_pause" />

        <ImageView
            android:id="@+id/widget_next"
            android:layout_width="@dimen/widget_btn_size"
            android:layout_height="@dimen/widget_btn_size"
            android:layout_weight="1"
            android:src="@drawable/selector_widget_btn_play_next" />

        <ImageView
            android:id="@+id/widget_collection"
            android:layout_width="@dimen/widget_btn_size"
            android:layout_height="@dimen/widget_btn_size"
            android:layout_weight="1"
            android:src="@drawable/selector_widget_btn_uncollection"
            android:visibility="gone" />
    </LinearLayout>

</RelativeLayout>