package com.kaolafm.kradio.flavor.carnetwork.api;

import com.kaolafm.kradio.flavor.carnetwork.carnetworkutils.OuShangAuthConstants;
import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.http.core.HttpCallback;

import java.util.HashMap;

import io.reactivex.functions.Function;


public class AuthApiRequest extends BaseRequest {

    private final AuthApiServices mChangAnApiServices;

    public AuthApiRequest() {
//        mRetrofitUrlManager.putDomain("changan", "http://auth.test-tboss.cu-sc.com:10010");
        mUrlManager.putDomain("oushang", OuShangAuthConstants.BASEURL);
        mChangAnApiServices = obtainRetrofitService(AuthApiServices.class);
    }

    public void getChangAnAuth(String appid, String vid, String itemid, long timestamp, HttpCallback<Integer> callback) {
        HashMap<String, Object> param = new HashMap<>();
        param.put("appid", appid);
        param.put("vid", vid);
        param.put("itemid", itemid);
        param.put("timestamp", timestamp);
        String toJson = mGsonLazy.get().toJson(param);
        doHttpDeal(mChangAnApiServices.getChangAnAuthInfo(toJson), new Function<BaseResult, Integer>() {
            @Override
            public Integer apply(BaseResult baseResult) throws Exception {
                return baseResult.getCode();
            }
        }, callback);
    }

}
