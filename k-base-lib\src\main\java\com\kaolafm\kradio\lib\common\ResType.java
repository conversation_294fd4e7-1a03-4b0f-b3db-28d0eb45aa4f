package com.kaolafm.kradio.lib.common;

/**
 * 资源类型:服务器和客户端共用一套
 */
public interface ResType {
    int NOACTION_TYPE = -1;//无
    int ALBUM_TYPE = 0;//专辑
    int AUDIO_TYPE = 1;//碎片
    int TOPIC_TYPE = 2;//专题包
    int RADIO_TYPE = 3;//电台
    int URL_TYPE = 4;//URL
    int LIVE_TYPE = 5;//直播
    int URL_TYPE_OUT = 6;//外部URL
    int RANK_LIST_TYPE = 7;//排行榜
    int RADIO_LIST_TYPE = 8;//电台列表
    int LIVE_LIST_TYPE = 9;//直播列表
    int TOPIC_LIST_TYPE = 10;//专题列表
    int BROADCAST_TYPE = 11;//传统电台
    int TV_TYPE = 12;//听电视列表
    int FEATURE_TYPE = 13;//专题
    int VIDEO_ALBUM_TYPE = 15;//视频专辑
    int VIDEO_TYPE = 16;//视频单曲
    int ACTIVITY_TYPE = 20;//活动


    int USER_TYPE = 13;//具体某一个用户
    int KEYWORD_TYPE = 14;//关键字、联想词
    int TOPIC_DETAIL_TYPE = 15;//具体某一个
    int RANK_DETAIL_TYPE = 15;//具体某一个排行榜
    int CATEGORY_TYPE = 16;//具体某一个一级分类
    int CATEGORY_SUB_TYPE = 17;//具体某一个二级分类
    int TXZ_BROADCAST_TYPE = 18;//同行者本地电台展位
    int TXZ_CATEGORY_TYPE = 19;//同行者分类大全占位
    int RADIO_TYPE_PAGE = 20;//5.x 跳转调频发现页
    int FUNCTION_KRADIO_RADIO_ENTER = 101; //kradio电台功能入口
    int FUNCTION_KRADIO_MUSIC_ENTER = 102; //kradio音乐功能入口
    //所有分类功能入口，小卡片
    int FUNCTION_ENTER_SMALL = 103;
    int RESOURCES_TYPE_QQ_MUSIC = 100;//QQ音乐

    int RESOURCES_TYPE_SONGMENU = 1001;//QQ 歌单 客户端自定义
    int RESOURCES_TYPE_SONG_CHARTS = 1002;//QQ 排行榜 客户端自定义
    /**QQ 音乐电台 标签电台，相当于QQ音乐文档里的分类电台*/
    int RESOURCES_TYPE_MUSIC_RADIO_LABEL = 1003;
    /**QQ 音乐电台 场景电台,相当于QQ音乐文档里的公共电台*/
    int RESOURCES_TYPE_MUSIC_RADIO_SCENE = 1004;
    /** 语音搜索QQ音乐资源 */
    int RESOURCES_TYPE_MUSIC_VOICE_RESULT = 1005;

    int MUSIC_MINE_PRIVATE_FM = 1010;//私人fm,
    int MUSIC_MINE_DAY = 1011;//每日30收
    int MUSIC_MINE_LIKE = 1012;//我的红心/我喜欢的
    int MUSIC_MINE_FAVOURITE = 1013;//我的收藏
    /** 音乐最近收听 */
    int MUSIC_MINE_HISTORY = 1014;
    /** 电台收听历史 */
    int RADIO_MINE_HISTORY = 1015;

    int TYPE_ITEM_TITLE = 31;

    int HOME_ITEM_TYPE_1_1=21;
    int HOME_ITEM_TYPE_2_1=22;
    int HOME_ITEM_TYPE_2_3=23;
    int HOME_ITEM_TYPE_BIG=24;
    int HOME_ITEM_TYPE_ROTATUON=25;
    int HOME_ITEM_TYPE_BRAND_PAGE_BIG=26;
    int HOME_ITEM_TYPE_BRAND_PAGE_1_1=27;
    int HOME_ITEM_TYPE_TOPIC=28;
    int HOME_ITEM_TYPE_TOPIC_HOME=29;
    int HOME_ITEM_TYPE_BRAND_HOME=30;
    int HOME_ITEM_TYPE_BRAND=31;

}