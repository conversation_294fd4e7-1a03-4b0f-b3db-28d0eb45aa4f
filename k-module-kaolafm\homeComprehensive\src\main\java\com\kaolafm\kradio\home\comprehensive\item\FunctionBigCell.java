package com.kaolafm.kradio.home.comprehensive.item;

import androidx.annotation.NonNull;
import android.util.Log;
import android.util.TypedValue;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.kradio.lib.widget.square.SquareLayout;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.flavor.KRadioHomeItemFontSizeInter;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;


/**
 * 首页功能入口类型，横向大图
 * @date 2021-11-24
 */
public class FunctionBigCell extends FunctionPairCell {

    ImageView mIvItemHomeCover;
    SquareLayout mSquareLayout;
    TextView mTvItemHomeTitle;
    View mViewItemHomeCoverBg;

    public FunctionBigCell() {
        super();
    }

    public void mountView(@NonNull FunctionPairCell data, @NonNull View view, int position) {
        mIvItemHomeCover=view.findViewById(R.id.iv_item_home_cover);
        mSquareLayout=view.findViewById(R.id.sv_item_home_place);
        mTvItemHomeTitle=view.findViewById(R.id.tv_item_home_function_top);
        mViewItemHomeCoverBg=view.findViewById(R.id.view_item_home_cover_bg);

        Log.i("FunctionBigCell", "mountView position:" + position + ",url:" + data.imageUrl);
        ImageLoader.getInstance().displayImage(view.getContext(), data.imageUrl, mIvItemHomeCover);
        mTvItemHomeTitle.setText(data.name);
        mSquareLayout.setPlayState(data.selected);
        mViewItemHomeCoverBg.setSelected(data.selected);
        mTvItemHomeTitle.setSelected(data.selected);

        KRadioHomeItemFontSizeInter homeItemFontSizeInter = ClazzImplUtil.getInter("KRadioHomeItemFontSizeImpl");
        if (homeItemFontSizeInter != null) {
            changeHomeTitleSize(ResUtil.getDimen(homeItemFontSizeInter.getSizeId()));
        } else {
            changeHomeTitleSize(ResUtil.getDimen(R.dimen.home_item_golden_text_size));
        }

        ViewUtil.setViewVisibilityAccordingToSetting(mViewItemHomeCoverBg);

        view.setOnClickListener(v -> {
            if (mListener != null) {
                view.setTag(data);
                mListener.onViewClick(view, position);
            }
        });
    }

    @Override
    public int getItemType() {
        return R.layout.item_home_function_big;
    }

    private void changeHomeTitleSize(int size) {
        mTvItemHomeTitle.setTextSize(TypedValue.COMPLEX_UNIT_PX, size);
    }

}
