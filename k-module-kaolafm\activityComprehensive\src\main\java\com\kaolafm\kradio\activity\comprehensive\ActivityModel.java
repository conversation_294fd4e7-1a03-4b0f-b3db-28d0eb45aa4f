package com.kaolafm.kradio.activity.comprehensive;

import com.kaolafm.kradio.lib.base.mvp.BaseModel;
import com.kaolafm.kradio.lib.utils.KaolaAppConfigData;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.activity.ActivityRequest;
import com.kaolafm.opensdk.api.activity.model.Activity;
import com.kaolafm.opensdk.http.core.HttpCallback;

import java.util.List;

public class ActivityModel extends BaseModel {

    private ActivityRequest mActivityRequest;

    public ActivityModel(){
        mActivityRequest = new ActivityRequest().setTag(this.toString());
    }

    public void getInfoList(HttpCallback<BasePageResult<List<Activity>>> callback){
        KaolaAppConfigData kaolaAppConfigData = KaolaAppConfigData.getInstance();
        String aid = kaolaAppConfigData.getAppId();
        mActivityRequest.getInfoList(aid, callback);
    }

    @Override
    public void destroy() {

    }
}
