package com.kaolafm.kradio.lib.utils;

import android.util.Log;

public class YTLogUtil {
    // CPU优化：禁用日志以降低CPU使用率
    private static boolean isNormalLogClose = false;

    private static boolean isStartLogClose = false;

    public static void closeLog() {
        isNormalLogClose = false;
    }

    public static void openLog() {
        isNormalLogClose = true;
    }


    private static Long startTime = -1L;
    public static void logStart(String className, String methodName, String logStr) {
        if (isNormalLogClose) {
            return;
        }

        if (isStartLogClose){
            return;
        }

        long currentTime = 0L;
        if (startTime == -1L){
            startTime = System.currentTimeMillis();
        } else  {
            currentTime = System.currentTimeMillis() - startTime;
        }
        Log.i(Constants.START_TAG, "cost time: " + currentTime + "ms " + className + "." + methodName + "->" + logStr);
    }

    public static void logStartEnd(){
        startTime = -1L;
        isStartLogClose = true;
    }

    public static void logI(String tag, String msg) {
        if (isNormalLogClose) {
            return;
        }
        Log.i(tag, msg);
    }

    public static void logE(String tag, String msg, Exception e) {
        Log.e(tag, msg, e);
    }
}
