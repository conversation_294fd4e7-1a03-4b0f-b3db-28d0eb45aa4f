<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/online_page_bg_vague"
    android:paddingTop="@dimen/activity_title_top_padding">

    <RelativeLayout
        android:id="@+id/titleBg"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="match_parent"
        android:layout_height="@dimen/y116">

        <ImageView
            style="@style/Online_FragmentBackButton_white"
            tools:ignore="MissingConstraints" />

        <TextView
            android:layout_centerInParent="true"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/mine_order"
            android:textColor="@color/online_myorder_title_text_color"
            android:textSize="@dimen/m28" />

    </RelativeLayout>

    <View
        android:id="@+id/line"
        app:layout_constraintTop_toBottomOf="@id/titleBg"
        android:layout_width="match_parent"
        android:layout_height="@dimen/y1"
        android:background="@color/online_message_line_color" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/mine_order_item_start"
        android:layout_marginEnd="@dimen/mine_order_item_end"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line"
        tools:listitem="@layout/online_item_order" />

    <TextView
        android:id="@+id/tvHelp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/y49"
        android:text="@string/mine_order_help"
        android:textColor="#6C7190"
        android:textSize="@dimen/text_size3"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <ViewStub
        android:id="@+id/vsErrorLayout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line"
        android:layout="@layout/online_error_layout" />
</androidx.constraintlayout.widget.ConstraintLayout>