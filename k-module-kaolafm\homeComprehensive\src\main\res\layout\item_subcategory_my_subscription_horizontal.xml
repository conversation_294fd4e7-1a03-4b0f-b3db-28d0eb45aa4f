<?xml version="1.0" encoding="utf-8"?>
<com.kaolafm.kradio.lib.widget.square.SquareHeightFrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_view"
    android:layout_width="wrap_content"
    android:layout_height="match_parent">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.kaolafm.kradio.component.ui.base.view.OvalImageView
            android:id="@+id/iv_cover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:contentDescription="@null"
            tool:src="@drawable/media_default_pic" />

        <View
            android:id="@+id/view_item_subscription_mongolian"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/category_item_layer_meng_normal" />


        <ViewStub
            android:id="@+id/vs_layout_playing"
            android:layout_width="@dimen/m32"
            android:layout_height="@dimen/m32"
            android:layout_alignParentEnd="true"
            android:layout_alignParentBottom="true"
            android:layout_marginEnd="@dimen/x15"
            android:layout_marginBottom="@dimen/y15"
            android:layout="@layout/layout_playing_square_item" />

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginStart="@dimen/m4"
            android:layout_marginEnd="@dimen/m4"
            android:layout_marginBottom="@dimen/m10"
            android:ellipsize="end"
            android:gravity="center_horizontal"
            android:maxEms="12"
            android:maxLines="@integer/sub_ctg_max_lines"
            android:textColor="@color/category_item_radio_title_text_color"
            android:textSize="@dimen/subcategory_item_radio_text_size"
            tool:text="箱底私藏!迷醉于电音质感女嗓" />

        <TextView
            android:id="@+id/tv_number"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_alignParentRight="true"
            android:layout_marginTop="@dimen/y8"
            android:layout_marginRight="@dimen/x8"
            android:ellipsize="end"
            android:gravity="center"
            android:maxEms="5"
            android:maxLines="2"
            android:textColor="@color/category_item_radio_subtitle_text_color"
            android:textSize="@dimen/text_size_ext_2"
            tool:text="20万" />

        <RelativeLayout
            android:id="@+id/layout_offline"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/offline_layer">

            <ImageView
                android:layout_width="@dimen/x100"
                android:layout_height="@dimen/y56"
                android:layout_centerInParent="true"
                android:src="@drawable/offline" />

        </RelativeLayout>
    </RelativeLayout>

    <com.kaolafm.kradio.component.ui.base.view.OvalImageView
        android:id="@+id/vip_icon"
        android:layout_width="@dimen/m62"
        android:layout_height="@dimen/m30"
        android:layout_alignStart="@+id/iv_item_home_cover"
        android:layout_alignTop="@+id/iv_item_home_cover"
        android:scaleType="centerCrop"
        app:rid_type="1"
        tool:src="@drawable/comprehensive_icon_vip" />

    <LinearLayout
        android:id="@+id/llFreq"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/m26"
        android:layout_gravity="right"
        android:layout_margin="@dimen/m10"
        android:background="@drawable/sh_bg_freq"
        android:gravity="center_horizontal|bottom"
        android:orientation="horizontal"
        android:paddingStart="@dimen/m6"
        android:paddingEnd="@dimen/m6"
        android:paddingBottom="@dimen/m1"
        android:visibility="visible">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="FM "
            android:textColor="@color/text_color_7"
            android:textSize="@dimen/m16" />

        <TextView
            android:id="@+id/tvFreq"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/m24"
            android:textColor="@color/text_color_7"
            android:textSize="@dimen/text_size1"
            tools:text="103.9" />
    </LinearLayout>

</com.kaolafm.kradio.lib.widget.square.SquareHeightFrameLayout>
