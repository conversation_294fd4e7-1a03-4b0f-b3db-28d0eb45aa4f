package com.kaolafm.kradio.search;

import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.component.ui.base.view.RateView;
import com.kaolafm.kradio.k_kaolafm.BuildConfig;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.UrlUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.search.model.Compere;
import com.kaolafm.opensdk.api.search.model.HighLightWord;
import com.kaolafm.opensdk.api.search.model.SearchProgramBean;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.trello.rxlifecycle3.LifecycleTransformer;

import java.util.ArrayList;
import java.util.List;


public class SearchResultAdapter extends BaseAdapter<SearchProgramBeanResult> {

    protected static final int TYPE_ALBUM = 0;
    protected static final int TYPE_AUDIO = 1;
    protected static final int TYPE_RADIO = 3;
    protected static final int TYPE_LIVE = 5;
    protected static final int TYPE_BROADCAST = 11;
    protected static final int TYPE_TV = 12;
    protected static final int TYPE_FEATURE = 13;
    protected static final int TYPE_VIDEO_ALBUM = 15;
    protected static final int TYPE_QQ_SCENE = 1003;
    protected static final int TYPE_QQ_TAG = 1004;

    private int mType;
    private LifecycleTransformer mLifecycleTransformer;

    public SearchResultAdapter(LifecycleTransformer<Object> bindUntilEvent) {
        mLifecycleTransformer = bindUntilEvent;
    }

    @Override
    protected BaseHolder<SearchProgramBeanResult> getViewHolder(ViewGroup parent, int viewType) {
        return new SearchResultHolder(inflate(parent, getLayoutId(), viewType));
    }

    private int getLayoutId() {
        if (BuildConfig.LAYOUT_TYPE == 1) {
            return R.layout.item_search_result_1280_720;
        } else {
            return R.layout.item_search_result;
        }
    }


    String mCurrentPlayingId = PlayerManagerHelper.getInstance().getCurPlayItem().getRadioId();

    public void refreshPlaying(String radioId) {
        boolean inDataList = false; //radioId是否在当前搜索结果内
        int selectedPosition = -1;
        if (!TextUtils.equals(mCurrentPlayingId, radioId)) {
            for (int i = 0; i < getItemCount(); i++) {
                SearchProgramBean searchProgramBean = mDataList.get(i);
                if (TextUtils.equals(mCurrentPlayingId, String.valueOf(searchProgramBean.getId()))) {
                    selectedPosition = i;
                }
                if (TextUtils.equals(radioId, String.valueOf(searchProgramBean.getId()))) {
                    notifyItemChanged(i);
                    inDataList = true;
                    break;
                }
            }
        } else {
            inDataList = true;
        }
        //没在当前搜索结果内，则应删除选中状态
        if (!inDataList && selectedPosition != -1) {
            getItemData(selectedPosition).isPlaying = false;
            notifyItemChanged(selectedPosition);
        }
        mCurrentPlayingId = radioId;
    }

    public void setType(int type) {
        mType = type;
    }

    public void setLifeCycleNull() {
        this.mLifecycleTransformer = null;
    }

    public class SearchResultHolder extends BaseHolder<SearchProgramBeanResult> {

        ImageView mIvSearchCover;
        KeywordTextView mTvSearchTitle;
        TextView mTvSearchSubTitle;
        View mSearchBgView;
        ConstraintLayout mClSearchResult;
        ImageView mVipIcon;
        RateView audioAnimationIcon;
        TextView typeTv;

        public SearchResultHolder(View itemView) {
            super(itemView);
            mIvSearchCover=itemView.findViewById(R.id.iv_search_cover);
            mTvSearchTitle=itemView.findViewById(R.id.tv_search_title);
            mTvSearchSubTitle=itemView.findViewById(R.id.tv_search_sub_title);
            mSearchBgView=itemView.findViewById(R.id.search_bg_view);
            mClSearchResult=itemView.findViewById(R.id.cl_search_result);
            mVipIcon=itemView.findViewById(R.id.vip_icon);
            audioAnimationIcon=itemView.findViewById(R.id.audioAnimationIcon);
            typeTv=itemView.findViewById(R.id.typeTv);
        }

        private boolean isPlayingItem(PlayItem playItem, SearchProgramBean searchProgramBean) {
            long searchItemId = searchProgramBean.getId();
            long audioId = playItem.getAudioId();
            String radioId = playItem.getRadioId();
            String albumId = playItem.getAlbumId();

            if (audioId == searchItemId) {
                return true;
            }
            if (String.valueOf(searchItemId).equals(albumId)) {
                return true;
            }
            if (String.valueOf(searchItemId).equals(radioId)) {
                return true;
            }
            return false;
        }

        @Override
        public void setupData(SearchProgramBeanResult searchProgramBean, int position) {
            ImageLoader.getInstance().displayImage(itemView.getContext(),
                    UrlUtil.getCustomPicUrl(UrlUtil.PIC_250_250, searchProgramBean.getImg()), mIvSearchCover);
            boolean isPlaying = searchProgramBean.isPlaying;
            int type = searchProgramBean.getType();

            if (isPlaying) {
                mSearchBgView.setActivated(true);
                audioAnimationIcon.setVisibility(View.VISIBLE);
                boolean playingCurrent = isPlayingCurrent(searchProgramBean.getId(), searchProgramBean.getType());
                if (PlayerManagerHelper.getInstance().isPlaying() && playingCurrent) {
                    audioAnimationIcon.playAnimation();
                } else {
                    audioAnimationIcon.pauseAnimation();
                }
            } else {
                mSearchBgView.setActivated(false);
                audioAnimationIcon.setVisibility(View.GONE);
                audioAnimationIcon.pauseAnimation();
            }
            mTvSearchSubTitle.setSelected(isPlaying);
            List<HighLightWord> highLightWords = searchProgramBean.getHighlight();
            List<String> keywords = new ArrayList<>();
            if (!ListUtil.isEmpty(highLightWords)) {
                for (HighLightWord highLightWord : highLightWords) {
                    keywords.add(highLightWord.getToken());
                }
            }

            if (searchProgramBean.getFine() == 1) {
                //精品
                mVipIcon.setBackgroundResource(R.drawable.comprehensive_icon_supreme);
                mVipIcon.setVisibility(View.VISIBLE);
                ViewGroup.LayoutParams layoutParams = mVipIcon.getLayoutParams();
                layoutParams.width = ResUtil.getDimen(R.dimen.m52);
                mVipIcon.setLayoutParams(layoutParams);
            } else if (searchProgramBean.getVip() == 1) {
                //vip
                mVipIcon.setBackgroundResource(R.drawable.comprehensive_icon_vip);
                mVipIcon.setVisibility(View.VISIBLE);
                ViewGroup.LayoutParams layoutParams = mVipIcon.getLayoutParams();
                layoutParams.width = ResUtil.getDimen(R.dimen.m52);
                mVipIcon.setLayoutParams(layoutParams);
            } else if (type == TYPE_BROADCAST || type == TYPE_TV) {
                mVipIcon.setBackgroundResource(R.drawable.comprehensive_icon_live_class);
                mVipIcon.setVisibility(View.VISIBLE);
                ViewGroup.LayoutParams layoutParams = mVipIcon.getLayoutParams();
                layoutParams.width = ResUtil.getDimen(R.dimen.m52);
                mVipIcon.setLayoutParams(layoutParams);
            } else if (type == ResType.TYPE_VIDEO_ALBUM || type == ResType.TYPE_VIDEO_AUDIO) {
                mVipIcon.setBackgroundResource(R.drawable.comprehensive_icon_video);
                mVipIcon.setVisibility(View.VISIBLE);
                ViewGroup.LayoutParams layoutParams = mVipIcon.getLayoutParams();
                layoutParams.width = ResUtil.getDimen(R.dimen.m52);
                mVipIcon.setLayoutParams(layoutParams);
            }
//            else if (type == TYPE_TV) {
//                mVipIcon.setBackgroundResource(R.drawable.comprehensive_icon_live_tv);
//                mVipIcon.setVisibility(View.VISIBLE);
//                ViewGroup.LayoutParams layoutParams = mVipIcon.getLayoutParams();
//                layoutParams.width = ResUtil.getDimen(R.dimen.m78);
//                layoutParams.width = ResUtil.getDimen(R.dimen.m52);
//                mVipIcon.setLayoutParams(layoutParams);
//            }
            else {
                mVipIcon.setBackground(null);
                mVipIcon.setVisibility(View.GONE);
            }
//            VipCornerUtil.setVipCorner(mVipIcon, searchProgramBean.getVip(),
//                    searchProgramBean.getFine(), true);

            switch (type) {
                case TYPE_RADIO:
//                    专辑封面及VIP/精品标签（无则不展示）、专辑名称
//                    专辑内容标签+运营推荐语（后台配置）
                    ViewUtil.setViewVisibility(mTvSearchSubTitle, View.VISIBLE);
                    mTvSearchTitle.setKeywordsAndColor(searchProgramBean.getAlbumName(), keywords,
                            ResUtil.getColor(R.color.search_key_word_text_color));
                    mClSearchResult.setContentDescription(searchProgramBean.getAlbumName());
                    typeTv.setText("专辑");
                    mTvSearchSubTitle.setText(searchProgramBean.getRecommend());
                    break;
                case TYPE_BROADCAST:
//                    电台封面及直播标签、电台名称、频率号、
//                    广播内容标签+正在直播：节目名称 /若获取不到节目单则展示：暂无节目单
                    ViewUtil.setViewVisibility(mTvSearchSubTitle, View.VISIBLE);
                    mTvSearchSubTitle.setText("");
                    mTvSearchTitle.setKeywordsAndColor(searchProgramBean.getAlbumName() + " " + (StringUtil.isEmpty(searchProgramBean.getFreq()) ? "" : searchProgramBean.getFreq()), keywords,
                            ResUtil.getColor(R.color.search_key_word_text_color));
                    mClSearchResult.setContentDescription(searchProgramBean.getAlbumName());
                    typeTv.setText("广播");
                    if (StringUtil.isNotEmpty(searchProgramBean.getCurrentProgram())) {
                        updateBroadcastOrTvSubTitle(searchProgramBean.getCurrentProgram());
                    } else {
                        updateBroadcastOrTvSubTitleToNoCurrentProgram();
                    }
//                    if (searchProgramBean.getProgramEnable() == 1) {
//                        //获取正在播放的广播播单信息
//                        if (searchProgramBean.currentPlayingPlayItem != null) {
//                            updateBroadcastOrTvSubTitle(searchProgramBean.currentPlayingPlayItem);
//                        } else {
//                            getCurrentBroadcastPlayItem(searchProgramBean, position);
//                        }
//                    }
                    break;
                case TYPE_TV:
//                    电台封面及直播标签、电台名称
//                    电视内容标签+正在直播：节目名称 /若获取不到节目单则展示：暂无节目单
                    mTvSearchTitle.setKeywordsAndColor(searchProgramBean.getName(), keywords,
                            ResUtil.getColor(R.color.search_key_word_text_color));
                    mClSearchResult.setContentDescription(searchProgramBean.getName());
                    typeTv.setText("电视");
                    ViewUtil.setViewVisibility(mTvSearchSubTitle, View.VISIBLE);
                    mTvSearchSubTitle.setText("");
//                    mTvSearchSubTitle.setText(getHostMaxLength(searchProgramBean));

                    if (StringUtil.isNotEmpty(searchProgramBean.getCurrentProgram())) {
                        updateBroadcastOrTvSubTitle(searchProgramBean.getCurrentProgram());
                    } else {
                        updateBroadcastOrTvSubTitleToNoCurrentProgram();
                    }
//                    if (searchProgramBean.getProgramEnable() == 1) {
//                        //获取正在播放的广播播单信息
//                        if (searchProgramBean.currentPlayingPlayItem != null) {
//                            updateBroadcastOrTvSubTitle(searchProgramBean.currentPlayingPlayItem);
//                        } else {
//                            getCurrentTvPlayItem(searchProgramBean, position);
//                        }
//                    }
                    break;
                case TYPE_ALBUM:
                case ResType.TYPE_VIDEO_ALBUM:
//                    专辑封面及VIP/精品标签（无则不展示）、专辑名称
//                    专辑内容标签+运营推荐语（后台配置）
                    ViewUtil.setViewVisibility(mTvSearchSubTitle, View.VISIBLE);
//                    mTvSearchSubTitle.setText(getHostMaxLength(searchProgramBean));
                    mTvSearchTitle.setKeywordsAndColor(searchProgramBean.getAlbumName(), keywords,
                            ResUtil.getColor(R.color.search_key_word_text_color));
                    mClSearchResult.setContentDescription(searchProgramBean.getAlbumName());
//                    mTvSearchSubTitle.setText(getHost(searchProgramBean));
                    typeTv.setText("专辑");
                    mTvSearchSubTitle.setText(searchProgramBean.getRecommend());
                    break;
                case TYPE_LIVE:
                    mTvSearchTitle.setKeywordsAndColor(searchProgramBean.getName(), keywords,
                            ResUtil.getColor(R.color.search_key_word_text_color));
                    mClSearchResult.setContentDescription(searchProgramBean.getName());
                    typeTv.setText("直播");
                    mTvSearchSubTitle.setText(getHostMaxLength(searchProgramBean));
                    break;
                case TYPE_QQ_SCENE:
                case TYPE_QQ_TAG:
                    ViewUtil.setViewVisibility(mTvSearchSubTitle, View.VISIBLE);
                    mTvSearchTitle.setKeywordsAndColor(searchProgramBean.getName(), keywords,
                            ResUtil.getColor(R.color.search_key_word_text_color));
                    mTvSearchSubTitle.setText(getHostMaxLength(searchProgramBean));
                    mClSearchResult.setContentDescription(searchProgramBean.getName());
                    typeTv.setText("单曲");
                    break;
                case TYPE_AUDIO:
                case ResType.TYPE_VIDEO_AUDIO:
//                    所属专辑封面及VIP/精品标签（无则不展示）、单曲名称
//                     单曲内容标签+所属专辑名
                    ViewUtil.setViewVisibility(mTvSearchSubTitle, View.VISIBLE);
                    mTvSearchTitle.setKeywordsAndColor(searchProgramBean.getName(), keywords,
                            ResUtil.getColor(R.color.search_key_word_text_color));
                    mClSearchResult.setContentDescription(searchProgramBean.getName());
//                    String host = getHost(searchProgramBean);
//                    if (TextUtils.isEmpty(host)) {
//                    } else {
//                        mTvSearchSeconSubTitle.setText(host);
//                    }
                    typeTv.setText("单曲");
                    mTvSearchSubTitle.setText(searchProgramBean.getAlbumName());
                    break;
                case TYPE_FEATURE:
//                    专题封面、专题名称
//                     专题内容标签+运营推荐语（后台配置）
                    ViewUtil.setViewVisibility(mTvSearchSubTitle, View.VISIBLE);
//                    mTvSearchSubTitle.setText(getHostMaxLength(searchProgramBean));
                    mTvSearchTitle.setKeywordsAndColor(StringUtil.isNotEmpty(searchProgramBean.getAlbumName()) ? searchProgramBean.getAlbumName() : searchProgramBean.getName(), keywords,
                            ResUtil.getColor(R.color.search_key_word_text_color));
                    mClSearchResult.setContentDescription(StringUtil.isNotEmpty(searchProgramBean.getAlbumName()) ? searchProgramBean.getAlbumName() : searchProgramBean.getName());
//                    mTvSearchSubTitle.setText(getHost(searchProgramBean));
                    typeTv.setText("专题");
                    mTvSearchSubTitle.setText(searchProgramBean.getRecommend());
                    break;
                default:
                    break;
            }
        }

        private boolean isPlayingCurrent(Long id, Integer type) {
            PlayItem curPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
            if (curPlayItem == null) return false;
            switch (type) {
                case TYPE_RADIO:
                    if (curPlayItem.getType() != type) return false;
                    return TextUtils.equals(curPlayItem.getRadioId(), String.valueOf(id));
                case TYPE_ALBUM:
                case TYPE_FEATURE:
                    if (curPlayItem.getType() != type) return false;
                    return curPlayItem.getAlbumId() != null && curPlayItem.getAlbumId().equals(String.valueOf(id));
                case TYPE_BROADCAST:
                case TYPE_TV:
                    if (curPlayItem.getType() != type) return false;
                    return curPlayItem.getRadioId() != null && curPlayItem.getRadioId().equals(String.valueOf(id));
                case TYPE_QQ_SCENE:
                case TYPE_QQ_TAG:
                case TYPE_AUDIO:
                case TYPE_LIVE:
                    return curPlayItem.getAudioId() == id;
                case TYPE_VIDEO_ALBUM:
                    return curPlayItem.getAlbumId().equals(String.valueOf(id));
                default:
                    return curPlayItem.getAudioId() == id;
            }
        }

//        private void getCurrentTvPlayItem(SearchProgramBeanResult searchProgramBean, int position) {
//            TVRequest tvRequest = new TVRequest();
//            if (mLifecycleTransformer != null) {
//                tvRequest = tvRequest.bindLifecycle(mLifecycleTransformer);
//            }
//            tvRequest.getTVCurrentProgramDetails(searchProgramBean.getId(), new HttpCallback<TVProgramDetails>() {
//                @Override
//                public void onSuccess(TVProgramDetails programDetails) {
//                    TVPlayItem tvPlayItem = PlayListUtils.translateToPlayItem(programDetails, searchProgramBean.getFreq(), 0);
//                    searchProgramBean.currentPlayingPlayItem = tvPlayItem;
//                    if (getAdapterPosition() == position) {
//                        //更新播放信息
//                        updateBroadcastOrTvSubTitle(tvPlayItem);
//                    }
//                }
//
//                @Override
//                public void onError(ApiException e) {
//                    searchProgramBean.currentPlayingPlayItem = null;
//                    if (getAdapterPosition() == position) {
//                        //更新播放信息
//                        updateBroadcastOrTvSubTitle(null);
//                    }
//                }
//            });
//        }

//        private void getCurrentBroadcastPlayItem(SearchProgramBeanResult searchProgramBean, int position) {
//            BroadcastRequest broadcastRequest = new BroadcastRequest();
//            if (mLifecycleTransformer != null) {
//                broadcastRequest = broadcastRequest.bindLifecycle(mLifecycleTransformer);
//            }
//            broadcastRequest.getBroadcastCurrentProgramDetails(searchProgramBean.getId(), new HttpCallback<ProgramDetails>() {
//                @Override
//                public void onSuccess(ProgramDetails programDetails) {
//                    BroadcastPlayItem broadcastPlayItem = PlayListUtils.translateToPlayItem(programDetails, searchProgramBean.getFreq(), PlayerConstants.SORT_ACS, 0);
//                    searchProgramBean.currentPlayingPlayItem = broadcastPlayItem;
//                    if (getAdapterPosition() == position) {
//                        //更新播放信息
//                        updateBroadcastOrTvSubTitle(broadcastPlayItem);
//                    }
//                }
//
//                @Override
//                public void onError(ApiException e) {
//                    searchProgramBean.currentPlayingPlayItem = null;
//                    if (getAdapterPosition() == position) {
//                        //更新播放信息
//                        updateBroadcastOrTvSubTitle(null);
//                    }
//                }
//            });
//        }

        //        private void updateBroadcastOrTvSubTitle(PlayItem playItem) {
//            if (playItem == null) {
//                mTvSearchSubTitle.setText("");
//                return;
//            }
//            mTvSearchSubTitle.setText(String.format(ResUtil.getString(R.string.comprehensive_player_playing_title), playItem.getTitle()));
//        }
        private void updateBroadcastOrTvSubTitle(String title) {
            if (title == null) {
                mTvSearchSubTitle.setText("");
                return;
            }
            mTvSearchSubTitle.setText(StringUtil.getMaxStringCenterDot(String.format(ResUtil.getString(R.string.comprehensive_player_playing_title), title), 25));
        }

        private void updateBroadcastOrTvSubTitleToNoCurrentProgram() {
            mTvSearchSubTitle.setText(ResUtil.getString(R.string.comprehensive_player_broadcast_no_playlist));
        }


        private String getHost(SearchProgramBean searchProgramBean) {
            List<Compere> comperes = searchProgramBean.getComperes();
            StringBuilder host = new StringBuilder();
            for (Compere compere : comperes) {
                host.append(compere.getName()).append("、");
            }
            if (host.length() > 0) {
                host.deleteCharAt(host.length() - 1);
            }
            return host.toString();
        }

        private String getHostMaxLength(SearchProgramBean searchProgramBean) {
            return StringUtil.getMaxString(getHost(searchProgramBean), 12);
        }
    }
}
