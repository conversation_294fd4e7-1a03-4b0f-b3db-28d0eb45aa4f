package com.kaolafm.kradio.user.channel;

import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.setting.SettingItem;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-05-31
 */
@Aspect
public class ChannelAspect {

    @AfterReturning(value = "execution(* com.kaolafm.kradio.setting.SettingModel.getHiddenItems(..))", returning = "itemList")
    public void addSwitchChannel(List<SettingItem> itemList, JoinPoint point) throws Throwable {
        SettingItem autoAdapter = new SettingItem(R.drawable.settings_channel_back_door, "渠道切换")
                .setFunctionId(SettingItem.ITEM_FUNCTION_AUTO_ADAPTATION)
                .setProcessType(SettingItem.TYPE_FRAGMENT)
                .setInstance(new SwitchChannelFragment());
        itemList.add(autoAdapter);
    }
}
