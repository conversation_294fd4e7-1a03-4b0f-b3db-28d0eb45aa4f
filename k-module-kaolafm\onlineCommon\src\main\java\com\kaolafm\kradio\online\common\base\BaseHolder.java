package com.kaolafm.kradio.online.common.base;

import androidx.recyclerview.widget.RecyclerView;
import android.view.View;

public class BaseHolder extends RecyclerView.ViewHolder {
    protected OnViewClickListener mViewClickListener;

    public BaseHolder(View itemView) {
        super(itemView);
    }

    public void setOnViewHolderClickListener(OnViewClickListener listener) {
        mViewClickListener = listener;
    }

    public interface OnViewClickListener {
        /**
         * 点击事件
         *
         * @param position 可能会返回-1，当改条目被remove后会返回-1.
         */
        void onViewClick(View view, int position);
    }
}