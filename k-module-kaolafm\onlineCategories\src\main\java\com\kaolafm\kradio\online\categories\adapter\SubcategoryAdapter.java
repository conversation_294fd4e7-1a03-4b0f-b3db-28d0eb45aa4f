package com.kaolafm.kradio.online.categories.adapter;

import android.text.TextUtils;
import android.util.Log;
import android.view.ViewGroup;

import com.kaolafm.kradio.common.SubcategoryItemBean;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.common.ResType;
import com.kaolafm.kradio.online.categories.holder.BaseSubcategoryViewHolder;
import com.kaolafm.kradio.online.categories.holder.BroadcastCategoryViewHolder;
import com.kaolafm.kradio.online.categories.holder.BroadcastLocalViewHolder;
import com.kaolafm.kradio.online.categories.holder.RadioChannelViewHolder;
import com.kaolafm.kradio.online.categories.holder.SongMenuViewHolder;
import com.kaolafm.kradio.online.categories.holder.TitleViewHolder;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

/**
 * 具体每一页二级分类的adapter
 *
 * <AUTHOR>
 * @date 2018/4/25
 */

public class SubcategoryAdapter extends BaseAdapter<SubcategoryItemBean> {

    @Override
    public int getItemViewType(int position) {
        return position < getItemCount() ? mDataList.get(position).getItemType() : 0;
    }

    public void setSelected() {
        PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
        if (playItem == null) {
            return;
        }
        String playingId = playItem.getRadioId();
        int radioType = playItem.getType();
        if (radioType == PlayerConstants.RESOURCES_TYPE_ONE_KEY_LISTENER_TYPE) {
            radioType = ResType.ALBUM_TYPE;
        }

        for (int i = 0, size = getItemCount(); i < size; i++) {
            SubcategoryItemBean itemBean = mDataList.get(i);
            if (itemBean.isSelected()) {
                itemBean.setSelected(false);
                notifyItemChanged(i);
            }
            //ID和类型一样就显示播放状态
            if (TextUtils.equals(String.valueOf(itemBean.getId()), playingId) && radioType == itemBean.getResType()) {
                Log.i("kradio.cate", "  setSelected: title=" + itemBean.getName());
                itemBean.setSelected(true);
                notifyItemChanged(i);
            }
        }

    }


    @Override
    protected BaseHolder<SubcategoryItemBean> getViewHolder(ViewGroup parent, int viewType) {
        switch (viewType) {
            case SubcategoryItemBean.TYPE_ITEM_TITLE:
                return new TitleViewHolder(inflate(parent, R.layout.online_item_subcategory_title, viewType));
            case SubcategoryItemBean.TYPE_ITEM_ALBUM:
                return new SongMenuViewHolder(inflate(parent, R.layout.online_item_subcategory_songmenu, viewType));
            case SubcategoryItemBean.TYPE_ITEM_RADIO_CHANNEL:
            case SubcategoryItemBean.TYPE_ITEM_TV:
                return new RadioChannelViewHolder(inflate(parent, R.layout.online_item_subcategory_radio_channel, viewType), null);
            case SubcategoryItemBean.TYPE_ITEM_BROADCAST_CATEGORY:
                return new BroadcastCategoryViewHolder(inflate(parent, R.layout.online_item_subcategory_broadcast_category, viewType));
            case SubcategoryItemBean.TYPE_ITEM_BROADCAST_LOCAL:
                return new BroadcastLocalViewHolder(inflate(parent, R.layout.online_item_subcategory_broadcast_local, viewType));
            default:
                return new BaseSubcategoryViewHolder(inflate(parent, R.layout.online_item_subcategory_title, viewType));
        }
    }
}
