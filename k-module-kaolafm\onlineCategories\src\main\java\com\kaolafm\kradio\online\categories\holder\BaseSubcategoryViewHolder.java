package com.kaolafm.kradio.online.categories.holder;

import android.content.Context;
import android.content.res.Resources.NotFoundException;
import android.view.View;

import com.kaolafm.kradio.common.SubcategoryItemBean;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;

/**
 * 次级分类的ViewHolder的基类。做一些功能操作。
 *
 * <AUTHOR>
 * @date 2018/4/25
 */

public class BaseSubcategoryViewHolder extends BaseHolder<SubcategoryItemBean> {

    protected final Context mContext;

    //    ViewStub mVsLayoutPlaying;
    private View view_item_subscription_mongolian;
    private View view_item_subscription_mongolian_bg;

    public BaseSubcategoryViewHolder(View itemView) {
        super(itemView);
        mContext = itemView.getContext();
        try {
            view_item_subscription_mongolian = itemView.findViewById(R.id.view_item_subscription_mongolian);
            view_item_subscription_mongolian_bg = itemView.findViewById(R.id.view_item_subscription_mongolian_bg);
        } catch (NotFoundException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void setupData(SubcategoryItemBean subcategoryItemBean, int position) {
        setPlay(subcategoryItemBean);
    }

    /**
     * 设置播放动画
     *
     * @param subcategoryItemBean
     */
    private void setPlay(SubcategoryItemBean subcategoryItemBean) {
        if (subcategoryItemBean.isSelected()) {
            if (view_item_subscription_mongolian != null) {
                view_item_subscription_mongolian.setVisibility(View.VISIBLE);
            }
//            if (view_item_subscription_mongolian_bg != null) {
//                view_item_subscription_mongolian_bg.setVisibility(View.GONE);
//            }
        } else {
            if (view_item_subscription_mongolian != null) {
                view_item_subscription_mongolian.setVisibility(View.GONE);
            }
//            if (view_item_subscription_mongolian_bg != null) {
//                view_item_subscription_mongolian_bg.setVisibility(View.VISIBLE);
//            }
        }
    }
}
