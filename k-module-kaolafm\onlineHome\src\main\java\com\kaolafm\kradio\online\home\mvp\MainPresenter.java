package com.kaolafm.kradio.online.home.mvp;

import android.content.Intent;
import android.util.Log;

import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.kradio.common.http.api.volume.VolumeRequest;
import com.kaolafm.kradio.home.HomeDataManager;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioPlayLogicInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioSystemSourceChangeInter;
import com.kaolafm.kradio.lib.base.mvp.IPresenter;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.IntentUtils;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.IPlayerInteractionFiredListener;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;
import com.kaolafm.report.event.StartReportEvent;
import com.kaolafm.report.util.ReportParameterManager;

public class MainPresenter implements IPresenter {
    private static final String TAG = "LauncherPresenter";



    private KRadioAudioPlayLogicInter mKRadioAudioPlayLogicInter;

    private KRadioSystemSourceChangeInter mKRadioSystemSourceChangeInter;

    private IPlayerInteractionFiredListener mPlayerInteractionFiredListener;

    public MainPresenter() {
    }

    @Override
    public void start() {
    }

    @Override
    public void destroy() {
    }

    public void reportAppStart(Intent intent) {
        String type = StartReportEvent.TYPE_LAUNCH;
        if (intent != null) {
            String tempType = intent.getStringExtra(IntentUtils.START_TYPE);
            if (!StringUtil.isEmpty(tempType)) {
                type = tempType;
            }
        }
        ReportParameterManager.getInstance().forceSetAppStartType(type);
    }

    public void playNetOrLocal() {
        HomeDataManager.getInstance().autoPlay();
    }

    public void init() {
        mKRadioAudioPlayLogicInter = ClazzImplUtil.getInter("KRadioAudioPlayLogicImpl");
        mKRadioSystemSourceChangeInter = ClazzImplUtil.getInter("KRadioSystemSourceChangeImpl");
        initPlayerInteractionFiredListener();
    }

    public void onPlayerInitSuccess() {
        requestAudioFocus();
        checkVolumeBalance();
        registerSourceChanged();
//        MediaSessionUtil.getInstance().registerMediaSession();
    }

    private void checkVolumeBalance() {
        new VolumeRequest().checkVolumeBalanceStatus(new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean isOpen) {
                int openValue = isOpen ? 1 : 0;
                PlayerManager.getInstance().setLoudnessNormalization(openValue);
            }

            @Override
            public void onError(ApiException e) {

            }
        });
    }

    /**
     * 注册源切换
     */
    private void registerSourceChanged() {
        if (mKRadioSystemSourceChangeInter != null) {
            mKRadioSystemSourceChangeInter.registerSourceChanged();
        }
    }

    private void requestAudioFocus() {
        if (mKRadioAudioPlayLogicInter != null) {
            boolean flag = mKRadioAudioPlayLogicInter.requestAudioFocus();
            Log.i(TAG, "requestAudioFocus-------->flag = " + flag);
        }
    }

    private void initPlayerInteractionFiredListener() {
        mPlayerInteractionFiredListener = (playItem, position, id) -> {
            PlayerLogUtil.log(getClass().getSimpleName(), "ijk soft ad callback: position" + position + ", id = " + id);
            exposureAd(String.valueOf(id));
        };
        PlayerManager playerManager = PlayerManager.getInstance();
        if (playerManager.isPlayerInitSuccess()) {
            playerManager.setPlayerInteractionFiredListener(mPlayerInteractionFiredListener);
        } else {
            playerManager.addPlayerInitComplete(b -> playerManager.setPlayerInteractionFiredListener(mPlayerInteractionFiredListener));
        }
    }

    /**
     * 广告曝光
     *
     * @param adId
     */
    public void exposureAd(String adId) {
        AdvertisingManager.getInstance().expose(adId,
                String.valueOf(ScreenUtil.getScreenWidth()),
                String.valueOf(ScreenUtil.getScreenHeight()),
                "4",
                ""
        );
    }

}
