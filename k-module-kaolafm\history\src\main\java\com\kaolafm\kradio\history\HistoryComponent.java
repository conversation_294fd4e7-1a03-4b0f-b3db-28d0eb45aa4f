package com.kaolafm.kradio.history;

import com.kaolafm.kradio.component.BaseComponent;
import com.kaolafm.kradio.history.processor.QueryHistoryProcessor;
import com.kaolafm.kradio.history.processor.SaveHistoryProcessor;

/**
 * 历史组件接口类。
 * <AUTHOR>
 * @date 2019-10-23
 */
public class HistoryComponent extends BaseComponent {

    @Override
    protected void initProcessors() {
        addProcessor(new SaveHistoryProcessor());
        addProcessor(new QueryHistoryProcessor());
    }
}
