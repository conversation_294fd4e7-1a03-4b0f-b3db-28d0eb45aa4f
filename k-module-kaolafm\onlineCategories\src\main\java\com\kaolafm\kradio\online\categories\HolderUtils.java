package com.kaolafm.kradio.online.categories;

import com.kaolafm.kradio.common.SubcategoryItemBean;
import com.kaolafm.kradio.lib.base.flavor.KRadioPicSettingInter;
import com.kaolafm.kradio.lib.utils.UrlUtil;

public class HolderUtils {

    public static String getUrlString(SubcategoryItemBean subcategoryItemBean, KRadioPicSettingInter picSetting) {
        String picUrl = subcategoryItemBean.getCoverUrl();
        if(picSetting != null){
            picUrl = picSetting.getAllCategoryPicUrl(picUrl);
        }else{
            picUrl = UrlUtil.getDefaultConfigPicUrl(picUrl);
        }
        return picUrl;
    }
}
