package com.kaolafm.kradio.categories.broadcast;

import android.content.res.Configuration;
import android.graphics.Rect;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alibaba.android.arouter.utils.TextUtils;
import com.kaolafm.kradio.common.widget.CustomerRefreshBottom;
import com.kaolafm.kradio.home.comprehensive.gallery.PageJumper;
import com.kaolafm.kradio.home.comprehensive.ui.view.BaseBackFragment;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.bean.BroadcastRadioDetailData;
import com.kaolafm.kradio.lib.report.ReportUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.RecyclerViewExposeUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.UrlUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.AlbumPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.BroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.FeaturePlayItem;
import com.kaolafm.opensdk.player.logic.model.item.LivePlayItem;
import com.kaolafm.opensdk.player.logic.model.item.RadioPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.TVPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.lcodecore.tkrefreshlayout.RefreshListenerAdapter;
import com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout;
import com.lcodecore.tkrefreshlayout.utils.ScrollingUtil;

import java.util.List;

/**
 * 分类广播列表页面
 * Created by kaolafm on 2018/4/25.
 */

public class BroadcastTabContentFragment extends BaseBackFragment<BroadcastPresent>
        implements IBroadcastView, RecyclerViewExposeUtil.OnItemExposeListener {

    private static final String TAG = "BroadcastTabContentFM";

    RecyclerView mRvBroadcastList;

    TwinklingRefreshLayout mTrflBroadcastListRefresh;

    private BroadcastAdapter mBroadcastAdapter;

    private static final String KEY_BROADCAST_CATEGORY_ID = "broadcastCategoryId";

    private static final String KEY_BROADCAST_CATEGORY_NAME = "categoryName";

    private int mCategoryId;

    private String mCategoryName;

    private BasePlayStateListener mPlayerStateListener;
    private int lineCount = 2;// 默认横板，设置初始化值
    private GridLayoutManager gridLayoutManager;
    View mLoadingView;
    TextView mScrollUp, mScrollDown;

    public static BroadcastTabContentFragment newInstance(int categoryId, String categoryName) {
        Bundle args = new Bundle();
        args.putInt(KEY_BROADCAST_CATEGORY_ID, categoryId);
        args.putString(KEY_BROADCAST_CATEGORY_NAME, categoryName);
        BroadcastTabContentFragment fragment = new BroadcastTabContentFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void initView(View view) {
        super.initView(view);
        // 隐藏掉title
        View titleView = view.findViewById(R.id.bbf_title);
        titleView.setVisibility(View.GONE);
//        TextView titleView = (TextView) View.inflate(getContext(), R.layout.bbf_title_center_textview, null);
//        titleView.setText(mCategoryName);
//        this.addTitleCenterView(titleView);
//        this.setTextlineVisible();
//      设置内容区距离上面的间距
//        this.setContentMaginTop();
        View contentView = View.inflate(getContext(), R.layout.fragment_broadcast_tab_content, null);
        this.addContentView(contentView);
        mLoadingView = contentView.findViewById(R.id.loadingView);
        mTrflBroadcastListRefresh = contentView.findViewById(R.id.trfl_broadcast_list_refresh);
        mRvBroadcastList = contentView.findViewById(R.id.rv_broadcast_list);

        int mCurrentOrientation = ResUtil.getOrientation();
        initBroadcastListView(mCurrentOrientation);

        gridLayoutManager = new GridLayoutManager(getContext(), lineCount);
        gridLayoutManager.setSpanCount(lineCount);

        mRvBroadcastList.setLayoutManager(gridLayoutManager);
        // 关闭 RecyclerView 的 item 动画
        try {
            ((DefaultItemAnimator) mRvBroadcastList.getItemAnimator()).setSupportsChangeAnimations(false);
        } catch (Exception e) {
            e.printStackTrace();
        }
        mRvBroadcastList.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                super.getItemOffsets(outRect, view, parent, state);
                // 四级分类列表下不需要第一行顶部的间距
//                if (parent.getChildLayoutPosition(view) < lineCount) {
//                    outRect.top = ResUtil.getDimen(R.dimen.y30);
//                }
            }
        });
        mBroadcastAdapter = new BroadcastAdapter();

        mBroadcastAdapter.setOnItemClickListener((itemView, viewType, broadcastRadioDetailData, position) -> {
            if (mBroadcastAdapter.getCurrentPlayingId() == broadcastRadioDetailData.getBroadcastId()){
                return;
            }
            if (NetworkUtil.isNetworkAvailable(getContext(), true)) {
                PlayerManagerHelper.getInstance().addBroadcastRadioSimpleItems(mPresenter.itemBean2Simple(mBroadcastAdapter.getDataList()));
                long broadcastId = broadcastRadioDetailData.getBroadcastId();
                int resourcesType = broadcastRadioDetailData.getResourcesType();
                Log.d(TAG, "===mBroadcastAdapter --- onItemClick --- broadcastId:" + broadcastId + ", resourcesType:" + resourcesType);
                // 分类列表中暂无视频类型页面
                if (resourcesType == PlayerConstants.RESOURCES_TYPE_LIVING) {
                    PlayItem playItem = PlayerManagerHelper.getInstance().getCurPlayItem();
                    if (!PlayerManagerHelper.getInstance().isSameProgram(playItem, String.valueOf(broadcastId))) {
                        // 相同则只跳转到直播间；不相同先起播再跳转到直播间
                        PlayerManagerHelper.getInstance().start(String.valueOf(broadcastId), resourcesType);
                    }
                    PageJumper.getInstance().jumpToLivePage(broadcastId);
                } else {
                    PlayerManagerHelper.getInstance().start(String.valueOf(broadcastId), resourcesType);
                }
            }
        });
        mRvBroadcastList.setAdapter(mBroadcastAdapter);

        RecyclerViewExposeUtil exposeUtil = new RecyclerViewExposeUtil();
        exposeUtil.setRecyclerItemExposeListener(mRvBroadcastList, this);

        mScrollUp = contentView.findViewById(R.id.cd_up);
        mScrollDown = contentView.findViewById(R.id.cd_down);
        mScrollUp.setOnClickListener((v) -> ScrollingUtil.scrollRrefreshListByVoice(mRvBroadcastList, mTrflBroadcastListRefresh, -1));
        mScrollDown.setOnClickListener((v) -> ScrollingUtil.scrollRrefreshListByVoice(mRvBroadcastList, mTrflBroadcastListRefresh, 1));

        mPlayerStateListener = new BasePlayStateListener() {
            @Override
            public void onPlayerPlaying(PlayItem playItem) {
                setPlaying();
            }

            @Override
            public void onPlayerPaused(PlayItem playItem) {
                setPlaying();
            }
        };
        PlayerManager.getInstance().addPlayControlStateCallback(mPlayerStateListener);
        mTrflBroadcastListRefresh.setBottomView(new CustomerRefreshBottom(getContext()));
        mTrflBroadcastListRefresh.setHeaderView(null);
        mTrflBroadcastListRefresh.setEnableRefresh(false);
        mTrflBroadcastListRefresh.setAnimation(null);

        mTrflBroadcastListRefresh.setOnRefreshListener(new RefreshListenerAdapter() {
            @Override
            public void onLoadMore(TwinklingRefreshLayout refreshLayout) {
                if (mPresenter != null) {
                    mPresenter.loadMore();
                }
            }
        });
        initBroadcastListView(ResUtil.getOrientation());
    }

    private void setPlaying() {
        PlayItem playItem = PlayerManagerHelper.getInstance().getCurPlayItem();
        if (playItem != null) {
            long id = 0;
            try {
                if (playItem instanceof AlbumPlayItem){
                    // setResourcesType(PlayerConstants.RESOURCES_TYPE_ALBUM);
                    // setResourcesType(PlayerConstants.RESOURCES_TYPE_AUDIO);
                    id = Long.parseLong(playItem.getAlbumId());
                    if (id == 0){
                        id = playItem.getAudioId();
                    }
                } else if (playItem instanceof BroadcastPlayItem){
                    // setResourcesType(PlayerConstants.RESOURCES_TYPE_BROADCAST);
                    id = Long.parseLong(playItem.getAlbumId());
                } else if (playItem instanceof FeaturePlayItem){
                    // setResourcesType(PlayerConstants.RESOURCES_TYPE_FEATURE);
                    id = Long.parseLong(playItem.getAlbumId());
                } else if (playItem instanceof LivePlayItem){
                    // setResourcesType(PlayerConstants.RESOURCES_TYPE_LIVING);
                    id = ((LivePlayItem) playItem).getLiveId();
                } else if (playItem instanceof RadioPlayItem){
                    // setResourcesType(PlayerConstants.RESOURCES_TYPE_RADIO);
                    id = Long.parseLong(playItem.getRadioId());
                } else if (playItem instanceof TVPlayItem){
                    // setResourcesType(PlayerConstants.RESOURCES_TYPE_TV);
                    id = Long.parseLong(playItem.getAlbumId());
                } else {
                    id = Long.parseLong(playItem.getAlbumId());
                }
                Log.i(TAG, "fourlevel--- setPlaying id = " + id);
            } catch (Exception e) {

            }
            if (id > 0) {
                mBroadcastAdapter.setSelected(id);
            }
        }
    }


    @Override
    protected BroadcastPresent createPresenter() {
        return new BroadcastPresent(this);
    }

    @Override
    public void initArgs() {
        Bundle args = getArguments();
        if (args != null) {
            this.mCategoryId = args.getInt(KEY_BROADCAST_CATEGORY_ID);
            this.mCategoryName = args.getString(KEY_BROADCAST_CATEGORY_NAME);
        }
    }

    @Override
    protected int getLayoutId_Tow() {
        return 0;
    }

    @Override
    public void onStart() {
        super.onStart();
        // 转移到懒加载 lazyLoad();
//        mPresenter.loadFirstData(mCategoryId);
//        showLoading();
    }

    @Override
    public void notifyDataChange(List<BroadcastRadioDetailData> resultList, boolean isLoadMore) {
        boolean enableLoadmore = mPresenter.enableLoadmore();
        mTrflBroadcastListRefresh.setEnableLoadmore(enableLoadmore);
        mTrflBroadcastListRefresh.setAutoLoadMore(enableLoadmore);
        mTrflBroadcastListRefresh.setOverScrollRefreshShow(enableLoadmore);

        mTrflBroadcastListRefresh.finishLoadmore();
        if (isLoadMore) {
            mBroadcastAdapter.addDataList(resultList);
        } else {
            mBroadcastAdapter.setDataList(resultList);
        }
        setPlaying();
        hideLoading();
    }

    @Override
    protected void showLoading() {
        super.showLoading();
        ViewUtil.setViewVisibility(mLoadingView, View.VISIBLE);
    }

    @Override
    protected void hideLoading() {
        super.hideLoading();
        ViewUtil.setViewVisibility(mLoadingView, View.GONE);
    }

    @Override
    public void showError() {
        mTrflBroadcastListRefresh.finishLoadmore();
    }

    @Override
    public void showImage(long broadcastId, String programImg, String desc) {
        BroadcastRadioDetailData broadcastRadioDetailData = new BroadcastRadioDetailData();
        broadcastRadioDetailData.setBroadcastId(broadcastId);
        broadcastRadioDetailData.setIcon(programImg);
        broadcastRadioDetailData.setName(desc);
        mBroadcastAdapter.updateItem(broadcastRadioDetailData);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mPresenter.cancelSchedule();
        PlayerManager.getInstance().removePlayControlStateCallback(mPlayerStateListener);
    }

    @Override
    public void onItemViewVisible(boolean visible, int position) {
        if (visible && mBroadcastAdapter != null) {
            BroadcastRadioDetailData bean = mBroadcastAdapter.getItemData(position);
            ReportUtil.addContentShowEvent("", "1", "",
                    String.valueOf(bean.getBroadcastId()), "无",
                    Constants.PAGE_ID_CATEGORY, String.valueOf(bean.getClassifyId()), "");
        }
    }

    static class BroadcastAdapter extends BaseAdapter<BroadcastRadioDetailData> {

        private long mCurrentPlayingId;
        private int mCurrentPosition = -1;

        public BroadcastAdapter() {
        }


        @Override
        protected BaseHolder<BroadcastRadioDetailData> getViewHolder(ViewGroup parent, int viewType) {
            return new BroadcastViewHolder(inflate(parent, R.layout.item_subcategory_broadcast_local, viewType));
        }


        @Override
        public long getItemId(int position) {
            return position;
        }

        @Override
        public int getItemCount() {
            return super.getItemCount();
        }

        public void setSelected(long playingId) {
            if (mCurrentPlayingId != playingId) {
                for (int i = 0, size = getItemCount(); i < size; i++) {
                    BroadcastRadioDetailData itemBean = mDataList.get(i);
                    //取消上一个播放状态
                    long broadcastId = itemBean.getBroadcastId();
                    if (broadcastId == mCurrentPlayingId) {
                        itemBean.setPlaying(false);
                        notifyItemChanged(i);
                    }
                    //ID一样就显示播放状态
                    if (broadcastId == playingId) {
                        itemBean.setPlaying(true);
                        notifyItemChanged(i);
                        mCurrentPosition = i;
                    }
                }
                mCurrentPlayingId = playingId;
            } else {
                if (mCurrentPosition != -1) {
                    notifyItemChanged(mCurrentPosition);
                }
            }
        }

        public void updateItem(BroadcastRadioDetailData data){
            for (int i = 0; i < mDataList.size(); i++) {
                BroadcastRadioDetailData bean = mDataList.get(i);
                if(data.getBroadcastId() == bean.getBroadcastId()){
                    if(!TextUtils.isEmpty(data.getIcon())){
                        bean.setIcon(data.getIcon());
                    }
                    if(!TextUtils.isEmpty(data.getDesc())){
                        bean.setName(data.getDesc());
                    }
                    notifyItemChanged(i);
                    break;
                }
            }
        }
        public long getCurrentPlayingId() {
            return mCurrentPlayingId;
        }

        static class BroadcastViewHolder extends BaseHolder<BroadcastRadioDetailData> {

            ImageView mIvBroadcastCover;
            TextView mTvBroadcastName;
            ImageView live_icon;
            View card_layout_playing;
            TextView tvListenNum;
            ImageView ivPlay;
            View rootView;

            BroadcastViewHolder(final View itemView) {
                super(itemView);
                // 重新设置 itemView[R.layout.item_subcategory_broadcast_local] 根布局的左右边距
                RecyclerView.LayoutParams itemViewParams = (RecyclerView.LayoutParams) itemView.getLayoutParams();
                itemViewParams.leftMargin = ResUtil.getDimen(R.dimen.x28);
                itemViewParams.rightMargin = 0;
                itemView.requestLayout();
                mIvBroadcastCover = itemView.findViewById(R.id.iv_broadcast_cover);
                mTvBroadcastName = itemView.findViewById(R.id.tv_broadcast_name);
                live_icon = itemView.findViewById(R.id.live_icon);
                card_layout_playing = itemView.findViewById(R.id.card_layout_playing);

                tvListenNum = itemView.findViewById(R.id.tvListenNum);
                ivPlay = itemView.findViewById(R.id.ivPlay);
                rootView = itemView.findViewById(R.id.ll_broadcast);
            }

            @Override
            public void setupData(BroadcastRadioDetailData broadcastRadioDetailData, int position) {

                if (broadcastRadioDetailData.isPlaying()) {
                    card_layout_playing.setVisibility(View.VISIBLE);
                    mTvBroadcastName.setActivated(true);
                    itemView.setActivated(true);
                    tvListenNum.setSelected(true);
                    ivPlay.setSelected(true);
                    tvListenNum.setTextColor(ResUtil.getColor(R.color.text_color_7));
                } else {
                    card_layout_playing.setVisibility(View.GONE);
                    mTvBroadcastName.setActivated(false);
                    itemView.setActivated(false);
                    tvListenNum.setSelected(false);
                    ivPlay.setSelected(false);
                    tvListenNum.setTextColor(ResUtil.getColor(R.color.text_color_8));
                }
                tvListenNum.setText(StringUtil.formatNum(broadcastRadioDetailData.getPlayTimes()));
                mTvBroadcastName.setText(broadcastRadioDetailData.getName() + "  " + broadcastRadioDetailData.getFreq());
                rootView.setContentDescription(broadcastRadioDetailData.getName());
                Log.i("BroadcastListFragment", "url = " + broadcastRadioDetailData.getIcon());
                ImageLoader.getInstance().displayImage(itemView.getContext(), UrlUtil.getCustomPicUrl(UrlUtil.PIC_100_100, broadcastRadioDetailData.getIcon()), mIvBroadcastCover,
                        ResUtil.getDrawable(R.drawable.media_default_pic));

                // 左上角角标显示逻辑参考 Component2And1Cell#Component2And1BottomAdapter#onBindViewHolder#holder.vip_icon_bottom
                switch (broadcastRadioDetailData.getResourcesType()){
                    case PlayerConstants.RESOURCES_TYPE_BROADCAST:
                    case PlayerConstants.RESOURCES_TYPE_TV:
                        live_icon.setVisibility(View.VISIBLE);
                        live_icon.setImageDrawable(ResUtil.getDrawable(com.kaolafm.kradio.component.ui.R.drawable.comprehensive_icon_live_class));
                        break;

                    case PlayerConstants.RESOURCES_TYPE_LIVING:
                        // 直播类型，此处获取不到 liveStatus ，固定展示 [直播间] 角标
                        live_icon.setVisibility(View.VISIBLE);
                        live_icon.setImageDrawable(ResUtil.getDrawable(com.kaolafm.kradio.component.ui.R.drawable.comprehensive_live_icon_reday));
                        break;

                        // todo 视频类型角标
                        //  live_icon.setImageDrawable(ResUtil.getDrawable(com.kaolafm.kradio.component.ui.R.drawable.comprehensive_icon_video));

                    default:
                        live_icon.setVisibility(View.INVISIBLE);
                        break;
                }
            }
        }
    }

    @Override
    protected void showAccordingToScreen(int orientation) {
        super.showAccordingToScreen(orientation);
        ConstraintLayout.LayoutParams lp = (ConstraintLayout.LayoutParams) mLoadingView.findViewById(R.id.llRefresh).getLayoutParams();
        lp.verticalBias = ResUtil.getFloat(R.dimen.loading_vertical_bias);
        mLoadingView.findViewById(R.id.llRefresh).setLayoutParams(lp);
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        initBroadcastListView(newConfig.orientation);
        gridLayoutManager.setSpanCount(lineCount);
        mRvBroadcastList.setLayoutManager(gridLayoutManager);
        mBroadcastAdapter.notifyDataSetChanged();
    }

    @Override
    public void onMultiWindowModeChanged(boolean isInMultiWindowMode) {
        super.onMultiWindowModeChanged(isInMultiWindowMode);
        RecyclerView.Adapter adapter = mRvBroadcastList.getAdapter();
        RecyclerView.LayoutManager manager = mRvBroadcastList.getLayoutManager();
        mRvBroadcastList.setAdapter(null);
        mRvBroadcastList.setLayoutManager(null);
        mRvBroadcastList.getRecycledViewPool().clear();
        mRvBroadcastList.setLayoutManager(manager);
        mRvBroadcastList.setAdapter(adapter);
    }

    private void initBroadcastListView(int orientation) {
        int padding = ResUtil.getDimen(R.dimen.y10);

        int globalPaddingRight;
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            lineCount = 2;
            globalPaddingRight = ResUtil.getDimen(R.dimen.x39);
        } else {
            lineCount = 1;
            globalPaddingRight = 0;
        }
        int globalPaddingLeft = ScreenUtil.getGlobalPaddingLeft(orientation);
//        mRvBroadcastList.setPadding(globalPaddingLeft, 0, globalPaddingRight, 0);
    }

    /*
     * ----------------------------------
     * 懒加载相关
     * ----------------------------------
     */
    /**
     * 是否已经初始化
     */
    private boolean isInit = false;
    /**
     * 是否已经加载过数据
     */
    protected boolean isLoaded = false;
    private boolean isVisibleToUser;

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        if (isVisibleToUser && !isInit) {
            isInit = true;
            isLoaded = true;
            lazyLoad();
        } else {
            isInit = true;
        }
        // 去除基类 BaseBackFragment 添加的 topMargin
        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) root_layout_back.getLayoutParams();
        layoutParams.setMargins(0, 0, 0, 0);
        root_layout_back.setLayoutParams(layoutParams);
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        this.isVisibleToUser = isVisibleToUser;
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser && isInit && !isLoaded) {
            isLoaded = true;
            lazyLoad();
        }
        Log.d("BaseLazyFragment", "isLoaded = " + isLoaded + " isVisibleToUser = " + isVisibleToUser);
        //下边的代码打开后,会导致后一页划出时,是空白的.
        //注释掉后,会导致,横竖屏切换,页面item出现重叠.
        if (getView() != null) {
            getView().setVisibility(isVisibleToUser ? View.VISIBLE : View.GONE);
        }
    }

    /**
     * 懒加载数据
     */
    private void lazyLoad() {
        mPresenter.loadFirstData(mCategoryId);
        showLoading();
    }

    ;
    /*
     * ----------------------------------
     * 懒加载相关
     * ----------------------------------
     */
}
