package com.kaolafm.kradio.online.mine.order;

import android.graphics.Rect;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.kaolafm.kradio.common.base.BaseSkinAppCompatActivity;
import com.kaolafm.kradio.constant.UserComponentConst;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.utils.ComponentUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.router.IRouterConsumer;
import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.kradio.purchase.PayManager;
import com.kaolafm.kradio.purchase.constant.PayConst;
import com.kaolafm.opensdk.api.purchase.model.Order;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.PageShowReportEvent;
import com.kaolafm.report.util.ReportParameterManager;

import java.util.List;

/**
 * @Package: com.kaolafm.kradio.k_kaolafm
 * @Description: 我的订单
 * @Author: Maclay
 * @Date: 14:52
 */
@Route(path = RouterConstance.ONLINE_URL_MINE_ORDER)
public class MyOrderActivity extends BaseSkinAppCompatActivity<OrderPresenter> implements IMyOrderView, IRouterConsumer {
    private RecyclerView rv;
    private OrderAdapter adapter;
    private TextView tvHelp;
    private RelativeLayout vsErrorLayout;
    protected long startTime = -1;

    @Override
    public int getLayoutId() {
        return R.layout.online_fragment_my_order;
    }

    @Override
    protected void onStart() {
        super.onStart();
        startTime = System.currentTimeMillis();
    }

    @Override
    protected void onPause() {
        super.onPause();
        reportPageShowEvent();
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        RouterManager.getInstance().addRouterConsumer(this);
        tvHelp = findViewById(R.id.tvHelp);
        changeViewLayoutForStatusBar((View) tvHelp.getParent());
        rv = findViewById(R.id.rv);
        showAccordingToScreen(ResUtil.getOrientation());
        rv.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                super.getItemOffsets(outRect, view, parent, state);
                int pos = parent.getChildLayoutPosition(view);
                if (pos == 0) {
                    outRect.top = ResUtil.getDimen(R.dimen.y37);
                } else {
                    outRect.top = ResUtil.getDimen(R.dimen.y36);
                }
                if (pos == parent.getAdapter().getItemCount() - 1) {
                    outRect.bottom = ResUtil.getDimen(R.dimen.y87);
                }
            }
        });
        rv.addOnScrollListener(new RecyclerView.OnScrollListener() {
            private int lastVisibleItem;
            private boolean refreshTag = true;

            @Override
            public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (mPresenter != null && !mPresenter.hasMore()) {
                    boolean bottom = recyclerView.canScrollVertically(1);
                    if (!bottom) {
                        //需求变更，隐藏掉联系方式
                        ViewUtil.setViewVisibility(tvHelp, View.GONE);
                    } else {
                        ViewUtil.setViewVisibility(tvHelp, View.GONE);
                    }
                } else {
                    if (!refreshTag && newState == RecyclerView.SCROLL_STATE_IDLE && lastVisibleItem + 1 == recyclerView.getAdapter().getItemCount()) {
                        mPresenter.loadingNext();
                    }
                }
            }

            @Override
            public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                LinearLayoutManager layoutManager = (LinearLayoutManager) recyclerView.getLayoutManager();
                lastVisibleItem = layoutManager.findLastVisibleItemPosition();
                if (dy > 0) {
                    refreshTag = false;
                } else {
                    refreshTag = true;
                }
            }
        });
        rv.setLayoutManager(new LinearLayoutManager(MyOrderActivity.this));
        rv.setAdapter(adapter = new OrderAdapter());

        if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(MyOrderActivity.this)) {
            showError();
            return;
        }
        if (mPresenter != null) {
            mPresenter.getList();
        }
    }

    @Override
    public void initData() {

    }

    @Override
    protected OrderPresenter createPresenter() {
        return new OrderPresenter(this);
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (mPresenter != null) {
            mPresenter.start();
        }
    }

    @Override
    public void showError(String error, boolean clickToRetry) {
        if (vsErrorLayout == null) {
            vsErrorLayout = (RelativeLayout) ((ViewStub) findViewById(R.id.vsErrorLayout)).inflate();
            TextView tvNetworkNosign = vsErrorLayout.findViewById(R.id.tv_error);
            tvNetworkNosign.setText(error);
            ImageView ivNetworkNoSign = vsErrorLayout.findViewById(R.id.iv_error);
            // 支持点击重试
            if (clickToRetry) {
                vsErrorLayout.setOnClickListener(v -> {
                    hideError();
                    if (mPresenter != null) {
                        mPresenter.getList();
                    }
                });
            } else {
                ivNetworkNoSign.setImageDrawable(ResUtil.getDrawable(R.drawable.online_error_empty));
            }
        }
        ViewUtil.setViewVisibility(vsErrorLayout, View.VISIBLE);
    }

    @Override
    public void hideError() {
        if (vsErrorLayout == null) {
            return;
        }
        ViewUtil.setViewVisibility(vsErrorLayout, View.GONE);
    }

    @Override
    public void showList(List<Order> list, int currentPage) {
        if (list != null && list.size() > 0) {
            hideError();
            if (currentPage == 1) {
                adapter.setDataList(list);
            } else {
                adapter.addDataList(list);
            }
            if (!mPresenter.hasMore()) {
                rv.post(() -> {
                    int lastIndex = ((LinearLayoutManager) rv.getLayoutManager()).findLastCompletelyVisibleItemPosition();
                    if (lastIndex == adapter.getItemCount() - 1) {
                        //需求变更，隐藏掉联系方式
                        ViewUtil.setViewVisibility(tvHelp, View.GONE);
                    }
                });
            }

        } else if (currentPage == 1) {
            showError(ResUtil.getString(R.string.mine_order_empty_info), false);
        }
    }

    @Override
    protected void showAccordingToScreen(int orientation) {
        super.showAccordingToScreen(orientation);
        ConstraintLayout.LayoutParams lp = (ConstraintLayout.LayoutParams) rv.getLayoutParams();
        lp.leftMargin = ResUtil.getDimen(R.dimen.mine_order_item_start);
        lp.rightMargin = ResUtil.getDimen(R.dimen.mine_order_item_end);
        rv.setLayoutParams(lp);
    }

    @Override
    public void showError() {
        adapter.setDataList(null);
        showError(ResUtil.getString(R.string.network_nosigin), true);
    }

    @Override
    public void onDestroy() {
        RouterManager.getInstance().removeRouterConsumer(this);
        super.onDestroy();
        if (mPresenter != null) {
            mPresenter.destroy();
        }
    }

    @Override
    public String consumeRoute(String pageId, Object extra) {
        switch (pageId) {
            case Constants.ONLINE_PAGE_ID_PAY_VIP: //VIP购买
                if (ComponentUtil.getResultValue(UserComponentConst.NAME, UserComponentConst.IS_USER_BOUND)) {
                    PayManager.getInstance()
                            .pay(PayConst.PAY_TYPE_VIP, PlayerManager.getInstance().getCurPlayItem());
                } else {
                    Bundle bundle = new Bundle();
                    bundle.putString("type", ReportParameterManager.getInstance().getPage());
                    RouterManager.getInstance().jumpPage(RouterConstance.ACTIVITY_URL_LOGIN, bundle);
                }
                break;
        }
        return ROUTER_CONSUME_FULLY;
    }

    private class OrderVH extends BaseHolder<Order> {
        private ConstraintLayout bg;
        private View header;
        private TextView tvName, tvState;
        private LinearLayout llContent;
        private ImageView iv;

        public OrderVH(View itemView) {
            super(itemView);
            bg = itemView.findViewById(R.id.bg);
            header = itemView.findViewById(R.id.header);
            tvName = itemView.findViewById(R.id.tvName);
            tvState = itemView.findViewById(R.id.tvState);
            llContent = itemView.findViewById(R.id.llContent);
            iv = itemView.findViewById(R.id.iv);
        }

        @Override
        public void setupData(Order orderItemBean, int position) {
            tvName.setText(orderItemBean.getTitle());
            String status = orderItemBean.getStatusDesc();
            if (TextUtils.isEmpty(status)) {
                status = getOrderStatus(orderItemBean.getStatus());
            }
            tvState.setText(status);
            switch (orderItemBean.getProductType()) {
                case 1:
                    //vip
                    bg.setBackground(ResUtil.getDrawable(R.drawable.online_members_order_vip_bg));
                    header.setBackground(ResUtil.getDrawable(R.drawable.online_members_order_vip_header));
                    iv.setImageDrawable(ResUtil.getDrawable(R.drawable.online_ic_vip));
                    initContent(5);
                    showTxt(0, R.string.mine_order_vip_start_time, orderItemBean.getVipEffectiveDate());
                    showTxt(1, R.string.mine_order_vip_end_time, orderItemBean.getVipExpireDate());
                    showTxt(2, R.string.mine_order_buy_time, orderItemBean.getCreateTime());
                    showTxt(3, R.string.mine_order_pay_type, getPayStr(orderItemBean));
                    showTxt(4, R.string.mine_order_pay_num, orderItemBean.getBillNo());
                    break;
                case 2:
                    //专辑
                case 3:
                    //碎片
                    bg.setBackground(ResUtil.getDrawable(R.drawable.online_members_order_audios_bg));
                    header.setBackground(ResUtil.getDrawable(R.drawable.online_members_order_audios_header));
                    iv.setImageDrawable(ResUtil.getDrawable(R.drawable.online_ic_audios));
                    initContent(3);
                    showTxt(0, R.string.mine_order_buy_time, orderItemBean.getCreateTime());
                    showTxt(1, R.string.mine_order_pay_type, getPayStr(orderItemBean));
                    showTxt(2, R.string.mine_order_pay_num, orderItemBean.getBillNo());
                    break;
            }
        }

        private void initContent(int childCount) {
            while (llContent.getChildCount() < childCount) {
                llContent.addView(LayoutInflater.from(MyOrderActivity.this).inflate(R.layout.online_item_order_text, llContent, false));
            }
            while (llContent.getChildCount() > childCount) {
                llContent.removeViewAt(0);
            }
        }

        private void showTxt(int index, int sId, String content) {
            View item = llContent.getChildAt(index);
            TextView tvDes0 = item.findViewById(R.id.tvDes0);
            tvDes0.setTextColor(ResUtil.getColor(R.color.online_members_order_key_text_color));
            tvDes0.setText(sId);
            TextView tvContent0 = item.findViewById(R.id.tvContent0);
            tvContent0.setTextColor(ResUtil.getColor(R.color.online_members_order_value_text_color));
            tvContent0.setText(content);
        }

        private String getOrderStatus(int status) {
            //0-未支付，1-已取消，2-已支付
            switch (status) {
                case 0:
                    return "未支付";
//                case 1:
//                    return "待支付";
                case 2:
                    return "支付成功";
//                case 3:
//                    return "支付失败";
//                case 4:
//                    return "重新支付";
                case 1:
                    return "取消订单";
                default:
                    return "未知：" + status;
            }
        }

        private String getPayStr(Order orderItemBean) {
            StringBuilder sb = new StringBuilder();
            switch (orderItemBean.getPlantform()) {
                case 1:
                    sb.append("Android端");
                    break;
                case 2:
                    sb.append("iPhone端");
                    break;
                case 3:
                    sb.append("其他端");
                    break;
            }
            sb.append(" ");
            switch (orderItemBean.getPayType()) {
                case 1:
                    sb.append("微信");
                    break;
                case 2:
                    sb.append("支付宝");
                    break;
                case 3:
                    sb.append("云币");
                    break;
                case 4:
                    sb.append("云闪付");
                    break;
            }
            return sb.toString();
        }
    }


    private class OrderAdapter extends BaseAdapter<Order> {

        @Override
        protected BaseHolder<Order> getViewHolder(ViewGroup parent, int viewType) {
            return new OrderVH(LayoutInflater.from(MyOrderActivity.this).inflate(R.layout.online_item_order, parent, false));
        }

    }

    public String getPageId() {
        return Constants.ONLINE_PAGE_ID_MINE_PURCHASED_MY;
    }

    /**
     * 页面曝光事件上报
     */
    private void reportPageShowEvent() {
        long duration = System.currentTimeMillis() - startTime;
        if (startTime < 0 || duration < 300) {
            return;
        }

        PageShowReportEvent event = new PageShowReportEvent();
        event.setPageId(getPageId());
        event.setPageTime(String.valueOf(duration));
        ReportHelper.getInstance().addEvent(event);
        Log.i("BaseDialogFragment", "report=" + duration);
        startTime = -1;
    }
}
