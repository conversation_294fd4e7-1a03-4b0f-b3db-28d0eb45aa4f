<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center">

        <TextView
            android:id="@+id/toast_msg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/online_shape_sys_toast_layout_bg"
            android:ellipsize="end"
            android:gravity="center"
            android:maxHeight="@dimen/y168"
            android:maxLines="2"
            android:minHeight="@dimen/y102"
            android:paddingLeft="@dimen/x56"
            android:paddingRight="@dimen/x56"
            android:shadowColor="#BB000000"
            android:textStyle="bold"
            android:textColor="@color/text_color_toast"
            android:textSize="@dimen/text_size7"
            tools:text="我是Toast" />
    </LinearLayout>
</FrameLayout>
