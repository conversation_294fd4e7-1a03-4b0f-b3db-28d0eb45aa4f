package com.kaolafm.kradio.flavor.impl;

import android.annotation.SuppressLint;
import android.media.AudioManager;
import android.os.Handler;
import android.os.IBinder;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.FutureTarget;
import com.bumptech.glide.request.target.Target;
import com.google.gson.Gson;
import com.incall.serversdk.interactive.DoubleMediaProxy;
import com.incall.serversdk.interactive.callback.IMediaCallback;
import com.incall.serversdk.interactive.media.DoubleMediaConstant;
import com.incall.serversdk.server.OnSrvConnChangeListener;
import com.kaolafm.kradio.bean.MediaSource;
import com.kaolafm.kradio.flavor.BuildConfig;
import com.kaolafm.kradio.launcher.sdk.LauncherSdkManager;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.SyncInstrumentInter;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.kradio.lib.utils.UrlUtil;
import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.IPlayerStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import java.io.File;
import java.util.concurrent.ExecutionException;

import io.reactivex.Observable;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;

public class SyncInstrumentImpl implements SyncInstrumentInter, IPlayerStateListener,
        OnSrvConnChangeListener, IMediaCallback {
    private static final String TAG = "SyncInstrumentImpl";

    private boolean isConSuccess = BuildConfig.DEBUG;
    Gson gson;

    private IPlayerInitCompleteListener onPlayerInitCompleteListener = new IPlayerInitCompleteListener() {
        @Override
        public void onPlayerInitComplete(boolean b) {
            try {
                PlayerManager.getInstance().removePlayerInitComplete(onPlayerInitCompleteListener);
                PlayerManager.getInstance().addPlayControlStateCallback(SyncInstrumentImpl.this);
                DoubleMediaProxy.getInstance().registerConnChangeListener(SyncInstrumentImpl.this);
                LauncherSdkManager.getInstance().registerListener();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    };

    @Override
    public boolean initSyncInstrument() {
        Log.i(TAG, "initSyncInstrument: ");
        initPlayer();
        return true;
    }

    private void initPlayer() {
        PlayerManager playerManager = PlayerManager.getInstance();
        boolean isInitSuccess = playerManager.isPlayerInitSuccess();
        Log.i(TAG, "isInitSuccess=" + isInitSuccess);
        if (isInitSuccess) {
            try {
                PlayerManager.getInstance().addPlayControlStateCallback(SyncInstrumentImpl.this);
                LauncherSdkManager.getInstance().registerListener();
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            playerManager.addPlayerInitComplete(onPlayerInitCompleteListener);
        }
    }

    @Override
    public boolean releaseSyncInstrument() {
        Log.i(TAG, "releaseSyncInstrument: ");
//        PlayerManager.getInstance().removePlayControlStateCallback(this);
//        BroadcastRadioListManager.getInstance().removeOnPlayingRadioChangedListener(this);
        LauncherSdkManager.getInstance().unRegisterListener();
        return true;
    }

    @Override
    public void onIdle(PlayItem playItem) {

    }

    private String toMediaSource(PlayItem playItem, int status) {
        String result = "";
        if (playItem == null) {
            return result;
        }
        MediaSource mediaSource = new MediaSource();
        mediaSource.programName = playItem.getAlbumTitle();
        mediaSource.songName = playItem.getTitle();
        mediaSource.singerName = playItem.getAlbumTitle();
        mediaSource.playStatus = status;
        mediaSource.sourceType = DoubleMediaConstant.MEDIA_SOURCE_NET_RADIO;
        mediaSource.sourceID = longIdToIntId(playItem.getAudioId());
        if (gson == null) {
            gson = new Gson();
        }
        result = gson.toJson(mediaSource);
        return result;
    }

    //长安sdk需要传入int类型，长安建议long转成int，有一定概率id重复，坑爹啊
    private int longIdToIntId(long audioId) {
        int id;
        id = (int) audioId;
        return id;
    }

    @Override
    public void onPlayerPreparing(PlayItem playItem) {
        if (playItem == null) {
            return;
        }
        Log.i(TAG, "onPlayerPreparing: " + playItem.getAlbumTitle() + "|" + playItem.getTitle());
        if (isConSuccess && PlayerManager.getInstance().getCurrentAudioFocusStatus() == AudioManager.AUDIOFOCUS_GAIN) {
            DoubleMediaProxy.getInstance().sendMediaSource(
                    toMediaSource(playItem, DoubleMediaConstant.MEDIA_STATUS_PLAYING));
            sendAbsoluteImagePath(playItem);
        }
    }

    @Override
    public void onPlayerPlaying(PlayItem playItem) {
        if (playItem == null) {
            return;
        }
        Log.i(TAG, "onPlayerPlaying: MEDAI_STATE_START ");
        if (isConSuccess && PlayerManager.getInstance().getCurrentAudioFocusStatus() == AudioManager.AUDIOFOCUS_GAIN) {
            DoubleMediaProxy.getInstance().sendMediaSource(
                    toMediaSource(playItem, DoubleMediaConstant.MEDIA_STATUS_PLAYING));
            //sendAbsoluteImagePath(playItem);
        }
    }

    @Override
    public void onPlayerPaused(PlayItem playItem) {
        Log.i(TAG, "onPlayerPaused: MEDAI_STATE_PAUSE ");
        if (isConSuccess && playItem != null) {
            DoubleMediaProxy.getInstance().sendMediaSource(
                    toMediaSource(playItem, DoubleMediaConstant.MEDIA_STATUS_PAUSE));
        }
    }

    @Override
    public void onProgress(PlayItem playItem, long mProgress, long mDuration) {
        PlayItem item = PlayerManager.getInstance().getCurPlayItem();
        boolean isPlaying = PlayerManager.getInstance().isPlaying();
        if (item != null && isPlaying) {
            long progressTalSeconds = (mProgress + 500) / 1000;
            long durationTalSeconds = (mDuration + 500) / 1000;
            //Log.i(TAG, "progress=" + (int)progressTalSeconds + "/" + (int)durationTalSeconds);
            if (isConSuccess) {
                DoubleMediaProxy.getInstance().sendMediaPlayTime(longIdToIntId(playItem.getAudioId()),
                        (int) progressTalSeconds, (int) durationTalSeconds);
            }
        }
    }

    @Override
    public void onPlayerFailed(PlayItem playItem, int i, int i1) {
        if (playItem == null) {
            return;
        }
        if (isConSuccess && PlayerManager.getInstance().getCurrentAudioFocusStatus() == AudioManager.AUDIOFOCUS_GAIN) {
            DoubleMediaProxy.getInstance().sendMediaSource(
                    toMediaSource(playItem, DoubleMediaConstant.MEDIA_STATUS_PAUSE));
        }
    }

    @Override
    public void onPlayerEnd(PlayItem playItem) {

    }

    @Override
    public void onSeekStart(PlayItem playItem) {

    }

    @Override
    public void onSeekComplete(PlayItem playItem) {

    }

    @Override
    public void onBufferingStart(PlayItem playItem) {

    }

    @Override
    public void onBufferingEnd(PlayItem playItem) {

    }

    @Override
    public void onDownloadProgress(PlayItem playItem, long l, long l1) {

    }

    @Override
    public void onSrvConnChange(boolean flag) {
        Log.i(TAG, "onSrvConnChange=" + flag);
        isConSuccess = flag;
        if (BuildConfig.DEBUG) {
            isConSuccess = true;
        }
        if (isConSuccess) {
            DoubleMediaProxy.getInstance().registerMediaCallback(this);
        }
    }

    @Override
    public void onMediaStatus(int status) throws RemoteException {
        Log.i(TAG, "onMediaStatus=" + status);
    }

    @Override
    public void onMediaInfo(String info) throws RemoteException {
        Log.i(TAG, "onMediaInfo=" + info);
    }

    @Override
    public IBinder asBinder() {
        return null;
    }

    @SuppressLint("CheckResult")
    private synchronized void sendAbsoluteImagePath(PlayItem playItem) {
        Log.i(TAG, "sendAbsoluteImagePath");
        Observable.create((ObservableOnSubscribe<String>) emitter -> {
            String imgPath = "";
            try {
                String originPicUrl = playItem.getPicUrl();
                Log.i(TAG, "originPicUrl:" + originPicUrl);
                String extension = originPicUrl.substring(originPicUrl.lastIndexOf("."));
                String imgUrl = UrlUtil.getDefaultConfigPicUrlforJPG(originPicUrl);

                Logger.i(TAG, "url=" + imgUrl);
                FutureTarget<File> target = Glide.with(AppDelegate.getInstance().getContext())
                        .downloadOnly()
                        .load(imgUrl)
                        .submit(Target.SIZE_ORIGINAL, Target.SIZE_ORIGINAL);
                File imageFile = target.get();
                String filePath = imageFile.getAbsolutePath().substring(0, imageFile.getAbsolutePath().lastIndexOf("."));
                File renameFile = new File(filePath + extension);
                if (imageFile.renameTo(renameFile)) {
                    imageFile = renameFile;
                    Log.i(TAG, "重命名成功");
                }
                imgPath = imageFile.getAbsolutePath();
                Log.i(TAG, "img path=" + imgPath);
            } catch (ExecutionException e) {
                Log.i(TAG, "Exception1:" + e.toString());
            } catch (InterruptedException e) {
                Log.i(TAG, "Exception2:" + e.toString());
            } catch (Exception e) {
                Log.i(TAG, "Exception3:" + e.toString());
            }
            Log.i(TAG, "Submit imgPath:" + imgPath);
            emitter.onNext(imgPath);
        }).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(path -> {
                    Log.i(TAG, "sendAlbumPath= LongID"
                            + playItem.getAudioId()
                            + " IntID:"
                            + longIdToIntId(playItem.getAudioId()));
                    DoubleMediaProxy.getInstance().sendMediaAlbum(longIdToIntId(playItem.getAudioId()), String.valueOf(playItem.getAudioId()), path);

                });
    }

}