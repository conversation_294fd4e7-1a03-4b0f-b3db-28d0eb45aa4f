<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_activated="true" android:state_pressed="true">
        <shape>
            <solid android:color="#8006B5D2" />
            <corners android:bottomLeftRadius="@dimen/y40" android:bottomRightRadius="@dimen/y40" android:topLeftRadius="@dimen/y40" android:topRightRadius="@dimen/y40" />
        </shape>
    </item>

    <item android:state_activated="true" android:state_pressed="false">
        <shape>
            <solid android:color="#06B5D2" />
            <corners android:bottomLeftRadius="@dimen/y40" android:bottomRightRadius="@dimen/y40" android:topLeftRadius="@dimen/y40" android:topRightRadius="@dimen/y40" />
        </shape>
    </item>

    <item android:state_activated="false">
        <shape>
            <solid android:color="#313944" />
            <corners android:bottomLeftRadius="@dimen/y40" android:bottomRightRadius="@dimen/y40" android:topLeftRadius="@dimen/y40" android:topRightRadius="@dimen/y40" />
        </shape>
    </item>
</selector>