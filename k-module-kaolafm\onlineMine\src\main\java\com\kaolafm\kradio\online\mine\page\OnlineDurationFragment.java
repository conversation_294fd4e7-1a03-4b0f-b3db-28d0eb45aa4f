package com.kaolafm.kradio.online.mine.page;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.kaolafm.kradio.k_kaolafm.R; 
import com.kaolafm.kradio.lib.base.mvp.IPresenter;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.lib.base.ui.online.BaseViewPagerFragment;
import com.kaolafm.kradio.lib.base.ui.online.BaseViewPagerLazyFragment;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.KaolaAppConfigData;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.online.common.event.OnlineLoginEvent;
import com.kaolafm.kradio.online.common.utils.AppDateUtils;
import com.kaolafm.kradio.online.common.utils.LinearGradientFontSpan;
import com.kaolafm.kradio.user.UserInfoManager;
import com.kaolafm.opensdk.api.login.LoginRequest;
import com.kaolafm.opensdk.api.login.model.CumPlaytimeInfo;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.trello.rxlifecycle3.LifecycleTransformer;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
 

/**
 * 收听时长
 * 蔡佳彬
 */
public class OnlineDurationFragment extends BaseViewPagerLazyFragment {
    private static final String TAG = OnlineDurationFragment.class.getSimpleName();
 
    TextView mine_duration_nub_tv; 
    TextView mine_duration_tv2; 
    TextView mine_duration_tv; 
    TextView mine_duration_not_login_hint; 
    LinearLayout mine_duration_ll; 
    LinearLayout mine_duration_ll2;

    public OnlineDurationFragment() {
        // Required empty public constructor
    }

    @Override
    protected boolean isReportFragment() {
        return true;
    }

    public static OnlineDurationFragment newInstance() {
        OnlineDurationFragment fragment = new OnlineDurationFragment();
        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.online_fragment_duration;
    }

    @Override
    public String getPageId() {
        return Constants.PAGE_ID_MINE_DURATION;
    }

    @Override
    protected IPresenter createPresenter() {
        return null;
    }

    UserInfoManager.IUserInfoStateListener iUserInfoStateListener = new UserInfoManager.IUserInfoStateListener() {
        @Override
        public void userLogin() {
            setCumPlaytime(UserInfoManager.getInstance().isUserLogin());
        }

        @Override
        public void userLogout() {
            setCumPlaytime(UserInfoManager.getInstance().isUserLogin());
        }

        @Override
        public void userCancel() {

        }
    };

    @Override
    public void initView(View view) {

        mine_duration_nub_tv=view.findViewById(R.id.mine_duration_nub_tv);
        mine_duration_tv2=view.findViewById(R.id.mine_duration_tv2);
        mine_duration_tv=view.findViewById(R.id.mine_duration_tv);
        mine_duration_not_login_hint=view.findViewById(R.id.mine_duration_not_login_hint);
        mine_duration_ll=view.findViewById(R.id.mine_duration_ll);
        mine_duration_ll2=view.findViewById(R.id.mine_duration_ll2);
       
    }

    @Override
    public void onResume() {
        super.onResume();
        UserInfoManager.getInstance().addUserInfoStateListener(iUserInfoStateListener);
        setCumPlaytime(UserInfoManager.getInstance().isUserLogin());
    }

    /**
     * 设置用户收听时长
     *
     * @param isLogin
     */
    private void setCumPlaytime(boolean isLogin) {
        if (isLogin) {
            new LoginRequest().getCumPlaytime(UserInfoManager.getInstance().getUserId()
                    , KaolaAppConfigData.getInstance().getAppId(), new HttpCallback<CumPlaytimeInfo>() {
                        @SuppressLint("SetTextI18n")
                        @Override
                        public void onSuccess(CumPlaytimeInfo cumPlaytimeInfo) {
                            if (cumPlaytimeInfo.getUid() != null && mine_duration_ll != null) {
                                mine_duration_ll.setVisibility(View.VISIBLE);
                                mine_duration_ll2.setVisibility(View.VISIBLE);
                                mine_duration_not_login_hint.setVisibility(View.GONE);

                                mine_duration_tv.setText(getRadiusGradientSpan(AppDateUtils.getInstance().userDurationFormat(cumPlaytimeInfo.getMonthCumulative())
                                        , mine_duration_nub_tv.getLineHeight()));

                                if (!TextUtils.isEmpty(cumPlaytimeInfo.getListenRank()))
                                    mine_duration_tv2.setText(getRadiusGradientSpan(cumPlaytimeInfo.getListenRank(), mine_duration_tv2.getLineHeight()));

                                mine_duration_nub_tv.setText(getRadiusGradientSpan(
                                        AppDateUtils.getInstance().userDurationFormat(cumPlaytimeInfo.getCumulativeDuration()) + "h"
                                        , mine_duration_nub_tv.getLineHeight()));
                            }
                        }

                        @Override
                        public void onError(ApiException e) {
                            Log.e(TAG, e.getMessage());
                        }
                    });
        } else {
            mine_duration_ll.setVisibility(View.GONE);
            mine_duration_ll2.setVisibility(View.GONE);
            mine_duration_not_login_hint.setVisibility(View.VISIBLE);
            mine_duration_nub_tv.setText(getRadiusGradientSpan("--", mine_duration_nub_tv.getLineHeight()));
        }
    }

    public SpannableStringBuilder getRadiusGradientSpan(String string, int lineHeight) {
        SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder(string);
        LinearGradientFontSpan span = new LinearGradientFontSpan(
                ResUtil.getColor(R.color.online_about_text_start_color)
                , ResUtil.getColor(R.color.online_about_text_end_color));
        spannableStringBuilder.setSpan(span, 0, spannableStringBuilder.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spannableStringBuilder;

    }

//    @Subscribe(threadMode = ThreadMode.MAIN)
//    public void onLoginEvent(OnlineLoginEvent loginEvent) {
//        setCumPlaytime(UserInfoManager.getInstance().isUserLogin());
//    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        UserInfoManager.getInstance().removeUserInfoStateListener(iUserInfoStateListener);
    }

    @Override
    protected void lazyLoad() {

    }


}