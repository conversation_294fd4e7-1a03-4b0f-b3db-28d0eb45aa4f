<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/widget_layout"
    android:layout_width="390px"
    android:layout_height="530px">
    <!--android:layout_width="390px"-->
    <!--android:layout_height="530px"-->

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginLeft="20px"
        android:layout_marginTop="18px"
        >

        <ImageView
            android:id="@+id/widge_logo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/widget_logo" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignLeft="@+id/widge_logo"
            android:layout_alignTop="@+id/widge_logo"
            android:layout_marginLeft="5px"
            android:text="吉讯电台"
            android:textColor="#A3A3A3"
            android:layout_gravity="center_vertical"
            android:textSize="24px" />

    </LinearLayout>

    <FrameLayout
        android:id="@+id/widget_cover_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="66px">

        <ImageView
            android:id="@+id/widget_radio_cover"
            android:layout_width="224px"
            android:layout_height="224px"
            android:src="@drawable/widget_default_cover" />

        <ImageView
            android:id="@+id/widget_radio_flag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/widget_live_flag"
            android:visibility="gone" />

    </FrameLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="22px"
        android:gravity="center"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/widget_left_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="40px"
            android:src="@drawable/widget_unsubscription" />

        <ImageView
            android:id="@+id/widget_play_pause"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/selector_widget_play" />

        <ImageView
            android:id="@+id/widget_right_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="40px"
            android:src="@drawable/selector_widget_next" />

    </LinearLayout>

    <TextView
        android:id="@+id/widget_audio_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/widget_cover_layout"
        android:layout_marginTop="30px"
        android:ellipsize="end"
        android:gravity="center"
        android:minLines="1"
        android:paddingLeft="28px"
        android:paddingRight="28px"
        android:singleLine="true"
        android:textColor="@color/colorWhite"
        android:textSize="30px" />

    <TextView
        android:id="@+id/widget_radio_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/widget_audio_name"
        android:layout_marginTop="7px"
        android:ellipsize="end"
        android:gravity="center"
        android:minLines="1"
        android:paddingLeft="28px"
        android:paddingRight="28px"
        android:singleLine="true"
        android:textColor="#A3A3A3"
        android:textSize="26px" />

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/widget_yikatong_bg" />

</RelativeLayout>