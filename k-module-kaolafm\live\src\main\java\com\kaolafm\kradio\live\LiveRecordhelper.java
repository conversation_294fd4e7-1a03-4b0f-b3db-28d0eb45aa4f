package com.kaolafm.kradio.live;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.util.Log;

import com.kaolafm.kradio.live.player.LiveManager;
import com.kaolafm.kradio.lib.base.AppDelegate;

/**
 * <pre>
 *     <AUTHOR> Wenchl
 *     e-mail : <EMAIL>
 *     time   : 2019/03/27
 *     desc   :
 *     version: 1.0
 * </pre>
 */
public class LiveRecordhelper {
    /**
     * 是否注册了广播消息 true为是，false为否
     */
    private boolean isRegisterBroadcast = false;

    public void registerResponseBroadcast() {
        if (!isRegisterBroadcast) {
            isRegisterBroadcast = true;
            IntentFilter intentFilter = new IntentFilter(LiveManager.START_RECORD_RESPONSSE_ACTION);
            AppDelegate.getInstance().getContext().registerReceiver(responseRecord, intentFilter);
        }
    }

    public void unRegisterResponseRecord() {
        // 解决36519问题
        if (isRegisterBroadcast) {
            AppDelegate.getInstance().getContext().unregisterReceiver(responseRecord);
            isRegisterBroadcast = false;
        }
    }

    private BroadcastReceiver responseRecord = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent.getAction() != LiveManager.START_RECORD_RESPONSSE_ACTION) {
                return;
            }
            Bundle bundle = intent.getExtras();
            boolean re = bundle.getBoolean(LiveManager.START_RECORD_RESPONSE_BOOLEAN_EXTRA);
            Log.i("LiveRecordhelper", "onreceive response  re:" + re);
            if (re) {
                LiveManager.getInstance().startRecord();
            }
        }
    };

}
