//package com.kaolafm.kradio.flavor.impl;
//
//import android.annotation.SuppressLint;
//import android.content.BroadcastReceiver;
//import android.content.Context;
//import android.content.Intent;
//import android.content.IntentFilter;
//import android.media.AudioManager;
//import android.util.Log;
//
//import com.incall.proxy.constant.SourceConstantsDef;
//import com.incall.proxy.source.SourceManager;
//import com.kaolafm.kradio.lib.base.AppDelegate;
//import com.kaolafm.kradio.lib.base.flavor.KRadioSystemSourceChangeInter;
//
//
//import static com.incall.proxy.constant.SourceConstantsDef.COAGENT_ACTION_SOURCE_CHANGED;
//
// TODO:ZC 2020-04-30  需要打开
///******************************************
// * 类描述:
// *
// * @version: V1.0
// * @author: yangshaoning
// * @time: 2019-05-23 11:41
// ******************************************/
//@SuppressLint("LongLogTag")
//public final class KRadioSystemSourceChangeImpl implements KRadioSystemSourceChangeInter, KLAudioSourceOpereateInter {
//    private final static String TAG = "KRadioSystemSourceChangeImpl";
//    public static boolean mIsInSource = false;
//    private KLAudioSourceChangeListener innerKLAudioSourceChangeListener;
//
//    public KRadioSystemSourceChangeImpl() {
//        SourceConstantsDef.SourceID sourceID = SourceManager.getInstance().getCurrentSource();
//        if (sourceID == null || !SourceConstantsDef.SourceID.MULTI_MEDIA.name().equals(sourceID.name())) {
//            SourceManager.getInstance().requestSource(SourceConstantsDef.SourceID.MULTI_MEDIA);
//        }
//    }
//
//    @Override
//    public boolean registerSourceChanged(Object... args) {
//        innerKLAudioSourceChangeListener = AudioStatusManager.getInstance().getInnerKLAudioSourceChangeListener();
//        AudioStatusManager.getInstance().injectKLAudioSourceOpereateInter("com.kaolafm.kradio.flavor.impl.KRadioSystemSourceChangeImpl");
//        SourceManager.getInstance().addChangedListener(sourceInterruptListener);
//        AppDelegate.getInstance().getContext().registerReceiver(mSourceReceiver, new IntentFilter(
//                SourceConstantsDef.COAGENT_ACTION_SOURCE_CHANGED));
//        return true;
//    }
//
//    @Override
//    public boolean unregisterSourceChanged(Object... args) {
//        try {
//            SourceManager.getInstance().removeChangedListener(sourceInterruptListener);
//            AppDelegate.getInstance().getContext().unregisterReceiver(mSourceReceiver);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return true;
//    }
//
//
//    /**
//     * 音源中断监听器，当讯飞语音或tts等叠加源激活时回调此方法，当前音源不变
//     * onResume时通过mSourceManager.addChangedListener(sourceInterruptListener)注册监听器
//     * onPause时通过mSourceManager.removeChangedListener(sourceInterruptListener)注销监听器
//     */
//    private final SourceManager.SourceChangedListener sourceInterruptListener = new SourceManager.SourceChangedListener() {
//
//
//        @Override
//        public void onCurrnetInterruptChanged(boolean isInterrupted) {
//            SourceConstantsDef.SourceID sourceId = SourceManager.getInstance().getCurrentSource();
//            Log.i(TAG, "onCurrnetInterruptChanged:  sourceId = " + sourceId + ", isInterrupted =  " + isInterrupted);
//            if (sourceId != SourceConstantsDef.SourceID.MULTI_MEDIA) {
//                //不在MULTI_MEDIA源时，发生中断不做处理
//                return;
//            }
//            if (isInterrupted) {
//                //当前为MULTI_MEDIA源，发生叠加源打断，需停止内部音频播放
//                mIsInSource = false;
//                innerKLAudioSourceChangeListener.onAudioSourceChange(AudioManager.AUDIOFOCUS_LOSS);
////                AudioStatusManager.getInstance().abandonAudioFocus();
//            } else {
//                //当前为MULTI_MEDIA源，叠加源处理完毕释放时，可恢复内部音频播放
//                mIsInSource = true;
//                innerKLAudioSourceChangeListener.onAudioSourceChange(AudioManager.AUDIOFOCUS_GAIN);
////                AudioStatusManager.getInstance().requestAudioFocus();
//            }
//        }
//    };
//
//    /**
//     * 音源切换广播接收器，当音源切换时会发出此广播，需要使用音频场景的各应用监听广播SourceConstantsDef.COAGENT_ACTION_SOURCE_CHANGED，
//     * 进入监听源时可进行相关播放逻辑，进入其他源时需停止当前播放逻辑
//     */
//    private final BroadcastReceiver mSourceReceiver = new BroadcastReceiver() {
//        @Override
//        public void onReceive(Context context, Intent intent) {
//            String action = intent.getAction();
//            if (COAGENT_ACTION_SOURCE_CHANGED.equals(action)) {
//                String sourceFrom = intent.getStringExtra(SourceConstantsDef.EXTRA_SOURCE_FROM);
//                String sourceTo = intent.getStringExtra(SourceConstantsDef.EXTRA_SOURCE_TO);
//                Log.i(TAG, "SOURCE Switch FROM: " + sourceFrom + ", TO: " + sourceTo);
//                if (SourceConstantsDef.SourceID.valueOf(sourceFrom) == SourceConstantsDef.SourceID.MULTI_MEDIA) {
//                    //pause all audio
//                    mIsInSource = false;
//                    innerKLAudioSourceChangeListener.onAudioSourceChange(AudioManager.AUDIOFOCUS_LOSS);
//                } else if (SourceConstantsDef.SourceID.valueOf(sourceTo) == SourceConstantsDef.SourceID.MULTI_MEDIA) {
//                    //resume audio
//                    mIsInSource = true;
//                    innerKLAudioSourceChangeListener.onAudioSourceChange(AudioManager.AUDIOFOCUS_GAIN);
//                }
//            }
//
//        }
//    };
//
//
//    /**
//     * 申请音频焦点-好帮手, 需要播放音频消息前调用此接口申请音源
//     * 申请后需等待回调，申请成功方可播放音频
//     *
//     * @return
//     */
//    private static void requestAudioFocus() {
//        Log.i(TAG, "requestAudioFocus: SourceID.MULTI_MEDIA");
//        SourceManager.getInstance().requestSource(SourceConstantsDef.SourceID.MULTI_MEDIA);
//    }
//
//    /**
//     * 释放音频焦点-好帮手，音频消息播放结束后调用此接口释放源
//     *
//     * @return
//     */
//    private static void abandonAudioFocus() {
//        //mSourceManager.releaseSource(SourceID.CAR_CHAT);//拉起下一个音源界面时调用
//        Log.i(TAG, "abandonAudioFocus: SourceID.MULTI_MEDIA");
//        SourceManager.getInstance().releaseSource(SourceConstantsDef.SourceID.MULTI_MEDIA, false);//保持当前界面，不拉起下一个音源界面时调用
//    }
//
//    @Override
//    public boolean isRetentionFocusChange(Object... objects) {
//        return false;
//    }
//
//    @Override
//    public boolean requestAudioSource(Object... objects) {
//        requestAudioFocus();
//        return true;
//    }
//
//    @Override
//    public boolean abandonAudioSource(Object... objects) {
//        abandonAudioFocus();
//        return true;
//    }
//
//}
