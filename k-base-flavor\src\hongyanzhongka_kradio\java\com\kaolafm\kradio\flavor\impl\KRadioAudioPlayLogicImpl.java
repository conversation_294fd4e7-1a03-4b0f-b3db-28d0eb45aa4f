package com.kaolafm.kradio.flavor.impl;

import android.annotation.SuppressLint;
import android.util.Log;


import com.kaolafm.kradio.lib.base.flavor.KRadioAudioPlayLogicInter;

/**
 * <pre>
 *     <AUTHOR> Wenchl
 *     e-mail : <EMAIL>
 *     time   : 2019/07/28
 *     desc   :
 *     version: 1.0
 * </pre>
 */

//https://app.huoban.com/tables/2100000007530121/items/2300001612816703?userId=1874548
public class KRadioAudioPlayLogicImpl implements KRadioAudioPlayLogicInter {
    private static final String TAG = "KRadioAudioPlayLogicImpl";

    @SuppressLint("LongLogTag")
    @Override
    public boolean autoPlayAudio(Object... args) {
        return false;
    }

    @Override
    public boolean requestAudioFocus(Object... args) {
        boolean flag = PlayerManager.getInstance().requestAudioFocus();
        return flag;
    }

    @Override
    public boolean resumeAudioPlayLogic(Object... args) {
        recoverPlay();
        return true;
    }

    @Override
    public boolean restartAudioPlayLogic(Object... args) {
        return false;
    }

    @SuppressLint("LongLogTag")
    private void recoverPlay() {
        KLAutoPlayerManager klAutoPlayerManager = KLAutoPlayerManager.getInstance();
        AudioStatusManager audioStatusManager = AudioStatusManager.getInstance();
        Log.i(TAG, "recoverPlay---------->audioStatusManager.isPausedFromUser() = " + audioStatusManager.isPausedFromUser()
                + "          audioStatusManager.getCurrentFocusChange() = " + audioStatusManager.getCurrentFocusChange());
        if (audioStatusManager.isPausedFromUser()) {
            if (audioStatusManager.getCurrentFocusChange() < 0) {
                requestAudioFocus();
            }
            return;
        }
        if (audioStatusManager.getCurrentFocusChange() >= 0) {
            return;
        }
        requestAudioFocus();
        if (!klAutoPlayerManager.isPlaying()) {
            klAutoPlayerManager.switchPlayerStatus(false);
        }
    }

    @Override
    public boolean doStartInPlay(Object... args) {
        return false;
    }
}

