package com.kaolafm.kradio.flavor.impl;

import android.annotation.SuppressLint;
import android.util.Log;

import com.kaolafm.kradio.lib.base.flavor.KRadioActivityLifecyleInter;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-04-02 10:41
 ******************************************/
public class KRadioActivityLifecyleImpl implements KRadioActivityLifecyleInter {
    @Override
    public boolean onRestart(Object... args) {
        return false;
    }

    @Override
    public boolean onResume(Object... args) {
        return false;
    }

    @Override
    public boolean onPause(Object... args) {
        return false;
    }

    @SuppressLint("LongLogTag")
    @Override
    public boolean onStop(Object... args) {
        Log.i("KRadioActivityLifecyleImpl", "onStop start");
        PlayerManagerHelper.getInstance().pause(true);
        return true;
    }
}
