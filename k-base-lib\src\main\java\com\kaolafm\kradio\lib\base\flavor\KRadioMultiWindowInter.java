package com.kaolafm.kradio.lib.base.flavor;

import androidx.constraintlayout.widget.ConstraintLayout;

/**
 * <pre>
 *     <AUTHOR> Wenchl
 *     e-mail : <EMAIL>
 *     time   : 2019/09/17
 *     desc   : 基于121 各个页面功能建立的接口，各个页面功能可自行添加接口实现，比如首页的导航栏，横竖屏的展示不一样，如果进入分屏可以自行指定一种固定的UI（始终是竖屏UI）
 *
 *     version: 1.0
 * </pre>
 */
public interface KRadioMultiWindowInter {

    boolean setSearchGuidelinePercent(Object... objects);

    boolean setAccountGridLayoutManager(Object... objects);

    boolean setSettingGridLayoutManager(Object... objects);

    boolean setHistoryGridLayoutManager(Object... objects);

    boolean setSubscriptionGridLayoutManager(Object... objects);

    boolean setPurchasedGridLayoutManager(Object... objects);

    void doFunctionsEndMargin(Object... objects);

    void doFunctionsWidth(Object... objects);

    boolean getMultiStatus(Object... objects);

    /**
     * 分屏适配搜索界面搜索框
     * @param objects
     */
    void doMultiSearchFragment(Object... objects);

    /**
     * 分屏适配播放器界面播放列表
     */
    void doMultiPlayerFragmentRadioPlayList(Object... objects);

    /**
     * 分屏适配播放器界面播放按钮布局
     */
    void doMultiPlayerFragmentRadioControllerBar(Object... objects);

    /**
     * 分屏适配播放器界面View的显示和隐藏
     */
    void doMultiPlayerFragmentViewShow(Object... objects);

    /**
     * 分屏适配播放器界面title下灰线的位置
     */
    void doMultiPlayerFragmentTitleLine(Object... objects);


    /**
     * 分屏适配广播播放器列表界面
     */
    void doMultiBroadcastPlayerFragmentList(Object... objects);

    /**
     * 分屏适配广播播放器列表界面,item属性修改
     */
    void doMultiBroadcastPlayerFragmentListItem(Object... objects);

    /**
     * 分屏适配广播播放器列表界面,播放器栏
     */
    void doMultiBroadcastPlayerFragmentPlayIcon(Object... objects);

    void doMultiPlayListView(ConstraintLayout.LayoutParams layoutParams);

    void doMutiSearchView(Object... typeSpinner);

}
