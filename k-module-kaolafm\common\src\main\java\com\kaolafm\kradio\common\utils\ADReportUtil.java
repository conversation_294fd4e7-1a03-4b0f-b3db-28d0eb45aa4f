package com.kaolafm.kradio.common.utils;

import android.util.Log;

import com.kaolafm.ad.api.model.Advert;
import com.kaolafm.ad.expose.AdvertisingManager;

public class ADReportUtil {

    /**
     * app内部推送导致广告关闭的skip事件处理
     */
    public static void reportSkipForAppInternallyConflict(boolean isPlayingAd,Advert advert){
        Log.i("ADUtils","reportSkipForAppInternallyConflict ,playing:"+isPlayingAd);
        if (isPlayingAd) {
            AdvertisingManager.getInstance().getReporter().skip(advert);
        } else {
            AdvertisingManager.getInstance().getImager().skip(null);
        }
    }

}
