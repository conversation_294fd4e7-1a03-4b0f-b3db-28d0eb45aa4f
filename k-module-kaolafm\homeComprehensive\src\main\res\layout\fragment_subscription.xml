<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/sub_main_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorTransparent"
    android:paddingStart="@dimen/default_edge_start"
    android:paddingEnd="@dimen/default_edge_end">

    <TextView
        android:id="@+id/tv_subscription_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/m15"
        android:textColor="@color/text_count_color"
        android:textSize="@dimen/text_size3"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout
        android:id="@+id/refreshLayout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_subscription_count">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_sub"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never"
            tools:background="#a0a0"
            android:paddingTop="@dimen/y15"
            android:paddingBottom="@dimen/y15" />
    </com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout>

    <ViewStub
        android:id="@+id/vs_layout_error_page"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout="@layout/home_no_network_rl" />

    <include
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        layout="@layout/comprehensive_no_content_layout" />


    <include
        android:id="@+id/subscription_loading"
        layout="@layout/refresh_center"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintVertical_bias="0.4"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 隐藏控件，用于所见即可说语音执行滑动操作 -->
    <TextView
        style="@style/ContentDescriptionScrollUp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/refreshLayout" />

    <TextView
        style="@style/ContentDescriptionScrollDown"
        app:layout_constraintBottom_toBottomOf="@id/refreshLayout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>