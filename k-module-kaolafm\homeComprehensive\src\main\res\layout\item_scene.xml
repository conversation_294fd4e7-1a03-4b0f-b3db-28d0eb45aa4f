<?xml version="1.0" encoding="utf-8"?>
<com.kaolafm.kradio.lib.widget.square.SquareHeightFrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:siv="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="match_parent">

    <com.kaolafm.kradio.lib.widget.square.SquareImageView
        android:id="@+id/scene_iv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:contentDescription="@null"
        siv:canScale="false" />

    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/color_black_60_transparent" />

    <TextView
        android:id="@+id/scene_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:maxLines="1"
        android:singleLine="true"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size3" />
</com.kaolafm.kradio.lib.widget.square.SquareHeightFrameLayout>