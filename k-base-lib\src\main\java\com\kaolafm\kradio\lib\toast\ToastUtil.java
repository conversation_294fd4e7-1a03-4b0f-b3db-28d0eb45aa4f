package com.kaolafm.kradio.lib.toast;

import android.content.Context;
import androidx.annotation.StringRes;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.base.flavor.KRadioToastInter;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.IntentUtils;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.opensdk.log.Logging;

/**
 * toast工具类。
 *
 * <AUTHOR>
 * @date 2018/4/10
 */

public class ToastUtil {
    private static final String TAG = "ToastUtil";

    private static SuperToast mToast;
    private static SuperToast mNotify;

    /**
     * 普通toast，就是默认样式
     */
    public static void showNormal(Context context, String msg) {
        showOnly(context,msg);
//        boolean isAppOnForeground = IntentUtils.getInstance().isAppOnForeground();
//        if (isAppOnForeground) {
//            if (context == null) {
//                context = AppDelegate.getInstance().getContext();
//            }
//            mToast = new SuperToast(context)
//                    .setMessage(msg)
//                    .create()
//                    .show();
//        }
    }

    /**
     * 普通toast，就是默认样式
     */
    public static void showNormal(Context context, @StringRes int resId) {
        showNormal(context, ResUtil.getString(resId));
    }


    /**
     * 只显示当前的toast，取消所有正在显示或等待显示的toast
     */
    public static void showOnly(Context context, String msg) {
        boolean isAppOnForeground = IntentUtils.getInstance().isAppOnForeground();
        Logging.i(TAG, "showOnly msg = " + msg + " isAppOnForeground = " + isAppOnForeground);
        if (isAppOnForeground) {
            if (context == null) {
                context = AppDelegate.getInstance().getContext();
            }
            int displayLevel = ToastStyle.LEVEL_SYSTEM;
            KRadioToastInter kRadioToastInter = ClazzImplUtil.getInter("KRadioToastImpl");
            if (kRadioToastInter != null) {
                displayLevel = kRadioToastInter.displayLevel();
            }
            Log.d(TAG, "showOnly mToast=" + mToast);
            //二维码一直展示问题
            if (null != mToast) {
                dismiss();
            }
            mToast = new SuperToast(context);
//            mToast.cancelAllToasts();
            mToast.setMessage(msg)
                    .setDuration(ToastStyle.DURATION_LONG)
                    .setDisplayLevel(displayLevel)
                    .create()
                    .show();
        }
    }

    public static void showOnly(Context context, @StringRes int resId) {
        showOnly(context, ResUtil.getString(resId));
    }

    /**
     * 在当前页面显示toast。显示前会取消所有正在显示或等待显示的toast
     *
     * @param context 必须是activity
     */
    public static void showOnActivity(Context context, String msg) {
        showOnly(context.getApplicationContext(), msg);

//        boolean isAppOnForeground = IntentUtils.getInstance().isAppOnForeground();
//        if (isAppOnForeground) {
//            if (context == null) {
//                return;
//            }
//            mToast = new SuperToast(context);
//            mToast.cancelAllToasts();
//            mToast.setMessage(msg)
//                    .setDuration(ToastStyle.DURATION_LONG)
//                    .setDisplayLevel(ToastStyle.LEVEL_ACTIVITY)
//                    .create().show();
//        }
    }

    /**
     * 在当前页面显示toast。显示前会取消所有正在显示或等待显示的toast
     *
     * @param context 必须是activity
     */
    public static void showOnActivity(Context context, int resId) {
        showOnly(context, ResUtil.getString(resId));
    }


    public static void showInfo(Context context, String msg) {
        //目前所有的toast的显示都一样
        showOnly(context, msg);
    }

    public static void showInfo(Context context, int resId) {
        //目前所有的toast的显示都一样
        showOnly(context, ResUtil.getString(resId));
    }

    public static void showError(Context context, String msg) {
        //目前所有的toast的显示都一样
        showOnly(context, msg);
    }

    public static void showError(Context context, int resId) {
        //目前所有的toast的显示都一样
        showOnly(context, ResUtil.getString(resId));
    }

    /**
     * 持久显示。只在当前activity里显示。退出页面就会消失
     */
    public static void showIndefinite(Context context, String msg) {
        boolean isAppOnForeground = IntentUtils.getInstance().isAppOnForeground();
        if (isAppOnForeground) {
            if (context == null) {
                return;
            }
            if (mToast != null) {
                mToast.dismiss();
            }
            Logging.Log.d(TAG, "showIndefinite mToast");
            mToast = new SuperToast(context)
                    .setMessage(msg)
                    .setDisplayLevel(ToastStyle.LEVEL_ACTIVITY)
                    .setRadius(80)
                    .setDuration(ToastStyle.DURATION_INDEFINITE)
                    .create()
                    .show();
        }
    }

    /**
     * 持久显示。只在当前activity里显示。退出页面就会消失
     */
    public static void showIndefinite(Context context, int resId) {
        boolean isAppOnForeground = IntentUtils.getInstance().isAppOnForeground();
        if (isAppOnForeground) {
            if (context == null) {
                return;
            }
            if (mToast != null) {
                mToast.dismiss();
            }
            Logging.Log.d(TAG, "showIndefinite mToast");
            mToast = new SuperToast(context)
                    .setMessage(resId)
                    .setDisplayLevel(ToastStyle.LEVEL_ACTIVITY)
                    .setRadius(80)
                    .setDuration(ToastStyle.DURATION_INDEFINITE)
                    .create()
                    .show();
        }
    }

    public static void dismiss() {
        if (mToast != null) {
            Logging.Log.d(TAG, "dismiss mToast");
            // 解决https://app.huoban.com/tables/2100000007530121/items/2300001315871138?userId=1229522问题
            mToast.cancelAllToasts();
            mToast.dismiss();
            mToast = null;
        }
    }

    public static void dismissNotify() {
        if (mNotify != null) {
            mNotify.dismiss();
            mNotify = null;
        }
    }

    /**
     * 持久显示。只在当前activity里显示。退出页面就会消失
     */
    public static void showScene(Context context, View view) {
//        if (context == null) {
//            context = AppManager.getInstance().getCurrentActivity();
//        }
        context = AppManager.getInstance().getMainActivity();
        dismissNotify();
        if (context != null) {
            mNotify = new NotifyToast(context)
                    .setCanTouch(true)
                    .setView(view)
                    //横竖屏都是这个长度,已挣得于继祥同意
                    .setWidth(ScreenUtil.getGlobalNotifyWindowWidth(ResUtil.getOrientation()))
                    .setDisplayLevel(ToastStyle.LEVEL_ACTIVITY)
                    .setGravity(Gravity.TOP)
                    .setXOffset(0)
                    .setYOffset(0)
                    .setRadius(80)
                    .setDuration(-1)
                    .setHeight(ViewGroup.LayoutParams.WRAP_CONTENT)
                    .create()
                    .show();
        }

    }

    /**
     * 持久显示。只在当前activity里显示。退出页面就会消失
     */
    public static void showLive(Context context, View view) {
//        if (context == null) {
//            context = AppManager.getInstance().getMainActivity();
//        }
        context = AppManager.getInstance().getMainActivity();
        dismissNotify();
        if (context != null) {
            mNotify = new NotifyToast(context)
                    .setCanTouch(true)
                    .setView(view)
                    .setWidth(ScreenUtil.getGlobalNotifyWindowWidth(ResUtil.getOrientation()))
                    .setDisplayLevel(ToastStyle.LEVEL_ACTIVITY)
                    .setGravity(Gravity.TOP)
                    .setXOffset(0)
                    .setYOffset(0)
                    .setRadius(80)
                    .setDuration(-1)
                    .setHeight(ViewGroup.LayoutParams.WRAP_CONTENT)
                    .create()
                    .show();
        }
    }

    /**
     * 释放toast
     */
    public static void release() {
        mToast = null;
        mNotify = null;
    }
}
