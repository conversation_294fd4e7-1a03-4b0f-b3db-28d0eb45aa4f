package com.ecarx.sdk.step;

import android.text.TextUtils;
import android.util.Log;

import com.ecarx.sdk.ECarX;
import com.kaolafm.opensdk.api.login.LoginRequest;
import com.kaolafm.opensdk.api.login.model.UserInfo;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

/**
 * <AUTHOR>
 **/
public class LoginStep extends Step {

    private final ECarX ecarx;

    public LoginStep(ECarX eCarX) {
        this.ecarx = eCarX;
    }

    @Override
    public void exe() {
        Log.i(ECarX.TAG, "exe:"+getClass().getSimpleName());
        String openUid = ecarx.getOpenUid();
        if (TextUtils.isEmpty(openUid)) {
            ecarx.updateStep();
            ecarx.nextStep();
        } else {
            new LoginRequest().getUserInfo(new HttpCallback<UserInfo>() {
                @Override
                public void onSuccess(UserInfo userInfo) {
                    ecarx.updateUserInfoManager(userInfo);
                    ecarx.updateStep();
                    ecarx.nextStep();
                }

                @Override
                public void onError(ApiException e) {
                    Log.i(ECarX.TAG, "   onError: error=" + e);
                    ecarx.error(e);
                }
            });
        }
    }


}
