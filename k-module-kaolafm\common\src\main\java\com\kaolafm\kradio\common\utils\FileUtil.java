package com.kaolafm.kradio.common.utils;

import android.content.Context;
import android.content.res.Resources;
import android.util.Log;

import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class FileUtil {
    // CPU优化：使用BufferedInputStream提升读取性能
    public static String readRawFile(Context ctx, int fileRaw){
        Resources resources = ctx.getResources();
        InputStream inputStream = null;
        BufferedInputStream bufferedInputStream = null;
        String result = "";
        try {
            inputStream = resources.openRawResource(fileRaw);
            // CPU优化：使用BufferedInputStream减少系统调用
            bufferedInputStream = new BufferedInputStream(inputStream, 8192);

            // CPU优化：使用ByteArrayOutputStream避免预分配大数组
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = bufferedInputStream.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            result = new String(baos.toByteArray(), "utf-8");
        } catch (IOException e) {
            Log.e("ResourceFileDemo", e.getMessage(), e);
        } finally {
            if (bufferedInputStream != null) {
                try {
                    bufferedInputStream.close();
                } catch (IOException e) { }
            }
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) { }
            }
        }
        return result;
    }
    private static final char HEX_DIGITS[] = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
            'A', 'B', 'C', 'D', 'E', 'F' };
    public static String toHexString(byte[] b) {
        //String to byte
        StringBuilder sb = new StringBuilder(b.length * 2);
        for (int i = 0; i < b.length; i++) {
            sb.append(HEX_DIGITS[(b[i] & 0xf0) >>> 4]);
            sb.append(HEX_DIGITS[b[i] & 0x0f]);
        }
        return sb.toString();
    }
    public static String md5(String s) {
        try {
            // Create MD5 Hash
            MessageDigest digest = java.security.MessageDigest.getInstance("MD5");
            digest.update(s.getBytes());
            byte messageDigest[] = digest.digest();
            return toHexString(messageDigest);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return "";
    }

}
