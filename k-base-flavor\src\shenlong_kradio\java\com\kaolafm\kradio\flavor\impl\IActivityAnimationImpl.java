package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;

//import com.kaolafm.kradio.lib.base.flavor.IActivityAnimationInter;
//
///**
// * @Description:
// * @Author: Maclay
// * @Date: 2021/4/1 14:44
// */
//public class IActivityAnimationImpl implements IActivityAnimationInter {
//    @Override
//    public void jumpLauncher(Activity activity) {
//        //每次从主界面在线进入听伴时，屏幕会从黑色界面再跳入听伴。同样的方法进入酷狗音乐时正常
//        activity.overridePendingTransition(0,0);
//    }
//}