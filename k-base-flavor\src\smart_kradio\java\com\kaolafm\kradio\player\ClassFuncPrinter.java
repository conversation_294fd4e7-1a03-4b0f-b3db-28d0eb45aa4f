package com.kaolafm.kradio.player;

import android.util.Log;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;

/**
 * 用于切换sdk的http和https
 * <AUTHOR>
 * @date 2019-06-18
 */
@Aspect
public class ClassFuncPrinter {
    final String TAG = ClassFuncPrinter.class.getSimpleName();
    private boolean useHttp = false;

    //    @Before("execution(* com.kaolafm.kradio.lib.widget.tab.SlidingTabLayout+.**(..))")
//    public void printClassAllFunc(JoinPoint joinPoint) throws Throwable{
//        printClassInfo(joinPoint);
//    }
//
//    @Before("execution(* com.kaolafm.kradio.subscribe.SubscriptionFragment+.**(..))")
//    public void printClassAllFuncInner(JoinPoint joinPoint) throws Throwable{
//        printClassInfo(joinPoint);
//    }
//
//    @Before("execution(* com.kaolafm.kradio.subscribe.SubscriptionFragment.SubscriptionAdapter+.**(..))")
//    public void printClassAllFuncInner2(JoinPoint joinPoint) throws Throwable{
//        printClassInfo(joinPoint);
//    }
//
//    @Before("execution(* com.kaolafm.kradio.lib.base.ui.BaseLazyFragment+.**(..))")
//    public void printClassAllFuncInner3(JoinPoint joinPoint) throws Throwable{
//        printClassInfo(joinPoint);
//    }

    private void printClassInfo(JoinPoint joinPoint){
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        String className = "nullClass";
        int classHash = 0;
        if(joinPoint.getThis()!=null){
            className = joinPoint.getThis().getClass().getSimpleName();
            classHash = joinPoint.getThis().hashCode();
            Log.i(TAG, className +":"+ methodSignature.getName());
            //打印调用栈，注掉可以让信息简洁
//            printCallStatck();
        }
    }

    public void printCallStatck() {
        Throwable ex = new Throwable();
        StackTraceElement[] stackElements = ex.getStackTrace();
        if (stackElements != null) {
            for (int i = 0; i < stackElements.length; i++) {
                Log.i(TAG,stackElements[i].toString());
            }
        }
    }
}
