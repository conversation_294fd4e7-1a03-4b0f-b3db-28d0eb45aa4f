package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.util.Log;

import com.kaolafm.kradio.flavor.common.SystemBootUtil;

import com.kaolafm.kradio.flavor.utils.PlayerUtil;
import com.kaolafm.kradio.lib.base.flavor.KRadioClientPlayerInter;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-03-02 17:47
 ******************************************/

public class KRadioClientPlayerImpl implements KRadioClientPlayerInter {
    private static final String TAG = "KRadioClientPlayerImpl";

    @Override
    public boolean doIgnorePlayAction(Object... args) {
        Context context = (Context) args[0];
        SystemBootUtil systemBootUtil = new SystemBootUtil();
        boolean flag = systemBootUtil.isFirstBoot(context);
        Log.i(TAG, "autoPlayAudio:   flag = " + flag);
        if (flag) {
            PlayerUtil.playDefaultMediaForChannel();
            systemBootUtil.updateFirstBoot(context, false);
            return true;
        }
        return false;
    }
}
