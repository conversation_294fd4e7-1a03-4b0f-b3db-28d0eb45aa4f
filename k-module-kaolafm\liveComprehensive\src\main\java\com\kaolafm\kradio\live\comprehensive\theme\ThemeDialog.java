package com.kaolafm.kradio.live.comprehensive.theme;

import android.content.DialogInterface;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import android.text.method.ScrollingMovementMethod;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.kaolafm.kradio.common.utils.CommonUtils;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.DialogExposureEvent;
import com.kaolafm.report.util.ReportConstants;

/**
 * 直播主题弹窗
 *
 * <AUTHOR>
 * @date 2023-02-17
 */
public class ThemeDialog extends DialogFragment {

    private String content;
    private static final String PAGE_ID = "PAGE_ID";
    private String pageId;

    public static ThemeDialog getInstance(String pageId) {
        ThemeDialog giftPannelDialog = new ThemeDialog();
        Bundle bundle = new Bundle();
        bundle.putString(PAGE_ID, pageId);
        giftPannelDialog.setArguments(bundle);
        return giftPannelDialog;
    }

    @Override
    public void onStart() {
        super.onStart();

        Window win = getDialog().getWindow();
        CommonUtils.getInstance().initGreyStyle(win);
        //设置背景半透明
//        DisplayMetrics dm = new DisplayMetrics();
//        getActivity().getWindowManager().getDefaultDisplay().getMetrics(dm);
//        getDialog().getWindow().setLayout(dm.widthPixels, getDialog().getWindow().getAttributes().height);
        win.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        //getDialog().setCancelable(false);//这个会屏蔽掉返回键
//        getDialog().setCanceledOnTouchOutside(isCanceledOnTouchOutside());
        WindowManager.LayoutParams params = win.getAttributes();
        params.gravity = Gravity.TOP;
        params.y = ResUtil.getDimen(R.dimen.y140);
        // 使用ViewGroup.LayoutParams，以便Dialog 宽度充满整个屏幕
        params.width = ViewGroup.LayoutParams.WRAP_CONTENT;
        params.height = ViewGroup.LayoutParams.WRAP_CONTENT;
        win.setAttributes(params);
    }


    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        this.pageId = getArguments().getString(PAGE_ID, "");
        View view = inflater.inflate(R.layout.comprehensive_theme_dialog, container, false);
        TextView themeContent = view.findViewById(R.id.live_theme_content);
        themeContent.setMovementMethod(ScrollingMovementMethod.getInstance());
        view.findViewById(R.id.cd_close).setOnClickListener((v) -> dismiss());
        themeContent.setText(content);
        return view;
    }


    public ThemeDialog setContent(String content) {
        this.content = content;
        return this;
    }

    protected long startTime = -1;

    @Override
    public void onResume() {
        super.onResume();
        startTime = System.currentTimeMillis();
    }

    @Override
    public void onDismiss(DialogInterface dialog) {
        super.onDismiss(dialog);
        reportPageShowEvent();
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser) startTime = System.currentTimeMillis();
        else reportPageShowEvent();
    }

    /**
     * 页面曝光事件上报
     */
    private void reportPageShowEvent() {
        long duration = System.currentTimeMillis() - startTime;
        if (startTime < 0 || duration < 300) {
            return;
        }
        ReportHelper.getInstance().addEvent(new DialogExposureEvent(ReportConstants.DIALOG_ID_LIVE_ROOM_NOTICE, pageId, duration, null));
        Log.i("BaseDialogFragment", "report=" + duration);
        startTime = -1;
    }
}
