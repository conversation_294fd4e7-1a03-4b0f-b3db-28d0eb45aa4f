package com.kaolafm.kradio.flavor.carnetwork.carnetworkutils;

import android.app.Activity;
import android.content.Context;
import androidx.annotation.UiThread;
import android.util.Log;

import com.kaolafm.kradio.flavor.carnetwork.dialog.OuShangDialog;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.utils.SharedPreferenceUtil;

import org.aspectj.lang.ProceedingJoinPoint;

import java.text.SimpleDateFormat;
import java.util.Date;

public class ChanganAuthManager {

    private static final String AUTH_DATE_STATUS = "authDateStatus";
    private static final String SP_KEY_TIME_INFO = "timeInfo";
    private static final String SP_KEY_STATUS_INFO = "statusInfo";

    private boolean auth_through = false;//鉴权状态是否通过
    private SharedPreferenceUtil mSPUtil;
    private OuShangDialog ouShangDialog;
    private Activity preActivity;
    private Activity curActivity;

    private static class InstanceHolder {
        private final static ChanganAuthManager mChanganDialogFactory = new ChanganAuthManager();
    }

    public static ChanganAuthManager getInstance() {
        return InstanceHolder.mChanganDialogFactory;
    }

    public ChanganAuthManager initDialog() {
        curActivity = AppManager.getInstance().getCurrentActivity();
        if (preActivity != curActivity && curActivity != null) {
            preActivity = curActivity;
            if (curActivity != null) ouShangDialog = new OuShangDialog((Context) curActivity);
        } else {
            if (ouShangDialog == null && curActivity != null) {
                ouShangDialog = new OuShangDialog((Context) curActivity);
            }
        }

        return InstanceHolder.mChanganDialogFactory;
    }

    @UiThread
    public void runUIshow(Integer code, ProceedingJoinPoint mProceedingJoinPoint) {
        if (ouShangDialog != null) {
            ouShangDialog.setCode(code);
            ouShangDialog.setProceedingJoinPoint(mProceedingJoinPoint);
            ouShangDialog.show();
        }
    }

    @UiThread
    public void runUIdismiss() {
        if (ouShangDialog != null) {
            ouShangDialog.dismiss();
        }
    }

    public boolean isAuth_through() {
        return auth_through;
    }

    /**
     * 根据日期和状态判断是否需要去鉴权
     *
     * @return true 继续响应鉴权  false 不做鉴权
     */
    public boolean checkAuthStatusAndDate() {
        if (mSPUtil == null) {
            mSPUtil = SharedPreferenceUtil.newInstance(AppDelegate.getInstance().getContext(), AUTH_DATE_STATUS, Context.MODE_PRIVATE);
        }
        String time = mSPUtil.getString(SP_KEY_TIME_INFO, "0");
        boolean status = auth_through = mSPUtil.getBoolean(SP_KEY_STATUS_INFO, false);
        Log.i("zsj", "checkAuthStatusAndDate: isSameDate(time) = " + isSameDate(time) + ",status = " + status + ",time = " + time);
        if (status == false || !isSameDate(time)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 对比是否是同一天
     *
     * @param time String类型-当前的时间戳
     * @return
     */
    public boolean isSameDate(String time) {
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
            long longTime = new Long(time);
            Date date = new Date(longTime);
//        String res = simpleDateFormat.format(date);
            //当前时间
            Date now = new Date();
            SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
            //获取今天的日期
            String nowDay = sf.format(now);
            //对比的时间
            String day = sf.format(date);
            return day.equals(nowDay);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 保存时间和鉴权状态
     *
     * @param time
     * @param status
     */
    public void saveAuthAndDate(String time, boolean status) {
        if (mSPUtil == null) {
            mSPUtil = SharedPreferenceUtil.newInstance(AppDelegate.getInstance().getContext(), AUTH_DATE_STATUS, Context.MODE_PRIVATE);
        }
        mSPUtil.putString(SP_KEY_TIME_INFO, time);
        mSPUtil.putBoolean(SP_KEY_STATUS_INFO, status);
        auth_through = status;
        Log.i("zsj", "saveAuthAndDate: time = " + time + ",status = " + status);
    }

}
