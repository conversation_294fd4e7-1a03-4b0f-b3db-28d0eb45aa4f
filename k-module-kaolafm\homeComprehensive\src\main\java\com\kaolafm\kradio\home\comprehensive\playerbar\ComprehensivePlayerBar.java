package com.kaolafm.kradio.home.comprehensive.playerbar;

import static com.kaolafm.kradio.lib.utils.date.TimeUtil.getAIAudioUpdateTime;

import android.app.Activity;
import android.content.Context;
import android.content.res.Configuration;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.os.Looper;
import android.text.SpannableStringBuilder;
import android.text.SpannedString;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.SeekBar;
import android.widget.TextView;

import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleObserver;
import androidx.lifecycle.OnLifecycleEvent;

import com.kaolafm.ad.api.model.Advert;
import com.kaolafm.ad.api.model.ImageAdvert;
import com.kaolafm.ad.comprehensive.ads.image.AdvertisingImagerImpl;
import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.kradio.IndicatorV212SeekBar;
import com.kaolafm.kradio.common.event.BroadcastPlayerChangedData;
import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.kradio.component.ComponentClient;
import com.kaolafm.kradio.constant.LiveComponentConst;
import com.kaolafm.kradio.home.comprehensive.gallery.PageJumper;
import com.kaolafm.kradio.home.data.Category;
import com.kaolafm.kradio.k_kaolafm.BuildConfig;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioNavBarInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioToastInter;
import com.kaolafm.kradio.lib.bean.BroadcastRadioSimpleData;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.OritationViewUtils;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.utils.date.TimeUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.player.comprehensive.play.view.AIRadioMinusFeedbackView;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.subscribe.SubscribeModel;
import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.AlbumPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.BroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.RadioPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.TVPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.TempTaskPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.util.ReportConstants;
import com.kaolafm.report.util.ReportParameterManager;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.HashMap;
import java.util.Map;

import fr.castorflex.android.circularprogressbar.CircularProgressBar;
import skin.support.widget.SkinCompatRelativeLayout;


/**
 * Created by v on 2018/4/12.
 */

public class ComprehensivePlayerBar extends SkinCompatRelativeLayout implements ComprehensivePlayerbarContract.IPlayerView {
    private static final String TAG = "PlayerBar";
    /**
     * 电台标签最长
     */
    private static final int MAX_SUB_TAG_LEN = 8;
    private static final String SUB_TAG_REPLACE_STRING = "...";

    private View infoPlayer;

    private ImageView playerBarCover;

    private IndicatorV212SeekBar playerBarProgress;
    private TextView mProgressBarThumbView;

    private TextView playerBarTitle;
    private TextView playerBarTagText;

//    private TextView playerBarSubTagText;
//
//    private TextView playerBarTimeText;
//
//    private ImageView playerBarVerticalLine;
//
//    private TextView playerBarSubTitleText;

    private CircularProgressBar playerBarLoading;

    private ImageView playerBarPlay;

    private ImageView playerBarCollect;

    private ImageView playerVideoMinimum;

    private ImageView playerBarNext;

    private ImageView playerBarPrevious;

//    private ImageView playerBarLiveClose;

//    private ImageView playerBarLivePlayPause;

//    private View playerBarPlayRl;

//    private LinearLayout playerBarLivePlayRl;

    private View playerBarConstrantlayoutRl;

    private ComprehensivePlayerBarPresenter mPresenter;

    private AIRadioMinusFeedbackView mAiRadioMinusFeedbackView;

    private boolean isLiveState;
    private Context mContext;
    private ImageView playerBarHome;

//    private View playerBarActivity;

    private View messageBadgeView;

//    private TextView player_bar_activty_tv;

    private TextView mPlayForward, mPlayBackward, mReplay;

    private boolean isSeekByUser = false;

    private Handler mHandler = new Handler(Looper.getMainLooper());
    public ComprehensivePlayerBar(Context context) {
        super(context);
        init(context);
    }

    public ComprehensivePlayerBar(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public ComprehensivePlayerBar(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private int getLayoutId() {
        if (BuildConfig.LAYOUT_TYPE == 1) {
            return R.layout.bar_player_horiztal_v212_1280_720;
        } else {
            return R.layout.bar_player_horiztal_v212;
        }
    }

    private void init(Context ctx) {
        mContext = ctx;
        LayoutInflater.from(ctx).inflate(getLayoutId(), this, true);
        setClickable(true);

        infoPlayer = findViewById(R.id.infoPlayer);

        playerBarCover = (ImageView) findViewById(R.id.player_bar_cover);

//        player_bar_activty_tv = findViewById(R.id.player_bar_activty_tv);
        playerBarHome = findViewById(R.id.player_bar_home);
        if (playerBarHome != null) {
            playerBarHome.setOnClickListener(v -> {
                FragmentActivity activity = (FragmentActivity) getContext();
                if (activity != null) {
                    Log.i(TAG, "mPlayerBar home onClick");
                    activity.onBackPressed();
                }
            });
        }

        playerBarLoading = (CircularProgressBar) findViewById(R.id.player_bar_loading);
        playerBarPlay = (ImageView) findViewById(R.id.player_bar_play);
        playerBarCollect = (ImageView) findViewById(R.id.player_bar_collect);
        playerBarPrevious = (ImageView) findViewById(R.id.player_bar_previous);
        playerBarNext = (ImageView) findViewById(R.id.player_bar_next);
//        playerBarPlayRl = findViewById(R.id.player_bar_play_rl);

        playerBarTitle = findViewById(R.id.player_bar_title_text);
        playerBarTagText = findViewById(R.id.player_bar_tag_text);
//        playerBarSubTagText = findViewById(R.id.player_bar_sub_tag_text);
//        playerBarTimeText = findViewById(R.id.player_bar_time_text);
//        playerBarVerticalLine = findViewById(R.id.player_bar_vertical_line);
//        playerBarSubTitleText = findViewById(R.id.player_bar_sub_title_text);

//        playerBarLivePlayRl = (LinearLayout) findViewById(R.id.playerbar_live_rl);

//        playerBarActivity = findViewById(R.id.playerbar_activity);
//        playerBarActivity.setOnClickListener(v -> {
//            Log.i(TAG, "点击活动按钮");
//            PageJumper.getInstance().jumpToActivityPage();
//            //事件上报
//            ButtonClickReportEvent event = new ButtonClickReportEvent(BUTTON_BANNER);
//            ReportHelper.getInstance().addEvent(event);
//        });
//        rlContent = findViewById(R.id.rl);
//        playerBarLiveClose = (ImageView) findViewById(R.id.player_bar_live_iv);
//        playerBarLivePlayPause = (ImageView) findViewById(R.id.player_bar_live_playorPause_iv);
        playerBarConstrantlayoutRl = findViewById(R.id.player_bar_constrantlayout_rl);
        playerBarConstrantlayoutRl.setContentDescription(ResUtil.getString(R.string.content_desc_player_playing));
        OnClickListener clickListener = v -> {
            Log.i(TAG, "mPlayerBar onClick");
            if (!AntiShake.check(v.getId())) {
                Log.i(TAG, "mPlayerBar onClick be executed");
                int type = PlayerManager.getInstance().getCustomType();
                if (type == PlayerConstants.RESOURCES_TYPE_VIDEO_AUDIO ||
                        type == PlayerConstants.RESOURCES_TYPE_VIDEO_ALBUM ||
                        type == PlayerConstants.RESOURCES_TYPE_TEMP_TASK) {
                    navigationToVideoPlayer();
                } else {
                    PageJumper.getInstance().jumpToPlayerFragment(this);
                }
            }
        };

        playerBarConstrantlayoutRl.setOnClickListener(clickListener);
        playerBarCover.setOnClickListener(clickListener);
        playerVideoMinimum = findViewById(R.id.player_bar_video_minimum);
        if (PlayerManager.getInstance().getCustomType() == PlayerConstants.RESOURCES_TYPE_VIDEO_AUDIO ||
                PlayerManager.getInstance().getCustomType() == PlayerConstants.RESOURCES_TYPE_VIDEO_ALBUM ||
                audioContainVideoPiece()) {
            playerVideoMinimum.setVisibility(View.VISIBLE);
        } else {
            playerVideoMinimum.setVisibility(View.GONE);
        }
        playerVideoMinimum.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                navigationToVideoPlayer();
            }
        });

        messageBadgeView = findViewById(R.id.bar_message_badge_view);
        messageBadgeView.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                AdvertisingImagerImpl imager = (AdvertisingImagerImpl) AdvertisingManager.getInstance().getImager();
                // fix 空指针crash
                if (imager != null) {
                    Advert advert = imager.getAdvert();
                    if (advert instanceof ImageAdvert) {
                        imager.skip((ImageAdvert) advert);
                    }
                } else {
                    Log.w(TAG, "AdvertisingImagerImpl is null!!!!");
                }
                RouterManager.getInstance().navigateToPage(getContext(), Constants.PAGE_ID_MESSAGE);
                ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_PLAYER_MESSAGE_BUTTON, "", ReportParameterManager.getInstance().getPage(), ReportConstants.CONTROL_TYPE_SCREEN));
            }
        });
        mAiRadioMinusFeedbackView = findViewById(R.id.ai_radio_minus_feed_back_view);
        KRadioNavBarInter kRadioNavBarInter = ClazzImplUtil.getInter("KRadioNavBarImpl");
        if (kRadioNavBarInter != null) {
            kRadioNavBarInter.initNavBarHomeUI(this);
        }

        mPresenter = new ComprehensivePlayerBarPresenter(this);
        playerBarProgress = findViewById(R.id.player_bar_progress);
        mProgressBarThumbView = findViewById(R.id.thumb_view);
        playerBarProgress.setThumbView(mProgressBarThumbView);
        playerBarProgress.setOnSeekBarChangeListener(new IndicatorV212SeekBar.OnIndicatorSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, float indicatorOffset) {

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                Log.d(TAG, "onStartTrackingTouch");
                isSeekByUser = true;
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                PlayerManagerHelper.getInstance().seek(seekBar.getProgress());
                // 延迟一秒，否则可能出现buffer还没缓冲好，进度条快速闪烁回原位置的问题
                mHandler.postDelayed(() -> {
                    Log.d(TAG, "onStopTrackingTouch");
                    isSeekByUser = false;
                }, 1000);
            }

            @Override
            public String getProgressText(int progress) {
                int duration = playerBarProgress.getMax();
                duration = duration < 0 ? 0 : duration;
                String currentProgressTime = TimeUtil.getStringForTimeShowHour(progress);
                String totalTime = TimeUtil.getStringForTimeShowHour(duration);
                String str = String.format("%s/%s", currentProgressTime, totalTime);
                return str;
            }
        });
//        playerBarLiveClose.setOnClickListener(new OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(getContext())) {
//                    return;
//                }
////             boolean isLivePlaying = LiveBroadcastPlayerManager.getInstance().isPlaying();
//                playerBarCover.setImageDrawable(null);
//                updatePlayItemTitle("", "");
//                PlayerManager.getInstance().reset();
//                ComponentClient.obtainBuilder(LiveComponentConst.NAME)
//                        .setActionName(LiveComponentConst.EXIT_CHAT_ROOM)
//                        .build().callAsync();
//                playerBarLivePlayPause.setImageResource(R.drawable.ic_player_bar_pause_normal);
//                playerBarLivePlayRl.setVisibility(View.GONE);
//                playerBarPlayRl.setVisibility(View.VISIBLE);
//            }
//        });

//        playerBarLivePlayPause.setOnClickListener(v -> {
//            PlayItem mCurrentPlayItem = PlayerManager.getInstance().getCurPlayItem();
//            PlayerLogUtil.log(getClass().getSimpleName(), "click play or pause: " + mCurrentPlayItem.getType());
//
//            if (mCurrentPlayItem.getType() == PlayerConstants.BROADCAST_STATUS_LIVING || mCurrentPlayItem.getType() == PlayerConstants.RESOURCES_TYPE_LIVING) {
//                PlayerLogUtil.log(getClass().getSimpleName(), "live play or pause");
//
//                if (PlayerManager.getInstance().isPlaying()) {
//                    PlayerLogUtil.log(getClass().getSimpleName(), "pause");
//                    PlayerManagerHelper.getInstance().pause(true);
//                    Drawable drawable = ResUtil.getDrawable(R.drawable.playerbar_play);
//                    playerBarLivePlayPause.setImageDrawable(drawable);
//                    ReportUtil.addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_PAUSE, PlayerUiControlReportEvent.CONTROL_TYPE_CLICK, PlayerUiControlReportEvent.POSITION_PLAY_BAR);
//                } else {
//                    if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(getContext())) {
//                        return;
//                    }
//                    PlayerLogUtil.log(getClass().getSimpleName(), "play");
//                    PlayerManagerHelper.getInstance().play(true);
//                    playerBarLivePlayPause.setImageResource(R.drawable.ic_player_bar_pause_normal);
//                    ReportUtil.addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_PLAY, PlayerUiControlReportEvent.CONTROL_TYPE_CLICK, PlayerUiControlReportEvent.POSITION_PLAY_BAR);
//                }
//            }
//        });
        // 所见即可说，快进、快退、重播功能
        mPlayForward = findViewById(R.id.cd_forward);
        mPlayBackward = findViewById(R.id.cd_backward);
        mReplay = findViewById(R.id.cd_replay);
        mPlayForward.setOnClickListener((v) -> {
            if (playerBarProgress.isEnabled()) {
                PlayerManagerHelper.getInstance().fastForward(30);
            } else {
                if (PlayerManagerHelper.getInstance().getCurPlayItem().isLiving()) {
                    ToastUtil.showInfo(getContext(), ResUtil.getString(R.string.content_desc_live_playing_tip));
                } else {
                    Log.d(TAG, "playerBarProgress not enabled");
                }
            }
        });
        mPlayBackward.setOnClickListener((v) -> {
            if (playerBarProgress.isEnabled()) {
                PlayerManagerHelper.getInstance().fastBackward(30);
            } else {
                if (PlayerManagerHelper.getInstance().getCurPlayItem().isLiving()) {
                    ToastUtil.showInfo(getContext(), ResUtil.getString(R.string.content_desc_live_playing_tip));
                } else {
                    Log.d(TAG, "playerBarProgress not enabled");
                }
            }
        });
        mReplay.setOnClickListener((v) -> {
            if (playerBarProgress.isEnabled()) {
                PlayerManagerHelper.getInstance().seek(0);
            } else {
                if (PlayerManagerHelper.getInstance().getCurPlayItem().isLiving()) {
                    ToastUtil.showInfo(getContext(), ResUtil.getString(R.string.content_desc_live_playing_tip));
                } else {
                    Log.d(TAG, "playerBarProgress not enabled");
                }
            }
        });

        EventBus.getDefault().register(this);
        showPauseState();

        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_PLAYER_MESSAGE_BUTTON, "", ReportParameterManager.getInstance().getPage(), ReportConstants.CONTROL_TYPE_SCREEN));
    }

    /**
     * 跳到直播界面
     */
    public void jumpToLivePage(Category.Item item) {
        PlayerManagerHelper.getInstance().finishAudioAd();
        Log.i(TAG, "jumpToLivePage");
        long id = -1L;
        if (item != null) {
            id = item.id;
        }
        ComponentClient.obtainBuilder(LiveComponentConst.NAME)
                .setActionName(LiveComponentConst.START_HOME_LIVE)
                .cancelOnDestroyWith((Activity) getContext())
                .addParam("liveId", id)
                .addParam("context", this)
                .addParam("containerId", R.id.launcher_main_layout)
                .build().callAsync();
    }

    public void setPlayerBarButtonMargin(int orition) {
        int mMarginLeft = 0, marginRight = 0;
        if (orition == 1) {
//            竖屏
            mMarginLeft = (int) getResources().getDimension(R.dimen.x4);
        } else {
//            横屏
            mMarginLeft = (int) getResources().getDimension(R.dimen.x56);
            marginRight = (int) getResources().getDimension(R.dimen.x36);
        }
//        ConstraintLayout.LayoutParams cl = (ConstraintLayout.LayoutParams) rlContent.getLayoutParams();
//        if (orition == 1) {
//            cl.startToStart = 0;
//            cl.startToEnd = -1;
//        } else {
//            cl.startToStart = -1;
//            cl.startToEnd = R.id.playerbar_activity;
//        }
//        rlContent.setLayoutParams(cl);
//        int p_margin = mMarginLeft;
//        if (orition == 1) {
//            p_margin = ResUtil.getDimen(R.dimen.x20);
//        }
//        ConstraintLayout.LayoutParams playerBarPrevious_lp = (ConstraintLayout.LayoutParams) playerBarPrevious.getLayoutParams();
////        playerBarPrevious_lp.gravity = Gravity.CENTER_VERTICAL;
//        playerBarPrevious_lp.setMargins(p_margin, 0, marginRight, 0);
//        playerBarPrevious_lp.setMarginStart(p_margin);
//        playerBarPrevious_lp.startToEnd = R.id.bar_message_badge_view;
//        playerBarPrevious.setLayoutParams(playerBarPrevious_lp);
////        playerBarPrevious.requestLayout();
//
//
//        LinearLayout.LayoutParams playerBarCollect_lp = (LinearLayout.LayoutParams) playerBarCollect.getLayoutParams();
//        playerBarCollect_lp.gravity = Gravity.CENTER_VERTICAL;
//        playerBarCollect_lp.setMargins(marginRight, 0, marginRight, 0);
////        playerBarCollect.requestLayout();
//
//
//        LinearLayout.LayoutParams playerBarNext_lp = (LinearLayout.LayoutParams) playerBarNext.getLayoutParams();
//        playerBarNext_lp.gravity = Gravity.CENTER_VERTICAL;
//        playerBarNext_lp.setMargins(0, 0, mMargin, 0);
////        playerBarNext.requestLayout();
//
//
////        LinearLayout.LayoutParams playerBarLiveClose_lp = (LinearLayout.LayoutParams) playerBarLiveClose.getLayoutParams();
////        playerBarLiveClose_lp.gravity = Gravity.CENTER_VERTICAL;
////        playerBarLiveClose_lp.setMargins(0, 0, mMargin, 0);
//////        playerBarLiveClose.requestLayout();
////
////        LinearLayout.LayoutParams playerBarLivePlayPause_lp = (LinearLayout.LayoutParams) playerBarLivePlayPause.getLayoutParams();
////        playerBarLivePlayPause_lp.gravity = Gravity.CENTER_VERTICAL;
////        playerBarLivePlayPause_lp.setMargins(mMargin, 0, mMargin, 0);
//////        playerBarLivePlayPause.requestLayout();
//
//
//        LinearLayout.LayoutParams playerBarPlay_lp = (LinearLayout.LayoutParams) playerBarPlay.getLayoutParams();
//        playerBarPlay_lp.gravity = Gravity.CENTER_VERTICAL;
//        playerBarPlay_lp.setMargins(mMargin, 0, mMargin, 0);
////        playerBarPlay.requestLayout();
//
//
//        LinearLayout.LayoutParams aiRadioMinusFeedbackView_lp = (LinearLayout.LayoutParams) mAiRadioMinusFeedbackView.getLayoutParams();
//        aiRadioMinusFeedbackView_lp.gravity = Gravity.CENTER_VERTICAL;
//        aiRadioMinusFeedbackView_lp.setMargins(0, 0, mMargin + (int) getResources().getDimension(R.dimen.x5), 0);

//        changeOrientationTextSize(R.dimen.player_bar_title_text_size_p, R.dimen.player_bar_title_text_size_h);
    }

    private void changeOrientationTextSize(int portrait, int landscap) {
        Log.i(TAG, "changeOrientationTextSize:" + ResUtil.getTextSize(portrait) + " " + ResUtil.getTextSize(landscap));
        OritationViewUtils.handleOrientationTextSize(playerBarTitle, playerBarTitle, portrait, landscap);
    }

//    public RelativeLayout getPlayerBarConstrantlayoutRl() {
//        return playerBarConstrantlayoutRl;
//    }

//    public View getPlayerBarActivityView() {
//        return playerBarActivity;
//    }

//    public void setPlayerBarConstrantlayoutRl(RelativeLayout playerBarConstrantlayoutRl) {
//        this.playerBarConstrantlayoutRl = playerBarConstrantlayoutRl;
//    }

    public void attachPlayer() {
        mPresenter.attachPlayer();
    }

    public void detachPlayer() {
        mPresenter.detachPlayer();
    }

    public void attachSubscribeModel(SubscribeModel subscribeModel) {
        mPresenter.attachSubscribeModel(subscribeModel);
    }

    public void detachSubscribeModel() {
        mPresenter.detachSubscribeModel();
    }

    @Override
    public void setCoverImageDrawable(Drawable drawable) {
        playerBarCover.setImageDrawable(drawable);
    }

    @Override
    public void setCoverImageUrl(String coverUrl) {
        ImageLoader.getInstance().displayImage(getContext(), coverUrl, playerBarCover, ResUtil.getDrawable(R.drawable.media_default_pic));
    }

    @Override
    public void setTitle(CharSequence title) {
        Log.i("onekeylistener", title.toString());
        playerBarTitle.setText(title);
    }

    @Override
    public void setSubtitle(CharSequence title) {
        // playerBarSubtitle.setText(title);
    }

    /**
     * @param progress 1~100
     */
    @Override
    public void updateProgress(int progress) {
//        修改 退出app ,播放器还在播放，再次打开app ，更新播放条状态，post 更新
        if (isSeekByUser) {
            return;
        }
        if (progress > playerBarProgress.getProgress()) {
            playerBarProgress.setProgress(progress);
        }
    }

    @Override
    public void updateProgress(int position, int duration) {
        if (isSeekByUser) {
            return;
        }
        post(new Runnable() {
            @Override
            public void run() {
                playerBarProgress.setMax(duration);
                playerBarProgress.setProgress(position);
            }
        });
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        showAccordingToScreen(ResUtil.getOrientation());
        attachPlayer();
        detachSubscribeModel();
        attachSubscribeModel(new SubscribeModel());
        addLifeObs();
    }

    @Override
    protected void onDetachedFromWindow() {
        detachPlayer();
        super.onDetachedFromWindow();
        EventBus.getDefault().unregister(this);
        if (mAiRadioMinusFeedbackView != null) {
            mAiRadioMinusFeedbackView.release();
        }
        removeLifeObs();
    }

    private void addLifeObs() {
        getLifecycle().addObserver(lifecycleObserver = new LifecycleObserver() {
            @OnLifecycleEvent(Lifecycle.Event.ON_RESUME)
            public void onResume() {
                //只在启辰车机上出现这个问题，37534
                resetPlayerBarState();
                if (mPresenter != null)
                    mPresenter.updateBtnsState();
            }
        });
    }

    private void removeLifeObs() {
        if (lifecycleObserver != null) {
            getLifecycle().removeObserver(lifecycleObserver);
            lifecycleObserver = null;
        }
    }

    LifecycleObserver lifecycleObserver;


    @Override
    public void showLoading(boolean show) {
        if (isLiveState) {
            playerBarLoading.setVisibility(View.GONE);
            playerBarLoading.progressiveStop();
        } else {
            if (show && playerBarLoading.getVisibility() == VISIBLE) {
                return;
            }
            ViewUtil.setViewVisibility(playerBarLoading, show ? View.VISIBLE : View.INVISIBLE);
            if (show) {
                playerBarLoading.progressiveStart();
            } else {
                playerBarLoading.progressiveStop();
            }
        }
    }

    @Override
    public void showPauseState() {
        Logging.i(TAG, "showPauseState start isLiveState = " + isLiveState);
//        if (!isLiveState) {
        playerBarPlay.setImageResource(R.drawable.comprehensive_playerbar_play);
        playerBarPlay.setContentDescription(ResUtil.getString(R.string.content_desc_play));
        Logging.i(TAG, "showPauseState drawable playerbar_play");
//        } else {
//            Drawable drawable = ResUtil.getDrawable(R.drawable.playerbar_play);
//            playerBarLivePlayPause.setImageDrawable(drawable);
//        }
        playerBarTitle.setEllipsize(TextUtils.TruncateAt.END);
    }

    @Override
    public void showPlayState() {
        Logging.i(TAG, "showPlayState start isLiveState = " + isLiveState);
        if (!isLiveState) {
            playerBarPlay.setImageResource(R.drawable.comprehensive_playerbar_pause);
            playerBarPlay.setContentDescription(ResUtil.getString(R.string.content_desc_pause));
            Logging.i(TAG, "showPlayState drawable ic_player_bar_pause_normal");
        }
        playerBarTitle.setEllipsize(TextUtils.TruncateAt.MARQUEE);
    }

    /**
     * 收藏
     */
    @Override
    public void setCollectState(boolean isCollect) {
        playerBarCollect.setSelected(isCollect);
        if (isCollect) {
            playerBarCollect.setContentDescription(ResUtil.getString(R.string.content_desc_collect_cancel));
        } else {
            playerBarCollect.setContentDescription(ResUtil.getString(R.string.content_desc_collect));
        }

//        boolean isDisplay =  PlayerManager.getInstance().getPlayListInfo().getNoSubscribe() != 1;
//        ViewUtil.setViewVisibility(playerBarCollect, isDisplay ? VISIBLE : View.GONE);
    }

    @Override
    public boolean getCollectState() {
        if (playerBarCollect != null) {
            return playerBarCollect.isSelected();
        }
        return false;
    }

    @Override
    public void setCollectClickListener(OnClickListener listener) {
        playerBarCollect.setOnClickListener(listener);
    }

    @Override
    public void setPlayVideoMaximumClickListener(OnClickListener listener) {
        playerVideoMinimum.setOnClickListener(listener);
    }

    @Override
    public void setNextClickListener(OnClickListener listener) {
        playerBarNext.setOnClickListener(listener);
    }

    @Override
    public void setPreClickListener(OnClickListener listener) {
        playerBarPrevious.setOnClickListener(listener);
    }

    @Override
    public void setPlayOrPauseClickListener(OnClickListener listener) {
        playerBarPlay.setOnClickListener(listener);
    }

    private String getAlbumName(PlayItem playItem) {
        if (playItem instanceof BroadcastPlayItem) {
            String frequencyChannel = ((BroadcastPlayItem) playItem).getFrequencyChannel();
            String albumName = ((BroadcastPlayItem) playItem).getInfoData().getAlbumName();
            if (!StringUtil.isEmpty(frequencyChannel)) {
                return String.format(AppDelegate.getInstance().getContext().getResources().getString(R.string.one_zh_cn_char_joint_str), albumName, ResUtil.getString(R.string.full_str), frequencyChannel);
            } else {
                return albumName;
            }
        } else if (playItem instanceof TVPlayItem) {
            String frequencyChannel = ((TVPlayItem) playItem).getFrequencyChannel();
            String albumName = ((TVPlayItem) playItem).getInfoData().getAlbumName();
            if (!StringUtil.isEmpty(frequencyChannel)) {
                return String.format(AppDelegate.getInstance().getContext().getResources().getString(R.string.one_zh_cn_char_joint_str), albumName, ResUtil.getString(R.string.full_str), frequencyChannel);
            } else {
                return albumName;
            }
        }
        return playItem.getAlbumTitle();
    }


    @Override
    public void updateInfo(int cp, PlayItem playItem) {
        if (playItem == null) {
            return;
        }
        String title;
        //特殊场景：直播直接显示名称。
        if (playItem.getType() == PlayerConstants.RESOURCES_TYPE_LIVING) {
            title = playItem.getTitle();
        } else
            //广播：电台封面、节目名--电台名称FMxx.x
            //电视：电台封面、节目名--CCTVxx
            //若获取不到节目单：电台封面、暂无节目单-- 电台名称
            if (!PlayerManagerHelper.getInstance().isHasBroadcastPlayList()) {
                title = ResUtil.getString(R.string.comprehensive_player_broadcast_no_playlist);
            } else {
                title = playItem.getTitle();
            }
        String subtitle = getAlbumName(playItem);
        String audioPic = PlayerManagerHelper.getInstance().getPlayItemPicUrl(playItem);

        int type = playItem.getType();
        if (PlayerConstants.RESOURCES_TYPE_ALBUM == type) {
            String order = getResources().getString(R.string.album_audio_info_format_str);
            order = StringUtil.format(order, ((AlbumPlayItem) playItem).getInfoData().getOrderNum());
            title = order + "  " + title;
        } else if (PlayerConstants.RESOURCES_TYPE_VIDEO_ALBUM == type) {
            // 视频不需要显示 [第几期]
//            String order = getResources().getString(R.string.album_audio_info_format_str);
//            order = StringUtil.format(order, ((VideoAlbumPlayItem) playItem).getInfoData().getOrderNum());
//            title = order + "  " + title;
        }
        // 设置圆形图片
        ImageLoader.getInstance().displayImage(getContext(), audioPic, playerBarCover, ResUtil.getDrawable(R.drawable.media_default_pic));

        int whichPlayer = PlayerManagerHelper.getInstance().getCurPlayItem().getType();

        setPlayerUi(whichPlayer);

        String flag = ComprehensivePlayerHelper.getPlayFlagType(playItem);
        if (!StringUtil.isEmpty(flag)) {
            playerBarTagText.setVisibility(View.VISIBLE);
            playerBarTagText.setText(flag);
        } else {
            playerBarTagText.setVisibility(View.GONE);
        }

        if (type == PlayerConstants.RESOURCES_TYPE_RADIO) {
//            updateSubTitle(playItem); //原先的逻辑是ai电台显示碎片更新时间等信息，212版本上要求显示电台名称
            updatePlayItemTitle(formatTitle(playItem), playItem.getRadioName());
        } else if (type == PlayerConstants.RESOURCES_TYPE_LIVING) {
            updatePlayItemTitle(title, playItem.getHost());
        } else {
            updatePlayItemTitle(title, subtitle);
        }

        if (PlayerManager.getInstance().getCustomType() == PlayerConstants.RESOURCES_TYPE_VIDEO_AUDIO ||
                PlayerManager.getInstance().getCustomType() == PlayerConstants.RESOURCES_TYPE_VIDEO_ALBUM ||
                audioContainVideoPiece()) {
            playerVideoMinimum.setVisibility(View.VISIBLE);
        } else {
            playerVideoMinimum.setVisibility(View.GONE);
        }
    }

//    private void setButtonIcons(@DrawableRes int collectIconRid, @DrawableRes int leftIconRid, @DrawableRes int rightIconRid) {
//        if (playerBarPrevious.getVisibility() == View.VISIBLE) {
//            playerBarPrevious.setVisibility(View.GONE);
//        }
//        if (playerBarNext.getVisibility() != View.VISIBLE) {
//            playerBarNext.setVisibility(View.VISIBLE);
//        }
//
//        showOrHideAiRadioMinusFeedback(false);
//        showOrHideCollect(true);
//        playerBarCollect.setImageDrawable(ResUtil.getDrawable(collectIconRid));
//        playerBarPrevious.setImageDrawable(ResUtil.getDrawable(leftIconRid));
//        playerBarNext.setImageDrawable(ResUtil.getDrawable(rightIconRid));
//    }

    private void setBroadcastIcons(@DrawableRes int leftIconRid, @DrawableRes int rightIconRid) {
        showOrHideAiRadioMinusFeedback(false);
        showOrHideCollect(false);

        if (playerBarPrevious.getVisibility() != View.VISIBLE) {
            playerBarPrevious.setVisibility(View.VISIBLE);
        }
        if (playerBarNext.getVisibility() != View.VISIBLE) {
            playerBarNext.setVisibility(View.VISIBLE);
        }

        Drawable drawable = ResUtil.getDrawable(leftIconRid);
        playerBarPrevious.setImageDrawable(drawable);

        Drawable drawable2 = ResUtil.getDrawable(rightIconRid);
        playerBarNext.setImageDrawable(drawable2);
    }

    @Override
    public void showBroadcastState() {
        isLiveState = false;

        setBroadcastIcons(R.drawable.playerbar_broadcast_prev, R.drawable.playerbar_broadcast_next);

        if (playerBarPlay.getVisibility() != View.VISIBLE) {
            playerBarPlay.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void showNormalState() {
        isLiveState = false;

//        setButtonIcons(R.drawable.playerbar_collect, R.drawable.playerbar_prev, R.drawable.playerbar_next);

        if (playerBarPlay.getVisibility() != View.VISIBLE) {
            playerBarPlay.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void showLiveState() {
        isLiveState = true;
//        playerBarLivePlayRl.setVisibility(View.VISIBLE);
//        if (PlayerManager.getInstance().isPlaying()) {
//            playerBarLivePlayPause.setImageResource(R.drawable.ic_player_bar_pause_normal);
//        } else {
//            Drawable drawable = ResUtil.getDrawable(R.drawable.playerbar_play);
//            playerBarLivePlayPause.setImageDrawable(drawable);
//        }
//        playerBarPlayRl.setVisibility(View.GONE);
    }


    @Override
    public void setPrevState(boolean enable) {
        playerBarPrevious.setActivated(enable);
        playerBarCollect.setActivated(enable);
    }

    @Override
    public void setNextState(boolean hasNext) {
        playerBarNext.setActivated(hasNext);
    }

    public void setPlayerAndPauseBtnState(boolean enabled) {
        playerBarPlay.setEnabled(enabled);
    }

    @Override
    public void setEnabled(boolean enabled) {
        super.setEnabled(enabled);
        setPlayerAndPauseBtnState(enabled);
        showOrHideCollect(enabled);
        setPrevState(enabled);
        setNextState(enabled);
        updateProgress(0);
        updatePlayItemTitle("", "");
    }

    @Override
    public void showToast(String info) {
        ToastUtil.showOnly(getContext().getApplicationContext(), info);
    }

    @Override
    public void showError(int strRid) {
        if (NetworkUtil.isNetworkAvailable(getContext().getApplicationContext(), true)) {
            KRadioToastInter radioToastInter = ClazzImplUtil.getInter("KRadioToastImpl");
            if (radioToastInter != null) {
                radioToastInter.showToast(0, getContext().getApplicationContext(), getResources().getString(strRid));
            } else {
                ToastUtil.showNormal(getContext().getApplicationContext(), strRid);
            }
        }
    }


    /*****************************************************************************************************/
    /*****************************************************************************************************/


    private void setPlayerUi(int whichPlayer) {
        switch (whichPlayer) {
//            case PlayerConstants.RESOURCES_TYPE_LIVING: {
//                Log.i("PlayStatus", "PLAYER_TYPE_LIVE");
//                showLiveState();
//            }
//            break;
//            case PlayerConstants.RESOURCES_TYPE_BROADCAST: {
//                Log.i("PlayStatus", "PLAYER_TYPE_BROADCAST");
//                showBroadcastState();
//            }
//            break;
            default: {
                Log.i("PlayStatus", "showNormalState");
                showNormalState();
            }
            break;
        }
    }


    /**
     * 为了在断网情况下,统一首页与播放器页面的订阅状态.
     *
     * @return
     */
    public boolean getSubscribeState() {
        return playerBarCollect.isSelected();
    }

    @NonNull
    @Override
    public Lifecycle getLifecycle() {
        return ((AppCompatActivity) getContext()).getLifecycle();
    }


    /**
     * ai电台设置subTitle
     *
     * @param playItem
     */
    protected void updateSubTitle(PlayItem playItem) {
        if (!(playItem instanceof RadioPlayItem)) {
            return;
        }
        RadioPlayItem radioPlayItem = (RadioPlayItem) playItem;

        playerBarTitle.setText(formatTitle(playItem));

        String time = radioPlayItem.getInfoData().getUpdateTime();
        String name = radioPlayItem.getRadioInfoData().getSubheadName();

        String playerBarTimeText = null, playerBarSubTitleText = null;
        /**
         * 如果是台宣, 只显示标签, 不显示其他信息.
         */
        if (PlayerManagerHelper.getInstance().isRadioTaiXuan(playItem)) {
            playerBarTimeText = null;
            playerBarSubTitleText = null;
            updatePlayItemTitle(formatTitle(playItem), "");
            return;
        }
        if (TextUtils.isEmpty(time)
                || TextUtils.isEmpty(name)) {
//            playerBarVerticalLine.setVisibility(View.GONE);
        }
        if ((!TextUtils.isEmpty(time))
                && (!TextUtils.isEmpty(name))) {
            if (!PlayerManagerHelper.getInstance().isRadioTaiXuan(playItem)) {
                playerBarTimeText = getAIAudioUpdateTime(getContext(), Long.parseLong(time), radioPlayItem.getTimeType());
                playerBarSubTitleText = name;
            } else {
                playerBarTimeText = name;
            }
        }

        if (TextUtils.isEmpty(name) && !TextUtils.isEmpty(time)) {
            if (!PlayerManagerHelper.getInstance().isRadioTaiXuan(playItem)) {
                playerBarTimeText = getAIAudioUpdateTime(getContext(), Long.parseLong(time), radioPlayItem.getTimeType());
            } else {
                playerBarTimeText = null;
            }
            playerBarSubTitleText = null;
        }

        if (TextUtils.isEmpty(time) && !TextUtils.isEmpty(name)) {
            playerBarTimeText = name;
            playerBarSubTitleText = null;
        }

        if (TextUtils.isEmpty(time) && TextUtils.isEmpty(name)) {
            playerBarTimeText = null;
            playerBarSubTitleText = null;
        }

        StringBuilder sb = new StringBuilder();
        if (!StringUtil.isEmpty(playerBarTimeText)) {
            sb.append(playerBarTimeText);
            if (!StringUtil.isEmpty(playerBarSubTitleText)) {
                sb.append("|").append(playerBarSubTitleText);
            }
        } else {
            if (!StringUtil.isEmpty(playerBarSubTitleText)) {
                sb.append(playerBarSubTitleText);
            }
        }
        updatePlayItemTitle(formatTitle(playItem), sb.toString());
    }

    private String formatSubTag(String subTag) {
        /**
         * 去掉半角空格
         */
        String subTemp = subTag.replace(" ", "");
        /**
         * 去掉全角空格
         */
        String subTemp1 = subTemp.replace("　", "").trim();

        if (StringUtil.isEmpty(subTemp1)) {
            return null;
        }
        int len = subTemp1.length();
        if (len <= MAX_SUB_TAG_LEN) {
            return subTemp1;
        }
        String tag = subTemp1.substring(0, MAX_SUB_TAG_LEN);
        return StringUtil.join(tag, SUB_TAG_REPLACE_STRING);
    }

    private String formatTitle(PlayItem playItem) {
        String title = playItem.getTitle();

        if (playItem instanceof RadioPlayItem) {
            title = ((RadioPlayItem) playItem).getRadioInfoData().getMainTitleName();

            if (PlayerManagerHelper.getInstance().isRadioTaiXuan(playItem)) {
                title = ((RadioPlayItem) playItem).getRadioInfoData().getSubheadName();
            }
        }

        return title;
    }

    /**
     * 显示和隐藏负反馈
     *
     * @param showMinus
     */
    private void showOrHideAiRadioMinusFeedback(boolean showMinus) {
        if (showMinus) {
            ViewUtil.setViewVisibility(mAiRadioMinusFeedbackView, View.VISIBLE);
        } else {
            ViewUtil.setViewVisibility(mAiRadioMinusFeedbackView, View.GONE);
        }
    }

    /**
     * 显示和隐藏收藏
     *
     * @param showCollect
     */
    private void showOrHideCollect(boolean showCollect) {
        PlayItem curPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
        boolean isDisplay = true;
        if (PlayerManager.getInstance().getPlayListInfo() != null) {
            isDisplay = PlayerManager.getInstance().getPlayListInfo().getNoSubscribe() != 1;
        }
        if (PlayerManagerHelper.getInstance().getCurrentPlayType() == PlayerConstants.RESOURCES_TYPE_LIVING) {
            isDisplay = false;
        }
        if (showCollect && isDisplay) {
            ViewUtil.setViewVisibility(playerBarCollect, View.VISIBLE);
            String radioId = null, audioId = null;
            if (curPlayItem != null) {
                radioId = curPlayItem.getRadioId();
                audioId = String.valueOf(curPlayItem.getAudioId());
            }
            ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_PLAYER_SUBSCRIBE_BUTTON, "", ReportParameterManager.getInstance().getPage(), ReportConstants.CONTROL_TYPE_SCREEN, null, radioId, audioId, null));
        } else {
            ViewUtil.setViewVisibility(playerBarCollect, View.GONE);
        }
    }

    /**
     * 点击播放bar跳转视频播放页
     */
    private void navigationToVideoPlayer() {
        if (PlayerManagerHelper.getInstance().invalidPlayAction()) {
            return;
        }
        if (PlayerManagerHelper.getInstance().lockPlayerIntercept()) {
            if (PlayerManagerHelper.getInstance().isAudioAdPlayLockPlayer()) {
                return;
            }
            mHandler.postDelayed(() -> mHandler.post(() -> PlayerManagerHelper.getInstance().jumpAd(false)),500L);
        }
        Map<String, String> map = new HashMap<>();
        String albumId = String.valueOf(PlayerManagerHelper.getInstance().getCurPlayItem().getAudioId());
        if (PlayerManager.getInstance().getSpeedLimitState()) {
            ToastUtil.showInfo(getContext(), R.string.video_player_overspeed_toast);
            if (audioContainVideoPiece()) {
                boolean isPlaying = PlayerManagerHelper.getInstance().isPlaying();
                TempTaskPlayItem tempTaskPlayItem = new TempTaskPlayItem();
                tempTaskPlayItem.setPlayUrl(PlayerManagerHelper.getInstance().getCurPlayItem().getVideoPiece().getPlayUrl());
                tempTaskPlayItem.setPlayStateListener(new BasePlayStateListener() {
                    @Override
                    public void onPlayerEnd(PlayItem playItem) {
                        super.onPlayerEnd(playItem);
                        // ZMKQ-5960 处理视频碎片temptask播放音频时，播放器变成暂停状态。处理方式：此种情况不暂停播放，等待tempTask播放结束后，暂停PlayItem、继续播放PlayItem
                        if (isPlaying) {
                            PlayerManager.getInstance().switchPlayerStatus();
                            new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                                @Override
                                public void run() {
                                    PlayerLogUtil.log("Video piece audio play end, then continue playItem");
                                    PlayerManager.getInstance().switchPlayerStatus();
                                }
                            }, 1500);
                        }
                    }
                });
                PlayerManagerHelper.getInstance().startTempTask(tempTaskPlayItem, !isPlaying);
            } else {
                PlayerManagerHelper.getInstance().startVideo(albumId, PlayerConstants.RESOURCES_TYPE_VIDEO_ALBUM, true);
            }
        } else {
            if (audioContainVideoPiece()) {
                map.put(Constants.ROUTER_PARAMS_KEY_RESOURCE_TYPE, String.valueOf(PlayerConstants.RESOURCES_TYPE_TEMP_TASK));
                map.put(Constants.ROUTER_PARAMS_KEY_RESOURCE_URL, PlayerManagerHelper.getInstance().getCurPlayItem().getVideoPiece().getPlayUrl());
            } else {
                int type = PlayerConstants.RESOURCES_TYPE_VIDEO_AUDIO;
//                if (TextUtils.isEmpty(albumId) || (albumId != null && Long.valueOf(albumId) == 0)) {
//                    albumId = String.valueOf(PlayerManager.getInstance().getCurPlayItem().getAudioId());
//                    type = PlayerConstants.RESOURCES_TYPE_VIDEO_AUDIO;
//                }
                map.put(Constants.ROUTER_PARAMS_KEY_RESOURCE_TYPE, String.valueOf(type));
                map.put(Constants.ROUTER_PARAMS_KEY_RESOURCE_ID, albumId);
            }
            RouterManager.getInstance().navigateToPage(getContext(), Constants.PAGE_ID_VIDEO, map);
        }

    }

    /**
     * 音频资讯含视频
     *
     * @return
     */
    private boolean audioContainVideoPiece() {
        return PlayerManager.getInstance().getCustomType() == PlayerConstants.RESOURCES_TYPE_TEMP_TASK ||
                PlayerManagerHelper.getInstance().getCurPlayItem().videoPieceValidate();
    }

    @Override
    protected void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        showAccordingToScreen(ResUtil.getOrientation());
    }

    @Override
    public void setProgressEnabled(boolean isEnabled) {
        playerBarProgress.setEnabled(isEnabled);
//        if (isEnabled) {
//            ViewUtil.setViewVisibility(playerBarProgress, View.VISIBLE);
//        } else {
//            ViewUtil.setViewVisibility(playerBarProgress, View.GONE);
//        }
    }

    @Override
    public void updateBroadcastErrorInfo() {
        int position = PlayerManagerHelper.getInstance().getCurrentFrequencyPosition();
        BroadcastRadioSimpleData broadcastRadioSimpleData = PlayerManagerHelper.getInstance().getBroadcastRadioSimpleDataByIndex(position);
        if (broadcastRadioSimpleData == null) {
            return;
        }
        String title = broadcastRadioSimpleData.getName();
        String audioPic = broadcastRadioSimpleData.getImg();
        updatePlayItemTitle(title, "");

        // 设置圆形图片
        ImageLoader.getInstance().displayImage(getContext(), audioPic, playerBarCover, ResUtil.getDrawable(R.drawable.media_default_pic));

        setPlayerUi(PlayerConstants.RESOURCES_TYPE_BROADCAST);

        playerBarTagText.setVisibility(View.VISIBLE);
        playerBarTagText.setText(ResUtil.getString(R.string.comprehensive_playerbar_state_living));
    }

    @Override
    public void showPlayBarType() {
        PlayItem playItem = PlayerManagerHelper.getInstance().getCurPlayItem();
        if (playItem == null) {
            return;
        }
        int whichPlayer = playItem.getType();
        setPlayerUi(whichPlayer);
    }

    @Override
    public Activity getRootActivity() {
        return (Activity) mContext;
    }

    protected synchronized void showAccordingToScreen(int orientation) {

        Log.i("playerbar", "showAccordingToScreen");
//        player_bar_activty_tv.setText(orientation == Configuration.ORIENTATION_LANDSCAPE ? R.string.comprehensive_main_activity_name : R.string.comprehensive_main_activity_name2);
//        playerBarActivity.setVisibility(orientation == Configuration.ORIENTATION_LANDSCAPE ? View.VISIBLE : View.GONE);
//        ConstraintLayout.LayoutParams lp = (ConstraintLayout.LayoutParams) playerBarActivity.getLayoutParams();
//        lp.width = ResUtil.getDimen(R.dimen.default_play_bar_activity_w);
//        playerBarActivity.setLayoutParams(lp);

        setPlayerBarButtonMargin(orientation == Configuration.ORIENTATION_LANDSCAPE ? 0 : 1);
        if (infoPlayer != null) {
            ViewGroup.LayoutParams layoutParams = infoPlayer.getLayoutParams();
            layoutParams.width = getResources().getDisplayMetrics().widthPixels - 2 * ResUtil.getDimen(R.dimen.m80);
//            if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
//                if (BuildConfig.LAYOUT_TYPE == 0) {
//                    //1920x720
//                    layoutParams.width = ResUtil.getDimen(R.dimen.m1760);
//                } else {
//                    //1280x720
//                    layoutParams.width = ResUtil.getDimen(R.dimen.m1120);
//                }
//            } else {
//                layoutParams.width = getResources().getDisplayMetrics().widthPixels - 2 * ResUtil.getDimen(R.dimen.m80);
//            }
            infoPlayer.setLayoutParams(layoutParams);
        }
    }

    /**
     * 上汽大通渠道需求，需要添加按钮返回桌面
     *
     * @param listener
     */
    public void setPlayerBarHomeListener(View.OnClickListener listener) {
        if (playerBarHome != null) {
            playerBarHome.setOnClickListener(listener);
        } else {
            Log.i(TAG, "playerBarHome:" + playerBarHome);
        }
    }

    //  广播暂无广播资源，接受广播，并在此刷新播放条信息
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void playNextIsNotLiving(BroadcastPlayerChangedData broadcastPlayerChangedData) {
        updateInfo(1, PlayerManagerHelper.getInstance().getCurPlayItem());
        resetPlayerBarState();
    }

    private void resetPlayerBarState() {
        PlayerManagerHelper playerManagerHelper = PlayerManagerHelper.getInstance();
        Log.i(TAG, "Player: isPlaying = " + playerManagerHelper.isPlaying());
        Log.i(TAG, "Player: isPlayingClock = " + playerManagerHelper.isPlayingClock());
        if (playerManagerHelper.isPlaying() && !playerManagerHelper.isPlayingClock()) {
            showPlayState();
        } else {
            showPauseState();
        }
    }


    private void updatePlayItemTitle(String title, String subtitle) {
        SpannableStringBuilder spannableString = new SpannableStringBuilder();
        if (!StringUtil.isEmpty(title)) {
            spannableString.append(title);
            spannableString.setSpan(new ForegroundColorSpan(ResUtil.getColor(R.color.player_bar_hight_light_color)), 0, title.length(), SpannableStringBuilder.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
        if (!StringUtil.isEmpty(subtitle)) {
            int beginIndex = spannableString.length();
            if (spannableString.length() > 0) {
                spannableString.append("-");
            }
            spannableString.append(subtitle);
            spannableString.setSpan(new ForegroundColorSpan(ResUtil.getColor(R.color.player_bar_low_light_color)), beginIndex, spannableString.length(), SpannableStringBuilder.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
        playerBarTitle.setText(spannableString);
    }

    public void updateTitleColor() {
        CharSequence text = playerBarTitle.getText();
        if (text instanceof SpannedString) {
            String title = null, subTitle = null;
            ForegroundColorSpan[] spans = ((SpannedString) text).getSpans(0, text.length(), ForegroundColorSpan.class);
            if (spans == null) return;
            if (spans.length > 0) {
                int spanEnd = ((SpannedString) text).getSpanEnd(spans[0]);
                int spanStart = ((SpannedString) text).getSpanStart(spans[0]);
                if (spanEnd != -1)
                    title = text.toString().substring(spanStart, spanEnd);
            }
            if (spans.length > 1) {
                int spanEnd = ((SpannedString) text).getSpanEnd(spans[1]);
                int spanStart = ((SpannedString) text).getSpanStart(spans[1]);
                if (spanEnd != -1 && spanStart != -1)
                    subTitle = text.toString().substring(spanStart + 1, spanEnd);
            }
            updatePlayItemTitle(title, subTitle);
        }
    }

    @Override
    public void applySkin() {
        super.applySkin();
        updateInfo(mPresenter.getCP(), PlayerManagerHelper.getInstance().getCurPlayItem());
    }
}