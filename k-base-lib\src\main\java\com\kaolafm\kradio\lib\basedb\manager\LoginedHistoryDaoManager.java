package com.kaolafm.kradio.lib.basedb.manager;

import android.os.SystemClock;
import android.util.Log;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.lib.basedb.GreenDaoInterface;
import com.kaolafm.kradio.lib.basedb.greendao.LoginedHistoryItemDao;
import com.kaolafm.kradio.lib.basedb.greendao.LoginedHistoryItemDao.Properties;
import com.kaolafm.kradio.lib.bean.HistoryItem;
import com.kaolafm.kradio.lib.bean.LoginedHistoryItem;
import com.kaolafm.kradio.lib.utils.HistoryBeanUtil;
import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.history.HistoryRequest;
import com.kaolafm.opensdk.api.history.model.ListeningHistory;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import org.greenrobot.greendao.Property;

import java.util.ArrayList;
import java.util.List;
import java.util.ListIterator;
import java.util.Map;

/**
 * 登录之后保存本地历史的操作类。
 *
 * <AUTHOR> Yan
 * @date 2020/9/4
 */
public class LoginedHistoryDaoManager extends BaseHistoryDaoManager<LoginedHistoryItem, LoginedHistoryItemDao> {


    private static volatile LoginedHistoryDaoManager mInstance;

    private HistoryRequest mHistoryRequest;

    public static LoginedHistoryDaoManager getInstance() {
        if (mInstance == null) {
            synchronized (LoginedHistoryDaoManager.class) {
                if (mInstance == null) {
                    mInstance = new LoginedHistoryDaoManager();
                }
            }
        }
        return mInstance;
    }

    private LoginedHistoryDaoManager() {
        mHistoryRequest = new HistoryRequest().setTag(this);
    }

    @Override
    public void progress(PlayItem playItem, long progress) {
//        KRadioSaveHistoryInter inter = ClazzImplUtil.getInter("KRadioSaveHistoryImpl");
//        if (inter != null && inter.isSaveProgressHistory()) {
        if (needSaveHistory()) {
            saveHistory(playItem, false);
        }
//        }
    }

    private long lastSaveHistoryTime = -1;

    private boolean needSaveHistory() {
        long curT = SystemClock.elapsedRealtime();
        if (curT - lastSaveHistoryTime >= 3000) {
            lastSaveHistoryTime = curT;
            return true;
        }
        return false;
    }

    @Override
    public void getHistoryList(HttpCallback<List<HistoryItem>> callback) {
        //这个版本是新装的，就不需要兼容以前的版本，如果是升级需要兼容。去掉第一个参数，使用一个参数的就行了。
        mHistoryRequest.getHistoryList( new HttpCallback<List<ListeningHistory>>() {
            @Override
            public void onSuccess(List<ListeningHistory> historyList) {
                Log.d("autoplay", "server historyList is :" + historyList.toString());
                saveHistoryList(historyList, callback);
            }

            @Override
            public void onError(ApiException e) {
                if (callback != null) {
                    callback.onError(e);
                }
            }
        });
    }

    private void saveHistoryList(List<ListeningHistory> historyList, HttpCallback<List<HistoryItem>> callback) {
        runInNewThread(() -> {
            updateLocalHistory(historyList);
            return queryHistoryListOrderByTime(MAX_HISTORY_NUM);
        }, historyItemList -> {
            if (callback != null) {
                if (historyItemList != null) {
                    Log.d("autoplay", "historyItemList is :" + historyItemList.toString());
                    callback.onSuccess(historyItemList);
                } else {
                    callback.onError(new ApiException("历史为空"));
                }
            }
        });
    }

    @Override
    public List<HistoryItem> getBroadcastList() {
        return getBroadcastList(Properties.Type.eq(ResType.TYPE_BROADCAST));
    }

    @Override
    public void queryLatestHistoryByAlbumId(String albumId, GreenDaoInterface.OnQueryListener<HistoryItem> listener) {
        queryLatestHistoryByAlbumId(Properties.RadioId.eq(albumId), listener);
    }

    @Override
    public void destroy() {
        mHistoryRequest.cancel(this);
    }

    @Override
    HistoryItem toHistoryItem(LoginedHistoryItem loginedHistoryItem) {
        return HistoryBeanUtil.toLocalItem(loginedHistoryItem);
    }

    @Override
    List<HistoryItem> toHistoryItemList(List<LoginedHistoryItem> list) {
        ArrayList<HistoryItem> historyItems = new ArrayList<>();
        if (!ListUtil.isEmpty(list)) {
            for (LoginedHistoryItem loginedHistoryItem : list) {
                historyItems.add(toHistoryItem(loginedHistoryItem));
            }
        }
        return historyItems;
    }

    @Override
    LoginedHistoryItem toHistoryEntity(PlayItem playItem) {
        LoginedHistoryItem loginedHistoryItem = new LoginedHistoryItem();
        HistoryBeanUtil.translateToHistoryItem(playItem, loginedHistoryItem);
        return loginedHistoryItem;
    }

    @Override
    protected LoginedHistoryItemDao getDao() {
        return mDaoSession.getLoginedHistoryItemDao();
    }

    @Override
    Property getTimestamp() {
        return Properties.TimeStamp;
    }

    @Override
    public void clear(HttpCallback<Boolean> callback) {
        mHistoryRequest.clearListeningHistory(new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                if (aBoolean) {
                    clearLocal(callback);
                } else {
                    if (callback != null) {
                        callback.onError(new ApiException("清空历史失败"));
                    }
                }
            }

            @Override
            public void onError(ApiException e) {
                if (callback != null) {
                    callback.onError(e);
                }
            }
        });
    }

    public void clearLocal(HttpCallback<Boolean> callback) {
        super.clear(callback);
    }

    public void updateLocalHistory(List<ListeningHistory> listenHistoryData) {
        if (listenHistoryData == null) {
            return;
        }
        Map<String, LoginedHistoryItem> historyItems = HistoryBeanUtil.listeningToLoginedItem(listenHistoryData);
        List<LoginedHistoryItem> localHistoryItems = queryAllSync();
        Log.d("断点续播", "local historyList start is :" + localHistoryItems.toString());
        ListIterator<LoginedHistoryItem> iterator = localHistoryItems.listIterator();
        List<LoginedHistoryItem> deleteList = new ArrayList<>();
        while (iterator.hasNext()) {
            LoginedHistoryItem item = iterator.next();
            String radioId = item.getRadioId();
            LoginedHistoryItem historyItem = historyItems.remove(radioId);
            //网络数据中和本地数据又相同的电台时，并且比本地的时间戳更近，就更新本地数据
            if (historyItem != null) {
                if (historyItem.getTimeStamp() > item.getTimeStamp()) {
                    iterator.remove();
                    iterator.add(historyItem);
                } else {
                    item.setIsOffline(historyItem.getIsOffline());//是否下线更新
                }
            } else {
                deleteList.add(item);
            }
        }
        if (!deleteList.isEmpty()) {
            localHistoryItems.removeAll(deleteList);//本地的不存在在网络中。删除掉。
            mDao.deleteAll();
        }
        //如果网络数据有本地没有的数据就添加
        if (!historyItems.isEmpty()) {
            localHistoryItems.addAll(historyItems.values());
        }
        Log.d("断点续播", "local historyList end is :" + localHistoryItems.toString());
        mDao.insertOrReplaceInTx(localHistoryItems);
    }
}
