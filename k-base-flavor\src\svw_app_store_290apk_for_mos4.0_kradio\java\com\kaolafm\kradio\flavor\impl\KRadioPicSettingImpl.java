package com.kaolafm.kradio.flavor.impl;

import com.kaolafm.kradio.common.utils.FlavorUtil;
import com.kaolafm.kradio.lib.base.flavor.KRadioPicSettingInter;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.UrlUtil;
import com.kaolafm.opensdk.player.logic.PlayerManager;

public final class KRadioPicSettingImpl implements KRadioPicSettingInter {
    private final static String TAG = "KRadioPicSettingImpl";


    @Override
    public String getHomePicUrl(String image) {
        if (!image.contains(":443/"))
            image = FlavorUtil.getHttp443Url(image);
        return UrlUtil.getDefaultConfigPicUrl(image);
    }

    @Override
    public String getAllCategoryPicUrl(String image) {
        if (!image.contains(":443/"))
            image = FlavorUtil.getHttp443Url(image);
        return UrlUtil.getDefaultConfigPicUrl(image);
    }
}
