package com.kaolafm.ad.comprehensive.audioad;

import android.util.Log;

import com.kaolafm.ad.api.model.Advert;
import com.kaolafm.ad.api.model.AudioAdvert;
import com.kaolafm.ad.api.model.ImageAdvert;
import com.kaolafm.ad.expose.AdvertisingLifecycleCallback;
import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.ad.expose.AdvertisingPlayer;
import com.kaolafm.opensdk.http.error.ApiException;

/**
 * @ClassName AudioADPlayer
 * @Description 音频广告播放器
 * <AUTHOR>
 * @Date 2020-03-12 10:42
 * @Version 1.0
 */
public class AudioADPlayer implements AdvertisingPlayer {


    public AudioADPlayer() {
        initAdvertLifecycle();
    }

    @Override
    public void play(AudioAdvert audioAdvert) {
        AudioADFactory.getAdPlayer(audioAdvert.getSubtype()).play(audioAdvert);
    }

    @Override
    public void stop(AudioAdvert audioAdvert) {
        AudioADFactory.getAdPlayer(audioAdvert.getSubtype()).stop(audioAdvert);

    }

    @Override
    public void pause(AudioAdvert audioAdvert) {
        AudioADFactory.getAdPlayer(audioAdvert.getSubtype()).pause(audioAdvert);
    }

    @Override
    public void error(String s, int i, ApiException e) {
        AudioADFactory.getAdPlayer(i).error(s, i, e);
    }

    public void setCallback(int type, Object o) {
        AudioADFactory.getAdPlayer(type).setCallback(o);
    }


    private void initAdvertLifecycle() {
        AdvertisingManager.getInstance().registerAdvertLifecycleCallback(new AdvertisingLifecycleCallback() {
            @Override
            public void onCreate(String s, int i) {
            }

            @Override
            public void onStart(Advert advert) {
                if (advert == null) {
                    return;
                }
                int type = advert.getSubtype();
                if (advert instanceof ImageAdvert) {
                    Log.i("AudioAD", "广告生命周期回调: " + type);
                    AudioADFactory.getAdPlayer(type).error(String.valueOf(advert.getId()), type, null);
                }
            }

            @Override
            public void onClose(Advert advert) {
            }

            @Override
            public void onError(String s, int i, Exception e) {

            }

        });
    }

}
