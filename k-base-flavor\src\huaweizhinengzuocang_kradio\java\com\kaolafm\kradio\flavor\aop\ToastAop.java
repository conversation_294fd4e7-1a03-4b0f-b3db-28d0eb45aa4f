package com.kaolafm.kradio.flavor.aop;

import android.app.Activity;
import android.content.Context;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.utils.IntentUtils;
import com.kaolafm.kradio.lib.utils.ResUtil;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;


@Aspect
public class ToastAop {

    private static final String TAG = "ToastAop";
    static long showtime;
    private static Toast toast;

    @Around("execution(* com.kaolafm.kradio.lib.toast.ToastUtil.showOnly(..))")
    public void toastShowOnActivity(ProceedingJoinPoint point) throws Throwable {
        boolean isAppOnForeground = IntentUtils.getInstance().isAppOnForeground();
        try {
            if (isAppOnForeground) {
                Context context = (Context) (point.getArgs()[0]);
                String msg;
                if (point.getArgs()[1] instanceof String) {
                    Log.i(TAG, "toastShowOnActivity: msg  instanceof String ");
                    msg = (String) (point.getArgs()[1]);
                } else {
                    Log.i(TAG, "toastShowOnActivity: msg  instanceof int ");
                    int resId = (int) (point.getArgs()[1]);
                    msg = ResUtil.getString(resId);
                }

                if (context == null || !(context instanceof Activity)) {
                    context = AppManager.getInstance().getMainActivity();
                }
                if (System.currentTimeMillis() - showtime < 3000) {
                    return;
                }
                if (toast == null) {
                    toast = new Toast(context);
                } else {
                    toast.cancel();
                    toast = null;
                    toast = new Toast(context);
                }
                try {
                    View view = ((LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE)).inflate(R.layout.hw_toast_layout, null, false);

                    TextView text = (TextView) view.findViewById(R.id.toast_msg);
                    text.setText(msg);

                    toast.setView(view);
                    toast.setGravity(Gravity.CENTER, 0, 0);
                    toast.setDuration(Toast.LENGTH_SHORT);
                    toast.show();
                } catch (Exception e) {
                    e.printStackTrace();
                    Toast.makeText(AppDelegate.getInstance().getContext(), msg, Toast.LENGTH_SHORT).show();

                }
                showtime = System.currentTimeMillis();
            }


        } catch (
                Exception e) {
            point.proceed();
            e.printStackTrace();
        }
    }
}
