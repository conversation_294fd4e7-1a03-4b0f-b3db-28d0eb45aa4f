package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.util.Log;

import com.kaolafm.kradio.user.ui.LauncherInter;
import com.kaolafm.kradio.util.DashboardUtil;
import com.kaolafm.kradio.util.FlyScreenUtil;
import com.kaolafm.kradio.util.SteeringWheelUtil;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.IPlayerStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

public class LauncherImpl implements LauncherInter {

    private static final String TAG = "LauncherImpl";

    @Override
    public void onStart(Object... args) {
    }

    @Override
    public void onCreate(Object... args) {
        Activity activity = (Activity) args[0];
        if (activity == null) {
            Log.i(TAG, "activity is null");
            return;
        }
        FlyScreenUtil.init();
        SteeringWheelUtil.init(activity);
        DashboardUtil.getInstance().init(activity);
        PlayerManager.getInstance().addPlayControlStateCallback(new IPlayerStateListener() {
            @Override
            public void onIdle(PlayItem playItem) {

            }

            @Override
            public void onPlayerPreparing(PlayItem playItem) {
                DashboardUtil.getInstance().sendMediaPic(playItem.getPicUrl());
                DashboardUtil.getInstance().sendMediaData(playItem.getTitle()
                        , playItem.getAlbumTitle()
                        , playItem.getDuration() / 1000);
                Log.i(TAG, " 1" + playItem.getTitle() + " 2" + playItem.getSourceName() + " 3" + playItem.getAlbumTitle() + " 4" + playItem.getRadioName() + " 5" + playItem.getSource());
            }

            @Override
            public void onPlayerPlaying(PlayItem playItem) {
                DashboardUtil.getInstance().sendPlayState(DashboardUtil.PLAYING);
            }

            @Override
            public void onPlayerPaused(PlayItem playItem) {
                DashboardUtil.getInstance().sendPlayState(DashboardUtil.PAUSED);
            }

            @Override
            public void onProgress(PlayItem playItem, long l, long l1) {
                DashboardUtil.getInstance().sendPlayState(DashboardUtil.PLAYING, (double) ((double) l / (double) l1));
            }

            @Override
            public void onPlayerFailed(PlayItem playItem, int i, int i1) {
                DashboardUtil.getInstance().sendPlayState(DashboardUtil.ERROR);
            }

            @Override
            public void onPlayerEnd(PlayItem playItem) {

            }

            @Override
            public void onSeekStart(PlayItem playItem) {

            }

            @Override
            public void onSeekComplete(PlayItem playItem) {

            }

            @Override
            public void onBufferingStart(PlayItem playItem) {
                DashboardUtil.getInstance().sendPlayState(DashboardUtil.BUFFER, 0);
            }

            @Override
            public void onBufferingEnd(PlayItem playItem) {

            }

            @Override
            public void onDownloadProgress(PlayItem playItem, long l, long l1) {
            }
        });
    }

    @Override
    public void onResume(Object... args) {

    }

    @Override
    public void onPause(Object... args) {

    }

    @Override
    public void onStop(Object... args) {

    }

    @Override
    public void onRestart(Object... args) {

    }

    @Override
    public void onDestory(Object... args) {

    }
}
