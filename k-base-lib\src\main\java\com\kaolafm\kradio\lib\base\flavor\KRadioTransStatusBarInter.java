package com.kaolafm.kradio.lib.base.flavor;

import android.app.Activity;
import androidx.annotation.ColorRes;
import android.view.View;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-03-11 20:36
 ******************************************/
public interface KRadioTransStatusBarInter {
    /**
     * 改变当前Activity状态栏颜色
     *
     * @param activity
     * @param colorRes
     * @return true 已处理，false 未处理
     */
    boolean changeStatusBarColor(Activity activity, @ColorRes int colorRes);

    /**
     * 调整当前View布局以适应沉浸模式
     *
     * @param view
     * @param id
     * @return true 已处理，false 未处理
     */
    boolean changeViewLayoutForStatusBar(View view, int id);

    /**
     * 是否可调整首页布局边距以适应沉浸式状态栏
     *
     * @param args
     * @return
     */
    boolean canChangeViewLayoutForStatusBar(Object... args);

    /**
     * 获取状态栏定制高度
     * @param args
     * @return
     */
    int getStatusBarHeight(Object... args);
}
