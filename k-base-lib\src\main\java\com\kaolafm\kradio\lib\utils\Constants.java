package com.kaolafm.kradio.lib.utils;



/******************************************
 * 类描述： 常量类 类名称：Constants
 *
 * @version: 1.0
 * @author: shaoning<PERSON>ang
 * @time: 2016-7-19 10:25
 ******************************************/
public final class Constants {
    public static final String START_TAG = "app_start";

    private static final boolean IS_DEBUG = false;
    /**
     * app静默模式
     */
    public static  boolean switchGreyStyle = false;
    /**
     * SDK 默认appId
     */
    public static final String APP_ID = "1000420";
    /**
     * 专辑在线状态
     */
    public static final int STATUS_ONLINE = 1;
    /**
     * 无效值
     */
    public static final int INVALID_NUM = -1;

    public static final String BLANK_STR = "";
    public static final String COLON_STR = ":";

    public static final String LOGIN_TYPE_QR = "1";//扫码登录
    public static final String LOGIN_TYPE_PHONE = "2";//手机号登录

    public static final int SUBSCRIBE_INDEX = 0; //我的-收藏下标
    public static final int HISTORY_INDEX = 1; //我的-历史下标
    public static final int PURCHASED_INDEX = 2; //我的-已购下标
    public static final int PLAYER_ITEM_CLICK = 100; //播放页面点击item跳转到登录页面
    public static final int PLAYER_ITEM_CAROUSEL = 101; //试听碎片轮播至需付费碎片——判断登录
    public static final int HOME_TO_LOGIN = 102;//从首页点击我的到登录页面

    /**
     * 播放器播放上报自用默认标识
     */
    public static final String PLAYER_DEFAULT_REMARKS = "player";

    /**
     * 有下一页或上一页
     */
    public static final int HAVE_PAGE = 1;

    /**
     * 无效节目id
     */
    public final static int INVALID_ID = -1;

    /**
     * 正序
     */
    public final static int ORDER_MODE_POSITIVE = 0;
    /**
     * 倒序
     */
    public final static int ORDER_MODE_REVERSE = -1;

    /**********************************
     * http请求通用关键字
     **********************************************/
    public static final String KEY_SIGN = "sign";
    public static final String KEY_OPEN_ID = "openid";
    public static final String KEY_APP_ID = "appid";
    public static final String KEY_DEVICE_ID = "deviceid";
    public static final String KEY_OS = "os";
    public static final String KEY_CHANNEL = "channel";
    public static final String KEY_CHILD_CHANNEL = "child_channel";
    public static final String KEY_PACKAGE = "packagename";
    public static final String OS_NAME = "android";
    public static final String KEY_USER_ID = "uid";
    public static final String KEY_VERSION = "version";
    public static final String KEY_APP_TYPE = "app_type";
    public static final String KEY_CAR_TYPE = "car_type";
    public static final String KEY_ACCESS_TOKEN = "access_token";
    public static final String KEY_TOKEN = "token";
    /**
     * 时间戳
     */
    public static final String KEY_TIMESTAMP = "timestamp";

    /**
     * 随机数
     */
    public static final String KEY_NONCE = "nonce";

    /**
     * KRadio userid
     */
    public static final String KEY_KRADIO_USER_ID = "kradioUid";

    public static final String LAT = "lat";

    public static final String LNG = "lng";

//    /**********************************
//     * http请求URL
//     **********************************************/
//    //  private static final String GENERAL_HOST_URL = "http://*************:9090/";
//    private static final String GENERAL_HOST_URL = "http://" + Constant.OPEN_KAOLA_HOST; //"http://*************";
//    private static final String GENERAL_HOST_REPORT_URL = "http://" + Constant.OPEN_KAOLA_HOST + BuildConfig.NET_REQUEST_VERSION + "/"; //"http://***********:9531/v3/";
//    //   private static final String GENERAL_HOST_REPORT_URL = "http://*************:9090/" + "v2/"; //"http://***********:9531/";
//    public static final String KAOLA_GENERAL_BASE = GENERAL_HOST_URL + BuildConfig.NET_REQUEST_VERSION + "/";
//    private static final String KAOLA_STATISTICS_BASE_URI = "http://iovmsg.radio.cn/";
//    /**
//     * 推荐使用域名
//     */
//    public static final String KAOLA_RECOMMEND_BASE_URL = "http://api.rec.kaolafm.com/";


//    /**
//     * 常规数据上报接口
//     */
//    public static final String REQUEST_STATISTICS_COMMON = KAOLA_STATISTICS_BASE_URI
//            + "k.gif?%s";
//    /**
//     * 异常数据上报接口
//     */
//    public static final String REQUEST_STATISTICS_ERROR = KAOLA_STATISTICS_BASE_URI
//            + "e.gif";
//    /**
//     * 专辑或碎片消费上报
//     */
//    public static final String REQUEST_UPLOAD_ALBUM_AUDIO_CONSUME = GENERAL_HOST_REPORT_URL
//            + "user/hisrecord";
//    /**
//     * 获取openId接口
//     */
//    public static final String REQUEST_OPEN_ID = KAOLA_GENERAL_BASE + "app/init";
//    public static final String REQUEST_APP_CHECK_VOLUME_BALANCE = KAOLA_GENERAL_BASE + "config/switchs";
//    /**
//     * 激活APP接口
//     */
//    public static final String REQUEST_ACTIVE_APP = KAOLA_GENERAL_BASE + "app/active";
//    /**
//     * 获取节目库一级分类接口
//     */
//    public static final String REQUEST_CATEGORY = KAOLA_GENERAL_BASE + "category";
//    /**
//     * 获取节目库一级分类接口
//     */
//    public static final String REQUEST_NEWS_CATEGORY = KAOLA_GENERAL_BASE + "news/category";
//    /**
//     * 获取节目库二级分类接口
//     */
//    public static final String REQUEST_SUB_CATEGORY = KAOLA_GENERAL_BASE + "category/sublist?cid=%d";
//    /**
//     * 获取碎片详情接口
//     */
//    public static final String REQUEST_AUDIO_INFO = KAOLA_GENERAL_BASE
//            + "audio/detail?id=%d";
//    /**
//     * 获取一组碎片详情接口
//     */
//    public static final String REQUEST_AUDIOS_INFO = KAOLA_GENERAL_BASE
//            + "audio/details?ids=%s";
//    /**
//     * 获取PGC详情接口
//     */
//    public static final String REQUEST_SMART_RADIO_DETAIL_INFO = KAOLA_GENERAL_BASE
//            + "radio/detail?rid=%d";
//    /**
//     * 获取专辑详情接口
//     * 备注：此接口还有一个attributes(属性名称，多个需要 ","分隔，例如 "name,desc") 非必须字段目前没有传
//     */
//    public static final String REQUEST_RADIO_INFO = KAOLA_GENERAL_BASE
//            + "album/detail?ids=%s";
//    /**
//     * 获取PGC播单接口
//     */
////    public static final String REQUEST_PGC_PLAYLIST = KAOLA_GENERAL_BASE
////            + "radio/list?rid=%d&clockid=%s";
//    /**
//     * 获取PGC播单接口
//     */
////    public static final String REQUEST_ONE_BUTTON_LISTEN_PGC_PLAYLIST = KAOLA_GENERAL_BASE
////            + "kradio/user/followradio?clockid=%s";
//    /**
//     * 获取专辑播单接口
//     */
//    public static final String REQUEST_ALBUM_PLAYLIST_V4 = GENERAL_HOST_REPORT_URL
//            + "audio/list?aid=%d&sorttype=%d&pagesize=%d&pagenum=%d";
//    /**
//     * 获取指定碎片所在专辑播单接口
//     */
//    public static final String REQUEST_AUDIO_PLAYLIST_V4 = GENERAL_HOST_REPORT_URL
//            + "audio/list?aid=%d&audioid=%d&sorttype=%d&pagesize=%d";
//
//    /**
//     * 获取特定分类下专辑列表接口
//     */
//    public static final String REQUEST_CTG_ALBUM_LIST = KAOLA_GENERAL_BASE
//            + "album/list?cid=%d&sorttype=%d&pagenum=%d&pagesize=%d";
//    /**
//     * 获取相关专辑列表接口
//     */
//    public static final String REQUEST_RELATED_ALBUM_LIST = KAOLA_GENERAL_BASE + "album/related?aid=%d&length=%d";
//    /**
//     * 获取推荐列表接口
//     */
//    public static final String REQUEST_RECOMMEND_LIST = KAOLA_GENERAL_BASE + "operate?pagenum=%d&pagesize=%d&lon=%s&lat=%s";
//    /**
//     * 获取PGC推荐列表接口 2.4.0新增
//     */
//    public static final String REQUEST_RECOMMEND_PGC_LIST = KAOLA_GENERAL_BASE + "radioLable/list?pagenum=%d&pagesize=%d&lon=%s&lat=%s";
//    /**
//     * 获取精选列表接口
//     */
//    public static final String REQUEST_SELECTION_LIST = KAOLA_GENERAL_BASE + "carlabelinfo/get";
//    /**
//     * 获取电台分类接口
//     */
//    public static final String REQUEST_PGC_CTG_LIST = KAOLA_GENERAL_BASE + "category/radio";
//    /**
//     * 获取特定分类下PGC列表接口
//     */
//    public static final String REQUEST_CTG_PGC_LIST = KAOLA_GENERAL_BASE + "typeradio/list?cid=%d";
//    /**
//     * 获取热播榜单接口
//     */
//    public static final String REQUEST_HOT_PLAY_ALBUM_LIST = KAOLA_GENERAL_BASE + "album/top?type=%s&pagenum=%d&pagesize=%d";
//    /**
//     * 获取传统电台列表接口
//     */
//    public static final String REQUEST_BROADCAST_LIST = KAOLA_GENERAL_BASE + "broadcast/list?type=%d&classifyid=%s&pagenum=%d&pagesize=%d&area=%d";
//    /**
//     * 获取传统电台详情接口
//     */
//    public static final String REQUEST_BROADCAST_DETAIL = KAOLA_GENERAL_BASE + "broadcast/detail?bid=%d";
//    /**
//     * 获取当前电台节目单列表接口
//     */
//    public static final String REQUEST_BROADCAST_PROGRAM_LIST = KAOLA_GENERAL_BASE + "broadcast/programlist?bid=%d&date=%s";
//    /**
//     * 获取当前电台对应单个节目单接口
//     */
//    public static final String REQUEST_BROADCAST_CURRENT_PROGRAM = KAOLA_GENERAL_BASE + "broadcast/currentprogram?bid=%d";
//    /**
//     * 获取电台节目详情接口
//     */
//    public static final String REQUEST_BROADCAST_PROGRAM_DETAIL = KAOLA_GENERAL_BASE + "program/detail?programid=%s";
//    /**
//     * 订阅专辑，PGC，电台接口
//     */
//    public static final String REQUEST_SUBSCRIBE = KAOLA_GENERAL_BASE + "subscribe?id=%d";
//    /**
//     * 取消订阅专辑，PGC，电台接口
//     */
//    public static final String REQUEST_UN_SUBSCRIBE = KAOLA_GENERAL_BASE + "unsubscribe?id=%d";
//    /**
//     * 检测专辑，PGC，电台是否订阅过接口
//     */
//    public static final String REQUEST_IS_SUBSCRIBE = KAOLA_GENERAL_BASE + "issubscribe?id=%d";
//    /**
//     * 获取专辑，PGC，电台订阅列表接口
//     */
//    public static final String REQUEST_SUBSCRIBE_LIST = KAOLA_GENERAL_BASE + "subscribe/list?type=%d&pagenum=%d&pagesize=%d";
//
//    /**
//     * 获取传统广播电台分类接口
//     */
//    public static final String REQUEST_BROADCAST_CTG = KAOLA_GENERAL_BASE + "category/broadcast?type=%d";
//
//    public static final String REQUEST_BROADCAST_WITHOUT_PGC = KAOLA_GENERAL_BASE + "category/broadcast/withoutPGC?type=%d";
//
//    public static final String REQUEST_BROADCAST_WITHOUT_AREASORT = KAOLA_GENERAL_BASE + "category/broadcast/withoutPGC?type=%d&getAreaSort=false";
//    /**
//     * 获取传统广播电台分类接口
//     */
//    public static final String REQUEST_BROADCAST_CTG1 = KAOLA_GENERAL_BASE + "category/broadcast";
//
//    /**
//     * 获取地区列表接口（传统广播相关）
//     */
//    public static final String REQUEST_BROADCAST_AREA = KAOLA_GENERAL_BASE + "broadcast/arealist";
//
//    /**
//     * 获取传统广播日期列表接口
//     */
//    public static final String REQUEST_BROADCAST_DATE = KAOLA_GENERAL_BASE + "broadcast/date";
//
//    /**
//     * 获取猜你喜欢
//     */
//    public static final String REQUEST_GUESS_U_LIKE = KAOLA_GENERAL_BASE + "guess";
//
//    /**
//     * 获取语音搜索结果
//     */
//    public static final String REQUEST_VOICE_SEARCH = KAOLA_GENERAL_BASE + "resource/searchall?q=%s";
//    /**
//     * 获取按照分类和关键字搜索结果
//     */
//    public static final String REQUEST_VOICE_SEARCH_CTG = KAOLA_GENERAL_BASE + "resource/searchtype?q=%s&rtype=%s&pagenum=%d&pagesize=%d";
//
//    /**
//     * app校验升级接口
//     */
//    public static final String REQUEST_APP_UPDATE = KAOLA_GENERAL_BASE + "app/version?version=%s";
//    /**
//     * 检测节目库左上角搜索入口是否可用
//     */
//    public static final String REQUEST_CHECK_SEARCH_CAN_USE = KAOLA_GENERAL_BASE + "isshow";
//    /**
//     * 通过经纬度获取当前城市id和name
//     */
//    public static final String REQUEST_CITY_AREA = KAOLA_GENERAL_BASE + "broadcast/getarea?lat=%s&lon=%s";
//
//    /**
//     * 用户登录
//     */
//    public static final String REQUEST_USER_LOGIN = KAOLA_GENERAL_BASE + "user/login";
//
//    /**
//     * 关键字搜索接口 搜索全部
//     */
//    public final static String REQUEST_KEYWORDS_SEARCH_ALL = KAOLA_GENERAL_BASE +
//            "resource/searchall?";
//
//    /**
//     * 搜索接口 搜索建议
//     * 描述
//     * word	String	是	用户输入的搜索词
//     * limit	int 	否 	显示数量，默认10
//     */
//    public final static String REQUEST_SUGGESTION = KAOLA_GENERAL_BASE
//            + "resource/suggestionword?word=%s&limit=%s";
//
//    /**
//     * 语义搜索
//     */
//    public static final String REQUEST_VOICE_SEARCH_SEMANTICS = "http://" + Constant.OPEN_KAOLA_HOST + "/v3/resource/voice/searchall?deviceid=%s&sign=%s&appid=%s&voicesource=%s&appsource=%s&qualitytype=%s&q=%s";
//
//    /**
//     * 请求生成用户绑定的二维码
//     */
//    public static final String REQUEST_USER_BOUND_CREATE_QR = KAOLA_GENERAL_BASE + "auth/getQRCode";
//
//    /**
//     * 请求查询用户绑定状态
//     */
//    public static final String REQUEST_QUERY_USER_BOUND_STATE = KAOLA_GENERAL_BASE + "auth/hasBeenBound?uuid=%s";
//    /**
//     * 用户解绑
//     */
//    public static final String REQUEST_USER_UNBOUND = KAOLA_GENERAL_BASE + "auth/unbind";
//
//    /**
//     * 获取听友群ur
//     */
//    public static final String REQUEST_LISTENER_GROUP = KAOLA_GENERAL_BASE + "group/QRCode";
//
//    /**
//     * 一键激活，获取感兴趣的节目列表
//     */
//    public static final String REQUEST_GET_INTERESTED_PROGRAMS = KAOLA_GENERAL_BASE + "label/interest/list";
//
//    /**
//     * 一键激活，提交一键激活信息
//     */
//    public static final String REQUEST_ACTIVATE_INFO = KAOLA_GENERAL_BASE + "label/user/bind";
//    public static final String REQUEST_ACTIVATE_INFO_URL = REQUEST_ACTIVATE_INFO + "?gender=%d&labels=%s";
//
//    /**
//     * 获取收藏中一键收听播放列表
//     */
//    public static final String REQUEST_GET_ONEKEY_LISTEN_LIST = KAOLA_GENERAL_BASE + "subscribe/flow";
//    public static final String REQUEST_GET_ONEKEY_LISTEN_LIST_URL = REQUEST_GET_ONEKEY_LISTEN_LIST + "?lastValidDate=%d&pagesize=%d";
//    /**
//     * 获取收藏中一键收听播放id
//     */
//    public static final String REQUEST_GET_ONEKEY_LISTEN_ID = KAOLA_GENERAL_BASE + "subscribe/flow/radioId";
//
//    /**
//     * 获取顶部tab接口
//     */
//    public static final String REQUEST_OPERATE_FRAME = KAOLA_GENERAL_BASE /*"http://192.168.4.175:9090/v2/" */ + "operateFrame";
//    /**
//     * 根据运营位id获取运营位信息接
//     */
//    public static final String REQUEST_CAR_LABEL_INFO = KAOLA_GENERAL_BASE /*"http://192.168.4.175:9090/v2/" */ + "carlabelinfo/get?pageId=%s&cartype=%s&recjson=%s";
//
//    /**
//     * 场景
//     */
//    public static final String REQUEST_SCENES = KAOLA_GENERAL_BASE /*"http://192.168.4.175:9090/v2/" */ + "categorycontent/list?cid=%s";


    /**
     * -------------------- pageid
     */

    /**
     * 首页
     */
    public static final String PAGE_ID_MAIN = "110000";
    /**
     * 品牌电台页
     */
    public static final String PAGE_ID_BRAND_PAGE = "160004";
    /**
     * 话题详情
     */
    public static final String PAGE_ID_TOPIC_DATEILS = "160009";
    /**
     * 全部分类 （290之后改成了全部分类页面，之前是 全部分类->我的）
     */
    public static final String PAGE_ID_CATEGORY = "120000";
    /**
     * 我的-我的订阅 （290之后改成了订阅，之前是历史）
     */
    public static final String PAGE_ID_MINE_SUBSCRIBE = "122110";
    /**
     * 全部分类-扫码登录
     */
    public static final String PAGE_ID_CLASSIFY_MINE_QR_CODE = "122121";
    /**
     * 全部分类-解除绑定
     */
    public static final String PAGE_ID_CLASSIFY_MINE_UNBIND = "122122";
    /**
     * 全部分类-头条
     */
    public static final String PAGE_ID_CLASSIFY_HEADLINE = "122200";
    /**
     * 全部分类-综艺
     */
    public static final String PAGE_ID_CLASSIFY_VARIETY = "122300";
    /**
     * 全部分类-专辑
     */
    public static final String PAGE_ID_CLASSIFY_ALBUM = "122400";
    /**
     * 全部分类-在线广播-本地
     */
    public static final String PAGE_ID_CLASSIFY_BROADCAST_LOCAL = "123100";
    /**
     * 全部分类-在线广播-分类
     */
    public static final String PAGE_ID_CLASSIFY_BROADCAST_CLASSIFY = "123200";

    /**
     * 全部分类页面
     * 从212版本开始全部分类页面使用同一个PageId，不再区分资源类型
     */
    public static final String COMPREHENSIVE_PAGE_ID_ALL_CATEGORIES = "120000";
    /**
     * 账户-我的页面
     */
    public static final String PAGE_ID_ACCOUNT_MAIN = "130000";
    /**
     * 账户-账号登录
     */
    public static final String PAGE_ID_ACCOUNT_LOGIN = "131100";
    /**
     * 账户-退出登录
     */
    public static final String PAGE_ID_ACCOUNT_LOGOUT = "131200";
    /**
     * 账户-关于我们
     */
    public static final String PAGE_ID_ACCOUNT_ABOUT_US = "160010";
//    public static final String PAGE_ID_ACCOUNT_ABOUT_US = "131300";
    /**
     * 账户-服务协议
     */
    public static final String PAGE_ID_ACCOUNT_PROTOCOL = "131310";
    /**
     * 账户-隐藏政策
     */
    public static final String PAGE_ID_ACCOUNT_POLICY = "131311";
    /**
     * 账户-意见反馈
     */
    public static final String PAGE_ID_ACCOUNT_OPINION = "134000";
    /**
     * 播放器-音乐播放器
     */
    public static final String PAGE_ID_PLAYER_MUSIC_MAIN = "141000";
    /**
     * 播放器-歌词
     */
    public static final String PAGE_ID_PLAYER_MUSIC_LYRIC = "141100";
    /**
     * 播放器-播单
     */
    public static final String PAGE_ID_PLAYER_MUSIC_LIST = "141200";
    /**
     * 播放器-专辑
     */
    public static final String PAGE_ID_PLAYER_ALBUM_MAIN = "142000";
    /**
     * 播放器-广播
     */
    public static final String PAGE_ID_BROADCAST_MAIN = "143000";
    /**
     * 播放器-明天播单
     */
    public static final String PAGE_ID_BROADCAST_LIST_TOMORROW = "143100";
    /**
     * 播放器-今天播单
     */
    public static final String PAGE_ID_BROADCAST_LIST_TODAY = "143200";
    /**
     * 播放器-昨天播单
     */
    public static final String PAGE_ID_BROADCAST_LIST_YESTERDAY = "143300";
    /**
     * 直播
     */
    public static final String PAGE_ID_PLAYER_LIVING = "151000";

    /**
     * 搜索
     */
    public static final String PAGE_ID_SEARCH = "152000";

    /**
     * 搜索结果
     */
    public static final String PAGE_ID_SEARCH_RESULT = "153000";

    //290 增加的pageid
    /**
     * 云听使用提示页面
     */
    public static final String PAGE_ID_USE_TIPS = "131300";
    /**
     * 我的-我的历史
     */
    public static final String PAGE_ID_MINE_HISTRORY = "122111";
    /**
     * 我的-已购
     */
    public static final String PAGE_ID_MINE_PURCHASED = "122112";
    /**
     * 我的-个人中心
     */
    public static final String PAGE_ID_MINE_USER = "122113";
    /**
     * 登录
     */
    public static final String PAGE_ID_LOGIN = "160006";
    /**
     * 我的-设置
     */
    public static final String PAGE_ID_MINE_SETTING = "122114";
    /**
     * 我的-个人中心-我的订单
     */
    public static final String PAGE_ID_MINE_USER_MY_ORDER = "122115";
    /**
     * 支付弹窗 弃用
     */
//    public static final String PAGE_ID_PAY_VIP = "160000";
    /**
     * 专辑购买页面
     */
//    public static final String PAGE_ID_PAY_ALBUM = "160001";
    /**
     * 碎片购买页面
     */
//    public static final String PAGE_ID_PAY_AUDIO = "160002";
    /**
     * 活动页面
     */
    public static final String PAGE_ID_ACTIVITY = "160003";
    /**
     * 消息盒子页面
     */
    public static final String PAGE_ID_MESSAGE = "160005";
    //  end 290pageid
    //-----------------------------------在线电台新增  start  -----------------------------------------
    /**
     * 服务协议
     */
    public static final String ONLINE_PAGE_ID_ACCOUNT_PROTOCOL = "200020";
    /**
     * 隐私政策
     */
    public static final String ONLINE_PAGE_ID_ACCOUNT_PRIVATE = "200030";
    /**
     * 云听使用提示页面
     */
    public static final String ONLINE_PAGE_ID_USE_TIPS = "200010";
    /**
     * 活动页面
     */
    public static final String ONLINE_PAGE_ID_ACTIVITY = "240010";
    /**
     * 活动详情
     */
    public static final String ONLINE_PAGE_ID_ACTIVITY_DATEILS = "240020";
    /**
     * 登录页面
     */
    public static final String ONLINE_PAGE_ID_LOGIN = "250016";
    /**
     * 用户中心-设置
     */
    public static final String ONLINE_PAGE_ID_MINE_SETTING = "250040";
    /**
     * 用户中心-已购
     */
    public static final String ONLINE_PAGE_ID_MINE_PURCHASED = "250030";
    /**
     * 用户中心-关于
     */
    public static final String ONLINE_PAGE_ID_MINE_ABOUT = "250050";
    /**
     * 用户中心—已购—我的订单
     */
    public static final String ONLINE_PAGE_ID_MINE_PURCHASED_MY = "250011";
    /**
     * 用户中心--会员中心
     */
    public static final String PAGE_ID_MINE_MEMBERS = "250010";
    /**
     * 用户中心--收听时长页面
     */
    public static final String PAGE_ID_MINE_DURATION = "250020";
    /**
     * 用户中心--关于云听页面
     */
    public static final String PAGE_ID_MINE_ABOUT = "250050";

    /**
     * 支付弹窗
     */
    public static final String ONLINE_PAGE_ID_PAY_VIP = "250012";
    /**
     * 专辑购买页面
     */
    public static final String ONLINE_PAGE_ID_PAY_ALBUM = "250013";
    /**
     * 碎片购买页面-单集购买
     */
    public static final String ONLINE_PAGE_ID_PAY_AUDIO = "250014";
    /**
     * 碎片购买页面-多集购买
     */
    public static final String ONLINE_PAGE_ID_PAY_AUDIO_LIST = "250015";

    /**
     * 地理位置页面
     */
    public static final String PAGE_ID_LOCATION = "255010";
    /**
     * 搜索主页面
     */
    public static final String ONLINE_PAGE_ID_SEARCH = "260010";

    /**
     * 搜索结果页面
     */
    public static final String ONLINE_PAGE_ID_SEARCH_RESULT = "260020";
    /**
     * 猜你喜欢
     */
    public static final String PAGE_ID_HOME_LIKE = "210010";
    /**
     * 分类--广播tab页面
     */
    public static final String PAGE_ID_CATEGORIES_BROADCAST = "220010";
    /**
     * 分类--电视tab页面
     */
    public static final String PAGE_ID_CATEGORIES_TV = "220020";
    /**
     * 分类--AI电台tab页面
     */
    public static final String PAGE_ID_CATEGORIES_AI = "220030";
    /**
     * 分类--专辑tab页面
     */
    public static final String PAGE_ID_CATEGORIES_ALBUM = "220040";
    /**
     * 听迹--我的订阅--专辑/AI电台tab页面
     */
    public static final String PAGE_ID_LISTENING_TRACE_SUBSCRIBE_ALBUM = "230010";
    /**
     * 听迹--我的订阅--单曲tab页面
     */
    public static final String PAGE_ID_LISTENING_TRACE_SUBSCRIBE_SONG = "230011";
    /**
     * 听迹--我的订阅--广播/电视tab页面
     */
    public static final String PAGE_ID_LISTENING_TRACE_SUBSCRIBE_TV = "230012";
    /**
     * 听迹--收听历史页面
     */
    public static final String PAGE_ID_LISTENING_TRACE_HISTORY = "230020";
    /**
     * 播放详情页--广播/电视播放详情页
     */
    public static final String PAGE_ID_PLAY_TV = "280020";
    /**
     * 播放详情页-专辑/AI电台
     */
    public static final String ONLINE_PAGE_ID_PLAYER_ALBUM = "280010";
    /**
     * 播放详情页--直播间详情页
     */
    public static final String PAGE_ID_LIVE_ROOM = "160008";
    public static final String PAGE_ID_VIDEO = "1600010";


    /**
     * 消息卡片页面
     */
    public static final String PAGE_ID_MESSAGE_CARD = "270020";
    /**
     * 消息详情页面
     */
    public static final String PAGE_ID_MESSAGE_DETAILS = "270030";
    /**
     * 消息盒子页面
     */
    public static final String ONLINE_PAGE_ID_MESSAGE = "270010";


    //-----------------------------------在线电台新增  end  -----------------------------------------


    /**
     * 用于外调sdk 跳转某个页面（比如跳转全部分类、个人中心）
     */
    public static final String START_PAGE = "start_page";

    public static final int START_PAGE_LOGIN = 0;


    /**
     * 语音外调SDK启动APP携带的extra类型
     */
    public static final String CLIENT_EXTRA_TYPE = "client_extra_type";
    /**
     * 播放关键字类型
     */
    public static final int SEARCH_BY_KEYWORDS_EXTRA_TYPE = 1;

    /**
     * APP Intent ACTION widget使用
     */
    public static final String APPEXIT_ACTION = "com.kaolafm.auto.home.appExit.action";


    /**
     * 智能电台子分类-个推
     */
    public static final int AI_RADIO_CONTENT_TYPE_RECOMMEND = 5;


    /**
     * 广告位ID
     */
    public static final int SPLASH_AD_ID = 179;
    public static final int PLAYER_IMAGE_AD_ID = 180;
    public static final long AD_ZONE_ID = 181;


    //直播入流开播通知
    public static final int NOTIFY_TYPE_LIVE = 1;
    //场景推送通知
    public static final int NOTIFY_TYPE_SCENE = 2;

//    public static final String HTTP_URL_SERVICE_AGREEMENT = "https://m.kaolafm.com/location/serverAgreement.html?appid=";
//    public static final String HTTP_URL_HIDDEN_POLICY = "https://m.kaolafm.com/location/policy.html?appid=";

//    public static final String HTTP_URL_SERVICE_AGREEMENT = "https://iovm.radio.cn/location/ytServerAgreement.html?appid=";
//    public static final String HTTP_URL_HIDDEN_POLICY = "https://iovm.radio.cn/location/ytPolicy.html?appid=";

    public static final String USER_PROMPT_FIRST_SP_FILE_NAME = "first.sp";
    public static final String USER_PROMPT_FIRST_SP_ITEM_VALUE = "isFirst";
    public static final String USER_PROMPT_AGREE_SP_FILE_NAME = "userAgree.sp";
    public static final String USER_PROMPT_AGREE = "userAgree";

    public static long HubActivityStartTime = -1;


    /*-------------------------------------------  ARouter应用内跳转用到的Key -------------------------------------------*/
    public static final String ROUTER_IDENTIFICATION = "app://kradiorouter.com";
    public static final String ROUTER_PARAMS_KEY_WEB_URL = "AROUTER_PARAMS_KEY_WEB_URL";    //应用内跳转 WebViewActivity url
    public static final String ROUTER_PARAMS_KEY_WEB_TITLE = "ROUTER_PARAMS_KEY_WEB_TITLE";  //应用内跳转 WebViewActivity title
    public static final String ROUTER_PARAMS_KEY_WEB_PAGE_ID = "ROUTER_PARAMS_KEY_WEB_PAGE_ID";  //应用内跳转 WebViewActivity pageid,告诉WebViewActivity使用本pageId
    public static final String ROUTER_PARAMS_KEY_PAGE_ID = "pageId";    //应用内跳转指令参数发送来的pageId
    public static final String ROUTER_PARAMS_KEY_RESOURCE_ID = "id";    //应用内跳转指令参数发送来的资源id。对于媒体资源，对应媒资后台id，对于活动，对应活动id
    public static final String ROUTER_PARAMS_KEY_RESOURCE_TYPE = "type";    //应用内跳转指令参数发送来的资源type
    public static final String ROUTER_PARAMS_KEY_RESOURCE_URL = "url";    //应用内跳转指令参数发送来的资源url
    public static final String ROUTER_PARAMS_KEY_SEARCH_KEYWORD = "searchKeyword";  //应用内跳转指令参数发送来的搜索关键词
    public static final String ROUTER_PARAMS_KEY_SEARCH_WAY = "searchWay";  //应用内跳转指令参数发送来的搜索方式
    public static final String ROUTER_PARAMS_KEY_EXTRA = "extra";   //应用内跳转指令参数发送来的额外数据
    public static final String ROUTER_PARAMS_KEY_SELECTED_CODES = "selectedCodes";   //应用内跳转多级code
    public static final String ROUTER_PARAMS_VALUE_SPLIT = ",";   // 指定多级code时,多个code中间用英文逗号[,]隔开

    public static final String SP_IS_CLICK_HUB_AGREE = "sp_is_click_hub_agree";   // 标识是否是从 HubActivity 页点击同意协议按钮进入到的 LauncherActivity
    public static final String SP_POLICY_VERSION = "sp_policy_version";   //当前的app隐私协议版本
    public static final String SP_AGREEMENT_VERSION = "sp_agreement_version";   //当前的app服务协议版本
    public static final String ARGUMENT_TOPIC_ID = "ARGUMENT_TOPIC_ID";   //话题详情id
    public static final String PAGE_POSITION = "page_position";   //个人中心各页面
}
