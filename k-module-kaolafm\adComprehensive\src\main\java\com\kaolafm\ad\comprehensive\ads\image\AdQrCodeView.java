package com.kaolafm.ad.comprehensive.ads.image;

import android.animation.Animator;
import android.content.Context;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.ad.api.model.InteractionAdvert;
import com.kaolafm.ad.comprehensive.ads.image.base.BaseInteractContentView;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.util.ReportConstants;
import com.kaolafm.report.util.ReportParameterManager;

public class AdQrCodeView extends BaseInteractContentView {
    private ImageView mTvAdClose;
    private TextView mTvAdMsg;

    public AdQrCodeView(Context context) {
        super(context);
        init(context);
    }

    public AdQrCodeView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public AdQrCodeView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    @Override
    public void loadAdContent(InteractionAdvert advert) {
        super.loadAdContent(advert);
        if (StringUtil.isEmpty(advert.getDescription())) {
            ViewUtil.setViewVisibility(mTvAdMsg, View.INVISIBLE);
        } else {
            ViewUtil.setViewVisibility(mTvAdMsg, View.VISIBLE);
            mTvAdMsg.setText(StringUtil.getMaxSubstring(advert.getDescription(), 7));
        }
    }

    @Override
    public void countdownToCloseAd() {
        if (mDuration != 0) {
            super.countdownToCloseAd();
        }
    }

    private void init(Context context) {
        LayoutInflater.from(context).inflate(R.layout.ad_qr_code_view_layout, this, true);
        mImageView = findViewById(R.id.iv_ad_qr);
        mTvAdMsg = findViewById(R.id.tv_ad_msg);
        mTvAdClose = findViewById(R.id.tv_ad_close);
        mTvAdClose.setOnClickListener((v) -> {
            cancelClose();
            hide();
            ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_AD_INTERACTION_CLOSE, "", ReportParameterManager.getInstance().getPage()
                    , ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_ADVERT, null, null, null, String.valueOf(interactionAdvert.getId())));
        });
        this.setVisibility(INVISIBLE);
    }

    @Override
    public void show() {
        super.show();
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_AD_INTERACTION_CLOSE, "", ReportParameterManager.getInstance().getPage()
                , ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_ADVERT, null, null, null, String.valueOf(interactionAdvert.getId())));
    }

    @Override
    public void hide() {
        super.hide(new HideAnimationListener(this) {
            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
                mAdImageListener.onAdSkip();
            }
        });
    }
}
