<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="FragmentBackButton">
        <item name="android:id">@id/backView</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:layout_width">@dimen/m36</item>
        <item name="android:layout_height">@dimen/m36</item>
        <item name="android:scaleType">centerInside</item>
        <item name="android:src">@drawable/base_back</item>
        <item name="layout_constraintLeft_toLeftOf">parent</item>
        <item name="layout_constraintTop_toTopOf">parent</item>
        <item name="android:contentDescription">@string/content_desc_back</item>
    </style>

    <style name="FragmentBackButton_white">
        <item name="android:id">@id/backView</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:layout_width">@dimen/m80</item>
        <item name="android:layout_height">@dimen/m80</item>
        <item name="android:background">@drawable/color_main_button_click_selector</item>
        <item name="android:padding">@dimen/m22</item>
        <item name="android:scaleType">centerInside</item>
        <item name="android:layout_marginLeft">@dimen/x18</item>
        <item name="layout_constraintLeft_toLeftOf">parent</item>
        <item name="layout_constraintTop_toTopOf">parent</item>
        <item name="android:contentDescription">@string/content_desc_back</item>
    </style>

    <!-- 所见即可说 -->
    <style name="ContentDescriptionScrollUp">
        <item name="android:id">@id/cd_up</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@color/transparent</item>
        <item name="android:text">@string/content_desc_up_scroll</item>
        <item name="android:contentDescription">@string/content_desc_up_scroll</item>
        <item name="android:textColor">@color/transparent</item>
        <item name="android:textSize">1sp</item>
    </style>

    <style name="ContentDescriptionScrollDown">
        <item name="android:id">@id/cd_down</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@color/transparent</item>
        <item name="android:text">@string/content_desc_down_scroll</item>
        <item name="android:contentDescription">@string/content_desc_down_scroll</item>
        <item name="android:textColor">@color/transparent</item>
        <item name="android:textSize">1sp</item>
    </style>

    <style name="ContentDescriptionScrollLeft">
        <item name="android:id">@id/cd_left</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@color/transparent</item>
        <item name="android:text">@string/content_desc_left_scroll</item>
        <item name="android:contentDescription">@string/content_desc_left_scroll</item>
        <item name="android:textColor">@color/transparent</item>
        <item name="android:textSize">1sp</item>
    </style>

    <style name="ContentDescriptionScrollRight">
        <item name="android:id">@id/cd_right</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@color/transparent</item>
        <item name="android:text">@string/content_desc_right_scroll</item>
        <item name="android:contentDescription">@string/content_desc_right_scroll</item>
        <item name="android:textColor">@color/transparent</item>
        <item name="android:textSize">1sp</item>
    </style>

    <!--文字描边-->
    <declare-styleable name="StrokeTextView">
        <attr name="outerColor" format="color|reference" />
        <attr name="innnerColor" format="color|reference" />
    </declare-styleable>

    <!--  倒计时控件  -->
    <declare-styleable name="KeepCountdownView">
        <attr name="idleColor" format="color" />//圆弧颜色
        <attr name="arcColor" format="color" />//圆弧颜色
        <attr name="numColor" format="color" />//数字颜色
        <attr name="arcStrokeWidth" format="dimension" />//圆弧厚度
        <attr name="numTextSize" format="dimension" />//数字大小
        <attr name="initDegree" format="integer" />//倒计时开始角度
        <attr name="maxNum" format="integer" />//倒计时时间
        <attr name="isCW" format="boolean" />//是否顺时针倒计时
        <attr name="radius" format="dimension" />//圆半径
        <attr name="plusNumAnimDuration" format="float"/>//动态增加/减少时间时圆弧增长动画时长
    </declare-styleable>
</resources>
