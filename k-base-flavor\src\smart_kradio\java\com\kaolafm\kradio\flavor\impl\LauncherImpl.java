package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import androidx.cardview.widget.CardView;
import android.widget.FrameLayout;

public class LauncherImpl implements LauncherInter {
    @Override
    public void onStart(Object... args) {

    }

    @Override
    public void onCreate(Object... args) {
        if (args[0] != null && args[0] instanceof Activity) {
            Activity activity = (Activity) args[0];
            CardView layout = activity.findViewById(com.kaolafm.kradio.k_kaolafm.R.id.launcher_main_layout);
            CardView.LayoutParams layoutParams = (CardView.LayoutParams) layout.getLayoutParams();
            layoutParams.setMargins(10, 94, 10, 136); //让80px给导航栏
            layout.setRadius(40);
        }
    }

    @Override
    public void onResume(Object... args) {

    }

    @Override
    public void onPause(Object... args) {

    }

    @Override
    public void onStop(Object... args) {

    }

    @Override
    public void onRestart(Object... args) {

    }

    @Override
    public void onDestory(Object... args) {

    }
}
