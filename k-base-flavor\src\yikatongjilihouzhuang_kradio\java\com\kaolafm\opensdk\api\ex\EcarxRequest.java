package com.kaolafm.opensdk.api.ex;

import com.kaolafm.opensdk.account.token.KaolaAccessToken;
import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.base.utils.DateUtil;

import java.util.HashMap;

import io.reactivex.functions.Function;
import okhttp3.MediaType;
import okhttp3.RequestBody;

/**
 * <AUTHOR>
 **/
public class EcarxRequest extends BaseRequest {

    private EcarxService mService;

    public EcarxRequest() {
        mService = obtainRetrofitService(EcarxService.class);
    }

    public void bindDeviceId(String phone, String nickName, String avatar, HttpCallback<DeviceInfo> callback) {
        HashMap<String, String> params = new HashMap<>();
        params.put("phone", phone);
        params.put("nickName", nickName);
        params.put("avatar", avatar);
        String body = mGsonLazy.get().toJson(params);
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), body);
        doHttpDeal(mService.bindDeviceId(requestBody), new Function<BaseResult<DeviceInfo>, DeviceInfo>() {
            @Override
            public DeviceInfo apply(BaseResult<DeviceInfo> baseResult) throws Exception {
                DeviceInfo result = baseResult.getResult();
                saveToken(result);
                return result;
            }
        }, callback);
    }

    private boolean saveToken(DeviceInfo deviceInfo) {
        KaolaAccessToken accessToken = toKaolaAccessToken(deviceInfo);
        if (accessToken != null) {
            KaolaAccessToken kaolaAccessToken = mAccessTokenManagerLazy.get().getKaolaAccessToken();
            accessToken.setOpenId(kaolaAccessToken.getOpenId());
            accessToken.setExpireTime(
                    DateUtil.getServerTime() + (kaolaAccessToken.getRefreshTime() - 10 * 1000));
            mAccessTokenManagerLazy.get().setCurrentAccessToken(accessToken);
            return true;
        }
        return false;
    }

    private KaolaAccessToken toKaolaAccessToken(DeviceInfo deviceInfo) {
        KaolaAccessToken kat = new KaolaAccessToken();
        kat.setUserId(deviceInfo.getUserId());
        kat.setAccessToken(deviceInfo.getAccessToken());
        kat.setRefreshToken(deviceInfo.getRefreshToken());
        kat.setRefreshTime(deviceInfo.getRefreshTime());
        return kat;
    }
}
