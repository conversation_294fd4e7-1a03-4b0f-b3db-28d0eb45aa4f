<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <ImageView
        android:layout_width="@dimen/m296"
        android:layout_height="@dimen/m114"
        android:src="@drawable/comprehensive_mini_player_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.kaolafm.kradio.component.ui.base.view.RoundCircleImageView
        android:id="@+id/player_bar_cover"
        android:layout_width="@dimen/m82"
        android:layout_height="@dimen/m82"
        android:layout_marginStart="@dimen/m16"
        app:isCircle="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/media_default_pic" />

    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/comprehensive_mini_player_cover_stroke"
        app:layout_constraintBottom_toBottomOf="@id/player_bar_cover"
        app:layout_constraintEnd_toEndOf="@id/player_bar_cover"
        app:layout_constraintStart_toStartOf="@id/player_bar_cover"
        app:layout_constraintTop_toTopOf="@id/player_bar_cover" />

    <ImageView
        android:id="@+id/player_bar_loading"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:src="@drawable/comprehensive_mini_player_loading"
        app:layout_constraintBottom_toBottomOf="@id/player_bar_cover"
        app:layout_constraintEnd_toEndOf="@id/player_bar_cover"
        app:layout_constraintStart_toStartOf="@id/player_bar_cover"
        app:layout_constraintTop_toTopOf="@id/player_bar_cover" />

    <ImageView
        android:id="@+id/player_bar_play"
        android:layout_width="@dimen/m72"
        android:layout_height="@dimen/m72"
        android:layout_marginStart="@dimen/m10"
        android:paddingStart="@dimen/m20"
        android:paddingTop="@dimen/m20"
        android:paddingEnd="@dimen/m20"
        android:paddingBottom="@dimen/m20"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/player_bar_cover"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/comprehensive_playerbar_play_brand" />

    <ImageView
        android:id="@+id/player_bar_next"
        android:layout_width="@dimen/m72"
        android:layout_height="@dimen/m72"
        android:paddingStart="@dimen/m20"
        android:paddingTop="@dimen/m20"
        android:paddingEnd="@dimen/m20"
        android:paddingBottom="@dimen/m20"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/player_bar_play"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/comprehensive_playerbar_next_brand" />

</merge>