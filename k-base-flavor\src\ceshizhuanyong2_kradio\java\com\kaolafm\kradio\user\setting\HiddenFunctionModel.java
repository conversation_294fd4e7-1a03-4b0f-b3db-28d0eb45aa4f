package com.kaolafm.kradio.user.setting;

import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.base.mvp.BaseModel;
import com.kaolafm.kradio.setting.SettingItem;
import com.kaolafm.kradio.user.channel.SwitchChannelFragment;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-06-03
 */
public class HiddenFunctionModel extends BaseModel {

    @Override
    public void destroy() {

    }

    /**
     * 获取隐藏item
     *
     * @return
     */
    public List<SettingItem> getHiddenItems() {
        ArrayList<SettingItem> itemList = new ArrayList<>();

//        SettingItem autoAdapter = new SettingItem(R.drawable.settings_channel_back_door, R.string.user_person_center_performance_setting)
//                .setFunctionId(SettingItem.ITEM_FUNCTION_AUTO_ADAPTATION)
//                .setProcessType(SettingItem.TYPE_FRAGMENT)
//                .setInstance(new PerformanceSettingFragment());
//        itemList.add(autoAdapter);

        SettingItem changChannel = new SettingItem(R.drawable.comprehensive_setting_fun_icon, "渠道切换")
                .setFunctionId(SettingItem.ITEM_FUNCTION_AUTO_ADAPTATION)
                .setProcessType(SettingItem.TYPE_FRAGMENT)
                .setInstance(new SwitchChannelFragment());
        itemList.add(changChannel);

        return itemList;
    }
}
