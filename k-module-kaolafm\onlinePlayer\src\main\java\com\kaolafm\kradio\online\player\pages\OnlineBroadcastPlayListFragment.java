package com.kaolafm.kradio.online.player.pages;

import android.os.Bundle;
import androidx.fragment.app.Fragment;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewStub;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.common.bean.ProgramDateData;
import com.kaolafm.kradio.common.report.ReportParamUtil;
import com.kaolafm.kradio.common.report.ReportUtil;
import com.kaolafm.kradio.k_kaolafm.R; 
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.DateUtil;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.RecyclerViewExposeUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.online.common.event.ScrollToPlayingPlayItemPositionEvent;
import com.kaolafm.kradio.online.player.adapters.BroadcastPlayListRvAdapter;
import com.kaolafm.kradio.online.player.base.OnlineBasePlayListFragment;
import com.kaolafm.kradio.online.player.base.OnlineBasePlayListRvAdapter;
import com.kaolafm.kradio.online.player.models.OnlineTabData;
import com.kaolafm.kradio.online.player.mvp.BroadcastDateListPresenter;
import com.kaolafm.kradio.online.player.mvp.BroadcastDateListView;
import com.kaolafm.kradio.online.player.mvp.BroadcastProgramListPresenter;
import com.kaolafm.kradio.online.player.mvp.BroadcastProgramListView;
import com.kaolafm.kradio.online.player.mvp.BroadcastProgramRequestBean;
import com.kaolafm.kradio.online.player.mvp.ListenTVProgramListPresenter;
import com.kaolafm.kradio.online.player.mvp.ListenTVProgramListView;
import com.kaolafm.kradio.online.player.mvp.ListenTVProgramRequestBean;
import com.kaolafm.kradio.player.event.PlayerListChangedEBData;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.player.helper.intercept.OverhaulInterceptManager;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.IPlayListControl;
import com.kaolafm.opensdk.player.logic.model.item.BroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.TVPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.playlist.BroadcastPlayListControl;
import com.kaolafm.opensdk.player.logic.playlist.TVPlayListControl;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.report.event.PlayerUiControlReportEvent;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List; 
import kotlin.jvm.Synchronized;

public class OnlineBroadcastPlayListFragment extends OnlineBasePlayListFragment implements BroadcastDateListView, BroadcastProgramListView, ListenTVProgramListView, RecyclerViewExposeUtil.OnItemExposeListener {
    private BroadcastDateListPresenter mDatePresenter;
 
    ViewStub broadcastNoProgramTipViewStub;

    public static OnlineBroadcastPlayListFragment newInstance(String mTag) {
        OnlineBroadcastPlayListFragment fragment = new OnlineBroadcastPlayListFragment();
        Bundle args = new Bundle();
        args.putString(ARGUMENT_TAG, mTag);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public boolean useTabLayout() {
        return true;
    }

    @Override
    protected BasePresenter createPresenter() {
        mDatePresenter = new BroadcastDateListPresenter(this);
        getLifecycle().addObserver(mDatePresenter);
        if (PlayerManagerHelper.getInstance().getCurrentPlayType() == PlayerConstants.RESOURCES_TYPE_TV)
            return new ListenTVProgramListPresenter(this);
        else return new BroadcastProgramListPresenter(this);
    }

    @Override
    public String getPageId() {
        return Constants.PAGE_ID_PLAY_TV;
    }

    @Override
    public void queryDateList() {
        super.queryDateList();
        //没有节目单，就不去请求数据了
        if (!PlayerManagerHelper.getInstance().isHasBroadcastPlayList()) {
            return;
        }
        mDatePresenter.getBroadcastDate();
    }

    @Override
    public void initView(View view) {
        super.initView(view);

        broadcastNoProgramTipViewStub=view.findViewById(R.id.broadcastNoProgramTipViewStub);
 
        if (PlayerManagerHelper.getInstance().getCurrentPlayType() == PlayerConstants.RESOURCES_TYPE_BROADCAST
                || PlayerManagerHelper.getInstance().getCurrentPlayType() == PlayerConstants.RESOURCES_TYPE_TV) {
            setItemCountViewGroupVisibility(View.GONE);
            if (!PlayerManagerHelper.getInstance().isHasBroadcastPlayList()) {
                broadcastNoProgramTipViewStub.inflate();
            } else {
                ViewUtil.setViewVisibility(view.findViewById(R.id.tabLayout), View.VISIBLE);
            }
        }

        RecyclerViewExposeUtil exposeUtil = new RecyclerViewExposeUtil();
        exposeUtil.setRecyclerItemExposeListener(recyclerView, this);
    }

    @Override
    public boolean useEventBus() {
        return true;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void scrollToPlayingPlayItemPositionEvent(ScrollToPlayingPlayItemPositionEvent event) {
        addScrollEvent(false, false);
    }

    @Override
    public void getProgramFromServer(OnlineTabData onlineTabData) {
        getProgramFromServer(onlineTabData, true);
    }

    /**
     * @param onlineTabData
     * @param updateView    是否更新列表组件
     */
    private void getProgramFromServer(OnlineTabData onlineTabData, boolean updateView) {
        if (!(onlineTabData.getData() instanceof ProgramDateData) || !(onlineTabData.getKey() instanceof Integer)) {
            return;
        }
        if (mPresenter != null) {
            if (mPresenter instanceof BroadcastProgramListPresenter) {
                BroadcastProgramRequestBean broadcastProgramRequestBean = new BroadcastProgramRequestBean();
                broadcastProgramRequestBean.setProgramIndex((Integer) onlineTabData.getKey());
                broadcastProgramRequestBean.setDate(((ProgramDateData) onlineTabData.getData()).getTime());
                long radioId = 0L;
                try {
                    radioId = Long.parseLong(PlayerManager.getInstance().getCurPlayItem().getRadioId());
                } catch (NumberFormatException nfe) {
                    nfe.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                broadcastProgramRequestBean.setParentId(radioId);
                ((BroadcastProgramListPresenter) mPresenter).getBroadcastProgramList(broadcastProgramRequestBean, updateView, PlayerManager.getInstance().getPlayListInfo().getBroadcastChannel(), PlayerManager.getInstance().getPlayListInfo().getBroadcastClassifyId(), PlayerManager.getInstance().getPlayListInfo().getListenNum());
            } else if (mPresenter instanceof ListenTVProgramListPresenter) {
                ListenTVProgramRequestBean listenTVProgramRequestBean = new ListenTVProgramRequestBean();
                listenTVProgramRequestBean.setProgramIndex((Integer) onlineTabData.getKey());
                listenTVProgramRequestBean.setDate(((ProgramDateData) onlineTabData.getData()).getTime());
                long radioId = 0L;
                try {
                    radioId = Long.parseLong(PlayerManager.getInstance().getCurPlayItem().getRadioId());
                } catch (NumberFormatException nfe) {
                    nfe.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                listenTVProgramRequestBean.setListenTVid(radioId);
                ((ListenTVProgramListPresenter) mPresenter).getTVProgramList(listenTVProgramRequestBean, PlayerManager.getInstance().getPlayListInfo().getListenNum(), updateView);
            }
        }
    }

    @Override
    public void onGetBroadcastDateSuccess(BroadcastDateData broadcastDateData) {
        Log.e(TAG, broadcastDateData.toString());
        ArrayList<ProgramDateData> dataList = broadcastDateData.getDataList();
        if (ListUtil.isEmpty(dataList)) {
            return;
        }
        List<OnlineTabData> datas = new ArrayList<>();
        for (int i = 0; i < broadcastDateData.getDataList().size(); i++) {
            if (TextUtils.equals(broadcastDateData.getSelected(), broadcastDateData.getDataList().get(i).getTime())) {
                datas.add(new OnlineTabData<>("今天", BroadcastProgramRequestBean.TODAY_INDEX, broadcastDateData.getDataList().get(i)));
                if (i > 0) {
                    datas.add(0, new OnlineTabData<>("昨天", BroadcastProgramRequestBean.YESTERDAY_INDEX, broadcastDateData.getDataList().get(i - 1)));
                } else {
                    //没有昨天
//                    defaultIndex = 0;
                }

                if (broadcastDateData.getDataList().size() > i + 1) {
                    datas.add(new OnlineTabData<>("明天", BroadcastProgramRequestBean.TOMORROW_INDEX, broadcastDateData.getDataList().get(i + 1)));
                }
                break;
            }
        }
        if (ListUtil.isEmpty(datas)) {
            return;
        }
        setTabs(datas);
    }

    @Override
    public int findTabSelectedPosition(PlayItem curPlayItem, List<OnlineTabData> onlineTabDataList) {
        int currentTabPosition = -1;
        for (int i = 0; i < onlineTabDataList.size(); i++) {
            Object data = onlineTabDataList.get(i).getData();
            //当前播放的playItem的日期来确定选中的tab下标
            String date = null;

            if (data instanceof ProgramDateData) {
                if (curPlayItem instanceof BroadcastPlayItem) {
                    date = DateUtil.formatMillis(((BroadcastPlayItem) curPlayItem).getTimeInfoData().getStartTime());
                } else if (curPlayItem instanceof TVPlayItem) {
                    date = DateUtil.formatMillis(((TVPlayItem) curPlayItem).getTimeInfoData().getStartTime());
                }
            }
            if (StringUtil.isNotEmpty(date) && date.equals(((ProgramDateData) data).getTime())) {
                currentTabPosition = i;
                break;
            }
        }
        return currentTabPosition;
    }

    @Synchronized
    @Override
    protected void addPlayItemToPlayList(List<PlayItem> playItems) {
        Log.d(getClass().getSimpleName(), "addPlayItemToPlayList playItems size " + (playItems != null ? playItems.size() : "null"));
        int livingPlayItemIndex = findLivingPlayItemIndex(playItems);
        if (PlayerManagerHelper.isBroadcastOrTvNotSupportPlayBack(null)) {
            if (livingPlayItemIndex != -1) {
                addToday(playItems, livingPlayItemIndex, false);
            } else {
                //不是当天的，就看playItems是哪天的节目单，对于不支持回听的只关心明天的节目单就好了
                if (ListUtil.isEmpty(playItems)) return;
                if (playItems.get(0).getStatus() == PlayerConstants.BROADCAST_STATUS_NOT_ON_AIR) {
                    //明天的播单
                    addTomorrow(playItems);
                }
            }
            EventBus.getDefault().post(new PlayerListChangedEBData());
            return;
        }

        if (livingPlayItemIndex != -1) {
            addToday(playItems, livingPlayItemIndex, true);
        } else if (playItems.get(0).getStatus() == PlayerConstants.BROADCAST_STATUS_NOT_ON_AIR) {
            //明天的播单
            addTomorrow(playItems);
        } else if (playItems.get(0).getStatus() == PlayerConstants.BROADCAST_STATUS_PLAYBACK) {
            //昨天的播单
            addYesterday(playItems);
        }
    }

    private void addYesterday(List<PlayItem> playItems) {
        if (ListUtil.isEmpty(playItems)) return;
        IPlayListControl playListControl = PlayerManager.getInstance().getPlayListControl();
        if (playListControl == null) return;
        PlayItem curPlayItem = PlayerManager.getInstance().getCurPlayItem();
        List<PlayItem> playList = playListControl.getPlayList();
        Log.d(getClass().getSimpleName(), "addYesterday before add size " + playList.size());
        //先判断是否已经存在昨天的播单了
        PlayItem firstInPlayItems = playItems.get(playItems.size() - 1);
        int indexInPlayList = -1;
        for (int i = 0; i < playList.size(); i++) {
            if (firstInPlayItems.getAudioId() == playList.get(i).getAudioId()) {
                indexInPlayList = i;
                break;
            }
        }
        if (indexInPlayList != -1) {
            //存在昨天播单，先删除
            if (playList.size() > indexInPlayList) {
                playList.subList(0, indexInPlayList + 1).clear();
            }
        }
        playList.addAll(0, playItems);
        Log.d(getClass().getSimpleName(), "after add size " + playList.size());
        playListControl.setCurPosition(curPlayItem);
    }

    /**
     * 添加明天的播单到播放器播单中
     *
     * @param playItems 明天的播单
     */
    private void addTomorrow(List<PlayItem> playItems) {
        if (ListUtil.isEmpty(playItems)) return;
        IPlayListControl playListControl = PlayerManager.getInstance().getPlayListControl();
        if (playListControl == null) return;
        PlayItem curPlayItem = PlayerManager.getInstance().getCurPlayItem();
        List<PlayItem> playList = playListControl.getPlayList();
        Log.d(getClass().getSimpleName(), "addTomorrow before add size " + playList.size());
        //先判断是否已经添加了明天的播单
        PlayItem firstInPlayItems = playItems.get(0);
        int indexInPlayList = -1;
        for (int i = 0; i < playList.size(); i++) {
            if (firstInPlayItems.getAudioId() == playList.get(i).getAudioId()) {
                indexInPlayList = i;
                break;
            }
        }

        if (indexInPlayList != -1) {
            //已经添加了明天的播单，则先删除
            if (playList.size() > indexInPlayList) {
                playList.subList(indexInPlayList, playList.size()).clear();
            }
        }
        playList.addAll(playItems);
        Log.d(getClass().getSimpleName(), "after add size " + playList.size());
        playListControl.setCurPosition(curPlayItem);
    }

    /**
     * 添加今天的播单到播放器播单中
     *
     * @param playItems           今天的播单
     * @param livingPlayItemIndex 正在直播项在playItems中的下标
     * @param programEnable       是否能收听回听节目
     */
    private void addToday(List<PlayItem> playItems, int livingPlayItemIndex, boolean programEnable) {
        if (ListUtil.isEmpty(playItems)) return;
        IPlayListControl playListControl = PlayerManager.getInstance().getPlayListControl();
        if (playListControl == null) return;
        //正在直播的
        PlayItem curPlayItem = PlayerManager.getInstance().getCurPlayItem();
        PlayItem firstInPlayItems = playItems.get(0);
        PlayItem lastInPlayItems = playItems.get(playItems.size() - 1);
        List<PlayItem> playList = playListControl.getPlayList();
        Log.d(getClass().getSimpleName(), "addToday before add size " + playList.size());
        int beginIndexInPlayList = 0, endIndexInPlayList = -1;
        //找出playItems中第一个元素再playList中的位置
        for (int i = 0; i < playList.size(); i++) {
            if (firstInPlayItems.getAudioId() == playList.get(i).getAudioId()) {
                beginIndexInPlayList = i;
                break;
            }
        }
        //找出playItems中最后一个元素在playList中的位置
        for (int i = 0; i < playList.size(); i++) {
            if (lastInPlayItems.getAudioId() == playList.get(i).getAudioId()) {
                endIndexInPlayList = i;
                break;
            }
        }
        if (endIndexInPlayList != -1) {
            //找到了playItems中最后一个元素在playList中的位置
            //先删除
            playList.subList(beginIndexInPlayList, endIndexInPlayList + 1).clear();
        } else {
            //没找到，说明第一次添加今天的播单到playList
        }
        playList.addAll(beginIndexInPlayList, programEnable ? playItems : playItems.subList(livingPlayItemIndex, playItems.size()));
        Log.d(getClass().getSimpleName(), "after add size " + playList.size());
        playListControl.setCurPosition(curPlayItem);
    }

    /**
     * 添加playItems到playList开头，并忽略重复item
     *
     * @param playItems
     * @param playList
     */
    private void addPlayItemsToStart(List<PlayItem> playItems, List<PlayItem> playList) {
        if (ListUtil.isEmpty(playItems) || playList == null) return;
        PlayItem playItemLast = playItems.get(playItems.size() - 1);
        PlayItem item;
        int position = -1;
        for (int i = 0; i < playList.size(); i++) {
            item = playList.get(i);
            if (playItemLast.getAudioId() == item.getAudioId()) {
                position = i;
                break;
            }
        }
        if (position == -1) {
            playList.addAll(0, playItems);
        } else {
            if (playItems.size() > position + 1) {
                playList.addAll(0, Arrays.asList(Arrays.copyOfRange(playItems.toArray(new PlayItem[0]), 0, playItems.size() - position - 1)));
            }
        }
    }

    /**
     * 添加playItems到playList末尾，并忽略重复item
     *
     * @param playItems
     * @param playList
     */
    private void addPlayItemsToEnd(List<PlayItem> playItems, List<PlayItem> playList) {
        if (ListUtil.isEmpty(playItems) || playList == null) return;
        PlayItem playItem0 = playItems.get(0);
        PlayItem item;
        int position = -1;
        for (int i = 0; i < playList.size(); i++) {
            item = playList.get(i);
            if (playItem0.getAudioId() == item.getAudioId()) {
                position = i;
            }
        }
        if (position == -1) {
            playList.addAll(playItems);
        } else {
            int startIndex = playList.size() - position;
            if (playItems.size() > startIndex) {
                for (int i = startIndex; i < playItems.size(); i++) {
                    playList.add(playItems.get(i));
                }
            }
        }
    }

    private PlayItem findLivingPlayItem(List<PlayItem> playItems) {
        if (ListUtil.isEmpty(playItems)) {
            return null;
        }
        for (PlayItem playItem : playItems) {
            if (playItem.isLiving()) return playItem;
        }
        return null;
    }

    private int findLivingPlayItemIndex(List<PlayItem> playItems) {
        if (ListUtil.isEmpty(playItems)) {
            return -1;
        }
        PlayItem playItem;
        for (int i = 0; i < playItems.size(); i++) {
            playItem = playItems.get(i);
            if (playItem.isLiving()) return i;
        }

        return -1;
    }

    @Override
    public void setUnsubscribeToRvAdapter(OnlineBasePlayListRvAdapter mAdapter) {
        super.setUnsubscribeToRvAdapter(mAdapter);
        if (mAdapter instanceof BroadcastPlayListRvAdapter) {
            ((BroadcastPlayListRvAdapter) mAdapter).setUnsubscribe(new BaseAdapter.OnItemClickListener() {
                @Override
                public void onItemClick(View view, int viewType, Object o, int position) {
                }
            });
        }
    }

    @Override
    protected OnlineBasePlayListRvAdapter getPlayListAdapter(List<PlayItem> playItems) {
        return new BroadcastPlayListRvAdapter(playItems);
    }

    @Override
    public void onPlayListItemClick(View view, int viewType, PlayItem playItem, int position) {
        if (viewType == BroadcastPlayListRvAdapter.PLAY_ITEM_NO_START) {
            //未开始
            ToastUtil.showInfo(getContext(), R.string.broadcast_tomorrow);
            return;
        }
        if (AntiShake.check(playItem.getAudioId())) {
            return;
        }
        if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(getContext())) {
            return;
        }
        //只对过往节目有效
        if (viewType == BroadcastPlayListRvAdapter.PLAY_ITEM_NORMAL) {
            //对于地方台广播和听电视，不支持回放，只提示
            if (PlayerManagerHelper.isBroadcastOrTvNotSupportPlayBack(playItem)) {
                ToastUtil.showInfo(getContext(), R.string.online_player_broadcast_no_playback);
                return;
            }
        }
        if (viewType == BroadcastPlayListRvAdapter.PLAY_ITEM_NORMAL || viewType == BroadcastPlayListRvAdapter.PLAY_ITEM_PLAYING) {
            OverhaulInterceptManager.getInstance().intercept(getContext(), playItem, true);
            List<PlayItem> playItemList = PlayerManager.getInstance().getPlayList();
            if (ListUtil.isEmpty(playItemList)) {
                refreshPlayListAndPlayItem(playItem);
            } else {
                boolean flag = isContainsPlayItem(playItemList, playItem);
                if (flag) {
                    PlayerManagerHelper.getInstance().start(String.valueOf(playItem.getAudioId()), playItem.getType());
                } else {
                    refreshPlayListAndPlayItem(playItem);
                }
            }
            Fragment parentFragment = getParentFragment();
            if (parentFragment instanceof OnlinePlayerFragment) {
                ((OnlinePlayerFragment) parentFragment).upDateRingItemView();
            }
            ReportUtil.addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_PLAY_LIST, PlayerUiControlReportEvent.CONTROL_TYPE_CLICK, PlayerUiControlReportEvent.POSITION_PLAY_FRAGEMNT);
            reportContentClickEvent(playItem, position);
        }
    }

    /**
     * 点击item事件上报
     *
     * @param item
     */
    private void reportContentClickEvent(PlayItem item, int position) {
        ReportUtil.addContentClickEvent(String.valueOf(item.getAudioId()),
                ReportParamUtil.getRadioType(item.getType()),
                ReportParamUtil.getAudioType(item),
                item.getRadioId(),
                ReportParamUtil.getEventTag(item.getVip() == 1, item.getFine() == 1),
                getPageId(), getPageId(), "" + position);
    }

    private void refreshPlayListAndPlayItem(PlayItem playItem) {
        IPlayListControl iPlayListControl = PlayerManager.getInstance().getPlayListControl();
        if (iPlayListControl instanceof BroadcastPlayListControl) {
            BroadcastPlayListControl playListControl = (BroadcastPlayListControl) iPlayListControl;
            playListControl.getSongPlayList().clear();
            playListControl.addSongPlayItem(playItems);
        }
        PlayerManagerHelper.getInstance().startPlayItemInList(playItem);
    }

    @Override
    public void onGetBroadcastDateError(int code, String msg) {
        Log.e(TAG, "请求失败：" + msg);
    }

    @Override
    public void onGetBroadcastProgramListDataSuccess(List<PlayItem> playItemArrayList, boolean updateView) {
        if (!updateView) {
            addPlayItemToPlayList(playItemArrayList);
            return;
        }
        if (getAdapter() != null) {
            int selectPosition = getAdapter().getSelectPosition();
            int playingPosition = getAdapter().findSelectPosition();
            if (selectPosition != playingPosition) {
                getAdapter().unSelectItem();
            }
        }
        notifyPlayListChanged(playItemArrayList);
    }

    @Override
    public void onGetBroadcastProgramListDataError(int code, String msg) {
        Log.e(TAG, "请求失败：" + msg);
    }

    @Override
    public void onGetListenTvProgramListDataSuccess(List<PlayItem> playItemArrayList, boolean updateView) {
        if (!updateView) {
            addPlayItemToPlayList(playItemArrayList);
            return;
        }
        if (getAdapter() != null) {
            int selectPosition = getAdapter().getSelectPosition();
            int playingPosition = getAdapter().findSelectPosition();
            if (selectPosition != playingPosition) {
                getAdapter().unSelectItem();
            }
        }
        notifyPlayListChanged(playItemArrayList);
    }

    @Override
    public void onGetListenTvProgramListDataError(int code, String msg) {
        Log.e(TAG, "请求失败：" + msg);
    }

    @Override
    public void showLoading() {

    }

    @Override
    public void hideLoading() {

    }

    @Override
    public void refreshPlayListAndUpdateItemOnPosition(int position) {
        super.refreshPlayListAndUpdateItemOnPosition(position);
        getProgramFromServer(onlineTabDataList.get(tabLayout.getCurrentPosition()));
    }


    @Override
    public void setTabs(List<OnlineTabData> onlineTabDataList) {
        super.setTabs(onlineTabDataList);

        //先把其他日期的播单加载出来
        for (OnlineTabData onlineTabData : onlineTabDataList) {
            getProgramFromServer(onlineTabData, false);
        }
//        int currentTabPosition = tabLayout.getCurrentPosition();
//        if (onlineTabDataList.size() <= currentTabPosition) return;
//        if ((Integer) onlineTabDataList.get(currentTabPosition).getKey() == ProgramRequestBean.TODAY_INDEX) {
//            //选中今天，则把昨天的播单加载进来
//            OnlineTabData onlineTabData = onlineTabDataList.get(currentTabPosition);
//            getProgramFromServer(onlineTabData, false);
//        } else if ((Integer) onlineTabDataList.get(currentTabPosition).getKey() == ProgramRequestBean.YESTERDAY_INDEX) {
//            //选中昨天，则把今天的播单加载进来
//            if (onlineTabDataList.size() < 2) return;
//            OnlineTabData onlineTabData = onlineTabDataList.get(currentTabPosition);
//            getProgramFromServer(onlineTabData, false);
//        }
    }

    @Override
    public void onItemViewVisible(boolean visible, int position) {
        if (visible && recyclerView != null) {
            BroadcastPlayListRvAdapter broadcastPlayerListAdapter = (BroadcastPlayListRvAdapter) recyclerView.getAdapter();
            if (broadcastPlayerListAdapter != null) {
                PlayItem bean = broadcastPlayerListAdapter.getItemData(position);
                ReportUtil.addContentShowEvent(String.valueOf(bean.getAudioId()),
                        ReportParamUtil.getRadioType(bean.getType()),
                        ReportParamUtil.getAudioType(bean),
                        String.valueOf(bean.getRadioId()),
                        ReportParamUtil.getEventTag(bean.getVip() == 1, bean.getFine() == 1),
                        Constants.PAGE_ID_PLAY_TV, "", "" + position);
            }

        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        OnlineBasePlayListRvAdapter mAdapter = getAdapter();
        if (mAdapter instanceof BroadcastPlayListRvAdapter) {
            ((BroadcastPlayListRvAdapter) mAdapter).setUnsubscribe(null);
        }
    }
}
