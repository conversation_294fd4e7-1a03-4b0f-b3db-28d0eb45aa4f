package com.kaolafm.gradle.plugin.flavor

class GenerateSkinValueFilesUtil {
    private static String WTemplate = "<dimen name=\"x{0}\">{1}px</dimen>\n"

    private static String HTemplate = "<dimen name=\"y{0}\">{1}px</dimen>\n"

    private static String SizeTemplate = "<dimen name=\"m{0}\">{1}px</dimen>\n"

    private static String NearestSizeTemplate = "<dimen name=\"n{0}\">{1}px</dimen>\n"

    private static int baseW = 1920

    private static int baseH = 720

    private static int layoutType = 0

    private static List<Integer> xValues = new ArrayList<>()
    private static List<Integer> yValues = new ArrayList<>()
    private static List<Integer> mValues = new ArrayList<>()
    private static List<Integer> nValues = new ArrayList<>()

    /**
     * 更新基准
     * @param layoutType
     * @return
     */
    def static updateBaseWH(int layoutType) {
        if (this.layoutType != layoutType) {
            this.layoutType = layoutType
            switch (this.layoutType) {
                case 1:
                    baseW = 1280
                    baseH = 720
                    break
                default:
                    baseW = 1920
                    baseH = 720
                    break
            }
        }
    }

    def static generateDensity(String projectPath, String channel, List<String> densities) {
        if (densities != null) {
            try {
                initNightDimensValues(projectPath);
            } catch (Exception e) {
                e.printStackTrace()
            }
            densities.each {
                density ->
                    if (projectPath != null && projectPath.trim().length() > 0) {
                        String path = projectPath + "/src/" + channel + "/res/values-" + density
                        File values = new File(path)
                        if (!values.exists()) {
                            values.mkdirs()
                            String[] wh = density.split("x")
                            int w = Integer.parseInt(wh[0])
                            int h = Integer.parseInt(wh[1])
                            //这里判断是为了竖屏适配
                            if (w > h) {
                                generateXmlFile(path, w, h)
                            } else {
                                generateXmlFile(path, h, w)
                            }

                        }
                    }
            }
        }
    }

    private static void initNightDimensValues(String projectPath) throws Exception {
        String path = projectPath + "/src/main/res/values/dimens.xml"
        File file = new File(path)
        InputStream inputStream = new FileInputStream(file)
        if (inputStream != null) {
            InputStreamReader inputStreamReader = new InputStreamReader(inputStream);
            BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
            String line;
            //逐行读取
            while ((line = bufferedReader.readLine()) != null) {
                if (line.contains("@dimen/m")) {
                    int start = line.indexOf("@dimen/m") + 8;
                    int end = line.indexOf("</dimen>")
                    String m = line.substring(start, end)
                    add2List(mValues, Integer.parseInt(m));
                } else if (line.contains("@dimen/x")) {
                    int start = line.indexOf("@dimen/x") + 8;
                    int end = line.indexOf("</dimen>")
                    String x = line.substring(start, end)
                    add2List(xValues, Integer.parseInt(x));
                } else if (line.contains("@dimen/y")) {
                    int start = line.indexOf("@dimen/y") + 8;
                    int end = line.indexOf("</dimen>")
                    String y = line.substring(start, end)
                    add2List(yValues, Integer.parseInt(y));
                }else if (line.contains("@dimen/n")) {
                    int start = line.indexOf("@dimen/n") + 8;
                    int end = line.indexOf("</dimen>")
                    String y = line.substring(start, end)
                    add2List(nValues, Integer.parseInt(y));
                }
            }
            bufferedReader.close()
            inputStreamReader.close()
            inputStream.close()
        }

    }

    private static void add2List(List<Integer> list, int value) {
        if (!list.contains(value)) {
            list.add(value)
        }
    }

    private static void generateXmlFile(String path, Integer w, Integer h) {
        float cellw = w * 1.0f / baseW
        StringBuffer sbForWidth = new StringBuffer()
        sbForWidth.append("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n")
        sbForWidth.append("<resources>")
        System.out.println("width : " + w + "," + baseW + "," + cellw)
        for (int x : xValues) {
            sbForWidth.append(WTemplate.replace("{0}", x + "").replace("{1}", change(cellw * x) + ""))
        }
        sbForWidth.append(WTemplate.replace("{0}", baseW + "").replace("{1}", w + ""))
        sbForWidth.append("</resources>")

        float cellh = h * 1.0f / baseH
        StringBuffer sbForHeight = new StringBuffer()
        sbForHeight.append("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n")
        sbForHeight.append("<resources>")
        System.out.println("height : " + h + "," + baseH + "," + cellh)
        for (int i : yValues) {
            sbForHeight.append(HTemplate.replace("{0}", i + "").replace("{1}", change(cellh * i) + ""))
        }
        sbForHeight.append(HTemplate.replace("{0}", baseH + "").replace("{1}", h + ""))
        sbForHeight.append("</resources>")

        float minSize = Math.abs(cellw - 1) < Math.abs(cellh - 1) ? cellw : cellh
        StringBuffer sbMinSize = new StringBuffer()
        sbMinSize.append("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n")
        sbMinSize.append("<resources>")
        System.out.println("height : " + minSize + "," + cellw + "," + cellh)
        for (int i : mValues) {
            sbMinSize.append(SizeTemplate.replace("{0}", i + "").replace("{1}", change(minSize * i) + ""))
        }
        sbMinSize.append(SizeTemplate.replace("{0}", ((cellw < cellh) ? baseW : baseH) + "").
                replace("{1}", ((cellw < cellh) ? w : h) + ""))
        sbMinSize.append("</resources>")

        float nearest = (cellw < cellh) ? cellw : cellh
        StringBuffer sbNearestSize = new StringBuffer()
        sbNearestSize.append("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n")
        sbNearestSize.append("<resources>")
        System.out.println("height : " + nearest + "," + cellw + "," + cellh)
        for (int i : nValues) {
            sbNearestSize.append(NearestSizeTemplate.replace("{0}", i + "").replace("{1}", change(nearest * i) + ""))
        }
        sbNearestSize.append(NearestSizeTemplate.replace("{0}", ((cellw < cellh) ? baseW : baseH) + "").
                replace("{1}", ((cellw < cellh) ? w : h) + ""))
        sbNearestSize.append("</resources>")

        File fileDir = new File(path)
        fileDir.mkdir()

        File layxFile = new File(fileDir.getAbsolutePath(), "lay_x.xml")
        File layyFile = new File(fileDir.getAbsolutePath(), "lay_y.xml")
        File laysizeFile = new File(fileDir.getAbsolutePath(), "lay_size.xml")
        File layNearestFile = new File(fileDir.getAbsolutePath(), "lay_size_nearest.xml")
        try {
            PrintWriter pw = new PrintWriter(new FileOutputStream(layxFile))
            pw.print(sbForWidth.toString())
            pw.close()
            pw = new PrintWriter(new FileOutputStream(layyFile))
            pw.print(sbForHeight.toString())
            pw.close()
            pw = new PrintWriter(new FileOutputStream(laysizeFile))
            pw.print(sbMinSize.toString())
            pw.close()
            pw = new PrintWriter(new FileOutputStream(layNearestFile))
            pw.print(sbNearestSize.toString())
            pw.close()
        } catch (FileNotFoundException e) {
            e.printStackTrace()
        }
    }

    private static float change(double a) {
        int temp = (int) (a * 100)
        return temp / 100f
    }
}