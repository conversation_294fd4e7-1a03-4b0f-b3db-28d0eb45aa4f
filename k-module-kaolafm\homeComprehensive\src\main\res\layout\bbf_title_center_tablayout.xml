<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:layout_gravity="center"
    android:gravity="center">
    <!--必须保留 LinearLayout,否则SlidingTabLayout的下三角与文字太靠近-->
    <com.kaolafm.kradio.lib.widget.tab.SlidingTabLayout
        android:id="@+id/stb_all_category_title_name"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/m60"
        android:layout_gravity="center"
        app:tl_indicator_drawable="@drawable/tab_base_indicator"
        app:tl_indicator_height="0dp"
        app:tl_indicator_width="0dp"
        app:tl_first_no_padding="false"
        app:tl_indicator_style="NORMAL"
        app:tl_indicator_width_equal_title="true"
        app:tl_tab_padding="0dp"
        app:tl_tab_width="@dimen/m224"
        app:tl_textSelectColor="@color/text_color_7"
        app:kradio_tl_textSelectSize="@dimen/category_nav_bar_tv_selected"
        app:tl_textSelectSize="@dimen/category_nav_bar_tv_selected"
        app:tl_textUnselectColor="@color/text_color_8"
        app:kradio_tl_textSize="@dimen/nav_bar_tv_unselected" />
</LinearLayout>
