package com.kaolafm.kradio.online.categories.tab;

import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.OnLifecycleEvent;
import android.text.TextUtils;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.common.SubcategoryItemBean;
import com.kaolafm.kradio.common.bean.BroadcastRadioSimpleData;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.opensdk.api.operation.model.category.LeafCategory;
import com.kaolafm.kradio.online.categories.CategoryConstant;
import com.kaolafm.kradio.online.categories.ClickHelper;
import com.kaolafm.kradio.online.categories.adapter.HorizontalSubcategoryAdapter;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.api.operation.model.category.Category;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 **/
public class TabItemPresenter extends BasePresenter<TabItemModel, TabItemContract.IView> implements TabItemContract.IPresenter {

    private boolean mHaveMembers;

    private long cid;

    public TabItemPresenter(TabItemContract.IView view, long id, boolean haveMembers) {
        super(view);
        this.cid = id;
        mHaveMembers = haveMembers;
        registerListener();
    }

    @Override
    protected TabItemModel createModel() {
        return new TabItemModel();
    }

    /**
     * 加载市级数据
     *
     * @param parentCode
     */
    public void loadCityDate(String parentCode) {
        mModel.getSubcategoryList(parentCode, new HttpCallback<List<Category>>() {
            @Override
            public void onSuccess(List<Category> categories) {
                ArrayList<SubcategoryItemBean> itemBeans = new ArrayList<>();
                if (!ListUtil.isEmpty(categories)) {
                    for (Category category : categories) {
                        SubcategoryItemBean subcategoryItemBean = new SubcategoryItemBean();
                        String code = category.getCode();
                        if (TextUtils.isEmpty(code)) {
                            code = "0";
                        }
                        subcategoryItemBean.setId(Long.parseLong(code));
                        subcategoryItemBean.setName(category.getName());
                        subcategoryItemBean.setType(category.getType());
                        subcategoryItemBean.setItemType(SubcategoryItemBean.TYPE_ITEM_BROADCAST_CATEGORY);
                        itemBeans.add(subcategoryItemBean);
                    }
                }
                if (mView != null) {
                    mView.showData(itemBeans);
                }
            }

            @Override
            public void onError(ApiException e) {
                if (mView != null) {
                    mView.showError(e);
                }
            }
        });
    }

    @Override
    public void loadData() {
        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
            if (mView != null) {
                mView.showError(new ApiException("网络错误!"));
            }
            return;
        }
        String parentCode = String.valueOf(cid);
        if (mHaveMembers) {
            mModel.getCategoryMemberList(parentCode, new HttpCallback<List<SubcategoryItemBean>>() {
                @Override
                public void onSuccess(List<SubcategoryItemBean> itemBeans) {
                    if (mView != null) {
                        mView.showData(itemBeans);
                    }
                }

                @Override
                public void onError(ApiException e) {
                    if (mView != null) {
                        mView.showError(e);
                    }
                }
            });
        } else {
            mModel.getSubcategoryList(parentCode, new HttpCallback<List<Category>>() {
                @Override
                public void onSuccess(List<Category> categories) {
                    ArrayList<SubcategoryItemBean> itemBeans = new ArrayList<>();
                    if (!ListUtil.isEmpty(categories)) {
                        for (Category category : categories) {
                            SubcategoryItemBean subcategoryItemBean = new SubcategoryItemBean();
                            String code = category.getCode();
                            if (TextUtils.isEmpty(code)) {
                                code = "0";
                            }

                            subcategoryItemBean.setId(Long.parseLong(code));
                            subcategoryItemBean.setName(category.getName());
                            if (!(category instanceof LeafCategory)) {
                                subcategoryItemBean.setType(CategoryConstant.TYPE_CATEGORY);
                            } else {
                                subcategoryItemBean.setType("");
                            }
                            subcategoryItemBean.setItemType(SubcategoryItemBean.TYPE_ITEM_BROADCAST_CATEGORY);
                            itemBeans.add(subcategoryItemBean);
                        }
                    }
                    if (mView != null) {
                        mView.showData(itemBeans);
                    }
                }

                @Override
                public void onError(ApiException e) {
                    if (mView != null) {
                        mView.showError(e);
                    }
                }
            });
        }

    }

    @Override
    public void loadMore() {
        if (mModel == null) {
            return;
        }
        if (isHasNextPage()) {
            mModel.loadMoreCategoryMember(new HttpCallback<List<SubcategoryItemBean>>() {
                @Override
                public void onSuccess(List<SubcategoryItemBean> itemBeanList) {
                    if (mView != null) {
                        mView.showMoreData(itemBeanList);
                    }
                }

                @Override
                public void onError(ApiException e) {
                    if (mView != null) {
                        mView.showMoreDataError(e);
                    }
                }
            });
        }
    }

    @Override
    public boolean isHasNextPage() {
        return mModel.isHaveNext();
    }


    @Override
    public void onClick(SubcategoryItemBean subcategoryItemBean, int position) {
        ClickHelper.onClick(subcategoryItemBean, position);
    }

    @Override
    public void onClick(SubcategoryItemBean subcategoryItemBean, HorizontalSubcategoryAdapter adapter, int position) {
        //即点即播的广播需要转换成广播的播单数据列表。
        if (subcategoryItemBean.getItemType() == SubcategoryItemBean.TYPE_ITEM_BROADCAST_LOCAL
        ||subcategoryItemBean.getItemType() == SubcategoryItemBean.TYPE_ITEM_BROADCAST_CATEGORY
                ||subcategoryItemBean.getItemType() == SubcategoryItemBean.TYPE_ITEM_TV) {
            if (adapter != null) {
                List<SubcategoryItemBean> dataList = adapter.getDataList();
                ArrayList<BroadcastRadioSimpleData> list = mModel.itemBean2Simple(dataList);
                PlayerManagerHelper.getInstance().addBroadcastRadioSimpleItems(list);
            }
        }
        //position 没有用到，所以随便填了一个0
        ClickHelper.onClick(subcategoryItemBean, position);
    }

    private BasePlayStateListener basePlayStateListener = new BasePlayStateListener() {
        @Override
        public void onPlayerPreparing(PlayItem playItem) {
            if (mView != null) {
                mView.setSelected();
            }
        }

        @Override
        public void onPlayerPlaying(PlayItem playItem) {
            if (mView != null) {
                mView.setSelected();
            }
        }

        @Override
        public void onPlayerPaused(PlayItem playItem) {
            if (mView != null) {
                mView.setSelected();
            }
        }

    };

    //@OnLifecycleEvent(Lifecycle.Event.ON_RESUME)
    @Override
    public void registerListener() {
        PlayerManager.getInstance().addPlayControlStateCallback(basePlayStateListener);
    }

    //解决https://app.huoban.com/tables/2100000007530121/items/2300001555864138?userId=1229522问题
    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    public void unregisterListener() {
        PlayerManager.getInstance().removePlayControlStateCallback(basePlayStateListener);
    }
}
