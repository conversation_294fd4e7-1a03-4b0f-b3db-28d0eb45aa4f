package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.util.Log;

import com.hsae.autosdk.settings.AutoSettings;
import com.kaolafm.kradio.flavor.utils.DeviceInfoUtil;
import com.kaolafm.kradio.lib.base.flavor.DeviceInfoSetting;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;


/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-03-22 11:46
 ******************************************/
public final class DeviceInfoSettingImpl implements DeviceInfoSetting {
    private static final String TAG = "DeviceInfoSettingImpl";
    @Override
    public void setInfoForSDK(Context context) {
        try {
            DeviceInfoUtil.setDeviceIdAndCarType(getDeviceId(), getCarType());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public String getDeviceId(Object... args) {
        try {
            Class clazz = Class.forName("com.hsae.autosdk.settings.AutoSettings");
            Method methodInstance = clazz.getDeclaredMethod("getInstance");
            Object obj = methodInstance.invoke(clazz);
            Method methodDeviceEx = clazz.getDeclaredMethod("getDeviceIdEx");
            String deviceId = (String) methodDeviceEx.invoke(obj);
            return deviceId;
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public String getCarType(Object... args) {
        String productType = "";
        int type = -1;
        try {
            type = AutoSettings.getInstance().getProductType();
        } catch (Throwable t) {
            t.printStackTrace();
        }
        Log.i(TAG, "getProductType productType: " + type);
        switch (type) {
            case 0x00:
                productType = "L12F-e";
                break;
            case 0x02:
                productType = "B12L";
                break;
            case 0x03:
                productType = "P02F";
                break;
            case 0x04:
                productType = "P32S";
                break;
            case 0x05:
            case 0x06:
                productType = "P32R";
                break;
            case 0x07:
            case 0x0C:
            case 0x08:
                productType = "L42P";
                break;
            case 0x09:
                productType = "L21B";
                break;
            case 0x0A:
            case 0x0B:
                productType = "P42M";
                break;
            default:
                Log.i(TAG, "unknow product type");
                break;
        }
        return productType;
    }
}
