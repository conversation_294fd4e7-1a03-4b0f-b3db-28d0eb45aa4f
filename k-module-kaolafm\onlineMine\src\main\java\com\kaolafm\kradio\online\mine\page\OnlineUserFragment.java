package com.kaolafm.kradio.online.mine.page;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.kaolafm.kradio.common.widget.OvalImageView;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.lib.base.ui.online.BaseShowHideFragment;
import com.kaolafm.kradio.lib.base.ui.online.BaseViewPagerFragment;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.online.common.event.OnlineLoginEvent;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.kradio.online.mine.MinePageUtils;
import com.kaolafm.kradio.onlineuser.ui.ILoginView;
import com.kaolafm.kradio.onlineuser.ui.LoginPresenter;
import com.kaolafm.kradio.user.UserInfoManager;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonClickReportEvent;
import com.kaolafm.report.event.LoginReportEvent;
import com.kaolafm.report.util.ReportParameterManager;

import org.greenrobot.eventbus.EventBus;

import static com.kaolafm.report.event.ButtonClickReportEvent.ONLINE_BUTTON_SIGN_OUT;

/**
 * 用户信息
 * 蔡佳彬
 */
public class OnlineUserFragment extends BaseShowHideFragment<LoginPresenter> implements ILoginView {

    TextView user_login_tv;

    TextView mine_user_name_tv;
    TextView mine_user_gender_tv;
    TextView mine_user_area_tv;
    TextView mine_user_car_type_tv;
    TextView mine_user_car_code_tv;

    LinearLayout user_dateil_ll;
    OvalImageView user_oiv;
    ImageView user_oiv_bg;
    ImageView online_main_user_vip_iv;


    public OnlineUserFragment() {
        // Required empty public constructor
    }


    public static OnlineUserFragment newInstance() {
        OnlineUserFragment fragment = new OnlineUserFragment();

        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

    }


    @Override
    protected int getLayoutId() {
        return R.layout.online_fragment_user;
    }

    @Override
    protected LoginPresenter createPresenter() {
        return new LoginPresenter(this);
    }

    @Override
    public void initView(View view) {
        user_login_tv = view.findViewById(R.id.user_login_tv);
        mine_user_name_tv = view.findViewById(R.id.mine_user_name_tv);
        mine_user_gender_tv = view.findViewById(R.id.mine_user_gender_tv);
        mine_user_area_tv = view.findViewById(R.id.mine_user_area_tv);
        mine_user_car_type_tv = view.findViewById(R.id.mine_user_car_type_tv);
        mine_user_car_code_tv = view.findViewById(R.id.mine_user_car_code_tv);
        user_dateil_ll = view.findViewById(R.id.user_dateil_ll);
        user_oiv = view.findViewById(R.id.user_oiv);
        user_oiv_bg = view.findViewById(R.id.user_oiv_bg);
        online_main_user_vip_iv = view.findViewById(R.id.online_main_user_vip_iv);
        user_login_tv.setOnClickListener(v -> onClick(v));

        UserInfoManager.getInstance().addUserInfoStateListener(iUserInfoStateListener);
        updateLogin();
    }

    public void updateLogin() {
        if (UserInfoManager.getInstance().isUserLogin()) {
            mine_user_name_tv.setText(UserInfoManager.getInstance().getUserNickName());
            ImageLoader.getInstance().displayCircleImage(getContext(), UserInfoManager.getInstance().getUserFavicon()
                    , user_oiv, ResUtil.getDrawable(R.drawable.online_user_no_login_icon));
            mine_user_gender_tv.setText(TextUtils.isEmpty(
                    UserInfoManager.getInstance().getGender())
                    ? getResources().getString(R.string.user_center_str)
                    : UserInfoManager.getInstance().getGender()
            );//性别
            mine_user_area_tv.setText(TextUtils.isEmpty(
                    UserInfoManager.getInstance().getUserArea())
                    ? getResources().getString(R.string.user_center_str)
                    : UserInfoManager.getInstance().getUserArea());//地区
//            mine_user_car_type_tv.setText(UserInfoManager.getInstance().getUserNickName());//车型
//            mine_user_car_code_tv.setText(UserInfoManager.getInstance().getUserNickName());//车牌号
            user_oiv_bg.setVisibility(View.VISIBLE);
            user_login_tv.setBackground(ResUtil.getDrawable(R.drawable.user_login_tv_bg));
            user_login_tv.setText(getResources().getString(R.string.mine_user_login_tv_text));
            user_dateil_ll.setVisibility(View.VISIBLE);
            if (UserInfoManager.getInstance().getVip() == 1) {
                online_main_user_vip_iv.setVisibility(View.VISIBLE);
            } else {
                online_main_user_vip_iv.setVisibility(View.GONE);
            }

        } else {
            user_oiv.setImageDrawable(ResUtil.getDrawable(R.drawable.online_user_no_login_icon));
            user_oiv_bg.setVisibility(View.GONE);
            user_login_tv.setBackground(ResUtil.getDrawable(R.drawable.user_not_login_tv_bg));
            user_login_tv.setText(getResources().getString(R.string.mine_user_not_login_tv_text));
            user_dateil_ll.setVisibility(View.GONE);
            online_main_user_vip_iv.setVisibility(View.GONE);
        }
    }

    public void onClick(View v) {
        if (!AntiShake.check(v.getId())) {
            if (v.getId() == R.id.user_login_tv) {
                if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
                    ToastUtil.showOnly(getActivity(), R.string.no_net_work_str);
                    return;
                }
                if (UserInfoManager.getInstance().isUserLogin()) {
//                //点击退出按钮事件上报
                    ButtonClickReportEvent event = new ButtonClickReportEvent(ONLINE_BUTTON_SIGN_OUT);
                    event.setPage(MinePageUtils.page);
                    ReportHelper.getInstance().addEvent(event);
                }
                Bundle bundle = new Bundle();
                bundle.putString("type", ReportParameterManager.getInstance().getPage());
                RouterManager.getInstance().jumpPage(RouterConstance.ACTIVITY_URL_LOGIN, bundle);
            }
        }
    }

    UserInfoManager.IUserInfoStateListener iUserInfoStateListener = new UserInfoManager.IUserInfoStateListener() {
        @Override
        public void userLogin() {
            updateLogin();
        }

        @Override
        public void userLogout() {
            updateLogin();
        }

        @Override
        public void userCancel() {

        }
    };

    @Override
    public void failedToGetCode(ApiException e) {

    }

    @Override
    public void startCountdown() {

    }

    @Override
    public void loginSuccess() {

    }

    @Override
    public void loginError() {

    }

    @Override
    public void logoutSuccess() {
//        EventBus.getDefault().post(new OnlineLoginEvent());
    }

    @Override
    public void logoutSError() {

    }

    @Override
    public void toast(String msg) {

    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        UserInfoManager.getInstance().removeUserInfoStateListener(iUserInfoStateListener);
    }

}