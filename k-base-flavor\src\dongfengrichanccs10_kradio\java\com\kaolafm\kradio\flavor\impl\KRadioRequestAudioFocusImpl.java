package com.kaolafm.kradio.flavor.impl;

import android.util.Log;

import com.kaolafm.kradio.lib.base.flavor.KRadioRequestAudioFocusInter;

import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.opensdk.player.logic.PlayerManager;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-01-06 20:46
 ******************************************/
public class KRadioRequestAudioFocusImpl implements KRadioRequestAudioFocusInter {
    private static final String TAG = "KRadioRequestAudioFocusImpl";

    @Override
    public boolean requestAudioFocusBySelf(Object... args) {
        PlayerManager playerManager = PlayerManager.getInstance();
        if (playerManager.isPlayerInitSuccess()) {
            Log.i(TAG, "initThirdPlatform------>start");
            PlayerManager.getInstance().requestAudioFocus();
        } else {
            playerManager.addPlayerInitComplete(new OnPlayerInitCompleteClazz());
        }
        return true;
    }

    private static class OnPlayerInitCompleteClazz implements IPlayerInitCompleteListener {

        @Override
        public void onPlayerInitComplete(boolean b) {
            Log.i(TAG, "initThirdPlatform------>onPlayerInitComplete");
            PlayerManager.getInstance().requestAudioFocus();
        }
    }
}