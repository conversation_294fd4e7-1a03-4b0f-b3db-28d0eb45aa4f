package com.kaolafm.kradio.flavor.impl;

import android.annotation.SuppressLint;
import android.os.Looper;
import android.util.Log;


import com.kaolafm.kradio.lib.base.flavor.KRadioAudioPlayLogicInter;
import com.kaolafm.kradio.lib.utils.IntentUtils;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.player.core.listener.OnPlayLogicListener;
import com.kaolafm.opensdk.player.core.utils.PlayerCustomizeManager;
import com.kaolafm.opensdk.player.logic.PlayerManager;


/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-07-05 12:16
 ******************************************/
public final class KRadioAudioPlayLogicImpl implements KRadioAudioPlayLogicInter {
    private final static String TAG = "KRadioAudioPlayLogicImpl";

    @SuppressLint("LongLogTag")
    public KRadioAudioPlayLogicImpl() {
        PlayerCustomizeManager.getInstance().setPlayLogicListener(() -> {
            int currentFocus = PlayerManager.getInstance().getCurrentAudioFocusStatus();
            Log.i(TAG, "isAppOnForeground--------->currentFocus = " + currentFocus);
            if ((currentFocus < 0)) {
                boolean isAppOnForeground = IntentUtils.getInstance().isAppOnForeground();
                Log.i(TAG, "isAppOnForeground--------->isAppOnForeground = " + isAppOnForeground);
                return !isAppOnForeground;
            }
            return false;
        });
    }

    @SuppressLint("LongLogTag")
    @Override
    public boolean autoPlayAudio(Object... args) {
        return false;
    }

    @Override
    public boolean requestAudioFocus(Object... args) {
        boolean flag = PlayerManager.getInstance().requestAudioFocus();
        return flag;
    }

    @Override
    public boolean  resumeAudioPlayLogic(Object... args) {
        recoverPlay();
        return true;
    }

    @Override
    public boolean restartAudioPlayLogic(Object... args) {
        return false;
    }

    private void recoverPlay() {
        PlayerManager playerManager = PlayerManager.getInstance();
        if (playerManager.isPauseFromUser()) {
            if (playerManager.getCurrentAudioFocusStatus() < 0) {
                requestAudioFocus();
            }
            return;
        }
        if (playerManager.getCurrentAudioFocusStatus() >0) {
            return;
        }
        requestAudioFocus();
        if (!playerManager.isPlaying()) {
            PlayerManagerHelper.getInstance().switchPlayerStatus(false);
        }
    }

    @Override
    public boolean doStartInPlay(Object... args) {
        return false;
    }
}