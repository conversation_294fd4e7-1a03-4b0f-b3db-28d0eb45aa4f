package com.kaolafm.kradio.online.common.view;

import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentActivity;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.kaolafm.kradio.common.helper.SkinHelper;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.dialog.BaseDialogFragment;
import com.kaolafm.kradio.lib.dialog.DialogListener;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.ViewUtil;

/**
 * 支持具有title、message和两个button的dialogFragment
 * 内容copy自Center2BtnDialogFragment.java
 * 如果后续样式统一成具体title的dialog的时候，可以删除Center2BtnDialogFragment.java
 * <AUTHOR>
 **/
public class Center2BtnWithTitleDialogFragment extends BaseDialogFragment {

    private View mTvDialogButtonMainLayout;
    private TextView mTvDialogBottomCancel;
    private TextView mTvDialogBottomDefine;
    private TextView mTvDialogBottomMessage;
    private TextView mTVDialogTitle;
    private DialogListener.OnNativeListener<DialogFragment> mNativeListener;
    private DialogListener.OnPositiveListener<DialogFragment> mPositiveListener;
    private CharSequence title;
    private CharSequence message;
    private CharSequence leftBtnText;
    private CharSequence rightBtnText;
    private boolean canShowButton = true;

    public static Center2BtnWithTitleDialogFragment create() {
        Center2BtnWithTitleDialogFragment fragment = new Center2BtnWithTitleDialogFragment();
        fragment.setGravity(Gravity.CENTER);
        fragment.setWidth(WindowManager.LayoutParams.WRAP_CONTENT);
//        fragment.setHeight((int) (ScreenUtil.getScreenHeight() * 0.14f));
        fragment.setHeight(WindowManager.LayoutParams.WRAP_CONTENT);
        return fragment;
    }

    @Override
    public void onStart() {
        super.onStart();
        Window window = getDialog().getWindow();
        WindowManager.LayoutParams params = window.getAttributes();
        if (SkinHelper.isNightMode()) {
            params.dimAmount = 0.9f;
        } else {
            params.dimAmount = 0.7f;
        }
        window.setAttributes(params);
    }

    @Override
    protected View getContentView() {
        FragmentActivity activity = getActivity();
        View inflate = View.inflate(activity, R.layout.online_dialog_fragment_center_2btn_with_title, null);
        mTvDialogButtonMainLayout = inflate.findViewById(R.id.tv_dialog_button_main_layout);
        mTvDialogBottomCancel = inflate.findViewById(R.id.tv_dialog_bottom_cancel);
        mTvDialogBottomDefine = inflate.findViewById(R.id.tv_dialog_bottom_define);
        mTvDialogBottomMessage = inflate.findViewById(R.id.tv_dialog_bottom_message);
        mTVDialogTitle = inflate.findViewById(R.id.tv_dialog_title);

        mTvDialogBottomCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!AntiShake.check(v.getId())) {
                    if (mNativeListener != null) {
                        mNativeListener.onClick(Center2BtnWithTitleDialogFragment.this);
                    } else {
                        dismiss();
                    }
                }
            }
        });

        mTvDialogBottomDefine.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!AntiShake.check(v.getId())) {
                    if (mPositiveListener != null) {
                        mPositiveListener.onClick(Center2BtnWithTitleDialogFragment.this);
                    }
                }
            }
        });

        mTVDialogTitle.setText(title);
        mTvDialogBottomMessage.setText(message);
        ViewUtil.setViewVisibility(mTvDialogButtonMainLayout, canShowButton ? View.VISIBLE : View.GONE);
        if (!TextUtils.isEmpty(leftBtnText)) {
            mTvDialogBottomCancel.setText(leftBtnText);
        }

        if (!TextUtils.isEmpty(rightBtnText)) {
            mTvDialogBottomDefine.setText(rightBtnText);
        }
        return inflate;
    }

    public void setTitle(CharSequence title) {
        this.title = title;
    }

    public void setMessage(CharSequence message) {
        this.message = message;
    }

    public void setLeftButton(CharSequence leftBtnText) {
        this.leftBtnText = leftBtnText;
    }

    public void setRightButton(CharSequence rightBtnText) {
        this.rightBtnText = rightBtnText;
    }

    public void setOnNativeListener(DialogListener.OnNativeListener<DialogFragment> nativeListener) {
        mNativeListener = nativeListener;
    }

    public void setOnPositiveListener(DialogListener.OnPositiveListener<DialogFragment> positiveListener) {
        mPositiveListener = positiveListener;
    }

    public void setCanShowButton(boolean canShowButton) {
        this.canShowButton = canShowButton;
    }

    @Override
    protected void showAccordingToScreen(int orientation) {
        super.showAccordingToScreen(orientation);
    }
}
