package com.ecarx.sdk.step;


import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import android.text.TextUtils;
import android.util.Log;

import com.ecarx.sdk.ECarX;
import com.ecarx.sdk.oauth2.OAuth2API;
import com.ecarx.sdk.oauth2.event.SendAuth;

/**
 * <AUTHOR>
 **/
public class GetCodeStep extends Step {

    private final ECarX ecarx;

    public GetCodeStep(ECarX eCarX) {
        super();
        this.ecarx = eCarX;
    }

    /**
     * 通过授权去拿code
     */
    @Override
    public void exe() {
        Log.i(ECarX.TAG, "exe:" + getClass().getSimpleName());

        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction("com.ecarx.action.GetCodeSuccess");
        intentFilter.addAction("com.ecarx.action.GetCodeFailure");
        BroadcastReceiver localReceiver = new EcarXCodeReceiver();
        LocalBroadcastManager.getInstance(ecarx.getContext()).registerReceiver(localReceiver, intentFilter);

//        SendAuth.Req req = new SendAuth.Req();
//        req.scope = "get_user_info";//作用域
//        MCOAuthApiUtils.getMCApi().sendReq(req);//申请授权

        SendAuth.Req req = new SendAuth.Req();
        req.openId = "58946fa69154a14398e63334b5d91bc3";
        req.scope = "get_user_info";//作用域
        req.skipConfirm = false;//是否跳过确认授权界面
        try {
            OAuth2API.getInstance().sendReq(req);
        } catch (Exception e) {}
    }

    private class EcarXCodeReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            //注销广播监听
            LocalBroadcastManager.getInstance(ecarx.getContext()).unregisterReceiver(this);
            Log.i(ECarX.TAG, "   onReceive: ecarx intent=" + intent);
            if (intent != null) {
                String action = intent.getAction();
                Log.i(ECarX.TAG, "   onReceive: ecarx action=" + action);
                if ("com.ecarx.action.GetCodeSuccess".equals(action)) {
                    String code = intent.getStringExtra("code");
                    Log.i(ECarX.TAG, "   onReceive: ecarx code=" + code);
                    if (TextUtils.isEmpty(code)) {
                        ecarx.error(new Exception("Ecarx Code is Empty."));
                    } else {
                        ecarx.setEcarxCode(code);
                        ecarx.updateStep();
                        ecarx.nextStep();
                    }
                } else {
                    Log.i(ECarX.TAG, "   onReceive: ecarx code failure.");
                    ecarx.error(new Exception("   获取Ecarx Code失败."));
                }
            }
        }
    }
}
