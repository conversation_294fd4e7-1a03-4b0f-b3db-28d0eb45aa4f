package com.kaolafm.kradio.component.ui.base;

import android.view.View;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;

/**
 * <AUTHOR>
 * @date 2019-08-15
 */
public class BindViewHolder<V extends View, D> extends BaseHolder<D> {

    private V mItemView;

    public BindViewHolder(V itemView, CellBinder<V, D> binder) {
        super(itemView);
        mItemView = itemView;
    }

    public BindViewHolder(V view) {
        super(view);
        mItemView = view;
    }

    @Override
    public void setupData(D d, int position) {
        if (mItemView != null) {
            bind(d, mItemView, position);
        }
    }

    public void bind(D data, V itemView, int position) {
        if (data instanceof CellBinder) {
            ((CellBinder) data).mountView(data, itemView, position);
            if ( data instanceof ItemClickSupport) {
                ((ItemClickSupport) data).setOnItemClickListener(mViewClickListener);
            }
            if ( data instanceof ItemOhterClickSupport) {
                ((ItemOhterClickSupport) data).setOnItemOhterClickListener(mViewCOtherlickListener);
            }
        }
    }

    public void unbind() {
    }

}
