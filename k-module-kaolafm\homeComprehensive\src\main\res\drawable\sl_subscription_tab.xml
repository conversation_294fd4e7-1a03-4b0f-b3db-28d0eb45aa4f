<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_selected="true">
        <shape>
            <corners android:radius="@dimen/default_radius_img" />
            <gradient android:angle="270" android:endColor="@color/bg_subscription_tab_end" android:startColor="@color/bg_subscription_tab_start" android:type="linear" />

        </shape>
    </item>
    <item android:state_selected="false">
        <shape>
            <solid android:color="@color/transparent" />
        </shape>
    </item>
</selector>