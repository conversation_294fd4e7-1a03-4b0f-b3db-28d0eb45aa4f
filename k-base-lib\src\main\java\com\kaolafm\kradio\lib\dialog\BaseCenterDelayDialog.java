package com.kaolafm.kradio.lib.dialog;

import android.content.Context;
import android.content.DialogInterface;
import android.content.res.Resources.NotFoundException;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;

import com.kaolafm.kradio.lib.R;
import com.kaolafm.kradio.lib.dialog.BaseDialog.Build;
import com.kaolafm.kradio.lib.dialog.DialogListener.OnDismissListener;
import com.kaolafm.kradio.lib.dialog.DialogListener.OnNativeListener;
import com.kaolafm.kradio.lib.dialog.DialogListener.OnPositiveListener;

import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.Observer;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

/**
 * 普通中间显示的基类dialog
 *
 * <AUTHOR>
 * @date 2018/5/11
 */

public abstract class BaseCenterDelayDialog {

    protected final Context mContext;

    protected BaseDialog mDialog;

    private Build myBuild;

    protected OnNativeListener mNativeListener;

    protected OnPositiveListener mPositiveListener;

    protected OnDismissListener mDismissListener;

    private DialogInterface.OnDismissListener mOnDismissListener;

    private int autoDismissDelay;

    private boolean isShowing = false;//是否正在显示dialog

    private Disposable myDisposable;
    private FrameLayout mFlDialogCommonContent;

    private View mContentView;

    public BaseCenterDelayDialog(Context context) {
        mContext = context;
        mDialog = new Build(mContext).setDialogView(R.layout.dialog_common_all_screen)
                .setWidthScale(1F)
                .setHeightScale(1F)
                .setCancelOutside(false)
                .build();
    }

    public BaseCenterDelayDialog(Context context,boolean cancelOutside,float widthScale,float hightScale) {
        mContext = context;
        mDialog = new Build(mContext).setDialogView(R.layout.dialog_common_all_screen)
                .setWidthScale(widthScale)
                .setHeightScale(hightScale)
                .setCancelOutside(cancelOutside)
                .build();
    }

    public BaseCenterDelayDialog(Context context,boolean cancelOutside,float widthScale,float hightScale,boolean isFullScreen) {
        mContext = context;
        mDialog = new Build(mContext).setDialogView(R.layout.dialog_common_all_screen)
                .setWidthScale(widthScale)
                .setHeightScale(hightScale)
                .setCancelOutside(cancelOutside)
                .setFullScreen(isFullScreen)
                .build();
    }


    public void show() {
        onCreateView();
        isShowing = true;
        mDialog.show();
    }

    public void dismiss() {
        if (mDialog != null) {
            isShowing = false;
            mDialog.dismiss();
        }
    }

    protected void release() {
        if (mDialog != null) {
            mDialog.setOnDismissListener(null);
        }
    }

    public <T extends BaseCenterDelayDialog> T setOnDismissListener(OnDismissListener dismissListener) {
        mDismissListener = dismissListener;
        return (T) this;
    }

    protected void onCreateView() {

        mOnDismissListener = dialog -> {
            if (mDismissListener != null) {
                mDismissListener.onDismiss();
                mDismissListener = null;
            }
            release();
        };
        mDialog.setOnDismissListener(mOnDismissListener);

        mFlDialogCommonContent = mDialog.getDialogView().findViewById(R.id.fl_dialog_center_content);
        mFlDialogCommonContent.removeAllViews();
        mContentView = null;
        try {
            mContentView = LayoutInflater.from(mContext).inflate(getLayoutId(), null);
            initView(mContentView);
        } catch (NotFoundException e) {
            mContentView = getContentView();
        }
        mFlDialogCommonContent.addView(mContentView);
        autoDismissDelay = delayTimeDimiss();
        delayDismiss();
    }
    protected abstract void initView(View view);

    protected View getContentView() {
        return null;
    }

    /**
     * 获取dialog延时消失时间
     */
    protected abstract int delayTimeDimiss();

    /**
     * 获取dialog显示内容的布局id
     */
    protected abstract int getLayoutId();

    public <T extends BaseCenterDelayDialog> T setOnPositiveListener(OnPositiveListener<T> positiveListener) {
        mPositiveListener = positiveListener;
        return (T) this;
    }

    public <T extends BaseCenterDelayDialog> T setOnNativeListener(OnNativeListener nativeListener) {
        mNativeListener = nativeListener;
        return (T) this;
    }


    private void delayDismiss() {
        if (autoDismissDelay > 0) {
            if (myDisposable != null && !myDisposable.isDisposed()) {
                myDisposable.dispose();
                myDisposable = null;
            }
            Observable.timer(autoDismissDelay, TimeUnit.MILLISECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(new Observer<Long>() {
                        @Override
                        public void onSubscribe(Disposable d) {
                            myDisposable = d;
                        }

                        @Override
                        public void onNext(Long aLong) {
                            delayDismissAction(aLong);
                            dismiss();
                        }

                        @Override
                        public void onError(Throwable e) {

                        }

                        @Override
                        public void onComplete() {

                        }
                    });
        }
    }


    /**
     * dialog是否正在显示
     *
     * @return
     */
    public boolean isShowing() {
        return isShowing;
    }


    public void delayDismissAction(Long aLong){

    }
}
