package com.kaolafm.kradio.online.player.views

import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.PropertyValuesHolder
import android.content.Context
import androidx.constraintlayout.widget.ConstraintLayout
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.DecelerateInterpolator
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.TextView
import com.kaolafm.base.utils.StringUtil
import com.kaolafm.kradio.common.widget.YunTingMusicPlayAnimationView
import com.kaolafm.kradio.k_kaolafm.R
import com.kaolafm.kradio.lib.utils.ResUtil
import com.kaolafm.kradio.live.player.HomeLiveManager
import com.kaolafm.kradio.live.player.RecorderStatus

class RecordButtonBoxLayout : ConstraintLayout {
    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )


    private lateinit var mRecordButton: ImageButton
    private lateinit var recordTipText: TextView
    private lateinit var mListenButton: ViewGroup
    private lateinit var mListenButtonAnim: YunTingMusicPlayAnimationView

    private lateinit var recordTextViewParent: ViewGroup
    private lateinit var recordTextIv: ImageView
    private lateinit var recordTextView: TextView
    private lateinit var mCancelButton: ViewGroup

    private val visiableViews = mutableListOf<View>()

    private var mMeasureSpec: Int = 0

    init {
        LayoutInflater.from(context)
            .inflate(R.layout.online_player_record_button_popup_window, this)
        mRecordButton = findViewById(R.id.recordIv)
        recordTipText = findViewById(R.id.recordTipText)
        mListenButton = findViewById(R.id.live_listen_button_layout)
        mListenButtonAnim = findViewById(R.id.live_listen_anim_image)
        recordTextViewParent = findViewById(R.id.recordTextViewParent)
        recordTextIv = findViewById(R.id.recordTextIv)
        recordTextView = findViewById(R.id.recordTextView)
        mCancelButton = findViewById(R.id.live_cancel_button_layout)

        mMeasureSpec = MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED)
    }

    fun setOnRecordButtonClickListener(listener: View.OnClickListener) {
        mRecordButton.setOnClickListener(listener)
    }

    fun setOnListenButtonClickListener(listener: View.OnClickListener) {
        mListenButton.setOnClickListener(listener)
    }

    fun setOnCancelButtonClickListener(listener: View.OnClickListener) {
        mCancelButton.setOnClickListener(listener)
    }

    fun setOnSendButtonClickListener(listener: View.OnClickListener) {
        recordTextViewParent.setOnClickListener(listener)
    }

    fun notifyStartRecord() {
        mRecordButton.visibility = View.INVISIBLE
        recordTipText.text = ResUtil.getString(R.string.online_player_tip_recording)
        recordTextIv.setImageResource(R.drawable.online_player_recording)
        recordTextViewParent.visibility = View.VISIBLE
    }

    fun stopRecordingAnim(isRecordFinish: Boolean) {
        mRecordButton.setImageResource(R.drawable.online_player_record_mic)
        recordTipText.visibility = if (isRecordFinish) {
            View.INVISIBLE
        } else {
            recordTipText.text = ResUtil.getString(R.string.online_player_tip_record_idle)
            View.VISIBLE
        }
    }

    fun updateRecordText(string: String) {
        recordTextView.text = string
        if (StringUtil.isEmpty(string)) {
            recordTextIv.setImageResource(R.drawable.online_player_send)
        }
    }

    fun notifyUserLoginStatus(logged: Boolean) {
        recordTipText.text = if (logged) {
            ResUtil.getString(R.string.online_player_tip_record_idle)
        } else {
            ResUtil.getString(R.string.online_player_tip_record_unlogin)
        }
    }


    fun hideRecordButton() {
        resetVisibilityViews()
        mListenButton.visibility = View.INVISIBLE
        mCancelButton.visibility = View.INVISIBLE
        mRecordButton.visibility = View.INVISIBLE
        recordTipText.visibility = View.INVISIBLE
        recordTextViewParent.visibility = View.INVISIBLE
    }

    private fun resetVisibilityViews() {
        visiableViews.clear()
        if (mListenButton.visibility == View.VISIBLE) {
            visiableViews.add(mListenButton)
        }
        if (mCancelButton.visibility == View.VISIBLE) {
            visiableViews.add(mCancelButton)
        }
        if (mRecordButton.visibility == View.VISIBLE) {
            visiableViews.add(mRecordButton)
        }
        if (recordTipText.visibility == View.VISIBLE) {
            visiableViews.add(recordTipText)
        }
        if (recordTextViewParent.visibility == View.VISIBLE) {
            visiableViews.add(recordTextViewParent)
        }
    }

    fun startRecordFinishAnim() {
        val recordWidth: Int = mRecordButton.getMeasuredWidth()
        val middle: Int = mRecordButton.getLeft() + recordWidth / 2
        val cancelLeft: Int = mCancelButton.getLeft()
        val listenLeft = mListenButton.left

        val animSet = AnimatorSet()
        animSet.addListener(object : Animator.AnimatorListener {
            override fun onAnimationStart(animation: Animator) {
                mCancelButton.setVisibility(View.VISIBLE)
                mListenButton.visibility = View.VISIBLE
            }

            override fun onAnimationEnd(animation: Animator) {}
            override fun onAnimationCancel(animation: Animator) {}
            override fun onAnimationRepeat(animation: Animator) {}
        })

        val animDuration = 300

        val pvhr =
            PropertyValuesHolder.ofFloat(
                "x",
                middle.toFloat(),
                cancelLeft.toFloat()
            )
        val pvhl =
            PropertyValuesHolder.ofFloat(
                "x",
                middle.toFloat(),
                listenLeft.toFloat()
            )

        val pvha =
            PropertyValuesHolder.ofFloat("alpha", 0f, 1f)

        val oali =
            ObjectAnimator.ofPropertyValuesHolder(mListenButton, pvhl)
        oali.duration = animDuration.toLong()
        val oala =
            ObjectAnimator.ofPropertyValuesHolder(mListenButton, pvha)
        oala.duration = animDuration.toLong()

        val oari: ObjectAnimator =
            ObjectAnimator.ofPropertyValuesHolder(mCancelButton, pvhr)
        oari.duration = animDuration.toLong()
        val oara: ObjectAnimator =
            ObjectAnimator.ofPropertyValuesHolder(mCancelButton, pvha)
        oala.duration = animDuration.toLong()

        animSet.playTogether(oali, oari, oala, oara)
        animSet.interpolator = DecelerateInterpolator()
        animSet.start()
    }

    fun notifyCancel() {
        mCancelButton.setVisibility(View.INVISIBLE)
        recordTipText.text = ResUtil.getString(R.string.online_player_tip_record_idle)
        recordTipText.visibility = View.VISIBLE
        mListenButton.visibility = View.INVISIBLE
    }

    fun showRecordIdle() {
        mCancelButton.visibility = View.INVISIBLE
        mListenButton.visibility = View.INVISIBLE
        mRecordButton.visibility = View.VISIBLE
        recordTipText.text = ResUtil.getString(R.string.online_player_tip_record_idle)
        recordTipText.visibility = View.VISIBLE
        recordTextViewParent.visibility = View.INVISIBLE
    }

    fun showRecordUploading() {
        mCancelButton.setVisibility(View.INVISIBLE)
        mListenButton.visibility = View.INVISIBLE
//        recordTextView.setText(R.string.online_player_live_uploading)
        recordTextIv.setImageResource(R.drawable.online_player_sending)
    }

    fun showRecordUploaded() {
        recordTextIv.setImageResource(R.drawable.online_player_send_finish)
        mRecordButton.removeCallbacks(mChangeToIdleRunnable)
        mRecordButton.postDelayed(mChangeToIdleRunnable, 1000)
    }

    fun showRecordUploadAgainAsFailure() {
        mCancelButton.setVisibility(View.VISIBLE)
        mListenButton.visibility = View.VISIBLE
        recordTextIv.setImageResource(R.drawable.online_player_resend)
        mRecordButton.visibility = View.INVISIBLE
        recordTipText.visibility = View.INVISIBLE
        recordTextViewParent.visibility = View.VISIBLE
    }

    fun startListenTimer() {
        mListenButtonAnim.startAnimation()
        mListenButtonAnim.visibility = View.VISIBLE
    }

    fun stopListenTimer() {
        mListenButtonAnim.visibility = View.INVISIBLE
        mListenButtonAnim.stopAnimation()
    }

    private val mChangeToIdleRunnable =
        Runnable {
            mRecordButton.visibility = View.VISIBLE
            recordTipText.text = ResUtil.getString(R.string.online_player_tip_record_idle)
            recordTextViewParent.visibility = View.INVISIBLE
            HomeLiveManager.getInstance().setRecorderStatus(RecorderStatus.IDLE)
        }
}