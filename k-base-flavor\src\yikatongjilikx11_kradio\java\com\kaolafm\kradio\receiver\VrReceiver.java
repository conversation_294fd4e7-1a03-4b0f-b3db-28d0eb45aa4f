package com.kaolafm.kradio.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.kaolafm.kradio.lib.base.AppManager;


public class VrReceiver extends BroadcastReceiver {
    private static final String TAG = VrReceiver.class.getSimpleName();
    private static final String ECARX_VR_APP_CLOSE = "ecarx.intent.broadcast.action.ECARX_VR_APP_CLOSE";

    @Override
    public void onReceive(Context context, Intent intent) {
        if (intent == null) {
            return;
        }
        Log.i(TAG, "onReceive=" + intent.getAction());
        if (ECARX_VR_APP_CLOSE.equals(intent.getAction())) {
            Log.i(TAG, "vRExitApp");
            AppManager.getInstance().appExit();
        }
    }
}
