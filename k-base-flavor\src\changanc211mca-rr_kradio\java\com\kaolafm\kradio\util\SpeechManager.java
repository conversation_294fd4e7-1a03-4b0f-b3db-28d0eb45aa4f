package com.kaolafm.kradio.util;

import android.Manifest;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Handler;
import android.os.IBinder;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.kradio.bean.SpeechBean;
import com.kaolafm.kradio.clientControlerForKradio.ClientImpl;
import com.kaolafm.kradio.common.utils.PermissionUtils;
import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.flavor.utils.PlayerUtil;
import com.kaolafm.kradio.k_kaolafm.home.HomeDataManager;
import com.kaolafm.kradio.k_kaolafm.home.LoginManager;
import com.kaolafm.kradio.k_kaolafm.home.gallery.PageJumper;
import com.kaolafm.kradio.k_kaolafm.home.player.PlayerHelper;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.IntentUtils;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.BroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.InvalidPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.sdk.client.ErrorInfo;
import com.kaolafm.sdk.client.ISubscribeResult;
import com.kaolafm.sdk.client.PlayResult;

public class SpeechManager {

    public static final String TAG = "SpeechManager ";

    public static final String success = "SUCCESS";
    public static final String fail = "FAIL";

    public static void pushStatus(Context context, String requestCode) {
        String title = "";
        String album = "";
        String activeStatus = "";
        String sceneStatus = "";
        PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
        if (playItem != null) {
            title = playItem.getTitle();
            album = getAlbumName(playItem);
            activeStatus = IntentUtils.getInstance().isAppOnForeground() ? "fg" : "bg";
            sceneStatus = PlayerManager.getInstance().isPlaying() ? "playing" : "paused";
        }

        SpeechBean.SpeechPushBean pushBean = new SpeechBean.SpeechPushBean(requestCode, activeStatus, sceneStatus, album, title);
        String pushString = SpeechBean.getString(pushBean);
        Log.w(TAG, "push:" + pushString);
        if (pushString != null) {
            sendToSpeech(context, pushString);
        }
    }

    private static void sendToSpeech(Context context, String content) {
        if (content == null) {
            Log.w(TAG, "sendToSpeech:" + "content is null");
            return;
        }
        Log.w(TAG, "sendToSpeech:" + content);
        Intent intent = new Intent("com.iflytek.aufofly.sendToSpeech.message");
        intent.putExtra("value", content);//组装后的json push
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        intent.addFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
        intent.setPackage("com.iflytek.cutefly.speechclient.hmi");
        context.sendBroadcast(intent);
    }

    public static void initRegisterReceiver(Context context) {
        ReceiveFromSpeechReceiver receiver = new ReceiveFromSpeechReceiver();
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction("com.iflytek.autofly.handMessage");
        context.registerReceiver(receiver, intentFilter);
    }

    public static class ReceiveFromSpeechReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent != null) {//对应的json字符串
                String requestValue = intent.getStringExtra("value");
                Log.w(TAG, "Receive:" + requestValue);
                SpeechBean.SpeechRequestBean bean = SpeechBean.getBean(requestValue, SpeechBean.SpeechRequestBean.class);
                if (bean != null) {
                    if ("system".equals(bean.focus)
                            || "internetRadio".equals(bean.focus)
                            || "radio".equals(bean.focus)
                            || "all".equals(bean.focus)) {
                        if (!PermissionUtils.checkPermission(context
                                , Manifest.permission.READ_EXTERNAL_STORAGE
                                , Manifest.permission.WRITE_EXTERNAL_STORAGE)) {
                            Log.i(TAG, "无权限提示");
                            sendBooleanResponse(context, bean, false, "请给予云听权限后重试");
                        }
                    }
                    if ("system".equals(bean.focus)) {
                        handleSystem(context, bean);
                    } else if ("internetRadio".equals(bean.focus)) {
                        handleInternetRadio(context, bean);
                    } else if ("radio".equals(bean.focus)) {
                        handleRadio(context, bean);
                    } else if ("all".equals(bean.focus)) {
                        handleAll(context, bean);
                    }
                }
            }
        }

        private void handleRadio(Context context, SpeechBean.SpeechRequestBean bean) {
            switch (bean.semantic.operation) {
                case "OPEN":
                case "PLAY":
                case "SEARCH":
                case "QUERY":
                    startAppToFront(context);
                    String name = bean.semantic.name == null ? "" : bean.semantic.name; //电台名称
                    String code = bean.semantic.code == null ? "" : bean.semantic.code; //电台频点
                    String waveband = bean.semantic.waveband == null ? "" : bean.semantic.waveband; //fm/am
                    String category = bean.semantic.category == null ? "" : bean.semantic.category; //电台类型，音乐、交通、网络等
                    String location = bean.semantic.location == null ? "" : bean.semantic.location; //表示地区

                    String keywords = name + location + category + waveband + code;

                    if (TextUtils.isEmpty(keywords)) {
                        Log.w(TAG, "handleRadio: keyword == null");
                        new Handler().postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                if (!PlayerManager.getInstance().isPlaying()) {
                                    if (PlayerManager.getInstance().getCurPlayItem() instanceof InvalidPlayItem
                                            || PlayerManager.getInstance().getCurPlayItem() == null) {
                                        PlayerUtil.playNetOrLocal();
                                        new Handler().postDelayed(new Runnable() {
                                            @Override
                                            public void run() {
                                                if (PlayerManager.getInstance().isPlaying()) {
                                                    return;
                                                }
                                                if (HomeDataManager.getInstance().isEmptyHistoryAndEmptyData()) {
                                                    HomeDataManager.getInstance().playFirstItemWithCallBack(new HomeDataManager.PlayFirstItemCallback() {
                                                        @Override
                                                        public void success() {
                                                        }

                                                        @Override
                                                        public void failed() {
                                                        }
                                                    });
                                                }
                                            }
                                        }, 1000);
                                    } else {
                                        PlayerManager.getInstance().play();
                                    }
                                }
                            }
                        }, 1000);
                    } else {
                        Log.w(TAG, "handleRadio:" + keywords);
                        ClientImpl clientImpl = new ClientImpl(AppDelegate.getInstance().getContext());
                        try {
                            clientImpl.search5("kaola", 1, 6, 1, null,
                                    null, null, null, keywords, null, new PlayResult() {
                                        @Override
                                        public void onSuccuss() throws RemoteException {
                                        }

                                        @Override
                                        public void onFailure(ErrorInfo errorInfo) throws RemoteException {
                                            sendBooleanResponse(context, bean, false, errorInfo.info);
                                        }
                                    });
                        } catch (Exception e) {
                            Log.w(TAG, "handleRadio:" + e.toString());
                            sendBooleanResponse(context, bean, false);
                        }
                    }
                    break;
                case "CLOSE":
                    new Handler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            AppManager.getInstance().appExit();
                            Log.i(TAG, "EXIT, backToHome");
                        }
                    }, 500);
                case "PAUSE":
                    if (PlayerManager.getInstance().isPlaying()) {
                        PlayerManager.getInstance().pause(true);
                    }
                    break;
                case "PAST":
                    boolean isHasPre = false;
                    if (PlayerManagerHelper.getInstance().hasPreItem()) {
                        PlayerManagerHelper.getInstance().playPre(true);
                        isHasPre = true;
                    }
                    if (!isHasPre) {
                        sendBooleanResponse(context, bean, false, "当前已经是第一个了");
                    }
                    break;
                case "NEXT":
                    boolean isHasNext = false;
                    if (PlayerManagerHelper.getInstance().hasNextItem()) {
                        PlayerManagerHelper.getInstance().playNext(true);
                        isHasNext = true;
                    }
                    if (!isHasNext) {
                        sendBooleanResponse(context, bean, false, "当前已经是最后一个了");
                    }
                    break;
                case "BROADCAST_SONG":
                case "BROADCAST_RADIO":
                case "BROADCAST_PROGRAM":
                    PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
                    if (playItem != null) {
                        String albumName = getAlbumName(playItem);
                        if (albumName != null) {
                            sendBooleanResponse(context, bean, true, albumName);
                        } else {
                            sendBooleanResponse(context, bean, false);
                        }
                    }
                    break;
                case "COLLECT":
                    if (!UserInfoManager.getInstance().isUserBound()) {
                        if (IntentUtils.getInstance().isAppOnForeground()) {
                            PageJumper.getInstance().jumpToKRadioLoginPage();
                            LoginManager.getInstance().registerLoginListener(
                                    new LoginManager.LoginListener() {
                                        @Override
                                        public void onLoginStateChange(int cp, boolean isLogin) {
                                            if (isLogin) {
                                                collect(context, bean);
                                                ToastUtil.showNormal(context, "订阅成功");
                                            }
                                            LoginManager.getInstance().unregisterLoginListener(this);
                                        }

                                        @Override
                                        public void onCancel() {
                                            LoginManager.getInstance().unregisterLoginListener(this);
                                        }
                                    }
                            );
                        } else {
                            sendBooleanResponse(context, bean, false, "请登陆后进行操作");
                        }
                        return;
                    }
                    collect(context, bean);
                    break;
                case "CANCEL_COLLECT":
                    if (!UserInfoManager.getInstance().isUserBound()) {
                        if (IntentUtils.getInstance().isAppOnForeground()) {
                            PageJumper.getInstance().jumpToKRadioLoginPage();
                            LoginManager.getInstance().registerLoginListener(
                                    new LoginManager.LoginListener() {
                                        @Override
                                        public void onLoginStateChange(int cp, boolean isLogin) {
                                            if (isLogin) {
                                                cancelCollect(context, bean);
                                                ToastUtil.showNormal(context, "取消订阅成功");
                                            }
                                            LoginManager.getInstance().unregisterLoginListener(this);
                                        }

                                        @Override
                                        public void onCancel() {
                                            LoginManager.getInstance().unregisterLoginListener(this);
                                        }
                                    }
                            );
                        } else {
                            sendBooleanResponse(context, bean, false, "请登陆后进行操作");
                        }
                        return;
                    }
                    cancelCollect(context, bean);
                    break;
            }
        }

        private void handleAll(Context context, SpeechBean.SpeechRequestBean bean) {
            pushStatus(context, bean.requestCode);
        }

        private void handleInternetRadio(Context context, SpeechBean.SpeechRequestBean bean) {
//            startAppToFront(context);
            switch (bean.semantic.operation) {
                case "OPEN":
                case "PLAY":
                case "ORDER":
                case "QUERY":
                    startAppToFront(context); //先打开app

                    String presenter = bean.semantic.presenter; //主播
                    String program = bean.semantic.program; //专辑名/节目名
                    String tags = bean.semantic.tags;
                    String famous = bean.semantic.famous;

                    String keywords = "";
                    String artist = "";
                    if (presenter != null) {
                        artist = presenter;
                    }
                    if (famous != null) {
                        artist = famous;
                    }

                    if (program != null) {
                        keywords = program;
                        if (!TextUtils.isEmpty(artist)) {
                            keywords = program + artist;
                        }
                    }
                    if (TextUtils.isEmpty(keywords)) {
                        keywords = artist;
                        if (!TextUtils.isEmpty(tags)) {
                            keywords = artist + tags;
                        }
                    }
                    if (TextUtils.isEmpty(keywords)) {
                        Log.w(TAG, "handleInternetRadio: keyword == null");
                        new Handler().postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                if (!PlayerManager.getInstance().isPlaying()) {
                                    if (PlayerManager.getInstance().getCurPlayItem() instanceof InvalidPlayItem
                                            || PlayerManager.getInstance().getCurPlayItem() == null) {
                                        PlayerUtil.playNetOrLocal();
                                        new Handler().postDelayed(new Runnable() {
                                            @Override
                                            public void run() {
                                                if (PlayerManager.getInstance().isPlaying()) {
                                                    return;
                                                }
                                                if (HomeDataManager.getInstance().isEmptyHistoryAndEmptyData()) {
                                                    HomeDataManager.getInstance().playFirstItemWithCallBack(new HomeDataManager.PlayFirstItemCallback() {
                                                        @Override
                                                        public void success() {
                                                        }

                                                        @Override
                                                        public void failed() {
                                                        }
                                                    });
                                                }
                                            }
                                        }, 1000);
                                    } else {
                                        PlayerManager.getInstance().play();
                                    }
                                }
                            }
                        }, 1000);
                    } else {
                        Log.w(TAG, "handleInternetRadio:" + keywords + " " + artist);
                        ClientImpl clientImpl = new ClientImpl(AppDelegate.getInstance().getContext());
                        try {
                            clientImpl.search5("kaola", 1, 6, 1, null,
                                    null, null, null, keywords, null, new PlayResult() {
                                        @Override
                                        public void onSuccuss() throws RemoteException {
                                        }

                                        @Override
                                        public void onFailure(ErrorInfo errorInfo) throws RemoteException {
                                            sendBooleanResponse(context, bean, false, errorInfo.info);
                                        }
                                    });
                        } catch (Exception e) {
                            sendBooleanResponse(context, bean, false);
                        }
                    }

                    break;
                case "CLOSE":
                    new Handler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            AppManager.getInstance().appExit();
                            Log.i(TAG, "EXIT, backToHome");
                        }
                    }, 500);
                case "PAUSE":
                    if (PlayerManager.getInstance().isPlaying()) {
                        PlayerManager.getInstance().pause(true);
                    }
                    break;
                case "PAST":
                    boolean isHasPre = false;
                    if (PlayerManagerHelper.getInstance().hasPreItem()) {
                        PlayerManagerHelper.getInstance().playPre(true);
                        isHasPre = true;
                    }
                    if (!isHasPre) {
                        sendBooleanResponse(context, bean, false, "当前已经是第一个了");
                    }
                    break;
                case "NEXT":
                    boolean isHasNext = false;
                    if (PlayerManagerHelper.getInstance().hasNextItem()) {
                        PlayerManagerHelper.getInstance().playNext(true);
                        isHasNext = true;
                    }
                    if (!isHasNext) {
                        sendBooleanResponse(context, bean, false, "当前已经是最后一个了");
                    }
                    break;
                case "SEARCH_PROGRAM":
                case "BROADCAST_PROGRAM":
                    PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
                    if (playItem != null) {
                        String albumName = getAlbumName(playItem);
                        if (albumName != null) {
                            sendBooleanResponse(context, bean, true, albumName);
                        } else {
                            sendBooleanResponse(context, bean, false);
                        }
                    }
                    break;
                case "COLLECT":
                    if (!UserInfoManager.getInstance().isUserBound()) {
                        if (IntentUtils.getInstance().isAppOnForeground()) {
                            PageJumper.getInstance().jumpToKRadioLoginPage();
                            LoginManager.getInstance().registerLoginListener(
                                    new LoginManager.LoginListener() {
                                        @Override
                                        public void onLoginStateChange(int cp, boolean isLogin) {
                                            if (isLogin) {
                                                collect(context, bean);
                                                ToastUtil.showNormal(AppDelegate.getInstance().getContext(), "订阅成功");
                                            }
                                            LoginManager.getInstance().unregisterLoginListener(this);
                                        }

                                        @Override
                                        public void onCancel() {
                                            LoginManager.getInstance().unregisterLoginListener(this);
                                        }
                                    }
                            );
                        } else {
                            sendBooleanResponse(context, bean, false, "请登陆后进行操作");
                        }
                        return;
                    }
                    collect(context, bean);
                    break;
                case "CANCEL_COLLECT":
                    if (!UserInfoManager.getInstance().isUserBound()) {
                        if (IntentUtils.getInstance().isAppOnForeground()) {
                            PageJumper.getInstance().jumpToKRadioLoginPage();
                            LoginManager.getInstance().registerLoginListener(
                                    new LoginManager.LoginListener() {
                                        @Override
                                        public void onLoginStateChange(int cp, boolean isLogin) {
                                            if (isLogin) {
                                                cancelCollect(context, bean);
                                                ToastUtil.showNormal(context, "取消订阅成功");
                                            }
                                            LoginManager.getInstance().unregisterLoginListener(this);
                                        }

                                        @Override
                                        public void onCancel() {
                                            LoginManager.getInstance().unregisterLoginListener(this);
                                        }
                                    }
                            );
                        } else {
                            sendBooleanResponse(context, bean, false, "请登陆后进行操作");
                        }
                        return;
                    }
                    cancelCollect(context, bean);
                    break;

            }
        }

        public void collect(Context context, SpeechBean.SpeechRequestBean bean) {
            long subscribedId = PlayerManagerHelper.getInstance().getSubscribeId();
            try {
                ClientImpl client = new ClientImpl(AppDelegate.getInstance().getContext());
                client.subscribe(subscribedId, new ISubscribeResult() {
                    @Override
                    public void onSuccuss() throws RemoteException {
                        sendBooleanResponse(context, bean, true);
                    }

                    @Override
                    public void onFailure(ErrorInfo errorInfo) throws RemoteException {
                        sendBooleanResponse(context, bean, false);
                    }

                    @Override
                    public IBinder asBinder() {
                        return null;
                    }
                });
            } catch (RemoteException e) {
                e.printStackTrace();
                sendBooleanResponse(context, bean, false);
            }
        }

        public void cancelCollect(Context context, SpeechBean.SpeechRequestBean bean) {
            long subscribeId = PlayerManagerHelper.getInstance().getSubscribeId();
            try {
                ClientImpl client = new ClientImpl(AppDelegate.getInstance().getContext());
                client.unsubscribe(subscribeId, new ISubscribeResult() {
                    @Override
                    public void onSuccuss() throws RemoteException {
                        sendBooleanResponse(context, bean, true);
                    }

                    @Override
                    public void onFailure(ErrorInfo errorInfo) throws RemoteException {
                        sendBooleanResponse(context, bean, false);
                    }

                    @Override
                    public IBinder asBinder() {
                        return null;
                    }
                });
            } catch (RemoteException e) {
                sendBooleanResponse(context, bean, false);
                e.printStackTrace();
            }
        }

        public void sendBooleanResponse(Context context, SpeechBean.SpeechRequestBean bean, boolean isSuccess) {
            sendBooleanResponse(context, bean, isSuccess, null);
        }

        public void sendBooleanResponse(Context context, SpeechBean.SpeechRequestBean bean, boolean isSuccess, String msg) {
            if ("YES".equals(bean.needResponse)) {
                SpeechBean.SpeechResponseBean responseBean
                        = new SpeechBean.SpeechResponseBean(bean.requestCode, isSuccess, msg);
                String responseString = SpeechBean.getString(responseBean);
                if (responseString != null) {
                    sendToSpeech(context, responseString);
                }
            }
        }

        private static void handleSystem(Context context, SpeechBean.SpeechRequestBean bean) {
            if ("REQUEST_AUDIOFOCUS".equals(bean.semantic.service)) {
                boolean isDone = PlayerManager.getInstance().requestAudioFocus();
                if ("YES".equals(bean.needResponse) && !isDone) {
                    SpeechBean.SpeechResponseBean.SpeechResponseSemanticBean responseSemanticBean
                            = new SpeechBean.SpeechResponseBean.SpeechResponseSemanticBean();
                    responseSemanticBean.service = "REQUEST_AUDIOFOCUS";
                    if (isDone) {
                        responseSemanticBean.status = success;
                        responseSemanticBean.message = "请求成功";
                    } else {
                        responseSemanticBean.status = fail;
                        responseSemanticBean.message = "请求失败";
                    }
                    SpeechBean.SpeechResponseBean responseBean
                            = new SpeechBean.SpeechResponseBean(bean.focus, bean.requestCode, responseSemanticBean);
                    String responseString = SpeechBean.getString(responseBean);
                    if (responseString != null) {
                        sendToSpeech(context, responseString);
                    }
                }
            } else if ("ABANDON_AUDIOFOCUS".equals(bean.semantic.service)) {
                boolean isDone = PlayerManager.getInstance().abandonAudioFocus();
                if ("YES".equals(bean.needResponse) && !isDone) {
                    SpeechBean.SpeechResponseBean.SpeechResponseSemanticBean responseSemanticBean
                            = new SpeechBean.SpeechResponseBean.SpeechResponseSemanticBean();
                    responseSemanticBean.service = "ABANDON_AUDIOFOCUS";
                    if (isDone) {
                        responseSemanticBean.status = success;
                        responseSemanticBean.message = "请求成功";
                    } else {
                        responseSemanticBean.status = fail;
                        responseSemanticBean.message = "请求失败";
                    }
                    SpeechBean.SpeechResponseBean responseBean
                            = new SpeechBean.SpeechResponseBean(bean.focus, bean.requestCode, responseSemanticBean);
                    String responseString = SpeechBean.getString(responseBean);
                    if (responseString != null) {
                        sendToSpeech(context, responseString);
                    }
                }
            } else if ("APP".equals(bean.semantic.service) && "云听".equals(bean.semantic.name)) {
                if ("LAUNCH".equals(bean.semantic.operation)) {
                    boolean isSuccess = false;
                    try {
                        startAppToFront(context);
                        isSuccess = true;
                    } catch (Exception e) {
                    }
                    if ("YES".equals(bean.needResponse) && !isSuccess) {
                        SpeechBean.SpeechResponseBean.SpeechResponseSemanticBean responseSemanticBean
                                = new SpeechBean.SpeechResponseBean.SpeechResponseSemanticBean();
                        responseSemanticBean.service = "APP";
                        responseSemanticBean.name = bean.semantic.name;
                        if (isSuccess) {
                            responseSemanticBean.status = success;
                            responseSemanticBean.message = "打开成功";
                        } else {
                            responseSemanticBean.status = fail;
                            responseSemanticBean.message = "应用打开失败";
                        }
                        SpeechBean.SpeechResponseBean responseBean
                                = new SpeechBean.SpeechResponseBean(bean.focus, bean.requestCode, responseSemanticBean);
                        String responseString = SpeechBean.getString(responseBean);
                        if (responseString != null) {
                            sendToSpeech(context, responseString);
                        }
                    }
                } else if ("EXIT".equals(bean.semantic.operation)) {
                    if (PlayerManager.getInstance().isPlaying()) {
                        PlayerManager.getInstance().pause();
                        Log.i(TAG, "EXIT, pause");
                    }
//                    PlayerManager.getInstance().abandonAudioFocus();
                    new Handler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
//                            Intent homeIntent = new Intent(Intent.ACTION_MAIN);
//                            homeIntent.addCategory(Intent.CATEGORY_HOME);
//                            homeIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED);
//                            AppDelegate.getInstance().getContext().startActivity(homeIntent);
                            AppManager.getInstance().appExit();
                            Log.i(TAG, "EXIT, backToHome");
                        }
                    }, 500);
                    if ("YES".equals(bean.needResponse)) {
                        SpeechBean.SpeechResponseBean.SpeechResponseSemanticBean responseSemanticBean
                                = new SpeechBean.SpeechResponseBean.SpeechResponseSemanticBean();
                        responseSemanticBean.service = "APP";
                        responseSemanticBean.name = bean.semantic.name;
                        responseSemanticBean.status = success;
                        responseSemanticBean.message = "退出成功";
                        SpeechBean.SpeechResponseBean responseBean
                                = new SpeechBean.SpeechResponseBean(bean.focus, bean.requestCode, responseSemanticBean);
                        String responseString = SpeechBean.getString(responseBean);
                        if (responseString != null) {
                            sendToSpeech(context, responseString);
                        }
                    }
                }
            }
        }
    }

    private static void startAppToFront(Context context) {
        Log.i(TAG, "startActivity");
        boolean isForeground = AppDelegate.getInstance().isAppForeground();
        if (!isForeground) {
            String packageName = context.getPackageName();
            Log.i(TAG, "background:" + packageName);
            Intent startIntent = context.getPackageManager().getLaunchIntentForPackage(packageName);
            if (startIntent != null) {
                Log.i(TAG, "startActivity: nonnull");
                context.startActivity(startIntent);
            }
        }
    }

    private static String getAlbumName(PlayItem playItem) {
        if (playItem instanceof BroadcastPlayItem) {
            String frequencyChannel = ((BroadcastPlayItem) playItem).getFrequencyChannel();
            String albumName = ((BroadcastPlayItem) playItem).getInfoData().getAlbumName();
            if (!StringUtil.isEmpty(frequencyChannel)) {
                return String.format(AppDelegate.getInstance().getContext().getResources().getString(R.string.one_zh_cn_char_joint_str), albumName, ResUtil.getString(R.string.full_str), frequencyChannel);
            } else {
                return albumName;
            }
        }
        return playItem.getAlbumTitle();
    }

}
