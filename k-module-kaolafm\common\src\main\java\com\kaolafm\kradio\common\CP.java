package com.kaolafm.kradio.common;

import androidx.annotation.IntDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

public interface CP {
    public static final int KRadio = 0;
    public static final int KaoLaFM = 1;
    public static final int QQ = 2;
    public static final int XiMaLaYa = 3;
    public static final int WangYiYun = 4;
    public static final int QingTing = 5;
    public static final int XiaMi = 6;

    @Retention(RetentionPolicy.SOURCE)
    @IntDef({KRadio,KaoLaFM, QQ, XiMaLaYa, WangYiYun, QingTing, XiaMi})
    public @interface CpType {
    }
}