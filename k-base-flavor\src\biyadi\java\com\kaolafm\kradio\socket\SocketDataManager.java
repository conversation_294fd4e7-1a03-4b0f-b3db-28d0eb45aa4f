package com.kaolafm.kradio.socket;

import android.text.TextUtils;
import android.util.LongSparseArray;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.kaolafm.kradio.k_kaolafm.home.data.Category;
import com.kaolafm.kradio.k_kaolafm.home.data.DataConverter;
import com.kaolafm.kradio.k_kaolafm.home.gallery.DataHandler;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.location.LocationManager;
import com.kaolafm.kradio.lib.utils.CommonRequestParamsUtil;
import com.kaolafm.opensdk.api.operation.model.column.AlbumDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.AudioDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.BroadcastDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.CategoryColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.Column;
import com.kaolafm.opensdk.api.operation.model.column.ColumnGrp;
import com.kaolafm.opensdk.api.operation.model.column.ColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.LiveProgramDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.RadioDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.RadioQQMusicDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.SearchResultColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.WebViewColumnMember;
import com.kaolafm.opensdk.http.core.RuntimeTypeAdapterFactory;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class SocketDataManager implements ISocketDataInter {

    private List<Category> mCategories = new ArrayList<>();
    private LongSparseArray<List<Category.Group>> mGroups = new LongSparseArray();
    private SocketService.HomeRefreshListener mHomeRefreshListener;
    private List<IGetSocketDataCallBack> mIGetSocketDataCallBackList = new ArrayList<>();
    private IGetSocketDataCallBack onlyThisCallBack;
    private boolean MyOnlyThis = false;

    private static class SingletonHoler {
        private static SocketDataManager instance = new SocketDataManager();
    }

    public SocketDataManager() {
    }

    public static SocketDataManager getInstance() {
        return SingletonHoler.instance;
    }

    @Override
    public void initSocket() {
        SocketManager.getInstance().loginSocket(AppDelegate.getInstance().getContext(), createLinkStringByGet(
                CommonRequestParamsUtil.getCommonParams()));
        if (mHomeRefreshListener == null) {
            mHomeRefreshListener = new HomeResfreshListener();
            SocketManager.getInstance().addHomeRefreshListener(mHomeRefreshListener);
        }
    }

    @Override
    public void releaseSocket() {
        SocketManager.getInstance().logoutSocket(AppDelegate.getInstance().getContext());
        if (mHomeRefreshListener != null) {
            SocketManager.getInstance().removeHomeRefreshListener(mHomeRefreshListener);
        }
        removeAllIGetSocketDataCallBack();
    }

    @Override
    public void notifySocketDataOnlyThis(boolean onlyThis, IGetSocketDataCallBack getSocketDataCallBack) {
        MyOnlyThis = onlyThis;
        onlyThisCallBack = getSocketDataCallBack;
        getSocketData();
    }

    @Override
    public void addIGetSocketDataCallBack(IGetSocketDataCallBack getSocketDataCallBack) {
        if (mIGetSocketDataCallBackList != null && !mIGetSocketDataCallBackList.contains(getSocketDataCallBack)) {
            mIGetSocketDataCallBackList.add(getSocketDataCallBack);
        }
    }

    @Override
    public void removeIGetSocketDataCallBack(IGetSocketDataCallBack getSocketDataCallBack) {
        if (mIGetSocketDataCallBackList != null && mIGetSocketDataCallBackList.contains(getSocketDataCallBack)) {
            mIGetSocketDataCallBackList.remove(getSocketDataCallBack);
        }
    }


    @Override
    public void requestRefresh() {
        if (mHomeRefreshListener != null) {
            mHomeRefreshListener.requestRefresh();
        }
    }

    @Override
    public void getSocketData() {
        if (mCategories.isEmpty()) {
            requestRefresh();
        } else {
            socketDataNotify();
        }
    }


    //实现了SocketService.HomeResfreshListener监听，用于接收socket传递过来数据，刷新
    private class HomeResfreshListener implements SocketService.HomeRefreshListener {
        @Override
        public void refresh(Object msg) {
            mCategories.clear();
            mGroups.clear();
            Gson gson = new GsonBuilder().registerTypeAdapterFactory(RuntimeTypeAdapterFactory.of(ColumnGrp.class)
                    .registerSubtype(ColumnGrp.class)
                    .registerSubtype(Column.class))
                    .registerTypeAdapterFactory(RuntimeTypeAdapterFactory.of(ColumnMember.class)
                            .registerSubtype(AlbumDetailColumnMember.class)
                            .registerSubtype(AudioDetailColumnMember.class)
                            .registerSubtype(BroadcastDetailColumnMember.class)
                            .registerSubtype(CategoryColumnMember.class)
                            .registerSubtype(LiveProgramDetailColumnMember.class)
                            .registerSubtype(RadioDetailColumnMember.class)
                            .registerSubtype(RadioQQMusicDetailColumnMember.class)
                            .registerSubtype(SearchResultColumnMember.class)
                            .registerSubtype(WebViewColumnMember.class))
                    .create();
            List<ColumnGrp> grps = gson.fromJson(msg.toString(), RefreshResult.class).getResult();
            for (int i = 0; i < grps.size(); i++) {
                ColumnGrp cg = grps.get(i);
                Category cate = DataConverter.toCategory(cg);
                if (cate != null) {
                    mCategories.add(cate);
                }

                //第二层,组信息
                List<? extends ColumnGrp> secondColumnGrp = cg.getChildColumns();

                List<Category.Group> groups = new ArrayList<>();

                if (secondColumnGrp != null && !secondColumnGrp.isEmpty()) {
                    for (int j = 0; j < secondColumnGrp.size(); j++) {
                        ColumnGrp secondChild = secondColumnGrp.get(j);
                        if (secondChild instanceof Column) {
                            Column column = (Column) secondChild;
                            Category.Group group = DataConverter.toGroup(column);
                            if (group != null && !TextUtils.isEmpty(group.title)) {
                                groups.add(group);
                            }
                        }
                    }
                }

                List handleGroups = DataHandler.handleData(null, cate.id, groups);
                mGroups.put(cate.id, handleGroups);
            }

            socketDataNotify();
        }

        @Override
        public void requestRefresh() {
            HashMap<String, String> msg = new HashMap<>();
            //zone
//            String zone = "mainPage";

            //经纬度:默认北京
            String lng = "116.23";
            String lat = "39.54";
            LocationManager.MapLocation location = LocationManager.getInstance().getMapLocation();
            if (location != null) {
                lng = String.valueOf(location.getLongitude());
                lat = String.valueOf(location.getLatitude());
            }


//            msg.put("zone", zone);
            msg.put("lng", lng);
            msg.put("lat", lat);
            msg.put("parentCode", "0");
            msg.put("isRecursive", "1");
            msg.putAll(CommonRequestParamsUtil.getCommonParams());
            Gson gson = new GsonBuilder().enableComplexMapKeySerialization().create();
            JSONObject jsonObject = null;
            try {
                jsonObject = new JSONObject(gson.toJson(msg));
            } catch (JSONException e) {
                e.printStackTrace();
            }
            SocketManager.getInstance().requestRefresh(jsonObject);
        }

        @Override
        public void connectError() {
            socketConnectErrorNotify(0);
        }
    }


    /**
     * 通知注册的接口刷新数据
     */
    private void socketDataNotify() {
        if (MyOnlyThis) {
            if (onlyThisCallBack != null) {
                onlyThisCallBack.onResult(mCategories, mGroups);
                MyOnlyThis = false;
                onlyThisCallBack = null;
                return;
            }
        } else {
            if (onlyThisCallBack != null) {
                onlyThisCallBack.onResult(mCategories, mGroups);
                MyOnlyThis = false;
                onlyThisCallBack = null;
            }
        }

        if (mIGetSocketDataCallBackList != null) {
            //Log.i(TAG, "onResult: callback != null.");
            for (IGetSocketDataCallBack callback : mIGetSocketDataCallBackList) {
                callback.onResult(mCategories, mGroups);
            }
        }
    }

    /**
     * 通知socket连接错误码
     */
    private void socketConnectErrorNotify(int errorCode) {
        if (MyOnlyThis) {
            if (onlyThisCallBack != null) {
                onlyThisCallBack.onResult(mCategories, mGroups);
                MyOnlyThis = false;
                onlyThisCallBack = null;
                return;
            }
        } else {
            if (onlyThisCallBack != null) {
                onlyThisCallBack.onResult(mCategories, mGroups);
                MyOnlyThis = false;
                onlyThisCallBack = null;
            }
        }
        if (mIGetSocketDataCallBackList != null) {
            //Log.i(TAG, "onResult: callback != null.");
            for (IGetSocketDataCallBack callback : mIGetSocketDataCallBackList) {
                callback.onError(errorCode);
            }
        }
    }

    /**
     * 移除所有监听（慎用，会清除监听列表里的所有数据）
     */
    public void removeAllIGetSocketDataCallBack() {
        if (mIGetSocketDataCallBackList != null) {
            mIGetSocketDataCallBackList.clear();
        }
    }

    /**
     * 　　* 把数组所有元素排序，并按照“参数=参数值”的模式用“&”字符拼接成字符串
     * 　　* @param params 需要排序并参与字符拼接的参数组
     * 　　* @return 拼接后字符串
     * 　　* @throws UnsupportedEncodingException
     */
    private static String createLinkStringByGet(Map<String, String> params) {
        List<String> keys = new ArrayList<String>(params.keySet());
        String prestr = "";
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            String value = params.get(key);
            if (i == keys.size() - 1) {//拼接时，不包括最后一个&字符
                prestr = prestr + key + "=" + value;
            } else {
                prestr = prestr + key + "=" + value + "&";
            }
        }
        return prestr;
    }
}
