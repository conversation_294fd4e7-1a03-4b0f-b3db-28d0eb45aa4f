package com.kaolafm.kradio.flavor.impl;

import android.annotation.SuppressLint;
import android.util.Log;

import com.kaolafm.kradio.lib.base.flavor.KRadioAudioPlayLogicInter;
import com.kaolafm.kradio.lib.utils.IntentUtils;
import com.kaolafm.sdk.core.mediaplayer.AudioStatusManager;
import com.kaolafm.sdk.core.mediaplayer.OnPlayLogicListener;



/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-07-05 12:16
 ******************************************/
public final class KRadioAudioPlayLogicImpl implements KRadioAudioPlayLogicInter {
    private final static String TAG = "KRadioAudioPlayLogicImpl";

    public KRadioAudioPlayLogicImpl() {
        AudioStatusManager.getInstance().setOnPlayLogicListener(new OnPlayLogicListener() {
            @Override
            public boolean onPlayLogicDispose() {
                boolean isAppOnForeground = IntentUtils.getInstance().isAppOnForeground();
                Log.i(TAG, "isAppOnForeground--------->isAppOnForeground = " + isAppOnForeground);
                return !isAppOnForeground;
            }
        });
    }

    @SuppressLint("LongLogTag")
    @Override
    public boolean autoPlayAudio(Object... args) {
        return false;
    }

    @Override
    public boolean requestAudioFocus(Object... args) {
        return false;
    }

    @Override
    public boolean resumeAudioPlayLogic(Object... args) {
        return false;
    }

    @Override
    public boolean restartAudioPlayLogic(Object... args) {
        return false;
    }

    @Override
    public boolean doStartInPlay(Object... args) {
        return false;
    }
}
