package com.kaolafm.kradio.activity.comprehensive.ui;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.GradientDrawable;
import android.media.MediaMetadataRetriever;
import android.media.MediaPlayer;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.VideoView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.bitmap_recycle.BitmapPool;
import com.bumptech.glide.load.resource.bitmap.BitmapTransformation;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.transition.Transition;
import com.kaolafm.base.utils.DateUtil;
import com.kaolafm.kradio.common.router.RouterManager;
//import com.kaolafm.kradio.home.comprehensive.gallery.PageJumper;
import com.kaolafm.kradio.component.ComponentClient;
import com.kaolafm.kradio.component.ui.base.skin.SkinHelper;
import com.kaolafm.kradio.component.ui.base.view.OvalImageView;
import com.kaolafm.kradio.constant.MessageButtonActionComponentConst;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.report.ReportUtil;
import com.kaolafm.kradio.common.utils.CommonUtils;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.api.CrashMessageBaseBean;
import com.kaolafm.opensdk.api.activity.ActivityRequest;
import com.kaolafm.opensdk.api.activity.model.ActivityInfo;
import com.kaolafm.opensdk.crash.CrashPlayerHelper;
import com.kaolafm.opensdk.crash.Icrashstate;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.event.DialogExposureEvent;
import com.kaolafm.report.util.ReportConstants;
import com.kaolafm.report.util.ReportParameterManager;

import java.io.File;
import java.security.MessageDigest;
import java.util.List;
import java.util.Map;

import static com.bumptech.glide.load.resource.bitmap.VideoDecoder.FRAME_OPTION;

/**
 * 活动详情
 * 调用示例：
 * MessageBubbleDialogFragment dialogFragment = (MessageBubbleDialogFragment) new Dialogs.Builder().setType(Dialogs.TYPE_MESSAGE_BUBBLE).create();
 * dialogFragment.setBubbleType(MessageBubbleDialogFragment.TYPE_DANGER)
 * .setSceneBitmap(BitmapFactory.decodeResource(getResources(), R.drawable.online_message_bubble_thunderbolt))
 * .setTitle("国家应急广播")
 * .setSubTitle("石家庄市发布暴雨雷电红色预警")
 * .setButtons(new ArrayList<MessageBubbleDialogFragment.MessageBubbleButton>() {{
 * add(new MessageBubbleDialogFragment.MessageBubbleButton("查看详情", R.id.online_message_bubble_button_1_id));
 * add(new MessageBubbleDialogFragment.MessageBubbleButton("倒计时", 20L, R.id.online_message_bubble_button_2_id));
 * add(new MessageBubbleDialogFragment.MessageBubbleButton("删除", R.drawable.online_search_icon_delete, R.id.online_message_bubble_button_3_id));
 * add(new MessageBubbleDialogFragment.MessageBubbleButton("确认", R.drawable.online_user_no_login_icon, 15, R.id.online_message_bubble_button_4_id));
 * }}, new View.OnClickListener() {
 *
 * @Override public void onClick(View v) {
 * int id = v.getId();
 * if (id == R.id.online_message_bubble_button_1_id) {
 * Log.e(TAG, "点击按钮：1");
 * } else if (id == R.id.online_message_bubble_button_2_id) {
 * Log.e(TAG, "点击按钮：2");
 * } else if (id == R.id.online_message_bubble_button_3_id) {
 * Log.e(TAG, "点击按钮：3");
 * } else if (id == R.id.online_message_bubble_button_4_id) {
 * Log.e(TAG, "点击按钮：4");
 * }
 * }
 * }).show(getSupportFragmentManager(), "MessageBubbleDialogFragment");
 */
public class ActivitysDetailsDialogFragment extends Dialog {
    private ImageView bubbleBg;
    private TextView play_iv;
    private View replay_panel;
    private View replay_panel_state_icon;
    private ImageView viedo_iv;
    private ImageView viedo_replay;
    private TextView bubbleTitle;
    private TextView bubbleSubTitle;
    private TextView bubbleContent;
//    private RecyclerView btn_rv;
    private LinearLayout bubbleButtonParent;
    private LinearLayout content_ll;
    private VideoView video_view;
    private ImageView bubble_pic_iv;
    private TextView bubble_video_desc_tv, bubble_qr_desc_tv;
    private ImageView bubble_pic_detils_iv, ok_qr_iv;
    private ImageView pic_big_iv;
    private RelativeLayout video_view_rl, content_lift_rl, activity_video_rl, root_view, bubble_pic_rl, pic_big_rl, info_ll, ok_ll;
    private View loading;
    private TextView cd_close;

    private OvalImageView dialog_right_half_bg;

    private View activity_register_big_rl;
    private TextView tv_qrcode_desc;
    private View activity_play_video_big_rl;

    private String mTitle, mSubTitle;
    private String mIconResource;
    private ActivityInfo activityBean;
    private Bitmap destBitmap;
    private Bitmap srcBitmap;
    private long startTimr = -1;
    private boolean hasSceneBg = false;
    private boolean isPlay = false;

    private boolean crashMsgPlay = false;
    //    private List<MessageBubbleButton> mButtons;
    private View.OnClickListener mViewClickListener;
    private String id;

    private long startTime = -1;

    public ActivitysDetailsDialogFragment(@NonNull Context context) {
        super(context, R.style.FullScreenDialogTheme);
    }

    public ActivitysDetailsDialogFragment(@NonNull Context context, String id) {
        super(context, R.style.FullScreenDialogTheme);
        this.id = id;
    }


//    public static ActivitysDetailsDialogFragment create() {
//        ActivitysDetailsDialogFragment fragment = new ActivitysDetailsDialogFragment();
//        return fragment;
//    }

    //    @Override
//    public void onStart() {
//        setWidth(MATCH_PARENT);
//        setHeight(MATCH_PARENT);
//        setDimAmount(0);
//        setOutCancel(true);
//        setAnimStyle(R.style.OnlineMessageBubbleAnimation);
//        setGravity(Gravity.CENTER);
//        super.onStart();
//    }
    private void getInfo() {
        loading.setVisibility(View.VISIBLE);
        new ActivityRequest().getInfoActivity(id, new HttpCallback<ActivityInfo>() {
            @Override
            public void onSuccess(ActivityInfo activityInfo) {
                loading.setVisibility(View.GONE);
                activityBean = activityInfo;
                initDialogDate();
            }

            @Override
            public void onError(ApiException e) {
                loading.setVisibility(View.GONE);
                initEmptyView();
                ToastUtil.showInfo(getContext().getApplicationContext(), e.getMessage());
            }
        });
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.comprehensive_layout_activity_details);
        root_view = findViewById(R.id.root_view);
        bubbleBg = findViewById(R.id.bubbleBg);
        bubbleTitle = findViewById(R.id.bubbleTitle);
        play_iv = findViewById(R.id.play_iv);
        replay_panel = findViewById(R.id.replay_panel);
        replay_panel_state_icon = findViewById(R.id.replay_panel_state_icon);
        viedo_iv = findViewById(R.id.viedo_iv);
        bubbleSubTitle = findViewById(R.id.bubbleSubTitle);
        bubbleContent = findViewById(R.id.bubbleContent);
        bubbleButtonParent = findViewById(R.id.bubbleButtonParent);
        video_view = findViewById(R.id.video_view);
        viedo_replay = findViewById(R.id.viedo_replay);
        video_view_rl = findViewById(R.id.video_view_rl);
        bubble_video_desc_tv = findViewById(R.id.bubble_video_desc_tv);
        bubble_qr_desc_tv = findViewById(R.id.bubble_qr_desc_tv);

        activity_video_rl = findViewById(R.id.activity_video_rl);
        content_ll = findViewById(R.id.content_ll);
        bubble_pic_iv = findViewById(R.id.bubble_pic_iv);
        bubble_pic_detils_iv = findViewById(R.id.bubble_pic_detils_iv);
        bubble_pic_rl = findViewById(R.id.bubble_pic_rl);
        pic_big_rl = findViewById(R.id.pic_big_rl);
        pic_big_iv = findViewById(R.id.pic_big_iv);
        loading = findViewById(R.id.loading);
//        btn_rv = findViewById(R.id.btn_rv);
        info_ll = findViewById(R.id.info_ll);
        ok_ll = findViewById(R.id.ok_ll);
        ok_qr_iv = findViewById(R.id.ok_qr_iv);
        content_lift_rl = findViewById(R.id.content_lift_rl);
        cd_close = findViewById(R.id.cd_close);
        cd_close.setOnClickListener((v) -> {dismiss();});

        dialog_right_half_bg = findViewById(R.id.dialog_right_half_bg);
        initBGRadius();

        findViewById(R.id.cd_close).setOnClickListener((v)->dismiss());

        findViewById(R.id.front_ground_content).setOnClickListener(v->{
            //do nothing，拦截点击事件不要到root_view上
        });

        Window window = this.getWindow();
        //设置弹出位置
//        window.setGravity(Gravity.BOTTOM | Gravity.START);

        int matchParent = ViewGroup.LayoutParams.MATCH_PARENT;//父布局的宽度

        WindowManager.LayoutParams lp = window.getAttributes();
        lp.width = matchParent;
        lp.height = matchParent;
//        lp.x = matchParent;
//        lp.y = 300;  //设置出现的高度，距离顶部
        window.setAttributes(lp);
        //去除系统自带的margin
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        //背景全透明
        window.setDimAmount(0f);
        //设置弹出动画
        window.setWindowAnimations(R.style.MessageBubbleAnimation);
        //设置对话框大小
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        window.setFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE, WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);
        //Set the dialog to immersive sticky mode
        window.getDecorView().setSystemUiVisibility(
                View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                        | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION);
        //Clear the not focusable flag from the window
        window.clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);
        CommonUtils.getInstance().initGreyStyle(window);
//        setBubbleType(activityBean.getEventLevel());

        activity_register_big_rl = findViewById(R.id.activity_register_big_rl);
        tv_qrcode_desc = findViewById(R.id.tv_qrcode_desc);
        activity_register_big_rl.setOnClickListener(v-> {
//            info_ll.setVisibility(View.VISIBLE);
            v.setVisibility(View.GONE);
        });
        activity_play_video_big_rl = findViewById(R.id.activity_play_video_big_rl);
        activity_play_video_big_rl.setOnClickListener(v->{
            activity_play_video_big_rl.setVisibility(View.GONE);
            loading.setVisibility(View.GONE);
            stopVideo();
        });

        root_view.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        setOnDismissListener(new OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                Log.e("TAG", "===needTermUrl onDismiss");
                stopVideo();
                if (CrashPlayerHelper.getInstance().isPlay()) {
                    CrashPlayerHelper.getInstance().stopPlayTips();
                }
            }
        });
    }

    private void stopVideo(){
        if (viedo_iv != null) {
            video_view.stopPlayback();
            if (isPlay) {
                //记录视频播放前的播放状态，视频播放结束要恢复播放，在手机上有的不会自己播放，所以要手动播放
                PlayerManager.getInstance().play();
                isPlay = false;
            }
        }
    }

    private void initEmptyView(){
        findViewById(R.id.video_pic_wrapper).setVisibility(View.INVISIBLE);
        findViewById(R.id.replay_panel).setVisibility(View.INVISIBLE);
        findViewById(R.id.activity_time_tip).setVisibility(View.INVISIBLE);
        setTitle("");
    }
    private void initDialogDate() {
        setTitle(activityBean.getName());
        setSubTitle(DateUtil.formatMillis("yyyy.MM.dd", Long.parseLong(activityBean.getStartTime()))
                + "-" + DateUtil.formatMillis("yyyy.MM.dd", Long.parseLong(activityBean.getEndTime())));
        setBubbleContent(activityBean.getDescription());
        setSceneBitmap(activityBean.getComprehensiveBackgroundUrl());
        setButtons();

        bubble_qr_desc_tv.setText("扫描二维码 参加活动");
        if (!TextUtils.isEmpty(activityBean.getCodeDes())) {
            tv_qrcode_desc.setText(activityBean.getCodeDes());
        } else {
            tv_qrcode_desc.setText("请使用手机扫码参与\n活动并查看详情");
        }
        //FIXME remove
//        activityBean.setVedioUrl("http://7xjmzj.com1.z0.glb.clouddn.com/20171026175005_JObCxCE2.mp4");
        if (!TextUtils.isEmpty(activityBean.getVedioUrl())) {
            //显示视频
            bubble_pic_rl.setVisibility(View.GONE);
            activity_video_rl.setVisibility(View.VISIBLE);
            content_lift_rl.setVisibility(View.VISIBLE);
            loadVideoScreenshot(activityBean.getVedioUrl(), viedo_iv, 3000);
        } else if (!TextUtils.isEmpty(activityBean.getImgUrl())) {
            //显示图片
            bubble_qr_desc_tv.setText("点击查看详情");
            activity_video_rl.setVisibility(View.GONE);
            bubble_pic_rl.setVisibility(View.VISIBLE);
            content_lift_rl.setVisibility(View.VISIBLE);
            ImageLoader.getInstance().displayImage(getContext(), activityBean.getImgUrl()
                    , bubble_pic_iv, ResUtil.getDrawable(R.drawable.message_pic_error));
            loadBigPic(activityBean.getImgUrl());

            View.OnClickListener picOnClickListener = new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    //显示大图
                    pic_big_rl.setVisibility(View.VISIBLE);
                    ButtonExposureOrClickReportEvent reportEventBean = new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_MESSAGE_BUBBLE_DETAIL_COVER
                            , "", getPageId(), ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_MESSAGE_BUBBLE);
                    reportEventBean.setPage(getPageId());
                    ReportHelper.getInstance().addEvent(reportEventBean);
                }
            };
            bubble_pic_iv.setOnClickListener(picOnClickListener);
            bubble_qr_desc_tv.setOnClickListener(picOnClickListener);


            pic_big_iv.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    pic_big_rl.setVisibility(View.GONE);
                }
            });
            pic_big_rl.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    pic_big_rl.setVisibility(View.GONE);
                }
            });
            ButtonExposureOrClickReportEvent reportEventBean = new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_MESSAGE_BUBBLE_DETAIL_COVER
                    , "", getPageId(), ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_MESSAGE_BUBBLE);
            reportEventBean.setPage(getPageId());
            ReportHelper.getInstance().addEvent(reportEventBean);
        }

        //音频是都有的，视频和图片二选一
        if (!TextUtils.isEmpty(activityBean.getRadioUrl())) {
            //显示音频
            play_iv.setVisibility(View.VISIBLE);
            replay_panel.setVisibility(View.VISIBLE);
        } else {
            play_iv.setVisibility(View.GONE);
            replay_panel.setVisibility(View.GONE);
        }
        play_iv.setSelected(true);
        replay_panel.setSelected(true);
        replay_panel_state_icon.setEnabled(true);

        replay_panel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (activityBean != null) {
                    if (TextUtils.isEmpty(activityBean.getRadioUrl())) {
                        ToastUtil.showNormal(getContext().getApplicationContext(), "地址为空");
                        return;
                    }
                    if (crashMsgPlay) {
                        return;
                    }


                    crashMsgPlay = true;
                    isPlay = PlayerManagerHelper.getInstance().isPlaying();
//                    play_iv.setImageDrawable(ResUtil.getDrawable(R.drawable.msg_play_not_icon));
                    loading.setVisibility(View.VISIBLE);
                    CrashPlayerHelper.getInstance().setIcrashstate(new Icrashstate() {
                        @Override
                        public void onCrashstate(int i) {
                            crashMsgPlay = false;
//                            play_iv.setImageDrawable(ResUtil.getDrawable(R.drawable.msg_play_icon));
                            play_iv.setText("播报");
                            play_iv.setSelected(true);
                            replay_panel.setSelected(true);
                            replay_panel_state_icon.setEnabled(true);
                        }

                        @Override
                        public void onBufferingUpdate(CrashMessageBaseBean crashMessageBaseBean) {
                            loading.setVisibility(View.GONE);
                            play_iv.setText("播报中");
                            play_iv.setSelected(false);
                            replay_panel.setSelected(false);
                            replay_panel_state_icon.setEnabled(false);
                        }

                        @Override
                        public void onPlayerFailed(PlayerFailedType playerFailedType) {

                        }
                    }).playTips(activityBean.getRadioUrl());
                }
            }
        });

        View.OnClickListener videoOnClickListener = new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (video_view != null) {
//                    content_ll.setVisibility(View.GONE);
//                    bubbleBg.setVisibility(View.GONE);
//                    play_iv.setVisibility(View.GONE);

                    playVideo();
                }
            }
        };
        viedo_iv.setOnClickListener(videoOnClickListener);
        bubble_video_desc_tv.setOnClickListener(videoOnClickListener);

        viedo_replay.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (video_view != null) {
                    viedo_replay.setVisibility(View.GONE);
                    playVideo();
                }
            }
        });

    }

    /**
     * 加载大图显示
     */
    private void loadBigPic(String url) {
        Glide.with(getContext()).downloadOnly()
                .load(url)
                .into(new SimpleTarget<File>() {
                    @Override
                    public void onResourceReady(@NonNull File resource, @Nullable Transition<? super File> transition) {
                        pic_big_iv.setImageBitmap(BitmapFactory.decodeFile(resource.getAbsolutePath()));
//                        pic_big_iv.setMinimumScaleType(SubsamplingScaleImageView.SCALE_TYPE_CENTER_INSIDE);
//                        pic_big_iv.setImage(ImageSource.uri(resource.getAbsolutePath()),
//                                new ImageViewState(0f, new PointF(0f, 0f), 0));
                    }
                });
    }


    private void playVideo() {
        if (TextUtils.isEmpty(activityBean.getVedioUrl())) {
            ToastUtil.showNormal(getContext().getApplicationContext(), "播放失败！");
            return;
        }
        isPlay = PlayerManagerHelper.getInstance().isPlaying();
        // 当暂停时，判断是否为用户主动暂停
        if (!isPlay) {
            isPlay = !PlayerManager.getInstance().isPauseFromUser();
        }
//        video_view_rl.setVisibility(View.VISIBLE);
        activity_play_video_big_rl.setVisibility(View.VISIBLE);
        video_view.setOnPreparedListener(new MediaPlayer.OnPreparedListener() {
            @Override
            public void onPrepared(MediaPlayer mp) {
                loading.setVisibility(View.GONE);
                mp.start();
            }
        });

        video_view.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
            @Override
            public void onCompletion(MediaPlayer mp) {
                viedo_replay.setVisibility(View.VISIBLE);
                video_view.stopPlayback();
                if (isPlay) {
                    //记录视频播放前的播放状态，视频播放结束要恢复播放，在手机上有的不会自己播放，所以要手动播放
                    PlayerManager.getInstance().play();
                    isPlay = false;
                }
            }
        });
        video_view.setVideoPath(activityBean.getVedioUrl());
        video_view.requestFocus();
        loading.setVisibility(View.VISIBLE);
//        video_view.start();
//使视频能够暂停、播放、进度条显示等控制
//        MediaController mediaController = new MediaController(getContext());
//        video_view.setMediaController(mediaController);
//        mediaController.setMediaPlayer(video_view);
    }

    private String pageShowPageId = ""; //弹窗曝光用的pageId

    @Override
    public void show() {
        pageShowPageId = ReportParameterManager.getInstance().getPage();
        super.show();
        startTime = System.currentTimeMillis();
        if (TextUtils.isEmpty(id)) {
            initDialogDate();
        } else {
            getInfo();
        }
    }

    @Override
    public void dismiss() {
        if (activityBean != null) {
            CrashPlayerHelper.getInstance().terminatePlayMsgByUrl(activityBean.getRadioUrl());
        }
        startTimr = System.currentTimeMillis();
        ReportUtil.addPageShowEvent(startTimr, getPageId());
        startTimr = -1;
        reportDialogShowEvent();
        super.dismiss();
    }

    @Override
    public void hide() {
        reportDialogShowEvent();
        super.hide();
    }

    private void reportDialogShowEvent() {
        long duration = System.currentTimeMillis() - startTime;
        if (startTime < 0 || duration < 300) {
            return;
        }
        DialogExposureEvent reportEventBean = new DialogExposureEvent(ReportConstants.DIALOG_ID_MESSAGE_BUBBLE, pageShowPageId, duration, null);
        reportEventBean.setPage(pageShowPageId);
        ReportHelper.getInstance().addEvent(reportEventBean);
        startTime = -1;
    }

    private String getPageId() {
        return Constants.PAGE_ID_ACTIVITY;
    }

    public ActivitysDetailsDialogFragment setTitle(String title) {
        this.mTitle = title;
        if (bubbleTitle != null)
            bubbleTitle.setText(title);
        return this;
    }

    public ActivitysDetailsDialogFragment setSubTitle(String subtitle) {
        this.mSubTitle = subtitle;
        if (bubbleSubTitle != null)
            bubbleSubTitle.setText(subtitle);
//            bubbleSubTitle.setText(getRadiusGradientSpan(subtitle, 0));
        return this;
    }

    public ActivitysDetailsDialogFragment setBubbleContent(String content) {

        if (bubbleContent != null)
            bubbleContent.setText(content);
        return this;
    }

    private void initBGRadius(){
        try {
            GradientDrawable gradientDrawable = (GradientDrawable) info_ll.getBackground();
            gradientDrawable.setCornerRadius(ResUtil.getDimen(R.dimen.m16));
            info_ll.setBackground(gradientDrawable);
        } catch (Exception e) {
        }
    }

    private ActivitysDetailsDialogFragment setSceneBitmap(String url) {
        if (!TextUtils.isEmpty(url))
            ImageLoader.getInstance().displayImage(getContext(), url, dialog_right_half_bg, new ColorDrawable());
        return this;
    }


    //设置按钮
    @SuppressLint("ResourceType")
    public void setButtons() {
        if (activityBean != null && activityBean.getButtonList() != null && activityBean.getButtonList().size() > 0) {

            ImageLoader.getInstance().displayImage(getContext(), activityBean.getQrCodeUrl(), ok_qr_iv);

            List<ActivityInfo.ButtonList> beans = ActivitysDetailsHelper.getValidButtons(activityBean);

            LayoutInflater inflater = LayoutInflater.from(this.getContext());

            if(beans.size() == 1 ){
                //空白占位
                TextView hiddenButton = (TextView)inflater.inflate(R.layout.activity_button_ll_item, bubbleButtonParent, false);
                hiddenButton.setVisibility(View.INVISIBLE);
                bubbleButtonParent.addView(hiddenButton, 0);

                ActivitysDetailsHelper.addSep(inflater, bubbleButtonParent);

                ActivityInfo.ButtonList bean = beans.get(0);
                ActivitysDetailsHelper.addButton(inflater, bubbleButtonParent, bean, this::handleButtonAction);
            } else {
                for(int i = beans.size() - 1; i >= 0; i--){
                    ActivitysDetailsHelper.addSep(inflater, bubbleButtonParent);

                    ActivityInfo.ButtonList bean = beans.get(i);
                    ActivitysDetailsHelper.addButton(inflater, bubbleButtonParent, bean, this::handleButtonAction);
                }
                bubbleButtonParent.removeViewAt(bubbleButtonParent.getChildCount() -1);
            }

            bubbleButtonParent.setVisibility(View.VISIBLE);
        }
    }

    private void handleButtonAction(ActivityInfo.ButtonList bean){
        if(bean == null){
            return;
        }
        switch (bean.getAction()) {
            case 0://关闭
                if (CrashPlayerHelper.getInstance().isPlay()) {
                    CrashPlayerHelper.getInstance().stopPlayTips();
                }
                dismiss();
                break;
            case 1://活动报名
                if (CrashPlayerHelper.getInstance().isPlay()) {
                    CrashPlayerHelper.getInstance().stopPlayTips();
                }
//                info_ll.setVisibility(View.GONE);
                activity_register_big_rl.setVisibility(View.VISIBLE);
                break;
            case 2://跳转
                dismiss();
                if (!TextUtils.isEmpty(bean.getDestUrl())) {

                    Map<String, String> params = RouterManager.getInstance().parseUrlParams(bean.getDestUrl());
                    String id = params.get(Constants.ROUTER_PARAMS_KEY_RESOURCE_ID);
                    String type = params.get(Constants.ROUTER_PARAMS_KEY_RESOURCE_TYPE);

                    if( !StringUtil.isEmpty(id) && !StringUtil.isEmpty(type)){
                        handleJumpPlayer(id, type);
                        return;
                    }

                    RouterManager.getInstance().interceptApplicationJumpEvent(getContext(), bean.getDestUrl());
                }
                break;
            case 3://隐藏按钮

                break;
        }
    }

    private static boolean waitPlayerPlaying = false;
    private static int waitResType = -1;
    private static final BasePlayStateListener stateListener = new BasePlayStateListener() {
        @Override
        public void onProgress (PlayItem playItem,long progress, long total){
            LoadingDialog.hideLoading();
            if(waitPlayerPlaying){
                int type = PlayerManagerHelper.getInstance().getCurrentPlayType();
                if(waitResType == type){
                    waitResType = -1;
//                    PageJumper.getInstance().jumpToPlayerFragment(null);
                    jumpToPlayer();
                    waitPlayerPlaying = false;
                }
            }
        }

        @Override
        public void onPlayerEnd(PlayItem playItem){ //临时任务播放完成
            LoadingDialog.hideLoading();
        }
    };
    private void handleJumpPlayer(String id, String typeStr){
        int type = parseInt(typeStr);
        if(type == -1){
            return;
        }

        if(type ==  PlayerConstants.RESOURCES_TYPE_LIVING){
            PlayItem playItem = PlayerManagerHelper.getInstance().getCurPlayItem();
            if (PlayerManagerHelper.getInstance().isSameProgram(playItem, id)) {
                //相同 只跳转到直播间
//                PageJumper.getInstance().jumpToLivePage(Long.parseLong(id));
                jumpToLive(Long.parseLong(id));
                return;
            }
            PlayerManagerHelper.getInstance().start(id, type);
//            PageJumper.getInstance().jumpToLivePage(Long.parseLong(id));
            jumpToLive(Long.parseLong(id));
        } else {

            PlayerManagerHelper.getInstance().start(id, type);

            waitPlayerPlaying = true;
            waitResType = type;
            LoadingDialog.showLoading();
            PlayerManager.getInstance().addPlayControlStateCallback(stateListener);
            PlayerManager.getInstance().setTempTaskPlayListener(stateListener);

        }
    }

    private static void jumpToPlayer(){
        ComponentClient.obtainBuilder(MessageButtonActionComponentConst.NAME)
                .setActionName(MessageButtonActionComponentConst.JUMP_TO_PLAYER)
                .build().call();
    }
    private static void jumpToLive(long id){
        ComponentClient.obtainBuilder(MessageButtonActionComponentConst.NAME)
                .setActionName(MessageButtonActionComponentConst.JUMP_TO_LIVE)
                .addParam("id", id)
                .build().call();
    }
    private int parseInt(String typeStr){
        try {
            return Integer.parseInt(typeStr);
        } catch (NumberFormatException e){
            Log.e(ActivitysDetailsDialogFragment.class.getSimpleName(), "type error: "+typeStr, e);
        }

        return -1;
    }

    public static class LoadingDialog extends Dialog{

        public LoadingDialog(@NonNull Context context) {
            super(context, R.style.FullScreenDialogTheme);
        }

        @Override
        public void onCreate(@Nullable Bundle savedInstanceState) {
            super.onCreate(savedInstanceState);
            setContentView(R.layout.refresh_center);

            setCancelable(true);
        }

        private static LoadingDialog loadingDialog;
        public static void showLoading(){
            if(loadingDialog != null){
                loadingDialog.dismiss();
                loadingDialog = null;
            }
            loadingDialog = new LoadingDialog(AppManager.getInstance().getCurrentActivity());
            loadingDialog.show();
        }

        public static void hideLoading(){
            if(loadingDialog != null){
                loadingDialog.dismiss();
                loadingDialog = null;
            }
        }
    }
    /**
     * 使用Glide方式获取视频某一帧
     *
     * @param uri             视频地址
     * @param imageView       设置image
     * @param frameTimeMicros 获取某一时间帧.
     */
    public void loadVideoScreenshot(String uri, ImageView imageView, long frameTimeMicros) {
        RequestOptions requestOptions = RequestOptions.frameOf(frameTimeMicros);
        requestOptions = requestOptions.set(FRAME_OPTION, MediaMetadataRetriever.OPTION_CLOSEST);
        requestOptions = requestOptions.transform(new BitmapTransformation() {
            @Override
            protected Bitmap transform(@NonNull BitmapPool pool, @NonNull Bitmap toTransform, int outWidth, int outHeight) {
                Log.d("--使用glide方式--", "高度为" + toTransform.getHeight() + "寛度为" + toTransform.getWidth());
                return toTransform;
            }

            @Override
            public void updateDiskCacheKey(MessageDigest messageDigest) {
                try {
                    messageDigest.update((getContext().getPackageName() + "RotateTransform").getBytes("utf-8"));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
        Glide.with(getContext()).load(uri).apply(requestOptions).into(imageView);
    }
}
