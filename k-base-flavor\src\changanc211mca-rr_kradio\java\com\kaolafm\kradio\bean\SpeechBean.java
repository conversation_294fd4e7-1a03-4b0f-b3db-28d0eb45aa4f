package com.kaolafm.kradio.bean;

import com.alibaba.fastjson.JSON;

public class SpeechBean {

    private abstract static class SpeechBaseBean {

        public SpeechBaseBean() {
            this.version = "v1.0"; //协议版本写死
            this.operationApp = "yunting";
        }

        public String messageType;
        public String focus;
        public String needResponse;
        public String requestCode;

        public String version;
        public String operationApp;
    }

    /**
     * request ex:
     * {
     * "messageType": "REQUEST",
     * "focus": " internetRadio ",
     * "needResponse": "YES",
     * "requestCode": "10001",
     * "semantic": {
     * "service": "INTERNETRADIO",
     * "operation": "PLAY",
     * "category": "相声评书",
     * "presenter": "岳云鹏"
     * },
     * "version": "v1.0",
     * "operationApp": "speech"
     * }
     */

    public static class SpeechRequestBean extends SpeechBaseBean {
        public SpeechRequestSemanticBean semantic;

        public static class SpeechRequestSemanticBean {
            //OPEN  string 打开节目
            //CLOSE string 关闭节目
            //PLAY string 播放节目
            //DISLIKE string 不喜欢
            //PAUSE string 暂停
            //PAST string 上一个
            //REPEAT string 重听
            //NEXT string 下一个
            //RANDOM string 随机播放
            //CYCLE string 单曲循环
            //ORDER string 顺序播放
            //LOOP string 循环播放
            //REVERSE string 倒序播放
            //ORDER_TRANS string 换个顺序
            //SEARCH_PRESENTER string 这是谁的节目
            //SEARCH_PROGRAM string 节目名是什么
            //COLLECT string 收藏
            //CANCEL_COLLECT string 取消收藏
            //LIKE string 喜欢
            //BOOK string 订阅
            //CANCEL_BOOK string 取消订阅
            //BROADCAST_HOST string 这是谁的节目
            //BROADCAST_PROGRAM string 这是什么节目
            //BROADCAST_SONG string 听歌识曲
            //BROADCAST_SINGER string 这首歌是谁唱的
            //PLAY_LIST string 收听播放列表
            //CLOSE_LIST String 关闭播放列表
            //OPEN_LIST string 打开播放列表
            public String operation; //操作类型=PLAY时，会有上面的播放属性字段其他类型的operation主要为播放控制
            public String presenter; //主播
            public String program; //专辑名/节目名
            public String label; //筛选类型：最新、热门、经典
            public String category; //节目类型
            public String chapter; //第几期
            public String datetime; //时间
            public String source; //来源：历史，订阅，播放列表，本地，线上
            public String famous; //名人
            public String morePresenter; //更多主播，合播时使用
            public String moreFamous; //更多名人，合播时使用
            public String exclude_presenter; //除了xxx主播
            public String second; //快进、快退、调到xx秒
            public String minute; //快进、快退、调到xx分
            public String hour; //快进、快退、调到xx时
            public String tags; //泛标签，例如：相声、小品、世界史
            public String service; //INTERNETRADIO

            public String name; //开启app用

            public String code;
            public String waveband; //fm|am
            public String location;
            /**
             类型为电台时 focus=="radio"：
             Operation list:
             NEXT	下一台|换一台
             PAST	上一台
             OPEN	打开广播
             PLAY	播放|打开收音机
             PAUSE	暂停收音机
             CLOSE	关闭收音机
             SEARCH	搜索|扫描电台
             COLLECT	收藏
             CANCEL_COLLECT	取消收藏
             BROADCAST_PROGRAM	节目询问
             参数意义变更为：
             service	String	是	本应用请使用RADIO_CONTROL
             name	String	否	电台名称
             code	String	否	电台频点
             waveband	String	否	fm|am
             category	String	否	电台类型，音乐、交通、网络等
             source	String	否	电台源：收藏
             location	String	否	表示地区
             operation	String	是	操作类型 **/
        }
    }

    /**
     * push ex:
     * {
     * "messageType": "PUSH", //这次是对一个请求的应答
     * "focus": "internetRadio",
     * "requestCode": "10001",
     * "data": {
     * "dataInfo": {
     * "presenter": "郭德纲"
     * },
     * "activeStatus": "fg",
     * "sceneStatus": "playing"* 	},
     * "version": "v1.0",
     * "operationApp": "internetRadio"
     * }
     */
    public static class SpeechPushBean extends SpeechBaseBean {
        public SpeechPushDataBean data;

        public SpeechPushBean(String requestCode, String activeStatus, String sceneStatus, String presenter, String program) {
            this.messageType = "PUSH";
            this.focus = "internetRadio";
            if (requestCode == null) {
                this.requestCode = "10001";
            } else {
                this.requestCode = requestCode;
            }

            SpeechBean.SpeechPushBean.SpeechPushDataBean data = new SpeechBean.SpeechPushBean.SpeechPushDataBean();
            data.activeStatus = activeStatus;
            data.sceneStatus = sceneStatus;
            data.dataInfo = new SpeechBean.SpeechPushBean.SpeechPushDataBean.SpeechPushDataInfoBean();
            data.dataInfo.presenter = presenter;
            data.dataInfo.program = program;
            this.data = data;
        }

        /**
         * activeStatus 应用状态。
         * fg|bg|noExists
         * fg：表明应用在前台
         * bg：表明应用在后台
         * noExists：暂时不需要关注
         * <p>
         * sceneStatus 场景状态：
         * playing|paused
         **/
        private static class SpeechPushDataBean {
            public SpeechPushDataInfoBean dataInfo;
            public String activeStatus;
            public String sceneStatus;

            private static class SpeechPushDataInfoBean {
                public String presenter;// 主播名
                public String program;// 专辑名
                public String itemid;// 节目条目id
            }
        }
    }

    /**
     * response ex:
     * {
     * "messageType": "RESPONSE", //这次是对一个请求的应答
     * "focus": "internetRadio",
     * "responseCode": 10001, //应答码是10001，和其应答的请求消息的请求码一致
     * "semantic": { //应答的详细解释
     * "status": "SUCCESS", //执行成功 "status": "FAIL","message": "操作失败"
     * "message": "操作成功" //内容
     * },
     * "version": "v1.0", //协议的版本号是1.0
     * "operationApp": "navi" //发出的应用简称为navi。表示由导航发出
     * }
     */

    public static class SpeechResponseBean {
        public String messageType;
        public String focus;
        public String responseCode;
        public SpeechResponseSemanticBean semantic;
        public String version;
        public String operationApp;

        public SpeechResponseBean(String focus, String requestCode, SpeechResponseSemanticBean semantic) {
            this.messageType = "RESPONSE";
            this.focus = focus;
            this.responseCode = requestCode == null ? "10001" : requestCode;
            this.semantic = semantic;
            this.version = "v1.0";
            this.operationApp = "yunting";
        }

        public SpeechResponseBean(String responseCode, boolean isSuccess, String msg) {
            this("internetRadio", responseCode, null);
            SpeechResponseSemanticBean responseSemanticBean = new SpeechResponseSemanticBean();
            if (isSuccess) {
                responseSemanticBean.status = "SUCCESS";
                responseSemanticBean.message = msg == null ? "操作成功" : msg;
            } else {
                responseSemanticBean.status = "FAIL";
                responseSemanticBean.message = msg == null ? "操作失败" : msg;
            }
            this.semantic = responseSemanticBean;

        }

        public static class SpeechResponseSemanticBean {
            public String service;
            public String status;
            public String message;
            public String name;
        }
    }

    public static <T> T getBean(String string, Class<T> clazz) {
        try {
            return JSON.parseObject(string, clazz);
        } catch (Exception e) {
            return null;
        }
    }

    public static String getString(Object obj) {
        try {
            return JSON.toJSONString(obj);
        } catch (Exception e) {
            return null;
        }
    }

}
