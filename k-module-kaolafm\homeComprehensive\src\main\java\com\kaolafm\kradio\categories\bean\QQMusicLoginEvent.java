package com.kaolafm.kradio.categories.bean;

import com.kaolafm.kradio.categories.CategoryConstant;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.ResUtil;

/**
 * QQ音乐登录相关的event
 *
 * <AUTHOR>
 * @date 2018/6/14
 */

public class QQMusicLoginEvent {

    private int resultCode;

    private String msg;

    public int getResultCode() {
        return resultCode;
    }

    public void setResultCode(int resultCode) {
        this.resultCode = resultCode;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public QQMusicLoginEvent(int resultCode, String msg) {
        this.resultCode = resultCode;
        this.msg = msg;
    }

    public QQMusicLoginEvent() {
    }

    public static QQMusicLoginEvent cancelInstance() {
        QQMusicLoginEvent qqMusicLoginEvent = new QQMusicLoginEvent();
        qqMusicLoginEvent.setResultCode(CategoryConstant.LOGIN_RESULT_CANCEL_CODE);
        qqMusicLoginEvent.setMsg(ResUtil.getString(R.string.cancel_login));
        return qqMusicLoginEvent;
    }

    public static QQMusicLoginEvent loginInstance() {
        QQMusicLoginEvent qqMusicLoginEvent = new QQMusicLoginEvent();
        qqMusicLoginEvent.setResultCode(CategoryConstant.LOGIN_RESULT_SUCCESS_CODE);
        qqMusicLoginEvent.setMsg(ResUtil.getString(R.string.login_success));
        return qqMusicLoginEvent;
    }

    public static QQMusicLoginEvent logoutInstance() {
        QQMusicLoginEvent qqMusicLoginEvent = new QQMusicLoginEvent();
        qqMusicLoginEvent.setResultCode(CategoryConstant.LOGOUT_RESULT_CODE);
        qqMusicLoginEvent.setMsg(ResUtil.getString(R.string.music_logout_title_str));
        return qqMusicLoginEvent;
    }
}
