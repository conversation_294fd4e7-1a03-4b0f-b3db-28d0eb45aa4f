package com.kaolafm.kradio.flavor.view;

import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.kaolafm.kradio.flavor.R;

public class CustomAlertDialog extends AlertDialog{


    protected CustomAlertDialog(Context context) {
        super(context);
    }

    protected CustomAlertDialog(Context context, boolean cancelable, OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    protected CustomAlertDialog(Context context, int themeResId) {
        super(context, themeResId);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 显示导航栏
        int vsysui = getWindow().getDecorView().getSystemUiVisibility();
        getWindow().getDecorView().setSystemUiVisibility(vsysui
                | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);

//        if(hasFocus){
//            // 显示导航栏
//            int vsysui = getWindow().getDecorView().getSystemUiVisibility();
//            getWindow().getDecorView().setSystemUiVisibility(vsysui
//                    | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
//                    | View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
//        }else{
//
//
//// 隐藏导航栏
//            int vsysui = getWindow().getDecorView().getSystemUiVisibility();
//            getWindow().getDecorView().setSystemUiVisibility(vsysui
//                    | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
//                    | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
//                    | View.SYSTEM_UI_FLAG_LAYOUT_STABLE
//                    | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION);
//        }
    }

    public static class Builder {
        private Context context;
        private String title;
        private String message;
        private int gravity;
        private boolean cancelable;
        private boolean canceledOnTouchOutside;

        public boolean isCanceledOnTouchOutside() {
            return canceledOnTouchOutside;
        }

        public Builder setCanceledOnTouchOutside(boolean canceledOnTouchOutside) {
            this.canceledOnTouchOutside = canceledOnTouchOutside;
            return this;
        }

        public boolean isCancelable() {
            return cancelable;
        }

        public Builder setCancelable(boolean cancelable) {
            this.cancelable = cancelable;
            return this;
        }

        public int getGravity() {
            return gravity;
        }

        public void setGravity(int gravity) {
            this.gravity = gravity;
        }

        private String positiveButtonText;
        private String negativeButtonText;
        private View contentView;
        private DialogInterface.OnClickListener positiveButtonClickListener;
        private DialogInterface.OnClickListener negativeButtonClickListener;

        public Builder(Context context) {
            this.context = context;
        }

        public Builder setMessage(String message) {
            this.message = message;
            return this;
        }

        /**
         * Set the Dialog message from resource
         *
         * @param
         * @return
         */
        public Builder setMessage(int message) {
            this.message = (String) context.getText(message);
            return this;
        }

        /**
         * Set the Dialog title from resource
         *
         * @param title
         * @return
         */
        public Builder setTitle(int title) {
            this.title = (String) context.getText(title);
            return this;
        }

        /**
         * Set the Dialog title from String
         *
         * @param title
         * @return
         */

        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }

        public Builder setContentView(View v) {
            this.contentView = v;
            return this;
        }

        /**
         * Set the positive button resource and it's listener
         *
         * @param positiveButtonText
         * @return
         */
        public Builder setPositiveButton(int positiveButtonText,
                                         DialogInterface.OnClickListener listener) {
            this.positiveButtonText = (String) context
                    .getText(positiveButtonText);
            this.positiveButtonClickListener = listener;
            return this;
        }

        public Builder setPositiveButton(String positiveButtonText,
                                         DialogInterface.OnClickListener listener) {
            this.positiveButtonText = positiveButtonText;
            this.positiveButtonClickListener = listener;
            return this;
        }

        public Builder setNegativeButton(int negativeButtonText,
                                         DialogInterface.OnClickListener listener) {
            this.negativeButtonText = (String) context
                    .getText(negativeButtonText);
            this.negativeButtonClickListener = listener;
            return this;
        }

        public Builder setNegativeButton(String negativeButtonText,
                                         DialogInterface.OnClickListener listener) {
            this.negativeButtonText = negativeButtonText;
            this.negativeButtonClickListener = listener;
            return this;
        }

        public CustomAlertDialog create() {
//            LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            // instantiate the dialog with the custom Theme
            final CustomAlertDialog dialog = new CustomAlertDialog(context);
            View view = LayoutInflater.from(context).inflate(R.layout.dialog_custom, null);

            TextView mTitle = view.findViewById(R.id.title);
            if(TextUtils.isEmpty(title)){
                mTitle.setVisibility(View.GONE);
            } else {
                mTitle.setText(title);
                mTitle.setVisibility(View.VISIBLE);
            }

            TextView mMessage = view.findViewById(R.id.message);
            if(TextUtils.isEmpty(message)){
                mMessage.setVisibility(View.GONE);
            } else {
                mMessage.setText(message);
                mMessage.setVisibility(View.VISIBLE);
            }

            RelativeLayout btnRelativeLayout = view.findViewById(R.id.rl_btn);
            btnRelativeLayout.setGravity(gravity);

            Button positiveButton = view.findViewById(R.id.positiveButton);
            if(TextUtils.isEmpty(positiveButtonText)){
                positiveButton.setVisibility(View.GONE);
            } else {
                positiveButton.setText(positiveButtonText);
                positiveButton.setVisibility(View.VISIBLE);
                if(positiveButtonClickListener != null){
                    positiveButton.setOnClickListener(new View.OnClickListener() {
                        public void onClick(View v) {
                            positiveButtonClickListener.onClick(dialog,
                                    DialogInterface.BUTTON_POSITIVE);
                        }
                    });
                    dialog.dismiss();
                }
            }

            View divider = view.findViewById(R.id.divider);
            if(TextUtils.isEmpty(positiveButtonText) || TextUtils.isEmpty(negativeButtonText)){
                divider.setVisibility(View.GONE);
            } else {
                divider.setVisibility(View.VISIBLE);
            }

            Button negativeButton = view.findViewById(R.id.negativeButton);
            if(TextUtils.isEmpty(negativeButtonText)){
                negativeButton.setVisibility(View.GONE);
            } else {
                negativeButton.setText(negativeButtonText);
                negativeButton.setVisibility(View.VISIBLE);
                if(negativeButtonClickListener != null) {
                    negativeButton.setOnClickListener(new View.OnClickListener() {
                        public void onClick(View v) {
                            negativeButtonClickListener.onClick(dialog,
                                    DialogInterface.BUTTON_NEGATIVE);
                        }
                    });
                    dialog.dismiss();
                }
            }
            dialog.setCancelable(cancelable);
            dialog.setCanceledOnTouchOutside(canceledOnTouchOutside);
            dialog.setView(view);
            return dialog;
        }
    }
}
