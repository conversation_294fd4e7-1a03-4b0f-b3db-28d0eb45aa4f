package com.kaolafm.kradio.online.home.location;

import android.content.Context;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.SharedPreferenceUtil;

import java.util.ArrayList;
import java.util.List;

public class OnlineLocationSearchHistoryManager {

    private static final String SP_SEARCH_HISTORY = "onlineLocationSearchHistory";

    private static final String SP_KEY_RECENT_SEARCH_TAG = "onlineLocationSearchTag";

    private static final int MAX_SEARCH_HISTORY_NUM = 5;

    private SharedPreferenceUtil mSPUtil;

    private static final class OnlineLocationSearchHistoryManagerHolder{
        private static final OnlineLocationSearchHistoryManager INSTANCE = new OnlineLocationSearchHistoryManager();
    }

    public static OnlineLocationSearchHistoryManager getInstance(){
        return OnlineLocationSearchHistoryManagerHolder.INSTANCE;
    }

    private OnlineLocationSearchHistoryManager(){
        init();
    }

    private void init(){
        mSPUtil = SharedPreferenceUtil
                .newInstance(AppDelegate.getInstance().getContext(), SP_SEARCH_HISTORY, Context.MODE_PRIVATE);
    }

    public void addRecentSearchTag(String tag){
        if(TextUtils.isEmpty(tag)){
            return;
        }
        List<String> tags = getRecentSearchTags();
        if(tags.contains(tag)){
            tags.remove(tag);
        }
        tags.add(0, tag);
        if(tags.size() > MAX_SEARCH_HISTORY_NUM){
            tags.remove(MAX_SEARCH_HISTORY_NUM);
        }
        Gson gson = new Gson();
        String tagStr = gson.toJson(tags);
        mSPUtil.putString(SP_KEY_RECENT_SEARCH_TAG, tagStr);
    }

    public List<String> getRecentSearchTags(){
        List<String> results = new ArrayList<>();
        String tags = mSPUtil.getString(SP_KEY_RECENT_SEARCH_TAG, null);
        if(TextUtils.isEmpty(tags)){
            return results;
        }
        Gson gson = new Gson();
        results = gson.fromJson(tags, new TypeToken<List<String>>() {}.getType());
        return results;
    }

    public void clearAllRecentSearchTags(){
        mSPUtil.remove(SP_KEY_RECENT_SEARCH_TAG);
    }

}
