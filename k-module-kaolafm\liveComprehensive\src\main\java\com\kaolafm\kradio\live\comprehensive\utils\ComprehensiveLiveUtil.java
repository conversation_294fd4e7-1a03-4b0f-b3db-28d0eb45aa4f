package com.kaolafm.kradio.live.comprehensive.utils;

import android.os.Bundle;

import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.kradio.live.utils.LiveUtil;
import com.kaolafm.report.event.LoginReportEvent;

/**
 * 直播模块的工具类
 * <AUTHOR>
 * @date 2019-10-30
 */
public class ComprehensiveLiveUtil {
    /**
     * 检查用户是否登录，如果没有登录就显示登录页。
     * @return
     */
    public static boolean showLoginIfNotLogin() {
        boolean userBound = LiveUtil.isUserBound();
        if (!userBound) {
//            RouterManager.getInstance().jumpPage(RouterConstance.ACTIVITY_URL_MINE);
            Bundle bundle=new Bundle();
            bundle.putString("type", LoginReportEvent.ONLINE_REMARKS1_LIVE_MSG);
            RouterManager.getInstance().jumpPage(RouterConstance.LOGIN_COMPREHENSIVE_URL,bundle);
        }
        return userBound;
    }
}
