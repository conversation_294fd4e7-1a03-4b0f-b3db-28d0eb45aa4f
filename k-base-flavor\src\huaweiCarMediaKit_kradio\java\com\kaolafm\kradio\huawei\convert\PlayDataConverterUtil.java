package com.kaolafm.kradio.huawei.convert;


import android.util.Log;

import com.huawei.carmediakit.bean.MediaElement;
import com.huawei.carmediakit.bean.Single;
import com.kaolafm.kradio.huawei.utils.Constant;
import com.kaolafm.opensdk.api.broadcast.ProgramDetails;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import java.util.ArrayList;
import java.util.List;

import static com.kaolafm.kradio.huawei.utils.MediaIdHelper.getMediaId;

public class PlayDataConverterUtil {
    public static final String TAG = Constant.TAG;

    public static MediaElement toMediaEntity(PlayItem playItem) {
        MediaElement mediaElement = new MediaElement();
        mediaElement.setPlaying(false);
        if (playItem == null) {
            return mediaElement;
        }
        PlayItem currentPlayItem = PlayerManager.getInstance().getCurPlayItem();
        Log.i(TAG, "mid=" + playItem.getAudioId() + ":pid=" + currentPlayItem.getAudioId());
        if (playItem.getAudioId() == currentPlayItem.getAudioId()) {
            mediaElement.setPlaying(true);
        }
        Log.i(TAG, "title=" + playItem.getType() + ":url=" + playItem.getPicUrl() + ":singer=" + playItem.getSourceName());
        mediaElement.setName(playItem.getTitle());
        mediaElement.setMediaId(getMediaId(playItem));
        mediaElement.setCoverUrl(playItem.getPicUrl());
        mediaElement.setSinger(playItem.getSourceName());
        mediaElement.setElementType(MediaElement.ElementType.SINGLE);
        mediaElement.setDesp(playItem.getTitle());
        mediaElement.setParentId(playItem.getRadioId());

        return mediaElement;
    }

    public static MediaElement toMediaEntityAlbum(PlayItem playItem) {
        MediaElement mediaElement = new MediaElement();
        mediaElement.setPlaying(false);
        if (playItem == null) {
            return mediaElement;
        }
        PlayItem currentPlayItem = PlayerManager.getInstance().getCurPlayItem();
        Log.i(TAG, "mid=" + playItem.getAudioId() + ":pid=" + currentPlayItem.getAudioId());
        if (playItem.getAudioId() == currentPlayItem.getAudioId()) {
            mediaElement.setPlaying(true);
        }
        Log.i(TAG, "title=" + playItem.getType() + ":url=" + playItem.getPicUrl() + ":singer=" + playItem.getSourceName());
        mediaElement.setName(playItem.getTitle());
        mediaElement.setMediaId(getMediaId(playItem, true));
        mediaElement.setCoverUrl(playItem.getPicUrl());
        mediaElement.setSinger(playItem.getSourceName());
        mediaElement.setElementType(MediaElement.ElementType.ALBUM);
        mediaElement.setDesp(playItem.getTitle());

        return mediaElement;
    }

    public static List<MediaElement> toMediaElementList(List<PlayItem> playItemList) {
        List<MediaElement> mediaElementList = new ArrayList<>();
        for (PlayItem playItem: playItemList) {
            MediaElement element = toMediaEntity(playItem);
            mediaElementList.add(element);
        }

        return mediaElementList;
    }

    public static MediaElement toMediaElement(AudioDetails audioDetails) {
        MediaElement mediaElement = new MediaElement();
        mediaElement.setPlaying(false);
        if (audioDetails == null) {
            return mediaElement;
        }
        PlayItem currentPlayItem = PlayerManager.getInstance().getCurPlayItem();
        if (audioDetails.getAudioId() == currentPlayItem.getAudioId()) {
            mediaElement.setPlaying(true);
        }
        Log.i(TAG, "title=" + audioDetails.getContentType() + ":url=" + audioDetails.getAudioPic() + ":singer=" + audioDetails.getSourceName());
        mediaElement.setName(audioDetails.getAudioName());
        mediaElement.setMediaId(getMediaId(audioDetails));
        mediaElement.setCoverUrl(audioDetails.getAudioPic());
        mediaElement.setSinger(audioDetails.getSourceName());
        mediaElement.setElementType(MediaElement.ElementType.SINGLE);
        mediaElement.setDesp(audioDetails.getAudioName());

        return mediaElement;
    }

    public static List<MediaElement> toMediaElementListFromAudioDetails(List<AudioDetails> playItemList) {
        List<MediaElement> mediaElementList = new ArrayList<>();
        for (AudioDetails audioDetails: playItemList) {
            MediaElement element = toMediaElement(audioDetails);
            mediaElementList.add(element);
        }

        return mediaElementList;
    }

    public static MediaElement toMediaElement(ProgramDetails audioDetails) {
        MediaElement mediaElement = new MediaElement();
        mediaElement.setPlaying(false);
        if (audioDetails == null) {
            return mediaElement;
        }
        PlayItem currentPlayItem = PlayerManager.getInstance().getCurPlayItem();
        if (audioDetails.getBroadcastId() == currentPlayItem.getAudioId()) {
            mediaElement.setPlaying(true);
        }
        Log.i(TAG, "title=" + audioDetails.getTitle() + ":url=" + audioDetails.getBroadcastImg());
        mediaElement.setName(audioDetails.getTitle());
        mediaElement.setMediaId(getMediaId(audioDetails));
        mediaElement.setCoverUrl(audioDetails.getBroadcastImg());
        //mediaElement.setSinger(audioDetails.get);
        mediaElement.setElementType(MediaElement.ElementType.SINGLE);
        mediaElement.setDesp(audioDetails.getBroadcastName());

        return mediaElement;
    }

    public static List<MediaElement> toMediaElementListFromProgramDetails(List<ProgramDetails> playItemList) {
        List<MediaElement> mediaElementList = new ArrayList<>();
        for (ProgramDetails audioDetails: playItemList) {
            MediaElement element = toMediaElement(audioDetails);
            mediaElementList.add(element);
        }

        return mediaElementList;
    }

    public static Single toSingle(PlayItem item, boolean isPlaying) {
        Single single = new Single();
        single.setPlaying(isPlaying);
        if (item == null) {
            return single;
        }

        single.setMediaId(getMediaId(item));
        single.setCoverUrl(item.getPicUrl());
        single.setName(item.getTitle());

        return single;
    }

}
