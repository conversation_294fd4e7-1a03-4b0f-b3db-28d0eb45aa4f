<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/history_main_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:id="@+id/user_clear_his"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="@dimen/y26"
        android:layout_marginRight="@dimen/x32"
        android:layout_width="@dimen/m40"
        android:layout_height="@dimen/m40"
        android:gravity="center"
        android:background="@drawable/online_history_clear_bg">

        <ImageView
            android:layout_width="@dimen/m24"
            android:layout_height="@dimen/m24"
            android:src="@drawable/online_sl_clear_his"/>
    </RelativeLayout>

    <TextView
        android:id="@+id/tv_history_count"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toLeftOf="@id/user_clear_his"
        android:layout_marginTop="@dimen/y18"
        android:layout_marginRight="@dimen/x24"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/y56"
        android:gravity="center"
        android:textColor="@color/online_text_count_color"
        android:textSize="@dimen/text_size3"
        android:text=""/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_history_list"
        app:layout_constraintTop_toBottomOf="@id/tv_history_count"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="@dimen/y18"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:overScrollMode="never"
        android:visibility="gone" />

    <ViewStub
        android:id="@+id/vs_history_net_error"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout="@layout/online_error_layout" />

    <include
        android:id="@+id/history_loading"
        layout="@layout/online_refresh_center"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
