package com.kaolafm.kradio.live.comprehensive.ui;

import static android.media.AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK;

import android.Manifest;
import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.PropertyValuesHolder;
import android.content.Context;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.media.AudioManager;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.AnimationSet;
import android.view.animation.AnimationUtils;
import android.view.animation.DecelerateInterpolator;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;
import androidx.constraintlayout.widget.Guideline;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.kaolafm.kradio.common.ViewConstants;
import com.kaolafm.kradio.common.widget.CircleProgressImageView;
import com.kaolafm.kradio.common.widget.PlayingIndicator;
import com.kaolafm.kradio.component.DynamicComponent;
import com.kaolafm.kradio.component.RealCaller;
import com.kaolafm.kradio.constant.UserComponentConst;
import com.kaolafm.kradio.constant.UserStateObserverProcessorConst;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioFocusInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioSpeakImageInter;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.lib.report.ReportUtil;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.ComponentUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.TimerUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.lib.utils.imageloader.listener.OnImageLoaderListener;
import com.kaolafm.kradio.live.comprehensive.utils.ComprehensiveLiveUtil;
import com.kaolafm.kradio.live.mvp.LivePresenter;
import com.kaolafm.kradio.live.mvp.LiveView;
import com.kaolafm.kradio.live.player.LiveManager;
import com.kaolafm.kradio.live.player.RecordUploadHelper;
import com.kaolafm.kradio.live.player.RecorderStatus;
import com.kaolafm.kradio.live.utils.LiveUtil;
import com.kaolafm.kradio.player.event.PlayerChangedEBData;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.player.radiolive.LiveLifecycleListener;
import com.kaolafm.kradio.player.radiolive.LiveStateManager;
import com.kaolafm.kradio.player.radiolive.RadioLiveInfo;
import com.kaolafm.opensdk.api.live.model.ChatUserInfo;
import com.kaolafm.opensdk.api.live.model.LiveDetails;
import com.kaolafm.opensdk.api.live.model.MessageBean;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.LiveStreamPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Observable;
import java.util.Observer;

import skin.support.content.res.SkinCompatResources;

/**
 * 直播界面,负责直播的展示，播放，录音，消息发送，接收。
 * <p/>
 * 直播状态的展示以{@link com.kaolafm.opensdk.api.live.model.LiveInfoDetail# status} 为依据,每30秒轮询一次， 这些状态码与服务器的码一一对应。
 * 具体可以参照 {@link com.kaolafm.opensdk.api.live.model.LiveInfoDetail }中的常量定义。
 * 还有一个影响直播展示的，就是播放器的状态回调。
 * <p/>
 * 目前，直播提前结束，服务器返回的状态码还没有变更，或者返回的状态码已经变更，但直播播放没有结束的情况，
 * 还在讨论。
 *
 * <AUTHOR> Huangui
 */
public class LiveFragment extends BaseFragment<LivePresenter>
        implements LiveView {

    private static final String TAG = "LiveFragment";

    private static final String LIVE_INFO = "live_info";

    private static final int CODE_PERMISSION_REQUEST = 1;

    private static final int RECORD_DURATION = 20 * 1000;
    private static final int RECORD_COUNTDOWN = 1000;

    public static final int DURATION_LONG = 1500;

    private LiveDetails mLiveInfo;

    ImageView mMinimumImage;
    ImageView mStopLiveImage;
    ImageView mLiveImage;
    TextView mLiveNameText;
    TextView mLivePlayerText;
    View mPlayerVerticalLine;
    TextView mListenerNumberText;
    TextView mLoginPromptText;
    ConstraintLayout mErrorLayout;
    ImageView mSpeakImage;
    ImageView mRecordSoundImage;
    CircleProgressImageView mRecordButtonImage;
    ViewGroup mListenButton;
    ViewGroup mCancelButton;
    PlayingIndicator mListenButtonAnim;
    TextView mListenButtonText;
    TextView mCancelButtonText;
    TextView mRecordButtonText;
    FrameLayout mScreenBulletContainer;
    View mTopGradientImage;
    View mBottomGradientImage;
    ConstraintLayout mRootLayout;
    Guideline mTopGuideline;

    private boolean bPlayerEnabled;

    private TextView mLastMessageText;

    private String mForecastString;

    private int mSystemVolume;

    private CountDownTimer mRecordTimer;
    private CountDownTimer mForecastTimer;

    private boolean bDoMemberEnterAnim;
    private LinkedList<ChatUserInfo> mMemberEnterQueue = new LinkedList<>();

    private boolean bDoMessageReceiveAnim;
    private LinkedList<MessageBean> mMessageReceiveQueue = new LinkedList<>();

    private long mLastRecordTime;
    private long mLastListenTime;

    private KRadioAudioFocusInter mKRadioAudioFocusInter;

    private UserLoginComponent mUserLoginComponent;

    private String mImageUri = "";

    @Override
    protected int getLayoutId() {
        return R.layout.fragment_live;
    }

    @Override
    protected int getLayoutId_Tow() {
        return 0;
    }

    @Override
    protected LivePresenter createPresenter() {
        LivePresenter presenter = new LivePresenter(this);
        return presenter;
    }

    public static LiveFragment create(LiveDetails liveInfo) {
        Bundle args = new Bundle();
        args.putSerializable(LIVE_INFO, liveInfo);
        LiveFragment lf = new LiveFragment();
        lf.setArguments(args);
        return lf;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mForecastString = getString(R.string.live_hour_minute_second);

        mUserLoginComponent = new UserLoginComponent();
        ComponentUtil.addObserver(UserComponentConst.NAME, mUserLoginComponent);
    }


    @Override
    public void onResume() {
        super.onResume();
        TimerUtil.newInstance().timer(250, num -> {
            //36568 【monkey】FATAL EXCEPTION: main，io.reactivex.exceptions.OnErrorNotImplementedException
            if (mPresenter != null) {
                mPresenter.initNim();
                initLiveManager();
                LiveStateManager.getInstance().registerLiveLifecycleListener(Long.parseLong(PlayerManagerHelper.getInstance().getCurPlayItem().getRadioId()), mLiveLifecycleListener);
            }
        });
    }

    private void initLiveManager() {
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "initLiveManager");
        }
        LiveManager.getInstance().addRecorderStatusObserver(mRecorderStatusObserver);
        if (!LiveUtil.isUserBound()) {
            mLoginPromptText.setText(R.string.voice_message_please_login);
        } else {
            mLoginPromptText.setText(R.string.voice_message_click_to_start);
        }
        showLiveInfo(mLiveInfo);
    }

    @Override
    public String getPageId() {
        return Constants.PAGE_ID_PLAYER_LIVING;
    }

    @Override
    public void initArgs() {
        Bundle arg = getArguments();
        if (arg != null) {
            mLiveInfo = (LiveDetails) arg.getSerializable(LIVE_INFO);
        }
    }

    @Override
    public boolean useEventBus() {
        return true;
    }

    @Override
    public void initView(View view) {
        mMinimumImage=view.findViewById(R.id.live_minimum);
        mStopLiveImage=view.findViewById(R.id.live_stop);
        mLiveImage=view.findViewById(R.id.live_image);
        mLiveNameText=view.findViewById(R.id.live_name);
        mLivePlayerText=view.findViewById(R.id.live_anchor);
        mPlayerVerticalLine=view.findViewById(R.id.live_player_vertical_line);
        mListenerNumberText=view.findViewById(R.id.live_listening_number);
        mLoginPromptText=view.findViewById(R.id.live_login_prompt_text);
        mErrorLayout=view.findViewById(R.id.live_error_layout);
        mSpeakImage=view.findViewById(R.id.live_speak_image);
        mRecordSoundImage=view.findViewById(R.id.live_record_anim_image);
        mRecordButtonImage=view.findViewById(R.id.live_leave_a_message);
        mListenButton=view.findViewById(R.id.live_listen_button_layout);
        mCancelButton=view.findViewById(R.id.live_cancel_button_layout);
        mListenButtonAnim=view.findViewById(R.id.live_listen_anim_image);
        mListenButtonText=view.findViewById(R.id.live_listen_message_text);
        mCancelButtonText=view.findViewById(R.id.live_cancel_message_text);
        mRecordButtonText=view.findViewById(R.id.live_countdown_text);
        mScreenBulletContainer=view.findViewById(R.id.live_screen_bullet_layout);
        mTopGradientImage=view.findViewById(R.id.live_top_gradient);
        mBottomGradientImage=view.findViewById(R.id.live_bottom_gradient);
        mRootLayout=view.findViewById(R.id.live_root_layout);
        mTopGuideline=view.findViewById(R.id.live_image_top_guideline);

        mMinimumImage.setOnClickListener(v -> onViewClick(v));
        mStopLiveImage.setOnClickListener(v -> onViewClick(v));
        mRecordButtonImage.setOnClickListener(v -> onViewClick(v));
        mListenButton.setOnClickListener(v -> onViewClick(v));
        mCancelButton.setOnClickListener(v -> onViewClick(v));

        initViewInner();
        KRadioSpeakImageInter kRadioSpeakImageInter = ClazzImplUtil.getInter("KRadioSpeakImageImpl");
        if (kRadioSpeakImageInter != null && kRadioSpeakImageInter.hideSpeakImage()) {
            ViewUtil.setViewVisibility(mRecordButtonImage, View.GONE);
        }
    }
    public void onViewClick(View v) {
        int id = v.getId();

        if (id == R.id.live_minimum) {
            backToRadioFragment();
        } else if (id == R.id.live_stop) {
            LiveManager.getInstance().onLiveExit();
            exitChatRoom();
            bPlayerEnabled = false;
            pop();
        } else if (id == R.id.live_cancel_button_layout) {
            cancel();
        } else if (id == R.id.live_listen_button_layout) {
            if (LiveManager.getInstance().isPlaying()) {
                stopListen();
            } else if (LiveManager.getInstance().isFinished()) {
                startListen();
            } else if (LiveManager.getInstance().isListened()) {
                startListen();
            }
        } else if (id == R.id.live_leave_a_message) {
            boolean isUserLogin = ComprehensiveLiveUtil.showLoginIfNotLogin();
            if (isUserLogin) {
                if (LiveManager.getInstance().isRecording()) {
                    stopRecord();
                } else if (LiveManager.getInstance().isIdle()) {
                    startRecordWithPermissionCheck();
                } else if (LiveManager.getInstance().isUploading()) {
                    //
                } else if (LiveManager.getInstance().isUploaded()) {
                    LiveManager.getInstance().setRecorderStatus(RecorderStatus.IDLE);
                } else {
                    sendMessage();
                }
            }
        }
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPlayerChanged(PlayerChangedEBData playerChangedEBData) {
        if (bPlayerEnabled) {
            LiveManager.getInstance().onLiveExit();
            LiveFragment.this.pop();
            exitChatRoom();
        }
    }

    @Override
    public void showFileNotExist() {
        String msg = getResources().getString(R.string.live_upload_failed) + ": "
                + getResources().getString(R.string.live_file_not_exist);
        showErrorToast(msg);
        LiveManager.getInstance().setRecorderStatus(RecorderStatus.IDLE);
    }

    @Override
    public void showRecordUploadProgress(int progress) {
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "showRecordUploadProgress: " + progress);
        }
        mRecordButtonImage.setProgress(progress);
    }

    @Override
    public void showRecordUploadSuccess() {
        LiveManager.getInstance().setRecorderStatus(RecorderStatus.UPLOADED);
        LiveManager.getInstance().deleteFile();
    }

    @Override
    public void showRecordUploadFailure() {
        //由于文件上传的回调是在子线程进行的，所以用post的方式让它在UI线程更新UI
        post(new Runnable() {
            @Override
            public void run() {
                ToastUtil.showError(getContext(), R.string.live_upload_failed);
                LiveManager.getInstance().setRecorderStatus(RecorderStatus.FAILURE);
            }
        });
    }

    public void showLiveInfo(LiveDetails liveInfo) {
        if (liveInfo == null) {
            return;
        }
        String roomId = String.valueOf(liveInfo.roomId);
        if (!TextUtils.isEmpty(roomId)) {
            mPresenter.enterChatRoom(getContext(), roomId);
        }
        setContent(liveInfo);
    }

    private void setContent(LiveDetails liveInfo) {
        showCommonInfo(liveInfo);
        mPresenter.getListenerNumber();
    }

    private void showCommonInfo(LiveDetails liveInfo) {
        loadImage(liveInfo.livePic, false);
        mLiveNameText.setText(liveInfo.programName);
        mLivePlayerText.setText(String.format(getString(R.string.live_anchor), liveInfo.comperes));
    }

    private void loadImage(String imageUrl, boolean isChangeLand) {
        if (mImageUri != null && mImageUri.equals(imageUrl) && !isChangeLand) {
            return;
        }
        if (!StringUtil.isEmpty(mImageUri)) {
            ImageLoader.getInstance().getBitmapFromCache(getContext(), mImageUri, bitmap -> {
                if (mLiveImage != null && bitmap != null) {
                    mLiveImage.setScaleType(ImageView.ScaleType.CENTER_CROP);
                    mLiveImage.setImageBitmap(bitmap);
                }
            });
        } else {
            mImageUri = imageUrl;
            ImageLoader.getInstance().displayImage(getContext(), imageUrl, mLiveImage,
                    new OnImageLoaderListener() {
                        @Override
                        public void onLoadingFailed(String url, ImageView target, Exception exception) {
                            if (mLiveImage != null) {
                                mLiveImage.setScaleType(ImageView.ScaleType.CENTER);
                            }
                        }

                        @Override
                        public void onLoadingComplete(String url, ImageView target) {
                            if (mLiveImage != null) {
                                mLiveImage.setScaleType(ImageView.ScaleType.CENTER_CROP);
                            }
                        }
                    });
        }
    }


    /**
     * 成员进入动画，在顶部自右往左飞过
     *
     * @param member
     */
    private void showMemberEnter(ChatUserInfo member) {
        Context context = getContext();
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001107580293问题
        if (context == null) {
            return;
        }
        final TextView textView = (TextView)
                LayoutInflater.from(context).inflate(R.layout.live_screen_bullet_text, null);
        textView.setText(String.format(getString(R.string.live_member_enter), member.getNickName()));
//        textView.setBackgroundResource(R.drawable.live_screen_bullet_bg);
        textView.setBackgroundDrawable(SkinCompatResources.getDrawable(getContext(), R.drawable.live_screen_bullet_bg));
        textView.setGravity(Gravity.CENTER_VERTICAL);
        FrameLayout.LayoutParams lp = new FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.WRAP_CONTENT,
                FrameLayout.LayoutParams.WRAP_CONTENT);
        lp.topMargin = (int) (mScreenBulletContainer.getHeight() * 0.2);
        mScreenBulletContainer.addView(textView, lp);

        int start = mScreenBulletContainer.getRight();
        //最小化按钮右边50px慢下来
        int middle = mMinimumImage.getRight() + 50;
        int left = mMinimumImage.getLeft();

        ObjectAnimator oar = ObjectAnimator.ofFloat(textView, "x", start, middle);
        oar.setDuration(500);
        oar.setInterpolator(new DecelerateInterpolator());

        ObjectAnimator oam = ObjectAnimator.ofFloat(textView, "x", middle, left);
        oam.setDuration(3000);

        //滑出屏幕，需要一个负值,由于View刚Add，还没有宽度，所以写一个参考值
        ObjectAnimator oal = ObjectAnimator.ofFloat(textView, "x", left, -300);
        oal.setDuration(300);
        oal.setInterpolator(new AccelerateInterpolator());

        AnimatorSet animSet = new AnimatorSet();
        animSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
                bDoMemberEnterAnim = true;
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                bDoMemberEnterAnim = false;
                if (mScreenBulletContainer != null && textView != null) {
                    mScreenBulletContainer.removeView(textView);
                }
                if (mMemberEnterQueue.size() > 0) {
                    ChatUserInfo member = mMemberEnterQueue.removeFirst();
                    showMemberEnter(member);
                }
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                bDoMemberEnterAnim = false;
            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        animSet.play(oal).after(oam).after(oar);
        animSet.start();
    }

    /**
     * 收到消息动画，在左下部向上弹起
     *
     * @param message
     */
    private void showMessageReceived(MessageBean message) {
        final TextView textView = (TextView)
                LayoutInflater.from(getContext()).inflate(R.layout.live_screen_bullet_text, null);
        textView.setText(String.format(getString(R.string.live_message_received), message.nickName));
//        textView.setBackgroundResource(R.drawable.live_message_received_bg);
        textView.setBackgroundDrawable(SkinCompatResources.getDrawable(getContext(), R.drawable.live_message_received_bg));
        final FrameLayout.LayoutParams lp = new FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.WRAP_CONTENT,
                FrameLayout.LayoutParams.WRAP_CONTENT);
        lp.leftMargin = mMinimumImage.getLeft();
        mScreenBulletContainer.addView(textView, lp);

        int bottom = mScreenBulletContainer.getBottom();

        if (mLastMessageText != null) {
            int s = (int) mLastMessageText.getY();
            int e = (int) (bottom * 0.60);

            ObjectAnimator oan = ObjectAnimator.ofFloat(mLastMessageText, "y", s, e);
            oan.setDuration(300);

            ObjectAnimator oaa = ObjectAnimator.ofFloat(mLastMessageText, "alpha", 1, 0);
            oaa.setDuration(300);

            AnimatorSet as = new AnimatorSet();
            as.playTogether(oan, oaa);
            as.start();
        }

        int width = View.MeasureSpec.makeMeasureSpec((1 << 30) - 1, View.MeasureSpec.AT_MOST);
        int height = View.MeasureSpec.makeMeasureSpec((1 << 30) - 1, View.MeasureSpec.AT_MOST);
        textView.measure(width, height);

        int middle = bottom - (textView.getMeasuredHeight() * 2);
        // 解决http://redmine.itings.cn/issues/40841问题
        ObjectAnimator oan = ObjectAnimator.ofFloat(textView, "y", bottom, middle - ResUtil.getInt(R.integer.live_msg_y_d_value));
        oan.setDuration(500);
        oan.setInterpolator(new DecelerateInterpolator());
        AnimatorSet animSet = new AnimatorSet();
        animSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
                bDoMessageReceiveAnim = true;
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                bDoMessageReceiveAnim = false;
                if (mLastMessageText != null) {
                    mScreenBulletContainer.removeView(mLastMessageText);
                }
                mLastMessageText = textView;
                if (mMessageReceiveQueue.size() > 0) {
                    MessageBean mb = mMessageReceiveQueue.removeFirst();
                    showMessageReceived(mb);
                }
                mLastMessageText.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (mLastMessageText != null && mScreenBulletContainer != null) {
                            mScreenBulletContainer.removeView(mLastMessageText);
                        }
                    }
                }, 4000);
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                bDoMessageReceiveAnim = false;
            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        animSet.play(oan);
        animSet.start();
    }

    /**
     * 显示收听人数
     *
     * @param number
     */
    @Override
    public void showListenerNumber(int number) {
        mListenerNumberText.setText(String.format(getString(R.string.live_listening_number), number));
        mPlayerVerticalLine.setVisibility(View.VISIBLE);
        mListenerNumberText.setVisibility(View.VISIBLE);
    }

    /**
     * 显示成员进入，如果当前正在执行{@link #showMemberEnter(ChatUserInfo)}中的动画,则这个消息进入队列，
     * 等动画完成后再进行
     *
     * @param member
     */
    @Override
    public void showRoomMemberEnter(ChatUserInfo member) {
        if (member != null) {
            if (LivePresenter.DEBUG_LIVE) {
                Log.i(TAG, "showRoomMemberEnter: " + member.getNickName());
            }
            //如果正在做成员进入动画，或者成员进入队列中有元素在排队，添加到队尾，否则展示此条进入提示
            if (mMemberEnterQueue.size() > 0 || bDoMemberEnterAnim) {
                mMemberEnterQueue.addLast(member);
            } else {
                showMemberEnter(member);
            }
        }

    }

    /**
     * 显示收到消息，如果当前正在执行{@link #showMessageReceived(MessageBean)}中的动画,
     * 则这个消息进入队列，等动画完成后再进行。如果是多条消息，则逐条进入队列
     *
     * @param messageList
     */
    @Override
    public void showChatMessageReceived(ArrayList<MessageBean> messageList) {
        if (messageList == null || messageList.size() <= 0) {
            if (LivePresenter.DEBUG_LIVE) {
                Log.i(TAG, "showChatMessageReceived empty message list");
            }
            return;
        }
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "showChatMessageReceived message count: " + messageList.size());
        }
        //消息以列表的方式组织，所以如果消息队列中有消息或者正在进行消息提示的动画，把所有
        //新来的消息添加到队列，
        if (mMessageReceiveQueue.size() > 0 || bDoMessageReceiveAnim) {
            mMessageReceiveQueue.addAll(messageList);
        } else {
            //否则，取列表中的第一条展示，如果还有剩下的，进入消息队列
            MessageBean message = messageList.remove(0);
            showMessageReceived(message);
            if (messageList.size() > 0) {
                mMessageReceiveQueue.addAll(messageList);
            }
        }

    }

    @Override
    public void onDestroy() {
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "onDestroy");
        }
        LiveStateManager.getInstance().unregisterLiveLifecycleListener(Long.parseLong(PlayerManagerHelper.getInstance().getCurPlayItem().getRadioId()), mLiveLifecycleListener);
        LiveManager.getInstance().removeRecorderStatusObserver(mRecorderStatusObserver);
        if (LiveManager.getInstance().isPlaying()) {
            LiveManager.getInstance().stopListen();
        }
        if (LiveManager.getInstance().isRecording()) {
            LiveManager.getInstance().stopRecord(true);
        }

        stopRecordTimer();
        stopForecastTimer();
        super.onDestroy();
        ComponentUtil.removeObserver(UserComponentConst.NAME, mUserLoginComponent);

    }

    private void exitChatRoom() {
        // 添加null检查，避免在Fragment销毁过程中访问已置空的presenter
        if (mPresenter != null) {
            mPresenter.exitChatRoom();
        }
        clearChatRoomQueue();
    }

    private void clearChatRoomQueue() {
        mMemberEnterQueue.clear();
        mMessageReceiveQueue.clear();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
                                           @NonNull int[] grantResults) {
        switch (requestCode) {
            case CODE_PERMISSION_REQUEST:
                if (grantResults.length > 0
                        && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    startRecord();
                } else {

                }
                break;
        }
    }

    private void startRecordWithPermissionCheck() {
        List<String> needPermission = new ArrayList<String>();
        boolean needRecordAudio = ContextCompat.checkSelfPermission(getContext(),
                Manifest.permission.RECORD_AUDIO) != PackageManager.PERMISSION_GRANTED;
        if (needRecordAudio) {
            needPermission.add(Manifest.permission.RECORD_AUDIO);
        }
        boolean needReadExternal = ContextCompat.checkSelfPermission(getContext(),
                Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED;
        if (needReadExternal) {
            needPermission.add(Manifest.permission.READ_EXTERNAL_STORAGE);
        }
        boolean needWriteExternal = ContextCompat.checkSelfPermission(getContext(),
                Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED;
        if (needWriteExternal) {
            needPermission.add(Manifest.permission.WRITE_EXTERNAL_STORAGE);
        }
        if (needPermission.size() > 0) {
            String[] requestPermissions = new String[needPermission.size()];
            for (int i = 0; i < needPermission.size(); i++) {
                requestPermissions[i] = needPermission.get(i);
            }
            ActivityCompat.requestPermissions(getActivity(), requestPermissions,
                    CODE_PERMISSION_REQUEST);
        } else {
            startRecord();
        }
    }

    private void startRecord() {
        long delta = SystemClock.elapsedRealtime() - mLastRecordTime;
        if (delta < DURATION_LONG) {
            if (LivePresenter.DEBUG_LIVE) {
                Log.i(TAG, "startRecord but period to last recording is too short");
            }
            return;
        }
        //recordAndSetSystemVolume();
        //解除语音助手对麦克风的占用
//        IASRControl ias = (IASRControl) ARouter.getInstance()
//                .build(RouterFragmentConstants.FRAG_KAOLA_VOICE_ASSISTANT_CONTROL).navigation();
//        ias.stopASR();
        LiveManager.getInstance().startRecordDeal();
    }

    /**
     * 录音倒计时，到时间后自动结束录音
     */
    private void startCountdownTimer() {
        if (mRecordTimer != null) {
            mRecordTimer.cancel();
            mRecordTimer = null;
        }
        String s = getResources().getString(R.string.live_second);
        mRecordButtonText.setText(RECORD_DURATION / 1000 + s);
        mRecordTimer = new CountDownTimer(RECORD_DURATION, RECORD_COUNTDOWN) {
            @Override
            public void onTick(long millisUntilFinished) {
                mRecordButtonText.setText((millisUntilFinished / 1000) + s);
            }

            @Override
            public void onFinish() {
                stopRecord();
            }
        };
        mRecordTimer.start();
    }

    private void startListenTimer() {
        mListenButtonAnim.setVisibility(View.VISIBLE);
        mListenButtonText.setVisibility(View.GONE);
    }

    private void stopListenTimer() {
        mListenButtonText.setVisibility(View.VISIBLE);
        mListenButtonAnim.setVisibility(View.GONE);
    }

    private void stopRecord() {
        String path = LiveManager.getInstance().stopRecord();
        String recordFile = LiveManager.getInstance().getFilePath();
        //restoreSystemVolume();
        //让语音助手继续监听麦克风
//        IASRControl ias = (IASRControl) ARouter.getInstance()
//                .build(RouterFragmentConstants.FRAG_KAOLA_VOICE_ASSISTANT_CONTROL).navigation();
//        ias.startASR();
        stopRecordTimer();
        if (recordFile != null && recordFile.equals(path)) {
            startRecordFinishAnim();
        } else if (LiveManager.RECORD_TIME_TOO_SHORT.equals(path)) {
            ToastUtil.showNormal(getContext(), R.string.live_speak_too_short);
        }
    }

    private void cancelRecord() {
        stopRecordTimer();
        stopRecordingAnim();
        mRecordButtonText.setVisibility(View.INVISIBLE);
        LiveManager.getInstance().stopRecord(true);
//        IASRControl ias = (IASRControl) ARouter.getInstance()
//                .build(RouterFragmentConstants.FRAG_KAOLA_VOICE_ASSISTANT_CONTROL).navigation();
//        ias.startASR();
    }

    /**
     * 录音正在进行动画，是一个圆在不断地放大-缩小
     */
    private void startRecordFinishAnim() {
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "startRecordFinishAnim");
        }
        int recordWidth = mRecordButtonImage.getMeasuredWidth();
        int middle = mRecordButtonImage.getLeft() + recordWidth / 2;
        int cancelLeft = mCancelButton.getLeft();
        int listenLeft = mListenButton.getLeft();

        AnimatorSet animSet = new AnimatorSet();
        animSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
                mCancelButton.setVisibility(View.VISIBLE);
                mListenButton.setVisibility(View.VISIBLE);
            }

            @Override
            public void onAnimationEnd(Animator animation) {

            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });

        int animDuration = 300;

        PropertyValuesHolder pvhr = PropertyValuesHolder.ofFloat("x", middle, cancelLeft);
        PropertyValuesHolder pvhl = PropertyValuesHolder.ofFloat("x", middle, listenLeft);

        PropertyValuesHolder pvha = PropertyValuesHolder.ofFloat("alpha", 0, 1);

        ObjectAnimator oali = ObjectAnimator.ofPropertyValuesHolder(mListenButton, pvhl);
        oali.setDuration(animDuration);
        ObjectAnimator oala = ObjectAnimator.ofPropertyValuesHolder(mListenButton, pvha);
        oala.setDuration(animDuration);

        ObjectAnimator oari = ObjectAnimator.ofPropertyValuesHolder(mCancelButton, pvhr);
        oari.setDuration(animDuration);
        ObjectAnimator oara = ObjectAnimator.ofPropertyValuesHolder(mCancelButton, pvha);
        oala.setDuration(animDuration);

        animSet.playTogether(oali, oari, oala, oara);
        animSet.setInterpolator(new DecelerateInterpolator());
        animSet.start();

    }

    private void sendMessage() {
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "sendMessage");
        }
        //可上传状态有：录音完成FINISHED，试听完成LISTENED，正在试听LISTENING, 上传失败FAILURE
        //其他状态：录音中RECORDING，空闲IDLE，正在上传UPLOADING，上传完成UPLOADED则不能上传
        //以此避免无效上传, 但这个判断在按钮点击的时候已经处理了，这里留个备忘
//        if (LiveManager.getInstance().isListened() || LiveManager.getInstance().isPlaying() ||
//                LiveManager.getInstance().isFinished() || LiveManager.getInstance().isFailure()) {
//        }
        stopListen();
        String uid = ComponentUtil.getResultValue(UserComponentConst.NAME, UserComponentConst.GET_USER_ID);

        int timeLength = LiveManager.getInstance().getRecordDuration();
        String uploadFileName = RecordUploadHelper.generateFileUploadName();
        String nickName = mPresenter.getNickName();
        RecordUploadHelper.UploadParam param = new RecordUploadHelper.UploadParam(mLiveInfo.programId, uid,
                nickName, timeLength, uploadFileName);
        // todo live 确认是否使用新版本 sendAudioMessageToServer 发送语音
        mPresenter.sendAudioMessageToServer(getContext(), param, uploadFileName);
        mRecordButtonImage.setProgress(0);
        ReportUtil.addLivingLeaveMessageEvent(String.valueOf(mLiveInfo.liveId), String.valueOf(mLiveInfo.programId), mLiveInfo.comperes, PlayerManagerHelper.getInstance().getCurPlayItem().getRadioId());
    }

    private void recordAndSetSystemVolume() {
        //音量设为0
        AudioManager am = (AudioManager) getContext().getSystemService(Context.AUDIO_SERVICE);
        mSystemVolume = am.getStreamVolume(AudioManager.STREAM_MUSIC);
        am.setStreamVolume(AudioManager.STREAM_MUSIC, 0, AudioManager.FLAG_PLAY_SOUND);
    }

    private void restoreSystemVolume() {
        //还原系统音量
        AudioManager am = (AudioManager) getContext().getSystemService(Context.AUDIO_SERVICE);
        am.setStreamVolume(AudioManager.STREAM_MUSIC, mSystemVolume, AudioManager.FLAG_PLAY_SOUND);
    }


    private boolean requestAudioFocus() {
        if (mKRadioAudioFocusInter == null) {
            mKRadioAudioFocusInter = ClazzImplUtil.getInter("KRadioAudioFocusImpl");
        }

        if (mKRadioAudioFocusInter != null) {
            // 解决https://app.huoban.com/tables/2100000007530121/items/2300001158191064?userId=1881599问题
            boolean flag = mKRadioAudioFocusInter.requestAudioFocus(mAudioFocusListener);
            return flag;
        }
        AudioManager am = (AudioManager) getContext().getSystemService(Context.AUDIO_SERVICE);
        boolean status = AudioManager.AUDIOFOCUS_REQUEST_GRANTED ==
                am.requestAudioFocus(mAudioFocusListener, AudioManager.STREAM_MUSIC,
                        AudioManager.AUDIOFOCUS_GAIN);
        Log.i(TAG, "live requestAudioFocus status:" + status);
        return status;
    }

    private boolean abandonAudioFocus() {
        if (mKRadioAudioFocusInter != null) {
            boolean flag = mKRadioAudioFocusInter.abandonAudioFocus(mAudioFocusListener);
            return flag;
        }
        AudioManager am = (AudioManager) getContext().getSystemService(Context.AUDIO_SERVICE);
        boolean status = AudioManager.AUDIOFOCUS_REQUEST_GRANTED ==
                am.abandonAudioFocus(mAudioFocusListener);
        Log.i(TAG, "live abandonAudioFocus status:" + status);
        return status;
    }

    private AudioManager.OnAudioFocusChangeListener mAudioFocusListener
            = new AudioManager.OnAudioFocusChangeListener() {
        @Override
        public void onAudioFocusChange(int focusChange) {
            if (focusChange == AudioManager.AUDIOFOCUS_LOSS_TRANSIENT) {
                Log.i(TAG, "OnAudioFocusChangeListener:AUDIOFOCUS_LOSS_TRANSIENT");
            } else if (focusChange == AudioManager.AUDIOFOCUS_LOSS) {
                Log.i(TAG, "OnAudioFocusChangeListener:AUDIOFOCUS_LOSS");
            } else if (focusChange == AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK) {
                Log.i(TAG, "OnAudioFocusChangeListener:AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK");
            } else if (focusChange == AudioManager.AUDIOFOCUS_GAIN) {
                Log.i(TAG, "OnAudioFocusChangeListener:AUDIOFOCUS_GAIN");
            }
        }
    };

    private void stopRecordTimer() {
        if (mRecordTimer != null) {
            mRecordTimer.cancel();
            mRecordTimer = null;
        }
    }


    private void stopForecastTimer() {
        if (mForecastTimer != null) {
            mForecastTimer.cancel();
            mForecastTimer = null;
        }
    }

    private void startListen() {
        long delta = SystemClock.elapsedRealtime() - mLastListenTime;
        if (delta < DURATION_LONG) {
            if (LivePresenter.DEBUG_LIVE) {
                Log.i(TAG, "startRecord but period to last listening is too short");
            }
            return;
        }
        if (requestAudioFocus()) {
            LiveManager.getInstance().startListen();
        }
    }

    private void stopListen() {
        LiveManager.getInstance().stopListen();
        stopListenTimer();
        mListenButtonText.setText(R.string.voice_message_audition);
    }

    private void cancel() {
        LiveManager.getInstance().cancel();
        mCancelButton.setVisibility(View.INVISIBLE);
        mListenButton.setVisibility(View.INVISIBLE);
    }

    private void startRecordingAnim() {
        mRecordSoundImage.setVisibility(View.VISIBLE);
        AnimationSet as = (AnimationSet) AnimationUtils.loadAnimation(getContext(), R.anim.anim_live_record_sound);
        mRecordSoundImage.startAnimation(as);
    }

    private void stopRecordingAnim() {
        if (mRecordSoundImage != null) {
            mRecordSoundImage.clearAnimation();
            mRecordSoundImage.setVisibility(View.GONE);
        }
    }

    private void updateLastRecordTime() {
        mLastRecordTime = SystemClock.elapsedRealtime();
    }

    private void updateLastListenTime() {
        mLastListenTime = SystemClock.elapsedRealtime();
    }

    /**
     * 在录音-上传过程中，随着状态的变化面更新界面
     */
    private Observer mRecorderStatusObserver = new Observer() {
        @Override
        public void update(Observable o, Object arg) {
            RecorderStatus status = (RecorderStatus) arg;
            if (LivePresenter.DEBUG_LIVE) {
                Log.i(TAG, "RecorderStatusObserver update: " + status);
            }
            switch (status) {
                case CANCEL:
                    updateLastRecordTime();
                    break;
                case IDLE:
                    showRecordIdle();
                    break;
                case RECORDING:
                    startCountdownTimer();
                    showRecording();
                    break;
                case LISTENING:
                    requestAudioFocus();
                    startListenTimer();
                    break;
                case LISTENED:
                    stopListenTimer();
                    abandonAudioFocus();
                    updateLastListenTime();
                    break;
                case FINISHED:
                    showRecordFinished();
                    break;
                case UPLOADING:
                    showRecordUploading();
                    break;
                case UPLOADED:
                    showRecordUploaded();
                    break;
                case FAILURE:
                    showRecordUploadAgainAsFailure();
                    break;

            }
        }
    };

    private void showRecordIdle() {
        mCancelButton.setVisibility(View.INVISIBLE);
        mListenButton.setVisibility(View.INVISIBLE);
        mRecordButtonImage.setDrawProgress(false);
        mRecordButtonImage.setImageDrawable(SkinCompatResources.getDrawable(getContext(), R.drawable.live_leave_a_message));
        mSpeakImage.setVisibility(View.VISIBLE);
        mLoginPromptText.setVisibility(View.VISIBLE);
        mRecordButtonText.setVisibility(View.INVISIBLE);
        mLoginPromptText.setText(R.string.voice_message_click_to_start);
        stopRecordingAnim();
        abandonAudioFocus();
    }

    private void showRecordFinished() {
        mRecordButtonText.setText(R.string.voice_message_send);
        mLoginPromptText.setVisibility(View.INVISIBLE);
        mRecordButtonImage.setImageDrawable(SkinCompatResources.getDrawable(getContext(), R.drawable.live_send_message_selector));
        mRecordButtonImage.setVisibility(View.VISIBLE);
        stopRecordingAnim();
        abandonAudioFocus();
        updateLastRecordTime();
    }

    private void showRecording() {
        mRecordButtonText.setVisibility(View.VISIBLE);
        mSpeakImage.setVisibility(View.GONE);
        mLoginPromptText.setText(R.string.voice_message_click_finish);
        // mRecordButtonImage.setImageResource(R.drawable.live_leave_message_recording);
        mRecordButtonImage.setImageDrawable(SkinCompatResources.getDrawable(getContext(), R.drawable.live_leave_message_recording));
        startRecordingAnim();
        requestAudioFocus();
    }

    private void showRecordUploading() {
        mCancelButton.setVisibility(View.INVISIBLE);
        mListenButton.setVisibility(View.INVISIBLE);
        mRecordButtonText.setText(R.string.voice_message_sending);
        mLoginPromptText.setVisibility(View.GONE);
        mRecordButtonImage.setDrawProgress(true);
        mRecordButtonImage.setVisibility(View.VISIBLE);
    }

    private void showRecordUploaded() {
        mRecordButtonImage.setDrawProgress(false);
//        mRecordButtonImage.setImageResource(R.drawable.live_message_send_success);
        mRecordButtonImage.setImageDrawable(SkinCompatResources.getDrawable(getContext(), R.drawable.live_message_send_success));
        mRecordButtonImage.setVisibility(View.VISIBLE);
        mRecordButtonText.setVisibility(View.INVISIBLE);
        mRecordButtonImage.removeCallbacks(mChangeToIdleRunnable);
        mRecordButtonImage.postDelayed(mChangeToIdleRunnable, 500);
        showChatMessageReceived(createMineMessage());
    }

    private void showRecordUploadAgainAsFailure() {
        mRecordButtonText.setText(R.string.voice_message_resend);
        mLoginPromptText.setVisibility(View.GONE);
        mRecordButtonImage.setDrawProgress(false);
//        mRecordButtonImage.setImageResource(R.drawable.live_send_failure_selector);
        mRecordButtonImage.setImageDrawable(SkinCompatResources.getDrawable(getContext(), R.drawable.live_send_failure_selector));
        mRecordButtonImage.setVisibility(View.VISIBLE);
        mCancelButton.setVisibility(View.VISIBLE);
        mListenButton.setVisibility(View.VISIBLE);
    }

    private Runnable mChangeToIdleRunnable = new Runnable() {
        @Override
        public void run() {
            LiveManager.getInstance().setRecorderStatus(RecorderStatus.IDLE);
        }
    };

    private ArrayList<MessageBean> createMineMessage() {
        MessageBean mb = new MessageBean();
        String nickName = mPresenter.getNickName();
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(TAG, "createMineMessage nickName: " + nickName);
        }
        if (nickName == null || "null".equals(nickName)) {
            nickName = getResources().getString(R.string.live_nickname_me);
        }
        mb.nickName = nickName;
        ArrayList<MessageBean> list = new ArrayList<>(1);
        list.add(mb);
        return list;
    }

    private class UserLoginComponent implements DynamicComponent {

        @Override
        public String getName() {
            return getClass().getSimpleName() + "$Live";
        }

        @Override
        public boolean onCall(RealCaller caller) {
            String actionName = caller.actionName();
            if (UserStateObserverProcessorConst.USER_LOGOUT.equals(actionName)) {
                Log.i("UserLoginComponent", "onCall: ");
                initLiveManager();
            }
            return false;
        }
    }

    private LiveLifecycleListener mLiveLifecycleListener = new LiveLifecycleListener() {
        @Override
        public void onState(int state, RadioLiveInfo radioLiveInfo) {
            if (radioLiveInfo == null) {
                PlayerLogUtil.log(getClass().getSimpleName(), "onState: callback RadioLiveInfo null");
                return;
            }
            PlayerLogUtil.log(getClass().getSimpleName(), "callback state = " + state + ",  radioLiveInfoDataId = " + radioLiveInfo.getProgramId() + " program id = " + mLiveInfo.programId);

            if (state == LiveStreamPlayItem.Living) {
                Bundle extend = getExtend();
                String action = extend.getString("operating");
                if (TextUtils.equals(action, "yes")) {
                    showLiveInfo(radioLiveInfo.getLiveDetails());
                }
            } else if ((state == LiveStreamPlayItem.PlaybackGenerating || state == LiveStreamPlayItem.NoLive) && radioLiveInfo.getProgramId() == mLiveInfo.programId) {
                if (state == LiveStreamPlayItem.NoLive) {
                    ToastUtil.showOnly(getContext(), R.string.live_finished);
                }
                pop();
            }
        }
    };

    private void backToRadioFragment() {
        pop();
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (ResUtil.getOrientation() == Configuration.ORIENTATION_LANDSCAPE) {
            uploadView(true);
        } else {
            uploadView(false);
        }
    }

    private void uploadView(boolean isLand) {
        ConstraintSet set = new ConstraintSet();
        set.clone(mRootLayout);
        if (isLand) {
            set.setGuidelinePercent(mTopGuideline.getId(), ViewConstants.TITLE_LAND_PERCENT);
            set.constrainPercentHeight(mSpeakImage.getId(), 0.06f);
            set.constrainPercentHeight(mRecordButtonImage.getId(), 0.1f);
            set.constrainPercentHeight(mRecordSoundImage.getId(), 0.14f);
            set.constrainPercentHeight(mListenButton.getId(), 0.08f);
            set.constrainPercentHeight(mCancelButton.getId(), 0.08f);
            //set.constrainPercentHeight(mBottomGradientImage.getId(), 0.3f);
            //   set.setVerticalBias(mLivePlayerText.getId(), 0.06f);
        } else {
            set.setGuidelinePercent(mTopGuideline.getId(), ViewConstants.TITLE_PORT_PERCENT);
            set.constrainPercentHeight(mSpeakImage.getId(), 0.03f);
            set.constrainPercentHeight(mRecordButtonImage.getId(), 0.06f);
            set.constrainPercentHeight(mRecordSoundImage.getId(), 0.08f);
            set.constrainPercentHeight(mListenButton.getId(), 0.045f);
            set.constrainPercentHeight(mCancelButton.getId(), 0.045f);
            //set.constrainPercentHeight(mBottomGradientImage.getId(), 0.6f);
            //  set.setVerticalBias(mLivePlayerText.getId(), 0.75f);
        }
        loadImage(mImageUri, true);
        set.applyTo(mRootLayout);
    }

    private void initViewInner() {
        int mCurrentOrientation = ResUtil.getOrientation();
        if (mCurrentOrientation == Configuration.ORIENTATION_PORTRAIT) {
            uploadView(false);
        } else if (mCurrentOrientation == Configuration.ORIENTATION_LANDSCAPE) {
            uploadView(true);
        }
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        if (!hidden) {
            PlayItem playItem = PlayerManagerHelper.getInstance().getCurPlayItem();
            if (playItem.getType() != PlayerConstants.RESOURCES_TYPE_LIVING) {
                pop();
            }
        }
    }

}
