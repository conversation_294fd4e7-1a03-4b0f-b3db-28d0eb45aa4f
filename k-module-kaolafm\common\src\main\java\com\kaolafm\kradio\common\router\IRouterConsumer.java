package com.kaolafm.kradio.common.router;

/**
 * ARouter的消费者
 */
public interface IRouterConsumer {
    //路由已经完全消费
    public static final String ROUTER_CONSUME_FULLY = "ROUTER_CONSUME_FULLY";


    /**
     * 消费事件
     *
     * @return 是否完全消费，如果完全消费则返回{@link IRouterConsumer#ROUTER_CONSUME_FULLY},否则返回下一个需要消费的pageId
     */
    String consumeRoute(String pageId, Object extra);

    /**
     * @return 页面可以消费的所有pageId
     */
//    List<Integer> willInterceptPageId();
}
