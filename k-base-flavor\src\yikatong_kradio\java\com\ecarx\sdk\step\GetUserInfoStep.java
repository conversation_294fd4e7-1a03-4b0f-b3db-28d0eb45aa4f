package com.ecarx.sdk.step;

import android.text.TextUtils;
import android.util.Log;

import com.ecarx.sdk.ECarX;
import com.ecarx.sdk.ECarXSdk;
import com.ecarx.sdk.TokenInfo;
import com.ecarx.sdk.UserInfo;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

/**
 * <AUTHOR>
 **/
public class GetUserInfoStep extends Step {
    private final ECarX ecarx;

    public GetUserInfoStep(ECarX eCarX) {
        this.ecarx = eCarX;
    }

    @Override
    public void exe() {
        Log.i(ECarX.TAG, "exe:" + getClass().getSimpleName());
        TokenInfo tokenInfo = ecarx.getEcarxTokenInfo();
        if (tokenInfo == null
                || tokenInfo.getAccessTokenResult() == null
                || TextUtils.isEmpty(tokenInfo.getAccessTokenResult().getAccessToken())
                || TextUtils.isEmpty(tokenInfo.getAccessTokenResult().getOpenId())) {
            //ecarx.updateEcarxTokenInfo(null);
            ecarx.updateStep();
            ecarx.nextStep();
        } else {
            String accessToken = tokenInfo.getAccessTokenResult().getAccessToken();
            String openId = tokenInfo.getAccessTokenResult().getOpenId();
            ECarXSdk.getInstance().getUserInfo(openId, accessToken, new HttpCallback<UserInfo>() {
                @Override
                public void onSuccess(UserInfo userInfo) {
                    ecarx.updateEcarxUserInfo(userInfo);
                    ecarx.updateStep();
                    ecarx.nextStep();
                }

                @Override
                public void onError(ApiException e) {
//                    if (exception instanceof ECarXAccessTokenExpiredException) {
//                        ecarx.updateEcarxTokenInfo(null);
//                        ecarx.updateStep();
//                        ecarx.nextStep();
//                    }
                    Log.i(ECarX.TAG, "   onError: error=" + e);
                    ecarx.error(e);
                }
            });
        }
    }
}
