package com.kaolafm.kradio.online.categories;

import android.os.Bundle;
import androidx.fragment.app.Fragment;
import android.util.Log;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.kradio.lib.base.ui.online.BaseViewPagerFragment;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/4/16
 */

public class AllCategoriesPresenter extends BasePresenter<AllCategoriesModel, IAllCategoriesView> {
    private long mFatherCode;
    private long mSonCode;
    private Fragment mFragmentQQ;
    //    private Fragment mFragmentBroadcast;
    private static final String TAG = "AllCategoriesPresenter";

    public AllCategoriesPresenter(IAllCategoriesView view, long fatherCode, long sonCode) {
        super(view);
        this.mFatherCode = fatherCode;
        this.mSonCode = sonCode;
    }

    @Override
    protected AllCategoriesModel createModel() {
        return new AllCategoriesModel();
    }


    public void loadData() {
        Log.i(TAG, "loadData start " + mModel);
        if (mModel == null) {
            return;
        }
        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
            ApiException exception = new ApiException(ErrorCode.NO_NET, ResUtil.getString(R.string.no_net_work_str));
            mView.showError(exception);
            return;
        }
        mModel.getTopTabsAndFragments(mFatherCode, new HttpCallback<AllCategory>() {

            @Override
            public void onSuccess(AllCategory allCategory) {
                Log.i(TAG, "loadData onSuccess " + allCategory);
                if (allCategory != null) {
                    String[] tabNames = allCategory.tabNames;
                    if (!ListUtil.isEmpty(tabNames)) {
                        List<Fragment> fragments = allCategory.fragments;
                        Fragment fragment = fragments.get(allCategory.showIndex);
                        Bundle arguments = fragment.getArguments();
                        if (arguments == null) {
                            arguments = new Bundle();
                        }
                        arguments.putLong(CategoryConstant.SUBCATEGORY_ID, mSonCode);
                        fragment.setArguments(arguments);
                        if (mView != null) {
                            mView.showData(tabNames, allCategory.code,fragments, allCategory.showIndex);
                        }
                        return;
                    }
                }
                if (mView != null) {
                    mView.showError(new ApiException("数据为空"));
                }
            }

            @Override
            public void onError(ApiException e) {
                Log.i(TAG, "loadData onError " + e);
                if (mView != null) {
                    mView.showError(e);
                }
            }
        });
    }
//
//    /**
//     * 通过类型获取index
//     *
//     * @param mediaTypeMusic
//     * @return
//     */
//    private int getIndexByType(List<AllCategoriesItem> data, int mediaTypeMusic) {
//        int rst = 0;
//        if (data != null && !data.isEmpty()) {
//            for (int i = 0; i < data.size(); i++) {
//                if (data.get(i).type == mediaTypeMusic) {
//                    rst = i;
//                    break;
//                }
//            }
//        }
//        return rst;
//    }

//    @Override
//    public void onStatusChanged(int newStatus, int oldStatus) {
//        if (oldStatus == NetworkMonitor.STATUS_NO_NETWORK
//                && (newStatus == NetworkMonitor.STATUS_WIFI || newStatus == NetworkMonitor.STATUS_MOBILE)) {
//            NetworkManager.getInstance().addNetworkReadyListener(new NetworkManager.INetworkReady() {
//                @Override
//                public void networkChange(boolean hasNetwork) {
//                    if (hasNetwork) {
//                        NetworkManager.getInstance().addNetworkReadyListener(this);
//                        loadData();
//                    }
//                }
//            });
//        }
//    }
//
//    @OnLifecycleEvent(Lifecycle.Event.ON_RESUME)
//    public void registerListener() {
//        //NetworkMonitor.getInstance(AppDelegate.getInstance().getContext()).registerNetworkStatusChangeListener(this);
//    }
//
//    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
//    public void unregisterListener() {
//        //NetworkMonitor.getInstance(AppDelegate.getInstance().getContext()).registerNetworkStatusChangeListener(this);
//    }

}
