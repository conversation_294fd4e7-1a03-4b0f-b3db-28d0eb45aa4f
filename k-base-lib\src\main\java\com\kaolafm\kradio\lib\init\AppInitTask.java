package com.kaolafm.kradio.lib.init;

import androidx.annotation.NonNull;

import com.kaolafm.kradio.lib.utils.YTLogUtil;

/**
 * 初始化任务。内部逻辑处理使用，外部不需要关心
 * <AUTHOR>
 * @date 2019-09-10
 */
public class AppInitTask implements Comparable<AppInitTask> {

    public int priority;

    public int process;

    public String description;

    public boolean isAsync;
    
    public AppInitializable initializer;

    public AppInitTask(int priority, int process, String description, boolean isAsync, AppInitializable initializer) {
        YTLogUtil.logStart("AppInitTask", "AppInitTask", "priority = " + priority + ", process = " + process + ", description = " + description + ", isAsync = " + isAsync + ", initializer = " + initializer);
        this.priority = priority;
        this.process = process;
        this.description = description;
        this.isAsync = isAsync;
        this.initializer = initializer;
    }

    @Override
    public int compareTo(@NonNull AppInitTask item) {
        return Integer.compare(this.priority, item.priority);
    }

    public boolean inAllProcess() {
        return process == Process.ALL;
    }

    public boolean inMainProcess() {
        return process == Process.MAIN || inAllProcess();
    }

    public boolean inOtherProcess() {
        return process == Process.OTHER || inAllProcess();
    }
}
