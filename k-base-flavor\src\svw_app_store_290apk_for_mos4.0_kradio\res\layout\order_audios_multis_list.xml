<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/order_bg_color">

    <LinearLayout
        android:id="@+id/ll_list"
        android:layout_marginBottom="@dimen/m30"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:paddingLeft="@dimen/m60"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/cl_btn"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text=""
            android:textSize="@dimen/text_size5"
            android:textColor="@color/order_select_all_text_color"
            />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <com.scwang.smartrefresh.layout.SmartRefreshLayout
                android:id="@+id/refreshLayout"
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_order_audios_list"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scrollbars="vertical"
                    android:fadeScrollbars="false"
                    android:scrollbarThumbVertical="@drawable/order_list_scroll_bar_bg"
                    android:scrollbarTrackVertical="@drawable/order_list_scroll_bar_track_bg"
                    android:scrollbarAlwaysDrawVerticalTrack="true"/>
            </com.scwang.smartrefresh.layout.SmartRefreshLayout>

        </RelativeLayout>


        <RelativeLayout
            android:layout_marginTop="@dimen/m13"
            android:layout_width="match_parent"
            android:layout_height="@dimen/m65"
            android:layout_marginBottom="@dimen/m70"
            android:gravity="bottom">

            <TextView
                android:id="@+id/tv_1"
                android:layout_alignParentLeft="true"
                android:layout_alignParentTop="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="总计:"
                android:textSize="@dimen/text_size3"
                android:textColor="@color/order_select_all_text_color"
                />

            <TextView
                android:id="@+id/tv_money"
                android:layout_toRightOf="@id/tv_1"
                android:layout_alignBaseline="@id/tv_1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0"
                android:textSize="@dimen/m50"
                android:textColor="@color/order_money_color"
                />

            <TextView
                android:id="@+id/tv_yb"
                android:layout_toRightOf="@id/tv_money"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="元"
                android:textSize="@dimen/text_size3"
                android:textColor="@color/order_money_color"
                />

            <TextView
                android:id="@+id/tv_3"
                android:layout_toRightOf="@id/tv_yb"
                android:layout_marginLeft="@dimen/m86"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="已选:"
                android:textSize="@dimen/text_size3"
                android:textColor="@color/order_select_all_text_color"
                />

            <TextView
                android:id="@+id/tv_num"
                android:layout_toRightOf="@id/tv_3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0集"
                android:textSize="@dimen/text_size3"
                android:textColor="@color/order_select_all_text_color"
                />

        </RelativeLayout>

    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_btn"
        android:layout_marginTop="@dimen/m36"
        android:layout_width="@dimen/x241"
        android:layout_height="match_parent"
        android:paddingRight="@dimen/m20"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toRightOf="@id/ll_list"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:id="@+id/tv_select_all"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="全选本页"
            android:textSize="@dimen/text_size3"
            android:textColor="@color/order_select_all_text_color"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"/>

        <TextView
            android:id="@+id/tv_pay"
            android:layout_width="@dimen/x140"
            android:layout_height="@dimen/y50"
            android:layout_marginBottom="@dimen/m89"
            android:gravity="center"
            android:text="支付"
            android:textSize="@dimen/text_size3"
            android:textColor="@color/colorWhite"
            android:background="@drawable/order_pay_btn_bg"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>