package com.kaolafm.kradio.online.home.location;

import android.content.Context;
import android.content.res.Configuration;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;
import androidx.constraintlayout.widget.Guideline;
import androidx.fragment.app.DialogFragment;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.util.TypedValue;
import android.view.ActionMode;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewStub;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.google.android.flexbox.FlexboxLayoutManager;
import com.google.gson.Gson;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.common.ViewConstants;
import com.kaolafm.kradio.common.bean.BroadcastRadioSimpleData;
import com.kaolafm.kradio.common.widget.KradioTextView;
import com.kaolafm.kradio.k_kaolafm.R; 
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioMultiWindowInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioSearchInter;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.lib.base.ui.online.BaseShowHideFragment;
import com.kaolafm.kradio.lib.dialog.Dialogs;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.KaolaAppConfigData;
import com.kaolafm.kradio.lib.utils.MultiUtil;
import com.kaolafm.kradio.lib.utils.NetworkMonitor;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.RecyclerViewExposeUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.online.common.event.CloseDrawerEvent;
import com.kaolafm.kradio.online.home.location.adapter.OnlineLocationAssociateAdapter;
import com.kaolafm.kradio.online.home.location.adapter.OnlineLocationSearchHistoryAdapter;
import com.kaolafm.kradio.online.home.location.bean.OnlineRecomandCityBean;
import com.kaolafm.kradio.online.search.bean.SearchProgramBeanResult;
import com.kaolafm.kradio.online.search.utils.SearchHistoryManager;
import com.kaolafm.kradio.online.search.utils.SearchItemDecoration;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.report.ReportHelper;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List; 

/**
 * 选择定位城市
 */
public class OnlineLocationSelectFragment extends BaseShowHideFragment<OnlineLocationSelectPresenter>
        implements ILocationSelectView, RecyclerViewExposeUtil.OnItemExposeListener {
    private static final String TAG = OnlineLocationSelectFragment.class.getSimpleName();
    
    ConstraintLayout mClSearchView; 
    EditText mEtSearchWord; 
    ImageView mIvSearchWordDelete; 
    KradioTextView mTvSearchHistory; 
    RecyclerView mRvSearchHistoryTags; 
    KradioTextView mTvHotSearchWords; 
    RecyclerView mRvHotSearchWords; 
    RecyclerView mRvAssociateList; 
    View mSearchLoadingView; 
    ViewStub mVsSearchException; 
    ViewStub mVsSearchNetworkError; 
    ImageView mTvClearSearchHistory; 
    TextView mTvSearch; 
    TextView tv_ding_name; 
    ImageView mIvSearchBack; 
    ConstraintLayout mRootLayout; 
    LinearLayout tv_search_ding_ll;

    private TextView mTvException;
    private View mExceptionView;
    private View mNetworkErrorView;

    //加载数据时传入的type
    private String loadDataType = "0";

    private static final int STATE_SEARCH_HISTORY = 0;
    private static final int STATE_SEARCH_ASSOCIATE = 1;
    private static final int STATE_SEARCH_RESULT = 2;
    private static final int STATE_SEARCH_EXCEPTION = 3;
    private static final int STATE_SEARCH_LOADING = 4;

    private int mState = STATE_SEARCH_HISTORY;

    public static final int STATE_LOADING = 0;
    public static final int STATE_LOADED = 1;
    public static final int STATE_LOAD_FAILED = 2;
    private int mLoadState = STATE_LOADING;//默认加载中

    KRadioMultiWindowInter kRadioMultiWindowInter;
    private int lineCount = 2;
    GridLayoutManager layoutManager;


    private boolean isSearchStarted = false;

    private List<String> mHotWords = new ArrayList<>();


    @Override
    protected int getLayoutId() {
        return R.layout.online_fragment_location_search;
    }

    @Override
    protected OnlineLocationSelectPresenter createPresenter() {
        return new OnlineLocationSelectPresenter(this);
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        kRadioMultiWindowInter = ClazzImplUtil.getInter("KradioMultiWindowImpl");
        getActivity().getWindow().setSoftInputMode(
                WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN);
    }

    @Override
    public void initView(View view) {

        mClSearchView = itemView.findViewById(R.id.cl_search_view);
        mEtSearchWord = itemView.findViewById(R.id.et_search_word);
        mIvSearchWordDelete = itemView.findViewById(R.id.iv_search_word_delete);
        mTvSearchHistory = itemView.findViewById(R.id.tv_search_history);
        mRvSearchHistoryTags = itemView.findViewById(R.id.rv_search_history_tags);
        mTvHotSearchWords = itemView.findViewById(R.id.tv_hot_search_words);
        mRvHotSearchWords = itemView.findViewById(R.id.rv_hot_search_words);
        mRvAssociateList = itemView.findViewById(R.id.rv_associate_list);
        mSearchLoadingView = itemView.findViewById(R.id.search_loading);
        mVsSearchException = itemView.findViewById(R.id.vs_search_exception);
        mVsSearchNetworkError = itemView.findViewById(R.id.vs_search_network_error);
        mTvClearSearchHistory = itemView.findViewById(R.id.tv_clear_search_history);
        mTvSearch = itemView.findViewById(R.id.tv_search);
        tv_ding_name = itemView.findViewById(R.id.tv_ding_name);
        mIvSearchBack = itemView.findViewById(R.id.backViewBt);
        mRootLayout = itemView.findViewById(R.id.search_main_layout);
        tv_search_ding_ll = itemView.findViewById(R.id.tv_search_ding_ll);

        mTvSearch.setOnClickListener(v -> onViewClick(v));
        mIvSearchWordDelete.setOnClickListener(v -> onViewClick(v));
        mTvClearSearchHistory.setOnClickListener(v -> onViewClick(v));
        mIvSearchBack.setOnClickListener(v -> onViewClick(v));

        int ori = ResUtil.getOrientation(); //获取屏幕方向
        if (ori == Configuration.ORIENTATION_LANDSCAPE) {
            //横屏
            lineCount = 2;
        } else if (ori == Configuration.ORIENTATION_PORTRAIT) {
            //竖屏
            lineCount = 1;
        }

        mEtSearchWord.addTextChangedListener(mTextWatcher);
        mEtSearchWord.setOnEditorActionListener(mEditActionListener);
//        mEtSearchWord.setLongClickable(true);
//        mEtSearchWord.setTextIsSelectable(true);
        mEtSearchWord.setHighlightColor(ResUtil.getColor(R.color.online_search_bar_text_select_background_color));
        mEtSearchWord.setCustomSelectionActionModeCallback(new ActionMode.Callback() {
            public boolean onCreateActionMode(ActionMode mode, Menu menu) {
                return false;
            }

            public boolean onPrepareActionMode(ActionMode mode, Menu menu) {
                return false;
            }

            public boolean onActionItemClicked(ActionMode mode, MenuItem item) {
                return false;
            }

            public void onDestroyActionMode(ActionMode mode) {
            }
        });

        NetworkMonitor.getInstance(getContext()).registerNetworkStatusChangeListener(mNetworkStatusChangedListener);

        initSearchHistoryView();
        refreshSearchHistoryView();
        initHotSearchWordsView();
        getHotSearchWords();
        initSearchAssociateView();
        initViewInner();
        KRadioSearchInter kRadioSearchInter = ClazzImplUtil.getInter("KRadioSearchImpl");
        if (kRadioSearchInter != null) {
            kRadioSearchInter.configInputSoft(mEtSearchWord);
        }

    }


    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

    }

    @Override
    public void onResume() {
        super.onResume();
        if (tv_ding_name != null)
            tv_ding_name.setText(TextUtils.isEmpty(KaolaAppConfigData.getInstance().getCityName())
                    ? "定位中..."
                    : KaolaAppConfigData.getInstance().getCityName());
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001757409503?userId=1229522问题
//        showSoftKeyboard();
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        mEtSearchWord.setTextSize(TypedValue.COMPLEX_UNIT_PX, ResUtil.getDimen(R.dimen.online_search_text_size));
        if (ResUtil.getOrientation() == Configuration.ORIENTATION_LANDSCAPE) {
            lineCount = 2;
            uploadView(true);
        } else {
            lineCount = 1;
            uploadView(false);
        }
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
    }

    private void setViewShow(boolean b) {
        if (b) {
            tv_search_ding_ll.setVisibility(View.VISIBLE);
        } else {
            tv_search_ding_ll.setVisibility(View.GONE);
        }
        showOrHideHistoryView(b);
        showOrHideHotSearchView(b);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        NetworkMonitor.getInstance(getContext()).removeNetworkStatusChangeListener(mNetworkStatusChangedListener);

    }


    private TextView.OnEditorActionListener mEditActionListener = new TextView.OnEditorActionListener() {
        @Override
        public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
            if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
                return true;
            }
            String keyword = v.getText().toString();
            // 解决https://app.huoban.com/tables/2100000007530121/items/2300001665111238?userId=1229522问题
            //todo
            if (!TextUtils.isEmpty(keyword)) {
                hideSoftKeyboard();
            }
//            searchByKeyword("1", keyword);
            mPresenter.getAssociateWords(keyword);
            return true;
        }
    };

    private TextWatcher mTextWatcher = new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {

        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            if (s.length() == 0) {
                ViewUtil.setViewVisibility(mIvSearchWordDelete, View.GONE);
            } else {
                ViewUtil.setViewVisibility(mIvSearchWordDelete, View.VISIBLE);
            }
        }

        @Override
        public void afterTextChanged(Editable s) {
            if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
                return;
            }
            String word = s.toString();
            if (word.length() == 0) {
                if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
                    setViewShow(false);
                    showNoNetView();
                } else {
                    setViewShow(true);
                }
                mPresenter.cancelRequest();
                if (mState == STATE_SEARCH_RESULT || mState == STATE_SEARCH_EXCEPTION || mState == STATE_SEARCH_LOADING) {
                    refreshSearchHistoryView();
                }
                switchState(STATE_SEARCH_HISTORY);
                return;
            }
            if (isSearchStarted) {
                isSearchStarted = false;
                return;
            }
            if (mPresenter != null) {
                mPresenter.getAssociateWords(word);
            }
        }
    };

    private NetworkMonitor.OnNetworkStatusChangedListener mNetworkStatusChangedListener = new NetworkMonitor.OnNetworkStatusChangedListener() {
        @Override
        public void onStatusChanged(int newStatus, int oldStatus) {
            if (oldStatus == NetworkMonitor.STATUS_NO_NETWORK) {
                if (mState == STATE_SEARCH_EXCEPTION) {
//                    searchByKeyword("1", mEtSearchWord.getText().toString());
                    mPresenter.getAssociateWords(mEtSearchWord.getText().toString());
                }
                getHotSearchWords();
            }
        }
    };

    private void closeDrawer(boolean b) {
        EventBus.getDefault().post(new CloseDrawerEvent(b));
    } 
    public void onViewClick(View view) {
        int id = view.getId();
        if (id == R.id.backViewBt) {
            closeDrawer(false);
            return;
        }
        if (id == R.id.tv_search) {
//            searchByKeyword("1", mEtSearchWord.getText().toString());
            mPresenter.getAssociateWords(mEtSearchWord.getText().toString());
            return;
        }
        if (id == R.id.iv_search_word_delete) {
            mEtSearchWord.setText(Constants.BLANK_STR);
            return;
        }
        if (id == R.id.tv_clear_search_history) {
            if (!AntiShake.check(id)) {
                clearSearchHistory();
            }
            return;
        }
    }

    @Override
    public boolean onBackPressedSupport() {
        return manageBackEvent();
    }

    @Override
    public String getPageId() {
        return Constants.PAGE_ID_LOCATION;
    }


    private void handleBroadcast(SearchProgramBeanResult searchProgramBean) {
        if (PlayerConstants.RESOURCES_TYPE_BROADCAST == searchProgramBean.getType()) {
            BroadcastRadioSimpleData data = new BroadcastRadioSimpleData();
            data.setBroadcastId(searchProgramBean.getId());
            data.setImg(searchProgramBean.getImg());
            String name = searchProgramBean.getAlbumName();
            data.setName(name);
            PlayerManagerHelper.getInstance().addBroadcastRadioSimpleItem(data);
        }
    }


    @Override
    public void showNoNetView() {
        showExceptionView(false, ResUtil.getString(R.string.no_net_work_str));
        setViewShow(false);
    }

    @Override
    public void showNoResultView() {
        showExceptionView(false, ResUtil.getString(R.string.online_search_no_result));
    }

    @Override
    public void showErrorView(int errorCode) {
        if (errorCode == 604) {
            showExceptionView(true, ResUtil.getString(R.string.home_network_timerout));
        } else {
            showExceptionView(true, ResUtil.getString(R.string.home_network_failed));
        }
    }

    List<OnlineRecomandCityBean> hotWords = new ArrayList<>();

    @Override
    public void showHotSearchWordsView(List<OnlineRecomandCityBean> hotWords) {
        if (hotWords == null) {
            showOrHideHotSearchView(false);
            return;
        }
        this.hotWords = hotWords;
        if (hotWords.size() > 0) {
            showOrHideHotSearchView(true);
        } else {
            showOrHideHotSearchView(false);
        }
        Log.d("cai", "============:" + hotWords.size());
        OnlineLocationSearchHistoryAdapter searchHotAdapter = (OnlineLocationSearchHistoryAdapter) mRvHotSearchWords.getAdapter();
        if (searchHotAdapter == null) {
            searchHotAdapter = new OnlineLocationSearchHistoryAdapter();
            searchHotAdapter.setOnItemClickListener(new BaseAdapter.OnItemClickListener<OnlineRecomandCityBean>() {
                @Override
                public void onItemClick(View view, int viewType, OnlineRecomandCityBean onlineRecomandCityBean, int position) {
                    isSearchStarted = true;
                    String json = new Gson().toJson(onlineRecomandCityBean);
                    OnlineLocationSearchHistoryManager.getInstance().addRecentSearchTag(json);
                    KaolaAppConfigData.getInstance().setCityName(onlineRecomandCityBean.name);
                    KaolaAppConfigData.getInstance().setLat(onlineRecomandCityBean.lat);
                    KaolaAppConfigData.getInstance().setLng(onlineRecomandCityBean.lng);
                    closeDrawer(true);
                }
            });
            mRvHotSearchWords.setAdapter(searchHotAdapter);
        }
        searchHotAdapter.setDataList(hotWords);
    }


    @Override
    public void showNetworkError(String error, boolean clickToRetry) {
        isSearchStarted = false;
        if (mNetworkErrorView == null) {
            mNetworkErrorView = mVsSearchNetworkError.inflate();
        }
        ViewUtil.setViewVisibility(mNetworkErrorView, View.VISIBLE);

        // 改变默认的错误信息文本
        TextView tvNetworkNoSign = mNetworkErrorView.findViewById(R.id.tv_error);
        tvNetworkNoSign.setText(error);

        // 支持点击图标重试
        ImageView ivNetworkNoSign = mNetworkErrorView.findViewById(R.id.iv_error);
        if (clickToRetry) {
            ivNetworkNoSign.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    ViewUtil.setViewVisibility(mNetworkErrorView, View.GONE);
//                    searchByKeyword("1", mEtSearchWord.getText().toString());
                    mPresenter.getAssociateWords(mEtSearchWord.getText().toString());
                }
            });
        } else {
            ivNetworkNoSign.setOnClickListener(null);
        }
        switchState(STATE_SEARCH_EXCEPTION);
    }

    @Override
    public void showLoadingView() {
        switchState(STATE_SEARCH_LOADING);
    }

    @Override
    public void showAssociateWordsView(List<OnlineRecomandCityBean> resultBean) {
        if (resultBean == null) {
            return;
        }
        setViewShow(false);
        for (OnlineRecomandCityBean onlineGetCityResultBean : resultBean) {
            onlineGetCityResultBean.keyword = mEtSearchWord.getText().toString();
        }
        Log.d("cai", "%%%%%%%%%%%%%%%%%%%%%==" + resultBean.size());
        OnlineLocationAssociateAdapter associateAdapter = (OnlineLocationAssociateAdapter) mRvAssociateList.getAdapter();
        if (associateAdapter == null) {
            associateAdapter = new OnlineLocationAssociateAdapter();
            associateAdapter.setOnItemClickListener((View v, int viewType, OnlineRecomandCityBean associateInfo, int position) -> {
                isSearchStarted = true;
                String json = new Gson().toJson(associateInfo);
                OnlineLocationSearchHistoryManager.getInstance().addRecentSearchTag(json);
                KaolaAppConfigData.getInstance().setCityName(associateInfo.name);
                KaolaAppConfigData.getInstance().setLat(associateInfo.lat);
                KaolaAppConfigData.getInstance().setLng(associateInfo.lng);
                hideSoftKeyboard();
                closeDrawer(true);
            });
            mRvAssociateList.setAdapter(associateAdapter);
        }
        associateAdapter.setDataList(resultBean);
        switchState(STATE_SEARCH_ASSOCIATE);
    }

    public void showExceptionView(boolean isNeedRefresh, String exceptionString) {
        isSearchStarted = false;
        if (mExceptionView == null) {
            mExceptionView = mVsSearchException.inflate();
            mTvException = mExceptionView.findViewById(R.id.tv_search_exception_message);
        }
        mTvException.setText(exceptionString);
        if (isNeedRefresh) {
            mTvException.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
//                    searchByKeyword("1", mEtSearchWord.getText().toString());
                    mPresenter.getAssociateWords(mEtSearchWord.getText().toString());
                }
            });
        } else {
            mTvException.setOnClickListener(null);
        }
        switchState(STATE_SEARCH_EXCEPTION);
    }

    private void getHotSearchWords() {
        mPresenter.getHotSearchWords();
    }

    private void initSearchHistoryView() {
        FlexboxLayoutManager flexboxLayoutManager = new FlexboxLayoutManager(getContext());
        mRvSearchHistoryTags.setLayoutManager(flexboxLayoutManager);
        mRvSearchHistoryTags.addItemDecoration(new SearchItemDecoration(0, ResUtil.getDimen(R.dimen.y20),
                0, ResUtil.getDimen(R.dimen.y20)));
    }

    private void initHotSearchWordsView() {
        FlexBoxLayoutMaxLines flexboxLayoutManager = new FlexBoxLayoutMaxLines(getContext());
        flexboxLayoutManager.setMaxLine(3);
        mRvHotSearchWords.setLayoutManager(flexboxLayoutManager);
        mRvHotSearchWords.addItemDecoration(new SearchItemDecoration(0, ResUtil.getDimen(R.dimen.y20),
                0, ResUtil.getDimen(R.dimen.y20)));
    }

    private void initSearchAssociateView() {
//        mRvAssociateList.setLayoutManager(new FlexboxLayoutManager(getContext()));
//        mRvAssociateList.addItemDecoration(new SearchItemDecoration(0, ResUtil.getDimen(R.dimen.x60),
//                0, ResUtil.getDimen(R.dimen.y39)));
        mRvAssociateList.setLayoutManager(new LinearLayoutManager(getContext(), LinearLayoutManager.VERTICAL, false));
    }


    private void initSearchTypeView() {
        //todo
        getActivity().getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING);
    }


    private void switchState(int state) {
        if (mState == state) {
            return;
        }
        showOrHideStateView(mState, false);
        mState = state;
        showOrHideStateView(mState, true);
    }

    private void showOrHideStateView(int state, boolean isShow) {
        switch (state) {
            case STATE_SEARCH_HISTORY:
                showOrHideHistoryView(isShow);
                break;
            case STATE_SEARCH_ASSOCIATE:
                showOrHideAssociateView(isShow);
                break;
            case STATE_SEARCH_RESULT:
                break;
            case STATE_SEARCH_EXCEPTION:
                showOrHideExceptionView(isShow);
                break;
            case STATE_SEARCH_LOADING:
                showOrHideLoadingView(isShow);
                break;
            default:
        }
    }

    private void showOrHideHistoryView(boolean isShow) {
        //ViewUtil.setViewVisibility(mSearchHistoryGroup, isShow ? View.VISIBLE : View.GONE);
//        showOrHideHotSearchView(isShow);
        List<String> tags = OnlineLocationSearchHistoryManager.getInstance().getRecentSearchTags();
        if (ListUtil.isEmpty(tags) || !isShow) {
//            ViewUtil.setViewVisibility(mTvClearSearchHistory, View.GONE);
            ViewUtil.setViewVisibility(mTvSearchHistory, View.GONE);
            ViewUtil.setViewVisibility(mRvSearchHistoryTags, View.GONE);
        } else {
//            ViewUtil.setViewVisibility(mTvClearSearchHistory, View.VISIBLE);
            ViewUtil.setViewVisibility(mTvSearchHistory, View.VISIBLE);
            ViewUtil.setViewVisibility(mRvSearchHistoryTags, View.VISIBLE);
        }
        reportSearchPage(isShow);
    }

    private void showOrHideHotSearchView(boolean isShow) {
        if (!isShow || hotWords.size() == 0) {
            ViewUtil.setViewVisibility(mTvHotSearchWords, View.GONE);
            ViewUtil.setViewVisibility(mRvHotSearchWords, View.GONE);
        } else {
            ViewUtil.setViewVisibility(mTvHotSearchWords, View.VISIBLE);
            ViewUtil.setViewVisibility(mRvHotSearchWords, View.VISIBLE);
        }
    }

    private void showOrHideAssociateView(boolean isShow) {
        ViewUtil.setViewVisibility(mRvAssociateList, isShow ? View.VISIBLE : View.GONE);
    }


    private void showOrHideExceptionView(boolean isShow) {
        ViewUtil.setViewVisibility(mExceptionView, isShow ? View.VISIBLE : View.GONE);
    }

    private void showOrHideLoadingView(boolean isShow) {
        ViewUtil.setViewVisibility(mSearchLoadingView, isShow ? View.VISIBLE : View.GONE);
    }

    private void reportSearchPage(boolean isShow) {
        if (isShow) {
            ReportHelper.getInstance().setPage(getPageId());
        }
    }


    private void showSoftKeyboard() {
        mEtSearchWord.setFocusable(true);
        mEtSearchWord.setFocusableInTouchMode(true);
        mEtSearchWord.requestFocus();
        mEtSearchWord.setCursorVisible(true);
        InputMethodManager imm = (InputMethodManager) getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
        imm.showSoftInput(mEtSearchWord, 0);
    }

    private void hideSoftKeyboard() {
        InputMethodManager imm = (InputMethodManager) getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
        imm.hideSoftInputFromWindow(mEtSearchWord.getWindowToken(), InputMethodManager.HIDE_NOT_ALWAYS);
    }

    private Boolean manageBackEvent() {
        if (mState == STATE_SEARCH_HISTORY || mState == STATE_SEARCH_EXCEPTION) {
            if (null != mNetworkErrorView) {
                ViewUtil.setViewVisibility(mNetworkErrorView, View.GONE);
            }
            pop();
        } else {
            mEtSearchWord.setText(Constants.BLANK_STR);
        }
        return false;
    }

    private void refreshSearchHistoryView() {
        OnlineLocationSearchHistoryAdapter searchHistoryAdapter = (OnlineLocationSearchHistoryAdapter) mRvSearchHistoryTags.getAdapter();
        if (searchHistoryAdapter == null) {
            searchHistoryAdapter = new OnlineLocationSearchHistoryAdapter();
            searchHistoryAdapter.setOnItemClickListener(new BaseAdapter.OnItemClickListener<OnlineRecomandCityBean>() {
                @Override
                public void onItemClick(View view, int viewType, OnlineRecomandCityBean onlineRecomandCityBean, int position) {
//                    ToastUtil.showNormal(getContext(), "搜索历史" + position);
                    String json = new Gson().toJson(onlineRecomandCityBean);
                    OnlineLocationSearchHistoryManager.getInstance().addRecentSearchTag(json);
                    if (TextUtils.isEmpty(onlineRecomandCityBean.name)) {
                        KaolaAppConfigData.getInstance().setCityName(onlineRecomandCityBean.name);
                    } else {
                        KaolaAppConfigData.getInstance().setCityName(onlineRecomandCityBean.name);
                    }
                    KaolaAppConfigData.getInstance().setLat(onlineRecomandCityBean.lat);
                    KaolaAppConfigData.getInstance().setLng(onlineRecomandCityBean.lng);
                    closeDrawer(true);
                }
            });
            mRvSearchHistoryTags.setAdapter(searchHistoryAdapter);
        }
        List<String> tags = OnlineLocationSearchHistoryManager.getInstance().getRecentSearchTags();
        List<OnlineRecomandCityBean> beanList = new ArrayList<>();
        OnlineRecomandCityBean bean;
        Gson gson = new Gson();
        for (String tag : tags) {
            bean = gson.fromJson(tag, OnlineRecomandCityBean.class);
            beanList.add(bean);
        }
        searchHistoryAdapter.setDataList(beanList);
        if (beanList.size() == 0) {
            showOrHideHistoryView(false);
        } else {
            showOrHideHistoryView(true);
        }
    }

    private void clearSearchHistory() {
        // 解决[DI4.0_3.5UI][功能缺陷] [规划院] 【云听】[概率100%】云听主界面---搜索，同时点击搜索框中的节目类型下拉按钮和清空记录按钮，节目类型弹框和清空记录弹框能同时存在
//        boolean expanded = mTsSearchType.isExpanded();
//        Log.i(TAG, "expanded:" + expanded);
//        if (expanded) {
//            return;
//        }
        DialogFragment dialogFragment = new Dialogs.Builder()
                .setType(Dialogs.TYPE_2BTN)
                .setGravity(Gravity.CENTER)
                .setMessage(ResUtil.getString(R.string.online_are_you_sure_to_clear_your_search_history))
                .setOnPositiveListener(dialog -> {
                    SearchHistoryManager.getInstance().clearAllRecentSearchTags();
                    refreshSearchHistoryView();
                    showOrHideHistoryView(true);
                    dialog.dismiss();
                })
                .create();
        dialogFragment.show(getFragmentManager(), "clear_search");

    }

    private void uploadView(boolean isLand) {
        mEtSearchWord.setTextSize(TypedValue.COMPLEX_UNIT_PX, ResUtil.getDimen(R.dimen.online_search_text_size));
        boolean isMutiWindow = MultiUtil.getMultiStatus();
//        if (isMutiWindow && kRadioMultiWindowInter != null) {
//            kRadioMultiWindowInter.doMutiSearchView(mTsSearchType, mEtSearchWord);
//        }
        mEtSearchWord.setHint(ResUtil.getString(R.string.online_location_search_hint));
        ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) mClSearchView.getLayoutParams();
        layoutParams.setMarginStart(ResUtil.getDimen(R.dimen.online_search_edit_left_margin));
//        layoutParams.width = ResUtil.getDimen(R.dimen.online_search_edit_width);
        layoutParams.height = ResUtil.getDimen(R.dimen.online_search_edit_height);
        //更改分屏状态下宽度,所以需要在setLayoutParams方法之前调用
        if (isLand && isMutiWindow) {
            if (kRadioMultiWindowInter != null) {
                kRadioMultiWindowInter.doMultiSearchFragment(layoutParams);
            }
        }
        mClSearchView.setLayoutParams(layoutParams);

//        ConstraintLayout.LayoutParams lp = (ConstraintLayout.LayoutParams) mTvSearch.getLayoutParams();
//        lp.setMarginStart(ResUtil.getDimen(R.dimen.online_search_btn_left_margin));
//        lp.width = ResUtil.getDimen(R.dimen.online_search_btn_width);
//        lp.height = ResUtil.getDimen(R.dimen.online_search_btn_height);
//        mTvSearch.setLayoutParams(lp);

//        ConstraintLayout.LayoutParams backLp = (ConstraintLayout.LayoutParams) mIvSearchBack.getLayoutParams();
//        backLp.setMarginStart(ResUtil.getDimen(R.dimen.online_back_left_margin));

//        if (!isLand) {
//            ConstraintLayout.LayoutParams histroyLp = (ConstraintLayout.LayoutParams) mTvSearchHistory.getLayoutParams();
//            histroyLp.setMarginStart(ResUtil.getDimen(R.dimen.m24));
//            ConstraintLayout.LayoutParams associateLp = (ConstraintLayout.LayoutParams) mRvAssociateList.getLayoutParams();
//            associateLp.setMarginStart(ResUtil.getDimen(R.dimen.m24));
//            ConstraintLayout.LayoutParams hotSearchLp = (ConstraintLayout.LayoutParams) mTvHotSearchWords.getLayoutParams();
//            hotSearchLp.setMarginStart(ResUtil.getDimen(R.dimen.m24));
//        } else {
//            ConstraintLayout.LayoutParams histroyLp = (ConstraintLayout.LayoutParams) mTvSearchHistory.getLayoutParams();
//            histroyLp.setMarginStart(1);
//            ConstraintLayout.LayoutParams associateLp = (ConstraintLayout.LayoutParams) mRvAssociateList.getLayoutParams();
//            associateLp.setMarginStart(1);
//            ConstraintLayout.LayoutParams hotSearchLp = (ConstraintLayout.LayoutParams) mTvHotSearchWords.getLayoutParams();
//            hotSearchLp.setMarginStart(1);
//        }
//
//        ConstraintSet set = new ConstraintSet();
//        set.clone(mRootLayout);
//        if (isLand) {
//            set.connect(mTvSearchHistory.getId(), ConstraintSet.LEFT, mClSearchView.getId(), ConstraintSet.LEFT);
//            set.connect(mRvSearchHistoryTags.getId(), ConstraintSet.LEFT, mClSearchView.getId(), ConstraintSet.LEFT);
//            set.connect(mRvAssociateList.getId(), ConstraintSet.LEFT, mClSearchView.getId(), ConstraintSet.LEFT);
//            set.connect(mTvHotSearchWords.getId(), ConstraintSet.LEFT, mClSearchView.getId(), ConstraintSet.LEFT);
//        } else {
//            set.connect(mTvSearchHistory.getId(), ConstraintSet.LEFT, mIvSearchBack.getId(), ConstraintSet.LEFT);
//            set.connect(mRvSearchHistoryTags.getId(), ConstraintSet.LEFT, mTvSearchHistory.getId(), ConstraintSet.LEFT);
//            set.connect(mRvAssociateList.getId(), ConstraintSet.LEFT, mIvSearchBack.getId(), ConstraintSet.LEFT);
//            set.connect(mTvHotSearchWords.getId(), ConstraintSet.LEFT, mIvSearchBack.getId(), ConstraintSet.LEFT);
//        }
//        set.applyTo(mRootLayout);
    }

    public void setGuideline(ConstraintSet set, Guideline guideLine, boolean isLand) {
        if (isLand) {
            set.setGuidelinePercent(guideLine.getId(), ViewConstants.TITLE_LAND_PERCENT);
        } else {
            set.setGuidelinePercent(guideLine.getId(), 0.133f);
        }
    }

    private void initViewInner() {
        int mCurrentOrientation = ResUtil.getOrientation();
        if (mCurrentOrientation == Configuration.ORIENTATION_PORTRAIT) {
            uploadView(false);
        } else if (mCurrentOrientation == Configuration.ORIENTATION_LANDSCAPE) {
            uploadView(true);
        }
    }

    @Override
    public void onPause() {
        super.onPause();
//        hideSoftKeyboard();
    }

    public boolean isReportFragment() {
        return true;
    }

    @Override
    public void onItemViewVisible(boolean visible, int position) {

    }
}
