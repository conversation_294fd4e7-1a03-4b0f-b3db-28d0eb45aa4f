package com.kaolafm.gradle.plugin.component

import com.android.build.gradle.BaseExtension
import com.squareup.javapoet.FieldSpec
import com.squareup.javapoet.JavaFile
import com.squareup.javapoet.TypeName
import com.squareup.javapoet.TypeSpec
import org.gradle.api.Project

import javax.lang.model.element.Modifier
import java.util.regex.Matcher
import java.util.regex.Pattern

/**
 * 生成常量的类
 */
class ConstantFactory {

    private static final String CONST_PACKAGE_NAME = "com.kaolafm.kradio.constant"

    static private Map<String, List<ConstantInfo>> constMaps = new HashMap<>()

    private ConstantFactory() {
    }

    static generateConstant(Project project) {
        if (!constMaps.isEmpty()) {
            def libProject = project.rootProject.project("k-base-lib")
            def javaPath = libProject.buildDir.path + "/generated/source/apt"
            BaseExtension extension = libProject.extensions.getByName("android")
            constMaps.each {
                key, value ->
                    //构建常量类
                    def classNames = key.split("/")
                    def className = classNames[classNames.length - 1]
                    def typeBuilder = TypeSpec.classBuilder(className + "Const")
                            .addModifiers(Modifier.PUBLIC, Modifier.FINAL)
                            .addJavadoc("这是由插件自动生成的，不要修改。\n")
                            .addJavadoc("{@link \$S}\n", key.replace("/", "."))
                    value.each {
                        //常量类型
                        if (isValid(it)) {
                            TypeName type = getType(it.desc)
                            def fieldSpec = FieldSpec.builder(type, it.name)
                                    .addModifiers(Modifier.PUBLIC, Modifier.STATIC, Modifier.FINAL)
                                    .initializer("\$S", it.value)
                                    .build()
                            typeBuilder.addField(fieldSpec)
                        }
                    }
                    typeBuilder.addField(FieldSpec.builder(TypeName.get(String.class), "NAME")
                            .addModifiers(Modifier.PUBLIC, Modifier.STATIC, Modifier.FINAL)
                            .initializer("\$S", className)
                            .build())
                    def javaFile = JavaFile.builder(CONST_PACKAGE_NAME, typeBuilder.build()).build()
                    extension.productFlavors.each {
                        javaFile.writeTo(new File(javaPath + File.separator + it.name +File.separator+ "debug"))
                    }
            }
        }
    }

    static TypeName getType(String desc) {
        return TypeName.get(String.class)
    }

    static boolean isValid(ConstantInfo info) {
        return !isEmpty(info.name) && !isEmpty(info.desc) && info.value != null
    }

    static boolean isEmpty(String text) {
        return text == null || text.size() == 0
    }

    /**驼峰转下划线*/
    static String humpToLine(String line) {
        if (line == null || "".equals(line)) {
            return ""
        }
        line = String.valueOf(line.charAt(0)).toUpperCase().concat(line.substring(1))
        StringBuffer sb = new StringBuffer()
        Pattern pattern = Pattern.compile("[A-Z]([a-z\\d]+)?")
        Matcher matcher = pattern.matcher(line)
        while (matcher.find()) {
            String word = matcher.group()
            sb.append(word.toUpperCase())
            sb.append(matcher.end() == line.length() ? "" : "_")
        }
        return sb.toString()
    }

    static setConsts(Map<String, List<ConstantInfo>> map) {
        constMaps = map
    }

    static addConstInfos(String className, List<ConstantInfo> list) {
        if (className != null && list != null) {
            constMaps.put(className, list)
        }
    }

    static getInfoList(String className) {
        if (className != null && className.size() > 0) {
            return constMaps.get(className)
        }
        return null
    }
}