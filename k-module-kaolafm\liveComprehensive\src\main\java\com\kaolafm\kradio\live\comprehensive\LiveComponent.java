package com.kaolafm.kradio.live.comprehensive;

import android.os.Bundle;
import android.util.Log;

import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.kradio.component.Component;
import com.kaolafm.kradio.component.RealCaller;
import com.kaolafm.kradio.component.SharedConst;
import com.kaolafm.kradio.live.comprehensive.ui.LiveFragment;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.live.player.HomeLiveManager;
import com.kaolafm.kradio.live.player.NimManager;
import com.kaolafm.opensdk.api.live.model.LiveDetails;

import me.yokeyword.fragmentation.SupportActivity;

/**
 * 直播组件对外交互类
 * <AUTHOR>
 * @date 2019-07-16
 */
@SharedConst
public class LiveComponent implements Component {

    private static final String START_FRAGMENT = "startFragment";

    private static final String EXIT_CHAT_ROOM = "exitChatRoom";

    private static final String START_HOME_LIVE = "startHomeLive";

    @Override
    public boolean onCall(RealCaller caller) {
        String actionName = caller.actionName();
        Log.e("LiveComponent", "===onCall actionName=" + actionName);
        //PlayerManagerHelper.getInstance().liveStreamIntercept();
        if (START_FRAGMENT.equals(actionName)) {
            if (true){
                // 统一走主站直播间逻辑
//                long liveId = caller.getParamValue("liveId");
                int containerId = caller.getParamValue("containerId");
                Bundle bundle = new Bundle();
                bundle.putLong("liveId", containerId);
                Log.e("LiveComponent", "===onCall START_FRAGMENT containerId=" + containerId);
                RouterManager.getInstance().jumpPage(RouterConstance.PLAY_LIVE_COMPREHENSIVE_URL, bundle);
            } else {
                LiveDetails liveInfo = caller.getParamValue("liveInfo");
                BaseFragment context = caller.getParamValue("context");
                int containerId = caller.getParamValue("containerId");
                LiveFragment choiceFragment = LiveFragment.create(liveInfo);
                if (context.getTopFragment() != null && !(context.getTopFragment() instanceof LiveFragment)) {
//                context.extraTransaction().start(choiceFragment);
                    context.loadRootFragment(containerId,choiceFragment);
                }
            }
        }else if (EXIT_CHAT_ROOM.equals(actionName)) {
            NimManager.getInstance().exitChatRoom();
            HomeLiveManager.getInstance().onLiveExit(true);
        }else if (START_HOME_LIVE.equals(actionName)) {
            long liveId = caller.getParamValue("liveId");
            SupportActivity context = caller.getParamValue("context");
//            int containerId = caller.getParamValue("containerId");
            Bundle bundle = new Bundle();
            bundle.putLong("liveId",liveId);
            Log.e("LiveComponent", "===onCall START_HOME_LIVE liveId=" + liveId);
//            Intent intent = new Intent(context, ComprehensiveLiveActivity.class);
//            intent.putExtras(bundle);
//            context.startActivity(intent);
            RouterManager.getInstance().jumpPage(RouterConstance.PLAY_LIVE_COMPREHENSIVE_URL, bundle);


//            ComprehensiveLiveActivity choiceFragment = ComprehensiveLiveActivity.newInstance(liveId,"直播详情页");
//            ISupportFragment topFragment = context.getTopFragment();
//            if (topFragment != null && !(context.getTopFragment() instanceof ComprehensiveLiveActivity)) {
////                context.getTopFragment().extraTransaction().start(choiceFragment);
//                context.loadRootFragment(containerId,choiceFragment);
//            }
        }
        return false;
    }
}
