package com.kaolafm.kradio.lib.widget.square;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.TypedArray;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.ImageView;

import com.kaolafm.kradio.lib.R;
import com.kaolafm.kradio.lib.utils.AnimUtil;

/**
 * 正方形的ImageView。根据宽度设置高度的方式呈现正方形
 * <AUTHOR>
 * @date 2018/4/24
 */

@SuppressLint("AppCompatCustomView")
public class SquareImageView extends ImageView {

    private boolean canScale;

    public SquareImageView(@NonNull Context context) {
        this(context, null);
    }

    public SquareImageView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SquareImageView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    private void init(Context context, AttributeSet attrs) {
        TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.SquareImageView);
        canScale = ta.getBoolean(R.styleable.SquareFrameLayout_canScale, true);
        ta.recycle();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        setMeasuredDimension(getDefaultSize(0, widthMeasureSpec), getDefaultSize(0, heightMeasureSpec));
        int childWidthSize = getMeasuredWidth();
        //高度和宽度一样
        heightMeasureSpec = widthMeasureSpec = MeasureSpec.makeMeasureSpec(childWidthSize, MeasureSpec.EXACTLY);
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (canScale){
            int action = event.getAction();
            switch (action) {
                case MotionEvent.ACTION_DOWN:
                    AnimUtil.startScalePress(this);
                    break;
                case MotionEvent.ACTION_OUTSIDE:
                case MotionEvent.ACTION_CANCEL:
                case MotionEvent.ACTION_UP:
                    AnimUtil.startScaleRelease(this);
                    break;
                default:
                    break;
            }
        }
        return super.onTouchEvent(event);
    }
}
