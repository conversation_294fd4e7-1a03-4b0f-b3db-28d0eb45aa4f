package com.kaolafm.kradio.onlineactivity;

import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.onlineactivity.ui.IActivityView;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.activity.model.Activity;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import java.util.List;

/**
 * 1、如果qrCodeList是空则整个页面显示empty
 * 2、activityList是空的情况下只会局中显示qrCodeList的第二个二维码
 */
public class ActivityPresenter extends BasePresenter<ActivityModel, IActivityView> {

    public ActivityPresenter(IActivityView view) {
        super(view);
    }

    @Override
    protected ActivityModel createModel() {
        return new ActivityModel();
    }

    public void getActivityInfo(int pageNum, int pageSize) {
        if (mView != null) {
            mView.showLoading();
            mView.hideEmpty();
        }
        getInfoList(pageNum, pageSize);
    }

    /**
     * 是否有下一页
     */
    private boolean isHaveNext = false;

    public boolean isHaveNext() {
        return isHaveNext;
    }

    private void getInfoList(int pageNum, int pageSize) {
        mModel.getInfoList(pageNum, pageSize, new HttpCallback<BasePageResult<List<Activity>>>() {
            @Override
            public void onSuccess(BasePageResult<List<Activity>> listBasePageResult) {
                if (mView == null) {
                    return;
                }

                if (listBasePageResult != null) {
//                    mNextPage = listBasePageResult.getNextPage();
                    isHaveNext = listBasePageResult.getHaveNext() == Constants.HAVE_PAGE;
                    if (listBasePageResult.getDataList() != null && listBasePageResult.getDataList().size() > 0)
                        mView.onActivityInfo(listBasePageResult.getDataList());
                } else {
                    mView.showEmpty();
                }
                mView.hideLoading();
            }

            @Override
            public void onError(ApiException e) {
                if (mView != null) {
                    mView.hideLoading();
                    mView.showError(ResUtil.getString(R.string.network_nosigin), true);
                }
            }
        });
    }
}
