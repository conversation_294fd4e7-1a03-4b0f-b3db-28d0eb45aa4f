package com.kaolafm.kradio.huawei.listener;

import android.content.Context;
import android.util.Log;

import com.huawei.carmediakit.bean.MediaElement;
import com.huawei.carmediakit.bean.MineDataType;
import com.huawei.carmediakit.bean.PlayState;
import com.huawei.carmediakit.reporter.MediaDataReporter;
import com.huawei.carmediakit.reporter.MediaPlayingReporter;
import com.kaolafm.kradio.huawei.convert.PlayDataConverterUtil;
import com.kaolafm.kradio.huawei.utils.Constant;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.IPlayerStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.kaolafm.kradio.huawei.utils.MediaIdHelper.getMediaId;


public class PlayerListener implements IPlayerStateListener {
    private static final String TAG = Constant.TAG;

    private Context mContext;
    private IPlayerInitCompleteListener onPlayerInitCompleteListener = new IPlayerInitCompleteListener() {
        @Override
        public void onPlayerInitComplete(boolean b) {
            try {
                PlayerManager.getInstance().removePlayerInitComplete(onPlayerInitCompleteListener);
                PlayerManager.getInstance().addPlayControlStateCallback(PlayerListener.this);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    };

    private void initPlayer() {
        PlayerManager playerManager = PlayerManager.getInstance();
        boolean isInitSuccess = playerManager.isPlayerInitSuccess();
        if (isInitSuccess) {
            try {
                PlayerManager.getInstance().addPlayControlStateCallback(PlayerListener.this);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            playerManager.addPlayerInitComplete(onPlayerInitCompleteListener);
        }
    }

    public boolean init() {
        //初始化注册监听接口
        Log.i(TAG, "initSyncInstrument: ");
        mContext = AppDelegate.getInstance().getContext();
        initPlayer();
        return true;
    }

    public boolean release() {
        //
        return true;
    }

    @Override
    public void onIdle(PlayItem playItem) {

    }

    @Override
    public void onPlayerPreparing(PlayItem playItem) {
        Log.i(TAG, "onPlayerPreparing: " + playItem.getAlbumTitle() + "      " + playItem.getTitle());
        updateMediaElement(playItem, true);
        //sendTitle(playItem.getTitle());
        //sendImg(playItem.getPicUrl());
    }

    @Override
    public void onPlayerPlaying(PlayItem playItem) {
        Log.i(TAG, "onPlayerPlaying: MEDAI_STATE_START ");
    }

    @Override
    public void onPlayerPaused(PlayItem playItem) {
        Log.i(TAG, "onPlayerPaused: MEDAI_STATE_PAUSE ");
        updateMediaElement(playItem, false);
    }

    private void updateMediaElement(PlayItem playItem, boolean isPlaying) {
        PlayState playState = new PlayState();
        if (isPlaying) {
            playState.setState(PlayState.State.PLAYING);
        } else {
            playState.setState(PlayState.State.PAUSED);
        }
        MediaPlayingReporter.reportPlayState(playState);

        MediaElement element = PlayDataConverterUtil.toMediaEntityAlbum(playItem);
        element.setPlaying(isPlaying);
        Logger.i(TAG, "media=" + element.getMediaId());
        List<MediaElement> mediaElementList = new ArrayList<>();
        mediaElementList.add(element);
        MediaDataReporter.reportMineDataAdded(MineDataType.LATEST_PLAY, mediaElementList);
        MediaPlayingReporter.reportCurrentSingle(PlayDataConverterUtil.toSingle(playItem, isPlaying));

        long id = PlayerManagerHelper.getInstance().getSubscribeId();
        if (id == Long.parseLong(playItem.getRadioId())) {
            element.setFavorite(true);
        } else {
            element.setFavorite(false);
        }
        MediaDataReporter.reportMediaElementsChange(Collections.singletonList(element));
    }

    @Override
    public void onProgress(PlayItem playItem, long mProgress, long mDuration) {
        Logger.i(TAG, "onProgress=" + mProgress + ":dur=" + mDuration);
        PlayState playState = new PlayState();
        playState.setTotalDuration((int)mDuration);
        playState.setPastTime((int)mProgress);
        playState.setState(PlayState.State.PLAYING);
        MediaPlayingReporter.reportPlayState(playState);
    }

    @Override
    public void onPlayerFailed(PlayItem playItem, int i, int i1) {

    }

    @Override
    public void onPlayerEnd(PlayItem playItem) {

    }

    @Override
    public void onSeekStart(PlayItem playItem) {

    }

    @Override
    public void onSeekComplete(PlayItem playItem) {

    }

    @Override
    public void onBufferingStart(PlayItem playItem) {

    }

    @Override
    public void onBufferingEnd(PlayItem playItem) {

    }

    @Override
    public void onDownloadProgress(PlayItem playItem, long l, long l1) {

    }

    //设置歌曲名称
    private void sendTitle(String title) {

    }

    //通知封面图片
    private void sendImg(String img) {

    }

}