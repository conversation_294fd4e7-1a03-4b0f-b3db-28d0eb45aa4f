<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_home">

    <!--标题头-->
    <include
        android:id="@+id/pc_all_head"
        layout="@layout/pc_select_head"
        android:visibility="visible"
         />
     <View
         android:id="@+id/viewstub"
         android:layout_width="wrap_content"
         android:layout_height="1dp"
         android:background="@color/text_line_color_white"
         app:layout_constraintLeft_toLeftOf="parent"
         app:layout_constraintRight_toRightOf="parent"
         app:layout_constraintBottom_toBottomOf="@id/pc_all_head"
         ></View>

    <!--测试专用-->
    <include
        android:id="@+id/pc_yikatong"
        layout="@layout/pc_yikatong_test"
        android:visibility="gone"
        />
    <!--选择性别-->
    <include
        layout="@layout/pc_selcet_sex"
        android:visibility="gone" />

    <!-- 选择年代 -->
    <include
        layout="@layout/pc_age_item"
        android:visibility="gone" />

    <!--选择年龄-->
    <include
        layout="@layout/pc_age_detail_item"
        android:visibility="gone" />

    <!--选择兴趣标签-->
    <include
        app:layout_constraintTop_toBottomOf="@id/pc_all_head"
        layout="@layout/family_fragment_content_recommend"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:visibility="gone"
        android:layout_height="0dp"
        android:layout_width="0dp"
        />

    <!--选择兴趣标签-->
    <include
        app:layout_constraintTop_toBottomOf="@id/pc_all_head"
        layout="@layout/family_fragment_content_recommend_port"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:visibility="gone"
        android:layout_height="0dp"
        android:layout_width="0dp"
        />


    <include
        android:id="@+id/home_no_network_rel"
        layout="@layout/home_no_network_rl"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"></include>

    <include
        android:id="@+id/pc_loading"
        layout="@layout/refresh_bottom"
        android:layout_height="0dp"
        android:layout_width="0dp"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>