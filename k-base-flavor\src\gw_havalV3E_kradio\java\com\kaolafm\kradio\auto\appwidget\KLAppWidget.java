package com.kaolafm.kradio.auto.appwidget;

import android.appwidget.AppWidgetManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;

import com.kaolafm.kradio.auto.appwidget.remoteviews.RemoteViewFactory;
import com.kaolafm.kradio.lib.utils.Constants;


/**
 * Implementation of App Widget functionality.
 */
public class KLAppWidget {
    private static final String TAG = "YunBydWidget";

    public static void onReceive(Context context, Intent intent, Class clazz) {
        Log.i(TAG, "onReceive----->" + intent.getAction());
        AppWidgetManager appWidgetManager = AppWidgetManager.getInstance(context);
        ComponentName componentName = new ComponentName(context, clazz);
        for (int appWidgetId : appWidgetManager.getAppWidgetIds(componentName)) {
            updateAppWidget(context, appWidgetManager, appWidgetId, Constants.APPEXIT_ACTION);
        }
    }

    public static void onEnabled(Context context, Class clazz) {
        Log.i(TAG, "onEnable ----- ");
        Intent intent = new Intent(context, clazz);
        intent.setAction(WidgetService.WIDGET_ACTION_REFRESH);
        context.startService(intent);
    }


    public static void onUpdate(Context context, AppWidgetManager appWidgetManager, int[] appWidgetIds) {
        Log.i(TAG, "onUpdate ----- appwidgetId.size appWidgetIds size = " + appWidgetIds.length + "-----appWidgetIds = " + appWidgetIds);
        int maxId = Integer.MIN_VALUE;
        for (int appWidgetId : appWidgetIds) {
            if (appWidgetId > maxId) {
                maxId = appWidgetId;
            }
        }
        updateAppWidget(context, appWidgetManager, maxId, "android.appwidget.action.APPWIDGET_UPDATE");
    }

    public static void onDisabled(Context context, Class clazz) {
        Log.i(TAG, "onDisabled ----- ");
        Intent intent = new Intent(context, clazz);
        intent.setAction(WidgetService.WIDGET_ACTION_REFRESH);
        context.startService(intent);
    }

    public static void onAppWidgetOptionsChanged(Context context, AppWidgetManager appWidgetManager, int appWidgetId, Bundle newOptions) {
//        Log.e("zsj", "onAppWidgetOptionsChanged: maxheight = " + newOptions.getInt(AppWidgetManager.OPTION_APPWIDGET_MAX_HEIGHT));
//        Log.e("zsj", "onAppWidgetOptionsChanged: minheight = " + newOptions.getInt(AppWidgetManager.OPTION_APPWIDGET_MIN_HEIGHT));
//        Log.e("zsj", "onAppWidgetOptionsChanged: maxwidth = " + newOptions.getInt(AppWidgetManager.OPTION_APPWIDGET_MAX_WIDTH));
//        Log.e("zsj", "onAppWidgetOptionsChanged: minwidth = " + newOptions.getInt(AppWidgetManager.OPTION_APPWIDGET_MIN_WIDTH));
        int maxHeight = newOptions.getInt(AppWidgetManager.OPTION_APPWIDGET_MAX_HEIGHT);
        int maxWidth = newOptions.getInt(AppWidgetManager.OPTION_APPWIDGET_MAX_WIDTH);
        Log.i(TAG, "onAppWidgetOptionsChanged------->maxHeight = " + maxHeight + "--maxWidth = " + maxWidth);
        if (maxWidth <= 580) {
            if (maxHeight <= 295) {
                //小widget one
                RemoteViewFactory.getInstance().getRemoteView(RemoteViewFactory.TYPE_ONE);
            } else {
                //大widget two
                RemoteViewFactory.getInstance().getRemoteView(RemoteViewFactory.TYPE_TWO);
            }
        } else {
            if (maxHeight <= 295) {
                //小widget three
                RemoteViewFactory.getInstance().getRemoteView(RemoteViewFactory.TYPE_THREE);
            } else {
                //大widget four
                RemoteViewFactory.getInstance().getRemoteView(RemoteViewFactory.TYPE_FOUR);
            }
        }
        updateAppWidget(context, appWidgetManager, appWidgetId, "android.appwidget.action.APPWIDGET_UPDATE");

    }

    private static void updateAppWidget(final Context pContext, AppWidgetManager appWidgetManager, final int appWidgetId, String action) {
        Log.i(TAG, "updateAppWidget ----- appWidgetId:" + appWidgetId);
//        PlayItem playItem = KLAutoPlayerManager.getInstance().getCurrentPlayItem();
//        if (Constants.APPEXIT_ACTION.equals(action) || playItem == null) {
//            RemoteViews views = new RemoteViews(pContext.getPackageName(), BYDWidgetService.getRemoteViewsLayoutId());
//            views.setTextViewText(R.id.widget_audio_name, "");
//            views.setTextViewText(R.id.widget_album_name, "");
//            views.setImageViewBitmap(R.id.widget_cover,null);
//            views.setImageViewBitmap(R.id.widget_blur_imageview,null);
//            views.setImageViewBitmap(R.id.widget_broadcast_label,null);
//            views.setImageViewResource(R.id.widget_play_or_pause, R.drawable.selector_widget_btn_pause);
//            views.setProgressBar(R.id.widget_progressBar,0,0,false);
//            views.setTextViewText(R.id.widget_duration, "");
//            views.setTextViewText(R.id.widget_cur_time,  "");
//            views.setTextViewText(R.id.widget_broadcast_label_textview,  "");
//            views.setImageViewResource(R.id.widget_collection,  R.drawable.selector_widget_btn_uncollection);
//            views.setOnClickPendingIntent(R.id.widget_playinfo_layout, PendingIntent.getActivity(MyApplication.mContext, 0, new Intent(MyApplication.mContext, HomeActivity.class), 0));
//            appWidgetManager.updateAppWidget(appWidgetId, views);
//        } else {
        Intent intent = new Intent(pContext, WidgetService.class);
//        if (Constants.APPEXIT_ACTION.equals(action) || playItem == null) {
//            intent.setAction(BYDWidgetService.WIDGET_ACTION_EXIT);
//        } else {
//        }
        intent.setAction(WidgetService.WIDGET_ACTION_REFRESH);
        intent.putExtra(AppWidgetManager.EXTRA_APPWIDGET_ID, appWidgetId);
//        pContext.startService(intent);
        startServiceCompat(pContext, intent);
    }

    private static void startServiceCompat(Context context, Intent intent) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.startForegroundService(intent);
        } else {
            context.startService(intent);
        }
    }

}

