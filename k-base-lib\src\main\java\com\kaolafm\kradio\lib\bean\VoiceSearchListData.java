package com.kaolafm.kradio.lib.bean;

import android.os.Parcel;
import android.os.Parcelable;

import com.kaolafm.kradio.lib.utils.Constants;

import java.util.ArrayList;


/******************************************
 * 类描述： 语音搜索结果对象 类名称：VoiceSearchListData
 *
 * @version: 1.0
 * @author: shaoningYang
 * @time: 2016-8-9 18:12
 ******************************************/
public class VoiceSearchListData implements Parcelable {
    public VoiceSearchListData() {
    }

    private VoiceSearchListData(Parcel in) {
        this.haveNext = in.readInt();
        this.nextPage = in.readInt();
        this.havePre = in.readInt();
        this.prePage = in.readInt();
        in.readTypedList(dataList, VoiceSearchData.CREATOR);
    }

    private int haveNext;
    private int nextPage;
    private int havePre;
    private int prePage;
    private ArrayList<VoiceSearchData> dataList = new ArrayList<>();

    public int getHaveNext() {
        return haveNext;
    }

    public void setHaveNext(int haveNext) {
        this.haveNext = haveNext;
    }

    public int getNextPage() {
        return nextPage;
    }

    public void setNextPage(int nextPage) {
        this.nextPage = nextPage;
    }

    public int getHavePre() {
        return havePre;
    }

    public void setHavePre(int havePre) {
        this.havePre = havePre;
    }

    public int getPrePage() {
        return prePage;
    }

    public void setPrePage(int prePage) {
        this.prePage = prePage;
    }

    public ArrayList<VoiceSearchData> getDataList() {
        return dataList;
    }

    public void setDataList(ArrayList<VoiceSearchData> dataList) {
        this.dataList = dataList;
    }

    public boolean hasNextPage() {
        boolean flag = haveNext == Constants.HAVE_PAGE;
        return flag;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(haveNext);
        dest.writeInt(nextPage);
        dest.writeInt(havePre);
        dest.writeInt(prePage);
        dest.writeTypedList(dataList);
    }

    public static final Creator<VoiceSearchListData> CREATOR = new Creator<VoiceSearchListData>() {
        public VoiceSearchListData createFromParcel(Parcel source) {
            return new VoiceSearchListData(source);
        }

        public VoiceSearchListData[] newArray(int size) {
            return new VoiceSearchListData[size];
        }
    };
}
