package com.kaolafm.kradio.user.channel;

import android.view.View;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.mvp.IPresenter;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;

/**
 * <pre>
 *     <AUTHOR> Wenchl
 *     e-mail : <EMAIL>
 *     time   : 2019/02/18
 *     desc   :
 *     version: 1.0
 * </pre>
 */

public class InterfaceSettingFragment extends BaseFragment {
    @Override
    protected int getLayoutId() {
        return R.layout.user_fragment_interface_setting;
    }

    @Override
    protected int getLayoutId_Tow() {
        return 0;
    }

    @Override
    protected IPresenter createPresenter() {
        return null;
    }

    @Override
    public void initView(View view) {

    }

}
