package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.Fragment;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.view.Gravity;

import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.flavor.common.SystemBootUtil;
import com.kaolafm.kradio.lib.base.flavor.KRadioLoginToastInter;
import com.kaolafm.kradio.lib.dialog.Dialogs;
import com.kaolafm.kradio.lib.utils.ResUtil;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-08-31 16:53
 ******************************************/
public class KRadioLoginToastImpl implements KRadioLoginToastInter {
    private static final int JUMP_TIME_DURATION = 3000;

    @Override
    public boolean showLoginToast(Object... args) {
        final Fragment fragment = (Fragment) args[0];
        Context context = fragment.getActivity();
        SystemBootUtil systemBootUtil = new SystemBootUtil();
        boolean flag = systemBootUtil.isFirstBootLogin(context);
        if (flag) {
            systemBootUtil.updateFirstBootLogin(context, false);
            if (!UserInfoManager.getInstance().isUserLogin()) {
                String content = ResUtil.getString(R.string.login_jump_warning_str);
                int index = content.lastIndexOf('\n');
                SpannableString spannableString = new SpannableString(content);

                int len = spannableString.length();
                spannableString.setSpan(new AbsoluteSizeSpan(ResUtil.getDimen(R.dimen.text_size5)), 0, index, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
                spannableString.setSpan(new ForegroundColorSpan(ResUtil.getColor(R.color.colorWhite)), 0, index, Spannable.SPAN_INCLUSIVE_INCLUSIVE);

                spannableString.setSpan(new AbsoluteSizeSpan(ResUtil.getDimen(R.dimen.text_size1)), index, len, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
                spannableString.setSpan(new ForegroundColorSpan(ResUtil.getColor(R.color.gray_c1_color)), index, len, Spannable.SPAN_INCLUSIVE_INCLUSIVE);

                DialogFragment dialogFragment = new Dialogs.Builder()
                        .setType(Dialogs.TYPE_2BTN)
                        .setCanShowButton(false)
                        .setGravity(Gravity.CENTER)
                        .setMessage(spannableString)
                        .create();
                dialogFragment.show(fragment.getFragmentManager(), "goToLogin");

                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            dialogFragment.dismiss();
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        gotoLoginPage(fragment.getActivity());
                    }
                }, JUMP_TIME_DURATION);
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean showLoginView(Object... args) {
        return false;
    }

    private void gotoLoginPage(Activity activity) {
        Intent intent = new Intent();
        ComponentName component = new ComponentName(
                "com.aerozhonghuan.changchun_largescreen", // 解放行包名
                "com.aerozhonghuan.changchun_largescreen.LargeScreenActivity"); // 解放行要跳转到的页面
        intent.setComponent(component);
        intent.putExtra("key", "login");
        try {
            activity.startActivity(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
