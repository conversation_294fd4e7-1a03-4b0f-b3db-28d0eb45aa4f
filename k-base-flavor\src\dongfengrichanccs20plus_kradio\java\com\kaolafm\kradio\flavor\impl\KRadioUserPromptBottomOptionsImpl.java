package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.base.flavor.KRadioUserPromptBottomOptionsInter;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.SharedPreferenceUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.utils.MyDebouncingOnClickListener;

import static com.kaolafm.kradio.lib.utils.Constants.USER_PROMPT_FIRST_SP_FILE_NAME;
import static com.kaolafm.kradio.lib.utils.Constants.USER_PROMPT_FIRST_SP_ITEM_VALUE;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-10-26 12:14
 ******************************************/
public class KRadioUserPromptBottomOptionsImpl implements KRadioUserPromptBottomOptionsInter {
    private ImageView userPromptAgreeImage;
    private TextView userPromptAgreeUse;
    private TextView userPromptAgreeText;
    private TextView mUserPromptDisAgreeText;
    private boolean isUserAgree = false;

    @Override
    public boolean doBottomOptions(Object... args) {
        View view = (View) args[0];
        UserPromptFragment.IUserAgreePrompt iUserAgreePrompt = (UserPromptFragment.IUserAgreePrompt) args[1];
        userPromptAgreeImage = view.findViewById(R.id.user_prompt_agree_image);
        userPromptAgreeImage.setOnClickListener(v -> {
            int id = v.getId();
            if (!AntiShake.check(id)) {
                isUserAgree = !isUserAgree;
                updateView();
            }
        });
        userPromptAgreeText = view.findViewById(R.id.user_prompt_agree_text);
        userPromptAgreeText.setOnClickListener(v -> {
            int id = v.getId();
            if (!AntiShake.check(id)) {
                isUserAgree = !isUserAgree;
                updateView();
            }
        });
        userPromptAgreeUse = view.findViewById(R.id.user_prompt_agree_use);
        userPromptAgreeUse.setActivated(true);
        userPromptAgreeUse.setOnClickListener(v -> {
            int id = v.getId();
            if (!AntiShake.check(id)) {
                ViewUtil.setViewVisibility(view, View.GONE);
                if (iUserAgreePrompt != null) {
                    iUserAgreePrompt.agree();
                }
            }
        });
        mUserPromptDisAgreeText = view.findViewById(R.id.user_prompt_disagree_use);
        mUserPromptDisAgreeText.setOnClickListener(new MyDebouncingOnClickListener() {
            @Override
            public void doClick(View v) {
                AppManager.getInstance().appExit();
            }
        });
        return true;
    }

    private void updateView() {
        if (isUserAgree) {
            ViewUtil.setEnabled(mUserPromptDisAgreeText, false);
            userPromptAgreeImage.setImageResource(R.drawable.ic_user_prompt_agree_click);
            userPromptAgreeText.setTextColor(ResUtil.getColor(R.color.text_color_1));
            SharedPreferenceUtil sharedPreferenceUtil = SharedPreferenceUtil
                    .newInstance(AppDelegate.getInstance().getContext(), USER_PROMPT_FIRST_SP_FILE_NAME, Context.MODE_PRIVATE);
            sharedPreferenceUtil.putBoolean(USER_PROMPT_FIRST_SP_ITEM_VALUE, false);
        } else {
            userPromptAgreeImage.setImageResource(R.drawable.ic_user_prompt_agree);
            userPromptAgreeText.setTextColor(ResUtil.getColor(R.color.text_color_5));
            SharedPreferenceUtil sharedPreferenceUtil = SharedPreferenceUtil
                    .newInstance(AppDelegate.getInstance().getContext(), USER_PROMPT_FIRST_SP_FILE_NAME, Context.MODE_PRIVATE);
            sharedPreferenceUtil.putBoolean(USER_PROMPT_FIRST_SP_ITEM_VALUE, true);
            ViewUtil.setEnabled(mUserPromptDisAgreeText, true);
        }
    }
}
