package com.kaolafm.kradio.home.comprehensive.playerbar;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;

import com.kaolafm.kradio.lib.widget.viewpager.VerticalViewPager;


/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: NoScrollVerticalViewPager.java                                               
 *                                                                  *
 * Created in 2018/5/13 下午4:50                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
public class NoScrollVerticalViewPager extends VerticalViewPager {
    public NoScrollVerticalViewPager(Context context) {
        super(context);
    }

    public NoScrollVerticalViewPager(Context context, AttributeSet attrs) {
        super(context, attrs);
    }


    @Override
    public boolean onTouchEvent(MotionEvent arg0) {
        return false;
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent arg0) {
        return false;
    }
}
