package com.kaolafm.kradio.lib.dialog;

import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.content.res.Configuration;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.StyleRes;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.widget.FrameLayout;

import com.kaolafm.kradio.lib.R;
import com.kaolafm.kradio.lib.base.flavor.KRadioDialogAttrInter;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.MultiUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.PageShowReportEvent;
import com.kaolafm.report.util.ReportConstants;

import me.yokeyword.fragmentation.ExtraTransaction;
import me.yokeyword.fragmentation.ISupportFragment;
import me.yokeyword.fragmentation.SupportFragmentDelegate;
import me.yokeyword.fragmentation.anim.FragmentAnimator;

/**
 * <AUTHOR> Yan
 * @date 2018/5/20
 * 子类只有重写getContentView()即可.
 * 默认dialog在屏幕中央显示.
 */

public class BaseDialogFragment extends DialogFragment implements ISupportFragment {

    SupportFragmentDelegate mDelegate = new SupportFragmentDelegate(this);

    private FragmentActivity mActivity;

    //左右边距
    private int margin;

    //宽度
    private int width = 0;

    //高度
    private int height = ScreenUtil.getScreenHeight() / 2;

    //灰度深浅
    private float dimAmount = 0.5f;
    private boolean isCanShowing;

    //是否点击外部取消
    private boolean outCancel = true;
    protected long startTime = -1;
    private int background = -1;

    @StyleRes
    private int animStyle;
    private int gravity;
    private DialogListener.OnClickListener mOutsideClickListener;

    private KRadioDialogAttrInter mKRadioDialogAttrInter;


    public void setMargin(int margin) {
        this.margin = margin;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public void setDimAmount(float dimAmount) {
        this.dimAmount = dimAmount;
    }

    public void setAnimStyle(int animStyle) {
        this.animStyle = animStyle;
    }

    public void setGravity(int gravity) {
        this.gravity = gravity;
    }

    public void setOutCancel(boolean outCancel) {
        this.outCancel = outCancel;
    }

    public void setBackground(int background) {
        this.background = background;
    }

    public int getBackground() {
        return background;
    }

    /**
     * 页面曝光事件上报
     */
    private void reportPageShowEvent() {
        long duration = System.currentTimeMillis() - startTime;
        if (startTime < 0 || duration < 300) {
            return;
        }

        PageShowReportEvent event = new PageShowReportEvent();
        event.setPageId(getPageId());
        event.setPageTime(String.valueOf(duration));
        ReportHelper.getInstance().addEvent(event);

        reportDialogExposureEvent(duration);
        Log.i("BaseDialogFragment", "report=" + duration);
        startTime = -1;
    }

    protected void reportDialogExposureEvent(long duration) {

    }

    /**
     * 获取pageid
     *
     * @return
     */
    public String getPageId() {
        return Constants.BLANK_STR;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        FrameLayout view = (FrameLayout) inflater.inflate(R.layout.dialog_fragment_content, container, false);
        if (background != -1) {
            //如果设置了background，则应将view全屏，使用设置的background
            setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
            setHeight(ViewGroup.LayoutParams.MATCH_PARENT);
            view.setBackgroundColor(background);
        }
        View contentView = getContentView();
        if (contentView != null) {
            view.addView(contentView);
        }

        view.findViewById(R.id.cd_close).setOnClickListener((v)->dismiss());

        showAccordingToScreen(ResUtil.getOrientation());
        view.setOnClickListener(v -> {
            if (mOutsideClickListener != null) {
                mOutsideClickListener.onClick(getDialog(), 0);
            }
            if (outCancel) {
                dismiss();
            }
        });
        return view;
    }

    protected View getContentView() {
        return null;
    }

    @Override
    public SupportFragmentDelegate getSupportDelegate() {
        return mDelegate;
    }

    @Override
    public ExtraTransaction extraTransaction() {
        return mDelegate.extraTransaction();
    }

    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);

    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        mDelegate.onAttach(getActivity());
        mActivity = mDelegate.getActivity();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mKRadioDialogAttrInter = ClazzImplUtil.getInter("KRadioDialogAttrImpl");
        setStyle(DialogFragment.STYLE_NO_TITLE, R.style.BaseDialogTheme);
        mDelegate.onCreate(savedInstanceState);
    }

    @Override
    public Animation onCreateAnimation(int transit, boolean enter, int nextAnim) {
        return mDelegate.onCreateAnimation(transit, enter, nextAnim);
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        mDelegate.onActivityCreated(savedInstanceState);
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        mDelegate.onSaveInstanceState(outState);
    }

    @Override
    public void onStart() {
        boolean flag = mKRadioDialogAttrInter != null;
        if (flag) {
            // 解决https://app.huoban.com/tables/2100000007530121/items/2300001494632638?userId=1229522问题
            mKRadioDialogAttrInter.beforeDialogShow(getDialog());
        }
        super.onStart();
        initParams();
        if (flag) {
            mKRadioDialogAttrInter.afterDialogShow(getDialog());
        }
    }

    private void initParams() {
        setupWindowDimAmount();
        setupWindowGravity();
        setupWindowAnim();
        setupWindowSize();
        setCancelable(outCancel);
    }

    private void setupWindowDimAmount() {
        Window window = getDialog().getWindow();
        if (window != null) {
            WindowManager.LayoutParams lp = window.getAttributes();
            //调节灰色背景透明度[0-1]，默认0.5f
            lp.dimAmount = dimAmount;
        }
    }

    private void setupWindowGravity() {
        Window window = getDialog().getWindow();
        if (window != null) {
            WindowManager.LayoutParams lp = window.getAttributes();
            //是否在底部显示
            lp.gravity = gravity;// Gravity.BOTTOM;
        }
    }

    private void setupWindowAnim() {
        Window window = getDialog().getWindow();
        if (window != null) {
            if (animStyle == 0) {
                animStyle = R.style.DefaultAnimation;
            }
            //设置dialog进入、退出的动画
            window.setWindowAnimations(animStyle);
        }
    }

    /**
     * 设置window大小,会影响content view的显示,所以去掉;
     */
    private void setupWindowSize() {
        Window window = getDialog().getWindow();
        if (window != null) {
            WindowManager.LayoutParams lp = window.getAttributes();
            //设置dialog宽度
            //必须减去状态栏的高度,否则,dialog弹出时,状态栏会出现;
            if (MultiUtil.isInMultiWindowMode && ResUtil.getOrientation() == Configuration.ORIENTATION_PORTRAIT && height > 0) {
                lp.height = height / 2;
            } else {
                lp.height = height;// ScreenUtil.getScreenHeightWithoutStatus();
            }
            lp.width = width;// WindowManager.LayoutParams.MATCH_PARENT;
            window.setAttributes(lp);
        }
    }

    @Override
    public void onMultiWindowModeChanged(boolean isInMultiWindowMode) {
        super.onMultiWindowModeChanged(isInMultiWindowMode);
        setupWindowSize();
    }

    @Override
    public void onResume() {
        super.onResume();
        mDelegate.onResume();
        String pageId = getPageId();
        if (!StringUtil.isEmpty(pageId)) {
            Log.i(ReportConstants.REPORT_TAG, "设置pageid= " + pageId);
            ReportHelper.getInstance().setPage(pageId);
        }
        isCanShowing = true;
        startTime = System.currentTimeMillis();
    }


    @Override
    public void onPause() {
        super.onPause();
        mDelegate.onPause();
        if (isCanShowing) {
            reportPageShowEvent();
        }
    }

    @Override
    public void onDestroyView() {
        mDelegate.onDestroyView();
        super.onDestroyView();
    }

    @Override
    public void onDestroy() {
        mDelegate.onDestroy();
        super.onDestroy();
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        mDelegate.onHiddenChanged(hidden);
        isCanShowing = !hidden;
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        mDelegate.setUserVisibleHint(isVisibleToUser);
        isCanShowing = isVisibleToUser;
        onVisibleChanged(isVisibleToUser);
    }

    protected void onVisibleChanged(boolean isVisible) {
        if (!isVisible) {
            reportPageShowEvent();
        } else {
            startTime = System.currentTimeMillis();
        }
    }

    @Override
    public void enqueueAction(Runnable runnable) {
        mDelegate.post(runnable);
    }

    @Override
    public void post(Runnable runnable) {
        mDelegate.post(runnable);
    }

    @Override
    public void onEnterAnimationEnd(@Nullable Bundle savedInstanceState) {
        mDelegate.onEnterAnimationEnd(savedInstanceState);
    }

    @Override
    public void onLazyInitView(@Nullable Bundle savedInstanceState) {
        mDelegate.onLazyInitView(savedInstanceState);
    }

    @Override
    public void onSupportVisible() {
        mDelegate.onSupportVisible();
    }

    @Override
    public void onSupportInvisible() {
        mDelegate.onSupportInvisible();
    }

    @Override
    public boolean isSupportVisible() {
        return mDelegate.isSupportVisible();
    }

    @Override
    public FragmentAnimator onCreateFragmentAnimator() {
        return mDelegate.onCreateFragmentAnimator();
    }

    @Override
    public FragmentAnimator getFragmentAnimator() {
        return mDelegate.getFragmentAnimator();
    }

    @Override
    public void setFragmentAnimator(FragmentAnimator fragmentAnimator) {
        mDelegate.setFragmentAnimator(fragmentAnimator);
    }

    @Override
    public void setFragmentResult(int resultCode, Bundle bundle) {
        mDelegate.setFragmentResult(resultCode, bundle);
    }

    @Override
    public void onFragmentResult(int requestCode, int resultCode, Bundle data) {
        mDelegate.onFragmentResult(requestCode, resultCode, data);
    }

    @Override
    public void onNewBundle(Bundle args) {
        mDelegate.onNewBundle(args);
    }

    @Override
    public void putNewBundle(Bundle newBundle) {
        mDelegate.putNewBundle(newBundle);
    }

    @Override
    public boolean onBackPressedSupport() {
        return mDelegate.onBackPressedSupport();
    }

    @Override
    public void onCancel(DialogInterface dialog) {
        super.onCancel(dialog);
        if (mOutsideClickListener != null) {
            mOutsideClickListener.onClick(getDialog(), 0);
        }
    }

    /**
     * 加载根Fragment, 即Activity内的第一个Fragment 或 Fragment内的第一个子Fragment
     *
     * @param containerId 容器id
     * @param toFragment  目标Fragment
     */
    public void loadRootFragment(int containerId, ISupportFragment toFragment) {
        mDelegate.loadRootFragment(containerId, toFragment);
    }

    public void loadRootFragment(Fragment toFragment) {
        getFragmentManager().beginTransaction()
                .addToBackStack(toFragment.getTag())
                .replace(R.id.fl_dialog_fragment_content, toFragment)
                .commit();
    }


    public void loadRootFragment(int containerId, ISupportFragment toFragment, boolean addToBackStack,
                                 boolean allowAnim) {
        mDelegate.loadRootFragment(containerId, toFragment, addToBackStack, allowAnim);
    }

    public void replace(ISupportFragment toFragment) {
        mDelegate.replaceFragment(toFragment, true);
    }

    public void replace(ISupportFragment toFragment, boolean addToBackStack) {
        mDelegate.replaceFragment(toFragment, addToBackStack);
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        showAccordingToScreen(ResUtil.getOrientation());
    }

    /**
     * @param orientation Configuration.ORIENTATION_LANDSCAPE(横屏) or Configuration.ORIENTATION_PORTRAIT(竖屏)
     */
    protected void showAccordingToScreen(int orientation) {
        setupWindowSize();
    }

    /**
     * 点击外部时的事件
     *
     * @param onOutsideClickListener
     */
    public void setOnOutsideClickListener(DialogListener.OnClickListener onOutsideClickListener) {
        this.mOutsideClickListener = onOutsideClickListener;
    }
}
