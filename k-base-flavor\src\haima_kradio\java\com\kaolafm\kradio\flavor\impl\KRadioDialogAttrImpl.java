package com.kaolafm.kradio.flavor.impl;

import android.app.Dialog;
import android.os.Build;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import com.kaolafm.kradio.lib.base.flavor.KRadioDialogAttrInter;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-01-13 17:08
 ******************************************/
public class KRadioDialogAttrImpl implements KRadioDialogAttrInter {
    @Override
    public boolean beforeDialogShow(Object... args) {
        Dialog dialog = (Dialog) args[0];
        if (dialog == null) {
            return false;
        }
        Window window = dialog.getWindow();
        if (window == null) {
            return false;
        }
        window.setFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);
        return true;
    }

    @Override
    public boolean afterDialogShow(Object... args) {
        Dialog dialog = (Dialog) args[0];
        if (dialog == null) {
            return false;
        }
        Window window = dialog.getWindow();
        if (window == null) {
            return false;
        }

        window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_HIDE_NAVIGATION);
        window.getDecorView().setOnSystemUiVisibilityChangeListener(new View.OnSystemUiVisibilityChangeListener() {
            @Override
            public void onSystemUiVisibilityChange(int visibility) {
                int uiOptions = View.SYSTEM_UI_FLAG_LAYOUT_STABLE |
                        //布局位于状态栏下方
                        View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION |
                        //全屏
                        View.SYSTEM_UI_FLAG_FULLSCREEN |
                        //隐藏导航栏
                        View.SYSTEM_UI_FLAG_HIDE_NAVIGATION |
                        View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN;
                if (Build.VERSION.SDK_INT >= 19) {
                    uiOptions |= View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY;
                } else {
                    uiOptions |= View.SYSTEM_UI_FLAG_LOW_PROFILE;
                }
                window.getDecorView().setSystemUiVisibility(uiOptions);
            }
        });

        window.clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);
        return true;
    }
}