package com.kaolafm.kradio.flavor.recorder;

import android.util.Log;

import com.hsae.hsaerecord.HsaeRecord;
import com.kaolafm.kradio.lib.base.flavor.live.KradioRecorderInterface;
import com.kaolafm.kradio.lib.utils.recorder.PcmToAacUtil;
import com.kaolafm.kradio.live1.player.LiveManager;

import java.io.BufferedOutputStream;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * <pre>
 *     <AUTHOR> Wenchl
 *     e-mail : <EMAIL>
 *     time   : 2019/04/28
 *     desc   :
 *     version: 1.0
 * </pre>
 */
public class QichenRecorder implements HsaeRecord.HsaeRecordListener, KradioRecorderInterface {

    private HsaeRecord hsaeRecord;

    private DataOutputStream dos;

    private static final String TAG = "KradioQichenRecorder";

    private static final String FILE_PCM = "Live-Leave-message-qichen.aac";

    private PcmToAacUtil pcmToAacUtil;

    public QichenRecorder(){
        hsaeRecord = HsaeRecord.getInstance(this);
    }

    private File createNewAudioFile() {
        if (!LiveManager.RECORDINGS_DIR.exists()) {
            LiveManager.RECORDINGS_DIR.mkdirs();
        }
        File file = new File(LiveManager.RECORDINGS_DIR, FILE_PCM);
        return file;
    }


    @Override
    public void onRecordData(byte[] bytes, int i) {
        if (dos != null) {
            try {
                byte[] b = pcmToAacUtil.offerEncoder(bytes);
                Log.i(TAG, "onRecordData length " + b.length);
                if (b.length > 0) {
                    dos.write(b);
                }
            } catch (IOException e) {
                e.printStackTrace();
                Log.i(TAG, "onRecordData error " + e.getMessage());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void startRecord() {
        File file = createNewAudioFile();

        if (file.exists()) {
            file.delete();
        }

        Log.i(TAG, "startRecord");

        try {
            pcmToAacUtil = new PcmToAacUtil(44100, 1);
            dos = new DataOutputStream(
                    new BufferedOutputStream(
                            new FileOutputStream(file, false)));
            hsaeRecord.startRecording();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            Log.i(TAG, "startRecord error:"+e.getMessage());
        } catch (IllegalStateException e) {
            Log.i(TAG, "startRecord error:"+e.getMessage());
        }

    }

    @Override
    public boolean stopRecord() {
        Log.i(TAG, "stopRecord");
        try {
            hsaeRecord.stop();
            if (pcmToAacUtil != null) {
                pcmToAacUtil.close();
            }
            if (dos != null) {
                dos.flush();
                dos.close();// 关闭写入流
            }
        } catch (IOException e) {
            Log.i(TAG, "stopRecord error:"+e.getMessage());
            return false;
        } catch (IllegalStateException e) {
            Log.i(TAG, "stopRecord error:"+e.getMessage());
            return false;
        }
        return true;
    }

    @Override
    public String getFilePath() {
        return LiveManager.RECORDINGS_DIR.getAbsolutePath()+"/"+FILE_PCM;
    }


}
