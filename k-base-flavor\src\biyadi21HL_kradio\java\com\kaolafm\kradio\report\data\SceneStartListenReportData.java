package com.kaolafm.kradio.report.data;

import com.kaolafm.report.event.StartListenReportEvent;

import java.util.UUID;

import static com.kaolafm.kradio.uitl.Constants.RADIO_PLAY_START_EVENT_CODE;
import static com.kaolafm.kradio.uitl.Constants.SCENE_REMARK_VALUE;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-03-04 20:14
 ******************************************/
public class SceneStartListenReportData extends StartListenReportEvent {

    public SceneStartListenReportData(StartListenReportEvent startListenReportEvent) {
        super();
        setEventcode(RADIO_PLAY_START_EVENT_CODE);
        setRemarks2(SCENE_REMARK_VALUE);

        setAudioid(startListenReportEvent.getAudioid());
        setRadioid(startListenReportEvent.getRadioid());
        setAlbumid(startListenReportEvent.getAlbumid());
        setType(startListenReportEvent.getType());
        setPosition(startListenReportEvent.getPosition());
        setRemarks1(startListenReportEvent.getRemarks1());
        setRemarks4(startListenReportEvent.getRemarks4());
        setRemarks9(startListenReportEvent.getRemarks9());
        setAi_mz_location(startListenReportEvent.getAi_mz_location());
        setRemarks6(startListenReportEvent.getRemarks6());
        setSource(startListenReportEvent.getSource());
        setRemarks11(startListenReportEvent.getRemarks11());
    }

    /**
     * 场景推送session
     */
    private String cjsession = UUID.randomUUID().toString();

    /**
     * 场景内容code
     */
    private String cjcode;

    /**
     * 运营类型
     */
    private String cjtype;

    public String getCjsession() {
        return cjsession;
    }

    public void setCjsession(String cjsession) {
        this.cjsession = cjsession;
    }

    public String getCjcode() {
        return cjcode;
    }

    public void setCjcode(String cjcode) {
        this.cjcode = cjcode;
    }

    public String getCjtype() {
        return cjtype;
    }

    public void setCjtype(String cjtype) {
        this.cjtype = cjtype;
    }
}
