package com.kaolafm.kradio.history.comprehensive.ui;


import android.os.Build;
import android.util.Log;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.lib.bean.HistoryItem;
import com.kaolafm.kradio.component.ComponentClient;
import com.kaolafm.kradio.component.DynamicComponent;
import com.kaolafm.kradio.component.MainThreadable;
import com.kaolafm.kradio.component.RealCaller;
import com.kaolafm.kradio.constant.LoginProcessorConst;
import com.kaolafm.kradio.constant.UserComponentConst;
import com.kaolafm.kradio.constant.UserStateObserverProcessorConst;
import com.kaolafm.kradio.lib.bean.HeadTitleItem;
import com.kaolafm.kradio.history.mvp.HistoryModel;
import com.kaolafm.kradio.history.mvp.IHistoryView;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioHistoryInter;
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.ComponentUtil;
import com.kaolafm.kradio.lib.utils.NetworkManager;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.opensdk.api.personalise.model.HotRecommend;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by kaolafm on 2018/5/2.
 */

public class HistoryPresent extends BasePresenter<HistoryModel, IHistoryView> {

    private DynamicComponent mHistoryUserObserver;

    private boolean mLogin;

    NetworkManager.INetworkReady mNetworkReady = hasNetwork -> {
        if (hasNetwork) {
            getHistoryList();
        }
    };

    public HistoryPresent(IHistoryView view) {
        super(view);
        mHistoryUserObserver = new HistoryUserObserver();
        ComponentUtil.addObserver(UserComponentConst.NAME, mHistoryUserObserver);
    }

    @Override
    public void start() {
        super.start();
        getHotRecommend();
        try {
            mLogin = ComponentUtil.getResultValue(UserComponentConst.NAME, UserComponentConst.IS_USER_BOUND);
        } catch (Exception e) {
            mLogin = false;
        }

        // 注册网络连接监听
        NetworkManager.getInstance().addNetworkReadyListener(mNetworkReady);
        mModel.setListener(aBoolean -> updateList());
    }

    @Override
    public void destroy() {
        ComponentUtil.removeObserver(UserComponentConst.NAME, mHistoryUserObserver);
        mModel.setListener(null);
        super.destroy();
        NetworkManager.getInstance().removeNetworkReadyListener(mNetworkReady);
    }

    @Override
    protected HistoryModel createModel() {
        return new HistoryModel();
    }
    /**
     * 请求热门推荐内容
     */
    public void getHotRecommend() {
        mModel.getHotRecommend(new HttpCallback<HotRecommend>() {
            @Override
            public void onSuccess(HotRecommend hotRecommend) {
                if (mView != null) {
                    mView.showHotRecommend(hotRecommend);
                }
            }

            @Override
            public void onError(ApiException e) {

            }
        });
    }

    private void loginStatusChanged() {
        if (mView != null) {
            mView.loginStatusChanged();
        }
    }

    /**
     * 在无网或者未登录的情况下，加载本地历史。
     * 否则加载用户历史。
     */
    public void getHistoryList() {
        //逻辑移动到下面 统一处理目前有两次调用但是逻辑不一致
//        mLogin = ComponentUtil.getResultValue(UserComponentConst.NAME, UserComponentConst.IS_USER_BOUND);
//        if (mLogin && !NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
//            if (mView != null) {
//                mView.showButton(false);
//                mView.showError(ResUtil.getString(R.string.home_network_nosigin), false);
//            }
//            return;
//        }
//        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
//            if (mView != null) {
//                mView.showError(ResUtil.getString(R.string.home_network_nosigin), false);
//            }
//            return;
//        }
        if (mView != null) {
            mView.prepareFragmentStateForShowLoading();
            mView.showLoading();
        }
        updateList();
    }

    /**
     * 从数据库按照时间戳重新拉取数据
     */
    private void updateList() {
        if (null == mModel) {
            return;
        }
        try {
            mLogin = ComponentUtil.getResultValue(UserComponentConst.NAME, UserComponentConst.IS_USER_BOUND);
        } catch (Exception e) {
            mLogin = false;
        }
        if (mLogin && !NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
            if (mView != null) {
                mView.finishRefresh(false);
                mView.hideLoading();
                mView.showButton(false);
                mView.showError(ResUtil.getString(R.string.network_nosigin), false);
            }
            return;
        }

        mModel.getHistoryList(mLogin, new HttpCallback<List<HistoryItem>>() {
            @Override
            public void onSuccess(List<HistoryItem> historyList) {
                if (mView != null) {
                    mView.finishRefresh(true);
                    mView.hideLoading();
                    showHistory(historyList);
                }
            }

            @Override
            public void onError(ApiException e) {
                Log.e("HistoryPresent", "onError: " + e);
                if (mView != null) {
                    mView.finishRefresh(false);
                    mView.hideLoading();
                    if (mLogin && !NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
                        if (mView != null) {
                            mView.hideLoading();
                            mView.showButton(false);
                            mView.showError(ResUtil.getString(R.string.network_nosigin), false);
                        }
                    } else if (mLogin) {
                        mView.showEmpty();
                    }
                }
            }
        });
    }


    private void showHistory(List<HistoryItem> historyList) {
        if (historyList == null) {
            historyList = new ArrayList<>();
        } else {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                historyList = historyList.stream().filter(historyItem ->
                        !historyItem.getType().equals(String.valueOf(PlayerConstants.RESOURCES_TYPE_VIDEO_ALBUM))&&
                                !historyItem.getType().equals(String.valueOf(PlayerConstants.RESOURCES_TYPE_VIDEO_AUDIO))).collect(Collectors.toList());
            } else {
                List<HistoryItem> result = new ArrayList<HistoryItem>();
                for (HistoryItem historyItem: historyList) {
                    if (!historyItem.getType().equals(String.valueOf(PlayerConstants.RESOURCES_TYPE_VIDEO_ALBUM)) &&
                            !historyItem.getType().equals(String.valueOf(PlayerConstants.RESOURCES_TYPE_VIDEO_AUDIO))) {
                        result.add(historyItem);
                    }
                }
                historyList = result;
            }
        }
        int size = historyList.size();
        boolean nonEmpty = size > 0;
        if (!mLogin) {
            //todo 迁移到byd渠道代码
            HeadTitleItem head = new HeadTitleItem();
            head.setTypeId(HistoryAdapter.HEAD_UNLOGIN_TIP);
//            head.setItemHeight(nonEmpty ? LayoutParams.WRAP_CONTENT : LayoutParams.MATCH_PARENT);
            historyList.add(0, head);
        }
        if (nonEmpty) {
            //todo 迁移到byd渠道代码
            HeadTitleItem title = new HeadTitleItem();
            title.setCount(size);
            title.setTypeId(HistoryAdapter.HISTORY_COUNT_TITLE);
            historyList.add(mLogin ? 0 : 1, title);
            mView.showButton(true);
        } else {
            mView.showButton(false);
        }

        if (!ListUtil.isEmpty(historyList)) {
            mView.showHistory(historyList);
        } else {
            //登录情况下且没有历史才显示空页面
            if (mLogin) {
                mView.showEmpty();
            } else {
                KRadioHistoryInter kRadioHistoryInter = ClazzImplUtil
                        .getInter("KRadioHistoryImpl");
                if (kRadioHistoryInter != null) {
                    kRadioHistoryInter.doNoHistoryData(mView);
                } else {
                    mView.showEmpty();
                }
            }
        }
    }

    /**
     * 清空历史
     */
    public void clearHistory() {
        mModel.clearHistory(new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                if (mView != null) {
                    showHistory(null);
                }
            }

            @Override
            public void onError(ApiException e) {

            }
        });
    }

    private class HistoryUserObserver implements DynamicComponent, MainThreadable {

        @Override
        public String getName() {
            return "HistoryPresent-UserObserver";
        }

        @Override
        public boolean onCall(RealCaller caller) {
            String actionName = caller.actionName();
            switch (actionName) {
                case UserStateObserverProcessorConst.USER_LOGIN:
                case UserStateObserverProcessorConst.USER_LOGOUT:
                    loginStatusChanged();
                    getHistoryList();
                    break;
                case LoginProcessorConst.BACKTYPE_SWITCH:
                    Log.e("HistoryUserObserver", "onCall: BACKTYPE_SWITCH");
                    break;
                default:
                    break;
            }
            return false;
        }

        @Override
        public Boolean shouldActionRunOnMainThread(String actionName, ComponentClient caller) {
            return true;
        }
    }
}
