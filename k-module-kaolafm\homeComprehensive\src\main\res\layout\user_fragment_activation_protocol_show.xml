<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_home"
    >

    <!--<include layout="@layout/user_head_view" />-->

    <ImageView
        android:id="@+id/activation_protocol_back"
        android:layout_width="@dimen/m80"
        android:layout_height="@dimen/m80"
        android:layout_marginLeft="@dimen/m40"
        android:layout_marginTop="@dimen/m40"
        android:padding="@dimen/m25"
        android:src="@drawable/globle_arrow_normal"
        android:background="@drawable/color_main_button_click_selector" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="@dimen/m80"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/m40"
        android:gravity="center"
        android:text="@string/activation_protocol_title_str"
        android:textColor="@color/global_title_text_color"
        android:textSize="@dimen/m32" />

    <WebView
        android:id="@+id/activation_protocol_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/activation_protocol_back"
        android:layout_marginLeft="@dimen/m40"
        android:layout_marginRight="@dimen/m40"
        android:layout_marginBottom="@dimen/m40"
        android:layerType="software" />
</RelativeLayout>