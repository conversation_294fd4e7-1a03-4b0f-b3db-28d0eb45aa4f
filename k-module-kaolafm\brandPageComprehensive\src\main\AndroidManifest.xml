<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.kaolafm.kradio.brand.comprehensive">

    <application>
        <activity
            android:name="com.kaolafm.kradio.brand.comprehensive.ComprehensiveBrandPageActivity"
            android:configChanges="orientation|screenSize|locale|layoutDirection|keyboard|screenLayout|uiMode"
            android:launchMode="singleTop"
            android:screenOrientation="landscape"
            android:theme="@style/ComprehensiveAppThemeCompat"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".topic.ComprehensiveTopicDetailActivity"
            android:configChanges="orientation|screenSize|locale|layoutDirection|keyboard|screenLayout|uiMode"
            android:launchMode="singleTop"
            android:screenOrientation="landscape"
            android:theme="@style/ComprehensiveAppThemeCompat"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".topic.ComprehensivePostsInputActivity"
            android:configChanges="orientation|screenSize|locale|layoutDirection|keyboard|screenLayout|uiMode"
            android:launchMode="singleTop"
            android:screenOrientation="landscape"
            android:theme="@style/ComprehensiveAppThemeCompatTranslucent"
            android:windowSoftInputMode="adjustPan" />
    </application>
</manifest>
