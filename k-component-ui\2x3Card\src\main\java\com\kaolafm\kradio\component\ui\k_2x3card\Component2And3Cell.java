package com.kaolafm.kradio.component.ui.k_2x3card;

import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;

import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;

import com.kaolafm.kradio.component.ui.R;
import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.component.ui.base.utils.ComponentUtils;
import com.kaolafm.kradio.component.ui.base.view.OvalImageView;
import com.kaolafm.kradio.component.ui.base.view.RateView;
import com.kaolafm.kradio.component.ui.base.CellBinder;
import com.kaolafm.kradio.component.ui.base.ItemClickSupport;
import com.kaolafm.kradio.component.ui.base.utils.VipCornerUtil;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.UrlUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.operation.model.column.ActivityDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.AlbumDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.PageRedirectionColumnMember;


/**
 * 首页组件2+3
 */
public class Component2And3Cell extends HomeCell implements CellBinder<View, Component2And3Cell>, ItemClickSupport {

    View layout_2_3_top_lift;
    View layout_2_3_top_right;
    View layout_2_3_bottom_1;
    View layout_2_3_bottom_2;
    View layout_2_3_bottom_3;

    private OvalImageView card_bg_iv_lift, card_pic_iv_lift;
    private TextView card_title_tv_lift;
    private ImageView card_play_iv_lift;
    private ImageView vip_icon_lift;
    private RateView card_layout_playing_lift;

    private OvalImageView card_bg_iv_right, card_pic_iv_right;
    private TextView card_title_tv_right;
    private ImageView card_play_iv_right;
    private ImageView vip_icon_right;
    private RateView card_layout_playing_right;

    private OvalImageView card_pic_iv_bottom_1;
    private TextView card_title_tv_bottom_1;
    private ImageView card_play_iv_bottom_1;
    private ImageView vip_icon_bottom_1;
    private RateView card_layout_playing_bottom_1;

    private OvalImageView card_pic_iv_bottom_2;
    private TextView card_title_tv_bottom_2;
    private ImageView card_play_iv_bottom_2;
    private ImageView vip_icon_bottom_2;
    private RateView card_layout_playing_bottom_2;

    private OvalImageView card_pic_iv_bottom_3;
    private TextView card_title_tv_bottom_3;
    private ImageView card_play_iv_bottom_3;
    private ImageView vip_icon_bottom_3;
    private RateView card_layout_playing_bottom_3;

    private static final String TAG = "Component2And3Cell";

    @Override
    public void mountView(@NonNull Component2And3Cell data, @NonNull View view, int position) {
        layout_2_3_top_lift=view.findViewById(R.id.layout_2_3_top_lift);
        layout_2_3_top_right=view.findViewById(R.id.layout_2_3_top_right);
        layout_2_3_bottom_1=view.findViewById(R.id.layout_2_3_bottom_1);
        layout_2_3_bottom_2=view.findViewById(R.id.layout_2_3_bottom_2);
        layout_2_3_bottom_3=view.findViewById(R.id.layout_2_3_bottom_3);

        initView();
        setTopLiftViewDate(data);
        setTopRightViewDate(data);
        setBottomViewDate(data);
    }

    private BaseHolder.OnViewClickListener onViewClickListener;

    @Override
    public void setOnItemClickListener(BaseHolder.OnViewClickListener listener) {
        onViewClickListener = listener;
    }

    @Override
    public int getItemType() {
        return R.layout.component_2_3_layout;
    }

    private void initView() {

        //上边左区域
        card_bg_iv_lift = layout_2_3_top_lift.findViewById(R.id.card_bg_iv);
        card_pic_iv_lift = layout_2_3_top_lift.findViewById(R.id.card_pic_iv);
        card_title_tv_lift = layout_2_3_top_lift.findViewById(R.id.card_title_tv);
        card_play_iv_lift = layout_2_3_top_lift.findViewById(R.id.card_play_iv);
        vip_icon_lift = layout_2_3_top_lift.findViewById(R.id.vip_icon);
        card_layout_playing_lift = layout_2_3_top_lift.findViewById(R.id.card_layout_playing);
        //上边右区域
        card_bg_iv_right = layout_2_3_top_right.findViewById(R.id.card_bg_iv);
        card_pic_iv_right = layout_2_3_top_right.findViewById(R.id.card_pic_iv);
        card_title_tv_right = layout_2_3_top_right.findViewById(R.id.card_title_tv);
        card_play_iv_right = layout_2_3_top_right.findViewById(R.id.card_play_iv);
        vip_icon_right = layout_2_3_top_right.findViewById(R.id.vip_icon);
        card_layout_playing_right = layout_2_3_top_right.findViewById(R.id.card_layout_playing);
        //下方区域
        card_pic_iv_bottom_1 = layout_2_3_bottom_1.findViewById(R.id.card_pic_iv);
        card_title_tv_bottom_1 = layout_2_3_bottom_1.findViewById(R.id.card_title_tv);
        card_play_iv_bottom_1 = layout_2_3_bottom_1.findViewById(R.id.card_play_iv);
        vip_icon_bottom_1 = layout_2_3_bottom_1.findViewById(R.id.vip_icon);
        card_layout_playing_bottom_1 = layout_2_3_bottom_1.findViewById(R.id.card_layout_playing);

        card_pic_iv_bottom_2 = layout_2_3_bottom_2.findViewById(R.id.card_pic_iv);
        card_title_tv_bottom_2 = layout_2_3_bottom_2.findViewById(R.id.card_title_tv);
        card_play_iv_bottom_2 = layout_2_3_bottom_2.findViewById(R.id.card_play_iv);
        vip_icon_bottom_2 = layout_2_3_bottom_2.findViewById(R.id.vip_icon);
        card_layout_playing_bottom_2 = layout_2_3_bottom_2.findViewById(R.id.card_layout_playing);

        card_pic_iv_bottom_3 = layout_2_3_bottom_3.findViewById(R.id.card_pic_iv);
        card_title_tv_bottom_3 = layout_2_3_bottom_3.findViewById(R.id.card_title_tv);
        card_play_iv_bottom_3 = layout_2_3_bottom_3.findViewById(R.id.card_play_iv);
        vip_icon_bottom_3 = layout_2_3_bottom_3.findViewById(R.id.vip_icon);
        card_layout_playing_bottom_3 = layout_2_3_bottom_3.findViewById(R.id.card_layout_playing);

        layout_2_3_top_lift.setBackground(ResUtil.getDrawable(R.drawable.component_card_bg_5));
        layout_2_3_top_right.setBackground(ResUtil.getDrawable(R.drawable.component_card_bg_6));

        card_title_tv_lift.setMaxLines(2);
        card_title_tv_right.setMaxLines(2);
        card_title_tv_bottom_1.setMaxLines(1);
        card_title_tv_bottom_2.setMaxLines(1);
        card_title_tv_bottom_3.setMaxLines(1);
        setTitleViewSize(ResUtil.getDimen(R.dimen.m113));
    }
    private void setTitleViewSize(int size) {
        ViewGroup.LayoutParams para;
        para = card_title_tv_lift.getLayoutParams();
        para.width = size;
        card_title_tv_lift.setLayoutParams(para);

        para = card_title_tv_right.getLayoutParams();
        para.width = size;
        card_title_tv_right.setLayoutParams(para);
    }
    /**
     * 设置上方左区域数据
     *
     * @param data
     */
    private void setTopLiftViewDate(Component2And3Cell data) {
        if (data.contentList != null && data.contentList.size() > 0) {
            String title = "";
            if (!TextUtils.isEmpty(data.getContentList().get(0).getTitle())) {
                title = data.getContentList().get(0).getTitle();
            } else if(!TextUtils.isEmpty(data.getContentList().get(0).getProgramDesc())){
                title = data.getContentList().get(0).getProgramDesc();
            }else if(!TextUtils.isEmpty(data.getContentList().get(0).getProgramTitle())){
                title = data.getContentList().get(0).getProgramTitle();
            }else {
                title = data.getContentList().get(0).getDescription();
            }
            card_title_tv_lift.setText(title);
            if (!TextUtils.isEmpty(UrlUtil.getCardBgUrl(data.contentList.get(0).getImageFiles())))
                ImageLoader.getInstance().displayImage(card_pic_iv_lift.getContext(),
                        UrlUtil.getCardBgUrl(data.contentList.get(0).getImageFiles()), card_bg_iv_lift);

//            if (data.getContentList().get(0) instanceof ActivityDetailColumnMember) {
//
//                card_play_iv_lift.setVisibility(View.GONE);
//                card_pic_iv_lift.setVisibility(View.GONE);
//                card_play_iv_lift.setVisibility(View.GONE);
//            } else {
                card_play_iv_lift.setVisibility(View.VISIBLE);
                card_pic_iv_lift.setVisibility(View.VISIBLE);
                if (!TextUtils.isEmpty(UrlUtil.getCardPicUrl(data.getContentList().get(0).getImageFiles()))) {
                    ConstraintLayout pCl = (ConstraintLayout) card_pic_iv_lift.getParent();
                    ConstraintSet cs = new ConstraintSet();
                    cs.clone(pCl);
                    cs.clear(card_pic_iv_lift.getId());
                    cs.connect(card_pic_iv_lift.getId(), ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP, 0);
                    cs.connect(card_pic_iv_lift.getId(), ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, 0);
                    cs.connect(card_pic_iv_lift.getId(), ConstraintSet.RIGHT, ConstraintSet.PARENT_ID, ConstraintSet.RIGHT, ResUtil.getDimen(R.dimen.m20));
                    cs.constrainWidth(card_pic_iv_lift.getId(), ResUtil.getDimen(R.dimen.y132));
                    cs.constrainHeight(card_pic_iv_lift.getId(), ResUtil.getDimen(R.dimen.y132));
                    cs.applyTo(pCl);

                    card_pic_iv_lift.setVisibility(View.VISIBLE);
                    vip_icon_lift.setVisibility(View.VISIBLE);
                    ImageLoader.getInstance().displayImageFixedSize(card_pic_iv_lift.getContext(),
                            UrlUtil.getCardPicUrl(data.contentList.get(0).getImageFiles()), card_pic_iv_lift, ResUtil.getDimen(R.dimen.m136));
                    if (data.contentList.get(0).getResType() == ResType.TYPE_BROADCAST
                            ||data.contentList.get(0).getResType() == ResType.TYPE_TV) {
                        vip_icon_lift.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_icon_live_class));
                    } else if (data.getContentList().get(0).getResType() == ResType.TYPE_LIVE) {
                        if (data.getContentList().get(0).getLiveStatus()==0||data.getContentList().get(0).getLiveStatus()==6){
                            vip_icon_lift.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_live_icon_reday));
                        }else if (data.getContentList().get(0).getLiveStatus()==1){
                            vip_icon_lift.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_live_icon_playing));
                        }
                    } else if (data.getContentList().get(0).getResType() == ResType.TYPE_VIDEO_AUDIO ||
                            data.getContentList().get(0).getResType() == ResType.TYPE_VIDEO_ALBUM) {
                        vip_icon_lift.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_icon_video));
                    } else {
                        if (data.contentList.get(0) instanceof AlbumDetailColumnMember) {
                            AlbumDetailColumnMember albumDetailColumnMember= (AlbumDetailColumnMember) data.contentList.get(0);
                            VipCornerUtil.setVipCorner(vip_icon_lift,albumDetailColumnMember.getVip(), albumDetailColumnMember.getFine(), false);
                        }else {
                            vip_icon_lift.setVisibility(View.GONE);
                        }
                    }
                }else {
                    card_pic_iv_lift.setVisibility(View.GONE);
                    vip_icon_lift.setVisibility(View.GONE);
                }

                boolean notShowPlaying = data.getContentList().get(0) instanceof PageRedirectionColumnMember
                    || data.getContentList().get(0) instanceof ActivityDetailColumnMember;
            if (!notShowPlaying) {
                card_layout_playing_lift.setVisibility(data.selected
                        && ComponentUtils.getInstance().getCardHomePlayIdIsPlaying(data.contentList.get(0).getId(), data.contentList.get(0).getCanPlay()) ?
                        View.VISIBLE : View.GONE);
                card_play_iv_lift.setVisibility(card_layout_playing_lift.getVisibility() == View.GONE ? View.VISIBLE : View.GONE);
            } else {
                card_layout_playing_lift.setVisibility(View.GONE);
                card_play_iv_lift.setVisibility(View.GONE);
            }
//            }
            layout_2_3_top_lift.setContentDescription(card_title_tv_lift.getText());
            layout_2_3_top_lift.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onViewClickListener != null) {
                        v.setTag(0);
                        onViewClickListener.onViewClick(v, getPositionInParent());
                    }
                }
            });

        }else {
            card_play_iv_lift.setVisibility(View.GONE);
            card_pic_iv_lift.setVisibility(View.GONE);
            card_play_iv_lift.setVisibility(View.GONE);
        }


    }

    /**
     * 设置上方右区域数据
     *
     * @param data
     */
    private void setTopRightViewDate(Component2And3Cell data) {
        if (data.contentList != null && data.contentList.size() > 1) {
            String title = "";
            if (!TextUtils.isEmpty(data.getContentList().get(1).getTitle())) {
                title = data.getContentList().get(1).getTitle();
            } else if(!TextUtils.isEmpty(data.getContentList().get(1).getProgramDesc())){
                title = data.getContentList().get(1).getProgramDesc();
            }else if(!TextUtils.isEmpty(data.getContentList().get(1).getProgramTitle())){
                title = data.getContentList().get(1).getProgramTitle();
            }else {
                title = data.getContentList().get(1).getDescription();
            }
            card_title_tv_right.setText(title);
            if (!TextUtils.isEmpty(UrlUtil.getCardBgUrl(data.contentList.get(1).getImageFiles())))
                ImageLoader.getInstance().displayImage(card_pic_iv_right.getContext(),
                        UrlUtil.getCardBgUrl(data.contentList.get(1).getImageFiles()), card_bg_iv_right);

            layout_2_3_top_right.setContentDescription(card_title_tv_right.getText());
            layout_2_3_top_right.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onViewClickListener != null) {
                        v.setTag(1);
                        onViewClickListener.onViewClick(v, getPositionInParent());
                    }
                }
            });
//            if (data.getContentList().get(1) instanceof PageRedirectionColumnMember
//                    || data.getContentList().get(1) instanceof ActivityDetailColumnMember) {
//                card_play_iv_right.setVisibility(View.GONE);
//                card_pic_iv_right.setVisibility(View.GONE);
//                card_play_iv_right.setVisibility(View.GONE);
//            } else {
                card_play_iv_right.setVisibility(View.VISIBLE);
                card_pic_iv_right.setVisibility(View.VISIBLE);
                if (!TextUtils.isEmpty(UrlUtil.getCardPicUrl(data.getContentList().get(1).getImageFiles()))) {
                    ConstraintLayout pCl = (ConstraintLayout) card_pic_iv_right.getParent();
                    ConstraintSet cs = new ConstraintSet();
                    cs.clone(pCl);
                    cs.clear(card_pic_iv_right.getId());
                    cs.connect(card_pic_iv_right.getId(), ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP, 0);
                    cs.connect(card_pic_iv_lift.getId(), ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, 0);
                    cs.connect(card_pic_iv_right.getId(), ConstraintSet.RIGHT, ConstraintSet.PARENT_ID, ConstraintSet.RIGHT, ResUtil.getDimen(R.dimen.m20));
                    cs.constrainWidth(card_pic_iv_right.getId(), ResUtil.getDimen(R.dimen.y132));
                    cs.constrainHeight(card_pic_iv_right.getId(), ResUtil.getDimen(R.dimen.y132));
                    cs.applyTo(pCl);

                    card_pic_iv_right.setVisibility(View.VISIBLE);
                    vip_icon_right.setVisibility(View.VISIBLE);
                    ImageLoader.getInstance().displayImageFixedSize(card_pic_iv_right.getContext(),
                            UrlUtil.getCardPicUrl(data.contentList.get(1).getImageFiles()),
                            card_pic_iv_right,
                            ResUtil.getDimen(R.dimen.m136));
                    if (data.contentList.get(1).getResType() == ResType.TYPE_BROADCAST
                            ||data.contentList.get(1).getResType() == ResType.TYPE_TV) {
                        vip_icon_right.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_icon_live_class));
                    } else if (data.getContentList().get(1).getResType() == ResType.TYPE_LIVE) {
                        if (data.getContentList().get(1).getLiveStatus()==0||data.getContentList().get(1).getLiveStatus()==6){
                            vip_icon_right.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_live_icon_reday));
                        }else if (data.getContentList().get(1).getLiveStatus()==1){
                            vip_icon_right.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_live_icon_playing));
                        }
                    } else if (data.getContentList().get(1).getResType() == ResType.TYPE_VIDEO_AUDIO ||
                            data.getContentList().get(1).getResType() == ResType.TYPE_VIDEO_ALBUM) {
                        vip_icon_right.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_icon_video));
                    } else {
//                        VipCornerUtil.setVipCorner(vip_icon_right, data.contentList.get(1).getVip(), data.contentList.get(1).getFine(), false);
                        if (data.contentList.get(1) instanceof AlbumDetailColumnMember) {
                            AlbumDetailColumnMember albumDetailColumnMember= (AlbumDetailColumnMember) data.contentList.get(1);
                            VipCornerUtil.setVipCorner(vip_icon_right,albumDetailColumnMember.getVip(), albumDetailColumnMember.getFine(), false);
                        }else {
                            vip_icon_right.setVisibility(View.GONE);
                        }
                    }
                }else {
                    card_pic_iv_right.setVisibility(View.GONE);
                    vip_icon_right.setVisibility(View.GONE);
                }

            boolean notShowPlaying = data.getContentList().get(1) instanceof PageRedirectionColumnMember
                    || data.getContentList().get(1) instanceof ActivityDetailColumnMember;
            if (!notShowPlaying) {
                card_layout_playing_right.setVisibility(data.selected
                        && ComponentUtils.getInstance().getCardHomePlayIdIsPlaying(data.contentList.get(1).getId()
                        , data.contentList.get(1).getCanPlay()) ?
                        View.VISIBLE : View.GONE);
                card_play_iv_right.setVisibility(card_layout_playing_right.getVisibility() == View.GONE ? View.VISIBLE : View.GONE);
            } else {
                card_layout_playing_right.setVisibility(View.GONE);
                card_play_iv_right.setVisibility(View.GONE);
            }
//            }

        }else {
            card_play_iv_right.setVisibility(View.GONE);
            card_pic_iv_right.setVisibility(View.GONE);
            card_play_iv_right.setVisibility(View.GONE);
        }

    }

    /**
     * 设置下方区域数据
     *
     * @param data
     */
    private void setBottomViewDate(Component2And3Cell data) {
        layout_2_3_bottom_1.setBackground(new ColorDrawable());
        if (data.contentList != null && data.contentList.size() > 2) {
            String title = "";
            if (!TextUtils.isEmpty(data.getContentList().get(2).getTitle())) {
                title = data.getContentList().get(2).getTitle();
            } else if(!TextUtils.isEmpty(data.getContentList().get(2).getProgramDesc())){
                title = data.getContentList().get(2).getProgramDesc();
            }else if(!TextUtils.isEmpty(data.getContentList().get(2).getProgramTitle())){
                title = data.getContentList().get(2).getProgramTitle();
            }else {
                title = data.getContentList().get(2).getDescription();
            }
            card_title_tv_bottom_1.setText(title);
            if (!TextUtils.isEmpty(UrlUtil.getCardPicUrl(data.getContentList().get(2).getImageFiles()))) {
                card_pic_iv_bottom_1.setVisibility(View.VISIBLE);
                vip_icon_bottom_1.setVisibility(View.VISIBLE);
                ImageLoader.getInstance().displayImage(card_pic_iv_bottom_1.getContext(),
                        UrlUtil.getCardPicUrl(data.contentList.get(2).getImageFiles()), card_pic_iv_bottom_1);
                if (data.contentList.get(2).getResType() == ResType.TYPE_BROADCAST
                        ||data.contentList.get(2).getResType() == ResType.TYPE_TV) {
                    vip_icon_bottom_1.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_icon_live_class));
                } else if (data.getContentList().get(2).getResType() == ResType.TYPE_LIVE) {
                    if (data.getContentList().get(2).getLiveStatus()==0||data.getContentList().get(2).getLiveStatus()==6){
                        vip_icon_bottom_1.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_live_icon_reday));
                    }else if (data.getContentList().get(2).getLiveStatus()==1){
                        vip_icon_bottom_1.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_live_icon_playing));
                    }
                } else if (data.getContentList().get(2).getResType() == ResType.TYPE_VIDEO_AUDIO ||
                        data.getContentList().get(2).getResType() == ResType.TYPE_VIDEO_ALBUM) {
                    vip_icon_bottom_1.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_icon_video));
                } else {
//                    VipCornerUtil.setVipCorner(vip_icon_bottom_1, data.contentList.get(2).getVip(), data.contentList.get(2).getFine(), false);
                    if (data.contentList.get(2) instanceof AlbumDetailColumnMember) {
                        AlbumDetailColumnMember albumDetailColumnMember= (AlbumDetailColumnMember) data.contentList.get(2);
                        VipCornerUtil.setVipCorner(vip_icon_bottom_1,albumDetailColumnMember.getVip(), albumDetailColumnMember.getFine(), false);
                    }else {
                        vip_icon_bottom_1.setVisibility(View.GONE);
                    }
                }
            }else {
                card_pic_iv_bottom_1.setVisibility(View.GONE);
                vip_icon_bottom_1.setVisibility(View.GONE);
            }
            layout_2_3_bottom_1.setContentDescription(card_title_tv_bottom_1.getText());
            layout_2_3_bottom_1.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onViewClickListener != null) {
                        v.setTag(2);
                        onViewClickListener.onViewClick(v, getPositionInParent());
                    }
                }
            });

            boolean notShowPlaying = data.getContentList().get(2) instanceof PageRedirectionColumnMember
                    || data.getContentList().get(2) instanceof ActivityDetailColumnMember;
            if (!notShowPlaying) {
                card_layout_playing_bottom_1.setVisibility(data.selected
                        && ComponentUtils.getInstance().getCardHomePlayIdIsPlaying(data.contentList.get(2).getId(), data.contentList.get(2).getCanPlay()) ?
                        View.VISIBLE : View.GONE);
                card_play_iv_bottom_1.setVisibility(card_layout_playing_bottom_1.getVisibility() == View.GONE ? View.VISIBLE : View.GONE);
            } else {
                card_layout_playing_bottom_1.setVisibility(View.GONE);
                card_play_iv_bottom_1.setVisibility(View.GONE);
            }

        }

        layout_2_3_bottom_2.setBackground(new ColorDrawable());
        if (data.contentList != null && data.contentList.size() > 3) {
            String title = "";
            if (!TextUtils.isEmpty(data.getContentList().get(3).getTitle())) {
                title = data.getContentList().get(3).getTitle();
            } else if(!TextUtils.isEmpty(data.getContentList().get(3).getProgramDesc())){
                title = data.getContentList().get(3).getProgramDesc();
            }else if(!TextUtils.isEmpty(data.getContentList().get(3).getProgramTitle())){
                title = data.getContentList().get(3).getProgramTitle();
            }else {
                title = data.getContentList().get(3).getDescription();
            }
            card_title_tv_bottom_2.setText(title);
            if (!TextUtils.isEmpty(UrlUtil.getCardPicUrl(data.getContentList().get(3).getImageFiles()))) {
                card_pic_iv_bottom_2.setVisibility(View.VISIBLE);
                vip_icon_bottom_2.setVisibility(View.VISIBLE);
                ImageLoader.getInstance().displayImage(card_pic_iv_bottom_2.getContext(),
                        UrlUtil.getCardPicUrl(data.contentList.get(3).getImageFiles()), card_pic_iv_bottom_2);
                if (data.contentList.get(3).getResType() == ResType.TYPE_BROADCAST
                        ||data.contentList.get(3).getResType() == ResType.TYPE_TV) {
                    vip_icon_bottom_2.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_icon_live_class));
                }else if (data.getContentList().get(3).getResType() == ResType.TYPE_LIVE) {
                    if (data.getContentList().get(3).getLiveStatus()==0||data.getContentList().get(3).getLiveStatus()==6){
                        vip_icon_bottom_2.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_live_icon_reday));
                    }else if (data.getContentList().get(3).getLiveStatus()==1){
                        vip_icon_bottom_2.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_live_icon_playing));
                    }
                } else if (data.getContentList().get(3).getResType() == ResType.TYPE_VIDEO_AUDIO ||
                        data.getContentList().get(3).getResType() == ResType.TYPE_VIDEO_ALBUM) {
                    vip_icon_bottom_2.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_icon_video));
                } else {
//                    VipCornerUtil.setVipCorner(vip_icon_bottom_2, data.contentList.get(3).getVip(), data.contentList.get(3).getFine(), false);
                    if (data.contentList.get(3) instanceof AlbumDetailColumnMember) {
                        AlbumDetailColumnMember albumDetailColumnMember= (AlbumDetailColumnMember) data.contentList.get(3);
                        VipCornerUtil.setVipCorner(vip_icon_bottom_2,albumDetailColumnMember.getVip(), albumDetailColumnMember.getFine(), false);
                    }else {
                        vip_icon_bottom_2.setVisibility(View.GONE);
                    }
                }
            }else {
                card_pic_iv_bottom_2.setVisibility(View.GONE);
                vip_icon_bottom_2.setVisibility(View.GONE);
            }
            layout_2_3_bottom_2.setContentDescription(card_title_tv_bottom_2.getText());
            layout_2_3_bottom_2.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onViewClickListener != null) {
                        v.setTag(3);
                        onViewClickListener.onViewClick(v, getPositionInParent());
                    }
                }
            });

            boolean notShowPlaying = data.getContentList().get(3) instanceof PageRedirectionColumnMember
                    || data.getContentList().get(3) instanceof ActivityDetailColumnMember;
            if (!notShowPlaying) {
                card_layout_playing_bottom_2.setVisibility(data.selected
                        && ComponentUtils.getInstance().getCardHomePlayIdIsPlaying(data.contentList.get(3).getId(), data.contentList.get(3).getCanPlay()) ?
                        View.VISIBLE : View.GONE);
                card_play_iv_bottom_2.setVisibility(card_layout_playing_bottom_2.getVisibility() == View.GONE ? View.VISIBLE : View.GONE);
            } else {
                card_layout_playing_bottom_2.setVisibility(View.GONE);
                card_play_iv_bottom_2.setVisibility(View.GONE);
            }

        }

        layout_2_3_bottom_3.setBackground(new ColorDrawable());
        if (data.contentList != null && data.contentList.size() > 4) {

            String title = "";
            if (!TextUtils.isEmpty(data.getContentList().get(4).getTitle())) {
                title = data.getContentList().get(4).getTitle();
            } else if(!TextUtils.isEmpty(data.getContentList().get(4).getProgramDesc())){
                title = data.getContentList().get(4).getProgramDesc();
            }else if(!TextUtils.isEmpty(data.getContentList().get(4).getProgramTitle())){
                title = data.getContentList().get(4).getProgramTitle();
            }else {
                title = data.getContentList().get(4).getDescription();
            }
            card_title_tv_bottom_3.setText(title);
            if (!TextUtils.isEmpty(UrlUtil.getCardPicUrl(data.getContentList().get(4).getImageFiles()))) {
                card_pic_iv_bottom_3.setVisibility(View.VISIBLE);
                vip_icon_bottom_3.setVisibility(View.VISIBLE);
                ImageLoader.getInstance().displayImage(card_pic_iv_bottom_3.getContext(),
                        UrlUtil.getCardPicUrl(data.contentList.get(4).getImageFiles()), card_pic_iv_bottom_3);
                if (data.contentList.get(4).getResType() == ResType.TYPE_BROADCAST
                        ||data.contentList.get(4).getResType() == ResType.TYPE_TV) {
                    vip_icon_bottom_3.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_icon_live_class));
                } else if (data.getContentList().get(4).getResType() == ResType.TYPE_LIVE) {
                    if (data.getContentList().get(4).getLiveStatus()==0||data.getContentList().get(4).getLiveStatus()==6){
                        vip_icon_bottom_3.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_live_icon_reday));
                    }else if (data.getContentList().get(4).getLiveStatus()==1){
                        vip_icon_bottom_3.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_live_icon_playing));
                    }
                } else if (data.getContentList().get(4).getResType() == ResType.TYPE_VIDEO_AUDIO ||
                        data.getContentList().get(4).getResType() == ResType.TYPE_VIDEO_ALBUM) {
                    vip_icon_bottom_3.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_icon_video));
                } else {
//                    VipCornerUtil.setVipCorner(vip_icon_bottom_3, data.contentList.get(4).getVip(), data.contentList.get(4).getFine(), false);
                    if (data.contentList.get(4) instanceof AlbumDetailColumnMember) {
                        AlbumDetailColumnMember albumDetailColumnMember= (AlbumDetailColumnMember) data.contentList.get(4);
                        VipCornerUtil.setVipCorner(vip_icon_bottom_3,albumDetailColumnMember.getVip(), albumDetailColumnMember.getFine(), false);
                    }else {
                        vip_icon_bottom_3.setVisibility(View.GONE);
                    }
                }
            }else {
                card_pic_iv_bottom_3.setVisibility(View.GONE);
                vip_icon_bottom_3.setVisibility(View.GONE);
            }
            ImageLoader.getInstance().displayImage(card_pic_iv_bottom_3.getContext(),
                    UrlUtil.getCardPicUrl(data.contentList.get(4).getImageFiles()), card_pic_iv_bottom_3);
            layout_2_3_bottom_3.setContentDescription(card_title_tv_bottom_3.getText());
            layout_2_3_bottom_3.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onViewClickListener != null) {
                        v.setTag(4);
                        onViewClickListener.onViewClick(v, getPositionInParent());
                    }
                }
            });

            boolean notShowPlaying = data.getContentList().get(4) instanceof PageRedirectionColumnMember
                    || data.getContentList().get(4) instanceof ActivityDetailColumnMember;
            if (!notShowPlaying) {
                card_layout_playing_bottom_3.setVisibility(data.selected
                        && ComponentUtils.getInstance().getCardHomePlayIdIsPlaying(data.contentList.get(4).getId(), data.contentList.get(4).getCanPlay()) ?
                        View.VISIBLE : View.GONE);
                card_play_iv_bottom_3.setVisibility(card_layout_playing_bottom_3.getVisibility() == View.GONE ? View.VISIBLE : View.GONE);
            } else {
                card_layout_playing_bottom_3.setVisibility(View.GONE);
                card_play_iv_bottom_3.setVisibility(View.GONE);
            }

        }

    }

    @Override
    public void release() {
        super.release();
        // CPU优化：安全地清理Glide图片资源
        if (card_pic_iv_lift != null) {
            Context context = card_pic_iv_lift.getContext();
            if (context != null && !isContextDestroyed(context)) {
                Logger.i(TAG, "清理card_pic_iv_lift图片资源");
                Glide.with(context).clear(card_pic_iv_lift);
            }
        }
        if (card_pic_iv_right != null) {
            Context context = card_pic_iv_right.getContext();
            if (context != null && !isContextDestroyed(context)) {
                Logger.i(TAG, "清理card_pic_iv_right图片资源");
                Glide.with(context).clear(card_pic_iv_right);
            }
        }
        if (card_pic_iv_bottom_1 != null) {
            Context context = card_pic_iv_bottom_1.getContext();
            if (context != null && !isContextDestroyed(context)) {
                Logger.i(TAG, "清理card_pic_iv_bottom_1图片资源");
                Glide.with(context).clear(card_pic_iv_bottom_1);
            }
        }
        if (card_pic_iv_bottom_2 != null) {
            Context context = card_pic_iv_bottom_2.getContext();
            if (context != null && !isContextDestroyed(context)) {
                Logger.i(TAG, "清理card_pic_iv_bottom_2图片资源");
                Glide.with(context).clear(card_pic_iv_bottom_2);
            }
        }
        if (card_pic_iv_bottom_3 != null) {
            Context context = card_pic_iv_bottom_3.getContext();
            if (context != null && !isContextDestroyed(context)) {
                Logger.i(TAG, "清理card_pic_iv_bottom_3图片资源");
                Glide.with(context).clear(card_pic_iv_bottom_3);
            }
        }
    }

    /**
     * 检查Context是否已被销毁
     */
    private boolean isContextDestroyed(Context context) {
        if (context instanceof Activity) {
            return ((Activity) context).isDestroyed();
        }
        return false;
    }
}
