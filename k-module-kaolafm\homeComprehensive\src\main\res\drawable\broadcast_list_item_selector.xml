<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <gradient android:angle="270" android:endColor="#2B4369" android:startColor="#4D5E99" />
            <corners android:radius="@dimen/m8" />
        </shape>
    </item>
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <gradient android:angle="270" android:endColor="#2B4369" android:startColor="#4D5E99" />
            <corners android:radius="@dimen/m8" />
        </shape>
    </item>
    <item android:drawable="@color/colorTransparent" />
</selector>