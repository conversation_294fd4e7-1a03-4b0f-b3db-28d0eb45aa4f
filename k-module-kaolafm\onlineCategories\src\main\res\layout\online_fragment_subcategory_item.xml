<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_subcategory_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorTransparent">


    <com.kaolafm.kradio.common.widget.GridTouchInterceptRecyclerView
        android:id="@+id/rv_subcategory_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/tv_refresh_btn"
        android:overScrollMode="never" />


    <ViewStub
        android:layout="@layout/online_layout_each_status_page"
        android:id="@+id/vs_layout_error_page"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <include
        android:id="@+id/category_loading_view"
        layout="@layout/online_refresh_center"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        tools:visibility="visible" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clPermissionTip"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="获取不到定位"
            android:textColor="@color/online_broadcast_list_item_text_color"
            android:textSize="@dimen/text_size4"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.35" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>
