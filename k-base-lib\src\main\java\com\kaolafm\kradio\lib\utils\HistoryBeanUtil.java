package com.kaolafm.kradio.lib.utils;

import android.text.TextUtils;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.lib.bean.HistoryItem;
import com.kaolafm.kradio.lib.bean.Itemable;
import com.kaolafm.kradio.lib.bean.LoginedHistoryItem;
import com.kaolafm.opensdk.api.history.model.ListeningHistory;
import com.kaolafm.opensdk.player.logic.model.item.AlbumPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.BroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.FeaturePlayItem;
import com.kaolafm.opensdk.player.logic.model.item.OneKeyPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.RadioPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.TVPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.VideoAlbumPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Yan
 * @date 2020/8/18
 */
public class HistoryBeanUtil {

    public static LoginedHistoryItem listeningToLoginedItem(ListeningHistory history) {
        if (history == null) {
            return null;
        }
        LoginedHistoryItem historyItem = new LoginedHistoryItem();
        int type = history.getType();
        if (type == 1) {
            type = 0;
        }
        historyItem.setType(String.valueOf(type));
        historyItem.setTypeId(type);
        historyItem.setAudioId(history.getAudioId());
        historyItem.setRadioId(history.getRadioId());
        historyItem.setPlayedTime(history.getPlayedTime());
        historyItem.setRadioTitle(history.getRadioTitle());
        historyItem.setPicUrl(history.getPicUrl());
        historyItem.setTimeStamp(history.getTimeStamp());
        historyItem.setAudioTitle(history.getAudioTitle());
        historyItem.setDuration(history.getDuration());
        historyItem.setOrderNum(history.getOrderNum());
        historyItem.setShareUrl(history.getShareUrl());
        historyItem.setPlayUrl(history.getPlayUrl());
        historyItem.setFine(history.getFine());
        historyItem.setVip(history.getVip());
        historyItem.setIsOffline(history.getOnline() != 1);
        historyItem.setFreq(history.getFreq());
        historyItem.setListenCount(history.getListenCount());
        historyItem.setParamOne(history.getParamOne());
        historyItem.setParamTwo(history.getParamTwo());
        historyItem.setCurrentProgramName(history.getCurrentProgramName());
        historyItem.setRadioUpdateTime(history.getRadioUpdateTime());
        return historyItem;
    }

    /**
     * 转成map，是为了方便查询，时间复杂度为1；如果使用list时间复杂度就是n，后面再加一层就是n²了
     *
     * @param historyList
     * @return
     */
    public static Map<String, LoginedHistoryItem> listeningToLoginedItem(List<ListeningHistory> historyList) {
        HashMap<String, LoginedHistoryItem> historyItems = new HashMap<>();
        if (!ListUtil.isEmpty(historyList)) {
            for (ListeningHistory history : historyList) {
                LoginedHistoryItem item = listeningToLoginedItem(history);
                if (item != null) {
                    historyItems.put(item.getRadioId(), item);
                }
            }
        }
        return historyItems;
    }

    public static void interchange(Itemable from, Itemable to) {
        to.setType(from.getType());
        to.setRadioId(from.getRadioId());
        to.setTypeId(from.getTypeId());
        to.setRadioTitle(from.getRadioTitle());
        to.setPicUrl(from.getPicUrl());
        to.setAudioId(from.getAudioId());
        to.setAudioTitle(from.getAudioTitle());
        to.setPlayUrl(from.getPlayUrl());
        to.setPlayedTime(from.getPlayedTime());
        to.setDuration(from.getDuration());
        to.setOffline(from.isOffline());
        to.setTimeStamp(from.getTimeStamp());
        to.setOrderNum(from.getOrderNum());
        to.setOfflinePlayUrl(from.getOfflinePlayUrl());
        to.setShareUrl(from.getShareUrl());
        to.setCategoryId(from.getCategoryId());
        to.setParamOne(from.getParamOne());
        to.setParamTwo(from.getParamTwo());
        to.setSourceUrl(from.getSourceUrl());
        to.setOrderMode(from.getOrderMode());
        to.setPlaying(from.isPlaying());
        to.setFine(from.getFine());
        to.setVip(from.getVip());
        to.setFreq(from.getFreq());
        to.setBroadcastSort(from.getBroadcastSort());
        to.setListenCount(from.getListenCount());
        to.setCurrentProgramName(from.getCurrentProgramName());
        to.setRadioUpdateTime(from.getRadioUpdateTime());
    }

    public static HistoryItem toLocalItem(LoginedHistoryItem item) {
        if (item == null) {
            return null;
        }
        HistoryItem historyItem = new HistoryItem();
        interchange(item, historyItem);
        return historyItem;
    }

    public static void translateToHistoryItem(PlayItem playItem, Itemable historyItem) {
        if (playItem == null || historyItem == null) {
            return;
        }
        historyItem.setAudioId(String.valueOf(playItem.getAudioId()));
        historyItem.setAudioTitle(playItem.getTitle());
        historyItem.setTimeStamp(System.currentTimeMillis());
        historyItem.setType(String.valueOf(playItem.getType()));
        historyItem.setTypeId(playItem.getType());
        historyItem.setListenCount(playItem.getListenCount());
        //tudo
        if (playItem instanceof BroadcastPlayItem) {
            setBroadcastPlayItem(historyItem, (BroadcastPlayItem) playItem);
        } else if (playItem instanceof TVPlayItem) {
            setTVPlayItem(historyItem, (TVPlayItem) playItem);
        } else if (playItem instanceof RadioPlayItem) {
            setRadioHistoryItem(historyItem, (RadioPlayItem) playItem);
        } else if (playItem instanceof AlbumPlayItem) {
            setAlbumHistoryItem(historyItem, (AlbumPlayItem) playItem);
        }
        else if (playItem instanceof VideoAlbumPlayItem) {
            setVideoAlbumHistoryItem(historyItem, (VideoAlbumPlayItem) playItem);
        }
        else if (playItem instanceof FeaturePlayItem) {
            setFeatureHistoryItem(historyItem, (FeaturePlayItem) playItem);
        } else if (playItem instanceof OneKeyPlayItem) {
            setOneKeyPlayItem(historyItem, (OneKeyPlayItem) playItem);
        }

    }

    private static void setBroadcastPlayItem(Itemable historyItem, BroadcastPlayItem playItem) {
        historyItem.setRadioId(String.valueOf(playItem.getInfoData().getAlbumId()));
        historyItem.setPicUrl(playItem.getInfoData().getAlbumPic());
        String freq = playItem.getFrequencyChannel() == null ? "" : playItem.getFrequencyChannel();
        historyItem.setFreq(freq);
        historyItem.setBroadcastSort(playItem.getBroadcastSort());
        historyItem.setRadioTitle(playItem.getInfoData().getAlbumName());
        historyItem.setSourceUrl(playItem.getInfoData().getIcon());
        String currentProgramName = "暂无节目单";
        if (!playItem.getInfoData().getTitle().equals(playItem.getInfoData().getAlbumName())) {
            currentProgramName = playItem.getInfoData().getTitle();
        }
        historyItem.setCurrentProgramName(currentProgramName);
        String updateTime = playItem.getInfoData().getUpdateTime();
        if (updateTime == null) {
            updateTime = "0";
        } else if (updateTime.length() < 13) {
            updateTime += "000";
        }
        historyItem.setRadioUpdateTime(Long.parseLong(updateTime));
    }

    private static void setTVPlayItem(Itemable historyItem, TVPlayItem playItem) {
        historyItem.setRadioId(String.valueOf(playItem.getInfoData().getAlbumId()));
        historyItem.setPicUrl(playItem.getInfoData().getAlbumPic());
        String freq = playItem.getFrequencyChannel() == null ? "" : playItem.getFrequencyChannel();
        historyItem.setFreq(freq);
        historyItem.setRadioTitle(playItem.getInfoData().getAlbumName());
        historyItem.setSourceUrl(playItem.getInfoData().getIcon());
        String currentProgramName = "暂无节目单";
        if (!playItem.getInfoData().getTitle().equals(playItem.getInfoData().getAlbumName())) {
            currentProgramName = playItem.getInfoData().getTitle();
        }
        historyItem.setCurrentProgramName(currentProgramName);
        String updateTime = playItem.getInfoData().getUpdateTime();
        if (updateTime == null) {
            updateTime = "0";
        } else if (updateTime.length() < 13) {
            updateTime += "000";
        }
        historyItem.setRadioUpdateTime(Long.parseLong(updateTime));
    }

    private static void setOneKeyPlayItem(Itemable historyItem, OneKeyPlayItem playItem) {
        historyItem.setRadioId(String.valueOf(playItem.getInfoData().getAlbumId()));
        historyItem.setRadioTitle(playItem.getInfoData().getAlbumName());
        historyItem.setPlayUrl(playItem.getPlayUrl());
        historyItem.setPlayedTime(playItem.getPosition());
        historyItem.setDuration(playItem.getDuration());
        historyItem.setOffline(false);
        historyItem.setOrderNum(playItem.getInfoData().getOrderNum());
        String pic = playItem.getInfoData().getAudioPic();
        if (TextUtils.isEmpty(pic)) {
            pic = playItem.getInfoData().getAlbumPic();
        }
        historyItem.setPicUrl(pic);
        historyItem.setVip(playItem.getVip());
        historyItem.setFine(playItem.getFine());
    }

    private static void setRadioHistoryItem(Itemable historyItem, RadioPlayItem radioPlayItem) {
        historyItem.setRadioId(String.valueOf(radioPlayItem.getRadioInfoData().getRadioId()));
        historyItem.setRadioTitle(radioPlayItem.getRadioInfoData().getRadioName());
        historyItem.setPlayUrl(radioPlayItem.getPlayUrl());
        historyItem.setPlayedTime(radioPlayItem.getPosition());
        historyItem.setDuration(radioPlayItem.getDuration());
        historyItem.setOrderNum(radioPlayItem.getInfoData().getOrderNum());
        historyItem.setPicUrl(radioPlayItem.getRadioInfoData().getRadioPic());
        historyItem.setCategoryId((int) radioPlayItem.getRadioInfoData().getCategoryId());
        String updateTime = radioPlayItem.getInfoData().getUpdateTime();
        if (updateTime == null) {
            updateTime = "0";
        } else if (updateTime.length() < 13) {
            updateTime += "000";
        }
        historyItem.setRadioUpdateTime(Long.parseLong(updateTime));
    }

    private static void setAlbumHistoryItem(Itemable historyItem, AlbumPlayItem playItem) {
        historyItem.setRadioId(String.valueOf(playItem.getInfoData().getAlbumId()));
        historyItem.setRadioTitle(playItem.getInfoData().getAlbumName());
        historyItem.setPlayUrl(playItem.getPlayUrl());
        historyItem.setOfflinePlayUrl(playItem.getOfflineInfoData().getOfflinePlayUrl());
        historyItem.setPlayedTime(playItem.getPosition());
        historyItem.setDuration(playItem.getDuration());
        historyItem.setOffline(false);
        historyItem.setOrderNum(playItem.getInfoData().getOrderNum());
        String pic = playItem.getInfoData().getAudioPic();
        if (TextUtils.isEmpty(pic)) {
            pic = playItem.getInfoData().getAlbumPic();
        }
        historyItem.setPicUrl(pic);
        historyItem.setVip(playItem.getVip());
        historyItem.setFine(playItem.getFine());
        historyItem.setParamOne(playItem.getAlbumInfoData().getBreakPointContinue());
        String updateTime = playItem.getInfoData().getUpdateTime();
        if (updateTime == null) {
            updateTime = "0";
        } else if (updateTime.length() < 13) {
            updateTime += "000";
        }
        historyItem.setRadioUpdateTime(Long.parseLong(updateTime));
    }

    private static void setVideoAlbumHistoryItem(Itemable historyItem, VideoAlbumPlayItem playItem) {
        historyItem.setRadioId(String.valueOf(playItem.getRadioId()));
        historyItem.setRadioTitle(playItem.getInfoData().getTitle());
        historyItem.setPlayUrl(playItem.getPlayUrl());
        historyItem.setOfflinePlayUrl(playItem.getOfflineInfoData().getOfflinePlayUrl());
        historyItem.setPlayedTime(playItem.getPosition());
        historyItem.setDuration(playItem.getDuration());
        historyItem.setOffline(false);
        historyItem.setOrderNum(playItem.getInfoData().getOrderNum());
        String pic = playItem.getInfoData().getAudioPic();
        if (TextUtils.isEmpty(pic)) {
            pic = playItem.getInfoData().getAlbumPic();
        }
        historyItem.setPicUrl(pic);
        historyItem.setVip(playItem.getVip());
        historyItem.setFine(playItem.getFine());
        historyItem.setParamOne(playItem.getAlbumInfoData().getBreakPointContinue());
        String updateTime = playItem.getInfoData().getUpdateTime();
        if (updateTime == null) {
            updateTime = "0";
        } else if (updateTime.length() < 13) {
            updateTime += "000";
        }
        historyItem.setRadioUpdateTime(Long.parseLong(updateTime));
    }

    private static void setFeatureHistoryItem(Itemable historyItem, FeaturePlayItem playItem) {
        historyItem.setRadioId(String.valueOf(playItem.getInfoData().getAlbumId()));
        historyItem.setRadioTitle(playItem.getInfoData().getAlbumName());
        historyItem.setPlayUrl(playItem.getPlayUrl());
        historyItem.setOfflinePlayUrl(playItem.getOfflineInfoData().getOfflinePlayUrl());
        historyItem.setPlayedTime(playItem.getPosition());
        historyItem.setDuration(playItem.getDuration());
        historyItem.setOffline(false);
        historyItem.setOrderNum(playItem.getInfoData().getOrderNum());
        String pic = playItem.getInfoData().getAlbumPic();
        historyItem.setRadioUpdateTime(Long.parseLong(playItem.getInfoData().getUpdateTime()));
        historyItem.setPicUrl(pic);
        historyItem.setVip(playItem.getVip());
        historyItem.setFine(playItem.getFine());
        String updateTime = playItem.getInfoData().getUpdateTime();
        if (updateTime == null) {
            updateTime = "0";
        } else if (updateTime.length() < 13) {
            updateTime += "000";
        }
        historyItem.setRadioUpdateTime(Long.parseLong(updateTime));

    }
}
