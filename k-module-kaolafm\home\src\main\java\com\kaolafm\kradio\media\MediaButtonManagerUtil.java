package com.kaolafm.kradio.media;

import android.content.Context;
import android.os.CountDownTimer;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.SystemClock;
import android.util.Log;
import android.view.KeyEvent;

import com.kaolafm.kradio.lib.report.ReportUtil;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioConvertMediaKeyCodeInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioMediaKeyOkCodeInter;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.report.event.PlayerUiControlReportEvent;
import com.kaolafm.report.util.ReportConstants;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-04-09 17:26
 ******************************************/
public final class MediaButtonManagerUtil {
    private static final String TAG = "MediaButtonManagerUtil";

    private static boolean bFinish = true;

    private CountDownTimer mCountDownTimer;
    /**
     * 必须保证此变量为static类型否则会出现此变量在双击或多次点击不计数问题
     */
    private static int mClickIndex = -1;
    /**
     * 播放暂停消息
     */
    private static final int MEDIA_PLAY_OR_PAUSE_MSG = 0X1;
    /**
     * 播放消息
     */
    private static final int MEDIA_PLAY = 0X2;
    /**
     * 暂停消息
     */
    private static final int MEDIA_PAUSE = 0X3;
    /**
     * 播放下一首消息
     */
    private static final int MEDIA_NEXT = 0X4;
    /**
     * 播放上一首消息
     */
    private static final int MEDIA_PRE = 0X5;
    /**
     * 停止消息
     */
    private static final int MEDIA_STOP = 0X6;
    /**
     * 两次点击时间间隔单位MS
     */
    private long timeInterval = 500L;
    /**
     * 上次触发事件的时间
     */
    private static long lastClickTime;

    private Handler mHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case MEDIA_PLAY_OR_PAUSE_MSG:
                    KRadioMediaKeyOkCodeInter mediaKeyOkCodeImpl = ClazzImplUtil.getInter("KRadioMediaKeyOkCodeImpl");
                    if (mediaKeyOkCodeImpl != null) {
                        mediaKeyOkCodeImpl.onClick();
                    } else {
                        checkForMultiClick();
                    }
                    break;
                case MEDIA_PLAY: {
                    ReportUtil.addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_PLAY, PlayerUiControlReportEvent.CONTROL_TYPE_HARD_WARE, null);
                    PlayerManagerHelper.getInstance().play(true);
                }
                break;
                case MEDIA_PAUSE: {
                    ReportUtil.addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_PAUSE, PlayerUiControlReportEvent.CONTROL_TYPE_HARD_WARE, null);
                    PlayerManagerHelper.getInstance().pause(true);
                }
                break;
                case MEDIA_NEXT: {
                    if (PlayerManagerHelper.getInstance().hasNextItem()) {
                        ReportUtil.addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_NEXT, PlayerUiControlReportEvent.CONTROL_TYPE_HARD_WARE, null);
                        ReportUtil.reportEndPlay(ReportConstants.PLAY_CHANGE_BY_HANDWARE_CONTROL, true);
                        PlayerManagerHelper.getInstance().playNext(true);
                    } else {
                        Context context = AppDelegate.getInstance().getContext();
                        if (context != null) {
                            ToastUtil.showOnly(context, context.getString(R.string.is_last_one_warning_str));
                        }
                    }
                }
                break;
                case MEDIA_PRE: {
                    if (PlayerManagerHelper.getInstance().hasPreItem()) {
                        ReportUtil.addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_PREVIOUS, PlayerUiControlReportEvent.CONTROL_TYPE_HARD_WARE, null);
                        ReportUtil.reportEndPlay(ReportConstants.PLAY_CHANGE_BY_HANDWARE_CONTROL, true);
                        PlayerManagerHelper.getInstance().playPre(true);
                    } else {
                        Context context = AppDelegate.getInstance().getContext();
                        if (context != null) {
                            ToastUtil.showOnly(context, context.getString(R.string.is_first_one_warning_str));
                        }
                    }
                }
                break;
                case MEDIA_STOP:
                    PlayerManager.getInstance().reset();
                    break;
                default:
                    break;
            }
        }
    };

    /**
     * 处外部耳机按键事件
     *
     * @param keyCode 耳机对应键值
     */
    public void manageMediaButtonClick(int keyCode) {
        KRadioConvertMediaKeyCodeInter convertKeycodeInter = ClazzImplUtil.getInter("KRadioConvertMediaKeyCodeImpl");
        if (convertKeycodeInter != null) {
            keyCode = convertKeycodeInter.convertKeyCode(keyCode);
        }
        Message msg = mHandler.obtainMessage();
        msg.arg1 = keyCode;
        Log.i(TAG, "manageMediaButtonClick:" + keyCode);
        if (PlayerManagerHelper.getInstance().isAudioAdPlayLockOver()) {
            return;
        }
        switch (keyCode) {
            case KeyEvent.KEYCODE_MEDIA_PLAY:
                if (mClickIndex != -1) {
                    break;
                }
                msg.what = MEDIA_PLAY;
                mHandler.sendMessage(msg);
                break;
            case KeyEvent.KEYCODE_MEDIA_PAUSE:
                if (mClickIndex != -1) {
                    break;
                }
                msg.what = MEDIA_PAUSE;
                mHandler.sendMessage(msg);
                break;
            case KeyEvent.KEYCODE_MEDIA_STOP:
                if (mClickIndex != -1) {
                    break;
                }
                msg.what = MEDIA_STOP;
                mHandler.sendMessage(msg);
                break;
            case KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE:
            case KeyEvent.KEYCODE_HEADSETHOOK:
                msg.what = MEDIA_PLAY_OR_PAUSE_MSG;
                mHandler.sendMessage(msg);
                break;
            case KeyEvent.KEYCODE_F3:
            case KeyEvent.KEYCODE_MEDIA_NEXT:
                long nextTime = SystemClock.elapsedRealtime();
                if (nextTime - lastClickTime > timeInterval) {
                    msg.what = MEDIA_NEXT;
                    mHandler.sendMessage(msg);
                }
                lastClickTime = SystemClock.elapsedRealtime();
                break;
            case KeyEvent.KEYCODE_F5:
            case KeyEvent.KEYCODE_MEDIA_PREVIOUS:
                long preTime = SystemClock.elapsedRealtime();
                if (preTime - lastClickTime > timeInterval) {
                    msg.what = MEDIA_PRE;
                    mHandler.sendMessage(msg);
                }
                lastClickTime = SystemClock.elapsedRealtime();
                break;
            default:
                break;
        }
    }

    private void singleClick() {
        boolean isPlaying = PlayerManagerHelper.getInstance().isPlaying();
        if (isPlaying) {
            ReportUtil.addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_PAUSE, PlayerUiControlReportEvent.CONTROL_TYPE_HARD_WARE, null);
        } else {
            ReportUtil.addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_PLAY, PlayerUiControlReportEvent.CONTROL_TYPE_HARD_WARE, null);
        }
        PlayerManagerHelper.getInstance().switchPlayerStatus(true);
    }

    private void doublePress() {
        ReportUtil.addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_NEXT, PlayerUiControlReportEvent.CONTROL_TYPE_HARD_WARE, null);
        ReportUtil.reportEndPlay(ReportConstants.PLAY_CHANGE_BY_HANDWARE_CONTROL, true);
        PlayerManager.getInstance().playNext();
    }

    private void trapPress() {
        ReportUtil.addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_PREVIOUS, PlayerUiControlReportEvent.CONTROL_TYPE_HARD_WARE, null);
        ReportUtil.reportEndPlay(ReportConstants.PLAY_CHANGE_BY_HANDWARE_CONTROL, true);
        PlayerManager.getInstance().playPre();
    }

    private void checkForMultiClick() {
        // 有长按时间发生，则不处理单击、双击事件
        if (mCountDownTimer == null) {
            mCountDownTimer = new CountDownTimer(900, 900) {
                @Override
                public void onTick(long millisUntilFinished) {
                }

                @Override
                public void onFinish() {
                    mCountDownTimer.cancel();
                    bFinish = true;
                    if (mClickIndex < 3) {
                        switch (mClickIndex) {
                            case 0:
                                singleClick();
                                break;
                            case 1:
                                doublePress();
                                break;
                            case 2:
                                trapPress();
                                break;
                            default:
                                break;
                        }
                    } else {
                        trapPress();
                    }
                    mClickIndex = -1;
                }
            };
        }
        if (bFinish) {
            bFinish = false;
            mCountDownTimer.start();
        }
        ++mClickIndex;
    }
}
