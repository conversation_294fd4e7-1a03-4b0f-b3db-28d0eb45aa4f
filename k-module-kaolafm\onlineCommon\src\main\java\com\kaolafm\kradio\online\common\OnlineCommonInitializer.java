package com.kaolafm.kradio.online.common;

import android.app.Application;
import android.util.Log;

import com.alibaba.android.arouter.launcher.ARouter;
import com.kaolafm.base.utils.SpUtil;
import com.kaolafm.flavor.AccountInterworkInter;
import com.kaolafm.kradio.common.KradioSDKManager;
import com.kaolafm.kradio.common.http.vehicle.KlSdkVehicle;
import com.kaolafm.kradio.common.report.ReportUtil;
import com.kaolafm.kradio.common.utils.OnlineConstants;
import com.kaolafm.kradio.k_kaolafm.BuildConfig;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.DeviceInfoSettingASync;
import com.kaolafm.kradio.lib.base.flavor.NetPingOptions;
import com.kaolafm.kradio.lib.init.AppInit;
import com.kaolafm.kradio.lib.init.BaseAppInitializer;
import com.kaolafm.kradio.lib.utils.AppUtil;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkManager;
import com.kaolafm.kradio.scene.launcher.InitService;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.report.ReportHelper;

import static com.kaolafm.kradio.common.utils.OnlineConstants.TRAVEL_SERVICE_DEFAULT_SWITCH;
import static com.kaolafm.kradio.common.utils.OnlineConstants.VOICE_ASSISTANT;
import static com.kaolafm.kradio.common.utils.OnlineConstants.VOICE_ASSISTANT_DEFAULT_SWITCH;
import static com.kaolafm.kradio.common.utils.OnlineConstants.VOICE_SPEAK_DEFAULT_SWITCH;

@AppInit(description = "电台聚合通用初始化",priority = 51)
public final class OnlineCommonInitializer extends BaseAppInitializer {


    @Override
    public void onCreate(Application application) {
        Log.i(Constants.START_TAG, "CommonInitializer: onCreate");

        ReportHelper.getInstance().setProductId(BuildConfig.PRODUCT_ID + "");
        ReportHelper.getInstance().setAppMode(BuildConfig.APP_MODE + "");
        ReportHelper.getInstance().setCarType(KlSdkVehicle.getInstance().getCarType());
        ReportUtil.report(ReportManager.getInstance());
        ReportHelper.getInstance().initByApk(); //使用apk自己上报v
        InitService.start(application);
        if (BuildConfig.DEBUG) {
            // 这两行必须写在init之前，否则这些配置在init过程中将无效
            ARouter.openLog();     // 打印日志
            ARouter.openDebug();   // 开启调试模式(如果在InstantRun模式下运行，必须开启调试模式！线上版本需要关闭,否则有安全风险)
        }
        ARouter.init(application);

        //  应用启动，账号打通相关初始化
        AccountInterworkInter mAccountInterworkInter = ClazzImplUtil.getInter("AccountInterworkImpl");
        if (mAccountInterworkInter != null && mAccountInterworkInter.isOpenThirdPartyAccount()) {
            mAccountInterworkInter.init();
        }
//        DeviceInfoSettingASync deviceInfoSettingASync = ClazzImplUtil.getInter("DeviceInfoSettingASyncImpl");
        //有异步获取did的车机，这里不初始化sdk，防止生成默认的did激活，
//        if (deviceInfoSettingASync == null) {
//            KradioSDKManager.getInstance().initAndActivate();
//        }
//        PlayerManager.getInstance().init(mApplication);
        SpUtil.init(application.getApplicationContext());
        initMessageSwitchState();

    }

    /**
     * 初始化消息按钮状态
     */
    private void initMessageSwitchState() {
        int voiceSpeakState = SpUtil.getInt(OnlineConstants.VOICE_SPEAK, 0);
        int voiceAssistantState = SpUtil.getInt(OnlineConstants.VOICE_ASSISTANT, 0);
        int travelServiceState = SpUtil.getInt(OnlineConstants.TRAVEL_SERVICE, 0);
        if (voiceSpeakState == 0) {
            SpUtil.putInt(OnlineConstants.VOICE_SPEAK, VOICE_SPEAK_DEFAULT_SWITCH);
        }
        if (voiceAssistantState == 0) {
            SpUtil.putInt(VOICE_ASSISTANT, VOICE_ASSISTANT_DEFAULT_SWITCH);
        }
        if (travelServiceState == 0) {
            SpUtil.putInt(OnlineConstants.TRAVEL_SERVICE, TRAVEL_SERVICE_DEFAULT_SWITCH);
        }
    }

}
