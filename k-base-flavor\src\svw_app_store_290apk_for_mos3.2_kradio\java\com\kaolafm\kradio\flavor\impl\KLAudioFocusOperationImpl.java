package com.kaolafm.kradio.flavor.impl;

import android.annotation.SuppressLint;
import android.media.AudioManager;
import android.util.Log;

import com.kaolafm.opensdk.player.core.listener.KLAudioFocusOperationListener;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-12-26 12:25
 ******************************************/
public class KLAudioFocusOperationImpl implements KLAudioFocusOperationListener {
    private static final String TAG = "KLAudioFocusOperationImpl";

    @SuppressLint("LongLogTag")
    @Override
    public boolean beforeRequestAudioFocus(Object... objects) {
        AudioManager audioManager = (AudioManager) objects[0];
        if (audioManager != null) {
            Log.i(TAG, "beforeRequestAudioFocus start");
            audioManager.setParameters("jpcc.music=unmute");
            return true;
        }
        Log.i(TAG, "beforeRequestAudioFocus end");
        return false;
    }
}
