package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.util.Log;

import androidx.car.util.CarUxRestrictionsHelper;
import androidx.car.uxrestrictions.CarUxRestrictions;
import androidx.car.uxrestrictions.OnUxRestrictionsChangedListener;

import com.kaolafm.kradio.lib.base.flavor.KRadioVehicleSafetyCallback;
import com.kaolafm.kradio.lib.base.flavor.KRadioVehicleSafetyInter;

import java.util.concurrent.CopyOnWriteArrayList;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-01-17 20:27
 ******************************************/
public class KRadioVehicleSafetyImpl implements KRadioVehicleSafetyInter {
    private static final String TAG = "KRadioVehicleSafetyImpl";

    private CarUxRestrictionsHelper mCarUxRestrictionsHelper;

    private CopyOnWriteArrayList<KRadioVehicleSafetyCallback> mKRadioVehicleSafetyCallbacks;

    /**
     * 长字符串权限是否被允许 true 是，false 否
     */
    private boolean isStringLengthGranted = true;
    /**
     * 是否可显示字符串检测结果 true为是，false为否
     */
    private boolean canShowStringLengthCheckInfo = true;

    /**
     * 二维码权限是否被允许 true 是，false 否
     */
    private boolean isQRCodeGranted = true;
    /**
     * 是否可显示二维码检测结果 true为是，false为否
     */
    private boolean canShowQRCodeCheckInfo = true;


    @Override
    public boolean startCheck(Object... args) {
        Log.i(TAG, "startCheck");
        if (mCarUxRestrictionsHelper == null) {
            try {
                mCarUxRestrictionsHelper = new CarUxRestrictionsHelper((Context) args[0], mUxRestrictionsChangedListener);
            } catch (NoClassDefFoundError noClassDefFoundError) {
                return false;
            }
        }
        mCarUxRestrictionsHelper.start();
        return true;
    }

    @Override
    public boolean stopCheck(Object... args) {
        Log.i(TAG, "stopCheck");
        if (mCarUxRestrictionsHelper != null) {
            mCarUxRestrictionsHelper.stop();
            return true;
        }
        return false;
    }

    @Override
    public boolean resumeCheck(Object... args) {
        Log.i(TAG, "resumeCheck----->isStringLengthGranted = " + isStringLengthGranted + "--isQRCodeGranted = " + isQRCodeGranted);
        if (args == null || args.length == 0) {
            notifyCallback(null);
        } else {
            Object obj = args[0];
            if (obj instanceof KRadioVehicleSafetyCallback) {
                notifyCallback((KRadioVehicleSafetyCallback) obj);
            }
        }
        return isStringLengthGranted && isQRCodeGranted;
    }

    @Override
    public void registerVehicleSafetyCheckCallback(KRadioVehicleSafetyCallback kRadioVehicleSafetyCallback) {
        if (mKRadioVehicleSafetyCallbacks == null) {
            mKRadioVehicleSafetyCallbacks = new CopyOnWriteArrayList<>();
        }
        if (mKRadioVehicleSafetyCallbacks.contains(kRadioVehicleSafetyCallback)) {
            return;
        }
        mKRadioVehicleSafetyCallbacks.add(kRadioVehicleSafetyCallback);
    }

    @Override
    public void unregisterVehicleSafetyCheckCallback(KRadioVehicleSafetyCallback kRadioVehicleSafetyCallback) {
        if (mKRadioVehicleSafetyCallbacks == null) {
            return;
        }
        if (mKRadioVehicleSafetyCallbacks.contains(kRadioVehicleSafetyCallback)) {
            mKRadioVehicleSafetyCallbacks.remove(kRadioVehicleSafetyCallback);
        }
    }

    private void notifyCallback(KRadioVehicleSafetyCallback kRadioVehicleSafetyCallback) {
        if (mKRadioVehicleSafetyCallbacks == null) {
            return;
        }

        if (kRadioVehicleSafetyCallback != null) {
            kRadioVehicleSafetyCallback.checkImage(isQRCodeGranted, canShowQRCodeCheckInfo);
            kRadioVehicleSafetyCallback.checkLongText(isStringLengthGranted, canShowStringLengthCheckInfo);
            return;
        }
        int size = mKRadioVehicleSafetyCallbacks.size();
        for (int i = 0; i < size; i++) {
            KRadioVehicleSafetyCallback tempKRadioVehicleSafetyCallback = mKRadioVehicleSafetyCallbacks.get(i);
            if (tempKRadioVehicleSafetyCallback == null) {
                continue;
            }
            tempKRadioVehicleSafetyCallback.checkImage(isQRCodeGranted, canShowQRCodeCheckInfo);
            tempKRadioVehicleSafetyCallback.checkLongText(isStringLengthGranted, canShowStringLengthCheckInfo);
        }
    }

    private OnUxRestrictionsChangedListener mUxRestrictionsChangedListener = new OnUxRestrictionsChangedListener() {
        @Override
        public void onUxRestrictionsChanged(CarUxRestrictions restrictionInfo) {
            boolean isDistractionOptimizationRequired = restrictionInfo.isDistractionOptimizationRequired();
//            isGranted = !isDistractionOptimizationRequired;
            // 驾驶模式开启
            if (isDistractionOptimizationRequired) {
                int activeRestrictions = restrictionInfo.getActiveRestrictions();

                // true长文本被禁用，false长文本被启用
                boolean stringLengthLimit = CarUxRestrictions.UX_RESTRICTIONS_LIMIT_STRING_LENGTH ==
                        (activeRestrictions & CarUxRestrictions.UX_RESTRICTIONS_LIMIT_STRING_LENGTH);

                // true二维码被禁用，false二维码被启用
                boolean qrCodeLimit = CarUxRestrictions.UX_RESTRICTIONS_NO_VIDEO ==
                        (activeRestrictions & CarUxRestrictions.UX_RESTRICTIONS_NO_VIDEO);

                if (stringLengthLimit) {
                    if (isStringLengthGranted) {
                        canShowStringLengthCheckInfo = true;
                    }
                    isStringLengthGranted = false;
                } else {
                    isStringLengthGranted = true;
                }

                if (qrCodeLimit) {
                    if (isQRCodeGranted) {
                        canShowQRCodeCheckInfo = true;
                    }
                    isQRCodeGranted = false;
                } else {
                    isQRCodeGranted = true;
                }
                Log.i(TAG, "onUxRestrictionsChanged----->isStringLengthGranted = " + isStringLengthGranted +
                        "--isQRCodeGranted = " + isQRCodeGranted +
                        "--activeRestrictions = " + activeRestrictions +
                        "--isDistractionOptimizationRequired = true");
            }
            // 驾驶模式关闭
            else {
                isStringLengthGranted = true;
                canShowStringLengthCheckInfo = false;

                isQRCodeGranted = true;
                canShowQRCodeCheckInfo = false;
                Log.i(TAG, "onUxRestrictionsChanged----->isDistractionOptimizationRequired = false");
            }
            notifyCallback(null);
        }
    };
}