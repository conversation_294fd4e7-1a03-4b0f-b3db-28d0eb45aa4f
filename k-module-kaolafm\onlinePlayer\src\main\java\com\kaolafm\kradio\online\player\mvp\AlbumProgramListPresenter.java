package com.kaolafm.kradio.online.player.mvp;

import android.util.Log;

import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.trello.rxlifecycle3.android.FragmentEvent;

import java.util.List;

public class AlbumProgramListPresenter extends BasePresenter<AlbumProgramListModel, AlbumProgramListView> {
    public AlbumProgramListPresenter(AlbumProgramListView view) {
        super(view);
    }

    @Override
    protected AlbumProgramListModel createModel() {
        return new AlbumProgramListModel(((BaseFragment) mView).bindUntilEvent(FragmentEvent.DESTROY_VIEW));
    }


    /**
     * 获取专辑播单数据
     *
     * @param bean
     */
    public void getAlbumProgramList(AlbumProgramRequestBean bean) {
        Log.i("getAlbumProgramList", "bean = " + bean);
        mModel.getAlbumProgramList(bean.getAlbumId(), bean.getSort(), bean.getPageSize(), bean.getPageNum(), new HttpCallback<BasePageResult<List<AudioDetails>>>() {
            @Override
            public void onSuccess(BasePageResult<List<AudioDetails>> listBasePageResult) {
                onGetProgramListDataSuccess(listBasePageResult);
            }

            @Override
            public void onError(ApiException e) {
                onGetProgramListDataError(e.getCode(), e.getMessage());
            }
        });
    }

    /**
     * 获取专辑播单数据
     *
     * @param bean
     */
    public void getAudioPageAlbumProgramList(AlbumProgramRequestBean bean) {
        Log.i("getAlbumProgramList", "bean = " + bean);
        mModel.getAudioPageAlbumProgramList(bean.getAlbumId(), bean.getAudioId(), bean.getSort(), bean.getPageSize(), new HttpCallback<BasePageResult<List<AudioDetails>>>() {
            @Override
            public void onSuccess(BasePageResult<List<AudioDetails>> listBasePageResult) {
                onGetProgramListDataSuccess(listBasePageResult);
            }

            @Override
            public void onError(ApiException e) {
                onGetProgramListDataError(e.getCode(), e.getMessage());
            }
        });
    }

    private void onGetProgramListDataSuccess(BasePageResult<List<AudioDetails>> listBasePageResult) {
        if (mView == null) return;
        mView.onGetAlbumProgramListDataSuccess(listBasePageResult);
    }

    private void onGetProgramListDataError(int code, String message) {
        if (mView == null) return;
        mView.onGetAlbumProgramListDataError(code, message);
    }
}
