package com.kaolafm.kradio.lib.utils;

/**
 * <AUTHOR> on 2019-06-27.
 */

public class ClazzImplUtil {
    public static final String CLASS_FATHER_PACKAGE = "com.kaolafm.kradio.flavor.impl.";

    public static <T> T getInter(String className) {
        if (StringUtil.isEmpty(className)) {
            return null;
        }
        return ClazzUtil.getClazzInstance(StringUtil.join(CLASS_FATHER_PACKAGE, className));
    }
    public static <T> T getInterSingleInstance(String className) {
        if (StringUtil.isEmpty(className)) {
            return null;
        }
        return ClazzUtil.getClazzSingleInstance(StringUtil.join(CLASS_FATHER_PACKAGE, className));
    }
}
