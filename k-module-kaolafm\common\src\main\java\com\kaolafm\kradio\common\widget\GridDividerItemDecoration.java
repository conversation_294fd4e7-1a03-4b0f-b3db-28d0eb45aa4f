package com.kaolafm.kradio.common.widget;

import android.graphics.Rect;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;
import android.view.View;

public class GridDividerItemDecoration extends RecyclerView.ItemDecoration {

    private float mHorizontalDivider;

    /**
     * 最后一行的地步是否需要偏移，默认true，偏移。
     */
    private boolean mOffetBottom = true;

    private float mVerticalDivider;

    /**
     * 最后一行一列是否也偏移，true表示偏移，false表示不偏移。默认true
     */
    private boolean lastOffset = true;

    public GridDividerItemDecoration() {

    }

    public GridDividerItemDecoration(float horizontalDivider, float verticalDivider) {
        mHorizontalDivider = horizontalDivider;
        mVerticalDivider = verticalDivider;
    }

    public GridDividerItemDecoration(float horizontalDivider, float verticalDivider, boolean isLastOffset) {
        mHorizontalDivider = horizontalDivider;
        mVerticalDivider = verticalDivider;
        this.lastOffset = isLastOffset;
    }

    public float getHorizontalDivider() {
        return mHorizontalDivider;
    }

    public void setHorizontalDivider(int mHorizontalDivider) {
        this.mHorizontalDivider = mHorizontalDivider;
    }

    public float getVerticalDivider() {
        return mVerticalDivider;
    }

    public void setVerticalDivider(int mVerticalDivider) {
        this.mVerticalDivider = mVerticalDivider;
    }

    public void isOffsetBottom(boolean offetBottom) {
        mOffetBottom = offetBottom;
    }

    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
//        Log.i("GridDividerItemDecoration", "getItemOffsets: "+view.getResources().getConfiguration().orientation);
        final int spanCount = getSpanCount(parent);
        final int childCount = parent.getAdapter().getItemCount();
        final int adapterPosition = parent.getChildAdapterPosition(view);

        outRect.set(0, 0, Math.round(mHorizontalDivider), Math.round(mVerticalDivider));
        if (isLastRow(adapterPosition, spanCount, childCount) && (!lastOffset || !mOffetBottom)) {
            outRect.bottom = 0;
        }

        if (isLastColumn(adapterPosition, spanCount, childCount) && !lastOffset) {
            outRect.right = 0;
        }

    }

    private boolean isLastColumn(int position, int spanCount, int childCount) {
        return (position + 1) % spanCount == 0;
    }

    private boolean isLastRow(int position, int spanCount, int childCount) {
        int lastColumnCount = childCount % spanCount;
        lastColumnCount = lastColumnCount == 0 ? spanCount : lastColumnCount;
        return position >= childCount - lastColumnCount;
    }

    private int getSpanCount(RecyclerView parent) {
        RecyclerView.LayoutManager layoutManager = parent.getLayoutManager();

        if (layoutManager instanceof GridLayoutManager) {
            return ((GridLayoutManager) layoutManager).getSpanCount();
        } else if (layoutManager instanceof StaggeredGridLayoutManager) {
            return ((StaggeredGridLayoutManager) layoutManager).getSpanCount();
        } else {
            throw new UnsupportedOperationException("the GridDividerItemDecoration can only be used in " +
                    "the RecyclerView which use a GridLayoutManager or StaggeredGridLayoutManager");
        }
    }
}