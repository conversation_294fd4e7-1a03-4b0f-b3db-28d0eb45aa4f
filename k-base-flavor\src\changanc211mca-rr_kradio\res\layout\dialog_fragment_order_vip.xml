<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/x934"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@drawable/order_pay_bg">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="@dimen/x934"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y24"
        android:gravity="center"
        android:text="VIP购买"
        android:textStyle="bold"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size7"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_select_title"
        android:layout_height="@dimen/m41"
        android:layout_width="wrap_content"
        android:layout_marginStart="@dimen/x80"
        android:layout_marginTop="@dimen/y45"
        android:gravity="center"
        android:text="选择VIP会员套餐"
        android:textSize="@dimen/text_size4"
        android:textColor="@color/text_color_1"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        app:layout_constraintLeft_toLeftOf="@+id/tv_title"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_order_content"
        android:layout_marginTop="@dimen/m16"
        android:layout_width="@dimen/x557"
        android:layout_height="wrap_content"
        android:paddingBottom="@dimen/y30"
        android:orientation="vertical"
        app:layout_constraintTop_toBottomOf="@id/tv_select_title"
        app:layout_constraintLeft_toLeftOf="@+id/tv_select_title">

        <androidx.recyclerview.widget.RecyclerView
            android:layout_marginTop="@dimen/m30"
            android:id="@+id/rv_select"
            android:layout_width="match_parent"
            android:layout_height="@dimen/m188"
            android:paddingEnd="@dimen/x30"
            app:layout_constraintTop_toBottomOf="@id/tv_select_title"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintBottom_toTopOf="@id/ll_order_desc"
            tools:ignore="NotSibling" />

        <LinearLayout
            android:id="@+id/ll_order_desc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingTop="@dimen/y20"
            app:layout_constraintTop_toBottomOf="@id/rv_select"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintBottom_toTopOf="@id/ll_order_notice">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="开通则表示您同意"
                android:textColor="@color/text_color_1"
                android:textSize="@dimen/text_size3" />
            <TextView
                android:id="@+id/tv_agree_vip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="VIP会员服务协议"
                android:textColor="@color/order_desc_key_txt_color"
                android:textSize="@dimen/text_size3" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_order_notice"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="@dimen/y10"
            app:layout_constraintTop_toBottomOf="@id/ll_order_desc"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="注:VIP会员到期后需手动续费，云听车载不提供自动续费功能。可添加云听客服微信：yuntingkefu，咨询开发票相关事宜。"
                android:textColor="@color/text_color_2"
                android:textSize="@dimen/text_size1" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <FrameLayout
        android:id="@+id/fl_order_qrcode"
        android:layout_width="@dimen/x238"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toRightOf="@id/cl_order_content"
        app:layout_constraintTop_toTopOf="@id/cl_order_content"
        app:layout_constraintRight_toRightOf="@+id/tv_title">

        <LinearLayout
            android:id="@+id/ll_order_rmb"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center">

            <TextView
                android:id="@+id/tv_rmb_buy_notice"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/y30"
                android:gravity="center"
                android:text="购买须知"
                android:textColor="@color/order_qrcode_title_color"
                android:textSize="@dimen/text_size3" />


            <FrameLayout
                android:layout_width="@dimen/m180"
                android:layout_height="@dimen/m180"
                android:background="@drawable/order_pay_bg">

                <LinearLayout
                    android:id="@+id/ll_qrcode_failed"
                    android:layout_width="@dimen/m165"
                    android:layout_height="@dimen/m165"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:layout_gravity="center"
                    android:background="@drawable/order_qrcode_failed_bg">
                    <ImageView
                        android:id="@+id/iv_qrcode_refresh"
                        android:layout_width="@dimen/m32"
                        android:layout_height="@dimen/m32"
                        android:src="@drawable/ic_order_arcode_refresh"/>
                    <TextView
                        android:id="@+id/tv_qrcode_failed"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/text_color_1"
                        android:textSize="@dimen/text_size1"
                        android:text="请稍候"
                        android:gravity="center"/>
                </LinearLayout>

                <com.kaolafm.kradio.view.OvalImageView
                    android:id="@+id/iv_qrcode"
                    android:layout_gravity="center"
                    android:layout_width="@dimen/m165"
                    android:layout_height="@dimen/m165" />
            </FrameLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingTop="@dimen/m17"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="@string/pay_open_third_method"
                    android:textColor="@color/text_color_1"
                    android:textSize="@dimen/text_size3" />
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:visibility="gone"
            android:id="@+id/ll_order_yunbi"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="@dimen/y30"
                android:gravity="center"
                android:text="购买须知"
                android:textColor="@color/text_color_1"
                android:textSize="@dimen/text_size3" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingTop="@dimen/m17"
                android:orientation="horizontal">

                <ImageView
                    android:layout_marginLeft="@dimen/m18"
                    android:layout_marginTop="@dimen/m5"
                    android:id="@+id/iv_yunbi_notice"
                    android:layout_width="@dimen/m26"
                    android:layout_height="@dimen/m26"
                    android:background="@drawable/ic_pay_notice" />

                <TextView
                    android:id="@+id/tv_yunbi_notice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="请在手机云听APP进行云币充值"
                    android:textColor="@color/text_color_1"
                    android:textSize="@dimen/text_size3" />
            </LinearLayout>
        </LinearLayout>

    </FrameLayout>

    <TextView
        android:id="@+id/tv_count_down"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textStyle="bold"
        android:textSize="@dimen/text_size3"
        android:textColor="@color/text_color_1"
        android:layout_marginEnd="@dimen/m35"
        android:layout_marginBottom="@dimen/m36"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/cl_order_content"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>