package com.kaolafm.kradio.common.view;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewpager.widget.ViewPager;
import android.util.AttributeSet;

public class CustomViewPager extends ViewPager {
    private boolean isCanSlide;

    public CustomViewPager(@NonNull Context context) {
        super(context);
    }

    public CustomViewPager(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

//    //事件拦截
//    @Override
//    public boolean dispatchTouchEvent(MotionEvent ev) {
//        if (!isCanSlide) {//设置为false时，viewpager不能横向滑动
//            requestDisallowInterceptTouchEvent(true);//使viewpager不再通过onInterceptTouchEvent捕获触摸事件
//        }
//        return super.dispatchTouchEvent(ev);
//    }
//
//    public void setCanSlide(boolean canSlide) {
//        isCanSlide = canSlide;
//    }
//
//    public boolean isCanSlide() {
//        return isCanSlide;
//    }



}
