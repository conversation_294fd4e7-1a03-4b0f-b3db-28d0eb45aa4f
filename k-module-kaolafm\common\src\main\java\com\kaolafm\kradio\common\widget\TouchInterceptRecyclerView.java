package com.kaolafm.kradio.common.widget;

import android.content.Context;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;

import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.lib.utils.ScrollTouchHelper;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.HomeScrollReportEvent;

public class TouchInterceptRecyclerView extends RecyclerView {

    private static final String TAG = "TCRecyclerView";
    private ScrollTouchHelper.TouchEvent touchEvent;


    public TouchInterceptRecyclerView(Context context) {
        this(context, null);
    }

    public TouchInterceptRecyclerView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public TouchInterceptRecyclerView(Context context, @Nullable AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        ScrollTouchHelper touchHelper = new ScrollTouchHelper();
        touchEvent = touchHelper.new TouchEvent();
        touchHelper.setEvent(touchEvent);
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent event) {
        // 处理拦截事件，此处会把事件向下分发，如果子view对事件进行了消费，那么此处将不会收到move和up事件
        int sw = ScreenUtil.getScreenWidth();
        int sh = ScreenUtil.getScreenHeight();
        touchEvent.setScreenWidth(sw);
        touchEvent.setScreenHeight(sh);
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                float rawX = event.getRawX();
                float rawY = event.getRawY();
                Log.i(TAG, "ACTION_DOWN: (" + rawX + "," + rawY + ")  / ( " + sw + " ," + sh + ")");
                touchEvent.setDownX((int) rawX);
                touchEvent.setDownY((int) rawY);
                break;
        }
        return super.onInterceptTouchEvent(event);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        // 处理触摸事件，如果子view不对event进行消费，事件会由回传会父控件，走up事件
        int sw = ScreenUtil.getScreenWidth();
        int sh = ScreenUtil.getScreenHeight();
        switch (event.getAction()) {
            case MotionEvent.ACTION_UP:
                float rawX = event.getRawX();
                float rawY = event.getRawY();
                Log.i(TAG, "ACTION_UP: (" + rawX + "," + rawY + ")  / ( " + sw + " ," + sh + ")");
                touchEvent.setUpX((int) rawX);
                touchEvent.setUpY((int) rawY);
                //至此，一个上报事件产生
                HomeScrollReportEvent homeScrollReportEvent = new HomeScrollReportEvent();
                homeScrollReportEvent.setDownPointer(touchEvent.getDownX(),touchEvent.getDownY());
                homeScrollReportEvent.setUpPointer(touchEvent.getUpX(),touchEvent.getUpY());
                homeScrollReportEvent.setScreenSize(touchEvent.getScreenWidth(),touchEvent.getScreenHeight());
                Log.i(TAG, "generate HomeScrollReportEvent:" + homeScrollReportEvent.toString());
                ReportHelper.getInstance().addEvent(homeScrollReportEvent);
                break;
        }
        return super.onTouchEvent(event);
    }

}
