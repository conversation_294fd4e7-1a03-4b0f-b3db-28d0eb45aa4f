package com.kaolafm.ads.image;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.kaolafm.ad.Advertisement;
import com.kaolafm.ad.api.model.ImageAdvert;
import com.kaolafm.ads.image.SplashAdActivity;
import com.kaolafm.ads.image.SplashAdHelper;

import java.lang.ref.WeakReference;

public class SplashAdView {

//    private Context mContext;
    private WeakReference<Context> mWeakReference;

    public SplashAdView(Context context){
        mWeakReference = new WeakReference<>(context);
    }

    public void loadAd(ImageAdvert advert){
        if (advert.getExposeDuration() <= 0){
            startActivity(SplashAdHelper.getTargetActivity(), advert, true);
            return;
        }
        startActivity(SplashAdActivity.class, advert, false);
    }

    private void startActivity(Class<?> targetActivity, ImageAdvert advert, boolean isAnim){
        Context mContext = (Context) mWeakReference.get();
        if(mContext!=null){
            Intent intent = new Intent(mContext, targetActivity);
            intent.putExtra("advert", advert);
            mContext.startActivity(intent);
            if(!isAnim) {
                ((Activity) mContext).overridePendingTransition(0, 0);
            }
            ((Activity)mContext).finish();
        }
    }

    public void hide(){
        LocalBroadcastManager localBroadcastManager = LocalBroadcastManager.getInstance(Advertisement.getApplication());
        localBroadcastManager.sendBroadcast(new Intent(SplashAdActivity.CLOSE_SPLASH_AD));
    }
}
