<?xml version="1.0" encoding="utf-8"?>

<com.kaolafm.kradio.component.ui.base.view.ScaleConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/bg"
    android:layout_width="@dimen/skin_adapter_item_size"
    android:layout_height="@dimen/skin_adapter_item_size"
    android:background="@drawable/skin_adapter_bg">

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textColor="@color/skin_adapter_item_title_text_color"
        android:textSize="@dimen/text_size4"
        app:layout_constraintBottom_toTopOf="@+id/sub_title"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="标准音质" />

    <TextView
        android:id="@+id/sub_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y7"
        android:gravity="center"
        android:textColor="@color/skin_adapter_item_subtitle_text_color"
        android:textSize="@dimen/text_size2"
        app:layout_constraintBottom_toTopOf="@+id/sub_sub_title"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title"
        tools:text="音质效果佳" />

    <TextView
        android:id="@+id/sub_sub_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/skin_adapter_item_subtitle_text_color"
        android:textSize="@dimen/text_size2"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/sub_title"
        tools:text="约20-30M/小时" />

    <ImageView
        android:id="@+id/iv_check"
        android:layout_width="@dimen/x32"
        android:layout_height="@dimen/y32"
        android:layout_marginTop="@dimen/m12"
        android:layout_marginRight="@dimen/m12"
        android:src="@drawable/selector_dialog_item_choose"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</com.kaolafm.kradio.component.ui.base.view.ScaleConstraintLayout>