<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="AppThemeCompat" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowCloseOnTouchOutside">false</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowBackground">@drawable/background_splash</item>
    </style>

    <style name="AppThemeCompat.splash" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowCloseOnTouchOutside">false</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowBackground">@drawable/background_splash</item>
    </style>

</resources>