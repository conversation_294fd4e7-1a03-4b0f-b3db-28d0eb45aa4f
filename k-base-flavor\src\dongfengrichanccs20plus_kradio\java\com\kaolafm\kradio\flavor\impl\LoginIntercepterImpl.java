package com.kaolafm.kradio.flavor.impl;


import android.content.Context;
import android.view.View;

import com.kaolafm.kradio.flavor.view.AccountLoginView;
import com.kaolafm.kradio.lib.base.flavor.LoginIntercepter;
import com.kaolafm.kradio.lib.utils.Logger;

/**
 * 使用二维码登录前拦截
 *
 * <AUTHOR>
 **/
public class LoginIntercepterImpl implements LoginIntercepter {

    private static final String TAG = "k.login.lii";

    /**
     * 是否拦截
     *
     * @param args
     * @return
     */
    @Override
    public boolean intercept(Object... args) {
        if (args != null) {
            boolean loginStatus = RiChanHelper.getInstance((Context) args[0]).getLoginStatus();
            if (!loginStatus) {
                //如果没有登录,则表示拦截,并显示登录页面;
                Logger.i(TAG, "未登录,[拦截],显示日产个人中心登录页面.");
                return true;
            } else {
                //如果登录,则不拦截;
                Logger.i(TAG, "已登录,[不]拦截.");
                return false;
            }
        }
        return false;
    }

    @Override
    public View getInterceptView(Object... args) {
        return new AccountLoginView((Context) args[0]);
    }
}
