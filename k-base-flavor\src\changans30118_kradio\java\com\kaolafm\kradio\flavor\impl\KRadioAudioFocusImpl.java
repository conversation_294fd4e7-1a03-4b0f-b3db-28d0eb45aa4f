package com.kaolafm.kradio.flavor.impl;


import android.util.Log;


import com.kaolafm.kradio.lib.base.flavor.KRadioAudioFocusInter;
import com.kaolafm.opensdk.player.logic.PlayerManager;


/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-06-21 10:54
 ******************************************/
public final class KRadioAudioFocusImpl implements KRadioAudioFocusInter {
    private static final String TAG = "KRadioAudioFocusImpl";
    private boolean isPlaying = false;

    @Override
    public boolean requestAudioFocus(Object... args) {
//        boolean requestSource = SourceManager.getInstance().requestSource(SourceConstantsDef.SourceID.APP);
        if (!isPlaying) {
            isPlaying = PlayerManager.getInstance().isPlaying();
        }
        PlayerManager.getInstance().requestAudioFocus();
        Log.i(TAG, "requestAudioFocus: isPlaying = " + isPlaying);
        if (isPlaying) {
            PlayerManager.getInstance().pause(false);
        }
        return true;
    }

    @Override
    public boolean abandonAudioFocus(Object... args) {
//        boolean releaseSource = SourceManager.getInstance().releaseSource(SourceConstantsDef.SourceID.APP, false);
        Log.i(TAG, "abandonAudioFocus: isPlaying = " + isPlaying);
        if (isPlaying) {
            PlayerManager.getInstance().play(false);
            isPlaying = false;
        }
        return true;
    }

}