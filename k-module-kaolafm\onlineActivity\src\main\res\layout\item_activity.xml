<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/item_root"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <com.kaolafm.kradio.component.ui.base.view.OvalImageView
        android:id="@+id/item_activitys_bg_iv"
        android:layout_width="@dimen/m360"
        android:layout_height="@dimen/m360"
        android:src="@drawable/item_activitys_bg_pic"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="MissingConstraints" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="@dimen/m360"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true">

        <RelativeLayout
            android:id="@+id/pic_rl"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="@dimen/m122"
                android:layout_height="@dimen/m122"
                android:src="@drawable/online_activity_item_qr_bg"  />
            <ImageView
                android:layout_centerInParent="true"
                android:id="@+id/qrCode_image"
                android:layout_width="@dimen/m102"
                android:layout_height="@dimen/m102"
                tools:background="@color/blue01"
                tools:ignore="MissingConstraints" />

        </RelativeLayout>

        <com.kaolafm.kradio.component.ui.base.view.KradioTextView
            android:id="@+id/title_activity"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/y10"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:paddingLeft="@dimen/x5"
            android:paddingRight="@dimen/x5"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/m38"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/pic_rl"
            tools:ignore="MissingConstraints"
            tools:text="活动标题活动标题活动标题" />

        <TextView
            android:layout_marginTop="@dimen/y12"
            android:id="@+id/des_activity"
            android:layout_width="wrap_content"
            android:paddingLeft="@dimen/x60"
            android:paddingRight="@dimen/x60"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="2"
            android:gravity="center"
            android:textColor="#D3EDFF"
            android:textSize="@dimen/m20"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title_activity"
            tools:text="新年活动新年活动新年活动新年活动新年活动新年活动新年活动新年活动新年活动新年活动新年活动" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/qr_view_expire"
        android:layout_width="@dimen/m360"
        android:layout_height="@dimen/m360"
        android:background="@drawable/item_expire_bg"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="MissingConstraints" />

    <TextView
        android:id="@+id/qr_expire_icon"
        android:layout_width="@dimen/m240"
        android:layout_height="@dimen/m72"
        android:visibility="visible"
        android:layout_centerHorizontal="true"
        android:layout_alignBottom="@+id/item_activitys_bg_iv"
        android:layout_marginBottom="@dimen/m94"
        android:background="@drawable/component_activity_end_text_bg"
        android:textColor="@color/colorWhite"
        android:textSize="@dimen/m28"
        android:text="活动已结束"
        android:gravity="center"
        tools:ignore="MissingConstraints" />
</RelativeLayout>