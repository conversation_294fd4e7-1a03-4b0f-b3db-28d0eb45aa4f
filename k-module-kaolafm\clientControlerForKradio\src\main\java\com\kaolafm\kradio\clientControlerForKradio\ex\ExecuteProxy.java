package com.kaolafm.kradio.clientControlerForKradio.ex;

import android.content.Context;
import android.os.RemoteException;

import com.google.gson.Gson;
import com.kaolafm.sdk.core.IExecuteResult;
import com.kaolafm.sdk.core.Versions;
import com.kaolafm.sdk.core.cmd.Command;

import java.lang.ref.WeakReference;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: ExecuteProxy.java                                               
 *                                                                  *
 * Created in 2018/8/2 下午5:31                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
public class ExecuteProxy {
    private final WeakReference<Context> refContext;
    private Gson gson;
    private Executor executor = null;

    public ExecuteProxy(Context context) {
        this.refContext = new WeakReference<Context>(context);
        gson = new Gson();
    }

    public String execute(String s) throws RemoteException {
        Command cmd = gson.fromJson(s, Command.class);
        String method = cmd.getMethod();
        String[] param = cmd.getParam();
        int sdkVersion = cmd.getSdkVersion();

        Executor executor = dispatch(method, sdkVersion);
        if (executor != null) {
            return executor.execute(method, param);
        }

        return null;
    }

    public void execute(String s, IExecuteResult executeResult) throws RemoteException {
        Command cmd = gson.fromJson(s, Command.class);
        String method = cmd.getMethod();
        String[] param = cmd.getParam();
        int sdkVersion = cmd.getSdkVersion();

        Executor executor = dispatch(method, sdkVersion);
        if (executor != null) {
            executor.execute(method, param, executeResult);
        }
    }

    private Executor dispatch(String method, int sdkVersion) {
        if (sdkVersion >= Versions.VERSION_CODE_132) {
            if (executor == null) {
                executor = new Executor132(refContext.get());
            }
        }
        return executor;
    }


}
