package com.kaolafm.kradio.lib.bean;

/******************************************
 * 类描述： 订阅详情对象 类名称：SubscribeData
 *
 * @version: 1.0
 * @author: shaoning<PERSON>ang
 * @time: 2016-7-22 14:57
 ******************************************/
public class SubscribeData {
    /**
     * 专辑，PGC，传统广播电台ID
     */
    private long id;
    /**
     * 专辑，PGC，传统广播电台名称
     */
    private String name;
    /**
     * 专辑，PGC，传统广播电台 类型
     */
    private int type;
    /**
     * 专辑，PGC，传统广播电台图片URL
     */
    private String img;
    /**
     * 更新时间 时间戳形式
     */
    private String updateTime;
    /**
     * 是否已经上线 （1是，0否）
     */
    private int isOnline;
    /**
     * 是否有版权 （1是，0否）
     */
    private int hasCopyright;
    private String desc;

    /**
     * 订阅的位置, 播放条, 播放器...
     */
    private String location;
    /**
     * 更新数
     */
    private int updateNum;

    public int getUpdateNum() {
        return updateNum;
    }

    public void setUpdateNum(int updateNum) {
        this.updateNum = updateNum;
    }

    /**
     * 收藏专辑总共期数
     */
    private int countNum;

    /**
     * 是否需要付费
     */
    private int fine;

    /**
     * 专辑是否vip
     */
    private int vip;
    private int playCount;
    private String albumName;
    private String freq;
    private long subscribeCount;
    private String currentProgram;//广播/听电视当前正在播放节目名
    public SubscribeData() {
    }

    public String getCurrentProgram() {
        return currentProgram;
    }

    public void setCurrentProgram(String currentProgram) {
        this.currentProgram = currentProgram;
    }

    public long getSubscribeCount() {
        return subscribeCount;
    }

    public void setSubscribeCount(long subscribeCount) {
        this.subscribeCount = subscribeCount;
    }

    public String getFreq() {
        return freq;
    }

    public void setFreq(String freq) {
        this.freq = freq;
    }

    public int getPlayCount() {
        return playCount;
    }

    public void setPlayCount(int playCount) {
        this.playCount = playCount;
    }

    public String getAlbumName() {
        return albumName;
    }

    public void setAlbumName(String albumName) {
        this.albumName = albumName;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public int getIsOnline() {
        return isOnline;
    }

    public void setIsOnline(int isOnline) {
        this.isOnline = isOnline;
    }

    public int getHasCopyright() {
        return hasCopyright;
    }

    public void setHasCopyright(int hasCopyright) {
        this.hasCopyright = hasCopyright;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public int getCountNum() {
        return countNum;
    }

    public void setCountNum(int countNum) {
        this.countNum = countNum;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public int getFine() {
        return fine;
    }

    public void setFine(int fine) {
        this.fine = fine;
    }

    public int getVip() {
        return vip;
    }

    public void setVip(int vip) {
        this.vip = vip;
    }

    @Override
    public String toString() {
        return "SubscribeData{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", type=" + type +
                ", img='" + img + '\'' +
                ", updateTime='" + updateTime + '\'' +
                ", isOnline=" + isOnline +
                ", hasCopyright=" + hasCopyright +
                ", desc='" + desc + '\'' +
                ", location='" + location + '\'' +
                ", updateNum=" + updateNum +
                ", countNum=" + countNum +
                ", fine=" + fine +
                ", vip=" + vip +
                ", playCount=" + playCount +
                ", albumName='" + albumName + '\'' +
                ", freq='" + freq + '\'' +
                ", subscribeCount=" + subscribeCount +
                '}';
    }
}
