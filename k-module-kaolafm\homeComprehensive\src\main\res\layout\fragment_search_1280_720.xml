<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/search_main_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_home"
    android:clickable="false">

    <View
        android:id="@+id/search_top_guideline"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/y40"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cl_search_view" />

    <ImageView
        style="@style/ComprehensiveFragmentBackButton"
        android:layout_marginStart="@dimen/m48"
        android:layout_marginTop="0dp"
        app:layout_constraintBottom_toBottomOf="@id/cl_search_view"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/cl_search_view" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_search_view"
        android:layout_width="@dimen/x874"
        android:layout_height="@dimen/comprehensive_search_edit_height"
        android:layout_marginStart="@dimen/m136"
        android:layout_marginTop="@dimen/m50"
        android:background="@drawable/bg_search_edit_text"
        app:layout_constraintEnd_toStartOf="@id/tv_search"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="NotSibling">

        <com.kaolafm.kradio.search.TypeSpinner
            android:id="@+id/ts_search_type"
            android:layout_width="@dimen/m156"
            android:layout_height="match_parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.kaolafm.kradio.common.widget.CursorSkinEditText
            android:id="@+id/et_search_word"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/m186"
            android:background="@null"
            android:gravity="center_vertical"
            android:hint="@string/search_hint"
            android:imeOptions="actionSearch|flagNoExtractUi"
            android:paddingStart="0dp"
            android:paddingEnd="@dimen/x10"
            android:singleLine="true"
            android:textColor="@color/search_edit_text_color"
            android:textColorHint="@color/comprehensive_search_text_hint_color"
            android:textCursorDrawable="@drawable/cursor_drawable"
            android:textSize="@dimen/text_size4"
            app:cset_cursorDrawable="@drawable/cursor_drawable"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/iv_search_word_delete"
            app:layout_constraintTop_toTopOf="parent" />
        <!--            android:textCursorDrawable="@drawable/cursor_drawable"-->


        <ImageView
            android:id="@+id/iv_search_word_delete"
            android:layout_width="@dimen/m36"
            android:layout_height="@dimen/m36"
            android:layout_marginEnd="@dimen/m30"
            android:background="@drawable/ic_search_cancel"
            android:contentDescription="@string/content_desc_clear"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="W,1:1"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_search"
        android:layout_width="@dimen/m160"
        android:layout_height="@dimen/m60"
        android:layout_marginStart="@dimen/m30"
        android:background="@drawable/comprehensive_home_search_text"
        android:gravity="center"
        android:textColor="@color/search_btn_text_color"
        android:textSize="@dimen/text_size3"
        android:contentDescription="@string/content_desc_search"
        app:layout_constraintBottom_toBottomOf="@id/cl_search_view"
        app:layout_constraintStart_toEndOf="@id/cl_search_view"
        app:layout_constraintTop_toTopOf="@id/cl_search_view" />

    <TextView
        android:id="@+id/tv_search_history"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/search_history"
        android:textColor="@color/search_history_text_color"
        android:textSize="@dimen/text_size4"
        app:layout_constraintLeft_toLeftOf="@id/cl_search_view"
        app:layout_constraintTop_toBottomOf="@id/search_top_guideline"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/tv_clear_search_history"
        android:layout_width="@dimen/m36"
        android:layout_height="@dimen/m36"
        android:layout_marginStart="@dimen/m19"
        android:contentDescription="@string/content_desc_delete"
        android:scaleType="fitXY"
        android:src="@drawable/comprehensive_search_bg_delete_icon"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="@id/tv_search_history"
        app:layout_constraintLeft_toRightOf="@id/tv_search_history"
        tools:visibility="visible" />


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_search_history_tags"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y20"
        android:overScrollMode="never"
        app:layout_constraintLeft_toLeftOf="@id/cl_search_view"
        app:layout_constraintRight_toRightOf="@id/tv_search"
        app:layout_constraintTop_toBottomOf="@id/tv_search_history"
        app:layout_goneMarginTop="0dp"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_hot_search_words"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y10"
        android:text="@string/hot_search_words"
        android:textColor="@color/search_history_text_color"
        android:textSize="@dimen/text_size4"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="@id/cl_search_view"
        app:layout_constraintTop_toBottomOf="@id/rv_search_history_tags"
        app:layout_goneMarginTop="0dp"
        tools:visibility="visible" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_hot_search_words"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/y20"
        android:overScrollMode="never"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@id/tv_hot_search_words"
        app:layout_constraintRight_toRightOf="@id/tv_search"
        app:layout_constraintTop_toBottomOf="@id/tv_hot_search_words" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_associate_list"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/m8"
        android:layout_marginBottom="@dimen/y120"
        android:background="@drawable/bg_search_popwindow"
        android:overScrollMode="never"
        android:paddingStart="@dimen/m40"
        android:paddingEnd="@dimen/m40"
        android:paddingTop="@dimen/m20"
        android:paddingBottom="@dimen/m20"
        android:scrollbarAlwaysDrawVerticalTrack="true"
        android:scrollbarThumbVertical="@drawable/comprehensive_search_list_scroll_bar_bg"
        android:scrollbarTrackVertical="@drawable/comprehensive_search_list_scroll_bar_track_bg"
        android:scrollbars="vertical"
        android:visibility="gone"
        app:layout_constrainedHeight="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@id/cl_search_view"
        app:layout_constraintRight_toRightOf="@id/cl_search_view"
        app:layout_constraintTop_toBottomOf="@id/cl_search_view"
        app:layout_constraintVertical_bias="0"
        tools:itemCount="10"
        tools:visibility="visible" />

    <FrameLayout
        android:id="@+id/search_result_parent_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/m80"
        android:layout_marginTop="@dimen/y30"
        android:layout_marginEnd="@dimen/m80"
        android:overScrollMode="never"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cl_search_view">

        <com.kaolafm.kradio.common.widget.refresh.KradioSmartRefreshLayout
            android:id="@+id/smartRefreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.kaolafm.kradio.online.common.view.ColorSettableClassicsHeader
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:srlArrowColor="@color/comprehensive_search_srl_color"
                app:srlDrawableMarginRight="@dimen/m12"
                app:srlDrawableSize="@dimen/m40"
                app:srlEnableLastTime="false"
                app:srlProgressColor="@color/comprehensive_search_srl_color"
                app:srlTextColorTime="@color/comprehensive_search_srl_color"
                app:srlTextColorTitle="@color/comprehensive_search_srl_color"
                app:srlTextRefreshing="@string/comprehensive_hot_search_refreshing_text"
                app:srlTextSizeTitle="@dimen/m24" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_search_results"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fadingEdgeLength="@dimen/y40"
                android:overScrollMode="never"
                android:requiresFadingEdge="vertical" />

            <com.kaolafm.kradio.online.common.view.ColorSettableClassicsFooter
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:srlArrowColor="@color/comprehensive_search_srl_color"
                app:srlDrawableMarginRight="@dimen/m12"
                app:srlDrawableSize="@dimen/m40"
                app:srlProgressColor="@color/comprehensive_search_srl_color"
                app:srlTextColorTitle="@color/comprehensive_search_srl_color"
                app:srlTextLoading="@string/comprehensive_hot_search_loading_text"
                app:srlTextSizeTitle="@dimen/m24" />
        </com.kaolafm.kradio.common.widget.refresh.KradioSmartRefreshLayout>
    </FrameLayout>
    <!--    <android.support.v7.widget.RecyclerView-->
    <!--        android:id="@+id/rv_search_results"-->
    <!--        android:layout_width="0dp"-->
    <!--        android:layout_height="0dp"-->
    <!--        android:layout_marginStart="@dimen/m80"-->
    <!--        android:layout_marginEnd="@dimen/m80"-->
    <!--        android:layout_marginTop="@dimen/y43"-->
    <!--        android:layout_marginBottom="@dimen/y39"-->
    <!--        android:overScrollMode="never"-->
    <!--        android:visibility="gone"-->
    <!--        app:layout_constraintBottom_toBottomOf="parent"-->
    <!--        app:layout_constraintEnd_toEndOf="parent"-->
    <!--        app:layout_constraintStart_toStartOf="parent"-->
    <!--        app:layout_constraintTop_toBottomOf="@id/cl_search_view" />-->

    <include
        android:id="@+id/search_loading"
        layout="@layout/refresh_center"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <ViewStub
        android:id="@+id/vs_search_exception"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout="@layout/search_result_exception_layout"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ViewStub
        android:id="@+id/vs_search_network_error"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout="@layout/home_no_network_rl" />

    <!-- 隐藏控件，用于所见即可说语音执行滑动操作 -->
    <TextView
        android:id="@+id/associate_cd_up"
        style="@style/ContentDescriptionScrollUp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/rv_associate_list"
        app:layout_constraintStart_toStartOf="@id/rv_associate_list"
        app:layout_constraintTop_toTopOf="@id/rv_associate_list" />

    <TextView
        android:id="@+id/associate_cd_down"
        style="@style/ContentDescriptionScrollDown"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/rv_associate_list"
        app:layout_constraintEnd_toEndOf="@id/rv_associate_list"
        app:layout_constraintStart_toStartOf="@id/rv_associate_list" />

    <TextView
        android:id="@+id/result_cd_up"
        style="@style/ContentDescriptionScrollUp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/search_result_parent_layout" />

    <TextView
        android:id="@+id/result_cd_down"
        style="@style/ContentDescriptionScrollDown"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/search_result_parent_layout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>