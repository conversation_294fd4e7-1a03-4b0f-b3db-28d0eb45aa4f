package com.kaolafm.ad.view;

import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.os.Build;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.ad.api.model.AudioAdvert;
import com.kaolafm.ad.control.KradioAdSceneConstants;
import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.ad.implement.KradioAdAudioViewStatusImpl;
import com.kaolafm.ad.KradioAdAudioManager;
import com.kaolafm.ad.conflict.AdConflict;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;

/**
 * 音频广告父View,定义 view 的控制逻辑
 *
 * <AUTHOR>
 * @date 2020-01-13
 */
public class KradioAdAudioSuperView implements KradioAdAudioViewStatusImpl {

    private Context mContext;

    private KradioAdAudioViewStyleConfig mKradioAdAudioViewStyleConfig;

    private View mView;

    private boolean isTimerOver = false;

    private ViewGroup mViewGroup;

    private TextView mAudioAdTitleTv;
    private ViewGroup mAudioCenterView;
    private ImageView mAudioImageView;

    private ImageView mAudioAdHornImageView;

    private static final long millisecond = 1000;

    public KradioAdAudioSuperView(Context mContext, KradioAdAudioViewStyleConfig mKradioAdAudioViewStyleConfig) {
        this.mContext = mContext;
        this.mKradioAdAudioViewStyleConfig = mKradioAdAudioViewStyleConfig;
        LayoutInflater layoutInflater = (LayoutInflater)
                mContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        mView = layoutInflater.inflate(R.layout.audio_ad_suspension_layout, null);
        mAudioImageView = mView.findViewById(R.id.ad_audio_image);
        mAudioAdHornImageView = mView.findViewById(R.id.ad_audio_horn_iv);
        mAudioCenterView = mView.findViewById(R.id.ad_audio_center_view);
        mAudioAdTitleTv = mView.findViewById(R.id.ad_audio_title_tv);
        mAudioAdTitleTv.setTextSize(TypedValue.COMPLEX_UNIT_PX, ResUtil.getTextSize(R.dimen.text_size3));
        //api21以上加阴影效果。
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            mView.setElevation(3F);
        }

        mAudioCenterView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!isTimerOver) {
                    return;
                }
                AudioAdvert audioAdvert = KradioAdAudioManager.getInstance().getAudioAdvert();
                jumpAd(audioAdvert);
                cancel();
                AdConflict.removeAdvert(audioAdvert);
                AdvertisingManager.getInstance().getReporter().skip(audioAdvert);
            }
        });
    }

    private void jumpAd(AudioAdvert audioAdvert) {
        //如果是开屏广告就只关闭当前广告，因为有可能会有首页连图的二次互动。
        if (audioAdvert.getSubtype() == KradioAdSceneConstants.SUB_TYPE_SPRAND_SCREEN) {
            AdvertisingManager.getInstance().close(audioAdvert);
            if (audioAdvert.getInteractionAdvert() != null) {
                AdvertisingManager.getInstance().close(audioAdvert.getInteractionAdvert());
            }
        } else {
            AdvertisingManager.getInstance().close(audioAdvert);
        }
        PlayerManagerHelper.getInstance().playAudioAdOver(true);
        PlayerManagerHelper.getInstance().removelockPlayerForAudioAd();
    }


    public Context getmContext() {
        return mContext;
    }

    public void setmContext(Context mContext) {
        this.mContext = mContext;
    }

    public View getmView() {
        return mView;
    }

    public ImageView getmAudioAdHornImageView() {
        return mAudioAdHornImageView;
    }

    public void setmAudioAdHornImageView(ImageView mAudioAdHornImageView) {
        this.mAudioAdHornImageView = mAudioAdHornImageView;
    }

    public void setmView(View mView) {
        this.mView = mView;
    }


    public ViewGroup getViewGroup() {
        if (mViewGroup == null) {
            if (!(mContext instanceof Activity)) {
                throw new IllegalArgumentException("使用LEVEL_ACTIVITY层级的toast Context需要是Activity");
            }
            mViewGroup = ((Activity) mContext).findViewById(android.R.id.content);
        }
        return mViewGroup;
    }


    public KradioAdAudioSuperView setView(View view) {
        mView = view;
        return this;
    }

    public KradioAdAudioSuperView setRadius(int radius) {
        mKradioAdAudioViewStyleConfig.radius = radius;
        return this;
    }

    public boolean isShowing() {
        return mView != null && mView.isShown();
    }

    @Override
    public KradioAdAudioSuperView create() {
        mView = onCreateView();
        if (mView.getBackground() == null) {
            mView.setBackground(getBackground());
        }
        return this;
    }


    private Drawable getBackground() {
        if (mKradioAdAudioViewStyleConfig.background == null) {
            GradientDrawable gradientDrawable = new GradientDrawable();
            gradientDrawable.setColor(mKradioAdAudioViewStyleConfig.backgroundColor);
            gradientDrawable.setCornerRadius(mKradioAdAudioViewStyleConfig.radius);
            return gradientDrawable;
        } else {
            return mKradioAdAudioViewStyleConfig.background;
        }
    }

    protected View onCreateView() {
        if (mView != null) {
            return mView;
        }
        if (!(mContext instanceof Activity)) {
            throw new IllegalArgumentException("使用LEVEL_ACTIVITY层级的toast Context需要是Activity");
        }
        mViewGroup = ((Activity) mContext).findViewById(android.R.id.content);
        return LayoutInflater.from(mContext)
                .inflate(mKradioAdAudioViewStyleConfig.layoutId,
                        null, false);
    }

    @Override
    public void timerend() {
        if (KradioAdAudioManager.getInstance().getAudioAdvert().isJump()) {
            mAudioAdTitleTv.setText(
                    ResUtil.getString(R.string.ad_audio_jump));
            isTimerOver = true;
            mAudioCenterView.setSelected(true);
            PlayerManagerHelper.getInstance().removelockPlayerForAudioAd();
        } else {
//          音频广告不可跳过，提示一直在播放中，并不可点击取消
            isTimerOver = false;
            mAudioCenterView.setSelected(false);
            mAudioAdTitleTv.setText(
                    ResUtil.getString(R.string.ad_audio_playing));
        }
    }

    @Override
    public void playAdOver() {
    }

    @Override
    public void interrupt() {
    }

    @Override
    public void updateTime() {
    }

    @Override
    public void reset() {
    }

    @Override
    public void show() {
    }

    public void updateViewTime(long time) {
        isTimerOver = false;
//        mAudioAdTitleTv.setText((time / millisecond) + ResUtil.getString(R.string.ad_audio_tip));
        mAudioAdTitleTv.setText(
                ResUtil.getString(R.string.ad_audio_playing));
    }

    @Override
    public void hide() {
    }

    @Override
    public void pause() {
    }

    @Override
    public void cancel() {
    }

    @Override
    public void dismiss() {
    }

    @Override
    public void distroy() {
    }


    public ImageView getAudioImageView() {
        return mAudioImageView;
    }
}
