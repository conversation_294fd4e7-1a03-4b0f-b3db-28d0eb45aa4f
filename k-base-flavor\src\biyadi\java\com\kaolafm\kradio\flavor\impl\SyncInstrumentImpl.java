package com.kaolafm.kradio.flavor.impl;

import android.content.Intent;
import android.hardware.bydauto.instrument.BYDAutoInstrumentDevice;
import android.util.Log;


import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.SyncInstrumentInter;
import com.kaolafm.kradio.service.AutoPlayService;
import com.kaolafm.sdk.core.mediaplayer.IPlayerStateListener;




public class SyncInstrumentImpl implements SyncInstrumentInter, IPlayerStateListener {
    private static final String TAG = "SyncInstrumentImpl";
    private BYDAutoInstrumentDevice mBYDAutoInstrumentDevice;

    private KLAutoPlayerManager mPlayerManager;
    private IPlayerInitCompleteListener onPlayerInitCompleteListener = new IPlayerInitCompleteListener() {
        @Override
        public void onPlayerInitComplete(boolean b) {
            try {
                PlayerManager.getInstance().removePlayerInitComplete(onPlayerInitCompleteListener);
                mPlayerManager.addIPlayerStateListener(SyncInstrumentImpl.this);
                mBYDAutoInstrumentDevice = BYDAutoInstrumentDevice.getInstance(AppDelegate.getInstance().getContext());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    };

    private void initPlayer() {
        PlayerManager playerManager = PlayerManager.getInstance();
        boolean isInitSuccess = playerManager.isPlayerInitSuccess();
        if (isInitSuccess) {
            try {
                mPlayerManager.addIPlayerStateListener(this);
                mBYDAutoInstrumentDevice = BYDAutoInstrumentDevice.getInstance(AppDelegate.getInstance().getContext());
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            playerManager.addPlayerInitComplete(onPlayerInitCompleteListener);
        }
    }

    @Override
    public boolean initSyncInstrument() {
        //初始化注册监听接口
        mPlayerManager = KLAutoPlayerManager.getInstance();
        initPlayer();
        return true;
    }

    @Override
    public boolean releaseSyncInstrument() {
        //反注册监听接口
        PlayerManager.getInstance().removePlayControlStateCallback(this);
        //注销AutoPlayService
        Intent intent = new Intent(AppDelegate.getInstance().getContext(), AutoPlayService.class);
        AppDelegate.getInstance().getContext().stopService(intent);
        return true;
    }

    @Override
    public void onIdle(PlayItem playItem) {
        try {
            Log.i(TAG, "onIdle: BYDAutoInstrumentDevice.MUSIC_PAUSE");
            mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_PAUSE);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onPlayerPreparing(PlayItem playItem) {
        try {
            Log.i(TAG, "onPlayerPreparing: playItem.getTitle() = " + playItem.getTitle());
            mBYDAutoInstrumentDevice.sendMusicName(playItem.getTitle());
            mBYDAutoInstrumentDevice.sendMusicPlaybackProgress(0);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onPlayerPlaying(PlayItem playItem) {
        try {
            Log.i(TAG, "onPlayerPlaying: playItem.getTitle() = " + playItem.getTitle());
            Log.i(TAG, "onPlayerPlaying: BYDAutoInstrumentDevice.MUSIC_PLAY ");
            mBYDAutoInstrumentDevice.sendMusicName(playItem.getTitle());
            mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_PLAY);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onPlayerPaused(PlayItem playItem) {
        try {
            Log.i(TAG, "onPlayerPaused: BYDAutoInstrumentDevice.MUSIC_PAUSE");
            mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_PAUSE);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onProgress(String s, int i, int i1, boolean b) {
        try {
            Log.i(TAG, "onProgress: " + i / 1000);
            mBYDAutoInstrumentDevice.sendMusicPlaybackProgress(i / 1000);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onPlayerFailed(PlayItem playItem, int i, int i1) {
        try {
            Log.i(TAG, "onPlayerFailed: BYDAutoInstrumentDevice.MUSIC_PAUSE");
            mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_PAUSE);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onPlayerEnd(PlayItem playItem) {
        try {
            Log.i(TAG, "onPlayerEnd: BYDAutoInstrumentDevice.MUSIC_PAUSE");
            mBYDAutoInstrumentDevice.sendMusicState(BYDAutoInstrumentDevice.MUSIC_PAUSE);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onSeekStart(String s) {

    }

    @Override
    public void onSeekComplete(String s) {

    }

    @Override
    public void onBufferingStart(PlayItem playItem) {

    }

    @Override
    public void onBufferingEnd(PlayItem playItem) {

    }

}
