<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.kaolafm.kradio.flavor">

    <application>

        <!--亿咖通新增配置信息-->
        <meta-data
            android:name="eCarX_OpenAPI_AppId"
            android:value="58946fa69154a14398e63334b5d91bc3" />
        <meta-data
            android:name="eCarX_OpenAPI_AppKey"
            android:value="38ff2888cef557a8880f867ad019fc46" />

<!--        <meta-data-->
<!--            android:name="com.ecarx.membercenter.BuildInfo.APP_KEY"-->
<!--            android:value="58946fa69154a14398e63334b5d91bc3" />-->
        <receiver android:name="com.kaolafm.kradio.receiver.VrReceiver">
            <intent-filter>
                <action android:name="ecarx.intent.broadcast.action.ECARX_VR_APP_CLOSE" />
                <category android:name="android.intent.category.DEFAULT" />
                <data
                    android:host="vr.com"
                    android:path="/云听"
                    android:scheme="ecarx" />
            </intent-filter>
        </receiver>
    </application>
</manifest>


