package com.kaolafm.kradio.lib.toast;

import android.animation.Animator;
import android.animation.ObjectAnimator;
import android.annotation.SuppressLint;
import android.graphics.drawable.Drawable;
import androidx.annotation.IntDef;
import androidx.annotation.LayoutRes;
import android.view.Gravity;
import android.view.ViewGroup.LayoutParams;
import com.kaolafm.kradio.lib.R;
import com.kaolafm.kradio.lib.utils.ResUtil;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * toast的样式
 * <AUTHOR>
 * @date 2018/4/11
 */
 public class ToastStyle {

    @Retention(RetentionPolicy.SOURCE)
    @IntDef(flag = true, value = {PRIORITY_HIGH, PRIORITY_MEDIUM, PRIORITY_LOW})
    public @interface PriorityLevel {}

    public static final int PRIORITY_HIGH = 1;

    public static final int PRIORITY_MEDIUM = 2;

    public static final int PRIORITY_LOW = 3;

    @SuppressLint("RtlHardcoded")
    @Retention(RetentionPolicy.SOURCE)
    @IntDef({Gravity.BOTTOM, Gravity.CENTER, Gravity.CENTER_HORIZONTAL,
            Gravity.CENTER_VERTICAL, Gravity.END, Gravity.LEFT, Gravity.NO_GRAVITY,
            Gravity.RIGHT, Gravity.START, Gravity.TOP})
    public @interface GravityStyle {}

    @Retention(RetentionPolicy.SOURCE)
    @IntDef(flag = true, value = {LEVEL_SYSTEM, LEVEL_ACTIVITY})
    public @interface DisplayLevel{}

    public static final int LEVEL_SYSTEM = 10;

    public static final int LEVEL_ACTIVITY = 20;


    @Retention(RetentionPolicy.SOURCE)
    @IntDef({ICON_POSITION_LEFT, ICON_POSITION_RIGHT, ICON_POSITION_BOTTOM, ICON_POSITION_TOP})
    public @interface IconPosition {

    }

    public static final int ICON_POSITION_LEFT = 1;

    public static final int ICON_POSITION_RIGHT = 2;

    public static final int ICON_POSITION_BOTTOM = 3;

    public static final int ICON_POSITION_TOP = 4;

    public static final int DURATION_SHORT = 1000;

    public static final int DURATION_LONG = 2000;

    public static final int DURATION_INDEFINITE = -1;

    /**
     * 自定义view布局id
     */
    @LayoutRes
    public int layoutId;

    public int style;

    /**
     * toast的显示层级，{@link #LEVEL_ACTIVITY} Activity层级，只显示在当前页面。{@link #LEVEL_SYSTEM} 系统层级，显示在所有页面最上层
     */
    public int displayLevel;

    /**
     * toast显示时间
     */
    public int duration;

    /**
     * toast的高度
     */
    public int height;

    /**
     * toast的宽度
     */
    public int width;

    /**
     * toast的显示层级是{@link #LEVEL_SYSTEM} 只能用系统的动画资源，如{@link android.R.style#Animation_Toast}
     */
    public int animationId;

    /**
     * toast显示动画
     */
    public Animator showAnimator;

    /**
     * toast隐藏动画
     */
    public Animator hideAnimator;

    /**
     * toast显示位置
     */
    public int gravity;

    /**
     * 水平方向偏移量，左右的margin值
     */
    public int xOffset;

    /**
     * 竖直方法偏移量，上下的margin值
     */
    public int yOffset;

    /**
     * toast的时间戳
     */
    public long timestamp;

    /**
     * toast显示的文字
     */
    public String message;

    /**
     * toast文字颜色，色值
     */
    public int messageTextColor;

    /**
     * toast文字大小，px
     */
    public float messageTextSize;

    /**
     * toast文字旁边的icon资源id
     */
    public int messageIconRes;

    /**
     * icon在toast文字的位置，上下左右
     */
    public int messageIconPosition;

    /**
     * toast背景色, 色值
     */
    public int backgroundColor;

    /**
     * toast背景图， 图片
     */
    public Drawable background;

    /**
     * toast的圆角度数
     */
    public float radius;

    /**
     * toast的优先级，高中低，默认是中
     */
    public int priorityLevel;

    /**
     * 设置toast是否可以操作。
     */
    public boolean canTouch;

    public ToastStyle() {
        layoutId = R.layout.layout_normal_toast;

        width = LayoutParams.WRAP_CONTENT;
        height = ResUtil.getDimen(R.dimen.y90);
        animationId = android.R.style.Animation_Toast;
        duration = DURATION_LONG;
        gravity = Gravity.CENTER;
        yOffset = -ResUtil.getDimen(R.dimen.y136);
        radius = ResUtil.getDimen(R.dimen.m4);
        backgroundColor = ResUtil.getColor(R.color.information_main_color);
        priorityLevel = PRIORITY_MEDIUM;
        messageTextColor = ResUtil.getColor(R.color.toast_text_color);
        messageTextSize = ResUtil.getDimen(R.dimen.text_size7);
        messageIconPosition = ICON_POSITION_LEFT;

        ObjectAnimator objectAnimator1 = new ObjectAnimator();
        objectAnimator1.setPropertyName("alpha");
        objectAnimator1.setDuration(250).setFloatValues(0F,1F);
        showAnimator = objectAnimator1;
        ObjectAnimator objectAnimator2 = new ObjectAnimator();
        objectAnimator2.setPropertyName("alpha");
        objectAnimator2.setDuration(250).setFloatValues(1F,0F);
        hideAnimator = objectAnimator2;

        canTouch = false;
        displayLevel = LEVEL_SYSTEM;

    }
}
