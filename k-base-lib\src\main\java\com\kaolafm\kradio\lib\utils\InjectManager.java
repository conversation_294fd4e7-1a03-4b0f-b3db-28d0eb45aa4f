package com.kaolafm.kradio.lib.utils;


import com.kaolafm.kradio.lib.base.flavor.BootCompletedLogicListener;
import com.kaolafm.kradio.lib.base.flavor.ShutdownLogicListener;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/3/12.
 */

public class InjectManager {

    private BootCompletedLogicListener mBootCompletedLogicListener;

    private ShutdownLogicListener mShutdownLogicListener;

    private InjectManager(){

    }

    private static class INSTANCE_CLASS_INJECTMANAGER{
        private static final InjectManager INJECT_MANAGER = new InjectManager();
    }

    public static InjectManager getInstance(){
        return INSTANCE_CLASS_INJECTMANAGER.INJECT_MANAGER;
    }

    public void injectInit(){
        setBootCompletedLogicListener();
        setShutdownLogicListener();
        setNetworkAvailableListener();
    }

    private void setBootCompletedLogicListener(){
        if (mBootCompletedLogicListener == null) {
            BootCompletedLogicListener bootCompletedLogicListener = (BootCompletedLogicListener) ClazzImplUtil.getInter("BootCompletedLogic");
            if (bootCompletedLogicListener != null) {
                mBootCompletedLogicListener = bootCompletedLogicListener;
            }
        }
    }

    public BootCompletedLogicListener getBootCompletedLogicListener() {
        return mBootCompletedLogicListener;
    }

    private void setShutdownLogicListener(){
        if (mShutdownLogicListener == null) {
            ShutdownLogicListener shutdownLogicListener = (ShutdownLogicListener) ClazzImplUtil.getInter("ShutdownLogic");
            if (shutdownLogicListener != null) {
                mShutdownLogicListener = shutdownLogicListener;
            }
        }
    }

    private void setNetworkAvailableListener(){
//        if (mNetworkAvailableListener == null) {
//            NetworkAvailableListener networkAvailableListener = (NetworkAvailableListener) ClazzUtil.getClazzInstance("com.kaolafm.auto.flavor.impl.NetworkAvailableDispose");
//            if (networkAvailableListener != null) {
//                mNetworkAvailableListener = networkAvailableListener;
//            }
//        }
    }

    public ShutdownLogicListener getShutdownLogicListener() {
        return mShutdownLogicListener;
    }

//    public NetworkAvailableListener getNetworkAvailableListener(){
//        return mNetworkAvailableListener;
//    }
}
