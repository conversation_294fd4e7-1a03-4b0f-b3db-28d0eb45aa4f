package com.kaolafm.kradio.flavor.impl;


import com.huawei.carmediakit.session.CarMediaUILifecycle;
import com.huawei.carmediakit.session.MediaAppContextHolder;
import com.kaolafm.kradio.huawei.CarMediaUILifecycleObserver;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioAppInitInter;

public class KRadioAppInitImpl implements KRadioAppInitInter {
    @Override
    public void onAppInit() {
        MediaAppContextHolder.setAppContext(AppDelegate.getInstance().getContext());
        CarMediaUILifecycle.getInstance().addObserver(new
                CarMediaUILifecycleObserver());
    }
}
