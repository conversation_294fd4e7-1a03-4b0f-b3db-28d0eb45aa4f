package com.kaolafm.kradio.huawei.controller;

import android.os.IBinder;
import android.os.RemoteException;

import com.huawei.carmediakit.bean.MediaElement;
import com.huawei.carmediakit.bean.MineCompilation;
import com.huawei.carmediakit.bean.MineDataType;
import com.huawei.carmediakit.bean.OperResult;
import com.huawei.carmediakit.callback.BasicCallback;
import com.huawei.carmediakit.constant.ErrorCode;
import com.huawei.carmediakit.reporter.MediaDataReporter;
import com.kaolafm.kradio.clientControlerForKradio.ClientImpl;
import com.kaolafm.kradio.huawei.convert.PlayDataConverterUtil;
import com.kaolafm.kradio.huawei.utils.Constant;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.sdk.client.ErrorInfo;
import com.kaolafm.sdk.client.ISubscribeResult;

import java.util.Collections;

public class ClientImplController {
    private static final String TAG = Constant.TAG;
    private final ClientImpl client;
    private static ClientImplController controller;

    private ClientImplController() {
        client = new ClientImpl(AppDelegate.getInstance().getContext());
    }

    public static ClientImplController getClientImpl() {
        if (controller == null) {
            controller = new ClientImplController();
        }

        return controller;
    }

    public void subscribe(long subscribeId, BasicCallback<OperResult> basicCallback) {
        try {
            client.subscribe(subscribeId, new ISubscribeResult() {
                @Override
                public void onSuccuss() throws RemoteException {
                    Logger.i(TAG, "onSuccess");
                    updateSubscribeState(String.valueOf(subscribeId), true, basicCallback);
                }

                @Override
                public void onFailure(ErrorInfo errorInfo) throws RemoteException {
                    Logger.i(TAG, "onFailure=" + errorInfo);
                    basicCallback.callback(new OperResult(ErrorCode.SUCCESS, ""),
                            ErrorCode.SUCCESS, "");
                }

                @Override
                public IBinder asBinder() {
                    return null;
                }
            });
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void unSubscribe(long subscribeId, BasicCallback<OperResult> basicCallback) {
        try {
            client.unsubscribe(subscribeId, new ISubscribeResult() {
                @Override
                public void onSuccuss() throws RemoteException {
                    Logger.i(TAG, "onSuccess");
                    updateSubscribeState(String.valueOf(subscribeId), false, basicCallback);
                }

                @Override
                public void onFailure(ErrorInfo errorInfo) throws RemoteException {
                    Logger.i(TAG, "onFailure=" + errorInfo);
                    basicCallback.callback(new OperResult(ErrorCode.SUCCESS, ""),
                            ErrorCode.SUCCESS, "");
                }

                @Override
                public IBinder asBinder() {
                    return null;
                }
            });
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    private void updateSubscribeState(String s, boolean isSub, BasicCallback<OperResult> basicCallback) {
        PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
        MediaElement affectedItem = PlayDataConverterUtil.toMediaEntityAlbum(playItem);
        // 将保存操作的结果通过回调传递给 CarMediaUI
        basicCallback.callback(new OperResult(ErrorCode.SUCCESS, "success"), ErrorCode.SUCCESS,
                "");
        // 如果操作成功，将用户收藏数据的变化上报给 CarMediaUI
        affectedItem.setFavorite(isSub);
        // 上报音频元素的收藏状态
        MediaDataReporter.reportMediaElementsChange(Collections.singletonList(affectedItem)
        );
        // 上报“我喜欢的”新增一个元素
        MediaDataReporter.reportMineDataAdded(MineDataType.FAVOURITES,
                Collections.singletonList(affectedItem));
        // 上报“收藏歌单”新增一个元素
        MediaDataReporter.reportMineCompilationAdded(MineCompilation.MineCompilationType.FAVOURITE_PLAYLIST,
                Collections.singletonList(affectedItem));
    }

}
