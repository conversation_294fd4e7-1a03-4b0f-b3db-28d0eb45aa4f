apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'org.greenrobot.greendao'
apply plugin: 'product-flavor'
def and = rootProject.ext.android
def jpush = rootProject.ext.jpush
def dependent = rootProject.ext.dependencies

android {
    compileSdkVersion and.compileSdkVersion
    buildToolsVersion and.buildToolsVersion


    defaultConfig {
        minSdkVersion and.minSdkVersion
        targetSdkVersion and.targetSdkVersion
        versionCode 1
        versionName "1.0"
        multiDexEnabled true
        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [moduleName: project.getName(),
                             AROUTER_MODULE_NAME: project.getName()]
            }
        }

        manifestPlaceholders = [
                JPUSH_PKGNAME: jpush["jpush-pkgname"],
                JPUSH_APPKEY : jpush["jpush-appkey"], //JPush上注册的包名对应的appkey（*换成你的*）
                JPUSH_CHANNEL: jpush["jpush-channel"], //暂时填写默认值即可.
        ]
        buildConfigField "boolean", "SUPPORT_WEBP_ANIMATION", "true"
//        buildConfigField "String", "NET_REQUEST_VERSION", "\"${NET_REQUEST_VERSION}\""
    }

    buildTypes {
        debug {
            //测试环境
//            buildConfigField "String", "API_DOMAIN", "\"iovopen-test.radio.cn\""
//            buildConfigField "String", "API_WS_DOMAIN", "\"iovwsopen-test.radio.cn\""
            //预发布环境
//            buildConfigField "String", "API_DOMAIN", "\"iovopen-prelease.radio.cn\""
//            buildConfigField "String", "API_WS_DOMAIN", "\"iovwsopen-prelease.radio.cn\""
            //正式环境
//            buildConfigField "String", "API_DOMAIN", "\"iovopen.radio.cn\""
//            buildConfigField "String", "API_WS_DOMAIN", "\"iovwsopen.radio.cn\""
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
//            buildConfigField "String", "API_DOMAIN", "\"iovopen.radio.cn\""
//            buildConfigField "String", "API_WS_DOMAIN", "\"iovwsopen.radio.cn\""
        }
        prelease {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
//            buildConfigField "String", "API_DOMAIN", "\"iovopen-prelease.radio.cn\""
//            buildConfigField "String", "API_WS_DOMAIN", "\"iovwsopen-prelease.radio.cn\""
        }
    }
    compileOptions {
        sourceCompatibility and.javaSourceVersion
        targetCompatibility and.javaTargetVersion
    }
    dexOptions {
        javaMaxHeapSize "4g"
    }

    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }
    sourceSets {
        main {
            jni.srcDirs = ['src/main/jni', 'src/main/libs/']
            jniLibs.srcDirs = ['libs']
        }
    }
    testOptions {
        unitTests {
            includeAndroidResources = true
        }
    }
    repositories {
        flatDir {
            dirs 'libs'
        }
    }
}
greendao {
    schemaVersion 5
    daoPackage 'com.kaolafm.kradio.lib.basedb.greendao'
    targetGenDir 'src/main/java'
}

dependencies {
    //   implementation 只能对当前module有效，api是对所有依赖该module的都有效，但是annotationProcessor只对当前module有效
    api fileTree(include: ['*.jar'], dir: 'libs')
    api "org.jetbrains.kotlin:kotlin-stdlib-jdk8:$kotlin_version"
   // testImplementation dependent["junit"]
   // testImplementation 'org.mockito:mockito-core:2.19.0'
   // androidTestImplementation 'com.android.support.test:runner:1.0.2'
   // androidTestImplementation 'com.android.support.test.espresso:espresso-core:3.0.2'
   // testImplementation "org.robolectric:robolectric:4.3"
    api 'com.alibaba:arouter-annotation:1.0.6'
    api(dependent["openSDK"]) {
        changing = true
    }
    annotationProcessor dependent["arouter-compiler"]
    api dependent["support-v4"]
    api dependent["appcompat-v7"]
    api dependent["constraint"]
    api dependent["design"]
    api dependent["cardview"]
    api dependent["recyclerview-v7"]
    api dependent["eventbus"]
    api dependent["rxlifecycle2-components"]
   // api dependent["canary-debug"]
    api dependent["multidex"]
    api dependent["glide"]
    api dependent["apng"]
    api dependent["webpdecoder"]
    annotationProcessor dependent["glide-compiler"]
    api dependent["zxing"]
    api dependent["greenDao"]
    api dependent["fastjson"]
    implementation dependent['rebound']
    api(name: 'BaiduLBS_AndroidSDK_Lib', ext: 'aar')
    api(name: 'fragmentation-debug', ext: 'aar')
    api(name: 'fragmentation_core-debug', ext: 'aar')
    api(name: 'Kaolafm-circular-2.0.2', ext: 'aar')
    api(dependent["mmkv"]) {
        exclude module: 'support-annotations'
    }
    api dependent["relinker"]
}
repositories {
    maven { url 'https://maven.aliyun.com/repository/public' }
}