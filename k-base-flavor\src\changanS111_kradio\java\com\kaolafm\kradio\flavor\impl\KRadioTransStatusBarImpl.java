package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import com.kaolafm.kradio.lib.base.flavor.KRadioTransStatusBarInter;
import com.kaolafm.kradio.lib.utils.ScreenUtil;

import static com.kaolafm.kradio.lib.utils.ViewUtil.addPaddingForView;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-07-28 16:02
 ******************************************/
public final class KRadioTransStatusBarImpl implements KRadioTransStatusBarInter {
    private int mStatusBarHeight;

    public KRadioTransStatusBarImpl() {
        mStatusBarHeight = ScreenUtil.getStatusBarHeight();
    }

    @Override
    public boolean changeStatusBarColor(Activity activity, int colorRes) {
        Window window = activity.getWindow();
//        setStatusBar(window, true);
        window.addFlags(WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN);
        window.addFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS);
        window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION);
        return true;
    }

    @Override
    public boolean changeViewLayoutForStatusBar(View view, int id) {
        addPaddingForView(view, 0, mStatusBarHeight, 0, 0);
        return true;
    }

    @Override
    public boolean canChangeViewLayoutForStatusBar(Object... args) {
        return false;
    }

    @Override
    public int getStatusBarHeight(Object... args) {
        return mStatusBarHeight;
    }
}
