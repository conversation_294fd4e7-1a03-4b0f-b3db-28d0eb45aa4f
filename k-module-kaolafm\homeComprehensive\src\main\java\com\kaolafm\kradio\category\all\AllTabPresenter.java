package com.kaolafm.kradio.category.all;

import androidx.fragment.app.Fragment;
import com.kaolafm.kradio.category.FragmentFactory;
import com.kaolafm.kradio.category.base.TabContract;
import com.kaolafm.kradio.categories.CategoryConstant;
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 **/
class AllTabPresenter extends BasePresenter<AllTabModel, TabContract.IView> implements TabContract.IPresenter {
    private Fragment mFragmentQQ;
    private Fragment mFragmentRadio;
    private Fragment mFragmentBroadcast;

    public AllTabPresenter(TabContract.IView view) {
        super(view);
    }

    @Override
    protected AllTabModel createModel() {
        return new AllTabModel();
    }

    @Override
    public void loadAIData(long showTabId) {

    }

    @Override
    public void loadData(long showTabId) {

        mModel.getAllCategoryData(new HttpCallback<List<AllCategoriesItem>>() {
            @Override
            public void onSuccess(List<AllCategoriesItem> allCategoriesItems) {
                ArrayList<Fragment> fragments = new ArrayList<>();
                String[] titles = new String[allCategoriesItems.size()];
                for (int i = 0; i < allCategoriesItems.size(); i++) {
                    AllCategoriesItem item = allCategoriesItems.get(i);
                    titles[i] = item.title;

                    // TODO: 2019/2/27 HorizontalSubcategoryFragment换成具体的子类
                    switch (item.type) {
                        case CategoryConstant.MEDIA_TYPE_MUSIC:
                        case CategoryConstant.LOGIN_TYPE_WECHAT:
//                            if (mFragmentQQ == null) {
//                                mFragmentQQ = FragmentFactory.createQqDispatchFragment(showTabId);
//                            }
//                            fragments.add(mFragmentQQ);
                            break;
                        case CategoryConstant.MEDIA_TYPE_RADIO:
                            if (mFragmentRadio == null) {
                                mFragmentRadio = FragmentFactory.createRadioDispatchFragment(/*CategoryConstant.MEDIA_TYPE_RADIO,*/ showTabId);
                            }
                            fragments.add(mFragmentRadio);
                            break;
                        case CategoryConstant.MEDIA_TYPE_BROADCAST:
                            mFragmentBroadcast = FragmentFactory.createRadioDispatchFragment(/*CategoryConstant.MEDIA_TYPE_BROADCAST,*/ showTabId);
                            fragments.add(mFragmentBroadcast);
                            break;
                         default:
                    }

                }

                mView.showData(titles, fragments, 0);
            }

            @Override
            public void onError(ApiException exception) {
                mView.showError(exception);
            }
        });


    }
}
