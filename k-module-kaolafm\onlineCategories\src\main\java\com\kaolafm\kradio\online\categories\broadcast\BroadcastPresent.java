package com.kaolafm.kradio.online.categories.broadcast;

import android.text.TextUtils;

import com.kaolafm.kradio.common.bean.BroadcastRadioDetailData;
import com.kaolafm.kradio.common.bean.BroadcastRadioSimpleData;
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.lib.base.ui.online.BaseShowHideFragment;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.trello.rxlifecycle3.LifecycleProvider;
import com.trello.rxlifecycle3.android.FragmentEvent;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by kaolafm on 2018/4/25.
 */

public class BroadcastPresent extends BasePresenter<BroadcastModel, IBroadcastView> {

    private boolean isLoadMore;

    public BroadcastPresent(IBroadcastView view) {
        super(view);
    }

    public boolean enableLoadmore() {
        return mModel.haveNext();
    }

    public ArrayList<BroadcastRadioSimpleData> itemBean2Simple(List<BroadcastRadioDetailData> dataList) {
        if (dataList == null) {
            return null;
        }
        ArrayList<BroadcastRadioSimpleData> broadcastRadioSimpleDataList = new ArrayList<>();
        for (int i = 0, size = dataList.size(); i < size; i++) {
            BroadcastRadioDetailData broadcastRadioDetailData = dataList.get(i);
            BroadcastRadioSimpleData simpleData = new BroadcastRadioSimpleData();
            simpleData.setBroadcastId(broadcastRadioDetailData.getBroadcastId());
            String freq = broadcastRadioDetailData.getFreq();
            freq = TextUtils.isEmpty(freq) ? "" : freq;
            simpleData.setName(broadcastRadioDetailData.getName() + "  " + freq);
            simpleData.setImg(broadcastRadioDetailData.getIcon());
            broadcastRadioSimpleDataList.add(simpleData);
        }
        return broadcastRadioSimpleDataList;
    }

    @Override
    protected BroadcastModel createModel() {
        return new BroadcastModel();
    }

    public void loadFirstData(int categoryId) {
        setBroadcastCategoryType(categoryId);
        mModel.setPageNum(1);
        requestBroadcastListData();
        isLoadMore = false;
    }

    public void loadMore() {
        if (!mModel.haveNext()) {
            mView.showError();
            return;
        }
        isLoadMore = true;
        requestBroadcastListData();
    }

    /**
     * 设置广播类型
     */
    private void setBroadcastCategoryType(int classifyId) {
        mModel.setBroadcastCategoryType(classifyId);
    }

    public void requestBroadcastListData() {
        mModel.requestBroadcastListData(((LifecycleProvider) mView).bindUntilEvent(FragmentEvent.DESTROY_VIEW), new HttpCallback<List<BroadcastRadioDetailData>>() {
            @Override
            public void onSuccess(List<BroadcastRadioDetailData> basePageResult) {
                if (mView != null) {
                    mView.notifyDataChange(basePageResult, isLoadMore);
                }
            }

            @Override
            public void onError(ApiException e) {
                if (mView != null) {
                    mView.showError();
                }
            }
        });
    }
}
