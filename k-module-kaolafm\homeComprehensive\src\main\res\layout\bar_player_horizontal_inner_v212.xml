<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginStart="@dimen/x60"
    android:layout_marginEnd="@dimen/x100">

    <TextView
        android:id="@+id/player_bar_tag_text"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/y30"
        android:background="@drawable/comprehensive_playerbar_tag_bg"
        android:gravity="center_vertical"
        android:includeFontPadding="false"
        android:paddingLeft="@dimen/x10"
        android:paddingRight="@dimen/x10"
        android:singleLine="true"
        android:textColor="@color/player_radio_bar_subtitle_text_color2"
        android:textSize="@dimen/play_bar_sub_title_text_size"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="TopNews"
        tools:visibility="visible" />

    <com.kaolafm.kradio.lib.widget.AutoMarqueenTextView
        android:id="@+id/player_bar_title_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:layout_marginStart="@dimen/m10"
        android:ellipsize="marquee"
        android:marqueeRepeatLimit="marquee_forever"
        android:singleLine="true"
        android:textColor="@color/player_radio_bar_title_text_color"
        android:textSize="@dimen/m26"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@id/player_bar_tag_text"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_goneMarginStart="0dp"
        tools:text="山东人说话没问题山东人说话没问题山东人说话没问题山东人说话没问题" />

    <!--    <LinearLayout-->
    <!--        android:id="@+id/player_bar_sub_layout"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="@dimen/player_bar_audio_content_height"-->
    <!--        android:layout_marginTop="@dimen/y4"-->
    <!--        app:layout_constraintBottom_toBottomOf="parent"-->
    <!--        app:layout_constraintHorizontal_bias="0.5"-->
    <!--        app:layout_constraintLeft_toLeftOf="parent"-->
    <!--        app:layout_constraintTop_toBottomOf="@id/player_bar_title_text">-->

    <!--        <TextView-->
    <!--            android:id="@+id/player_bar_sub_tag_text"-->
    <!--            android:layout_width="wrap_content"-->
    <!--            android:layout_height="wrap_content"-->
    <!--            android:layout_marginRight="@dimen/x12"-->
    <!--            android:background="@drawable/color_medium_line_radio_sub_title_tag_round_bg"-->
    <!--            android:includeFontPadding="false"-->
    <!--            android:paddingLeft="@dimen/x8"-->
    <!--            android:paddingTop="@dimen/y3"-->
    <!--            android:paddingRight="@dimen/x8"-->
    <!--            android:paddingBottom="@dimen/y3"-->
    <!--            android:singleLine="true"-->
    <!--            android:textColor="@color/player_radio_bar_subtitle_text_color"-->
    <!--            android:textSize="@dimen/play_bar_sub_title_text_size"-->
    <!--            android:visibility="gone"-->
    <!--            tools:text="TopNews"-->
    <!--            tools:visibility="visible" />-->

    <!--        <TextView-->
    <!--            android:id="@+id/player_bar_time_text"-->
    <!--            android:layout_width="wrap_content"-->
    <!--            android:layout_height="wrap_content"-->
    <!--            android:layout_marginRight="@dimen/x8"-->
    <!--            android:ellipsize="end"-->
    <!--            android:textColor="@color/player_radio_bar_subtitle_text_color"-->
    <!--            android:textSize="@dimen/play_bar_sub_title_text_size"-->
    <!--            android:visibility="gone"-->
    <!--            tools:text="2020-07-16"-->
    <!--            tools:visibility="visible" />-->

    <!--        <ImageView-->
    <!--            android:id="@+id/player_bar_vertical_line"-->
    <!--            android:layout_width="@dimen/x1"-->
    <!--            android:layout_height="@dimen/y16"-->
    <!--            android:layout_gravity="center_vertical"-->
    <!--            android:layout_marginRight="@dimen/x8"-->
    <!--            android:src="@drawable/radio_list_item_vertical_line"-->
    <!--            android:visibility="gone"-->
    <!--            tools:visibility="visible" />-->

    <!--        <TextView-->
    <!--            android:id="@+id/player_bar_sub_title_text"-->
    <!--            android:layout_width="wrap_content"-->
    <!--            android:layout_height="wrap_content"-->
    <!--            android:ellipsize="end"-->
    <!--            android:maxLines="1"-->
    <!--            android:textColor="@color/player_radio_bar_subtitle_text_color"-->
    <!--            android:textSize="@dimen/play_bar_sub_title_text_size"-->
    <!--            tools:text="新石门客栈" />-->
    <!--    </LinearLayout>-->

</androidx.constraintlayout.widget.ConstraintLayout>