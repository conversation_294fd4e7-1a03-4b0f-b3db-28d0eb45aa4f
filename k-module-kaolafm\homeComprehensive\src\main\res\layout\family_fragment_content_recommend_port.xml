<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/intesting_cslayout_port"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/recommend_image_one_port"
        android:layout_width="@dimen/m155"
        android:layout_height="0dp"
        android:src="@drawable/pc_bg_unselect"
        app:layout_constraintCircle="@+id/recommend_image_five_port"
        app:layout_constraintCircleAngle="315"
        app:layout_constraintCircleRadius="@dimen/m170"
        app:layout_constraintDimensionRatio="1:1" />

    <TextView
        android:id="@+id/recommend_text_one_port"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size2"
        app:layout_constraintBottom_toBottomOf="@id/recommend_image_one_port"
        app:layout_constraintLeft_toLeftOf="@id/recommend_image_one_port"
        app:layout_constraintRight_toRightOf="@id/recommend_image_one_port"
        app:layout_constraintTop_toTopOf="@id/recommend_image_one_port" />


    <ImageView
        android:id="@+id/recommend_image_two_port"
        android:layout_width="@dimen/m165"
        android:layout_height="0dp"
        android:src="@drawable/pc_bg_unselect"
        app:layout_constraintCircle="@+id/recommend_image_five_port"
        app:layout_constraintCircleAngle="378"
        app:layout_constraintCircleRadius="@dimen/m240"
        app:layout_constraintDimensionRatio="1:1" />

    <TextView
        android:id="@+id/recommend_text_two_port"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size2"
        app:layout_constraintBottom_toBottomOf="@id/recommend_image_two_port"
        app:layout_constraintLeft_toLeftOf="@id/recommend_image_two_port"
        app:layout_constraintRight_toRightOf="@id/recommend_image_two_port"
        app:layout_constraintTop_toTopOf="@id/recommend_image_two_port" />


    <ImageView
        android:id="@+id/recommend_image_three_port"
        android:layout_width="@dimen/m155"
        android:layout_height="0dp"
        android:src="@drawable/pc_bg_unselect"
        app:layout_constraintCircle="@+id/recommend_image_six_port"
        app:layout_constraintCircleAngle="430"
        app:layout_constraintCircleRadius="@dimen/m170"
        app:layout_constraintDimensionRatio="1:1" />

    <TextView
        android:id="@+id/recommend_text_three_port"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size2"
        app:layout_constraintBottom_toBottomOf="@id/recommend_image_three_port"
        app:layout_constraintLeft_toLeftOf="@id/recommend_image_three_port"
        app:layout_constraintRight_toRightOf="@id/recommend_image_three_port"
        app:layout_constraintTop_toTopOf="@id/recommend_image_three_port" />

    <!--第二层-->
    <ImageView
        android:id="@+id/recommend_image_four_port"
        android:layout_width="@dimen/m150"
        android:layout_height="0dp"
        android:src="@drawable/pc_bg_unselect"
        app:layout_constraintCircle="@+id/recommend_image_nice_port"
        app:layout_constraintCircleAngle="290"
        app:layout_constraintCircleRadius="@dimen/m235"
        app:layout_constraintDimensionRatio="1:1" />

    <TextView
        android:id="@+id/recommend_text_four_port"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size2"
        app:layout_constraintBottom_toBottomOf="@id/recommend_image_four_port"
        app:layout_constraintLeft_toLeftOf="@id/recommend_image_four_port"
        app:layout_constraintRight_toRightOf="@id/recommend_image_four_port"
        app:layout_constraintTop_toTopOf="@id/recommend_image_four_port" />

    <ImageView
        android:id="@+id/recommend_image_five_port"
        android:layout_width="0dp"
        android:layout_height="@dimen/m155"
        android:src="@drawable/pc_bg_unselect"
        app:layout_constraintCircle="@+id/recommend_image_nice_port"
        app:layout_constraintCircleAngle="335"
        app:layout_constraintCircleRadius="@dimen/m190"
        app:layout_constraintDimensionRatio="1:1" />

    <TextView
        android:id="@+id/recommend_text_five_port"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size2"
        app:layout_constraintBottom_toBottomOf="@id/recommend_image_five_port"
        app:layout_constraintLeft_toLeftOf="@id/recommend_image_five_port"
        app:layout_constraintRight_toRightOf="@id/recommend_image_five_port"
        app:layout_constraintTop_toTopOf="@id/recommend_image_five_port" />

    <ImageView
        android:id="@+id/recommend_image_six_port"
        android:layout_width="0dp"
        android:layout_height="@dimen/m155"
        android:src="@drawable/pc_bg_unselect"
        app:layout_constraintCircle="@+id/recommend_image_nice_port"
        app:layout_constraintCircleAngle="375"
        app:layout_constraintCircleRadius="@dimen/m260"
        app:layout_constraintDimensionRatio="1:1" />

    <TextView
        android:id="@+id/recommend_text_six_port"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size2"
        app:layout_constraintBottom_toBottomOf="@id/recommend_image_six_port"
        app:layout_constraintLeft_toLeftOf="@id/recommend_image_six_port"
        app:layout_constraintRight_toRightOf="@id/recommend_image_six_port"
        app:layout_constraintTop_toTopOf="@id/recommend_image_six_port" />


    <ImageView
        android:id="@+id/recommend_image_seven_port"
        android:layout_width="@dimen/m150"
        android:layout_height="0dp"
        android:src="@drawable/pc_bg_unselect"
        app:layout_constraintCircle="@+id/recommend_image_nice_port"
        app:layout_constraintCircleAngle="420"
        app:layout_constraintCircleRadius="@dimen/m220"
        app:layout_constraintDimensionRatio="1:1" />

    <TextView
        android:id="@+id/recommend_text_seven_port"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size2"
        app:layout_constraintBottom_toBottomOf="@id/recommend_image_seven_port"
        app:layout_constraintLeft_toLeftOf="@id/recommend_image_seven_port"
        app:layout_constraintRight_toRightOf="@id/recommend_image_seven_port"
        app:layout_constraintTop_toTopOf="@id/recommend_image_seven_port" />


    <ImageView
        android:id="@+id/recommend_image_eight_port"
        android:layout_width="@dimen/m155"
        android:layout_height="0dp"
        android:src="@drawable/pc_bg_unselect"
        app:layout_constraintCircle="@+id/recommend_image_nice_port"
        app:layout_constraintCircleAngle="240"
        app:layout_constraintCircleRadius="@dimen/m220"
        app:layout_constraintDimensionRatio="1:1" />

    <TextView
        android:id="@+id/recommend_text_eight_port"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size2"
        app:layout_constraintBottom_toBottomOf="@id/recommend_image_eight_port"
        app:layout_constraintLeft_toLeftOf="@id/recommend_image_eight_port"
        app:layout_constraintRight_toRightOf="@id/recommend_image_eight_port"
        app:layout_constraintTop_toTopOf="@id/recommend_image_eight_port" />

    <ImageView
        android:id="@+id/recommend_image_nice_port"
        android:layout_width="@dimen/m210"
        android:layout_height="0dp"
        android:src="@drawable/pc_bg_unselect"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.56" />

    <TextView
        android:id="@+id/recommend_text_nice_port"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size2"
        app:layout_constraintBottom_toBottomOf="@id/recommend_image_nice_port"
        app:layout_constraintLeft_toLeftOf="@id/recommend_image_nice_port"
        app:layout_constraintRight_toRightOf="@id/recommend_image_nice_port"
        app:layout_constraintTop_toTopOf="@id/recommend_image_nice_port" />


    <ImageView
        android:id="@+id/recommend_image_ten_port"
        android:layout_width="@dimen/m160"
        android:layout_height="0dp"
        android:src="@drawable/pc_bg_unselect"
        app:layout_constraintCircle="@+id/recommend_image_nice_port"
        app:layout_constraintCircleAngle="120"
        app:layout_constraintCircleRadius="@dimen/m220"
        app:layout_constraintDimensionRatio="1:1" />

    <TextView
        android:id="@+id/recommend_text_ten_port"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size2"
        app:layout_constraintBottom_toBottomOf="@id/recommend_image_ten_port"
        app:layout_constraintLeft_toLeftOf="@id/recommend_image_ten_port"
        app:layout_constraintRight_toRightOf="@id/recommend_image_ten_port"
        app:layout_constraintTop_toTopOf="@id/recommend_image_ten_port" />

    <TextView
        android:id="@+id/interstedSubmit_port"
        android:layout_width="@dimen/family_recommend_button_width"
        android:layout_height="@dimen/family_recommend_button_height"
        android:layout_marginBottom="@dimen/y60"
        android:background="@drawable/pc_next_bg"
        android:gravity="center"
        android:text="@string/personlityrecommendation_intest_submit"
        android:textColor="#FFFFFF"
        android:textSize="@dimen/text_size2"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>