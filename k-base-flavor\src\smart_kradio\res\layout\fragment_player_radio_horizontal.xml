<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/player_radio_root_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_home"
    android:clickable="false">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/player_radio_land_guideline"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.38" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/player_radio_port_guideline"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.36" />

    <View
        android:id="@+id/player_title_line"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:background="@color/radio_fragment_title_line_bg_color"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.11" />

    <FrameLayout
        android:id="@+id/player_radio_subscribe_layout"
        android:layout_width="@dimen/player_subscribe_button_width"
        android:layout_height="@dimen/player_subscribe_button_height"
        android:layout_marginRight="@dimen/all_view_left_right_distance"
        android:background="@drawable/player_subscribe_selector"
        app:layout_constraintBottom_toTopOf="@id/player_title_line"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/player_radio_subscribe_number_text"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:ellipsize="end"
            android:gravity="center"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:textColor="@color/player_subscribe_text_selector"
            android:textSize="@dimen/player_subscribe_num_text_size" />

        <TextView
            android:id="@+id/player_radio_subscribe_text"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:ellipsize="end"
            android:gravity="center"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:text="@string/subscribe_str"
            android:textColor="@color/player_subscribe_text_selector"
            android:textSize="@dimen/text_size_title4" />

    </FrameLayout>


    <ImageView
        android:id="@+id/player_horizontal_back_image"
        style="@style/FragmentBackButton"
        app:layout_constraintBottom_toTopOf="@id/player_title_line" />


    <LinearLayout
        android:id="@+id/player_radio_title_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toTopOf="@id/player_title_line"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:paddingRight="@dimen/x180"
        android:paddingLeft="@dimen/x180"
        android:orientation="horizontal"
        >

        <TextView
            android:id="@+id/player_radio_title_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:textColor="@color/global_title_text_color"
            android:textSize="@dimen/text_size_title1"
            android:layout_gravity="center"
            android:layout_marginLeft="@dimen/x40"
            android:layout_weight="0.9" />


        <com.kaolafm.kradio.player.comprehensive.audio.RadioPlaySourceFoundView
            android:id="@+id/radio_play_source_found_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="0.1" />

    </LinearLayout>



    <ImageView
        android:id="@+id/radio_player_background"
        android:layout_width="0dp"
        android:layout_height="@dimen/y140"
        android:layout_marginLeft="@dimen/all_view_left_right_distance"
        android:layout_marginTop="@dimen/y112"
        android:layout_marginRight="@dimen/all_view_left_right_distance"
        android:background="@drawable/radio_player_control_bg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/player_title_line" />

    <ImageView
        android:id="@+id/player_radio_image"
        android:layout_width="@dimen/m272"
        android:layout_height="@dimen/m300"
        android:layout_marginTop="@dimen/y77"
        android:clickable="false"
        android:scaleType="fitXY"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/player_radio_land_guideline"
        app:layout_constraintTop_toBottomOf="@id/player_title_line" />

    <TextView
        android:id="@+id/tv_live_flag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/flag_live_bg"
        android:paddingLeft="@dimen/x8"
        android:paddingTop="@dimen/y6"
        android:paddingRight="@dimen/x8"
        android:paddingBottom="@dimen/y6"
        android:text="@string/live"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size3"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="@id/player_radio_image"
        app:layout_constraintTop_toTopOf="@id/player_radio_image" />

    <TextView
        android:id="@+id/player_radio_title_sub_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:maxLines="1"
        android:textColor="@color/global_subtitle_text_color"
        android:textSize="@dimen/text_size0"
        app:layout_constraintBottom_toTopOf="@id/player_title_line"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/player_radio_title_layout"
        app:layout_constraintVertical_bias="0.229"
        />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/player_radio_controller_bar"
        android:layout_width="0dp"
        android:layout_height="@dimen/radio_player_bar_height"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/player_radio_land_guideline"
        app:layout_constraintTop_toBottomOf="@id/player_radio_image">

        <include layout="@layout/view_radio_player_play_bar" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <com.kaolafm.kradio.player.comprehensive.audio.RadioLiveItemView
        android:id="@+id/cl_live"
        android:layout_width="0dp"
        android:layout_height="@dimen/y104"
        android:layout_marginTop="@dimen/y51"
        android:layout_marginEnd="@dimen/all_view_left_right_distance"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="@id/player_radio_play_list"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/player_title_line" />


    <com.kaolafm.kradio.player.comprehensive.play.view.AIRadioPlusFeedbackView
        android:id="@+id/ai_radio_plus_feed_back_view"
        android:layout_width="@dimen/m85"
        android:layout_height="@dimen/m85"
        android:layout_marginBottom="@dimen/m30"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/player_radio_image"
        app:layout_constraintRight_toRightOf="@id/player_radio_image" />

    <com.kaolafm.kradio.player.comprehensive.play.widget.RadioPlayListContent
        android:id="@+id/player_radio_play_list"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/player_radio_land_guideline"
        app:layout_constraintRight_toRightOf="@id/player_radio_subscribe_layout"
        app:layout_constraintTop_toBottomOf="@id/cl_live" />

    <com.kaolafm.kradio.common.widget.RadioPlayerImageAnimLayout
        android:id="@+id/player_radio_image_anim_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toTopOf="@id/player_radio_image"
        app:layout_constraintLeft_toLeftOf="@id/player_radio_image"
        app:layout_constraintRight_toRightOf="@id/player_radio_image" />
</androidx.constraintlayout.widget.ConstraintLayout>
