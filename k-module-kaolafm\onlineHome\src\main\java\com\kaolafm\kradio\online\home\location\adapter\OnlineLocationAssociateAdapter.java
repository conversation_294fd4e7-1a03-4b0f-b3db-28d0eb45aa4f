package com.kaolafm.kradio.online.home.location.adapter;

import android.view.View;
import android.view.ViewGroup;

import com.kaolafm.kradio.k_kaolafm.R; 
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.online.home.location.bean.OnlineRecomandCityBean;
import com.kaolafm.kradio.online.search.views.KeywordTextView;

import java.util.ArrayList;
import java.util.List;
 

public class OnlineLocationAssociateAdapter extends BaseAdapter<OnlineRecomandCityBean> {

    @Override
    protected BaseHolder<OnlineRecomandCityBean> getViewHolder(ViewGroup parent, int viewType) {
        return new AssociateHolder(inflate(parent, R.layout.online_item_associate_list, viewType));
    }

    public class AssociateHolder extends BaseHolder<OnlineRecomandCityBean> {
       
        KeywordTextView mTvAssociateWord;

        public AssociateHolder(View itemView) {
            super(itemView);
            mTvAssociateWord = itemView.findViewById(R.id.tv_associate_word);
        }

        @Override
        public void setupData(OnlineRecomandCityBean associateInfo, int position) {
            List<String> keywords = new ArrayList<>();
            keywords.add(associateInfo.keyword);
            mTvAssociateWord.setKeywordsAndColor(associateInfo.name,
                    keywords, ResUtil.getColor(R.color.online_search_history_text_color));
        }
    }
}
