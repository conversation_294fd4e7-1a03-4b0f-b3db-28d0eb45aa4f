<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mine_setting_acoustics_ll"
    android:layout_width="@dimen/x208"
    android:layout_height="@dimen/y84"
    android:layout_marginStart="@dimen/m2"
    android:layout_marginEnd="@dimen/m2"
    android:gravity="center"
    android:orientation="vertical"
    tools:background="@drawable/tab_setting_indicator_bg">

    <TextView
        android:id="@+id/mine_setting_acoustics_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/mine_setting_acoustics_text2"
        android:textColor="@color/online_setting_tab_text_color"
        android:textSize="@dimen/m24"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/mine_setting_acoustics_tips_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y10"
        android:text="@string/mine_setting_acoustics_text3"
        android:textColor="@color/online_setting_tab_text_color_50"
        android:textSize="@dimen/m20" />
</LinearLayout>