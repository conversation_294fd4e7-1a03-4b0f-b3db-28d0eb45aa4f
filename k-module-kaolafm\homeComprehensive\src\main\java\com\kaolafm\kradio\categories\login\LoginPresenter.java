package com.kaolafm.kradio.categories.login;

import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.kradio.categories.CategoryConstant;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.music.bean.QQMusicLoginInfoManager;
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.opensdk.api.music.qq.QQMusicRequest;
import com.kaolafm.opensdk.api.music.qq.model.SongMenu;
import com.kaolafm.opensdk.api.music.qq.model.TencentUserInfo;
import com.kaolafm.opensdk.api.music.qq.model.VipInfo;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.trello.rxlifecycle3.android.FragmentEvent;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URL;
import java.util.List;

import io.reactivex.Single;
import io.reactivex.SingleObserver;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

/**
 * <AUTHOR>
 * @date 2018/5/1
 */

class LoginPresenter extends BasePresenter<LoginModel, ILoginView> {

    private QQMusicRequest mMusicRequest;

    public LoginPresenter(ILoginView view) {
        super(view);
        mMusicRequest = new QQMusicRequest().bindLifecycle(((BaseFragment)view).bindUntilEvent(FragmentEvent.DESTROY_VIEW));
    }

    /**
     * 加载qq登录二维码
     */
    public void loadQQQRCodeForLogin() {
    }

    /**
     * 加载微信登录二维码
     */
    public void loadWeChatQRCodeForLogin() {
        mMusicRequest.getWeChatQRCodeForLogin(new HttpCallback<String>() {
            @Override
            public void onSuccess(String s) {
                modifyWechatQrStyle(s);
            }

            @Override
            public void onError(ApiException e) {
                Log.i("LoginPresenter", "onError: "+e);
            }
        });
    }

    /**
     * 修改微信二维码登录html样式，使只显示一个二维码图片
     */
    private void modifyWechatQrStyle(String html) {
        Single.fromCallable(() -> {
            URL url = new URL(html);
            InputStream is = url.openStream();
            BufferedReader br = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder sb = new StringBuilder();
            while ((line = br.readLine()) != null) {
                line = line.trim();
                if (line.contains("<meta charset=\"utf-8\">")) {
                    line = "<meta charset=\"utf-8\" name=\"viewport\" content=\"width=280px\">";
                }
                if (line.contains("class=\"title\"")) {
                    line = line.replace("class=\"title\"", "class=\"title\" style=\"display:none\"");
                }
                if (line.contains("class=\"info\"")) {
                    line = line.replace("class=\"info\"", "class=\"info\" style=\"display:none\"");
                }
                if (line.contains("<body")) {
                    line = line.replace("<body", "<body style=\"background-color: ''; "
                            + "padding: 0px; "
                            + "margin: 0px; "
                            + "width: 280px; "
                            + "height: 280px;\"");
                }
                if (line.contains("<img class=\"qrcode lightBorder\"")) {
                    line = line.replace("<img class=\"qrcode lightBorder\"", "<img class=\"qrcode lightBorder\""
                            + " style=\"margin:0;border:0\" ");
                }
                if (line.contains("document.body.style.backgroundColor=\"#333333\"")){
                    line = line.replace(",document.body.style.backgroundColor=\"#333333\"", "");
                }
                if (line.contains("document.body.style.padding=\"50px\"")) {
                    line = line.replace(",document.body.style.padding=\"50px\"", "");
                }
                sb.append(line).append("\n");
            }
            is.close();
            return sb.toString();
        }).subscribeOn(Schedulers.io())
                .unsubscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new SingleObserver<String>() {

                    private Disposable mDisposable;

                    @Override
                    public void onSubscribe(Disposable d) {
                        mDisposable = d;

                    }

                    @Override
                    public void onSuccess(String s) {
                        if (mView != null) {
                            mView.loadWebView(s);
                        }
                        dispose(mDisposable);
                    }

                    @Override
                    public void onError(Throwable e) {
                        dispose(mDisposable);
                    }
                });
    }

    private void dispose(Disposable disposable) {
        if (disposable != null && !disposable.isDisposed()) {
            disposable.dispose();
        }
    }

    public void login(int loginType, String url) {
        String code = StringUtil.getRequestParams(url).get("code");
        HttpCallback<TencentUserInfo> callback = new HttpCallback<TencentUserInfo>() {
            @Override
            public void onSuccess(TencentUserInfo tencentUserInfo) {
                loginSuccess(tencentUserInfo, loginType);
            }

            @Override
            public void onError(ApiException e) {
                loginFail(e);
            }
        };
        if (!TextUtils.isEmpty(code)) {
            if (loginType == CategoryConstant.LOGIN_TYPE_WECHAT) {
                mMusicRequest.loginQQMusicWithWechatAuthorizationCode(code, callback);
            } else {
                mMusicRequest.loginQQMusicWithQQAuthorizationCode(code, callback);
            }
        }
    }

    private void loginSuccess(TencentUserInfo tencentUserInfo, int loginType) {
        QQMusicLoginInfoManager infoManager = QQMusicLoginInfoManager.getInstance();
        infoManager.setLoginType(loginType);
        infoManager.saveInfo(tencentUserInfo);

        //获取我喜欢的id
        mMusicRequest.getSelfSongMenuList(new HttpCallback<List<SongMenu>>() {
            @Override
            public void onSuccess(List<SongMenu> songMenus) {
                if (songMenus != null && songMenus.size() > 0) {
                    SongMenu songMenu = songMenus.get(0);
                    if (TextUtils.equals(songMenu.getDissName(), ResUtil.getString(R.string.i_like))) {
                        infoManager.setMyLikeId(songMenu.getDissId());
                    }
                }
            }

            @Override
            public void onError(ApiException e) {

            }
        });

        mMusicRequest.getUserVipInfo(new HttpCallback<VipInfo>() {
            @Override
            public void onSuccess(VipInfo vipInfo) {
                infoManager.saveVipInfo(vipInfo);
                if (mView != null) {
                    mView.loginSuccess();
                }
            }

            @Override
            public void onError(ApiException e) {
                //会员信息请求错误也要显示账号信息
                if (mView != null) {
                    mView.loginSuccess();
                }
            }
        });

    }

    private void loginFail(Throwable throwable) {
        Log.i("LoginPresenter", "loginFail: throwable=" + throwable);
    }

    @Override
    public void destroy() {
        super.destroy();
    }
}
