package com.kaolafm.kradio.bluetooth;

import android.app.Activity;
import android.content.Context;
import android.os.Build;
import androidx.annotation.RequiresApi;
import android.util.Log;
import android.view.Display;

import com.ecarx.sdk.ECarXAPIBase;
import com.ecarx.sdk.openapi.ECarXApiClient;
import com.ecarx.sdk.vehicle.VehicleAPI;
import com.ecarx.sdk.vehicle.VehicleZone;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

import static com.ecarx.sdk.ECarXAPIBase.SUPPORT_AUTH_VERSION;

public class VehicleDisplayManager {
    private static final String TAG = VehicleDisplayManager.class.getSimpleName();

    private static VehicleDisplayManager instance;

    private static int mPsdDisplayId;
    private static int mDisplayId = -1;
    private static boolean isInitSuccess;

    VehicleAPI mVehicleAPI;

    private VehicleDisplayManager() {
    }

    public static VehicleDisplayManager getInstance() {
        if (instance == null) {
            instance = new VehicleDisplayManager();
        }

        return instance;
    }

    public int getPsdDisplayId() {
        return mPsdDisplayId;
    }

    public boolean isInitSuccess() {
        return isInitSuccess;
    }

    /**
     * 屏幕id变化的时候暂停播放，第一次打开app的时候mDisplayId是-1
     */
    public void initDisplayId() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            if (mDisplayId != -1 && mDisplayId != getCurrentDisplayId()) {
                if (PlayerManagerHelper.getInstance().isPlaying()) {
                    PlayerManagerHelper.getInstance().pause(false);
                }
            }
            mDisplayId = getCurrentDisplayId();
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    public int getCurrentDisplayId() {
        Activity activity = AppManager.getInstance().getTopActivity();
        int currentDisplayId = 0;
        try {
            Method method = activity.getClass().getMethod("getDisplay");
            method.setAccessible(true);
            Display display = (Display) method.invoke(activity);
            if (display != null) {
                currentDisplayId = display.getDisplayId();
            }
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            e.printStackTrace();
        }
        return currentDisplayId;
    }

    public void initVehicle(Context context) {
        Log.i(TAG, "initVehicle");
        try{
            if (ECarXAPIBase.VERSION_INT >= SUPPORT_AUTH_VERSION) {
                mVehicleAPI = VehicleAPI.get(context);
                //异步初始化模块服务
                mVehicleAPI.init(context, callback);
            }
        } catch(Throwable ignore){
            Log.e(TAG, "error=" + ignore);
        }
    }
    /**
     * 模块初始化的回调接口
     */
    private final ECarXApiClient.Callback callback = new ECarXApiClient.Callback(){
        @Override
        public void onAPIReady(boolean ready) {
            //客户端只需要根据 ready 来判断是否可以调用 OpenAPI 接口
            isInitSuccess = ready;
            if (ready) {
                if (mVehicleAPI != null) {
                    mPsdDisplayId = mVehicleAPI.getCarInfo().getDisplay(
                            VehicleZone.ZONE_ROW_1_RIGHT).getDisplayId();
                }
            }
        }
    };
}
