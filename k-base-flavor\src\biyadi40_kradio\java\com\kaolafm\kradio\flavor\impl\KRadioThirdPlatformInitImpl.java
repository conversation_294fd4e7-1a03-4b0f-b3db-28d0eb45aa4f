package com.kaolafm.kradio.flavor.impl;

import com.kaolafm.kradio.lib.base.flavor.KRadioThirdPlatformInitInter;
import com.kaolafm.opensdk.player.core.utils.PlayerCustomizeManager;


/**
 * <AUTHOR>
 **/
public class KRadioThirdPlatformInitImpl implements KRadioThirdPlatformInitInter {
    private static final String TAG = "KRadioThirdPlatformInitImpl";
    @Override
    public boolean initThirdPlatform(Object... args) {
        PlayerCustomizeManager.getInstance().setNeedRequestAudioFocus(true);
        return true;
    }

    @Override
    public boolean destroyThirdPlatform(Object... args) {
        return false;
    }
}