package com.kaolafm.kradio.component.ui.activitycard;

import androidx.annotation.NonNull;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.kaolafm.base.utils.DateUtil;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.component.ui.R;
import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.component.ui.base.view.KradioTextView;
import com.kaolafm.kradio.component.ui.base.CellBinder;
import com.kaolafm.kradio.component.ui.base.ItemClickSupport;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.UrlUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.opensdk.api.operation.model.column.ActivityDetailColumnMember;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.util.ReportConstants;
import com.kaolafm.report.util.ReportParameterManager;

/**
 * 首页活动组件
 */
public class ComponentActivityCell extends HomeCell implements CellBinder<View, ComponentActivityCell>, ItemClickSupport {
    KradioTextView titleTextView;
    TextView desTextView;
    ImageView qrCodeImage;
    ImageView item_activitys_bg_iv;
    TextView activity_details_tv;
    ImageView pic_image;
    KradioTextView date_activity;

    private ActivityDetailColumnMember data;
    private LinearLayout row_time;

    private View getAndInitViewByType(String type, View root){
        View acticity_item = root.findViewById(R.id.acticity_item);
        View acticity_btn_item = root.findViewById(R.id.acticity_btn_item);

        if (type.equals("1")) {
            acticity_item.setVisibility(View.GONE);
            acticity_btn_item.setVisibility(View.VISIBLE);
            return acticity_btn_item;
        } else{
            acticity_item.setVisibility(View.VISIBLE);
            acticity_btn_item.setVisibility(View.GONE);
            return acticity_item;
        }
    }
    @Override
    public void mountView(@NonNull ComponentActivityCell dataCell, @NonNull View view, int position) {
        //没有contentList，后面的设置点击事件也没有意义了
        if (dataCell == null || ListUtil.isEmpty(dataCell.getContentList())) return;

        if (dataCell.getContentList().get(0) instanceof ActivityDetailColumnMember) {
            data = (ActivityDetailColumnMember) dataCell.getContentList().get(0);
        }

        if (TextUtils.isEmpty(data.getActivityType())) {
            data.setActivityType("0");
        }

        View target = getAndInitViewByType(data.getActivityType(), view);
        titleTextView = target.findViewById(R.id.title_activity);
        desTextView = target.findViewById(R.id.des_activity);
        item_activitys_bg_iv = target.findViewById(R.id.item_activitys_bg_iv);
        date_activity = target.findViewById(R.id.date_activity);
        row_time = target.findViewById(R.id.row_time);
        titleTextView.setText(StringUtil.getMaxSubstring(data.getTitle(), 15));
        desTextView.setMaxLines(2);
        desTextView.setText(StringUtil.getMaxSubstring(data.getDescription() + "", 30));

        if (data.getActivityType().equals("0")) {
            //无按钮类型
            qrCodeImage = target.findViewById(R.id.qrCode_image);

            String picUrl = UrlUtil.getCardPicUrl(data.getImageFiles());
            ImageLoader.getInstance().displayImage(AppDelegate.getInstance().getContext(), picUrl, qrCodeImage);

            item_activitys_bg_iv.setBackgroundResource(R.drawable.comprehensive_activity_item_blue_no_btn);
            row_time.setVisibility(View.GONE);
        } else if (data.getActivityType().equals("1")) {
            pic_image = target.findViewById(R.id.pic_image);
            String picUrl = UrlUtil.getCardPicUrl(data.getImageFiles());
            ImageLoader.getInstance().displayCircleImage(AppDelegate.getInstance().getContext(), picUrl, pic_image);

            activity_details_tv = target.findViewById(R.id.activity_details_tv);
            if (!TextUtils.isEmpty(data.getButtonContent()))
                activity_details_tv.setText(data.getButtonContent());

            setItemStyle(data.getStyleType());
            String start = DateUtil.formatMillis("yyyy-MM-dd", data.getActivityStartTime());
            String end = DateUtil.formatMillis("yyyy-MM-dd", data.getActivityEndTime());
            date_activity.setText(String.format("%s — %s", start, end));
            row_time.setVisibility(View.VISIBLE);
        }


        view.setContentDescription(StringUtil.getMaxSubstring(data.getTitle(), 8));
        view.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onViewClickListener != null) {
                    v.setTag(0);
                    onViewClickListener.onViewClick(v, getPositionInParent());
                }
                if (activity_details_tv != null && activity_details_tv.getVisibility() == View.VISIBLE) {
                    ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_ACTIVITY_CARD_OPEN_DETAIL, activity_details_tv.getText().toString(), ReportParameterManager.getInstance().getPage(), ReportConstants.CONTROL_TYPE_SCREEN));
                }
            }
        });
    }

    @Override
    public int getItemType() {
        return R.layout.component_activity_layout;
    }
    /**
     * 设置item主题样式
     *
     * @param type
     */
    private void setItemStyle(String type) {
        switch (type) {
            case "1":
                item_activitys_bg_iv.setBackgroundResource(R.drawable.comprehensive_activity_item_green);
                break;
            case "2":
                item_activitys_bg_iv.setBackgroundResource(R.drawable.comprehensive_activity_item_blue);
                break;
            case "3":
                item_activitys_bg_iv.setBackgroundResource(R.drawable.comprehensive_activity_item_red);
                break;
            case "4":
                item_activitys_bg_iv.setBackgroundResource(R.drawable.comprehensive_activity_item_yellow);
                break;
            case "5":
                item_activitys_bg_iv.setImageResource(R.drawable.comprehensive_activity_item_pink);
                break;
            case "6":
                item_activitys_bg_iv.setImageResource(R.drawable.comprehensive_activity_item_purple);
                break;
        }
    }

    private BaseHolder.OnViewClickListener onViewClickListener;

    @Override
    public void setOnItemClickListener(BaseHolder.OnViewClickListener listener) {
        onViewClickListener = listener;
    }

    /**
     * 曝光
     */
    public void exposure() {
        if (activity_details_tv != null && activity_details_tv.getVisibility() == View.VISIBLE) {
            ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_ACTIVITY_CARD_OPEN_DETAIL, activity_details_tv.getText().toString(), ReportParameterManager.getInstance().getPage(), ReportConstants.CONTROL_TYPE_SCREEN));
        }
    }
}
