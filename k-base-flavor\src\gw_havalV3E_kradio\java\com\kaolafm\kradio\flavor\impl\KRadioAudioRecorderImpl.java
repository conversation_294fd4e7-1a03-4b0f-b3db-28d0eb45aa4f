//package com.kaolafm.kradio.flavor.impl;
//
//import com.kaolafm.kradio.lib.base.flavor.KRadioAudioRecorderInter;
//import com.kaolafm.kradio.lib.base.flavor.live.KradioRecorderInterface;
//
///******************************************
// * 类描述:
// *
// * @version: V1.0
// * @author: yangshaoning
// * @time: 2020-06-02 12:07
// ******************************************/
//public class KRadioAudioRecorderImpl implements KRadioAudioRecorderInter {
//    @Override
//    public boolean initVR(Object... args) {
//        return false;
//    }
//
//    @Override
//    public boolean onAudioRecordStart(Object... args) {
//        return false;
//    }
//
//    @Override
//    public boolean onAudioRecordStop(Object... args) {
//        return false;
//    }
//
//    @Override
//    public KradioRecorderInterface getRecorder() {
//        return null;
//    }
//
//    @Override
//    public void setVrStatusListener(OnVRStatusListener onVRStatusListener) {
//
//    }
//}
