package com.kaolafm.kradio.flavor.impl;

import android.annotation.SuppressLint;
import android.app.ActivityManager;
import android.content.Context;
import android.util.Log;


import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioPlayLogicInter;

import com.kaolafm.opensdk.player.core.listener.OnPlayLogicListener;

import java.util.List;

import com.kaolafm.opensdk.player.core.utils.PlayerCustomizeManager;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-08-07 15:39
 ******************************************/
public final class KRadioAudioPlayLogicImpl implements KRadioAudioPlayLogicInter {
    private static final String TAG = "KRadioAudioPlayLogicImpl";

    public KRadioAudioPlayLogicImpl() {
        PlayerCustomizeManager.getInstance().setPlayLogicListener(new OnPlayLogicListener() {
            @SuppressLint("LongLogTag")
            @Override
            public boolean onPlayLogicDispose() {
                int currentFocus = PlayerManager.getInstance().getCurrentAudioFocusStatus();
                Log.i(TAG, "isAppOnForeground--------->currentFocus = " + currentFocus);
                if ((currentFocus < 0)) {
                    boolean isAppOnForeground = isAppOnForeground();
                    Log.i(TAG, "isAppOnForeground--------->isAppOnForeground = " + isAppOnForeground);
                    return !isAppOnForeground;
                }
                return false;
            }
        });
    }

    @Override
    public boolean autoPlayAudio(Object... args) {
        return false;
    }

    @Override
    public boolean requestAudioFocus(Object... args) {
        boolean flag = PlayerManager.getInstance().requestAudioFocus();
        return flag;
    }

    @Override
    public boolean resumeAudioPlayLogic(Object... args) {
        recoverPlay();
        return true;
    }

    @SuppressLint("LongLogTag")
    private void recoverPlay() {
        PlayerManager playerManager = PlayerManager.getInstance();

        Log.i(TAG, "recoverPlay---------->PlayerManager.isPausedFromUser() = " + playerManager.isPauseFromUser()
                + "          PlayerManager.getCurrentAudioFocusStatus() = " + playerManager.getCurrentAudioFocusStatus());
        if (playerManager.isPauseFromUser()) {
            if (playerManager.getCurrentAudioFocusStatus() < 0) {
                requestAudioFocus();
            }
            return;
        }
        requestAudioFocus();
        if (!playerManager.isPlaying()) {
            PlayerManagerHelper.getInstance().switchPlayerStatus(false);
        }
    }

    @Override
    public boolean doStartInPlay(Object... args) {
        return false;
    }


    /**
     * 监测APP是否在前台运行
     *
     * @return true为是，false为否
     */
    private boolean isAppOnForeground() {
        Context context = AppDelegate.getInstance().getContext();
        ActivityManager activityManager = (ActivityManager) context.getSystemService(
                Context.ACTIVITY_SERVICE);
        String packageName = context.getPackageName();
        List<ActivityManager.RunningAppProcessInfo> appProcesses = activityManager.getRunningAppProcesses();
        if (appProcesses == null) {
            return false;
        }
        for (ActivityManager.RunningAppProcessInfo appProcess : appProcesses) {
            if (appProcess.processName.equals(packageName)
                    && appProcess.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND) {
                return true;
            }
        }
        return false;
    }
}