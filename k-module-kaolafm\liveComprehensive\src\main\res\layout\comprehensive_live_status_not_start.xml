<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:visibility="gone"
    android:id="@+id/live_not_start_layout"
    app:layout_constraintTop_toTopOf="parent"
    app:layout_constraintLeft_toLeftOf="parent"
    app:layout_constraintRight_toRightOf="parent"
    android:layout_marginTop="@dimen/y211"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/live_not_start_tip"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/m54"
        android:textSize="@dimen/m36"
        android:textColor="#FFEEEEEE"
        android:text="距离直播还有" />

    <LinearLayout
        android:id="@+id/live_not_start_time_layout"
        app:layout_constraintTop_toBottomOf="@id/live_not_start_tip"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/m57"
        android:orientation="horizontal">

        <com.kaolafm.kradio.component.ui.base.view.KradioTextView
            android:id="@+id/live_not_start_time_hour"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#FFEEEEEE"
            android:textSize="@dimen/m48"
            android:layout_weight="0.7"
            android:text="0"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#FFEEEEEE"
            android:textSize="@dimen/m36"
            android:text="小时"/>

        <com.kaolafm.kradio.component.ui.base.view.KradioTextView
            android:id="@+id/live_not_start_time_minute"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#FFEEEEEE"
            android:textSize="@dimen/m48"
            android:layout_weight="0.7"
            android:text="00"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#FFEEEEEE"
            android:textSize="@dimen/m36"
            android:text="分钟"/>

        <com.kaolafm.kradio.component.ui.base.view.KradioTextView
            android:id="@+id/live_not_start_time_second"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#FFEEEEEE"
            android:textSize="@dimen/m48"
            android:layout_weight="0.7"
            android:text="00"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#FFEEEEEE"
            android:textSize="@dimen/m36"
            android:text="秒"/>

    </LinearLayout>

    <TextView
        app:layout_constraintTop_toBottomOf="@id/live_not_start_time_layout"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="@dimen/m40"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/m54"
        android:textSize="@dimen/m36"
        android:textColor="#FFEEEEEE"
        android:text="- 敬请期待 -" />

</androidx.constraintlayout.widget.ConstraintLayout>