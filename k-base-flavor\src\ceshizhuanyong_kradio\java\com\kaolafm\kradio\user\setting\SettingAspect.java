package com.kaolafm.kradio.user.setting;//package com.kaolafm.kradio.user.setting;
//
//import android.support.v4.app.DialogFragment;
//import android.view.Gravity;
//import com.kaolafm.kradio.common.SkinStateManager;
//import com.kaolafm.kradio.flavor.R;
//import com.kaolafm.kradio.setting.SettingFragment;
//import com.kaolafm.kradio.setting.SettingItem;
//import com.kaolafm.kradio.lib.base.AppDelegate;
//import com.kaolafm.kradio.lib.dialog.Dialogs;
//import com.kaolafm.kradio.lib.utils.ResUtil;
//import com.kaolafm.kradio.lib.utils.StringUtil;
//import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
//import com.kaolafm.kradio.setting.SkinAndQualityAdapter;
//import com.kaolafm.kradio.component.ui.base.skin.SkinHelper;
//import java.util.ArrayList;
//import java.util.List;
//import org.aspectj.lang.JoinPoint;
//import org.aspectj.lang.ProceedingJoinPoint;
//import org.aspectj.lang.annotation.After;
//import org.aspectj.lang.annotation.Around;
//import org.aspectj.lang.annotation.Aspect;
//import skin.support.SkinCompatManager;
//
///**
// * 添加换肤功能
// *
// * <AUTHOR> Yan
// * @date 2019-06-25
// */
//@Aspect
//public class SettingAspect {
//
//    @Around("execution(* com.kaolafm.kradio.setting.SettingModel.getItemList(..))")
//    public List<SettingItem> addChangeSkinItem(ProceedingJoinPoint point) throws Throwable {
//        List<SettingItem> items = (List<SettingItem>) point.proceed();
//        if (items != null) {
//            SettingItem changeSkin = new SettingItem(R.drawable.settings_one_key_change_skin, R.string.one_key_change_skin)
//                    .setFunctionId(SettingItem.ITEM_FUNCTION_CHANGE_SKIN)
//                    .setProcessType(SettingItem.TYPE_DIALOG);
//            //将一键换肤功能作为第二个item
//            items.add(1, changeSkin);
//        }
//        return items;
//    }
//
//    @After("execution(* com.kaolafm.kradio.setting.SettingFragment.onClick(..))")
//    public void onClick(JoinPoint point) throws Throwable {
//        SettingItem item = (SettingItem) point.getArgs()[0];
//        if (item.getFunctionId() == SettingItem.ITEM_FUNCTION_CHANGE_SKIN) {
//            showSkins(getSkins(), (SettingFragment) point.getTarget());
//        }
//    }
//
//    /**
//     * 获取皮肤数据
//     */
//    public List<SkinAndQualityAdapter.Item> getSkins() {
//        String curSkinName = SkinCompatManager.getInstance().getCurSkinName();
//        List<String> skins = SkinHelper.getAllSkinsFromAssets(AppDelegate.getInstance().getContext());
//        List<SkinAndQualityAdapter.Item> adapterItem = new ArrayList<>();
//
//        for (int i = 0; i < skins.size(); i++) {
//            SkinAndQualityAdapter.Item item = new SkinAndQualityAdapter.Item();
//            String skinName = skins.get(i);
//            item.title = SkinHelper.getSkinDisplayName(skinName);
//            item.skin = skinName;
//            item.check = StringUtil.equals(skinName, curSkinName);
//            adapterItem.add(item);
//        }
//        return adapterItem;
//    }
//
//    private DialogFragment fullScreenDialogFragment = null;
//
//    /**
//     * 换肤对话框
//     */
//    public void showSkins(List<SkinAndQualityAdapter.Item> skins, SettingFragment fragment) {
//
//        SkinAndQualityAdapter skinAdapter = new SkinAndQualityAdapter();
//        skinAdapter.setDataList(skins);
//        skinAdapter.selectSkin(SkinCompatManager.getInstance().getCurSkinName());
//        skinAdapter.setOnItemClickListener((view, viewType, item, position) -> {
//            skinAdapter.selectSkin(item.skin);
//            SkinCompatManager.getInstance().loadSkin(item.skin, new SkinCompatManager.SkinLoaderListener() {
//                @Override
//                public void onStart() {
//
//                }
//
//                @Override
//                public void onSuccess() {
//                    ImageLoader
//                            .getInstance().initDefault(ResUtil.getDrawable(com.kaolafm.kradio.k_kaolafm.R.drawable.media_default_pic), ResUtil.getDrawable(
//                            com.kaolafm.kradio.k_kaolafm.R.drawable.media_default_pic));
//                    SkinStateManager.getInstance().notifyLoadSkinSuccess();
//                }
//
//                @Override
//                public void onFailed(String errMsg) {
//
//                }
//            }, SkinCompatManager.SKIN_LOADER_STRATEGY_ASSETS);
//
//            SkinHelper.setSkinName(fragment.getContext(), item.skin);
//            if (fullScreenDialogFragment != null) {
//                fullScreenDialogFragment.dismiss();
//            }
//        });
//
//
//        fullScreenDialogFragment = new Dialogs.Builder()
//                .setType(Dialogs.TYPE_LIST)
//                .setGravity(Gravity.CENTER)
//                .setTitle(ResUtil.getString(R.string.one_key_change_skin))
//                .setAdapter(skinAdapter)
//                .create();
//        if (fullScreenDialogFragment != null) {
//            fullScreenDialogFragment.show(fragment.getFragmentManager(), "change_skin");
//        }
//    }
//}
