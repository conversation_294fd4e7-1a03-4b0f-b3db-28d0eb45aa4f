package com.kaolafm.kradio.flavor.impl;

import com.kaolafm.kradio.lib.base.flavor.IKLDataChangeInter;
import com.kaolafm.sdk.client.Music;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-12-23 16:28
 ******************************************/

public class IKLDataChangeImpl implements IKLDataChangeInter {

    @Override
    public <T> T changeData(T t) {
        Music music = (Music) t;
        if (music != null) {
            if (music.audioName == null) {
                music.audioName = music.albumName;
            }
        }
        return (T) music;
    }
}