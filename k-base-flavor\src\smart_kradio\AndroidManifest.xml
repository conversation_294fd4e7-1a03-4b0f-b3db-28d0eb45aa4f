<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.kaolafm.kradio.flavor">

    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <application>
        <!--亿咖通账号授权回调:*******版本-->
<!--        <activity-->
<!--            android:name="com.edog.car.stub.EntryActivity"-->
<!--            android:exported="true"-->
<!--            android:launchMode="singleTop"-->
<!--            android:theme="@android:style/Theme.NoDisplay" />-->
        <!--亿咖通新增配置信息-->
        <meta-data android:name="eos_supports_psd" android:value="true" />
        <meta-data android:name="eos_supports_multipages" android:value="true" />
        <meta-data
            android:name="eCarX_OpenAPI_AppId"
            android:value="58946fa69154a14398e63334b5d91bc3" />
        <meta-data
            android:name="eCarX_OpenAPI_AppKey"
            android:value="38ff2888cef557a8880f867ad019fc46" />

<!--        <meta-data-->
<!--            android:name="com.ecarx.membercenter.BuildInfo.APP_KEY"-->
<!--            android:value="58946fa69154a14398e63334b5d91bc3" />-->

        <activity
            android:name="${TB_MAIN_ACTIVITY}"
            android:configChanges="orientation|screenSize|keyboardHidden|locale|layoutDirection|keyboard|uiMode"
            android:exported="true"
            android:launchMode="${MAIN_ACTIVITY_LAUNCH_MODE}"
            android:screenOrientation="unspecified"
            android:theme="@style/ComprehensiveAppThemeCompat.Splash" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.MONKEY" />
            </intent-filter>
            <intent-filter>
                <action android:name="ecarx.intent.action.ECARX_VR_APP_OPEN" />
                <category android:name="android.intent.category.DEFAULT" />
                <data
                    android:host="vr.com"
                    android:path="/云听"
                    android:scheme="ecarx" />
                <data
                    android:host="vr.com"
                    android:path="/音乐"
                    android:scheme="ecarx" />
                <data
                    android:host="vr.com"
                    android:path="/多媒体"
                    android:scheme="ecarx" />
                <data
                    android:host="vr.com"
                    android:path="/收音机"
                    android:scheme="ecarx" />
                <data
                    android:host="vr.com"
                    android:path="/网络电台"
                    android:scheme="ecarx" />
                <data
                    android:host="vr.com"
                    android:path="/USB音乐"
                    android:scheme="ecarx" />
            </intent-filter>
            <intent-filter >
                <category android:name="android.intent.category.BROWSABLE" />
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <data
                    android:scheme="kradio"/><!-- 打开以app协议的URL,这个自己定义 -->
            </intent-filter>
            <meta-data
                android:name="distractionOptimized"
                android:value="true" />
        </activity>
        <receiver android:name="com.kaolafm.kradio.clientControlerForKradio.ClientReceiver">
            <intent-filter>
                <action android:name="com.edog.car.server.ACTION" />
                <action android:name="com.kaolafm.sdk.client" />
            </intent-filter>
        </receiver>
        <receiver android:name="com.kaolafm.kradio.receiver.VrReceiver">
            <intent-filter>
                <action android:name="ecarx.intent.broadcast.action.ECARX_VR_APP_CLOSE" />
                <category android:name="android.intent.category.DEFAULT" />
                <data
                    android:host="vr.com"
                    android:path="/云听"
                    android:scheme="ecarx" />
            </intent-filter>
        </receiver>
        <service android:name="com.kaolafm.kradio.service.EmptyService"/>
    </application>
</manifest>


