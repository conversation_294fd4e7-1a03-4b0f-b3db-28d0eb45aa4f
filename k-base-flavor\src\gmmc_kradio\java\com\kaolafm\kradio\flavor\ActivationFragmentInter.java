package com.kaolafm.kradio.flavor;

import android.annotation.SuppressLint;
import android.content.res.Resources;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.widget.NestedScrollView;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.method.LinkMovementMethod;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.view.ViewStub;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.kaolafm.kradio.eb.data.HideShowEBData;
import com.kaolafm.kradio.k_kaolafm.user.activate.IActivationView;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioBackKeyInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioNavBarInter;
import com.kaolafm.kradio.lib.base.flavor.KradioBackToHomeSettingInter;
import com.kaolafm.kradio.lib.base.mvp.IPresenter;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.ActivationUtils;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.KaolaAppConfigData;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.skin.SkinHelper;

import org.greenrobot.eventbus.EventBus;

/**
 * <AUTHOR> on 2018/5/2.
 * 激活页面
 */
public class ActivationFragmentInter extends BaseFragment implements IActivationView, View.OnClickListener {
    private RelativeLayout activationMainLayout;
    private TextView tv_activation_title;
    private TextView tv_activation_content;
    private TextView tv_activation_activate;
    private ImageView activationUserProtocolChoose;
    private NestedScrollView myNestedScrollView;
    private LinearLayout activationUserProtocol;
    private boolean isChooseProtocol = false;
    private Resources resources;
    private String content;
    private SpannableString spannableString;
    private int status;
    private static int STATUS_CONTENT = 0;//当前显示activation_content内容
    private static int STATUS_FUWU = 1;//当前显示activation_fuwu内容
    private static int STATUS_YINSI = 2;//当前显示activation_yinsi内容

    @Override
    protected int getLayoutId() {
        return R.layout.fragment_first_activation;
    }

    @Override
    protected IPresenter createPresenter() {
        return null;
    }

    /**
     * 解决 在9.0启辰车机. 网络慢的情况下. 多次点击激活, 会引起崩溃问题.
     */
    private boolean isActivate = false;


    public static ActivationFragmentInter getInstance() {
        ActivationFragmentInter activationFragment = new ActivationFragmentInter();
        return activationFragment;
    }

    @SuppressLint("WrongConstant")
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public void initView(View view) {
        activationMainLayout = view.findViewById(R.id.activation_main_layout);
        tv_activation_title = view.findViewById(R.id.tv_activation_title);
        tv_activation_content = view.findViewById(R.id.tv_activation_content);
        activationUserProtocol = view.findViewById(R.id.activationUserProtocol);
        tv_activation_activate = view.findViewById(R.id.tv_activation_activate);
        activationUserProtocolChoose = view.findViewById(R.id.activationUserProtocolChoose);
        myNestedScrollView = view.findViewById(R.id.myNestedScrollView);
        tv_activation_activate.setOnClickListener(this);
        activationUserProtocolChoose.setOnClickListener(this);
        resources = view.getResources();
        content = resources.getString(R.string.activation_content);
        spannableString = new SpannableString(content);
        spannableString.setSpan(new ClickSpan(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showClose(STATUS_FUWU, R.string.terms_of_use_content_str, R.string.terms_of_use_str);
            }
        }), 30, 34, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

        spannableString.setSpan(new ClickSpan(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showClose(STATUS_YINSI, R.string.activation_yinsi, R.string.activation_title_yinsi);
            }
        }), 37, 41, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

        int color = resources.getColor(R.color.coloryinsiandfuwu);
        spannableString.setSpan(new ForegroundColorSpan(color), 30, 34, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(color), 37, 41, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        tv_activation_content.setText(spannableString);
        tv_activation_content.setMovementMethod(LinkMovementMethod.getInstance());//开始响应点击事件
        initSkin();
        KRadioNavBarInter kRadioNavBarInter = ClazzImplUtil.getInter("KRadioNavBarImpl");
        if (kRadioNavBarInter != null) {
            kRadioNavBarInter.initActivationHomeUI(view);
        } else if (KaolaAppConfigData.getInstance().isShowActivationHomeBack()) {
            ViewStub viewStub = view.findViewById(R.id.avition_back_home_layout);
            RelativeLayout relativeLayout = (RelativeLayout) viewStub.inflate();
            ImageView kradioBack = relativeLayout.findViewById(R.id.kradio_nav_back);
            ImageView kradioHome = relativeLayout.findViewById(R.id.kradio_nav_home);
            kradioBack.setOnClickListener(v -> {

                if (AntiShake.check(v.getId())) {
                    return;
                }

                KRadioBackKeyInter kRadioBackKeyInter = ClazzImplUtil.getInter("KRadioBackKeyImpl");
                if (kRadioBackKeyInter != null) {
                    kRadioBackKeyInter.onBackPressed(getActivity());
                } else {
                    getActivity().finish();
                }
            });

            kradioHome.setOnClickListener(v -> {

                if (AntiShake.check(v.getId())) {
                    return;
                }
                KradioBackToHomeSettingInter guideSettingsInter = ClazzImplUtil.getInter("KradioBackToHomeSettingImpl");
                if (guideSettingsInter != null) {
                    // 适配车联天下 ，if-else 的问题等改完车联天下 统一进行修改
                    guideSettingsInter.backToHome(getContext());
                } else {
                    getActivity().moveTaskToBack(true);
                }
            });
        }

    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
    }

    @Override
    public void activationPageError(int error) {
        isActivate = false;
    }

    @Override
    public void activationSuccess() {
        HideShowEBData hideShowEBData = new HideShowEBData();
        hideShowEBData.setType(HideShowEBData.HIDE_ACTIVATE);
        EventBus.getDefault().post(hideShowEBData);
    }

    @Override
    public void activationFailure() {
        isActivate = false;
        ToastUtil.showNormal(AppDelegate.getInstance().getContext(), getResources().getString(R.string.no_net_work_str));
    }

    @Override
    public void onClick(View v) {
        if (AntiShake.check(v.getId())) {
            return;
        }

        int i = v.getId();
        if (i == R.id.tv_activation_activate) {
            if (isChooseProtocol && status == STATUS_CONTENT) {
                saveActivationInfo();
            } else if (status == STATUS_FUWU || status == STATUS_YINSI) {
                tv_activation_activate.setText(R.string.activation_start);
                tv_activation_title.setText(R.string.activation_tingban);
                tv_activation_content.setText(spannableString);
                activationUserProtocolChoose.setActivated(false);
                tv_activation_activate.setActivated(false);
                isChooseProtocol = false;
                activationUserProtocol.setVisibility(View.VISIBLE);
                status = STATUS_CONTENT;
                myNestedScrollView.scrollTo(0, 0);//服务和隐私关闭后将其重置至起始位置
            }
        } else if (i == R.id.activationUserProtocolChoose) {
            if (isChooseProtocol) {
                isChooseProtocol = false;
                activationUserProtocolChoose.setActivated(false);
                tv_activation_activate.setActivated(false);
            } else {
                isChooseProtocol = true;
                activationUserProtocolChoose.setActivated(true);
                tv_activation_activate.setActivated(true);
            }
        }
    }

    /**
     * 此函数针对BaseFragment做了空实现，不能去掉
     *
     * @param view
     */
    @Override
    protected void changeViewLayoutForStatusBar(View view) {
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    /**
     * 因为加载皮肤延迟。再显示激活页时， 皮肤还没有加载成功。
     */
    private void initSkin() {
        String skinName = SkinHelper.getPreSkinName(getContext());
        if (StringUtil.isEmpty(skinName)) {
            activationMainLayout.setBackgroundResource(R.drawable.user_blue_bg_home);
            activationUserProtocolChoose.setImageResource(R.drawable.user_blue_activation_checkbox);
        } else if (skinName.equals("yellow.skin")) {
            activationMainLayout.setBackgroundResource(R.drawable.user_yellow_bg_home);
            activationUserProtocolChoose.setImageResource(R.drawable.user_yellow_activation_checkbox);
        } else if (skinName.equals("gray.skin")) {
            activationMainLayout.setBackgroundResource(R.drawable.user_gray_bg_home);
            activationUserProtocolChoose.setImageResource(R.drawable.user_gray_activation_checkbox);
        }
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        isActivate = false;
    }

    private void showClose(int statusYinsi, int resContent, int resTitle) {
        status = statusYinsi;
        tv_activation_content.setText(resContent);
        tv_activation_title.setText(resTitle);
        tv_activation_activate.setText(R.string.activation_colse);
        tv_activation_activate.setActivated(true);
        activationUserProtocol.setVisibility(View.GONE);
    }

    private void saveActivationInfo() {
        if (NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), true)) {
//                if (isChooseProtocol && !isActivate) {
//                    isActivate = true;
//                    mActivationPresenter.initActivation();
//                }
            if (isChooseProtocol) {
                new ActivationUtils().setActivation(getActivity(), true);
                activationSuccess();
            }
        }
    }
}