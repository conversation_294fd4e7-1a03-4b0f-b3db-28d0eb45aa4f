package com.kaolafm.kradio.online.player.mvp;

import android.util.Log;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.TVPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.trello.rxlifecycle3.LifecycleProvider;
import com.trello.rxlifecycle3.android.FragmentEvent;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-04-17 11:32
 ******************************************/

public final class ListenTVProgramListPresenter extends BasePresenter<ListenTVProgramListModel, ListenTVProgramListView> {

    public ListenTVProgramListPresenter(ListenTVProgramListView view) {
        super(view);
    }

    @Override
    protected ListenTVProgramListModel createModel() {
        return new ListenTVProgramListModel(((LifecycleProvider) mView).bindUntilEvent(FragmentEvent.DESTROY_VIEW));
    }

    /**
     * 获取广播播单数据
     *
     * @param bean
     * @param updateView
     */
    public void getTVProgramList(ListenTVProgramRequestBean bean,long listenCount, boolean updateView) {
        //如果是正在播放的播单 ,就直接取播单数据
//        if (disposeCurrentPlay(bean)) {
//            return;
//        }
        Log.i("getBroadcastProgramList","bean.getListenTVid() = "+bean.getListenTVid()+" bean.getDate() = "+bean.getDate());
        mModel.getListenTVProgramList(bean.getListenTVid(), bean.getDate(),listenCount, new HttpCallback<ArrayList<PlayItem>>() {
            @Override
            public void onSuccess(ArrayList<PlayItem> playItemList) {
                onGetProgramListDataSuccess(playItemList,updateView);
            }

            @Override
            public void onError(ApiException e) {
                onGetProgramListDataError(e.getCode(), e.getMessage());
            }
        });
    }

    /**
     * 如果获取的是当前播放器播放的节目 , 直接通过播单获取
     *
     * @return
     */
    private boolean disposeCurrentPlay(BroadcastProgramRequestBean broadcastProgramRequestBean) {
        if (broadcastProgramRequestBean == null || broadcastProgramRequestBean.getDate() == null) {
            return false;
        }
        PlayItem playItem = PlayerManager.getInstance().getCurPlayItem();
        if (playItem instanceof TVPlayItem) {
            if (broadcastProgramRequestBean.getDate().equals(time2Date(((TVPlayItem) playItem).getTimeInfoData().getStartTime()))) {
                List<PlayItem> playItems = PlayerManager.getInstance().getPlayList();
                if (!ListUtil.isEmpty(playItems)) {
                    onGetProgramListDataSuccess(playItems, true);
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 将Unix时间戳转换成指定格式日期字符串
     */
    private String time2Date(Long timestamp) {
        String formats = "yyyy-MM-dd";
        return new SimpleDateFormat(formats, Locale.CHINA).format(new Date(timestamp));
    }

    private void onGetProgramListDataSuccess(List<PlayItem> playItemArrayList, boolean updateView) {
        if (mView == null) {
            return;
        }
        mView.onGetListenTvProgramListDataSuccess(playItemArrayList,updateView);
    }

    private void onGetProgramListDataError(int code, String msg) {
        if (mView == null) {
            return;
        }
        mView.onGetListenTvProgramListDataError(code, msg);
    }
}
