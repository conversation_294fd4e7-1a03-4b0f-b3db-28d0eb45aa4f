package com.kaolafm.kradio.network.poll;

import android.app.AlarmManager;
import android.app.Application;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.os.SystemClock;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.utils.NetworkMonitor;
import com.kaolafm.kradio.lib.utils.NetworkMonitor.OnNetworkStatusChangedListener;
import com.kaolafm.opensdk.socket.Client;
import com.kaolafm.opensdk.socket.ConnectErrorListener;
import com.kaolafm.opensdk.socket.ConnectLostListener;
import com.kaolafm.opensdk.socket.SocketEvent;
import com.kaolafm.opensdk.socket.SocketListener;

import java.util.HashMap;
import java.util.Map;

/**
 * 轮询拉取客户端。
 * 渠道适配的时候可以复用该类，实现方式如下
 * 在com.kaolafm.flavor.impl文件夹定义一个类如PollClientImpl extend PollClient
 *
 * <AUTHOR> Yan
 * @date 2020/6/10
 */
public class PollClient implements Client {

    // CPU优化：增加轮询间隔以降低CPU使用率
    private static final int INTERVAL_TIME = 30 * 1000; // 10s -> 30s

    private static final int LONG_INTERVAL_TIME = 20 * 60 * 1000; // 10min -> 20min

    private static final String ACTION = "PollingRequest";

    private static final String TIME = "interval_time";

    private AlarmManager mAlarmManager;

    private Application mContext;

    private HashMap<String, SocketListener> mListeners;

    private OnNetworkStatusChangedListener mOnNetworkStatusChangedListener;

    @Override
    public void create() {
        mContext = AppDelegate.getInstance().getContext();
        RequestReceiver receiver = new RequestReceiver();
        IntentFilter filter = new IntentFilter();
        filter.addAction(ACTION);
        mContext.registerReceiver(receiver, filter);
        mAlarmManager = (AlarmManager) mContext.getSystemService(Context.ALARM_SERVICE);
        mListeners = new HashMap<>();
        mOnNetworkStatusChangedListener = (newStatus, oldStatus) -> {
            if (newStatus == NetworkMonitor.STATUS_NO_NETWORK) {
                stop();
            }else {
                open();
            }
        };
        NetworkMonitor.getInstance(mContext).registerNetworkStatusChangeListener(mOnNetworkStatusChangedListener);
    }

    @Override
    public void setSocketHost(String s) {

    }

    @Override
    public void setMap(Map<String, String> hashMap) {

    }

    @Override
    public void open() {
        start(getIntent(INTERVAL_TIME), INTERVAL_TIME);
        start(getIntent(LONG_INTERVAL_TIME), LONG_INTERVAL_TIME);
        //开启定时时，立刻请求一次
        request();
    }

    private void start(PendingIntent intent, int time) {
        long triggerAtMillis = time + SystemClock.elapsedRealtime();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            mAlarmManager.setExactAndAllowWhileIdle(AlarmManager.ELAPSED_REALTIME, triggerAtMillis, intent);
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            mAlarmManager.setExact(AlarmManager.ELAPSED_REALTIME, triggerAtMillis, intent);
        } else {
            mAlarmManager.set(AlarmManager.ELAPSED_REALTIME, triggerAtMillis, intent);
        }
    }

    private PendingIntent getIntent(int time) {
        Intent intent = new Intent(ACTION);
        intent.putExtra(TIME, time);
        return PendingIntent.getBroadcast(mContext, time, intent, PendingIntent.FLAG_UPDATE_CURRENT);
    }

    @Override
    public void reset() {

    }

    @Override
    public void release() {
        mAlarmManager.cancel(getIntent(INTERVAL_TIME));
        mAlarmManager.cancel(getIntent(LONG_INTERVAL_TIME));
        mListeners.clear();
        NetworkMonitor.getInstance(mContext).removeNetworkStatusChangeListener(mOnNetworkStatusChangedListener);
    }

    @Override
    public <T> void addListener(SocketListener<T> socketListener) {
        if (socketListener != null) {
            mListeners.put(socketListener.getEvent(), socketListener);
        }
    }

    @Override
    public void removeListener(SocketListener listener) {
        mListeners.remove(listener);
    }

    @Override
    public <T> void request(Object msg, SocketListener<T> listener) {
        if (msg instanceof Map) {
            addListener(listener);
            PollingRequestManager.getInstance().request(listener.getEvent(), (Map<String, Object>) msg, listener);
        }
    }

    @Override
    public <T> void request(SocketListener<T> listener) {
        if (listener != null) {
            request(listener.getParams(new HashMap<>()), listener);
        }
    }

    private void request() {
        for (SocketListener listener : mListeners.values()) {
            request(listener);
        }
    }
    private void request(String event) {
        SocketListener listener = mListeners.get(event);
        if (listener != null) {
            request(listener);
        }
    }

    /**
     * 停止轮询
     */
    private void stop() {
        mAlarmManager.cancel(getIntent(INTERVAL_TIME));
        mAlarmManager.cancel(getIntent(LONG_INTERVAL_TIME));
    }

    @Override
    public boolean canEmit() {
        return mAlarmManager != null;
    }

    @Override
    public void registerConnectErrorListener(ConnectErrorListener connectErrorListener) {

    }

    @Override
    public void registerConnectLostListener(ConnectLostListener connectLostListener) {

    }

    private class RequestReceiver extends BroadcastReceiver {

        @Override
        public void onReceive(Context context, Intent intent) {
            int time = INTERVAL_TIME;
            if (intent != null) {
                time = intent.getIntExtra(TIME, INTERVAL_TIME);
            }
            start(getIntent(time), time);
            if (time == LONG_INTERVAL_TIME) {
                request(SocketEvent.HOME_PAGE_REFRESH);
            }else {
                for (SocketListener listener : mListeners.values()) {
                    if (listener != null && !SocketEvent.HOME_PAGE_REFRESH.equals(listener.getEvent())) {
                        request(listener);
                    }
                }
            }
        }
    }
}
