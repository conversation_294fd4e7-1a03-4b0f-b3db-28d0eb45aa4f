<?xml version="1.0" encoding="utf-8"?>
<com.kaolafm.kradio.lib.widget.SquareFrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:siv="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.kaolafm.kradio.lib.widget.square.SquareImageView
        android:id="@+id/iv_radio_channel_cover"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:contentDescription="@null"
        siv:canScale="false"
        tool:src="@drawable/online_what_i_like" />

    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/color_black_60_transparent" />

    <ViewStub
        android:id="@+id/vs_layout_playing"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout="@layout/online_layout_playing_square_item" />

    <TextView
        android:id="@+id/tv_radio_channel_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:ellipsize="end"
        android:gravity="center"
        android:maxEms="5"
        android:maxLines="2"
        android:textColor="@color/text_color_1"
        android:textSize="@dimen/text_size3"
        tool:text="搞笑频道搞笑频道到搞笑频道搞笑频道到搞笑频道搞笑频道到" />
</com.kaolafm.kradio.lib.widget.SquareFrameLayout>
