<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_view"
    android:layout_width="match_parent"
    android:layout_height="@dimen/item_subcategory_category_height"
    android:background="@drawable/category_item_broadcast_group_bg">

    <TextView
        android:id="@+id/tv_broadcast_category_name"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_gravity="center"
        android:gravity="center"
        android:textColor="@color/category_item_broadcast_group_text_color"
        android:textSize="@dimen/all_ctg_broadcast_text_size"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="新闻台" />
</androidx.constraintlayout.widget.ConstraintLayout>