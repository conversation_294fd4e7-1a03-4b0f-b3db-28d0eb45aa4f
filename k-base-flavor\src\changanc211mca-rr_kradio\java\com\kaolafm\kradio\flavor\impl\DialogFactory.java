package com.kaolafm.kradio.flavor.impl;

import androidx.fragment.app.DialogFragment;
import android.util.Log;
import android.view.Gravity;

import com.kaolafm.kradio.flavor.R;
import com.kaolafm.kradio.lib.base.flavor.DialogCreator;
import com.kaolafm.kradio.lib.dialog.Dialogs;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.view.BottomDialogFragment;
import com.kaolafm.kradio.view.Center2BtnDialogFragment;
import com.kaolafm.kradio.view.Center2BtnWithTitleDialogFragment;
import com.kaolafm.kradio.view.FullScreenDialogFragment;
import com.kaolafm.kradio.view.PreferencesDialogFragment;

public class DialogFactory implements DialogCreator {

    private static final String TAG = "DialogFactory impl:";

    @Override
    public DialogFragment create(Dialogs.Builder builder) {
        DialogFragment dialog = null;

        if (builder == null) {
            dialog = Center2BtnDialogFragment.create();
            return dialog;
        }

        if (builder.getType() == Dialogs.TYPE_2BTN && builder.getGravity() == Gravity.CENTER) {
            Log.i(TAG, "TYPE_2BTN");
            dialog = Center2BtnDialogFragment.create();
            ((Center2BtnDialogFragment) dialog).setOutCancel(builder.isOutCancel());
            ((Center2BtnDialogFragment) dialog).setMessage(builder.getMessage());
            ((Center2BtnDialogFragment) dialog).setLeftButton(builder.getLeftBtnText());
            ((Center2BtnDialogFragment) dialog).setRightButton(builder.getRightBtnText());
            ((Center2BtnDialogFragment) dialog).setOnNativeListener(builder.getOnNativeListener());
            ((Center2BtnDialogFragment) dialog).setOnPositiveListener(builder.getOnPositiveListener());
            ((Center2BtnDialogFragment) dialog).setOnOutsideClickListener(builder.getOnOutsideClickListener());
            ((Center2BtnDialogFragment) dialog).setCanShowButton(builder.isCanShowButton());
        } else if (builder.getType() == Dialogs.TYPE_2BTN_WITH_TITLE) {
            Log.i(TAG, "TYPE_2BTN_WITH_TITLE");
            dialog = Center2BtnWithTitleDialogFragment.create();
            ((Center2BtnWithTitleDialogFragment) dialog).setWidth(ResUtil.getDimen(R.dimen.m675));
            ((Center2BtnWithTitleDialogFragment) dialog).setOutCancel(builder.isOutCancel());
            ((Center2BtnWithTitleDialogFragment) dialog).setTitle(builder.getTitle());
            ((Center2BtnWithTitleDialogFragment) dialog).setMessage(builder.getMessage());
            ((Center2BtnWithTitleDialogFragment) dialog).setLeftButton(builder.getLeftBtnText());
            ((Center2BtnWithTitleDialogFragment) dialog).setRightButton(builder.getRightBtnText());
            ((Center2BtnWithTitleDialogFragment) dialog).setOnNativeListener(builder.getOnNativeListener());
            ((Center2BtnWithTitleDialogFragment) dialog).setOnPositiveListener(builder.getOnPositiveListener());
            ((Center2BtnWithTitleDialogFragment) dialog).setOnOutsideClickListener(builder.getOnOutsideClickListener());
            ((Center2BtnWithTitleDialogFragment) dialog).setCanShowButton(builder.isCanShowButton());
        } else if (builder.getType() == Dialogs.TYPE_TITLE_LIST_2BTN) {
            Log.i(TAG, "TYPE_TITLE_LIST_2BTN");
            dialog = PreferencesDialogFragment.create();
            ((PreferencesDialogFragment) dialog).setOutCancel(builder.isOutCancel());
            ((PreferencesDialogFragment) dialog).setTitle(builder.getTitle());
            ((PreferencesDialogFragment) dialog).setSubtitle(builder.getMessage());
            ((PreferencesDialogFragment) dialog).setAdapter(builder.getAdapter());
            ((PreferencesDialogFragment) dialog).setLeftButton(builder.getLeftBtnText());
            ((PreferencesDialogFragment) dialog).setRightButton(builder.getRightBtnText());
            ((PreferencesDialogFragment) dialog).setOnNativeListener(builder.getOnNativeListener());
            ((PreferencesDialogFragment) dialog).setOnPositiveListener(builder.getOnPositiveListener());
            ((PreferencesDialogFragment) dialog).setOnOutsideClickListener(builder.getOnOutsideClickListener());
        } else if (builder.getType() == Dialogs.TYPE_LIST) {
            Log.i(TAG, "TYPE_LIST");
            dialog = FullScreenDialogFragment.create();
            ((FullScreenDialogFragment) dialog).setOutCancel(builder.isOutCancel());
            ((FullScreenDialogFragment) dialog).setTitle(builder.getTitle());
            ((FullScreenDialogFragment) dialog).setAdapter(builder.getAdapter());
            ((FullScreenDialogFragment) dialog).setOnOutsideClickListener(builder.getOnOutsideClickListener());
        } else {
            Log.i(TAG, "ELSE_TYPE");
            dialog = BottomDialogFragment.create();
            ((BottomDialogFragment) dialog).setOutCancel(builder.isOutCancel());
            ((BottomDialogFragment) dialog).setMessage(builder.getMessage());
            ((BottomDialogFragment) dialog).setLeftButton(builder.getLeftBtnText());
            ((BottomDialogFragment) dialog).setRightButton(builder.getRightBtnText());
            ((BottomDialogFragment) dialog).setOnNativeListener(builder.getOnNativeListener());
            ((BottomDialogFragment) dialog).setOnPositiveListener(builder.getOnPositiveListener());
            ((BottomDialogFragment) dialog).setOnOutsideClickListener(builder.getOnOutsideClickListener());
        }

        return dialog;
    }
}