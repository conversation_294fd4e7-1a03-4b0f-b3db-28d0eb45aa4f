package com.kaolafm.kradio.report.data;

import com.kaolafm.report.event.BroadcastStartListenReportEvent;

import java.util.UUID;

import static com.kaolafm.kradio.uitl.Constants.BROADCAST_PLAY_START_EVENT_CODE;
import static com.kaolafm.kradio.uitl.Constants.SCENE_REMARK_VALUE;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-03-09 17:50
 ******************************************/
public class SceneBroadcastStartListenReportEvent extends BroadcastStartListenReportEvent {


    public SceneBroadcastStartListenReportEvent(BroadcastStartListenReportEvent broadcastStartListenReportEvent) {
        super();
        setEventcode(BROADCAST_PLAY_START_EVENT_CODE);
        setAudioid(broadcastStartListenReportEvent.getAudioid());
        setRadioid(broadcastStartListenReportEvent.getRadioid());
        setStatus(broadcastStartListenReportEvent.getStatus());
    }

    /**
     * 场景推送session
     */
    private String cjsession = UUID.randomUUID().toString();

    /**
     * 场景内容code
     */
    private String cjcode;

    /**
     * 运营类型
     */
    private String cjtype;

    private String remarks1 = SCENE_REMARK_VALUE;

    public String getCjsession() {
        return cjsession;
    }

    public void setCjsession(String cjsession) {
        this.cjsession = cjsession;
    }

    public String getCjcode() {
        return cjcode;
    }

    public void setCjcode(String cjcode) {
        this.cjcode = cjcode;
    }

    public String getCjtype() {
        return cjtype;
    }

    public void setCjtype(String cjtype) {
        this.cjtype = cjtype;
    }

    public void setRemarks1(String remarks1) {
        this.remarks1 = remarks1;
    }

    public String getRemarks1() {
        return remarks1;
    }
}
