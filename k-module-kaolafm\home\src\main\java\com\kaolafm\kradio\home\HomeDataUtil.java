package com.kaolafm.kradio.home;


import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.lib.common.ContentType;
import com.kaolafm.kradio.lib.common.ResType;
import com.kaolafm.opensdk.api.operation.model.column.ActivityDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.AlbumDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.AudioDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.BroadcastDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.CategoryColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.Column;
import com.kaolafm.opensdk.api.operation.model.column.ColumnContent;
import com.kaolafm.opensdk.api.operation.model.column.ColumnGrp;
import com.kaolafm.opensdk.api.operation.model.column.ColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.FeatureDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.LiveProgramDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.PageRedirectionColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.RadioDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.RadioQQMusicDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.SearchResultColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.TVDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.TopicDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.WebViewColumnMember;
import com.kaolafm.opensdk.utils.operation.OperationAssister;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class HomeDataUtil {

    public static HomeCell getFirstCell(List<? extends ColumnGrp> columnGrps) {
        Iterator<? extends ColumnGrp> iterator = columnGrps.iterator();
        HomeCell homeCell = null;
        while (homeCell == null && iterator.hasNext()) {
            ColumnGrp columnGrp = iterator.next();
            if (columnGrp instanceof Column) {
                Column column = (Column) columnGrp;
                if (column.getColumnMembers() != null && column.getColumnMembers().size() > 0) {
                    Iterator<? extends ColumnMember> columnMemberIterator = column.getColumnMembers().iterator();
                    while (columnMemberIterator.hasNext()) {
                        ColumnMember columnMember = columnMemberIterator.next();
                        homeCell = memberToCell(columnMember);
                        List<ColumnContent> contentList = homeCell.getContentList();
                        if (ListUtil.isEmpty(contentList)) {
                            continue;
                        }
                        boolean b = false;
                        for (int i = 0; i < contentList.size(); i++) {
                            if (!(homeCell.getContentList().get(i) instanceof WebViewColumnMember)
                                    && !(homeCell.getContentList().get(i) instanceof ActivityDetailColumnMember)
                                    && !(homeCell.getContentList().get(i) instanceof TopicDetailColumnMember)
                                    && !(homeCell.getContentList().get(i) instanceof PageRedirectionColumnMember)) {
                                homeCell.getContentList().get(i).setResType(getResType(homeCell.getContentList().get(i)));
                                b = true;
                                break;
                            }
                        }
                        if (b) {
                            break;
                        }
                    }
                }
            } else {
                List<? extends ColumnGrp> columnGrpList = columnGrp.getChildColumns();
                homeCell = getFirstCell(columnGrpList);
                break;
            }
        }
        return homeCell;
    }

    /**
     * 获取播放类型
     */
    public static int getResType(ColumnMember cm) {
        int restype = ResType.ALBUM_TYPE;
        if (cm instanceof LiveProgramDetailColumnMember) {
            restype = ResType.LIVE_TYPE;
        } else if (cm instanceof SearchResultColumnMember) {

        } else if (cm instanceof BroadcastDetailColumnMember) {
            restype = ResType.BROADCAST_TYPE;
        } else if (cm instanceof AlbumDetailColumnMember) {
            restype = ResType.ALBUM_TYPE;
        } else if (cm instanceof AudioDetailColumnMember) {
            restype = ResType.AUDIO_TYPE;
        } else if (cm instanceof RadioDetailColumnMember) {
            restype = ResType.RADIO_TYPE;
        } else if (cm instanceof RadioQQMusicDetailColumnMember) {
            RadioQQMusicDetailColumnMember qqcm = (RadioQQMusicDetailColumnMember) cm;
//            0,场景电台 ;1,标签电台
            switch (qqcm.getRadioQQMusicType()) {
                case 1:
                    restype = ResType.RESOURCES_TYPE_MUSIC_RADIO_LABEL;
                    break;
                case 0:
                    restype = ResType.RESOURCES_TYPE_MUSIC_RADIO_SCENE;
                    break;
                default:
                    restype = ResType.RESOURCES_TYPE_QQ_MUSIC;
            }
        } else if (cm instanceof WebViewColumnMember) {
            //error
        } else if (cm instanceof CategoryColumnMember) {
//            1:专辑；2:广播；3:直播；4:智能电台；5:QQ音乐电台
            //功能入口，跳转，需要额外信息
            restype = ResType.FUNCTION_ENTER_SMALL;

        } else if (cm instanceof ActivityDetailColumnMember) {
            restype = ResType.ACTIVITY_TYPE;
        } else if (cm instanceof FeatureDetailColumnMember) {
            restype = ResType.FEATURE_TYPE;
        } else if (cm instanceof TVDetailColumnMember) {
            restype = ResType.TV_TYPE;
        } else if (cm instanceof TopicDetailColumnMember) {
            restype = ResType.TOPIC_TYPE;
        } else if (cm instanceof PageRedirectionColumnMember) {

        }
        Map<String, String> extInfo = cm.getExtInfo();
        return restype;
    }

    private static HomeCell memberToCell(ColumnMember columnMember) {
        HomeCell homeCell = new HomeCell();
//        String image = OperationAssister.getImage(columnMember);
//        if (picSetting != null) {
//            homeCell.imageUrl = picSetting.getHomePicUrl(image);
//        }else {
//            homeCell.imageUrl = UrlUtil.getDefaultConfigPicUrl(image);
//        }

//        homeCell.code = columnMember.getCode();
        homeCell.playId = OperationAssister.getId(columnMember);
        homeCell.name = columnMember.getTitle();
//        homeCell.parent = card;
        homeCell.resType = getResType(columnMember);
        homeCell.contentType = getContentType(columnMember);
//        homeCell.outputMode = columnMember.getOutputMode();
//        homeCell.callBack = columnMember.getCallBack();
        homeCell.vip = getVip(columnMember);
        homeCell.fine = getFine(columnMember);
        homeCell.contentList = getHomeCardContentList((List<ColumnContent>) columnMember.getContentList());
        homeCell.recommendReason = columnMember.getRecommendReason();
//        //将功能列表作为一个卡片添加到数据中。
//        homeCell = checkFunctionCell(card, functionPair, homeCell);
//        if (homeCell != null) {
//            homeCell.positionInParent = index++;
//            homeCell.isLastInParent = index == columnMembers.size();
//            homeCells.add(homeCell);
//        } else {
//            iterator.remove();
//        }

        homeCell.recommendReason = columnMember.getRecommendReason();
        if (columnMember instanceof LiveProgramDetailColumnMember) {
            homeCell.anchor = ((LiveProgramDetailColumnMember) columnMember).getAnchor();
        }
        return homeCell;
    }

    private static List<ColumnContent> getHomeCardContentList(List<ColumnContent> contentList) {
        if (contentList != null && contentList.size() > 0)
            for (int i = 0; i < contentList.size(); i++) {
                contentList.get(i).setResType(getResType(contentList.get(i)));
            }
        return contentList;
    }

    /**
     * 212 通过成员的type转换成ResType
     *
     * @param type
     * @return
     */
    private static int getHomeCardChildResType(String type) {
        int resType = ResType.NOACTION_TYPE;

        switch (type) {
            case "WebViewColumnMember"://web页面
                break;
            case "SearchResultColumnMember"://搜索类型
                break;
            case "AlbumDetailColumnMember"://专辑类型
                resType = ResType.ALBUM_TYPE;
                break;
            case "PageRedirectionColumnMember"://跳转页面类型
                break;
            case "FeatureDetailColumnMember"://专题类型
                resType = ResType.FEATURE_TYPE;
                break;
            case "ActivityDetailColumnMember"://活动类型
                resType = ResType.ACTIVITY_TYPE;
                break;
            case "TVDetailColumnMember"://听电视类型
                resType = ResType.TV_TYPE;
                break;
            case "LiveProgramDetailColumnMember"://直播类型
                resType = ResType.LIVE_TYPE;
                break;
            case "CategoryColumnMember"://分类内容类型
                resType = ResType.CATEGORY_TYPE;
                break;
            case "BroadcastDetailColumnMember"://广播类型
                resType = ResType.BROADCAST_TYPE;
                break;
            case "RadioDetailColumnMember"://电台类型
                resType = ResType.RADIO_TYPE;
                break;
            case "AudioDetailColumnMember"://单曲类型
                resType = ResType.AUDIO_TYPE;
                break;
        }
        return resType;
    }

//    private static HomeCell getCellAccordingToMember(ColumnMember columnMember) {
//        HomeCell homeCell;
//        int itemType;
//        if (columnMember instanceof BroadcastDetailColumnMember) {
//            homeCell = new BroadcastCell();
//            itemType = ResType.BROADCAST_TYPE;
//            ((BroadcastCell) homeCell).freq = ((BroadcastDetailColumnMember) columnMember).getFreq();
//        } else if (columnMember instanceof CategoryColumnMember) {
//            Map<String, String> extInfo = columnMember.getExtInfo();
//            //功能入口，小卡片
//            if (extInfo != null && ("SMALL".equals(extInfo.get("displayStyle"))
//                    || "BIG".equals(extInfo.get("displayStyle")))) {
//                if ("SMALL".equals(extInfo.get("displayStyle"))) {
//                    homeCell = new FunctionPairCell();
//                } else {
//                    homeCell = new FunctionBigCell();
//                }
//                String[] paths = getPaths(extInfo);
//                ((FunctionPairCell) homeCell).firstCode = paths[0];
//                ((FunctionPairCell) homeCell).secondCode = paths[1];
//            } else {
//                homeCell = new GoldenRatioCell();
//            }
//            itemType = ResType.FUNCTION_ENTER_SMALL;
//        } else {
//            homeCell = new GoldenRatioCell();
//            itemType = ResType.RADIO_TYPE;
//        }
//        homeCell.itemType = itemType;
//        return homeCell;
//    }

//    private static String[] getPaths(Map<String, String> extInfo) {
//        String[] codes = new String[]{"0", "0"};
//        String categoryPath = extInfo.get("categoryPath");
//        if (!TextUtils.isEmpty(categoryPath)) {
//            String[] paths = categoryPath.split("\\/");
//            if (paths.length > 0) {
//                codes[0] = paths[0];
//            }
//            if (paths.length > 1) {
//                codes[1] = paths[1];
//            }
//        }
//        return codes;
//    }

    /**
     * 获取播放类型
     */
//    private static int getResType(ColumnMember cm) {
//        int restype = ResType.NOACTION_TYPE;
//        if (cm instanceof LiveProgramDetailColumnMember) {
//            restype = ResType.LIVE_TYPE;
//        } else if (cm instanceof SearchResultColumnMember) {
//
//        } else if (cm instanceof BroadcastDetailColumnMember) {
//            restype = ResType.BROADCAST_TYPE;
//        } else if (cm instanceof AlbumDetailColumnMember) {
//            restype = ResType.ALBUM_TYPE;
//        } else if (cm instanceof AudioDetailColumnMember) {
//            restype = ResType.AUDIO_TYPE;
//        } else if (cm instanceof RadioDetailColumnMember) {
//            restype = ResType.RADIO_TYPE;
//        } else if (cm instanceof RadioQQMusicDetailColumnMember) {
//            RadioQQMusicDetailColumnMember qqcm = (RadioQQMusicDetailColumnMember) cm;
////            0,场景电台 ;1,标签电台
//            switch (qqcm.getRadioQQMusicType()) {
//                case 1:
//                    restype = ResType.RESOURCES_TYPE_MUSIC_RADIO_LABEL;
//                    break;
//                case 0:
//                    restype = ResType.RESOURCES_TYPE_MUSIC_RADIO_SCENE;
//                    break;
//                default:
//                    restype = ResType.RESOURCES_TYPE_QQ_MUSIC;
//            }
//        } else if (cm instanceof WebViewColumnMember) {
//            //error
//        } else if (cm instanceof CategoryColumnMember) {
////            1:专辑；2:广播；3:直播；4:智能电台；5:QQ音乐电台
//            //功能入口，跳转，需要额外信息
//            restype = ResType.FUNCTION_ENTER_SMALL;
//
//        }
//        return restype;
//    }

    /**
     * 获取内容类型
     */
    private static int getContentType(ColumnMember cm) {
        int contentType = ContentType.NOACTION_TYPE;
        if (cm instanceof LiveProgramDetailColumnMember) {

        } else if (cm instanceof BroadcastDetailColumnMember) {
            contentType = ((BroadcastDetailColumnMember) cm).getBroadcastSort();
        } else if (cm instanceof AlbumDetailColumnMember) {

        } else if (cm instanceof AudioDetailColumnMember) {

        } else if (cm instanceof RadioDetailColumnMember) {

        }
        return contentType;
    }

    /**
     * 是否vip 1:是,0:否
     */
    private static int getVip(ColumnMember cm) {
        int vipType = 0;
        if (cm instanceof AlbumDetailColumnMember) {
            vipType = ((AlbumDetailColumnMember) cm).getVip();
        }
        return vipType;
    }

    /**
     * 是否精品  1:是,0:否
     */
    private static int getFine(ColumnMember cm) {
        int fine = 0;
        if (cm instanceof AlbumDetailColumnMember) {
            fine = ((AlbumDetailColumnMember) cm).getFine();
        }
        return fine;
    }
}
