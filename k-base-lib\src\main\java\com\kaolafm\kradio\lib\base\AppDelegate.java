package com.kaolafm.kradio.lib.base;

import android.app.Application;
import android.content.ComponentCallbacks2;
import android.content.Context;
import android.content.res.Configuration;
import android.util.Log;

import androidx.multidex.MultiDex;

import com.getkeepsafe.relinker.ReLinker;
import com.kaolafm.kradio.component.ComponentClient;
import com.kaolafm.kradio.lib.base.flavor.KRadioIsKaolaRunningInter;
import com.kaolafm.kradio.lib.base.lifecycle.ActivityLifecycle;
import com.kaolafm.kradio.lib.base.lifecycle.ActivityLifecycleForRxLifecycle;
import com.kaolafm.kradio.lib.utils.AppUtil;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.tencent.mmkv.MMKV;

/**
 * Application代理，初始化等相关操作可以在这里对应的生命周期中进行。
 *
 * <AUTHOR>
 * @date 2018/4/13
 */

public class AppDelegate {
    private static final String TAG = "AppDelegate";

    private ActivityLifecycle mActivityLifecycle;

    private ActivityLifecycleForRxLifecycle mActivityRxLifecycle;

    private AppComponentCallbacks mAppComponentCallbacks;

    private volatile static AppDelegate mInstance;

    private Application mApplication;

    private boolean isKaolaRuning = true;

//    private boolean isDeviceIdChanged;

    private AppDelegate() {
    }

    public static AppDelegate getInstance() {
        if (mInstance == null) {
            synchronized (AppDelegate.class) {
                if (mInstance == null) {
                    mInstance = new AppDelegate();
                }
            }
        }
        return mInstance;
    }

    public void attachBaseContext(Context context) {
        // 在差的车机上，首次启动，安卓 5.0 以下版本耗时 平均14 s,首次启动慢的问题在这里。
        MultiDex.install(context);
    }

    public Application getContext() {
        return mApplication;
    }

    /**
     * app创建时回调
     *
     * @param application
     */
    public void onCreate(Application application) {
        if (!AppUtil.isMainProcess(application)) {
            return;
        }
        KRadioIsKaolaRunningInter kRadioIsKaolaRunningInter = ClazzImplUtil.getInter("KRadioIsKaolaRunningImpl");
        if (kRadioIsKaolaRunningInter != null) {
            kRadioIsKaolaRunningInter.setIsKaolaRunning(false);
        }
        mApplication = application;

        registerLifecycle(application);
        //替代SP的缓存库, 使用Relinker代替系统加载so库，以解决UnsatisfiedLinkError
        MMKV.initialize(application.getFilesDir().getAbsolutePath() + "/mmkv", libName -> ReLinker.loadLibrary(application, libName));
        ComponentClient.init(application);
    }

    private void registerLifecycle(Application application) {
        mActivityLifecycle = new ActivityLifecycle(application);
        application.registerActivityLifecycleCallbacks(mActivityLifecycle);

        mActivityRxLifecycle = new ActivityLifecycleForRxLifecycle(application);
        application.registerActivityLifecycleCallbacks(mActivityRxLifecycle);

        mAppComponentCallbacks = new AppComponentCallbacks(application);
        application.registerComponentCallbacks(mAppComponentCallbacks);

    }

    public boolean isAppForeground() {
//        Context context = AppDelegate.getInstance().getContext();
//        ActivityManager activityManager = (ActivityManager) context.getSystemService(
//                Context.ACTIVITY_SERVICE);
//        String packageName = context.getPackageName();
//        try {
//            List<ActivityManager.RunningAppProcessInfo> appProcesses = activityManager.getRunningAppProcesses();
//            if (appProcesses == null) {
//                return false;
//            }
//            for (ActivityManager.RunningAppProcessInfo appProcess : appProcesses) {
//                if (appProcess.processName.equals(packageName)
//                        && appProcess.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND) {
//                    Log.i("HubActivity", "isAppForeground------>appDelegate");
//                    return true;
//                }
//            }
//            return false;
//        } catch (Exception e) {
//            e.printStackTrace();
//        }

        if (mActivityLifecycle == null) {
            return false;
        }
        return mActivityLifecycle.isAppForeground();
    }

    /**
     * app退出时回调
     *
     * @param application
     */
    public void onTerminate(Application application) {
        if (mActivityLifecycle != null) {
            application.unregisterActivityLifecycleCallbacks(mActivityLifecycle);
        }
        if (mActivityRxLifecycle != null) {
            application.unregisterActivityLifecycleCallbacks(mActivityRxLifecycle);
        }
        if (mAppComponentCallbacks != null) {
            application.unregisterComponentCallbacks(mAppComponentCallbacks);
        }
        mActivityLifecycle = null;
        mActivityRxLifecycle = null;
        mAppComponentCallbacks = null;
    }

//    public boolean isDeviceIdChanged() {
//        return isDeviceIdChanged;
//    }
//
//    public void setDeviceIdChanged(boolean deviceIdChanged) {
//        isDeviceIdChanged = deviceIdChanged;
//    }

    public void setKaolaRuning(boolean kaolaRuning) {
        isKaolaRuning = kaolaRuning;
    }

    public boolean isKaolaRuning() {
        return isKaolaRuning;
    }

    /**
     * 是一个细粒度的内存回收管理回调。<br/>
     * Application、Activity、Service、ContentProvider、Fragment实现了ComponentCallback2<br/>
     * 实现onTrimMemory(int)方法，细粒度release 内存，参数可以体现不同程度的内存可用情况<br/>
     * 响应onTrimMemory回调：开发者的app会直接受益，有利于用户体验，系统更有可能让app存活的更持久。<br/>
     * 不响应onTrimMemory回调：系统更有可能kill 进程<br/>
     */
    private static class AppComponentCallbacks implements ComponentCallbacks2 {

        private final Application mApplication;

        public AppComponentCallbacks(Application application) {
            mApplication = application;
        }

        /**
         * 设备的配置信息发生改变时回调，比如旋转屏幕
         */
        @Override
        public void onConfigurationChanged(Configuration newConfig) {

        }

        /**
         * 内存不足,调用时机大概等同于{@link #onTrimMemory(int)}中的TRIM_MEMORY_COMPLETE．
         */
        @Override
        public void onLowMemory() {
            Log.i("AppComponentCallbacks", "onLowMemory");
        }

        /**
         * 内存的使用情况，在这里可以处理内存释放
         *
         * @param level 当你的app在后台时：<br/>
         *              <p>
         *              TRIM_MEMORY_COMPLETE ：当前进程在LRU列表的尾部，如果没有足够的内存，它将很快被杀死。这时候你应该释放任何不影响app运行的资源。<br/>
         *              <p>
         *              TRIM_MEMORY_MODERATE ：当前进程在LRU列表的中部，如果系统进一步需要内存，你的进程可能会被杀死。<br/>
         *              <p>
         *              TRIM_MEMORY_BACKGROUND：当前进程在LRU列表的头部，虽然你的进程不会被高优杀死，但是系统已经开始准备杀死LRU列表中的其他进程了，
         *              <p>
         *              因此你应该尽量的释放能够快速回复的资源，以保证当用户返回你的app时可以快速恢复。<br/>
         *              <p>
         *              当你的app的可见性改变时：<br/>
         *              <p>
         *              TRIM_MEMORY_UI_HIDDEN：当前进程的界面已经不可见，这时是释放UI相关的资源的好时机。<br/>
         *              <p>
         *              当你的app正在运行时：<br/>
         *              <p>
         *              TRIM_MEMORY_RUNNING_CRITICAL：虽然你的进程不会被杀死，但是系统已经开始准备杀死其他的后台进程了，这时候你应该释放无用资源以防止性能下降。<br/>
         *              <p>
         *              下一个阶段就是调用"onLowMemory()"来报告开始杀死后台进程了，特别是状况已经开始影响到用户。<br/>
         *              <p>
         *              TRIM_MEMORY_RUNNING_LOW：虽然你的进程不会被杀死，但是系统已经开始准备杀死其他的后台进程了，你应该释放不必要的资源来提供系统性能，否则会
         *              <p>
         *              影响用户体验。<br/>
         *              <p>
         *              TRIM_MEMORY_RUNNING_MODERATE：系统已经进入了低内存的状态，你的进程正在运行但是不会被杀死。<br/>
         */
        @Override
        public void onTrimMemory(int level) {
            Log.i("AppComponentCallbacks", "onTrimMemory: 内存情况=" + level);
            if (level == TRIM_MEMORY_COMPLETE) {
                ImageLoader.getInstance().clearMemoryCache(mApplication);
            }
        }
    }
}
