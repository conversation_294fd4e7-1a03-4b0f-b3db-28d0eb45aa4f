package com.kaolafm.kradio.lib.utils;

import android.content.Context;
import android.content.res.Configuration;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.widget.TextView;


/**
 * 处理屏幕横竖屏适配工具类
 */
public class OritationViewUtils {

    public static void handleOrientationTextSize(View itemView, TextView tv, int vSize, int hSize) {
        int orientation = ResUtil.getOrientation();
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            tv.setTextSize(TypedValue.COMPLEX_UNIT_PX, ResUtil.getTextSize(hSize));
        } else {
            tv.setTextSize(TypedValue.COMPLEX_UNIT_PX, ResUtil.getTextSize(vSize));
        }
    }

    public static void handleOrientationViewHeight(int orientation, View v, LayoutParams vl, LayoutParams hl) {
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            v.setLayoutParams(hl);
        } else {
            v.setLayoutParams(vl);
        }
    }

    public static int px2sp(float sp, Context ctx) {
        final float scale = ctx.getResources().getDisplayMetrics().scaledDensity;
        return (int) (sp / scale + 0.5f);
    }
}
