package com.kaolafm.base.internal;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.kradio.lib.base.flavor.KRadioDeviceIdInter;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.opensdk.log.Logging;

/**
 * 覆盖SDK中的DeviceId，用于添加特殊的生成方式和设置deviceId功能。
 * 包名和类名不可修改。
 *
 * <AUTHOR>
 * @date 2019-10-22
 */
public class DeviceIdUtil {
    private static final String TAG = "DeviceId";

    private static String mDeviceId;

    /**
     * 获取设备的deviceId，这个是SDK对外使用的。
     *
     * @param context
     * @return
     */
    public static String getDeviceId(Context context) {
        if (isIdInvalid(mDeviceId)) {
            DeviceIdFactory factory = DeviceIdFactory.getInstance();
            factory.set(0, AdaptedDeviceId.getInstance());
            factory.set(1, new TingBanToKRadioCacheObtainDeviceId());
            KRadioDeviceIdInter kRadioDeviceIdInter = ClazzImplUtil.getInter("KRadioDeviceIdImpl");
            if (kRadioDeviceIdInter != null) {
                kRadioDeviceIdInter.initDeviceFactory(context);
            }
            mDeviceId = factory.getDeviceId(context);
        }
        Logging.d(TAG,"getDeviceId mDeviceId = "+mDeviceId);
        return mDeviceId;
    }

    private static boolean isIdInvalid(String id) {
        return TextUtils.isEmpty(id) || "0".equals(id) || "null".equalsIgnoreCase(id);
    }

    public static void setDeviceId(String deviceId) {
        Logging.d(TAG,"setDeviceId deviceId = "+deviceId +" , mDeviceId = "+mDeviceId);
        mDeviceId = deviceId;
        AdaptedDeviceId.getInstance().saveUUID(deviceId);
    }
}
