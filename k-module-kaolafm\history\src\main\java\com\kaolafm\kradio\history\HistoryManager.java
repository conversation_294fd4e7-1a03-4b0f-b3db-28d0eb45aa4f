package com.kaolafm.kradio.history;

import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.Nullable;

import com.alibaba.fastjson.JSON;
import com.kaolafm.base.utils.SpUtil;
import com.kaolafm.kradio.component.ComponentClient;
import com.kaolafm.kradio.component.DynamicComponent;
import com.kaolafm.kradio.component.MainThreadable;
import com.kaolafm.kradio.component.RealCaller;
import com.kaolafm.kradio.constant.UserComponentConst;
import com.kaolafm.kradio.constant.UserStateObserverProcessorConst;
import com.kaolafm.kradio.history.utils.SaveHistoryPlayStateListener;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.ThirdPlatformLoginer;
import com.kaolafm.kradio.lib.base.flavor.ThirdPlatformLoginer.Callback;
import com.kaolafm.kradio.lib.basedb.GreenDaoInterface.OnQueryListener;
import com.kaolafm.kradio.lib.basedb.manager.BaseManager;
import com.kaolafm.kradio.lib.basedb.manager.HistoryDaoManager;
import com.kaolafm.kradio.lib.basedb.manager.LoginedHistoryDaoManager;
import com.kaolafm.kradio.lib.basedb.manager.Manager;
import com.kaolafm.kradio.lib.bean.BroadcastRadioSimpleData;
import com.kaolafm.kradio.lib.bean.HistoryItem;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.ComponentUtil;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.YTLogUtil;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import java.util.List;

import io.reactivex.Single;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;

/**
 * 历史管理类，用户管理登录或未登录时的历史
 * 对外暴露接口，封装内部逻辑
 *
 * <AUTHOR> Yan
 * @date 2020/8/18
 */
public class HistoryManager extends BaseManager {

    private static final String TAG = "HistoryManager";
    private boolean mLogin;

    private DynamicComponent mHistoryUserObserver;

    private final BasePlayStateListener mPlayStateListener;

    private final UploadListenHistoryManager mUploadManager;

    Manager mDaoManager;

    private HistoryManager() {
        mPlayStateListener = new SaveHistoryPlayStateListener(this);
        PlayerManager.getInstance().addPlayControlStateCallback(mPlayStateListener);
        mUploadManager = UploadListenHistoryManager.getInstance();
        try {
            mLogin = ComponentUtil.getResultValue(UserComponentConst.NAME, UserComponentConst.IS_USER_BOUND);
        } catch (Exception e) {
            Log.e(TAG, "HistoryManager: " + e.getMessage());
        }
        if (mLogin) {
            mDaoManager = LoginedHistoryDaoManager.getInstance();
        } else {
            mDaoManager = HistoryDaoManager.getInstance();
        }
    }

    public static HistoryManager getInstance() {
        return SingletonHolder.INSTANCE;
    }

    public void init() {
        Log.e(TAG, "init: ");

        if (mLogin) {
            mUploadManager.start();
        }
        mHistoryUserObserver = new HistoryUserObserver();
        ComponentUtil.addObserver(UserComponentConst.NAME, mHistoryUserObserver);
    }

    /**
     * 获取历史列表。
     * 如果已经登录就是用户的历史，如果未登录就是设备的本地历史。
     */
    @Override
    public void getHistoryList(HttpCallback<List<HistoryItem>> callback) {
        YTLogUtil.logStart("HistoryManager", "getHistoryList", "");
        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
            loadLocalHistory(callback);
        } else {
            getHistoryListWithLogin(callback);
        }
    }

    @Override
    public List<HistoryItem> getBroadcastList() {
        try {
            return mDaoManager.getBroadcastList();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public void queryLatestHistoryByAlbumId(String albumId, OnQueryListener<HistoryItem> listener) {
        mDaoManager.queryLatestHistoryByAlbumId(albumId, listener);
    }

    public void getHistoryListWithLogin(HttpCallback<List<HistoryItem>> callback) {
        YTLogUtil.logStart("HistoryManager", "getHistoryListWithLogin", "");
        try {
            mLogin = ComponentUtil.getResultValue(UserComponentConst.NAME, UserComponentConst.IS_USER_BOUND);
        } catch (Exception e) {
            e.printStackTrace();
        }
        getHistoryListWithLogin(mLogin, callback);
    }

    /**
     * 根据是否登录获取对应的历史记录。
     * 当登录状态变更的时候，有可能会出现这里还没回调过来，外面其他的监听已经回调了，导致两边状态不一致。
     * 所以提供了一个从外面传进登录状态的接口。
     *
     * @param callback
     */
    public void getHistoryListWithLogin(boolean login, HttpCallback<List<HistoryItem>> callback) {
        YTLogUtil.logStart("HistoryManager", "getHistoryListWithLogin", "callback");
        //如果本地登录，传入是未登录状态，就清除本地缓存。
        if (mLogin && !login) {
            mLogin = false;
            ((LoginedHistoryDaoManager) mDaoManager).clearLocal(null);
        }
        if (!login) {
            ThirdPlatformLoginer thirdPlatformLoginer = ClazzImplUtil.getInter("ThirdPlatformLoginerImpl");
            if (thirdPlatformLoginer != null) {
                thirdPlatformLoginer.login(AppDelegate.getInstance().getContext(), new Callback() {
                    @Override
                    public void onSuccess() {
                        pullNetHistories(callback);
                    }

                    @Override
                    public void onFailure() {
                        loadLocalHistory(callback);
                    }
                });
            } else {
                YTLogUtil.logStart("HistoryManager", "getHistoryListWithLogin", "loadLocalHistory");
                loadLocalHistory(callback);
            }
        } else {
            YTLogUtil.logStart("HistoryManager", "getHistoryListWithLogin", "Login");
            pullNetHistories(callback);
        }
    }

    @Override
    public HistoryItem getLasted() {
        return mDaoManager.getLasted();
    }

    @Override
    public void saveHistory(PlayItem playItem, boolean isUpdatePlayPosition) {
        mDaoManager.saveHistory(playItem, isUpdatePlayPosition);
    }

    @Override
    public void setListener(OnQueryListener<Boolean> listener) {
        mDaoManager.setListener(listener);
    }

    @Override
    public void saveHistoryWhenDestroy(PlayItem playItem) {
        mDaoManager.saveHistoryWhenDestroy(playItem);
    }

    @Override
    public void clear(HttpCallback<Boolean> callback) {
        if (mLogin) {
            mUploadManager.clear();
        }
        mDaoManager.clear(callback);
    }

    public boolean isLogin() {
        return mLogin;
    }

    @Override
    public void progress(PlayItem playItem, long progress) {
        mDaoManager.progress(playItem, progress);
        mUploadManager.progress(playItem, progress);
    }

    public void pullNetHistories(HttpCallback<List<HistoryItem>> callback) {
        Log.d("autoPlay", "-pullNetHistories");
        mUploadManager.start();
        mDaoManager = LoginedHistoryDaoManager.getInstance();
        mDaoManager.getHistoryList(callback);
    }

    public void loadLocalHistory(HttpCallback<List<HistoryItem>> callback) {
        YTLogUtil.logStart("HistoryManager", "loadLocalHistory", "");
        mDaoManager = HistoryDaoManager.getInstance();
        mDaoManager.getHistoryList(callback);
    }

    @Override
    public void destroy() {
        ComponentUtil.removeObserver(UserComponentConst.NAME, mHistoryUserObserver);
        HistoryDaoManager.getInstance().destroy();
        LoginedHistoryDaoManager.getInstance().destroy();
        PlayerManager.getInstance().removePlayControlStateCallback(mPlayStateListener);
    }


    private class HistoryUserObserver implements DynamicComponent, MainThreadable {

        @Override
        public String getName() {
            return "HistoryManager-UserObserver";
        }

        @Override
        public boolean onCall(RealCaller caller) {
            String actionName = caller.actionName();
            switch (actionName) {
                case UserStateObserverProcessorConst.USER_LOGIN:
                    mLogin = true;
                    mDaoManager = LoginedHistoryDaoManager.getInstance();
                    mUploadManager.start();
                    break;
                case UserStateObserverProcessorConst.USER_LOGOUT:
                    if (mLogin) {
                        mLogin = false;
                        //退出登录要清除该账户下的本地缓存并上传
                        mUploadManager.destroy();
                        Log.d(getClass().getSimpleName(), "LoginedHistoryDaoManager clear");
                        ((LoginedHistoryDaoManager) mDaoManager).clearLocal(null);
                    }
                    // fix ZMKQ-5544，退出登录后把一切历史数据清空
                    Log.d(getClass().getSimpleName(), "HistoryDaoManager clear");
                    HistoryDaoManager.getInstance().clear(null);

                    mDaoManager = HistoryDaoManager.getInstance();
                    break;
                default:
                    break;
            }
            //立马保存一下当前历史
            mDaoManager.saveHistory(PlayerManagerHelper.getInstance().getCurPlayItem(), true);
            return false;
        }

        @Override
        public Boolean shouldActionRunOnMainThread(String actionName, ComponentClient caller) {
            return true;
        }
    }

    private static final String BROADCAST_LIST = "BROADCAST_LIST";

    @Override
    public void saveBroadcastList(@Nullable List<BroadcastRadioSimpleData> arrayList) {
        SpUtil.init(AppDelegate.getInstance().getContext());
        try {
            if (arrayList != null && !arrayList.isEmpty()) {
                String json = JSON.toJSONString(arrayList);
                SpUtil.putString(BROADCAST_LIST, json);
            }
        } catch (Exception e) {
            Log.e(TAG, "saveBroadcastList: " + e.getMessage());
        }
    }

    @Override
    public Single<List<BroadcastRadioSimpleData>> getLocalBroadcastList() {
        return Single.<List<BroadcastRadioSimpleData>>create(emitter -> {
                    try {
                        // 初始化 SharedPreferences
                        SpUtil.init(AppDelegate.getInstance().getContext());
                        String json = SpUtil.getString(BROADCAST_LIST, "");

                        if (TextUtils.isEmpty(json)) {
                            emitter.onSuccess(null);  // 返回 null 的情况
                            return;
                        }

                        // 清空 SharedPreferences 中的 BROADCAST_LIST
                        SpUtil.putString(BROADCAST_LIST, "");

                        // 解析 JSON
                        List<BroadcastRadioSimpleData> result = JSON.parseArray(json, BroadcastRadioSimpleData.class);
                        emitter.onSuccess(result);
                    } catch (Exception e) {
                        emitter.onError(e);  // 将异常传递到 onError
                    }
                })
                .subscribeOn(Schedulers.io())  // 在 IO 线程上执行
                .observeOn(AndroidSchedulers.mainThread());  // 在主线程上观察结果
    }

    @Override
    public List<BroadcastRadioSimpleData> getLocalBroadcastListDirectly() {
        try {
            SpUtil.init(AppDelegate.getInstance().getContext());
            String json = SpUtil.getString(BROADCAST_LIST, "");
            if (TextUtils.isEmpty(json)) {
                return null;
            }
            SpUtil.putString(BROADCAST_LIST, "");
            return JSON.parseArray(json, BroadcastRadioSimpleData.class);
        } catch (Exception e) {
            Log.i(TAG, "getLocalBroadcastListDirectly: " + e.getMessage());
        }
        return null;
    }

    private static class SingletonHolder {

        private static final HistoryManager INSTANCE = new HistoryManager();

    }


}
