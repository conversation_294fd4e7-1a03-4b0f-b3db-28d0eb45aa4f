package com.kaolafm.kradio.component;

/**
 * 是否在主线程执行
 * <AUTHOR>
 * @date 2019-07-02
 */
public interface MainThreadable {

    /**
     * action是否运行在主线程。
     * @param actionName
     * @param caller
     * @return null--默认，不固定运行的线程（在主线程同步调用时在主线程运行，其它情况下在子线程运行）
     *         true--固定在主线程运行。
     *         false--固定在子线程运行。
     */
    Boolean shouldActionRunOnMainThread(String actionName, ComponentClient caller);

}
