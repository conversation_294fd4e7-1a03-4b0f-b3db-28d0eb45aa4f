package com.kaolafm.kradio.flavor.carnetwork.carnetworkutils;

public class AuthConstants {

    public static String BASEURL = "http://auth.tboss.cu-sc.com:10010";
    public final static String BASEURL_TEST = "http://test-tboss.cu-sc.com:10010";
    public final static String BASEURL_OFFICIAL = "http://auth.tboss.cu-sc.com:10010";

    public final static String REQUEST_GET_AUTH = "/tfsPlatform-INF/service/authbyvin2/{json}";

    public final static String APPID = "DongFengXK";
    public final static String ITEMID = "DFXKKL01";

    public final static String SECRETCODE = "6f38eb0d-c757-477e-b823-d1c2bdaca310";

    //    public final static int CODE_SUCCESS = 1;
//    public final static int CODE_ZERO = 0;
    public final static int CODE_MINUS_ONE = -1;
    public final static int CODE_MINUS_TWO = -2;
    public final static int CODE_MINUS_THREE = -3;
    public final static int CODE_MINUS_FOUR = -4;
    public final static int CODE_MINUS_FIVE = -5;
    public final static int CODE_MINUS_SIX = -6;
    public final static int CODE_MINUS_SEVEN = -7;
    public final static int CODE_MINUS_EIGHT = -8;
    public final static int CODE_MINUS_NINE = -9;
    public final static int CODE_VIN_FALSE = -10;//获取车机vin码失败
    public final static int CODE_NET_ERROR = -11;//网络不可用
    public final static int CODE_TIMESTAMP_ERROR = -12;//获取时间戳失败

    //自定义的code
    public final static int CODE_LOADING = 98;//loading状态
    public final static int CODE_TIMEOUT = 97;//超时状态
    public final static int CODE_ERROR = 96;//错误状态

    //长安提示语内容
//    public final static String DIALOG_TEXT_SHOPPING = "您暂未购买该产品，或产品已过有效期，请至服务商城购买。";
//    public final static String DIALOG_TEXT_NETWORKFAILED = "网络超时，请稍后重试。";
//    public final static String DIALOG_TEXT_NOCARINFO = "车辆信息不完整，服务认证失败，请联系购车经销商处理或拔打热线4008886677";


}
