package com.kaolafm.kradio.launcher;

import android.app.Activity;
import android.util.DisplayMetrics;
import android.util.Log;
import android.widget.Toast;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;

@Aspect
public class LauncherActivityAspect {

    private String TAG = "LauncherActivityAspect";


    @Around("execution(* com.kaolafm.launcher.LauncherActivity.onWindowFocusChanged(..))")
    public void onAttatchWindow(ProceedingJoinPoint point) throws Throwable {
        LauncherActivity activity = (LauncherActivity) point.getThis();
        Log.i(TAG, "activity:" + activity);
        point.proceed();
        printScreenInfo(activity);
    }

    public void printScreenInfo(Activity activity) {
        int screenWidth = activity.getWindowManager().getDefaultDisplay().getWidth();//真实分辨率 宽
        int screenHeight = activity.getWindowManager().getDefaultDisplay().getHeight();//真实分辨率 高

        DisplayMetrics dm;
        dm = activity.getResources().getDisplayMetrics();
        int densityDPI = dm.densityDpi;     // 屏幕密度（每寸像素：120(ldpi)/160(mdpi)/213(tvdpi)/240(hdpi)/320(xhdpi)）
        Log.i(TAG, "真实分辨率：" + screenWidth + "*" + screenHeight + "  每英寸:" + densityDPI);
//        Toast.makeText(activity, "真实分辨率：" + screenWidth + "*" + screenHeight + "  每英寸:" + densityDPI, Toast.LENGTH_LONG).show();
    }
}
