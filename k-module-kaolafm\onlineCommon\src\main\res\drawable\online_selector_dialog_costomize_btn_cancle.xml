<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <gradient android:angle="180" android:endColor="#ff456d99" android:startColor="#ff294b7d" android:type="linear" />
            <corners android:radius="@dimen/m8" />
        </shape>
    </item>

    <item>
        <shape>
            <gradient android:angle="180" android:endColor="#ff456d99" android:startColor="#ff294b7d" android:type="linear" />
            <corners android:radius="@dimen/m8" />
        </shape>
    </item>
</selector>