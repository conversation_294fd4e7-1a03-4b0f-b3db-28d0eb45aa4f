<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_dialog">


    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y50"
        android:text="@string/update_msg_title"
        android:textColor="@color/colorWhite"
        android:textSize="@dimen/text_size6"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="MissingConstraints" />

    <TextView
        android:id="@+id/tv_update_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_marginTop="@dimen/y100"
        android:text=""
        android:textColor="@color/colorWhite"
        android:textSize="@dimen/text_size4"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="MissingConstraints" />


    <RelativeLayout
        android:layout_width="@dimen/m180"
        android:layout_height="@dimen/m180"
        android:layout_marginBottom="@dimen/y30"
        app:layout_constraintBottom_toTopOf="@+id/guideline1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <fr.castorflex.android.circularprogressbar.CircularProgressBar xmlns:app="http://schemas.android.com/apk/res-auto"
            android:id="@+id/changan_loading"
            style="@style/CustomerCircularProgressBar"
            android:layout_width="@dimen/m120"
            android:layout_height="@dimen/m120"
            android:layout_centerInParent="true"
            android:visibility="visible"
            app:cpb_color="@color/circular_progress_color"
            app:cpb_stroke_width="@dimen/loading_progress_width"
            tools:ignore="MissingConstraints" />

        <TextView
            android:id="@+id/tv_progress"
            android:layout_width="@dimen/x110"
            android:layout_height="@dimen/y48"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:text="0%"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/text_size3" />
    </RelativeLayout>


    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="1" />


</androidx.constraintlayout.widget.ConstraintLayout>