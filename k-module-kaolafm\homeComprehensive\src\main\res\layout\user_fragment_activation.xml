<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/activation_main_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ViewStub
        android:id="@+id/avition_back_home_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout="@layout/layout_avition_left_navbar" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignLeft="@id/avition_back_home_layout"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <ImageView
                android:id="@+id/activationLogo"
                android:layout_width="@dimen/m300"
                android:layout_height="@dimen/activation_logo_height"
                android:layout_centerInParent="true"
                android:scaleType="fitCenter"
                android:visibility="gone" />

            <TextView
                android:id="@+id/activationLogoText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/activationLogo"
                android:layout_centerInParent="true"
                android:gravity="center_horizontal"
                android:text="@string/activation_k_radio_normal_str"
                android:textColor="@color/text_color_1"
                android:textSize="@dimen/text_size12" />

            <TextView
                android:id="@+id/activationTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/activationLogoText"
                android:layout_margin="@dimen/y21"
                android:gravity="center_horizontal"
                android:text="@string/activation_k_radio_normal_title_str"
                android:textColor="@color/text_anivation_color"
                android:textSize="@dimen/text_size7" />

        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/y90"
            android:layout_weight="1">

            <LinearLayout
                android:id="@+id/activationUserProtocol"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal"
                android:paddingTop="@dimen/y10"
                android:paddingBottom="@dimen/y30">

                <ImageView
                    android:id="@+id/activationUserProtocolChoose"
                    android:layout_width="@dimen/m52"
                    android:layout_height="@dimen/m52"
                    android:layout_gravity="center_vertical"
                    android:layout_marginTop="@dimen/m3"
                    android:padding="@dimen/m10"
                    android:src="@drawable/activation_checkbox" />

                <RelativeLayout
                    android:id="@+id/activationUserProtocolPSLayout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:padding="@dimen/m10">

                    <TextView
                        android:id="@+id/activationUserProtocolPS"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:text="@string/activation_protocol_title_str"
                        android:textSize="@dimen/text_size5" />

                    <View
                        android:id="@+id/activationUserProtocolShadow"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/y1"
                        android:layout_alignStart="@+id/activationUserProtocolPS"
                        android:layout_alignEnd="@+id/activationUserProtocolPS"
                        android:layout_alignBottom="@+id/activationUserProtocolPS" />
                </RelativeLayout>

            </LinearLayout>

            <TextView
                android:id="@+id/activationBtn"
                android:layout_width="@dimen/x400"
                android:layout_height="@dimen/y80"
                android:layout_below="@id/activationUserProtocol"
                android:layout_centerHorizontal="true"
                android:background="@drawable/activation_btn_selector"
                android:gravity="center"
                android:text="@string/activation_str"
                android:textColor="@color/text_color_1"
                android:textSize="@dimen/text_size7" />

        </RelativeLayout>
    </LinearLayout>

    <ViewStub
        android:id="@+id/activation_home_button_viewStub"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout="@layout/activation_home_button" />

    <fr.castorflex.android.circularprogressbar.CircularProgressBar
        android:id="@+id/activation_loading_view"
        style="@style/CustomerCircularProgressBar"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_centerInParent="true"
        android:visibility="gone"
        app:cpb_color="@color/circular_progress_color" />
</RelativeLayout>