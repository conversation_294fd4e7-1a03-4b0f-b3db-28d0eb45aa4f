<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_activated="true" android:state_pressed="true">
        <shape>
            <solid android:color="#80C6AA67" />
            <corners android:bottomLeftRadius="80dp" android:bottomRightRadius="80dp" android:topLeftRadius="80dp" android:topRightRadius="80dp" />
        </shape>
    </item>

    <item android:state_activated="true" android:state_pressed="false">
        <shape>
            <solid android:color="#C6AA67" />
            <corners android:bottomLeftRadius="80dp" android:bottomRightRadius="80dp" android:topLeftRadius="80dp" android:topRightRadius="80dp" />
        </shape>
    </item>

    <item android:state_activated="false">
        <shape>
            <solid android:color="#313944" />
            <corners android:bottomLeftRadius="80dp" android:bottomRightRadius="80dp" android:topLeftRadius="80dp" android:topRightRadius="80dp" />
        </shape>
    </item>
</selector>