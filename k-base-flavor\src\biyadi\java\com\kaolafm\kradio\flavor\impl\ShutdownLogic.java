package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.kaolafm.kradio.lib.base.flavor.ShutdownLogicListener;
import com.kaolafm.kradio.service.BYDWidgetService;

/**
 * Created by Wenchl on 2018/3/12.
 */

public class ShutdownLogic implements ShutdownLogicListener {
    @Override
    public boolean onShutdownLogic(Context context, Intent i) {
        Intent intent = new Intent(context, BYDWidgetService.class);
        Log.i("ShutdownLogic","ShutdownLogic bydstartTest : ");
        if (i != null && i.getBooleanExtra("from_quickboot",false)) {
            intent.setAction(BYDWidgetService.WIDGET_ACTION_PAUSE);
        } else {
            intent.setAction(BYDWidgetService.WIDGET_ACTION_REFRESH);
        }
        context.startService(intent);
        return true;
    }
}
