package com.kaolafm.kradio.online.home.location.request;

import com.kaolafm.kradio.online.home.location.bean.OnlineRecomandCityBean;
import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.http.core.HttpCallback;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 位置服务搜索接口
 *
 * <AUTHOR>
 * @date 2019/4/25
 */
public class OnlineLocationSelectRequest extends BaseRequest {

    private final OnlineLocationSelectService mSearchService;

    public OnlineLocationSelectRequest() {
        mSearchService = obtainRetrofitService(OnlineLocationSelectService.class);
    }




    /**
     * 根据给定的词获取联想词
     *
     * @param word     必填 关键词
     * @param callback 选填 回调，返回联想词的列表
     */
    public void getSuggestedWords(String word,  HttpCallback<List<OnlineRecomandCityBean>> callback) {
        Map<String, Object> map = new HashMap<>();
        map.put("keyWords", word);
        doHttpDeal(mSearchService.getTips(map), BaseResult::getResult, callback);
    }


    /**
     * 获取热词
     *
     * @param callback 选填 回调，返回热词的列表
     */
    public void getHotWords(HttpCallback<List<OnlineRecomandCityBean>> callback) {
        doHttpDeal(mSearchService.recomandCity(), BaseResult::getResult, callback);
    }
}
