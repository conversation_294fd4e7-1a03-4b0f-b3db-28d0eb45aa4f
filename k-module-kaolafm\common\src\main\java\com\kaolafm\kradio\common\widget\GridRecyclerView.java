package com.kaolafm.kradio.common.widget;

import android.content.Context;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.GridLayoutManager.SpanSizeLookup;
import androidx.recyclerview.widget.RecyclerView;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.GridLayoutAnimationController;
import android.view.animation.GridLayoutAnimationController.AnimationParameters;
import android.view.animation.LayoutAnimationController;

/**
 * grid布局，自定义是为了使用动画效果
 *
 * <AUTHOR>
 * @date 2018/5/3
 */

public class GridRecyclerView extends RecyclerView {

    public GridRecyclerView(Context context) {
        super(context);
        init();
    }

    public GridRecyclerView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public GridRecyclerView(Context context, @Nullable AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        init();
    }

    private void init(){
        // 关闭 RecyclerView 的 item 动画, 解决刷新 item 时出现闪烁的问题
//        try {
//            ((DefaultItemAnimator) getItemAnimator()).setSupportsChangeAnimations(false);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        setItemAnimator(null);
    }

    @Override
    protected void attachLayoutAnimationParameters(View child, ViewGroup.LayoutParams params, int index, int count) {
//        final LayoutManager layoutManager = getLayoutManager();
//        LayoutAnimationController layoutAnimation = getLayoutAnimation();
//        if (getAdapter() != null && layoutManager instanceof GridLayoutManager && layoutAnimation instanceof GridLayoutAnimationController) {
//            GridLayoutAnimationController.AnimationParameters animationParameters = null;
//            if (params.layoutAnimationParameters instanceof GridLayoutAnimationController.AnimationParameters){
//                animationParameters = (AnimationParameters) params.layoutAnimationParameters;
//            }
//            if (animationParameters == null) {
//                animationParameters = new AnimationParameters();
//                params.layoutAnimationParameters = animationParameters;
//            }
//            final int columns = ((GridLayoutManager) layoutManager).getSpanCount();
//            SpanSizeLookup spanSizeLookup = ((GridLayoutManager) layoutManager).getSpanSizeLookup();
//            int spanSize = spanSizeLookup.getSpanSize(index);
//            if (spanSize < columns){
//                animationParameters.count = count;
//                animationParameters.index = index;
//                animationParameters.columnsCount = columns;
//                animationParameters.rowsCount = count / columns;
//                final int invertedIndex = count - 1 - index;
//                animationParameters.column = columns - 1 - (invertedIndex % columns);
//                animationParameters.row = animationParameters.rowsCount - 1 - invertedIndex / columns;
//            }
//        } else {
//            super.attachLayoutAnimationParameters(child, params, index, count);
//        }

    }
}
