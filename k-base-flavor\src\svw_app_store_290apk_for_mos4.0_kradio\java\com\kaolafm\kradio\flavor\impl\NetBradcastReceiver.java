package com.kaolafm.kradio.flavor.impl;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.IntentUtils;

import static android.content.CustomIntent.EXTRA_NETWORK_STATUS;

/**
 * author : wxb
 * date   : 2022/1/12
 * desc   :
 */
public class NetBradcastReceiver extends BroadcastReceiver {

    @Override
    public void onReceive(Context context, Intent intent) {
        Log.d("NetworkBrodcastImpl", intent.toString());
        try {
            if (intent != null) {

//                    01-12 05:18:15.497 +0000 D/NetworkBrodcastImpl(10391): Intent { act=com.cns.android.intent.action.NETWORK_CHANGED flg=0x10 (has extras) }
//                    01-12 05:18:15.497 +0000 I/NetworkBrodcastImpl(10391): com.cns.android.intent.extra.network_type==
//                            01-12 05:18:15.497 +0000 I/NetworkBrodcastImpl(10391): com.cns.android.intent.extra.network_status==true
                Bundle bundle = intent.getExtras();
                for (String key : bundle.keySet()) {
                    Log.i("NetworkBrodcastImpl", key + "==" + bundle.get(key));
                }
//                    FakeNetworkCheckImpl.netStates =
                FakeNetworkCheckImpl.netStates = intent.getBooleanExtra(EXTRA_NETWORK_STATUS, false);
                try {
                    boolean isAppOnForeground = IntentUtils.getInstance().isAppOnForeground();
                    if (isAppOnForeground) {
                        if (FakeNetworkCheckImpl.netStates) {
                            IcasToast.show(AppDelegate.getInstance().getContext(), AppDelegate.getInstance().getContext().getResources().getString(com.kaolafm.kradio.lib.R.string.no_net_work_str), 1000);
                        } else {
                            IcasToast.show(AppDelegate.getInstance().getContext(), AppDelegate.getInstance().getContext().getResources().getString(com.kaolafm.kradio.lib.R.string.network_has_linked), 1000);
                        }
                    }


                } catch (Exception e) {
                    e.printStackTrace();
                }


            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

}
