<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="@dimen/x60"
    android:layout_height="match_parent"
    android:padding="@dimen/m15">

    <TextView
        android:id="@+id/item_subcategory_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:clickable="false"
        android:ems="1"
        android:gravity="center"
        android:textColor="@color/text_color_3"
        android:textSize="@dimen/text_size2"
        tool:text="热门" />
</FrameLayout>