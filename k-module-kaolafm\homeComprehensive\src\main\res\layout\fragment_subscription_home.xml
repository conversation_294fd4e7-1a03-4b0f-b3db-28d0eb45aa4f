<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/default_edge_start"
        android:layout_marginTop="@dimen/m30"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv0"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/m56"
            android:background="@drawable/sl_subscription_tab"
            android:gravity="center"
            android:paddingStart="@dimen/m20"
            android:paddingEnd="@dimen/m20"
            android:textColor="@color/sl_subscription_tab"
            android:textSize="@dimen/text_size4"
            tools:text="垃圾堆里煎熬发链接" />

        <TextView
            android:id="@+id/tv1"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/m56"
            android:layout_marginStart="@dimen/m30"
            android:background="@drawable/sl_subscription_tab"
            android:gravity="center"
            android:paddingStart="@dimen/m20"
            android:paddingEnd="@dimen/m20"
            android:textColor="@color/sl_subscription_tab"
            android:textSize="@dimen/text_size4"
            tools:text="垃圾堆里煎熬发链接" />

        <TextView
            android:id="@+id/tv2"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/m56"
            android:layout_marginStart="@dimen/m30"
            android:background="@drawable/sl_subscription_tab"
            android:gravity="center"
            android:paddingStart="@dimen/m20"
            android:paddingEnd="@dimen/m20"
            android:textColor="@color/sl_subscription_tab"
            android:textSize="@dimen/text_size4"
            tools:text="垃圾堆里煎熬发链接" />
        <TextView
            android:id="@+id/tv3"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/m56"
            android:layout_marginStart="@dimen/m30"
            android:background="@drawable/sl_subscription_tab"
            android:gravity="center"
            android:paddingStart="@dimen/x20"
            android:paddingEnd="@dimen/x20"
            android:textColor="@color/sl_subscription_tab"
            android:textSize="@dimen/text_size4"
            tools:text="垃圾堆里煎熬发链接" />
    </LinearLayout>

    <RelativeLayout
        android:id="@+id/home_root"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/ll"
        android:layout_marginTop="@dimen/m20" />
</RelativeLayout>