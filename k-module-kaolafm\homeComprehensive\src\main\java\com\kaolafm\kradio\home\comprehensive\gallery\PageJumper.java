package com.kaolafm.kradio.home.comprehensive.gallery;

import android.app.Activity;
import androidx.lifecycle.Lifecycle;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.core.app.ComponentActivity;

import android.content.Context;
import android.os.Bundle;
import android.util.Log;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.common.CP;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.kradio.component.ComponentClient;
import com.kaolafm.kradio.lib.utils.YTLogUtil;
import com.kaolafm.kradio.player.comprehensive.broadcast.BroadcastPlayerFragment;
import com.kaolafm.kradio.constant.ActivityComponentConst;
import com.kaolafm.kradio.home.data.Category;
import com.kaolafm.kradio.home.comprehensive.playerbar.ComprehensivePlayerBar;
import com.kaolafm.kradio.home.comprehensive.playerbar.ComprehensivePlayerHelper;
import com.kaolafm.kradio.purchase.base.BaseOrderDialogFragment;
import com.kaolafm.kradio.player.comprehensive.audio.RadioPlayerFragment;
import com.kaolafm.kradio.constant.LiveComponentConst;
import com.kaolafm.kradio.constant.LoginProcessorConst;
import com.kaolafm.kradio.home.comprehensive.HorizontalHomePlayerFragment;
import com.kaolafm.kradio.user.BackUserFragment;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.base.flavor.KRadioPlayerBarJumpLiveInter;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.player.helper.intercept.HintInterceptManager;
import com.kaolafm.kradio.subscribe.SubscribeHelper;
import com.kaolafm.launcher.LauncherActivity;
import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.PlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.item.LivePlayItem;
import com.kaolafm.opensdk.player.logic.model.item.TempTaskPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import me.yokeyword.fragmentation.ISupportFragment;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: PageJumper.java                                               
 *                                                                  *
 * Created in 2018/4/25 下午2:46                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
public class PageJumper {
    private static String TAG = "PageJumper";

    private WeakReference<ComponentActivity> refContext;
    private WeakReference<HorizontalHomePlayerFragment> mHomePlayerFragment;
    private boolean needComeBack = false;

    private PageJumper() {
    }

    private volatile static PageJumper mInstance;

    public static PageJumper getInstance() {
        if (mInstance == null) {
            synchronized (PageJumper.class) {
                if (mInstance == null) {
                    mInstance = new PageJumper();
                }
            }
        }
        return mInstance;
    }

    public void init(FragmentActivity activity, HorizontalHomePlayerFragment homePlayerFragment) {
        refContext = new WeakReference<ComponentActivity>(activity);
        this.mHomePlayerFragment = new WeakReference<>(homePlayerFragment);
    }

    public void jumpToLivePage(long playId) {
        ComponentClient build = ComponentClient.obtainBuilder(LiveComponentConst.NAME)
                .setActionName(LiveComponentConst.START_HOME_LIVE)
                .addParam("liveId", playId)
                .addParam("context", refContext.get())
//                .addParam("containerId", R.id.launcher_main_layout)
                .build();
        Log.i("PageJumper", "jumpToLivePage: " + build.componentName());
        build.callAsync();
    }
    public void jumpToLivePageWithoutContext(long playId,FragmentActivity activity) {
        if(refContext == null){
            refContext = new WeakReference<ComponentActivity>(activity);
        }
        ComponentClient build = ComponentClient.obtainBuilder(LiveComponentConst.NAME)
                .setActionName(LiveComponentConst.START_HOME_LIVE)
                .addParam("liveId", playId)
                .addParam("context", refContext.get())
//                .addParam("containerId", R.id.launcher_main_layout)
                .build();
        Log.i("PageJumper", "jumpToLivePage: " + build.componentName());
        build.callAsync();
    }

    public void jumpToLoginPage(@CP.CpType int cpType) {
        if (mHomePlayerFragment.get() != null) {
            mHomePlayerFragment.get().jumpToLoginPage(cpType);
        }
    }

    /**
     * 跳转到活动页面
     */
    public void jumpToActivityPage() {
        ComponentClient.obtainBuilder(ActivityComponentConst.NAME)
                .setActionName(ActivityComponentConst.START_ACTIVITY)
                .addParam("context", refContext.get())
                .build().callAsync();
    }

    public void back() {
        if (mHomePlayerFragment != null && mHomePlayerFragment.get() != null) {
            mHomePlayerFragment.get().back();
        }
//        FragmentActivity activity = refContext.get();
//        if (activity != null) {
//            activity.onBackPressed();
//        }
    }


//    public void jumpToLivePage(Category.Item item) {
//        if (mHomePlayerFragment != null && mHomePlayerFragment.get() != null) {
//            mHomePlayerFragment.get().jumpToLivePage(item);
//        }
//    }

    /**
     * 跳转到kradio登录界面
     */
    public void jumpToKRadioLoginPage() {
        if (mHomePlayerFragment != null && mHomePlayerFragment.get() != null) {
            mHomePlayerFragment.get().jumpToKRadioLoginPage();
        }
    }

    public void jumpToCoinFragment() {
        if (mHomePlayerFragment != null && mHomePlayerFragment.get() != null) {
            mHomePlayerFragment.get().jumpToCoinPage();
        }
    }

    public void jumpToLogin() {
        if (needComeBack) {
            needComeBack = false;
        }
        RouterManager.getInstance().jumpPage(RouterConstance.LOGIN_COMPREHENSIVE_URL);
//        if (AppManager.getInstance().getCurrentActivity() != null) {
//            Intent intent = new Intent();
//            intent.setClass(AppManager.getInstance().getCurrentActivity(), UserLoginActivity.class);
//            AppManager.getInstance().getCurrentActivity().startActivity(intent);
//        }
    }

    public void jumpToNewLoginPageAndComeBack(boolean needComeBack) {
        this.needComeBack = needComeBack;
        jumpToLogin();
//        if (needComeBack) {
//
////            jumpToNewLoginPage();
//        }
    }

    private void jumpToNewLoginPage() {
        FragmentManager fm = ((me.yokeyword.fragmentation.SupportActivity) refContext.get()).getSupportFragmentManager();
        Log.i("rsq", "size=" + fm.getFragments().size());
        for (int i = fm.getFragments().size() - 1; i >= 0; i--) {
            Log.i("rsq", "i=" + i);
            if (fm.getFragments().get(i) instanceof BackUserFragment) {
                ((BackUserFragment) fm.getFragments().get(i)).pop();
            }
        }

        //fixed 由于添加了"已购"标题，"账号登陆的索引需要变成3"
        HintInterceptManager.getInstance();
        BackUserFragment backUserFragment = BackUserFragment.newInstance(3);
        if (needComeBack) {
            needComeBack = false;
            if (PlayerManagerHelper.getInstance().isActionFromUser()) {
                backUserFragment.setBackData(LoginProcessorConst.BACKTYPE_POP, Constants.PLAYER_ITEM_CLICK);
            } else {
                backUserFragment.setBackData(LoginProcessorConst.BACKTYPE_POP, Constants.PLAYER_ITEM_CAROUSEL);
            }
        }
        ((me.yokeyword.fragmentation.SupportActivity) refContext.get()).extraTransaction().start(backUserFragment);
//        loadRootFragment(R.id.fl_launcher_root_content, backUserFragment, false, false);
    }


    /**
     * 跳到直播界面
     */
    public void jumpToLivePage(Category.Item item) {
        PlayerManagerHelper.getInstance().finishAudioAd();
        Log.i(TAG, "jumpToLivePage");
        long id = -1L;
        if (item != null) {
            id = item.id;
        } else {
            PlayItem curPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
            if (curPlayItem.getType() == PlayerConstants.RESOURCES_TYPE_LIVING) {
                id = ((LivePlayItem) curPlayItem).getLiveId();
            }
        }

        KRadioPlayerBarJumpLiveInter inter = ClazzImplUtil.getInter("KRadioPlayerBarJumpLiveImpl");
        if (inter == null) {
            ComponentClient.obtainBuilder(LiveComponentConst.NAME)
                    .setActionName(LiveComponentConst.START_HOME_LIVE)
                    .cancelOnDestroyWith(refContext.get())
                    .addParam("liveId", id)
                    .addParam("context", refContext.get())
//                    .addParam("containerId", R.id.launcher_main_layout)
                    .build().callAsync();
        } else {
            inter.jumpLive(refContext.get(), id);
        }

    }


    /**
     * 跳播放器
     */
    public void jumpToPlayerFragment(ComprehensivePlayerBar playerBar) {
        //fixed 避免多次跳转同一fragment，创建多个fragment实例
        if (ComprehensivePlayerHelper.isLiving()) {
            if (!PlayerManagerHelper.getInstance().isInProgramPlayerPage())
                jumpToLivePage(null);
        } else {
            int type = PlayerManagerHelper.getInstance().getCurrentPlayType();
            if (type == PlayerConstants.RESOURCES_TYPE_BROADCAST || type == PlayerConstants.RESOURCES_TYPE_TV) {
                if (!PlayerManagerHelper.getInstance().isInProgramPlayerPage())
                    jumpToBroadcastPlayerFragment();
                return;
            }
            List<PlayItem> playList = PlayerManager.getInstance().getPlayList();
            if (ListUtil.isEmpty(playList)) {
                Log.i(TAG, "jumpToPlayerFragment empty list");
                return;
            }
            if (playerBar != null) {
                SubscribeHelper.isSubscribe = playerBar.getSubscribeState();
            }
            jumpToRadioPlayerFragemnt();
        }
    }

    private void jumpToBroadcastPlayerFragment() {
        ISupportFragment topFragment = ((me.yokeyword.fragmentation.SupportActivity) refContext.get()).getTopFragment();
        if (AppManager.getInstance().getCurrentActivity() instanceof LauncherActivity) {
            if ((topFragment instanceof BaseOrderDialogFragment)) {
                //购买弹窗显示的时候，再次进入界面时有闪退现象。复现了3次。
                return;
            }
            if (!(topFragment instanceof BroadcastPlayerFragment)) {
                YTLogUtil.logStart(TAG, "jumpToPlayerFragment BroadcastPlayerFragment","start");
                if (mHomePlayerFragment.get() != null) {
                    //在首页显示的时候无法弹出播放页，分类页显示的时候就可以。
                    mHomePlayerFragment.get().extraTransaction()
                            .start((ISupportFragment) RouterManager.getInstance().getRouterFragment(RouterConstance.PLAY_BROADCAST_COMPREHENSIVE_URL));
                } else {
                    topFragment.extraTransaction()
                            .start((ISupportFragment) RouterManager.getInstance().getRouterFragment(RouterConstance.PLAY_BROADCAST_COMPREHENSIVE_URL));
                }
            } else {
                Fragment fragment = (Fragment) topFragment;
                Lifecycle.State state = fragment.getLifecycle().getCurrentState();
                if (state != Lifecycle.State.RESUMED) {
                    ((me.yokeyword.fragmentation.SupportActivity) refContext.get()).extraTransaction().startWithPop(topFragment);
                } else {
                    BroadcastPlayerFragment broadcastPlayerFragment = (BroadcastPlayerFragment) topFragment;
                }
            }
        } else {
            if (mHomePlayerFragment.get() != null) {
                //在首页显示的时候无法弹出播放页，分类页显示的时候就可以。
                mHomePlayerFragment.get().extraTransaction()
                        .start((ISupportFragment) RouterManager.getInstance().getRouterFragment(RouterConstance.PLAY_BROADCAST_COMPREHENSIVE_URL));
            } else {
                topFragment.extraTransaction()
                        .start((ISupportFragment) RouterManager.getInstance().getRouterFragment(RouterConstance.PLAY_BROADCAST_COMPREHENSIVE_URL));
            }
//            topFragment.extraTransaction().start(BroadcastPlayerFragment.getInstance());
            List<Activity> list = AppManager.getInstance().getActivityList();
            for (int i = list.size() - 1; i >= 0; i--) {
                if (list.get(i) instanceof LauncherActivity) {
                    break;
                } else {
                    list.get(i).finish();
                }
            }
        }
    }

    public void jumpToVideoAlbumFragment(String albumId) {
        ISupportFragment topFragment = ((me.yokeyword.fragmentation.SupportActivity) refContext.get()).getTopFragment();
        Fragment fragment = RouterManager.getInstance().getRouterFragment(RouterConstance.PLAY_VIDEO_COMPREHENSIVE_URL);
        Bundle bundle = new Bundle();
        bundle.putString("albumId", albumId);
        fragment.setArguments(bundle);
        if (mHomePlayerFragment.get() != null) {
            mHomePlayerFragment.get().extraTransaction()
                    .start((ISupportFragment) fragment);
        } else {
            topFragment.extraTransaction()
                    .start((ISupportFragment) fragment);
        }
    }

    private void jumpToRadioPlayerFragemnt() {
        ISupportFragment topFragment = ((me.yokeyword.fragmentation.SupportActivity) refContext.get()).getTopFragment();
        if (AppManager.getInstance().getCurrentActivity() instanceof LauncherActivity) {
            if ((topFragment instanceof BaseOrderDialogFragment)) {
                //购买弹窗显示的时候，再次进入界面时有闪退现象。复现了3次。
                return;
            }
            if (!(topFragment instanceof RadioPlayerFragment)) {
                YTLogUtil.logStart(TAG, "jumpToPlayerFragment RadioPlayerFragment","start");
                if (mHomePlayerFragment.get() != null) {
                    //在首页显示的时候无法弹出播放页，分类页显示的时候就可以。
                    mHomePlayerFragment.get().extraTransaction()
                            .start((ISupportFragment) RouterManager.getInstance().getRouterFragment(RouterConstance.PLAY_RADIO_COMPREHENSIVE_URL));
                } else {
                    topFragment.extraTransaction()
                            .start((ISupportFragment) RouterManager.getInstance().getRouterFragment(RouterConstance.PLAY_RADIO_COMPREHENSIVE_URL));
                }
            } else {
                Fragment fragment = (Fragment) topFragment;
                Lifecycle.State state = fragment.getLifecycle().getCurrentState();
                if (state != Lifecycle.State.RESUMED) {
                    ((me.yokeyword.fragmentation.SupportActivity) refContext.get()).extraTransaction().startWithPop(topFragment);
                } else {
                    RadioPlayerFragment radioPlayerFragment = (RadioPlayerFragment) topFragment;
                    radioPlayerFragment.mPlayListView.startCountDownTimer();
                }
            }
        } else {
            if (PlayerManagerHelper.getInstance().isInProgramPlayerPage()) {
                Activity currentActivity = AppManager.getInstance().getCurrentActivity();
                if (currentActivity == null) return;
                topFragment = ((me.yokeyword.fragmentation.SupportActivity) currentActivity).getTopFragment();
                //如果是在品牌电台页的播放详情页中
                if (topFragment instanceof RadioPlayerFragment
                        && ((Fragment) topFragment).getLifecycle().getCurrentState() == Lifecycle.State.RESUMED) {
                    RadioPlayerFragment radioPlayerFragment = (RadioPlayerFragment) topFragment;
                    radioPlayerFragment.mPlayListView.startCountDownTimer();
                }
                return;
            }

            List<Activity> list = AppManager.getInstance().getActivityList();
            for (int i = list.size() - 1; i >= 0; i--) {
                if (list.get(i) instanceof LauncherActivity) {
                    break;
                } else {
                    list.get(i).finish();
                }
            }
            if (mHomePlayerFragment.get() != null) {
                //在首页显示的时候无法弹出播放页，分类页显示的时候就可以。
                mHomePlayerFragment.get().extraTransaction()
                        .start((ISupportFragment) RouterManager.getInstance().getRouterFragment(RouterConstance.PLAY_RADIO_COMPREHENSIVE_URL));
            } else {
                topFragment.extraTransaction()
                        .start((ISupportFragment) RouterManager.getInstance().getRouterFragment(RouterConstance.PLAY_RADIO_COMPREHENSIVE_URL));
            }
        }
    }

}
