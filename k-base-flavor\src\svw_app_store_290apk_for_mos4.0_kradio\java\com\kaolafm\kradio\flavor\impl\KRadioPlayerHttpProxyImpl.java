package com.kaolafm.kradio.flavor.impl;

import com.kaolafm.kradio.lib.base.flavor.KRadioPlayerHttpProxyInter;

public class KRadioPlayerHttpProxyImpl implements KRadioPlayerHttpProxyInter {
    @Override
    public String getHttpProxy() {
//        "http://[fd53:7cb8:383:3::108]:50007"; 车机的代理
        if (DebugImpl.hasPoxy()) {
            return "http://[fd53:7cb8:383:3::108]:50007";
        } else
            return null;
    }
}
