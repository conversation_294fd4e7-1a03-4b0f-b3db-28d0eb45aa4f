<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_home"
    android:scrollbars="none">

    <ImageView
        android:id="@+id/back_webview"
        android:layout_width="@dimen/m36"
        android:layout_height="@dimen/m36"
        android:layout_marginStart="@dimen/m70"
        android:layout_marginTop="@dimen/m54"
        android:background="@color/transparent"
        android:scaleType="centerInside"
        android:src="@drawable/player_ic_back"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        android:id="@+id/bbf_center_title"
        layout="@layout/bbf_title_center_textview"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/m50"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="@dimen/m34"
        android:background="@color/activity_line_bg"
        app:layout_constraintTop_toBottomOf="@+id/back_webview"
        tools:ignore="MissingConstraints" />

    <WebView
        android:id="@+id/webview"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginLeft="@dimen/m80"
        android:layout_marginTop="@dimen/m62"
        android:layout_marginRight="@dimen/m80"
        android:layout_marginBottom="@dimen/m50"
        android:scrollbarStyle="outsideOverlay"
        android:background="@android:color/transparent"
        android:scrollbarThumbVertical="@drawable/sl_sh_sb_thumb"
        android:scrollbarTrackVertical="@drawable/sl_sh_sb_track"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line" />

    <View
        android:id="@+id/view"
        android:layout_width="match_parent"
        android:layout_height="@dimen/y119"
        android:background="@drawable/app_bottom_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <!-- 隐藏控件，用于所见即可说语音执行滑动操作 -->
    <TextView
        style="@style/ContentDescriptionScrollUp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/webview" />

    <TextView
        style="@style/ContentDescriptionScrollDown"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/webview" />
</androidx.constraintlayout.widget.ConstraintLayout>