package com.kaolafm.kradio.flavor.impl;

import android.view.KeyEvent;

import com.kaolafm.kradio.lib.base.flavor.KRadioMediaKeyEventInter;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2020-06-03 14:41
 ******************************************/
public class KRadioMediaKeyEventImpl implements KRadioMediaKeyEventInter {

    @Override
    public boolean onKeyUp(int keyCode, KeyEvent event) {
        return false;
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        int keyCode = event.getKeyCode();
        if (keyCode == KeyEvent.KEYCODE_DPAD_UP) {
            return true;
        }
        if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN) {
            return true;
        }
        if (keyCode == KeyEvent.KEYCODE_DPAD_LEFT) {
            return true;
        }
        if (keyCode == KeyEvent.KEYCODE_DPAD_RIGHT) {
            return true;
        }
        if (keyCode == KeyEvent.KEYCODE_ENTER) {
            return true;
        }
        return false;
    }
}