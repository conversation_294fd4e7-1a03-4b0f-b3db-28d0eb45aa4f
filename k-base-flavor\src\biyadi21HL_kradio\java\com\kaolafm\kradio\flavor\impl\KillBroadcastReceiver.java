package com.kaolafm.kradio.flavor.impl;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;


import com.kaolafm.kradio.report.ReportManager;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.report.event.PlayerUiControlReportEvent;



/**
 * 比亚迪车机在近期任务列表 kill 掉考拉fm进程发出通知
 * <p>
 * Created by <PERSON><PERSON><PERSON> on 2018/3/29.
 */

public class KillBroadcastReceiver extends BroadcastReceiver {

    private static final String KILL_EDOG_CAR = "byd.intent.action.KILL_EDOG_CAR";

    @Override
    public void onReceive(Context context, Intent intent) {
        if (KILL_EDOG_CAR.equals(intent.getAction())) {
            Log.d("KillBroadcastReceiver", "action:" + intent.getAction());
            PlayerManager.getInstance().pause(true);
            ReportManager.getInstance().addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_PAUSE, PlayerUiControlReportEvent.CONTROL_TYPE_CLICK, PlayerUiControlReportEvent.POSITION_WIDGET);
            Log.d("KillBroadcastReceiver", "action: WIDGET_ACTION_PAUSE end");
//            KRadioBackKeyInter kRadioBackKeyInter = ClazzImplUtil.getInter("KRadioBackKeyImpl");
//            if (kRadioBackKeyInter != null) {
//                kRadioBackKeyInter.appExit(this);
//            }
        }
    }
}
