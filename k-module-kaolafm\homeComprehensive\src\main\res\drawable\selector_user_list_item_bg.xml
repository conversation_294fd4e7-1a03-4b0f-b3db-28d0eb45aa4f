<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_selected="true">
        <shape>
            <corners android:radius="@dimen/default_radius_img" />
            <solid android:color="@color/bg_broadcast_play" />
        </shape>
    </item>
    <item>
        <shape>
            <corners android:radius="@dimen/default_radius_img" />
            <solid android:color="@color/bg_item_no_play" />
        </shape>
    </item>
</selector>