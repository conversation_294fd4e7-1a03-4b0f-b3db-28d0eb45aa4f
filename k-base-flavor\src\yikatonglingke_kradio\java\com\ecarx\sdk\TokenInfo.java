package com.ecarx.sdk;

/**
 * <AUTHOR>
 **/
public class TokenInfo {
    private AccessTokenResult atr;
    private long accessTokenTime;
    private long refreshTokenTime;

    public AccessTokenResult getAccessTokenResult() {
        return atr;
    }

    public void setAccessTokenResult(AccessTokenResult atr) {
        this.atr = atr;
    }

    public long getAccessTokenTime() {
        return accessTokenTime;
    }

    public void setAccessTokenTime(long accessTokenTime) {
        this.accessTokenTime = accessTokenTime;
    }

    public long getRefreshTokenTime() {
        return refreshTokenTime;
    }

    public void setRefreshTokenTime(long refreshTokenTime) {
        this.refreshTokenTime = refreshTokenTime;
    }

    @Override
    public String toString() {
        return "TokenInfo{" +
                "atr=" + atr +
                ", accessTokenTime=" + accessTokenTime +
                ", refreshTokenTime=" + refreshTokenTime +
                '}';
    }
}
