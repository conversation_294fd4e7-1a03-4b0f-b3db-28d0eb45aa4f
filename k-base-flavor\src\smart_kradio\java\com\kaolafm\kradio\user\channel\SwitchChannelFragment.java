package com.kaolafm.kradio.user.channel;

import android.annotation.SuppressLint;
import android.os.Environment;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckedTextView;
import android.widget.TextView;  
import com.google.gson.Gson;
import com.kaolafm.base.utils.FileUtil;
import com.kaolafm.kradio.flavor.R; 
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.utils.DataCleanManager;
import com.kaolafm.opensdk.account.token.AccessTokenManager;
import io.reactivex.Single;
import io.reactivex.SingleObserver;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;
import java.io.File;
import java.util.List;

/**
 * <AUTHOR> Yan
 * @date 2019/4/19
 */
public class SwitchChannelFragment extends BaseFragment<SwitchChannelPresent> implements ISwitchChannelView {

    public static final String KAOLA_AUTO_BASE = "kradio";

    public static final String UDID_PATH_KAOLA_API = "kaolaapi";
 
    RecyclerView mRvChannelList; 
    TextView mTvChannelCurrentName;

    private ChannelAdapter mChannelAdapter;

    @Override
    public void initArgs() {
        super.initArgs();
    }

    @Override
    public void initView(View view) {
        mRvChannelList = view.findViewById(com.kaolafm.kradio.k_kaolafm.R.id.rv_channel_list);
        mTvChannelCurrentName = view.findViewById(com.kaolafm.kradio.k_kaolafm.R.id.tv_channel_current_name);
        mRvChannelList.setOnClickListener(v -> onViewClicked(v));
        mTvChannelCurrentName.setOnClickListener(v -> onViewClicked(v));
        
        mChannelAdapter = new ChannelAdapter();
        mChannelAdapter.setOnItemClickListener((view1, viewType, channel, position) -> {
            showCurrentChannel(channel, position);
            showOrHideList();
        });
        mRvChannelList.setLayoutManager(new LinearLayoutManager(getContext()));
        mRvChannelList.addItemDecoration(new DividerItemDecoration(getContext(), DividerItemDecoration.VERTICAL));
        mRvChannelList.setAdapter(mChannelAdapter);
        mPresenter.start();
    } 
    public void onViewClicked(View view) {
        int id = view.getId();
        if (id == R.id.iv_switch_channel_back) {
            pop();
        } else if (id == R.id.tv_channel_commit) {
            switchChannel();
        } else if (id == R.id.tv_channel_current_name) {
            showOrHideList();
        }
    }

    @SuppressLint("MissingPermission")
    private void switchChannel() {
        Channel selectedChannel = (Channel) mTvChannelCurrentName.getTag();
        Single.fromCallable(() -> {
            String channelName = selectedChannel.getChannel();
            if (TextUtils.isEmpty(channelName)) {
                String packageName = selectedChannel.getPackageName();
                String[] split = packageName.split("\\.");
                channelName = split[split.length - 1];
                selectedChannel.setChannel(channelName);
            }
            String channel = new Gson().toJson(selectedChannel).trim();
            File file = Environment.getExternalStorageDirectory();
            if (file != null) {
                String path = file.getAbsolutePath();
                String dataPath = path + File.separator + KAOLA_AUTO_BASE + File.separator + UDID_PATH_KAOLA_API
                        + File.separator + "channel.txt";
                File file1 = new File(dataPath);
                if (file1.exists()) {
                    file1.delete();
                }
                FileUtil.writeFileToSDCard(dataPath, channel);
            }
            //清除数据和缓存
            AccessTokenManager.getInstance().clearAll();
            DataCleanManager.cleanApplicationData(getContext());
            return true;
        }).observeOn(AndroidSchedulers.mainThread())
                .subscribeOn(Schedulers.io())
                .subscribe(new SingleObserver<Object>() {

                    private Disposable mDisposable;

                    @Override
                    public void onSubscribe(Disposable d) {
                        mDisposable = d;
                    }

                    @Override
                    public void onSuccess(Object o) {
                        if (mDisposable != null) {
                            mDisposable.dispose();
                        }
                        AppManager.getInstance().appExit();
                    }

                    @Override
                    public void onError(Throwable e) {
                        AppManager.getInstance().appExit();
                        if (mDisposable != null) {
                            mDisposable.dispose();
                        }
                    }
                });
    }

    private void showOrHideList() {
        mRvChannelList.setVisibility(mRvChannelList.getVisibility() == View.VISIBLE? View.GONE : View.VISIBLE);
    }

    @Override
    public void showChannels(List<Channel> channelList) {
        mChannelAdapter.setDataList(channelList);
    }

    @Override
    public void showCurrentChannel(Channel channel, int position) {
        mTvChannelCurrentName.setText(channel.getAppName());
        mTvChannelCurrentName.setTag(channel);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.fragment_switch_channel;
    }

    @Override
    protected SwitchChannelPresent createPresenter() {
        return new SwitchChannelPresent(this);
    }

    class ChannelAdapter extends BaseAdapter<Channel> {

        public ChannelAdapter() {
        }

        @Override
        protected BaseHolder<Channel> getViewHolder(ViewGroup parent, int viewType) {
            return new ChannelHolder(inflate(parent, R.layout.item_switch_channel, viewType));
        }

        @Override
        public long getItemId(int position) {
            return position;
        }
    }

    class ChannelHolder extends BaseHolder<Channel> {
 
        CheckedTextView mTvSwitchChannelName;

        ChannelHolder(View convertView) {
            super(convertView);
            mTvSwitchChannelName =view.findViewById(R.id.tv_switch_channel_name);
        }

        @Override
        public void setupData(Channel channel, int position) {
            mTvSwitchChannelName.setText(channel.getAppName());
        }
    }

}
