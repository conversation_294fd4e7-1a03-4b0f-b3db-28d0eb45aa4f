package com.kaolafm.kradio.flavor.impl;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.kaolafm.kradio.common.event.LogoutBindEvent;
import com.kaolafm.kradio.lib.toast.ToastUtil;

import org.greenrobot.eventbus.EventBus;

/**
 * <AUTHOR>
 * <p>
 * 广播名称：com.beantechs.accountbind.tingban
 * <p>
 * 广播携带参数1：action 取值：bind 和 unbind 分别代表绑定和解绑操作
 * 参数2：result 取值：success 和 fail 分别代表结果成功和失败
 * 参数均为String类型
 **/
public class HaFuReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getStringExtra("action");
        String rst = intent.getStringExtra("result");
        com.kaolafm.kradio.lib.utils.Logger.v("k.login.rcv", "onReceive: action="+action);
        com.kaolafm.kradio.lib.utils.Logger.v("k.login.rcv", "         : rst="+rst);
        if ("bind".equals(action)) {
            if ("success".equals(rst)) {
                ToastUtil.showInfo(context, "绑定成功");
            } else {
                UserInfoManager userInfoManager = UserInfoManager.getInstance();
                userInfoManager.setUserFavicon("");
                userInfoManager.setUserNickName("");
                userInfoManager.localLogout();
                EventBus.getDefault().post(new LogoutBindEvent(LogoutBindEvent.LOGOUT));
            }

        } else if ("unbind".equals(action)) {
            if ("success".equals(rst)) {
                ToastUtil.showInfo(context, "解绑成功");
            } else {
                ToastUtil.showInfo(context, "解绑失败");
            }
        } else if ("logout".equals(action)) {
            if ("success".equals(rst)) {
                ToastUtil.showInfo(context, "账号退出成功");
            } else {
                ToastUtil.showInfo(context, "账号退出失败");
            }
        }
    }
}
