<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/online_mian_dl"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/online_main_bg_iv"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:src="@drawable/online_page_bg_clear"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <RelativeLayout
            android:id="@+id/root_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <RelativeLayout
                android:id="@+id/main_top_rl"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/m91"
                    android:layout_centerHorizontal="true"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <ImageView
                        android:layout_width="@dimen/m944"
                        android:layout_height="@dimen/m91"
                        android:scaleType="centerCrop"
                        android:src="@drawable/online_top_bg_main" />

                    <com.kaolafm.kradio.lib.widget.tab.SlidingTabLayout
                        android:id="@+id/online_home_navigation_tab"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/m75"
                        android:layout_alignParentBottom="true"
                        android:layout_centerInParent="true"
                        android:layout_marginBottom="@dimen/m10"
                        app:tl_first_no_padding="false"
                        app:tl_indicator_drawable="@drawable/online_main_tab_indicator_pic"
                        app:tl_tab_padding="@dimen/m40"
                        app:kradio_tl_textSelectSize="@dimen/m28"
                        app:kradio_tl_textSize="@dimen/m28"
                        app:tl_textSelectSize="@dimen/m28"
                        app:tl_textSize="@dimen/m28"
                        app:tl_textUnselectColor="@color/online_main_tab_text_color" />
                </RelativeLayout>

                <LinearLayout
                    android:id="@+id/online_main_login_ll"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingLeft="@dimen/m28"
                    android:paddingTop="@dimen/m16">

                    <RelativeLayout
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/m70">

                        <RelativeLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content">

                            <ImageView
                                android:id="@+id/online_main_user_pic_bg_iv"
                                android:layout_width="@dimen/m64"
                                android:layout_height="@dimen/m64"
                                android:layout_centerInParent="true"
                                android:src="@drawable/online_user_pic_bg"
                                android:visibility="visible" />

                            <com.kaolafm.kradio.component.ui.base.view.OvalImageView
                                android:id="@+id/online_main_user_pic_iv"
                                android:layout_width="@dimen/m56"
                                android:layout_height="@dimen/m56"
                                android:layout_centerInParent="true"
                                android:src="@drawable/online_user_no_login_icon"
                                app:circle="true" />
                        </RelativeLayout>

                        <ImageView
                            android:id="@+id/online_main_user_vip_iv"
                            android:layout_width="@dimen/m60"
                            android:layout_height="@dimen/m20"
                            android:layout_alignParentBottom="true"
                            android:layout_centerHorizontal="true"
                            android:layout_marginBottom="@dimen/m3"
                            android:src="@drawable/online_user_vip_icon"
                            android:visibility="invisible"
                            tools:visibility="visible" />
                    </RelativeLayout>


                    <LinearLayout
                        android:layout_width="@dimen/x160"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="@dimen/x12"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/online_main_user_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:maxLines="1"
                            android:text="立即登录"
                            android:textColor="@color/online_main_username_color"
                            android:textSize="@dimen/m24" />

                        <TextView
                            android:id="@+id/online_main_user_duration_tv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="收听时长 0h"
                            android:textColor="@color/online_main_user_duration_text_color"
                            android:textSize="@dimen/m20" />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true">

                    <LinearLayout
                        android:id="@+id/online_home_location_ll"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingStart="@dimen/x32"
                        android:paddingEnd="@dimen/x16">

                        <TextView
                            android:id="@+id/online_main_city_tv"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:ellipsize="end"
                            android:gravity="center_vertical"
                            android:maxEms="4"
                            android:maxLines="1"
                            android:text="定位中..."
                            android:textColor="@color/online_select_city_color"
                            android:textSize="@dimen/m24" />

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="@dimen/y10"
                            android:layout_marginLeft="@dimen/x6"
                            android:src="@drawable/online_top_address_icon" />

                    </LinearLayout>

                    <ImageView
                        android:id="@+id/online_mian_search_iv"
                        android:layout_width="@dimen/x80"
                        android:layout_height="@dimen/y91"
                        android:paddingStart="@dimen/x16"
                        android:paddingTop="@dimen/m32"
                        android:paddingEnd="@dimen/x32"
                        android:paddingBottom="@dimen/y27"
                        android:src="@drawable/online_top_search_main" />

                </LinearLayout>


            </RelativeLayout>

            <com.kaolafm.kradio.common.widget.ProgressImageView
                android:id="@+id/online_iv_main_road"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/y273"
                android:layout_alignParentBottom="true"
                android:scaleType="centerInside" />

            <com.kaolafm.kradio.online.common.playbar.OnlinePlayerBar
                android:id="@+id/pb_home_play"
                android:layout_width="match_parent"
                android:layout_height="@dimen/m220"
                android:layout_alignParentBottom="true"
                android:layout_marginLeft="@dimen/m90"
                android:layout_marginRight="@dimen/m90"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent" />

            <FrameLayout
                android:id="@+id/online_main_fl"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_below="@+id/main_top_rl"
                android:layout_marginTop="0dp" />
        </RelativeLayout>

        <View
            android:id="@+id/sub_class_view"
            android:layout_width="match_parent"
            android:layout_height="@dimen/y235"
            android:background="@color/login_bg"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent" />

        <FrameLayout
            android:id="@+id/playerFragmentParent"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <include
            layout="@layout/online_layout_msg_float_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
    <!--    drawerFrameLayout-->
    <FrameLayout
        android:id="@+id/searchFrameLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="right"
        android:paddingStart="@dimen/x120">

    </FrameLayout>
</androidx.drawerlayout.widget.DrawerLayout>
