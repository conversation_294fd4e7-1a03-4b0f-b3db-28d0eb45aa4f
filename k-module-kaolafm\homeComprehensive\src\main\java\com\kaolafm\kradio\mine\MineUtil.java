package com.kaolafm.kradio.mine;

import com.kaolafm.kradio.constant.LoginProcessorConst;
import com.kaolafm.kradio.user.BackUserFragment;

/**
 * 我的工具类
 * <AUTHOR>
 * @date 2019-10-30
 */
public class MineUtil {
    public static final MineUtil INSTANCE = new MineUtil();

    public BackUserFragment newMineFragment(int position, boolean isBack){
        BackUserFragment backFrag = BackUserFragment.newInstance(position);
        if(isBack)
            backFrag.setBackData(LoginProcessorConst.BACKTYPE_POP,-1);
        return backFrag;
    }

    public BackUserFragment showLoginFragment(boolean isBack) {
        return newMineFragment(3, isBack);
    }

    public BackUserFragment showLoginFragment() {
        return showLoginFragment(false);
    }

    public BackUserFragment showSubscribeFragment() {
        return newMineFragment(0,false);
    }

    public BackUserFragment showHistoryFragment() {
        return newMineFragment(1,false);
    }

    public BackUserFragment showSettingFragment() {
        return newMineFragment(4,false);
    }

    public BackUserFragment showPurchasedFragment() {
        return newMineFragment(2,false);
    }

    public BackUserFragment showCoinFragment() {
        return newMineFragment(3,false);
    }
}