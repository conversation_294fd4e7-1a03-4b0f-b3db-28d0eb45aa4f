package com.kaolafm.kradio.online.message;

import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageButton;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.kaolafm.kradio.basedb.entity.meaasge.CrashMessageBean;
import com.kaolafm.kradio.basedb.manager.MessageDaoManager;
import com.kaolafm.kradio.common.base.BaseSkinAppCompatActivity;
import com.kaolafm.kradio.component.ComponentClient;
import com.kaolafm.kradio.component.DynamicComponent;
import com.kaolafm.kradio.component.MainThreadable;
import com.kaolafm.kradio.component.RealCaller;
import com.kaolafm.kradio.constant.UserComponentConst;
import com.kaolafm.kradio.constant.UserStateObserverProcessorConst;
import com.kaolafm.kradio.k_kaolafm.R; 
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.ComponentUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.router.IRouterConsumer;
import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.purchase.PayManager;
import com.kaolafm.kradio.purchase.constant.PayConst;
import com.kaolafm.kradio.user.UserInfoManager;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.PageShowReportEvent;
import com.kaolafm.report.util.ReportParameterManager;

import java.util.ArrayList;
import java.util.List; 

/**
 * 消息盒子
 */
@Route(path = RouterConstance.ACTIVITY_URL_MESSAGE)
public class OnlineMessageActivity extends BaseSkinAppCompatActivity<OnlineMessagePresenter> implements IRouterConsumer {
    private static final String TAG = "OnlineMessageActivity";
    
    ImageView message_back_mine; 
    AppCompatImageButton msg_tips_ib; 
    RecyclerView online_msg_rv;
    private DynamicComponent mRadioPlayerUserObserver;
    private OnlineMessageAdapter onlineMessageAdapter;
    private List<CrashMessageBean> messageBeanList = new ArrayList<>();
    protected long startTime = -1;

    @Override
    protected OnlineMessagePresenter createPresenter() {
        return new OnlineMessagePresenter();
    }

    @Override
    public int getLayoutId() {
        return R.layout.online_message_activity_layout;
    }

    @Override
    public String getPageId() {
        return Constants.PAGE_ID_MESSAGE;
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mRadioPlayerUserObserver = new RadioPlayerUserObserver();
        ComponentUtil.addObserver(UserComponentConst.NAME, mRadioPlayerUserObserver);
    }

    @Override
    protected void onStart() {
        super.onStart();
        startTime = System.currentTimeMillis();
    }

    @Override
    public void initView(Bundle savedInstanceState) {

        message_back_mine = findViewById(R.id.message_back_mine);
        msg_tips_ib = findViewById(R.id.msg_tips_ib);
        online_msg_rv = findViewById(R.id.online_msg_rv);
        
        
        
        RouterManager.getInstance().addRouterConsumer(this);
        message_back_mine.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        msg_tips_ib.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //提示
                ToastUtil.showNormal(AppDelegate.getInstance().getContext(), getString(R.string.online_message_activity_tips_text));
            }
        });


        onlineMessageAdapter = new OnlineMessageAdapter();
        online_msg_rv.setLayoutManager(new LinearLayoutManager(this));
        online_msg_rv.setAdapter(onlineMessageAdapter);
        DividerItemDecoration decor = new DividerItemDecoration(this, DividerItemDecoration.VERTICAL);
        ColorDrawable drawable = new ColorDrawable(ResUtil.getColor(R.color.color_white_10_transparent));
        drawable.setBounds(0, 0, 0, ResUtil.getDimen(R.dimen.m1));
        decor.setDrawable(drawable);
        online_msg_rv.addItemDecoration(decor);
        onlineMessageAdapter.setDataList(messageBeanList);
        onlineMessageAdapter.setOnItemClickListener(new BaseAdapter.OnItemClickListener<CrashMessageBean>() {
            @Override
            public void onItemClick(View view, int viewType, CrashMessageBean onlineMessageBean, int position) {
                MessageDetailsDialogFragment dialogFragment
                        = new MessageDetailsDialogFragment(OnlineMessageActivity.this);
                dialogFragment.setCrashMessageBean(onlineMessageBean)
                        .setMsgList(true)
                        .show();
//                CrashPlayerHelper.getInstance().addImmediatelyplayDate(AppDateUtils.getInstance().changeDate(messageBeanList.get(position))).startPlay();
                MessageDaoManager.getInstance().updateLook(messageBeanList.get(position).getMsgId());

                online_msg_rv.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        //刷新列表已读状态
                        initData();
                    }
                }, 100);
            }
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        initData();
        watchUserStateChangeAndReplayAudioList();
    }

    private void watchUserStateChangeAndReplayAudioList() {
        UserInfoManager userInfoManager = UserInfoManager.getInstance();
        boolean loinStateChange = userInfoManager.isLoginStateChange();
        boolean vipStateChange = userInfoManager.isVipStateChange();
        Log.i(TAG, "loinStateChange:" + loinStateChange + " vipStateChange:" + vipStateChange);
        if (loinStateChange || vipStateChange) {
            replayAudioList();
        }
//        replayAudioList();
    }

    @Override
    protected void onDestroy() {
        RouterManager.getInstance().removeRouterConsumer(this);
        super.onDestroy();
        ComponentUtil.removeObserver(UserComponentConst.NAME, mRadioPlayerUserObserver);
    }

    @Override
    public void initData() {
        if (onlineMessageAdapter != null) {
            messageBeanList.clear();
            messageBeanList = MessageDaoManager.getInstance().queryAllSync();
            onlineMessageAdapter.setDataList(messageBeanList);
        }
    }

    @Override
    public String consumeRoute(String pageId, Object extra) {
        switch (pageId) {
            case Constants.ONLINE_PAGE_ID_PAY_VIP: //VIP购买
                if (ComponentUtil.getResultValue(UserComponentConst.NAME, UserComponentConst.IS_USER_BOUND)) {
                    PayManager.getInstance()
                            .pay(PayConst.PAY_TYPE_VIP, PlayerManager.getInstance().getCurPlayItem());
                } else {
                    Bundle bundle = new Bundle();
                    bundle.putString("type", ReportParameterManager.getInstance().getPage());
                    RouterManager.getInstance().jumpPage(RouterConstance.ACTIVITY_URL_LOGIN, bundle);
                }
                break;
        }
        return ROUTER_CONSUME_FULLY;
    }

    private class RadioPlayerUserObserver implements DynamicComponent, MainThreadable {

        @Override
        public String getName() {
            return "onlineMessage-UserObserver";
        }

        @Override
        public boolean onCall(RealCaller caller) {
            String actionName = caller.actionName();
            switch (actionName) {
                case UserStateObserverProcessorConst.USER_LOGIN:
                    break;
                case UserStateObserverProcessorConst.USER_LOGOUT:
                    replayAudioList();
                    break;
                default:
                    break;
            }
            return false;
        }

        @Override
        public Boolean shouldActionRunOnMainThread(String actionName, ComponentClient caller) {
            return true;
        }
    }

    private void replayAudioList() {
//        public static final int BUY_TYPE_FREE = 0;//免费
//        public static final int BUY_TYPE_AUDITION = 1;//试听
//        public static final int BUY_TYPE_AUDIO = 2;//单曲购买
//        public static final int BUY_TYPE_ALBUM = 3;//专辑购买
//        public static final int BUY_TYPE_VIP = 4;//vip购买
        Log.d(TAG, "replayAudioList");
        if (PlayerManager.getInstance().isPlaying()) {
            //如果退出登录当前播放的资源是需要付费的就要刷新播放
            switch (PlayerManager.getInstance().getCurPlayItem().getBuyType()) {
                case AudioDetails.BUY_TYPE_AUDIO:
                case AudioDetails.BUY_TYPE_ALBUM:
                case AudioDetails.BUY_TYPE_VIP:
                    long mRadioId = Long.parseLong(PlayerManager.getInstance().getCurPlayItem().getRadioId());
                    int mRadioType = PlayerManager.getInstance().getCurPlayItem().getType();
                    PlayerManagerHelper.getInstance().restart(String.valueOf(mRadioId), mRadioType);
                    break;
            }
        }
    }


    @Override
    protected void onPause() {
        super.onPause();
        reportPageShowEvent();
    }

    /**
     * 页面曝光事件上报
     */
    private void reportPageShowEvent() {
        long duration = System.currentTimeMillis() - startTime;
        if (startTime < 0 || duration < 300) {
            return;
        }

        PageShowReportEvent event = new PageShowReportEvent();
        event.setPageId(getPageId());
        event.setPageTime(String.valueOf(duration));
        ReportHelper.getInstance().addEvent(event);
        Log.i("BaseDialogFragment", "report=" + duration);
        startTime = -1;
    }
}
