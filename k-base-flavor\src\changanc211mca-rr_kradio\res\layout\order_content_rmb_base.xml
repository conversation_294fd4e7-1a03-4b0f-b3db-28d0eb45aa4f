<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/x960"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:background="@drawable/order_pay_bg">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:background="@drawable/order_info_bg"
        android:id="@+id/ll_order_top"
        android:layout_marginLeft="@dimen/order_pay_margin_left"
        android:layout_marginRight="@dimen/order_pay_margin_right"
        android:layout_width="match_parent"
        android:layout_height="@dimen/order_content_top_height"
        android:orientation="horizontal"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toTopOf="@id/ll_order_bottom">

        <RelativeLayout
            android:id="@+id/rl_order_content"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:paddingTop="@dimen/m51"
            android:paddingBottom="@dimen/m39"
            android:paddingLeft="@dimen/m50"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/fl_order_qrcode"
            app:layout_constraintBottom_toBottomOf="parent">

            <FrameLayout
                android:id="@+id/fl_image"
                android:layout_width="@dimen/m200"
                android:layout_height="@dimen/m200">

                <com.kaolafm.kradio.view.OvalImageView
                    android:id="@+id/iv_order_info_image"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/order_pay_bg">
                </com.kaolafm.kradio.view.OvalImageView>

                <TextView
                    android:id="@+id/tv_tag"
                    android:layout_width="@dimen/x76"
                    android:layout_height="@dimen/y38"
                    android:background="@drawable/order_album_image_tag_bg"
                    android:gravity="center"
                    android:text="精品"
                    android:textStyle="bold"
                    android:textColor="@color/text_color_white"
                    android:textSize="@dimen/text_size3"
                    android:visibility="gone"></TextView>
                <com.kaolafm.kradio.view.OvalImageView
                    android:visibility="gone"
                    android:id="@+id/iv_tag"
                    android:layout_width="@dimen/x76"
                    android:layout_height="@dimen/y38"
                    android:layout_alignStart="@+id/iv_item_home_cover"
                    android:layout_alignTop="@+id/iv_item_home_cover"
                    android:scaleType="centerCrop"
                    app:rid_type="1"
                    android:src="@drawable/icon_vip" />
            </FrameLayout>

            <RelativeLayout
                android:id="@+id/rl_info_rmb"
                android:layout_toRightOf="@id/fl_image"
                android:layout_marginLeft="@dimen/m30"
                android:paddingTop="@dimen/m20"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <LinearLayout
                    android:id="@+id/ll_order_rmb_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">
                    <TextView
                        android:id="@+id/tv_order_rmb_title"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:text=""
                        android:textStyle="bold"
                        android:maxLines="2"
                        android:ellipsize="end"
                        android:textColor="@color/text_color_1"
                        android:textSize="@dimen/text_size5"
                        tools:text="德云社相声全集德社相"/>

                    <TextView
                        android:visibility="gone"
                        android:id="@+id/tv_order_rmb_tag"
                        android:layout_marginLeft="@dimen/m10"
                        android:padding="@dimen/m5"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:text=""
                        android:textColor="@color/text_color_1"
                        android:textSize="@dimen/text_size1"
                        android:background="@drawable/order_price_tag"
                        tools:visibility="visible"/>

                </LinearLayout>

                <LinearLayout
                    android:layout_below="@id/ll_order_rmb_title"
                    android:layout_marginTop="@dimen/m8"
                    android:id="@+id/ll_order_rmb_origin_price"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/m41"
                    android:orientation="horizontal">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:text="价格"
                        android:textColor="@color/text_color_1"
                        android:textSize="@dimen/text_size3"/>
                    <TextView
                        android:id="@+id/tv_order_rmb_origin_price"
                        android:layout_marginLeft="@dimen/m10"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="    "
                        android:textColor="@color/order_money_delete_color"
                        android:textSize="@dimen/text_size3"/>
                </LinearLayout>

                <LinearLayout
                    android:layout_below="@id/ll_order_rmb_origin_price"
                    android:id="@+id/ll_order_rmb_current_price"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="horizontal">
                    <TextView
                        android:id="@+id/tv_order_rmb_current"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="折扣价"
                        android:textColor="@color/text_color_1"
                        android:textSize="@dimen/text_size3"/>
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/m10"
                        android:text="¥"
                        android:textColor="@color/order_money_color"
                        android:textSize="@dimen/text_size1"/>
                    <TextView
                        android:id="@+id/tv_order_rmb_current_price"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="    "
                        android:textColor="@color/order_money_color"
                        android:textSize="@dimen/m50"
                        android:textStyle="bold"/>
                </LinearLayout>
            </RelativeLayout>

        </RelativeLayout>

        <FrameLayout
            android:id="@+id/fl_order_qrcode"
            android:layout_width="@dimen/x196"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginRight="@dimen/m49"
            android:layout_marginTop="@dimen/m24"
            app:layout_constraintLeft_toRightOf="@id/rl_order_content"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <LinearLayout
                android:id="@+id/ll_order_rmb"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center">

                <FrameLayout
                    android:layout_width="@dimen/m180"
                    android:layout_height="@dimen/m180"
                    android:background="@drawable/order_qr_bg">

                    <LinearLayout
                        android:id="@+id/ll_qrcode_failed"
                        android:layout_width="@dimen/m165"
                        android:layout_height="@dimen/m165"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:layout_gravity="center"
                        android:background="@drawable/order_qrcode_failed_bg">
                        <ImageView
                            android:id="@+id/iv_qrcode_refresh"
                            android:layout_width="@dimen/m32"
                            android:layout_height="@dimen/m32"
                            android:src="@drawable/order_loading"/>
                        <TextView
                            android:id="@+id/tv_qrcode_failed"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/text_color_1"
                            android:textSize="@dimen/text_size1"
                            android:text=""
                            android:gravity="center"/>
                    </LinearLayout>

                    <com.kaolafm.kradio.view.OvalImageView
                        android:id="@+id/iv_qrcode"
                        android:layout_gravity="center"
                        android:layout_width="@dimen/m165"
                        android:layout_height="@dimen/m165" />
                </FrameLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="@string/pay_open_third_method"
                        android:textColor="@color/text_color_1"
                        android:textSize="@dimen/text_size3" />
                </LinearLayout>
            </LinearLayout>

        </FrameLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ll_order_bottom"
        android:layout_marginLeft="@dimen/order_pay_margin_left"
        android:layout_marginRight="@dimen/order_pay_margin_right"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/m12"
        android:orientation="vertical"
        app:layout_constraintTop_toBottomOf="@id/ll_order_top"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <LinearLayout
            android:id="@+id/ll_notice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent">

            <TextView
                android:id="@+id/tv_rmb_buy_notice"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/y30"
                android:gravity="center"
                android:text="购买须知"
                android:textColor="@color/order_qrcode_title_color"
                android:textSize="@dimen/text_size3"/>

            <TextView
                android:id="@+id/tv_buy_notice_1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/pay_notice_1"
                android:lineSpacingMultiplier="1.1"
                android:textColor="@color/text_color_2"
                android:textSize="@dimen/text_size0"/>
        </LinearLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_count_down"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textSize="@dimen/text_size3"
        android:textColor="@color/text_color_1"
        android:layout_marginRight="@dimen/m25"
        android:layout_marginBottom="@dimen/m6"
        android:text="倒计时"
        app:layout_constraintTop_toBottomOf="@+id/ll_order_bottom"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>