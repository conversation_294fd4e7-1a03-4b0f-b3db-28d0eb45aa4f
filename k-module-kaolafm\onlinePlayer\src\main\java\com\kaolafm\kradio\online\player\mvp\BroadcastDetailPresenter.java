package com.kaolafm.kradio.online.player.mvp;

import com.kaolafm.kradio.common.bean.BroadcastRadioDetailData;
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.kradio.lib.base.ui.BaseActivity;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.lib.base.ui.online.BaseShowHideFragment;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.trello.rxlifecycle3.android.ActivityEvent;

public class BroadcastDetailPresenter extends BasePresenter<BroadcastDetailModel, BroadcastDetailView> {
    public BroadcastDetailPresenter(BroadcastDetailView view) {
        super(view);
    }

    @Override
    protected BroadcastDetailModel createModel() {
        if (mView instanceof BaseActivity) {
            return new BroadcastDetailModel(((BaseActivity) mView).bindUntilEvent(ActivityEvent.DESTROY));
        } else {
            return new BroadcastDetailModel(((BaseActivity) (((BaseShowHideFragment) mView).getActivity())).bindUntilEvent(ActivityEvent.DESTROY));
        }
    }

    public void getBroadcastDetail(long id) {
        mModel.getBroadcastDetail(id, new HttpCallback<BroadcastRadioDetailData>() {
            @Override
            public void onSuccess(BroadcastRadioDetailData broadcastRadioDetailData) {
                onGetBroadcastDetailSuccess(broadcastRadioDetailData);
            }

            @Override
            public void onError(ApiException e) {
                onGetBroadcastDetailError(e.getCode(), e.getMessage());
            }
        });
    }

    private void onGetBroadcastDetailSuccess(BroadcastRadioDetailData broadcastRadioDetailData) {
        if (mView == null) {
            return;
        }
        mView.onGetBroadcastDetailSuccess(broadcastRadioDetailData);
    }

    private void onGetBroadcastDetailError(int code, String msg) {
        if (mView == null) {
            return;
        }
        mView.onGetBroadcastDetailError(code, msg);
    }
}
