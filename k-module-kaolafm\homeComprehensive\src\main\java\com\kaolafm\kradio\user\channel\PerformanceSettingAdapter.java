package com.kaolafm.kradio.user.channel;

import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;


/**
 * <pre>
 *     <AUTHOR> Wenchl
 *     e-mail : <EMAIL>
 *     time   : 2019/02/18
 *     desc   :
 *     version: 1.0
 * </pre>
 */
public class PerformanceSettingAdapter extends BaseAdapter<PerformanceSettingAdapter.PerformanceItemBean> {

    private int lastSelected = 0;


    @Override
    protected BaseHolder<PerformanceItemBean> getViewHolder(ViewGroup parent, int viewType) {
        return new PerformanceSettingViewHolder(inflate(parent, R.layout.item_performance_setting, viewType));
    }


    class PerformanceSettingViewHolder extends BaseHolder<PerformanceItemBean> {

        TextView title;

        public PerformanceSettingViewHolder(View itemView) {
            super(itemView);
            title = itemView.findViewById(R.id.item_setting_title);
        }

        @Override
        public void setupData(PerformanceItemBean performanceItemBean, int position) {
            title.setText(performanceItemBean.title);
            if (performanceItemBean.isSelected) {
                title.setSelected(true);
            } else {
                title.setSelected(false);
            }
        }

    }

    public void setSelected(int position) {
        mDataList.get(lastSelected).setSelected(false);
        notifyItemChanged(lastSelected);
        mDataList.get(position).setSelected(true);
        notifyItemChanged(position);
        lastSelected = position;
    }

    public static class PerformanceItemBean {
        private boolean isSelected;
        private String title;

        public void setSelected(boolean selected) {
            isSelected = selected;
        }

        public boolean isSelected() {
            return isSelected;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getTitle() {
            return title;
        }
    }
}
