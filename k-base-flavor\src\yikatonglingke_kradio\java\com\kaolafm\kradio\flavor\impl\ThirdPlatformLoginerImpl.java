package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.util.Log;

import com.ecarx.sdk.ECarX;
import com.kaolafm.kradio.lib.base.flavor.ThirdPlatformLoginer;

/**
 * <AUTHOR>
 **/
public class ThirdPlatformLoginerImpl implements ThirdPlatformLoginer {

    @Override
    public void login(Context applicationContext, final Callback callback) {
        Log.i(ECarX.TAG, "ThirdPlatformLoginerImpl------->login start");
        final ECarX eCarX = ECarX.getInstance(applicationContext);
        eCarX.setLoginListener(new ECarX.LoginListener() {
            @Override
            public void onSuccess() {
                Log.i(ECarX.TAG, "ThirdPlatformLoginerImpl------->login onSuccess");
                eCarX.setLoginListener(null);
                if (callback != null) {
                    callback.onSuccess();
                }
            }

            @Override
            public void onError(Exception e) {
                Log.i(ECarX.TAG, "ThirdPlatformLoginerImpl------->login onError");
                eCarX.setLoginListener(null);
                if (callback != null) {
                    callback.onFailure();
                }
            }
        });
        eCarX.login();
    }

}
