package com.kaolafm.kradio.common.http;

import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.kradio.lib.utils.KaolaAppConfigData;
import com.kaolafm.kradio.lib.utils.MD5;
import com.kaolafm.opensdk.account.token.AccessTokenManager;
import com.kaolafm.opensdk.account.token.KaolaAccessToken;

import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;

import static com.kaolafm.kradio.lib.utils.Constants.BLANK_STR;
import static com.kaolafm.kradio.lib.utils.Constants.KEY_APP_ID;
import static com.kaolafm.kradio.lib.utils.Constants.KEY_DEVICE_ID;
import static com.kaolafm.kradio.lib.utils.Constants.KEY_OPEN_ID;
import static com.kaolafm.kradio.lib.utils.Constants.KEY_OS;
import static com.kaolafm.kradio.lib.utils.Constants.KEY_PACKAGE;
import static com.kaolafm.kradio.lib.utils.Constants.OS_NAME;

/**
 * @ClassName SignUtil
 * @Description 新的sign 生成工具, 只用于四个接口
 * <AUTHOR>
 * @Date 2020-02-12 13:43
 * @Version 1.0
 */
class SignUtil {
    private static final String KEY_ACCESS = "access";
    private static final String KEY_ACCESS_VALUE = "internal";

    public static String makeSign() {
        LinkedHashMap<String, String> data = new LinkedHashMap<>();
        KaolaAppConfigData kaolaAppConfigData = KaolaAppConfigData.getInstance();
        KaolaAccessToken kaolaAccessToken = AccessTokenManager.getInstance().getKaolaAccessToken();

        String appId = kaolaAppConfigData.getAppId();
        String pkgName = kaolaAppConfigData.getPackageName();//.replace("."+kaolaAppConfigData.getChannel(),"");
        String deviceId = kaolaAppConfigData.getUdid();

        /**
         * 下面的顺序不能调整, 调整后 验证不通过, 原来的逻辑是先排序在拼接在校验. 但是这版 樊海舰 不让排序.必须这么写, md.坑
         */
        data.put(KEY_APP_ID, appId);
        data.put(KEY_DEVICE_ID, deviceId);
        data.put(KEY_OPEN_ID, kaolaAccessToken.getOpenId());
        data.put(KEY_OS, OS_NAME);
        data.put(KEY_PACKAGE, pkgName);
        data.put(KEY_ACCESS, KEY_ACCESS_VALUE);
        Log.i(RequestInterceptManager.TAG, "SignUtil->deviceId = "+ deviceId);
        Log.i(RequestInterceptManager.TAG, "SignUtil->pkgName = "+ pkgName);
        Log.i(RequestInterceptManager.TAG, "SignUtil->openid = "+ kaolaAccessToken.getOpenId());

        String contentSub = data.toString().replace("{", BLANK_STR).replace("}", BLANK_STR).replace(" ", BLANK_STR);
        String[] totalValues = contentSub.split(",");
        String signStr = madeUrlSign(totalValues, kaolaAppConfigData.getAppKey());

        Log.d("makeSign","signStr = "+signStr);
        return MD5.getMD5Str(signStr);
    }

    /**
     * 生成url加密sign数组
     *
     * @param params
     * @return
     */
    private static String madeUrlSign(String[] params, String appKey) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(appKey);
        int index = 0;
        String preValue = null;
        for (String value : params) {
            if (TextUtils.isEmpty(value) || value.equals(preValue)) {
                continue;
            }
            String tempValue = value.trim();
            preValue = tempValue;
            stringBuilder.append(index++);
            stringBuilder.append(tempValue);
        }
        stringBuilder.append(appKey);
        return stringBuilder.toString();
    }
}
