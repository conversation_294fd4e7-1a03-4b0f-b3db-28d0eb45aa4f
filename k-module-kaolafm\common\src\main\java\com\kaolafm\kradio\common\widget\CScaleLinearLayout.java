package com.kaolafm.kradio.common.widget;

import android.annotation.TargetApi;
import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.widget.LinearLayout;

import com.kaolafm.kradio.lib.utils.AnimUtil;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2018-04-18 15:47
 *
 * 备注如果其子控件添加了点击事件可能会影响动画效果
 *
 ******************************************/

public class CScaleLinearLayout extends LinearLayout implements CScaleWidgetInter {
    private boolean canMakeAnimation = true;

    private MakeAnimationImpl mMakeAnimationImpl = new MakeAnimationImpl() {
        @Override
        public void makeAnimationPress(View view) {
            AnimUtil.startScalePress(view);
        }

        @Override
        public void makeAnimationRelease(View view) {
            AnimUtil.startScaleRelease(view);
        }
    };

    public CScaleLinearLayout(Context context) {
        super(context);
    }

    public CScaleLinearLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public CScaleLinearLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @TargetApi(21)
    public CScaleLinearLayout(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (canMakeAnimation) {
            int action = event.getAction();
            switch (action) {
                case MotionEvent.ACTION_DOWN:
                    if (mMakeAnimationImpl != null) {
                        mMakeAnimationImpl.makeAnimationPress(this);
                    }
                    break;
                case MotionEvent.ACTION_OUTSIDE:
                case MotionEvent.ACTION_CANCEL:
                case MotionEvent.ACTION_UP:
                    if (mMakeAnimationImpl != null) {
                        mMakeAnimationImpl.makeAnimationRelease(this);
                    }
                    break;
                default:
                    break;
            }
        }
        return super.onTouchEvent(event);
    }

    @Override
    public void setMakeAnimationImpl(MakeAnimationImpl makeAnimationImpl) {
        this.mMakeAnimationImpl = makeAnimationImpl;
    }
}
