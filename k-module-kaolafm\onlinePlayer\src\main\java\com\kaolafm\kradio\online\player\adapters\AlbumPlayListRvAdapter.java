package com.kaolafm.kradio.online.player.adapters;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.AnimationDrawable;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import androidx.constraintlayout.widget.ConstraintLayout;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.kradio.common.CP;
import com.kaolafm.kradio.common.ErrorInfo;
import com.kaolafm.kradio.common.ResultCallback;
import com.kaolafm.kradio.common.SubscribeData;
import com.kaolafm.kradio.common.utils.TimeUtil;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.kradio.online.player.base.OnlineBasePlayListRvAdapter;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.player.utils.AudioSubscribeCacheUtil;
import com.kaolafm.kradio.subscribe.SubscribeHelper;
import com.kaolafm.kradio.user.UserInfoManager;
import com.kaolafm.opensdk.api.media.model.AlbumDetails;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.api.subscribe.SubscribeRequest;
import com.kaolafm.opensdk.player.logic.model.item.AlbumPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.RadioPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.report.event.LoginReportEvent;
import com.kaolafm.report.event.SubscibeReportEvent;

import java.util.List;

import static android.view.View.GONE;
import static android.view.View.VISIBLE;
import static com.kaolafm.kradio.common.utils.TimeUtil.getAudioUpdateTime;

public class AlbumPlayListRvAdapter extends OnlineBasePlayListRvAdapter {
    private String TAG = AlbumPlayListRvAdapter.class.getSimpleName();
    private PlayItem mWannarSubscribePlayItem = null;//需要自动订阅的单曲
    private Context context;

    public AlbumPlayListRvAdapter(List<PlayItem> dataList) {
        super(dataList);
    }

    @Override
    public void notifyAdapterPlayItemPaused(PlayItem playItem) {
        int position = findPlayItemPosition(playItem.getAudioId());
        if (position >= 0)
            notifyItemChanged(position, "pauseAnimation");
    }

    @Override
    public void notifyAdapterPlayItemResume(PlayItem playItem) {
        int position = findPlayItemPosition(playItem.getAudioId());
        if (position >= 0)
            notifyItemChanged(position, "resumeAnimation");
    }

    private int findPlayItemPosition(long audioId) {
        PlayItem mPlayItem;
        for (int i = 0; i < getDataList().size(); i++) {
            mPlayItem = getItemData(i);
            if (mPlayItem.getAudioId() == audioId) return i;
        }
        return -1;
    }

    @Override
    public void onBindViewHolder(BaseHolder<PlayItem> holder, int position, List<Object> payloads) {
        if (payloads == null || payloads.size() == 0) {
            super.onBindViewHolder(holder, position, payloads);
        } else {
            boolean isResume = "resumeAnimation".equals(payloads.get(0));
            if (holder instanceof PlayItemNormalViewHolder) {
                Drawable drawable = ((PlayItemNormalViewHolder) holder).getIvPlaying().getDrawable();
                if (drawable instanceof AnimationDrawable) {
                    AnimationDrawable animationDrawable = (AnimationDrawable) drawable;
                    if (isResume && !animationDrawable.isRunning())
                        animationDrawable.start();
                    else if (!isResume && animationDrawable.isRunning()) animationDrawable.stop();
                }
            }
        }
    }

    @Override
    protected BaseHolder<PlayItem> getViewHolder(ViewGroup parent, int viewType) {
        return new PlayItemNormalViewHolder(inflate(parent, R.layout.online_player_item_rv_play_item_album, 0));
    }

    private class PlayItemNormalViewHolder extends BaseHolder<PlayItem> {
        private TextView tvItemTitle, tvSubtitle, audioListenFlagTv;
        private ImageView ivPlaying, ivLiked, ivBuyType;

        private int mMeasureSpec, originTvItemTitleHeight, originTvSubtitleHeight;


        public ImageView getIvPlaying() {
            return ivPlaying;
        }

        public PlayItemNormalViewHolder(View itemView) {
            super(itemView);
            mMeasureSpec = View.MeasureSpec.makeMeasureSpec(View.MeasureSpec.UNSPECIFIED, View.MeasureSpec.UNSPECIFIED);
            tvItemTitle = itemView.findViewById(R.id.tvItemTitle);
            audioListenFlagTv = itemView.findViewById(R.id.audioListenFlagTv);
            tvSubtitle = itemView.findViewById(R.id.subtitle);
            ivPlaying = itemView.findViewById(R.id.ivPlaying);
            ivLiked = itemView.findViewById(R.id.ivLiked);
            ivBuyType = itemView.findViewById(R.id.ivBuyType);
            ivLiked.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (AntiShake.check(v.getId())) {
                        return;
                    }
                    if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(AppDelegate.getInstance().getContext())) {
                        return;
                    }
                    boolean userBound = UserInfoManager.getInstance().isUserBound();
                    // 触发订阅
                    PlayItem playItem = getItemData(getAdapterPosition());
                    if (!userBound) {
                        mWannarSubscribePlayItem = playItem;
                        context = ivLiked.getContext();
                        toLoginFragment();
                        return;
                    }

                    clickIvLiked(playItem, ivLiked);
                }
            });
            if (tvItemTitle.getMeasuredHeight() == 0)
                tvItemTitle.measure(mMeasureSpec, mMeasureSpec);
            if (tvSubtitle.getMeasuredHeight() == 0)
                tvSubtitle.measure(mMeasureSpec, mMeasureSpec);
            originTvItemTitleHeight = tvItemTitle.getMeasuredHeight();
            originTvSubtitleHeight = tvSubtitle.getMeasuredHeight();
        }


        @Override
        public void setupData(PlayItem playItem, int position) {
            String titleValue = null, updateTime = null, subtitle = null;
            boolean isLiked = false;
            if (playItem instanceof AlbumPlayItem) {
                ViewUtil.setViewVisibility(ivLiked, View.VISIBLE);
                AlbumPlayItem broadcast = (AlbumPlayItem) playItem;
                String order = tvItemTitle.getContext().getResources().getString(R.string.album_audio_info_format_str);
                order = StringUtil.format(order, ((AlbumPlayItem) playItem).getInfoData().getOrderNum());
                titleValue = order + "  " + broadcast.getInfoData().getTitle();

                updateTime = playItem.getUpdateTime();
                subtitle = String.format(this.itemView.getContext().getString(R.string.online_player_album_play_item_subtitle),
                        getAudioUpdateTime(this.itemView.getContext(), Long.parseLong(updateTime)),
                        TimeUtil.getStringForTime(playItem.getDuration(), true));

                if (AudioSubscribeCacheUtil.getInstance().containsKey(playItem.getAudioId())) {
                    boolean isSub = AudioSubscribeCacheUtil.getInstance().get(playItem.getAudioId());
                    Logger.i(TAG, "contains key=" + playItem.getAudioId() + " ,isSub=" + isSub);
                    ivLiked.setImageResource(isSub ? R.drawable.online_player_liked_red :
                            R.drawable.online_player_like);
                } else {
                    Logger.i(TAG, "not contains key=" + playItem.getAudioId());
                    ivLiked.setImageResource(R.drawable.online_player_like);
                    getAudioSubscribeState(playItem, ivLiked);
                }
            } else if (playItem instanceof RadioPlayItem) {
                ViewUtil.setViewVisibility(ivLiked, View.GONE);
                titleValue = playItem.getTitle();

                updateTime = playItem.getUpdateTime();
                subtitle = String.format(this.itemView.getContext().getString(R.string.online_player_album_play_item_subtitle),
                        getAudioUpdateTime(this.itemView.getContext(), Long.parseLong(updateTime)),
                        TimeUtil.getStringForTime(playItem.getDuration()));
            } else return;

            Drawable drawable = ivPlaying.getDrawable();
            if (AlbumPlayListRvAdapter.this.selectPosition != position || !AlbumPlayListRvAdapter.this.needInit) {
                tvItemTitle.setEllipsize(TextUtils.TruncateAt.END);
                ViewUtil.setViewVisibility(ivPlaying, View.GONE);
                if (drawable instanceof AnimationDrawable) {
                    ((AnimationDrawable) drawable).stop();
                }
                itemView.setBackgroundColor(Color.TRANSPARENT);
            } else {
                tvItemTitle.setEllipsize(TextUtils.TruncateAt.MARQUEE);
                if (drawable instanceof AnimationDrawable) {
                    if (PlayerManagerHelper.getInstance().isPlaying())
                        ((AnimationDrawable) drawable).start();
                    else ((AnimationDrawable) drawable).stop();
                }
                ViewUtil.setViewVisibility(ivPlaying, View.VISIBLE);
                itemView.setBackgroundResource(R.drawable.online_player_sound_selected);
            }
            tvItemTitle.setText(titleValue);
            if (!TextUtils.isEmpty(updateTime) && !PlayerManagerHelper.getInstance().isRadioTaiXuan(playItem)) {
                if (tvSubtitle.getVisibility() == View.GONE) {
                    ViewGroup.LayoutParams layoutParams = tvItemTitle.getLayoutParams();
                    layoutParams.height = originTvItemTitleHeight;
                    tvItemTitle.setLayoutParams(layoutParams);
                }
                tvSubtitle.setText(subtitle);
                ViewUtil.setViewVisibility(tvSubtitle, View.VISIBLE);
            } else {
                if (tvSubtitle.getVisibility() == View.VISIBLE) {
                    ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) tvItemTitle.getLayoutParams();
                    layoutParams.height = originTvItemTitleHeight + originTvSubtitleHeight +
                            tvSubtitle.getContext().getResources().getDimensionPixelSize(R.dimen.y8);
                    tvItemTitle.setLayoutParams(layoutParams);
                }
                tvSubtitle.setText(null);
                ViewUtil.setViewVisibility(tvSubtitle, View.GONE);
            }


            int buyStatus = playItem.getBuyStatus();
            if (!UserInfoManager.getInstance().isUserLogin()) {
                buyStatus = AlbumDetails.BUY_STATUS_NOT_PURCHASE;
            }
            int buyType = playItem.getBuyType();
            resetListenFlag(buyStatus, buyType);
        }


        private void resetListenFlag(int buyStatus, int buyType) {
            if (buyStatus == AlbumDetails.BUY_STATUS_PURCHASE) {
                if (buyType == AudioDetails.BUY_TYPE_AUDIO) {
                    //已购 标签仅在精品标识资源——碎片购买成功情况下展示；
                    audioListenFlagTv.setVisibility(VISIBLE);
                    audioListenFlagTv.setText(R.string.online_vip_purchased_text);
                    //fixed 已购条目需要隐藏右侧icon，否则会因为条目复用展示错误
                } else {
                    audioListenFlagTv.setVisibility(GONE);
                }
                ivBuyType.setVisibility(GONE);
            } else if (buyStatus == AlbumDetails.BUY_STATUS_NOT_PURCHASE) {
                if (buyType == AudioDetails.BUY_TYPE_AUDITION) {
                    //试听
                    audioListenFlagTv.setVisibility(VISIBLE);
                    audioListenFlagTv.setText(R.string.audition);
                    //fixed 试听条目需要隐藏右侧icon，否则会因为条目复用展示错误
                    ivBuyType.setVisibility(GONE);
                } else {
                    audioListenFlagTv.setVisibility(GONE);
                    if (buyType == AudioDetails.BUY_TYPE_AUDIO) {
                        //碎片是碎片购买的
                        ivBuyType.setVisibility(VISIBLE);
                        ivBuyType.setImageResource(R.drawable.online_player_album_money);
                    } else if (buyType == AudioDetails.BUY_TYPE_ALBUM) {
                        //付费专辑的
                        ivBuyType.setVisibility(VISIBLE);
                        ivBuyType.setImageResource(R.drawable.online_player_album_clock);
                    } else if (buyType == AudioDetails.BUY_TYPE_VIP) {
                        //vip
                        ivBuyType.setVisibility(VISIBLE);
                        ivBuyType.setImageResource(R.drawable.online_player_album_clock);
                    } else {
                        ivBuyType.setVisibility(GONE);
                    }
                }
            }
        }
    }

    private void clickIvLiked(PlayItem playItem, ImageView ivLiked) {
        if (AudioSubscribeCacheUtil.getInstance().containsKey(playItem.getAudioId())) {
            boolean isSubscribe = AudioSubscribeCacheUtil.getInstance().get(playItem.getAudioId());
            unSubscribeOrSubscribe(playItem, isSubscribe, ivLiked);
        } else {
            getAudioSubscribeState(playItem, ivLiked);
        }
    }

    private void unSubscribeOrSubscribe(PlayItem playItem, boolean isSubscribe, ImageView ivLiked) {
        if (isSubscribe) {
            unSubscribeAudio(playItem, ivLiked);
        } else {
            subscribeAudio(playItem, ivLiked);
        }
    }

    private void unSubscribeAudio(PlayItem playItem, ImageView ivLiked) {
        SubscribeHelper.unsubscribe(CP.KaoLaFM, makeSubscribeData(playItem.getAudioId(), playItem.getType()), new ResultCallback() {
            @Override
            public void onResult(boolean result, int code) {
                if (result) {
                    ivLiked.setImageResource(R.drawable.online_player_like);
                    AudioSubscribeCacheUtil.getInstance().put(playItem.getAudioId(), false);
                    ToastUtil.showOnActivity(ivLiked.getContext(),
                            ResUtil.getString(R.string.un_subscribed_success_str));
                } else {
                    ToastUtil.showOnActivity(ivLiked.getContext(),
                            ResUtil.getString(R.string.un_subscribe_failed_str));
                }
            }

            @Override
            public void onFailure(ErrorInfo errorInfo) {
                ToastUtil.showOnActivity(ivLiked.getContext(),
                        ResUtil.getString(R.string.un_subscribe_failed_str));
            }
        });
    }

    private SubscribeData makeSubscribeData(long subscribeId, int type) {
        type = SubscribeRequest.TYPE_SONG;
        SubscribeData subscribeData = new SubscribeData();
        subscribeData.setLocation(SubscibeReportEvent.POSITION_PLAY_LIST);
        subscribeData.setId(subscribeId);
        subscribeData.setType(type);
        return subscribeData;
    }

    private void subscribeAudio(PlayItem playItem, ImageView ivLiked) {
        SubscribeHelper.subscribe(CP.KaoLaFM, makeSubscribeData(playItem.getAudioId(), playItem.getType()), new ResultCallback() {
            @Override
            public void onResult(boolean result, int code) {
                if (result) {
                    ivLiked.setImageResource(R.drawable.online_player_liked_red);
                    AudioSubscribeCacheUtil.getInstance().put(playItem.getAudioId(), true);
                    ToastUtil.showOnActivity(ivLiked.getContext(),
                            ResUtil.getString(R.string.subscribed_success_str));
                } else {
                    ToastUtil.showOnActivity(ivLiked.getContext(),
                            ResUtil.getString(R.string.subscribe_failed_str));
                }
            }

            @Override
            public void onFailure(ErrorInfo errorInfo) {
                ToastUtil.showOnActivity(ivLiked.getContext(),
                        ResUtil.getString(R.string.subscribe_failed_str));
            }
        });
    }

    private void subscribeAudio(PlayItem playItem, Context context) {
        SubscribeHelper.subscribe(CP.KaoLaFM, makeSubscribeData(playItem.getAudioId(), playItem.getType()), new ResultCallback() {
            @Override
            public void onResult(boolean result, int code) {
                if (result) {
                    AudioSubscribeCacheUtil.getInstance().put(playItem.getAudioId(), true);
                    ToastUtil.showOnActivity(context,
                            ResUtil.getString(R.string.subscribed_success_str));
                    refreshViewHolder(playItem);
                } else {
                    ToastUtil.showOnActivity(context,
                            ResUtil.getString(R.string.subscribe_failed_str));
                }
            }

            @Override
            public void onFailure(ErrorInfo errorInfo) {
                ToastUtil.showOnActivity(context,
                        ResUtil.getString(R.string.subscribe_failed_str));
            }
        });
    }

    private void refreshViewHolder(PlayItem playItem) {
        Log.d(TAG, "自动订阅：刷新viewHolder ,audioId=" + playItem.getAudioId());
        PlayItem temp;
//        List<PlayItem> dataList = getDataList();
//        List<PlayItem> newList = Arrays.asList(new PlayItem[dataList.size()]);
//        Collections.copy(newList, dataList);
        List<PlayItem> newList = getDataList();
        for (int i = 0; i < newList.size(); i++) {
            temp = newList.get(i);
            if (temp.getAudioId() == playItem.getAudioId() && getItemCount() > i) {
                notifyItemChanged(i);
                return;
            }
        }
    }

    private void getAudioSubscribeState(PlayItem playItem, ImageView ivLiked) {
        SubscribeHelper.isSubscribed(CP.KaoLaFM, String.valueOf(playItem.getAudioId()), new ResultCallback() {
            @Override
            public void onResult(boolean result, int code) {
                Logger.i(TAG, "result=" + result + ":code=" + code + ":id=" + playItem.getAudioId());
                AudioSubscribeCacheUtil.getInstance().put(playItem.getAudioId(), result);
                if (ivLiked == null) return;
                if (result) {
                    ivLiked.setImageResource(R.drawable.online_player_liked_red);
                } else {
                    ivLiked.setImageResource(R.drawable.online_player_like);
                }
            }

            @Override
            public void onFailure(ErrorInfo errorInfo) {
                Logger.i(TAG, errorInfo.toString());
                if (ivLiked == null) return;
                ivLiked.setImageResource(R.drawable.online_player_like);
            }
        });
    }


    private void toLoginFragment() {
        Bundle bundle = new Bundle();
        bundle.putString("type", LoginReportEvent.ONLINE_REMARKS1_PLAY_CAROUSEL);
        RouterManager.getInstance().jumpPage(RouterConstance.ACTIVITY_URL_LOGIN, bundle);
    }

    public void autoSubscribe() {
        if (mWannarSubscribePlayItem == null) return;
        subscribeAudio(mWannarSubscribePlayItem, context);
    }
}
