package com.kaolafm.kradio.component.ui.brandpage;

import androidx.annotation.NonNull;
import android.view.View;

import com.kaolafm.kradio.component.ui.R;
import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.component.ui.base.CellBinder;
import com.kaolafm.kradio.component.ui.base.ItemClickSupport;
import com.kaolafm.kradio.component.ui.brandpage.carownerradio.CarOwnerRadioEntrance;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.utils.ResUtil;


/**
 * 品牌电台入口组件
 */
public class ComponentBrandPageHomeCardCell extends HomeCell implements CellBinder<View, ComponentBrandPageHomeCardCell>, ItemClickSupport {

    CarOwnerRadioEntrance car_owner_radio;

    @Override
    public void mountView(@NonNull ComponentBrandPageHomeCardCell data, @NonNull View view, int position) {
        if (data.getContentList() == null || data.getContentList().size() == 0) {
            return;
        }
        car_owner_radio=view.findViewById(R.id.car_owner_radio);
        car_owner_radio.setContentDescription(ResUtil.getString(R.string.content_desc_enter));
        car_owner_radio.runAnimationOnEnter(false, true);
        car_owner_radio.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onViewClickListener != null) {
                    v.setTag(0);
                    onViewClickListener.onViewClick(v, getPositionInParent());
                }
            }
        });
        car_owner_radio.updateDate(data);
    }


    @Override
    public int getItemType() {
        return R.layout.component_brand_page_home_card_layout;
    }

    private BaseHolder.OnViewClickListener onViewClickListener;

    @Override
    public void setOnItemClickListener(BaseHolder.OnViewClickListener listener) {
        onViewClickListener = listener;
    }
}
