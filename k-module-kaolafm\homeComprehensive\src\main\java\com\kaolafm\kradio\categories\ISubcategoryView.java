package com.kaolafm.kradio.categories;

import androidx.annotation.StringRes;
import com.flyco.tablayout.listener.CustomTabEntity;
import com.kaolafm.kradio.lib.bean.SubcategoryItemBean;
import com.kaolafm.kradio.lib.base.mvp.IView;
import com.kaolafm.opensdk.http.error.ApiException;
import java.util.ArrayList;
import java.util.List;

/**
 * 子分类view回调接口
 * <AUTHOR>
 * @date 2018/4/24
 */

public interface ISubcategoryView extends IView{

    void showError(ApiException e);

    void showSubcontent(List<SubcategoryItemBean> subcategoryItemBeans);

    void showSubtitles(ArrayList<CustomTabEntity> subtabs);

    void toast(@StringRes int stringId);
}
