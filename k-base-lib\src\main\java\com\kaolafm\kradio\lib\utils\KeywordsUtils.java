package com.kaolafm.kradio.lib.utils;

import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class KeywordsUtils {

    public static SpannableString highlightKeywords(String originText, List<String> keywords, int color) {
        SpannableString s = new SpannableString(originText);

        for (int i = 0; i < keywords.size(); i++) {
            final String keyword = keywords.get(i);
            try {
                Pattern p = Pattern.compile(escapeExprSpecialWord(keyword), Pattern.CASE_INSENSITIVE);
                Matcher m = p.matcher(s);

                while (m.find()) {
                    int start = m.start();
                    int end = m.end();

                    s.setSpan(new ForegroundColorSpan(color), start, end,
                            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                }
            }catch (Exception e){
                e.printStackTrace();
            }
        }

        return s;
    }

    /**
     * 转义正则特殊字符 （$()*+.[]?\^{},|）
     *
     * @param keyword
     * @return keyword
     */
    public static String escapeExprSpecialWord(String keyword) {
        if (!TextUtils.isEmpty(keyword)) {
            String[] fbsArr = { "\\", "$", "(", ")", "*", "+", ".", "[", "]", "?", "^", "{", "}", "|" };
            for (String key : fbsArr) {
                if (keyword.contains(key)) {
                    keyword = keyword.replace(key, "\\" + key);
                }
            }
        }
        return keyword;
    }
}
