package com.kaolafm.kradio.user;

import android.content.Context;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.user.bean.ToneQualityItemBean;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.dialog.BaseCenterDialog;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;

import java.util.ArrayList;

/**
 * <AUTHOR> on 2019/4/28.
 * 音质选择
 */

public class ToneQualityView extends BaseCenterDialog {

    TextView soundQualityTitle;
    RecyclerView soundQualityRecyclerView;

    private ToneQualityAdapter mToneQualityAdapter;
    private ArrayList<ToneQualityItemBean> mToneQualityItemBeanArrayList;

    public ToneQualityView(Context context) {
        super(context);
        initView();
    }

    @Override
    protected void initView(View view) {
        soundQualityTitle=view.findViewById(R.id.sound_quality_title);
        soundQualityRecyclerView=view.findViewById(R.id.sound_quality_recycler_view);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.view_sound_quality;
    }

    private void initView() {
        initData();
        mToneQualityAdapter = new ToneQualityAdapter();
        mToneQualityAdapter.setDataList(mToneQualityItemBeanArrayList);
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(mContext, LinearLayoutManager.VERTICAL, false);
        soundQualityRecyclerView.setLayoutManager(linearLayoutManager);
        soundQualityRecyclerView.setAdapter(mToneQualityAdapter);
    }

    private class ToneQualityAdapter extends BaseAdapter<ToneQualityItemBean> {
        @Override
        protected BaseHolder<ToneQualityItemBean> getViewHolder(ViewGroup parent, int viewType) {
            return new ToneQualityViewHolder(inflate(parent, R.layout.item_view_sound_quality, viewType));
        }
    }

    public class ToneQualityViewHolder extends BaseHolder<ToneQualityItemBean> {

        ConstraintLayout constraintLayout;
        TextView itemTitle;
        TextView itemTitleExplain;
        ImageView itemSelect;

        public ToneQualityViewHolder(View itemView) {
            super(itemView);
            constraintLayout=itemView.findViewById(R.id.item_sound_quality_main_layout);
            itemTitle=itemView.findViewById(R.id.item_title);
            itemTitleExplain=itemView.findViewById(R.id.item_title_explain);
            itemSelect=itemView.findViewById(R.id.item_select);
        }

        @Override
        public void setupData(ToneQualityItemBean toneQualityItemBean, int position) {
            if (toneQualityItemBean == null) {
                return;
            }
            itemTitle.setText(toneQualityItemBean.getmTitle());
            itemTitleExplain.setText(toneQualityItemBean.getmSubTitle());
            if (toneQualityItemBean.isSelect()) {
                itemTitle.setActivated(true);
                itemTitleExplain.setActivated(true);
                ViewUtil.setViewVisibility(itemSelect, View.VISIBLE);
            } else {
                itemTitle.setActivated(false);
                itemTitleExplain.setActivated(false);
                ViewUtil.setViewVisibility(itemSelect, View.GONE);
            }
            constraintLayout.setTag(toneQualityItemBean);
            constraintLayout.setOnClickListener(this);

        }

        @Override
        public void onClick(View v) {
            super.onClick(v);
            ToneQualityItemBean toneQualityItemBean = (ToneQualityItemBean) v.getTag();
            if (toneQualityItemBean == null) {
                return;
            }
            if (toneQualityItemBean.isSelect()) {
                return;
            }
            toneQualityItemBean.setSelect(true);
            switch (toneQualityItemBean.getPosition()) {
                case 0: {
//                    ToneQualityHelper.getInstance().setToneQuality(PlayerConstants.ToneQuality.LOW_TONE_QUALITY);
                }
                break;
                case 1: {
//                    ToneQualityHelper.getInstance().setToneQuality(PlayerConstants.ToneQuality.MIDDLE_TONE_QUALITY);
                }
                break;
                case 2: {
//                    ToneQualityHelper.getInstance().setToneQuality(PlayerConstants.ToneQuality.HIGH_TONE_QUALITY);
                }
                break;
                case 3: {
//                    ToneQualityHelper.getInstance().setToneQuality(PlayerConstants.ToneQuality.HIGHER_TONE_QUALITY);
                }
                break;
                default: {
                }
                break;
            }
            dismiss();
        }
    }

    private void initData() {
        mToneQualityItemBeanArrayList = new ArrayList<>();
        String[] title = ResUtil.getStringArray(R.array.tone_quality_title);
        String[] titleExplain = ResUtil.getStringArray(R.array.tone_quality_title_explain);
        int size = title.length;
//        int toneValue = ToneQualityHelper.getInstance().getToneQuality();
//
//        for (int i = 0; i < size; i++) {
//            ToneQualityItemBean toneQualityItemBean = new ToneQualityItemBean();
//            toneQualityItemBean.setmTitle(title[i]);
//            toneQualityItemBean.setmSubTitle(titleExplain[i]);
//            if (toneValue == i+1) {
//                toneQualityItemBean.setSelect(true);
//            } else {
//                toneQualityItemBean.setSelect(false);
//            }
//            toneQualityItemBean.setPosition(i);
//            mToneQualityItemBeanArrayList.add(toneQualityItemBean);
//        }
    }
}
