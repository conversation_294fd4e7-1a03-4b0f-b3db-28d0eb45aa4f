package com.kaolafm.ad.comprehensive.ads.image.base;

import android.content.Context;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import io.reactivex.Observable;
import io.reactivex.Observer;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;
import java.util.concurrent.TimeUnit;

public abstract class BaseAdWithDurationContentView<T> extends BaseAdContentView<T> {
    protected long mDuration;
    protected Disposable mCloseDisposable;

    public BaseAdWithDurationContentView(Context context) {
        super(context);
    }

    public BaseAdWithDurationContentView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public BaseAdWithDurationContentView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void countdownToCloseAd(){
        Observable.timer(mDuration, TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<Long>() {
                    @Override
                    public void onSubscribe(Disposable d) {
                        mCloseDisposable = d;
                    }

                    @Override
                    public void onNext(Long aLong) {
                        mAdImageListener.onAdComplete();
                    }

                    @Override
                    public void onError(Throwable e) {
                        cancelClose();
                    }

                    @Override
                    public void onComplete() {
                        cancelClose();
                    }
                });
    }

    protected void cancelClose(){
        if(mCloseDisposable != null && !mCloseDisposable.isDisposed()){
            mCloseDisposable.dispose();
            mCloseDisposable = null;
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        cancelClose();
    }
}
