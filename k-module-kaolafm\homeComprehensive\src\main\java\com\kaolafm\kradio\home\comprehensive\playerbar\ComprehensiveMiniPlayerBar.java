package com.kaolafm.kradio.home.comprehensive.playerbar;

import android.app.Activity;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleObserver;
import androidx.lifecycle.OnLifecycleEvent;
import androidx.lifecycle.Lifecycle.Event;
import android.content.Context;
import android.graphics.drawable.Drawable;
import androidx.appcompat.app.AppCompatActivity;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.Interpolator;
import android.view.animation.LinearInterpolator;
import android.view.animation.RotateAnimation;
import android.widget.ImageView;
import com.kaolafm.kradio.common.event.BroadcastPlayerChangedData;
import com.kaolafm.kradio.home.comprehensive.playerbar.ComprehensivePlayerbarContract.IPlayerView;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.k_kaolafm.R.drawable;
import com.kaolafm.kradio.k_kaolafm.R.id;
import com.kaolafm.kradio.k_kaolafm.R.layout;
import com.kaolafm.kradio.lib.base.flavor.KRadioToastInter;
import com.kaolafm.kradio.lib.bean.BroadcastRadioSimpleData;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.BaseReportEventBean;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.util.ReportParameterManager;
import java.util.HashMap;
import kotlin.TypeCastException;
import kotlin.jvm.internal.Intrinsics;
import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import skin.support.constraint.SkinCompatConstraintLayout;

public  class ComprehensiveMiniPlayerBar extends SkinCompatConstraintLayout implements IPlayerView {
    private final String TAG;
    private final int mMeasureSpec;
    private ImageView playerBarCover;
    private ImageView playerBarPlay;
    private ImageView playerBarNext;
    private View playerBarLoading;
    private boolean isLiveState;
    private ComprehensivePlayerBarPresenter mPresenter;
    private final RotateAnimation loadingAnimation;
    @Nullable
    private LifecycleObserver lifecycleObserver;
    private HashMap _$_findViewCache;

    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(this.mMeasureSpec, this.mMeasureSpec);
    }

    public void setEnabled(boolean enabled) {
        super.setEnabled(enabled);
        this.setPlayerAndPauseBtnState(enabled);
        this.setNextState(enabled);
    }

    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        EventBus.getDefault().register(this);
        this.attachPlayer();
        this.addLifeObs();
    }

    public final void attachPlayer() {
        ComprehensivePlayerBarPresenter var10000 = this.mPresenter;
        if (var10000 != null) {
            var10000.attachPlayer();
        }

    }

    @Nullable
    public final LifecycleObserver getLifecycleObserver() {
        return this.lifecycleObserver;
    }

    public final void setLifecycleObserver(@Nullable LifecycleObserver var1) {
        this.lifecycleObserver = var1;
    }

    private final void addLifeObs() {
        Lifecycle var10000 = this.getLifecycle();
        LifecycleObserver var1 = new LifecycleObserver() {
            @OnLifecycleEvent(Event.ON_RESUME)
            public final void onResume() {
                ComprehensiveMiniPlayerBar.this.resetPlayerBarState();
                ComprehensivePlayerBarPresenter var10000 = ComprehensiveMiniPlayerBar.this.mPresenter;
                if (var10000 != null) {
                    var10000.updateBtnsState();
                }

            }
        };
        Lifecycle var6 = var10000;
        this.lifecycleObserver = (LifecycleObserver)var1;
        var6.addObserver((LifecycleObserver)var1);
    }

    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        EventBus.getDefault().unregister(this);
        this.detachPlayer();
        this.removeLifeObs();
    }

    private final void removeLifeObs() {
        if (this.lifecycleObserver != null) {
            Lifecycle var10000 = this.getLifecycle();
            LifecycleObserver var10001 = this.lifecycleObserver;
            if (var10001 == null) {
                Intrinsics.throwNpe();
            }

            var10000.removeObserver(var10001);
            this.lifecycleObserver = (LifecycleObserver)null;
        }

    }

    private final void detachPlayer() {
        ComprehensivePlayerBarPresenter var10000 = this.mPresenter;
        if (var10000 != null) {
            var10000.detachPlayer();
        }

    }

    public final void setPlayerAndPauseBtnState(boolean enabled) {
        ImageView var10000 = this.playerBarPlay;
        if (var10000 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("playerBarPlay");
        }

        var10000.setEnabled(enabled);
    }

    static class OnClickListenerWrapper implements OnClickListener{

        private OnClickListener listener;
        public OnClickListenerWrapper(OnClickListener listener){
            this.listener = listener;
        }

        @Override
        public void onClick(View v) {
            if(listener != null){
                v.postDelayed(() ->{
                    listener.onClick(v);
                }, 300);
            }
        }
    }

    public void setPlayOrPauseClickListener(@Nullable OnClickListener listener) {
        ImageView var10000 = this.playerBarPlay;
        if (var10000 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("playerBarPlay");
        }
        OnClickListenerWrapper wrapper = new OnClickListenerWrapper(listener);
        var10000.setOnClickListener(wrapper);
    }

    public void performPlayerBarClick(){
        if(playerBarPlay != null){
            playerBarPlay.performClick();
        }
    }

    public boolean getCollectState() {
        return false;
    }

    public void showToast(@Nullable String info) {
        Context var10000 = this.getContext();
        Intrinsics.checkExpressionValueIsNotNull(var10000, "context");
        ToastUtil.showOnly(var10000.getApplicationContext(), info);
    }

    public void setPrevState(boolean hasPre) {
    }

    @NotNull
    public Activity getRootActivity() {
        Context var10000 = this.getContext();
        if (var10000 == null) {
            throw new TypeCastException("null cannot be cast to non-null type android.app.Activity");
        } else {
            return (Activity)var10000;
        }
    }

    public void updateInfo(int cp, @Nullable PlayItem playItem) {
        String audioPic = PlayerManagerHelper.getInstance().getPlayItemPicUrl(playItem);
        ImageLoader var10000 = ImageLoader.getInstance();
        Context var10001 = this.getContext();
        ImageView var10003 = this.playerBarCover;
        if (var10003 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("playerBarCover");
        }

        var10000.displayImage(var10001, audioPic, var10003, ResUtil.getDrawable(drawable.media_default_pic));
        this.showNormalState();
    }

    public void setNextClickListener(@Nullable OnClickListener listener) {
        ImageView var10000 = this.playerBarNext;
        if (var10000 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("playerBarNext");
        }

        var10000.setOnClickListener(listener);
    }

    public void setCollectClickListener(@Nullable OnClickListener listener) {
    }

    @Override
    public void setPlayVideoMaximumClickListener(OnClickListener listener) {

    }

    public void setTitle(@Nullable CharSequence title) {
    }

    public void showPlayState() {
        Logging.i(this.TAG, "showPlayState start isLiveState = " + this.isLiveState, new Object[0]);
        if (!this.isLiveState) {
            Drawable drawable = ResUtil.getDrawable(R.drawable.comprehensive_playerbar_pause_brand);
            ImageView var10000 = this.playerBarPlay;
            if (var10000 == null) {
                Intrinsics.throwUninitializedPropertyAccessException("playerBarPlay");
            }

            var10000.setImageDrawable(drawable);
            Logging.i(this.TAG, "showPlayState drawable ic_player_bar_pause_normal", new Object[0]);

//            playerBarPlay.setContentDescription("暂停播放");
        }

    }

    public void showLiveState() {
        this.isLiveState = true;
    }

    public void setSubtitle(@Nullable CharSequence title) {
    }

    public void setCoverImageDrawable(@Nullable Drawable drawable) {
        ImageView var10000 = this.playerBarCover;
        if (var10000 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("playerBarCover");
        }

        var10000.setImageDrawable(drawable);
    }

    public void showLoading(boolean show) {
        View var10000;
        Animation var2;
        if (this.isLiveState) {
            var10000 = this.playerBarLoading;
            if (var10000 == null) {
                Intrinsics.throwUninitializedPropertyAccessException("playerBarLoading");
            }

            var10000.setVisibility(GONE);
            var10000 = this.playerBarLoading;
            if (var10000 == null) {
                Intrinsics.throwUninitializedPropertyAccessException("playerBarLoading");
            }

            var2 = var10000.getAnimation();
            if (var2 != null) {
                var2.cancel();
            }
        } else {
            var10000 = this.playerBarLoading;
            if (var10000 == null) {
                Intrinsics.throwUninitializedPropertyAccessException("playerBarLoading");
            }

            ViewUtil.setViewVisibility(var10000, show ? 0 : 4);
            if (show) {
                var10000 = this.playerBarLoading;
                if (var10000 == null) {
                    Intrinsics.throwUninitializedPropertyAccessException("playerBarLoading");
                }

                var10000.setAnimation((Animation)this.loadingAnimation);
                var10000 = this.playerBarLoading;
                if (var10000 == null) {
                    Intrinsics.throwUninitializedPropertyAccessException("playerBarLoading");
                }

                var10000.getAnimation().start();
            } else {
                var10000 = this.playerBarLoading;
                if (var10000 == null) {
                    Intrinsics.throwUninitializedPropertyAccessException("playerBarLoading");
                }

                var2 = var10000.getAnimation();
                if (var2 != null) {
                    var2.cancel();
                }
            }
        }

    }

    public void setNextState(boolean hasNext) {
        ImageView var10000 = this.playerBarNext;
        if (var10000 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("playerBarNext");
        }

        var10000.setActivated(hasNext);
    }

    public void setProgressEnabled(boolean isEnabled) {
    }

    public void setPreClickListener(@Nullable OnClickListener listener) {
    }

    @NotNull
    public Lifecycle getLifecycle() {
        Context var10000 = this.getContext();
        if (var10000 == null) {
            throw new TypeCastException("null cannot be cast to non-null type android.support.v7.app.AppCompatActivity");
        } else {
            Lifecycle var1 = ((AppCompatActivity)var10000).getLifecycle();
            Intrinsics.checkExpressionValueIsNotNull(var1, "(context as AppCompatActivity).lifecycle");
            return var1;
        }
    }

    public void setCollectState(boolean isCollect) {
    }

    public void showNormalState() {
        this.isLiveState = false;
        ImageView var10000 = this.playerBarPlay;
        if (var10000 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("playerBarPlay");
        }

        if (var10000.getVisibility() != VISIBLE) {
            var10000 = this.playerBarPlay;
            if (var10000 == null) {
                Intrinsics.throwUninitializedPropertyAccessException("playerBarPlay");
            }

            var10000.setVisibility(VISIBLE);
        }

    }

    public void updateBroadcastErrorInfo() {
        PlayerManagerHelper var10000 = PlayerManagerHelper.getInstance();
        Intrinsics.checkExpressionValueIsNotNull(var10000, "PlayerManagerHelper.getInstance()");
        int position = var10000.getCurrentFrequencyPosition();
        BroadcastRadioSimpleData var4 = PlayerManagerHelper.getInstance().getBroadcastRadioSimpleDataByIndex(position);
        if (var4 != null) {
            BroadcastRadioSimpleData broadcastRadioSimpleData = var4;
            String var5 = broadcastRadioSimpleData.getImg();
            Intrinsics.checkExpressionValueIsNotNull(var5, "broadcastRadioSimpleData.getImg()");
            String audioPic = var5;
            ImageLoader var6 = ImageLoader.getInstance();
            Context var10001 = this.getContext();
            ImageView var10003 = this.playerBarCover;
            if (var10003 == null) {
                Intrinsics.throwUninitializedPropertyAccessException("playerBarCover");
            }

            var6.displayImage(var10001, audioPic, var10003, ResUtil.getDrawable(drawable.media_default_pic));
            this.showNormalState();
        }
    }

    public void updateProgress(int progress) {
    }

    public void updateProgress(int position, int duration) {
    }

    public void showPlayBarType() {
        this.showNormalState();
    }

    public void setCoverImageUrl(@Nullable String coverUrl) {
        ImageLoader var10000 = ImageLoader.getInstance();
        Context var10001 = this.getContext();
        ImageView var10003 = this.playerBarCover;
        if (var10003 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("playerBarCover");
        }

        var10000.displayImage(var10001, coverUrl, var10003, ResUtil.getDrawable(drawable.media_default_pic));
    }

    public void showPauseState() {
        Logging.i(this.TAG, "showPauseState start isLiveState = " + this.isLiveState, new Object[0]);
        ImageView var10000 = this.playerBarPlay;
        if (var10000 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("playerBarPlay");
        }

        var10000.setImageResource(drawable.comprehensive_playerbar_play_brand);
        Logging.i(this.TAG, "showPauseState drawable playerbar_play", new Object[0]);
//        playerBarPlay.setContentDescription("继续播放");
    }

    public void showError(int strRid) {
        Context var10000 = this.getContext();
        Intrinsics.checkExpressionValueIsNotNull(var10000, "context");
        if (NetworkUtil.isNetworkAvailable(var10000.getApplicationContext(), true)) {
            KRadioToastInter radioToastInter = (KRadioToastInter)ClazzImplUtil.getInter("KRadioToastImpl");
            if (radioToastInter != null) {
                Context var10002 = this.getContext();
                Intrinsics.checkExpressionValueIsNotNull(var10002, "context");
                radioToastInter.showToast(0, var10002.getApplicationContext(), this.getResources().getString(strRid));
            } else {
                var10000 = this.getContext();
                Intrinsics.checkExpressionValueIsNotNull(var10000, "context");
                ToastUtil.showNormal(var10000.getApplicationContext(), strRid);
            }
        }

    }

    public void showBroadcastState() {
    }

    @Subscribe(
            threadMode = ThreadMode.MAIN
    )
    public final void playNextIsNotLiving(@Nullable BroadcastPlayerChangedData broadcastPlayerChangedData) {
        this.updateInfo(1, PlayerManagerHelper.getInstance().getCurPlayItem());
        this.resetPlayerBarState();
    }

    private final void resetPlayerBarState() {
        PlayerManagerHelper playerManagerHelper = PlayerManagerHelper.getInstance();
        String var10000 = this.TAG;
        StringBuilder var10001 = (new StringBuilder()).append("Player: isPlaying = ");
        Intrinsics.checkExpressionValueIsNotNull(playerManagerHelper, "playerManagerHelper");
        Log.i(var10000, var10001.append(playerManagerHelper.isPlaying()).toString());
        Log.i(this.TAG, "Player: isPlayingClock = " + playerManagerHelper.isPlayingClock());
        if (playerManagerHelper.isPlaying() && !playerManagerHelper.isPlayingClock()) {
            this.showPlayState();
        } else {
            this.showPauseState();
        }

    }

    public void applySkin() {
        super.applySkin();
        ComprehensivePlayerBarPresenter var10001 = this.mPresenter;
        if (var10001 == null) {
            Intrinsics.throwNpe();
        }

        int var1 = var10001.getCP();
        this.updateInfo(var1, PlayerManagerHelper.getInstance().getCurPlayItem());
    }

    public final void setCoverClickListener(@Nullable final OnClickListener listener) {
        ImageView var10000 = this.playerBarCover;
        if (var10000 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("playerBarCover");
        }

        var10000.setOnClickListener((OnClickListener)(new OnClickListener() {
            public final void onClick(View it) {
                Log.i(ComprehensiveMiniPlayerBar.this.TAG, "mPlayerBar onClick");
                if (!AntiShake.check(it.getId())) {
                    Log.i(ComprehensiveMiniPlayerBar.this.TAG, "mPlayerBar onClick be executed");
                    OnClickListener var10000 = listener;
                    if (var10000 != null) {
                        var10000.onClick(it);
                    }
                }

            }
        }));
    }

    public ComprehensiveMiniPlayerBar(@Nullable Context context) {
        this(context, (AttributeSet)null);
    }

    public ComprehensiveMiniPlayerBar(@Nullable Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ComprehensiveMiniPlayerBar(@Nullable Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.TAG = "MiniPlayerBar";
        this.mMeasureSpec = MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED);
        RotateAnimation var4 = new RotateAnimation(0.0F, 360.0F, 1, 0.5F, 1, 0.5F);
        var4.setRepeatCount(-1);
        var4.setRepeatMode(1);
        var4.setInterpolator((Interpolator)(new LinearInterpolator()));
        var4.setDuration(2000L);
        this.loadingAnimation = var4;
        LayoutInflater.from(this.getContext()).inflate(layout.comprehensive_mini_playerbar, (ViewGroup)this);
        View var10001 = this.findViewById(id.player_bar_cover);
        Intrinsics.checkExpressionValueIsNotNull(var10001, "findViewById(R.id.player_bar_cover)");
        this.playerBarCover = (ImageView)var10001;
        this.playerBarCover.setContentDescription(getContext().getString(R.string.content_desc_player_playing));
        var10001 = this.findViewById(id.player_bar_play);
        Intrinsics.checkExpressionValueIsNotNull(var10001, "findViewById(R.id.player_bar_play)");
        this.playerBarPlay = (ImageView)var10001;

        var10001 = this.findViewById(id.player_bar_next);
        Intrinsics.checkExpressionValueIsNotNull(var10001, "findViewById(R.id.player_bar_next)");
        this.playerBarNext = (ImageView)var10001;
        var10001 = this.findViewById(id.player_bar_loading);
        Intrinsics.checkExpressionValueIsNotNull(var10001, "findViewById(R.id.player_bar_loading)");
        this.playerBarLoading = var10001;
        View var10000 = this.playerBarLoading;
        if (var10000 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("playerBarLoading");
        }

        var10000.setAnimation((Animation)this.loadingAnimation);
        this.mPresenter = new ComprehensivePlayerBarPresenter((IPlayerView)this);
        this.showPauseState();
        ReportHelper var11 = ReportHelper.getInstance();
        ReportParameterManager var10006 = ReportParameterManager.getInstance();
        Intrinsics.checkExpressionValueIsNotNull(var10006, "ReportParameterManager.getInstance()");
        var11.addEvent((BaseReportEventBean)(new ButtonExposureOrClickReportEvent("exposure", "66", "", var10006.getPage(), "1")));
    }

    // $FF: synthetic method
    public static final void access$setMPresenter$p(ComprehensiveMiniPlayerBar $this, ComprehensivePlayerBarPresenter var1) {
        $this.mPresenter = var1;
    }

    public View _$_findCachedViewById(int var1) {
        if (this._$_findViewCache == null) {
            this._$_findViewCache = new HashMap();
        }

        View var2 = (View)this._$_findViewCache.get(var1);
        if (var2 == null) {
            var2 = this.findViewById(var1);
            this._$_findViewCache.put(var1, var2);
        }

        return var2;
    }

    public void _$_clearFindViewByIdCache() {
        if (this._$_findViewCache != null) {
            this._$_findViewCache.clear();
        }

    }
}
