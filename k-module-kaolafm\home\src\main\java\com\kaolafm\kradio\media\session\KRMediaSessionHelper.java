package com.kaolafm.kradio.media.session;

import android.content.Context;
import android.media.AudioManager;
import android.os.Build;
import android.support.v4.media.session.MediaSessionCompat;
import android.util.Log;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.MediaSessionHelperInter;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter;
import com.kaolafm.opensdk.player.logic.PlayerManager;

import java.lang.ref.WeakReference;

/******************************************
 * 类描述:
 *
 * @version: V1.0
 * @author: yangshaoning
 * @time: 2019-04-09 18:14
 ******************************************/
public final class KRMediaSessionHelper {
    private static final String TAG = "KRMediaSessionHelper";

    private KRMediaSessionInter mKrMediaSessionInter;
    private MyOnAudioFocusChangeListener myOnAudioFocusChangeListener;

    public KRMediaSessionHelper() {
        // TODO: 2020-02-15 zc 为了解决 #39173 这个问题.  少宁删除了代码. 暂时先恢复. 不要阻断测试
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            Log.i(TAG, "初始化.....KRMediaSessionUpLP");
            mKrMediaSessionInter = new KRMediaSessionUpLP();
        } else {
            Log.i(TAG, "初始化.....KRMediaSessionDownLP");
            mKrMediaSessionInter = new KRMediaSessionDownLP();
        }
        myOnAudioFocusChangeListener = new MyOnAudioFocusChangeListener(this);
        PlayerManager.getInstance().addAudioFocusListener(myOnAudioFocusChangeListener);
    }

    /**
     * 注册多媒体按键
     *
     * @param context
     */
    public void registerMediaSession(Context context) {
        mKrMediaSessionInter.registerMediaSession(context);
    }

    /**
     * 注销多媒体按键
     *
     * @param context
     */
    public void unregisterMediaSession(Context context) {
        mKrMediaSessionInter.unregisterMediaSession(context);
    }

    /**
     * 获取MediaSession对象
     *
     * @return
     */
    public MediaSessionCompat getMediaSession() {
        return mKrMediaSessionInter.getMediaSession();
    }

    /**
     * 释放资源
     */
    public void release() {
        PlayerManager.getInstance().removeAudioFocusListener(myOnAudioFocusChangeListener);
    }


    private static class MyOnAudioFocusChangeListener implements OnAudioFocusChangeInter {
        private WeakReference<KRMediaSessionHelper> weakReference;

        public MyOnAudioFocusChangeListener(KRMediaSessionHelper krMediaSessionHelper) {
            weakReference = new WeakReference<>(krMediaSessionHelper);
        }

        @Override
        public void onAudioFocusChange(int i) {

            Log.i(TAG, "onAudioFocusChange--------> i = " + i);
            KRMediaSessionHelper krMediaSessionHelper = weakReference.get();
            if (krMediaSessionHelper == null) {
                Log.i(TAG, "krMediaSessionHelper: = " + krMediaSessionHelper);
                return;
            }
            if (i == AudioManager.AUDIOFOCUS_GAIN) {
                Log.i(TAG, "AUDIOFOCUS_GAIN-------> registerMediaSession");
                krMediaSessionHelper.registerMediaSession(AppDelegate.getInstance().getContext());
            } else {
                MediaSessionHelperInter helperInter = ClazzImplUtil.getInter("MediaSessionHelperInterImpl");
                Log.i(TAG, "MediaSessionHelperInter:" + helperInter);
                //个别渠道下，在音频焦点暂时丢失时也需要释放方控的注册，来解决切换到其他应用时，方控监听依然在云听应用这边的问题
                if (helperInter == null && i == AudioManager.AUDIOFOCUS_LOSS) {
                    Log.i(TAG, "AUDIOFOCUS_LOSS-------> unregisterMediaSession");
                    krMediaSessionHelper.unregisterMediaSession(AppDelegate.getInstance().getContext());
                } else if (helperInter != null) {
                    boolean needUnregisterMediaSession = helperInter.needUnregisterMediaSession(i);
                    Log.i(TAG, "MediaSessionHelperInter needUnregisterMediaSession:" + needUnregisterMediaSession);
                    if (needUnregisterMediaSession) {
                        krMediaSessionHelper.unregisterMediaSession(AppDelegate.getInstance().getContext());
                    }
                }
            }
        }
    }
}
